package com.kyb.pcberp.modules.hr.emp_center.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_ActivateAccount;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_HandOver;
import com.kyb.pcberp.modules.hr.permission_center.pojo.Hr_Role_Item;
import com.kyb.pcberp.modules.sys.entity.PositionEmployee;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@MyBatisDao
public interface Hr_EmployeeDao extends CrudDao<Hr_Employee> {

    List<Hr_Employee> getEmployeeProfileData (Hr_Employee employee);

    void delEmp(Hr_Employee employee);

    void insertEmp(Hr_Employee employeeProfile);

    void updateEmp(Hr_Employee employeeProfile);

    List<Hr_Employee> getEmployeeList (Hr_Employee employee);

    void updateEmployee(Hr_Employee hr_employee);

    List<Hr_Employee> getEmployeeProfileDataTwo(Hr_Employee hr_employee);

    void updatePosition(Hr_Role_Item hr_role_item);

    String getPositionExtis();

    void updateRelationEmp(Hr_Employee hr_employee);

    List<Hr_Employee> getEmployeeListT (Hr_Employee employee);

    String getSuperUserId(@Param("recordId") String recordId);

    Hr_Employee getEmployee(@Param("recordId") String recordId);

    List<Hr_ActivateAccount>getActivateAccountList(Hr_ActivateAccount hr_activateAccount);

    void updateActivateAccount(Hr_ActivateAccount hr_activateAccount);

    void insertActivateAccount(Hr_ActivateAccount hr_activateAccount);

    void delUserAccount(Hr_ActivateAccount hr_activateAccount);

    Integer getEmployeePhone(Hr_ActivateAccount hr_activateAccount);

    void addEmployeePhone(Hr_ActivateAccount hr_activateAccount);

    Integer getCount(Hr_ActivateAccount hr_activateAccount);

    void delEmployeePhone(Hr_ActivateAccount hr_activateAccount);

    Integer getPhone(Hr_ActivateAccount hr_activateAccount);

    void updateEmployeePhone(Hr_ActivateAccount hr_activateAccount);

    void updateUserAccount(Hr_ActivateAccount hr_activateAccount);

    void delUserAccountTwo(Hr_ActivateAccount hr_activateAccount);

    Hr_ActivateAccount getManageUserId(Hr_ActivateAccount hr_activateAccount);

    Hr_ActivateAccount getManageUserIdTwo(Hr_ActivateAccount hr_activateAccount);

    void receiveEmployeePhone(Hr_ActivateAccount hr_activateAccount);

    void receiveUserAccountTwo(Hr_ActivateAccount hr_activateAccount);

    List<Hr_HandOver> selectHandOve (Hr_HandOver handOver);

    void delHandOver(Hr_HandOver handOver);

    void insertHandOver(Hr_HandOver handOver);

    void updHandOver(Hr_HandOver handOver);

    List<PositionEmployee> getPoEmpList(Hr_Employee employee);

    Hr_HandOver getHandOver(Hr_HandOver handOver);

    void editHandOver(Hr_HandOver hr_handOver);

    void addHandOver(Hr_HandOver handOver);

    Integer getPoEmpNum(PositionEmployee poEmp);

    void delPoEmp(PositionEmployee poEmp);

    void updatePoEmpId(PositionEmployee poEmp);

    void cleanSuperEmpId(Hr_HandOver handOver);

    void updateAuditId(Hr_HandOver handOver);

    void updateReportId(Hr_HandOver handOver);

    void updateTaskId(Hr_HandOver handOver);

    void updateDayThingId(Hr_HandOver handOver);

    void recoverDate(Hr_Employee hr_employee);

    void insertRelationEmp(Hr_Employee hr_employee);

    List<Hr_Employee> getShowEmployeeList(Hr_Employee hr_employee);
}
