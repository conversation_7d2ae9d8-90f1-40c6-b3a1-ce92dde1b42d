<%@ page contentType="text/html;charset=UTF-8" %>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">统计报表</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="crm.quotation">工程</a>
        </li>
    </ul>
</div>
<!-- END 导航-->
<div class="row tabset-margin-top">
	<div class="col-md-12">
		<div class="row" ng-if="showDayOfWeek">
			<div class="col-md-12">
				<!-- BEGIN CHART PORTLET-->
				<div class="portlet light bordered">
					<div class="portlet-title">
						<div class="caption">
							<i class="icon-bar-chart font-green-haze"></i>
							<span class="caption-subject bold uppercase font-green-haze">工程统计</span>
							<span class="caption-helper">{{option.pro.year}}年&nbsp;
							<span ng-if="query.genSelected > -1">第{{query.genSelected}}周</span>
							&nbsp;每天走势图&nbsp;</span>
						</div>
						<div class="tools">
							<a href="javascript:;" class="collapse">
							</a>
							<a href="javascript:;" class="fullscreen">
							</a>
						</div>
						<div class="actions">
							<div class="btn-group btn-group-devided" data-toggle="buttons">
								<label class="btn btn-circle btn-transparent grey-salsa btn-sm active">
								<input type="radio" name="options" class="toggle" id="option1" >按 周</label>
								<label class="btn btn-circle btn-transparent grey-salsa btn-sm" ng-click="getMonthDatas()">
								<input type="radio" name="options" class="toggle" id="option2">按 月</label>
								<label class="btn btn-circle btn-transparent grey-salsa btn-sm" ng-click="getYearDatas()">
								<input type="radio" name="options" class="toggle" id="option2">按 年</label>
							</div>
						</div>
					</div>
					<div class="portlet-body">
						<form class="form-horizontal">
							<div class="col-md-8 col-lg-3">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">选择年份：</label>
	                                <div class="col-sm-7 col-md-6">
	                                    <select  class="form-control"
	                                    		 ng-change="getWeekDatas()"
	                                             ng-model="option.pro.year"
	                                             ng-options="option for option in option.pro.years"
	                                             required>
	                                        <option value="">--请选择--</option>
	                                    </select>
	                                </div>
	                            </div>
	                        </div>
                      	</form>
                        <form class="form-horizontal">
							<div class="col-md-8 col-lg-3">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">选择周：</label>
	                                <div class="col-sm-7 col-md-6">
	                                    <select  class="form-control"
	                                             ng-model="query.genSelected"
	                                             ng-change="getWeekDatas()"
	                                             ng-options="option for option in query.genList"
	                                             required>
	                                    </select>
	                                </div>
	                            </div>
	                        </div>
                     	</form>
                        <div class="portlet-input input-inline input-small">
	                        <form action="a/report/eg/engineeringExportFile" method="get" enctype="multipart/form-data" target="hidden_frame">
	                            <input type="text" ng-show="false" name="year" value="{{option.pro.year}}"/>
	                            <input type="text" ng-show="false" name="month" value="{{option.pro.month}}"/>
	                            <input type="text" ng-show="false"  name="week" value="{{query.genSelected}}"/>
	                            <input type="text" ng-show="false"  name="type" value="1"/>
	                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出
								<span ng-if="query.genSelected > -1">第{{query.genSelected}}周</span>
	                            &nbsp;工程数据</button>
	                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
	                        </form>
                    	</div>
						<div id="dayofweek" class="chart" eg-Charts 
							data-input-Data="daysOfWeek"
                            data-char-Option="charOptionDayOfWeek"  style="height: 400px;">
                        </div>
					</div>
				</div>
				<!-- END CHART PORTLET-->
			</div>
		</div>
		
		<div class="row" ng-if="showDayOfMonth">
			<div class="col-md-12">
				<!-- BEGIN CHART PORTLET-->
				<div class="portlet light bordered">
					<div class="portlet-title">
						<div class="caption">
							<i class="icon-bar-chart font-green-haze"></i>
							<span class="caption-subject bold uppercase font-green-haze">工程统计</span>
							<span class="caption-helper">{{option.pur.year}}年&nbsp;
							<span>{{option.pur.month}}月</span>&nbsp;每周走势图</span>
						</div>
						<div class="tools">
							<a href="javascript:;" class="collapse">
							</a>
							<a href="javascript:;" class="fullscreen">
							</a>
						</div>
						<div class="actions">
							<div class="btn-group btn-group-devided" data-toggle="buttons">
								<label class="btn btn-circle btn-transparent grey-salsa btn-sm" ng-click="getWeekDatas()">
								<input type="radio" name="options" class="toggle" id="option1" >按 周</label>
								<label class="btn btn-circle btn-transparent grey-salsa btn-sm active" >
								<input type="radio" name="options" class="toggle" id="option2">按 月</label>
								<label class="btn btn-circle btn-transparent grey-salsa btn-sm" ng-click="getYearDatas()">
								<input type="radio" name="options" class="toggle" id="option2">按 年</label>
							</div>
						</div>
					</div>
					<div class="portlet-body">
						<form class="form-horizontal">
							<div class="col-md-8 col-lg-3">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">选择年份：</label>
	                                <div class="col-sm-7 col-md-6">
	                                    <select  class="form-control"
	                                    		 ng-change="getMonthDatas()"
	                                             ng-model="option.pur.year"
	                                             ng-options="option for option in option.pur.years"
	                                             required>
	                                        <option value="">--请选择--</option>
	                                    </select>
	                                </div>
	                            </div>
	                        </div>
                      	</form>
						<form class="form-horizontal">
							<div class="col-md-8 col-lg-3">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">选择月份：</label>
	                                <div class="col-sm-7 col-md-6">
	                                    <select  class="form-control"
	                                    		 ng-change="getMonthDatas()"
	                                             ng-model="option.pur.month"
	                                             ng-options="option for option in option.pur.months"
	                                             required>
	                                    </select>
	                                </div>
	                            </div>
	                        </div>
                        </form>
                        <div class="portlet-input input-inline input-small">
	                        <form action="a/report/eg/engineeringExportFile" method="get" enctype="multipart/form-data" target="hidden_frame">
	                            <input type="text" ng-show="false" name="year" value="{{option.pur.year}}"/>
	                            <input type="text" ng-show="false" name="month" value="{{option.pur.month}}"/>
	                            <input type="text" ng-show="false"  name="week" value="{{option.pur.week}}"/>
	                            <input type="text" ng-show="false"  name="type" value="2"/>
	                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出&nbsp;
										<span>{{option.pur.month}}月</span>&nbsp;工程数据</button>
	                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
	                        </form>
                    	</div>
						<div id="dayofmonth" class="chart" eg-Charts
							data-input-Data="daysOfMonth"
                            data-char-Option="charOptionDayOfMonth"  style="height: 400px;">
                        </div>
					</div>
				</div>
				<!-- END CHART PORTLET-->
			</div>
		</div>
		
		<div class="row" ng-if="showMonthOfYear">
			<div class="col-md-12">
				<!-- BEGIN CHART PORTLET-->
				<div class="portlet light bordered">
					<div class="portlet-title">
						<div class="caption">
							<i class="icon-bar-chart font-green-haze"></i>
							<span class="caption-subject bold uppercase font-green-haze">工程统计</span>
							<span class="caption-helper">{{option.pvo.year}}年&nbsp;每月走势图</span>
						</div>
						<div class="tools">
							<a href="javascript:;" class="collapse">
							</a>
							<a href="javascript:;" class="fullscreen">
							</a>
						</div>
						<div class="actions">
							<div class="btn-group btn-group-devided" data-toggle="buttons">
								<label class="btn btn-circle btn-transparent grey-salsa btn-sm" ng-click="getWeekDatas()">
								<input type="radio" name="options" class="toggle" id="option1" >按 周</label>
								<label class="btn btn-circle btn-transparent grey-salsa btn-sm" ng-click="getMonthDatas()">
								<input type="radio" name="options" class="toggle" id="option2">按 月</label>
								<label class="btn btn-circle btn-transparent grey-salsa btn-sm active">
								<input type="radio" name="options" class="toggle" id="option2">按 年</label>
							</div>
						</div>
					</div>
					<div class="portlet-body">
					
						<form class="form-horizontal">
							<div class="col-md-8 col-lg-3">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">选择年份：</label>
	                                <div class="col-sm-7 col-md-6">
	                                    <select  class="form-control"
	                                    		 ng-change="getYearDatas()"
	                                             ng-model="option.pvo.year"
	                                             ng-options="option for option in option.pvo.years"
	                                             required>
	                                        <option value="">--请选择--</option>
	                                    </select>
	                                </div>
	                            </div>
	                        </div>
                        </form>
                        <div class="portlet-input input-inline input-small">
	                        <form action="a/report/eg/engineeringExportFile" method="get" enctype="multipart/form-data" target="hidden_frame">
	                            <input type="text" ng-show="false" name="year" value="{{option.pvo.year}}"/>
	                            <input type="text" ng-show="false" name="month" value="{{option.pvo.month}}"/>
	                            <input type="text" ng-show="false"  name="week" value="{{option.pvo.week}}"/>
	                            <input type="text" ng-show="false"  name="type" value="3"/>
	                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出{{option.pvo.year}}年工程数据</button>
	                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
	                        </form>
                    	</div>
						<div id="monthOfYear" class="chart" style="height: 400px;" 
							eg-Charts data-input-Data="monthsOfYear" data-char-Option="charOptionMonthOfYear"  >
                        </div>
					</div>
				</div>
				<!-- END CHART PORTLET-->
			</div>
		</div>
		
		<div class="row" ng-if="showDayOfWeek">
			<div class="col-md-12">
				<div class="table-responsive">
					<table class="table table-bordered table-hover">
	                    <thead>
	                    <tr role="row" class="heading">
	                        <th class="text-center">#</th>
	                        <th class="text-center"  ng-repeat="item in daysOfWeek">
	                        	<span ng-if="item.date != currDay">{{item.date}}</span>
	                        	<span ng-if="item.date == currDay" class="text-primary">{{item.date}}(今天)</span>
	                        </th>
	                    </tr>
	                    </thead>
	                    <tbody>
	                    <tr>
	                        <td class="text-right" >
	                        	FQC
	                        </td>
	                        <td ng-repeat="item in daysOfWeek" class="text-right">{{item.fqcCraftCount}}</td>
	                    </tr>
	                    <tr>
	                        <td class="text-right">
	                        	市场
	                        </td>
	                        <td ng-repeat="item in daysOfWeek" class="text-right">{{item.marketCraftCount}}</td>
	                    </tr>
	                    <tr>
	                        <td class="text-right">
	                           	工程
	                        </td>
	                        <td ng-repeat="item in daysOfWeek" class="text-right">{{item.egCraftCount}}</td>
	                    </tr>
	                    <tr>
	                        <td class="text-right">
	                        	利用率
	                        </td>
	                        <td ng-repeat="item in daysOfWeek" class="text-right">{{item.useRatio}} %</td>
	                    </tr>
	                    <tr>
	                        <td class="text-right">
	                        	交货
	                        </td>
	                        <td ng-repeat="item in daysOfWeek" class="text-right">{{item.deliveryArea}} ㎡</td>
	                     </tr>
	                     <tr>
	                        <td class="text-right">
	                          	开料
	                        </td>
	                        <td ng-repeat="item in daysOfWeek" class="text-right">{{item.cuttingArea}} ㎡</td>
	                     </tr>
	                     <tr>
	                        <td class="text-right">
	                        	入库
	                        </td>
	                        <td ng-repeat="item in daysOfWeek" class="text-right">{{item.pubWarehouseArea}} ㎡</td>
	                    </tr>
	                    <tr><td></td></tr>
	                    <tr>
	                    	<td colspan="1" class="text-right">周平均利用率</td>
	                    	<td colspan="3" class="text-right">{{currWeekData.useRatio}} %</td>
	                    </tr>
	                    <tr>
	                    	<td class="text-right">本周送货面积</td>
	                    	<td class="text-right">{{currWeekData.deliveryArea}} ㎡</td>
	                    	<td class="text-right">FQC样品款数</td>
	                    	<td class="text-right">{{currWeekData.fqcCraftCount}}</td>
	                    </tr>   
	                    <tr>    
	                    	<td class="text-right">本周开料面积</td>
	                    	<td class="text-right">{{currWeekData.cuttingArea}} ㎡</td>
	                    	<td class="text-right">市场下单款数</td>
	                    	<td class="text-right">{{currWeekData.marketCraftCount}}</td>
	                    </tr>   
	                    <tr>    
	                    	<td class="text-right">本周入库面积</td>
	                    	<td class="text-right">{{currWeekData.pubWarehouseArea}} ㎡</td>
	                    	<td class="text-right">工程制作款数</td>
	                    	<td class="text-right">{{currWeekData.egCraftCount}}</td>
	                    </tr> 
	                    </tbody>
	            	</table>
            	</div>
        	</div>
		</div>   
		
		<div class="row" ng-if="showDayOfMonth">
			<div class="col-md-12">
				<div class="table-responsive">
					<table class="table table-bordered table-hover">
	                    <thead>
	                    <tr role="row" class="heading">
	                        <th class="text-center"><span ng-if="query.month==0">本</span>
										<span ng-if="query.month > 0 ">{{query.month}}</span> 月</th>
	                        <th class="text-center"  ng-repeat="item in daysOfMonth">
	                        	<span ng-if="item.sourceWeek != currWeek">{{item.week}}</span>
	                        	<span ng-if="item.sourceWeek == currWeek" class="text-primary">{{item.week}}(本周)</span>
	                        </th>
	                    </tr>
	                    </thead>
	                    <tbody>
	                    <tr>
	                        <td class="text-right">
	                        	FQC
	                        </td>
	                        <td ng-repeat="item in daysOfMonth" class="text-right">{{item.fqcCraftCount}}</td>
	                    </tr>
	                    <tr>
	                        <td class="text-right">
	                        	市场
	                        </td>
	                        <td ng-repeat="item in daysOfMonth" class="text-right">{{item.marketCraftCount}}</td>
	                    </tr>
	                    <tr>
	                        <td class="text-right">
	                           	工程
	                        </td>
	                        <td ng-repeat="item in daysOfMonth" class="text-right">{{item.egCraftCount}}</td>
	                    </tr>
	                    <tr>
	                        <td class="text-right">
	                        	利用率
	                        </td>
	                        <td ng-repeat="item in daysOfMonth" class="text-right">{{item.useRatio}} %</td>
	                    </tr>
	                    <tr>
	                        <td class="text-right">
	                        	交货
	                        </td>
	                        <td ng-repeat="item in daysOfMonth" class="text-right">{{item.deliveryArea}} ㎡</td>
	                     </tr>
	                     <tr>
	                        <td class="text-right">
	                          	开料
	                        </td>
	                        <td ng-repeat="item in daysOfMonth" class="text-right">{{item.cuttingArea}} ㎡</td>
	                     </tr>
	                     <tr>
	                        <td class="text-right">
	                        	入库
	                        </td>
	                        <td ng-repeat="item in daysOfMonth" class="text-right">{{item.pubWarehouseArea}} ㎡</td>
	                    </tr>
	                    <tr><td></td></tr>
	                    <tr>
	                    	<td colspan="1" class="text-right">月平均利用率</td>
	                    	<td colspan="3" class="text-right">{{currMonthData.useRatio}} %</td>
	                    </tr>
	                    <tr>
	                    	<td class="text-right">本月送货面积</td>
	                    	<td class="text-right">{{currMonthData.deliveryArea}} ㎡</td>
	                    	<td class="text-right">FQC样品款数</td>
	                    	<td class="text-right">{{currMonthData.fqcCraftCount}}</td>
	                    </tr>   
	                    <tr>    
	                    	<td class="text-right">本月开料面积</td>
	                    	<td class="text-right">{{currMonthData.cuttingArea}} ㎡</td>
	                    	<td class="text-right">市场下单款数</td>
	                    	<td class="text-right">{{currMonthData.marketCraftCount}}</td>
	                    </tr>   
	                    <tr>    
	                    	<td class="text-right">本月入库面积</td>
	                    	<td class="text-right">{{currMonthData.pubWarehouseArea}} ㎡</td>
	                    	<td class="text-right">工程制作款数</td>
	                    	<td class="text-right">{{currMonthData.egCraftCount}}</td>
	                    </tr>
	                    </tbody>
	            	</table>
            	</div>
        	</div>
		</div> 
		
		<div class="row" ng-if="showMonthOfYear">
			<div class="col-md-12">
				<div class="table-responsive">
					<table class="table table-bordered table-hover">
	                    <thead>
	                    <tr role="row" class="heading">
	                        <th class="text-center">{{monthsOfYear[0].year}} 年</th>
	                        <th class="text-center"  ng-repeat="item in monthsOfYear">
	                        	<span ng-if="item.sourceMonth != currMonthData.month">{{item.month}}</span>
	                        	<span ng-if="item.sourceMonth == currMonthData.month" class="text-primary">{{item.month}}(本月)</span>
	                        </th>
	                    </tr>
	                    </thead>
	                    <tbody>
	                    <tr>
	                        <td class="text-right">
	                        	FQC
	                        </td>
	                        <td ng-repeat="item in monthsOfYear" class="text-right">{{item.fqcCraftCount}}</td>
	                    </tr>
	                    <tr>
	                        <td class="text-right">
	                        	市场
	                        </td>
	                        <td ng-repeat="item in monthsOfYear" class="text-right">{{item.marketCraftCount}}</td>
	                    </tr>
	                    <tr>
	                        <td class="text-right">
	                           	工程
	                        </td>
	                        <td ng-repeat="item in monthsOfYear" class="text-right">{{item.egCraftCount}}</td>
	                    </tr>
	                    <tr>
	                        <td class="text-right">
	                        	利用率
	                        </td>
	                        <td ng-repeat="item in monthsOfYear" class="text-right">{{item.useRatio}} %</td>
	                    </tr>
	                    <tr>
	                        <td class="text-right">
	                        	交货
	                        </td>
	                        <td ng-repeat="item in monthsOfYear" class="text-right">{{item.deliveryArea}} ㎡</td>
	                     </tr>
	                     <tr>
	                        <td class="text-right">
	                          	开料
	                        </td>
	                        <td ng-repeat="item in monthsOfYear" class="text-right">{{item.cuttingArea}} ㎡</td>
	                     </tr>
	                     <tr>
	                        <td class="text-right">
	                        	入库
	                        </td>
	                        <td ng-repeat="item in monthsOfYear" class="text-right">{{item.pubWarehouseArea}} ㎡</td>
	                    </tr>
	                    <tr><td></td></tr>
	                    </tbody>
	            	</table>
            	</div>
        	</div>
		</div>
		
	</div>
</div>



					