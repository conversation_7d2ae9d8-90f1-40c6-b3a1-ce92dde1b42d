<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="d-flex flex-column flex-root">
    <div class="d-flex flex-row flex-column-fluid page">
        <div class="d-flex flex-column flex-row-fluid wrapper">
            <div class="content d-flex flex-column flex-column-fluid"
                 style="background-image: url(${pageContext.request.contextPath}/assets/global/metronic/assets/media/bg/bgMain.jpg);height: 100px;">
                <div class="subheader subheader-transparent">
                    <div class="container d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <h5 class="text-white my-2 mr-5">评价客诉</h5>
                            </div>
                        </div>
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <router-link to="/maintenance" class="btn btn-transparent-warning font-weight-bold mr-2">
                                    返回主页
                                </router-link>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-column-fluid">
                    <div class="container">
                        <div class="card card-custom gutter-b">
                            <div class="card-body d-flex flex-column p-3">
                                <div class="row border-bottom pb-3">
                                    <div class="col-xl-12">
                                        <ul class="nav nav-light-primary nav-pills">
                                            <li class="nav-item">
                                                <a class="nav-link active" data-toggle="tab" v-on:click="loadMarkSystemList(2)">
                                                    <span class="nav-text font-size-h5">待评价</span>
                                                </a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" data-toggle="tab" v-on:click="loadMarkSystemList(1)">
                                                    <span class="nav-text font-size-h5">已评价</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="row pt-3 align-items-center">
                                    <div class="col-xl-12">
                                        <input type="text" class="form-control" placeholder="请输入要搜索的设备信息" v-model="searchInfo" v-on:change = "loadMarkSystemList" />
                                    </div>
                                </div>
                                <template v-for="item in markSystemList">
                                    <div class="row align-items-center pt-3 pb-3 border-bottom" :key="item.recordId">
                                        <div class="col-xl-12">
                                            <div class="row align-items-center">
                                                <div class="col-6">
                                                    {{item.preserveNo}}&nbsp;&nbsp;&nbsp;
                                                    <span class="badge badge-primary" v-if="item.repairStatus == 1001">未受理</span>
                                                    <span class="badge badge-success" v-if="item.repairStatus == 1002">维修中</span>
                                                    <span class="badge badge-success" v-if="item.repairStatus == 1003">已结束</span>
                                                </div>
                                                <div class="col-6 text-right">
                                                    <button class="btn btn-sm btn-primary" v-on:click="markScoreWindow(item)">
                                                        <span v-if="item.commmitStatus == 1">查看</span>
                                                        <span v-else>评分</span>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-xl-12">
                                                    申报:&nbsp;&nbsp;&nbsp;<span class="font-weight-bold">{{item.applyCompany ? item.applyCompany.name : null}} - {{item.applyName}}</span>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-xl-12">
                                                    维保:&nbsp;&nbsp;&nbsp;<span class="font-weight-bold">{{item.repairCompany ? item.repairCompany.name : null}} - <span v-if="item.repairName">{{item.repairName}}</span></span>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-xl-12">
                                                    <span>{{item.materialType}}</span>:&nbsp;&nbsp;&nbsp;<span class="font-weight-bold">{{item.materialName}}</span>&nbsp;&nbsp;&nbsp;
                                                    规格:&nbsp;&nbsp;&nbsp;<span class="font-weight-bold">{{item.specification}}</span>
                                                </div>
                                            </div>
                                            <div class="row" v-if="item.startDate || item.endDate">
                                                <div class="col-xl-12">
                                                    时间:&nbsp;&nbsp;&nbsp;<span>{{item.startDate}}
                                                    <span v-if="item.endDate">&nbsp;到&nbsp;{{item.endDate}}</span></span>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-xl-12">
                                                    问题:&nbsp;&nbsp;&nbsp;<span>{{item.problem}}</span>&nbsp;&nbsp;
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-xl-12">
                                                    原因:&nbsp;&nbsp;&nbsp;<span>{{item.remark}}</span>&nbsp;&nbsp;
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal fade" id="showMarkSystem" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="showMarkSystem" aria-hidden="true" style="overflow:auto!important;">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <span v-if="markSystem.recordId">维保评分</span>
                                    <span v-if="markSystem.commmitStatus == '1'" class="text-success">(已发布)</span>
                                </h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <i aria-hidden="true" class="ki ki-close"></i>
                                </button>
                            </div>
                            <div class="row p-3">
                                <div class="col-xl-12">
                                    维保:&nbsp;&nbsp;&nbsp;<span class="font-weight-bold">{{markSystem.repairCompany ? markSystem.repairCompany.name : null}} - <span v-if="markSystem.repairName">{{markSystem.repairName}}</span></span>
                                </div>
                                <div class="col-xl-12 pt-3">
                                    <span>{{markSystem.materialType}}</span>:&nbsp;&nbsp;&nbsp;<span class="font-weight-bold">{{markSystem.materialName}}</span>&nbsp;&nbsp;&nbsp;
                                    规格:&nbsp;&nbsp;&nbsp;<span class="font-weight-bold">{{markSystem.specification}}</span>
                                </div>
                                <div class="col-xl-12 pt-3">
                                    时间:&nbsp;&nbsp;&nbsp;<span>{{markSystem.startDate}}
                                                    <span v-if="markSystem.endDate">&nbsp;到&nbsp;{{markSystem.endDate}}</span></span>
                                </div>
                                <div class="col-xl-12">
                                    <div class="row">
                                        <div class="col-xl-12">
                                            <div class="row pt-3" v-if="markSystem.commmitStatus != '1'">
                                                <div class="col-xl-12">
                                                    <button class="btn btn-primary" v-on:click="chooseImg">上传附件</button>
                                                </div>
                                            </div>
                                            <div v-if="localIds && localIds.length > 0">
                                                <template v-for="(item,index) in localIds">
                                                    <div class="row pt-3 pb-3 align-items-center">
                                                        <div class="col">
                                                            <img :src="item" class="w-25" v-on:click="preview(item)">
                                                        </div>
                                                        <div class="col-2">
                                                            <button class="btn btn-sm btn-danger" v-on:click="deleteImg(index)">删除</button>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                            <div v-if="fileList && fileList.length > 0">
                                                <template v-for="(item,index) in fileList">
                                                    <div class="row pt-3 pb-3 align-items-center border-bottom" :key="index">
                                                        <div class="col-7">{{item.orgFileName}}</div>
                                                        <div class="col text-right">
                                                            <button class="btn btn-sm btn-danger" v-on:click="delFile(index)" v-if="markSystem.commmitStatus != '1'">删除</button>
                                                            <a class="btn btn-sm btn-success" :href="item.preUrl" v-if="item.preUrl">预览</a>
                                                            <button class="btn btn-sm btn-success" v-if="item.fileUrl" v-on:click="previewImg(item.fileUrl)">预览</button>
                                                            <a class="btn btn-sm btn-success" :href="item.pdfUrl" v-if="item.pdfUrl">预览</a>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                        <div class="col-xl-12 pt-3">
                                            <div class="checkbox-inline">
                                                <span style="color: red">*</span>效率:&nbsp;&nbsp;&nbsp;
                                                <div class="star-rating">
                                                    <template v-for="(item, index) in efficiencyList">
                                                        &nbsp;<i :class="{'fas fa-star text-danger': index + 1 <= checkedStarsOne, 'far fa-star': index + 1 > checkedStarsOne}"
                                                                 @click="setRating(index + 1)" :key="item.value"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-12 pt-3">
                                            <div class="checkbox-inline">
                                                <span style="color: red">*</span>质量:&nbsp;&nbsp;&nbsp;
                                                <div class="star-rating">
                                                    <template v-for="(item, index) in massScoreList">
                                                        &nbsp;<i
                                                            :class="{'fas fa-star text-danger': index + 1 <= checkedStarsTwo, 'far fa-star': index + 1 > checkedStarsTwo}"
                                                            @click="setRatingTwo(index + 1)" :key="item.value"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-12 pt-3">
                                            <div class="checkbox-inline">
                                                <span style="color: red">*</span>态度:&nbsp;&nbsp;&nbsp;
                                                <div class="star-rating">
                                                    <template v-for="(item, index) in attitudeScoreList">
                                                        &nbsp;<i
                                                            :class="{'fas fa-star text-danger': index + 1 <= checkedStarsThree, 'far fa-star': index + 1 > checkedStarsThree}"
                                                            @click="setRatingThree(index + 1)" :key="item.value"></i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-12 pt-3">
                                            <span style="color: red">*</span>服务评价:
                                            <textarea rows="3" type="textarea" class="form-control" :disabled="markSystem.commmitStatus == '1'" v-model="markSystem.remarkT" required/>
                                        </div>
                                        <div class="col-xl-12 pt-3 text-right" v-if="markSystem.commmitStatus != '1'">
                                            <button type="button" class="btn btn-light-primary"  v-on:click="saveMarkMessage(1)">发布</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>