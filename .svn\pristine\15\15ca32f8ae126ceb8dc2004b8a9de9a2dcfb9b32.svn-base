/**
 * 
 */
package com.kyb.pcberp.modules.crm.service;

import java.util.List;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.service.BaseService;
import com.kyb.pcberp.modules.crm.dao.CustomerAttachementsDao;
import com.kyb.pcberp.modules.crm.entity.CustomerAttachements;

@Service
@Transactional(readOnly = true)
public class CustomerAttachementsService extends BaseService implements InitializingBean
{
    @Autowired
    private CustomerAttachementsDao customerAttachementsDao;
    
    @Override
    public void afterPropertiesSet()
        throws Exception
    {
    }
    
    @Transactional(readOnly = false)
    public void saveCustomerAttachements(CustomerAttachements customerAttachements)
    {
        customerAttachementsDao.insert(customerAttachements);
    }
    
    @Transactional(readOnly = false)
    public void updateCustomerAttachements(CustomerAttachements customerAttachements)
    {
        customerAttachementsDao.update(customerAttachements);
    }
    
    public List<CustomerAttachements> findAllCustomerAttachementslist(CustomerAttachements customerAttachements)
    {
        List<CustomerAttachements> list = customerAttachementsDao.findAllList(customerAttachements);
        if (list != null && !list.isEmpty())
        {
            list = this.getFilesUrl(list);
        }
        return list;
    }
    
    @Transactional(readOnly = false)
    public void deleteByCustomerId(CustomerAttachements customerAttachements)
    {
        customerAttachementsDao.delete(customerAttachements);
    }
    
    public List<CustomerAttachements> getFilesUrl(List<CustomerAttachements> list)
    {
        // Map<String, Object> result = Maps.newHashMap();
        if (FileManageUtils.isLocal())
        {
            // 设置附件的URL
            list.forEach(item -> {
                item.setTempUrl(item.getFileUrl());
            });
            // result.put("fBoard", ConstKey.GLOBAL_FILE_UPLOAD_PATH + list);
        }
        else
        {
            // 设置附件的URL
            list.forEach(item -> {
                item.setTempUrl(FileManageUtils.getUrl(item.getFileUrl()).toString());
            });
            // result.put("fBoard", list);
        }
        return list;
    }
    
}