/**
 *
 */
package com.kyb.pcberp.modules.purch.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.enums.CommonEnums.DictItemEnum;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.pdf.ItextPdfHeaderFooter;
import com.kyb.pcberp.common.pdf.ext.ParagraphCn12;
import com.kyb.pcberp.common.pdf.ext.ParagraphCn16;
import com.kyb.pcberp.common.pdf.ext.ParagraphCn9;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.modules.approval.utils.ModifyUtils;
import com.kyb.pcberp.modules.approval.utils.ModifyUtilsImpl;
import com.kyb.pcberp.modules.contract.entity.Contract;
import com.kyb.pcberp.modules.contract.entity.ContractCraft;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.contract.utils.ChangeDataUtils;
import com.kyb.pcberp.modules.purch.dao.*;
import com.kyb.pcberp.modules.purch.entity.*;
import com.kyb.pcberp.modules.purch.vo.ProDetailInVo;
import com.kyb.pcberp.modules.quality.dao.SourceDetectionDao;
import com.kyb.pcberp.modules.quality.entity.SourceDetection;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.dao.ProductStoreDao;
import com.kyb.pcberp.modules.stock.entity.ProductStore;
import com.kyb.pcberp.modules.sys.dao.BranchDao;
import com.kyb.pcberp.modules.sys.entity.*;
import com.kyb.pcberp.modules.sys.utils.CockpitUtilsOne;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;

/**
 * 成品采购Service
 *
 * <AUTHOR>
 * @version 2015-09-17
 */
@Service
@Transactional(readOnly = true)
public class PrdorderService extends CrudService<PrdorderDao, Prdorder>
{

    @Autowired
    private ProductCraftDao contractCraftDao;

    @Autowired
    private ProductPriceDao priceDao;

    /**
     * 成品采购
     */
    @Autowired
    private PrdorderDao prdorderDao;

    @Autowired
    private PrdorderDetailDao prdorderDetailDao;

    @Autowired
    private SupplierDao supplierDao;

    @Autowired
    private BranchDao branchDao;

    @Autowired
    private SourceDetectionDao sourceDetectionDao;

    @Autowired
    private MaterialDao materialDao;

    @Autowired
    private ProductStoreDao productStoreDao;

    public Prdorder get(String id)
    {
        return super.get(id);
    }

    public List<Prdorder> findList(Prdorder prdorder)
    {
        return super.findList(prdorder);
    }

    public Page<Prdorder> findPage(Page<Prdorder> page, Prdorder prdorder)
    {
        prdorder.setPage(page);
        prdorder.setEcoemyId(CompanyUtil.getInstance().getEcoemyIds());
        List<Prdorder> prdorders = prdorderDao.findPrdorderList(prdorder);
        for (Prdorder pc : prdorders)
        {
            // 获取成品采购明细
            List<PrdorderDetail> list = prdorderDetailDao.getPrdorderDetailByPurchasingId(pc);
            int asFlag = 0;
            int storNumInt = 0;
            boolean purchFlag = false;
            for (int i = 0; i < list.size(); i++)
            {
                if (StringUtils.isBlank(pc.getGroupCenterId()) && StringUtils.isNotBlank(list.get(i)
                    .getGroupCenterId()))
                {
                    pc.setGroupCenterId(list.get(i).getGroupCenterId());
                }
                // 获取入库数量
                BigDecimal storNum =
                    sourceDetectionDao.getStorPurch(list.get(i).getRecordId(), prdorder.getCompany().getRecordId());
                if (storNum == null)
                {
                    storNumInt = 0;
                }
                else
                {
                    storNumInt = storNum.intValue();
                }
                if (list.get(i).getQuantity() != null && storNumInt < list.get(i).getQuantity().intValue())
                {
                    asFlag++;
                }
                if (CompanyUtil.getInstance().valiteSaleCompany())
                {
                    String name = list.get(i).getMadeSupplierName();
                    if (StringUtils.isNotBlank(name) && name.indexOf("江西领德辉") == -1)
                    {
                        purchFlag = true;
                    }
                }
            }
            pc.setStockFlag("");
            if (asFlag == 0)
            {
                pc.setStockFlag("1"); // 表示该订单已经入库
            }
            else
            {
                if (purchFlag)
                {
                    pc.setStockFlag("2"); // 表示该订单还可以调整
                }
            }
            if (pc.getStatus() != null && (pc.getStatus().equals(TypeKey.PU_PURCHASING_STATUS_CANCLE.toString())
                || pc.getStatus().equals(TypeKey.PU_PURCHASING_STATUS_UNCONFIRM.toString())))
            {
                pc.setExistOperate(false);
                continue;
            }

            // 查询当前采购单的明细是否已被检测，或已入库了，若已做了这些操作则该采购单不能被取消
            Integer ct = prdorderDetailDao.isExistOperateStatus(pc);
            if (ct != null && ct.compareTo(0) == 1)
            {
                pc.setExistOperate(false);
            }

        }
        page.setList(prdorders);
        return page;
    }

    @Transactional(readOnly = false)
    public void save(Prdorder prdorder)
    {
        Company company = UserUtils.getUser().getCompany();
        prdorder.setCompany(company);
        prdorder.setStatus(TypeKey.PU_PURCHASING_STATUS_UNCONFIRM.toString());
        if (prdorder.getIsNewRecord())
        {
            // WC 2018-04-03 查询编号是否重复
            Integer noNum = prdorderDao.findPrdorderNoisEnable(prdorder); // 查询编号是否依旧存在
            while (noNum != null && noNum > 0)
            {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.PURCHASEORDER.getIndex());
                prdorder.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString()));
                noNum = prdorderDao.findPrdorderNoisEnable(prdorder); // 查询编号是否依旧存在
            }
            // WC 2017-08-04 重新计算采购金额
            prdorder.setTotalAmt(countPrdorderMoney(prdorder));
            prdorder.preInsert();
            prdorderDao.insert(prdorder);
            // 添加成功把对应编码的nextNo
            CommonUtils.updateNextNo(8);
        }
        else
        {
            // WC 2017-08-04 重新计算采购金额
            prdorder.preUpdate();
            prdorder.setTotalAmt(prdorderDao.sumTotalAmt(prdorder));
            prdorderDao.update(prdorder);
        }

        // 根据供应商编号去查询统计表中是否有数据
        // PaymentTotal total = paymentTotalDao.selectBySupplierId(prdorder.getSupplier());
        // 子表不为空 连带添加子表
        if (Collections3.isEmpty(prdorder.getPrdorderDetailList()))
        {
            return;
        }

        for (int i = 0; i < prdorder.getPrdorderDetailList().size(); i++)
        {
            PrdorderDetail prdorderDetail = prdorder.getPrdorderDetailList().get(i);
            // 设置主表编号
            prdorderDetail.setPrdorder(prdorder);
            // prdorderDetail.setSubTotal(
            // new BigDecimal(prdorderDetail.getQuantity()).multiply(prdorderDetail.getPrice().getPrice()));// 小计金额
            // 关联的工艺
            ProductCraft contractCraft = prdorderDetail.getContractCraft();
            contractCraft.setCompany(company);
            // 重新为成品采购添加新的工艺信息
            contractCraft.preInsert();
            contractCraftDao.insert(contractCraft);
            // 关联的价格
            ProductPrice price = prdorderDetail.getPrice();
            price.setCraft(contractCraft);
            price.setCompany(company);
            // 重新为成品采购添加新的价位信息
            price.preInsert();
            priceDao.insert(price);
            // 添加成品采购明细
            prdorderDetail.setCompany(company);
            prdorderDetail.setSubTotal(prdorderDetail.getAmountNew(prdorderDetail.getQuantity()));
            prdorderDetail.setStatus(TypeKey.PU_PURCHASING_STATUS_UNCONFIRM.toString()); // TODO
            prdorderDetail.preInsert();
            prdorderDetailDao.insert(prdorderDetail);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Prdorder prdorder)
    {
        super.delete(prdorder);
    }

    /**
     * 加载页面需要的数据
     *
     * @return
     */
    public Map<String, List<?>> getDictValueByItems(Boolean queryAll)
    {
        // 返回结果
        Map<String, List<?>> result = Maps.newHashMap();

        // 货币类型
        result.put("currencyTypeList", DictUtils.getValuesByItem(DictItemEnum.CURRENCY_TYPE));

        // 结算方式
        result.put("payWayList", DictUtils.getValuesByItem(DictItemEnum.PAY_WAYS));

        // 税类说明
        result.put("taxDescriptList", DictUtils.getValuesByItem(DictItemEnum.TAX_DESCRIPTS));

        // 送货方式
        result.put("deliverywaysList", DictUtils.getValuesByItem(DictItemEnum.DELIVERY_WAYS));

        // 交货方式
        result.put("freightwaysList", DictUtils.getValuesByItem(DictItemEnum.FREIGHT_WAYS));

        // 交货期限
        result.put("deliverydaysList", DictUtils.getValuesByItem(DictItemEnum.DELIVERY_DAYS));

        // PCB类型
        result.put("boardLevelList", DictUtils.getValuesByItem(DictItemEnum.BOARD_LEVEL));

        // 覆铜板材
        result.put("materialTypeList", DictUtils.getValuesByItem(DictItemEnum.MATERIAL_TYPE));

        // 板材厚度
        result.put("boardThicknessList", DictUtils.getValuesByItem(DictItemEnum.BOARD_THICKNESS));

        // 覆铜要求
        result.put("copperCladThicknessList", DictUtils.getValuesByItem(DictItemEnum.COPPER_CLAD_THICKNESS));

        // 镀层处理
        result.put("surfaceProcessList", DictUtils.getValuesByItem(DictItemEnum.SURFACE_PROCESS));

        // 阻焊类型
        result.put("solderMaskTypeList", DictUtils.getValuesByItem(DictItemEnum.SOLDER_MASK_TYPE));

        // 板面字符
        result.put("characterTypeList", DictUtils.getValuesByItem(DictItemEnum.CHARACTER_TYPE));

        // 成型方式
        result.put("shapingWayList", DictUtils.getValuesByItem(DictItemEnum.SHAPING_WAY));

        // 测试要求
        result.put("testMethodList", DictUtils.getValuesByItem(DictItemEnum.TEST_METHOD));

        // 交货期限
        result.put("assuredaysList", DictUtils.getValuesByItem(DictItemEnum.ASSURE_DAYS));
        // 订单类型
        result.put("ordertypeList", DictUtils.getValuesByItem(DictItemEnum.ORDER_TYPE));

        // 包装要求
        result.put("packingrequirementList", DictUtils.getValuesByItem(DictItemEnum.PACKING_REQUIREMENT));

        // 订单交货期限
        result.put("deliverydaysList", DictUtils.getValuesByItem(DictItemEnum.DELIVERY_DAYS));

        // 生产方式 按照。。。
        result.put("productionTypeList", DictUtils.getValuesByItem(DictItemEnum.PRODUCTION_TYPE));
        // WC 2017-05-27 生产通知单新增三个工艺值
        // 导热
        result.put("daoreList", DictUtils.getValuesByItem(DictItemEnum.DAORE));
        // 耐压
        result.put("naiyaList", DictUtils.getValuesByItem(DictItemEnum.NAIYA));
        // 层数
        result.put("pliesnumberList", DictUtils.getValuesByItem(DictItemEnum.PLIES_NUMBER));
        // 交货要求
        result.put("deliveryRequierList", DictUtils.getValuesByItem(DictItemEnum.DELIVERREQU));
        // 最小线宽/线距
        result.put("lingeSpacingList", DictUtils.getValuesByItem(DictItemEnum.LINE_SPACING));

        // 最小孔径
        result.put("smallApertureList", DictUtils.getValuesByItem(DictItemEnum.SMALL_APERTURE));

        // 半孔
        result.put("halAholeList", DictUtils.getValuesByItem(DictItemEnum.HALF_AHOLE));

        // 埋盲孔
        result.put("buryBlindHoleList", DictUtils.getValuesByItem(DictItemEnum.BURY_BLIND_HOLE));

        // 抗阻
        result.put("resistanceList", DictUtils.getValuesByItem(DictItemEnum.RESISTANCE));
        // 油墨型号
        result.put("inkTypeList", DictUtils.getValuesByItem(DictItemEnum.INK_TYPE));
        // 特殊工艺
        result.put("specialTypeList", DictUtils.getValuesByItem(DictItemEnum.SPECIAL));
        // 板材供应商
        result.put("boardList", materialDao.findManufacturer(UserUtils.getUser().getCompany()));
        // 支付方式
        result.put("payCauseList", DictUtils.getValuesByItem(DictItemEnum.PAY_CAUSE));

        // 获取合同明细添加的“按照。。”的值的ID 有图片上传的生产方式
        List<String> loadIds = Lists.newArrayList();
        for (DictValue dictValue : DictUtils.getValuesByItem(DictItemEnum.PRODUCTION_TYPE))
        {
            if (ConstKey.CONTRACT_REFERENCETYPE_NEW.equals(dictValue.getValue())
                || ConstKey.CONTRACT_REFERENCETYPE_OTHER.equals(dictValue.getValue()))
            {
                loadIds.add(dictValue.getRecordId());
            }
        }
        result.put("referenceTypes", loadIds);
        // 获取按照。。生产的编号和值
        result.put("referenceTypeList", DictUtils.getValuesByItem(DictItemEnum.PRODUCTION_TYPE));

        // 所有供应商
        Supplier supplier = new Supplier();
        User user = UserUtils.getUser();
        if (null != queryAll && !queryAll)
        {
            supplier.setCreatedBy(user);
        }
        supplier.setCompany(user.getCompany());
        supplier.setStatus(TypeKey.MD_SUPPLIER_STATUS_NORMAL);
        result.put("supplierList", supplierDao.findList(supplier));

        // 承接公司，就是子公司下拉列表
        Branch branch = new Branch();
        branch.setCompany(UserUtils.getUser().getCompany());
        result.put("branchList", branchDao.findList(branch));

        return result;
    }

    /**
     * 审核（确认）
     *
     * @param purchasing
     * @return
     */
    @Transactional(readOnly = false)
    public int updateStatus(Prdorder prdorder, String operatingStatus, String oldStatus)
    {
        CockpitUtilsOne cockpit = new CockpitUtilsOne();
        PrdorderDetail prdor = new PrdorderDetail();
        prdor.setRecordId(prdorder.getRecordId());
        cockpit.addOperatingRecord(5,prdor,2);
        prdorder.preUpdate();

        int count = prdorderDao.updateStatus(prdorder);

        // Add: lpjLiu 20161108, 取消采购合同后也需要取消采购明细
        if (null == prdorder.getCompany())
        {
            prdorder.setCompany(UserUtils.getUser().getCompany());
        }
        prdorderDetailDao.updateStutasByPrdorder(prdorder);

        return count;
    }

    @Transactional(readOnly = false)
    public void cancelAllProduct(Prdorder prdorder, String operatingStatus)
    {
        prdorder.preUpdate();

        prdorderDao.updateAllStatus(prdorder);
        prdorderDetailDao.updateAllStutasByPrdorder(prdorder);
    }

    public Map<String, Object> getDefaultPdfData(Prdorder pro, String basePath, Company company)
    {
        Map<String, Object> map = Maps.newHashMap();
        if (pro == null)
        {
            return map;
        }
        Supplier supplier = pro.getSupplier();
        map.put("customer.name", FmtUtils.blank(supplier.getName()));
        map.put("customer.address", FmtUtils.blank(supplier.getAddress()));
        map.put("customer.phone", FmtUtils.blank(supplier.getPhone()));
        map.put("customer.fax", FmtUtils.blank(supplier.getFax()));
        map.put("customer.contact", FmtUtils.blank(supplier.getBizPerson()));
        map.put("contract.orderDate", FmtUtils.formatDate(pro.getOrderDate()));
        map.put("contract.no", FmtUtils.blank(pro.getNo()));
        map.put("contract.deliveryPlace", FmtUtils.blank(pro.getDeliveryPlace()));
        map.put("contract.currencyType",
            DictUtils.getDictValue(pro.getCurrencyType().toString(), DictItemEnum.CURRENCY_TYPE, ""));
        map.put("contract.remark", FmtUtils.blank(pro.getRemark()));

        map.put("contract.purchaser", FmtUtils.blank(pro.getPurchaser().getUserName()));

        // 结算方式及税率
        if (null != pro.getPayWay() && null != pro.getTaxDescript())
        {
            String payWay = DictUtils.getDictValue(pro.getPayWay().toString(), DictItemEnum.PAY_WAYS, "");
            String taxDescript =
                DictUtils.getDictValue(pro.getTaxDescript().toString(), DictItemEnum.TAX_DESCRIPTS, "");
            map.put("contract.payWay", payWay);
            map.put("contract.taxDescript", taxDescript);
            map.put("contract.payWayAndTaxDescript", payWay + "," + taxDescript);
        }

        // 制单人
        if (pro.getPurchaser() != null)
        {
            map.put("branch.maker", FmtUtils.empty(pro.getPurchaser().getUserName()));
        }
        else
        {
            User u = UserUtils.getUser();
            if (u != null)
            {
                map.put("branch.maker", FmtUtils.empty(u.getUserName()));
            }
        }
        BigDecimal total = new BigDecimal(0);
        Branch proBranch = pro.getBranch();// 2016.11.9 ojh 采购时选中的承接公司
        if (pro.getPrdorderDetailList() != null)
        {
            for (int i = 0; i < pro.getPrdorderDetailList().size(); i++)
            {
                PrdorderDetail detail = pro.getPrdorderDetailList().get(i);
                if (detail.getContractCraft() != null)
                {

                    if (i == 0)
                    {
                        ContractDetail cd = detail.getContractDetail();
                        if (cd != null)
                        {
                            Contract ct = cd.getContract();
                            if (ct != null)
                            {
                                Branch branch = ct.getBranch();

                                // 如果成品采购选择的承接公司不为空，且不和合同里的承接公司相同，那么就以成品采购的子公司为准 ojh 2016.11.09
                                if (proBranch != null && !proBranch.getRecordId().equals(branch.getRecordId()))
                                {
                                    branch = branchDao.get(proBranch);
                                }

                                if (branch != null)
                                {
                                    map.put("title1", FmtUtils.empty(branch.getName()));
                                    map.put("title2", FmtUtils.empty(branch.getNameEn()));
                                    map.put("branch.name", FmtUtils.empty(branch.getName()));
                                    map.put("branch.address", FmtUtils.empty(branch.getAddress()));
                                    map.put("branch.phone", FmtUtils.empty(branch.getPhone()));
                                    map.put("branch.fax", FmtUtils.empty(branch.getFax()));
                                    if (branch != null && StringUtils.isNotEmpty(branch.getLogoPath()))
                                    {
                                        String path = FileManageUtils.isLocal() ?
                                            basePath + ConstKey.GLOBAL_FILE_UPLOAD_PATH + branch.getLogoPath() :
                                            FileManageUtils.getUrl(branch.getLogoPath()).toString();
                                        map.put("image.logo", path);
                                    }
                                    if (branch != null && StringUtils.isNotEmpty(branch.getStampPath()))
                                    {
                                        String path = FileManageUtils.isLocal() ?
                                            basePath + ConstKey.GLOBAL_FILE_UPLOAD_PATH + branch.getStampPath() :
                                            FileManageUtils.getUrl(branch.getStampPath()).toString();
                                        map.put("image.stamp", path);
                                    }
                                }
                            }
                        }
                    }
                    ProductCraft craft = detail.getContractCraft();
                    String customerModel = FmtUtils.blank(craft.getCustomerModel());
                    if (StringUtils.isNotBlank(detail.getCustomerMaterialNo()))
                    {
                        customerModel = customerModel + "物料号：" + detail.getCustomerMaterialNo();
                    }
                    map.put("contractDetail" + i + ".customerModel", customerModel);

                    // 规格
                    String spec =
                        FmtUtils.hold0Decimal(craft.getPnlLength()) + "*" + FmtUtils.hold0Decimal(craft.getPnlWidth())
                            + "/" + FmtUtils.empty(craft.getPnlDivisor());
                    map.put("contractDetail" + i + ".spec", spec);

                    // PCB类型
                    if (null != craft.getBoardLevel())
                    {
                        String boardLevel =
                            DictUtils.getDictValue(craft.getBoardLevel().toString(), DictItemEnum.BOARD_LEVEL, "");
                        map.put("contractDetail" + i + ".boardLevel", boardLevel);
                    }
                    // 覆铜板材
                    if (null != craft.getMaterialType())
                    {
                        String materialType =
                            DictUtils.getDictValue(craft.getMaterialType().toString(), DictItemEnum.MATERIAL_TYPE, "");
                        map.put("contractDetail" + i + ".materialType", materialType);
                    }
                    // 板材厚度
                    if (null != craft.getBoardThickness())
                    {
                        String boardThickness = DictUtils.getDictValue(craft.getBoardThickness().toString(),
                            DictItemEnum.BOARD_THICKNESS,
                            "");
                        map.put("contractDetail" + i + ".boardThickness", boardThickness);
                    }
                    // 覆铜要求
                    if (null != craft.getCopperCladThickness())
                    {
                        String copperCladThickness = DictUtils.getDictValue(craft.getCopperCladThickness().toString(),
                            DictItemEnum.COPPER_CLAD_THICKNESS,
                            "");
                        map.put("contractDetail" + i + ".copperCladThickness", copperCladThickness);
                    }
                    // 镀层处理
                    if (null != craft.getSurfaceProcess())
                    {
                        String surfaceProcess = DictUtils.getDictValue(craft.getSurfaceProcess().toString(),
                            DictItemEnum.SURFACE_PROCESS,
                            "");
                        map.put("contractDetail" + i + ".surfaceProcess", surfaceProcess);
                    }
                    // 阻焊类型
                    if (null != craft.getSolderMaskType())
                    {
                        String solderMaskType = DictUtils.getDictValue(craft.getSolderMaskType().toString(),
                            DictItemEnum.SOLDER_MASK_TYPE,
                            "");
                        map.put("contractDetail" + i + ".solderMaskType", solderMaskType);
                    }
                    // 板面字符
                    if (null != craft.getCharacterType())
                    {
                        String characterType = DictUtils.getDictValue(craft.getCharacterType().toString(),
                            DictItemEnum.CHARACTER_TYPE,
                            "");
                        map.put("contractDetail" + i + ".characterType", characterType);
                    }
                    // 成型方式
                    if (null != craft.getShapingWay())
                    {
                        String shapingWay =
                            DictUtils.getDictValue(craft.getShapingWay().toString(), DictItemEnum.SHAPING_WAY, "");
                        map.put("contractDetail" + i + ".shapingWay", shapingWay);
                    }
                    // 导热
                    if (StringUtils.isNotBlank(craft.getDaore()))
                    {
                        String daore = MathUtils.getMathFromStr(DictUtils.getDictValue(craft.getDaore().toString(),
                            DictItemEnum.DAORE,
                            "")) + "W";
                        map.put("contractDetail" + i + ".daore", daore);
                    }
                    // 耐压
                    if (StringUtils.isNotBlank(craft.getNaiya()))
                    {
                        String naiya = DictUtils.getDictValue(craft.getNaiya().toString(), DictItemEnum.NAIYA, "");
                        map.put("contractDetail" + i + ".naiya", naiya);
                    }
                }

                BigDecimal totalFee = BigDecimal.ZERO;
                if (detail.getContractCraft() != null)
                {
                    ProductPrice price = detail.getPrice();
                    map.put("contractDetail" + i + ".price", FmtUtils.holdDecimal(price.getPrice(), 4));
                    map.put("contractDetail" + i + ".engineeringFee",
                        FmtUtils.holdDecimal(price.getEngineeringFee(), 4));
                    map.put("contractDetail" + i + ".sampleFee", FmtUtils.holdDecimal(price.getSampleFee(), 4));
                    map.put("contractDetail" + i + ".testShelfFee", FmtUtils.holdDecimal(price.getTestShelfFee(), 4));
                    map.put("contractDetail" + i + ".mouldFee", FmtUtils.holdDecimal(price.getMouldFee(), 4));
                    map.put("contractDetail" + i + ".othersFee", FmtUtils.holdDecimal(price.getOthersFee(), 4));

                    if (null == price.getEngineeringFee())
                    {
                        price.setEngineeringFee(BigDecimal.ZERO);
                    }
                    totalFee = totalFee.add(price.getEngineeringFee());

                    if (null == price.getFilmFee())
                    {
                        price.setFilmFee(BigDecimal.ZERO);
                    }
                    totalFee = totalFee.add(price.getFilmFee());

                    if (null == price.getMouldFee())
                    {
                        price.setMouldFee(BigDecimal.ZERO);
                    }
                    totalFee = totalFee.add(price.getMouldFee());

                    if (null == price.getTestShelfFee())
                    {
                        price.setTestShelfFee(BigDecimal.ZERO);
                    }
                    totalFee = totalFee.add(price.getTestShelfFee());

                    if (null == price.getOthersFee())
                    {
                        price.setOthersFee(BigDecimal.ZERO);
                    }
                    totalFee = totalFee.add(price.getOthersFee());
                    map.put("contractDetail" + i + ".totalFee", FmtUtils.holdDecimal(totalFee, 4));
                }
                map.put("contractDetail" + i + ".quantity", FmtUtils.blank(detail.getQuantity()));
                BigDecimal subTotal = detail.getSubTotal();
                if (company.getRecordId().equals(ConstKey.COMPANY_LDSY))
                {
                    map.put("contractDetail" + i + ".amount", FmtUtils.holdDecimal(subTotal.subtract(totalFee), 4));
                }
                else
                {
                    map.put("contractDetail" + i + ".amount", FmtUtils.holdDecimal(subTotal, 4));
                }
                try
                {
                    Calendar orderDate = Calendar.getInstance();
                    orderDate.setTime(pro.getOrderDate());
                    if (null != detail.getDeliveryDays())
                    {
                        // 计算交货的天数
                        String deliveryDay =
                            DictUtils.getDictValue(detail.getDeliveryDays(), DictItemEnum.DELIVERY_DAYS, "");
                        // 将交货天数格式化
                        Integer dd = Integer.parseInt(deliveryDay.replace("天交货", ""));
                        // 设置交货日期
                        orderDate.add(Calendar.DATE, dd);
                        // 设置交货日期表单数据
                        map.put("contractDetail" + i + ".deliveryDays",
                            FmtUtils.blank(DateUtils.formatDate(orderDate.getTime())));
                    }
                }
                catch (Exception e)
                {

                }
                // 交货日期
                if (null != detail.getContractDetail().getDeliveryDate())
                {
                    map.put("contractDetail" + i + ".deliveryDate",
                        DateUtils.formatDate(detail.getContractDetail().getDeliveryDate(), "yyyy-MM-dd"));
                    if (null == detail.getDeliveryDays())
                    {
                        map.put("contractDetail" + i + ".deliveryDays",
                            DateUtils.formatDate(detail.getContractDetail().getDeliveryDate(), "yyyy-MM-dd"));
                    }
                }
                map.put("contractDetail" + i + ".rowNumber", i + 1);
                map.put("contractDetail" + i + ".remark", FmtUtils.blank(detail.getRemark()));
                map.put("contractDetail" + i + ".remarkLdsy", "备注:" + FmtUtils.blank(detail.getRemark()));
                // 客户订单号
                if (null != detail.getContractDetail() && null != detail.getContractDetail().getContract())
                {
                    map.put("contract.customerPo",
                        FmtUtils.blank(detail.getContractDetail().getContract().getCustomerPo()));
                }
                if (subTotal != null)
                {
                    total = total.add(subTotal);
                }
            }
        }

        WordsAmount wordsAmount = new WordsAmount(total.toString());
        String amt = "";
        while (wordsAmount.next())
        {
            amt += wordsAmount.getResult();
            map.put("contract.totalAmtCn", amt);
            map.put("contract.totalAmt", FmtUtils.holdDecimal(total, 4));
        }
        return map;
    }

    public static void setFooter(String yeMei, PdfWriter writer, BaseFont bf, int presentFontSize, Rectangle pageSize)
    {
        ItextPdfHeaderFooter headerFooter = new ItextPdfHeaderFooter(yeMei, bf, presentFontSize, pageSize);
        writer.setPageEvent(headerFooter);
    }

    /**
     * 获得PDF 方法
     *
     * @param purchasing
     * @param basePath
     * @return
     */
    public Pdf getPdf(Prdorder prdorder, String basePath)
    {
        // 查询要打印的成品采购信息
        Pdf pdf = new Pdf();
        Company company = UserUtils.getUser().getCompany();
        Prdorder pro = prdorderDao.getPrdorderAndDetail(prdorder);
        String companyId = company.getRecordId();
        if (null != prdorder.getPrintType() && 2 == prdorder.getPrintType())
        {
            List<Supplier> lnSuppliers = prdorderDao.getLnSupplierList(pro);
            if (!CollectionUtils.isEmpty(lnSuppliers))
            {
                pro.setSupplier(lnSuppliers.get(0));
            }
        }
        if (companyId.equals(ConstKey.COMPANY_LDSY) || companyId.equals(ConstKey.COMPANY_KYBWLW) || companyId.equals(
            ConstKey.COMPANY_LNLDSY) || companyId.equals(ConstKey.COMPANY_SZLDH)
            || companyId.equals(ConstKey.COMPANY_JXLDH) || companyId.equals(ConstKey.COMPANY_AOPU))
        {

            ByteArrayOutputStream base = new ByteArrayOutputStream();
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            if (null == pro)
            {
                return pdf;
            }
            Supplier supplier = pro.getSupplier();
            Branch branch = null;
            Contract contract = null;
            if (Collections3.isNotEmpty(pro.getPrdorderDetailList()))
            {
                PrdorderDetail detail = pro.getPrdorderDetailList().get(0);
                if (null != detail && null != detail.getContractDetail() && null != detail.getContractDetail()
                    .getContract())
                {
                    contract = detail.getContractDetail().getContract();
                    if (null != contract.getBranch())
                    {
                        branch = detail.getContractDetail().getContract().getBranch();
                    }
                }
            }
            try
            {
                // 查询出分编号工艺的信息
                Rectangle rectPageSize = new Rectangle(PageSize.A4);
                Document document = new Document(rectPageSize, 10, 10, 20, 40);

                ByteArrayOutputStream page1 = new ByteArrayOutputStream();
                PdfWriter writer = PdfWriter.getInstance(document, page1);
                BaseFont baseFontChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
                setFooter("采购订单:" + pro.getNo(), writer, baseFontChinese, 9, rectPageSize);
                document.open();

                if (StringUtils.isNotBlank(branch.getLogoPath()))
                {
                    String path = FileManageUtils.isLocal() ?
                        basePath + ConstKey.GLOBAL_FILE_UPLOAD_PATH + branch.getLogoPath() :
                        FileManageUtils.getUrl(branch.getLogoPath()).toString();
                    Image img1 = Image.getInstance(new URL(path));
                    img1.setAbsolutePosition(25, 780);
                    // 以宽高100，75的比例为基准计算
                    Float width = img1.getWidth();
                    Integer computWidth = 1;
                    if (null != width)
                    {
                        computWidth =
                            Integer.valueOf((int)(width / 100)) == 0 ? 1 : Integer.valueOf((int)(width / 100));
                    }
                    Float height = img1.getHeight();
                    Integer computheight = 1;
                    if (null != height)
                    {
                        computheight =
                            Integer.valueOf((int)(width / 100)) == 0 ? 1 : Integer.valueOf((int)(width / 100));
                    }
                    if (computWidth > computheight)
                    {
                        img1.scalePercent(75 / computheight);
                    }
                    else
                    {
                        img1.scalePercent(75 / computWidth);
                    }
                    document.add(img1);
                }

                // 主表-无边框
                PdfPTable table = new PdfPTable(1);
                table.setSplitLate(false); // 这页表格能放多少就放多少
                table.setTotalWidth(556f);
                table.setLockedWidth(true);
                table.getDefaultCell().setBorder(PdfPCell.NO_BORDER);

                // 公司名称
                PdfPCell title = new PdfPCell(new ParagraphCn16(company.getName()));
                title.setHorizontalAlignment(Element.ALIGN_CENTER);
                title.setBorder(PdfPCell.NO_BORDER);
                table.addCell(title);

                PdfPTable sub = new PdfPTable(1);

                // 英文名字
                PdfPCell cell = new PdfPCell(new ParagraphCn12(company.getNameEn()));
                cell.setMinimumHeight(36);
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setBorder(PdfPCell.NO_BORDER);
                cell.setColspan(1);
                sub.addCell(cell);

                cell = new PdfPCell(new ParagraphCn12("成品采购单"));
                cell.setMinimumHeight(36);
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setBorder(PdfPCell.NO_BORDER);
                cell.setColspan(1);
                sub.addCell(cell);
                table.addCell(sub);

                cell = new PdfPCell(new ParagraphCn12(""));
                cell.setPaddingTop(5);
                cell.setPadding(10f);
                table.addCell(cell);

                // 双方信息
                PdfPTable infoTable = new PdfPTable(10);

                cell = new PdfPCell(new ParagraphCn9("供方：" + FmtUtils.empty(supplier.getName())));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                infoTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("需方：" + FmtUtils.empty(branch.getName())));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                infoTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("地址：" + FmtUtils.blank(supplier.getAddress())));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                infoTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("地址：" + FmtUtils.blank(branch.getAddress())));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                infoTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("电话：" + FmtUtils.blank(supplier.getPhone())));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                infoTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("电话：" + FmtUtils.blank(branch.getPhone())));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                infoTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("传真：" + FmtUtils.blank(supplier.getFax())));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                infoTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("传真：" + FmtUtils.blank(branch.getFax())));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                infoTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("联系人：" + FmtUtils.blank(supplier.getBizPerson())));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                infoTable.addCell(cell);

                String maker = null;
                if (pro.getPurchaser() != null)
                {
                    maker = FmtUtils.empty(pro.getPurchaser().getUserName());
                }
                else
                {
                    User u = UserUtils.getUser();
                    if (u != null)
                    {
                        maker = FmtUtils.empty(u.getUserName());
                    }
                }
                cell = new PdfPCell(new ParagraphCn9("制单：" + FmtUtils.blank(maker)));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                infoTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("日期：" + FmtUtils.formatDate(pro.getOrderDate())));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                infoTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("编号：" + FmtUtils.blank(contract.getCustomerPo())));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                infoTable.addCell(cell);
                table.addCell(infoTable);

                // 列表提示
                cell = new PdfPCell(new ParagraphCn12("经需供双方同意成交下列产品，订立条款如下："));
                cell.setPaddingTop(10);
                cell.setPaddingBottom(5);
                cell.setBorder(PdfPCell.NO_BORDER);
                table.addCell(cell);

                cell = new PdfPCell(new ParagraphCn12(""));
                cell.setPaddingTop(5);
                cell.setPadding(10f);
                table.addCell(cell);

                // 列表标题
                PdfPTable titleTable = new PdfPTable(26);

                cell = new PdfPCell(new ParagraphCn9("序号"));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(2);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                titleTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("客户型号"));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(8);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                titleTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("尺寸"));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(4);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                titleTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("交货日期"));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(3);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                titleTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("数量"));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(2);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                titleTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("单价"));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(3);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                titleTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("合计金额"));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(4);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                titleTable.addCell(cell);
                table.addCell(titleTable);

                cell = new PdfPCell(new ParagraphCn12(""));
                cell.setPaddingTop(5);
                cell.setPadding(10f);
                table.addCell(cell);

                // 列表内容
                BigDecimal total = new BigDecimal(0);
                if (Collections3.isNotEmpty(pro.getPrdorderDetailList()))
                {
                    for (int i = 0; i < pro.getPrdorderDetailList().size(); i++)
                    {
                        PrdorderDetail detail = pro.getPrdorderDetailList().get(i);
                        StringBuffer sb1 = new StringBuffer("");
                        StringBuffer sb2 = new StringBuffer("");
                        ProductCraft craft = detail.getContractCraft();
                        String customerModel = FmtUtils.blank(craft.getCustomerModel());
                        if (StringUtils.isNotBlank(detail.getCustomerMaterialNo()))
                        {
                            customerModel = customerModel + "物料号：" + detail.getCustomerMaterialNo();
                        }

                        // 规格
                        String spec = FmtUtils.hold0Decimal(craft.getPnlLength()) + "*"
                            + FmtUtils.hold0Decimal(craft.getPnlWidth()) + "/" + FmtUtils.empty(craft.getPnlDivisor());
                        // sb1.append("规格(mm)：" + spec + ",");

                        // PCB类型
                        if (null != craft.getBoardLevel())
                        {
                            String boardLevel =
                                DictUtils.getDictValue(craft.getBoardLevel().toString(), DictItemEnum.BOARD_LEVEL, "");
                            sb1.append(boardLevel + ",");
                        }
                        // 覆铜板材
                        if (null != craft.getMaterialType())
                        {
                            String materialType = DictUtils.getDictValue(craft.getMaterialType().toString(),
                                DictItemEnum.MATERIAL_TYPE,
                                "");
                            sb1.append(materialType + ",");
                        }
                        // 板材厚度
                        if (null != craft.getBoardThickness())
                        {
                            String boardThickness = DictUtils.getDictValue(craft.getBoardThickness().toString(),
                                DictItemEnum.BOARD_THICKNESS,
                                "");
                            sb1.append(boardThickness + ",");
                        }
                        // 覆铜要求
                        if (null != craft.getCopperCladThickness())
                        {
                            String copperCladThickness =
                                DictUtils.getDictValue(craft.getCopperCladThickness().toString(),
                                    DictItemEnum.COPPER_CLAD_THICKNESS,
                                    "");
                            sb1.append(copperCladThickness + ",");
                        }
                        // 镀层处理
                        if (null != craft.getSurfaceProcess())
                        {
                            String surfaceProcess = DictUtils.getDictValue(craft.getSurfaceProcess().toString(),
                                DictItemEnum.SURFACE_PROCESS,
                                "");
                            sb1.append(surfaceProcess + ",");
                        }
                        // 阻焊类型
                        if (null != craft.getSolderMaskType())
                        {
                            String solderMaskType = DictUtils.getDictValue(craft.getSolderMaskType().toString(),
                                DictItemEnum.SOLDER_MASK_TYPE,
                                "");
                            sb1.append(solderMaskType + ",");
                        }
                        // 板面字符
                        if (null != craft.getCharacterType())
                        {
                            String characterType = DictUtils.getDictValue(craft.getCharacterType().toString(),
                                DictItemEnum.CHARACTER_TYPE,
                                "");
                            sb1.append(characterType + ",");
                        }
                        // 成型方式
                        if (null != craft.getShapingWay())
                        {
                            String shapingWay =
                                DictUtils.getDictValue(craft.getShapingWay().toString(), DictItemEnum.SHAPING_WAY, "");
                            sb1.append(shapingWay + ",");
                        }
                        // 测试要求
                        if (null != craft.getTestMethod())
                        {
                            sb1.append(
                                DictUtils.getDictValue(craft.getTestMethod().toString(), DictItemEnum.TEST_METHOD, "")
                                    + ",");
                        }
                        // 导热
                        if (StringUtils.isNotBlank(craft.getDaore()))
                        {
                            String daore = MathUtils.getMathFromStr(DictUtils.getDictValue(craft.getDaore().toString(),
                                DictItemEnum.DAORE,
                                "")) + "W";
                            sb1.append(daore + ",");
                        }
                        // 耐压
                        if (StringUtils.isNotBlank(craft.getNaiya()))
                        {
                            String naiya = DictUtils.getDictValue(craft.getNaiya().toString(), DictItemEnum.NAIYA, "");
                            sb1.append(naiya + ",");
                        }

                        // 通孔个数
                        if (StringUtils.isNotBlank(craft.getThroughHole()))
                        {
                            sb1.append("通孔个数:" + FmtUtils.blank(craft.getThroughHole()) + ",");
                        }
                        // 沉头孔个数
                        if (StringUtils.isNotBlank(craft.getCountersinkHole()))
                        {
                            sb1.append("沉头孔个数:" + FmtUtils.blank(craft.getCountersinkHole()) + ",");
                        }
                        // 特殊工艺
                        if (StringUtils.isNotBlank(craft.getSpecialCraft()))
                        {
                            String[] strs = craft.getSpecialCraft().split(",");
                            if (null != strs && strs.length > 0)
                            {
                                String specialCraft = "";
                                for (String specialCraftId : strs)
                                {
                                    if (StringUtils.isNotBlank(specialCraft))
                                    {
                                        specialCraft = specialCraft + "," + DictUtils.getDictValue(specialCraftId,
                                            DictItemEnum.SPECIAL,
                                            "");
                                    }
                                    else
                                    {
                                        specialCraft = DictUtils.getDictValue(specialCraftId, DictItemEnum.SPECIAL, "");
                                    }
                                }
                                sb1.append("特殊工艺:(" + FmtUtils.blank(specialCraft) + "),");
                            }
                        }
                        // 出货地点
                        if (StringUtils.isNotBlank(craft.getShippingAddress()))
                        {
                            sb1.append(FmtUtils.blank(craft.getShippingAddress()) + ",");
                        }
                        // 板材
                        if (StringUtils.isNotBlank(craft.getBoard()))
                        {
                            sb1.append(FmtUtils.blank(craft.getBoard()) + ",");
                        }
                        // 油墨型号
                        if (null != craft.getInkType())
                        {
                            sb1.append(
                                DictUtils.getDictValue(craft.getInkType().toString(), DictItemEnum.INK_TYPE, "") + ",");
                        }

                        BigDecimal totalFee = BigDecimal.ZERO;
                        ProductPrice price = detail.getPrice();
                        if (null != prdorder.getPrintType() && 2 == prdorder.getPrintType())
                        {
                            price = detail.getLnPrice() == null ? price : detail.getLnPrice();
                        }
                        if (detail.getContractCraft() != null)
                        {
                            if (null == price.getEngineeringFee())
                            {
                                price.setEngineeringFee(BigDecimal.ZERO);
                            }
                            totalFee = totalFee.add(price.getEngineeringFee());
                            sb2.append("工程费: " + FmtUtils.holdDecimal(price.getEngineeringFee(), 4) + ",");

                            if (null == price.getFilmFee())
                            {
                                price.setFilmFee(BigDecimal.ZERO);
                            }
                            totalFee = totalFee.add(price.getFilmFee());
                            sb2.append("菲林费用: " + FmtUtils.holdDecimal(price.getFilmFee(), 4) + ",");

                            if (null == price.getMouldFee())
                            {
                                price.setMouldFee(BigDecimal.ZERO);
                            }
                            totalFee = totalFee.add(price.getMouldFee());
                            sb2.append("模具费: " + FmtUtils.holdDecimal(price.getMouldFee(), 4) + ",");

                            if (null == price.getTestShelfFee())
                            {
                                price.setTestShelfFee(BigDecimal.ZERO);
                            }
                            totalFee = totalFee.add(price.getTestShelfFee());
                            sb2.append("测试架费: " + FmtUtils.holdDecimal(price.getTestShelfFee(), 4) + ",");

                            if (null == price.getOthersFee())
                            {
                                price.setOthersFee(BigDecimal.ZERO);
                            }
                            totalFee = totalFee.add(price.getOthersFee());
                            sb2.append("其它费用: " + FmtUtils.holdDecimal(price.getOthersFee(), 4) + ",");
                        }
                        BigDecimal subTotal = detail.getSubTotal();
                        if (null != prdorder.getPrintType() && 2 == prdorder.getPrintType())
                        {
                            subTotal = detail.getLnSubTotal() == null ? subTotal : detail.getLnSubTotal();
                        }
                        if (subTotal != null)
                        {
                            total = total.add(subTotal);
                        }

                        PdfPTable detailTable = new PdfPTable(26);

                        // 序号
                        cell = new PdfPCell(new ParagraphCn9(i + 1));
                        cell.setPaddingTop(2);
                        cell.setPaddingBottom(4);
                        cell.setColspan(2);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        cell.setBorder(PdfPCell.NO_BORDER);
                        detailTable.addCell(cell);

                        // 客户型号
                        cell = new PdfPCell(new ParagraphCn9(customerModel));
                        cell.setPaddingTop(2);
                        cell.setPaddingBottom(4);
                        cell.setColspan(8);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        cell.setBorder(PdfPCell.NO_BORDER);
                        detailTable.addCell(cell);

                        // 尺寸
                        cell = new PdfPCell(new ParagraphCn9(spec));
                        cell.setPaddingTop(2);
                        cell.setPaddingBottom(4);
                        cell.setColspan(4);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        cell.setBorder(PdfPCell.NO_BORDER);
                        detailTable.addCell(cell);

                        // 交货日期
                        String deliveryDate = null;
                        if (null != detail.getDeliveryDate())
                        {
                            deliveryDate = DateUtils.formatDate(detail.getDeliveryDate(), "yyyy-MM-dd");
                        }
                        else if (null != detail.getContractDetail() && null != detail.getContractDetail()
                            .getDeliveryDate())
                        {
                            deliveryDate =
                                DateUtils.formatDate(detail.getContractDetail().getDeliveryDate(), "yyyy-MM-dd");
                        }
                        cell = new PdfPCell(new ParagraphCn9(deliveryDate));
                        cell.setPaddingTop(2);
                        cell.setPaddingBottom(4);
                        cell.setColspan(3);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        cell.setBorder(PdfPCell.NO_BORDER);
                        detailTable.addCell(cell);

                        // 数量
                        cell = new PdfPCell(new ParagraphCn9(FmtUtils.blank(detail.getQuantity())));
                        cell.setPaddingTop(2);
                        cell.setPaddingBottom(4);
                        cell.setColspan(2);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        cell.setBorder(PdfPCell.NO_BORDER);
                        detailTable.addCell(cell);

                        // 单价
                        cell = new PdfPCell(new ParagraphCn9(FmtUtils.holdDecimal(price.getPrice(), 4)));
                        cell.setPaddingTop(2);
                        cell.setPaddingBottom(4);
                        cell.setColspan(3);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        cell.setBorder(PdfPCell.NO_BORDER);
                        detailTable.addCell(cell);

                        // 合计金额
                        String amount = null;
                        if (company.getRecordId().equals(ConstKey.COMPANY_LDSY))
                        {
                            amount = FmtUtils.holdDecimal(subTotal.subtract(totalFee), 4);
                        }
                        else
                        {
                            amount = FmtUtils.holdDecimal(subTotal, 4);
                        }
                        cell = new PdfPCell(new ParagraphCn9(amount));
                        cell.setPaddingTop(2);
                        cell.setPaddingBottom(4);
                        cell.setColspan(4);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        cell.setBorder(PdfPCell.NO_BORDER);
                        detailTable.addCell(cell);

                        // 费用
                        cell = new PdfPCell(new ParagraphCn9((sb2.deleteCharAt(sb2.length() - 1)).toString()));
                        cell.setPaddingTop(2);
                        cell.setPaddingBottom(4);
                        cell.setColspan(22);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        cell.setBorder(PdfPCell.NO_BORDER);
                        detailTable.addCell(cell);

                        // 合计费用
                        cell = new PdfPCell(new ParagraphCn9(FmtUtils.holdDecimal(totalFee, 4)));
                        cell.setPaddingTop(2);
                        cell.setPaddingBottom(4);
                        cell.setColspan(4);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        cell.setBorder(PdfPCell.NO_BORDER);
                        detailTable.addCell(cell);

                        // 工艺
                        cell = new PdfPCell(new ParagraphCn9("工艺：" + (sb1.deleteCharAt(sb1.length() - 1)).toString()));
                        cell.setPaddingTop(2);
                        cell.setPaddingBottom(4);
                        cell.setColspan(26);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        cell.setBorder(PdfPCell.NO_BORDER);
                        detailTable.addCell(cell);

                        // 明细备注
                        cell = new PdfPCell(new ParagraphCn9("备注：" + FmtUtils.blank(detail.getRemark())));
                        cell.setPaddingTop(2);
                        cell.setPaddingBottom(4);
                        cell.setColspan(26);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        cell.setBorder(PdfPCell.NO_BORDER);
                        detailTable.addCell(cell);
                        table.addCell(detailTable);

                        cell = new PdfPCell(new ParagraphCn12(""));
                        cell.setPaddingTop(5);
                        cell.setPadding(10f);
                        table.addCell(cell);
                    }
                }

                WordsAmount wordsAmount = new WordsAmount(total != null ? total.toString() : "");
                String amt = "";
                while (wordsAmount.next()) {
                    amt += wordsAmount.getResult();
                }

                PdfPTable askTable = new PdfPTable(26);
                String currencyType = pro.getCurrencyType() != null ? pro.getCurrencyType().toString() : "";
                String dictValue = DictUtils.getDictValue(currencyType, DictItemEnum.CURRENCY_TYPE, "");

                cell = new PdfPCell(new ParagraphCn9("总金额(大写) ：" + dictValue));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(8);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                askTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9(amt));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(13);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                askTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("小 写：" + FmtUtils.holdDecimal(total, 4)));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(5);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                askTable.addCell(cell);

                String payDaysValue = null;
                if (null != pro.getAssureDays())
                {
                    payDaysValue = DictUtils.getDictValue(pro.getAssureDays().toString(), DictItemEnum.ASSURE_DAYS, "");
                }
                if (StringUtils.isBlank(payDaysValue))
                {
                    payDaysValue = "□24个月□12个月□6个月□3个月";
                }
                cell = new PdfPCell(new ParagraphCn9(
                    "一、产品质保期限为自验收合格之日起" + payDaysValue + "内，在质保期限内，产品引起的不良后果及造成的一切损失由供方承担。"));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(26);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                askTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9(
                    "二、供方必须按期交货,逾期应提前与我司协商允准。如未经允准逾期交货的或不能交货的，每逾期交期一日，供方按未交货货值的1%向需方支付违约金，逾期超过七日的，需方有权解除合同，并要求供方承担需方因此所遭受的损失。"));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(26);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                askTable.addCell(cell);

                String showStr = "三、";
                if (null != pro.getPayWay())
                {
                    String payWay = DictUtils.getDictValue(pro.getPayWay().toString(), DictItemEnum.PAY_WAYS, "");
                    if (StringUtils.isNotBlank(payWay))
                    {
                        showStr = showStr + "结算方式：" + payWay;
                    }
                    else
                    {
                        showStr = showStr + "结算方式：□月结90天□60天□30天□现金";
                    }
                }
                if (null != pro.getTaxDescript())
                {
                    String taxDescript =
                        DictUtils.getDictValue(pro.getTaxDescript().toString(), DictItemEnum.TAX_DESCRIPTS, "");
                    showStr = showStr + ",税率:" + taxDescript;
                }
                if (null != pro.getPaycause())
                {
                    String paycause = DictUtils.getDictValue(pro.getPaycause().toString(), DictItemEnum.PAY_CAUSE, "");
                    if (StringUtils.isNotBlank(paycause))
                    {
                        showStr = showStr + ",付款方式：" + paycause;
                    }
                    else
                    {
                        showStr = showStr + ",付款方式：□电汇 □银承";
                    }
                }
                cell = new PdfPCell(new ParagraphCn9(showStr + "。"));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(26);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                askTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9(
                    "四、品质条款：符合IPC-6012（国家标准\\行业标准）标准（特殊要求另议），供方对货品品质、规格、包装等要求须与标准样品及需方所指定的要求相符，否则绝收，需方均按 IPC-6012标准验收。"));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(26);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                askTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9(
                    "五、供方在接到需方对产品质量或其他方面的异议后，应在72小时内（双方另行商定期限者除外）负责处理，否则，即视为默认需方提出的异议和处理意见。"));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(26);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                askTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9(
                    "六、送货要求：送货单请按照需方采购订单要求的产品名称、规格、数量填写，并注明采购单号；送货单经需方收货人签字或盖章方可有效；包装箱内外需贴物料标示并附检验报告。"));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(26);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                askTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9(
                    "七、此合同供需双方各执一份,经供需双方签字或盖章后生效(需方必须盖采购部专用章或合同专用章),复印及传真件具同等法律效力；本合同引起任何争议双方友好协商解决，协商不成提交需方所在地法院管辖。"));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(26);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                askTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("备注：" + FmtUtils.blank(pro.getRemark())));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(26);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                askTable.addCell(cell);
                table.addCell(askTable);

                cell = new PdfPCell(new ParagraphCn12(""));
                cell.setPaddingTop(5);
                cell.setPadding(10f);
                table.addCell(cell);

                // 底部
                PdfPTable bottomTable = new PdfPTable(26);

                cell = new PdfPCell(new ParagraphCn9("供方签字"));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(19);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                bottomTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("需方签字"));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(2);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                bottomTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9(FmtUtils.blank(pro.getPurchaser().getUserName())));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(2);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                bottomTable.addCell(cell);

                if (StringUtils.isNotEmpty(branch.getStampPath()))
                {
                    String path = FileManageUtils.isLocal() ?
                        basePath + ConstKey.GLOBAL_FILE_UPLOAD_PATH + branch.getStampPath() :
                        FileManageUtils.getUrl(branch.getStampPath()).toString();
                    Image img2 = Image.getInstance(new URL(path));
                    img2.setAbsolutePosition(780, 25);
                    // 以宽高100，75的比例为基准计算
                    Float width = img2.getWidth();
                    Integer computWidth = 1;
                    if (null != width)
                    {
                        computWidth = Integer.valueOf((int)(width / 100));
                    }
                    Float height = img2.getHeight();
                    Integer computheight = 1;
                    if (null != height)
                    {
                        computheight = Integer.valueOf((int)(width / 100));
                    }
                    if (computWidth > computheight)
                    {
                        img2.scalePercent(75 / computheight);
                    }
                    else
                    {
                        img2.scalePercent(75 / computWidth);
                    }
                    cell = new PdfPCell(img2);
                    cell.setPaddingTop(2);
                    cell.setPaddingBottom(4);
                    cell.setColspan(3);
                    cell.setRowspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    cell.setBorder(PdfPCell.NO_BORDER);
                    bottomTable.addCell(cell);
                }
                else
                {
                    cell = new PdfPCell();
                    cell.setPaddingTop(2);
                    cell.setPaddingBottom(4);
                    cell.setColspan(3);
                    cell.setRowspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    cell.setBorder(PdfPCell.NO_BORDER);
                    bottomTable.addCell(cell);
                }

                cell = new PdfPCell(new ParagraphCn9("(盖章/日期)"));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(19);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                bottomTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("(盖章/日期)"));
                cell.setPaddingTop(2);
                cell.setPaddingBottom(4);
                cell.setColspan(4);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                bottomTable.addCell(cell);

                table.addCell(bottomTable);

                document.add(table);
                document.close();

                output = page1;
                base = output;
                pdf = new Pdf();
                pdf.setOutPut(base);
                pdf.setShowName(pro.getNo());

            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
        else
        {
            pdf.setFileName(ConstKey.DEFAULT_TEMPLATE_PATH + "productOrder3.pdf");
            pdf.setShowName(pro.getNo());
            Map<String, Object> map = getDefaultPdfData(pro, basePath, company);
            if (null != map)
            {
                pdf.setDataMap(map);
            }
        }
        return pdf;
    }

    /**
     * WC 2017-08-04 重新计算成品采购单金额
     */
    public BigDecimal countPrdorderMoney(Prdorder prdorder)
    {
        BigDecimal totalAmt = BigDecimal.ZERO;
        if (prdorder == null || Collections3.isEmpty(prdorder.getPrdorderDetailList()))
        {
            return totalAmt;
        }
        List<PrdorderDetail> list = prdorder.getPrdorderDetailList();
        for (PrdorderDetail pd : list)
        {
            BigDecimal subTotal = pd.getAmount(pd.getQuantity());
            subTotal = subTotal == null ? BigDecimal.ZERO : subTotal;
            totalAmt = totalAmt.add(subTotal);
        }
        return totalAmt;
    }

    /**
     * WC 2017-08-02 修改采购单保存时重新计算明细的金额
     */
    @Transactional(readOnly = false)
    public Boolean updateDetailsAmout(Prdorder prdorder)
    {
        List<PrdorderDetail> list = prdorder.getPrdorderDetailList();
        if (Collections3.isEmpty(list))
        {
            return false;
        }
        // 根据供应商编号去查询统计表中是否有数据
        prdorder.getSupplier().setCompany(UserUtils.getUser().getCompany());
        for (PrdorderDetail pd : list)
        {
            BigDecimal nowAmount = pd.getAmount(pd.getQuantity());
            BigDecimal befAmount = prdorderDetailDao.getDetailAmount(pd.getRecordId());
            BigDecimal subAmount = nowAmount.subtract(befAmount);
            if (subAmount.compareTo(BigDecimal.ZERO) == 0)
            {
                break;
            }
            pd.setSubTotal(nowAmount);
            prdorderDetailDao.updateDetailAmout(pd);
        }
        return true;
    }

    // tj 生成采购金额审批
    @Transactional(readOnly = false)
    public String prdorderApproval(Prdorder prdorder)
    {
        // return ModifyUtils.prdorderApproval(prdorder);
        boolean prd = ModifyUtils.allocationExist("2", UserUtils.getUser().getCompany());
        String re = "fail";
        if (prd)
        {
            re = ModifyUtils.prdorderApproval(prdorder);
        }
        else
        {
            List<PrdorderDetail> details = prdorderDetailDao.findDetailByPrdorder(prdorder);
            for (PrdorderDetail detail : details)
            {
                if (ModifyUtils.judgeMoneyWhetherChange(detail.getRecordId(),
                    prdorder.getNo(),
                    prdorder.getCompany().getRecordId(),
                    "12",
                    detail.getSubTotal(),
                    ModifyUtilsImpl.computeAreaDetail(detail)))
                {
                    re = ModifyUtils.prdDetailMoneyApproval(detail);
                }
            }
        }
        return re;
    }

    // tj 采购结案
    @Transactional(readOnly = false)
    public String closingCase(PrdorderDetail detail)
    {
        String re = "success";
        Prdorder prdorder = detail.getPrdorder();

        // 验证入库数量
        List<ProductStore> productStores = productStoreDao.findProductStoreByDetail(detail);
        BigDecimal storesq = BigDecimal.ZERO;
        for (ProductStore product : productStores)
        {
            storesq = storesq.add(new BigDecimal(product.getQuantity()));
        }
        // 验证检测数量
        List<SourceDetection> detections = sourceDetectionDao.findByPrdorderDetail(detail);
        BigDecimal detectionNum = BigDecimal.ZERO;
        for (SourceDetection detection : detections)
        {
            if (TypeKey.ST_SOURCEDETECTION_STATUS_AUDITED - detection.getStatus() == 0
                || TypeKey.ST_SOURCEDETECTION_STATUS_INHOUSE - detection.getStatus() == 0)
            {
                detectionNum = detectionNum.add(detection.getDetectionNum().subtract(detection.getPoorNum()));
            }
            if (TypeKey.ST_SOURCEDETECTION_STATUS_APPLY - detection.getStatus() == 0)
            {
                re = "此采购单您还有未检测完成的来料检测单，请完成检测单、入库后再操作！";
                return re;
            }
        }
        if (detectionNum.compareTo(storesq) != 0)
        {
            re = "此采购单您还有已检测完成但是没有入库的产品，请入库后再操作！";
            return re;
        }
        // 计算差值
        BigDecimal q = new BigDecimal(detail.getQuantity());
        BigDecimal subTotal = BigDecimal.ZERO;
        BigDecimal difference = storesq.subtract(q); // 数量差值
        subTotal = difference.multiply(detail.getPrice().getPrice()); // 总价差值
        prdorder.setTotalAmt(prdorder.getTotalAmt().add(difference));
        detail.setSubTotal(detail.getSubTotal().add(subTotal));
        detail.setQuantity(storesq.intValue());
        detail.setStatus(TypeKey.PU_PURCHASING_STATUS_ALREADY_IN.toString());
        // 更新采购明细，采购单，报表数据
        PrdorderDetail pdd = prdorderDetailDao.getDetailAndContracDetail(detail);
        // 修改采购报表
        pdd.setSubTotal(subTotal);
        pdd.setQuantity(difference.abs().intValue());
        Prdorder prd = prdorderDao.get(prdorder);
        prd.setPrdorderDetail(pdd);

        prdorderDetailDao.updateStatusAndQuantity(detail);
        Integer count = prdorderDetailDao.countNotProductDetail(detail);
        if (count == 0 || null == count)
        {
            prdorder.setStatus(TypeKey.PU_PURCHASING_STATUS_ALREADY_IN.toString());
            prdorderDao.updateStatusAndTotalAmt(prdorder);
        }
        else
        {
            prdorderDao.updateTotalAmt(prdorder);
        }
        return re;
    }

    /** tj 结案信息 */
    public List<PrdorderDetail> closingCaseInfo(Prdorder prdorder)
    {
        List<PrdorderDetail> details = prdorderDetailDao.closingCaseInfo(prdorder);
        return details;
    }

    public String getPrice(String recordId)
    {
        ContractCraft craft = new ContractCraft();
        craft.setCompany(UserUtils.getUser().getCompany());
        craft.setRecordId(recordId);
        return priceDao.getPrice(craft);
    }

    @Transactional(readOnly = false)
    public String purchInStocks(Prdorder prdorder)
    {
        List<ProductStore> productStoreList = Lists.newArrayList();
        String factoryComId = CompanyUtil.getInstance().getFactId();
        prdorder.setFactoryComId(factoryComId);
        List<PrdorderDetail> deailList = prdorderDetailDao.getPrdDetailListByPurchId(prdorder);
        for (int i = 0; i < deailList.size(); i++)
        {
            PrdorderDetail details = deailList.get(i);
            Company company = UserUtils.getUser().getCompany();
            // 执行采购入库操作
            if (CompanyUtil.getInstance().valiteSaleCompany())
            {
                ChangeDataUtils changeDataUtils = new ChangeDataUtils();
                ProductStore product = changeDataUtils.genPurchData(details);
                productStoreList.add(product);
                String groupCenterId = details.getGroupCenterId();
                List<PrdorderDetail> prdList =
                    prdorderDetailDao.getPrdDetailListByGroupCenterId(groupCenterId, company, factoryComId);
                for (int j = 0; j < prdList.size(); j++)
                {
                    PrdorderDetail detail = prdList.get(j);
                    product = changeDataUtils.genPurchData(detail);
                    productStoreList.add(product);
                }
            }
            else
            {
                return "false";
            }
            // 更新采购入库记录旧id
            updateOldPrdStockIdData(productStoreList);
        }
        return "success";
    }

    public List<ProDetailInVo> getWaitingInList(ProDetailInVo vo)
    {
        vo.setCompany(UserUtils.getUser().getCompany());
        return prdorderDetailDao.getWaitingInList(vo);
    }

    public void updateOldPrdStockIdData(List<ProductStore> productStoreList)
    {
        if (Collections3.isEmpty(productStoreList))
        {
            return;
        }
        String recordIds = null;
        String oldPrdStockId = null;
        for (ProductStore ps : productStoreList)
        {
            if (StringUtils.isNotBlank(recordIds))
            {
                recordIds = recordIds + "," + ps.getRecordId();
            }
            else
            {
                recordIds = ps.getRecordId();
            }
            if (null != ps.getCompany() && StringUtils.isBlank(oldPrdStockId) && (ps.getCompany()
                .getRecordId()
                .equals(CompanyUtil.getInstance().getEcoemyId())))
            {
                oldPrdStockId = ps.getRecordId();
            }
        }
        if (StringUtils.isNotBlank(oldPrdStockId))
        {
            ProductStore productStore = new ProductStore();
            productStore.setRecordId(recordIds);
            productStore.setOldPrdStockId(oldPrdStockId);
            productStoreDao.updateOldPrdStockId(productStore);
        }
    }

    @Transactional(readOnly = false)
    public String saveInStore(List<ProDetailInVo> comList)
    {
        Date deliveryDate = comList.get(0).getInTime() == null ? new Date() : comList.get(0).getInTime();
        List<ProDetailInVo> deailList = canInStock(comList);
        if (null == deailList)
        {
            return "false";
        }
        List<ProductStore> productStoreList = Lists.newArrayList();
        Company company = UserUtils.getUser().getCompany();
        ChangeDataUtils changeDataUtils = new ChangeDataUtils();
        ProDetailInVo details = null;
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
        String proBatchNo = "ICC" + sf.format(new Date());
        for (int i = 0; i < deailList.size(); i++)
        {
            details = deailList.get(i);
            details.setInTime(deliveryDate);
            details.setCompany(company);
            details.setPrdBatchNo(proBatchNo);
            // 执行采购入库操作
            if (CompanyUtil.getInstance().getFactId().contains(company.getRecordId()))
            {
                ProductStore product = changeDataUtils.genPurchDetailData(details, false);
                productStoreList.add(product);
            }
            else if (CompanyUtil.getInstance().valiteSaleCompany())
            {
                ProductStore product = changeDataUtils.genPurchDetailData(details, false);
                productStoreList.add(product);
                String groupCenterId = details.getGroupCenterId();
                List<ProDetailInVo> prdList = prdorderDetailDao.getWaitingInListByGroupCenterId(groupCenterId, company);
                for (int j = 0; j < prdList.size(); j++)
                {
                    ProDetailInVo detail = prdList.get(j);
                    Boolean sentDeliveryFlag = false;
                    if (detail.getCompany().getRecordId().equals(CompanyUtil.getInstance().getEcoemyId()))
                    {
                        sentDeliveryFlag = true;
                    }
                    detail.setInTime(deliveryDate);
                    detail.setInStock(details.getInStock());
                    product = changeDataUtils.genPurchDetailData(detail, sentDeliveryFlag);
                    productStoreList.add(product);
                }
            }
            else
            {
                return "false";
            }

            // 更新对账单明细终端id
            updateOldPrdStockIdData(productStoreList);
        }
        return "success";
    }

    /**
     * 验证是否入库
     *
     * @param deailList
     * @return
     */
    private List<ProDetailInVo> canInStock(List<ProDetailInVo> deailList)
    {
        // 检查待入库的数量和入库数量的关系
        List<ProDetailInVo> updateList = new ArrayList<>();
        if (CollectionUtils.isEmpty(deailList))
        {
            return null;
        }
        StringBuilder recordB = new StringBuilder();
        for (ProDetailInVo vo : deailList)
        {
            recordB.append(vo.getRecordId() + ",");
        }
        ProDetailInVo query = new ProDetailInVo();
        query.setCompany(UserUtils.getUser().getCompany());
        query.setRecordId(recordB.toString());
        List<ProDetailInVo> dbList = prdorderDetailDao.getWaitingInListByIds(query);
        if (CollectionUtils.isEmpty(dbList))
        {
            return null;// 没找到对应待入库记录
        }
        for (ProDetailInVo v : deailList)
        {
            boolean isOK = false;
            for (ProDetailInVo d : dbList)
            {
                if (v.getRecordId().equals(d.getRecordId()))
                {
                    /*if (d.getQuantity() - d.getInStoreQty() < v.getInStock())
                    {
                        return null;// 待入库数量比入库数小
                    }*/
                    updateList.add(d);
                    d.setInStock(v.getInStock());
                    isOK = true;
                    break;
                }
            }
            if (isOK)
            {
                isOK = false;
            }
            else
            {
                return null;// 没找到待入库记录
            }
        }
        return updateList;
    }

    @Transactional(readOnly = false)
    public String resetLnInfo()
    {
        List<ProDetailInVo> deailList = prdorderDetailDao.getLossLnDetails();
        ChangeDataUtils changeDataUtils = new ChangeDataUtils();
        int count = 0;
        for (int i = 0; i < deailList.size(); i++)
        {
            // 执行采购入库操作
            String groupCenterId = deailList.get(i).getGroupCenterId();
            Company company = new Company("15");
            List<ProDetailInVo> prdList = prdorderDetailDao.getWaitingInListByGroupCenterId(groupCenterId, company);
            for (int j = 0; j < prdList.size(); j++)
            {
                if ("205".equals(prdList.get(j).getCompany().getRecordId()))
                {
                    ProDetailInVo detail = prdList.get(j);
                    detail.setInStock(deailList.get(i).getInStock());
                    detail.setInTime(deailList.get(i).getInTime());
                    changeDataUtils.genPurchDetailData(detail, true);
                    count++;
                }
            }
        }
        return "success" + count;
    }
}