/**
 *
 */
package com.kyb.pcberp.modules.contract.web;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.internal.$Gson$Preconditions;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.mapper.JsonMapper;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.pdf.PdfResult;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.persistence.PageNoInterceptor;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.contract.entity.*;
import com.kyb.pcberp.modules.contract.service.ContractAttachementsService;
import com.kyb.pcberp.modules.contract.service.ContractDetailService;
import com.kyb.pcberp.modules.contract.service.ContractService;
import com.kyb.pcberp.modules.contract.service.NotificationService;
import com.kyb.pcberp.modules.contract.utils.ChangeDataUtils;
import com.kyb.pcberp.modules.crm.entity.*;
import com.kyb.pcberp.modules.crm.service.*;
import com.kyb.pcberp.modules.eg.entity.CardA;
import com.kyb.pcberp.modules.production.entity.Feeding;
import com.kyb.pcberp.modules.production.entity.ProduceBatch;
import com.kyb.pcberp.modules.production.entity.Replenish;
import com.kyb.pcberp.modules.production.service.FeedingService;
import com.kyb.pcberp.modules.purch.entity.PrdorderDetail;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import com.kyb.pcberp.modules.purch.service.PrdorderDetailService;
import com.kyb.pcberp.modules.purch.service.SupplierService;
import com.kyb.pcberp.modules.sys.dao.DictValueDao;
import com.kyb.pcberp.modules.sys.entity.*;
import com.kyb.pcberp.modules.sys.service.BranchService;
import com.kyb.pcberp.modules.sys.service.DepartmentService;
import com.kyb.pcberp.modules.sys.service.ParameterSetService;
import com.kyb.pcberp.modules.sys.service.SystemService;
import com.kyb.pcberp.modules.sys.utils.CockpitUtilsOne;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import com.kyb.pcberp.modules.wechat.entity.InterProduct;
import com.kyb.pcberp.modules.wechat.service.ProductService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * 合同 2015/08/31Controller
 *
 * <AUTHOR>
 * @version 2015-08-31
 */
@Controller
@RequestMapping(value = "${adminPath}/order/contract")
public class ContractController extends BaseController
{
    private final ObjectMapper contractMapper = new JsonMapper().enableSimple();

    private final MappingJackson2JsonView view = new MappingJackson2JsonView();

    @Autowired
    private ContractService contractService;

    @Autowired
    private ContractAttachementsService contractAttachementsService;

    @Autowired
    private SlCustomerOrderService slCustomerOrderServicel;

    @Autowired
    private ProductService productService;

    /**
     * 客户service
     */
    @Autowired
    private CustomerService customerService;

    /**
     * 合同详情
     */
    @Autowired
    private ContractDetailService contractDetailService;

    @Autowired
    private BranchService branchService;

    /**
     * 报价单
     */
    @Autowired
    private QuotationService quotationService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private SlCustomerOrderDetailService slCustomerOrderDetailService;

    @Autowired
    private FeedingService feedingService;

    @Autowired
    private PrdorderDetailService prdorderDetailService;

    @Autowired
    private ParameterSetService parameterSetService;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private ComplaintService complaintService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private SystemService systemService;

    @Autowired
    private DictValueDao dictValueDao;

    @RequestMapping(value = "list")
    public String list(Contract contract)
    {
        return "modules/order/contract/contractList";
    }

    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = {"page"})
    @ResponseBody
    public Page<Contract> getlist(@RequestBody Contract contract, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        // 设置查询范围
        if (contract.getQueryAll() != null && !contract.getQueryAll())
        {
            contract.setCreatedBy(user);
        }
        // 设置查询企业编号
        if (contract != null)
        {
            contract.setCompany(UserUtils.getUser().getCompany());
        }
        if (contract.getSentTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(contract.getSentTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            contract.setSentTimeEndQr(deliTime.getTime());
        }
        if (contract.getSentTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(contract.getSentTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            contract.setSentTimeStartQr(deliTime.getTime());
        }
        // 分页查询数据
        Page<Contract> qpage = new Page<Contract>(request, response);
        if (StringUtils.isNotBlank(contract.getPageNo()) && StringUtils.isNotBlank(contract.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(contract.getPageNo()));
            qpage.setPageSize(Integer.parseInt(contract.getPageSize()));

            // 设置排序
            if (StringUtils.isNotBlank(contract.getOrderBy()))
            {
                qpage.setOrderBy(contract.getOrderBy());
            }

        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        if (contract.getCustomerModel() != null && contract.getCustomerModel() != "")
        {
            contract.setCustomerModel(contract.getCustomerModel().replace(" ", ""));
        }
        Page<Contract> page = contractService.findPage(qpage, contract);
        return page;
    }

    @RequestMapping(value = {"getSaleReportList"})
    @ResponseBody
    public PageNoInterceptor<SaleCombina> getSaleReportList(@RequestBody SaleCombina saleCombina,
        HttpServletRequest request, HttpServletResponse response)
    {
        if (saleCombina.getEndTime() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(saleCombina.getEndTime());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            saleCombina.setEndTime(deliTime.getTime());
        }
        if (saleCombina.getStartTime() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(saleCombina.getStartTime());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            saleCombina.setStartTime(deliTime.getTime());
        }
        PageNoInterceptor<SaleCombina> qpage = new PageNoInterceptor<SaleCombina>(request, response);
        if (StringUtils.isNotBlank(saleCombina.getPageNo()) && StringUtils.isNotBlank(saleCombina.getPageSize()))
        {
            qpage.setPageNo(Integer.parseInt(saleCombina.getPageNo()));
            qpage.setPageSize(Integer.parseInt(saleCombina.getPageSize()));
            Integer no = Integer.parseInt(saleCombina.getPageNo());
            Integer size = Integer.parseInt(saleCombina.getPageSize());
            // 分页查询数据
            if (no >= 1)
            {
                no = (no - 1) * size;
                saleCombina.setPageNo(no.toString());
            }
        }
        else
        {
            saleCombina.setPageNo(Global.PAGE_NO.toString());
            saleCombina.setPageSize(Global.PAGE_SIZE.toString());
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        PageNoInterceptor<SaleCombina> page = contractService.getSaleReportList(qpage, saleCombina);
        return page;
    }

    /**
     * 导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "exportReportList")
    public String exportReportList(SaleCombina saleCombina, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            String startTime = request.getParameter("startTime");
            String endTime = request.getParameter("endTime");
            if (StringUtils.isNotBlank(startTime))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(startTime)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                saleCombina.setStartTime(deliTime.getTime());
            }
            if (StringUtils.isNotBlank(endTime))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(endTime)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                saleCombina.setEndTime(deliTime.getTime());
            }
            String showStatus = request.getParameter("showStatus");
            String showCraftNo = request.getParameter("showCraftNo");
            saleCombina.setShowStatus(showStatus);
            saleCombina.setShowCraftNo(showCraftNo);
            List<SaleCombina> list = contractService.getSaleReportExcelList(saleCombina);
            String fileName = "业务数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            if (StringUtils.isNotBlank(saleCombina.getGroupCenterType())
                && "1".equals(saleCombina.getGroupCenterType()))
            {
                new ExportExcel("业务数据", SaleCombina.class, new Integer(1)).setDataList(list)
                    .write(response, fileName)
                    .dispose();
            }
            else if (StringUtils.isNotBlank(saleCombina.getGroupCenterType())
                && "2".equals(saleCombina.getGroupCenterType()))
            {
                new ExportExcel("业务数据", SaleCombinaOut.class, new Integer(1)).setDataList(list)
                    .write(response, fileName)
                    .dispose();
            }

            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";

    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "delete/{id}", method = {RequestMethod.POST})
    @ResponseBody
    public String delete(@PathVariable("id") String id)
    {
        Contract contract = new Contract(id);
        contract.setCompany(UserUtils.getUser().getCompany());

        slCustomerOrderServicel.updateChF(contract.getRecordId(), UserUtils.getUser().getCompany().getRecordId());
        List<SlCustomerOrderDetail> custOrderList =
            slCustomerOrderServicel.getDelCust(contract.getRecordId(), UserUtils.getUser().getCompany().getRecordId());
        if (custOrderList != null)
        {
            for (int i = 0; i < custOrderList.size(); i++)
            {
                Integer custNum = slCustomerOrderServicel.getCustNum(custOrderList.get(i).getRecordId());
                if (custNum != null)
                {
                    if (custNum == 0)
                    {
                        slCustomerOrderServicel.updateCustSta(custOrderList.get(i).getRecordId());
                    }
                }
            }
        }

        quotationService.updateSta(contract.getRecordId(), UserUtils.getUser().getCompany().getRecordId());
        List<QuotationDetail> quoList =
            quotationService.getDelQuo(contract.getRecordId(), UserUtils.getUser().getCompany().getRecordId());
        if (quoList != null)
        {
            for (int i = 0; i < quoList.size(); i++)
            {
                Integer quNum = quotationService.getQuoNum(quoList.get(i).getRecordId());
                if (quNum != null)
                {
                    if (quNum == 0)
                    {
                        quotationService.updateQuoSta(quoList.get(i).getRecordId());
                    }
                }
            }
        }
        contract = contractService.get(contract);
        contractService.delete(contract);
        return "删除合同“" + contract.getNo() + "”成功！";
    }

    /**
     * 检验正在操作的状态值
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "checkRepet", method = {RequestMethod.POST})
    @ResponseBody
    public String checkRepet(@RequestBody String recordId)
    {
        Contract contract = new Contract();
        contract.setRecordId(recordId);
        Contract contractCopy = contractService.get(contract);
        if (contractCopy != null && contractCopy.getStatus() != null && !contractCopy.getActiveFlag().equals("2"))
        {
            return contractCopy.getStatus();
        }
        else
        {
            return "failure";
        }
    }

    /**
     * 移除明细
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "deleteDetail", method = {RequestMethod.POST})
    @ResponseBody
    public String deleteDetail(@RequestBody ContractDetail contractDetail)
    {
        Integer num = contractDetailService.getContractNum(contractDetail);
        if (num != null)
        {
            if (num > 1)
            {
                contractDetailService.delete(contractDetail);
                ContractDetail contractDetailDel = contractDetailService.get(contractDetail.getRecordId());
                if (StringUtils.isNotBlank(contractDetailDel.getQuotationDetailId()))
                {
                    quotationService.updateDelQu(contractDetailDel.getQuotationDetailId());
                    Integer quNum = quotationService.getQuoNum(contractDetailDel.getQuotationDetailId());
                    if (quNum != null)
                    {
                        if (quNum == 0)
                        {
                            quotationService.updateQuoSta(contractDetailDel.getQuotationDetailId());
                        }
                    }
                }
                if (contractDetailDel.getCustomerOrderDetailId() != null)
                {
                    if (StringUtils.isNotBlank(contractDetail.getCustomerOrderDetailId().getRecordId()))
                    {
                        // 修改客户下单明细的状态
                        slCustomerOrderServicel.updateDelCh(contractDetailDel.getCustomerOrderDetailId().getRecordId());
                        Integer custNum =
                            slCustomerOrderServicel.getCustNum(contractDetailDel.getCustomerOrderDetailId()
                                .getRecordId());
                        if (custNum != null)
                        {
                            if (custNum == 0)
                            {
                                slCustomerOrderServicel.updateCustSta(contractDetailDel.getCustomerOrderDetailId()
                                    .getRecordId());
                            }
                        }
                    }

                }
                // 删除出库存记录
                contractService.deleteStockUse(contractDetail);

                CockpitUtilsOne cockpitUtilsOne = new CockpitUtilsOne();
                cockpitUtilsOne.addOperatingRecord(1,contractDetail,2);

                return "success";
            }
            else
            {
                return "failure";
            }
        }
        else
        {
            return "failure";
        }

    }

    /**
     * 获取编号方法
     *
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "getNo", method = {RequestMethod.POST})
    @ResponseBody
    public String getNo()
    {
        return CommonUtils.geDocumentNo(CommonEnums.CodeType.CONTRACT.getIndex().toString());
    }

    /**
     * 保存合同
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "save", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> save(@RequestBody Contract contract)
    {
        if (contract.getInventorySwitch().equals("true"))
        {
            contract.setInventorySwitch("1");
        }
        else
        {
            contract.setInventorySwitch("0");
        }
        // 设置公司
        contract.setCompany(UserUtils.getUser().getCompany());
        Customer customer = null;
        if (contract.getCustomer() != null)
        {
            customer = customerService.getCustomer(contract.getCustomer().getRecordId());// 查询客户信息
        }
        User user = customer.getSalesman();
        contract.setUser(user);
        Map<String, Object> map = contractService.saveContract(contract);
        map.put("message", "保存合同“" + contract.getNo() + "”成功！");
        return map;
    }

    /**
     * 查询公司下面的所有客户
     *
     * @param contact
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = {"customers"})
    @ResponseBody
    public List<Customer> contactList(Boolean queryAll)
    {
        if (queryAll == null)
        {
            queryAll = false;
        }

        User user = UserUtils.getUser();
        Customer customer = new Customer();
        customer.setCompany(user.getCompany());
        customer.setStatus(TypeKey.MD_CUSTOMER_STATUS_NORMAL);

        if (!queryAll)
        {
            customer.setSalesman(user);
        }
        return customerService.findCustomers(customer);
    }

    /**
     *
     * 根据编号去查询 详细信息
     *
     * @param RecordId
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "getAllByContractDetail", method = {RequestMethod.POST})
    @ResponseBody
    public List<ContractDetail> getAllByContractDetail(@RequestBody Contract contract)
    {
        contract.setCompany(UserUtils.getUser().getCompany());
        contract.setHideCopyDetailFlag("Y");// 不显示复制的合同明细
        return contractDetailService.findDetailAndCraftsByContractId(contract);
    }

    /**
     * 导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "export")
    public String exportFile(Contract contract, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            if ("0".equals(contract.getStatus()))
            {
                contract.setStatus(null);
            }
            // 设置查询范围
            if (contract.getQueryAll() != null && !contract.getQueryAll())
            {
                contract.setCreatedBy(UserUtils.getUser());
            }
            if (contract.getOrderBy() != null)
            {
                if (contract.getOrderBy().indexOf(".") == -1)
                {
                    contract.setOrderBy("a." + contract.getOrderBy());
                }
            }
            else
            {
                contract.setOrderBy("a.no");
            }

            String inoutTimeStartQr = request.getParameter("sentTimeStartQr");
            String inoutTimeEndQr = request.getParameter("sentTimeEndQr");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(inoutTimeStartQr)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                contract.setSentTimeStartQr(deliTime.getTime());
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(inoutTimeEndQr)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                contract.setSentTimeEndQr(deliTime.getTime());
            }
            contract.setCompany(UserUtils.getUser().getCompany());
            String fileName = "合同数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            // TJ 2017-06-30
            if (contract.getCustomerModel() != null && contract.getCustomerModel() != "")
            {
                contract.setCustomerModel(contract.getCustomerModel().replace(" ", ""));
            } // END
            // rx 2021-12-3 隐藏复制的合同明细
            contract.setHideCopyDetailFlag("Y");
            List<Contract> contracts = contractService.findExpList(contract);
            // 计算价格(元/㎡)
            if (!CollectionUtils.isEmpty(contracts))
            {
                for (Contract con : contracts)
                {
                    con.countSquareMeterPrice();
                }
            }
            new ExportExcel("合同数据", Contract.class, new Integer(1)).setDataList(contracts)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";

    }

    /**
     * 打印
     *
     * @param recordId
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "print")
    public ResponseEntity<byte[]> printPDF(String recordId, HttpServletRequest request, HttpServletResponse response)
    {
        Contract con = new Contract(recordId);
        con.setCompany(UserUtils.getUser().getCompany());
        Pdf pdf = contractService.getPdf(con, request.getSession().getServletContext().getRealPath("/"));
        if (pdf == null)
        {
            ResponseEntity<byte[]> responseEntity = null;
            return responseEntity;
        }
        else
        {
            ResponseEntity<byte[]> responseEntity = null;
            if (null != pdf.getOutPut() && null != pdf.getOutPut().toByteArray())
            {
                byte[] contents = pdf.getOutPut().toByteArray();
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.parseMediaType("application/pdf"));
                String filename = CommonUtils.getDisplayPDFName(pdf.getShowName());
                headers.add("X-File-Name", filename);
                headers.setContentDispositionFormData(filename, filename);
                responseEntity = new ResponseEntity<byte[]>(contents, headers, HttpStatus.OK);
                return responseEntity;
            }
            else
            {
                try
                {
                    new PdfResult().doExecute(pdf.getFileName(),
                        request,
                        response,
                        pdf.getDataMap(),
                        pdf.getShowName());
                }
                catch (IOException e)
                {
                    e.printStackTrace();
                }
            }
            return responseEntity;
        }
    }

    /**
     * 查询报价单
     *
     * @param findQuoByU 状态为1表示根据客户来查询
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "findListOfQuotation", method = {RequestMethod.POST})
    @ResponseBody
    public List<Quotation> findListOfQuotation(String no, String start, Boolean queryAll, Integer findQuoByU,
        String customerId)
    {
        Quotation quotation = new Quotation();
        if (findQuoByU != null && findQuoByU == 1)
        {
            if (StringUtils.isNotBlank(customerId))
            {
                Customer customer = new Customer();
                customer.setRecordId(customerId);
                quotation.setCustomer(customer);
            }
            else
            {
                Customer customer = null;
                quotation.setCustomer(customer);
            }
        }
        else
        {
            Customer customer = null;
            quotation.setCustomer(customer);
        }

        User user = UserUtils.getUser();
        if (queryAll != null && !queryAll)
        {
            quotation.setSalesman(user);
        }

        quotation.setCompany(user.getCompany());
        quotation.setNo(no);
        quotation.setPageNo(start);
        quotation.setPageSize(ConstKey.Max_SHOW_SIZE);
        quotation.setStatus(TypeKey.SL_QUOTATION_STATUS_DEALED.toString());
        return quotationService.findListOfVerify(quotation);
    }

    /**
     * 查询客户下单
     *
     * @param findOrderByU 状态为1表示根据客户来查询
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "findListOfCustomerOrder", method = {RequestMethod.POST})
    @ResponseBody
    public List<SlCustomerOrder> findListOfCustomerOrder(String no, String start, Integer findOrderByU,
        String customerId)
    {
        SlCustomerOrder slCustomerOrder = new SlCustomerOrder();
        if (findOrderByU != null && findOrderByU == 1)
        {
            if (StringUtils.isNotBlank(customerId))
            {
                Customer customer = new Customer();
                customer.setRecordId(customerId);
                slCustomerOrder.setCustomer(customer);
            }
            else
            {
                Customer customer = null;
                slCustomerOrder.setCustomer(customer);
            }
        }
        else
        {
            Customer customer = null;
            slCustomerOrder.setCustomer(customer);
        }

        slCustomerOrder.setCompany(UserUtils.getUser().getCompany());
        slCustomerOrder.setNo(no);
        slCustomerOrder.setPageNo(start);
        slCustomerOrder.setPageSize(ConstKey.Max_SHOW_SIZE);
        slCustomerOrder.setStatus(TypeKey.SL_QUOTATION_STATUS_DEALED.toString());
        return slCustomerOrderServicel.findListOfVerify(slCustomerOrder);
    }

    /**
     * 检查合同
     *
     * @param notification
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "findContractNoisEnable", method = RequestMethod.POST)
    @ResponseBody
    public Integer findContractNoisEnable(@RequestBody Contract contract)
    {
        // 设置公司
        contract.setCompany(UserUtils.getUser().getCompany());

        Integer list = contractService.findContractNoisEnable(contract);
        return list;
    }

    /**
     * 根据报价单编号 查询出子表信息
     *
     * @param quotation
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "quotationdetail/{id}", method = {RequestMethod.POST})
    @ResponseBody
    public List<QuotationDetail> findQuotationDetailBycustomerModel(@PathVariable("id") String recordId)
    {
        return quotationService.findQuotationDetailByQuotation(new Quotation(recordId));
    }

    /**
     * 确认合同
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "updateContractStutas", method = RequestMethod.POST)
    @ResponseBody
    public String updateContractStutas(@RequestBody Contract contract)
    {
        // 设置公司
        contract.setCompany(UserUtils.getUser().getCompany());
        contract.setStatus(TypeKey.SL_CONTRACT_STATUS_CONFIRMED.toString());
        // 更新订单时间，统计报表以该时间为依据
        contract.setOrderDate(new Date());
        String str = contractService.contractApproval(contract);
        if (!"fail".equals(str))
        {
            return str;
        }
        contractService.updateContractStutas(contract, TypeKey.SL_CONTRACT_STATUS_CONFIRMED);

        return "确认合同“" + contract.getNo() + "”成功！";
    }

    /**
     * 审核合同
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "updateContractStutasManege", method = RequestMethod.POST)
    @ResponseBody
    public String updateContractStutasManege(@RequestBody Contract contract)
    {
        Company company = UserUtils.getUser().getCompany();

        // 设置公司
        contract.setCompany(company);
        contract.setStatus(TypeKey.SL_CONTRACT_STATUS_MANEGE.toString());
        contractService.updateContractStutasAuditing(contract, TypeKey.SL_CONTRACT_STATUS_MANEGE);

        return "审核合同“" + contract.getNo() + "”成功！";
    }

    /**
     * 查询生产编号
     *
     * @param no
     * @param start
     * @param findProByU为1的时候表示根据客户查询
     * @param customerId
     * @return
     */
    @RequestMapping(value = "notifications")
    @ResponseBody
    public List<ContractCraft> notifications(String no, String start, Integer findProByU, String customerId)
    {
        ContractCraft temp = new ContractCraft();
        if (findProByU != null && findProByU == 1)
        {
            if (StringUtils.isNotBlank(customerId))
            {
                Customer customer = new Customer();
                customer.setRecordId(customerId);
                temp.setCustomer(customer);
            }
            else
            {
                Customer customer = null;
                temp.setCustomer(customer);
            }
        }
        else
        {
            Customer customer = null;
            temp.setCustomer(customer);
        }
        // lh 2018-02-26 是否应用其他客户型号
        // ParameterSet parameterApplicatOtherCustomersCopy = new ParameterSet();
        // parameterApplicatOtherCustomersCopy.setCompany(UserUtils.getUser().getCompany());
        // parameterApplicatOtherCustomersCopy.setJianPin("applicatOtherCustomers");
        // ParameterSet applicatOtherCustomers =
        // parameterSetService.getParameterSetByCompanyAndjianPin(parameterApplicatOtherCustomersCopy);
        // if (applicatOtherCustomers != null && "1".equals(applicatOtherCustomers.getStart()))
        // {
        // Customer customer = null;
        // temp.setCustomer(customer);
        // }

        temp.setCompany(UserUtils.getUser().getCompany());
        temp.setStatus(TypeKey.SL_CRAFT_STATUS_CONFIRMED.toString()); // 条件为等于本状态的
        temp.setNo(no);
        temp.setPageNo(start);
        temp.setPageSize(ConstKey.Max_SHOW_SIZE);
        return notificationService.selectNotification(temp);
    }

    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "selectCraftByNotificationId/{id}", method = RequestMethod.POST)
    @ResponseBody
    public ContractCraft selectCraftByNotificationId(@PathVariable("id") String recordId)
    {
        Notification noti = new Notification(recordId);
        noti.setCompany(UserUtils.getUser().getCompany());
        noti.setStatus(TypeKey.SL_NOTIFICATION_STATUS_CONFIRMED.toString());
        noti.setStatus1(TypeKey.SL_NOTIFICATION_STATUS_FEED.toString());
        return contractService.selectCraftByNotificationId(noti);
    }

    @RequestMapping(value = "selectContractDetailPrice", method = RequestMethod.POST)
    @ResponseBody
    public Price selectContractDetailPrice(@RequestBody ContractDetail contractDetail)
    {

        contractDetail = contractDetailService.get(contractDetail);

        return contractService.selectContractDetailPrice(contractDetail.getPricees());
    }

    /**
     * 通过合同No去查询合同
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "getContractByContractRecordId/{id}", method = RequestMethod.POST)
    @ResponseBody
    public Contract getContractByContractRecordId(@PathVariable("id") String recordId)
    {
        Contract contract = new Contract();
        contract.setNo(recordId);
        contract.setCompany(UserUtils.getUser().getCompany());
        return contractService.selectContractByContractNo(contract);
    }

    /**
     * 加载数据
     *
     * @param model
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "load/data")
    public View loadData(Boolean queryAll, Model model)
    {
        view.setObjectMapper(contractMapper);

        // 查询数据字典
        if (queryAll == null)
        {
            queryAll = false;
        }
        User user = UserUtils.getUser();

        Map<String, List<?>> data = contractService.getDictValueByItems(queryAll);

        String u_id = "";
        if (user != null && StringUtils.isNotBlank(user.getRecordId()))
        {
            u_id = UserUtils.getUser().getRecordId();
        }
        model.addAttribute("u_id", u_id);

        model.addAttribute("data", data);
        model.addAttribute("time", DateUtils.formatDateTime(new Date()));
        // lh 2018-02-26 是否工艺参与报价
        ParameterSet parameterQuotationSettingCopy = new ParameterSet();
        parameterQuotationSettingCopy.setCompany(user.getCompany());
        parameterQuotationSettingCopy.setJianPin("quotationSetting");
        ParameterSet quotationSetting =
            parameterSetService.getParameterSetByCompanyAndjianPin(parameterQuotationSettingCopy);
        model.addAttribute("quotationSetting", quotationSetting);

        List<Supplier> suppliers = supplierService.queryAllSupplierTwo(user.getCompany());
        model.addAttribute("supplierList", suppliers);

        Boolean addBtnFlag = true;
        if (StringUtils.isNotBlank(user.getBranchId()))
        {
            Branch branch = new Branch();
            branch.setCompany(user.getCompany());
            branch.setRecordId(user.getBranchId());
            Integer count = branchService.getBranRoleExcit(branch);
            if (null == count || count == 0)
            {
                addBtnFlag = false;
            }
        }
        model.addAttribute("addBtnFlag", addBtnFlag);

        // 管理费百分比
        ParameterSet ps = new ParameterSet();
        ps.setCompany(user.getCompany());
        ps.setJianPin("managePrecent");
        ParameterSet managePrecent = parameterSetService.getParameterSetByCompanyAndjianPin(ps);
        model.addAttribute("managePrecent", managePrecent);

        String factId = CompanyUtil.getInstance().getFactId();
        BigDecimal lnPrecent = CommonUtils.calculate(user.getCompany().getRecordId(), null, null);
        model.addAttribute("lnPrecent", lnPrecent);

        Department dept = new Department();
        dept.setCompany(user.getCompany());
        dept.setPurpose(TypeKey.DEPART_ORDER.toString());
        List<Department> deptList = departmentService.findList(dept);
        model.addAttribute("deptList", deptList);

        User u = new User();
        u.setCompany(user.getCompany());
        List<User> salesManList = systemService.getSalesManList(u);
        model.addAttribute("salesManList", salesManList);

        ParameterSet query = new ParameterSet();
        Company factCom = new Company();
        factCom.setRecordId(factId);
        query.setCompany(factCom);
        query.setJianPin("maxBatchArea");
        ps = parameterSetService.getParameterSetByCompanyAndjianPin(query);
        if (null != ps && StringUtils.isNotBlank(ps.getParameterValue()))
        {
            model.addAttribute("maxBatchArea", ps.getParameterValue());
        }

        // 数据库id
        model.addAttribute("g_id", UserUtils.getToDbId());

        return view;
    }

    /**
     * 显示 用户所在公司下 的所有 子公司
     *
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "branchs", method = {RequestMethod.POST})
    @ResponseBody
    public List<Branch> branchs()
    {
        Branch branch = new Branch();
        branch.setCompany(UserUtils.getUser().getCompany());
        List<Branch> a = branchService.findList(branch);
        return a;
    }

    /**
     * 修改合同明细上传文件前先清空原有的文件
     *
     * @param originalFilename
     * @param contractNo
     * @param contractDetailId
     * @param contractId
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "cleanFiles")
    @ResponseBody
    public void cleanFiles(@RequestParam("contractNo") String contractNo,
        @RequestParam("contractDetailId") String contractDetailId, @RequestParam("contractId") String contractId,
        @RequestParam("originalFilename") String originalFilename)
    {
        Company company = UserUtils.getUser().getCompany();

        // 若存在多个通知单的情况，将其替换为下划线
        String savePath = company.getRecordId() + "/contactAtt/" + contractNo + "/" + contractDetailId + "/";

        // 删除文件
        FileManageUtils.delFiles(savePath);

        // 基本路径
        String sourcePath = savePath + originalFilename;

        ContractAttachements attach = new ContractAttachements();
        attach.setFileUrl(sourcePath);
        attach.setRealFileName(originalFilename);
        attach.setCompany(company);
        attach.setContractDetailId(contractDetailId);
        attach.setContractId(contractId);

        // 删除原有的数据
        contractAttachementsService.delete(attach);

    }

    /**
     * 上传附件并保存合同附件
     *
     * @param file
     * @param contractNo
     * @param oper
     * @param contractDetailId
     * @param contractId
     * @param req
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "upload")
    @ResponseBody
    public Map<String, Object> uploadFile1(@RequestParam("file") MultipartFile file,
        @RequestParam("contractNo") String contractNo, @RequestParam("contractDetailId") String contractDetailId,
        @RequestParam("contractId") String contractId, HttpServletRequest req)
    {
        Company company = UserUtils.getUser().getCompany();

        // 若存在多个通知单的情况，将其替换为下划线
        String savePath = company.getRecordId() + "/contactAtt/" + contractNo + "/" + contractDetailId + "/";

        Map<String, Object> result = Maps.newHashMap();

        // 上传文件
        String url = FileManageUtils.uploadFile(file, savePath, req);
        if (url == null)
        {
            result.put("result", false);
            return result;
        }

        // 基本路径
        String sourcePath = savePath + file.getOriginalFilename();

        ContractAttachements attach = new ContractAttachements();
        attach.setFileUrl(sourcePath);
        attach.setRealFileName(file.getOriginalFilename());
        attach.setCompany(company);
        attach.setContractDetailId(contractDetailId);
        attach.setContractId(contractId);
        attach.setTempUrl(url);
        contractAttachementsService.save(attach);
        result.put("attach", attach);

        result.put("result", true);

        return result;
    }

    /**
     * 根据合同id和合同明细id查找合同附件
     *
     * @param contractAttachs
     * @return
     */
    @RequestMapping(value = "findContractAttachementsList", method = RequestMethod.POST)
    @ResponseBody
    public List<ContractAttachements> findContractAttachementsList(@RequestBody ContractAttachements contractAttachs)
    {
        contractAttachs.setCompany(UserUtils.getUser().getCompany());
        List<ContractAttachements> list = contractAttachementsService.findContractAttachementsList(contractAttachs);
        return list;
    }

    /**
     * 增加合同明细
     *
     * @param contractDetail
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "addContractDetail", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> addContractDetail(@RequestBody ContractDetail contractDetail)
    {
        Map<String, Object> map = contractService.addContractDetail(contractDetail);
        return map;
    }

    /**
     * 修改合同明细
     *
     * @param contractDetail
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "modContractDetail", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> modContractDetail(@RequestBody ContractDetail contractDetail)
    {
        return contractService.modContractDetail(contractDetail);
    }

    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "detail/list")
    @ResponseBody
    public Page<ContractDetail> detialList(@RequestBody ContractDetail detial, HttpServletRequest request,
        HttpServletResponse response)
    {
        detial.setCompany(UserUtils.getUser().getCompany());
        return contractService.findPage(new Page<ContractDetail>(request, response, -1), detial);
    }

    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "checkcomplaintbycraftno")
    @ResponseBody
    public String checkComplaintByCraftNo(String craftNo)
    {
        return complaintService.getComplaintInfo(craftNo, UserUtils.getUser().getCompany());
    }

    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "checkcomplaintbycustomermodel")
    @ResponseBody
    public String checkComplaintByCustomerModel(String customerModel)
    {
        return complaintService.getComplaintInfoByCustomerModel(customerModel, UserUtils.getUser().getCompany());
    }

    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "selectAccountsReceivableCustomer")
    @ResponseBody
    public Integer selectAccountsReceivableCustomer(@RequestBody Customer customer)
    {

        return contractService.selectAccountsReceivableCustomer(customer);
    }

    /**
     * 检查合同是否可以变更
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "checkChange", method = RequestMethod.POST)
    @ResponseBody
    public String checkChange(@RequestBody Contract contract)
    {
        if (contract != null && !StringUtils.isBlank(contract.getRecordId()))
        {
            if (contractService.isCheckChange(contractService.get(contract)))
            {
                return "{\"result\":true,\"message\":\"合同可以变更\"}";
            }
        }
        return "{\"result\":false,\"message\":\"通知单已入库，合同不能变更\"}";
    }

    /**
     * 根据合同no 和公司编号去查询 所属的明细是否有入库记录
     *
     * @param contract
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "selectProductStore", method = RequestMethod.POST)
    @ResponseBody
    public Integer selectProductStore(@RequestBody Contract contract)
    {
        return contractService.selectProductStore(contract);
    }

    /**
     * 获取合同作废信息
     *
     * @param contract
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "invalidinfo", method = RequestMethod.POST)
    @ResponseBody
    public List<ContractDetail> invalidInfo(@RequestBody Contract contract)
    {
        return contractService.findInvalidInfo(contract);
    }

    /**
     * 对合同明细进行作废
     *
     * @param detail
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "doinvalid", method = RequestMethod.POST)
    @ResponseBody
    public String doInvalid(@RequestBody ContractDetail detail)
    {
        // 获取是否存在工程资料
        Integer checkNum = contractService.checkCardNum(detail);
        if (null != checkNum && checkNum > 0)
        {
            return "checkNumFail";
        }

        // tj 验证是否走审批
        String results = contractService.contractCancelApproval(detail);
        if (!"fail".equals(results))
        {
            return results;
        }else{
            ChangeDataUtils changeDataUtils = new ChangeDataUtils();
            changeDataUtils.computerCustomBill("2","3",detail.getRecordId(),"","");
        }
        String result = contractService.doInvalidContractDetail(detail);
        // 获取需要取消的工程卡
        return result;
    }

    /**
     * 根据客户下单编号去查询所有明细
     *
     * @param recordId
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "customerOrder/{id}", method = {RequestMethod.POST})
    @ResponseBody
    public List<SlCustomerOrderDetail> findcustomerOrderDetailByrecordId(@PathVariable("id") String recordId)
    {
        return slCustomerOrderDetailService.getAllByCustomerOrder(new SlCustomerOrder(recordId));
    }

    /**
     * 获取合同调整信息
     *
     * @param contract
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "adjustinfo", method = RequestMethod.POST)
    @ResponseBody
    public List<ContractDetail> adjustInfo(@RequestBody Contract contract)
    {
        return contractService.findAdjustInfo(contract);
    }

    /**
     * 获取合同调整信息
     *
     * @param contract
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "adjustfeedinfo", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> adjustFeedInfo(@RequestBody Feeding feeding)
    {
        Map<String, Object> objs = Maps.newHashMap();
        // 查询开料信息
        CardA cardA =
            contractService.getCuttingInfo(new CardA(feeding.getProduceBatch().getProcessCardAId().toString()));
        // 查询投料的批次明细信息
        ProduceBatch produceBatch = contractService.getProduceBatchInfo(feeding.getProduceBatch());
        objs.put("cardA", cardA);
        objs.put("produceBatch", produceBatch);
        return objs;
    }

    /**
     * 获取合同调整信息
     *
     * @param contract
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "adjustrepinfo", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> adjustRepInfo(@RequestBody Replenish replenish)
    {
        Map<String, Object> objs = Maps.newHashMap();

        // 查询开料信息
        CardA cardA =
            contractService.getCuttingInfo(new CardA(replenish.getProduceBatchNo().getProcessCardAId().toString()));

        // 查询投料的批次明细信息
        ProduceBatch produceBatch = contractService.getProduceBatchInfo(replenish.getProduceBatchNo());
        objs.put("cardA", cardA);
        objs.put("produceBatch", produceBatch);
        return objs;
    }

    /**
     * 对合同明细进行调整
     *
     * @param detail
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "doadjust", method = RequestMethod.POST)
    @ResponseBody
    public Boolean doAdjust(@RequestBody AdjustContract adjustContract)
    {
        return contractService.doAdjustContractDetail(adjustContract);
    }

    /**
     * 查询客户型号是否存在
     *
     * @param companyId
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "getConCraList", method = RequestMethod.POST)
    @ResponseBody
    public String getConCraList(@RequestBody String custModel)
    {
        List<ContractCraft> list = contractService.getConCraList(custModel);
        if (Collections3.isNotEmpty(list))
        {
            String no = "";
            for (ContractCraft contractCraft : list)
            {
                if (StringUtils.isNotBlank(contractCraft.getNo()))
                {
                    no = contractCraft.getNo();
                    break;
                }
            }
            return "{\"result\":false,\"message\":\"" + no + "\"}";
        }
        else
        {
            return "{\"result\":true,\"message\":\"\"}";
        }
    }

    /**
     * 添加合同时根据客户下单编号去查询所有明细
     *
     * @param recordId
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "getAllByCust/{id}", method = {RequestMethod.POST})
    @ResponseBody
    public List<SlCustomerOrderDetail> getAllByCust(@PathVariable("id") String recordId)
    {
        return slCustomerOrderDetailService.getAllByCust(new SlCustomerOrder(recordId));
    }

    /**
     * 根据工艺id获取工艺的生产编号
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "getNoByCraft/{id}", method = {RequestMethod.POST})
    @ResponseBody
    public String getNoByCraft(@PathVariable("id") String recordId)
    {
        return contractService.getNoByCraft(recordId);
    }

    /**
     * 获取修改已审核的合同的提示语句
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "getEditMessage", method = {RequestMethod.POST})
    @ResponseBody
    public String getEditMessage(@RequestBody String recordId)
    {
        ContractDetail contractDetail = new ContractDetail();
        contractDetail.setRecordId(recordId);
        contractDetail.setCompany(UserUtils.getUser().getCompany());
        // 先根据合同明细编号获取通知单、流程卡
        List<Notification> list = contractDetailService.getNotificationState(contractDetail);
        List<CardA> listCard = contractDetailService.getCardState(contractDetail);
        String cardMessage = null;
        // 根据合同明细编号查询该合同是否有送货单，送货单是否已经确认
        List<DeliveryDetail> listDetail = contractDetailService.getDeliveryState(contractDetail);
        // 根据合同明细获取该该合同是否已经进行成品采购
        List<PrdorderDetail> listPrdOrder = prdorderDetailService.getPrdOrderState(contractDetail);
        if (Collections3.isNotEmpty(listDetail))
        {
            for (int i = 0; i < listDetail.size(); i++)
            {
                if (listDetail.get(i) != null && listDetail.get(i).getStatus() != null && listDetail.get(i)
                    .getStatus()
                    .equals(TypeKey.PU_ORDER_DELIVERY_STATUS_CONFIRMED))
                {
                    // 标识该合同明细已经打印流程卡
                    cardMessage = "delivery";
                    return cardMessage;
                }
            }
        }
        if (Collections3.isNotEmpty(list))
        {
            for (int i = 0; i < list.size(); i++)
            {
                if (list.get(i) != null && list.get(i).getStatus() != null)
                {
                    // 标识该合同明细已经生成通知单
                    cardMessage = "notification";
                    if (Collections3.isNotEmpty(listPrdOrder))
                    {
                        for (int l = 0; l < listPrdOrder.size(); l++)
                        {
                            if (listPrdOrder.get(l) != null)
                            {
                                // 标识该合同明细已经进行成品采购
                                cardMessage = "prdOrder";
                            }
                        }
                    }
                    if (Collections3.isNotEmpty(listCard))
                    {
                        for (int j = 0; j < listCard.size(); j++)
                        {
                            if (listCard.get(j) != null && listCard.get(j).getStatus() != null)
                            {
                                // 标识该合同明细已经打印流程卡
                                cardMessage = "card";
                            }
                        }
                    }
                    // 查询该通知单是否已经投料，投料了就标识该合同明细已投料
                    List<Feeding> listFeed = feedingService.getFeedState(list.get(i));
                    if (Collections3.isNotEmpty(listFeed))
                    {
                        for (int f = 0; f < listFeed.size(); f++)
                        {
                            if (listFeed.get(f) != null)
                            {
                                // 标识该合同明细已经开始投料
                                cardMessage = "feed";
                            }
                        }
                    }
                    return cardMessage;
                }
                else
                {
                    cardMessage = "auditContract";
                }
            }
        }
        else
        {
            cardMessage = "auditContract";
        }
        return cardMessage;
    }

    /**
     * 客户合同由客户重新确认
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "confirmByCustomer", method = RequestMethod.POST)
    @ResponseBody
    public void confirmByCustomer(@RequestBody Contract contract)
    {
        contractService.confirmByCustomer(contract);
    }

    // TODO 测试专用，还原数据 wc 2016-09-29
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "initInHouseData", method = RequestMethod.POST)
    @ResponseBody
    public String initInHouseData()
    {
        contractService.initInHouseData();
        return "重置成功！";
    }

    // TODO 测试专用，重置明细的交期数据 wc 2016-09-29
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "initContrDetaDeliveryDate", method = RequestMethod.POST)
    @ResponseBody
    public String initContrDetaDeliveryDate(@RequestBody String companyId)
    {
        // 1. 重置明细的交期数据
        contractService.initContrDetaDeliveryDate(companyId);
        return "重置成功！";
    }

    // TODO 测试专用，重置合同明细金额和合同金额 wc 2016-10-25
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "initContractAndDetailMoney", method = RequestMethod.POST)
    @ResponseBody
    public String initContractAndDetailMoney(@RequestBody String companyId)
    {
        // 2. 重置合同明细金额和合同金额
        contractService.initContractDetailSubTotal(companyId);
        return "重置成功！";
    }

    // TODO 测试专用，重置工艺的set尺寸 wc 2016-11-29
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "initContractCraftSetSize", method = RequestMethod.POST)
    @ResponseBody
    public String initContractCraftSetSize(@RequestBody String companyId)
    {
        contractService.initContractCraftSetSize(companyId);
        return "重置成功！";
    }

    /**
     * WC 2017-03-15 合同有送过货管理权限可更改是否寄存
     */
    @RequiresPermissions("order:contract:manage")
    @RequestMapping(value = "changeDeposit", method = RequestMethod.POST)
    @ResponseBody
    public String changeDeposit(@RequestBody String recordId)
    {
        return contractService.changeDeposit(recordId);
    }

    /**
     * 修改订单明细的寄存状态
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "editDeposit", method = RequestMethod.POST)
    @ResponseBody
    public void editDeposit(@RequestBody ContractDetail contractDeail)
    {
        contractService.editDeposit(contractDeail);
    }

    // 刷新当前系统时间
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "getSysTime", method = RequestMethod.POST)
    @ResponseBody
    public String getSysTime()
    {
        return DateUtils.formatDateTime(new Date());
    }

    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "getExchangeRate", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> getExchangeRate(@RequestBody Contract contract)
    {
        Map<String, Object> map = new HashMap<>();
        Date orderDate = contract.getOrderDate();
        if (contract.getCurrencyType() != null && orderDate != null)
        {
            orderDate = DateUtils.getMonthFirstDay(orderDate);
            String value = dictValueDao.getDictValueById(contract.getCurrencyType().toString());
            if (StringUtils.isNotBlank(value) && !value.equals("RMB"))
            {
                CrawlingExchangeRateUtil util = CrawlingExchangeRateUtil.getInstance();
                BigDecimal rate = util.getMiddleRadio(value, orderDate);
                map.put("rate", rate);
                map.put("orderDate", DateUtils.formatDate(orderDate));
                map.put("currencyType", value);
            }
        }
        return map;
    }

    // 查询合同明细交货计划变更记录
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "getBatchChangeRecord", method = RequestMethod.POST)
    @ResponseBody
    public List<BatchDeliveryChange> getBatchChangeRecord(@RequestBody String recordId)
    {
        return contractService.getBatchChangeRecord(recordId);
    }

    // 保存变更分批
    @RequiresPermissions("order:contract:manage")
    @RequestMapping(value = "saveChange", method = RequestMethod.POST)
    @ResponseBody
    public void saveChange(@RequestBody ContractDetail detail)
    {
        contractService.saveChange(detail);
    }

    /**
     * 更新绑定工艺id
     *
     * @param detail
     */
    @RequestMapping(value = "updateOlderCraft", method = RequestMethod.POST)
    @ResponseBody
    public String updateOlderCraft(@RequestBody ContractDetail detail)
    {
        contractService.updateOlderCraft(detail);
        return "success";
    }

    /**
     * tj 2018-10-29 保存合同客户订单号
     */
    @RequestMapping(value = "saveCustomerPo", method = RequestMethod.POST)
    @ResponseBody
    public String saveCustomerPo(@RequestBody Contract contract)
    {
        return contractService.saveCustomerPo(contract);
    }

    /**
     * 上传附件并保存合同附件
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "uploadOrderFile")
    @ResponseBody
    public Map<String, String> uploadOrderFile(@RequestParam("file") MultipartFile file, @RequestParam("id") String id,
        @RequestParam("flag") int flag, @RequestParam("no") String no, @RequestParam("companyId") String companyId,
        HttpServletRequest req)
    {
        // 查询该合同明细是否有云平台合同明细id
        Map<String, String> map = new HashMap<>();
        String name = file.getOriginalFilename();
        String strs[] = name.split("\\.");
        // 判断类型是否匹配
        if (strs[1].equals("jpg") || strs[1].equals("JPG") || strs[1].equals("png") || strs[1].equals("PNG")
            || strs[1].equals("jpeg") || strs[1].equals("JPEG") || strs[1].equals("pdf") || strs[1].equals("PDF"))
        {
            String savePath = "";
            ContractAttachMent attach = new ContractAttachMent();
            if (flag == 1)
            {
                String icloudId = contractService.getCloudId(id);
                // 每个合同明细一个对应地址
                if (StringUtils.isNotBlank(icloudId))
                {
                    attach.setErpContractId(null);
                    attach.setContractId(icloudId);
                    savePath = "contractDeail" + "/" + icloudId + "s/";
                }
                else
                {
                    attach.setContractId(null);
                    attach.setErpContractId(id);
                    savePath = "contractDeail" + "/" + id + "s/";
                }
                attach.setOrderFlag("s");
            }
            else
            {
                String icloudId = contractService.gerIcloudId(companyId, no);
                if (StringUtils.isNotBlank(icloudId))
                {
                    attach.setContractId(icloudId);
                    attach.setErpContractId(null);
                }
                else
                {
                    attach.setContractId(null);
                    attach.setErpContractId(id);
                }
                savePath = "contractDeail" + "/" + id + "p/";
                attach.setOrderFlag("p");
            }
            int count =
                contractService.getFile(name, attach.getOrderFlag(), attach.getContractId(), attach.getErpContractId());
            if (count > 0)
            {
                map.put("result", "have");
                return map;
            }
            else
            {
                // 上传文件
                String url = FileManageUtils.uploadFile(file, savePath, req);
                if (url == null)
                {
                    map.put("result", "false");
                    return map;
                }
                // 基本路径
                String sourcePath = savePath + file.getOriginalFilename();
                attach.setOrgFileName(name);
                attach.setFileUrl(sourcePath);
                attach.setRealFileName(file.getName());
                attach.setDownloadUrl(url);
                // 存储路径到数据库
                contractService.uploadFile(attach);
                // 返回合同明细文件列表
                List<ContractAttachMent> list = contractService.getFileList(attach.getContractId(),
                    attach.getOrderFlag(),
                    attach.getErpContractId());
                String listAttach = JSON.toJSONString(list);
                map.put("result", "true");
                map.put("list", listAttach);
                return map;
            }
        }
        else
        {
            map.put("result", "noMatch");
            return map;
        }
    }

    // 删除附件
    @RequestMapping(value = "delAttachments", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, String> delAttachments(@RequestBody Contract contract)
    {
        Map<String, String> map = new HashMap<>();
        ContractAttachMent attach = new ContractAttachMent();
        if (contract.getOrderFlag().equals("1"))
        {
            String icloudId = contractService.getCloudId(contract.getRecordId());
            if (StringUtils.isNotBlank(icloudId))
            {
                attach.setErpContractId(null);
                attach.setContractId(icloudId);
            }
            else
            {
                attach.setContractId(null);
                attach.setErpContractId(contract.getRecordId());
            }
            attach.setOrderFlag("s");
        }
        else
        {
            // 根据公司id和采购编码查询合同号
            attach.setErpContractId(contract.getRecordId());
            String icloudId = contractService.gerIcloudId(contract.getCompanyId(), contract.getNo());
            if (StringUtils.isNotBlank(icloudId))
            {
                attach.setContractId(icloudId);
                attach.setErpContractId(null);
            }
            else
            {
                attach.setContractId(null);
            }
            attach.setOrderFlag("p");
        }
        contractService.delAttachments(contract.getFileId(), attach.getOrderFlag());
        List<ContractAttachMent> list =
            contractService.getFileList(attach.getContractId(), attach.getOrderFlag(), attach.getErpContractId());
        String listAttach = JSON.toJSONString(list);
        map.put("list", listAttach);
        return map;
    }

    // 获取某个合同明细的附件
    @RequestMapping(value = "getAttachments", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, String> getAttachments(@RequestBody Contract contract)
    {
        return contractService.getAttachments(contract);
    }

    /** tj 2018-04-01 提交客户订单号审批 */
    @RequestMapping(value = "poApproval", method = RequestMethod.POST)
    @ResponseBody
    public String poApproval(@RequestBody Contract contract)
    {
        return contractService.poApproval(contract);
    }

    /** tj 2018-04-23 查询当前客户订单号共使用多少次 */
    @RequestMapping(value = "queryCustomerPoNum", method = RequestMethod.POST)
    @ResponseBody
    public Integer queryCustomerPoNum(@RequestBody String customerPo)
    {
        return contractService.queryCustomerPoNum(customerPo);
    }

    /** tj 销售合同结案 */
    @RequestMapping(value = "closingCase")
    @ResponseBody
    public String closingCase(@RequestBody ContractDetail detail)
    {
        return contractService.closingCase(detail);
    }

    /** tj 销售合同结案信息 */
    @RequestMapping(value = "closingCaseInfo")
    @ResponseBody
    public List<ContractDetail> closingCaseInfo(@RequestBody Contract Contract)
    {
        return contractService.closingCaseInfo(Contract);
    }

    @RequestMapping(value = "getClosingCaseDetail")
    @ResponseBody
    public List<ContractDetail> getClosingCaseDetail(@RequestBody String contractIds)
    {
        return contractService.getClosingCaseDetail(contractIds);
    }

    @RequestMapping(value = "batchClosingCase")
    @ResponseBody
    public String batchClosingCaseInfo(@RequestBody List<ContractDetail> contractDetailList)
    {
        return contractService.batchClosingCase(contractDetailList);
    }

    /** zjn 2019-08-15 获取工程卡最新set尺寸和周期 */
    @RequestMapping(value = "getCardSet")
    @ResponseBody
    public ContractCraft getCardSet(@RequestBody ContractCraft contractCraft)
    {
        return contractService.getCardSet(contractCraft);
    }

    @RequestMapping(value = "showDetailAdjustRecords")
    @ResponseBody
    public List<AdjustBalance> showDetailAdjustRecords(@RequestBody ContractDetail contractDetail)
    {
        return contractService.showDetailAdjustRecords(contractDetail);
    }

    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "auditContractPrice", method = RequestMethod.POST)
    @ResponseBody
    public String auditContractPrice(@RequestBody ContractDetail contractDetail)
    {
        String str = contractService.auditContractPrice(contractDetail);
        if (StringUtils.isNotBlank(str))
        {
            return str;
        }
        return "提交审批成功！";
    }

    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "editCustomerPo", method = RequestMethod.POST)
    @ResponseBody
    public String editCustomerPo(@RequestBody Contract contract)
    {
        String str = contractService.editCustomerPo(contract);
        if (StringUtils.isNotBlank(str))
        {
            return str;
        }
        return "提交客户订单号审批成功！";
    }

    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "getEditPriceCause", method = RequestMethod.POST)
    @ResponseBody
    public String getEditPriceCause(@RequestBody ContractDetail contractDetail)
    {
        return contractService.getEditPriceCause(contractDetail);
    }

    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "loadMadeSupplierData", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> loadMadeSupplierData(@RequestBody ContractDetail contractDetail)
    {
        return contractService.loadMadeSupplierData(contractDetail);
    }

    @RequestMapping(value = "uploadPrintFile")
    @ResponseBody
    public String uploadPrintFile(@RequestParam("file") MultipartFile file,
        @RequestParam("contractDeailId") String contractDeailId, HttpServletRequest req)
    {
        ContractDetail contractDetail = contractDetailService.getContractDetailSavePath(contractDeailId);
        String savePath = "PCBCAM/" + contractDetail.getRecordId() + "/" + file.getOriginalFilename();
        if (StringUtils.isNotBlank(contractDetail.getFileUrl()))
        {
            // 删除文件
            FileManageUtils.delFilesNew(contractDetail.getFileUrl());
        }
        // 上传文件
        String url = FileManageUtils.uploadFileNew(file, savePath);
        if (url == null)
        {
            return "false";
        }

        // 基本路径
        String sourcePath = savePath;

        contractDetail.setFileUrl(sourcePath);
        contractDetail.setFileName(file.getOriginalFilename());
        contractDetailService.updateFileUrl(contractDetail);
        return "success";
    }

    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "getCustomerContactList", method = RequestMethod.POST)
    @ResponseBody
    public List<CustomerContact> getCustomerContactList(@RequestBody Customer customer)
    {
        return contractService.getCustomerContactList(customer);
    }

    @RequestMapping(value = "orderStatisticsList", method = RequestMethod.POST)
    @ResponseBody
    public List<OrderStatistics> orderStatisticsList(@RequestBody Contract contract)
    {
        if (contract.getSentTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(contract.getSentTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            contract.setSentTimeEndQr(deliTime.getTime());
        }
        if (contract.getSentTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(contract.getSentTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            contract.setSentTimeStartQr(deliTime.getTime());
        }
        return contractService.orderStatisticsList(contract);
    }

    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "getGroupCenterTaskList", method = RequestMethod.POST)
    @ResponseBody
    public List<ContractDetail> getGroupCenterTaskList(@RequestBody Contract contract)
    {
        if (contract.getSentTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(contract.getSentTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            contract.setSentTimeEndQr(deliTime.getTime());
        }
        if (contract.getSentTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(contract.getSentTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            contract.setSentTimeStartQr(deliTime.getTime());
        }
        return contractService.getGroupCenterTaskList(contract);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "doCancelAudit/{id}", method = {RequestMethod.POST})
    @ResponseBody
    public String doCancelAudit(@PathVariable("id") String id)
    {
        return contractService.doCancelAudit(id);
    }

    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "getCraftRecord", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> getCraftRecord(@RequestBody DictValue dictValue)
    {
        return contractService.getCraftRecord(dictValue);
    }

    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "saveMaxBatchArea", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> saveMaxBatchArea(@RequestBody MaxBatchAreaConfirm maxBatchAreaConfirm)
    {
        return contractService.saveMaxBatchArea(maxBatchAreaConfirm);
    }

    @RequiresPermissions("order:contract:edit")
    @RequestMapping(value = "recallDetail", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> recallDetail(@RequestBody ContractDetail detail)
    {
        return contractService.recallDetail(detail);
    }

    // 查询订单线
    @RequestMapping(value = "getOrderList", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, String> getOrderList(@RequestBody OrderMsg orderMsg)
    {
        return contractService.getOrderList(orderMsg);
    }

    @RequestMapping(value = "checkCusOverdue", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String,Object> checkCusOverdue(@RequestBody Customer customer)
    {
        return contractService.checkCusOverdue(customer);
    }

    /*@RequestMapping(value = "customerOrderList", method = {RequestMethod.POST})
    @ResponseBody
    public Page<CustomerOrder> getCustomerOrderList(CustomerOrder customerOrder, HttpServletRequest request, HttpServletResponse response)
    {
        Page<CustomerOrder> qpage = new Page<>(request, response);
        if (StringUtils.isNotBlank(customerOrder.getPageNo()) && StringUtils.isNotBlank(customerOrder.getPageSize()))
        {
            qpage.setPageNo(Integer.parseInt(customerOrder.getPageNo()));
            qpage.setPageSize(Integer.parseInt(customerOrder.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageSize(Global.PAGE_SIZE);
        }
        Page<CustomerOrder> page = contractService.getCustomerOrderList(qpage, customerOrder);
        return page;
    }*/

    // 亏损下单申请列表
    @RequestMapping(value = "lossOrdersList", method = RequestMethod.POST)
    @ResponseBody
    public List<LossOrders> getLossOrders(LossOrders lossOrders)
    {
        return contractService.getLossOrdersList(lossOrders);
    }

    // 获取客户列表
    @RequestMapping(value = "customerList", method = RequestMethod.POST)
    @ResponseBody
    public List<Customer> getCustomerLossOrdersList(Customer customer)
    {
        return contractService.getCustomerLossOrdersList(customer);
    }

    // 保存亏损客户
    @RequestMapping(value = "insertLossOrdersList", method = RequestMethod.POST)
    @ResponseBody
    public String insertLossOrdersList(@RequestBody LossOrders lossOrders)
    {
        return contractService.insertLossOrdersList(lossOrders);
    }

    //线上下单清单
    @RequestMapping(value = "onlineOrderList", method = RequestMethod.POST)
    @ResponseBody
    public List<InterProduct>onlineOrderList(@RequestBody InterProduct interProduct,HttpServletRequest request,
        HttpServletResponse response){
        List<InterProduct> list = Lists.newArrayList();
        // 香港公司才能查看单
        Company company = UserUtils.getUser().getCompany();
        interProduct.setStatus("4");// 默认查已付款的
        interProduct.setManageFlag(1);
        interProduct.setCompanyId(company.getRecordId());
        Page<InterProduct> qpage = new Page<InterProduct>(request, response,-1);
        qpage = productService.getInterProductPage(qpage,interProduct);
        list = qpage.getList();
        return list;
    }

    @RequestMapping(value = "conversionOrder", method = RequestMethod.POST)
    @ResponseBody
    public Contract conversionOrder(@RequestBody InterProduct interProduct)
    {
        return contractService.conversionOrder(interProduct);
    }
    @RequestMapping(value = "getProductTypeList", method = RequestMethod.POST)
    @ResponseBody
    public List<DictValue> getProductTypeList()
    {
        ChangeDataUtils changeDataUtils = new ChangeDataUtils();
        return changeDataUtils.getProductTypeList();
    }
}