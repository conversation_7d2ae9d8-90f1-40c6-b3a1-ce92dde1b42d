package com.kyb.pcberp.modules.crm.dao;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.crm.entity.QuotationPrePrice;
import com.kyb.pcberp.modules.crm.entity.QuotationPrePriceResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface QuotationPrePriceDao
{
    QuotationPrePrice get(QuotationPrePrice prePrice);

    List<QuotationPrePrice> findList(QuotationPrePrice prePrice);

    void insert(QuotationPrePrice prePrice);

    void update(QuotationPrePrice prePrice);

    void delete(QuotationPrePrice prePrice);

    Integer checkCount(QuotationPrePrice prePrice);

    void deletePrePriceResult(QuotationPrePrice prePrice);

    void batchInsertPrePriceResult(@Param("list") List<QuotationPrePriceResult> list);

    List<QuotationPrePriceResult> loadDetailSetUpList(@Param("prePriceId") String prePriceId);
}
