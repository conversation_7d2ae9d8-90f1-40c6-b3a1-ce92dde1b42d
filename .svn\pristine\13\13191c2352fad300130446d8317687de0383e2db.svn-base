package com.kyb.pcberp.modules.stock.entity;

import java.math.BigDecimal;

import com.kyb.pcberp.common.persistence.DataEntity;

public class WipCopyInfo extends DataEntity<WipCopyInfo>
{
    
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    
    private Integer copyDetailId;
    
    private Integer notificationId;
    
    private String feedId;
    
    private Integer orderQty;
    
    private Integer feedQty;
    
    private Integer qtyPnlA;
    
    private Integer qtyPnlB;
    
    private Integer snapQtyPnlA;
    
    private Integer snapQtyPnlB;
    
    private String feedArea;
    
    private String diffExplain;
    
    private String status;
    
    private WipCopy wipCopy;
    
    private String notificationNo;
    
    private Integer batchDetailId;
    
    private String batchDetailNo;
    
    private Integer discardPcsQtyA;
    
    private Integer discardPcsQtyB;
    
    private String craftNo;
    
    private String handleOrderNo;
    
    private Integer handleType;
    
    private Integer detailCraftId;
    
    private BigDecimal setLength;
    
    private BigDecimal setWidth;
    
    private Integer qtySetT;
    
    private String cateName;
    
    private Integer discardPcsQty;
    
    private Integer qtySetA;
    
    private Integer qtySetB;
    
    private BigDecimal pcsArea;
    
    private String feedNo;
    
    private String batchShowNo;
    
    private String batchDetailShowNo;
    
    private String produceType;
    
    private BigDecimal pnlArea;
    
    private Integer unitType;
    
    private Integer aPnlHasPcsQty;
    
    private Integer bPnlHasPcsQty;
    
    private Integer qtyPcsA;
    
    private Integer qtyPcsB;
    
    private Integer qtyA;
    
    private Integer qtyB;
    
    private String cateId;
    
    private String unitName;
    
    public void setUnitName(String unitName)
    {
        this.unitName = unitName;
    }

    public Integer getCopyDetailId()
    {
        return copyDetailId;
    }
    
    public void setCopyDetailId(Integer copyDetailId)
    {
        this.copyDetailId = copyDetailId;
    }
    
    public Integer getNotificationId()
    {
        return notificationId;
    }
    
    public void setNotificationId(Integer notificationId)
    {
        this.notificationId = notificationId;
    }
    
    public String getFeedId()
    {
        return feedId;
    }
    
    public void setFeedId(String feedId)
    {
        this.feedId = feedId;
    }
    
    public Integer getOrderQty()
    {
        return orderQty;
    }
    
    public void setOrderQty(Integer orderQty)
    {
        this.orderQty = orderQty;
    }
    
    public Integer getFeedQty()
    {
        return feedQty;
    }
    
    public void setFeedQty(Integer feedQty)
    {
        this.feedQty = feedQty;
    }
    
    public Integer getQtyPnlA()
    {
        return qtyPnlA;
    }
    
    public void setQtyPnlA(Integer qtyPnlA)
    {
        this.qtyPnlA = qtyPnlA;
    }
    
    public Integer getQtyPnlB()
    {
        return qtyPnlB;
    }
    
    public void setQtyPnlB(Integer qtyPnlB)
    {
        this.qtyPnlB = qtyPnlB;
    }
    
    public Integer getSnapQtyPnlA()
    {
        return snapQtyPnlA;
    }
    
    public void setSnapQtyPnlA(Integer snapQtyPnlA)
    {
        this.snapQtyPnlA = snapQtyPnlA;
    }
    
    public Integer getSnapQtyPnlB()
    {
        return snapQtyPnlB;
    }
    
    public void setSnapQtyPnlB(Integer snapQtyPnlB)
    {
        this.snapQtyPnlB = snapQtyPnlB;
    }
    
    public String getFeedArea()
    {
        return feedArea;
    }
    
    public void setFeedArea(String feedArea)
    {
        this.feedArea = feedArea;
    }
    
    public String getDiffExplain()
    {
        return diffExplain;
    }
    
    public void setDiffExplain(String diffExplain)
    {
        this.diffExplain = diffExplain;
    }
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public WipCopy getWipCopy()
    {
        return wipCopy;
    }
    
    public void setWipCopy(WipCopy wipCopy)
    {
        this.wipCopy = wipCopy;
    }
    
    public String getNotificationNo()
    {
        return notificationNo;
    }
    
    public void setNotificationNo(String notificationNo)
    {
        this.notificationNo = notificationNo;
    }
    
    public Integer getBatchDetailId()
    {
        return batchDetailId;
    }
    
    public void setBatchDetailId(Integer batchDetailId)
    {
        this.batchDetailId = batchDetailId;
    }
    
    public String getBatchDetailNo()
    {
        return batchDetailNo;
    }
    
    public void setBatchDetailNo(String batchDetailNo)
    {
        this.batchDetailNo = batchDetailNo;
    }
    
    public Integer getDiscardPcsQtyA()
    {
        return discardPcsQtyA;
    }
    
    public void setDiscardPcsQtyA(Integer discardPcsQtyA)
    {
        this.discardPcsQtyA = discardPcsQtyA;
    }
    
    public Integer getDiscardPcsQtyB()
    {
        return discardPcsQtyB;
    }
    
    public void setDiscardPcsQtyB(Integer discardPcsQtyB)
    {
        this.discardPcsQtyB = discardPcsQtyB;
    }
    
    public String getCraftNo()
    {
        return craftNo;
    }
    
    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }
    
    public String getHandleOrderNo()
    {
        return handleOrderNo;
    }
    
    public void setHandleOrderNo(String handleOrderNo)
    {
        this.handleOrderNo = handleOrderNo;
    }
    
    public Integer getHandleType()
    {
        return handleType;
    }
    
    public void setHandleType(Integer handleType)
    {
        this.handleType = handleType;
    }
    
    public Integer getDetailCraftId()
    {
        return detailCraftId;
    }
    
    public void setDetailCraftId(Integer detailCraftId)
    {
        this.detailCraftId = detailCraftId;
    }
    
    public BigDecimal getSetLength()
    {
        return setLength;
    }
    
    public void setSetLength(BigDecimal setLength)
    {
        this.setLength = setLength;
    }
    
    public BigDecimal getSetWidth()
    {
        return setWidth;
    }
    
    public void setSetWidth(BigDecimal setWidth)
    {
        this.setWidth = setWidth;
    }
    
    public Integer getQtySetT()
    {
        return qtySetT;
    }
    
    public void setQtySetT(Integer qtySetT)
    {
        this.qtySetT = qtySetT;
    }
    
    public String getCateName()
    {
        return cateName;
    }
    
    public void setCateName(String cateName)
    {
        this.cateName = cateName;
    }
    
    public Integer getDiscardPcsQty()
    {
        return discardPcsQty;
    }
    
    public void setDiscardPcsQty(Integer discardPcsQty)
    {
        this.discardPcsQty = discardPcsQty;
    }
    
    public Integer getQtySetA()
    {
        return qtySetA;
    }
    
    public void setQtySetA(Integer qtySetA)
    {
        this.qtySetA = qtySetA;
    }
    
    public Integer getQtySetB()
    {
        return qtySetB;
    }
    
    public void setQtySetB(Integer qtySetB)
    {
        this.qtySetB = qtySetB;
    }
    
    public BigDecimal getPcsArea()
    {
        return pcsArea;
    }
    
    public void setPcsArea(BigDecimal pcsArea)
    {
        this.pcsArea = pcsArea;
    }
    
    public String getFeedNo()
    {
        return feedNo;
    }
    
    public void setFeedNo(String feedNo)
    {
        this.feedNo = feedNo;
    }
    
    public String getBatchShowNo()
    {
        return batchShowNo;
    }
    
    public void setBatchShowNo(String batchShowNo)
    {
        this.batchShowNo = batchShowNo;
    }
    
    public String getBatchDetailShowNo()
    {
        return batchDetailShowNo;
    }
    
    public void setBatchDetailShowNo(String batchDetailShowNo)
    {
        this.batchDetailShowNo = batchDetailShowNo;
    }
    
    public String getProduceType()
    {
        return produceType;
    }
    
    public void setProduceType(String produceType)
    {
        this.produceType = produceType;
    }
    
    public BigDecimal getPnlArea()
    {
        return pnlArea;
    }
    
    public void setPnlArea(BigDecimal pnlArea)
    {
        this.pnlArea = pnlArea;
    }
    
    public Integer getUnitType()
    {
        return unitType;
    }
    
    public void setUnitType(Integer unitType)
    {
        this.unitType = unitType;
    }
    
    public Integer getaPnlHasPcsQty()
    {
        return aPnlHasPcsQty;
    }
    
    public void setaPnlHasPcsQty(Integer aPnlHasPcsQty)
    {
        this.aPnlHasPcsQty = aPnlHasPcsQty;
    }
    
    public Integer getbPnlHasPcsQty()
    {
        return bPnlHasPcsQty;
    }
    
    public void setbPnlHasPcsQty(Integer bPnlHasPcsQty)
    {
        this.bPnlHasPcsQty = bPnlHasPcsQty;
    }
    
    public Integer getQtyPcsA()
    {
        return qtyPcsA;
    }
    
    public void setQtyPcsA(Integer qtyPcsA)
    {
        this.qtyPcsA = qtyPcsA;
    }
    
    public Integer getQtyPcsB()
    {
        return qtyPcsB;
    }
    
    public void setQtyPcsB(Integer qtyPcsB)
    {
        this.qtyPcsB = qtyPcsB;
    }
    
    public Integer getQtyA()
    {
        if (this.qtyA != null)
        {
            return this.qtyA;
        }
        if (getUnitType() != null && 1 == getUnitType())
        {
            return this.getQtyPcsA();
        }
        else
        {
            return this.getQtyPnlA();
        }
    }
    
    public void setQtyA(Integer qtyA)
    {
        this.qtyA = qtyA;
    }
    
    public Integer getQtyB()
    {
        if (this.qtyB != null)
        {
            return this.qtyB;
        }
        if (getUnitType() != null && 1 == getUnitType())
        {
            return this.getQtyPcsB();
        }
        else
        {
            return this.getQtyPnlB();
        }
    }
    
    public void setQtyB(Integer qtyB)
    {
        this.qtyB = qtyB;
    }
    
    public String getUnitName()
    {
        if(this.unitName!=null)
        {
            return unitName;
        }
        if (null == getUnitType() || 2 == getUnitType())
        {
            return "PNL";
        }
        else
        {
            return "PCS";
        }
        
    }
    
    public String getCateId()
    {
        return cateId;
    }

    public void setCateId(String cateId)
    {
        this.cateId = cateId;
    }
    
    
    
}
