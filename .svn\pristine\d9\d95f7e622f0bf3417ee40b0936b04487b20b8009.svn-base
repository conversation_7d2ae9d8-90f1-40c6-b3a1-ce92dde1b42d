package com.kyb.pcberp.modules.stock.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.sys.entity.Company;

/**
 * zjn 2018-07-19 
 * 共享物料实体
 */
@SuppressWarnings("serial")
public class MaterialInternet extends DataEntity<MaterialInternet> {

	/** 共享公司id*/
	private Company shareCompany;
	
	/** 共享公司id组*/
	private String shareCompanyIds;
	
	/** 我的仓库*/
	private StoreHouse storeHouse;
	
	/** 我的仓库id组*/
	private String storeHouseIds;
	
	/** 物料*/
	private Material material;
	
	/** 设置金额*/
	private BigDecimal istallAmount;
	
	/** 百分率金额*/
	private BigDecimal percentageAmount;
	
	private List<MaterialInternetQuery> queryList = new ArrayList<>();
	
	private String erpUserId;// 分配用户id
	
	public Company getShareCompany() {
		return shareCompany;
	}

	public void setShareCompany(Company shareCompany) {
		this.shareCompany = shareCompany;
	}

	public String getShareCompanyIds() {
		return shareCompanyIds;
	}

	public void setShareCompanyIds(String shareCompanyIds) {
		this.shareCompanyIds = shareCompanyIds;
	}

	public StoreHouse getStoreHouse() {
		return storeHouse;
	}

	public void setStoreHouse(StoreHouse storeHouse) {
		this.storeHouse = storeHouse;
	}

	public String getStoreHouseIds() {
		return storeHouseIds;
	}

	public void setStoreHouseIds(String storeHouseIds) {
		this.storeHouseIds = storeHouseIds;
	}

	public Material getMaterial() {
		return material;
	}

	public void setMaterial(Material material) {
		this.material = material;
	}

	public BigDecimal getIstallAmount() {
		return istallAmount;
	}

	public void setIstallAmount(BigDecimal istallAmount) {
		this.istallAmount = istallAmount;
	}

	public BigDecimal getPercentageAmount() {
		return percentageAmount;
	}

	public void setPercentageAmount(BigDecimal percentageAmount) {
		this.percentageAmount = percentageAmount;
	}
	
	// 实体深拷贝
    public MaterialInternet clone()
    {
    	MaterialInternet mi = null;
        try
        {
        	mi = (MaterialInternet)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            e.printStackTrace();
        } 
        return mi;
    }

	public List<MaterialInternetQuery> getQueryList() {
		return queryList;
	}

	public void setQueryList(List<MaterialInternetQuery> queryList) {
		this.queryList = queryList;
	}

    public String getErpUserId()
    {
        return erpUserId;
    }

    public void setErpUserId(String erpUserId)
    {
        this.erpUserId = erpUserId;
    }

}
