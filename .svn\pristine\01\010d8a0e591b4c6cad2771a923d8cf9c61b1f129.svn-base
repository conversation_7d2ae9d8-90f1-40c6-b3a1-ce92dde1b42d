/**
 * 
 */
package com.kyb.pcberp.modules.crm.entity;

import java.math.BigDecimal;

import com.kyb.pcberp.common.persistence.DataEntity;

/**
 * cqEntity
 * 
 * <AUTHOR>
 * @version 2016-08-12
 */
public class SlCustomerOrderDetail extends DataEntity<SlCustomerOrderDetail>
{
    
    private static final long serialVersionUID = 1L;
    
    private SlCustomerOrder customerorderId; // customerorderid
    
    private Customer customerId; // customerid
    
    private String customerModel; // 客户型号
    
    private Integer sampleAmt; // 样品数量
    
    private Long deliveryPrdDays; // 量产交期
    
    private Long deliverySmpDays; // 交样时限，以天为计数半单位
    
    private BigDecimal price; // 产品生产单价
    
    private BigDecimal engineeringFee; // 工程费
    
    private BigDecimal sampleFee; // 样板费
    
    private BigDecimal testShelfFee; // 测试架费
    
    private BigDecimal mouldFee; // 模具费
    
    private BigDecimal orderLowLimit; // 每次订货时的最低限额
    
    private BigDecimal engineeringLimit; // 返还样板费最低限额、工程费返还费
    
    private BigDecimal mouldLimit; // 返还模具费最低限额
    
    private BigDecimal testShelfLimit; // 返还测试架费最低限额
    
    private BigDecimal unitWidth; // PCS宽度，单位mm
    
    private BigDecimal unitLength; // PCS长度，单位mm
    
    private BigDecimal pnlWidth; // PNL宽度，单位mm
    
    private BigDecimal pnlLength; // PNL长度，单位mm
    
    private Integer pnlDivisor; // PNL拼接数,指一个PNL中含有多少个PCS
    
    private Long materialType; // 材料类别、覆铜板材
    
    private Long boardLevel; // pcb类型
    
    private Long boardThickness; // 板厚
    
    private Long surfaceProcess; // 表面加工工艺，镀层处理
    
    private Long copperCladThickness; // 覆铜厚度，覆铜要求
    
    private Long solderMaskType; // 阻焊类型
    
    private Long characterType; // 字符印刷类型，板面字符
    
    private Long shapingWay; // 成型方式
    
    private Long testMethod; // 产品出厂检测方法，测试要求
    
    private Long lingeSpacing;// 最小线宽/线距
    
    private Long smallAperture;// 最小孔径
    
    private Long halAhole;// 半孔
    
    private Long buryBlindHole;// 埋盲孔
    
    private Long resistance;// 抗阻
    
    private Long deliveryUrgent;// 发货加急
    
    private Long daore; // 导热
    
    private Long naiya; // 耐压
    
    private Long pliesnumber;// 层数
    
    private Long productionType;// 生产类型
    
    private String oldprice; // 原先产品生产单价
    
    private String priceCause;// 改价格原因
    
    private String totalAmt;// 税后总金额
    
    private String pcsSizeCopy;// 合同的pcs尺寸
    
    private String deliverSizeCopy;// 合同的交货尺寸
    
    private String customerModelCopy;// 合同的客户型号
    
    private Integer sampleAmtCopy;// 合同的数量
    
    private Long boardLevelCopy;// 合同的pcb类型
    
    private Long materialTypeCopy;// 合同的覆铜板材
    
    private Long boardThicknessCopy;// 合同的板厚
    
    private Long copperCladThicknessCopy;// 合同的覆铜要求
    
    private Long surfaceProcessCopy;// 合同的镀层处理
    
    private Long solderMaskTypeCopy;// 合同的阻焊类型
    
    private Long characterTypeCopy;// 合同的板面字符
    
    private Long shapingWayCopy;// 合同的成型方式
    
    private Long testMethodCopy;// 合同的测试要求
    
    private Long lingeSpacingCopy;// 合同的最小线宽/线距
    
    private Long smallApertureCopy;// 合同的最小孔径
    
    private Long halAholeCopy;// 合同的半孔
    
    private Long buryBlindHoleCopy;// 合同的埋盲孔
    
    private Long resistanceCopy;// 合同的抗阻
    
    private Long deliveryUrgentCopy;// 合同的发货加急
    
    private BigDecimal priceCopy;// 合同的产品生产单价
    
    private BigDecimal engineeringFeeCopy;// 合同的工程费
    
    private String totalAmtCopy;// 总金额
    
    private String pcsSize;
    
    private String deliverSize;
    
    private Long deliveryDays; // 交货期限
    
    private Long deliveryDaysCopy; // 合同的交货期限
    
    private String totalAmtBefore;// 税前金额
    
    private String subTotalCopy; // 合同的总金额
    
    private BigDecimal unitWidthCopy; // 合同的PCS宽度
    
    private BigDecimal unitLengthCopy; // 合同的PCS长度
    
    private BigDecimal pnlWidthCopy; // 合同的PNL宽度
    
    private BigDecimal pnlLengthCopy; // 合同的PNL长度
    
    private BigDecimal pnlDivisorCopy; // 合同的PNL拼接数
    
    private String deailArea;// 订单明细总面积
    
    private String deailAreaCopy;// 合同明细总面积
    
    private Long daoreCopy; // 导热
    
    private Long naiyaCopy; // 耐压
    
    private Long pliesnumberCopy;// 层数
    
    public Long getDaoreCopy()
    {
        return daoreCopy;
    }

    public void setDaoreCopy(Long daoreCopy)
    {
        this.daoreCopy = daoreCopy;
    }

    public Long getNaiyaCopy()
    {
        return naiyaCopy;
    }

    public void setNaiyaCopy(Long naiyaCopy)
    {
        this.naiyaCopy = naiyaCopy;
    }

    public Long getPliesnumberCopy()
    {
        return pliesnumberCopy;
    }

    public void setPliesnumberCopy(Long pliesnumberCopy)
    {
        this.pliesnumberCopy = pliesnumberCopy;
    }

    public SlCustomerOrderDetail()
    {
        super();
    }
    
    public SlCustomerOrderDetail(String id)
    {
        super(id);
    }
    
    public SlCustomerOrder getCustomerorderId()
    {
        return customerorderId;
    }
    
    public void setCustomerorderId(SlCustomerOrder customerorderId)
    {
        this.customerorderId = customerorderId;
    }
    
    public Customer getCustomerId()
    {
        return customerId;
    }
    
    public void setCustomerId(Customer customerId)
    {
        this.customerId = customerId;
    }
    
    public String getCustomerModel()
    {
        return customerModel;
    }
    
    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }
    
    public Integer getSampleAmt()
    {
        return sampleAmt;
    }
    
    public void setSampleAmt(Integer sampleAmt)
    {
        this.sampleAmt = sampleAmt;
    }
    
    public Long getDeliveryPrdDays()
    {
        return deliveryPrdDays;
    }
    
    public void setDeliveryPrdDays(Long deliveryPrdDays)
    {
        this.deliveryPrdDays = deliveryPrdDays;
    }
    
    public Long getDeliverySmpDays()
    {
        return deliverySmpDays;
    }
    
    public void setDeliverySmpDays(Long deliverySmpDays)
    {
        this.deliverySmpDays = deliverySmpDays;
    }
    
    public BigDecimal getPrice()
    {
        return price;
    }
    
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }
    
    public BigDecimal getEngineeringFee()
    {
        return engineeringFee;
    }
    
    public void setEngineeringFee(BigDecimal engineeringFee)
    {
        this.engineeringFee = engineeringFee;
    }
    
    public BigDecimal getSampleFee()
    {
        return sampleFee;
    }
    
    public void setSampleFee(BigDecimal sampleFee)
    {
        this.sampleFee = sampleFee;
    }
    
    public BigDecimal getTestShelfFee()
    {
        return testShelfFee;
    }
    
    public void setTestShelfFee(BigDecimal testShelfFee)
    {
        this.testShelfFee = testShelfFee;
    }
    
    public BigDecimal getMouldFee()
    {
        return mouldFee;
    }
    
    public void setMouldFee(BigDecimal mouldFee)
    {
        this.mouldFee = mouldFee;
    }
    
    public BigDecimal getOrderLowLimit()
    {
        return orderLowLimit;
    }
    
    public void setOrderLowLimit(BigDecimal orderLowLimit)
    {
        this.orderLowLimit = orderLowLimit;
    }
    
    public BigDecimal getEngineeringLimit()
    {
        return engineeringLimit;
    }
    
    public void setEngineeringLimit(BigDecimal engineeringLimit)
    {
        this.engineeringLimit = engineeringLimit;
    }
    
    public BigDecimal getMouldLimit()
    {
        return mouldLimit;
    }
    
    public void setMouldLimit(BigDecimal mouldLimit)
    {
        this.mouldLimit = mouldLimit;
    }
    
    public BigDecimal getTestShelfLimit()
    {
        return testShelfLimit;
    }
    
    public void setTestShelfLimit(BigDecimal testShelfLimit)
    {
        this.testShelfLimit = testShelfLimit;
    }
    
    public BigDecimal getUnitWidth()
    {
        return unitWidth;
    }
    
    public void setUnitWidth(BigDecimal unitWidth)
    {
        this.unitWidth = unitWidth;
    }
    
    public BigDecimal getUnitLength()
    {
        return unitLength;
    }
    
    public void setUnitLength(BigDecimal unitLength)
    {
        this.unitLength = unitLength;
    }
    
    public BigDecimal getPnlWidth()
    {
        return pnlWidth;
    }
    
    public void setPnlWidth(BigDecimal pnlWidth)
    {
        this.pnlWidth = pnlWidth;
    }
    
    public BigDecimal getPnlLength()
    {
        return pnlLength;
    }
    
    public void setPnlLength(BigDecimal pnlLength)
    {
        this.pnlLength = pnlLength;
    }
    
    public Integer getPnlDivisor()
    {
        return pnlDivisor;
    }
    
    public void setPnlDivisor(Integer pnlDivisor)
    {
        this.pnlDivisor = pnlDivisor;
    }
    
    public Long getMaterialType()
    {
        return materialType;
    }
    
    public void setMaterialType(Long materialType)
    {
        this.materialType = materialType;
    }
    
    public Long getBoardLevel()
    {
        return boardLevel;
    }
    
    public void setBoardLevel(Long boardLevel)
    {
        this.boardLevel = boardLevel;
    }
    
    public Long getBoardThickness()
    {
        return boardThickness;
    }
    
    public void setBoardThickness(Long boardThickness)
    {
        this.boardThickness = boardThickness;
    }
    
    public Long getSurfaceProcess()
    {
        return surfaceProcess;
    }
    
    public void setSurfaceProcess(Long surfaceProcess)
    {
        this.surfaceProcess = surfaceProcess;
    }
    
    public Long getCopperCladThickness()
    {
        return copperCladThickness;
    }
    
    public void setCopperCladThickness(Long copperCladThickness)
    {
        this.copperCladThickness = copperCladThickness;
    }
    
    public Long getSolderMaskType()
    {
        return solderMaskType;
    }
    
    public void setSolderMaskType(Long solderMaskType)
    {
        this.solderMaskType = solderMaskType;
    }
    
    public Long getCharacterType()
    {
        return characterType;
    }
    
    public void setCharacterType(Long characterType)
    {
        this.characterType = characterType;
    }
    
    public Long getShapingWay()
    {
        return shapingWay;
    }
    
    public void setShapingWay(Long shapingWay)
    {
        this.shapingWay = shapingWay;
    }
    
    public Long getTestMethod()
    {
        return testMethod;
    }
    
    public void setTestMethod(Long testMethod)
    {
        this.testMethod = testMethod;
    }
    
    public Long getLingeSpacing()
    {
        return lingeSpacing;
    }
    
    public Long getSmallAperture()
    {
        return smallAperture;
    }
    
    public Long getHalAhole()
    {
        return halAhole;
    }
    
    public Long getBuryBlindHole()
    {
        return buryBlindHole;
    }
    
    public Long getResistance()
    {
        return resistance;
    }
    
    public Long getDeliveryUrgent()
    {
        return deliveryUrgent;
    }
    
    public Long getProductionType()
    {
        return productionType;
    }
    
    public String getOldprice()
    {
        return oldprice;
    }
    
    public String getPriceCause()
    {
        return priceCause;
    }
    
    public String getTotalAmt()
    {
        return totalAmt;
    }
    
    public void setLingeSpacing(Long lingeSpacing)
    {
        this.lingeSpacing = lingeSpacing;
    }
    
    public void setSmallAperture(Long smallAperture)
    {
        this.smallAperture = smallAperture;
    }
    
    public void setHalAhole(Long halAhole)
    {
        this.halAhole = halAhole;
    }
    
    public void setBuryBlindHole(Long buryBlindHole)
    {
        this.buryBlindHole = buryBlindHole;
    }
    
    public void setResistance(Long resistance)
    {
        this.resistance = resistance;
    }
    
    public void setDeliveryUrgent(Long deliveryUrgent)
    {
        this.deliveryUrgent = deliveryUrgent;
    }
    
    public void setProductionType(Long productionType)
    {
        this.productionType = productionType;
    }
    
    public void setOldprice(String oldprice)
    {
        this.oldprice = oldprice;
    }
    
    public void setPriceCause(String priceCause)
    {
        this.priceCause = priceCause;
    }
    
    public void setTotalAmt(String totalAmt)
    {
        this.totalAmt = totalAmt;
    }
    
    public String getPcsSizeCopy()
    {
        return pcsSizeCopy;
    }
    
    public String getDeliverSizeCopy()
    {
        return deliverSizeCopy;
    }
    
    public String getCustomerModelCopy()
    {
        return customerModelCopy;
    }
    
    public Integer getSampleAmtCopy()
    {
        return sampleAmtCopy;
    }
    
    public Long getBoardLevelCopy()
    {
        return boardLevelCopy;
    }
    
    public Long getMaterialTypeCopy()
    {
        return materialTypeCopy;
    }
    
    public Long getBoardThicknessCopy()
    {
        return boardThicknessCopy;
    }
    
    public Long getCopperCladThicknessCopy()
    {
        return copperCladThicknessCopy;
    }
    
    public Long getSurfaceProcessCopy()
    {
        return surfaceProcessCopy;
    }
    
    public Long getSolderMaskTypeCopy()
    {
        return solderMaskTypeCopy;
    }
    
    public Long getCharacterTypeCopy()
    {
        return characterTypeCopy;
    }
    
    public Long getShapingWayCopy()
    {
        return shapingWayCopy;
    }
    
    public Long getTestMethodCopy()
    {
        return testMethodCopy;
    }
    
    public Long getLingeSpacingCopy()
    {
        return lingeSpacingCopy;
    }
    
    public Long getSmallApertureCopy()
    {
        return smallApertureCopy;
    }
    
    public Long getHalAholeCopy()
    {
        return halAholeCopy;
    }
    
    public Long getBuryBlindHoleCopy()
    {
        return buryBlindHoleCopy;
    }
    
    public Long getResistanceCopy()
    {
        return resistanceCopy;
    }
    
    public Long getDeliveryUrgentCopy()
    {
        return deliveryUrgentCopy;
    }
    
    public BigDecimal getPriceCopy()
    {
        return priceCopy;
    }
    
    public BigDecimal getEngineeringFeeCopy()
    {
        return engineeringFeeCopy;
    }
    
    public void setPcsSizeCopy(String pcsSizeCopy)
    {
        this.pcsSizeCopy = pcsSizeCopy;
    }
    
    public void setDeliverSizeCopy(String deliverSizeCopy)
    {
        this.deliverSizeCopy = deliverSizeCopy;
    }
    
    public void setCustomerModelCopy(String customerModelCopy)
    {
        this.customerModelCopy = customerModelCopy;
    }
    
    public void setSampleAmtCopy(Integer sampleAmtCopy)
    {
        this.sampleAmtCopy = sampleAmtCopy;
    }
    
    public void setBoardLevelCopy(Long boardLevelCopy)
    {
        this.boardLevelCopy = boardLevelCopy;
    }
    
    public void setMaterialTypeCopy(Long materialTypeCopy)
    {
        this.materialTypeCopy = materialTypeCopy;
    }
    
    public void setBoardThicknessCopy(Long boardThicknessCopy)
    {
        this.boardThicknessCopy = boardThicknessCopy;
    }
    
    public void setCopperCladThicknessCopy(Long copperCladThicknessCopy)
    {
        this.copperCladThicknessCopy = copperCladThicknessCopy;
    }
    
    public void setSurfaceProcessCopy(Long surfaceProcessCopy)
    {
        this.surfaceProcessCopy = surfaceProcessCopy;
    }
    
    public void setSolderMaskTypeCopy(Long solderMaskTypeCopy)
    {
        this.solderMaskTypeCopy = solderMaskTypeCopy;
    }
    
    public void setCharacterTypeCopy(Long characterTypeCopy)
    {
        this.characterTypeCopy = characterTypeCopy;
    }
    
    public void setShapingWayCopy(Long shapingWayCopy)
    {
        this.shapingWayCopy = shapingWayCopy;
    }
    
    public void setTestMethodCopy(Long testMethodCopy)
    {
        this.testMethodCopy = testMethodCopy;
    }
    
    public void setLingeSpacingCopy(Long lingeSpacingCopy)
    {
        this.lingeSpacingCopy = lingeSpacingCopy;
    }
    
    public void setSmallApertureCopy(Long smallApertureCopy)
    {
        this.smallApertureCopy = smallApertureCopy;
    }
    
    public void setHalAholeCopy(Long halAholeCopy)
    {
        this.halAholeCopy = halAholeCopy;
    }
    
    public void setBuryBlindHoleCopy(Long buryBlindHoleCopy)
    {
        this.buryBlindHoleCopy = buryBlindHoleCopy;
    }
    
    public void setResistanceCopy(Long resistanceCopy)
    {
        this.resistanceCopy = resistanceCopy;
    }
    
    public void setDeliveryUrgentCopy(Long deliveryUrgentCopy)
    {
        this.deliveryUrgentCopy = deliveryUrgentCopy;
    }
    
    public void setPriceCopy(BigDecimal priceCopy)
    {
        this.priceCopy = priceCopy;
    }
    
    public void setEngineeringFeeCopy(BigDecimal engineeringFeeCopy)
    {
        this.engineeringFeeCopy = engineeringFeeCopy;
    }
    
    public String getTotalAmtCopy()
    {
        return totalAmtCopy;
    }
    
    public void setTotalAmtCopy(String totalAmtCopy)
    {
        this.totalAmtCopy = totalAmtCopy;
    }
    
    public String getPcsSize()
    {
        return pcsSize;
    }
    
    public String getDeliverSize()
    {
        return deliverSize;
    }
    
    public void setPcsSize(String pcsSize)
    {
        this.pcsSize = pcsSize;
    }
    
    public void setDeliverSize(String deliverSize)
    {
        this.deliverSize = deliverSize;
    }
    
    public Long getDeliveryDays()
    {
        return deliveryDays;
    }
    
    public void setDeliveryDays(Long deliveryDays)
    {
        this.deliveryDays = deliveryDays;
    }
    
    public Long getDeliveryDaysCopy()
    {
        return deliveryDaysCopy;
    }
    
    public void setDeliveryDaysCopy(Long deliveryDaysCopy)
    {
        this.deliveryDaysCopy = deliveryDaysCopy;
    }
    
    public String getTotalAmtBefore()
    {
        return totalAmtBefore;
    }
    
    public void setTotalAmtBefore(String totalAmtBefore)
    {
        this.totalAmtBefore = totalAmtBefore;
    }
    
    public String getSubTotalCopy()
    {
        return subTotalCopy;
    }
    
    public void setSubTotalCopy(String subTotalCopy)
    {
        this.subTotalCopy = subTotalCopy;
    }
    
    public BigDecimal getUnitWidthCopy()
    {
        return unitWidthCopy;
    }
    
    public BigDecimal getUnitLengthCopy()
    {
        return unitLengthCopy;
    }
    
    public BigDecimal getPnlWidthCopy()
    {
        return pnlWidthCopy;
    }
    
    public BigDecimal getPnlLengthCopy()
    {
        return pnlLengthCopy;
    }
    
    public void setUnitWidthCopy(BigDecimal unitWidthCopy)
    {
        this.unitWidthCopy = unitWidthCopy;
    }
    
    public void setUnitLengthCopy(BigDecimal unitLengthCopy)
    {
        this.unitLengthCopy = unitLengthCopy;
    }
    
    public void setPnlWidthCopy(BigDecimal pnlWidthCopy)
    {
        this.pnlWidthCopy = pnlWidthCopy;
    }
    
    public void setPnlLengthCopy(BigDecimal pnlLengthCopy)
    {
        this.pnlLengthCopy = pnlLengthCopy;
    }
    
    public BigDecimal getPnlDivisorCopy()
    {
        return pnlDivisorCopy;
    }
    
    public void setPnlDivisorCopy(BigDecimal pnlDivisorCopy)
    {
        this.pnlDivisorCopy = pnlDivisorCopy;
    }
    
    public String getDeailArea()
    {
        return deailArea;
    }
    
    public String getDeailAreaCopy()
    {
        return deailAreaCopy;
    }
    
    public void setDeailArea(String deailArea)
    {
        this.deailArea = deailArea;
    }
    
    public void setDeailAreaCopy(String deailAreaCopy)
    {
        this.deailAreaCopy = deailAreaCopy;
    }
    
    public Long getDaore()
    {
        return daore;
    }
    
    public void setDaore(Long daore)
    {
        this.daore = daore;
    }
    
    public Long getNaiya()
    {
        return naiya;
    }
    
    public void setNaiya(Long naiya)
    {
        this.naiya = naiya;
    }
    
    public Long getPliesnumber()
    {
        return pliesnumber;
    }
    
    public void setPliesnumber(Long pliesnumber)
    {
        this.pliesnumber = pliesnumber;
    }
    
}