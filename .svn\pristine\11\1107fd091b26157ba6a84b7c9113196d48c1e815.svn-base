package com.kyb.pcberp.modules.quality.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.modules.contract.entity.ContractCraft;
import com.kyb.pcberp.modules.eg.entity.EgProcess;

@SuppressWarnings("serial")
public class InsectTaskDetail extends DataEntity<InsectTaskDetail> {
    private String companyId;

    private String produceRecordId; // 过数记录id

    private String produceBatchId; // 批次id

    private String produceBatchDetailId; // 批次明细id

    private Integer testWay; // 测试类型

    private Integer checkQty; // 测试数量

    private Integer goodQty; // 良品数量

    private Integer repairQty; // 修复数量

    private Integer unGoodQty; // 不良数量

    private String status; // 状态状态1:处理，2：不处理

    private Date madeDate; // 检测日期

    private String madeUserId; // 检测用户

    private String discardUnit; // 责任方

    private String discardCause; // 责任原因

    private String tableNo; // 表格编号

    private String edition; // 版本

    private String auditUserId; // 审核人

    private Integer inspectType; // 不良类型(0：正常，1:报废,2:返工,3:替换，4：二次补料)

    private String pressureValue; // 耐压值

    private String leakageCurrent; // 漏电流

    private Integer classType; // 班别类型(1：白班，2：晚班)

    private String unit; // 单位

    private List<InsectTaskDetail> list;

    private String craftNo; // 生产编号

    private BigDecimal area; // 面积

    private ContractCraft craft;

    private String craftNoOne;

    private String currProcessId;

    private String dutyProcessId;

    private String complaintId;

    private String cardId;

    private Integer sortNum;

    private String dutyType;

    private String taskId;

    private Integer sourceType;

    private String processIds;

    private String madeUserName;

    private List<EgProcess> processList;

    private String processId;

    private String processCraftId;

    private String madeName;

    private String no;

    private String testWayStr;

    private String userId;

    private String notificationNo;

    private Integer takeOverQtyPcsA;

    private Integer takeOverQtyPcsB;

    private String oldDetailId;

    private Integer pcsQuantity; // 投料数量

    private Integer orderQty; // 订单数量

    private Integer discardPcsQty; // 累计报废数量

    private Integer fedQty; // 累计补料数量

    private String batchNo; // 批次号

    private String takeOverName; // 接板人

    private String handOverName; // 交板人

    private String processName; // 责任工序

    public String getCompanyId() {
        return companyId;
    }

    public String getProduceRecordId() {
        return produceRecordId;
    }

    public String getProduceBatchId() {
        return produceBatchId;
    }

    public String getProduceBatchDetailId() {
        return produceBatchDetailId;
    }

    public Integer getTestWay() {
        return testWay;
    }

    public Integer getCheckQty() {
        return checkQty;
    }

    public Integer getGoodQty() {
        return goodQty;
    }

    public Integer getRepairQty() {
        return repairQty;
    }

    public Integer getUnGoodQty() {
        return unGoodQty;
    }

    public String getStatus() {
        return status;
    }

    public Date getMadeDate() {
        return madeDate;
    }

    public String getMadeUserId() {
        return madeUserId;
    }

    public String getDiscardUnit() {
        return discardUnit;
    }

    public String getDiscardCause() {
        return discardCause;
    }

    public String getTableNo() {
        return tableNo;
    }

    public String getEdition() {
        return edition;
    }

    public String getAuditUserId() {
        return auditUserId;
    }

    public Integer getInspectType() {
        return inspectType;
    }

    public String getPressureValue() {
        return pressureValue;
    }

    public String getLeakageCurrent() {
        return leakageCurrent;
    }

    public List<InsectTaskDetail> getList() {
        return list;
    }

    public String getCraftNo() {
        return craftNo;
    }

    public BigDecimal getArea() {
        return area;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public void setProduceRecordId(String produceRecordId) {
        this.produceRecordId = produceRecordId;
    }

    public void setProduceBatchId(String produceBatchId) {
        this.produceBatchId = produceBatchId;
    }

    public void setProduceBatchDetailId(String produceBatchDetailId) {
        this.produceBatchDetailId = produceBatchDetailId;
    }

    public void setTestWay(Integer testWay) {
        this.testWay = testWay;
    }

    public void setCheckQty(Integer checkQty) {
        this.checkQty = checkQty;
    }

    public void setGoodQty(Integer goodQty) {
        this.goodQty = goodQty;
    }

    public void setRepairQty(Integer repairQty) {
        this.repairQty = repairQty;
    }

    public void setUnGoodQty(Integer unGoodQty) {
        this.unGoodQty = unGoodQty;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setMadeDate(Date madeDate) {
        this.madeDate = madeDate;
    }

    public void setMadeUserId(String madeUserId) {
        this.madeUserId = madeUserId;
    }

    public void setDiscardUnit(String discardUnit) {
        this.discardUnit = discardUnit;
    }

    public void setDiscardCause(String discardCause) {
        this.discardCause = discardCause;
    }

    public void setTableNo(String tableNo) {
        this.tableNo = tableNo;
    }

    public void setEdition(String edition) {
        this.edition = edition;
    }

    public void setAuditUserId(String auditUserId) {
        this.auditUserId = auditUserId;
    }

    public void setInspectType(Integer inspectType) {
        this.inspectType = inspectType;
    }

    public void setPressureValue(String pressureValue) {
        this.pressureValue = pressureValue;
    }

    public void setLeakageCurrent(String leakageCurrent) {
        this.leakageCurrent = leakageCurrent;
    }

    public void setList(List<InsectTaskDetail> list) {
        this.list = list;
    }

    public void setCraftNo(String craftNo) {
        this.craftNo = craftNo;
    }

    public void setArea(BigDecimal area) {
        this.area = area;
    }

    public Integer getClassType() {
        return classType;
    }

    public void setClassType(Integer classType) {
        this.classType = classType;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public ContractCraft getCraft() {
        return craft;
    }

    public void setCraft(ContractCraft craft) {
        this.craft = craft;
    }

    public String getCraftNoOne() {
        return craftNoOne;
    }

    public void setCraftNoOne(String craftNoOne) {
        this.craftNoOne = craftNoOne;
    }

    public String getCurrProcessId() {
        return currProcessId;
    }

    public void setCurrProcessId(String currProcessId) {
        this.currProcessId = currProcessId;
    }

    public String getDutyProcessId() {
        return dutyProcessId;
    }

    public void setDutyProcessId(String dutyProcessId) {
        this.dutyProcessId = dutyProcessId;
    }

    public String getComplaintId() {
        return complaintId;
    }

    public void setComplaintId(String complaintId) {
        this.complaintId = complaintId;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public String getDutyType() {
        return dutyType;
    }

    public void setDutyType(String dutyType) {
        this.dutyType = dutyType;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public String getProcessIds() {
        return processIds;
    }

    public void setProcessIds(String processIds) {
        this.processIds = processIds;
    }

    public String getMadeUserName() {
        return madeUserName;
    }

    public void setMadeUserName(String madeUserName) {
        this.madeUserName = madeUserName;
    }

    public List<EgProcess> getProcessList() {
        return processList;
    }

    public void setProcessList(List<EgProcess> processList) {
        this.processList = processList;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getProcessCraftId() {
        return processCraftId;
    }

    public void setProcessCraftId(String processCraftId) {
        this.processCraftId = processCraftId;
    }

    public String getMadeName() {
        return madeName;
    }

    public void setMadeName(String madeName) {
        this.madeName = madeName;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getTestWayStr() {
        return testWayStr;
    }

    public void setTestWayStr(String testWayStr) {
        this.testWayStr = testWayStr;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNotificationNo() {
        return notificationNo;
    }

    public void setNotificationNo(String notificationNo) {
        this.notificationNo = notificationNo;
    }

    public Integer getTakeOverQtyPcsA() {
        return takeOverQtyPcsA;
    }

    public void setTakeOverQtyPcsA(Integer takeOverQtyPcsA) {
        this.takeOverQtyPcsA = takeOverQtyPcsA;
    }

    public Integer getTakeOverQtyPcsB() {
        return takeOverQtyPcsB;
    }

    public void setTakeOverQtyPcsB(Integer takeOverQtyPcsB) {
        this.takeOverQtyPcsB = takeOverQtyPcsB;
    }

    public String getOldDetailId() {
        return oldDetailId;
    }

    public void setOldDetailId(String oldDetailId) {
        this.oldDetailId = oldDetailId;
    }

    public Integer getPcsQuantity() {
        return pcsQuantity;
    }

    public void setPcsQuantity(Integer pcsQuantity) {
        this.pcsQuantity = pcsQuantity;
    }

    public Integer getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(Integer orderQty) {
        this.orderQty = orderQty;
    }

    public Integer getDiscardPcsQty() {
        return discardPcsQty;
    }

    public void setDiscardPcsQty(Integer discardPcsQty) {
        this.discardPcsQty = discardPcsQty;
    }

    public Integer getFedQty() {
        return fedQty;
    }

    public void setFedQty(Integer fedQty) {
        this.fedQty = fedQty;
    }

    // 欠数
    public Integer getUndercount()
    {
        Integer undercount = 0;
        if(null == pcsQuantity)
        {
            pcsQuantity = 0;
        }
        if(null == orderQty)
        {
            orderQty = 0;
        }
        if(null == discardPcsQty)
        {
            discardPcsQty = 0;
        }
        if(null == fedQty)
        {
            fedQty = 0;
        }
        undercount = pcsQuantity + fedQty - discardPcsQty - orderQty;
        if(undercount > 0)
        {
            undercount = 0;
        }
        return undercount;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getTakeOverName() {
        return takeOverName;
    }

    public void setTakeOverName(String takeOverName) {
        this.takeOverName = takeOverName;
    }

    public String getHandOverName() {
        return handOverName;
    }

    public void setHandOverName(String handOverName) {
        this.handOverName = handOverName;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getMadeDateStr()
    {
        if (madeDate != null)
        {
            return DateUtils.formatDate(madeDate, "yyyy-MM-dd");
        }
        return null;
    }
}
