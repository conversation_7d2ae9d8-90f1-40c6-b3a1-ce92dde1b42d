<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.stock.dao.MatOutboundDao">
	
	<insert id="insert" keyProperty="recordId" useGeneratedKeys="true">
		INSERT INTO sl_material_outbound
		(
			companyId,
			customerId,
			no,
			linkman,
			status,
			activeFlag,
			createdBy,
			createdDate,
			branchId,
			itemType
		)VALUES(
			#{company.recordId},
			#{customerId},
			#{no},
			#{linkman},
			#{status},
			1,
			#{createdBy.recordId},
			#{createdDate},
			#{branchId},
			#{itemType}
		)
	</insert>
	
	<select id="findList" resultType="MatOutbound">
		SELECT
			a.*
		FROM sl_material_outbound a
		WHERE a.companyId = #{companyId} AND a.activeFlag = 1
	</select>
	
	<select id="get" resultType="MatOutbound">
		SELECT
			a.*
		FROM sl_material_outbound a
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="getOutBoundIds" resultType="String">
 	  	SELECT
			GROUP_CONCAT(DISTINCT a.outboundId)
		FROM st_material_store a
		LEFT JOIN sl_material_outbound b ON b.recordId = a.outboundId
		LEFT JOIN sl_material_contract_detail c ON c.recordId = a.contractDetailId
		LEFT JOIN sl_material_contract d ON d.recordId = c.purchasingId
		LEFT JOIN md_material e ON e.recordId = a.materialId
		WHERE a.companyId = #{companyId} AND a.`status` = 99999901 
		AND a.activeFlag = 1 AND a.outBoundId IS NOT NULL
		<if test="startTime != null and startTime != ''">
			AND b.createdDate <![CDATA[>=]]> #{startTime}
		</if>
		<if test="endTime != null and endTime != ''">
			AND b.createdDate <![CDATA[<=]]> #{endTime}
		</if>
		<if test="outBoundNoQuery != null and outBoundNoQuery != ''">
			AND REPLACE(b.no," ","") LIKE CONCAT('%',REPLACE(#{outBoundNoQuery}," ",""),'%')
		</if>
		<if test="specificationQuery != null and specificationQuery != ''">
			AND REPLACE(e.specification," ","") LIKE CONCAT('%',REPLACE(#{specificationQuery}," ",""),'%')
		</if>
		<if test="matNoQuery != null and matNoQuery != ''">
			AND REPLACE(e.no," ","") LIKE CONCAT('%',REPLACE(#{matNoQuery}," ",""),'%')
		</if>	
		<if test="matNameQuery != null and matNameQuery != ''">
			AND REPLACE(e.`name`," ","") LIKE CONCAT('%',REPLACE(#{matNameQuery}," ",""),'%')
		</if>			
	</select>
	
	<select id="loadOutBoundList" resultType="MatOutbound">
		SELECT 
			a.*,
 			CONCAT(
				IFNULL(e.`name`,d.`name`),
				"-",
				IFNULL(e.`phone`,d.`phone`),
				"-",
				IFNULL(e.`address`,d.`address`)
			) AS "linkmanMsg",
			b.no AS "cusNo",
			b.shortName AS "cusName",
			f.userName
		FROM sl_material_outbound a
		LEFT JOIN md_customer b ON b.recordId = a.customerId
		LEFT JOIN md_branch c ON c.recordId = a.branchId
		LEFT JOIN md_customer_contact d ON d.recordId = b.bizPerson
		LEFT JOIN md_customer_contact e ON e.recordId = a.linkman
		LEFT JOIN sm_user f ON f.recordId = a.createdBy
		WHERE a.companyId = #{companyId} AND a.activeFlag = 1
		AND FIND_IN_SET(a.recordId,#{recordId})
		ORDER BY a.createdDate DESC
		<if test="pageNo != null and pageNo !=''">
	    	LIMIT ${pageNo}, ${pageSize}
	    </if>
	</select>
	
	<select id="getOutBoundDeail" resultType="MatOutBoundDeail">
        SELECT
			a.recordId,
			a.companyId,
			a.materialId,
			f.customerId,
			a.contractDetailId,
			a.inOutType,
			a.operateDate,
			a.operator,
			a.outBoundStocks,
			a.donateStocks,
			e.`name` AS "storeName",
			c.no AS "matNo",
			c.name AS "matName",
			c.specification AS "matSpecification",
			d.userName
		FROM st_material_store a
		LEFT JOIN sl_material_contract_detail b ON b.recordId = a.contractDetailId
		LEFT JOIN md_material c ON c.recordId = a.materialId
		LEFT JOIN sm_user d ON d.recordId = a.operator
		LEFT JOIN md_store_house e ON e.recordId = c.storeHouseId
		LEFT JOIN sl_material_contract f ON f.recordId = b.purchasingId
		WHERE a.outBoundId = #{recordId} AND a.`status` = 99999901 AND a.activeFlag = 1
	</select>
	
	<select id="getOutBoundCount" resultType="Integer">
       SELECT
			COUNT(recordId)
		FROM sl_material_outbound
		WHERE companyId = #{companyId} AND activeFlag = 1
		AND FIND_IN_SET(recordId,#{recordId})
	</select>
	
	<select id="loadWaitOutBoundList" resultType="MatOutBoundDeail">
		SELECT
			a.recordId AS "recordId",
			a.companyId,
			a.recordId AS "contractDetailId",
			b.recordId AS "contractId",
			b.no AS "contractNo",
			c.no AS "matNo",
			a.orgId,
			c.name AS "matName",
			c.specification AS "matSpecification",
			d.recordId AS "cusId",
			d.recordId AS "customerId",
			d.no AS "cusNo",
			d.shortName AS "cusName",
			d.address,
			d.phone,
			a.quantity,
			b.no,
			b.orderDate,
			e.concatValue AS "concatValue",
			IFNULL(f.snapStocks,0) AS "snapStocks",
			1 AS "stockType",
			b.branchId,
			d.bizPerson AS "linkman",
			c.recordId AS "materialId",
			g.recordId AS "storeHouseId",
			100000 AS "sortNum"
		FROM sl_material_contract_detail a
		LEFT JOIN sl_material_contract b ON b.recordId = a.purchasingId
		LEFT JOIN md_material c ON c.recordId = a.materialId
		LEFT JOIN md_customer d ON d.recordId = b.customerId
		LEFT JOIN 
		(
			SELECT
				GROUP_CONCAT(a.`value` SEPARATOR '*') AS "concatValue",
				a.materialId AS "materialId"
			FROM md_material_specification_relation a
			LEFT JOIN md_material_specification b ON b.recordId = a.specificationId
			WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND a.`value` IS NOT NULL
			AND a.`value` <![CDATA[<>]]> '' AND FIND_IN_SET(b.`name`, '长,宽') GROUP BY a.materialId
		) e ON e.materialId = c.recordId
		LEFT JOIN 
		(
			SELECT
				SUM(
					CASE inOutType WHEN 1 THEN IFNULL(quantity, 0 ) 
						WHEN 2 THEN IFNULL( quantity, 0 ) 
						WHEN 3 THEN IFNULL(- quantity, 0 ) 
						WHEN 4 THEN IFNULL(- quantity, 0 ) 
						WHEN 5 THEN IFNULL(- quantity, 0 ) 
						WHEN 6 THEN IFNULL(- quantity, 0 ) 
						WHEN 7 THEN IFNULL( quantity, 0 ) 
						WHEN 8 THEN IFNULL(- quantity, 0 ) 
						WHEN 9 THEN IFNULL( quantity, 0 ) 
						WHEN 10 THEN IFNULL(- quantity, 0 ) 
						WHEN 20 THEN IFNULL( quantity, 0 ) 
					 	ELSE 0 
					END 
					) AS "snapStocks",
					materialId AS "materialId"
			FROM st_material_store
			WHERE companyId = #{company.recordId} AND activeFlag = 1 
			AND STATUS = 99999901 AND operateDate >= '2021-01-01' 
			GROUP BY materialId
		) f ON f.materialId = c.recordId
		LEFT JOIN md_store_house g ON g.recordId = c.storeHouseId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 
		AND b.`status` IN(2000201,500101,500102)
		AND a.`status` IN(2000201,500101,500102)
		AND a.createdDate >= #{startTime}
		AND a.createdDate <![CDATA[<=]]> #{endTime}
		ORDER BY c.`no`
	</select>
	
	<select id="loadComplaintWaitOutBoundList" resultType="MatOutBoundDeail">
		SELECT
			a.recordId AS "recordId",
			a.companyId,
			a.recordId AS "contractDetailId",
			b.recordId AS "contractId",
			b.no AS "contractNo",
			c.no AS "matNo",
			c.name AS "matName",
			c.specification AS "matSpecification",
			d.recordId AS "cusId",
			d.recordId AS "customerId",
			d.no AS "cusNo",
			d.shortName AS "cusName",
			d.address,
			d.phone,
			a.quantity,
			b.no,
			b.orderDate,
			e.concatValue AS "concatValue",
			IFNULL(f.snapStocks,0) AS "snapStocks",
			2 AS "stockType",
			b.branchId,
			d.bizPerson AS "linkman",
			c.recordId AS "materialId",
			g.recordId AS "storeHouseId",
			h.recordId AS "reject.recordId",
			h.no AS "reject.no",
			h.replenishQty AS "reject.replenishQty",
			100000 AS "sortNum"
		FROM sl_material_contract_detail a
		LEFT JOIN sl_material_contract b ON b.recordId = a.purchasingId
		LEFT JOIN md_material c ON c.recordId = a.materialId
		LEFT JOIN md_customer d ON d.recordId = b.customerId
		LEFT JOIN 
		(
			SELECT
				GROUP_CONCAT(a.`value` SEPARATOR '*') AS "concatValue",
				a.materialId AS "materialId"
			FROM md_material_specification_relation a
			LEFT JOIN md_material_specification b ON b.recordId = a.specificationId
			WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND a.`value` IS NOT NULL
			AND a.`value` <![CDATA[<>]]> '' AND FIND_IN_SET(b.`name`, '长,宽') GROUP BY a.materialId
		) e ON e.materialId = c.recordId
		LEFT JOIN 
		(
			SELECT
				SUM(
					CASE inOutType WHEN 1 THEN IFNULL(quantity, 0 ) 
						WHEN 2 THEN IFNULL( quantity, 0 ) 
						WHEN 3 THEN IFNULL(- quantity, 0 ) 
						WHEN 4 THEN IFNULL(- quantity, 0 ) 
						WHEN 5 THEN IFNULL(- quantity, 0 ) 
						WHEN 6 THEN IFNULL(- quantity, 0 ) 
						WHEN 7 THEN IFNULL( quantity, 0 ) 
						WHEN 8 THEN IFNULL(- quantity, 0 ) 
						WHEN 9 THEN IFNULL( quantity, 0 ) 
						WHEN 10 THEN IFNULL(- quantity, 0 ) 
						WHEN 20 THEN IFNULL( quantity, 0 ) 
					 	ELSE 0 
					END 
					) AS "snapStocks",
					materialId AS "materialId"
			FROM st_material_store
			WHERE companyId = #{company.recordId} AND activeFlag = 1 
			AND STATUS = 99999901 AND operateDate >= '2021-01-01' 
			GROUP BY materialId
		) f ON f.materialId = c.recordId
		LEFT JOIN md_store_house g ON g.recordId = c.storeHouseId
		LEFT JOIN 
		(
			SELECT
				GROUP_CONCAT(recordId) AS "recordId",
				GROUP_CONCAT(`no`) AS "no",
				purchasingDetailId AS "contractDetailId",
				SUM(IFNULL(replenishQty,0)) AS "replenishQty",
				MAX(createdDate) AS "createdDate"
			FROM pu_material_reject
			WHERE companyId = #{company.recordId} AND activeFlag = 1 AND `status` = 200207
			GROUP BY purchasingDetailId
		) h ON h.contractDetailId = a.recordId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1  AND h.recordId IS NOT NULL
		AND h.createdDate >= #{startTime} AND h.createdDate <![CDATA[<=]]> #{endTime}
		ORDER BY c.`no`
	</select>
	
	<select id="findDeliveryNoisEnable" resultType="Integer">
		select COUNT(1) from sl_material_outbound a where a.no = #{no} and a.companyId  = #{company.recordId}
		<if test="recordId != null and recordId != ''">
			AND a.recordId <![CDATA[!=]]> #{recordId}
		</if>
	</select>
	
	<insert id="insertDelivery" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO sl_material_delivery
		(
		   companyId,
		   customerId,
		   branchId,
		   no,
		   sentTime,
		   receivedNotes,
		   deliveryWay,
		   address,
		   phone,
		   status,
		   activeFlag,
		   createdBy,
		   createdDate,
		   remark,
		   itemType,
		   linkManId
		)VALUES(
			#{company.recordId},
			#{customerId},
			#{branchId},
			#{no},
			#{sentTime},
			"自动生成",
			#{deliveryWay},
			#{address},
			#{phone},
			500517,
			1,
			#{createdBy.recordId},
			#{createdDate},
			#{remark},
			#{itemType},
			#{linkman}
		)
	</insert>
	
	<insert id="runBatchAdd" keyProperty="recordId" useGeneratedKeys="true">
		INSERT INTO sl_material_delivery_detail
		(
		   companyId,
		   deliveryId,
		   materialId,
		   contractDetailId,
		   quantity,
		   spares,
		   activeFlag,
		   createdBy,
		   createdDate,
		   status,
		   remark,
		   complaintId
		)VALUES(
			#{companyId},
			#{deliveryId},
			#{materialId},
			#{contractDetailId},
			#{outBoundStocks},
			#{donateStocks},
			1,
			#{createdBy.recordId},
			#{createdDate},
			500518,
			#{remark},
			#{complaintId}
		)
	</insert>
	
	<insert id="runInsertDeail" keyProperty="recordId" useGeneratedKeys="true">
		INSERT INTO st_material_store(
			companyId,
			materialId,
			storeHouseId,
			inOutType,
			quantity,
			operateDate,
			operator,
			status,
			activeFlag,
			createdBy,
			createdDate,
			remark,
			stockPlaceId,
			stockPlaceComId,
			contractDetailId,
			deliveryDetailId,
			outboundId,
			rejectId,
			outBoundStocks,
			donateStocks
		) VALUES (
			#{companyId},
			#{materialId},
			#{storeHouseId},
			8,
			#{sumQualitity},
			#{createdDate},
			#{createdBy.recordId},
			99999901,
			1,
			#{createdBy.recordId},
			#{createdDate},
			#{remark},
			#{stockPlaceId},
			#{stockPlaceComId},
			#{contractDetailId},
			#{deliveryDetailId},
			#{outBoundId},
			#{complaintId},
			#{outBoundStocks},
			#{donateStocks}
		)
	</insert>
	
	<select id="getListById" resultType="MatOutBoundDeail">
		SELECT
			a.recordId AS "recordId",
			a.outboundId AS "outboundId",
			c.recordId AS "deliveryDetailId",
			c.deliveryId AS "deliveryId",
			d.recordId AS "contractDetailId",
			d.purchasingId AS "contractId",
			a.rejectId AS "reject.recordId"
		FROM st_material_store a
		LEFT JOIN sl_material_delivery_detail c ON c.recordId = a.deliveryDetailId
		LEFT JOIN sl_material_contract_detail d ON d.recordId = c.contractDetailId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND a.outboundId = #{recordId}
		<if test="outBoundDetailId != null and outBoundDetailId != ''">
			AND a.recordId = #{outBoundDetailId}
		</if>
	</select>
	
	<select id="checkCount" resultType="Integer">
		SELECT
			COUNT(1)
		FROM st_material_store
		WHERE outboundId = #{outBoundId} AND activeFlag = 1 AND status = 99999901
	</select>
	
	<update id="delete">
		UPDATE sl_material_outbound SET
			activeFlag = 2
		WHERE recordId = #{recordId}
	</update>
	
</mapper>