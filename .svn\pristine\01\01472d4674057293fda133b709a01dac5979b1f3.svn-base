package com.kyb.pcberp.modules.stock.entity;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.HttpApi;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

import net.sf.json.JSONArray;

/** zjn 2019-03-11 共享仓库实体类*/
@SuppressWarnings("serial")
public class ShareHouse extends DataEntity<ShareHouse>
{
    private Company shareCompany; // 共享公司
    
    private StoreHouse storeHouse; // 共享仓库
    
    private String status; //1001:取消自动生成,1002 :自动生成
    
    private User user; //用做保存共享仓库用户
    
    private boolean equalFlag = false;// 是否相同
    
    private List<User> userList; // 用户集合

    public Company getShareCompany()
    {
        return shareCompany;
    }

    public void setShareCompany(Company shareCompany)
    {
        this.shareCompany = shareCompany;
    }

    public StoreHouse getStoreHouse()
    {
        return storeHouse;
    }

    public void setStoreHouse(StoreHouse storeHouse)
    {
        this.storeHouse = storeHouse;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public User getUser()
    {
        return user;
    }

    public void setUser(User user)
    {
        this.user = user;
    }
    
    public String getCreatedDateStr() 
    {
        if(null != this.createdDate) 
        {
            return DateUtils.formatDate(this.createdDate, "yyyy-MM-dd HH:mm:ss");
        }
        else 
        {
            return null;
        }
    }
    
    /** zjn 2019-03-12 获取共享公司*/
    @SuppressWarnings("unchecked")
    public List<String> getShareCompanyIdList(String url)
    {
        List<String> erpCompanyIds = new ArrayList<>();
        Company company = UserUtils.getUser().getCompany();
        Map<String, String> map = HttpApi.getResult(company.getRecordId(), url);
        if(null != map && null != map.get("shareErpCompanyList")) 
        {
            JSONArray array = JSONArray.fromObject(map.get("shareErpCompanyList"));
            if(null != array)
            {
                for(int i=0;i<array.size();i++)
                {
                    //erp公司Id
                    String erpCompanyId = array.get(i).toString();
                    erpCompanyIds.add(erpCompanyId);
                }
            }
        }
        return erpCompanyIds;
    }

    public boolean getEqualFlag()
    {
        return equalFlag;
    }

    public void setEqualFlag(boolean equalFlag)
    {
        this.equalFlag = equalFlag;
    }

    public List<User> getUserList()
    {
        return userList;
    }

    public void setUserList(List<User> userList)
    {
        this.userList = userList;
    }
    
    
}
