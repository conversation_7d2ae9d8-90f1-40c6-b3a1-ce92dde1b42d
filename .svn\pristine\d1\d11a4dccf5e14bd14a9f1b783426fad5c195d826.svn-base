<%@ page contentType="text/html;charset=UTF-8" %>
<div ng-intro-options="introListOptions" ng-intro-method="helpList" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="introInfoOptions" ng-intro-method="helpInfo" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="introInfo1Options" ng-intro-method="helpInfo1" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="introDetailOptions" ng-intro-method="helpDetail" ng-intro-autostart="shouldAutoStart"></div>
<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">采购管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="purch.puReturns">客诉单管理</a>
        </li>
    </ul>
    <div class="page-toolbar">
    	<button class="btn default btn-fit-height pull-right" ng-click="help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>
</div>
<!-- END 导航-->
<tabset class="tabset-margin-top">
    <!-- BEGIN 用户列表 -->
    <tab heading="客诉单" active="rejectCtrl.tabs.viewForm.active">
        <div class="rows">
            <div class="panel panel-default" id="step6">
                <div class="panel-heading font-blue-hoki">查询</div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">客诉单号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="rejectCtrl.query.no"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">客户编号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="rejectCtrl.query.customerNo"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">原料合同号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="rejectCtrl.query.contractNo"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                        	<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">申请日期：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <div class="input-prepend input-group">
											<span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
											<input type="text" class="form-control" disable-auto-validate="true"
												ng-blur="rejectCtrl.initDate(rejectCtrl.time)"
												kyb-daterange 
												kyb-daterange-options="rejectCtrl.rangeOptions"
												ng-model="rejectCtrl.time" 
												placeholder="请选择时间段">
										</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default btn-default-width pull-right"
                                        ng-click="rejectCtrl.doQuery()"><i class="fa fa-search"></i> 查询
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">客诉单列表</div>
                    <div class="actions" id="step7">
	                    <div class="portlet-input input-inline input-small">
	                        <button type="button" class="btn green btn-default-width"
	                                ng-click="rejectCtrl.addReject()" ng-if="rejectCtrl.right.edit">
	                            <i class="fa fa-plus"></i> 添加
	                        </button>
	                    </div>
                    </div>
                </div>
                <div id="step1" class="portlet-body">
                    <div class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading">
                                <th id="step2" ng-class="{'sorting': rejectCtrl.sort.no.both, 'sorting_desc': rejectCtrl.sort.no.desc, 'sorting_asc': rejectCtrl.sort.no.asc}" 
                                			ng-click="rejectCtrl.sortClick('no')">客诉单号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                </th>
                                <th >客户编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                </th>
                                <th >客户名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                </th>
                                <th>原料合同</th>
                                <th>物料编号</th>
                                <th>退货数量</th>
                                <th>退货金额</th>
                                <th>赔偿金额</th>
                                <th>退货原因</th>
                                <th >申请日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                </th>
                                <th>申请人</th>
                                <th >状态&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                </th>
                                <th>操作</th>
                               
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in rejectCtrl.page.data.list" >
                                <td><a ng-click="rejectCtrl.showOrEditPuRtuens($index)">{{row.no}}</a></td>
                                <td ng-bind="row.customer.no"></td>
                                <td ng-bind="row.customer.name"></td>
                                <td ng-bind="row.materialContractDetail.materialContract.no"></td>
                                <td ng-bind="row.materialContractDetail.materialNo"></td>
                                <td ng-bind="row.quantity"></td>
                                <td ng-bind="row.amount"></td>
                                <td ng-bind="row.satisfactionAmount"></td>
                                <td ng-bind="row.rejectCause"></td>
                                <td ng-bind="row.applyDate"></td>
                                <td ng-bind="row.createdBy.userName"></td>
                                <td>
	                                <span ng-if="row.status == '200201'" class="label label-sm label-default">未确认</span>
	                                <span ng-if="row.status == '200207'" class="label label-sm label-success">已审核 </span>
	                                <span ng-if="row.status == '200204'" class="label label-sm label-success">已出库 </span>
	                                <span ng-if="row.status == '200205'"  class="label label-sm label-success">已作废 </span>
                                </td>
                                <td>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="rejectCtrl.right.edit && (row.status == '200201')" ng-click="rejectCtrl.delRejectApplication($index)"><i class="fa fa-times font-red"></i> 删除</a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="rejectCtrl.right.manage && (row.status == '200201')" ng-click="rejectCtrl.audRejectApplication($index)"><i class="fa fa-check-square-o font-green"></i> 审核</a>
                                </td>
                                
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row page-margin-top">
                        <div id="step5" class="col-md-12 col-lg-6">
                            <span class="inline">每页</span>
                            <select class="form-control inline" style="margin-top:8px; width:100px;"
                                    disable-valid-styling="true"
                                    disable-invalid-styling="true"
                                    ng-model="rejectCtrl.page.pageSize"
                                    ng-change="rejectCtrl.pageSizeChange()"
                                    ng-options="option for option in rejectCtrl.page.pageSizeOptions">
                            </select>
                            <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{rejectCtrl.page.data.startCount}} / {{rejectCtrl.page.data.endCount}} 条，共 {{rejectCtrl.page.data.count}} 条</span>
                        </div>
                        <div id="step4" class="col-md-12 col-lg-6">
                            <paging
                                    class="pull-right"
                                    page="rejectCtrl.page.data.pageNo"
                                    page-size="rejectCtrl.page.data.pageSize"
                                    total="rejectCtrl.page.data.count"
                                    adjacent="1"
                                    dots="..."
                                    scroll-top="false"
                                    hide-if-empty="false"
                                    ul-class="pagination"
                                    active-class="active"
                                    disabled-class="disabled"
                                    show-prev-next="true"
                                    paging-action="rejectCtrl.doPage(page, pageSize, total)">
                            </paging>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- END 用户列表 -->
    <!-- BEGIN 用户编辑 -->
    <tab active="rejectCtrl.tabs.editForm.active" ng-show="rejectCtrl.tabs.editForm.show">
        <tab-heading>客诉单详情<i class="fa fa-times set-cursor-pointer" ng-click="rejectCtrl.hideEditForm()"></i></tab-heading>
        <div class="rows">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">客诉单管理{{rejectCtrl.editTitle}}</div>
                </div>
                <div class="portlet-body" id="stepInfo1">
                    <form class="form-horizontal" name="returnsForm1" novalidate="novalidate"
                          ng-submit="rejectCtrl.submitReturnsFormMod(returnsForm1);" ng-submit-force="true"
                          ng-init="rejectCtrl.setFormScope(this, 0)">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">客诉单编号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-model="rejectCtrl.finished.no" ng-disabled="true" />
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>申请日期：</label>
									<div class="col-sm-7 col-md-8">
										<div class="dropdown">
										  <a class="dropdown-toggle" id="dropdown2" role="button" data-toggle="dropdown" data-target="#" href="#">
											<div class="input-group">
												<span type="text" class="form-control" data-ng-bind="rejectCtrl.finished.applyDate | date:'yyyy-MM-dd HH:mm:ss'" ></span><span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
											</div>
										  </a>
										  <ul class="dropdown-menu" role="menu" aria-labelledby="dLabel">
											<datetimepicker data-ng-model="rejectCtrl.finished.createdDate" data-max-date="today" data-datetimepicker-config="{ dropdownSelector: '#dropdown2' }" data-on-set-time="rejectCtrl.onTimeSetReturnDate(newDate, oldDate)" />
										  </ul>
										</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>操作员：</label>

                                     <div class="col-sm-7 col-md-8">
                                        <span class="form-control" ng-disabled="true" ng-bind="rejectCtrl.finished.createdBy.userName"></span>
                                    </div>
                                </div>
                            </div> -->
                            
                            <div class="col-md-6 col-lg-4">
				                <div class="form-group">
				                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>客户编号:</label>
				                    <div class="col-sm-7 col-md-8">
				                        <ui-select on-select="rejectCtrl.selectCustomer($select.selected)" ng-model="rejectCtrl.finished.customerId" theme="bootstrap" required>
				                            <ui-select-match placeholder="">{{$select.selected.name}}</ui-select-match>
				                            <ui-select-choices repeat="item.recordId as item in rejectCtrl.customerList | filter: $select.search">
				                                <div ng-bind-html="item.no | highlight: $select.search"></div>
				                                <small><span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.shortName}}<br></span></small>
				                            </ui-select-choices>
				                        </ui-select>
				                    </div>
				                </div>
				            </div>
				            <div class="col-md-6 col-lg-4">
				                <div class="form-group">
				                    <label class="col-sm-3 col-md-4 control-label">客户名称:</label>
				                    <div class="col-sm-7 col-md-8">
				                        <span class="form-control">{{rejectCtrl.custShowName}}</span>
				                    </div>
				                </div>
				            </div>
				            
				            <div class="col-md-6 col-lg-4">
				                <div class="form-group">
				                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>原料订单号:</label>
				                    <div class="col-sm-7 col-md-8">
				                        <ui-select on-select="rejectCtrl.selectContract($select.selected)" ng-model="rejectCtrl.finished.contractId" theme="bootstrap" required>
				                            <ui-select-match placeholder="">{{$select.selected.no}}</ui-select-match>
				                            <ui-select-choices repeat="item.recordId as item in rejectCtrl.contractList | filter: $select.search">
				                                <div ng-bind-html="item.no | highlight: $select.search"></div>
				                                <small><span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.no}}<br></span></small>
				                            </ui-select-choices>
				                        </ui-select>
				                    </div>
				                </div>
				            </div>
                            
                            <div class="col-md-6 col-lg-4">
				                <div class="form-group">
				                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>物料编号:</label>
				                    <div class="col-sm-7 col-md-8">
				                        <ui-select on-select="rejectCtrl.selectMaterial($select.selected)" ng-model="rejectCtrl.finished.purchasingDetailId" theme="bootstrap" required>
				                            <ui-select-match placeholder="">{{$select.selected.materialNo}}</ui-select-match>
				                            <ui-select-choices repeat="item.recordId as item in rejectCtrl.contractDetailList | filter: $select.search">
				                                <div ng-bind-html="item.materialNo | highlight: $select.search"></div>
				                                <small><span style="color: blue;">数量：{{item.quantity}},单价：{{item.price}}<br></span></small>
				                                <small><span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.craftDescript}}<br></span></small>
				                            </ui-select-choices>
				                        </ui-select>
				                    </div>
				                </div>
				            </div>
                            
                            <!-- <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>物料名称：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" ng-disabled="true" ng-bind="rejectCtrl.finished.material.name"></span>
                                    </div>
                                </div>
                            </div> -->
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>退货数量：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input required type="text" class="form-control"  ng-model="rejectCtrl.finished.quantity"  ng-change="rejectCtrl.changeQuantity()" ng-onlynumberandzero></input>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>单价：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input required class="form-control" ng-disabled="true" ng-model="rejectCtrl.finished.price"></input>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">退货金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" ng-disabled="true" ng-model="rejectCtrl.finished.amount"></input>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">赔偿金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control"  ng-model="rejectCtrl.finished.satisfactionAmount" ng-onlynumberandzero></input>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">补发数量：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"   ng-model="rejectCtrl.finished.replenishQty" ng-change="rejectCtrl.changeReplenishQty()" ng-onlynumberandzero></input>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>原因：</label>
                                    <div class="col-sm-7 col-md-10">
                                            <textarea required class="form-control" ng-model="rejectCtrl.finished.rejectCause" ng-maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>
                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-model="rejectCtrl.finished.remark" ng-maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group" id="stepInfo4">
                            <div class="pull-right">
                                <button type="submit" class="btn btn-primary btn-default-width" style="margin-right:40px;"><i class="fa fa-save"></i> 保&nbsp;存
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
    </tab>
    <!-- END 报价编辑 -->

    <tab active="rejectCtrl.tabs.seeDetail.active" ng-show="rejectCtrl.tabs.seeDetail.show">
        <tab-heading>客诉单详情 <i class="fa fa-times set-cursor-pointer" ng-click="rejectCtrl.hideSeeDetail()"></i></tab-heading>
        <div class="rows">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">客诉单详情</div>
                </div>
                <div class="portlet-body" id="stepInfo11">
                     <form class="form-horizontal" name="returnsForm" novalidate="novalidate"
                          ng-submit="rejectCtrl.submitReturnsFormMod(returnsForm);" ng-submit-force="true"
                          ng-init="rejectCtrl.setFormScope(this, 0)">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">客诉单编号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-model="rejectCtrl.finished.no" ng-disabled="true" />
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>申请日期：</label>
									<div class="col-sm-7 col-md-8">
										<div class="dropdown">
										  <a class="dropdown-toggle" id="dropdown2" role="button" data-toggle="dropdown" data-target="#" href="#">
											<div class="input-group">
												<span type="text"  ng-disabled="rejectCtrl.finished.status != 200201"  class="form-control" data-ng-bind="rejectCtrl.finished.applyDate | date:'yyyy-MM-dd HH:mm:ss'" ></span><span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
											</div>
										  </a>
										  <ul class="dropdown-menu" role="menu" aria-labelledby="dLabel">
											<datetimepicker data-ng-model="rejectCtrl.finished.createdDate" data-datetimepicker-config="{ dropdownSelector: '#dropdown2' }" data-on-set-time="rejectCtrl.onTimeSetReturnDate(newDate, oldDate)" />
										  </ul>
										</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
				                <div class="form-group">
				                    <label class="col-sm-3 col-md-4 control-label">客户编号:</label>
				                    <div class="col-sm-7 col-md-8">
				                        <span class="form-control">{{rejectCtrl.finished.customer.no}}</span>
				                    </div>
				                </div>
				            </div>
				            <div class="col-md-6 col-lg-4">
				                <div class="form-group">
				                    <label class="col-sm-3 col-md-4 control-label">客户名称:</label>
				                    <div class="col-sm-7 col-md-8">
				                        <span class="form-control">{{rejectCtrl.finished.customer.name}}</span>
				                    </div>
				                </div>
				            </div>
				            
				            <div class="col-md-6 col-lg-4">
				                <div class="form-group">
				                    <label class="col-sm-3 col-md-4 control-label">原料订单号:</label>
				                    <div class="col-sm-7 col-md-8">
				                        <span class="form-control">{{rejectCtrl.finished.materialContractDetail.materialContract.no}}</span>
				                    </div>
				                </div>
				            </div>
                            
                            <div class="col-md-6 col-lg-4">
				                <div class="form-group">
				                    <label class="col-sm-3 col-md-4 control-label">物料编号:</label>
				                    <div class="col-sm-7 col-md-8">
				                        <span class="form-control">{{rejectCtrl.finished.materialContractDetail.materialNo}}</span>
				                    </div>
				                </div>
				            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>退货数量：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" ng-disabled="rejectCtrl.finished.status != 200201" class="form-control"  ng-model="rejectCtrl.finished.quantity"  ng-change="rejectCtrl.updateQuantity()" ng-onlynumberandzero></input>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">单价：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" ng-disabled="true" ng-model="rejectCtrl.finished.price"></input>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">退货金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" ng-disabled="true" ng-model="rejectCtrl.finished.amount"></input>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">赔偿金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control"  ng-disabled="rejectCtrl.finished.status != 200201" ng-model="rejectCtrl.finished.satisfactionAmount" ng-onlynumberandzero></input>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">补发数量：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-disabled="rejectCtrl.finished.status != 200201"  ng-model="rejectCtrl.finished.replenishQty" ng-change="rejectCtrl.changeReplenishQty()" ng-onlynumberandzero></input>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>原因：</label>
                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-disabled="rejectCtrl.finished.status != 200201" ng-model="rejectCtrl.finished.rejectCause" ng-maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>
                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-disabled="rejectCtrl.finished.status != 200201" ng-model="rejectCtrl.finished.remark" ng-maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group" id="stepInfo4">
                            <div class="pull-right">
                                <button type="submit" class="btn btn-primary btn-default-width" style="margin-right:40px;" ng-if="rejectCtrl.finished.status == 200201" ><i class="fa fa-save"></i> 保&nbsp;存
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- 详情列表 -->
    </tab>
    <tab active="rejectCtrl.tabs.detailForm.active" ng-show="rejectCtrl.tabs.detailForm.show" select="rejectCtrl.selectDetailForm()">
        <tab-heading>客诉单明细详情 <i class="fa fa-times set-cursor-pointer" ng-click="rejectCtrl.hideDetailForm()"></i></tab-heading>
        <div class="rows">
            <div class="portlet light bordered ">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">客诉单明细{{rejectCtrl.edit.detail.btnDesc}}</div>
                </div>
                <div class="portlet-body form" id="stepDetail1">
                    <form class="form-horizontal" name="detailForm" novalidate="novalidate"
                          ng-submit="rejectCtrl.submitModDetail(rejectCtrl.puReturnsDetail,detailForm);" ng-submit-force="true"
                          ng-init="rejectCtrl.setFormScope(this, 1)">
                        <div class="row">
                       		<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>希望处理方式：</label>
                                    <div class="col-sm-7 col-md-8" ng-if="!rejectCtrl.edit.detail.ismodify">
                                        <ui-select  ng-model="rejectCtrl.puReturnsDetail.desireTreatment" theme="bootstrap" register-custom-form-control ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'"  on-select="rejectCtrl.selDesireTreatment($select.selected.value)" required>
                                            <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                            <ui-select-choices repeat="item.value as item in rejectCtrl.refer.desireTreatments">
                                                <div ng-bind-html="item.name | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                    <div class="col-sm-7 col-md-8" ng-if="rejectCtrl.edit.detail.ismodify">
                                        <ui-select ng-disabled="true" ng-model="rejectCtrl.puReturnsDetail.desireTreatment" theme="bootstrap" register-custom-form-control ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'"  on-select="rejectCtrl.selDesireTreatment($select.selected.value)" required>
                                            <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                            <ui-select-choices repeat="item.value as item in rejectCtrl.refer.desireTreatments">
                                                <div ng-bind-html="item.name | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>采购类型：</label>
                                    <div class="col-sm-7 col-md-8" ng-if="!rejectCtrl.edit.detail.ismodify">
                                        <ui-select on-select="rejectCtrl.selectSupplierAndPuType()" register-custom-form-control ng-model="rejectCtrl.puReturnsDetail.purchasingType" 
                                        		  firstfocus="{{rejectCtrl.focus.detail}}"  theme="bootstrap" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'" required>
                                            <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                            <ui-select-choices repeat="item.value as item in rejectCtrl.purchasingTypes | filter: $select.search">
                                                <div ng-bind-html="item.name | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                     <div class="col-sm-7 col-md-8" ng-if="rejectCtrl.edit.detail.ismodify">
                                        <ui-select ng-disabled="true" on-select="rejectCtrl.selectSupplierAndPuType()" register-custom-form-control ng-model="rejectCtrl.puReturnsDetail.purchasingType" 
                                        		  firstfocus="{{rejectCtrl.focus.detail}}"  theme="bootstrap" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'" required>
                                            <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                            <ui-select-choices repeat="item.value as item in rejectCtrl.purchasingTypes | filter: $select.search">
                                                <div ng-bind-html="item.name | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>采购订单编号:</label>
                                    <div class="col-sm-7 col-md-8" ng-if="!rejectCtrl.edit.detail.ismodify">
                                        <ui-select ng-model="rejectCtrl.purchasingDetail.purchasing" theme="bootstrap" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505' || rejectCtrl.edit.isordercode" 
                                        				register-custom-form-control ng-required="true" on-select="rejectCtrl.selectPuTypeAndPurchasingId()">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
                                            <ui-select-choices repeat="item in rejectCtrl.refer.purchasingNos | filter: $select.search">
                                                <div ng-bind-html="item.no | highlight: $select.search">
                                                    <small>
                                                        <span style="color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.no}}<br></span>
                                                    </small>
                                                </div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                     <div class="col-sm-7 col-md-8" ng-if="rejectCtrl.edit.detail.ismodify">
                                        <ui-select ng-disabled="true" ng-model="rejectCtrl.purchasingDetail.purchasing" theme="bootstrap" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505' || rejectCtrl.edit.isordercode" 
                                        				register-custom-form-control ng-required="true" on-select="rejectCtrl.selectPuTypeAndPurchasingId()">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
                                            <ui-select-choices repeat="item in rejectCtrl.refer.purchasingNos | filter: $select.search">
                                                <div ng-bind-html="item.no | highlight: $select.search">
                                                    <small>
                                                        <span style="color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.no}}<br></span>
                                                    </small>
                                                </div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label" ng-if="rejectCtrl.puReturnsDetail.purchasingType==1"><span class="required">*</span>物料编号:</label>
                                    <label class="col-sm-3 col-md-4 control-label" ng-if="rejectCtrl.puReturnsDetail.purchasingType==2"><span class="required">*</span>客户型号:</label>
                                    <div class="col-sm-7 col-md-8" ng-if="!rejectCtrl.edit.detail.ismodify">
                                        <ui-select ng-model="rejectCtrl.puReturnsDetail.purchasingDetail" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'" 
                                        			ng-if="rejectCtrl.puReturnsDetail.purchasingType==1" register-custom-form-control theme="bootstrap" 
                                        			ng-required="rejectCtrl.puReturnsDetail.purchasingType==1" on-select="rejectCtrl.setAmount()" >
                                            <ui-select-match placeholder="请选择...">{{$select.selected.material.no}}</ui-select-match>
                                            <ui-select-choices repeat="item in rejectCtrl.refer.purchasingDetailList | filter: $select.search">
                                                <div ng-bind-html="item.material.no | highlight: $select.search"></div>
                                                <small>
                                                    <span style="color: blue;">名称：&nbsp;&nbsp;&nbsp;&nbsp;{{item.material.name}}<br></span>
													<span style="color: red;">规格：&nbsp;&nbsp;&nbsp;&nbsp;{{item.material.specification}}</span>
                                                </small>
                                            </ui-select-choices>
                                        </ui-select>
                                        <ui-select ng-model="rejectCtrl.puReturnsDetail.prdorderDetail" theme="bootstrap" register-custom-form-control 
                                        							ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'" on-select="rejectCtrl.setAmount()" 
                                        							ng-if="rejectCtrl.puReturnsDetail.purchasingType==2" ng-required="rejectCtrl.puReturnsDetail.purchasingType==2">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.contractCraft.customerModel}}</ui-select-match>
                                            <ui-select-choices repeat="item in rejectCtrl.refer.prdorderDetailList | filter: $select.search">
                                                <div ng-bind-html="item.contractCraft.customerModel | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                    <div class="col-sm-7 col-md-8" ng-if="rejectCtrl.edit.detail.ismodify">
                                        <ui-select ng-disabled="true" ng-model="rejectCtrl.puReturnsDetail.purchasingDetail" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'" 
                                        			ng-if="rejectCtrl.puReturnsDetail.purchasingType==1" register-custom-form-control theme="bootstrap" 
                                        			ng-required="rejectCtrl.puReturnsDetail.purchasingType==1" on-select="rejectCtrl.setAmount()" >
                                            <ui-select-match placeholder="请选择...">{{$select.selected.material.no}}</ui-select-match>
                                            <ui-select-choices repeat="item in rejectCtrl.refer.purchasingDetailList | filter: $select.search">
                                                <div ng-bind-html="item.material.no | highlight: $select.search"></div>
                                                <small>
                                                    <span style="color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.material.name}}<br></span>
                                                </small>
                                            </ui-select-choices>
                                        </ui-select>
                                        <ui-select ng-disabled="true" ng-model="rejectCtrl.puReturnsDetail.prdorderDetail" theme="bootstrap" register-custom-form-control 
                                        							ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'" on-select="rejectCtrl.setAmount()" 
                                        							ng-if="rejectCtrl.puReturnsDetail.purchasingType==2" ng-required="rejectCtrl.puReturnsDetail.purchasingType==2">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.contractCraft.customerModel}}</ui-select-match>
                                            <ui-select-choices repeat="item in rejectCtrl.refer.prdorderDetailList | filter: $select.search">
                                                <div ng-bind-html="item.contractCraft.customerModel | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>单价：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" placeholder="请输入单价,支持三位小数位。" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'" 
                                        						ng-if="rejectCtrl.puReturnsDetail.purchasingType!=2" ng-model="rejectCtrl.puReturnsDetail.purchasingDetail.price" ng-blur="rejectCtrl.setAmount()" ng-checkmoneymoredragon required/>
                                        <input type="text" class="form-control" placeholder="请输入单价,支持三位小数位。" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'" 
                                        						ng-if="rejectCtrl.puReturnsDetail.purchasingType==2" ng-model="rejectCtrl.puReturnsDetail.prdorderDetail.price.price" ng-blur="rejectCtrl.setAmount()" ng-checkmoneymoredragon required/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>数量：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'" ng-onlydecimal4s
                                        		ng-if="rejectCtrl.puReturnsDetail.purchasingType!=2" ng-model="rejectCtrl.puReturnsDetail.purchasingDetail.quantity" ng-blur="rejectCtrl.setAmount()" ng-change="rejectCtrl.changeDate()" required/>
                                        <input type="text" class="form-control" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'" ng-Onlynumber
                                        		ng-if="rejectCtrl.puReturnsDetail.purchasingType==2" ng-model="rejectCtrl.puReturnsDetail.prdorderDetail.quantity" ng-blur="rejectCtrl.setAmount()" ng-change="rejectCtrl.changeDate()" required/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">总价：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" ng-disabled="true"
                                                  ng-bind="rejectCtrl.puReturnsDetail.amount | currency:'':3"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-lg-4" ng-show="rejectCtrl.showRedeliverDate">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">补货交期：</label>
                                    <div class="col-sm-7 col-md-8">
										<div class="dropdown">
										  <a class="dropdown-toggle" id="dropdown2" role="button" data-toggle="dropdown" data-target="#" href="#">
											<div class="input-group">
												<span type="text" class="form-control" data-ng-bind="rejectCtrl.puReturnsDetail.redeliverDate | date:'yyyy-MM-dd HH:mm:ss'" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'"></span><span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
											</div>
										  </a>
										  <ul class="dropdown-menu" role="menu" aria-labelledby="dLabel" ng-hide="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'">
											<datetimepicker data-ng-model="rejectCtrl.puReturnsDetail.redeliverDate" data-datetimepicker-config="{ dropdownSelector: '#dropdown2' }" data-on-set-time="rejectCtrl.onTimeSetRedeliverDate(newDate, oldDate)" />
										  </ul>
										</div>			                                               
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label" ng-show="!rejectCtrl.showRedeliverDate"><span class="required">*</span>退货原因：</label>
                                     <label class="col-sm-3 col-md-2 control-label" ng-show="rejectCtrl.showRedeliverDate"><span class="required">*</span>补货原因：</label>
                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'" ng-model="rejectCtrl.puReturnsDetail.rejectCause" ng-maxlength="255" required></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>
                                    <div class="col-sm-7 col-md-10">
                                        <textarea class="form-control" ng-disabled="!rejectCtrl.right.edit || rejectCtrl.finished.status == '500505'" ng-model="rejectCtrl.puReturnsDetail.remark" ng-maxlength="255"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-offset-4" id="stepDetail2">
                           <!--  <button type="reset" class="btn btn-default btn-default-width" ng-if="rejectCtrl.right.edit && rejectCtrl.edit.detail.isAdd && rejectCtrl.finished.status != '500505'" ng-click="rejectCtrl.resetPuReturnsDetail()" style="margin-left:15px;"><i class="fa fa-mail-reply"></i> 重&nbsp;置</button> -->
                            <button type="submit" class="btn btn-default-width" ng-class="{green: rejectCtrl.edit.detail.isAdd, purple: !rejectCtrl.edit.detail.isAdd}" style="margin-left:15px;" ng-if="rejectCtrl.right.edit && rejectCtrl.finished.status != '500505'">
                                <i class="fa fa-plus" ng-class="{'fa-plus':rejectCtrl.edit.detail.isAdd, 'fa-edit':!rejectCtrl.edit.detail.isAdd}" ng-disabled="detailForm.$invalid"></i> {{rejectCtrl.edit.detail.btnDesc}}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </tab>
</tabset>

<div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">提示</h4>
            </div>
            <div class="modal-body">
                <p>
                    <span ng-bind="rejectCtrl.message"></span>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="staticRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">删除客诉单</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="rejectCtrl.edit.delMsg">确认删除该客诉单吗？</p></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="rejectCtrl.doDelCard()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="staticDetailRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">删除客诉单明细</h4>
            </div>
            <div class="modal-body clearfix" style="padding-top:25px;">
                <div class="col-sm-1 text-center"><i class="fa fa-warning font-yellow"
                                                     style="font-size: 30px !important;"></i></div>
                <div class="col-sm-11"><p ng-bind="rejectCtrl.delDetailMsg"></p></div>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn blue"
                        ng-click="rejectCtrl.doDelPuReturnsDetail()">确定
                </button>
                <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
            </div>
        </div>
    </div>
</div>