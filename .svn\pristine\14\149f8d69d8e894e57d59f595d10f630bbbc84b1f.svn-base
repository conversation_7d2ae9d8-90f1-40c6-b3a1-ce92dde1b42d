<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="d-flex flex-column flex-root">
    <div class="d-flex flex-row flex-column-fluid page">
        <div class="d-flex flex-column flex-row-fluid wrapper" id="kt_wrapper">
            <div class="content d-flex flex-column flex-column-fluid" id="kt_content"
                 style="background-image: url(${pageContext.request.contextPath}/assets/global/metronic/assets/media/bg/bgMain.jpg);height: 160px;">
                <div class="subheader subheader-transparent" id="kt_subheader">
                    <div class="container d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <h5 class="text-white font-weight-bold my-2 mr-5">实名验证</h5>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-column-fluid">
                    <div class="container">
                        <div class="row">
                            <div class="col-12 text-center">
                                <div class="card card-custom gutter-b card-stretch">
                                    <div class="row card-body p-0 align-items-center">
                                        <div class="col-12">
                                            <div class="d-flex flex-column flex-root">
                                                <div class="row" v-if="!user.valiteStatus">
                                                    <div class="col">
                                                        <div class="d-flex flex-column flex-lg-row flex-row-fluid">
                                                            <div class="flex-row-fluid d-flex flex-column position-relative p-7 overflow-hidden">
                                                                <div class="d-flex flex-center">
                                                                    <div class="login-form login-signin">
                                                                        <form class="form">
                                                                            <div class="form-group">
                                                                                <input class="form-control form-control-solid" placeholder="请输入身份证号" type="text" v-model="idCard" name="idCard" autocomplete="off" required>
                                                                            </div>
                                                                            <div class="form-group">
                                                                                <input class="form-control form-control-solid" placeholder="请输入真实姓名" type="text" v-model="realName" name="realName" autocomplete="off" required>
                                                                            </div>
                                                                            <div class="row pt-8">
                                                                                <div class="col text-center">
                                                                                    <button class="btn btn-primary" type="button" style="width: 25rem" v-on:click="valiteAuth">实名验证</button>
                                                                                </div>
                                                                            </div>
                                                                        </form>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row bg-white" v-else>
                                                    <div class="col text-left">
                                                        <div class="d-flex flex-column flex-lg-row flex-row-fluid">
                                                            <div class="flex-row-fluid d-flex flex-column position-relative p-7 overflow-hidden">
                                                                <div class="d-flex flex-center">
                                                                    <div class="row">
                                                                        <div class="col-xl-12 pb-3 border-bottom">
                                                                            <h5>你已经完成了实名验证</h5>
                                                                        </div>
                                                                        <div class="col-xl-12 pt-3">
                                                                            姓名：{{user.realName}}
                                                                        </div>
                                                                        <div class="col-xl-12 pt-3">
                                                                            身份证号：{{user.idCard}}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>