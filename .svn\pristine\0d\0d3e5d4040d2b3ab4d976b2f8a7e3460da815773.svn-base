package com.kyb.pcberp.modules.hr.payment_center.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.hr.payment_center.pojo.Hr_ClockIn;

import java.util.List;

@MyBatisDao
public interface Hr_ClockInDao extends CrudDao<Hr_ClockIn> {

    List<Hr_ClockIn> getHitCardList(Hr_ClockIn hrClockIn);

    void delHitCard(Hr_ClockIn hrClockIn);

    void insertHitCard(Hr_ClockIn hrClockIn);

    void updateHitCard(Hr_ClockIn hrClockIn);

    List<Hr_ClockIn>getOrganizationList(Hr_ClockIn hrClockIn);

}
