const bus_main = {
    template: '#bus_main',
    created:function(){
        this.$store.dispatch('myStore/setUserInformation')
        this.$parent.changeClass(1)
        this.loadMoudleList("user");
        this.loadReport();
    },
    data(){
        return {
            moudleList: [],
            saleNum: "0",
            saleMoney: "0",
            purchNum: "0",
            purchMoney: "0"
        }
    },
    methods: {
        loadReport: function () {
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/loadReport",
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    _this.saleNum = data.saleNum;
                    _this.saleMoney = data.saleMoney;
                    _this.purchNum = data.purchNum;
                    _this.purchMoney = data.purchMoney;
                }
            })
        },
        loadMoudleList: function (flag) {
            const _this = this;
            const moudle = {};
            moudle.flag = flag;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/getApplyList",
                data: JSON.stringify(moudle),
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    _this.moudleList = data;
                }
            })
        },
        addAllMoudle: function (){
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/addAllMoudle",
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    _this.loadMoudleList("user");
                }
            })
        },
        selectFunction: function (item) {
            if (item.recordId == 1){
                // 企业合作
                this.$router.push("cooperation_main");
            }else if (item.recordId == 2){
                // 产品管理
                this.$router.push("production_main");
            }else if (item.recordId == 3){
                // 历史询价
                this.$router.push("historyBidding_main");
            }else if (item.recordId == 4){
                // 开始询价
                this.$router.push("bidding_main");
            }else if (item.recordId == 5){
                // 产品库存
                this.$router.push("stockTaking_main");
            }else if (item.recordId == 6){
                // 订单管理
                this.$router.push("order_main");
            }else if (item.recordId == 7){
                // 供应商产品
                this.$router.push("supplierProduct");
            }else if (item.recordId == 8){
                // 推送产品
                this.$router.push("pushProduct");
            }else if (item.recordId == 9){
                // 我的招标
                this.$router.push("tender_main");
            }else if (item.recordId == 10){
                // 平台产品
                this.$router.push("icloud_product_main");
            }
            else if (item.recordId == 11){
                // 设备维保
                this.$router.push("maintenance");
                //销售系统
            }else if (item.recordId == 12)
            {
                this.$router.push("salesSystem");
            }else if (item.recordId == 13)
            {
                this.$router.push("purchasingSystem");
            }
/*            else if (item.recordId == 11){
                // 表格导入
                this.$router.push("excelProduct_main");
            }else if (item.recordId == 12){
                // PCB下单
                this.$router.push("pcb_order");
            }*/
        },
        quickOperation: function () {
            $('#quickOperation').modal();
        }
    }
}