package com.kyb.pcberp.modules.quality.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.FmtUtils;
import com.kyb.pcberp.modules.quality.dao.BadDao;
import com.kyb.pcberp.modules.quality.dao.DiscardDao;
import com.kyb.pcberp.modules.quality.dao.ReworkDao;
import com.kyb.pcberp.modules.quality.entity.Bad;
import com.kyb.pcberp.modules.quality.entity.Discard;
import com.kyb.pcberp.modules.quality.entity.Inspect;
import com.kyb.pcberp.modules.quality.entity.Rework;
import com.kyb.pcberp.modules.quality.vo.BadVO;
import com.kyb.pcberp.modules.quality.vo.DiscardVO;
import com.kyb.pcberp.modules.quality.vo.ReworkVO;

@Service
@Transactional(readOnly = true)
public class QualityRecordService
{
    @Autowired
    private DiscardDao discardDao;
    
    @Autowired
    private ReworkDao reworkDao;
    
    @Autowired
    private BadDao badDao;
    
    public Page<Discard> findPage(Page<Discard> page, Inspect inspect)
    {
        Discard discard = new Discard();
        discard.setInspect(inspect);
        discard.setPage(page);
        List<Discard> list = discardDao.findDiscardList(discard);
        if(Collections3.isNotEmpty(list))
        {
            list = getList(list);
            /*String no = null;
            for(Discard dis : list)
            {
                if(StringUtils.isNotBlank(dis.getMatNo()))
                {
                    if(StringUtils.isNotBlank(no))
                    {
                        no = no +","+ dis.getMatNo();
                    }
                    else
                    {
                        no = dis.getMatNo();
                    }
                }
            }
            if(StringUtils.isNotBlank(no))
            {
                Discard query = new Discard();
                query.setCompany(inspect.getCompany());
                query.setCraftNo(no);
                List<Discard> newCardSizeList = discardDao.getNewCardSizeList(query);
                if(Collections3.isNotEmpty(newCardSizeList))
                {
                    for(Discard dis : list)
                    {
                        if(null != dis.getInspect() && dis.getInspect().getType() != 1)
                        {
                            for(Discard newCardSize : newCardSizeList)
                            {
                                if(StringUtils.isNotBlank(dis.getMatNo()) && StringUtils.isNotBlank(newCardSize.getMatNo()) && dis.getMatNo().equals(newCardSize.getMatNo()))
                                {
                                    dis.setUnitLength(newCardSize.getUnitLength());
                                    dis.setUnitWidth(newCardSize.getUnitWidth());
                                    dis.setPnlDivisor(newCardSize.getPnlDivisor());
                                    break;
                                }
                            }
                        }
                    }
                }
            }*/
        }
        page.setList(list);
        return page;
    }
    
    public Page<Rework> findPageOfShow(Page<Rework> page, Inspect inspect)
    {
        Rework rework = new Rework();
        rework.setInspect(inspect);
        rework.setPage(page);
        page.setList(reworkDao.findReworkList(rework));
        return page;
    }
    
    public Page<Bad> findPageOfShowBad(Page<Bad> page, Inspect inspect)
    {
        Bad bad = new Bad();
        bad.setInspect(inspect);
        bad.setPage(page);
        page.setList(badDao.findBadList(bad));
        return page;
    }
    
    public List<DiscardVO> findExportDiscardVOs(HttpServletRequest request, HttpServletResponse response, Inspect is)
    {
        // 定义返回的导出报废记录集合
        List<DiscardVO> discardVOs = new ArrayList<DiscardVO>();
        // 构造查询条件
        Page<Discard> page = new Page<Discard>(request, response, -1);
        Discard discard = new Discard();
        discard.setInspect(is);
        discard.setPage(page);
        // 根据查询条件查询报废记录
        List<Discard> discards = discardDao.findDiscardList(discard);
        if (null != discards && discards.size() > 0)
        {
            discards = getList(discards);
            /*String no = null;
            for(Discard dis : discards)
            {
                if(StringUtils.isNotBlank(dis.getMatNo()))
                {
                    if(StringUtils.isNotBlank(no))
                    {
                        no = no +","+ dis.getMatNo();
                    }
                    else
                    {
                        no = dis.getMatNo();
                    }
                }
            }
            if(StringUtils.isNotBlank(no))
            {
                Discard query = new Discard();
                query.setCompany(is.getCompany());
                query.setCraftNo(no);
                List<Discard> newCardSizeList = discardDao.getNewCardSizeList(query);
                if(Collections3.isNotEmpty(newCardSizeList))
                {
                    for(Discard dis : discards)
                    {
                        if(null != dis.getInspect() && dis.getInspect().getType() != 1)
                        {
                            for(Discard newCardSize : newCardSizeList)
                            {
                                if(StringUtils.isNotBlank(dis.getMatNo()) && StringUtils.isNotBlank(newCardSize.getMatNo()) && dis.getMatNo().equals(newCardSize.getMatNo()))
                                {
                                    dis.setUnitLength(newCardSize.getUnitLength());
                                    dis.setUnitWidth(newCardSize.getUnitWidth());
                                    dis.setPnlDivisor(newCardSize.getPnlDivisor());
                                    break;
                                }
                            }
                        }
                    }
                }
            }*/

            for (Discard dc : discards)
            {
                DiscardVO vo = new DiscardVO();
                Inspect inspect = dc.getInspect();
                vo.setMadeDate(dc.getMadeDate());
                vo.setRemark(dc.getRemark());
                vo.setCheckDate(inspect.getCheckDate());
                vo.setProduceBatchNo(
                    FmtUtils.blank(inspect.getProduceBatch() == null ? "" : inspect.getProduceBatch().getShowNo()));
                vo.setProduceBatchDetailNo(FmtUtils
                    .blank(inspect.getProduceBatchDetail() == null ? "" : inspect.getProduceBatchDetail().getShowNo()));
                vo.setCraftNo(FmtUtils
                    .blank(inspect.getProduceBatch() == null || inspect.getProduceBatch().getNotification() == null ? ""
                        : inspect.getProduceBatch().getNotification().getCraftNo()));
                vo.setProcessName(FmtUtils.blank(inspect.getProcess() == null ? "" : inspect.getProcess().getName()));
                vo.setDutyProcessName(
                    FmtUtils.blank(dc.getDutyProcess() == null ? "" : dc.getDutyProcess().getCategory()));
                vo.setOperator(
                    FmtUtils.blank(null == inspect.getOperator() ? null : inspect.getOperator().getUserName()));
                vo.setProducePcsQty(FmtUtils.formatInt(inspect.getProducePcsQty()));
                vo.setDiscardPcsQty(FmtUtils.formatInt(dc.getDiscardPcsQty()));
                vo.setDiscardCause(FmtUtils.blank(dc.getDiscardCause()));
                vo.setDiscardCauseOne(FmtUtils.blank(dc.getDiscardCauseOne()));
                vo.setUnitWidthAndLength(dc.getUnitLengthWidth());
                vo.setUnitArea(dc.getDiscardArea());
                vo.setCustomerPo(inspect.getCustomerPo());
                discardVOs.add(vo);
            }
        }
        return discardVOs;
    }
    
    public List<ReworkVO> findExportReworkVOs(HttpServletRequest request, HttpServletResponse response, Inspect is)
    {
        // 定义返回的导出报废记录集合
        List<ReworkVO> reworkVOs = new ArrayList<ReworkVO>();
        // 构造查询条件
        Page<Rework> page = new Page<Rework>(request, response, -1);
        Rework rework = new Rework();
        rework.setInspect(is);
        rework.setPage(page);
        // 根据查询条件查询报废记录
        List<Rework> reworks = reworkDao.findReworkList(rework);
        if (null != reworks && reworks.size() > 0)
        {
            for (Rework rwk : reworks)
            {
                ReworkVO vo = new ReworkVO();
                Inspect inspect = rwk.getInspect();
                vo.setCheckDate(inspect.getCheckDate());
                vo.setProduceBatchNo(
                    FmtUtils.blank(inspect.getProduceBatch() == null ? "" : inspect.getProduceBatch().getShowNo()));
                vo.setProduceBatchDetailNo(FmtUtils
                    .blank(inspect.getProduceBatchDetail() == null ? "" : inspect.getProduceBatchDetail().getShowNo()));
                vo.setCraftNo(FmtUtils
                    .blank(inspect.getProduceBatch() == null || inspect.getProduceBatch().getNotification() == null ? ""
                        : inspect.getProduceBatch().getNotification().getCraftNo()));
                vo.setProcessName(FmtUtils.blank(inspect.getProcess() == null ? "" : inspect.getProcess().getName()));
                vo.setDutyProcessName(
                    FmtUtils.blank(rwk.getDutyProcess() == null ? "" : rwk.getDutyProcess().getCategory()));
                vo.setReworker(FmtUtils.blank(rwk.getReworker() == null ? "" : rwk.getReworker().getUserName()));
                vo.setChecker(FmtUtils.blank(rwk.getChecker() == null ? "" : rwk.getChecker().getUserName()));
                vo.setProducePcsQty(FmtUtils.formatInt(inspect.getProducePcsQty()));
                vo.setReworkQty(FmtUtils.formatInt(rwk.getReworkQty()));
                vo.setReworkCause(FmtUtils.blank(rwk.getReworkCause()));
                vo.setReworkResult(FmtUtils.blank(rwk.getReworkResult()));
                reworkVOs.add(vo);
            }
        }
        return reworkVOs;
    }
    
    public List<BadVO> findExportBadVOs(HttpServletRequest request, HttpServletResponse response, Inspect is)
    {
        // 定义返回的导出不良记录集合
        List<BadVO> badVOs = new ArrayList<BadVO>();
        
        // 构造查询条件
        Page<Bad> page = new Page<Bad>(request, response, -1);
        Bad bad = new Bad();
        bad.setInspect(is);
        bad.setPage(page);
        
        // 根据查询条件查询不良记录
        List<Bad> bads = badDao.findBadList(bad);
        if (null != bads && bads.size() > 0)
        {
            for (Bad tBad : bads)
            {
                BadVO vo = new BadVO();
                Inspect inspect = tBad.getInspect();
                vo.setCheckDate(inspect.getCheckDate());
                vo.setProduceBatchNo(
                    FmtUtils.blank(inspect.getProduceBatch() == null ? "" : inspect.getProduceBatch().getShowNo()));
                vo.setProduceBatchDetailNo(FmtUtils
                    .blank(inspect.getProduceBatchDetail() == null ? "" : inspect.getProduceBatchDetail().getShowNo()));
                vo.setCraftNo(FmtUtils
                    .blank(inspect.getProduceBatch() == null || inspect.getProduceBatch().getNotification() == null ? ""
                        : inspect.getProduceBatch().getNotification().getCraftNo()));
                vo.setProcessName(FmtUtils.blank(inspect.getProcess() == null ? "" : inspect.getProcess().getName()));
                vo.setDutyProcessName(
                    FmtUtils.blank(tBad.getDutyProcess() == null ? "" : tBad.getDutyProcess().getCategory()));
                vo.setOperator(FmtUtils.blank(
                    tBad.getInspect().getOperator() == null ? "" : tBad.getInspect().getOperator().getUserName()));
                vo.setProducePcsQty(FmtUtils.formatInt(inspect.getProducePcsQty()));
                vo.setBadQty(FmtUtils.blank(tBad.getBadQty()));
                vo.setBadCause(FmtUtils.blank(tBad.getBadCause()));
                badVOs.add(vo);
            }
        }
        return badVOs;
    }
    
    public Page<Discard> getDiscardReport(Page<Discard> qpage, Inspect inspect)
    {
        Discard discard = new Discard();
        discard.setInspect(inspect);
        // discard.setPage(qpage);
        List<Discard> discards = discardDao.getDiscardReport(discard);
        List<Discard> resultDis = new ArrayList<>();
        if (!CollectionUtils.isEmpty(discards))
        {
            // 按照日期（大组） + 责任工序组+ 料号+责任工序
            Discard groupObj = null;
            int num = discards.size();
            Inspect ins = null;
            for (Discard dis : discards)
            {
                num--;
                // 获取责任工序的工序组
                if (dis.getProcessGroupName() == null)
                {
                    dis.setProcessGroupName("");
                }
                if (dis.getInspectType() != null && dis.getInspectType() == 1)
                {
                    dis.setDiscardPcsQtyA(dis.getDiscardPcsQty());
                    dis.setDiscardPcsQtyB(0);
                    dis.setDiscardAreaA(dis.getDiscardArea());
                    dis.setDiscardAreaB(BigDecimal.ZERO);
                }
                else
                {
                    dis.setDiscardPcsQtyA(0);
                    dis.setDiscardPcsQtyB(dis.getDiscardPcsQty());
                    dis.setDiscardAreaA(BigDecimal.ZERO);
                    dis.setDiscardAreaB(dis.getDiscardArea());
                }
                if (groupObj == null)
                {
                    resultDis.add(dis);
                    groupObj = new Discard();
                    groupObj.setDiscardArea(dis.getDiscardArea());
                    groupObj.setCraftNo(dis.getCraftNo());
                    groupObj.setDiscardCause(dis.getDiscardCause());
                    groupObj.setDutyProcess(dis.getDutyProcess());
                    groupObj.setDiscardAreaA(dis.getDiscardAreaA());
                    groupObj.setDiscardAreaB(dis.getDiscardAreaB());
                    groupObj.setProcessGroupName(dis.getProcessGroupName());
                    ins = new Inspect();
                    ins.setCheckDate(dis.getInspect().getCheckDate());
                    groupObj.setDiscardType(dis.getDiscardType());
                    groupObj.setInspect(ins);
                    if (num == 0)
                    {
                        groupObj.getInspect().setCheckDate(null);// 汇总日期的检查日期为空
                        groupObj.setCraftNo(null);
                        groupObj.setDiscardCause(null);
                        groupObj.setDutyProcess(null);
                        resultDis.add(groupObj);
                        groupObj = null;
                    }
                    
                }
                else if (groupObj.getInspect().getCheckDate().compareTo(dis.getInspect().getCheckDate()) == 0
                    && groupObj.getProcessGroupName().equals(dis.getProcessGroupName())
                    && groupObj.getDiscardType().equals(dis.getDiscardType()))
                {
                    
                    // 检测日期和工序组相同->累计汇总数据
                    if (groupObj.getCraftNo().equals(dis.getCraftNo())
                        && groupObj.getDiscardCause().equals(dis.getDiscardCause())
                        && groupObj.getDutyProcess() != null && dis.getDutyProcess() != null)
                    {
                        if (groupObj.getDutyProcess().getName().equals(dis.getDutyProcess().getName()))
                        {
                            Discard oldDis = resultDis.get(resultDis.size() - 1);
                            if (dis.getInspectType() != null && dis.getInspectType() == 1)
                            {
                                oldDis.setDiscardPcsQtyA(dis.getDiscardPcsQty());
                                oldDis.setDiscardAreaA(dis.getDiscardArea());
                            }
                            else
                            {
                                oldDis.setDiscardPcsQtyB(dis.getDiscardPcsQty());
                                oldDis.setDiscardAreaB(dis.getDiscardArea());
                            }
                        }
                    }
                    else
                    {
                        groupObj.setCraftNo(dis.getCraftNo());
                        groupObj.setDiscardCause(dis.getDiscardCause());
                        groupObj.setDutyProcess(dis.getDutyProcess());
                        groupObj.setDiscardType(dis.getDiscardType());
                        resultDis.add(dis);
                    }
                    groupObj.setDiscardArea(groupObj.getDiscardArea().add(dis.getDiscardArea()));
                    groupObj.setDiscardAreaA(groupObj.getDiscardAreaA().add(dis.getDiscardAreaA()));
                    groupObj.setDiscardAreaB(groupObj.getDiscardAreaB().add(dis.getDiscardAreaB()));
                    if (num == 0)
                    {
                        groupObj.getInspect().setCheckDate(null);// 汇总日期的检查日期为空
                        groupObj.setCraftNo(null);
                        groupObj.setDiscardCause(null);
                        groupObj.setDutyProcess(null);
                        resultDis.add(groupObj);
                        groupObj = null;
                    }
                    
                }
                else
                {
                    // 检测日期和工序组存在不相同->插入一条汇总数据
                    groupObj.setInspect(null);// 汇总日期的检查日期为空
                    groupObj.setCraftNo(null);
                    groupObj.setDiscardCause(null);
                    groupObj.setDutyProcess(null);
                    resultDis.add(groupObj);
                    resultDis.add(dis);
                    groupObj = new Discard();
                    groupObj.setDiscardArea(dis.getDiscardArea());
                    groupObj.setCraftNo(dis.getCraftNo());
                    groupObj.setDiscardCause(dis.getDiscardCause());
                    groupObj.setDutyProcess(dis.getDutyProcess());
                    groupObj.setDiscardAreaA(dis.getDiscardAreaA());
                    groupObj.setDiscardAreaB(dis.getDiscardAreaB());
                    groupObj.setProcessGroupName(dis.getProcessGroupName());
                    groupObj.setDiscardType(dis.getDiscardType());
                    ins = new Inspect();
                    ins.setCheckDate(dis.getInspect().getCheckDate());
                    groupObj.setInspect(ins);
                    if (num == 0)
                    {
                        groupObj.getInspect().setCheckDate(null);// 汇总日期的检查日期为空
                        groupObj.setCraftNo(null);
                        groupObj.setDiscardCause(null);
                        groupObj.setDutyProcess(null);
                        resultDis.add(groupObj);
                        groupObj = null;
                    }
                    
                }
            }
        }
        qpage.setList(resultDis);
        return qpage;
    }

    public List<Discard> getList(List<Discard> list)
    {
        String recordIds = null;
        for (Discard discard : list)
        {
            if (null != discard.getInspectId())
            {
                if (StringUtils.isNotBlank(recordIds))
                {
                    recordIds = recordIds + "," + discard.getInspectId();
                }
                else
                {
                    recordIds = discard.getInspectId().toString();
                }
            }
        }
        if(StringUtils.isNotBlank(recordIds))
        {
            Discard query = new Discard();
            query.setCompany(UserUtils.getUser().getCompany());
            query.setInspectIds(recordIds);
            List<Discard> newCardSizeList = discardDao.getNewCardSizeListTwo(query);
            if(Collections3.isNotEmpty(newCardSizeList))
            {
                for(Discard dis : list)
                {
                    if(null != dis.getInspect() && dis.getInspect().getType() != 1)
                    {
                        for(Discard newCardSize : newCardSizeList)
                        {
                            if(null != dis.getInspectId() && null != newCardSize.getInspectId() && dis.getInspectId().equals(newCardSize.getInspectId()))
                            {
                                dis.setUnitLength(newCardSize.getUnitLength());
                                dis.setUnitWidth(newCardSize.getUnitWidth());
                                dis.setPnlDivisor(newCardSize.getPnlDivisor());
                                break;
                            }
                        }
                    }
                }
            }
        }
        return list;
    }
}
