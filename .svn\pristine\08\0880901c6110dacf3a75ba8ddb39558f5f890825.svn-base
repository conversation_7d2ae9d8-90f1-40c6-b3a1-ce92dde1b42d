package com.kyb.pcberp.modules.finance.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

import java.math.BigDecimal;
import java.util.Date;

public class CollectBank extends DataEntity<CollectBank>
{
    private String companyId;

    private Date inoutTimeStartQr;

    private Date inoutTimeEndQr;

    private String no;

    private String startDate;

    private String endDate;

    private String status;

    private BigDecimal riskFee;

    private BigDecimal periodFee;

    private String userName;

    private String userId;

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    public Date getInoutTimeStartQr()
    {
        return inoutTimeStartQr;
    }

    public void setInoutTimeStartQr(Date inoutTimeStartQr)
    {
        this.inoutTimeStartQr = inoutTimeStartQr;
    }

    public Date getInoutTimeEndQr()
    {
        return inoutTimeEndQr;
    }

    public void setInoutTimeEndQr(Date inoutTimeEndQr)
    {
        this.inoutTimeEndQr = inoutTimeEndQr;
    }

    public String getNo()
    {
        return no;
    }

    public void setNo(String no)
    {
        this.no = no;
    }

    public String getStartDate()
    {
        return startDate;
    }

    public void setStartDate(String startDate)
    {
        this.startDate = startDate;
    }

    public String getEndDate()
    {
        return endDate;
    }

    public void setEndDate(String endDate)
    {
        this.endDate = endDate;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public BigDecimal getRiskFee()
    {
        return riskFee;
    }

    public void setRiskFee(BigDecimal riskFee)
    {
        this.riskFee = riskFee;
    }

    public BigDecimal getPeriodFee()
    {
        return periodFee;
    }

    public void setPeriodFee(BigDecimal periodFee)
    {
        this.periodFee = periodFee;
    }

    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getUserId()
    {
        return userId;
    }

    public void setUserId(String userId)
    {
        this.userId = userId;
    }
}
