package com.kyb.pcberp.modules.report.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.hr.permission_center.pojo.Hr_Item;
import com.kyb.pcberp.modules.sys.entity.GroupOrgRelation;
import com.kyb.pcberp.modules.sys.entity.User;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/** zjn 2019-07-31 报表对象 */
@SuppressWarnings("serial")
public class Report extends DataEntity<Report>
{
    private String day; // 统计日期
    
    private Customer customer;// 客户
    
    private User salesMan;// 业务员
    
    private BigDecimal amount; // 金额
    
    private BigDecimal area; // 面积
    
    private Integer sums; // 款数
    
    private BigDecimal price; // 单价
    
    private BigDecimal avgPrice; // 平均单价
    
    private String moreResult; // 最多原因
    
    private String maxUnit; // 单位最多数量
    
    private String queryDate; // 时间条件(日、月)
    
    private Integer dateType; // 时间类型(1:日，2：周，3：月)
    
    private String orderBy; // 排序方式
    
    private Integer analyticalNum; // 分析数
    
    private String dateFlag;
    
    private String days; // 日
    
    private String months; // 周
    
    private String weeks; // 月
    
    private Date nearDate; // 最新更新时间
    
    private String sequence; // 序号
    
    private String role; // 角色
    
    private String queryDateStrs;// 分析数据时间组
    
    private String name;
    
    private String reportDate;
    
    private String personId;
    
    private String reportType;
    
    private Integer overDueSum; // 未交货逾期款数
    
    private BigDecimal overDueAmount; // 未交货逾期金额
    
    private String userId; // 用户id

    private String departId;
    
    // erp报表详情条件
    private Integer status; // 报表模块
    
    private List<ReportValue> reportValueList; // 报表值列表
    
    private String typeStr; // 单个属性值
    
    private String sentTimeStartQr; // 时间段查询条件 载体
    
    private String sentTimeEndQr;

    private String deptId;

    private String statusFlag; // 1公司，2部门

    private String phone;

    private List<ReportRole> reportRoleList;

    private String moduleType;

    private String groupOrgId;

    private Integer typeStatus;

    private String dimensionType; // 统计维度

    private String showType; // 显示类型

    private String datetime; //明细日期筛选

    private String departName; //部门名称

    private String craftNo; //厂编

    private String customerModel; //客户型号

    private String customerPro; //客户订单号

    private String changeType;

    private String terminal;

    private String customerName;

    private String paymentTerm;

    private String userName;

    private String factId;

    private String compareDateFlag; // 1最终交期，2首批交期

    private String customerId;

    private String processManagementId; // 瓶颈工序id

    private Integer inoutType; // 出入库方式

    private List<GroupOrgRelation> groupDeptList;

    private String queryType; // 1当月，2在线

    private String mergeQuery; // 合并条件

    private String startTime;

    private String endTime;

    private String empId;

    private String dateQuery;

    private String orderRank;

    public String getDay()
    {
        return day;
    }
    
    public void setDay(String day)
    {
        this.day = day;
    }
    
    public Customer getCustomer()
    {
        return customer;
    }
    
    public void setCustomer(Customer customer)
    {
        this.customer = customer;
    }
    
    public User getSalesMan()
    {
        return salesMan;
    }
    
    public void setSalesMan(User salesMan)
    {
        this.salesMan = salesMan;
    }
    
    public BigDecimal getAmount()
    {
        return amount;
    }
    
    public void setAmount(BigDecimal amount)
    {
        this.amount = amount;
    }
    
    public BigDecimal getArea()
    {
        return area;
    }
    
    public void setArea(BigDecimal area)
    {
        this.area = area;
    }
    
    public Integer getSums()
    {
        return sums;
    }
    
    public void setSums(Integer sums)
    {
        this.sums = sums;
    }
    
    public BigDecimal getPrice()
    {
        return price;
    }
    
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }
    
    public BigDecimal getAvgPrice()
    {
        return avgPrice;
    }
    
    public void setAvgPrice(BigDecimal avgPrice)
    {
        this.avgPrice = avgPrice;
    }
    
    public String getMoreResult()
    {
        return moreResult;
    }
    
    public void setMoreResult(String moreResult)
    {
        this.moreResult = moreResult;
    }
    
    public String getMaxUnit()
    {
        return maxUnit;
    }
    
    public void setMaxUnit(String maxUnit)
    {
        this.maxUnit = maxUnit;
    }
    
    public String getQueryDate()
    {
        return queryDate;
    }
    
    public void setQueryDate(String queryDate)
    {
        this.queryDate = queryDate;
    }
    
    public Integer getDateType()
    {
        return dateType;
    }
    
    public void setDateType(Integer dateType)
    {
        this.dateType = dateType;
    }
    
    public String getOrderBy()
    {
        return orderBy;
    }
    
    public void setOrderBy(String orderBy)
    {
        this.orderBy = orderBy;
    }
    
    public Integer getAnalyticalNum()
    {
        return analyticalNum;
    }
    
    public void setAnalyticalNum(Integer analyticalNum)
    {
        this.analyticalNum = analyticalNum;
    }
    
    public String getDateFlag()
    {
        return dateFlag;
    }
    
    public void setDateFlag(String dateFlag)
    {
        this.dateFlag = dateFlag;
    }
    
    public String getDays()
    {
        return days;
    }
    
    public void setDays(String days)
    {
        this.days = days;
    }
    
    public String getMonths()
    {
        return months;
    }
    
    public void setMonths(String months)
    {
        this.months = months;
    }
    
    public String getWeeks()
    {
        return weeks;
    }
    
    public void setWeeks(String weeks)
    {
        this.weeks = weeks;
    }
    
    public Date getNearDate()
    {
        return nearDate;
    }
    
    public void setNearDate(Date nearDate)
    {
        this.nearDate = nearDate;
    }
    
    public String getNearDateStr()
    {
        if (nearDate != null)
        {
            return DateUtils.formatDateTime(nearDate);
        }
        return null;
    }
    
    public String getSequence()
    {
        return sequence;
    }
    
    public void setSequence(String sequence)
    {
        this.sequence = sequence;
    }
    
    public String getRole()
    {
        return role;
    }
    
    public void setRole(String role)
    {
        this.role = role;
    }
    
    public String getQueryDateStrs()
    {
        return queryDateStrs;
    }
    
    public void setQueryDateStrs(String queryDateStrs)
    {
        this.queryDateStrs = queryDateStrs;
    }
    
    public String getName()
    {
        return name;
    }
    
    public void setName(String name)
    {
        this.name = name;
    }
    
    public String getReportDate()
    {
        return reportDate;
    }
    
    public void setReportDate(String reportDate)
    {
        this.reportDate = reportDate;
    }
    
    public String getPersonId()
    {
        return personId;
    }
    
    public void setPersonId(String personId)
    {
        this.personId = personId;
    }
    
    public String getReportType()
    {
        return reportType;
    }
    
    public void setReportType(String reportType)
    {
        this.reportType = reportType;
    }
    
    public Integer getOverDueSum()
    {
        return overDueSum;
    }
    
    public void setOverDueSum(Integer overDueSum)
    {
        this.overDueSum = overDueSum;
    }
    
    public BigDecimal getOverDueAmount()
    {
        return overDueAmount;
    }
    
    public void setOverDueAmount(BigDecimal overDueAmount)
    {
        this.overDueAmount = overDueAmount;
    }
    
    public String getUserId()
    {
        return userId;
    }
    
    public void setUserId(String userId)
    {
        this.userId = userId;
    }
    
    public Integer getStatus()
    {
        return status;
    }
    
    public void setStatus(Integer status)
    {
        this.status = status;
    }
    
    public List<ReportValue> getReportValueList()
    {
        return reportValueList;
    }
    
    public void setReportValueList(List<ReportValue> reportValueList)
    {
        this.reportValueList = reportValueList;
    }
    
    public String getTypeStr()
    {
        return typeStr;
    }
    
    public void setTypeStr(String typeStr)
    {
        this.typeStr = typeStr;
    }
    
    public String getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }
    
    public void setSentTimeStartQr(String sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }
    
    public String getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }
    
    public void setSentTimeEndQr(String sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public String getStatusFlag() {
        return statusFlag;
    }

    public void setStatusFlag(String statusFlag) {
        this.statusFlag = statusFlag;
    }

    public String getPhone()
    {
        return phone;
    }

    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public List<ReportRole> getReportRoleList()
    {
        return reportRoleList;
    }

    public void setReportRoleList(List<ReportRole> reportRoleList)
    {
        this.reportRoleList = reportRoleList;
    }

    public String getModuleType()
    {
        return moduleType;
    }

    public void setModuleType(String moduleType)
    {
        this.moduleType = moduleType;
    }

    public String getGroupOrgId()
    {
        return groupOrgId;
    }

    public void setGroupOrgId(String groupOrgId)
    {
        this.groupOrgId = groupOrgId;
    }

    public Integer getTypeStatus() {
        return typeStatus;
    }

    public void setTypeStatus(Integer typeStatus) {
        this.typeStatus = typeStatus;
    }

    public String getDimensionType() {
        return dimensionType;
    }

    public void setDimensionType(String dimensionType) {
        this.dimensionType = dimensionType;
    }

    public String getShowType() {
        return showType;
    }

    public void setShowType(String showType) {
        this.showType = showType;
    }

    public String getDatetime() {
        return datetime;
    }

    public void setDatetime(String datetime) {
        this.datetime = datetime;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public String getCraftNo() {
        return craftNo;
    }

    public void setCraftNo(String craftNo) {
        this.craftNo = craftNo;
    }

    public String getCustomerModel() {
        return customerModel;
    }

    public void setCustomerModel(String customerModel) {
        this.customerModel = customerModel;
    }

    public String getCustomerPro() {
        return customerPro;
    }

    public void setCustomerPro(String customerPro) {
        this.customerPro = customerPro;
    }

    public String getChangeType()
    {
        return changeType;
    }

    public void setChangeType(String changeType)
    {
        this.changeType = changeType;
    }

    public String getTerminal() {
        return terminal;
    }

    public void setTerminal(String terminal) {
        this.terminal = terminal;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getPaymentTerm() {
        return paymentTerm;
    }

    public void setPaymentTerm(String paymentTerm) {
        this.paymentTerm = paymentTerm;
    }

    public String getDepartId() {
        return departId;
    }

    public void setDepartId(String departId) {
        this.departId = departId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFactId()
    {
        return factId;
    }

    public void setFactId(String factId)
    {
        this.factId = factId;
    }

    public String getCompareDateFlag()
    {
        return compareDateFlag;
    }

    public void setCompareDateFlag(String compareDateFlag)
    {
        this.compareDateFlag = compareDateFlag;
    }

    public String getCustomerId()
    {
        return customerId;
    }

    public void setCustomerId(String customerId)
    {
        this.customerId = customerId;
    }

    public String getProcessManagementId()
    {
        return processManagementId;
    }

    public void setProcessManagementId(String processManagementId)
    {
        this.processManagementId = processManagementId;
    }

    public Integer getInoutType()
    {
        return inoutType;
    }

    public void setInoutType(Integer inoutType)
    {
        this.inoutType = inoutType;
    }

    public List<GroupOrgRelation> getGroupDeptList()
    {
        return groupDeptList;
    }

    public void setGroupDeptList(List<GroupOrgRelation> groupDeptList)
    {
        this.groupDeptList = groupDeptList;
    }

    public String getQueryType()
    {
        return queryType;
    }

    public void setQueryType(String queryType)
    {
        this.queryType = queryType;
    }

    public String getMergeQuery() {
        return mergeQuery;
    }

    public void setMergeQuery(String mergeQuery) {
        this.mergeQuery = mergeQuery;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public String getDateQuery() {
        return dateQuery;
    }

    public void setDateQuery(String dateQuery) {
        this.dateQuery = dateQuery;
    }

    public String getOrderRank() {
        return orderRank;
    }

    public void setOrderRank(String orderRank) {
        this.orderRank = orderRank;
    }
}
