package com.kyb.pcberp.modules.wechat.dao;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.DayThingFiles;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.KybDayThing;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatAttachments;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatComment;

import java.util.List;

@MyBatisDao
public interface KybDayThingDao
{
    KybDayThing getKybDayThing(KybDayThing dayThing);
    
    List<WechatComment> getDayThingComment(KybDayThing dayThing);
    
    List<WechatAttachments> getAttachmments(KybDayThing dayThing);
    
    List<KybDayThing> getDayThingList(KybDayThing dayThing);
    
    void updateDayThing(KybDayThing dayThing);
    
    void addDayThing(KybDayThing dayThing);
    
    void delDayThing(KybDayThing dayThing);
    
    void uploadFile(DayThingFiles dayThingFile);
    
    void delFile(WechatAttachments attach);
    
    List<KybDayThing> getTimeMsg();
}
