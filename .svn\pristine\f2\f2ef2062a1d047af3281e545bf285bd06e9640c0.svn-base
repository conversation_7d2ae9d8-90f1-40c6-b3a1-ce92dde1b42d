package com.kyb.pcberp.modules.contract.entity;

import java.math.BigDecimal;
import java.util.List;

import com.kyb.pcberp.common.persistence.DataEntity;

public class GroupQuote extends DataEntity<GroupQuote>
{
    private static final long serialVersionUID = 1L;
    
    private String companyId;
    
    private String item;
    
    private String itemName;
    
    private BigDecimal value;
    
    private int unit;
    
    private int useFlag;
    
    private List<GroupQuoteList> list;
    
    private String listStatus;
    
    public String getCompanyId()
    {
        return companyId;
    }
    
    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }
    
    public String getItem()
    {
        return item;
    }
    
    public void setItem(String item)
    {
        this.item = item;
    }
    
    public String getItemName()
    {
        return itemName;
    }
    
    public void setItemName(String itemName)
    {
        this.itemName = itemName;
    }
    
    public BigDecimal getValue()
    {
        return value;
    }
    
    public void setValue(BigDecimal value)
    {
        this.value = value;
    }
    
    public int getUnit()
    {
        return unit;
    }
    
    public void setUnit(int unit)
    {
        this.unit = unit;
    }
    
    public int getUseFlag()
    {
        return useFlag;
    }
    
    public void setUseFlag(int useFlag)
    {
        this.useFlag = useFlag;
    }
    
    public String getListStatus()
    {
        return listStatus;
    }
    
    public void setListStatus(String listStatus)
    {
        this.listStatus = listStatus;
    }
    
    public List<GroupQuoteList> getList()
    {
        return list;
    }
    
    public void setList(List<GroupQuoteList> list)
    {
        this.list = list;
    }
}
