package com.kyb.pcberp.modules.wechat.pojo.wechatUser;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;

import java.text.ParseException;
import java.util.Date;

public class WechatAuditRest extends DataEntity<WechatAuditRest>
{
    private static final long serialVersionUID = 1L;
    
    private String restGroupId;
    
    private String workStartDate;
    
    private String workEndDate;
    
    private String restStartDate;
    
    private String restEndDate;
    
    private String createBy;
    
    public String getRestGroupId()
    {
        return restGroupId;
    }
    
    public void setRestGroupId(String restGroupId)
    {
        this.restGroupId = restGroupId;
    }
    
    public String getWorkStartDate()
    {
        return workStartDate;
    }
    
    public void setWorkStartDate(String workStartDate)
    {
        this.workStartDate = workStartDate;
    }
    
    public String getWorkEndDate()
    {
        return workEndDate;
    }
    
    public void setWorkEndDate(String workEndDate)
    {
        this.workEndDate = workEndDate;
    }
    
    public String getRestStartDate()
    {
        return restStartDate;
    }
    
    public void setRestStartDate(String restStartDate)
    {
        this.restStartDate = restStartDate;
    }
    
    public String getRestEndDate()
    {
        return restEndDate;
    }
    
    public void setRestEndDate(String restEndDate)
    {
        this.restEndDate = restEndDate;
    }
    
    public Date getWorkStartDateStr()
    {
        if (workStartDate != null)
        {
            try
            {
                return DateUtils.parseDate(workStartDate, "yyyy-MM-dd HH:mm:ss");
            }
            catch (ParseException e)
            {
                return null;
            }
        }
        return null;
    }
    
    public Date getworkEndDateStr()
    {
        if (workEndDate != null)
        {
            try
            {
                return DateUtils.parseDate(workEndDate, "yyyy-MM-dd HH:mm:ss");
            }
            catch (ParseException e)
            {
                return null;
            }
        }
        return null;
    }
    
    public Date getRestStartDateStr()
    {
        if (restStartDate != null)
        {
            try
            {
                return DateUtils.parseDate(restStartDate, "yyyy-MM-dd HH:mm:ss");
            }
            catch (ParseException e)
            {
                return null;
            }
        }
        return null;
    }
    
    public Date getRestEndDateStr()
    {
        if (restEndDate != null)
        {
            try
            {
                return DateUtils.parseDate(restEndDate, "yyyy-MM-dd HH:mm:ss");
            }
            catch (ParseException e)
            {
                return null;
            }
        }
        return null;
    }
    
    public String getCreateBy()
    {
        return createBy;
    }
    
    public void setCreateBy(String createBy)
    {
        this.createBy = createBy;
    }
}
