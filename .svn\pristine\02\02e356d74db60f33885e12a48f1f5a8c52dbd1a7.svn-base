package com.kyb.pcberp.modules.stock.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.MaterialQuotationRecord;
import com.kyb.pcberp.modules.stock.entity.MaterialQuotationVersion;

@MyBatisDao
public interface MaterialQuotationDao
{
    
    List<MaterialQuotationVersion> versionList(@Param("companyId") String companyId);
    
    Integer createVersion(@Param("version") MaterialQuotationVersion version);
    
    void updateRecordList(@Param("list") List<MaterialQuotationRecord> list);
    
    void insertRecordList(@Param("list") List<MaterialQuotationRecord> list);
    
    List<Material> findProductList(Material material);
    
    List<MaterialQuotationRecord> findByMaterialQuotationRecord(MaterialQuotationRecord record);

    Integer getQuotationStatus(MaterialQuotationVersion materialQuotationVersion);

    List<MaterialQuotationRecord> getQuotationRecordList(MaterialQuotationRecord materialQuotationRecord);

    MaterialQuotationVersion getMaterialQuotationVersion(MaterialQuotationVersion materialQuotationVersion);

    void updateMaterialQuotationVersion(MaterialQuotationVersion materialQuotationVersion);

    List<MaterialQuotationRecord> getMaterialQuotationRecordList(MaterialQuotationRecord materialQuotationRecord);

    List<MaterialQuotationRecord> getPrevQuotationRecordList(@Param("versionId") String versionId,@Param("materialId") String materialId);

    Integer getQuotationStatusPass(MaterialQuotationVersion materialQuotationVersion);
}
