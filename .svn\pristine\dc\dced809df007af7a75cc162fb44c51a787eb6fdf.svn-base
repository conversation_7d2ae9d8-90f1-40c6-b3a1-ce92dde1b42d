package com.kyb.pcberp.modules.stock.entity;

import java.math.BigDecimal;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.contract.entity.MaterialContractDetail;

@SuppressWarnings("serial")
public class MatDeliveryDetail extends DataEntity<MatDeliveryDetail>
{
    private String companyId;
    
    private String deliveryId;
    
    private MatDelivery delivery;
    
    private String materialId;
    
    private Material material;
    
    private String contractDetailId;
    
    private MaterialContractDetail contractDetail;
    
    private Integer quantity;
    
    private Integer spares;
    
    private String status;
    
    private String complaintId;
    
    private String oldPurWareId; // 采购入库记录ID
    
    private String contractNo;
    
    private BigDecimal amount;
    
    private String checkOrgId;

    private BigDecimal serviceFee;
    
    public String getCompanyId()
    {
        return companyId;
    }
    
    public String getDeliveryId()
    {
        return deliveryId;
    }
    
    public MatDelivery getDelivery()
    {
        return delivery;
    }
    
    public String getMaterialId()
    {
        return materialId;
    }
    
    public Material getMaterial()
    {
        return material;
    }
    
    public String getContractDetailId()
    {
        return contractDetailId;
    }
    
    public MaterialContractDetail getContractDetail()
    {
        return contractDetail;
    }
    
    public Integer getQuantity()
    {
        return quantity;
    }
    
    public Integer getSpares()
    {
        return spares;
    }
    
    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getComplaintId()
    {
        return complaintId;
    }
    
    public String getOldPurWareId()
    {
        return oldPurWareId;
    }
    
    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }
    
    public void setDeliveryId(String deliveryId)
    {
        this.deliveryId = deliveryId;
    }
    
    public void setDelivery(MatDelivery delivery)
    {
        this.delivery = delivery;
    }
    
    public void setMaterialId(String materialId)
    {
        this.materialId = materialId;
    }
    
    public void setMaterial(Material material)
    {
        this.material = material;
    }
    
    public void setContractDetailId(String contractDetailId)
    {
        this.contractDetailId = contractDetailId;
    }
    
    public void setContractDetail(MaterialContractDetail contractDetail)
    {
        this.contractDetail = contractDetail;
    }
    
    public void setQuantity(Integer quantity)
    {
        this.quantity = quantity;
    }
    
    public void setSpares(Integer spares)
    {
        this.spares = spares;
    }
    
    public void setComplaintId(String complaintId)
    {
        this.complaintId = complaintId;
    }
    
    public void setOldPurWareId(String oldPurWareId)
    {
        this.oldPurWareId = oldPurWareId;
    }

    public String getContractNo()
    {
        return contractNo;
    }

    public void setContractNo(String contractNo)
    {
        this.contractNo = contractNo;
    }

    public BigDecimal getAmount()
    {
        return amount;
    }

    public void setAmount(BigDecimal amount)
    {
        this.amount = amount;
    }

    public String getCheckOrgId()
    {
        return checkOrgId;
    }

    public void setCheckOrgId(String checkOrgId)
    {
        this.checkOrgId = checkOrgId;
    }

    public BigDecimal getServiceFee() {
        return serviceFee;
    }

    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }
}
