/**
 * 
 */
package com.kyb.pcberp.modules.purch.web;

import java.util.Calendar;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.CommonUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.purch.entity.PuReturns;
import com.kyb.pcberp.modules.purch.service.MaterialRejectService;
import com.kyb.pcberp.modules.stock.entity.MaterialReject;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

/**
 * 退货管理Controller
 * 
 * <AUTHOR>
 * @version 2015-09-17
 */
@Controller
@RequestMapping(value = "${adminPath}/purch/materialReject")
public class MaterialRejectController extends BaseController
{
    
    @Autowired
    private MaterialRejectService materialRejectService;
    
    /**
     * 定位页面
     * 
     * @param purchasing
     * @return
     */
    @RequiresPermissions("purch:returns:view")
    @RequestMapping(value = "list")
    public String list(PuReturns puReturns)
    {
        return "modules/purch/materialReject";
    }
    
    /**
     * 分页查询
     * 
     * @param notification
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("purch:returns:view")
    @RequestMapping(value = {"page"})
    @ResponseBody
    public Page<MaterialReject> getlist(@RequestBody MaterialReject rej, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        // 设置查询企业编号
        if (rej != null)
        {
            rej.setCompany(user.getCompany());
        }
        
        // 分页查询数据
        Page<MaterialReject> qpage = new Page<MaterialReject>(request, response);
        if (StringUtils.isNotBlank(rej.getPageNo()) && StringUtils.isNotBlank(rej.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(rej.getPageNo()));
            qpage.setPageSize(Integer.parseInt(rej.getPageSize()));
            qpage.setOrderBy("no desc");
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        // 设置查询范围
        if (rej.getQueryAll() != null && !rej.getQueryAll())
        {
            rej.setCreatedBy(user);
        }
        // 设置排序
        if (StringUtils.isNotBlank(rej.getOrderBy()))
        {
            qpage.setOrderBy(rej.getOrderBy());
        }
        if (rej.getSentTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rej.getSentTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            rej.setSentTimeEndQr(deliTime.getTime());
        }
        if (rej.getSentTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rej.getSentTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            rej.setSentTimeStartQr(deliTime.getTime());
        }
        
        Page<MaterialReject> page = materialRejectService.findPage(qpage, rej);
        
        return page;
    }
    
    /**
     * 获取采购订单编号
     * 
     * @return
     */
    @RequiresPermissions("purch:returns:view")
    @RequestMapping(value = "getNo", method = {RequestMethod.POST})
    @ResponseBody
    public String getNo()
    {
        return CommonUtils.geDocumentNo(CommonEnums.CodeType.RETURNS.getIndex().toString());
    }
    
    @RequestMapping(value = "update")
    @ResponseBody
    public String update(@RequestBody MaterialReject mr)
    {
        if(mr.getApplyDate()!=null&&mr.getApplyDate().compareTo(new Date())>0)
        {
            return "申请日期不能大于当前时间！";
        }
        materialRejectService.save(mr);
        return "success";
    }
    
    /**
     * 删除
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "delete", method = {RequestMethod.POST})
    @ResponseBody
    public String delete(@RequestBody MaterialReject mr)
    {
        MaterialReject old = materialRejectService.get(mr);
        if (TypeKey.SL_CONTRACT_STATUS_DRAFT.toString().equals(old.getStatus()))
        {
            materialRejectService.delete(mr);
            return "删除成功！";
        }
        else
        {
            return "删除失败，客诉单状态已变更，请刷新后重试！";
        }
        
    }
    
    @RequestMapping(value = "audit")
    @ResponseBody
    public String audit(@RequestBody MaterialReject mr)
    {
        return materialRejectService.audit(mr);
    }
    
}