<%@ page contentType="text/html;charset=UTF-8"%>
<div ng-intro-options="fedCtrl.introListOptions"
	ng-intro-method="fedCtrl.helpList"
	ng-intro-autostart="fedCtrl.shouldAutoStart"></div>
<div ng-intro-options="fedCtrl.introDetailOptions"
	ng-intro-method="fedCtrl.helpDetail"
	ng-intro-autostart="fedCtrl.shouldAutoStart"></div>
<div ng-intro-options="fedCtrl.introinfoOptions"
	ng-intro-method="fedCtrl.helpinfo"
	ng-intro-autostart="fedCtrl.shouldAutoStart"></div>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
	<ul class="page-breadcrumb">
		<li><i class="fa fa-home"></i> <a href="#/dashboard.html">主页</a>
			<i class="fa fa-angle-right"></i></li>
		<li><a href="javascript:;">生产管理</a> <i class="fa fa-angle-right"></i></i>
		</li>
		<li><a ui-sref="production.fed">生产补料</a></li>
	</ul>

	<div class="page-toolbar">
		<button class="btn btn-fit-height default pull-right"
			ng-click="fedCtrl.help()">
			<i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助
		</button>
	</div>
</div>

<!-- END 导航-->
<tabset class="tabset-margin-top"> <!-- BEGIN 公告列表 --> <tab
	heading="补料单列表" active="fedCtrl.tabs.viewForm.active">
<div class="rows">
	<div class="panel panel-default" id="step6">
		<div class="panel-heading font-blue-hoki">查询</div>
		<div class="panel-body">
			<form class="form-horizontal">
				<div class="row">
					<div class="col-md-6 col-lg-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">补料单编号：</label>
							<div class="col-sm-7 col-md-8">
								<input type="text" class="form-control"
									ng-model="fedCtrl.query.userCode.value"
									disable-valid-styling="true" disable-invalid-styling="true" />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">生产编号：</label>
							<div class="col-sm-7 col-md-8">
								<input type="text" class="form-control"
									ng-model="fedCtrl.query.craftNo.value"
									disable-valid-styling="true" disable-invalid-styling="true" />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">状态：</label>

							<div class="col-sm-7 col-md-8">
								<select class="form-control"
									ng-model="fedCtrl.query.genSelected"
									ng-options="gen.value as gen.name for gen in fedCtrl.query.genList"
									disable-auto-validate="true">
								</select>
							</div>
						</div>
					</div>

					<div class="col-md-6 col-lg-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">是否使用余料：</label>

							<div class="col-sm-7 col-md-8">
								<select class="form-control"
									ng-model="fedCtrl.query.statusListed"
									ng-options="gen.value as gen.name for gen in fedCtrl.query.statusListeded"
									disable-auto-validate="true">
								</select>
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4">
	                  <div class="form-group">
                         <label class="col-sm-3 col-md-4 control-label">补料日期：</label>
                              <div class="col-sm-7 col-md-8">
                                <div class="input-prepend input-group">
									<span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
									<input type="text" class="form-control" disable-auto-validate="true"
										ng-blur="fedCtrl.initDate(fedCtrl.time)"
										kyb-daterange 
										kyb-daterange-options="fedCtrl.rangeOptions"
										ng-model="fedCtrl.time" 
										placeholder="请选择时间段">
								</div>
                            </div>
	                   </div>       
                    </div>

				</div>



				<div class="row">
					<div class="col-sm-12">
						<button class="btn btn-default btn-default-width pull-right"
							ng-click="fedCtrl.doQuery()">
							<i class="fa fa-search"></i> 查&nbsp;询
						</button>
					</div>
				</div>
			</form>
		</div>
	</div>
	<div class="portlet light bordered">
		<div class="portlet-title">
			<div class="caption font-blue-hoki">补料单列表</div>
			<div class="actions" id="step7">
<!-- 				<div class="portlet-input input-inline input-small">
					<button ng-if="fedCtrl.right.edit" type="button"
						class="btn green btn-default-width"
						ng-click="fedCtrl.addReplenish()">
						<i class="fa fa-plus"></i> 添加补料单
					</button>
				</div>
				<div class="portlet-input input-inline input-small">
					<button ng-if="fedCtrl.right.edit" type="button"
						class="btn btn-primary btn-default-width"
						ng-click="fedCtrl.receReplenish()">
						<i class="fa fa-recycle"></i> 客诉补料
					</button>
				</div> -->
				<div class="portlet-input input-inline input-small">
                       <form action="a/production/feed/exports" method="get" enctype="multipart/form-data" target="hidden_frame">
                           <input type="text" ng-show="false" name="no" value="{{fedCtrl.query.userCode.value}}" /> 
                           <input type="text" ng-show="false" name="notification.craftNo" value="{{fedCtrl.query.craftNo.value}}" /> 
                           <input type="text" ng-show="false" name="status" value="{{fedCtrl.query.genSelected}}" /> 
                           <input type="text" ng-show="false" name="changeStatus" value="{{fedCtrl.query.statusListed}}" /> 
                           <input type="text" ng-show="false" name="sentTimeStartQr" value="{{fedCtrl.query.sentTimeStartQr.value}}"/>
                           <input type="text" ng-show="false" name="sentTimeEndQr" value="{{fedCtrl.query.sentTimeEndQr.value}}"/>
                           <input type="text" ng-show="false"  name="queryAll" value="{{fedCtrl.queryAll}}"/>
                           <input type="text" type="text" ng-show="false" name="orderBy" value="{{fedCtrl.query.sort.value}}" />
                           <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出补料记录</button>
                           <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
                       </form>
                 </div>
			</div>
		</div>
		<div class="portlet-body">
			<div class="table-scrollable" style="margin-top: 0px !important"
				id="step1">
				<table id="contentTable"
					class="table table-striped table-bordered table-condensed table-advance table-hover">
					<thead>
						<tr class="heading" id="step2">
							<th width="20%" id="step3">操&nbsp;作&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
							<th width="10%"
								ng-class="{'sorting': fedCtrl.sort.no.both, 'sorting_desc': fedCtrl.sort.no.desc, 'sorting_asc': fedCtrl.sort.no.asc}"
								ng-click="fedCtrl.sortClick('no')">补料单号&nbsp;&nbsp;&nbsp;&nbsp;</th>
							<th>单据类型</th>
							<th
								ng-class="{'sorting': fedCtrl.sort.status.both, 'sorting_desc': fedCtrl.sort.status.desc, 'sorting_asc': fedCtrl.sort.status.asc}"
								ng-click="fedCtrl.sortClick('status')">状态&nbsp;&nbsp;&nbsp;&nbsp;</th>
							<th>使用余料</th>

							<th width="15%"
								ng-class="{'sorting': fedCtrl.sort.notificationNo.both, 'sorting_desc': fedCtrl.sort.notificationNo.desc, 'sorting_asc': fedCtrl.sort.notificationNo.asc}"
								ng-click="fedCtrl.sortClick('notificationNo')">通知单号&nbsp;&nbsp;&nbsp;&nbsp;</th>
							<th width="10%"
								ng-class="{'sorting': fedCtrl.sort.createdDate.both, 'sorting_desc': fedCtrl.sort.createdDate.desc, 'sorting_asc': fedCtrl.sort.createdDate.asc}"
								ng-click="fedCtrl.sortClick('createdDate')">补料日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
							<th>操作员</th>
							<th>补充数量</th>
							<th>真实预投率</th>
							<th>流程卡张数</th>
							<th>占可用库存数量</th>
							<th>报废原因&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
							<th>生产编号</th>

							<th>复投时间&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
							<th>备&nbsp;注</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-repeat="row in fedCtrl.page.data.list"
							ng-dblclick="fedCtrl.showModofSeeByReplenishId($index)">
							<td>
								
								<a href="javascript:void(0)" class="btn btn-xs btn-default" title="取消补料单"
								ng-if="fedCtrl.right.edit && row.rep == false && row.status === '700402'"
								 ng-click="fedCtrl.showCancelfeding($index)"><i
									class="fa fa-mail-reply"></i>取&nbsp;消</a> 
							
							
								<!--  <a href="javascript:void(0)" ng-if="fedCtrl.right.edit && row.status === '700401'"
                                   ng-click="fedCtrl.modReplenish($index)">修&nbsp;改</a>-->
								<a href="javascript:void(0)" class="btn btn-xs btn-default"
								ng-if="fedCtrl.right.edit && row.status === '700401'"
								title="删除补料单" ng-click="fedCtrl.delReplenish($index)"><i
									class="fa fa-times font-red"></i>删&nbsp;除</a> 
								<a href="javascript:void(0)" class="btn btn-xs btn-default"
								 ng-if="fedCtrl.right.edit && row.status !== '700402' && row.status !== '700405'&&row.status !== '700403'"
								ng-click="fedCtrl.affirmReplenish($index)"><i class="fa fa-check font-green" title="确认补料单"></i> 确&nbsp;认</a>
								<a href="javascript:void(0)" class="btn btn-xs btn-default"
								 ng-if="fedCtrl.right.edit && row.status == '700402' && !row.rep"
								ng-click="fedCtrl.cancleReplenishOpen($index)"><i class="fa fa-check font-green" title="取消确认补料单"></i>取消确认</a>
								<form class="inline" ng-form-commit name="printForm"
									action="a/production/feed/print" method="get"
									target="hidden_frame">
									<input type="text" id="recordId" ng-show="false"
										name="recordId" ng-model="row.recordId" /> <a
										href="javascript:void(0)" class="btn btn-xs btn-default"
										ng-click="fedCtrl.printReplenish(printForm)" title="打印补料单"><i
										class="fa fa-print font-blue-hoki"></i> 打&nbsp;印</a>
									<iframe name="hidden_frame" id="hidden_frame"
										style="display: none"></iframe>
								</form> <a title="打印流程卡" href="javascript:void(0)"
								class="btn btn-xs btn-default"
								ng-click="fedCtrl.download(row.recordId)"><i
									class="fa fa-print font-blue-hoki"></i> 打印流程卡</a> <!-- 		<a
								href="javascript:void(0)" class="btn btn-xs btn-default"
								ng-if="fedCtrl.right.edit && row.status !== '700401'  && row.status !== '700405'"
								ng-click="fedCtrl.starvedFeeding(row.recordId)"><i
									class="fa fa-codepen"></i> 缺&nbsp;料</a> -->
								<a title="交期调整" href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="fedCtrl.right.edit && row.status !== '700401' && row.status !== '700403'" ng-click="fedCtrl.deliveryAdjustOpen($index)"> <i class="fa fa-check font-green"></i>交期调整</a>

							</td>
							<td><a ng-bind="row.no" href="javascript:void(0)"
								ng-click="fedCtrl.showModofSeeByReplenishId($index)"></a></td>
							<td ng-if="row.receiptsType == 1 "><span
								class="label label-sm label-default">正常补料</span></td>
							<td ng-if="row.receiptsType == 2 "><span
								class="label label-sm label-info">客诉补料</span></td>

							<td ng-if="row.status === '700401'"><span
								class="label label-sm label-default">未确认</span></td>
							<td ng-if="row.status === '700402'"><span
								class="label label-sm label-success">已确认 </span></td>
							<td ng-if="row.status === '700403'"><span
								class="label label-sm label-primary">已取消 </span></td>
							<td ng-if="row.status === '700405'"><span
								class="label label-sm label-info">缺料 </span></td>
							<td ng-if="row.status === '700406'"><span
								class="label label-sm label-danger">暂停 </span></td>
							<td ng-if="row.changeStatus === 1"><span
								class="label label-sm label-primary">否 </span></td>
							<td ng-if="row.changeStatus === 2"><span
								class="label label-sm label-success">是 </span></td>


							<td ng-bind="row.notification.no"></td>
							<td ng-bind="row.createdDate"></td>
							<td ng-bind="row.createdBy.userName"></td>
							<td ng-bind="row.discardQty | currency:'':0" style="text-align: right;"></td>
							<td ng-bind="row.percent | number : 2"></td>
							<td ng-bind="row.produceBatchCount"></td>
							<td ng-bind="row.occupiedAvailableQty | number : 0"></td>
							<td ng-bind="row.discardReason"></td>
							<td ng-bind="row.notification.craftNo"></td>
							<td ng-bind="row.replenishDate"></td>
							<td ng-bind="row.remark"></td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="row page-margin-top">
				<div class="col-md-12 col-lg-6" id="step5">
					<span class="inline">每页</span> <select class="form-control inline"
						style="margin-top: 8px; width: 100px;"
						disable-valid-styling="true" disable-invalid-styling="true"
						ng-model="fedCtrl.page.pageSize"
						ng-change="fedCtrl.pageSizeChange()"
						ng-options="pageSizeOption for pageSizeOption in fedCtrl.page.pageSizeOptions">
					</select> <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示
						{{fedCtrl.page.data.startfeed}} / {{fedCtrl.page.data.endfeed}}
						条，共 {{fedCtrl.page.data.count}} 条</span>
				</div>
				<div class="col-md-12 col-lg-6" id="step4">
					<paging class="pull-right" page="fedCtrl.page.data.pageNo"
						page-size="fedCtrl.page.data.pageSize"
						total="fedCtrl.page.data.count" adjacent="1" dots="..."
						scroll-top="false" hide-if-empty="false" ul-class="pagination"
						active-class="active" disabled-class="disabled"
						show-prev-next="true"
						paging-action="fedCtrl.doPage(page, pageSize, total)"></paging>
				</div>
			</div>
		</div>
	</div>
</div>
</tab> <tab active="fedCtrl.tabs.editForm.active"
	ng-show="fedCtrl.tabs.editForm.show"> <tab-heading>
补料单详情 <i style="cursor: pointer" class="fa fa-times"
	ng-click="fedCtrl.hideEditForm()"></i></tab-heading>
<div class="rows">
	<div class="portlet light bordered">
		<div class="portlet-title">
			<div class="caption font-blue-hoki">补料单{{fedCtrl.editTitle}}</div>
		</div>
		<div class="portlet-body" id="stepDetail1">
			<div class="alert alert-warning"
				ng-if="fedCtrl.complaint.hadComplaint">
				<strong>您好，您当前选择的生产编号存在客诉!</strong><br /> 客诉原因：<span
					ng-bind-html="fedCtrl.complaint.info"></span>
			</div>
			<form class="form-horizontal" name="feeding_form"
				ng-init="fedCtrl.setFormScope(this)" novalidate="novalidate"
				ng-submit="fedCtrl.submitMod(feeding_form);" ng-submit-force="true">
				<div class="row">
					<div class="col-md-6 col-lg-4" id="stepDetail2">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label"><span
								class="required">*</span>通知单号:</label>
							<div class="col-sm-7 col-md-8">
								<ui-select theme="bootstrap" firstfocus="{{fedCtrl.focus.main}}"
									ng-disabled="fedCtrl.showAndreadyAs || !fedCtrl.right.edit" 
									register-custom-form-control ng-model="fedCtrl.as" required
									on-select="fedCtrl.selectNotifications($select.selected.recordId)">
								<ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
								<ui-select-choices refresh="fedCtrl.seachFed($select.search)"
									refresh-delay="1000"
									repeat="item.recordId as item in fedCtrl.notification"
									infinite-scroll='fedCtrl.addMoreItems()'
									infinite-scroll-distance='3'>
								<small> 
								<span>通知单编号：&nbsp;&nbsp;&nbsp;&nbsp;{{item.no}}<br></span>
								<span style="color: blue;">生产编号：&nbsp;&nbsp;&nbsp;&nbsp;{{item.craftNo}}<br></span>
								<span style="color: red;">客户型号：&nbsp;&nbsp;&nbsp;&nbsp;{{item.customerModel}}<br>
								</small>
								</ui-select-choices> </ui-select>
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label ">补料单号:</label>
							<div class="col-sm-7 col-md-8">
								<input type="text" class="form-control"
									ng-model="fedCtrl.feed.no" readonly="readonly" />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label"><span
								class="required">*</span> 申请日期:</label>
							<div class="col-sm-7 col-md-8">
								<input type="text" class="form-control"
									ng-blur="fedCtrl.snameBlur()"
									ng-model="fedCtrl.feed.distributeDate" readonly="readonly"
									required />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">生产编号:</label>
							<div class="col-sm-7 col-md-8">
								<input readonly="readonly" type="text" class="form-control"
									ng-model="fedCtrl.bizPerson" />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">交货数量:</label>
							<div class="col-sm-7 col-md-8">
								<input readonly="readonly" type="text" class="form-control"
									ng-model="fedCtrl.boardAPnlQty"
									ng-disabled="!fedCtrl.right.edit" ng-onlynumber />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label"><span
								class="required">*</span>操作员:</label>
							<div class="col-sm-7 col-md-8">
								<!-- 	<input type="text" class="form-control"
									ng-model="fedCtrl.loginUser.userName" Readonly />
								 -->
								<ui-select required ng-model="fedCtrl.feed.doApplicant"
									theme="bootstrap" ng-disabled="!fedCtrl.right.edit">
								<ui-select-match placeholder="请选择...">{{$select.selected.userName}}</ui-select-match>
								<ui-select-choices
									repeat="item.recordId as item in fedCtrl.refer.userList | filter: $select.search">
								<div ng-bind-html="item.userName | highlight: $select.search"></div>
								</ui-select-choices> </ui-select>
							</div>
						</div>
					</div>

					<div class="col-md-6 col-lg-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">报废数量:</label>
							<div class="col-sm-7 col-md-8">
								<input readonly="readonly" class="form-control"
									ng-model="fedCtrl.failedPcsQty" />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">需要补料数量:</label>
							<div class="col-sm-7 col-md-8">
								<input readonly="readonly" class="form-control"
									ng-model="fedCtrl.haveFedQty" />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">尾数:</label>
							<div class="col-sm-7 col-md-8">
								<input readonly="readonly" class="form-control"
									ng-model="fedCtrl.feed.occupyMantissa" />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4" id="stepDetail3">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label"><span
								class="required">*</span>补料数量:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control" ng-model="fedCtrl.feed.discardQty"
									disabled="disabled" required ng-onlynumberandzero />
							</div>
						</div>
					</div>

					<div class="col-md-6 col-lg-4" >
						<div class="form-group" id="stepDetail5">
							<label class="col-sm-3 col-md-4 control-label"><span
								class="required">*</span>流程卡张数:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control"
									ng-change="fedCtrl.clickNgblurCount(fedCtrl.feed.produceBatchCount)"
									ng-model="fedCtrl.feed.produceBatchCount" required ng-onlynumber />
							</div>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-md-6 col-lg-4" >
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label"><span
								class="required">*</span>A板数:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control" ng-model="fedCtrl.feed.qtyPnlA"
									ng-onlynumberandzero required ng-blur="fedCtrl.clickNgblurA(fedCtrl.feed.qtyPnlA)" />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4" >
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label"><span
								class="required">*</span>B板数:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control" ng-model="fedCtrl.feed.qtyPnlB"
									ng-onlynumberandzero required ng-blur="fedCtrl.clickNgblurB(fedCtrl.feed.qtyPnlB)" />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4" >
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label"><span
								class="required">*</span>大板数:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control" ng-model="fedCtrl.feed.qtyPnlT"
									ng-disabled="fedCtrl.editofshowqtyPnlT" required
									ng-onlynumberandzero />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4"
						ng-if="!fedCtrl.ishe && fedCtrl.showOaQty">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">可用库存数量:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control"
									ng-model="fedCtrl.feed.material.availableQty"
									ng-disabled="true" />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4"
						ng-if="!fedCtrl.ishe && fedCtrl.showOaQty">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">占可用库存数量:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control"  placeholder="请输入需要占用可用库存的数量。" 
									ng-model="fedCtrl.feed.occupiedAvailableQty"
									ng-disabled="fedingCtrl.contractFlag"
									ng-blur="fedCtrl.validateOaQty()" ng-onlynumberandzero />
							</div>
						</div>
					</div>
					<div class="col-md-12 col-lg-8" ng-if="fedCtrl.ishe || fedCtrl.isheNoShow">
						<div class="form-group">
							<label class="col-sm-3 col-md-2 control-label">占可用库存数量:</label>
							<div class="col-sm-7 col-md-10">
								<input class="form-control" placeholder="点击文本框,在弹出框中分配占库存的可用数量。" 
									ng-model="fedCtrl.feed.occupiedAvailableQty"
									ng-disabled="fedingCtrl.heDisabled || fedingCtrl.contractFlag"
									ng-focus="fedCtrl.breakDownHeQty()" ng-onlynumberandzero />
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12 col-lg-8">
						<div class="form-group">
							<label class="col-sm-3 col-md-2 control-label "><span
								class="required">*</span>报废原因:</label>
							<div class="col-sm-7 col-md-10">
								<textarea class="form-control" ng-disabled="!fedCtrl.right.edit"
									ng-model="fedCtrl.feed.discardReason" ng-maxlength="255"
									required></textarea>
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4"
						ng-if="fedCtrl.feed.changeStatus == 2">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">选择方案:</label>
							<div class="col-sm-7 col-md-8">

								<button type="button" ng-click="fedCtrl.showIsyu()"
									class="btn btn-primary">使用余料方案</button>

							</div>
						</div>
					</div>


				</div>
				<div class="row">
					<div class="col-md-12 col-lg-8">
						<div class="form-group">
							<label class="col-sm-3 col-md-2 control-label ">备注:</label>
							<div class="col-sm-7 col-md-10">
								<textarea class="form-control" ng-disabled="!fedCtrl.right.edit"
									ng-model="fedCtrl.feed.remark" ng-maxlength="255"></textarea>
							</div>
						</div>
					</div>
				</div>
				
				<div class="row">
					<div class="col-md-18 col-lg-12">
						<div class="form-group">
						<label class="col-sm-3 col-md-1 control-label">基础信息:</label>
							<div class="col-sm-7 col-md-11">
								<span class="control-label" ng-if="fedCtrl.feed.unitLength">
									工程PCS尺寸：{{fedCtrl.feed.unitLength}}&nbsp;*&nbsp;{{fedCtrl.feed.unitWidth}}&emsp;&emsp;&emsp;
									工程SET尺寸：{{fedCtrl.feed.setWidth}}&nbsp;*&nbsp;{{fedCtrl.feed.setLength}}&nbsp;/&nbsp;{{fedCtrl.feed.pnlDivisor}}&emsp;&emsp;&emsp;
									油墨类型：{{fedCtrl.boardCut.inkTypeVal}}&emsp;&emsp;&emsp;
									覆铜板材：{{fedCtrl.boardCut.materialTypeValue}}&emsp;&emsp;&emsp;
									PCB类型：{{fedCtrl.boardCut.boardLevelValue}}&emsp;&emsp;&emsp;
									板材厚度：{{fedCtrl.boardCut.boardThicknessValue}}&emsp;&emsp;&emsp;
									覆铜要求：{{fedCtrl.boardCut.copperCladThicknessValue}}&emsp;&emsp;&emsp;
									镀层处理：{{fedCtrl.boardCut.surfaceProcessValue}}&emsp;&emsp;&emsp;
									阻焊类型：{{fedCtrl.boardCut.solderMaskTypeValue}}&emsp;&emsp;&emsp;
									加急状态：{{fedCtrl.boardCut.urgentName}}<br>
									特别说明：{{fedCtrl.feed.notifiRemark}}
								</span>
							</div>
						</div>
					</div>
				</div>
				
				<div class="row" ng-show="fedCtrl.showDetailFlag">
					<div class="portlet light bordered">
						<div class="portlet-title">
							<div class="caption font-blue-hoki">流程卡编辑</div>
						</div>
						<div class="form-group">
							<table id="list"
								class="table table-striped table-bordered table-condensed table-advance table-hover">
								<thead>
									<tr class="heading">
										<th width="5%" style="text-align: center;" ng-if="fedCtrl.right.manage">操&nbsp;作</th>
										<th style="text-align: center;">批次编号</th>
										<th style="text-align: center;">A板Pnl数</th>
										<th style="text-align: center;">A板Set数</th>
										<th style="text-align: center;">A板Pcs数</th>
										<th style="text-align: center;">B板Pnl数</th>
										<th style="text-align: center;">B板Set数</th>
										<th style="text-align: center;">B板Pcs数</th>
										<th style="text-align: center;">总Set数</th>
										<th style="text-align: center;">总Pcs数</th>
									</tr>
								</thead>
								<tbody ng-repeat="row in fedCtrl.items">
										<tr>
										<td></td>
										<td><input style="text-align: center;" class="form-control" ng-model="fedCtrl.no" value="{{row.no}}" readonly /></td>
										<td><input ng-disabled="fedCtrl.qtyPnlAShow" class="form-control" ng-model="row.qtyPnlA" ng-blur="fedCtrl.clickNgblurItems($index)" required ng-onlynumberandzero /></td>
										<td><input class="form-control" ng-model="row.qtySetA" readonly /></td>
										<td><input class="form-control" ng-model="row.qtyPcsA" readonly /></td>
										<td><input ng-disabled="fedCtrl.qtyPnlBShow" class="form-control" ng-model="row.qtyPnlB" ng-blur="fedCtrl.clickNgblurItemsB($index)" required ng-onlynumberandzero /></td>
										<td><input class="form-control" ng-model="row.qtySetB" readonly /></td>
										<td><input class="form-control" ng-model="row.qtyPcsB" readonly /></td>
										<td><input class="form-control" ng-model="row.qtySetT" readonly /></td>
										<td><input class="form-control" ng-model="row.qtyPcsT" readonly /></td>
									</tr>
									<tr ng-repeat="rows in fedCtrl.subitems"
										ng-if="row.no == rows.superItem ">
										<td><input class="form-control" ng-model="rows.superItem" readonly style="text-align: center;" /></td>
										<td><input class="form-control" ng-model="rows.craftNo" readonly style="text-align: center;" /></td>
										<td></td>
										<td><input class="form-control" ng-model="rows.qtySetA" readonly /></td>
										<td><input class="form-control" ng-model="rows.qtyPcsA" readonly /></td>
										<td></td>
										<td><input class="form-control" ng-model="rows.qtySetB" readonly /></td>
										<td><input class="form-control" ng-model="rows.qtyPcsB" readonly /></td>
										<td><input class="form-control" ng-model="rows.qtySetT" readonly /></td>
										<td><input class="form-control" ng-model="rows.qtyPcsT" readonly /></td>
									</tr>
								</tbody>
								<thead>
									<tr class="heading">
										<th><input class="form-control"  readonly style="text-align: center;" value="可投数量/已投数量" /></th>
										<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtySetA" readonly /> --></th>
										<th><input class="form-control" ng-model="fedCtrl.itemsTotal.describeATotal" readonly /></th>
										<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtySetA" readonly /> --></th>
										<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtyPcsA" readonly /> --></th>
										<th><input class="form-control" ng-model="fedCtrl.itemsTotal.describeBTotal" readonly /></th>
										<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtySetB" readonly /> --></th>
										<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtyPcsB" readonly /> --></th>
										<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtySetT" readonly /> --></th>
										<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtyPcsT" readonly /> --></th>
									</tr> 
								</thead>
							</table>
						</div>

					</div>

				</div>


				<div class="form-group">
					<div class="pull-right" id="stepDetail4">
						<button type="submit" class="btn btn-primary btn-default-width"
							style="margin-right: 55px;" ng-if="fedCtrl.right.edit">
							<i class="fa fa-save"></i> 保&nbsp;存
						</button>
					</div>
				</div>
				
			<div class="row" ng-if="fedCtrl.boardCut != null && fedCtrl.boardCut != ''">
				<div class="col-md-18 col-lg-12">
					<div class="form-group">
						<label class="col-sm-3 col-md-1 control-label">开料信息：</label>
						<div class="col-sm-7 col-md-11">
							<span class="control-label" ng-bind="fedCtrl.boardCut.partA.msg"></span><br>
							<span class="control-label" ng-bind="fedCtrl.boardCut.partB.msg"></span><br>
							<span class="control-label" ng-bind="fedCtrl.boardCut.msg"></span>
						</div>
					</div>
				</div>
				<div id="infoStep17" class="panel panel-default">
           			<div class="panel-heading">
              			<h3 class="panel-title">示意图</h3>
       				</div>
           			<div class="panel-body">
           	 			<!-- 显示文件 -->
               			<div ng-if="!ctrl.edit.canUpload">
                   			<div class="col-md-12">
                       			<div class="form-group">
                           			<label class="col-sm-3 col-md-2 control-label">大板：</label>
                           			<div class="col-sm-7 col-md-10">
                           				<span ng-if="!fedCtrl.boardCut.tempUrl">无</span>
                               			<img ng-if="fedCtrl.boardCut.tempUrl" 
                               				ng-src="{{fedCtrl.boardCut.tempUrl}}" 
                               				width="75%">
                           			</div>
                       			</div>
                   			</div>
                   			<div class="col-md-12">
                      				<div class="form-group">
                           			<label class="col-sm-3 col-md-2 control-label">A板：</label>
                           			<div class="col-sm-7 col-md-10">
                           				<span ng-if="!fedCtrl.boardCutting.partA.tempUrl">无</span>
                               			<img ng-if="fedCtrl.boardCutting.partA.tempUrl" 
                               				ng-src="{{fedCtrl.boardCutting.partA.tempUrl}}" 
                               				width="100%">
                           			</div>
                       			</div>
                   			</div>
                   			<div class="col-md-12">
                       			<div class="form-group">
                           			<label class="col-sm-3 col-md-2 control-label">B板：</label>
                           			<div class="col-sm-7 col-md-10">
                           				<span ng-if="!fedCtrl.boardCut.partB.tempUrl">无</span>
                              				<img ng-if="fedCtrl.boardCut.partB.tempUrl" 
                              					ng-src="{{fedCtrl.boardCut.partB.tempUrl}}" 
                              					width="100%">
                           			</div>
                       			</div>
                   			</div>
                   			<!-- 显示附件 -->
                   		</div>
                   	</div>
                  </div>
				</div>
			</form>
		</div>
	</div>
</div>
</tab> 



<tab active="fedCtrl.tabs.seeDetail.active"
	ng-show="fedCtrl.tabs.seeDetail.show"> <tab-heading>
补料单查看详情 <i style="cursor: pointer" class="fa fa-times"
	ng-click="fedCtrl.hideSeeDetail()"></i></tab-heading>
<div class="rows">
	<div class="portlet light bordered">
		<div class="portlet-title">
			<div class="caption font-blue-hoki">补料单{{fedCtrl.editTitle}}</div>
		</div>
		<div class="portlet-body">
			<form class="form-horizontal" name="log_form" novalidate="novalidate"
				ng-submit="" ng-submit-force="true">

				<div class="row">
					<div class="col-md-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">通知单号:</label>
							<div class="col-sm-7 col-md-8">
								<input type="text" class="form-control" ng-model="fedCtrl.as"
									readonly="readonly" />
							</div>
						</div>
					</div>
					<div class="col-md-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label ">补料单号:</label>
							<div class="col-sm-7 col-md-8">
								<input type="text" class="form-control"
									ng-model="fedCtrl.feed.no" readonly="readonly" />
							</div>
						</div>
					</div>
					<div class="col-md-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label"> <span
								class="required">*</span>申请日期:
							</label>
							<div class="col-sm-7 col-md-8">
								<input type="text" class="form-control"
									ng-blur="fedCtrl.snameBlur()"
									ng-model="fedCtrl.feed.distributeDate" readonly="readonly"
									required />
							</div>
						</div>
					</div>

				</div>
				<div class="row">
					<div class="col-md-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">生产编号:</label>
							<div class="col-sm-7 col-md-8">
								<input type="text" class="form-control"
									ng-model="fedCtrl.bizPerson" readonly="readonly" />
							</div>
						</div>
					</div>

					<!--   <div class="col-md-4">
                              <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">板材类型:</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="fedCtrl.feed.orderQuantity"  />
                                    </div>
                                </div>
                            </div> -->
					<div class="col-md-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">交货数量:</label>
							<div class="col-sm-7 col-md-8">
								<input type="text" class="form-control"
									ng-model="fedCtrl.boardAPnlQty" readonly="readonly" />
							</div>
						</div>
					</div>

					<div class="col-md-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">操作员:</label>
							<div class="col-sm-7 col-md-8">
								<input type="text" class="form-control"
									ng-model="fedCtrl.feed.createdBy.userName" Readonly />
							</div>
						</div>
					</div>

				</div>
				<div class="row">
					<div class="col-md-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">报废数量:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control" ng-model="fedCtrl.failedPcsQty"
									readonly="readonly" />
							</div>
						</div>
					</div>

					<div class="col-md-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">补充数量:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control" ng-model="fedCtrl.replenishQty"
									readonly="readonly" />
							</div>
						</div>
					</div>

					<div class="col-md-6 col-lg-4">
						<div class="form-group" >
							<label class="col-sm-3 col-md-4 control-label">流程卡张数:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control"
									ng-model="fedCtrl.feed.produceBatchCount" readonly />
							</div>
						</div>
					</div>

				</div>


				<div class="row">
					<div class="col-md-6 col-lg-4" >
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">A板数:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control" ng-model="fedCtrl.feed.qtyPnlA"
									readonly />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4" >
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">B板数:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control" ng-model="fedCtrl.feed.qtyPnlB"
									readonly />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4" >
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">大板数:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control" ng-model="fedCtrl.feed.qtyPnlT"
									readonly />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4" ng-if="!fedCtrl.ishe && fedCtrl.showOaQty">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">可用库存数量:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control"
									ng-model="fedCtrl.feed.material.availableQty"
									ng-disabled="true" />
							</div>
						</div>
					</div>
					<div class="col-md-6 col-lg-4" ng-if="!fedCtrl.ishe && fedCtrl.showOaQty">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">占可用库存数量:</label>
							<div class="col-sm-7 col-md-8">
								<input class="form-control"
									ng-model="fedCtrl.feed.occupiedAvailableQty"
									ng-disabled="true" />
							</div>
						</div>
					</div>
					<div class="col-md-12 col-lg-8" ng-if="fedCtrl.ishe || fedCtrl.isheNoShow">
						<div class="form-group">
							<label class="col-sm-3 col-md-2 control-label">占可用库存数量:</label>
							<div class="col-sm-7 col-md-10">
								<input class="form-control" placeholder="点击文本框,在弹出框中分配占库存的可用数量。" 
									ng-model="fedCtrl.feed.occupiedAvailableQty"
									ng-disabled="fedingCtrl.heDisabled || fedingCtrl.contractFlag"
									ng-focus="fedCtrl.breakDownHeQty()" ng-onlynumberandzero />
							</div>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-md-8">
						<div class="form-group">
							<label class="col-sm-3 col-md-2 control-label ">报废原因:</label>
							<div class="col-sm-7 col-md-10">
								<textarea class="form-control" rows="5"
									ng-model="fedCtrl.feed.discardReason" readonly="readonly"
									maxlength="1000"></textarea>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-8">
						<div class="form-group">
							<label class="col-sm-3 col-md-2 control-label ">备注:</label>
							<div class="col-sm-7 col-md-10">
								<textarea class="form-control" rows="5"
									ng-model="fedCtrl.feed.remark" readonly="readonly"
									maxlength="1000"></textarea>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-18 col-lg-12">
						<div class="form-group">
						<label class="col-sm-3 col-md-1 control-label">基础信息:</label>
							<div class="col-sm-7 col-md-11">
								<span class="control-label" ng-if="fedCtrl.feed.unitLength">
									工程PCS尺寸：{{fedCtrl.feed.unitLength}}&nbsp;*&nbsp;{{fedCtrl.feed.unitWidth}}&emsp;&emsp;&emsp;
									工程SET尺寸：{{fedCtrl.feed.setWidth}}&nbsp;*&nbsp;{{fedCtrl.feed.setLength}}&nbsp;/&nbsp;{{fedCtrl.feed.pnlDivisor}}&emsp;&emsp;&emsp;
									油墨类型：{{fedCtrl.boardCut.inkTypeVal}}&emsp;&emsp;&emsp;
									覆铜板材：{{fedCtrl.boardCut.materialTypeValue}}&emsp;&emsp;&emsp;
									PCB类型：{{fedCtrl.boardCut.boardLevelValue}}&emsp;&emsp;&emsp;
									板材厚度：{{fedCtrl.boardCut.boardThicknessValue}}&emsp;&emsp;&emsp;
									覆铜要求：{{fedCtrl.boardCut.copperCladThicknessValue}}&emsp;&emsp;&emsp;
									镀层处理：{{fedCtrl.boardCut.surfaceProcessValue}}&emsp;&emsp;&emsp;
									阻焊类型：{{fedCtrl.boardCut.solderMaskTypeValue}}&emsp;&emsp;&emsp;
									加急状态：{{fedCtrl.boardCut.urgentName}}<br>
									特别说明：{{fedCtrl.feed.notifiRemark}}
								</span>
							</div>
						</div>
					</div>
				</div>
				<div class="row" ng-show="fedCtrl.showDetailFlag">
					<div class="portlet light bordered">
						<div class="portlet-title">
							<div class="caption font-blue-hoki">流程卡明细</div>
						</div>
						<div class="form-group">
							<table id="list"
								class="table table-striped table-bordered table-condensed table-advance table-hover">
								<thead>
									<tr class="heading">
										<th width="5%" style="text-align: center;" ng-if="fedCtrl.right.manage">操&nbsp;作</th>
										<th style="text-align: center;width: 10%;">批次编号</th>
										<th style="text-align: center;">报废数量</th>
										<th style="text-align: center;">返工数量</th>
										<th style="text-align: center;">不良数量</th>
										<th style="text-align: center;">A板Pnl数</th>
										<th style="text-align: center;">A板Set数</th>
										<th style="text-align: center;">A板Pcs数</th>
										<th style="text-align: center;">B板Pnl数</th>
										<th style="text-align: center;">B板Set数</th>
										<th style="text-align: center;">B板Pcs数</th>
										<th style="text-align: center;">总Set数</th>
										<th style="text-align: center;">总Pcs数</th>
									</tr>
								</thead>
								<tbody ng-repeat="row in fedCtrl.items" ng-if="!row.oldDetailId">
									<tr ng-class="{'success':true}">
										<td></td>
										<td><input readonly class="form-control"
											ng-model="fedCtrl.no" style="text-align: center;"
											value="{{row.no}}" /></td>
										<td><input readonly class="form-control"
											ng-model="row.discardPisQty" /></td>
										<td><input readonly class="form-control"
											ng-model="row.reworkQty" /></td>
										<td><input readonly class="form-control" ng-model="row.badQty" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtyPnlA" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtySetA" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtyPcsA" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtyPnlB" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtySetB" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtyPcsB" /></td>
										<!-- 	<td><input readonly class="form-control" ng-model="row.qtyPnlT" /></td> -->
										<td><input readonly class="form-control"
											ng-model="row.qtySetT" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtyPcsT" /></td>
									</tr>

									<tr ng-repeat="rows in fedCtrl.subitems"
										ng-if="rows.superItem == row.no">
										<td><input readonly class="form-control"
											ng-model="fedCtrl.no" style="text-align: center;"
											value="{{rows.superItem}}" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.craftNo" style="text-align: center;" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.discardPisQty" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.reworkQty" /></td>
										<td><input readonly class="form-control" ng-model="rows.badQty" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtyPnlA" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtySetA" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtyPcsA" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtyPnlB" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtySetB" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtyPcsB" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtySetT" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtyPcsT" /></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>

				<div class="row" ng-show="fedCtrl.showDisCardFlag">
					<div class="portlet light bordered">
						<div class="portlet-title">
							<div class="caption font-blue-hoki">拆卡</div>
						</div>
						<div class="form-group">
							<table id="list" class="table table-striped table-bordered table-condensed table-advance table-hover">
								<thead>
									<tr class="heading">
										<th width="5%" style="text-align: center;" ng-if="fedCtrl.right.manage">操&nbsp;作</th>
										<th style="text-align: center;width: 10%;">批次编号</th>
										<th style="text-align: center;">报废数量</th>
										<th style="text-align: center;">返工数量</th>
										<th style="text-align: center;">不良数量</th>
										<th style="text-align: center;">A板Pnl数</th>
										<th style="text-align: center;">A板Set数</th>
										<th style="text-align: center;">A板Pcs数</th>
										<th style="text-align: center;">B板Pnl数</th>
										<th style="text-align: center;">B板Set数</th>
										<th style="text-align: center;">B板Pcs数</th>
										<th style="text-align: center;">总Set数</th>
										<th style="text-align: center;">总Pcs数</th>
									</tr>
								</thead>
								<tbody ng-repeat="row in fedCtrl.items" ng-if="row.oldDetailId">
									<tr ng-class="{'success':true}">
										<td></td>
										<td><input readonly class="form-control"
											ng-model="fedCtrl.no" style="text-align: center;"
											value="{{row.no}}" /></td>
										<td><input readonly class="form-control"
											ng-model="row.discardPisQty" /></td>
										<td><input readonly class="form-control"
											ng-model="row.reworkQty" /></td>
										<td><input readonly class="form-control" ng-model="row.badQty" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtyPnlA" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtySetA" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtyPcsA" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtyPnlB" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtySetB" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtyPcsB" /></td>
										<!-- 	<td><input readonly class="form-control" ng-model="row.qtyPnlT" /></td> -->
										<td><input readonly class="form-control"
											ng-model="row.qtySetT" /></td>
										<td><input readonly class="form-control"
											ng-model="row.qtyPcsT" /></td>
									</tr>

									<tr ng-repeat="rows in fedCtrl.newBatchDetailCrafts"
										ng-if="$parent.$index == fedCtrl.items.length -1">
										<td><input readonly class="form-control"
											ng-model="fedCtrl.no" style="text-align: center;"
											value="{{rows.superItem}}" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.craftNo" style="text-align: center;" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.discardPisQty" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.reworkQty" /></td>
										<td><input readonly class="form-control" ng-model="rows.badQty" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtyPnlA" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtySetA" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtyPcsA" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtyPnlB" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtySetB" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtyPcsB" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtySetT" /></td>
										<td><input readonly class="form-control"
											ng-model="rows.qtyPcsT" /></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<div class="row" ng-if="fedCtrl.boardCut != null && fedCtrl.boardCut != ''">
				<div class="col-md-18 col-lg-12">
					<div class="form-group">
						<label class="col-sm-3 col-md-1 control-label">开料信息：</label>
						<div class="col-sm-7 col-md-11">
							<span class="control-label" ng-bind="fedCtrl.boardCut.partA.msg"></span><br>
							<span class="control-label" ng-bind="fedCtrl.boardCut.partB.msg"></span><br>
							<span class="control-label" ng-bind="fedCtrl.boardCut.msg"></span>
						</div>
					</div>
				</div>
				<div id="infoStep17" class="panel panel-default">
           			<div class="panel-heading">
              			<h3 class="panel-title">示意图</h3>
       				</div>
           			<div class="panel-body">
           	 			<!-- 显示文件 -->
               			<div ng-if="!ctrl.edit.canUpload">
                   			<div class="col-md-12">
                       			<div class="form-group">
                           			<label class="col-sm-3 col-md-2 control-label">大板：</label>
                           			<div class="col-sm-7 col-md-10">
                           				<span ng-if="!fedCtrl.boardCut.tempUrl">无</span>
                               			<img ng-if="fedCtrl.boardCut.tempUrl" 
                               				ng-src="{{fedCtrl.boardCut.tempUrl}}" 
                               				width="75%">
                           			</div>
                       			</div>
                   			</div>
                   			<div class="col-md-12">
                      				<div class="form-group">
                           			<label class="col-sm-3 col-md-2 control-label">A板：</label>
                           			<div class="col-sm-7 col-md-10">
                           				<span ng-if="!fedCtrl.boardCutting.partA.tempUrl">无</span>
                               			<img ng-if="fedCtrl.boardCutting.partA.tempUrl" 
                               				ng-src="{{fedCtrl.boardCutting.partA.tempUrl}}" 
                               				width="100%">
                           			</div>
                       			</div>
                   			</div>
                   			<div class="col-md-12">
                       			<div class="form-group">
                           			<label class="col-sm-3 col-md-2 control-label">B板：</label>
                           			<div class="col-sm-7 col-md-10">
                           				<span ng-if="!fedCtrl.boardCut.partB.tempUrl">无</span>
                              				<img ng-if="fedCtrl.boardCut.partB.tempUrl" 
                              					ng-src="{{fedCtrl.boardCut.partB.tempUrl}}" 
                              					width="100%">
                           			</div>
                       			</div>
                   			</div>
                   			<!-- 显示附件 -->
                   		</div>
                   	</div>
                  </div>
				</div>
			</form>
		</div>
	</div>
</div>
</tab> 



				<!-- 客诉补料 -->

<tab active="fedCtrl.tabs.receeditForm.active"
					ng-show="fedCtrl.tabs.receeditForm.show"> <tab-heading>
				补料单详情 <i style="cursor: pointer" class="fa fa-times"
					ng-click="fedCtrl.hidereceEditform()"></i></tab-heading>
				<div class="rows">
					<div class="portlet light bordered">
						<div class="portlet-title">
							<div class="caption font-blue-hoki">客诉补料</div>
						</div>
						<div class="portlet-body" id="stepinfo1">
							<form class="form-horizontal" name="feeding_form"
								ng-init="fedCtrl.setFormScope(this)" novalidate="novalidate"
								ng-submit="fedCtrl.submitMod(feeding_form);"
								ng-submit-force="true">
								<div class="row">
									<div class="col-md-6 col-lg-4">
										<div class="form-group">
											<label class="col-sm-3 col-md-4 control-label ">补料单号:</label>
											<div class="col-sm-7 col-md-8">
												<input type="text" class="form-control"
													ng-model="fedCtrl.feed.no" readonly="readonly" />
											</div>
										</div>
									</div>
									<div class="col-md-6 col-lg-4" id="stepinfo2">
										<div class="form-group">
											<label class="col-sm-3 col-md-4 control-label"><span
												class="required">*</span>客诉单号:</label>
											<div class="col-sm-7 col-md-8">
												<ui-select theme="bootstrap"
													firstfocus="{{fedCtrl.focus.main}}" ng-model="fedCtrl.as"
													on-select="fedCtrl.selectreces($select.selected)">
												<ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
												<ui-select-choices refresh="fedCtrl.seachste($select.search)" refresh-delay="1000" 
													infinite-scroll='fedingCtrl.addMoreItemsste()' infinite-scroll-distance='3'
													repeat="item.recordId as item in fedCtrl.rece">
												<div ng-bind-html="item.no | highlight: $select.search"></div>
												<small>
													 <span style="color: blue;">客诉数量:&nbsp;&nbsp;&nbsp;&nbsp;{{item.quantity}}<br></span>
													 <span style="color: blue;" ng-if="item.sourceReject && item.sourceReject.no">终端客诉单:&nbsp;&nbsp;&nbsp;&nbsp;{{item.sourceReject.no}}<br></span>
												</small>
												</ui-select-choices> </ui-select>
											</div>
										</div>
									</div>
									<div class="col-md-6 col-lg-4">
										<div class="form-group">
											<label class="col-sm-3 col-md-4 control-label"><span
												class="required">*</span> 申请日期:</label>
											<div class="col-sm-7 col-md-8">
												<input type="text" class="form-control"
													ng-blur="fedCtrl.snameBlur()"
													ng-model="fedCtrl.feed.distributeDate" readonly="readonly"
													required />
											</div>
										</div>
									</div>
									<div class="col-md-6 col-lg-4">
										<div class="form-group">
											<label class="col-sm-3 col-md-4 control-label">生产编号:</label>
											<div class="col-sm-7 col-md-8">
												<input readonly="readonly" type="text" class="form-control"
													ng-model="fedCtrl.bizPerson" />
											</div>
										</div>
									</div>


									<div class="col-md-6 col-lg-4">
										<div class="form-group">
											<label class="col-sm-3 col-md-4 control-label"><span
												class="required">*</span>操作员:</label>
											<div class="col-sm-7 col-md-8">
												<!-- 	<input type="text" class="form-control"
									ng-model="fedCtrl.loginUser.userName" Readonly />
								 -->
												<ui-select required ng-model="fedCtrl.feed.doApplicant"
													theme="bootstrap" ng-disabled="!fedCtrl.right.edit">
												<ui-select-match placeholder="请选择...">{{$select.selected.userName}}</ui-select-match>
												<ui-select-choices
													repeat="item.recordId as item in fedCtrl.refer.userList | filter: $select.search">
												<div
													ng-bind-html="item.userName | highlight: $select.search"></div>
												</ui-select-choices> </ui-select>
											</div>
										</div>
									</div>

									<div class="col-md-6 col-lg-4">
										<div class="form-group">
											<label class="col-sm-3 col-md-4 control-label">客诉数量:</label>
											<div class="col-sm-7 col-md-8">
												<input readonly="readonly" class="form-control"
													ng-model="fedCtrl.failedPcsQty" />
											</div>
										</div>
									</div>
									<div class="col-md-6 col-lg-4">
										<div class="form-group">
											<label class="col-sm-3 col-md-4 control-label">需要补料数量:</label>
											<div class="col-sm-7 col-md-8">
												<input readonly="readonly" class="form-control"
													ng-model="fedCtrl.haveFedQty" />
											</div>
										</div>
									</div>
									<div class="col-md-6 col-lg-4">
										<div class="form-group">
											<label class="col-sm-3 col-md-4 control-label">尾数:</label>
											<div class="col-sm-7 col-md-8">
												<input class="form-control"
													ng-model="fedCtrl.feed.occupyMantissa" ng-blur="fedCtrl.computHaveFedQty()" ng-disabled="fedCtrl.editofshow" ng-onlynumberandzero/>
											</div>
										</div>
									</div>
									<div class="col-md-6 col-lg-4" >
										<div class="form-group" id="stepinfo3">
											<label class="col-sm-3 col-md-4 control-label"><span
												class="required">*</span>补料数量:</label>
											<div class="col-sm-7 col-md-8">
												<input class="form-control"
													ng-model="fedCtrl.feed.discardQty" disabled="disabled"
													required ng-onlynumberandzero />
											</div>
										</div>
									</div>

									<div class="col-md-6 col-lg-4" >
										<div class="form-group" id="stepinfo5">
											<label class="col-sm-3 col-md-4 control-label"><span
												class="required">*</span>流程卡张数:</label>
											<div class="col-sm-7 col-md-8">
												<input class="form-control"
													ng-change="fedCtrl.clickNgblurCount(fedCtrl.feed.produceBatchCount)"
													ng-model="fedCtrl.feed.produceBatchCount" required
													ng-disabled="fedCtrl.editofshow" ng-onlynumberandzero />
											</div>
										</div>
									</div>

								</div>

								<div class="row">
									<div class="col-md-6 col-lg-4" >
										<div class="form-group">
											<label class="col-sm-3 col-md-4 control-label"><span
												class="required">*</span>A板数:</label>
											<div class="col-sm-7 col-md-8">
												<input class="form-control" ng-model="fedCtrl.feed.qtyPnlA"
													ng-onlynumberandzero required
													ng-blur="fedCtrl.clickNgblurA(fedCtrl.feed.qtyPnlA)" />
											</div>
										</div>
									</div>
									<div class="col-md-6 col-lg-4" >
										<div class="form-group">
											<label class="col-sm-3 col-md-4 control-label"><span
												class="required">*</span>B板数:</label>
											<div class="col-sm-7 col-md-8">
												<input class="form-control" ng-model="fedCtrl.feed.qtyPnlB"
													ng-onlynumberandzero required
													ng-blur="fedCtrl.clickNgblurB(fedCtrl.feed.qtyPnlB)" />
											</div>
										</div>
									</div>
									<div class="col-md-6 col-lg-4" >
										<div class="form-group">
											<label class="col-sm-3 col-md-4 control-label"><span
												class="required">*</span>大板数:</label>
											<div class="col-sm-7 col-md-8">
												<input class="form-control" ng-model="fedCtrl.feed.qtyPnlT"
													ng-disabled="fedCtrl.editofshowqtyPnlT" required
													ng-onlynumberandzero />
											</div>
										</div>
									</div>

								</div>
								<div class="row">
									<div class="col-md-12 col-lg-8">
										<div class="form-group">
											<label class="col-sm-3 col-md-2 control-label"><span
												class="required">*</span>客诉原因:</label>
											<div class="col-sm-7 col-md-10">
												<textarea class="form-control" ng-disabled="true"
													ng-model="fedCtrl.feed.discardReason" ng-maxlength="255"
													required></textarea>
											</div>
										</div>
									</div>



								</div>
								<div class="row">
									<div class="col-md-12 col-lg-8">
										<div class="form-group">
											<label class="col-sm-3 col-md-2 control-label ">备注:</label>
											<div class="col-sm-7 col-md-10">
												<textarea class="form-control"
													ng-disabled="!fedCtrl.right.edit"
													ng-model="fedCtrl.feed.remark" ng-maxlength="255"></textarea>
											</div>
										</div>
									</div>
								</div>




								<div class="form-group">
									<div class="pull-right" id="stepinfo4">
										<button type="submit"
											class="btn btn-primary btn-default-width"
											style="margin-right: 55px;" ng-if="fedCtrl.right.edit">
											<i class="fa fa-save"></i> 保&nbsp;存
										</button>
									</div>
								</div>
								<div class="row">
									<div class="col-md-18 col-lg-12">
										<div class="form-group">
										<label class="col-sm-3 col-md-1 control-label">基础信息:</label>
											<div class="col-sm-7 col-md-11">
												<span class="control-label" ng-if="fedCtrl.feed.unitLength">
													工程PCS尺寸：{{fedCtrl.feed.unitLength}}&nbsp;*&nbsp;{{fedCtrl.feed.unitWidth}}&emsp;&emsp;&emsp;
													工程SET尺寸：{{fedCtrl.feed.setWidth}}&nbsp;*&nbsp;{{fedCtrl.feed.setLength}}&nbsp;/&nbsp;{{fedCtrl.feed.pnlDivisor}}&emsp;&emsp;&emsp;
													油墨类型：{{fedCtrl.boardCut.inkTypeVal}}&emsp;&emsp;&emsp;
													覆铜板材：{{fedCtrl.boardCut.materialTypeValue}}&emsp;&emsp;&emsp;
													PCB类型：{{fedCtrl.boardCut.boardLevelValue}}&emsp;&emsp;&emsp;
													板材厚度：{{fedCtrl.boardCut.boardThicknessValue}}&emsp;&emsp;&emsp;
													覆铜要求：{{fedCtrl.boardCut.copperCladThicknessValue}}&emsp;&emsp;&emsp;
													镀层处理：{{fedCtrl.boardCut.surfaceProcessValue}}&emsp;&emsp;&emsp;
													阻焊类型：{{fedCtrl.boardCut.solderMaskTypeValue}}&emsp;&emsp;&emsp;
													加急状态：{{fedCtrl.boardCut.urgentName}}<br>
													特别说明：{{fedCtrl.feed.notifiRemark}}
												</span>
											</div>
										</div>
									</div>
								</div>

								<div class="row" ng-show="fedCtrl.showDetailFlag">
									<div class="portlet light bordered">
										<div class="portlet-title">
											<div class="caption font-blue-hoki">流程卡编辑</div>
										</div>
										<div class="form-group">
											<table id="list"
												class="table table-striped table-bordered table-condensed table-advance table-hover">
												<thead>
													<tr class="heading">
														<th style="text-align: center;">批次编号</th>
														<th style="text-align: center;">A板Pnl数</th>
														<th style="text-align: center;">A板Set数</th>
														<th style="text-align: center;">A板Pcs数</th>
														<th style="text-align: center;">B板Pnl数</th>
														<th style="text-align: center;">B板Set数</th>
														<th style="text-align: center;">B板Pcs数</th>
														<th style="text-align: center;">总Set数</th>
														<th style="text-align: center;">总Pcs数</th>
													</tr>
												</thead>
												<tbody ng-repeat="row in fedCtrl.items">
													<tr>
														<td><input style="text-align: center;" class="form-control" ng-model="fedCtrl.no" value="{{row.no}}" readonly /></td>
														<td><input ng-disabled="fedCtrl.qtyPnlAShow" class="form-control" ng-model="row.qtyPnlA" ng-blur="fedCtrl.clickNgblurItems($index)" required ng-onlynumberandzero /></td>
														<td><input class="form-control" ng-model="row.qtySetA" readonly /></td>
														<td><input class="form-control" ng-model="row.qtyPcsA" readonly /></td>
														<td><input ng-disabled="fedCtrl.qtyPnlBShow" class="form-control" ng-model="row.qtyPnlB" ng-blur="fedCtrl.clickNgblurItemsB($index)" required ng-onlynumberandzero /></td>
														<td><input class="form-control" ng-model="row.qtySetB" readonly /></td>
														<td><input class="form-control" ng-model="row.qtyPcsB" readonly /></td>
														<td><input class="form-control" ng-model="row.qtySetT" readonly /></td>
														<td><input class="form-control" ng-model="row.qtyPcsT" readonly /></td>
													</tr>
												</tbody>
												<thead>
													<tr class="heading">
															<th><input class="form-control"  readonly style="text-align: center;" value="可投数量/已投数量" /></th>
															<th><input class="form-control" ng-model="fedCtrl.itemsTotal.describeATotal" readonly /></th>
															<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtySetA" readonly /> --></th>
															<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtyPcsA" readonly /> --></th>
															<th><input class="form-control" ng-model="fedCtrl.itemsTotal.describeBTotal" readonly /></th>
															<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtySetB" readonly /> --></th>
															<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtyPcsB" readonly /> --></th>
															<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtySetT" readonly /> --></th>
															<th><!-- <input class="form-control" ng-model="fedCtrl.itemsTotal.qtyPcsT" readonly /> --></th>
													</tr> 
												</thead>
											</table>
										</div>

									</div>

								</div>
								<div class="row" ng-if="fedCtrl.boardCut != null && fedCtrl.boardCut != ''">
									<div class="col-md-18 col-lg-12">
										<div class="form-group">
											<label class="col-sm-3 col-md-1 control-label">开料信息：</label>
											<div class="col-sm-7 col-md-11">
												<span class="control-label" ng-bind="fedCtrl.boardCut.partA.msg"></span><br>
												<span class="control-label" ng-bind="fedCtrl.boardCut.partB.msg"></span><br>
												<span class="control-label" ng-bind="fedCtrl.boardCut.msg"></span>
											</div>
										</div>
									</div>
									<div id="infoStep17" class="panel panel-default">
					           			<div class="panel-heading">
					              			<h3 class="panel-title">示意图</h3>
					       				</div>
					           			<div class="panel-body">
					           	 			<!-- 显示文件 -->
					               			<div ng-if="!ctrl.edit.canUpload">
					                   			<div class="col-md-12">
					                       			<div class="form-group">
					                           			<label class="col-sm-3 col-md-2 control-label">大板：</label>
					                           			<div class="col-sm-7 col-md-10">
					                           				<span ng-if="!fedCtrl.boardCut.tempUrl">无</span>
					                               			<img ng-if="fedCtrl.boardCut.tempUrl" 
					                               				ng-src="{{fedCtrl.boardCut.tempUrl}}" 
					                               				width="75%">
					                           			</div>
					                       			</div>
					                   			</div>
					                   			<div class="col-md-12">
					                      				<div class="form-group">
					                           			<label class="col-sm-3 col-md-2 control-label">A板：</label>
					                           			<div class="col-sm-7 col-md-10">
					                           				<span ng-if="!fedCtrl.boardCutting.partA.tempUrl">无</span>
					                               			<img ng-if="fedCtrl.boardCutting.partA.tempUrl" 
					                               				ng-src="{{fedCtrl.boardCutting.partA.tempUrl}}" 
					                               				width="100%">
					                           			</div>
					                       			</div>
					                   			</div>
					                   			<div class="col-md-12">
					                       			<div class="form-group">
					                           			<label class="col-sm-3 col-md-2 control-label">B板：</label>
					                           			<div class="col-sm-7 col-md-10">
					                           				<span ng-if="!fedCtrl.boardCut.partB.tempUrl">无</span>
					                              				<img ng-if="fedCtrl.boardCut.partB.tempUrl" 
					                              					ng-src="{{fedCtrl.boardCut.partB.tempUrl}}" 
					                              					width="100%">
					                           			</div>
					                       			</div>
					                   			</div>
					                   			<!-- 显示附件 -->
					                   		</div>
					                   	</div>
					                  </div>
									</div>


							</form>
						</div>
					</div>
				</div>
				</tab>









</tabset>


<div class="row">
	<div class="col-md-12">
		<div id="static" class="modal fade" tabindex="-1"
			data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true"></button>
						<h4 class="modal-title">提示</h4>
					</div>
					<div class="modal-body">
						<p>{{fedCtrl.message}}</p>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


<div class="row">
	<div class="col-md-12">
		<div id="staticRemove" class="modal fade" tabindex="-1"
			data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true"></button>
						<h4 class="modal-title">删除补料单</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="col-sm-1 text-center">
							<i class="fa fa-warning font-yellow"
								style="font-size: 30px !important;"></i>
						</div>
						<div class="col-sm-11">确认删除该补料单吗？</div>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue"
							ng-click="fedCtrl.doDelfedCtrl()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="staticNois" class="modal fade" tabindex="-1"
			data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true"></button>
						<h4 class="modal-title">操作提示</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="col-sm-1 text-center">
							<i class="fa fa-warning font-yellow"
								style="font-size: 30px !important;"></i>
						</div>
						<div class="col-sm-11">当前编号已经被用将自动为您累加，是否可以？</div>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue"
							ng-click="fedCtrl.insertFed()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="breakDownOaQtyModal" class="modal fade" tabindex="-1"
			data-backdrop="static" data-keyboard="false" role="dialog"
			aria-hidden="true" aria-labelledby="breakDownOaQtyModalLabel">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							ng-click="fedCtrl.resetIsheOaQtyList()" aria-hidden="true"></button>
						<h4 class="modal-title">合单分配库存可用数量</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<table
							class="table table-striped table-bordered table-condensed table-advance table-hover">
							<thead>
								<tr class="heading">
									<th style="text-align: center;"></th>
									<th style="text-align: center;">通知单号</th>
									<th style="text-align: center;">生产编号</th>
									<th style="text-align: center;">库存可用数量</th>
									<th style="text-align: center;">占库存可用数量</th>
								</tr>
							</thead>
							<tbody>
								<tr ng-repeat="isheOaQty in fedCtrl.feedingIsheOaQtyList">
									<td>{{$index + 1}}</td>
									<td ng-bind="isheOaQty.notification.no"></td>
									<td ng-bind="isheOaQty.craftNo"></td>
									<td ng-bind="isheOaQty.availableQty | zeroFilter |  number:0"></td>
									<td><input class="form-control"
										ng-disabled="fedCtrl.tabs.seeDetail.show"
										ng-model="isheOaQty.occupiedAvailableQty"
										ng-blur="fedCtrl.avalidOaQty(isheOaQty)" /></td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue"
							ng-click="fedCtrl.modIsheOaQtyList()" ng-show="!fedCtrl.tabs.seeDetail.show">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default"
							ng-click="fedCtrl.resetIsheOaQtyList()">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="isyu" class="modal fade" tabindex="-1" data-backdrop="static"
			data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true"></button>
						<h4 class="modal-title">选择补料方案</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">

						<div class="col-sm-11">
							方案一:
							<div class="task-checkbox">
								<checkbox ng-model="fedCtrl.urgentFlagone" value="1" name="test"></checkbox>
								<strong style="color: red;">您原本的补料单,A/B板数如下! A板：<span
									ng-bind-html="fedCtrl.feed.qtyPnlA"></span>, B板：<span
									ng-bind-html="fedCtrl.feed.qtyPnlB"></span>, 大板：<span
									ng-bind-html="fedCtrl.feed.qtyPnlT"></span></strong>
							</div>
						</div>


						<div class="col-sm-11">
							方案二:
							<div class="task-checkbox">
								<checkbox ng-model="fedCtrl.urgentFlagtwo" value="1" name="test"></checkbox>
								<strong style="color: red;">您当前选择的补料单,仓库建议的A/B板数如下! A板：<span
									ng-bind-html="fedCtrl.feed.aban"></span>, B板：<span
									ng-bind-html="fedCtrl.feed.bban"></span>, 大板：<span
									ng-bind-html="fedCtrl.feed.daban"></span></strong>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue"
							ng-click="fedCtrl.saveProject()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>



<div class="row">
	<div class="col-md-12">
		<div id="Cancelfeding" class="modal fade" tabindex="-1"
			data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true"></button>
						<h4 class="modal-title">操作提示</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="col-sm-1 text-center">
							<i class="fa fa-warning font-yellow"
								style="font-size: 30px !important;"></i>
						</div>
						<div class="col-sm-11">将取消{{fedCtrl.tempfeed.no}}补料单,是否继续？</div>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue"
							ng-click="fedCtrl.doCancelfeding()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="cancleReplenishStatic" class="modal fade" tabindex="-1"
			data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">取消确认补料单</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="col-sm-1 text-center"><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
						<div class="col-sm-11">{{fedCtrl.message}}</div>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue"
							ng-click="fedCtrl.cancleReplenish()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div class="modal fade bs-modal-lg" id="deliveryAdjustStatic" tabindex="-1" role="dialog" aria-hidden="true">
		    <div class="modal-dialog modal-lg">
		        <div class="modal-content">
		            <div class="modal-header">
		                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
		                <h4 class="modal-title">
		                    <span>{{fedCtrl.fed.no}} / {{fedCtrl.fed.notification.craftNo}}交期调整</span>
		                </h4>
		            </div>
		            <div class="modal-body clearfix" style="padding-top: 5px;">
						<div class="col-md-12">
							<div class="row">
			       			   <div class="col-md-6 col-lg-6">
		                            <div class="form-group">
		                                <label class="col-sm-5 col-md-4 control-label">当前交期：</label>
		                                <div class="col-sm-7 col-md-8">
		                                    <span class="form-control" disabled ng-bind="fedCtrl.fed.notification.deliveryDate" ></span>
		                                </div>
		                            </div>
		                        </div>
					       		<div class="col-md-6 col-lg-6">
									<div class="form-group">
										<label class="col-sm-5 col-md-4 control-label"><span class="required">*</span>修改后交期：</label>
										<div class="col-sm-7">
				                            <input type="text" class="form-control" 
				                                   ng-model="fedCtrl.fed.deliveryAdjust"
				                                   data-date-format="yyyy-MM-dd"
				                                   data-date-type="number"
				                                   data-min-date="today"
				                                   data-autoclose="1"
				                                   daysOfWeekDisabled="false"
				                                   bs-datepicker  />
			                       		 </div>
									</div>
								</div>
							</div>
						</div>
	            	</div>
		   			<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue" ng-click="fedCtrl.deliveryAdjust()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
		        </div>
		    </div>
		</div>
	</div>
</div>
