package com.kyb.pcberp.modules.contract.vo;

import java.math.BigDecimal;

import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.contract.entity.GroupCenterTwoData;

public class GroupCenterDataVo extends GroupCenterTwoData
{
    
    /**
     * 
     */
    private static final long serialVersionUID = 1280112507370707711L;
    
    @ExcelField(title = "账期", align = 2, sort = 10)
    public String getPeriod()
    {
        return super.getPeriod();
    }
    
    @ExcelField(title = "客户简称", align = 2, sort = 18)
    public String getCusName()
    {
        return super.getCusName();
    }
    
    @ExcelField(title = "客户编号", align = 2, sort = 20)
    public String getFinalCustNo()
    {
        return super.getFinalCustNo();
    }
    
    @ExcelField(title = "客户订单号", align = 2, sort = 25)
    public String getCustomerPo()
    {
        return super.getCustomerPo();
    }
    
    @ExcelField(title = "客户型号", align = 2, sort = 50)
    public String getCustomerModel()
    {
        return super.getCustomerModel();
    }
    
    @ExcelField(title = "对账数量 ", align = 2, sort = 80)
    public Integer getQuantity()
    {
        return super.getQuantity();
    }
    
    @ExcelField(title = "深圳领德实业收款", align = 2, sort = 110)
    public BigDecimal getXsReceivableAmount()
    {
        return super.getXsReceivableAmount();
    }
    
    @ExcelField(title = "江西领德辉成本", align = 2, sort = 120)
    public BigDecimal getGcReceivableAmount()
    {
        return super.getGcReceivableAmount();
    }
    
    
    @ExcelField(title = "物料成本", align = 2, sort = 130)
    public BigDecimal getMaterialAmout()
    {
        return super.getMaterialAmout();
    }
    
    
    @ExcelField(title = "加工费", align = 2, sort = 140)
    public BigDecimal getQuoteAmount()
    {
        return super.getQuoteAmount();
    }
    
    @ExcelField(title = "五费", align = 2, sort = 150)
    public BigDecimal getFiveAmontFlag()
    {
        return super.getFiveAmontFlag();
    }
    
    @ExcelField(title = "PCS成本", align = 2, sort = 160)
    public BigDecimal getPcsMadePrice()
    {
        return super.getPcsMadePrice();
    }
    
    @ExcelField(title = "合同编号", align = 2, sort = 170)
    public String getNo()
    {
        return super.getNo();
    }
    
    @ExcelField(title = "生产编号", align = 2, sort = 175)
    public String getCraftNo()
    {
        return super.getCraftNo();
    }
    
    @ExcelField(title = "合同订量", align = 2, sort = 180)
    public Integer getGcPayableQuality()
    {
        return super.getGcPayableQuality();
    }
    
    @ExcelField(title = "合同单价", align = 2, sort = 190)
    public BigDecimal getPrice()
    {
        return super.getPrice();
    }
    
    
    
    @ExcelField(title = "集控id", align = 2, sort = 200)
    public String getGroupCenterId()
    {
        return super.getGroupCenterId();
    }
    
    @ExcelField(title = "生产属性", align = 2, sort = 210)
    public String getTypeDesc()
    {
        return super.getTypeDesc();
    }
    
    @ExcelField(title = "龙南外协客户型号匹配厂编", align = 2, sort = 220)
    public String getXsCraftNo()
    {
        return super.getXsCraftNo();
    }
    
    @ExcelField(title = "客户型号匹配厂编", align = 2, sort = 230)
    public String getOtherCraftNo()
    {
        return super.getOtherCraftNo();
    }
    
    @ExcelField(title = "匹配方式", align = 2, sort = 240)
    public String getOlderType()
    {
        return null;
    }
    
    @ExcelField(title = "备注", align = 2, sort = 250)
    public String getFailReson()
    {
        return super.getFailReson();
    }
    
    
}
