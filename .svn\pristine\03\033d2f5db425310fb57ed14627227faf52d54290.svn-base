<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.eg.dao.CardADao">
    
	<sql id="cardAColumns">
		a.recordId,
		a.companyId AS "company.recordId",
		a.templateId,
		a.processCardBId AS "cardB.recordId",
		a.no,
		a.produceModel,
		a.noticeNos,
		a.customerNos,
		a.customerModels,
		a.customerFileNames,
		a.pcsWidth,
		a.pcsLength,
		a.setWidth,
		a.setLength,
		a.orderType,
		a.cardMadeDate,
		a.cancelCause,
		a.pnlDivisor,
		a.boardLevel,
		a.finishedThickness,
		a.finishedTolerance,
		a.innerThickness,
		a.status,
		a.activeFlag,
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.remark,
		a.cardAuditUser as "cardAuditUser.recordId",
		a.cardAuditDate,
		a.pliesnumber,
		a.remark2,
		a.materialId,
		a.testShelfSelected,
		a.mouldSelected,
		a.starAniseLength,
		a.starAniseWidth,
		a.projectStatus,
		a.setAreaRatio,
		a.wasteArea,
		a.causewayLayer,
		a.borehole,
		IFNULL(a.version,0)
	</sql>
	
	<sql id="cardAJoins">
	</sql>
    
	<resultMap id="Result_ProcessValue" type="CardA">
		<id property="recordId" column="recordId" />
		<result property="templateId" column="templateId"  />
		<result property="no" column="no" />
		<result property="produceModel" column="produceModel" />
		<result property="noticeNos" column="noticeNos" />
		<result property="customerNos" column="customerNos" />
		<result property="customerModels" column="customerModels"/>
		<result property="customerFileNames" column="customerFileNames"/>
		<result property="pcsWidth" column="pcsWidth"/>
		<result property="pcsLength" column="pcsLength"/>
		<result property="setWidth" column="setWidth" />
		<result property="setLength" column="setLength" />
		<result property="orderType" column="orderType"/>
		<result property="cardMadeDate" column="cardMadeDate"/>
		<result property="cancelCause" column="cancelCause"/>
		<result property="pnlDivisor" column="pnlDivisor"/>
		<result property="boardLevel" column="boardLevel" />
		<result property="finishedThickness" column="finishedThickness" />
		<result property="finishedTolerance" column="finishedTolerance" />
		<result property="innerThickness" column="innerThickness" />
		<result property="status" column="status" />
		<result property="activeFlag" column="activeFlag" />
		<result property="createdDate" column="createdDate"/>
		<result property="remark" column="remark" />
		<result property="pliesnumber" column="pliesnumber"/>
		<result property="remark2" column="remark2" />
		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
		</association>
       	
       	<!-- 工序/工艺 TODO -->
       	<collection property="processValueList" 
       		column="{cardA.recordId=recordId, company.recordId=company.recordId}" 
       		select="com.kyb.pcberp.modules.eg.dao.CardAProcessValueDao.findListByCardA" /> 
	</resultMap>
	
	<resultMap id="Result_CardB_ProcessValue" type="CardA">
		<id property="recordId" column="recordId" />
		<result property="templateId" column="templateId"  />
		<result property="no" column="no" />
		<result property="produceModel" column="produceModel" />
		<result property="noticeNos" column="noticeNos" />
		<result property="customerNos" column="customerNos" />
		<result property="customerModels" column="customerModels"/>
		<result property="customerFileNames" column="customerFileNames"/>
		<result property="pcsWidth" column="pcsWidth"/>
		<result property="pcsLength" column="pcsLength"/>
		<result property="setWidth" column="setWidth" />
		<result property="setLength" column="setLength" />
		<result property="orderType" column="orderType"/>
		<result property="cardMadeDate" column="cardMadeDate"/>
		<result property="status" column="status" />
		<result property="createdDate" column="createdDate"/>
		<result property="lastUpdDate" column="lastUpdDate"/>
		<result property="remark" column="remark" />
		<result property="cancelCause" column="cancelCause" />
		<result property="pnlDivisor" column="pnlDivisor"/>
		<result property="boardLevel" column="boardLevel" />
		<result property="pliesnumber" column="pliesnumber"/>
		<result property="finishedThickness" column="finishedThickness" />
		<result property="finishedTolerance" column="finishedTolerance" />
		<result property="innerThickness" column="innerThickness" />
		<result property="activeFlag" column="activeFlag" />
		
		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
		</association>
		
		<association property="createdBy" column="createdBy" javaType="User">
			<id property="recordId" column="createdBy.recordId"/>
			<result property="userName" column="createdBy.userName" />
		</association>
		
		<association property="cardB" javaType="CardB"
       		column="{recordId=cardB.recordId, company.recordId=company.recordId, activeFlag=activeFlag}" 
       		select="com.kyb.pcberp.modules.eg.dao.CardBDao.getById" /> 
       	
       	<!-- 工序/工艺 TODO -->
       	<collection property="processValueList" 
       		column="{cardA.recordId=recordId, company.recordId=company.recordId}" 
       		select="com.kyb.pcberp.modules.eg.dao.CardAProcessValueDao.findListByCardA" /> 
	</resultMap>
	
	<resultMap id="Result_CardB" type="CardA">
		<id property="recordId" column="recordId" />
		
		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
		</association>
		
		<association property="cardB" javaType="CardB"
       		column="{recordId=cardB.recordId, company.recordId=company.recordId, activeFlag=activeFlag}" 
       		select="com.kyb.pcberp.modules.eg.dao.CardBDao.getById" /> 
	</resultMap>
	
	<resultMap id="Result_CardB_BoardCutting_PartA_PartB" type="CardA">
		<id property="recordId" column="recordId" />
		<result property="templateId" column="templateId"  />
		<result property="no" column="no" />
		<result property="produceModel" column="produceModel" />
		<result property="produceModelCopy" column="produceModelCopy" />
		<result property="noticeNos" column="noticeNos" />
		<result property="customerNos" column="customerNos" />
		<result property="customerModels" column="customerModels"/>
		<result property="customerFileNames" column="customerFileNames"/>
		<result property="pcsWidth" column="pcsWidth"/>
		<result property="pcsLength" column="pcsLength"/>
		<result property="setWidth" column="setWidth" />
		<result property="setLength" column="setLength" />
		<result property="orderType" column="orderType"/>
		<result property="cardMadeDate" column="cardMadeDate"/>
		<result property="cancelCause" column="cancelCause"/>
		<result property="pnlDivisor" column="pnlDivisor"/>
		<result property="boardLevel" column="boardLevel" />
		<result property="pliesnumber" column="pliesnumber"/>
		<result property="finishedThickness" column="finishedThickness" />
		<result property="finishedTolerance" column="finishedTolerance" />
		<result property="innerThickness" column="innerThickness" />
		<result property="status" column="status" />
		<result property="activeFlag" column="activeFlag" />
		<result property="createdDate" column="createdDate"/>
		<result property="remark" column="remark" />
		<result property="progressDesc" column="progressDesc" />
		<result property="remark2" column="remark2" />
		<result property="feedingId" column="feedingId" />
		<result property="cardAuditDate" column="cardAuditDate" />
		<result property="testShelfSelected" column="testShelfSelected" />
		<result property="mouldSelected" column="mouldSelected" />
		<result property="arrangePlanDate" column="arrangePlanDate" />
		<result property="orderTypeValue" column="orderTypeValue" />
		<result property="deleteFlag" column="deleteFlag" />
		<result property="materialNo" column="materialNo" />
		<result property="secondMaterialNo" column="secondMaterialNo" />
		<result property="supShortName" column="supShortName" />
		<result property="saleName" column="saleName" />
		<result property="cusSaleName" column="cusSaleName" />
		<result property="saleUserName" column="saleUserName" />
		<result property="salesManUserName" column="salesManUserName" />
		<result property="feedCause" column="feedCause" />
		<result property="dictOrderType" column="dictOrderType" />
		<result property="notificationId" column="notificationId" />
		<result property="versionContent" column="versionContent" />
		<result property="defaultVersion" column="defaultVersion" />
		<result property="documentsStatus" column="documentsStatus" />
		<result property="materialIds" column="materialIds" />
		<result property="orderArea" column="orderArea" />
		<result property="mouldFee" column="mouldFee" />
		<result property="starAniseLength" column="starAniseLength"/>
		<result property="starAniseWidth" column="starAniseWidth"/>
		<result property="projectStatus" column="projectStatus"/>
		<result property="materialType" column="materialType"/>
		<result property="productTypeId" column="productTypeId"/>
		<result property="brandValue" column="brandValue"/>
		<result property="setAreaRatio" column="setAreaRatio"/>
		<result property="wasteArea" column="wasteArea"/>
		<result property="causewayLayer" column="causewayLayer"/>
		<result property="borehole" column="borehole"/>
		<result property="urgentFlag" column="urgentFlag"/>
		<result property="materialUse" column="materialUse"/>
		<result property="version" column="version"/>

		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
		</association>
		
		<association property="material" column="materialId" javaType="Material">
			<id property="recordId" column="material.recordId"/>
			<id property="no" column="material.no"/>
			<id property="stocks" column="material.stocks"/>
		</association>
		
		<association property="createdBy" column="createdBy" javaType="User">
			<id property="recordId" column="createdBy.recordId"/>
			<id property="userName" column="createdBy.userName"/>
		</association>
		
		<association property="cardAuditUser" column="cardAuditUser" javaType="User">
			<id property="recordId" column="cardAuditUser.recordId"/>
			<id property="userName" column="cardAuditUser.userName"/>
		</association>
		
		<association property="cardB" javaType="CardB">
			<id property="recordId" column="cardB.recordId" />
			<result property="firstDrillATotal" column="cardB.firstDrillATotal"  />
			<result property="firstDrillBTotal" column="cardB.firstDrillBTotal" />
			<result property="secondDrillATotal" column="cardB.secondDrillATotal" />
			<result property="secondDrillBTotal" column="cardB.secondDrillBTotal" />
			<result property="remark" column="cardB.remark" />
	        
	        <association property="cardA" javaType="CardA">
				<id property="recordId" column="recordId"/>
			</association>
			
	        <association property="boardCutting" javaType="BoardCutting">
				<id property="recordId" column="boardCutting.recordId" />
				<result property="boardLength" column="boardCutting.boardLength" />
				<result property="boardWidth" column="boardCutting.boardWidth" />
				<result property="boardSketchMapPath" column="boardCutting.boardSketchMapPath" />
				<result property="pcsesPerSet" column="boardCutting.pcsesPerSet" />
				<result property="totalSets" column="boardCutting.totalSets" />
				<result property="totalPcses" column="boardCutting.totalPcses" />
				<result property="useRatio" column="boardCutting.useRatio" />

				<association property="partA" javaType="BoardPart">
				    <id property="recordId" column="partA.recordId"/>
					<result property="pegLocateSide" column="partA.pegLocateSide" />
					<result property="partLength" column="partA.partLength" />
					<result property="partWidth" column="partA.partWidth" />
					<result property="pnlLength" column="partA.pnlLength" />
					<result property="pnlWidth" column="partA.pnlWidth" />
					<result property="pnlCount" column="partA.pnlCount" />
					<result property="horizontalSetLength" column="partA.horizontalSetLength" />
					<result property="horizontalSetCount" column="partA.horizontalSetCount" />
					<result property="horizontalGap" column="partA.horizontalGap" />
					<result property="horizonMargin" column="partA.horizonMargin" />
					<result property="verticalSetLength" column="partA.verticalSetLength" />
					<result property="verticalSetCount" column="partA.verticalSetCount" />
					<result property="verticalGap" column="partA.verticalGap" />
					<result property="verticalMargin" column="partA.verticalMargin" />
					<result property="totalSetCount" column="partA.totalSetCount" />
					<result property="pcsCount" column="partA.pcsCount" />
					<result property="sketchMapPath" column="partA.sketchMapPath" />
		       	</association> 
		       	
	       		<association property="partB" javaType="BoardPart">
		       		<id property="recordId" column="partB.recordId"/>
					<result property="pegLocateSide" column="partB.pegLocateSide" />
					<result property="partLength" column="partB.partLength" />
					<result property="partWidth" column="partB.partWidth" />
					<result property="pnlLength" column="partB.pnlLength" />
					<result property="pnlWidth" column="partB.pnlWidth" />
					<result property="pnlCount" column="partB.pnlCount" />
					<result property="horizontalSetLength" column="partB.horizontalSetLength" />
					<result property="horizontalSetCount" column="partB.horizontalSetCount" />
					<result property="horizontalGap" column="partB.horizontalGap" />
					<result property="horizonMargin" column="partB.horizonMargin" />
					<result property="verticalSetLength" column="partB.verticalSetLength" />
					<result property="verticalSetCount" column="partB.verticalSetCount" />
					<result property="verticalGap" column="partB.verticalGap" />
					<result property="verticalMargin" column="partB.verticalMargin" />
					<result property="totalSetCount" column="partB.totalSetCount" />
					<result property="pcsCount" column="partB.pcsCount" />
					<result property="sketchMapPath" column="partB.sketchMapPath" />
		       	</association> 
			</association>
			<collection property="attachList" 
			    column="{cardB.recordId=cardB.recordId, company.recordId=company.recordId}" 
			    select="com.kyb.pcberp.modules.eg.dao.CardBAttachDao.findListByCardB"></collection>
		</association>
		
		<collection property="processValueList" 
       		column="{cardA.recordId=recordId, company.recordId=company.recordId}" 
       		select="com.kyb.pcberp.modules.eg.dao.CardAProcessValueDao.findListByCardA" />
		
		<collection property="boardPartCraftList" 
       		column="{cardA.recordId=recordId, company.recordId=company.recordId}" 
       		select="com.kyb.pcberp.modules.eg.dao.BoardPartCraftDao.findListByCardA" /> 
	</resultMap>
	
	<resultMap id="Result_CardA_BoardPartCraft" type="CardA">
		<id property="recordId" column="recordId" />
		
		<collection property="boardPartCraftList" 
       		column="{cardA.recordId=recordId, company.recordId=company.recordId}" 
       		select="com.kyb.pcberp.modules.eg.dao.BoardPartCraftDao.findListByCardA" /> 
	</resultMap>
	
	<resultMap id="Result_CardA_BoardPartCraftList" type="CardA">
		<id property="recordId" column="recordId" />
		<result property="no" column="no" />
		<result property="produceModel" column="produceModel" />
		<result property="noticeNos" column="noticeNos" />
		<result property="customerNos" column="customerNos" />
		<result property="customerModels" column="customerModels"/>
		<result property="customerFileNames" column="customerFileNames"/>
		<result property="pcsWidth" column="pcsWidth"/>
		<result property="pcsLength" column="pcsLength"/>
		<result property="setWidth" column="setWidth" />
		<result property="setLength" column="setLength" />
		<result property="orderType" column="orderType"/>
		<result property="cardMadeDate" column="cardMadeDate"/>
		<result property="cancelCause" column="cancelCause"/>
		<result property="pnlDivisor" column="pnlDivisor"/>
		<result property="boardLevel" column="boardLevel" />
		<result property="pliesnumber" column="pliesnumber"/>
		<result property="finishedThickness" column="finishedThickness" />
		<result property="finishedTolerance" column="finishedTolerance" />
		<result property="innerThickness" column="innerThickness" />
		
		<collection property="boardPartCraftList" 
       		column="{cardA.recordId=recordId, company.recordId=company.recordId}" 
       		select="com.kyb.pcberp.modules.eg.dao.BoardPartCraftDao.findListByCardA" /> 
	</resultMap>
	
	<select id="getBoardPartCraftListById" resultMap="Result_CardA_BoardPartCraft">
		SELECT 
			a.recordId as "recordId",
			a.companyId AS "company.recordId"
		FROM eg_carda a
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="getProcessNotification" resultType="CardA">
		SELECT a.recordId as "recordId",a.status as 'status' from eg_carda a 
		WHERE a.noticeNos=#{noticeNos} and a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	
	<select id="getCardaAndBoardPartCraftListById" resultMap="Result_CardA_BoardPartCraftList">
		SELECT 
			a.recordId,
			a.companyId AS "company.recordId",
			a.no,
			a.produceModel,
			a.noticeNos,
			a.customerNos,
			a.customerModels,
			a.customerFileNames,
			a.pcsWidth,
			a.pcsLength,
			a.setWidth,
			a.setLength,
			a.orderType,
			a.cardMadeDate,
			a.cancelCause,
			a.pnlDivisor,
			a.boardLevel,
			a.finishedThickness,
			a.finishedTolerance,
			a.innerThickness,
			a.pliesnumber
		FROM eg_carda a
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="get" resultType="CardA">
		SELECT 
			<include refid="cardAColumns"/>
		FROM eg_carda a
		<include refid="cardAJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="getByNotification" resultType="CardA">
		SELECT 
			<include refid="cardAColumns"/>
		FROM eg_carda a 
		join sl_notification n on n.processCardAId = a.recordId
		WHERE a.companyId = #{company.recordId} and n.recordId = #{recordId}
	</select>

	<select id="getByNoticeNos" resultType="java.lang.Integer" >
	    SELECT count(1) FROM eg_carda a
		WHERE a.companyId = #{company.recordId} AND a.noticeNos = #{noticeNos} AND a.activeFlag = #{DEL_FLAG_NORMAL}
		<if test="recordId != null and recordId != ''">
			AND a.recordId <![CDATA[<>]]> #{recordId}
		</if>
	</select>
	
	<select id="getById" resultMap="Result_CardB_ProcessValue">
		SELECT 
			<include refid="cardAColumns"/>,
			su.userName AS "createdBy.userName"
		FROM eg_carda a
		<include refid="cardAJoins"/>
		LEFT JOIN sm_user su on su.recordId = a.createdBy
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="getById1" resultMap="Result_CardB_BoardCutting_PartA_PartB">
		SELECT 
			a.recordId,
			a.companyId AS "company.recordId",
			a.templateId,
			a.no,
			a.produceModel,
			a.noticeNos,
			a.customerNos,
			a.customerModels,
			a.customerFileNames,
			a.pcsWidth,
			a.pcsLength,
			a.setWidth,
			a.setLength,
			a.orderType,
			a.cardMadeDate,
			a.cancelCause,
			a.pnlDivisor,
			a.boardLevel,
			a.finishedThickness,
			a.finishedTolerance,
			a.innerThickness,
			a.status,
			a.activeFlag,
			a.createdBy AS "createdBy.recordId",
			a.createdDate,
			a.remark,
			a.progressDesc,
			a.cardAuditUser as "cardAuditUser.recordId",
			a.cardAuditDate,
			a.pliesnumber,
			a.produceModelCopy,
		
			a.processCardBId AS "cardB.recordId",
			cb.firstDrillATotal AS "cardB.firstDrillATotal",
			cb.firstDrillBTotal AS "cardB.firstDrillBTotal",
			cb.secondDrillATotal AS "cardB.secondDrillATotal",
			cb.secondDrillBTotal AS "cardB.secondDrillBTotal",
			cb.remark AS "cardB.remark",

			cb.boardCuttingId AS "boardCutting.recordId",
			cbc.boardLength AS "boardCutting.boardLength",
			cbc.boardWidth AS "boardCutting.boardWidth",
			cbc.boardSketchMapPath AS "boardCutting.boardSketchMapPath",
			cbc.pcsesPerSet AS "boardCutting.pcsesPerSet",
			cbc.totalSets AS "boardCutting.totalSets",
			cbc.totalPcses AS "boardCutting.totalPcses",
			cbc.useRatio AS "boardCutting.useRatio",
			
			cbca.recordId AS "partA.recordId",
			cbca.pegLocateSide AS "partA.pegLocateSide",
			cbca.partLength AS "partA.partLength",
			cbca.partWidth AS "partA.partWidth",
			cbca.pnlLength AS "partA.pnlLength",
			cbca.pnlWidth AS "partA.pnlWidth",
			cbca.pnlCount AS "partA.pnlCount",
			cbca.horizontalSetLength AS "partA.horizontalSetLength",
			cbca.horizontalSetCount AS "partA.horizontalSetCount",
			cbca.horizontalGap AS "partA.horizontalGap",
			cbca.horizonMargin AS "partA.horizonMargin",
			cbca.verticalSetLength AS "partA.verticalSetLength",
			cbca.verticalSetCount AS "partA.verticalSetCount",
			cbca.verticalGap AS "partA.verticalGap",
			cbca.verticalMargin AS "partA.verticalMargin",
			cbca.totalSetCount AS "partA.totalSetCount",
			cbca.pcsCount AS "partA.pcsCount",
			cbca.sketchMapPath AS "partA.sketchMapPath",

			cbcb.recordId AS "partB.recordId",
			cbcb.pegLocateSide AS "partB.pegLocateSide",
			cbcb.partLength AS "partB.partLength",
			cbcb.partWidth AS "partB.partWidth",
			cbcb.pnlLength AS "partB.pnlLength",
			cbcb.pnlWidth AS "partB.pnlWidth",
			cbcb.pnlCount AS "partB.pnlCount",
			cbcb.horizontalSetLength AS "partB.horizontalSetLength",
			cbcb.horizontalSetCount AS "partB.horizontalSetCount",
			cbcb.horizontalGap AS "partB.horizontalGap",
			cbcb.horizonMargin AS "partB.horizonMargin",
			cbcb.verticalSetLength AS "partB.verticalSetLength",
			cbcb.verticalSetCount AS "partB.verticalSetCount",
			cbcb.verticalGap AS "partB.verticalGap",
			cbcb.verticalMargin AS "partB.verticalMargin",
			cbcb.totalSetCount AS "partB.totalSetCount",
			cbcb.pcsCount AS "partB.pcsCount",
			cbcb.sketchMapPath AS "partB.sketchMapPath",
			su.userName AS "createdBy.userName",
			su1.userName AS "cardAuditUser.userName",
			(
				SELECT
					bb.`no`
				FROM
					sl_notification_material aa
				LEFT JOIN md_material bb ON bb.recordId = aa.materialId
				WHERE
					aa.notifiId = b.recordId
				AND aa.activeFlag = 1
				LIMIT 1
			) AS "secondMaterialNo",
			GROUP_CONCAT(DISTINCT IFNULL(IF(e.manufacturer IS NOT NULL AND e.manufacturer <![CDATA[<>]]> '',e.manufacturer,NULL),h.`value`)) AS "brandValue",
			e.urgentFlag AS "urgentFlag",
		  	e.materialUse AS "materialUse",
		  	i.`no` AS "materialNo"
		FROM eg_carda a
	  	JOIN eg_cardb cb on cb.recordId = a.processCardBId and cb.companyId = a.companyId 
		JOIN eg_cardb_board_cutting cbc on cbc.recordId = cb.boardCuttingId and cbc.companyId = a.companyId
		JOIN eg_cardb_board_part cbca on cbca.recordId = cbc.partAId and cbca.companyId = a.companyId
		JOIN eg_cardb_board_part cbcb on cbcb.recordId = cbc.partBId and cbcb.companyId = a.companyId
		LEFT JOIN sm_user su on su.recordId = a.createdBy
		LEFT JOIN sm_user su1 on su1.recordId = a.cardAuditUser
		LEFT JOIN sl_notification b ON b.`no` = a.noticeNos AND b.activeFlag = 1 AND b.companyId = a.companyId
		LEFT JOIN sl_notification c ON c.mergeId = b.recordId AND c.companyId = a.companyId AND c.activeFlag = 1
		LEFT JOIN sl_contract_detail d ON d.recordId = IFNULL(c.contractDetailId,b.contractDetailId)
		LEFT JOIN icloud_group_center e ON e.recordId = d.groupCenterId

		LEFT JOIN md_material f ON f.recordId = IFNULL(c.materialId,b.materialId)
		LEFT JOIN md_material_specification_relation g ON g.materialId = f.recordId AND g.activeFlag = 1
		AND g.companyId = a.companyId AND g.specificationId = (SELECT recordId FROM md_material_specification WHERE companyId = a.companyId AND activeFlag = 1 AND dictItemId = 70)
		LEFT JOIN md_dict_value h ON h.recordId = g.`value`
		LEFT JOIN md_material i ON i.recordId = e.materialId
		WHERE a.recordId = #{recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
		GROUP BY a.recordId
	</select>
	
	<select id="getCardBById" resultMap="Result_CardB">
		SELECT a.recordId as "recordId", a.processCardBId AS "cardB.recordId",a.companyId AS "company.recordId",a.activeFlag FROM eg_carda a WHERE a.recordId = #{recordId}
	</select>
	
	<select id="getCardAForNotication" resultMap="Result_CardB_ProcessValue">
		SELECT 
			<include refid="cardAColumns"/>,
			su.userName AS "createdBy.userName"
		FROM eg_carda a
		<include refid="cardAJoins"/>
		LEFT JOIN sm_user su on su.recordId = a.createdBy
		WHERE a.recordId = #{recordId}
			AND a.companyId = #{company.recordId} 
			AND a.activeFlag = #{activeFlag}
	</select>
	
	<select id="getById_ProcessValue" resultMap="Result_ProcessValue">
		SELECT 
			<include refid="cardAColumns"/>
		FROM eg_carda a
		<include refid="cardAJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	<resultMap id="Result_ForAutoAdd" type="CardA">
		<id property="recordId" column="recordId" />
		<result property="templateId" column="templateId"  />
		<result property="no" column="no" />
		<result property="produceModel" column="produceModel" />
		<result property="noticeNos" column="noticeNos" />
		<result property="customerNos" column="customerNos" />
		<result property="customerModels" column="customerModels"/>
		<result property="customerFileNames" column="customerFileNames"/>
		<result property="pcsWidth" column="pcsWidth"/>
		<result property="pcsLength" column="pcsLength"/>
		<result property="setWidth" column="setWidth" />
		<result property="setLength" column="setLength" />
		<result property="cardMadeDate" column="cardMadeDate"/>
		<result property="pnlDivisor" column="pnlDivisor"/>
		<result property="boardLevel" column="boardLevel" />
		<result property="finishedThickness" column="finishedThickness" />
		<result property="finishedTolerance" column="finishedTolerance" />
		<result property="innerThickness" column="innerThickness" />
		<result property="status" column="status" />
		<result property="activeFlag" column="activeFlag" />
		<result property="progressDesc" column="progressDesc" />
		<result property="remark" column="remark" />
		<result property="pliesnumber" column="pliesnumber" />
		<result property="cancelCause" column="cancelCause" />
		<result property="remark2" column="remark2" />
		<result property="materialIds" column="materialIds" />
		<result property="starAniseLength" column="starAniseLength"/>
		<result property="starAniseWidth" column="starAniseWidth"/>
		<result property="projectStatus" column="projectStatus"/>
		<result property="orderTypeValue" column="orderTypeValue"/>
		<result property="setAreaRatio" column="setAreaRatio"/>
		<result property="wasteArea" column="wasteArea"/>
		<result property="causewayLayer" column="causewayLayer"/>
		<result property="borehole" column="borehole"/>

		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
		</association>
		
		<association property="createdBy" column="createdBy" javaType="User">
			<id property="recordId" column="createdBy.recordId"/>
		</association>
		
		<association property="lastUpdBy" column="lastUpdBy" javaType="User">
			<id property="recordId" column="lastUpdBy.recordId"/>
		</association>
		
		<association property="material" column="materialId" javaType="Material">
			<id property="recordId" column="material.recordId"/>
		</association>
		
		<association property="cardB" javaType="CardB">
			<id property="recordId" column="cardB.recordId" />
			<result property="firstDrillATotal" column="cardB.firstDrillATotal"  />
			<result property="firstDrillBTotal" column="cardB.firstDrillBTotal" />
			<result property="secondDrillATotal" column="cardB.secondDrillATotal" />
			<result property="secondDrillBTotal" column="cardB.secondDrillBTotal" />

	        <association property="boardCutting" javaType="BoardCutting">
				<id property="recordId" column="boardCutting.recordId" />
				<result property="boardLength" column="boardCutting.boardLength" />
				<result property="boardWidth" column="boardCutting.boardWidth" />
				<result property="pcsesPerSet" column="boardCutting.pcsesPerSet" />
				<result property="totalSets" column="boardCutting.totalSets" />
				<result property="totalPcses" column="boardCutting.totalPcses" />
				<result property="useRatio" column="boardCutting.useRatio" />
				<result property="boardSketchMapPath" column="boardCutting.boardSketchMapPath" />

				<association property="partA" javaType="BoardPart">
				    <id property="recordId" column="partA.recordId" />
					<result property="pegLocateSide" column="partA.pegLocateSide" />
					<result property="partLength" column="partA.partLength" />
					<result property="partWidth" column="partA.partWidth" />
					<result property="pnlLength" column="partA.pnlLength" />
					<result property="pnlWidth" column="partA.pnlWidth" />
					<result property="pnlCount" column="partA.pnlCount" />
					<result property="horizontalSetLength" column="partA.horizontalSetLength" />
					<result property="horizontalSetCount" column="partA.horizontalSetCount" />
					<result property="horizontalGap" column="partA.horizontalGap" />
					<result property="horizonMargin" column="partA.horizonMargin" />
					<result property="verticalSetLength" column="partA.verticalSetLength" />
					<result property="verticalSetCount" column="partA.verticalSetCount" />
					<result property="verticalGap" column="partA.verticalGap" />
					<result property="verticalMargin" column="partA.verticalMargin" />
					<result property="totalSetCount" column="partA.totalSetCount" />
					<result property="sketchMapPath" column="partA.sketchMapPath" />
					<result property="pcsCount" column="partA.pcsCount" />
		       	</association> 
		       	
	       		<association property="partB" javaType="BoardPart">
	       		    <id property="recordId" column="partB.recordId" />
					<result property="pegLocateSide" column="partB.pegLocateSide" />
					<result property="partLength" column="partB.partLength" />
					<result property="partWidth" column="partB.partWidth" />
					<result property="pnlLength" column="partB.pnlLength" />
					<result property="pnlWidth" column="partB.pnlWidth" />
					<result property="pnlCount" column="partB.pnlCount" />
					<result property="horizontalSetLength" column="partB.horizontalSetLength" />
					<result property="horizontalSetCount" column="partB.horizontalSetCount" />
					<result property="horizontalGap" column="partB.horizontalGap" />
					<result property="horizonMargin" column="partB.horizonMargin" />
					<result property="verticalSetLength" column="partB.verticalSetLength" />
					<result property="verticalSetCount" column="partB.verticalSetCount" />
					<result property="verticalGap" column="partB.verticalGap" />
					<result property="verticalMargin" column="partB.verticalMargin" />
					<result property="totalSetCount" column="partB.totalSetCount" />
					<result property="sketchMapPath" column="partB.sketchMapPath" />
					<result property="pcsCount" column="partB.pcsCount" />
		       	</association> 
			</association>
			<collection property="attachList" 
			    column="{cardB.recordId=cardB.recordId, company.recordId=company.recordId}" 
			    select="com.kyb.pcberp.modules.eg.dao.CardBAttachDao.findListByCardB"></collection>
		</association>
		<collection property="processValueList" 
       		column="{cardA.recordId=recordId, company.recordId=company.recordId}" 
       		select="com.kyb.pcberp.modules.eg.dao.CardAProcessValueDao.findListByCardA" /> 
	</resultMap>
	
	<select id="getByCraftNo" resultMap="Result_ForAutoAdd">
	    SELECT 
	    	a.recordId,
			a.companyId AS "company.recordId",
			a.templateId,
			a.no,
			a.produceModel,
			a.noticeNos,
			a.customerNos,
			a.customerModels,
			a.customerFileNames,
			a.pcsWidth,
			a.pcsLength,
			a.setWidth,
			a.setLength,
			a.cardMadeDate,
			a.pnlDivisor,
			a.boardLevel,
			a.finishedThickness,
			a.finishedTolerance,
			a.innerThickness,
			a.progressDesc,
			a.status,
			a.activeFlag,
			a.remark,
			a.createdBy AS "createdBy.recordId",
			a.createdDate,
			a.lastUpdBy AS "lastUpdBy.recordId",
			a.lastUpdDate,
			a.pliesnumber,
			a.cancelCause,
			a.remark2,
			a.materialId AS "material.recordId",
			a.materialIds AS "materialIds",
		
			cb.recordId as "cardB.recordId",
			cb.firstDrillATotal AS "cardB.firstDrillATotal",
			cb.firstDrillBTotal AS "cardB.firstDrillBTotal",
			cb.secondDrillATotal AS "cardB.secondDrillATotal",
			cb.secondDrillBTotal AS "cardB.secondDrillBTotal",
			
			cbc.recordId AS "boardCutting.recordId",
			cbc.boardLength AS "boardCutting.boardLength",
			cbc.boardWidth AS "boardCutting.boardWidth",
			cbc.pcsesPerSet AS "boardCutting.pcsesPerSet",
			cbc.totalSets AS "boardCutting.totalSets",
			cbc.totalPcses AS "boardCutting.totalPcses",
			cbc.useRatio AS "boardCutting.useRatio",
			cbc.boardSketchMapPath AS "boardCutting.boardSketchMapPath",
			
			cbca.recordId AS "partA.recordId",
			cbca.pegLocateSide AS "partA.pegLocateSide",
			cbca.partLength AS "partA.partLength",
			cbca.partWidth AS "partA.partWidth",
			cbca.pnlLength AS "partA.pnlLength",
			cbca.pnlWidth AS "partA.pnlWidth",
			cbca.pnlCount AS "partA.pnlCount",
			cbca.horizontalSetLength AS "partA.horizontalSetLength",
			cbca.horizontalSetCount AS "partA.horizontalSetCount",
			cbca.horizontalGap AS "partA.horizontalGap",
			cbca.horizonMargin AS "partA.horizonMargin",
			cbca.verticalSetLength AS "partA.verticalSetLength",
			cbca.verticalSetCount AS "partA.verticalSetCount",
			cbca.verticalGap AS "partA.verticalGap",
			cbca.verticalMargin AS "partA.verticalMargin",
			cbca.totalSetCount AS "partA.totalSetCount",
			cbca.sketchMapPath AS "partA.sketchMapPath",
			cbca.pcsCount AS "partA.pcsCount",

			cbcb.recordId AS "partB.recordId",
			cbcb.pegLocateSide AS "partB.pegLocateSide",
			cbcb.partLength AS "partB.partLength",
			cbcb.partWidth AS "partB.partWidth",
			cbcb.pnlLength AS "partB.pnlLength",
			cbcb.pnlWidth AS "partB.pnlWidth",
			cbcb.pnlCount AS "partB.pnlCount",
			cbcb.horizontalSetLength AS "partB.horizontalSetLength",
			cbcb.horizontalSetCount AS "partB.horizontalSetCount",
			cbcb.horizontalGap AS "partB.horizontalGap",
			cbcb.horizonMargin AS "partB.horizonMargin",
			cbcb.verticalSetLength AS "partB.verticalSetLength",
			cbcb.verticalSetCount AS "partB.verticalSetCount",
			cbcb.verticalGap AS "partB.verticalGap",
			cbcb.verticalMargin AS "partB.verticalMargin",
			cbcb.totalSetCount AS "partB.totalSetCount",
			cbcb.sketchMapPath AS "partB.sketchMapPath",
			cbcb.pcsCount AS "partB.pcsCount",
			a.starAniseLength AS "starAniseLength",
			a.starAniseWidth AS "starAniseWidth",
			a.projectStatus AS "projectStatus",
			b.value AS "orderTypeValue"
		FROM eg_carda a 
		JOIN eg_cardb cb on cb.recordId = a.processCardBId
		JOIN eg_cardb_board_cutting cbc on cbc.recordId = cb.boardCuttingId
		JOIN eg_cardb_board_part cbca on cbca.recordId = cbc.partAId
		JOIN eg_cardb_board_part cbcb on cbcb.recordId = cbc.partBId
		LEFT JOIN md_dict_value b ON b.recordId = a.orderType
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
		AND (FIND_IN_SET(REPLACE(#{produceModel}," ",""),REPLACE(a.produceModel," ","")) OR REPLACE(a.produceModel," ","") = REPLACE(#{produceModel}," ",""))
		AND (a.projectStatus IS NULL or a.projectStatus = "")
		ORDER BY a.defaultVersion DESC,a.createdDate DESC
		LIMIT 1
	</select>
	
	<resultMap id="Result_Mobile_Info" type="CardA">
		<id property="recordId" column="recordId" />
		<result property="no" column="no" />
		<result property="produceModel" column="produceModel" />
		<result property="noticeNos" column="noticeNos" />
		<result property="customerNos" column="customerNos" />
		<result property="customerModels" column="customerModels"/>
		<result property="produceBatchNo" column="produceBatchNo"/>
		<result property="produceBatchDetailNo" column="produceBatchDetailNo"/>
		<association property="createdBy" column="createdBy" javaType="User">
			<id property="recordId" column="createdBy.recordId"/>
			<result property="userName" column="createdBy.userName" />
		</association>
		
		<association property="cardB" javaType="CardB">
			<id property="recordId" column="cardB.recordId"/>
			
			<association property="boardCutting" javaType="BoardCutting">
			    <id property="recordId" column="boardCutting.recordId"/>
			    
			    <association property="partA" javaType="BoardPart">
			    	<id property="recordId" column="partA.recordId"/>
				</association> 
			
			    <association property="partB" javaType="BoardPart">
			    	<id property="recordId" column="partB.recordId"/>
				</association> 
			</association> 
       		
		</association>
	</resultMap>
	
	<select id="getInfoByBatch" resultMap="Result_Mobile_Info" parameterType="java.lang.String">
		SELECT 
			b.no AS "produceBatchNo",
			bd.no AS "produceBatchDetailNo",
			ca.recordId AS "recordId",
			ca.no AS "no",
			ca.produceModel AS "produceModel",
			ca.noticeNos AS "noticeNos",
			ca.customerNos AS "customerNos",
			ca.customerModels AS "customerModels",
		  	ca.createdBy AS "createdBy.recordId",
			u.userName AS "createdBy.userName",
			cb.recordId AS "cardB.recordId",
			cb.boardCuttingId AS "boardCutting.recordId",
			cbb.partAId AS "partA.recordId",
			cbb.partBId AS "partB.recordId"
		FROM pd_produce_batch b
		LEFT JOIN pd_produce_batch_detail bd on bd.recordId = #{batchDetailId} and bd.companyId = b.companyId
	  	JOIN eg_carda ca on ca.recordId = b.processCardAId and ca.companyId = b.companyId
    	LEFT JOIN sm_user u on u.recordId = ca.createdBy and u.companyId = b.companyId
		JOIN eg_cardb cb on cb.recordId = ca.processCardBId and cb.companyId = b.companyId
		JOIN eg_cardb_board_cutting cbb on cbb.recordId = cb.boardCuttingId and cbb.companyId = b.companyId	  
		WHERE b.recordId = #{batchId}
	</select>
	
	<select id="getBaseInfoById" resultType="CardA">
		SELECT 
			a.recordId,
			a.no,
			a.produceModel,
			a.noticeNos,
			a.customerNos,
			a.customerModels,
			a.customerFileNames,
			a.pcsWidth,
			a.pcsLength,
			a.setWidth,
			a.setLength,
			a.orderType,
			a.cardMadeDate,
			a.status,
			a.createdBy AS "createdBy.recordId",
			a.createdDate,
			a.remark,
			a.cancelCause,
			a.pnlDivisor,
			a.boardLevel,
			a.finishedThickness,
			a.finishedTolerance,
			a.innerThickness,
			a.pliesnumber
		FROM eg_carda a
		WHERE a.recordId = #{recordId}
	</select>

	<select id="findList" resultMap="Result_CardB_BoardCutting_PartA_PartB">
	    SELECT 
			a.recordId,
			a.companyId AS "company.recordId",
			a.templateId,
			a.no,
			a.produceModel,
			a.noticeNos,
			a.customerNos,
			a.customerModels,
			a.customerFileNames,
			a.pcsWidth,
			a.pcsLength,
			a.setWidth,
			a.setLength,
			a.orderType,
			a.cardMadeDate,
			a.cancelCause,
			a.pnlDivisor,
			a.boardLevel,
			a.finishedThickness,
			a.finishedTolerance,
			a.innerThickness,
			(CASE a.activeFlag WHEN #{DEL_FLAG_NORMAL} THEN a.status ELSE 2 END) AS "status",
			a.activeFlag,
			a.createdBy AS "createdBy.recordId",
			a.createdDate,
			a.remark,
			a.progressDesc,
			a.cardAuditUser as "cardAuditUser.recordId",
			a.cardAuditDate,
			a.pliesnumber,
			a.produceModelCopy,
			a.remark2,
			a.materialId AS "material.recordId",
			a.feedCause,
			a.versionContent AS "versionContent",
			a.defaultVersion AS "defaultVersion",
			a.version,
			ma.no AS "material.no",
			ma.specification AS "material.specification",
			ma.stocks AS "material.stocks",
			
			a.processCardBId AS "cardB.recordId",
			a.testShelfSelected,
			a.mouldSelected,
			a.materialIds,
			cb.firstDrillATotal AS "cardB.firstDrillATotal",
			cb.firstDrillBTotal AS "cardB.firstDrillBTotal",
			cb.secondDrillATotal AS "cardB.secondDrillATotal",
			cb.secondDrillBTotal AS "cardB.secondDrillBTotal",
			cb.remark AS "cardB.remark",
			
			cb.boardCuttingId AS "boardCutting.recordId",
			cbc.boardLength AS "boardCutting.boardLength",
			cbc.boardWidth AS "boardCutting.boardWidth",
			cbc.boardSketchMapPath AS "boardCutting.boardSketchMapPath",
			cbc.pcsesPerSet AS "boardCutting.pcsesPerSet",
			cbc.totalSets AS "boardCutting.totalSets",
			cbc.totalPcses AS "boardCutting.totalPcses",
			cbc.useRatio AS "boardCutting.useRatio",
			
			cbca.recordId AS "partA.recordId",
			cbca.pegLocateSide AS "partA.pegLocateSide",
			cbca.partLength AS "partA.partLength",
			cbca.partWidth AS "partA.partWidth",
			cbca.pnlLength AS "partA.pnlLength",
			cbca.pnlWidth AS "partA.pnlWidth",
			cbca.pnlCount AS "partA.pnlCount",
			cbca.horizontalSetLength AS "partA.horizontalSetLength",
			cbca.horizontalSetCount AS "partA.horizontalSetCount",
			cbca.horizontalGap AS "partA.horizontalGap",
			cbca.horizonMargin AS "partA.horizonMargin",
			cbca.verticalSetLength AS "partA.verticalSetLength",
			cbca.verticalSetCount AS "partA.verticalSetCount",
			cbca.verticalGap AS "partA.verticalGap",
			cbca.verticalMargin AS "partA.verticalMargin",
			cbca.totalSetCount AS "partA.totalSetCount",
			cbca.pcsCount AS "partA.pcsCount",
			cbca.sketchMapPath AS "partA.sketchMapPath",

			cbcb.recordId AS "partB.recordId",
			cbcb.pegLocateSide AS "partB.pegLocateSide",
			cbcb.partLength AS "partB.partLength",
			cbcb.partWidth AS "partB.partWidth",
			cbcb.pnlLength AS "partB.pnlLength",
			cbcb.pnlWidth AS "partB.pnlWidth",
			cbcb.pnlCount AS "partB.pnlCount",
			cbcb.horizontalSetLength AS "partB.horizontalSetLength",
			cbcb.horizontalSetCount AS "partB.horizontalSetCount",
			cbcb.horizontalGap AS "partB.horizontalGap",
			cbcb.horizonMargin AS "partB.horizonMargin",
			cbcb.verticalSetLength AS "partB.verticalSetLength",
			cbcb.verticalSetCount AS "partB.verticalSetCount",
			cbcb.verticalGap AS "partB.verticalGap",
			cbcb.verticalMargin AS "partB.verticalMargin",
			cbcb.totalSetCount AS "partB.totalSetCount",
			cbcb.pcsCount AS "partB.pcsCount",
			cbcb.sketchMapPath AS "partB.sketchMapPath",
			su.userName AS "createdBy.userName",
			su1.userName AS "cardAuditUser.userName",
			dv.value AS "orderTypeValue",
			a.activeFlag AS "deleteFlag",
			sns2.saleName AS "saleName",
			sns2.cusSaleName AS "cusSaleName",
			sns2.saleUserName AS "saleUserName",
			sns2.salesManUserName AS "salesManUserName",
			ns.recordId notificationId,
			CASE IFNULL(ns2.inspectType,0) WHEN 0 THEN '正常'
			WHEN 1 THEN '补料'
			WHEN 2 THEN '客诉'
			WHEN 3 THEN '客诉'
			WHEN 4 THEN '补料'
			ELSE NULL END AS "documentsStatus",
			dv.orderType AS "dictOrderType",

			IFNULL(sns2.orderDeailArea,sd.orderDeailArea) AS "orderArea",
			IFNULL(sns2.mouldFee,sp.mouldFee) AS "mouldFee",
			a.starAniseLength,
			a.starAniseWidth,
			a.projectStatus,
			dv.materialType AS "materialType",
			a.productTypeId,
		        a.setAreaRatio,
		        a.wasteArea,
			a.causewayLayer AS "causewayLayer",
			a.borehole AS "borehole"
		FROM eg_carda a
		LEFT JOIN eg_cardb cb on cb.recordId = a.processCardBId and cb.companyId = a.companyId
		LEFT JOIN eg_cardb_board_cutting cbc on cbc.recordId = cb.boardCuttingId and cbc.companyId = a.companyId
		LEFT JOIN eg_cardb_board_part cbca on cbca.recordId = cbc.partAId and cbca.companyId = a.companyId
		LEFT JOIN eg_cardb_board_part cbcb on cbcb.recordId = cbc.partBId and cbcb.companyId = a.companyId
		LEFT JOIN sm_user su on su.recordId = a.createdBy
		LEFT JOIN sm_user su1 on su1.recordId = a.cardAuditUser
		LEFT JOIN md_dict_value dv ON dv.recordId = a.orderType
		LEFT JOIN md_material ma ON ma.recordId = a.materialId AND ma.activeFlag = 1
		LEFT JOIN sl_notification sn ON sn.processCardAId = a.recordId AND sn.activeFlag = 1
		LEFT JOIN sl_notification_scheduling ns ON sn.recordId = ns.notificationId AND ns.activeFlag = 1
		LEFT JOIN sl_contract_detail ns2 ON sn.contractDetailId = ns2.recordId
		LEFT JOIN icloud_group_center gf ON gf.recordId = ns2.groupCenterId
		LEFT JOIN sl_contract_detail sd ON sd.recordId = gf.contactDeailId
		LEFT JOIN sl_price sp ON  sp.recordId = sd.priceId
		LEFT JOIN
		(
			SELECT
				IFNULL(ff.shortName,dd.shortName) AS "saleName",
			    CONCAT(IFNULL(ee.`no`,gg.`no`),':',IFNULL(ee.shortName,gg.shortName)) AS "cusSaleName",
			    kk.userName AS "saleUserName",
			    ll.userName AS "salesManUserName",
				aa.processCardAId AS "processCardAId",
				SUM(IFNULL(bb.orderDeailArea,0)) AS "orderDeailArea",
				SUM(IFNULL(sp2.mouldFee,0)) AS "mouldFee"
			FROM sl_notification aa
			LEFT JOIN sl_contract_detail bb ON bb.recordId = aa.contractDetailId
			LEFT JOIN sl_price sp2 ON sp2.recordId = bb.priceId
			LEFT JOIN sl_contract cc ON cc.recordId = bb.contractId
			LEFT JOIN md_branch dd ON dd.recordId = cc.branchId
			LEFT JOIN md_branch ff ON ff.recordId = aa.saleId
			LEFT JOIN md_customer gg ON gg.recordId = aa.saleCsutomerId
			LEFT JOIN icloud_group_center hh ON hh.recordId = bb.groupCenterId
			LEFT JOIN sl_contract_detail ii ON ii.recordId = hh.contactDeailId
			LEFT JOIN sl_contract jj ON jj.recordId = ii.contractId
            LEFT JOIN md_customer ee ON ee.recordId = jj.customerId
			LEFT JOIN sm_user kk ON kk.recordId = jj.createdBy
			LEFT JOIN sm_user ll ON ll.recordId = IFNULL(jj.userId,gg.salesman)
			WHERE aa.companyId = #{company.recordId} AND aa.activeFlag = 1 AND aa.`status` <![CDATA[<>]]> 200406
			AND aa.processCardAId IN
			(
				SELECT recordId FROM eg_carda WHERE companyId = #{company.recordId} AND activeFlag = 1 AND cardMadeDate <![CDATA[>=]]> #{sentTimeStartQr}
				AND cardMadeDate <![CDATA[<=]]> #{sentTimeEndQr}
			)
			GROUP BY aa.processCardAId
		) sns2 ON sns2.processCardAId = a.recordId
		<where>
			a.companyId = #{company.recordId}
			<if test="produceModel != null and produceModel != ''">
				AND (REPLACE(a.produceModel," ","") LIKE CONCAT('%', REPLACE(#{produceModel}," ",""), '%')  OR REPLACE(a.produceModelCopy," ","") LIKE CONCAT('%', REPLACE(#{produceModel}," ",""), '%'))
			</if>
			<if test="customerModels != null and customerModels != ''">
				AND REPLACE(a.customerModels," ","") LIKE CONCAT('%', REPLACE(#{customerModels}," ",""), '%')
			</if>
			<if test="pcsDimensionLength != null and pcsDimensionLength != ''">
				AND a.pcsLength = #{pcsDimensionLength}
			</if>
			<if test="pcsDimensionWidth != null and pcsDimensionWidth != ''">
				AND a.pcsWidth = #{pcsDimensionWidth}
			</if>
			<if test="setLength != null and setLength != ''">
				AND a.setLength = #{setLength}
			</if>
			<if test="setWidth != null and setWidth != ''">
				AND a.setWidth = #{setWidth}
			</if>
			<if test="noticeNos != null and noticeNos != ''">
				AND REPLACE(a.noticeNos," ","") LIKE CONCAT('%', REPLACE(#{noticeNos}," ",""), '%')
			</if>
			<if test="createByName != null and createByName != ''">
				AND REPLACE(su.userName," ","") LIKE CONCAT('%', REPLACE (#{createByName}, ' ', ''), '%')
			</if>
			<if test="auditByName != null and auditByName != ''">
				AND REPLACE(su1.userName," ","") LIKE CONCAT('%', REPLACE (#{auditByName}, ' ', ''), '%')
			</if>
			<if test="cardStatus == 1">
				AND a.activeFlag = #{DEL_FLAG_NORMAL}
			</if>
			<if test="cardStatus == 2">
				AND a.activeFlag <![CDATA[<>]]> #{DEL_FLAG_NORMAL}
			</if>
			<if test="status != null and status != ''">
				<if test="status != -1">
					AND a.status = #{status}
				</if>
			</if>
			AND (cbc.useRatio > 0 OR a.activeFlag = 1)
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} or a.lastUpdBy = #{createdBy.recordId} 
				OR a.recordId IN (SELECT	noti.processCardAId FROM sl_notification noti JOIN sl_contract_detail contdeatil
				ON noti.contractDetailId = contdeatil.recordId JOIN sl_contract cont ON cont.recordId = contdeatil.contractId
				WHERE cont.userId =  #{createdBy.recordId}  
		   		or cont.customerId in (SELECT cs.customerId from sm_customer_salesAssistant cs WHERE cs.activeFlag = #{DEL_FLAG_NORMAL} AND cs.userId = #{createdBy.recordId})
				  ))
			</if>
			<!-- tj 2019-06-24 生产类型查询  -->
			<if test="orderTypeValue != null and orderTypeValue != ''">
				AND FIND_IN_SET(dv.value,#{orderTypeValue})
			</if>
			<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
				AND a.cardMadeDate <![CDATA[>=]]> #{sentTimeStartQr}
			</if>
			<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
				AND a.cardMadeDate <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
			<if test="materialTypeId != null and materialTypeId != ''">
				AND a.recordId IN 
				(
					SELECT
						sn.processCardAId
					FROM sl_notification sn
					LEFT JOIN sl_contract_detail scd ON scd.recordId = sn.contractDetailId
					LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId
					WHERE sn.companyId = #{company.recordId}  AND sn.activeFlag = 1 AND sn.`status` <![CDATA[<>]]> 200406
					AND scc.materialType = #{materialTypeId} AND sn.processCardAId IS NOT NULL
				)
			</if>
			<if test="saleName != null and saleName != ''">
				AND REPLACE(sns2.saleName," ","") LIKE CONCAT('%', REPLACE(#{saleName}," ",""), '%')
			</if>
			<if test="cusSaleName != null and cusSaleName != ''">
				AND REPLACE(sns2.cusSaleName," ","") LIKE CONCAT('%', REPLACE(#{cusSaleName}," ",""), '%')
			</if>
			<if test="documentsStatus != null and documentsStatus != ''">
				<!-- 正常 -->
				<if test="documentsStatus == 1">
					AND ns2.inspectType IS NULL
				</if>
				<!-- 补料 -->
				<if test="documentsStatus == 2">
					AND ns2.inspectType IN (1,4)
				</if>
				<!-- 客诉 -->
				<if test="documentsStatus == 3">
					AND ns2.inspectType IN (2,3)
				</if>
			</if>
			<if test="processName != null and processName != ''">
				AND a.templateId IN
				(
					SELECT
						a.templateId
					FROM eg_template_process_relation a
					LEFT JOIN eg_process b ON b.recordId = a.processId
					LEFT JOIN eg_template c ON c.recordId = a.templateId
					WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND c.showFlag = 1
					AND FIND_IN_SET(REPLACE(b.category," ",""),#{processName})
				)
			</if>
	    	<if test="projectStatus != null and projectStatus !=''">
				<if test="projectStatus == 1">
					AND a.projectStatus IS NULL
				</if>
				<if test="projectStatus == 2">
					AND a.projectStatus = 1
				</if>
			</if>
			GROUP BY a.recordId
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="CardA">
		SELECT 
			<include refid="cardAColumns"/>
		FROM eg_carda a
		<include refid="cardAJoins"/>
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
  
	<select id="findAllForClearData" resultType="CardA">
		SELECT 
			a.recordId,
			a.processCardBId AS "cardB.recordId"
		FROM eg_carda a
		WHERE a.companyId = 1
	</select>
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO eg_carda(
			companyId,
			templateId,
			processCardBId,
			no,
			produceModel,
			noticeNos,
			customerNos,
			customerModels,
			customerFileNames,
			pcsWidth,
			pcsLength,
			setWidth,
			setLength,
			orderType,
			cardMadeDate,
			progressDesc,
			status,
			activeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			remark,
			cancelCause,
			pnlDivisor,
			boardLevel,
			finishedThickness,
			finishedTolerance,
			innerThickness,
			pliesnumber,
			produceModelCopy,
			remark2,
			materialId,
			testShelfSelected,
			mouldSelected,
			materialIds,
			starAniseLength,
			starAniseWidth,
			projectStatus,
			productTypeId,
			wasteArea,
			setAreaRatio,
			causewayLayer,
			borehole,
			version
		) VALUES (
			#{company.recordId},
			#{templateId},
			#{cardB.recordId},
			#{no},
			#{produceModel},
			#{noticeNos},
			#{customerNos},
			#{customerModels},
			#{customerFileNames},
			#{pcsWidth},
			#{pcsLength},
			#{setWidth},
			#{setLength},
			#{orderType},
			#{cardMadeDate},
			#{progressDesc},
			#{status},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{cancelCause},
			#{pnlDivisor},
			#{boardLevel},
			#{finishedThickness},
			#{finishedTolerance},
			#{innerThickness},
			#{pliesnumber},
			#{produceModelCopy},
			#{remark2},
			#{material.recordId},
			#{testShelfSelected},
			#{mouldSelected},
			#{materialIds},
			#{starAniseLength},
			#{starAniseWidth},
			#{projectStatus},
			#{productTypeId},
			#{wasteArea},
			#{setAreaRatio},
			#{causewayLayer},
			#{borehole},
			IFNULL(#{version},0)
		)
	</insert>
	
	<update id="update">
		UPDATE eg_carda SET 	
			companyId = #{company.recordId},
			templateId = #{templateId},
			processCardBId = #{cardB.recordId},
			no = #{no},
			produceModel = #{produceModel},
			noticeNos = #{noticeNos},
			customerNos = #{customerNos},
			customerModels = #{customerModels},
			customerFileNames = #{customerFileNames},
			pcsWidth = #{pcsWidth},
			pcsLength = #{pcsLength},
			setWidth = #{setWidth},
			setLength = #{setLength},
			orderType = #{orderType},
			cardMadeDate = #{cardMadeDate},
			status = #{status},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			remark = #{remark},
			cancelCause = #{cancelCause},
			pnlDivisor = #{pnlDivisor},
			boardLevel = #{boardLevel},
			finishedThickness = #{finishedThickness},
			finishedTolerance = #{finishedTolerance},
			innerThickness = #{innerThickness},
			pliesnumber = #{pliesnumber},
			produceModelCopy = #{produceModelCopy},
			progressDesc = #{progressDesc},
			materialId = #{material.recordId},
			testShelfSelected = #{testShelfSelected},
			mouldSelected = #{mouldSelected},
			materialIds = #{materialIds},
			starAniseLength = #{starAniseLength},
			starAniseWidth = #{starAniseWidth},
			projectStatus = #{projectStatus},
			productTypeId = #{productTypeId},
			wasteArea = #{wasteArea},
			setAreaRatio = #{setAreaRatio},
			causewayLayer = #{causewayLayer},
			borehole = #{borehole},
			version = IFNULL(#{version},0) + 1
		WHERE recordId = #{recordId} AND version = #{version}
	</update>
	
	<update id="updateCardA">
		UPDATE eg_carda SET 	
			<if test="materialId != null and materialId != ''">
				materialId = #{materialId},
			</if>
			no = #{no},
			produceModel = #{produceModel},
			noticeNos = #{noticeNos},
			customerNos = #{customerNos},
			customerModels = #{customerModels},
			customerFileNames = #{customerFileNames},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			remark = #{remark}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateStatus">
		UPDATE eg_carda SET 	
			status = #{status},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateCardStatus">
		UPDATE eg_carda SET 	
			status = #{status}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="confirm">
		UPDATE eg_carda SET 	
			status = #{status},
			cardAuditUser = #{cardAuditUser.recordId},
			cardAuditDate = #{cardAuditDate},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="cancelConfirm">
		UPDATE eg_carda SET 	
			status = #{status},
			cancelCause = #{cancelCause},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE eg_carda SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>
	
	<delete id="deleteCardAProcessValue">
		UPDATE eg_carda_process_value SET
			activeFlag = 0
		WHERE processCardAId = #{recordId} AND companyId = #{company.recordId} AND activeFlag = 1
	</delete>
	
	<select id="getFeedingCount" resultType="java.lang.Integer" >
	    select count(1) from eg_carda a
			left join sl_notification n on n.processCardAId = a.recordId and n.mergeType in (${mergeTypes})
			left join pd_feeding f on f.notificationId = n.recordId and f.status != #{feedingStatus}
		where a.recordId = #{recordId} and a.cancelCause = #{cancelCause};
	</select>
	
	<select id="getFeedingCountByCard" resultType="java.lang.Integer" >
		select count(1) from pd_feeding where processCardAId = #{recordId} and activeFlag = #{DEL_FLAG_NORMAL} and status <![CDATA[<>]]> #{status}
	</select>
	
	<select id="getFeedingCountByCardOne" resultType="java.lang.Integer" >
		select count(1) from pd_feeding where companyId = #{company.recordId} AND processCardAId = #{recordId} and activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	
	<resultMap id="Result_CuttingInfo" type="CardA">
		<id property="recordId" column="recordId" />
		<result property="templateId" column="templateId"  />
		<result property="no" column="no" />
		<result property="noticeNos" column="noticeNos" />
		<result property="pcsWidth" column="pcsWidth"/>
		<result property="pcsLength" column="pcsLength"/>
		<result property="setWidth" column="setWidth" />
		<result property="setLength" column="setLength" />
		<result property="pnlDivisor" column="pnlDivisor"/>
		
		<association property="cardB" javaType="CardB">
			<id property="recordId" column="cardB.recordId" />
	        <association property="boardCutting" javaType="BoardCutting">
				<id property="recordId" column="boardCutting.recordId" />
				<result property="boardLength" column="boardCutting.boardLength" />
				<result property="boardWidth" column="boardCutting.boardWidth" />
				<result property="pcsesPerSet" column="boardCutting.pcsesPerSet" />
				<result property="totalSets" column="boardCutting.totalSets" />
				<result property="totalPcses" column="boardCutting.totalPcses" />
				<result property="useRatio" column="boardCutting.useRatio" />

				<association property="partA" javaType="BoardPart">
				    <id property="recordId" column="partA.recordId" />
					<result property="pegLocateSide" column="partA.pegLocateSide" />
					<result property="partLength" column="partA.partLength" />
					<result property="partWidth" column="partA.partWidth" />
					<result property="pnlLength" column="partA.pnlLength" />
					<result property="pnlWidth" column="partA.pnlWidth" />
					<result property="pnlCount" column="partA.pnlCount" />
					<result property="horizontalSetLength" column="partA.horizontalSetLength" />
					<result property="horizontalSetCount" column="partA.horizontalSetCount" />
					<result property="horizontalGap" column="partA.horizontalGap" />
					<result property="horizonMargin" column="partA.horizonMargin" />
					<result property="verticalSetLength" column="partA.verticalSetLength" />
					<result property="verticalSetCount" column="partA.verticalSetCount" />
					<result property="verticalGap" column="partA.verticalGap" />
					<result property="verticalMargin" column="partA.verticalMargin" />
					<result property="totalSetCount" column="partA.totalSetCount" />
					<result property="pcsCount" column="partA.pcsCount" />
		       	</association> 
		       	
	       		<association property="partB" javaType="BoardPart">
	       		    <id property="recordId" column="partB.recordId" />
					<result property="pegLocateSide" column="partB.pegLocateSide" />
					<result property="partLength" column="partB.partLength" />
					<result property="partWidth" column="partB.partWidth" />
					<result property="pnlLength" column="partB.pnlLength" />
					<result property="pnlWidth" column="partB.pnlWidth" />
					<result property="pnlCount" column="partB.pnlCount" />
					<result property="horizontalSetLength" column="partB.horizontalSetLength" />
					<result property="horizontalSetCount" column="partB.horizontalSetCount" />
					<result property="horizontalGap" column="partB.horizontalGap" />
					<result property="horizonMargin" column="partB.horizonMargin" />
					<result property="verticalSetLength" column="partB.verticalSetLength" />
					<result property="verticalSetCount" column="partB.verticalSetCount" />
					<result property="verticalGap" column="partB.verticalGap" />
					<result property="verticalMargin" column="partB.verticalMargin" />
					<result property="totalSetCount" column="partB.totalSetCount" />
					<result property="pcsCount" column="partB.pcsCount" />
		       	</association> 
			</association>
		</association>
	</resultMap>
	
	<select id="getCuttingInfoById" resultMap="Result_CuttingInfo">
		SELECT 
			a.recordId,
			a.companyId AS "company.recordId",
			a.templateId,
			a.no,
			a.noticeNos,
			a.pcsWidth,
			a.pcsLength,
			a.setWidth,
			a.setLength,
			a.pnlDivisor,
			a.pliesnumber,
			a.processCardBId AS "cardB.recordId",

			cb.boardCuttingId AS "boardCutting.recordId",
			cbc.boardLength AS "boardCutting.boardLength",
			cbc.boardWidth AS "boardCutting.boardWidth",
			cbc.pcsesPerSet AS "boardCutting.pcsesPerSet",
			cbc.totalSets AS "boardCutting.totalSets",
			cbc.totalPcses AS "boardCutting.totalPcses",
			cbc.useRatio AS "boardCutting.useRatio",
			
			cbca.recordId AS "partA.recordId",
			cbca.pegLocateSide AS "partA.pegLocateSide",
			cbca.partLength AS "partA.partLength",
			cbca.partWidth AS "partA.partWidth",
			cbca.pnlLength AS "partA.pnlLength",
			cbca.pnlWidth AS "partA.pnlWidth",
			cbca.pnlCount AS "partA.pnlCount",
			cbca.horizontalSetLength AS "partA.horizontalSetLength",
			cbca.horizontalSetCount AS "partA.horizontalSetCount",
			cbca.horizontalGap AS "partA.horizontalGap",
			cbca.horizonMargin AS "partA.horizonMargin",
			cbca.verticalSetLength AS "partA.verticalSetLength",
			cbca.verticalSetCount AS "partA.verticalSetCount",
			cbca.verticalGap AS "partA.verticalGap",
			cbca.verticalMargin AS "partA.verticalMargin",
			cbca.totalSetCount AS "partA.totalSetCount",
			cbca.pcsCount AS "partA.pcsCount",

			cbcb.recordId AS "partB.recordId",
			cbcb.pegLocateSide AS "partB.pegLocateSide",
			cbcb.partLength AS "partB.partLength",
			cbcb.partWidth AS "partB.partWidth",
			cbcb.pnlLength AS "partB.pnlLength",
			cbcb.pnlWidth AS "partB.pnlWidth",
			cbcb.pnlCount AS "partB.pnlCount",
			cbcb.horizontalSetLength AS "partB.horizontalSetLength",
			cbcb.horizontalSetCount AS "partB.horizontalSetCount",
			cbcb.horizontalGap AS "partB.horizontalGap",
			cbcb.horizonMargin AS "partB.horizonMargin",
			cbcb.verticalSetLength AS "partB.verticalSetLength",
			cbcb.verticalSetCount AS "partB.verticalSetCount",
			cbcb.verticalGap AS "partB.verticalGap",
			cbcb.verticalMargin AS "partB.verticalMargin",
			cbcb.totalSetCount AS "partB.totalSetCount",
			cbcb.pcsCount AS "partB.pcsCount"
		FROM eg_carda a
	  	JOIN eg_cardb cb on cb.recordId = a.processCardBId and cb.companyId = a.companyId 
		JOIN eg_cardb_board_cutting cbc on cbc.recordId = cb.boardCuttingId and cbc.companyId = a.companyId
		JOIN eg_cardb_board_part cbca on cbca.recordId = cbc.partAId and cbca.companyId = a.companyId
		JOIN eg_cardb_board_part cbcb on cbcb.recordId = cbc.partBId and cbcb.companyId = a.companyId
		LEFT JOIN sm_user su on su.recordId = a.createdBy
		WHERE a.recordId = #{recordId} 
	</select>
	
	<resultMap id="Result_Cutting_BoardCraft_Info" type="CardA">
		<id property="recordId" column="recordId" />
		<result property="setWidth" column="setWidth" />
		<result property="setLength" column="setLength" />
		<result property="pnlDivisor" column="pnlDivisor"/>
		
		<association property="cardB" javaType="CardB">
			<id property="recordId" column="cardB.recordId" />
	        <association property="boardCutting" javaType="BoardCutting">
				<id property="recordId" column="boardCutting.recordId" />
				<result property="totalSets" column="boardCutting.totalSets" />
				<result property="totalPcses" column="boardCutting.totalPcses" />
				<result property="useRatio" column="boardCutting.useRatio" />

				<association property="partA" javaType="BoardPart">
				    <id property="recordId" column="partA.recordId" />
					<result property="pnlCount" column="partA.pnlCount" />
					<result property="totalSetCount" column="partA.totalSetCount" />
					<result property="pcsCount" column="partA.pcsCount" />
		       	</association> 
		       	
	       		<association property="partB" javaType="BoardPart">
	       		    <id property="recordId" column="partB.recordId" />
					<result property="pnlCount" column="partB.pnlCount" />
					<result property="totalSetCount" column="partB.totalSetCount" />
					<result property="pcsCount" column="partB.pcsCount" />
		       	</association> 
			</association>
		</association>
		
		<collection property="boardPartCraftList" 
       		column="{cardA.recordId=recordId, company.recordId=company.recordId}" 
       		select="com.kyb.pcberp.modules.eg.dao.BoardPartCraftDao.findListByCardA" /> 
	</resultMap>
	
	<select id="getCuttingInfoAndBoardCraftById" resultMap="Result_Cutting_BoardCraft_Info">
		SELECT 
			a.recordId,
			a.companyId AS "company.recordId",
			a.setWidth,
			a.setLength,
			a.pnlDivisor,
			a.pliesnumber,
			a.processCardBId AS "cardB.recordId",
			
			cb.boardCuttingId AS "boardCutting.recordId",
			cbc.totalSets AS "boardCutting.totalSets",
			cbc.totalPcses AS "boardCutting.totalPcses",
			cbc.useRatio AS "boardCutting.useRatio",
			
			cbca.recordId AS "partA.recordId",
			cbca.pnlCount AS "partA.pnlCount",
			cbca.totalSetCount AS "partA.totalSetCount",
			cbca.pcsCount AS "partA.pcsCount",

			cbcb.recordId AS "partB.recordId",
			cbcb.pnlCount AS "partB.pnlCount",
			cbcb.totalSetCount AS "partB.totalSetCount",
			cbcb.pcsCount AS "partB.pcsCount"
		FROM eg_carda a
	  	JOIN eg_cardb cb on cb.recordId = a.processCardBId and cb.companyId = a.companyId 
		JOIN eg_cardb_board_cutting cbc on cbc.recordId = cb.boardCuttingId and cbc.companyId = a.companyId
		JOIN eg_cardb_board_part cbca on cbca.recordId = cbc.partAId and cbca.companyId = a.companyId
		JOIN eg_cardb_board_part cbcb on cbcb.recordId = cbc.partBId and cbcb.companyId = a.companyId
		LEFT JOIN sm_user su on su.recordId = a.createdBy
		WHERE a.recordId = #{recordId} 
	</select>
	
	<select id="countCardA" resultType="int"> 
		SELECT COUNT(a.recordId) from eg_carda a WHERE a.companyId=#{company.recordId} and a.templateId=#{recordId}
	</select>
	
	<select id="findSetsAndPcses" resultType="BoardCutting">
		SELECT
			cbc.totalSets AS "totalSets",
			cbc.totalPcses AS "totalPcses",
			cbc.boardSketchMapPath AS "boardSketchMapPath",
			cbca.sketchMapPath AS "partA.sketchMapPath",
			cbcb.sketchMapPath AS "partB.sketchMapPath"
		FROM eg_carda a
		JOIN eg_cardb cb ON cb.recordId = a.processCardBId AND cb.companyId = a.companyId
		JOIN eg_cardb_board_cutting cbc ON cbc.recordId = cb.boardCuttingId AND cbc.companyId = a.companyId
		JOIN eg_cardb_board_part cbca on cbca.recordId = cbc.partAId and cbca.companyId = a.companyId
		JOIN eg_cardb_board_part cbcb on cbcb.recordId = cbc.partBId and cbcb.companyId = a.companyId
		WHERE a.customerModels = #{customerModels}
		AND a.activeFlag = 1
		AND a.companyId = #{company.recordId}
		GROUP BY a.createdDate DESC
		LIMIT 0,1
	</select>
	
	<!-- tj 2019-06-25 保存备注 -->
	<update id="automaticSaveRemark">
		UPDATE eg_carda SET 	
			remark2 = #{remark2}
		WHERE recordId = #{recordId}
	</update>
	
	<!-- tj 2019-06-26 获取工程开料信息 -->
	<select id="getEngineeringInfo" resultType="BoardCutting">
		SELECT
			cbc.totalSets AS "totalSets",
			cbc.totalPcses AS "totalPcses",
			cbc.boardSketchMapPath AS "boardSketchMapPath",
			cbca.sketchMapPath AS "partA.sketchMapPath",
			cbca.pnlLength AS "partA.pnlLength",
			cbca.pnlWidth AS "partA.pnlWidth",
			cbca.totalSetCount AS "partA.totalSetCount",
			cbca.pcsCount AS "partA.pcsCount",
			cbca.pnlCount AS "partA.pnlCount",
			cbcb.sketchMapPath AS "partB.sketchMapPath",
			cbcb.pnlLength AS "partB.pnlLength",
			cbcb.pnlWidth AS "partB.pnlWidth",
			cbcb.totalSetCount AS "partB.totalSetCount",
			cbcb.pcsCount AS "partB.pcsCount",
			cbcb.pnlCount AS "partB.pnlCount"
		FROM eg_carda a
		JOIN eg_cardb cb ON cb.recordId = a.processCardBId AND cb.companyId = a.companyId
		JOIN eg_cardb_board_cutting cbc ON cbc.recordId = cb.boardCuttingId AND cbc.companyId = a.companyId
		JOIN eg_cardb_board_part cbca on cbca.recordId = cbc.partAId and cbca.companyId = a.companyId
		JOIN eg_cardb_board_part cbcb on cbcb.recordId = cbc.partBId and cbcb.companyId = a.companyId
		WHERE a.recordId = #{recordId}
		AND a.activeFlag = 1
		GROUP BY a.createdDate DESC
		LIMIT 0,1
	</select>
	
	<!-- zjn 2019-08-15 获取工程卡最新set尺寸和周期 -->
	<select id="getCardSet" resultType="ContractCraft">
		SELECT
			CONCAT(0+CAST(ec.setLength AS CHAR),'*',0+CAST(ec.setWidth AS CHAR),'/',ec.pnlDivisor) AS "cardSet",
			ec.progressDesc AS "progressDesc",
			(ec.setLength * ec.setWidth / ec.pnlDivisor / 1000000) AS "setArea",
			ec.setLength AS "cardPnlLength",
			ec.setWidth AS "cardPnlWidth",
			ec.pnlDivisor AS "cardPnlDivisor"
		FROM sl_contract_craft a
		LEFT JOIN eg_carda ec ON  
		(FIND_IN_SET(REPLACE(a.customerModel," ",""),REPLACE(ec.customerModels," ","")) OR REPLACE(a.customerModel," ","") = REPLACE(ec.customerModels," ",""))
		AND ec.companyId = a.companyId AND ec.activeFlag = 1
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND a.recordId = #{recordId}
		ORDER BY ec.createdDate DESC LIMIT 1
	</select>
	
	<select id="getNowCount" resultType="Integer">
		SELECT 
			COUNT(1) 
		FROM eg_carda a
		LEFT JOIN md_dict_value b ON b.recordId = a.orderType AND b.activeFlag = 1
		WHERE a.companyId = #{recordId} AND a.activeFlag = 1
		AND b.`value` LIKE CONCAT('%',"新单",'%') AND DATE(a.createdDate) = CURDATE()
	</select>
	
	<select id="findExpList" resultType="CardA">
		 SELECT
		 	a.recordId,
		 	a.noticeNos,
		 	a.progressDesc,
		 	a.produceModel,
		 	a.customerModels,
		 	dv.value AS "orderTypeValue",
		 	a.status,
			su.userName AS "createdBy.userName",
			su1.userName AS "cardAuditUser.userName",
		 	a.cardMadeDate,
			cbc.useRatio AS "cardB.boardCutting.useRatio",
			sns2.saleName AS "saleName",
			sns2.cusSaleName AS "cusSaleName",
			sns2.saleUserName AS "saleUserName",
			IFNULL(sns2.orderDeailArea,sd.orderDeailArea) AS "area",
		    a.starAniseLength,
		    a.starAniseWidth,
			a.setWidth,
			a.setLength,
			a.pnlDivisor
		FROM eg_carda a
	  	JOIN eg_cardb cb on cb.recordId = a.processCardBId and cb.companyId = a.companyId 
		JOIN eg_cardb_board_cutting cbc on cbc.recordId = cb.boardCuttingId and cbc.companyId = a.companyId
		LEFT JOIN sm_user su on su.recordId = a.createdBy
		LEFT JOIN sm_user su1 on su1.recordId = a.cardAuditUser
		LEFT JOIN md_dict_value dv ON dv.recordId = a.orderType
		LEFT JOIN sl_notification sn ON sn.processCardAId = a.recordId AND sn.activeFlag = 1
		LEFT JOIN sl_notification_scheduling ns ON sn.recordId = ns.notificationId AND ns.activeFlag = 1
		LEFT JOIN sl_contract_detail ns2 ON sn.contractDetailId = ns2.recordId
		LEFT JOIN icloud_group_center gf ON gf.recordId = ns2.groupCenterId
		LEFT JOIN sl_contract_detail sd ON sd.recordId = gf.contactDeailId
		LEFT JOIN
		(
			SELECT
				IFNULL(ff.shortName,dd.shortName) AS "saleName",
			    CONCAT(IFNULL(gg.`no`,ee.`no`),':',IFNULL(gg.shortName,ee.shortName)) AS "cusSaleName",
			    kk.userName AS "saleUserName",
				aa.processCardAId AS "processCardAId",
				SUM(IFNULL(bb.orderDeailArea,0)) AS "orderDeailArea"
			FROM sl_notification aa
			LEFT JOIN sl_contract_detail bb ON bb.recordId = aa.contractDetailId
			LEFT JOIN sl_contract cc ON cc.recordId = bb.contractId
			LEFT JOIN md_branch dd ON dd.recordId = cc.branchId
			LEFT JOIN md_customer ee ON ee.recordId = cc.customerId
			LEFT JOIN md_branch ff ON ff.recordId = aa.saleId
			LEFT JOIN md_customer gg ON gg.recordId = aa.saleCsutomerId
			LEFT JOIN icloud_group_center hh ON hh.recordId = bb.groupCenterId
			LEFT JOIN sl_contract_detail ii ON ii.recordId = hh.contactDeailId
			LEFT JOIN sl_contract jj ON jj.recordId = ii.contractId
			LEFT JOIN sm_user kk ON kk.recordId = jj.createdBy
			WHERE aa.companyId = #{company.recordId} AND aa.activeFlag = 1 AND aa.`status` <![CDATA[<>]]> 200406
			AND aa.processCardAId IN 
			(
				SELECT recordId FROM eg_carda WHERE companyId = #{company.recordId} AND activeFlag = 1 AND cardMadeDate <![CDATA[>=]]> #{sentTimeStartQr}
				AND cardMadeDate <![CDATA[<=]]> #{sentTimeEndQr}
			)
			GROUP BY aa.processCardAId
		) sns2 ON sns2.processCardAId = a.recordId
		<where>
			a.companyId = #{company.recordId}
			<if test="status == null || status == '' || status == -1 || status != 2">
				AND a.activeFlag = #{DEL_FLAG_NORMAL}
			</if>
			<if test="produceModel != null and produceModel != ''">
				AND (REPLACE(a.produceModel," ","") LIKE CONCAT('%', REPLACE(#{produceModel}," ",""), '%')  OR REPLACE(a.produceModelCopy," ","") LIKE CONCAT('%', REPLACE(#{produceModel}," ",""), '%'))
			</if>
			<if test="customerModels != null and customerModels != ''">
				AND REPLACE(a.customerModels," ","") LIKE CONCAT('%', REPLACE(#{customerModels}," ",""), '%')
			</if>
			<if test="pcsDimensionLength != null and pcsDimensionLength != ''">
				AND a.pcsLength = #{pcsDimensionLength}
			</if>
			<if test="pcsDimensionWidth != null and pcsDimensionWidth != ''">
				AND a.pcsWidth = #{pcsDimensionWidth}
			</if>
			<if test="setLength != null and setLength != ''">
				AND a.setLength = #{setLength}
			</if>
			<if test="setWidth != null and setWidth != ''">
				AND a.setWidth = #{setWidth}
			</if>
			<if test="noticeNos != null and noticeNos != ''">
				AND REPLACE(a.noticeNos," ","") LIKE CONCAT('%', REPLACE(#{noticeNos}s," ",""), '%')
			</if>
			<if test="createByName != null and createByName != ''">
				AND REPLACE(su.userName," ","") LIKE CONCAT('%', REPLACE (#{createByName}, ' ', ''), '%')
			</if>
			<if test="auditByName != null and auditByName != ''">
				AND REPLACE(su1.userName," ","") LIKE CONCAT('%', REPLACE (#{auditByName}, ' ', ''), '%')
			</if>
			<if test="status != null and status != ''">
				<if test="status != 2 and status != -1">
					AND a.status = #{status}
				</if>
				<if test="status == 2">
					AND a.activeFlag <![CDATA[<>]]> 1
				</if>
			</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} or a.lastUpdBy = #{createdBy.recordId} 
				OR a.recordId IN (SELECT	noti.processCardAId FROM sl_notification noti JOIN sl_contract_detail contdeatil
				ON noti.contractDetailId = contdeatil.recordId JOIN sl_contract cont ON cont.recordId = contdeatil.contractId
				WHERE cont.userId =  #{createdBy.recordId}  
		   		or cont.customerId in (SELECT cs.customerId from sm_customer_salesAssistant cs WHERE cs.activeFlag = #{DEL_FLAG_NORMAL} AND cs.userId = #{createdBy.recordId})
				  ))
			</if>
			<!-- tj 2019-06-24 生产类型查询  -->
			<if test="orderTypeValue != null and orderTypeValue != ''">
				AND FIND_IN_SET(dv.value,#{orderTypeValue})
			</if>
			<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
				AND a.cardMadeDate <![CDATA[>=]]> #{sentTimeStartQr}
			</if>
			<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
				AND a.cardMadeDate <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
			<if test="materialTypeId != null and materialTypeId != ''">
				AND a.recordId IN 
				(
					SELECT
						sn.processCardAId
					FROM sl_notification sn
					LEFT JOIN sl_contract_detail scd ON scd.recordId = sn.contractDetailId
					LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId
					WHERE sn.companyId = #{company.recordId}  AND sn.activeFlag = 1 AND sn.`status` <![CDATA[<>]]> 200406
					AND scc.materialType = #{materialTypeId} AND sn.processCardAId IS NOT NULL
				)
			</if>
			<if test="saleName != null and saleName != ''">
				AND REPLACE(sns2.saleName," ","") LIKE CONCAT('%', REPLACE(#{saleName}," ",""), '%')
			</if>
			<if test="cusSaleName != null and cusSaleName != ''">
				AND REPLACE(sns2.cusSaleName," ","") LIKE CONCAT('%', REPLACE(#{cusSaleName}," ",""), '%')
			</if>
			<if test="processName != null and processName != ''">
				AND a.templateId IN
				(
				SELECT
				a.templateId
				FROM eg_template_process_relation a
				LEFT JOIN eg_process b ON b.recordId = a.processId
				LEFT JOIN eg_template c ON c.recordId = a.templateId
				WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND c.showFlag = 1
				AND FIND_IN_SET(REPLACE(b.category," ",""),#{processName})
				)
			</if>
			GROUP BY a.recordId
		</where>
	</select>
	
	<select id="getCardSetList" resultType="ContractCraft">
		SELECT
			aa.cardPnlLength AS "cardPnlLength",
			aa.cardPnlWidth AS "cardPnlWidth",
			aa.cardPnlDivisor AS "cardPnlDivisor",
			(aa.cardPnlLength * aa.cardPnlWidth / aa.cardPnlDivisor / 1000000) AS "setArea",
			aa.cardSet AS "cardSet",
			aa.recordId AS "recordId"
		FROM
		(
			SELECT
				c.setLength AS "cardPnlLength",
				c.setWidth AS "cardPnlWidth",
				c.pnlDivisor AS "cardPnlDivisor",
				CONCAT(
					0 + CAST(c.setLength AS CHAR),
					'*',
					0 + CAST(c.setWidth AS CHAR),
					'/',
					c.pnlDivisor
				) AS "cardSet",
				b.craftId AS "recordId"
			FROM sl_notification a
			LEFT JOIN sl_contract_detail b ON b.recordId = a.contractDetailId
			LEFT JOIN icloud_group_center d ON d.recordId = b.groupCenterId
			LEFT JOIN sl_contract_detail e ON e.groupCenterId = d.recordId AND e.companyId = b.factoryId
			LEFT JOIN sl_notification f ON f.contractDetailId = e.recordId AND f.activeFlag = 1 AND f.`status` <![CDATA[<>]]> 200406
			LEFT JOIN eg_carda c ON c.recordId = f.processCardAId
			WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND f.processCardAId IS NOT NULL
			AND a.`status` <![CDATA[<>]]> 200406 AND b.craftId IN (${recordId})
			ORDER BY c.createdDate DESC
		) aa
		WHERE 1=1 GROUP BY aa.recordId
	</select>
	
	<update id="updateFeedCause">
		UPDATE eg_carda SET
			feedCause = #{feedCause}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="getWaitGroupData" resultType="GroupCenter">
		SELECT
			a.recordId,
			d.`value` AS "orderType",
			CONCAT(
				IFNULL(e.`value`, ""),
				" / ",
				IFNULL(h5.`value`, ""),
				" / ",
				IFNULL(f.`value`, ""),
				" / ",
				IFNULL(g.`value`, ""),
				" / ",
				IFNULL(h6.`value`, ""),
				" / ",
				IFNULL(h1.`value`, ""),
				" / ",
				IFNULL(h.`value`, ""),
				" / ",
				IFNULL(h2.`value`, ""),
				" / ",
				IFNULL(h7.`value`, ""),
				(
					CASE
					WHEN h3.`value` IS NOT NULL THEN
						CONCAT(" / ", h3.`value`)
					ELSE
						""
					END
				),
				(
					CASE
					WHEN h4.`value` IS NOT NULL THEN
						CONCAT(" / ", h4.`value`)
					ELSE
						""
					END
				)
			) AS "craftDeail",
			c.customerModel,
			bb.`no`,
			bb.customerPo,
			bb.orderDate,
			h8.shortName,
			CONCAT(
				IFNULL(c.pnlLength, ""),
				"*",
				IFNULL(c.pnlWidth, ""),
				"/",
				IFNULL(c.pnlDivisor, "")
			) AS "deliverSize",
			b.fileUrl,
			b.fileName
		FROM
			icloud_group_center a
		LEFT JOIN sl_contract_detail b ON b.recordId = a.contactDeailId
		LEFT JOIN sl_contract bb ON bb.recordId = b.contractId
		LEFT JOIN sl_contract_craft c ON c.recordId = b.craftId
		LEFT JOIN md_dict_value d ON d.recordId = b.referenceType
		LEFT JOIN md_dict_value e ON e.recordId = c.boardLevel
		LEFT JOIN md_dict_value f ON f.recordId = c.boardThickness
		LEFT JOIN md_dict_value g ON g.recordId = c.copperCladThickness
		LEFT JOIN md_dict_value h ON h.recordId = c.shapingWay
		LEFT JOIN md_dict_value h1 ON h1.recordId = c.characterType
		LEFT JOIN md_dict_value h2 ON h2.recordId = c.testMethod
		LEFT JOIN md_dict_value h3 ON h3.recordId = c.daore
		LEFT JOIN md_dict_value h4 ON h4.recordId = c.naiya
		LEFT JOIN md_dict_value h5 ON h5.recordId = c.materialType
		LEFT JOIN md_dict_value h6 ON h6.recordId = c.solderMaskType
		LEFT JOIN md_dict_value h7 ON h7.recordId = c.surfaceProcess
		LEFT JOIN md_company h8 ON h8.recordId = bb.companyId
		WHERE
			a.estimateRemark IS NULL
		AND a.cardTotalPcses IS NULL
		AND a.activeFlag = 1
		AND b.`status` = 200201
		AND b.activeFlag = 1
		AND a.estimateStartDate IS NOT NULL
		AND bb.activeFlag = 1
		AND bb.`status` = 200201
		ORDER BY
			bb.orderDate ASC
	</select>
	
	<update id="editGroupData">
		UPDATE icloud_group_center SET
			cardTotalPcses = #{cardTotalPcses},
			cardUseRatio = #{cardUseRatio},
			cardUserId = #{cardUserId},
			cardTime = NOW()
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateMatId">
		UPDATE eg_carda SET
			materialId = #{materialId},
			materialIds = #{materialIds},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="getCardAData" resultMap="Result_CardB_BoardCutting_PartA_PartB">
		 SELECT 
			a.*,
			a.companyId AS "company.recordId",
			a.processCardBId AS "cardB.recordId"
		FROM eg_carda a
		WHERE a.recordId = #{recordId} 
	</select>

	<select id="getCraftNoByCradId" resultType="String">
		 SELECT
			(SELECT GROUP_CONCAT(DISTINCT `no`) FROM sl_contract_craft WHERE FIND_IN_SET(`no`,a.`no`))
		FROM
			eg_carda a
		WHERE a.recordId = #{recordId}
	</select>

	<select id="getArrangePlanDateList" resultType="CardA">
		SELECT
			aaa.arrangePlanDate AS "arrangePlanDate",
			aaa.processCardAId AS "recordId"
		FROM
		(
			SELECT
				bb.processCardAId AS "processCardAId",
				aa.arrangePlanDate AS "arrangePlanDate"
			FROM sl_notification_scheduling aa
			LEFT JOIN sl_notification bb ON bb.recordId = aa.notificationId
			WHERE aa.companyId = #{company.recordId} AND aa.activeFlag = 1 AND bb.`status` <![CDATA[<>]]> 200406
			AND FIND_IN_SET(bb.processCardAId,#{recordId}) ORDER BY bb.createdDate DESC
		) aaa
		WHERE 1=1 GROUP BY aaa.processCardAId
	</select>

	<update id="updateShelfSelected">
		UPDATE eg_carda SET
			testShelfSelected = #{testShelfSelected},
			mouldSelected = #{mouldSelected}
		WHERE recordId = #{recordId}
	</update>

	<select id="getEngineeringCard" resultType="CardA">
		SELECT
			aa.*
		FROM
			(
				SELECT
					a.recordId AS "recordId",
					a.versionContent AS "versionContent",
					c.useRatio AS "cardB.boardCutting.useRatio",
					a.defaultVersion AS "defaultVersion"
				FROM eg_carda a
				LEFT JOIN eg_cardb b ON b.recordId = a.processCardBId
				LEFT JOIN eg_cardb_board_cutting c ON c.recordId = b.boardCuttingId
				WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1
				AND a.`no` = #{no} AND a.recordId <![CDATA[<>]]> #{recordId}
				ORDER BY a.createdDate DESC
			) aa
		GROUP BY aa.versionContent
	</select>

	<update id="defualtValue">
			UPDATE eg_carda aa
	INNER JOIN (
		SELECT
			aa.recordId AS "recordId"
		FROM
			(
				SELECT
					recordId AS "recordId",
					versionContent AS "versionContent"
				FROM
					eg_carda
				WHERE
					companyId = #{company.recordId}
				AND activeFlag = 1
				AND `no` = #{no}
				ORDER BY
					createdDate DESC
			) aa
		WHERE
			aa.recordId != #{recordId}
		GROUP BY
			aa.versionContent
	) bb ON bb.recordId = aa.recordId
	SET aa.defaultVersion = 0;
	</update>

    <select id="cardaExist" resultType="Integer">
		SELECT
			count(1)
		FROM eg_carda
		WHERE companyId = #{company.recordId} AND activeFlag = 1
		AND no = #{no} AND versionContent = #{versionContent}
		<if test="recordId != null and recordId != ''">
			AND recordId <![CDATA[<>]]> #{recordId}
		</if>
    </select>

	<update id="updateDefualtV">
		UPDATE eg_carda SET
		 		versionContent = #{versionContent},defaultVersion = #{defaultVersion}
		WHERE recordId = #{recordId}
	</update>

	<select id="getByCraftNoTwo" resultMap="Result_ForAutoAdd">
	    SELECT
	    	a.recordId,
			a.companyId AS "company.recordId",
			a.templateId,
			a.no,
			a.produceModel,
			a.noticeNos,
			a.customerNos,
			a.customerModels,
			a.customerFileNames,
			a.pcsWidth,
			a.pcsLength,
			a.setWidth,
			a.setLength,
			a.cardMadeDate,
			a.pnlDivisor,
			a.boardLevel,
			a.finishedThickness,
			a.finishedTolerance,
			a.innerThickness,
			a.progressDesc,
			a.status,
			a.activeFlag,
			a.remark,
			a.createdBy AS "createdBy.recordId",
			a.createdDate,
			a.lastUpdBy AS "lastUpdBy.recordId",
			a.lastUpdDate,
			a.pliesnumber,
			a.cancelCause,
			a.remark2,
			a.materialId AS "material.recordId",

			cb.recordId as "cardB.recordId",
			cb.firstDrillATotal AS "cardB.firstDrillATotal",
			cb.firstDrillBTotal AS "cardB.firstDrillBTotal",
			cb.secondDrillATotal AS "cardB.secondDrillATotal",
			cb.secondDrillBTotal AS "cardB.secondDrillBTotal",

			cbc.recordId AS "boardCutting.recordId",
			cbc.boardLength AS "boardCutting.boardLength",
			cbc.boardWidth AS "boardCutting.boardWidth",
			cbc.pcsesPerSet AS "boardCutting.pcsesPerSet",
			cbc.totalSets AS "boardCutting.totalSets",
			cbc.totalPcses AS "boardCutting.totalPcses",
			cbc.useRatio AS "boardCutting.useRatio",
			cbc.boardSketchMapPath AS "boardCutting.boardSketchMapPath",

			cbca.recordId AS "partA.recordId",
			cbca.pegLocateSide AS "partA.pegLocateSide",
			cbca.partLength AS "partA.partLength",
			cbca.partWidth AS "partA.partWidth",
			cbca.pnlLength AS "partA.pnlLength",
			cbca.pnlWidth AS "partA.pnlWidth",
			cbca.pnlCount AS "partA.pnlCount",
			cbca.horizontalSetLength AS "partA.horizontalSetLength",
			cbca.horizontalSetCount AS "partA.horizontalSetCount",
			cbca.horizontalGap AS "partA.horizontalGap",
			cbca.horizonMargin AS "partA.horizonMargin",
			cbca.verticalSetLength AS "partA.verticalSetLength",
			cbca.verticalSetCount AS "partA.verticalSetCount",
			cbca.verticalGap AS "partA.verticalGap",
			cbca.verticalMargin AS "partA.verticalMargin",
			cbca.totalSetCount AS "partA.totalSetCount",
			cbca.sketchMapPath AS "partA.sketchMapPath",
			cbca.pcsCount AS "partA.pcsCount",

			cbcb.recordId AS "partB.recordId",
			cbcb.pegLocateSide AS "partB.pegLocateSide",
			cbcb.partLength AS "partB.partLength",
			cbcb.partWidth AS "partB.partWidth",
			cbcb.pnlLength AS "partB.pnlLength",
			cbcb.pnlWidth AS "partB.pnlWidth",
			cbcb.pnlCount AS "partB.pnlCount",
			cbcb.horizontalSetLength AS "partB.horizontalSetLength",
			cbcb.horizontalSetCount AS "partB.horizontalSetCount",
			cbcb.horizontalGap AS "partB.horizontalGap",
			cbcb.horizonMargin AS "partB.horizonMargin",
			cbcb.verticalSetLength AS "partB.verticalSetLength",
			cbcb.verticalSetCount AS "partB.verticalSetCount",
			cbcb.verticalGap AS "partB.verticalGap",
			cbcb.verticalMargin AS "partB.verticalMargin",
			cbcb.totalSetCount AS "partB.totalSetCount",
			cbcb.sketchMapPath AS "partB.sketchMapPath",
			cbcb.pcsCount AS "partB.pcsCount"
		FROM eg_carda a
		JOIN eg_cardb cb on cb.recordId = a.processCardBId
		JOIN eg_cardb_board_cutting cbc on cbc.recordId = cb.boardCuttingId
		JOIN eg_cardb_board_part cbca on cbca.recordId = cbc.partAId
		JOIN eg_cardb_board_part cbcb on cbcb.recordId = cbc.partBId
		WHERE a.recordId = #{recordId} AND a.activeFlag = 1
	</select>
	
	<delete id="deleteLockDataA">
		UPDATE eg_carda SET
            activeFlag = 0
        WHERE recordId = #{processCardAId}
	</delete>

	<select id="getMaterialContractLsit" resultType="CardA">
		SELECT
			a.STATUS,
			SUM(IFNULL( LOCATE( 'MJ', c.`name` ), 0 )) AS "mjCount",
			SUM(IFNULL( LOCATE( 'CSJ', c.`name` ), 0 )) AS "csjCount"
		FROM
			pu_purchasing_detail a
			LEFT JOIN pu_purchasing b ON b.recordId = a.purchasingId
			LEFT JOIN md_material c ON c.recordId = a.materialId
		WHERE
			a.companyId = #{company.recordId}
		  	AND a.activeFlag = 1
		  	AND b.sourceNo = #{recordId}
		GROUP BY
			a.STATUS
	</select>

	<update id="approveSizeChanges">
		UPDATE eg_carda SET
			starAniseLength = #{starAniseLength},
			starAniseWidth = #{starAniseWidth},
			projectStatus = #{projectStatus}
		WHERE recordId = #{recordId}
	</update>
	<select id="getSaleContractDetailId" resultType="String">
		SELECT
			b.contactDeailId
		FROM
			sl_contract_detail a
			LEFT JOIN icloud_group_center b ON b.recordId = groupCenterId
		WHERE
			a.recordId = (
			SELECT
				contractDetailId
			FROM
				sl_notification
			WHERE
			`no` = #{noticeNos})
	</select>

	<update id="updateProjectStatus">
		UPDATE eg_carda set projectStatus = NULL where recordId = #{recordId}
	</update>

	<select id="getCardStarAniSize" resultType="CardA">
		SELECT
			starAniseLength,
			starAniseWidth
		FROM eg_carda WHERE recordId = #{recordId}
	</select>
	<select id="getCardStatus" resultType="CardA">
		select * from eg_carda WHERE activeFlag = 1 AND recordId = #{recordId}
	</select>
	<update id="updateCardStatusA">
		update eg_carda set status = #{status} WHERE recordId = #{recordId}
	</update>

	<select id="getByRemarkTwoList" resultType="CardA">
		SELECT
			a.recordId AS "processValueId",
			b.recordId,
			a.remark,
			b.progressDesc,
			a.processId
		FROM
			eg_carda_process_value a
				LEFT JOIN eg_carda b ON b.recordId = a.processCardAId
		WHERE
			a.companyId = #{company.recordId}
		  AND a.activeFlag = 1
		  AND b.recordId = (
			SELECT
				a.recordId
			FROM
				eg_carda a
			WHERE
				a.companyId = #{company.recordId}
			  AND a.activeFlag = 1
			  AND (
					FIND_IN_SET(
							REPLACE (#{produceModel}, " ", ""),
							REPLACE (a.produceModel, " ", "")
						)
					OR REPLACE (a.produceModel, " ", "") = REPLACE (#{produceModel}, " ", "")
				)
			ORDER BY
				a.createdDate DESC
			LIMIT 1
			)
		  AND b.activeFlag = 1
		ORDER BY
			a.seqNum
	</select>

	<select id="getOrderStatisticsList" resultType="CardA">
		SELECT
		a.recordId AS "notificationId",
		c.materialType,
		d.recordId
		FROM
			sl_notification a
			LEFT JOIN sl_contract_detail b ON b.recordId = a.contractDetailId
			LEFT JOIN md_dict_value c ON c.recordId = b.referenceType
			LEFT JOIN eg_carda d ON d.recordId = a.processCardAId
		WHERE
			a.companyId = #{company.recordId}
			AND a.activeFlag = 1
			AND a.`status` <![CDATA[<>]]> 200406
			AND a.mergeType <![CDATA[<>]]> 200602
			<if test="sentTimeStartQr!= null and sentTimeStartQr !=''">
				AND a.createdDate <![CDATA[>=]]> #{sentTimeStartQr}
			</if>
			<if test="sentTimeEndQr!= null and sentTimeEndQr !=''">
				AND a.createdDate <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
	</select>

	<select id="getCardADataTwo" resultType="CardA">
		SELECT
			a.recordId,
			a.testShelfSelected,
			a.mouldSelected,
			b.userName AS "createByName",
			c.userName AS "cardAuditUser.userName",
			0+CAST(e.useRatio AS CHAR)  AS "cardB.boardCutting.useRatio",
			e.totalPcses,
			CONCAT(0+CAST(a.setLength AS CHAR),'*',0+CAST(a.setWidth AS CHAR),'/',a.pnlDivisor) AS "cardSet",
			0+CAST(a.starAniseLength AS CHAR) AS "starAniseLength",
			0+CAST(a.starAniseWidth AS CHAR) AS "starAniseWidth",
			a.createdDate
		FROM eg_carda a
		LEFT JOIN sm_user b ON b.recordId = a.createdBy
		LEFT JOIN sm_user c ON c.recordId = a.cardAuditUser
		LEFT JOIN eg_cardb d ON d.processCardAId = a.recordId AND d.activeFlag = 1 AND d.`status` = 300102
		LEFT JOIN eg_cardb_board_cutting e ON e.recordId = d.boardCuttingId
		WHERE a.recordId = #{recordId} LIMIT 1
	</select>

	<select id="getVersion" resultType="Integer">
		SELECT
			IFNULL(version,0)
		FROM eg_carda
		WHERE recordId = #{recordId}
	</select>

</mapper>