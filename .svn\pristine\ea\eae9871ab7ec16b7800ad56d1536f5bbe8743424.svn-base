package com.kyb.pcberp.modules.eg.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.sys.entity.Company;
import org.hibernate.validator.constraints.Length;

import java.util.List;

public class Template extends DataEntity<Template>
{

    private static final long serialVersionUID = 1L;

    private String name; // 流程卡模板名称

    private String operLevel; // 数据级别

    private List<EgProcess> processList;

    private Boolean showFlag;// 显示:1,隐藏:0

    private String processIds;

    private String processNames;

    public Template()
    {
        super();
    }

    public Template(String id)
    {
        super(id);
    }

    public Template(Company company)
    {
        this.setCompany(company);
    }

    @Length(min = 1, max = 100, message = "流程卡模板名称长度必须介于 1 和 100 之间")
    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getOperLevel()
    {
        return operLevel;
    }

    public void setOperLevel(String operLevel)
    {
        this.operLevel = operLevel;
    }

    public List<EgProcess> getProcessList()
    {
        return processList;
    }

    public void setProcessList(List<EgProcess> processList)
    {
        this.processList = processList;
    }

    public Boolean getShowFlag()
    {
        return showFlag;
    }

    public void setShowFlag(Boolean showFlag)
    {
        this.showFlag = showFlag;
    }

    public String getProcessIds()
    {
        return processIds;
    }

    public void setProcessIds(String processIds)
    {
        this.processIds = processIds;
    }

    public String getProcessNames()
    {
        return processNames;
    }

    public void setProcessNames(String processNames)
    {
        this.processNames = processNames;
    }
}