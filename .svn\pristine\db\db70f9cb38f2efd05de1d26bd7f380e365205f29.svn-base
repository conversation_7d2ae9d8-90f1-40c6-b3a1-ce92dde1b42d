/**
 *
 */
package com.kyb.pcberp.modules.crm.service;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.kyb.pcberp.modules.approval.dao.AllocationRoleDao;
import com.kyb.pcberp.modules.approval.entity.AllocationRole;
import com.kyb.pcberp.modules.approval.utils.ModifyUtils;
import com.kyb.pcberp.modules.contract.dao.ContractDetailDao;
import com.kyb.pcberp.modules.contract.utils.ChangeDataUtils;
import com.kyb.pcberp.modules.sys.utils.CockpitUtilsOne;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Maps;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.persistence.PageNoInterceptor;
import com.kyb.pcberp.common.service.BaseService;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.CommonUtils;
import com.kyb.pcberp.common.utils.CompanyUtil;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.FmtUtils;
import com.kyb.pcberp.common.utils.MathUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.contract.dao.ContractDao;
import com.kyb.pcberp.modules.contract.dao.DeliveryDao;
import com.kyb.pcberp.modules.contract.entity.Contract;
import com.kyb.pcberp.modules.contract.entity.ContractCraft;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.contract.entity.Delivery;
import com.kyb.pcberp.modules.contract.entity.DeliveryDetail;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.contract.vo.RejectTraceReportVo;
import com.kyb.pcberp.modules.crm.dao.AccountsReceivableDao;
import com.kyb.pcberp.modules.crm.dao.GoodsCheckDao;
import com.kyb.pcberp.modules.crm.dao.RejectApplicationDao;
import com.kyb.pcberp.modules.crm.entity.AccountsReceivable;
import com.kyb.pcberp.modules.crm.entity.ComplaintFile;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.crm.entity.GoodsCheck;
import com.kyb.pcberp.modules.crm.entity.RejectApplication;
import com.kyb.pcberp.modules.crm.vo.RejectPlanVo;
import com.kyb.pcberp.modules.eg.entity.EgProcess;
import com.kyb.pcberp.modules.production.dao.ProduceRecordDao;
import com.kyb.pcberp.modules.production.entity.ProduceRecord;
import com.kyb.pcberp.modules.production.entity.ProductionCycleCate;
import com.kyb.pcberp.modules.quality.dao.DiscardDao;
import com.kyb.pcberp.modules.quality.dao.ReworkDao;
import com.kyb.pcberp.modules.quality.entity.Discard;
import com.kyb.pcberp.modules.quality.entity.Rework;
import com.kyb.pcberp.modules.stock.dao.ProductStoreDao;
import com.kyb.pcberp.modules.stock.entity.ProductStore;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.sys.entity.Branch;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.Pdf;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

/**
 * 客户投诉/退货申请单Service
 *
 * <AUTHOR>
 * @version 2015-10-15
 */
@Service
@Transactional(readOnly = false)
public class ComplaintService extends BaseService
{
    @Autowired
    private RejectApplicationDao rejectApplicationDao;

    @Autowired
    private DeliveryDao deliveryDao;

    @Autowired
    private ReworkDao reworkDao;

    @Autowired
    private DiscardDao discardDao;

    @Autowired
    private ContractDao contractDao;

    @Autowired
    ProduceRecordDao produceRecordDao;

    @Autowired
    ProductStoreDao productStoreDao;

    @Autowired
    AccountsReceivableDao accountsReceivableDao;

    @Autowired
    GoodsCheckDao goodsCheckDao;

    @Autowired
    private AllocationRoleDao allocationRoleDao;

    @Autowired
    private ContractDetailDao contractDetailDao;

    public Page<RejectApplication> findPage(Page<RejectApplication> page, RejectApplication rejectApplication)
    {
        rejectApplication.setPage(page);
        String factoryComId = CompanyUtil.getInstance().getFactId();
        rejectApplication.setFactoryComId(factoryComId);
        List<RejectApplication> list = rejectApplicationDao.findList(rejectApplication);
        if (Collections3.isNotEmpty(list))
        {
            for (RejectApplication re : list)
            {
                if (null != re.getSendQty() && 0 != re.getSendQty())
                {
                    if (null != re.getContractDetail() && null != re.getContractDetail().getCraft())
                    {
                        if (null != re.getContractDetail().getCraft().getPnlLength() && null!= re.getContractDetail().getCraft().getPnlWidth() && null != re.getContractDetail().getCraft().getPnlDivisor())
                        {
                            re.setReplenishmentArea(new BigDecimal(re.getSendQty())
                                .multiply(re.getContractDetail().getCraft().getPnlLength()
                                    .multiply(re.getContractDetail().getCraft().getPnlWidth())
                                    .divide(re.getContractDetail().getCraft().getPnlDivisor(), 2, BigDecimal.ROUND_HALF_UP))
                                .divide(new BigDecimal(1000000))
                                .setScale(4, BigDecimal.ROUND_HALF_UP));
                        }
                    }
                }
            }
        }
        addReworkAndDiscard(list);
        page.setList(list);
        return page;
    }

    private void addReworkAndDiscard(List<RejectApplication> list)
    {
        if (!CollectionUtils.isEmpty(list))
        {
            String ids = "";
            for (RejectApplication r : list)
            {
                ids = ids + r.getJxAppId() + ",";
            }
            List<RejectApplication> infoList = rejectApplicationDao.findReworkAndDiscard(ids);
            for (RejectApplication re : list)
            {
                for (RejectApplication info : infoList)
                {
                    if (info.getRecordId().equals(re.getJxAppId()))
                    {
                        re.setReworkList(info.getReworkList());
                        re.setDiscardList(info.getDiscardList());
                        break;
                    }
                }
            }
        }

    }

    @Transactional(readOnly = false)
    public synchronized Map<String, Object> save(RejectApplication rejectApplication)
    {
        Map<String, Object> data = new HashMap<>();
        RejectApplication reject = rejectApplicationDao.get(rejectApplication);
        if (null != reject && null != reject.getCompany() && null != rejectApplication.getCompany()
            && !reject.getCompany().getRecordId().equals(rejectApplication.getCompany().getRecordId()))
        {
            data.put("result", "fail");
            data.put("message", "显示失效了，请刷新重试!");
            return data;
        }

        if (rejectApplication.getIsNewRecord())
        {
            // 检查同一产品明细，不能存在两张末处理客诉单
            int num = rejectApplicationDao.checkIsExsit(rejectApplication);
            if (num > 0)
            {
                data.put("result", "fail");
                data.put("message", "同一产品明细，不能存在两张末处理客诉单");
                return data;
            }
            rejectApplication.setStatus(TypeKey.ST_REJECTAPPLICATION_STATUS_RETURNAPPLY);
            rejectApplication.preInsert();
            rejectApplicationDao.insert(rejectApplication);
            // 添加成功把对应编码的nextNo
            CommonUtils.updateNextNo(7);
            if(rejectApplication.getRecordId() != null){
                Integer type = 6;
                Integer operatingMode = 1;
                CockpitUtilsOne cockpit = new CockpitUtilsOne();
                cockpit.addOperatingRecord(type,rejectApplication,operatingMode);
        }
        }
        else
        {
            if (rejectApplication.getStatus() != 400102)
            {
                if(rejectApplication.getRecordId() != null){
                    Integer type = 6;
                    Integer operatingMode = 3;
                    CockpitUtilsOne cockpit = new CockpitUtilsOne();
                    cockpit.addOperatingRecord(type,rejectApplication,operatingMode);
                }
                rejectApplication.preUpdate();
                rejectApplicationDao.update(rejectApplication);
            }

            if (rejectApplication.getStatus() == 400102)
            {
                if (StringUtils.isBlank(rejectApplication.getGroupCenterId()) || rejectApplication.getOutsourceFlag())
                {
                    String result = ModifyUtils.complaintApproval(rejectApplication,
                        rejectApplication.getCompany(),
                        1,
                        UserUtils.getUser());
                    if (!"fail".equals(result))
                    {
                        data.put("result", "success");
                        data.put("message", result);
                        return data;
                    }
                    if ("fail".equals(result))
                    {
                        data.put("result", "fail");
                        data.put("message", "请先配置客诉审批");
                        return data;
                    }
                    rejectApplication.preUpdate();
                    rejectApplicationDao.update(rejectApplication);
                    ModifyUtils.sentRejectApplicationData(rejectApplication, UserUtils.getUser(), 1);
                    data.put("result", "success");
                    data.put("message", "审核成功!");
                    return data;
                }
                else
                {
//                    ChangeDataUtils changeDataUtils = new ChangeDataUtils();
//                    changeDataUtils.computerCustomBill("2","5",rejectApplication.getContractDetail().getRecordId(),"",rejectApplication.getRecordId());
                    rejectApplication.preUpdate();
                    rejectApplicationDao.update(rejectApplication);
                    ModifyUtils.sentRejectApplicationData(rejectApplication, UserUtils.getUser(), 3);
                    data.put("result", "success");
                    data.put("message", "审核成功!");
                    return data;
                }
            }
        }
        data.put("result", true);
        data.put("message", "保存成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public void delete(RejectApplication rejectApplication)
    {
        Integer typeA = 6;
        Integer operatingMode = 2;
        CockpitUtilsOne cockpit = new CockpitUtilsOne();
        cockpit.addOperatingRecord(typeA,rejectApplication,operatingMode);
        rejectApplicationDao.delete(rejectApplication);
    }

    public Pdf getPdf(RejectApplication rej, String basePath)
    {
        RejectApplication rejectApp = rejectApplicationDao.getById(rej);

        Map<String, Object> map = Maps.newHashMap();
        Pdf pdf = new Pdf();
        if (rejectApp == null)
        {
            pdf.setDataMap(map);
            pdf.setFileName(ConstKey.DEFAULT_TEMPLATE_PATH + "reject.pdf");
            return pdf;
        }

        // 获取审批流程数据
        List<AllocationRole> allocationRoles = allocationRoleDao.getRejectRoleData(rejectApp);

        String oneContent = null;
        String twoContent = null;
        String threeContent = null;
        String fourContent = null;
        if(Collections3.isEmpty(allocationRoles))
        {
            oneContent = "品质部:";
            twoContent = "工程部:";
            threeContent = "生产部:";
            fourContent = "品质部:";
        }
        else
        {
            for(int i = 0; i < allocationRoles.size();i++)
            {
                AllocationRole role = allocationRoles.get(i);
                if(null == role)
                {
                    continue;
                }
                map.put("role"+i+".opinion", FmtUtils.blank(role.getOpinion()));
                map.put("role"+i+".lastUpdDate", FmtUtils.blank(role.getDateStr()));
                map.put("role"+i+".userName", FmtUtils.blank(role.getUserName()));
                map.put("role"+i+".positionName", FmtUtils.blank(role.getPositionName()));
            }
        }
        map.put("oneContent", FmtUtils.blank(oneContent));
        map.put("twoContent", FmtUtils.blank(twoContent));
        map.put("threeContent", FmtUtils.blank(threeContent));
        map.put("fourContent", FmtUtils.blank(fourContent));

        ContractDetail conDtl = rejectApp.getContractDetail();
        Contract contract = conDtl.getContract();
        ContractCraft craft = conDtl.getCraft();

        // 获取子公司
        Branch branch = rejectApp.getBranch();
        if (branch != null)
        {
            map.put("title1", FmtUtils.empty(branch.getName()));
            map.put("title2", FmtUtils.empty(branch.getNameEn()));
        }

        if (branch != null && StringUtils.isNotEmpty(branch.getLogoPath()))
        {
            String path = FileManageUtils.isLocal() ?
                basePath + ConstKey.GLOBAL_FILE_UPLOAD_PATH + branch.getLogoPath() :
                FileManageUtils.getUrl(branch.getLogoPath()).toString();
            map.put("image.logo", path);
        }

        map.put("ra.no", FmtUtils.blank(rejectApp.getNo()));
        map.put("ra.applyDate", FmtUtils.blank(FmtUtils.formatDateWithoutTime((rejectApp.getApplyDate()))));
        map.put("ra.contractDetail.craft.no", FmtUtils.blank(craft.getNo()));
        map.put("ra.contractDetail.craft.customerModel", FmtUtils.blank(craft.getCustomerModel()));
        map.put("ra.contractDetail.quantity", FmtUtils.blank(conDtl.getQuantity()) + " PCS");

        try
        {
            map.put("ra.contractDetail.deliveryDate",
                FmtUtils.blank(FmtUtils.formatDateWithoutTime((DateUtils.addDays(contract.getOrderDate(),
                    CommonUtils.getDays(conDtl.getDeliveryDays()) - 1)))));
        }
        catch (Exception e)
        {
        }

        map.put("ra.quantity", FmtUtils.blank(rejectApp.getQuantity()));

        Map<String, String> desireTreatment = Maps.newHashMap();
        desireTreatment.put(TypeKey.PU_RETURNS_DETAIL_TREATMENT_RETURN.toString(), "退货");
        desireTreatment.put(TypeKey.PU_RETURNS_DETAIL_TREATMENT_REDELIVER.toString(), "补货");
        map.put("ra.desireTreatment", CommonUtils.getConstValue(rejectApp.getDesireTreatment(), desireTreatment));

        map.put("ra.redeliverDate", FmtUtils.blank(FmtUtils.formatDateWithoutTime((rejectApp.getRedeliverDate()))));
        if (rejectApp.getApplicant() != null)
        {
            map.put("ra.applicant", FmtUtils.blank(rejectApp.getApplicant().getUserName()));
        }
        map.put("ra.rejectCause", FmtUtils.blank(rejectApp.getRejectCause()));

        String treatOpinion = null;
        if (TypeKey.ST_REJECTAPPLICATION_STATUS_RETURNAUDITED.equals(rejectApp.getStatus())
            || TypeKey.ST_REJECTAPPLICATION_STATUS_STORED.equals(rejectApp.getStatus()))
        {
            treatOpinion = "已批准  ";
        }
        else if (TypeKey.ST_REJECTAPPLICATION_STATUS_DENIED.equals(rejectApp.getStatus()))
        {
            treatOpinion = "未批准  ";
        }
        if (rejectApp.getTreatOpinion() != null)
        {
            if (treatOpinion != null)
            {
                treatOpinion = treatOpinion + rejectApp.getTreatOpinion();
            }
            else
            {
                treatOpinion = rejectApp.getTreatOpinion();
            }
        }
        map.put("ra.treatOpinion", FmtUtils.empty(treatOpinion));

        String template = "";
        template = StringUtils.isNotEmpty(template) ? template : ConstKey.DEFAULT_TEMPLATE_PATH;
        pdf.setFileName(template + "reject.pdf");
        pdf.setDataMap(map);

        return pdf;
    }

    /**
     * 查询可以客诉补料的客诉单
     *
     * @return
     */
    public List<RejectApplication> findrejectList()
    {
        RejectApplication rece = new RejectApplication();
        rece.setStatus(TypeKey.ST_REJECTAPPLICATION_STATUS_RETURNAUDITED);
        rece.setCompany(UserUtils.getUser().getCompany());
        return rejectApplicationDao.findrejectList(rece);
    }

    /**
     * 查询客户的客诉单
     */
    public List<RejectApplication> selectComplaints(Customer customer)
    {
        List<RejectApplication> rjas = rejectApplicationDao.selectComplaintsIncludeDeposit(customer);
        if (Collections3.isNotEmpty(rjas))
        {
            for (int i = 0; i < rjas.size(); i++)
            {
                String size = "";
                if (rjas.get(i).getContractDetail() != null && rjas.get(i).getContractDetail().getCraft() != null)
                {
                    String length = null;
                    String width = null;
                    String divisor = null;
                    NumberFormat nf = NumberFormat.getInstance();
                    if (rjas.get(i).getContractDetail().getCraft().getPnlLength() != null)
                    {
                        length = nf.format(rjas.get(i).getContractDetail().getCraft().getPnlLength());
                    }
                    if (rjas.get(i).getContractDetail().getCraft().getPnlWidth() != null)
                    {
                        width = nf.format(rjas.get(i).getContractDetail().getCraft().getPnlWidth());
                    }
                    if (rjas.get(i).getContractDetail().getCraft().getPnlDivisor() != null)
                    {
                        divisor = nf.format(rjas.get(i).getContractDetail().getCraft().getPnlDivisor());
                    }

                    if (length != null && width != null && divisor != null)
                    {
                        size = length + "*" + width + "/" + divisor;
                    }
                    else
                    {
                        size = "";
                    }
                    rjas.get(i).getContractDetail().setDeliveSize(size);
                }

                if (rjas.get(i).getQuantity().equals(rjas.get(i).getAlDeliveryQtys()))
                {
                    rjas.set(i, null);
                }

            }
            rjas.removeAll(Collections.singleton(null));

        }
        if (customer.getTempDelDetails() != null && customer.getTempDelDetails().size() > 0 && rjas != null
            && rjas.size() > 0)
        {
            List<DeliveryDetail> temps = customer.getTempDelDetails();
            List<RejectApplication> rs = new ArrayList<RejectApplication>();
            for (RejectApplication rejectApplication : rjas)
            {
                boolean isList = false;
                for (DeliveryDetail temp : temps)
                {
                    if (temp != null && temp.getComplaint() != null && rejectApplication.getRecordId()
                        .equals(temp.getComplaint().getRecordId()))
                    {

                        // 如果已送货数量等于 客诉单数量
                        if (rejectApplication.getQuantity()
                            .toString()
                            .equals(rejectApplication.getAlDeliveryQtys().toString()))
                        {
                            isList = true;
                        }
                    }
                }
                if (!isList)
                {
                    rs.add(rejectApplication);
                }
            }
            return rs;
        }
        return rjas;
    }

    public List<RejectApplication> findrejectListShow(RejectApplication reject)
    {
        List<RejectApplication> list = rejectApplicationDao.findrejectListShow(reject);
        Iterator<RejectApplication> iter = list.iterator();
        while (iter.hasNext())
        {
            RejectApplication rej = iter.next();
            rej.setCompany(reject.getCompany());

            // 若客诉数量为0过滤掉
            if (null == rej.getQuantity() || rej.getQuantity().intValue() <= 0)
            {
                iter.remove();
                continue;
            }

            // 寄存客诉单跳过WC 2017-02-23
            // if (rej.getStyle().equals(TypeKey.COMPLAINT_STYLE_DEPOSIT))
            // {
            // continue;
            // }
            // 若数据有问题，过滤掉
            if (rej.getContractDetail() == null || StringUtils.isBlank(rej.getContractDetail().getRecordId()))
            {
                iter.remove();
                continue;
            }

            // 查询出该合同明细对应的客诉单的已审核的总数
            rej.getContractDetail().setCompany(rej.getCompany());
            String qty = rejectApplicationDao.getComplaintQtyByContractDetail(rej.getContractDetail());
            if (StringUtils.isBlank(qty))
            {
                iter.remove();
                continue;
            }

            // 若送货数量大于当前合同明细的数量，不能客诉
            int qty1 = Integer.parseInt(qty);
            int delivQty = rej.getContractDetail().getAlDeliveryRejQty() == null ?
                0 :
                rej.getContractDetail().getAlDeliveryRejQty();
            if (delivQty >= qty1)
            {
                iter.remove();
            }
        }
        return list;
    }

    /**
     * 获取已客诉审核后的数量
     */
    public int getQuantityTotle(ContractDetail contractDetail)
    {
        return rejectApplicationDao.getQuantityTotle(contractDetail) == null ?
            0 :
            rejectApplicationDao.getQuantityTotle(contractDetail);
    }

    /**
     * fzd 2017-01-05 根据合同明细编号获取已添加的客诉数量
     */
    public Integer getCustComplaintGoods(ContractDetail contractDetail)
    {
        return rejectApplicationDao.getCustComplaintGoods(contractDetail);
    }

    /**
     * WC 2017-02-15 根据客户ID查询所有寄存送货单
     */
    public List<Delivery> getDeliveryListByCustomID(Customer customer)
    {
        Delivery delivery = new Delivery();
        delivery.setCustomer(customer);
        delivery.setStatus(TypeKey.PU_ORDER_DELIVERY_STATUS_CONFIRMED); // 已确认
        delivery.setCompany(UserUtils.getUser().getCompany());
        delivery.setItemType(Delivery.ITEM_DEPOSIT_TYPE); // 寄存送货单
        return deliveryDao.getAllDeliveryListByCustomID(delivery);
    }

    /**
     * WC 2017-02-15 根据寄存送货单ID获取所有已添加的客诉数量
     */
    public Integer getCustComplaintGoodsByDelDtlId(String deliveryDetailId)
    {
        RejectApplication ra = new RejectApplication();
        ra.setCompany(UserUtils.getUser().getCompany());
        ra.setActiveFlag(TypeKey.ACTIVE.toString());
        ra.setDeliveryDetail(new DeliveryDetail(deliveryDetailId));
        Integer data = rejectApplicationDao.getCustComplaintGoodsByDelDtlId(ra);
        data = data == null ? 0 : data;
        return data;
    }

    // 文件上传保存至中间表
    @Transactional(readOnly = false)
    public void uploadFile(ComplaintFile complaintFile)
    {
        rejectApplicationDao.uploadFile(complaintFile);
    }

    public List<ComplaintFile> getFile(RejectApplication rejectApplication)
    {
        List<ComplaintFile> list = rejectApplicationDao.getFile(rejectApplication);
        if (list != null && !list.isEmpty())
        {
            list = this.getFilesUrl(list);
        }
        return list;
    }

    public List<ComplaintFile> getFilesUrl(List<ComplaintFile> list)
    {
        // Map<String, Object> result = Maps.newHashMap();
        if (FileManageUtils.isLocal())
        {
            // 设置附件的URL
            list.forEach(item -> {
                item.setTempUrl(item.getFileUrl());
            });

        }
        else
        {
            // 设置附件的URL
            list.forEach(item -> {
                item.setTempUrl(FileManageUtils.getUrl(item.getFileUrl()).toString());
            });
            // result.put("fBoard", list);
        }
        return list;
    }

    // lq 2019-01-25 清除附件
    public void clearUpload(ComplaintFile complaintFile)
    {
        rejectApplicationDao.clearUpload(complaintFile);
    }

    @Transactional(readOnly = false)
    public Map<String, Object> updateQualitity(RejectApplication rejectApplication)
    {
        Map<String, Object> data = new HashMap<>();
        User user = UserUtils.getUser();
        rejectApplication.setCompany(user.getCompany());
        rejectApplication.setStatus(TypeKey.ST_REJECTAPPLICATION_STATUS_QUATITY);
        // Integer num = rejectApplicationDao.getRejectNum(rejectApplication);
        // if (num != null && num > 0)
        // {
        // return "false";
        // }
        String result = ModifyUtils.complaintApproval(rejectApplication, rejectApplication.getCompany(), 2, user);
        if (!"fail".equals(result))
        {
            data.put("result", "success");
            data.put("message", result);
            return data;
        }
        if ("fail".equals(result))
        {
            data.put("result", "fail");
            data.put("message", "请先配置客诉审批");
            return data;
        }
        ModifyUtils.sentRejectApplicationData(rejectApplication, user, 2);
        data.put("result", "success");
        data.put("message", "品质确认成功!");
        return data;
    }

    // private void executeGenerateGoodsCheck(RejectApplication applications, RejectApplication rejectApplication)
    // {
    // ProductStore productStore = new ProductStore();
    // productStore.setInoutType(TypeKey.ST_PRODUCT_INOUTTYPE_RETURN);
    // if (rejectApplication.getRepirNums() != null && rejectApplication.getRepirNums() > 0)
    // {
    // productStore.setQuantity(rejectApplication.getRepirNums());
    // }
    // productStore.setAmount(rejectApplication.getCompensation());
    // productStore.setOperateDate(new Date());
    // productStore.setOper(UserUtils.getUser());
    // productStore.setStatus(TypeKey.BILL_STATUS_NORMAL);
    // productStore.preInsert();
    // if (StringUtils.isNotBlank(applications.getJxAppId()))
    // {
    // Company company = UserUtils.getUser().getCompany();
    // Customer customer = new Customer(applications.getJxCustId());
    // Material material = materialDao.findMaterialByNo(new Material(applications.getCraftNo(), company));
    // productStore.setCompany(company);
    // productStore.setCustomer(customer);
    // productStore.setMaterial(material);
    // ContractDetail contractDetail = new ContractDetail(applications.getJxAppId());
    // productStore.setContractDetail(contractDetail);
    // productStoreDao.insert(productStore);
    // executeGenerateGoodsCheckTwo(rejectApplication,
    // applications.getJxCheckDate(),
    // company,
    // customer,
    // applications.getCraftNo());
    // }
    // if (StringUtils.isNotBlank(applications.getLnAppId()))
    // {
    // Company company = new Company(applications.getLnComId());
    // Customer customer = new Customer(applications.getLnCustId());
    // Material material = materialDao.findMaterialByNo(new Material(applications.getCraftNo(), company));
    // productStore.setCompany(company);
    // productStore.setCustomer(customer);
    // productStore.setMaterial(material);
    // ContractDetail contractDetail = new ContractDetail(applications.getLnAppId());
    // productStore.setContractDetail(contractDetail);
    // }
    // if (StringUtils.isNotBlank(applications.getXsAppId()))
    // {
    // Company company = new Company(applications.getXsComId());
    // Customer customer = new Customer(applications.getXsCustId());
    // Material material = materialDao.findMaterialByNo(new Material(applications.getCraftNo(), company));
    // productStore.setCompany(company);
    // productStore.setCustomer(customer);
    // productStore.setMaterial(material);
    // ContractDetail contractDetail = new ContractDetail(applications.getXsAppId());
    // productStore.setContractDetail(contractDetail);
    // }
    // }

    // private void executeGenerateGoodsCheckTwo(RejectApplication rejectApplication, String checkDate, Company company,
    // Customer customer, String craftNo)
    // {
    // // 计算此退货单的对账周期
    // Calendar cal = Calendar.getInstance();
    // cal.setTime(new Date());
    // Integer period = cal.get(Calendar.YEAR) * 100 + (cal.get(Calendar.MONTH) + 1);// 收货年月
    // Integer checkDay = new Integer(StringUtils.isEmpty(checkDate) ? "1" : checkDate);// 约定的对账日
    // if (checkDay < 15 && cal.get(Calendar.DATE) < checkDay)
    // {
    // // 如果仓库收货日期小于约定的对账日期且对账日期小于15日则划到上一个对周期，
    // // 如果仓库收货月份正好是1月份，则上个对周期是上个年度的12月份
    // period--;
    // if (period % 100 == 0)
    // {
    // period = (period / 100 - 1) * 100 + 12;
    // }
    // }
    // else if (checkDay >= 15 && cal.get(Calendar.DATE) > checkDay)
    // {
    // // 如果仓库收货日期大于约定的对账日期且对账日期大于等于15日则划到下一个周期
    // // 如果仓库收货月份正好是12月份，则下一下周期是下一年度的1月份
    // // 其余的情况当划到本月对账周期内
    // period++;
    // if (period % 100 == 13)
    // {
    // period = (period / 100 + 1) * 100 + 1;
    // }
    // }
    // /* Begin 获得该对账周期相应的应收账款记录 */
    // AccountsReceivable ar = new AccountsReceivable();
    // ar.setCompany(company);
    // ar.setCustomer(customer);// 取得客户方式和老版不一样
    // ar.setPeriod(period);
    // List<AccountsReceivable> arList = accountReceivableDao.findAccountsReceivableListByBean(ar);
    // if (arList != null && arList.size() > 0)
    // {
    // // 如果该对账周期的应收账款记录已有了，则在原记录是修改
    // ar = arList.get(0);
    // if (!ar.getAdjustFlag())
    // {
    // ar.setAdjustFlag(true);
    // accountReceivableDao.updateStatus(ar);
    // }
    // }
    // else
    // {
    // /* 如果无该对账周期的应收账款记录，则新生成一条 */
    // // 初始化其它属性
    // ar.setSentGoodsValue(new BigDecimal(0));
    // ar.setReturnGoodsValue(new BigDecimal(0));
    // ar.setReceivedAmount(new BigDecimal(0));
    // ar.setAdjustValue(new BigDecimal(0));
    // ar.setCompleteFlag(TypeKey.FALSE);
    // ar.setStatus(TypeKey.RECEIVABLE_STATUS_UNCONFIRMEND);
    // ar.setAdjustFlag(true);
    // ar.preInsert();
    // }
    // /* End 获得该对账周期相应的应收账款记录 */
    // // 将退货单中的数据导入到“货物对账单”表中
    // GoodsCheck gc = new GoodsCheck();
    // gc.setCompany(company);
    // gc.setCustomer(customer);
    // gc.setInOutFlag(TypeKey.GOODS_INOUTFLAG_RETURN);
    // gc.setOperateStatus(TypeKey.GOODSCHECK_STATUS_UNCONFIRM);
    // gc.setReceivedDate(new Date());
    // gc.setPeriod(period);
    // gc.setCraftNo(craftNo);
    // Integer qty = rejectApplication.getQuantity() == null ? 0 : rejectApplication.getQuantity();
    // gc.setQuantity(qty);
    // gc.setSourceId(Long.parseLong(rejectApplication.getRecordId()));
    // // 冲红原因与生产本条记录的源记录保持一致
    // gc.setStatus(TypeKey.BILL_STATUS_NORMAL);
    //
    // // zjn 2018-10-29 客诉金额、备注赋值
    // gc.setAmount(MathUtils.getBigDecimal(rejectApplication.getCompensation()));
    //
    // gc.preInsert();
    // // 保存送货记录
    // goodsCheckDao.insert(gc);
    // // 保存或修改应收账款记录
    // if (ar.getReturnGoodsValue() == null)
    // {
    // ar.setReturnGoodsValue(MathUtils.getBigDecimal(gc.getAmount()));
    // }
    // else
    // {
    // ar.setReturnGoodsValue(MathUtils.getBigDecimal(ar.getReturnGoodsValue().add(gc.getAmount())));
    // }
    // if (ar.getRecordId() == null)
    // {
    // accountReceivableDao.insert(ar);
    // }
    // else
    // {
    // if (BigDecimal.ZERO.compareTo(ar.getRcvAmount()) >= 0)
    // {
    // ar.setCompleteFlag(TypeKey.TRUE);
    // }
    // else
    // {
    // ar.setCompleteFlag(TypeKey.FALSE);
    // }
    // accountReceivableDao.update(ar);
    // }
    // }

    // private void executeGenerateProductRtsCheck(String returnsDetailId)
    // {
    // PuReturnsDetail puReturnsDetail = new PuReturnsDetail();
    // puReturnsDetail.setRecordId(returnsDetailId);
    // PuReturnsDetail returnsDetail = puReturnsDetailDao.getById(puReturnsDetail);
    // Company company = returnsDetail.getCompany();
    // // 获取退货单主表
    // PuReturns returnsChk = returnsDetail.getPuReturns();
    // // 计算此批进货的对账周期
    // Calendar cal = Calendar.getInstance();
    // Date recDate =
    // returnsChk.getReceivedDate() == null ? returnsChk.getCreatedDate() : returnsChk.getReceivedDate();
    // cal.setTime(recDate);
    // Integer period = cal.get(Calendar.YEAR) * 100 + (cal.get(Calendar.MONTH) + 1);// 收货年月
    // Integer checkDay =
    // returnsChk.getSupplier().getCheckDate() == null ? 1 : returnsChk.getSupplier().getCheckDate();// 约定的对账日
    // if (checkDay < 15 && cal.get(Calendar.DATE) < checkDay)
    // {
    // // 如果仓库收货日期小于约定的对账日期且对账日期小于15日则划到上一个对周期，
    // // 如果仓库收货月份正好是1月份，则上个对周期是上个年度的12月份
    // period--;
    // if (period % 100 == 0)
    // {
    // period = (period / 100 - 1) * 100 + 12;
    // }
    // }
    // else if (checkDay >= 15 && cal.get(Calendar.DATE) > checkDay)
    // {
    // // 如果仓库收货日期大于约定的对账日期且对账日期大于等于15日则划到下一个周期
    // // 如果仓库收货月份正好是12月份，则下一下周期是下一年度的1月份
    // // 其余的情况当划到本月对账周期内
    // period++;
    // if (period % 100 == 13)
    // {
    // period = (period / 100 + 1) * 100 + 1;
    // }
    // }
    //
    // /* Begin 获得该对账周期相应的应付账款记录 */
    //
    // AccountsPayable ap = new AccountsPayable();
    // ap.setCompany(returnsChk.getCompany());
    // ap.setSupplier(returnsChk.getSupplier());
    // ap.setPeriod(period);
    // //
    // List<AccountsPayable> apList = accountsPayableDao.findAccountsPayableListByBean(ap);
    // if (apList != null && apList.size() > 0)
    // {
    // // 如果该对账周期的应付账款记录已有了，则在原记录是修改
    // ap = apList.get(0);
    // if (!ap.getAdjustFlagTwo())
    // {
    // ap.setAdjustFlagTwo(true);
    // accountsPayableDao.updateStatus(ap);
    // }
    // }
    // else
    // {
    // // 初始化其它属性
    // ap.setPaiedAmount(new BigDecimal(0));
    // ap.setRecvMaterialValue(new BigDecimal(0));
    // ap.setRetnMaterialValue(new BigDecimal(0));
    // ap.setAdjustValue(new BigDecimal(0));
    // ap.setCompleteFlag(TypeKey.FALSE);
    // ap.setStatus(TypeKey.PAYABLE_STATUS_UNCONFIRMEND);
    // ap.setAdjustFlagTwo(true);
    // ap.preInsert();
    // /* End 如果无该对账周期的应付账款记录，则新生成一条 */
    // }
    // // 将退货单中的数据导入采购进货/退货对账单中
    // MaterialCheck mc = new MaterialCheck();
    // mc.setCompany(returnsChk.getCompany());
    // mc.setSupplier(returnsChk.getSupplier());
    // // 根据成品ID查询成品信息，获取物料
    // PrdorderDetail prdorderDetail = new PrdorderDetail();
    // prdorderDetail.setCompany(returnsDetail.getCompany());
    // prdorderDetail.setRecordId(returnsDetail.getPurchasingDetailId().toString());
    // PrdorderDetail prdoDetail = proDetailDao.getProductOrderDetailById(prdorderDetail);
    // if (prdoDetail == null || prdoDetail.getRecordId() == null)
    // {
    // throw new ServiceException(ErrorCode.ERROR_STORE_ILLEGALARGUMENT_VIOLATEBUSINESSRULE.toString(),
    // "您所指定的采购订单明细项不存在!");
    // }
    // Material material =
    // materialDao.findMaterialByNo(new Material(prdoDetail.getContractCraft().getNo(), prdoDetail.getCompany()));
    // if (material == null || material.getRecordId() == null)
    // {
    // throw new ServiceException(ErrorCode.ERROR_STORE_ILLEGALARGUMENT_VIOLATEBUSINESSRULE.toString(), "物料不存在!");
    // }
    // mc.setMaterial(material);
    // mc.setInOutFlag(TypeKey.MATERIAL_INOUTFLAG_RETURN);
    // mc.setOperateStatus(TypeKey.MATERIAL_STATUS_UNCONFIRM);
    // mc.setPeriod(period);
    // mc.setReceivedDate(recDate);
    // mc.setBillNo(returnsChk.getNo());
    // BigDecimal price = returnsDetail.getPrice() == null ? new BigDecimal(0) : returnsDetail.getPrice();
    // // 仅对采购成品退货数量生成对账单
    // mc.setQuantity(returnsDetail.getQuantity());
    // mc.setPrice(price);
    // mc.setAmount(MathUtils.getBigDecimal(returnsDetail.getAmount()));
    // mc.setSourceId(returnsDetail.getRecordId());// 产生此条记录的源记录ID
    // mc.setStatus(TypeKey.BILL_STATUS_NORMAL);// 冲红状态也产生此条记录的源记录一致
    // mc.preInsert();
    // // 修改采购应付款对账单的数据
    // ap.setRetnMaterialValue(MathUtils.getBigDecimal(mc.getAmount()));
    // // 保存退货出库记录
    // ProductStore productStore = new ProductStore();
    // productStore.setCompany(company);
    // productStore.setMaterial(material);
    // productStore.setInoutType(TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_OUT);
    // productStore.setQuantity(returnsDetail.getQuantity().intValue());
    // productStore.setAmount(returnsDetail.getAmount());
    // productStore.setOperateDate(new Date());
    // productStore.setOper(UserUtils.getUser());
    // productStore.setStatus(TypeKey.BILL_STATUS_NORMAL);
    // productStore.preInsert();
    // ContractDetail contractDetail = new ContractDetail(returnsDetailId);
    // productStore.setContractDetail(contractDetail);
    // productStoreDao.insert(productStore);
    // // 保存退货对账记录
    // materialCheckDao.insert(mc);
    //
    // // 修改或添加采购庆付款对账单记录
    // if (ap.getIsNewRecord())
    // {
    // accountsPayableDao.insert(ap);
    // }
    // else
    // {
    // ap.preUpdate();
    // accountsPayableDao.updateAccountsPayable(ap);
    // }
    // }

    public List<EgProcess> getProcessList(RejectApplication rejectApplication)
    {
        rejectApplication = rejectApplicationDao.getRejectAndMaterial(rejectApplication);
        return rejectApplicationDao.getProcessList(rejectApplication);
    }

    public String saveRework(Rework rework)
    {
        // TODO 校验
        rework.setCompany(UserUtils.getUser().getCompany());
        if (rework.getIsNewRecord())
        {
            // 报废记录id转负数，当做检测id保存
            rework.preInsert();
            reworkDao.insert(rework);
        }
        else
        {
            rework.preInsert();
            reworkDao.update(rework);
        }

        return "success";
    }

    public String saveDiscard(Discard discard)
    {
        // TODO 校验
        discard.setCompany(UserUtils.getUser().getCompany());
        if (discard.getIsNewRecord())
        {
            // 报废记录id转负数，当做检测id保存
            discard.preInsert();
            discardDao.insert(discard);
        }
        else
        {
            discard.preInsert();
            discardDao.update(discard);
        }

        return "success";
    }

    public List<RejectApplication> findReject(RejectApplication reject)
    {
        if (null == reject)
        {
            reject = new RejectApplication();
        }
        reject.setCompany(UserUtils.getUser().getCompany());
        reject.setStatus(400102);
        return rejectApplicationDao.findReject(reject);
    }

    public PageNoInterceptor<RejectTraceReportVo> getSaleReportList(PageNoInterceptor<RejectTraceReportVo> page,
        RejectTraceReportVo saleCombina)
    {
        saleCombina.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        List<RejectTraceReportVo> list = getSaleReports(saleCombina);

        Integer count = 0;
        String factoryComId = CompanyUtil.getInstance().getFactId();
        saleCombina.setFactoryComId(factoryComId);
        count = rejectApplicationDao.getSaleReportListThreeCount(saleCombina);
        page.setCount(count);
        page.setList(list);
        return page;
    }

    public List<RejectTraceReportVo> getSaleReports(RejectTraceReportVo saleCombina)
    {
        String factoryComId = CompanyUtil.getInstance().getFactId();
        saleCombina.setFactoryComId(factoryComId);
        List<RejectTraceReportVo> saleList = rejectApplicationDao.getSaleReportListTwo(saleCombina);

        String feedIds = "";
        String purchDeailIds = "";
        for (int i = 0; i < saleList.size(); i++)
        {
            if (StringUtils.isNotBlank(saleList.get(i).getFeedId()))
            {
                if (StringUtils.isNotBlank(feedIds))
                {
                    feedIds = feedIds + "," + saleList.get(i).getFeedId();
                }
                else
                {
                    feedIds = saleList.get(i).getFeedId();
                }
            }
            if (StringUtils.isNotBlank(saleList.get(i).getPurchDeailId()))
            {
                if (StringUtils.isNotBlank(purchDeailIds))
                {
                    purchDeailIds = purchDeailIds + "," + saleList.get(i).getPurchDeailId();
                }
                else
                {
                    purchDeailIds = saleList.get(i).getPurchDeailId();
                }
            }
        }
        List<ProduceRecord> recordLists = new ArrayList<ProduceRecord>();
        List<ProduceRecord> resultLists = new ArrayList<ProduceRecord>();
        if (StringUtils.isNotBlank(feedIds))
        {
            // 获取相关的过数记录
            recordLists = rejectApplicationDao.getRecordBybatchByNotifi(feedIds);

            resultLists = rejectApplicationDao.getProduceByDeail(feedIds);
        }
        List<RawmaterialStock> list = new ArrayList<RawmaterialStock>();
        if (StringUtils.isNotBlank(purchDeailIds))
        {
            // 获取相关的入库记录
            list = contractDao.getPurchDeailStore(purchDeailIds);
        }

        List<ProductionCycleCate> caList = produceRecordDao.getProcessByCycleList(saleCombina.getCompanyId());
        for (int i = 0; i < saleList.size(); i++)
        {
            if (StringUtils.isNotBlank(saleList.get(i).getPurchDeailId()))
            {
                BigDecimal quatity = BigDecimal.ZERO;
                Date opearDate = null;
                // 实际收货时间、实际入库数量
                for (int j = 0; j < list.size(); j++)
                {
                    if (list.get(j).getPurchasingDtlId() != null && list.get(j)
                        .getPurchasingDtlId()
                        .toString()
                        .equals(saleList.get(i).getPurchDeailId()))
                    {
                        if (list.get(j).getQuantity() != null)
                        {
                            quatity = quatity.add(list.get(j).getQuantity());
                            opearDate = list.get(j).getOperateDate();
                        }
                    }
                }
                saleList.get(i).setRowStoreDate(opearDate);
                saleList.get(i).setRowStoreQua(quatity);
            }

            // 有问题的生产过数周期
            if (saleList.get(i).getEstimateDate() != null && saleList.get(i).getNotiNeedDays() != null)
            {
                // 赋予本投料单的过数记录
                List<ProduceRecord> recordList = new ArrayList<ProduceRecord>();
                if (StringUtils.isNotBlank(saleList.get(i).getFeedId()) && !recordLists.isEmpty())
                {
                    for (int j = 0; j < recordLists.size(); j++)
                    {
                        if (saleList.get(i).getFeedId().equals(recordLists.get(j).getFeedId()))
                        {
                            recordList.add(recordLists.get(j));
                        }
                    }
                }

                String realTime = "";
                if (StringUtils.isNotBlank(saleList.get(i).getProductionCycleId()))
                {
                    for (int j = 0; j < caList.size(); j++)
                    {
                        if (caList.get(j).getCycleId().equals(saleList.get(i).getProductionCycleId()))
                        {
                            // 真实工序耗时
                            if (StringUtils.isNotBlank(caList.get(j).getCateName()) && recordList != null
                                && recordList.size() > 0 && caList.get(j).getNeedDays() != null)
                            {
                                for (int z = 0; z < recordList.size(); z++)
                                {
                                    if (StringUtils.isNotBlank(recordList.get(z).getCategory()) && recordList.get(z)
                                        .getCategory()
                                        .equals(caList.get(j).getCateName())
                                        && recordList.get(z).getTakeOverTime() != null)
                                    {
                                        if (recordList.size() > z + 1
                                            && recordList.get(z + 1).getTakeOverTime() != null)
                                        {
                                            Date date = recordList.get(z + 1).getTakeOverTime();
                                            double num =
                                                DateUtils.pastDaysThree(recordList.get(z).getTakeOverTime(), date);
                                            if (num > caList.get(j).getNeedDays())
                                            {
                                                realTime = realTime + caList.get(j).getCateName() + "(耗时" + num + "天,需要"
                                                    + caList.get(j).getNeedDays() + "天,已延期" + (num - caList.get(j)
                                                    .getNeedDays()) + "天)";
                                            }
                                        }
                                        else
                                        {
                                            double num = DateUtils.pastDaysThree(recordList.get(z).getTakeOverTime(),
                                                new Date());
                                            if (num > caList.get(j).getNeedDays())
                                            {
                                                realTime = realTime + caList.get(j).getCateName() + "(耗时" + num + "天,需要"
                                                    + caList.get(j).getNeedDays() + "天,已延期" + (num - caList.get(j)
                                                    .getNeedDays()) + "天)";
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                saleList.get(i).setRealTime(realTime);
            }

            if (StringUtils.isNotBlank(saleList.get(i).getFeedId()))
            {
                List<ProduceRecord> resultList = new ArrayList<ProduceRecord>();
                if (StringUtils.isNotBlank(saleList.get(i).getFeedId()) && !resultLists.isEmpty())
                {
                    for (int j = 0; j < resultLists.size(); j++)
                    {
                        if (saleList.get(i).getFeedId().equals(resultLists.get(j).getFeedId()))
                        {
                            resultList.add(resultLists.get(j));
                        }
                    }
                }
                List<ProduceRecord> recordList = new ArrayList<ProduceRecord>();
                Map<String, String> map = new HashMap<String, String>();
                for (int j = 0; j < resultList.size(); j++)
                {
                    if (map.containsKey(resultList.get(j).getProduceBatchDetailId()))
                    {
                        continue;
                    }
                    else
                    {
                        if (resultList.get(j).getHandOverFlag().equals("2"))
                        {
                            map.put(resultList.get(j).getProduceBatchDetailId(),
                                resultList.get(j).getProduceBatchDetailId());
                            recordList.add(resultList.get(j));
                        }
                        else
                        {
                            Integer recordId = 0;
                            ProduceRecord record = new ProduceRecord();
                            for (int l = 0; l < resultList.size(); l++)
                            {
                                if (resultList.get(l)
                                    .getProduceBatchDetailId()
                                    .equals(resultList.get(j).getProduceBatchDetailId()))
                                {
                                    if (resultList.get(l).getHandOverFlag().equals("2"))
                                    {
                                        map.put(resultList.get(l).getProduceBatchDetailId(),
                                            resultList.get(l).getProduceBatchDetailId());
                                        recordList.add(resultList.get(l));
                                        break;
                                    }
                                    else
                                    {
                                        if (Integer.valueOf(resultList.get(l).getRecordId()) > recordId)
                                        {
                                            recordId = Integer.valueOf(resultList.get(l).getRecordId());
                                            record = resultList.get(l);
                                        }
                                    }
                                }
                            }
                            if (record != null && StringUtils.isNotBlank(record.getRecordId()))
                            {
                                map.put(record.getProduceBatchDetailId(), record.getProduceBatchDetailId());
                                recordList.add(record);
                            }
                        }
                    }
                }
                if (recordList.size() > 0)
                {
                    String currName = "";
                    for (int j = 0; j < recordList.size(); j++)
                    {
                        if (StringUtils.isNotBlank(recordList.get(j).getName()))
                        {
                            currName = currName + "第" + (j + 1) + "批：" + recordList.get(j).getName();
                        }
                    }
                    saleList.get(i).setProcess(currName);
                }
            }

            String res = "";
            if (saleList.get(i).getProStoreDate() != null)
            {
                // 已入库
                res = "完工";
                if (saleList.get(i).getDeliveryDate() != null)
                {
                    if (saleList.get(i).getProStoreDate().getTime() > saleList.get(i).getDeliveryDate().getTime())
                    {
                        res = res + "延期";
                    }
                    else
                    {
                        res = res + "正常";
                    }
                }
            }
            else
            {
                // 未入库
                if (StringUtils.isNotBlank(saleList.get(i).getAcDistributeDateStr()))
                {
                    res = "生产中";
                }
                else
                {
                    res = "待安排";
                }
                if (saleList.get(i).getDeliveryDate() != null)
                {
                    Date date = new Date();
                    if (date.getTime() > saleList.get(i).getDeliveryDate().getTime())
                    {
                        res = res + "延期";
                    }
                    else
                    {
                        res = res + "正常";
                    }
                }
            }
            saleList.get(i).setRes(res);

            String purchStatus = "不需要采购";
            if (saleList.get(i).getReplyStock() != null && saleList.get(i).getReplyStock() > 0)
            {
                if (saleList.get(i).getRowStoreDate() != null)
                {
                    // 已入库
                    purchStatus = "入库";
                    if (saleList.get(i).getEstimateAcDistr() != null)
                    {
                        if (saleList.get(i).getRowStoreDate().getTime() > saleList.get(i)
                            .getEstimateAcDistr()
                            .getTime())
                        {
                            purchStatus = purchStatus + "延期";
                        }
                        else
                        {
                            purchStatus = purchStatus + "正常";
                        }
                    }
                }
                else
                {
                    // 未入库
                    if (StringUtils.isNotBlank(saleList.get(i).getPurchDeailId()))
                    {
                        purchStatus = "采购中";
                    }
                    else
                    {
                        purchStatus = "未开始";
                    }
                    if (saleList.get(i).getEstimateAcDistr() != null)
                    {
                        Date date = new Date();
                        if (date.getTime() > saleList.get(i).getEstimateAcDistr().getTime())
                        {
                            purchStatus = purchStatus + "延期";
                        }
                        else
                        {
                            purchStatus = purchStatus + "正常";
                        }
                    }
                }
            }
            saleList.get(i).setPurchStatus(purchStatus);
            // 计算送货状态
            if (saleList.get(i).getStockDeliveryDate() != null)
            {
                if (saleList.get(i).getStockDeliveryDate().getTime() > saleList.get(i).getDeliveryDate().getTime())
                {
                    double num = DateUtils.pastDaysThree(saleList.get(i).getDeliveryDate(),
                        saleList.get(i).getStockDeliveryDate());
                    saleList.get(i).setDeliverStatus("延期" + num + "天");
                }
                else
                {
                    saleList.get(i).setDeliverStatus("正常");
                }
            }
            else
            {
                Date date = new Date();
                if (null == saleList.get(i).getDeliveryDate())
                {
                    saleList.get(i).setDeliverStatus("");
                }
                else if (date.getTime() > saleList.get(i).getDeliveryDate().getTime())
                {
                    double num = DateUtils.pastDaysThree(saleList.get(i).getDeliveryDate(), date);
                    saleList.get(i).setDeliverStatus("延期" + num + "天");
                }
                else
                {
                    saleList.get(i).setDeliverStatus("正常");
                }
            }
        }
        return saleList;
    }

    public List<RejectTraceReportVo> getSaleReportExcelList(RejectTraceReportVo saleCombina)
    {
        saleCombina.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        return getSaleReports(saleCombina);
    }

    public String getComplaintInfo(String craftNo, Company company)
    {
        String result = "";
        if (StringUtils.isBlank(craftNo))
        {
            return result;
        }
        String[] craftList = craftNo.split(ConstKey.COMMA);
        String craftNos = "";
        for (String no : craftList)
        {
            if (StringUtils.isNotBlank(craftNos))
            {
                craftNos = craftNos + ",'" + no + "'";
            }
            else
            {
                craftNos = "'" + no + "'";
            }
        }
        RejectApplication re = new RejectApplication();
        re.setCompany(company);
        re.setCraftNos(craftNos);
        result = rejectApplicationDao.getRejectCause(re);
        return result;
    }

    public String getComplaintInfoByCustomerModel(String customerModel, Company company)
    {
        ContractCraft craft = new ContractCraft();
        craft.setCompany(company);
        craft.setCustomerModel(customerModel);

        // 客诉列表
        List<RejectApplication> rejects = rejectApplicationDao.findListByCustomerModel(craft);
        String result = "";
        for (RejectApplication rejectApplication : rejects)
        {
            if (StringUtils.isNotBlank(rejectApplication.getRejectCause()))
            {
                if (StringUtils.isNotBlank(result))
                {
                    result += "; ";
                }
                result += rejectApplication.getRejectCause();
            }
        }

        return result;
    }

    public String getInspectInfo(String recordId, Company company)
    {
        String result = "";
        if (StringUtils.isBlank(recordId))
        {
            return result;
        }
        Notification n = new Notification();
        n.setCompany(company);
        n.setRecordId(recordId);
        result = rejectApplicationDao.getInspectDiscardCauseByNotifi(n);
        return result;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> doRollBack(RejectApplication rejectApplication)
    {
        Map<String, Object> data = new HashMap<>();
        // 检查同一产品明细，不能存在两张末处理客诉单
        int num = rejectApplicationDao.checkIsExsit(rejectApplication);
        if (num > 0)
        {
            data.put("result", "fail");
            data.put("message", "同一产品明细，已经存在末处理客诉单,不能反审核!");
            return data;
        }
        // 状态为审核的才能反审核
        if (400102 != rejectApplication.getStatus() && 400103 != rejectApplication.getStatus())
        {
            data.put("result", "fail");
            data.put("message", "客诉单不是审核状态,不能反审核!");
            return data;
        }
        // 终端客诉单才能反审核
        if (!StringUtils.isEmpty(rejectApplication.getSourceId()) && !rejectApplication.getRecordId()
            .equals(rejectApplication.getSourceId()))
        {
            data.put("result", "fail");
            data.put("message", "客诉单不是终端客诉单,不能反审核!");
            return data;
        }
        if (StringUtils.isBlank(rejectApplication.getGroupCenterId()) || rejectApplication.getOutsourceFlag())
        {
            // 外协
            // 验证是否已送货 -1、送货，不能反审核;2、未送货、反审核--回退数据;
            if (hasOutBound(rejectApplication))
            {
                data.put("result", "fail");
                data.put("message", "客诉单" + rejectApplication.getNo() + "已经送货,不能反审核!");
                return data;
            }
            else
            {
                rollBackOutBound(rejectApplication);
                rollBackRejectInfo(rejectApplication);
                data.put("result", "success");
                return data;
            }

        }
        else
        {
            // 工厂
            // 验证1、已送货;2、生成投料单;--不能反审核 3、其它 --反审核--回滚相关数据
            if (hasOutBound(rejectApplication))
            {
                data.put("result", "fail");
                data.put("message", "客诉单" + rejectApplication.getNo() + "已经送货,不能反审核!");
                return data;
            }
            else
            {
                // 1、相关客诉单全部重置为历史状态，复制销售公司的数据;
                // 分两个阶段回退 客诉单审核 ;品质审核（提交审批-审批通过）;
                // 状态改为新建 2/3
                // 生成退货入库记录与客诉退货入库对账单 回退 成品采购入库
                // TODO 销售、龙南 客诉退回入库记录 重置为历史状态
                // 保存送货记录 重置为历史状态
                // 回退 对应收账款记录修改 executeGenerateGoodsCheckOutsource
                // 处理进程 品质未受理才能反审核
                if ("品质未受理".equals(rejectApplication.getExportProcess()))
                {
                    rollBackRejectInfo(rejectApplication);
                    data.put("result", "success");
                    data.put("message", "审核成功!");
                    return data;
                }
                else
                {
                    data.put("result", "fail");
                    data.put("message", "客诉单" + rejectApplication.getNo() + ",品质已处理,请联系品质驳回,再反审核!");
                    return data;
                }

            }
        }
    }

    private void rollBackRejectInfo(RejectApplication rejectApplication)
    {
        // 将对应的客诉单
        List<RejectApplication> rejects = rejectApplicationDao.getRejectsAllCompany(rejectApplication);
        RejectApplication xsReject = null;
        CockpitUtilsOne cockpit = new CockpitUtilsOne();
        if (!CollectionUtils.isEmpty(rejects))
        {
            String ids = "";
            for (RejectApplication r : rejects)
            {
                RejectApplication reject  = new RejectApplication();
                reject.setRecordId(r.getRecordId());
                cockpit.addOperatingRecord(6,reject,2);
                ids = ids + r.getRecordId() + ",";
                if (CompanyUtil.getInstance().valiteSaleCompany(r.getCompanyId()))
                {
                    xsReject = r;
                }
            }
            if (ids.length() > 0)
            {
                ids = ids.substring(0, ids.length() - 1);
            }
            rejectApplicationDao.updateTohistory(ids);
        }
        else
        {
            rejectApplicationDao.updateTohistory(rejectApplication.getRecordId());

        }
        // 复制销售客诉单
        if (null == xsReject)
        {
            xsReject = rejectApplication;
        }
        // 清空反审核不需要的数据
        resetReject(xsReject);
        rejectApplicationDao.insert(xsReject);
        cockpit.addOperatingRecord(6,xsReject,1);

    }

    private void resetReject(RejectApplication xsReject)
    {
        //
        xsReject.setRecordId(null);
        xsReject.setSourceId(null);
        xsReject.setRepirNums(null);
        xsReject.setRepirDiscardNum(null);
        xsReject.setFedNo(null);
        xsReject.setStatus(TypeKey.ST_REJECTAPPLICATION_STATUS_RETURNAPPLY);
        if (xsReject.getCompany() == null)
        {
            xsReject.setCompany(new Company(xsReject.getCompanyId()));
        }
        if (xsReject.getContractDetail() == null)
        {
            ContractDetail con = new ContractDetail();
            con.setRecordId(xsReject.getContractDeailId());
            xsReject.setContractDetail(con);

        }
        if (xsReject.getMaterial() == null)
        {
            xsReject.getContractDetail().setMaterialId(xsReject.getMaterialId());
        }
        else
        {
            xsReject.getContractDetail().setMaterialId(xsReject.getMaterial().getRecordId());
        }
        if (xsReject.getCustomer() == null)
        {
            xsReject.setCustomer(new Customer(xsReject.getCustomerId()));
        }
        xsReject.preUpdate();
    }

    private void rollBackOutBound(RejectApplication rejectApplication)
    {
        String ecoemyId = null;
        if (null != rejectApplication.getContractDetail() && StringUtils.isNotBlank(rejectApplication.getContractDetail().getRecordId()))
        {
            ecoemyId = contractDetailDao.getEcoemyIdTwo(rejectApplication.getContractDetail().getRecordId());
            if (null == ecoemyId && StringUtils.isBlank(ecoemyId))
            {
                ecoemyId = CompanyUtil.getInstance().getEcoemyId();
            }
        }
        else
        {
            ecoemyId = CompanyUtil.getInstance().getEcoemyId();
        }
        rejectApplication.setEcoemyId(ecoemyId);
        RejectApplication applications = rejectApplicationDao.findGroupDataTwo(rejectApplication);
        if (StringUtils.isNotBlank(applications.getXsAppId()))
        {
            rollBackStore(applications.getXsAppId(), rejectApplication.getNo(), applications.getXsCheckDate());
        }

        if (StringUtils.isNotBlank(applications.getLnAppId()))
        {
            rollBackStore(applications.getLnAppId(), rejectApplication.getNo(), applications.getLnCheckDate());
        }
    }

    /**
     * @param compalintId 客诉id
     * @param no 客诉单号
     * @param checkDate
     */
    private void rollBackStore(String compalintId, String no, String checkDate)
    {
        List<ProductStore> productStores = rejectApplicationDao.getRejectReturnStore(compalintId);
        if (!CollectionUtils.isEmpty(productStores))
        {
            for (ProductStore ps : productStores)
            {
                // 更新为被冲红
                ps.setStatus(TypeKey.BILL_STATUS_BE_WRITEOFFED);
                // ps.setActiveFlag("0");
                ps.setWriteOffCause("客诉单" + no + "反审核");
                ps.preUpdate();
                productStoreDao.updateReturnStoreStatus(ps);

                // 新增冲红记录
                rollBackGoodsCheck(ps, checkDate);

                ps.setStatus(TypeKey.BILL_STATUS_FOR_WRITEOFF);
                ps.setActiveFlag("1");
                ps.preInsert();
                productStoreDao.insert(ps);

            }
        }
    }

    /**
     * @param rejectApplication
     */
    private void rollBackGoodsCheck(ProductStore productStore, String checkDate)
    {
        GoodsCheck gc = new GoodsCheck();// 设置为历史状态

        AccountsReceivable ar = new AccountsReceivable(); // 减去金额
        // 计算此退货单的对账周期
        Calendar cal = Calendar.getInstance();
        cal.setTime(productStore.getOperateDate());
        Integer period = cal.get(Calendar.YEAR) * 100 + (cal.get(Calendar.MONTH) + 1);// 收货年月
        Integer checkDay = new Integer(StringUtils.isEmpty(checkDate) ? "1" : checkDate);// 约定的对账日
        if (checkDay < 15 && cal.get(Calendar.DATE) < checkDay)
        {
            // 如果仓库收货日期小于约定的对账日期且对账日期小于15日则划到上一个对周期，
            // 如果仓库收货月份正好是1月份，则上个对周期是上个年度的12月份
            period--;
            if (period % 100 == 0)
            {
                period = (period / 100 - 1) * 100 + 12;
            }
        }
        else if (checkDay >= 15 && cal.get(Calendar.DATE) > checkDay)
        {
            // 如果仓库收货日期大于约定的对账日期且对账日期大于等于15日则划到下一个周期
            // 如果仓库收货月份正好是12月份，则下一下周期是下一年度的1月份
            // 其余的情况当划到本月对账周期内
            period++;
            if (period % 100 == 13)
            {
                period = (period / 100 + 1) * 100 + 1;
            }
        }
        gc.setSourceId(Long.parseLong(productStore.getRecordId()));
        // 冲红原因与生产本条记录的源记录保持一致
        gc.setStatus(TypeKey.BILL_STATUS_NORMAL);
        gc.preUpdate();
        goodsCheckDao.updateToHistoryBySourceId(gc);
        /* Begin 获得该对账周期相应的应收账款记录 */
        ar.setCompany(productStore.getCompany());
        ar.setCustomer(productStore.getCustomer());// 取得客户方式和老版不一样
        ar.setPeriod(period);
        List<AccountsReceivable> arList = accountsReceivableDao.findAccountsReceivableListByBean(ar);
        if (arList != null && arList.size() > 0)
        {
            // 如果该对账周期的应收账款记录已有了，则在原记录是修改
            ar = arList.get(0);
            ar.setReturnGoodsValue(MathUtils.getBigDecimal(ar.getReturnGoodsValue()
                .subtract(productStore.getAmount())));
            if (BigDecimal.ZERO.compareTo(ar.getRcvAmount()) >= 0)
            {
                ar.setCompleteFlag(TypeKey.TRUE);
            }
            else
            {
                ar.setCompleteFlag(TypeKey.FALSE);
            }
            accountsReceivableDao.update(ar);
        }

    }

    /**
     * 客诉单已经送货
     *
     * @param rejectApplication
     * @return
     */
    private boolean hasOutBound(RejectApplication rejectApplication)
    {
        Integer num = rejectApplicationDao.getRejectOutBoundQty(rejectApplication);
        return num != null && num > 0;
    }

    public Page<RejectPlanVo> rejectPlanReport(Page<RejectPlanVo> qpage, RejectPlanVo rp)
    {
        rp.setPage(qpage);
        String factoryComId = CompanyUtil.getInstance().getFactId();
        rp.setFactoryComId(factoryComId);
        List<RejectPlanVo> list = rejectApplicationDao.rejectPlanReport(rp);
        qpage.setList(list);
        return qpage;
    }

    public List<RejectPlanVo> rejectPlanReportList(RejectPlanVo rp)
    {
        String factoryComId = CompanyUtil.getInstance().getFactId();
        rp.setFactoryComId(factoryComId);
        return rejectApplicationDao.rejectPlanReport(rp);
    }
    
/*    public List<RejectApplication> getNeedSentDataList()
    {
        return rejectApplicationDao.getNeedSentDataList();
    }*/

    @Transactional(readOnly = false)
    public String cancellation(RejectApplication rejectApplication)
    {
        String result = "";
        if(null == rejectApplication || StringUtils.isBlank(rejectApplication.getRecordId()))
        {
            result = "数据错误，请刷新重试";
            return result;
        }
        // 验证是否要走审批
        result = ModifyUtils.cancellation(rejectApplication);
        /*rejectApplication.setStatus(TypeKey.ST_REJECTAPPLICATION_STATUS_CANCELED);
        rejectApplicationDao.updateStatusData(rejectApplication);
        result = "success";*/
        return result;
    }
}