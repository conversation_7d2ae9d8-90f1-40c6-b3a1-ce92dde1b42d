/**
 * 
 */
package com.kyb.pcberp.modules.contract.dao;

import java.util.List;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.contract.entity.ExpressTemplate;

/**
 * 快递模板DAO接口
 * 
 * <AUTHOR>
 * @version 2016-08-24
 */
@MyBatisDao
public interface ExpressTemplateDao extends CrudDao<ExpressTemplate>
{
    int updateDefault(ExpressTemplate tpl);
    
    int resetDefault(ExpressTemplate tpl);
    
    ExpressTemplate getDefault(ExpressTemplate tpl);
    
    ExpressTemplate getOne(ExpressTemplate tpl);
	
    /**
     * 加载所有快递模板 WC 2017-01-20
     * @param tpl
     * @return
     */
    List<ExpressTemplate> getAll(ExpressTemplate tpl);
}