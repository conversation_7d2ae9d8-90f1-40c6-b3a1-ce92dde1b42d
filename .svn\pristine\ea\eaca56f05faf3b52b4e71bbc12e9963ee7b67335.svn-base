package com.kyb.pcberp.modules.stock.web;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.kyb.pcberp.modules.contract.entity.Contract;
import com.kyb.pcberp.modules.stock.dao.MaterialUseDao;
import com.kyb.pcberp.modules.sys.entity.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.mapper.JsonMapper;
import com.kyb.pcberp.common.pdf.PdfResult;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.CommonUtils;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.MaterialUse;
import com.kyb.pcberp.modules.stock.service.MaterialUseService;
import com.kyb.pcberp.modules.stock.service.WarehouseService;
import com.kyb.pcberp.modules.sys.service.BranchService;
import com.kyb.pcberp.modules.sys.service.DepartmentService;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

/**
 * wc:2016-11-30 物料领用单controller
 * 
 * <AUTHOR>
 * 
 */
@Controller
@RequestMapping(value = "${adminPath}/stock/materialUse")
public class MaterialUseController extends BaseController
{
    @Autowired
    private MaterialUseService materialUseService;
    
    @Autowired
    private WarehouseService warehouseService;
    
    @Autowired
    private DepartmentService departmentService;
    
    @Autowired
    private BranchService branchService;

    @Autowired
    private MaterialUseDao materialUseDao;
    
    // @Autowired
    // private ParentMessageService parentMessageService;
    
    private final ObjectMapper materialUseMapper = new JsonMapper().enableSimple();
    
    private final MappingJackson2JsonView view = new MappingJackson2JsonView();
    
    @RequestMapping(value = "list")
    public String list()
    {
        return "modules/stock/materialUse";
    }
    
    @RequiresPermissions("stock:materialUse:view")
    @RequestMapping(value = {"page"})
    @ResponseBody
    public Page<MaterialUse> getlist(@RequestBody MaterialUse materialUse, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        // 设置查询范围
        if (materialUse.getQueryAll() != null && !materialUse.getQueryAll())
        {
            materialUse.setUser(user);
        }
        // 设置查询企业编号
        if (materialUse != null)
        {
            materialUse.setCompany(user.getCompany());
        }
        if (materialUse.getEndDate() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(materialUse.getEndDate());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            materialUse.setEndDate(deliTime.getTime());
        }
        if (materialUse.getBeginDate() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(materialUse.getBeginDate());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            materialUse.setBeginDate(deliTime.getTime());
        }
        
        // 分页查询数据
        Page<MaterialUse> qpage = new Page<MaterialUse>(request, response);
        if (StringUtils.isNotBlank(materialUse.getPageNo()) && StringUtils.isNotBlank(materialUse.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(materialUse.getPageNo()));
            qpage.setPageSize(Integer.parseInt(materialUse.getPageSize()));
            
            // 设置排序
            if (StringUtils.isNotBlank(materialUse.getOrderBy()))
            {
                qpage.setOrderBy(materialUse.getOrderBy());
            }
            
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        Page<MaterialUse> page = materialUseService.findPage(qpage, materialUse);
        return page;
    }
    
    // zjn 2018-08-13 物联网领用列表
    @RequiresPermissions("stock:materialUse:view")
    @RequestMapping(value = {"miPage"})
    @ResponseBody
    public Page<MaterialUse> getMiList(@RequestBody MaterialUse materialUse, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        // 设置查询范围
        if (materialUse.getQueryAll() != null && !materialUse.getQueryAll())
        {
            materialUse.setUser(user);
        }
        // 设置查询企业编号
        if (materialUse != null)
        {
            materialUse.setCompany(user.getCompany());
        }
        if (materialUse.getEndDate() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(materialUse.getEndDate());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            materialUse.setEndDate(deliTime.getTime());
        }
        if (materialUse.getBeginDate() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(materialUse.getBeginDate());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            materialUse.setBeginDate(deliTime.getTime());
        }
        
        // 分页查询数据
        Page<MaterialUse> qpage = new Page<MaterialUse>(request, response);
        if (StringUtils.isNotBlank(materialUse.getPageNo()) && StringUtils.isNotBlank(materialUse.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(materialUse.getPageNo()));
            qpage.setPageSize(Integer.parseInt(materialUse.getPageSize()));
            
            // 设置排序
            if (StringUtils.isNotBlank(materialUse.getOrderBy()))
            {
                qpage.setOrderBy(materialUse.getOrderBy());
            }
            
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        Page<MaterialUse> page = materialUseService.findMiPage(qpage, materialUse);
        return page;
    }
    
    /**
     * 加载数据
     * 
     * @param model
     * @return
     */
    @RequiresPermissions("stock:materialUse:view")
    @RequestMapping(value = "load/data")
    public View loadData(Model model)
    {
        view.setObjectMapper(materialUseMapper);
        Map<String, List<?>> map = Maps.newHashMap();
        User user = UserUtils.getUser();
        user.setCreatedBy(null);
        // 加载领用人列表
        List<User> userList = warehouseService.getUserAdministractor(user);
        // 加载原料列表
        List<Material> materialList = materialUseService.loadData();
        // 加载部门列表
        Department department = new Department();
        department.setCompany(user.getCompany());
        List<Department> departList = departmentService.findParentDepts(department);
        map.put("departList", departList);
        map.put("userList", userList);
        map.put("materialList", materialList);
        
        // 承接公司，就是子公司下拉列表
        Branch branch = new Branch();
        branch.setCompany(UserUtils.getUser().getCompany());
        map.put("branchList", branchService.findList(branch));

        //加载合同明细列表
        /*Contract contract = new Contract();
        contract.setCompany(UserUtils.getUser().getCompany());
        map.put("contractDetailList", materialUseService.getContractDetailList(contract));*/
        
        model.addAttribute("data", map);
        return view;
    }
    
    /**
     * 保存物料领用申请单
     */
    @RequiresPermissions("stock:materialUse:edit")
    @RequestMapping(value = "save", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> save(@RequestBody MaterialUse materialUse)
    {
        Map<String, Object> map = materialUseService.saveMaterialUse(materialUse);
        return map;
    }
    
    /**
     * 删除物料领用单
     */
    @RequiresPermissions("stock:materialUse:edit")
    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @ResponseBody
    public String delete(@RequestBody MaterialUse materialUse)
    {
        return materialUseService.deleteMaterialUse(materialUse);
    }
    
    /**
     * 修改物料领用单
     */
    @RequiresPermissions("stock:materialUse:edit")
    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ResponseBody
    public String update(@RequestBody MaterialUse materialUse)
    {
        return materialUseService.updateMaterialUse(materialUse);
    }
    
    /**
     * 导出物料领用单
     */
    @RequiresPermissions("stock:materialUse:view")
    @RequestMapping(value = "export")
    public String exportFile(MaterialUse materialUse, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            // 设置查询范围
            if (materialUse.getQueryAll() != null && !materialUse.getQueryAll())
            {
                materialUse.setUser(UserUtils.getUser());
            }
            if (materialUse.getOrderBy() != null)
            {
                if (materialUse.getOrderBy().indexOf(".") == -1)
                {
                    materialUse.setOrderBy("a." + materialUse.getOrderBy());
                }
            }
            else
            {
                materialUse.setOrderBy("a.no");
            }
            String beginDate = request.getParameter("beginDate");
            String endDate = request.getParameter("endDate");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(beginDate))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(beginDate)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                materialUse.setBeginDate(deliTime.getTime());
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(endDate))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(endDate)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                materialUse.setEndDate(deliTime.getTime());
            }
            materialUse.setCompany(UserUtils.getUser().getCompany());
            String fileName = "物料领用单" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            Page<MaterialUse> page = materialUseService.findPage(new Page<MaterialUse>(request,response,-1),materialUse);
            List<String> heardList = new ArrayList<String>();
            heardList.add("领用单编号");
            heardList.add("打印编号");
            heardList.add("物料编号");
            heardList.add("物料种类");
            heardList.add("投料单编号");
            heardList.add("通知单编号");
            heardList.add("厂编");
            heardList.add("日期");
            heardList.add("物料名称");
            heardList.add("物料规格");
            heardList.add("物料类型");
            heardList.add("单位");
            heardList.add("申请数量");
            heardList.add("已领用数量");
            heardList.add("单价");
            heardList.add("成本");
            heardList.add("仓库管理员");
            heardList.add("部门");
            heardList.add("申请人");
            heardList.add("时间");
            heardList.add("备注");
            heardList.add("状态");
            String[] tabulations = new String[heardList.size()];
            heardList.toArray(tabulations);
            ExportExcel excel = new ExportExcel("", tabulations);
            materialUseService.setMaterialUseExport(excel, page.getList(), tabulations);
            excel.write(response, fileName).dispose();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
        
    }
    
    /**
     * 获取领用单编号
     * 
     * @return
     */
    @RequiresPermissions("stock:materialUse:edit")
    @RequestMapping(value = "getMaterialUseNo", method = {RequestMethod.GET})
    @ResponseBody
    public String getMaterialUseNo()
    {
        String materialNo =  CommonUtils.geDocumentNo(CommonEnums.CodeType.MATERIALUSE.getIndex().toString());
        Integer noNum = materialUseDao.findMaterialUseNo(materialNo,UserUtils.getUser().getCompany().getRecordId());
        while(noNum !=null && noNum >0){
            CommonUtils.updateNextNo(CommonEnums.CodeType.MATERIALUSE.getIndex());
             materialNo = CommonUtils.geDocumentNo(CommonEnums.CodeType.MATERIALUSE.getIndex().toString());
             noNum = materialUseDao.findMaterialUseNo(materialNo,UserUtils.getUser().getCompany().getRecordId());
        }
        return materialNo;
    }
    
    /**
     * 检查领用单编号是否被引用
     * 
     * @param no
     * @return
     */
    
    @RequestMapping(value = "checkMaterialUseNo", method = {RequestMethod.POST})
    @ResponseBody
    public Integer checkMaterialUseNo(@RequestBody String no)
    {
        return materialUseService.checkMaterialUseNo(no);
    }
    
    /**
     * 确认领用单
     */
    @RequiresPermissions("stock:materialUse:edit")
    @RequestMapping(value = "updateStatusToOk", method = {RequestMethod.POST})
    @ResponseBody
    public String updateStatusToOk(@RequestBody MaterialUse materialUse)
    {
        return materialUseService.updateMaterialUseStatus(materialUse);
    }
    
    /**
     * 取消确认领用单
     */
    @RequiresPermissions("stock:materialUse:edit")
    @RequestMapping(value = "updateStatusToNotOk", method = {RequestMethod.POST})
    @ResponseBody
    public String updateStatusToNotOk(@RequestBody MaterialUse materialUse)
    {
        return materialUseService.updateMaterialUseStatusToDraft(materialUse);
    }
    
    /**
     * WC 2018-03-27 打印
     */
    @RequiresPermissions("stock:materialUse:view")
    @RequestMapping(value = "print")
    public String printPDF(String recordId, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            Pdf pdf = materialUseService.getPdf(recordId, request.getSession().getServletContext().getRealPath("/"));
            new PdfResult().doExecute(pdf.getFileName(), request, response, pdf.getDataMap());
        }
        catch (Exception e)
        {
        }
        return null;
    }

    @RequiresPermissions("stock:materialUse:view")
    @RequestMapping(value = "miExport")
    public String miExportFile(MaterialUse materialUse, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            // 设置查询范围
            if (materialUse.getQueryAll() != null && !materialUse.getQueryAll())
            {
                materialUse.setUser(UserUtils.getUser());
            }
            if (materialUse.getOrderBy() != null)
            {
                if (materialUse.getOrderBy().indexOf(".") == -1)
                {
                    materialUse.setOrderBy("a." + materialUse.getOrderBy());
                }
            }
            else
            {
                materialUse.setOrderBy("a.no");
            }
            String beginDate = request.getParameter("beginDate");
            String endDate = request.getParameter("endDate");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(beginDate))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(beginDate)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                materialUse.setBeginDate(deliTime.getTime());
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(endDate))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(endDate)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                materialUse.setEndDate(deliTime.getTime());
            }
            materialUse.setCompany(UserUtils.getUser().getCompany());
            String fileName = "物料领用单" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            Page<MaterialUse> page = materialUseService.findMiPage(new Page<MaterialUse>(request,response,-1),materialUse);
            List<String> heardList = new ArrayList<String>();
            heardList.add("领用单编号");
            heardList.add("客户公司");
            heardList.add("供应商公司");
            heardList.add("物料编号");
            heardList.add("物料名称");
            heardList.add("物料规格");
            heardList.add("物料类型");
            heardList.add("申请数量");
            heardList.add("已领用数量");
            heardList.add("单价");
            heardList.add("成本");
            heardList.add("申请人");
            heardList.add("操作人");
            heardList.add("时间");
            heardList.add("状态");
            String[] tabulations = new String[heardList.size()];
            heardList.toArray(tabulations);
            ExportExcel excel = new ExportExcel("", tabulations);
            materialUseService.setMiMaterialUseExport(excel, page.getList(), tabulations);
            excel.write(response, fileName).dispose();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;

    }
}
