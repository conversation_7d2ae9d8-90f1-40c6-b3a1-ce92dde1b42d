package com.kyb.pcberp.modules.hr.permission_center.pojo;


import com.kyb.pcberp.common.persistence.DataEntity;

@SuppressWarnings("serial")
public class Hr_Approve extends DataEntity<Hr_Approve>
{
    private String departmentName;
    
    private String approveUserIds;
    
    private String auditTypeDesc;
    
    private String approveName;
    
    private String groupId;
    
    private String auditType;
    
    private String result;
    
    private String groupName;
    
    private String companyName;
    
    private String copyfor;
    
    private String copyforStr;
    
    private String day;
    
    private String time; // 小时
    
    private String lowMoney; // 大于多少
    
    private String bigMoney; // 小于多少
    
    private String departId;
    
    private String condition;
    
    private String showUser;
    
    private String approveLevel;
    
    private String positionId;
    
    private String userIds;
    
    private String finshDay; // 完成天数

    private String judgmentMode; // 判断方式

    private String employeeLevel; // 员工等级

    private String auditTypeCh;

    private String version;

    private String status;

    private String employeeId;

    private String architectureId;

    private String oaDepartId;

    private String auditTypeId;

    private String auditTypeIdT;

    private String auditTypeT;
    
    public String getDepartmentName()
    {
        return departmentName;
    }
    
    public void setDepartmentName(String departmentName)
    {
        this.departmentName = departmentName;
    }
    
    public String getAuditTypeDesc()
    {
        return auditTypeDesc;
    }
    
    public void setAuditTypeDesc(String auditTypeDesc)
    {
        this.auditTypeDesc = auditTypeDesc;
    }
    
    public String getApproveName()
    {
        return approveName;
    }
    
    public void setApproveName(String approveName)
    {
        this.approveName = approveName;
    }
    
    public String getAuditType()
    {
        return auditType;
    }
    
    public void setAuditType(String auditType)
    {
        this.auditType = auditType;
    }
    
    public String getApproveUserIds()
    {
        return approveUserIds;
    }
    
    public void setApproveUserIds(String approveUserIds)
    {
        this.approveUserIds = approveUserIds;
    }
    
    public String getGroupId()
    {
        return groupId;
    }
    
    public void setGroupId(String groupId)
    {
        this.groupId = groupId;
    }
    
    public String getResult()
    {
        return result;
    }
    
    public void setResult(String result)
    {
        this.result = result;
    }
    
    public String getGroupName()
    {
        return groupName;
    }
    
    public void setGroupName(String groupName)
    {
        this.groupName = groupName;
    }
    
    public String getCompanyName()
    {
        return companyName;
    }
    
    public void setCompanyName(String companyName)
    {
        this.companyName = companyName;
    }
    
    public String getCopyfor()
    {
        return copyfor;
    }
    
    public void setCopyfor(String copyfor)
    {
        this.copyfor = copyfor;
    }
    
    public String getCopyforStr()
    {
        return copyforStr;
    }
    
    public void setCopyforStr(String copyforStr)
    {
        this.copyforStr = copyforStr;
    }
    
    public String getDay()
    {
        return day;
    }
    
    public void setDay(String day)
    {
        this.day = day;
    }
    
    public String getTime()
    {
        return time;
    }
    
    public void setTime(String time)
    {
        this.time = time;
    }
    
    public String getLowMoney()
    {
        return lowMoney;
    }
    
    public void setLowMoney(String lowMoney)
    {
        this.lowMoney = lowMoney;
    }
    
    public String getBigMoney()
    {
        return bigMoney;
    }
    
    public void setBigMoney(String bigMoney)
    {
        this.bigMoney = bigMoney;
    }
    
    public String getDepartId()
    {
        return departId;
    }
    
    public void setDepartId(String departId)
    {
        this.departId = departId;
    }
    
    public String getCondition()
    {
        return condition;
    }
    
    public void setCondition(String condition)
    {
        this.condition = condition;
    }
    
    public String getShowUser()
    {
        return showUser;
    }
    
    public void setShowUser(String showUser)
    {
        this.showUser = showUser;
    }
    
    public String getApproveLevel()
    {
        return approveLevel;
    }
    
    public void setApproveLevel(String approveLevel)
    {
        this.approveLevel = approveLevel;
    }
    
    public String getPositionId()
    {
        return positionId;
    }
    
    public void setPositionId(String positionId)
    {
        this.positionId = positionId;
    }
    
    public String getUserIds()
    {
        return userIds;
    }
    
    public void setUserIds(String userIds)
    {
        this.userIds = userIds;
    }

    public String getFinshDay()
    {
        return finshDay;
    }

    public void setFinshDay(String finshDay)
    {
        this.finshDay = finshDay;
    }

    public String getJudgmentMode()
    {
        return judgmentMode;
    }

    public void setJudgmentMode(String judgmentMode)
    {
        this.judgmentMode = judgmentMode;
    }

    public String getEmployeeLevel()
    {
        return employeeLevel;
    }

    public void setEmployeeLevel(String employeeLevel)
    {
        this.employeeLevel = employeeLevel;
    }

    public String getAuditTypeCh() {
        return auditTypeCh;
    }

    public void setAuditTypeCh(String auditTypeCh) {
        this.auditTypeCh = auditTypeCh;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getArchitectureId() {
        return architectureId;
    }

    public void setArchitectureId(String architectureId) {
        this.architectureId = architectureId;
    }

    public String getOaDepartId() {
        return oaDepartId;
    }

    public void setOaDepartId(String oaDepartId) {
        this.oaDepartId = oaDepartId;
    }

    public String getAuditTypeId() {
        return auditTypeId;
    }

    public void setAuditTypeId(String auditTypeId) {
        this.auditTypeId = auditTypeId;
    }

    public String getAuditTypeIdT() {
        return auditTypeIdT;
    }

    public void setAuditTypeIdT(String auditTypeIdT) {
        this.auditTypeIdT = auditTypeIdT;
    }

    public String getAuditTypeT() {
        return auditTypeT;
    }

    public void setAuditTypeT(String auditTypeT) {
        this.auditTypeT = auditTypeT;
    }
}
