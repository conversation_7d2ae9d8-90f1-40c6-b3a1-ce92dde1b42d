<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true" %>
<div>
    <div class="row pt-3">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-body p-0 d-flex flex-column">
                    <div class="flex-grow-1 card-spacer">
                        <div class="row" style="margin-top: -2rem;">
                            <div class="col-12">
                                <div class="row align-items-center pt-3">
                                    审批类型
                                    <div class="col">
                                        <select class="custom-select form-control" v-model="wechatAudit.auditType" v-on:change="getAuditList">
                                            <option value="">全部</option>
                                            <option value="22001">请假申请单</option>
                                            <option value="22002">采购申请单</option>
                                            <option value="22003">报销申请单</option>
                                            <option value="22004">离职申请单</option>
                                            <option value="22005">出差申请单</option>
                                            <option value="22007">人员增补申请单</option>
                                            <option value="22008">工作调动申请单</option>
                                            <option value="22009">工作请示单</option>
                                            <option value="22010">转正申请单</option>
                                            <option value="22011">调休申请单</option>
                                            <option value="22012">补卡申请单</option>
                                            <option value="22013">付款申请单</option>
                                            <option value="22014">借款申请单</option>
                                            <option value="22015">用印申请单</option>
                                            <option value="22016">外出申请单</option>
                                            <option value="22017">公司报表审批</option>
                                            <option value="22018">收付款计划审批</option>
                                            <option value="22019">财务报表审批</option>
                                            <option value="22020">工资核算审批</option>
                                            <option value="22021">集团内部资金调拨审批</option>
                                            <option value="22022">业务费付款申请单</option>
                                            <option value="22023">供应链授权申请单</option>
                                            <option value="22024">奖金分配申请单</option>
                                            <option value="22025">订单交期申请单</option>
                                            <option value="22026">客户汇款审批</option>
                                            <option value="22027">人员进出入申请单</option>
                                            <option value="22028">加班申请单</option>
                                            <option value="1">销售金额</option>
                                            <option value="2">采购金额</option>
                                            <option value="3">客户订单号</option>
                                            <option value="4">生产编号</option>
                                            <option value="5">成品合同费用</option>
                                            <option value="6">成品采购费用</option>
                                            <option value="7">成品合同作废</option>
                                            <option value="8">成品合同调整</option>
                                            <option value="9">成品采购调整</option>
                                            <option value="10">原料采购调整</option>
                                            <option value="11">销售合同明细金额</option>
                                            <option value="12">采购合同明细金额</option>
                                            <option value="13">原料申请</option>
                                            <option value="14">客户资料</option>
                                            <option value="15">供应商资料</option>
                                            <option value="16">原料采购合同金额</option>
                                            <option value="17">原料采购合同明细金额</option>
                                            <option value="18">收款对账单确认</option>
                                            <option value="19">付款对账单确认</option>
                                            <option value="20">加急审批</option>
                                            <option value="21">协调交接审批</option>
                                            <option value="22">合同反审</option>
                                            <option value="23">订单审批</option>
                                            <option value="24">通知单结案</option>
                                            <option value="34">发布审批</option>
                                            <option value="35">中标审批</option>
                                            <option value="36">原料采购反审</option>
                                            <option value="26">库存盘点</option>
                                            <option value="28">品质审批</option>
                                            <option value="200">批次面积确认</option>
                                            <option value="37">耗材领用审批</option>
                                            <option value="39">处理方案反审批</option>
                                            <option value="40">业务变更审批</option>
                                            <option value="41">原料入库审批</option>
                                            <option value="42">议价审批</option>
                                            <option value="43">亏损下单审批</option>
                                            <option value="44">礼品领用审批</option>
                                            <option value="45">出库超标审批</option>
                                            <option value="46">报价单审批</option>
                                            <option value="47">供应商报价审批</option>
                                            <option value="48">供应商报价修改审批</option>
                                            <option value="49">原料报价审批</option>
                                            <option value="51">材料最新价格、周期审批</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="row align-items-center pt-2">
                                    审批状态
                                    <div class="col">
                                        <select class="custom-select form-control" v-model="wechatAudit.applicationsResult" v-on:change="getAuditList">
                                            <option value="">所有</option>
                                            <option value="assent">通过</option>
                                            <option value="reject">驳回</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="row align-items-center pt-2">
                                    审批内容
                                    <div class="col">
                                        <input type="text" class="form-control h-25" placeholder="请输入审批内容" v-model="wechatAudit.condition" v-on:change="getAuditList"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="row align-items-center pt-2">
                                    开始时间
                                    <div class="col">
                                        <input id="startTime" class="form-control h-25"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="row align-items-center pt-2">
                                    结束时间
                                    <div class="col">
                                        <input id="endTime" class="form-control h-25"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row pt-3">
        <div class="col-12">
            <div class="card card-custom">
                <div class="row text-info pt-3 pb-3">
                    <div class="col text-center" v-on:click="selMordAudit(1)">
                        <span :class="waitClass">待办</span>
                    </div>
                    <div class="col text-center" v-on:click="selMordAudit(2)">
                        <span :class="alerdClass">已办</span>
                    </div>
                    <div class="col text-center" v-on:click="selMordAudit(3)">
                        <span :class="myClass">我的</span>
                    </div>
                    <div class="col text-center" v-on:click="selMordAudit(4)">
                        <span :class="oaClass">抄送</span>
                    </div>
                    <div class="col text-center" v-on:click="selMordAudit(5)">
                        <span :class="erpClass">ERP</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row pt-3 pb-3" v-if="auditList && auditList.length > 0">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-body p-0 d-flex flex-column">
                    <div class="flex-grow-1 card-spacer" style="margin-top: -2rem;">
                        <div class="pt-3" v-for="item in auditList">
                            <div class="row align-items-center pt-2 pb-2 border-bottom border-bottom-secondary">
                                <div class="col text-left">
                                    <div class="text-primary" v-on:click="inDeail(item)">
                                        {{item.auditTypeCh}}
                                        <span v-if="item.no">-{{item.no}}</span>
                                        <span v-if="item.supplierName">-{{item.supplierName}}</span>
                                    </div>
                                    <div class="col text-right" v-if="!item.applicationsType && item.status && item.status == 60003 && item.phone == user.phone">
                                        <button class="btn-sm btn btn-danger text-right" v-on:click="delAudit(item)">删除</button>
                                    </div>
                                    <div class="row">
                                        <div class="col-12" v-if="item.applicationsType == 2 && item.positionName">
                                            {{item.createDateStr}}&nbsp;&nbsp;&nbsp;[待{{item.positionName}}审批]
                                        </div>
                                        <div class="col-12" v-if="item.applicationsType == 1">
                                            <div class="row">
                                                <div class="col-8 text-primary" v-on:click="inDeail(item)">
                                                    {{item.createDateStr}}&nbsp;&nbsp;&nbsp;草稿
                                                </div>
                                                <div class="col text-right">
                                                    <button class="btn-sm btn btn-danger text-right" v-on:click="delAudit(item)">删除</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 text-primary" v-if="item.applicationsType == 3 && item.applicationsResult=='reject'" v-on:click="inDeail(item)">
                                            {{item.createDateStr}}&nbsp;&nbsp;&nbsp;已驳回
                                        </div>
                                        <div class="col-12 text-primary" v-if="item.applicationsType == 3 && item.applicationsResult=='assent'" v-on:click="inDeail(item)">
                                            {{item.createDateStr}}通过&nbsp;&nbsp;&nbsp;
                                            <span class="text-primary" v-if="item.auditStatus && item.auditStatus == '1'">待付款</span>
                                            <span class="text-success" v-if="item.auditStatus && item.auditStatus == '3'">已结束</span>
                                        </div>
                                    </div>
                                    <div>{{item.company.name}}<span v-if="item.auditType !== '22026'">-{{item.name}}&nbsp;&nbsp;&nbsp;</span>
                                        <span v-if="item.expensePrice">金额: {{item.expensePrice}}&nbsp;&nbsp;&nbsp;</span>
                                        <span v-if="item.auditStatus && item.expensePrice && item.applicationsType == 3 && item.applicationsResult=='assent' && item.auditType != '22022'">
                                            已付:{{item.sumPaidAmount ? item.sumPaidAmount : 0}}&nbsp;&nbsp;&nbsp;
                                            未付:{{item.unPaidAmount ? item.unPaidAmount : 0}}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="h-100px"></div>
    <div class="modal fade" id="delAudit" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="row pt-3">
                        <div class="col-12">
                            确定删除（{{audit.auditTypeCh}}-{{audit.no}}（{{audit.auditResult}}））这个审批单吗？
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" v-on:click="deleteAudit">删除</button>
                </div>
            </div>
        </div>
    </div>
</div>