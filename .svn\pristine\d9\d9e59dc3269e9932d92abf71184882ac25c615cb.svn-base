package com.kyb.pcberp.modules.wechat.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

public class ErpCommit extends DataEntity<AccountBind>
{
    private static final long serialVersionUID = 1L;

    private String opinion;

    private String examine;

    private String phone;

    private String updateValue;

    private String confirmRecordId;

    private String comIds;

    public String getOpinion()
    {
        return opinion;
    }

    public void setOpinion(String opinion)
    {
        this.opinion = opinion;
    }

    public String getExamine()
    {
        return examine;
    }

    public void setExamine(String examine)
    {
        this.examine = examine;
    }

    public String getPhone()
    {
        return phone;
    }

    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getUpdateValue()
    {
        return updateValue;
    }

    public void setUpdateValue(String updateValue)
    {
        this.updateValue = updateValue;
    }

    public String getConfirmRecordId()
    {
        return confirmRecordId;
    }

    public void setConfirmRecordId(String confirmRecordId)
    {
        this.confirmRecordId = confirmRecordId;
    }

    public String getComIds()
    {
        return comIds;
    }

    public void setComIds(String comIds)
    {
        this.comIds = comIds;
    }
}
