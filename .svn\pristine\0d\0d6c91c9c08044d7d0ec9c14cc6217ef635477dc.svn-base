/**
 * 
 */
package com.kyb.pcberp.modules.production.dao;

import java.util.List;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.production.entity.Feeding;
import com.kyb.pcberp.modules.production.entity.FeedingIsheOaQty;

/**
 * 生产补料DAO接口
 * 
 * <AUTHOR>
 * @version 2015-10-09
 */
@MyBatisDao
public interface FeedingIsheOaQtyDao extends CrudDao<FeedingIsheOaQty>
{
    public FeedingIsheOaQty getByFeedingOrRepAndNotification(FeedingIsheOaQty oaq);
    
    public List<FeedingIsheOaQty> findBeheData(Feeding feed);

    public void updateOaQtyById(FeedingIsheOaQty fioq);

    public void deleteByFeeding(FeedingIsheOaQty delFioaq);

    public List<FeedingIsheOaQty> findIsheOaQtyListByFeeding(FeedingIsheOaQty oaq);
    
    public List<FeedingIsheOaQty> findMergedNotis(Notification noti);
    
    public List<FeedingIsheOaQty> findOaQty(Notification noti);
}