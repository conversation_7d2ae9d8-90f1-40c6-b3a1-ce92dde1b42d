/*! kybyasuo layout 2016-11-07 */
var Layout=function(){var a="layout/img/",b="layout/css/",c=MainCtrl.getResponsiveBreakpoint("md"),d=function(){var a,b=$(".page-content"),d=$(".page-sidebar"),e=$("body");if(e.hasClass("page-footer-fixed")===!0&&e.hasClass("page-sidebar-fixed")===!1){var f=MainCtrl.getViewPort().height-$(".page-footer").outerHeight()-$(".page-header").outerHeight();b.height()<f&&b.attr("style","min-height:"+f+"px")}else{if(e.hasClass("page-sidebar-fixed"))a=g(),e.hasClass("page-footer-fixed")===!1&&(a-=$(".page-footer").outerHeight());else{var h=$(".page-header").outerHeight(),i=$(".page-footer").outerHeight();a=MainCtrl.getViewPort().width<c?MainCtrl.getViewPort().height-h-i:d.height()+20,a+h+i<=MainCtrl.getViewPort().height&&(a=MainCtrl.getViewPort().height-h-i)}b.attr("style","min-height:"+a+"px")}},e=function(a,b,d){var e="";if(void 0!=b&&void 0!=b.name&&""!=b.name)if(-1==b.name.indexOf("."))e="#/"+b.name;else{var f=b.name.split(".");2==f.length&&(e="#/"+f[0]+"/"+f[1])}var g=""==e?location.hash.toLowerCase():e;b&&b.name&&("order.notification"==b.name||"purch.rawmaterial"==b.name)&&(g+="/");var h=$(".page-sidebar-menu");if("click"===a||"set"===a?d=$(d):"match"===a&&h.find("li > a").each(function(){var a=$(this).attr("href").toLowerCase();return a.length>1&&g.substr(1,a.length-1)==a.substr(1)?void(d=$(this)):void 0}),d&&0!=d.size()&&"javascript:;"!==d.attr("href").toLowerCase()&&"#"!==d.attr("href").toLowerCase()){{parseInt(h.data("slide-speed")),h.data("keep-expanded")}h.find("li.active").removeClass("active"),h.find("li > a > .selected").remove(),h.hasClass("page-sidebar-menu-hover-submenu")===!1?h.find("li.open").each(function(){0===$(this).children(".sub-menu").size()&&($(this).removeClass("open"),$(this).find("> a > .arrow.open").removeClass("open"))}):h.find("li.open").removeClass("open"),d.parents("li").each(function(){$(this).addClass("active"),$(this).find("> a > span.arrow").addClass("open"),1===$(this).parent("ul.page-sidebar-menu").size()&&$(this).find("> a").append('<span class="selected"></span>'),1===$(this).children("ul.sub-menu").size()&&$(this).addClass("open")}),"click"===a&&MainCtrl.getViewPort().width<c&&$(".page-sidebar").hasClass("in")&&$(".page-header .responsive-toggler").click()}},f=function(){jQuery(".page-sidebar").on("click","li > a",function(a){var b=$(this).next().hasClass("sub-menu");if(!(MainCtrl.getViewPort().width>=c&&1===$(this).parents(".page-sidebar-menu-hover-submenu").size())){if(b===!1)return void(MainCtrl.getViewPort().width<c&&$(".page-sidebar").hasClass("in")&&$(".page-header .responsive-toggler").click());if(!$(this).next().hasClass("sub-menu always-open")){var e=$(this).parent().parent(),f=$(this),g=$(".page-sidebar-menu"),h=jQuery(this).next(),i=g.data("auto-scroll"),j=parseInt(g.data("slide-speed")),k=g.data("keep-expanded");k!==!0&&(e.children("li.open").children("a").children(".arrow").removeClass("open"),e.children("li.open").children(".sub-menu:not(.always-open)").slideUp(j),e.children("li.open").removeClass("open"));var l=-200;h.is(":visible")?(jQuery(".arrow",jQuery(this)).removeClass("open"),jQuery(this).parent().removeClass("open"),h.slideUp(j,function(){i===!0&&$("body").hasClass("page-sidebar-closed")===!1&&($("body").hasClass("page-sidebar-fixed")?g.slimScroll({scrollTo:f.position().top}):MainCtrl.scrollTo(f,l)),d()})):b&&(jQuery(".arrow",jQuery(this)).addClass("open"),jQuery(this).parent().addClass("open"),h.slideDown(j,function(){i===!0&&$("body").hasClass("page-sidebar-closed")===!1&&($("body").hasClass("page-sidebar-fixed")?g.slimScroll({scrollTo:f.position().top}):MainCtrl.scrollTo(f,l)),d()})),a.preventDefault()}}}),jQuery(".page-sidebar").on("click"," li > a.ajaxify",function(a){a.preventDefault(),MainCtrl.scrollTop();var b=$(this).attr("href"),d=jQuery(".page-sidebar ul"),e=($(".page-content"),$(".page-content .page-content-body"));d.children("li.active").removeClass("active"),d.children("arrow.open").removeClass("open"),$(this).parents("li").each(function(){$(this).addClass("active"),$(this).children("a > span.arrow").addClass("open")}),$(this).parents("li").addClass("active"),MainCtrl.getViewPort().width<c&&$(".page-sidebar").hasClass("in")&&$(".page-header .responsive-toggler").click(),MainCtrl.startPageLoading();var f=$(this);$.ajax({type:"GET",cache:!1,url:b,dataType:"html",success:function(a){0===f.parents("li.open").size()&&$(".page-sidebar-menu > li.open > a").click(),MainCtrl.stopPageLoading(),e.html(a),Layout.fixContentHeight(),MainCtrl.initAjax()},error:function(a,b,c){MainCtrl.stopPageLoading(),e.html("<h4>Could not load the requested content.</h4>")}})}),jQuery(".page-content").on("click",".ajaxify",function(a){a.preventDefault(),MainCtrl.scrollTop();var b=$(this).attr("href"),d=($(".page-content"),$(".page-content .page-content-body"));MainCtrl.startPageLoading(),MainCtrl.getViewPort().width<c&&$(".page-sidebar").hasClass("in")&&$(".page-header .responsive-toggler").click(),$.ajax({type:"GET",cache:!1,url:b,dataType:"html",success:function(a){MainCtrl.stopPageLoading(),d.html(a),Layout.fixContentHeight(),MainCtrl.initAjax()},error:function(a,b,c){d.html("<h4>Could not load the requested content.</h4>"),MainCtrl.stopPageLoading()}})}),i(),$(".page-sidebar").on("click",".sidebar-search .remove",function(a){a.preventDefault(),$(".sidebar-search").removeClass("open")}),$(".page-sidebar .sidebar-search").on("keypress","input.form-control",function(a){return 13==a.which?($(".sidebar-search").submit(),!1):void 0}),$(".sidebar-search .submit").on("click",function(a){a.preventDefault(),$("body").hasClass("page-sidebar-closed")&&$(".sidebar-search").hasClass("open")===!1?(1===$(".page-sidebar-fixed").size()&&$(".page-sidebar .sidebar-toggler").click(),$(".sidebar-search").addClass("open")):$(".sidebar-search").submit()}),0!==$(".sidebar-search").size()&&($(".sidebar-search .input-group").on("click",function(a){a.stopPropagation()}),$("body").on("click",function(){$(".sidebar-search").hasClass("open")&&$(".sidebar-search").removeClass("open")}))},g=function(){var a=MainCtrl.getViewPort().height-$(".page-header").outerHeight();return $("body").hasClass("page-footer-fixed")&&(a-=$(".page-footer").outerHeight()),a},h=function(){var a=$(".page-sidebar-menu");return MainCtrl.destroySlimScroll(a),0===$(".page-sidebar-fixed").size()?void d():void(MainCtrl.getViewPort().width>=c&&(a.attr("data-height",g()),MainCtrl.initSlimScroll(a),d()))},i=function(){var a=$("body");a.hasClass("page-sidebar-fixed")&&$(".page-sidebar").on("mouseenter",function(){a.hasClass("page-sidebar-closed")&&$(this).find(".page-sidebar-menu").removeClass("page-sidebar-menu-closed")}).on("mouseleave",function(){a.hasClass("page-sidebar-closed")&&$(this).find(".page-sidebar-menu").addClass("page-sidebar-menu-closed")})},j=function(){var a=$("body");$.cookie&&"1"===$.cookie("sidebar_closed")&&MainCtrl.getViewPort().width>=c&&($("body").addClass("page-sidebar-closed"),$(".page-sidebar-menu").addClass("page-sidebar-menu-closed")),$("body").on("click",".sidebar-toggler",function(b){var c=$(".page-sidebar"),d=$(".page-sidebar-menu");$(".sidebar-search",c).removeClass("open"),a.hasClass("page-sidebar-closed")?(a.removeClass("page-sidebar-closed"),d.removeClass("page-sidebar-menu-closed"),$.cookie&&$.cookie("sidebar_closed","0")):(a.addClass("page-sidebar-closed"),d.addClass("page-sidebar-menu-closed"),a.hasClass("page-sidebar-fixed")&&d.trigger("mouseleave"),$.cookie&&$.cookie("sidebar_closed","1")),$(window).trigger("resize")})},k=function(){$(".page-header").on("click",'.hor-menu a[data-toggle="tab"]',function(a){a.preventDefault();var b=$(".hor-menu .nav"),c=b.find("li.current");$("li.active",c).removeClass("active"),$(".selected",c).remove();var d=$(this).parents("li").last();d.addClass("current"),d.find("a:first").append('<span class="selected"></span>')}),$(".page-header").on("click",".search-form",function(a){$(this).addClass("open"),$(this).find(".form-control").focus(),$(".page-header .search-form .form-control").on("blur",function(a){$(this).closest(".search-form").removeClass("open"),$(this).unbind("blur")})}),$(".page-header").on("keypress",".hor-menu .search-form .form-control",function(a){return 13==a.which?($(this).closest(".search-form").submit(),!1):void 0}),$(".page-header").on("mousedown",".search-form.open .submit",function(a){a.preventDefault(),a.stopPropagation(),$(this).closest(".search-form").submit()}),$('[data-hover="megamenu-dropdown"]').not(".hover-initialized").each(function(){$(this).dropdownHover(),$(this).addClass("hover-initialized")}),$(document).on("click",".mega-menu-dropdown .dropdown-menu",function(a){a.stopPropagation()})},l=function(){$("body").on("shown.bs.tab",'a[data-toggle="tab"]',function(){d()})},m=function(){var a=300,b=500;navigator.userAgent.match(/iPhone|iPad|iPod/i)?$(window).bind("touchend touchcancel touchleave",function(c){$(this).scrollTop()>a?$(".scroll-to-top").fadeIn(b):$(".scroll-to-top").fadeOut(b)}):$(window).scroll(function(){$(this).scrollTop()>a?$(".scroll-to-top").fadeIn(b):$(".scroll-to-top").fadeOut(b)}),$(".scroll-to-top").click(function(a){return a.preventDefault(),$("html, body").animate({scrollTop:0},b),!1})},n=function(){var a,b=$(".full-height-content");if(a=MainCtrl.getViewPort().height-$(".page-header").outerHeight(!0)-$(".page-footer").outerHeight(!0)-$(".page-title").outerHeight(!0)-$(".page-bar").outerHeight(!0),b.hasClass("portlet")){var d=b.find(".portlet-body");if(MainCtrl.getViewPort().width<c)return void MainCtrl.destroySlimScroll(d.find(".full-height-content-body"));a=a-b.find(".portlet-title").outerHeight(!0)-parseInt(b.find(".portlet-body").css("padding-top"))-parseInt(b.find(".portlet-body").css("padding-bottom"))-2,b.hasClass("full-height-content-scrollable")?(a-=35,d.find(".full-height-content-body").css("height",a),MainCtrl.initSlimScroll(d.find(".full-height-content-body"))):d.css("min-height",a)}else{if(MainCtrl.getViewPort().width<c)return void MainCtrl.destroySlimScroll(b.find(".full-height-content-body"));b.hasClass("full-height-content-scrollable")?(a-=35,b.find(".full-height-content-body").css("height",a),MainCtrl.initSlimScroll(b.find(".full-height-content-body"))):b.css("min-height",a)}};return{initHeader:function(){k()},setSidebarMenuActiveLink:function(a,b){e(a,b)},initSidebar:function(){h(),f(),j(),MainCtrl.isAngularJsApp()&&e("match"),MainCtrl.addResizeHandler(h)},initContent:function(){n(),l(),MainCtrl.addResizeHandler(d),MainCtrl.addResizeHandler(n)},initFooter:function(){m()},init:function(){this.initHeader(),this.initSidebar(),this.initContent(),this.initFooter()},fixContentHeight:function(){d()},initFixedSidebarHoverEffect:function(){i()},initFixedSidebar:function(){h()},getLayoutImgPath:function(){return MainCtrl.getAssetsPath()+a},getLayoutCssPath:function(){return MainCtrl.getAssetsPath()+b}}}();