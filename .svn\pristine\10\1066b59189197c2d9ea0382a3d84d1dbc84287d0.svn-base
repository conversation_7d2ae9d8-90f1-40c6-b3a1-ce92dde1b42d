<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.hr.permission_center.dao.Hr_ApproveDao">
    <select id="getAuditConfigList" resultType="Hr_Approve">
        SELECT
        m.auditType,
        m.auditTypeDesc,
        m.approveName,
        m.copyforStr,
        m.groupId,
        m.finshDay,
        m.auditTypeId AS "auditTypeId"
        FROM
        (
        SELECT
        a.auditType,
        c.name AS auditTypeDesc,
        GROUP_CONCAT(
        CONCAT(
        b.`name`
        <if test="showUser != null and showUser != ''">
            ,'(',
            IFNULL(
            (
            SELECT
            GROUP_CONCAT(DISTINCT q.`name`)
            FROM
            oa_position_employee p
            LEFT JOIN oa_md_employee q ON q.recordId = p.employeeId
            WHERE
            p.activeFlag = 1
            AND q.activeFlag = 1
            AND q.workStatus = 1
            AND p.positionId = a.positionId
            <if test="departId != null and departId != ''">
                AND p.groupId IN (
                SELECT
                recordId
                FROM
                oa_department_relation
                WHERE
                recordId = #{departId}
                UNION
                SELECT
                allId
                FROM
                oa_department_relation
                WHERE
                recordId = #{departId}
                UNION
                SELECT
                j.allId
                FROM
                oa_department_relation i
                LEFT JOIN oa_department_relation j ON j.recordId = i.allId
                WHERE
                i.recordId = #{departId}
                UNION
                SELECT
                k.allId
                FROM
                oa_department_relation i
                LEFT JOIN oa_department_relation j ON j.recordId = i.allId
                LEFT JOIN oa_department_relation k ON k.recordId = j.allId
                WHERE
                i.recordId = #{departId}
                )
            </if>
            ),
            ''
            ),
            ')'
        </if>
        ,''
        )
        ) AS approveName,
        (
        SELECT
        group_concat(`name`)
        FROM
        oa_md_employee
        WHERE
        FIND_IN_SET(recordId, a.copyfor)
        AND activeFlag = 1
        AND workStatus = 1
        ) AS copyforStr,
        a.groupId,
        a.finshDay,
        c.recordId AS "auditTypeId"
        FROM
        oa_approve_config a
        LEFT JOIN oa_position b ON b.recordId = a.positionId
        LEFT JOIN oa_audit_type_sheet c ON c.no = a.auditType
        WHERE
        (a.groupId = #{groupId} OR a.groupId IS NULL)
        AND a.activeFlag = 1
        AND b.activeFlag = 1
        GROUP BY
        a.auditType
        ) m
        WHERE 1=1
        <if test="auditType != null and auditType != ''">
            AND m.auditType = #{auditType}
        </if>
        <if test="condition != null and condition != ''">
            AND m.approveName LIKE CONCAT('%', #{condition}, '%')
        </if>
    </select>

    <update id="delAuditConfigList">
		UPDATE oa_approve_config SET
			activeFlag = 0
		WHERE auditType = #{auditType}
	</update>

    <select id="getAuditConfigDeail" resultType="Hr_Approve">
        SELECT
        c.name AS auditTypeDesc,
        IFNULL(
        (
        SELECT
        GROUP_CONCAT(DISTINCT q.`name`)
        FROM
        oa_position_employee p
        LEFT JOIN oa_md_employee q ON q.recordId = p.employeeId
        WHERE
        p.activeFlag = 1
        AND q.activeFlag = 1
        AND q.workStatus = 1
        AND p.positionId = a.positionId
        <if test="departId != null and departId != ''">
            AND p.groupId IN (
            SELECT
            recordId
            FROM
            oa_department_relation
            WHERE
            recordId = #{departId}
            UNION
            SELECT
            allId
            FROM
            oa_department_relation
            WHERE
            recordId = #{departId}
            UNION
            SELECT
            j.allId
            FROM
            oa_department_relation i
            LEFT JOIN oa_department_relation j ON j.recordId = i.allId
            WHERE
            i.recordId = #{departId}
            UNION
            SELECT
            k.allId
            FROM
            oa_department_relation i
            LEFT JOIN oa_department_relation j ON j.recordId = i.allId
            LEFT JOIN oa_department_relation k ON k.recordId = j.allId
            WHERE
            i.recordId = #{departId}
            )
        </if>
        ),
        ''
        ) AS "approveUserIds",
        (
        SELECT
        group_concat(`name`)
        FROM
        oa_md_employee
        WHERE
        FIND_IN_SET(recordId, a.copyfor)
        AND activeFlag = 1
        AND workStatus = 1
        ) AS copyforStr,
        a.recordId,
        a.groupId,
        a.auditType,
        a.approveLevel,
        a.positionId,
        a.copyfor,
        a.`day`,
        a.time,
        a.lowMoney,
        a.bigMoney,
        a.judgmentMode,
        a.employeeLevel,
        b.name AS "departmentName",
        a.companyId AS "companyId"
        FROM
        oa_approve_config a
        LEFT JOIN oa_position b ON b.recordId = a.positionId
        LEFT JOIN oa_audit_type_sheet c ON c.recordId = a.auditType
        WHERE
        a.activeFlag = 1
        AND a.auditType = #{auditType}
        AND (a.groupId = #{groupId} OR a.groupId IS NULL)
        ORDER BY CAST(a.approveLevel AS UNSIGNED)
    </select>

    <select id="saveAuditConfig" statementType="CALLABLE" useCache="false"  parameterType="Hr_Approve"
            resultType="String">
	       <![CDATA[
	        {call batchPutApprove_Proc(#{userIds,mode=IN},
						       #{auditType,mode=IN},
						       #{createdBy.recordId,mode=IN},
						       #{groupId,mode=IN},
						       #{copyfor,mode=IN},
						       #{finshDay,mode=IN},
						       #{result,mode=OUT,jdbcType=VARCHAR},
                               #{companyId,mode=IN}
	            )}
	       ]]>
	</select>

    <select id="getConfigDeail" resultType="Hr_Approve">
        SELECT
        a.`name` AS "departmentName",
        (
        SELECT
        GROUP_CONCAT(DISTINCT q.`name`)
        FROM
        oa_position_employee p
        LEFT JOIN oa_md_employee q ON q.recordId = p.employeeId
        WHERE
        p.activeFlag = 1
        AND q.activeFlag = 1
        AND q.workStatus = 1
        AND p.positionId = a.recordId
        <if test="departId != null and departId != ''">
            AND p.groupId IN (
            SELECT
            recordId
            FROM
            oa_department_relation
            WHERE
            recordId = #{departId}
            UNION
            SELECT
            allId
            FROM
            oa_department_relation
            WHERE
            recordId = #{departId}
            UNION
            SELECT
            j.allId
            FROM
            oa_department_relation i
            LEFT JOIN oa_department_relation j ON j.recordId = i.allId
            WHERE
            i.recordId = #{departId}
            UNION
            SELECT
            k.allId
            FROM
            oa_department_relation i
            LEFT JOIN oa_department_relation j ON j.recordId = i.allId
            LEFT JOIN oa_department_relation k ON k.recordId = j.allId
            WHERE
            i.recordId = #{departId}
            )
        </if>
        ) AS "approveUserIds"
        FROM
        oa_position a
        WHERE
        a.recordId = #{positionId}
    </select>

    <select id="getOaGroup" resultType="Hr_DepartMent">
	    SELECT
			a.recordId,
			a.allId,
			a.departmentName,
			a.managerUserId,
			a.departmentLevel,
			a.companyAddress,
			a.activeFlag,
			a.phone,
			a.createdBy AS "createdBy.recordId",
			a.createdDate,
			a.lastUpdBy AS "lastUpdBy.recordId",
			a.lastUpdDate,
			a.`no`,
			a.bindErpId,
			a.shortName,
			a.expdate,
			(
				SELECT
					COUNT(recordId)
				FROM
					oa_md_employee
				WHERE
					FIND_IN_SET(a.recordId, groupId) AND activeFlag=1 AND workStatus=1
			) AS sumMan,
			TIMESTAMPDIFF(DAY,NOW(),DATE_FORMAT(a.expdate, '%Y-%m-%d %H:%i:%S')) AS day,
			a.groupId
		FROM
			oa_department_relation a
		WHERE
			a.activeFlag = 1
		AND a.groupId = #{recordId}
	</select>

    <select id="getOaEmpList" resultType="Hr_Employee">
        SELECT
        a.recordId,
        a.`name`,
        a.phone,
        a.position,
        a.userId,
        b.fileUrl,
        b.defaultImg,
        b.recordId AS "user.recordId",
        b.userName AS "user.userName",
        (
        SELECT
        GROUP_CONCAT(departmentName)
        FROM
        oa_department_relation
        WHERE
        FIND_IN_SET(recordId, a.groupId)
        ) AS "path",
        a.departId,
        a.manage,
        a.workStatus
        FROM
        oa_md_employee a
        LEFT JOIN icloud_sm_user b ON b.recordId = a.userId
        WHERE
        a.activeFlag = 1
        AND FIND_IN_SET(#{recordId}, a.groupId)
        AND a.salarySystemId IS NULL
        <choose>
            <when test="workStatus == 1">
                AND a.workStatus = 1
            </when>
            <otherwise>
                AND a.workStatus <![CDATA[<>]]> 1
            </otherwise>
        </choose>
        <if test="condition != null and condition != ''">
            AND CONCAT(
            a.`name`,
            a.position,
            (
            SELECT
            GROUP_CONCAT(departmentName)
            FROM
            oa_department_relation
            WHERE
            FIND_IN_SET(recordId, a.groupId)
            )
            ) LIKE CONCAT('%', #{condition}, '%')
        </if>
    </select>

    <select id="getAttachmments" resultType="Hr_KybAttachments">
		SELECT recordId,orgFileName AS name,fileUrl,type FROM oa_sysupdate_attachment WHERE activeFlag=1 LIMIT 1
	</select>

    <select id="getPositionList" resultType="Position">
		SELECT
			recordId,
			`name`
		FROM
			oa_position
		WHERE
			activeFlag = 1
		AND (groupId = #{departId} OR groupId IS NULL)
	</select>

    <select id="getPoEmpList" resultType="PositionEmployee">
		SELECT
			a.recordId,
			b.`name` AS "name",
			CONCAT(
				(
					CASE
					WHEN f.departmentName IS NOT NULL THEN
						CONCAT(f.departmentName, "-")
					WHEN f.departmentName IS NULL THEN
						''
					END
				),
				(
					CASE
					WHEN e.departmentName IS NOT NULL THEN
						CONCAT(e.departmentName, "-")
					WHEN e.departmentName IS NULL THEN
						''
					END
				),
				(
					CASE
					WHEN d.departmentName IS NOT NULL THEN
						CONCAT(d.departmentName, "-")
					WHEN d.departmentName IS NULL THEN
						''
					END
				),
				IFNULL(c.departmentName, '')
			) AS "departName",
			(
				SELECT
					GROUP_CONCAT(j.`name`)
				FROM
					oa_position_employee i
				LEFT JOIN oa_md_employee j ON j.recordId = i.employeeId
				WHERE
					i.positionId = a.positionId
				AND i.groupId = a.groupId
				AND i.activeFlag = 1
				AND j.activeFlag = 1
			) AS "names",
			a.positionId,
			a.groupId
		FROM
			oa_position_employee a
		LEFT JOIN oa_position b ON b.recordId = a.positionId
		LEFT JOIN oa_department_relation c ON c.recordId = a.groupId
		LEFT JOIN oa_department_relation d ON d.recordId = c.allId
		LEFT JOIN oa_department_relation e ON e.recordId = d.allId
		LEFT JOIN oa_department_relation f ON f.recordId = e.allId
		WHERE a.employeeId = #{recordId} AND a.activeFlag = 1
	</select>

    <select id="getAuditTypeName" resultType="Oa_AuditTypeSheet">
        SELECT * FROM oa_audit_type_sheet WHERE activeFlag = 1
        ORDER BY createdDate
    </select>

    <select id="getTaskHandOver" resultType="Hr_Employee">
		SELECT
			b.recordId,
			b.name
		FROM
			oa_handover a
		LEFT JOIN oa_md_employee b ON b.recordId=a.employeeId
		WHERE
			a.taskId = #{employeeId}
		AND a.activeFlag = 1
	</select>

    <select id="getDayThingHandOver" resultType="Hr_Employee">
		SELECT
			b.recordId,
			b.name
		FROM
			oa_handover a
		LEFT JOIN oa_md_employee b ON b.recordId=a.employeeId
		WHERE
			a.dayThingId = #{employeeId}
		AND a.activeFlag = 1
	</select>

    <select id="getReportHandOver" resultType="Hr_Employee">
		SELECT
			b.recordId,
			b.name
		FROM
			oa_handover a
		LEFT JOIN oa_md_employee b ON b.recordId=a.employeeId
		WHERE
			a.reportId = #{employeeId}
		AND a.activeFlag = 1
	</select>

    <select id="getAuditHandOver" resultType="Employee">
        SELECT
            b.recordId,
            b.name
        FROM
            oa_handover a
        LEFT JOIN oa_md_employee b ON b.recordId=a.employeeId
        WHERE
            a.auditId = #{employeeId}
          AND a.activeFlag = 1
    </select>

    <select id="getAuditApproveCount" resultType="Integer">
        SELECT
            COUNT(1)
        FROM
            oa_audit
        WHERE
            activeFlag = 1
          AND auditTypeId = #{auditTypeId} AND auditType = #{auditType}
          AND (applicationsType <![CDATA[<>]]> 3 OR applicationsResult <![CDATA[<>]]> "assent")
    </select>

    <select id="getOldApproveIds" resultType="String">
        SELECT
            GROUP_CONCAT(positionId) AS "positionId"
        FROM
            oa_approve_config
        WHERE
            activeFlag = 1
          AND auditType = #{auditTypeT}
          AND groupId = #{groupId}
    </select>
</mapper>