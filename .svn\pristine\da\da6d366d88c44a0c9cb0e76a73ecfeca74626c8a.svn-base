package com.kyb.pcberp.modules.stock.service;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.common.utils.FmtUtils;
import com.kyb.pcberp.common.utils.MathUtils;
import com.kyb.pcberp.modules.stock.dao.AdjustStocksDao;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.entity.AdjustStocks;
import com.kyb.pcberp.modules.stock.entity.Material;

@Service
@Transactional(readOnly = true)
public class AdjustService extends CrudService<AdjustStocksDao, AdjustStocks>
{
    @Autowired
    private MaterialDao materialDao;
    
    @Autowired
    private AdjustStocksDao adjustStockDao;
    
    public AdjustStocks get(String id)
    {
        return super.get(id);
    }
    
    public List<AdjustStocks> findList(AdjustStocks material)
    {
        return super.findList(material);
    }
    
    public Page<AdjustStocks> findPage(Page<AdjustStocks> page, AdjustStocks material)
    {
        return super.findPage(page, material);
    }
    
    @Transactional(readOnly = false)
    public void save(AdjustStocks material)
    {
        super.save(material);
    }
    
    @Transactional(readOnly = false)
    public String updateAdjustStocks(AdjustStocks adjustStocks)
    {
        AdjustStocks oldAdjustStocks = get(adjustStocks.getRecordId());
        if (oldAdjustStocks.getStatus() != null
            && oldAdjustStocks.getStatus().equals(TypeKey.ST_ADJUSTSTOCKS_STATUS_APPROVED))
        {
            return "此库存调整记录已经审核批准过了，不用再次审核批准!";
        }
        
        // 如果将库存调整记录改为审核批准状态，则同时修改物料的实际库存
        if (adjustStocks.getStatus() != null
            && adjustStocks.getStatus().equals(TypeKey.ST_ADJUSTSTOCKS_STATUS_APPROVED.toString()))
        {
            String quantity =
                adjustStocks.getQuantity() == null ? oldAdjustStocks.getQuantity() : adjustStocks.getQuantity();
                
            int fIndex = quantity.indexOf("-");
            Integer oper = ConstKey.ADJUST_ADD;
            if (fIndex != -1)
            {
                quantity = quantity.substring(fIndex + 1);
                oper = ConstKey.ADJUST_MIN;
            }
            
            BigDecimal zero = new BigDecimal(0);
            BigDecimal adjustQty = new BigDecimal(quantity);
            // 查询Material为了判断物料是成品还是原料，原料的话不需要调整可用库存
            Material material = new Material(oldAdjustStocks.getMaterial().getRecordId());
            material = materialDao.getMaterialById(material);
            if (adjustQty != null && adjustQty.compareTo(BigDecimal.ZERO) != 0)
            {
                // 现库存
                BigDecimal stocks = material.getStocks() == null ? zero : material.getStocks();
                // 可用库存
                BigDecimal aQty = material.getAvailableQty() == null ? BigDecimal.ZERO : material.getAvailableQty();
                // 寄存库存
                Integer depositQty = material.getDepositQty() == null ? 0 : material.getDepositQty();
                BigDecimal maxStocks = new BigDecimal(ConstKey.MAX_STOCKS);
                if (oper == ConstKey.ADJUST_ADD)
                {
                    stocks = stocks.compareTo(maxStocks) == 1 ? maxStocks : stocks.add(adjustQty);
                    if (adjustStocks.getMaterialStyle() != null
                        && adjustStocks.getMaterialStyle().equals(ConstKey.ADJUST_MATERIAL_DEPOSIT_NO))
                    {
                        aQty = aQty.add(adjustQty);
                    }
                    else
                    {
                        depositQty += adjustQty.intValue();
                    }
                }
                else
                {
                    if (adjustStocks.getMaterial().getMaterialKind().equals(TypeKey.MD_MATERIAL_TYPE_PRODUCT))
                    { // 成品
                        if (MathUtils.sub(new Double(aQty.intValue()), adjustQty.doubleValue(), 3) < 0
                            && adjustStocks.getMaterialStyle().equals(ConstKey.ADJUST_MATERIAL_DEPOSIT_NO))
                        {
                            return "库存已被合同占用，请驳回重新调整";
                        }
                        if (MathUtils.sub(new Double(depositQty), adjustQty.doubleValue(), 3) < 0
                            && adjustStocks.getMaterialStyle().equals(ConstKey.ADJUST_MATERIAL_DEPOSIT_YES))
                        {
                            return "寄存物料已被占用，请驳回重新调整";
                        }
                    }
                    else // 原料
                    {
                        if (stocks.compareTo(adjustQty) == -1)
                        {
                            return "库存数量不足，请驳回重新调整";
                        }
                    }
                    
                    stocks = stocks.subtract(adjustQty);
                    if (stocks.compareTo(zero) == -1)
                    {
                        stocks = zero;
                    }
                    
                    if (adjustStocks.getMaterialStyle() != null
                        && adjustStocks.getMaterialStyle().equals(ConstKey.ADJUST_MATERIAL_DEPOSIT_NO))
                    {
                        aQty = aQty.subtract(adjustQty) ;
                        if (aQty.compareTo(BigDecimal.ZERO) == -1)
                        {
                            aQty = BigDecimal.ZERO;
                        }
                    }
                    else
                    {
                        depositQty -= adjustQty.intValue();
                        if (depositQty.compareTo(0) == -1)
                        {
                            depositQty = 0;
                        }
                    }
                    
                }
                
                material.setStocks(new BigDecimal(FmtUtils.holdDecimal(stocks,3)));
                material.setAvailableQty(aQty);
                material.setDepositQty(depositQty);
                // 更新物料
                materialDao.updateStocksAndAvailableQty(material);
            }
        }
        
        save(adjustStocks);
        
        return null;
    }
    
    @Transactional(readOnly = false)
    public void delete(AdjustStocks material)
    {
        super.delete(material);
    }
    
    /**
     * 导出
     * 
     * @param material
     * @return
     */
    public List<AdjustStocks> findExpList(AdjustStocks material)
    {
        return adjustStockDao.findExpList(material);
    }
}
