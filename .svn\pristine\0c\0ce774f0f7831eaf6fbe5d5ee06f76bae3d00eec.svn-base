/**
 *
 */
package com.kyb.pcberp.modules.approval.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import com.kyb.pcberp.modules.sys.entity.User;

import java.math.BigDecimal;
import java.util.Date;

/** 客户、供应商、原料采购审批备份实体 */
public class BackupTwo extends DataEntity<BackupTwo>
{
    private static final long serialVersionUID = 1L;

    private String no; // 客户编号

    private String name; // 客户名称

    private String shortName; // 客户简称

    private String address; // 客户地址

    private String zip; // 客户邮编

    private String areaCode; // 客户所属地区的行政区域编码

    private String companyType; // 客户公司类型

    private String industry; // 客户所属行业

    private Long staffQty; // 客户员工规模,引用md_dict表中的recordId字段

    private Long yearSaleroom; // 客户公司的年销售额规模,引用md_dict表中的recordId字段

    private String phone; // 电话

    private String fax; // 传真

    private String website; // 客户公司网址

    private String bank; // 客户开户银行

    private String account; // 客户银行账号

    private Long payWay; // 约定付款方式,引用md_dict表中的recordId字段

    private String checkDate; // 约定对账日期

    private String bizPerson; // 联系人

    private String bizPersonName; // 联系人名称

    private String legalPerson; // 客户法人代表

    private String organCode; // 公司的法人机构代码证编号

    private String licence; // 工商营业执照注册号

    private String taxRegNo; // 税务登记号

    private String casingInfo; // 客户要求在包装箱上印刷的信息(文字)

    private String deliveryNeedPriceFlag; // 送货单是否需要加金额

    private String type; // 客户类型

    private Integer status; // 100301,为初步合作，100302为正常状态，100303为中断合作

    private User salesman; // 本公司业务员

    private Long freightWay; // 交货方式

    private Long taxDescript; // 价格税率说明

    private Long currencyType; // 货币类型

    private Long deliveryWay; // 提货方式

    private String qq; // 客户QQ

    private String wechat; // 客户微信

    private double scrappage; // 客户的预投率

    private Date initializeDate;// 客户分配业务员时间

    private Date sentTimeStartQr; // 时间段查询条件 载体

    private Date sentTimeEndQr;

    private String selectdate; // 查询日期

    private Integer count;

    private BigDecimal totalAmt;

    // WC 2017-05-27 ADD
    private Long signplace; // 签订地点

    private Long deliverycity; // 交货地点

    private Long frightbear; // 运费承担

    private Long billformula; // 票据形式

    private Long hasquestion; // 如有问题

    private Long paycause; // 付款方式

    private Long inspectionstandard; // 检验标准

    private Long packingrequirement; // 包装方式

    private Company quiltCombinationCompany;// zjn 2018-02-28 被合并公司id

    private Double diffiCoff; // 难度系数

    private Long level; // 等级

    private String assistantName;// zjn 业务员助理名称

    private String customerId; // 客户id

    private DictValue payDays; // 质保期限

    private String qualityStd; // 质量标准

    private String taxpayerFlag; // 是否是一般纳税人

    private String specialRemark;// 特殊类型备注

    private Integer isDefault;// 是否为默认报价 0-不是 , 1-是材料默认供应商, 2-油墨默认供应商

    private String supplierId; // 供应商id

    private String customerContactId; // 客户联系人id

    private String customerBranchId; // 客户子公司id

    private String customerSalesAssistantId; // 业务员助理id

    private String mobile; // 联系人手机号

    private String email; // 电子邮件

    private String position; // 职务

    private String habit; // 习惯爱好

    private Date birthday; // 生日

    private String code; // 公司代码

    private String nameEn; // 公司英文

    private String templatePath; // 路径

    private String logoPath; // logo地址

    private String stampPath;// 专用章

    private String linkName; // 客户子公司联系人

    private User user; // 用户

    private Integer saveType; // 保存类型

    private Integer backupFlag; // 1:非备份记录，2:备份记录(用来区分备份表中的客户子公司、客户联系人、业务员助理、默认客户联系人属性保存的是备份id和非备份id)

    private String staffQtyValue;

    private String payWayValue;

    private String freightWayValue;

    private String taxDescriptValue;

    private String currencyTypeValue;

    private String deliveryWayValue;

    private String signplaceValue;

    private String deliverycityValue;

    private String frightbearValue;

    private String billformulaValue;

    private String hasquestionValue;

    private String paycauseValue;

    private String inspectionstandardValue;

    private String packingrequirementValue;

    private String levelValue;

    private String typeValue;

    private String yearSaleroomValue;

    private Double capacityPrecent;

    private Integer logisticsTime;

    private String deptId;

    private Double salePrecent;

    private String batchId;

    private String confirmBilDate;

    private String checkBilDate;

    private BigDecimal annualDemand;//线路板年需求量

    private String custType;

    private Date exportDate;

    private BigDecimal spareRate; // 备品比例

    private boolean sparestatus;

    private boolean auctionStatus;

    private Boolean arrivePay;

    private Boolean cashArrival; // 票到付款

    private Long businessPaymentId; // 业务费月结方式

    private String businessPaymentValue;

    private String industryName;

    private String manufacturerId; // 品牌id

    private String icloudCompanyId; // 大生态圈企业id

    private String manufacturer;

    private String icomName;

    private String supplyBrandId; // 供应品牌

    private String supplyBrandName; // 供应品牌名称

    private String printRemark; // 供应商打印备注

    private Integer materialKind; // 区分原料/成品，2成品，1和''原料

    private String classificationId;

    private String classificationValue;

    private String payDaysValue;

    public BackupTwo()
    {
        super();
        this.backupFlag = 1;
    }

    public BackupTwo(String id)
    {
        super(id);
    }

    public String getNo()
    {
        return no;
    }

    public void setNo(String no)
    {
        this.no = no;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getShortName()
    {
        return shortName;
    }

    public void setShortName(String shortName)
    {
        this.shortName = shortName;
    }

    public String getAddress()
    {
        return address;
    }

    public void setAddress(String address)
    {
        this.address = address;
    }

    public String getZip()
    {
        return zip;
    }

    public void setZip(String zip)
    {
        this.zip = zip;
    }

    public String getAreaCode()
    {
        return areaCode;
    }

    public void setAreaCode(String areaCode)
    {
        this.areaCode = areaCode;
    }

    public String getCompanyType()
    {
        return companyType;
    }

    public void setCompanyType(String companyType)
    {
        this.companyType = companyType;
    }

    public String getIndustry()
    {
        return industry;
    }

    public void setIndustry(String industry)
    {
        this.industry = industry;
    }

    public Long getStaffQty()
    {
        return staffQty;
    }

    public void setStaffQty(Long staffQty)
    {
        this.staffQty = staffQty;
    }

    public Long getYearSaleroom()
    {
        return yearSaleroom;
    }

    public void setYearSaleroom(Long yearSaleroom)
    {
        this.yearSaleroom = yearSaleroom;
    }

    public String getPhone()
    {
        return phone;
    }

    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getFax()
    {
        return fax;
    }

    public void setFax(String fax)
    {
        this.fax = fax;
    }

    public String getWebsite()
    {
        return website;
    }

    public void setWebsite(String website)
    {
        this.website = website;
    }

    public String getBank()
    {
        return bank;
    }

    public void setBank(String bank)
    {
        this.bank = bank;
    }

    public String getAccount()
    {
        return account;
    }

    public void setAccount(String account)
    {
        this.account = account;
    }

    public Long getPayWay()
    {
        return payWay;
    }

    public void setPayWay(Long payWay)
    {
        this.payWay = payWay;
    }

    public String getCheckDate()
    {
        return checkDate;
    }

    public void setCheckDate(String checkDate)
    {
        this.checkDate = checkDate;
    }

    public String getBizPersonName()
    {
        return bizPersonName;
    }

    public void setBizPersonName(String bizPersonName)
    {
        this.bizPersonName = bizPersonName;
    }

    public String getLegalPerson()
    {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson)
    {
        this.legalPerson = legalPerson;
    }

    public String getOrganCode()
    {
        return organCode;
    }

    public void setOrganCode(String organCode)
    {
        this.organCode = organCode;
    }

    public String getLicence()
    {
        return licence;
    }

    public void setLicence(String licence)
    {
        this.licence = licence;
    }

    public String getTaxRegNo()
    {
        return taxRegNo;
    }

    public void setTaxRegNo(String taxRegNo)
    {
        this.taxRegNo = taxRegNo;
    }

    public String getCasingInfo()
    {
        return casingInfo;
    }

    public void setCasingInfo(String casingInfo)
    {
        this.casingInfo = casingInfo;
    }

    public String getDeliveryNeedPriceFlag()
    {
        return deliveryNeedPriceFlag;
    }

    public void setDeliveryNeedPriceFlag(String deliveryNeedPriceFlag)
    {
        this.deliveryNeedPriceFlag = deliveryNeedPriceFlag;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public Integer getStatus()
    {
        return status;
    }

    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public User getSalesman()
    {
        return salesman;
    }

    public void setSalesman(User salesman)
    {
        this.salesman = salesman;
    }

    public Long getFreightWay()
    {
        return freightWay;
    }

    public void setFreightWay(Long freightWay)
    {
        this.freightWay = freightWay;
    }

    public Long getTaxDescript()
    {
        return taxDescript;
    }

    public void setTaxDescript(Long taxDescript)
    {
        this.taxDescript = taxDescript;
    }

    public Long getCurrencyType()
    {
        return currencyType;
    }

    public void setCurrencyType(Long currencyType)
    {
        this.currencyType = currencyType;
    }

    public Long getDeliveryWay()
    {
        return deliveryWay;
    }

    public void setDeliveryWay(Long deliveryWay)
    {
        this.deliveryWay = deliveryWay;
    }

    public String getQq()
    {
        return qq;
    }

    public void setQq(String qq)
    {
        this.qq = qq;
    }

    public String getWechat()
    {
        return wechat;
    }

    public void setWechat(String wechat)
    {
        this.wechat = wechat;
    }

    public double getScrappage()
    {
        return scrappage;
    }

    public void setScrappage(double scrappage)
    {
        this.scrappage = scrappage;
    }

    public Date getInitializeDate()
    {
        return initializeDate;
    }

    public void setInitializeDate(Date initializeDate)
    {
        this.initializeDate = initializeDate;
    }

    public Date getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }

    public void setSentTimeStartQr(Date sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }

    public Date getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }

    public void setSentTimeEndQr(Date sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }

    public String getSelectdate()
    {
        return selectdate;
    }

    public void setSelectdate(String selectdate)
    {
        this.selectdate = selectdate;
    }

    public Integer getCount()
    {
        return count;
    }

    public void setCount(Integer count)
    {
        this.count = count;
    }

    public BigDecimal getTotalAmt()
    {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt)
    {
        this.totalAmt = totalAmt;
    }

    public Long getSignplace()
    {
        return signplace;
    }

    public void setSignplace(Long signplace)
    {
        this.signplace = signplace;
    }

    public Long getDeliverycity()
    {
        return deliverycity;
    }

    public void setDeliverycity(Long deliverycity)
    {
        this.deliverycity = deliverycity;
    }

    public Long getFrightbear()
    {
        return frightbear;
    }

    public void setFrightbear(Long frightbear)
    {
        this.frightbear = frightbear;
    }

    public Long getBillformula()
    {
        return billformula;
    }

    public void setBillformula(Long billformula)
    {
        this.billformula = billformula;
    }

    public Long getHasquestion()
    {
        return hasquestion;
    }

    public void setHasquestion(Long hasquestion)
    {
        this.hasquestion = hasquestion;
    }

    public Long getPaycause()
    {
        return paycause;
    }

    public void setPaycause(Long paycause)
    {
        this.paycause = paycause;
    }

    public Long getInspectionstandard()
    {
        return inspectionstandard;
    }

    public void setInspectionstandard(Long inspectionstandard)
    {
        this.inspectionstandard = inspectionstandard;
    }

    public Long getPackingrequirement()
    {
        return packingrequirement;
    }

    public void setPackingrequirement(Long packingrequirement)
    {
        this.packingrequirement = packingrequirement;
    }

    public Company getQuiltCombinationCompany()
    {
        return quiltCombinationCompany;
    }

    public void setQuiltCombinationCompany(Company quiltCombinationCompany)
    {
        this.quiltCombinationCompany = quiltCombinationCompany;
    }

    public Double getDiffiCoff()
    {
        return diffiCoff;
    }

    public void setDiffiCoff(Double diffiCoff)
    {
        this.diffiCoff = diffiCoff;
    }

    public Long getLevel()
    {
        return level;
    }

    public void setLevel(Long level)
    {
        this.level = level;
    }

    public String getAssistantName()
    {
        return assistantName;
    }

    public void setAssistantName(String assistantName)
    {
        this.assistantName = assistantName;
    }

    public String getCustomerId()
    {
        return customerId;
    }

    public void setCustomerId(String customerId)
    {
        this.customerId = customerId;
    }

    public DictValue getPayDays()
    {
        return payDays;
    }

    public void setPayDays(DictValue payDays)
    {
        this.payDays = payDays;
    }

    public String getQualityStd()
    {
        return qualityStd;
    }

    public void setQualityStd(String qualityStd)
    {
        this.qualityStd = qualityStd;
    }

    public String getBizPerson()
    {
        return bizPerson;
    }

    public void setBizPerson(String bizPerson)
    {
        this.bizPerson = bizPerson;
    }

    public String getTaxpayerFlag()
    {
        return taxpayerFlag;
    }

    public void setTaxpayerFlag(String taxpayerFlag)
    {
        this.taxpayerFlag = taxpayerFlag;
    }

    public String getSpecialRemark()
    {
        return specialRemark;
    }

    public void setSpecialRemark(String specialRemark)
    {
        this.specialRemark = specialRemark;
    }

    public Integer getIsDefault()
    {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault)
    {
        this.isDefault = isDefault;
    }

    public String getSupplierId()
    {
        return supplierId;
    }

    public void setSupplierId(String supplierId)
    {
        this.supplierId = supplierId;
    }

    public String getCustomerContactId()
    {
        return customerContactId;
    }

    public void setCustomerContactId(String customerContactId)
    {
        this.customerContactId = customerContactId;
    }

    public String getCustomerBranchId()
    {
        return customerBranchId;
    }

    public void setCustomerBranchId(String customerBranchId)
    {
        this.customerBranchId = customerBranchId;
    }

    public String getCustomerSalesAssistantId()
    {
        return customerSalesAssistantId;
    }

    public void setCustomerSalesAssistantId(String customerSalesAssistantId)
    {
        this.customerSalesAssistantId = customerSalesAssistantId;
    }

    public String getMobile()
    {
        return mobile;
    }

    public void setMobile(String mobile)
    {
        this.mobile = mobile;
    }

    public String getEmail()
    {
        return email;
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    public String getPosition()
    {
        return position;
    }

    public void setPosition(String position)
    {
        this.position = position;
    }

    public String getHabit()
    {
        return habit;
    }

    public void setHabit(String habit)
    {
        this.habit = habit;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getBirthday()
    {
        return birthday;
    }

    public void setBirthday(Date birthday)
    {
        this.birthday = birthday;
    }

    public String getCode()
    {
        return code;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public String getNameEn()
    {
        return nameEn;
    }

    public void setNameEn(String nameEn)
    {
        this.nameEn = nameEn;
    }

    public String getTemplatePath()
    {
        return templatePath;
    }

    public void setTemplatePath(String templatePath)
    {
        this.templatePath = templatePath;
    }

    public String getLogoPath()
    {
        return logoPath;
    }

    public void setLogoPath(String logoPath)
    {
        this.logoPath = logoPath;
    }

    public String getStampPath()
    {
        return stampPath;
    }

    public void setStampPath(String stampPath)
    {
        this.stampPath = stampPath;
    }

    public String getLinkName()
    {
        return linkName;
    }

    public void setLinkName(String linkName)
    {
        this.linkName = linkName;
    }

    public User getUser()
    {
        return user;
    }

    public void setUser(User user)
    {
        this.user = user;
    }

    public Integer getSaveType()
    {
        return saveType;
    }

    public void setSaveType(Integer saveType)
    {
        this.saveType = saveType;
    }

    public Integer getBackupFlag()
    {
        return backupFlag;
    }

    public void setBackupFlag(Integer backupFlag)
    {
        this.backupFlag = backupFlag;
    }

    public String getStaffQtyValue()
    {
        return staffQtyValue;
    }

    public void setStaffQtyValue(String staffQtyValue)
    {
        this.staffQtyValue = staffQtyValue;
    }

    public String getPayWayValue()
    {
        return payWayValue;
    }

    public void setPayWayValue(String payWayValue)
    {
        this.payWayValue = payWayValue;
    }

    public String getFreightWayValue()
    {
        return freightWayValue;
    }

    public void setFreightWayValue(String freightWayValue)
    {
        this.freightWayValue = freightWayValue;
    }

    public String getTaxDescriptValue()
    {
        return taxDescriptValue;
    }

    public void setTaxDescriptValue(String taxDescriptValue)
    {
        this.taxDescriptValue = taxDescriptValue;
    }

    public String getCurrencyTypeValue()
    {
        return currencyTypeValue;
    }

    public void setCurrencyTypeValue(String currencyTypeValue)
    {
        this.currencyTypeValue = currencyTypeValue;
    }

    public String getDeliveryWayValue()
    {
        return deliveryWayValue;
    }

    public void setDeliveryWayValue(String deliveryWayValue)
    {
        this.deliveryWayValue = deliveryWayValue;
    }

    public String getSignplaceValue()
    {
        return signplaceValue;
    }

    public void setSignplaceValue(String signplaceValue)
    {
        this.signplaceValue = signplaceValue;
    }

    public String getDeliverycityValue()
    {
        return deliverycityValue;
    }

    public void setDeliverycityValue(String deliverycityValue)
    {
        this.deliverycityValue = deliverycityValue;
    }

    public String getFrightbearValue()
    {
        return frightbearValue;
    }

    public void setFrightbearValue(String frightbearValue)
    {
        this.frightbearValue = frightbearValue;
    }

    public String getBillformulaValue()
    {
        return billformulaValue;
    }

    public void setBillformulaValue(String billformulaValue)
    {
        this.billformulaValue = billformulaValue;
    }

    public String getHasquestionValue()
    {
        return hasquestionValue;
    }

    public void setHasquestionValue(String hasquestionValue)
    {
        this.hasquestionValue = hasquestionValue;
    }

    public String getPaycauseValue()
    {
        return paycauseValue;
    }

    public void setPaycauseValue(String paycauseValue)
    {
        this.paycauseValue = paycauseValue;
    }

    public String getInspectionstandardValue()
    {
        return inspectionstandardValue;
    }

    public void setInspectionstandardValue(String inspectionstandardValue)
    {
        this.inspectionstandardValue = inspectionstandardValue;
    }

    public String getPackingrequirementValue()
    {
        return packingrequirementValue;
    }

    public void setPackingrequirementValue(String packingrequirementValue)
    {
        this.packingrequirementValue = packingrequirementValue;
    }

    public String getLevelValue()
    {
        return levelValue;
    }

    public void setLevelValue(String levelValue)
    {
        this.levelValue = levelValue;
    }

    public String getTypeValue()
    {
        return typeValue;
    }

    public void setTypeValue(String typeValue)
    {
        this.typeValue = typeValue;
    }

    public String getYearSaleroomValue()
    {
        return yearSaleroomValue;
    }

    public void setYearSaleroomValue(String yearSaleroomValue)
    {
        this.yearSaleroomValue = yearSaleroomValue;
    }

    public Double getCapacityPrecent()
    {
        return capacityPrecent;
    }

    public void setCapacityPrecent(Double capacityPrecent)
    {
        this.capacityPrecent = capacityPrecent;
    }

    public Integer getLogisticsTime()
    {
        return logisticsTime;
    }

    public void setLogisticsTime(Integer logisticsTime)
    {
        this.logisticsTime = logisticsTime;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public Double getSalePrecent()
    {
        return salePrecent;
    }

    public void setSalePrecent(Double salePrecent)
    {
        this.salePrecent = salePrecent;
    }

    public String getBatchId()
    {
        return batchId;
    }

    public void setBatchId(String batchId)
    {
        this.batchId = batchId;
    }

    public String getConfirmBilDate()
    {
        return confirmBilDate;
    }

    public String getCheckBilDate()
    {
        return checkBilDate;
    }

    public void setConfirmBilDate(String confirmBilDate)
    {
        this.confirmBilDate = confirmBilDate;
    }

    public void setCheckBilDate(String checkBilDate)
    {
        this.checkBilDate = checkBilDate;
    }

    public BigDecimal getAnnualDemand()
    {
        return annualDemand;
    }

    public void setAnnualDemand(BigDecimal annualDemand)
    {
        this.annualDemand = annualDemand;
    }

    public String getCustType()
    {
        return custType;
    }

    public void setCustType(String custType)
    {
        this.custType = custType;
    }

    public Date getExportDate()
    {
        return exportDate;
    }

    public void setExportDate(Date exportDate)
    {
        this.exportDate = exportDate;
    }

    public BigDecimal getSpareRate() {
        return spareRate;
    }

    public void setSpareRate(BigDecimal spareRate) {
        this.spareRate = spareRate;
    }

    public boolean isSparestatus() {
        return sparestatus;
    }

    public void setSparestatus(boolean sparestatus) {
        this.sparestatus = sparestatus;
    }

    public boolean isAuctionStatus() {
        return auctionStatus;
    }

    public void setAuctionStatus(boolean auctionStatus) {
        this.auctionStatus = auctionStatus;
    }

    public Boolean getArrivePay() {
        return arrivePay;
    }

    public void setArrivePay(Boolean arrivePay) {
        this.arrivePay = arrivePay;
    }

    public Boolean getCashArrival()
    {
        return cashArrival;
    }

    public void setCashArrival(Boolean cashArrival)
    {
        this.cashArrival = cashArrival;
    }

    public Long getBusinessPaymentId()
    {
        return businessPaymentId;
    }

    public void setBusinessPaymentId(Long businessPaymentId)
    {
        this.businessPaymentId = businessPaymentId;
    }

    public String getBusinessPaymentValue()
    {
        return businessPaymentValue;
    }

    public void setBusinessPaymentValue(String businessPaymentValue)
    {
        this.businessPaymentValue = businessPaymentValue;
    }

    public String getIndustryName()
    {
        return industryName;
    }

    public void setIndustryName(String industryName)
    {
        this.industryName = industryName;
    }

    public String getManufacturerId()
    {
        return manufacturerId;
    }

    public void setManufacturerId(String manufacturerId)
    {
        this.manufacturerId = manufacturerId;
    }

    public String getIcloudCompanyId()
    {
        return icloudCompanyId;
    }

    public void setIcloudCompanyId(String icloudCompanyId)
    {
        this.icloudCompanyId = icloudCompanyId;
    }

    public String getManufacturer()
    {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer)
    {
        this.manufacturer = manufacturer;
    }

    public String getIcomName()
    {
        return icomName;
    }

    public void setIcomName(String icomName)
    {
        this.icomName = icomName;
    }

    public String getSupplyBrandId()
    {
        return supplyBrandId;
    }

    public void setSupplyBrandId(String supplyBrandId)
    {
        this.supplyBrandId = supplyBrandId;
    }

    public String getSupplyBrandName()
    {
        return supplyBrandName;
    }

    public void setSupplyBrandName(String supplyBrandName)
    {
        this.supplyBrandName = supplyBrandName;
    }

    public String getPrintRemark()
    {
        return printRemark;
    }

    public void setPrintRemark(String printRemark)
    {
        this.printRemark = printRemark;
    }

    public Integer getMaterialKind()
    {
        return materialKind;
    }

    public void setMaterialKind(Integer materialKind)
    {
        this.materialKind = materialKind;
    }

    public String getMaterialKindStr()
    {
        String result = "原料";
        if(null != materialKind && materialKind.compareTo(2) == 0)
        {
            result = "成品";
        }
        return result;
    }

    public String getClassificationId()
    {
        return classificationId;
    }

    public void setClassificationId(String classificationId)
    {
        this.classificationId = classificationId;
    }

    public String getClassificationValue()
    {
        return classificationValue;
    }

    public void setClassificationValue(String classificationValue)
    {
        this.classificationValue = classificationValue;
    }

    public String getPayDaysValue() {
        return payDaysValue;
    }

    public void setPayDaysValue(String payDaysValue) {
        this.payDaysValue = payDaysValue;
    }
}