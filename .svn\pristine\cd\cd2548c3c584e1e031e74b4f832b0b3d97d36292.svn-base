package com.kyb.pcberp.modules.report.dao;

import java.util.List;

import com.kyb.pcberp.modules.production.entity.CapacityChange;
import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.production.entity.CapacityRecord;
import com.kyb.pcberp.modules.report.entity.*;

/**
 * @ClassName ReportCapacityDao
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/2/9 9:53
 * @Version 1.0
 **/
@MyBatisDao
public interface ReportCapacityDao
{
    
    List<ReportCapacityVersion> versionList(@Param("companyId") String companyId);
    
    Integer createVersion(@Param("version") ReportCapacityVersion version);
    
    ReportCapacityRecord findByVersionId(@Param("versionId") String versionId, @Param("companyId") String companyId);
    
    Integer insertRecord(ReportCapacityRecord record);
    
    Integer updateRecord(ReportCapacityRecord record);
    
    // 查询所有已用产能集合
    List<CapacityDeail> getCapacityUsed(ReportCapacityRecordVo req);
    
    // 查询产能配置月份或最近月份集合
    List<CapacityRecord> getCapacitySetByMonth(ReportCapacityRecordVo req);
    
    // 查询产能规则月份或最近月份集合
    ReportCapacityRecord getReportCapacitySetByMonth(ReportCapacityRecordVo req);

    // 查询所有已用产能集合
    List<CapacityDeail> getCapacityUsedDetail(ReportCapacityRecordVo req);
    
    // 修改版本为已应用
    void updateVesionUseFlag(@Param("useFlag") Integer useFlag, @Param("recordId") String recordId);
    
    // 新增考核结算记录
    int insertCapacityAssessment(ReportCapacityAssessment assessment);
    
    // 新增考核结算记录详情
    int insertCapacityAssessmentDetail(@Param("list") List<ReportCapacityAssessmentDetail> assessmentDetailList);
    
    Integer getCapacityAssessmentMaxId();
    
    // 查询账期结账记录
    List<ReportCapacityAssessment> capacityAssessmentList(@Param("yearMonth") String yearMonth);

//    //查询当月订单总面积
//    List<ReportCapacityRecordVo> totalOrderArea(ReportCapacityRecordVo vo);
}
