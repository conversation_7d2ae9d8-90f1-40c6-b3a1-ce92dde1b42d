package com.kyb.pcberp.modules.purch.vo;

import java.math.BigDecimal;

import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;

/**
 * 付款申请 Value Object
 * 
 * <AUTHOR> @version
 */
public class PayApplicationVo
{
    private String receiptNo;// 单据编号
    
    private String supplierNo; // 供应商编号
    
    private String supplierName; // 供应商名称
    
    private String fundType; // 款项类型
    
    private String purchasingType;// 采购类型
    
    private String purchasingNo;// 采购单据编号
    
    private String payMethod; // 支付方式
    
    private BigDecimal amount; // 申请金额
    
    private BigDecimal nonePayAmount; // 待付金额
    
    private BigDecimal totalAmount;// 采购单据金额
    
    private String status; // 状态
    
    private String currencyType; // 货币类型
    
    private String payBank; // 付款银行
    
    private String payAccount; // 付款账号
    
    private String bank; // 对方收款银行
    
    private String receiveAccount; // 对方收款账号
    
    private String applyDate; // 申请日期
    
    private String applicant; // 申请人
    
    private String payCause; // 付款原因
    
    protected String remark; // 备注
    
    private String applyType;
    
    private String taxRate; // 税率
    
    private BigDecimal adValoremAmount; // 价税金额

    private String period;

    private BigDecimal inOutQuantity; // 入库数量

    private BigDecimal nowarehousingNum; // 未入库数量

    private BigDecimal nowarehousingAmount; // 未入库金额
    
    @ExcelField(title = "单据编号", align = 2, sort = 10)
    public String getReceiptNo()
    {
        return receiptNo;
    }
    
    public void setReceiptNo(String receiptNo)
    {
        this.receiptNo = receiptNo;
    }
    
    @ExcelField(title = "供应商编号", align = 2, sort = 20)
    public String getSupplierNo()
    {
        return supplierNo;
    }
    
    public void setSupplierNo(String supplierNo)
    {
        this.supplierNo = supplierNo;
    }
    
    @ExcelField(title = "供应商名称", align = 2, sort = 30)
    public String getSupplierName()
    {
        return supplierName;
    }
    
    public void setSupplierName(String supplierName)
    {
        this.supplierName = supplierName;
    }
    
    @ExcelField(title = "款项类型", align = 2, sort = 40)
    public String getFundType()
    {
        return fundType;
    }
    
    public void setFundType(String fundType)
    {
        this.fundType = fundType;
    }
    
    @ExcelField(title = "采购类型", align = 2, sort = 50)
    public String getPurchasingType()
    {
        return purchasingType;
    }
    
    public void setPurchasingType(String purchasingType)
    {
        this.purchasingType = purchasingType;
    }
    
    @ExcelField(title = "采购单据编号", align = 2, sort = 60)
    public String getPurchasingNo()
    {
        return purchasingNo;
    }
    
    public void setPurchasingNo(String purchasingNo)
    {
        this.purchasingNo = purchasingNo;
    }
    
    @ExcelField(title = "支付方式", align = 2, sort = 70)
    public String getPayMethod()
    {
        return payMethod;
    }
    
    public void setPayMethod(String payMethod)
    {
        this.payMethod = payMethod;
    }
    
    @ExcelField(title = "申请金额", align = 2, sort = 80)
    public BigDecimal getAmount()
    {
        return amount;
    }
    
    public void setAmount(BigDecimal amount)
    {
        this.amount = amount;
    }
    
    @ExcelField(title = "待付金额", align = 2, sort = 90)
    public BigDecimal getNonePayAmount()
    {
        return nonePayAmount;
    }
    
    public void setNonePayAmount(BigDecimal nonePayAmount)
    {
        this.nonePayAmount = nonePayAmount;
    }

    @ExcelField(title = "入库数量", align = 2, sort = 100)
    public BigDecimal getInOutQuantity() {
        return inOutQuantity;
    }

    public void setInOutQuantity(BigDecimal inOutQuantity) {
        this.inOutQuantity = inOutQuantity;
    }

    @ExcelField(title = "未入库数量", align = 2, sort = 110)
    public BigDecimal getNowarehousingNum() {
        return nowarehousingNum;
    }

    public void setNowarehousingNum(BigDecimal nowarehousingNum) {
        this.nowarehousingNum = nowarehousingNum;
    }

    @ExcelField(title = "未入库金额", align = 2, sort = 120)
    public BigDecimal getNowarehousingAmount() {
        return nowarehousingAmount;
    }

    public void setNowarehousingAmount(BigDecimal nowarehousingAmount) {
        this.nowarehousingAmount = nowarehousingAmount;
    }

    @ExcelField(title = "采购单据金额", align = 2, sort = 130)
    public BigDecimal getTotalAmount()
    {
        return totalAmount;
    }
    
    public void setTotalAmount(BigDecimal totalAmount)
    {
        this.totalAmount = totalAmount;
    }
    
    @ExcelField(title = "状态", align = 2, sort = 140)
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    @ExcelField(title = "货币类型", align = 2, sort = 150)
    public String getCurrencyType()
    {
        return currencyType;
    }
    
    public void setCurrencyType(String currencyType)
    {
        this.currencyType = currencyType;
    }
    
    @ExcelField(title = "付款银行", align = 2, sort = 160)
    public String getPayBank()
    {
        return payBank;
    }
    
    public void setPayBank(String payBank)
    {
        this.payBank = payBank;
    }
    
    @ExcelField(title = "付款账号", align = 2, sort = 170)
    public String getPayAccount()
    {
        return payAccount;
    }
    
    public void setPayAccount(String payAccount)
    {
        this.payAccount = payAccount;
    }
    
    @ExcelField(title = "收款银行", align = 2, sort = 180)
    public String getBank()
    {
        return bank;
    }
    
    public void setBank(String bank)
    {
        this.bank = bank;
    }
    
    @ExcelField(title = "收款账号", align = 2, sort = 190)
    public String getReceiveAccount()
    {
        return receiveAccount;
    }
    
    public void setReceiveAccount(String receiveAccount)
    {
        this.receiveAccount = receiveAccount;
    }
    
    @ExcelField(title = "申请时间", align = 2, sort = 200)
    public String getApplyDate()
    {
        return applyDate;
    }
    
    public void setApplyDate(String applyDate)
    {
        this.applyDate = applyDate;
    }
    
    @ExcelField(title = "申请人", align = 2, sort = 210)
    public String getApplicant()
    {
        return applicant;
    }
    
    public void setApplicant(String applicant)
    {
        this.applicant = applicant;
    }
    
    @ExcelField(title = "付款原因", align = 2, sort = 220)
    public String getPayCause()
    {
        return payCause;
    }
    
    public void setPayCause(String payCause)
    {
        this.payCause = payCause;
    }
    
    @ExcelField(title = "备注", align = 2, sort = 230)
    public String getRemark()
    {
        return remark;
    }
    
    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    @ExcelField(title = "申请类型", align = 2, sort = 31)
    public String getApplyType()
    {
        return applyType;
    }
    
    public void setApplyType(String applyType)
    {
        this.applyType = applyType;
    }

    @ExcelField(title = "税率", align = 2, sort = 92)
    public String getTaxRate()
    {
        return taxRate;
    }

    @ExcelField(title = "价税金额", align = 2, sort = 94)
    public BigDecimal getAdValoremAmount()
    {
        return adValoremAmount;
    }

    public void setTaxRate(String taxRate)
    {
        this.taxRate = taxRate;
    }

    public void setAdValoremAmount(BigDecimal adValoremAmount)
    {
        this.adValoremAmount = adValoremAmount;
    }

    @ExcelField(title = "付款账期", align = 2, sort = 75)
    public String getPeriod()
    {
        return period;
    }

    public void setPeriod(String period)
    {
        this.period = period;
    }
}
