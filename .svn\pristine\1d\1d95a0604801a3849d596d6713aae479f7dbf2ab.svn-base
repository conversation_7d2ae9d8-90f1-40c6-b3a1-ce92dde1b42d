<!doctype html>
<head>
  <!-- jQuery packaging changed for 2.1.0, so try to load both paths, one will work. -->
  <script src="../../bower_components/jquery/dist/jquery.js"></script>
  <script src="../../bower_components/jquery/jquery.js"></script>
  <script src="../../bower_components/raphael/raphael-min.js"></script>
  <script src="../../morris.js"></script>
  <link rel="stylesheet" href="../../morris.css">
  <style>
    body {
      padding: 0;
      margin: 0;
      background-color: white;
    }
    #chart {
      width: 500px;
      height: 300px;
    }
  </style>
  <script>
    function bridge(e) {
      window.alert(JSON.stringify(e));
    }
    window.snapshot = function () {
      bridge({ fn: "snapshot" });
    };
    window.mousemove = function (x, y) {
      bridge({ fn: "mousemove", x: x, y: y });
    };
  </script>
</head>
<body>
<div id="chart"></div>
</body>
