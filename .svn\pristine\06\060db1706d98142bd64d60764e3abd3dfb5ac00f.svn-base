package com.kyb.pcberp.modules.crm.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

public class PrintInstructionsDetail extends DataEntity<PrintInstructionsDetail>
{
    private static final long serialVersionUID = 1L;

    private PrintInstructions printInstructions;

    private String printInsId;

    private String content;

    public PrintInstructions getPrintInstructions()
    {
        return printInstructions;
    }

    public void setPrintInstructions(PrintInstructions printInstructions)
    {
        this.printInstructions = printInstructions;
    }

    public String getPrintInsId()
    {
        return printInsId;
    }

    public void setPrintInsId(String printInsId)
    {
        this.printInsId = printInsId;
    }

    public String getContent()
    {
        return content;
    }

    public void setContent(String content)
    {
        this.content = content;
    }
}
