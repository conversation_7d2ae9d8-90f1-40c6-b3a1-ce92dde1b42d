<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.hr.permission_center.dao.Hr_RoleDao">
    <select id="getGroupItemList" resultType="Hr_Item">
        SELECT
            a.*,
            1 AS "flag",
            (
                SELECT
                    COUNT(aa.recordId)
                FROM
                    group_role_item aa
                WHERE
                    aa.itemId = a.recordId
                AND aa.activeFlag = 1
                AND aa.flag = 1
            ) AS "useNum"
        FROM group_item a
        WHERE a.activeFlag = 1;
    </select>

    <select id="getOaItemList" resultType="Hr_Item">
        SELECT
            a.*,
            2 AS "flag",
            (
                SELECT
                    COUNT(aa.recordId)
                FROM
                    group_role_item aa
                WHERE
                    aa.itemId = a.recordId
                  AND aa.activeFlag = 1
                  AND aa.flag = 2
            ) AS "useNum"
        FROM oa_item a
        WHERE a.activeFlag = 1;
    </select>

    <select id="getErpItemList" resultType="Hr_Item">
        SELECT
            a.*,
            3 AS "flag",
            (
                SELECT
                    COUNT(aa.recordId)
                FROM
                    group_role_item aa
                WHERE
                    aa.itemId = a.recordId
                  AND aa.activeFlag = 1
                  AND aa.flag = 3
            ) AS "useNum"
        FROM sm_item a
        WHERE a.activeFlag = 1;
    </select>

    <select id="getWxItemList" resultType="Hr_Item">
        SELECT
            a.*,
            4 AS "flag",
            (
                SELECT
                    COUNT(aa.recordId)
                FROM
                    group_role_item aa
                WHERE
                    aa.itemId = a.recordId
                  AND aa.activeFlag = 1
                  AND aa.flag = 4
            ) AS "useNum"
        FROM wx_item a
        WHERE a.activeFlag = 1;
    </select>

    <select id="getRoleItemList" resultType="Hr_Role_Item">
        SELECT
            a.*, b.`name` AS "roleName",
            c.`name` AS "erpName",
            d.`name` AS "departName"
        FROM
            group_role_item a
        LEFT JOIN group_role b ON b.recordId = a.roleId
        LEFT JOIN md_company c ON c.recordId = a.erpId
        LEFT JOIN icloud_group_org d ON d.recordId = a.departId
        WHERE
            a.itemId = #{itemId}
         <if test="flag != null and flag != ''">
            AND a.flag = #{flag}
         </if>
        AND a.activeFlag = 1
    </select>

    <select id="getRoleList" resultType="Hr_Role">
        SELECT
            a.*, (
            SELECT
                COUNT(aa.recordId)
            FROM
                group_role_item aa
            WHERE
                aa.roleId = a.recordId
              AND aa.activeFlag = 1
        ) AS "itemNums",
            (
                SELECT
                    COUNT(aa.recordId)
                FROM
                    group_emp_role aa
                WHERE
                    aa.roleId = a.recordId
                  AND aa.activeFlag = 1
            ) AS "empNums"
        FROM
            group_role a
        WHERE
            a.activeFlag = 1;
    </select>

    <select id="getErpList" resultType="Company">
        SELECT
            *
        FROM
            md_company
        WHERE
            activeFlag = 1
          AND groupUserId = #{groupManageId};
    </select>

    <select id="getDepartList" resultType="Hr_DepartMent">
        SELECT
            *
        FROM
            icloud_group_org
        WHERE
            activeFlag = 1
    </select>

    <select id="getItemListByRole" resultType="Hr_Role_Item">
        SELECT
            a.*
        FROM
            group_role_item a
        WHERE
            a.roleId = #{recordId}
        AND a.activeFlag = 1;
    </select>

    <update id="delRoleItemAll">
        UPDATE group_role_item SET
            activeFlag = 0
        WHERE roleId = #{recordId}
    </update>

    <update id="delRoleItem">
        UPDATE group_role_item SET
            activeFlag = 0
        WHERE recordId = #{recordId}
    </update>

    <update id="delRole">
        UPDATE group_role SET
            activeFlag = 0
        WHERE recordId = #{recordId}
    </update>

    <select id="getRoleItemNum" resultType="Integer">
        SELECT
            COUNT(recordId)
        FROM
            group_role_item
        WHERE
            activeFlag = 1
        AND roleId = #{roleId}
        AND itemId = #{itemId}
        AND (erpId IS NULL OR erpId = #{erpId})
        AND (departId IS NULL OR departId = #{departId})
        AND flag = #{flag}
    </select>

    <select id="getRoleByName" resultType="Integer">
        SELECT
        *
        FROM
        group_role
        WHERE
        activeFlag = 1
        AND `name` = #{name}
        <if test="recordId != null and recordId != ''">
            AND recordId <![CDATA[<>]]> #{recordId}
        </if>
    </select>

    <update id="updateRoleList">
        <foreach collection="list" item="item" separator=";">
            UPDATE group_role SET
            name = #{item.name},
            remark = #{item.remark}
            WHERE recordId = #{item.recordId}
        </foreach>
    </update>

    <insert id="insertRoleList" parameterType="java.util.List">
        INSERT INTO group_role (
            name,
            activeFlag,
            remark
        ) VALUES
        <foreach collection="list" item="item" index= "index" separator =",">
            (
            #{item.name},
            1,
            #{item.remark}
            )
        </foreach>
    </insert>

    <insert id="setItemList" parameterType="java.util.List">
        INSERT INTO group_role_item(
            roleId,
            itemId,
            erpId,
            departId,
            level,
            permission,
            activeFlag,
            remark,
            dataRange,
            flag
        ) VALUES
        <foreach collection="list" item="item" index= "index" separator =",">
            (
            #{item.roleId},
            #{item.itemId},
            #{item.erpId},
            #{item.departId},
            #{item.level},
            #{item.permission},
            1,
            #{item.remark},
            #{item.dataRange},
            #{item.flag}
            )
        </foreach>
    </insert>

    <update id="editRoleItem">
        UPDATE group_role_item SET
            erpId = #{erpId},
            departId = #{departId},
            level = #{level},
            dataRange = #{dataRange}
        WHERE recordId = #{recordId}
    </update>

    <insert id="setEmpRoleList" parameterType="java.util.List">
        INSERT INTO group_emp_role(
        empId,
        roleId,
        activeFlag,
        remark,
        erpId,
        departId,
        level,
        dataRange
        ) VALUES
        <foreach collection="list" item="item" index= "index" separator =",">
            (
            #{item.empId},
            #{item.roleId},
            1,
            #{item.remark},
            #{item.erpId},
            #{item.departId},
            #{item.level},
            #{item.dataRange}
            )
        </foreach>
    </insert>

    <update id="editEmpRole">
        UPDATE group_emp_role SET
            erpId = #{erpId},
            departId = #{departId},
            level = #{level},
            dataRange = #{dataRange}
        WHERE recordId = #{recordId}
    </update>

    <update id="delEmpItemAll">
        UPDATE group_emp_role SET
            activeFlag = 0
        WHERE empId = #{recordId}
    </update>

    <update id="delEmpRole">
        UPDATE group_emp_role SET
            activeFlag = 0
        WHERE recordId = #{recordId}
    </update>

    <update id="delEmpRoleTwo">
        UPDATE staff_position_sheet SET
            activeFlag = 0
        WHERE recordId = #{recordId}
    </update>

    <select id="getEmpListByRole" resultType="Hr_Emp_Role">
        SELECT
            a.*
        FROM
            group_emp_role a
        WHERE
            a.roleId = #{recordId}
          AND a.activeFlag = 1;
    </select>

    <select id="getEmpRoleList" resultType="Hr_Emp_Role">
        SELECT
            a.*
        FROM
            group_emp_role a
        WHERE
            a.empId = #{recordId}
        AND a.activeFlag = 1;
    </select>

    <select id="getEmpRoleNum" resultType="Integer">
        SELECT
            COUNT(recordId)
        FROM
            group_emp_role
        WHERE
            activeFlag = 1
          AND empId = #{empId}
        AND roleId =#{roleId}
    </select>
    <update id="delRoleExample">
        UPDATE group_role_item a
		LEFT JOIN group_role b ON b.recordId = a.roleId
		SET a.activeFlag = 0
		WHERE
			a.activeFlag = 1
		AND a.roleId = #{recordId} AND a.flag = #{flag}
    </update>

    <select id="getEmpListByRoleTwo" resultType="Hr_Emp_Role">
        SELECT
            a.*
        FROM
            staff_position_sheet a
        WHERE
            a.activeFlag = 1;
    </select>

    <update id="updateEmpPositionCenter">
        <foreach collection="list" item="item" separator=";">
            UPDATE oa_md_employee SET
            position = (select name from oa_position WHERE recordId = #{item.positionId})
            WHERE recordId = #{item.recordId}
        </foreach>
    </update>

    <select id="getErpCompanyList" resultType="Company">
        SELECT
            b.recordId,
            b.`name`
        FROM
            sm_user a
                LEFT JOIN md_company b ON b.recordId = a.companyId
        WHERE
            a.phone = #{phone} AND a.activeFlag = 1
    </select>
</mapper>