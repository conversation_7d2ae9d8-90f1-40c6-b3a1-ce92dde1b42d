package com.kyb.pcberp.modules.stock.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.stock.entity.ProductStore;

public class ProductStockPlaceExpVo extends ProductStore
{
    // 物料编号 客户型号 创建时间 交货尺寸 期初数量 总入库 总出库 结余 仓库
    
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    
    private String no;
    
    private String customerModel;
    
    private Date createdDate;
    
    private String size;
    
    private BigDecimal startStocks;
    
    private BigDecimal inStocks;
    
    private BigDecimal outStocks;
    
    private BigDecimal currStocks;
    
    private String name;
    
    @ExcelField(title = "物料编号", align = 2, sort = 10)
    public String getNo()
    {
        return no;
    }
    
    public void setNo(String no)
    {
        this.no = no;
    }
    
    @ExcelField(title = "客户型号", align = 2, sort = 20)
    public String getCustomerModel()
    {
        return customerModel;
    }
    
    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }
    
    @ExcelField(title = "创建时间", align = 2, sort = 30)
    public Date getCreatedDate()
    {
        return createdDate;
    }
    
    public void setCreatedDate(Date createdDate)
    {
        this.createdDate = createdDate;
    }
    
    @ExcelField(title = "交货尺寸", align = 2, sort = 40)
    public String getSize()
    {
        return size;
    }
    
    public void setSize(String size)
    {
        this.size = size;
    }
    
    @ExcelField(title = "期初数量", align = 2, sort = 50)
    public BigDecimal getStartStocks()
    {
        return startStocks;
    }
    
    public void setStartStocks(BigDecimal startStocks)
    {
        this.startStocks = startStocks;
    }
    
    @ExcelField(title = "总入库", align = 2, sort = 60)
    public BigDecimal getInStocks()
    {
        return inStocks;
    }
    
    public void setInStocks(BigDecimal inStocks)
    {
        this.inStocks = inStocks;
    }
    
    @ExcelField(title = "总出库", align = 2, sort = 70)
    public BigDecimal getOutStocks()
    {
        return outStocks;
    }
    
    public void setOutStocks(BigDecimal outStocks)
    {
        this.outStocks = outStocks;
    }
    
    @ExcelField(title = "结余", align = 2, sort = 80)
    public BigDecimal getCurrStocks()
    {
        return currStocks;
    }
    
    public void setCurrStocks(BigDecimal currStocks)
    {
        this.currStocks = currStocks;
    }
    
    @ExcelField(title = "仓库", align = 2, sort = 90)
    public String getName()
    {
        return name;
    }
    
    public void setName(String name)
    {
        this.name = name;
    }
    
}
