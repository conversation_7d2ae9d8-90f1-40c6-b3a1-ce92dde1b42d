package com.kyb.pcberp.modules.contract.service;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.enums.CommonEnums.DictItemEnum;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.modules.approval.dao.ApprovalDao;
import com.kyb.pcberp.modules.approval.entity.Approval;
import com.kyb.pcberp.modules.approval.entity.Backups;
import com.kyb.pcberp.modules.approval.utils.ModifyUtils;
import com.kyb.pcberp.modules.contract.dao.*;
import com.kyb.pcberp.modules.contract.entity.*;
import com.kyb.pcberp.modules.contract.utils.ChangeDataUtils;
import com.kyb.pcberp.modules.contract.vo.ContractImpVo;
import com.kyb.pcberp.modules.crm.dao.CustomerDao;
import com.kyb.pcberp.modules.crm.dao.QuotationDetailDao;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.crm.entity.CustomerContact;
import com.kyb.pcberp.modules.crm.entity.QuotationDetail;
import com.kyb.pcberp.modules.inter.dao.ConfigCustomerAttachementsDao;
import com.kyb.pcberp.modules.inter.entity.ConfigCustomerAttachements;
import com.kyb.pcberp.modules.production.dao.FeedingDao;
import com.kyb.pcberp.modules.production.dao.ProduceBatchDetailDao;
import com.kyb.pcberp.modules.purch.dao.*;
import com.kyb.pcberp.modules.purch.entity.*;
import com.kyb.pcberp.modules.stock.dao.ProductStoreDao;
import com.kyb.pcberp.modules.stock.entity.ProductStore;
import com.kyb.pcberp.modules.sys.dao.*;
import com.kyb.pcberp.modules.sys.entity.*;
import com.kyb.pcberp.modules.sys.utils.CockpitUtilsOne;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class NewContractService
{
    @Autowired
    private ContractDao contractDao;

    @Autowired
    private ContractDetailDao contractDetailDao;

    @Autowired
    private ContractCraftDao contractCraftDao;

    @Autowired
    private PriceDao priceDao;

    @Autowired
    private BatchDeliveryDao batchDeliveryDao;

    @Autowired
    private DeliveryDetailDao deliveryDetailDao;

    @Autowired
    private NotificationDao notificationDao;

    @Autowired
    private SupplierDao supplierDao;

    @Autowired
    private UpdateDataDao updateDataDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private ProduceBatchDetailDao produceBatchDetailDao;

    @Autowired
    private BranchDao branchDao;

    @Autowired
    private FeedingDao feedingDao;

    @Autowired
    private DictValueDao dictValueDao;

    @Autowired
    private ParameterSetDao parameterSetDao;

    @Autowired
    private PrdorderDao prdorderDao;

    @Autowired
    private ProductCraftDao productCraftDao;

    @Autowired
    private ProductPriceDao productPriceDao;

    @Autowired
    private PrdorderDetailDao prdorderDetailDao;

    @Autowired
    private CompanyDao companyDao;

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private ContractAttachementsDao contractAttachementsDao;

    @Autowired
    private ApprovalDao approvalDao;

    @Autowired
    private ProductStoreDao productStoreDao;

    @Autowired
    private ProductStocksUseDao productStocksUseDao;

    @Autowired
    private MaxBatchAreaConfirmDao maxBatchAreaConfirmDao;

    @Autowired
    private QuotationDetailDao quotationDetailDao;

    @Autowired
    private MaterialAreaDetailDao materialAreaDetailDao;

    @Autowired
    private GroupCenterDao groupCenterDao;

    @Autowired
    private ConfigCustomerAttachementsDao configCustomerAttachementsDao;

    @Transactional(readOnly = false)
    public synchronized Map<String, Object> saveContractDetail(ContractDetail contractDetail)
    {
        if (null == contractDetail)
        {
            return null;
        }
        Map<String, Object> result = new HashMap<>();

        // 控制一键集控后马上修改合同明细内容
        Integer checkNum = contractDetailDao.getGroupCenterCount(contractDetail);
        if(null != checkNum && checkNum > 0)
        {
            result.put("result", "failRefresh");
            result.put("message", "订单明细已经一键集控了，不能修改!");
            return result;
        }

        BigDecimal valiArea = BigDecimal.ZERO;
        Company company = UserUtils.getUser().getCompany();
        String factId = CompanyUtil.getInstance().getFactId();
        if (StringUtils.isNotBlank(contractDetail.getOrderDeailArea()))
        {
            valiArea = new BigDecimal(contractDetail.getOrderDeailArea());
        }

        /*if (valiArea.compareTo(BigDecimal.ONE) > 0)
        {
            BigDecimal price = contractDetail.getPricees().getPrice() == null ?
                BigDecimal.ZERO :
                contractDetail.getPricees().getPrice();
            if (price.compareTo(BigDecimal.ZERO) <= 0)
            {
                result.put("result", "fail");
                result.put("message", "订单面积大于1㎡的必须要有单价!");
                return result;
            }
        }*/
        else if (company.getRecordId().equals(factId))
        {
            // rx 2021-12-03 返单样品 不用效验订单必须要有金额!
            String reOrderSample = null;
            for (DictValue dictValue : DictUtils.getValuesByItem(DictItemEnum.PRODUCTION_TYPE))
            {
                if (ConstKey.ORDER_TYPE_REORDER_SAMPLE.equals(dictValue.getValue())
                    || ConstKey.ORDER_TYPE_NEW_SAMPLE.equals(dictValue.getValue())
                    || "只做资料不投产".equals(dictValue.getValue()) || "样板".equals(dictValue.getValue())
                    || ConstKey.ORDER_TYPE_SAMPLE_CHANGE.equals(dictValue.getValue()))
                {
                    if (StringUtils.isNotBlank(reOrderSample))
                    {
                        reOrderSample = reOrderSample + "," + dictValue.getRecordId();
                    }
                    else
                    {
                        reOrderSample = dictValue.getRecordId();
                    }
                }

            }
            if (!(contractDetail.getReferenceType() != null && reOrderSample.contains(contractDetail.getReferenceType()
                .toString())))
            {
                BigDecimal subTotal =
                    contractDetail.getSubTotal() == null ? BigDecimal.ZERO : contractDetail.getSubTotal();
                if (subTotal.compareTo(BigDecimal.ZERO) <= 0)
                {
                    result.put("result", "fail");
                    result.put("message", "订单必须要有金额!");
                    return result;
                }
            }

        }
        // 验证下合同数量>备品数量
        if (StringUtils.isNotBlank(contractDetail.getQuantity()) && null != contractDetail.getSpareQuantity())
        {
            if (contractDetail.getSpareQuantity() > Integer.parseInt(contractDetail.getQuantity()))
            {
                result.put("result", "fail");
                result.put("message", "备单数量不得大于总数量!");
                return result;
            }
        }

        // 判断订单类型为样板、新单样品、返单样品、新资料的订单明细面积要小于等于配置条件
        ParameterSet param = new ParameterSet();
        param.setCompany(new Company(CompanyUtil.getInstance().getFactId()));
        param.setJianPin("orderAreaLimit");
        List<ParameterSet> params = parameterSetDao.findList(param);
        BigDecimal paramsValue = BigDecimal.ZERO;
        if (Collections3.isNotEmpty(params) && StringUtils.isNotBlank(params.get(0).getParameterValue()))
        {
            paramsValue = new BigDecimal(params.get(0).getParameterValue());
        }
        Integer count = dictValueDao.checkReferenceTypeCount(contractDetail);
        if (null != count && count > 0)
        {
            BigDecimal pnlWidth = BigDecimal.ZERO;
            BigDecimal pnlLength = BigDecimal.ZERO;
            BigDecimal pnlDivisor = new BigDecimal("1");
            BigDecimal qty = BigDecimal.ZERO;
            if (null != contractDetail.getContractCraftList())
            {
                ContractCraft craft = contractDetail.getContractCraftList();
                if (null != craft.getPnlWidth())
                {
                    pnlWidth = craft.getPnlWidth();
                }
                if (null != craft.getPnlLength())
                {
                    pnlLength = craft.getPnlLength();
                }
                if (null != craft.getPnlDivisor())
                {
                    pnlDivisor = craft.getPnlDivisor();
                }
                if (StringUtils.isNotBlank(contractDetail.getQuantity()))
                {
                    qty = new BigDecimal(contractDetail.getQuantity());
                }
                BigDecimal area = pnlWidth.multiply(pnlLength)
                    .multiply(qty)
                    .divide(pnlDivisor.multiply(new BigDecimal("1000000")), 4, BigDecimal.ROUND_HALF_UP);
                if (area.compareTo(paramsValue) > 0)
                {
                    result.put("result", "fail");
                    result.put("message", "样品的面积不能大于" + paramsValue + "平米,请重新选择订单类型!");
                    return result;
                }
            }
        }

        // 如果返单生产厂编包含S直接返回
/*        Integer num = contractDetailDao.checkDetailOrderTypeCount(contractDetail);
        if (null != num && num > 0)
        {
            String craftNo = null;
            if (StringUtils.isNotBlank(contractDetail.getNotiCraftNo()))
            {
                ContractCraft query = new ContractCraft();
                query.setRecordId(contractDetail.getNotiCraftNo());
                ContractCraft craft = contractCraftDao.get(query);
                if (null != craft && StringUtils.isNotBlank(craft.getNo()))
                {
                    craftNo = craft.getNo();
                }
            }
            else if (StringUtils.isNotBlank(contractDetail.getGenCraftNo()))
            {
                craftNo = contractDetail.getGenCraftNo().trim();
            }
            if (StringUtils.isNotBlank(craftNo))
            {
                craftNo = craftNo.substring(1, craftNo.length() - 1);
                if (craftNo.toLowerCase().contains("s".toLowerCase()))
                {
                    result.put("result", "fail");
                    result.put("message", "返单生产、新单量产的厂编不能是含S的样品厂编");
                    return result;
                }
            }
        }*/

        // 只做资料不投产单价才能为0
        // Boolean checkFlag = false;
        // List<DictValue> productionTypeList = DictUtils.getValuesByItem(DictItemEnum.PRODUCTION_TYPE);
        // for (DictValue productionType : productionTypeList)
        // {
        // if (productionType.getRecordId().equals(contractDetail.getReferenceType().toString()))
        // {
        // if (!"只做资料不投产".equals(productionType.getValue())
        // && (null == contractDetail.getPricees() || null == contractDetail.getPricees().getPrice()
        // || contractDetail.getPricees().getPrice().compareTo(BigDecimal.ZERO) == 0))
        // {
        // checkFlag = true;
        // break;
        // }
        // }
        // }
        // if(checkFlag)
        // {
        // result.put("result", "fail");
        // result.put("message", "单价必须填写!");
        // return result;
        // }
        Contract contract = null == contractDetail.getContract() ? new Contract() : contractDetail.getContract();
        Customer customer = customerDao.get(contract.getCustomer());
        if (null != customer)
        {
            if (!customer.getCompany().getRecordId().equals(company.getRecordId()))
            {
                result.put("result", "fail");
                result.put("message", "数据异常，请刷新重试!");
                return result;
            }
            if (null == contract.getUser() || StringUtils.isBlank(contract.getUser().getRecordId()))
            {
                contract.setUser(customer.getSalesman());
            }
        }
        // 客户等级更新
        if(null != contract.getCustomer() && StringUtils.isNotBlank(contract.getCustomerGrade()))
        {
            contract.getCustomer().setCustomerGrade(Long.valueOf(contract.getCustomerGrade()));
            customerDao.updateCustomerGrade(contract.getCustomer());
        }
        String saleId = null;
        if (contract.getIsNewRecord())
        {
            contract.setCompany(company);
            contract.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
            Integer noNum = contractDao.findContractNoisEnable(contract);
            while (noNum != null && noNum > 0)
            {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.CONTRACT.getIndex());
                contract.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CONTRACT.getIndex().toString()));
                noNum = contractDao.findContractNoisEnable(contract);
            }
            contract.preInsert();
            contractDao.insert(contract);
            CommonUtils.updateNextNo(CommonEnums.CodeType.CONTRACT.getIndex());
        }
        else
        {
            // 存在合同时需判断同一个合同中不能出现内部的和外协不选供应商的
            ContractDetail supNumObj = contractDetailDao.getSupNum(contract);
            if(null != supNumObj)
            {
                if(contractDetail.getIsNewRecord())
                {
                    if(StringUtils.isNotBlank(contractDetail.getMadeSupplierId()))
                    {
                        supNumObj.setSupNumOne(supNumObj.getSupNumOne() + 1);
                    }
                    else
                    {
                        supNumObj.setSupNumTwo(supNumObj.getSupNumTwo() + 1);
                    }
                }
                if(supNumObj.getSupNumOne() > 0 && supNumObj.getSupNumTwo() > 0)
                {
                    result.put("result", "fail");
                    result.put("message", "同一个合同中，不能出现合同明细有选外发供应商和不选外发供应商的情况!");
                    return result;
                }
            }

            // 存在合同时需判断同一个合同中不能出现不同总部经济
            List<ContractDetail> ecoemyIdList = contractDetailDao.getEcoemyIdList(contractDetail);
            if (Collections3.isNotEmpty(ecoemyIdList))
            {
                for (ContractDetail detail : ecoemyIdList)
                {
                    if (null != detail.getEconnmyId() && StringUtils.isNotBlank(detail.getEconnmyId()) && !detail.getEconnmyId().equals(contractDetail.getEconnmyId()))
                    {
                        result.put("result", "fail");
                        result.put("message", "同一个合同中，不能出现合同明细有不同的总部经济公司!");
                        return result;
                    }
                }
            }

            contract.preUpdate();
            contractDao.update(contract);
            contractDao.updatecreatedBy(contract);
        }

        ContractCraft craft = contractDetail.getContractCraftList();
        Boolean addFlag = false;

        if(StringUtils.isNotBlank(contract.getAppId()) && "1".equals(contract.getAppId()))
        {
            if(StringUtils.isBlank(contractDetail.getCustomerProcessNumber()))
            {
                contractDetail.setCustomerProcessNumber(contract.getCustomerPo()+"/1");
            }
        }

        if (contractDetail.getIsNewRecord())
        {
            addFlag = true;
            if (null == craft)
            {
                craft = new ContractCraft();
            }

            if (StringUtils.isBlank(craft.getRecordId()) || StringUtils.isBlank(craft.getNo()) || (StringUtils.isNotBlank(
                contractDetail.getDictMaterialType()) && "1".equals(contractDetail.getDictMaterialType())))
            {
                craft.setCompany(company);
                craft.setCustomer(contract.getCustomer());
                craft.setNo("");
                craft.setStatus(TypeKey.SL_CRAFT_STATUS_DRAFT.toString());
                craft.setAddCraftFlag("1");
                if(StringUtils.isBlank(craft.getProductTypeId()))
                {
                    craft.setProductTypeId(contractDetail.getProductTypeId());
                }
                craft.preInsert();
                contractCraftDao.insert(craft);
                // 设置终端id
                craft.setFinalId(craft.getRecordId());
                contractCraftDao.setFinalId(craft);
            }else{
                craft.setProductTypeId(contractDetail.getProductTypeId());
                craft.preUpdate();
                contractCraftDao.update(craft);
            }

            Price price = contractDetail.getPricees();
            price.setCompany(company);
            price.setCraft(craft);
/*            price.setCardMouldFee(price.getMouldFee());
            price.setCardTestShelfFee(price.getTestShelfFee());*/
            price.preInsert();
            priceDao.insert(price);

            contractDetail.setCompany(company);
            contractDetail.setPricees(price);
            contractDetail.setCraft(craft);
            contractDetail.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
            contractDetail.setGroupCenterType("2"); // 集控类型默认为外协，一键集控操作后会修改为内部
            contractDetail.preInsert();
            contractDetailDao.insert(contractDetail);
            if(StringUtils.isNotBlank(contractDetail.getInterCustomRecordId())){
                contractDetailDao.updateContractId(contractDetail.getContract().getRecordId(),contractDetail.getInterCustomRecordId());
                // 查询附件
                List<ConfigCustomerAttachements> attachementsList =  configCustomerAttachementsDao.getAttachementsList(contractDetail.getInterCustomRecordId());
                if (Collections3.isNotEmpty(attachementsList))
                {
                    for (ConfigCustomerAttachements configCustomerAttachements : attachementsList)
                    {
                        ContractAttachements contractAttachements = new ContractAttachements();
                        contractAttachements.setContractDetailId(contractDetail.getRecordId());
                        contractAttachements.setCompany(company);
                        contractAttachements.setContractId(contractDetail.getContract().getRecordId());
                        contractAttachements.setOrgFileName(configCustomerAttachements.getOrgFileName());
                        contractAttachements.setRealFileName(configCustomerAttachements.getRealFileName());
                        contractAttachementsDao.insert(contractAttachements);
                    }
                }
            }
            CockpitUtilsOne cockpitUtilsOne = new CockpitUtilsOne();
            cockpitUtilsOne.addOperatingRecord(1,contractDetail,1);

            contractDetailDao.updateComData(contractDetail);

            if (StringUtils.isBlank(saleId))
            {
                saleId = branchDao.getBranchId(contract.getCompany());
            }

//            BatchDelivery batch = new BatchDelivery();
//            batch.setCompany(company);
//            if (null != contractDetail.getQuantity())
//            {
//                batch.setQuantity(Integer.parseInt(contractDetail.getQuantity()));
//            }
//            batch.setDeliveryDate(contractDetail.getDeliveryDate());
//            batch.setConDetId(contractDetail.getRecordId());
//            batch.setOrderDate(contract.getOrderDate());
//            batch.preInsert();
//            batchDeliveryDao.insert(batch);

            if (StringUtils.isNotBlank(saleId))
            {
                contract.setSaleId(saleId);
                contractDao.updateSaleId(contract);
            }
            // 新增合同明细时，增加一条默认收货地址：收货人取合同的，收货数据量等于明细数量
            if (null == contractDetail.getContract().getCustomerContact()
                || StringUtils.isEmpty(contractDetail.getContract().getCustomerContact().getAddress())
                || StringUtils.isEmpty(contractDetail.getContract().getCustomerContact().getPhone()))
            {
                result.put("result", "fail");
                result.put("message", "客户联系人必填，且客户地址和电话不能为空");
                return result;
            }
            addDetailDefaulAddress(contractDetail);
        }
        else
        {
            String newNo = contractDetailDao.getNewCraftNo(contractDetail);
            if(StringUtils.isNotBlank(newNo))
            {
                craft.setNo(newNo);
            }

            // 修改自己的厂编
            craft.setProductTypeId(contractDetail.getProductTypeId());
            craft.preUpdate();
            contractCraftDao.update(craft);
/*            ShareDataUtil.getInstance().saveData(craft, 4, 2);*/
            // 保存合同价格
            Price pricees = contractDetail.getPricees();
            pricees.setCompany(company);
            pricees.setCraft(craft);
            pricees.preUpdate();
/*            pricees.setCardMouldFee(pricees.getMouldFee());
            pricees.setCardTestShelfFee(pricees.getTestShelfFee());*/
            priceDao.update(pricees);

            CockpitUtilsOne cockpitUtilsOne = new CockpitUtilsOne();
            cockpitUtilsOne.addOperatingRecord(1,contractDetail,3);
            contractDetail.preUpdate();
            contractDetailDao.update(contractDetail);

        }

        // 获取分批交货信息
        List<ContractDetailAddress> addressList =  batchDeliveryDao.getListByContract(contract);
        if(Collections3.isNotEmpty(addressList))
        {
            for(ContractDetailAddress address : addressList)
            {
                batchDeliveryDao.updateConCatAdress(address);
            }
        }

        // 更新分批交货
//        batchDeliveryDao.updateDeliveryDateByConDetId(contract);

        if (null != contractDetail.getMaxBatchAreaSetUp() && null != contractDetail.getMaxBatchArea())
        {
            if (contractDetail.getMaxBatchArea().compareTo(contractDetail.getMaxBatchAreaSetUp()) > 0)
            {
                // 备份批次面积
                Backups query = new Backups();
                query.setCompany(contractDetail.getCompany());
                query.setContractDetailId(contractDetail.getRecordId());
                Backups backups = approvalDao.getBackups(query);
                if (null != backups && StringUtils.isNotBlank(backups.getRecordId()))
                {
                    backups.setArea(contractDetail.getMaxBatchArea().toString());
                    approvalDao.updateBackups(backups);
                }
                else
                {
                    query.setArea(contractDetail.getMaxBatchArea().toString());
                    approvalDao.insertBackups(query);
                }
            }
            else
            {
                contractDetailDao.updateMaxBatchArea(contractDetail);
            }
        }

        // 更新采购税种说明
        contractDetailDao.updatePrdTaxDescript(contractDetail);

        String factComName = null;
        Company factCom = companyDao.get(factId);
        if (null != factCom)
        {
            factComName = factCom.getName();
        }

        GroupCenter groupCenter = contractDetailDao.getGroupCenter(contractDetail);
        if (CompanyUtil.getInstance().valiteSaleCompany() || (StringUtils.isNotBlank(factComName)
            && !StringUtils.isEmpty(contractDetail.getFactoryName())
            && contractDetail.getFactoryName().indexOf(factComName) != -1))
        {
            company.setFactoryComId(factId);
            String supplierId = supplierDao.getJxSupplierId(company);
            if ((null == groupCenter || StringUtils.isBlank(groupCenter.getRecordId())) && StringUtils.isNotBlank(
                supplierId) && !supplierId.equals(contractDetail.getMadeSupplierId()))
            {
                contractDetail.setGroupCenterId(contractDetail.getRecordId());
                contractDetailDao.updateGroupCenterId(contractDetail);
                contractDetail.setGroupCenterId(null);
            }
            else
            {
                if (null != groupCenter)
                {
                    if (StringUtils.isNotBlank(groupCenter.getCraftNo()))
                    {
                        contractDetail.setCraftNo(groupCenter.getCraftNo());
                    }
                    else if (StringUtils.isNotBlank(groupCenter.getGenCraftNo()))
                    {
                        contractDetail.setCraftNo(groupCenter.getGenCraftNo());
                    }
                    contractDetail.setGroupCenterId(groupCenter.getRecordId());
                }

                // 内部添加时备份工艺数据，工程修改时会更新工艺表数据
                ContractCraft craftCopy = craft.clone();
                craftCopy.setCompany(company);
                craftCopy.setContractDetailId(contractDetail.getRecordId());
                if (addFlag)
                {
                    contractCraftDao.insertCraftClone(craftCopy);
                }
                else
                {
                    contractCraftDao.updateCraftClone(craftCopy);
                }
            }
        }
        else
        {
            if ((null == groupCenter || StringUtils.isBlank(groupCenter.getRecordId())) && StringUtils.isNotBlank(
                contractDetail.getMadeSupplierId()))
            {
                contractDetail.setGroupCenterId(contractDetail.getRecordId());
                contractDetailDao.updateGroupCenterId(contractDetail);
                contractDetail.setGroupCenterId(null);
            }
        }

        // 修改合同金额
        BigDecimal money = BigDecimal.ZERO;
        BigDecimal area = BigDecimal.ZERO;
        String factoryComId = CompanyUtil.getInstance().getFactId();
        String ecoemyId = CompanyUtil.getInstance().getEcoemyId();
        contract.setFactoryComId(factoryComId);
        contract.setEcoemyId(ecoemyId);
        List<ContractDetail> list = contractDetailDao.findDetailAndCraftsByContractId(contract);
        for (ContractDetail cd : list)
        {
            if (null != cd.getSubTotal())
            {
                money = cd.getSubTotal().add(money);
            }
            if (StringUtils.isNotBlank(cd.getOrderDeailArea()))
            {
                area = new BigDecimal(cd.getOrderDeailArea()).add(area);
            }
        }
        // 保存外协出库存占用记录
        saveOutStocks(contractDetail);
        // 获取出库存占用记录
        ProductStocksUse use = new ProductStocksUse();
        use.setCompany(company);
        use.setDetailId(contractDetail.getRecordId());
        List<ProductStocksUse> useList = productStocksUseDao.getList(use);
        contractDetail.setStocksUseList(useList);

        contract.setTotalAmt(money);
        contract.setOrderArea(String.valueOf(area));
        contractDao.updateTotalAmt(contract);
        contract.setContractDetails(list);
        result.put("contract", contract);
        result.put("contractDeail", contractDetail);
        result.put("result", "success");
        return result;
    }

    private void addDetailDefaulAddress(ContractDetail contractDetail)
    {
        ContractDetailAddress defaultAddress = new ContractDetailAddress();
        defaultAddress.preInsert();
        defaultAddress.setCompany(contractDetail.getCompany());
        defaultAddress.setContacter(contractDetail.getContract().getCustomerContact());
        defaultAddress.setQuantity(Integer.parseInt(contractDetail.getQuantity()));
        defaultAddress.setSortNum(1);
        defaultAddress.setContractDetailId(contractDetail.getRecordId());
        defaultAddress.setContacterId(contractDetail.getContract().getCustomerContact().getRecordId());
        contractDetailDao.insertAddress(defaultAddress);
        List<ContractDetailAddress> addressList = new ArrayList<ContractDetailAddress>();
        addressList.add(defaultAddress);
        contractDetail.setAddressList(addressList);

    }

    public synchronized Map<String, Object> orderApproval(Contract contract)
    {
        Map<String, Object> data = new HashMap<>();
        String ecoemyId = CompanyUtil.getInstance().getEcoemyIdTwo(1, contract.getRecordId());
        String factId = CompanyUtil.getInstance().getFactId();
        boolean con = ModifyUtils.allocationExist("23", contract.getCompany());
        if (con)
        {
            Company company = UserUtils.getUser().getCompany();
            ChangeDataUtils dataUtils = new ChangeDataUtils();
            String companyId = company.getRecordId();
            String result = null;
            contract.setFactoryComId(factId);
            // 本公司合同明细（合同、厂编、价格、合同明细）
            List<ContractDetail> conDetailList =
                contractDetailDao.getDeailByContractId(contract.getRecordId(), "23", contract);
            if (Collections3.isNotEmpty(conDetailList))
            {
                contract.setContractDetails(conDetailList);
            }
            // 组合所有要处理的工艺，获取所有该公司相关工艺
            String ids = "";
            for (int i = 0; i < conDetailList.size(); i++)
            {
                if (StringUtils.isNotBlank(conDetailList.get(i).getMadeSupplierId()))
                {
                    if (StringUtils.isNotBlank(ids))
                    {
                        ids = ids + "," + dataUtils.getNeedDealData(conDetailList.get(i));
                    }
                    else
                    {
                        ids = dataUtils.getNeedDealData(conDetailList.get(i));
                    }
                }
            }
            String comIds = "";
            List<DictValue> valList = new ArrayList<DictValue>();
            Map<String, String> map = new HashMap<String, String>();
            if (!CompanyUtil.getInstance().getFactId().equals(company.getRecordId()))
            {
                if (StringUtils.isNotBlank(ids))
                {
                    if(StringUtils.isNotBlank(ecoemyId))
                    {
                        comIds = ecoemyId;
                    }
                    if(StringUtils.isNotBlank(factId))
                    {
                        if(StringUtils.isNotBlank(comIds))
                        {
                            comIds = comIds +","+ factId;
                        }
                        else
                        {
                            comIds = factId;
                        }
                    }
                    if(StringUtils.isNotBlank(companyId))
                    {
                        if(StringUtils.isNotBlank(comIds))
                        {
                            comIds = comIds +","+ companyId;
                        }
                        else
                        {
                            comIds = companyId;
                        }
                    }
                    valList = contractDetailDao.getAllValue(comIds, ids);
                    map = dataUtils.getMapDatas(valList);
                }
                for (int i = 0; i < conDetailList.size(); i++)
                {
                    if (StringUtils.isNotBlank(conDetailList.get(i).getMadeSupplierId()))
                    {
                        if (StringUtils.isNotBlank(conDetailList.get(i).getMadeSupplierName())
                            && conDetailList.get(i).getMadeSupplierName().indexOf("江西领德辉") != -1)
                        {
                            // 江西合同
                            ContractDetail jxContractDetail = conDetailList.get(i).clone();
                            // 进行工艺转换
                            result = dataUtils.trainCondeail(valList,jxContractDetail,factId,map,company.getRecordId());
                            if (StringUtils.isNotBlank(result))
                            {
                                break;
                            }
                        }
                        if(StringUtils.isNotBlank(ecoemyId))
                        {
                            // 龙南合同
                            ContractDetail lnContractDetail = conDetailList.get(i).clone();
                            // 进行工艺转换
                            result = dataUtils.trainCondeail(valList,lnContractDetail,ecoemyId,map,company.getRecordId());
                        }
                        if (StringUtils.isNotBlank(result))
                        {
                            break;
                        }
                    }
                }
            }
            if (StringUtils.isNotBlank(result))
            {
                data.put("result", "fail");
                data.put("message", result);
                return data;
            }
            String status = contractDao.getContractStatus(contract);
            if (StringUtils.isNotBlank(status) && TypeKey.SL_CONTRACT_STATUS_WAIT.toString().equals(status))
            {
                data.put("result", "fail");
                data.put("message", "合同已经在审批中，请刷新重试");
                return data;
            }
            // 查询订单免审批是否开启，开启才执行
            ParameterSet param = new ParameterSet();
            param.setJianPin("orderApprovalFree");
            param.setCompany(company);
            Integer orderApprovalFree = parameterSetDao.getRawCompanyAndJianPin(param);
            if (null != orderApprovalFree && orderApprovalFree == 1)
            {
                // 验证pcs单价是否大于等于报价pcs单价
                for (ContractDetail contractDetail : conDetailList)
                {
                    // 平米单价
                    BigDecimal pcsPrice = BigDecimal.ZERO;
                    BigDecimal deliverySize = BigDecimal.ZERO;
                    BigDecimal squareMeterPrice = BigDecimal.ZERO;
                    if (null == contractDetail.getContractCraftList().getPnlLength() || contractDetail.getContractCraftList().getPnlLength().compareTo(BigDecimal.ZERO) == 0)
                    {
                        contractDetail.getContractCraftList().setPnlLength(BigDecimal.ZERO);
                    }
                    if (null == contractDetail.getContractCraftList().getPnlWidth() || contractDetail.getContractCraftList().getPnlWidth().compareTo(BigDecimal.ZERO) == 0)
                    {
                        contractDetail.getContractCraftList().setPnlWidth(BigDecimal.ZERO);
                    }
                    if (null == contractDetail.getContractCraftList().getPnlDivisor() || contractDetail.getContractCraftList().getPnlDivisor().compareTo(BigDecimal.ZERO) == 0)
                    {
                        contractDetail.getContractCraftList().setPnlDivisor(BigDecimal.ONE);
                    }
                    deliverySize = contractDetail.getContractCraftList().getPnlLength().multiply(contractDetail.getContractCraftList().getPnlWidth()).divide(contractDetail.getContractCraftList().getPnlDivisor(), 6, RoundingMode.HALF_UP).divide(new BigDecimal(1000000),6, RoundingMode.HALF_UP);
                    pcsPrice = contractDetail.getPricees().getPrice();
                    if (null != deliverySize && deliverySize.compareTo(BigDecimal.ZERO) != 0)
                    {
                        squareMeterPrice = pcsPrice.divide(deliverySize,6, RoundingMode.HALF_UP);
                    }
                    QuotationDetail quotationDetail = new QuotationDetail();
                    quotationDetail.setCompany(UserUtils.getUser().getCompany());
                    quotationDetail.setCustomer(new Customer());
                    if (null != contractDetail.getContract() && StringUtils.isNotBlank(contractDetail.getContract().getCustomerId()))
                    {
                        quotationDetail.getCustomer().setRecordId(contractDetail.getContract().getCustomerId());
                    }
                    if (null != contractDetail.getProcessValueId() && StringUtils.isNotBlank(contractDetail.getProcessValueId()))
                    {
                        quotationDetail.setProcessValueId(contractDetail.getProcessValueId());
                    }
                    if (null != contractDetail.getContractCraftList())
                    {
                        if (null!= contractDetail.getContractCraftList().getCustomerModel())
                        {
                            quotationDetail.setCustomerModel(contractDetail.getContractCraftList().getCustomerModel());
                        }
                        if (null != contractDetail.getContractCraftList().getUnitWidth())
                        {
                            quotationDetail.setUnitWidth(contractDetail.getContractCraftList().getUnitWidth());
                        }
                        if (null != contractDetail.getContractCraftList().getUnitLength())
                        {
                            quotationDetail.setUnitLength(contractDetail.getContractCraftList().getUnitLength());
                        }
                        if (null != contractDetail.getContractCraftList().getPnlWidth())
                        {
                            quotationDetail.setPnlWidth(contractDetail.getContractCraftList().getPnlWidth());
                        }
                        if (null != contractDetail.getContractCraftList().getPnlLength())
                        {
                            quotationDetail.setPnlLength(contractDetail.getContractCraftList().getPnlLength());
                        }
                        if (null != contractDetail.getContractCraftList().getPnlDivisor())
                        {
                            quotationDetail.setPnlDivisor(contractDetail.getContractCraftList().getPnlDivisor().intValue());
                        }
                        if (null != contractDetail.getContractCraftList().getMaterialType())
                        {
                            quotationDetail.setMaterialType(contractDetail.getContractCraftList().getMaterialType());
                        }
                        if (null != contractDetail.getContractCraftList().getBoardLevel())
                        {
                            quotationDetail.setBoardLevel(contractDetail.getContractCraftList().getBoardLevel());
                        }
                        if (null != contractDetail.getContractCraftList().getBoardThickness())
                        {
                            quotationDetail.setBoardThickness(contractDetail.getContractCraftList().getBoardThickness());
                        }
                        if (null != contractDetail.getContractCraftList().getSurfaceProcess())
                        {
                            quotationDetail.setSurfaceProcess(contractDetail.getContractCraftList().getSurfaceProcess());
                        }
                        if (null != contractDetail.getContractCraftList().getCopperCladThickness())
                        {
                            quotationDetail.setCopperCladThickness(contractDetail.getContractCraftList().getCopperCladThickness());
                        }
                        if (null != contractDetail.getContractCraftList().getSolderMaskType())
                        {
                            quotationDetail.setSolderMaskType(contractDetail.getContractCraftList().getSolderMaskType());
                        }
                        if (null != contractDetail.getContractCraftList().getCharacterType())
                        {
                            quotationDetail.setCharacterType(contractDetail.getContractCraftList().getCharacterType());
                        }
                        if (null != contractDetail.getContractCraftList().getShapingWay())
                        {
                            quotationDetail.setShapingWay(contractDetail.getContractCraftList().getShapingWay());
                        }
                        if (null != contractDetail.getContractCraftList().getTestMethod())
                        {
                            quotationDetail.setTestMethod(contractDetail.getContractCraftList().getTestMethod());
                        }
                        if (null != contractDetail.getContractCraftList().getLingeSpacing())
                        {
                            quotationDetail.setLingeSpacing(contractDetail.getContractCraftList().getLingeSpacing());
                        }
                        if (null != contractDetail.getContractCraftList().getSmallAperture())
                        {
                            quotationDetail.setSmallAperture(contractDetail.getContractCraftList().getSmallAperture());
                        }
                        if (null != contractDetail.getContractCraftList().getHalAhole())
                        {
                            quotationDetail.setHalAhole(contractDetail.getContractCraftList().getHalAhole());
                        }
                        if (null != contractDetail.getContractCraftList().getBuryBlindHole())
                        {
                            quotationDetail.setBuryBlindHole(contractDetail.getContractCraftList().getBuryBlindHole());
                        }
                        if (null != contractDetail.getContractCraftList().getResistance())
                        {
                            quotationDetail.setResistance(contractDetail.getContractCraftList().getResistance());
                        }
                        if (null != contractDetail.getContractCraftList().getDeliveryUrgent())
                        {
                            quotationDetail.setDeliveryUrgent(contractDetail.getContractCraftList().getDeliveryUrgent());
                        }
                        if (null != contractDetail.getContractCraftList().getBanLiaoSupplier())
                        {
                            quotationDetail.setBanLiaoSupplier(contractDetail.getContractCraftList().getBanLiaoSupplier());
                        }
                        if (null != contractDetail.getContractCraftList().getYouMoSupplier())
                        {
                            quotationDetail.setYouMoSupplier(contractDetail.getContractCraftList().getYouMoSupplier());
                        }
                        if (null != contractDetail.getContractCraftList().getDaore())
                        {
                            quotationDetail.setDaore(contractDetail.getContractCraftList().getDaore());
                        }
                        if (null != contractDetail.getContractCraftList().getNaiya())
                        {
                            quotationDetail.setNaiya(contractDetail.getContractCraftList().getNaiya());
                        }
                        if (null != contractDetail.getContractCraftList().getPliesnumber())
                        {
                            quotationDetail.setPliesnumber(contractDetail.getContractCraftList().getPliesnumber());
                        }
                        quotationDetail.setPriceStatus("1");
                        QuotationDetail detail = quotationDetailDao.getQuotationDetailList(quotationDetail);
                        if (null != detail)
                        {
                            if (null != detail.getWindowPcsPrice() && null != detail.getEffectiveTime() && detail.getEffectiveTime().compareTo(new Date()) > 0)
                            {
                                if (squareMeterPrice.compareTo(detail.getWindowPcsPrice()) < 0)
                                {
                                    contractDetail.setType("2");
                                }
                                else
                                {
                                    contractDetail.setType("1");
                                }
                            }
                            else
                            {
                                if (null == detail.getEffectiveTime())
                                {
                                    if (squareMeterPrice.compareTo(detail.getWindowPcsPrice()) < 0)
                                    {
                                        contractDetail.setType("2");
                                    }
                                    else
                                    {
                                        contractDetail.setType("1");
                                    }
                                }
                                else
                                {
                                    contractDetail.setType("2");
                                }
                            }
                        }
                        else
                        {
                            quotationDetail.setUnitWidth(null);
                            quotationDetail.setUnitLength(null);
                            quotationDetail.setPnlWidth(null);
                            quotationDetail.setPnlLength(null);
                            quotationDetail.setPnlDivisor(null);
                            quotationDetail.setPriceStatus("2");
                            QuotationDetail detail1 = quotationDetailDao.getQuotationDetailList(quotationDetail);
                            if (null != detail1)
                            {
                                if (null != detail1.getWindowPcsPrice() && null != detail1.getEffectiveTime() && detail1.getEffectiveTime().compareTo(new Date()) > 0)
                                {
                                    if (squareMeterPrice.compareTo(detail1.getWindowPcsPrice()) < 0)
                                    {
                                        contractDetail.setType("2");
                                    }
                                    else
                                    {
                                        contractDetail.setType("1");
                                    }
                                }
                                else
                                {
                                    if (null == detail1.getEffectiveTime())
                                    {
                                        if (squareMeterPrice.compareTo(detail1.getWindowPcsPrice()) < 0)
                                        {
                                            contractDetail.setType("2");
                                        }
                                        else
                                        {
                                            contractDetail.setType("1");
                                        }
                                    }
                                }
                            }
                            else
                            {
                                contractDetail.setType("2");
                            }
                        }
                    }
                }
            }
            boolean flag = true;
            for (ContractDetail contractDetail : conDetailList)
            {
                if (!"1".equals(contractDetail.getType()))
                {
                    flag = false;
                    break;
                }
            }
            if (!flag)
            {
                result = ModifyUtils.orderApproval(contract, company);
                if (!"fail".equals(result))
                {
                    data.put("result", "success");
                    data.put("message", result);
                    return data;
                }
            }
            contract.setStatus(TypeKey.SL_CONTRACT_STATUS_MANEGE.toString());
            contractDao.updateStatus(contract);

            // 控制合同明细都是已审核状态
            ContractDetail contractDetail = new ContractDetail();
            contractDetail.setContractId(contract.getRecordId());
            contractDetail.setStatus(TypeKey.SL_CONTRACT_STATUS_MANEGE.toString());
            contractDetailDao.updateStatusByContractId(contractDetail);

            // 根据一键集控交期更新订单明细交期
            contractDetail = new ContractDetail();
            contractDetail.setContractId(contract.getRecordId());
            List<ContractDetail> contractDetailList = contractDetailDao.getContractDetailList(contractDetail);
            if (Collections3.isNotEmpty(contractDetailList))
            {
                for (ContractDetail condetail : contractDetailList)
                {
                    if (null != condetail.getGroupCenterId() && StringUtils.isNotBlank(condetail.getGroupCenterId()))
                    {
                        // 获取一键集控评估交期
                        GroupCenter groupCenter = groupCenterDao.selectGroupCenter(condetail.getGroupCenterId());
                        if (null != groupCenter && null != groupCenter.getEstimateDate())
                        {
                            condetail.setDeliveryDate(groupCenter.getEstimateDate());
                            contractDetailDao.updateDeliveryDate(condetail);
                        }
                    }
                }
            }

            ChangeDataUtils changeDataUtils = new ChangeDataUtils();
            User userCopy = new User();
            if (null != contract.getCreatedBy() && StringUtils.isNotBlank(contract.getCreatedBy().getRecordId()))
            {
                userCopy = contract.getCreatedBy();
            }
            else if (null != contract.getLastUpdBy() && StringUtils.isNotBlank(contract.getLastUpdBy().getRecordId()))
            {
                userCopy = contract.getLastUpdBy();
            }
            User us = userDao.get(userCopy);
            if (null == us)
            {
                us = UserUtils.getUser();
            }
            changeDataUtils.genConrtactData(contract.getRecordId(), company, us);
            data.put("result", "success");
            data.put("message", "合同审批成功!");
            return data;
        }
        else
        {
            data.put("result", "fail");
            data.put("message", "请先配置订单审批!");
        }
        return data;
    }

    @Transactional(readOnly = false)
    public synchronized Map<String, Object> contractApproval(Contract contract)
    {
        Map<String, Object> data = new HashMap<>();
        if (null == contract || StringUtils.isBlank(contract.getRecordId()))
        {
            data.put("result", "fail");
            data.put("message", "合同不能为空!");
            return data;
        }
        Company com = UserUtils.getUser().getCompany();
        contract.setCompany(com);
        Integer num = contractDetailDao.checkAttachementCount(contract);
        if (null == num || num == 0)
        {
            data.put("result", "fail");
            data.put("message", "请先上传附件!");
            return data;
        }

        contract.setFactoryComId(CompanyUtil.getInstance().getFactId());
        List<ContractDetail> details = contractDetailDao.getDeailByContractId(contract.getRecordId(), null, contract);
        if (Collections3.isEmpty(details))
        {
            data.put("result", "fail");
            data.put("message", "合同明细列表不能为空!");
            return data;
        }
        // 验证合同明细是否有在审批
        if (Collections3.isNotEmpty(details))
        {
            for (ContractDetail detail : details)
            {
                Approval approval = new Approval();
                approval.setTypeId("42");
                approval.setCompany(UserUtils.getUser().getCompany());
                approval.setDataId(detail.getRecordId());
                Integer count = approvalDao.checkIsApproved(approval);
                if (count > 0)
                {
                    data.put("result", "fail");
                    data.put("message", "议价审批正在审批中!");
                    return data;
                }
                Approval approvalTwo = new Approval();
                approvalTwo.setTypeId("20");
                approvalTwo.setCompany(new Company(CompanyUtil.getInstance().getFactId()));
                approvalTwo.setDataId(detail.getRecordId());
                Integer numTwo = approvalDao.checkIsApproved(approvalTwo);
                if (numTwo > 0)
                {
                    data.put("result", "fail");
                    data.put("message", "加急审批正在审批中!");
                    return data;
                }
            }
        }
        // 验证客户亏损额度是否达到
        if(null != contract.getCustomer())
        {
            LossOrders lossOrders = new LossOrders();
            lossOrders.setCustomerId(contract.getCustomer().getRecordId());
            lossOrders.setCompany(contract.getCompany());
            lossOrders = customerDao.getLossOrders(lossOrders);
            // 验证客户是否可亏损下单
            if (null != lossOrders)
            {
                Date finishLossTime = lossOrders.getFinishLossTime();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String finishLossDate = dateFormat.format(finishLossTime);
                // 获取当前时间
                Date currentDate = new Date();
                SimpleDateFormat date = new SimpleDateFormat("yyyy-MM-dd");
                String currentTime = date.format(currentDate);
                if (finishLossDate.compareTo(currentTime) < 0)
                {
                    DictValue checkValue = dictValueDao.getEffectiveData(contract.getCustomer().getRecordId());
                    if(null != checkValue && null != checkValue.getLossLimit())
                    {
                        checkValue.setCustomerId(contract.getCustomer().getRecordId());
                        checkValue.setContractId(contract.getRecordId());
                        // 获取当前订单是否亏损
                        BigDecimal lossLimit = contractDao.getLossLimitData(checkValue);
                        if(lossLimit.compareTo(BigDecimal.ZERO) < 0)
                        {
                            // 获取客户有效时间段的亏损额度
                            checkValue.setContractId(null);
                            lossLimit = contractDao.getLossLimitData(checkValue);
                            if(lossLimit.compareTo(BigDecimal.ZERO) < 0)
                            {
                                lossLimit = lossLimit.multiply(new BigDecimal(-1));
                                if(lossLimit.compareTo(new BigDecimal(checkValue.getLossLimit())) > 0)
                                {
                                    data.put("result", "fail");
                                    data.put("message", "客户类型亏损额度"+lossLimit+"已经超过系统设置亏损额度"+checkValue.getLossLimit());
                                    return data;
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                DictValue checkValue = dictValueDao.getEffectiveData(contract.getCustomer().getRecordId());
                if(null != checkValue && null != checkValue.getLossLimit())
                {
                    checkValue.setCustomerId(contract.getCustomer().getRecordId());
                    checkValue.setContractId(contract.getRecordId());
                    // 获取当前订单是否亏损
                    BigDecimal lossLimit = contractDao.getLossLimitData(checkValue);
                    if(lossLimit.compareTo(BigDecimal.ZERO) < 0)
                    {
                        // 获取客户有效时间段的亏损额度
                        checkValue.setContractId(null);
                        lossLimit = contractDao.getLossLimitData(checkValue);
                        if(lossLimit.compareTo(BigDecimal.ZERO) < 0)
                        {
                            lossLimit = lossLimit.multiply(new BigDecimal(-1));
                            if(lossLimit.compareTo(new BigDecimal(checkValue.getLossLimit())) > 0)
                            {
                                data.put("result", "fail");
                                data.put("message", "客户类型亏损额度"+lossLimit+"已经超过系统设置亏损额度"+checkValue.getLossLimit());
                                return data;
                            }
                        }
                    }
                }
            }
        }
        // 验证合同中的合同明细是否有确认单未确认
        Integer confirmNum = maxBatchAreaConfirmDao.getCountByContract(contract);
        if(null != confirmNum && confirmNum > 0)
        {
            data.put("result", "fail");
            data.put("message", "合同中有订单明细的借用产能确认到还没有确认完，不能进行提交审批!");
            return data;
        }

        boolean checkOneFlag = false;
        boolean checkTwoFlag = false;
        boolean checkThreeFlag = false;
        for (ContractDetail detail : details)
        {
            if (StringUtils.isBlank(detail.getGroupCenterId()))
            {
                checkOneFlag = true;
                break;
            }
/*            if (StringUtils.isBlank(detail.getMadeSupplierId()))
            {
                // 暂时限制不是江西工厂的话
                if (!com.getRecordId().equals(CompanyUtil.getInstance().getFactId()))
                {
                    checkTwoFlag = true;
                    break;
                }
            }*/
            // 如果返单生产厂编包含S直接返回
/*            num = contractDetailDao.checkDetailOrderTypeCount(detail);
            if (null != num && num > 0)
            {
                String craftNo = null;
                if (StringUtils.isNotBlank(detail.getNotiCraftNo()))
                {
                    ContractCraft query = new ContractCraft();
                    query.setRecordId(detail.getNotiCraftNo());
                    ContractCraft craft = contractCraftDao.get(query);
                    if (null != craft && StringUtils.isNotBlank(craft.getNo()))
                    {
                        craftNo = craft.getNo();
                    }
                }
                else if (StringUtils.isNotBlank(detail.getGenCraftNo()))
                {
                    craftNo = detail.getGenCraftNo().trim();
                }
                if (StringUtils.isNotBlank(craftNo))
                {
                    craftNo = craftNo.substring(1, craftNo.length() - 1);
                    if (craftNo.toLowerCase().contains("s".toLowerCase()))
                    {
                        checkThreeFlag = true;
                        break;
                    }
                }
            }*/
        }
        if (checkOneFlag)
        {
            data.put("result", "fail");
            data.put("message", "合同明细已评估的一键集控不能为空");
            return data;
        }
        if (checkTwoFlag)
        {
            data.put("result", "fail");
            data.put("message", "合同明细外发供应商不能为空!");
            return data;
        }
        if (checkThreeFlag)
        {
            data.put("result", "fail");
            data.put("message", "返单生产、新单量产的厂编不能是含S的样品厂编");
            return data;
        }
        //判断客户账户金额是否足够下单
        Contract customerBill = contractDao.getPayWayTotal(contract);
        if (customerBill.getPayWayValue().equals("现金"))
        {
            //查询客户审批中的金额
           BigDecimal contractWait = customerDao.getCustomerMoney(contract.getCustomer().getRecordId());
           if (contractWait == null)
           {
               contractWait = BigDecimal.ZERO;
           }
           //获取客户账户余额
            BigDecimal customBlance = contractDao.getBalanceDate(contract.getCustomer().getRecordId());
            if (customBlance == null)
            {
                data.put("result", "fail");
                data.put("message", "客户余额不足，请充值!");
                return data;
            }else{
                if ((customBlance.subtract(contractWait).subtract(customerBill.getTotalAmt())).compareTo(new BigDecimal(0)) < 0)
                {
                    data.put("result", "fail");
                    data.put("message", "客户余额不足，请充值!");
                    return data;
                }
            }
        }
        data = orderApproval(contract);
        return data;
    }

    public Map<String, Object> commitReflashAudit(Contract contract)
    {
        Map<String, Object> data = new HashMap<>();
        Company company = UserUtils.getUser().getCompany();
        contract.setCompany(company);
        boolean con = ModifyUtils.allocationExist("22", contract.getCompany());
        if (con)
        {
            contract.setFactoryComId(CompanyUtil.getInstance().getFactId());
            List<ContractDetail> conDetailList =
                contractDetailDao.getDeailByContractId(contract.getRecordId(), "22", contract);
            Boolean sampleFlag = true;
            if (Collections3.isNotEmpty(conDetailList))
            {
                String conDetailIds = null;
                String groupCenterIds = null;
                for (ContractDetail contractDetail : conDetailList)
                {
                    if (StringUtils.isNotBlank(conDetailIds))
                    {
                        conDetailIds = conDetailIds + "," + contractDetail.getRecordId();
                    }
                    else
                    {
                        conDetailIds = contractDetail.getRecordId();
                    }
                    if (StringUtils.isNotBlank(contractDetail.getGroupCenterId()))
                    {
                        if (StringUtils.isNotBlank(groupCenterIds))
                        {
                            groupCenterIds = groupCenterIds + "," + contractDetail.getGroupCenterId();
                        }
                        else
                        {
                            groupCenterIds = contractDetail.getGroupCenterId();
                        }
                    }
                    if(StringUtils.isNotBlank(contractDetail.getDictOrderType()) && !"1".equals(contractDetail.getDictOrderType()))
                    {
                        sampleFlag = false;
                    }
                }

                // 验证只能有销售公司反审
                Company saleCom = companyDao.getsaleComByGroupCenterId(groupCenterIds);
                if(null != saleCom && !saleCom.getRecordId().equals(company.getRecordId()))
                {
                    data.put("result", "fail");
                    data.put("message", "在"+saleCom.getName() + "才能反审!");
                    return data;
                }

                // 验证当前公司是否已做送货单
                Integer countOne = deliveryDetailDao.checkDetailCount(conDetailIds);
                if (null != countOne && countOne > 0)
                {
                    data.put("result", "fail");
                    data.put("message", company.getName() + "已做送货单不能反审!");
                    return data;
                }

                if (StringUtils.isNotBlank(groupCenterIds))
                {
                    String ecoemyId = CompanyUtil.getInstance().getEcoemyIdTwo(1, contract.getRecordId());
                    Company ecoemyCom = companyDao.get(ecoemyId);
                    // 验证龙南领德是否已做送货单
                    Integer countTwo = deliveryDetailDao.checkDetailCountTwo(groupCenterIds, ecoemyId);
                    if (null != countTwo && countTwo > 0)
                    {
                        data.put("result", "fail");
                        if (null != ecoemyCom)
                        {
                            data.put("message", ecoemyCom.getName() + "已做送货单不能反审!");
                        }
                        else
                        {
                            data.put("message", "龙南领德公司已做送货单不能反审!");
                        }
                        return data;
                    }

                    // 验证江西领德辉是否已投料出库
                    String factoryComId = CompanyUtil.getInstance().getFactId();
                    Integer countThree = feedingDao.checkFeedOutCount(groupCenterIds, factoryComId);
                    if (null != countThree && countThree > 0)
                    {
                        data.put("result", "fail");
                        data.put("message", "工厂已经投料出库,不能作废!");
                        return data;
                    }
                    // 验证江西领德辉是否已做投料单
                    Integer countFour = feedingDao.checkFeedOutCountThree(groupCenterIds, factoryComId);
                    if (null != countFour && countFour > 0)
                    {
                        data.put("result", "fail");
                        data.put("message", "工厂已经做了投料单，请通知计划删除投料单后再操作!");
                        return data;
                    }
                    // 验证江西领德辉是否已做工程卡
                    Integer countFive = notificationDao.checkDetailCount(groupCenterIds, factoryComId);
                    if (null != countFive && countFive > 0)
                    {
                        data.put("result", "fail");
                        data.put("message", "工厂已制作工程卡，请通知工程删除工程资料再操作!");
                        return data;
                    }
                }
            }

            if(!sampleFlag)
            {
                String result = ModifyUtils.reflashApproval(contract);
                if (!"fail".equals(result))
                {
                    data.put("result", "success");
                    data.put("message", result);
                    return data;
                }
            }

            // 合同状态改为未确认
            contract.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
            contractDao.updateStatus(contract);

            ContractDetail contractDetail = new ContractDetail();
            contractDetail.setContractId(contract.getRecordId());
            contractDetail.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
            contractDetailDao.updateStatusByContractId(contractDetail);

            // 删除生成的数据
            ChangeDataUtils changeDataUtils = new ChangeDataUtils();
            Approval approval = new Approval();
            approval.setCustomerType(TypeKey.APPROVAL_CONTRACT_REFLASH.toString());
            approval.setDataId(contract.getRecordId());
            changeDataUtils.deleteGenConrtactData(approval, company);
            changeDataUtils.computerCustomBill("1","2",contractDetail.getContractId(),"","");
            data.put("result", "success");
            data.put("message", "订单反审成功!");
            return data;
        }
        else
        {
            data.put("result", "fail");
            data.put("message", "请先配置合同反审审批!");
        }
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> deleteContract(Contract contract)
    {
        Map<String, Object> data = new HashMap<>();
        if (null == contract)
        {
            data.put("result", "fail");
            data.put("message", "数据异常，请刷新重试!");
            return data;
        }
        contract.setCompany(UserUtils.getUser().getCompany());
        Contract con = contractDao.get(contract);
        if (!TypeKey.SL_CONTRACT_STATUS_DRAFT.toString().equals(con.getStatus()))
        {
            data.put("result", "fail");
            data.put("message", "订单状态已经改变，请刷新重试!");
            return data;
        }
        // 验证合同明细状态
        Integer checkCount = contractDetailDao.getUnConfirmCount(contract);
        if (null != checkCount && checkCount > 0)
        {
            data.put("result", "fail");
            data.put("message", "订单明细状态已经改变，不能删除!");
            return data;
        }
        List<String> contractdetailIds = contractDetailDao.getContractDetail(contract);
        ContractDetail contractDetail = new ContractDetail();
        for(String contractdetailId : contractdetailIds)
        {
            contractDetail.setRecordId(contractdetailId);
            if (StringUtils.isNotBlank(contractdetailId))
            {
                CockpitUtilsOne cockpitUtilsOne = new CockpitUtilsOne();
                cockpitUtilsOne.addOperatingRecord(1,contractDetail,2);
            }
        }
        contractDao.delete(contract);
        contractDetailDao.deleteDetailByConId(contract);
        // 删除出库存记录
        productStocksUseDao.deleteByContractId(contract.getRecordId());
        data.put("result", "success");
        data.put("message", "删除成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public String deleteContractDetail(ContractDetail contractDetail)
    {
        contractDetailDao.delete(contractDetail);
        return "success";
    }

    @Transactional(readOnly = false)
    public String clearGroupCenter(ContractDetail contractDetail)
    {
        contractDetailDao.delGroupCenter(contractDetail);
        contractDetailDao.delPurchConcat(contractDetail);
        // 清除备料使用记录
        if (null != contractDetail.getGroupCenterId() && StringUtils.isNotBlank(contractDetail.getGroupCenterId()))
        {
            MaterialArea materialArea = new MaterialArea();
            materialArea.setGroupCenterId(contractDetail.getGroupCenterId());
            List<MaterialArea> materialAreaList = materialAreaDetailDao.getMaterialAreaLists(materialArea);
            if (Collections3.isNotEmpty(materialAreaList))
            {
                for (MaterialArea area : materialAreaList)
                {
                    if (StringUtils.isNotBlank(area.getRecordId()))
                    {
                        MaterialAreaDetail materialAreaDetail = new MaterialAreaDetail();
                        materialAreaDetail.setMaterialAreaId(area.getRecordId());
                        materialAreaDetailDao.updateDetailGroupCenter(materialAreaDetail);
                    }
                }
            }
            materialAreaDetailDao.updateGroupCenter(contractDetail);
        }
        contractDetail.setGroupCenterId(null);
        contractDetailDao.updateGroupCenterId(contractDetail);

        // 如果是工厂的并且是新单的要清除生产编号
        Company company = UserUtils.getUser().getCompany();
        String factId = CompanyUtil.getInstance().getFactId();
        if (null != company && StringUtils.isNotBlank(factId) && company.getRecordId().equals(factId))
        {
            contractCraftDao.updateCraftNoNull(contractDetail);
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public Map<String, Object> saveSplitList(ContractDetail contractDetail)
    {
        Map<String, Object> result = new HashMap<>();
        if (null == contractDetail || StringUtils.isBlank(contractDetail.getRecordId()))
        {
            result.put("result", "fail");
            result.put("message", "数据错误，请刷新重试");
            return result;
        }
        if (StringUtils.isBlank(contractDetail.getInsertQuantitys()))
        {
            result.put("result", "fail");
            result.put("message", "拆分数量不能为空");
            return result;
        }
        List<ContractDetail> contractDetailList = Lists.newArrayList();
        ContractDetail cd = contractDetailDao.getSplitDetialData(contractDetail);
        String[] insertQuantitys = contractDetail.getInsertQuantitys().split(",");
        Integer sumQty = 0;
        BigDecimal subTotal = BigDecimal.ZERO;
        BigDecimal price = BigDecimal.ZERO;
        if (null != cd.getPricees() && null != cd.getPricees().getPrice())
        {
            price = cd.getPricees().getPrice();
        }
        for (int i = 0; i < insertQuantitys.length; i++)
        {
            ContractDetail detail = contractDetail.clone();
            if (StringUtils.isNotBlank(cd.getGroupCenterId()) && cd.getMadeSupplierName().indexOf("江西领德辉") == -1)
            {
                detail.setGroupCenterId(cd.getGroupCenterId());
            }
            else
            {
                detail.setGroupCenterId(null);
            }
            subTotal = price.multiply(new BigDecimal(insertQuantitys[i]));
            detail.setSubTotal(subTotal);
            detail.setQuantity(insertQuantitys[i]);
            detail.setSplitDetailId(cd.getRecordId());
            contractDetailList.add(detail);
            if (StringUtils.isNotBlank(insertQuantitys[i]))
            {
                sumQty += Integer.valueOf(insertQuantitys[i]);
            }
        }
        Integer quantity =
            StringUtils.isNotBlank(contractDetail.getQuantity()) ? Integer.valueOf(contractDetail.getQuantity()) : 0;
        Integer updateQty = quantity - sumQty;
        if (updateQty <= 0)
        {
            result.put("result", "fail");
            result.put("message", "拆分数量不能超过订单数量");
            return result;
        }

        if (StringUtils.isNotBlank(cd.getGroupCenterId()))
        {
            GroupCenter groupCenter = new GroupCenter();
            groupCenter.setRecordId(cd.getGroupCenterId());
            List<ContractDetail> cdList = updateDataDao.getContDetailList(groupCenter);
            if (Collections3.isNotEmpty(cdList))
            {
                for (ContractDetail conDetail : cdList)
                {
                    // 更新合同明细数量和金额
                    if (null != conDetail.getPricees() && null != conDetail.getPricees().getPrice())
                    {
                        price = conDetail.getPricees().getPrice();
                    }
                    else
                    {
                        price = BigDecimal.ZERO;
                    }
                    subTotal = price.multiply(new BigDecimal(updateQty));
                    BigDecimal orderDeailArea = conDetail.getPcsArea().multiply(new BigDecimal(updateQty));
                    if (null != conDetail.getWrongComputAmount())
                    {
                        subTotal = subTotal.add(conDetail.getWrongComputAmount());
                    }
                    conDetail.setQuantity(updateQty.toString());
                    conDetail.setSubTotal(subTotal);
                    conDetail.setOrderDeailArea(orderDeailArea.toString());
                    contractDetailDao.updateThreeData(conDetail);
                }
            }
        }
        else
        {
            // 更新合同明细数量、金额和面积
            subTotal = price.multiply(new BigDecimal(updateQty));
            BigDecimal orderDeailArea = cd.getPcsArea().multiply(new BigDecimal(updateQty));
            if (null != cd.getWrongComputAmount())
            {
                subTotal = subTotal.add(cd.getWrongComputAmount());
            }
            cd.setQuantity(updateQty.toString());
            cd.setSubTotal(subTotal);
            cd.setOrderDeailArea(orderDeailArea.toString());
            contractDetailDao.updateThreeData(cd);
        }

        // 批量添加合同明细
        contractDetailDao.batchInsertDetail(contractDetailList);

        // 获取批量插入的第一个id
        String recordId = produceBatchDetailDao.getRecordId();

        if (StringUtils.isNotBlank(recordId))
        {
            Integer id = Integer.valueOf(recordId) - 1;
            for (ContractDetail detail : contractDetailList)
            {
                id++;
                detail.setRecordId(id.toString());
            }
        }
        for (ContractDetail detail : contractDetailList)
        {
            if (StringUtils.isNotBlank(detail.getGroupCenterId()))
            {
                detail.setGroupCenterId(detail.getRecordId());
                contractDetailDao.updateGroupCenterId(detail);
            }
        }

        result.put("result", "success");
        result.put("message", "保存数据成功!");
        return result;
    }

    public Map<String, Object> genSplitDetailData(List<ContractDetail> contractDetailList)
    {
        Map<String, Object> data = new HashMap<>();
        if (Collections3.isEmpty(contractDetailList))
        {
            data.put("result", "fail");
            data.put("message", "数据错误，请刷新重试");
            return data;
        }
        if (null == contractDetailList.get(0).getContract() || StringUtils.isBlank(contractDetailList.get(0)
            .getContract()
            .getRecordId()))
        {
            data.put("result", "fail");
            data.put("message", "数据错误，请刷新重试");
            return data;
        }
        List<ContractDetail> useDetailList = Lists.newArrayList();
        String contractId = contractDetailList.get(0).getContract().getRecordId();
        Contract contractTwo = contractDetailList.get(0).getContract();
        contractTwo.setFactoryComId(CompanyUtil.getInstance().getFactId());
        List<ContractDetail> cdList = contractDetailDao.getDeailByContractId(contractId, null, contractTwo);
        if (Collections3.isNotEmpty(cdList))
        {
            for (ContractDetail cd : cdList)
            {
                if (StringUtils.isNotBlank(cd.getSplitDetailId()) && !cd.getGenNotiFlag())
                {
                    useDetailList.add(cd);
                }
            }
        }

        if (Collections3.isEmpty(useDetailList))
        {
            data.put("result", "fail");
            data.put("message", "当前没有可以提交的明细");
            return data;
        }

        Company company = UserUtils.getUser().getCompany();
        ChangeDataUtils dataUtils = new ChangeDataUtils();
        String result = null;
        // 组合所有要处理的工艺，获取所有该公司相关工艺
        String ids = "";
        Boolean checkFlag = false;
        String detailIds = null;
        for (int i = 0; i < useDetailList.size(); i++)
        {
            if (StringUtils.isNotBlank(useDetailList.get(i).getMadeSupplierId()))
            {
                if (StringUtils.isBlank(useDetailList.get(i).getGroupCenterId()))
                {
                    checkFlag = true;
                    break;
                }
                if (StringUtils.isNotBlank(ids))
                {
                    ids = ids + "," + dataUtils.getNeedDealData(useDetailList.get(i));
                }
                else
                {
                    ids = dataUtils.getNeedDealData(useDetailList.get(i));
                }
            }
            if (StringUtils.isNotBlank(detailIds))
            {
                detailIds = detailIds + "," + useDetailList.get(i).getRecordId();
            }
            else
            {
                detailIds = useDetailList.get(i).getRecordId();
            }
        }
        if (checkFlag)
        {
            data.put("result", "fail");
            data.put("message", "外发供应商必须有一键集控");
            return data;
        }

        String comIds = "";
        List<DictValue> valList = new ArrayList<DictValue>();
        Map<String, String> map = new HashMap<String, String>();
        if (StringUtils.isNotBlank(ids))
        {
            comIds = CompanyUtil.getInstance().getEcoemyId() + "," + CompanyUtil.getInstance().getFactId() + ","
                + company.getRecordId();
            valList = contractDetailDao.getAllValue(comIds, ids);
            map = dataUtils.getMapDatas(valList);
        }

        // 龙南公司的合同明细
        List<ContractDetail> lnDeailList = new ArrayList<ContractDetail>();
        // 江西公司的合同明细
        List<ContractDetail> jxDeailList = new ArrayList<ContractDetail>();

        for (int i = 0; i < useDetailList.size(); i++)
        {
            if (StringUtils.isNotBlank(useDetailList.get(i).getMadeSupplierId()))
            {

                if (StringUtils.isNotBlank(useDetailList.get(i).getMadeSupplierName())
                    && useDetailList.get(i).getMadeSupplierName().indexOf("江西领德辉") != -1)
                {
                    // 江西合同
                    ContractDetail jxContractDetail = useDetailList.get(i).clone();
                    ContractCraft craft = useDetailList.get(i).getContractCraftList().clone();
                    jxContractDetail.setContractCraftList(craft);
                    Contract contract = useDetailList.get(i).getContract().clone();
                    jxContractDetail.setContract(contract);
                    // 进行工艺转换
                    result = dataUtils.trainCondeail(valList,
                        jxContractDetail,
                        CompanyUtil.getInstance().getFactId(),
                        map,
                        company.getRecordId());
                    if (StringUtils.isNotBlank(result))
                    {
                        break;
                    }
                    jxDeailList.add(jxContractDetail);
                }

                // 龙南合同
                ContractDetail lnContractDetail = useDetailList.get(i).clone();
                ContractCraft craft = useDetailList.get(i).getContractCraftList().clone();
                lnContractDetail.setContractCraftList(craft);
                Contract contract = useDetailList.get(i).getContract().clone();
                lnContractDetail.setContract(contract);
                if (StringUtils.isBlank(lnContractDetail.getGroupCenterId()))
                {
                    lnContractDetail.setGroupCenterId(useDetailList.get(i).getRecordId());
                }
                // 进行工艺转换
                result = dataUtils.trainCondeail(valList,
                    lnContractDetail,
                    CompanyUtil.getInstance().getEcoemyId(),
                    map,
                    company.getRecordId());
                if (StringUtils.isNotBlank(result))
                {
                    break;
                }
                lnDeailList.add(lnContractDetail);
            }
        }
        if (StringUtils.isNotBlank(result))
        {
            data.put("result", "fail");
            data.put("message", result);
            return data;
        }

        // 生成本公司的通知单
        dataUtils.setNoticationData(useDetailList, company, null, null, UserUtils.getUser(), 1);

        ContractDetail cdQuery = new ContractDetail();
        cdQuery.setRecordId(detailIds);
        List<GroupCenter> groupCenterList = contractDetailDao.getGroupCenterList(cdQuery);

        // 龙南领德辉数据生成
        dataUtils.genCompanyDataTwo(lnDeailList, CompanyUtil.getInstance().getEcoemyId(), company, groupCenterList);
        // 江西领德辉数据生成
        dataUtils.genCompanyDataTwo(jxDeailList, CompanyUtil.getInstance().getFactId(), company, groupCenterList);

        data.put("result", "success");
        data.put("message", "提交成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public String updateContract(Contract contract)
    {
        contractDao.update(contract);
        batchDeliveryDao.updateDeliveryDateByConDetId(contract);
        return "success";
    }

    public Map<String, Object> detailReflashAudit(ContractDetail contractDetail)
    {
        Map<String, Object> data = new HashMap<>();
        if (null == contractDetail || StringUtils.isBlank(contractDetail.getRecordId()))
        {
            data.put("result", "fail");
            data.put("message", "数据错误，请联系管理员!");
            return data;
        }
        Company company = UserUtils.getUser().getCompany();
        contractDetail.setCompany(company);
        // 查询是否提交通知单作废(合同反审)
        Notification noti = notificationDao.getNotiByDetail(contractDetail);
        if (null != noti && TypeKey.APPROVAL_STATUS_WAIT.toString().equals(noti.getStatus()))
        {
            data.put("result", "fail");
            data.put("message", "订单明细对应的通知单正在审批中!");
            return data;
        }

        boolean con = ModifyUtils.allocationExist("22", contractDetail.getCompany());
        if (con)
        {
            String conDetailIds = contractDetail.getRecordId();
            String groupCenterIds = contractDetail.getGroupCenterId();

            // 验证当前公司是否已做送货单
            Integer countOne = deliveryDetailDao.checkDetailCount(conDetailIds);
            if (null != countOne && countOne > 0)
            {
                data.put("result", "fail");
                data.put("message", company.getName() + "已做送货单不能反审!");
                return data;
            }

            if (StringUtils.isNotBlank(groupCenterIds))
            {
                String ecoemyId = CompanyUtil.getInstance().getEcoemyId();
                Company ecoemyCom = companyDao.get(ecoemyId);
                // 验证龙南领德是否已做送货单
                Integer countTwo = deliveryDetailDao.checkDetailCountTwo(groupCenterIds, ecoemyId);
                if (null != countTwo && countTwo > 0)
                {
                    data.put("result", "fail");
                    if (null != ecoemyCom)
                    {
                        data.put("message", ecoemyCom.getName() + "已做送货单不能反审!");
                    }
                    else
                    {
                        data.put("message", "龙南领德公司已做送货单不能反审!");
                    }
                    return data;
                }

                // 验证江西领德辉是否已投料出库
                String factoryComId = CompanyUtil.getInstance().getFactId();
                Integer countThree = feedingDao.checkFeedOutCount(groupCenterIds, factoryComId);
                if (null != countThree && countThree > 0)
                {
                    data.put("result", "fail");
                    data.put("message", "工厂已经投料出库,不能作废!");
                    return data;
                }
                // 验证江西领德辉是否已做投料单
                Integer countFour = feedingDao.checkFeedOutCountThree(groupCenterIds, factoryComId);
                if (null != countFour && countFour > 0)
                {
                    data.put("result", "fail");
                    data.put("message", "工厂已经做了投料单，请通知计划删除投料单后再操作!");
                    return data;
                }
                // 验证江西领德辉是否已做工程卡
                Integer countFive = notificationDao.checkDetailCount(groupCenterIds, factoryComId);
                if (null != countFive && countFive > 0)
                {
                    data.put("result", "fail");
                    data.put("message", "工厂已制作工程卡，请通知工程删除工程资料再操作!");
                    return data;
                }
            }

            String result = ModifyUtils.reflashApproval(contractDetail);
            if (!"fail".equals(result))
            {
                data.put("result", "success");
                data.put("message", result);
                return data;
            }

            // 获取订单中是否有审批中的订单明细
            Integer count = contractDetailDao.getApprovalDetailCount(contractDetail);
            if (null == count || count == 0)
            {
                ContractDetail cd = contractDetailDao.get(contractDetail);
                Contract contract = cd.getContract();
                contract.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
                contractDao.updateStatus(contract);
            }

            contractDetail.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
            contractDetailDao.updateStatus(contractDetail);

            ChangeDataUtils changeDataUtils = new ChangeDataUtils();
            Approval approval = new Approval();
            approval.setCustomerType(TypeKey.APPROVAL_CONTRACTDETAIL_REFLASH.toString());
            approval.setDataDetailId(contractDetail.getRecordId());
            changeDataUtils.deleteGenConrtactData(approval, company);
            data.put("result", "success");
            data.put("message", "订单明细反审成功!");
            return data;
        }
        else
        {
            data.put("result", "fail");
            data.put("message", "请先配置合同反审审批!");
        }
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> addFeeApproval(ContractDetail contractDetail)
    {
        Map<String, Object> result = new HashMap<>();
        if (null == contractDetail || StringUtils.isBlank(contractDetail.getRecordId()))
        {
            result.put("result", "fail");
            result.put("message", "数据错误，请联系管理员");
            return result;
        }
        // 验证明细是否在审批中
        ContractDetail cd = contractDetailDao.get(contractDetail);
        if (null != cd && TypeKey.SL_CONTRACT_STATUS_WAIT.toString().equals(cd.getStatus()))
        {
            result.put("result", "fail");
            result.put("message", "订单明细已经在审批中，请刷新重试");
            return result;
        }
        if (null == contractDetail.getAuditRemark() || StringUtils.isBlank(contractDetail.getAuditRemark()))
        {
            result.put("result", "fail");
            result.put("message", "请填写调整原因后再提交!");
            return result;
        }
        ContractDetail contractObj = contractDetailDao.getContractDataGroup(contractDetail);
        contractDetail.setMaterialFee(contractObj.getMaterialFee());
        contractDetail.setProcessFee(contractObj.getProcessFee());
        //计算采购费
        BigDecimal prdAmount = BigDecimal.ZERO;
        BigDecimal prdAmountTwo = BigDecimal.ZERO;
        BigDecimal costFee = BigDecimal.ZERO;
        BigDecimal grossProfitRate = BigDecimal.ZERO;
        BigDecimal netProfitRate = BigDecimal.ZERO;
        BigDecimal onePrdEngineeringFee = BigDecimal.ZERO;
        BigDecimal twoPrdMouldFee = BigDecimal.ZERO;
        BigDecimal threePrdTestShelfFee = BigDecimal.ZERO;
        BigDecimal fourPrdFilmFee = BigDecimal.ZERO;
        BigDecimal fivePrdOthersFee = BigDecimal.ZERO;

        BigDecimal onePrdEngineeringFeeA = BigDecimal.ZERO;
        BigDecimal twoPrdMouldFeeB = BigDecimal.ZERO;
        BigDecimal threePrdTestShelfFeeC = BigDecimal.ZERO;
        BigDecimal fourPrdFilmFeeD = BigDecimal.ZERO;
        BigDecimal fivePrdOthersFeeE = BigDecimal.ZERO;
        //采购变更后
        onePrdEngineeringFee = contractDetail.getPrdEngineeringFee() == null ? new BigDecimal(0):contractDetail.getPrdEngineeringFee();
        twoPrdMouldFee = contractDetail.getPrdMouldFee() == null ? new BigDecimal(0):contractDetail.getPrdMouldFee();
        threePrdTestShelfFee = contractDetail.getPrdTestShelfFee() == null ? new BigDecimal(0):contractDetail.getPrdTestShelfFee();
        fourPrdFilmFee = contractDetail.getPrdFilmFee() == null ? new BigDecimal(0):contractDetail.getPrdFilmFee();
        fivePrdOthersFee = contractDetail.getPrdOthersFee() == null? new BigDecimal(0):contractDetail.getPrdOthersFee();
        //采购变更前
        onePrdEngineeringFeeA = contractObj.getPrdEngineeringFee() == null ? new BigDecimal(0):contractObj.getPrdEngineeringFee();
        twoPrdMouldFeeB = contractObj.getPrdMouldFee() == null ? new BigDecimal(0):contractObj.getPrdMouldFee();
        threePrdTestShelfFeeC  = contractObj.getPrdTestShelfFee() == null ? new BigDecimal(0):contractObj.getPrdTestShelfFee();
        fourPrdFilmFeeD = contractObj.getPrdFilmFee() == null ? new BigDecimal(0):contractObj.getPrdFilmFee();
        fivePrdOthersFeeE = contractObj.getPrdOthersFee() == null? new BigDecimal(0):contractObj.getPrdOthersFee();

        if(StringUtils.isNotBlank(contractDetail.getValueOrderType()) && contractDetail.getValueOrderType().equals("1") && !contractDetail.getRecordId().equals( contractDetail.getGroupCenterId())){
            onePrdEngineeringFee = BigDecimal.ZERO;
            onePrdEngineeringFeeA = BigDecimal.ZERO;
        }
        prdAmount = twoPrdMouldFee.add(threePrdTestShelfFee).add(fourPrdFilmFee).add(fivePrdOthersFee).add(onePrdEngineeringFee);
        prdAmountTwo = twoPrdMouldFeeB.add(threePrdTestShelfFeeC).add(fourPrdFilmFeeD).add(fivePrdOthersFeeE).add(onePrdEngineeringFeeA);


        //变更前净利率
        BigDecimal netProfitRateBefor = BigDecimal.ZERO;
        BigDecimal costFeeTwo = BigDecimal.ZERO;

        if(null != prdAmountTwo){
            costFeeTwo = contractObj.getMaterialFee().add(contractObj.getProcessFee()).add(prdAmountTwo);
        }else{
            costFeeTwo = contractObj.getMaterialFee().add(contractObj.getProcessFee());
        }
        //计算成本费
        costFee = contractObj.getMaterialFee().add(contractObj.getProcessFee()).add(prdAmount);

        BigDecimal saleMoney = contractDetail.getSubTotal();
        BigDecimal saleMoneyTwo = contractDetail.getSubTotal();
        if(saleMoneyTwo.equals(new BigDecimal(0))){
            saleMoneyTwo = new BigDecimal(1);
        }
        if(contractDetail.getRecordId().equals(contractDetail.getGroupCenterId())){
            if(null != prdAmountTwo){
                costFeeTwo = contractDetail.getMadePcsPrice().multiply(BigDecimal.valueOf(contractDetail.getPrdQuantity())).add(prdAmountTwo);
            }else{
                costFeeTwo = contractDetail.getMadePcsPrice().multiply(BigDecimal.valueOf(contractDetail.getPrdQuantity()));
            }

            costFee = contractDetail.getMadePcsPrice().multiply(BigDecimal.valueOf(contractDetail.getPrdQuantity())).add(prdAmount);

        }
        //计算毛利率
        if (null != saleMoney && saleMoney.compareTo(BigDecimal.ZERO) != 0)
        {
            grossProfitRate = (saleMoney.subtract(costFee)).divide(saleMoneyTwo,4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        }
        //计算净利率
        BigDecimal saleFee = contractDetail.getSaleFee();
        BigDecimal manageFee = contractDetail.getManageFee();

        if (null != saleMoneyTwo && saleMoneyTwo.compareTo(BigDecimal.ZERO) != 0)
        {
            netProfitRateBefor = (contractObj.getSubTotal().subtract(costFeeTwo.add(saleFee).add(manageFee))).divide(saleMoneyTwo,4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        }

        contractDetail.setNetProfitRateAfter(netProfitRateBefor);

        if (null != saleMoney && saleMoney.compareTo(BigDecimal.ZERO) != 0)
        {
            netProfitRate = (saleMoney.subtract(costFee.add(saleFee).add(manageFee))).divide(saleMoneyTwo,4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        }
        contractDetail.setGrossProfitRate(grossProfitRate);
        contractDetail.setNetProfitRate(netProfitRate);
        contractDetail.setPrdAmountTwo(prdAmount);
        String str = ModifyUtils.contractDetailExpensesApproval(contractDetail);
        result.put("result", "success");
        result.put("message", str);
        return result;
    }

    public Map<String, Object> copyRemark(Contract contract)
    {
        Map<String, Object> data = new HashMap<>();
        contractDetailDao.copyRemark(contract);
        data.put("result", "success");
        data.put("message", "一键赋值备注完成!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> saveContractDetailAddress(ContractDetail contractDetail)
    {
        Map<String, Object> result = new HashMap<String, Object>();
        // 验证1、合同的状态，必须是修改状态
        /*
         * Contract contract =
         * contractDetailDao.contracIsUpdateStatus(contractDetail.getRecordId(),TypeKey.SL_CONTRACT_STATUS_DRAFT);
         * if(null==contract){ result.put("result", "fail"); result.put("message", "合同状态不是新增，不能更新明细地址"); return result;
         * }
         */
        // 验证2、每个地址的数量大于零，相加等于明细总数量;送货顺序不相等，大于零;送货地址、电话、名称必须都有值；
        checkAddressInfo(contractDetail, result);
        if (null != result.get("message"))
        {
            return result;
        }
        List<ContractDetailAddress> commitAddressList = contractDetail.getAddressList();
        // 重新查下数据库；已出货数量不能小于地址总数量;已出完的不能修改;已出货的出货顺序也不能修改，未出货的出货顺序必须大于部分出货和已出货
        List<ContractDetailAddress> oldInfo = contractDetailDao.findAddressList(contractDetail.getRecordId());
        List<ContractDetailAddress> updateInfo = new ArrayList<ContractDetailAddress>();
        int sortNum = -1;
        boolean isExist = true;
        boolean hasOutAddress = false;
        Set<String> finishAddress = new HashSet<String>();
        if (!CollectionUtils.isEmpty(oldInfo))
        {
            for (ContractDetailAddress old : oldInfo)
            {
                if (null != old.getIsFinish() && old.getIsFinish() == 1)
                {
                    // 已经完成的出货的;必须存在，比较联系人、顺序、数量;
                    if (!CollectionUtils.isEmpty(commitAddressList))
                    {
                        isExist = false;
                        for (ContractDetailAddress cc : commitAddressList)
                        {
                            if (old.getContacterId().equals(cc.getContacterId()))
                            {
                                isExist = true;
                                if (old.getSortNum() != cc.getSortNum() || old.getQuantity() != cc.getQuantity())
                                {
                                    result.put("result", "fail");
                                    result.put("message",
                                        "收货地址：" + old.getContractAdress() + "数量：" + old.getQuantity()
                                            + "已经全部出货完成，不能修改");
                                    return result;
                                }
                            }
                        }
                        if (!isExist)
                        {
                            result.put("result", "fail");
                            result.put("message",
                                "收货地址：" + old.getContractAdress() + "已经出货" + old.getOutQuantity() + "，不能删除");
                            return result;
                        }

                    }
                    else
                    {
                        result.put("result", "fail");
                        result.put("message",
                            "收货地址：" + old.getContractAdress() + "已经出货" + old.getOutQuantity() + "，不能删除");
                        return result;
                    }
                    if (sortNum < old.getSortNum())
                    {
                        sortNum = old.getSortNum();
                    }
                    finishAddress.add(old.getContacterId());
                    hasOutAddress = true;
                }
                else if (old.getOutQuantity() != null && old.getOutQuantity() > 0)
                {
                    // 出货顺序、联系人id不能边，且新数量不能小余已出货数量
                    isExist = false;
                    for (ContractDetailAddress cc : commitAddressList)
                    {
                        if (old.getContacterId().equals(cc.getContacterId()))
                        {
                            isExist = true;
                            if (old.getSortNum() != cc.getSortNum() || old.getOutQuantity() > cc.getQuantity())
                            {
                                result.put("result", "fail");
                                result.put("message",
                                    "收货地址：" + old.getContractAdress() + "数量：" + old.getQuantity()
                                        + "已经部份出货，不能修改出货顺序且出货量必须大于等于已出货数量");
                                return result;
                            }
                            else
                            {
                                cc.setOutQuantity(old.getOutQuantity());
                                cc.setFirstOutDate(old.getFirstOutDate());
                                if (old.getOutQuantity() == cc.getQuantity())
                                {
                                    cc.setIsFinish(1);
                                    cc.setFinishDate(new Date());
                                }
                            }
                        }
                    }
                    if (!isExist)
                    {
                        result.put("result", "fail");
                        result.put("message",
                            "收货地址：" + old.getContractAdress() + "已经出货" + old.getOutQuantity() + "，不能删除");
                        return result;
                    }
                    if (sortNum < old.getSortNum())
                    {
                        sortNum = old.getSortNum();
                    }
                    hasOutAddress = true;
                }
                /*
                 * else { result.put("result", "fail"); result.put("message",
                 * "收货地址："+old.getContractAdress()+"已经出货"+old.getOutQuantity()+"，不能删除"); return result; }
                 */
            }
            // 整理修改地址集合和验证排序号是否都大于等于部分出货的排序号
            for (ContractDetailAddress cc : commitAddressList)
            {
                if (!finishAddress.contains(cc.getContacterId()))
                {
                    if (hasOutAddress && cc.getSortNum() < sortNum)
                    {
                        result.put("result", "fail");
                        result.put("message", "未出货的收货地址，出货顺序必须大于已出货的出货顺序" + sortNum);
                        return result;
                    }
                    updateInfo.add(cc);
                }
            }
        }
        else
        {
            updateInfo = commitAddressList;
        }
        // 删除该明细已经存在的地址信息,不包括已完成出货的
        contractDetailDao.deleteDetatilAddress(contractDetail.getRecordId());
        // 增加新提交的信息
        if (!CollectionUtils.isEmpty(updateInfo))
        {
            contractDetailDao.batchAddAdress(updateInfo);
        }
        result.put("result", "success");
        result.put("message", "地址保存完成！");
        return result;
    }

    /**
     * 校验合同明细地址完整性
     *
     * @param contractDetail
     * @param result
     */
    private void checkAddressInfo(ContractDetail contractDetail, Map<String, Object> result)
    {
        if (!CollectionUtils.isEmpty(contractDetail.getAddressList()))
        {
            int detailQuantity = Integer.parseInt(contractDetail.getQuantity());
            int addressCountQuantity = 0;
            Set<String> contacterSet = new HashSet<String>();
            Set<Integer> sortNum = new HashSet<Integer>();
            User user = UserUtils.getUser();
            Company company = user.getCompany();
            for (ContractDetailAddress address : contractDetail.getAddressList())
            {
                if (null == address.getQuantity() || address.getQuantity() <= 0)
                {
                    result.put("result", "fail");
                    result.put("message", "送货地址数量需要大于零");
                    return;
                }
                else
                {
                    addressCountQuantity += address.getQuantity();
                }
                if (null == address.getContacter() || StringUtils.isEmpty(address.getContacter().getRecordId()))
                {
                    result.put("result", "fail");
                    result.put("message", "联系人不能为空");
                    return;
                }
                if (contacterSet.contains(address.getContacter().getRecordId()))
                {
                    result.put("result", "fail");
                    result.put("message", "联系人不能重复");
                    return;
                }
                else
                {
                    contacterSet.add(address.getContacter().getRecordId());
                    address.setContacterId(address.getContacter().getRecordId());
                    address.setContactName(address.getContacter().getName());
                    address.setContactPhone(address.getContacter().getPhone());
                    address.setContractAdress(address.getContacter().getAddress());
                }
                if (null == address.getSortNum() || address.getSortNum() <= 0)
                {
                    result.put("result", "fail");
                    result.put("message", "出货顺序不能为空，且大于0");
                    return;
                }
                if (sortNum.contains(address.getSortNum()))
                {
                    result.put("result", "fail");
                    result.put("message", "出货顺序不能重复");
                    return;
                }
                address.setCompany(company);
                address.setContractDetailId(contractDetail.getRecordId());
                address.setCreatedBy(user);
                address.setLastUpdBy(user);
                address.setStatus(0);
            }
            if (addressCountQuantity != detailQuantity)
            {
                result.put("result", "fail");
                result.put("message", "地址设置总数量:" + addressCountQuantity + ",明细数量：" + detailQuantity + "需要相等");
                return;
            }
        }

    }

    @Transactional(readOnly = false)
    public Map<String, Object> sentPrdorder(Prdorder prd)
    {
        Map<String, Object> data = new HashMap<>();
        if (null == prd || StringUtils.isBlank(prd.getContractIds()) || StringUtils.isBlank(prd.getSupplierId()))
        {
            data.put("result", "fail");
            data.put("message", "参数错误，请刷新重试!");
            return data;
        }
        User user = UserUtils.getUser();
        Company company = user.getCompany();

        // 获取订单明细数据
        List<ContractDetail> conDetailList = contractDetailDao.getSentPrdorderDeailData(prd.getContractIds(), company);
        if (Collections3.isNotEmpty(conDetailList))
        {
            // 获取合同集合并去重
            Set<String> contractIds = conDetailList.stream()
                .map(contractDetail -> contractDetail.getContract().getRecordId())
                .collect(Collectors.toSet());
            if (Collections3.isNotEmpty(contractIds))
            {
                for (String contractId : contractIds)
                {
                    Prdorder prdorder = new Prdorder();
                    Contract contract = null;
                    for (ContractDetail detail : conDetailList)
                    {
                        if (null != detail.getContract() && contractId.equals(detail.getContract().getRecordId()))
                        {
                            contract = detail.getContract();
                            break;
                        }
                    }
                    prdorder.setCompany(company);
                    prdorder.setSupplier(new Supplier(prd.getSupplierId()));
                    prdorder.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString(),
                        company));
                    Integer noNum = prdorderDao.findPrdorderNoisEnable(prdorder); // 查询编号是否依旧存在
                    while (noNum != null && noNum > 0)
                    {
                        // 将对应编码的nextNo 修改为+1
                        CommonUtils.updateNextNo(CommonEnums.CodeType.PURCHASEORDER.getIndex(), company);
                        prdorder.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex()
                            .toString(), company));
                        noNum = prdorderDao.findPrdorderNoisEnable(prdorder); // 查询编号是否依旧存在
                    }
                    Branch branch = new Branch();
                    branch.setCompany(company);
                    branch = branchDao.getBranchByCompanyName(branch);
                    prdorder.setBranch(branch);
                    prdorder.setPurchaser(UserUtils.getUser()); // 采购员
                    prdorder.setStatus(TypeKey.PU_PURCHASING_STATUS_UNCONFIRM.toString());
                    prdorder.setRemark(contract.getRemark());
                    prdorder.setOrderDate(contract.getOrderDate());
                    prdorder.setDeliveryPlace(contract.getDeliveryPlace());
                    prdorder.setTaxDescript(contract.getTaxDescript());
                    prdorder.setCurrencyType(contract.getCurrencyType());
                    prdorder.setPayWay(contract.getPayWay());
                    prdorder.setFreightWay(contract.getFreightWay());
                    prdorder.setDeliveryWay(contract.getDeliveryWay());
                    prdorder.setCreatedDate(new Date());
                    prdorder.setLastUpdDate(new Date());
                    prdorder.setCreatedBy(user);
                    prdorder.setLastUpdBy(user);
                    prdorderDao.insert(prdorder);
                    CommonUtils.updateNextNo(CommonEnums.CodeType.PURCHASEORDER.getIndex(), company);

                    prdorder.setPrdorderDetailList(Lists.newArrayList());
                    for (ContractDetail detail : conDetailList)
                    {
                        if (null != detail.getContract() && contractId.equals(detail.getContract().getRecordId()))
                        {
                            ContractCraft craft = detail.getContractCraftList();
                            ProductCraft proCraft = new ProductCraft();
                            proCraft.setCompany(company);
                            proCraft.setCustomer(new Customer(contract.getCustomerId()));
                            proCraft.setNo(craft.getNo());
                            proCraft.setCustomerModel(craft.getCustomerModel());
                            proCraft.setUnitWidth(craft.getUnitWidth());
                            proCraft.setUnitLength(craft.getUnitLength());
                            proCraft.setPnlWidth(craft.getPnlWidth());
                            proCraft.setPnlLength(craft.getPnlLength());
                            proCraft.setPnlDivisor(craft.getPnlDivisor());
                            proCraft.setDeliverySize(craft.getDeliverySize());
                            proCraft.setFilmDescript(craft.getFilmDescript());
                            proCraft.setReferenceType(craft.getReferenceType());
                            proCraft.setReferenceDesc(craft.getReferenceDesc());
                            proCraft.setMaterialType(craft.getMaterialType());
                            proCraft.setBoardLevel(craft.getBoardLevel());
                            proCraft.setBoardThickness(craft.getBoardThickness());
                            proCraft.setSurfaceProcess(craft.getSurfaceProcess());
                            proCraft.setCopperCladThickness(craft.getCopperCladThickness());
                            proCraft.setSolderMaskType(craft.getSolderMaskType());
                            proCraft.setCharacterType(craft.getCharacterType());
                            proCraft.setShapingWay(craft.getShapingWay());
                            proCraft.setDrillingWay(craft.getDrillingWay());
                            proCraft.setHoleSolderMask(craft.getHoleSolderMask());
                            proCraft.setPackingType(craft.getPackingType());
                            proCraft.setTestMethod(craft.getTestMethod());
                            proCraft.setBlueFilmFlag(craft.getBlueFilmFlag());
                            proCraft.setNewMouldFlag(craft.getNewMouldFlag());
                            proCraft.setNewShelfFlag(craft.getNewShelfFlag());
                            proCraft.setHoleThiaOilFlag(craft.getHoleThiaOilFlag());
                            proCraft.setThroughHole(craft.getThroughHole());
                            proCraft.setCountersinkHole(craft.getCountersinkHole());
                            proCraft.setShippingAddress(craft.getShippingAddress());
                            proCraft.setBoard(craft.getBoard());
                            proCraft.setInkType(craft.getInkType());
                            if (null != craft.getDaore())
                            {
                                proCraft.setDaore(craft.getDaore().toString());
                            }
                            if (null != craft.getNaiya())
                            {
                                proCraft.setNaiya(craft.getNaiya().toString());
                            }
                            proCraft.setSpecialCraft(craft.getSpecialCraft());
                            proCraft.setStatus(TypeKey.PU_PURCHASING_STATUS_CONFIRMED.toString());
                            proCraft.setCreatedDate(new Date());
                            proCraft.setLastUpdDate(new Date());
                            proCraft.setCreatedBy(user);
                            proCraft.setLastUpdBy(user);
                            productCraftDao.insert(proCraft);

                            Price price = detail.getPricees();
                            price = price == null ? new Price() : price;
                            ProductPrice proPrice = new ProductPrice();
                            proPrice.setCompany(company);
                            proPrice.setCraft(proCraft);
                            proPrice.setPrice(detail.getPricees().getPrice());
                            // 防止单价为空
                            if (null == proPrice.getPrice())
                            {
                                proPrice.setPrice(BigDecimal.ZERO);
                            }
                            proPrice.setEngineeringFee(price.getEngineeringFee());
                            proPrice.setMouldFee(price.getMouldFee());
                            proPrice.setTestShelfFee(price.getTestShelfFee());
                            proPrice.setOthersFee(price.getOthersFee());

                            proPrice.setEngineeringLimit(price.getEngineeringLimit());
                            proPrice.setMouldLimit(price.getMouldLimit());
                            proPrice.setTestShelfLimit(price.getTestShelfLimit());
                            proPrice.setCreatedDate(new Date());
                            proPrice.setLastUpdDate(new Date());
                            proPrice.setCreatedBy(user);
                            proPrice.setLastUpdBy(user);
                            productPriceDao.insert(proPrice);

                            PrdorderDetail prdorderDetail = new PrdorderDetail();
                            prdorderDetail.setCompany(company);
                            prdorderDetail.setPrdorder(prdorder);
                            prdorderDetail.setContractCraft(proCraft);
                            prdorderDetail.setPuPrice(proPrice.getPrice());
                            prdorderDetail.setPrice(proPrice);
                            prdorderDetail.setContractDetail(detail);
                            // prdorderDetail.setGroupCenterId(detail.getGroupCenterId());

                            ChangeDataUtils changeDataUtils = new ChangeDataUtils();
                            DictValue orderType =
                                changeDataUtils.getNotificationOrderType(detail.getOrderType(), company);
                            if (null != orderType && StringUtils.isNotBlank(orderType.getRecordId()))
                            {
                                prdorderDetail.setOrderType(getLongValue(orderType.getRecordId()));
                            }
                            prdorderDetail.setSubTotal(prdorderDetail.getAmountNew(Integer.parseInt(detail.getQuantity())));
                            prdorderDetail.setStatus(TypeKey.PU_PURCHASING_STATUS_CONFIRMED.toString());
                            prdorderDetail.setCustomerPo(contract.getCustomerPo());
                            prdorderDetail.setQuantity(Integer.valueOf(detail.getQuantity()));
                            prdorderDetail.setDeliveryDate(detail.getDeliveryDate());
                            prdorderDetail.setRemark(detail.getRemark());
                            prdorderDetail.setCreatedDate(new Date());
                            prdorderDetail.setLastUpdDate(new Date());
                            prdorderDetail.setCreatedBy(user);
                            prdorderDetail.setLastUpdBy(user);
                            prdorderDetailDao.insert(prdorderDetail);
                        }
                    }
                }
            }
        }
        data.put("result", "success");
        data.put("message", "生成采购合同成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public void inDataNew(List<ContractImpVo> details, String customerId, String string, HttpSession session)
        throws ParseException
    {
        // 业务逻辑处理
        if (details.size() > 0)
        {
            ContractDetail cd = null;
            Contract contract = initImpContract(details.get(0));
            if (contract.getIsNewRecord())
            {
                contract.setCompany(UserUtils.getUser().getCompany());
                contract.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
                CommonUtils.updateNextNo(CommonEnums.CodeType.CONTRACT.getIndex());
                contract.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CONTRACT.getIndex().toString()));
                Integer noNum = contractDao.findContractNoisEnable(contract);
                while (noNum != null && noNum > 0)
                {
                    // 将对应编码的nextNo 修改为+1
                    CommonUtils.updateNextNo(CommonEnums.CodeType.CONTRACT.getIndex());
                    contract.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CONTRACT.getIndex().toString()));
                    noNum = contractDao.findContractNoisEnable(contract);
                }
                contract.preInsert();
                contractDao.insert(contract);
                CommonUtils.updateNextNo(CommonEnums.CodeType.CONTRACT.getIndex());
            }
            else
            {
                contract.preUpdate();
                contractDao.update(contract);
            }
            BigDecimal area = BigDecimal.ZERO;
            BigDecimal amount = BigDecimal.ZERO;
            for (ContractImpVo vo : details)
            {
                cd = initImpContractDetail(vo);
                cd.setContract(contract);
                area = area.add(new BigDecimal(cd.getOrderDeailArea()));
                amount = amount.add(cd.getSubTotal());
                contract.setOrderArea(area.toString());
                contract.setTotalAmt(amount);
                // contract.setSubTotal(amount);
                this.saveContractDetail(cd);
            }

        }

    }

    private ContractDetail initImpContractDetail(ContractImpVo vo)
        throws ParseException
    {
        // TODO getOrderDeailArea getPricees().getPrice()
        ContractDetail scd = new ContractDetail();
        scd.setCompany(UserUtils.getUser().getCompany());
        // TODO 厂编
        ContractCraft craft = new ContractCraft();
        craft.setUnitLength(new BigDecimal(vo.getUnitLength()));
        craft.setUnitWidth(new BigDecimal(vo.getUnitWidth()));
        craft.setPnlDivisor(new BigDecimal(vo.getPnlDivisor()));
        craft.setCustomerModel(vo.getCustomerModel());
        craft.setPnlWidth(new BigDecimal(vo.getPnlWidth()));
        craft.setPnlLength(new BigDecimal(vo.getPnlLength()));
        craft.setDeliverySize(vo.getDeliverySize());
        craft.setMaterialType(getLongValue(vo.getMaterialType()));
        craft.setBoardLevel(getLongValue(vo.getBoardLevel()));
        craft.setBoardThickness(getLongValue(vo.getBoardThickness()));
        craft.setSurfaceProcess(getLongValue(vo.getSurfaceProcess()));
        craft.setCopperCladThickness(getLongValue(vo.getCopperCladThickness()));
        craft.setSolderMaskType(getLongValue(vo.getSolderMaskType()));
        craft.setCharacterType(getLongValue(vo.getCharacterType()));
        craft.setShapingWay(getLongValue(vo.getShapingWay()));
        // craft.setDrillingWay(getLongValue(vo.getDrillingWay()));
        // craft.setHoleSolderMask(getLongValue(vo.getHoleSolderMask()));
        // craft.setPackingType(getLongValue(vo.getPackingType()));
        craft.setTestMethod(getLongValue(vo.getTestMethod()));
        craft.setTestMethod(getLongValue(vo.getTestMethod()));
        craft.setStatus(TypeKey.SL_CRAFT_STATUS_DRAFT.toString());
        craft.setActiveFlag("1");
        craft.setDaore(getLongValue(vo.getDaore()));
        craft.setNaiya(getLongValue(vo.getNaiya()));
        craft.setAddCraftFlag("1");
        craft.setLingeSpacing(getLongValue(vo.getLingeSpacing()));
        craft.setSmallAperture(getLongValue(vo.getSmallAperture()));
        craft.setHalAhole(getLongValue(vo.getHalAhole()));
        craft.setBuryBlindHole(getLongValue(vo.getBuryBlindHole()));
        craft.setResistance(getLongValue(vo.getResistance()));
        craft.setResistance(getLongValue(vo.getResistance()));
        craft.setThroughHole(vo.getThroughHole());
        craft.setCountersinkHole(vo.getCountersinkHole());
        craft.setShippingAddress(vo.getShippingAddress());
        craft.setInkType(getLongValue(vo.getInkType()));
        // TODO specialCraft
        craft.setSpecialCraft(vo.getSpecialCraftList());

        scd.setContractCraftList(craft);
        Price price = new Price();
        price.setPrice(new BigDecimal(vo.getPrice()));
        scd.setPricees(price);
        scd.setQuantity(new BigDecimal(vo.getQuantity()).intValue() + "");
        scd.setSubTotal(price.getPrice().multiply(new BigDecimal(vo.getQuantity())));
        // #{deliveryDays}, #{customerFileName}, #{customerMaterialNo},不用
        scd.setReferenceType(getLongValue(vo.getReferenceType()));
        scd.setActiveFlag("1");
        scd.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
        scd.setRemark(vo.getDetailRemark());
        scd.setDeliveryDate(DateUtils.parseDate(vo.getDeliveryDate(), "yyyy-MM-dd HH:mm:ss"));
        BigDecimal area = new BigDecimal(vo.getPnlWidth()).multiply(new BigDecimal(vo.getPnlLength()))
            .multiply(new BigDecimal(vo.getQuantity()))
            .divide(new BigDecimal(vo.getPnlDivisor()).multiply(new BigDecimal("1000000")),
                4,
                BigDecimal.ROUND_HALF_UP);
        scd.setOrderDeailArea(area.toString());
        // TODO orderType oldCraftId
        scd.setMadeSupplierId(vo.getMadeSupplierId());
        scd.setProcessValueId(vo.getProcessValueId());

        return scd;
    }

    private Contract initImpContract(ContractImpVo contractImpVo)
        throws ParseException
    {
        Contract contract = new Contract();
        contract.setCompany(UserUtils.getUser().getCompany());
        contract.setCustomer(new Customer(contractImpVo.getCustomerId()));
        // NO
        contract.setCustomerPo(contractImpVo.getCustomerPo());
        contract.setOrderDate(DateUtils.parseDate(contractImpVo.getOrderDate(), "yyyy-MM-dd HH:mm:ss"));
        contract.setTaxDescript(getLongValue(contractImpVo.getTaxDescript()));
        contract.setCurrencyType(getLongValue(contractImpVo.getCurrencyType()));
        contract.setPayWay(getLongValue(contractImpVo.getPayWay()));
        // payDays prePayAmount prePayBalance 不用存
        contract.setDeliveryWay(getLongValue(contractImpVo.getDeliveryWay()));
        contract.setDeliveryPlace(contractImpVo.getDeliveryPlace());
        contract.setFreightWay(getLongValue(contractImpVo.getFreightWay()));
        contract.setFreightWay(getLongValue(contractImpVo.getFreightWay()));
        // #{chkAcptWay},#{chkDissentDays},#{qualityStd}, #{assureDays},#{packagingStd}, #{status}, #{cancelReason}, 不用存
        contract.setRemark(contractImpVo.getRemark());
        contract.setBranch(new Branch(contractImpVo.getBranch()));
        // #{user.recordId}, #{slCustomerOrder.recordId}, 不用存
        // TODO orderArea
        // #{signplace}, #{deliverycity}, #{frightbear}, #{billformula},#{hasquestion}, 不用存
        // TODO saleId saleCsutomerId
        contract.setPaycause(getLongValue(contractImpVo.getPaycause()));
        contract.setCustomerContact(new CustomerContact(contractImpVo.getCustomerContact()));
        contract.setDeptId(contractImpVo.getDeptId());
        contract.setActiveFlag("1");
        return contract;

    }

    private Long getLongValue(String str)
    {
        if (StringUtils.isEmpty(str))
        {
            return null;
        }
        else
        {
            return Long.valueOf(str);
        }
    }

    @Transactional(readOnly = false)
    public Map<String, Object> modSimpleQuantity(ContractDetail contractDetail)
    {
        //  自制的合同 验证订单类型（样品） 订单面积小余1平米  订单状态审批通过，未生成投料单
        contractDetail.setFactId(CompanyUtil.getInstance().getFactId());
        Map<String, Object> result = new HashMap<>();
        if (null == contractDetail || StringUtils.isBlank(contractDetail.getRecordId()) || StringUtils.isBlank(
            contractDetail.getGroupCenterId()))
        {
            result.put("result", "fail");
            result.put("message", "数据错误，请联系管理员");
            return result;
        }
        BigDecimal valiArea = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(contractDetail.getOrderDeailArea()))
        {
            valiArea = new BigDecimal(contractDetail.getOrderDeailArea());
        }
        if (valiArea.compareTo(BigDecimal.ONE) > 0)
        {
            result.put("result", "fail");
            result.put("message", "样品订单面积不能大于1㎡");
            return result;
        }
        //验证是自制和订单类型是样品
        String msg = contractDetailDao.getCheckedModSimp(contractDetail);
        if (StringUtils.isBlank(msg))
        {
            result.put("result", "fail");
            result.put("message", "只能修改样品订单，数据错误，请联系管理员");
            return result;
        }
        //验证是否有投料单
        String feedId = contractDetailDao.getfeedId(contractDetail);
        if (!StringUtils.isBlank(feedId))
        {
            result.put("result", "fail");
            result.put("message", "合同明细已经生成投料单，不可以改量");
            return result;
        }
        //修改合同订量、金额、面积 (三个公司)  修改成品采购合同订量、金额    修改通知单订单数量、金额 、面积
        contractDetailDao.updateSimpleQuantity(contractDetail);
        //合同主表的金额、 面积(三个公司)
        contractDetailDao.updateSimpleOrderQuantity(contractDetail);
        //采购单 数量 、金额
        contractDetailDao.updatePrdorderQuantity(contractDetail);
        //采购主表
        //contractDetailDao.updatePrdorderOrder(contractDetail);
        contractDetailDao.updateNotifiQuantity(contractDetail);
        //如果是合单 合单的通知单的数量修改
        String mergeId = contractDetailDao.getNofiMergeType(contractDetail);
        if (StringUtils.isNotBlank(mergeId))
        {
            contractDetailDao.updateMergeQuantity(mergeId);
        }
        result.put("result", "success");
        result.put("message", "样品订量修改成功！");
        return result;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> commitReflashAuditBatch(ContractDetail detail)
    {
        Map<String, Object> data = new HashMap<>();
        if (null == detail || detail.getNotification() == null || StringUtils.isBlank(detail.getNotification()
            .getRecordId()) || StringUtils.isEmpty(detail.getRemark()))
        {
            data.put("result", "fail");
            data.put("message", "数据错误，请联系管理员!");
            return data;
        }
        // 验证通知单状态 60001 审批中;合同已做送货单;已经投料;已制作工程卡;-->合同明细集合
        detail.setFactId(CompanyUtil.getInstance().getFactId());
        List<ContractDetail> details = notificationDao.checkAndDetails(detail);
        StringBuffer sb = new StringBuffer();
        for (ContractDetail cc : details)
        {
            if (cc.getNotification() != null && !StringUtils.isEmpty(cc.getNotification().getRemark()))
            {
                sb.append(cc.getNotification().getNo());
                sb.append(":");
                sb.append(cc.getNotification().getRemark());
                sb.append("; ");
            }
        }
        if (sb.length() > 0)
        {
            data.put("result", "fail");
            data.put("message", sb.toString());
            return data;
        }
        boolean con = ModifyUtils.allocationExist("22", UserUtils.getUser().getCompany());
        if (con)
        {
            boolean commitFlag = false;
            String result = "";
            for (ContractDetail contractDetail : details)
            {
                contractDetail.setRemark(detail.getRemark());
                result = ModifyUtils.reflashApproval(contractDetail);
                if (!"fail".equals(result))
                {
                    commitFlag = true;
                    continue;
                }

                // 获取订单中是否有审批中的订单明细
                Integer count = contractDetailDao.getApprovalDetailCount(contractDetail);
                if (null == count || count == 0)
                {
                    ContractDetail cd = contractDetailDao.get(contractDetail);
                    Contract contract = cd.getContract();
                    contract.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
                    contractDao.updateStatus(contract);
                }

                contractDetail.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
                contractDetailDao.updateStatus(contractDetail);

                ChangeDataUtils changeDataUtils = new ChangeDataUtils();
                Approval approval = new Approval();
                approval.setCustomerType(TypeKey.APPROVAL_CONTRACTDETAIL_REFLASH.toString());
                approval.setDataDetailId(contractDetail.getRecordId());
                changeDataUtils.deleteGenConrtactData(approval, UserUtils.getUser().getCompany());

            }
            //更新通知单状态为审批中
            detail.getNotification().setStatus(TypeKey.APPROVAL_STATUS_WAIT.toString());
            notificationDao.updateStatusToHistory(detail.getNotification());
            data.put("result", "success");
            data.put("message", commitFlag ? result : "批量合同明细反审成功!");
            return data;
        }
        else
        {
            data.put("result", "fail");
            data.put("message", "请先配置合同反审审批!");
        }
        return data;
    }

    @Transactional(readOnly = false)
    public void saveContractDetailData(ContractDetail contractDetail)
    {
        if (null == contractDetail || StringUtils.isBlank(contractDetail.getRecordId()))
        {
            return;
        }
        String groupCenterId = contractDetailDao.getGroupCenterIdById(contractDetail);
        if (StringUtils.isNotBlank(groupCenterId))
        {
            contractDetail.setGroupCenterId(groupCenterId);
            contractDetailDao.updateDetailData(contractDetail);
            notificationDao.updateRemark(contractDetail);
            prdorderDetailDao.updateDetailData(contractDetail);
        }
        UserUtils.saveUpdateLog(contractDetail.getRecordId(), 1, "更新合同明细的备注和客户物料号");
    }

    public List<ContractAttachements> findContDetialAttachements(ContractAttachements contractAttachements)
    {
        contractAttachements.setCompany(UserUtils.getUser().getCompany());
        return contractAttachementsDao.findContractAttachementsList(contractAttachements);
    }

    @Transactional(readOnly = false)
    public void saveContractAttachements(ContractDetail cd, String realFileName, String originalFilename)
    {
        // 保存发票附件信息
        ContractAttachements attachement = new ContractAttachements();
        attachement.setCompany(cd.getCompany());
        attachement.setContractDetailId(cd.getRecordId());
        if (null != cd.getContract())
        {
            attachement.setContractId(cd.getContract().getRecordId());
        }
        attachement.setRealFileName(realFileName + originalFilename);
        attachement.setOrgFileName(originalFilename);
        attachement.preInsert();
        contractAttachementsDao.insert(attachement);
    }

    @Transactional(readOnly = false)
    public void deleteContractAttachements(ContractAttachements contractAttachements)
    {
        contractAttachementsDao.delete(contractAttachements);
    }

    public List<ContractDetail> getOutStocksList(ContractDetail contractDetail)
    {
        List<ContractDetail> list = Lists.newArrayList();
        if (null == contractDetail || StringUtils.isBlank(contractDetail.getCraftNo()))
        {
            return list;
        }
        Company company = UserUtils.getUser().getCompany();
        ContractCraft craft = new ContractCraft();
        craft.setCompany(company);
        craft.setNo(contractDetail.getCraftNo());
        List<ContractDetail> detailList = contractDetailDao.getOutStocksList(craft);
        if (Collections3.isNotEmpty(detailList))
        {
            String contractDetailIds = null;
            for (ContractDetail detail : detailList)
            {
                if (StringUtils.isNotBlank(contractDetailIds))
                {
                    contractDetailIds = contractDetailIds + "," + detail.getRecordId();
                }
                else
                {
                    contractDetailIds = detail.getRecordId();
                }
            }
            // 获取库存
            List<ProductStore> storeList = productStoreDao.getStocksByContractDetailIds(contractDetailIds);
            if (Collections3.isNotEmpty(storeList))
            {
                for (ContractDetail detail : detailList)
                {
                    for (ProductStore store : storeList)
                    {
                        if (detail.getRecordId().equals(store.getContractDetailId()))
                        {
                            if (null != store.getQuantity())
                            {
                                detail.setQuantity(store.getQuantity().toString());
                            }
                            break;
                        }
                    }
                }
            }
            // 获取已占用记录
            ProductStocksUse use = new ProductStocksUse();
            use.setCompany(company);
            use.setDetailId(contractDetail.getRecordId());
            use.setRelationDetailId(contractDetailIds);
            List<ProductStocksUse> useList = productStocksUseDao.getStocksList(use);
            if (Collections3.isNotEmpty(useList))
            {
                for (ContractDetail detail : detailList)
                {
                    if (StringUtils.isBlank(detail.getQuantity()) || "0".equals(detail.getQuantity()))
                    {
                        continue;
                    }
                    Integer deQty = Integer.valueOf(detail.getQuantity());
                    for (ProductStocksUse ptu : useList)
                    {
                        if (ptu.getRelationDetailId().equals(detail.getRecordId()))
                        {
                            if (null != ptu.getQuantity())
                            {
                                detail.setQuantity(String.valueOf(deQty - ptu.getQuantity()));
                            }
                        }
                    }
                    deQty = Integer.valueOf(detail.getQuantity());
                    if (deQty < 0)
                    {
                        detail.setQuantity("0");
                    }
                }
            }
            for (ContractDetail detail : detailList)
            {
                if (StringUtils.isBlank(detail.getQuantity()) || "0".equals(detail.getQuantity()))
                {
                    continue;
                }
                list.add(detail);
            }
        }
        list.sort((t1, t2) -> (null == t2.getQuantity() ? "0" : t2.getQuantity()).compareTo((null == t1.getQuantity() ?
            "0" :
            t1.getQuantity())));
        return list;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> saveOutStocks(ContractDetail detail)
    {
        Map<String, Object> data = new HashMap<>();
        if (null == detail || Collections3.isEmpty(detail.getConcatList()))
        {
            data.put("result", "fail");
            data.put("message", "参数错误，请刷新重试!");
            return data;
        }
        Company company = UserUtils.getUser().getCompany();

        // 删除
        ProductStocksUse delQuery = new ProductStocksUse();
        delQuery.setDetailId(detail.getRecordId());
        productStocksUseDao.batchDelete(delQuery);

        // 添加
        List<ProductStocksUse> insertList = Lists.newArrayList();
        for (ContractDetail cd : detail.getConcatList())
        {
            ProductStocksUse use = new ProductStocksUse();
            use.setCompany(company);
            use.setDetailId(detail.getRecordId());
            use.setRelationDetailId(cd.getRecordId());
            use.setType("1");
            if (StringUtils.isNotBlank(cd.getQuantity()))
            {
                use.setQuantity(Integer.valueOf(cd.getQuantity()));
            }
            use.setPrice(cd.getMadePcsPrice());
            use.preInsert();
            insertList.add(use);
        }
        productStocksUseDao.batchInsert(insertList);
        data.put("result", "success");
        data.put("message", "保存成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> preservation (Contract contract)
    {
        Map<String, Object> data = new HashMap<>();
        if (null == contract)
        {
            data.put("result","fail");
            data.put("message","参数失败，请刷新重试！");
            return data;
        }
        if (StringUtils.isNotBlank(contract.getRecordId()))
        {
            contract.preUpdate();
            contractDetailDao.getPreservation(contract);
        }
        // 客户等级更新
        if(null != contract.getCustomer() && StringUtils.isNotBlank(contract.getCustomerGrade()))
        {
            contract.getCustomer().setCustomerGrade(Long.valueOf(contract.getCustomerGrade()));
            customerDao.updateCustomerGrade(contract.getCustomer());
        }
        data.put("result","success");
        data.put("message","保存成功");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> saveContractDetailDelivery (List<DeliveryDetail> deliveryDetails)
    {
        Map<String, Object> data = new HashMap<>();
        if (Collections3.isEmpty(deliveryDetails))
        {
            data.put("result","fail");
            data.put("message","参数失败，请刷新重试！");
            return data;
        }
        for(DeliveryDetail conn:deliveryDetails){
            conn.setCompany(UserUtils.getUser().getCompany());
            if (StringUtils.isNotBlank(conn.getRecordId()))
            {
                conn.preUpdate();
                deliveryDetailDao.updateDelivery(conn);
            }else{
                conn.preInsert();
                deliveryDetailDao.insertDelivery(conn);
            }
        }
        data.put("result","success");
        data.put("message","保存成功");
        return data;
    }

    @Transactional(readOnly = false)
    public String delDelivery(ContractDetail contractDetail){
        contractDetailDao.delDelivery(contractDetail);
        return "success";
    }

    public List<DeliveryDetail>selectDeliveryList(DeliveryDetail deliveryDetail)
    {
        deliveryDetail.setCompanyId("3");
        return deliveryDetailDao.selectDeliveryList(deliveryDetail);
    }

    @Transactional(readOnly = false)
    public void cleanProductStocksUse(ContractDetail contractDetail)
    {
        ProductStocksUse delQuery = new ProductStocksUse();
        delQuery.setDetailId(contractDetail.getRecordId());
        productStocksUseDao.batchDelete(delQuery);
    }
}
