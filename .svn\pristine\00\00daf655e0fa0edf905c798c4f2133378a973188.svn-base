package com.kyb.pcberp.common.utils;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.material.MaterialTimeUtils;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.purch.dao.PurchasingDetailDao;
import com.kyb.pcberp.modules.purch.dao.PurchasingDetailDeliverysDao;
import com.kyb.pcberp.modules.purch.dao.PurchasingDetailDeliverysMiddleDao;
import com.kyb.pcberp.modules.purch.entity.PurchRaw;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetailDeliverys;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetailDeliverysMiddle;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.dao.RawmaterialStockDao;
import com.kyb.pcberp.modules.stock.dao.StoreHouseDao;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.MaterialPlace;
import com.kyb.pcberp.modules.stock.entity.MaterialPlaceCom;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import com.kyb.pcberp.modules.wechat.dao.ProductDao;
import com.kyb.pcberp.modules.wechat.entity.InterProduct;
import com.kyb.pcberp.modules.wechat.entity.InterProductStockRecord;
import org.apache.shiro.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

public class RawStockUtil
{
    private MaterialDao materialDao = SpringContextHolder.getBean(MaterialDao.class);

    private StoreHouseDao storeHouseDao = SpringContextHolder.getBean(StoreHouseDao.class);

    private RawmaterialStockDao rawmaterialStockDao = SpringContextHolder.getBean(RawmaterialStockDao.class);

    private PurchasingDetailDao purchasingDetailDao = SpringContextHolder.getBean(PurchasingDetailDao.class);

    private CompanyDao companyDao = SpringContextHolder.getBean(CompanyDao.class);

    private PurchasingDetailDeliverysDao deliverysDao = SpringContextHolder.getBean(PurchasingDetailDeliverysDao.class);

    private PurchasingDetailDeliverysMiddleDao deliverysMiddleDao = SpringContextHolder.getBean(PurchasingDetailDeliverysMiddleDao.class);

    private ProductDao productDao = SpringContextHolder.getBean(ProductDao.class);

    // 获取某个时间点物料所有库位/某个库位的库存
    public List<MaterialPlaceCom> getMaterialApplyStock(String materialIds, Date operDate, String stockPlaceComId)
    {
        // 根据materialIds找出所有库位以及各库位对应的期初库存、期初时间
        MaterialPlaceCom materialPlaceCom = new MaterialPlaceCom();
        materialPlaceCom.setMaterialIds(materialIds);
        materialPlaceCom.setInitDate(operDate);
        if (StringUtils.isNotBlank(stockPlaceComId))
        {
            materialPlaceCom.setRecordId(stockPlaceComId);
        }
        List<MaterialPlaceCom> mpList = materialDao.getMaterialPlaceApplyList(materialPlaceCom);
        // 获取从期初到查询开始时间之间的出入库记录
        Material material = new Material();
        material.setMaterialIds(materialIds);
        material.setInitDate(operDate);
        if (mpList.size() > 0)
        {
            material.setStartDate(mpList.get(0).getInitDate());
        }
        List<RawmaterialStock> rsList = materialDao.getRawmaterialStockApplyTwoList(material);
        // 进行滚存数据得到最终库存
        List<MaterialPlaceCom> list = cumulativeStock(mpList, rsList);
        // 进行修改期初库存和期初时间
        for (int i = 0; i < mpList.size(); i++)
        {
            for (int j = 0; j < list.size(); j++)
            {
                if (mpList.get(i).getRecordId().equals(list.get(j).getRecordId()))
                {
                    mpList.get(i).setPeriodStocks(list.get(j).getChangeStocks());
                    mpList.get(i).setInitDate(operDate);
                }
            }
        }
        return mpList;
    }

    // 给对应物料赋予结余和结余归属
    public List<RawmaterialStock> getMaterialApplyList(List<RawmaterialStock> materialList,
        List<MaterialPlaceCom> comList, List<RawmaterialStock> rsList)
    {
        // 得到库位结存
        List<MaterialPlaceCom> list = cumulativeStock(comList, rsList);
        // 将库位结存合并成物料库存
        for (int i = 0; i < materialList.size(); i++)
        {
            BigDecimal stocks = BigDecimal.ZERO;
            BigDecimal periodStocks = BigDecimal.ZERO;
            BigDecimal inStocks = BigDecimal.ZERO;
            BigDecimal outStocks = BigDecimal.ZERO;
            String msg = "";
            for (int j = 0; j < list.size(); j++)
            {
                // 物料id相等的话进行拼接数据
                if (list.get(j).getMaterialId().equals(materialList.get(i).getRecordId()))
                {
                    if (list.get(j).getChangeStocks() != null)
                    {
                        stocks = stocks.add(list.get(j).getChangeStocks());

                        if (StringUtils.isNotBlank(list.get(j).getComName()))
                        {
                            String changeStocksStr = strs(list.get(j).getChangeStocks().toString());
                            if (StringUtils.isNotBlank(msg))
                            {
                                msg = msg + "/" + list.get(j).getComName() + ":" + changeStocksStr;
                            }
                            else
                            {
                                msg = list.get(j).getComName() + ":" + changeStocksStr;
                            }
                        }
                    }
                    if (list.get(j).getPeriodStocks() != null
                        && list.get(j).getPeriodStocks().compareTo(BigDecimal.ZERO) > 0)
                    {
                        periodStocks = periodStocks.add(list.get(j).getPeriodStocks());
                    }
                    if (list.get(j).getInStocks() != null && list.get(j).getInStocks().compareTo(BigDecimal.ZERO) > 0)
                    {
                        inStocks = inStocks.add(list.get(j).getInStocks());
                    }
                    if (list.get(j).getOutStocks() != null && list.get(j).getOutStocks().compareTo(BigDecimal.ZERO) > 0)
                    {
                        outStocks = outStocks.add(list.get(j).getOutStocks());
                    }
                }
            }
            materialList.get(i).setQuantity(stocks);
            materialList.get(i).setStartStocks(periodStocks);
            materialList.get(i).setInStocks(inStocks);
            materialList.get(i).setOutStocks(outStocks);
            materialList.get(i).setStockSpec(msg);
        }
        return materialList;
    }

    public List<RawmaterialStock> dealPlace(RawmaterialStock rawMaterial, String flag)
    {
        // 查询条件有库位id，才会调用以下方法
        if (rawMaterial.getMaterial() != null && StringUtils.isNotBlank(rawMaterial.getStockPlaceId()))
        {
            MaterialPlaceCom materialPlaceCom = new MaterialPlaceCom();
            materialPlaceCom.setCompany(rawMaterial.getCompany());
            materialPlaceCom.setMaterialId(rawMaterial.getMaterial().getRecordId());
            materialPlaceCom.setMaterPlaceId(rawMaterial.getStockPlaceId());
            MaterialPlaceCom maCom = storeHouseDao.getMaterialPlaceCom(materialPlaceCom);
            if (null != maCom)
            {
                rawMaterial.setStockPlaceComId(maCom.getRecordId());
            }
        }
        List<RawmaterialStock> list = rawmaterialStockDao.findStockList(rawMaterial);
        if (Collections3.isNotEmpty(list))
        {
            for(RawmaterialStock stock : list)
            {
                if (null == stock)
                {
                    continue;
                }
                if (TypeKey.ST_MATERIAL_INOUTTYPE_IN.compareTo(stock.getInoutType()) == 0 || TypeKey.ST_MATERIAL_INOUTTYPE_OUT.compareTo(stock.getInoutType()) == 0)
                {
                    List<RawmaterialStock> fisrtInFirstOutList = rawmaterialStockDao.getFisrtInFirstOutList(stock);
                    stock.setFisrtInFirstOutList(fisrtInFirstOutList);
                    if (Collections3.isNotEmpty(fisrtInFirstOutList))
                    {
                        BigDecimal amount = BigDecimal.ZERO;
                        for(RawmaterialStock inOut : fisrtInFirstOutList)
                        {
                            amount = inOut.getFirstInFirstAmount().add(amount);
                        }
                        stock.setFirstInFirstAmount(amount);
                    }
                }
            }
        }
        Material stockRawmaterialStock;
        if (list.size() > 0)
        {
            stockRawmaterialStock = list.get(0).getMaterial();
        }
        else
        {
            stockRawmaterialStock = rawmaterialStockDao.findMaterialByPlace(rawMaterial.getMaterial());
        }
        rawMaterial.setMaterial(stockRawmaterialStock);

        List<RawmaterialStock> stockMaStockList = getRawoMaterialStockPlace(rawMaterial, flag);
        // 模仿插入汇总数据
        RawmaterialStock sto = new RawmaterialStock();
        if (stockMaStockList != null && stockMaStockList.size() > 0)
        {
            for (RawmaterialStock stock : stockMaStockList)
            {
                // id为空的代表汇总数据
                if (StringUtils.isBlank(stock.getRecordId()) && stock.getCurrStocks() != null
                    && stock.getCurrStocks().compareTo(BigDecimal.ZERO) >= 0)
                {
                    sto.setMaterial(stockRawmaterialStock);
                    sto.setCurrStocks(stock.getCurrStocks());
                    sto.setAllStocks(stock.getAllStocks());

                    if (stock.getCurrPrice() != null)
                    {
                        sto.setCurrPrice(stock.getCurrPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                    }

                    if (stock.getStartPrice() != null)
                    {
                        sto.setStartPrice(stock.getStartPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                    }

                    if (stock.getNewPurchPrice() != null)
                    {
                        sto.setNewPurchPrice(stock.getNewPurchPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    if(null != stock.getUpdatePriceDate())
                    {
                        sto.setUpdatePriceDate(stock.getUpdatePriceDate());
                    }
                    sto.setInStocks(stock.getInStocks());
                    sto.setOutStocks(stock.getOutStocks());
                    break;
                }
            }
        }
        // 对集合数据进行赋予库存价格信息
        if (list.size() > 0)
        {
            if (stockMaStockList != null && stockMaStockList.size() > 0)
            {
                for (RawmaterialStock raw : list)
                {
                    for (int i = 0; i < stockMaStockList.size(); i++)
                    {
                        RawmaterialStock stock = stockMaStockList.get(i);
                        if (raw.getRecordId().equals(stock.getRecordId()))
                        {
                            raw.setSortNum(i);
                            raw.setCurrStocks(stock.getCurrStocks());
                            raw.setInStocks(stock.getInStocks());
                            raw.setOutStocks(stock.getOutStocks());
                            raw.setPrice(stock.getPrice());
                            raw.setCost(stock.getCost());
                            break;
                        }
                    }
                }
            }
        }
        for (RawmaterialStock raw : list)
        {
            if (raw != null && raw.getSortNum() == null)
            {
                raw.setSortNum(0);
            }
        }
        list.sort((x, y) -> Double.compare(x.getSortNum(), y.getSortNum()));
        list.add(0, sto);
        return list;
    }

    // 根据库位和出入库记录滚存数据得到库存相关信息
    public List<MaterialPlaceCom> cumulativeStock(List<MaterialPlaceCom> mpList, List<RawmaterialStock> rsList)
    {
        Integer stockAge;
        List<MaterialPlaceCom> list = new ArrayList<>();
        Iterator<MaterialPlaceCom> it = mpList.iterator();
        while (it.hasNext())
        {
            stockAge = null;
            MaterialPlaceCom materialPlaceCom = it.next();
            // 获取库存进行更新
            if (StringUtils.isNotBlank(materialPlaceCom.getRecordId()))
            {
                BigDecimal changeStocks = BigDecimal.ZERO;
                BigDecimal inStocks = BigDecimal.ZERO;
                BigDecimal outStocks = BigDecimal.ZERO;
                // 期初库存
                if (materialPlaceCom.getPeriodStocks() != null)
                {
                    changeStocks = materialPlaceCom.getPeriodStocks();
                }
                // 计算库存
                if (rsList != null && rsList.size() > 0)
                {
                    for (RawmaterialStock rawmaterialStock : rsList)
                    {
                        // 物料id和库位id都相同的进行计算
                        if (rawmaterialStock.getMaterialId().equals(materialPlaceCom.getMaterialId())
                            && rawmaterialStock.getStockPlaceComId().equals(materialPlaceCom.getRecordId()))
                        {
                            if (rawmaterialStock.getInoutType() != null && rawmaterialStock.getQuantity() != null)
                            {
                                // 出入库数量
                                BigDecimal qty = null == rawmaterialStock.getQuantity() ?
                                    BigDecimal.ZERO :
                                    rawmaterialStock.getQuantity();
                                // 赠送数量
                                BigDecimal giveNum = null == rawmaterialStock.getGiveNum() ?
                                    BigDecimal.ZERO :
                                    rawmaterialStock.getGiveNum();

                                // 采购入库
                                if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_IN)
                                {
                                    // 出入库数量+赠送数量
                                    qty = qty.add(giveNum);
                                    changeStocks = changeStocks.add(qty);
                                    inStocks = inStocks.add(qty);
                                }
                                // 退货出库
                                else if (rawmaterialStock.getInoutType()
                                    == TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT)
                                {
                                    changeStocks = changeStocks.subtract(qty);
                                    outStocks = outStocks.add(qty);
                                }
                                // 原料入库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_OTHER_IN)
                                {
                                    changeStocks = changeStocks.add(qty);
                                    inStocks = inStocks.add(qty);
                                }
                                // 领料出库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_OUT)
                                {
                                    changeStocks = changeStocks.subtract(rawmaterialStock.getQuantity());
                                    outStocks = outStocks.add(qty);
                                }
                                // 补货入库
                                else if (rawmaterialStock.getInoutType()
                                    == TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_REDELIVER)
                                {
                                    changeStocks = changeStocks.add(qty);
                                    inStocks = inStocks.add(qty);
                                }
                                // 投料出库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_FEEDING_OUT)
                                {
                                    changeStocks = changeStocks.subtract(qty);
                                    outStocks = outStocks.add(qty);
                                }
                                // 补料出库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_FEDING_OUT)
                                {
                                    changeStocks = changeStocks.subtract(qty);
                                    outStocks = outStocks.add(qty);
                                }
                                // 移库入库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_MOVE_IN)
                                {
                                    changeStocks = changeStocks.add(qty);
                                    inStocks = inStocks.add(qty);
                                }
                                // 移库出库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_MOVE_OUT)
                                {
                                    changeStocks = changeStocks.subtract(rawmaterialStock.getQuantity());
                                    outStocks = outStocks.add(qty);
                                }
                                // 原料客诉退货入库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_REJECT_IN)
                                {
                                    changeStocks = changeStocks.add(qty);
                                    inStocks = inStocks.add(qty);
                                }
                                // 原料送货出库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_SENTOUT)
                                {
                                    changeStocks = changeStocks.subtract(qty);
                                    outStocks = outStocks.add(qty);
                                }
                                // 查询是按时间顺序的，设置最后操作时间
                                materialPlaceCom.setFinaInOutDate(rawmaterialStock.getOperateDate());
                            }
                        }
                    }
                }
                materialPlaceCom.setChangeStocks(changeStocks);
                materialPlaceCom.setInStocks(inStocks);
                materialPlaceCom.setOutStocks(outStocks);
                // 设置库存未操作的时间，库龄
                if (materialPlaceCom.getFinaInOutDate() != null)
                {
                    stockAge = DateUtils.getDaysMax(materialPlaceCom.getFinaInOutDate(), new Date());
                }
                materialPlaceCom.setStockAge(stockAge);
                list.add(materialPlaceCom);
            }
        }
        return list;
    }

    // 根据物料和采购记录得到期初相关信息
/*    public List<Material> cumulativePrice(List<Material> mpList, List<RawmaterialStock> rsList)
    {
        BigDecimal price;
        List<Material> list = new ArrayList<>();
        // 按照物料进行初始化
        Iterator<Material> it = mpList.iterator();
        while (it.hasNext())
        {
            Material material = it.next();
            price = material.getInitPrice() == null ? BigDecimal.ZERO : material.getInitPrice();
            // 获取库存进行更新
            if (StringUtils.isNotBlank(material.getRecordId()))
            {
                if (rsList != null && rsList.size() > 0)
                {
                    for (RawmaterialStock rawmaterialStock : rsList)
                    {
                        // 物料id相同的进行计算
                        if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_IN
                            && rawmaterialStock.getMaterialId().equals(material.getRecordId()))
                        {
                            // 进行计算加权平均单价，移动加权平均单价计算方法：（（上次加权平均价*剩余数量）+（本次采购单价*本次采购数量））/总数量
                            if (rawmaterialStock.getQuantity() != null
                                && rawmaterialStock.getQuantity().compareTo(BigDecimal.ZERO) > 0)
                            {
                                // 总数量
                                BigDecimal num = BigDecimal.ZERO;
                                // 总金额
                                BigDecimal money;
                                // 本次采购成本
                                money = rawmaterialStock.getCost();
                                num = num.add(rawmaterialStock.getQuantity());
                                // 获取当前物料的库存
                                BigDecimal stockNum =
                                    material.getInitStocks() == null ? BigDecimal.ZERO : material.getInitStocks();
                                // 减去本次采购入库数量
                                stockNum = stockNum.subtract(num);
                                // 进行本次采购后加权平均单价计算
                                if (price.compareTo(BigDecimal.ZERO) > 0 && stockNum.compareTo(BigDecimal.ZERO) > 0)
                                {
                                    money = money.add(price.multiply(stockNum));
                                    num = num.add(stockNum);
                                }
                                price = money.divide(num, 4, BigDecimal.ROUND_HALF_UP);
                            }
                        }
                    }
                }
                material.setPrice(price);
                list.add(material);
            }
        }
        return list;
    }*/

    // 根据物料id和时间获取当前库存
    public BigDecimal getMaterialStock(Material material, Date operDate, Company company)
    {
        BigDecimal stock = BigDecimal.ZERO;
        if (operDate != null)
        {
            // 获取期初数量
            material.setInitDate(operDate);
            material.setCompanyId(company.getRecordId());
            Material initMaterial = materialDao.getInitMaterial(material);
            BigDecimal periodStocks =
                initMaterial.getPeriodStocks() == null ? BigDecimal.ZERO : initMaterial.getPeriodStocks();
            stock = stock.add(periodStocks);
            // 获取该物料从最近期初至参数时间的出入库数量
            Date initDate = initMaterial.getInitDate();
            material.setInitDate(initDate);
            material.setPurDate(operDate);
            BigDecimal dealStock = materialDao.getStocks(material);
            // 将期初加上出入库数量，进行返回
            stock = stock == null ? BigDecimal.ZERO : stock;
            dealStock = dealStock == null ? BigDecimal.ZERO : dealStock;
            stock = stock.add(dealStock);
        }
        return stock;
    }

    // 获取库位id
    public String genStockPlaceCom(PurchRaw purchRaw)
    {
        if (StringUtils.isNotBlank(purchRaw.getStockPlaceId()))
        {
            MaterialPlaceCom materialPlaceCom = new MaterialPlaceCom();
            Company company = purchRaw.getCompany();
            materialPlaceCom.setCompany(company);
            materialPlaceCom.setCompanyId(company.getRecordId());
            materialPlaceCom.setSaleCompanyId(purchRaw.getBranchId());
            materialPlaceCom.setMaterPlaceId(purchRaw.getStockPlaceId());
            materialPlaceCom.setMaterialId(purchRaw.getMaterial().getRecordId());
            // 验证库位、物料、下单公司是否唯一
            String id = storeHouseDao.getMaterialPlaceComId(materialPlaceCom);
            if (StringUtils.isBlank(id))
            {
                materialPlaceCom.preInsert();
                storeHouseDao.insertMaterialPlaceCom(materialPlaceCom);
                return materialPlaceCom.getRecordId();
            }
            else
            {
                return id;
            }
        }
        return null;
    }

    // 获取库位当前可用数量
    public BigDecimal getStockPlaceComStocks(String branchId, String materialId)
    {
        BigDecimal stocks = BigDecimal.ZERO;
        List<MaterialPlaceCom> comList = getMaterialApplyStock(materialId, new Date(), null);
        if (Collections3.isNotEmpty(comList))
        {
            for (MaterialPlaceCom maCom : comList)
            {
                BigDecimal dealStocks = maCom.getPeriodStocks() == null ? BigDecimal.ZERO : maCom.getPeriodStocks();
                if (StringUtils.isNotBlank(branchId))
                {
                    if (StringUtils.isNotBlank(maCom.getSaleCompanyId()))
                    {
                        if (maCom.getSaleCompanyId().equals(branchId))
                        {
                            stocks = stocks.add(dealStocks);
                        }
                    }
                    else
                    {
                        stocks = stocks.add(dealStocks);
                    }
                }
                else
                {
                    stocks = stocks.add(dealStocks);
                }
            }
        }
        return stocks;
    }

    // 对出入库记录进行计算并且给每条出入库记录赋值
    public RawmaterialStock setRawInoutList(List<RawmaterialStock> rawInOutList, BigDecimal changeStock,
        BigDecimal startPrice, String materialId)
    {
        BigDecimal changeStocks = changeStock;
        BigDecimal price = startPrice;
        RawmaterialStock stock = new RawmaterialStock();
        BigDecimal inStocks = BigDecimal.ZERO;
        BigDecimal outStocks = BigDecimal.ZERO;
        for (int j = 0; j < rawInOutList.size(); j++)
        {
            if(null == rawInOutList.get(j).getStatus() || rawInOutList.get(j).getStatus().compareTo(TypeKey.BILL_STATUS_NORMAL) != 0)
            {
                continue;
            }
            if (rawInOutList.get(j).getInoutType() != null)
            {
                BigDecimal qty =
                    null == rawInOutList.get(j).getQuantity() ? BigDecimal.ZERO : rawInOutList.get(j).getQuantity();

                BigDecimal giveNum =
                    null == rawInOutList.get(j).getGiveNum() ? BigDecimal.ZERO : rawInOutList.get(j).getGiveNum();

                rawInOutList.get(j).setCurrStocks(changeStocks);
                // 采购入库
                if (rawInOutList.get(j).getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_IN)
                {
                    // 总数量
                    BigDecimal num = BigDecimal.ZERO;
                    // 总金额
                    BigDecimal money = BigDecimal.ZERO;
                    // 本次采购成本
                    money = rawInOutList.get(j).getCost();
                    qty = qty.add(giveNum);
                    num = num.add(qty);
                    // 获取截止到该采购时间该物料的剩余数量
                    BigDecimal stockNum = changeStocks;
                    // 进行本次采购后加权平均单价计算
                    if (price.compareTo(BigDecimal.ZERO) > 0 && stockNum.compareTo(BigDecimal.ZERO) > 0)
                    {
                        money = money.add(price.multiply(stockNum));
                        num = num.add(stockNum);
                    }
                    if (BigDecimal.ZERO.compareTo(num) != 0)
                    {
                        price = money.divide(num, 4, BigDecimal.ROUND_HALF_UP);
                    }
                    changeStocks = changeStocks.add(qty);
                    inStocks = inStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setInStocks(qty);
                }
                // 退货出库
                else if (rawInOutList.get(j).getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT)
                {
                    changeStocks = changeStocks.subtract(qty);
                    outStocks = outStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setOutStocks(qty);

                    rawInOutList.get(j).setPrice(price);
                    rawInOutList.get(j).setCost(price.multiply(qty));
                }
                // 原料入库
                else if (rawInOutList.get(j).getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_OTHER_IN)
                {
                    changeStocks = changeStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setInStocks(qty);

                    inStocks = inStocks.add(qty);
                    rawInOutList.get(j).setPrice(price);
                    rawInOutList.get(j).setCost(price.multiply(qty));
                }
                // 领料出库
                else if (rawInOutList.get(j).getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_OUT)
                {
                    changeStocks = changeStocks.subtract(qty);
                    outStocks = outStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setOutStocks(qty);

                    rawInOutList.get(j).setPrice(price);
                    rawInOutList.get(j).setCost(price.multiply(qty));
                }
                // 补货入库
                else if (rawInOutList.get(j).getInoutType()
                    == TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_REDELIVER)
                {
                    changeStocks = changeStocks.add(qty);
                    inStocks = inStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setInStocks(qty);

                    rawInOutList.get(j).setPrice(price);
                    rawInOutList.get(j).setCost(price.multiply(qty));
                }
                // 投料出库
                else if (rawInOutList.get(j).getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_FEEDING_OUT)
                {
                    changeStocks = changeStocks.subtract(qty);
                    outStocks = outStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setOutStocks(qty);

                    rawInOutList.get(j).setPrice(price);
                    rawInOutList.get(j).setCost(price.multiply(qty));
                }
                // 补料出库
                else if (rawInOutList.get(j).getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_FEDING_OUT)
                {
                    changeStocks = changeStocks.subtract(qty);
                    outStocks = outStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setOutStocks(qty);

                    rawInOutList.get(j).setPrice(price);
                    rawInOutList.get(j).setCost(price.multiply(qty));
                }
                // 移库入库
                else if (rawInOutList.get(j).getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_MOVE_IN)
                {
                    changeStocks = changeStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setInStocks(qty);

                    inStocks = inStocks.add(qty);
                    rawInOutList.get(j).setPrice(price);
                    rawInOutList.get(j).setCost(price.multiply(qty));
                }
                // 移库出库
                else if (rawInOutList.get(j).getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_MOVE_OUT)
                {
                    changeStocks = changeStocks.subtract(qty);
                    outStocks = outStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setOutStocks(qty);

                    rawInOutList.get(j).setPrice(price);
                    rawInOutList.get(j).setCost(price.multiply(qty));
                } // 客诉退货入库
                else if (rawInOutList.get(j).getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_REJECT_IN)
                {
                    changeStocks = changeStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setInStocks(qty);

                    inStocks = inStocks.add(qty);
                    rawInOutList.get(j).setPrice(price);
                    rawInOutList.get(j).setCost(price.multiply(qty));
                }
                if(changeStocks.compareTo(BigDecimal.ZERO) <= 0)
                {
                    price = BigDecimal.ZERO;
                }
            }
        }
        stock.setChangeStocks(changeStocks);
        stock.setInStocks(inStocks);
        stock.setOutStocks(outStocks);
        stock.setRawList(rawInOutList);
        stock.setCurrPrice(price);
        return stock;
    }

    /**
     * 返回物料集合到指定时间的，期初、账面库存
     *
     * @param materialList getRecordId()
     * @param date         为空时，取服务器当前时间
     * @return
     */
    public List<Material> getMaterialList(List<Material> materialList, Date date)
    {
        String materialIds = "";
        for (Material material : materialList)
        {
            if (StringUtils.isNotBlank(materialIds))
            {
                materialIds = materialIds + "," + material.getRecordId();
            }
            else
            {
                materialIds = material.getRecordId();
            }
        }
        if (date == null)
        {
            date = new Date();
        }
        List<MaterialPlaceCom> comList = getMaterialApplyStock(materialIds, date, null);
        for (int i = 0; i < materialList.size(); i++)
        {
            BigDecimal stocks = BigDecimal.ZERO;
            for (int j = 0; j < comList.size(); j++)
            {
                if (StringUtils.isNotBlank(comList.get(j).getMaterialId()) && comList.get(j)
                    .getMaterialId()
                    .equals(materialList.get(i).getRecordId()))
                {
                    stocks = stocks.add(comList.get(j).getPeriodStocks());
                }
            }
            materialList.get(i).setStocks(stocks);
        }
        return materialList;
    }

    /**
     * 返回物料集合到指定时间的，期初、账面库存
     *
     * @param material
     * @param date         为空时，取服务器当前时间
     * @return
     */
    public Material getMaterialList(Material material, Date date)
    {
        if (date == null)
        {
            date = new Date();
        }
        List<MaterialPlaceCom> comList = getMaterialApplyStock(material.getRecordId(), date, null);

        BigDecimal stocks = BigDecimal.ZERO;
        for (int j = 0; j < comList.size(); j++)
        {
            if (StringUtils.isNotBlank(comList.get(j).getMaterialId()) && comList.get(j).getMaterialId().equals(material.getRecordId()))
            {
                stocks = stocks.add(comList.get(j).getPeriodStocks());
            }
        }
        material.setStocks(stocks);
        return material;
    }

    public void addStocks(BigDecimal occupiedStock, String materialId, String saleId, String notiId, Date operateDate,
        Integer num, RawmaterialStock raw)
    {
        Integer inoutType = null;
        if (num == 1)
        {
            inoutType = TypeKey.ST_MATERIAL_INOUTTYPE_IN;
        }
        else if (num == 2)
        {
            inoutType = TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_REDELIVER;
        }
        else if (num == 4)
        {
            inoutType = TypeKey.ST_MATERIAL_INOUTTYPE_OTHER_IN;
        }
        else if (num == 11)
        {
            inoutType = TypeKey.ST_MATERIAL_INOUTTYPE_REJECT_IN;
        }
        else
        {
            return;
        }
        if (null == occupiedStock || occupiedStock.compareTo(BigDecimal.ZERO) <= 0)
        {
            return;
        }
        List<RawmaterialStock> rawList = Lists.newArrayList();
        Company company = raw.getCompany();
        MaterialPlaceCom materialPlaceCom = new MaterialPlaceCom();
        materialPlaceCom.setMaterialId(materialId);
        materialPlaceCom.setCompanyId(company.getRecordId());
        if (StringUtils.isNotBlank(saleId))
        {
            materialPlaceCom.setSaleCompanyId(saleId);
            List<MaterialPlaceCom> placeComList = storeHouseDao.getMaterialPlaceComListTwo(materialPlaceCom);
            if (Collections3.isNotEmpty(placeComList))
            {
                if (raw != null && StringUtils.isNotBlank(raw.getStockPlaceId()))
                {
                    Boolean addFlag = false;
                    for (MaterialPlaceCom maCom : placeComList)
                    {
                        if (StringUtils.isNotBlank(maCom.getMaterPlaceId()) && maCom.getMaterPlaceId()
                            .equals(raw.getStockPlaceId()))
                        {
                            rawList.add(saveRawStocks(company,
                                materialId,
                                maCom,
                                null,
                                occupiedStock,
                                null,
                                inoutType,
                                raw,
                                null));
                            occupiedStock = BigDecimal.ZERO;
                            addFlag = true;
                            break;
                        }
                    }
                    if (!addFlag)
                    {
                        rawList.add(saveRawStocks(company,
                            materialId,
                            placeComList.get(0),
                            null,
                            occupiedStock,
                            null,
                            inoutType,
                            raw,
                            null));
                        occupiedStock = BigDecimal.ZERO;
                    }
                }
                else
                {
                    rawList.add(saveRawStocks(company,
                        materialId,
                        placeComList.get(0),
                        null,
                        occupiedStock,
                        null,
                        inoutType,
                        raw,
                        null));
                    occupiedStock = BigDecimal.ZERO;
                }
            }
        }
        if (occupiedStock.compareTo(BigDecimal.ZERO) > 0)
        {
            materialPlaceCom.setSaleCompanyId(null);
            List<MaterialPlaceCom> placeComList = storeHouseDao.getMaterialPlaceComListTwo(materialPlaceCom);
            if (Collections3.isNotEmpty(placeComList))
            {
                if (raw != null && StringUtils.isNotBlank(raw.getStockPlaceId()))
                {
                    Boolean addFlag = false;
                    for (MaterialPlaceCom maCom : placeComList)
                    {
                        if (StringUtils.isNotBlank(maCom.getMaterPlaceId()) && maCom.getMaterPlaceId()
                            .equals(raw.getStockPlaceId()))
                        {
                            rawList.add(saveRawStocks(company,
                                materialId,
                                maCom,
                                null,
                                occupiedStock,
                                null,
                                inoutType,
                                raw,
                                null));
                            occupiedStock = BigDecimal.ZERO;
                            addFlag = true;
                            break;
                        }
                    }
                    if (!addFlag)
                    {
                        rawList.add(saveRawStocks(company,
                            materialId,
                            placeComList.get(0),
                            null,
                            occupiedStock,
                            null,
                            inoutType,
                            raw,
                            null));
                        occupiedStock = BigDecimal.ZERO;
                    }
                }
                else
                {
                    rawList.add(saveRawStocks(company,
                        materialId,
                        placeComList.get(0),
                        null,
                        occupiedStock,
                        null,
                        inoutType,
                        raw,
                        null));
                    occupiedStock = BigDecimal.ZERO;
                }
            }
        }
        if (occupiedStock.compareTo(BigDecimal.ZERO) == 0)
        {
            if (Collections3.isNotEmpty(rawList))
            {
                String recordIds = null;
                for (RawmaterialStock rawStock : rawList)
                {
                    // 设置出库默认价格
                    setDefaultRawStockPrice(rawStock);
                    Material mat = rawStock.getMaterial();
                    mat.setStorehouse(rawStock.getStorehouse());
                    MaterialPlaceCom mpc = sentMaterialPlaceCom(company, mat);
                    rawStock.setStockPlaceComId(mpc.getRecordId());
                    rawStock.setStockPlaceId(mpc.getMaterPlaceId());
                    rawmaterialStockDao.insert(rawStock);

                    // 处理采购单明细剩余数量和先进先出记录
                    handlePurDetailFirstInFirstOut(rawStock,1);

                    handlePurchasingDetailDeliverys(rawStock,1);

                    if (StringUtils.isNotBlank(recordIds))
                    {
                        recordIds = recordIds + "," + rawStock.getRecordId();
                    }
                    else
                    {
                        recordIds = rawStock.getRecordId();
                    }
                }
                if (StringUtils.isNotBlank(recordIds))
                {
                    raw.setRecordId(recordIds);
                }
            }
        }
        else
        {
            if (null != raw)
            {
                if (raw.getStatus() == null)
                {
                    raw.setStatus(TypeKey.BILL_STATUS_NORMAL);
                }
                raw.preInsert();
                setDefaultRawStockPrice(raw);
                Material mat = raw.getMaterial();
                mat.setStorehouse(raw.getStorehouse());
                MaterialPlaceCom mpc = sentMaterialPlaceCom(company, mat);
                raw.setStockPlaceComId(mpc.getRecordId());
                raw.setStockPlaceId(mpc.getMaterPlaceId());
                rawmaterialStockDao.insert(raw);

                // 处理采购单明细剩余数量和先进先出记录
                handlePurDetailFirstInFirstOut(raw,1);

                handlePurchasingDetailDeliverys(raw,1);
            }
        }
    }

    // 用作物料台账查单个物料数据
    public List<RawmaterialStock> getRawoMaterialStockPlace(RawmaterialStock rawMaterial, String flag)
    {
        List<RawmaterialStock> rowList = new ArrayList<RawmaterialStock>();
        Company company = UserUtils.getUser().getCompany();
        if (null == company)
        {
            company = rawMaterial.getCompany();
        }
        // 获取期初单价
        BigDecimal startPrice = BigDecimal.ZERO;
        Material materialPrice = new Material();
        materialPrice.setMaterialIds(rawMaterial.getMaterial().getRecordId());
        materialPrice.setInitDate(rawMaterial.getInoutTimeStartQr());
        List<Material> matList = materialDao.getMaterialApplyOneList(materialPrice);
        List<RawmaterialStock> purInoutList = materialDao.getRawmaterialPriceApplyOneList(materialPrice);
        List<Material> mList = cumulativePricTwo(matList, purInoutList);
        if (mList.size() > 0)
        {
            startPrice = mList.get(0).getPrice();
        }
        if (rawMaterial.getMaterial() != null && StringUtils.isNotBlank(rawMaterial.getMaterial().getRecordId()))
        {
            // 如果是按单个库位查询
            if (StringUtils.isNotBlank(rawMaterial.getStockPlaceComId()))
            {
                BigDecimal changeStocks = BigDecimal.ZERO;
                List<MaterialPlaceCom> comList = getMaterialApplyStock(rawMaterial.getMaterial().getRecordId(),
                    rawMaterial.getInoutTimeStartQr(),
                    rawMaterial.getStockPlaceComId());
                if (comList.size() > 0)
                {
                    changeStocks = comList.get(0).getPeriodStocks();
                }
                // 查询出入库记录，给每个出入库记录赋值
                RawmaterialStock stock = new RawmaterialStock();
                stock.setMaterial(rawMaterial.getMaterial());
                stock.setStockPlaceComId(rawMaterial.getStockPlaceComId());
                stock.setInoutTimeStartQr(rawMaterial.getInoutTimeStartQr());
                stock.setInoutTimeEndQr(rawMaterial.getInoutTimeEndQr());
                stock.setCompany(company);
                List<RawmaterialStock> rawInOutList = storeHouseDao.getRawmaterialStocksPlaceComTwo(stock);

                RawmaterialStock rawStock =
                    setRawInoutList(rawInOutList, changeStocks, startPrice, rawMaterial.getMaterial().getRecordId());
                rowList = rawStock.getRawList();
                RawmaterialStock stok = new RawmaterialStock();
                stok.setCurrStocks(changeStocks);

                BigDecimal stocks = BigDecimal.ZERO;
                if (changeStocks != null)
                {
                    stocks = stocks.add(changeStocks);
                }
                if (rawStock.getInStocks() != null)
                {
                    stocks = stocks.add(rawStock.getInStocks());
                }
                if (rawStock.getOutStocks() != null)
                {
                    stocks = stocks.subtract(rawStock.getOutStocks());
                }
                stok.setAllStocks(stocks);
                stok.setInStocks(rawStock.getInStocks());
                stok.setOutStocks(rawStock.getOutStocks());

                stok.setStartPrice(startPrice);
                stok.setCurrPrice(rawStock.getCurrPrice());

                // 查询最新采购单价
                PurchasingDetail purchasingDetail = materialDao.getPurchasingDetailById(rawMaterial.getMaterial());
                stok.setNewPurchPrice(BigDecimal.ZERO);
                if (purchasingDetail != null && purchasingDetail.getPrice() != null)
                {
                    stok.setNewPurchPrice(purchasingDetail.getPrice());
                }
                if (purchasingDetail != null && purchasingDetail.getRawmaterialPrice() != null)
                {
                    stok.setNewPurchPrice(purchasingDetail.getRawmaterialPrice());
                    stok.setUpdatePriceDate(purchasingDetail.getUpdatePriceDate());
                }
                rowList.add(stok);
                return rowList;
            }
            else
            {
                BigDecimal changeStocks = BigDecimal.ZERO;
                List<MaterialPlaceCom> comList = getMaterialApplyStock(rawMaterial.getMaterial().getRecordId(),
                    rawMaterial.getInoutTimeStartQr(),
                    null);
                // 查询每个库位的出入库记录
                for (int i = 0; i < comList.size(); i++)
                {
                    changeStocks = changeStocks.add(comList.get(i).getPeriodStocks());
                }
                // 查询出入库记录，给每个出入库记录赋值
                RawmaterialStock stock = new RawmaterialStock();
                stock.setMaterial(rawMaterial.getMaterial());
                stock.setInoutTimeStartQr(rawMaterial.getInoutTimeStartQr());
                stock.setInoutTimeEndQr(rawMaterial.getInoutTimeEndQr());
                stock.setCompany(company);
                List<RawmaterialStock> rawInOutList = storeHouseDao.getRawmaterialStocksPlaceComTwo(stock);
                RawmaterialStock rawStock =
                    setRawInoutList(rawInOutList, changeStocks, startPrice, rawMaterial.getMaterial().getRecordId());
                rowList = rawStock.getRawList();
                RawmaterialStock stok = new RawmaterialStock();
                stok.setCurrStocks(changeStocks);
                BigDecimal stocks = BigDecimal.ZERO;
                if (changeStocks != null)
                {
                    stocks = stocks.add(changeStocks);
                }
                if (rawStock.getInStocks() != null)
                {
                    stocks = stocks.add(rawStock.getInStocks());
                }
                if (rawStock.getOutStocks() != null)
                {
                    stocks = stocks.subtract(rawStock.getOutStocks());
                }
                stok.setAllStocks(stocks);
                stok.setInStocks(rawStock.getInStocks());
                stok.setOutStocks(rawStock.getOutStocks());

                stok.setStartPrice(startPrice);
                stok.setCurrPrice(rawStock.getCurrPrice());
                if (StringUtils.isNotBlank(flag))
                {
                    // 查询最新采购单价
                    PurchasingDetail purchasingDetail = materialDao.getPurchasingDetailById(rawMaterial.getMaterial());
                    stok.setNewPurchPrice(BigDecimal.ZERO);
                    if (purchasingDetail != null && purchasingDetail.getPrice() != null)
                    {
                        stok.setNewPurchPrice(purchasingDetail.getPrice());
                    }
                    if (purchasingDetail != null && purchasingDetail.getRawmaterialPrice() != null)
                    {
                        stok.setNewPurchPrice(purchasingDetail.getRawmaterialPrice());
                        stok.setUpdatePriceDate(purchasingDetail.getUpdatePriceDate());
                    }
                }
                rowList.add(stok);
                return rowList;
            }
        }
        return null;
    }

    public String subStocks(BigDecimal occupiedStock, String materialId, String saleId, String notiId, Date operateDate,
        Integer num, RawmaterialStock raw, Double remainQty)
    {
        Integer inoutType = null;
        if (num == 3)
        {
            inoutType = TypeKey.ST_MATERIAL_INOUTTYPE_FEEDING_OUT;
        }
        else if (num == 4)
        {
            inoutType = TypeKey.ST_MATERIAL_INOUTTYPE_FEDING_OUT;
        }
        else if (num == 5)
        {
            inoutType = TypeKey.ST_MATERIAL_INOUTTYPE_OUT;
        }
        else if (num == 6)
        {
            inoutType = TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT;
        }
        else if (num == 7)
        {
            inoutType = TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_REDELIVER;
        }
        else
        {
            return null;
        }
        if ((null == occupiedStock || occupiedStock.compareTo(BigDecimal.ZERO) <= 0) && (null == remainQty
            || remainQty <= 0))
        {
            return null;
        }

        BigDecimal allStocks = BigDecimal.ZERO;
        // 获取各库位的具体库存
        List<MaterialPlaceCom> comList = getMaterialApplyStock(materialId, new Date(), null);
        for (int i = 0; i < comList.size(); i++)
        {
            BigDecimal stock =
                comList.get(i).getPeriodStocks() == null ? BigDecimal.ZERO : comList.get(i).getPeriodStocks();
            allStocks = allStocks.add(stock);
        }
        if (allStocks.compareTo(occupiedStock) < 0)
        {
            return "false";
        }

        MaterialPlaceCom maComStock = null;
        List<RawmaterialStock> rawList = Lists.newArrayList();
        Company company = UserUtils.getUser().getCompany();
        MaterialPlaceCom materialPlaceCom = new MaterialPlaceCom();
        materialPlaceCom.setMaterialId(materialId);
        materialPlaceCom.setCompanyId(company.getRecordId());
        if (StringUtils.isNotBlank(saleId))
        {
            if (Collections3.isNotEmpty(comList))
            {
                for (MaterialPlaceCom maCom : comList)
                {
                    BigDecimal stocks = maCom.getPeriodStocks();
                    if (null == stocks || stocks.compareTo(BigDecimal.ZERO) <= 0)
                    {
                        continue;
                    }
                    BigDecimal computStocks = BigDecimal.ZERO;
                    if (stocks.compareTo(occupiedStock) >= 0)
                    {
                        computStocks = occupiedStock;
                        occupiedStock = BigDecimal.ZERO;
                    }
                    else
                    {
                        computStocks = stocks;
                        occupiedStock = occupiedStock.subtract(stocks);
                    }
                    if(computStocks.compareTo(BigDecimal.ZERO) > 0)
                    {
                        rawList.add(saveRawStocks(company,
                            materialId,
                            maCom,
                            notiId,
                            computStocks,
                            operateDate,
                            inoutType,
                            raw,
                            null));
                    }
                    if (occupiedStock.compareTo(BigDecimal.ZERO) <= 0)
                    {
                        break;
                    }
                }
            }
        }
        // 使用工厂的库位库存
        if (occupiedStock.compareTo(BigDecimal.ZERO) > 0)
        {
            if (Collections3.isNotEmpty(comList))
            {
                for (MaterialPlaceCom maCom : comList)
                {
                    BigDecimal stocks = maCom.getPeriodStocks();
                    if (null == stocks || stocks.compareTo(BigDecimal.ZERO) <= 0)
                    {
                        continue;
                    }
                    BigDecimal computStocks = BigDecimal.ZERO;
                    if (stocks.compareTo(occupiedStock) >= 0)
                    {
                        computStocks = occupiedStock;
                        occupiedStock = BigDecimal.ZERO;
                    }
                    else
                    {
                        computStocks = stocks;
                        occupiedStock = occupiedStock.subtract(stocks);
                    }
                    if(computStocks.compareTo(BigDecimal.ZERO) > 0)
                    {
                        rawList.add(saveRawStocks(company,
                            materialId,
                            maCom,
                            notiId,
                            computStocks,
                            operateDate,
                            inoutType,
                            raw,
                            null));
                    }
                    if (occupiedStock.compareTo(BigDecimal.ZERO) <= 0)
                    {
                        break;
                    }

                }
            }
        }

        if (!StringUtils.isEmpty(saleId) && occupiedStock.compareTo(BigDecimal.ZERO) > 0)
        {
            String parameterValue = materialDao.getQueryAllSaleMaterial(saleId);
            // 使用其它销售公司的库存
            if ("1".equals(parameterValue))
            {
                if (Collections3.isNotEmpty(comList))
                {
                    for (MaterialPlaceCom maCom : comList)
                    {
                        if (StringUtils.isNotBlank(maCom.getSaleCompanyId())
                            && !saleId.contains(maCom.getSaleCompanyId()))
                        {
                            BigDecimal stocks = maCom.getPeriodStocks();
                            if (null == stocks || stocks.compareTo(BigDecimal.ZERO) <= 0)
                            {
                                continue;
                            }
                            BigDecimal computStocks = BigDecimal.ZERO;
                            if (stocks.compareTo(occupiedStock) >= 0)
                            {
                                computStocks = occupiedStock;
                                occupiedStock = BigDecimal.ZERO;
                            }
                            else
                            {
                                computStocks = stocks;
                                occupiedStock = occupiedStock.subtract(stocks);
                            }
                            if(computStocks.compareTo(BigDecimal.ZERO) > 0)
                            {
                                rawList.add(saveRawStocks(company,
                                    materialId,
                                    maCom,
                                    notiId,
                                    computStocks,
                                    operateDate,
                                    inoutType,
                                    raw,
                                    null));
                            }
                            if (occupiedStock.compareTo(BigDecimal.ZERO) <= 0)
                            {
                                break;
                            }
                        }
                    }
                }
            }
        }

        if (occupiedStock.compareTo(BigDecimal.ZERO) > 0)
        {
            return "false";
        }
        if (Collections3.isNotEmpty(rawList))
        {
            // 插入出入库记录
            // 设置默认价格
            for (RawmaterialStock rr : rawList)
            {
                setDefaultRawStockPrice(rr);
            }
            for(RawmaterialStock stock : rawList)
            {
                rawmaterialStockDao.bathInsertTwo(stock);
                Integer type = 2;
                if(inoutType.compareTo(TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT) == 0)
                {
                    type = 1;
                }
                handlePurDetailFirstInFirstOut(stock,type);

                handlePurchasingDetailDeliverys(stock, type);
            }
        }
        // 插入余料使用记录
        if (remainQty != null && remainQty > 0)
        {
            RawmaterialStock stock = saveRawStocks(company,
                materialId,
                maComStock,
                notiId,
                BigDecimal.ZERO,
                operateDate,
                inoutType,
                raw,
                remainQty);
            setDefaultRawStockPrice(stock);
            rawmaterialStockDao.insert(stock);
        }
        return null;
    }

    public MaterialPlaceCom sentMaterialPlaceCom(Company company, Material mat,String groupDeptId)
    {
        MaterialPlaceCom mpc = new MaterialPlaceCom();

        // 创建库位
        MaterialPlace mp = new MaterialPlace();
        mp.setCompanyId(company.getRecordId());
        mp.setName("Z-1");
        if (null != mat && null != mat.getStorehouse())
        {
            mp.setStoreId(mat.getStorehouse().getRecordId());
        }
        MaterialPlace query = storeHouseDao.getMaterialPlaceByName(mp);
        if (null == query || StringUtils.isBlank(query.getRecordId()))
        {
            mp.setLandscape("Z");
            mp.setPortait("1");
            mp.preInsert();
            storeHouseDao.insertMaterialPlace(mp);

            mpc.setMaterPlaceId(mp.getRecordId());
        }
        else
        {
            mpc.setMaterPlaceId(query.getRecordId());
        }
        // 创建物料与库位关联
        mpc.setCompanyId(company.getRecordId());
        if (null != mat)
        {
            mpc.setMaterialId(mat.getRecordId());
        }
        mpc.setGroupDeptId(groupDeptId);
        // 获取与库位关联的
        String placeComId = storeHouseDao.getMaterialPlaceComId(mpc);
        if (StringUtils.isNotBlank(placeComId))
        {
            mpc.setRecordId(placeComId);
        }
        else
        {
            mpc.preInsert();
            storeHouseDao.insertMaterialPlaceCom(mpc);
        }
        return mpc;
    }

    public MaterialPlaceCom sentMaterialPlaceCom(Company company, Material mat)
    {
        MaterialPlaceCom mpc = new MaterialPlaceCom();

        // 创建库位
        MaterialPlace mp = new MaterialPlace();
        mp.setCompanyId(company.getRecordId());
        mp.setName("Z-1");
        if (null != mat && null != mat.getStorehouse())
        {
            mp.setStoreId(mat.getStorehouse().getRecordId());
        }
        MaterialPlace query = storeHouseDao.getMaterialPlaceByName(mp);
        if (null == query || StringUtils.isBlank(query.getRecordId()))
        {
            mp.setLandscape("Z");
            mp.setPortait("1");
            mp.preInsert();
            storeHouseDao.insertMaterialPlace(mp);

            mpc.setMaterPlaceId(mp.getRecordId());
        }
        else
        {
            mpc.setMaterPlaceId(query.getRecordId());
        }
        // 创建物料与库位关联
        mpc.setCompanyId(company.getRecordId());
        if (null != mat)
        {
            mpc.setMaterialId(mat.getRecordId());
            ;
        }
        // 获取与库位关联的
        String placeComId = storeHouseDao.getMaterialPlaceComId(mpc);
        if (StringUtils.isNotBlank(placeComId))
        {
            mpc.setRecordId(placeComId);
        }
        else
        {
            mpc.preInsert();
            storeHouseDao.insertMaterialPlaceCom(mpc);
        }
        return mpc;
    }

    public RawmaterialStock saveRawStocks(Company company, String materialId, MaterialPlaceCom maCom, String notiId,
        BigDecimal computStocks, Date operateDate, Integer inoutType, RawmaterialStock raw, Double remainQty)
    {
        if (null == inoutType)
        {
            return null;
        }
        if (raw == null)
        {
            raw = new RawmaterialStock();
        }
        raw.setCompany(company);
        Material mat = new Material();
        if (null != raw && null != raw.getMaterialUse() && null != raw.getMaterialUse().getProduct()
                && StringUtils.isNotBlank(raw.getMaterialUse().getProduct()) && raw.getMaterialUse().getProduct().equals("2"))
        {
            mat = materialDao.getInterProduct(materialId);
        }
        else
        {
            mat = materialDao.get(materialId);
        }
        if (null != mat)
        {
            raw.setMaterial(mat);
            raw.setStorehouse(mat.getStorehouse());
        }
        if (null == raw.getMaterialUse() || null == raw.getMaterialUse().getProduct()
                || StringUtils.isBlank(raw.getMaterialUse().getProduct()) || raw.getMaterialUse().getProduct().equals("1"))
        {
            if(StringUtils.isNotBlank(raw.getGroupDeptId()) && null != maCom && StringUtils.isBlank(maCom.getGroupDeptId()))
            {
                MaterialPlaceCom mpc = sentMaterialPlaceCom(company, mat,raw.getGroupDeptId());
                raw.setStockPlaceComId(mpc.getRecordId());
                raw.setStockPlaceId(mpc.getMaterPlaceId());
            }
            else
            {
                if (maCom != null && StringUtils.isNotBlank(maCom.getRecordId()))
                {
                    raw.setStockPlaceComId(maCom.getRecordId());
                    raw.setStockPlaceId(maCom.getMaterPlaceId());
                }
                else
                {
                    MaterialPlaceCom mpc = sentMaterialPlaceCom(company, mat);
                    raw.setStockPlaceComId(mpc.getRecordId());
                    raw.setStockPlaceId(mpc.getMaterPlaceId());
                }
            }
        }


        if (StringUtils.isNotBlank(notiId))
        {
            raw.setNotification(new Notification(notiId));
        }
        raw.setQuantity(computStocks);
        raw.setInoutType(inoutType);
        raw.setStatus(TypeKey.BILL_STATUS_NORMAL);
        if (null != operateDate)
        {
            raw.setOperateDate(operateDate);
        }
        else
        {
            raw.setOperateDate(new Date());
        }
        if (remainQty != null)
        {
            raw.setRemainQty(remainQty);
        }

        RawmaterialStock rawStock = (RawmaterialStock)raw.clone();
        rawStock.preInsert();
        if (null == remainQty)
        {
            rawStock.setRemainQty(new Double(0));
        }
        return rawStock;
    }

    private String strs(String str)
    {
        if (str.indexOf(".") > 0)
        {
            str = str.replaceAll("0+?$", "");// 删掉尾数为0的字符
            str = str.replaceAll("[.]$", "");// 结尾如果是小数点，则去掉
        }
        return str;
    }

    public List<RawmaterialStock> dealStockAgeData(RawmaterialStock rawMaterial, Date start, Date end)
    {
        Company company = UserUtils.getUser().getCompany();
        Material material = new Material(rawMaterial.getRecordId());
        rawMaterial.setMaterial(material);
        rawMaterial.setCompany(company);
        rawMaterial.setInoutTimeStartQr(start);
        rawMaterial.setInoutTimeEndQr(end);
        List<RawmaterialStock> list = dealPlace(rawMaterial, null);
        // 授予待使用库存
        for (int i = 0; i < list.size(); i++)
        {
            if (list.get(i).getOperateDate() != null)
            {
                String dealTime = DateUtils.formatDate(list.get(i).getOperateDate());
                list.get(i).setShowTime(dealTime);
            }
            if (list.get(i).getOperateDate() == null)
            {
                list.get(i).setWaitUseStocks(list.get(i).getCurrStocks());
            }
            else if (list.get(i).getInStocks() != null && list.get(i).getOperateDate() != null)
            {

                if (list.get(i).getInStocks().compareTo(BigDecimal.ZERO) < 0)
                {
                    // 入库冲红 入库数量为负数的情况
                    list.get(i).setWaitUseStocks(BigDecimal.ZERO);// 待出库不存在
                    list.get(i).setOutStocks(BigDecimal.ZERO.subtract(list.get(i).getInStocks()));// 入库负数转化为出库量
                    list.get(i).setInStocks(BigDecimal.ZERO);// 清空入库负数
                }
                else
                {
                    list.get(i).setWaitUseStocks(list.get(i).getInStocks());
                }

            }
            else if (list.get(i).getOutStocks() != null && list.get(i).getOperateDate() != null
                && list.get(i).getOutStocks().compareTo(BigDecimal.ZERO) < 0)
            {
                // 出库冲红 出库为负数转为入库计算
                list.get(i).setInStocks(BigDecimal.ZERO.subtract(list.get(i).getOutStocks()));
                list.get(i).setWaitUseStocks(list.get(i).getInStocks());
                list.get(i).setOutStocks(BigDecimal.ZERO);

            }
        }
        // 进行库龄处理
        List<RawmaterialStock> showList = dealStockAge(list);
        return showList;
    }

    public List<RawmaterialStock> dealStockAge(RawmaterialStock rawMaterial, Date start, Date end)
    {
        List<RawmaterialStock> showList = dealStockAgeData(rawMaterial, start, end);
        // 结余库龄处理
        RawmaterialStock stock = nowStockAge(showList);
        if (stock != null)
        {
            showList.add(stock);
        }
        // 将出入库汇总成日报表
        List<RawmaterialStock> reportList = dayStockAge(showList);
        // 汇总处理
        RawmaterialStock stockAll = addAllStockAge(reportList);
        if (stockAll != null)
        {
            reportList.add(stockAll);
        }
        return reportList;
    }

    public RawmaterialStock addAllStockAge(List<RawmaterialStock> list)
    {
        RawmaterialStock stock = new RawmaterialStock();
        BigDecimal money = BigDecimal.ZERO;
        if (list.size() > 0)
        {
            for (int i = 0; i < list.size(); i++)
            {
                BigDecimal moneyRadio =
                    list.get(i).getMoneyRadio() == null ? BigDecimal.ZERO : list.get(i).getMoneyRadio();
                money = money.add(moneyRadio);
            }
            stock.setMaterial(list.get(0).getMaterial());
        }
        stock.setOperateDate(new Date());
        stock.setMoneyRadio(money);
        stock.setShowTime("汇总");
        return stock;
    }

    public RawmaterialStock nowStockAge(List<RawmaterialStock> list)
    {
        if (list.size() > 0)
        {
            // 处理结余库龄
            String msg = "";
            // 最长库龄
            Integer stockAge = 0;
            BigDecimal usePrice = BigDecimal.ZERO;
            String time = DateUtils.formatDate(new Date());
            BigDecimal moneyRadio = BigDecimal.ZERO;
            RawmaterialStock stock = new RawmaterialStock();
            boolean ageFlag = true;

            if (list.get(list.size() - 1).getOperateDate() != null && DateUtils.formatDate(list.get(list.size() - 1)
                .getOperateDate()).equals(time))
            {
                msg = list.get(list.size() - 1).getStockAgeMsg();
            }
            else
            {
                stock.setShowTime(time);
                stock.setCurrStocks(list.get(list.size() - 1).getCurrStocks());
                stock.setPrice(list.get(list.size() - 1).getPrice());
                stock.setOperateDate(new Date());

                BigDecimal coutCurrStocks = list.get(list.size() - 1).getCurrStocks() == null ?
                    BigDecimal.ZERO :
                    list.get(list.size() - 1).getCurrStocks();
                BigDecimal money = BigDecimal.ZERO;
                usePrice = list.get(list.size() - 1).getPrice() == null ?
                    BigDecimal.ZERO :
                    list.get(list.size() - 1).getPrice();
                if (coutCurrStocks != null)
                {
                    money = usePrice.multiply(coutCurrStocks);
                    stock.setMoney(money);
                }
            }

            for (int i = 0; i < list.size(); i++)
            {
                if (list.get(i).getWaitUseStocks() != null
                    && list.get(i).getWaitUseStocks().compareTo(BigDecimal.ZERO) > 0)
                {
                    stockAge = 0;
                    if (list.get(i).getOperateDate() != null)
                    {
                        String showTime = DateUtils.formatDate(list.get(i).getOperateDate(), "yyyy-MM-dd HH:mm:ss");
                        stockAge = DateUtils.getDaysMax(list.get(i).getOperateDate(), new Date());
                        if (StringUtils.isNotBlank(msg))
                        {
                            msg = msg + "," + showTime + "剩余数量" + list.get(i).getWaitUseStocks();
                        }
                        else
                        {
                            msg = showTime + "剩余数量" + list.get(i).getWaitUseStocks();
                        }
                        usePrice = list.get(i).getPrice() == null ? BigDecimal.ZERO : list.get(i).getPrice();
                    }
                    else
                    {
                        Date start = DateUtils.stringToDate(2021, 0, 1);
                        stockAge = DateUtils.getDaysMax(start, new Date());
                        if (StringUtils.isNotBlank(msg))
                        {
                            msg = msg + ",期初2021-01-01剩余数量" + list.get(i).getWaitUseStocks();
                        }
                        else
                        {
                            msg = "期初2021-01-01剩余数量" + list.get(i).getWaitUseStocks();
                        }
                        usePrice = list.get(i).getStartPrice() == null ? BigDecimal.ZERO : list.get(i).getStartPrice();
                    }
                    if (ageFlag)
                    {
                        stock.setStockAge(stockAge);
                        ageFlag = false;
                    }

                    moneyRadio = moneyRadio.add(usePrice.multiply(list.get(i).getWaitUseStocks())
                        .multiply(new BigDecimal(stockAge)));
                    msg = msg + "-单价" + usePrice + "-库龄" + stockAge;
                }
            }
            if (moneyRadio.compareTo(BigDecimal.ZERO) > 0)
            {
                moneyRadio = moneyRadio.multiply(new BigDecimal(0.022))
                    .divide(new BigDecimal(100))
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            if (list.get(list.size() - 1).getOperateDate() != null && DateUtils.formatDate(list.get(list.size() - 1)
                .getOperateDate()).equals(time))
            {
                list.get(list.size() - 1).setStockAgeMsg(msg);
                BigDecimal moneySelf = list.get(list.size() - 1).getMoneyRadio();
                moneyRadio = moneyRadio == null ? BigDecimal.ZERO : moneyRadio;
                moneySelf = moneySelf == null ? BigDecimal.ZERO : moneySelf;
                list.get(list.size() - 1).setMoneyRadio(moneyRadio.add(moneySelf));
            }
            else
            {
                stock.setStockAgeMsg(msg);
                stock.setMoneyRadio(moneyRadio);
                stock.setMaterial(list.get(0).getMaterial());
                return stock;
            }
        }
        return null;
    }

    public List<RawmaterialStock> dayStockAge(List<RawmaterialStock> list)
    {
        // 移动库龄日清单列表
        List<RawmaterialStock> showList = new ArrayList<RawmaterialStock>();
        String dealTime = null;
        for (int i = 0; i < list.size(); i++)
        {
            if (list.get(i).getOperateDate() != null)
            {
                BigDecimal inStocks = list.get(i).getInStocks() == null ? BigDecimal.ZERO : list.get(i).getInStocks();
                BigDecimal outStocks =
                    list.get(i).getOutStocks() == null ? BigDecimal.ZERO : list.get(i).getOutStocks();
                Double remainQty = list.get(i).getRemainQty() == null ? 0d : list.get(i).getRemainQty();
                BigDecimal currStocks =
                    list.get(i).getCurrStocks() == null ? BigDecimal.ZERO : list.get(i).getCurrStocks();
                BigDecimal price = list.get(i).getPrice() == null ? BigDecimal.ZERO : list.get(i).getPrice();
                BigDecimal waitUseStocks =
                    list.get(i).getWaitUseStocks() == null ? BigDecimal.ZERO : list.get(i).getWaitUseStocks();
                boolean newFlag = true;
                dealTime = DateUtils.formatDate(list.get(i).getOperateDate());
                Date operatedate = list.get(i).getOperateDate();
                BigDecimal money = list.get(i).getMoney() == null ? BigDecimal.ZERO : list.get(i).getMoney();
                BigDecimal moneyRadio =
                    list.get(i).getMoneyRadio() == null ? BigDecimal.ZERO : list.get(i).getMoneyRadio();
                String msg = list.get(i).getStockAgeMsg();
                for (int j = 0; j < showList.size(); j++)
                {
                    if (StringUtils.isNotBlank(showList.get(j).getShowTime()) && showList.get(j)
                        .getShowTime()
                        .equals(dealTime))
                    {
                        newFlag = false;
                        // 累积数据
                        BigDecimal countInstok =
                            showList.get(j).getInStocks() == null ? BigDecimal.ZERO : showList.get(j).getInStocks();
                        countInstok = countInstok.add(inStocks);
                        showList.get(j).setInStocks(countInstok);

                        BigDecimal countOutstok =
                            showList.get(j).getOutStocks() == null ? BigDecimal.ZERO : showList.get(j).getOutStocks();
                        countOutstok = countOutstok.add(outStocks);
                        showList.get(j).setOutStocks(countOutstok);

                        Double coutRemainQty =
                            showList.get(j).getRemainQty() == null ? 0d : showList.get(j).getRemainQty();
                        coutRemainQty = coutRemainQty + remainQty;
                        showList.get(j).setRemainQty(coutRemainQty);

                        showList.get(j).setCurrStocks(currStocks);

                        showList.get(j).setPrice(price);

                        BigDecimal countWaitUseStocks = showList.get(j).getWaitUseStocks() == null ?
                            BigDecimal.ZERO :
                            showList.get(j).getWaitUseStocks();
                        countWaitUseStocks = countWaitUseStocks.add(waitUseStocks);
                        showList.get(j).setWaitUseStocks(countWaitUseStocks);

                        showList.get(j).setMoney(money);
                        BigDecimal countMoneyRadio =
                            showList.get(j).getMoneyRadio() == null ? BigDecimal.ZERO : showList.get(j).getMoneyRadio();
                        countMoneyRadio = countMoneyRadio.add(moneyRadio);
                        showList.get(j).setMoneyRadio(countMoneyRadio);
                        if (StringUtils.isNotBlank(msg))
                        {
                            String countMsg = showList.get(j).getStockAgeMsg();
                            if (StringUtils.isNotBlank(countMsg))
                            {
                                countMsg = countMsg + "," + msg;
                            }
                            else
                            {
                                countMsg = msg;
                            }
                            showList.get(j).setStockAgeMsg(countMsg);
                        }
                    }
                }
                if (newFlag)
                {
                    // 创建数据
                    RawmaterialStock stock = new RawmaterialStock();
                    stock.setShowTime(dealTime);
                    stock.setInStocks(inStocks);
                    stock.setOutStocks(outStocks);
                    stock.setRemainQty(remainQty);
                    stock.setCurrStocks(currStocks);
                    stock.setPrice(price);
                    stock.setWaitUseStocks(waitUseStocks);
                    stock.setOperateDate(operatedate);
                    stock.setMoney(money);
                    stock.setMoneyRadio(moneyRadio);
                    stock.setStockAgeMsg(msg);
                    stock.setMaterial(list.get(i).getMaterial());
                    showList.add(stock);
                }
            }
            else
            {
                // 设置期初
                showList.add(list.get(i));
            }
        }
        return showList;
    }

    public List<RawmaterialStock> dealStockAge(List<RawmaterialStock> list)
    {
        for (int i = 0; i < list.size(); i++)
        {
            if (list.get(i).getShowTime() != null)
            {
                BigDecimal coutCurrStocks =
                    list.get(i).getCurrStocks() == null ? BigDecimal.ZERO : list.get(i).getCurrStocks();
                BigDecimal money;
                BigDecimal moneyRadio = BigDecimal.ZERO;
                BigDecimal usePrice = list.get(i).getPrice() == null ? BigDecimal.ZERO : list.get(i).getPrice();
                if (coutCurrStocks != null)
                {
                    money = usePrice.multiply(coutCurrStocks);
                    list.get(i).setMoney(money);
                }
                // 找当前剩余库存分配入库日期
                Integer stockAge = 0;
                for (int j = 0; j <= i; j++)
                {
                    if (list.get(j).getWaitUseStocks() != null
                        && list.get(j).getWaitUseStocks().compareTo(BigDecimal.ZERO) > 0)
                    {
                        String msg = "";
                        if (list.get(j).getOperateDate() != null)
                        {
                            // 最长的出库库龄
                            stockAge = DateUtils.getDaysMax(list.get(j).getOperateDate(), list.get(i).getOperateDate());
                            list.get(i).setStockAge(stockAge);
                        }
                        else
                        {
                            Date start = DateUtils.stringToDate(2021, 0, 1);
                            stockAge = DateUtils.getDaysMax(start, list.get(i).getOperateDate());
                            list.get(i).setStockAge(stockAge);
                        }
                        if (list.get(i).getOutStocks() != null
                            && list.get(i).getOutStocks().compareTo(BigDecimal.ZERO) > 0)
                        {
                            BigDecimal waitOutStocks = list.get(i).getOutStocks();
                            BigDecimal useStocks = BigDecimal.ZERO;
                            msg = "出库" + list.get(i).getOutStocks() + ",";
                            if (list.get(j).getWaitUseStocks().compareTo(list.get(i).getOutStocks()) > 0)
                            {
                                useStocks = list.get(i).getOutStocks();
                                BigDecimal waitUseStocks =
                                    list.get(j).getWaitUseStocks().subtract(list.get(i).getOutStocks());
                                list.get(j).setWaitUseStocks(waitUseStocks);
                            }
                            else
                            {
                                useStocks = list.get(j).getWaitUseStocks();
                                list.get(j).setWaitUseStocks(BigDecimal.ZERO);
                            }
                            if (list.get(j).getShowTime() == null)
                            {
                                msg = msg + useStocks + "按期初2021-01-01计息";
                                usePrice =
                                    list.get(j).getStartPrice() == null ? BigDecimal.ZERO : list.get(j).getStartPrice();
                            }
                            else
                            {
                                String showTime =
                                    DateUtils.formatDate(list.get(i).getOperateDate(), "yyyy-MM-dd HH:mm:ss");
                                msg = msg + useStocks + "按" + showTime + "计息";
                                usePrice = list.get(j).getPrice() == null ? BigDecimal.ZERO : list.get(j).getPrice();
                            }
                            if (useStocks != null)
                            {
                                moneyRadio = usePrice.multiply(useStocks).multiply(new BigDecimal(stockAge));
                            }
                            msg = msg + ",计算单价" + usePrice + ",库龄" + stockAge;
                            waitOutStocks = waitOutStocks.subtract(useStocks);
                            int startIndex = j;
                            while (waitOutStocks.compareTo(BigDecimal.ZERO) > 0 && startIndex < i)
                            {
                                for (int p = startIndex; p <= i; p++)
                                {
                                    if (list.get(p).getWaitUseStocks() != null
                                        && list.get(p).getWaitUseStocks().compareTo(BigDecimal.ZERO) > 0)
                                    {
                                        if (list.get(p).getOperateDate() != null)
                                        {
                                            // 最长的出库库龄
                                            stockAge = DateUtils.getDaysMax(list.get(p).getOperateDate(),
                                                list.get(i).getOperateDate());
                                        }
                                        else
                                        {
                                            Date start = DateUtils.stringToDate(2021, 0, 1);
                                            stockAge = DateUtils.getDaysMax(start, list.get(i).getOperateDate());
                                        }

                                        BigDecimal useStockTwo = BigDecimal.ZERO;
                                        if (list.get(p).getWaitUseStocks().compareTo(waitOutStocks) > 0)
                                        {
                                            useStockTwo = waitOutStocks;
                                            BigDecimal waitUseStocks =
                                                list.get(p).getWaitUseStocks().subtract(waitOutStocks);
                                            list.get(p).setWaitUseStocks(waitUseStocks);
                                        }
                                        else
                                        {
                                            useStockTwo = list.get(p).getWaitUseStocks();
                                            list.get(p).setWaitUseStocks(BigDecimal.ZERO);
                                        }
                                        if (list.get(p).getShowTime() == null)
                                        {
                                            msg = msg + "," + useStockTwo + "按期初2021-01-01计息";
                                            usePrice = list.get(p).getStartPrice() == null ?
                                                BigDecimal.ZERO :
                                                list.get(p).getStartPrice();
                                        }
                                        else
                                        {
                                            String showTime = DateUtils.formatDate(list.get(i).getOperateDate(),
                                                "yyyy-MM-dd HH:mm:ss");
                                            msg = msg + "," + useStockTwo + "按" + showTime + "计息";
                                            usePrice = list.get(p).getPrice() == null ?
                                                BigDecimal.ZERO :
                                                list.get(p).getPrice();
                                        }
                                        if (useStockTwo != null)
                                        {
                                            moneyRadio = moneyRadio.add(usePrice.multiply(useStockTwo)
                                                .multiply(new BigDecimal(stockAge)));
                                        }
                                        msg = msg + ",计算单价" + usePrice + ",库龄" + stockAge;
                                        waitOutStocks = waitOutStocks.subtract(useStockTwo);
                                        startIndex = p;
                                        break;
                                    }
                                    if (p == i)
                                    {
                                        startIndex = p;
                                    }
                                }
                            }
                        }
                        list.get(i).setStockAgeMsg(msg);
                        break;
                    }
                }
                if (moneyRadio.compareTo(BigDecimal.ZERO) > 0)
                {
                    // 每笔明细保留2位小数，后续累，不会有差异
                    moneyRadio = moneyRadio.multiply(new BigDecimal(0.022))
                        .divide(new BigDecimal(100))
                        .setScale(2, BigDecimal.ROUND_HALF_UP);
                    list.get(i).setMoneyRadio(moneyRadio);
                }
            }
        }
        return list;
    }

    /**
     * 出库和调拨入库，没有价格时，设置默认值：取当前加权平均价
     *
     * @param rs
     */
    public void setDefaultRawStockPrice(RawmaterialStock rs)
    {
        if (needSetDefaultPrice(rs))
        {
            String materialId = rs.getMaterialId();
            if (rs.getMaterial() != null && !StringUtils.isEmpty(rs.getMaterial().getRecordId()))
            {
                materialId = rs.getMaterial().getRecordId();
            }
            // 设置加权平均价
            rs.setPrice(getWAAPrice(materialId, rs.getOperateDate()));
            if (rs.getPrice() != null && rs.getQuantity() != null)
            {
                rs.setCost(rs.getPrice().multiply(rs.getQuantity()).setScale(4, BigDecimal.ROUND_HALF_UP));
            }
        }
    }

    /**
     * 获取物料指定时间的加权平均价
     *
     * @param materialId 物料id
     * @param time       指定时间
     * @return
     */
    public BigDecimal getWAAPrice(String materialId, Date time)
    {
        Material materialPrice = new Material();
        materialPrice.setMaterialIds(materialId);
        materialPrice.setInitDate(time);
        List<Material> matList = materialDao.getMaterialApplyOneList(materialPrice);
        if (CollectionUtils.isEmpty(matList))
        {
            return null;
        }
        materialPrice.setStartDate(matList.get(0).getInitDate());
        List<RawmaterialStock> purInoutList = materialDao.getRawmaterialStockApplyTwoList(materialPrice);
        Company company = new Company();
        company.setRecordId(matList.get(0).getCompanyId());
        List<Material> mList = cumulativePricTwo(matList, purInoutList);
        return CollectionUtils.isEmpty(mList) ? null : mList.get(0).getPrice();
    }

    /**
     * 出入库记录，是否需要设置默认价格
     *
     * @param rs
     * @return
     */
    public Boolean needSetDefaultPrice(RawmaterialStock rs)
    {
        if (rs == null)
        {
            return false;
        }

        /*
         * if(!rs.getIsNewRecord()) { return false; }
         */

        if (rs.getPrice() != null)
        {
            return false;
        }

        if (StringUtils.isEmpty(rs.getMaterialId()) && rs.getMaterial() == null && StringUtils.isEmpty(rs.getMaterial()
            .getRecordId()))
        {
            return false;
        }

        if (null == rs.getOperateDate())
        {
            return false;
        }

        if (rs.getInoutType() != null)
        {
            // 出库
            if (rs.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_FEEDING_OUT)
            {
                return true;
            }
            if (rs.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT)
            {
                return true;
            }
            if (rs.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_FEDING_OUT)
            {
                return true;
            }
            if (rs.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_OUT)
            {
                return true;
            }
            // 调拨
            if (rs.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_MOVE_IN)
            {
                return true;
            }
            if (rs.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_MOVE_OUT)
            {
                return true;
            }
        }

        return false;
    }

    public List<Material> getIcloudMaterialApplyList(List<Material> materialList, List<MaterialPlaceCom> comList,
        List<RawmaterialStock> rsList)
    {
        // 得到库位结存
        List<MaterialPlaceCom> list = cumulativeStock(comList, rsList);
        // 将库位结存合并成物料库存
        for (int i = 0; i < materialList.size(); i++)
        {
            BigDecimal stocks = BigDecimal.ZERO;
            BigDecimal periodStocks = BigDecimal.ZERO;
            BigDecimal inStocks = BigDecimal.ZERO;
            BigDecimal outStocks = BigDecimal.ZERO;
            String msg = "";
            for (int j = 0; j < list.size(); j++)
            {
                // 物料id相等的话进行拼接数据
                if (list.get(j).getMaterialId().equals(materialList.get(i).getRecordId()))
                {
                    if (list.get(j).getChangeStocks() != null
                        && list.get(j).getChangeStocks().compareTo(BigDecimal.ZERO) > 0)
                    {
                        stocks = stocks.add(list.get(j).getChangeStocks());

                        if (StringUtils.isNotBlank(list.get(j).getComName()))
                        {
                            String changeStocksStr = strs(list.get(j).getChangeStocks().toString());
                            if (StringUtils.isNotBlank(msg))
                            {
                                msg = msg + "/" + list.get(j).getComName() + ":" + changeStocksStr;
                            }
                            else
                            {
                                msg = list.get(j).getComName() + ":" + changeStocksStr;
                            }
                        }
                    }
                    if (list.get(j).getPeriodStocks() != null
                        && list.get(j).getPeriodStocks().compareTo(BigDecimal.ZERO) > 0)
                    {
                        periodStocks = periodStocks.add(list.get(j).getPeriodStocks());
                    }
                    if (list.get(j).getInStocks() != null && list.get(j).getInStocks().compareTo(BigDecimal.ZERO) > 0)
                    {
                        inStocks = inStocks.add(list.get(j).getInStocks());
                    }
                    if (list.get(j).getOutStocks() != null && list.get(j).getOutStocks().compareTo(BigDecimal.ZERO) > 0)
                    {
                        outStocks = outStocks.add(list.get(j).getOutStocks());
                    }
                }
            }
            BigDecimal occStocks = materialList.get(i).getMomentumExpected();
            materialList.get(i).setResidueStocks(stocks);
            materialList.get(i).setInStocks(inStocks);
            materialList.get(i).setOutStocks(outStocks);
            materialList.get(i).setPeriodStocks(periodStocks);
            stocks = stocks == null ? BigDecimal.ZERO : stocks;
            occStocks = occStocks == null ? BigDecimal.ZERO : occStocks;
            BigDecimal canUseStocks = BigDecimal.ZERO;
            if (stocks.compareTo(occStocks) > 0)
            {
                canUseStocks = stocks.subtract(occStocks);
            }
            materialList.get(i).setStocks(canUseStocks);
        }
        return materialList;
    }

    // 根据物料和采购记录得到期初相关信息
    public List<Material> cumulativePricTwo(List<Material> mpList, List<RawmaterialStock> rsList)
    {
        BigDecimal price = null;
        List<Material> list = new ArrayList<>();
        // 按照物料进行初始化
        Iterator<Material> it = mpList.iterator();
        while (it.hasNext())
        {
            Material material = it.next();
            price = material.getInitPrice() == null ? BigDecimal.ZERO : material.getInitPrice();
            if(null == material.getInitStocks())
            {
                material.setInitStocks(BigDecimal.ZERO);
            }
            // 获取库存进行更新
            if (StringUtils.isNotBlank(material.getRecordId()))
            {
                if (rsList != null && rsList.size() > 0)
                {
                    for (RawmaterialStock rawmaterialStock : rsList)
                    {
                        BigDecimal qty = null == rawmaterialStock.getQuantity() ? BigDecimal.ZERO : rawmaterialStock.getQuantity();
                        // 物料id相同的进行计算
                        if(rawmaterialStock.getMaterialId().equals(material.getRecordId()))
                        {
                            if(material.getInitStocks().compareTo(BigDecimal.ZERO) <= 0)
                            {
                                price = BigDecimal.ZERO;
                            }
                            if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_IN || rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_OTHER_IN)
                            {
                                // 进行计算加权平均单价，移动加权平均单价计算方法：（（上次加权平均价*剩余数量）+（本次采购单价*本次采购数量））/总数量
                                if (rawmaterialStock.getQuantity() != null
                                    && rawmaterialStock.getQuantity().compareTo(BigDecimal.ZERO) > 0)
                                {
                                    // 总数量
                                    BigDecimal num = BigDecimal.ZERO;
                                    // 总金额
                                    BigDecimal money;
                                    // 本次采购成本
                                    money = null == rawmaterialStock.getCost() ? BigDecimal.ZERO : rawmaterialStock.getCost();
                                    num = num.add(rawmaterialStock.getQuantity());
                                    // 获取当前物料的库存
                                    BigDecimal stockNum = material.getInitStocks();
                                    // 进行本次采购后加权平均单价计算
                                    if (price.compareTo(BigDecimal.ZERO) > 0 && stockNum.compareTo(BigDecimal.ZERO) > 0)
                                    {
                                        money = money.add(price.multiply(stockNum));
                                        num = num.add(stockNum);
                                    }
                                    price = money.divide(num, 4, BigDecimal.ROUND_HALF_UP);
                                    material.setInitStocks(material.getInitStocks().add(qty));
                                }
                            }
                            else
                            {
                                // 退货出库
                                if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT)
                                {
                                    material.setInitStocks(material.getInitStocks().subtract(qty));
                                }
                                // 原料入库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_OTHER_IN)
                                {
                                    material.setInitStocks(material.getInitStocks().add(qty));
                                }
                                // 领料出库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_OUT)
                                {
                                    material.setInitStocks(material.getInitStocks().subtract(qty));
                                }
                                // 补货入库
                                else if (rawmaterialStock.getInoutType()
                                    == TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_REDELIVER)
                                {
                                    material.setInitStocks(material.getInitStocks().add(qty));
                                }
                                // 投料出库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_FEEDING_OUT)
                                {
                                    material.setInitStocks(material.getInitStocks().subtract(qty));
                                }
                                // 补料出库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_FEDING_OUT)
                                {
                                    material.setInitStocks(material.getInitStocks().subtract(qty));
                                }
                                // 移库入库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_MOVE_IN)
                                {
                                    material.setInitStocks(material.getInitStocks().add(qty));
                                }
                                // 移库出库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_MOVE_OUT)
                                {
                                    material.setInitStocks(material.getInitStocks().subtract(qty));
                                }
                                // 原料客诉退货入库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_REJECT_IN)
                                {
                                    material.setInitStocks(material.getInitStocks().add(qty));
                                }
                                // 原料送货出库
                                else if (rawmaterialStock.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_SENTOUT)
                                {
                                    material.setInitStocks(material.getInitStocks().subtract(qty));
                                }
                            }
                        }
                    }
                }
                material.setPrice(price);
                list.add(material);
            }
        }
        return list;
    }

    /**
     * zjn 2024-10-17 处理采购单明细剩余数量和先进先出记录
     * @param raw 原料出入库记录
     * @param type 类型(1:入库，2:出库，3:冲红)
     * */
    public void handlePurDetailFirstInFirstOut(RawmaterialStock raw,Integer type)
    {
        if(null == raw || null == type)
        {
            return;
        }
        switch (type)
        {
            // 入库
            case 1:
                // 采购入库、补货入库、退货出库
                if(TypeKey.ST_MATERIAL_INOUTTYPE_IN.compareTo(raw.getInoutType()) == 0)
                {
                    List<RawmaterialStock> updateList = Lists.newArrayList();

                    // 入库赠品数量
                    BigDecimal giveNum = (raw.getGiveNum() == null || raw.getGiveNum().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO : raw.getGiveNum();

                    // 入库数量
                    BigDecimal qty = (raw.getQuantity() == null || raw.getQuantity().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO : raw.getQuantity();
                    raw.setSurplusStock(qty.add(giveNum));
                    updateList.add(raw);

/*                    if(TypeKey.ST_MATERIAL_INOUTTYPE_IN.compareTo(raw.getInoutType()) == 0)
                    {
                        raw.setSurplusStock(qty.add(giveNum));
                        updateList.add(raw);
                    }
                    else
                    {
                        // 获取采购入库记录剩余数量
                        RawmaterialStock query = new RawmaterialStock();
                        query.setCompanyId(raw.getCompany().getRecordId());
                        query.setPurchasingDtlId(raw.getPurchasingDtlId());
                        List<RawmaterialStock> rawStockList = rawmaterialStockDao.getWarehousingListTwo(query);
                        if(Collections3.isNotEmpty(rawStockList))
                        {
                            return;
                        }
                        for(RawmaterialStock obj : rawStockList)
                        {
                            BigDecimal surplusStock = null == obj.getSurplusStock() ? BigDecimal.ZERO : obj.getSurplusStock();
                            BigDecimal saveSurplusStock = BigDecimal.ZERO;
                            if(TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT.compareTo(raw.getInoutType()) == 0)
                            {
                                if(surplusStock.compareTo(BigDecimal.ZERO) <= 0)
                                {
                                    continue;
                                }
                                if(qty.compareTo(surplusStock) > 0)
                                {
                                    qty = qty.subtract(surplusStock);
                                }
                                else
                                {
                                    saveSurplusStock = saveSurplusStock.subtract(qty);
                                    qty = BigDecimal.ZERO;
                                }
                            }
                            else if(TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_REDELIVER.compareTo(raw.getInoutType()) == 0)
                            {
                                BigDecimal quantity = null == obj.getQuantity() ? BigDecimal.ZERO : obj.getQuantity();
                                BigDecimal computQty = quantity.subtract(surplusStock);
                                if(computQty.compareTo(BigDecimal.ZERO) <= 0)
                                {
                                    continue;
                                }
                                if(qty.compareTo(computQty) > 0)
                                {
                                    qty = qty.subtract(computQty);
                                    saveSurplusStock = surplusStock.add(computQty);
                                }
                                else
                                {
                                    saveSurplusStock = surplusStock.add(qty);
                                    qty = BigDecimal.ZERO;
                                }
                            }
                            obj.setSurplusStock(saveSurplusStock);
                            updateList.add(obj);
                            if(qty.compareTo(BigDecimal.ZERO) <= 0)
                            {
                                break;
                            }
                        }
                    }*/
                    if(Collections3.isNotEmpty(updateList))
                    {
                        rawmaterialStockDao.batchUpdateSurplusStock(updateList);
                    }
                }
                break;
            // 出库
            case 2:
                // 投料出库、领料出库、补料出库、原料送货出库
                if(!(TypeKey.ST_MATERIAL_INOUTTYPE_FEEDING_OUT.compareTo(raw.getInoutType()) == 0 || TypeKey.ST_MATERIAL_INOUTTYPE_OUT.compareTo(raw.getInoutType()) == 0
                 || TypeKey.ST_MATERIAL_INOUTTYPE_FEDING_OUT.compareTo(raw.getInoutType()) == 0 || TypeKey.ST_MATERIAL_INOUTTYPE_SENTOUT.compareTo(raw.getInoutType()) == 0))
                {
                    return;
                }
                if (null != raw.getMaterial() && StringUtils.isNotBlank(raw.getMaterial().getRecordId()) && StringUtils.isBlank(raw.getMaterialId()))
                {
                    raw.setMaterialId(raw.getMaterial().getRecordId());
                }
                if(StringUtils.isBlank(raw.getMaterialId()))
                {
                    return;
                }
                List<RawmaterialStock> updateList = Lists.newArrayList();
                // 获取物料所有有剩余数量的采购入库记录
                RawmaterialStock query = new RawmaterialStock();
                query.setCompanyId(raw.getCompany().getRecordId());
                query.setMaterialId(raw.getMaterialId());
                List<RawmaterialStock> rawStockList = rawmaterialStockDao.getWarehousingListTwo(query);
                if (Collections3.isNotEmpty(rawStockList))
                {
                    BigDecimal sumQty = raw.getQuantity(); //出库存
                    for (RawmaterialStock rawStock : rawStockList)
                    {
                        BigDecimal computQty = BigDecimal.ZERO; // 出库数量
                        BigDecimal remainCount = BigDecimal.ZERO; // 采购单明细剩余数量
                        if (sumQty.compareTo(rawStock.getSurplusStock()) >= 0) {
                            sumQty = sumQty.subtract(rawStock.getSurplusStock());
                            computQty = rawStock.getSurplusStock();
                        } else {
                            remainCount = rawStock.getSurplusStock().subtract(sumQty);
                            computQty = sumQty;
                            sumQty = BigDecimal.ZERO;
                        }
                        //原料出入库记录Id
                        rawStock.setRawMaterialId(raw.getRecordId());
                        rawStock.setPurchasingInId(rawStock.getRecordId());
                        rawStock.setOutboundQuantity(computQty);
                        rawStock.setSurplusStock(remainCount);
                        rawStock.setCompanyId(raw.getCompany().getRecordId());

                        //插入先进先出记录表
                        rawmaterialStockDao.insetFirstInFirstOut(rawStock);

                        //更新采购入库记录上的剩余数量
                        updateList.add(rawStock);

                        if (sumQty.compareTo(BigDecimal.ZERO) <= 0) {
                            break;
                        }
                    }
                }
                if(Collections3.isNotEmpty(updateList))
                {
                    rawmaterialStockDao.batchUpdateSurplusStock(updateList);
                }
                break;
            // 冲红
            case 3:
                // 采购入库、补货入库、退货出库
                if(TypeKey.ST_MATERIAL_INOUTTYPE_IN.compareTo(raw.getInoutType()) == 0)
                {
                    updateList = Lists.newArrayList();
                    raw.setSurplusStock(BigDecimal.ZERO);
                    updateList.add(raw);

                   /* //冲红数量
                    BigDecimal rushredQuantity = (raw.getRushredQuantity() == null || raw.getRushredQuantity().compareTo(BigDecimal.ZERO) == 0) ? raw.getQuantity() : raw.getRushredQuantity();
                    if (rushredQuantity.compareTo(BigDecimal.ZERO) < 0)
                    {
                        rushredQuantity = rushredQuantity.multiply(new BigDecimal(-1));
                    }


                    if (TypeKey.ST_MATERIAL_INOUTTYPE_IN.compareTo(raw.getInoutType()) == 0)
                    {
                        raw.setSurplusStock(BigDecimal.ZERO);
                        updateList.add(raw);
                    }
                    else
                    {
                        // 获取采购入库记录剩余数量
                        query = new RawmaterialStock();
                        query.setCompanyId(raw.getCompany().getRecordId());
                        query.setPurchasingDtlId(raw.getPurchasingDtlId());
                        rawStockList = rawmaterialStockDao.getWarehousingListTwo(query);
                        if(Collections3.isNotEmpty(rawStockList))
                        {
                            return;
                        }
                        for(RawmaterialStock obj : rawStockList)
                        {
                            BigDecimal surplusStock = null == obj.getSurplusStock() ? BigDecimal.ZERO : obj.getSurplusStock();
                            BigDecimal saveSurplusStock = BigDecimal.ZERO;
                            if(TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_REDELIVER.compareTo(raw.getInoutType()) == 0)
                            {
                                if(surplusStock.compareTo(BigDecimal.ZERO) <= 0)
                                {
                                    continue;
                                }
                                if(rushredQuantity.compareTo(surplusStock) > 0)
                                {
                                    rushredQuantity = rushredQuantity.subtract(surplusStock);
                                }
                                else
                                {
                                    saveSurplusStock = saveSurplusStock.subtract(rushredQuantity);
                                    rushredQuantity = BigDecimal.ZERO;
                                }
                            }
                            else if(TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT.compareTo(raw.getInoutType()) == 0)
                            {
                                BigDecimal quantity = null == obj.getQuantity() ? BigDecimal.ZERO : obj.getQuantity();
                                BigDecimal computQty = quantity.subtract(surplusStock);
                                if(computQty.compareTo(BigDecimal.ZERO) <= 0)
                                {
                                    continue;
                                }
                                if(rushredQuantity.compareTo(computQty) > 0)
                                {
                                    rushredQuantity = rushredQuantity.subtract(computQty);
                                    saveSurplusStock = surplusStock.add(computQty);
                                }
                                else
                                {
                                    saveSurplusStock = surplusStock.add(rushredQuantity);
                                    rushredQuantity = BigDecimal.ZERO;
                                }
                            }
                            obj.setSurplusStock(saveSurplusStock);
                            updateList.add(obj);
                            if(rushredQuantity.compareTo(BigDecimal.ZERO) <= 0)
                            {
                                break;
                            }
                        }
                    }*/
                    if(Collections3.isNotEmpty(updateList))
                    {
                        rawmaterialStockDao.batchUpdateSurplusStock(updateList);
                    }
                }
                if(TypeKey.ST_MATERIAL_INOUTTYPE_FEEDING_OUT.compareTo(raw.getInoutType()) == 0 || TypeKey.ST_MATERIAL_INOUTTYPE_OUT.compareTo(raw.getInoutType()) == 0)
                {
                    // 更新采购入库记录的剩余库存
                    rawmaterialStockDao.batchUpdateSurplusStockTwo(raw);
                    //删除先进先出记录
                    rawmaterialStockDao.updateFirstInFistOut(raw);
                }
                break;
        }
    }



    public void handOnlineInventoryData()
    {
        // 获取当前的LocalDateTime
        LocalDateTime now = LocalDateTime.now();
        // 格式化LocalDateTime为字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime = now.format(formatter);
        // 将LocalDateTime转换为Date
        Date date = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        List<String> chooseCompanyList = Lists.newArrayList();
        List<Company> erpCompanyListT = companyDao.getErpCompanyList(chooseCompanyList);
        for(Company company : erpCompanyListT)
        {
            List<DictValue> dictValueMatList = new ArrayList<>();// 物料类型
            List<DictValue> dictValueList = DictUtils.getValuesByItem(CommonEnums.DictItemEnum.MATERIAL_TYPE_STORE,company);
            if(Collections3.isEmpty(dictValueList))
            {
                continue;
            }
            for (DictValue dictValue : dictValueList) {
                if (!dictValue.getValue().equals(ConstKey.MATERIAL_REMAIN)) {
                    dictValueMatList.add(dictValue);
                }
            }
            RawmaterialStock rawmaterialStock = new RawmaterialStock();
            Date currentDate = new Date();
//            long oneDayMillis = 24 * 60 * 60 * 1000;
//            // 将当前时间往前推一天
//            Date previousDay = new Date(currentDate.getTime() - oneDayMillis);

            rawmaterialStock.setInoutTimeStartQr(currentDate);
            rawmaterialStock.setInoutTimeEndQr(currentDate);
            rawmaterialStock.setCompany(company);
            MaterialTimeUtils materialTimeUtils = new MaterialTimeUtils();
            List<RawmaterialStock> materialLlist =  materialTimeUtils.dealAllPlace(rawmaterialStock,company);

            if (Collections3.isNotEmpty(dictValueMatList))
            {
                for(DictValue valueObj : dictValueMatList)
                {
                    if (Collections3.isNotEmpty(materialLlist))
                    {
                        BigDecimal materialFirstInOutTotalTax = BigDecimal.ZERO;
                        BigDecimal weightedAmountsTax = BigDecimal.ZERO;
                        for(RawmaterialStock stock : materialLlist)
                        {
                            if (StringUtils.isNotBlank(stock.getMaterialTypeId()) && stock.getMaterialTypeId().equals(valueObj.getRecordId()))
                            {
                                //先进先出金额(含税)
                                BigDecimal firstInFirstAmountTotalTax = (stock.getFirstInFirstAmountTotalTax() == null || stock.getFirstInFirstAmountTotalTax().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO : stock.getFirstInFirstAmountTotalTax();

                                materialFirstInOutTotalTax = materialFirstInOutTotalTax.add(firstInFirstAmountTotalTax);

                                //加权金额(含税)
                                BigDecimal cost = (stock.getCost() == null || stock.getCost().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO : stock.getCost();

                                weightedAmountsTax = weightedAmountsTax.add(cost);
                            }
                        }
                        valueObj.setMaterialFirstInOutTotalTax(materialFirstInOutTotalTax);
                        valueObj.setWeightedAmountsTax(weightedAmountsTax);
                        valueObj.setCompanyId(rawmaterialStock.getCompany().getRecordId());
                        valueObj.setResetDate(date);
                    }
                }
            }
            List<DictValue> dictMaterialList = Lists.newArrayList();
            if (Collections3.isNotEmpty(dictValueMatList))
            {
                for(DictValue dictValueObj : dictValueMatList)
                {
                    if ((dictValueObj.getMaterialFirstInOutTotal() != null &&
                            dictValueObj.getMaterialFirstInOutTotal().compareTo(new BigDecimal(0)) > 0)
                            || (dictValueObj.getWeightedAmountsTax() != null &&
                            dictValueObj.getWeightedAmountsTax().compareTo(new BigDecimal(0)) > 0))
                    {
                        dictMaterialList.add(dictValueObj);
                    }
                }
            }
            if (Collections3.isNotEmpty(dictMaterialList))
            {
                materialDao.batchInsertTwo(dictMaterialList);
            }
        }
    }

    /**
     * zjn 2024-12-21 处理采购单分批交货状态，生成分批交货与采购入库记录
     * @param raw 原料出入库记录
     * @param type 类型(1:入库，2:出库，3:冲红)
     * */
    public void handlePurchasingDetailDeliverys(RawmaterialStock raw,Integer type)
    {
        if(null == raw || null == type)
        {
            return;
        }
        switch (type)
        {
            // 入库
            case 1:
                // 采购入库、补货入库
                if(TypeKey.ST_MATERIAL_INOUTTYPE_IN.compareTo(raw.getInoutType()) == 0 || TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_REDELIVER.compareTo(raw.getInoutType()) == 0)
                {
                    if(null == raw.getPurchasingDtlId())
                    {
                        return;
                    }
                    // 入库赠品数量
                    BigDecimal giveNum = (raw.getGiveNum() == null || raw.getGiveNum().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO : raw.getGiveNum();

                    // 入库数量
                    BigDecimal qty = (raw.getQuantity() == null || raw.getQuantity().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO : raw.getQuantity();

                    // 本次入库总数
                    BigDecimal sumQty = giveNum.add(qty);

                    List<PurchasingDetailDeliverys> updateList = Lists.newArrayList(); // 更新分批入库状态集合
                    List<PurchasingDetailDeliverysMiddle> insertList = Lists.newArrayList();// 添加分批交货与采购入库记录集合


                    // 获取采购单分批交货集合
                    PurchasingDetailDeliverys query = new PurchasingDetailDeliverys();
                    query.setCompany(raw.getCompany());
                    query.setPurchasingDetailId(raw.getPurchasingDtlId().toString());
                    List<PurchasingDetailDeliverys> pddList = deliverysDao.findStatusList(query);
                    if(Collections3.isEmpty(pddList))
                    {
                        return;
                    }

                    // 获取分批交货已入库集合
                    PurchasingDetailDeliverysMiddle queryTwo = new PurchasingDetailDeliverysMiddle();
                    queryTwo.setCompany(raw.getCompany());
                    queryTwo.setList(pddList);
                    List<PurchasingDetailDeliverysMiddle> deliverysMiddleList = deliverysMiddleDao.findLists(queryTwo);

                   for(PurchasingDetailDeliverys pdd : pddList)
                   {
                        BigDecimal quantity = null == pdd.getQuantity() ? BigDecimal.ZERO : pdd.getQuantity();

                        BigDecimal inQuantity = BigDecimal.ZERO;
                        if(Collections3.isNotEmpty(deliverysMiddleList))
                        {
                            for(PurchasingDetailDeliverysMiddle deliverysMiddle : deliverysMiddleList)
                            {
                                if(pdd.getRecordId().equals(deliverysMiddle.getDeliverysId()))
                                {
                                    inQuantity = inQuantity.add(null == deliverysMiddle.getQuantity() ? BigDecimal.ZERO : deliverysMiddle.getQuantity());
                                }
                            }
                        }
                        // 分批交货剩余待入库数量
                        BigDecimal remainQuantity = quantity.subtract(inQuantity);
                        if(remainQuantity.compareTo(BigDecimal.ZERO) <= 0)
                        {
                            pdd.setStatus("200204");
                            updateList.add(pdd);
                            continue;
                        }

                        BigDecimal computQty = BigDecimal.ZERO;

                        if(sumQty.compareTo(remainQuantity) >= 0)
                        {
                            computQty = remainQuantity;
                            sumQty = sumQty.subtract(computQty);
                        }
                        else
                        {
                            computQty = sumQty;
                            sumQty = BigDecimal.ZERO;
                        }
                        if(computQty.compareTo(BigDecimal.ZERO) > 0)
                        {
                            // 添加分批交货与采购入库数据
                            PurchasingDetailDeliverysMiddle data = new PurchasingDetailDeliverysMiddle();
                            data.setCompany(pdd.getCompany());
                            data.setDeliverysId(pdd.getRecordId());
                            data.setStoreId(raw.getRecordId());
                            data.setQuantity(computQty);
                            data.preInsert();
                            insertList.add(data);
                        }
                        if(remainQuantity.compareTo(computQty) <= 0)
                        {
                            pdd.setStatus("200204");
                            pdd.preUpdate();
                            updateList.add(pdd);
                        }
                        if(sumQty.compareTo(BigDecimal.ZERO) <= 0)
                        {
                            break;
                        }
                   }
                   // 批量插入分批交货与采购入库记录集合
                   if(Collections3.isNotEmpty(insertList))
                   {
                       deliverysMiddleDao.insertList(insertList);
                   }
                   // 批量更新分批入库状态为已入库
                   if(Collections3.isNotEmpty(updateList))
                   {
                       deliverysDao.updateList(updateList);
                   }
                }
                break;
            // 出库
/*            case 2:
                // 退货出库
                if(!(TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT.compareTo(raw.getInoutType()) == 0))
                {
                    return;
                }
                if(null == raw.getPurchasingDtlId())
                {
                    return;
                }
                // 本次出库总数
                BigDecimal sumQty = raw.getQuantity();



                break;*/
            // 冲红
            case 3:
                // 采购入库、补货入库
                if(TypeKey.ST_MATERIAL_INOUTTYPE_IN.compareTo(raw.getInoutType()) == 0 || TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_REDELIVER.compareTo(raw.getInoutType()) == 0)
                {
                    // 根据入库id查询分批交货中间表数据
                    PurchasingDetailDeliverysMiddle deliverysMiddle = new PurchasingDetailDeliverysMiddle();
                    deliverysMiddle.setStoreId(raw.getRecordId());
                    deliverysMiddle.setCompany(UserUtils.getUser().getCompany());
                    List<PurchasingDetailDeliverysMiddle> deliverysMiddleList = deliverysMiddleDao.findListTwo(deliverysMiddle);
                    if (Collections3.isNotEmpty(deliverysMiddleList))
                    {
                        deliverysMiddleDao.updateStatusList(deliverysMiddleList);
                        deliverysDao.updateStatusList(deliverysMiddleList);
                    }

                }
                // 退货出库
                else if(TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT.compareTo(raw.getInoutType()) == 0)
                {
                    // 根据入库id查询分批交货中间表数据
                    PurchasingDetailDeliverysMiddle deliverysMiddle = new PurchasingDetailDeliverysMiddle();
                    deliverysMiddle.setStoreId(raw.getRecordId());
                    deliverysMiddle.setCompany(UserUtils.getUser().getCompany());
                    List<PurchasingDetailDeliverysMiddle> deliverysMiddleList = deliverysMiddleDao.findListTwo(deliverysMiddle);
                    if (Collections3.isNotEmpty(deliverysMiddleList))
                    {
                        deliverysMiddleDao.updateStatusList(deliverysMiddleList);
                        deliverysDao.updateStatusList(deliverysMiddleList);
                    }
                }
                break;
        }
    }

    public String autoPurchasing(BigDecimal occupiedStock, String materialId, String saleId, String notiId, Date operateDate,
                            RawmaterialStock raw, Double remainQty)
    {
        Integer inoutType = TypeKey.ST_MATERIAL_INOUTTYPE_OUT;;
        if ((null == occupiedStock || occupiedStock.compareTo(BigDecimal.ZERO) <= 0) && (null == remainQty
                || remainQty <= 0))
        {
            return null;
        }

        BigDecimal allStocks = BigDecimal.ZERO;
        List<MaterialPlaceCom> comList = Lists.newArrayList();

        MaterialPlaceCom maComStock = null;
        List<RawmaterialStock> rawList = Lists.newArrayList();
        Company company = UserUtils.getUser().getCompany();
        MaterialPlaceCom materialPlaceCom = new MaterialPlaceCom();
        materialPlaceCom.setMaterialId(materialId);
        materialPlaceCom.setCompanyId(company.getRecordId());

        if (null != materialId && StringUtils.isNotBlank(materialId)
                && null != raw.getMaterialUse().getProduct() && StringUtils.isNotBlank(raw.getMaterialUse().getProduct())
                && raw.getMaterialUse().getProduct().equals("2"))
        {
            // 库存数量
            allStocks = materialDao.getChemistryStocks(materialId);

            if (allStocks.compareTo(occupiedStock) < 0)
            {
                return "false";
            }

            BigDecimal computStocks = BigDecimal.ZERO;
            if (allStocks.compareTo(occupiedStock) >= 0)
            {
                computStocks = occupiedStock;
                occupiedStock = BigDecimal.ZERO;
            }
            else
            {
                return "false";
            }

            if(computStocks.compareTo(BigDecimal.ZERO) > 0)
            {
                rawList.add(saveRawStocks(company,
                        materialId,
                        null,
                        notiId,
                        computStocks,
                        operateDate,
                        inoutType,
                        raw,
                        null));
            }

            if (occupiedStock.compareTo(BigDecimal.ZERO) > 0)
            {
                return "false";
            }
            if (Collections3.isNotEmpty(rawList))
            {
                // 插入出入库记录
                // 设置默认价格
                for (RawmaterialStock rr : rawList)
                {
                    // 使用最新采购价
                    rr.setPrice(materialDao.getRawmaterialPrice(raw.getMaterial().getRecordId()));
                    rr.setCost(rr.getPrice().multiply(rr.getQuantity()).setScale(4, BigDecimal.ROUND_HALF_UP));
                }
                for(RawmaterialStock stock : rawList)
                {
                    if (null != materialId && StringUtils.isNotBlank(materialId)
                        && null != raw.getMaterialUse().getProduct() && StringUtils.isNotBlank(raw.getMaterialUse().getProduct()))
                    {
                        stock.setProduct(raw.getMaterialUse().getProduct());
                    }
                    rawmaterialStockDao.bathInsertTwo(stock);
                    Integer type = 2;
                    if(inoutType.compareTo(TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT) == 0)
                    {
                        type = 1;
                    }
                    handlePurDetailFirstInFirstOut(stock,type);

                    handlePurchasingDetailDeliverys(stock, type);
                }
            }
            // 插入余料使用记录
            if (remainQty != null && remainQty > 0)
            {
                RawmaterialStock stock = saveRawStocks(company,
                        materialId,
                        maComStock,
                        notiId,
                        BigDecimal.ZERO,
                        operateDate,
                        inoutType,
                        raw,
                        remainQty);
                setDefaultRawStockPrice(stock);
                rawmaterialStockDao.insert(stock);
            }

            // 插入维护表数据
            InterProductStockRecord interProductStockRecord = materialDao.getInterProductStockRecordTwo(materialId);
            InterProductStockRecord inter = new InterProductStockRecord();
            inter.setProductId(materialId);
            inter.setInoutType("2");
            inter.setInoutPlace("领料出库");
            inter.setInitStocks(allStocks);
            inter.setChangeStocks(raw.getQuantity());
            inter.setStocks(allStocks.subtract(raw.getQuantity()));
            inter.setInitPrice(interProductStockRecord.getInitPrice());
            inter.setPrice(interProductStockRecord.getPrice());
            inter.setInitLeadTime(interProductStockRecord.getInitLeadTime());
            inter.setLeadTime(interProductStockRecord.getLeadTime());
            inter.setErpId(interProductStockRecord.getErpId());
            inter.setIcloudCompanyId(interProductStockRecord.getIcloudCompanyId());
            inter.setMaterialKind(interProductStockRecord.getMaterialKind());
            inter.preInsert();
            materialDao.addInterProductStockRecord(inter);

            // 更新产品表数量
            InterProduct interProduct = new InterProduct();
            interProduct.setRecordId(materialId);
            interProduct.setStocks(inter.getStocks());
            productDao.updateSupplierStocks(interProduct);
        }
        else
        {
            // 获取各库位的具体库存
            comList = getMaterialApplyStock(materialId, new Date(), null);
            for (int i = 0; i < comList.size(); i++)
            {
                BigDecimal stock =
                        comList.get(i).getPeriodStocks() == null ? BigDecimal.ZERO : comList.get(i).getPeriodStocks();
                allStocks = allStocks.add(stock);
            }

            if (allStocks.compareTo(occupiedStock) < 0)
            {
                return "false";
            }

            if (StringUtils.isNotBlank(saleId))
            {
                if (Collections3.isNotEmpty(comList))
                {
                    for (MaterialPlaceCom maCom : comList)
                    {
                        BigDecimal stocks = maCom.getPeriodStocks();
                        if (null == stocks || stocks.compareTo(BigDecimal.ZERO) <= 0)
                        {
                            continue;
                        }
                        BigDecimal computStocks = BigDecimal.ZERO;
                        if (stocks.compareTo(occupiedStock) >= 0)
                        {
                            computStocks = occupiedStock;
                            occupiedStock = BigDecimal.ZERO;
                        }
                        else
                        {
                            computStocks = stocks;
                            occupiedStock = occupiedStock.subtract(stocks);
                        }
                        if(computStocks.compareTo(BigDecimal.ZERO) > 0)
                        {
                            rawList.add(saveRawStocks(company,
                                    materialId,
                                    maCom,
                                    notiId,
                                    computStocks,
                                    operateDate,
                                    inoutType,
                                    raw,
                                    null));
                        }
                        if (occupiedStock.compareTo(BigDecimal.ZERO) <= 0)
                        {
                            break;
                        }
                    }
                }
            }
            // 使用工厂的库位库存
            if (occupiedStock.compareTo(BigDecimal.ZERO) > 0)
            {
                if (Collections3.isNotEmpty(comList))
                {
                    for (MaterialPlaceCom maCom : comList)
                    {
                        BigDecimal stocks = maCom.getPeriodStocks();
                        if (null == stocks || stocks.compareTo(BigDecimal.ZERO) <= 0)
                        {
                            continue;
                        }
                        BigDecimal computStocks = BigDecimal.ZERO;
                        if (stocks.compareTo(occupiedStock) >= 0)
                        {
                            computStocks = occupiedStock;
                            occupiedStock = BigDecimal.ZERO;
                        }
                        else
                        {
                            computStocks = stocks;
                            occupiedStock = occupiedStock.subtract(stocks);
                        }
                        if(computStocks.compareTo(BigDecimal.ZERO) > 0)
                        {
                            rawList.add(saveRawStocks(company,
                                    materialId,
                                    maCom,
                                    notiId,
                                    computStocks,
                                    operateDate,
                                    inoutType,
                                    raw,
                                    null));
                        }
                        if (occupiedStock.compareTo(BigDecimal.ZERO) <= 0)
                        {
                            break;
                        }

                    }
                }
            }

            if (!StringUtils.isEmpty(saleId) && occupiedStock.compareTo(BigDecimal.ZERO) > 0)
            {
                String parameterValue = materialDao.getQueryAllSaleMaterial(saleId);
                // 使用其它销售公司的库存
                if ("1".equals(parameterValue))
                {
                    if (Collections3.isNotEmpty(comList))
                    {
                        for (MaterialPlaceCom maCom : comList)
                        {
                            if (StringUtils.isNotBlank(maCom.getSaleCompanyId())
                                    && !saleId.contains(maCom.getSaleCompanyId()))
                            {
                                BigDecimal stocks = maCom.getPeriodStocks();
                                if (null == stocks || stocks.compareTo(BigDecimal.ZERO) <= 0)
                                {
                                    continue;
                                }
                                BigDecimal computStocks = BigDecimal.ZERO;
                                if (stocks.compareTo(occupiedStock) >= 0)
                                {
                                    computStocks = occupiedStock;
                                    occupiedStock = BigDecimal.ZERO;
                                }
                                else
                                {
                                    computStocks = stocks;
                                    occupiedStock = occupiedStock.subtract(stocks);
                                }
                                if(computStocks.compareTo(BigDecimal.ZERO) > 0)
                                {
                                    rawList.add(saveRawStocks(company,
                                            materialId,
                                            maCom,
                                            notiId,
                                            computStocks,
                                            operateDate,
                                            inoutType,
                                            raw,
                                            null));
                                }
                                if (occupiedStock.compareTo(BigDecimal.ZERO) <= 0)
                                {
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            if (occupiedStock.compareTo(BigDecimal.ZERO) > 0)
            {
                return "false";
            }
            if (Collections3.isNotEmpty(rawList))
            {
                // 插入出入库记录
                // 设置默认价格
                for (RawmaterialStock rr : rawList)
                {
                    setDefaultRawStockPrice(rr);
                }
                for(RawmaterialStock stock : rawList)
                {
                    rawmaterialStockDao.bathInsertTwo(stock);
                    Integer type = 2;
                    if(inoutType.compareTo(TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT) == 0)
                    {
                        type = 1;
                    }
                    handlePurDetailFirstInFirstOut(stock,type);

                    handlePurchasingDetailDeliverys(stock, type);
                }
            }
            // 插入余料使用记录
            if (remainQty != null && remainQty > 0)
            {
                RawmaterialStock stock = saveRawStocks(company,
                        materialId,
                        maComStock,
                        notiId,
                        BigDecimal.ZERO,
                        operateDate,
                        inoutType,
                        raw,
                        remainQty);
                setDefaultRawStockPrice(stock);
                rawmaterialStockDao.insert(stock);
            }
        }

        return null;
    }
}
