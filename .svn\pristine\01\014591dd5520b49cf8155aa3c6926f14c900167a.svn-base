/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.4.1 (2020-07-08)
 */
!function(i){"use strict";var n,t,e,r,o=tinymce.util.Tools.resolve("tinymce.PluginManager"),u=tinymce.util.Tools.resolve("tinymce.util.VK"),a=function(r){return function(t){return e=typeof(n=t),(null===n?"null":"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e)===r;var n,e}},l=function(n){return function(t){return typeof t===n}},c=a("string"),f=a("array"),s=function(t){return n===t},m=l("boolean"),g=l("function"),d=function(t){var n=t.getParam("link_assume_external_targets",!1);return m(n)&&n?1:!c(n)||"http"!==n&&"https"!==n?0:n},h=function(t){return t.getParam("default_link_target")},v=function(t){return t.getParam("target_list",!0)},p=function(t){return t.getParam("rel_list",[],"array")},y=function(t){return t.getParam("allow_unsafe_link_target",!1,"boolean")},k=function(){},x=function(t){return function(){return t}},b=x(!1),O=x(!(n=null)),w=function(){return C},C=(t=function(t){return t.isNone()},{fold:function(t,n){return t()},is:b,isSome:b,isNone:O,getOr:r=function(t){return t},getOrThunk:e=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:x(null),getOrUndefined:x(undefined),or:r,orThunk:e,map:w,each:k,bind:w,exists:b,forall:O,filter:w,equals:t,equals_:t,toArray:function(){return[]},toString:x("none()")}),A=function(e){var t=x(e),n=function(){return o},r=function(t){return t(e)},o={fold:function(t,n){return n(e)},is:function(t){return e===t},isSome:O,isNone:b,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:n,orThunk:n,map:function(t){return A(t(e))},each:function(t){t(e)},bind:r,exists:r,forall:r,filter:function(t){return t(e)?o:C},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(t){return t.is(e)},equals_:function(t,n){return t.fold(b,function(t){return n(e,t)})}};return o},N={some:A,none:w,from:function(t){return null===t||t===undefined?C:A(t)}},P=Array.prototype.indexOf,_=Array.prototype.push,S=function(t,n){return e=t,r=n,-1<P.call(e,r);var e,r},T=function(t){for(var n=[],e=0,r=t.length;e<r;++e){if(!f(t[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+t);_.apply(n,t[e])}return n},D=function(t,n){return T(function(t,n){for(var e=t.length,r=new Array(e),o=0;o<e;o++){var i=t[o];r[o]=n(i,o)}return r}(t,n))},M=function(t,n){for(var e=0;e<t.length;e++){var r=n(t[e],e);if(r.isSome())return r}return N.none()},L=tinymce.util.Tools.resolve("tinymce.util.Tools"),E=function(t){return c(t.value)?t.value:""},R=function(e){return void 0===e&&(e=E),function(t){return N.from(t).map(function(t){return n=t,r=e,o=[],L.each(n,function(t){var n=c(t.text)?t.text:c(t.title)?t.title:"";if(t.menu===undefined){var e=r(t);o.push({text:n,value:e})}}),o;var n,r,o})}},U={sanitize:function(t){return R(E)(t)},sanitizeWith:R,createUi:function(n,e){return function(t){return{name:n,type:"selectbox",label:e,items:t}}},getValue:E},q=function(){return(q=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var o in n=arguments[e])Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o]);return t}).apply(this,arguments)},K=Object.keys,z=function(t,e,r,o){return function(t,n){for(var e=K(t),r=0,o=e.length;r<o;r++){var i=e[r];n(t[i],i)}}(t,function(t,n){(e(t,n)?r:o)(t,n)}),{}},I=function(t,n){var e,r={};return z(t,n,(e=r,function(t,n){e[n]=t}),k),r},j=function(t){return!(!/(^|[ ,])rtc([, ]|$)/.test(t.getParam("plugins","","string"))||!o.get("rtc"))},B=function(t){return/^\w+:/i.test(t)},V=function(t){var n=t.getAttribute("data-mce-href");return n||t.getAttribute("href")},F=function(t,n){var e,r,o=["noopener"],i=t?t.split(/\s+/):[],u=function(t){return t.filter(function(t){return-1===L.inArray(o,t)})},a=n?0<(e=u(e=i)).length?e.concat(o):o:u(i);return 0<a.length?(r=a,L.trim(r.sort().join(" "))):""},W=function(t,n){return n=n||t.selection.getNode(),X(n)?t.dom.select("a[href]",n)[0]:t.dom.getParent(n,"a[href]")},$=function(t,n){var e=n?n.innerText||n.textContent:t.getContent({format:"text"});return e.replace(/\uFEFF/g,"")},H=function(t){return t&&"A"===t.nodeName&&!!V(t)},G=function(t){return 0<L.grep(t,H).length},J=function(t){return!(/</.test(t)&&(!/^<a [^>]+>[^<]+<\/a>$/.test(t)||-1===t.indexOf("href=")))},X=function(t){return t&&"FIGURE"===t.nodeName&&/\bimage\b/i.test(t.className)},Q=function(t){return n=["title","rel","class","target"],e=function(n,e){return t[e].each(function(t){n[e]=0<t.length?t:null}),n},r={href:t.href},function(t,n){for(var e=0,r=t.length;e<r;e++)n(t[e],e)}(n,function(t){r=e(r,t)}),r;var n,e,r},Y=function(t,n){var e,r,o=q({},n);if(!(0<p(t).length)&&!1===y(t)){var i=F(o.rel,"_blank"===o.target);o.rel=i||null}return N.from(o.target).isNone()&&!1===v(t)&&(o.target=h(t)),o.href=(e=o.href,"http"!==(r=d(t))&&"https"!==r||B(e)?e:r+"://"+e),o},Z=function(l,c,f){var s=l.selection.getNode(),m=W(l,s),g=Y(l,Q(f));l.undoManager.transact(function(){var n,t,e,r,o,i,u,a;f.href===c.href&&c.attach(),m?(l.focus(),o=l,i=m,u=f.text,a=g,u.each(function(t){i.hasOwnProperty("innerText")?i.innerText=t:i.textContent=t}),o.dom.setAttribs(i,a),o.selection.select(i)):(n=l,t=s,e=f.text,r=g,X(t)?rt(n,t,r):e.fold(function(){n.execCommand("mceInsertLink",!1,r)},function(t){n.insertContent(n.dom.createHTML("a",r,n.dom.encode(t)))}))})},tt=function(t,n,e){var r,o,i,u,a,l,c;j(t)?t.execCommand("createlink",!1,(o=(r=e)["class"],i=r.href,u=r.rel,a=r.target,l=r.text,c=r.title,I({"class":o.getOrNull(),href:i,rel:u.getOrNull(),target:a.getOrNull(),text:l.getOrNull(),title:c.getOrNull()},function(t,n){return!1===s(t)}))):Z(t,n,e)},nt=function(t){var e;j(t)?t.execCommand("unlink"):(e=t).undoManager.transact(function(){var t=e.selection.getNode();if(X(t))et(e,t);else{var n=e.dom.getParent(t,"a[href]",e.getBody());n&&e.dom.remove(n,!0)}e.focus()})},et=function(t,n){var e=t.dom.select("img",n)[0];if(e){var r=t.dom.getParents(e,"a[href]",n)[0];r&&(r.parentNode.insertBefore(e,r),t.dom.remove(r))}},rt=function(t,n,e){var r=t.dom.select("img",n)[0];if(r){var o=t.dom.create("a",e);r.parentNode.insertBefore(o,r),o.appendChild(r)}},ot=function(n,t,e,r){var o,i=r[t],u=0<n.length;return i!==undefined?(o=i,M(e,function(t){return N.some(t).filter(function(t){return t.value===o})}).map(function(t){return{url:{value:t.value,meta:{text:u?n:t.text,attach:k}},text:u?n:t.text}})):N.none()},it=function(t,i){var n,e,u=(n=t.text,e=n,{get:function(){return e},set:function(t){e=t}}),r=function(t,n){var e,r,o=(e=i,("link"===(r=n.name)?e.catalogs.link:"anchor"===r?e.catalogs.anchor:N.none()).getOr([]));return ot(u.get(),n.name,o,t)};return{onChange:function(t,n){return"url"===n.name?function(t){if(u.get().length<=0){var n=t.url.meta.text!==undefined?t.url.meta.text:t.url.value,e=t.url.meta.title!==undefined?t.url.meta.title:"";return N.some({text:n,title:e})}return N.none()}(t()):S(["anchor","link"],n.name)?r(t(),n):("text"===n.name&&u.set(t().text),N.none())}}},ut=tinymce.util.Tools.resolve("tinymce.util.Delay"),at=tinymce.util.Tools.resolve("tinymce.util.Promise"),lt=function(t){var n=t.href;return 0<n.indexOf("@")&&-1===n.indexOf("/")&&-1===n.indexOf("mailto:")?N.some({message:"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",preprocess:function(t){return q(q({},t),{href:"mailto:"+n})}}):N.none()},ct=function(u,a){return M([lt,(e=d(u),r=u.getParam("link_default_protocol","http","string"),function(t){var n=t.href;return 1===e&&!B(n)||0===e&&/^\s*www[\.|\d\.]/i.test(n)?N.some({message:"The URL you entered seems to be an external link. Do you want to add the required "+r+":// prefix?",preprocess:function(t){return q(q({},t),{href:r+"://"+n})}}):N.none()})],function(t){return t(a)}).fold(function(){return at.resolve(a)},function(i){return new at(function(n){var e,t,r,o;e=u,t=i.message,r=function(t){n(t?i.preprocess(a):a)},o=e.selection.getRng(),ut.setEditorTimeout(e,function(){e.windowManager.confirm(t,function(t){e.selection.setRng(o),r(t)})})})});var e,r},ft=function(t){var n=t.dom.select("a:not([href])"),e=D(n,function(t){var n=t.name||t.id;return n?[{text:n,value:"#"+n}]:[]});return 0<e.length?N.some([{text:"None",value:""}].concat(e)):N.none()},st=function(t){var n=t.getParam("link_class_list",[],"array");return 0<n.length?U.sanitize(n):N.none()},mt=tinymce.util.Tools.resolve("tinymce.util.XHR"),gt=function(n){var e=function(t){return n.convertURL(t.value||t.url,"href")},t=n.getParam("link_list");return new at(function(n){c(t)?mt.send({url:t,success:function(t){return n(function(t){try{return N.some(JSON.parse(t))}catch(n){return N.none()}}(t))},error:function(t){return n(N.none())}}):g(t)?t(function(t){return n(N.some(t))}):n(N.from(t))}).then(function(t){return t.bind(U.sanitizeWith(e)).map(function(t){return 0<t.length?[{text:"None",value:""}].concat(t):t})})},dt=function(t,n){var e=p(t);if(0<e.length){var r=n.is("_blank");return(!1===y(t)?U.sanitizeWith(function(t){return F(U.getValue(t),r)}):U.sanitize)(e)}return N.none()},ht=[{text:"Current window",value:""},{text:"New window",value:"_blank"}],vt=function(t){var n=v(t);return f(n)?U.sanitize(n).orThunk(function(){return N.some(ht)}):!1===n?N.none():N.some(ht)},pt=function(t,n,e){var r=t.getAttrib(n,e);return null!==r&&0<r.length?N.some(r):N.none()},yt=function(f,s){return gt(f).then(function(t){var n,e,r,o,i,u,a,l,c=(e=s,r=(n=f).dom,o=J(n.selection.getContent())?N.some($(n.selection,e)):N.none(),i=e?N.some(r.getAttrib(e,"href")):N.none(),u=e?N.from(r.getAttrib(e,"target")):N.none(),a=pt(r,e,"rel"),l=pt(r,e,"class"),{url:i,text:o,title:pt(r,e,"title"),target:u,rel:a,linkClass:l});return{anchor:c,catalogs:{targets:vt(f),rels:dt(f,c.target),classes:st(f),anchor:ft(f),link:t},optNode:N.from(s),flags:{titleEnabled:f.getParam("link_title",!0,"boolean")}}})},kt=function(d){var t,n;(n=W(t=d),yt(t,n)).then(function(t){var i,u,n,e,r,o,a,l,c,f,s,m,g;return e=function(t){var e=t.getData();if(!e.url.value)return nt(i),void t.close();var n=function(n){return N.from(e[n]).filter(function(t){return!u.anchor[n].is(t)})},r={href:e.url.value,text:n("text"),target:n("target"),rel:n("rel"),"class":n("linkClass"),title:n("title")},o={href:e.url.value,attach:e.url.meta!==undefined&&e.url.meta.attach?e.url.meta.attach:function(){}};ct(i,r).then(function(t){tt(i,o,t)}),t.close()},r=i=d,l=(n=u=t).anchor.text.map(function(){return{name:"text",type:"input",label:"Text to display"}}).toArray(),c=n.flags.titleEnabled?[{name:"title",type:"input",label:"Title"}]:[],f=N.from(h(r)),a=f,s={url:{value:(o=n).anchor.url.getOr(""),meta:{attach:function(){},text:o.anchor.url.fold(function(){return""},function(){return o.anchor.text.getOr("")}),original:{value:o.anchor.url.getOr("")}}},text:o.anchor.text.getOr(""),title:o.anchor.title.getOr(""),anchor:o.anchor.url.getOr(""),link:o.anchor.url.getOr(""),rel:o.anchor.rel.getOr(""),target:o.anchor.target.or(a).getOr(""),linkClass:o.anchor.linkClass.getOr("")},m=it(s,n),g=n.catalogs,{title:"Insert/Edit Link",size:"normal",body:{type:"panel",items:T([[{name:"url",type:"urlinput",filetype:"file",label:"URL"}],l,c,function(t){for(var n=[],e=function(t){n.push(t)},r=0;r<t.length;r++)t[r].each(e);return n}([g.anchor.map(U.createUi("anchor","Anchors")),g.rels.map(U.createUi("rel","Rel")),g.targets.map(U.createUi("target","Open link in...")),g.link.map(U.createUi("link","Link list")),g.classes.map(U.createUi("linkClass","Class"))])])},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onChange:function(n,t){var e=t.name;m.onChange(n.getData,{name:e}).each(function(t){n.setData(t)})},onSubmit:e}}).then(function(t){d.windowManager.open(t)})},xt=function(t){var n=i.document.createElement("a");n.target="_blank",n.href=t,n.rel="noreferrer noopener";var e,r,o=i.document.createEvent("MouseEvents");o.initMouseEvent("click",!0,!0,i.window,0,0,0,0,0,!1,!1,!1,!1,0,null),e=n,r=o,i.document.body.appendChild(e),e.dispatchEvent(r),i.document.body.removeChild(e)},bt=function(t,n){return t.dom.getParent(n,"a[href]")},Ot=function(t){return bt(t,t.selection.getStart())},wt=function(t,n){if(n){var e=V(n);if(/^#/.test(e)){var r=t.$(e);r.length&&t.selection.scrollIntoView(r[0],!0)}else xt(n.href)}},Ct=function(t){return function(){kt(t)}},At=function(t){return function(){wt(t,Ot(t))}},Nt=function(r){r.on("click",function(t){var n=bt(r,t.target);n&&u.metaKeyPressed(t)&&(t.preventDefault(),wt(r,n))}),r.on("keydown",function(t){var n,e=Ot(r);e&&13===t.keyCode&&(!0===(n=t).altKey&&!1===n.shiftKey&&!1===n.ctrlKey&&!1===n.metaKey)&&(t.preventDefault(),wt(r,e))})},Pt=function(e){return function(n){var t=function(t){return n.setActive(!e.mode.isReadOnly()&&!!W(e,t.element))};return e.on("NodeChange",t),function(){return e.off("NodeChange",t)}}},_t=function(r){return function(n){var t=r.dom.getParents(r.selection.getStart());n.setDisabled(!G(t));var e=function(t){return n.setDisabled(!G(t.parents))};return r.on("NodeChange",e),function(){return r.off("NodeChange",e)}}};!function St(){o.add("link",function(t){var n,e,r,i,o,u,a;(n=t).ui.registry.addToggleButton("link",{icon:"link",tooltip:"Insert/edit link",onAction:Ct(n),onSetup:Pt(n)}),n.ui.registry.addButton("openlink",{icon:"new-tab",tooltip:"Open link",onAction:At(n),onSetup:_t(n)}),n.ui.registry.addButton("unlink",{icon:"unlink",tooltip:"Remove link",onAction:function(){return nt(n)},onSetup:_t(n)}),(e=t).ui.registry.addMenuItem("openlink",{text:"Open link",icon:"new-tab",onAction:At(e),onSetup:_t(e)}),e.ui.registry.addMenuItem("link",{icon:"link",text:"Link...",shortcut:"Meta+K",onAction:Ct(e)}),e.ui.registry.addMenuItem("unlink",{icon:"unlink",text:"Remove link",onAction:function(){return nt(e)},onSetup:_t(e)}),(r=t).ui.registry.addContextMenu("link",{update:function(t){return G(r.dom.getParents(t,"a"))?"link unlink openlink":"link"}}),o=function(t){var n=i.selection.getNode();return t.setDisabled(!W(i,n)),function(){}},(i=t).ui.registry.addContextForm("quicklink",{launch:{type:"contextformtogglebutton",icon:"link",tooltip:"Link",onSetup:Pt(i)},label:"Link",predicate:function(t){return!!W(i,t)&&i.getParam("link_context_toolbar",!1,"boolean")},initValue:function(){var t=W(i);return t?V(t):""},commands:[{type:"contextformtogglebutton",icon:"link",tooltip:"Link",primary:!0,onSetup:function(t){var n=i.selection.getNode();return t.setActive(!!W(i,n)),Pt(i)(t)},onAction:function(t){var n=W(i),e=t.getValue();if(n)i.undoManager.transact(function(){i.dom.setAttrib(n,"href",e),i.selection.collapse(!1),t.hide()});else{var r={href:e,attach:function(){}},o=J(i.selection.getContent())?N.some($(i.selection,n)).filter(function(t){return 0<t.length}).or(N.from(e)):N.none();tt(i,r,{href:e,text:o,title:N.none(),rel:N.none(),target:N.none(),"class":N.none()}),t.hide()}}},{type:"contextformbutton",icon:"unlink",tooltip:"Remove link",onSetup:o,onAction:function(t){nt(i),t.hide()}},{type:"contextformbutton",icon:"new-tab",tooltip:"Open link",onSetup:o,onAction:function(t){At(i)(),t.hide()}}]}),Nt(t),(u=t).addCommand("mceLink",function(){u.getParam("link_quicklink",!1,"boolean")?u.fire("contexttoolbar-show",{toolbarKey:"quicklink"}):Ct(u)()}),(a=t).addShortcut("Meta+K","",function(){a.execCommand("mceLink")})})}()}(window);