package com.kyb.pcberp.modules.contract.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.production.entity.CapacityChange;
import com.kyb.pcberp.modules.report.entity.CapacityDeail;

import java.math.BigDecimal;
import java.util.List;

public class CapacityUse  extends DataEntity<CapacityUse> {
    private static final long serialVersionUID = 1L;
    // 款数
    private Integer amount;

    // 产能面积
    private BigDecimal capacity;

    // 样品日总款数
    private Integer sampleAmount;

    // 新单日总款数
    private Integer orderAmount;

    // 使用款数
    private Integer useAmount;

    // 使用产能面积
    private BigDecimal useCapacity;

    // 使用样品日总款数
    private Integer useSampleAmount;

    // 使用新单日总款数
    private Integer useOrderAmount;

    private Integer flag;

    private List<CapacityDeail> deailList;

    private List<CapacityChange> chaneList;

    private List<MaxBatchAreaConfirm> confirmList;

    private BigDecimal comfirmCapacity; // 借用产能

    private Integer comfirmAmount; // 借用量产款数

    private Integer comfirmOrderAmount; // 借用新单款数

    private Integer comfirmSampleAmount; // 借用样品款数

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public BigDecimal getCapacity() {
        return capacity;
    }

    public void setCapacity(BigDecimal capacity) {
        this.capacity = capacity;
    }

    public Integer getSampleAmount() {
        return sampleAmount;
    }

    public void setSampleAmount(Integer sampleAmount) {
        this.sampleAmount = sampleAmount;
    }

    public Integer getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(Integer orderAmount) {
        this.orderAmount = orderAmount;
    }

    public Integer getUseAmount() {
        return useAmount;
    }

    public void setUseAmount(Integer useAmount) {
        this.useAmount = useAmount;
    }

    public BigDecimal getUseCapacity() {
        return useCapacity;
    }

    public void setUseCapacity(BigDecimal useCapacity) {
        this.useCapacity = useCapacity;
    }

    public Integer getUseSampleAmount() {
        return useSampleAmount;
    }

    public void setUseSampleAmount(Integer useSampleAmount) {
        this.useSampleAmount = useSampleAmount;
    }

    public Integer getUseOrderAmount() {
        return useOrderAmount;
    }

    public void setUseOrderAmount(Integer useOrderAmount) {
        this.useOrderAmount = useOrderAmount;
    }

    public List<CapacityDeail> getDeailList() {
        return deailList;
    }

    public void setDeailList(List<CapacityDeail> deailList) {
        this.deailList = deailList;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public CapacityUse clone()
    {
        CapacityUse o = null;
        try
        {
            o = (CapacityUse)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } // Object 中的clone()识别出你要复制的是哪一个对象。
        return o;
    }

    public List<CapacityChange> getChaneList() {
        return chaneList;
    }

    public void setChaneList(List<CapacityChange> chaneList) {
        this.chaneList = chaneList;
    }

    public List<MaxBatchAreaConfirm> getConfirmList() {
        return confirmList;
    }

    public void setConfirmList(List<MaxBatchAreaConfirm> confirmList) {
        this.confirmList = confirmList;
    }

    public BigDecimal getComfirmCapacity() {
        return comfirmCapacity;
    }

    public void setComfirmCapacity(BigDecimal comfirmCapacity) {
        this.comfirmCapacity = comfirmCapacity;
    }

    public Integer getComfirmAmount() {
        return comfirmAmount;
    }

    public void setComfirmAmount(Integer comfirmAmount) {
        this.comfirmAmount = comfirmAmount;
    }

    public Integer getComfirmOrderAmount() {
        return comfirmOrderAmount;
    }

    public void setComfirmOrderAmount(Integer comfirmOrderAmount) {
        this.comfirmOrderAmount = comfirmOrderAmount;
    }

    public Integer getComfirmSampleAmount() {
        return comfirmSampleAmount;
    }

    public void setComfirmSampleAmount(Integer comfirmSampleAmount) {
        this.comfirmSampleAmount = comfirmSampleAmount;
    }
}
