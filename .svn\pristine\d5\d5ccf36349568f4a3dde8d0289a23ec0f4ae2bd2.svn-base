<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.crm.dao.QuoteSetupDao">
	<sql id="configIdSql">
		<if test="configId != null and configId != ''">
			AND configId = #{configId}
		</if>
		<if test="configId == null || configId == ''">
			AND configId IS NULL
		</if>
	</sql>

	<select id="checkNameNum" resultType="Integer">
		SELECT
			COUNT(recordId)
		FROM
			sl_quote_setup
		WHERE
			`name` = #{name}
		AND companyId = #{companyId}
		AND activeFlag = 1
		<include refid="configIdSql"/>
		<if test="recordId != null and recordId != ''">
			AND recordId <![CDATA[<>]]> #{recordId}
		</if>
	</select>

	<select id="maxSortNum" resultType="Integer">
		SELECT IFNULL(MAX(sortNum),0) FROM sl_quote_setup
		WHERE companyId = #{recordId}
		<include refid="configIdSql"/>
	</select>

	<select id="findList" resultType="QuoteSetup">
		SELECT
		a.*,
		b.userName AS "createdByName",
		IFNULL(c.value,a.orderType) AS "orderTypeVal",
		d.name AS "configName"
		FROM
		sl_quote_setup a
		LEFT JOIN sm_user b ON b.recordId = a.createdBy
		LEFT JOIN md_dict_value c ON c.recordId = a.orderType AND a.conditionFlag IN (1,2)
		LEFT JOIN inter_order_config_main d ON d.recordId = a.configId
		WHERE
		a.companyId = #{companyId} AND a.activeFlag = 1
		<include refid="configIdSql"/>
		<if test="status != null and status != ''">
			AND a.status = #{status}
		</if>
		ORDER BY
		a.sortNum,a.createdDate DESC
	</select>

	<select id="getExportList" resultType="QuoteSetup">
		SELECT
			a.*,
			d.name AS "configName"
		FROM
			sl_quote_setup a
		LEFT JOIN inter_order_config_main d ON d.recordId = a.configId
		WHERE
			a.companyId = #{companyId}
		AND a.activeFlag = 1
		AND a.`status` = 2
		<if test="configId != null and configId != ''">
			AND (configId IS NULL OR configId <![CDATA[<>]]> #{configId})
		</if>
		<if test="configId == null || configId == ''">
			AND configId IS NOT NULL
		</if>
		AND a.`name` NOT IN (
			SELECT
				`name`
			FROM
				sl_quote_setup
			WHERE
				companyId = #{companyId}
			<include refid="configIdSql"/>
		)
		ORDER BY
		a.sortNum,a.createdDate DESC
	</select>

	<select id="getPrevious" resultType="QuoteSetup">
		SELECT * FROM sl_quote_setup WHERE companyId = #{companyId} <include refid="configIdSql"/> <include refid="previousSql"/>
	</select>

	<select id="getLast" resultType="QuoteSetup">
		SELECT * FROM sl_quote_setup WHERE companyId = #{companyId} <include refid="configIdSql"/> <include refid="lastSql"/>
	</select>

	<update id="batchUpdate">
		<foreach collection="list" item="item" separator=";">
			UPDATE sl_quote_setup SET
			lastUpdBy = #{item.lastUpdBy.recordId},
			lastUpdDate = NOW(),
			name = #{item.name},
			countMethod = #{item.countMethod},
			productType = #{item.productType},
			conditionFlag = #{item.conditionFlag},
			orderType = #{item.orderType},
			remark = #{item.remark},
			configId = #{item.configId}
			WHERE recordId = #{item.recordId}
		</foreach>
	</update>

	<insert id="batchInsert">
		INSERT INTO sl_quote_setup
		(
		companyId,
		status,
		activeFlag,
		createdBy,
		createdDate,
		name,
		countMethod,
		sortNum,
		productType,
		conditionFlag,
		orderType,
		remark,
		configId
		)VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.company.recordId},
			#{item.status},
			1,
			#{item.createdBy.recordId},
			NOW(),
			#{item.name},
			#{item.countMethod},
			#{item.sortNum},
			#{item.productType},
			#{item.conditionFlag},
			#{item.orderType},
			#{item.remark},
			#{item.configId}
			)
		</foreach>
	</insert>

	<insert id="insetSetup"  useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO sl_quote_setup(
			companyId,
			status,
			activeFlag,
			createdDate,
			name,
			countMethod,
			sortNum,
			productType,
			conditionFlag,
			orderType,
			remark,
			configId,
			exportId
		) VALUES (
			 #{companyId},
			 #{status},
			 1,
			 NOW(),
			 #{name},
			 #{countMethod},
			 #{sortNum},
			 #{productType},
			 #{conditionFlag},
			 #{orderType},
			 #{remark},
			 #{configId},
		     #{recordId}
		 )
	</insert>

	<select id="maxColumSortNum" resultType="Integer">
		SELECT IFNULL(MAX(sortNum),0) FROM sl_quote_setup_column
		WHERE setupId = #{setupId}
	</select>

	<select id="maxDeailSortNum" resultType="Integer">
		SELECT IFNULL(MAX(sortNum),0) FROM sl_quote_setup_detail
		WHERE setupId = #{setupId}
	</select>

	<update id="updateColumn">
		UPDATE
		    sl_quote_setup_column
		SET
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = NOW(),
			remark = #{remark},
			craftId = #{craftId},
			type = #{type}
		WHERE recordId = #{recordId}
	</update>

	<update id="batchUpdateDeail">
		<foreach collection="list" item="item" separator=";">
			UPDATE sl_quote_setup_detail SET
				lastUpdBy = #{userId},
				lastUpdDate = NOW(),
				price = #{item.price},
				remark = #{item.remark}
			WHERE recordId = #{item.recordId}
		</foreach>
	</update>

	<insert id="insetColumn"  useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO sl_quote_setup_column(
			companyId,
			craftId,
			type,
			setupId,
			sortNum,
			activeFlag,
			remark,
			createdBy,
			createdDate
		) VALUES (
			 #{companyId},
			 #{craftId},
			 #{type},
			 #{setupId},
			 #{sortNum},
			 1,
			 #{remark},
			 #{createdBy.recordId},
			 NOW()
		 )
	</insert>

	<insert id="batchInsetColumn">
		INSERT INTO sl_quote_setup_column(
			companyId,
			craftId,
			type,
			setupId,
			sortNum,
			activeFlag,
			remark,
			createdDate,
			exportId
		) VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.companyId},
			#{item.craftId},
			#{item.type},
			#{item.setupId},
			#{item.sortNum},
			1,
			#{item.remark},
			NOW(),
			#{item.recordId}
			)
		</foreach>
	</insert>

	<insert id="batchInsertDeail">
		INSERT INTO sl_quote_setup_detail
		(
			companyId,
			status,
			activeFlag,
			createdBy,
			createdDate,
			setupId,
			sortNum,
			price,
			remark,
			exportId
		)VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.companyId},
			#{item.status},
			1,
			#{userId},
			NOW(),
			#{item.setupId},
			#{item.sortNum},
			#{item.price},
			#{item.remark},
			#{item.recordId}
			)
		</foreach>
	</insert>

	<insert id="batchInsertDeailColumn">
		INSERT INTO sl_quote_setup_detail_column
		(
			companyId,
			deailId,
			columnId,
			columnCondition,
			columnConditionTwo,
			columnVal,
			columnValTwo,
			activeFlag,
			createdBy,
			createdDate,
			remark,
			exportId
		)VALUES
		<foreach collection="list" item="item" separator=",">
		(
			#{item.companyId},
			#{item.deailId},
			#{item.columnId},
			#{item.columnCondition},
			#{item.columnConditionTwo},
			#{item.columnVal},
			#{item.columnValTwo},
			1,
			#{userId},
			NOW(),
			#{item.remark},
		 	#{item.recordId}
		)
		</foreach>
	</insert>

	<select id="getColumList" resultType="QuoteSetupColumn">
		SELECT
			a.*,
			b.itemName AS "itemName"
		FROM sl_quote_setup_column a
		LEFT JOIN md_dict_item b ON b.recordId = a.craftId
		WHERE a.setupId = #{recordId}
		<if test="name != null and name != ''">
			AND a.craftId = #{name}
		</if>
		AND a.activeFlag = 1
		ORDER BY a.sortNum ASC
	</select>

	<select id="getDeailList" resultType="QuoteSetupDetail">
        SELECT
            a.*
        FROM sl_quote_setup_detail a
        WHERE
		a.setupId = #{recordId}
        AND a.activeFlag = 1
        ORDER BY
            a.sortNum ASC
	</select>

	<select id="getDeailListTwo" resultType="QuoteSetupDetail">
        SELECT
            a.*
        FROM sl_quote_setup_detail a
        WHERE
            a.companyId = #{companyId}
        AND a.activeFlag = 1
        ORDER BY
            a.sortNum ASC
	</select>


	<select id="getDeailColumnListBySetId" resultType="QuoteSetupDetailColumn">
		SELECT
			*
		FROM
			sl_quote_setup_detail_column
		WHERE
			activeFlag = 1
		  AND deailId IN (
			SELECT
				recordId
			FROM
				sl_quote_setup_detail
			WHERE
				setupId = #{recordId}
			  AND activeFlag = 1
		)
	</select>

	<select id="getDeailColumnList" resultType="QuoteSetupDetailColumn">
        SELECT
            a.*,
            IFNULL(c.value,a.columnVal) AS "columnValVal",
            b.craftId,
            b.type,
            d.columnName,
            d.tableFlag,
            e.itemName
        FROM sl_quote_setup_detail_column a
        LEFT JOIN sl_quote_setup_column b ON b.recordId = a.columnId
        LEFT JOIN md_dict_value c ON c.recordId = a.columnVal AND c.itemId = b.craftId AND a.columnCondition IN (1,2) AND b.type = 2
        LEFT JOIN sl_quote_setup_item d ON d.itemId = b.craftId AND d.companyId = b.companyId
        LEFT JOIN md_dict_item e ON e.recordId = b.craftId
        WHERE
            a.deailId = #{recordId}
        AND a.activeFlag = 1
        AND b.activeFlag = 1
	</select>

	<select id="getDeailColumnListT" resultType="QuoteSetupDetailColumn">
        SELECT
            a.*,
            IFNULL(c.value,a.columnVal) AS "columnValVal",
            b.craftId,
            b.type,
            d.columnName,
            d.tableFlag,
            e.itemName
        FROM sl_quote_setup_detail_column a
        LEFT JOIN sl_quote_setup_column b ON b.recordId = a.columnId
        LEFT JOIN md_dict_value c ON c.recordId = a.columnVal AND c.itemId = b.craftId AND a.columnCondition IN (1,2) AND b.type = 2
        LEFT JOIN sl_quote_setup_item d ON d.itemId = b.craftId AND d.companyId = b.companyId
        LEFT JOIN md_dict_item e ON e.recordId = b.craftId
        WHERE
            a.deailId = #{recordId}
        AND a.activeFlag = 1
        AND b.activeFlag = 1
        AND e.itemName = "订单面积" limit 1
	</select>

	<select id="getDeailColumnListTwo" resultType="QuoteSetupDetailColumn">
        SELECT
            a.*,
            IFNULL(c.value,a.columnVal) AS "columnValVal",
            b.craftId,
            b.type,
            d.columnName,
            d.tableFlag,
            e.itemName
        FROM sl_quote_setup_detail_column a
        LEFT JOIN sl_quote_setup_column b ON b.recordId = a.columnId
        LEFT JOIN md_dict_value c ON c.recordId = a.columnVal AND c.itemId = b.craftId AND a.columnCondition IN (1,2) AND b.type = 2
        LEFT JOIN sl_quote_setup_item d ON d.itemId = b.craftId AND d.companyId = b.companyId
        LEFT JOIN md_dict_item e ON e.recordId = b.craftId
        WHERE
            a.companyId = #{companyId}
        AND a.activeFlag = 1
        AND b.activeFlag = 1
	</select>

	<sql id="delSql">
		SET lastUpdBy = #{userId},lastUpdDate = NOW(),activeFlag = 0
	</sql>

	<update id="deleteQuoSetUp">
		<foreach collection="list" item="item" separator=";">
			UPDATE sl_quote_setup <include refid="delSql"/> WHERE recordId = #{item.recordId}
		</foreach>
	</update>

	<update id="deleteSetupColumn">
		UPDATE sl_quote_setup_column <include refid="delSql"/> WHERE recordId = #{recordId}
	</update>

	<update id="delDeail">
		<foreach collection="list" item="item" separator=";">
			UPDATE sl_quote_setup_detail <include refid="delSql"/> WHERE recordId = #{item.recordId}
		</foreach>
	</update>

	<update id="delDeailColumns">
		UPDATE sl_quote_setup_detail_column <include refid="delSql"/> WHERE FIND_IN_SET(deailId,#{ids})
	</update>

	<sql id="previousSql">
		AND activeFlag = 1 AND sortNum <![CDATA[<]]> #{sortNum} ORDER BY sortNum DESC LIMIT 1
	</sql>

	<sql id="lastSql">
		AND activeFlag = 1 AND sortNum > #{sortNum} ORDER BY sortNum ASC LIMIT 1
	</sql>

	<select id="getPreviousColumn" resultType="QuoteSetupColumn">
		SELECT * FROM sl_quote_setup_column WHERE setupId = #{setupId} <include refid="previousSql"/>
	</select>

	<select id="getLastColumn" resultType="QuoteSetupColumn">
		SELECT * FROM sl_quote_setup_column WHERE setupId = #{setupId} <include refid="lastSql"/>
	</select>

	<select id="getPreviousDeail" resultType="QuoteSetupDetail">
		SELECT * FROM sl_quote_setup_detail WHERE setupId = #{setupId} <include refid="previousSql"/>
	</select>

	<select id="getLastDeail" resultType="QuoteSetupDetail">
		SELECT * FROM sl_quote_setup_detail WHERE setupId = #{setupId} <include refid="lastSql"/>
	</select>

	<sql id="sortNumSql">
		SET lastUpdBy = #{userId},lastUpdDate = NOW(),sortNum = #{item.sortNum} WHERE recordId = #{item.recordId}
	</sql>

	<update id="updateSortNum">
		<foreach collection="list" item="item" separator=";">
			UPDATE sl_quote_setup <include refid="sortNumSql"/>
		</foreach>
	</update>

	<update id="updateSortNumColumn">
		<foreach collection="list" item="item" separator=";">
			UPDATE sl_quote_setup_column <include refid="sortNumSql"/>
		</foreach>
	</update>

	<update id="updateSortNumDeail">
		<foreach collection="list" item="item" separator=";">
			UPDATE sl_quote_setup_detail <include refid="sortNumSql"/>
		</foreach>
	</update>

	<sql id="statusSql">
		SET lastUpdBy = #{userId},lastUpdDate = NOW(),status = #{item.status} WHERE recordId = #{item.recordId}
	</sql>

	<update id="updateStatus">
		<foreach collection="list" item="item" separator=";">
			UPDATE sl_quote_setup <include refid="statusSql"/>
		</foreach>
	</update>

	<update id="updateDeailStatus">
		<foreach collection="list" item="item" separator=";">
			UPDATE sl_quote_setup_detail <include refid="statusSql"/>
		</foreach>
	</update>

	<update id="updateDeailColumn">
		UPDATE sl_quote_setup_detail_column a
		SET a.columnId = (
			 SELECT
				 recordId
			 FROM
				 sl_quote_setup_column
			 WHERE
				 exportId = a.columnId
			 AND setupId = #{recordId}
			 AND activeFlag = 1
		 ),
		 a.deailId = (
			SELECT
				recordId
			FROM
				sl_quote_setup_detail
			WHERE
				exportId = a.deailId
			AND setupId = #{recordId}
			AND activeFlag = 1
		)
		WHERE
			a.exportId IS NOT NULL
			AND a.activeFlag = 1
			AND a.deailId IN (
			SELECT
				exportId
			FROM
				sl_quote_setup_detail
			WHERE
				setupId = #{recordId}
			AND activeFlag = 1
		)
	</update>

	<select id="getQuoteSetupDetailList" resultType="QuoteSetupDetail">
		SELECT
			a.salePcsPrice,
			b.*
		FROM
			customer_quotation_result a LEFT JOIN
			sl_quote_setup_detail b ON b.recordId = a.description
		WHERE
			a.activeFlag = 1 AND a.quotationDetailId = #{quotationDetailId}
			ORDER BY
            b.sortNum ASC
	</select>

</mapper>