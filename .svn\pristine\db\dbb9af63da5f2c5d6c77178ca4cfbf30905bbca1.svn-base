<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.wechat.dao.ApplyDao">
    <select id="getUserApplyList" resultType="IcloudMoudle">
        SELECT
            a.*, b.recordId AS "concatId",
            b.phone,
            b.sortNum
        FROM
            icloud_md_moudle a
        LEFT JOIN icloud_md_moudle_user b ON b.moudleId = a.recordId
        AND b.phone = #{phone}
        LEFT JOIN icloud_sm_user c ON c.phone = b.phone
        WHERE
            b.recordId IS NOT NULL AND b.activeFlag = 1
        AND (a.status = 1 OR c.manageSatus = 1)
        ORDER BY b.sortNum
    </select>

    <select id="getApplyList" resultType="IcloudMoudle">
        SELECT
        a.*
        FROM
        icloud_md_moudle a
        WHERE 1=1
        AND (
            a.`status` = 1
            OR (
            SELECT
            manageSatus
            FROM
            icloud_sm_user
            WHERE
            recordId = #{userId}
            ) = 1
        )
        <if test="moudleId != null and moudleId != ''">
            AND a.moudleId = #{moudleId}
        </if>
    </select>

    <select id="getMoudleUserNum" resultType="Integer">
        SELECT
            COUNT(recordId)
        FROM
            icloud_md_moudle_user
        WHERE
            phone = #{phone}
        AND moudleId = #{moudleId}
        AND activeFlag = 1
    </select>

    <select id="getMaxSortNum" resultType="Integer">
        SELECT
            MAX(sortNum)
        FROM
            icloud_md_moudle_user
        WHERE
            phone = #{phone}
            AND activeFlag = 1
    </select>

    <select id="getLastMoudleUser" resultType="IcloudMoudleUser">
        SELECT
            *
        FROM
            icloud_md_moudle_user
        WHERE
            phone = #{phone}
        AND sortNum <![CDATA[<]]> #{sortNum}
        AND activeFlag = 1
        ORDER BY sortNum DESC
        LIMIT 1
    </select>

    <select id="getNextMoudleUser" resultType="IcloudMoudleUser">
        SELECT
            *
        FROM
            icloud_md_moudle_user
        WHERE
            phone = #{phone}
        AND sortNum > #{sortNum}
        AND activeFlag = 1
        ORDER BY sortNum ASC
        LIMIT 1
    </select>

    <insert id="addApply" useGeneratedKeys="true" keyProperty="recordId">
        INSERT INTO icloud_md_moudle_user(
            moudleId,
            phone,
            sortNum,
            activeFlag
        ) VALUES
        (
            #{moudleId},
            #{phone},
            #{sortNum},
            1
        )
    </insert>

    <update id="sortApply">
        <foreach collection="list" item="item" separator=";">
            UPDATE icloud_md_moudle_user SET sortNum = #{item.sortNum} WHERE recordId = #{item.recordId}
        </foreach>
    </update>

    <delete id="deleteApply">
        DELETE FROM icloud_md_moudle_user WHERE recordId = #{recordId}
    </delete>
</mapper>