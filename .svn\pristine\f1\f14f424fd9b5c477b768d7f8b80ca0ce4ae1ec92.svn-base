<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.eg.dao.CardAProcessCraftValueDao">
    
	<sql id="cardAProcessCraftValueColumns">
		a.recordId,
		a.companyId AS "company.recordId",
		a.processValueId AS "processValue.recordId",
		a.processCraftId AS "processCraft.recordId",
		a.processCraftValue
	</sql>
	
	<sql id="cardAProcessCraftValueJoins">
	</sql>
    
	<select id="get" resultType="CardAProcessCraftValue">
		SELECT 
			<include refid="cardAProcessCraftValueColumns"/>
		FROM eg_carda_process_craft_value a
		<include refid="cardAProcessCraftValueJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="findList" resultType="CardAProcessCraftValue">
		SELECT 
			<include refid="cardAProcessCraftValueColumns"/>
		FROM eg_carda_process_craft_value a
		<include refid="cardAProcessCraftValueJoins"/>
		<where>
			
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="CardAProcessCraftValue">
		SELECT 
			<include refid="cardAProcessCraftValueColumns"/>
		FROM eg_carda_process_craft_value a
		<include refid="cardAProcessCraftValueJoins"/>
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findListByValueList" resultType="CardAProcessCraftValue">
		SELECT 
			a.recordId as "recordId"
		FROM eg_carda_process_craft_value a
		<where>
			a.companyId = #{company.recordId}
			<if test="valueList != null and valueList.size > 0">
				AND a.processValueId in
			    <foreach collection="valueList" item ="item" open="(" separator="," close=")">
			    	#{item.recordId}  
			    </foreach>  
		   	</if>
		</where>		
	</select>
	
	<select id="findListByProcessValue" resultType="CardAProcessCraftValue">
		SELECT 
			<include refid="cardAProcessCraftValueColumns"/>,
			epc.name as "processCraft.name",
			epc.postFix as "processCraft.postFix",
			epc.defaultValue as "processCraft.defaultValue",
			epci.showWay AS "processCraft.showWay"
		FROM eg_carda_process_craft_value a
		<include refid="cardAProcessCraftValueJoins"/>
		LEFT JOIN eg_process_craft epc on epc.recordId = a.processCraftId
		LEFT JOIN eg_carda_process_value cpv on cpv.recordId = a.processValueId
		LEFT JOIN eg_process_craft_relation pcr on pcr.processId = cpv.processId and pcr.craftId = a.processCraftId 
		LEFT JOIN eg_process_craft_install epci ON epci.processId = cpv.processId AND epci.craftId = a.processCraftId AND epci.companyId = a.companyId AND epci.activeFlag = 1
		WHERE a.processValueId = #{processValue.recordId} 
			AND a.companyId=#{company.recordId} and a.activeFlag = 1 and pcr.activeFlag = 1
		order by pcr.seqNum
	</select>
	
	<insert id="insert">
		INSERT INTO eg_carda_process_craft_value(
			companyId,
			processValueId,
			processCraftId,
			processCraftValue,
			activeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			remark
		) VALUES (
			#{company.recordId},
			#{processValue.recordId},
			#{processCraft.recordId},
			#{processCraftValue},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark}
		)
	</insert>
	
	<update id="update">
		UPDATE eg_carda_process_craft_value SET 	
			processValueId = #{processValue.recordId},
			processCraftId = #{processCraft.recordId},
			processCraftValue = #{processCraftValue},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			remark = #{remark}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE eg_carda_process_craft_value SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>
	
	<delete id="delBatch">
		DELETE FROM eg_carda_process_craft_value 
		<where>
			companyId = #{company.recordId}
			<if test="craftValues != null and craftValues.size > 0">
				AND recordId in
			    <foreach collection="craftValues" item ="item" open="(" separator="," close=")">
			    	#{item.recordId}  
			    </foreach>  
		   	</if>
		</where>		
	</delete>
	
	<select id="getShowValueData" resultType="CardAProcessCraftValue">
		SELECT
			b.processCardAId AS "cardId",
			a.processCraftValue AS "processCraftValue",
			c.recordId AS "processCraft.recordId",
			c.name AS "processCraft.name"
		FROM eg_carda_process_craft_value a
		LEFT JOIN eg_carda_process_value b ON b.recordId = a.processValueId
		LEFT JOIN eg_process_craft c ON c.recordId = a.processCraftId
		LEFT JOIN eg_process d ON d.recordId = b.processId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND d.category LIKE '%大板%' 
		AND FIND_IN_SET(c.`name`,'耐压值,漏电流')  AND a.processCraftValue IS NOT NULL
		AND FIND_IN_SET(b.processCardAId,#{cardId})
	</select>

	<select id="findListByProcessValueTwo" resultType="CardAProcessCraftValue">
		SELECT
			a.recordId,
			c.`name` AS "processCraft.name",
			a.processCraftValue,
			b.processCardAId AS "cardId",
			d.category,
			b.remark
		FROM eg_carda_process_craft_value a
		LEFT JOIN eg_carda_process_value b ON b.recordId = a.processValueId
		LEFT JOIN eg_process_craft c ON c.recordId = a.processCraftId
		LEFT JOIN eg_process d ON d.recordId = b.processId
		WHERE a.companyId = #{companyId} AND a.activeFlag = 1
		AND b.processCardAId IN(${cardId}) AND REPLACE(d.category,' ','') LIKE CONCAT('%',#{processName},'%')
	</select>
	
</mapper>