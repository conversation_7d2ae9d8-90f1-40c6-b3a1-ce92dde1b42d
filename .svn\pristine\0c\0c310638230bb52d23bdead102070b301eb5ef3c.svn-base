<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.production.dao.PopoDao">
    
	<sql id="produceOutwardProcessingOrderColumns">
		a.recordId AS "recordId",
		a.companyId AS "company.recordId",
		a.no AS "no",
		a.craftNo AS "craftNo",
		a.processId AS "process.recordId",
		a.operator AS "operator.recordId",
		a.produceBatchId AS "produceBatch.recordId",
		a.produceBatchDetailId AS "produceBatchDetail.recordId",
		a.notificationId AS "notification.recordId",
		a.supplierId AS "supplier.recordId",
		a.qtyPnlA AS "qtyPnlA",
		a.qtySetA AS "qtySetA",
		a.qtyPcsA AS "qtyPcsA",
		a.qtyPnlB AS "qtyPnlB",
		a.qtySetB AS "qtySetB",
		a.qtyPcsB AS "qtyPcsB",
		a.qtyPnlT AS "qtyPnlT",
		a.qtySetT AS "qtySetT",
		a.qtyPcsT AS "qtyPcsT",
		a.time AS "time",
		a.area AS "area",
		a.price AS "price",
		a.money AS "money",
		a.auditor AS "auditor.recordId",
		a.auditTime AS "auditTime",
		a.status AS "status",
		a.payStatus AS "payStatus",
		a.activeFlag AS "activeFlag",
		a.createdBy AS "createdBy.recordId",
		a.createdDate AS "createdDate",
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate AS "lastUpdDate",
		a.remark AS "remark",
		a.hours AS "hours",
		a.endDate AS "endDate",
		a.materialName AS "materialName",
		a.cutSize AS "cutSize",
		a.productHours AS "productHours",
		a.type AS "type"
	</sql>
	
	<sql id="produceOutwardProcessingOrderJoins">
	</sql>
    
	<select id="get" resultType="Popo">
		SELECT 
			<include refid="produceOutwardProcessingOrderColumns"/>,
            b.category as "process.category",
            b.name as "process.name",
            c.no as "notification.no",
            d.name as "supplier.name",
            d.checkDate as "supplier.checkDate",
            e.userName as "operator.userName",
            f.userName as "auditor.userName",
            h.no as "feedingNo",
            i.no AS "replenishNo"
		FROM pd_produce_outward_processing_order a
		<include refid="produceOutwardProcessingOrderJoins"/>
        join eg_process b on b.recordId = a.processId
        join sl_notification c on c.recordId = a.notificationId
        left join md_supplier d on d.recordId = a.supplierId
        join sm_user e on e.recordId = a.operator
        left join sm_user f on f.recordId = a.auditor
        LEFT JOIN pd_produce_batch g ON g.recordId = a.produceBatchId
        LEFT JOIN pd_feeding h ON h.recordId = g.feedingNo
        LEFT JOIN pd_replenish i ON i.produceBatchNo = a.produceBatchId
		WHERE a.recordId = #{recordId} 
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>

    <select id="getInfoByProcessAndDetail" resultType="Popo">
		SELECT
			<include refid="produceOutwardProcessingOrderColumns"/>
		FROM pd_produce_outward_processing_order a
		<include refid="produceOutwardProcessingOrderJoins"/>
		WHERE a.companyId = #{company.recordId}
          and a.processId =#{process.recordId}
          and a.produceBatchDetailId = #{produceBatchDetail.recordId}
          and a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>

    <select id="getContractDetailMoney" resultType="java.math.BigDecimal">
        select sum(b.subTotal) from sl_notification a
        join sl_contract_detail b on b.recordId = a.contractDetailId
        where a.companyId=#{company.recordId} and (a.recordId = #{notification.recordId} or a.mergeId = #{notification.recordId})
	</select>

    <select id="getCountByTemplateIdAndProcess" resultType="java.lang.Integer">
        select count(1)
        from pd_produce_outward_processing_order a
        join pd_produce_batch b on b.recordId = a.produceBatchId and b.activeFlag =1 and b.status in(900301,900302,900303)
        join eg_carda c on c.recordId = b.processCardAId and c.activeFlag = 1 and c.status in(300101,300102) and c.templateId = #{templateId}
        join sl_notification d on d.processCardAId = c.recordId and d.activeFlag =1 and d.status in (200404,200405)
        where a.companyId = #{companyId} and a.activeFlag = 1 and a.status in (700601, 700603) and a.processId = #{processId}
    </select>

    <select id="findNotifications" resultType="Notification">
        select a.recordId, a.contractDetailId, a.no, a.craftNo, a.quantity, a.mergeId, a.mergeType,
           b.recordId as "contractDetail.recordId", b.quantity as "contractDetail.quantity"
        from sl_notification a
        left join sl_contract_detail b on b.recordId = a.contractDetailId
        where a.companyId=#{company.recordId} and (a.recordId = #{notification.recordId} or a.mergeId = #{notification.recordId})
	</select>

    <select id="findProduceOutwardBySupplier" resultType="Popo">
        select
          <include refid="produceOutwardProcessingOrderColumns"/>
        from pd_produce_outward_processing_order a
        where a.companyId = #{company.recordId} and a.activeFlag =  #{DEL_FLAG_NORMAL} and a.status in (700603, 700604) and a.payStatus = 0 and a.supplierId = #{supplier.recordId};
	</select>


    <resultMap id="batchDetailMap" type="ProduceBatchDetail">
        <id property="recordId" column="recordId" />
        <result property="qtyPnlT" column="qtyPnlT" />

        <association property="notification" javaType="Notification">
            <id property="recordId" column="notification.recordId"/>
            <result property="no" column="notification.no"/>
            <result property="craftNo" column="notification.craftNo"/>
            <association property="cardA"  javaType="CardA">
                <id property="recordId" column="notification.cardA.recordId"/>
                <result property="setWidth" column="notification.cardA.setWidth"/>
                <result property="setLength" column="notification.cardA.setLength"/>
                <result property="pnlDivisor" column="notification.cardA.pnlDivisor"/>
            </association>
        </association>

        <collection property="procraft"  ofType="ProduceBatchDetailCraft">
            <id property="recordId" column="procraft.recordId"/>
            <result property="craftNo" column="procraft.craftNo"/>
            <result property="qtyPcsT" column="procraft.qtyPcsT"/>

            <association property="boardPartCraft"  javaType="BoardPartCraft">
                <id property="recordId" column="procraft.boardPartCraft.recordId"/>
                <result property="setWidth" column="procraft.boardPartCraft.setWidth"/>
                <result property="setLength" column="procraft.boardPartCraft.setLength"/>
                <result property="pnlDivisor" column="procraft.boardPartCraft.pnlDivisor"/>
            </association>
        </collection>
    </resultMap>

    <select id="getBatchDetailInfo" resultMap="batchDetailMap">
        select
        a.recordId,
        a.qtyPnlT,
        c.recordId as "notification.recordId",
        c.no as "notification.no",
        c.craftNo as "notification.craftNo",
        d.recordId as "notification.cardA.recordId",
        d.setLength as "notification.cardA.setLength",
        d.setWidth as "notification.cardA.setWidth",
        d.pnlDivisor as "notification.cardA.pnlDivisor",

        e.recordId as "procraft.recordId",
        e.craftNo as "procraft.craftNo",
        e.qtyPcsT as "procraft.qtyPcsT",

        f.recordId as "procraft.boardPartCraft.recordId",
        f.setLength as "procraft.boardPartCraft.setLength",
        f.setWidth as "procraft.boardPartCraft.setWidth",
        f.pnlDivisor as "procraft.boardPartCraft.pnlDivisor"

        from pd_produce_batch_detail a
        join pd_produce_batch b on b.recordId = a.produceBatchId
        join sl_notification c on c.recordId = b.notificationId
        join eg_carda d on d.recordId = c.processCardAId
        left join pd_produce_batch_detail_craft e on e.produceBatchDetailId = a.recordId
        left join eg_cardb_board_part_craft f on f.notificationId = e.notificationId
        where a.recordId = #{recordId}
    </select>
    
    <resultMap type="Popo" id="findListPopoMap">
    	<id property="recordId" column="recordId"/>
    	<result property="no" column="no"/>
    	<result property="craftNo" column="craftno"/>
    	<result property="qtyPnlA" column="qtyPnlA"/>
    	<result property="qtySetA" column="qtySetA"/>
    	<result property="qtyPcsA" column="qtyPcsA"/>
    	<result property="qtyPnlB" column="qtyPnlB"/>
    	<result property="qtySetB" column="qtySetB"/>
    	<result property="qtyPcsB" column="qtyPcsB"/>
    	<result property="qtyPnlT" column="qtyPnlT"/>
    	<result property="qtySetT" column="qtySetT"/>
    	<result property="qtyPcsT" column="qtyPcsT"/>
    	<result property="time" column="time"/>
    	<result property="area" column="area"/>
    	<result property="price" column="price"/>
    	<result property="money" column="money"/>
    	<result property="auditTime" column="auditTime"/>
    	<result property="status" column="status"/>
    	<result property="payStatus" column="payStatus"/>
    	<result property="activeFlag" column="activeFlag"/>
    	<result property="createdDate" column="createdDate"/>
    	<result property="lastUpdDate" column="lastUpdDate"/>
    	<result property="remark" column="remark"/>
    	<result property="hours" column="hours"/>
    	<result property="endDate" column="endDate"/> 
    	<result property="materialName" column="materialName"/> 
    	<result property="cutSize" column="cutSize"/> 
    	<result property="productHours" column="productHours"/> 
    	<result property="type" column="type"/> 
    	
    	<association property="company" column="companyId" javaType="Company">
    		<id property="recordId" column="company.recordId"/>
    	</association>
    	<association property="process" column="processId" javaType="EgProcess">
    		<id property="recordId" column="process.recordId"/>
    		<result property="category" column="process.category"/>
    		<result property="name" column="process.name"/>
    	</association>
    	<association property="operator" column="operator" javaType="User">
    		<id property="recordId" column="operator.recordId"/>
    		<result property="userName" column="operator.userName"/>
    	</association>
    	<association property="produceBatch" column="producebatchId" javaType="ProduceBatch">
    		<id property="recordId" column="produceBatch.recordId"/>
    		<result property="no" column="produceBatch.no"/>
    	</association>
    	<association property="produceBatchDetail" column="producebatchdetailId" javaType="ProduceBatchDetail">
    		<id property="recordId" column="produceBatchDetail.recordId"/>
    		<result property="no" column="produceBatchDetail.no"/>
    		<!-- 批次明细工艺列表 -->
    		<collection property="procraft" 
    			column="{recordId=produceBatchDetail.recordId,DEL_FLAG_NORMAL=activeFlag,company.recordId=company.recordId}" 
    			select="com.kyb.pcberp.modules.production.dao.ProduceBatchDetailCraftDao.getprocralist"/>
    	</association>
    	<association property="notification" column="notificationId" javaType="Notification">
    		<id property="recordId" column="notification.recordId"/>
    		<result property="no" column="notification.no"/>
    	</association>
    	<association property="supplier" column="supplierId" javaType="Supplier">
    		<id property="recordId" column="supplier.recordId"/>
    		<result property="name" column="supplier.name"/>
    	</association>
    	<association property="auditor" column="auditor" javaType="User">
    		<id property="recordId" column="auditor.recordId"/>
    		<result property="userName" column="auditor.userName"/>
    	</association>
    	<association property="createdBy" column="createdBy" javaType="User">
    		<id property="recordId" column="createdBy.recordId"/>
    	</association>
    	<association property="lastUpdBy" column="lastUpdBy" javaType="User">
    		<id property="recordId" column="lastUpdBy.recordId"/>
    	</association>
    	<!-- 费用列表 -->
    	<collection property="costList" column="{popo.recordId=recordId,activeFlag=activeFlag,company.recordId=company.recordId}" select="findCostListByPopo"/>
    	<!-- 工艺列表 -->
    	<collection property="craftList" column="{popo.recordId=recordId,activeFlag=activeFlag,company.recordId=company.recordId}" select="findCraftListByPopo"/>
    </resultMap>
    
    <select id="findCostListByPopo" resultType="PopoCost">
    	SELECT * FROM pd_produce_outward_processing_order_cost
    	WHERE popoId = #{popo.recordId}
    	AND activeFlag = #{activeFlag}
    	AND companyId = #{company.recordId}
    </select>
    
    <select id="findCraftListByPopo" resultType="PopoCraft">
    	SELECT * FROM pd_produce_outward_processing_order_craft
    	WHERE popoId = #{popo.recordId}
    	AND activeFlag = #{activeFlag}
    	AND companyId = #{company.recordId}
    </select>

    <select id="findList" resultMap="findListPopoMap">
		SELECT 
			<include refid="produceOutwardProcessingOrderColumns"/>,
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(b.category," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(b.category," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(b.category," ","") END AS "process.category",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(b.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(b.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(b.name," ","") END AS "process.name",
            c.no as "notification.no",
            d.name as "supplier.name",
            e.userName as "operator.userName",
            f.userName as "auditor.userName",
            g.no AS "produceBatch.no",
            h.no AS "produceBatchDetail.no"
		FROM pd_produce_outward_processing_order a
		<include refid="produceOutwardProcessingOrderJoins"/>
        JOIN eg_process b on b.recordId = a.processId
        JOIN sl_notification c on c.recordId = a.notificationId
        LEFT JOIN md_supplier d on d.recordId = a.supplierId
        JOIN sm_user e on e.recordId = a.operator
        LEFT JOIN sm_user f on f.recordId = a.auditor
        LEFT JOIN pd_produce_batch g ON g.recordId = a.produceBatchId
        LEFT JOIN eg_carda_process_value ecpv on ecpv.processCardAId = g.processCardAId AND ecpv.processId = b.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
        LEFT JOIN pd_produce_batch_detail h ON h.recordId = a.produceBatchDetailId
       	LEFT JOIN eg_process_craft_install epci ON epci.processId = b.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1 
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
            <if test="no != null and no != ''">
                AND a.no like CONCAT('%', #{no}, '%')
            </if>

            <if test="craftNo != null and craftNo != ''">
                AND a.craftNo like CONCAT('%', #{craftNo}, '%')
            </if>

            <if test="cardNo != null and cardNo != ''">
                AND (a.produceBatchId like CONCAT('%', #{cardNo}, '%') OR a.produceBatchDetailId like CONCAT('%', #{cardNo}, '%'))
            </if>

            <if test="status != null and status != ''">
                AND a.status = #{status}
            </if>

            <if test="process != null and process.category != null and process.category != ''">
                AND b.category like CONCAT('%', #{process.category}, '%')
            </if>

            <if test="notification != null and notification.no != null and notification.no != ''">
                AND c.no like CONCAT('%', #{notification.no}, '%')
            </if>

            <if test="sentTimeStartQr!=null and sentTimeStartQr!='' and sentTimeEndQr!=null and sentTimeEndQr!=''">
                and a.time between #{sentTimeStartQr} and #{sentTimeEndQr}
            </if>
            
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} OR a.operator = #{createdBy.recordId} OR a.auditor = #{createdBy.recordId}) 
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
    <select id="findListForInspect" resultMap="findListPopoMap">
		SELECT 
			<include refid="produceOutwardProcessingOrderColumns"/>,
            b.category as "process.category",
            b.name as "process.name",
            c.no as "notification.no",
            d.name as "supplier.name",
            e.userName as "operator.userName",
            f.userName as "auditor.userName",
            g.no AS "produceBatch.no",
            h.no AS "produceBatchDetail.no"
		FROM pd_produce_outward_processing_order a
		<include refid="produceOutwardProcessingOrderJoins"/>
        join eg_process b on b.recordId = a.processId
        join sl_notification c on c.recordId = a.notificationId
        left join md_supplier d on d.recordId = a.supplierId
        join sm_user e on e.recordId = a.operator
        left join sm_user f on f.recordId = a.auditor
        LEFT JOIN pd_produce_batch g ON g.recordId = a.produceBatchId
        LEFT JOIN pd_produce_batch_detail h ON h.recordId = a.produceBatchDetailId
        WHERE a.companyId = #{company.recordId} 
        AND a.activeFlag = #{DEL_FLAG_NORMAL}
        AND a.status = #{status}
	</select>
	
    <select id="findPopoById" resultMap="findListPopoMap">
		SELECT 
			<include refid="produceOutwardProcessingOrderColumns"/>,
            b.category as "process.category",
            b.name as "process.name",
            c.no as "notification.no",
            d.name as "supplier.name",
            e.userName as "operator.userName",
            f.userName as "auditor.userName",
            g.no AS "produceBatch.no",
            h.no AS "produceBatchDetail.no"
		FROM pd_produce_outward_processing_order a
		<include refid="produceOutwardProcessingOrderJoins"/>
        join eg_process b on b.recordId = a.processId
        join sl_notification c on c.recordId = a.notificationId
        left join md_supplier d on d.recordId = a.supplierId
        join sm_user e on e.recordId = a.operator
        left join sm_user f on f.recordId = a.auditor
        LEFT JOIN pd_produce_batch g ON g.recordId = a.produceBatchId
        LEFT JOIN pd_produce_batch_detail h ON h.recordId = a.produceBatchDetailId
        WHERE a.recordId = #{recordId}
	</select>
	
	<select id="findAllList" resultType="Popo">
		SELECT 
			<include refid="produceOutwardProcessingOrderColumns"/>
		FROM pd_produce_outward_processing_order a
		<include refid="produceOutwardProcessingOrderJoins"/>
		<where>
            a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>

    <resultMap id="batchDetail_CraftMap" type="ProduceBatchDetail">
        <id property="recordId" column="recordId" />
        <result property="no" column="no" />
        <result property="qtyPnlA" column="qtyPnlA" />
        <result property="qtySetA" column="qtySetA" />
        <result property="qtyPcsA" column="qtyPcsA" />
        <result property="qtyPnlB" column="qtyPnlB" />
        <result property="qtySetB" column="qtySetB" />
        <result property="qtyPcsB" column="qtyPcsB" />
        <result property="qtyPnlT" column="qtyPnlT" />
        <result property="qtySetT" column="qtySetT" />
        <result property="qtyPcsT" column="qtyPcsT" />

        <collection property="procraft"  ofType="ProduceBatchDetailCraft">
            <id property="recordId" column="procraft.recordId"/>
            <result property="notificationId" column="procraft.notificationId"/>
            <result property="quantity" column="procraft.quantity"/>
            <result property="craftNo" column="procraft.craftNo"/>
            <result property="qtyPnlA" column="procraft.qtyPnlA"/>
            <result property="qtySetA" column="procraft.qtySetA"/>
            <result property="qtyPcsA" column="procraft.qtyPcsA"/>
            <result property="qtyPnlB" column="procraft.qtyPnlB"/>
            <result property="qtySetB" column="procraft.qtySetB"/>
            <result property="qtyPcsB" column="procraft.qtyPcsB"/>
            <result property="qtyPnlT" column="procraft.qtyPnlT"/>
            <result property="qtySetT" column="procraft.qtySetT"/>
            <result property="qtyPcsT" column="procraft.qtyPcsT"/>
        </collection>
    </resultMap>

    <select id="findBatchDetailAndCraft" resultMap="batchDetail_CraftMap">
        select
            a.recordId,
            a.no as "no",
            a.qtyPnlA AS "qtyPnlA",
            a.qtySetA AS "qtySetA",
            a.qtyPcsA AS "qtyPcsA",
            a.qtyPnlB AS "qtyPnlB",
            a.qtySetB AS "qtySetB",
            a.qtyPcsB AS "qtyPcsB",
            a.qtyPnlT AS "qtyPnlT",
            a.qtySetT AS "qtySetT",
            a.qtyPcsT AS "qtyPcsT",

            b.recordid AS "procraft.recordId",
            b.notificationId AS "procraft.notificationId",
            b.quantity AS "procraft.quantity",
            b.craftno AS "procraft.craftNo",
            b.qtypnla AS "procraft.qtyPnlA",
            b.qtyseta AS "procraft.qtySetA",
            b.qtypcsa AS "procraft.qtyPcsA",
            b.qtypnlb AS "procraft.qtyPnlB",
            b.qtysetb AS "procraft.qtySetB",
            b.qtypcsb AS "procraft.qtyPcsB",
            b.qtypnlt AS "procraft.qtyPnlT",
            b.qtysett AS "procraft.qtySetT",
            b.qtypcst AS "procraft.qtyPcsT"
        from pd_produce_batch_detail a
        join pd_produce_batch_detail_craft b on b.produceBatchDetailId = a.recordId and b.activeFlag = #{DEL_FLAG_NORMAL}
        where a.recordId = #{produceBatchDetail.recordId};
    </select>

    <insert id="insert" keyProperty="recordId" useGeneratedKeys="true">
		INSERT INTO pd_produce_outward_processing_order(
			companyId,
			no,
			craftNo,
			processId,
			operator,
			produceBatchId,
			produceBatchDetailId,
			notificationId,
			supplierId,
			qtyPnlA,
			qtySetA,
			qtyPcsA,
			qtyPnlB,
			qtySetB,
			qtyPcsB,
			qtyPnlT,
			qtySetT,
			qtyPcsT,
			time,
			area,
			price,
			money,
			auditor,
			auditTime,
			hours,
			status,
			payStatus,
			activeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			remark,
			endDate,
			materialName,
			cutSize,
			productHours,
			type
		) VALUES (
			#{company.recordId},
			#{no},
			#{craftNo},
			#{process.recordId},
			#{operator.recordId},
			#{produceBatch.recordId},
			#{produceBatchDetail.recordId},
			#{notification.recordId},
			#{supplier.recordId},
			#{qtyPnlA},
			#{qtySetA},
			#{qtyPcsA},
			#{qtyPnlB},
			#{qtySetB},
			#{qtyPcsB},
			#{qtyPnlT},
			#{qtySetT},
			#{qtyPcsT},
			#{time},
			#{area},
			#{price},
			#{money},
			#{auditor.recordId},
			#{auditTime},
			#{hours},
			#{status},
			#{payStatus},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{endDate},
			#{materialName},
			#{cutSize},
			#{productHours},
			#{type}
		)
	</insert>
	
	<update id="update">
		UPDATE pd_produce_outward_processing_order SET 	
			money = #{money},
			remark = #{remark},
			materialName = #{materialName},
			hours = #{hours},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate},
			remark = #{remark}
		WHERE recordId = #{recordId}
	</update>

    <update id="updateStatus">
        UPDATE pd_produce_outward_processing_order SET
            status = #{status},
            lastupdby = #{lastUpdBy.recordId},
            lastupddate = #{lastUpdDate}
            <if test="endDate != null and endDate != ''">
            	,endDate = #{endDate},
            	productHours = #{productHours}
            </if>
            <if test="auditor != null and auditor.recordId != null and auditor.recordId != ''">
            	,auditor = #{auditor.recordId},
            	auditTime = #{auditTime}
            </if>
        WHERE recordId = #{recordId}
    </update>

    <select id="findInvalidOutward" resultType="Popo">
        select recordId from pd_produce_outward_processing_order
        WHERE companyId = #{company.recordId}
          and produceBatchId = #{produceBatch.recordId}
          and activeFlag = #{DEL_FLAG_NORMAL}
          and status in (700601, 700603);
    </select>
          
    <update id="invalid">
        UPDATE pd_produce_outward_processing_order SET
        status = 700605,
        lastupdby = #{lastUpdBy.recordId},
        lastupddate = #{lastUpdDate}
        WHERE companyId = #{company.recordId}
          and produceBatchId = #{produceBatch.recordId}
          and activeFlag = #{DEL_FLAG_NORMAL}
          and status in (700601, 700603);
    </update>

    <update id="updatePayStatus">
        UPDATE pd_produce_outward_processing_order SET
            payStatus = #{payStatus},
            lastupdby = #{lastUpdBy.recordId},
            lastupddate = #{lastUpdDate}
        WHERE recordId = #{recordId}
    </update>
	
	<update id="delete">
		UPDATE pd_produce_outward_processing_order SET
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>
	
</mapper>