package com.kyb.pcberp.modules.wechat.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

public class IcloudMoudleUser extends DataEntity<IcloudMoudleUser>
{
    private String moudleId;

    private String phone;

    private Integer sortNum;

    private String adjustFlag;

    public String getMoudleId()
    {
        return moudleId;
    }

    public void setMoudleId(String moudleId)
    {
        this.moudleId = moudleId;
    }

    public String getPhone()
    {
        return phone;
    }

    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public Integer getSortNum()
    {
        return sortNum;
    }

    public void setSortNum(Integer sortNum)
    {
        this.sortNum = sortNum;
    }

    public String getAdjustFlag()
    {
        return adjustFlag;
    }

    public void setAdjustFlag(String adjustFlag)
    {
        this.adjustFlag = adjustFlag;
    }
}
