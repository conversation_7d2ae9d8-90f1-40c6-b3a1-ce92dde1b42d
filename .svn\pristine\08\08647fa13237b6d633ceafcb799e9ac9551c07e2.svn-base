/**
 * 
 */
package com.kyb.pcberp.modules.purch.dao;

import java.util.List;
import java.util.Map;

import com.kyb.pcberp.modules.contract.entity.GroupCenterBill;
import com.kyb.pcberp.modules.hr.finance_center.pojo.Hr_ReportData;
import com.kyb.pcberp.modules.purch.entity.*;
import com.kyb.pcberp.modules.sys.entity.Company;
import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.crm.entity.GoodsCheck;
import com.kyb.pcberp.modules.crm.entity.ReconciliationData;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.sys.entity.User;

/**
 * 原材料收货/退货对账记录DAO接口
 * 
 * <AUTHOR>
 * @version 2015-10-08
 */
@MyBatisDao
public interface MaterialCheckDao extends CrudDao<MaterialCheck>
{
    public List<MaterialCheck> findExportMaterialCheckList(MaterialCheck materialCheck);
    
    /** zjn 2018-01-23 原料采购入库、补货入库、退货出库对账明细 */
    public List<MaterialCheck> findAllRawThreeList(MaterialCheck materialCheck);
    
    /** zjn 2018-02-26 原料采购 同一出入库时间所有调整的对账明细 */
    public List<MaterialCheck> findAllRawMaterialCheck(MaterialCheck materialCheck);
    
    /** zjn 2018-02-26 成品采购 同一出入库时间所有调整的对账明细 */
    public List<MaterialCheck> findAllPrdMaterialCheck(MaterialCheck materialCheck);
    
    /** zjn 2018-07-13 更新月份、调整标志和费用 */
    public void updateMaterialCheckAdjustFlag(MaterialCheck materialCheck);
    
    /** zjn 2018-04-17 根据供应商名称月份获取成品采购对账明细 */
    public List<MaterialCheck> getMaterialCheckListByName(AccountsPayable ap);
    
    /** zjn 2018-07-11 更新付款对账明细调整的费用 */
    public void updateAdjustAmount(MaterialCheck materialCheck);
    
    /** zjn 2018-08-13 原料入库(领用单) 同一出入库时间所有调整的对账明细 */
    public List<MaterialCheck> getAllRawStockMaterialCheck(MaterialCheck materialCheck);
    
    /** zjn 2018-10-16 根据对账明细获取对账明细 */
    public MaterialCheck getMaterialCheckById(MaterialCheck materialCheck);
    
    /** zjn 2018-11-15 查询付款对账单明细 */
    public List<MaterialCheck> getMaterialCheckDetail(Map<String, Object> map);
    
    /** zjn 2018-12-05 根据源记录id获取对账单明细 */
    public MaterialCheck getMaterialCheckBySourceId(Map<String, Object> map);
    
    /** zjn 2018-12-05 逻辑删除付款对账明细 */
    public void deleteMaterialCheck(MaterialCheck materialCheck);
    
    /** zjn 2018-12-12 更新操作状态 */
    public void updateOperateStatus(MaterialCheck materialCheck);
    
    /** zjn 2018-12-12 获取未确认完的对账单明细的条数 */
    public Integer getUnConfirmMaterialCheckCount(MaterialCheck materialCheck);
    
    /** zjn 2019-01-21 根据报表跟单员查询付款对账明细 */
    public List<MaterialCheck> getMaterialCheckByCreatedId(User user);
    
    /** tj 2019-03-04 根据采购单明细查询对账单明细 */
    public List<MaterialCheck> findMaterialCheckByDetail(PrdorderDetail prdorderDetail);
    
    /** tj 2019-03-04 修改费用 */
    public void updateCost(MaterialCheck check);
    
    /** tj 2019-03-04 根据原料采购单明细查询对账单明细 */
    public List<MaterialCheck> findMaterialCheckByDetail2(PurchasingDetail detail);
    
    /** tj 2019-03-17 修改付款对账单数量 */
    public void updateQuantity(MaterialCheck check);
    
    /** tj 2019-03-19 查询最后一条对账单明细 */
    public MaterialCheck findLastMaterialCheck(PrdorderDetail prdorderDetail);
    
    List<MaterialCheck> getWxMaterialChecks(AccountsPayable accountsPayable);
    
    List<MaterialCheck> getWxMaterialCheckDetails(MaterialCheck materialCheck);
    
    void updateMatCheckPriceData(PurchasingDetail purchasingDetail);
    
    List<MaterialCheck> getMatCheckSupplierData(PurchasingDetail purchasingDetail);
    
    Integer checkMatCheckConfirmCount(PurchasingDetail purchasingDetail);
    
    void delMcTwoData(MaterialCheck materialCheck);
    
    void saleCheckInsert(MaterialCheck materialCheck);
    
    void lnCheckInsert(MaterialCheck materialCheck);
    
    public void redPartUpdateMaterialCheck(MaterialCheck mc);
    
    void delMcOutSourceData(MaterialCheck materialCheck);
    
    void saleCheckOutSourceInsert(MaterialCheck materialCheck);
    
    // void lnCheckOutSourceInsert(MaterialCheck materialCheck);
    
    void saleCheckComplaintInsert(MaterialCheck materialCheck);
    
    void lnCheckComplaintInsert(MaterialCheck materialCheck);
    
    void saleCheckComplaintOutSourceInsert(MaterialCheck materialCheck);
    
    // void lnCheckComplaintOutSourceInsert(MaterialCheck materialCheck);
    
    List<String> getMaterialCheckGroupCenterId(MaterialCheck materialCheck);
    
    String getXsMcIds(MaterialCheck materialCheck);
    
    void updateOperateStatusTwo(MaterialCheck materialCheck);
    
    List<MaterialCheck> getChekPageList(MaterialCheck materialCheck);
    
    MaterialCheck getSourcePeriodChecks(MaterialCheck materialCheck);
    
    void batchInsertAdjustData(@Param("insertList") List<MaterialCheck> insertList);
    
    void batchUpdateAdjustData(@Param("updateList") List<MaterialCheck> updateList);
    
    void batchDeleteAdjustData(@Param("recordId") String recordId);
    
    void saleCheckOutSourceTwoInsert(MaterialCheck materialCheck);
    
    public List<GoodsCheck> getReceivableExceptionList(GoodsCheck goodsCheck);
    
    public List<ReconciliationData> getExportExceptionList(GoodsCheck goodsCheck);
    
    void updateExcelPath(@Param("customerId") String customerId, @Param("period") String period,
        @Param("sourcePath") String sourcePath);
        
    void updateGoodsMatchStatus(@Param("customerId") String customerId, @Param("period") String period,
        @Param("matchStatus") String matchStatus);

    void deleteReconciliation(@Param("customerId") String customerId, @Param("period") String period);
    
    void batchInsetReconciliation(@Param("list") List<ReconciliationData> list);

    List<ReconciliationData> getFailDataList(@Param("customerId") String customerId, @Param("period") String period,
        @Param("type") String type);

    void updateReceivableStatus(@Param("customerId") String customerId, @Param("period") String period,
        @Param("receivableStatus") String receivableStatus);

    void updateReconMatchStatus(@Param("customerId") String customerId, @Param("period") String period,
        @Param("matchStatus") String matchStatus, @Param("goodCheckId") String goodCheckId,
        @Param("failReson") String failReson);

    List<ReconciliationData> getReconExcelList(@Param("customerId") String customerId, @Param("period") String period);

    void batchUpdateReconList(@Param("list") List<ReconciliationData> list);

    void batchUpdateGoodCheckList(@Param("list") List<GoodsCheck> list);

        
    
    List<MaterialCheck> getMyMcList(MaterialCheck materialCheck);

    public void updateGoodsCheckException(GoodsCheck goodsCheck);

    public void updateReconciliationException(GoodsCheck goodsCheck);
    
    void sentReturnPayable(PuReturnsDetail puReturnsDetail);
    
    List<MaterialCheck> getMatCheckSumAmount(MaterialCheck materialCheck);
    
    void updateOriginalAmount(MaterialCheck materialCheck);
    
    void updateProofPath(@Param("supplierId") String supplierId, @Param("period") String period,
        @Param("sourcePath") String sourcePath);
    
    Integer checkMaterialCheckByDetailId(PrdorderDetail prdorderDetail);
    
    List<MaterialCheck> getConcatMxList(@Param("concatMxId") String concatMxId);
    
    Integer checkMaterialCheckByRawDetailId(PurchasingDetail purchasingDetail);

    public void updateSourceId(@Param("recordId")String recordId, @Param("orgId")String orgId);
    
    List<MaterialCheck> getSupplyChainData(MaterialCheck materialCheck);
    
    List<MaterialCheck> getSourceList(MaterialCheck materialCheck);
    
    MaterialCheck getConcatData(RawmaterialStock rawmaterialStock);
    
    List<MaterialCheck> getOutsideMaterialCheck(@Param("groupCenterId") String groupCenterId);
    
    void updatePeriod(MaterialCheck materialCheck);
    
    void updateMcPeriodData(MaterialCheck materialCheck);
    
    MaterialCheck getIdData(MaterialCheck materialCheck);
    
    MaterialCheck getIdDataTwo(@Param("recordId")String recordId);
    
    void updatePriceAmount(MaterialCheck materialCheck);
    
    List<MaterialCheck> getListByOldStokckId(@Param("oldPrdStockId")String oldPrdStockId);
    
    void updateAddAmount(MaterialCheck materialCheck);

    void batchUpdatePayWay(MaterialCheck materialCheck);

    Integer getUnCheckMaterialCheckCount(MaterialCheck materialCheck);

    List<MaterialCheck> getInitCheckData(@Param("ecoemyId")String ecoemyId);

    void updateMaterial(MaterialCheck materialCheck);

    List<MaterialCheck> getInitCheckDataTwo(@Param("ecoemyId")String ecoemyId);

    List<Company> getsaleCustomGiveId (GroupCenterBill groupCenterBill);

    void updateDetailStatusData(MaterialCheck materialCheck);

    void updateSourceIdTwo(@Param("checkorgId")String checkorgId,@Param("checkDetailId")String checkDetailId);

    void renewalOperateStatus(MaterialCheck materialCheck);

    List<Company>getCompanyFlowDate(@Param("recordId")String recordId);

    void cleanFeeAdjust(MaterialCheck materialCheck);

    List<MaterialCheck> getUnReconciliationList(Hr_ReportData reportData);

    MaterialCheck getMaterialCheckData(@Param("recordId") String recordId);

    List<GroupSupplyChainProcess> getSupplyChainProcessList(MaterialCheck materialCheck);

    List<MaterialCheck> getListByOrgId(MaterialCheck materialCheck);

    String getSourceGcId(@Param("companyId") String companyId,@Param("orgId") String orgId);

    List<String> getOrgList(MaterialCheck materialCheck);

    List<MaterialCheck> getSourceNoList(MaterialCheck materialCheck);

}