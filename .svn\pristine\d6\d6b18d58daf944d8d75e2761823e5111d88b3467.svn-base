/**
 * 带上传文件类型过滤器
 */
kybApp.filter('fileTypeFilter', function () {
    return function (item, mode) {
        var out = item;
        if (out === 4) {
            out = !mode ? "附件" : 'fBoard';
        }
        else if (out === 1) {
            out = !mode ? "大板" : 'dBoard';
        }
        else if (out === 2) {
            out = !mode ? "A板" : 'aBoard';
        }
        else if (out === 3) {
            out = !mode ? "B板" : 'bBoard';
        }
        return out;
    };
});

/**
 * 流程卡状态过滤器
 */
kybApp.filter('cardStatusFilter', function () {
    return function (item) {
        var out = item;
        if (out === 300101) {
            out = "未确认";
        }
        else if (out === 300102) {
            out = "已确认";
        }
        return out;
    };
});
