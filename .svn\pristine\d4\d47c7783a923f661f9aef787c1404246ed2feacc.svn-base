package com.kyb.pcberp.modules.report.service;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.modules.contract.dao.*;
import com.kyb.pcberp.modules.contract.entity.*;
import com.kyb.pcberp.modules.crm.dao.GoodsCheckDao;
import com.kyb.pcberp.modules.crm.entity.GoodsCheck;
import com.kyb.pcberp.modules.eg.dao.ProcessDao;
import com.kyb.pcberp.modules.eg.entity.EgProcess;
import com.kyb.pcberp.modules.finance.entity.CollectMuchMoney;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_MarkManage;
import com.kyb.pcberp.modules.production.dao.BatchDetailSetupDao;
import com.kyb.pcberp.modules.production.dao.BottleneckProcessUseDao;
import com.kyb.pcberp.modules.production.dao.ProduceRecordDao;
import com.kyb.pcberp.modules.production.entity.BatchDetailSetup;
import com.kyb.pcberp.modules.production.entity.BottleneckProcessUse;
import com.kyb.pcberp.modules.production.entity.ProduceRecord;
import com.kyb.pcberp.modules.production.service.ControlsService;
import com.kyb.pcberp.modules.production.service.PlanSchedulingService;
import com.kyb.pcberp.modules.purch.dao.PurchasingDao;
import com.kyb.pcberp.modules.purch.dao.PurchasingDetailDao;
import com.kyb.pcberp.modules.purch.entity.PurchRaw;
import com.kyb.pcberp.modules.purch.entity.Purchasing;
import com.kyb.pcberp.modules.report.dao.DeliveryAssessmentDao;
import com.kyb.pcberp.modules.report.entity.*;
import com.kyb.pcberp.modules.report.utils.ReportAllUtils;
import com.kyb.pcberp.modules.stock.dao.ProductStoreDao;
import com.kyb.pcberp.modules.stock.dao.RawmaterialStockDao;
import com.kyb.pcberp.modules.stock.entity.ProductStore;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.sys.dao.DepartmentDao;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.GroupOrgRelation;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class AppraisalReportService
{
    @Autowired
    private DeliveryAssessmentDao deliveryAssessmentDao;

    @Autowired
    private NotificationDao notificationDao;

    @Autowired
    private ProductStoreDao productStoreDao;

    @Autowired
    private MatPreparationDao matPreparationDao;

    @Autowired
    private PurchasingDetailDao purchasingDetailDao;

    @Autowired
    private GoodsCheckDao goodsCheckDao;

    @Autowired
    private DepartmentDao departmentDao;

    @Autowired
    private ControlsService controlsService;

    @Autowired
    private ContractDetailDao contractDetailDao;

    @Autowired
    private CapacityDao capacityDao;

    @Autowired
    private BottleneckProcessUseDao bottleneckProcessUseDao;

    @Autowired
    private RawmaterialStockDao rawmaterialStockDao;

    @Autowired
    private ProcessDao processDao;

    @Autowired
    private ProduceRecordDao produceRecordDao;

    @Autowired
    private BatchDetailSetupDao batchDetailSetupDao;

    @Autowired
    private PurchasingDao purchasingDao;

    @Autowired
    private MaterialContractDao materialContractDao;

    @Autowired
    private DeliveryDetailDao deliveryDetailDao;

    @Autowired
    private PlanSchedulingService planSchedulingService;

    public Page<DeliveryAssessmentDetail> getDeliveryAssessmentDetailPage(@RequestBody DeliveryAssessmentDetail detail,
        Page<DeliveryAssessmentDetail> qpage)
    {
        User user = UserUtils.getUser();
        // 设置查询范围
        if (detail.getQueryAll() != null && !detail.getQueryAll())
        {
            detail.setCreatedBy(user);
        }
        // 设置查询企业编号
        if (detail != null)
        {
            detail.setCompany(UserUtils.getUser().getCompany());
        }
        // 分页查询数据
        if (StringUtils.isNotBlank(detail.getPageNo()) && StringUtils.isNotBlank(detail.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(detail.getPageNo()));
            qpage.setPageSize(Integer.parseInt(detail.getPageSize()));

            // 设置排序
            if (StringUtils.isNotBlank(detail.getOrderBy()))
            {
                qpage.setOrderBy(detail.getOrderBy());
            }
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        detail.setPage(qpage);
        List<DeliveryAssessmentDetail> list = notificationDao.getDeliveryAssessmentPage(detail);
        setPageConcatData(list, detail.getCompany().getRecordId());
        qpage.setList(list);
        return qpage;
    }

    public void setPageConcatData(List<DeliveryAssessmentDetail> list, String companyId)
    {
        if (Collections3.isNotEmpty(list))
        {
            List<String> contractDetailIdList = Lists.newArrayList();
            for (DeliveryAssessmentDetail da : list)
            {
                if (!(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString().equals(da.getStatus())
                    || TypeKey.SL_CONTRACT_STATUS_CHECKED.toString().equals(da.getStatus())))
                {
                    continue;
                }
                if (StringUtils.isNotBlank(da.getContractDetailId()))
                {
                    contractDetailIdList.add(da.getContractDetailId());
                }
            }
            if (Collections3.isNotEmpty(contractDetailIdList))
            {
                List<ProductStore> productStoreList = productStoreDao.getProListByIds(contractDetailIdList, companyId);

                for (DeliveryAssessmentDetail da : list)
                {
                    da.setProductStoreList(Lists.newArrayList());
                    Integer quantity = 0;
                    if (Collections3.isNotEmpty(productStoreList))
                    {
                        for (ProductStore ps : productStoreList)
                        {
                            if (da.getContractDetailId().equals(ps.getContractDetailId()))
                            {
                                if (TypeKey.ST_PRODUCT_INOUTTYPE_OUT.compareTo(ps.getInoutType()) == 0)
                                {
                                    quantity += null == ps.getQuantity() ? 0 : ps.getQuantity();
                                    da.getProductStoreList().add(ps);
                                }
                            }
                        }
                    }
                    da.setQuantity(quantity);
                    da.getProductStoreList()
                        .sort((x, y) -> (null == x.getOperateDate() ? new Date() : x.getOperateDate()).compareTo(
                            null == y.getOperateDate() ? new Date() : y.getOperateDate()));
                    if (Collections3.isNotEmpty(da.getProductStoreList()))
                    {
                        da.setFirstSentTime(da.getProductStoreList().get(0).getOperateDate());
                        da.setSentTime(da.getProductStoreList().get(da.getProductStoreList().size() - 1).getOperateDate());
                    }
                    if(null != da.getEstimateStartDate())
                    {
                        Date estimateStartDate = DateUtils.parseDate(da.getEstimateStartDateStr());
                        if (null != da.getFirstBatchDate())
                        {
                            // 首批天数
                            Date firstBatchDate = DateUtils.parseDate(da.getFirstBatchDateStr());
                            Integer firstDays = DateUtils.getDays(estimateStartDate,firstBatchDate);
                            if(null != firstDays)
                            {
                                firstDays = firstDays + 1;
                                da.setFirstDays(firstDays.toString());

                            }
                        }
                        if (null != da.getEstimateDate())
                        {
                            // 最终天数
                            Date firstBatchDate = DateUtils.parseDate(da.getEstimateDateStr());
                            Integer finalDays = DateUtils.getDays(estimateStartDate,firstBatchDate);
                            if(null != finalDays)
                            {
                                finalDays = finalDays + 1;
                                da.setFinalDays(finalDays.toString());
                            }
                        }
                        if (null != da.getFirstSentTime())
                        {
                            // 第一次实际天数
                            Date firstSentTime = DateUtils.parseDate(da.getFirstSentTimeStr());
                            Integer firstSentDay = DateUtils.getDays(estimateStartDate,firstSentTime);
                            if(null != firstSentDay)
                            {
                                firstSentDay = firstSentDay + 1;
                                da.setFirstSentDay(firstSentDay);
                            }
                        }
                        if (null != da.getSentTime())
                        {
                            // 实际天数
                            Date sentTime = DateUtils.parseDate(da.getSentTimeStr());
                            Integer actualDays = DateUtils.getDays(estimateStartDate,sentTime);
                            if(null != actualDays)
                            {
                                actualDays = actualDays + 1;
                                da.setActualDays(actualDays.toString());
                            }
                        }
                    }
                }
            }
        }
    }

    public List<DeliveryAssessmentDetail> getAllDeliveryAssessment(DeliveryAssessmentDetail detail)
    {
        List<DeliveryAssessmentDetail> list = Lists.newArrayList();
        if (null == detail)
        {
            return list;
        }
        list = notificationDao.getDeliveryAssessmentPage(detail);
        if (Collections3.isEmpty(list))
        {
            return list;
        }
        setPageConcatData(list, detail.getCompany().getRecordId());
        return list;
    }

    /**
     * @Parm type: 1交货达成率报表，2市场报表报表
     */
    public Map<String, Object> loadData(DeliveryAssessmentDetail detail)
        throws ParseException
    {
        Map<String, Object> data = new HashMap<>();
        if (null == detail || null == detail.getType())
        {
            return data;
        }
        Company company = UserUtils.getUser().getCompany();

        // 集团部门
        List<GroupOrgRelation> deptList = CompanyUtil.getInstance().getGroupList();

        // 获取集团部门的销售公司
        String saleComIds = CompanyUtil.getInstance().getSaleIds();
        List<GroupOrgRelation> groupSaleComList = departmentDao.getGroupSaleComData(saleComIds);
        if (Collections3.isNotEmpty(deptList) && Collections3.isNotEmpty(groupSaleComList))
        {
            for (GroupOrgRelation dept : deptList)
            {
                for (GroupOrgRelation groupSaleCom : groupSaleComList)
                {
                    if (dept.getGroupOrgId().equals(groupSaleCom.getGroupOrgId()))
                    {
                        dept.setSaleComId(groupSaleCom.getSaleComId());
                        dept.setSaleComName(groupSaleCom.getSaleComName());
                        break;
                    }
                }
            }
        }
        data.put("deptList", deptList);

        // 获取使用版本
        DeliveryAssessmentVersion version = new DeliveryAssessmentVersion();
        version.setCompany(company);
        version.setType(detail.getType());
        version.setPeriod(detail.getPeriod());
        DeliveryAssessmentVersion useVersion = deliveryAssessmentDao.getUseVersion(version);
        if (null == useVersion)
        {
            useVersion = deliveryAssessmentDao.getNewVersion(version);
        }

        // 加载所有版本
        version = new DeliveryAssessmentVersion();
        version.setCompany(company);
        version.setType(detail.getType());
        List<DeliveryAssessmentVersion> versionList = deliveryAssessmentDao.getVersionList(version);
        String versionIds = null;
        for (DeliveryAssessmentVersion dv : versionList)
        {
            if (StringUtils.isNotBlank(versionIds))
            {
                versionIds = versionIds + "," + dv.getRecordId();
            }
            else
            {
                versionIds = dv.getRecordId();
            }
        }
        // 加载所有版本规则
        if (StringUtils.isNotBlank(versionIds))
        {
            DeliveryAssessmentRule ruleQuery = new DeliveryAssessmentRule();
            ruleQuery.setCompany(company);
            ruleQuery.setVersionId(versionIds);
            List<DeliveryAssessmentRule> ruleList = deliveryAssessmentDao.getRuleList(ruleQuery);
            for (DeliveryAssessmentVersion dav : versionList)
            {
                dav.setRuleList(Lists.newArrayList());
                for (DeliveryAssessmentRule dar : ruleList)
                {
                    if (dar.getVersionId().equals(dav.getRecordId()))
                    {
                        dav.getRuleList().add(dar);
                    }
                }
            }
            if (null != useVersion)
            {
                useVersion.setRuleList(Lists.newArrayList());
                for (DeliveryAssessmentRule dar : ruleList)
                {
                    if (dar.getVersionId().equals(useVersion.getRecordId()))
                    {
                        useVersion.getRuleList().add(dar);
                    }
                }
                data.put("useVersion", useVersion);
            }
        }
        data.put("versionList", versionList);

        switch (detail.getType())
        {
            // 交货考核
            case 1:

                // 获取汇总数据
                detail.setCompany(company);
                List<DeliveryAssessmentDetail> sumList = getAllDeliveryAssessment(detail);
                data.put("sumList", sumList);

                // 获取平均交期
                getFirstBatchAverageLeadTime(sumList,data);

                // 获取账期
                List<Integer> periodList = DateUtils.getPeriodList(null, null);
                data.put("periodList", periodList);

                // 获取结账按钮标志
                GoodsCheck gcQuery = new GoodsCheck();
                gcQuery.setCompany(detail.getCompany());
                gcQuery.setPeriod(Integer.valueOf(detail.getPeriod()));
                Boolean checkOutFlag = goodsCheckDao.getCheckOutFlag(gcQuery);
                data.put("checkOutFlag", checkOutFlag);

                // 获取操作账期有多少工作日
                String period = detail.getPeriod();
                if (StringUtils.isBlank(period))
                {
                    period = DateUtils.formatDate(new Date(), "yyyyMM");
                }
                if (StringUtils.isNotBlank(period))
                {
                    Integer periodValue = Integer.valueOf(period);
                    int year = periodValue / 100;
                    int month = periodValue % 100;
                    int days = controlsService.holidayList(year, month);
                    data.put("days", days);
                }
                break;
            // 市场报备考核
            case 2:
                // 获取汇总数据
                MarketAssessmentDetail marketAssessmentDetail = new MarketAssessmentDetail();
                marketAssessmentDetail.setCompany(company);
                List<MarketAssessmentDetail> maList = getAllMarketAssessment(marketAssessmentDetail);
                data.put("sumList", maList);

                break;
        }
        return data;
    }

    public List<MarketAssessmentDetail> getAllMarketAssessment(MarketAssessmentDetail marketAssessmentDetail)
    {
        List<MarketAssessmentDetail> list = Lists.newArrayList();
        if (null == marketAssessmentDetail)
        {
            return list;
        }
        list = matPreparationDao.getMarketAssessmentPage(marketAssessmentDetail);
        if (Collections3.isEmpty(list))
        {
            return list;
        }
        setMarketAssessmentPageData(list, marketAssessmentDetail.getCompany().getRecordId());
        return list;
    }

    public void setDeliveryAssessmentData(ExportExcel excel, List<DeliveryAssessmentDetail> list, String[] hearList)
    {
        for (DeliveryAssessmentDetail da : list)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList)
            {
                String val = "";
                switch (name)
                {
                    case "销售公司":
                        val = da.getSaleComName();
                        break;
                    case "终端客户":
                        val = da.getSaleCus();
                        break;
                    case "合同编号":
                        val = da.getContractNo();
                        break;
                    case "订单日期":
                        val = da.getOrderDateStr();
                        break;
                    case "客户订单号":
                        val = da.getCustomerPo();
                        break;
                    case "生产编号":
                        val = da.getCraftNo();
                        break;
                    case "订单面积(㎡)":
                        String orderArea = "-";
                        if (null != da.getOrderArea())
                        {
                            orderArea = da.getOrderArea().toString();
                        }
                        val = orderArea;
                        break;
                    case "客户型号":
                        val = da.getCustomerModule();
                        break;
                    case "曝光/丝印":
                        val = da.getProcessValue();
                        break;
                    case "订单类型":
                        val = da.getOrderTypeValue();
                        break;
                    case "部门":
                        val = da.getDeptName();
                        break;
                    case "业务员":
                        val = da.getSaleUserName();
                        break;
                    case "首批交期":
                        val = da.getFirstBatchDateStr();
                        break;
                    case "首批天数":
                        val = da.getFirstDays();
                        break;
                    case "最终交期":
                        val = da.getEstimateDateStr();
                        break;
                    case "最终天数":
                        val = da.getFinalDays();
                        break;
                    case "首批送货时间":
                        val = da.getFirstSentTimeStr();
                        break;
                    case "首批实际天数":
                        val = null == da.getFirstSentDay() ? "0" : da.getFirstSentDay().toString();
                        break;
                    case "首批超期天数":
                        String firstOverdueDays = "-";
                        if (null != da.getFirstOverdueDays())
                        {
                            firstOverdueDays = da.getFirstOverdueDays().toString();
                        }
                        val = firstOverdueDays;
                        break;
                    case "最终送货时间":
                        val = da.getSentTimeStr();
                        break;
                    case "最终实际天数":
                        val = da.getActualDays();
                        break;
                    case "最终超期天数":
                        String overdueDays = "-";
                        if (null != da.getOverdueDays())
                        {
                            overdueDays = da.getOverdueDays().toString();
                        }
                        val = overdueDays;
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    public List<DeliveryAssessmentRule> getdefaultRuleList(DeliveryAssessmentRule ruleQuery, Integer type)
    {
        List<DeliveryAssessmentRule> list = Lists.newArrayList();
        if (null == ruleQuery || null == type)
        {
            return list;
        }
        List<DeliveryAssessmentRule> defaultRuleList = deliveryAssessmentDao.getDefaultRuleList(ruleQuery);
        if (Collections3.isEmpty(defaultRuleList))
        {
            return list;
        }
        for (DeliveryAssessmentRule rule : defaultRuleList)
        {
            Boolean checkFlag = false;
            switch (type)
            {
                // 交货考核
                case 1:
                    switch (rule.getAbbreviation())
                    {
                        // 交货达成率目标值
                        case "targetValue":
                            checkFlag = true;
                            break;
                        // 超出率
                        case "excessRate":
                            checkFlag = true;
                            break;
                        // 超出目标值每平米奖励
                        case "targetValueReward":
                            checkFlag = true;
                            break;
                        // 低于率
                        case "belowRate":
                            checkFlag = true;
                            break;
                        // 低于目标值每平米惩罚
                        case "targetValuePunishment":
                            checkFlag = true;
                            break;
                        // 曝光日出货平米数
                        case "shippedDaliyArea":
                            checkFlag = true;
                            break;
                        // 丝印日出货平米数
                        case "silkScreenDaliyArea":
                            checkFlag = true;
                            break;
                        // 量产日出货款数
                        case "batchProductionDaliyCount":
                            checkFlag = true;
                            break;
                        // 样品日出货款数
                        case "sampleDaliyCount":
                            checkFlag = true;
                            break;
                    }
                    break;
                // 市场报备考核
                case 2:
                    switch (rule.getAbbreviation())
                    {
                        // 免费存放时间
                        case "freeStorageTime":
                            checkFlag = true;
                            break;
                        // 超出存放时间每平米收费
                        case "chargePerSquareMeterBeyondStorageTime":
                            checkFlag = true;
                            break;
                        // 按计时单位收费
                        case "chargeByTimeUnit":
                            checkFlag = true;
                            break;
                        // 按计时值收费
                        case "chargeByTimeValue":
                            checkFlag = true;
                            break;
                    }
                    break;
            }
            if (checkFlag)
            {
                list.add(rule);
            }
        }
        return list;
    }

    public Map<String, Object> loadRuleData(DeliveryAssessmentDetail detail)
    {
        Map<String, Object> data = new HashMap<>();
        Company company = UserUtils.getUser().getCompany();

        DeliveryAssessmentRule ruleQuery = new DeliveryAssessmentRule();
        ruleQuery.setCompany(company);

        // 获取最新的版本
        DeliveryAssessmentVersion versionQuery = new DeliveryAssessmentVersion();
        versionQuery.setCompany(company);
        versionQuery.setType(detail.getType());
        DeliveryAssessmentVersion version = deliveryAssessmentDao.getNewVersion(versionQuery);
        if (null != version)
        {
            ruleQuery.setVersionId(version.getRecordId());
            List<DeliveryAssessmentRule> ruleList = deliveryAssessmentDao.getRuleList(ruleQuery);
            version.setRuleList(ruleList);
            data.put("version", version);
        }

        // 获取系统默认规则
        List<DeliveryAssessmentRule> defaultRuleList = getdefaultRuleList(ruleQuery, detail.getType());
        data.put("defaultRuleList", defaultRuleList);
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> saveVesion(DeliveryAssessmentVersion version)
    {
        Map<String, Object> data = new HashMap<>();
        if (null == version || Collections3.isEmpty(version.getRuleList()) || StringUtils.isBlank(version.getFlag()))
        {
            data.put("result", "fail");
            data.put("message", "页面失效，请刷新重试!");
            return data;
        }
        String message = null;
        Company company = UserUtils.getUser().getCompany();
        version.setCompany(company);
        switch (version.getFlag())
        {
            // 保存
            case "1":
                DeliveryAssessmentVersion versionData = deliveryAssessmentDao.getVersion(version);
                if (StringUtils.isNotBlank(versionData.getUseFlag()) && "1".equals(versionData.getUseFlag()))
                {
                    data.put("result", "fail");
                    data.put("message", "版本在使用中了，不能进行修改!");
                    return data;
                }
                version.preUpdate();
                deliveryAssessmentDao.updateVersion(version);
                for (DeliveryAssessmentRule rule : version.getRuleList())
                {
                    rule.preUpdate();
                }
                deliveryAssessmentDao.batchUpdateRule(version.getRuleList());
                message = "保存成功!";
                break;
            // 创建新版本
            case "2":
                version.setVersionDate(new Date());
                version.preInsert();
                deliveryAssessmentDao.saveVersion(version);
                for (DeliveryAssessmentRule rule : version.getRuleList())
                {
                    rule.setCompany(company);
                    rule.setVersionId(version.getRecordId());
                    rule.preInsert();
                }
                deliveryAssessmentDao.batchSaveRule(version.getRuleList());
                message = "创建新版本成功!";
                break;
        }
        data.put("result", "success");
        data.put("message", message);
        return data;
    }

    public Page<MarketAssessmentDetail> getMarketAssessmentPage(
        @RequestBody MarketAssessmentDetail marketAssessmentDetail, Page<MarketAssessmentDetail> qpage)
    {
        User user = UserUtils.getUser();
        // 设置查询范围
        if (marketAssessmentDetail.getQueryAll() != null && !marketAssessmentDetail.getQueryAll())
        {
            marketAssessmentDetail.setCreatedBy(user);
        }
        // 设置查询企业编号
        if (marketAssessmentDetail != null)
        {
            marketAssessmentDetail.setCompany(UserUtils.getUser().getCompany());
        }
        // 分页查询数据
        if (StringUtils.isNotBlank(marketAssessmentDetail.getPageNo())
            && StringUtils.isNotBlank(marketAssessmentDetail.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(marketAssessmentDetail.getPageNo()));
            qpage.setPageSize(Integer.parseInt(marketAssessmentDetail.getPageSize()));

            // 设置排序
            if (StringUtils.isNotBlank(marketAssessmentDetail.getOrderBy()))
            {
                qpage.setOrderBy(marketAssessmentDetail.getOrderBy());
            }
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        marketAssessmentDetail.setPage(qpage);
        List<MarketAssessmentDetail> list = matPreparationDao.getMarketAssessmentPage(marketAssessmentDetail);
        setMarketAssessmentPageData(list, marketAssessmentDetail.getCompany().getRecordId());
        qpage.setList(list);
        return qpage;
    }

    public void setMarketAssessmentPageData(List<MarketAssessmentDetail> list, String companyId)
    {
        if (Collections3.isEmpty(list) || StringUtils.isBlank(companyId))
        {
            return;
        }
        Company company = new Company(companyId);

        // 获取使用版本
        DeliveryAssessmentVersion version = new DeliveryAssessmentVersion();
        version.setCompany(company);
        version.setType(2);
        DeliveryAssessmentVersion useVersion = deliveryAssessmentDao.getNewVersion(version);
        if (null != useVersion)
        {
            DeliveryAssessmentRule ruleQuery = new DeliveryAssessmentRule();
            ruleQuery.setCompany(company);
            ruleQuery.setVersionId(useVersion.getRecordId());
            List<DeliveryAssessmentRule> ruleList = deliveryAssessmentDao.getRuleList(ruleQuery);
            useVersion.setRuleList(ruleList);
        }

        String freeStorageTimeValue = null;
        String chargePerSquareMeterBeyondStorageTimeValue = null;
        String chargeByTimeUnitValue = null;
//        String chargeByTimeValueValue = null;
        if (null != useVersion && Collections3.isNotEmpty(useVersion.getRuleList()))
        {
            for (DeliveryAssessmentRule rule : useVersion.getRuleList())
            {
                switch (rule.getAbbreviation())
                {
                    // 免费存放时间
                    case "freeStorageTime":
                        freeStorageTimeValue = rule.getValue();
                        break;
                    // 超出存放时间每平米收费
                    case "chargePerSquareMeterBeyondStorageTime":
                        chargePerSquareMeterBeyondStorageTimeValue = rule.getValue();
                        break;
                    // 按计时单位收费
                   case "chargeByTimeUnit":
                        chargeByTimeUnitValue = rule.getValue();
                        break;
//                    // 按计时值收费
//                   case "chargeByTimeValue":
//                        chargeByTimeValueValue = rule.getValue();
//                        break;
                }
            }
        }

        String matPreparationIds = null;
        for (MarketAssessmentDetail ma : list)
        {
            if (StringUtils.isNotBlank(matPreparationIds))
            {
                matPreparationIds = matPreparationIds + "," + ma.getMatPreparationId();
            }
            else
            {
                matPreparationIds = ma.getMatPreparationId();
            }
        }
        if (StringUtils.isBlank(matPreparationIds))
        {
            return;
        }
        // 获取备料已做采购单的数据
        List<PurchRaw> purchRawList =
            purchasingDetailDao.getPurchMatPreparationData(companyId,matPreparationIds);
        for (MarketAssessmentDetail ma : list)
        {
            ma.setPurchasedArea(BigDecimal.ZERO);
            for (PurchRaw pr : purchRawList)
            {
                if (ma.getMatPreparationId().equals(pr.getMatPreparationId()))
                {
                    // 已采购面积处理
                    ma.setPurchasedArea(pr.getArea());
                    break;
                }
            }
            BigDecimal purchasedArea = null == ma.getPurchasedArea() ? BigDecimal.ZERO : ma.getPurchasedArea();
            BigDecimal useArea = null == ma.getUseArea() ? BigDecimal.ZERO : ma.getUseArea();
            // 剩余面积 = 已采购面积 - 使用面积
            BigDecimal remainingArea = purchasedArea.subtract(useArea);
            if (remainingArea.compareTo(BigDecimal.ZERO) <= 0)
            {
                remainingArea = BigDecimal.ZERO;
            }
            ma.setRemainingArea(remainingArea);

            // 到期日期
            if (StringUtils.isNotBlank(freeStorageTimeValue))
            {
                int days = Integer.parseInt(freeStorageTimeValue);
                ma.setExpirationDate(DateUtils.date_add(ma.getApplyTime(), days));
            }
            // 未结算金额
            MarketAssessmentDetail detail =  new MarketAssessmentDetail();
            detail.setExpirationDate(ma.getExpirationDate());
            Integer value = detail.getOverdueDays();
            Integer test = 0;
            if (StringUtils.isNotBlank(chargePerSquareMeterBeyondStorageTimeValue)
                && remainingArea.compareTo(BigDecimal.ZERO) > 0)
            {
                switch (chargeByTimeUnitValue){
                    case "1":
                        ma.setClosingAmount(remainingArea.multiply(new BigDecimal(chargePerSquareMeterBeyondStorageTimeValue)).multiply(new BigDecimal(value)));
                        break;
                    case "2":
                        test = value/7;
                         ma.setClosingAmount(remainingArea.multiply(new BigDecimal(chargePerSquareMeterBeyondStorageTimeValue)).multiply(new BigDecimal(test)));
                        break;
                    case "3":
                        test = value/30;
                        ma.setClosingAmount(remainingArea.multiply(new BigDecimal(chargePerSquareMeterBeyondStorageTimeValue)).multiply(new BigDecimal(test)));
                        break;
                    case "4":
                        test = value/365;
                        ma.setClosingAmount(remainingArea.multiply(new BigDecimal(chargePerSquareMeterBeyondStorageTimeValue)).multiply(new BigDecimal(test)));
                        break;
                }
            }
        }
    }

    public void setMarketAssessmentData(ExportExcel excel, List<MarketAssessmentDetail> list, String[] hearList)
    {
        for (MarketAssessmentDetail da : list)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList)
            {
                String val = "";
                switch (name)
                {
                    case "报备部门":
                        val = da.getDeptName();
                        break;
                    case "客户编号":
                        val = da.getCusNo();
                        break;
                    case "客户名称":
                        val = da.getCusName();
                        break;
                    case "规格型号":
                        val = da.getReportingSpecification();
                        break;
                    case "申请日期":
                        val = da.getApplyTimeStr();
                        break;
                    case "到货日期":
                        val = da.getArrivalDate();
                        break;
                    case "报备面积㎡":
                        String reportingArea = "-";
                        if (null != da.getReportingArea())
                        {
                            reportingArea = da.getReportingArea().toString();
                        }
                        val = reportingArea;
                        break;
                    case "已采购面积㎡":
                        String purchasedArea = "-";
                        if (null != da.getPurchasedArea())
                        {
                            purchasedArea = da.getPurchasedArea().toString();
                        }
                        val = purchasedArea;
                        break;
                    case "使用面积㎡":
                        String useArea = "-";
                        if (null != da.getUseArea())
                        {
                            useArea = da.getUseArea().toString();
                        }
                        val = useArea;
                        break;
                    case "考核面积㎡":
                        String remainingArea = "-";
                        if (null != da.getRemainingArea())
                        {
                            remainingArea = da.getRemainingArea().toString();
                        }
                        val = remainingArea;
                        break;
                    case "到期日期":
                        val = da.getExpirationDateStr();
                        break;
                    case "超期天数":
                        String overdueDays = "-";
                        if (null != da.getOverdueDays())
                        {
                            overdueDays = da.getOverdueDays().toString();
                        }
                        val = overdueDays;
                        break;
                    case "已结清":
                        String chargeAmount = "-";
                        if (null != da.getChargeAmount())
                        {
                            chargeAmount = da.getChargeAmount().toString();
                        }
                        val = chargeAmount;
                        break;
                    case "未结清":
                        String closingAmount = "-";
                        if (null != da.getClosingAmount())
                        {
                            closingAmount = da.getClosingAmount().toString();
                        }
                        val = closingAmount;
                        break;

                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    @Transactional(readOnly = false)
    public Map<String, Object> deliveryAssessmentSettlement(DeliveryAssessment deliveryAssessment)
        throws CloneNotSupportedException
    {
        Map<String, Object> data = new HashMap<>();
        if (null == deliveryAssessment)
        {
            data.put("result", "fail");
            data.put("message", "页面失效，请刷新重试!");
            return data;
        }
        String checkOutType = deliveryAssessment.getCheckOutType();
        String checkoutData = deliveryAssessment.getCheckoutData();
        String versionData = deliveryAssessment.getVersionData();
        if (StringUtils.isBlank(checkOutType) || StringUtils.isBlank(checkoutData) || StringUtils.isBlank(versionData))
        {
            data.put("result", "fail");
            data.put("message", "参数为空，请刷新重试!");
            return data;
        }
        Company company = UserUtils.getUser().getCompany();
        data = ReportAllUtils.saveCheckOutData(checkOutType, checkoutData, versionData, company);
        return data;
    }

    public Page<OrderDeliveryForm> getOrderDeliveryFormPage(Page<OrderDeliveryForm> page,OrderDeliveryForm orderDeliveryForm)
    {
        orderDeliveryForm.setPage(page);
        List<OrderDeliveryForm> pageList = contractDetailDao.getOrderDeliveryFormData(orderDeliveryForm);
        if(Collections3.isEmpty(pageList))
        {
            return page;
        }
        String detailIds = null;
        String groupCenterIds = null;
        for(OrderDeliveryForm order : pageList)
        {
            if(StringUtils.isNotBlank(order.getContractDetailId()))
            {
                if(StringUtils.isNotBlank(detailIds))
                {
                    detailIds = detailIds +","+ order.getContractDetailId();
                }
                else
                {
                    detailIds = order.getContractDetailId();
                }
            }
            if(StringUtils.isNotBlank(order.getGroupCenterId()))
            {
                if(StringUtils.isNotBlank(groupCenterIds))
                {
                    groupCenterIds = groupCenterIds +","+ order.getGroupCenterId();
                }
                else
                {
                    groupCenterIds = order.getGroupCenterId();
                }
            }
        }
        if(StringUtils.isNotBlank(detailIds))
        {
            List<ProductStore> storeList = productStoreDao.getStoreListByDetailIds(detailIds);
            if(Collections3.isNotEmpty(storeList))
            {
                for(OrderDeliveryForm order : pageList)
                {
                    order.setStoreList(Lists.newArrayList());
                    for(ProductStore store : storeList)
                    {
                        if(order.getContractDetailId().equals(store.getContractDetailId()))
                        {
                            if(null == order.getShippingDate())
                            {
                                order.setShippingDate(store.getOperateDate());
                            }
                            if(null == order.getShippingArea())
                            {
                                order.setShippingArea(store.getStockAreaReport());
                            }
                            order.getStoreList().add(store);
                        }

                    }
                }
            }
        }
        if(StringUtils.isNotBlank(groupCenterIds))
        {
            List<CapacityDeail> deailList = capacityDao.getCapacityDeailListByGroupCenterId(groupCenterIds);
            if(Collections3.isNotEmpty(deailList))
            {
                for(OrderDeliveryForm order : pageList)
                {
                    order.setCapacityDeailList(Lists.newArrayList());
                    for(CapacityDeail deail : deailList)
                    {
                        if(order.getGroupCenterId().equals(deail.getGroupCenterId()))
                        {
                            order.getCapacityDeailList().add(deail);
                        }
                    }
                }
            }
        }
        page.setList(pageList);
        return page;
    }

    public Page<MatPreparation> getReportSpareMaterialsPage(Page<MatPreparation> page, MatPreparation matPreparation)
    {
        matPreparation.setPage(page);
        List<MatPreparation> mpList = matPreparationDao.getReportSpareMaterialsData(matPreparation);
        List<MatPreparation>mpListTwo = new ArrayList<>();
        if(Collections3.isEmpty(mpList))
        {
            return page;
        }
        for (MatPreparation mp : mpList)
        {
            BigDecimal area = null == mp.getArea() ? BigDecimal.ZERO : mp.getArea();
            BigDecimal useArea = null == mp.getUseArea() ? BigDecimal.ZERO : mp.getUseArea();
            // 剩余面积 = 报备面积 - 使用面积
            BigDecimal remainingArea = area.subtract(useArea);
            if (remainingArea.compareTo(BigDecimal.ZERO) <= 0)
            {
                remainingArea = BigDecimal.ZERO;
            }
            mp.setRemainingArea(remainingArea);
        }
        for(MatPreparation matpre:mpList){
            if (StringUtils.isNotBlank(matPreparation.getMaterialSpecification())){
                if (StringUtils.isBlank(matpre.getMaterialSpecification())){
                    continue;
                }else{
                    if (!matpre.getMaterialSpecification().contains(matPreparation.getMaterialSpecification())){
                        continue;
                    }
                }
            }
            mpListTwo.add(matpre);
        }
        page.setList(mpListTwo);
        return page;
    }
    public Page<BottleneckProcessUse> getBottleneckProcessPage(Page<BottleneckProcessUse> page, BottleneckProcessUse bottleneckProcessUse)
    {
        bottleneckProcessUse.setPage(page);
        List<BottleneckProcessUse> pageList = bottleneckProcessUseDao.getBottleneckProcessPage(bottleneckProcessUse);
        if(Collections3.isEmpty(pageList))
        {
            return page;
        }
        String craftIds = null;
        for(BottleneckProcessUse use : pageList)
        {
            if(StringUtils.isNotBlank(use.getCraftId()))
            {
                if(StringUtils.isNotBlank(craftIds))
                {
                    craftIds = craftIds +","+ use.getCraftId();
                }
                else
                {
                    craftIds = use.getCraftId();
                }
            }
        }
        if(StringUtils.isNotBlank(craftIds))
        {
            List<EgProcess> processList = processDao.getListByCtaftId(bottleneckProcessUse.getCompany().getRecordId(),craftIds);
            if(Collections3.isNotEmpty(processList))
            {
                for(BottleneckProcessUse use : pageList)
                {
                    if(StringUtils.isBlank(use.getCraftId()))
                    {
                        continue;
                    }
                    for(EgProcess process : processList)
                    {
                        if(null == process || StringUtils.isBlank(process.getCraftId()))
                        {
                            continue;
                        }
                        if(use.getCraftId().contains(process.getCraftId()))
                        {
                            if(StringUtils.isNotBlank(use.getProcessId()))
                            {
                                use.setProcessId(use.getProcessId() +","+ process.getProcessIds());
                            }
                            else
                            {
                                use.setProcessId( process.getProcessIds());
                            }
                        }
                    }
                }
            }
        }
        String processIds = null;
        String batchDetailIds = null;
        for(BottleneckProcessUse use : pageList)
        {
            if(StringUtils.isNotBlank(use.getProcessId()))
            {
                if(StringUtils.isNotBlank(processIds))
                {
                    processIds = processIds +","+ use.getProcessId();
                }
                else
                {
                    processIds = use.getProcessId();
                }
            }
            if(StringUtils.isNotBlank(batchDetailIds))
            {
                batchDetailIds = batchDetailIds +","+ use.getBatchDetailId();
            }
            else
            {
                batchDetailIds = use.getBatchDetailId();
            }
        }
        if(StringUtils.isNotBlank(processIds) && StringUtils.isNotBlank(batchDetailIds))
        {
            List<ProduceRecord> recordList = produceRecordDao.getListByDetailProcess(bottleneckProcessUse.getCompany().getRecordId(),batchDetailIds,processIds);
            if(Collections3.isNotEmpty(recordList))
            {
                for(BottleneckProcessUse use : pageList)
                {
                    for(ProduceRecord record : recordList)
                    {
                        if(use.getBatchDetailId().equals(record.getProduceBatchDetailId()) && use.getProcessId().contains(record.getProcessId()))
                        {
                            use.setHandOverTime(record.getHandOverTime());
                            break;
                        }
                    }
                }
            }
        }
        page.setList(pageList);
        return page;
    }

    public Page<ContractDetail> getReportSpareMaterialsPage(Page<ContractDetail> page, ContractDetail contractDetail)
    {
        contractDetail.setPage(page);
        List<ContractDetail> detailList = contractDetailDao.getReportSpareMaterialsData(contractDetail);
        List<ContractDetail> detailListTwo = new ArrayList<>();
        if(Collections3.isEmpty(detailList))
        {
            return page;
        }
        String detailIds = null;
        for (ContractDetail cd : detailList)
        {
            if (StringUtils.isNotBlank(detailIds))
            {
                detailIds = detailIds + "," + cd.getRecordId();
            }
            else
            {
                detailIds = cd.getRecordId();
            }
            // 组合工艺值
            String concatCraftValue = null;
            if(null != cd.getCraft())
            {
                ContractCraft craft = cd.getCraft();
                // PCB类型
                if(StringUtils.isNotBlank(craft.getBoardLevelValue()))
                {
                    concatCraftValue = craft.getBoardLevelValue();
                }
                // 覆铜板材
                if(StringUtils.isNotBlank(craft.getMaterialTypeValue()))
                {
                    concatCraftValue = StringUtils.isNoneBlank(concatCraftValue) ? concatCraftValue +" "+ craft.getMaterialTypeValue() : craft.getMaterialTypeValue();
                }
                // 板材厚度
                if(StringUtils.isNotBlank(craft.getBoardThicknessValue()))
                {
                    concatCraftValue = StringUtils.isNoneBlank(concatCraftValue) ? concatCraftValue +" "+ craft.getBoardThicknessValue() : craft.getBoardThicknessValue();
                }
                // 覆铜要求
                if(StringUtils.isNotBlank(craft.getCopperCladThicknessValue()))
                {
                    concatCraftValue = StringUtils.isNoneBlank(concatCraftValue) ? concatCraftValue +" "+ craft.getCopperCladThicknessValue() : craft.getCopperCladThicknessValue();
                }
                // 镀层处理
                if(StringUtils.isNotBlank(craft.getSurfaceProcessValue()))
                {
                    concatCraftValue = StringUtils.isNoneBlank(concatCraftValue) ? concatCraftValue +" "+ craft.getSurfaceProcessValue() : craft.getSurfaceProcessValue();
                }
                // 阻焊类型
                if(StringUtils.isNotBlank(craft.getSolderMaskTypeValue()))
                {
                    concatCraftValue = StringUtils.isNoneBlank(concatCraftValue) ? concatCraftValue +" "+ craft.getSolderMaskTypeValue() : craft.getSolderMaskTypeValue();
                }
                // 板面字符
                if(StringUtils.isNotBlank(craft.getCharacterTypeValue()))
                {
                    concatCraftValue = StringUtils.isNoneBlank(concatCraftValue) ? concatCraftValue +" "+ craft.getCharacterTypeValue() : craft.getCharacterTypeValue();
                }
                // 成型方式
                if(StringUtils.isNotBlank(craft.getShapingWayValue()))
                {
                    concatCraftValue = StringUtils.isNoneBlank(concatCraftValue) ? concatCraftValue +" "+ craft.getShapingWayValue() : craft.getShapingWayValue();
                }
                // 测试要求
                if(StringUtils.isNotBlank(craft.getTestMethodValue()))
                {
                    concatCraftValue = StringUtils.isNoneBlank(concatCraftValue) ? concatCraftValue +" "+ craft.getTestMethodValue() : craft.getTestMethodValue();
                }
                // 导热
                if(StringUtils.isNotBlank(craft.getDaoreValue()))
                {
                    concatCraftValue = StringUtils.isNoneBlank(concatCraftValue) ? concatCraftValue +" "+ craft.getDaoreValue() : craft.getDaoreValue();
                }
                // 耐压
                if(StringUtils.isNotBlank(craft.getNaiyaValue()))
                {
                    concatCraftValue = StringUtils.isNoneBlank(concatCraftValue) ? concatCraftValue +" "+ craft.getNaiyaValue() : craft.getNaiyaValue();
                }
            }
            cd.setConcatCraftValue(concatCraftValue);
        }
        if(StringUtils.isNotBlank(detailIds))
        {
            List<RawmaterialStock> rawList = rawmaterialStockDao.getListByContractDetailIds(detailIds);
            if(Collections3.isNotEmpty(rawList))
            {
                for(ContractDetail cd : detailList)
                {
                    cd.setRawList(Lists.newArrayList());
                    List<String> storeHouseMatValueList = Lists.newArrayList();
                    for(RawmaterialStock raw : rawList)
                    {
                        if(cd.getRecordId().equals(raw.getContractDetailId()))
                        {
                            // 工程指定物料
                            if(StringUtils.isBlank(cd.getCardMatValue()))
                            {
                                cd.setCardMatValue(raw.getMatConcatValue());
                            }
                            Boolean flag = true;
                            if(Collections3.isNotEmpty(storeHouseMatValueList))
                            {
                                for(String value : storeHouseMatValueList)
                                {
                                    if(value.equals(raw.getMatConcatValueTwo()))
                                    {
                                        flag = false;
                                        break;
                                    }
                                }
                            }
                            if(flag)
                            {
                                storeHouseMatValueList.add(raw.getMatConcatValueTwo());
                            }
                            // 投料出库时间
                            if(null == cd.getAcDistributeDate())
                            {
                                cd.setAcDistributeDate(raw.getOperateDate());
                            }
                            cd.getRawList().add(raw);
                        }
                    }
                    String storeHouseMatValue = null;
                    for(String value : storeHouseMatValueList)
                    {
                        if(StringUtils.isNotBlank(storeHouseMatValue))
                        {
                            storeHouseMatValue = storeHouseMatValue +","+ value;
                        }
                        else
                        {
                            storeHouseMatValue = value;
                        }
                    }
                    cd.setStoreHouseMatValue(storeHouseMatValue);
                }
            }
        }
        for(ContractDetail contract:detailList){
            if (StringUtils.isNotBlank(contractDetail.getConcatCraftValue())){
                if (StringUtils.isBlank(contract.getConcatCraftValue())){
                    continue;
                }else{
                    if(!contract.getConcatCraftValue().contains(contractDetail.getConcatCraftValue())){
                        continue;
                    }
                }
            }
            detailListTwo.add(contract);
        }
        page.setList(detailListTwo);
        return page;
    }
    public Page<BatchDetailSetup> getStageFormPage(Page<BatchDetailSetup> page, BatchDetailSetup batchDetailSetup)
    {
        batchDetailSetup.setPage(page);
        List<BatchDetailSetup> pageList = batchDetailSetupDao.getStageFormPage(batchDetailSetup);
        if(Collections3.isEmpty(pageList))
        {
            return page;
        }
        page.setList(pageList);
        return page;
    }

    public Page<Purchasing> getSupplychainScheduleTracePage(Page<Purchasing> page, Purchasing purchasing)
    {
        purchasing.setPage(page);
        List<Purchasing> pageList = purchasingDao.getSupplychainScheduleTracePage(purchasing);
        if (Collections3.isEmpty(pageList))
        {
            return page;
        }
        if (Collections3.isNotEmpty(pageList))
        {
            // 订单进度
            for (Purchasing pur : pageList)
            {
                if (StringUtils.isBlank(pur.getSourcePurchaseOrdersId()))
                {
                    pur.setOrderSchedule("暂未进行采购");
                    continue;
                }
                else
                {
                    List<Purchasing> orderScheduleList = purchasingDao.getOrderScheduleList(pur);
                    if (Collections3.isNotEmpty(orderScheduleList))
                    {
                        boolean flag = false;
                        for (Purchasing orderSchedule : orderScheduleList)
                        {
                            if (null != orderSchedule.getStatus() && null != orderSchedule.getSupplyChain().getName() && !orderSchedule.getCompanyId().equals(purchasing.getCompany().getRecordId()))
                            {
                                if (orderSchedule.getStatus().equals("60003"))
                                {
                                    orderSchedule.setOrderSchedule("暂未进行采购!");
                                    continue;
                                }
                                else
                                {
                                    if (orderSchedule.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED))
                                    {
                                        orderSchedule.setStatus(TypeKey.PU_PURCHASING_STATUS_CONFIRMED);
                                    }
                                    String orderScheduleVale = orderSchedule.getSupplyChain().getName() + "   " + orderSchedule.getStatusStr();
                                    pur.setOrderSchedule(orderScheduleVale);
                                    flag = true;
                                }
                            }
                        }
                        if (!flag)
                        {
                            pur.setOrderSchedule("暂未进行采购");
                        }
                    }
                    // 送货进度
                    MaterialContract contract = new MaterialContract();
                    contract.setSourcePurchaseOrdersId(pur.getSourcePurchaseOrdersId());
                    List<MaterialContract> materialContractList = materialContractDao.getDeliverGoodsList(contract);
                    if (Collections3.isNotEmpty(materialContractList))
                    {
                        boolean flag = false;
                        for (MaterialContract materialContract : materialContractList)
                        {
                            if (null != pur.getQuantity() && pur.getQuantity().compareTo(BigDecimal.ZERO) != 0)
                            {
                                if (null != materialContract && null != materialContract.getDelivery()
                                    && null != materialContract.getDelivery().getDeliveryDetail())
                                {
                                    if (pur.getQuantity().intValue() > materialContract.getDelivery().getDeliveryDetail().getQuantity())
                                    {
                                        String deliverGoodsStatus = materialContract.getCompany().getName() + "   " + "未送货";
                                        break;
                                    }
                                    else
                                    {
                                        String deliverGoodsStatus = materialContract.getCompany().getName() + "   " + "已送货";
                                        pur.setDeliverySchedule(deliverGoodsStatus);
                                        flag = true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        page.setList(pageList);
        return page;
    }

    public void setExportValue(ExportExcel excel, List<Object> list, String[] hearList,String num)
    {
        if(Collections3.isEmpty(list) || StringUtils.isBlank(num))
        {
            return;
        }
        switch (num)
        {
            // 订单交期稽核表
            case "1":
                List<OrderDeliveryForm> listOne = (List<OrderDeliveryForm>) list.get(0);
                for (OrderDeliveryForm df : listOne)
                {
                    int colunm = 0;
                    Row row = excel.addRow();
                    for (String name : hearList)
                    {
                        String val = "";
                        switch (name)
                        {
                            case "合同号":
                                val = df.getFinalContractNo();
                                break;
                            case "客户订单号":
                                val = df.getCustomerPo();
                                break;
                            case "生产编号":
                                val = df.getCraftNo();
                                break;
                            case "下单时间":
                                val = df.getOrderDateStr();
                                break;
                            case "客户要求交期":
                                val = df.getDeliveryDateStr();
                                break;
                            case "系统交期":
                                val = df.getEstimateDateStr();
                                break;
                            case "出货日期":
                                val = df.getShippingDateStr();
                                break;
                            case "交货面积":
                                val = null == df.getShippingArea() ? "0" : df.getShippingArea().toString();
                                break;
                            case "下单部门":
                                val = df.getDeptName();
                                break;
                        }
                        excel.addCell(row, colunm++, val, 2, null);
                    }
                }
                break;
            // 报备用料稽查表
            case "2":
                List<MatPreparation> listTwo = (List<MatPreparation>) list.get(0);
                for (MatPreparation df : listTwo)
                {
                    int colunm = 0;
                    Row row = excel.addRow();
                    for (String name : hearList)
                    {
                        String val = "";
                        switch (name)
                        {
                            case "部门":
                                val = df.getDeptName();
                                break;
                            case "规格型号":
                                val = df.getMaterialSpecification();
                                break;
                            case "报备张数":
                                val = null == df.getQuantity() ? "0" : df.getQuantity().toString();
                                break;
                            case "报备面积":
                                val = null == df.getArea() ? "0" : df.getArea().toString();
                                break;
                            case "使用面积":
                                val = null == df.getUseArea() ? "0" : df.getUseArea().toString();
                                break;
                            case "剩余面积":
                                val = null == df.getRemainingArea() ? "0" : df.getRemainingArea().toString();
                                break;
                            case "备料时间":
                                val = df.getConfirmTimeStr();
                                break;
                            case "备料申请人":
                                String createdByName = null;
                                if(null != df.getCreatedBy())
                                {
                                    createdByName = df.getCreatedBy().getUserName();
                                }
                                val = createdByName;
                                break;
                        }
                        excel.addCell(row, colunm++, val, 2, null);
                    }
                }
                break;
            // 瓶颈工序完成考核表
            case "3":
                List<BottleneckProcessUse> listThree = (List<BottleneckProcessUse>) list.get(0);
                for (BottleneckProcessUse df : listThree)
                {
                    int colunm = 0;
                    Row row = excel.addRow();
                    for (String name : hearList)
                    {
                        String val = "";
                        switch (name)
                        {
                            case "工序名称":
                                val = df.getProcessName();
                                break;
                            case "通知单号":
                                val = df.getNotificationNo();
                                break;
                            case "生产编号":
                                val = df.getCraftNo();
                                break;
                            case "生产批次":
                                val = df.getProductionBatch();
                                break;
                            case "过数时间":
                                val = df.getHandOverTimeStr();
                                break;
                            case "工序负责人":
                                val = df.getName();
                                break;
                            case "预计完成时间":
                                val = df.getOccurrenceDateStr();
                                break;
                        }
                        excel.addCell(row, colunm++, val, 2, null);
                    }
                }
                break;
            // 订单材料使用表
            case "4":
                List<ContractDetail> listFour = (List<ContractDetail>) list.get(0);
                for (ContractDetail df : listFour)
                {
                    int colunm = 0;
                    Row row = excel.addRow();
                    for (String name : hearList)
                    {
                        String val = "";
                        switch (name)
                        {
                            case "下单部门":
                                String deptName = null;
                                if(null != df.getContract())
                                {
                                    deptName = df.getContract().getDeptName();
                                }
                                val = deptName;
                                break;
                            case "合同号":
                                String contractNo = null;
                                if(null != df.getContract())
                                {
                                    contractNo = df.getContract().getNo();
                                }
                                val = contractNo;
                                break;
                            case "生产编号":
                                String craftNo = null;
                                if(null != df.getCraft())
                                {
                                    craftNo = df.getCraft().getNo();
                                }
                                val = craftNo;
                                break;
                            case "合同工艺规格":
                                val = df.getConcatCraftValue();
                                break;
                            case "工程指定用料":
                                val = df.getCardMatValue();
                                break;
                            case "仓库出库物料":
                                val = df.getStoreHouseMatValue();
                                break;
                            case "投料时间":
                                val = null == df.getAcDistributeDate() ? null : DateUtils.formatDate(df.getAcDistributeDate());
                                break;
                        }
                        excel.addCell(row, colunm++, val, 2, null);
                    }
                }
                break;
            // 准备阶段考核表
            case "5":
                List<BatchDetailSetup> listFive = (List<BatchDetailSetup>) list.get(0);
                for (BatchDetailSetup df : listFive)
                {
                    int colunm = 0;
                    Row row = excel.addRow();
                    for (String name : hearList)
                    {
                        String val = "";
                        switch (name)
                        {
                            case "事项名称":
                                val = df.getName();
                                break;
                            case "负责人":
                                val = df.getPrincipalName();
                                break;
                            case "确认时间":
                                val = df.getConfirmDateStr();
                                break;
                            case "预计确认时间":
                                val = null == df.getConfirmationA() ? null : DateUtils.formatDate(df.getConfirmationA());
                                break;
                            case "生产编号":
                                val = df.getCraftNo();
                                break;
                            case "生产批次":
                                val = df.getBatchNo();
                                break;
                            case "通知单号":
                                val = df.getNotiNo();
                                break;
                            case "状态":
                                val = df.getStatusStr();
                                break;
                        }
                        excel.addCell(row, colunm++, val, 2, null);
                    }
                }
                break;
            //供应链进度追踪表
            case "6":
                List<Purchasing> listSix = (List<Purchasing>) list.get(0);
                for (Purchasing purchasing : listSix)
                {
                    int colunm = 0;
                    Row row = excel.addRow();
                    for (String name : hearList)
                    {
                        String val = "";
                        switch (name)
                        {
                            case "采购单号":
                                val = purchasing.getNo();
                                break;
                            case "采购时间":
                                val = null == purchasing.getOrderDate() ? null : DateUtils.formatDate(purchasing.getOrderDate());
                                break;
                            case "物料编号":
                                val = purchasing.getMaterialNo();
                                break;
                            case "物料名称":
                                val = purchasing.getMaterialName();
                                break;
                            case "采购数量":
                                val = null == purchasing.getQuantity() ? "0" : purchasing.getQuantity().toString();
                                break;
                            case "订单进度":
                                val = null;
                                break;
                            case "送货进度":
                                val = null;
                                break;
                            case "对账进度":
                                val = null;
                                break;
                            case "付款进度":
                                val = null;
                                break;
                        }
                        excel.addCell(row, colunm++, val, 2, null);
                    }
                }
        }
    }
    public Map<String,Object>getMarketShipment(CapacityDeail capacityDeail){
        Map<String, Object> data = new HashMap<>();
        String specifiedMonth = capacityDeail.getYearMonth();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        YearMonth specifiedYearMonthObj = YearMonth.parse(specifiedMonth, formatter);
        YearMonth lastMonthYearMonth = specifiedYearMonthObj.minusMonths(1);
        YearMonth nextMonthYearMonth = specifiedYearMonthObj.plusMonths(1);
        String lastMonth = lastMonthYearMonth.format(formatter);
        String currentMonth = specifiedYearMonthObj.format(formatter);
        String nextMonth = nextMonthYearMonth.format(formatter);
        capacityDeail.setCurrentMonth(currentMonth);
        capacityDeail.setNextMonth(nextMonth);
        capacityDeail.setLastMonth(lastMonth);

        List<CapacityDeail> capacityDeailsList = capacityDao.getMarketShipment(capacityDeail);

        if (Collections3.isNotEmpty(capacityDeailsList))
        {
            //获取实际送货面积和款数
            List<CapacityDeail> actualDeliveryList = capacityDao.actualDeliveryDate(capacityDeail);

            //获取当期送货面积和款数
            List<CapacityDeail> currentDeliveryList = capacityDao.currentDeliveryDate(capacityDeail);

            //获取集控使用款数、面积
            List<CapacityDeail> icloudCoutAreaList = capacityDao.getIcloudCoutArea(capacityDeail);

            //获取上月款数、面积
            List<CapacityDeail> beforCoutAreaList = capacityDao.getBeforCoutArea(capacityDeail);

            //获取当月款数、面积
            List<CapacityDeail> currentCountAreaList = capacityDao.getcurrentCountArea(capacityDeail);

            for(CapacityDeail capacityT : capacityDeailsList)
            {
                //实际送货
                for(CapacityDeail actualDelivery : actualDeliveryList)
                {
                    if (StringUtils.isNotBlank(capacityT.getProcessValueId()) && StringUtils.isNotBlank(capacityT.getDepartId())
                            &&StringUtils.isNotBlank(actualDelivery.getProcessValueId()) && StringUtils.isNotBlank(actualDelivery.getDepartId())
                            && capacityT.getProcessValueId().equals(actualDelivery.getProcessValueId()) && capacityT.getDepartId().equals(actualDelivery.getDepartId()))
                    {
                        capacityT.setActualArea(actualDelivery.getActualArea());
                        capacityT.setActualCount(actualDelivery.getActualCount());
                    }
                }
                //当前送货
                for(CapacityDeail currentDelivery : currentDeliveryList)
                {
                    if (StringUtils.isNotBlank(capacityT.getProcessValueId()) && StringUtils.isNotBlank(capacityT.getDepartId())
                            &&StringUtils.isNotBlank(currentDelivery.getProcessValueId()) && StringUtils.isNotBlank(currentDelivery.getDepartId())
                            && capacityT.getProcessValueId().equals(currentDelivery.getProcessValueId()) && capacityT.getDepartId().equals(currentDelivery.getDepartId()))
                    {
                        capacityT.setCurrentArea(currentDelivery.getCurrentArea());
                        capacityT.setCurrentCount(currentDelivery.getCurrentCount());
                    }
                }
                //集控使用
                for(CapacityDeail icloudCoutArea : icloudCoutAreaList)
                {
                    if (StringUtils.isNotBlank(capacityT.getProcessValueId()) && StringUtils.isNotBlank(capacityT.getDepartId())
                    &&StringUtils.isNotBlank(icloudCoutArea.getProcessValueId()) && StringUtils.isNotBlank(icloudCoutArea.getDepartId())
                    && capacityT.getProcessValueId().equals(icloudCoutArea.getProcessValueId()) && capacityT.getDepartId().equals(icloudCoutArea.getDepartId()))
                    {
                        capacityT.setIcloudUseArea(icloudCoutArea.getIcloudUseArea());
                        capacityT.setIcloudUseCount(icloudCoutArea.getIcloudUseCount());
                    }
                }
                //上月款数、面积赋值
                for(CapacityDeail beforCoutArea : beforCoutAreaList)
                {
                    if (StringUtils.isNotBlank(capacityT.getProcessValueId()) && StringUtils.isNotBlank(capacityT.getDepartId())
                            &&StringUtils.isNotBlank(beforCoutArea.getProcessValueId()) && StringUtils.isNotBlank(beforCoutArea.getDepartId())
                            && capacityT.getProcessValueId().equals(beforCoutArea.getProcessValueId()) && capacityT.getDepartId().equals(beforCoutArea.getDepartId()))
                    {
                        capacityT.setBeforUseArea(beforCoutArea.getBeforUseArea());
                        capacityT.setBeforUseCount(beforCoutArea.getBeforUseCount());
                    }
                }
                //当月款数、面积赋值
                for(CapacityDeail currentCountArea : currentCountAreaList)
                {
                    if (StringUtils.isNotBlank(capacityT.getProcessValueId()) && StringUtils.isNotBlank(capacityT.getDepartId())
                            &&StringUtils.isNotBlank(currentCountArea.getProcessValueId()) && StringUtils.isNotBlank(currentCountArea.getDepartId())
                            && capacityT.getProcessValueId().equals(currentCountArea.getProcessValueId()) && capacityT.getDepartId().equals(currentCountArea.getDepartId()))
                    {
                        capacityT.setCurrentUseArea(currentCountArea.getCurrentUseArea());
                        capacityT.setCurrentUseCount(currentCountArea.getCurrentUseCount());
                    }
                }
            }
        }
        data.put("capacityDeailsList",capacityDeailsList);
        return data;
    }

    public Map<String,Object> getMarketShipmentDetail(CapacityDeail capacityDeail){
        Map<String, Object> data = new HashMap<>();
        List<CapacityDeail> capacityDeailList = capacityDao.getMarketShipmentDetail(capacityDeail);
        data.put("capacityDeailList",capacityDeailList);
        return data;
    }

    public List<ContractDetail> getCapacityDetail(CapacityDeail capacityDeail)
    {
        //集控使用面积
        List<ContractDetail> capacityDeailsList = new ArrayList<>();
        //获取上月、当月 日期格式
        String specifiedMonth = capacityDeail.getYearMonth();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        YearMonth specifiedYearMonthObj = YearMonth.parse(specifiedMonth, formatter);
        YearMonth lastMonthYearMonth = specifiedYearMonthObj.minusMonths(1);
        String lastMonth = lastMonthYearMonth.format(formatter);
        String currentMonth = specifiedYearMonthObj.format(formatter);
        capacityDeail.setLastMonth(lastMonth);
        capacityDeail.setCurrentMonth(currentMonth);

        if (capacityDeail.getNumber().equals("1"))
        {
            capacityDeailsList = contractDetailDao.getIcloudUseDetail(capacityDeail);
        }
        else if (capacityDeail.getNumber().equals("2"))
        {
            capacityDeailsList = contractDetailDao.getIcloudCurrentDetail(capacityDeail);
        }
        else if (capacityDeail.getNumber().equals("3"))
        {
            capacityDeailsList = contractDetailDao.getIcloudBeforDetail(capacityDeail);
        }
        else if (capacityDeail.getNumber().equals("7"))
        {
            capacityDeailsList = contractDetailDao.getActualDeliveryDetail(capacityDeail);
        }
        else if (capacityDeail.getNumber().equals("8"))
        {
            capacityDeailsList = contractDetailDao.getCurrentDeliveryDetail(capacityDeail);
        }
        capacityDeailsList = setOpenData(capacityDeail.getNumber(),capacityDeailsList);

        String contractDetailIdsT = null;
        for(ContractDetail capacity: capacityDeailsList)
        {
            for(ContractDetail detail : capacity.getConcatList())
            {
                if (StringUtils.isNotBlank(contractDetailIdsT))
                {
                    contractDetailIdsT = contractDetailIdsT + "," + detail.getRecordId();
                }else{
                    contractDetailIdsT = detail.getRecordId();
                }
            }
        }
        List<DeliveryDetail> deliveryDetailList = deliveryDetailDao.getDeliveryDetailList(contractDetailIdsT);
        if (Collections3.isNotEmpty(capacityDeailsList))
        {
            for(ContractDetail cap : capacityDeailsList)
            {
                for(ContractDetail capT : cap.getConcatList())
                {
                    for(DeliveryDetail deliveryDetail : deliveryDetailList)
                    {
                        if (capT.getRecordId().equals(deliveryDetail.getContractDetailId()))
                        {
                            capT.setFirstDate(deliveryDetail.getFirstDate());
                            capT.setCurrentDate(deliveryDetail.getCurrentDate());
                            capT.setSumQuotation(deliveryDetail.getSumQuotation());
                            capT.setDeliveryArea(deliveryDetail.getDeliveryArea());
                            capT.setQuantity(String.valueOf(deliveryDetail.getQuantity()));
                        }
                    }
                }
            }
        }
        if (capacityDeail.getNumber().equals("7") || capacityDeail.getNumber().equals("8"))
        {
            //获取产能占用数据
          List<CapacityDeail> capacityDetailList = capacityDao.getCapacityDetailList(contractDetailIdsT);
            if (Collections3.isNotEmpty(capacityDeailsList))
            {
                for(ContractDetail capacity : capacityDeailsList)
                {
                    for(ContractDetail capTwo : capacity.getConcatList())
                    {
                        capTwo.setConcatList(Lists.newArrayList());
                        for(CapacityDeail capacityDeai : capacityDetailList)
                        {
                            if (capTwo.getRecordId().equals(capacityDeai.getContactDeailId()))
                            {
                                ContractDetail contractDetail = new ContractDetail();
                                contractDetail.setProTime(capacityDeai.getProTimeT());
                                contractDetail.setDeailArea(String.valueOf(capacityDeai.getDeailArea()));
                                capTwo.getConcatList().add(contractDetail);
                            }
                        }
                    }
                }
            }
        }
        return capacityDeailsList;
    }


    public List<ContractDetail> setOpenData(String number,List<ContractDetail> capacityDeailsList)
    {
        if(StringUtils.isBlank(number) || Collections3.isEmpty(capacityDeailsList))
        {
            return capacityDeailsList;
        }
//        if(!("1".equals(number) || "2".equals(number) || "3".equals(number)))
//        {
//            return capacityDeailsList;
//        }
        Map<String,String> map = new HashMap<>();
        for(ContractDetail detail : capacityDeailsList)
        {
            String key = detail.getContractNo();
            if(map.containsKey(key))
            {
                continue;
            }
            map.put(key,key);
        }
        List<ContractDetail> list = Lists.newArrayList();
        if(null != map)
        {
            for (Map.Entry<String, String> entry : map.entrySet())
            {
                if (entry != null && entry.getValue() != null)
                {
                    for(ContractDetail detail : capacityDeailsList)
                    {
                        if(entry.getValue().equals(detail.getContractNo()))
                        {
                            ContractDetail data = detail.clone();
                            list.add(data);
                            break;
                        }
                    }
                }
            }
        }
        if(Collections3.isNotEmpty(list))
        {
            int a = 1;
            for(ContractDetail contract : list)
            {
//                System.out.println("合同:"+a);
                contract.setConcatList(Lists.newArrayList());
                int b = 1;
                for(ContractDetail detail : capacityDeailsList)
                {
                    if(contract.getContractNo().equals(detail.getContractNo()))
                    {
                        Boolean flag = false;
                        if(Collections3.isNotEmpty(contract.getConcatList()))
                        {
                            for(ContractDetail obj : contract.getConcatList())
                            {
                                if (StringUtils.isNotBlank(obj.getRecordId()) && StringUtils.isNotBlank(detail.getRecordId()))
                                {
                                    if(obj.getRecordId().equals(detail.getRecordId()))
                                    {
                                        flag = true;
                                    }
                                }
                            }
                        }
                        if(flag)
                        {
                            continue;
                        }
                        ContractDetail data = detail.clone();
                        data.setConcatList(Lists.newArrayList());
                        int c = 1;
                        for(ContractDetail detail2 : capacityDeailsList)
                        {
                            if (StringUtils.isNotBlank(data.getRecordId()) && StringUtils.isNotBlank(detail2.getRecordId()))
                            {
                                if(data.getRecordId().equals(detail2.getRecordId()))
                                {
                                    ContractDetail data2 = detail2.clone();
//                                System.out.println("产能明细:"+c+'-'+data2.getCapacityDetailId());
                                    data.getConcatList().add(data2);
                                    c++;
                                }
                            }
                        }
//                        System.out.println("合同明细:"+b);
                        contract.getConcatList().add(data);
                        b++;
                    }
                }
                a++;
            }
        }
        return list;
    }

    public void getFirstBatchAverageLeadTime(List<DeliveryAssessmentDetail> sumList,Map<String,Object> data)
    {
        if(Collections3.isEmpty(sumList))
        {
            return;
        }
        BigDecimal newOrderAveDays = BigDecimal.ZERO; // 新单首批平均交期
        BigDecimal reorderAveDays = BigDecimal.ZERO; // 返单首批平均交期
        BigDecimal sampleAveDays = BigDecimal.ZERO; // 样品首批平均交期
        Integer newOrder = 0; // 新单款数
        Integer reorder = 0; // 返单款数
        Integer sample = 0; // 样品款数
        for(DeliveryAssessmentDetail da : sumList)
        {
            BigDecimal firstDays = StringUtils.isBlank(da.getFirstDays()) ? BigDecimal.ZERO : new BigDecimal(da.getFirstDays());
            if (null != da.getDictOrderType())
            {
                if (da.getDictOrderType().equals("1"))
                {
                    sample++;
                    sampleAveDays = sampleAveDays.add(firstDays);
                }
                if (da.getDictOrderType().equals("2") && null != da.getMaterialType() && da.getMaterialType().equals("1"))
                {
                    newOrder++;
                    newOrderAveDays = newOrderAveDays.add(firstDays);
                }
                if (da.getDictOrderType().equals("2") && null != da.getMaterialType() && da.getMaterialType().equals("2"))
                {
                    reorder++;
                    reorderAveDays = reorderAveDays.add(firstDays);
                }
            }
        }
        if (sampleAveDays.compareTo(BigDecimal.ZERO) != 0 && sampleAveDays.compareTo(BigDecimal.ZERO) > 0)
        {
            sampleAveDays = sampleAveDays.divide(BigDecimal.valueOf(sample), 1, RoundingMode.HALF_UP);
        }
        if (newOrderAveDays.compareTo(BigDecimal.ZERO) != 0 && newOrderAveDays.compareTo(BigDecimal.ZERO) > 0)
        {
            newOrderAveDays = newOrderAveDays.divide(BigDecimal.valueOf(newOrder), 1, RoundingMode.HALF_UP);
        }
        if (reorderAveDays.compareTo(BigDecimal.ZERO) != 0 && reorderAveDays.compareTo(BigDecimal.ZERO) > 0)
        {
            reorderAveDays = reorderAveDays.divide(BigDecimal.valueOf(reorder), 1, RoundingMode.HALF_UP);
        }
        data.put("sampleAveDays",sampleAveDays);
        data.put("newOrderAveDays",newOrderAveDays);
        data.put("reorderAveDays",reorderAveDays);
    }

    public void exportOrganizationSumList(ExportExcel excel, List<GroupOrgRelation> list, String[] hearList)
    {
        for (GroupOrgRelation relation : list)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "部门名称":
                        val = null == relation.getGroupOrgName() ? "" : relation.getGroupOrgName();
                        break;
                    case "本期未结算金额":
                        val = null == relation.getSumClosingAmount() ? "0" : String.valueOf(relation.getSumClosingAmount());
                        break;
                    case "本期剩余面积":
                        val = null == relation.getSumRemainingArea() ? "0" : String.valueOf(relation.getSumRemainingArea());
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    public void exportDeliveryAssessmentDetailList(ExportExcel excel, List<GroupOrgRelation> list, String[] hearList)
    {
        for (GroupOrgRelation relation : list)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "部门名称":
                        val = null == relation.getGroupOrgName() ? "" : relation.getGroupOrgName();
                        break;
                    case "订单面积(㎡)":
                        val = null == relation.getDeliveryArea() ? "0" : String.valueOf(relation.getDeliveryArea());
                        break;
                    case "订单款数":
                        val = null == relation.getDeliveryPayment() ? "0" : String.valueOf(relation.getDeliveryPayment());
                        break;
                    case "未送货款数":
                        val = null == relation.getNormalAmount() ? "0" : String.valueOf(relation.getNormalAmount());
                        break;
                    case "逾期款数":
                        val = null == relation.getOverduePayments() ? "0" : String.valueOf(relation.getOverduePayments());
                        break;
                    case "达标款数":
                        val = null == relation.getEligibleFunds() ? "0" : String.valueOf(relation.getEligibleFunds());
                        break;
                    case "交货达成率":
                        val = null == relation.getDeliveryAchievementRate() ? "0" : String.valueOf(relation.getDeliveryAchievementRate());
                        break;
                    case "奖惩金额":
                        val = null == relation.getRewardsPunishmentAmount() ? "0" : String.valueOf(relation.getRewardsPunishmentAmount());
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }
}
