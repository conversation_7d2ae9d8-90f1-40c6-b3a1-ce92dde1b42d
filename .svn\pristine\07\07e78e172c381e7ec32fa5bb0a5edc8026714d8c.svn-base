package com.kyb.pcberp.modules.hr.depart_center.pojo;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;
import com.kyb.pcberp.modules.wechat.entity.IcloudUser;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class Hr_DepartMent extends DataEntity<Hr_DepartMent>
{
    private static final long serialVersionUID = 1L;

    private String allId;

    private String departmentName;

    private String managerUserId;

    private String departmentLevel;

    private String companyAddress;

    private String phone;

    private String no;

    private String bindErpId;

    private String shortName;

    private List<Hr_DepartMent> departList;

    private String condition;

    private String workStatus;

    private List<Hr_Employee> empList;

    private String sumMan;

    private String groupId;

    private Map<String, String> hometownMap;

    private String expdate;

    private String day;

    private String oaCompanyId;

    private String oaCompanyName;

    private Hr_KybAttachments attachments;

    private IcloudUser icloudUser;

    private String groupManageId;

    private String defaultDb;

    private String manageName;

    private String name;

    private String path;

    private String oldId;

    private String bindErpName;

    private String schemaDescription;

    private List<Hr_DepartMent> departNextFloorList;

    private String architecture;

    private List<String> list;

    private BigDecimal scale;

    private String shareStatus;

    private String groupDepartId;

    private String status;

    public String getAllId()
    {
        return allId;
    }

    public void setAllId(String allId)
    {
        this.allId = allId;
    }

    public String getDepartmentName()
    {
        return departmentName;
    }

    public void setDepartmentName(String departmentName)
    {
        this.departmentName = departmentName;
    }

    public String getManagerUserId()
    {
        return managerUserId;
    }

    public void setManagerUserId(String managerUserId)
    {
        this.managerUserId = managerUserId;
    }

    public String getDepartmentLevel()
    {
        return departmentLevel;
    }

    public void setDepartmentLevel(String departmentLevel)
    {
        this.departmentLevel = departmentLevel;
    }

    public String getCompanyAddress()
    {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress)
    {
        this.companyAddress = companyAddress;
    }

    public String getPhone()
    {
        return phone;
    }

    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getNo()
    {
        return no;
    }

    public void setNo(String no)
    {
        this.no = no;
    }

    public String getBindErpId()
    {
        return bindErpId;
    }

    public void setBindErpId(String bindErpId)
    {
        this.bindErpId = bindErpId;
    }

    public String getShortName()
    {
        return shortName;
    }

    public void setShortName(String shortName)
    {
        this.shortName = shortName;
    }

    public List<Hr_DepartMent> getDepartList()
    {
        return departList;
    }

    public void setDepartList(List<Hr_DepartMent> departList)
    {
        this.departList = departList;
    }

    public String getCondition()
    {
        return condition;
    }

    public void setCondition(String condition)
    {
        this.condition = condition;
    }

    public String getWorkStatus()
    {
        return workStatus;
    }

    public void setWorkStatus(String workStatus)
    {
        this.workStatus = workStatus;
    }

    public List<Hr_Employee> getEmpList()
    {
        return empList;
    }

    public void setEmpList(List<Hr_Employee> empList)
    {
        this.empList = empList;
    }

    public String getSumMan()
    {
        return sumMan;
    }

    public void setSumMan(String sumMan)
    {
        this.sumMan = sumMan;
    }

    public String getGroupId()
    {
        return groupId;
    }

    public void setGroupId(String groupId)
    {
        this.groupId = groupId;
    }

    public Map<String, String> getHometownMap()
    {
        return hometownMap;
    }

    public void setHometownMap(Map<String, String> hometownMap)
    {
        this.hometownMap = hometownMap;
    }

    public String getExpdate()
    {
        return expdate;
    }

    public void setExpdate(String expdate)
    {
        this.expdate = expdate;
    }

    public String getDay()
    {
        return day;
    }

    public void setDay(String day)
    {
        this.day = day;
    }

    public String getOaCompanyId()
    {
        return oaCompanyId;
    }

    public void setOaCompanyId(String oaCompanyId)
    {
        this.oaCompanyId = oaCompanyId;
    }

    public String getOaCompanyName()
    {
        return oaCompanyName;
    }

    public void setOaCompanyName(String oaCompanyName)
    {
        this.oaCompanyName = oaCompanyName;
    }

    public Hr_KybAttachments getAttachments()
    {
        return attachments;
    }

    public void setAttachments(Hr_KybAttachments attachments)
    {
        this.attachments = attachments;
    }

    public IcloudUser getIcloudUser() {
        return icloudUser;
    }

    public void setIcloudUser(IcloudUser icloudUser) {
        this.icloudUser = icloudUser;
    }

    public String getGroupManageId() {
        return groupManageId;
    }

    public void setGroupManageId(String groupManageId) {
        this.groupManageId = groupManageId;
    }

    public String getDefaultDb() {
        return defaultDb;
    }

    public void setDefaultDb(String defaultDb) {
        this.defaultDb = defaultDb;
    }

    public String getManageName() {
        return manageName;
    }

    public void setManageName(String manageName) {
        this.manageName = manageName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getOldId()
    {
        return oldId;
    }

    public void setOldId(String oldId)
    {
        this.oldId = oldId;
    }

    public String getBindErpName() {
        return bindErpName;
    }

    public void setBindErpName(String bindErpName) {
        this.bindErpName = bindErpName;
    }

    public String getSchemaDescription()
    {
        return schemaDescription;
    }

    public void setSchemaDescription(String schemaDescription)
    {
        this.schemaDescription = schemaDescription;
    }

    public List<Hr_DepartMent> getDepartNextFloorList() {
        return departNextFloorList;
    }

    public void setDepartNextFloorList(List<Hr_DepartMent> departNextFloorList) {
        this.departNextFloorList = departNextFloorList;
    }

    public String getArchitecture() {
        return architecture;
    }

    public void setArchitecture(String architecture) {
        this.architecture = architecture;
    }

    public List<String> getList() {
        return list;
    }

    public void setList(List<String> list) {
        this.list = list;
    }

    public BigDecimal getScale() {
        return scale;
    }

    public void setScale(BigDecimal scale) {
        this.scale = scale;
    }

    public String getShareStatus() {
        return shareStatus;
    }

    public void setShareStatus(String shareStatus) {
        this.shareStatus = shareStatus;
    }

    public String getGroupDepartId() {
        return groupDepartId;
    }

    public void setGroupDepartId(String groupDepartId) {
        this.groupDepartId = groupDepartId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
