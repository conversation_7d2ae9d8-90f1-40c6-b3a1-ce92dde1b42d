<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.finance.dao.CollectMuchMoneyDao">

	<sql id="slCollectMuchMoneyColumns">
		a.recordid AS "recordId",
		a.companyid AS "company.recordId",
		a.customerid AS "customer.recordId",
		a.collectMoneyId AS "collectMoney.recordId",
		a.contractid AS "contract.recordId",
		a.collecteddate AS "collectedDate",
		a.amount AS "amount",
		a.billno AS "billNo",
		a.collectway AS "collectWay",
		a.receiveaccount AS "receiveAccount",
		a.bank AS "bank",
		a.fundtype AS "fundType",
		a.responsibleperson AS "responsiblePerson.recordId",
		a.sourceid AS "sourceId",
		a.writeoffid AS "writeOffId",
		a.writeoffcause AS "writeOffCause",
		a.status AS "status",
		a.activeflag AS "activeFlag",
		a.createdby AS "createdBy.recordId",
		a.createddate AS "createdDate",
		a.lastupdby AS "lastUpdBy.recordId",
		a.lastupddate AS "lastUpdDate",
		a.remark AS "remark",
		a.dateForWriteOffCollectMoney As "dateForWriteOffCollectMoney",
		a.changeFee,
		a.otherFee,
		a.singleReceivableId
	</sql>
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO sl_collect_much_money(
			companyId,
			customerId,
			collectMoneyId,
			contractId,
			period,
			collecteddate,
			amount,
			billno,
			collectway,
			receiveaccount,
			bank,
			fundtype,
			responsibleperson,
			sourceid,
			writeoffid,
			writeoffcause,
			status,
			activeflag,
			createdby,
			createddate,
			lastupdby,
			lastupddate,
			remark,
			dateForWriteOffCollectMoney,
			changeFee,
			otherFee,
			singleReceivableId
		) VALUES (
			#{company.recordId},
			#{customer.recordId},
			#{collectMoney.recordId},
			#{contract.recordId},
			#{period},
			#{collectedDate},
			#{amount},
			#{billNo},
			#{collectWay},
			#{receiveAccount},
			#{bank},
			#{fundType},
			#{responsiblePerson.recordId},
			#{sourceId},
			#{writeOffId},
			#{writeOffCause},
			#{status},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{dateForWriteOffCollectMoney},
			#{changeFee},
			#{otherFee},
			#{singleReceivableId}
		)
	</insert>
	
	<update id="update">
		UPDATE sl_collect_much_money SET 	
			customerId = #{customer.recordId},
			collectMoneyId = #{collectMoney.recordId},
			contractId = #{contract.recordId},
			period = #{period},
			collecteddate = #{collectedDate},
			amount = #{amount},
			billno = #{billNo},
			collectway = #{collectWay},
			receiveaccount = #{receiveAccount},
			bank = #{bank},
			fundtype = #{fundType},
			responsibleperson = #{responsiblePerson.recordId},
			sourceid = #{sourceId},
			writeoffid = #{writeOffId},
			writeoffcause = #{writeOffCause},
			status = #{status},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate},
			remark = #{remark},
			dateForWriteOffCollectMoney = #{dateForWriteOffCollectMoney},
			changeFee = #{changeFee},
			otherFee = #{otherFee},
			singleReceivableId = #{singleReceivableId}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="get" resultType="CollectMuchMoney">
		SELECT 
			<include refid="slCollectMuchMoneyColumns"/>
		FROM sl_collect_much_money a
		WHERE a.recordId = #{recordId}
	</select>
	
	<sql id="slCollectMuchMoneyJoins">
		LEFT JOIN md_customer mcu on mcu.recordId = a.customerid
		LEFT JOIN md_company mc on mc.recordId = a.companyId
		LEFT JOIN sm_user su  on su.recordId = a.responsiblePerson
	</sql>
	
	<!-- zjn 2019-03-01 获取分月收款记录列表 -->
	<select id="getCollectMuchMoneyList" resultType="CollectMuchMoney">
		SELECT 
			<include refid="slCollectMuchMoneyColumns"/>,
			mc.name as "company.name",
			mcu.no as "customer.no",
			mcu.name as "customer.name",
			mcu.shortName as "customer.shortName",
			mcu.salesman as "customer.salesman.recordId",
			us.userName AS "customer.salesman.userName",
			su.userName as  "responsiblePerson.userName",
			b.period
		FROM sl_collect_much_money a
		<include refid="slCollectMuchMoneyJoins"/>
		LEFT JOIN sm_user us on us.recordId = mcu.salesman
		LEFT JOIN sl_single_receivable b ON b.recordId = a.singleReceivableId
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
			AND a.collectMoneyId = #{recordId}
		</where>
	</select>

	<update id="delete">
		UPDATE sl_collect_much_money SET
			activeFlag = 2
		WHERE recordId = #{recordId}
	</update>


	<select id="getCollectMuchMoneyData" resultType="CollectMuchMoney">
		SELECT
			c.billNo,
			c.collectedDate,
			SUM(a.amount) AS "amount",
			d.userName,
			e.`value` AS "collectWayValue",
			c.period
		FROM sl_singledetail_collect a
		LEFT JOIN sl_single_receivable_detail b ON b.recordId = a.detailId
		LEFT JOIN sl_collect_much_money c ON c.recordId = a.collectId
		LEFT JOIN sm_user d ON d.recordId = IFNULL(c.responsiblePerson,c.createdBy)
		LEFT JOIN md_dict_value e ON e.recordId = c.collectWay
		WHERE b.recordId = #{recordId} AND c.activeFlag = 1
		GROUP BY c.recordId
	</select>

</mapper>