package com.kyb.pcberp.modules.oa.controller;

import com.alibaba.fastjson.JSON;
import com.kyb.pcberp.common.dataSource.TemporaryDataSource;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_KybAttachments;
import com.kyb.pcberp.modules.oa.pojo.dayThing.Oa_dayThing;
import com.kyb.pcberp.modules.oa.service.Oa_dayThingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping(value = "kybDayThing/")
public class Oa_dayThingController
{
    @Autowired
    private Oa_dayThingService oa_dayThingService;
    
    // 获取日程安排
    @RequestMapping(value = "getDayThingList", method = RequestMethod.POST)
    @ResponseBody
    public List<Oa_dayThing> getDayThingList(@RequestBody Oa_dayThing dayThing)
    {
        return oa_dayThingService.getDayThingList(dayThing);
    }
    
    // 添加日程安排
    @RequestMapping(value = "addDayThing", method = RequestMethod.POST)
    @ResponseBody
    public String addDayThing(@RequestParam("files") MultipartFile[] files, @RequestParam("dayThing") String dayThing)
    {
        Oa_dayThing kybDayThing = JSON.parseObject(dayThing, Oa_dayThing.class);
        return oa_dayThingService.addDayThing(files, kybDayThing);
    }
    
    // 删除日程安排
    @RequestMapping(value = "delDayThing", method = RequestMethod.POST)
    @ResponseBody
    public String delDayThing(@RequestBody Oa_dayThing dayThing)
    {
        return oa_dayThingService.delDayThing(dayThing);
    }
    
    // 获取日程安排详情
    @RequestMapping(value = "getDayThing", method = RequestMethod.POST)
    @ResponseBody
    public Oa_dayThing getDayThing(@RequestBody Oa_dayThing dayThing)
    {
        return oa_dayThingService.getDayThing(dayThing);
    }
    
    // 微信推送相关日程
    @RequestMapping(value = "weixinNotice", method = RequestMethod.POST)
    @ResponseBody
    public String weixinNotice(@RequestBody Oa_dayThing dayThing)
    {
        return oa_dayThingService.weixinNotice(dayThing);
    }
    
    // 删除附件
    @RequestMapping(value = "delFile", method = RequestMethod.POST)
    @ResponseBody
    public String delFile(@RequestBody Hr_KybAttachments attach)
    {
        return oa_dayThingService.delFile(attach);
    }

    // 导出日程列表
    @RequestMapping(value = "export", method = RequestMethod.POST)
    public String exportFile(Oa_dayThing dayThing, HttpServletRequest request, HttpServletResponse response)
    {
        if (StringUtils.isNotBlank(dayThing.getDefaultDb()))
        {
            TemporaryDataSource.getInstance().icloudDataSource(dayThing.getDefaultDb());
        }
        try
        {
            // 导出名字为配置类型的名字
            String fileName = "日程列表" + DateUtils.formatDateTime(new Date()) + ".xlsx";
            List<Oa_dayThing> daythingList = oa_dayThingService.getDayThingList(dayThing);
            if (null != daythingList && daythingList.size() > 0)
            {
                String[] headerList =
                        {"标题","日程内容","日程地址","日程时间","发布人", "参与人","定时微信提醒参与人","定时微信提醒发布人"};
                new ExportExcel("日程数据", headerList).setDataListDayThing(daythingList).write(response, fileName).dispose();
            }
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }
}
