<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.production.dao.ProduceRecordDailyDao">
    
	<sql id="produceRecordDailyColumns">
		a.recordId,
		a.companyId AS "company.recordId",
		a.processId AS "process.recordId",
		a.processDate AS "processDate",
		a.processQtyA AS "processQtyA",
		a.processQtyB AS "processQtyB",
		a.activeFlag AS "activeFlag",
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.remark AS "remark"
	</sql>
	
	<sql id="produceRecordDailyJoins">
	</sql>
    
    <!-- 查询本公司 本天  本工序 是否有数据 -->
    <select id="selectFristCount" resultType="ProduceRecordDaily"> 
    select 
    	<include refid="produceRecordDailyColumns"/>
     from pd_produce_record_daily a where a.companyId = #{company.recordId} and a.processId = #{process.recordId} and a.processDate like #{processDate}
    </select>
    
	<select id="get" resultType="ProduceRecordDaily">
		SELECT 
			<include refid="produceRecordDailyColumns"/>
		FROM pd_produce_record_daily a
		<include refid="produceRecordDailyJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="findList" resultType="ProduceRecordDaily">
		SELECT 
			<include refid="produceRecordDailyColumns"/>,
			b.name as "process.name"
		FROM pd_produce_record_daily a
		LEFT JOIN eg_process b on a.processId = b.recordId
		<where>
			 a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
			 <if test="process != null and process != ''">
					and b.name like
					<if test="dbName == 'mssql'">'%'+#{process.name}+'%'</if>
					<if test="dbName == 'mysql'">concat('%',#{process.name},'%')</if>
			</if>
				
			 <if test="processDate != null and processDate != ''">
				and a.processDate like
					<if test="dbName == 'mssql'">'%'+#{processDate}+'%'</if>
					<if test="dbName == 'mysql'">concat('%',#{processDate},'%')</if>
				</if>
				
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="ProduceRecordDaily">
		SELECT 
			<include refid="produceRecordDailyColumns"/>
		FROM pd_produce_record_daily a
		<include refid="produceRecordDailyJoins"/>
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<insert id="insert">
		INSERT INTO pd_produce_record_daily(
			companyId,
			processId,
			processDate,
			processQtyA,
			processQtyB,
			activeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			remark
		) VALUES (
			#{company.recordId},
			#{process.recordId},
			#{processDate},
			#{processQtyA},
			#{processQtyB},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark}
		)
	</insert>
	
	<update id="update">
		UPDATE pd_produce_record_daily SET 	
			companyId = #{company.recordId},
			processId = #{process.recordId},
			processDate = #{processDate},
			processQtyA = #{processQtyA},
			processQtyB = #{processQtyB},
			activeFlag = #{DEL_FLAG_NORMAL},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			remark = #{remark}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		DELETE FROM pd_produce_record_daily
		WHERE recordId = #{recordId}
	</update>
	
</mapper>