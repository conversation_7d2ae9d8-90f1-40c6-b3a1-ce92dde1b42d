package com.kyb.pcberp.modules.stock.service;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.dao.MaterialSpecificationDao;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.MaterialSpecification;
import com.kyb.pcberp.modules.sys.dao.DictItemDao;
import com.kyb.pcberp.modules.sys.dao.DictValueDao;
import com.kyb.pcberp.modules.sys.entity.DictItem;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional(readOnly = true)
public class MaterialSpecificationService extends CrudService<MaterialSpecificationDao, MaterialSpecification> {
    @Autowired
    private MaterialSpecificationDao materialSpecificationDao;

    @Autowired
    private MaterialDao materialDao;

    // @Autowired
    // private UserDao userDao;

    @Autowired
    private DictItemDao dictItemDao;

    @Autowired
    private DictValueDao dictValueDao;

    public Page<MaterialSpecification> findPage(Page<MaterialSpecification> page, MaterialSpecification material) {
        return super.findPage(page, material);
    }

    public List<MaterialSpecification> findAllList(MaterialSpecification spec) {
        List<MaterialSpecification> materialSpecifications = dao.findAllList(spec);
        if (materialSpecifications != null && materialSpecifications.size() > 0) {
            materialSpecifications = getValues(materialSpecifications);
        }
        return materialSpecifications;
    }

    /**
     * 检查物料规格名称是否重复可用
     */
    public String getCount(MaterialSpecification spec) {
        return materialSpecificationDao.getCount(spec);
    }

    /**
     * 保存物料规格
     */
    @Transactional(readOnly = false)
    public String saveMaterialSpec(MaterialSpecification materialSpecification) {
        String tips = "保存规格错误";
        if (materialSpecification.getIsNewRecord()) {
            tips = "添加物料规格成功。";
            materialSpecification.preInsert();
            dao.insert(materialSpecification);
        } else {
            tips = "修改物料规格成功。";
            materialSpecification.preUpdate();
            dao.update(materialSpecification);
        }
        return tips;
    }

    public List<MaterialSpecification> materialSpecFindPage(MaterialSpecification materialSpecification) {
        materialSpecification.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        List<MaterialSpecification> materialSpecificationList = dao.findList(materialSpecification);

        if (materialSpecificationList != null && materialSpecificationList.size() > 0) {
            for(MaterialSpecification mater:materialSpecificationList){
                String copper = null;
                String copperSheetIds = mater.getCopperSheetIds();
                List<DictValue> copperValue = dictValueDao.getCopperValue(copperSheetIds);
                for(DictValue dict : copperValue){
                    if (StringUtils.isNotBlank(copper)){
                        copper = copper+ ","+ dict.getValue();
                    }else{
                        copper = dict.getValue();
                    }
                }
                mater.setCopperSheetValue(copper);
            }
        }
        return materialSpecificationList;
    }

    /**
     * 所有的该企业的物料规格
     */
    public List<MaterialSpecification> selectMaterialSpecList(Material material) {
        return materialSpecificationDao.findListByMaterial(material);
    }

    /**
     * 物料规格用于导出List
     */
    public List<MaterialSpecification> findAllExportList(MaterialSpecification materialSpecification) {
        return materialSpecificationDao.findAllExportList(materialSpecification);
    }

    /**
     * ycy 2016-11-30 根据物料id查询规格属性值是否存在
     *
     * @param material
     * @return
     */
    @Transactional(readOnly = false)
    public void materialSpecificationDelete(Material material) {
        List<MaterialSpecification> list = materialSpecificationDao.getIdSpecificationRelation(material);
        if (Collections3.isNotEmpty(list)) {
            for (MaterialSpecification materialSpecification : list) {
                materialSpecificationDao.deleteSpec(materialSpecification);
            }
        }
    }

    /**
     * 检查有几个物料在用此规格
     */
    public Integer getUsedCount(MaterialSpecification spec) {
        return materialSpecificationDao.getUsedCount(spec);
    }

    /**
     * 删除物料规格
     */
    @Transactional(readOnly = false)
    public String deleteSpec(MaterialSpecification materialSpecification) {
        List<Material> materialList = materialDao.getMaterialListBySpec(UserUtils.getUser().getCompany().getRecordId(), materialSpecification.getRecordId());
        materialSpecificationDao.deleteSpec(materialSpecification);// 改变此规格为不可用状态
        materialSpecificationDao.deleteSpecRalation(materialSpecification);// 改变此规格为不可用状态
        // 更新涉及物料的规格
        materialList = dealAddSpec(materialList);
        for (Material material : materialList) {
            // 拼接物料规格
            dealMaterialSpe(material);
            reConcactNameStr(material);
            materialDao.updateSpecificationAndName(material);
        }
        if (materialList.size() > 0) {
            return "删除成功，已同步物料规格";
        }
        return "删除成功";
    }

    public void dealMaterialSpe(Material material) {
        if (material != null && material.getMaterialSpecList() != null && material.getMaterialSpecList().size() > 0) {
            for (MaterialSpecification fujian : material.getMaterialSpecList()) {
                if (fujian == null || StringUtils.isBlank(fujian.getValue())) {
                    continue;
                }
                // 选中了值并且设置了打开项
                String val = fujian.getValue();
                if (StringUtils.isNotBlank(fujian.getValDefinition()) && fujian.getValDefinition().equals("1")) {
                    if (StringUtils.isNotBlank(fujian.getMinValue()) && StringUtils.isNotBlank(fujian.getMaxValue())) {
                        val = "(" + fujian.getMinValue() + "≤值" + fujian.getMaxValue() + ")";
                    }
                } else if (StringUtils.isNotBlank(val) && fujian.getDictItem() != null && fujian.getDictItem().getValues() != null) {
                    if (StringUtils.isNotBlank(fujian.getValDefinition()) && fujian.getValDefinition().equals("2")) {
                        String[] valueList = val.split(",");
                        val = "(";
                        for (int i = 0; i < valueList.length; i++) {
                            for (DictValue dictValue : fujian.getDictItem().getValues()) {
                                if (dictValue.getRecordId().equals(valueList[i])) {
                                    val = val + " " + dictValue.getValue();
                                    break;
                                }
                            }
                        }
                        val = val + ")";
                    } else {
                        if (fujian != null && fujian.getDictItem() != null && fujian.getDictItem().getValues() != null) {
                            for (DictValue dictValue : fujian.getDictItem().getValues()) {
                                if (dictValue != null && dictValue.getRecordId() != null && dictValue.getRecordId().equals(val)) {
                                    val = dictValue.getValue();
                                    break;
                                }
                            }
                        }
                    }
                }
                fujian.setValStr(val);
            }
        }
    }

    public void reConcactNameStr(Material material) {
        String materialName = "";
        String specification = "";
        if (material != null && material.getMaterialSpecList() != null && material.getMaterialSpecList().size() > 0) {
            // 制造商
            String madeSupplierName = getValue("70", material);
            // 覆铜板材
            String board = getValue("2", material);
            // 组合物料名字,制造商+覆铜板材+追加项
            if (StringUtils.isNotBlank(madeSupplierName)) {
                materialName = madeSupplierName;
                if (StringUtils.isNotBlank(board)) {
                    materialName = materialName + " " + board;
                }
            } else {
                if (StringUtils.isNotBlank(board)) {
                    materialName = board;
                }
            }
            if (StringUtils.isNotBlank(material.getAdditionalItems())) {
                materialName = materialName + " " + material.getAdditionalItems();
            }
            // 板材厚度
            String thickness = getValue("3", material);
            // 导热
            String daore = getValue("58", material);
            // 耐压
            String naiya = getValue("59", material);
            // 覆铜要求
            String copperClad = getValue("31", material);
            String length = getValueStr("长", material);
            String width = getValueStr("宽", material);
            // 贴膜
            String film = getValue("71", material);
            // PCB类型
            String pcbType = getValue("1", material);
            String simple = getValueStr("样品", material);
            String gum = getValueStr("胶", material);
            String insulat = getValueStr("绝缘层", material);
            String cuprum = getValueStr("无铜", material);
            String special = getValueStr("特殊要求", material);
            // 组合规格型号，板材厚度+覆铜要求+长*宽+胶+膜+样品+无铜
            if (StringUtils.isNotBlank(thickness)) {
                String thicknessStr = thickness.replace("mm", "厚");
                specification = thicknessStr;
            }
            if (StringUtils.isNotBlank(copperClad)) {
                copperClad = copperClad.toLowerCase();
                String copperCladStr = "";
                switch (copperClad) {
                    case "2/0":
                        copperCladStr = "70um";
                        break;
                    case "1/0":
                        copperCladStr = "35um";
                        break;
                    case "h/0":
                        copperCladStr = "18um";
                        break;
                    case "3/0":
                        copperCladStr = "105um";
                        break;
                    default:
                        copperCladStr = copperClad;
                        break;
                }
                specification = concatSpec(specification, copperCladStr, false);
            }
            if (StringUtils.isNotBlank(material.getSpecification())) {
                specification = specification + " ";
            }
            specification = concatSpec(specification, length, false);
            specification = concatSpec(specification, "*", false);
            specification = concatSpec(specification, width, false);
            specification = concatSpec(specification, daore, true);
            specification = concatSpec(specification, naiya, true);
            specification = concatSpec(specification, film, true);
            specification = concatSpec(specification, pcbType, false);
            specification = concatSpec(specification, simple, false);
            specification = concatSpec(specification, gum, false);
            specification = concatSpec(specification, insulat, false);
            specification = concatSpec(specification, cuprum, false);
            specification = concatSpec(specification, special, false);
            // 其它组合
            for (MaterialSpecification fujian : material.getMaterialSpecList()) {
                String value = "";
                if (fujian != null && StringUtils.isBlank(fujian.getUseFlag())) {
                    value = fujian.getValStr();
                    if (StringUtils.isNotBlank(value)) {
                        specification = concatSpec(specification, value, true);
                    }
                }
            }
        }
        material.setName(materialName);
        material.setSpecification(specification);
    }

    public String getValueStr(String str, Material material) {
        for (MaterialSpecification fujian : material.getMaterialSpecList()) {
            if (fujian.getName().equals(str)) {
                fujian.setUseFlag("1");
                return fujian.getValStr();
            }
        }
        return "";
    }

    public String getValue(String id, Material material) {
        for (MaterialSpecification fujian : material.getMaterialSpecList()) {
            if (fujian.getDictItem() != null && fujian.getDictItem().getValues() != null) {
                if (fujian.getDictItem().getRecordId().equals(id)) {
                    fujian.setUseFlag("1");
                    return fujian.getValStr();
                }
            }
        }
        return "";
    }

    public String concatSpec(String specification, String value, boolean flag) {
        if (StringUtils.isNotBlank(value)) {
            return specification + (flag ? " " : "") + value;
        }
        return specification;
    }

    private List<Material> dealAddSpec(List<Material> materialList) {
        // 对原料page进行加工
        if (Collections3.isNotEmpty(materialList)) {
            for (Material material : materialList) {
                if (material != null && material.getMaterialType() != null
                        && StringUtils.isNotBlank(material.getMaterialType().getValue())) {
                    List<MaterialSpecification> list = materialSpecificationDao.findSpecsByMaterial(material);
                    if (list != null && list.size() > 0) {
                        list = getValues(list);
                    }
                    material.setMaterialSpecList(list);
                }
            }
        }
        return materialList;
    }

    @SuppressWarnings("deprecation")
    public List<DictItem> getDictionaries() {
        List<DictItem> items = Lists.newArrayList();
        List<DictItem> itemList = dictItemDao.findAllList();
        for (DictItem item : itemList) {
            items.add(item);
        }
        return items;
    }

    // TJ 获取物料规格字典项的选项
    public List<MaterialSpecification> getValues(List<MaterialSpecification> materialSpecifications) {
        for (MaterialSpecification materialSpecification : materialSpecifications) {
            if (materialSpecification.getDictItem() != null
                    && materialSpecification.getDictItem().getRecordId() != null) {
                for (CommonEnums.DictItemEnum s : CommonEnums.DictItemEnum.values()) {
                    if (s.getIndex().toString().equals(materialSpecification.getDictItem().getRecordId())) {
                        if (materialSpecification.getCompany() != null
                                && materialSpecification.getCompany().getRecordId() != null) {
                            materialSpecification.getDictItem()
                                    .setValues(DictUtils.getValuesByItem(s, materialSpecification.getCompany()));
                        } else {
                            materialSpecification.getDictItem().setValues(DictUtils.getValuesByItem(s));
                        }
                        List<DictValue> values = materialSpecification.getDictItem().getValues();
                        if (values != null && values.size() > 0) {
                            DictValue dictValue = DictUtils.getValuesByItem(materialSpecification.getValue());
                            if (!values.contains(dictValue)) {
                                values.add(dictValue);
                            }
                        }
                    }
                }
            }
        }
        return materialSpecifications;
    }

    // tj 2018-09-13 获取物料名称
    public Map<String, Object> getMaterialName(List<Material> list) {
        Map<String, Object> map = new HashMap<>();
        List<Material> mList = new ArrayList<>();
        String result = "success";

        if (Collections3.isNotEmpty(list)) {
            for (Material material : list) {
                Material ma = materialDao.get(material);
                if (null == ma.getAvailableQty()) {
                    ma.setAvailableQty(BigDecimal.ZERO);
                }
                // 判断库存数量与出库数量
                BigDecimal outQuantity = new BigDecimal(material.getQuantity());
                if (ma.getAvailableQty().compareTo(outQuantity) < 0) {
                    result = "您当前物料可用库存数量不足，请补全后再次进行操作；当前可用数量：" + ma.getAvailableQty().toString();
                    map.put("result", result);
                    return map;
                }
                mList.add(ma);
            }
        }

        map.put("result", result);
        map.put("mList", mList);
        return map;
    }
}
