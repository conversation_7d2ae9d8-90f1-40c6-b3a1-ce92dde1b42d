<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.report.dao.OverdueDeductionsDao">

    <insert id="batchInsert">
        INSERT INTO sl_single_overdue_deductions
        (
            companyId,
            userId,
            customerId,
            groupOrgId,
            royaltyRateId,
            period,
            overdueDate,
            amount,
            rateAmount,
            deductionAmount,
            deductionRate,
            grossProfit,
            grossProfitMonth,
            policy,
            royaltyRate,
            reconcilingAmount,
            prdorderMoney,
            saleFee,
            barrelBoltFee,
            manageFee,
            profits,
            status,
            activeFlag,
            createdDate
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.companyId},
            #{item.userId},
            #{item.customerId},
            #{item.groupOrgId},
            #{item.royaltyRateId},
            #{item.period},
            #{item.overdueDate},
            #{item.amount},
            #{item.rateAmount},
            #{item.deductionAmount},
            #{item.deductionRate},
            #{item.grossProfit},
            #{item.grossProfitMonth},
            #{item.policy},
            #{item.royaltyRate},
            #{item.reconcilingAmount},
            #{item.prdorderMoney},
            #{item.saleFee},
            #{item.barrelBoltFee},
            #{item.manageFee},
            #{item.profits},
            #{item.status},
            1,
            NOW()
        )
        </foreach>
    </insert>

    <select id="getList" resultType="OverdueDeductions">
        SELECT
            b.shortName AS "companyName",
            c.userName AS "userName",
            IFNULL(d.name,d.shortName) AS "cusName",
            f.recordId AS "groupOrgId",
            f.name AS "groupOrgName",
            g.value AS "payWayValue",
            d.custType,
            a.*
        FROM sl_single_overdue_deductions a
        LEFT JOIN md_company b ON b.recordId = a.companyId
        LEFT JOIN sm_user c ON c.recordId = a.userId
        LEFT JOIN md_customer d ON d.recordId = a.customerId
        LEFT JOIN icloud_group_org f ON f.recordId = a.groupOrgId
        LEFT JOIN md_dict_value g ON g.recordId = d.payWay
        WHERE a.activeFlag = 1
        <if test="companyId != null and companyId != ''">
            AND a.companyId = #{companyId}
        </if>
        <if test="userId != null and userId != ''">
            AND a.userId = #{userId}
        </if>
        <if test="customerId != null and customerId != ''">
            AND a.customerId = #{customerId}
        </if>
        <if test="overdueDate != null and overdueDate != ''">
            AND a.overdueDate = #{overdueDate}
        </if>
        <if test="dateStr != null and dateStr != ''">
            AND DATE_FORMAT(a.overdueDate,'%Y%m') = #{dateStr}
        </if>
        ORDER BY a.createdDate DESC
    </select>

    <update id="updateStatus">
        UPDATE sl_single_overdue_deductions SET
            status = #{status}
        WHERE recordId = #{recordId}
    </update>

    <update id="update">
        UPDATE sl_single_overdue_deductions SET
            userId = #{userId},
            customerId = #{customerId},
            royaltyRateId = #{royaltyRateId},
            period = #{period},
            overdueDate = #{overdueDate},
            amount = #{amount},
            rateAmount = #{rateAmount},
            deductionAmount = #{deductionAmount},
            deductionRate = #{deductionRate}
        WHERE recordId = #{recordId}
    </update>

    <select id="checkCount" resultType="Integer">
        SELECT
            COUNT(1)
        FROM sl_single_overdue_deductions
        WHERE companyId = #{companyId} AND activeFlag = 1
        AND  customerId = #{customerId} AND period = #{period}
        AND deductionRate = #{deductionRate}
    </select>

</mapper>