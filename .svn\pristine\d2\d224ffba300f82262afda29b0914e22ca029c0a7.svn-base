package com.kyb.pcberp.modules.hr.payment_center.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;

import java.math.BigDecimal;
import java.util.Date;

public class Hr_SecurityManagement extends DataEntity<Hr_SecurityManagement> {

    private static final long serialVersionUID = 1L;

    private String number; //员工工号

    private String name; //姓名

    private String department; //部门

    private String phone; //电话

    private Date onboardtime; //入职日期

    private Date turnovertime; //离职日期

    private String surpluscity; //公积金城市

    private BigDecimal socialbase; //社保基数

    private BigDecimal reservebase; //公积金基数

    private String staffName; // 员工名称

    private String departmentName; // 部门名称

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getOnboardtime() {
        return onboardtime;
    }

    public void setOnboardtime(Date onboardtime) {
        this.onboardtime = onboardtime;
    }
    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getTurnovertime() {
        return turnovertime;
    }

    public void setTurnovertime(Date turnovertime) {
        this.turnovertime = turnovertime;
    }

    public String getSurpluscity() {
        return surpluscity;
    }

    public void setSurpluscity(String surpluscity) {
        this.surpluscity = surpluscity;
    }

    public BigDecimal getSocialbase() {
        return socialbase;
    }

    public void setSocialbase(BigDecimal socialbase) {
        this.socialbase = socialbase;
    }

    public BigDecimal getReservebase() {
        return reservebase;
    }

    public void setReservebase(BigDecimal reservebase) {
        this.reservebase = reservebase;
    }

    public String getStaffName()
    {
        return staffName;
    }

    public void setStaffName(String staffName)
    {
        this.staffName = staffName;
    }

    public String getDepartmentName()
    {
        return departmentName;
    }

    public void setDepartmentName(String departmentName)
    {
        this.departmentName = departmentName;
    }

    public String getOnboardtimeStr(){
        if (onboardtime == null){
            return "";
        }
        return DateUtils.formatDate(onboardtime);
    }
    public String getTurnovertimeStr(){
        if (turnovertime == null){
            return "";
        }
        return DateUtils.formatDate(turnovertime);
    }
}
