<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.finance.dao.StockAccountingDao">
    
	<sql id="stockAccountingColumns">
		a.recordId,
		a.companyId,
		a.month,
		a.materialNum,
		a.startAmount,
		a.endAmount,
		a.activeFlag,
		a.status,
		a.createdBy,
		a.createdDate,
		a.lastUpdBy,
		a.lastUpdDate,
		a.remark,
		a.inAmount,
		a.outAmount
	</sql>
	
	<sql id="stockAccountingJoins">
		LEFT JOIN re_stock_accounting_detail rsad.accountingId = a.recordId AND rsad.activeFlag = 1
	</sql>
	
	<select id="get" resultType="StockAccounting">
		SELECT 
			<include refid="stockAccountingColumns"/>
		FROM re_stock_accounting a
		WHERE a.recordId = #{recordId}
	</select>
	
	
	<select id="findList" resultType="StockAccounting">
		SELECT 
			<include refid="stockAccountingColumns"/>
		FROM re_stock_accounting a
		<where>
			a.companyId = #{company.recordId} 
			AND a.activeFlag = 1
			<if test="month != null and month != ''">
			AND a.month LIKE CONCAT('%',#{month},'%')
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
		
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO re_stock_accounting(
			companyId,
			month,
			materialNum,
			startAmount,
			endAmount,
			activeFlag,
			status,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			remark,
			inAmount,
			outAmount
		) VALUES (
			#{company.recordId},
			#{month},
			#{materialNum},
			#{startAmount},
			#{endAmount},
			1,
			#{status},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{inAmount},
			#{outAmount}
		)
	</insert>
	
	<insert id="batchInsertDetails" parameterType="java.util.List">
		INSERT INTO re_stock_accounting_detail(
				companyId,
				materialId,
				accountingId,
				startStrocks,
				inStrocks,
				outStrocks,
				endStrocks,
				startPrice,
				endPrice,
				inAmount,
				outAmount,
				startAmount,
				endAmount,
				`status`,
				activeFlag,
				createdBy,
				createdDate,
				remark,
				month
			) VALUES 
			<foreach collection="list" item="item" index= "index" separator =",">
			(
				#{item.company.recordId},
				#{item.materialId},
				#{item.stockAccounting.recordId},
				#{item.startStrocks},
				#{item.inStrocks},
				#{item.outStrocks},
				#{item.endStrocks},
				#{item.startPrice},
				#{item.endPrice},
				#{item.inAmount},
				#{item.outAmount},
				#{item.startAmount},
				#{item.endAmount},
				#{item.status},
				1,
				#{item.createdBy.recordId},
				#{item.createdDate},
				#{item.remark},
				#{item.month}
			)
		</foreach>
	
	</insert>
	
	<update id="update">
		UPDATE re_stock_accounting SET 	
			companyId = #{company.recordId},
			month = #{month},
			materialNum = #{materialNum},
			startAmount = #{startAmount},
			endAmount = #{endAmount},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			remark = #{remark}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE  re_stock_accounting SET activeFlag = 0
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateStatus">
		UPDATE  re_stock_accounting SET 
			status = #{status},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="findMonth" resultType="StockAccounting">
		SELECT month,status,recordId 
		FROM re_stock_accounting WHERE companyId = #{company.recordId} AND activeFlag = 1 
		ORDER BY month DESC
		LIMIT 1
	</select>
	
	<select id="getDetailList" resultType="StockAccounting">
		SELECT recordId AS 'materialId' 
		FROM md_material 
		WHERE 
		materialKind = 100701 AND activeFlag = 1 
		AND companyId = #{company.recordId} 
	</select>
	
	<select id="getPeriodDate" resultType="Date">
		SELECT MAX(initDate) FROM md_material_stock_init WHERE initDate <![CDATA[<=]]>#{date} AND companyId = #{companyId}
	</select>
	
	<select id="getStockAccountingList" resultType="StockAccounting">
		
		SELECT 
			mm.recordId AS 'materialId',
			mm.`no`,
			mm.specification,
			mdv.`value` AS 'materialType',
			(
			SELECT SUM(periodStocks) FROM  md_material_stock_init mmsiS WHERE mmsiS.materialId = mm.recordId AND mmsiS.initDate = #{startPeriodDate}
			) AS 'startPeriodStocks',
			(SELECT price FROM md_material_price_init WHERE materialId = mm.recordId AND activeFlag = 1 AND initDate <![CDATA[<=]]> #{startDate} ORDER BY initDate DESC LIMIT 1) AS 'startPrice',
			(SELECT price FROM md_material_price_init WHERE materialId = mm.recordId AND activeFlag = 1 AND initDate <![CDATA[<=]]> #{endDate}  ORDER BY initDate DESC LIMIT 1) AS 'endPrice',
			(
			SELECT SUM(CASE smsS.inOutType WHEN 1 THEN IFNULL(smsS.quantity,0) 
									WHEN 2 THEN IFNULL(smsS.quantity,0) 
									WHEN 3 THEN IFNULL(-smsS.quantity,0) 
									WHEN 4 THEN IFNULL(-smsS.quantity,0) 
									WHEN 5 THEN IFNULL(-smsS.quantity,0) 
									WHEN 6 THEN IFNULL(-smsS.quantity,0) 
									WHEN 7 THEN IFNULL(smsS.quantity,0) 
									WHEN 8 THEN IFNULL(-smsS.quantity,0) 
									WHEN 9 THEN IFNULL(smsS.quantity,0) 
									WHEN 10 THEN IFNULL(-smsS.quantity,0) 
									WHEN 20 THEN IFNULL(smsS.quantity,0) 
									ELSE 0 END)  FROM st_material_store smsS WHERE smsS.operateDate<![CDATA[>=]]>#{startPeriodDate}  AND smsS.operateDate <![CDATA[<]]>#{startDate} AND  smsS.materialId = mm.recordId AND smsS.activeFlag = 1
			) AS 'startInOutQty',
			(
			SELECT SUM(periodStocks) FROM  md_material_stock_init mmsiE WHERE mmsiE.materialId = mm.recordId AND mmsiE.initDate = #{endPeriodDate}
			) AS 'endPeriodStocks',
			(
			SELECT SUM(CASE smsE.inOutType WHEN 1 THEN IFNULL(smsE.quantity,0) 
									WHEN 2 THEN IFNULL(smsE.quantity,0) 
									WHEN 3 THEN IFNULL(-smsE.quantity,0) 
									WHEN 4 THEN IFNULL(-smsE.quantity,0) 
									WHEN 5 THEN IFNULL(-smsE.quantity,0) 
									WHEN 6 THEN IFNULL(-smsE.quantity,0) 
									WHEN 7 THEN IFNULL(smsE.quantity,0) 
									WHEN 8 THEN IFNULL(-smsE.quantity,0) 
									WHEN 9 THEN IFNULL(smsE.quantity,0) 
									WHEN 10 THEN IFNULL(-smsE.quantity,0) 
									WHEN 20 THEN IFNULL(smsE.quantity,0) 
									ELSE 0 END)  FROM st_material_store smsE WHERE smsE.operateDate<![CDATA[>=]]>#{endPeriodDate}  AND smsE.operateDate <![CDATA[<]]>#{endDate} AND  smsE.materialId = mm.recordId AND smsE.activeFlag = 1
			) AS 'endInOutQty',
			(
			SELECT 
			SUM(CASE smsI.inOutType WHEN 1 THEN IFNULL(smsI.quantity,0) 
									WHEN 2 THEN IFNULL(smsI.quantity,0) 					
									WHEN 7 THEN IFNULL(smsI.quantity,0) 	
									WHEN 20 THEN IFNULL(smsI.quantity,0) 
									ELSE 0 END) FROM st_material_store smsI WHERE smsI.operateDate<![CDATA[>=]]>#{startDate}  AND smsI.operateDate<![CDATA[<]]>#{endDate} AND  smsI.materialId = mm.recordId AND smsI.activeFlag = 1
			)  AS 'inStrocks',
			(
			SELECT 
			SUM(CASE smsI.inOutType WHEN 1 THEN IFNULL(smsI.quantity,0)*IFNULL(smsI.price,0)
									WHEN 2 THEN IFNULL(smsI.quantity,0)*IFNULL(smsI.price,0)		
									WHEN 7 THEN IFNULL(smsI.quantity,0)*IFNULL(smsI.price,0) 	
									WHEN 20 THEN IFNULL(smsI.quantity,0)*IFNULL(smsI.price,0) 
									ELSE 0 END) FROM st_material_store smsI WHERE smsI.operateDate<![CDATA[>=]]>#{startDate}  AND smsI.operateDate <![CDATA[<]]>#{endDate} AND  smsI.materialId = mm.recordId AND smsI.activeFlag = 1
			)  AS 'inAmount',
			(
			SELECT SUM(CASE smsO.inOutType  
									WHEN 3 THEN IFNULL(smsO.quantity,0) 
									WHEN 4 THEN IFNULL(smsO.quantity,0) 
									WHEN 5 THEN IFNULL(smsO.quantity,0) 
									WHEN 6 THEN IFNULL(smsO.quantity,0) 
									WHEN 8 THEN IFNULL(smsO.quantity,0) 
									ELSE 0 END)  FROM st_material_store smsO WHERE smsO.operateDate<![CDATA[>=]]>#{startDate}  AND smsO.operateDate <![CDATA[<]]>#{endDate}  AND  smsO.materialId = mm.recordId AND smsO.activeFlag = 1
			) AS 'outStrocks'
		FROM md_material mm
		LEFT JOIN md_dict_value mdv ON mdv.recordId = mm.materialType
		WHERE mm.materialKind = 100701 AND mm.activeFlag = 1 AND mm.companyId = #{company.recordId} 
		GROUP BY mm.recordId 
	
	</select>
	
	<select id="findDetailList" resultType="StockAccounting">
		SELECT 
			mm.`no` AS 'materialNo',
			mm.specification,
			mdv.`value` AS 'materialType',
			rsad.* 
		FROM 
		re_stock_accounting_detail rsad 
		LEFT JOIN re_stock_accounting  rsa ON rsa.recordId = rsad.accountingId 
		LEFT JOIN md_material mm ON mm.recordId = rsad.materialId
		LEFT JOIN md_dict_value mdv ON mdv.recordId = mm.materialType
		WHERE rsad.companyId = #{company.recordId} AND rsad.activeFlag = 1 AND rsa.activeFlag = 1
		<if test="month != null and month !=''">
				AND REPLACE(rsad.month," ","") LIKE CONCAT('%', REPLACE(#{month}," ",""), '%')
		</if> 
		<if test="materialNo != null and materialNo !=''">
				AND REPLACE(mm.no," ","") LIKE CONCAT('%', REPLACE(#{materialNo}," ",""), '%')
		</if>
		<if test="accountingId != null and accountingId !=''">
				AND rsad.accountingId = #{accountingId}
		</if> 
		<if test="materialType != null and materialType !=''">
				AND REPLACE(mdv.`value`," ","") LIKE CONCAT('%', REPLACE(#{materialType}," ",""), '%')
		</if>
	
	</select>
	
</mapper>