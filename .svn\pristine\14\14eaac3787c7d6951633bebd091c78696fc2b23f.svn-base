package com.kyb.pcberp.modules.wechat.service;

import com.kyb.pcberp.common.service.BaseService;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.alimessage.MessageUtil;
import com.kyb.pcberp.modules.wechat.dao.AccountBindDao;
import com.kyb.pcberp.modules.wechat.dao.MessageDao;
import com.kyb.pcberp.modules.wechat.entity.IcloudMessage;
import com.kyb.pcberp.modules.wechat.entity.IcloudUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class MessageService extends BaseService
{
    @Autowired
    private AccountBindDao accountBindDao;

    @Autowired
    private MessageDao messageDao;

    // 插入消息
    @Transactional(readOnly = false)
    public String addMessage(IcloudMessage icloudMessage, String openId)
    {
        if (StringUtils.isNotBlank(openId))
        {
            IcloudUser user = accountBindDao.getUser(openId);
            icloudMessage.setCreatedId(user.getRecordId());
            if (StringUtils.isBlank(icloudMessage.getReceiveId()))
            {
                icloudMessage.setReceiveId(user.getRecordId());
            }
        }
        if (StringUtils.isBlank(icloudMessage.getMessage()) || StringUtils.isBlank(icloudMessage.getReceiveId()))
        {
            return "fail";
        }
        icloudMessage.setFlag("1");
        if (StringUtils.isBlank(icloudMessage.getStatus()))
        {
            icloudMessage.setStatus("2");
        }
        messageDao.addMessage(icloudMessage);
        // 微信发送消息
        MessageUtil.sendMsg(icloudMessage.getReceiveId(), "88", icloudMessage.getMessage());
        return "success";
    }

    // 读取消息
    @Transactional(readOnly = false)
    public String updateFlag(IcloudMessage icloudMessage)
    {
        messageDao.updateFlag(icloudMessage);
        return "success";
    }

    // 获取我的消息
    public List<IcloudMessage> getMessageList(IcloudMessage icloudMessage, String openId)
    {
        IcloudUser user = accountBindDao.getUser(openId);
        icloudMessage.setReceiveId(user.getRecordId());
        return messageDao.getMessageList(icloudMessage);
    }
}
