<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.wechat.dao.KybDayThingDao">
	<select id="getKybDayThing" resultType="KybDayThing">
		SELECT
			a.recordId,
			a.groupId,
			a.departId,
			a.title,
			a.content,
			a.shareTimeWx,
			a.createTimeWx,
			a.shareId,
			a.address,
			DATE_FORMAT(a.startTime,'%Y-%m-%d %H:%i:%s') AS "startTime",
			DATE_FORMAT(a.endTime,'%Y-%m-%d %H:%i:%s') AS "endTime",
			a.createdBy AS "createdBy.recordId",
			DATE_FORMAT(a.createdDate,'%Y-%m-%d %H:%i:%s') AS "createdDate",
			b.`name` AS "createdBy.userName",
			(
			  	SELECT 
			  		GROUP_CONCAT(NAME) 
			  	FROM oa_md_employee 
			  	WHERE FIND_IN_SET(recordId, a.shareId)
		 	) AS "shareNames",
			(
				SELECT
					GROUP_CONCAT(recordId)
				FROM oa_md_employee
				WHERE FIND_IN_SET(recordId, a.shareId)
			) AS "shareId",
			(
				CASE WHEN a.startTime <![CDATA[<]]> NOW() AND a.endTime <![CDATA[>]]> NOW() THEN '1'
				WHEN a.endTime <![CDATA[<]]> NOW() AND a.startTime <![CDATA[<]]> NOW() THEN '2'
				WHEN a.startTime <![CDATA[>]]> NOW() THEN '3'
				ELSE '0' END
			) AS "categoryFlag"
		FROM oa_dayThing a
		LEFT JOIN oa_md_employee b ON b.recordId = a.createdBy
		WHERE a.recordId = #{recordId}
	</select>
    
   	<select id="getDayThingComment" resultType="WechatComment">
		SELECT
			a.recordId,
			a.dayThingId,
			a.content,
			a.commentId,
			a.activeFlag,
			a.createdBy,
			DATE_FORMAT(a.createdDate,'%Y-%m-%d %H:%i:%s') AS "createdDate",
			b.`name`,
			d.`name` AS "replyName",
			a.commentFaId,
			e.fileUrl AS "employee.fileUrl",
			e.defaultImg AS "employee.defaultImg"
		FROM oa_audit_new_comment a
		LEFT JOIN oa_md_employee b ON b.recordId = a.createdBy
		LEFT JOIN oa_audit_new_comment c ON c.recordId = a.commentId
		LEFT JOIN oa_md_employee d ON d.recordId = c.createdBy
		LEFT JOIN icloud_sm_user e ON e.recordId = b.userId
		WHERE a.dayThingId = #{recordId}
		AND a.activeFlag = 1
		ORDER BY a.createdDate ASC
	</select>
	
	<select id="getAttachmments" resultType="WechatAttachments">
		SELECT 
			recordId,
			orgFileName AS name,
			fileUrl,
			type
		FROM oa_dayThing_attachment 
		WHERE dayThingId = #{recordId} 
		AND activeFlag = 1
	</select>
	
	<select id="getDayThingList" resultType="KybDayThing">
		SELECT
			a.recordId,
			a.groupId,
			a.departId,
			a.title,
			a.content,
			a.shareTimeWx,
			a.createTimeWx,
			a.shareId,
			a.address,
			DATE_FORMAT(a.startTime,'%Y-%m-%d %H:%i:%s') AS "startTime",
			DATE_FORMAT(a.endTime,'%Y-%m-%d %H:%i:%s') AS "endTime",
			a.createdBy AS "createdBy.recordId",
			DATE_FORMAT(a.createdDate,'%Y-%m-%d %H:%i:%s') AS "createdDate",
			b.`name` AS "createdBy.userName",
			(
			  	SELECT 
			  		GROUP_CONCAT(NAME) 
			  	FROM oa_md_employee 
			  	WHERE FIND_IN_SET(recordId, a.shareId)
		 	) AS "shareNames",
			(
				CASE WHEN a.startTime <![CDATA[<]]> NOW() AND a.endTime <![CDATA[>]]> NOW() THEN '1'
				WHEN a.endTime <![CDATA[<]]> NOW() AND a.startTime <![CDATA[<]]> NOW() THEN '2'
				WHEN a.startTime <![CDATA[>]]> NOW() THEN '3'
				ELSE '0' END
			) AS "categoryFlag"
		FROM oa_dayThing a
		LEFT JOIN oa_md_employee b ON b.recordId = a.createdBy
		WHERE a.activeFlag = 1
		<choose>
			<when test="status != null and status !='' and status == 1">
				AND a.createdBy = #{empId}
			</when>
			<when test="status != null and status !='' and status == 2">
				AND FIND_IN_SET(#{empId}, a.shareId)
			</when>
			<otherwise>
				AND (a.createdBy = #{empId} OR FIND_IN_SET(#{empId}, a.shareId))
			</otherwise>
		</choose>
		<if test="flag != null and flag !='' and flag == 1">
			AND DATE(a.startTime) <![CDATA[<=]]> CURDATE() AND DATE(a.endTime) <![CDATA[>=]]> CURDATE()
		</if>
		<if test="flag != null and flag !='' and flag == 2">
			AND YEARWEEK(a.startTime, 1) <![CDATA[<=]]> YEARWEEK(CURDATE(), 1) AND YEARWEEK(a.endTime, 1) <![CDATA[>=]]> YEARWEEK(CURDATE(), 1)
		</if>
		<if test="flag != null and flag !='' and flag == 3">
			AND YEAR(a.startTime) <![CDATA[<=]]> YEAR(CURDATE())
			AND MONTH(a.startTime) <![CDATA[<=]]> MONTH(CURDATE())
			AND YEAR(a.endTime) <![CDATA[>=]]> YEAR(CURDATE())
			AND MONTH(a.endTime) <![CDATA[>=]]> MONTH(CURDATE())
		</if>
		<if test="categoryFlag != null and categoryFlag !='' and categoryFlag == 1">
			AND a.startTime <![CDATA[<]]> NOW() AND a.endTime <![CDATA[>]]> NOW()
		</if>
		<if test="categoryFlag != null and categoryFlag !='' and categoryFlag == 2">
			AND a.endTime <![CDATA[<]]> NOW() AND a.startTime <![CDATA[<]]> NOW()
		</if>
		<if test="categoryFlag != null and categoryFlag !='' and categoryFlag == 3">
			AND a.startTime <![CDATA[>]]> NOW()
		</if>
		<if test="title != null and title !=''">
			AND CONCAT(
			IFNULL(a.content, ""),
			IFNULL(a.title, ""),
			IFNULL(a.address, "")
			)
			LIKE CONCAT('%', REPLACE(#{title}," ",""), '%')
		</if>
		ORDER BY a.createdDate DESC, a.recordId DESC
		<if test="pageNo != null and pageNo != '' and pageSize != null and pageSize != ''">
			LIMIT ${pageNo},${pageSize};
		</if>
	</select>
	
	<insert id="addDayThing" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO oa_dayThing (
			title,
			content,
			shareTimeWx,
			createTimeWx,
			shareId,
			address,
			startTime,
			endTime,
			createdBy,
			createdDate,
			activeFlag
		)
		VALUES
			(
				#{title},
				#{content},
				#{shareTimeWx},
				#{createTimeWx},
				#{shareId},
				#{address},
				#{startTime},
				#{endTime},
				#{empId},
				NOW(),
				1
			)
	</insert>
	
	<update id="delDayThing" >
    	UPDATE oa_dayThing
			SET activeFlag = 0
    	WHERE recordId = #{recordId}
    </update>
	
	<update id="updateDayThing" >
    	UPDATE oa_dayThing SET 
    		title = #{title},
    		content = #{content},
    		shareTimeWx = #{shareTimeWx},
    		createTimeWx = #{createTimeWx},
    		shareId = #{shareId},
    		address = #{address},
    		startTime = #{startTime},
    		endTime = #{endTime},
    		lastUpdBy = #{empId},
    		lastUpdDate = NOW()
    	WHERE recordId = #{recordId}
    </update>
	
	<insert id="uploadFile" useGeneratedKeys="true" keyProperty="recordId"  parameterType="DayThingFiles">
    	INSERT INTO oa_dayThing_attachment(
    		dayThingId,
    		createdDate,
    		orgFileName,
    		realFileName,
    		fileUrl,
    		type
    	)VALUES (
    		#{dayThingId},
    		now(),
    		#{orgFileName},
    		#{realFileName},
    		#{fileUrl},
    		#{type}
    	)	
    </insert>
    
    <delete id="delFile">
    	DELETE FROM oa_dayThing_attachment WHERE recordId = #{recordId}
    </delete>
    
    <select id="getTimeMsg" resultType="KybDayThing">
		SELECT
			shareId AS "empId",
			content,
			title,
			shareTimeWx AS timeNotice
		FROM
			oa_dayThing
		WHERE
			(
				shareTimeWx IS NOT NULL
				AND DATE_FORMAT(
					DATE_SUB(
						startTime,
						INTERVAL shareTimeWx DAY
					),
					'%Y-%m-%d'
				) = DATE_FORMAT(NOW(), '%Y-%m-%d')
			)
		UNION ALL
			SELECT
				createdBy AS "empId",
				content,
				title,
				createTimeWx AS timeNotice
			FROM
				oa_dayThing
			WHERE
				(
					createTimeWx IS NOT NULL
					AND DATE_FORMAT(
						DATE_SUB(
							startTime,
							INTERVAL createTimeWx DAY
						),
						'%Y-%m-%d'
					) = DATE_FORMAT(NOW(), '%Y-%m-%d')
				);
	</select>
</mapper>