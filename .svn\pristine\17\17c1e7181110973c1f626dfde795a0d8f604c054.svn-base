package com.kyb.pcberp.modules.wechat.pojo.wechatUser;

import com.kyb.pcberp.common.persistence.DataEntity;

public class WechatOaNumber extends DataEntity<WechatOaNumber>
{
    private static final long serialVersionUID = 1L;
    
    private String id;
    
    private String no;
    
    private String num;
    
    private String type;
    
    private String date;
    
    public String getId()
    {
        return id;
    }
    
    public void setId(String id)
    {
        this.id = id;
    }
    
    public String getNo()
    {
        return no;
    }
    
    public void setNo(String no)
    {
        this.no = no;
    }
    
    public String getNum()
    {
        return num;
    }
    
    public void setNum(String num)
    {
        this.num = num;
    }
    
    public String getType()
    {
        return type;
    }
    
    public void setType(String type)
    {
        this.type = type;
    }
    
    public String getDate()
    {
        return date;
    }
    
    public void setDate(String date)
    {
        this.date = date;
    }
}
