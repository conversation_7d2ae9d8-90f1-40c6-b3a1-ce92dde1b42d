package com.kyb.pcberp.modules.group.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

public class GroupDbManage extends DataEntity<GroupDbManage>
{
    private static final long serialVersionUID = 1L;

    private String name;

    private String groupDbName;

    private String remark;

    private String groupId;

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getGroupDbName()
    {
        return groupDbName;
    }

    public void setGroupDbName(String groupDbName)
    {
        this.groupDbName = groupDbName;
    }

    public String getRemark()
    {
        return remark;
    }

    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public String getGroupId()
    {
        return groupId;
    }

    public void setGroupId(String groupId)
    {
        this.groupId = groupId;
    }
}
