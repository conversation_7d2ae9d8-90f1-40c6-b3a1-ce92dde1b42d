package com.kyb.pcberp.modules.hr.group_center.controller;

import com.kyb.pcberp.common.dataSource.TemporaryDataSource;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.modules.hr.group_center.pojo.*;
import com.kyb.pcberp.modules.hr.group_center.service.Hr_CenterService;
import com.kyb.pcberp.modules.wechat.entity.IcloudUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

// 控制中心
@Controller
@RequestMapping(value = "${hrPath}/center")
public class Hr_CenterController {
    @Autowired
    private Hr_CenterService hr_centerService;

    // 获取系统监控
    @RequestMapping(value = {"getReports"})
    @ResponseBody
    public Page<Hr_Report> getReports(@RequestBody Hr_Report report) {
        return hr_centerService.getReports(report);
    }

    // 获取系统信息
    @RequestMapping(value = {"getSystemMsg"})
    @ResponseBody
    public Hr_System getSystemMsg(@RequestBody IcloudUser user) {
        TemporaryDataSource.getInstance().clearSession();
        return hr_centerService.getSystemMsg(user);
    }

    // 修改系统信息
    @RequestMapping(value = {"editSystemMsg"})
    @ResponseBody
    public String editSystemMsg(@RequestBody Hr_System hrSystem) {
        TemporaryDataSource.getInstance().clearSession();
        return hr_centerService.editSystemMsg(hrSystem);
    }

    // 修改数据库
    @RequestMapping(value = {"editDbMsg"})
    @ResponseBody
    public String editDbMsg(@RequestBody Hr_Db hrDb) {
        return hr_centerService.editDbMsg(hrDb);
    }

    @RequestMapping(value = {"getProcessList"})
    @ResponseBody
    public Page<Hr_Process> getProcessList(@RequestBody IcloudUser user) {
        return hr_centerService.getProcessList(user);
    }

    //集团管控保存
    @RequestMapping(value = {"saveProcess"})
    @ResponseBody
    public String saveProcess(@RequestBody Hr_Process groupProcessTwo) {
        return hr_centerService.saveProcess(groupProcessTwo);
    }

    //获取组织架构树数据
    @RequestMapping(value = {"getList"})
    @ResponseBody
    public List<Hr_DepartRelation> getList(@RequestBody IcloudUser user) {
        List<Hr_DepartRelation> list = hr_centerService.getList(user);
        return list;
    }

    //获取数据库信息
    @RequestMapping(value = {"getDbList"})
    @ResponseBody
    public List<Hr_Db> getDbList(@RequestBody IcloudUser user) {
        List<Hr_Db> list = hr_centerService.getDbList(user);
        return list;
    }

    //删除集团管控数据
    @RequestMapping(value = {"delProcess"})
    @ResponseBody
    public String delProcess(@RequestBody Hr_Process groupProcessTwo) {
        return hr_centerService.delProcess(groupProcessTwo);
    }
}
