<%@ page contentType="text/html;charset=UTF-8"%>
<!-- BEGIN 导航-->
<div class="page-bar  page-bar-margin-top"" >
	<ul class="page-breadcrumb">
		<li><i class="fa fa-home"></i> <a href="#/dashboard.html">主页</a>
			<i class="fa fa-angle-right"></i></li>
		<li><a href="javascript:;">生产管理</a> <i class="fa fa-angle-right"></i></li>

		<li><a ui-sref="production.capacity">产能评估</a></li>
	</ul>
</div>



<!-- END 导航-->
<tabset class="tabset-margin-top"> <!-- BEGIN 公告列表 -->

 <tab
	heading="产能列表" active="capacityCtrl.tabs.viewForm.active">
	<div class="rows">
		 <div class="panel panel-default">
		<div class="panel-heading font-blue-hoki">查询</div>
		<div class="panel-body">
			<form class="form-horizontal">
				<div class="row">
					<div class="col-md-10">
						<div class="form-group">
							<label class="control-label col-md-2">日期：</label>
							<div class="col-md-4">
								<input style="width: 100%;" type="text" class="form-control"
									ng-model="capacityCtrl.query.data.value"
									data-date-format="yyyy-MM-dd" 
									data-date-type="number"
									data-min-date="02/10/1901"
									data-max-date="today"
									data-autoclose="1" 
									daysOfWeekDisabled="false" name="birthday"
									bs-datepicker disable-valid-styling="true"
									disable-invalid-styling="true" />
							</div>
						<!-- <label class="control-label col-md-2">工序名称：</label>
							<div class="col-md-4">
								<ui-select ng-model="capacityCtrl.query.userCode.value"
									theme="bootstrap"> <ui-select-match
									placeholder="请选择...">{{$select.selected.name}} </ui-select-match>
								<ui-select-choices
									repeat="item.name as item in capacityCtrl.process | filter: $select.search">
								<div ng-bind-html="item.name | highlight: $select.search"></div>
								</ui-select-choices> </ui-select>
							</div> -->	
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-12">
						<button class="btn btn-default btn-default-width pull-right"
							ng-click="capacityCtrl.doQuery()">
							<i class="fa fa-search"></i> 查询
						</button>
					</div>
				</div>
			</form>
		</div>
	</div>
	<div class="portlet light bordered">
		<div class="portlet-title">
			<div class="caption font-blue-hoki">产能列表</div>
			<div class="actions">
				<form class="navbar-form navbar-right"
					action="a/production/capacity/export" method="get"
					enctype="multipart/form-data">
					<div class="form-group" ng-if="capacityCtrl.right.edit">
						<button type="submit" class="btn btn-info">
							<i class="fa fa-file-excel-o"></i> 导出
						</button>
					</div>
				</form>
			</div>
		</div>
		<div class="collapse navbar-collapse navbar-ex1-collapse"></div>
		<div class="portlet-body">
			<div class="table-responsive">
				<table
					class="table table-striped table-bordered table-condensed table-advance table-hover">
					<thead>
						<tr class="heading">
							<th>工序编号</th>
							<th>工序名称</th>
							<th>A板产能</th>
							<th>B板产能</th>
							<th>产能日期</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-repeat="row in capacityCtrl.page.data.list">
							<td ng-bind="row.process.recordId"></td>
							<td ng-bind="row.process.name"></td>
							<td ng-bind="row.processQtyA"></td>
							<td ng-bind="row.processQtyB"></td>
							<td ng-bind="row.processDate"></td>
						</tr>
					</tbody>
				</table>
			</div>
			<div  class="row page-margin-top">
				<div class="col-md-12 col-lg-6">
					<span class="inline">每页</span> <select class="form-control inline"
						style="margin-top: 8px; width: 100px;"
						disable-valid-styling="true" disable-invalid-styling="true"
						ng-model="capacityCtrl.page.pageSize"
						ng-change="capacityCtrl.pageSizeChange()"
						ng-options="pageSizeOption for pageSizeOption in capacityCtrl.page.pageSizeOptions">
					</select> <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示
						{{capacityCtrl.page.data.startCount}} /
						{{capacityCtrl.page.data.endCount}} 条，共
						{{capacityCtrl.page.data.count}} 条</span>
				</div>
				<div class="col-md-12 col-lg-6">
					<paging class="pull-right" page="capacityCtrl.page.data.pageNo"
						page-size="capacityCtrl.page.data.pageSize"
						total="capacityCtrl.page.data.count" adjacent="1" dots="..."
						scroll-top="false" hide-if-empty="false" ul-class="pagination"
						active-class="active" disabled-class="disabled"
						show-prev-next="true"
						paging-action="capacityCtrl.doPage(page, pageSize, total)"></paging>
				</div>
			</div>
		</div>
	</div>
</div>
</tab> 

<!--  <tab heading="工序月产能评估列表" active="capacityCtrl.tabs.editForm.active">
<div class="rows">
	 <div class="panel panel-default">
		<div class="panel-heading font-blue-hoki">查询</div>
		<div class="portlet-body">
			<form class="form-horizontal">
				<div class="row">
					<div class="col-md-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">时间区间：</label>
							<div class="col-sm-7 col-md-8">
								<input style="width: 200px;" type="text" class="form-control"
									ng-model="rcapacityCtrl.query.userCode12.value"
									data-date-format="yyyy-MM-dd" data-date-type="number"
									data-min-date="02/10/1901" data-max-date="today"
									data-autoclose="1" daysOfWeekDisabled="false" name="birthday"
									bs-datepicker disable-valid-styling="true"
									disable-invalid-styling="true" />
							</div>
						</div>
					</div>
					<div class="col-md-8">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">工序名称：</label>
							<div class="col-sm-7 col-md-8">
								<input type="text" class="form-control"
									ng-model="capacityCtrl.query.userCode.value"
									disable-valid-styling="true" disable-invalid-styling="true" />
							</div>
						</div>
					</div>

					<div class="col-md-4">
						<div class="form-group">
							<label class="col-sm-3 col-md-4 control-label">至</label>
							<div class="col-sm-7 col-md-8">
								<input style="width: 200px;" type="text" class="form-control"
									ng-model="rcapacityCtrl.query.userCode1122.value"
									data-date-format="yyyy-MM-dd" data-date-type="number"
									data-min-date="02/10/1901" data-max-date="today"
									data-autoclose="1" daysOfWeekDisabled="false" name="birthday"
									bs-datepicker disable-valid-styling="true"
									disable-invalid-styling="true" />
							</div>
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-sm-offset-10 col-md-12">
						<input class="btn btn-primary" type="button" value="查询"
							ng-click="capacityCtrl.doQuery()" /> <input
							class="btn btn-primary" type="button" value="重置"
							ng-click="capacityCtrl.reset()" />
					</div>
				</div>
			</form>
		</div>
	</div>
	<div class="portlet light bordered">
		<div class="portlet-title">
			<div class="caption font-blue-hoki">
				<span class="caption-subject bold uppercase"> 工序产能列表</span>
			</div>
			<div class="actions">
				<a class="btn btn-circle btn-icon-only btn-default fullscreen"
					href="#"> </a>
			</div>
		</div>
		<div class="collapse navbar-collapse navbar-ex1-collapse">

			<form class="navbar-form navbar-right"
				action="a/production/capacity/export" method="get"
				enctype="multipart/form-data">
				<div class="form-group" ng-if="capacityCtrl.right.edit">
					<button type="submit" class="btn btn-info">
						<i class="fa fa-file-excel-o"></i> 导出
					</button>
				</div>
			</form>

		</div>
		<div class="portlet-body">
			<table id="contentTable"
				class="table table-striped table-bordered table-condensed table-hover">
				<thead>
					<tr>
						<th>工序编号</th>
						<th>工序名称</th>
						<th>A板产能</th>
						<th>B板产能</th>
						<th>产能日期</th>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="row in capacityCtrl.page.data.list">
						<td ng-bind="row.process.recordId"></td>
						<td ng-bind="row.process.name"></td>
						<td ng-bind="row.processQtyA"></td>
						<td ng-bind="row.processQtyB"></td>
						<td ng-bind="row.processDate"></td>
					</tr>
				</tbody>
			</table>
			<div class="row" style="margin-top: -20px;">
				<div class="col-md-12 col-lg-6">
					<span class="inline">每页</span> <select class="form-control inline"
						style="margin-top: 8px; width: 100px;"
						disable-valid-styling="true" disable-invalid-styling="true"
						ng-model="capacityCtrl.page.pageSize"
						ng-change="capacityCtrl.pageSizeChange()"
						ng-options="pageSizeOption for pageSizeOption in capacityCtrl.page.pageSizeOptions">
					</select> <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示
						{{capacityCtrl.page.data.startCount}} /
						{{capacityCtrl.page.data.endCount}} 条，共
						{{capacityCtrl.page.data.count}} 条</span>
				</div>
				<div class="col-md-12 col-lg-6">
					<paging class="pull-right" page="capacityCtrl.page.data.pageNo"
						page-size="capacityCtrl.page.data.pageSize"
						total="capacityCtrl.page.data.count" adjacent="1" dots="..."
						scroll-top="false" hide-if-empty="false" ul-class="pagination"
						active-class="active" disabled-class="disabled"
						show-prev-next="true"
						paging-action="capacityCtrl.doPage(page, pageSize, total)"></paging>
				</div>
			</div>
		</div>
	</div>
</div>
</tab> 
-->

 </tabset>
