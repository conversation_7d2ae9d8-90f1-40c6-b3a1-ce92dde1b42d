package com.kyb.pcberp.modules.hr.finance_center.pojo;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.contract.entity.DeliveryDetail;
import com.kyb.pcberp.modules.contract.entity.GroupCenter;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.crm.entity.GoodsCheck;
import com.kyb.pcberp.modules.crm.entity.RejectApplication;
import com.kyb.pcberp.modules.eg.entity.CardA;
import com.kyb.pcberp.modules.eg.entity.EgProcess;
import com.kyb.pcberp.modules.finance.entity.CollectMuchMoney;
import com.kyb.pcberp.modules.finance.entity.SinglePayableDetail;
import com.kyb.pcberp.modules.finance.entity.SingleReceivableDetail;
import com.kyb.pcberp.modules.production.entity.Feeding;
import com.kyb.pcberp.modules.purch.entity.MaterialCheck;
import com.kyb.pcberp.modules.purch.entity.PrdorderDetail;
import com.kyb.pcberp.modules.purch.entity.PuReturnsDetail;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;
import com.kyb.pcberp.modules.quality.entity.Discard;
import com.kyb.pcberp.modules.quality.entity.Inspect;
import com.kyb.pcberp.modules.quality.entity.SourceDetection;
import com.kyb.pcberp.modules.stock.entity.ProductStore;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;

import java.util.List;

public class Hr_ReportDetailData extends DataEntity<Hr_ReportDetailData>
{
    private static final long serialVersionUID = 1L;

    private String dataId; // 查账具体id

    private Integer typeId; // 查账具体类型id

    private Integer type; // 类型

    private String deliveryDetailId;

    private String productStoreId;

    private String contractDetailId;

    private Integer inOutFlag;

    private String complaintId;

    private String goodsCheckId;

    private String prdorderDetailId; // 成品采购单明细id

    private String purchasingDtlId; // 原料采购单明细Id

    private String rawStoreId;

    private String materialCheckId; // 付款对账单明细id

    private String rawReturnDetailId; // 原料退货id

    private String prdReturnDetailId; // 成品退货id

    private ContractDetail contractDetail; // 订单明细

    private Notification notification; // 通知单集合

    private DeliveryDetail deliveryDetail; // 送货单明细

    private GoodsCheck goodsCheck; // 收款对账单明细

    private SingleReceivableDetail singleReceivableDetail; // 应收单明细

    private GroupCenter groupCenter; // 一键集控

    private CardA card; // 工程卡

    private Feeding feeding; // 投料单

    private PurchasingDetail purchasingDetail;

    private PrdorderDetail prdorderDetail;

    private List<SourceDetection> sourceDetectionList;

    private RawmaterialStock rawmaterialStock;

    private ProductStore productStore;

    private MaterialCheck materialCheck;

    private SinglePayableDetail singlePayableDetail;

    private List<RawmaterialStock> rawmaterialStockList;

    private List<EgProcess> processList;

    private List<ProductStore> productStoreList;

    private List<Discard> discardList;

    private List<Inspect> inspectList;

    private CollectMuchMoney collectMuchMoney;

    private Integer seqNum;

    private RejectApplication rejectApplication;

    private String desireTreatment;

    private PuReturnsDetail puReturnsDetail;

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    public String getTypeIdStr()
    {
        String result = null;
        if(null == typeId)
        {
            return result;
        }
        switch (typeId)
        {
            case 1:
                result = "收款对账单明细";
                break;
            case 2:
                result = "应收单明细";
                break;
        }
        return result;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTypeStr()
    {
        String result = null;
        if(null == type)
        {
            return result;
        }
        switch (type)
        {
            case 1:
                result = "销售订单";
                break;
            case 2:
                result = "一键集控";
                break;
            case 4:
                result = "通知单";
                break;
            case 5:
                result = "工程卡";
                break;
            case 6:
                result = "投料单";
                break;
            case 7:

                break;
            case 8:
                result = "送货单";
                break;
            case 9:
                result = "收款对账单";
                break;
            case 10:
                result = "应收单";
                break;
            case 11:
                result = "原料采购订单";
                break;
            case 12:
                result = "成品采购订单";
                break;
            case 13:
                result = "来料检测";
                break;
            case 14:
                result = "原料采购入库";
                break;
            case 15:
                result = "成品采购入库";
                break;
            case 16:
                result = "付款对账单";
                break;
            case 17:
                result = "应付单";
                break;
            case 18:
                result = "投料出库记录";
                break;
            case 19:
                result = "生产工序";
                break;
            case 20:
                result = "生产入库";
                break;
            case 21:
                result = "报废记录";
                break;
            case 22:
                result = "品质记录";
                break;
            // 已收单
            case 23:
                result = "已收单";
                break;
            // 外协成品采购
            case 24:
                result = "成品采购";
                break;
            // 外协来料检测
            case 25:
                result = "成品来料检测";
                break;
            // 外协成品采购入库
            case 26:
                result = "成品采购入库";
                break;
            case 27:
                result = "客诉单";
                break;
            case 28:
                result = "原料补货单";
                break;
            case 29:
                result = "补货入库";
                break;
            case 30:
                result = "原料退货单";
                break;
            case 31:
                result = "退货出库";
                break;
            case 32:
                result = "成品补货单";
                break;
            case 33:
                result = "补货入库";
                break;
            case 34:
                result = "成品退货单";
                break;
            case 35:
                result = "退货出库";
                break;

        }
        return result;
    }

    public String getDeliveryDetailId() {
        return deliveryDetailId;
    }

    public void setDeliveryDetailId(String deliveryDetailId) {
        this.deliveryDetailId = deliveryDetailId;
    }

    public String getProductStoreId() {
        return productStoreId;
    }

    public void setProductStoreId(String productStoreId) {
        this.productStoreId = productStoreId;
    }

    public String getRawReturnDetailId() {
        return rawReturnDetailId;
    }

    public void setRawReturnDetailId(String rawReturnDetailId) {
        this.rawReturnDetailId = rawReturnDetailId;
    }

    public String getPrdReturnDetailId() {
        return prdReturnDetailId;
    }

    public void setPrdReturnDetailId(String prdReturnDetailId) {
        this.prdReturnDetailId = prdReturnDetailId;
    }

    public String getContractDetailId() {
        return contractDetailId;
    }

    public void setContractDetailId(String contractDetailId) {
        this.contractDetailId = contractDetailId;
    }

    public Integer getInOutFlag() {
        return inOutFlag;
    }

    public void setInOutFlag(Integer inOutFlag) {
        this.inOutFlag = inOutFlag;
    }

    public String getComplaintId() {
        return complaintId;
    }

    public void setComplaintId(String complaintId) {
        this.complaintId = complaintId;
    }

    public String getGoodsCheckId() {
        return goodsCheckId;
    }

    public void setGoodsCheckId(String goodsCheckId) {
        this.goodsCheckId = goodsCheckId;
    }

    public String getPrdorderDetailId() {
        return prdorderDetailId;
    }

    public void setPrdorderDetailId(String prdorderDetailId) {
        this.prdorderDetailId = prdorderDetailId;
    }

    public String getPurchasingDtlId() {
        return purchasingDtlId;
    }

    public void setPurchasingDtlId(String purchasingDtlId) {
        this.purchasingDtlId = purchasingDtlId;
    }

    public String getRawStoreId() {
        return rawStoreId;
    }

    public void setRawStoreId(String rawStoreId) {
        this.rawStoreId = rawStoreId;
    }

    public String getMaterialCheckId() {
        return materialCheckId;
    }

    public void setMaterialCheckId(String materialCheckId) {
        this.materialCheckId = materialCheckId;
    }

    public ContractDetail getContractDetail() {
        return contractDetail;
    }

    public void setContractDetail(ContractDetail contractDetail) {
        this.contractDetail = contractDetail;
    }

    public Notification getNotification() {
        return notification;
    }

    public void setNotification(Notification notification) {
        this.notification = notification;
    }

    public DeliveryDetail getDeliveryDetail() {
        return deliveryDetail;
    }

    public void setDeliveryDetail(DeliveryDetail deliveryDetail) {
        this.deliveryDetail = deliveryDetail;
    }

    public GoodsCheck getGoodsCheck() {
        return goodsCheck;
    }

    public void setGoodsCheck(GoodsCheck goodsCheck) {
        this.goodsCheck = goodsCheck;
    }

    public SingleReceivableDetail getSingleReceivableDetail() {
        return singleReceivableDetail;
    }

    public void setSingleReceivableDetail(SingleReceivableDetail singleReceivableDetail) {
        this.singleReceivableDetail = singleReceivableDetail;
    }

    public GroupCenter getGroupCenter() {
        return groupCenter;
    }

    public void setGroupCenter(GroupCenter groupCenter) {
        this.groupCenter = groupCenter;
    }

    public CardA getCard() {
        return card;
    }

    public void setCard(CardA card) {
        this.card = card;
    }

    public Feeding getFeeding() {
        return feeding;
    }

    public void setFeeding(Feeding feeding) {
        this.feeding = feeding;
    }

    public PurchasingDetail getPurchasingDetail() {
        return purchasingDetail;
    }

    public void setPurchasingDetail(PurchasingDetail purchasingDetail) {
        this.purchasingDetail = purchasingDetail;
    }

    public PrdorderDetail getPrdorderDetail() {
        return prdorderDetail;
    }

    public void setPrdorderDetail(PrdorderDetail prdorderDetail) {
        this.prdorderDetail = prdorderDetail;
    }

    public List<SourceDetection> getSourceDetectionList() {
        return sourceDetectionList;
    }

    public void setSourceDetectionList(List<SourceDetection> sourceDetectionList) {
        this.sourceDetectionList = sourceDetectionList;
    }

    public RawmaterialStock getRawmaterialStock() {
        return rawmaterialStock;
    }

    public void setRawmaterialStock(RawmaterialStock rawmaterialStock) {
        this.rawmaterialStock = rawmaterialStock;
    }

    public ProductStore getProductStore() {
        return productStore;
    }

    public void setProductStore(ProductStore productStore) {
        this.productStore = productStore;
    }

    public MaterialCheck getMaterialCheck() {
        return materialCheck;
    }

    public void setMaterialCheck(MaterialCheck materialCheck) {
        this.materialCheck = materialCheck;
    }

    public SinglePayableDetail getSinglePayableDetail() {
        return singlePayableDetail;
    }

    public void setSinglePayableDetail(SinglePayableDetail singlePayableDetail) {
        this.singlePayableDetail = singlePayableDetail;
    }

    public List<RawmaterialStock> getRawmaterialStockList() {
        return rawmaterialStockList;
    }

    public void setRawmaterialStockList(List<RawmaterialStock> rawmaterialStockList) {
        this.rawmaterialStockList = rawmaterialStockList;
    }

    public List<EgProcess> getProcessList() {
        return processList;
    }

    public void setProcessList(List<EgProcess> processList) {
        this.processList = processList;
    }

    public List<ProductStore> getProductStoreList() {
        return productStoreList;
    }

    public void setProductStoreList(List<ProductStore> productStoreList) {
        this.productStoreList = productStoreList;
    }

    public List<Discard> getDiscardList() {
        return discardList;
    }

    public void setDiscardList(List<Discard> discardList) {
        this.discardList = discardList;
    }

    public List<Inspect> getInspectList() {
        return inspectList;
    }

    public void setInspectList(List<Inspect> inspectList) {
        this.inspectList = inspectList;
    }

    public CollectMuchMoney getCollectMuchMoney() {
        return collectMuchMoney;
    }

    public void setCollectMuchMoney(CollectMuchMoney collectMuchMoney) {
        this.collectMuchMoney = collectMuchMoney;
    }

    public Integer getSeqNum() {
        return seqNum;
    }

    public void setSeqNum(Integer seqNum) {
        this.seqNum = seqNum;
    }

    public RejectApplication getRejectApplication() {
        return rejectApplication;
    }

    public void setRejectApplication(RejectApplication rejectApplication) {
        this.rejectApplication = rejectApplication;
    }

    public String getDesireTreatment() {
        return desireTreatment;
    }

    public void setDesireTreatment(String desireTreatment) {
        this.desireTreatment = desireTreatment;
    }

    public PuReturnsDetail getPuReturnsDetail() {
        return puReturnsDetail;
    }

    public void setPuReturnsDetail(PuReturnsDetail puReturnsDetail) {
        this.puReturnsDetail = puReturnsDetail;
    }
}
