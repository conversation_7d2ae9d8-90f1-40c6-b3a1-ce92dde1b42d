package com.kyb.pcberp.modules.finance.dao;

import java.util.List;

import com.kyb.pcberp.modules.finance.entity.PayMoney;
import com.kyb.pcberp.modules.hr.finance_center.pojo.Hr_ReportData;
import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.finance.entity.SinglePayable;
import com.kyb.pcberp.modules.finance.entity.SinglePayableDetail;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;

@MyBatisDao
public interface SinglePayableDetailDao
{
    List<SinglePayableDetail> getDetailList(SinglePayable singlePayable);
    
    void cleanDetail(SinglePayable singlePayable);
    
    void batchInsert(SinglePayable singlePayable);
    
    SinglePayableDetail getSingleDetailByPurOrderId(@Param("companyId") String companyId, @Param("type") String type,
        @Param("orderDetailId") String orderDetailId);
    
    void updateConfirmAmount(SinglePayableDetail detail);
    
    void batchUpdateConfirmAmount(SinglePayable singlePayable);
    
    Integer checkDeailCount(RawmaterialStock raw);

    SinglePayableDetail getSinglePayableDetail(@Param("recordId") String recordId);

    List<SinglePayableDetail> getReceivableList();

    SinglePayableDetail getSinglePayableDetailData(@Param("recordId") String recordId);

    void updateStatus(SinglePayableDetail detail);

    void batchInsertSingleDetailCollect(PayMoney payMoney);
}
