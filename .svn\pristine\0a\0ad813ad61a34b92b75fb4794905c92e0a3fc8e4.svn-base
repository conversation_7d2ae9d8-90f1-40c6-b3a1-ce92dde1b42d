package com.kyb.pcberp.modules.report.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.contract.entity.MaxBatchAreaConfirm;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class CapacityDeail extends DataEntity<CapacityDeail> {
    private static final long serialVersionUID = 1L;

    private String deailId; // 批次id

    private BigDecimal deailArea; // 使用产能

    private Date proTime; // 安排生产时间

    private Date firstWorkTime; // 本批次可最早生产时间

    private Date endWorkTime; // 本批次可最晚生产时间

    private String departId; // 本产能所属部门id

    private String customerId; // 本产能所属客户id

    private String processValueId; // 本产能所属订单类型

    private String versionDate; // 本产能所有版本日期

    private String companyId; // 工厂id

    private String noficationId; // 通知单id

    private String contactDeailId; // 合同明细id

    private String customerName;

    private String departName;

    private String contractNo;

    private String customerPo; // 客户订单号

    private String craftNo; // 生产编号

    private BigDecimal dayAreas; // 该所属当日提供产能

    private Integer orderNum; // 该所属当日提供款数

    private String showDays;

    private String addDays;

    private String factoryId;

    private Date deliveryDate;

    private Date estimateDate;

    private String useFlag;

    private String groupCenterId;

    private Date orgProTime;

    private String shortName;

    private String finalCustName;

    private String finalCustId;

    private String finalSalesman;

    private String finalSalesmanId;

    private String cardType;

    private BigDecimal capacity;

    private Integer status; // 1假期0非假期

    private Date minDate;

    private Integer sampleOrderNum; // 样品款数

    private Integer newOrderNum; // 新单款数

    private Integer day; // 查询天数

    private Date estimateStartDate;

    private BigDecimal purchRowDays;

    private BigDecimal notiNeedDays;

    private String orderType;

    private String materialType;

    private BigDecimal partCapacity;

    private String checked;

    private List<CapacityDeail> list;

    private Date proTimeCopy;

    private Date acDistributeDate;

    private String feedId;

    private String feedNo;

    private String orderStatus;

    private String exportData;

    private Date startDate;

    private Date endDate;

    private String customerPoQuery;

    private String craftNoQuery;

    private List<MaxBatchAreaConfirm> confirmList;

    private BigDecimal feedArea;

    private BigDecimal deliveryArea;

    private String notiNo;

    private Integer productionNum; // 量产款数

    private String dateOne; // 最大面积日期

    private String dateTwo; // 最大新单款数日期

    private String dateThree; // 最大样品款数日期

    private String dateFour; // 最大量产款数日期

    private BigDecimal mantissaArea; // 尾数面积

    private BigDecimal balanceArea; // 在线结存面积

    private String oldDeailId;

    private String produceBatchDetailId;

    private Integer count;

    private String yearMonth;

    private List<CapacityDeail> capacityDeailList;

    private BigDecimal beforeSumArea;

    private BigDecimal nextSumArea;

    private String currentMonth;

    private String lastMonth;

    private String nextMonth;

    private BigDecimal capacityArea;

    private Integer capacityCount;

    private BigDecimal orderArea;

    private Integer changeDateCount;

    private BigDecimal beforMonthOrderArea;

    private Integer beforMonthCount;

    private BigDecimal nowMonthOrderArea;

    private Integer nowMonthCount;

    private BigDecimal icloudUseArea;

    private Integer icloudUseCount;

    private BigDecimal currentUseArea;

    private Integer currentUseCount;

    private BigDecimal beforUseArea;

    private Integer beforUseCount;

    private String number;

    //实际送货
    private BigDecimal actualArea;

    private String actualCount;

    //当期送货
    private BigDecimal currentArea;

    private String currentCount;

    private String customerModel; // 客户型号

    private Date orderDate; // 订单日期

    private String referenceTypeValue; // 订单类型

    private String deliveryDateTwo;

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    private String proTimeT;

    public String getStatusStr() {
        if(StringUtils.isBlank(orderStatus))
        {
            return null;
        }
        if (orderStatus.equals("200204")) {
            return "已结单";
        }
        if (StringUtils.isNotBlank(feedId)){
            if (orderStatus.equals("200207")) {
                return "生产中";
            }
        }else {
            if (status != null){
                return "已确认";
            }else {
                return "待确认";
            }
        }
        return "";
    }

    public String getDeailId() {
        return deailId;
    }

    public void setDeailId(String deailId) {
        this.deailId = deailId;
    }

    public BigDecimal getDeailArea() {
        return deailArea;
    }

    public void setDeailArea(BigDecimal deailArea) {
        this.deailArea = deailArea;
    }

    public Date getProTime() {
        return proTime;
    }

    public String getProTimeStr() {
        if (proTime != null) {
            return DateUtils.formatDate(proTime);
        }
        return "";
    }

    public void setProTime(Date proTime) {
        this.proTime = proTime;
    }

    public Date getFirstWorkTime() {
        return firstWorkTime;
    }

    public void setFirstWorkTime(Date firstWorkTime) {
        this.firstWorkTime = firstWorkTime;
    }

    public Date getEndWorkTime() {
        return endWorkTime;
    }

    public void setEndWorkTime(Date endWorkTime) {
        this.endWorkTime = endWorkTime;
    }

    public String getDepartId() {
        return departId;
    }

    public void setDepartId(String departId) {
        this.departId = departId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getProcessValueId() {
        return processValueId;
    }

    public void setProcessValueId(String processValueId) {
        this.processValueId = processValueId;
    }

    public String getProcessValueStr()
    {
        String result = null;
        if(StringUtils.isBlank(processValueId))
        {
            return result;
        }
        if("1".equals(processValueId))
        {
            result = "曝光";
        }
        else if("2".equals(processValueId))
        {
            result = "丝印";
        }
        return result;
    }

    public String getVersionDate() {
        return versionDate;
    }

    public void setVersionDate(String versionDate) {
        this.versionDate = versionDate;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getNoficationId() {
        return noficationId;
    }

    public void setNoficationId(String noficationId) {
        this.noficationId = noficationId;
    }

    public String getContactDeailId() {
        return contactDeailId;
    }

    public void setContactDeailId(String contactDeailId) {
        this.contactDeailId = contactDeailId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getCustomerPo() {
        return customerPo;
    }

    public void setCustomerPo(String customerPo) {
        this.customerPo = customerPo;
    }

    public String getCraftNo() {
        return craftNo;
    }

    public void setCraftNo(String craftNo) {
        this.craftNo = craftNo;
    }

    public BigDecimal getDayAreas() {
        return dayAreas;
    }

    public void setDayAreas(BigDecimal dayAreas) {
        this.dayAreas = dayAreas;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getShowDays() {
        return showDays;
    }

    public void setShowDays(String showDays) {
        this.showDays = showDays;
    }

    public String getAddDays() {
        return addDays;
    }

    public void setAddDays(String addDays) {
        this.addDays = addDays;
    }

    public String getFactoryId() {
        return factoryId;
    }

    public void setFactoryId(String factoryId) {
        this.factoryId = factoryId;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Date getEstimateDate() {
        return estimateDate;
    }

    public void setEstimateDate(Date estimateDate) {
        this.estimateDate = estimateDate;
    }

    public String getDeliveryDateStr() {
        if (deliveryDate != null) {
            return DateUtils.formatDate(deliveryDate);
        }
        return "";
    }

    public String getEstimateDateStr() {
        if (estimateDate != null) {
            return DateUtils.formatDate(estimateDate);
        }
        return "";
    }

    public String getUseFlag() {
        return useFlag;
    }

    public void setUseFlag(String useFlag) {
        this.useFlag = useFlag;
    }

    public String getUseFlagStr()
    {
        String result = null;
        if(StringUtils.isBlank(useFlag))
        {
            return result;
        }
        if("1".equals(useFlag))
        {
            result = "量产";
        }
        else if("2".equals(useFlag))
        {
            result = "样品";
        }else if("3".equals(useFlag))
        {
            result = "加急";
        }
        return result;
    }

    public String getGroupCenterId() {
        return groupCenterId;
    }

    public void setGroupCenterId(String groupCenterId) {
        this.groupCenterId = groupCenterId;
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getOrgProTime() {
        return orgProTime;
    }

    public String getOrgProTimeStr() {
        if (orgProTime != null) {
            return DateUtils.formatDate(orgProTime);
        }
        return "";
    }

    public void setOrgProTime(Date orgProTime) {
        this.orgProTime = orgProTime;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getFinalCustName() {
        return finalCustName;
    }

    public void setFinalCustName(String finalCustName) {
        this.finalCustName = finalCustName;
    }

    public String getFinalCustId() {
        return finalCustId;
    }

    public void setFinalCustId(String finalCustId) {
        this.finalCustId = finalCustId;
    }

    public String getFinalSalesman() {
        return finalSalesman;
    }

    public void setFinalSalesman(String finalSalesman) {
        this.finalSalesman = finalSalesman;
    }

    public String getFinalSalesmanId() {
        return finalSalesmanId;
    }

    public void setFinalSalesmanId(String finalSalesmanId) {
        this.finalSalesmanId = finalSalesmanId;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public BigDecimal getCapacity() {
        return capacity;
    }

    public void setCapacity(BigDecimal capacity) {
        this.capacity = capacity;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getMinDate() {
        return minDate;
    }

    public String getMinDateStr() {
        if (minDate != null) {
            return DateUtils.formatDate(minDate);
        }
        return "";
    }

    public void setMinDate(Date minDate) {
        this.minDate = minDate;
    }

    public Integer getSampleOrderNum() {
        return sampleOrderNum;
    }

    public void setSampleOrderNum(Integer sampleOrderNum) {
        this.sampleOrderNum = sampleOrderNum;
    }

    public Integer getNewOrderNum() {
        return newOrderNum;
    }

    public void setNewOrderNum(Integer newOrderNum) {
        this.newOrderNum = newOrderNum;
    }

    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public Date getEstimateStartDate() {
        return estimateStartDate;
    }

    public String getEstimateStartDateStr() {
        if (estimateStartDate != null) {
            return DateUtils.formatDate(estimateStartDate);
        }
        return "";
    }

    public void setEstimateStartDate(Date estimateStartDate) {
        this.estimateStartDate = estimateStartDate;
    }

    public BigDecimal getPurchRowDays() {
        return purchRowDays;
    }

    public void setPurchRowDays(BigDecimal purchRowDays) {
        this.purchRowDays = purchRowDays;
    }

    public BigDecimal getNotiNeedDays() {
        return notiNeedDays;
    }

    public void setNotiNeedDays(BigDecimal notiNeedDays) {
        this.notiNeedDays = notiNeedDays;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOrderTypeStr()
    {
        String result = null;
        if(StringUtils.isBlank(orderType))
        {
            return result;
        }
        if("1".equals(orderType))
        {
            result = "样品";
        }
        else if("2".equals(orderType))
        {
            result = "量产";
        }
        return result;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public String getMaterialTypeStr()
    {
        String result = null;
        if(StringUtils.isBlank(materialType))
        {
            return result;
        }
        if("1".equals(materialType))
        {
            result = "新单";
        }
        else if("2".equals(materialType))
        {
            result = "返单";
        }
        return result;
    }

    public BigDecimal getPartCapacity() {
        return partCapacity;
    }

    public void setPartCapacity(BigDecimal partCapacity) {
        this.partCapacity = partCapacity;
    }

    public String getChecked() {
        return checked;
    }

    public void setChecked(String checked) {
        this.checked = checked;
    }

    public List<CapacityDeail> getList() {
        return list;
    }

    public void setList(List<CapacityDeail> list) {
        this.list = list;
    }

    public Date getProTimeCopy() {
        return proTimeCopy;
    }

    public String getProTimeCopyStr() {
        if (proTimeCopy != null) {
            return DateUtils.formatDate(proTimeCopy);
        }
        return "";
    }

    public void setProTimeCopy(Date proTimeCopy) {
        this.proTimeCopy = proTimeCopy;
    }

    public Date getAcDistributeDate() {
        return acDistributeDate;
    }

    public String getAcDistributeDateStr() {
        if (acDistributeDate != null) {
            return DateUtils.formatDate(acDistributeDate);
        }
        return "";
    }

    public void setAcDistributeDate(Date acDistributeDate) {
        this.acDistributeDate = acDistributeDate;
    }

    public String getFeedId() {
        return feedId;
    }

    public void setFeedId(String feedId) {
        this.feedId = feedId;
    }

    public String getFeedNo() {
        return feedNo;
    }

    public void setFeedNo(String feedNo) {
        this.feedNo = feedNo;
    }

    public CapacityDeail clone() {
        CapacityDeail o = null;
        try {
            o = (CapacityDeail) super.clone();
        } catch (CloneNotSupportedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } // Object 中的clone()识别出你要复制的是哪一个对象。
        return o;
    }
    public String getExportData()
    {
        return exportData;
    }

    public void setExportData(String exportData)
    {
        this.exportData = exportData;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getCustomerPoQuery() {
        return customerPoQuery;
    }

    public void setCustomerPoQuery(String customerPoQuery) {
        this.customerPoQuery = customerPoQuery;
    }

    public String getCraftNoQuery() {
        return craftNoQuery;
    }

    public void setCraftNoQuery(String craftNoQuery) {
        this.craftNoQuery = craftNoQuery;
    }

    public List<MaxBatchAreaConfirm> getConfirmList() {
        return confirmList;
    }

    public void setConfirmList(List<MaxBatchAreaConfirm> confirmList) {
        this.confirmList = confirmList;
    }

    public BigDecimal getFeedArea() {
        return feedArea;
    }

    public void setFeedArea(BigDecimal feedArea) {
        this.feedArea = feedArea;
    }

    public BigDecimal getDeliveryArea() {
        return deliveryArea;
    }

    public void setDeliveryArea(BigDecimal deliveryArea) {
        this.deliveryArea = deliveryArea;
    }

    public String getNotiNo() {
        return notiNo;
    }

    public void setNotiNo(String notiNo) {
        this.notiNo = notiNo;
    }

    public Integer getProductionNum() {
        return productionNum;
    }

    public void setProductionNum(Integer productionNum) {
        this.productionNum = productionNum;
    }

    public String getDateOne() {
        return dateOne;
    }

    public void setDateOne(String dateOne) {
        this.dateOne = dateOne;
    }

    public String getDateTwo() {
        return dateTwo;
    }

    public void setDateTwo(String dateTwo) {
        this.dateTwo = dateTwo;
    }

    public String getDateThree() {
        return dateThree;
    }

    public void setDateThree(String dateThree) {
        this.dateThree = dateThree;
    }

    public String getDateFour() {
        return dateFour;
    }

    public void setDateFour(String dateFour) {
        this.dateFour = dateFour;
    }

    public BigDecimal getMantissaArea() {
        return mantissaArea;
    }

    public void setMantissaArea(BigDecimal mantissaArea) {
        this.mantissaArea = mantissaArea;
    }

    public BigDecimal getBalanceArea() {
        return balanceArea;
    }

    public void setBalanceArea(BigDecimal balanceArea) {
        this.balanceArea = balanceArea;
    }

    public String getOldDeailId()
    {
        return oldDeailId;
    }

    public void setOldDeailId(String oldDeailId)
    {
        this.oldDeailId = oldDeailId;
    }

    public String getProduceBatchDetailId()
    {
        return produceBatchDetailId;
    }

    public void setProduceBatchDetailId(String produceBatchDetailId)
    {
        this.produceBatchDetailId = produceBatchDetailId;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public List<CapacityDeail> getCapacityDeailList() {
        return capacityDeailList;
    }

    public void setCapacityDeailList(List<CapacityDeail> capacityDeailList) {
        this.capacityDeailList = capacityDeailList;
    }

    public BigDecimal getBeforeSumArea() {
        return beforeSumArea;
    }

    public void setBeforeSumArea(BigDecimal beforeSumArea) {
        this.beforeSumArea = beforeSumArea;
    }

    public BigDecimal getNextSumArea() {
        return nextSumArea;
    }

    public void setNextSumArea(BigDecimal nextSumArea) {
        this.nextSumArea = nextSumArea;
    }

    public String getCurrentMonth() {
        return currentMonth;
    }

    public void setCurrentMonth(String currentMonth) {
        this.currentMonth = currentMonth;
    }

    public String getLastMonth() {
        return lastMonth;
    }

    public void setLastMonth(String lastMonth) {
        this.lastMonth = lastMonth;
    }

    public String getNextMonth() {
        return nextMonth;
    }

    public void setNextMonth(String nextMonth) {
        this.nextMonth = nextMonth;
    }

    public String getCreatedDateStr() {
        if (createdDate != null) {
            return DateUtils.formatDate(createdDate);
        }
        return "";
    }

    public BigDecimal getCapacityArea() {
        return capacityArea;
    }

    public void setCapacityArea(BigDecimal capacityArea) {
        this.capacityArea = capacityArea;
    }

    public Integer getCapacityCount() {
        return capacityCount;
    }

    public void setCapacityCount(Integer capacityCount) {
        this.capacityCount = capacityCount;
    }

    public BigDecimal getOrderArea() {
        return orderArea;
    }

    public void setOrderArea(BigDecimal orderArea) {
        this.orderArea = orderArea;
    }

    public Integer getChangeDateCount() {
        return changeDateCount;
    }

    public void setChangeDateCount(Integer changeDateCount) {
        this.changeDateCount = changeDateCount;
    }

    public BigDecimal getBeforMonthOrderArea() {
        return beforMonthOrderArea;
    }

    public void setBeforMonthOrderArea(BigDecimal beforMonthOrderArea) {
        this.beforMonthOrderArea = beforMonthOrderArea;
    }

    public Integer getBeforMonthCount() {
        return beforMonthCount;
    }

    public void setBeforMonthCount(Integer beforMonthCount) {
        this.beforMonthCount = beforMonthCount;
    }

    public BigDecimal getNowMonthOrderArea() {
        return nowMonthOrderArea;
    }

    public void setNowMonthOrderArea(BigDecimal nowMonthOrderArea) {
        this.nowMonthOrderArea = nowMonthOrderArea;
    }

    public Integer getNowMonthCount() {
        return nowMonthCount;
    }

    public void setNowMonthCount(Integer nowMonthCount) {
        this.nowMonthCount = nowMonthCount;
    }

    public BigDecimal getIcloudUseArea() {
        return icloudUseArea;
    }

    public void setIcloudUseArea(BigDecimal icloudUseArea) {
        this.icloudUseArea = icloudUseArea;
    }

    public Integer getIcloudUseCount() {
        return icloudUseCount;
    }

    public void setIcloudUseCount(Integer icloudUseCount) {
        this.icloudUseCount = icloudUseCount;
    }

    public BigDecimal getCurrentUseArea() {
        return currentUseArea;
    }

    public void setCurrentUseArea(BigDecimal currentUseArea) {
        this.currentUseArea = currentUseArea;
    }

    public Integer getCurrentUseCount() {
        return currentUseCount;
    }

    public void setCurrentUseCount(Integer currentUseCount) {
        this.currentUseCount = currentUseCount;
    }

    public BigDecimal getBeforUseArea() {
        return beforUseArea;
    }

    public void setBeforUseArea(BigDecimal beforUseArea) {
        this.beforUseArea = beforUseArea;
    }

    public Integer getBeforUseCount() {
        return beforUseCount;
    }

    public void setBeforUseCount(Integer beforUseCount) {
        this.beforUseCount = beforUseCount;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public BigDecimal getActualArea() {
        return actualArea;
    }

    public void setActualArea(BigDecimal actualArea) {
        this.actualArea = actualArea;
    }

    public String getActualCount() {
        return actualCount;
    }

    public void setActualCount(String actualCount) {
        this.actualCount = actualCount;
    }

    public BigDecimal getCurrentArea() {
        return currentArea;
    }

    public void setCurrentArea(BigDecimal currentArea) {
        this.currentArea = currentArea;
    }

    public String getCurrentCount() {
        return currentCount;
    }

    public void setCurrentCount(String currentCount) {
        this.currentCount = currentCount;
    }

    public String getProTimeT() {
        return proTimeT;
    }

    public void setProTimeT(String proTimeT) {
        this.proTimeT = proTimeT;
    }

    public String getCustomerModel()
    {
        return customerModel;
    }

    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderDateStr()
    {
        if (orderDate != null)
        {
            return DateUtils.formatDate(orderDate);
        }
        return "";
    }

    public String getReferenceTypeValue() {
        return referenceTypeValue;
    }

    public void setReferenceTypeValue(String referenceTypeValue) {
        this.referenceTypeValue = referenceTypeValue;
    }

    public String getDeliveryDateTwo() {
        return deliveryDateTwo;
    }

    public void setDeliveryDateTwo(String deliveryDateTwo) {
        this.deliveryDateTwo = deliveryDateTwo;
    }
}
