const information = {
	template:'#information',
	created () {
		this.$store.dispatch('myStore/setUserInformation')
	},
	computed: {
		userInformation:{
			get () {
				return this.$store.state.myStore.userInformation
		    }
		},
		produceFile:{
			get () {
				return this.$store.state.produceStore.produceFile
			}
		}
	},
	watch:{
		userInformation:function(){
			$('#loadingModal').modal()
			this.$store.dispatch('produceStore/setFileUrl')
		},
		produceFile:function()
		{
			$('#loadingModal').modal('hide')
		}
	},
}