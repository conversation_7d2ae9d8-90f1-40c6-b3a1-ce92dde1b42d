'use strict';
angular.module("ngLocale", [], ["$provide", function($provide) {
var PLURAL_CATEGORY = {ZERO: "zero", ONE: "one", TWO: "two", FEW: "few", MANY: "many", OTHER: "other"};
function getDecimals(n) {
  n = n + '';
  var i = n.indexOf('.');
  return (i == -1) ? 0 : n.length - i - 1;
}

function getVF(n, opt_precision) {
  var v = opt_precision;

  if (undefined === v) {
    v = Math.min(getDecimals(n), 3);
  }

  var base = Math.pow(10, v);
  var f = ((n * base) | 0) % base;
  return {v: v, f: f};
}

$provide.value("$locale", {
  "DATETIME_FORMATS": {
    "AMPMS": [
      "\u05e4\u05d0\u05e8\u05de\u05d9\u05d8\u05d0\u05d2",
      "\u05e0\u05d0\u05db\u05de\u05d9\u05d8\u05d0\u05d2"
    ],
    "DAY": [
      "\u05d6\u05d5\u05e0\u05d8\u05d9\u05e7",
      "\u05de\u05d0\u05b8\u05e0\u05d8\u05d9\u05e7",
      "\u05d3\u05d9\u05e0\u05e1\u05d8\u05d9\u05e7",
      "\u05de\u05d9\u05d8\u05d5\u05d5\u05d0\u05da",
      "\u05d3\u05d0\u05e0\u05e2\u05e8\u05e9\u05d8\u05d9\u05e7",
      "\u05e4\u05bf\u05e8\u05f2\u05b7\u05d8\u05d9\u05e7",
      "\u05e9\u05d1\u05ea"
    ],
    "ERANAMES": [
      "BCE",
      "CE"
    ],
    "ERAS": [
      "BCE",
      "CE"
    ],
    "FIRSTDAYOFWEEK": 0,
    "MONTH": [
      "\u05d9\u05d0\u05b7\u05e0\u05d5\u05d0\u05b7\u05e8",
      "\u05e4\u05bf\u05e2\u05d1\u05e8\u05d5\u05d0\u05b7\u05e8",
      "\u05de\u05e2\u05e8\u05e5",
      "\u05d0\u05b7\u05e4\u05bc\u05e8\u05d9\u05dc",
      "\u05de\u05d9\u05d9",
      "\u05d9\u05d5\u05e0\u05d9",
      "\u05d9\u05d5\u05dc\u05d9",
      "\u05d0\u05d5\u05d9\u05d2\u05d5\u05e1\u05d8",
      "\u05e1\u05e2\u05e4\u05bc\u05d8\u05e2\u05de\u05d1\u05e2\u05e8",
      "\u05d0\u05e7\u05d8\u05d0\u05d1\u05e2\u05e8",
      "\u05e0\u05d0\u05d5\u05d5\u05e2\u05de\u05d1\u05e2\u05e8",
      "\u05d3\u05e2\u05e6\u05e2\u05de\u05d1\u05e2\u05e8"
    ],
    "SHORTDAY": [
      "\u05d6\u05d5\u05e0\u05d8\u05d9\u05e7",
      "\u05de\u05d0\u05b8\u05e0\u05d8\u05d9\u05e7",
      "\u05d3\u05d9\u05e0\u05e1\u05d8\u05d9\u05e7",
      "\u05de\u05d9\u05d8\u05d5\u05d5\u05d0\u05da",
      "\u05d3\u05d0\u05e0\u05e2\u05e8\u05e9\u05d8\u05d9\u05e7",
      "\u05e4\u05bf\u05e8\u05f2\u05b7\u05d8\u05d9\u05e7",
      "\u05e9\u05d1\u05ea"
    ],
    "SHORTMONTH": [
      "\u05d9\u05d0\u05b7\u05e0\u05d5\u05d0\u05b7\u05e8",
      "\u05e4\u05bf\u05e2\u05d1\u05e8\u05d5\u05d0\u05b7\u05e8",
      "\u05de\u05e2\u05e8\u05e5",
      "\u05d0\u05b7\u05e4\u05bc\u05e8\u05d9\u05dc",
      "\u05de\u05d9\u05d9",
      "\u05d9\u05d5\u05e0\u05d9",
      "\u05d9\u05d5\u05dc\u05d9",
      "\u05d0\u05d5\u05d9\u05d2\u05d5\u05e1\u05d8",
      "\u05e1\u05e2\u05e4\u05bc\u05d8\u05e2\u05de\u05d1\u05e2\u05e8",
      "\u05d0\u05e7\u05d8\u05d0\u05d1\u05e2\u05e8",
      "\u05e0\u05d0\u05d5\u05d5\u05e2\u05de\u05d1\u05e2\u05e8",
      "\u05d3\u05e2\u05e6\u05e2\u05de\u05d1\u05e2\u05e8"
    ],
    "WEEKENDRANGE": [
      5,
      6
    ],
    "fullDate": "EEEE, d\u05d8\u05df MMMM y",
    "longDate": "d\u05d8\u05df MMMM y",
    "medium": "d\u05d8\u05df MMM y HH:mm:ss",
    "mediumDate": "d\u05d8\u05df MMM y",
    "mediumTime": "HH:mm:ss",
    "short": "dd/MM/yy HH:mm",
    "shortDate": "dd/MM/yy",
    "shortTime": "HH:mm"
  },
  "NUMBER_FORMATS": {
    "CURRENCY_SYM": "$",
    "DECIMAL_SEP": ".",
    "GROUP_SEP": ",",
    "PATTERNS": [
      {
        "gSize": 3,
        "lgSize": 3,
        "maxFrac": 3,
        "minFrac": 0,
        "minInt": 1,
        "negPre": "-",
        "negSuf": "",
        "posPre": "",
        "posSuf": ""
      },
      {
        "gSize": 3,
        "lgSize": 3,
        "maxFrac": 2,
        "minFrac": 2,
        "minInt": 1,
        "negPre": "\u00a4\u00a0-",
        "negSuf": "",
        "posPre": "\u00a4\u00a0",
        "posSuf": ""
      }
    ]
  },
  "id": "yi-001",
  "pluralCat": function(n, opt_precision) {  var i = n | 0;  var vf = getVF(n, opt_precision);  if (i == 1 && vf.v == 0) {    return PLURAL_CATEGORY.ONE;  }  return PLURAL_CATEGORY.OTHER;}
});
}]);
