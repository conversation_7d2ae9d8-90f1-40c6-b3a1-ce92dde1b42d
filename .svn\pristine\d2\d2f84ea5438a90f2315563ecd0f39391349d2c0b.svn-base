<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isErrorPage="true"%>
<div class="p-2">
	<div class="row pt-2 pb-2 alert-light border-bottom border-bottom-secondary">
		<div class="col text-center text-primary">
			<h5>出差申请单</h5>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col text-left font-weight-bold text-primary" v-if="comList && comList.length > 0 && comList[0].name">
			提交人：{{comList[0].name}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			来&emsp;&emsp;源
			<select class="custom-select form-control h-32px" v-model="company">
				<option v-for="item in comList" :key="item.recordId" :value="item">
					{{item.departmentName}}
				</option>
			</select>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			出差地点
			<input class="custom-select form-control h-32px" v-model="audit.travelAddress">
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			开始时间
			<startDate></startDate>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			结束时间
			<endDate></endDate>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			出差天数
			<input class="custom-select form-control h-32px" v-model="audit.days">
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			预算费用
			<input class="custom-select form-control h-32px" v-model="audit.expensePrice" v-on:change="setAuditData">
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			出差事由
			<textarea class="form-control" v-model="audit.auditResult"></textarea>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">图片列表</div>
	</div>
	<div v-for="(item,index) in localIds">
		<div class="row alert-light pt-2 pb-2 align-items-center">
			<div class="col">
				<img :src="item" class="w-25" v-on:click="preview(item)">
			</div>
			<div class="col-2">
				<button class="btn btn-sm btn-danger" v-on:click="deleteImg(index)">删除</button>
			</div>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col"><button class="btn btn-primary" v-on:click="chooseImg">上传图片</button></div>
	</div>
	<div class="row fixed-bottom bg-light pt-2 pb-2">
		<div class="col">
			<button class="btn btn-secondary w-100" v-on:click="cancle">取消</button>
		</div>
		<div class="col">
			<button class="btn btn-primary w-100" v-on:click="submit">提交</button>
		</div>
	</div>
</div>