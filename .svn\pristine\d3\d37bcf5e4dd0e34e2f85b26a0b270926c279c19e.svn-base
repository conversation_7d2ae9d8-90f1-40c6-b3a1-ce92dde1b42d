<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.sys.dao.CockpitSheetDao">
    <select id="departList" resultType="CockpitSheet">
        SELECT
            amount,
            area,
            money,
            costOrder,
            pcsQty,
            price,
            module,
            datetime,
            modulType,
            departId,
            terminal
        FROM
            cockpit_department_sheet
        WHERE
            activeFlag = 1
        AND companyId = #{company.recordId}
    </select>
    <select id="customertList" resultType="CockpitSheet" >
        SELECT
            amount,
            area,
            money,
            costOrder,
            pcsQty,
            price,
            module,
            datetime,
            modulType,
            customId,
            terminal
        FROM
            cockpit_customer_sheet
        WHERE
            activeFlag = 1
        AND companyId = #{company.recordId}
    </select>
    <select id="salesmanList" resultType="CockpitSheet">
        SELECT
            amount,
            area,
            money,
            costOrder,
            pcsQty,
            price,
            module,
            datetime,
            modulType,
            saleId,
            terminal
        FROM
            cockpit_salesman_sheet
        WHERE
            activeFlag = 1
        AND companyId = #{company.recordId}
    </select>
    <select id="supplierList" resultType="CockpitSheet">
        SELECT
            amount,
            area,
            money,
            costOrder,
            pcsQty,
            price,
            module,
            datetime,
            modulType,
            supplierId,
            terminal
        FROM
            cockpit_supplier_sheet
        WHERE
            activeFlag = 1
        AND companyId = #{company.recordId}
    </select>
    <update id="departDelete">
       UPDATE cockpit_department_sheet SET activeFlag = 0
       WHERE recordId = #{recordId}
    </update>

    <update id="customerDelete">
        UPDATE cockpit_customer_sheet SET activeFlag = 0
        WHERE recordId = #{recordId}
    </update>

    <update id="salesmanDelete">
        UPDATE cockpit_salesman_sheet SET activeFlag = 0
        WHERE recordId = #{recordId}
    </update>
    <update id="supplierDelete">
        UPDATE cockpit_supplier_sheet SET activeFlag = 0
        WHERE recordId = #{recordId}
    </update>

    <update id="departUpdate">
    UPDATE cockpit_department_sheet SET amount = #{amount},
    area = #{area},
    money = #{money},
    costOrder = #{costOrder},
    pcsQty = #{pcsQty},
    price = #{price},
    `module` = #{module},
    modulType = #{modulType}
    WHERE recordId = #{recordId}
    </update>
    <update id="customerUpdate">
    UPDATE cockpit_customer_sheet SET amount = #{amount},
    area = #{area},
    money = #{money},
    costOrder = #{costOrder},
    pcsQty = #{pcsQty},
    price = #{price},
    `module` = #{module},
    modulType = #{modulType}
    WHERE recordId = #{recordId}

    </update>

    <update id="salesmanUpdate">
    UPDATE cockpit_salesman_sheet SET amount = #{amount},
    area = #{area},
    money = #{money},
    costOrder = #{costOrder},
    pcsQty = #{pcsQty},
    price = #{price},
    `module` = #{module},
    modulType = #{modulType}
    WHERE recordId = #{recordId}

    </update>

    <update id="supplierUpdate">
    UPDATE cockpit_supplier_sheet SET amount = #{amount},
    area = #{area},
    money = #{money},
    costOrder = #{costOrder},
    pcsQty = #{pcsQty},
    price = #{price},
    `module` = #{module},
    modulType = #{modulType}
    WHERE recordId = #{recordId}

    </update>

    <insert id="departInsert">
        INSERT INTO cockpit_department_sheet(
			companyId,
			amount,
			area,
			money,
			costOrder,
			pcsQty,
			price,
			`module`,
			modulType,
			datetime,
			activeflag,
			createdby,
			createddate,
			lastupdby,
			lastupddate,
			remark,
			departId,
			terminal
		) VALUES (
			#{company.recordId},
			#{amount},
			#{area},
			#{money},
			#{costOrder},
			#{pcsQty},
			#{price},
			#{module},
			#{modulType},
			#{datetime},
			1
			#{createdBy.recordId},
			NOW(),
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{departId},
			#{terminal}
		)
    </insert>

    <insert id="customerInsert">
        INSERT INTO cockpit_customer_sheet(
			companyId,
			amount,
			area,
			money,
			costOrder,
			pcsQty,
			price,
			`module`,
			modulType,
			datetime,
			activeflag,
			createdby,
			createddate,
			lastupdby,
			lastupddate,
			remark,
			customId,
			terminal
		) VALUES (
			#{company.recordId},
			#{amount},
			#{area},
			#{money},
			#{costOrder},
			#{pcsQty},
			#{price},
			#{module},
			#{modulType},
			#{datetime},
			1,
			#{createdBy.recordId},
			NOW(),
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{customId},
			#{terminal}
		)
    </insert>

    <insert id="salesmanInsert">
        INSERT INTO cockpit_salesman_sheet(
			companyId,
			amount,
			area,
			money,
			costOrder,
			pcsQty,
			price,
			`module`,
			modulType,
			datetime,
			activeflag,
			createdby,
			createddate,
			lastupdby,
			lastupddate,
			remark,
			saleId,
			terminal
		) VALUES (
			#{company.recordId},
			#{amount},
			#{area},
			#{money},
			#{costOrder},
			#{pcsQty},
			#{price},
			#{module},
			#{modulType},
			#{datetime},
			1,
			#{createdBy.recordId},
			NOW(),
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{saleId},
			#{terminal}
		)
    </insert>

    <insert id="supplierInsert">
        INSERT INTO cockpit_supplier_sheet(
			companyId,
			amount,
			area,
			money,
			costOrder,
			pcsQty,
			price,
			`module`,
			modulType,
			datetime,
			activeflag,
			createdby,
			createddate,
			lastupdby,
			lastupddate,
			remark,
			supplierId,
			terminal
		) VALUES (
			#{company.recordId},
			#{amount},
			#{area},
			#{money},
			#{costOrder},
			#{pcsQty},
			#{price},
			#{module},
			#{modulType},
			#{datetime},
			1,
			#{createdBy.recordId},
			NOW(),
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{supplierId},
			#{terminal}
		)
    </insert>


    <delete id="departClean">
		DELETE FROM cockpit_department_sheet WHERE activeFlag = 1
		<if test="initDate != null and initDate != ''">
			AND datetime >= #{initDate}
		</if>
		<if test="startDate != null and startDate != ''">
			AND datetime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND datetime <![CDATA[<]]> #{endDate}
		</if>
    </delete>

    <delete id="customerClean">
		DELETE FROM cockpit_customer_sheet  WHERE activeFlag = 1
		<if test="initDate != null and initDate != ''">
			AND datetime >= #{initDate}
		</if>
		<if test="startDate != null and startDate != ''">
			AND datetime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND datetime <![CDATA[<]]> #{endDate}
		</if>
    </delete>

    <delete id="salesmanClean">
		DELETE FROM cockpit_salesman_sheet WHERE activeFlag = 1
		<if test="initDate != null and initDate != ''">
			AND datetime >= #{initDate}
		</if>
		<if test="startDate != null and startDate != ''">
			AND datetime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND datetime <![CDATA[<]]> #{endDate}
		</if>
    </delete>

    <delete id="supplierClean">
		DELETE FROM cockpit_supplier_sheet WHERE activeFlag = 1
		<if test="initDate != null and initDate != ''">
			AND datetime >= #{initDate}
		</if>
		<if test="startDate != null and startDate != ''">
			AND datetime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND datetime <![CDATA[<]]> #{endDate}
		</if>
    </delete>

    <select id="getDeptData" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			SUM(CASE a.operatingMode WHEN 1 THEN 1 WHEN 2 THEN -1 ELSE 0 END) AS "amount",
			SUM(IFNULL(a.area,0)) AS "area",
			SUM(IFNULL(a.subTotal,0)) AS "money",
			SUM(IFNULL(a.costExpense,0)) AS "costOrder",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(a.price,0)) AS "price",
			a.module AS "module",
			DATE(a.operationTime) AS "datetime",
			a.moduleType AS "modulType",
			IFNULL(igor.groupOrgId,mmmp.deptId) AS "departId",
			CASE a.moduleType WHEN 1 THEN CASE WHEN scd5.recordId IS NULL OR b.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 2 THEN CASE WHEN scd5.recordId IS NULL OR scd1.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 3 THEN CASE WHEN scd5.recordId IS NULL OR scd1.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 4 THEN 1
			WHEN 5 THEN CASE WHEN scd5.recordId IS NULL OR scd2.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 6 THEN CASE WHEN scd5.recordId IS NULL OR scd2.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 7 THEN CASE WHEN scd5.recordId IS NULL OR scd4.recordId = scd5.recordId THEN 1 ELSE 2 END
			ELSE NULL END AS terminal
		FROM operating_record a
		LEFT JOIN sl_contract_detail b ON b.recordId = a.moduleId AND a.moduleType = 1
		LEFT JOIN st_product_store c ON c.recordId = a.moduleId AND a.moduleType = 2
		LEFT JOIN sl_singledetail_collect d ON d.recordId = a.moduleId AND a.moduleType = 3
		LEFT JOIN pu_purchasing_detail e ON e.recordId = a.moduleId AND a.moduleType = 4
		LEFT JOIN pu_prdorder_detail f ON f.recordId = a.moduleId AND a.moduleType = 5
		LEFT JOIN st_reject_application g ON g.recordId = a.moduleId AND a.moduleType = 6
		LEFT JOIN qc_inspect h ON h.recordId = a.moduleId AND a.moduleType = 7
		LEFT JOIN sl_single_receivable_detail ssrd ON ssrd.recordId = d.detailId
		LEFT JOIN sl_goods_check sgc ON sgc.recordId = ssrd.goodsCheckId
		LEFT JOIN sl_delivery_detail sdd ON sdd.recordId = sgc.sourceId AND sgc.inOutFlag = 1
		LEFT JOIN st_product_store spc ON spc.recordId = sgc.sourceId AND sgc.inOutFlag = 0
		LEFT JOIN sl_contract_detail scd1 ON scd1.recordId = IFNULL(c.contractDetailId,IFNULL(sdd.contractDetailId,spc.contractDetailId))
		LEFT JOIN sl_contract_detail scd2 ON scd2.recordId = IFNULL(f.contractDetailId,g.contractDetailId)
		LEFT JOIN sl_contract_detail scd3 ON scd3.inspectId = h.recordId AND scd3.activeFlag = 1
		LEFT JOIN sl_contract_detail scd4 ON scd4.recordId = IFNULL(scd3.oldDeailId,scd3.recordId)
		LEFT JOIN icloud_group_center igc ON igc.recordId = IFNULL(IFNULL(scd1.groupCenterId,IFNULL(scd2.groupCenterId,scd4.groupCenterId)),b.groupCenterId)
		LEFT JOIN sl_contract_detail scd5 ON scd5.recordId = IFNULL(IFNULL(igc.contactDeailId,IFNULL(scd1.groupCenterId,IFNULL(scd2.groupCenterId,scd4.groupCenterId))),b.groupCenterId)
		LEFT JOIN sl_contract sc ON sc.recordId = scd5.contractId
		LEFT JOIN icloud_group_org_relation igor ON igor.deptId = sc.deptId AND igor.activeFlag = 1
		LEFT JOIN pu_purchasing_apply ppa ON ppa.recordId = e.purchRawId
		LEFT JOIN md_market_material_preparation mmmp ON mmmp.recordId = ppa.matPreparationId
		WHERE a.activeFlag = 1 AND IFNULL(igor.groupOrgId,mmmp.deptId) IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND a.operationTime >= #{initDate}
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.operationTime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.operationTime <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,DATE(a.operationTime),IFNULL(igor.groupOrgId,mmmp.deptId),a.moduleType,terminal
	</select>

    <select id="getCustomerData" resultType="CockpitSheet">
        SELECT
            a.companyId AS "companyId",
            SUM(CASE a.operatingMode WHEN 1 THEN 1 WHEN 2 THEN -1 ELSE 0 END) AS "amount",
            SUM(IFNULL(a.area,0)) AS "area",
            SUM(IFNULL(a.subTotal,0)) AS "money",
            SUM(IFNULL(a.costExpense,0)) AS "costOrder",
            SUM(IFNULL(a.quantity,0)) AS "pcsQty",
            SUM(IFNULL(a.price,0)) AS "price",
            a.module AS "module",
            DATE(a.operationTime) AS "datetime",
            a.moduleType AS "modulType",
            mc.recordId AS "customId",
			CASE a.moduleType WHEN 1 THEN CASE WHEN scd5.recordId IS NULL OR b.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 2 THEN CASE WHEN scd5.recordId IS NULL OR scd1.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 3 THEN CASE WHEN scd5.recordId IS NULL OR scd1.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 4 THEN 1
			WHEN 5 THEN CASE WHEN scd5.recordId IS NULL OR scd2.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 6 THEN CASE WHEN scd5.recordId IS NULL OR scd2.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 7 THEN CASE WHEN scd5.recordId IS NULL OR scd4.recordId = scd5.recordId THEN 1 ELSE 2 END
			ELSE NULL END AS terminal
        FROM operating_record a
        LEFT JOIN sl_contract_detail b ON b.recordId = a.moduleId AND a.moduleType = 1
        LEFT JOIN st_product_store c ON c.recordId = a.moduleId AND a.moduleType = 2
        LEFT JOIN sl_singledetail_collect d ON d.recordId = a.moduleId AND a.moduleType = 3
        LEFT JOIN pu_prdorder_detail f ON f.recordId = a.moduleId AND a.moduleType = 5
        LEFT JOIN st_reject_application g ON g.recordId = a.moduleId AND a.moduleType = 6
        LEFT JOIN qc_inspect h ON h.recordId = a.moduleId AND a.moduleType = 7
		LEFT JOIN sl_single_receivable_detail ssrd ON ssrd.recordId = d.detailId
		LEFT JOIN sl_goods_check sgc ON sgc.recordId = ssrd.goodsCheckId
		LEFT JOIN sl_delivery_detail sdd ON sdd.recordId = sgc.sourceId AND sgc.inOutFlag = 1
		LEFT JOIN st_product_store spc ON spc.recordId = sgc.sourceId AND sgc.inOutFlag = 0
		LEFT JOIN sl_contract_detail scd1 ON scd1.recordId = IFNULL(c.contractDetailId,IFNULL(sdd.contractDetailId,spc.contractDetailId))
		LEFT JOIN sl_contract_detail scd2 ON scd2.recordId = IFNULL(f.contractDetailId,g.contractDetailId)
		LEFT JOIN sl_contract_detail scd3 ON scd3.inspectId = h.recordId AND scd3.activeFlag = 1
		LEFT JOIN sl_contract_detail scd4 ON scd4.recordId = IFNULL(scd3.oldDeailId,scd3.recordId)
		LEFT JOIN icloud_group_center igc ON igc.recordId = IFNULL(IFNULL(scd1.groupCenterId,IFNULL(scd2.groupCenterId,scd4.groupCenterId)),b.groupCenterId)
		LEFT JOIN sl_contract_detail scd5 ON scd5.recordId = IFNULL(IFNULL(igc.contactDeailId,IFNULL(scd1.groupCenterId,IFNULL(scd2.groupCenterId,scd4.groupCenterId))),b.groupCenterId)
		LEFT JOIN sl_contract sc ON sc.recordId = scd5.contractId
        LEFT JOIN md_customer mc ON mc.recordId = sc.customerId
        WHERE a.activeFlag = 1 AND mc.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND a.operationTime >= #{initDate}
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.operationTime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.operationTime <![CDATA[<]]> #{endDate}
		</if>
        GROUP BY a.companyId,DATE(a.operationTime),mc.recordId,a.moduleType,terminal
    </select>

    <select id="getSaleData" resultType="CockpitSheet">
        SELECT
			a.companyId AS "companyId",
			SUM(CASE a.operatingMode WHEN 1 THEN 1 WHEN 2 THEN -1 ELSE 0 END) AS "amount",
			SUM(IFNULL(a.area,0)) AS "area",
			SUM(IFNULL(a.subTotal,0)) AS "money",
			SUM(IFNULL(a.costExpense,0)) AS "costOrder",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(a.price,0)) AS "price",
			a.module AS "module",
			DATE(a.operationTime) AS "datetime",
			a.moduleType AS "modulType",
			su.recordId AS "saleId",
			CASE a.moduleType WHEN 1 THEN CASE WHEN scd5.recordId IS NULL OR b.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 2 THEN CASE WHEN scd5.recordId IS NULL OR scd1.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 3 THEN CASE WHEN scd5.recordId IS NULL OR scd1.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 4 THEN 1
			WHEN 5 THEN CASE WHEN scd5.recordId IS NULL OR scd2.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 6 THEN CASE WHEN scd5.recordId IS NULL OR scd2.recordId = scd5.recordId THEN 1 ELSE 2 END
			WHEN 7 THEN CASE WHEN scd5.recordId IS NULL OR scd4.recordId = scd5.recordId THEN 1 ELSE 2 END
			ELSE NULL END AS terminal
		FROM operating_record a
		LEFT JOIN sl_contract_detail b ON b.recordId = a.moduleId AND a.moduleType = 1
		LEFT JOIN st_product_store c ON c.recordId = a.moduleId AND a.moduleType = 2
		LEFT JOIN sl_singledetail_collect d ON d.recordId = a.moduleId AND a.moduleType = 3
		LEFT JOIN pu_prdorder_detail f ON f.recordId = a.moduleId AND a.moduleType = 5
		LEFT JOIN st_reject_application g ON g.recordId = a.moduleId AND a.moduleType = 6
		LEFT JOIN qc_inspect h ON h.recordId = a.moduleId AND a.moduleType = 7
		LEFT JOIN sl_single_receivable_detail ssrd ON ssrd.recordId = d.detailId
		LEFT JOIN sl_goods_check sgc ON sgc.recordId = ssrd.goodsCheckId
		LEFT JOIN sl_delivery_detail sdd ON sdd.recordId = sgc.sourceId AND sgc.inOutFlag = 1
		LEFT JOIN st_product_store spc ON spc.recordId = sgc.sourceId AND sgc.inOutFlag = 0
		LEFT JOIN sl_contract_detail scd1 ON scd1.recordId = IFNULL(c.contractDetailId,IFNULL(sdd.contractDetailId,spc.contractDetailId))
		LEFT JOIN sl_contract_detail scd2 ON scd2.recordId = IFNULL(f.contractDetailId,g.contractDetailId)
		LEFT JOIN sl_contract_detail scd3 ON scd3.inspectId = h.recordId AND scd3.activeFlag = 1
		LEFT JOIN sl_contract_detail scd4 ON scd4.recordId = IFNULL(scd3.oldDeailId,scd3.recordId)
		LEFT JOIN icloud_group_center igc ON igc.recordId = IFNULL(IFNULL(scd1.groupCenterId,IFNULL(scd2.groupCenterId,scd4.groupCenterId)),b.groupCenterId)
		LEFT JOIN sl_contract_detail scd5 ON scd5.recordId = IFNULL(IFNULL(igc.contactDeailId,IFNULL(scd1.groupCenterId,IFNULL(scd2.groupCenterId,scd4.groupCenterId))),b.groupCenterId)
		LEFT JOIN sl_contract sc ON sc.recordId = scd5.contractId
		LEFT JOIN md_customer mc ON mc.recordId = sc.customerId
		LEFT JOIN sm_user su ON su.recordId = IFNULL(sc.userId,mc.salesman)
		WHERE a.activeFlag = 1 AND su.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND a.operationTime >= #{initDate}
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.operationTime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.operationTime <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,DATE(a.operationTime),su.recordId,a.moduleType,terminal
    </select>

    <select id="getSupplierData" resultType="CockpitSheet">
        SELECT
			a.companyId AS "companyId",
			SUM(CASE a.operatingMode WHEN 1 THEN 1 WHEN 2 THEN -1 ELSE 0 END) AS "amount",
			SUM(IFNULL(a.area,0)) AS "area",
			SUM(IFNULL(a.subTotal,0)) AS "money",
			SUM(IFNULL(a.costExpense,0)) AS "costOrder",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(a.price,0)) AS "price",
			a.module AS "module",
			DATE(a.operationTime) AS "datetime",
			a.moduleType AS "modulType",
			sup.recordId AS "supplierId",
			CASE a.moduleType WHEN 4 THEN 1
			WHEN 5 THEN CASE WHEN g.recordId = i.recordId THEN 1 ELSE 2 END
			ELSE NULL END terminal
		FROM operating_record a
		LEFT JOIN pu_purchasing_detail e ON e.recordId = a.moduleId AND a.moduleType = 4
		LEFT JOIN pu_prdorder_detail f ON f.recordId = a.moduleId AND a.moduleType = 5
		LEFT JOIN pu_purchasing pp1 ON pp1.recordId = e.purchasingId
		LEFT JOIN pu_prdorder pp2 ON pp2.recordId = f.priceId
		LEFT JOIN md_supplier sup ON sup.recordId = IFNULL(pp1.supplierId,pp2.supplierId)
		LEFT JOIN sl_contract_detail g ON g.recordId = f.contractDetailId
		LEFT JOIN icloud_group_center h ON h.recordId = g.groupCenterId
		LEFT JOIN sl_contract_detail i ON i.recordId = IFNULL(h.contactDeailId,g.groupCenterId)
		WHERE a.activeFlag = 1 AND sup.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND a.operationTime >= #{initDate}
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.operationTime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.operationTime <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,DATE(a.operationTime),sup.recordId,a.moduleType,terminal
    </select>

   <insert id="batchInsertDept">
        INSERT INTO cockpit_department_sheet(
			companyId,
			amount,
			area,
			money,
			costOrder,
			pcsQty,
			price,
			`module`,
			modulType,
			datetime,
			activeflag,
			createdby,
			createddate,
			remark,
			departId,
	   		terminal
		) VALUES
		<foreach collection="list" item="item" separator=",">
        (
            #{item.companyId},
            #{item.amount},
            #{item.area},
            #{item.money},
            #{item.costOrder},
            #{item.pcsQty},
            #{item.price},
            #{item.module},
            #{item.modulType},
            #{item.datetime},
            1,
            NULL,
            NOW(),
            #{item.remark},
            #{item.departId},
            #{item.terminal}
        )
        </foreach>
    </insert>

   <insert id="batchInsertCustomer">
        INSERT INTO cockpit_customer_sheet(
			companyId,
			amount,
			area,
			money,
			costOrder,
			pcsQty,
			price,
			`module`,
			modulType,
			datetime,
			activeflag,
			createdby,
			createddate,
			remark,
			customId,
	   		terminal
		) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.companyId},
            #{item.amount},
            #{item.area},
            #{item.money},
            #{item.costOrder},
            #{item.pcsQty},
            #{item.price},
            #{item.module},
            #{item.modulType},
            #{item.datetime},
            1,
            NULL,
            NOW(),
            #{item.remark},
            #{item.customId},
            #{item.terminal}
        )
        </foreach>
    </insert>

    <insert id="batchInsertSale">
        INSERT INTO cockpit_salesman_sheet(
            companyId,
            amount,
            area,
            money,
            costOrder,
            pcsQty,
            price,
            `module`,
            modulType,
            datetime,
            activeflag,
            createdby,
            createddate,
            remark,
			saleId,
			terminal
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.companyId},
            #{item.amount},
            #{item.area},
            #{item.money},
            #{item.costOrder},
            #{item.pcsQty},
            #{item.price},
            #{item.module},
            #{item.modulType},
            #{item.datetime},
            1,
            NULL,
            NOW(),
            #{item.remark},
            #{item.saleId},
            #{item.terminal}
        )
        </foreach>
    </insert>


    <insert id="batchInsertSupplier">
        INSERT INTO cockpit_supplier_sheet(
            companyId,
            amount,
            area,
            money,
            costOrder,
            pcsQty,
            price,
            `module`,
            modulType,
            datetime,
            activeflag,
            createdby,
            createddate,
            remark,
			supplierId,
			terminal
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.companyId},
            #{item.amount},
            #{item.area},
            #{item.money},
            #{item.costOrder},
            #{item.pcsQty},
            #{item.price},
            #{item.module},
            #{item.modulType},
            #{item.datetime},
            1,
            NULL,
            NOW(),
            #{item.remark},
            #{item.supplierId},
            #{item.terminal}
            )
        </foreach>
    </insert>

	<select id="getDeptSheetList" resultType="CockpitSheet">
		SELECT
			*
		FROM cockpit_department_sheet
		WHERE activeflag = 1 AND modulType = #{modulType}
		<if test="initDate != null and initDate != ''">
			AND datetime >= #{initDate}
		</if>
		<if test="startDate != null and startDate != ''">
			AND datetime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND datetime <![CDATA[<]]> #{endDate}
		</if>
	</select>

	<select id="getCusSheetList" resultType="CockpitSheet">
		SELECT
			*
		FROM cockpit_customer_sheet
		WHERE activeflag = 1 AND modulType = #{modulType}
		<if test="initDate != null and initDate != ''">
			AND datetime >= #{initDate}
		</if>
		<if test="startDate != null and startDate != ''">
			AND datetime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND datetime <![CDATA[<]]> #{endDate}
		</if>
	</select>

	<select id="getSaleSheetList" resultType="CockpitSheet">
		SELECT
			*
		FROM cockpit_salesman_sheet
		WHERE activeflag = 1 AND modulType = #{modulType}
		<if test="initDate != null and initDate != ''">
			AND datetime >= #{initDate}
		</if>
		<if test="startDate != null and startDate != ''">
			AND datetime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND datetime <![CDATA[<]]> #{endDate}
		</if>
	</select>

	<select id="getDeptDataList" resultType="CockpitSheet">
		SELECT
			SUM(IFNULL(a.amount,0)) AS "amount",
			ROUND(SUM(IFNULL(a.area,0)),4) AS "area",
			ROUND(SUM(IFNULL(a.money,2)),4) AS "money",
			ROUND(SUM(IFNULL(a.costOrder,0)),2) AS "costOrder",
			SUM(IFNULL(a.pcsQty,0)) AS "pcsQty",
			ROUND(SUM(IFNULL(a.price,0)),6) AS "price",
			CASE #{dateType} WHEN  1 THEN Date(a.datetime)
			WHEN 2 THEN YEARWEEK(DATE(a.datetime),7)
			WHEN 3 THEN DATE_FORMAT(DATE(a.datetime),'%Y%m')
			ELSE NULL END AS "date",
			a.module,
			a.modulType,
			a.departId
		FROM cockpit_department_sheet a
		WHERE a.activeFlag = 1
		<if test="terminal != null and terminal != ''">
			AND a.terminal = #{terminal}
		</if>
		<if test="moduleType != null and moduleType != ''">
			AND FIND_IN_SET(a.modulType,#{moduleType})
		</if>
		<if test="groupOrgId != null and groupOrgId != ''">
			AND FIND_IN_SET(a.departId,#{groupOrgId})
		</if>
		<if test="dateType != null and dateType != ''">
			<!-- 日 -->
			<if test="dateType == 1">
				AND Date(a.datetime) > DATE_SUB(Date(NOW()),INTERVAL 3 DAY) AND  Date(a.datetime) <![CDATA[<=]]> Date(NOW())
				GROUP BY Date(a.datetime),a.modulType
			</if>
			<!-- 周 -->
			<if test="dateType == 2">
				AND YEARWEEK(DATE(a.datetime),7) > YEARWEEK(DATE_SUB(Date(NOW()),INTERVAL 21 DAY)) AND  YEARWEEK(DATE(a.datetime),7)  <![CDATA[<=]]> YEARWEEK(DATE(NOW()),7)
				GROUP BY YEARWEEK(DATE(a.datetime),7),a.modulType
			</if>
			<!-- 月 -->
			<if test="dateType == 3">
				AND DATE_FORMAT(DATE(a.datetime),'%Y%m') > DATE_FORMAT(DATE_SUB(DATE(NOW()),INTERVAL 3 MONTH),'%Y%m') AND DATE_FORMAT(DATE(a.datetime),'%Y%m') <![CDATA[<=]]>  DATE_FORMAT(DATE(NOW()),'%Y%m')
				GROUP BY DATE_FORMAT(DATE(a.datetime),'%Y%m'),a.modulType
			</if>
		</if>
	</select>
	<select id="getSalesDataList" resultType="CockpitSheet">
		SELECT
		SUM(IFNULL(a.amount,0)) AS "amount",
		ROUND(SUM(IFNULL(a.area,0)),4) AS "area",
		ROUND(SUM(IFNULL(a.money,2)),4) AS "money",
		ROUND(SUM(IFNULL(a.costOrder,0)),2) AS "costOrder",
		SUM(IFNULL(a.pcsQty,0)) AS "pcsQty",
		ROUND(SUM(IFNULL(a.price,0)),6) AS "price",
		CASE #{dateType} WHEN  1 THEN Date(a.datetime)
		WHEN 2 THEN YEARWEEK(DATE(a.datetime),7)
		WHEN 3 THEN DATE_FORMAT(DATE(a.datetime),'%Y%m')
		ELSE NULL END AS "date",
		a.module,
		a.modulType
		FROM cockpit_salesman_sheet a
		LEFT JOIN md_company b ON b.recordId = a.companyId
		LEFT JOIN sm_user c ON c.recordId = a.saleId
		LEFT JOIN md_customer d ON d.salesman = c.recordId AND d.activeFlag = 1 AND d.companyId = c.companyId
		LEFT JOIN icloud_group_org_relation e ON e.deptId = d.deptId AND e.activeFlag = 1
		LEFT JOIN icloud_group_org f ON f.recordId = e.groupOrgId
		WHERE a.activeFlag = 1
		<if test="companyId != null and companyId != '' and companyId != '所有'">
			AND b.recordId = #{companyId}
		</if>
		<if test="departId != null and departId != '' and departId != '所有'">
			AND e.groupOrgId = #{departId}
		</if>
		<if test="userName != null and userName != '' and userName != '所有'">
			AND c.userName = #{userName}
		</if>
		<if test="terminal != null and terminal != ''">
			AND a.terminal = #{terminal}
		</if>
		<if test="moduleType != null and moduleType != ''">
			AND FIND_IN_SET(a.modulType,#{moduleType})
		</if>
		<if test="groupOrgId != null and groupOrgId != ''">
			AND FIND_IN_SET(f.recordId,#{groupOrgId})
		</if>
		<if test="dateType != null and dateType != ''">
			<!-- 日 -->
			<if test="dateType == 1">
				AND Date(a.datetime) > DATE_SUB(Date(NOW()),INTERVAL 3 DAY) AND  Date(a.datetime) <![CDATA[<=]]> Date(NOW())
				GROUP BY Date(a.datetime),a.modulType
			</if>
			<!-- 周 -->
			<if test="dateType == 2">
				AND YEARWEEK(DATE(a.datetime),7) > YEARWEEK(DATE_SUB(Date(NOW()),INTERVAL 21 DAY)) AND  YEARWEEK(DATE(a.datetime),7)  <![CDATA[<=]]> YEARWEEK(DATE(NOW()),7)
				GROUP BY YEARWEEK(DATE(a.datetime),7),a.modulType
			</if>
			<!-- 月 -->
			<if test="dateType == 3">
				AND DATE_FORMAT(DATE(a.datetime),'%Y%m') > DATE_FORMAT(DATE_SUB(DATE(NOW()),INTERVAL 3 MONTH),'%Y%m') AND DATE_FORMAT(DATE(a.datetime),'%Y%m') <![CDATA[<=]]>  DATE_FORMAT(DATE(NOW()),'%Y%m')
				GROUP BY DATE_FORMAT(DATE(a.datetime),'%Y%m'),a.modulType
			</if>
		</if>

	</select>


	<select id="getDeptOrderList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(IFNULL(a.orderDeailArea,0)),4) AS "area",
			ROUND(SUM(IFNULL(a.subTotal,0)),2) AS "money",
			ROUND(SUM((CASE WHEN b.recordId IS NOT NULL THEN IFNULL(b.processFee,0) + IFNULL(b.materialFee,0) + ROUND(IFNULL(e.salePrecent,0) * IFNULL(a.subTotal,0) / 100,2)+ ROUND(IFNULL(f.parameterValue,0) * IFNULL(a.subTotal,0) / 100,2) ELSE IFNULL(IFNULL(a.prdQuantity,a.quantity),0) * IFNULL(a.madePcsPrice,0) END)+ IFNULL(a.prdEngineeringFee,0) + IFNULL(a.prdMouldFee,0) + IFNULL(a.prdTestShelfFee,0) + IFNULL(a.prdOthersFee,0) + IFNULL(a.prdFilmFee,0)),2)
			AS "costOrder",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(h.price,0)) AS "price",
			"订单" AS "module",
			1 AS "modulType",
			DATE(d.orderDate) AS "datetime",
			g.groupOrgId AS "departId",
			CASE WHEN a.recordId = c.recordId THEN 1 ELSE 2 END AS terminal
		FROM sl_contract_detail a
		LEFT JOIN icloud_group_center b ON b.recordId = a.groupCenterId
		LEFT JOIN sl_contract_detail c ON c.recordId = IFNULL(b.contactDeailId,a.groupCenterId)
		LEFT JOIN sl_contract d ON d.recordId = c.contractId
		LEFT JOIN md_customer e ON e.recordId = d.customerId
		LEFT JOIN md_parameterset f ON f.companyId = a.companyId AND f.jianPin = 'managePrecent'
		LEFT JOIN icloud_group_org_relation g ON g.deptId = d.deptId AND g.activeFlag = 1
		LEFT JOIN sl_price h ON h.recordId = a.priceId
		WHERE c.recordId IS NOT NULL AND a.activeFlag = 1 AND a.`status` <![CDATA[<>]]> 200205 AND g.groupOrgId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(d.orderDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND d.orderDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND d.orderDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,g.groupOrgId,DATE(d.orderDate),terminal
	</select>

	<select id="getCusOrderList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(IFNULL(a.orderDeailArea,0)),4) AS "area",
			ROUND(SUM(IFNULL(a.subTotal,0)),2) AS "money",
			ROUND(SUM((CASE WHEN b.recordId IS NOT NULL THEN IFNULL(b.processFee,0) + IFNULL(b.materialFee,0) + ROUND(IFNULL(e.salePrecent,0) * IFNULL(a.subTotal,0) / 100,2)+ ROUND(IFNULL(f.parameterValue,0) * IFNULL(a.subTotal,0) / 100,2) ELSE IFNULL(IFNULL(a.prdQuantity,a.quantity),0) * IFNULL(a.madePcsPrice,0) END)+ IFNULL(a.prdEngineeringFee,0) + IFNULL(a.prdMouldFee,0) + IFNULL(a.prdTestShelfFee,0) + IFNULL(a.prdOthersFee,0) + IFNULL(a.prdFilmFee,0)),2)
			AS "costOrder",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(h.price,0)) AS "price",
			"订单" AS "module",
			1 AS "modulType",
			DATE(d.orderDate) AS "datetime",
			e.recordId AS "customId",
			CASE WHEN a.recordId = c.recordId THEN 1 ELSE 2 END AS terminal
		FROM sl_contract_detail a
		LEFT JOIN icloud_group_center b ON b.recordId = a.groupCenterId
		LEFT JOIN sl_contract_detail c ON c.recordId = IFNULL(b.contactDeailId,a.groupCenterId)
		LEFT JOIN sl_contract d ON d.recordId = c.contractId
		LEFT JOIN md_customer e ON e.recordId = d.customerId
		LEFT JOIN md_parameterset f ON f.companyId = a.companyId AND f.jianPin = 'managePrecent'
		LEFT JOIN sl_price h ON h.recordId = a.priceId
		WHERE c.recordId IS NOT NULL AND a.activeFlag = 1 AND a.`status` <![CDATA[<>]]> 200205 AND e.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(d.orderDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND d.orderDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND d.orderDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,e.recordId,DATE(d.orderDate),terminal
	</select>

	<select id="getSaleOrderList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(IFNULL(a.orderDeailArea,0)),4) AS "area",
			ROUND(SUM(IFNULL(a.subTotal,0)),2) AS "money",
			ROUND(SUM((CASE WHEN b.recordId IS NOT NULL THEN IFNULL(b.processFee,0) + IFNULL(b.materialFee,0) + ROUND(IFNULL(e.salePrecent,0) * IFNULL(a.subTotal,0) / 100,2)+ ROUND(IFNULL(f.parameterValue,0) * IFNULL(a.subTotal,0) / 100,2) ELSE IFNULL(IFNULL(a.prdQuantity,a.quantity),0) * IFNULL(a.madePcsPrice,0) END)+ IFNULL(a.prdEngineeringFee,0) + IFNULL(a.prdMouldFee,0) + IFNULL(a.prdTestShelfFee,0) + IFNULL(a.prdOthersFee,0) + IFNULL(a.prdFilmFee,0)),2)
			AS "costOrder",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(h.price,0)) AS "price",
			"订单" AS "module",
			1 AS "modulType",
			DATE(d.orderDate) AS "datetime",
			i.recordId AS "saleId",
			CASE WHEN a.recordId = c.recordId THEN 1 ELSE 2 END AS terminal
		FROM sl_contract_detail a
		LEFT JOIN icloud_group_center b ON b.recordId = a.groupCenterId
		LEFT JOIN sl_contract_detail c ON c.recordId = IFNULL(b.contactDeailId,a.groupCenterId)
		LEFT JOIN sl_contract d ON d.recordId = c.contractId
		LEFT JOIN md_customer e ON e.recordId = d.customerId
		LEFT JOIN md_parameterset f ON f.companyId = a.companyId AND f.jianPin = 'managePrecent'
		LEFT JOIN sl_price h ON h.recordId = a.priceId
		LEFT JOIN sm_user i ON i.recordId = IFNULL(d.userId,e.salesman)
		WHERE c.recordId IS NOT NULL AND a.activeFlag = 1 AND a.`status` <![CDATA[<>]]> 200205 AND i.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(d.orderDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND d.orderDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND d.orderDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,i.recordId,DATE(d.orderDate),terminal
	</select>

    <select id="getDeptStoreList" resultType="CockpitSheet">
        SELECT
            a.companyId AS "companyId",
            COUNT(1) AS "amount",
            ROUND(SUM(c.pnlLength * c.pnlWidth * a.quantity / c.pnlDivisor / 1000000),4) AS "area",
            ROUND(SUM(IFNULL(a.quantity,0) * IFNULL(d.price,0)),2) AS "money",
            SUM(IFNULL(a.quantity,0)) AS "pcsQty",
            SUM(IFNULL(d.price,0)) AS "price",
            "出货" AS "module",
            2 AS "modulType",
            DATE(a.operateDate) AS "datetime",
            h.groupOrgId AS "departId",
            CASE WHEN b.recordId = f.recordId THEN 1 ELSE 2 END AS terminal
        FROM st_product_store a
        LEFT JOIN sl_contract_detail b ON b.recordId = a.contractDetailId
        LEFT JOIN sl_contract_craft c ON c.recordId = b.craftId
        LEFT JOIN sl_price d ON d.recordId = b.priceId
        LEFT JOIN icloud_group_center e ON e.recordId = b.groupCenterId
        LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(e.contactDeailId,b.groupCenterId)
        LEFT JOIN sl_contract g ON g.recordId = f.contractId
        LEFT JOIN icloud_group_org_relation h ON h.deptId = g.deptId AND h.activeFlag = 1
        WHERE a.activeFlag = 1 AND f.recordId IS NOT NULL AND a.inOutType = 12 AND h.groupOrgId IS NOT NULL
        <if test="initDate != null and initDate != ''">
            AND DATE(a.operateDate) >= DATE(#{initDate})
        </if>
		<if test="startDate != null and startDate != ''">
			AND a.operateDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.operateDate <![CDATA[<]]> #{endDate}
		</if>
        GROUP BY a.companyId,h.groupOrgId,DATE(a.operateDate),terminal
    </select>

    <select id="getCusStoreList" resultType="CockpitSheet">
        SELECT
            a.companyId AS "companyId",
            COUNT(1) AS "amount",
            ROUND(SUM(c.pnlLength * c.pnlWidth * a.quantity / c.pnlDivisor / 1000000),4) AS "area",
            ROUND(SUM(IFNULL(a.quantity,0) * IFNULL(d.price,0)),2) AS "money",
            SUM(IFNULL(a.quantity,0)) AS "pcsQty",
            SUM(IFNULL(d.price,0)) AS "price",
            "出货" AS "module",
            2 AS "modulType",
            DATE(a.operateDate) AS "datetime",
            h.recordId AS "customId",
            CASE WHEN b.recordId = f.recordId THEN 1 ELSE 2 END AS terminal
        FROM st_product_store a
        LEFT JOIN sl_contract_detail b ON b.recordId = a.contractDetailId
        LEFT JOIN sl_contract_craft c ON c.recordId = b.craftId
        LEFT JOIN sl_price d ON d.recordId = b.priceId
        LEFT JOIN icloud_group_center e ON e.recordId = b.groupCenterId
        LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(e.contactDeailId,b.groupCenterId)
        LEFT JOIN sl_contract g ON g.recordId = b.contractId
        LEFT JOIN md_customer h ON h.recordId = g.customerId
        WHERE a.activeFlag = 1 AND f.recordId IS NOT NULL AND a.inOutType = 12 AND h.recordId IS NOT NULL
        <if test="initDate != null and initDate != ''">
            AND DATE(a.operateDate) >= DATE(#{initDate})
        </if>
		<if test="startDate != null and startDate != ''">
			AND a.operateDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.operateDate <![CDATA[<]]> #{endDate}
		</if>
        GROUP BY a.companyId,h.recordId,DATE(a.operateDate),terminal
    </select>

    <select id="getSaleStoreList" resultType="CockpitSheet">
        SELECT
            a.companyId AS "companyId",
            COUNT(1) AS "amount",
            ROUND(SUM(c.pnlLength * c.pnlWidth * a.quantity / c.pnlDivisor / 1000000),4) AS "area",
            ROUND(SUM(IFNULL(a.quantity,0) * IFNULL(d.price,0)),2) AS "money",
            SUM(IFNULL(a.quantity,0)) AS "pcsQty",
            SUM(IFNULL(d.price,0)) AS "price",
            "出货" AS "module",
            2 AS "modulType",
            DATE(a.operateDate) AS "datetime",
            i.recordId AS "saleId",
            CASE WHEN b.recordId = f.recordId THEN 1 ELSE 2 END AS terminal
        FROM st_product_store a
        LEFT JOIN sl_contract_detail b ON b.recordId = a.contractDetailId
        LEFT JOIN sl_contract_craft c ON c.recordId = b.craftId
        LEFT JOIN sl_price d ON d.recordId = b.priceId
        LEFT JOIN icloud_group_center e ON e.recordId = b.groupCenterId
        LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(e.contactDeailId,b.groupCenterId)
        LEFT JOIN sl_contract g ON g.recordId = b.contractId
        LEFT JOIN md_customer h ON h.recordId = g.customerId
        LEFT JOIN sm_user i ON i.recordId = IFNULL(g.userId,h.salesman)
        WHERE a.activeFlag = 1 AND f.recordId IS NOT NULL AND a.inOutType = 12 AND i.recordId IS NOT NULL
        <if test="initDate != null and initDate != ''">
            AND DATE(a.operateDate) >= DATE(#{initDate})
        </if>
		<if test="startDate != null and startDate != ''">
			AND a.operateDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.operateDate <![CDATA[<]]> #{endDate}
		</if>
        GROUP BY a.companyId,i.recordId,DATE(a.operateDate),terminal
    </select>

	<select id="getDeptReturnedMoneyList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(IFNULL(a.amount,0)),2) AS "money",
			SUM(IFNULL(c.price,0)) AS "price",
			"回款" AS "module",
			3 AS "modulType",
			DATE(d.singleReceivableDate) AS "datetime",
			k.groupOrgId AS "departId",
			CASE WHEN g.recordId = i.recordId THEN 1 ELSE 2 END AS terminal
		FROM sl_singledetail_collect a
		LEFT JOIN sl_single_receivable_detail b ON b.recordId = a.detailId
		LEFT JOIN sl_goods_check c ON c.recordId = b.goodsCheckId
		LEFT JOIN sl_single_receivable d ON d.recordId = b.singleReceivableId
		LEFT JOIN sl_delivery_detail e ON e.recordId = c.sourceId AND c.inOutFlag = 1
		LEFT JOIN st_product_store f ON f.recordId = c.sourceId AND c.inOutFlag = 0
		LEFT JOIN sl_contract_detail g ON g.recordId = IFNULL(e.contractDetailId,f.contractDetailId)
		LEFT JOIN icloud_group_center h ON h.recordId = g.groupCenterId
		LEFT JOIN sl_contract_detail i ON i.recordId = IFNULL(h.contactDeailId,g.groupCenterId)
		LEFT JOIN sl_contract j ON j.recordId = i.contractId
		LEFT JOIN icloud_group_org_relation k ON k.deptId = j.deptId AND k.activeFlag = 1
		WHERE a.activeFlag = 1 AND i.recordId IS NOT NULL AND d.singleReceivableDate IS NOT NULL AND k.groupOrgId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(d.singleReceivableDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND DATE_FORMAT(d.singleReceivableDate ,'%Y%m')  = DATE_FORMAT(#{startDate} ,'%Y%m')
		</if>
<!--		<if test="endDate != null and endDate != ''">
			AND d.singleReceivableDate <![CDATA[<]]> #{endDate}
		</if>-->
		GROUP BY a.companyId,k.groupOrgId,DATE(d.singleReceivableDate),terminal
	</select>

	<select id="getCusReturnedMoneyList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(IFNULL(a.amount,0)),2) AS "money",
			SUM(IFNULL(c.price,0)) AS "price",
			"回款" AS "module",
			3 AS "modulType",
			DATE(d.singleReceivableDate) AS "datetime",
			k.recordId AS "customId",
			CASE WHEN g.recordId = i.recordId THEN 1 ELSE 2 END AS terminal
		FROM sl_singledetail_collect a
		LEFT JOIN sl_single_receivable_detail b ON b.recordId = a.detailId
		LEFT JOIN sl_goods_check c ON c.recordId = b.goodsCheckId
		LEFT JOIN sl_single_receivable d ON d.recordId = b.singleReceivableId
		LEFT JOIN sl_delivery_detail e ON e.recordId = c.sourceId AND c.inOutFlag = 1
		LEFT JOIN st_product_store f ON f.recordId = c.sourceId AND c.inOutFlag = 0
		LEFT JOIN sl_contract_detail g ON g.recordId = IFNULL(e.contractDetailId,f.contractDetailId)
		LEFT JOIN icloud_group_center h ON h.recordId = g.groupCenterId
		LEFT JOIN sl_contract_detail i ON i.recordId = IFNULL(h.contactDeailId,g.groupCenterId)
		LEFT JOIN sl_contract j ON j.recordId = i.contractId
		LEFT JOIN md_customer k ON k.recordId = j.customerId
		WHERE a.activeFlag = 1 AND i.recordId IS NOT NULL AND d.singleReceivableDate IS NOT NULL AND k.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(d.singleReceivableDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND d.singleReceivableDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND d.singleReceivableDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,k.recordId,DATE(d.singleReceivableDate),terminal
	</select>

	<select id="getSaleReturnedMoneyList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(IFNULL(a.amount,0)),2) AS "money",
			SUM(IFNULL(c.price,0)) AS "price",
			"回款" AS "module",
			3 AS "modulType",
			DATE(d.singleReceivableDate) AS "datetime",
			l.recordId AS "saleId",
			CASE WHEN g.recordId = i.recordId THEN 1 ELSE 2 END AS terminal
		FROM sl_singledetail_collect a
		LEFT JOIN sl_single_receivable_detail b ON b.recordId = a.detailId
		LEFT JOIN sl_goods_check c ON c.recordId = b.goodsCheckId
		LEFT JOIN sl_single_receivable d ON d.recordId = b.singleReceivableId
		LEFT JOIN sl_delivery_detail e ON e.recordId = c.sourceId AND c.inOutFlag = 1
		LEFT JOIN st_product_store f ON f.recordId = c.sourceId AND c.inOutFlag = 0
		LEFT JOIN sl_contract_detail g ON g.recordId = IFNULL(e.contractDetailId,f.contractDetailId)
		LEFT JOIN icloud_group_center h ON h.recordId = g.groupCenterId
		LEFT JOIN sl_contract_detail i ON i.recordId = IFNULL(h.contactDeailId,g.groupCenterId)
		LEFT JOIN sl_contract j ON j.recordId = i.contractId
		LEFT JOIN md_customer k ON k.recordId = j.customerId
		LEFT JOIN sm_user l ON l.recordId = IFNULL(j.userId,k.salesman)
		WHERE a.activeFlag = 1 AND i.recordId IS NOT NULL AND d.singleReceivableDate IS NOT NULL AND l.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(d.singleReceivableDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND d.singleReceivableDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND d.singleReceivableDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,l.recordId,DATE(d.singleReceivableDate),terminal
	</select>

	<select id="getDeptPurDetailList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(c.`value` * d.`value` * a.quantity / 1000000),4) AS "area",
			ROUND(SUM(IFNULL(a.amount,0)),2) AS "money",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(a.price,0)) AS "price",
			"原料采购" AS "module",
			4 AS "modulType",
			DATE(a.createdDate) AS "datetime",
			f.deptId AS "departId",
			1 AS terminal
		FROM pu_purchasing_detail a
		LEFT JOIN md_material b ON b.recordId = a.materialId
		LEFT JOIN md_material_specification_relation c ON c.materialId = b.recordId AND c.companyId = a.companyId
		AND c.specificationId = (SELECT recordId FROM md_material_specification WHERE companyId = a.companyId AND `name` = '长' AND activeFlag = 1 LIMIT 1)
		AND c.activeFlag = 1
		LEFT JOIN md_material_specification_relation d ON d.materialId = b.recordId AND d.companyId = a.companyId
		AND d.specificationId = (SELECT recordId FROM md_material_specification WHERE companyId = a.companyId AND `name` = '宽' AND activeFlag = 1 LIMIT 1)
		AND d.activeFlag = 1
		LEFT JOIN pu_purchasing_apply e ON e.recordId = a.purchRawId
		LEFT JOIN md_market_material_preparation f ON f.recordId = e.matPreparationId
		WHERE a.activeFlag = 1 AND a.`status` <![CDATA[<>]]> 500108 AND f.deptId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(a.createdDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.createdDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.createdDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,f.deptId,DATE(a.createdDate),terminal
	</select>

	<select id="getSupPurDetailList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(c.`value` * d.`value` * a.quantity / 1000000),4) AS "area",
			ROUND(SUM(IFNULL(a.amount,0)),2) AS "money",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(a.price,0)) AS "price",
			"原料采购" AS "module",
			4 AS "modulType",
			DATE(a.createdDate) AS "datetime",
			f.recordId AS "supplierId",
			1 AS terminal
		FROM pu_purchasing_detail a
		LEFT JOIN md_material b ON b.recordId = a.materialId
		LEFT JOIN md_material_specification_relation c ON c.materialId = b.recordId AND c.companyId = a.companyId
		AND c.specificationId = (SELECT recordId FROM md_material_specification WHERE companyId = a.companyId AND `name` = '长' AND activeFlag = 1 LIMIT 1)
		AND c.activeFlag = 1
		LEFT JOIN md_material_specification_relation d ON d.materialId = b.recordId AND d.companyId = a.companyId
		AND d.specificationId = (SELECT recordId FROM md_material_specification WHERE companyId = a.companyId AND `name` = '宽' AND activeFlag = 1 LIMIT 1)
		AND d.activeFlag = 1
		LEFT JOIN pu_purchasing e ON e.recordId = a.purchasingId
		LEFT JOIN md_supplier f ON f.recordId = e.supplierId
		WHERE a.activeFlag = 1 AND a.`status`<![CDATA[<>]]> 500108 AND f.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(a.createdDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.createdDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.createdDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,f.recordId,DATE(a.createdDate),terminal
	</select>

	<select id="getDeptPrdDetailList" resultType="CockpitSheet">
        SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(b.pnlLength * b.pnlWidth * a.quantity / b.pnlDivisor / 1000000),4) AS "area",
			ROUND(SUM(IFNULL(a.subTotal,0)),2) AS "money",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(c.price,0)) AS "price",
			"成品采购" AS "module",
			5 AS "modulType",
			DATE(a.createdDate) AS "datetime",
			h.groupOrgId AS "departId",
			CASE WHEN d.recordId = f.recordId THEN 1 ELSE 2 END AS terminal
        FROM pu_prdorder_detail a
        LEFT JOIN pu_craft b ON b.recordId = a.craftId
        LEFT JOIN pu_price c ON c.recordId = a.priceId
		LEFT JOIN sl_contract_detail d ON d.recordId = a.contractDetailId
		LEFT JOIN icloud_group_center e ON e.recordId = d.groupCenterId
		LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(e.contactDeailId,d.groupCenterId)
		LEFT JOIN sl_contract g ON g.recordId = f.contractId
		LEFT JOIN icloud_group_org_relation h ON h.deptId = g.deptId AND h.activeFlag = 1
        WHERE a.activeFlag = 1 AND h.groupOrgId IS NOT NULL AND f.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(a.createdDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.createdDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.createdDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,h.groupOrgId,DATE(a.createdDate),terminal
	</select>

	<select id="getCusPrdDetailList" resultType="CockpitSheet">
       SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(b.pnlLength * b.pnlWidth * a.quantity / b.pnlDivisor / 1000000),4) AS "area",
			ROUND(SUM(IFNULL(a.subTotal,0)),2) AS "money",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(c.price,0)) AS "price",
			"成品采购" AS "module",
			5 AS "modulType",
			DATE(a.createdDate) AS "datetime",
			h.recordId AS "customId",
			CASE WHEN d.recordId = f.recordId THEN 1 ELSE 2 END AS terminal
        FROM pu_prdorder_detail a
        LEFT JOIN pu_craft b ON b.recordId = a.craftId
        LEFT JOIN pu_price c ON c.recordId = a.priceId
		LEFT JOIN sl_contract_detail d ON d.recordId = a.contractDetailId
		LEFT JOIN icloud_group_center e ON e.recordId = d.groupCenterId
		LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(e.contactDeailId,d.groupCenterId)
		LEFT JOIN sl_contract g ON g.recordId = f.contractId
		LEFT JOIN md_customer h ON h.recordId = g.customerId
        WHERE a.activeFlag = 1 AND h.recordId IS NOT NULL AND f.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND a.createdDate >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.createdDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.createdDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,h.recordId,DATE(a.createdDate),terminal
	</select>

	<select id="getSalePrdDetailList" resultType="CockpitSheet">
        SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(b.pnlLength * b.pnlWidth * a.quantity / b.pnlDivisor / 1000000),4) AS "area",
			ROUND(SUM(IFNULL(a.subTotal,0)),2) AS "money",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(c.price,0)) AS "price",
			"成品采购" AS "module",
			5 AS "modulType",
			DATE(a.createdDate) AS "datetime",
			i.recordId AS "saleId",
			CASE WHEN d.recordId = f.recordId THEN 1 ELSE 2 END AS terminal
        FROM pu_prdorder_detail a
        LEFT JOIN pu_craft b ON b.recordId = a.craftId
        LEFT JOIN pu_price c ON c.recordId = a.priceId
		LEFT JOIN sl_contract_detail d ON d.recordId = a.contractDetailId
		LEFT JOIN icloud_group_center e ON e.recordId = d.groupCenterId
		LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(e.contactDeailId,d.groupCenterId)
		LEFT JOIN sl_contract g ON g.recordId = f.contractId
		LEFT JOIN md_customer h ON h.recordId = g.customerId
		LEFT JOIN sm_user i ON i.recordId = IFNULL(g.userId,h.salesman)
        WHERE a.activeFlag = 1 AND f.recordId IS NOT NULL AND i.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(a.createdDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.createdDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.createdDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,i.recordId,DATE(a.createdDate),terminal
	</select>

	<select id="getSupPrdDetailList" resultType="CockpitSheet">
		     SELECT
				a.companyId AS "companyId",
				COUNT(1) AS "amount",
				ROUND(SUM(b.pnlLength * b.pnlWidth * a.quantity / b.pnlDivisor / 1000000),4) AS "area",
				ROUND(SUM(IFNULL(a.subTotal,0)),2) AS "money",
				SUM(IFNULL(a.quantity,0)) AS "pcsQty",
				SUM(IFNULL(c.price,0)) AS "price",
				"成品采购" AS "module",
				5 AS "modulType",
				DATE(a.createdDate) AS "datetime",
				h.recordId AS "supplierId",
				CASE WHEN d.recordId = f.recordId THEN 1 ELSE 2 END AS terminal
        FROM pu_prdorder_detail a
        LEFT JOIN pu_craft b ON b.recordId = a.craftId
        LEFT JOIN pu_price c ON c.recordId = a.priceId
		LEFT JOIN sl_contract_detail d ON d.recordId = a.contractDetailId
		LEFT JOIN icloud_group_center e ON e.recordId = d.groupCenterId
		LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(e.contactDeailId,d.groupCenterId)
		LEFT JOIN pu_prdorder g ON g.recordId = a.prdorderId
		LEFT JOIN md_supplier h ON h.recordId = g.supplierId
        WHERE a.activeFlag = 1 AND h.recordId IS NOT NULL AND f.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(a.createdDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.createdDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.createdDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,h.recordId,DATE(a.createdDate),terminal
	</select>

	<select id="getDeptRejectList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(c.pnlLength * c.pnlWidth * a.quantity / c.pnlDivisor / 1000000),4) AS "area",
			ROUND(SUM(IFNULL(a.amount,0)),2) AS "money",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(d.price,0)) AS "price",
			"客诉" AS "module",
			6 AS "modulType",
			DATE(a.businessHappenTime) AS "datetime",
			h.groupOrgId AS "departId",
			CASE WHEN b.recordId = f.recordId THEN 1 ELSE 2 END AS terminal
        FROM st_reject_application a
        LEFT JOIN sl_contract_detail b ON b.recordId = a.contractDetailId
        LEFT JOIN sl_contract_craft c ON c.recordId = b.craftId
        LEFT JOIN sl_price d ON d.recordId = b.priceId
		LEFT JOIN icloud_group_center e ON e.recordId = b.groupCenterId
		LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(e.contactDeailId,b.groupCenterId)
		LEFT JOIN sl_contract g ON g.recordId = f.contractId
		LEFT JOIN icloud_group_org_relation h ON h.deptId = g.deptId AND h.activeFlag = 1
        WHERE a.activeFlag = 1 AND h.groupOrgId IS NOT NULL AND f.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(a.businessHappenTime) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.businessHappenTime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.businessHappenTime <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,h.groupOrgId,DATE(a.businessHappenTime),terminal
	</select>

	<select id="getCusRejectList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(c.pnlLength * c.pnlWidth * a.quantity / c.pnlDivisor / 1000000),4) AS "area",
			ROUND(SUM(IFNULL(a.amount,0)),2) AS "money",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(d.price,0)) AS "price",
			"客诉" AS "module",
			6 AS "modulType",
			DATE(a.businessHappenTime) AS "datetime",
			h.recordId AS "customId",
			CASE WHEN b.recordId = f.recordId THEN 1 ELSE 2 END AS terminal
        FROM st_reject_application a
        LEFT JOIN sl_contract_detail b ON b.recordId = a.contractDetailId
        LEFT JOIN sl_contract_craft c ON c.recordId = b.craftId
        LEFT JOIN sl_price d ON d.recordId = b.priceId
		LEFT JOIN icloud_group_center e ON e.recordId = b.groupCenterId
		LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(e.contactDeailId,b.groupCenterId)
		LEFT JOIN sl_contract g ON g.recordId = f.contractId
		LEFT JOIN md_customer h ON h.recordId = g.customerId
        WHERE a.activeFlag = 1 AND h.recordId IS NOT NULL AND f.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(a.businessHappenTime) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.businessHappenTime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.businessHappenTime <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,h.recordId,DATE(a.businessHappenTime),terminal
	</select>

	<select id="getSaleRejectList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(c.pnlLength * c.pnlWidth * a.quantity / c.pnlDivisor / 1000000),4) AS "area",
			ROUND(SUM(IFNULL(a.amount,0)),2) AS "money",
			SUM(IFNULL(a.quantity,0)) AS "pcsQty",
			SUM(IFNULL(d.price,0)) AS "price",
			"客诉" AS "module",
			6 AS "modulType",
			DATE(a.businessHappenTime) AS "datetime",
			i.recordId AS "saleId",
			CASE WHEN b.recordId = f.recordId THEN 1 ELSE 2 END AS terminal
        FROM st_reject_application a
        LEFT JOIN sl_contract_detail b ON b.recordId = a.contractDetailId
        LEFT JOIN sl_contract_craft c ON c.recordId = b.craftId
        LEFT JOIN sl_price d ON d.recordId = b.priceId
		LEFT JOIN icloud_group_center e ON e.recordId = b.groupCenterId
		LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(e.contactDeailId,b.groupCenterId)
		LEFT JOIN sl_contract g ON g.recordId = f.contractId
		LEFT JOIN md_customer h ON h.recordId = g.customerId
		LEFT JOIN sm_user i ON i.recordId = IFNULL(g.userId,h.salesman)
        WHERE a.activeFlag = 1 AND i.recordId IS NOT NULL AND f.recordId IS NOT NULL
		<if test="initDate != null and initDate != ''">
			AND DATE(a.businessHappenTime) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.businessHappenTime >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.businessHappenTime <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,i.recordId,DATE(a.businessHappenTime),terminal
	</select>

	<select id="getDeptInspectList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(c.setLength * c.setWidth * a.failedPcsQty / c.pnlDivisor / 1000000),4) AS "area",
			SUM(IFNULL(a.failedPcsQty,0)) AS "pcsQty",
			"品质" AS "module",
			7 AS "modulType",
			DATE(a.createdDate) AS "datetime",
			i.groupOrgId AS "departId",
			CASE WHEN e.recordId = g.recordId THEN 1 ELSE 2 END AS terminal
        FROM qc_inspect a
        LEFT JOIN pd_produce_batch b ON b.recordId = a.produceBatchId
        LEFT JOIN eg_carda c ON c.recordId = b.processCardAId
		LEFT JOIN sl_contract_detail d ON d.inspectId = a.recordId AND d.activeFlag = 1 AND d.companyId = a.companyId
		LEFT JOIN sl_contract_detail e ON e.recordId = IFNULL(d.oldDeailId,d.recordId)
		LEFT JOIN icloud_group_center f ON f.recordId = e.groupCenterId
		LEFT JOIN sl_contract_detail g ON g.recordId = IFNULL(f.contactDeailId,e.groupCenterId)
		LEFT JOIN sl_contract h ON h.recordId = g.contractId
		LEFT JOIN icloud_group_org_relation i ON i.deptId = h.deptId AND i.activeFlag = 1
        WHERE a.activeFlag = 1 AND i.groupOrgId IS NOT NULL AND g.recordId IS NOT NULL AND a.replenishMergeType IN(200601,200603)
		<if test="initDate != null and initDate != ''">
			AND DATE(a.createdDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.createdDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.createdDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,i.groupOrgId,DATE(a.createdDate),terminal
	</select>

	<select id="getCusInspectList" resultType="CockpitSheet">
			SELECT
				a.companyId AS "companyId",
				COUNT(1) AS "amount",
				ROUND(SUM(c.setLength * c.setWidth * a.failedPcsQty / c.pnlDivisor / 1000000),4) AS "area",
				SUM(IFNULL(a.failedPcsQty,0)) AS "pcsQty",
				"品质" AS "module",
				7 AS "modulType",
				DATE(a.createdDate) AS "datetime",
				i.recordId AS "customId",
				CASE WHEN e.recordId = g.recordId THEN 1 ELSE 2 END AS terminal
        FROM qc_inspect a
        LEFT JOIN pd_produce_batch b ON b.recordId = a.produceBatchId
        LEFT JOIN eg_carda c ON c.recordId = b.processCardAId
		LEFT JOIN sl_contract_detail d ON d.inspectId = a.recordId AND d.activeFlag = 1 AND d.companyId = a.companyId
		LEFT JOIN sl_contract_detail e ON e.recordId = IFNULL(d.oldDeailId,d.recordId)
		LEFT JOIN icloud_group_center f ON f.recordId = e.groupCenterId
		LEFT JOIN sl_contract_detail g ON g.recordId = IFNULL(f.contactDeailId,e.groupCenterId)
		LEFT JOIN sl_contract h ON h.recordId = g.contractId
		LEFT JOIN md_customer i ON i.recordId = h.customerId
        WHERE a.activeFlag = 1 AND i.recordId IS NOT NULL AND g.recordId IS NOT NULL AND a.replenishMergeType IN(200601,200603)
		<if test="initDate != null and initDate != ''">
			AND DATE(a.createdDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.createdDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.createdDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,i.recordId,DATE(a.createdDate),terminal
	</select>

	<select id="getSaleInspectList" resultType="CockpitSheet">
		SELECT
			a.companyId AS "companyId",
			COUNT(1) AS "amount",
			ROUND(SUM(c.setLength * c.setWidth * a.failedPcsQty / c.pnlDivisor / 1000000),4) AS "area",
			SUM(IFNULL(a.failedPcsQty,0)) AS "pcsQty",
			"品质" AS "module",
			7 AS "modulType",
			DATE(a.createdDate) AS "datetime",
			j.recordId AS "saleId",
			CASE WHEN e.recordId = g.recordId THEN 1 ELSE 2 END AS terminal
        FROM qc_inspect a
        LEFT JOIN pd_produce_batch b ON b.recordId = a.produceBatchId
        LEFT JOIN eg_carda c ON c.recordId = b.processCardAId
		LEFT JOIN sl_contract_detail d ON d.inspectId = a.recordId AND d.activeFlag = 1 AND d.companyId = a.companyId
		LEFT JOIN sl_contract_detail e ON e.recordId = IFNULL(d.oldDeailId,d.recordId)
		LEFT JOIN icloud_group_center f ON f.recordId = e.groupCenterId
		LEFT JOIN sl_contract_detail g ON g.recordId = IFNULL(f.contactDeailId,e.groupCenterId)
		LEFT JOIN sl_contract h ON h.recordId = g.contractId
		LEFT JOIN md_customer i ON i.recordId = h.customerId
		LEFT JOIN sm_user j ON j.recordId = IFNULL(h.userId,i.salesman)
        WHERE a.activeFlag = 1 AND j.recordId IS NOT NULL AND g.recordId IS NOT NULL AND a.replenishMergeType IN(200601,200603)
		<if test="initDate != null and initDate != ''">
			AND DATE(a.createdDate) >= DATE(#{initDate})
		</if>
		<if test="startDate != null and startDate != ''">
			AND a.createdDate >= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			AND a.createdDate <![CDATA[<]]> #{endDate}
		</if>
		GROUP BY a.companyId,j.recordId,DATE(a.createdDate),terminal

	</select>

	<select id="getWipData" resultType="CockpitSheet">
		SELECT
			a.recordId AS "produceBatchDetailId",
			CASE WHEN pbdc.recordId IS NOT NULL THEN  ROUND(pbdc.qtyPcsT * ROUND(cbpc.setLength * cbpc.setWidth,4) / cbpc.pnlDivisor / 1000000,4)
			ELSE ROUND (IFNULL(e.takeOverQtyPcsT,a.qtyPcsT) * ROUND(c.setLength * c.setWidth,4) / c.pnlDivisor / 1000000,4) END AS "area",
			IFNULL(IFNULL(e.takeOverQtyPcsT,a.qtyPcsT),0) AS "pcsQty",
			e.processId,
			REPLACE(IFNULL(s.category,p.processName)," ","") AS "processName",
			DATE_FORMAT(e.createdDate,'%H')+0 AS "hour",
			1 AS "workingCount",
			k.customerPo,
			CONCAT('A',b.recordId,'B',a.recordId) AS "batchNo",
			o.no AS "craftNo",
			o.customerModel AS "customerModel",
		    b.recordId AS "produceBatchId",
			g.processValueId,
			e.processManagementId,
			h.no AS "notiNo",
			q.orderType,
			r.seqNum,
			e.takeOverTime,
			e.recordId AS "produceRecordId",
			k.orderDate AS "orderDate",
			h.deliveryDate AS "deliveryDate"
		FROM pd_produce_batch_detail a
		LEFT JOIN pd_produce_batch b ON b.recordId = a.produceBatchId
		LEFT JOIN pd_feeding d ON d.recordId = b.feedingNo
		LEFT JOIN pd_produce_record e ON e.produceBatchDetailId = a.recordId AND e.produceBatchId = b.recordId AND e.companyId = a.companyId AND e.activeFlag = 1
		LEFT JOIN sl_notification h ON h.recordId = d.notificationId
		LEFT JOIN eg_carda c ON c.recordId = h.processCardAId
		LEFT JOIN pd_produce_batch_detail_craft pbdc ON pbdc.produceBatchId = b.recordId AND pbdc.produceBatchDetailId = a.recordId AND pbdc.companyId =
		a.companyId AND pbdc.activeFlag = 1
		LEFT JOIN eg_cardb_board_part_craft cbpc ON cbpc.processcardaid = c.recordId AND cbpc.notificationId = pbdc.notificationId
		AND cbpc.companyId = a.companyId
		AND cbpc.activeFlag = 1

		LEFT JOIN sl_notification f ON f.mergeId = h.recordId
		AND f.companyId = h.companyId
		AND f.activeFlag = 1
		LEFT JOIN sl_contract_detail g ON g.recordId = IFNULL(
		f.contractDetailId,
		h.contractDetailId
		)
		LEFT JOIN sl_contract_detail scd ON scd.recordId = g.oldDeailId
		LEFT JOIN icloud_group_center i ON i.recordId = IFNULL(
		scd.groupCenterId,
		g.groupCenterId
		)
		LEFT JOIN sl_contract_detail j ON j.recordId = i.contactDeailId
		LEFT JOIN sl_contract k ON k.recordId = j.contractId
		LEFT JOIN icloud_group_org_relation l ON l.deptId = k.deptId
		AND l.activeFlag = 1

		LEFT JOIN md_customer m ON m.recordId = k.customerId
		LEFT JOIN sm_user n ON n.recordId = IFNULL(k.userId, m.salesman)
		LEFT JOIN sl_contract_craft o ON o.recordId = j.craftId
		LEFT JOIN sl_process_management p ON p.recordId = e.processManagementId
		LEFT JOIN md_dict_value q ON q.recordId = j.referenceType
		LEFT JOIN eg_carda_process_value r ON r.processCardAId = b.processCardAId AND r.processId = e.processId AND r.activeFlag = 1
		LEFT JOIN eg_process s ON s.recordId = e.processId
		WHERE
		a.`status` IN (900301, 900302, 900303)
		AND b.`status` IN(900301, 900302, 900303)
		AND a.activeFlag = 1
		AND c.activeFlag = 1
		AND d.activeFlag = 1 AND d. STATUS IN (700102, 700105,700106)
		AND !ISNULL(d.productionSequence)
		AND d.productionSequence != ''
		AND (e.skipCount = 0 OR ISNULL(e.skipCount)) AND (e.handOverFlag IN(1, 2) OR e.recordId IS NULL)
		AND h.`status` IN (200404, 200405) AND g.processValueId IS NOT NULL AND IFNULL(a.qtyPcsT,0) - IFNULL(a.discardQty,0) > 0
		AND a.issueStatus = 1
		<if test="userName != null and userName != ''">
			AND n.userName = #{userName}
		</if>
		<if test="companyId != null and companyId != ''">
			AND a.companyId = #{companyId}
		</if>
		<if test="groupDeptList != null and groupDeptList != ''">
			AND (
				l.groupOrgId
			) IN
			<foreach collection="groupDeptList" item="item" index="index" open="(" close=")" separator=",">
				#{item.groupOrgId}
			</foreach>
		</if>
		GROUP BY a.recordId,pbdc.recordId,cbpc.recordId
		ORDER BY IFNULL(p.sequence,0)
	</select>

	<select id="getDepartListTwo" resultType="CockpitSheet">
		SELECT
			CASE #{showType} WHEN  1 THEN ROUND(IFNULL(a.area,0),4)
			WHEN 2 THEN IFNULL(a.amount,0)
			WHEN 3 THEN ROUND(IFNULL(a.money,0),2)
			WHEN 4 THEN IFNULL(a.pcsQty,0)
			ELSE NULL END AS "showValue",
			CASE #{dateType} WHEN  1 THEN Date(a.datetime)
			WHEN 2 THEN YEARWEEK(DATE(a.datetime),7)
			WHEN 3 THEN DATE_FORMAT(DATE(a.datetime),'%Y%m')
			ELSE NULL END AS "date",
			#{sentTimeStartQr} AS "sentTimeStartQr",
			#{sentTimeEndQr} AS "sentTimeEndQr"
		FROM cockpit_department_sheet a
		WHERE a.activeFlag = 1
		<if test="moduleType != null and moduleType != ''">
			AND FIND_IN_SET(a.modulType,#{moduleType})
		</if>
		<if test="groupOrgId != null and groupOrgId != ''">
			AND FIND_IN_SET(a.departId,#{groupOrgId})
		</if>
		<if test="dateType != null and dateType != ''">
			<!-- 日 -->
			<if test="dateType == 1">
				AND DATE(a.datetime) IN (${queryDateStrs})
			</if>
			<!-- 周 -->
			<if test="dateType == 2">
				AND YEARWEEK(DATE(a.datetime),7) IN (${queryDateStrs})
			</if>
			<!-- 月 -->
			<if test="dateType == 3">
				AND DATE_FORMAT(a.datetime,'%Y%m') IN (${queryDateStrs})
			</if>
			<!-- 自定义 -->
			<if test="dateType == 4">
				AND a.datetime >= #{sentTimeStartQr} AND a.datetime <![CDATA[<]]> #{sentTimeEndQr}
			</if>
		</if>
	</select>
	<select id="getDepartListTotalDetail" resultType="CockpitSheet">
		SELECT
		ROUND(IFNULL(a.area,0),4) AS "area",
		IFNULL(a.amount,0) AS "amount",
		ROUND(IFNULL(a.money,0),2) AS "money",
		IFNULL(a.pcsQty,0)AS "pcsQty",
		CASE #{dateType} WHEN  1 THEN Date(a.datetime)
		WHEN 2 THEN YEARWEEK(DATE(a.datetime),7)
		WHEN 3 THEN DATE_FORMAT(DATE(a.datetime),'%Y%m')
		ELSE Date(a.datetime) END AS "date",
		b.name AS "departName",
		#{sentTimeStartQr} AS "sentTimeStartQr",
		#{sentTimeEndQr} AS "sentTimeEndQr"
		FROM cockpit_department_sheet a
		LEFT JOIN icloud_group_org b ON b.recordId = a.departId
		WHERE a.activeFlag = 1
		<if test="terminal != null and terminal !=''">
			AND a.terminal = #{terminal}
		</if>
		<if test="moduleType != null and moduleType != ''">
			AND FIND_IN_SET(a.modulType,#{moduleType})
		</if>
		<if test="groupOrgId != null and groupOrgId != ''">
			AND FIND_IN_SET(a.departId,#{groupOrgId})
		</if>
		<if test="dateType != null and dateType != ''">
			<!-- 日 -->
			<if test="dateType == 1">
				AND DATE(a.datetime) IN (${queryDateStrs})
			</if>
			<!-- 周 -->
			<if test="dateType == 2">
				AND YEARWEEK(DATE(a.datetime),7) IN (${queryDateStrs})
			</if>
			<!-- 月 -->
			<if test="dateType == 3">
				AND DATE_FORMAT(a.datetime,'%Y%m') IN (${queryDateStrs})
			</if>
			<!-- 自定义 -->
			<if test="dateType == 4">
				AND a.datetime >= #{sentTimeStartQr} AND a.datetime <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
		</if>
	</select>
	<select id="getCustomListTotalDetail" resultType="CockpitSheet">
		SELECT
		ROUND(IFNULL(a.area,0),4) AS "area",
		IFNULL(a.amount,0) AS "amount",
		ROUND(IFNULL(a.money,0),2) AS "money",
		IFNULL(a.pcsQty,0)AS "pcsQty",
		CASE #{dateType}
		WHEN  1 THEN Date(a.datetime)
		WHEN 2 THEN YEARWEEK(DATE(a.datetime),7)
		WHEN 3 THEN DATE_FORMAT(DATE(a.datetime),'%Y%m')
		ELSE Date(a.datetime) END AS "date",
		b.name AS "departName",
		#{sentTimeStartQr} AS "sentTimeStartQr",
		#{sentTimeEndQr} AS "sentTimeEndQr"
		FROM cockpit_customer_sheet a
		LEFT JOIN md_customer b ON b.recordId = a.customId
		LEFT JOIN icloud_group_org_relation c ON c.deptId = b.deptId AND c.activeFlag = 1
		LEFT JOIN icloud_group_org d ON d.recordId = c.groupOrgId
		WHERE a.activeFlag = 1
		<if test="terminal != null and terminal !=''">
			AND a.terminal = #{terminal}
		</if>
		<if test="moduleType != null and moduleType != ''">
			AND FIND_IN_SET(a.modulType,#{moduleType})
		</if>
		<if test="groupOrgId != null and groupOrgId != ''">
			AND FIND_IN_SET(d.recordId,#{groupOrgId})
		</if>
		<if test="dateType != null and dateType != ''">
			<!-- 日 -->
			<if test="dateType == 1">
				AND DATE(a.datetime) IN (${queryDateStrs})
			</if>
			<!-- 周 -->
			<if test="dateType == 2">
				AND YEARWEEK(DATE(a.datetime),7) IN (${queryDateStrs})
			</if>
			<!-- 月 -->
			<if test="dateType == 3">
				AND DATE_FORMAT(a.datetime,'%Y%m') IN (${queryDateStrs})
			</if>
			<!-- 自定义 -->
			<if test="dateType == 4">
				AND a.datetime >= #{sentTimeStartQr} AND a.datetime <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
		</if>
	</select>

	<select id="getSalesListTotalDetail" resultType="CockpitSheet">
		SELECT
		ROUND(IFNULL(a.area,0),4) AS "area",
		IFNULL(a.amount,0) AS "amount",
		ROUND(IFNULL(a.money,0),2) AS "money",
		IFNULL(a.pcsQty,0)AS "pcsQty",
		CASE #{dateType}
		WHEN  1 THEN Date(a.datetime)
		WHEN 2 THEN YEARWEEK(DATE(a.datetime),7)
		WHEN 3 THEN DATE_FORMAT(DATE(a.datetime),'%Y%m')
		ELSE Date(a.datetime) END AS "date",
		b.userName AS "departName",
		#{sentTimeStartQr} AS "sentTimeStartQr",
		#{sentTimeEndQr} AS "sentTimeEndQr"
		FROM cockpit_salesman_sheet a
		LEFT JOIN sm_user b ON b.recordId = a.saleId
		LEFT JOIN md_customer c ON c.salesman = b.recordId AND c.activeFlag = 1 AND c.companyId = b.companyId
		LEFT JOIN icloud_group_org_relation d ON d.deptId = c.deptId AND d.activeFlag = 1
		LEFT JOIN icloud_group_org e ON e.recordId = d.groupOrgId
		WHERE a.activeFlag = 1
		<if test="terminal != null and terminal !=''">
			AND a.terminal = #{terminal}
		</if>
		<if test="moduleType != null and moduleType != ''">
			AND FIND_IN_SET(a.modulType,#{moduleType})
		</if>
		<if test="groupOrgId != null and groupOrgId != ''">
			AND FIND_IN_SET(e.recordId,#{groupOrgId})
		</if>
		<if test="dateType != null and dateType != ''">
			<!-- 日 -->
			<if test="dateType == 1">
				AND DATE(a.datetime) IN (${queryDateStrs})
			</if>
			<!-- 周 -->
			<if test="dateType == 2">
				AND YEARWEEK(DATE(a.datetime),7) IN (${queryDateStrs})
			</if>
			<!-- 月 -->
			<if test="dateType == 3">
				AND DATE_FORMAT(a.datetime,'%Y%m') IN (${queryDateStrs})
			</if>
			<!-- 自定义 -->
			<if test="dateType == 4">
				AND a.datetime >= #{sentTimeStartQr} AND a.datetime <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
		</if>
		GROUP BY a.recordId
	</select>
	<select id="getSupplierListTotalDetail" resultType="CockpitSheet">
		SELECT
		ROUND(IFNULL(a.area,0),4) AS "area",
		IFNULL(a.amount,0) AS "amount",
		ROUND(IFNULL(a.money,0),2) AS "money",
		IFNULL(a.pcsQty,0)AS "pcsQty",
		CASE #{dateType}
		WHEN  1 THEN Date(a.datetime)
		WHEN 2 THEN YEARWEEK(DATE(a.datetime),7)
		WHEN 3 THEN DATE_FORMAT(DATE(a.datetime),'%Y%m')
		ELSE Date(a.datetime) END AS "date",
		b.name AS "departName",
		#{sentTimeStartQr} AS "sentTimeStartQr",
		#{sentTimeEndQr} AS "sentTimeEndQr"
		FROM cockpit_supplier_sheet a
		LEFT JOIN md_supplier b ON b.recordId = a.supplierId
		WHERE a.activeFlag = 1
		<if test="terminal != null and terminal !=''">
			AND a.terminal = #{terminal}
		</if>
		<if test="moduleType != null and moduleType != ''">
			AND FIND_IN_SET(a.modulType,#{moduleType})
		</if>
		<if test="dateType != null and dateType != ''">
			<!-- 日 -->
			<if test="dateType == 1">
				AND DATE(a.datetime) IN (${queryDateStrs})
			</if>
			<!-- 周 -->
			<if test="dateType == 2">
				AND YEARWEEK(DATE(a.datetime),7) IN (${queryDateStrs})
			</if>
			<!-- 月 -->
			<if test="dateType == 3">
				AND DATE_FORMAT(a.datetime,'%Y%m') IN (${queryDateStrs})
			</if>
			<!-- 自定义 -->
			<if test="dateType == 4">
				AND a.datetime >= #{sentTimeStartQr} AND a.datetime <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
		</if>
	</select>

	<select id="getCompanyLists" resultType="Company">
		SELECT
			b.recordId,
			b.`name`,
			b.shortName
		FROM sm_user a
		LEFT JOIN md_company b ON b.recordId = a.companyId
		WHERE a.phone  = #{phone} AND b.activeFlag = 1
		GROUP BY b.recordId
	</select>

	<select id="getOrgDepartLists" resultType="Department">
	SELECT
	    c.`recordId` as no,
		c.`name`
	FROM
		md_department a
		LEFT JOIN icloud_group_org_relation b ON b.deptId = a.recordId
		LEFT JOIN icloud_group_org c ON c.recordId = b.groupOrgId
	WHERE a.activeFlag = 1
		<if test="companyId != null and companyId != ''">
			AND a.companyId = #{companyId}
		</if>
		AND c.`name` IS NOT NULL
	GROUP BY
		c.`name`
	</select>

	<select id="getSalesLists" resultType="User">
		SELECT
			d.recordId,
			d.userName,
			e.groupOrgId
		FROM md_position_employee a
		LEFT JOIN md_employee_position b ON b.recordId = a.employeePositionId
		LEFT JOIN md_employee c ON c.recordId = a.employeeId
		LEFT JOIN sm_user d ON d.recordId = c.userId
		LEFT JOIN icloud_group_org_relation e ON e.deptId = a.groupId AND e.activeFlag = 1 AND a.`status` = 2
		WHERE  a.activeFlag = 1 AND b.`name` = '业务员'
		<if test="companyId != null and companyId != ''">
			AND a.companyId = #{companyId}
			AND CASE a.`status` WHEN 1 THEN a.groupId = #{companyId} ELSE 1= 1 END
		</if>
		<if test="departId != null and departId != ''">
			AND CASE a.`status`WHEN 2 THEN a.groupId IN
			(
				SELECT deptId FROM icloud_group_org_relation WHERE groupOrgId = #{departId} AND activeFlag = 1
			) ELSE 1= 1 END
		</if>
		AND d.activeFlag = 1 AND d.`status` IN(1,3)
		GROUP BY d.userName
	</select>
	<select id="getEmployeeUserName" resultType="Employee">
	SELECT
		a.name,
		a.position
	FROM
		oa_md_employee a
		LEFT JOIN icloud_sm_user b ON b.recordId = a.userId
	WHERE
		b.phone = #{phone} AND a.position IS NOT NULL AND a.activeFlag = 1
		AND a.workStatus = 1 LIMIT 1
	</select>
</mapper>