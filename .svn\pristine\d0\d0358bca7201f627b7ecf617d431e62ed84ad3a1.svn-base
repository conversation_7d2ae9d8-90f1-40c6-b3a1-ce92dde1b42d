package com.kyb.pcberp.modules.purch.web;

import java.math.BigDecimal;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.kyb.pcberp.common.persistence.PageNoInterceptor;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.modules.contract.entity.MatPreparation;
import com.kyb.pcberp.modules.contract.entity.SummaryPreparation;
import com.kyb.pcberp.modules.contract.service.MatPreparationService;
import com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_Notification;
import com.kyb.pcberp.modules.sys.entity.*;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Maps;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.modules.purch.entity.PurchRaw;
import com.kyb.pcberp.modules.purch.entity.Purchasing;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import com.kyb.pcberp.modules.purch.entity.SupplierFile;
import com.kyb.pcberp.modules.purch.service.PurchRawService;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.MaterialPlace;
import com.kyb.pcberp.modules.stock.service.RawmaterialStockService;
import com.kyb.pcberp.modules.sys.service.BranchService;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

@Controller
@RequestMapping(value = "${adminPath}/purch/rawApply")
public class PurchRawController
{
    
    @Autowired
    private PurchRawService purchRawService;
    
    @Autowired
    private BranchService branchService;
    
    @Autowired
    private RawmaterialStockService rawmaterialStockService;

    @Autowired
    private MatPreparationService matPreparationService;
    
    @RequestMapping(value = "list")
    public String list(Material material)
    {
        return "modules/purch/purchRaw";
    }
    
    /**
     * 查询所有的申购订单
     */
    @RequiresPermissions("purch.rawApply:view")
    @RequestMapping(value = "page", method = {RequestMethod.POST})
    @ResponseBody
    public Page<PurchRaw> getList(@RequestBody PurchRaw purchRaw, HttpServletRequest request,
        HttpServletResponse response)
    {
        purchRaw.setCompany(UserUtils.getUser().getCompany());
        Page<PurchRaw> qpage = new Page<PurchRaw>(request, response);
        if (StringUtils.isNotBlank(purchRaw.getPageNo()) && StringUtils.isNotBlank(purchRaw.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(purchRaw.getPageNo()));
            qpage.setPageSize(Integer.parseInt(purchRaw.getPageSize()));
            qpage.setOrderBy("p.purchApplyId desc");
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        // 设置查询范围
        if (purchRaw.getQueryAll() != null && !purchRaw.getQueryAll())
        {
            purchRaw.setCreatedBy(UserUtils.getUser());
        }
        Page<PurchRaw> page = purchRawService.getList(qpage, purchRaw);
        return page;
    }
    
    /**
     * 加载所有下拉框
     */
    @RequiresPermissions("purch.rawApply:view")
    @RequestMapping(value = "load/data", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, List<?>> getData()
    {
        Map<String, List<?>> data = purchRawService.getData();
        return data;
    }
    
    /**
     * 获取申购订单编号
     * 
     * @return
     */
    @RequiresPermissions("purch.rawApply:view")
    @RequestMapping(value = "getNo", method = {RequestMethod.POST})
    @ResponseBody
    public String getNo()
    {
        return CommonUtils.geDocumentNo("22");
    }
    
    /**
     * 添加
     */
    @RequiresPermissions("purch.rawApply:edit")
    @RequestMapping(value = "save", method = RequestMethod.POST)
    @ResponseBody
    public String save(@RequestBody PurchRaw purchRaw)
    {
        String result = null;
        if (StringUtils.isNotBlank(purchRaw.getRecordId()))
        {
            purchRawService.updaeRaw(purchRaw);
            result = "editSuccess";
        }
        else
        {
            purchRaw.setCompany(UserUtils.getUser().getCompany());
            purchRaw.setCreatedBy(UserUtils.getUser());
            purchRaw.setStatus("1");
            purchRaw.setMergeType(TypeKey.PU_PURCHASING_APPLY_MERGETY_ONE.toString());
            purchRawService.savePurRaw(purchRaw);
            // sendMsg(purchRaw, TypeKey.MESSAGE_PURCHASINGAPPLYCREATE.toString(), "已经创建");
            result = "addSuccess";
        }
        
        // 修改物料的板料供应商
        if (null != purchRaw.getMaterial() && StringUtils.isNotBlank(purchRaw.getMaterial().getSupplierId()))
        {
            rawmaterialStockService.editSupplier(purchRaw.getMaterial());
        }
        
        return result;
    }
    
    // 提前期修改
    @RequiresPermissions("purch.rawApply:edit")
    @RequestMapping(value = "updateLeadTime", method = RequestMethod.POST)
    @ResponseBody
    public String updateLeadTime(@RequestBody Integer leadTime)
    {
        return purchRawService.updateLeadTime(leadTime);
    }
    
    // 提前期获取
    @RequiresPermissions("purch.rawApply:edit")
    @RequestMapping(value = "getLeadTime", method = RequestMethod.POST)
    @ResponseBody
    public String getLeadTime()
    {
        return purchRawService.getLeadTime();
    }
    
    /**
     * 删除
     */
    @RequiresPermissions("purch.rawApply:edit")
    @RequestMapping(value = "editStatus", method = RequestMethod.POST)
    @ResponseBody
    public String editStatus(@RequestBody PurchRaw purchRaw)
    {
        purchRaw.setCompany(UserUtils.getUser().getCompany());
        PurchRaw purchRawCopy = purchRawService.getPurRaw(purchRaw);
        String result = null;
        if (StringUtils.isNotBlank(purchRaw.getStatus()))
        {
            if (purchRaw.getStatus().equals("3"))
            {
                result = purchRawService.checkDelPurchRaw(purchRaw);
                if (!"success".equals(result))
                {
                    return result;
                }
                if (purchRawCopy.getStatus().equals("1"))
                {
                    // sendMsg(purchRawCopy, TypeKey.MESSAGE_PURCHASINGAPPLYDELETE.toString(), "已经删除");
                    result = "删除原料采购单成功！";
                }
                else
                {
                    result = "不能重复操作！";
                    return result;
                }
                purchRawService.genUpdateStatus(purchRaw);
            }
            else if (purchRaw.getStatus().equals("2"))
            {
                if (purchRawCopy.getStatus().equals("1"))
                {
/*                    result = purchRawService.contractApproval(purchRawCopy);
                    if (!"fail".equals(result))
                    {
                        return result;
                    }*/
                    // sendMsg(purchRawCopy, TypeKey.MESSAGE_PURCHASINGAPPLYCOMMIT.toString(), "已经确认");
                    purchRaw.setStatus("2");
                    result = "确认成功！";
                }
                else
                {
                    result = "不能重复操作！";
                    return result;
                }
            }
            else if (purchRaw.getStatus().equals("7"))
            {
                if (purchRawCopy.getStatus().equals("2"))
                {
                    // sendMsg(purchRawCopy, TypeKey.MESSAGE_PURCHASINGAPPLYAUDIT.toString(), "已经审核");
                    result = "审核成功！";
                }
                else
                {
                    result = "不能重复操作！";
                    return result;
                }
            }
            else if (purchRaw.getStatus().equals("4"))
            {
                if (purchRawCopy.getStatus().equals("7"))
                {
                    // sendMsg(purchRawCopy, TypeKey.MESSAGE_PURCHASINGAPPLYCANCLE.toString(), "已经取消");
                    result = "取消原料采购单成功！";
                }
                else
                {
                    result = "不能重复操作！";
                    return result;
                }
                purchRawService.genUpdateStatus(purchRaw);
            }
            else if (purchRaw.getStatus().equals("1"))
            {
                if (purchRawCopy.getStatus().equals("2"))
                {
                    // sendMsg(purchRawCopy, TypeKey.MESSAGE_PURCHASINGAPPLYCANCLECOMMIT.toString(), "已经取消确认");
                    result = "取消确认成功！";
                }
                else
                {
                    result = "不能重复操作！";
                    return result;
                }
            }
        }
        purchRawService.updatePurRaw(purchRaw);
        return result;
    }
    
    // public void sendMsg(PurchRaw purchRaw, String msgType, String name)
    // {
    // ParentMessage parentMessage = new ParentMessage();
    // parentMessage.setMessageModuleCopy(msgType);
    // parentMessage.setMessaeDeail("编号为" + purchRaw.getPurchApplyId() + "的原料申请单"+name);
    // if (purchRaw.getCreatedBy() != null)
    // {
    // parentMessage.setCreateByName(purchRaw.getCreatedBy());
    // }
    // parentMessageService.sendMessage(parentMessage);
    // }
    
    /**
     * 上传原料申请单附件
     */
    @RequiresPermissions("purch:supplier:edit")
    @RequestMapping(value = "uploadSupplierFile")
    @ResponseBody
    public Map<String, Object> uploadSupplierFile(@RequestParam("file") MultipartFile file,
        @RequestParam("no") String no, HttpServletRequest req)
    {
        Company company = UserUtils.getUser().getCompany();
        
        // 原料申请单资料命名 公司id+供应商编号+资料
        String savePath = company.getRecordId() + "/purchRaw/" + no + "/";
        Map<String, Object> result = Maps.newHashMap();
        
        // 上传文件
        String url = FileManageUtils.uploadFile(file, savePath, req);
        if (url == null)
        {
            result.put("result", false);
            return result;
        }
        
        // 基本路径
        String sourcePath = savePath + file.getOriginalFilename();
        
        SupplierFile supplierFile = new SupplierFile();
        supplierFile.setNo(no);
        supplierFile.setCompany(company);
        supplierFile.setFileUrl(sourcePath);
        supplierFile.setRealFileName(file.getOriginalFilename());
        supplierFile.setTempUrl(url);
        purchRawService.uploadFile(supplierFile);
        result.put("supplierFile", supplierFile);
        result.put("result", true);
        return result;
    }
    
    /**
     * 获取原料申请单上传的文件
     * 
     * @param request
     * @return
     */
    @RequiresPermissions("purch:supplier:view")
    @RequestMapping(value = "getFile", method = RequestMethod.POST)
    @ResponseBody
    public List<SupplierFile> getFile(@RequestBody PurchRaw purchRaw)
    {
        purchRaw.setCompany(UserUtils.getUser().getCompany());
        return purchRawService.getFile(purchRaw);
    }
    
    @RequiresPermissions("purch.rawApply:edit")
    @RequestMapping(value = "genaPurchRow", method = {RequestMethod.POST})
    @ResponseBody
    public String genaPurchRow(@RequestBody List<PurchRaw> rawList)
    {
        Purchasing purchasing = new Purchasing();
        if (Collections3.isNotEmpty(rawList) && StringUtils.isNotBlank(rawList.get(0).getSupplierId()))
        {
            Supplier supplier = purchRawService.getSupplier(new Supplier(rawList.get(0).getSupplierId()));
            purchasing.setCompany(UserUtils.getUser().getCompany());
            purchasing.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString()));
            purchasing.setSupplier(supplier);
            purchasing.setOrderDate(new Date());
            Branch branch = new Branch();
            branch.setCompany(UserUtils.getUser().getCompany());
            List<Branch> branchList = branchService.findList(branch);
            if (branchList.size() > 0)
            {
                purchasing.setBranch(branchList.get(0));
                if (StringUtils.isNotBlank(branchList.get(0).getAddress()))
                {
                    purchasing.setDeliveryPlace(branchList.get(0).getAddress());
                }
            }
            if (supplier.getDeliveryWay() != null && StringUtils.isNotBlank(supplier.getDeliveryWay().getRecordId()))
            {
                purchasing.setDeliveryWay(Long.valueOf(supplier.getDeliveryWay().getRecordId()));
            }
            if (supplier.getPayWay() != null && StringUtils.isNotBlank(supplier.getPayWay().getRecordId()))
            {
                purchasing.setPayWay(Integer.valueOf(supplier.getPayWay().getRecordId()));
            }
            if (supplier.getPayDays() != null && StringUtils.isNotBlank(supplier.getPayDays().getRecordId()))
            {
                purchasing.setPayDays(Integer.valueOf(supplier.getPayDays().getRecordId()));
            }
            if (supplier.getTaxDescript() != null && StringUtils.isNotBlank(supplier.getTaxDescript().getRecordId()))
            {
                purchasing.setTaxDescript(Long.valueOf(supplier.getTaxDescript().getRecordId()));
            }
            
            // 默认赋值RMB
            DictValue dictValue = new DictValue();
            dictValue.setCompany(purchasing.getCompany());
            dictValue.setItem(new DictItem("11"));
            dictValue.setValue("RMB");
            Long currencyType = purchRawService.getDictValueId(dictValue);
            purchasing.setCurrencyType(currencyType);
            
            purchasing.setPurchaser(UserUtils.getUser());
            purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_UNCONFIRM);
            purchasing.setActiveFlag("1");
            purchasing.setDeliveryPlace(supplier.getAddress());
            purchasing.setQualityStd(supplier.getQualityStd());
            // 设置采购明细列表
            List<PurchasingDetail> deailList = new ArrayList<PurchasingDetail>();
            for (PurchRaw purchRaw : rawList)
            {
                Boolean addFlag = false;
                if (Collections3.isNotEmpty(deailList))
                {
                    for (PurchasingDetail deail : deailList)
                    {
                        if (null != deail.getMaterial() && null != purchRaw.getMaterial()
                            && StringUtils.isNotBlank(deail.getMaterial().getRecordId())
                            && StringUtils.isNotBlank(purchRaw.getMaterial().getRecordId())
                            && deail.getMaterial().getRecordId().equals(purchRaw.getMaterial().getRecordId()))
                        {
                            BigDecimal sumQty = deail.getQuantity();
                            if (null == sumQty)
                            {
                                sumQty = BigDecimal.ZERO;
                            }
                            Integer purchRawQty = null == purchRaw.getQuantity() ? 0 : purchRaw.getQuantity().intValue();
                            if (null == purchRawQty)
                            {
                                purchRawQty = 0;
                            }
                            sumQty = sumQty.add(new BigDecimal(purchRawQty));
                            deail.setQuantity(sumQty);
                            if (StringUtils.isNotBlank(deail.getPurchRawIds()))
                            {
                                deail.setPurchRawIds(deail.getPurchRawIds() + "," + purchRaw.getRecordId());
                            }
                            else
                            {
                                deail.setPurchRawIds(purchRaw.getRecordId());
                            }
                            addFlag = true;
                            break;
                        }
                    }
                }
                if (addFlag)
                {
                    continue;
                }
                PurchasingDetail deail = new PurchasingDetail();
                if (StringUtils.isNotBlank(deail.getPurchRawIds()))
                {
                    deail.setPurchRawIds(deail.getPurchRawIds() + "," + purchRaw.getRecordId());
                }
                else
                {
                    deail.setPurchRawIds(purchRaw.getRecordId());
                }
                deail.setMaterial(purchRaw.getMaterial());
                if (purchRaw.getQuantity() != null)
                {
                    deail.setQuantity(purchRaw.getQuantity());
                }
                deail.setCraftDescript(purchRaw.getCraftDescript());
                deail.setRemark(purchRaw.getRemark());
                deail.setSupplierModel(purchRaw.getSupplierModel());
                if (StringUtils.isNotBlank(purchRaw.getDeliveryDays()))
                {
                    deail.setDeliveryDays(Long.valueOf(purchRaw.getDeliveryDays()));
                }
                // deail.setPurchRaw(purchRaw);
                deailList.add(deail);
            }
            purchasing.setPurchasingDetailList(deailList);
        }
        return purchRawService.genaPurchRow(purchasing);
    }
    
/*    @RequiresPermissions("purch.rawApply:edit")
    @RequestMapping(value = "updatePurchDays", method = {RequestMethod.POST})
    @ResponseBody
    public String updatePurchDays(@RequestBody PurchRaw raw)
    {
        return purchRawService.updatePurchDays(raw);
    }*/
    
    @RequestMapping(value = "getSaleStockPlaceList", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> getSaleStockPlaceList(@RequestBody MaterialPlace materialPlace)
    {
        return purchRawService.getSaleStockPlaceList(materialPlace);
    }
    
    @RequiresPermissions(value = {"stock:material:view", "purch:rawmaterial:view"}, logical = Logical.OR)
    @RequestMapping(value = "getPurchasing")
    @ResponseBody
    public List<PurchRaw> getPurchasing(String id)
    {
        return purchRawService.getPurchRawList(id);
    }
    
    @RequiresPermissions("purch.rawApply:edit")
    @RequestMapping(value = "batchConfirm", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> batchConfirm(@RequestBody PurchRaw raw)
    {
        return purchRawService.batchConfirm(raw);
    }
    
    @RequiresPermissions("purch.rawApply:edit")
    @RequestMapping(value = "getPurchRawData", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> getPurchRawData(@RequestBody PurchRaw raw)
    {
        return purchRawService.getPurchRawData(raw);
    }

    @RequiresPermissions("purch.rawApply:edit")
    @RequestMapping(value = "getPriceParityList", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> getPriceParityList(@RequestBody PurchRaw raw)
    {
        return purchRawService.getPriceParityList(raw);
    }
    
    @RequestMapping(value = "getProductStockPlaceList", method = {RequestMethod.POST})
    @ResponseBody
    public List<MaterialPlace> getProductStockPlaceList(@RequestBody MaterialPlace materialPlace)
    {
        return purchRawService.getProductStockPlaceList(materialPlace);
    }
    
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "export")
    public String exportFile(PurchRaw purchRaw, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            // 设置查询范围
            if (purchRaw.getQueryAll() != null && !purchRaw.getQueryAll())
            {
                purchRaw.setCreatedBy(UserUtils.getUser());
            }
            purchRaw.setCompany(UserUtils.getUser().getCompany());
            String fileName = "原料申请单数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            Page<PurchRaw> qpage = new Page<PurchRaw>(request, response,-1);
            qpage.setOrderBy("createdDate desc");
            Page<PurchRaw> page = purchRawService.getList(qpage,purchRaw);
            new ExportExcel("原料申请单数据", PurchRaw.class, new Integer(1)).setDataList(page.getList())
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "getPurchMergeList", method = {RequestMethod.POST})
    @ResponseBody
    public List<PurchRaw> getPurchMergeList(@RequestBody PurchRaw purchRaw)
    {
        return purchRawService.getPurchMergeList(purchRaw);
    }

    @RequestMapping(value = "genPurchMergeData", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String,Object> genPurchMergeData(@RequestBody List<PurchRaw> list)
    {
        return purchRawService.genPurchMergeData(list);
    }

    @RequestMapping(value = "canclePurchMergeData", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String,Object> canclePurchMergeData(@RequestBody PurchRaw purchRaw)
    {
        return purchRawService.canclePurchMergeData(purchRaw);
    }

    @RequestMapping(value = {"summaryPage"})
    @ResponseBody
    public Page<SummaryPreparation> summaryPage(@RequestBody SummaryPreparation summaryPreparation, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        // 设置查询范围
        if (summaryPreparation.getQueryAll() != null && !summaryPreparation.getQueryAll())
        {
            summaryPreparation.setCreatedBy(user);
        }
        // 设置查询企业编号
        if (summaryPreparation != null)
        {
            summaryPreparation.setCompany(UserUtils.getUser().getCompany());
        }
        if (summaryPreparation.getSentTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(summaryPreparation.getSentTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            summaryPreparation.setSentTimeEndQr(deliTime.getTime());
        }
        if (summaryPreparation.getSentTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(summaryPreparation.getSentTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            summaryPreparation.setSentTimeStartQr(deliTime.getTime());
        }
        // 分页查询数据
        Page<SummaryPreparation> qpage = new Page<SummaryPreparation>(request, response);
        if (StringUtils.isNotBlank(summaryPreparation.getPageNo()) && StringUtils.isNotBlank(summaryPreparation.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(summaryPreparation.getPageNo()));
            qpage.setPageSize(Integer.parseInt(summaryPreparation.getPageSize()));

            // 设置排序
            if (StringUtils.isNotBlank(summaryPreparation.getOrderBy()))
            {
                qpage.setOrderBy(summaryPreparation.getOrderBy());
            }

        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        Page<SummaryPreparation> page = matPreparationService.getPurchSummaryPage(qpage, summaryPreparation);
        return page;
    }

    @RequestMapping(value = "getApplyMatData", method = {RequestMethod.POST})
    @ResponseBody
    public List<Material> getApplyMaDatat(@RequestBody Material material)
    {
        String factIds = CompanyUtil.getInstance().getFactId();
        Company com = new Company(factIds);
        String ids = matPreparationService.getIdsByConcatValue(material.getSpecification(),material.getName(),2);
        return purchRawService.getApplyMatData(ids,com);
    }

    @RequestMapping(value = "saveApplyMat", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String,Object> saveApplyMat(@RequestBody List<PurchRaw> list)
    {
        return purchRawService.saveApplyMat(list);
    }

    @RequestMapping(value = "updateConfirmReason", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String,Object> updateConfirmReason(@RequestBody MatPreparation matPreparation)
    {
        return purchRawService.updateConfirmReason(matPreparation);
    }

    @RequestMapping(value = "updatePurhchaseWay", method = {RequestMethod.POST})
    @ResponseBody
    public String updatePurhchaseWay(@RequestBody PurchRaw purchRaw)
    {
        return purchRawService.updatePurhchaseWay(purchRaw);
    }

    @RequestMapping(value = "getMaterialManuList", method = {RequestMethod.POST})
    @ResponseBody
    public List<Material> getMaterialManuList(@RequestBody PurchRaw purchRaw)
    {
        return purchRawService.getMaterialManuList(purchRaw);
    }

    @RequestMapping(value = "updateUseFlag", method = {RequestMethod.POST})
    @ResponseBody
    public String updateUseFlag(@RequestBody PurchRaw purchRaw)
    {
        return purchRawService.updateUseFlag(purchRaw);
    }

}
