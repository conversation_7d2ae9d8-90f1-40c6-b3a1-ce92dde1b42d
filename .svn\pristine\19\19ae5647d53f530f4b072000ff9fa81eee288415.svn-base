const routes = [
	{
		path: '/',
		name: '',
		component: maintenanceMenu,
		children:[
			{
				path: '',
				name: 'maintenance',
				component: maintenance
			},
			{
				path: 'my',
				name: 'my',
				component: my
			},
			{
				path: '/maintenanceCount',
				name: 'maintenanceCount',
				component: maintenanceCount
			},
			{
				path: '/waiteRepair',
				name: 'waiteR<PERSON><PERSON>',
				component: waiteRepair
			},
			{
				path: '/equipmentManage',
				name: 'equipmentManage',
				component: equipmentManage
			},
			{
				path: '/inputEquipment',
				name: 'inputEquipment',
				component: inputEquipment
			},
			{
				path: '/timePackage',
				name: 'timePackage',
				component: timePackage
			},
			{
				path: '/timeReport',
				name: 'timeReport',
				component: timeReport
			},
		]
	},
	{
		path: '/',
		component: auth,
		children:[
			{
				path: 'login',
				name: 'login',
				component: login
			},
			{
				path: 'register',
				name: 'register',
				component: register
			},
			{
				path: 'editPwd',
				name: 'editPwd',
				component: editPwd
			}
		]
	}
]
const router = new VueRouter({
	routes
})