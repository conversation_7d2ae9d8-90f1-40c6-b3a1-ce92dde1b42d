/**
 * 
 */
package com.kyb.pcberp.modules.stock.dao;

import java.util.List;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.stock.entity.AdjustStocks;

/**
 * stockAdjustDAO接口
 * 
 * <AUTHOR>
 * @version 2015-09-17
 */
@MyBatisDao
public interface AdjustStocksDao extends CrudDao<AdjustStocks>
{
    /**
     * 导出
     * 
     * @param material
     * @return
     */
    public List<AdjustStocks> findExpList(AdjustStocks material);
    
}