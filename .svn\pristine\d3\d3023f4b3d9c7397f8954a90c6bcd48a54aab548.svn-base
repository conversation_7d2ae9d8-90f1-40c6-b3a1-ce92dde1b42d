package com.kyb.pcberp.modules.contract.dao;

import java.util.List;

import com.kyb.pcberp.modules.eg.entity.CardA;
import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.contract.entity.GroupCenter;
import com.kyb.pcberp.modules.contract.entity.GroupConDeail;
import com.kyb.pcberp.modules.contract.entity.GroupMaterialMoney;
import com.kyb.pcberp.modules.contract.entity.GroupQuote;
import com.kyb.pcberp.modules.contract.entity.GroupQuoteList;
import com.kyb.pcberp.modules.contract.entity.GroupQuoteRecord;

@MyBatisDao
public interface GroupCenterDao
{
    List<GroupConDeail> getDeailList(@Param("saleIds") String saleIds, @Param("startDate") String startDate,
        @Param("endDate") String endDate);
    
    List<GroupQuote> getQuoteList(@Param("factoryId") String factoryId);
    
    List<GroupQuoteList> getQuoteDeailList(@Param("factoryId") String factoryId);
    
    void bathProcessInsert(@Param("recordList") List<GroupQuoteRecord> recordList);
    
    String getSpecialCraftVal(@Param("specialCraft") String specialCraft);
    
    String getShapWayId(@Param("no") String no);
    
    void clearQuoteRecord(@Param("saleIds") String saleIds, @Param("startDate") String startDate,
        @Param("endDate") String endDate);
    
    List<GroupQuoteRecord> getProcessFeeList(@Param("saleIds") String saleIds, @Param("startDate") String startDate,
        @Param("endDate") String endDate);
    
    List<GroupMaterialMoney> getMaterialFeeList(@Param("saleIds") String saleIds, @Param("startDate") String startDate,
        @Param("endDate") String endDate);
    
    void batchUpdateGroupCenter(@Param("groupList") List<GroupCenter> groupList);

    List<GroupConDeail> getDeailListByIds(@Param("ids") String groupCenterIds,@Param("saleIds") String saleIds);

    void clearQuoteRecordByIds( @Param("ids") String groupCenterIds);

    List<GroupQuoteRecord> getProcessFeeListByIds( @Param("ids")String groupCenterIds);

    List<GroupMaterialMoney> getMaterialFeeListByIds(@Param("ids")String groupCenterIds);

    List<GroupCenter> getGroDataByCard(CardA cardA);

    GroupCenter getDifferenceData(@Param("groupCenterId") String groupCenterId);

    GroupCenter getNegotiatedApprovalData(GroupCenter groupCenter);

    void updateNegotiatedApprovalData(GroupCenter groupCenter);

    void updateActiveFlag(GroupCenter groupCenter);

    List<GroupCenter> getGroupCenterList(GroupCenter groupCenter);

    GroupCenter getUseAgeDataCount(@Param("contactDeailId") String contactDeailId);

    GroupCenter selectGroupCenter(@Param("groupCenterId") String groupCenterId);

    GroupCenter getGroupCenterData(@Param("groupCenterId") String groupCenterId);
}
