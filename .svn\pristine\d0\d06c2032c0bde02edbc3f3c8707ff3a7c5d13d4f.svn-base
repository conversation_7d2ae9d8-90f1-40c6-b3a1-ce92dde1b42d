/**
 * 
 */
package com.kyb.pcberp.modules.crm.entity;

import java.util.Date;
import java.util.List;

import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import org.hibernate.validator.constraints.Length;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.sys.entity.Branch;
import com.kyb.pcberp.modules.sys.entity.User;

/**
 * 报价Entity
 * 
 * <AUTHOR>
 * @version 2015-09-02
 */
public class Quotation extends DataEntity<Quotation>
{
    
    private static final long serialVersionUID = 1L;
    
    private Integer findNoisEnable; // 在保存单据的时候 单据编号被占用了 就在前台赋值为 1 了 没有占用就是为0
    
    private Customer customer; // 客户ID
    
    private String no; // 报价单编号
    
    private String totalAmt; // 总报价
    
    private Long taxDescript; // 报价税率说明
    
    private Long currencyType; // 货币类型
    
    private Long payWay; // 付款方式
    
    private Long newPdtDelivDays; // 新单生产交货期限
    
    private Long repdcDelivDays; // 返单生产交货期限
    
    private String status; // 报价状态：200101=未报给客户;200102=已报给客户;200103=客户已反馈;200104=已转为合同;200105=已失效;
    
    private User quoter; // 报价人
    
    private Branch branch; // 子公司
    
    private List<QuotationDetail> quotationItemList; // 报价单明细项列表
    
    private String contractId;// 被引用的合同编号
    
    private Date sentTimeStartQr; // 时间段查询条件 载体
    
    private Date sentTimeEndQr;
    
    private User salesman;
    
    // WC 2017-05-27 ADD
    private Long signplace; // 签订地点
    
    private Long deliverycity; // 交货地点
    
    private Long frightbear; // 运费承担
    
    private Long billformula; // 票据形式
    
    private Long hasquestion; // 如有问题
    
    private Long paycause; // 付款方式
    
    private Long inspectionstandard; // 检验标准
    
    private Long packingrequirement; // 包装方式
    
    private Long freightWay; // 交货方式
    
    private String customerModel; // 客户型号
    
    private String taxDescriptValue;
    
    private String currencyTypeValue;
    
    private String payWayValue;
    
    private CustomerContact customerContact;

    private List<QuotationResult> setUpListResult;

    private String priceStatus;

    private String currencyTypeValueTwo;

    private String taxDescriptTwo;

    private String processValueId;

    private String keepDecimal;

    private String typePrice;

    private String offerCategory;

    private Supplier supplier;

    private String accredit;

    private String specialCraft;
    
    public Quotation()
    {
        super();
    }
    
    public Quotation(String id)
    {
        super(id);
    }
    
    public Integer getFindNoisEnable()
    {
        return findNoisEnable;
    }
    
    public void setFindNoisEnable(Integer findNoisEnable)
    {
        this.findNoisEnable = findNoisEnable;
    }
    
    public String getContractId()
    {
        return contractId;
    }
    
    public void setContractId(String contractId)
    {
        this.contractId = contractId;
    }
    
    @ExcelField(title = "客户", align = 2, sort = 20, fieldType = (Customer.class), value = ("customer.shortName"), parentExport = false)
    public Customer getCustomer()
    {
        return customer;
    }
    
    public void setCustomer(Customer customer)
    {
        this.customer = customer;
    }
    
    @Length(min = 1, max = 200, message = "报价单编号长度必须介于 1 和 200 之间")
    @ExcelField(title = "报价单编号", align = 2, sort = 10, parentExport = true, parentSort = {0, 10})
    public String getNo()
    {
        return no;
    }
    
    public void setNo(String no)
    {
        this.no = no;
    }
    
    public String getTotalAmt()
    {
        return totalAmt;
    }
    
    public void setTotalAmt(String totalAmt)
    {
        this.totalAmt = totalAmt;
    }
    
    public Long getTaxDescript()
    {
        return taxDescript;
    }
    
    public void setTaxDescript(Long taxDescript)
    {
        this.taxDescript = taxDescript;
    }
    
    public Long getCurrencyType()
    {
        return currencyType;
    }
    
    public void setCurrencyType(Long currencyType)
    {
        this.currencyType = currencyType;
    }
    
    public Long getPayWay()
    {
        return payWay;
    }
    
    public void setPayWay(Long payWay)
    {
        this.payWay = payWay;
    }
    
    public Long getNewPdtDelivDays()
    {
        return newPdtDelivDays;
    }
    
    public void setNewPdtDelivDays(Long newPdtDelivDays)
    {
        this.newPdtDelivDays = newPdtDelivDays;
    }
    
    public Long getRepdcDelivDays()
    {
        return repdcDelivDays;
    }
    
    public void setRepdcDelivDays(Long repdcDelivDays)
    {
        this.repdcDelivDays = repdcDelivDays;
    }
    
    @Length(min = 0, max = 8, message = "报价状态：200101=未报给客户;200102=已报给客户;200103=客户已反馈;200104=已转为合同;200105=已失效;长度必须介于 0 和 8 之间")
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public User getQuoter()
    {
        return quoter;
    }
    
    public void setQuoter(User quoter)
    {
        this.quoter = quoter;
    }
    
    public Branch getBranch()
    {
        return branch;
    }
    
    public void setBranch(Branch branch)
    {
        this.branch = branch;
    }
    
    public List<QuotationDetail> getQuotationItemList()
    {
        return quotationItemList;
    }
    
    public void setQuotationItemList(List<QuotationDetail> quotationItemList)
    {
        this.quotationItemList = quotationItemList;
    }
    
    public Date getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }
    
    public void setSentTimeStartQr(Date sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }
    
    public Date getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }
    
    public void setSentTimeEndQr(Date sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }
    
    public User getSalesman()
    {
        return salesman;
    }
    
    public void setSalesman(User salesman)
    {
        this.salesman = salesman;
    }

    public Long getSignplace()
    {
        return signplace;
    }

    public void setSignplace(Long signplace)
    {
        this.signplace = signplace;
    }

    public Long getDeliverycity()
    {
        return deliverycity;
    }

    public void setDeliverycity(Long deliverycity)
    {
        this.deliverycity = deliverycity;
    }

    public Long getFrightbear()
    {
        return frightbear;
    }

    public void setFrightbear(Long frightbear)
    {
        this.frightbear = frightbear;
    }

    public Long getBillformula()
    {
        return billformula;
    }

    public void setBillformula(Long billformula)
    {
        this.billformula = billformula;
    }

    public Long getHasquestion()
    {
        return hasquestion;
    }

    public void setHasquestion(Long hasquestion)
    {
        this.hasquestion = hasquestion;
    }

    public Long getPaycause()
    {
        return paycause;
    }

    public void setPaycause(Long paycause)
    {
        this.paycause = paycause;
    }

    public Long getInspectionstandard()
    {
        return inspectionstandard;
    }

    public void setInspectionstandard(Long inspectionstandard)
    {
        this.inspectionstandard = inspectionstandard;
    }

    public Long getPackingrequirement()
    {
        return packingrequirement;
    }

    public void setPackingrequirement(Long packingrequirement)
    {
        this.packingrequirement = packingrequirement;
    }

    public Long getFreightWay()
    {
        return freightWay;
    }

    public void setFreightWay(Long freightWay)
    {
        this.freightWay = freightWay;
    }

    public String getCustomerModel() {
        return customerModel;
    }

    public void setCustomerModel(String customerModel) {
        this.customerModel = customerModel;
    }

    @ExcelField(title = "报价税率", align = 2, sort = 30)
    public String getTaxDescriptValue()
    {
        return taxDescriptValue;
    }

    public void setTaxDescriptValue(String taxDescriptValue)
    {
        this.taxDescriptValue = taxDescriptValue;
    }

    @ExcelField(title = "货币类型", align = 2, sort = 40)
    public String getCurrencyTypeValue()
    {
        return currencyTypeValue;
    }

    public void setCurrencyTypeValue(String currencyTypeValue)
    {
        this.currencyTypeValue = currencyTypeValue;
    }

    @ExcelField(title = "结算方式", align = 2, sort = 50)
    public String getPayWayValue()
    {
        return payWayValue;
    }

    public void setPayWayValue(String payWayValue)
    {
        this.payWayValue = payWayValue;
    }

    public CustomerContact getCustomerContact()
    {
        return customerContact;
    }

    public void setCustomerContact(CustomerContact customerContact)
    {
        this.customerContact = customerContact;
    }

    public List<QuotationResult> getSetUpListResult() {
        return setUpListResult;
    }

    public void setSetUpListResult(List<QuotationResult> setUpListResult) {
        this.setUpListResult = setUpListResult;
    }

    public String getPriceStatus() {
        return priceStatus;
    }

    public void setPriceStatus(String priceStatus) {
        this.priceStatus = priceStatus;
    }

    public String getCurrencyTypeValueTwo() {
        return currencyTypeValueTwo;
    }

    public void setCurrencyTypeValueTwo(String currencyTypeValueTwo) {
        this.currencyTypeValueTwo = currencyTypeValueTwo;
    }

    public String getTaxDescriptTwo() {
        return taxDescriptTwo;
    }

    public void setTaxDescriptTwo(String taxDescriptTwo) {
        this.taxDescriptTwo = taxDescriptTwo;
    }

    public String getProcessValueId() {
        return processValueId;
    }

    public void setProcessValueId(String processValueId) {
        this.processValueId = processValueId;
    }

    public String getProcessValueIdStr(){
        String result = null;
        if (StringUtils.isBlank(processValueId))
        {
            return result;
        }
        switch (processValueId){
            case "1":
                result = "曝光";
                break;
            case "2":
                result = "丝印";
                break;
        }
        return result;
    }


    public String getKeepDecimal() {
        return keepDecimal;
    }

    public void setKeepDecimal(String keepDecimal) {
        this.keepDecimal = keepDecimal;
    }

    public String getTypePrice() {
        return typePrice;
    }

    public void setTypePrice(String typePrice) {
        this.typePrice = typePrice;
    }

    public String getOfferCategory() {
        return offerCategory;
    }

    public void setOfferCategory(String offerCategory) {
        this.offerCategory = offerCategory;
    }

    public String getOfferCategoryStr()
    {
        String result = null;
        if (StringUtils.isBlank(offerCategory))
        {
            return result;
        }
        switch (offerCategory){
            case "1":
                result = "客户";
                break;
            case "2":
                result = "供应商";
                break;
        }
        return result;
    }

    public Supplier getSupplier() {
        return supplier;
    }

    public void setSupplier(Supplier supplier) {
        this.supplier = supplier;
    }

    public String getAccredit() {
        return accredit;
    }

    public void setAccredit(String accredit) {
        this.accredit = accredit;
    }
    public String getAccreditStr()
    {
        String result = null;
        if (StringUtils.isBlank(accredit) || accredit == "")
        {
            result = "否";
            return result;
        }
        switch (accredit){
            case "1":
                result = "是";
                break;
            case "2":
                result = "否";
                break;
        }
        return result;
    }

    public String getSpecialCraft() {
        return specialCraft;
    }

    public void setSpecialCraft(String specialCraft) {
        this.specialCraft = specialCraft;
    }
}