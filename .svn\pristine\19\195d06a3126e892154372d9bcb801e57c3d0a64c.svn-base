package com.kyb.pcberp.modules.purch.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.purch.entity.PrdorderDetail;

public class ProDetailInVo extends PrdorderDetail
{
    
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    
    private String allCustomerPo; // 客户订单号
    
    private String allCustomerName;// 客户名称
    
    private String prdorderNo;// 型 号
    
    private Integer inStoreQty;// 已入库数 量
    
    private Integer waitingQty;// 待入库数 量
    
    private Integer inStock;// 准备入库数量
    
    private Integer contractDetailQty;//合同明细数量
    
    /** 时间段查询条件载体 */
    private Date sentTimeStartQr;
    
    /** 时间段查询条件载体 */
    private Date sentTimeEndQr;
    
    private Date inTime;
    
    private String prdBatchNo;
    
    @ExcelField(title = "客户订单号", align = 2, sort = 40)
    public String getAllCustomerPo()
    {
        return allCustomerPo;
    }
    
    public void setAllCustomerPo(String allCustomerPo)
    {
        this.allCustomerPo = allCustomerPo;
    }
    
    @ExcelField(title = "客户名称", align = 2, sort = 50)
    public String getAllCustomerName()
    {
        return allCustomerName;
    }
    
    public void setAllCustomerName(String allCustomerName)
    {
        this.allCustomerName = allCustomerName;
    }
    
    @ExcelField(title = "采购订单号", align = 2, sort = 70)
    public String getPrdorderNo()
    {
        return prdorderNo;
    }
    
    public void setPrdorderNo(String prdorderNo)
    {
        this.prdorderNo = prdorderNo;
    }
    
    @ExcelField(title = "已入库", align = 2, sort = 100)
    public Integer getInStoreQty()
    {
        return inStoreQty;
    }
    
    public void setInStoreQty(Integer inStoreQty)
    {
        this.inStoreQty = inStoreQty;
    }
    
    @ExcelField(title = "待入库", align = 2, sort = 110)
    public Integer getWaitingQty()
    {
        return waitingQty==null?(getQuantity()==null?0:getQuantity())-(getInStoreQty()==null?0:getInStoreQty()):waitingQty;
    }
    
    public void setWaitingQty(Integer waitingQty)
    {
        this.waitingQty = waitingQty;
    }
    
    public Date getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }
    
    public void setSentTimeStartQr(Date sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }
    
    public Date getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }
    
    public void setSentTimeEndQr(Date sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }
    
    public Integer getInStock()
    {
        return inStock;
    }
    
    public void setInStock(Integer inStock)
    {
        this.inStock = inStock;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getInTime()
    {
        return inTime;
    }

    public void setInTime(Date inTime)
    {
        this.inTime = inTime;
    }

    public Integer getContractDetailQty()
    {
        return contractDetailQty;
    }

    public void setContractDetailQty(Integer contractDetailQty)
    {
        this.contractDetailQty = contractDetailQty;
    }
    
    @ExcelField(title = "供应商编码 ", align = 2, sort = 10)
    public String getSupplierNo()
    {
        return super.getSupplierNo();
    }
    
    @ExcelField(title = "供应商名称  ", align = 2, sort = 20)
    public String getSupplierName()
    {
        return super.getSupplierName();
    }
    
    @ExcelField(title = "终端供应商 ", align = 2, sort = 30)
    public String getLnSupplierName()
    {
        return super.getLnSupplierName();
    }
    
    @ExcelField(title = "型 号", align = 2, sort = 60)
    public String getCustomerModel()
    {
        return super.getCustomerModel();
    }
    
    @ExcelField(title = "合同编号", align = 2, sort = 80)
    public String getContractNo()
    {
        return super.getContractNo();
    }
    
    @ExcelField(title = "订单量", align = 2, sort = 90)
    public Integer getQuantity()
    {
        return super.getQuantity();
    }
    
    @ExcelField(title = "订单日期", align = 2, sort = 130)
    public Date getOrderDate()
    {
        return super.getOrderDate();
    }
    
    @ExcelField(title = "审核日期", align = 2, sort = 140)
    public Date getLastUpdDate()
    {
        return super.getLastUpdDate();
    }
    
    @ExcelField(title = "备注", align = 2, sort = 150)
    public String getRemark()
    {
        return super.getRemark();
    }

    public String getPrdBatchNo()
    {
        return prdBatchNo;
    }

    public void setPrdBatchNo(String prdBatchNo)
    {
        this.prdBatchNo = prdBatchNo;
    }
    
    
}
