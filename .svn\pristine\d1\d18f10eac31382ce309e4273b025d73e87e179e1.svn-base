package com.kyb.pcberp.modules.icloud.erp.approval.dao;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.icloud.erp.approval.pojo.Icloud_Allocation;

import java.util.List;

@MyBatisDao
public interface Icloud_AllocationDao
{
    /** 根据审批类型查询审批模板 */
    Icloud_Allocation finAllocationByType(Icloud_Allocation allocation);

    /** tj 2019-03-13 查询审批额度 */
    List<Icloud_Allocation> findQuota(Icloud_Allocation allocation);

    /** tj 查询审批面积 */
    List<Icloud_Allocation> findArea(Icloud_Allocation allocation);

    List<Icloud_Allocation> findProfitRate(Icloud_Allocation allocation);
}
