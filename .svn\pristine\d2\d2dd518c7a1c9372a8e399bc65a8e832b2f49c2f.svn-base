package com.kyb.pcberp.modules.wechat.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.stock.entity.Material;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class Maintenance extends DataEntity<Maintenance>
{
    private static final long serialVersionUID = 1L;

    private String preserveNo;

    private String materialId;

    private String problem;

    private String repairMan;

    private String applyMan;

    private String repairStatus;

    private String materialNo;

    private String materialName;

    private String specification;

    private String materialType;

    private String applyName;

    private String userId;

    private String repairName;

    private String statusFlag;

    private String moduleId;

    private String groupId; // 集团管控id

    private String startDate; // 开始日期

    private String endDate; //结束日期

    private Material material;

    private String searchInfo;

    private String applyCompanyId;

    private String repairCompanyId;

    private IcloudCompany  applyCompany;

    private IcloudCompany  repairCompany;

    private String phone;

    private Integer roleFlag;

    private Integer efficiency;

    private Integer massScore;

    private Integer attitudeScore;

    private Integer commmitStatus;

    private String remarkT;

    private List<String> serverList;

    private String maintenanceId;

    private Integer number;

    private String deviceName;

    private String reason;

    private String manageSatus;

    private BigDecimal latestHours;

    private Integer count;

    private BigDecimal hoursAppeal;

    private BigDecimal hoursAppealSum;

    private String monthly;

    private String accessoryContent;

    private Integer dayArrival;

    private String expectStart;

    private List<Maintenance> hourSectionList;

    private Date maintenanceTime;

    //工时套餐
    private Integer hours;

    private Integer money;

    private String content;

    private String repaircompanyName;

    private String applycompanyName;

    private String packageId;

    private BigDecimal useHour;

    private BigDecimal totalHours;

    private BigDecimal maintenanceCost;

    private List<MarkSystemAttach> systemAttachList;

    private Date failureTime;

    private String purchasePlan;

    private BigDecimal useHoursTotal;

    private Integer checkStatus;

    private BigDecimal billedHours; //已结算工时

    private BigDecimal paidPackageHours; //已结结算套餐工时

    private BigDecimal closeHours;

    private BigDecimal closeAccount;

    private Integer num;

    private Integer statusTwo;

    private Integer effectiveDate;

    private BigDecimal sparePartsCost;

    private BigDecimal buyNowPrice;

    private BigDecimal settledPartCost;

    private String confimNum;

    private String superiorsId;

    private String name;

    private String companyId;

    private String employeeId;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getPreserveNo() {
        return preserveNo;
    }

    public void setPreserveNo(String preserveNo) {
        this.preserveNo = preserveNo;
    }

    public String getMaterialId() {
        return materialId;
    }

    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }

    public String getProblem() {
        return problem;
    }

    public void setProblem(String problem) {
        this.problem = problem;
    }

    public String getRepairMan() {
        return repairMan;
    }

    public void setRepairMan(String repairMan) {
        this.repairMan = repairMan;
    }

    public String getApplyMan() {
        return applyMan;
    }

    public void setApplyMan(String applyMan) {
        this.applyMan = applyMan;
    }

    public String getRepairStatus() {
        return repairStatus;
    }

    public void setRepairStatus(String repairStatus) {
        this.repairStatus = repairStatus;
    }

    public String getMaterialNo() {
        return materialNo;
    }

    public void setMaterialNo(String materialNo) {
        this.materialNo = materialNo;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRepairName() {
        return repairName;
    }

    public void setRepairName(String repairName) {
        this.repairName = repairName;
    }

    public String getStatusFlag() {
        return statusFlag;
    }

    public void setStatusFlag(String statusFlag) {
        this.statusFlag = statusFlag;
    }

    public String getModuleId() {
        return moduleId;
    }

    public void setModuleId(String moduleId) {
        this.moduleId = moduleId;
    }

    public String getGroupId()
    {
        return groupId;
    }

    public void setGroupId(String groupId)
    {
        this.groupId = groupId;
    }

    public String getStartDate()
    {
        return startDate;
    }

    public void setStartDate(String startDate)
    {
        this.startDate = startDate;
    }

    public String getEndDate()
    {
        return endDate;
    }

    public void setEndDate(String endDate)
    {
        this.endDate = endDate;
    }

    public Material getMaterial()
    {
        return material;
    }

    public void setMaterial(Material material)
    {
        this.material = material;
    }

    public String getSearchInfo()
    {
        return searchInfo;
    }

    public void setSearchInfo(String searchInfo)
    {
        this.searchInfo = searchInfo;
    }

    public String getApplyCompanyId()
    {
        return applyCompanyId;
    }

    public void setApplyCompanyId(String applyCompanyId)
    {
        this.applyCompanyId = applyCompanyId;
    }

    public String getRepairCompanyId()
    {
        return repairCompanyId;
    }

    public void setRepairCompanyId(String repairCompanyId)
    {
        this.repairCompanyId = repairCompanyId;
    }

    public IcloudCompany getApplyCompany()
    {
        return applyCompany;
    }

    public void setApplyCompany(IcloudCompany applyCompany)
    {
        this.applyCompany = applyCompany;
    }

    public IcloudCompany getRepairCompany()
    {
        return repairCompany;
    }

    public void setRepairCompany(IcloudCompany repairCompany)
    {
        this.repairCompany = repairCompany;
    }

    public String getPhone()
    {
        return phone;
    }

    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public Integer getRoleFlag()
    {
        return roleFlag;
    }

    public void setRoleFlag(Integer roleFlag)
    {
        this.roleFlag = roleFlag;
    }

    public Integer getEfficiency() {
        return efficiency;
    }

    public void setEfficiency(Integer efficiency) {
        this.efficiency = efficiency;
    }

    public Integer getMassScore() {
        return massScore;
    }

    public void setMassScore(Integer massScore) {
        this.massScore = massScore;
    }

    public Integer getAttitudeScore() {
        return attitudeScore;
    }

    public void setAttitudeScore(Integer attitudeScore) {
        this.attitudeScore = attitudeScore;
    }

    public Integer getCommmitStatus() {
        return commmitStatus;
    }

    public void setCommmitStatus(Integer commmitStatus) {
        this.commmitStatus = commmitStatus;
    }

    public String getRemarkT() {
        return remarkT;
    }

    public void setRemarkT(String remarkT) {
        this.remarkT = remarkT;
    }

    public List<String> getServerList() {
        return serverList;
    }

    public void setServerList(List<String> serverList) {
        this.serverList = serverList;
    }

    public String getMaintenanceId() {
        return maintenanceId;
    }

    public void setMaintenanceId(String maintenanceId) {
        this.maintenanceId = maintenanceId;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public List<MarkSystemAttach> getSystemAttachList() {
        return systemAttachList;
    }

    public void setSystemAttachList(List<MarkSystemAttach> systemAttachList) {
        this.systemAttachList = systemAttachList;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getManageSatus() {
        return manageSatus;
    }

    public void setManageSatus(String manageSatus) {
        this.manageSatus = manageSatus;
    }

    public BigDecimal getLatestHours() {
        return latestHours;
    }

    public void setLatestHours(BigDecimal latestHours) {
        this.latestHours = latestHours;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public BigDecimal getHoursAppeal() {
        return hoursAppeal;
    }

    public void setHoursAppeal(BigDecimal hoursAppeal) {
        this.hoursAppeal = hoursAppeal;
    }

    public BigDecimal getHoursAppealSum() {
        return hoursAppealSum;
    }

    public void setHoursAppealSum(BigDecimal hoursAppealSum) {
        this.hoursAppealSum = hoursAppealSum;
    }

    public String getMonthly() {
        return monthly;
    }

    public void setMonthly(String monthly) {
        this.monthly = monthly;
    }

    public String getAccessoryContent() {
        return accessoryContent;
    }

    public void setAccessoryContent(String accessoryContent) {
        this.accessoryContent = accessoryContent;
    }

    public Integer getDayArrival() {
        return dayArrival;
    }

    public void setDayArrival(Integer dayArrival) {
        this.dayArrival = dayArrival;
    }

    public String getExpectStart() {
        return expectStart;
    }

    public void setExpectStart(String expectStart) {
        this.expectStart = expectStart;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getMaintenanceTime() {
        return maintenanceTime;
    }

    public void setMaintenanceTime(Date maintenanceTime) {
        this.maintenanceTime = maintenanceTime;
    }

    public List<Maintenance> getHourSectionList() {
        return hourSectionList;
    }

    public void setHourSectionList(List<Maintenance> hourSectionList) {
        this.hourSectionList = hourSectionList;
    }

    public Integer getHours() {
        return hours;
    }

    public void setHours(Integer hours) {
        this.hours = hours;
    }

    public Integer getMoney() {
        return money;
    }

    public void setMoney(Integer money) {
        this.money = money;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getRepaircompanyName() {
        return repaircompanyName;
    }

    public void setRepaircompanyName(String repaircompanyName) {
        this.repaircompanyName = repaircompanyName;
    }

    public String getApplycompanyName() {
        return applycompanyName;
    }

    public void setApplycompanyName(String applycompanyName) {
        this.applycompanyName = applycompanyName;
    }

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public BigDecimal getUseHour() {
        return useHour;
    }

    public void setUseHour(BigDecimal useHour) {
        this.useHour = useHour;
    }

    public BigDecimal getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(BigDecimal totalHours) {
        this.totalHours = totalHours;
    }

    public BigDecimal getMaintenanceCost() {
        return maintenanceCost;
    }

    public void setMaintenanceCost(BigDecimal maintenanceCost) {
        this.maintenanceCost = maintenanceCost;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getFailureTime() {
        return failureTime;
    }

    public void setFailureTime(Date failureTime) {
        this.failureTime = failureTime;
    }

    public String getPurchasePlan() {
        return purchasePlan;
    }

    public void setPurchasePlan(String purchasePlan) {
        this.purchasePlan = purchasePlan;
    }

    public BigDecimal getUseHoursTotal() {
        return useHoursTotal;
    }

    public void setUseHoursTotal(BigDecimal useHoursTotal) {
        this.useHoursTotal = useHoursTotal;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public BigDecimal getCloseHours() {
        return closeHours;
    }

    public void setCloseHours(BigDecimal closeHours) {
        this.closeHours = closeHours;
    }

    public BigDecimal getCloseAccount() {
        return closeAccount;
    }

    public void setCloseAccount(BigDecimal closeAccount) {
        this.closeAccount = closeAccount;
    }

    public BigDecimal getBilledHours() {
        return billedHours;
    }

    public void setBilledHours(BigDecimal billedHours) {
        this.billedHours = billedHours;
    }

    public BigDecimal getPaidPackageHours() {
        return paidPackageHours;
    }

    public void setPaidPackageHours(BigDecimal paidPackageHours) {
        this.paidPackageHours = paidPackageHours;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Integer effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Integer getStatusTwo() {
        return statusTwo;
    }

    public void setStatusTwo(Integer statusTwo) {
        this.statusTwo = statusTwo;
    }

    public BigDecimal getSparePartsCost() {
        return sparePartsCost;
    }

    public void setSparePartsCost(BigDecimal sparePartsCost) {
        this.sparePartsCost = sparePartsCost;
    }

    public BigDecimal getBuyNowPrice() {
        return buyNowPrice;
    }

    public void setBuyNowPrice(BigDecimal buyNowPrice) {
        this.buyNowPrice = buyNowPrice;
    }

    public BigDecimal getSettledPartCost() {
        return settledPartCost;
    }

    public void setSettledPartCost(BigDecimal settledPartCost) {
        this.settledPartCost = settledPartCost;
    }

    public String getConfimNum() {
        return confimNum;
    }

    public void setConfimNum(String confimNum) {
        this.confimNum = confimNum;
    }

    public String getSuperiorsId() {
        return superiorsId;
    }

    public void setSuperiorsId(String superiorsId) {
        this.superiorsId = superiorsId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getCompanyId() {
        return companyId;
    }

    @Override
    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }
}
