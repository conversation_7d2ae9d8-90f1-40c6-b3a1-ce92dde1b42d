package com.kyb.pcberp.modules.contract.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.crm.entity.AccountsAttachements;

public class DeliveryAttachements extends DataEntity<DeliveryAttachements> {

    private static final long serialVersionUID = 1L;

    private String deliveryId ; // 送货单明细id

    private String orgFileName; // 文件名称

    private String realFileName; // 路径名称

    private String fileUrl; //文件路径

    public String getOrgFileName() {
        return orgFileName;
    }

    public void setOrgFileName(String orgFileName) {
        this.orgFileName = orgFileName;
    }

    public String getRealFileName() {
        return realFileName;
    }

    public void setRealFileName(String realFileName) {
        this.realFileName = realFileName;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getDeliveryId() {
        return deliveryId;
    }

    public void setDeliveryId(String deliveryId) {
        this.deliveryId = deliveryId;
    }
}
