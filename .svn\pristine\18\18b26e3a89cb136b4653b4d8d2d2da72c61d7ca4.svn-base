package com.kyb.pcberp.modules.production.dao;

import java.util.List;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.production.entity.SupplierProcessPopo;
import com.kyb.pcberp.modules.production.entity.SupplierProcessPopoCraft;
import com.kyb.pcberp.modules.production.entity.SupplierProcessPopoPrice;
import com.kyb.pcberp.modules.purch.entity.Supplier;

/**
 * 供应商外发工序设置DAO
 * 
 * <AUTHOR> 2018-05-08
 *        
 */
@MyBatisDao
public interface SupplierProcessPopoDao extends CrudDao<SupplierProcessPopo>
{
    
    void insertPrice(SupplierProcessPopoPrice popoPrice);
    
    void insertCraft(SupplierProcessPopoCraft popoCraft);

    /**
     * 查询供应商工序是否过设置
     */
    public Integer getRecords(SupplierProcessPopo popo);
    
    /**
     * 查询供应商工序设置
     */
    public SupplierProcessPopo getPopoBySupplierProcess(SupplierProcessPopo popo);
    
    /**
     * 删除计价规则
     */
    void deletePrice(SupplierProcessPopo popo);
    
    /**
     * 删除违约规则
     */
    void deleteDefault(SupplierProcessPopo popo);
    
    /**
     * 删除工艺规则
     */
    void deleteCraft(SupplierProcessPopo befoPopo);
    
    /**
     * 查询供应商的外发设置
     */
    List<SupplierProcessPopo> getPopoBySupplier(Supplier supplier);

}
