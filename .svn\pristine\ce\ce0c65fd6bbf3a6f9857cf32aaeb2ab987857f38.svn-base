const reportMonth = {
	template:'#reportMonth',
	computed: {
		yearList:{
			get () {
				return this.$store.state.myStore.yearList
		    }
		},
		empList: {
			get () {
				return this.$store.state.auditStore.empList
		    }
		},
		emp: {
			get () {
				return this.$store.state.myStore.emp
		    }
		},
		report: {
			get () {
				return this.$store.state.auditStore.report
		    }
		},
		years:{
			get () {
				return this.$store.state.myStore.year
		    }
		},
		months:{
			get () {
				return this.$store.state.myStore.month
		    }
		},
		shareList: {
			get () {
				return this.$store.state.auditStore.shareList
		    }
		},
		phone: {
			get() {
				return this.$store.state.myStore.phone
			}
		}
	},
	data(){
		return {
			monthList: [],
			weekList: [],
			year: '',
			month: '',
			shareId: [],
			localIds: [],
			serverList: [],
			localIdsCopy: [],
			superName: '',
			leadershipsName: '',
			clickFlag:false,
			superId: '',
			leadershipsId: ''
		}
	},
	created(){
		this.getWxMsg()
		this.$store.dispatch('myStore/setUserInformation')
		this.$store.dispatch('myStore/setYearList')
		this.superiorLeaders()
	},
	watch: {
		emp: function () {
			this.$store.dispatch('auditStore/setEmpList', this.emp)
	    },
	    empList:function () {
	    	this.initMonth()
			this.setMonth()
			this.initEmpList()
	    },
	    shareList:function(){
	    	this.shareId = []
			if (this.superId != null)
			{
				this.shareId.push(this.superId)
			}
			if (this.leadershipsId != null)
			{
				this.shareId.push(this.leadershipsId)
			}
	    	if(this.shareList != null && this.shareList.length > 0){
    			for(var i=0;i<this.shareList.length;i++){
    				this.shareId.push(this.shareList[i].recordId)
    				$('#shareEmp').val(this.shareId)
    			}
    		}else{
    			$('#shareEmp').val(this.shareId)
    		}
    		$('#shareEmp').selectpicker('refresh')
	    }
	},
	methods:{
		getWxMsg:function()
		{
			var config = {}
			config.url = location.href.split('#')[0]
			var _this = this
			$.ajax({
	     		type:"post",
	     		url:ctx + "/f/wechat/produce/getWxMsg",
	     		data:JSON.stringify(config),
	     		contentType:"application/json",
	     		success:function(data)
	     		{
	     			_this.wxConfig(data)
	     		}
	     	})
		},
		wxConfig: function (data) {
			wx.config({
		        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端console.log出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
		        appId: data.appId, // 必填，公众号的唯一标识
		        timestamp: data.timestamp, // 必填，生成签名的时间戳
		        nonceStr: data.nonceStr, // 必填，生成签名的随机串
		        signature: data.signature, // 必填，签名
		        jsApiList: [
		          'checkJsApi', 'chooseImage', 'previewImage', 'uploadImage', 'downloadImage'
		        ]
			})
	    },
		chooseImg:function(){
			var _this = this
			wx.chooseImage({
				count: 9, // 默认9
				sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
				success: function (res) {
					if(_this.localIds && _this.localIds.length > 0){
						for(var i=0;i< res.localIds.length;i++){
							_this.localIds.push(res.localIds[i])
						}
					}else{
						_this.localIds = res.localIds // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
					}
					// 上传图片
					_this.serverList = []
					_this.localIdsCopy = []
					for (let i = 0; i < _this.localIds.length; i++) {
						_this.localIdsCopy.push(_this.localIds[i])
					}
					_this.uploadImage()
				}
			});
		},
		preview:function(item){
			var _this = this
			wx.previewImage({
				current: item, // 当前显示图片的http链接
				urls: _this.localIds // 需要预览的图片http链接列表
			});
		},
		uploadImage:function(){
			var vm = this
			if(vm.localIdsCopy && vm.localIdsCopy.length > 0){
				wx.uploadImage({
					localId: vm.localIdsCopy.pop(), // 需要上传的图片的本地ID，由chooseImage接口获得
					isShowProgressTips: 1, // 默认为1，显示进度提示
					success: function (res) {
						vm.serverList.push(res.serverId)
						vm.uploadImage()
					}
				});
			}
		},
		deleteImg:function(index){
			this.localIds.splice(index,1)
		},
		initEmpList:function(){
			if (this.empList && this.empList.length > 0) {
	    		let option = ''
	    	    option += "<option value=''>" + '请选择' + '</option>'
	    	    this.empList.forEach(el => {
	    	    	option += "<option value='" + el.recordId + "'>" + el.name + '</option>'
	    	    })
	    	    $('#shareEmp').empty()
	    	    $('#shareEmp').append(option)
	    	    $('#shareEmp').selectpicker('render')
	    	    $('#shareEmp').selectpicker('refresh')
	    	    $('#shareEmp').selectpicker()
	    	}
		},
		initMonth:function(){
			this.year = this.years
			this.month = this.months
			this.getMonth()
		},
		setMonth:function(){
			for(let i=0;i<this.monthList.length;i++){
				if(this.month == this.monthList[i].id){
					var query = {}
					query.day = this.monthList[i].queryDate
					query.flag = 3
					query.employeeId = this.emp.recordId;
					query.recordId = ""
		    		this.$store.dispatch('auditStore/setReport', query)
					break;
				}
			}
		},
		getMonth: function(){
			for(let i in this.yearList){
				if(this.year == this.yearList[i].id){
					this.monthList = this.yearList[i].monthList;
					break;
				}
			}
		},
		cancle:function(){
			this.$router.push('/submit');
		},
		submit:function(){
			if(this.clickFlag)
			{
				alert("请勿多次点击！");
				return;
			}
			if(!this.report.day){
				alert("请选择报表时间！");
				return;
			}
			if(!this.report.content){
				alert("请填写已完成工作！");
				return;
			}
			if(this.shareId && this.shareId.length > 0){
				this.report.sharesList = [];
				for(let i in this.shareId){
					var s = {};
					s.recordId = this.shareId[i];
					if(s.recordId && s.recordId != "")
					{
						this.report.sharesList.push(s);
					}
				}
			}
			this.report.serverList = this.serverList
			this.clickFlag = true;
			var router = this.$router;
			var _this = this;
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftOA/saveReport",
		     	data:JSON.stringify(this.report),
		     	contentType:"application/json",
		     	success:function(data)
		     	{ 
		     		if(data == "success"){
		     			alert("提交成功");
		     			router.push('/submit');
		     		}else{
		     			alert(data);
		     		}
					_this.clickFlag = false;
		     	}
		    })
		},
		//通过当前登录人电话获取直接上级
		superiorLeaders: function () {
			const phone = {};
			phone.phone = this.phone
			const _this = this;
			$.ajax({
				type: "post",
				url: ctx + "/f/wechat/kybsoftOA/getSuperiorLeaders",
				data: JSON.stringify(phone),
				contentType: "application/json",
				success: function (data) {
					if (data)
					{
						_this.superName = data.superName;
						_this.leadershipsName = data.leadershipsName;
						_this.superId = data.superId;
						_this.leadershipsId = data.leadershipsId;
					}
				}
			})
		}
	}
}