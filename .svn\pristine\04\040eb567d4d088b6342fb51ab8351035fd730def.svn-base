<%@ page contentType="text/html;charset=UTF-8"%>
<div ng-intro-options="introListOptions" ng-intro-method="helpList" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="introDetailOptions" ng-intro-method="helpDetail" ng-intro-autostart="shouldAutoStart"></div>

<!-- BEGIN PAGE HEADER-->
<div class="page-bar page-bar-margin-top">
	<ul class="page-breadcrumb">
		<li>
			<i class="fa fa-home"></i>
			<a href="#/dashboard.html">主页</a>
			<i class="fa fa-angle-right"></i>
		</li>
		<li>
			<a href="javascript:void(0);">系统管理</a>
			<i class="fa fa-angle-right"></i>
		</li>
		<li>
			<a ui-sref="sys.codegen">编码管理</a>
		</li>
	</ul>

	<div class="page-toolbar">
		<button class="btn btn-fit-height default pull-right" ng-click="ctrl.help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>

</div>

<!-- END 导航-->
<tabset class="tabset-margin-top">
	<tab heading="编码列表" active="ctrl.tabs.viewForm.active">
		<div class="portlet light bordered">
			<div class="portlet-title">
				<div class="caption font-blue-hoki">
					<div class="caption font-blue-hoki">编码列表
						<label style="color: red;">&nbsp;&nbsp;&nbsp;&nbsp;*系统对各项单据均采用默认编码，点击设置编码,设置属于您的编码</label>
					</div>

				</div>
				<div class="actions">
				</div>
			</div>
			<div class="portlet-body" id="step1">
				<div class="form-horizontal">
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">客户编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[1].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons" id="step2">
									<label class="btn blue" ng-click="ctrl.checkKhbm(1)" data-toggle="modal" data-target="#myModal"> <input 
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>

					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">供应商编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[2].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(2)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>

					<!-- 合同 -->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">合同编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[3].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(3)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>

					<!-- 通知单 -->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">通知单编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[4].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label id="tzd1" class="btn blue" ng-click="ctrl.checkKhbm(4)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 生产编码 -->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">生产编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[5].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label id="scbh1" class="btn blue" ng-click="ctrl.checkKhbm(5)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 送货单编码 -->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">送货单编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[6].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label id="shd1" class="btn blue" ng-click="ctrl.checkKhbm(6)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 客户投诉编码-->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">客户投诉编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[7].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(7)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 采购订单编码-->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">采购订单编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[8].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(8)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 退货单编码-->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">退货单编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[9].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(9)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 付款申请编码-->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">付款申请编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[10].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label id="fk1" class="btn blue" ng-click="ctrl.checkKhbm(10)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 物料编码(原料)-->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">原料编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[11].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(11)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 补料单-->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">补料单：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[12].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(12)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 报价单-->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">报价单：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[13].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(13)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 付款记录单-->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">付款记录单：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[14].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(14)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 收款记录单-->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">收款记录单：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[15].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(15)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 物流编码 -->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">物流编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[16].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps"> 
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(16)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>

					<!-- 投料单编码 -->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">投料单编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[17].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(17)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>

					<!-- 成品物料编码 -->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">成品物料编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[18].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(18)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 来料检测单号 -->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">来料检测单号：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[19].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(19)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>

					<!-- 客户下单 -->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">客户下单单号：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[20].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(20)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 物料领用单 -->
					<div class="row" style="margin-top: 10px;">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">物料领用单单号：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name"
										placeholder="单据名+8位自增码" ng-model="ctrl.codes[21].regexDesc"
										readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label  class="btn blue" ng-click="ctrl.checkKhbm(21)"
										data-toggle="modal" data-target="#myModal"> <input
										type="radio" class="toggle"> 设置编码
									</label>
								</div>
							</div>
						</div>
					</div>
					
					<!-- 原料申请编码 -->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">原料申请编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[22].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(22)" data-toggle="modal" data-target="#myModal"> <input
								type="radio" class="toggle"> 设置编码
							</label>
								</div>
							</div>
						</div>
					</div>

                    <!-- 生产外发加工单编码 -->
					<div class="row">
						<div class="col-md-7">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label text-right">外发加工单编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[23].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(23)" data-toggle="modal" data-target="#myModal">
                                        <input type="radio" class="toggle"> 设置编码
							        </label>
								</div>
							</div>
						</div>
					</div>
					
					 <!-- 入职申请表编码 -->
					<div class="row" >
						<div class="col-md-7">
							<div class="form-group" >
								<label class="col-sm-3 col-md-4 control-label text-right">员工编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[24].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(24)" data-toggle="modal" data-target="#myModal">
                                        <input type="radio" class="toggle"> 设置编码
							        </label>
								</div>
							</div>
						</div>
					</div>
					
					<!-- 应收单请表编码 -->
					<div class="row" >
						<div class="col-md-7">
							<div class="form-group" >
								<label class="col-sm-3 col-md-4 control-label text-right">应收单编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[28].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(28)" data-toggle="modal" data-target="#myModal">
                                        <input type="radio" class="toggle"> 设置编码
							        </label>
								</div>
							</div>
						</div>
					</div>
					<!-- 应付单请表编码 -->
					<div class="row" >
						<div class="col-md-7">
							<div class="form-group" >
								<label class="col-sm-3 col-md-4 control-label text-right">应付单编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[29].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(29)" data-toggle="modal" data-target="#myModal">
                                        <input type="radio" class="toggle"> 设置编码
							        </label>
								</div>
							</div>
						</div>
					</div>
					<!-- 尾数管理编码 -->
					<div class="row" >
						<div class="col-md-7">
							<div class="form-group" >
								<label class="col-sm-3 col-md-4 control-label text-right">尾数管理编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[30].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(30)" data-toggle="modal" data-target="#myModal">
                                        <input type="radio" class="toggle"> 设置编码
							        </label>
								</div>
							</div>
						</div>
					</div>
					<!-- 方案编码 -->
					<div class="row" >
						<div class="col-md-7">
							<div class="form-group" >
								<label class="col-sm-3 col-md-4 control-label text-right">方案编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[31].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(31)" data-toggle="modal" data-target="#myModal">
										<input type="radio" class="toggle"> 设置编码
									</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 市场备料 -->
					<div class="row" >
						<div class="col-md-7">
							<div class="form-group" >
								<label class="col-sm-3 col-md-4 control-label text-right">市场备料编码：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" id="name" placeholder="单据名+8位自增码" ng-model="ctrl.codes[32].regexDesc" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="col-md-1" ng-if="ctrl.apps">
							<div class="clearfix">
								<div class="btn-group" data-toggle="buttons">
									<label class="btn blue" ng-click="ctrl.checkKhbm(32)" data-toggle="modal" data-target="#myModal">
										<input type="radio" class="toggle"> 设置编码
									</label>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</tab>
	
	<tab active="ctrl.tabs.editForm.active" ng-show="ctrl.tabs.editForm.show">
		<tab-heading>
			编码详情 <i style="cursor: pointer" class="fa fa-times" ng-click="ctrl.hideEditForm()"></i> </tab-heading>

		<div class="rows">
			<form class="form-horizontal" name="editForm" ng-submit="ctrl.testData()">

				<div class="panel panel-default">
					<div class="panel-heading font-blue-hoki" id="stepDetail1">{{ctrl.showType}}</div>
					<div class="panel-body">
						<div class="row" style="margin-top: 10px;">
							<div class="col-md-6 col-lg-4">
								<div class="form-group" id="stepDetail2">
									<label class="col-sm-3 col-md-4 control-label">具体规则：</label>
									<div class="col-sm-7 col-md-8">
										<span disabled class="form-control text-center" ng-if="ctrl.showno != 5">{{ctrl.type.name }}+{{ctrl.type1.name }}+{{ctrl.type2.name}}</span>
										<span disabled class="form-control text-center" ng-if="ctrl.showno == 5">{{ctrl.type.name }}+{{ctrl.type0.name1 }}+{{ctrl.type1.name }}+{{ctrl.type2.name}}</span>
									</div>
								</div>
							</div>
	
							<div class="col-md-6 col-lg-4">
								<div class="form-group" id="stepDetail3">
									<label class="col-sm-3 col-md-4 control-label">编码实例：</label>
									<div class="col-sm-7 col-md-8">
										<div class="input-group" ng-show="ctrl.canShow">
											<input type="text" class="form-control" 
												placeholder="请在这里输入固定缩写"
												ng-model="ctrl.fixation" 
												disable-valid-styling="true"
	                                            disable-invalid-styling="true">
											<span class="input-group-addon" ng-if="ctrl.showno != 5">
											{{ctrl.type.eg}}&nbsp;{{ctrl.type1.eg}}&nbsp;{{ctrl.type2.eg}}
											</span>
											<span class="input-group-addon" ng-if="ctrl.showno == 5">
											{{ctrl.type.eg}}&nbsp;{{ctrl.type0.eg}}&nbsp;{{ctrl.type1.eg}}&nbsp;{{ctrl.type2.eg}}
											</span>
										</div>
										<span disabled class="form-control text-center" ng-show="!ctrl.canShow && ctrl.showno != 5">{{ctrl.type.eg}}&nbsp;{{ctrl.type1.eg}}&nbsp;{{ctrl.type2.eg}}</span>
										<span disabled class="form-control text-center" ng-show="!ctrl.canShow && ctrl.showno == 5">{{ctrl.type.eg}}&nbsp;{{ctrl.type0.eg}}&nbsp;{{ctrl.type1.eg}}&nbsp;{{ctrl.type2.eg}}</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="panel panel-default">
					<div class="panel-heading font-blue-hoki">设置编码规则</div>
					<div class="panel-body">
						
						<div class="form-horizontal" id="stepDetail4">
							<div class="form-group">
		                        <label class="col-sm-3 control-label">规则一：</label>
		                        <div class="col-sm-7">
		                            <select class="form-control" ng-model="ctrl.type" ng-options="c.name for c in ctrl.types" ng-change="ctrl.value()"
		                            	disable-valid-styling="true"
                                    	disable-invalid-styling="true"></select>
		                        </div>
		                    </div>
		                    <div class="form-group" ng-if="ctrl.showno == 5">
		                        <label class="col-sm-3 control-label">规则二：</label>
		                        <div class="col-sm-7">
		                            <select class="form-control" ng-model="ctrl.type0" ng-options="c.name for c in ctrl.types0"
		                            	disable-valid-styling="true"
                                    	disable-invalid-styling="true"></select>
		                        </div>
		                    </div>
		                    <div class="form-group">
		                        <label class="col-sm-3 control-label" ng-if="ctrl.showno == 5">规则三：</label>
		                        <label class="col-sm-3 control-label" ng-if="ctrl.showno != 5">规则二：</label>
		                        <div class="col-sm-7">
		                            <select class="form-control" ng-model="ctrl.type1" ng-options="c.name for c in ctrl.types1" 
		                            	disable-valid-styling="true"
                                    	disable-invalid-styling="true"></select>
		                        </div>
		                    </div>
		                    <div class="form-group">
		                        <label class="col-sm-3 control-label" ng-if="ctrl.showno == 5">规则四：</label>
		                        <label class="col-sm-3 control-label" ng-if="ctrl.showno != 5">规则三：</label>
		                        <div class="col-sm-7">
		                            <select class="form-control" ng-model="ctrl.type2" ng-options="c.name for c in ctrl.types2" 
		                            	disable-valid-styling="true"
                                    	disable-invalid-styling="true"></select>
		                        </div>
		                    </div>
		                    
		                     <div class="form-group" ng-if="ctrl.type1.id == '07' || ctrl.type1.id == '08'">
		                       <!--  <label class="col-sm-3 control-label" ng-if="ctrl.showno == 5 && ctrl.">规则五：</label> -->
		                        <label class="col-sm-3 control-label" ng-if="ctrl.showno == 3 || ctrl.showno == 4">规则四：</label>
		                        <div class="col-md-4" ng-if="ctrl.apps">
									<checkbox  ng-model = "ctrl.codeList[ctrl.showno].selfDefaultCodegen" ng-click="ctrl.Codegen(ctrl.showno)"></checkbox>月初重置编码 
								</div>
		                    </div>
		                    
		                     <div class="form-group" ng-if="ctrl.right.edit" id="stepDetail5">
		                        <div class="col-sm-offset-4 col-sm-10">
			                        <button type="button" class="btn grey-cascade btn-default-width" ng-click="ctrl.chongzhi()">
										<i class="fa fa-undo"></i> 重置编码
									</button>
								
			                        <button type="submit" class="btn btn-primary btn-default-width" style="margin-left: 0px;">
										<i class="fa fa-save"></i> 保&nbsp;存
									</button>
								
		                        </div>
		                    </div>
						</div>

						<div class="row">
							<div class="col-md-12 ">
								<div class="portlet light">
									<div class="portlet-title">
										<div class="caption">
											<div class="caption font-blue-hoki">操作规则</div>
										</div>
										<div class="inputs">
											<div class="portlet-input input-inline input-small">
												<div class="input-icon right"></div>
											</div>
										</div>
									</div>
									<div class="portlet-body">
										<div class="scroller" style="height: 90px">
											<p>
												<span class="badge badge-success"> 1 </span>.设置编码规则下依次选择规则头(必选),规则中间,规则尾部(必选)
											</p>
											<label style="color: red;"><p>
													<span class="badge badge-success"> 2 </span>.选择固定缩写请在固定编码输入框中输入您需要的编码
												</p></label>

											<p>
												<span class="badge badge-success"> 3 </span>.具体规则框中显示的是您将保存的编码规则,编码实例显示具体编码,点击保存以完成本次操作
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</form>
		</div>
	</tab>
</tabset>

<div class="row">
	<div class="col-md-12">
		<div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">提示</h4>
					</div>
					<div class="modal-body">
						<p>
							<span ng-bind="ctrl.message"></span>
						</p>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="staticRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">
							<strong>提示</strong>
						</h4>
					</div>
					<div class="modal-body">
						<p>
							<strong>确认删除本条记录吗？</strong>
						</p>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn btn-danger" ng-click="deleteCondegen()">删除</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="staticchongzhi" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">重置编码</h4>
					</div>
					<div class="modal-body">
						<div class="col-sm-1 text-center">
							<i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i>
						</div>
						确定要重置本编码规则为系统默认编码吗？
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.dochongzhi()">确认
                        </button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>