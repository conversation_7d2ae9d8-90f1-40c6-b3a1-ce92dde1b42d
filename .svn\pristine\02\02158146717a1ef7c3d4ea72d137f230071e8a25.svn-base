const report_common = {
    template: '#report_common',
    created () {
        let report = eval('(' + window.localStorage.getItem('report') + ')');
        this.reportName = report.name;
        this.phone = report.phone;
        this.dateA = report.timeFlag;
        if(this.dateA == 4)
        {
            this.getDate();
        }
        else
        {
            this.getReportDetailTotal();
        }
    },
    computed: {

    },
    mounted:function(){

    },
    watch: {
        startTime: function () {
            this.getReportDetailTotal();
        },
        endTime: function () {
            this.getReportDetailTotal();
        },
    },
    data(){
        return {
            dateA:1,
            showType:1,
            dimensionType:2,
            dataMap: new Map(),
            keyList:[],
            reportName:"",
            analyticalNum:3,
            dataMapTwo:new Map(),
            listDate:[],
            dateAllDetail:[],
            ttListDate:[],
            oneObj:{},
            twoObj:{},
            btType:1,
            dataMap:{},
            testOne:{},
            phone:null,
            nameList:[],
            startTime:null,
            endTime:null,
            errorMsg: ''
        }
    },
    methods: {
        getReportDetailTotal:function(){
            const query ={}
            query.name = this.reportName;
            query.dateType = this.dateA;
            query.phone = this.phone;
            query.showType = this.showType;
            query.dimensionType = this.dimensionType;
            query.analyticalNum = this.analyticalNum;
            if (this.dateA == 4){
                if(this.startTime)
                {
                    query.sentTimeStartQr = this.startTime;
                }
                if(this.endTime)
                {
                    query.sentTimeEndQr = this.endTime;
                }
            }
            if(this.reportName == "采购"){
                query.dimensionType = 3;
            }
            if(this.analyticalNum == 0){
                this.analyticalNum = 0;
                this.errorMsg = "分析数不能为0";
            }else {
                this.errorMsg = '';
                const _this = this;
                $.ajax({
                    type: "post",
                    url: ctx + "/f/wechat/kybReport/getReportTotal",
                    data: JSON.stringify(query),
                    contentType: "application/json",
                    success: function (data) {
                        _this.dataMap = data;
                        _this.dataMapTwo = data.dataTwo;

                        // 获取名称集合
                        _this.getNameList();

                        // 获取第一个数据
                        _this.getOneData();

                        // 获取第二个列表数据
                        _this.getTwoDataList();

                        _this.getPictureOne();

                        _this.getPictureTwo();

                    }
                })
            }
        },
        selectDateType:function()
        {
            // 加载时间区间
            if(this.dateA == 4)
            {
                this.getDate();
            }
            else
            {
                this.getReportDetailTotal();
            }
        },
        // 获取名称集合
        getNameList:function()
        {
            let nameList = [];
            for (let key in this.dataMapTwo)
            {
                for (let obj of this.dataMapTwo[key])
                {
                    let flag = true;
                    if(nameList && nameList.length > 0)
                    {
                        for(let name of nameList)
                        {
                            if(name == obj.departName)
                            {
                                flag = false;
                                break;
                            }
                        }
                    }
                    if(flag)
                    {
                        nameList.push(obj.departName);
                    }
                }
            }
            this.nameList = nameList;
        },
        handleIncrease:function(oneValue,twoValue)
        {
            let increase = 0;
            if(!twoValue)
            {
                return increase;
            }
            if(!oneValue)
            {
                oneValue = 0;
            }
            increase =(Number(oneValue) - Number(twoValue)) / Number(twoValue) * 100;
            return increase.toFixed(2);
        },
        getOneData:function()
        {
            this.oneObj = {};
            this.twoObj = {};
            let nowStr = this.dataMap.nowStr;
            let yesterStr = this.dataMap.yesterStr;
            for(let key in this.dataMapTwo)
            {
                if(!key || !(key == nowStr || key == yesterStr))
                {
                    continue;
                }
                let data = {};
                data.date = key;
                data.amount = 0;
                data.money = 0;
                data.area = 0;
                data.pcsQty = 0;
                data.deptName = null;
                for(let obj of this.dataMapTwo[key])
                {
                    if(obj.date == nowStr)
                    {
                        if(obj.amount)
                        {
                            data.amount += Number(obj.amount);
                        }
                        if(obj.money){
                            data.money += Number(obj.money);
                        }
                        if (obj.area){
                            data.area += Number(obj.area);
                        }
                        if (obj.pcsQty){
                            data.pcsQty += Number(obj.pcsQty);
                        }
                        if(data.deptName)
                        {
                            if(!data.deptName.concat(obj.departName))
                            {
                                data.deptName = data.deptName +","+ obj.departName;
                            }
                        }
                        else
                        {
                            data.deptName = obj.departName;
                        }
                    }
                    if(obj.date == yesterStr)
                    {
                        if(obj.amount)
                        {
                            data.amount += Number(obj.amount);
                        }
                        if(obj.money){
                            data.money += Number(obj.money);
                        }
                        if (obj.area){
                            data.area += Number(obj.area);
                        }
                        if (obj.pcsQty){
                            data.pcsQty += Number(obj.pcsQty);
                        }
                        if(data.deptName)
                        {
                            if(!data.deptName.concat(obj.departName))
                            {
                                data.deptName = data.deptName +","+ obj.departName;
                            }
                        }
                        else
                        {
                            data.deptName = obj.departName;
                        }
                    }
                }
                if(key == nowStr)
                {
                    data.money = data.money.toFixed(2);
                    data.area = data.area.toFixed(4);
                    this.oneObj = data;
                }
                else if(key == yesterStr)
                {
                    data.money = data.money.toFixed(2);
                    data.area = data.area.toFixed(4);
                    this.twoObj = data;
                }
            }
        },
        //最下面图汇总
        getTwoDataList:function()
        {
            this.listDate = [];

            // 根据名称集合进行数据汇总
            for(let name of this.nameList) {
                if(name == "无")
                {
                    continue;
                }
                let data = {};
                data.departName = name;
                data.amount = 0;
                data.money = 0;
                data.area = 0;
                data.pcsQty = 0;
                for (let key in this.dataMapTwo) {
                    for (let obj of this.dataMapTwo[key]) {
                        if (name == obj.departName) {
                            if (obj.amount) {
                                data.amount += Number(obj.amount);
                            }
                            if (obj.money) {
                                data.money += Number(obj.money);
                            }
                            if (obj.area) {
                                data.area += Number(obj.area);
                            }
                            if (obj.pcsQty) {
                                data.pcsQty += Number(obj.pcsQty);
                            }
                        }
                    }
                }
                data.area = Number(data.area.toFixed(4));
                data.money = Number(data.money.toFixed(2));
                this.listDate.push(data);
            }
            this.listDate.sort((a,b)=>{
                return b.area < a.area? -1:1;
            });
        },

        setBtType:function(type)
        {
            echarts.dispose(document.getElementById('defectRate'));
            this.btType = type;
            if (type == 1)
            {

            }else if (type == 2)
            {
                this.getPictureTwo();
            }else if(type == 2)
            {
                this.getPictureOne();
            }
        },
        getPictureOne:function () {
            let list = [];
            this.btType = 3;
            for(let name of this.nameList)
            {
                if(name == "无")
                {
                    continue;
                }
                let data = {};
                data.deptName = name;
                data.value = 0;
                for(let key in this.dataMapTwo)
                {
                    for(let obj of this.dataMapTwo[key])
                    {
                        if(name == obj.departName)
                        {
                            switch (this.showType.toString()) {
                                // 面积
                                case "1":
                                    if(obj.area)
                                    {
                                        data.value += Number(obj.area);
                                    }
                                    break;
                                // 款数
                                case "2":
                                    if(obj.amount)
                                    {
                                        data.value += Number(obj.amount);
                                    }
                                    break;
                                // 金额
                                case "3":
                                    if(obj.money)
                                    {
                                        data.value += Number(obj.money);
                                    }
                                    break;
                                // 数量
                                case "4":
                                    if(obj.pcsQty)
                                    {
                                        data.value += Number(obj.pcsQty);
                                    }
                                    break;
                            }
                        }
                    }
                }
                if(this.showType.toString() == "1")
                {
                    data.value = Number(data.value.toFixed(4));
                }
                else if(this.showType.toString() == "3")
                {
                    data.value = Number(data.value.toFixed(2));
                }
                list.push(data);
            }

            list.sort((a,b)=>{
                return b.value > a.value? -1:1;
            });
            const xAxisData = list.map(item => item.deptName);
            const seriesData = list.map(item => item.value);
            this.barChat = echarts.init(document.getElementById('defectRate'));
            option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.01]
                },
                yAxis: {
                    type: 'category',
                    data: xAxisData,
                    axisLabel: {
                        show: false
                    }
                },
                dataZoom:
                    [{
                        type: "slider",
                        realtime: true, // 拖动时，是否实时更新系列的视图
                        startValue: 0,
                        endValue: 5,
                        width: 5,
                        height: "90%",
                        top: "5%",
                        right: 0,
                        // orient: 'vertical', // 设置横向还是纵向, 但是官方不太建议如此使用，建议使用 yAxisIndex 具体指明
                        yAxisIndex: [0, 1], // 控制y轴滚动对象
                        fillerColor: "#0093ff", // 滚动条颜色
                        borderColor: "rgba(17, 100, 210, 0.12)",
                        backgroundColor: "#cfcfcf", //两边未选中的滑动条区域的颜色
                        handleSize: 0, // 两边手柄尺寸
                        showDataShadow: false, //是否显示数据阴影 默认auto
                        showDetail: false, // 拖拽时是否展示滚动条两侧的文字
                        zoomLock: true,
                        moveHandleStyle: {
                            opacity: 0,
                        },
                    },
                        {
                            type: "inside",
                            // width: 0,
                            startValue: 0,
                            endValue: 10,
                            minValueSpan: 10,
                            yAxisIndex: [0],
                            showDetail: false,
                            zoomOnMouseWheel: false, // 关闭滚轮缩放
                            moveOnMouseWheel: true, // 开启滚轮平移
                            moveOnMouseMove: true, // 鼠标移动能触发数据窗口平移
                        }],
                series: [
                    {
                        label:{
                            show: true,
                            color: '#fff',
                            formatter: '{b}-{c}',
                            fontSize: 10,
                            align:'left',
                            padding:[0,0,0,20],
                            position:'left',
                            textStyle: {
                                color: '#000000',
                                fontWeight:'bold'
                            }
                        },
                        type: 'bar',
                        data:seriesData
                    }
                ]
            };
            this.barChat.setOption(option);
        },
        getPictureTwo:function () {
            let list = [];
            this.btType = 2;
            for(let key in this.dataMapTwo)
            {
                if(!key)
                {
                    continue;
                }
                let data = {};
                data.date = key;
                data.value = 0;
                for(let obj of this.dataMapTwo[key])
                {
                    switch (this.showType.toString()) {
                        // 面积
                        case "1":
                            if(obj.area)
                            {
                                data.value += Number(obj.area);
                            }
                            break;
                        // 款数
                        case "2":
                            if(obj.amount)
                            {
                                data.value += Number(obj.amount);
                            }
                            break;
                        // 金额
                        case "3":
                            if(obj.money)
                            {
                                data.value += Number(obj.money);
                            }
                            break;
                        // 数量
                        case "4":
                            if(obj.pcsQty)
                            {
                                data.value += Number(obj.pcsQty);
                            }
                            break;
                    }
                }
                if(this.showType.toString() == "1")
                {
                    data.value = Number(data.value.toFixed(4));
                }
                else if(this.showType.toString() == "3")
                {
                    data.value = Number(data.value.toFixed(2));
                }
                list.push(data);
            }
            list.sort((a,b)=>{
                return b.date > a.date? -1:1;
            });
            const xAxisData = list.map(item => item.date);
            const seriesData = list.map(item => item.value);
            this.discountedGraph = echarts.init(document.getElementById('defectRate'));
            option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: xAxisData
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        data: seriesData,
                        type: 'line'
                    }
                ]
            };
            this.discountedGraph.setOption(option);
        },
        modelType:function(num){
            let status = false;
            if(!num)
            {
                return status;
            }
            let dimensionType = null;
            switch (this.reportName) {
                case "接单":
                    dimensionType = "1,2,4";
                    break;
                case "出货":
                    dimensionType = "1,2,4";
                    break;
                case "回款":
                    dimensionType = "1,2,4";
                    break;
                case "客诉":
                    dimensionType = "1,2,4";
                    break;
                case "品质":
                    dimensionType = "1,2,4";
                    break;
                case "采购":
                    dimensionType = "3";
                    this.dimensionType = 3;
                    break;
            }
            if(dimensionType.indexOf(num) != -1)
            {
                status = true;
            }
            return status;
        },
        showTypeExam:function(num){
            let type = false;
            if(!num)
            {
                return type;
            }
            let showType = null;
            switch (this.reportName) {
                case "接单":
                    showType = "1,2,3,4";
                    break;
                case "出货":
                    showType = "1,2,3,4";
                    break;
                case "回款":
                    showType = "3";
                    this.showType = 3;
                    break;
                case "客诉":
                    showType = "1,2,3,4";
                    break;
                case "品质":
                    showType = "1,2,3,4";
                    break;
                case "采购":
                    showType = "1,2,3,4";
                    break;
            }
            if(showType.indexOf(num) != -1)
            {
                type = true;
            }
            return type;
        },
        // 获取时间
        getDate: function () {
            let startDate = new Date()
            startDate.setMonth(startDate.getMonth() - 1)
            let startYear = startDate.getFullYear()
            let startMonth = startDate.getMonth() + 1
            startMonth = (startMonth < 10 ? '0' + startMonth : startMonth);
            let startDay = startDate.getDate() >= 10 ? startDate.getDate() : '0' + startDate.getDate()
            let startTime = startYear + '-' + startMonth + '-' + startDay;
            this.startTime = startTime;

            let endDate = new Date()
            let endYear = endDate.getFullYear()
            let endMonth = endDate.getMonth() + 1
            endMonth = (endMonth < 10 ? '0' + endMonth : endMonth)
            let endDay = endDate.getDate() >= 10 ? endDate.getDate() : '0' + endDate.getDate()
            let endTime = (endYear + '-' + endMonth + '-' + endDay);
            this.endTime = endTime;

            this.initQueryDate('startTime', startTime)
            this.initQueryDate('endTime', endTime)
        },
        initQueryDate: function (id, startDates) {
            if ($('#' + id + '').is(':visible')) {
                const _this = this
                $('#' + id + '').daterangepicker({
                    'singleDatePicker': true,
                    'showDropdowns': true,
                    'timePicker': true,
                    'timePicker24Hour': false,
                    'startDate': startDates, // 设置开始日期
                    'opens': 'center',
                    'drops': 'down',
                    'locale': {
                        'format': 'YYYY-MM-DD',
                        'separator': ' - ',
                        'applyLabel': '确定',
                        'cancelLabel': '取消',
                        'fromLabel': 'From',
                        'toLabel': '到',
                        'customRangeLabel': 'Custom',
                        'weekLabel': 'W',
                        'daysOfWeek': [
                            '日',
                            '一',
                            '二',
                            '三',
                            '四',
                            '五',
                            '六'
                        ],
                        'monthNames': [
                            '一月',
                            '二月',
                            '三月',
                            '四月',
                            '五月',
                            '六月',
                            '七月',
                            '八月',
                            '九月',
                            '十月',
                            '十一月',
                            '十二月'
                        ],
                        'firstDay': 1
                    }
                }, function (start, end, label) {
                    if (id === 'startTime') {
                        _this.startTime = start.format('YYYY-MM-DD')
                    } else if (id === 'endTime') {
                        _this.endTime = start.format('YYYY-MM-DD')
                    }
                })
            } else {
                if (this.temp > 50) {
                    this.temp = 0
                }
                this.temp++
                // 递归 等待dom渲染完毕
                const _this = this
                setTimeout(function () { _this.initQueryDate(id, startDates) }, 500)
            }
        },
        getOrderDetail: function (item){
            const query ={};
            query.name = this.reportName;
            query.dimensionType = this.dimensionType;
            query.dateType = this.dateA;
            query.phone = this.phone;
            query.analyticalNum = this.analyticalNum;
            query.reportName = this.reportName;
            query.departName = item.departName;
            query.startTime = this.startTime;
            query.endTime = this.endTime;
            window.localStorage.setItem('reportDetail', JSON.stringify(query));
            this.$router.push('/report_listTwo');
        }
    }
}