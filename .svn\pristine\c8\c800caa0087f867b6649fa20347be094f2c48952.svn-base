package com.kyb.pcberp.modules.sys.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;

import java.util.Date;
import java.util.List;

/**
 * 职位员工对象
 */
@SuppressWarnings("serial")
public class PositionEmployee extends DataEntity<PositionEmployee>
{
    
    /** 职位对象 */
    private Position position;
    
    /** 结构id */
    private String groupId;
    
    /** 员工对象 */
    private Employee employee;
    
    private EmployeePosition employeePosition;
    
    private String status;// 1公司 2部门 3组织
    
    private String name;
    
    private String positionName;
    
    private String type;// 用来判断查询组织 还是公司 还是部门

    private Date startTime;

    private Date endTime;

    private String groupOrgId;

    private List<Department> groupIdList;

    private String departName;

    private String names;

    private String poEmpJoin;

    private String positionId;

    private String joinTime;
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public EmployeePosition getEmployeePosition()
    {
        return employeePosition;
    }
    
    public void setEmployeePosition(EmployeePosition employeePosition)
    {
        this.employeePosition = employeePosition;
    }
    
    public Position getPosition()
    {
        return position;
    }
    
    public void setPosition(Position position)
    {
        this.position = position;
    }
    
    public Employee getEmployee()
    {
        return employee;
    }
    
    public void setEmployee(Employee employee)
    {
        this.employee = employee;
    }
    
    public String getGroupId()
    {
        return groupId;
    }
    
    public void setGroupId(String groupId)
    {
        this.groupId = groupId;
    }
    
    public String getPositionName()
    {
        return positionName;
    }
    
    public void setPositionName(String positionName)
    {
        this.positionName = positionName;
    }
    
    public String getName()
    {
        return name;
    }
    
    public void setName(String name)
    {
        this.name = name;
    }
    
    public String getType()
    {
        return type;
    }
    
    public void setType(String type)
    {
        this.type = type;
    }

    public Date getStartTime()
    {
        return startTime;
    }

    public void setStartTime(Date startTime)
    {
        this.startTime = startTime;
    }

    public Date getEndTime()
    {
        return endTime;
    }

    public void setEndTime(Date endTime)
    {
        this.endTime = endTime;
    }

    public String getStartTimeStr()
    {
        if(null != startTime)
        {
            return DateUtils.formatDate(startTime,"yyyy-MM-dd");
        }
        return null;
    }

    public String getEndTimeStr()
    {
        if(null != endTime)
        {
            return DateUtils.formatDate(endTime,"yyyy-MM-dd");
        }
        return null;
    }

    public String getGroupOrgId()
    {
        return groupOrgId;
    }

    public void setGroupOrgId(String groupOrgId)
    {
        this.groupOrgId = groupOrgId;
    }

    public List<Department> getGroupIdList()
    {
        return groupIdList;
    }

    public void setGroupIdList(List<Department> groupIdList)
    {
        this.groupIdList = groupIdList;
    }

    public String getDepartName()
    {
        return departName;
    }

    public void setDepartName(String departName)
    {
        this.departName = departName;
    }

    public String getNames()
    {
        return names;
    }

    public void setNames(String names)
    {
        this.names = names;
    }

    public String getPoEmpJoin()
    {
        return poEmpJoin;
    }

    public void setPoEmpJoin(String poEmpJoin)
    {
        this.poEmpJoin = poEmpJoin;
    }

    public String getPositionId()
    {
        return positionId;
    }

    public void setPositionId(String positionId)
    {
        this.positionId = positionId;
    }

    public String getJoinTime() {
        return joinTime;
    }

    public void setJoinTime(String joinTime) {
        this.joinTime = joinTime;
    }
}
