package com.kyb.pcberp.modules.sys.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

/**
 * 用户
 * 
 * <AUTHOR>
 * @version 2017-06-28
 */
public class CustomerSalesAssistant extends DataEntity<CustomerSalesAssistant>
{
    
    private static final long serialVersionUID = 1L;
    
    private String userId; // 用户id
    
    private String customerId; // 客户id
    
    private User user;
    
    private Integer type; //1:业务员助理，2:备份的业务员助理
    
    private String salesAssistantId;
    
    public CustomerSalesAssistant()
    {
        super();
    }
    
    public CustomerSalesAssistant(String id)
    {
        super(id);
    }
    
    public String getUserId()
    {
        return userId;
    }
    
    public void setUserId(String userId)
    {
        this.userId = userId;
    }
    
    public User getUser()
    {
        return user;
    }
    
    public void setUser(User user)
    {
        this.user = user;
    }
    
    public String getCustomerId()
    {
        return customerId;
    }
    
    public void setCustomerId(String customerId)
    {
        this.customerId = customerId;
    }

    public Integer getType()
    {
        return type;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    public String getSalesAssistantId()
    {
        return salesAssistantId;
    }

    public void setSalesAssistantId(String salesAssistantId)
    {
        this.salesAssistantId = salesAssistantId;
    }
    
}
