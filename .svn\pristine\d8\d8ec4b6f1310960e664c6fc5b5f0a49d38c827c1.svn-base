package com.kyb.pcberp.modules.report.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

import java.math.BigDecimal;
import java.util.List;

public class DeliveryAssessment extends DataEntity<DeliveryAssessment>
{
    private static final long serialVersionUID = 1L;

    private String groupOrgId;

    private BigDecimal deliveryArea; // 出货平米数

    private Integer deliveryPayment; // 送货数量

    private Integer normalAmount; // 正常款数

    private Integer overduePayments; // 逾期款数

    private Integer eligibleFunds; // 达标款数

    private BigDecimal deliveryAchievementRate; // 交货达成率

    private BigDecimal rewardsPunishmentAmount; // 奖惩金额

    private String checkOutType; // 结账类型

    private String checkoutData; // 结账数据

    private String versionData; // 版本数据

    private List<DeliveryAssessment> daList;

    private List<MarketAssessment> maList;

    private String name;

    private List<DeliveryAssessmentDetail> detailList;

    private String period;

    private String saleComId;

    public String getGroupOrgId()
    {
        return groupOrgId;
    }

    public void setGroupOrgId(String groupOrgId)
    {
        this.groupOrgId = groupOrgId;
    }

    public BigDecimal getDeliveryArea()
    {
        return deliveryArea;
    }

    public void setDeliveryArea(BigDecimal deliveryArea)
    {
        this.deliveryArea = deliveryArea;
    }

    public Integer getDeliveryPayment()
    {
        return deliveryPayment;
    }

    public void setDeliveryPayment(Integer deliveryPayment)
    {
        this.deliveryPayment = deliveryPayment;
    }

    public Integer getNormalAmount()
    {
        return normalAmount;
    }

    public void setNormalAmount(Integer normalAmount)
    {
        this.normalAmount = normalAmount;
    }

    public Integer getOverduePayments()
    {
        return overduePayments;
    }

    public void setOverduePayments(Integer overduePayments)
    {
        this.overduePayments = overduePayments;
    }

    public Integer getEligibleFunds()
    {
        return eligibleFunds;
    }

    public void setEligibleFunds(Integer eligibleFunds)
    {
        this.eligibleFunds = eligibleFunds;
    }

    public BigDecimal getDeliveryAchievementRate()
    {
        return deliveryAchievementRate;
    }

    public void setDeliveryAchievementRate(BigDecimal deliveryAchievementRate)
    {
        this.deliveryAchievementRate = deliveryAchievementRate;
    }

    public BigDecimal getRewardsPunishmentAmount()
    {
        return rewardsPunishmentAmount;
    }

    public void setRewardsPunishmentAmount(BigDecimal rewardsPunishmentAmount)
    {
        this.rewardsPunishmentAmount = rewardsPunishmentAmount;
    }

    public String getCheckOutType()
    {
        return checkOutType;
    }

    public void setCheckOutType(String checkOutType)
    {
        this.checkOutType = checkOutType;
    }


    public String getCheckoutData()
    {
        return checkoutData;
    }

    public void setCheckoutData(String checkoutData)
    {
        this.checkoutData = checkoutData;
    }

    public String getVersionData()
    {
        return versionData;
    }

    public void setVersionData(String versionData)
    {
        this.versionData = versionData;
    }

    public List<DeliveryAssessment> getDaList()
    {
        return daList;
    }

    public void setDaList(List<DeliveryAssessment> daList)
    {
        this.daList = daList;
    }

    public List<MarketAssessment> getMaList()
    {
        return maList;
    }

    public void setMaList(List<MarketAssessment> maList)
    {
        this.maList = maList;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public List<DeliveryAssessmentDetail> getDetailList()
    {
        return detailList;
    }

    public void setDetailList(List<DeliveryAssessmentDetail> detailList)
    {
        this.detailList = detailList;
    }

    public String getPeriod()
    {
        return period;
    }

    public void setPeriod(String period)
    {
        this.period = period;
    }

    @Override
    public Object clone()
    {
        DeliveryAssessment deliveryAssessment = null;
        try
        {
            deliveryAssessment = (DeliveryAssessment)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            e.printStackTrace();
        }
        return deliveryAssessment;
    }

    public String getSaleComId()
    {
        return saleComId;
    }

    public void setSaleComId(String saleComId)
    {
        this.saleComId = saleComId;
    }
}
