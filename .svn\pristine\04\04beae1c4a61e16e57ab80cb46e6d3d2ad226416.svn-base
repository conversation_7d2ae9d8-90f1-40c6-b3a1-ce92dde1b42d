/**
 * 
 */
package com.kyb.pcberp.modules.quality.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.purch.entity.PrdorderDetail;
import com.kyb.pcberp.modules.purch.entity.Purchasing;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;
import com.kyb.pcberp.modules.quality.entity.AdviseDelivery;
import com.kyb.pcberp.modules.quality.entity.Inspect;
import com.kyb.pcberp.modules.quality.entity.SourceDetection;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.sys.entity.Company;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 来料检测DAO接口
 * 
 * <AUTHOR>
 * @version 2016-05-19
 */
@MyBatisDao
public interface SourceDetectionDao extends CrudDao<SourceDetection>
{
    
    // 查询某个原料采购明细单的已检测通过的
    BigDecimal getPoorNumSum(SourceDetection sd);
    
    /**
     * ycy 2016-11-9 根据采购id 查询不良数量
     * 
     * @param sd
     * @return
     */
    BigDecimal getPoorNumSumTo(SourceDetection sd);
    
    /**
     * ycy 2016-10-10 查询某个成品采购明细单的已检测通过的数量
     * 
     * @param sd
     * @return
     */
    BigDecimal getDetectionNum(SourceDetection sd);

    Integer getDetectionNum2(SourceDetection sd);



  

    Integer findNoisEnable(SourceDetection sourceDetection);
    
    List<SourceDetection> findExpList(SourceDetection sourceDetection);
    
    /**
     * ycy 2016-11-9 根据采购id查询来料检测
     * 
     * @param sourceDetection
     * @return
     */
    List<SourceDetection> getSourceDetection(SourceDetection sourceDetection);
    
    /**
     * ycy 2016-11-9 根据采购id查询来料检测
     * 
     * @param sourceDetection
     * @return
     */
    List<SourceDetection> getSourceDetectionList(SourceDetection sourceDetection);
    
    
    /**
     * ycy 2016-11-9 采购入库修改状态：已入库
     * 
     * @param sourceDetection
     */
    void updateStatus(SourceDetection sourceDetection);
    
    List<SourceDetection> findCanCancelList(SourceDetection sourceDetection);
    
    int updateStatusById(SourceDetection sourceDetection);
    
    /**
     * fzd 2016-12-16 获取采购检测数量
     */
    BigDecimal getDetectPurch(@Param("recordId") String recordId, @Param("companyId") String companyId);
    
    /**
     * fzd 2016-12-23 获取检测不良数量
     */
    BigDecimal getPoorPurch(@Param("recordId") String recordId, @Param("companyId") String companyId);
    
    /**
     * fzd 2016-12-16 获取采购入库数量
     */
    BigDecimal getStorPurch(@Param("recordId") String recordId, @Param("companyId") String companyId);
    
    /** tj 2018-11-22 获取不良数量 **/
    SourceDetection findPoorNum(PrdorderDetail prdorderDetail);

    /** lq 2018-12-03 冲红修改补货入库 来料检测 的状态 */
    void updateDetailStatus(RawmaterialStock raw);

    /** tj 2019-03-14 根据采购单明细查询来料检测 */
    public List<SourceDetection> findByPrdorderDetail(PrdorderDetail prdorderDetail);

    /** tj 2019-03-14 修改检测数量 */
    public void updataDetectionNum(SourceDetection detection);

    /** tj 2019-03-14 根据原料采购单明细查询来料检测 */
    public List<SourceDetection> findByPurchasingDetail(PurchasingDetail detail);
    
    /** tj 2019-04-01 查询总数量 */
    public SourceDetection getNumSum(SourceDetection sourceDetection);
    
    /** tj 2019-04-01 查询正在检测中的数量 */
    public BigDecimal findDetectionNum(PurchasingDetail prdorderDetail);

    /** lq 2019-03-29 查询来料检测单的个数 */
    Integer selectSourceDetectionNumber(@Param("company")Company company);
    
    Integer sourceDetectionExitCount(Purchasing purchasing);

    Integer sourceDetectionExitCountTwo(PurchasingDetail detail);
    
    void updateSourceId(SourceDetection sourceDetection);
    
    Integer getCountBySourceId(SourceDetection sourceDetection);

    Integer rollBackAudit(Inspect inspect);

    List<AdviseDelivery>showAdviseDeliveryList(AdviseDelivery adviseDelivery);

    void updateStatusAdvice(SourceDetection sourceDetection);

    List<SourceDetection> getSourceDetectionData(@Param("orderId") String orderId,@Param("resourceType") Integer resourceType);
}