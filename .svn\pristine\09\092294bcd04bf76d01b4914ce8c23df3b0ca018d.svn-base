package com.kyb.pcberp.modules.crm.vo;

import java.math.BigDecimal;
import java.math.RoundingMode;

import org.apache.commons.lang3.StringUtils;

import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.crm.entity.AccountsReceivable;
import com.kyb.pcberp.modules.crm.entity.Customer;

public class FinanceCostVo extends AccountsReceivable
{

    /**
     * 
     */
    private static final long serialVersionUID = 8543392627097835125L;
    
    private String operateStatus;
    
    private Integer quantity;
    
    private BigDecimal amount;
    
    private String type;
    
    private BigDecimal purPrice;
    
    private String payStatus;
    
    private Integer payQuantity;
    
    private BigDecimal otherFee;
    
    private Integer deliveryNum;
    
    private String customerNo;
    
    private String customerName;
    
    private String customerModel;
    
    private String statusStr;
    
    private BigDecimal inAmount;
    
    private BigDecimal outAmount;
    
    private BigDecimal inEstimateCost;
    
    private BigDecimal outEstimateCost;
    
    private String madeFrom;
    
    private String billStatus;
    
    private String proCompanyId;
    
    private BigDecimal inAmountDL;
    
    private BigDecimal outAmountDL;
    
    private BigDecimal amountDL;
    
    private BigDecimal inEstimateCostDL;
    
    private BigDecimal outEstimateCostDL;
    
    private BigDecimal estimateCostDL;
    
    private BigDecimal inAmountKJ;
    
    private BigDecimal outAmountKJ;
    
    private BigDecimal amountKJ;
    
    private BigDecimal inEstimateCostKJ;
    
    private BigDecimal outEstimateCostKJ;
    
    private BigDecimal estimateCostKJ;
    
    private BigDecimal inAmountLN;
    
    private BigDecimal outAmountLN;
    
    private BigDecimal amountLN;
    
    private BigDecimal inEstimateCostLN;
    
    private BigDecimal outEstimateCostLN;
    
    private BigDecimal estimateCostLN;
    
    private BigDecimal inAmountJX;
    
    private BigDecimal outAmountJX;
    
    private BigDecimal amountJX;
    
    private BigDecimal inEstimateCostJX;
    
    private BigDecimal outEstimateCostJX;
    
    private BigDecimal estimateCostJX;

    private String departName;

    private String salesman;
    
    
    public BigDecimal getInAmountDL()
    {
        return inAmountDL;
    }

    public void setInAmountDL(BigDecimal inAmountDL)
    {
        this.inAmountDL = inAmountDL;
    }

    public BigDecimal getOutAmountDL()
    {
        return outAmountDL;
    }

    public void setOutAmountDL(BigDecimal outAmountDL)
    {
        this.outAmountDL = outAmountDL;
    }

    public BigDecimal getAmountDL()
    {
        return amountDL;
    }

    public void setAmountDL(BigDecimal amountDL)
    {
        this.amountDL = amountDL;
    }

    public BigDecimal getInEstimateCostDL()
    {
        return inEstimateCostDL;
    }

    public void setInEstimateCostDL(BigDecimal inEstimateCostDL)
    {
        this.inEstimateCostDL = inEstimateCostDL;
    }

    public BigDecimal getOutEstimateCostDL()
    {
        return outEstimateCostDL;
    }

    public void setOutEstimateCostDL(BigDecimal outEstimateCostDL)
    {
        this.outEstimateCostDL = outEstimateCostDL;
    }

    public BigDecimal getEstimateCostDL()
    {
        return estimateCostDL;
    }

    public void setEstimateCostDL(BigDecimal estimateCostDL)
    {
        this.estimateCostDL = estimateCostDL;
    }

    public BigDecimal getInAmountKJ()
    {
        return inAmountKJ;
    }

    public void setInAmountKJ(BigDecimal inAmountKJ)
    {
        this.inAmountKJ = inAmountKJ;
    }

    public BigDecimal getOutAmountKJ()
    {
        return outAmountKJ;
    }

    public void setOutAmountKJ(BigDecimal outAmountKJ)
    {
        this.outAmountKJ = outAmountKJ;
    }

    public BigDecimal getAmountKJ()
    {
        return amountKJ;
    }

    public void setAmountKJ(BigDecimal amountKJ)
    {
        this.amountKJ = amountKJ;
    }

    public BigDecimal getInEstimateCostKJ()
    {
        return inEstimateCostKJ;
    }

    public void setInEstimateCostKJ(BigDecimal inEstimateCostKJ)
    {
        this.inEstimateCostKJ = inEstimateCostKJ;
    }

    public BigDecimal getOutEstimateCostKJ()
    {
        return outEstimateCostKJ;
    }

    public void setOutEstimateCostKJ(BigDecimal outEstimateCostKJ)
    {
        this.outEstimateCostKJ = outEstimateCostKJ;
    }

    public BigDecimal getEstimateCostKJ()
    {
        return estimateCostKJ;
    }

    public void setEstimateCostKJ(BigDecimal estimateCostKJ)
    {
        this.estimateCostKJ = estimateCostKJ;
    }

    public BigDecimal getInAmountLN()
    {
        return inAmountLN;
    }

    public void setInAmountLN(BigDecimal inAmountLN)
    {
        this.inAmountLN = inAmountLN;
    }

    public BigDecimal getOutAmountLN()
    {
        return outAmountLN;
    }

    public void setOutAmountLN(BigDecimal outAmountLN)
    {
        this.outAmountLN = outAmountLN;
    }

    public BigDecimal getAmountLN()
    {
        return amountLN;
    }

    public void setAmountLN(BigDecimal amountLN)
    {
        this.amountLN = amountLN;
    }

    public BigDecimal getInEstimateCostLN()
    {
        return inEstimateCostLN;
    }

    public void setInEstimateCostLN(BigDecimal inEstimateCostLN)
    {
        this.inEstimateCostLN = inEstimateCostLN;
    }

    public BigDecimal getOutEstimateCostLN()
    {
        return outEstimateCostLN;
    }

    public void setOutEstimateCostLN(BigDecimal outEstimateCostLN)
    {
        this.outEstimateCostLN = outEstimateCostLN;
    }

    public BigDecimal getEstimateCostLN()
    {
        return estimateCostLN;
    }

    public void setEstimateCostLN(BigDecimal estimateCostLN)
    {
        this.estimateCostLN = estimateCostLN;
    }

    public BigDecimal getInAmountJX()
    {
        return inAmountJX;
    }

    public void setInAmountJX(BigDecimal inAmountJX)
    {
        this.inAmountJX = inAmountJX;
    }

    public BigDecimal getOutAmountJX()
    {
        return outAmountJX;
    }

    public void setOutAmountJX(BigDecimal outAmountJX)
    {
        this.outAmountJX = outAmountJX;
    }

    public BigDecimal getAmountJX()
    {
        return amountJX;
    }

    public void setAmountJX(BigDecimal amountJX)
    {
        this.amountJX = amountJX;
    }

    public BigDecimal getInEstimateCostJX()
    {
        return inEstimateCostJX;
    }

    public void setInEstimateCostJX(BigDecimal inEstimateCostJX)
    {
        this.inEstimateCostJX = inEstimateCostJX;
    }

    public BigDecimal getOutEstimateCostJX()
    {
        return outEstimateCostJX;
    }

    public void setOutEstimateCostJX(BigDecimal outEstimateCostJX)
    {
        this.outEstimateCostJX = outEstimateCostJX;
    }

    public BigDecimal getEstimateCostJX()
    {
        return estimateCostJX;
    }

    public void setEstimateCostJX(BigDecimal estimateCostJX)
    {
        this.estimateCostJX = estimateCostJX;
    }

    public String getProCompanyId()
    {
        return proCompanyId;
    }

    public void setProCompanyId(String proCompanyId)
    {
        this.proCompanyId = proCompanyId;
    }
    
    @ExcelField(title = "成本状态", align = 2, sort = 5)
    public String getBillStatus()
    {
        return billStatus;
    }

   
    public void setBillStatus(String billStatus)
    {
        this.billStatus = billStatus;
    }

    public String getMadeFrom()
    {
        return madeFrom;
    }

    public void setMadeFrom(String madeFrom)
    {
        this.madeFrom = madeFrom;
    }

    @ExcelField(title = "内部收入 ", align = 2, sort = 7)
    public BigDecimal getInAmount()
    {
        return inAmount;
    }

    public void setInAmount(BigDecimal inAmount)
    {
        this.inAmount = inAmount;
    }

    @ExcelField(title = "外协收入 ", align = 2, sort = 8)
    public BigDecimal getOutAmount()
    {
        return outAmount;
    }

    public void setOutAmount(BigDecimal outAmount)
    {
        this.outAmount = outAmount;
    }

    @ExcelField(title = "内部成本 ", align = 2, sort = 10)
    public BigDecimal getInEstimateCost()
    {
        return inEstimateCost;
    }

    public void setInEstimateCost(BigDecimal inEstimateCost)
    {
        this.inEstimateCost = inEstimateCost;
    }

    @ExcelField(title = "外协成本 ", align = 2, sort = 11)
    public BigDecimal getOutEstimateCost()
    {
        return outEstimateCost;
    }

    public void setOutEstimateCost(BigDecimal outEstimateCost)
    {
        this.outEstimateCost = outEstimateCost;
    }

    @ExcelField(title = "对账状态 ", align = 2, sort = 1)
    public String getStatusStr()
    {
        if(!StringUtils.isEmpty(statusStr))
        {
            return statusStr;
        }
        if(this.getStatus()==null)
        {
            return "";
        }
        if("2001".equals(this.getStatus().toString()))
        {
            return "未确认";
        }
        else if("2002".equals(this.getStatus().toString()))
        {
            return "已确认";
        }
        else if("2003".equals(this.getStatus().toString()))
        {
            return "已完成";
        }
        else if("2004".equals(this.getStatus().toString()))
        {
            return "未复核";
        }
        else if("2005".equals(this.getStatus().toString()))
        {
            return "已复核";
        }
        else if("60001".equals(this.getStatus().toString()))
        {
            return "审批中";
        }
        return statusStr;
    }

    public void setStatusStr(String statusStr)
    {
        this.statusStr = statusStr;
    }

    @ExcelField(title = "客户名称", align = 2, sort = 2, fieldType = (Customer.class), value = ("customer.name"))
    public Customer getCustomer()
    {
        return super.getCustomer();
    }
    
    @ExcelField(title = "客户编号", align = 2, sort = 3, fieldType = (Customer.class), value = ("customer.no"))
    public String getCustomerNo()
    {
        return this.customerNo;
    }
    
    @ExcelField(title = "账期 ", align = 2, sort = 4)
    public Integer getPeriod()
    {
        return super.getPeriod();
    }

    public void setCustomerNo(String customerNo)
    {
        this.customerNo = customerNo;
    }

    public String getCustomerName()
    {
        return customerName;
    }

    public void setCustomerName(String customerName)
    {
        this.customerName = customerName;
    }

    public Integer getDeliveryNum()
    {
        return deliveryNum;
    }

    public void setDeliveryNum(Integer deliveryNum)
    {
        this.deliveryNum = deliveryNum;
    }

    public BigDecimal getOtherFee()
    {
        return otherFee;
    }

    public void setOtherFee(BigDecimal otherFee)
    {
        this.otherFee = otherFee;
    }

    public Integer getPayQuantity()
    {
        return payQuantity;
    }

    public void setPayQuantity(Integer payQuantity)
    {
        this.payQuantity = payQuantity;
    }

    
    public String getOperateStatus()
    {
        return operateStatus;
    }

    public void setOperateStatus(String operateStatus)
    {
        this.operateStatus = operateStatus;
    }

    @ExcelField(title = "出库数量 ", align = 2, sort = 6)
    public Integer getQuantity()
    {
        return quantity;
    }

    public void setQuantity(Integer quantity)
    {
        this.quantity = quantity;
    }

    @ExcelField(title = "收入合计 ", align = 2, sort = 9)
    public BigDecimal getAmount()
    {
        if(amount==null)
        {
            return (this.getInAmount()==null?BigDecimal.ZERO:this.getInAmount()).add(this.getOutAmount()==null?BigDecimal.ZERO:this.getOutAmount());
        }
        return amount;
    }

    public void setAmount(BigDecimal amount)
    {
        this.amount = amount;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public BigDecimal getPurPrice()
    {
        return purPrice;
    }

    public void setPurPrice(BigDecimal purPrice)
    {
        this.purPrice = purPrice;
    }

    public String getPayStatus()
    {
        return payStatus;
    }

    public void setPayStatus(String payStatus)
    {
        this.payStatus = payStatus;
    }

    public String getCustomerModel()
    {
        return customerModel;
    }

    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }
    
    @ExcelField(title = "成本合计 ", align = 2, sort = 12)
    public BigDecimal getEstimateCost()
    {
        if(super.getEstimateCost()==null)
        {
            return (this.getInEstimateCost()==null?BigDecimal.ZERO:this.getInEstimateCost()).add(this.getOutEstimateCost()==null?BigDecimal.ZERO:this.getOutEstimateCost());
        }
        return super.getEstimateCost();
    }
    
    public BigDecimal getCheckCost()
    {
        return super.getCheckCost();
    }
    
    public BigDecimal getBillCost()
    {
        return super.getBillCost();
    }

    @ExcelField(title = "部门 ", align = 2, sort = 4)
    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    @ExcelField(title = "业务员 ", align = 2, sort = 4)
    public String getSalesman() {
        return salesman;
    }

    public void setSalesman(String salesman) {
        this.salesman = salesman;
    }
}
