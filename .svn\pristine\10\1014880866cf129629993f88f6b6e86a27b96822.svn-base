const waiteRepair = {
    template: '#waiteRepair',
    created:function(){
        this.$store.dispatch('myStore/setUserInformation');
        this.num = window.localStorage.getItem('num');
        if (!this.num){
            alert("非法点入,请确认通道是否点击正确！");
            router.push("/maintenance");
            return;
        }
    },
    computed: {
        user: {
            get () {
                return this.$store.state.myStore.user;
            }
        },
        userInformation:{
            get () {
                return this.$store.state.myStore.userInformation;
            }
        },
    },
    data(){
        return {
            myWaiteRepairList:[],
            maintenanceObj:{},
            num:'',
            message:'',
            searchInfo:'',
            waiteRepair:{},
            loadDeviceList:[],
            applyCompanyList:[],
            applyCompanyId:null,
            repairCompanyList:[],
            repairCompanyId:null,
            userList:[],
            // applyManId:null,
            equipmentId:null,
            repairStatus:'',
            applyManUserId:'',
            equipmentUserId:'',

            manualRepairT:{},
            markSystemList:[],


            markSystem:{},
            efficiencyList:[
                {"recordId" : "1", "value": 1},
                {"recordId" : "2", "value": 2},
                {"recordId" : "3", "value": 3},
                {"recordId" : "4", "value": 4},
                {"recordId" : "5", "value": 5}
            ],
            massScoreList:[
                {"recordId" : "1", "value": 1},
                {"recordId" : "2", "value": 2},
                {"recordId" : "3", "value": 3},
                {"recordId" : "4", "value": 4},
                {"recordId" : "5", "value": 5}
            ],
            attitudeScoreList:[
                {"recordId" : "1", "value": 1},
                {"recordId" : "2", "value": 2},
                {"recordId" : "3", "value": 3},
                {"recordId" : "4", "value": 4},
                {"recordId" : "5", "value": 5}
            ],
            serverList: [],
            localIdsImg: [],
            localIds: [],
            fileList:[],
            comments: {},
            sectionValueOne:'',
            checkedStarsOne: 0,
            checkedStarsTwo:0,
            checkedStarsThree:0,
            numOne:'',
            status:'',
            //工时申诉
            appealH:{},
            hoursRecordList:[],

            dayArrival:null,
            expectStart:'',
            accessoryContent:'',
            sparePartsCost:'',
            buyNowPrice:'',
            hoursAppealObj:{},
            authority:false,
            confimNum:'1'
        }
    },
    watch:{
        userInformation:function(){
            this.loadData();
            if (this.num === '8')
            {
                this.loadMarkSystemList(2);
            }else{
                this.getMyWaiteRepairList();
            }
        },
    },
    methods: {
        initSelect: function (id, list, value, showId, showValue) {
            if (list && list.length > 0) {
                let option = '';
                option += "<option value=''>" + '请选择' + '</option>';
                list.forEach(el => {
                    option += "<option value='" + eval('el.' + showId) + "'>" + eval('el.' + showValue) + '</option>';
                });
                $('#' + id).empty();
                $('#' + id).append(option);
                $('#' + id).selectpicker('val', value);
                $('#' + id).selectpicker('render');
                $('#' + id).selectpicker('refresh');
                $('#' + id).selectpicker();
            }
        },
        loadData: function () {
            const query = {};
            query.userId = this.user.userId;
            query.phone = this.user.phone;
            query.moduleId = this.num;
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/getUserList",
                data: JSON.stringify(query),
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    if (data) {
                        _this.userList = data.userList;
                        _this.applyCompanyList = data.applyCompanyList;
                        _this.repairCompanyList = data.repairCompanyList;
                        // _this.initSelect('applyManId', _this.userList, null, 'recordId', 'userName');
                        _this.initSelect('equipmentId', _this.userList, null, 'recordId', 'userName');
                        _this.initSelect('applyCompanyId', _this.applyCompanyList, null, 'recordId', 'name');
                        _this.initSelect('repairCompanyId', _this.repairCompanyList, null, 'recordId', 'name');
                        //验证当前登录员是否是维保员
                        if(_this.userList && _this.userList.length > 0)
                        {
                            for(let userId of _this.userList)
                            {
                                if (_this.user.userId === userId.recordId)
                                {
                                    _this.authority = true;
                                }
                            }
                        }


                    }
                    _this.loadDataTwo();
                }
            });
        },
        getMyWaiteRepairList: function (confimNum) {
            const _this = this;
            const query = {};
            query.applyCompanyId = this.applyCompanyId;
            query.repairCompanyId = this.repairCompanyId;
            query.userId = this.user.userId;
            query.repairStatus = this.repairStatus;
            query.applyMan = this.applyManUserId;
            query.repairMan = this.equipmentUserId;
            if (this.num === "1") {
                query.moduleId = "1";
            } else if (this.num === "2") {
                query.moduleId = "2";
            } else if (this.num === "3") {
                query.moduleId = "3";
                query.employeeId = this.user.employeeId;
            } else if (this.num === "4") {
                query.moduleId = "4";
                query.manageSatus = this.user.manageSatus;
            } else if (this.num === "5") {
                query.moduleId = "5";
            }
            query.searchInfo = this.searchInfo;
            query.confimNum = confimNum ? confimNum : this.confimNum;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/getMyWaiteRepairList",
                data: JSON.stringify(query),
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    if (data) {
                        _this.myWaiteRepairList = data;
                        for (let scoreT of _this.myWaiteRepairList) {
                            if (scoreT.efficiency) {
                                scoreT.checkedStarsOne = scoreT.efficiency;
                            }
                            if (scoreT.massScore) {
                                scoreT.checkedStarsTwo = scoreT.massScore;
                            }
                            if (scoreT.attitudeScore) {
                                scoreT.checkedStarsThree = scoreT.attitudeScore;
                            }
                        }
                    }
                }
            });
        },
        calculateArrivalDate(day) {
            var currentDateTime = new Date();
            var daysToAdd = day * 24 * 60 * 60 * 1000;
            var arrivalDateTime = new Date(currentDateTime.getTime() + daysToAdd);
            var formattedDateTime = arrivalDateTime.toLocaleString('zh-CN', { hour12: false, timeZone: 'Asia/Shanghai' });
            this.expectStart = formattedDateTime;
        },
        updateStatus: function (row, numOne,statusFlag) {
            if (!row) {
                return;
            }
            //点击暂停后未点开始，就点击结束则返回
            if (statusFlag === "3" && numOne === "2")
            {
                alert("暂停后点击完开始才能点击结束!");
                return;
            }
            this.maintenanceObj = row;
            this.accessoryContent = this.maintenanceObj.accessoryContent;
            this.dayArrival = this.maintenanceObj.dayArrival;
            this.expectStart = this.maintenanceObj.expectStart;
            this.sparePartsCost = this.maintenanceObj.sparePartsCost;
            this.buyNowPrice = this.maintenanceObj.buyNowPrice;
            this.numOne = numOne;
            if (numOne === '1') {
                if (this.maintenanceObj.repairMan && this.maintenanceObj.repairMan !== this.user.userId)
                {
                    alert("该维保申请单待" + this.maintenanceObj.repairName + "维修");
                    return;
                }
                this.maintenanceObj.userId = this.user.userId;
                this.message = "开始维保!";
            } else if (numOne === '2') {
                this.message = "结束维保！";
            }else if (numOne === '3'){
                this.message = "暂停维保！";
            }
            if(this.maintenanceObj.startDate)
            {
                $('#confirmButtonStatic').modal();
            }else{
                $('#showStartWindow').modal();
            }

        },

        confirmButton: function (number) {
            if (!this.maintenanceObj) {
                alert("页面失效，请刷新重试！");
                return;
            }
            if (number == 1)
            {
                this.maintenanceObj.accessoryContent = this.accessoryContent;
                this.maintenanceObj.dayArrival = this.dayArrival;
                this.maintenanceObj.expectStart = this.expectStart;
                this.maintenanceObj.sparePartsCost = this.sparePartsCost;
                this.maintenanceObj.buyNowPrice = this.buyNowPrice;
            }
            //区分是保存还是开始
            this.maintenanceObj.number = number;
            //区分开始、暂停、结束
            this.maintenanceObj.statusFlag = this.numOne;
            this.maintenanceObj.repairMan = this.user.userId;
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/updateStatus",
                data: JSON.stringify(_this.maintenanceObj),
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    if (data === "success") {
                        _this.getMyWaiteRepairList();
                        alert("操作成功！");
                    } else {
                        alert(data);
                    }
                    $('#showStartWindow').modal('hide');
                    $('#confirmButtonStatic').modal('hide');
                }
            });
        },
        bindEquipment: function (item) {
            this.waiteRepair = item;
            if (this.waiteRepair.repairMan && this.waiteRepair.repairMan !== this.user.userId)
            {
                alert("待" + this.waiteRepair.repairName + "进行设备绑定！");
                return;
            }
            let company = {};
            if (this.waiteRepair && this.waiteRepair.repairCompanyId) {
                company.repairCompanyId = this.waiteRepair.repairCompanyId;
                const _this = this;
                $.ajax({
                    type: "post",
                    url: ctx + "/f/wechat/repair/getLoadDeviceList",
                    data: JSON.stringify(company),
                    contentType: "application/json",
                    success: function (data) {
                        _this.loadDeviceList = data;
                        if (_this.loadDeviceList && _this.loadDeviceList.length > 0) {
                            _this.initSelect('materialId', _this.loadDeviceList, _this.waiteRepair.materialId, 'recordId', 'name');
                        }
                    }
                });
            }
            $('#addBindWaitRepair').modal();
        },
        //绑定设备值
        saveconfirmButtonTwo: function () {
            this.waiteRepair.repairMan = this.user.userId;
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/bindMaterialId",
                data: JSON.stringify(_this.waiteRepair),
                contentType: "application/json",
                success: function (data) {
                    if (data === "success") {
                        alert("绑定设备成功！");
                        _this.getMyWaiteRepairList();
                        $('#addBindWaitRepair').modal('hide');
                    }
                }
            });
        },
        //正在维保删除、修改
        underMainInitSelect: function (id, list) {
            $('#' + id).empty();
            if (list && list.length > 0) {
                let option = '';
                option += "<option value=''>" + '请选择' + '</option>';
                list.forEach(el => {
                    option += "<option value='" + el.recordId + "'>" + el.name + '</option>';
                });
                $('#' + id).empty();
                $('#' + id).append(option);
                $('#' + id).selectpicker('val', this.manualRepairT.repairCompanyId);
                $('#' + id).selectpicker('render');
                $('#' + id).selectpicker('refresh');
                $('#' + id).selectpicker();
                if (!this.manualRepairT.repairCompanyId) {
                    this.manualRepairT.repairCompanyId = this.manualRepairT.repairCompany.recordId;
                    $('#' + id).selectpicker('val', this.manualRepairT.repairCompanyId);
                }
            }
        },

        underMainSelect: function (id, list, value) {
            $('#' + id).empty();
            if (list && list.length > 0) {
                let option = '';
                option += "<option value=''>" + '请选择' + '</option>';
                list.forEach(el => {
                    option += "<option value='" + el.recordId + "'>" + el.name + '</option>';
                });
                $('#' + id).empty();
                $('#' + id).append(option);
                $('#' + id).selectpicker('val', value);
                $('#' + id).selectpicker('render');
                $('#' + id).selectpicker('refresh');
                $('#' + id).selectpicker();
                this.manualRepairT.applyCompanyId = value;
            }
        },

        //获取申请公司、维保人
        loadDataTwo: function () {
            let query = {};
            query.userId = this.user.userId;
            query.phone = this.user.phone;
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/getUserList",
                data: JSON.stringify(query),
                contentType: "application/json",
                success: function (data) {
                    if (data) {
                        //申请公司
                        if (data.applyCompanyList && data.applyCompanyList.length > 0) {
                            _this.underMainSelect('applyCompanyIdTwo', data.applyCompanyList, _this.manualRepairT.applyCompanyId);
                        }
                    }
                }
            });
        },

        //依据申请公司获取对应的维修合作公司
        loadRepairCompanyList: function () {
            let query = {};
            query.applyCompanyId = this.manualRepairT.applyCompanyId;
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/getRepairCompanyList",
                data: JSON.stringify(query),
                contentType: "application/json",
                success: function (data) {
                    if (data) {
                        _this.repairCompanyList = data;
                        //申请公司
                        if (_this.repairCompanyList && _this.repairCompanyList.length > 0) {
                            _this.underMainInitSelect('repairCompanyIdTwo', _this.repairCompanyList);
                        }
                    }
                }
            });
        },

        delManualRepair: function (item) {
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/delManualRepair",
                data: JSON.stringify(item),
                contentType: "application/json",
                success: function (data) {
                    if (data == "success") {
                        alert("删除成功！");
                        _this.getMyWaiteRepairList();
                    }
                }
            });
        },

        updateManualRepair: function (item) {
            this.manualRepairT = {...item};
            this.loadDataTwo();
            this.loadRepairCompanyList();
            $("#addRepairWindow").modal();

        },
        saveconfirmButton: function () {
            if (!this.manualRepairT || !this.manualRepairT.repairCompanyId) {
                alert("请确认是否有和维保公司合作!");
                return;
            }
            if (!this.manualRepairT || !this.manualRepairT.applyCompanyId) {
                alert("请确认是否有申请公司!");
                return;
            }
            if (!this.manualRepairT || !this.manualRepairT.deviceName) {
                alert("请确认设备及规格是否录入!");
                return;
            }
            this.manualRepairT.userId = this.user.userId;
            $('#loadingModal').modal();
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/addProblemDescription",
                data: JSON.stringify(this.manualRepairT),
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    if (data === "success") {
                        alert("保存成功!");
                        $('#addRepairWindow').modal('hide');
                        _this.getMyWaiteRepairList();
                    } else {
                        alert(data);
                    }
                }
            });
        },

        //评分客诉
        loadMarkSystemList: function (number) {
            let query = {};
            query.userId = this.user.userId;
            query.searchInfo = this.searchInfo;
            query.number = number;
            query.applyCompanyId = this.applyCompanyId;
            query.repairCompanyId = this.repairCompanyId;
            query.userId = this.user.userId;
            query.applyMan = this.applyManUserId;
            query.repairMan = this.equipmentUserId;
            query.moduleId = this.num;
            if (number) {
                this.status = number;
            }
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/getMarkSystemList",
                data: JSON.stringify(query),
                contentType: "application/json",
                success: function (data) {
                    _this.myWaiteRepairList = data;
                    for (let score of _this.myWaiteRepairList) {
                        if (score.efficiency) {
                            score.checkedStarsOne = score.efficiency;
                        }
                        if (score.massScore) {
                            score.checkedStarsTwo = score.massScore;
                        }
                        if (score.attitudeScore) {
                            score.checkedStarsThree = score.attitudeScore;
                        }
                    }
                }
            });
        },

        markScoreWindow: function (item) {
            this.checkedStarsOne = 0;
            this.checkedStarsTwo = 0;
            this.checkedStarsThree = 0;
            this.markSystem = item;
            if (this.markSystem && this.markSystem.efficiency) {
                this.checkedStarsOne = this.markSystem.efficiency;
            }
            if (this.markSystem && this.markSystem.massScore) {
                this.checkedStarsTwo = this.markSystem.massScore;
            }
            if (this.markSystem && this.markSystem.attitudeScore) {
                this.checkedStarsThree = this.markSystem.attitudeScore;
            }
            //获取附件信息
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/getMarkSystemAttach",
                data: JSON.stringify(_this.markSystem),
                contentType: "application/json",
                success: function (data) {
                    if (data) {
                        _this.fileList = data.markSystemAttachList;
                        _this.markSystem.commentList = data.markSystemCommentList;
                        if (_this.fileList && _this.fileList.length > 0) {
                            for (var i = 0; i < _this.fileList.length; i++) {
                                const file = _this.fileList[i];
                                const type = ['image/jpg', 'image/jpeg', 'image/png'];
                                const typePdf = ['application/pdf'];
                                if (file && type.indexOf(file.type) >= 0) {
                                    _this.fileList[i].imgUrl = file.downloadUrl;
                                    _this.localIdsImg.push(file.downloadUrl);
                                } else if (file && typePdf.indexOf(file.type) >= 0) {
                                    _this.fileList[i].pdfUrl = file.downloadUrl;
                                } else {
                                    if (file.downloadUrl) {
                                        if (file.type) {
                                            _this.fileList[i].preUrl = "https://view.officeapps.live.com/op/view.aspx?src=" +
                                                encodeURIComponent(file.downloadUrl);
                                        } else {
                                            const strs = file.name.split('.');
                                            if (strs[1] === 'jpg' || strs[1] === 'JPG' || strs[1] === 'png' || strs[1] === 'PNG' || strs[1] === 'jpeg' ||
                                                strs[1] === 'JPEG' || strs[1] === 'bmp' || strs[1] === 'BMP' || strs[1] === 'gif' || strs[1] === 'GIF') {
                                                _this.fileList[i].imgUrl = file.downloadUrl;
                                                _this.localIdsImg.push(file.downloadUrl);
                                            } else if (strs[1] === 'pdf' || strs[1] === 'PDF') {
                                                _this.fileList[i].pdfUrl = file.downloadUrl;
                                            } else {
                                                _this.fileList[i].preUrl = "https://view.officeapps.live.com/op/view.aspx?src=" +
                                                    encodeURIComponent(file.downloadUrl);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    $("#showMarkSystem").modal();
                }
            });
        },
        openConfirmHours: function (item) {
            this.markSystem = item;
            $("#showConfirmHours").modal();
        },

        updateConfirm:function()
        {
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/updateConfirmMainStatus",
                data: JSON.stringify(this.markSystem),
                contentType: "application/json",
                success: function (data) {
                    if(data) {
                        alert("维保工时确认成功！");
                        _this.getMyWaiteRepairList(1);
                        $('#showConfirmHours').modal('hide');
                    }
                }
            });
        },

        setRating(star) {
            if (this.markSystem && this.markSystem.commmitStatus !== 1) {
                if (star < this.checkedStarsOne) {
                    this.checkedStarsOne = this.checkedStarsOne - (this.checkedStarsOne - star);
                } else if (star == this.checkedStarsOne) {
                    this.checkedStarsOne -= star;
                } else {
                    this.checkedStarsOne = star;
                }
            }
        },
        setRatingTwo(star) {
            if (this.markSystem && this.markSystem.commmitStatus !== 1) {
                if (star < this.checkedStarsTwo) {
                    this.checkedStarsTwo = this.checkedStarsTwo - (this.checkedStarsTwo - star);
                } else if (star == this.checkedStarsTwo) {
                    this.checkedStarsTwo -= star;
                } else {
                    this.checkedStarsTwo = star;
                }
            }
        },
        setRatingThree(star) {
            if (this.markSystem && this.markSystem.commmitStatus !== 1) {
                if (star < this.checkedStarsThree) {
                    this.checkedStarsThree = this.checkedStarsThree - (this.checkedStarsThree - star);
                } else if (star == this.checkedStarsThree) {
                    this.checkedStarsThree -= star;
                } else {
                    this.checkedStarsThree = star;
                }
            }
        },
        saveMarkMessage: function (number) {
            this.markSystem.efficiency = this.checkedStarsOne;
            this.markSystem.massScore = this.checkedStarsTwo;
            this.markSystem.attitudeScore = this.checkedStarsThree;
            if (!this.markSystem || this.markSystem.efficiency === 0) {
                alert("效率分不能为空!");
                return;
            }
            if (!this.markSystem || !this.markSystem.massScore === 0) {
                alert("质量分不能为空!");
                return;
            }
            if (!this.markSystem || !this.markSystem.attitudeScore === 0) {
                alert("态度分不能为空!");
                return;
            }
            if (!this.markSystem || !this.markSystem.remarkT) {
                alert("建议不能为空!");
                return;
            }
            this.markSystem.serverList = this.serverList;
            this.markSystem.number = number;
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/updateMarkMessage",
                data: JSON.stringify(this.markSystem),
                contentType: "application/json",
                success: function (data) {
                    if (number === 1) {
                        alert("提交成功！");
                    } else if (number === 2) {
                        alert("保存成功！");
                    }
                    _this.loadMarkSystemList(2);
                    $('#showMarkSystem').modal('hide');
                }
            });
        },
        chooseImg: function () {
            var _this = this;
            wx.chooseImage({
                count: 9, // 默认9
                sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
                success: function (res) {
                    if (_this.localIds && _this.localIds.length > 0) {
                        for (var i = 0; i < res.localIds.length; i++) {
                            _this.localIds.push(res.localIds[i]);
                        }
                    } else {
                        _this.localIds = res.localIds; // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
                    }
                    // 上传图片
                    _this.serverList = [];
                    _this.localIdsCopy = [];
                    for (let i = 0; i < _this.localIds.length; i++) {
                        _this.localIdsCopy.push(_this.localIds[i]);
                    }
                    _this.uploadImage();
                }
            });
        },
        uploadImage: function () {
            var vm = this;
            if (vm.localIdsCopy && vm.localIdsCopy.length > 0) {
                wx.uploadImage({
                    localId: vm.localIdsCopy.pop(), // 需要上传的图片的本地ID，由chooseImage接口获得
                    isShowProgressTips: 1, // 默认为1，显示进度提示
                    success: function (res) {
                        vm.serverList.push(res.serverId);
                        vm.uploadImage();
                    }
                });
            }
        },
        deleteImg: function (index) {
            this.localIds.splice(index, 1);
        },
        delFile: function (index) {
            if (this.fileList[index]) {
                // 删除后台关联及存储
                const _this = this;
                $.ajax({
                    type: "post",
                    url: ctx + "/f/wechat/repair/delMarkFile",
                    data: JSON.stringify(this.fileList[index]),
                    contentType: "application/json",
                    success: function (data) {
                        if (data) {
                            if (data === 'success') {
                                alert('删除成功');
                                _this.fileList.splice(index, 1);
                            } else {
                                alert('系统错误');
                            }
                        }
                    }
                });
            }
        },
        previewImg: function (item) {
            var _this = this;
            wx.previewImage({
                current: item, // 当前显示图片的http链接
                urls: _this.localIdsImg // 需要预览的图片http链接列表
            });
        },

        commit: function () {
            if (this.clickFlag) {
                alert("请勿多次点击！");
                return;
            }
            this.clickFlag = true;
            if (!this.comments || !this.comments.content) {
                alert('请填写评论内容');
            }
            if (!this.markSystem || !this.markSystem.recordId) {
                alert('当前页面失效，请退出重进');
            }
            if (!(this.emp && this.emp.recordId)) {
                alert('用户未登陆，请登录');
            }
            this.comments.maintenanceRecordId = this.markSystem.recordId;
            this.comments.createdBy = {};
            this.comments.createdBy.recordId = this.emp.recordId;
            $('#loadingModal').modal();
            var _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/kybsoftOA/addComment",
                data: JSON.stringify(this.comments),
                contentType: "application/json",
                success: function (data) {
                    if (data) {
                        _this.markSystem.commentList = data;
                        _this.comments = {};
                    }
                    $('#comment').modal('hide');
                    $('#loadingModal').modal('hide');
                    _this.clickFlag = false;
                }
            });
        },
        replyComment: function (item) {
            if (item && item.recordId) {
                this.comments.commentFaId = item.recordId;
            }
            $('#comment').modal();
        },
        replyCom: function (item, commen) {
            if (item && item.recordId) {
                this.comments.commentFaId = item.recordId;
            }
            if (commen && commen.recordId) {
                this.comments.commentId = commen.recordId;
            }
            $('#comment').modal();
        },
        close: function (item) {
            if (item.commentFlag) {
                this.$set(item, 'commentFlag', false);
            } else {
                this.$set(item, 'commentFlag', true);
            }
        },

        handleSelectChange() {
            if (this.num === '8') {
                this.loadMarkSystemList(this.status);
            } else {
                this.getMyWaiteRepairList()
            }
        },
        //计算实际工时
        calculateHoursTwo(item)
        {
            let totalHours = 0;
            let currentStartTime = null;
            if (item && item.hourSectionList && item.hourSectionList.length > 0) {
                item.hourSectionList.forEach(record => {
                    const maintenanceTime = new Date(record.maintenanceTime);
                    const timeDiff = currentStartTime ? Math.abs(currentStartTime - maintenanceTime) : 0;
                    switch (record.statusFlag) {
                        case "1": // 开始
                            currentStartTime = maintenanceTime;
                            break;
                        case "2": // 结束
                            if (currentStartTime) {
                                totalHours += (maintenanceTime - currentStartTime) / (1000 * 60 * 60);
                                currentStartTime = null;
                            }
                            break;
                        case "3": // 暂停
                            if (currentStartTime) {
                                totalHours += timeDiff / (1000 * 60 * 60);
                                currentStartTime = null;
                            }
                            break;
                    }
                });
                if (currentStartTime) {
                    totalHours += (new Date() - currentStartTime) / (1000 * 60 * 60); //
                }
            }
            return totalHours.toFixed(2);
        },
        hoursAppealOpen(item) {
            this.appealH = "";
            this.appealH = {...item};
            this.appealH.useHour = this.appealH.latestHours;
            $('#hoursAppeal').modal();
        },
        saveHoursAppeal(row) {
            if (!row || !row.hoursAppeal) {
                alert("申诉工时不能为空！");
                return;
            }
            if (!row || !row.reason) {
                alert("申诉原因不能为空！");
                return;
            }
            this.appealH.createdBy = this.user.userId;
            this.appealH.maintenanceId = this.appealH.recordId;
            this.appealH.useHour = this.appealH.hoursAppeal;
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/saveHoursAppeal",
                data: JSON.stringify(_this.appealH),
                contentType: "application/json",
                success: function (data) {
                    if (data == 'success') {
                        alert("工时申诉成功！");
                    }
                    $('#hoursAppeal').modal('hide');
                    _this.getMyWaiteRepairList()
                }
            });
        },
        getHoursRecordList(item) {
            const query = item;
            if (this.num === '8'){
                query.recordId = item.maintenanceId;
            }
            this.hoursAppealObj = item;
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/getHoursRecordList",
                data: JSON.stringify(item),
                contentType: "application/json",
                success: function (data) {
                    _this.hoursRecordList = data;
                    $('#hoursRecordList').modal();
                }
            });
        },
        closeWindow()
        {
            $('#addRepairWindow').modal('hide');
            $('#showMarkSystem').modal('hide');
            $('#hoursAppeal').modal('hide');
            $('#hoursRecordList').modal('hide');
            $('#showConfirmHours').modal('hide');
        },
    }
}