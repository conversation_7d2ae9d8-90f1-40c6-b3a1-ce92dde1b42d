<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.production.dao.ReplenishDao">
   <!--   生产补料   -->
	<sql id="replenishColumns">
		a.recordId,
		a.companyId AS "company.recordId",
		a.notificationId AS "notification.recordId",
		a.no AS "no",
		a.applicant AS "applicant",
		a.doApplicant AS "doApplicant.recordId",
		a.dept AS "dept.recordId",
		a.replenishDate AS "replenishDate",
		a.redeliveryDate AS "redeliveryDate",
		a.discardQty AS "discardQty",
		a.discardArea AS "discardArea",
		a.replenishPcsQty AS "replenishPcsQty",
		a.replenishPnlQty AS "replenishPnlQty",
		a.occupiedAvailableQty AS "occupiedAvailableQty",
		a.discardReason AS "discardReason",
		a.qtyPnlA AS "qtyPnlA",
		a.qtyPnlB AS "qtyPnlB",
		a.qtyPnlT AS "qtyPnlT",
		a.status AS "status",
		a.activeFlag AS "activeFlag",
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.remark AS "remark",
		a.productionSequence as "productionSequence",
		a.produceBatchNo as "produceBatchNo.recordId",
		a.bitSuspend as "bitSuspend",
		a.changeStatus as "changeStatus",
		a.aban as "aban",
		a.bban as "bban",
		a.daban as "daban",
		a.receiptsType as "receiptsType",
		a.confirmDay as "confirmDay",
		a.occupyMantissa
	</sql>
	
	<sql id="replenishJoins">
	</sql>
	
	<select id="findByNotification" resultType="Replenish">
		SELECT
		<include refid="replenishColumns" />,
		b.processCardAId AS "produceBatchNo.processCardAId",
		b.cardCount as "produceBatchNo.cardCount",
		b.status as "produceBatchNo.status"
		FROM pd_replenish a
		join pd_produce_batch b on b.recordId = a.produceBatchNo
		WHERE a.notificationid = #{notification.recordId} and a.companyId= #{company.recordId} and a.activeFlag = 1 and a.status in (700401, 700402, 700404, 700405, 700406)
	</select>
	
	<select id="getCountSum" resultType="String">
		SELECT 
			count(0)
		FROM pd_produce_record 
		WHERE producebatchId = #{produceBatchNo.recordId}
	</select>
	
	<!-- 查询补料单是否已生产 -->
	<select id="getProduceRecordCountTow" resultType="string">
		SELECT COUNT(a.recordId) from pd_produce_record a 
		LEFT JOIN pd_produce_batch b on a.produceBatchId=b.recordId and b.companyId=#{company.recordId} and b.activeFlag=#{DEL_FLAG_NORMAL}
		LEFT JOIN pd_produce_batch_detail c on b.recordId=c.produceBatchId and c.companyId=#{company.recordId} and c.activeFlag=#{DEL_FLAG_NORMAL}
		LEFT JOIN pd_replenish re on re.produceBatchNo=b.recordId and re.companyId=#{company.recordId} and re.activeFlag=#{DEL_FLAG_NORMAL}
		WHERE re.recordId=#{recordId} and a.companyId=#{company.recordId} and a.activeFlag=#{DEL_FLAG_NORMAL}
	</select>
	
	<!-- 根据通知单查询所有补料单 -->
	<select id="getReplenishlist" resultType="Replenish">
		select 
			<include refid="replenishColumns" />
		from pd_replenish a
		LEFT JOIN sl_notification b on a.notificationId=b.recordId
		where b.no=#{no} and a.status 
		IN(700401,700402,700404,700405,700406) and a.activeFlag=1 and a.companyId=#{company.recordId}
	</select>
	
	
	<!-- 工程变更查询 根据通知单查询所有补料单 -->
	<select id="findReplenishForChangeCardByNotification" resultType="Replenish">
		select 
			<include refid="replenishColumns" />
		from pd_replenish a
		LEFT JOIN sl_notification b on a.notificationId=b.recordId
		where b.recordId = #{recordId} 
			and a.activeFlag = #{DEL_FLAG_NORMAL}
			and a.status <![CDATA[<>]]> 700403 
			and a.status <![CDATA[<>]]> 700404 
			and a.companyId= #{company.recordId}
	</select>


	<!-- 打印 -->
	<select id="getPrint" resultType="Replenish">
		SELECT 
			a.recordId,
			a.companyId AS "company.recordId",
			f.name as "company.name",
			f.logo as "company.logo",
			a.notificationId AS "notification.recordId",
			a.no AS "no",
			a.applicant AS "applicant",
			a.replenishDate AS "replenishDate",
			a.redeliveryDate AS "redeliveryDate",
			a.discardQty AS "discardQty",
			a.discardArea AS "discardArea",
			a.replenishPcsQty AS "replenishPcsQty",
			a.replenishPnlQty AS "replenishPnlQty",
			a.occupiedAvailableQty AS "occupiedAvailableQty",
			a.discardReason AS "discardReason",
			a.status AS "status",
			a.activeFlag AS "activeFlag",
			a.createdBy AS "createdBy.recordId",
			a.createdDate,
			a.lastUpdBy AS "lastUpdBy.recordId",
			a.lastUpdDate,
			a.remark AS "remark",
			a.productionSequence as "productionSequence",
			b.craftNo as "notification.craftNo",
			b.quantity as "deliveryQty",
			c.userName as "createdBy.userName",
			mb.name AS "branch.name",
			mb.code AS "branch.code",
			mb.shortName AS "branch.shortName",
			mb.address AS "branch.address",
			mb.zip AS "branch.zip",
			mb.phone AS "branch.phone",
			mb.fax AS "branch.fax",
			mb.email AS "branch.email",
			mb.website AS "branch.website",
			mb.maker AS "branch.maker",
			mb.nameEn AS "branch.nameEn",
			mb.templatePath AS "branch.templatePath",
			mb.logoPath AS "branch.logoPath"
			
			FROM pd_replenish a
			join md_company f on f.recordId = a.companyId
			LEFT JOIN sl_notification b on a.notificationId = b.recordId
			LEFT JOIN sm_user c on a.createdBy = c.recordId
			LEFT JOIN sl_contract_detail sd on sd.recordId = b.contractDetailId
			LEFT JOIN sl_contract sc on sc.recordId = sd.contractId
			LEFT JOIN md_branch mb on mb.recordId  = sc.branchId

			WHERE a.recordId = #{recordId}
	</select>
  
	  <!-- 根据通知单编号去查询补料单 -->
	  <select id="selectByNotification" resultType="Replenish">
	  	select <include refid="replenishColumns"/> from pd_replenish a where a.notificationId = #{recordId} 
	  </select>
  
	<select id="get" resultType="Replenish">
		SELECT 
			<include refid="replenishColumns"/>,
			a.produceBatchNo as "produceBatchNo.recordId",
			b.craftNo as "notification.craftNo",
			b.quantity as "deliveryQty",
			c.userName as "createdBy.userName"
		FROM pd_replenish a
		LEFT JOIN sl_notification b on a.notificationId = b.recordId
		LEFT JOIN sm_user c on a.createdBy = c.recordId
		<include refid="replenishJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="getByIdAndNo" resultType="Replenish">
		SELECT 
			<include refid="replenishColumns"/>,
			a.produceBatchNo as "produceBatchNo.recordId",
			b.craftNo as "notification.craftNo",
			b.quantity as "deliveryQty",
			c.userName as "createdBy.userName"
		FROM pd_replenish a
		LEFT JOIN sl_notification b on a.notificationId = b.recordId
		LEFT JOIN sm_user c on a.createdBy = c.recordId
		<include refid="replenishJoins"/>
		WHERE a.recordId = #{recordId} and a.no = #{no} 
	</select>
	
	<select id="produceBatchDetailByFedRecordId" resultType="ProduceBatchDetail">
		select 
			b.recordId,
			b.companyId AS "company.recordId",
			b.produceBatchId AS "produceBatch.recordId",
			b.no as "no",
			b.qtyPnlA AS "qtyPnlA",       
		    b.qtySetA AS "qtySetA",       
		    b.qtyPcsA AS "qtyPcsA",       
		    b.qtyPnlB AS "qtyPnlB",       
		    b.qtySetB AS "qtySetB",       
		    b.qtyPcsB AS "qtyPcsB",       
		    b.qtyPnlT AS "qtyPnlT",       
		    b.qtySetT AS "qtySetT",       
		    b.qtyPcsT AS "qtyPcsT",
		    b.discardQty AS "discardQty",
			b.activeFlag,
			b.createdBy AS "createdBy",
			b.createddate AS "createdDate",
			b.lastupdby AS "lastUpdBy",
			b.lastupddate AS "lastUpdDate",
			b.remark AS "remark",
			b.excrete as "excrete",
			b.excreterecordId as "excreteRecordId",
			b.batchDetailType as "batchDetailType",
			b.status as "status",
			b.oldDetailId AS "oldDetailId"
		from pd_produce_batch_detail b 
		where b.produceBatchId = #{recordId} AND b.activeFlag = 1
	</select>
	
	
	<!-- 查询数据是否是本表的 -->
	<select id="selectTemp"  resultType="Integer">
		select count(*) from pd_replenish pd where pd.companyId = #{company.recordId} and pd.recordId =#{recordId}  and pd.no =#{no} 
	</select>
	
	
	<!-- wip调整顺序需要查看数据是否位于本表 -->
	<select id="selectData"  resultType="Integer">
		select count(a.productionSequence) from pd_replenish a  where a.productionSequence = #{productionSequence} and a.companyId = #{company.recordId}
	</select>
		
	<!-- 查询补料单中 此单据编号是否被用了 -->
	<select id="findFedNoisEnable" resultType="Integer">
		select COUNT(*) from pd_replenish a where a.no = #{no}  and a.companyId  = #{company.recordId}
	</select>
	
	<resultMap id="ResultMap_Replenish_Batch_BatchDetail" type="Replenish">
		<id property="recordId" column="recordId" />
		<result property="no" column="no"  />
		<result property="discardQty" column="discardQty" />
		<result property="qtyPnlA" column="qtyPnlA" />
		<result property="qtyPnlB" column="qtyPnlB" />
		<result property="qtyPnlT" column="qtyPnlT" />
		
		<association property="company" javaType="Company">
			<id property="recordId" column="company.recordId"/>
		</association>
		
		<association property="produceBatchNo" javaType="ProduceBatch">
			<id property="recordId" column="produceBatch.recordId"/>
			<result property="processCardAId" column="produceBatch.processCardAId" />
			
			<collection property="produceBatchDetail" ofType="ProduceBatchDetail">
				<id property="recordId" column="produceBatch.produceBatchDetail.recordId"/>
			</collection>
		</association>
	</resultMap>
	<select id="findReplenishList" resultMap="ResultMap_Replenish_Batch_BatchDetail"> 
	    select 
			a.recordId,
			a.companyId AS "company.recordId",
			a.no AS "no",
			a.discardQty AS "discardQty",
			a.qtyPnlA AS "qtyPnlA",
			a.qtyPnlB AS "qtyPnlB",
			a.qtyPnlT AS "qtyPnlT",
			pb.recordId as "produceBatch.recordId", 
			pb.processCardAId as "produceBatch.processCardAId", 
			pbd.recordId as "produceBatch.produceBatchDetail.recordId" 
		from pd_replenish a 
		join pd_produce_batch pb on pb.recordId = a.produceBatchNo and pb.activeFlag = #{DEL_FLAG_NORMAL}
		left join pd_produce_batch_detail pbd on pbd.produceBatchId = pb.recordId and pbd.activeFlag = #{DEL_FLAG_NORMAL}
		where a.companyId = #{company.recordId}
    </select>
    
    <select id="getQcReplenishInitialization" resultType="Replenish">
    	SELECT 
			<include refid="replenishColumns"/>,
				b.no as "notification.no",
				b.processCardAId as "notification.cardA.recordId",
				c.userName as "createdBy.userName",
				b.craftNo as "notification.craftNo",
				b.quantity as "notification.quantity",
				b.mergeType as "notification.mergeType",
				b.failedPcsQty as "notification.failedPcsQty",
				b.occupiedAvailableQty AS "notification.occupiedAvailableQty",
				d.cardCount as "produceBatchCount",
				e.cancelCause as "notification.cardA.cancelCause",
				e.status as "notification.cardA.status",
				ecb.totalSetCount as "notification.partA.totalSetCount",
				ecb.pcsCount as "notification.partA.pcsCount",
				ecbc.totalSetCount as "notification.partB.totalSetCount",
				ecbc.pcsCount as "notification.partB.pcsCount"
			FROM pd_replenish a
			LEFT JOIN sl_notification b on a.notificationId = b.recordId
	    	LEFT JOIN sm_user c on a.doApplicant = c.recordId
	    	LEFT JOIN pd_produce_batch d on a.produceBatchNo = d.recordId
	    	LEFT JOIN eg_carda e on e.recordId = b.processCardAId
			LEFT JOIN eg_cardb eb on e.processCardBId = eb.recordId
			LEFT JOIN eg_cardb_board_cutting ec on eb.boardCuttingId = ec.recordId
			LEFT JOIN eg_cardb_board_part ecb on ec.partAId = ecb.recordId
			LEFT JOIN eg_cardb_board_part ecbc on ec.partBId = ecbc.recordId
		WHERE  a.activeFlag=#{DEL_FLAG_NORMAL} and a.status=700402 and a.companyId = #{company.recordId}
    </select>
    
	<select id="findList" resultType="Replenish">
		SELECT 
			<include refid="replenishColumns"/>,
				b.no as "notification.no",
				b.processCardAId as "notification.cardA.recordId",
				c.userName as "createdBy.userName",
				b.craftNo as "notification.craftNo",
				b.quantity as "notification.quantity",
				b.mergeType as "notification.mergeType",
				b.failedPcsQty as "notification.failedPcsQty",
				b.occupiedAvailableQty AS "notification.occupiedAvailableQty",
				d.cardCount as "produceBatchCount",
				e.cancelCause as "notification.cardA.cancelCause",
				e.status as "notification.cardA.status",
				ecb.totalSetCount as "notification.partA.totalSetCount",
				ecb.pcsCount as "notification.partA.pcsCount",
				ecbc.totalSetCount as "notification.partB.totalSetCount",
				ecbc.pcsCount as "notification.partB.pcsCount",
				e.setLength AS "setLength",
				e.setWidth AS "setWidth",
				e.pnlDivisor,
				e.pcsLength AS "unitLength",
				e.pcsWidth AS "unitWidth",
				e.remark AS "notifiRemark",
				CASE WHEN b.mergeType = 200602 THEN ff.deliveryDate
				ELSE IFNULL(b.estimateDate,b.deliveryDate)
				END AS "notification.deliveryDate",
				sra.quantity AS "reject.quantity",
				sra.loadNums AS "reject.loadNums",
				sra.repirNums AS "reject.repirNums",
				sra.fedNum AS "reject.fedNum",
				sra.repirDiscardNum AS "reject.repirDiscardNum"
			FROM pd_replenish a
			LEFT JOIN sl_notification b on a.notificationId = b.recordId
	    	LEFT JOIN sm_user c on a.doApplicant = c.recordId
	    	LEFT JOIN pd_produce_batch d on a.produceBatchNo = d.recordId
	    	LEFT JOIN eg_carda e on e.recordId = b.processCardAId
			LEFT JOIN eg_cardb eb on e.processCardBId = eb.recordId
			LEFT JOIN eg_cardb_board_cutting ec on eb.boardCuttingId = ec.recordId
			LEFT JOIN eg_cardb_board_part ecb on ec.partAId = ecb.recordId
			LEFT JOIN eg_cardb_board_part ecbc on ec.partBId = ecbc.recordId
			LEFT JOIN 
			(
				SELECT
					mergeId AS "mergeId",
					MIN(deliveryDate) AS "deliveryDate"
				FROM sl_notification
				WHERE companyId = #{company.recordId} AND activeFlag = 1 AND mergeId IS NOT NULL
				AND `status` <![CDATA[<>]]> 200406
			) ff ON ff.mergeId = b.recordId
			LEFT JOIN st_reject_application sra ON sra.recordId = a.rejectId
		<where>
			a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
			<if test=" no != null and no != ''">
				AND a.no LIKE
				<if test="dbName == 'mssql'">'%'+#{no}+'%'</if>
				<if test="dbName == 'mysql'">concat('%',#{no},'%')</if>
			</if>

			<if test=" status != null and status != ''">
				AND a.status LIKE
				<if test="dbName == 'mssql'">'%'+#{status}+'%'</if>
				<if test="dbName == 'mysql'">concat('%',#{status},'%')</if>
			</if>

			<if test=" changeStatus != null and changeStatus != ''">
				AND a.changeStatus LIKE
				<if test="dbName == 'mssql'">'%'+#{changeStatus}+'%'</if>
				<if test="dbName == 'mysql'">concat('%',#{changeStatus},'%')</if>
			</if>


			<!-- 生产编号 -->
			<if
				test=" notification != null and notification.craftNo != '' and notification.craftNo != null ">
				AND b.craftNo LIKE
				<if test="dbName == 'mssql'">'%'+#{notification.craftNo}+'%'</if>
				<if test="dbName == 'mysql'">concat('%',#{notification.craftNo},'%')</if>
			</if>


			<if
				test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} or a.doApplicant = #{createdBy.recordId}
				or e.recordId IN  (SELECT	noti.processCardAId FROM sl_notification noti JOIN sl_contract_detail contdeatil
				ON noti.contractDetailId = contdeatil.recordId JOIN sl_contract cont ON cont.recordId = contdeatil.contractId
				WHERE cont.userId =  #{createdBy.recordId}  
		   		or cont.customerId in (SELECT cs.customerId from sm_customer_salesAssistant cs WHERE cs.activeFlag = #{DEL_FLAG_NORMAL} AND cs.userId = #{createdBy.recordId})
				  )
				)
			</if>
			
			<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
					AND a.createdDate >= #{sentTimeStartQr}
			</if>
			<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
				AND a.createdDate <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findFedingsForStock"  resultType="Replenish">
		select 
			<include refid="replenishColumns"/>,
			sn.craftNo AS "notification.craftNo"
		 from pd_replenish a 
		 JOIN sl_notification sn ON a.notificationId = sn.recordId
		 <where>
			a.companyId = #{company.recordId} 
			and a.activeFlag = #{DEL_FLAG_NORMAL}
			<!-- 已确认的投料单-->
			and a.status in (#{status},#{statuses})
			<!-- 未投料的投料单-->
			and ISNULL(a.replenishDate)
			AND !ISNULL(sn.estimateRemark) AND sn.estimateRemark <![CDATA[<>]]> ''
			order by a.lastUpdDate desc
		</where>
	</select>
	
	<select id="findAllList" resultType="Replenish">
		SELECT 
			<include refid="replenishColumns"/>
		FROM pd_replenish a
		<include refid="replenishJoins"/>
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findProduceBatchDetailCraft" resultType="ProduceBatchDetailCraft">
		select 
			c.recordId as "recordId",
			c.craftNo as "craftNo",
			SUM(c.qtyPcsT) as "qtyPcsT" 
		from pd_produce_batch_detail_craft c
		join pd_produce_batch_detail pbd on pbd.recordId = c.produceBatchDetailId
		join pd_produce_batch pb on pb.recordId =c.produceBatchId
		join pd_replenish r on r.produceBatchNo = pb.recordId and r.recordId = #{recordId} 
		where c.companyId = #{company.recordId} 
		group by c.craftNo
	</select>
	

	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO pd_replenish(
			companyId,
			notificationId,
			no,
			applicant,
			doApplicant,
			dept,
			replenishDate,
			redeliveryDate,
			discardQty,
			discardArea,
			replenishPcsQty,
			replenishPnlQty,
			occupiedAvailableQty,
			discardReason,
			qtyPnlA,
			qtyPnlB,
			qtyPnlT,
			status,
			activeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			remark,
			productionSequence,
			bitSuspend,
			changeStatus,
			receiptsType,
			rejectId,
			occupyMantissa
		) VALUES (
			#{company.recordId},
			#{notification.recordId},
			#{no},
			#{applicant},
			#{doApplicant.recordId},
			#{dept.recordId},
			#{replenishDate},
			#{redeliveryDate},
			#{discardQty},
			#{discardArea},
			#{replenishPcsQty},
			#{replenishPnlQty},
			#{occupiedAvailableQty},
			#{discardReason},
			#{qtyPnlA},
			#{qtyPnlB},
			#{qtyPnlT},
			#{status},
			#{activeFlag},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{productionSequence},
			#{bitSuspend},
			#{changeStatus},
			#{receiptsType},
			#{rejectId},
			#{occupyMantissa}
		)
	</insert>
	
	<update id="update">
		UPDATE pd_replenish SET 	
			applicant = #{applicant},
			doApplicant = #{doApplicant.recordId},
			dept = #{dept.recordId},
			replenishDate = #{replenishDate},
			redeliveryDate = #{redeliveryDate},
			discardQty = #{discardQty},
			discardArea = #{discardArea},
			replenishPcsQty = #{replenishPcsQty},
			replenishPnlQty = #{replenishPnlQty},
			occupiedAvailableQty = #{occupiedAvailableQty},
			discardReason = #{discardReason},
			qtyPnlA = #{qtyPnlA},
			qtyPnlB = #{qtyPnlB},
			qtyPnlT = #{qtyPnlT},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			remark = #{remark},
			bitSuspend = #{bitSuspend},
			changeStatus = #{changeStatus},
			occupyMantissa = #{occupyMantissa}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateQty">
		UPDATE pd_replenish SET 	
			qtyPnlA = #{qtyPnlA},
			qtyPnlB = #{qtyPnlB},
			qtyPnlT = #{qtyPnlT}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateOfAdjust">
		UPDATE pd_replenish SET 	
			discardQty = #{discardQty},
			qtyPnlA = #{qtyPnlA},
			qtyPnlB = #{qtyPnlB},
			qtyPnlT = #{qtyPnlT},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
			<if test="status != null and status != ''">
				, status = #{status}
			</if>
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE  pd_replenish  SET
		activeFlag = 2
		WHERE recordId = #{recordId}
	</update>
	
	<update id ="updateStatusById">
		UPDATE  pd_replenish  SET
		status = #{status}
		WHERE recordId = #{recordId}
	</update>
	
	<update id ="updateReplenishRed">
		UPDATE  pd_replenish  SET
		status = #{status},
		replenishDate = #{replenishDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id = "updateStatusAndConfirmDay">
		UPDATE  pd_replenish  SET
		status = #{status},
		confirmDay = #{confirmDay}
		WHERE recordId = #{recordId}
	</update>
	
	<update id = "updateProduceBatchNo">
		UPDATE  pd_replenish  SET
		produceBatchNo = #{produceBatchNo.recordId}
		WHERE recordId = #{recordId}
	</update>
	
	<update id = "updateProduceBatch">
		UPDATE  pd_replenish  SET
		produceBatchNo = #{produceBatch.recordId}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateProductionSequenceNo">
		UPDATE  pd_replenish  SET
		productionSequence = #{productionSequence}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateProductionSequenceNoBatch">
		UPDATE  pd_replenish  SET
		productionSequence = productionSequence+1
		WHERE productionSequence >= #{productionSequence} and companyId=#{company.recordId}
	</update>
	
	<!-- 更新补料完成时间 -->
	<update id="updateReplenishDate">
		UPDATE  pd_replenish  SET
		replenishDate = #{replenishDate},
		lastUpdDate = #{lastUpdDate},
		bitSuspend = 1
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateReplenishDateAndStatus">
		UPDATE  pd_replenish  SET
		replenishDate = #{replenishDate},
		lastUpdDate = #{lastUpdDate},
		status = #{status},
		bitSuspend = 1
		WHERE recordId = #{recordId}
	</update>
	
	
	
	<update id="starvedFeeding">
		UPDATE  pd_replenish  SET
		status = 700405,
		bitSuspend = 2		
		WHERE recordId = #{recordId}
	</update>
	
	
	<update id="updateSuspend">
		UPDATE pd_replenish SET
			bitSuspend = 3
		where recordId = #{recordId}
	</update>
	
	<update id="updateRecover">
		UPDATE pd_replenish SET
			bitSuspend = 1
		where recordId = #{recordId}
	</update>
	
	<select id="findOccupiedAvailableById" resultType="Replenish">
		select 
			a.occupiedAvailableQty 
		from pd_replenish a 
		where a.companyId = #{company.recordId}  
			and a.recordId = #{recordId}
			and a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	
	<update id="updateIsyu">
		UPDATE pd_replenish SET 	
			aban = #{aban},
			bban = #{bban},
			daban = #{daban},
			changeStatus = #{changeStatus}
		WHERE recordId = #{recordId}
	</update>
	
	<!-- 导出 -->
	<select id="findExpList" resultType="Replenish">
		SELECT 
				a.recordId,
				a.companyId AS "company.recordId",
				a.qtyPnlA,
				a.qtyPnlB,
				a.qtyPnlT,
				a.discardReason,
				a.remark,
				a.status,
				a.receiptsType,
				a.occupiedAvailableQty,
				a.no,
				a.discardQty,
				a.createdDate,
				b.no as 'notification.no',
				b.craftNo as 'notification.craftNo',
				b.quantity as 'notification.quantity',
				c.userName as 'doApplicant.userName',
				b.failedPcsQty as 'notification.failedPcsQty',
				d.cardCount as 'produceBatchCount'
		FROM pd_replenish a
		LEFT JOIN sl_notification b on a.notificationId = b.recordId
		LEFT JOIN sm_user c on a.doApplicant = c.recordId
		LEFT JOIN pd_produce_batch d on a.produceBatchNo = d.recordId
		<where>
			a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
			<if test=" no != null and no != ''">
				AND a.no LIKE
				<if test="dbName == 'mssql'">'%'+#{no}+'%'</if>
				<if test="dbName == 'mysql'">concat('%',#{no},'%')</if>
			</if>

			<if test=" status != null and status != ''">
				AND a.status LIKE
				<if test="dbName == 'mssql'">'%'+#{status}+'%'</if>
				<if test="dbName == 'mysql'">concat('%',#{status},'%')</if>
			</if>

			<if test=" changeStatus != null and changeStatus != ''">
				AND a.changeStatus LIKE
				<if test="dbName == 'mssql'">'%'+#{changeStatus}+'%'</if>
				<if test="dbName == 'mysql'">concat('%',#{changeStatus},'%')</if>
			</if>
			<!-- 生产编号 -->
			<if
				test=" notification != null and notification.craftNo != '' and notification.craftNo != null ">
				AND b.craftNo LIKE
				<if test="dbName == 'mssql'">'%'+#{notification.craftNo}+'%'</if>
				<if test="dbName == 'mysql'">concat('%',#{notification.craftNo},'%')</if>
			</if>
			<if
				test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} or a.doApplicant = #{createdBy.recordId}
				or b.recordId IN  (
				SELECT	noti.processCardAId FROM sl_notification noti JOIN sl_contract_detail contdeatil
				ON noti.contractDetailId = contdeatil.recordId JOIN sl_contract cont ON cont.recordId = contdeatil.contractId
				WHERE cont.userId =  #{createdBy.recordId}  
				  )
		   		or c.customerId in (SELECT cs.customerId from sm_customer_salesAssistant cs WHERE cs.activeFlag = #{DEL_FLAG_NORMAL} AND cs.userId = #{createdBy.recordId})
				 )
			</if>
			
			<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
					AND a.createdDate >= #{sentTimeStartQr}
			</if>
			<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
				AND a.createdDate <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<!-- fzd:2017-03-24  根据补料单编号获取通知单编号 -->
	<select id="getNotifiNo" resultType="Replenish">
	SELECT 
			a.no AS "no",
			a.createdBy AS "createdBy.recordId",
			b.no AS "notification.no",
			sc.no AS "notification.contract.no",
			mc.name AS "notification.contract.customer.name",
			mc.salesman AS "notification.contract.user.recordId"
		FROM pd_replenish a
		LEFT JOIN sl_notification b on a.notificationId = b.recordId
		LEFT JOIN sl_contract_detail cd on cd.recordId = b.contractDetailId
		LEFT JOIN sl_contract sc on sc.recordId = cd.contractId
		LEFT JOIN md_customer mc on mc.recordId = sc.customerId
		where a.no = #{no} and a.companyId = #{company.recordId} limit 0,1
	</select>
	
	<!-- fzd:2017-03-24  根据补料单编号获取通知单编号 -->
	<select id="getNotifiRecordId" resultType="Replenish">
	SELECT 
			a.no AS "no",
			a.createdBy AS "createdBy.recordId",
			b.no AS "notification.no",
			sc.no AS "notification.contract.no",
			mc.name AS "notification.contract.customer.name",
			mc.salesman AS "notification.contract.user.recordId"
		FROM pd_replenish a
		LEFT JOIN sl_notification b on a.notificationId = b.recordId
		LEFT JOIN sl_contract_detail cd on cd.recordId = b.contractDetailId
		LEFT JOIN sl_contract sc on sc.recordId = cd.contractId
		LEFT JOIN md_customer mc on mc.recordId = sc.customerId
		where a.recordId = #{recordId}
	</select>
	
	<!-- WC 2017-04-26 查询该补料单类型：是正常补料单还是客诉补料单 -->
	<select id="getReplenishType" resultType="Integer">
		SELECT 
			receiptsType
		FROM pd_replenish
		WHERE recordId = #{recordId}
	</select>
	
	<!--zjn 2017-07-03 根据补料编号单获取A、B板尺寸 -->
	<select id="getPartByreplenishNo" resultType="Replenish">
	    SELECT
        a.discardQty as "discardQty",
        a.companyId as "company.recordId",
        b.no as "notification.no",
        b.status as "notification.status",
        ec.totalPcses as "boardCutting.totalPcses",
	    ecb.totalSetCount as "boardCutting.partA.totalSetCount",
	    ecb.pcsCount as "boardCutting.partA.pcsCount",
        ecb.horizontalSetLength as "boardCutting.partA.horizontalSetLength",
        ecb.verticalSetLength as "boardCutting.partA.verticalSetLength",
        ecbc.pcsCount as "boardCutting.partB.pcsCount"
		from pd_replenish a  
        LEFT JOIN sl_notification b ON a.notificationId = b.recordId
        LEFT JOIN pd_feeding c ON b.feedingId = c.recordId
		LEFT JOIN eg_carda d on b.processCardAId = d.recordId
		LEFT JOIN eg_cardb eb on d.processCardBId = eb.recordId
		LEFT JOIN eg_cardb_board_cutting ec on eb.boardCuttingId = ec.recordId
		LEFT JOIN eg_cardb_board_part ecb on ec.partAId = ecb.recordId
		LEFT JOIN eg_cardb_board_part ecbc on ec.partBId = ecbc.recordId
		where
		a.no = #{no} and a.companyId = #{company.recordId}
	</select>
	
	<select id="getQtyData" resultType="Notification">
		SELECT
			(
				SELECT
					SUM(IFNULL(a.discardQty,0))
				FROM pd_replenish a
				WHERE
					a.companyId = aa.companyId
				AND a.notificationId = aa.recordId
				AND a.activeFlag = 1
				AND a. STATUS <![CDATA[<>]]> 700403
				AND a.receiptsType <![CDATA[<>]]> 2
			) AS "replenishQty",
			(
				SELECT 
					SUM(IFNULL(a.discardPcsQty,0))
				FROM qc_discard a
				LEFT JOIN qc_inspect b ON b.recordId = a.inspectId
				LEFT JOIN pd_produce_batch c ON c.recordId = b.produceBatchId
				WHERE a.companyId = aa.companyId AND a.activeFlag = 1
				AND b.`status` = 900605 AND c.notificationId = aa.recordId
			) AS "discardQty"
		FROM sl_notification aa
		WHERE aa.companyId = #{company.recordId} AND aa.recordId = #{recordId} AND aa.activeFlag = 1
	</select>
	
	<select id="getExportReplenishList" resultType="com.kyb.pcberp.modules.production.vo.ReplenishVo">
		SELECT
			a.no,
			CASE a.`status` WHEN 700401 THEN '未确认'
			WHEN 700402 THEN '已确认'
			WHEN 700403 THEN '已取消'
			WHEN 700404 THEN '已完成'
			WHEN 700405 THEN '缺料'
			WHEN 700406 THEN ' 暂停'
			END AS "replenishStatus",
			a.createdDate AS "createdDate",
			a.replenishDate AS "replenishDate",
			CASE sn.status WHEN 200401 THEN "未确认"
			WHEN 200402 THEN "已确认"
			WHEN 200403 THEN "已入库"
			WHEN 200404 THEN "已投料"
			WHEN 200405 THEN "补料中"
			WHEN 200406 THEN "已作废"
			END AS "noticeStatus",
			sn.no AS "noticeNo",
			DATE_FORMAT(sn.orderDate,'%Y年%m月%d日')  AS "orderDate",
			DATE_FORMAT(sn.deliveryDate,'%Y年%m月%d日')  AS "deliveryDate",
			ec.customerNos AS "customerNo",
			IFNULL(sc.customerPo,aa.customerPo) AS "customerPo",
			sn.craftNo AS "craftNo",
			sn.customerModel AS "customerModule",
			orderType.value AS "orderType",
			IFNULL(boardLevel.value,aa.boardLevel) AS "boardLevel",
			IFNULL(materialType.value,aa.materialType) AS "materialType",
			IFNULL(boardThickness.value,aa.boardThickness) AS "boardThickness",
			CASE WHEN orderType.value IN ('新单样品','返单样品','样品返单有改') THEN CAST(ecbpA.pnlLength AS char)+0 ELSE NULL END AS "sizeLength",
			CASE WHEN orderType.value IN ('新单样品','返单样品','样品返单有改') THEN CAST(ecbpA.pnlWidth AS char)+0 ELSE NULL END AS "sizeWidth",
			(SELECT remark FROM eg_carda_process_value WHERE processCardAId = ec.recordId AND activeFlag = 1 ORDER BY seqNum LIMIT 1) AS "specification",
			(SELECT ecpcv.processCraftValue FROM eg_carda_process_craft_value ecpcv LEFT JOIN eg_carda_process_value ecpv ON ecpv.recordId = ecpcv.processValueId WHERE ecpcv.companyId = a.companyId AND ecpcv.processCraftId = 3 AND ecpv.processCardAId = ec.recordId ORDER BY ecpv.seqNum LIMIT 1) AS "copperThickness",
			IFNULL(surfaceProcess.value,aa.surfaceProcess) AS "surfaceProcess",
		 	IFNULL(solderMaskType.value,aa.solderMaskType) AS "solderMaskType",
			IFNULL(characterType.value,aa.characterType) AS "characterType",
			IFNULL(shapingWay.value,aa.shapingWay) AS "shapingWay",
			IFNULL(testMethod.value,aa.testMethod) AS "testMethod",
			CAST(ec.setLength AS char)+0 AS "setLength",
			CAST(ec.setWidth AS char)+0 AS "setWidth",
			ec.pnlDivisor AS "pnlDivisor",
			CAST(ec.pcsLength AS char)+0 AS "pcsLength",
			CAST(ec.pcsWidth AS char)+0 AS "pcsWidth",
			a.discardQty AS "discardQty",
			a.qtyPnlT AS "fedQuantity",
			a.discardReason AS "discardReason",
			ROUND (a.discardQty * ec.setLength * ec.setWidth / ec.pnlDivisor / 1000000,4) AS "pcsArea",
			ecbpA.pnlLength AS "ecbApnlLength",
			ecbpA.pnlWidth AS "ecbApnlWidth",
			a.qtyPnlA AS "ecbApnlCount",
			ecbpB.pnlLength AS "ecbBpnlLength",
			ecbpB.pnlWidth AS "ecbBpnlWidth",
			a.qtyPnlB AS "ecbBpnlCount",
			IFNULL(
				aa.urgentName,
				(
					CASE sn.urgentFlag
					WHEN 0 THEN
						"不加急"
					WHEN 1 THEN
						"加急"
					END
				)
			) AS "urgentName",
			ecbpA.totalSetCount AS "ecbAsetCount",
			ecbpB.totalSetCount AS "ecbBsetCount"
		FROM pd_replenish a
		LEFT JOIN sl_notification sn ON sn.recordId = a.notificationId AND sn.companyId = a.companyId AND sn.activeFlag = 1
		LEFT JOIN eg_carda ec ON ec.recordId = sn.processCardAId AND ec.companyId = a.companyId AND ec.activeFlag = 1
		LEFT JOIN eg_cardb ecb ON ecb.recordId = ec.processCardBId AND ecb.companyId = a.companyId AND ecb.activeFlag = 1
		LEFT JOIN eg_cardb_board_cutting ecbc ON ecbc.recordId = ecb.boardCuttingId AND ecbc.companyId = a.companyId AND ecbc.activeFlag = 1
		LEFT JOIN eg_cardb_board_part ecbpA ON ecbpA.recordId = ecbc.partAId AND ecbpA.companyId = a.companyId AND ecbpA.activeFlag = 1
		LEFT JOIN eg_cardb_board_part ecbpB ON ecbpB.recordId = ecbc.partBId AND ecbpB.companyId = a.companyId AND ecbpB.activeFlag = 1
		LEFT JOIN sl_contract_detail scd ON scd.recordId = sn.contractDetailId AND scd.companyId = a.companyId AND scd.activeFlag = 1
		LEFT JOIN sl_contract sc ON sc.recordId = scd.contractId AND sc.companyId = a.companyId AND sc.activeFlag = 1
		LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId AND scc.companyId = a.companyId AND scc.activeFlag = 1

		LEFT JOIN md_dict_value orderType ON orderType.recordId = sn.orderType AND orderType.itemId = 16
		LEFT JOIN md_dict_value boardLevel ON boardLevel.recordId = scc.boardLevel AND boardLevel.itemId = 1
		LEFT JOIN md_dict_value materialType ON materialType.recordId = scc.materialType AND materialType.itemId = 2
		LEFT JOIN md_dict_value boardThickness ON boardThickness.recordId = scc.boardThickness AND boardThickness.itemId = 3
		LEFT JOIN md_dict_value surfaceProcess ON surfaceProcess.recordId = scc.surfaceProcess AND surfaceProcess.itemId = 5
		LEFT JOIN md_dict_value solderMaskType ON solderMaskType.recordId = scc.solderMaskType AND solderMaskType.itemId = 6
		LEFT JOIN md_dict_value characterType ON characterType.recordId = scc.characterType AND characterType.itemId = 7
		LEFT JOIN md_dict_value shapingWay ON shapingWay.recordId = scc.shapingWay AND shapingWay.itemId = 8
		LEFT JOIN md_dict_value testMethod ON testMethod.recordId = scc.testMethod AND testMethod.itemId = 9
		LEFT JOIN
		(
			SELECT
				a.mergeId AS "recordId",
				GROUP_CONCAT(sc.customerPo) AS "customerPo",
				GROUP_CONCAT(boardLevel.value) AS "boardLevel",
				GROUP_CONCAT(materialType.value) AS "materialType",
				GROUP_CONCAT(boardThickness.value) AS "boardThickness",
				GROUP_CONCAT(surfaceProcess.value) AS "surfaceProcess",
				GROUP_CONCAT(solderMaskType.value) AS "solderMaskType",
				GROUP_CONCAT(characterType.value) AS "characterType",
				GROUP_CONCAT(shapingWay.value) AS "shapingWay",
				GROUP_CONCAT(testMethod.value) AS "testMethod",
				GROUP_CONCAT(
					CASE a.urgentFlag
					WHEN 0 THEN
						"不加急"
					WHEN 1 THEN
						"加急"
					END
				) AS "urgentName"
			FROM sl_notification a
			LEFT JOIN sl_contract_detail scd ON scd.recordId = a.contractDetailId
			LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId
			LEFT JOIN sl_contract sc ON sc.recordId = scd.contractId
			LEFT JOIN md_dict_value boardLevel ON boardLevel.recordId = scc.boardLevel AND boardLevel.itemId = 1
			LEFT JOIN md_dict_value materialType ON materialType.recordId = scc.materialType AND materialType.itemId = 2
			LEFT JOIN md_dict_value boardThickness ON boardThickness.recordId = scc.boardThickness AND boardThickness.itemId = 3
			LEFT JOIN md_dict_value surfaceProcess ON surfaceProcess.recordId = scc.surfaceProcess AND surfaceProcess.itemId = 5
			LEFT JOIN md_dict_value solderMaskType ON solderMaskType.recordId = scc.solderMaskType AND solderMaskType.itemId = 6
			LEFT JOIN md_dict_value characterType ON characterType.recordId = scc.characterType AND characterType.itemId = 7
			LEFT JOIN md_dict_value shapingWay ON shapingWay.recordId = scc.shapingWay AND shapingWay.itemId = 8
			LEFT JOIN md_dict_value testMethod ON testMethod.recordId = scc.testMethod AND testMethod.itemId = 9
			WHERE a.companyId = #{factoryComId} AND a.activeFlag = 1 AND !ISNULL(a.mergeId) GROUP BY a.mergeId
		) aa ON aa.recordId = sn.recordId
		<where>
			a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
			<if test="no != null and no != ''">
				AND REPLACE(a.no," ","") LIKE CONCAT('%',REPLACE(#{no}," ",""),'%')
			</if>
			<if test="status != null and status != ''">
				<if test="status != 700403">
					AND REPLACE(a.status," ","") LIKE CONCAT ('%',REPLACE(#{status}," ",""),'%')
					AND a.status <![CDATA[<>]]> 700403
				</if>
				<if test="status == 700403">
					AND REPLACE(a.status," ","") LIKE CONCAT ('%',REPLACE(#{status}," ",""),'%')
				</if>
			</if>
			<if test=" changeStatus != null and changeStatus != ''">
				AND REPLACE(a.changeStatus," ","") LIKE CONCAT ('%',REPLACE(#{changeStatus}," ",""),'%')
			</if>
			<!-- 生产编号 -->
			<if test="notification != null and notification.craftNo != '' and notification.craftNo != null ">
				AND REPLACE(sn.craftNo," ","") LIKE CONCAT ('%',#{notification.craftNo},'%')
			</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} or a.doApplicant = #{createdBy.recordId}
				or b.recordId IN  (
				SELECT	noti.processCardAId FROM sl_notification noti JOIN sl_contract_detail contdeatil
				ON noti.contractDetailId = contdeatil.recordId JOIN sl_contract cont ON cont.recordId = contdeatil.contractId
				WHERE cont.userId =  #{createdBy.recordId}  
				  )
		   		or c.customerId in (SELECT cs.customerId from sm_customer_salesAssistant cs WHERE cs.activeFlag = #{DEL_FLAG_NORMAL} AND cs.userId = #{createdBy.recordId})
				 )
			</if>
			<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
				AND a.createdDate >= #{sentTimeStartQr}
			</if>
			<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
				AND a.createdDate <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
		</where>
	</select>
	
	<update id="updatedeliveryUpdateData">
		UPDATE pd_replenish SET
			deliveryUpdateUser = #{deliveryUpdateUser.recordId},
			deliveryUpdateDate = NOW()
		WHERE recordId = #{recordId}
	</update>
	
</mapper>