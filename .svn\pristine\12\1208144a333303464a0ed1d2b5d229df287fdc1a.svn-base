const supplierProduct = {
    template: '#supplierProduct',
    created:function(){
        this.scroll();
        this.getProductList();
    },
    destroyed () {
        window.removeEventListener('scroll',this.scrollEvent,false);
    },
    beforeRouteLeave(to, from, next){
        window.removeEventListener('scroll',this.scrollEvent,false);
        next();
    },
    activated(){
        this.scroll();
    },
    data(){
        return {
            productList: [],
            serchMsg: "",
            pageNo: 0,
            pageSize: 7,
            isLoading: false,
            isMore: true
        }
    },
    methods:{
        queryList(){
            this.pageNo = 0;
            this.isLoading = false;
            this.getProductList();
        },
        scroll(){
            window.addEventListener('scroll',this.scrollEvent);
        },
        scrollEvent(){
            const top = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop;
            const bottomOfWindow = document.body.scrollHeight - top - window.innerHeight <= 100;
            if (bottomOfWindow && this.isMore && !this.isLoading){
                this.isLoading = true;
                this.pageNo = this.pageNo + 1;
                this.getProductList();
            }
        },
        getProductList: function () {
            if (this.pageNo == 0) {
                this.productList = [];
            }
            const product = {};
            product.type = 2;
            product.serchMsg = this.serchMsg;
            product.finalFlag = 1;
            product.pageNo = this.pageNo * this.pageSize;
            product.pageSize = this.pageSize;
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/getProductList",
                data: JSON.stringify(product),
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    if (_this.pageNo > 0) {
                        for (let i=0;i<data.length;i++){
                            _this.productList.push(data[i]);
                        }
                    }else {
                        _this.productList = data;
                    }
                    _this.isLoading = false;
                }
            })
        },
        addProduct: function (id) {
            window.localStorage.setItem('productId', id);
            this.$router.push("supplierProduct_deail");
        },
    }
}