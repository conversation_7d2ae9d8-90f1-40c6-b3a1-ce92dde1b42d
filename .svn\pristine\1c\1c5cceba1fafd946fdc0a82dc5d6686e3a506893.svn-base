kybApp.controller('biddingCtrl', ['$rootScope', '$scope', '$timeout', 'upida','Upload', 'CommonUtil', 'BaseUtil','$stateParams','$filter',
    function ($rootScope, $scope, $timeout, upida,Upload, CommonUtil, BaseUtil,$stateParams,$filter) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();
        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
    var vm = this;
    // tabs控制
    vm.tabs = {
        listForm: {active: true},

        deailForm: {active: false,show: false}
    };

    vm.showDeailForm = function() {
        vm.tabs.deailForm.show = true;
        vm.tabs.deailForm.active = true;
    };

    vm.hideDeailForm = function() {
        vm.tabs.deailForm.show = false;
        $timeout(function() {
            vm.tabs.listForm.active = true;
        });
        vm.clearBidding();
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.url);
    };

    vm.message = "";
    vm.clickFlag = true;

    // 分页数据
    vm.page = {};

    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];

    // 原料申请分页数据
    vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "purch/bidding/loadBiddingList";

    vm.time = {
        start: {},
        end: {}
    };
    vm.sentTimeStartQr = "";
    vm.sentTimeEndQr = "";

    vm.initDate=function(date)
    {
        if(date == ""){
            vm.rangeOptions = {
                //format: "YYYY-MM-DD",
                startDate: new Date(vm.sentTimeStartQr),
                minDate:new Date(vm.sentTimeEndQr)
            };
            vm.time= {
                start: vm.rangeOptions.startDate,
                end: vm.rangeOptions.minDate
            }
        }
    };

    vm.pageSizeChange = function(){
        vm.init(1, vm.page.pageSize, vm.page.url);
    };

    vm.doPage = function(page, pageSize, total){
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        vm.init(page, pageSize, vm.page.url);
    };

    vm.noStr = "";
    vm.titleStr = "";
    vm.statusStr = "";
    vm.init = function(no, size, url) {
        MainCtrl.blockUI({
            animate: true,
        });
        // 请求数据
        const reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;

        reqData.sentTimeStartQr = vm.sentTimeStartQr;
        reqData.sentTimeEndQr = vm.sentTimeEndQr;
        reqData.no = vm.noStr;
        reqData.title = vm.titleStr;
        reqData.status = vm.statusStr;

        // 请求分页数据
        upida.post(url, reqData).then(function(result) {
            let data = {};
            // 如果结果为空
            if(typeof result === 'undefined' || typeof result.list === 'undefined') {
                data.pageNo = 1;
                data.pageSize = 10;
                data.list = [];
                data.startCount = 0;
                data.endCount = 0;
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }
            vm.page.data = data;
            MainCtrl.unblockUI();
        });
    };

    vm.doQuery = function() {
        if(vm.time.start) {
            vm.sentTimeStartQr = vm.time.start.valueOf();
        }
        if(vm.time.end) {
            vm.sentTimeEndQr = vm.time.end.valueOf();
        }
        vm.page.pageNo = 1;
        // 查询数据
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.url);
    };

    vm.addBidding = function() {
        vm.clearBidding();
        vm.showDeailForm();
        vm.loadDeailList();
    };

    vm.clearBidding = function (){
        vm.purchRawList = [];
        vm.purchRawId = "";
        vm.bidding.title = "";
        vm.startTime = "";
        vm.endTime = "";
        vm.deliveryDate = "";
        vm.bidding.startTime = "";
        vm.bidding.endTime = "";
        vm.bidding.deliveryDate = "";
        vm.bidding.content = "";
        vm.bidding.address = "";
        vm.purchRaw = {};
    };

    vm.biddingEntity = {};
    vm.editBidding = function (row){
        vm.clearBidding();
        vm.biddingEntity = row;
        vm.bidding.recordId = row.recordId;
        vm.purchRawId = row.purchRawId;
        vm.bidding.title = row.title;
        vm.startTime = row.startTime;
        vm.endTime = row.endTime;
        vm.deliveryDate = row.deliveryDate;
        vm.bidding.startTime = row.startTime;
        vm.bidding.endTime = row.endTime;
        vm.bidding.deliveryDate = row.deliveryDate;
        vm.bidding.content = row.content;
        vm.bidding.address = row.address;
        vm.bidding.status = row.status;
        vm.bidding.supplierList = row.supplierList;
        vm.bidding.customerList = row.customerList;
        vm.supplierBiddeAttachementsList = row.supplierBiddeAttacheList;
        vm.loadDeailList();
        vm.showDeailForm();
    };

    vm.reAddSupplier = function (){
        vm.biddingEntity.supplierList = [];
        vm.loadDeailList();
    };

    vm.editSupplierModel = function (){
        $('#editSupplier').modal();
    };

    vm.bidding = {};

    vm.purchRaw = {};
    vm.loadPurchRaw = function (){
        if (!vm.purchRawId){
            return;
        }
        // 将vm.purchRawId转换为数字数组
        const ids = vm.purchRawId.split(',');
        for (let i = 0; i < vm.purchRawList.length; i++){
            if (ids.includes(vm.purchRawList[i].recordId)){
                vm.purchRaw = vm.purchRawList[i];
                if(vm.purchRaw.endPurchDateTwoStr)
                {
                    vm.deliveryDate = vm.purchRaw.endPurchDateTwoStr;
                }
                return;
            }
        }
    };

    vm.startTime = "";
    vm.endTime = "";
    vm.deliveryDate = "";

    vm.saveBidding = function (type){
        if (type && type == 1)
        {
            vm.bidding.type = type;
            if (!(vm.supplierList && vm.supplierList.length > 0)){
                vm.message = "没有可招标的供应商！";
                $("#static").modal();
                return;
            }
            vm.bidding.supplierList = vm.supplierList;
        }
        if (type && type == 2)
        {
            vm.bidding.type = type;
            if (!(vm.customerList && vm.customerList.length > 0)){
                vm.message = "没有可招标的客户！";
                $("#static").modal();
                return;
            }
            vm.bidding.customerList = vm.customerList;
        }
        if (!vm.purchRawId){
            vm.message = "请选择申购单！";
            $("#static").modal();
            return;
        }
        if (!vm.bidding.title){
            vm.message = "请填写招标标题！";
            $("#static").modal();
            return;
        }
        vm.bidding.startTime = vm.startTime;
        vm.bidding.endTime = vm.endTime;
        vm.bidding.deliveryDate = vm.deliveryDate;
        let nowTime = new Date();
        if (!vm.bidding.startTime){
            vm.message = "请录入开始时间！";
            $("#static").modal();
            return;
        }else if (vm.bidding.startTime < nowTime){
            vm.message = "开始时间不能小于当前时间！";
            $("#static").modal();
            return;
        }
        if (!vm.bidding.endTime){
            vm.message = "请录入截止时间！";
            $("#static").modal();
            return;
        }else if(vm.bidding.endTime<vm.bidding.startTime){
            vm.message = "截止时间不能小于开始时间！";
            $("#static").modal();
            return;
        }else if(vm.bidding.endTime< nowTime){
            vm.message = "截止时间不能小于当前时间！";
            $("#static").modal();
            return;
        }
        if (!vm.bidding.deliveryDate){
            vm.message = "请录入到货时间！";
            $("#static").modal();
            return;
        }
        if (!vm.bidding.content){
            vm.message = "请填写招标内容！";
            $("#static").modal();
            return;
        }
        if (!vm.bidding.address){
            vm.message = "请填写招标地点！";
            $("#static").modal();
            return;
        }
        if(!vm.clickFlag)
        {
            vm.message = "请勿多次点击！";
            $("#static").modal();
            return;
        }
        vm.clickFlag = false;
        vm.bidding.purchRawId = vm.purchRawId;
        const materialList = [];
        const recordIds = vm.purchRawId.split(',');
        if (null != recordIds && recordIds.length > 1)
        {
            for (let i = 0; i < recordIds.length; i++)
            {
                const material = {};
                material.materialId = recordIds[i];
                materialList.push(material);
            }
        }
        else
        {
            if (vm.purchRaw && vm.purchRaw.recordId){
                const material = {};
                material.materialId = vm.purchRaw.recordId;
                materialList.push(material);
            }
        }
        vm.bidding.materialList = materialList;
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("purch/bidding/saveBidding", vm.bidding).then(function(data) {
            MainCtrl.unblockUI();
            vm.clickFlag = true;
            if (data == "success"){
                vm.message = "保存招标成功！";
                $("#static").modal();
                vm.hideDeailForm();
            }
        });
    };

    vm.delBidding = function (row){
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("purch/bidding/delete", row).then(function(data) {
            MainCtrl.unblockUI();
            if (data == "success"){
                vm.message = "删除招标成功！";
                $("#static").modal();
                vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.url);
            }
        });
    };

    vm.editStatus = function (row,status){
        if (null != row.type && row.type == 1)
        {
            if (status == 2){
                if (!(row.supplierList && row.supplierList.length > 0)){
                    vm.message = "没有匹配的供应商，发布不了！";
                    $("#static").modal();
                    return;
                }
            }
        }
        if (null != row.type && row.type == 2)
        {
            if (status == 2){
                if (!(row.customerList && row.customerList.length > 0)){
                    vm.message = "没有匹配的客户，发布不了！";
                    $("#static").modal();
                    return;
                }
            }
        }
        row.status = status;
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("purch/bidding/editStatus", row).then(function(data) {
            MainCtrl.unblockUI();
            if (data == "success"){
                if (status == '2'){
                    vm.message = "确认招标成功！";
                }else if (status == '6'){
                    vm.message = "作废招标成功！";
                }
                $("#static").modal();
                vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.url);
            }
            else
            {
                messageBidding(data);
                vm.message = "确认招标成功";
                $("#static").modal();
                vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.url);
            }
        });
    };

    // 发消息
    function messageBidding(comIds)
    {
        const obj = {};
        obj.comIds = comIds;
        upida.post("purch/bidding/messageBidding", obj).then(function(data) {
        });
    }


    vm.winningEntity = {};
    vm.winningList = [];
    vm.winningModal = function (row){
        vm.winningEntity = row;
        vm.getWinningList();
        $("#winningModal").modal();
    };

    vm.getWinningList = function () {
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("purch/bidding/getWinningList", vm.winningEntity).then(function(data) {
            vm.winningList = data;
            if (!vm.displayControl) {
                for (let i = 0; i < vm.winningList.length; i++) {
                    vm.winningList[i].priceShow = vm.winningList[i].price;
                    if (new Date() < new Date(vm.winningList[i].endTime)) {
                        if (vm.winningList[i].price && vm.winningList[i].price != null) {
                            vm.winningList[i].priceShow = "已报价";
                        } else {
                            vm.winningList[i].priceShow = "未报价";
                        }
                    }
                }
            }

            MainCtrl.unblockUI();
        });
    };

    vm.icloudBidding = {};
    vm.winningOpen = function(row)
    {
        vm.icloudBidding = row;
        vm.icloudBidding.materialId = vm.icloudBidding.materialIdTwo;
        getMaterialList();
    }

    vm.materialList = [];
    function getMaterialList()
    {
        let obj = {};
        obj.recordId = vm.winningEntity.purchRawId;
        obj.actualThickness = vm.winningEntity.actualThickness;
        obj.materialId = vm.icloudBidding.materialId;
        // obj.manufacturer = vm.icloudBidding.manufacturer;
        MainCtrl.blockUI({
            animate: true,
        });
        if (vm.winningEntity.type && null != vm.winningEntity.type && vm.winningEntity.type == "2")
        {
            $("#icloudBiddingModal").modal();
            MainCtrl.unblockUI();
        }
        else
        {
            upida.post("purch/bidding/getMaterialList", obj).then(function(data) {
                vm.materialList = data;
                if(!vm.materialList || vm.materialList.length == 0)
                {
                    vm.message = "没有匹配搭配具体料，请到物料管理添加对应规格的材料";
                    $("#static").modal();
                    return;
                }
                $("#icloudBiddingModal").modal();
                MainCtrl.unblockUI();
            });
        }
    }

    vm.winning = function (){
        vm.icloudBidding;
        if(!vm.icloudBidding.remark)
        {
            vm.message = "请填写中标原因！";
            $("#static").modal();
            return;
        }
        if(!vm.icloudBidding.materialId)
        {
            vm.message = "请选择具体料！";
            $("#static").modal();
            return;
        }
        if (vm.winningEntity.type && null != vm.winningEntity.type && vm.winningEntity.type == "2")
        {
            vm.saveWinning();
            return;
        }
        else
        {
            vm.message = "您确定要更换具体料?";
            $("#confirmMaterialStatic").modal();
            return;
        }

    };

    vm.saveWinning = function()
    {
        let row = vm.icloudBidding
        if (vm.winningEntity.type && null != vm.winningEntity.type)
        {
            row.subjectType = vm.winningEntity.type
        }
        if(!vm.clickFlag)
        {
            vm.message = "请勿多次点击！";
            $("#static").modal();
            return;
        }
        vm.clickFlag = false;
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("purch/bidding/winning", row).then(function(data) {
            vm.message = data;
            $("#static").modal();
            $("#winningModal").modal('hide');
            vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.url);
            MainCtrl.unblockUI();
            vm.clickFlag = true;
        });
    }

    vm.purchRawId = "";
    vm.purchRawList = [];
    vm.supplierList = [];
    vm.customerList = [];
    vm.subjectType = "";
    vm.loadDeailList = function (){
        vm.purchRawList = [];
        vm.supplierList = [];
        vm.customerList = [];
        vm.subjectType = "";
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("purch/bidding/loadDeailList", vm.purchRawId).then(function(data) {
            vm.purchRawList = data.purchRawList;
            vm.subjectType = data.subjectType;
            if(data.supplierList && data.supplierList.length > 0)
            {
                for (let i=0;i<data.supplierList.length;i++){
                    data.supplierList[i].type = 1;
                    if (data.supplierList[i] && data.supplierList[i].recordId){
                        vm.supplierList.push(data.supplierList[i]);
                    }
                }
                vm.getIcloudCompanyOpen(1,1);
            }
            if(data.customerList && data.customerList.length > 0)
            {
                for (let i=0;i<data.customerList.length;i++){
                    data.customerList[i].type = 1;
                    if (data.customerList[i] && data.customerList[i].recordId){
                        vm.customerList.push(data.customerList[i]);
                    }
                }
                vm.getIcloudCompanyOpen(1,2);
            }
            vm.loadPurchRaw();
            MainCtrl.unblockUI();
        });
    };

    function loadData() {
        if(vm.time.start) {
            vm.sentTimeStartQr = vm.time.start.valueOf();
        }
        if(vm.time.end) {
            vm.sentTimeEndQr = vm.time.end.valueOf();
        }
        vm.page.pageNo = 1;
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.url);
    };

    vm.right = {};
    function loadRight() {

        upida.get("common/rightall?prefix=purch:supplierbidding").then(function(data) {
            vm.right.view = data.view;
            vm.right.edit = data.edit;
            vm.right.manage = data.manage;
            $timeout(function() {
                loadData();
            },500);
        });
        if ($stateParams.purchRawId) {
            showPrucgRaw($stateParams.purchRawId);
        }


    };

    function showPrucgRaw(purchRawId)
    {
        vm.clearBidding();
        vm.showDeailForm();
        vm.purchRawId = purchRawId;
        vm.loadDeailList();
    }

    vm.icloudCompanyList = [];
    vm.getIcloudCompanyOpen = function(num,type)
    {
        // 获取云平台公司
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("purch/bidding/getIcloudCompany", type).then(function(data) {
            if(data && data.length > 0)
            {
                vm.icloudCompanyList = [];
                if (type == 1)
                {
                    for(let obj of data)
                    {
                        const supplier = {};
                        supplier.recordId = obj.recordId;
                        supplier.no = obj.no;
                        supplier.shortName =  obj.name || obj.shortName  || '无';
                        supplier.bizPerson = obj.linkMan || obj.legalPhone || '无';
                        supplier.phone = obj.linkPhone || '无';
                        supplier.supplierLevel =  obj.supplierLevel;
                        supplier.industryName =  obj.industryName;
                        supplier.supplierType =  obj.supplierType;
                        supplier.type = 2;
                        supplier.code = obj.code || '无';
                        supplier.checked = false;
                        for(let sup of vm.supplierList)
                        {
                            if(sup.type == 2 && sup.recordId == supplier.recordId)
                            {
                                supplier.checked = true;
                                break;
                            }
                        }
                        vm.icloudCompanyList.push(supplier);
                    }
                }
                else if (type == 2)
                {
                    for(let obj of data)
                    {
                        const customer = {};
                        customer.recordId = obj.recordId;
                        customer.no = obj.no;
                        customer.shortName =  obj.name || obj.shortName  || '无';
                        customer.type = 2;
                        customer.checked = false;
                        for(let cus of vm.customerList)
                        {
                            if(cus.recordId == customer.recordId)
                            {
                                customer.checked = true;
                                break;
                            }
                        }
                        vm.icloudCompanyList.push(customer);
                    }
                }

                if(!num && type == 1)
                {
                    $("#icloudCompanyModal").modal();
                }
                else if (!num && type == 2)
                {
                    $("#icloudCompanyModalTwo").modal();
                }
                else
                {
                    if(vm.bidding.supplierList && vm.bidding.supplierList.length > 0)
                    {
                        for(let sup of vm.bidding.supplierList)
                        {
                            if(sup.type == 2)
                            {
                                for(let icloud of vm.icloudCompanyList)
                                {
                                    if(icloud.recordId == sup.supplierId)
                                    {
                                        vm.supplierList.push(icloud);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                vm.message = "没找到云公司！";
                $("#static").modal();
            }
            MainCtrl.unblockUI();
        });
    };

    vm.changeCheked = function(item)
    {
        for (let i = 0; i < vm.supplierList.length; i++)
        {
            if (vm.supplierList[i].icloudId == item.recordId)
            {
                vm.message = "该供应商已参与！";
                $("#static").modal();
                return;
            }
        }
        item.checked = !item.checked;
    }

    vm.addSupplier = function()
    {
        let flag = false;
        for(let icloud of vm.icloudCompanyList)
        {
            if(icloud.checked)
            {
                flag = true;
                break;
            }
        }
        if(!flag)
        {
            vm.message = "请选择云公司！";
            $("#static").modal();
            return;
        }
        // 清楚原来选择的云公司
        for(let i=0;i<vm.supplierList.length;i++)
        {
            const supplier = vm.supplierList[i];
            if(supplier.type == 2)
            {
                vm.supplierList.splice(i,1);
                i--;
            }
        }
        // 添加现在选择的云公司
        for(let icloud of vm.icloudCompanyList)
        {
            if(icloud.checked)
            {
                vm.supplierList.push(icloud);
            }
        }
        $("#icloudCompanyModal").modal('hide');
    }

        vm.addSupplierTwo = function()
        {
            let flag = false;
            for(let icloud of vm.icloudCompanyList)
            {
                if(icloud.checked)
                {
                    flag = true;
                    break;
                }
            }
            if(!flag)
            {
                vm.message = "请选择云公司！";
                $("#static").modal();
                return;
            }
            // 清楚原来选择的云公司
            for(let i=0;i<vm.customerList.length;i++)
            {
                vm.customerList.splice(i,1);
                i--;
            }
            // 添加现在选择的云公司
            for(let icloud of vm.icloudCompanyList)
            {
                if(icloud.checked)
                {
                    vm.customerList.push(icloud);
                }
            }
            $("#icloudCompanyModalTwo").modal('hide');
        }

        vm.allPageChecked = false;
        vm.selectAllPage = function ()
        {
            if (!vm.page.data.list)
            {
                return;
            }
            angular.forEach(vm.page.data.list, function (row){
                row.checked = !vm.allPageChecked;
            });

            vm.getRecordIds();
        };

        vm.getRecordIds = function ()
        {
            vm.recordIds = null;
            vm.recordIdList = [];
            angular.forEach(vm.page.data.list, function (row){
                if(row.checked && row.status == "1")
                {
                    if (vm.recordIds)
                    {
                        vm.recordIds = vm.recordIds + "," + row.recordId;
                    }
                    else
                    {
                        vm.recordIds = row.recordId;
                    }
                    vm.recordIdList.push(angular.copy(row));
                }
            })
        };

        vm.batchEditStatus = function ()
        {
            if (vm.recordIdList && vm.recordIdList.length > 0)
            {
                for (var i = 0; i < vm.recordIdList.length; i++)
                {
                    if (!(vm.recordIdList[i].supplierList && vm.recordIdList[i].supplierList.length > 0))
                    {
                        vm.message = "当前选择的数据中存在没有匹配的供应商，不能发布!";
                        $("#static").modal();
                        return;
                    }
                }
            }
            else
            {
                vm.message = "请选择要发布的招标信息!";
                $("#static").modal();
                return;
            }
            vm.message = "您确定要批量发布招标单吗?";
            $("#batchEditStatusOpen").modal();
        };

        vm.clicks = true;
        vm.commitAudit = function ()
        {
            if(!vm.clicks)
            {
                vm.message = "请勿多次点击!";
                $('#static').modal();
                return;
            }
            vm.clicks = false;
            MainCtrl.blockUI({
                animate: true,
            });
            let approvalList = [];
            for (let i = 0; i < vm.recordIdList.length; i++)
            {
                if (vm.recordIdList[i].status != '1')
                {
                    continue;
                }
                vm.recordIdList[i].status = 2;
                approvalList.push(vm.recordIdList[i]);
            }
            upida.post("purch/bidding/batchEditStatus",approvalList).then(function(data) {
                if (data){
                    vm.message = "批量发布成功!";
                    messageBidding(data);
                }else{
                    vm.message = "数据异常,请刷新重试!";
                }
                $('#static').modal();
                MainCtrl.unblockUI();
                vm.clickFlag = true;
                $("#batchEditStatusOpen").modal('hide');
                vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.url);
            });
        }

        vm.onTimeSet = function (newDate, oldDate) {
            vm.endTime = newDate;
            vm.bidding.deliveryDate = null;
            vm.bidding.endTime = vm.endTime;
            if(vm.bidding && vm.bidding.recordId)
            {
                MainCtrl.blockUI({
                    animate: true,
                });
                upida.post("purch/bidding/updateEndTime", vm.bidding).then(function (data) {
                    if(data == "success")
                    {
                        vm.message = "修改结束时间成功!";
                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.url)
                    }
                    else
                    {
                        vm.message = "修改结束时间失败!";
                    }
                    $('#static').modal();
                    MainCtrl.unblockUI();
                });
            }
        };
        vm.onTimeSetTwo = function (newDate, oldDate) {
            vm.deliveryDate = newDate;
            vm.bidding.endTime = null;
            vm.bidding.deliveryDate = vm.deliveryDate;
            if(vm.bidding && vm.bidding.recordId)
            {
                MainCtrl.blockUI({
                    animate: true,
                });
                upida.post("purch/bidding/updateEndTime", vm.bidding).then(function (data) {
                    if(data == "success")
                    {
                        vm.message = "修改送货时间成功!";
                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.url)
                    }
                    else
                    {
                        vm.message = "修改送货时间失败!";
                    }
                    $('#static').modal();
                    MainCtrl.unblockUI();
                });
            }
        };

        vm.selectAll = function (){
            if(!vm.icloudCompanyList){
                return;
            }
            angular.forEach(vm.icloudCompanyList,function(row){
                row.checked = 1;
            });
        }

        vm.cancelSelectAll = function (){
            if(!vm.icloudCompanyList){
                return;
            }
            angular.forEach(vm.icloudCompanyList,function(row){
                row.checked = "";
            });
        }

        vm.addInvoiceForAccounts = function ()
        {
            $('#uploadInvoiceDiv').modal();
        };

        vm.edit = {};
        // 初始化文件
        vm.uFiles = []; // 上传文件列表
        vm.uErrFiles = []; // 错误文件列表
        vm.uFilescon = {}; // 添加合同时用于保存文件选项，以key-value的形式保存，key为明细在数据中的索引，value为文件列表
        vm.edit.upload = false;
        vm.filesizeflag = 0;

        vm.uploadFiles = function(files, errFiles) {
            // 获取上传前缀
            angular.forEach(files, function(p){
                vm.uFiles.push(p);
            });
            vm.uErrFiles = errFiles;
            if(vm.uFiles!=null && vm.uFiles.length>0){
                angular.forEach(vm.uFiles, function(file) {
                    if (typeof file.isNew === "undefined") {
                        //上传发票操作
                        vm.edit.upload = true;
                        file.isNew = false;
                        file.upload = Upload.upload({
                            url: 'a/purch/bidding/uploadInvoice',
                            fields: {'recordIds': vm.bidding.recordId},
                            file: file
                        });
                        // 设置默认的上传状态
                        file.status = 2;
                        file.upload.then(function (response) {
                            vm.filesizeflag += 1;
                            if(vm.filesizeflag == vm.uFiles.length){
                                vm.edit.upload = false;
                            }
                            $timeout(function () {
                                file.status = response.status;
                                file.result = response.data;
                            });
                        }, function (response) {
                            vm.filesizeflag += 1;
                            file.status = response.status;
                            if (response.status > 0) {
                                vm.errorMsg = response.status + ': ' + response.data;
                            }
                        }, function (evt) {
                            file.progress = Math.min(100, parseInt(100.0 * evt.loaded / evt.total));
                        });
                    }
                });
            }
        };
        vm.confirmUplodFile = function ()
        {
            vm.getSupplierBiddeAttachementsList();
        };

        vm.supplierBiddeAttachementsList = [];
        vm.getSupplierBiddeAttachementsList = function ()
        {
            MainCtrl.blockUI({
                animate: true,
            });
            // 重置上传文件数据
            vm.uFiles = [];
            vm.uErrFiles = [];
            vm.filesizeflag = 0;
            upida.post("purch/bidding/getSupplierBiddeAttachementsList/" + vm.bidding.recordId).then(function (data) {
                vm.supplierBiddeAttachementsList = data;
                MainCtrl.unblockUI();
            });
        };
        vm.imageSrc = "";
        vm.supplierBiddeAttachements = {};
        vm.getViewUrl = function (index, orgFileName)
        {
            if(orgFileName)
            {
                if(!vm.validateIsImage(orgFileName)){
                    vm.message = "亲,只支持图片在线浏览！";
                    $("#static").modal();
                    return;
                }
            }
            MainCtrl.blockUI({
                //进度条展示
                animate: true,
            });
            //获取查看的记录
            vm.supplierBiddeAttachements = angular.copy(vm.supplierBiddeAttachementsList[index]);
            upida.post("purch/bidding/getUrl", vm.supplierBiddeAttachements).then(function (data) {
                vm.imageSrc = data;
                // 显示图片
                $('#viewInvoiceImg').modal();
                //进度条关闭
                MainCtrl.unblockUI();
            });
        };


        // 删除文件或文件夹
        vm.showDelFile = function(index){
            //获取查看的记录
            vm.supplierBiddeAttachements = angular.copy(vm.supplierBiddeAttachementsList[index]);
            vm.delMsg = "您确定要删除原始凭据\" "+ vm.supplierBiddeAttachements.orgFileName + " \"吗?";
            $("#staticInvoiceRemove").modal();
        };

        // 做删除操作
        vm.doDelFile = function(){
            MainCtrl.blockUI({
                //进度条展示
                animate: true
            });
            upida.post("purch/bidding/delFile", vm.supplierBiddeAttachements).then(function(data){
                if (data){
                    vm.message = "原始凭据\" "+ vm.supplierBiddeAttachements.orgFileName + " \"删除成功！";
                    $("#static").modal();
                    vm.getSupplierBiddeAttachementsList();
                } else {
                    //进度条关闭
                    MainCtrl.unblockUI();
                    vm.message = "原始凭据\" "+ vm.supplierBiddeAttachements.orgFileName + " \"删除失败！";
                    $("#static").modal();
                }
            });
        };

        //下载
        vm.submitDown = function(downForm){
            downForm.commit();
        };

        vm.validateIsImage = function(fileName){
            var fileStart=fileName.lastIndexOf(".");
            var hz=fileName.substring(fileStart,fileName.length).toUpperCase();
            if(hz!=".BMP"&&hz!=".PNG"&&hz!=".GIF"&&hz!=".JPG"&&hz!=".JPEG"){
                return false;
            }
            return true;
        };

    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
    });
}]);
