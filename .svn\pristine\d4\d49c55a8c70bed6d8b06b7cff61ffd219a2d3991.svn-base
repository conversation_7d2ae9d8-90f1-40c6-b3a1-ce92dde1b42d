<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.icloud.erp.erp.dao.Icloud_MaterialDao">
    <select id="get" resultType="com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_Material">
		SELECT
			a.recordId AS "recordId",
			a.companyId AS "company.recordId",
			a.no AS "no",
			a.name AS "name",
			a.specification AS "specification",
			a.manufacturer AS "manufacturer",
			a.availableQty AS "availableQty",
			a.xboardQty AS "xboardQty",
			a.deliveryOaQty AS "deliveryOaQty",
			a.lowerLimit AS "lowerLimit",
			a.materialType AS "materialType.recordId",
			a.unit AS "unit.recordId",
			a.version AS "version",
			a.storehouseId AS "storehouse.recordId",
			a.stocks AS "stocks",
			a.status AS "status",
			a.applicant AS "applicant",
			a.majorFlag AS "majorFlag",
			a.materialKind AS "materialKind",
			a.systemFlag AS "systemFlag",
			a.activeFlag AS "activeFlag",
			a.createdby AS "createdBy.recordId",
			a.createddate AS "createdDate",
			a.lastupdby AS "lastUpdBy.recordId",
			a.lastupddate AS "lastUpdDate",
			a.remark AS "remark",
			a.customerno AS "customerNo",
			a.countKeyword as "countKeyword",
			IFNULL(a.depositQty,0) AS "depositQty",
			IFNULL(a.depositOaQty,0) AS "depositOaQty",
			a.deliverySize AS "deliverySize",
			a.customerId AS "customer.recordId",
			a.remain AS "remain",
			a.salesStoreHouseId AS "salesStoreHouse.recordId"
		FROM md_material a
		WHERE a.recordId = #{recordId}
	</select>

    <select id="getMaterialByCraftTwo" resultType="com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_Material">
        SELECT
        *
        FROM
        (
        SELECT
        a.recordId,
        a.initPrice,
        a.`no`,
        a.`name`,
        a.specification,
        a.actualThickness,
        a.remark,
        a.leadTime,
        manufacturer.`value` AS "manufacturer",
        CASE WHEN IFNULL(a.initStocks,0) > 0 THEN
        (
            IFNULL(
                a.rawmaterialPrice,
                (
                    SELECT
                    price
                    FROM pu_purchasing_detail aa
                    LEFT JOIN pu_purchasing bb ON bb.recordId = aa.purchasingId
                    WHERE
                    aa.materialId = a.recordId
                    AND aa.activeFlag = 1
                    AND bb.activeFlag = 1
                    AND aa.`status` NOT IN (500108)
                    AND aa.createdDate >= "2021-01-01"
                    ORDER BY
                    aa.createdDate DESC
                    LIMIT 1
                )
            )
        ) ELSE
        (
            CASE WHEN pricePeriod.parameterValue IS NOT NULL AND pricePeriod.parameterValue > 0 THEN
            (
            CASE WHEN a.updatePriceDate >= DATE_SUB(NOW(),INTERVAL pricePeriod.parameterValue MONTH) THEN a.rawmaterialPrice ELSE 0 END
            )ELSE a.rawmaterialPrice END
        ) END as "price",
        ee.`value` AS "bordLength",
        ff.`value` AS "bordWidth"
        FROM md_material a
        LEFT JOIN md_material_specification_relation aa ON aa.materialId = a.recordId
        AND aa.companyId = a.companyId
        AND aa.specificationId = #{specificationId1}
        AND aa.activeFlag = 1
        LEFT JOIN md_material_specification_relation b ON b.materialId = a.recordId
        AND b.companyId = a.companyId
        AND b.specificationId = #{specificationId2}
        AND b.activeFlag = 1
        LEFT JOIN md_material_specification_relation c ON c.materialId = a.recordId
        AND c.companyId = a.companyId
        AND c.specificationId = #{specificationId3}
        AND c.activeFlag = 1
        LEFT JOIN md_dict_value cc ON cc.recordId = c.value
        LEFT JOIN md_material_specification_relation d ON d.materialId = a.recordId
        AND d.companyId = a.companyId
        AND d.specificationId = #{specificationId4}
        AND d.activeFlag = 1
        LEFT JOIN md_material_specification_relation e ON e.materialId = a.recordId
        AND e.companyId = a.companyId
        AND e.specificationId = #{specificationId5}
        AND e.activeFlag = 1
        LEFT JOIN md_material_specification_relation f ON f.materialId = a.recordId
        AND f.companyId = a.companyId
        AND f.specificationId = #{specificationId6}
        AND f.activeFlag = 1
        LEFT JOIN md_material_specification_relation ee ON ee.materialId = a.recordId
        AND ee.companyId = a.companyId
        AND ee.specificationId = #{specificationId8}
        AND ee.activeFlag = 1
<!--        <if test="specificationId8Value != null and specificationId8Value != ''">-->
<!--            AND ee.value = #{specificationId8Value}-->
<!--        </if>-->
        LEFT JOIN md_material_specification_relation ff ON ff.materialId = a.recordId
        AND ff.companyId = a.companyId
        AND ff.specificationId = #{specificationId9}
        AND ff.activeFlag = 1
<!--        <if test="specificationId9Value != null and specificationId9Value != ''">-->
<!--            AND ff.value = #{specificationId9Value}-->
<!--        </if>-->
        LEFT JOIN md_material_specification_relation gg ON gg.materialId = a.recordId
        AND gg.companyId = a.companyId
        AND gg.specificationId = #{specificationId7}
        AND gg.activeFlag = 1
        LEFT JOIN md_dict_value manufacturer ON manufacturer.recordId = gg.`value`
        LEFT JOIN md_parameterset pricePeriod ON pricePeriod.companyId = a.companyId AND pricePeriod.jianPin = 'materialPriceValidityPeriod'
        WHERE a.companyId = #{companyId}
        AND a.activeFlag = 1
        AND a.materialKind = 100701
        AND a.status = 1
        <!-- 物料id -->
        <if test="recordId != null and recordId != ''">
            AND a.recordId = #{recordId}
        </if>
        <!-- 物料编号 -->
        <if test="no != null and no != ''">
            AND REPLACE(a.no," ","") LIKE CONCAT('%', REPLACE(#{no}," ",""), '%')
        </if>
        <!-- 物料名称 -->
        <if test="name != null and name != ''">
            AND REPLACE(a.`name`," ","") LIKE CONCAT('%', REPLACE(#{name}," ",""), '%')
        </if>
        <if test="manufacturer != null and manufacturer != ''">
            AND manufacturer.value = #{manufacturer}
        </if>
        <if test="condition1 != null and condition1 != ''">
            AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition1}," ",""), '%')
        </if>
        <if test="condition2 != null and condition2 != ''">
            AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition2}," ",""), '%')
        </if>
        <if test="condition3 != null and condition3 != ''">
            AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition3}," ",""), '%')
        </if>
        <if test="condition4 != null and condition4 != ''">
            AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition4}," ",""), '%')
        </if>
        <if test="condition5 != null and condition5 != ''">
            AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition5}," ",""), '%')
        </if>
        <if test="condition6 != null and condition6 != ''">
            AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition6}," ",""), '%')
        </if>
        <if test="boardLevelId != null and boardLevelId != ''">
            AND aa.`value` = (
            SELECT
            recordId
            FROM
            md_dict_value
            WHERE
            companyId = a.companyId
            AND `value` = (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = #{boardLevelId}
            )
            AND activeFlag = 1
            LIMIT 1
            )
        </if>
        <if test="materialTypeId != null and materialTypeId != ''">
            AND b.`value` = (
            SELECT
            recordId
            FROM
            md_dict_value
            WHERE
            companyId = a.companyId
            AND `value` = (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = #{materialTypeId}
            )
            AND activeFlag = 1
            LIMIT 1
            )
        </if>
        <if test="copperCladThicknessId != null and copperCladThicknessId != ''">
            AND FIND_IN_SET(
                (
                    SELECT
                    `value`
                    FROM
                    md_dict_value
                    WHERE
                    recordId = d.`value`
                ),
                (
                    SELECT
                        GROUP_CONCAT(`value`,",",IFNULL(remark, ""))
                    FROM
                    md_dict_value
                    WHERE
                    recordId = #{copperCladThicknessId}
                )
            )
        </if>
        <if test="daoreId != null and daoreId != ''">
            AND (e.`value` IS NULL OR FIND_IN_SET(
            (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = e.`value`
            ),
            (
            SELECT
            CONCAT(
            `value`,
            ",",
            IFNULL(remark, "")
            )
            FROM
            md_dict_value
            WHERE
            recordId = #{daoreId}
            )
            )
            )
        </if>
        <if test="naiyaId != null and naiyaId != ''">
            AND (f.`value` IS NULL OR f.`value` = (
            SELECT
            recordId
            FROM
            md_dict_value
            WHERE
            companyId = a.companyId
            AND `value` = (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = #{naiyaId}
            )
            AND activeFlag = 1
            LIMIT 1
            ))
        </if>
        <if test="boardThicknessId != null and boardThicknessId != ''">
            AND (REPLACE ( REPLACE ( REPLACE (cc.`value`, 'm', '' ), 'M', '' ), ' ', '' )  <![CDATA[=]]>
            (
                SELECT
                CAST(REPLACE(REPLACE (REPLACE ( `value`, 'm', '' ), 'M', '' ), ' ', '' ) AS DECIMAL(2,1))
                FROM md_dict_value
                WHERE recordId = #{boardThicknessId}
            )
            <if test="boardThicknessIdTwo != null and boardThicknessIdTwo != ''">
                OR c.`value` =
                (
                SELECT
                recordId
                FROM md_dict_value
                WHERE companyId = a.companyId AND itemId = 3
                AND `value` = (SELECT `value` FROM md_dict_value WHERE recordId = #{boardThicknessIdTwo})
                LIMIT 1
                )
            </if>
            )
        </if>
        <if test="useAgeData != null and useAgeData != ''">
            AND REPLACE(a.`name`," ","") LIKE '%客供%'
        </if>
        <if test="bordLength != null and bordLength != ''">
            AND (ee.`value` = #{bordLength} OR ff.`value` = #{bordLength})
        </if>
        <if test="bordWidth != null and bordWidth != ''">
            AND (ff.`value` = #{bordWidth} OR ee.`value` = #{bordWidth})
        </if>
        <if test="materialNo != null and materialNo != ''">
            AND REPLACE(a.no," ","") LIKE CONCAT('%', REPLACE(#{materialNo}," ",""), '%')
        </if>
        ) material
        WHERE 1=1
        <if test="useAgeData == null or useAgeData == ''">
            AND material.price > 0
            AND material.manufacturer IS NOT NULL
            AND material.bordLength IS NOT NULL
            AND material.bordWidth IS NOT NULL
            AND material.manufacturer <![CDATA[<>]]> "客供料"
        </if>
        ORDER BY material.price ASC
    </select>

    <select id="getMaterialByCraftThree" resultType="com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_Material">
        SELECT
        a.`no`,
        a.`name`,
        a.specification,
        manufacturer.`value` AS "manufacturer",
        ee.`value` AS "bordLength",
        ff.`value` AS "bordWidth",
        s.quantity,
        boardLevel.`value` AS "boardLevel",
        materialType.`value` AS "materialType",
        boardThickness.`value` AS "boardThickness",
        copperCladThickness.`value` AS "copperCladThickness",
        daore.`value` AS "daore",
        naiya.`value` AS "naiya",
        s.versionId,
        CASE WHEN s.confirmTime IS NOT NULL AND s.leadTime IS NOT NULL THEN DATE_ADD(s.confirmTime,INTERVAL s.leadTime DAY)
        ELSE NULL END AS "arrivalDate",
        s.customerId,
        g.name AS "customerName",
        s.recordId AS "matPreparationId"
        FROM md_market_material_preparation s
        LEFT JOIN md_material a ON a.recordId = s.materialId
        LEFT JOIN md_material_specification_relation aa ON aa.materialId = a.recordId
        AND aa.companyId = a.companyId
        AND aa.specificationId = #{specificationId1}
        AND aa.activeFlag = 1
        LEFT JOIN md_material_specification_relation b ON b.materialId = a.recordId
        AND b.companyId = a.companyId
        AND b.specificationId = #{specificationId2}
        AND b.activeFlag = 1
        LEFT JOIN md_material_specification_relation c ON c.materialId = a.recordId
        AND c.companyId = a.companyId
        AND c.specificationId = #{specificationId3}
        AND c.activeFlag = 1
        LEFT JOIN md_dict_value cc ON cc.recordId = c.value
        LEFT JOIN md_material_specification_relation d ON d.materialId = a.recordId
        AND d.companyId = a.companyId
        AND d.specificationId = #{specificationId4}
        AND d.activeFlag = 1
        LEFT JOIN md_material_specification_relation e ON e.materialId = a.recordId
        AND e.companyId = a.companyId
        AND e.specificationId = #{specificationId5}
        AND e.activeFlag = 1
        LEFT JOIN md_material_specification_relation f ON f.materialId = a.recordId
        AND f.companyId = a.companyId
        AND f.specificationId = #{specificationId6}
        AND f.activeFlag = 1
        LEFT JOIN md_material_specification_relation ee ON ee.materialId = a.recordId
        AND ee.companyId = a.companyId
        AND ee.specificationId = #{specificationId8}
        AND ee.activeFlag = 1
        LEFT JOIN md_material_specification_relation ff ON ff.materialId = a.recordId
        AND ff.companyId = a.companyId
        AND ff.specificationId = #{specificationId9}
        AND ff.activeFlag = 1
        LEFT JOIN md_material_specification_relation gg ON gg.materialId = a.recordId
        AND gg.companyId = a.companyId
        AND gg.specificationId = #{specificationId7}
        AND gg.activeFlag = 1
        LEFT JOIN md_dict_value manufacturer ON manufacturer.recordId = gg.`value`
        LEFT JOIN md_dict_value boardLevel ON boardLevel.recordId = aa.`value`
        LEFT JOIN md_dict_value materialType ON materialType.recordId = b.`value`
        LEFT JOIN md_dict_value boardThickness ON boardThickness.recordId = c.`value`
        LEFT JOIN md_dict_value copperCladThickness ON copperCladThickness.recordId = d.`value`
        LEFT JOIN md_dict_value daore ON daore.recordId = e.`value`
        LEFT JOIN md_dict_value naiya ON naiya.recordId = f.`value`
        LEFT JOIN md_customer g ON g.recordId = s.customerId
        WHERE s.deptId = #{deptId}
        AND s.activeFlag = 1
        AND s.`status` = 1003
        AND a.companyId = #{companyId}
        AND a.activeFlag = 1
        AND a.materialKind = 100701
        AND a.status = 1
        AND s.quantity IS NOT NULL
        AND s.groupCenterId IS NULL
        <if test="boardLevelId != null and boardLevelId != ''">
            AND aa.`value` = (
            SELECT
            recordId
            FROM
            md_dict_value
            WHERE
            companyId = a.companyId
            AND `value` = (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = #{boardLevelId}
            )
            AND activeFlag = 1
            LIMIT 1
            )
        </if>
        <if test="materialTypeId != null and materialTypeId != ''">
            AND b.`value` = (
            SELECT
            recordId
            FROM
            md_dict_value
            WHERE
            companyId = a.companyId
            AND `value` = (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = #{materialTypeId}
            )
            AND activeFlag = 1
            LIMIT 1
            )
        </if>
        <if test="copperCladThicknessId != null and copperCladThicknessId != ''">
            AND FIND_IN_SET(
            (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = d.`value`
            ),
            (
            SELECT
            GROUP_CONCAT(`value`,",",IFNULL(remark, ""))
            FROM
            md_dict_value
            WHERE
            recordId = #{copperCladThicknessId}
            )
            )
        </if>
        <if test="daoreId != null and daoreId != ''">
            AND (e.`value` IS NULL OR FIND_IN_SET(
            (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = e.`value`
            ),
            (
            SELECT
            CONCAT(
            `value`,
            ",",
            IFNULL(remark, "")
            )
            FROM
            md_dict_value
            WHERE
            recordId = #{daoreId}
            )
            )
            )
        </if>
        <if test="naiyaId != null and naiyaId != ''">
            AND (f.`value` IS NULL OR f.`value` = (
            SELECT
            recordId
            FROM
            md_dict_value
            WHERE
            companyId = a.companyId
            AND `value` = (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = #{naiyaId}
            )
            AND activeFlag = 1
            LIMIT 1
            ))
        </if>
        <if test="boardThicknessId != null and boardThicknessId != ''">
            AND (REPLACE ( REPLACE ( REPLACE (cc.`value`, 'm', '' ), 'M', '' ), ' ', '' )  <![CDATA[=]]>
            (
            SELECT
            CAST(REPLACE(REPLACE (REPLACE ( `value`, 'm', '' ), 'M', '' ), ' ', '' ) AS DECIMAL(2,1))
            FROM md_dict_value
            WHERE recordId = #{boardThicknessId}
            )
            <if test="boardThicknessIdTwo != null and boardThicknessIdTwo != ''">
                OR c.`value` =
                (
                SELECT
                recordId
                FROM md_dict_value
                WHERE companyId = a.companyId AND itemId = 3
                AND `value` = (SELECT `value` FROM md_dict_value WHERE recordId = #{boardThicknessIdTwo})
                LIMIT 1
                )
            </if>
            )
        </if>
        <if test="bordLength != null and bordLength != ''">
            AND (ee.`value` = #{bordLength} OR ff.`value` = #{bordLength})
        </if>
        <if test="bordWidth != null and bordWidth != ''">
            AND (ff.`value` = #{bordWidth} OR ee.`value` = #{bordWidth})
        </if>
    </select>

    <select id="getMaterialSpecificationList" resultType="com.kyb.pcberp.modules.icloud.contract.pojo.Icloud_MaterialSpecification">
		SELECT
			recordId,
			dictItemId,
			`name`
		FROM
			md_material_specification
		WHERE
			companyId = #{companyId}
		AND activeFlag = 1
		AND materialType = 14784
	</select>

    <select id="getMaterialArea" resultType="com.kyb.pcberp.modules.icloud.material.pojo.Icloud_MaterialArea">
        SELECT
            a.useArea,
            b.specification,
            b.groupCenterId,
            a.matPreparationId
        FROM icloud_group_material_area_detail a
        LEFT JOIN icloud_group_material_area b ON b.recordId = a.materialAreaId
        LEFT JOIN icloud_group_center c ON c.recordId = b.groupCenterId
        WHERE b.deptId = #{deptId} AND a.activeFlag = 1 AND b.activeFlag = 1
        AND c.activeFlag = 1
        <if test="groupCenterId != null and groupCenterId != ''">
            AND b.groupCenterId <![CDATA[<>]]> #{groupCenterId}
        </if>
    </select>

    <select id="getPurchMaterList" resultType="com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_MaterialPruch">
		SELECT
			b.`no`,
			a.recordId,
			a.deliveryDate,
			(
				a.quantity - IFNULL(
					(
						SELECT
							SUM(za.stocks)
						FROM
							icloud_purch_concat za
						LEFT JOIN sl_contract_detail zb ON zb.groupCenterId = za.groupCenterId
						AND zb.activeFlag = 1 AND zb.companyId = 17
						LEFT JOIN sl_notification zc ON zc.contractDetailId = zb.recordId
						AND zc.activeFlag = 1
						LEFT JOIN sl_notification zd ON zd.recordId = zc.mergeId
						AND zd.activeFlag = 1
						LEFT JOIN pd_feeding ze ON ze.recordId = IFNULL(zd.feedingId, zc.feedingId)
						WHERE
							za.activeFlag = 1
						AND za.purchDeailId = a.recordId
						AND ze.acDistributeDate IS NULL
					),
					0
				) - IFNULL(
					(
						SELECT
							SUM(quantity)
						FROM
							st_material_store
						WHERE
							purchasingDtlId = a.recordId
						AND activeFlag = 1
					),
					0
				)
			) AS quantity,
			a.createdDate,
			TIMESTAMPDIFF(DAY, DATE_FORMAT(NOW(),"%y%m%d"), DATE_FORMAT(a.deliveryDate,"%y%m%d")) AS "needDays",
			d.leadTime,
			a.materialId
		FROM
			pu_purchasing_detail a
		LEFT JOIN pu_purchasing b ON b.recordId = a.purchasingId
		LEFT JOIN md_branch c ON c.recordId = b.branchId
		LEFT JOIN md_material d ON d.recordId = a.materialId
		LEFT JOIN pu_purchasing_apply e ON FIND_IN_SET(e.recordId, a.purchRawId)
		LEFT JOIN md_branch f ON f.recordId = e.branchId
		WHERE
			a.activeFlag = 1
		AND a.`status` NOT IN (500104, 500106, 500108)
		AND a.createdDate > "2021-01-01"
 		AND f.bindErpComId = #{companyId}
		AND	FIND_IN_SET(a.materialId, #{materialIds})
		AND e.notifiId IS NULL
		AND a.deliveryDate IS NOT NULL
		ORDER BY a.deliveryDate ASC
	</select>

    <select id="getRawmaterialStockApplyList" resultType="com.kyb.pcberp.modules.icloud.contract.pojo.Icloud_RawmaterialStock">
		SELECT
			a.*
		FROM
			st_material_store a
		LEFT JOIN md_material b ON b.recordId = a.materialId
		WHERE
			a.activeFlag = 1
		AND a.operateDate <![CDATA[ < ]]> NOW()
		AND a.operateDate >= IFNULL(b.initDate, "2021-01-01")
		AND FIND_IN_SET(
				a.materialId,
				#{materialIds}
			)
		AND a.stockPlaceComId IS NOT NULL
	</select>

    <select id="getMaterialPlaceApplyList" resultType="com.kyb.pcberp.modules.icloud.material.pojo.Icloud_MaterialPlaceCom">
        SELECT
        b.shortName AS "comName",
        a.*
        FROM
        md_material_childcompany a
        LEFT JOIN md_branch b ON b.recordId = a.saleCompanyId
        WHERE
        FIND_IN_SET(
        a.materialId,
        #{materialIds}
        )
        AND a.activeFlag = 1
        <if test="bindErpComId != null and bindErpComId != ''">
            AND (b.bindErpComId IS NULL
            OR b.bindErpComId IN (a.companyId, #{bindErpComId}))
        </if>
    </select>

    <select id="getMaterialByCraftFour" resultType="com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_Material">
        SELECT
        *
        FROM
        (
        SELECT
        a.recordId,
        a.initPrice,
        a.`no`,
        a.`name`,
        a.specification,
        a.actualThickness,
        a.remark,
        a.leadTime,
        manufacturer.`value` AS "manufacturer",
        CASE WHEN IFNULL(a.initStocks,0) > 0 THEN
        (
        IFNULL(
        a.rawmaterialPrice,
        (
        SELECT
        price
        FROM pu_purchasing_detail aa
        LEFT JOIN pu_purchasing bb ON bb.recordId = aa.purchasingId
        WHERE
        aa.materialId = a.recordId
        AND aa.activeFlag = 1
        AND bb.activeFlag = 1
        AND aa.`status` NOT IN (500108)
        AND aa.createdDate >= "2021-01-01"
        ORDER BY
        aa.createdDate DESC
        LIMIT 1
        )
        )
        ) ELSE
        (
        CASE WHEN pricePeriod.parameterValue IS NOT NULL AND pricePeriod.parameterValue > 0 THEN
        (
        CASE WHEN a.updatePriceDate >= DATE_SUB(NOW(),INTERVAL pricePeriod.parameterValue MONTH) THEN a.rawmaterialPrice ELSE 0 END
        )ELSE a.rawmaterialPrice END
        ) END as "price",
        ee.`value` AS "bordLength",
        ff.`value` AS "bordWidth"
        FROM md_material a
        LEFT JOIN md_material_specification_relation aa ON aa.materialId = a.recordId
        AND aa.companyId = a.companyId
        AND aa.specificationId = #{specificationId1}
        AND aa.activeFlag = 1
        LEFT JOIN md_material_specification_relation b ON b.materialId = a.recordId
        AND b.companyId = a.companyId
        AND b.specificationId = #{specificationId2}
        AND b.activeFlag = 1
        LEFT JOIN md_material_specification_relation c ON c.materialId = a.recordId
        AND c.companyId = a.companyId
        AND c.specificationId = #{specificationId3}
        AND c.activeFlag = 1
        LEFT JOIN md_dict_value cc ON cc.recordId = c.value
        LEFT JOIN md_material_specification_relation d ON d.materialId = a.recordId
        AND d.companyId = a.companyId
        AND d.specificationId = #{specificationId4}
        AND d.activeFlag = 1
        LEFT JOIN md_material_specification_relation e ON e.materialId = a.recordId
        AND e.companyId = a.companyId
        AND e.specificationId = #{specificationId5}
        AND e.activeFlag = 1
        LEFT JOIN md_material_specification_relation f ON f.materialId = a.recordId
        AND f.companyId = a.companyId
        AND f.specificationId = #{specificationId6}
        AND f.activeFlag = 1
        LEFT JOIN md_material_specification_relation ee ON ee.materialId = a.recordId
        AND ee.companyId = a.companyId
        AND ee.specificationId = #{specificationId8}
        AND ee.activeFlag = 1
        LEFT JOIN md_material_specification_relation ff ON ff.materialId = a.recordId
        AND ff.companyId = a.companyId
        AND ff.specificationId = #{specificationId9}
        AND ff.activeFlag = 1
        LEFT JOIN md_material_specification_relation gg ON gg.materialId = a.recordId
        AND gg.companyId = a.companyId
        AND gg.specificationId = #{specificationId7}
        AND gg.activeFlag = 1
        LEFT JOIN md_dict_value manufacturer ON manufacturer.recordId = gg.`value`
        LEFT JOIN md_parameterset pricePeriod ON pricePeriod.companyId = a.companyId AND pricePeriod.jianPin = 'materialPriceValidityPeriod'
        WHERE a.companyId = #{companyId}
        AND a.activeFlag = 1
        AND a.materialKind = 100701
        AND a.status = 1
        <!-- 物料id -->
        <if test="recordId != null and recordId != ''">
            AND a.recordId = #{recordId}
        </if>
        <!-- 物料编号 -->
        <if test="no != null and no != ''">
            AND REPLACE(a.no," ","") LIKE CONCAT('%', REPLACE(#{no}," ",""), '%')
        </if>
        <!-- 物料名称 -->
        <if test="name != null and name != ''">
            AND REPLACE(a.`name`," ","") LIKE CONCAT('%', REPLACE(#{name}," ",""), '%')
        </if>
        <if test="condition1 != null and condition1 != ''">
            AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition1}," ",""), '%')
        </if>
        <if test="condition2 != null and condition2 != ''">
            AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition2}," ",""), '%')
        </if>
        <if test="condition3 != null and condition3 != ''">
            AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition3}," ",""), '%')
        </if>
        <if test="condition4 != null and condition4 != ''">
            AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition4}," ",""), '%')
        </if>
        <if test="condition5 != null and condition5 != ''">
            AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition5}," ",""), '%')
        </if>
        <if test="condition6 != null and condition6 != ''">
            AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition6}," ",""), '%')
        </if>
        <if test="manufacturer != null and manufacturer != ''">
            AND manufacturer.`value` = #{manufacturer}
        </if>
        <if test="boardLevelId != null and boardLevelId != ''">
            AND aa.`value` = (
            SELECT
            recordId
            FROM
            md_dict_value
            WHERE
            companyId = a.companyId
            AND `value` = (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = #{boardLevelId}
            )
            AND activeFlag = 1
            LIMIT 1
            )
        </if>
        <if test="materialTypeId != null and materialTypeId != ''">
            AND b.`value` = (
            SELECT
            recordId
            FROM
            md_dict_value
            WHERE
            companyId = a.companyId
            AND `value` = (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = #{materialTypeId}
            )
            AND activeFlag = 1
            LIMIT 1
            )
        </if>
        <if test="copperCladThicknessId != null and copperCladThicknessId != ''">
            AND FIND_IN_SET(
            (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = d.`value`
            ),
            (
            SELECT
            GROUP_CONCAT(`value`,",",IFNULL(remark, ""))
            FROM
            md_dict_value
            WHERE
            recordId = #{copperCladThicknessId}
            )
            )
        </if>
        <if test="daoreId != null and daoreId != ''">
            AND (e.`value` IS NULL OR FIND_IN_SET(
            (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = e.`value`
            ),
            (
            SELECT
            CONCAT(
            `value`,
            ",",
            IFNULL(remark, "")
            )
            FROM
            md_dict_value
            WHERE
            recordId = #{daoreId}
            )
            )
            )
        </if>
        <if test="naiyaId != null and naiyaId != ''">
            AND (f.`value` IS NULL OR f.`value` = (
            SELECT
            recordId
            FROM
            md_dict_value
            WHERE
            companyId = a.companyId
            AND `value` = (
            SELECT
            `value`
            FROM
            md_dict_value
            WHERE
            recordId = #{naiyaId}
            )
            AND activeFlag = 1
            LIMIT 1
            ))
        </if>
        <if test="boardThicknessId != null and boardThicknessId != ''">
            AND (REPLACE ( REPLACE ( REPLACE (cc.`value`, 'm', '' ), 'M', '' ), ' ', '' )  <![CDATA[=]]>
            (
            SELECT
            CAST(REPLACE(REPLACE (REPLACE ( `value`, 'm', '' ), 'M', '' ), ' ', '' ) AS DECIMAL(2,1))
            FROM md_dict_value
            WHERE recordId = #{boardThicknessId}
            )
            <if test="boardThicknessIdTwo != null and boardThicknessIdTwo != ''">
                OR c.`value` =
                (
                SELECT
                recordId
                FROM md_dict_value
                WHERE companyId = a.companyId AND itemId = 3
                AND `value` = (SELECT `value` FROM md_dict_value WHERE recordId = #{boardThicknessIdTwo})
                LIMIT 1
                )
            </if>
            )
        </if>
        <if test="useAgeData != null and useAgeData != ''">
            AND REPLACE(a.`name`," ","") LIKE '%客供%'
        </if>
        <if test="bordLength != null and bordLength != ''">
            AND (ee.`value` = #{bordLength} OR ff.`value` = #{bordLength})
        </if>
        <if test="bordWidth != null and bordWidth != ''">
            AND (ff.`value` = #{bordWidth} OR ee.`value` = #{bordWidth})
        </if>
        <if test="materialNo != null and materialNo != ''">
            AND REPLACE(a.no," ","") LIKE CONCAT('%', REPLACE(#{materialNo}," ",""), '%')
        </if>
        ) material
        WHERE 1=1
        <if test="useAgeData == null or useAgeData == ''">
            AND material.price > 0
            AND material.manufacturer IS NOT NULL
            AND material.bordLength IS NOT NULL
            AND material.bordWidth IS NOT NULL
            AND material.manufacturer <![CDATA[<>]]> "客供料"
        </if>
        ORDER BY material.price ASC
    </select>

</mapper>