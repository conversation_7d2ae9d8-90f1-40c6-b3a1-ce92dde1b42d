package com.kyb.pcberp.modules.hr.permission_center.controller;

import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.modules.hr.permission_center.pojo.Hr_DictItem;
import com.kyb.pcberp.modules.hr.permission_center.pojo.Hr_DictValue;
import com.kyb.pcberp.modules.hr.permission_center.service.Hr_DictService;
import com.kyb.pcberp.modules.sys.entity.DictItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Controller
@RequestMapping(value = "${hrPath}/dict")
public class Hr_DictController {
    @Autowired
    private Hr_DictService hr_dictService;

    @RequestMapping(value = {"itempage"})
    @ResponseBody
    public Page<DictItem> itemPage(@RequestBody DictItem dictItem, HttpServletRequest request,
        HttpServletResponse response) {
        return hr_dictService.itemPage(dictItem, request, response, -2,null);
    }

    @RequestMapping(value = {"getSelectDictManage"})
    @ResponseBody
    public Page<Hr_DictItem> getSelectDictManage(@RequestBody Hr_DictItem hrDictItem, HttpServletRequest request, HttpServletResponse response) {
        return hr_dictService.getSelectDictManage(hrDictItem, request, response, -2);
    }


    @RequestMapping(value = {"addOrUpdateDict"})
    @ResponseBody
    public String addOrUpdateDict(@RequestBody Hr_DictItem dictItem) {
        hr_dictService.addOrUpdateDict(dictItem);
        return "success";
    }


    @RequestMapping(value = {"updateDictStatus"})
    @ResponseBody
    public String updateDictStatus(@RequestBody Hr_DictItem dictItem) {
        hr_dictService.updateDictStatus(dictItem);
        return "success";
    }

    @RequestMapping(value = {"getDictValues"})
    @ResponseBody
    public Page<Hr_DictValue> getDictValues(@RequestBody Hr_DictValue dictValue, HttpServletRequest request, HttpServletResponse response) {
        return hr_dictService.getDictValues(dictValue, request, response, -2);
    }

    @RequestMapping(value = {"addOrUpdateDictValue"})
    @ResponseBody
    public String addOrUpdateDictValue(@RequestBody Hr_DictValue dictValue) {
        hr_dictService.addOrUpdateDictValue(dictValue);
        return "success";
    }

    @RequestMapping(value = {"updateValueStatus"})
    @ResponseBody
    public String updateValueStatus(@RequestBody Hr_DictValue dictValue) {
        hr_dictService.updateValueStatus(dictValue);
        return "success";
    }
}
