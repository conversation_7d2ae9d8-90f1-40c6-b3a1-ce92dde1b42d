package com.kyb.pcberp.modules.sys.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.modules.sys.dao.PcbErpversionDao;
import com.kyb.pcberp.modules.sys.entity.PcbErpVersion;

/**
 * PCBERP Service
 * 
 * <AUTHOR> @version 2016-05-31
 */
@Service
@Transactional(readOnly = true)
public class PcbErpversionService extends CrudService<PcbErpversionDao, PcbErpVersion>
{
    @Transactional(readOnly = false)
    public void save(PcbErpVersion pcbErpVersion)
    {
        super.save(pcbErpVersion);
    }
    
    @Transactional(readOnly = false)
    public void update(PcbErpVersion pcbErpVersion)
    {
        super.dao.update(pcbErpVersion);
    }
    
    @Transactional(readOnly = false)
    public void dousepcbErpVersion(PcbErpVersion pcbErpVersion)
    {
        super.dao.updatestatus();
        super.dao.updateBydouse(pcbErpVersion);
    }
    
}
