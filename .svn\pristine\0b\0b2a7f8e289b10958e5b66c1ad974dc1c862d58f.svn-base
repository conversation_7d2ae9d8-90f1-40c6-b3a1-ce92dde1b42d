package com.kyb.pcberp.modules.quality.web;

import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.quality.entity.Bad;
import com.kyb.pcberp.modules.quality.entity.Discard;
import com.kyb.pcberp.modules.quality.entity.Inspect;
import com.kyb.pcberp.modules.quality.entity.Rework;
import com.kyb.pcberp.modules.quality.service.QualityRecordService;
import com.kyb.pcberp.modules.quality.vo.BadVO;
import com.kyb.pcberp.modules.quality.vo.DiscardVO;
import com.kyb.pcberp.modules.quality.vo.ReworkVO;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

@Controller
@RequestMapping(value = "${adminPath}/quality/qualityRecord")
public class QualityRecordController extends BaseController {

    @Autowired
    private QualityRecordService qualityRecordService;

    @RequestMapping(value = "list")
    public String list(Inspect inspect) {
        return "modules/quality/qualityRecord";
    }

    @RequiresPermissions(value = {"quality:record:view"})
    @RequestMapping(value = {"page"})
    @ResponseBody
    public Page<Discard> getlist(@RequestBody Inspect inspect, HttpServletRequest request, HttpServletResponse response) {

        User user = UserUtils.getUser();
        inspect.setCompany(user.getCompany());

        // 分页查询数据
        Page<Discard> qpage = new Page<Discard>(request, response);
        if (StringUtils.isNotBlank(inspect.getPageNo()) && StringUtils.isNotBlank(inspect.getPageSize())) {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(inspect.getPageNo()));
            qpage.setPageSize(Integer.parseInt(inspect.getPageSize()));
            qpage.setOrderBy("qd.lastUpdDate desc");
        } else {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }

        if (inspect.getStartDate() != null) {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(inspect.getStartDate());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            inspect.setStartDate(deliTime.getTime());
        }

        if (inspect.getEndDate() != null) {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(inspect.getEndDate());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            inspect.setEndDate(deliTime.getTime());
        }

        // 设置排序
        if (StringUtils.isNotBlank(inspect.getOrderBy())) {
            qpage.setOrderBy(inspect.getOrderBy());
        }
        // 设置查询范围
        if (inspect.getQueryAll() != null && !inspect.getQueryAll()) {
            inspect.setCreatedBy(user);
        }
        Page<Discard> page = qualityRecordService.findPage(qpage, inspect);
        return page;
    }

    @RequiresPermissions(value = {"quality:record:view"})
    @RequestMapping(value = {"pageshow"})
    @ResponseBody
    public Page<Rework> pageShow(@RequestBody Inspect inspect, HttpServletRequest request, HttpServletResponse response) {

        User user = UserUtils.getUser();
        inspect.setCompany(user.getCompany());
        // 分页查询数据
        Page<Rework> qpage = new Page<Rework>(request, response);
        if (StringUtils.isNotBlank(inspect.getPageNo()) && StringUtils.isNotBlank(inspect.getPageSize())) {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(inspect.getPageNo()));
            qpage.setPageSize(Integer.parseInt(inspect.getPageSize()));
            qpage.setOrderBy("qr.lastUpdDate desc");
        } else {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        // 设置排序
        if (StringUtils.isNotBlank(inspect.getOrderBy())) {
            qpage.setOrderBy(inspect.getOrderBy());
        }
        // 设置查询范围
        if (inspect.getQueryAll() != null && !inspect.getQueryAll()) {
            inspect.setCreatedBy(user);
        }
        Page<Rework> page = qualityRecordService.findPageOfShow(qpage, inspect);
        return page;
    }

    @RequiresPermissions(value = {"quality:record:view"})
    @RequestMapping(value = {"pageshowbad"})
    @ResponseBody
    public Page<Bad> pageShowBad(@RequestBody Inspect inspect, HttpServletRequest request, HttpServletResponse response) {

        User user = UserUtils.getUser();
        inspect.setCompany(user.getCompany());

        // 分页查询数据
        Page<Bad> qpage = new Page<Bad>(request, response);
        if (StringUtils.isNotBlank(inspect.getPageNo()) && StringUtils.isNotBlank(inspect.getPageSize())) {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(inspect.getPageNo()));
            qpage.setPageSize(Integer.parseInt(inspect.getPageSize()));
            qpage.setOrderBy("qb.lastUpdDate desc");
        } else {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }

        // 设置排序
        if (StringUtils.isNotBlank(inspect.getOrderBy())) {
            qpage.setOrderBy(inspect.getOrderBy());
        }

        // 设置查询范围
        if (inspect.getQueryAll() != null && !inspect.getQueryAll()) {
            inspect.setCreatedBy(user);
        }
        Page<Bad> page = qualityRecordService.findPageOfShowBad(qpage, inspect);
        return page;
    }

    /**
     * 导出报废记录列表
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("quality:record:view")
    @RequestMapping(value = "exportDiscard")
    @ResponseBody
    public String exportInspect(Inspect inspect, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 设置查询范围
            if (inspect.getQueryAll() != null && !inspect.getQueryAll()) {
                inspect.setCreatedBy(UserUtils.getUser());
            }
            if (inspect.getOrderBy() != null) {
                if (inspect.getOrderBy().indexOf(".") == -1) {
                    inspect.setOrderBy("a." + inspect.getOrderBy());
                }
            } else {
                inspect.setOrderBy("sn.craftNo");
            }
            if (inspect.getEndDate() != null) {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(inspect.getEndDate());
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                inspect.setEndDate(deliTime.getTime());
            }
            if (inspect.getStartDate() != null) {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(inspect.getStartDate());
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                inspect.setStartDate(deliTime.getTime());
            }
            inspect.setCompany(UserUtils.getUser().getCompany());
            String fileName = "报废记录" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<DiscardVO> discardVOs = qualityRecordService.findExportDiscardVOs(request, response, inspect);
            new ExportExcel("报废记录", DiscardVO.class, new Integer(0)).setDataList(discardVOs)
                    .write(response, fileName)
                    .dispose();
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/modules/quality/qualityRecord";
    }

    /**
     * 导出返工记录列表
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("quality:record:view")
    @RequestMapping(value = "exportRework")
    @ResponseBody
    public String exportInspectOfShow(Inspect inspect, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 设置查询范围
            if (inspect.getQueryAll() != null && !inspect.getQueryAll()) {
                inspect.setCreatedBy(UserUtils.getUser());
            }
            if (inspect.getOrderBy() != null) {
                if (inspect.getOrderBy().indexOf(".") == -1) {
                    inspect.setOrderBy("a." + inspect.getOrderBy());
                }
            } else {
                inspect.setOrderBy("sn.craftNo");
            }
            if (inspect.getEndDate() != null) {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(inspect.getEndDate());
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                inspect.setEndDate(deliTime.getTime());
            }
            if (inspect.getStartDate() != null) {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(inspect.getStartDate());
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                inspect.setStartDate(deliTime.getTime());
            }
            inspect.setCompany(UserUtils.getUser().getCompany());
            String fileName = "返工记录" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";

            List<ReworkVO> reworkVOs = qualityRecordService.findExportReworkVOs(request, response, inspect);
            new ExportExcel("返工记录", ReworkVO.class, new Integer(0)).setDataList(reworkVOs)
                    .write(response, fileName)
                    .dispose();
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/modules/quality/qualityRecord";
    }

    /**
     * 导出不良记录
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("quality:record:view")
    @RequestMapping(value = "exportbad")
    @ResponseBody
    public String exportInspectOfShowBad(Inspect inspect, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 设置查询范围
            if (inspect.getQueryAll() != null && !inspect.getQueryAll()) {
                inspect.setCreatedBy(UserUtils.getUser());
            }
            if (inspect.getOrderBy() != null) {
                if (inspect.getOrderBy().indexOf(".") == -1) {
                    inspect.setOrderBy("a." + inspect.getOrderBy());
                }
            } else {
                inspect.setOrderBy("sn.craftNo");
            }
            if (inspect.getEndDate() != null) {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(inspect.getEndDate());
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                inspect.setEndDate(deliTime.getTime());
            }
            if (inspect.getStartDate() != null) {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(inspect.getStartDate());
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                inspect.setStartDate(deliTime.getTime());
            }
            inspect.setCompany(UserUtils.getUser().getCompany());
            String fileName = "不良记录" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";

            List<BadVO> badVOs = qualityRecordService.findExportBadVOs(request, response, inspect);
            new ExportExcel("不良记录", BadVO.class, new Integer(0)).setDataList(badVOs)
                    .write(response, fileName)
                    .dispose();
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/modules/quality/qualityRecord";
    }

    @RequiresPermissions(value = {"quality:record:view"})
    @RequestMapping(value = {"discardReport"})
    @ResponseBody
    public Page<Discard> getDiscardReport(@RequestBody Inspect inspect, HttpServletRequest request,
                                          HttpServletResponse response) {

        User user = UserUtils.getUser();
        inspect.setCompany(user.getCompany());

        // 分页查询数据
        Page<Discard> qpage = new Page<Discard>(request, response);
        if (StringUtils.isNotBlank(inspect.getPageNo()) && StringUtils.isNotBlank(inspect.getPageSize())) {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(inspect.getPageNo()));
            qpage.setPageSize(Integer.parseInt(inspect.getPageSize()));
            qpage.setOrderBy("qd.lastUpdDate desc");
        } else {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }

        if (inspect.getStartDate() != null) {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(inspect.getStartDate());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            inspect.setStartDate(deliTime.getTime());
        }

        if (inspect.getEndDate() != null) {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(inspect.getEndDate());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            inspect.setEndDate(deliTime.getTime());
        }

        // 设置排序
        if (StringUtils.isNotBlank(inspect.getOrderBy())) {
            qpage.setOrderBy(inspect.getOrderBy());
        }
        // 设置查询范围
        if (inspect.getQueryAll() != null && !inspect.getQueryAll()) {
            inspect.setCreatedBy(user);
        }
        Page<Discard> page = qualityRecordService.getDiscardReport(qpage, inspect);
        return page;
    }

    /**
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("quality:record:view")
    @RequestMapping(value = "exportDiscardReport")
    @ResponseBody
    public String exportDiscardReport(Inspect inspect, HttpServletRequest request, HttpServletResponse response) {
        try {
            User user = UserUtils.getUser();
            inspect.setCompany(user.getCompany());

            // 分页查询数据
            Page<Discard> qpage = new Page<Discard>(request, response);
            if (StringUtils.isNotBlank(inspect.getPageNo()) && StringUtils.isNotBlank(inspect.getPageSize())) {
                // 分页查询数据
                qpage.setPageNo(Integer.parseInt(inspect.getPageNo()));
                qpage.setPageSize(Integer.parseInt(inspect.getPageSize()));
                qpage.setOrderBy("qd.lastUpdDate desc");
            } else {
                qpage.setPageNo(Global.PAGE_NO);
                qpage.setPageNo(Global.PAGE_SIZE);
            }

            if (inspect.getStartDate() != null) {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(inspect.getStartDate());
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                inspect.setStartDate(deliTime.getTime());
            }

            if (inspect.getEndDate() != null) {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(inspect.getEndDate());
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                inspect.setEndDate(deliTime.getTime());
            }

            List<Discard> discards = qualityRecordService.getDiscardReport(qpage, inspect).getList();
            List<String> heardList = getHeardList();
            String fileName = "各工序报废明细" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            ExportExcel excel = new ExportExcel("", heardList);
            setDataList(excel, discards);
            excel.write(response, fileName).dispose();
            return null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/modules/quality/qualityRecord";
    }

    private void setDataList(ExportExcel excel, List<Discard> discards) {
        int colunm = 0;
        SimpleDateFormat fomart = new SimpleDateFormat("yyyy/MM/dd");
        for (Discard dis : discards) {
            Row row = excel.addRow();
            if (dis.getInspect() == null || dis.getInspect().getCheckDate() == null) {
                CellStyle style = excel.getWb().createCellStyle();
                style.setAlignment(CellStyle.ALIGN_LEFT);
                style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
                style.setAlignment(CellStyle.ALIGN_CENTER);
                style.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
                style.setFillPattern(CellStyle.SOLID_FOREGROUND);
                Cell cell = row.createCell((short) 0);
                cell.setCellValue(dis.getProcessGroupName());
                cell.setCellStyle(style);
                cell = row.createCell((short) 1);
                cell.setCellValue("");
                cell.setCellStyle(style);
                excel.addMergedRegion(row.getRowNum(), row.getRowNum(), 0, 1);
                cell = row.createCell((short) 2);
                cell.setCellValue("");
                cell.setCellStyle(style);
                cell = row.createCell((short) 3);
                cell.setCellValue("");
                cell.setCellStyle(style);
                cell = row.createCell((short) 4);
                cell.setCellValue("");
                cell.setCellStyle(style);
                cell = row.createCell((short) 5);
                cell.setCellValue("");
                cell.setCellStyle(style);
                cell = row.createCell((short) 6);
                cell.setCellValue("");
                cell.setCellStyle(style);
                cell = row.createCell((short) 7);
                cell.setCellValue("");
                cell.setCellStyle(style);
                cell = row.createCell((short) 8);
                cell.setCellValue("");
                cell.setCellStyle(style);
                cell = row.createCell((short) 9);
                cell.setCellValue("");
                cell.setCellStyle(style);
                cell = row.createCell((short) 10);
                cell.setCellValue("");
                cell.setCellStyle(style);
                cell = row.createCell((short) 11);
                cell.setCellValue("");
                cell.setCellStyle(style);
                cell = row.createCell((short) 12);
                cell.setCellValue(dis.getPnlDivisor() == null ? "0" : dis.getPnlDivisor().toString());
                cell.setCellStyle(style);
                cell = row.createCell((short) 13);
                cell.setCellValue(dis.getDiscardArea().toString());
                cell.setCellStyle(style);
                cell = row.createCell((short) 14);
                cell.setCellValue(dis.getDiscardAreaA().toString());
                cell.setCellStyle(style);
                cell = row.createCell((short) 15);
                cell.setCellValue(dis.getDiscardAreaB().toString());
                cell.setCellStyle(style);
            } else {
                colunm = 0;
                excel.addCell(row, colunm++, fomart.format(dis.getInspect().getCheckDate()), 2, null);
                if (dis.getDiscardType().equals("2")) {
                    excel.addCell(row, colunm++, "客诉", 2, null);
                } else {
                    excel.addCell(row, colunm++, "生产", 2, null);
                }
                excel.addCell(row, colunm++, dis.getCraftNo(), 2, null);
                if (dis.getInspectType().equals(1)) {
                    excel.addCell(row, colunm++, "报废", 2, null);
                } else if (dis.getInspectType().equals(2)) {
                    excel.addCell(row, colunm++, "返工", 2, null);
                } else if (dis.getInspectType().equals(3)) {
                    excel.addCell(row, colunm++, "替换", 2, null);
                } else if (dis.getInspectType().equals(4)) {
                    excel.addCell(row, colunm++, "二次补料", 2, null);
                } else {
                    excel.addCell(row, colunm++, "其它", 2, null);
                }
                excel.addCell(row,
                        colunm++,
                        dis.getDutyProcess() == null ? "" : dis.getDutyProcess().getName(),
                        2,
                        null);
                excel.addCell(row, colunm++, dis.getDiscardCause(), 2, null);
                excel.addCell(row, colunm++, dis.getCheckQty(), 2, null);
                excel.addCell(row, colunm++, dis.getDiscardPcsQty(), 2, null);
                excel.addCell(row, colunm++, dis.getDiscardPcsQtyA(), 2, null);
                excel.addCell(row, colunm++, dis.getDiscardPcsQtyB(), 2, null);
                excel.addCell(row, colunm++, dis.getUnitLength(), 2, null);
                excel.addCell(row, colunm++, dis.getUnitWidth(), 2, null);
                excel.addCell(row, colunm++, dis.getPnlDivisor(), 2, null);
                excel.addCell(row, colunm++, dis.getDiscardArea(), 2, null);
                excel.addCell(row, colunm++, dis.getDiscardAreaA(), 2, null);
                excel.addCell(row, colunm++, dis.getDiscardAreaB(), 2, null);
                excel.addCell(row, colunm++, dis.getRemark(), 2, null);
            }

        }

    }

    private List<String> getHeardList() {
        List<String> result = new ArrayList<>();
        result.add("日期");
        result.add("检查类型");
        result.add("生产编号");
        result.add("检测类型");
        result.add("扣数工序");
        result.add("责任原因");
        result.add("检测数(pcs)");
        result.add("不良数(pcs)");
        result.add("报废数(pcs)");
        result.add("返工数(pcs)");
        result.add("SET长(mm)");
        result.add("SET宽(mm)");
        result.add("SET拼版");
        result.add("不良总数(㎡)");
        result.add("报废总数(㎡) ");
        result.add("返工总数(㎡)");
        result.add("备注");
        return result;
    }
}
