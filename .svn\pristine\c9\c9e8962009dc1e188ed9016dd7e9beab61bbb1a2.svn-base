package com.kyb.pcberp.modules.icloud.contract.pojo;

import com.kyb.pcberp.common.base.pojo.Icloud_BasePojo;
import com.kyb.pcberp.modules.icloud.erp.customer.pojo.Icloud_Customer;
import com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_Branch;
import com.kyb.pcberp.modules.icloud.erp.pojo.Icloud_Company;
import com.kyb.pcberp.modules.icloud.sys.pojo.Icloud_User;

import java.util.Date;
import java.util.List;

public class Icloud_Contract extends Icloud_BasePojo
{
    private static final long serialVersionUID = 1L;

    // 承接公司
    private Icloud_Company supplier;

    // 云平台客户公司
    private Icloud_Company customer;

    // 总金额
    private String totalAmt;

    // 合同状态
    private String status;

    // 合同状态
    private String backupStatus;

    // 价格税率说明
    private Long taxDescript;

    // 交货地点
    private String deliveryPlace;

    // 客户订单号
    private String customerPo;

    // 货币类型
    private Long currencyType;

    // 送货方式
    private Long deliveryWay;

    // 备注
    private String remark;

    // 订单总面积
    private String area;

    // erp绑定的客户id
    private String erpCustomerId;

    // erp绑定的客户简称
    private String erpCustomerName;

    // 客户型号
    private String customerModel;

    // 合同明细列表
    private List<Icloud_ContractDeail> contractDeailList;

    // 业务员id
    private String userId;

    // 业务员名字
    private String userName;

    // 发单的下一个合同id
    private String lastId;

    // 发单最后的合同id
    private String finalId;

    // 发单到那个公司
    private String billCompanyName;

    // 开始时间
    private String startDate;

    // 结束时间
    private String endDate;

    // 采购单编号
    private String prdorderNo;

    // 访问合同人
    private Icloud_User user;

    // 各操作审核按钮
    private String audit;

    // 交货方式
    private Long freightWay;

    // 结算方式
    private Long payWay;

    // 客户订单号
    private String cuNo;

    // 发单关联的销售合同
    private String saleId;

    // 个人1，所有0
    private String queryFlag;

    // erp供应商Id
    private String erpSupplierId;

    // erp供应商名称
    private String erpSupplierName;

    // tj 审批类型 1：销售审批；2：采购审批
    private String approvalType;

    // tj 审批提示
    private String msg;

    // 子公司
    private Integer branch;

    private Icloud_Branch branchs;

    private Icloud_Customer erpCustomer;

    private String no;

    private Date OrderDate;

    private String craftNoList;

    private String custModelList;

    private String customerPorderNo;

    private String ranDomQuery;

    private String craftNo;

    public Icloud_Company getSupplier()
    {
        return supplier;
    }

    public void setSupplier(Icloud_Company supplier)
    {
        this.supplier = supplier;
    }

    public Icloud_Company getCustomer()
    {
        return customer;
    }

    public void setCustomer(Icloud_Company customer)
    {
        this.customer = customer;
    }

    public String getTotalAmt()
    {
        return totalAmt;
    }

    public void setTotalAmt(String totalAmt)
    {
        this.totalAmt = totalAmt;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public Long getTaxDescript()
    {
        return taxDescript;
    }

    public void setTaxDescript(Long taxDescript)
    {
        this.taxDescript = taxDescript;
    }

    public String getDeliveryPlace()
    {
        return deliveryPlace;
    }

    public void setDeliveryPlace(String deliveryPlace)
    {
        this.deliveryPlace = deliveryPlace;
    }

    public String getCustomerPo()
    {
        return customerPo;
    }

    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }

    public Long getCurrencyType()
    {
        return currencyType;
    }

    public void setCurrencyType(Long currencyType)
    {
        this.currencyType = currencyType;
    }

    public Long getDeliveryWay()
    {
        return deliveryWay;
    }

    public void setDeliveryWay(Long deliveryWay)
    {
        this.deliveryWay = deliveryWay;
    }

    public String getRemark()
    {
        return remark;
    }

    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public String getArea()
    {
        return area;
    }

    public void setArea(String area)
    {
        this.area = area;
    }

    public String getErpCustomerId()
    {
        return erpCustomerId;
    }

    public void setErpCustomerId(String erpCustomerId)
    {
        this.erpCustomerId = erpCustomerId;
    }

    public String getErpCustomerName()
    {
        return erpCustomerName;
    }

    public void setErpCustomerName(String erpCustomerName)
    {
        this.erpCustomerName = erpCustomerName;
    }

    public String getCustomerModel()
    {
        return customerModel;
    }

    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }

    public List<Icloud_ContractDeail> getContractDeailList()
    {
        return contractDeailList;
    }

    public void setContractDeailList(List<Icloud_ContractDeail> contractDeailList)
    {
        this.contractDeailList = contractDeailList;
    }

    public String getUserId()
    {
        return userId;
    }

    public void setUserId(String userId)
    {
        this.userId = userId;
    }

    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getLastId()
    {
        return lastId;
    }

    public void setLastId(String lastId)
    {
        this.lastId = lastId;
    }

    public String getFinalId()
    {
        return finalId;
    }

    public void setFinalId(String finalId)
    {
        this.finalId = finalId;
    }

    public String getBillCompanyName()
    {
        return billCompanyName;
    }

    public void setBillCompanyName(String billCompanyName)
    {
        this.billCompanyName = billCompanyName;
    }

    public String getStartDate()
    {
        return startDate;
    }

    public void setStartDate(String startDate)
    {
        this.startDate = startDate;
    }

    public String getEndDate()
    {
        return endDate;
    }

    public void setEndDate(String endDate)
    {
        this.endDate = endDate;
    }

    public String getPrdorderNo()
    {
        return prdorderNo;
    }

    public void setPrdorderNo(String prdorderNo)
    {
        this.prdorderNo = prdorderNo;
    }

    public Icloud_User getUser()
    {
        return user;
    }

    public void setUser(Icloud_User user)
    {
        this.user = user;
    }

    public String getAudit()
    {
        return audit;
    }

    public void setAudit(String audit)
    {
        this.audit = audit;
    }

    public Long getFreightWay()
    {
        return freightWay;
    }

    public void setFreightWay(Long freightWay)
    {
        this.freightWay = freightWay;
    }

    public Long getPayWay()
    {
        return payWay;
    }

    public void setPayWay(Long payWay)
    {
        this.payWay = payWay;
    }

    public String getCuNo()
    {
        return cuNo;
    }

    public void setCuNo(String cuNo)
    {
        this.cuNo = cuNo;
    }

    public String getSaleId()
    {
        return saleId;
    }

    public void setSaleId(String saleId)
    {
        this.saleId = saleId;
    }

    public String getBackupStatus()
    {
        return backupStatus;
    }

    public void setBackupStatus(String backupStatus)
    {
        this.backupStatus = backupStatus;
    }

    public String getQueryFlag()
    {
        return queryFlag;
    }

    public void setQueryFlag(String queryFlag)
    {
        this.queryFlag = queryFlag;
    }

    public String getErpSupplierId()
    {
        return erpSupplierId;
    }

    public void setErpSupplierId(String erpSupplierId)
    {
        this.erpSupplierId = erpSupplierId;
    }

    public String getErpSupplierName()
    {
        return erpSupplierName;
    }

    public void setErpSupplierName(String erpSupplierName)
    {
        this.erpSupplierName = erpSupplierName;
    }

    public String getApprovalType()
    {
        return approvalType;
    }

    public void setApprovalType(String approvalType)
    {
        this.approvalType = approvalType;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    public Integer getBranch()
    {
        return branch;
    }

    public void setBranch(Integer branch)
    {
        this.branch = branch;
    }

    public Icloud_Customer getErpCustomer()
    {
        return erpCustomer;
    }

    public void setErpCustomer(Icloud_Customer erpCustomer)
    {
        this.erpCustomer = erpCustomer;
    }

    public String getNo()
    {
        return no;
    }

    public void setNo(String no)
    {
        this.no = no;
    }

    public Date getOrderDate()
    {
        return OrderDate;
    }

    public void setOrderDate(Date orderDate)
    {
        OrderDate = orderDate;
    }

    public Icloud_Branch getBranchs()
    {
        return branchs;
    }

    public void setBranchs(Icloud_Branch branchs)
    {
        this.branchs = branchs;
    }

    public String getCraftNoList()
    {
        return craftNoList;
    }

    public void setCraftNoList(String craftNoList)
    {
        this.craftNoList = craftNoList;
    }

    public String getCustModelList()
    {
        return custModelList;
    }

    public void setCustModelList(String custModelList)
    {
        this.custModelList = custModelList;
    }

    public String getCustomerPorderNo()
    {
        return customerPorderNo;
    }

    public void setCustomerPorderNo(String customerPorderNo)
    {
        this.customerPorderNo = customerPorderNo;
    }

    public String getRanDomQuery()
    {
        return ranDomQuery;
    }

    public void setRanDomQuery(String ranDomQuery)
    {
        this.ranDomQuery = ranDomQuery;
    }

    public String getCraftNo()
    {
        return craftNo;
    }

    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }
}

