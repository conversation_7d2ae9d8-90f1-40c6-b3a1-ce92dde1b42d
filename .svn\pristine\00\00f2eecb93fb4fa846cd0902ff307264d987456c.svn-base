package com.kyb.pcberp.modules.inter.entity;

import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.StringUtils;

public class ConfigCustomerAttachements extends DataEntity<ConfigCustomerAttachements>
{
    private static final long serialVersionUID = 1L;

    private String configCustomerId;

    private String orgFileName;

    private String realFileName;

    private String fileUrl;

    private String name;

    private String type;

    public String getConfigCustomerId()
    {
        return configCustomerId;
    }

    public void setConfigCustomerId(String configCustomerId)
    {
        this.configCustomerId = configCustomerId;
    }

    public String getOrgFileName()
    {
        return orgFileName;
    }

    public void setOrgFileName(String orgFileName)
    {
        this.orgFileName = orgFileName;
    }

    public String getRealFileName()
    {
        return realFileName;
    }

    public void setRealFileName(String realFileName)
    {
        this.realFileName = realFileName;
    }

    public String getFileUrl()
    {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl)
    {
        this.fileUrl = fileUrl;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public String getDownloadUrl()
    {
        if(StringUtils.isNotBlank(fileUrl))
        {
            return FileManageUtils.getUrl(fileUrl).toString();
        }
        return null;
    }
}
