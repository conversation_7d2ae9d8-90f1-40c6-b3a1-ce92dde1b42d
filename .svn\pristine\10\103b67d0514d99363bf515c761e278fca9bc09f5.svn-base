kybApp.controller('supplierQuotationCtrl', ['$rootScope', '$scope', '$timeout', 'upida', 'CommonUtil', 'BaseUtil','$stateParams',
    function ($rootScope, $scope, $timeout, upida, CommonUtil, BaseUtil,$stateParams) {
        $scope.$on('$viewContentLoaded', function () {
            // initialize core components
            MainCtrl.initAjax();
            // set default layout mode
            $rootScope.settings.layout.pageBodySolid = false;
            $rootScope.settings.layout.pageSidebarClosed = false;
        });
        var vm = this;
        // tabs控制
        vm.tabs = {
            listForm: {active: true},
            deailForm: {active: false,show: false}
        };
        vm.queryAll = false;
        function loadRight() {
            vm.queryAll = CommonUtil.dataRangeIsAll("10710", BaseUtil.getMenuList());
            if ($stateParams.purchRawId) {
                showPrucgRaw($stateParams.purchRawId);
            }
            $timeout(function() {
                vm.loadSupplierQquotation();
            },500);
            loadItemsData();
        };
        vm.message = "";
        vm.clickFlag = true;

        // 分页数据
        vm.page = {};

        // 显示数据大小
        vm.page.pageSizeOptions = [5, 10, 30, 50];

        // 原料申请分页数据
        vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
        vm.page.pageSize = 10;
        vm.page.pageNo = 1;
        vm.page.url = "purch/bidding/loadBiddingList";

        vm.time = {
            start: {},
            end: {}
        };
        vm.sentTimeStartQr = "";
        vm.sentTimeEndQr = "";

        vm.clicks = true;

        vm.initDate=function(date)
        {
            if(date == ""){
                vm.rangeOptions = {
                    //format: "YYYY-MM-DD",
                    startDate: new Date(vm.sentTimeStartQr),
                    minDate:new Date(vm.sentTimeEndQr)
                };
                vm.time= {
                    start: vm.rangeOptions.startDate,
                    end: vm.rangeOptions.minDate
                }
            }
        };

        // //加载下拉框数据
        // function loadItemsData() {
        //     const query = {};
        //     MainCtrl.blockUI({
        //         animate: true
        //     });
        //     // 加载物料类型
        //     upida.post("purch/supplierQuotation/getSupplierList",query).then(function(data) {
        //         vm.supplier = result.data.supplierList;
        //         MainCtrl.unblockUI();
        //     });
        // }

        vm.materialList = [];//物料list
        //加载下拉框数据
        function loadItemsData() {
            MainCtrl.blockUI({
                animate: true
            });
            // 加载物料类型
            upida.get("purch/rawmaterial/load/data?queryAll="+vm.queryAll).then(function (result) {
                angular.forEach(result.materialList,function(p){
                    if(!p.remain)
                    {
                        vm.materialList.push(p);
                    }
                });
                vm.supplier = result.data.supplierList;
            });
        }

        vm.loadSupplierQquotationList = [];
        vm.effectTimeStatus = "1";
        vm.loadSupplierQquotation = function(){
            const query = {};
            query.supplierName = vm.supplierName;
            query.status = vm.status;
            query.materialName = vm.materialName;
            query.materialNo = vm.materialNo;
            query.effectTimeStatus = vm.effectTimeStatus;
            MainCtrl.blockUI({
                animate: true,
            });
            upida.post("purch/supplierQuotation/loadSupplierQquotation",query).then(function(data) {
                vm.loadSupplierQquotationList = data;
                MainCtrl.unblockUI();
            });
        }

        vm.delSupplierQuotation = function(row){
            MainCtrl.blockUI({
                animate: true,
            });
            upida.post("purch/supplierQuotation/delSupplierQuotation",row).then(function(data) {
                if (data === "success"){
                    vm.message = "供应商报价单删除成功!";
                }else{
                    vm.message = "数据异常,请刷新重试!"
                }
                $('#static').modal();
                vm.loadSupplierQquotation();
            });
        }

        vm.addQuotation = function(){
            vm.quotation = {};
            $("#addSupplierQuotation").modal();
        }

        vm.saveSupplierQuotation = function(){
            if(!vm.clickFlag)
            {
                vm.message = "请勿多次点击！";
                $("#static").modal();
                return;
            }
            if (undefined === vm.quotation.supplier){
                // vm.message = "请选择供应商！";
                // $("#static").modal();
                alert("请选择供应商！");
                return;
            }
            if (undefined === vm.quotation.material){
                // vm.message = "请选择具体物料！";
                // $("#static").modal();
                alert("请选择具体物料！");
                return;
            }
            if (!vm.quotation.price){
                // vm.message = "请填写具体价格！";
                // $("#static").modal();
                alert("请填写价格！")
                return;
            }
            if (!/^[1-9]\d*(\.\d+)?$/.test(vm.quotation.price)) {
                alert("单价请输入正数！");
                return;
            }
            vm.clickFlag = false;
            MainCtrl.blockUI({
                animate: true,
            });
            const query = {};
            query.supplierId = vm.quotation.supplier.recordId;
            query.materialId = vm.quotation.material.recordId;
            query.price = vm.quotation.price;
            query.recordId = vm.quotation.recordId;
            query.effectiveTime = vm.quotation.effectiveTime;
            query.status = vm.quotation.status;
            query.supplierName = vm.quotation.supplierName;
            query.materialName = vm.quotation.materialName;
            query.supplierNo = vm.quotation.supplierNo;
            query.materialNo = vm.quotation.materialNo;
            upida.post("purch/supplierQuotation/saveSupplierQuotation",query).then(function(data) {
                if (data === "success")
                {
                    vm.message = "保存成功!";
                }
                else if (data === "failStatus")
                {
                    vm.message = "该报价单正常审批中，请刷新重试!";
                }
                else if (data === "approval")
                {
                    vm.message = "您修改的供应商报价需要上级审批，系统已自动为您提交审批!";
                }
                else
                {
                    vm.message = "数据异常,请刷新重试!"
                }
                $('#static').modal();
                MainCtrl.unblockUI();
                vm.clickFlag = true;
                $("#addSupplierQuotation").modal('hide');
                vm.loadSupplierQquotation();
            });
        }

        vm.updateSupplierQuotation = function(row){
             vm.quotation = row;
             vm.quotation.supplier = {"recordId":row.supplierId,"name":row.supplierName};
             vm.quotation.material = {"recordId":row.materialId,"name":row.materialName};
            $("#addSupplierQuotation").modal();
        };


        vm.allPageChecked = false;
        vm.selectAllPage = function ()
        {
            if (!vm.loadSupplierQquotationList)
            {
                return;
            }
            angular.forEach(vm.loadSupplierQquotationList, function (row){
                row.checked = !vm.allPageChecked;
            });

            vm.getRecordIds();
        };

        vm.getRecordIds = function ()
        {
            vm.recordIds = null;
            vm.recordIdList = [];
            angular.forEach(vm.loadSupplierQquotationList, function (row){
                if(row.checked)
                {
                    if (vm.recordIds)
                    {
                        vm.recordIds = vm.recordIds + "," + row.recordId;
                    }
                    else
                    {
                        vm.recordIds = row.recordId;
                    }
                    vm.recordIdList.push(angular.copy(row));
                }
            })
        };

        vm.approvalQuotation = function ()
        {
            if (!vm.recordIds)
            {
                vm.message = "请选择要审批的报价单！";
                $("#static").modal();
                return;
            }
            vm.message = "您确定要批量审批报价单吗?";
            $("#staticCommitAuditOpen").modal();
        };

        vm.commitAudit = function ()
        {
            if (vm.clicks)
            {
                vm.clicks = false;
            }
            MainCtrl.blockUI({
                animate: true,
            });
            let approvalList = [];
            for (let i = 0; i < vm.recordIdList.length; i++)
            {
                if (vm.recordIdList[i].status != '1001')
                {
                    continue;
                }
                approvalList.push(vm.recordIdList[i]);
            }
            upida.post("purch/supplierQuotation/approvalQuotation",approvalList).then(function(data) {
                if (data && data.message){
                    vm.message = data.message;
                }else{
                    vm.message = "数据异常,请刷新重试!";
                }
                $('#static').modal();
                MainCtrl.unblockUI();
                vm.clickFlag = true;
                $("#addSupplierQuotation").modal('hide');
                vm.loadSupplierQquotation();
            });
        }
        vm.getSpecificationValue = function(){
            let materialId = vm.quotation.material.recordId;
            for(let i = 0;i < vm.materialList.length; i++){
                if (vm.materialList[i].recordId === materialId){
                     vm.quotation.specification = vm.materialList[i].specification;
                     break;
                }
            }
        }


        $scope.$on("$stateChangeSuccess", function () {
            upida.setScope($scope);
            loadRight();
        });
    }]);
