<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.purch.dao.PurchasingDao">
    
    <resultMap id="purchasingResult" type="Purchasing">
    			<id property="recordId" column="recordId" />
				<result property="no" column="no" />
				<result property="orderDate" column="orderDate" />
				<result property="deliveryDate" column="deliveryDate" />
				<result property="deliveryWay" column="deliveryWay" />
				<result property="currencyType" column="currencyType" />
				<result property="payWay" column="payWay" />
				<result property="payDays" column="payDays" />
				<result property="prePayAmount" column="prePayAmount" />
				<result property="prePayBalance" column="prePayBalance" />
				<result property="taxDescript" column="taxDescript" />
				<result property="status" column="status" />
				<result property="activeFlag" column="activeFlag" />
				<result property="createdDate" column="createdDate" />
				<result property="lastUpdDate" column="lastUpdDate" />
				<result property="remark" column="remark" />
				<result property="deliveryPlace" column="deliveryPlace" />
				<result property="qualityStd" column="qualityStd" />
				<result property="assureDays" column="assureDays" />
				<result property="cancelCause" column="cancelCause" />
				<result property="paycause" column="paycause" />
				<result property="supplyChainId" column="supplyChainId" />
				<result property="printRemark" column="printRemark"/>
				<result property="internalSupplyChainId" column="internalSupplyChainId" />
				
				<association property="branch" column="branchId" javaType="Branch">
					<id property="recordId" column="branch.recordId"/>
				</association>
				
				<association property="company" column="companyId" javaType="Company">
					<id property="recordId" column="company.recordId"/>
					<result property="name" column="company.name"/>
					<result property="nameEn" column="company.nameEn"/>
					<result property="address" column="company.address"/>
					<result property="phone" column="company.phone"/>
					<result property="fax" column="company.fax"/>
					<result property="logo" column="company.logo"/>
				</association>
			
				<association property="createdBy" column="createdBy" javaType="User">
					<id property="recordId" column="createdBy.recordId"/>
				</association>
				
				<association property="purchaser" column="purchaser" javaType="User">
					<id property="recordId" column="purchaser.recordId"/>
					<result property="userName" column="purchaser.userName"/>
					<result property="mobile" column="purchaser.mobile"/>
					<result property="phone" column="purchaser.phone"/>
				</association>
	
				<association property="lastUpdBy" column="lastUpdBy" javaType="User">
					<id property="recordId" column="lastUpdBy.recordId"/>
				</association>
				<!-- 供应商 -->
				<association property="supplier" column="supplierId" javaType="Supplier">
					<id property="recordId" column="supplier.recordId"/>
					<result property="name" column="supplier.name"/>
					<result property="address" column="supplier.address"/>
					<result property="bizPerson" column="supplier.bizPerson"/>
					<result property="phone" column="supplier.phone"/>
					<result property="fax" column="supplier.fax"/>
					<result property="checkDate" column="supplier.checkDate"/>
				</association>
						
				<!-- 打印详单		 -->	
				<collection property="purchasingDetailList" ofType="PurchasingDetail">
					<id property="recordId" column="purchasingDetailList.recordId" />
					<result property="price" column="purchasingDetailList.price" />
					<result property="quantity" column="purchasingDetailList.quantity" />
					<result property="amount" column="purchasingDetailList.amount" />
					<result property="craftDescript" column="purchasingDetailList.craftDescript" />
					<result property="remark" column="purchasingDetailList.remark" />
					<result property="supplierModel" column="purchasingDetailList.supplierModel" />
					<result property="deliveryDays" column="purchasingDetailList.deliveryDays" />
					<result property="createdDate" column="purchasingDetailList.createdDate" />
					<result property="deliveryDate" column="purchasingDetailList.deliveryDate" />
					<association property="material" column="materialId" javaType="Material">
						<id property="recordId" column="purchasingDetailList.materialId"/>
						<result property="no" column="purchasingDetailList.material.no" />
						<result property="name" column="purchasingDetailList.material.name" />
						<result property="specification" column="purchasingDetailList.material.specification" />
						<association property="unit" column="unit.recordId" javaType="DictValue">
							<id property="recordId" column="purchasingDetailList.material.unit.recordId"/>
							<result property="value" column="purchasingDetailList.material.unit.value" />
						</association>
					</association>
				</collection>
    </resultMap>
    
    
	<sql id="puPurchasingColumns">
		a.recordId,
		a.companyId AS "company.recordId",
		a.no AS "no",
		a.supplierId AS "supplier.recordId",
		a.branchId AS "branch.recordId",
		a.orderDate AS "orderDate",
		a.deliveryDate AS "deliveryDate",
		a.deliveryWay AS "deliveryWay",
		a.currencyType AS "currencyType",
		a.payWay AS "payWay",
		a.payDays AS "payDays",
		a.prePayAmount AS "prePayAmount",
		a.prePayBalance AS "prePayBalance",
		a.taxDescript AS "taxDescript",
		a.purchaser AS "purchaser.recordId",
		a.status AS "status",
		a.activeFlag AS "activeFlag",
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.remark AS "remark",
		a.deliveryPlace AS "deliveryPlace",
		a.qualityStd AS "qualityStd",
		a.assureDays AS "assureDays",
		a.cancelCause AS "cancelCause",
		a.period AS "period",
		a.supplierContractNo as "supplierContractNo",
		a.paycause,
		a.type,
		a.proofPath,
		a.sourceNo,
		a.counterTrialCause,
		a.supplyChainId AS "supplyChain.recordId",
		a.paymentDays,
		a.radio,
		a.sourcePurchaseOrdersId,
		a.deptId,
		a.printRemark,
		a.internalSupplyChainId AS "internalSupplyChain.recordId"
	</sql>
	
	<sql id="puPurchasingJoins">
	</sql>
    
	<select id="get" resultType="Purchasing">
		SELECT 
			<include refid="puPurchasingColumns"/>,
			ms.checkDate as "supplier.checkDate",
			supplyChain.calculationType AS "supplyChain.calculationType",
			supplyChain.rateType AS "supplyChain.rateType",
			(
				SELECT
					ROUND(SUM(IFNULL(amount,0)),2)
				FROM pu_purchasing_detail WHERE purchasingId = a.recordId AND activeFlag = 1 AND status <![CDATA[<>]]> 500108
			) AS "totalAmount",
			ms.erpId AS "supplier.erpId",
			b.name AS "deptName",
			a.internalSupplyChainId
		FROM pu_purchasing a
		<include refid="puPurchasingJoins"/>
		LEFT JOIN md_supplier ms on a.supplierId = ms.recordId
		LEFT JOIN md_supplier supplyChain on a.supplyChainId = supplyChain.recordId
		LEFT JOIN md_department b ON b.recordId = a.deptId
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="getById" resultType="Purchasing">
		SELECT 
			<include refid="puPurchasingColumns"/>,
			ms.checkDate as "supplier.checkDate"
		FROM pu_purchasing a
		<include refid="puPurchasingJoins"/>
		left join md_supplier ms on a.supplierId=ms.recordId
		WHERE a.recordId = #{recordId}
	</select>
	
	<!-- 通知单号条件 rx 20210914 -->
	<select id="findList" resultType="Purchasing">
		SELECT 
			<include refid="puPurchasingColumns"/>,
			b.name AS "supplier.name",
			b.shortName AS "supplier.shortName",
			b.no AS "supplier.no",
			su.userName AS "purchaser.userName",
			sn2.no AS "sourceNoShow",
			dep.name AS "deptName",
			c.name AS "supplyChain.name",
			c.no AS "supplyChain.no",
			c.shortName AS "supplyChain.shortName",
			c.calculationType AS "supplyChain.calculationType",
			c.rateType AS "supplyChain.rateType",
			d.value AS "businessPaymentValue",
		    b.erpId AS "supplier.erpId",
			e.name AS "internalSupplyChain.name",
			e.no AS "internalSupplyChain.no",
			e.shortName AS "internalSupplyChain.shortName",
			e.calculationType AS "internalSupplyChain.calculationType",
			e.rateType AS "internalSupplyChain.rateType"
		FROM pu_purchasing a
		LEFT JOIN md_supplier b on a.supplierId = b.recordId
		LEFT JOIN sm_user su on a.purchaser = su.recordId
		LEFT JOIN pu_purchasing_detail ppu on a.recordId = ppu.purchasingId
		LEFT JOIN md_material md on md.recordId = ppu.materialId
		LEFT JOIN eg_carda ec ON ec.recordId = a.sourceNo AND a.type = 2
		LEFT JOIN sl_notification sn2 ON sn2.processCardAId = ec.recordId AND sn2.activeFlag = 1 
		
		LEFT JOIN md_employee me ON me.userId = a.purchaser AND me.activeFlag = 1
		LEFT JOIN md_department_relation_employee mdre ON mdre.employeeId = me.recordId 
		AND mdre.activeFlag = 1 
		AND mdre.`status` = 1
		LEFT JOIN md_department_relation_employee mdre2 ON mdre2.employeeId = me.recordId 
		AND mdre2.activeFlag = 1 
		AND mdre2.`status` = 2
		LEFT JOIN md_organization mo ON mo.recordId = mdre2.allId 
		AND mo.activeFlag = 1
		LEFT JOIN md_department dep ON dep.recordId = IFNULL( mdre.allId, mo.deptId )
		LEFT JOIN md_supplier c ON c.recordId = a.supplyChainId
		LEFT JOIN md_dict_value d ON d.recordId = c.businessPaymentId
		LEFT JOIN md_supplier e ON e.recordId = a.internalSupplyChainId
		<include refid="puPurchasingJoins"/>
		<where>
			a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
			<if test=" no != null and no != ''">
				AND REPLACE(a.no,' ','') LIKE CONCAT('%',REPLACE(#{no},' ',''),'%')
			</if>
			<!-- 供应商编号 -->
			<if test="supplier != null and supplier.no != null and supplier.no !=''">
				AND REPLACE(b.no,' ','') LIKE CONCAT('%',REPLACE(#{supplier.no},' ',''),'%')
			</if>
			<!-- 供应商编号 -->
			<if test="supplier != null and supplier.name != null and supplier.name !=''">
				AND REPLACE(b.name,' ','') LIKE CONCAT('%',REPLACE(#{supplier.name},' ',''),'%')
			</if>

			<if test="supplyChain != null and supplyChain.no != null and supplyChain.no !=''">
				AND REPLACE(c.no,' ','') LIKE CONCAT('%',REPLACE(#{supplyChain.no},' ',''),'%')
			</if>
			<if test="supplyChain != null and supplyChain.name != null and supplyChain.name !=''">
				AND REPLACE(c.name,' ','') LIKE CONCAT('%',REPLACE(#{supplyChain.name},' ',''),'%')
			</if>

			<!-- 物料编号 -->
			<if test=" materialNo != null and materialNo != ''">
				AND REPLACE(md.no,' ','') LIKE CONCAT('%',REPLACE(#{materialNo},' ',''),'%')
			</if>
			<!-- 物料名称 -->
			<if test=" materialName != null and materialName != ''">
				AND REPLACE(md.name,' ','') LIKE CONCAT('%',REPLACE(#{materialName},' ',''),'%')
			</if>
			<if test="craftRequierName != null and craftRequierName != ''">
				AND REPLACE(ppu.craftDescript,' ','') LIKE CONCAT('%',REPLACE(#{craftRequierName},' ',''),'%')
			</if>
			<if test="status != null and status != ''">
	   			<if test="status != -1">
	   				AND a.status = #{status}
	   			</if>
	   			<if test="status == -1">
	   				AND a.status != 500108
	   			</if>
	   		</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (
					a.createdBy = #{createdBy.recordId}
					OR a.purchaser = #{createdBy.recordId}
					OR b.createdBy = #{createdBy.recordId}
					OR b.recordId IN (SELECT supplierId FROM md_supplier_user WHERE userId = #{createdBy.recordId} AND activeFlag = 1)
				)
	   		</if>
	   		<if test="recordId == null || recordId == ''">
				<if test="sentTimeStartQr != null and sentTimeStartQr != '' and sentTimeEndQr != null and sentTimeEndQr != ''">
					<![CDATA[ AND a.orderDate >= #{sentTimeStartQr} AND a.orderDate <= #{sentTimeEndQr} ]]>
				</if>
			</if>
			<if test=" type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="recordId != null and recordId != ''">
				AND a.recordId = #{recordId}
			</if>
		</where>
		GROUP BY a.recordId
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="getInitializationPurchasing" resultType="Purchasing">
		SELECT 
			a.recordId,
			a.companyId AS "company.recordId",
			a.no AS "no",
			a.supplierId AS "supplier.recordId",
			a.orderDate AS "orderDate",
			a.deliveryDate AS "deliveryDate",
			a.deliveryWay AS "deliveryWay",
			a.currencyType AS "currencyType",
			a.payWay AS "payWay",
			a.payDays AS "payDays",
			a.prePayAmount AS "prePayAmount",
			a.prePayBalance AS "prePayBalance",
			a.taxDescript AS "taxDescript",
			a.purchaser AS "purchaser.recordId",
			a.status AS "status",
			a.activeFlag AS "activeFlag",
			a.createdBy AS "createdBy.recordId",
			a.createdDate,
			a.lastUpdBy AS "lastUpdBy.recordId",
			a.lastUpdDate,
			a.remark AS "remark",
			a.deliveryPlace AS "deliveryPlace",
			a.qualityStd AS "qualityStd",
			a.assureDays AS "assureDays",
			a.cancelCause AS "cancelCause",
			b.name AS "supplier.name",
			b.no AS "supplier.no",
			su.userName AS "purchaser.userName"
		FROM pu_purchasing a
		LEFT JOIN md_supplier b on a.supplierId = b.recordId
		LEFT JOIN sm_user su on a.purchaser = su.recordId
		WHERE a.status in(500103,500104)
		and a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	
	<!-- 打印方法 -->
	<select id="getPurchasingAndDetail" resultMap="purchasingResult">
		SELECT 
			a.recordId,
			a.companyId AS "company.recordId",
			a.no AS "no",
			a.supplierId AS "supplier.recordId",
			a.orderDate AS "orderDate",
			a.deliveryDate AS "deliveryDate",
			a.deliveryWay AS "deliveryWay",
			a.currencyType AS "currencyType",
			a.payWay AS "payWay",
			a.payDays AS "payDays",
			a.prePayAmount AS "prePayAmount",
			a.prePayBalance AS "prePayBalance",
			a.taxDescript AS "taxDescript",
			a.purchaser AS "purchaser",
			a.status AS "status",
			a.activeFlag AS "activeFlag",
			a.createdBy AS "createdBy.recordId",
			a.createdDate,
			a.lastUpdBy AS "lastUpdBy.recordId",
			a.lastUpdDate,
			a.remark AS "remark",
			a.deliveryPlace AS "deliveryPlace",
			a.qualityStd AS "qualityStd",
			a.assureDays AS "assureDays",
			a.cancelCause AS "cancelCause",
			a.branchId AS "branch.recordId",
			a.paycause AS "paycause",
			a.supplyChainId AS "supplyChainId",
			sup.name as "supplier.name",
			sup.address as "supplier.address", 
			sup.bizPerson as "supplier.bizPerson", 
			sup.phone as "supplier.phone", 
			sup.fax as "supplier.fax", 
			sup.checkDate as "supplier.checkDate", 
			com.name as "company.name",
			com.nameEn as "company.nameEn",
			com.address as "company.address",
			com.phone as "company.phone",
			com.fax as "company.fax",
			com.logo as "company.logo",
			de.recordId AS "purchasingDetailList.recordId",
			de.materialId AS "purchasingDetailList.materialId",
			de.price AS "purchasingDetailList.price",
			CAST(de.quantity AS CHAR)+0 AS "purchasingDetailList.quantity",
			de.amount AS "purchasingDetailList.amount",
			de.craftDescript AS "purchasingDetailList.craftDescript",
			de.remark AS "purchasingDetailList.remark",
			de.supplierModel AS "purchasingDetailList.supplierModel",
			de.deliveryDays AS "purchasingDetailList.deliveryDays",
			de.createdDate AS "purchasingDetailList.createdDate",
			de.deliveryDate AS "purchasingDetailList.deliveryDate",
			us.userName AS "purchaser.userName",
			us.mobile AS "purchaser.mobile",
			us.phone AS "purchaser.phone",
		    a.printRemark,
			IFNULL(b.NO,CONCAT('YL', inter.recordId)) AS "purchasingDetailList.material.no",
			IFNULL(b.NAME, inter.name) AS "purchasingDetailList.material.name",
			IFNULL(b.specification, inter.specifications) AS "purchasingDetailList.material.specification",
			c.VALUE AS "purchasingDetailList.material.unit.value",
			c.recordId AS "purchasingDetailList.material.unit.recordId"
		FROM pu_purchasing a
		LEFT JOIN md_supplier sup ON sup.recordId = IFNULL(a.supplyChainId,a.supplierId)
		LEFT JOIN md_company com ON a.companyId=com.recordId
		LEFT JOIN pu_purchasing_detail de ON de.purchasingId = a.recordId
		LEFT JOIN sm_user us ON a.purchaser = us.recordId
		LEFT JOIN md_material b ON b.recordId = de.materialId AND (de.product = 1 OR de.product IS NULL)
		LEFT JOIN md_dict_value c ON c.recordId = b.unit
		LEFT JOIN inter_config_supplier inter ON inter.recordId = de.materialId AND de.product = 2
		WHERE a.recordId =#{recordId} 
			AND a.companyId = #{company.recordId} 
			AND a.activeFlag = #{DEL_FLAG_NORMAL}
			AND de.activeFlag = #{DEL_FLAG_NORMAL}
			ORDER BY de.craftDescript
	</select>
	
	<select id="findAllList" resultType="Purchasing">
		SELECT 
			<include refid="puPurchasingColumns"/>
		FROM pu_purchasing a
		<include refid="puPurchasingJoins"/>
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	<!-- 原料采购和成品采购的采购编码 -->
	<sql id="purNoJoins">
		(
		select ppu.recordId,ppu.no,ppu.taxDescript
			from pu_purchasing ppu
			<where>
				ppu.companyId = #{company.recordId} 
				and ppu.activeFlag = #{DEL_FLAG_NORMAL}
				and ppu.supplierId =  #{supplier.recordId}
				and ppu.status = #{status}
				<if test="sourceFlag != null and sourceFlag == 2">
				    <!-- 来源是付款申请单,只需要查询包含检测通过、待审核和新建状态的采购单明细 -->
					and EXISTS(SELECT 1 FROM pu_purchasing_detail ppd 
							WHERE ppd.purchasingId = ppu.recordId and ppd.status in (500101,500105,500107)  and ppd.activeFlag = #{DEL_FLAG_NORMAL})
				</if>
				<if test="sourceFlag != null and sourceFlag == 1">
				    <!-- 来源是来料检测,只需要查询包含检测未通过和新建状态的采购单明细 -->
					and EXISTS(SELECT 1 FROM pu_purchasing_detail ppd 
							WHERE ppd.purchasingId = ppu.recordId and ppd.status in (500101,500106)  and ppd.activeFlag = #{DEL_FLAG_NORMAL})
				</if>	
				<if test="pwList != null and pwList.size()>0">
					and ppu.payWay in 
					<foreach collection="pwList" item ="item" open="(" separator="," close=")">
				    	#{item}  
				    </foreach>
				</if>    
			</where>
			union all
		select ppr.recordId,ppr.no,ppr.taxDescript
			from pu_prdorder ppr
			<where>
				ppr.companyId = #{company.recordId} 
				and ppr.activeFlag = #{DEL_FLAG_NORMAL}
				and ppr.supplierId =  #{supplier.recordId}
				and ppr.status = #{prdStatus}
				<if test="sourceFlag != null and sourceFlag == 2">
				    <!-- 来源是付款申请单,只需要查询包含检测通过、待审核和新建状态的采购单明细 -->
					and EXISTS(SELECT 1 FROM pu_prdorder_detail ppd 
							WHERE ppd.prdorderId = ppr.recordId and ppd.status in (500101,500105,500107)  and ppd.activeFlag = #{DEL_FLAG_NORMAL})
				</if>
				<if test="sourceFlag != null and sourceFlag == 1">
				    <!-- 来源是来料检测,只需要查询包含检测未通过和新建状态的采购单明细 -->
					and EXISTS(SELECT 1 FROM pu_prdorder_detail ppd 
							WHERE ppd.prdorderId = ppr.recordId and ppd.status in (500101,500106)  and ppd.activeFlag = #{DEL_FLAG_NORMAL})
				</if>
				<if test="pwList != null and pwList.size()>0">
					and ppr.payWay in 
					<foreach collection="pwList" item ="item" open="(" separator="," close=")">
				    	#{item}  
				    </foreach>
				</if>
			</where>
		) pp
	</sql>
	<!-- 查询供应商包含的采购单号 -->
	<select id="findAllPurchasingsBySupplier" resultType="Purchasing">
		SELECT 
			pp.recordId,
			pp.no,
			pp.taxDescript
		FROM 
		<include refid="purNoJoins"/>	
	</select>
	<!-- 查询供应商包含的原料采购单号 -->
	<select id="findPurchasingsBySupplier" resultType="Purchasing">
		SELECT 
			<include refid="puPurchasingColumns"/>
			FROM pu_purchasing a
			LEFT JOIN
			(
				SELECT 
					purchasingId AS "purchasingId",
					COUNT(1) AS "count"
				FROM pu_purchasing_detail 
				WHERE companyId = #{company.recordId} AND `status` IN(500101,500103,500106,500107,500109) 
				AND activeFlag = 1 AND contractDeailId IS NULL GROUP BY purchasingId
			) b ON b.purchasingId = a.recordId
			<where>
				a.companyId = #{company.recordId} 
				and a.activeFlag = #{DEL_FLAG_NORMAL}
				and a.supplierId =  #{supplier.recordId}
				AND
				( 
					a.status = #{status} OR b.count > 0
				)
				<if test="sourceFlag != null and sourceFlag == 2">
				    <!-- 来源是付款申请单,只需要查询包含检测通过、待审核和新建状态的采购单明细 -->
					and EXISTS(SELECT 1 FROM pu_purchasing_detail ppd 
							WHERE ppd.purchasingId = a.recordId and ppd.status in (500101,500105,500107,500103)  and ppd.activeFlag = #{DEL_FLAG_NORMAL})
				</if>
				<if test="sourceFlag != null and sourceFlag == 1">
				    <!-- 来源是来料检测,只需要查询包含检测未通过和新建状态的采购单明细 -->
					and EXISTS(SELECT 1 FROM pu_purchasing_detail ppd 
							WHERE ppd.purchasingId = a.recordId and ppd.status in (500101,500106,500107,500109,500103) and ppd.activeFlag = #{DEL_FLAG_NORMAL})
				</if>
				<if test="pwList != null and pwList.size()>0">
					and a.payWay in 
					<foreach collection="pwList" item ="item" open="(" separator="," close=")">
				    	#{item}  
				    </foreach>
				</if>
			</where>
	</select>
	
	
	<!-- 查询供应商包含的原料采购单号 -->
	<select id="getPurchasingTo" resultType="Purchasing">
		SELECT 
				distinct d.recordId,
				a.companyId AS "company.recordId",
				d.no AS "no",
				a.supplierId AS "supplier.recordId",
				a.branchId AS "branch.recordId",
				a.orderDate AS "orderDate",
				a.deliveryDate AS "deliveryDate",
				a.deliveryWay AS "deliveryWay",
				a.currencyType AS "currencyType",
				a.payWay AS "payWay",
				a.payDays AS "payDays",
				a.prePayAmount AS "prePayAmount",
				a.prePayBalance AS "prePayBalance",
				a.taxDescript AS "taxDescript",
				a.purchaser AS "purchaser.recordId",
				a.status AS "status",
				a.activeFlag AS "activeFlag",
				a.createdBy AS "createdBy.recordId",
				a.createdDate,
				a.lastUpdBy AS "lastUpdBy.recordId",
				a.lastUpdDate,
				a.remark AS "remark",
				a.deliveryPlace AS "deliveryPlace",
				a.qualityStd AS "qualityStd",
				a.assureDays AS "assureDays",
				a.cancelCause AS "cancelCause"
			FROM pu_purchasing a
			LEFT JOIN pu_purchasing_detail b on a.recordId=b.purchasingId
			LEFT JOIN pu_returns_detail c on b.recordId=c.purchasingDetailId
			LEFT JOIN pu_returns d on c.returnsId=d.recordId
		where
				a.companyId = #{company.recordId} 
				and a.activeFlag = #{DEL_FLAG_NORMAL}
				and a.supplierId =  #{supplier.recordId}
			and c.status in(500109,500505,500107,500106) and c.purchasingType=1 and c.desireTreatment=1
	</select>
	
	<!-- 查询供应商包含的成品采购单号 -->
	<select id="findPrdorderBySupplier" resultType="Purchasing">
		select ppr.recordId,ppr.no,ppr.taxDescript
			from pu_prdorder ppr
			<where>
				ppr.companyId = #{company.recordId} 
				and ppr.activeFlag = #{DEL_FLAG_NORMAL}
				and ppr.supplierId =  #{supplier.recordId} 
				and ppr.status = #{prdStatus}
				<if test="sourceFlag != null and sourceFlag == 2">
				    <!-- 来源是付款申请单,只需要查询包含检测通过、待审核和新建状态的采购单明细 -->
					and EXISTS(SELECT 1 FROM pu_prdorder_detail ppd 
							WHERE ppd.prdorderId = ppr.recordId and ppd.status in (500101,500102,500105,500107)  and ppd.activeFlag = #{DEL_FLAG_NORMAL})
				</if>
				<if test="sourceFlag != null and sourceFlag == 1">
				    <!-- 来源是来料检测,只需要查询包含检测未通过和新建状态的采购单明细 -->
					and EXISTS(SELECT 1 FROM pu_prdorder_detail ppd 
							WHERE ppd.prdorderId = ppr.recordId and ppd.status in (500101,500102,500106,500109,500107)  and ppd.activeFlag = #{DEL_FLAG_NORMAL})
				</if>
				<if test="pwList != null and pwList.size()>0">
					and ppr.payWay in 
					<foreach collection="pwList" item ="item" open="(" separator="," close=")">
				    	#{item}  
				    </foreach>
				</if>
			</where>
	</select>
	
		<!-- 查询供应商包含的成品采购单号 -->
	<select id="getReturnsdetailPurchasing" resultType="Purchasing">
		SELECT 
			distinct d.recordId,d.no,a.taxDescript
			FROM pu_prdorder a
			LEFT JOIN pu_prdorder_detail b on a.recordId=b.prdorderId
			LEFT JOIN pu_returns_detail c on b.recordId=c.purchasingDetailId
			LEFT JOIN pu_returns d on c.returnsId=d.recordId
		where
				a.companyId = #{company.recordId} 
				and a.activeFlag = #{DEL_FLAG_NORMAL}
				and a.supplierId =  #{supplier.recordId}
			and c.status in(500109,500505,500107,500106) and c.purchasingType=2 and c.desireTreatment=1
	</select>
	
	
	<!-- 原料采购和成品采购的采购编码 -->
	<sql id="purNoWithReturnsJoins">
		(
		select ppu.recordId,ppu.no,ppu.taxDescript
			from pu_purchasing ppu
			<where>
				ppu.companyId = #{company.recordId} 
				and ppu.activeFlag = #{DEL_FLAG_NORMAL}
				and ppu.supplierId =  #{supplier.recordId}
				and ppu.status in (#{status},500104)
				and EXISTS(SELECT 1 FROM pu_purchasing_detail ppd WHERE ppd.purchasingId = ppu.recordId and ppd.activeFlag = #{DEL_FLAG_NORMAL})
			</where>
			union all
		select ppr.recordId,ppr.no,ppr.taxDescript
			from pu_prdorder ppr
			<where>
				ppr.companyId = #{company.recordId} 
				and ppr.activeFlag = #{DEL_FLAG_NORMAL}
				and ppr.supplierId =  #{supplier.recordId}
				and ppr.status in (#{prdStatus},500104)
				and EXISTS(SELECT 1 FROM pu_prdorder_detail ppd WHERE ppd.prdorderId = ppr.recordId and ppd.activeFlag = #{DEL_FLAG_NORMAL})
			</where>
		) pp
	</sql>
	<!-- 查询供应商包含的采购单号 -->
	<select id="findAllPurchasingsWithReturns" resultType="Purchasing">
		SELECT 
			pp.recordId,
			pp.no,
			pp.taxDescript
		FROM 
		<include refid="purNoWithReturnsJoins"/>	
	</select>
	<!-- 查询供应商包含的原料采购单号 -->
	<select id="findPurchasingsWithReturns" resultType="Purchasing">
		SELECT 
			distinct  <include refid="puPurchasingColumns"/>
			FROM pu_purchasing_detail b
			LEFT JOIN pu_purchasing a on a.recordId=b.purchasingId
			<where>
				b.companyId = #{company.recordId} 
				and b.activeFlag = #{DEL_FLAG_NORMAL}
				and a.supplierId =  #{supplier.recordId}
				and b.status in (500104,500109)
			</where>
	</select>
	
	
	<!-- 查询供应商包含的原料采购单号 -->
	<select id="getPurchasingsWithReturns" resultType="Purchasing">
		SELECT distinct b.* from pu_purchasing_detail a
		LEFT JOIN pu_purchasing b on b.recordId=a.purchasingId
		LEFT JOIN pu_returns_detail c on c.purchasingDetailId=a.recordId
		WHERE a.companyId=#{company.recordId} and c.desireTreatment=2 and c.status=500516 and c.purchasingType=1
		and b.supplierId =  #{supplier.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	
	<!-- 查询供应商包含的成品采购单号 -->
	<select id="findPrdorderWithReturns" resultType="Purchasing">
			select  distinct ppr.recordId,ppr.no,ppr.taxDescript
			from pu_prdorder_detail a
			LEFT JOIN pu_prdorder ppr on ppr.recordId=a.prdorderId
			<where>
				a.companyId = #{company.recordId}  
				and a.activeFlag = #{DEL_FLAG_NORMAL} 
				and ppr.supplierId =  #{supplier.recordId} 
				and a.status in (#{prdStatus})
			</where>
	</select>
	
	<!-- 查询供应商包含的成品采购单号 -->
	<select id="getPrdorderWithReturns" resultType="Purchasing">
		SELECT distinct b.recordId,b.no,b.taxDescript
		from pu_prdorder_detail a
		LEFT JOIN pu_prdorder b on b.recordId=a.prdorderId
		LEFT JOIN pu_returns_detail c on c.purchasingDetailId=a.recordId
		WHERE a.companyId=#{company.recordId} and c.desireTreatment=2 and c.status=500516 and c.purchasingType=2
		and b.activeFlag = #{DEL_FLAG_NORMAL} 
		and b.supplierId =  #{supplier.recordId} 
	</select>
	
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO pu_purchasing(
			companyId,
			no,
			supplierId,
			branchId,
			orderDate,
			deliveryDate,
			deliveryWay,
			currencyType,
			payWay,
			payDays,
			prePayAmount,
			prePayBalance,
			taxDescript,
			purchaser,
			status,
			activeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			remark,
			deliveryPlace,
			qualityStd,
			assureDays,
			cancelCause,
			period,
			supplierContractNo,
			paycause,
			type,
			sourceNo,
			supplyChainId,
			paymentDays,
			radio,
			sourcePurchaseOrdersId,
			deptId,
			printRemark,
			internalSupplyChainId
		) VALUES (
			#{company.recordId},
			#{no},
			#{supplier.recordId},
			#{branch.recordId},
			#{orderDate},
			#{deliveryDate},
			#{deliveryWay},
			#{currencyType},
			#{payWay},
			#{payDays},
			#{prePayAmount},
			#{prePayBalance},
			#{taxDescript},
			#{purchaser.recordId},
			#{status},
			#{activeFlag},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{deliveryPlace},
			#{qualityStd},
			#{assureDays},
			#{cancelCause},
			#{period},
			#{supplierContractNo},
			#{paycause},
			#{type},
			#{sourceNo},
			#{supplyChain.recordId},
			#{paymentDays},
			#{radio},
		  	#{sourcePurchaseOrdersId},
		  	#{deptId},
		    #{printRemark},
		    #{internalSupplyChain.recordId}
		)
	</insert>
	<update id="update">
		UPDATE pu_purchasing SET 	
			companyid = #{company.recordId},
			no = #{no},
			supplierId = #{supplier.recordId},
			branchId = #{branch.recordId},
			orderDate = #{orderDate},
			deliveryDate = #{deliveryDate},
			deliveryWay = #{deliveryWay},
			currencyType = #{currencyType},
			payWay = #{payWay},
			payDays = #{payDays},
			prePayAmount = #{prePayAmount},
			prePayBalance = #{prePayBalance},
			taxDescript = #{taxDescript},
			purchaser = #{purchaser.recordId},
			status = #{status},
			activeFlag = #{activeFlag},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			remark = #{remark},
			deliveryPlace = #{deliveryPlace},
			qualityStd = #{qualityStd},
			assureDays = #{assureDays},
			cancelCause = #{cancelCause},
			period = #{period},
			supplierContractNo=#{supplierContractNo},
			paycause = #{paycause},
			sourceNo = #{sourceNo},
			supplyChainId = #{supplyChain.recordId},
			paymentDays = #{paymentDays},
			radio = #{radio},
			deptId = #{deptId},
			printRemark = #{printRemark},
			internalSupplyChainId = #{internalSupplyChain.recordId}
		WHERE recordId = #{recordId}
	</update>
	<!-- 删除 -->
	<update id="delete">
		UPDATE pu_purchasing
		SET activeFlag = 2
		WHERE recordId = #{recordId}
	</update>
	<update id="deleteDetail">
		update pu_purchasing_detail
		SET activeFlag = 2
		WHERE purchasingId = #{recordId}
	</update>
	<!-- 确认 -->
	<update id="updateStutas">
			UPDATE 
				pu_purchasing
			SET 
				status = #{status},
				lastUpdBy = #{lastUpdBy.recordId},
				lastUpdDate = #{lastUpdDate}
			WHERE recordId = #{recordId}
	</update>


	<!--一键自动检测入库更新采购单状态-->
	<!-- 确认 -->
	<update id="updateStutasAuto">
			UPDATE
				pu_purchasing
			SET
			status = #{status}
			WHERE recordId = #{recordId}
	</update>
	<!-- 原料出入库 采购入库时处理对账单的 采购单的数量 -->
	<update id="updatePurchasingPrePayAmountOrBalance" parameterType="Purchasing">
		update pu_purchasing set
		<if test="prePayAmount!=null">
			prePayAmount = #{prePayAmount},
		</if>
		<if test="prePayBalance!=null">
			prePayBalance = #{prePayBalance},
		</if>
		<if test="lastUpdBy != null and lastUpdBy.recordId != null and lastUpdBy.recordId != ''">
			lastUpdBy = #{lastUpdBy.recordId},
		</if>
		<if test="lastUpdDate != null and lastUpdDate != ''">
			lastUpdDate = #{lastUpdDate},
		</if>
			activeFlag = #{DEL_FLAG_NORMAL}
		where recordId=#{recordId}
	</update>
	
	<!-- WC 2017-09-13 查询已入库的原料采购单信息 -->
	<select id="inHousePrint" resultMap="printInHouseMap">
		SELECT 
			b.recordId AS "recordId",
			b.companyId AS "company.recordId",
			b.no AS "no",
			b.supplierId AS "supplier.recordId",
			b.purchaser AS "purchaser.recordId",
			a.recordId AS "purchasingDetailList.recordId",
			a.price AS "purchasingDetailList.price",
			a.remark AS "purchasingDetailList.remark",
			a.materialId AS "purchasingDetailList.material.recordId",
			c.name AS "purchasingDetailList.material.name",
			c.specification AS "purchasingDetailList.material.specification",
			c.unit AS "purchasingDetailList.material.unit.recordId",
			d.recordId AS "purchasingDetailList.rawmaterialStockList.recordId",
			d.quantity AS "purchasingDetailList.rawmaterialStockList.quantity",
			d.createdBy AS "purchasingDetailList.rawmaterialStockList.createdBy.recordId",
			d.operateDate AS "purchasingDetailList.rawmaterialStockList.operateDate",
			e.recordId AS "purchasingDetailList.sourceDetectionList.recordId",
			e.detectionPerson AS "purchasingDetailList.sourceDetectionList.detectionPerson.recordId",
			f.name AS "supplier.name"
		FROM pu_purchasing b 
		LEFT JOIN pu_purchasing_detail a ON b.recordId = a.purchasingId AND a.activeFlag = #{DEL_FLAG_NORMAL}
		LEFT JOIN md_material c ON a.materialId = c.recordId
		LEFT JOIN st_material_store d ON a.recordId = d.purchasingDtlId AND d.inOutType = 1
		LEFT JOIN qc_source_detection e ON a.recordId = e.orderId AND e.status = 400505 AND e.activeFlag = #{DEL_FLAG_NORMAL} AND e.resourceType = 1
		JOIN  md_supplier f ON f.recordId = b.supplierId
		WHERE b.recordId = #{recordId}
	</select>
	
	<select id="getPeriod" resultType="Integer">
		SELECT period FROM pu_purchasing WHERE recordId = #{recordId}
	</select>

    <select id="selectId" resultType="java.lang.String">
		select recordId from pu_purchasing where companyId = #{company.recordId} and no=#{no}
	</select>


    <resultMap id="printInHouseMap" type="Purchasing">
  		<id property="recordId" column="recordId" />
		<result property="no" column="no" />
		<association property="purchaser" column="purchaser" javaType="User">
			<id property="recordId" column="purchaser.recordId"/>
		</association>
		<association property="supplier" column="supplierId" javaType="Supplier">
			<id property="recordId" column="supplier.recordId"/>
			<result property="name" column="supplier.name"/>
		</association>
		<collection property="purchasingDetailList" ofType="PurchasingDetail">
			<id property="recordId" column="purchasingDetailList.recordId" />
			<result property="price" column="purchasingDetailList.price" />
			<result property="remark" column="purchasingDetailList.remark" />
			<association property="material" column="materialId" javaType="Material">
	       		<id property="recordId" column="purchasingDetailList.material.recordId"/>
				<result property="name" column="purchasingDetailList.material.name"/>
				<result property="specification" column="purchasingDetailList.material.specification"/>
				<association property="unit" column="unit" javaType="DictValue">
					<id property="recordId" column="purchasingDetailList.material.unit.recordId"/>
				</association>
			</association>
			<collection property="rawmaterialStockList" ofType="RawmaterialStock">
				<id property="recordId" column="purchasingDetailList.rawmaterialStockList.recordId" />
				<result property="quantity" column="purchasingDetailList.rawmaterialStockList.quantity" />
				<result property="operateDate" column="purchasingDetailList.rawmaterialStockList.operateDate" />
				<association property="createdBy" column="createdBy" javaType="User">
					<id property="recordId" column="purchasingDetailList.rawmaterialStockList.createdBy.recordId"/>
				</association>
			</collection>
			<collection property="sourceDetectionList" ofType="SourceDetection">
				<id property="recordId" column="purchasingDetailList.sourceDetectionList.recordId" />
				<association property="detectionPerson" column="detectionPerson" javaType="User">
					<id property="recordId" column="purchasingDetailList.sourceDetectionList.detectionPerson.recordId" />
				</association>
			</collection>
		</collection>
    </resultMap>
    
    <!-- zjn 2018-10-19 根据采购明细id获取采购单的采购员 -->
    <select id="getPurchaserByDetailId" resultType="Purchasing">
    	SELECT 
		a.recordId,
		a.purchaser AS "purchaser.recordId"
		FROM pu_purchasing a
		LEFT JOIN pu_purchasing_detail b ON b.purchasingId = a.recordId
		where b.recordId = #{recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
    </select>
    
    <!-- tj 2019-03-14 修改原料采购单状态  -->
    <select id="updateStatus">
    	UPDATE pu_purchasing SET 
			status = #{status}
		WHERE recordId = #{recordId}
    </select>
	<!--lq 2019-04-23 查询供应商合同编号是否存在-->
    <select id="getCheckSupplierNo" resultType="java.lang.Integer">

		SELECT COUNT(a.recordId) FROM pu_purchasing a
		WHERE a.companyId=#{company.recordId} and a.supplierContractNo=#{supplierContractNo}
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	
	<select id="findTotalAmt" resultType="java.lang.String">
		SELECT
			ROUND(SUM(IFNULL(a.amount, 0)), 2)
    	FROM pu_purchasing_detail a
    	WHERE a.purchasingId = #{recordId} AND a.activeFlag = 1 AND a.status <![CDATA[<>]]> 500108	
	</select>
	
	<update id="approvalUpdateStatus">
		UPDATE
			pu_purchasing
		SET
			<if test="counterTrialCause != null and counterTrialCause != ''">
				counterTrialCause = #{counterTrialCause},
			</if>
			status = #{status}
		WHERE recordId = #{recordId}
	</update>
	
	<!-- rx 20210914 增加通知单查下条件 -->
	<select id="exportPurchasingData" resultType="com.kyb.pcberp.modules.purch.vo.PurchasingVo">
		SELECT
			a.recordId,
			b.`no` AS "purchasingNo",
			c.`no` AS "supplierNo",
			c.`name` AS "supplierName",
			b.orderDate AS "orderDate",
			CASE a.`status` WHEN 500101 THEN '新建'
			WHEN 500102 THEN '已确认'
			WHEN 500103 THEN '已审核'
			WHEN 500104 THEN '已入库'
			WHEN 500105 THEN '检测通过'
			WHEN 500106 THEN '检测未通过'
			WHEN 500107 THEN '待检测'
			WHEN 500109 THEN '检测中'
			WHEN 500110 THEN '审批中'
			ELSE '其它'
			END AS "status",
			currencyType.`value` AS "currencyType",
			payWay.`value` AS "payWay",
			taxDescript.`value` AS "taxDescript",
			e.userName AS "purchaser",
			b.remark AS "purchasingRemark",
			assureDays.`value` AS "assureDays",
<!-- 			CASE a.companyId WHEN 17  -->
<!-- 			THEN  -->
<!-- 			( -->
<!-- 				CASE WHEN a.supplierModel IS NOT NULL THEN CONCAT(d.name,'(',a.supplierModel,')') -->
<!-- 				ELSE d.name -->
<!-- 				END -->
<!-- 			) -->
<!-- 			ELSE d.name END AS "materialName", -->
			IFNULL(d.no,CONCAT('YL', inter.recordId)) AS "materialNo",
			IFNULL(d.name, inter.name) AS "materialName",
			a.craftDescript AS "craftDescript",
			unit.`value` AS "unit",
			a.price AS "price",
			a.quantity AS "quantity",
			a.amount AS "amount",
			a.remark AS "materiaRemark",
			a.deliveryDate,
			d.pcsLength AS "pcsLength",
			d.pcsWidth AS "pcsWidth",
			d.diameter AS "diameter",
			b.counterTrialCause,
			SUM(aa.quantity) AS "warehousingNum",
			a.purchasingRemarks AS "purchasingRemarks",
			supplyChain.no AS "supplyChainNo",
			supplyChain.name AS "supplyChainName"
		FROM pu_purchasing_detail a
		LEFT JOIN pu_purchasing b ON b.recordId = a.purchasingId
		LEFT JOIN md_supplier c ON c.recordId = b.supplierId
		LEFT JOIN md_material d ON d.recordId = a.materialId AND (a.product = 1 OR a.product IS NULL)
		LEFT JOIN sm_user e ON e.recordId = b.purchaser
		LEFT JOIN md_dict_value payWay ON payWay.recordId = b.payWay AND payWay.itemId = 13 AND payWay.activeFlag = 1
		LEFT JOIN md_dict_value currencyType ON currencyType.recordId = b.currencyType AND currencyType.itemId = 11 AND currencyType.activeFlag = 1
		LEFT JOIN md_dict_value taxDescript ON taxDescript.recordId = b.taxDescript AND taxDescript.itemId = 19 AND taxDescript.activeFlag = 1
		LEFT JOIN md_dict_value assureDays ON assureDays.recordId = b.assureDays AND assureDays.itemId = 29 AND assureDays.activeFlag = 1
		LEFT JOIN md_dict_value unit ON unit.recordId = d.unit AND unit.itemId = 27 AND unit.activeFlag = 1
		LEFT JOIN st_material_store aa ON aa.purchasingDtlId = a.recordId AND aa.activeFlag = 1 AND aa.`status` in (99999901,99999903,99999902)
		LEFT JOIN md_supplier supplyChain ON supplyChain.recordId = b.supplyChainId
		LEFT JOIN inter_config_supplier inter ON inter.recordId = a.materialId AND a.product = 2
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = 1 AND b.activeFlag = 1
			<!-- 采购单编号 -->
			<if test=" no != null and no != ''">
				AND REPLACE(b.no,' ','') LIKE CONCAT('%',REPLACE(#{no},' ',''),'%')
			</if>
			<!-- 供应商编号 -->
			<if test="supplier != null and supplier.no != null and supplier.no !=''">
				AND REPLACE(c.no,' ','') LIKE CONCAT('%',REPLACE(#{supplier.no},' ',''),'%')
			</if>
			<!-- 供应商编号 -->
			<if test="supplier != null and supplier.name != null and supplier.name !=''">
				AND REPLACE(c.name,' ','') LIKE CONCAT('%',REPLACE(#{supplier.name},' ',''),'%')
			</if>
			<!-- 物料编号 -->
			<if test=" materialNo != null and materialNo != ''">
				AND REPLACE(d.no,' ','') LIKE CONCAT('%',REPLACE(#{materialNo},' ',''),'%')
			</if>
			<!-- 物料名称 -->
			<if test=" materialName != null and materialName != ''">
				AND REPLACE(d.name,' ','') LIKE CONCAT('%',REPLACE(#{materialName},' ',''),'%')
			</if>
			<if test="craftRequierName != null and craftRequierName != ''">
				AND REPLACE(a.craftDescript,' ','') LIKE CONCAT('%',REPLACE(#{craftRequierName},' ',''),'%')
			</if>
			<if test="status != null and status != ''">
	   			<if test="status != -1">
	   				AND b.status = #{status}
	   			</if>
	   			<if test="status == -1">
	   				AND b.status != 500108
	   			</if>
	   		</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (
					b.createdBy = #{createdBy.recordId}
					OR b.purchaser = #{createdBy.recordId}
					OR c.createdBy = #{createdBy.recordId}
					OR c.recordId IN (SELECT supplierId FROM md_supplier_user WHERE userId = #{createdBy.recordId} AND activeFlag = 1)
				)
	   		</if>
	   		<if test="sentTimeStartQr != null and sentTimeStartQr != '' and sentTimeEndQr != null and sentTimeEndQr != ''">
				  <![CDATA[ AND b.orderDate >= #{sentTimeStartQr} AND b.orderDate <= #{sentTimeEndQr} ]]>
	   		</if>
	   		<if test="noticeNo != null and noticeNo != ''">
				AND b.recordId  IN (
				SELECT
					 DISTINCT aa.purchasingId
				FROM
					pu_purchasing_detail aa
					LEFT JOIN pu_purchasing pp ON aa.purchasingId = pp.recordId
					LEFT JOIN pu_purchasing_apply bb ON FIND_IN_SET( bb.recordId, aa.purchRawId )
					LEFT JOIN sl_notification cc ON cc.recordId = bb.notifiId 
				WHERE
					aa.companyId = #{company.recordId}
					AND pp.activeFlag = 1
					AND REPLACE(cc.no,' ','') LIKE CONCAT('%',REPLACE(#{noticeNo},' ',''),'%')
					AND cc.recordId IS NOT NULL 
				)
			</if>
			<if test=" type != null and type != ''">
				AND b.type = #{type}
			</if>
			GROUP BY
			a.recordId
 			ORDER BY b.createdDate DESC
		</where>
	</select>
	
	<select id="findPurchasingNoisEnable" resultType="Integer">
		SELECT 
			COUNT(1) 
		FROM pu_purchasing 
		WHERE companyId = #{company.recordId} AND no = #{no}
		<if test="recordId != null and recordId != ''">
			AND recordId <![CDATA[<>]]> #{recordId}
		</if>
	</select>
	
	<select id="exportPurWholeInfo" resultType="PurchasingExceVo">
		SELECT
		 b.`no` AS 'purchaseNo',
		 a.price AS 'price',
		 a.quantity AS 'quantity',
		 a.amount AS 'amount',
		 a.createdDate AS 'createdDate',
		 c.`no` AS 'materialNo',
		 c.`name` AS 'materialName',
		 c.specification AS 'specification',
		 c.pcsLength AS 'pcsLength',
		 c.pcsWidth AS 'pcsWidth',
		 c.diameter AS 'diameter',
		 f.`value` AS 'materailTypeName',
		 (
		  SELECT
		   SUM(quantity)
		  FROM
		   st_material_store
		  WHERE
		   purchasingDtlId = a.recordId
		  AND activeFlag = 1
		  AND `status` = 99999901
		  AND inOutType = 1
		 ) AS 'inNum',
		 (
		  SELECT
		   MAX(operateDate)
		  FROM
		   st_material_store
		  WHERE
		   purchasingDtlId = a.recordId
		  AND activeFlag = 1
		  AND `status` = 99999901
		  AND inOutType = 1
		 ) AS 'inDate',
		 GROUP_CONCAT(d.purchApplyId) AS 'applyNos',
		  GROUP_CONCAT(d.quantity) AS 'applyNums',
		  GROUP_CONCAT(d.createdDate) AS 'applyDates',
		  GROUP_CONCAT(e.`no`) AS 'noticeNos',
		  GROUP_CONCAT(IFNULL(e.replyStock,'')) AS 'noticeNums'
		FROM
		 pu_purchasing_detail a
		LEFT JOIN pu_purchasing b ON b.recordId = a.purchasingId
		LEFT JOIN md_material c ON c.recordId = a.materialId
		LEFT JOIN pu_purchasing_apply d ON FIND_IN_SET( d.recordId, a.purchRawId )
		LEFT JOIN sl_notification e ON e.recordId = d.notifiId
		LEFT JOIN md_dict_value f ON f.recordId = c.materialType
		LEFT JOIN md_supplier g on b.supplierId = g.recordId
		WHERE
		 a.activeFlag = 1
		AND b.activeFlag = 1
		AND a.`status` <![CDATA[ <>  ]]>500108
		AND b.`status` <![CDATA[ <>  ]]> 500108
		AND	a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
			<if test=" no != null and no != ''">
				AND REPLACE(b.no,' ','') LIKE CONCAT('%',REPLACE(#{no},' ',''),'%')
			</if>
			<!-- 供应商编号 -->
			<if test="supplier != null and supplier.no != null and supplier.no !=''">
				AND REPLACE(g.no,' ','') LIKE CONCAT('%',REPLACE(#{supplier.no},' ',''),'%')
			</if>
			<!-- 供应商编号 -->
			<if test="supplier != null and supplier.name != null and supplier.name !=''">
				AND REPLACE(g.name,' ','') LIKE CONCAT('%',REPLACE(#{supplier.name},' ',''),'%')
			</if>
			<!-- 物料编号 -->
			<if test=" materialNo != null and materialNo != ''">
				AND REPLACE(c.no,' ','') LIKE CONCAT('%',REPLACE(#{materialNo},' ',''),'%')
			</if>
			<!-- 物料名称 -->
			<if test=" materialName != null and materialName != ''">
				AND REPLACE(c.name,' ','') LIKE CONCAT('%',REPLACE(#{materialName},' ',''),'%')
			</if>
			<if test="craftRequierName != null and craftRequierName != ''">
				AND REPLACE(a.craftDescript,' ','') LIKE CONCAT('%',REPLACE(#{craftRequierName},' ',''),'%')
			</if>
			<if test="status != null and status != ''">
	   			<if test="status != -1">
	   				AND b.status = #{status}
	   			</if>
	   			<if test="status == -1">
	   				AND b.status != 500108
	   			</if>
	   		</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (
					b.createdBy = #{createdBy.recordId}
					OR g.purchaser = #{createdBy.recordId}
					OR g.createdBy = #{createdBy.recordId}
					OR g.recordId IN (SELECT supplierId FROM md_supplier_user WHERE userId = #{createdBy.recordId} AND activeFlag = 1)
				)
	   		</if>
	   		<if test="sentTimeStartQr != null and sentTimeStartQr != '' and sentTimeEndQr != null and sentTimeEndQr != ''">
				  <![CDATA[ AND b.orderDate >= #{sentTimeStartQr} AND b.orderDate <= #{sentTimeEndQr} ]]>
	   		</if>
	   		<!-- 通知单号查下 -->
	   		<if test=" noticeNo != null and noticeNo != ''">
				AND REPLACE(e.no,' ','') LIKE CONCAT('%',REPLACE(#{noticeNo},' ',''),'%')
			</if>
			<if test=" type != null and type != ''">
				AND b.type = #{type}
			</if>
		 GROUP BY a.recordId
		ORDER BY a.createdDate DESC
	</select>
	<update id="updateCardTestSelected">
		UPDATE eg_carda SET testShelfSelected = 1 WHERE recordId = #{cardId}
	</update>
	
	<update id="updateCardModelSelected">
		UPDATE eg_carda SET mouldSelected = 1 WHERE recordId = #{cardId}
	</update>
	
	<update id="updateProofPath">
		UPDATE pu_purchasing
		SET proofPath = #{sourcePath}
		WHERE
			recordId = #{purchasingId}
		;
	</update>
	
	<select id="getShowPurList" resultType="Purchasing">
		SELECT
			a.recordId,
			a.`no`,
			a.orderDate,
			a.`status`,
			b.`no` AS "supNo",
			b.shortName AS "supShortName"
		FROM pu_purchasing a
		LEFT JOIN md_supplier b ON b.recordId = a.supplierId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 
		AND a.`status` = 500102 AND a.type = #{type}
		ORDER BY a.orderDate DESC
	</select>

	<select id="getCardPurList" resultType="Purchasing">
		SELECT
			b.recordId AS "recordId",
			b.sourceNo AS "sourceNo",
			SUM(IFNULL(LOCATE('MJ',c.`name`),0)) AS "mjCount",
			SUM(IFNULL(LOCATE('CSJ',c.`name`),0)) AS "csjCount",
		    b.type
		FROM pu_purchasing_detail a
		LEFT JOIN pu_purchasing b ON b.recordId = a.purchasingId
		LEFT JOIN md_material c ON c.recordId = a.materialId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND a.`status` <![CDATA[<>]]> 500108
		AND b.type = 2 AND FIND_IN_SET(b.sourceNo,#{recordId}) AND b.activeFlag = 1
		<if test="purId != null and purId != ''">
			AND b.recordId = #{purId}
		</if>
		GROUP BY b.recordId
	</select>

	<select id="getCardPurId" resultType="String">
		SELECT
		 	recordId
		FROM pu_purchasing
		WHERE sourceNo = #{cardId} AND activeFlag = 1 AND type = 2 AND `status` <![CDATA[<>]]> 500108
		<if test="no != null and no != ''">
			AND recordId IN
			(
				SELECT
					b.recordId
				FROM pu_purchasing_detail a
				LEFT JOIN pu_purchasing b ON b.recordId = a.purchasingId
				LEFT JOIN md_material c ON c.recordId = a.materialId
				WHERE b.sourceNo = #{cardId} AND REPLACE(c.name," ","") LIKE CONCAT('%',REPLACE(#{no}," ",""),'%')
				AND a.activeFlag = 1 AND b.type = 2 AND b.`status` <![CDATA[<>]]> 500108
			)
		</if>
		LIMIT 1
	</select>

	<select id="getCardPurData" resultType="Purchasing">
		SELECT
			recordId
		FROM pu_purchasing
		WHERE companyId = #{recordId} AND activeFlag = 1 AND type = 2
	</select>

	<select id="getcheckDetialStatus" resultType="Integer">
		SELECT
			COUNT(1)
		FROM pu_purchasing_detail
		WHERE purchasingId = #{recordId} AND activeFlag = 1 AND `status` NOT IN(500101,500108)
	</select>

	<select id="getDeatilMinStatus" resultType="Integer">
		SELECT
			MIN(`status`)
		FROM pu_purchasing_detail
		WHERE purchasingId = #{recordId} AND activeFlag = 1 AND `status` <![CDATA[<>]]> 500108
	</select>
	<select id="getPurchingData" resultType="Purchasing">
		SELECT
			a.sourceId,
			a.count
		FROM
		(
			SELECT
				sourcePurchaseOrdersId AS "sourceId",
				SUM(CASE `status` WHEN 3 THEN 1 ELSE 0 END) AS "count",
				tun
			FROM group_supply_chain_process
			WHERE activeFlag = 1
			AND FIND_IN_SET(sourcePurchaseOrdersId ,#{sourcePurchaseOrdersId})
			GROUP BY sourcePurchaseOrdersId,tun
			ORDER BY tun DESC
		) a
		GROUP BY a.sourceId
	</select>

	<update id="updateSourcePurchaseOrdersId">
		UPDATE pu_purchasing
		SET sourcePurchaseOrdersId = #{recordId}
		WHERE
			recordId = #{recordId}
	</update>

	<select id="getMaterialContract" resultType="String">
		SELECT sourcePurchaseOrdersId FROM sl_material_contract WHERE sourcePurchaseOrdersId = #{sourcePurchaseOrdersId} AND companyId = #{company.recordId}
	</select>

	<select id="getPurchasing" resultType="Purchasing">
		SELECT
			a.recordId,
			a.companyId AS "company.recordId",
			a. NO AS "no",
			a.supplierId AS "supplier.recordId",
			a.branchId AS "branch.recordId",
			a.orderDate AS "orderDate",
			a.deliveryDate AS "deliveryDate",
			a.deliveryWay AS "deliveryWay",
			a.currencyType AS "currencyType",
			a.payWay AS "payWay",
			a.payDays AS "payDays",
			a.prePayAmount AS "prePayAmount",
			a.prePayBalance AS "prePayBalance",
			a.taxDescript AS "taxDescript",
			a.purchaser AS "purchaser.recordId",
			a. STATUS AS "status",
			a.activeFlag AS "activeFlag",
			a.createdBy AS "createdBy.recordId",
			a.createdDate,
			a.lastUpdBy AS "lastUpdBy.recordId",
			a.lastUpdDate,
			a.remark AS "remark",
			a.deliveryPlace AS "deliveryPlace",
			a.qualityStd AS "qualityStd",
			a.assureDays AS "assureDays",
			a.cancelCause AS "cancelCause",
			a.period AS "period",
			a.supplierContractNo AS "supplierContractNo",
			a.paycause,
			a.type,
			a.proofPath,
			a.sourceNo,
			a.counterTrialCause,
			a.supplyChainId AS "supplyChain.recordId",
			a.paymentDays,
			a.radio,
			a.sourcePurchaseOrdersId,
			b. NAME AS "supplier.name",
			b.shortName AS "supplier.shortName",
			b. NO AS "supplier.no",
			su.userName AS "purchaser.userName",
			sn2. NO AS "sourceNoShow",
			dep. NAME AS "deptName",
			c. NAME AS "supplyChain.name",
			c. NO AS "supplyChain.no",
			c.shortName AS "supplyChain.shortName",
			d.
				VALUE
				AS "businessPaymentValue"
		FROM
			pu_purchasing a
				LEFT JOIN md_supplier b ON a.supplierId = b.recordId
				LEFT JOIN sm_user su ON a.purchaser = su.recordId
				LEFT JOIN pu_purchasing_detail ppu ON a.recordId = ppu.purchasingId
				LEFT JOIN md_material md ON md.recordId = ppu.materialId
				LEFT JOIN pu_purchasing_apply ppa ON FIND_IN_SET(
					ppa.recordId,
					ppu.purchRawId
				)
				LEFT JOIN sl_notification sn ON sn.recordId = ppa.notifiId
				LEFT JOIN eg_carda ec ON ec.recordId = a.sourceNo
				AND a.type = 2
				LEFT JOIN sl_notification sn2 ON sn2.processCardAId = ec.recordId
				AND sn2.activeFlag = 1
				LEFT JOIN md_employee me ON me.userId = a.purchaser
				AND me.activeFlag = 1
				LEFT JOIN md_department_relation_employee mdre ON mdre.employeeId = me.recordId
				AND mdre.activeFlag = 1
				AND mdre.`status` = 1
				LEFT JOIN md_department_relation_employee mdre2 ON mdre2.employeeId = me.recordId
				AND mdre2.activeFlag = 1
				AND mdre2.`status` = 2
				LEFT JOIN md_organization mo ON mo.recordId = mdre2.allId
				AND mo.activeFlag = 1
				LEFT JOIN md_department dep ON dep.recordId = IFNULL(mdre.allId, mo.deptId)
				LEFT JOIN md_supplier c ON c.recordId = a.supplyChainId
				LEFT JOIN md_dict_value d ON d.recordId = c.businessPaymentId
		WHERE
			a.companyId = #{companyId}
		  	AND a.activeFlag = 1
		  	AND a. STATUS != 500108
			AND a.sourcePurchaseOrdersId = #{sourcePurchaseOrdersId}
			AND a.type = 1
		GROUP BY
			a.recordId
	</select>

	<update id="modifyStatus">
		UPDATE pu_purchasing SET
			status = #{status}
		WHERE recordId = #{recordId}
	</update>

	<select id="getSupplyChainList" resultType="Purchasing">
		SELECT * FROM pu_purchasing WHERE sourcePurchaseOrdersId = #{sourcePurchaseOrdersId} AND activeFlag = 1
	</select>

	<select id="getPurchasings" resultType="Purchasing">
		SELECT
			a.recordId,
		    b.recordId AS "detailId"
		FROM
			pu_purchasing a
			LEFT JOIN pu_purchasing_detail b ON b.purchasingId = a.recordId
			AND b.activeFlag = 1
		WHERE
			a.companyId = #{companyId}
		  AND a.sourcePurchaseOrdersId = #{sourcePurchaseOrdersId}
	</select>

	<select id="getSupplychainScheduleTracePage" resultType="Purchasing">
		SELECT
			a.recordId,
			a.`no`,
			a.orderDate,
			e.`no` AS "materialNo",
			e.`name` AS "materialName",
			b.quantity AS "quantity",
		    a.sourcePurchaseOrdersId
		FROM
			pu_purchasing a
			LEFT JOIN pu_purchasing_detail b ON b.purchasingId = a.recordId
			AND b.activeFlag = 1
			LEFT JOIN sl_material_contract_detail c ON c.orgId = b.recordId
			AND c.activeFlag = 1
			LEFT JOIN md_material e ON e.recordId = b.materialId
		WHERE
			a.companyId = #{company.recordId}
		  	AND a.activeFlag = 1
		<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
			AND a.orderDate <![CDATA[>=]]> #{sentTimeStartQr}
		</if>
		<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
			AND a.orderDate <![CDATA[<=]]> #{sentTimeEndQr}
		</if>
		<if test="no != null and no != ''">
			AND REPLACE(a.`no`," ","") LIKE CONCAT('%', REPLACE(#{no}," ",""), '%')
		</if>
		GROUP BY
			a.recordId
	</select>

	<select id="getOrderScheduleList" resultType="Purchasing">
		SELECT
			a.`status`,
			b.`name` AS "supplyChain.name",
		    a.companyId
		FROM
			sl_material_contract a
			LEFT JOIN md_company b ON b.recordId = a.companyId AND b.activeFlag = 1
		WHERE
			a.sourcePurchaseOrdersId = #{sourcePurchaseOrdersId} AND a.activeFlag = 1
	</select>
	<select id="selectPurchInformation" resultType="Purchasing">
		SELECT
			d.`name` AS "supplierName",
			a.quantity AS "quantity",
			c.specification AS "specification",
			c.`name` AS "materialName",
		    a.remark AS "remark",
		    a.purchasingRemarks AS "purchasingRemarks"
		FROM
			pu_purchasing_detail a
			LEFT JOIN pu_purchasing b ON b.recordId = a.purchasingId
			LEFT JOIN md_material c ON c.recordId = a.materialId
			LEFT JOIN md_supplier d ON d.recordId = b.supplierId

			WHERE b.activeFlag = 1 AND b.recordId = #{recordId}
	</select>

	<select id="findPurchsingNo" resultType="Integer">
		SELECT COUNT(no) FROM sl_material_contract WHERE no = #{no} AND companyId = #{companyId}
	</select>

	<select id="getSupplierErpId" resultType="Supplier">
		SELECT
		    b.recordId,
			b.name,
			b.shortName,
			b.no,
			b.erpId,
			b.phone,
			b.address
		FROM
			pu_purchasing a
				LEFT JOIN md_supplier b ON a.supplierId = b.recordId
		WHERE a.recordId = #{recordId} AND a.companyId = #{company.recordId}
	</select>

	<select id="getPurchasingDayNum" resultType="Integer">
		SELECT
			COUNT(recordId)
		FROM
			pu_purchasing
		WHERE
			companyId = #{companyId}
		AND DATE_FORMAT(createdDate, "%Y-%m-%d") = #{no}
	</select>

	<select id="getPurchasingStatusNum" resultType="Integer">
		SELECT
			COUNT(1)
		FROM
			pu_purchasing
		WHERE
			recordId = #{recordId}
		  	AND `status` = 500110
	</select>

</mapper>