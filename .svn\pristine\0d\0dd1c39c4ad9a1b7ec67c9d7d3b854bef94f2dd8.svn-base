kybApp.controller('controlsCtrl', ['$rootScope', '$scope', 'upida', '$timeout','$filter','CommonUtil', function ($rootScope, $scope, upida, $timeout, $filter,CommonUtil) {
    $scope.$on('$viewContentLoaded', function () {
        MainCtrl.initAjax();
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });

    const vm = this;
    vm.right = {};

    // tabs控制
    vm.tabs = {
        cycleForm: {
            active: true
        },
        viewForm: {
            active: false
        },
        viewCapacityForm: {
            active: false
        },
        viewBottleneckProcessForm: {
            active: false
        },
        editBottleneckProcessForm: {
            active: false
        },
        viewHolidayForm:{
            active: false
        },
        editForm: {
            active: false,
            show: false
        }
    };

    vm.showEditForm = function () {
        vm.tabs.editForm.show = true;
        vm.tabs.editForm.active = true;
        $timeout(function () {
            if (vm.showFlag == "1"){
                vm.tabs.cycleForm.active = false;
            }else if (vm.showFlag == "2"){
                vm.tabs.viewForm.active = false;
            }
        });
    };

    // 隐藏编辑
    vm.hideEditForm = function () {
        vm.tabs.editForm.show = false;
        vm.tabs.editForm.active = false;
        $timeout(function () {
            if (vm.showFlag == "1"){
                vm.tabs.cycleForm.active = true;
            }else if (vm.showFlag == "2"){
                vm.tabs.viewForm.active = true;
            }
        });
        vm.loadData();
    };
    // 隐藏编辑
    vm.showCapacity = function () {
        loadDataCapacity();
        vm.tabs.editForm.show = false;
        vm.tabs.editForm.active = false;
        vm.tabs.viewForm.active = false;
        $timeout(function () {
            vm.tabs.viewCapacityForm.active = true;
        });
    };

    vm.showBottleneckProcess = function () {
        vm.tabs.editForm.show = false;
        vm.tabs.editForm.active = false;
        vm.tabs.viewForm.active = false;
        $timeout(function () {
            vm.tabs.viewBottleneckProcessForm.active = true;
        });
        vm.page.condition.push({
            name: "name",
            value: vm.nameQuery
        });
        vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url);
    };

    vm.changeShiftCheked = function (row, flag){
        if(!row)
        {
            return;
        }
        if(row.checked == 1){
            row.checked = '';
        }else{
            row.checked = 1;
        }
        // row.checked = !row.checked;
        if(flag == 2)
        {
            return;
        }
        if (flag == 1) {
            vm.getCheckListTwo();
        }
        // }else {
        //     vm.getCheckList();
        // }
    };

    // vm.recordList = [];
    vm.getCheckList = function(){
        vm.recordList = [];
        for(let use of vm.page.data.list){
            if (use.checked){
                vm.recordList.push(use);
            }
        }
    }

    vm.recordTwoList = [];
    vm.getCheckListTwo = function(){
        vm.recordTwoList = [];
        angular.forEach(vm.shiftDetailList,function(p){
            if(p.checked){
                vm.recordTwoList.push(p);
            }
        });
    }

    vm.shiftFlag = null;
    vm.batchSaveOpen = function(num)
    {
        if(!num)
        {
            return;
        }
        vm.shiftFlag = num;
        if(num == 1)
        {
            vm.recordList = [];
        }
        else if(num == 2)
        {
            vm.getCheckList()
            if(!vm.recordList || vm.recordList.length == 0)
            {
                vm.message = "请选择修改的班次";
                $('#static').modal();
                return;
            }
        }
        $('#batchSaveStatic').modal();
    }

    vm.addShiftRecord = function(){
        if(!vm.recordList || vm.recordList.length == 0)
        {
            vm.recordList = [];
        }
        let record = {};
        vm.recordList.splice(0,0,record);
    };

    vm.delShiftRecord = function(){
        if(!vm.recordList || vm.recordList.length == 0)
        {
            vm.recordList = [];
            return;
        }
        for(let i=0;i<vm.recordList.length;i++)
        {
            if(vm.recordList[i].checked)
            {
                vm.recordList.splice(i,1);
                i--;
            }
        }
    };

    vm.saveShiftRecord = function(){
        if(!vm.recordList || vm.recordList.length == 0)
        {
            vm.message = "没有保存的内容!";
            $('#static').modal();
            return;
        }
        let checkFlag = false;
        for(let record of vm.recordList)
        {
            if(!record.name)
            {
                vm.message = "班次名称不能为空";
                checkFlag = true;
                break;
            }
/*            if(!record || !record.startDate || !record.endDate)
            {
                vm.message = "开始时间和结束时间不能为空";
                checkFlag = true;
                break;
            }*/
        }
        if(checkFlag)
        {
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/saveShift",vm.recordList).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#batchSaveStatic').modal('hide');
                vm.recordList = [];
                vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            }
            MainCtrl.unblockUI();
        });
    };

    vm.deleteIds = null;
    vm.batchDeleteOpen = function()
    {
        if(!vm.recordList || vm.recordList.length == 0)
        {
            vm.message = "请选择要删除的班次";
            $('#static').modal();
            return;
        }
        vm.deleteIds = null;
        for(let record of vm.recordList)
        {
            if(vm.deleteIds)
            {
                vm.deleteIds = vm.deleteIds +","+ record.recordId;
            }
            else
            {
                vm.deleteIds = record.recordId;
            }
        }
        vm.message = "您确认要删除班次？";
        $('#batcDeleteStatic').modal();
    }

    vm.batchDelte = function()
    {
        if(!vm.deleteIds)
        {
            vm.message = "没有要删除的内容！";
            $('#static').modal();
            return;
        }
        let obj = {};
        obj.recordId = vm.deleteIds;
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/delShift",obj).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#batcDeleteStatic').modal('hide');
                vm.recordList = [];
                vm.deleteIds = null;
                vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            }
            MainCtrl.unblockUI();
        });
    }

    vm.shiftDetailList = [];
    vm.shiftList = [];
    vm.productionLineList = [];
    vm.processList = [];
    vm.showShiftDetail = function()
    {
        MainCtrl.blockUI({
            animate: true,
        });
        // 请求分页数据
        upida.post("production/controls/getShiftDetailList").then(function(data) {
            vm.shiftList = data.shiftList;
            vm.shiftDetailList = data.shiftDetailList;
            vm.productionLineList = data.productionLineList;
            vm.processList = data.processList;
            MainCtrl.unblockUI();
        });
    }

    vm.shiftDetailFlag = null;
    vm.recordTwoList
    vm.batchSaveTwoOpen = function(num)
    {
        if(!num)
        {
            return;
        }
        vm.shiftDetailFlag = num;
        if(num == 1)
        {
            vm.recordTwoList = [];
        }
        else if(num == 2)
        {
            if(!vm.recordTwoList || vm.recordTwoList.length == 0)
            {
                vm.message = "请选择修改的班次明细";
                $('#static').modal();
                return;
            }
        }
        $('#batchSaveTwoStatic').modal();
    }

    vm.addShiftDetailRecord = function(){
        if(!vm.recordTwoList || vm.recordTwoList.length == 0)
        {
            vm.recordList = [];
        }
        let record = {};
        vm.recordTwoList.splice(0,0,record);
    };

    vm.delShiftDetailRecord = function(){
        if(!vm.recordTwoList || vm.recordTwoList.length == 0)
        {
            vm.recordList = [];
            return;
        }
        for(let i=0;i<vm.recordTwoList.length;i++)
        {
            if(vm.recordTwoList[i].checked)
            {
                vm.recordTwoList.splice(i,1);
                i--;
            }
        }
    };

    vm.saveShiftDetailRecord = function(){
        if(!vm.recordTwoList || vm.recordTwoList.length == 0)
        {
            vm.message = "没有保存的内容!";
            $('#static').modal();
            return;
        }
        let checkFlag = false;
        for(let record of vm.recordTwoList)
        {
            if(!record.processId)
            {
                vm.message = "工序不能为空";
                checkFlag = true;
                break;
            }
            if(!record.maxNum)
            {
                vm.message = "款数不能为空";
                checkFlag = true;
                break;
            }
            if(!record.productArea)
            {
                vm.message = "每天面积不能为空";
                checkFlag = true;
                break;
            }
        }
        if(checkFlag)
        {
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/saveShiftDetail",vm.recordTwoList).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#batchSaveTwoStatic').modal('hide');
                vm.recordTwoList = [];
                vm.showShiftDetail(vm.shift);
            }
            MainCtrl.unblockUI();
        });
    };

    vm.deleteTwoIds = null;
    vm.batchDeleteTwoOpen = function()
    {
        if(!vm.recordTwoList || vm.recordTwoList.length == 0)
        {
            vm.message = "请选择要删除的班次明细";
            $('#static').modal();
            return;
        }
        vm.deleteTwoIds = null;
        for(let record of vm.recordTwoList)
        {
            if(vm.deleteTwoIds)
            {
                vm.deleteTwoIds = vm.deleteTwoIds +","+ record.recordId;
            }
            else
            {
                vm.deleteTwoIds = record.recordId;
            }
        }
        vm.message = "您确认要删除班次明细？";
        $('#batcDeleteTwoStatic').modal();
    }

    vm.batchDelteTwo = function()
    {
        if(!vm.deleteTwoIds)
        {
            vm.message = "没有要删除的内容！";
            $('#static').modal();
            return;
        }
        let obj = {};
        obj.recordId = vm.deleteTwoIds;
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/delShiftDetail",obj).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#batcDeleteTwoStatic').modal('hide');
                vm.recordTwoList = [];
                vm.deleteTwoIds = null;
                vm.showShiftDetail(vm.shift);
            }
            MainCtrl.unblockUI();
        });
    }

    // 页面显示数量改变
    vm.pageSizeChange = function() {
        vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url);
    };

    vm.doPage = function(page, pageSize, total)
    {
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        vm.init(page, pageSize, vm.page.condition, vm.page.url);
    };

    vm.page = {};
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    // 字典项分页数据
    vm.page.data = {};
    vm.page.data.list = {};
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "production/controls/shiftPage";
    vm.page.condition = []; // 条件

    vm.init = function(no, size, condition, url) {
        MainCtrl.blockUI({
            animate: true,
        });
        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;
        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condition);

        // 请求分页数据
        upida.post(url, reqData).then(function(result) {
            if(url == vm.dataPage.url)
            {
                handleShowData(result);
            }else if (url == vm.projectPage.url){
                handProjectScheding(result);
            }else {
                var data = {};
                // 如果结果为空
                if(typeof result === 'undefined' || typeof result.list === 'undefined') {

                    data.pageNo = 1;
                    data.pageSize = 10;
                    data.list = [];
                    data.startCount = 0;
                    data.endCount = 0;
                } else {
                    data = result;
                    // 计算开始数
                    data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                    // 计算结束数
                    data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
                }
                if(url == vm.page.url)
                {
                    vm.page.data = data;
                }
            }
            MainCtrl.unblockUI();
        });
    };

    vm.craftManage = function () {
        $('#craftManage').modal();
    };

    vm.craftEntity = {};
    vm.addCraft = function () {
        vm.craftEntity = {};
        $('#addCraft').modal();
    };

    vm.editCraft = function (row) {
        vm.process = {};
        vm.craftEntity = JSON.parse(JSON.stringify(row));
        if (vm.craftEntity.craftId == 1){
            vm.craftEntity.itemName = "流程工序";
        }else if (vm.craftEntity.craftId == 2){
            vm.craftEntity.itemName = "资料类型";
        }else if (vm.craftEntity.craftId == 3){
            vm.craftEntityitemName = "订单类型";
        }else if (vm.craftEntity.craftId == 4){
            vm.craftEntity.itemName = "产品类型";
        }
        $('#addCraft').modal();
    };

    vm.deleteCraft = function (row) {
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/deleteSetupColumn", row).then(function (data) {
            if (data == "success") {
                vm.message = "操作成功";
            } else {
                vm.message = "操作失败，请刷新重试";
            }
            $('#static').modal();
            loadColumnList();
            MainCtrl.unblockUI();
        });
    };

    vm.process = {};
    vm.saveCycleSetupColumn = function () {
        if (!vm.craftEntity.craftId){
            vm.message = "请选择工艺！";
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        vm.craftEntity.setupId = vm.cycleSetup.recordId;
        // 进行保存
        upida.post("production/controls/saveCycleSetupColumn", vm.craftEntity).then(function (data) {
            if (data == "success") {
                vm.message = "保存成功";
                $('#addCraft').modal('hide');
            } else if (data == "exist") {
                vm.message = "工艺已存在";
            } else {
                vm.message = "保存失败";
            }
            $('#static').modal();
            loadColumnList();
            MainCtrl.unblockUI();
        });
    };

    vm.cycleSetup = {};
    vm.itemList = [
        {recordId: "1", name: "流程工序"},
        {recordId: "2", name: "资料类型"},
        {recordId: "3", name: "订单类型"},
        {recordId: "4", name: "产品类型"}
    ];
    vm.showFlag = "1";
    vm.processList = [];
    vm.showCycleSetup = function (row, flag) {
        vm.showFlag = flag;
        vm.columnList = [];
        vm.operatorList = [];
        vm.cycleSetup = row;
        vm.showEditForm();
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("production/controls/load/deaiData").then(function (data) {
            vm.processList = data.processList;
            vm.userSetList = data.userSetList;
            vm.processTechnologyList = data.processTechnologyList;
            loadColumnList();
            loadDeailList();
            MainCtrl.unblockUI();
        });
    };

    vm.deailList = [];
    function loadDeailList() {
        vm.deailList = [];
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("production/controls/getDeailList", vm.cycleSetup).then(function (data) {
            vm.deailList = data;
            vm.deailList = vm.deailList.filter(item => (item.stepId == 1 && item.mustNotHave == 1) || item.stepId == 2);
            for (let i=0;i<vm.deailList.length;i++){
                for (let j=0;j<vm.processTechnologyList.length;j++){
                    if (vm.deailList[i].craftId == vm.processTechnologyList[j].recordId
                        && vm.deailList[i].craftType == vm.processTechnologyList[j].type){
                        vm.deailList[i].itemName = vm.processTechnologyList[j].processName;
                        vm.deailList[i].column = JSON.parse(JSON.stringify(vm.processTechnologyList[j]));
                    }
                    if (vm.deailList[i].bindCraftId == vm.processTechnologyList[j].recordId
                        && vm.deailList[i].bindCraftType == vm.processTechnologyList[j].type){
                        vm.deailList[i].bindColumn = JSON.parse(JSON.stringify(vm.processTechnologyList[j]));
                    }
                    if (vm.deailList[i].principal){
                        vm.deailList[i].operatorList = vm.deailList[i].principal.split(",");
                    }
                }
            }
            vm.updateSetDays();
            MainCtrl.unblockUI();
        });
    };

    vm.columnList = [];
    function loadColumnList() {
        vm.columnList = [];
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("production/controls/getColumList", vm.cycleSetup).then(function (data) {
            vm.columnList = data;
            for (let i=0;i<vm.columnList.length;i++){
                if (vm.columnList[i].craftId == 1){
                    vm.columnList[i].itemName = "流程工序";
                }else if (vm.columnList[i].craftId == 2){
                    vm.columnList[i].itemName = "资料类型";
                }else if (vm.columnList[i].craftId == 3){
                    vm.columnList[i].itemName = "订单类型";
                }else if (vm.columnList[i].craftId == 4){
                    vm.columnList[i].itemName = "产品类型";
                }
            }
            MainCtrl.unblockUI();
        });
    };

    vm.deailRecordList = [];
    // 修改行
    vm.deailEditFlag = "";
    vm.editDeail = function () {
        vm.deailEditFlag = "1";
        vm.deailRecordList = getDeailSaveList();
        $('#addDeail').modal();
    };

    // 增加行
    vm.addDeail = function () {
        vm.deailEditFlag = "";
        vm.deailRecordList = [];
        $('#addDeail').modal();
    };

    function getDeailSaveList() {
        const deailSavaList = [];
        for (let i = 0; i < vm.deailList.length; i++) {
            if (vm.deailList[i].checked == 1) {
                const entity = vm.deailList[i];
                deailSavaList.push(entity);
            }
        }
        return deailSavaList;
    };

    vm.addDeailRecord = function () {
        if (!vm.deailRecordList || vm.deailRecordList.length == 0) {
            vm.deailRecordList = [];
        }
        let record = {};
        record.setupId = vm.cycleSetup.recordId;
        record.days = 0;
        record.hours = 0;
        vm.deailRecordList.splice(0, 0, record);
    };

    vm.delDeailRecord = function () {
        if (!vm.deailRecordList || vm.deailRecordList.length == 0) {
            vm.deailRecordList = [];
            return;
        }
        for (let i = 0; i < vm.deailRecordList.length; i++) {
            if (vm.deailRecordList[i].checked) {
                vm.deailRecordList.splice(i, 1);
                break;
            }
        }
    };

    vm.saveDeail = function () {
        if (!vm.deailRecordList || vm.deailRecordList.length == 0) {
            vm.message = "没有保存的内容!";
            $('#static').modal();
            return;
        }
        for (let record of vm.deailRecordList) {
            if (!record.days && !record.hours) {
                vm.message = "请录入时间！";
                $('#static').modal();
                return;
            }
            if (isNaN(record.days) || isNaN(record.hours)){
                vm.message = "时间请录入数字！";
                $('#static').modal();
                return;
            }
        }

        for (let i=0; i<vm.deailRecordList.length; i++){
            vm.deailRecordList[i].principal = "";
            if (vm.deailRecordList[i].operatorList ){
                for (let j =0;j<vm.deailRecordList[i].operatorList.length ; j++){
                    vm.deailRecordList[i].principal += vm.deailRecordList[i].principal ? ","+vm.deailRecordList[i].operatorList[j]:vm.deailRecordList[i].operatorList[j];
                }
            }
        }

        // 验证数据不能重复
        for (let i=0;i<vm.deailRecordList.length;i++){
            let deailRecord = vm.deailRecordList[i];
            for (let j=0;j<vm.deailRecordList.length;j++){
                if (i != j){
                    let deailRecordTwo = vm.deailRecordList[j];
                    if (deailRecord.craftId == deailRecordTwo.craftId
                        && deailRecord.cardType == deailRecordTwo.cardType
                        && deailRecord.orderType == deailRecordTwo.orderType
                        && deailRecord.productType == deailRecordTwo.productType
                        && deailRecord.craftType == deailRecordTwo.craftType
                        && deailRecord.bindType == deailRecordTwo.bindType
                        && deailRecord.stepId == deailRecordTwo.stepId
                        && deailRecord.name == deailRecordTwo.name
                        && deailRecord.bindCraftId == deailRecordTwo.bindCraftId
                        && deailRecord.bindCraftType == deailRecordTwo.bindCraftType){
                        vm.message = "不能配置重复数据！";
                        $('#static').modal();
                        return;
                    }
                }
            }
            for (let j=0;j<vm.deailList.length;j++){
                let deailRecordTwo = vm.deailList[j];
                if (deailRecord.recordId != deailRecordTwo.recordId){
                    if (deailRecord.craftId == deailRecordTwo.craftId
                        && deailRecord.cardType == deailRecordTwo.cardType
                        && deailRecord.orderType == deailRecordTwo.orderType
                        && deailRecord.productType == deailRecordTwo.productType
                        && deailRecord.craftType == deailRecordTwo.craftType
                        && deailRecord.bindType == deailRecordTwo.bindType
                        && deailRecord.stepId == deailRecordTwo.stepId
                        && deailRecord.name == deailRecordTwo.name
                        && deailRecord.bindCraftId == deailRecordTwo.bindCraftId
                        && deailRecord.bindCraftType == deailRecordTwo.bindCraftType){
                        vm.message = "不能配置重复数据！";
                        $('#static').modal();
                        return;
                    }
                }
            }
        }
        let setDays = vm.getSetDays();
        if (setDays > vm.cycleSetup.nomalDays){
            if (vm.cycleControl == 2){
                vm.message = "配置时间不能大于标准时间";
                $('#static').modal();
                $('#addDeail').modal('hide');
                loadDeailList();
                return;
            }
        }
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/saveDeail", vm.deailRecordList).then(function (data) {
            loadDeailList();
            vm.message = data.message;
            $('#static').modal();
            $('#addDeail').modal('hide');
            vm.deailRecordList = [];
            MainCtrl.unblockUI();
        });
    };

    vm.getSetDays = function () {
        // 累积时间
        let setDays = 0;
        for (let record of vm.deailList) {
            setDays += (record.days?Number(record.days):0)*24+(record.hours?Number(record.hours):0);
        }
        // 小时换算成天数
        setDays = (setDays / 24).toFixed(2);
        return setDays;
    };

    vm.updateSetDays = function () {
        const setUp = {};
        setUp.setDays = vm.getSetDays();
        setUp.recordId = vm.cycleSetup.recordId;
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/updateSetDays", setUp).then(function (data) {
            MainCtrl.unblockUI();
        });
    };

    vm.upSortDeail = function (row) {
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/upSortDeail", row).then(function (data) {
            if (data == "success") {
                loadDeailList();
            } else {
                vm.message = "不能往上调了，上面无数据";
                $('#static').modal();
            }
            MainCtrl.unblockUI();
        });
    };

    vm.downSortDeail = function (row) {
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/downSortDeail", row).then(function (data) {
            if (data == "success") {
                loadDeailList();
            } else {
                vm.message = "不能往下调了，下面无数据";
                $('#static').modal();
            }
            MainCtrl.unblockUI();
        });
    };

    vm.delDeail = function () {
        const deailSavaList = getDeailSaveList();
        if (!deailSavaList || deailSavaList.length == 0) {
            vm.message = "没有保存的内容!";
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/delDeail", deailSavaList).then(function (data) {
            if (data == "success") {
                vm.message = "操作成功";
            } else {
                vm.message = "操作失败，请刷新重试";
            }
            $('#static').modal();
            loadDeailList();
            MainCtrl.unblockUI();
        });
    };

    vm.closeAddDeail = function () {
        loadDeailList();
        $('#addDeail').modal('hide');
    };

    vm.upSortColum = function (row) {
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/upSortColum", row).then(function (data) {
            if (data == "success") {
                loadColumnList();
            } else {
                vm.message = "不能往上调了，上面无数据";
                $('#static').modal();
            }
            MainCtrl.unblockUI();
        });
    };

    vm.downSortColum = function (row) {
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/downSortColum", row).then(function (data) {
            if (data == "success") {
                loadColumnList();
            } else {
                vm.message = "不能往下调了，下面无数据";
                $('#static').modal();
            }
            MainCtrl.unblockUI();
        });
    };

    vm.upSort = function (row) {
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/upSort", row).then(function (data) {
            if (data == "success") {
                vm.getCycleList();
            } else {
                vm.message = "不能往上调了，上面无数据";
                $('#static').modal();
            }
            MainCtrl.unblockUI();
        });
    };

    vm.downSort = function (row) {
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/downSort", row).then(function (data) {
            if (data == "success") {
                vm.getCycleList();
            } else {
                vm.message = "不能往下调了，下面无数据";
                $('#static').modal();
            }
            MainCtrl.unblockUI();
        });
    };

    // 修改行
    vm.editFlag = "";
    vm.editCycleSetUpModal = function () {
        vm.editFlag = "1";
        vm.recordList = getSaveList();
        $('#batchHandleStatic').modal();
    };

    // 增加行
    vm.batchHandleOpen = function () {
        vm.editFlag = "";
        vm.recordList = [];
        $('#batchHandleStatic').modal();
    };

    vm.editStatus = "";
    // 批量生效、失效
    vm.updateStatusModal = function (status) {
        let flag = true;
        flag = canUseNum(flag);
        if (!flag) {
            vm.editStatus = status;
            $('#updateStatusModal').modal();
        }
    };

    vm.updateStatus = function () {
        const savaList = getSaveList();
        if (!savaList || savaList.length == 0) {
            vm.message = "没有保存的内容!";
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/updateStatus", savaList).then(function (data) {
            if (data == "success") {
                vm.message = "操作成功";
            } else {
                vm.message = "操作失败，请刷新重试";
            }
            $('#static').modal();
            vm.getCycleList();
            $('#updateStatusModal').modal('hide');
            MainCtrl.unblockUI();
        });
    };

    // 批量删除
    vm.deleteCycleSetUpModal = function () {
        let flag = true;
        flag = canUseNum(flag);
        if (!flag) {
            $('#deleteCycleSetUpModal').modal();
        }
    };

    vm.deleteCycleSetUp = function () {
        const savaList = getSaveList();
        if (!savaList || savaList.length == 0) {
            vm.message = "没有处理的内容!";
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/deleteCycleSetUp", savaList).then(function (data) {
            if (data == "success") {
                vm.message = "操作成功";
            } else {
                vm.message = "操作失败，请刷新重试";
            }
            $('#static').modal();
            vm.loadData();
            $('#deleteCycleSetUpModal').modal('hide');
            MainCtrl.unblockUI();
        });
    };

    function getSaveList() {
        const savaList = [];
        for (let i = 0; i < vm.cycleList.length; i++) {
            if (vm.cycleList[i].checked == 1) {
                const entity = JSON.parse(JSON.stringify(vm.cycleList[i]));
                if (vm.editStatus) {
                    entity.status = vm.editStatus;
                }
                savaList.push(entity);
            }
        }
        return savaList;
    };

    function canUseNum(flag) {
        for (let i = 0; i < vm.cycleList.length; i++) {
            if (vm.cycleList[i].checked == 1) {
                flag = false;
                break;
            }
        }
        if (flag) {
            vm.message = "请选择要操作的内容!";
            $('#static').modal();
        }
        return flag;
    };

    vm.editChecked = function (row) {
        let num = 0;
        for (let i = 0; i < vm.cycleList.length; i++) {
            if (vm.cycleList[i].checked == 1) {
                num++;
            }
        }
        row.checked = "";
        if (num <= 1) {
            $('#updateStatusModal').modal('hide');
            $('#deleteCycleSetUpModal').modal('hide');
        }
    };

    vm.recordList = [];
    vm.addRecord = function () {
        if (!vm.recordList || vm.recordList.length == 0) {
            vm.recordList = [];
        }
        let record = {amount:0,amountPer:0,capacity:0,capacityPer:0,orderAmount:0,orderAmountPer:0,sampleAmount:0,sampleAmountPer:0};
        vm.recordList.splice(0, 0, record);
    };

    vm.changeCheked = function (row, flag) {
        if (flag == 1) {
            for (let i = 0; i < vm.recordList.length; i++) {
                vm.recordList[i].checked = "";
            }
        } else if (flag == 2) {
            for (let i = 0; i < vm.deailRecordList.length; i++) {
                vm.deailRecordList[i].checked = "";
            }
        }
        if (row.checked == 1) {
            row.checked = "";
        } else {
            row.checked = 1;
        }
    };

    vm.deleteRecord = function () {
        if (!vm.recordList || vm.recordList.length == 0) {
            vm.recordList = [];
            return;
        }
        for (let i = 0; i < vm.recordList.length; i++) {
            if (vm.recordList[i].checked) {
                vm.recordList.splice(i, 1);
                break;
            }
        }
    };

    vm.saveRecord = function () {
        if (!vm.recordList || vm.recordList.length == 0) {
            vm.message = "没有保存的内容!";
            $('#static').modal();
            return;
        }
        for (let record of vm.recordList) {
            record.configId = this.configId;
            if (!record.name) {
                vm.message = "请录入规则名称！";
                $('#static').modal();
                return;
            }
            if (!record.countMethod) {
                vm.message = "请选择周期类型！";
                $('#static').modal();
                return;
            }
            if (record.nomalDays && isNaN(record.nomalDays)){
                vm.message = "标准天数请录入数字！";
                $('#static').modal();
                return;
            }
        }
        MainCtrl.blockUI({
            animate: true,
        });
        if (this.versionDate){
            for (let i=0;i<vm.recordList.length;i++){
                vm.recordList[i].versionDate = this.versionDate;
            }
        }
        // 进行保存
        upida.post("production/controls/saveCycleSetup", vm.recordList).then(function (data) {
            vm.message = data.message;
            $('#static').modal();
            $('#batchHandleStatic').modal('hide');
            vm.recordList = [];
            vm.loadData();
            MainCtrl.unblockUI();
        });
    };

    vm.checkedRecord = {};
    vm.cycleRemark = "";
    vm.setRemark = function (row) {
        vm.checkedRecord = row;
        vm.cycleRemark = row.remark;
        $('#cycleRemark').modal();
    };

    vm.setRemarkVal = function () {
        vm.checkedRecord.remark = vm.cycleRemark;
    };

    vm.checkdBindType = {};
    vm.setBindType = function (row) {
        vm.checkdBindType = row;
        $('#bindType').modal();
    };

    vm.setBindTypeVal = function () {
        vm.checkdBindType.bindCraftType = null;
        if (vm.checkdBindType.bindType == 1){
            vm.checkdBindType.bindColumn = {};
        }else if(vm.checkdBindType.bindType == 2 && vm.checkdBindType.bindColumn){
            vm.checkdBindType.bindCraftId = vm.checkdBindType.bindColumn.recordId;
            vm.checkdBindType.bindCraftType = vm.checkdBindType.bindColumn.type;
        }else {
            vm.checkdBindType.bindCraftId = null;
        }
    };

    vm.checkdColumn = {};
    vm.setColumn = function (row) {
        vm.checkdColumn = row;
        $('#cycleColumn').modal();
    };

    vm.setColumnVal = function () {
        vm.checkdColumn.craftId = vm.checkdColumn.column.recordId;
        vm.checkdColumn.craftType = vm.checkdColumn.column.type;
    };

    vm.createVersion = function () {
        const query = {};
        query.versionDate = this.versionDate;
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("production/controls/createVersion", query).then(function(data){
            vm.loadData();
            MainCtrl.unblockUI();
        });
    };

    vm.cycleList = [];
    vm.getCycleList = function () {
        if (!this.versionDate){
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        const query = {};
        query.versionDate = this.versionDate;
        upida.post("production/controls/getCycleSetupList", query).then(function(data){
            vm.cycleList = data;
            MainCtrl.unblockUI();
        });
    };

    vm.versionCount = 0;
    vm.loadVersionCount = function () {
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("production/controls/getVersionCount", vm.versionDate).then(function(data) {
            vm.versionCount = data;
            vm.getCycleList();
            MainCtrl.unblockUI();
        });
    };

    vm.templateList = [];
    vm.loadTemplateList = function () {
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("production/controls/getTemplateProcess", vm.versionDate).then(function(data) {
            vm.templateList = data;
            MainCtrl.unblockUI();
        });
    };

    vm.cycleControl = "";
    vm.getCycleControl = function () {
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("production/controls/getCycleControl").then(function(data) {
            vm.cycleControl = data;
            MainCtrl.unblockUI();
        });
    };

    vm.versionDate = "";
    // 加载数据方法
    vm.cycleTypeList = [];
    vm.versionList = [];
    vm.capacityVersionList = [];
    vm.newVersionDate = "";
    vm.processTwoList = [];
    vm.loadData = function () {
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("production/controls/load/data").then(function(data) {
            vm.cycleTypeList = data.cycleTypeList;
            vm.userSetList = data.userSetList;
            vm.versionList = data.versionList;
            vm.processTwoList = data.processList;
            if (vm.versionList && vm.versionList.length > 0)
            {
                for (let i=0;i<vm.versionList.length;i++){
                    if (vm.versionList[i].versionDate){
                        vm.versionList[i].versionDateStr = vm.versionList[i].versionDate.split(" ")[0];
                    }
                    if (vm.versionList[i].principal){
                        vm.versionList[i].operatorList = vm.versionList[i].principal.split(",");
                    }
                }
                vm.versionDate = vm.versionList[0].versionDate;
                vm.newVersionDate = vm.versionDate;
                vm.loadVersionCount();
            }
            vm.loadTemplateList();
            vm.getCycleControl();
            MainCtrl.unblockUI();
        });
    };

    // 加载权限
    function loadRight() {
        MainCtrl.blockUI({
            animate: true,
        });
        upida.get("common/rightall?prefix=production:controls").then(function (data) {
            vm.right.view = data.view;
            vm.right.edit = data.edit;
            vm.right.manage = data.manage;
            MainCtrl.unblockUI();
            vm.loadData();
        });
    };

    vm.updateVersion = function (){
        if (!vm.capaRecordId){
            vm.message = "请刷新重试";
            $('#static').modal();
            loadDataCapacity();
            return;
        }
        if(vm.capacityAuction<0 || isNaN(vm.capacityAuction)){
            vm.message = "产能面积有误";
            $('#static').modal();
            loadDataCapacity();
            return;
        }
        if(vm.totalAmountAuction<0 || isNaN(vm.totalAmountAuction)){
            vm.message = "批量日总款数有误";
            $('#static').modal();
            loadDataCapacity();
            return;
        }
        if(vm.sampleAmountAuction<0 || isNaN(vm.sampleAmountAuction)){
            vm.message = "样品日总款数有误";
            $('#static').modal();
            loadDataCapacity();
            return;
        }
        if(vm.orderAmountAuction<0 || isNaN(vm.orderAmountAuction)){
            vm.message = "新单日总款数有误";
            $('#static').modal();
            loadDataCapacity();
            return;
        }
        if(vm.exposureCapacity<0 || isNaN(vm.exposureCapacity)){
            vm.message = "曝光产能有误";
            $('#static').modal();
            loadDataCapacity();
            return;
        }
        if(vm.screenCapacity<0 || isNaN(vm.screenCapacity)){
            vm.message = "丝印产能有误";
            $('#static').modal();
            loadDataCapacity();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        const capa = {};
        capa.amount = vm.totalAmountAuction;
        capa.sampleAmount = vm.sampleAmountAuction;
        capa.orderAmount = vm.orderAmountAuction;
        capa.exposureCapacity = vm.exposureCapacity;
        capa.screenCapacity = vm.screenCapacity;
        capa.capacity = vm.capacityAuction;
        capa.recordId = vm.capaRecordId;
        upida.post("production/capacity/updateVersion", capa).then(function(data) {
            loadDataCapacity();
            MainCtrl.unblockUI();
        });
    };

    vm.versionCapacityDate = "";
    vm.branchList = [];
    vm.departList = [];
    function loadDataCapacity(){
        vm.capacityVersionList = [];
        MainCtrl.blockUI({
            animate: true,
        });
        upida.get("production/capacity/loadData").then(function (result) {
            vm.capacityVersionList = result.capacityVersionList;
            vm.branchList = result.branchList;
            vm.departList = result.departList;
            if (vm.capacityVersionList && vm.capacityVersionList.length > 0)
            {
                for (let i=0;i<vm.capacityVersionList.length;i++){
                    if (vm.capacityVersionList[i].versionDate){
                        vm.capacityVersionList[i].versionDateStr = vm.capacityVersionList[i].versionDate.split(" ")[0];
                    }
                }
                if (!vm.capaRecordId){
                    vm.capaRecordId = result.capacityVersionList[0].recordId;
                }
                vm.loadCapacityVersionCount();
            }
            MainCtrl.unblockUI();
        });
    }
    vm.capacityDetailList = [];
    vm.versionCapacityCount = 0;
    vm.activeFlag = 0;
    vm.capaRecordId = "";
    vm.loadCapacityVersionCount = function () {
        vm.capacityAuction =  0;
        vm.totalAmountAuction = 0;
        vm.orderAmountAuction = 0;
        vm.sampleAmountAuction = 0;
        vm.exposureCapacity = 0;
        vm.screenCapacity = 0;
        for (let i=0;i<vm.capacityVersionList.length;i++){
            if (vm.capaRecordId == vm.capacityVersionList[i].recordId){
                if (vm.capacityVersionList[i].capacity){
                    vm.capacityAuction =  Number(vm.capacityVersionList[i].capacity);
                }
                if (vm.capacityVersionList[i].amount){
                    vm.totalAmountAuction = Number(vm.capacityVersionList[i].amount);
                }
                if (vm.capacityVersionList[i].orderAmount){
                    vm.orderAmountAuction = Number(vm.capacityVersionList[i].orderAmount);
                }
                if (vm.capacityVersionList[i].sampleAmount){
                    vm.sampleAmountAuction = Number(vm.capacityVersionList[i].sampleAmount);
                }
                if (vm.capacityVersionList[i].exposureCapacity){
                    vm.exposureCapacity = Number(vm.capacityVersionList[i].exposureCapacity);
                }
                if (vm.capacityVersionList[i].screenCapacity){
                    vm.screenCapacity = Number(vm.capacityVersionList[i].screenCapacity);
                }
                vm.versionCapacityDate = vm.capacityVersionList[i].versionDate;
                break;
            }
        }
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("production/capacity/getVersionCount", vm.versionCapacityDate).then(function(data) {
            vm.versionCapacityCount = data;
            upida.get("production/capacity/getVersionDetail?versionDate="+vm.versionCapacityDate).then(function(data) {
                vm.capacityDetailList = data.capacityRecord;
                vm.activeFlag = data.capacityVersion.status;
                MainCtrl.unblockUI();
            });
        });
    };
    vm.amountPerAdjust = 100;
    vm.amountAdjust = 0;
    vm.capacityPerAdjust = 100;
    vm.capacityAdjust = 0;
    vm.capacityHandleModal = function () {
        vm.editFlag = "1";
        if(vm.capacityDetailList){
            for (let i = 0; i < vm.capacityDetailList.length; i++) {
                let item = vm.capacityDetailList[i];
                for (let j = 0; j < vm.departList.length; j++)
                {
                    if (vm.departList[j].recordId == item.departId)
                    {
                        vm.recordList.push(item)
                    }
                }
            }
            /*vm.recordList = JSON.parse(JSON.stringify(vm.capacityDetailList));*/
            vm.calAdjust(vm.capacityDetailList);
        }else{
            vm.recordList = [];
        }
        $('#capacityHandleStatic').modal();
    }
    vm.getCompanyDetail = function(row){
        upida.get("production/capacity/getCompanyDetail?recordId="+row.bindErpComId).then(function(data) {
            row.customerList = data.customerList;
            row.customer = null;
            row.departId = null;
            MainCtrl.unblockUI();
        });
    }
    vm.saveCapacityRecord = function () {
        if (!vm.recordList || vm.recordList.length == 0) {
            vm.message = "没有保存的内容!";
            $('#static').modal();
            return;
        }
        for (let record of vm.recordList) {
            record.configId = this.configId;
            if(!record.customer&&!record.departId){
                vm.message = "请选择部门或客户名称！";
                $('#static').modal();
                return;
            }
        }
/*        if(vm.amountPerAdjust<0){
            vm.message = "批量日款数比例有误";
            $('#static').modal();
            return;
        }else if(vm.amountPerAdjust==0){
            vm.message = "可调批量日节款数比例为0，请调整比例";
            $('#static').modal();
            return;
        }else if(vm.amountAdjust<0){
            vm.message = "批量日款数有误";
            $('#static').modal();
            return;
        }else if(vm.amountAdjust==0){
            vm.message = "可调批量日节款数为0，请调整比例";
            $('#static').modal();
            return;
        }else if(vm.capacityPerAdjust<0){
            vm.message = "产能面积比例有误";
            $('#static').modal();
            return;
        }else if(vm.capacityPerAdjust=0){
            vm.message = "可调节产面积能为0，请调整比例";
            $('#static').modal();
            return;
        }else if(vm.capacityAdjust<0){
            vm.message = "产能面积有误";
            $('#static').modal();
            return;
        }else if(vm.capacityAdjust==0){
            vm.message = "可调节产面积为0，请调整比例";
            $('#static').modal();
            return;
        }else if(vm.sampleAmountPerAdjust<0){
            vm.message = "样品日款数比例有误";
            $('#static').modal();
            return;
        }else if(vm.sampleAmountPerAdjust=0){
            vm.message = "可调样品日节款数比例为0，请调整比例";
            $('#static').modal();
            return;
        }else if(vm.sampleAmountAdjust<0){
            vm.message = "样品日款数有误";
            $('#static').modal();
            return;
        }else if(vm.sampleAmountAdjust==0){
            vm.message = "可调样品日节款数为0，请调整比例";
            $('#static').modal();
            return;
        }else if(vm.orderAmountPerAdjust<0){
            vm.message = "新单日款数比例有误";
            $('#static').modal();
            return;
        }else if(vm.orderAmountPerAdjust=0){
            vm.message = "可调新单日节款数比例为0，请调整比例";
            $('#static').modal();
            return;
        }else if(vm.orderAmountAdjust<0){
            vm.message = "新单日款数有误";
            $('#static').modal();
            return;
        }else if(vm.orderAmountAdjust==0){
            vm.message = "可调新单日节款数为0，请调整比例";
            $('#static').modal();
            return;
        }*/
        MainCtrl.blockUI({
            animate: true,
        });
        if (this.versionCapacityDate){
            for (let i=0;i<vm.recordList.length;i++){
                vm.recordList[i].versionDate = this.versionCapacityDate;
            }
        }
        upida.post("production/capacity/capacityCount",vm.recordList).then(function (result) {
            if(result == 'success'){
                // 进行保存
                upida.post("production/capacity/saveCapacity", vm.recordList).then(function (data) {
                    if(data=="success"){
                        vm.message = '保存成功!';
                        $('#static').modal();
                        $('#capacityHandleStatic').modal('hide');
                        vm.recordList = [];
                        loadDataCapacity();
                    }else{
                        vm.message = data;
                        $('#static').modal();
                        loadDataCapacity();
                    }
                    MainCtrl.unblockUI();
                });
            }else{
                vm.message = result;
                $('#static').modal();
                loadDataCapacity();
                MainCtrl.unblockUI();
            }
        });

    };
    vm.deleteCapacityRecord = function () {
        if (!vm.recordList || vm.recordList.length == 0) {
            vm.recordList = [];
            return;
        }
        let delRecord = [];
        for (let i = 0; i < vm.recordList.length; i++) {
            if (vm.recordList[i].checked == 1) {
                delRecord.push(vm.recordList[i]);
            }
        }
        upida.post("production/capacity/deleteCapacity", delRecord).then(function (data) {
            if(data=='success' || data == "无保存记录!"){
                for (let i = 0; i < vm.recordList.length; i++) {
                    if (vm.recordList[i].checked) {
                        vm.recordList.splice(i, 1);
                    }
                }
                vm.calAdjust(vm.recordList);
            }
        });
        loadDataCapacity();
    };

    vm.createCapacityVersion = function () {
        const query = {};
        query.versionDate = this.versionCapacityDate;
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("production/capacity/createVersion", query).then(function(data){
            loadDataCapacity();
            MainCtrl.unblockUI();
        });
    };
    vm.calAdjust = function(list){
        vm.amountAdjust = vm.totalAmountAuction;
        vm.sampleAmountAdjust = vm.sampleAmountAuction;
        vm.orderAmountAdjust = vm.orderAmountAuction;
        vm.capacityAdjust = vm.capacityAuction;
        vm.capacityPerAdjust = 100;
        vm.amountPerAdjust = 100;
        vm.sampleAmountPerAdjust = 100;
        vm.orderAmountPerAdjust = 100;
        let sampleAmountPerCount = 0;
        let sampleAmountCount = 0;
        let amountPerCount = 0;
        let amountCount = 0;
        let orderAmountPerCount = 0;
        let orderAmountCount = 0;
        let capacityCount = 0;
        let capacityPerCount = 0;
        for (let record of list) {
            if(record.amountPer==null || record.amountPer=='') record.amountPer=0
            if(record.amount==null || record.amount=='') record.amount=0
            if(record.sampleAmountPer==null || record.sampleAmountPer=='') record.sampleAmountPer=0
            if(record.sampleAmount==null || record.sampleAmount=='') record.sampleAmount=0
            if(record.orderAmountPer==null || record.orderAmountPer=='') record.orderAmountPer=0
            if(record.orderAmount==null || record.orderAmount=='') record.orderAmount=0
            if(record.capacityPer==null || record.capacityPer=='') record.capacityPer=0
            if(record.capacity==null || record.capacity=='') record.capacity=0
            amountPerCount+=record.amountPer;
            amountCount+=record.amount;
            sampleAmountPerCount+=record.sampleAmountPer;
            sampleAmountCount+=record.sampleAmount;
            orderAmountPerCount+=record.orderAmountPer;
            orderAmountCount+=record.orderAmount;
            capacityPerCount+=record.capacityPer;
            capacityCount+=record.capacity;
        }
        vm.amountPerAdjust = Math.round((100-amountPerCount)*100)/100;
        vm.sampleAmountAdjust = Math.round(vm.sampleAmountAuction-sampleAmountCount);
        vm.sampleAmountPerAdjust = Math.round((100-sampleAmountPerCount)*100)/100;
        vm.amountAdjust = Math.round(vm.totalAmountAuction-amountCount);
        vm.orderAmountPerAdjust = Math.round((100-orderAmountPerCount)*100)/100;
        vm.orderAmountAdjust = Math.round(vm.orderAmountAuction-orderAmountCount);
        vm.capacityPerAdjust = Math.round((100-capacityPerCount)*100)/100;
        vm.capacityAdjust = Math.round(vm.capacityAuction-capacityCount);
    }
    vm.updateAmountPer = function(row,type){
        if(type==1){
            if(row.amountPer>0){
                row.amount = Math.round(vm.totalAmountAuction*row.amountPer/100);
            }
        }else if(type==2){
            if(row.sampleAmountPer>0){
                row.sampleAmount = Math.round(vm.sampleAmountAuction*row.sampleAmountPer/100);
            }
        }else if(type==3){
            if(row.orderAmountPer>0){
                row.orderAmount = Math.round(vm.orderAmountAuction*row.orderAmountPer/100);
            }
        }
        vm.calAdjust(vm.recordList,type);
    };
    vm.updateAmount = function(row,type){
        if(type==1){
            if(row.amount>0 && vm.totalAmountAuction>0){
                row.amountPer = Math.round((row.amount/vm.totalAmountAuction)*100*100)/100;
            }
        }else if(type==2){
            if(row.sampleAmount>0 && vm.sampleAmountAuction>0){
                row.sampleAmountPer = Math.round((row.sampleAmount/vm.sampleAmountAuction)*100*100)/100;
            }
        }else if(type==3){
            if(row.orderAmount>0 && vm.orderAmountAuction>0){
                row.orderAmountPer = Math.round((row.orderAmount/vm.orderAmountAuction)*100*100)/100;
            }
        }
        vm.calAdjust(vm.recordList,type);
    };
    vm.updateCapacityPer = function(row){
        if(row.capacityPer>0){
            row.capacity = Math.round(vm.capacityAuction*row.capacityPer/100);
        }
        vm.calAdjust(vm.recordList);
    };
    vm.updateCapacity = function(row){
        if(row.capacity>0 && vm.capacityAuction>0){
            row.capacityPer = Math.round((row.capacity/vm.capacityAuction)*100*100)/100;
        }
        vm.calAdjust(vm.recordList);
    };
    vm.departCustom = function(index,row){
        if(index==1 && row.departId){
            row.customer = null;
            row.bindErpComId = null;
        }else if(index==2 && row.customer){
            row.departId = null;
        }
    }
    vm.days =[];
    vm.year = new Date().getFullYear();
    vm.yearHoliday = new Date().getFullYear();
    vm.years = '';
    vm.holidayData = function(){
        vm.days =[];
        for (let i = 1; i < 32; i++) {
            vm.days.push({key:i,value:i});
        }
        upida.get("production/individual/getMonth").then(function(result){
            vm.year = result.year;
            vm.yearHoliday = result.year;
            vm.years = result.yearsAround;
            MainCtrl.unblockUI();
        });
        upida.post("production/controls/getCycleHoliday?year="+vm.yearHoliday).then(function (result) {
            vm.holiday = result;
            vm.type = result.vacationType;
            if(vm.type==1){
                vm.day = result.vacationValue;
            }else if(vm.type == 2){
                vm.week = result.vacationValue;
            }
            vm.calendarInit();
        });
    }
    vm.holiday = {};
    vm.holiday.holidayList = [];
    vm.set = {};
    vm.cycleSet = function(type,model){
        vm.parameter = [];
        vm.param = {};
        if(vm.model == 1){
            if(vm.holiday.vacationSet==vm.holiday.day){
                vm.message = "盘点日和休息日不能为同一天";
                $('#static').modal();
                vm.holidayData();
                return;
            }
        }
        MainCtrl.blockUI({
            animate: true,
        });
        if (confirm("是否确认要批量设置?")==true){
            if(type==1){
                vm.param.jianPin = "inventorySet";
                vm.param.parameterName = "月盘点日设置";
                vm.param.parameterValue = vm.holiday.inventorySet;
                vm.parameter.push(vm.param);
            }else if(type==2){
                vm.param.jianPin = "vacationSet";
                vm.param.parameterName = "休息日设置";
                if(model==1){
                    vm.param.parameterValue = model+'-'+vm.day;
                }else{
                    vm.param.parameterValue = model+'-'+vm.week;
                }
                vm.parameter.push(vm.param);
            }
            upida.post("production/controls/addByParam?year="+vm.yearHoliday,vm.parameter ).then(function (result) {
                vm.message = "保存成功";
                $('#static').modal();
                vm.holidayData();
                MainCtrl.unblockUI();
            });
        }else{
            MainCtrl.unblockUI();
        }
    }
    //日历加载
    /* config object */
    vm.todayDate = '';
    vm.today = {};
    vm.events = [];
    vm.calendarInit=function(){
        /* event source that contains custom events on the scope */
        vm.events = [];
        if(vm.holiday.holidayList){
            for( let holiday of vm.holiday.holidayList){
                let title = '';
                if(holiday.type==1){
                    title = '节假日'+(holiday.remark?':'+holiday.remark:'');
                }else if(holiday.type==2){
                    title = '盘点日'+(holiday.remark?':'+holiday.remark:'');
                }else if(holiday.type==3){
                    title = '休息日'+(holiday.remark?':'+holiday.remark:'');
                }
                vm.events.push({id: holiday.recordId,title: title,start: new Date(holiday.holidayDate)});
            }
        }
        for (const single in vm.holiday.holidays) {
            vm.events.push({id: -1,title: single,start: new Date(vm.holiday.holidays[single]), color: "#ad2121"});
        }
        $scope.uiConfig = {
            calendar:{
                height: 450,
                defaultDate: new Date(),
                editable: false,
                header: {
                    left: '',
                    center: 'title',
                    right: 'today prev,next'
                },
                buttonText: {
                    today: '今天'
                },
                timeFormat: { // for event elements
                    'default': ''
                },
                events: vm.events,
                dayClick: function(date, allDay, jsEvent, view) {
                    vm.today = {};
                    date = new Date(date);
                    let dateFormate = date.getFullYear() + '-' + ((date.getMonth() + 1)>=10?date.getMonth()+1:"0"+(date.getMonth()+1)) + '-' + (date.getDate()>=10?date.getDate():"0"+date.getDate());
                    vm.todayDate = dateFormate;
                    if(vm.holiday.holidayList){
                        for( let holiday of vm.holiday.holidayList){
                            if(dateFormate == holiday.holidayDate.split(' ')[0]) {
                                vm.today = holiday;
                                break;
                            }
                        }
                        if(vm.today.recordId){
                            vm.deleteHolidayRecord(vm.today);
                        }else{
                            vm.holidayAdd(dateFormate);
                        }
                    }else{
                        vm.holidayAdd(dateFormate);
                    }
                },
                viewRender: function(view, element) {

                    // 当视图渲染时，我们需要得到当前视图的年份和月份
                    currentYear = view.intervalStart.format("YYYY"); // 当前年份

                    if(vm.yearHoliday!==currentYear){
                        vm.yearHoliday = currentYear;


                        upida.post("production/controls/getCycleHoliday?year="+vm.yearHoliday).then(function (result) {
                            vm.events = [];
                            vm.holiday = result;
                            vm.type = result.vacationType;
                            if(vm.type==1){
                                vm.day = result.vacationValue;
                            }else if(vm.type == 2){
                                vm.week = result.vacationValue;
                            }
                            if(vm.holiday.holidayList){
                                for( let holiday of vm.holiday.holidayList){
                                    let title = '';
                                    if(holiday.type==1){
                                        title = '节假日'+(holiday.remark?':'+holiday.remark:'');
                                    }else if(holiday.type==2){
                                        title = '盘点日'+(holiday.remark?':'+holiday.remark:'');
                                    }else if(holiday.type==3){
                                        title = '休息日'+(holiday.remark?':'+holiday.remark:'');
                                    }
                                    vm.events.push({id: holiday.recordId,title: title,start: new Date(holiday.holidayDate)});
                                }
                            }
                            for (const single in vm.holiday.holidays) {
                                vm.events.push({id: -1,title: single,start: new Date(vm.holiday.holidays[single]), color: "#ad2121"});
                            }
                            $("#holidayAddStatic").modal('hide');
                            // console.log(vm.today)
                            $scope.uiConfig.calendar.defaultDate=view.intervalStart;
                            $scope.uiConfig.calendar.events=vm.events;
                            // console.log(vm.events)

                        });
                    }

                },

            },

        };
    }
    vm.getCycleHoliday = function(today){
        // console.log("getCycleHoliday:"+today);
        upida.post("production/controls/getCycleHoliday?year="+vm.yearHoliday).then(function (result) {
            vm.events = [];
            vm.holiday = result;
            vm.type = result.vacationType;
            if(vm.type==1){
                vm.day = result.vacationValue;
            }else if(vm.type == 2){
                vm.week = result.vacationValue;
            }
            if(vm.holiday.holidayList){
                for( let holiday of vm.holiday.holidayList){
                    let title = '';
                    if(holiday.type==1){
                        title = '节假日'+(holiday.remark?':'+holiday.remark:'');
                    }else if(holiday.type==2){
                        title = '盘点日'+(holiday.remark?':'+holiday.remark:'');
                    }else if(holiday.type==3){
                        title = '休息日'+(holiday.remark?':'+holiday.remark:'');
                    }
                    vm.events.push({id: holiday.recordId,title: title,start: new Date(holiday.holidayDate)});
                }
            }
            for (const single in vm.holiday.holidays) {
                vm.events.push({id: -1,title: single,start: new Date(vm.holiday.holidays[single]), color: "#ad2121"});
            }
            $("#holidayAddStatic").modal('hide');
            $scope.uiConfig.calendar.defaultDate=today.holidayDate;
            $scope.uiConfig.calendar.events=vm.events;
        });
    }
    vm.deleteHolidayRecord = function(today){
        MainCtrl.blockUI({
            animate: true,
        });
        if (confirm("确认取消该日期设置")==true){
            upida.post("production/controls/deleteCycleHoliday", [today]).then(function (data) {
                if(data=='success'){
                    vm.getCycleHoliday(today);
                } else {
                    vm.message = data;
                    $('#static').modal();
                }
                MainCtrl.unblockUI();
            });
        }else{
            MainCtrl.unblockUI();
        }
    }

    vm.saveHolidayRecord = function(){
        upida.post("production/controls/saveCycleHoliday", [vm.edit]).then(function (data) {
            if (data == 'success') {
                vm.getCycleHoliday({holidayDate:vm.edit.holidayDate});
            } else {
                vm.message = data;
                $('#static').modal();
            }
            MainCtrl.unblockUI();
        });
    }
    vm.holidayList = [];
    vm.month = ['01','02','03','04','05','06','07','08','09','10','11','12'];
    vm.weeks = [{key:'2',value:"周一"}, {key:'3',value:"周二"} ,{key:'4',value:"周三"} ,{key:'5',value:"周四"} ,{key:'6',value:"周五"} ,{key:'7',value:"周六"} ,{key:'1',value:"周日"}];
    vm.sFtvs = [
        {key: "0101", value: "元旦节"},{key: "n0101", value: "春节"},
        {key: "0405", value: "清明节"},{key: "0501", value: "劳动节"},
        {key: "0505", value: "端午节"},{key: "0815", value: "中秋节"},
        {key: "1001", value: "国庆节"}
        // {key: "0101", value: "元旦节"}, {key: "0202", value: "湿地日"},
        // {key: "0214", value: "情人节"}, {key: "0308", value: "妇女节"},
        // {key: "0312", value: "植树节"}, {key: "0315", value: "消费者权益日"},
        // {key: "0401", value: "愚人节"}, {key: "0422", value: "地球日"},
        // {key: "0501", value: "劳动节"}, {key: "0504", value: "青年节"},
        // {key: "0512", value: "护士节"}, {key: "0518", value: "博物馆日"},
        // {key: "0520", value: "母亲节"}, {key: "0601", value: "儿童节"},
        // {key: "0623", value: "奥林匹克日"}, {key: "0630", value: "父亲节"},
        // {key: "0701", value: "建党节"}, {key: "0801", value: "建军节"},
        // {key: "0903", value: "抗战胜利日"}, {key: "0910", value: "教师节"},
        // {key: "1001", value: "国庆节"}, {key: "1201", value: "艾滋病日"},
        // {key: "1224", value: "平安夜"}, {key: "1225", value: "圣诞节"},
        //
        // {key: "n0100", value: "除夕"}, {key: "n0101", value: "春节"},
        // {key: "n0115", value: "元宵节"}, {key: "0505", value: "端午节"},
        // {key: "n0707", value: "七夕节"}, {key: "0715", value: "中元节"},
        // {key: "n0815", value: "中秋节"}, {key: "n0909", value: "重阳节"},
        // {key: "n1015", value: "下元节"}, {key: "n1208", value: "腊八节"},
        // {key: "n1223", value: "小年"}
    ];
    vm.day = 1;
    vm.week = vm.weeks[0].key;
    vm.sFtv;
    vm.type = "1";
    vm.edit = {};
    vm.holidayAdd = function(date) {
        vm.edit = { holidayDate : date, type : "1"}
        $("#holidayAddStatic").modal();
    }
    vm.getYear = function(){
        let date = new Date();
        let dateFomate = vm.yearHoliday + '-' + ((date.getMonth() + 1)>=10?date.getMonth()+1:"0"+(date.getMonth()+1)) + '-' + '01';
        vm.getCycleHoliday({holidayDate:dateFomate})
    },
    vm.holidayCount = function() {
        upida.post("production/controls/getHolidayDateCount",vm.year).then(function (data) {
            vm.holidayList = data;
            $("#holidayStatic").modal();
        })
    }
    vm.getHoliday = function(){
        upida.post("production/controls/getHoliday?year="+vm.yearHoliday+"&holiday="+vm.sFtv).then(function (data) {
            $scope.uiConfig.calendar.defaultDate=data;
        })
    }

    // 增加行
    vm.addingProcessOpen = function () {
        vm.deleteProcess = {};
        vm.editFlag = "";
        vm.processsList = [];
        $('#batchProcessStatic').modal();
    };

    vm.processsList = [];
    vm.addProcess = function () {
        if (!vm.processsList || vm.processsList.length == 0) {
            vm.processsList = [];
        }
        let record = {amount:0,amountPer:0,capacity:0,capacityPer:0,orderAmount:0,orderAmountPer:0,sampleAmount:0,sampleAmountPer:0,processIdList:[],craftIdList :[],principalList :[]};
        vm.processsList.splice(0, 0, record);
    };
    vm.deleteProcessOpen = function () {
        if (!vm.processsList || vm.processsList.length == 0) {
            vm.processsList = [];
            return;
        }
        for (let i = 0; i < vm.processsList.length; i++) {
            if (vm.processsList[i].checked) {
                vm.processsList.splice(i, 1);
                break;
            }
        }
    };

    vm.saveProcess = function () {
        if (vm.deleteProcess){
            vm.processsList.push(vm.deleteProcess);
        }
        if (!vm.processsList || vm.processsList.length == 0) {
            vm.message = "没有保存的内容!";
            $('#static').modal();
            return;
        }
        for (let i=0; i<vm.processsList.length; i++){
            vm.processsList[i].processId = "";
            vm.processsList[i].craftId = "";
            vm.processsList[i].principal = "";
            if (vm.processsList[i].processIdList){
                for (let j =0;j<vm.processsList[i].processIdList.length ; j++){
                    vm.processsList[i].processId += vm.processsList[i].processId ? ","+vm.processsList[i].processIdList[j]:vm.processsList[i].processIdList[j];
                }
            }
            if (vm.processsList[i].craftIdList ){
                for (let j =0;j<vm.processsList[i].craftIdList.length ; j++){
                    vm.processsList[i].craftId += vm.processsList[i].craftId ? ","+vm.processsList[i].craftIdList[j]:vm.processsList[i].craftIdList[j];
                }
            }
            if (vm.processsList[i].principalList ){
                for (let j =0;j<vm.processsList[i].principalList.length ; j++){
                    vm.processsList[i].principal += vm.processsList[i].principal ? ","+vm.processsList[i].principalList[j]:vm.processsList[i].principalList[j];
                }
            }
        }
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/processList", vm.processsList).then(function (data) {
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#batchProcessStatic').modal('hide');
                vm.processsList = [];
                vm.processData(vm.shift);
            }
            MainCtrl.unblockUI();
        });
    };

    vm.deleteProcess = {};
    vm.deleProcessList = [];
    vm.delProcessOpen = function(row)
    {
        if(!row)
        {
            return;
        }
        vm.deleteProcess = row;
        vm.message = "请确认删除!";
        $('#delCustomerRemarks').modal();
    }

    vm.deleteCustomerSale = function()
    {
        if(!vm.deleteProcess)
        {
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/delTemplateOpen",vm.deleteProcess).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#delCustomerRemarks').modal('hide');
                vm.deleProcessList = [];
                vm.processData(vm.shift);
            }
            MainCtrl.unblockUI();
        });
    }

    vm.processSetList = [];
    vm.technologySetList = [];
    vm.userSetList = [];
    vm.processDataList = [];
    vm.processData = function() {
        vm.craft = {};
        upida.post("production/controls/processDataList" ,vm.craft).then(function (data) {
            vm.processSetList = data.processSetList;
            vm.technologySetList = data.technologySetList;
            vm.userSetList = data.userSetList;
            vm.processDataList = data.processDataList;
            for (let i=0;i<vm.processDataList.length;i++){
                vm.processDataList[i].processIdList = [];
                vm.processDataList[i].craftIdList = [];
                vm.processDataList[i].principalList = [];
                if (vm.processDataList[i].processId){
                    vm.processDataList[i].processIdList = vm.processDataList[i].processId.split(",");
                }
                if (vm.processDataList[i].craftId){
                    vm.processDataList[i].craftIdList = vm.processDataList[i].craftId.split(",");
                }
                if (vm.processDataList[i].principal){
                    vm.processDataList[i].principalList = vm.processDataList[i].principal.split(",");
                }
            }
        });
    }

    vm.updingProcessOpen = function (row) {
        vm.editFlag = "";
        vm.deleteProcess = row;
        $('#batchProcessStatic').modal();
    };

    //查询默认参数值
    vm.parameterList = [];
    vm.paramterDate = function() {
        vm.craft = {};
        upida.post("production/controls/parameterList" ,vm.craft).then(function (data) {
            vm.parameterList = data.parameterList;
        });

    }

    //删除默认参数值
    vm.deleteParam = {};
    vm.deleteParamsList = [];
    vm.delParamter = function(row)
    {
        if(!row)
        {
            return;
        }
        vm.deleteProcess = row;
        vm.message = "请确认删除!";
        $('#delParam').modal();
    };

    vm.deleteParamDate = function()
    {
        if(!vm.deleteProcess)
        {
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/parameterDelete",vm.deleteProcess).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#delParam').modal('hide');
                vm.paramterDate();
            }
            MainCtrl.unblockUI();
        });
    };
    vm.addParamter = function(){
        vm.paramterD = {};
        $('#addParamter').modal();
    };


    //修改添加
    vm.paramterD = {};
    vm.paramterInseUp = function(){
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("production/controls/paramterInseUp", vm.paramterD).then(function (data) {
            vm.message = "保存成功";
            $('#static').modal();
            MainCtrl.unblockUI();
            vm.paramterDate();
        });
    };
    vm.paramterUp = function (row) {
        vm.paramterD = row;
        $('#addParamter').modal();

    },
    vm.bottlenck = {};
    vm.addTimeSection = function(item){
        vm.bottlenck = item;
        vm.timeSectionList = [];
        if(vm.bottlenck && vm.bottlenck.sectionList && vm.bottlenck.sectionList.length > 0)
        {
            vm.timeSectionList = vm.bottlenck.sectionList;
        }
        $('#showAddSection').modal();
    },

    vm.timeSectionList = [];
    vm.addTimeSectionData = function(){
        var flag = true;
        angular.forEach(vm.timeSectionList,function(p){
            if(!p.startHour){
                vm.message = "请先填写上一个的开始小时再添加";
                $('#static').modal();
                flag = false;
                return;
            }
            if (p.startHour > 24)
            {
                vm.message = "开始时数不能超过24小时";
                $('#static').modal();
                flag = false;
                return;
            }
            if(!p.startMinutes && p.startMinutes != '')
            {
                vm.message = "请先填写上一个的开始分钟再添加";
                $('#static').modal();
                flag = false;
                return;
            }
            if(p.startMinutes > 60)
            {
                vm.message = "开始分钟数不能超过60分钟";
                $('#static').modal();
                flag = false;
                return;
            }
            if(!p.endHour)
            {
                vm.message = "请先填写上一个的结束小时再添加";
                $('#static').modal();
                flag = false;
                return;
            }
            if(p.endHour > 24)
            {
                vm.message = "结束时数不能超过24小时";
                $('#static').modal();
                flag = false;
                return;
            }
            if(!p.endMinutes && p.endMinutes != '')
            {
                vm.message = "请先填写上一个的结束分钟再添加";
                $('#static').modal();
                flag = false;
                return;
            }
            if(p.endMinutes > 60)
            {
                vm.message = "结束分钟数不能超过60分钟";
                $('#static').modal();
                flag = false;
                return;
            }
            if(!p.area)
            {
                vm.message = "请先填写上一个的面积再添加";
                $('#static').modal();
                flag = false;
                return;
            }
            if(!p.count)
            {
                vm.message = "请先填写上一个的款数再添加";
                $('#static').modal();
                flag = false;
                return;
            }
        });
        if (vm.timeSectionList && vm.timeSectionList.length > 1)
        {
           let index = vm.timeSectionList.length - 2;
           let indexTwo = vm.timeSectionList.length - 1;
           let endHour = (Number(vm.timeSectionList[index].endHour) + Number(vm.timeSectionList[index].endMinutes / 60));
           let endHourTwo = (Number(vm.timeSectionList[indexTwo].startHour) + Number(vm.timeSectionList[indexTwo].startMinutes / 60));
            if (endHour > endHourTwo){
                vm.message = "时间不能在上一笔包含的时间内";
                $('#static').modal();
                flag = false;
                return;
            }
        }
        if(flag)
        {
            var obj = {};
            obj.startHour = "";
            obj.startMinutes = "";
            obj.endHour = "";
            obj.endMinutes = "";
            obj.area = "";
            obj.count = "";
            vm.timeSectionList.push(obj);
        }
    };

    vm.delTimeSection = function(index){
        if(vm.timeSectionList[index].recordId)
        {
            const query = {};
            query.recordId = vm.timeSectionList[index].recordId;
            upida.post("production/controls/delTimeSection", query).then(function(data) {
                if(data == "success")
                {
                    vm.message = "删除成功!";
                    $('#static').modal();
                    vm.timeSectionList.splice(index,1);
                }
            });
        }else{
            vm.timeSectionList.splice(index,1);
        }
        vm.showShiftDetail();
    };

    vm.saveTimeSection = function(){
        if(vm.timeSectionList.length <= 0)
        {
            vm.message = "未填写工序生产区间，请添加工序生产区间信息";
            $('#static').modal();
            return;
        }
        for(let i = 0; i < vm.timeSectionList.length; i++){
            vm.timeSectionList[i].bottleneckProcessId = vm.bottlenck.recordId;
            vm.timeSectionList[i].parameterValue = (Number(vm.timeSectionList[i].startHour) + Number(vm.timeSectionList[i].startMinutes / 60));
        }
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("production/controls/saveTimeSection", vm.timeSectionList).then(function(data) {
            if(data)
            {
                if(data.result == "fail")
                {
                    vm.message = data.message;
                    $('#static').modal();
                }
                else
                {
                    vm.message = "保存成功";
                    $('#static').modal();
                    $('#showAddSection').modal('hide');
                }
            }
            vm.showShiftDetail()
            MainCtrl.unblockUI();
        });
    }
    // 分页数据
    vm.projectPage = {};
    // 显示数据大小
    vm.projectPage.pageSizeOptions = [5, 10, 31, 50];
    // 项分页数据
    vm.projectPage.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.projectPage.pageSize = 31;
    vm.projectPage.pageNo = 1;
    vm.projectPage.url = "production/controls/projectSheduingList";
    vm.projectPage.condition = []; // 条件


    vm.showProjectSheduing = function(date,item)
    {
        vm.checkTaskList = [];
        for(let detail of item)
        {
            if (detail.taskList && detail.taskList.length > 0){
                for(let task of detail.taskList)
                {
                    if (date === task.showArrangePlanDate.substring(5,10))
                    {
                        vm.checkTaskList.push(task);
                    }
                }
            }
        }
        vm.showProjectList = vm.checkTaskList;
        $('#projectSheduingWindow').modal();
    }
    //获取工程排产监控
    vm.projectSheduingList = function()
    {
        // vm.projectPage.pageNo = 1;
        if(vm.time.start) {
            vm.projectPage.condition.push({
                name: "sentTimeStartQr",
                value: vm.time.start.valueOf()
            });
        }
        if(vm.time.end) {
            vm.projectPage.condition.push({
                name: "sentTimeEndQr",
                value: vm.time.end.valueOf()
            });
        }
        vm.projectPage.condition.push({
            name: "splotchQuery",
            value: "1"
        });
        vm.init(vm.projectPage.pageNo, vm.projectPage.pageSize, vm.projectPage.condition, vm.projectPage.url);
    }

    // 时间范围的选项
    vm.rangeOptions = {
        // format: "YYYY-MM-DD",
        startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
    };

    vm.time = {
        start: {},
        end: {}
    };

    vm.initDate = function(date) {
        if(date == "") {
            vm.rangeOptions = {
                format: "YYYY-MM-DD",
                startDate: new Date(vm.query.sentTimeStartQr.value),
                minDate: new Date(new Date(vm.query.sentTimeEndQr.value).setFullYear(new Date(vm.query.sentTimeEndQr.value).getFullYear() - 5))
            };
            vm.time = {
                start: vm.rangeOptions.startDate,
                end: vm.rangeOptions.minDate
            }
        }
    };

    vm.rangeOptionsTwo = {
        // format: "YYYY-MM-DD",
        startDate: new Date(),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5))),
        endDate: new Date((new Date).setDate(((new Date).getDate() + 15)))
    };

    vm.timeTwo = {
        start: {},
        end: {}
    };

    vm.initDateTwo = function(date) {
        if(date == "") {
            vm.rangeOptionsTwo = {
                format: "YYYY-MM-DD",
                startDate: new Date(vm.dataPage.condition.sentTimeStartQr.value),
                minDate: new Date(new Date(vm.dataPage.condition.sentTimeEndQr.value).setFullYear(new Date(vm.dataPage.condition.sentTimeEndQr.value).getFullYear() - 5))
            };
            vm.timeTwo = {
                start: vm.rangeOptionsTwo.startDate,
                end: vm.rangeOptionsTwo.minDate
            }
        }
    };

    // 分页数据
    vm.dataPage = {};
    // 显示数据大小
    vm.dataPage.pageSizeOptions = [5, 10, 31, 50];
    // 项分页数据
    vm.dataPage.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.dataPage.pageSize = 31;
    vm.dataPage.pageNo = 1;
    vm.dataPage.url = "production/controls/productionRowPool";
    vm.dataPage.condition = []; // 条件

    vm.doDataPage = function (page, pageSize, total) {
        vm.dataPage.pageNo = page;
        vm.dataPage.pageSize = pageSize;
        vm.init(page, pageSize, vm.dataPage.condition, vm.dataPage.url);
    };

    // 页面显示数量改变
    vm.pageSizeChangeForData = function() {
        vm.init(1, vm.dataPage.pageSize, vm.dataPage.condition, vm.dataPage.url);
    };

    vm.doDataPageTwo = function (page, pageSize, total) {
        vm.projectPage.pageNo = page;
        vm.projectPage.pageSize = pageSize;
        vm.init(page, pageSize, vm.projectPage.condition, vm.projectPage.url);
    };

    // 页面显示数量改变
    vm.pageSizeChangeForDataTwo = function() {
        vm.init(1, vm.projectPage.pageSize, vm.projectPage.condition, vm.projectPage.url);
    };

    vm.processQuery = null;
    vm.getDataList = function()
    {
        vm.dataPage.pageNo = 1;
        vm.dataPage.condition = [];
        if(vm.timeTwo.start) {
            vm.dataPage.condition.push({
                name: "sentTimeStartQr",
                value: vm.timeTwo.start.valueOf()
            });
        }
        if(vm.timeTwo.end) {
            vm.dataPage.condition.push({
                name: "sentTimeEndQr",
                value: vm.timeTwo.end.valueOf()
            });
        }
        // 工序组
        if (vm.processQuery && vm.processQuery !== "") {
            let processNames = null;
            angular.forEach(vm.processQuery,function(p){
                if(processNames)
                {
                    processNames = processNames +","+ p.processName;
                }
                else
                {
                    processNames = p.processName;
                }
            });
            vm.dataPage.condition.push({
                name: "processName",
                value: processNames
        });
        }
        vm.dataPage.condition.push({
            name: "splotchQuery",
            value: "1"
        });
        vm.init(vm.dataPage.pageNo, vm.dataPage.pageSize, vm.dataPage.condition, vm.dataPage.url);
    }

    vm.showColor = function(index){
        index = index + 1;
        if(index % 2 == 0) {
            return true;
        }
        else {
            return false;
        }
    };

    vm.showColorTwo = function(index){
        index = index + 1;
        if(index % 2 != 0) {
            return true;
        }
        else {
            return false;
        }
    };

    vm.showProcessName = function(index)
    {
        var oldProcessName = vm.showProcessList[index].processName;
        var count = oldProcessName.length / 2;
        var processNames = [];
        if(count > 1) {
            processNames.push(oldProcessName.substring(0,oldProcessName.length / 2));
            processNames.push(oldProcessName.substring(oldProcessName.length / 2,oldProcessName.length));
        }
        else {
            processNames.push(oldProcessName);
        }
        return processNames;
    };

    vm.showProcessList = [];
    function handleShowData (result)
    {
        vm.dataPage.data = {};
        if(!result || !result.page ||!result.page.list || !result.useList || !result.bottleneckProcessList)
        {
            return;
        }
        let processList = [];
        if(vm.processQuery && vm.processQuery.length > 0)
        {
            for(let one of vm.processQuery)
            {
                for(let two of vm.processTwoList)
                {
                    if(one.recordId == two.recordId)
                    {
                        processList.push(two);
                        break;
                    }
                }
            }
        }
        else
        {
            processList = angular.copy(vm.processTwoList);
        }
        vm.showProcessList = processList;
        for(let processData of processList)
        {
            for(let bott of result.bottleneckProcessList)
            {
                if (processData.recordId != bott.recordId) {
                    continue;
                }
                processData.productArea = bott.productArea;
                processData.maxNum = bott.maxNum;
                processData.productionType = bott.productionType;
                processData.sectionList = bott.sectionList;
            }
        }
        for(let data of result.page.list)
        {
            data.processList = angular.copy(processList);
            for(let processData of data.processList)
            {
                let useArea = 0;
                let useNum = 0;
                processData.showList = [];
                for(let use of result.useList)
                {
                    if(data.startTime != use.startTime)
                    {
                        continue;
                    }
                    if(processData.productionType && processData.productionType != use.productionType)
                    {
                        continue;
                    }
                    if(processData.processId == use.processManagementId)
                    {
                        if(use.useArea && Number(use.useArea) > 0)
                        {
                            useArea = useArea + Number(use.useArea);
                            useNum++;
                        }
                        processData.showList.push(use);
                    }
                }
                if(useArea > 0)
                {
                    useArea = useArea.toFixed(4);
                }
                processData.useArea = useArea;
                processData.useNum = useNum;
            }
        }
        // 计算开始数
        result.page.startCount = (result.page.pageNo - 1) * result.page.pageSize + 1;
        // 计算结束数
        result.page.endCount = (result.page.pageNo - 1) * result.page.pageSize;
        if(result.page && result.page.list && result.page.list.length > 0)
        {
            // 计算结束数
            result.page.endCount = result.page.endCount + result.page.list.length;
        }
        vm.dataPage.data = result.page
    }
    vm.showProjectList = [];
    vm.showOpenProjectList = function(row,data,number)
    {
        if (number === 1)
        {
            vm.showProjectList = data.taskList;
        }else if (number === 2){
            vm.showProjectList = data.finshTaskList;
        }
        $('#projectSheduingWindow').modal();
    }

    function handProjectScheding(result)
    {
        vm.projectPage.data = {};
        if(!result || !result.page ||!result.page.list || !result.productNameList || !result.projectScheduingList)
        {
            return;
        }
        vm.productNameList = result.productNameList;
        for(let data of result.page.list)
        {
            data.productNameList = angular.copy(vm.productNameList);
            for(let name of data.productNameList)
            {
                let taskNum = 0;
                let finishNum = 0;
                name.taskList = [];
                name.finshTaskList = [];
                for(let project of result.projectScheduingList)
                {
                    if(name.productName && name.productName !== project.orderType)
                    {
                        continue;
                    }
                    let arrangePlanDate = project.showArrangePlanDate.substring(5,10);
                    if (arrangePlanDate == data.date || (arrangePlanDate <= data.date && (project.confimDate === null || project.confimDate === '' || project.confimDate === undefined)))
                    {
                        if(name.productName === project.orderType)
                        {
                            taskNum++;
                            name.taskList.push(project);
                        }
                    }
                    if (arrangePlanDate === data.date && project.confimDate !== null && project.confimDate !== '' && project.confimDate !== undefined)
                    {
                        if(name.productName === project.orderType)
                        {
                            finishNum++;
                            name.finshTaskList.push(project);
                        }
                    }
                }
                name.taskNum = taskNum;
                name.finishNum = finishNum;
            }
        }
        // 计算开始数
        result.page.startCount = (result.page.pageNo - 1) * result.page.pageSize + 1;
        // 计算结束数
        result.page.endCount = (result.page.pageNo - 1) * result.page.pageSize;
        if(result.page && result.page.list && result.page.list.length > 0)
        {
            // 计算结束数
            result.page.endCount = result.page.endCount + result.page.list.length;
        }
        vm.projectPage.data = result.page;
    }

    vm.showList = [];
    vm.showSectionList = [];
    vm.showLookUp = null;
    vm.showOpenList = function(row,data)
    {
        vm.showLookUp = data.processName +'-'+ row.date;
        vm.showList = data.showList;
        vm.showSectionList = data.sectionList;
        if(vm.showSectionList && vm.showSectionList.length > 0)
        {
            for(let section of vm.showSectionList)
            {
                let arrangeCount = 0; // 安排款数
                let arrangeArea = 0; // 安排面积
                for(let data of vm.showList)
                {
                    data.intervalTime = data.schedulingStartTime +'-'+ data.schedulingEndTime;
                    if(section.startTime == data.schedulingStartTime && section.endTime == data.schedulingEndTime)
                    {
                        arrangeCount++;
                        if(data.useArea)
                        {
                            arrangeArea += Number(data.useArea);
                        }
                    }
                }
                if(arrangeArea > 0)
                {
                    arrangeArea = arrangeArea.toFixed(4);
                }
                section.arrangeCount = arrangeCount;
                section.arrangeArea = arrangeArea;
            }
        }
        $('#staticShowOpenList').modal();
    }

    vm.batchSchedulingMonitoringList = [];
    vm.getBatchSchedulingMonitoring = function(){
        let query = {};
        vm.batchSchedulingMonitoringList = [];
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("production/controls/batchSchedulingMonitoring", query).then(function(data) {
            if(data && data.length > 0)
            {
                angular.forEach(data,function(p){
                    p.showFlag = false;
                });
                vm.batchSchedulingMonitoringList = data;
            }
            MainCtrl.unblockUI();
        });
    }

    vm.openFlag = function (row)
    {
        if (!row)
        {
            return;
        }
        row.showFlag = !row.showFlag;
    }

    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
    });
}]);
