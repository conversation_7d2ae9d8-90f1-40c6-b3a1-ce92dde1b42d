const activation = {
	template:'#activation',
	data(){
		return{
			workClass: 'nav-link',
			myClass: 'nav-link'
		}
	},
	methods:{
		changeClass:function(item){
			this.initClass();
			if(item == 1){
				this.workClass = 'nav-link active';
			}else if(item == 5){
				this.myClass = 'nav-link active';
			}
		},
		initClass:function(){
			this.workClass = 'nav-link';
			this.myClass = 'nav-link';
		}
	}
}