/* Setup general page controller */
kybApp.controller('parametersetCtrl', ['$rootScope', '$scope', 'settings', '$http', 'upida', '$timeout', function($rootScope, $scope, settings, $http, upida, $timeout) {
	$scope.baseUrl = $rootScope.settings.baseUrl;

	$scope.help = function() {
		if ($scope.tabs.viewForm.active) {
			$scope.helpList();
		} else if ($scope.tabs.editForm.active) {
			$scope.helpDetail();
		}
	};

	$scope.shouldAutoStart = false;
	$scope.introListOptions = {
		steps: [{
			element: '#step1',
			intro: "当您打开页面后会自动加载所有参数信息至列表！",
			position: 'top'
		}, {
			element: '#step2',
			intro: "点击设置参数设置属于您自己的参数！",
			position: 'bottom'
		}, {
			element: '#step3',
			intro: '谢谢使用，再见。'
		}],
		showStepNumbers: false,
		exitOnOverlayClick: true,
		exitOnEsc: true,
		nextLabel: '<strong>下一步!</strong>',
		prevLabel: '<span style="color:green">上一步</span>',
		skipLabel: '退出',
		doneLabel: '谢谢'
	};

	$scope.introDetailOptions = {
		steps: [{
			element: '#stepDetail1',
			intro: "参数设定输入区域！",
			position: 'top'
		}, {
			element: '#stepDetail5',
			intro: '操作按钮区域，点击保存完成本次操作！',
			position: 'top'
		}, {
			element: '#stepDetail6',
			intro: '谢谢使用，再见。'
		}],
		showStepNumbers: false,
		exitOnOverlayClick: true,
		exitOnEsc: true,
		nextLabel: '<strong>下一步!</strong>',
		prevLabel: '<span style="color:green">上一步</span>',
		skipLabel: '退出',
		doneLabel: '谢谢'
	};

	var vm = this;

	// 权限
	$scope.right = {};

	//报废率
	vm.baofeilv = {};

	//跟单期限
	vm.gendanqixian = {};

	//app配置可款数
	vm.appConfigure = {};
	
	vm.qualitUnit = "";

	vm.cycleControl = "";
	vm.scheduleNum = 0;
	vm.partialArea = 0;

	vm.stockSpecification = "";

	vm.beforeCapacity = 0;
	vm.exceedCapacity = 0;
	vm.overdueDays = {};
	vm.overdueDate = {};
	
	vm.managePrecent = {};

	vm.fedControl = {};

	vm.copperCladding = {};

	vm.scheduleTime = {};

	vm.qualitVersion = "";

	vm.someCraftNoAutoMerge = {};
	
	//合同单价
	vm.contractPriceConfigure = {};

	//投料出库控制
	vm.feedingDischargingControl = {};

	//送货出库控制
	vm.deliveryOutWarehouseControl = {};
	
	//采购单价
	vm.purchasePriceConfigure = {};

	//计划排单参数控制
	vm.schedulingParameterControl = {};

	//使用余料参数控制
	vm.useManterialParameterControl = {};

	//延期投料出库参数控制
	vm.delayedFeedParameterControl = {};

	//工程选料全选
	vm.projectMaterialSelection = {};

	// 工程附件控制
	vm.engineeringAccessories = {};

	// 工作时长设置
	vm.workingHoursSetting = {};

	// 日样品款数
	vm.numberSamplesDay = {};

	// 生产日产能设置比例
	vm.productionPerDaySetTheRatio = {};

	// 材料计价方式
	vm.materialPricingMethod = {};

	// 订单免审批
	vm.orderApprovalFree = {};

	// 库存盘点权限控制
	vm.inventoryCounting = {};

	// 物料价格维护授权
	vm.materialPrice = {};

	// 物料交期维护授权
	vm.materialDeliveryTime = {};

	// 材料价格有效期
	vm.materialPriceValidityPeriod = {};

	//原料提前期设置
	vm.rawmaterialLeadTime = {};

	vm.reportingControl = {};

	// 生产排单算法
	vm.productionSchedulingAlgorithm = {};

	//采购价设置参数控制
	vm.procurementConfigure = {
		jianPin : "procurementConfigure",
		parameterName : "采购价参数控制设置"
	};

	vm.parameter = {};

	vm.purchase = {};
	
	vm.takeHandWip = {}; // zjn 2019-09-26 接/交wip
	
	vm.effectiveDayWarn = {};
	
	vm.maximumNewOrderDay = {};
	
	// 物料领用 WC 2016-12-22
	vm.materialUse = {};
	vm.materialUse.show = false;
	
	// 余料 zjn 2017-07-25
	vm.materialRemain = {};
	vm.materialRemain.show = false;

	vm.purchaseShow = false;
	vm.procurementShow = false;
	vm.wipReportFun = false;
	vm.purchaseHead = true;
	vm.preCastRateShow = false;
	vm.feedingShow = false;
	vm.quoteSetting = false;
	vm.showSomeCraftNoAutoMerge = false;
	vm.takeHandWipFlag = false; // zjn 2019-09-26 接/交wip标志
	vm.effectiveDayWarnFlag = false;
	vm.maximumNewOrderDayFlag = false;
	vm.materialList = [];

	vm.feedingDischargingControlShow = false;
	vm.deliveryOutWarehouseControlShow = false;
	vm.schedulingParameterControlShow = false;
	vm.useManterialParameterControlShow = false;
	vm.delayedFeedParameterControlShow = false;
	vm.projectMaterialSelectionShow = false;
	vm.engineeringAccessoriesShow = false;
	vm.workingHoursSettingShow = false;
	vm.numberSamplesDayShow = false;
	vm.productionPerDaySetTheRatioShow = false;
	vm.materialPricingMethodShow = false;
	vm.orderApprovalFreeShow = false;
	
	vm.company = {}; //公司详细信息

	// tabs控制
	$scope.tabs = {
		viewForm: {
			active: true
		},
		editForm: {
			active: false,
			show: false
		}
	};

	$scope.showEditForm = function() {
		$scope.tabs.editForm.show = true;
		$scope.tabs.editForm.active = true;
	};

	// 隐藏编辑
	$scope.hideEditForm = function() {
		vm.parameterValue = null;
		$scope.tabs.editForm.show = false;
		$timeout(function() {
			$scope.tabs.viewForm.active = true;
		});
	};

	$scope.formScope = null;
	$scope.setFormScope = function(scope) {
		$scope.formScope = scope;
	};
	
	vm.saveParam = function(type) {
		vm.parameter = {};
		if (type == "cycleControl"){
			vm.parameter = vm.cycleControl;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "cycleControl";
				vm.parameter.parameterName = "周期控制";
			}
		}else if(type == "scheduleNum"){
			vm.parameter = vm.scheduleNum;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "scheduleNum";
				vm.parameter.parameterName = "排班安排看板数量";
			}
		}else if(type == "beforeCapacity"){
			vm.parameter = vm.beforeCapacity;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "beforeCapacity";
				vm.parameter.parameterName = "产能往前可用天数";
			}
		}else if(type == "exceedCapacity"){
			vm.parameter = vm.exceedCapacity;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "exceedCapacity";
				vm.parameter.parameterName = "超过购买常能百分比";
			}
		}else if(type == "partialArea"){
			vm.parameter = vm.partialArea;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "partialArea";
				vm.parameter.parameterName = "系统分批最小批次面积";
			}
		}else if(type == "qualitUnit"){
			vm.parameter = vm.qualitUnit;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "qualitUnit";
				vm.parameter.parameterName = "品质单位";
			}
		}else if(type == "qualitVersion"){
			vm.parameter = vm.qualitVersion;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "qualitVersion";
				vm.parameter.parameterName = "品质版本";
			}
		}else if(type == "fedControl"){
			vm.parameter = vm.fedControl;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "fedControl";
				vm.parameter.parameterName = "补料数量控制";
			}
		}else if(type == "managePrecent"){
			vm.parameter = vm.managePrecent;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "managePrecent";
				vm.parameter.parameterName = "管理费百分比";
			}
		}else if(type == "fedControl") {
			vm.parameter = vm.fedControl;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "fedControl";
				vm.parameter.parameterName = "补料数量控制";
			}
		}else if(type == "stockSpecification"){
			vm.parameter = vm.stockSpecification;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "stockSpecification";
				vm.parameter.parameterName = "报备料规格";
			}
		}else if(type == "defaultAPR"){
			vm.parameter = vm.defaultAPR;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "defaultAPR";
				vm.parameter.parameterName = "年利率";
			}
		}else if(type == "boardThicknessRange"){
			vm.parameter = vm.boardThicknessRange;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "boardThicknessRange";
				vm.parameter.parameterName = "板厚范围";
			}
		}else if(type == "copperCladThicknessRange"){
			vm.parameter = vm.copperCladThicknessRange;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "copperCladThicknessRange";
				vm.parameter.parameterName = "铜厚范围";
			}
		}else if(type == "receivableDays"){
			vm.parameter = vm.receivableDays;
			if(!vm.parameter.recordId){
				vm.parameter.jianPin = "receivableDays";
				vm.parameter.parameterName = "收款复核天数";
			}
		}else if(type == "actualCostLimit") {
			vm.parameter = vm.actualCostLimit;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "actualCostLimit";
				vm.parameter.parameterName = "实际成本费用限额";
			}
		}else if(type == "actualDeliveryDateLimit") {
			vm.parameter = vm.actualDeliveryDateLimit;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "actualDeliveryDateLimit";
				vm.parameter.parameterName = "实际交期天数限制";
			}
		}else if(type == "maxBatchArea") {
			vm.parameter = vm.maxBatchArea;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "maxBatchArea";
				vm.parameter.parameterName = "最大批次产量";
			}
		}else if(type == "confirmationPerson") {
			vm.parameter = vm.confirmationPerson;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "confirmationPerson";
				vm.parameter.parameterName = "确认负责人";
			}
		}else if(type == "defaultUtilizationOrders") {
			vm.parameter = vm.defaultUtilizationOrders;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "defaultUtilizationOrders";
				vm.parameter.parameterName = "新单默认利用率";
			}
		}else if(type == "purchLeadTime") {
			vm.parameter = vm.purchLeadTime;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "purchLeadTime";
				vm.parameter.parameterName = "采购周期确认";
			}
		}else if(type == "purchPerson") {
			vm.parameter = vm.purchPerson;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "purchPerson";
				vm.parameter.parameterName = "采购负责人";
			}
		}else if(type == "marketingManager") {
			vm.parameter = vm.marketingManager;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "marketingManager";
				vm.parameter.parameterName = "市场负责人";
			}
		}else if(type == "financialAdministrator") {
			vm.parameter = vm.financialAdministrator;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "financialAdministrator";
				vm.parameter.parameterName = "财务负责人";
			}
		}else if(type == "overdueDate") {
			vm.parameter = vm.overdueDate;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "overdueDate";
				vm.parameter.parameterName = "日期类型";
			}
		}else if(type == "overdueDays") {
			vm.parameter = vm.overdueDays;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "overdueDays";
				vm.parameter.parameterName = "逾期天数";
			}
		}else if(type == "copperCladding") {
			vm.parameter = vm.copperCladding;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "copperCladding";
				vm.parameter.parameterName = "覆铜要求";
			}
		}else if (type == "scheduleTime") {
			vm.parameter = vm.scheduleTime;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "scheduleTime";
				vm.parameter.parameterName = "转板规定时间";
			}
		}
		else if (type == "workingHoursSetting") {
			vm.parameter = vm.workingHoursSetting;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "workingHoursSetting";
				vm.parameter.parameterName = "工作时长设置";
			}
		}
		else if (type == "numberSamplesDay") {
			vm.parameter = vm.numberSamplesDay;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "numberSamplesDay";
				vm.parameter.parameterName = "日样品款数";
			}
		}
		else if (type == "productionPerDaySetTheRatio") {
			vm.parameter = vm.productionPerDaySetTheRatio;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "productionPerDaySetTheRatio";
				vm.parameter.parameterName = "生产日产能设置比例";
			}
		}
		else if (type == "materialPricingMethod") {
			vm.parameter = vm.materialPricingMethod;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "materialPricingMethod";
				vm.parameter.parameterName = "材料计价方式";
			}
		}
        else if (type == "orderApprovalFree") {
            vm.parameter = vm.orderApprovalFree;
            if (!vm.parameter.recordId) {
                vm.parameter.jianPin = "orderApprovalFree";
                vm.parameter.parameterName = "订单免审批";
            }
        }else if (type == "inventoryCounting"){
			vm.parameter = vm.inventoryCounting;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "inventoryCounting";
				vm.parameter.parameterName = "库存盘点权限控制";
			}
		}else if (type == "materialPrice"){
			vm.parameter = vm.materialPrice;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "materialPrice";
				vm.parameter.parameterName = "物料价格维护授权";
			}
		}else if (type == "materialDeliveryTime"){
			vm.parameter = vm.materialDeliveryTime;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "materialDeliveryTime";
				vm.parameter.parameterName = "物料交期维护授权";
			}
		}else if (type == "materialPriceValidityPeriod"){
			vm.parameter = vm.materialPriceValidityPeriod;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "materialPriceValidityPeriod";
				vm.parameter.parameterName = "材料价格有效期";
			}
		}else if (type == "rawmaterialLeadTime") {
			vm.parameter = vm.rawmaterialLeadTime;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "rawmaterialLeadTime";
				vm.parameter.parameterName = "原料提前期设置";
			}
		}else if (type == "productionSchedulingAlgorithm"){
			vm.parameter = vm.productionSchedulingAlgorithm;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "productionSchedulingAlgorithm";
				vm.parameter.parameterName = "生产排单算法";
			}
		}else if (type == "reportingControl"){
			vm.parameter = vm.reportingControl;
			if (!vm.parameter.recordId) {
				vm.parameter.jianPin = "reportingControl";
				vm.parameter.parameterName = "产能报备设置";
			}
		}
		else {
			$scope.message = "不在保存范围内，请联系系统人员";
			$('#static').modal();
			return;
		}
		MainCtrl.blockUI({
			animate: true,
		});
		upida.post("sys/parameterset/add",vm.parameter).then(function (result) {
			$scope.message = "保存成功";
			$('#static').modal();
			loadItemsData();
			MainCtrl.unblockUI();
        });
	};

	//保存按钮
	$scope.testData = function(row) {
		if (vm.parameter.jianPin == "marketOrderStrict") {
			let i = 0;
			for (let record of vm.recordList) {
				if(!record.parameterType){
					$scope.message = "参数类型不能为空！";
					$('#static').modal();
					return;
				}
				if(!record.upSortPer && !record.upSort){
					$scope.message = "调上比例或调上固定值不能都为空！";
					$('#static').modal();
					return;
				}
				if(record.upSortPer && record.upSort){
					$scope.message = "调上比例或调上固定值不能同时存在！";
					$('#static').modal();
					return;
				}
				if(!record.downSortPer && !record.downSort){
					$scope.message = "调下比例或调下固定值不能都为空！";
					$('#static').modal();
					return;
				}
				if(record.downSortPer && record.downSort){
					$scope.message = "调下比例或调下固定值不能同时存在！";
					$('#static').modal();
					return;
				}
				vm.recordList[i].materialType = vm.currencyType;
				i++;
			}
			// 保存
			$http({
				method: 'POST',
				url: $scope.baseUrl + 'sys/parameterset/addMarketOrder',
				data: vm.recordList
			}).success(function(data) {
				// 自己定义弹出框
				// 提示信息
				$scope.message = data;
				$('#static').modal();
				vm.queryParameter(); //重新查询
				MainCtrl.unblockUI();
			});
		}else{
			vm.parameter.parameterValue = vm.parameterValue;
			vm.parameter.start = vm.start;

			if (vm.parameter.jianPin == "preCastRate") {
				if (vm.parameter.parameterValue == "0") {
					$scope.message = "预投率不能为0!";
					$('#static').modal();
					return;
				}
			}

			MainCtrl.blockUI({
				animate: true,
			});

			// 保存
			$http({
				method: 'POST',
				url: $scope.baseUrl + 'sys/parameterset/add',
				data: vm.parameter
			}).success(function(data) {
				// 自己定义弹出框
				// 提示信息
				$scope.message = data;
				$('#static').modal();
				$scope.hideEditForm();
				loadItemsData(); //重新查询
				MainCtrl.unblockUI();
			});
		}
	};

	$scope.showCode = '';
	$scope.showType = '';
	$scope.showTitle = '';
	$scope.unit = '';

	// zjn 2019-09-26 清除标志
	function cleanFlag()
	{
		vm.purchaseShow = false;
		vm.procurementShow = false;
		vm.wipReportFun = false;
		vm.purchaseHead = false;
		vm.preCastRateShow = false;
		vm.feedingShow = false;
		vm.quoteSetting = false;
		vm.showSomeCraftNoAutoMerge = false;
		vm.takeHandWipFlag = false; // zjn 2019-09-26 接/交wip标志
		vm.effectiveDayWarnFlag = false;
		vm.feedingDischargingControlShow = false;
		vm.deliveryOutWarehouseControlShow = false;
		vm.schedulingParameterControlShow = false;
		vm.useManterialParameterControlShow = false;
		vm.delayedFeedParameterControlShow = false;
		vm.projectMaterialSelectionShow = false;
		vm.engineeringAccessoriesShow = false;
		vm.workingHoursSettingShow = false;
		vm.numberSamplesDayShow = false;
		vm.orderApprovalFreeShow = false;
	}
	
	// 点击设置按钮
	$scope.checkKhbm = function(type) {
		$scope.formScope.editForm.$setPristine();
		cleanFlag();// 清除标志
		var object = angular.copy(type);
		$scope.showEditForm();
		switch (object.jianPin) {
			case "baofeilv":
				vm.purchaseHead = true;
				$scope.showCode = object.parameterValue;
				$scope.showType = "预警率";
				$scope.showTitle = "报废预警率";
				$scope.unit = object.unit;
				vm.difference = true;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "gendanqixian":
				vm.purchaseHead = true;
				$scope.showCode = object.parameterValue;
				$scope.showType = "最长期限";
				$scope.showTitle = "业务员与客户建立合作关系的最长期限";
				$scope.unit = object.unit;
				vm.difference = false;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "purchase":
				$scope.showCode = object.parameterValue;
				$scope.showType = "原料采购";
				$scope.showTitle = "物料的现库存量低于安全库存的时候快速生成采购订单";
				vm.purchaseShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "preCastRate":
				vm.preCastRateShow = true;
				$scope.showCode = object.parameterValue;
				$scope.showType = "预投率";
				$scope.showTitle = "所有客户投料的基本预投率";
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "materialUse":
				$scope.showCode = object.parameterValue;
				$scope.showType = "物料领用";
				$scope.showTitle = "物料领用";
				vm.purchaseShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "appConfigure":
				$scope.showCode = object.parameterValue;
				$scope.showType = "款数";
				$scope.showTitle = "生产过数时最大可过数款数";
				vm.feedingShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "someCraftNoAutoMerge":
				vm.preCastRateShow = true;
				$scope.showCode = object.parameterValue;
				$scope.showType = "最大合单款数";
				$scope.showTitle = "通知单是返单生产且相同生产编号确认时生成的工程卡是否自动合并";
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "contractPriceConfigure":
				$scope.showCode = object.parameterValue;
				$scope.showType = "合同单价设置";
				$scope.showTitle = "合同单价";
				vm.purchaseShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "purchasePriceConfigure":
				$scope.showCode = object.parameterValue;
				$scope.showType = "采购单价设置";
				$scope.showTitle = "采购单价";
				vm.purchaseShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "procurementConfigure":
				$scope.showCode = object.parameterValue;
				$scope.showType = "采购价参数控制设置";
				$scope.showTitle = "采购价参数控制";
				vm.procurementShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = 30;
				vm.start = object.start;
				break;
			case "materialRemain":
				vm.wipReportFun = true;
				$scope.showCode = object.parameterValue;
				$scope.showType = "WIP统计方式";
				$scope.showTitle = "统计方式";
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "quotationSetting":
				$scope.showCode = object.parameterValue;
				$scope.showType = "报价设置";
				$scope.showTitle = "是否工艺参与报价";
				vm.purchaseShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "applicatOtherCustomers":
				$scope.showCode = object.parameterValue;
				$scope.showType = "应用其他客户型号设置";
				$scope.showTitle = "是否应用其他客户型号";
				vm.purchaseShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			// 接/交wip
			case "takeOrHandWip":
				$scope.showCode = object.parameterValue;
				$scope.showType = "接/交wip设置";
				$scope.showTitle = "接/交wip";
				vm.takeHandWipFlag = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			// 有效天数提醒
			case "effectiveDayWarn":
				$scope.showCode = object.parameterValue;
				$scope.showType = "有效天数设置";
				$scope.showTitle = "有效天数提醒";
				vm.effectiveDayWarnFlag = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			// 工程卡每日新单最大款数
			case "maximumNewOrderDay":
				$scope.showCode = object.parameterValue;
				$scope.showType = "工程卡每日新单最大款数设置";
				$scope.showTitle = "工程卡每日新单最大款数";
				vm.maximumNewOrderDayFlag = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "usedMaterialStrict":
				$scope.showCode = object.parameterValue;
				$scope.showType = "出入库材料控制设置";
				$scope.showTitle = "是否严格按市场指定材料进行出入库";
				vm.usedMaterialStrictShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "marketOrderStrict":
				$scope.showType = "市场下单参数设置";
				vm.parameter.jianPin = "marketOrderStrict";
				// $scope.showTitle = "是否严格按市场指定材料进行出入库";
				vm.marketOrderStrictShow = true;
				marketOrderLoad();
				break;
			case "feedingDischargingControl":
				$scope.showCode = object.parameterValue;
				$scope.showType = "投料出库控制";
				$scope.showTitle = "投料出库控制";
				vm.feedingDischargingControlShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "deliveryOutWarehouseControl":
				$scope.showCode = object.parameterValue;
				$scope.showType = "送货出库控制";
				$scope.showTitle = "送货出库控制";
				vm.deliveryOutWarehouseControlShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "schedulingParameterControl":
				$scope.showCode = object.parameterValue;
				$scope.showType = "计划排单控制";
				$scope.showTitle = "计划排单控制";
				vm.schedulingParameterControlShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "useManterialParameterControl":
				$scope.showCode = object.parameterValue;
				$scope.showType = "使用余料控制";
				$scope.showTitle = "使用余料控制";
				vm.useManterialParameterControlShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "delayedFeedParameterControl":
				$scope.showCode = object.parameterValue;
				$scope.showType = "延期投料出库参数控制";
				$scope.showTitle = "延期投料出库参数控制";
				vm.delayedFeedParameterControlShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "projectMaterialSelection":
				$scope.showCode = object.parameterValue;
				$scope.showType = "工程选料全选";
				$scope.showTitle = "工程选料全选";
				vm.projectMaterialSelectionShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "engineeringAccessories":
				$scope.showCode = object.parameterValue;
				$scope.showType = "工程选料全选";
				$scope.showTitle = "工程选料全选";
				vm.engineeringAccessoriesShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "workingHoursSetting":
				$scope.showCode = object.parameterValue;
				$scope.showType = "工作时长设置";
				$scope.showTitle = "工作时长设置";
				vm.workingHoursSettingShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "numberSamplesDay":
				$scope.showCode = object.parameterValue;
				$scope.showType = "日样品款数";
				$scope.showTitle = "日样品款数";
				vm.numberSamplesDayShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "productionPerDaySetTheRatio":
				$scope.showCode = object.parameterValue;
				$scope.showType = "生产日产能设置比例";
				$scope.showTitle = "生产日产能设置比例";
				vm.productionPerDaySetTheRatioShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "materialPricingMethod":
				$scope.showCode = object.parameterValue;
				$scope.showType = "材料计价方式";
				$scope.showTitle = "材料计价方式";
				vm.materialPricingMethodShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
			case "orderApprovalFree":
				$scope.showCode = object.parameterValue;
				$scope.showType = "订单免审批";
				$scope.showTitle = "订单免审批";
				vm.orderApprovalFreeShow = true;
				$scope.unit = object.unit;
				vm.parameter = object;
				vm.parameterValue = object.parameterValue;
				vm.start = object.start;
				break;
		}
	};

	//加载数据
	$scope.loadData = function(data) {
		for (var i = 0; i < data.data.parameterSetList.length; i++) {
			if (data.data.parameterSetList) {
				var object = data.data.parameterSetList[i];
				switch (object.jianPin) {
					case "cycleControl":
						vm.cycleControl = object;
						break;
					case "partialArea":
						object.parameterValue = Number(object.parameterValue);
						vm.partialArea = object;
						break;
					case "scheduleNum":
						object.parameterValue = Number(object.parameterValue);
						vm.scheduleNum = object;
						break;
					case "beforeCapacity":
						object.parameterValue = Number(object.parameterValue);
						vm.beforeCapacity = object;
						break;
					case "exceedCapacity":
						object.parameterValue = Number(object.parameterValue);
						vm.exceedCapacity = object;
						break;
					case "qualitUnit":
						vm.qualitUnit = object;
						break;
					case "qualitVersion":
						vm.qualitVersion = object;
						break;
					case "baofeilv":
						vm.baofeilv = object;
						break;
					case "gendanqixian":
						vm.gendanqixian = object;
						break;
					case "purchase":
						vm.purchase = object;
						break;
					case "preCastRate":
						vm.preCastRate = object;
						break;
					case "materialUse":
						vm.materialUse = object;
						break;
					case "appConfigure":
						vm.appConfigure = object;
						break;
					case "someCraftNoAutoMerge":
						vm.someCraftNoAutoMerge = object;
						vm.someCraftNoAutoMerge.parameterValue = Number(vm.someCraftNoAutoMerge.parameterValue);
						break;
					case "contractPriceConfigure":
						vm.contractPriceConfigure = object;
						break;
					case "purchasePriceConfigure":
						vm.purchasePriceConfigure = object;
						break;
					case "procurementConfigure":
						vm.procurementConfigure = object;
						break;
					case "materialRemain":
						vm.materialRemain = object;
						break;
					case "quotationSetting":
						vm.quotationSetting = object;
						break;
					case "applicatOtherCustomers":
						vm.applicatOtherCustomers = object;
						break;
					// 接/交wip
					case "takeOrHandWip":
						vm.takeHandWip = object;
						break;
					// 有效天数提醒
					case "effectiveDayWarn":
						vm.effectiveDayWarn = object;
						break;
					// 工程卡每日新单最大款数
					case "maximumNewOrderDay":
						vm.maximumNewOrderDay = object;
						break;
					// 管理费百分比
					case "managePrecent":
						vm.managePrecent = object;
						break;
					// 补料数量控制
					case "fedControl":
						vm.fedControl = object;
						break;
					// 年利率
					case "defaultAPR":
						vm.defaultAPR = object;
						break;
					// 板厚范围±百分比
					case "boardThicknessRange":
						vm.boardThicknessRange = object;
						break;
					// 铜厚范围±百分比
					case "copperCladThicknessRange":
						vm.copperCladThicknessRange = object;
						break;
					// 收款复核天数
					case "receivableDays":
						vm.receivableDays = object;
						break;
					// 收款复核天数
					case "usedMaterialStrict":
						vm.usedMaterialStrict = object;
						break;
					// 实际成本费用限额
					case "actualCostLimit":
						vm.actualCostLimit = object;
						break;
					case "actualDeliveryDateLimit":
						vm.actualDeliveryDateLimit = object;
						break;
					case "maxBatchArea":
						vm.maxBatchArea = object;
						break;
					case "confirmationPerson":
						vm.confirmationPerson = object;
						break;
					case "defaultUtilizationOrders":
						vm.defaultUtilizationOrders = object;
						break;
					case "purchLeadTime":
						vm.purchLeadTime = object;
						break;
					case "purchPerson":
						vm.purchPerson = object;
						break;
					// 报备料规格
					case "stockSpecification":
						vm.stockSpecification = object;
						break;
					// 市场负责人
					case "marketingManager":
						vm.marketingManager = object;
						break;
					// 财务负责人
					case "financialAdministrator":
						vm.financialAdministrator = object;
						break;
					//日期类型
					case "overdueDate":
						vm.overdueDate = object;
						break;
					//逾期天数
					case "overdueDays":
						object.parameterValue = Number(object.parameterValue);
						vm.overdueDays = object;
						break;
					case "copperCladding":
						object.parameterValue = Number(object.parameterValue);
						vm.copperCladding = object;
						break;
					case "feedingDischargingControl":
						vm.feedingDischargingControl = object;
						break;
					case "deliveryOutWarehouseControl":
						vm.deliveryOutWarehouseControl = object;
						break;
					case "schedulingParameterControl":
						vm.schedulingParameterControl = object;
						break;
					case "useManterialParameterControl":
						vm.useManterialParameterControl = object;
						break;
					case "delayedFeedParameterControl":
						vm.delayedFeedParameterControl = object;
						break;
					case "projectMaterialSelection":
						vm.projectMaterialSelection = object;
						break;
					case "engineeringAccessories":
						vm.engineeringAccessories = object;
						break;
					case "scheduleTime":
						object.parameterValue = Number(object.parameterValue);
						vm.scheduleTime = object;
						break;
					case "workingHoursSetting":
						vm.workingHoursSetting = object;
						break;
					case "numberSamplesDay":
						vm.numberSamplesDay = object;
						break;
					case "productionPerDaySetTheRatio":
						vm.productionPerDaySetTheRatio = object;
						break;
					case "materialPricingMethod":
						vm.materialPricingMethod = object;
						break;
					case "orderApprovalFree":
						vm.orderApprovalFree = object;
						break;
					case "inventoryCounting":
						vm.inventoryCounting = object;
						break;
					case "materialPriceValidityPeriod":
						vm.materialPriceValidityPeriod = object;
						break;
					case "materialPrice":
						vm.materialPrice = object;
						break;
					case "materialDeliveryTime":
						vm.materialDeliveryTime = object;
						break;
					case "leadTime":
						vm.rawmaterialLeadTime = object;
						break;
					case "productionSchedulingAlgorithm":
						vm.productionSchedulingAlgorithm = object;
						break;
					case "mjOperator":
						vm.mjOperator = object;
						break;
					case "csjOperator":
						vm.csjOperator = object;
						break;
					case "reportingControl":
						vm.reportingControl = object;
						break;
					case "wbgsOperator":
						vm.wbgsOperator = object;
						break;
				}
			}
		}
	};

	/**
	 * 弹出重置编码的框
	 */
	$scope.chongzhi = function() {
		$('#staticchongzhi').modal();
		return;
	};

	/**
	 * 做重置操作
	 */
	$scope.dochongzhi = function() {

	};

	function loadRight() {
		MainCtrl.blockUI({
			animate: true,
		});
		upida.get("common/rightall?prefix=sys:parameterset").then(function(data) {
			$scope.right.view = data.view;
			$scope.right.edit = data.edit;
			$scope.right.manage = data.manage;
			loadItemsData();
		});

		upida.post("sys/company/listData", vm.company).then(function(company) {
			vm.company = company;
			MainCtrl.unblockUI();
		});
	}

	vm.customerList = [];
	function loadItemsData() {
		MainCtrl.blockUI({
    	   	    animate: true,
    		});
		// 加载基础数据
		upida.get("sys/parameterset/load/data").then(function(result) {
			$scope.loadData(result);
			marketOrderLoad();
			vm.userList = result.data.userList;
			vm.materialList = result.data.materialList;
			vm.customerList = result.data.customerList;
			MainCtrl.unblockUI();
		});
	}
	vm.recordList = [];
	vm.currencyTypeList = [];
	vm.materialSpecificationList = [];
	vm.currencyType = {};
	function marketOrderLoad() {
		// 加载基础数据
		upida.get("sys/parameterset/marketOrderStrict").then(function(result) {
			vm.currencyTypeList = result.currencyTypeList;
			vm.currencyType = vm.currencyTypeList[0].recordId;
			vm.queryParameter();
			vm.materialSpecificationList = result.materialSpecificationList;
		});

	}

	vm.addMarketOrderRecord = function(){
		vm.recordList.splice(0, 0, {status:"1"});
	}

	vm.queryParameter = function(){
		upida.post("sys/parameterset/parameterQuery",vm.currencyType).then(function(result) {
			vm.recordList = result;
		});
	}
	vm.updateAmount = function(row,type){
		if(type==1){
			if(row.upSortPer){
				row.upSort = null;
			}
		}else if(type==2){
			if(row.downSortPer){
				row.downSort = null;
			}
		}else if(type==3){
			if(row.upSort){
				row.upSortPer = null;
			}
		}else if(type==4){
			if(row.downSort){
				row.downSortPer = null;
			}
		}
	}

	vm.delParameter = function(row){
		if (!vm.recordList || vm.recordList.length == 0) {
			vm.recordList = [];
			return;
		}
		upida.post("sys/parameterset/delMarketOrder", row).then(function (data) {
			$scope.message = "删除成功！";
			$('#static').modal();
			vm.queryParameter();
		});
	}

	$scope.$on("$stateChangeSuccess", function() {
		upida.setScope($scope);
		loadRight();
	});
}]);