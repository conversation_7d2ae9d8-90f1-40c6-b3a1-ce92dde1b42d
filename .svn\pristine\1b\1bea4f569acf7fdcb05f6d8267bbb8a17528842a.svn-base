/**
 * 
 */
package com.kyb.pcberp.modules.production.dao;

import java.util.List;
import java.util.Map;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.production.entity.ProduceStatistics;

/**
 * 过数统计DAO接口
 * 
 * <AUTHOR>
 * @version 2015-10-26
 */
@MyBatisDao
public interface ProduceStatisticsDao extends CrudDao<ProduceStatistics>
{
    List<ProduceStatistics> findByProcessAndMonth(Map<String, Object> params);
}