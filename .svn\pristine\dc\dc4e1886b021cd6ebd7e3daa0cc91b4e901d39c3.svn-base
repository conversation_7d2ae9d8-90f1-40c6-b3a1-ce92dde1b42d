package com.kyb.pcberp.modules.production.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.kyb.pcberp.modules.sys.entity.User;

/**
 * 个人产出Entity
 * <AUTHOR>
 * @version 2016-07-28
 */
public class IndividualVo 
{
    private User userId; //用户
    private BigDecimal individual;  //每日个人产出
    private Date individualDate; //产出日期
    private String selectDate;//查询时间
    
    private Integer no;
    
    public Integer getNo()
    {
        return no;
    }
    
    public void setNo(Integer no)
    {
        this.no = no;
    }
    
    public String getSelectDate()
    {
        return selectDate;
    }
    public void setSelectDate(String selectDate)
    {
        this.selectDate = selectDate;
    }
    public User getUserId()
    {
        return userId;
    }
    public void setUserId(User userId)
    {
        this.userId = userId;
    }
    public BigDecimal getIndividual()
    {
        return individual;
    }
    public void setIndividual(BigDecimal individual)
    {
        this.individual = individual;
    }
    public Date getIndividualDate()
    {
        return individualDate;
    }
    public void setIndividualDate(Date individualDate)
    {
        this.individualDate = individualDate;
    }
    
    
    
    
    
}
