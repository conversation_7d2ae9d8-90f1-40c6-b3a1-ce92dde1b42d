kybApp.controller('stockMonthCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil', 'BaseUtil', '$filter', function ($rootScope, $scope, upida, $timeout, CommonUtil, BaseUtil, $filter) {
    $scope.$on('$viewContentLoaded', function () {
        MainCtrl.initAjax();
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
    var vm = this;
    vm.inoutPlaceQuery = {}; // 查询对象
    vm.inoutPlaceQuery.inoutType = {};
    vm.inoutPlaceQuery.inoutType.name = "inoutType";
    vm.inoutPlaceQuery.inoutType.value = "";
    vm.inoutPlaceQuery.material = {};
    vm.inoutPlaceQuery.materialPlace = {};
    vm.inoutPlaceQuery.supplierNo = {};
    vm.inoutPlaceQuery.supplierNo.name = "supplierNo";
    vm.inoutPlaceQuery.supplierNo.value = "";
    vm.inoutPlaceQuery.status = {};
    vm.inoutPlaceQuery.status.name = "status";
    vm.inoutPlaceQuery.status.value = "";
    vm.inoutPlaceQuery.storehouse = {};
    vm.inoutPlaceQuery.storehouse.name = "storehouse";
    vm.inoutPlaceQuery.storehouse.value = "";
    vm.inoutPlaceQuery.supplierName = {};
    vm.inoutPlaceQuery.supplierName.name = "supplierName";
    vm.inoutPlaceQuery.supplierName.value = "";
    vm.inoutPlaceQuery.sort = {};
    vm.inoutPlaceQuery.sort.name = "orderBy";
    vm.inoutPlaceQuery.sort.value = "month DESC,no ASC";
    vm.inoutPlaceQuery.inoutTimeStartQr = {};
    vm.inoutPlaceQuery.inoutTimeStartQr.name = "inoutTimeStartQr";
    vm.inoutPlaceQuery.inoutTimeStartQr.value = "";
    vm.inoutPlaceQuery.inoutTimeEndQr = {};
    vm.inoutPlaceQuery.inoutTimeEndQr.name = "inoutTimeEndQr";
    vm.inoutPlaceQuery.inoutTimeEndQr.value = "";
    vm.inoutPlaceQuery.inoutTimeTwoStartQr = {};
    vm.inoutPlaceQuery.inoutTimeTwoStartQr.value = "";
    vm.inoutPlaceQuery.inoutTimeTwoEndQr = {};
    vm.inoutPlaceQuery.craftNo = {};
    vm.inoutPlaceQuery.craftNo.name = "craftNo";
    vm.inoutPlaceQuery.craftNo.value = "";
    
 // 查询条件参数
    vm.inoutQuery = {}; // 查询对象
    vm.inoutQuery.inoutType = {};
    vm.inoutQuery.inoutType.name = "inoutType";
    vm.inoutQuery.inoutType.value = "";
    vm.inoutQuery.no = {};
    vm.inoutQuery.no.name = "no";
    vm.inoutQuery.no.value = "";
    vm.inoutQuery.name = {};
    vm.inoutQuery.name.name = "name";
    vm.inoutQuery.name.value = "";
    vm.inoutQuery.supplierNo = {};
    vm.inoutQuery.supplierNo.name = "supplierNo";
    vm.inoutQuery.supplierNo.value = "";
    vm.inoutQuery.status = {};
    vm.inoutQuery.status.name = "status";
    vm.inoutQuery.status.value = "";
    vm.inoutQuery.statusTwo = {};
    vm.inoutQuery.statusTwo.name = "statusTwo";
    vm.inoutQuery.statusTwo.value = "1";
    vm.inoutQuery.storehouse = {};
    vm.inoutQuery.storehouse.name = "storehouse";
    vm.inoutQuery.storehouse.value = "";
    vm.inoutQuery.supplierName = {};
    vm.inoutQuery.supplierName.name = "supplierName";
    vm.inoutQuery.supplierName.value = "";
    vm.inoutQuery.sort = {};
    vm.inoutQuery.sort.name = "orderBy";
    vm.inoutQuery.sort.value = "a.month DESC,m.no ASC";
    // 查询条件参数
    vm.inoutQuery.inoutTimeStartQr = {};
    vm.inoutQuery.inoutTimeStartQr.name = "inoutTimeStartQr";
    vm.inoutQuery.inoutTimeStartQr.value = "";
    vm.inoutQuery.inoutTimeEndQr = {};
    vm.inoutQuery.inoutTimeEndQr.name = "inoutTimeEndQr";
    vm.inoutQuery.inoutTimeEndQr.value = "";
    //生产编号查询
    vm.inoutQuery.craftNo = {};
    vm.inoutQuery.craftNo.name = "craftNo";
    vm.inoutQuery.craftNo.value = "";
    
    
    vm.query = {}; // 查询对象
    vm.query.materialType = {};
    vm.query.materialType.name = "materialType";
    vm.query.materialType.value = "";
    vm.query.specification = {};
    vm.query.specification.name = "specification";
    vm.query.specification.value = "";
    vm.query.storehouse = {};
    vm.query.storehouse.name = "storehouse";
    vm.query.storehouse.value = "";
    
    vm.page = {}; // 分页数据
    vm.page.infoMonth = {};
    vm.page.infoMonth.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.infoMonth.pageSize = 10;
    vm.page.infoMonth.pageNo = 1;
    vm.page.infoMonth.url = "stock/materialMonth/materialMonthPage";
    vm.page.infoMonth.condition = []; // 条件
    
    // 原料出入库记录
    vm.page.inout = {};
    vm.page.inout.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.inout.pageSize = 10;
    vm.page.inout.pageNo = 1;
    vm.page.inout.url = "stock/rawmaterial/inout/page";
    vm.page.inout.condition = []; // 条件
    
    var handle = {info: 0, inout: 1,adjust: 2,month:3}; // 操作对象
    vm.page.pageSizeOptions = [5, 10, 30, 50, 100, 150]; // 显示数据大小
    
 // 排序字段
 // 默认排序按时间排序排序处理
	vm.sort = {};
	vm.sort.no = {
		both: true,
		desc: false,
		asc: false
	};
	
	vm.sort.month = {
			both: true,
			desc: false,
			asc: false
		};

    
    vm.sortClick = function(col) {
		vm.sort[col].both = false;
		if(!vm.sort[col].desc && !vm.sort[col].asc) {
			vm.sort[col].asc = true;
		} else {
			if(vm.sort[col].asc) {
				vm.sort[col].desc = true;
				vm.sort[col].asc = false;
			} else {
				vm.sort[col].desc = false;
				vm.sort[col].asc = true;
			}
		}

		for(var p in vm.sort) {
			if(p !== col) {
				vm.sort[p].desc = false;
				vm.sort[p].asc = false;
				vm.sort[p].both = true;
			}
		}
		
    		if(col=='no')
    		{
    			vm.inoutQuery.sort.value = vm.sort[col].asc ? "m.no  ASC,a.month ASC" : "m.no  DESC,a.month ASC";
    		}
    		else if(col=='month')
    		{
    			vm.inoutQuery.sort.value = vm.sort[col].asc ? "a.month ASC,m.no ASC" : "a.month  DESC,m.no ASC";
    		}
    		else
    		{
    			vm.inoutQuery.sort.value = vm.sort[col].asc ? "a."+col + " ASC" : "a."+col + " DESC";
    		}
    		vm.init(vm.page.infoMonth.pageNo, vm.page.infoMonth.pageSize, vm.page.infoMonth.condition, vm.page.infoMonth.url, handle.month);
	};
    
 // 时间范围的Model
    vm.time = {
        start: {},
        end: {}
    };
    
    // 仓库列表
    vm.storehouseList = [];
    vm.materialTypeList = [];
    
     vm.timeTwo = {
	        start: {},
	        end: {}
	    };
	 vm.initDateTwo=function(date)
		{
			 if(date==""){
				 vm.rangeOptionsTwo = {
			        //format: "YYYY-MM-DD",
			    	startDate: new Date(vm.inoutQuery.inoutTimeStartQr.value),
			    	minDate:new Date(new Date(vm.inoutQuery.inoutTimeEndQr.value).setFullYear(new Date(vm.inoutQuery.inoutTimeEndQr.value).getFullYear()-5))
				 };
				 vm.timeTwo= {
			         start: vm.rangeOptionsTwo.startDate,
					 end: vm.rangeOptionsTwo.minDate
				 } 
			 }
		 };
		 
	vm.rangeOptionsTwo = {
	        //format: "YYYY-MM-DD",
	//		    	startDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5))),
	    	startDate:new Date((new Date).getFullYear(), (new Date).getMonth(), 1),
	        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 6)))
	    };
    
    vm.reportOne = true;
	vm.reportThree = false;
    vm.groupCheckQuery = function() {
    	vm.getGroupCenterBillList();
	};
	
	vm.monthDataList = [];
	vm.monthGroupDataList = [];
    vm.getGroupCenterBillList = function(){
    	
    	// 设置查询条件
        var condit = [];
        var material = {};//用物料属性查
        var supplier = {};//用客户属性查
        if(vm.materialNo)
        {
        	condit.push({
                name: 'no',
                value: vm.materialNo
            });
        }
        /*if(vm.materialName)
        {
        	condit.push({
                name: 'materialName',
                value: vm.materialName
            });
        }*/
        if(vm.materialTypeId)
        {
        	condit.push({
                name: 'materialTypeId',
                value: vm.materialTypeId
            });
        }
        
        if(vm.periodQuery&&vm.periodQuery!='所有')
        {
        	condit.push({
                name: 'month',
                value: vm.periodQuery
            });
        }
        
        if(vm.storeHouseId)
        {
        	condit.push({
                name: 'storeHouseId',
                value: vm.storeHouseId
            });
        }
        /*if(vm.specification)
        {
        	condit.push({
                name: 'specification',
                value: vm.specification
            });
        }
        

        if (vm.time.start) {
        	condit.push({
                name: "inoutTimeStartQr",
                value: vm.timeTwo.start.valueOf()
            });
        	vm.inoutPlaceQuery.inoutTimeTwoStartQr.value = vm.timeTwo.start.valueOf();
        }
        if (vm.time.end) {
        	condit.push({
                name: "inoutTimeEndQr",
                value: vm.timeTwo.end.valueOf()
            });
        	vm.inoutPlaceQuery.inoutTimeTwoEndQr.value = vm.timeTwo.end.valueOf();
        }*/
        
        vm.page.infoMonth.pageNo = 1;
        vm.page.infoMonth.condition = condit;
        
    	vm.init(vm.page.infoMonth.pageNo, vm.page.infoMonth.pageSize, vm.page.infoMonth.condition, vm.page.infoMonth.url, handle.month);
    	
    } ;
    
    
    /*
     * 兼容条件查询与普通查询
     * no 当前页
     * size 当前页显示数量
     * condition 是数组，可放入多个条件对象，如{name:"type", value:"1"}  名称对应实体字段名称，值对应页面输入的值
     * url 请求数据链接
     */
    vm.init = function (no, size, condition, url, h) {
        MainCtrl.blockUI({
            animate: true,
        });
        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;

    	condition.push({
            name: vm.inoutQuery.sort.name,
            value: vm.inoutQuery.sort.value
        });

        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condition);

        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};

            // 如果结果为空
            if (angular.isUndefined(result) || angular.isUndefined(result.list)) {
                data.pageNo = 1;
                data.pageSize = 10;
                data.startCount = 0;
                data.endCount = 0;
                data.list = [];
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }
            vm.page.infoMonth.data = data;
            // 设置返回的请求数据
            /*switch (h) {
                case handle.month:
                    vm.page.infoMonth.data = data;
                    break;
                case handle.adjust:
                	vm.adjustPage.data = data;
                	break;
            }*/

            MainCtrl.unblockUI();
        });
    };
    
 // 页面显示数量改变
    vm.pageSizeChange = function (h) {
        var size = 10;
        var condition = [];
        var url = "";

        if (h == handle.info) {
            size = vm.page.info.pageSize;
            url = vm.page.info.url;
            condition = vm.page.info.condition;
        } else if(h == handle.adjust){
        	size = vm.adjustPage.pageSize;
            url = vm.adjustPage.url;
            condition = vm.adjustPage.condition;
        } else if (h == handle.month) {
        	size = vm.page.inout.pageSize;
            url = vm.page.infoMonth.url;
            condition = vm.page.infoMonth.condition;
        }else {
            size = vm.page.inout.pageSize;
            url = vm.page.inout.url;
            condition = vm.page.inout.condition;
        }

        vm.init(1, size, condition, url, h);
    };
    
    // 分页按钮单击处理
    vm.doPage = function (h, page, pageSize) {
        var url = "";
        var condition = [];

        if (h == handle.info) {
            vm.page.info.pageNo = page;
            vm.page.info.pageSize = pageSize;
            url = vm.page.info.url;
            condition = vm.page.info.condition;
        } else if(h == handle.adjust){
        	vm.adjustPage.pageNo = page;
            vm.adjustPage.pageSize = pageSize;
            url = vm.adjustPage.url;
            condition = vm.adjustPage.condition;
        } else if(h == handle.month){
        	vm.page.infoMonth.pageNo = page;
            vm.page.infoMonth.pageSize = pageSize;
            url = vm.page.infoMonth.url;
            condition = vm.page.infoMonth.condition;
        }else {
            vm.page.inout.pageNo = page;
            vm.page.inout.pageSize = pageSize;
            url = vm.page.inout.url;
            condition = vm.page.inout.condition;
        }

        vm.init(page, pageSize, condition, url, h);
    };
    

    vm.checkType = 1;
    vm.checkDeail = {};
    vm.checkMonthData = function (checkType)
    {
    	// 设置查询条件
        MainCtrl.blockUI({
        	animate: true
        });
        vm.checkType = checkType;
     // 设置查询条件
        var condit = [];
        var material = {};//用物料属性查
        var supplier = {};//用客户属性查
        if(vm.materialNo)
        {
        	condit.push({
                name: 'materialNo',
                value: vm.materialNo
            });
        }
        if(vm.materialName)
        {
        	condit.push({
                name: 'materialName',
                value: vm.materialName
            });
        }
        if(vm.materialTypeId)
        {
        	condit.push({
                name: 'materialTypeId',
                value: vm.materialTypeId
            });
        }
        if(vm.specification)
        {
        	condit.push({
                name: 'specification',
                value: vm.specification
            });
        }
        if(vm.storeHouseId)
        {
        	condit.push({
                name: 'storeHouseId',
                value: vm.storeHouseId
            });
        }

        if (vm.time.start) {
        	condit.push({
                name: "inoutTimeStartQr",
                value: vm.timeTwo.start.valueOf()
            });
        	vm.inoutPlaceQuery.inoutTimeTwoStartQr.value = vm.timeTwo.start.valueOf();
        }
        if (vm.time.end) {
        	condit.push({
                name: "inoutTimeEndQr",
                value: vm.timeTwo.end.valueOf()
            });
        	vm.inoutPlaceQuery.inoutTimeTwoEndQr.value = vm.timeTwo.end.valueOf();
        }
        
        condit.push({
            name: "checkType",
            value: checkType
        });
        reqData = {};
        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condit);
        upida.post("stock/rawmaterial/getStockCheckInfo",reqData).then(function (data) {
            // 初始化数据
           vm.checkDeail.datalist = data;
           // 查询数据
            vm.showCheckDeail();
            MainCtrl.unblockUI();
        });
        
        
    	
    }
    
    // tabs控制
    vm.tabs = {
    		schde: {active:true},
    		checkDeail: {active:false, show: false}
    };
    
  //显示当前盘点的明细
    vm.showCheckDeail = function ()
    {
    	vm.tabs.checkDeail.show = true;
        vm.tabs.checkDeail.active = true;
    }
    
    //隐藏当前盘点的明细
    vm.hiddenCheckDeail = function ()
    {
    	vm.tabs.checkDeail.show = false;
    	vm.tabs.checkDeail.active = false;
    	 $timeout(function(){
    		 vm.tabs.schde.active = true;
         });
    };
    
    vm.stockList = [];
    vm.clickedObj = {};
    vm.openStockDeail = function(row) {
    	vm.clickedObj = row;
    	MainCtrl.blockUI({
            animate: true,
        });
    	if(row.materialKind==100702)
    	{
    		upida.post("stock/materialMonth/getProductStockList",row).then(function(data){
        		vm.stockList = [];
        		if(data && data.length > 0)
        		{
        			vm.stockList = data;
        			/*var obj = {name: '所有', recordId: ''};
                    vm.materialQueryList.splice(0, 0, obj);
                    
                    for(let i=0;i<vm.materialQueryList.length;i++){
                    	if(vm.materialQueryList[i] && vm.materialQueryList[i].recordId == row.recordId){
                    		vm.inoutPlaceQuery.material = vm.materialQueryList[i];
                    		vm.getPlaceDataList();
                    	}
                    }*/
        			
        		}
        		MainCtrl.unblockUI();
         		$("#openProductStockDeail").modal();
        	});
    	}
    	else	
    	{
    		upida.post("stock/materialMonth/getStockList",row).then(function(data){
        		vm.stockList = [];
        		if(data && data.length > 0)
        		{
        			vm.stockList = data;
        			/*var obj = {name: '所有', recordId: ''};
                    vm.materialQueryList.splice(0, 0, obj);
                    
                    for(let i=0;i<vm.materialQueryList.length;i++){
                    	if(vm.materialQueryList[i] && vm.materialQueryList[i].recordId == row.recordId){
                    		vm.inoutPlaceQuery.material = vm.materialQueryList[i];
                    		vm.getPlaceDataList();
                    	}
                    }*/
        			
        		}
        		MainCtrl.unblockUI();
         		$("#openStockDeail").modal();
        	});
    	}
	};
	vm.addPeriodList = [];
	vm.addMonthDataTab = function()
	{
		if(vm.addPeriodList.length==0)
		{
			angular.forEach(vm.periodList,function(p){
				if(p>='202101')
				{
					vm.addPeriodList.push(p);
				}
			});
		}
		$('#addMonthData').modal();
	}
	
	vm.addMonth = function()
	{
		if(!vm.periodAdd||vm.periodAdd=='所有')
		{
			vm.message = "请先选择月份";
        	$('#static').modal();
        	return;
		}
		MainCtrl.blockUI({
            animate: true,
        });
		var param = {};
		param.month = vm.periodAdd;
		upida.post("stock/materialMonth/resetMonthData",param).then(function(data){
			// 初始化数据
            if(data && data=="success"){
            	vm.message = vm.periodAdd+"月结完成";
            	$('#static').modal();
            	vm.periodQuery = vm.periodAdd;
            	vm.groupCheckQuery();
            }else{
            	vm.message = "月结失败";
            	$('#static').modal();
            }
            MainCtrl.unblockUI();
    	});
	}
	
	vm.fomatFloat = function(num)
    {
    	var n = 2
    	var f = parseFloat(num);
	    if(!num || isNaN(f)){
	        return null;
	    }   
	    f = Math.round(num*Math.pow(10, n))/Math.pow(10, n); // n 幂   
	    var s = f.toString();
	    return s;
    }
    
    
    // 加载数据
    function loadData() {
    	 MainCtrl.blockUI({
             animate: true,
         });
    	 vm.queryAll = CommonUtil.dataRangeIsAll("10903", BaseUtil.getMenuList());
    	upida.get("stock/rawmaterial/load/data?queryAll="+vm.queryAll).then(function (data) {
    		
    		 vm.periodList = data.periodList;
             if(vm.periodList && vm.periodList.length > 0)
             {
             	vm.periodList.splice(0,0,'所有');
             }
             
             vm.periodQuery = data.period
    		
            // 加载物料类型
            vm.materialTypeList = data.materialTypeList && data.materialTypeList.length > 0 ? data.materialTypeList : [];
            vm.materialTypeList.splice(0, 0, {value: '所有', recordId: ''});
            
            vm.materialSpecList = data.materialSpecList;
            
            //加载默认仓库
            vm.storehouseList = data.storehouseList && data.storehouseList.length > 0 ? data.storehouseList : [];
            vm.object = {name: '所有', recordId: ''};
            vm.storehouseList.splice(0, 0, vm.object);
            vm.query.storehouse.value = vm.storehouseList[0];
            vm.inoutQuery.storehouse.value = vm.storehouseList[0];
           
            vm.getGroupCenterBillList();
            MainCtrl.unblockUI();
        });
    }
    
    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadData();
    });
}]);
