package com.kyb.pcberp.common.pdf.ext;

import java.io.IOException;
import java.util.Date;

import com.kyb.pcberp.common.utils.FmtUtils;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.BaseFont;

public class ParagraphCn10Bold extends Paragraph
{
    
    private static final long serialVersionUID = -4767036786714471719L;
    
    private static Font font_cn10;
    
    static
    {
        BaseFont bfChinese = null;
        try
        {
            String path = ParagraphCn10Bold.class.getResource("/font/simsun.ttc").getPath();
            bfChinese = BaseFont.createFont(path + ",1", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            
            // String path = ParagraphCn.class.getResource("/font/MSYHMONO.ttf").getPath();
            // bfChinese = BaseFont.createFont(path, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            
            // bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        }
        catch (DocumentException e)
        {
            e.printStackTrace();
            
        }
        catch (IOException e)
        {
            e.printStackTrace();
            
        }
        catch (Exception e)
        {
            
        }
        font_cn10 = new Font(bfChinese, 10, Font.BOLD);
    }
    
    public ParagraphCn10Bold(String value)
    {
        super(value, font_cn10);
    }
    
    public ParagraphCn10Bold(Long value)
    {
        this(String.valueOf(value));
    }
    
    public ParagraphCn10Bold(Integer value)
    {
        this(String.valueOf(value));
    }
    
    public ParagraphCn10Bold(Date value)
    {
        this(FmtUtils.formatDate(value));
    }
}
