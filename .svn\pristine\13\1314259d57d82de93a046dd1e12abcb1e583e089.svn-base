package com.kyb.pcberp.modules.oa.pojo.report;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;

@SuppressWarnings("serial")
public class Oa_dailyShare extends DataEntity<Oa_dailyShare>
{
    private Oa_report report; // 报告ID
    
    private Hr_Employee share; // 报告共享人员/上级员工
    
    private Integer status; // 状态:1:未读,2:已读
    
    public Oa_report getReport()
    {
        return report;
    }
    
    public void setReport(Oa_report report)
    {
        this.report = report;
    }
    
    public Hr_Employee getShare()
    {
        return share;
    }
    
    public void setShare(Hr_Employee share)
    {
        this.share = share;
    }
    
    public Integer getStatus()
    {
        return status;
    }
    
    public void setStatus(Integer status)
    {
        this.status = status;
    }
    
}
