<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="d-flex flex-column flex-root">
    <div class="d-flex flex-row flex-column-fluid page">
        <div class="d-flex flex-column flex-row-fluid wrapper">
            <div class="content d-flex flex-column flex-column-fluid"
                 style="background-image: url(${pageContext.request.contextPath}/assets/global/metronic/assets/media/bg/bgMain.jpg);height: 180px;">
                <div class="subheader subheader-transparent">
                    <div class="container d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <h5 class="text-white my-2 mr-5">我的任务</h5>
                            </div>
                        </div>
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <router-link to="/maintenance" class="btn btn-transparent-warning font-weight-bold mr-2">
                                    返回主页
                                </router-link>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-column-fluid">
                    <div class="container">
                        <div class="row">
                            <div class="col-12">
                                <div class="card card-custom gutter-b">
                                    <div class="card-body d-flex flex-column p-3">
                                        <div class="row pb-3">
                                            <div class="col-12">
                                                <input type="text" class="form-control" placeholder="请输入要搜索的设备信息" v-model="searchInfo" v-on:blur="getMyWaiteRepairList()"/>
                                            </div>
                                        </div>
                                        <template v-for="item in myWaiteRepairList">
                                            <div class="row pb-2 border-bottom" :key="item.recordId">
                                                <div class="col-xl-12">
                                                    <div class="row pt-2 pb-2">
                                                        <div class="col-8">
                                                            {{item.preserveNo}}
                                                        </div>
                                                        <div class="col text-right">
                                                            <span class="badge badge-primary" v-if="item.repairStatus == 1001">未受理</span>
                                                            <span class="badge badge-success" v-if="item.repairStatus == 1001 && !item.materialId">未绑物料</span>
                                                            <span class="badge badge-success" v-if="item.repairStatus == 1002">维修中</span>
                                                            <span class="badge badge-success" v-if="item.repairStatus == 1003">已结束</span>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col">
                                                            申报：<span class="font-weight-bold">{{item.applyCompany ? item.applyCompany.name : null}} - {{item.applyName}}</span>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col">
                                                            维保：<span class="font-weight-bold">{{item.repairCompany ? item.repairCompany.name : null}} <span v-if="item.repairName">- {{item.repairName}}</span></span>
                                                        </div>
                                                    </div>
                                                    <div class="row" v-if="item.materialName">
                                                        <div class="col text-left">
                                                            {{item.materialType}}：<span class="font-weight-bold">{{item.materialName}}</span>&nbsp;
                                                            规格：<span class="font-weight-bold">{{item.specification}}</span>
                                                        </div>
                                                    </div>
                                                    <div class="row" v-if="item.startDate || item.endDate">
                                                        <div class="col">
                                                            维保时间：<span class="font-weight-bold">{{item.startDate}}<span v-if="item.endDate"> 到 {{item.endDate}}</span></span>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col">
                                                            问题描述：<span class="font-weight-bold">{{item.problem}}</span>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col">
                                                            原因：<span class="font-weight-bold">{{item.remark}}</span>
                                                        </div>
                                                    </div>
                                                    <div class="row pt-1 text-right">
                                                        <div class="col">
                                                            <button type="button" class="btn btn-sm btn-primary" v-if="item.repairStatus == 1001 && item.materialId" v-on:click="updateStatus(item,1)">开始</button>
                                                            <button class="btn btn-sm btn-danger" v-if="item.repairStatus == 1002 && item.materialId" v-on:click="updateStatus(item,2)">结束</button>
                                                            <button class="btn btn-sm btn-danger" v-if="item.repairStatus == 1001 && !item.materialId" v-on:click="bindEquipment(item)">绑定设备</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                        <div class="row">
                                            <div class="col-xl-12">
                                                <div style="height: 6rem;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="addBindWaitRepair" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="addBindWaitRepair" aria-hidden="true" style="overflow:auto!important;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <span>绑定设备</span>
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i aria-hidden="true" class="ki ki-close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row pt-3 pb-3">
                        <div class="col-12">
                            <label><span style="color: red;">*</span>设备名称:</label>
                            <input type="text" class="form-control" v-model="waiteRepair.deviceName" disabled/>
                        </div>
                    </div>
                    <div class="row pt-3 pb-3">
                        <div class="col-12">
                            <label>选择设备:</label>
                            <select v-model="waiteRepair.materialId" class="form-control input-icon-sm" data-max-options="6"
                                    data-none-selected-text="选择设备" data-live-search="true" id="materialId" data-size="6">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-light-primary font-weight-bold" v-on:click="saveconfirmButton">保存</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="confirmButtonStatic">
        <div class="modal-dialog">
            <div class="modal-content">
                <!-- Modal body -->
                <div class="modal-body">
                    <div class="col-1 text-center"><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                    <div class="col-11"><p>{{ message }}</p></div>
                </div>
                <!-- Modal footer -->
                <div class="modal-footer">
                    <button type="button" class="btn btn-sm btn-primary" v-on:click="confirmButton">确定</button>
                    <button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>
</div>