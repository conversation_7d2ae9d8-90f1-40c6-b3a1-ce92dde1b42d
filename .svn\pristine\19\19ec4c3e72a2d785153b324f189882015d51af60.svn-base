package com.kyb.pcberp.modules.icloud.erp.approval.utils;

import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.SpringContextHolder;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.icloud.contract.pojo.Icloud_GroupCenter;
import com.kyb.pcberp.modules.icloud.erp.craft.dao.Icloud_ErpCraftDao;
import com.kyb.pcberp.modules.icloud.erp.craft.pojo.Icloud_ContractCraft;
import com.kyb.pcberp.modules.icloud.erp.erp.dao.Icloud_MaterialDao;
import com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_Material;
import com.kyb.pcberp.modules.icloud.purchasing.dao.Icloud_PurchasingDeailDao;

import java.math.BigDecimal;

public class Icloud_ModifyUtilsImpl
{
    private static Icloud_PurchasingDeailDao purchasingDeailDao = SpringContextHolder.getBean(Icloud_PurchasingDeailDao.class);

    private static Icloud_MaterialDao materialDao = SpringContextHolder.getBean(Icloud_MaterialDao.class);

    private static Icloud_ErpCraftDao erpCraftDao = SpringContextHolder.getBean(Icloud_ErpCraftDao.class);

    // 云平台订单审批修改记录
    @SuppressWarnings("unused")
    public static String compareGroupCenter(Icloud_GroupCenter groupCenter)
    {
        StringBuffer sb = new StringBuffer();
        Icloud_GroupCenter oldGc = purchasingDeailDao.getGroupCenterData(groupCenter);
        if(null != oldGc && null != groupCenter)
        {
            // 材料
            String oldSpecification = null;
            if(StringUtils.isNotBlank(oldGc.getMaterialId()))
            {
                Icloud_Material oldMat = materialDao.get(new Icloud_Material(oldGc.getMaterialId()));
                if(null != oldMat)
                {
                    oldSpecification = oldMat.getSpecification();
                }
            }
            String newSpecification = null;
            if(StringUtils.isNotBlank(groupCenter.getMaterialId()))
            {
                Icloud_Material newMat = materialDao.get(new Icloud_Material(groupCenter.getMaterialId()));
                if(null != newMat)
                {
                    newSpecification = newMat.getSpecification();
                }
            }
            sb.append(compareString("材料", oldSpecification, newSpecification));

            // 厂编
            if(StringUtils.isNotBlank(groupCenter.getGenCraftNo()))
            {
                sb.append(compareString("厂编", oldGc.getGenCraftNo(), groupCenter.getGenCraftNo()));
            }
            else if(StringUtils.isNotBlank(groupCenter.getNotiCraftNo()))
            {
                String oldCraftNo = null;
                Icloud_ContractCraft oldCraft = new Icloud_ContractCraft();
                oldCraft.setRecordId(oldGc.getNotiCraftNo());
                oldCraft = erpCraftDao.get(oldCraft);
                if(null != oldCraft)
                {
                    oldCraftNo = oldCraft.getNo();
                }

                String newCraftNo = null;
                Icloud_ContractCraft newCraft = new Icloud_ContractCraft();
                newCraft.setRecordId(groupCenter.getNotiCraftNo());
                newCraft = erpCraftDao.get(newCraft);
                if(null != newCraftNo)
                {
                    newCraftNo = newCraft.getNo();
                }
                sb.append(compareString("厂编", oldCraftNo, newCraftNo));
            }
            // 占用数量
            sb.append(compareString("占用数量", oldGc.getOccupiedStock(), groupCenter.getOccupiedStock()));
            // 在途数量
            sb.append(compareInteger("在途数量", oldGc.getUsePurchStocks(), groupCenter.getUsePurchStocks()));
            // 申购数量
            sb.append(compareInteger("申购数量", oldGc.getReplyStock(), groupCenter.getReplyStock()));
            // 余料数量
            sb.append(compareBigDecimal("余料数量", oldGc.getExcessStock(), groupCenter.getExcessStock()));
            // 加急
            String oldUrgentFlagValue = null;
            if(StringUtils.isNotBlank(oldGc.getUrgentFlag()) && "1".equals(oldGc.getUrgentFlag()))
            {
                oldUrgentFlagValue = "加急";
            }
            else
            {
                oldUrgentFlagValue = "正常";
            }
            String newUrgentFlagValue = null;
            if(StringUtils.isNotBlank(groupCenter.getUrgentFlag()) && "1".equals(groupCenter.getUrgentFlag()))
            {
                newUrgentFlagValue = "加急";
            }
            else
            {
                newUrgentFlagValue = "正常";
            }
            sb.append(compareString("加急", oldUrgentFlagValue, newUrgentFlagValue));
            // 交期
            String oldEstimateDate = null;
            if(null != oldGc.getEstimateDate())
            {
                oldEstimateDate = DateUtils.formatDate(oldGc.getEstimateDate(), "yyyy-MM-dd");
            }
            String newEstimateDate = null;
            if(null != groupCenter.getEstimateDate())
            {
                newEstimateDate = DateUtils.formatDate(groupCenter.getEstimateDate(), "yyyy-MM-dd");
            }
            sb.append(compareString("交期", oldEstimateDate, newEstimateDate));
            // 测试架费
            sb.append(compareBigDecimal("外发测试架费", groupCenter.getPrdTestShelfFeeCopy(), groupCenter.getPrdTestShelfFee()));
            // 工程费
            sb.append(compareBigDecimal("外发工程费", groupCenter.getPrdEngineeringFeeCopy(), groupCenter.getPrdEngineeringFee()));
            // 模具费
            sb.append(compareBigDecimal("外发模具费", groupCenter.getPrdMouldFeeCopy(), groupCenter.getPrdMouldFee()));
            // 销售单价
            sb.append(compareBigDecimal("销售单价", oldGc.getPrice(), null));
            // 龙南单价
            sb.append(compareBigDecimal("龙南单价", oldGc.getLnPrice(), groupCenter.getLnPrice()));
            // 江西单价
            sb.append(compareBigDecimal("江西单价", oldGc.getJxPrice(), groupCenter.getJxPrice()));
        }
        return sb.toString();
    }

    // 比较字符串
    public static String compareString(String name, String beforeChange, String afterChange)
    {
        String result = "";
        if (StringUtils.isNotBlank(beforeChange) && StringUtils.isNotBlank(afterChange))
        {
            if (!beforeChange.equals(afterChange))
            {
                result = name + ":“" + beforeChange + "”变更为“" + afterChange + "”;";
            }
        }
        else if (StringUtils.isNotBlank(beforeChange))
        {
            result = name + ":“" + beforeChange + "”;";
        }
        else if (StringUtils.isNotBlank(afterChange))
        {
            result = name + ":“" + afterChange + "”;";
        }
        return result;
    }

    // 比较数字
    public static String compareInteger(String name, Integer beforeChange, Integer afterChange)
    {
        String result = "";
        if (null != beforeChange && null != afterChange)
        {
            if (beforeChange.compareTo(afterChange) != 0)
            {
                result = name + ":“" + beforeChange + "”变更为“" + afterChange + "”;";
            }
        }
        else if (null != beforeChange)
        {
            result = name + ":“" + beforeChange + "”;";
        }
        else if (null != afterChange)
        {
            result = name + ":“" + afterChange + "”;";
        }
        return result;
    }

    // 比较金额
    public static String compareBigDecimal(String name, BigDecimal beforeChange, BigDecimal afterChange)
    {
        String result = "";
        if (null != beforeChange && null != afterChange)
        {
            if (beforeChange.compareTo(afterChange) != 0)
            {
                result = name + ":“" + beforeChange + "”变更为“" + afterChange + "”;";
            }
        }
        else if (null != beforeChange)
        {
            result = name + ":“" + beforeChange + "”;";
        }
        else if (null != afterChange)
        {
            result = name + ":“" + afterChange + "”;";
        }
        return result;
    }
}
