<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.purch.dao.MaterialRejectDao">
    
	<select id="findList" resultType="MaterialReject">
		SELECT 
			a.*,
			a.companyId AS 'company.recordId',
			mc.`no` AS 'customer.no',
			mc.`name` AS 'customer.name',
			mc.recordId AS 'customer.recordId',
			mc.checkDate AS 'customer.checkDate',
			smc.`no` AS 'materialContractDetail.materialContract.no',
			smcd.recordId AS 'materialContractDetail.recordId',
			smcd.quantity AS 'materialContractDetail.quantity',
			smcd.replenishQty AS 'materialContractDetail.replenishQty',
			ms.`no` AS 'supplier.no',
			ms.shortName AS 'supplier.name',
			ms.recordId AS 'supplier.recordId',
			su.userName AS 'operator.userName',
			su2.userName AS 'createdBy.userName',
			su2.recordId AS 'createdBy.recordId',
			mm.no AS 'materialContractDetail.materialNo',
			mm.name AS 'material.name',
			mm.recordId AS 'materialContractDetail.materialId',
			pp.no AS 'purchasingNo'
		FROM pu_material_reject a
		LEFT JOIN sl_material_contract_detail smcd ON smcd.recordId = a.purchasingDetailId
		LEFT JOIN sl_material_contract smc ON smc.recordId = smcd.purchasingId
		LEFT JOIN md_customer mc ON mc.recordId = smc.customerId
		LEFT JOIN pu_purchasing_detail ppd ON ppd.contractDeailId = smcd.recordId
		LEFT JOIN pu_purchasing pp ON pp.recordId = ppd.purchasingId
		LEFT JOIN md_supplier ms ON pp.supplierId = ms.recordId 
		LEFT JOIN sm_user su ON su.recordId = a.applicant
		LEFT JOIN sm_user su2 ON su2.recordId = a.createdBy
		LEFT JOIN md_material mm ON mm.recordId = smcd.materialId
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
			<if test="no!=null and no!=''">
				AND REPLACE(a.no," ","") like CONCAT('%', REPLACE(#{no}," ",""), '%')
			</if>
			<if test="contractNo!=null and contractNo!=''">
				AND REPLACE(smc.no," ","") like CONCAT('%', REPLACE(#{contractNo}," ",""), '%')
			</if>
			<if test="customerNo!=null and customerNo!=''">
				AND REPLACE(mc.no," ","") like CONCAT('%', REPLACE(#{customerNo}," ",""), '%')
			</if>
			<if test="sentTimeStartQr!=null and sentTimeStartQr!=''">
				AND a.applyDate <![CDATA[>=]]> #{sentTimeStartQr}
			</if>
			<if test="sentTimeEndQr!=null and sentTimeEndQr!=''">
				AND a.applyDate <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
			INSERT INTO pu_material_reject(
			companyId,
			no,
			purchasingDetailId,
			rejectCause,
			applyDate,
			redeliverDate,
			materialId,
			quantity,
			price,
			amount,
			desireTreatment,
			actualRecQty,
			actualAmount,
			applicant,
			status,
			receivedDate,
			currencyType,
			receivedNotes,
			sourceId,
			activeFlag,
			createdBy,
			createdDate,
			remark,
			supplierId,
			satisfactionAmount,
			replenishQty
			) VALUES (
				#{company.recordId},
				#{no},
				#{purchasingDetailId},
				#{rejectCause},
				#{applyDate},
				#{redeliverDate},
				#{materialId},
				#{quantity},
				#{price},
				#{amount},
				#{desireTreatment},
				#{actualRecQty},
				#{actualAmount},
				#{createdBy.recordId},
				#{status},
				#{receivedDate},
				#{currencyType},
				#{receivedNotes},
				#{sourceId},
				1,
				#{createdBy.recordId},
				#{createdDate},
				#{remark},
				#{supplier.recordId},
				#{satisfactionAmount},
				#{replenishQty}
			)
	</insert>
	
	
	
	<update id="update">
		UPDATE pu_material_reject SET 	
			rejectCause = #{rejectCause},
			applyDate  = #{applyDate},
			quantity = #{quantity},
			amount = #{amount},
			status = #{status},
			sourceId  = #{sourceId},
			remark = #{remark},
			satisfactionAmount = #{satisfactionAmount},
			replenishQty =  #{replenishQty},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE pu_material_reject SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="get" resultType="MaterialReject">
		SELECT * FROM pu_material_reject WHERE recordId = #{recordId} AND activeFlag = 1
	</select>
	
	<select id="getcurrentMaxNo" resultType="String">
		SELECT `no` FROM pu_material_reject ORDER BY createdDate DESC LIMIT 1
	</select>
	
	<select id="getPurchasingDetail" resultType="PurchasingDetail">
		SELECT ppd.*,
			ppd.materialId AS 'material.recordId',
			pp.supplierId AS 'purchasing.supplier.recordId',
			ms.checkDate AS 'purchasing.supplier.checkDate'
		FROM pu_material_reject a
		LEFT JOIN sl_material_contract_detail smcd ON a.purchasingDetailId = smcd.recordId
		LEFT JOIN pu_purchasing_detail ppd ON ppd.contractDeailId = smcd.recordId
		LEFT JOIN pu_purchasing pp ON pp.recordId = ppd.purchasingId
		LEFT JOIN md_supplier ms ON ms.recordId = pp.supplierId
		WHERE a.recordId = #{recordId}
	</select>
	
	<update id="updateContractReplenishQty">
		UPDATE sl_material_contract_detail SET replenishQty = (IFNULL(replenishQty,0)+#{replenishQty}) WHERE recordId = #{purchasingDetailId}
	</update>
	
	<select id="getRejectData" resultType="MaterialReject">
		SELECT
			a.recordId,
			a.no,
			a.quantity,
			IFNULL(a.replenishQty,0) AS "replenishQty",
			IFNULL((
				SELECT 
					SUM(quantity) 
				FROM st_material_store 
				WHERE companyId = a.companyId AND activeFlag = 1 AND `status` = 99999901 AND inOutType = 8 AND rejectId = a.recordId
			),0) AS "sentOutQty",
			a.status
		FROM pu_material_reject a
		WHERE a.activeFlag = 1 AND FIND_IN_SET(a.recordId,#{reject.recordId})
		ORDER BY a.applyDate ASC
	</select>
	
	<update id="updateStatus">
		UPDATE pu_material_reject SET
			status = #{status}
		WHERE recordId = #{recordId}
	</update>
	
</mapper>