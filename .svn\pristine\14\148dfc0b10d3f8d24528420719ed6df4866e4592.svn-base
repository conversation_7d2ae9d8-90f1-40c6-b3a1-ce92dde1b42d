const report_listTwo = {
    template: '#report_listTwo',
    created () {
        this.getOrderDetail();
    },
    computed: {

    },
    mounted:function(){

    },
    watch: {
    },
    data(){
        return {
            dateAllDetail:[],
            reportName:"",
            deptName:"",
            craftNo:"",
            customerModel:"",
            customerPro:"",
            customerName:"",
            paymentTerm:""
        }
    },
    methods: {
        //明细列表
        getOrderDetail:function(){
            const query ={}
            let report = eval('(' + window.localStorage.getItem('reportDetail') + ')');
            query.name = report.name;
            query.dateType = report.dateType;
            query.phone = report.phone;
            query.departName = report.departName;
            query.analyticalNum = report.analyticalNum;
            query.dimensionType = report.dimensionType;
            query.sentTimeStartQr = report.startTime;
            query.sentTimeEndQr = report.endTime;
            query.craftNo = this.craftNo;
            query.customerModel = this.customerModel;
            query.customerPro = this.customerPro;


            this.reportName =report.reportName;
            this.deptName =  report.departName;
            if (this.reportName == "回款"){
               query.customerName = this.customerName;
               query.paymentTerm = this.paymentTerm;
            }
            const _this = this;
            $.ajax({
                type:"post",
                url:ctx + "/f/wechat/kybReport/getOrderDetail",
                data:JSON.stringify(query),
                contentType:"application/json",
                success:function(data)
                {
                    _this.dateAllDetail = data.allReportDetail;
                }
            })
        },
    }
}