package com.kyb.pcberp.modules.purch.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.contract.entity.MaterialContract;
import com.kyb.pcberp.modules.contract.entity.MaterialContractDetail;
import com.kyb.pcberp.modules.purch.entity.GroupSupplyChainProcess;
import com.kyb.pcberp.modules.purch.entity.Purchasing;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface GroupSupplyChainProcessDao extends CrudDao<GroupSupplyChainProcess>
{

    List<GroupSupplyChainProcess> maxTunList(GroupSupplyChainProcess groupSupplyChainProcess);

    void updateStatus(GroupSupplyChainProcess groupSupplyChainProcess);

    Integer getMaxTun(GroupSupplyChainProcess groupSupplyChainProcess);

    void updateStatusTwo(GroupSupplyChainProcess groupSupplyChainProcess);

    List<GroupSupplyChainProcess> getSupplierCompanyList(GroupSupplyChainProcess groupSupplyChainProcess);

    Integer getTailCount(@Param("bindErpId") String bindErpId);

}
