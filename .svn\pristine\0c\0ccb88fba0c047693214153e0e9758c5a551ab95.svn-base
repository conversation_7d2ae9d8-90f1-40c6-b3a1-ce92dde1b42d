'use strict';
/* Setup general page controller */
kybApp.controller('DashboardController', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil', 'BaseUtil','$state',
    function ($rootScope, $scope, upida, $timeout, CommonUtil, BaseUtil,$state) {
        $scope.$on('$viewContentLoaded', function () {
            MainCtrl.initAjax();
            $rootScope.settings.layout.paconfirmButtonOpengeBodySolid = false;
            $rootScope.settings.layout.pageSidebarClosed = false;

        });
        const vm = this;
        vm.departId = "";
        vm.workStatus = "";
        vm.workType = "";
        vm.custName = "";
        vm.custPo = "";
        vm.custCraft = "";
        vm.deptName = "";
        vm.clicks = true;

        // 时间范围的选项
        vm.rangeOptions = {
            // format: "YYYY-MM-DD",
            startDate: new Date((new Date).setMonth(((new Date).getMonth() - 3))),
            minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
        };

        // 时间范围的Model
        vm.time = {
            start: {},
            end: {}
        };

        vm.initDate = function(date) {
            if(date == "") {
                vm.rangeOptions = {
                    // format: "YYYY-MM-DD",
                    startDate: new Date(vm.rangeOptions.startDate),
                    minDate: new Date(new Date(vm.rangeOptions.minDate).setFullYear(new Date(vm.rangeOptions.minDate).getFullYear() - 5))
                };
                vm.time = {
                    start: vm.rangeOptions.startDate,
                    end: new Date()
                }
            }
        };
        vm.user = {};
        vm.orderList = [];
        vm.produceList = [];
        vm.processList = [];
        vm.allProcessList = {};
        vm.showList = [];
        vm.notice = [];

        // 权限
        vm.right = {};

        //设置工作台各模块的权限列表
        vm.unreviewedright = {};
        vm.examineingright = {};
        vm.dataright = {};
        vm.affirmright = {};
        vm.productionright = {};
        vm.feedgooutright = {};
        vm.processright = {};
        vm.deliverygooutright = {};
        vm.balanceright = {};

        vm.loadData = function (){
            vm.orderList = [];
            vm.produceList = [];
            vm.processList = [];
            vm.allProcessList = {};
            vm.showList = [];

            const query = {};
            query.departId = vm.departId;
            query.workStatus = vm.workStatus;
            query.workType = vm.workType;
            query.custName = vm.custName;
            query.custPo = vm.custPo;
            query.custCraft = vm.custCraft;
            query.deptName = vm.deptName;
            upida.post("order/contract/getOrderList", query).then(function(data){
                vm.produceList = eval("(" + data.produceList + ")");
                vm.processList = eval("(" + data.processList + ")");
                setRole(vm.processList);
                //将在线结存produceList也放入赋值权限方法进行判断
                setRole(vm.produceList);
                vm.url = vm.processList[0].url;
                vm.orderList = eval("(" + data.msgList + ")");
                for (let i=0;i<vm.processList.length;i++){
                    if (vm.processList[i].orderType == 9){
                        vm.allProcessList = vm.processList[i];
                        break;
                    }
                }
                vm.showList = vm.orderList;
                vm.initDeliveryEcharts();
                vm.initNotice();
                MainCtrl.unblockUI();
            });
        };




        //根据集合中orderType类型，判断是否赋予可见的权限
        function setRole(list)
        {
            if(!list || list.length == 0)
            {
                return;
            }
            for(let obj of list)
            {
                //权限默认为false不可见
                obj.roleFlag = false;
                if(!obj.orderType)
                {
                    continue;
                }
                switch (obj.orderType)
                {
                    // 订单未审核
                    case "1":
                        if(vm.unreviewedright && vm.unreviewedright.view)
                        {
                            obj.roleFlag = true;
                        }
                        break;
                    // 订单审批中
                    case "2":
                        if(vm.examineingright && vm.examineingright.view)
                        {
                            obj.roleFlag = true;
                        }
                        break;
                    // 工程资料
                    case "3":
                        if(vm.dataright && vm.dataright.view)
                        {
                            obj.roleFlag = true;
                        }
                        break;
                    // 材料确认
                    case "4":
                        if(vm.affirmright && vm.affirmright.view)
                        {
                            obj.roleFlag = true;
                        }
                        break;
                    // 生产投料
                    case "5":
                        if( vm.productionright && vm.productionright.view)
                        {
                            obj.roleFlag = true;
                        }
                        break;
                    // 投料出库
                    case "6":
                        if(vm.feedgooutright && vm.feedgooutright.view)
                        {
                            obj.roleFlag = true;
                        }
                        break;
                    // 生产工序
                    case "7":
                        if(vm.processright && vm.processright.view)
                        {
                            obj.roleFlag = true;
                        }
                        break;
                    // 送货出库
                    case "8":
                        if(vm.deliverygooutright && vm.deliverygooutright.view)
                        {
                            obj.roleFlag = true;
                        }
                        break;
                    // 在线结存
                    case "9":
                        if(vm.balanceright && vm.balanceright.view)
                        {
                            obj.roleFlag = true;
                        }
                        break;
                }
            }
        }

        vm.orderType = "";
        vm.orderName = "";
        vm.url;
        vm.setShowList = function (row){
            if (vm.orderName == row.name){
                vm.orderName = "";
                vm.orderType = "";
                vm.showList = vm.orderList;
            }else{
                vm.orderName = row.name;
                vm.orderType = row.orderType;
                vm.showList = row.list;
            }
            vm.url = row.url;
        };

        vm.orderOpen = function(){
            if(vm.url){
                window.open(vm.url);
            }
        }

        vm.initNotice = function(){
            upida.get("notice/getNoticeNew").then(function(data){
                vm.notice = data;
            })
        }

        vm.initDeliveryEcharts = function(){
            if ($('#report').is(':visible')) {
                echarts.dispose(document.getElementById('report'));
                if (vm.allProcessList.nomalArea || vm.allProcessList.warnArea || vm.allProcessList.urgArea || vm.allProcessList.dangerArea){
                    const myChart = echarts.init(document.getElementById('report'));
                    const option = {
                        tooltip: {
                            trigger: 'item'
                        },
                        legend: {
                            bottom: '10',
                            left: 'center'
                        },
                        color:['#32DC32','#FFFF00','#DAA520','#DC143C'],
                        series: [
                            {
                                name: '在线统计',
                                type: 'pie',
                                radius: '50%',
                                avoidLabelOverlap: false,
                                label: {
                                    show: false,
                                    position: 'center'
                                },
                                labelLine: {
                                    show: false
                                },
                                data: [
                                    { value: vm.allProcessList.nomalArea, name: '正常'},
                                    { value: vm.allProcessList.warnArea, name: '有风险' },
                                    { value: vm.allProcessList.urgArea, name: '加急' },
                                    { value: vm.allProcessList.dangerArea, name: '逾期' }
                                ]
                            }
                        ]
                    };
                    // 使用刚指定的配置项和数据显示图表。
                    myChart.setOption(option);
                }
            }
        };

        function loadRight() {
            vm.user = BaseUtil.getUser();
            /*vm.loadData();*/
            vm.queryAll = CommonUtil.dataRangeIsAll("115", BaseUtil.getMenuList());
            MainCtrl.blockUI({
                animate: true,
            });
            upida.get("common/rightall?prefix=workbench").then(function(data){
                vm.right.view = data.view;
                vm.right.edit = data.edit;
                vm.right.manage = data.manage;
                if(data || data.view)
                {
                    vm.initDate("");
                    vm.stageWorkData();
                }
                MainCtrl.unblockUI();
            });
        };


        let unreviewedquery = false;
        let examineingquery = false;
        let dataquery = false;
        let affirmquery = false;
        let feedgooutquery = false;
        let processquery = false;
        let productionquery = false;
        let deliverygooutquery = false;
        let balancequery = false;
        let menusList = BaseUtil.getMenuList() ;
        let queryAlls;

        //获取工作台权限
//         function loadRight() {
//             // vm.user = BaseUtil.getUser();
//             var nodeIds = "11501"+","
//                 +"11502"+","
//                 +"11503"+","
//                 +"11504"+","
//                 +"11505"+","
//                 +"11506"+","
//                 +"11507"+","
//                 +"11508"+","
//                 +"11509" ;
//             let nodeSplit = nodeIds.split(",");
//             for (var i = 0; i<nodeSplit.length; i++){
//                 queryAlls = CommonUtil.dataRangeIsAll( nodeSplit[i],menusList);
//                 switch (nodeSplit)
//                 {
//                     //订单未审核
//                     case "11501":
//                         unreviewedquery = queryAlls;
//                         break;
//                     //订单审批中
//                     case "11502":
//                         examineingquery = queryAlls;
//                         break;
//                     //工程资料
//                     case "11503":
//                         dataquery = queryAlls;
//                         break;
//                     //材料确认
//                     case "11504":
//                         affirmquery = queryAlls;
//                         break;
//                     //生产投料
//                     case "11505":
//                         feedgooutquery = queryAlls;
//                         break;
//                     //投料出库
//                     case "11506":
//                         processquery = queryAlls;
//                         break;
//                     //生产工序
//                     case "11507":
//                         productionquery = queryAlls;
//                         break;
//                     //送货出库
//                     case "11508":
//                         deliverygooutquery = queryAlls;
//                         break;
//                     //在线结存
//                     case "11509":
//                         balancequery = queryAlls;
//                         break;
//                 }
//             }
//
// /*            MainCtrl.blockUI({
//                 animate: true,
//             });*/
//             let workStr = "work:unreviewed" + ","
//                 +"work:examineing" + ","
//                 +"work:data" + ","
//                 +"work:affirm" + ","
//                 +"work:production" + ","
//                 +"work:feedgoout" + ","
//                 +"work:process" + ","
//                 +"work:deliverygoout" + ","
//                 +"work:balance" ;
//             upida.get("common/righworktall?prefixs=" + workStr).then(function(data)
//             {
//                 vm.unreviewedright = data.unreviewed;
//                 vm.examineingright = data.examineing;
//                 vm.dataright =  data.data;
//                 vm.affirmright =  data.affirm;
//                 vm.productionright =  data.production;
//                 vm.feedgooutright =  data.feedgoout;
//                 vm.processright =  data.process;
//                 vm.deliverygooutright =  data.deliverygoout;
//                 vm.balanceright =  data.balance;
//                 setWokeType();
//                 // vm.loadData();
//             });
//         };

        function setStr(wokeType,value)
        {
            if(wokeType)
            {
                wokeType = wokeType +','+ value;
            }
            else
            {
                wokeType = value;
            }
            return wokeType;
        }

        function setWokeType()
        {
            let wokeType = null;
            if(vm.unreviewedright && vm.unreviewedright.view)
            {
                wokeType = setStr(wokeType,1);
            }
            if(vm.examineingright && vm.examineingright.view)
            {
                wokeType = setStr(wokeType,2);
            }
            if(vm.dataright && vm.dataright.view)
            {
                wokeType = setStr(wokeType,3);
            }
            if(vm.affirmright && vm.affirmright.view)
            {
                wokeType = setStr(wokeType,4);
            }
            if(vm.productionright && vm.productionright.view)
            {
                wokeType = setStr(wokeType,5);
            }
            if(vm.feedgooutright && vm.feedgooutright.view)
            {
                wokeType = setStr(wokeType,6);
            }
            if(vm.processright && vm.processright.view)
            {
                wokeType = setStr(wokeType,7);
            }
            if(vm.deliverygooutright && vm.deliverygooutright.view)
            {
                wokeType = setStr(wokeType,8);
            }
            vm.workType = wokeType ;
        }

        vm.notice = function(){
            window.location.href='#/notice';
        };

        // 分页数据
        vm.page1 = {};
        // 显示数据大小
        vm.page1.pageSizeOptions = [5, 10, 30, 50];
        // 字典项分页数据
        vm.page1.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
        vm.page1.pageSize = 5;
        vm.page1.pageNo = 1;
        vm.page1.url = "notice/page";
        vm.page1.condition = []; // 条件
        vm.noticeOpen = function () {
            vm.page1.data = {};
            vm.page1.pageNo = 1;
            vm.init1(vm.page1.pageNo, vm.page1.pageSize, [], vm.page1.url);
            $('#noticeStatic').modal();
        };
        vm.init1 = function (no, size, condition, url) {
            // 请求数据
            var reqData = {};
            reqData.pageNo = no;
            reqData.pageSize = size;
            reqData.queryAll = vm.queryAll

            MainCtrl.blockUI({
                animate: true,
            });

            // 请求分页数据
            upida.post(url, reqData).then(function (result) {

                var data = {};

                // 如果结果为空
                if (typeof result === 'undefined' || typeof result.list === 'undefined') {

                    data.pageNo = 1;
                    data.pageSize = 10;
                    data.list = [];
                    data.startCount = 0;
                    data.endCount = 0;
                } else {
                    data = result;
                    // 计算开始数
                    data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                    // 计算结束数
                    data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
                }
                if(!vm.page1.data.list){
                    vm.page1.data = data;
                }else if(data.list){
                    for (var i = 0; i < data.list.length; i++) {
                        vm.page1.data.list.push(data.list[i]);
                    }
                }
                MainCtrl.unblockUI();
            });
        };

        vm.doQuery1 = function () {
            vm.page1.pageNo = vm.page1.pageNo + 1;
            // 查询数据
            vm.init1(vm.page1.pageNo, vm.page1.pageSize, vm.page1.condition, vm.page1.url);
        };
        //准备阶段
        vm.stageKind = "0";
        vm.statusA = "1001";
        vm.batchNoA = "";
        vm.nameA = "";
        vm.stageDataList = [];
        vm.stageWorkData = function(){
            let stageData = {};
            stageData.userId = vm.user.recordId;
            stageData.type = vm.stageKind;
            stageData.status = vm.statusA;
            stageData.batchNo = vm.batchNoA;
            stageData.name = vm.nameA;
            if(vm.time.start) {
                stageData.startDate = vm.time.start.valueOf();
            }
            if(vm.time.end) {
                stageData.endDate = vm.time.end.valueOf();
            }
            MainCtrl.blockUI({
                animate: true,
            });
            upida.post("batchDetail/stage/stageWorkData", stageData).then(function(data){
                vm.stageDataList = data;
                if(vm.stageDataList && vm.stageDataList.length > 0)
                {
                    for(let stageData of vm.stageDataList)
                    {
                        let confirmFlag = false;
                        if(stageData.batchDetailSetupList && stageData.batchDetailSetupList.length > 0)
                        {
                            for(let batchDetail of stageData.batchDetailSetupList)
                            {
                                if(batchDetail.status == 1001)
                                {
                                    confirmFlag = true;
                                    break;
                                }
                            }
                        }
                        stageData.confirmFlag = confirmFlag;
                    }
                }
                MainCtrl.unblockUI();
            });
        };

        vm.stageData = {};
        vm.confirmFlag = null;
        vm.confirmButtonOpen = function(row,num)
        {
            if(!num || !row)
            {
                return;
            }
            vm.stageData = angular.copy(row);

            vm.confirmFlag = num;
            // 确认
            if(num == 1)
            {
                if(!vm.stageData.materialOccupyList || vm.stageData.materialOccupyList.length == 0)
                {
                    vm.message = "先配料才能确认!";
                    $('#static').modal();
                    return;
                }
                vm.message = "您确定要确认？";
            }
            // 取消确认
            else if(num == 2)
            {
                vm.message = "您确定要取消确认？";
            }
            $('#confirmButtonStatic').modal();
        }

        vm.batchDetailSetup = {};
        vm.leftoverMatList = [];
        vm.confirmButton = function()
        {
            if(!vm.confirmFlag || !vm.stageData)
            {
                vm.message = "页面失效，请刷新重试！";
                $('#static').modal();
                return;
            }
            // 确认
            if(vm.confirmFlag == 1)
            {
                vm.stageData.status = 1002;
            }
            // 取消确认
            else if(vm.confirmFlag == 2)
            {
                vm.stageData.status = 1001;
            }
            MainCtrl.blockUI({
                animate: true,
            });
            upida.post("batchDetail/stage/confirmButton", vm.stageData).then(function(data){
                if(data.result == "failQty")
                {
                    vm.batchDetailSetup = data.batchDetailSetup;
                    vm.leftoverMatList = data.leftoverMatList;
                    $('#useSurplusStatic').modal();
                }
                else if(data.result == "useAgeDatafail")
                {
                    vm.groupCenterData = data.groupCenter;
                    vm.groupCenterData.nowDate = data.nowDate;
                    vm.message = data.message;
                    $('#useAgeDataStatic').modal();
                }
                else
                {
                    vm.message = data.message;
                    $('#static').modal();
                    if(data.result == "success")
                    {
                        vm.stageData = {};
                        vm.stageWorkData();
                    }
                }
                MainCtrl.unblockUI();
            });
        }

        vm.selectSurplustData = function()
        {
            let qtyPnlT = Number(vm.batchDetailSetup.qtyPnlT);
            let stocks = Number(vm.batchDetailSetup.stocks);
            if((qtyPnlT - stocks) > Number(vm.batchDetailSetup.surplusMat.stocks))
            {
                vm.message = "余料库存不能低于批次明细大板数-可用库存！";
                vm.batchDetailSetup.surplusMat = {};
                $('#static').modal();
                return;
            }
        }

        vm.saveMaterialOccupy = function()
        {
            if(!vm.batchDetailSetup.surplusMat || !vm.batchDetailSetup.surplusMat.recordId)
            {
                vm.message = "请选择余料！";
                $('#static').modal();
                return;
            }
            MainCtrl.blockUI({
                animate: true,
            });
            upida.post("batchDetail/stage/saveMaterialOccupy", vm.batchDetailSetup).then(function(data){
                vm.message = data.message;
                $('#static').modal();
                $('#useSurplusStatic').modal('hide');
                if(data.result == "success")
                {
                    vm.batchDetailSetup = {};
                    vm.stageWorkData();
                }
                MainCtrl.unblockUI();
            });
        };
        vm.allPageChecked = false;
        vm.prepareDataList = [];
        vm.selectAllData = function(){
            if (!vm.stageDataList){
                return;
            }
            angular.forEach(vm.stageDataList,function(row){
                row.checked = !vm.allPageChecked;
                vm.getPrepareListA(row);
            });
        };
        vm.getPrepareListA = function(row)
        {
            if(!row || !row.batchDetailSetupList)
            {
                return;
            }
            for(let batchDetailSetup of row.batchDetailSetupList)
            {
                batchDetailSetup.checked = row.checked;
            }
        };
        vm.prepareConfirmNoti = function(prepareDataList){
            if(prepareDataList)
            {
                vm.prepareDataList = prepareDataList;
            }
            if(!vm.prepareDataList || vm.prepareDataList.length == 0)
            {
                vm.message = "请选择要确认的任务";
                $('#static').modal();
                return;
            }
            for (let i = 0; i<vm.prepareDataList.length; i++){
                if (vm.prepareDataList[i].status == '1002'){
                    vm.message = "被选中任务已确认";
                    $('#static').modal();
                    return;
                }else{
                    vm.prepareDataList[i].status = '1002';
                }
            }
            vm.clicks = false;
            MainCtrl.blockUI({
                animate: true,
            });
            upida.post("batchDetail/stage/prepareConfirmData",vm.prepareDataList).then(function (data) {
                if(data.result == "failQty")
                {
                    vm.batchDetailSetup = data.batchDetailSetup;
                    vm.leftoverMatList = data.leftoverMatList;
                    $('#useSurplusStatic').modal();
                }
                else if(data.result == "useAgeDatafail")
                {
                    vm.groupCenterData = data.groupCenter;
                    vm.groupCenterData.nowDate = data.nowDate;
                    vm.message = data.message;
                    $('#useAgeDataStatic').modal();
                }
                else
                {
                    vm.message = data.message;
                    $('#static').modal();
                    if(data.result == "success")
                    {
                        vm.stageData = {};
                        vm.stageWorkData();
                    }
                }
                vm.clicks = true;
                MainCtrl.unblockUI();
            });
        };

        vm.showFeed = function(item)
        {
            item.showFlag = !item.showFlag;
        }

        vm.showFlag = false;
        vm.allShowFlag = function()
        {
            vm.showFlag = !vm.showFlag;
            for(let obj of vm.stageDataList)
            {
                obj.showFlag = vm.showFlag;
            }
        }

        vm.batchDetailSetupList = [];
        vm.dosingMaterialList = [];
        vm.needQty = 0;
        vm.businessChangeFlag = false;
        vm.sumDosingPcs = 0;
        vm.feedNo = "";
        vm.notiNo = "";
        vm.craftNo = "";
        vm.batchedCost = 0;
        vm.dosingMaterialList = [];
        vm.starAniseLength = 0;
        vm.starAniseWidth = 0;
        vm.useAgeData = "";
        vm.urgentFlag = "";
        vm.materialUse = "";
        vm.dosingOpen = function(row,item)
        {
            vm.sumDosingPcs = 0;
            vm.sumPcsQty = 0;
            vm.selectMatList = [];
            if(!row)
            {
                return;
            }
            for(let craft of vm.craftList)
            {
                craft.flag = true;
            }
            vm.stageData = row;
            let batchDetailSetupList = [];
            if(item && item.status == 1001)
            {
                item.materialOccupyList = [];
                batchDetailSetupList.push(item);
            }
            else
            {
                let flag = false;
                if(row.batchDetailSetupList && row.batchDetailSetupList.length > 0)
                {
                    // 验证只有板材确认才能配料
                    let flagTwo = false;
                    for(let batchDetailSetup of row.batchDetailSetupList)
                    {
                        if(batchDetailSetup.checked && batchDetailSetup.status == 1001 && batchDetailSetup.name != "板材确认")
                        {
                            flagTwo = true;
                            break;
                        }
                    }
                    if(flagTwo)
                    {
                        vm.message = "只有板材确认才能配料!";
                        $('#static').modal();
                        return;
                    }
                    // 检查是否有项目被选中
                    let hasCheckedItem = row.batchDetailSetupList.some(batchDetailSetup => batchDetailSetup.checked);

                    for (let batchDetailSetup of row.batchDetailSetupList)
                    {
                        // 如果没有项目被选中，则选择所有状态为1001的项目
                        if (!hasCheckedItem && batchDetailSetup.status == 1001 && (!batchDetailSetup.materialOccupyList || batchDetailSetup.materialOccupyList.length <= 0))
                        {
                            batchDetailSetup.materialOccupyList = [];
                            batchDetailSetupList.push(batchDetailSetup);
                        }
                        else if (hasCheckedItem && batchDetailSetup.checked && batchDetailSetup.status == 1001)
                        {
                            // 如果有项目被选中，则仅处理这些项目
                            if (batchDetailSetup.materialOccupyList && batchDetailSetup.materialOccupyList.some(materialOccupy => materialOccupy.recordId))
                            {
                                flag = true; // 已经有配料记录，不能重复配料
                                break;
                            }
                            else
                            {
                                batchDetailSetup.materialOccupyList = []; // 清空配料列表
                                batchDetailSetupList.push(batchDetailSetup);
                            }
                        }
                    }
                    /*for(let batchDetailSetup of row.batchDetailSetupList)
                    {
                        if(batchDetailSetup.checked && batchDetailSetup.status == 1001)
                        {
                            if(batchDetailSetup.materialOccupyList && batchDetailSetup.materialOccupyList.length > 0)
                            {
                                for(let materialOccupy of batchDetailSetup.materialOccupyList)
                                {
                                    if(materialOccupy.recordId)
                                    {
                                        flag = true;
                                        break;
                                    }
                                }
                            }
                            if(flag)
                            {
                                vm.message = "不能重复配料!";
                                break;
                            }
                            else
                            {
                                batchDetailSetup.materialOccupyList = [];
                                batchDetailSetupList.push(batchDetailSetup);
                            }
                        }
                    }*/
                }
                if(flag)
                {
                    $('#static').modal();
                   return;
                }
            }
            if(!batchDetailSetupList || batchDetailSetupList.length == 0)
            {
                vm.message = "没有找到准备阶段数据!";
                $('#static').modal();
                return;
            }
            vm.needQty = 0;
            vm.matchedQty = 0;
            for(let batchDetailSetup of batchDetailSetupList)
            {
                if(batchDetailSetup.qtyT && batchDetailSetup.qtyT > 0)
                {
                    vm.needQty += Number(batchDetailSetup.qtyT);
                }
                batchDetailSetup.brandNum = 1;
                batchDetailSetup.boardLevelNum = 1;
                batchDetailSetup.materialTypeNum = 1;
                batchDetailSetup.boardThicknessNum = 1;
                batchDetailSetup.copperCladThicknessNum = 1;
                batchDetailSetup.daoreNum = 1;
                batchDetailSetup.naiyaNum = 1;
                batchDetailSetup.lengthNum = 1;
                batchDetailSetup.widthNum = 1;
            }
            vm.batchDetailSetupList = batchDetailSetupList;
            let totalPcses = Number(vm.batchDetailSetupList[0].totalPcses);
            if(!totalPcses)
            {
                totalPcses = 0;
            }
            if(vm.needQty && vm.needQty > 0)
            {
                vm.sumDosingPcs = totalPcses * vm.needQty;
            }
            vm.feedNo = row.no;
            vm.notiNo = row.notiNo;
            vm.craftNo = row.craftNo;
            vm.starAniseLength = row.starAniseLength;
            vm.starAniseWidth = row.starAniseWidth;
            vm.useAgeData = row.useAgeData;
            vm.urgentFlag = row.urgentFlag;
            vm.materialUse = row.materialUse;
            vm.dosingMaterialList = [];
            MainCtrl.blockUI({
                animate: true,
            });
            upida.post("batchDetail/stage/getDosingMaterialList",vm.batchDetailSetupList).then(function (data) {
                if(data && data.length > 0)
                {
                    vm.businessChangeFlag = data[0].businessChangeFlag;
                    vm.stocksFlagSearchB = "有";
                    for(let mat of data)
                    {
                        if(mat.availableQty && mat.availableQty > 0)
                        {
                            mat.stocksFlag = "有"
                        }
                        else
                        {
                            mat.stocksFlag = "无";
                        }
                        if(mat.rimChargeFlag && mat.rimChargeFlag == 1)
                        {
                            mat.makindFlag = "余料";
                        }
                        else
                        {
                            mat.makindFlag = "大板";
                        }
                    }
                    vm.dosingMaterialList = data;
                    $('#dosingMateriaStatic').modal();
                }
                else
                {
                    vm.message = "没有匹配到材料!";
                    $('#static').modal();
                }
                MainCtrl.unblockUI();
            });
        };

        vm.selectMatList = [];
        vm.matchedQty = 0;
        vm.exceedQty = 0;
        vm.batchedCost = 0;
        vm.sumPcsQty = 0;
        vm.computBatchDetailMatQty = function(row,material)
        {
            vm.sumPcsQty = 0;
            if(!material)
            {
                return;
            }
            let useStocks = material.useStocks ? Number(material.useStocks) : 0;
            material.batchedCost = (useStocks * (material.price ? Number(material.price) : 0)).toFixed(2);

            if(useStocks > material.availableQty)
            {
                alert("使用数量不能超过可用库存!");
                return;
            }
            for(let batchDetailSetup of vm.batchDetailSetupList)
            {
                batchDetailSetup.materialOccupyList = [];
            }
            vm.selectMatList = [];

            let totalPcses = Number(vm.batchDetailSetupList[0].totalPcses);
            if(!totalPcses)
            {
                totalPcses = 0;
            }

            // 获取余料的pcs总数
            let sumRemaPcsQty = 0;
            let sumRemaQty = 0;

            for(let dosingMaterial of vm.dosingMaterialList)
            {
                if(dosingMaterial.rimChargeFlag && dosingMaterial.rimChargeFlag == 1)
                {
                    if(dosingMaterial.pcsQty && dosingMaterial.pcsQty > 0)
                    {
                        sumRemaPcsQty += Number(dosingMaterial.pcsQty);
                    }
                    if(dosingMaterial.useStocks && dosingMaterial.useStocks > 0)
                    {
                        sumRemaQty += Number(dosingMaterial.useStocks);
                    }
                }
            }
            let scaleRemaQty = Math.ceil(sumRemaPcsQty / sumRemaQty);

            for(let dosingMaterial of vm.dosingMaterialList)
            {
                if(dosingMaterial.useStocks && dosingMaterial.useStocks > 0)
                {
                    useStocks = dosingMaterial.useStocks ? Number(dosingMaterial.useStocks) : 0;
                    // 计算非余料的pcs数量
                    if(!(dosingMaterial.rimChargeFlag && dosingMaterial.rimChargeFlag == 1))
                    {
                        dosingMaterial.pcsQty = useStocks * totalPcses;
                        vm.sumPcsQty += dosingMaterial.pcsQty;
                    }
                    // 赋值物料占用
                    let remaPcsQty = 0;
                    for(let batchDetailSetup of vm.batchDetailSetupList)
                    {
                        let qtyT = batchDetailSetup.qtyT ? Number(batchDetailSetup.qtyT) : 0;
                        if(qtyT <= 0)
                        {
                            continue;
                        }
                        if(batchDetailSetup.materialOccupyList && batchDetailSetup.materialOccupyList.length > 0)
                        {
                            // 累计已经放入的数量
                            for(let mat of batchDetailSetup.materialOccupyList)
                            {
                                if(mat.useStocks && mat.useStocks > 0)
                                {
                                    qtyT -=  mat.useStocks;
                                }
                            }
                        }
                        if(qtyT <= 0)
                        {
                            continue;
                        }
                        let computQty = 0;
                        if(qtyT > useStocks)
                        {
                            computQty = useStocks;
                            useStocks = 0;
                        }
                        else
                        {
                            computQty = qtyT;
                            useStocks = useStocks - qtyT;
                        }
                        let pcsQty = 0;
                        if(batchDetailSetup.recordId == vm.batchDetailSetupList[vm.batchDetailSetupList.length - 1].recordId)
                        {
                            computQty = useStocks + computQty;
                            if(dosingMaterial.rimChargeFlag && dosingMaterial.rimChargeFlag == 1)
                            {
                                pcsQty = sumRemaPcsQty - remaPcsQty;
                            }
                            else
                            {
                                pcsQty = computQty * totalPcses;
                            }
                        }
                        else
                        {
                            if(!(dosingMaterial.rimChargeFlag && dosingMaterial.rimChargeFlag == 1))
                            {
                                pcsQty = computQty * totalPcses;
                            }
                            else
                            {
                                pcsQty = computQty * scaleRemaQty;
                            }
                        }
                        if(computQty > 0)
                        {
                            const matObj = {};
                            matObj.materialId = dosingMaterial.recordId;
                            matObj.useStocks = computQty;
                            matObj.pcsQty = pcsQty;
                            matObj.rimChargeFlag = dosingMaterial.rimChargeFlag;
                            batchDetailSetup.materialOccupyList.push(matObj);
                            if(matObj.rimChargeFlag && matObj.rimChargeFlag == 1 && matObj.pcsQty && matObj.pcsQty > 0)
                            {
                                remaPcsQty += Number(matObj.pcsQty);
                            }
                        }
                        if(useStocks <= 0)
                        {
                            break;
                        }
                    }
                    vm.selectMatList.push(dosingMaterial);
                }
            }
            vm.matchedQty = 0;
            vm.batchedCost = 0;
            vm.sumPcsQty = 0;
            for(let obj of vm.dosingMaterialList)
            {
                if(obj.useStocks && obj.useStocks > 0)
                {
                    vm.matchedQty += Number(obj.useStocks);
                    vm.batchedCost += Number((Number(obj.useStocks) * (obj.price ? Number(obj.price) : 0)).toFixed(2));
                }
                if(obj.pcsQty && obj.pcsQty > 0)
                {
                    vm.sumPcsQty += Number(obj.pcsQty);
                }
            }
            vm.stageData.profitAmount = Number(Number(row.stageData.materialFee)- Number(row.stageData.batchedCost) - Number(vm.batchedCost));
            vm.exceedQty = vm.matchedQty - vm.needQty;
            if(vm.exceedQty <= 0)
            {
                vm.exceedQty = 0;
            }
            vm.remainStr = null;
            let pcsQty = (vm.sumDosingPcs ? Number(vm.sumDosingPcs) : 0) - (vm.sumPcsQty ? Number(vm.sumPcsQty) : 0);
            if(pcsQty > 0)
            {
                vm.remainStr = "剩余pcs数量"+pcsQty;
            }

        }

        vm.addMaterialList = function(row)
        {
            var material = {};
            if(row.materialOccupyList && row.materialOccupyList.length > 0) {
                row.materialOccupyList.push(material);
            }else{
                row.materialOccupyList = [];
                row.materialOccupyList.push(material);
            }
        };

        vm.delMaterial = function(row,index){
            row.splice(index, 1);
        };

        vm.fullInverseSelection = function(row){
            row.checkdAll = !row.checkdAll;
            for(let batchDetailSetup of row.batchDetailSetupList)
            {
                batchDetailSetup.checked = row.checkdAll;
            }
        }

        vm.checkSelectMat = function(row,index)
        {
           if(!row)
           {
                return;
           }
           let flag = false;
           let materialId = row.materialOccupyList[index].materialId;
           for(let i = 0; i < row.materialOccupyList.length; i++)
           {
                if(i != index && materialId == row.materialOccupyList[i].materialId)
                {
                    flag = true;
                    break;
                }
           }
           if(flag)
           {
               row.materialOccupyList[index].materialId = null;
               alert("同一张卡不能只能选的材料不能重复!");
           }
        }

        vm.checkMaterialOccupyStocks = function(item)
        {
            if(!item)
            {
                return;
            }
            let materialId = item.materialId;

            let matMatchedQty = 0;
            if(vm.dosingMaterialList && vm.dosingMaterialList.length > 0)
            {
                for(let dosingMaterial of vm.dosingMaterialList)
                {
                    if(materialId == dosingMaterial.recordId && dosingMaterial.useStocks && dosingMaterial.useStocks > 0)
                    {
                        matMatchedQty = Number(dosingMaterial.useStocks);
                        break;
                    }
                }
            }

            let matMatchedQtyTwo = 0;
            if(vm.batchDetailSetupList && vm.batchDetailSetupList.length > 0)
            {
                for(let batchDetailSetup of vm.batchDetailSetupList)
                {
                    if(batchDetailSetup.materialOccupyList && batchDetailSetup.materialOccupyList.length > 0)
                    {
                        for(let materialOccupy of batchDetailSetup.materialOccupyList)
                        {
                            if(materialId == materialOccupy.materialId && materialOccupy.useStocks && materialOccupy.useStocks > 0)
                            {
                                matMatchedQtyTwo += Number(materialOccupy.useStocks);
                            }
                        }
                    }
                }
            }

            if(matMatchedQtyTwo > matMatchedQty)
            {
                alert("当前材料的数量已经超过已配的数量了!");
                item.useStocks = null;
                return;
            }

        }

        vm.saveDosing = function()
        {
            if(!vm.clicks)
            {
                alert("请勿多次点击!");
                $('#dosingMateriaStatic').modal();
                return;
            }
            if(vm.matchedQty < vm.needQty)
            {
                alert("已配的数量不能低于本次需的数量!");
                $('#dosingMateriaStatic').modal();
                return;
            }
            let flag = false;
            let message = null;
            for(let batchDetailSetup of vm.batchDetailSetupList)
            {
                if(!batchDetailSetup.materialOccupyList || batchDetailSetup.materialOccupyList.length == 0)
                {
                    flag = true;
                    message = "操作的批次明细必须有材料";
                    break;
                }
                let flagTwo = false;
                for(let materialOccupy of batchDetailSetup.materialOccupyList)
                {
                    if(!materialOccupy.materialId)
                    {
                        flagTwo = true;
                        message = "材料必须选择!";
                        break;
                    }
                    if(!materialOccupy.useStocks)
                    {
                        flagTwo = true;
                        message = "材料数量不能为空!";
                        break;
                    }
                    if(!materialOccupy.pcsQty)
                    {
                        flagTwo = true;
                        message = "PCS数量不能为空!";
                        break;
                    }
                }
                if(flagTwo)
                {
                    flag = true;
                    break;
                }
            }
            if(flag)
            {
                alert(message);
                $('#dosingMateriaStatic').modal();
                return;
            }
            let remaQty = vm.sumDosingPcs - vm.sumPcsQty;
            if(remaQty != 0)
            {
                alert("pcs数量"+vm.sumPcsQty+"必须满足总PCS数量"+vm.sumDosingPcs);
                $('#dosingMateriaStatic').modal();
                return;
            }
            vm.clicks = false;
            MainCtrl.blockUI({
                animate: true,
            });
            upida.post("batchDetail/stage/saveDosing",vm.batchDetailSetupList).then(function (data) {
                vm.message = data.message;
                $('#static').modal();
                if(data.result == "success")
                {
                    vm.stageWorkData();
                    $('#dosingMateriaStatic').modal('hide');
                }
                vm.clicks = true;
                MainCtrl.unblockUI();
            });
        }

        vm.cancleDosingObj = [];
        vm.cancleDosingOpen = function(row,item)
        {
            if(!row)
            {
                return;
            }
            if (row.batchDetailSetupList && row.batchDetailSetupList.length > 0)
            {
                // 只有板材确认才能取消配料
                let falg = false;
                let falgTwo = false;
                let hasCheckedItem = row.batchDetailSetupList.some(batchDetailSetup => batchDetailSetup.checked);
                for (let batchDetailSet of row.batchDetailSetupList)
                {
                    if (!hasCheckedItem && batchDetailSet.status == 1001 && batchDetailSet.name == "板材确认" &&
                        (batchDetailSet.materialOccupyList && batchDetailSet.materialOccupyList.length >= 0))
                    {
                        vm.cancleDosingObj.push(batchDetailSet);
                    }
                    else if (hasCheckedItem)
                    {
                        if (batchDetailSet.checked && batchDetailSet.status == 1001 && batchDetailSet.name != "板材确认")
                        {
                            falg = true;
                            break;
                        }
                        if (batchDetailSet.checked && batchDetailSet.status == 1001 && batchDetailSet.name == "板材确认")
                        {
                            if (!batchDetailSet.materialOccupyList || batchDetailSet.materialOccupyList.length <= 0)
                            {
                                falgTwo = true;
                                break;
                            }
                            vm.cancleDosingObj.push(batchDetailSet);
                        }
                    }
                }
                /*for (let batchDetailSet of row.batchDetailSetupList)
                {
                    if (batchDetailSet.checked && batchDetailSet.status == 1001 && batchDetailSet.name != "板材确认")
                    {
                        falg = true;
                        break;
                    }
                    if (batchDetailSet.checked && batchDetailSet.status == 1001 && batchDetailSet.name == "板材确认")
                    {
                        if (!batchDetailSet.materialOccupyList || batchDetailSet.materialOccupyList.length <= 0)
                        {
                            falgTwo = true;
                            break;
                        }
                        vm.cancleDosingObj.push(batchDetailSet);
                    }
                }*/
                if (falg)
                {
                    vm.message = "只有板材确认才能取消配料!";
                    $('#static').modal();
                    return;
                }

                if (falgTwo || vm.cancleDosingObj.length == 0)
                {
                    vm.message = "没有可取消的配料批次明细!";
                    $('#static').modal();
                    return;
                }
            }
            vm.message = "您确定要取消配料？";
            $('#cancleDosingStatic').modal();
        }

        vm.cancleDosing = function()
        {
            if(!vm.clicks)
            {
                vm.message = "请勿多次点击!";
                $('#static').modal();
                return;
            }
            vm.clicks = false;
            MainCtrl.blockUI({
                animate: true,
            });
            upida.post("batchDetail/stage/cancleDosing",vm.cancleDosingObj).then(function (data) {
                vm.message = data.message;
                $('#static').modal();
                if(data.result == "success")
                {
                    vm.stageWorkData();
                    $('#cancleDosingStatic').modal('hide');
                }
                vm.batchedCost = 0;
                vm.clicks = true;
                MainCtrl.unblockUI();
            });
        }

        vm.batchConfirmButtonOpen = function(row,num)
        {
            if(!row || !num)
            {
                return;
            }
            vm.batchDetailSetupList = [];
            if(num == 1)
            {
                let flag = false;
                let hasCheckedItem = row.batchDetailSetupList.some(batchDetailSetup => batchDetailSetup.checked);
                for (let batchDetailSetup of row.batchDetailSetupList)
                {
                    // 如果没有选中,则选择所有状态为1001的数据
                    if (!hasCheckedItem && batchDetailSetup.status == 1001 && (batchDetailSetup.materialOccupyList && batchDetailSetup.materialOccupyList.length > 0))
                    {
                        batchDetailSetup.status = '1002';
                        vm.batchDetailSetupList.push(batchDetailSetup);
                    }
                    else if (hasCheckedItem && batchDetailSetup.checked && batchDetailSetup.status == 1001)
                    {
                        if(!batchDetailSetup.materialOccupyList || batchDetailSetup.materialOccupyList.length == 0)
                        {
                            flag = true;
                            break;
                        }
                        batchDetailSetup.status = '1002';
                        vm.batchDetailSetupList.push(batchDetailSetup);
                    }
                }
                /*for(let batchDetailSetup of row.batchDetailSetupList)
                {
                    if(batchDetailSetup.checked && batchDetailSetup.status == 1001)
                    {
                        if(!batchDetailSetup.materialOccupyList || batchDetailSetup.materialOccupyList.length == 0)
                        {
                            flag = true;
                            break;
                        }
                        batchDetailSetup.status = '1002';
                        vm.batchDetailSetupList.push(batchDetailSetup);
                    }
                }*/
                if(flag)
                {
                    vm.message = "先配料才能确认!";
                    $('#static').modal();
                    return;
                }
                vm.message = "您确定要确认准备阶段？";
            }
            else if(num == 2)
            {
                let flag = false;
                let hasCheckedItem = row.batchDetailSetupList.some(batchDetailSetup => batchDetailSetup.checked);
                for (let batchDetailSetup of row.batchDetailSetupList)
                {
                    // 如果没有选中,则选择所有状态为1002的数据
                    if (!hasCheckedItem && batchDetailSetup.status == 1002 && (batchDetailSetup.name == '模具确认' || batchDetailSetup.name == '测试架确认' || batchDetailSetup.name == '板材确认'))
                    {
                        batchDetailSetup.status = '1001';
                        vm.batchDetailSetupList.push(batchDetailSetup);
                    }
                    else if (hasCheckedItem && batchDetailSetup.checked && batchDetailSetup.status == 1002)
                    {
                        if(!(batchDetailSetup.name == '模具确认' || batchDetailSetup.name == '测试架确认' || batchDetailSetup.name == '板材确认'))
                        {
                            flag = true;
                            break;
                        }
                        batchDetailSetup.status = '1001';
                        vm.batchDetailSetupList.push(batchDetailSetup);
                    }
                }
                /*for(let batchDetailSetup of row.batchDetailSetupList)
                {
                    if(batchDetailSetup.checked && batchDetailSetup.status == 1002)
                    {
                        if(!(batchDetailSetup.name == '模具确认' || batchDetailSetup.name == '测试架确认' || batchDetailSetup.name == '板材确认'))
                        {
                            flag = true;
                            break;
                        }
                        batchDetailSetup.status = '1001';
                        vm.batchDetailSetupList.push(batchDetailSetup);
                    }
                }*/
                if(flag)
                {
                    vm.message = "只有模具确认、测试架确认、板材确认才能取消确认!";
                    $('#static').modal();
                    return;
                }
                vm.message = "您确定要取消确认准备阶段？";
            }
            if(!vm.batchDetailSetupList || vm.batchDetailSetupList.length == 0)
            {
                vm.message = "没有选择准备阶段!";
                $('#static').modal();
                return;
            }
            $('#batchConfirmButtonStatic').modal();
        }

        vm.batchConfirmButton = function()
        {
            if(!vm.clicks)
            {
                vm.message = "请勿多次点击!";
                $('#static').modal();
                return;
            }
            vm.clicks = false;
            MainCtrl.blockUI({
                animate: true,
            });
            upida.post("batchDetail/stage/prepareConfirmData",vm.batchDetailSetupList).then(function (data) {
                vm.message = data.message;
                $('#static').modal();
                if(data.result == "success")
                {
                    vm.stageWorkData();
                    $('#batchConfirmButtonStatic').modal('hide');
                }
                vm.clicks = true;
                MainCtrl.unblockUI();
            });
        }

        vm.craftList = [
            {
                id:2,
                name:'PCB类型',
                flag:true
            },
            /*{
                id:3,
                name:'覆铜板材',
                flag:true
            },
            {
                id:4,
                name:'板材厚度',
                flag:true
            },*/
            {
                id:5,
                name:'覆铜要求',
                flag:true
            },
            {
                id:6,
                name:'导热',
                flag:true
            },
            {
                id:7,
                name:'耐压',
                flag:true
            },
        ]

        vm.choiceCraft = function(id)
        {
            let brandNum = 1;
            let boardLevelNum = 1;
            let materialTypeNum = 1;
            let boardThicknessNum = 1;
            let copperCladThicknessNum = 1;
            let daoreNum = 1;
            let naiyaNum = 1;
            let lengthNum = 1;
            let widthNum = 1;
            for(let craft of vm.craftList)
            {
                let num = craft.flag ? 1 : null;
                switch (craft.id)
                {
                    case 1:
                        brandNum = num;
                        break;
                    case 2:
                        boardLevelNum = num;
                        break;
                    case 3:
                        materialTypeNum = num;
                        break;
                    case 4:
                        boardThicknessNum = num;
                        break;
                    case 5:
                        copperCladThicknessNum = num;
                        break;
                    case 6:
                        daoreNum = num;
                        break;
                    case 7:
                        naiyaNum = num;
                        break;
                    case 8:
                        lengthNum = num;
                        break;
                    case 9:
                        widthNum = num;
                        break;
                }
            }
            for(let batchDetailSetup of vm.batchDetailSetupList)
            {
                batchDetailSetup.brandNum = brandNum;
                batchDetailSetup.boardLevelNum = boardLevelNum;
                batchDetailSetup.materialTypeNum = materialTypeNum;
                batchDetailSetup.boardThicknessNum = boardThicknessNum;
                batchDetailSetup.copperCladThicknessNum = copperCladThicknessNum;
                batchDetailSetup.daoreNum = daoreNum;
                batchDetailSetup.naiyaNum = naiyaNum;
                batchDetailSetup.lengthNum = lengthNum;
                batchDetailSetup.widthNum = widthNum;
            }
            MainCtrl.blockUI({
                animate: true,
            });
            upida.post("batchDetail/stage/getDosingMaterialList",vm.batchDetailSetupList).then(function (data) {
                if(data && data.length > 0)
                {
                    vm.stocksFlagSearchB = "有";
                    for(let mat of data)
                    {
                        if(mat.availableQty && mat.availableQty > 0)
                        {
                            mat.stocksFlag = "有"
                        }
                        else
                        {
                            mat.stocksFlag = "无";
                        }
                        if(mat.rimChargeFlag && mat.rimChargeFlag == 1)
                        {
                            mat.makindFlag = "余料";
                        }
                        else
                        {
                            mat.makindFlag = "大板";
                        }
                    }
                    vm.dosingMaterialList = data;
                }
                else
                {
                    vm.message = "没有匹配到材料!";
                    $('#static').modal();
                }
                MainCtrl.unblockUI();
            });
        }

        vm.computBatchDetailRimChargeQty = function(row,material)
        {
            if(!material)
            {
                return;
            }
            vm.computBatchDetailMatQty(row,material);
            let remaQty = vm.sumDosingPcs - vm.sumPcsQty;
            if(remaQty < 0)
            {
                alert("pcs数量"+vm.sumPcsQty+"不能超过总PCS数量"+vm.sumDosingPcs);
                material.pcsQty = null;
                vm.computBatchDetailMatQty(row,material);
                return;
            }
        }

        $scope.$on("$stateChangeSuccess", function () {
            upida.setScope($scope);
            loadRight();
        });
    }]);

