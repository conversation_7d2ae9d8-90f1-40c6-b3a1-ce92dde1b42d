package com.kyb.pcberp.modules.wechat.pojo;

public class WxConfig
{
    /** 公众号的唯一标识 */
    private String appId;
    
    /** 生成签名的时间戳 */
    private String timestamp;
    
    /** 生成签名的随机串 */
    private String nonceStr;
    
    /** 签名 */
    private String signature;
    
    /** 公众号秘钥 */
    private String appsecret;
    
    private String jsapi_ticket;
    
    private String url;
    
    public String getAppId()
    {
        return appId;
    }
    
    public void setAppId(String appId)
    {
        this.appId = appId;
    }
    
    public String getTimestamp()
    {
        return timestamp;
    }
    
    public void setTimestamp(String timestamp)
    {
        this.timestamp = timestamp;
    }
    
    public String getNonceStr()
    {
        return nonceStr;
    }
    
    public void setNonceStr(String nonceStr)
    {
        this.nonceStr = nonceStr;
    }
    
    public String getSignature()
    {
        return signature;
    }
    
    public void setSignature(String signature)
    {
        this.signature = signature;
    }
    
    public String getAppsecret()
    {
        return appsecret;
    }
    
    public void setAppsecret(String appsecret)
    {
        this.appsecret = appsecret;
    }

    public String getJsapi_ticket()
    {
        return jsapi_ticket;
    }

    public void setJsapi_ticket(String jsapi_ticket)
    {
        this.jsapi_ticket = jsapi_ticket;
    }

    public String getUrl()
    {
        return url;
    }

    public void setUrl(String url)
    {
        this.url = url;
    }
}
