const employeeDeail = {
    template: '#employeeDeail',
    created:function(){
        const comId = window.localStorage.getItem('comId');
        const number = window.localStorage.getItem('number');
        const userId = window.localStorage.getItem('userId');
        if (comId) {
            this.comId = comId;
            this.getCompanyUserList(comId);
        }else {
            alert("请刷新重试");
            this.$router.push("/bus_person");
        }
        if (number) {
            this.number = number;
        }
        if (userId) {
            this.userId = userId;
        }
    },
    data(){
        return {
            comId: "",
            emplyeeList: {},
            recordId: "",
            emp: {visibleFlag: "1"},
            serchMsg: "",
            flag: "",
            number:"",
            userId:"",
        }
    },
    methods: {
        changeFlag: function (flag) {
            this.flag = flag;
            this.getCompanyUserList();
        },
        getCompanyUserList: function () {
            const _this = this;
            const emp = {};
            emp.companyId = this.comId;
            emp.serchMsg = this.serchMsg;
            emp.flag = this.flag;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/getCompanyUserList",
                data: JSON.stringify(emp),
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    _this.emplyeeList = data;
                }
            })
        },
        manageModal:function (item) {
            this.emp = item;
            $('#manageModal').modal();
        },
        setManage: function (){
            if (!this.emp.concatId){
                alert("请刷新重试！");
                return;
            }
            const _this = this;
            const entity = {};
            entity.recordId = this.emp.concatId;
            if (this.emp.manageStatus){
                entity.status = "";
            }else {
                entity.status = 1;
            }
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/setManage",
                data: JSON.stringify(entity),
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    $('#manageModal').modal('hide');
                    if (data == "success"){
                        this.recordId = "";
                        alert("设置成功！");
                        _this.getCompanyUserList();
                    }else {
                        alert("请刷新重试！");
                    }
                }
            })
        },
        deleteModal: function (recordId) {
            this.recordId = recordId;
            $('#deleteModal').modal();
        },
        deleteEmp: function (){
            if (!this.recordId){
                alert("请刷新重试！");
                return;
            }
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/deleteCompanyUser",
                data: this.recordId,
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    $('#deleteModal').modal('hide');
                    if (data == "success"){
                        this.recordId = "";
                        if(_this.flag==1){
                            alert("拒绝成功！");
                        }else{
                            alert("删除成功！");
                        }
                        _this.getCompanyUserList();
                    }else {
                        alert("请刷新重试！");
                    }
                }
            })
        },
        addModal: function () {
            this.emp = {visibleFlag: "1",phone:"",position:""}
            $('#addModal').modal();
        },
        editModal: function (item) {
            this.emp = JSON.parse(JSON.stringify(item));
            $('#addModal').modal();
        },
        saveEmp: function () {
            if (!this.comId){
                alert("请刷新重试");
                return;
            }
            if (!(this.emp.phone && this.emp.position)){
                alert("请填写完整信息");
                return;
            }
            if(!(/^1[34578]\d{9}$/.test(this.emp.phone))){
                alert("手机号码有误，请重填");
                return;
            }
            const _this = this;
            this.emp.companyId = this.comId;
            if (this.emp.concatId){
                this.emp.recordId = this.emp.concatId;
            }
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/addCompanyUser",
                data: JSON.stringify(this.emp),
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    if (data == "exit"){
                        alert("员工手机号码已经添加！")
                    }else if (data == "success"){
                        _this.emp = {};
                        $('#addModal').modal('hide');
                        alert("保存成功！")
                        _this.getCompanyUserList();
                    }
                }
            })
        },
        updateFlagModal: function (item) {
            this.emp = item;
            $('#updateFlagModal').modal();
        },
        updateFlag: function (){
            if (!this.emp.concatId){
                alert("请刷新重试！");
                return;
            }
            const _this = this;
            const entity = {};
            entity.recordId = this.emp.concatId;
            entity.flag = null;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/updateCompanyUserFlag",
                data: JSON.stringify(entity),
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    $('#updateFlagModal').modal('hide');
                    if (data == "success"){
                        this.emp = "";
                        alert("已加入企业！");
                        _this.getCompanyUserList();
                    }else {
                        alert("请刷新重试！");
                    }
                }
            })
        },
        deleteCompanyModal: function (recordId) {
            this.recordId = recordId;
            $('#deleteCompanyModal').modal();
        },
        deleteCompany: function () {
            if (!this.recordId){
                alert("请刷新重试！");
                return;
            }
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/deleteCompany",
                data: this.recordId,
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    $('#deleteCompanyModal').modal('hide');
                    if (data == "success"){
                        this.recordId = "";
                        alert("停用成功！");
                        if (_this.number === "1")
                        {
                            _this.$router.push('bus_person');
                        }else if (_this.number === "2"){
                            _this.$router.push('maintenance');
                        }
                    }else {
                        alert("请刷新重试！");
                    }
                }
            })
        }
    }
}