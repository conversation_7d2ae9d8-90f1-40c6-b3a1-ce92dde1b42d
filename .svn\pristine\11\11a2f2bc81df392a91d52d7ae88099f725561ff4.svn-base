package com.kyb.pcberp.modules.hr.finance_center.pojo;

import com.kyb.pcberp.common.persistence.DataEntity;

public class Hr_FinancialCertificateWord extends DataEntity<Hr_FinancialCertificateWord>
{
    private static final long serialVersionUID = 1L;

    private String certificateWord; // 凭证字

    private String parentId; // 父级id

    private String status; // 状态

    private String disabledState; //禁用状态

    private String preset; // 系统预置

    private String restrictCrDr; // 限制多借多贷凭证

    public String getCertificateWord()
    {
        return certificateWord;
    }

    public void setCertificateWord(String certificateWord)
    {
        this.certificateWord = certificateWord;
    }

    public String getParentId()
    {
        return parentId;
    }

    public void setParentId(String parentId)
    {
        this.parentId = parentId;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getDisabledState()
    {
        return disabledState;
    }

    public void setDisabledState(String disabledState)
    {
        this.disabledState = disabledState;
    }

    public String getPreset()
    {
        return preset;
    }

    public void setPreset(String preset)
    {
        this.preset = preset;
    }

    public String getRestrictCrDr()
    {
        return restrictCrDr;
    }

    public void setRestrictCrDr(String restrictCrDr)
    {
        this.restrictCrDr = restrictCrDr;
    }
}
