/**
 * 
 */
package com.kyb.pcberp.modules.quality.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.eg.entity.BoardPartCraft;
import com.kyb.pcberp.modules.eg.entity.EgProcess;

import java.math.BigDecimal;

/**
 * 不良记录Entity
 * 
 * <AUTHOR>
 * @version 2016-06-07
 */
public class Bad extends DataEntity<Bad>
{
    
    private static final long serialVersionUID = 1L;
    
    private Long inspectId; // 检测记录ID
    
    private BoardPartCraft boardPartCraft; // 板分割后的工艺Id
    
    private String craftNo; // 工艺编号
    
    private String badQty; // 不良数量
    
    private String badCause; // 不良原因
    
    private EgProcess dutyProcess; // 责任工序
    
    private Inspect inspect; // 检测记录
    
    private String produceBatchDetailId; // zjn 拆卡：品质检测信息和不良信息判断

    private BigDecimal badArea; // 不良面积

    private String operator;
    
    public Bad()
    {
        super();
    }
    
    public Bad(String id)
    {
        super(id);
    }
    
    public Long getInspectId()
    {
        return inspectId;
    }
    
    public void setInspectId(Long inspectId)
    {
        this.inspectId = inspectId;
    }
    
    public BoardPartCraft getBoardPartCraft()
    {
        return boardPartCraft;
    }
    
    public void setBoardPartCraft(BoardPartCraft boardPartCraft)
    {
        this.boardPartCraft = boardPartCraft;
    }
    
    public String getCraftNo()
    {
        return craftNo;
    }
    
    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }
    
    public String getBadQty()
    {
        return badQty;
    }
    
    public void setBadQty(String badQty)
    {
        this.badQty = badQty;
    }
    
    public String getBadCause()
    {
        return badCause;
    }
    
    public void setBadCause(String badCause)
    {
        this.badCause = badCause;
    }
    
    public EgProcess getDutyProcess()
    {
        return dutyProcess;
    }
    
    public void setDutyProcess(EgProcess dutyProcess)
    {
        this.dutyProcess = dutyProcess;
    }
    
    public Inspect getInspect()
    {
        return inspect;
    }
    
    public void setInspect(Inspect inspect)
    {
        this.inspect = inspect;
    }
    
    @Override
    public Bad clone()
    {
        Bad bad = null;
        try
        {
            bad = (Bad)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            e.printStackTrace();
        }
        return bad;
    }

    public String getProduceBatchDetailId()
    {
        return produceBatchDetailId;
    }

    public void setProduceBatchDetailId(String produceBatchDetailId)
    {
        this.produceBatchDetailId = produceBatchDetailId;
    }

    public BigDecimal getBadArea() {
        return badArea;
    }

    public void setBadArea(BigDecimal badArea) {
        this.badArea = badArea;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}