/* Setup general page controller */
kybApp.controller('popoinspectCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil', 'BaseUtil', function ($rootScope, $scope, upida, $timeout, CommonUtil, BaseUtil) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();
        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });

    var vm = this;

    vm.help = function () {
        if (vm.tabs.viewForm.active) {
            vm.helpList();
        } else if (vm.tabs.editForm.active) {
        	if(vm.titleInspect!="disInspect"){
        		 vm.helpDetail();
        	}
        }else if(vm.tabs.psForm.active){
        	vm.introListOptionshepe();
        }
    };
    
    vm.queryAll = false;
    
    //焦点
    vm.focus={main:1};

    // 分页数据
    vm.page = {};
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    // 字典项分页数据
    vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "quality/popoinspect/page";
    vm.page.condition = []; // 条件

    // 时间范围的选项
    vm.rangeOptions = {
        //format: "YYYY-MM-DD",
        startDate: new Date((new Date).setMonth(((new Date).getMonth() - 1))),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
    };

    // 时间范围的Model
    vm.time = {
        start: {},
        end: {}
    };

    // 查询条件参数
    vm.query = {}; // 查询对象
    vm.query.popoNo = {};
    vm.query.popoNo.name = "popo.no";
    vm.query.popoNo.value = "";
    vm.query.craftNo = {};
    vm.query.craftNo.name = "popo.craftNo";
    vm.query.craftNo.value = "";
    vm.query.processName = {};
    vm.query.processName.name = "process.category";
    vm.query.processName.value = "";
    vm.query.startDate = {};
    vm.query.startDate.name = "startDate";
    vm.query.startDate.value = "";
    vm.query.endDate = {};
    vm.query.endDate.name = "endDate";
    vm.query.endDate.value = "";
    // 排序字段
    vm.query.sort = {};
    vm.query.sort.name = "orderBy";
    vm.query.sort.value = "createdDate DESC";
    
    vm.initDate=function(date)
	{
		 if(date==""){
			 vm.rangeOptions = {
		        //format: "YYYY-MM-DD",
		    	startDate: new Date(vm.query.startDate.value),
		    	minDate:new Date(new Date(vm.query.endDate.value).setFullYear(new Date(vm.query.endDate.value).getFullYear()-5))
			 };
		     vm.time= {
		       start: vm.rangeOptions.startDate,
				 end: vm.rangeOptions.minDate
			 } 
		 }
	}

    // tabs控制
    vm.tabs = {
        viewForm: {active: true},
        addForm: {active: false, show: false},
        seeForm: {active: false, show: false}
    };
    
    vm.popoInspect = {}; // 外发品检
    vm.popoInspect.popo = {}; // 品检对应外发单
    vm.popoList = []; // 外发单列表
    
    // 权限
    vm.right = {};

    vm.infiniteScroll = {};
    vm.infiniteScroll.numToAdd = 100;
    vm.infiniteScroll.currentItems = 100;

    vm.resetInfScroll = function () {
        vm.infiniteScroll.currentItems = vm.infiniteScroll.numToAdd;
    };

    vm.addMoreItems = function () {
        vm.infiniteScroll.currentItems += vm.infiniteScroll.numToAdd;
    };
    
    /**
     * 显示添加外发检测页面
     */
    vm.showAddForm = function(row){
    	vm.tabs.addForm.show = true;
    	vm.tabs.addForm.active = true;
         vm.popoInspect = {};
         vm.popoInspect.checker = BaseUtil.getUser();
         vm.popoInspect.checkDate = new Date();
         vm.popoInspect = row;
         vm.popoInspect.reworkList = [];
         vm.popoInspect.discardList = [];
         vm.popoInspect.badList = [];
         var craftNos = row.popo.craftNo;
         var arr = craftNos.split(",");
         angular.forEach(arr,function(no){
        	 vm.popoInspect.reworkList.push({
        		 type:"1",
        		 craftNo:no,
        		 subQty:"",
        		 subMoney:""
        	 });
        	 vm.popoInspect.discardList.push({
        		 type:"2",
        		 craftNo:no,
        		 subQty:"",
        		 subMoney:""
        	 });
        	 vm.popoInspect.badList.push({
        		 type:"3",
        		 craftNo:no,
        		 subQty:"",
        		 subMoney:""
        	 });
         });
    }
    
    /**
     * 隐藏外发检测添加页面
     */
    vm.hideAddForm = function(){
    	vm.popoInspect = {};
    	vm.tabs.addForm.show = false;
        $timeout(function () {
            vm.tabs.viewForm.active = true;
        });
    }
    
    /**
     * 隐藏查看页面
     */
    vm.hideSeeForm = function(){
    	vm.popoInspect = {};
    	vm.tabs.seeForm.show = false;
    	$timeout(function () {
    		vm.tabs.viewForm.active = true;
    	});
    }
    
    /**
     * 显示查看页面
     */
    vm.showSeeForm = function(row){
    	vm.popoInspect = {};
        vm.popoInspect = row;
        vm.tabs.seeForm.show = true;
    	vm.tabs.seeForm.active = true;
    }
    
    vm.checkFormData = function(){
    	var message = "";
    	
    }
    
    /**
     * 保存添加外发检测记录
     */
    vm.addPopoDetection = function(form){
    	form.$setDirty();
        if (!form.$valid) {
            return;
        }
        
        var sumQty = vm.popoInspect.popo ? Number(vm.popoInspect.popo.qtyPcsT) : 0;
        if(sumQty > 0)
        {
        	var badQty = 0;
        	if(vm.popoInspect.badList && vm.popoInspect.badList.length > 0){
        		angular.forEach(vm.popoInspect.badList,function(bad){
        			if(bad.subQty && bad.subQty !== ""){
        				badQty += Number(bad.subQty);
        			}
        		});
        	}
        	var discardQty = 0;
        	if(vm.popoInspect.discardList && vm.popoInspect.discardList.length > 0){
        		angular.forEach(vm.popoInspect.discardList,function(discard){
        			if(discard.subQty && discard.subQty !== ""){
        				discardQty += Number(discard.subQty);
        			}
        		});
        	}
        	var reworkQty = 0;
        	if(vm.popoInspect.reworkList && vm.popoInspect.reworkList.length > 0){
        		angular.forEach(vm.popoInspect.reworkList,function(rework){
        			if(rework.subQty && rework.subQty !== ""){
        				reworkQty += Number(rework.subQty);
        			}
        		});
        	}
        	if((badQty + discardQty + reworkQty) > sumQty){
            	vm.message = "不正常数量不能超过检测数量!";
            	$("#static").modal();
            	return;
        	}
        }
        
        vm.popoInspect;
        MainCtrl.blockUI({
    	    animate: true,
    	});
        upida.post("quality/popoinspect/save",vm.popoInspect).then(function(data){
        	vm.message = data;
        	$("#static").modal();
        	vm.hideAddForm();
        	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
        	MainCtrl.unblockUI();
        });
    }
    

    vm.setFormScope = function (scope) {
        vm.formScope = scope;
    };


    // 分页按钮单击处理
    vm.doPage = function (page, pageSize, total) {
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        vm.init(page, pageSize, vm.page.condition, vm.page.url);
    };

    /*
     * 兼容条件查询与普通查询
     * no 当前页
     * size 当前页显示数量
     * condition 是数组，可放入多个条件对象，如{name:"type", value:"1"}  名称对应实体字段名称，值对应页面输入的值
     * url 请求数据链接
     */
    vm.init = function (no, size, condition, url, show) {
        MainCtrl.blockUI({
            animate: true,
        });

        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;

        condition.push({
        	name: vm.query.sort.name,
        	value: vm.query.sort.value
        });
        if (vm.time.start) {
        	vm.page.condition.push({
                name: vm.query.startDate.name,
                value: vm.time.start.valueOf()
            });
        	vm.query.startDate.value=vm.time.start.valueOf();
        }

        if (vm.time.end) {
        	vm.page.condition.push({
                name: vm.query.endDate.name,
                value: vm.time.end.valueOf()
            });
        	vm.query.endDate.value=vm.time.end.valueOf();
        }

        CommonUtil.setRequestBody(reqData, condition);
        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};
            // 如果结果为空
            if (typeof result === 'undefined' || typeof result.list === 'undefined') {

                data.pageNo = 1;
                data.pageSize = 10;
                data.list = [];
                data.startCount = 0;
                data.endCount = 0;
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }
            vm.page.data = data;
            MainCtrl.unblockUI();
        });
    };

    // 页面显示数量改变
    vm.pageSizeChange = function (show) {
        vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url);
    };

    // 查询数据
    vm.doQuery = function () {
        // 设置查询条件
        var condition = [];

        if (vm.query.popoNo.value) {
        	condition.push({
        		name: vm.query.popoNo.name,
        		value: vm.query.popoNo.value
        	});
        }
        if (vm.query.craftNo.value) {
            condition.push({
                name: vm.query.craftNo.name,
                value: vm.query.craftNo.value
            });
        }
        if (vm.query.processName.value) {
        	condition.push({
        		name: vm.query.processName.name,
        		value: vm.query.processName.value
        	});
        }
        vm.page.pageNo = 1;
        vm.page.condition = condition;

        // 查询数据
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    };

    //判断是否正在盘点
    vm.isInventory = function (form) 
    {
    	vm.addPopoDetection(form);
    }

    
    // 加载权限
    function loadRight() {
        //是否可以查看所有人的
        vm.queryAll = CommonUtil.dataRangeIsAll("10505", BaseUtil.getMenuList());

        MainCtrl.blockUI({
            animate: true,
        });

        upida.get("common/rightall?prefix=popoinspect").then(function (data) {
            vm.right.view = data.view;
            vm.right.edit = data.edit;
            vm.right.manage = data.manage;
            vm.page.pageNo = 1;
            
            // 加载操作人和检测人
            upida.get("quality/detectionRecord/getUser").then(function (data) {
                vm.users = data;
                MainCtrl.unblockUI();
            });
            
            MainCtrl.unblockUI();
            // 初始化第一页，条件为空
            vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
        });
    }

    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
    });
}]);


   