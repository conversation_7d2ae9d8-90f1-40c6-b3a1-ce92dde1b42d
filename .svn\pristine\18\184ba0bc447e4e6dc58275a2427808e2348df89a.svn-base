package com.kyb.pcberp.modules.stock.dao;

import java.util.List;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.stock.entity.MatOutBoundDeail;
import com.kyb.pcberp.modules.stock.entity.MatOutbound;

@MyBatisDao
public interface MatOutboundDao
{
    void insert(MatOutbound matOutbound);
    
    List<MatOutbound> findList(MatOutbound matOutbound);
    
    MatOutbound get(MatOutbound matOutbound);
    
    String getOutBoundIds(MatOutbound matOutbound);
    
    List<MatOutbound> loadOutBoundList(MatOutbound matOutbound);
    
    List<MatOutBoundDeail> getOutBoundDeail(MatOutbound matOutbound);
    
    Integer getOutBoundCount(MatOutbound matOutbound);
    
    List<MatOutBoundDeail> loadWaitOutBoundList(MatOutbound matOutbound);
    
    Integer findDeliveryNoisEnable(MatOutbound matOutbound);
    
    void insertDelivery(MatOutbound matOutbound);
    
    void runInsertDeail(MatOutBoundDeail matOutBoundDeail);
    
    void runBatchAdd(MatOutBoundDeail matOutBoundDeail);
    
    List<MatOutBoundDeail> getListById(MatOutbound matOutbound);
    
    Integer checkCount(MatOutBoundDeail deail);
    
    void delete(MatOutbound matOutbound);
    
    List<MatOutBoundDeail> loadComplaintWaitOutBoundList(MatOutbound matOutbound);
}
