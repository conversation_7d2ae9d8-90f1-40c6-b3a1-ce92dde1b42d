package com.kyb.pcberp.modules.wechat.service;

import java.math.BigDecimal;
import java.util.*;

import com.kyb.pcberp.modules.approval.dao.ApprovalDao;
import com.kyb.pcberp.modules.purch.dao.*;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.CommonUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.inter.dao.InterOrderConfigMainDao;
import com.kyb.pcberp.modules.inter.entity.InterProductConversion;
import com.kyb.pcberp.modules.inter.entity.InterProductConversionDeail;
import com.kyb.pcberp.modules.purch.entity.*;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.dao.MaterialSpecificationDao;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.MaterialSpecification;
import com.kyb.pcberp.modules.sys.dao.BranchDao;
import com.kyb.pcberp.modules.sys.entity.Branch;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.wechat.dao.AccountBindDao;
import com.kyb.pcberp.modules.wechat.dao.IcloudCooperationDao;
import com.kyb.pcberp.modules.wechat.entity.*;

@Service
@Transactional(readOnly = true)
public class CooperationService
{
    @Autowired
    private IcloudCooperationDao icloudCooperationDao;

    @Autowired
    private AccountBindDao accountBindDao;

    @Autowired
    private SupplierDao supplierDao;

    @Autowired
    private InterOrderConfigMainDao interOrderConfigMainDao;

    @Autowired
    private MaterialDao materialDao;

    @Autowired
    private MaterialSpecificationDao materialSpecificationDao;

    @Autowired
    private BiddingDao biddingDao;

    @Autowired
    private PurchasingDao purchasingDao;

    @Autowired
    private PurchasingDetailDao purchasingDetailDao;

    @Autowired
    private BranchDao branchDao;

    @Autowired
    private PurchRawDao purchRawDao;

    @Autowired
    private ApprovalDao approvalDao;

    @Autowired
    private CompanyDao companyDao;

    @Transactional(readOnly = false)
    public String apply(IcloudCooperation icloudCooperation, String openId)
    {
        if (StringUtils.isBlank(icloudCooperation.getCustomerId())
            || StringUtils.isBlank(icloudCooperation.getSupplierId()))
        {
            return "请选择合作方以及申请的企业方";
        }
        // 判断合作关系是否已经建立
        String recordId = icloudCooperationDao.getCooperationNum(icloudCooperation);
        IcloudUser user = accountBindDao.getUser(openId);
        icloudCooperation.setApplyManId(user.getRecordId());
        if (StringUtils.isBlank(recordId))
        {
            icloudCooperationDao.apply(icloudCooperation);
        }
        else
        {
            if (StringUtils.isBlank(icloudCooperation.getFlag()))
            {
                return "已经和该企业有合作关系了，如要增加产品请至发布产品";
            }
            icloudCooperation.setRecordId(recordId);
        }
        // 关联产品
        if (icloudCooperation.getProductList() != null && icloudCooperation.getProductList().size() > 0)
        {
            icloudCooperationDao.insertCoopProductList(icloudCooperation);
        }
        return "success";
    }

    public List<InterProduct> getCoopProductList(String recordId)
    {
        return icloudCooperationDao.getCoopProductList(recordId);
    }

    public List<IcloudCooperation> getCoopList(String recordId)
    {
        return icloudCooperationDao.getCooperationList(recordId);
    }

    public List<IcloudCooperation> getCoopCustomerList(IcloudCooperation icloudCooperation, String openId)
    {
        if (StringUtils.isBlank(icloudCooperation.getSupplierId()))
        {
            return null;
        }
        IcloudUser user = accountBindDao.getUser(openId);
        if (user == null || StringUtils.isBlank(user.getPhone()))
        {
            return null;
        }
        return icloudCooperationDao.getCooperationList(user.getRecordId());
    }

    @Transactional(readOnly = false)
    public String updateStatus(IcloudCooperation icloudCooperation, String openId)
    {
        IcloudUser user = accountBindDao.getUser(openId);
        icloudCooperation.setThroughManId(user.getRecordId());
        if (!(StringUtils.isNotBlank(icloudCooperation.getDealStatus()) && icloudCooperation.getDealStatus()
            .equals("edit")))
        {
            icloudCooperationDao.updateStatus(icloudCooperation);
        }
        // 拒绝未选中的产品
        icloudCooperation.setProductStatus("3");
        icloudCooperationDao.updateProductStatus(icloudCooperation);
        if (StringUtils.isNotBlank(icloudCooperation.getStatus()) && icloudCooperation.getStatus().equals("2"))
        {
            if (icloudCooperation.getProductList() != null && icloudCooperation.getProductList().size() > 0)
            {
                // 更新选中的产品
                icloudCooperation.setProductStatus(icloudCooperation.getStatus());
                icloudCooperationDao.updateListStatus(icloudCooperation);
            }
        }
        return "操作成功";
    }

    public void generatedMaterial(InterProduct product,Company company,IcloudCompany supplier)
    {
        if (StringUtils.isNotBlank(product.getConfigId()))
        {
            InterProductConversion interProductConversion = new InterProductConversion();
            interProductConversion.setCompanyId(company.getRecordId());
            interProductConversion.setPageId(product.getConfigId());
            List<InterProductConversion> conversions =
                interOrderConfigMainDao.getConversionList(interProductConversion);
            if (conversions != null && conversions.size() > 0)
            {
                InterProductConversion conversion = conversions.get(0);
                company.setCode(conversion.getMaterialTypeId());
                List<MaterialSpecification> list = interOrderConfigMainDao.findSpecificationList(company);
                List<InterProductConversionDeail> deailList =
                    interOrderConfigMainDao.getConversionDeailList(conversion.getRecordId());
                Material material = new Material();
                material.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.MANAGEMENT.getIndex()
                    .toString(), company));
                material.setCompany(company);
                material.setMaterialKind(100701);
                DictValue materialType = new DictValue(conversion.getMaterialTypeId());
                material.setMaterialType(materialType);
                material.setIcloudSupplierId(supplier.getRecordId());
                material.setIcloudId(product.getRecordId());
                // 工艺插入
                List<MaterialSpecification> specificationList = new ArrayList<>();
                if (list.size() > 0 && "板材".equals(list.get(0).getMaterialTypeValue()))
                {
                    board(specificationList, product, list, deailList, company, material);
                }
                else
                {
                    if (product.getDeailList() != null && product.getDeailList().size() > 0)
                    {
                        for (MaterialSpecification specification : list)
                        {
                            for (InterProductConversionDeail deail : deailList)
                            {
                                if (specification.getRecordId().equals(deail.getSpecificationId()))
                                {
                                    String configId = deail.getConfigDeailId();
                                    for (InterProductDeail interProductDeail : product.getDeailList())
                                    {
                                        if (StringUtils.isNotBlank(interProductDeail.getConfigDeailId()))
                                        {
                                            if (interProductDeail.getConfigDeailId().equals(configId))
                                            {
                                                if(StringUtils.isNotBlank(specification.getName()))
                                                {
                                                    if (StringUtils.isNotBlank(specification.getDictItemId())
                                                        && StringUtils.isNotBlank(interProductDeail.getDictValue()))
                                                    {
                                                        String value = interOrderConfigMainDao.getDictIdByValue(
                                                            interProductDeail.getDictValue(),
                                                            company.getRecordId(),
                                                            specification.getDictItemId());
                                                        specification.setValue(value);
                                                        specification.setDictValue(interProductDeail.getDictValue());
                                                        interProductDeail.setUseFlag("1");
                                                    }
                                                    else if (interProductDeail.getInputType().equals("2"))
                                                    {
                                                        if (StringUtils.isNotBlank(specification.getName())
                                                            && specification.getName().equals("长"))
                                                        {
                                                            specification.setValue(interProductDeail.getValue1());
                                                            interProductDeail.setUseFlag("1");
                                                        }
                                                        else if (StringUtils.isNotBlank(specification.getName())
                                                            && specification.getName().equals("宽"))
                                                        {
                                                            specification.setValue(interProductDeail.getValue2());
                                                            interProductDeail.setUseFlag("1");
                                                        }
                                                        else
                                                        {
                                                            String value = interProductDeail.getValue1() + "*"
                                                                + interProductDeail.getValue2();
                                                            specification.setValue(value);
                                                            interProductDeail.setUseFlag("1");
                                                        }
                                                    }
                                                    else if (interProductDeail.getInputType().equals("3"))
                                                    {
                                                        if (StringUtils.isNotBlank(specification.getName())
                                                            && specification.getName().equals("长"))
                                                        {
                                                            specification.setValue(interProductDeail.getValue1());
                                                            interProductDeail.setUseFlag("1");
                                                        }
                                                        else if (StringUtils.isNotBlank(specification.getName())
                                                            && specification.getName().equals("宽"))
                                                        {
                                                            specification.setValue(interProductDeail.getValue2());
                                                            interProductDeail.setUseFlag("1");
                                                        }
                                                        else if (StringUtils.isNotBlank(specification.getName())
                                                            && specification.getName().equals("拼接数"))
                                                        {
                                                            specification.setValue(interProductDeail.getValue3());
                                                            interProductDeail.setUseFlag("1");
                                                        }
                                                        else
                                                        {
                                                            String value = interProductDeail.getValue1() + "*"
                                                                + interProductDeail.getValue2() + "/"
                                                                + interProductDeail.getValue3();
                                                            specification.setValue(value);
                                                            interProductDeail.setUseFlag("1");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        specification.setValue(interProductDeail.getValue());
                                                        interProductDeail.setUseFlag("1");
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    specificationList.add(specification);
                                }
                            }
                        }
                    }
                    String remark = "";
                    if (StringUtils.isNotBlank(product.getName()))
                    {
                        if (StringUtils.isNotBlank(remark))
                        {
                            remark = remark + " " + product.getName();
                        }
                        else
                        {
                            remark = product.getName();
                        }
                    }
                    if (StringUtils.isNotBlank(product.getSpecifications()))
                    {
                        if (StringUtils.isNotBlank(remark))
                        {
                            remark = remark + " " + product.getSpecifications();
                        }
                        else
                        {
                            remark = product.getSpecifications();
                        }
                    }
                    for (InterProductDeail interProductDeail : product.getDeailList())
                    {
                        if (StringUtils.isBlank(interProductDeail.getUseFlag()))
                        {
                            if (StringUtils.isNotBlank(interProductDeail.getConfigDeailId()))
                            {
                                String value = "";
                                if (StringUtils.isNotBlank(interProductDeail.getDictValue()))
                                {
                                    value = interProductDeail.getDictValue();

                                }
                                else if (interProductDeail.getInputType().equals("2"))
                                {
                                    if (StringUtils.isNotBlank(interProductDeail.getValue1())
                                        && StringUtils.isNotBlank(interProductDeail.getValue2()))
                                    {
                                        value =
                                            interProductDeail.getValue1() + "*" + interProductDeail.getValue2();
                                    }
                                }
                                else if (interProductDeail.getInputType().equals("3"))
                                {
                                    if (StringUtils.isNotBlank(interProductDeail.getValue1())
                                        && StringUtils.isNotBlank(interProductDeail.getValue2())
                                        && StringUtils.isNotBlank(interProductDeail.getValue3()))
                                    {
                                        value =
                                            interProductDeail.getValue1() + "*" + interProductDeail.getValue2()
                                                + "/" + interProductDeail.getValue3();
                                    }
                                }
                                else
                                {
                                    value = interProductDeail.getValue();
                                }
                                if (StringUtils.isNotBlank(value))
                                {
                                    if (StringUtils.isNotBlank(remark))
                                    {
                                        remark = remark + " " + value;
                                    }
                                    else
                                    {
                                        remark = value;
                                    }
                                }
                            }
                        }
                    }
                    material.setRemark(remark);
                    String specificationStr = "";
                    for (MaterialSpecification specification : list)
                    {
                        if (StringUtils.isNotBlank(specification.getDictItemId()))
                        {
                            if (StringUtils.isNotBlank(specification.getDictValue()))
                            {
                                specificationStr = StringUtils.isNotBlank(specificationStr)?" "+specification.getDictValue():specification.getDictValue();
                            }
                        }
                        else if (StringUtils.isNotBlank(specification.getName()))
                        {
                            if (StringUtils.isNotBlank(specification.getValue()))
                            {
                                specificationStr += StringUtils.isNotBlank(specificationStr)?" "+specification.getValue():specification.getValue();
                            }
                        }
                    }
                    if (StringUtils.isNotBlank(specificationStr))
                    {
                        material.setSpecification(specificationStr);
                    }
                    else
                    {
                        material.setSpecification(product.getSpecifications());
                    }
                }

                material.setMaterialSpecList(specificationList);
                material.preInsert();
                materialDao.insert(material);
                CommonUtils.updateNextNo(CommonEnums.CodeType.MANAGEMENT.getIndex());
                if (Collections3.isNotEmpty(material.getMaterialSpecList()))
                {
                    material.getMaterialSpecList().forEach(item -> {
                        item.preInsert();
                    });
                    materialSpecificationDao.deleteRelationByMaterial(material);
                    materialSpecificationDao.insertRelationByMaterial(material);
                }
            }
        }
    }

    @Transactional(readOnly = false)
    public void generateErpData(IcloudCooperation icloudCooperation, IcloudCompany customer, IcloudCompany supplier,
        List<InterProduct> productList)
    {
        if (StringUtils.isNotBlank(customer.getDbDefaultName()) && StringUtils.isNotBlank(customer.getErpCompanyId()))
        {
            Company company = new Company(customer.getErpCompanyId());
            // 切换数据库
            if (!(StringUtils.isNotBlank(icloudCooperation.getDealStatus()) && icloudCooperation.getDealStatus()
                .equals("edit")))
            {
                supplier.setCompany(company);
                // 验证供应商是否已经存在，存在更新icloudId
                String recordId = icloudCooperationDao.getErpSupplier(supplier);
                if (StringUtils.isNotBlank(recordId))
                {
                    Supplier sup = new Supplier(recordId);
                    sup.setIcloudId(supplier.getRecordId());
                    icloudCooperationDao.updateErpCompany(sup);
                }
                else
                {
                    Supplier sup = new Supplier();
                    sup.setCompany(company);
                    Integer noNum = 1;
                    while (noNum != null && noNum > 0)
                    {
                        sup.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.SUPPLIER.getIndex().toString(),
                            company));
                        noNum = supplierDao.findSupplierNoisEnable(sup); // 查询编号是否依旧存在
                        if (null != noNum && noNum > 0)
                        {
                            // 将对应编码的nextNo 修改为+1
                            CommonUtils.updateNextNo(CommonEnums.CodeType.SUPPLIER.getIndex(), company);
                        }
                    }
                    supplier.setNo(sup.getNo());
                    if(StringUtils.isBlank(supplier.getShortName()))
                    {
                        supplier.setShortName(supplier.getName());
                    }
                    // 生成供应商
                    icloudCooperationDao.insertErpCompany(supplier);
                }
            }
            if (productList != null && productList.size() > 0)
            {
                // 产品转换
                for (InterProduct product : productList)
                {
                    generatedMaterial(product,company,supplier);
                }
            }
        }
    }

    // 物料类型为板材
    private void board(List<MaterialSpecification> specificationList,InterProduct product,List<MaterialSpecification> list,
            List<InterProductConversionDeail> deailList,Company company,Material material){
        if (product.getDeailList() != null && product.getDeailList().size() > 0)
        {
            for (MaterialSpecification specification : list)
            {
                for (InterProductConversionDeail deail : deailList)
                {
                    if (specification.getRecordId().equals(deail.getSpecificationId()))
                    {
                        String configId = deail.getConfigDeailId();
                        for (InterProductDeail interProductDeail : product.getDeailList())
                        {
                            if (StringUtils.isNotBlank(interProductDeail.getConfigDeailId()))
                            {
                                if (interProductDeail.getConfigDeailId().equals(configId))
                                {
                                    if(StringUtils.isNotBlank(interProductDeail.getInputType()))
                                    {
                                        if (StringUtils.isNotBlank(specification.getDictItemId())
                                            && StringUtils.isNotBlank(interProductDeail.getDictValue()))
                                        {
                                            String value = interOrderConfigMainDao.getDictIdByValue(
                                                interProductDeail.getDictValue(),
                                                company.getRecordId(),
                                                specification.getDictItemId());
                                            specification.setValue(value);
                                            specification.setDictValue(interProductDeail.getDictValue());
                                            interProductDeail.setUseFlag("1");
                                        }
                                        else if (interProductDeail.getInputType().equals("2"))
                                        {
                                            if (StringUtils.isNotBlank(specification.getName())
                                                && specification.getName().equals("长"))
                                            {
                                                specification.setValue(interProductDeail.getValue1());
                                                interProductDeail.setUseFlag("1");
                                            }
                                            else if (StringUtils.isNotBlank(specification.getName())
                                                && specification.getName().equals("宽"))
                                            {
                                                specification.setValue(interProductDeail.getValue2());
                                                interProductDeail.setUseFlag("1");
                                            }
                                            else
                                            {
                                                String value = interProductDeail.getValue1() + "*"
                                                    + interProductDeail.getValue2();
                                                specification.setValue(value);
                                                interProductDeail.setUseFlag("1");
                                            }
                                        }
                                        else if (interProductDeail.getInputType().equals("3"))
                                        {
                                            if (StringUtils.isNotBlank(specification.getName())
                                                && specification.getName().equals("长"))
                                            {
                                                specification.setValue(interProductDeail.getValue1());
                                                interProductDeail.setUseFlag("1");
                                            }
                                            else if (StringUtils.isNotBlank(specification.getName())
                                                && specification.getName().equals("宽"))
                                            {
                                                specification.setValue(interProductDeail.getValue2());
                                                interProductDeail.setUseFlag("1");
                                            }
                                            else if (StringUtils.isNotBlank(specification.getName())
                                                && specification.getName().equals("拼接数"))
                                            {
                                                specification.setValue(interProductDeail.getValue3());
                                                interProductDeail.setUseFlag("1");
                                            }
                                            else
                                            {
                                                String value = interProductDeail.getValue1() + "*"
                                                    + interProductDeail.getValue2() + "/"
                                                    + interProductDeail.getValue3();
                                                specification.setValue(value);
                                                interProductDeail.setUseFlag("1");
                                            }
                                        }
                                        else
                                        {
                                            specification.setValue(interProductDeail.getValue());
                                            interProductDeail.setUseFlag("1");
                                        }
                                    }
                                    else
                                    {
                                        specification.setValue(interProductDeail.getValue());
                                        interProductDeail.setUseFlag("1");
                                    }
                                }
                            }
                        }
                        specificationList.add(specification);
                    }
                }
            }
        }
        String remark = "";
        if (StringUtils.isNotBlank(product.getName()))
        {
            if (StringUtils.isNotBlank(remark))
            {
                remark = remark + " " + product.getName();
            }
            else
            {
                remark = product.getName();
            }
        }
        if (StringUtils.isNotBlank(product.getSpecifications()))
        {
            if (StringUtils.isNotBlank(remark))
            {
                remark = remark + " " + product.getSpecifications();
            }
            else
            {
                remark = product.getSpecifications();
            }
        }
        for (InterProductDeail interProductDeail : product.getDeailList())
        {
            if (StringUtils.isBlank(interProductDeail.getUseFlag()))
            {
                if (StringUtils.isNotBlank(interProductDeail.getConfigDeailId()))
                {
                    String value = "";
                    if (StringUtils.isNotBlank(interProductDeail.getDictValue()))
                    {
                        value = interProductDeail.getDictValue();

                    }
                    else if (interProductDeail.getInputType().equals("2"))
                    {
                        if (StringUtils.isNotBlank(interProductDeail.getValue1())
                                && StringUtils.isNotBlank(interProductDeail.getValue2()))
                        {
                            value =
                                    interProductDeail.getValue1() + "*" + interProductDeail.getValue2();
                        }
                    }
                    else if (interProductDeail.getInputType().equals("3"))
                    {
                        if (StringUtils.isNotBlank(interProductDeail.getValue1())
                                && StringUtils.isNotBlank(interProductDeail.getValue2())
                                && StringUtils.isNotBlank(interProductDeail.getValue3()))
                        {
                            value =
                                    interProductDeail.getValue1() + "*" + interProductDeail.getValue2()
                                            + "/" + interProductDeail.getValue3();
                        }
                    }
                    else
                    {
                        value = interProductDeail.getValue();
                    }
                    if (StringUtils.isNotBlank(value))
                    {
                        if (StringUtils.isNotBlank(remark))
                        {
                            remark = remark + " " + value;
                        }
                        else
                        {
                            remark = value;
                        }
                    }
                }
            }
        }
        material.setRemark(remark);
        String madeSupplierName = "";
        String board = "";
        String thickness = "";

        String copperClad = "";
        String length = "";
        String width = "";

        String daore = "";
        String naiya = "";

        String film = "";
        String pcbType = "";
        String simple = "";
        String gum = "";
        String insulat = "";
        String cuprum = "";
        String special = "";
        for (MaterialSpecification specification : list)
        {
            if (StringUtils.isNotBlank(specification.getDictItemId()))
            {
                if (StringUtils.isNotBlank(specification.getDictValue()))
                {
                    if (specification.getDictItemId().equals("70"))
                    {
                        madeSupplierName = specification.getDictValue();
                    }
                    else if (specification.getDictItemId().equals("2"))
                    {
                        board = specification.getDictValue();
                    }
                    else if (specification.getDictItemId().equals("3"))
                    {
                        thickness = specification.getDictValue();
                    }
                    else if (specification.getDictItemId().equals("58"))
                    {
                        daore = specification.getDictValue();
                    }
                    else if (specification.getDictItemId().equals("59"))
                    {
                        naiya = specification.getDictValue();
                    }
                    else if (specification.getDictItemId().equals("31"))
                    {
                        copperClad = specification.getDictValue();
                    }
                    else if (specification.getDictItemId().equals("71"))
                    {
                        film = specification.getDictValue();
                    }
                    else if (specification.getDictItemId().equals("1"))
                    {
                        pcbType = specification.getDictValue();
                    }
                }
            }
            else if (StringUtils.isNotBlank(specification.getName()))
            {
                if (StringUtils.isNotBlank(specification.getValue()))
                {
                    if (specification.getName().equals("长"))
                    {
                        length = specification.getValue();
                    }
                    else if (specification.getName().equals("宽"))
                    {
                        width = specification.getValue();
                    }
                    else if (specification.getName().equals("样品"))
                    {
                        simple = specification.getValue();
                    }
                    else if (specification.getName().equals("胶"))
                    {
                        gum = specification.getValue();
                    }
                    else if (specification.getName().equals("绝缘层"))
                    {
                        insulat = specification.getValue();
                    }
                    else if (specification.getName().equals("无铜"))
                    {
                        cuprum = specification.getValue();
                    }
                    else if (specification.getName().equals("特殊要求"))
                    {
                        special = specification.getValue();
                    }
                }
            }
        }
        String materialName = "";
        String specification = "";
        if (StringUtils.isNotBlank(madeSupplierName))
        {
            materialName = madeSupplierName;

            if (StringUtils.isNotBlank(board))
            {
                materialName = materialName + " " + board;
            }
        }
        else
        {
            if (StringUtils.isNotBlank(board))
            {
                materialName = board;
            }
        }
        if (StringUtils.isNotBlank(thickness))
        {
            String thicknessStr = thickness.replace("mm", "厚");
            specification = thicknessStr;
        }
        if (StringUtils.isNotBlank(copperClad))
        {
            copperClad = copperClad.toLowerCase();
            String copperCladStr = "";
            if (copperClad == "2/0")
            {
                copperCladStr = "70um";
            }
            else if (copperClad == "1/0")
            {
                copperCladStr = "35um";
            }
            else if (copperClad == "h/0")
            {
                copperCladStr = "18um";
            }
            else if (copperClad == "18um")
            {
                copperCladStr = "18um";
            }
            else if (copperClad == "3/0")
            {
                copperCladStr = "105um";
            }
            else if (copperClad == "28um")
            {
                copperCladStr = "28um";
            }
            else if (copperClad == "25um")
            {
                copperCladStr = "25um";
            }
            else if (copperClad == "15um")
            {
                copperCladStr = "15um";
            }
            else if (copperClad == "12um")
            {
                copperCladStr = "12um";
            }
            else if (copperClad == "30um")
            {
                copperCladStr = "30um";
            }
            else if (copperClad == "35um")
            {
                copperCladStr = "35um";
            }
            specification = specification + copperCladStr;
        }
        if (StringUtils.isNotBlank(specification))
        {
            specification = specification + " ";
        }

        if (StringUtils.isNotBlank(length))
        {
            specification = specification + length + "*";
        }
        if (StringUtils.isNotBlank(width))
        {
            specification = specification + width + " ";
        }
        if (StringUtils.isNotBlank(daore))
        {
            specification = specification + daore + " ";
        }
        if (StringUtils.isNotBlank(naiya))
        {
            specification = specification + naiya + " ";
        }
        if (StringUtils.isNotBlank(film))
        {
            specification = specification + film;
        }
        if (StringUtils.isNotBlank(pcbType))
        {
            specification = specification + pcbType;
        }

        if (StringUtils.isNotBlank(simple))
        {
            specification = specification + simple;
        }
        if (StringUtils.isNotBlank(gum))
        {
            specification = specification + gum;
        }
        if (StringUtils.isNotBlank(insulat))
        {
            specification = specification + insulat;
        }
        if (StringUtils.isNotBlank(cuprum))
        {
            specification = specification + cuprum;
        }
        if (StringUtils.isNotBlank(special))
        {
            specification = specification + special;
        }
        if (StringUtils.isNotBlank(materialName))
        {
            material.setName(materialName);
        }
        else
        {
            material.setName(product.getName());
        }
        if (StringUtils.isNotBlank(specification))
        {
            material.setSpecification(specification);
        }
        else
        {
            material.setSpecification(product.getSpecifications());
        }
    }

    public Map<String, List<?>> loadBiddingData()
    {
        Map<String, List<?>> map = new HashMap<>();
        Company company = new Company("17");
        // 结款方式
        map.put("payWayList", DictUtils.getValuesByItem(CommonEnums.DictItemEnum.PAY_WAYS, company));
        // 含税说明
        map.put("taxDescriptList", DictUtils.getValuesByItem(CommonEnums.DictItemEnum.TAX_DESCRIPTS, company));
        // 货币类型
        map.put("currencyTypeList", DictUtils.getValuesByItem(CommonEnums.DictItemEnum.CURRENCY_TYPE, company));
        // 付款方式
        map.put("paycauseList", DictUtils.getValuesByItem(CommonEnums.DictItemEnum.PAY_CAUSE, company));
        // 交货天数
        map.put("deliveryDayList", DictUtils.getValuesByItem(CommonEnums.DictItemEnum.DELIVERY_DAYS, company));
        return map;
    }

    public List<IcloudBidding> getBiddingList(String userId)
    {
        List<IcloudBidding> biddingList = icloudCooperationDao.getBiddingList(userId);
        String biddingIds = "";
        String mainIds = "";
        for (IcloudBidding bidding : biddingList)
        {
            biddingIds = StringUtils.isNotBlank(biddingIds) ? biddingIds + "," + bidding.getBiddingId() : bidding.getBiddingId();
            mainIds = StringUtils.isNotBlank(mainIds) ? mainIds + "," + bidding.getRecordId() : bidding.getRecordId();
        }
        List<SupplierBiddeAttache> supplierAttacheList = icloudCooperationDao.getSupplierAttacheList(biddingIds);
        List<IcloudBiddingDelivery> list = icloudCooperationDao.getBiddingDeliveryList(mainIds);
        List<IcloudBidding> resList = new ArrayList<>();
        for (IcloudBidding bidding : biddingList)
        {
            List<SupplierBiddeAttache> attacheList = new ArrayList<>();
            for (SupplierBiddeAttache attache : supplierAttacheList)
            {
                if (attache.getSupplierBilldeId().equals(bidding.getBiddingId())){
                    attacheList.add(attache);
                }
            }
            bidding.setSupplierBiddeAttacheList(attacheList);

            List<IcloudBiddingDelivery> deliveryList = new ArrayList<>();
            for (IcloudBiddingDelivery delivery : list)
            {
                if (delivery.getMainId().equals(bidding.getRecordId())){
                    deliveryList.add(delivery);
                }
            }
            bidding.setList(deliveryList);
            // 判断是否已经开始竞标
            if (bidding.getStartTime() != null && bidding.getEndTime() != null)
            {
                Date currDate = new Date();
                if (bidding.getStartTime().compareTo(currDate) < 0)
                {
                    if (bidding.getEndTime().compareTo(currDate) < 0)
                    {
                        // 已经超时
                        bidding.setStartFlag(3);
                    }
                    else
                    {
                        // 正在进行
                        bidding.setStartFlag(1);
                    }
                }
                else
                {
                    // 还未开始
                    bidding.setStartFlag(2);
                }
                if (bidding.getStartFlag() != null && bidding.getStartFlag() > 0){
                    resList.add(bidding);
                }
            }
        }
        resList.sort((x, y) -> Integer.compare(x.getStartFlag(), y.getStartFlag()));
        return resList;
    }

//    public List<IcloudBidding> getMyBiddingList(IcloudBidding icloudBidding, String openId)
//    {
//        IcloudUser user = accountBindDao.getUser(openId);
//        icloudBidding.setUserId(user.getRecordId());
//        icloudBidding.setPhone(user.getPhone());
//        List<IcloudBidding> biddingList = icloudCooperationDao.getMyBiddingList(icloudBidding);
//        List<IcloudBidding> resList = new ArrayList<>();
//        for (IcloudBidding bidding : biddingList)
//        {
//            List<IcloudBiddingDelivery> list = icloudCooperationDao.getBiddingDeliveryList(bidding);
//            bidding.setList(list);
//            // 判断是否已经开始竞标
//            if (bidding.getStartTime() != null && bidding.getEndTime() != null)
//            {
//                Date currDate = new Date();
//                if (bidding.getStartTime().compareTo(currDate) < 0)
//                {
//                    if (bidding.getEndTime().compareTo(currDate) < 0)
//                    {
//                        // 已经超时
//                        bidding.setStartFlag("3");
//                    }
//                    else
//                    {
//                        // 正在进行
//                        bidding.setStartFlag("2");
//                    }
//                }
//                else
//                {
//                    // 还未开始
//                    bidding.setStartFlag("1");
//                }
//                resList.add(bidding);
//            }
//        }
//        return biddingList;
//    }

    @Transactional(readOnly = false)
    public String saveBidding(IcloudBidding icloudBidding)
    {
        if (StringUtils.isBlank(icloudBidding.getRecordId()))
        {
            return "请刷新重试";
        }
        if (icloudBidding.getStartTime() != null && icloudBidding.getEndTime() != null)
        {
            Date currDate = new Date();
            if (icloudBidding.getStartTime().compareTo(currDate) < 0)
            {
                if (icloudBidding.getEndTime().compareTo(currDate) < 0)
                {
                    return "招标已经截止，请退出";
                }
            }
            else
            {
                return "还未开始招标,请刷新重试";
            }
        }
        else
        {
            return "请刷新重试";
        }
        icloudCooperationDao.updateBidding(icloudBidding);
        icloudCooperationDao.deleteBiddingDeliveryList(icloudBidding);
        if (icloudBidding.getList() != null && icloudBidding.getList().size() > 0)
        {
            icloudCooperationDao.insertBiddingDeliveryList(icloudBidding);
        }
        return "操作成功";
    }

    @Transactional(readOnly = false)
    public String biddingPurch(IcloudBidding icloudBidding, String openId)
    {
        IcloudUser user = accountBindDao.getUser(openId);
        icloudBidding.setUserId(user.getRecordId());
        icloudBidding.setStatus("4");
        biddingDao.editIcloudStatus(icloudBidding);

        // 获取审批单备份的物料id
        String materialId = approvalDao.getMaterialId(icloudBidding);
        if(StringUtils.isNotBlank(materialId))
        {
            // 更新原料申请单物料id
            icloudBidding.setMaterialId(materialId);
            purchRawDao.updateMaterialId(icloudBidding);
        }
        // 更新云平台招标表名称/规格
        biddingDao.updateIcloudBiddingData(icloudBidding.getBiddingId());
        return "success";
    }

    public String getValue(String val, String companyId, String itemId)
    {
        return interOrderConfigMainDao.getDictIdByValue(val, companyId, itemId);
    }

    @Transactional(readOnly = false)
    public void biddingPurchErp(IcloudBidding icloudBidding)
    {
        PurchRaw purchRaw = icloudCooperationDao.getPurchRawByBiddingId(icloudBidding.getBiddingId());
        if (StringUtils.isBlank(purchRaw.getRecordId()))
        {
            return;
        }
        Company company = new Company(icloudBidding.getErpCompanyId());
        // 采购单
        Purchasing purchasing = new Purchasing();
        purchasing.setCompany(company);
        purchasing.setType("1");
        String no = CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString(), company);
        purchasing.setNo(no);
        Integer noNum = purchasingDao.findPurchasingNoisEnable(purchasing); // 查询编号是否依旧存在
        while (noNum != null && noNum > 0)
        {
            CommonUtils.updateNextNo(CommonEnums.CodeType.PURCHASEORDER.getIndex(),company);
            purchasing.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString(),company));
            noNum = purchasingDao.findPurchasingNoisEnable(purchasing); // 查询编号是否依旧存在
        }
        purchasing.setCreatedDate(new Date());
        purchasing.setOrderDate(new Date());
        purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_UNCONFIRM);
        if (StringUtils.isNotBlank(icloudBidding.getPayWayVal()))
        {
            String value = getValue(icloudBidding.getPayWayVal(),
                company.getRecordId(),
                CommonEnums.DictItemEnum.PAY_WAYS.toString());
            purchasing.setPayWay(Integer.valueOf(value));
        }
        if (StringUtils.isNotBlank(icloudBidding.getTaxDescriptVal()))
        {
            String value = getValue(icloudBidding.getTaxDescriptVal(),
                company.getRecordId(),
                CommonEnums.DictItemEnum.TAX_DESCRIPTS.toString());
            purchasing.setTaxDescript(Long.valueOf(value));
        }
        if (StringUtils.isNotBlank(icloudBidding.getCurrencyTypeVal()))
        {
            String value = getValue(icloudBidding.getCurrencyTypeVal(),
                company.getRecordId(),
                CommonEnums.DictItemEnum.CURRENCY_TYPE.toString());
            purchasing.setCurrencyType(Long.valueOf(value));
        }
        if (StringUtils.isNotBlank(icloudBidding.getPaycauseVal()))
        {
            String value = getValue(icloudBidding.getPaycauseVal(),
                company.getRecordId(),
                CommonEnums.DictItemEnum.PAY_CAUSE.toString());
            purchasing.setPaycause(Long.valueOf(value));
        }
        IcloudCompany icloudCompany = new IcloudCompany();
        icloudCompany.setRecordId(icloudBidding.getSupplierId());
        icloudCompany.setCompany(company);
        String recordId = icloudCooperationDao.getErpSupplierByIcloudId(icloudCompany);
        Supplier supplier = new Supplier(recordId);
        purchasing.setSupplier(supplier);
        Branch branch = new Branch();
        branch.setCompany(company);
        List<Branch> branchList = branchDao.findList(branch);
        if (branchList.size() > 0)
        {
            purchasing.setBranch(branchList.get(0));
            purchasing.setDeliveryPlace(branchList.get(0).getAddress());
        }
        if (null != purchRaw.getCreatedBy() && StringUtils.isNotBlank(purchRaw.getCreatedBy().getRecordId()))
        {
            purchasing.setPurchaser(purchRaw.getCreatedBy());
        }
        purchasingDao.insert(purchasing);
        CommonUtils.updateNextNo(8,company);
        // 采购明细
        PurchasingDetail detail = new PurchasingDetail();
        detail.setQuantity(icloudBidding.getMaterialNum());
        detail.setPrice(icloudBidding.getPrice());
        detail.setDeliveryDate(icloudBidding.getDeliveryDate());
        detail.setMaterial(new Material(purchRaw.getMaterialId()));
        detail.setCraftDescript(purchRaw.getCraftDescript());
        detail.setSupplierModel(purchRaw.getMaterialName());
        detail.setPurchRaw(purchRaw);
        BigDecimal amount = countRawPurchingAmount(detail);
        detail.setCreatedDate(new Date());
        detail.setCompany(purchasing.getCompany());
        detail.setPurchasing(purchasing);
        detail.setStatus(TypeKey.PU_PURCHASING_STATUS_UNCONFIRM);
        detail.setAmount(amount);
        detail.preInsert();
        purchasingDetailDao.insert(detail);
        // 招标关联
        Bidding bidding = new Bidding();
        bidding.setRecordId(icloudBidding.getBiddingId());
        bidding.setPurchId(purchasing.getRecordId());
        biddingDao.updatePurchId(bidding);
    }

    public BigDecimal countRawPurchingAmount(PurchasingDetail puchasingDet)
    {
        BigDecimal money = BigDecimal.ZERO;
        if (puchasingDet == null)
        {
            return money;
        }
        BigDecimal price = new BigDecimal(0);
        if (puchasingDet.getPrice() != null)
        {
            price = puchasingDet.getPrice();
        }
        BigDecimal quantity = new BigDecimal(0);
        if (puchasingDet.getQuantity() != null)
        {
            quantity = puchasingDet.getQuantity();
        }
        return price.multiply(quantity);
    }

    @Transactional(readOnly = false)
    public String deleteBidding(IcloudBidding icloudBidding, String openId)
    {
        IcloudUser user = accountBindDao.getUser(openId);
        icloudBidding.setUserId(user.getRecordId());

        // 恢复所有供应商至等待中标状态
        IcloudBidding bidding = new IcloudBidding();
        bidding.setStatus("1");
        bidding.setBiddingId(icloudBidding.getBiddingId());
        biddingDao.editIcloudStatus(bidding);

        // 标记当前供应商为弃标
        icloudBidding.setStatus("5");
        biddingDao.editIcloudStatus(icloudBidding);
        return "success";
    }

    @Transactional(readOnly = false)
    public String deleteBiddingErp(IcloudBidding icloudBidding)
    {
        Bidding bidding = new Bidding();
        bidding.setRecordId(icloudBidding.getBiddingId());
        bidding.setStatus("2");
        biddingDao.editStatus(bidding);
        return "success";
    }

    public Map<String, String> loadReport(String openId)
    {
        IcloudUser user = accountBindDao.getUser(openId);
        Map<String, String> map = new HashMap<>();
        map.put("saleNum", icloudCooperationDao.getSaleNum(user.getPhone()));
        map.put("saleMoney", icloudCooperationDao.getSaleMoney(user.getPhone()));
        map.put("purchNum", icloudCooperationDao.getPurchNum(user.getPhone()));
        map.put("purchMoney", icloudCooperationDao.getPurchMoney(user.getPhone()));
        return map;
    }

    @Transactional(readOnly = false)
    public String applyTwo(IcloudCooperation icloudCooperation, String openId)
    {
        IcloudCooperation icOne = icloudCooperation.clone();
        icOne.setApplyFlag("1");
        String resultOne = apply(icOne,openId);

        IcloudCooperation icTwo = icloudCooperation.clone();
        icTwo.setApplyFlag("2");
        icTwo.setCustomerId(icloudCooperation.getSupplierId());
        icTwo.setSupplierId(icloudCooperation.getCustomerId());
        apply(icTwo,openId);
        if(!resultOne.equals("success"))
        {
            return resultOne;
        }
        else
        {
            return "success";
        }
    }

    public Supplier getSupplier(IcloudBidding icloudBidding)
    {
        if(null == icloudBidding)
        {
            return null;
        }
        Supplier query = new Supplier();
        query.setCompanyId(icloudBidding.getErpCompanyId());
        query.setName(icloudBidding.getSupplierName());
        return supplierDao.getSupByNameTwo(query);
    }

    public IcloudCompany getCustomer(IcloudBidding icloudBidding)
    {
        if(null == icloudBidding)
        {
            return null;
        }
        IcloudCompany company = new IcloudCompany();
        company.setRecordId(icloudBidding.getCustomerId());
        return companyDao.getCusByName(company);
    }
}
