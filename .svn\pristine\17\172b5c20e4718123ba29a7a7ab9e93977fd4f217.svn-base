package com.kyb.pcberp.modules.sys.dao;

import java.util.List;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.sys.entity.Department;
import com.kyb.pcberp.modules.sys.entity.Organization;

@MyBatisDao
public interface OrganizationDao extends CrudDao<Organization> {

	/**
	 * zjn 2018-09-26
	 * 根据部门获取组织
	 */
	public List<Organization> getOrganizationListByDept(Department dept);
	
	/**
	 * zjn 2018-09-26 
	 * 根据组织id组删除组织
	 */
	public void deleteOrganizationByIds(Organization organization);
	
	/**
	 * zjn 2018-10-14 
	 * 验证组织编号
	 */
	public Integer getOrganizationNoCount(Organization organization);
	
	/**
	 * zjn 2018-10-14 
	 * 验证组织名称
	 */
	public Integer getOrganizationNameCount(Organization organization);
}
