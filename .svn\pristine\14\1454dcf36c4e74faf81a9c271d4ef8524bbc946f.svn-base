kybApp.controller('matPreparationCtrl', ['$rootScope', '$scope', 'upida', '$timeout', '$stateParams', 'CommonUtil', 'BaseUtil', '$filter','$http', function ($rootScope, $scope, upida, $timeout, $stateParams, CommonUtil, BaseUtil, $filter,$http) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });

    var vm = this;
    // 权限
    vm.right = {};

    vm.tabs = {
        viewForm: {active: true},
/*        editForm: {active: false, show: false},*/
        summaryForm: {active: false},
        viewCapacityForm: {active: false}
    };

    vm.page = {};
    // 分页数据
/*
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    // 字典项分页数据
    vm.page.data = {};
    vm.page.data.list = {};
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "order/matPreparation/page";
    vm.page.condition = []; // 条件*/

    // 市场备料汇总
    vm.summaryPage = {};
    vm.summaryPage.pageSizeOptions = [5, 10, 30, 50];
    vm.summaryPage.data = {};
    vm.summaryPage.data.list = {};
    vm.summaryPage.pageSize = 10;
    vm.summaryPage.pageNo = 1;
    vm.summaryPage.url = "order/matPreparation/summaryPage";
    vm.summaryPage.condition = []; // 条件

    vm.query = {}; // 查询对象
    vm.query.sort = {}; // 排序字段
    vm.query.sort.name = "orderBy";
    vm.query.sort.value = "a.no DESC";

    vm.query.sentTimeStartQr = {};
    vm.query.sentTimeStartQr.name = "sentTimeStartQr";
    vm.query.sentTimeStartQr.value = "";
    vm.query.sentTimeEndQr = {};
    vm.query.sentTimeEndQr.name = "sentTimeEndQr";
    vm.query.sentTimeEndQr.value = "";


    // 时间范围的选项
    vm.rangeOptions = {
        // format: "YYYY-MM-DD",
        startDate: new Date((new Date).setMonth(((new Date).getMonth() - 6))),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
    };

    // 时间范围的Model
    vm.time = {
        start: {},
        end: {}
    };

    vm.initDate = function(date) {
        if(date == "") {
            vm.rangeOptions = {
                // format: "YYYY-MM-DD",
                startDate: new Date(vm.query.sentTimeStartQr.value),
                minDate: new Date(new Date(vm.query.sentTimeEndQr.value).setFullYear(new Date(vm.query.sentTimeEndQr.value).getFullYear() - 5))
            };
            vm.time = {
                start: new Date(vm.rangeOptions.startDate),
                end: new Date(vm.rangeOptions.minDate)
            }
        }
    };

    vm.noQuery = "";
    vm.nameQuery = "";

    vm.changeCheked = function (row, flag){
        // if (flag == 1 && row.status != 1001){
        //     vm.message = "只能操作未确认的报备!";
        //     $('#static').modal();
        //     return;
        // }
        if (row.checked == 1){
            row.checked = "";
        }else {
            row.checked = 1;
        }
        if (flag == 1){
            vm.getCheckListTwo();
        }if (flag == 2){
            vm.getCheckListTwo();
        }else {
            // vm.getCheckList();
        }
    };

    // 查询数据
/*    vm.doQuery = function() {
        // 设置查询条件
        var condition = [];

        if(vm.noQuery != "")
        {
            condition.push({
                name: "no",
                value: vm.noQuery
            });
        }
        if(vm.nameQuery != "")
        {
            condition.push({
                name: "name",
                value: vm.nameQuery
            });
        }
        if(vm.time.start) {
            condition.push({
                name: vm.query.sentTimeStartQr.name,
                value: vm.time.start.valueOf()
            });
            vm.query.sentTimeStartQr.value = vm.time.start.valueOf();
        }
        if(vm.time.end) {
            condition.push({
                name: vm.query.sentTimeEndQr.name,
                value: vm.time.end.valueOf()
            });
            vm.query.sentTimeEndQr.value = vm.time.end.valueOf();
        }

        vm.page.pageNo = 1;
        vm.page.condition = condition;

        // 查询数据
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    };*/

    // 查询数据
    vm.statusQuery = "2";
    vm.doSummaryQuery = function() {
        // 设置查询条件
        var condition = [];
/*        if(vm.time.start) {
            condition.push({
                name: vm.query.sentTimeStartQr.name,
                value: vm.time.start.valueOf()
            });
            vm.query.sentTimeStartQr.value = vm.time.start.valueOf();
        }
        if(vm.time.end) {
            condition.push({
                name: vm.query.sentTimeEndQr.name,
                value: vm.time.end.valueOf()
            });
            vm.query.sentTimeEndQr.value = vm.time.end.valueOf();
        }*/
        if(vm.matSpecificationQuery && vm.matSpecificationQuery != "")
        {
            condition.push({
                name: "matSpecification",
                value: vm.matSpecificationQuery
            });
        }
        if(vm.statusQuery && vm.statusQuery != "")
        {
            condition.push({
                name: "status",
                value: vm.statusQuery
            });
        }

        vm.departName = null;
        if(vm.organizationName && vm.organizationName.length > 0){
            for(let name of vm.organizationName){
                if(!vm.departName){
                    vm.departName = name;
                }else{
                    vm.departName = vm.departName + ","+ name;
                }
            }
        }
        if(vm.departName)
        {
            condition.push({
                name: "departName",
                value: vm.departName
            });
        }
        vm.summaryPage.pageNo = 1;
        vm.summaryPage.condition = condition;

        // 查询数据
        vm.init(vm.summaryPage.pageNo, vm.summaryPage.pageSize, vm.summaryPage.condition, vm.summaryPage.url);
        loadItemsData();
    };

    vm.historyList = [];
    vm.seeHistoryData = function(row)
    {
        vm.showUseData = row;
        let query = {};
        if(row.query && row.query.recordId)
        {
            query.materialId = row.query.recordId;
        }
        if(vm.departName)
        {
            query.deptName = vm.departName;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("order/matPreparation/getHistoryData",query).then(function(data){
            vm.historyList = data;
            $('#staticHistoryData').modal();
            MainCtrl.unblockUI();
        });
    }

    // 分页按钮单击处理
    vm.doPage = function(page, pageSize, total)
    {
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        vm.init(page, pageSize, vm.page.condition, vm.page.url);
    };

    // 页面显示数量改变
    vm.pageSizeChange = function() {
        vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url);
    };

    // 页面显示数量改变.time
    vm.summaryPageSizeChange = function() {
        vm.init(1, vm.summaryPage.pageSize, vm.summaryPage.condition, vm.summaryPage.url);
    };

    // 分页按钮单击处理
    vm.doSummaryPage = function(page, pageSize, total)
    {
        vm.summaryPage.pageNo = page;
        vm.summaryPage.pageSize = pageSize;
        vm.init(page, pageSize, vm.summaryPage.condition, vm.summaryPage.url);
    };

    vm.init = function(no, size, condition, url) {
        MainCtrl.blockUI({
            animate: true,
        });
        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;
        condition.push({
            name: vm.query.sort.name,
            value: vm.query.sort.value
        });
        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condition);

        // 请求分页数据
        upida.post(url, reqData).then(function(result) {
            var data = {};

            // 如果结果为空
            if(typeof result === 'undefined' || typeof result.list === 'undefined') {

                data.pageNo = 1;
                data.pageSize = 10;
                data.list = [];
                data.startCount = 0;
                data.endCount = 0;
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }
            if(url == vm.page.url)
            {
                vm.page.data = data;
            }
            else if(url == vm.summaryPage.url)
            {
                vm.summaryPage.data = data;
            }
            MainCtrl.unblockUI();
        });
    };

    vm.allPageChecked = false;
    vm.selectAllPage = function()
    {
        if(!vm.page.data.list)
        {
            return;
        }
        angular.forEach(vm.page.data.list,function(p){
            p.checked = !vm.allPageChecked;
        });
        vm.getCheckList();
    };

/*    vm.recordList = [];
    vm.getCheckList = function(){
        vm.recordList = [];
        angular.forEach(vm.page.data.list,function(p){
            if(p.checked){
                vm.recordList.push(p);
            }
        });
    }*/

    vm.batchSaveOpen = function()
    {
        $('#batchSaveStatic').modal();
    }

    vm.importOpen = function()
    {
        vm.getMaterialByCraft();
        $('#importStatic').modal();
    }

    vm.saveMaterialOpen = function()
    {
        vm.material = {};
        vm.material.companyId = vm.factComList[0].recordId;
        vm.loadCompanyByMaterial();
        $('#saveMaterialStatic').modal();
    }


    vm.addRecord = function(){
        if(!vm.recordList || vm.recordList.length == 0)
        {
            vm.recordList = [];
        }
        let record = {};
        vm.recordList.splice(0,0,record);
    };

    vm.deleteRecord = function(index){
        if(!vm.recordList || vm.recordList.length == 0)
        {
            vm.recordList = [];
            return;
        }
        vm.recordList.splice(index,1);
    };

    vm.saveRecord = function(){
        if(!vm.recordList || vm.recordList.length == 0)
        {
            vm.message = "没有保存的内容!";
            $('#static').modal();
            return;
        }
        let checkFlag = false;
        for(let record of vm.recordList)
        {
            if(!record || !record.startDate || !record.endDate)
            {
                vm.message = "开始时间和结束时间不能为空";
                checkFlag = true;
                break;
            }
        }
        if(checkFlag)
        {
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("order/matPreparation/saveVersion",vm.recordList).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#batchSaveStatic').modal('hide');
                vm.recordList = [];
                vm.allPageChecked = false;
                vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            }
            MainCtrl.unblockUI();
        });
    };

    vm.deleteIds = null;
    vm.batchDeleteOpen = function()
    {
        if(!vm.recordList || vm.recordList.length == 0)
        {
            vm.message = "请选择要删除的版本";
            $('#static').modal();
            return;
        }
        vm.deleteIds = null;
        for(let record of vm.recordList)
        {
            if(vm.deleteIds)
            {
                vm.deleteIds = vm.deleteIds +","+ record.recordId;
            }
            else
            {
                vm.deleteIds = record.recordId;
            }
        }
        vm.message = "您确认要删除版本？";
        $('#batcDeleteStatic').modal();
    }

    vm.batchDelte = function()
    {
        if(!vm.deleteIds)
        {
            vm.message = "没有要删除的内容！";
            $('#static').modal();
            return;
        }
        let obj = {};
        obj.recordId = vm.deleteIds;
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("order/matPreparation/delVersion",obj).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#batcDeleteStatic').modal('hide');
                vm.recordList = [];
                vm.deleteIds = null;
                vm.allPageChecked = false;
                vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            }
            MainCtrl.unblockUI();
        });
    }
    vm.editButton = true;
/*    vm.showVersion = function(index)
    {
        vm.version = angular.copy(vm.page.data.list[index]);
        vm.editButton = new Date(vm.version.endDateStr)>new Date();
        getDetailList();
        vm.tabs.editForm.show = true;
        vm.tabs.editForm.active = true;
    }*/

    vm.periodQuery = "所有";
    vm.queryData = {};
    vm.loadDetailData = function()
    {
        let query = {};
        query.sentTimeStartQr = vm.time.start.valueOf();
        query.sentTimeEndQr = vm.time.end.valueOf();
        if(vm.periodQuery != "所有")
        {
            query.period = vm.periodQuery;
        }
        query.specification = vm.specification;
        vm.queryData = query;
        getDetailList();
    }

    function getDetailList()
    {
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("order/matPreparation/getDetailList",vm.queryData).then(function(data){
            vm.matPreparationList = data;
            handleDetailList(vm.matPreparationList);
            MainCtrl.unblockUI();
        });
    }

    function handleDetailList(list)
    {
        if(!list)
        {
            return;
        }
        angular.forEach(list,function (p) {
            if(!p.factComName)
            {
                p.factComName = "无";
            }
            if(!p.cusConcatName)
            {
                p.cusConcatName = "无";
            }
            if(!p.materialName)
            {
                p.materialName = "无";
            }
            if(!p.materialSpecification)
            {
                p.materialSpecification = "无";
            }
            if(!p.deptName)
            {
                p.deptName = "无";
            }
            if(!p.quantity)
            {
                p.quantity = "0";
            }
            if(!p.confirmArea)
            {
                p.confirmArea = "0";
            }
            if(!p.statusStr)
            {
                p.statusStr = "无";
            }
            if(!p.confirmTimeStr)
            {
                p.confirmTimeStr = "无";
            }
            if(!p.leadTime)
            {
                p.leadTime = "0";
            }
            if(!p.arrivalDate)
            {
                p.arrivalDate = "无";
            }
            if(!p.remark)
            {
                p.remark = "无";
            }
            if(!p.cardSet)
            {
                p.cardSet = "无";
            }
            if(!p.nullifyReason)
            {
                p.nullifyReason = "无";
            }
        });
    }

    vm.hideEditForm = function() {
        vm.tabs.editForm.show = false;
        $timeout(function() {
            vm.tabs.viewForm.active = true;
        });
    };

    vm.allPageCheckedTwo = false;
    vm.selectAllPageTwo = function()
    {
        if(!vm.matPreparationList)
        {
            return;
        }
        angular.forEach(vm.matPreparationList,function(p){
            if(p.status == 1001)
            {
                p.checked = !vm.allPageCheckedTwo;
            }
        });
        vm.getCheckListTwo();
    };

    vm.recordTwoList = [];
    vm.getCheckListTwo = function(){
        vm.recordTwoList = [];
        angular.forEach(vm.matPreparationList,function(p){
            if(p.checked){
                vm.recordTwoList.push(p);
            }
        });
    }

    vm.recordThreeList = [];
    vm.getCheckListThree = function(){
        vm.recordThreeList = [];
        angular.forEach(vm.detail.list,function(p){
            if(p.checked){
                vm.recordThreeList.push(p);
            }
        });
    }

    vm.detail = {};
    vm.batchSaveTwoOpen = function(detail)
    {
        if(detail && detail.recordId)
        {
            vm.detail = detail;
        }
        else
        {
            vm.detail = {};
            if(vm.factComList && vm.factComList.length == 1)
            {
                vm.detail.factComId = vm.factComList[0].recordId;
            }
        }
        $('#batchSaveTwoStatic').modal();
    }

    vm.saveDetail = function()
    {
        vm.recordTwoList = [];
        if(vm.detail && vm.detail.recordId)
        {
            vm.recordTwoList.push(vm.detail);
        }
        else
        {
            if(vm.detail.list && vm.detail.list.length > 0)
            {
                for(let detail of vm.detail.list)
                {
                    detail.factComId = vm.detail.factComId;
                    detail.customerId = vm.detail.customerId;
                    vm.recordTwoList.push(detail);
                }
            }
        }
        vm.saveRecordTwo();
    }

    vm.detailRecord = {};
    vm.detailRecordflag = null;
    vm.addRecordTwo = function(){
        if(!vm.detail.list || vm.detail.list.length == 0)
        {
            vm.detail.list = [];
        }
        vm.detailRecordflag = 1;
        vm.detailRecord = {};

        // 预估使用时间
        let myDate = new Date();
        let year = myDate.getFullYear();
        let month = myDate.getMonth();
        month += 2;
        if((month + "").length == 1)
        {
            month = "0" + month;
        }
        let date = year +"-"+ month +"-01";
        vm.detailRecord.estimatedUseTime = date;
        $('#saveDetailRecordStatic').modal();
    };

    vm.updateRecordTwo = function(detail)
    {
        vm.detailRecord = detail;
        vm.detailRecordflag = 2;
        $('#saveDetailRecordStatic').modal();
    }


    vm.deleteRecordTwo = function(){
        if(!vm.detail.list || vm.detail.list.length == 0)
        {
            vm.detail.list = [];
            return;
        }
        for(let i=0;i<vm.detail.list.length;i++)
        {
            if(vm.detail.list[i].checked)
            {
                vm.detail.list.splice(i,1);
            }
        }
    };

    vm.saveDetailRecord = function()
    {
        if(!vm.detailRecord.deptId)
        {
            vm.message = "请选择部门";
            $('#static').modal();
            return;
        }
        if(!vm.detailRecord.materialId)
        {
            vm.message = "请选择物料";
            $('#static').modal();
            return;
        }
        if(!vm.detailRecord.quantity)
        {
            vm.message = "请输入数量";
            $('#static').modal();
            return;
        }
        if(vm.detailRecordflag == 1)
        {
            vm.detail.list.push(vm.detailRecord);
        }
        $('#saveDetailRecordStatic').modal('hide');
    }

    vm.saveRecordTwo = function(){
        if(!vm.recordTwoList || vm.recordTwoList.length == 0)
        {
            vm.message = "没有保存的内容!";
            $('#static').modal();
            return;
        }
        let checkFlag = false;
        for(let record of vm.recordTwoList)
        {
            if(!record.factComId)
            {
                vm.message = "请选择工厂";
                checkFlag = true;
                break;
            }
            if(!record.materialId)
            {
                vm.message = "请选择物料";
                checkFlag = true;
                break;
            }
            if(!record.deptId)
            {
                vm.message = "请选择部门";
                checkFlag = true;
                break;
            }
            if(!record.quantity)
            {
                vm.message = "请输入数量";
                checkFlag = true;
                break;
            }
            if(!record.leadTime && vm.purchLeadTime && vm.purchLeadTime == '2')
            {
                vm.message = "请输入采购提前期";
                checkFlag = true;
                break;
            }
        }
        if(checkFlag)
        {
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("order/matPreparation/savePreparation",vm.recordTwoList).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#batchSaveTwoStatic').modal('hide');
                vm.recordTwoList = [];
                vm.detail = {};
                vm.allPageCheckedTwo = false;
                getDetailList();
            }
            MainCtrl.unblockUI();
        });
    };

    vm.batchDeleteTwoOpen = function(deleteId)
    {
        vm.deleteIds = null;
        if(deleteId)
        {
            vm.deleteIds = deleteId;
        }
        else
        {
            if(!vm.recordTwoList || vm.recordTwoList.length == 0)
            {
                vm.message = "请选择要删除的市场备料";
                $('#static').modal();
                return;
            }
            for(let record of vm.recordTwoList)
            {
                if(vm.deleteIds)
                {
                    vm.deleteIds = vm.deleteIds +","+ record.recordId;
                }
                else
                {
                    vm.deleteIds = record.recordId;
                }
            }
        }
        vm.message = "您确认要删除市场备料？";
        $('#batcDeleteTwoStatic').modal();
    }

    vm.batchDelteTwo = function()
    {
        if(!vm.deleteIds)
        {
            vm.message = "没有要删除的内容！";
            $('#static').modal();
            return;
        }
        let obj = {};
        obj.recordId = vm.deleteIds;
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("order/matPreparation/delPreparation",obj).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#batcDeleteTwoStatic').modal('hide');
                vm.recordTwoList = [];
                vm.deleteIds = null;
                vm.allPageCheckedTwo = false;
                getDetailList();
            }
            MainCtrl.unblockUI();
        });
    }

    vm.selectDept = function(row)
    {
        for(let dept of vm.deptList)
        {
            if(dept.groupOrgId == row.deptId)
            {
                row.deptName = dept.groupOrgName;
                break;
            }
        }
    }

    vm.selectMat = function(row)
    {
        for(let mat of vm.materialList)
        {
            if(mat.recordId == row.materialId)
            {
                row.leadTime = mat.leadTime;
                row.concatValue = mat.concatValue;
                break;
            }
        }
    }
    vm.selectProduceType = function(row){
        for(let product of vm.productTypeList){
            if (product.recordId == row.productTypId){
                row.value = product.value;
                break;
            }
        }
    }

    vm.confirmOpen = function(row)
    {
        if(!row)
        {
            return;
        }
        vm.matPreparation = row;
        vm.message = "您确定要确认市场备料？";
        $('#confirmStatic').modal();
    }

    vm.confirmMatPreparation = function()
    {
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("order/matPreparation/confirmMatPreparation",vm.matPreparation).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#confirmStatic').modal('hide');
                getDetailList();
            }
            MainCtrl.unblockUI();
        });
    }

    // 加载权限
    function loadRight() {
        MainCtrl.blockUI({
            animate: true,
        });
        vm.queryAll = CommonUtil.dataRangeIsAll("10208", BaseUtil.getMenuList());
        upida.get("common/rightall?prefix=order:preparation").then(function (data) {

            vm.right.manage = data.manage;
            vm.right.edit = data.edit;
            vm.right.view = data.view;

            loadItemsData();
            vm.loadDetailData();

            // 初始化第一页，条件为空
/*            vm.page.pageNo = 1;
            if (vm.time.start) {
                vm.page.condition.push({
                    name: vm.query.sentTimeStartQr.name,
                    value: vm.time.start.valueOf()
                });
                vm.query.sentTimeStartQr.value = vm.time.start.valueOf();
            }
            if (vm.time.end) {
                vm.page.condition.push({
                    name: vm.query.sentTimeEndQr.name,
                    value: vm.time.end.valueOf()
                });
                vm.query.sentTimeEndQr.value = vm.time.end.valueOf();
            }
            vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);*/
        });
    }


    function loadItemsData() {
        MainCtrl.blockUI({
            animate: true,
        });

        upida.get("order/matPreparation/loadData?queryAll=" + vm.queryAll).then(function (data) {
            vm.factComList = data.factComList;
            vm.materialList = data.materialList;
            vm.deptList = data.deptList;
            vm.customerList = data.customerList;
            vm.purchLeadTime = data.purchLeadTime;
            vm.judgeStock = data.judgeStock;
            vm.periodList = data.periodList || [];
            vm.periodList.splice(0,0,"所有");
            if(null != vm.organizationName &&vm.organizationName.length > 0){

                var targetString = vm.departName;
                var targetDepartments = targetString.split(",").map(function(department) {
                    return department.trim();
                });
                var filteredDepartments = vm.deptList.filter(function(department) {
                    return targetDepartments.includes(department.groupOrgName);
                });
                vm.deptList = filteredDepartments;

            }

            MainCtrl.unblockUI();
        });
    }

    vm.addMaterialItems = function(no)
    {
        var material = {};
        material.no = no;
        upida.post("order/matPreparation/addMaterialItems",material).then(function(data){
            if(data && data.length > 0)
            {
                vm.materialList = data;
            }
        });
    }

    vm.getMaterialByCraftList = [];
    vm.getMaterialByCraft = function()
    {
        // 查询可导入备料信息
        upida.post("order/matPreparation/getMaterialByCraftList",vm.version.recordId).then(function(data){
            vm.getMaterialByCraftList = data;
        });
    }

    vm.allCraftChecked = false;
    vm.selectCraftAll = function ()
    {
        if(!vm.getMaterialByCraftList)
        {
            return;
        }
        angular.forEach(vm.getMaterialByCraftList,function(p){
            p.checked = !vm.allCraftChecked;
        });
        vm.getCheckListCraft();
    }

    vm.recordCraftList = [];
    vm.getCheckListCraft = function () {
        vm.recordCraftList = [];
        angular.forEach(vm.getMaterialByCraftList, function (p) {
            if (p.checked) {
                vm.recordCraftList.push(p);
            }
        });
        if (vm.getMaterialByCraftList.length > 0) {
            vm.getMaterialByCraftList.sort(compare);
        }
    }
    var compare = function (obj1, obj2) {
        var val1 = obj1.checked;
        var val2 = obj2.checked;
        if (val1 == val2) {
            return 0;
        } else if (val1 == false && val2 == true) {
            return 1;
        } else if (val1 == true && val2 == false) {
            return -1;
        } else {
            return 0;
        }
    }

    vm.loadCompanyByMaterial = function () {
        upida.post("order/matPreparation/materialTypesMaterialList", vm.material.companyId).then(function (data) {
            vm.materialTypesMaterialList = data.materialTypesMaterialList;
            vm.material.materialType=vm.materialTypesMaterialList[1];
            vm.loadMaterialNoByType();
        });
    }

    vm.saveRecordCraft = function(){
        if(!vm.recordCraftList || vm.recordCraftList.length == 0)
        {
            vm.message = "没有保存的内容!";
            $('#static').modal();
            return;
        }
        let checkFlag = false;
        for(let record of vm.recordCraftList)
        {
            if(!record.importQuantity)
            {
                vm.message = "勾选中存在未输入导入物料的数量";
                checkFlag = true;
                break;
            }else if(record.importQuantity>record.remainQuantity){
                vm.message = "勾选中存在剩余物料不足";
                checkFlag = true;
                break;
            }else if(record.importQuantity<0){
                vm.message = "导入备料不能为负数";
                checkFlag = true;
                break;
            }

        }
        if(checkFlag)
        {
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        // 进行保存
        upida.post("order/matPreparation/matPreparationSave?recordId="+vm.version.recordId,vm.recordCraftList).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#importStatic').modal('hide');
                vm.recordCraftList = [];
                vm.allCraftChecked = false;
                getDetailList();
            }
            MainCtrl.unblockUI();
        });
    }

    vm.material = {};
    vm.loadMaterialNoByType = function() {
        vm.material.materialSpecList = [];
        upida.post("stock/material/materialSpecListByType",vm.material.materialType).then(function (data) {
            vm.material.materialSpecList = angular.copy(data.materialSpecList);
        });
    }
    vm.setFormScope= function(scope){
        vm.formScope = scope;
    };
    vm.saveMaterial = function(){
        upida.post("order/matPreparation/materialSave", vm.material).then(function (data) {
            vm.message = data.message;
            $('#static').modal();
            if (data.result == "success") {
                $('#saveMaterialStatic').modal('hide');
                loadItemsData();
            }
        })
    }

    vm.reConcactName = function() {
        vm.material.name = "";
        vm.material.specification = "";
        if(vm.material.materialSpecList && vm.material.materialSpecList.length > 0){

            // 制造商
            var madeSupplierName = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId == "70" && fujian.dictItem.values != null){
                    for(let j=0;j<fujian.dictItem.values.length;j++){
                        if(fujian.value == fujian.dictItem.values[j].recordId){
                            madeSupplierName = fujian.dictItem.values[j].value;
                            break;
                        }
                    }
                    break;
                }
            }

            // 覆铜板材
            var board = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId == "2" && fujian.dictItem.values != null){
                    for(let j=0;j<fujian.dictItem.values.length;j++){
                        if(fujian.value == fujian.dictItem.values[j].recordId){
                            board = fujian.dictItem.values[j].value;
                            break;
                        }
                    }
                    break;
                }
            }

            // 组合物料名字,制造商+覆铜板材+追加项
            if(madeSupplierName){
                vm.material.name = madeSupplierName;
                if(board){
                    vm.material.name = vm.material.name + " " + board;
                }
                if(vm.material.additionalItems)
                {
                    vm.material.name = vm.material.name + " " + vm.material.additionalItems;
                }
            }else{
                if(board){
                    vm.material.name = board;
                }
                if(vm.material.additionalItems)
                {
                    vm.material.name = vm.material.name + " " + vm.material.additionalItems;
                }
            }

            // 板材厚度
            var thickness = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId == "3" && fujian.dictItem.values != null){
                    for(let j=0;j<fujian.dictItem.values.length;j++){
                        if(fujian.value == fujian.dictItem.values[j].recordId){
                            thickness = fujian.dictItem.values[j].value;
                            break;
                        }
                    }
                    break;
                }
            }

            // 导热
            var daore = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId == "58" && fujian.dictItem.values != null){
                    for(let j=0;j<fujian.dictItem.values.length;j++){
                        if(fujian.value == fujian.dictItem.values[j].recordId){
                            daore = fujian.dictItem.values[j].value;
                            break;
                        }
                    }
                    break;
                }
            }

            // 耐压
            var naiya = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId == "59" && fujian.dictItem.values != null){
                    for(let j=0;j<fujian.dictItem.values.length;j++){
                        if(fujian.value == fujian.dictItem.values[j].recordId){
                            naiya = fujian.dictItem.values[j].value;
                            break;
                        }
                    }
                    break;
                }
            }

            // 覆铜要求
            var copperClad = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId == "31" && fujian.dictItem.values != null){
                    for(let j=0;j<fujian.dictItem.values.length;j++){
                        if(fujian.value == fujian.dictItem.values[j].recordId){
                            copperClad = fujian.dictItem.values[j].value;
                            break;
                        }
                    }
                    break;
                }
            }

            var length = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.name == "长"){
                    length = fujian.value;
                    break;
                }
            }
            var width = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.name == "宽"){
                    width = fujian.value;
                    break;
                }
            }

            // 贴膜
            var film = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId == "71" && fujian.dictItem.values != null){
                    for(let j=0;j<fujian.dictItem.values.length;j++){
                        if(fujian.value == fujian.dictItem.values[j].recordId){
                            film = fujian.dictItem.values[j].value;
                            break;
                        }
                    }
                    break;
                }
            }

            // PCB类型
            var pcbType = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId == "1" && fujian.dictItem.values != null){
                    for(let j=0;j<fujian.dictItem.values.length;j++){
                        if(fujian.value == fujian.dictItem.values[j].recordId){
                            pcbType = fujian.dictItem.values[j].value;
                            break;
                        }
                    }
                    break;
                }
            }
            var simple = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.name == "样品"){
                    simple = fujian.value;
                    break;
                }
            }
            var gum = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.name == "胶"){
                    gum = fujian.value;
                    break;
                }
            }
            var insulat = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.name == "绝缘层"){
                    insulat = fujian.value;
                    break;
                }
            }
            var cuprum = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.name == "无铜"){
                    cuprum = fujian.value;
                    break;
                }
            }
            var special = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                var fujian = vm.material.materialSpecList[i];
                if(fujian.name == "特殊要求"){
                    special = fujian.value;
                    break;
                }
            }
            // 组合规格型号，板材厚度+覆铜要求+长*宽+胶+膜+样品+无铜
            if(thickness){
                var thicknessStr = thickness.replace('mm', '厚');
                vm.material.specification = thicknessStr;
            }
            if(copperClad){
                copperClad = copperClad.toLowerCase();
                var copperCladStr = "";
                if(copperClad == "2/0"){
                    copperCladStr = "70um";
                }else if(copperClad == "1/0"){
                    copperCladStr = "35um";
                }else if(copperClad == "h/0"){
                    copperCladStr = "18um";
                }else if(copperClad == "18um"){
                    copperCladStr = "18um";
                }else if(copperClad == "3/0"){
                    copperCladStr = "105um";
                }else if(copperClad == "28um"){
                    copperCladStr = "28um";
                }else if(copperClad == "25um"){
                    copperCladStr = "25um";
                }else if(copperClad == "15um"){
                    copperCladStr = "15um";
                }else if(copperClad == "12um"){
                    copperCladStr = "12um";
                }else if(copperClad == "30um"){
                    copperCladStr = "30um";
                }else if(copperClad == "35um"){
                    copperCladStr = "35um";
                }
                vm.material.specification = vm.material.specification + copperCladStr;
            }
            if(vm.material.specification){
                vm.material.specification = vm.material.specification + " ";
            }
            if(length){
                vm.material.specification = vm.material.specification + length + "*";
            }
            if(width){
                vm.material.specification = vm.material.specification + width + " ";
            }
            if(daore){
                vm.material.specification = vm.material.specification + daore + " ";
            }
            if(naiya){
                vm.material.specification = vm.material.specification + naiya + " ";
            }
            if(film){
                vm.material.specification = vm.material.specification + film;
            }
            if(pcbType){
                vm.material.specification = vm.material.specification + pcbType;
            }
            if(simple){
                vm.material.specification = vm.material.specification + simple;
            }
            if(gum){
                vm.material.specification = vm.material.specification + gum;
            }
            if(insulat){
                vm.material.specification = vm.material.specification + insulat;
            }
            if(cuprum){
                vm.material.specification = vm.material.specification + cuprum;
            }
            if(special){
                vm.material.specification = vm.material.specification + special;
            }
        }
    }

    vm.nullifyReason = "";
    vm.nullifyList = [];
    vm.nullifyOpen = function (detail) {
        vm.nullifyList = [];
        if(detail)
        {
            if(detail.status != 1003)
            {
                vm.message = "已确认的备料单才能作废!";
                $('#static').modal();
                return;
            }
            vm.nullifyList.push(detail);
        }
        else
        {
            if (!vm.recordTwoList || vm.recordTwoList.length == 0) {
                vm.message = "请选择要删除的市场备料";
                $('#static').modal();
                return;
            }
            for(let record  of vm.recordTwoList)
            {
                if(record.status == 1003)
                {
                    vm.nullifyList.push(record);
                }
            }
            if (!vm.nullifyList || vm.nullifyList.length == 0) {
                vm.message = "已确认的备料单才能作废";
                $('#static').modal();
                return;
            }
        }
        vm.nullifyReason = "";
        $('#nullifyStatic').modal();
    }


    vm.nullify = function () {
        if (!vm.nullifyList || vm.nullifyList.length == 0) {
            vm.message = "请选择要删除的市场备料";
            $('#static').modal();
            return;
        }
        if(!vm.nullifyReason){
            vm.message = "作废理由不能为空";
            $('#static').modal();
            return;
        }
        for (let i = 0; i < vm.nullifyList.length; i++) {
            vm.nullifyList[i].nullifyReason = vm.nullifyReason;
        }
        upida.post("order/matPreparation/nullify", vm.nullifyList).then(function (data) {
            vm.message = data.message;
            $('#static').modal();
            if (data.result == "success") {
                $('#nullifyStatic').modal('hide');
                vm.recordTwoList = [];
                vm.allPageCheckedTwo = false;
                getDetailList();
            }
            MainCtrl.unblockUI();
        });

    }

    vm.showUseData = {};
    vm.showUseType = null;
    vm.showUseDataOpen = function(row,type)
    {
        vm.showUseData = row;
        vm.showUseType = type;
        $('#staticUseData').modal();
    },
        //加载产品类型
        vm.productTypeList = [];
    vm.getProductTypeList = function(){
        upida.post("order/matPreparation/getProductTypeList").then(function (data) {
            vm.productTypeList = data;
        });
    }

    vm.showCapacity = function ()
    {
        vm.capacityAuction = ""; // 剩余总产能面积
        vm.exposureCapacity = ""; // 剩余曝光产能
        vm.screenCapacity = ""; // 剩余丝印产能
        /*vm.massProductionNum = ""; // 剩余量产款数
        vm.sampleNum = ""; // 剩余样品款数
        vm.newOrderNum = ""; // 剩余新单款数*/
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("order/matPreparation/getCapacity").then(function (data) {
            vm.departList = data.departList;
            vm.capacityRecordList = data.capacityRecordList;
            vm.capacityAuction = data.capacityAuction;
            vm.exposureCapacity = data.exposureCapacity;
            vm.screenCapacity = data.screenCapacity;
            /*vm.massProductionNum = data.massProductionNum;
            vm.sampleNum = data.sampleNum;
            vm.newOrderNum = data.newOrderNum;*/
            MainCtrl.unblockUI();
        });
    }

    vm.capacity = {};
    vm.batchSaveThreeOpen = function (capactiy)
    {
        if (capactiy && capactiy.recordId)
        {
            vm.capacity = capactiy;
            vm.capacity.factComId = capactiy.companyId;
        }
        else
        {
            vm.capacity = {};
            if (vm.factComList && vm.factComList.length == 1)
            {
                vm.capacity.factComId = vm.factComList[0].recordId;
            }
        }
        $('#batchSaveThreeStatic').modal();
    };

    vm.updateCapacity = function(row){
        if(row.capacity>0 && vm.capacityAuction>0){
            row.capacityPer = Math.round((row.capacity/vm.capacityAuction)*100*100)/100;
        }
        vm.calAdjust(vm.capacityRecordList);
    };

    vm.calAdjust = function(list){
        vm.amountAdjust = vm.totalAmountAuction;
        vm.sampleAmountAdjust = vm.sampleAmountAuction;
        vm.orderAmountAdjust = vm.orderAmountAuction;
        vm.capacityAdjust = vm.capacityAuction;
        vm.capacityPerAdjust = 100;
        vm.amountPerAdjust = 100;
        vm.sampleAmountPerAdjust = 100;
        vm.orderAmountPerAdjust = 100;
        let sampleAmountPerCount = 0;
        let sampleAmountCount = 0;
        let amountPerCount = 0;
        let amountCount = 0;
        let orderAmountPerCount = 0;
        let orderAmountCount = 0;
        let capacityCount = 0;
        let capacityPerCount = 0;
        for (let record of list) {
            if(record.amountPer==null || record.amountPer=='') record.amountPer=0
            if(record.amount==null || record.amount=='') record.amount=0
            if(record.sampleAmountPer==null || record.sampleAmountPer=='') record.sampleAmountPer=0
            if(record.sampleAmount==null || record.sampleAmount=='') record.sampleAmount=0
            if(record.orderAmountPer==null || record.orderAmountPer=='') record.orderAmountPer=0
            if(record.orderAmount==null || record.orderAmount=='') record.orderAmount=0
            if(record.capacityPer==null || record.capacityPer=='') record.capacityPer=0
            if(record.capacity==null || record.capacity=='') record.capacity=0
            amountPerCount+=record.amountPer;
            amountCount+=record.amount;
            sampleAmountPerCount+=record.sampleAmountPer;
            sampleAmountCount+=record.sampleAmount;
            orderAmountPerCount+=record.orderAmountPer;
            orderAmountCount+=record.orderAmount;
            capacityPerCount+=record.capacityPer;
            capacityCount+=record.capacity;
        }
        vm.amountPerAdjust = Math.round((100-amountPerCount)*100)/100;
        vm.sampleAmountAdjust = Math.round(vm.sampleAmountAuction-sampleAmountCount);
        vm.sampleAmountPerAdjust = Math.round((100-sampleAmountPerCount)*100)/100;
        vm.amountAdjust = Math.round(vm.totalAmountAuction-amountCount);
        vm.orderAmountPerAdjust = Math.round((100-orderAmountPerCount)*100)/100;
        vm.orderAmountAdjust = Math.round(vm.orderAmountAuction-orderAmountCount);
        vm.capacityPerAdjust = Math.round((100-capacityPerCount)*100)/100;
        vm.capacityAdjust = Math.round(vm.capacityAuction-capacityCount);
    };

    vm.saveCapaDetail = function (capactiy)
    {
        if (!capactiy)
        {
            vm.message = "请输入要报备的产能信息!";
            $('#static').modal();
            return;
        }
        if (!capactiy.factComId)
        {
            vm.message = "请选择报备工厂!";
            $('#static').modal();
            return;
        }
        if (!capactiy.departId)
        {
            vm.message = "请选择报备部门!";
            $('#static').modal();
            return;
        }
        if (!capactiy.capacity)
        {
            vm.message = "请输入报备面积!";
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("order/matPreparation/addCapacity",capactiy).then(function (data) {
            vm.capacity = {};
            vm.message = data;
            $('#static').modal();
            vm.showCapacity();
            MainCtrl.unblockUI();
        });
    }

    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        vm.getProductTypeList();
        loadRight();
    });
}]);