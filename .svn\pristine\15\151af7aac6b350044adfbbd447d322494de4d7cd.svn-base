/**
 * 
 */
package com.kyb.pcberp.modules.production.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.quality.entity.Inspect;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 生产投料批次Entity
 * 
 * <AUTHOR>
 * @version 2015-09-22
 */
public class ProduceBatch extends DataEntity<ProduceBatch>
{
    
    private static final long serialVersionUID = 1L;
    
    private Long processCardAId; // processcardaid
    
    private String no; // 生产批次号
    
    private String qtyPnlA; // A板Pnl数
    
    private String qtySetA; // A板Pnl数
    
    private Integer qtyPcsA; // A板Pnl数
    
    private String qtyPnlB; // B板Pnl数
    
    private String qtySetB; // B板Pnl数
    
    private Integer qtyPcsB; // B板Pnl数
    
    private String qtyPnlT; // 大板Pnl数
    
    private String qtySetT; // 大板Pnl数
    
    private Integer qtyPcsT; // 大板Pnl数
    
    private String discardQty; // 报废数量
    
    private Notification notification; // notificationid
    
    private Feeding feed;
    
    private List<ProduceBatchDetail> produceBatchDetail; // 批次明细list
    
    private Integer cardCount; // 流程卡张数
    
    private Replenish replenish;
    
    private String showNo;
    
    private Integer batchType;  //数据类型  是投料的批次 还是补料的批次
    
    private String status;  //数据状态 是否完成
    
    private String ignoreCountOrder; // 忽略卡过数顺序，若为0不忽略，若为1忽略
    
    private ProduceBatchDetail produceBatchDetailForQuery;//用来传递查询批次明细中一个参数

    private String contractNo;

    private String customerPo;

    private String craftNo;

    private Integer orderQty;

    private Integer pcsQuantity;

    private List<Inspect> inspectList;

    private Integer reworkQty; // 返工数量

    private Integer badQty; // 不良数量

    private Integer replenishQty; // 补料数量

    private boolean statusRed; //是否标红

    private String setupDetailId; //周期设置明细Id

    private String produceBatchDetailId; // 批次明细id

    private String batchDetailSetupId; // 批次明细周期设置明细id

    private String occupyId; // 工程物料占用表id

    private String materialId; // 物料id

    private BigDecimal quantity; // 数量

    private BigDecimal rawmaterialPrice; // 采购单价

    private List<BottleneckProcessUse> useList;

    private String processName;
    
    public Integer getBatchType()
    {
        return batchType;
    }

    public void setBatchType(Integer batchType)
    {
        this.batchType = batchType;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public ProduceBatch()
    {
        super();
    }
    
    public Integer getCardCount()
    {
        return cardCount;
    }
    
    public void setCardCount(Integer cardCount)
    {
        this.cardCount = cardCount;
    }
    
    public List<ProduceBatchDetail> getProduceBatchDetail()
    {
        return produceBatchDetail;
    }
    
    public void setProduceBatchDetail(List<ProduceBatchDetail> produceBatchDetail)
    {
        this.produceBatchDetail = produceBatchDetail;
    }
    
    public ProduceBatch(String id)
    {
        super(id);
    }
    
    public Feeding getFeed()
    {
        return feed;
    }
    
    public void setFeed(Feeding feed)
    {
        this.feed = feed;
    }
    
    public Long getProcessCardAId()
    {
        return processCardAId;
    }
    
    public void setProcessCardAId(Long processCardAId)
    {
        this.processCardAId = processCardAId;
    }
    
    @ExcelField(title = "生产批号", align = 2, sort = 80, parentExport = true, parentSort = {50})
    public String getNo()
    {
        return no;
    }
    
    public void setNo(String no)
    {
        this.no = no;
    }
    
    public String getQtyPnlA()
    {
        return qtyPnlA;
    }
    
    public void setQtyPnlA(String qtyPnlA)
    {
        this.qtyPnlA = qtyPnlA;
    }
    
    public String getQtySetA()
    {
        return qtySetA;
    }
    
    public void setQtySetA(String qtySetA)
    {
        this.qtySetA = qtySetA;
    }
    
    public Integer getQtyPcsA()
    {
        return qtyPcsA;
    }
    
    public void setQtyPcsA(Integer qtyPcsA)
    {
        this.qtyPcsA = qtyPcsA;
    }
    
    public String getQtyPnlB()
    {
        return qtyPnlB;
    }
    
    public void setQtyPnlB(String qtyPnlB)
    {
        this.qtyPnlB = qtyPnlB;
    }
    
    public String getQtySetB()
    {
        return qtySetB;
    }
    
    public void setQtySetB(String qtySetB)
    {
        this.qtySetB = qtySetB;
    }
    
    public Integer getQtyPcsB()
    {
        return qtyPcsB;
    }
    
    public void setQtyPcsB(Integer qtyPcsB)
    {
        this.qtyPcsB = qtyPcsB;
    }
    
    public String getQtyPnlT()
    {
        return qtyPnlT;
    }
    
    public void setQtyPnlT(String qtyPnlT)
    {
        this.qtyPnlT = qtyPnlT;
    }
    
    public String getQtySetT()
    {
        return qtySetT;
    }
    
    public void setQtySetT(String qtySetT)
    {
        this.qtySetT = qtySetT;
    }
    
    public Integer getQtyPcsT()
    {
        return qtyPcsT;
    }
    
    public void setQtyPcsT(Integer qtyPcsT)
    {
        this.qtyPcsT = qtyPcsT;
    }
    
    public String getDiscardQty()
    {
        return discardQty;
    }

    public void setDiscardQty(String discardQty)
    {
        this.discardQty = discardQty;
    }

    @ExcelField(title = "生产编号", align = 2, parentExport = true, parentSort = {
        50}, fieldType = (Notification.class), value = ("notification.craftNo"))
    public Notification getNotification()
    {
        return notification;
    }
    
    public void setNotification(Notification notification)
    {
        this.notification = notification;
    }
    
    public Replenish getReplenish()
    {
        return replenish;
    }
    
    public void setReplenish(Replenish replenish)
    {
        this.replenish = replenish;
    }
    
    public String getShowNo()
    {
        if (this.getReplenish() != null && StringUtils.isNotBlank(this.getReplenish().getNo()))
        {
            return this.getNo() + "-A" + this.getRecordId() + "(" + this.getReplenish().getNo() + ")";
        }
        
        if (this.getFeed() != null && StringUtils.isNotBlank(this.getFeed().getNo()))
        {
            return this.getNo() + "-A" + this.getRecordId() + "(" + this.getFeed().getNo() + ")";
        }
        
        if (StringUtils.isNotBlank(this.getNo()))
        {
            return this.getNo() + "-A" + this.getRecordId();
        }
        
        return showNo;
    }
    
    public void setShowNo(String showNo)
    {
        this.showNo = showNo;
    }

    public String getIgnoreCountOrder()
    {
        return ignoreCountOrder;
    }

    public void setIgnoreCountOrder(String ignoreCountOrder)
    {
        this.ignoreCountOrder = ignoreCountOrder;
    }

    public ProduceBatchDetail getProduceBatchDetailForQuery()
    {
        return produceBatchDetailForQuery;
    }

    public void setProduceBatchDetailForQuery(ProduceBatchDetail produceBatchDetailForQuery)
    {
        this.produceBatchDetailForQuery = produceBatchDetailForQuery;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getCustomerPo() {
        return customerPo;
    }

    public void setCustomerPo(String customerPo) {
        this.customerPo = customerPo;
    }

    public String getCraftNo() {
        return craftNo;
    }

    public void setCraftNo(String craftNo) {
        this.craftNo = craftNo;
    }

    public Integer getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(Integer orderQty) {
        this.orderQty = orderQty;
    }

    public Integer getPcsQuantity() {
        return pcsQuantity;
    }

    public void setPcsQuantity(Integer pcsQuantity) {
        this.pcsQuantity = pcsQuantity;
    }

    public List<Inspect> getInspectList() {
        return inspectList;
    }

    public void setInspectList(List<Inspect> inspectList) {
        this.inspectList = inspectList;
    }

    public Integer getReworkQty() {
        return reworkQty;
    }

    public void setReworkQty(Integer reworkQty) {
        this.reworkQty = reworkQty;
    }

    public Integer getBadQty() {
        return badQty;
    }

    public void setBadQty(Integer badQty) {
        this.badQty = badQty;
    }

    public Integer getReplenishQty() {
        return replenishQty;
    }

    public void setReplenishQty(Integer replenishQty) {
        this.replenishQty = replenishQty;
    }

    public boolean isStatusRed() {
        return statusRed;
    }

    public void setStatusRed(boolean statusRed) {
        this.statusRed = statusRed;
    }

    public List<BottleneckProcessUse> getUseList() {
        return useList;
    }

    public void setUseList(List<BottleneckProcessUse> useList) {
        this.useList = useList;
    }

    public String getSetupDetailId()
    {
        return setupDetailId;
    }

    public void setSetupDetailId(String setupDetailId)
    {
        this.setupDetailId = setupDetailId;
    }

    public String getProduceBatchDetailId()
    {
        return produceBatchDetailId;
    }

    public void setProduceBatchDetailId(String produceBatchDetailId)
    {
        this.produceBatchDetailId = produceBatchDetailId;
    }

    public String getBatchDetailSetupId()
    {
        return batchDetailSetupId;
    }

    public void setBatchDetailSetupId(String batchDetailSetupId)
    {
        this.batchDetailSetupId = batchDetailSetupId;
    }

    public String getOccupyId()
    {
        return occupyId;
    }

    public void setOccupyId(String occupyId)
    {
        this.occupyId = occupyId;
    }

    public String getMaterialId()
    {
        return materialId;
    }

    public void setMaterialId(String materialId)
    {
        this.materialId = materialId;
    }

    public BigDecimal getQuantity()
    {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity)
    {
        this.quantity = quantity;
    }

    public BigDecimal getRawmaterialPrice()
    {
        return rawmaterialPrice;
    }

    public void setRawmaterialPrice(BigDecimal rawmaterialPrice)
    {
        this.rawmaterialPrice = rawmaterialPrice;
    }

    public String getProcessName()
    {
        return processName;
    }

    public void setProcessName(String processName)
    {
        this.processName = processName;
    }
}