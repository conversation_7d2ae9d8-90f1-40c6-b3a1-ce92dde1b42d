/**
 * 
 */
package com.kyb.pcberp.modules.sys.service;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Maps;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.modules.sys.dao.CodegenDao;
import com.kyb.pcberp.modules.sys.entity.Codegen;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

/**
 * 编码管理Service
 * 
 * <AUTHOR>
 * @version 2015-08-11
 */
@Service
@Transactional(readOnly = true)
public class CodegenService extends CrudService<CodegenDao, Codegen>
{
    
    public Codegen get(String id)
    {
        return super.get(id);
    }
    
    public List<Codegen> findList(Codegen mdCodegen)
    {
        return super.findList(mdCodegen);
    }
    
    public Page<Codegen> findPage(Page<Codegen> page, Codegen mdCodegen)
    {
        return super.findPage(page, mdCodegen);
    }
    
    @Transactional(readOnly = false)
    public Integer addNextNo(Codegen codegen)
    {
        return dao.addNextNo(codegen);
    }
    
    @Transactional(readOnly = false)
    public void save(Codegen mdCodegen)
    {
        super.save(mdCodegen);
    }
    
    @Transactional(readOnly = false)
    public void delete(Codegen mdCodegen)
    {
        super.delete(mdCodegen);
    }
    
    @Transactional(readOnly = false)
    public Map<String, Codegen> getData()
    {
        // 返回结果
        Map<String, Codegen> result = Maps.newHashMap();
        
        Codegen codegen = new Codegen();
        codegen.setCompany(UserUtils.getUser().getCompany());
        
        List<Codegen> gens = dao.findAllList(codegen);
        
        if (Collections3.isNotEmpty(gens))
        {
            for (Codegen code : gens)
            {
                result.put(code.getCodeType().toString(), code);
            }
        }
        return result;
    }
    
    /**
     * 查询编码类型
     * 
     * @param codegen
     * @return
     */
    public Codegen getByCodeType(Codegen codegen)
    {
        return dao.getByCodeType(codegen);
    }
    
}