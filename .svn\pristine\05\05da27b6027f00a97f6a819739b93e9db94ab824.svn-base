const bus_person = {
    template: '#bus_person',
    created:function(){
        this.$parent.changeClass(5);
        this.getUser();
        this.getCompanyList();
        this.getPersonData();
        this.getWxMsg();
    },
    mounted () {
        const goToFlag = window.localStorage.getItem("goToFlag");
        if (goToFlag == 3){
            this.inCompanyModal();
        }
    },
    data(){
        return {
            user: {},
            companyList: [],
            recordId: "",
            companyCode: "",
            icloudWaitNum: 0,
            myWaitNum: 0,
            coopNum: 0,
            coopChanceNum : 0,
            shareMsg: {},
            qcCode: "",
            appCompanyList: [],
            companyMessage:{},
        }
    },
    methods:{
        getWxMsg:function()
        {
            var config = {};
            config.url = location.href.split('#')[0];
            var _this = this
            $('#loadingModal').modal();
            $.ajax({
                type:"post",
                url:ctx + "/f/wechat/produce/getWxMsg",
                data:JSON.stringify(config),
                contentType:"application/json",
                success:function(data)
                {
                    $('#loadingModal').modal('hide');
                    _this.wxConfig(data);
                    _this.wxError();
                    _this.wxReady();
                }
            })
        },
        wxConfig: function (data) {
            wx.config({
                debug: false, // 开启调试模式,调用的所有api的返回值会在客户端console.log出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                appId: data.appId, // 必填，公众号的唯一标识
                timestamp: data.timestamp, // 必填，生成签名的时间戳
                nonceStr: data.nonceStr, // 必填，生成签名的随机串
                signature: data.signature, // 必填，签名
                jsApiList: [
                    'checkJsApi', 'startRecord', 'stopRecord', 'translateVoice', 'scanQRCode', 'openCard'
                    // 微信扫一扫接口
                ] // 必填，需要使用的JS接口列表
            })
        },
        wxError: function () {
            wx.error(function (res) {
                console.log('出错了：' + res.errorMsg);
                // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
            });
        },
        wxReady: function () {
            wx.ready(function () {
                wx.checkJsApi({
                    jsApiList: ['scanQRCode'],
                    success: function (res) {
                        console.log(res);
                    }
                });
                // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
            });
        },
        wxRcanQRcode: function () {
            var _this = this;
            wx.scanQRCode({
                needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                scanType: ['qrCode'], // 可以指定扫二维码qrCode还是一维码barCode，默认二者都有
                success: function (res) {
                    const result = res.resultStr // 当needResult 为 1 时，扫码返回的结果
                    // 进入明细
                    if(result){
                        _this.companyCode = result;
                        _this.inCompanyByCode();
                    }
                },
                error: function () {
                    console.log('系统错误');
                }
            })
        },
        getPersonData: function () {
            this.icloudWaitNum = 0;
            this.myWaitNum = 0;
            this.coopNum = 0;
            this.coopChanceNum = 0;
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url:ctx + "/f/wechat/business/getDataList",
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    if (data){
                        _this.myWaitNum = data.myWaitNum;
                        _this.icloudWaitNum = data.icloudWaitNum;
                    }
                }
            })
        },
        getUser: function (){
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url:ctx + "/f/wechat/kybsoft/getUser",
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    _this.user = data;
                }
            })
        },
        logOut: function (){
            var router = this.$router;
            $('#loadingModal').modal();
            $.ajax({
                type:"post",
                url:ctx + "/f/wechat/kybsoft/logOut",
                contentType:"application/json",
                success:function()
                {
                    $('#loadingModal').modal('hide');
                    router.push("/");
                }
            })
        },
        getCompanyList: function (){
            this.companyList = [];
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url:ctx + "/f/wechat/business/getCompanyList",
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    _this.companyList = data;
                }
            })
        },
        deleteCompanyModal: function (recordId) {
            this.recordId = recordId;
            $('#deleteModal').modal();
        },
        deleteCompany: function () {
            if (!this.recordId){
                alert("请刷新重试！");
                return;
            }
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/deleteCompany",
                data: this.recordId,
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    $('#deleteModal').modal('hide');
                    if (data == "success"){
                        this.recordId = "";
                        alert("删除成功！");
                        _this.getCompanyList();
                    }else {
                        alert("请刷新重试！");
                    }
                }
            })
        },
        useCom: function (id) {
            if (!id){
                alert("请刷新重试！");
                return;
            }
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/useCom",
                data: id,
                contentType: "application/json",
                success: function(data)
                {
                    $('#deleteModal').modal('hide');
                    if (data == "success"){
                        alert("启用成功成功！");
                        _this.getCompanyList();
                    }else {
                        alert("请刷新重试！");
                    }
                }
            })
        },
        addDeail: function (){
            window.localStorage.removeItem("comId");
            this.$router.push("/companyDeail");
        },
        editDeail: function (id){
            window.localStorage.setItem('comId', id)
            this.$router.push("/companyDeail");
        },
        manage: function (id){
            window.localStorage.setItem('comId', id);
            window.localStorage.setItem('number', 1);
            window.localStorage.setItem('userId', this.user.recordId);
            this.$router.push("/employeeDeail");
        },
        inCompanyModal: function () {
            window.localStorage.removeItem("goToFlag");
            $('#inCompanyModal').modal();
        },
        inCompanyByCode: function () {
            if (!this.companyCode){
                alert("请刷新重试！");
                return;
            }
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/addCompanyUserByCode",
                data: this.companyCode,
                contentType: "application/json",
                success: function(data)
                {
                    $('#inCompanyModal').modal('hide');
                    this.companyCode = "";
                    if (data == "exit"){
                        alert("员工手机号码已经添加！")
                    }else if (data == "exitCom"){
                        alert("该识别码存在错误！")
                    }else if (data == "success"){
                        alert("保存成功！")
                    }
                    _this.getCompanyList();
                }
            })
        },
        inCompany: function (row) {
            if (!row.recordId){
                alert("请刷新重试！");
                return;
            }
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/addCompanyUserTwo",
                data: row.recordId,
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    this.companyCode = "";
                    if (data == "exit"){
                        alert("员工手机号码已经添加！")
                    }else if (data == "exitCom"){
                        alert("该识别码存在错误！")
                    }else if (data == "success"){
                        alert("保存成功！")
                    }
                    _this.getCompanyList();
                }
            })
        },
        shareModal: function () {
            $('#shareModal').modal();
        },
        getQcCodeByCode: function () {
            this.qcCode = "";
            let code = "";
            if (this.shareMsg && this.shareMsg.recordId){
                code = this.shareMsg.code;
            }
            if (!this.shareMsg && this.user.code){
                code = this.user.code;
            }
            const _this = this;
            if (code){
                $('#loadingModal').modal();
                $.ajax({
                    type: "post",
                    url: ctx + "/f/wechat/business/getQcCodeByCode",
                    data: code,
                    contentType: "application/json",
                    success: function(data)
                    {
                        $('#loadingModal').modal('hide');
                        _this.qcCode = data;
                    }
                })
            }
        },
        loadCompany: function () {
            this.appCompanyList = {};
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/getCompanyByCode",
                data: this.companyCode,
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    _this.appCompanyList = data;
                }
            })
        },
        //公司退出
        exitModal: function (recordId) {
            this.recordId = recordId;
            const query = {};
            query.companyId = recordId;
            query.phone = this.user.phone;
            $('#loadingModal').modal();
            const _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/getExitCompanyMessage",
                data: JSON.stringify(query),
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    _this.companyMessage = data;
                    $('#exitModal').modal();
                }
            });
        },
        exitCompany: function (){
            if (!this.companyMessage.recordId){
                alert("请刷新重试！");
                return;
            }
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/deleteCompanyUser",
                data: this.companyMessage.recordId,
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    alert("退出成功！")
                    _this.getCompanyList();
                    $('#exitModal').modal('hide');
                }
            });
        },
        quickOperation: function () {
            $('#quickOperation').modal();
        }
    }
}