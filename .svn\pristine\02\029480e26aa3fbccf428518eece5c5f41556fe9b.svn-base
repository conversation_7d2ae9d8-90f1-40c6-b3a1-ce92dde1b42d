package com.kyb.pcberp.modules.report.dao;

import java.util.List;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.report.entity.Report;

@MyBatisDao
public interface SalesReportDao extends CrudDao<Report>
{
    /** zjn 2019-09-16 获取销售报表逾期数据 */
    List<Report> getSalesReportOverDue(Report salesReport);
    
    /** zjn 2019-09-17 获取所有报表数据 */
    List<Report> getAllReportData(Report report);
    
    /** zjn 2019-09-20 获取报表详情数据 */
    List<Report> getReportDetailData(Report report);
    
    /** zjn 2019-12-23 获取区间报表数据 */
    List<Report> getSectionReportData(Report report);
    
    /** zjn 2019-12-23 获取区间销售报表逾期数据 */
    List<Report> getSectionOverDueData(Report report);
    
    /** zjn 2019-12-27 获取区间报表详情数据 */
    List<Report> getSectionReportDetailData(Report report);
    
    /** fzd 2019-08-07 获取报表主数据 */
    List<Report> getSalesReportMain(Report salesReport);
    
    /** fzd 2019-08-09 重置报表数据 */
    void accountingSaleReportCurr();
    
    /** fzd 2019-08-09 删除销售报表所有数据 */
    void delReport();
}
