<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.purch.dao.BiddingDao">
    <select id="loadBiddingList" resultType="Bidding">
        SELECT
            a.*,
            b.purchApplyId,
            b.quantity,
            b.craftDescript,
            c.`no` AS "materialNo",
            IFNULL
            (
                (
                    SELECT
                        cc.`value`
                    FROM md_material_specification_relation aa
                    LEFT JOIN md_material_specification bb ON bb.recordId = aa.specificationId
                    LEFT JOIN md_dict_value cc ON cc.itemId = bb.dictItemId AND cc.recordId = aa.`value` AND cc.activeFlag = 1
                    WHERE aa.companyId = a.companyId AND aa.activeFlag = 1 AND aa.materialId = c.recordId AND bb.dictItemId = 2 LIMIT 1
                ),
                c.name
            ) AS "materialName",
            d.`no` AS "purchNo",
            (
                SELECT
                    COUNT(1)
                FROM icloud_md_bidding WHERE biddingId = a.recordId AND `status` = 60001
            ) AS "approvalCount",
            c.actualThickness,
            b.subjectType AS "type",
            e.VALUE AS "materialTypeValue"
        FROM
            md_bidding a
        LEFT JOIN pu_purchasing_apply b ON b.recordId = a.purchRawId
        LEFT JOIN md_material c ON c.recordId = b.materialId
        LEFT JOIN pu_purchasing d ON d.recordId = a.purchId
        LEFT JOIN md_dict_value e ON e.recordId = c.materialType
        WHERE
            a.companyId = #{companyId}
        AND a.activeFlag = 1
        <if test="sentTimeStartQr != null and sentTimeStartQr != ''">
            AND a.createdDate >= #{sentTimeStartQr}
        </if>
        <if test="sentTimeEndQr != null and sentTimeEndQr != ''">
            AND a.createdDate <![CDATA[<=]]> #{sentTimeEndQr}
        </if>
        <if test="no != null and no != ''">
            AND a.no like CONCAT('%', #{no}, '%')
        </if>
        <if test="title != null and title != ''">
            AND a.title like CONCAT('%', #{title}, '%')
        </if>
        <if test="status != null and status != ''">
            AND a.status = #{status}
        </if>
        ORDER BY a.createdDate DESC
    </select>

    <select id="getSupplierList" resultType="BiddingSupplier">
        SELECT
            a.*,
            IFNULL(b.`no`,CONCAT('ygs',c.recordId)) AS "no",
            IFNULL(b.`name`,c.name) AS "name",
            IFNULL(b.shortName,c.shortName) AS "shortName",
            IFNULL(b.bizPerson,c.linkMan) AS "bizPerson",
            IFNULL(b.icloudCompanyId,c.recordId) AS "icloudId",
            e.`value` AS "supplierLevel",
            f.`value` AS "supplierType",
            IFNULL(
                    GROUP_CONCAT(DISTINCT g. VALUE),
                    GROUP_CONCAT(DISTINCT h. VALUE)
                ) AS "industry"
        FROM
            md_bidding_supplier a
        LEFT JOIN md_supplier b ON b.recordId = a.supplierId AND a.type = 1
        LEFT JOIN icloud_md_company c ON c.recordId = a.supplierId AND a.type = 2
        LEFT JOIN md_approval_backup_two d ON d.supplierId = b.recordId AND d.activeFlag = 1
        LEFT JOIN md_dict_value e ON e.recordId = IFNULL(d.`level`, b.supplierlevel)
        LEFT JOIN md_dict_value f ON f.recordId = IFNULL(d.type, b.suppliertype)
        LEFT JOIN md_dict_value g ON FIND_IN_SET(g.recordId, d.industry)
        LEFT JOIN md_dict_value h ON FIND_IN_SET(h.recordId, b.industry)
        WHERE
            a.biddingId = #{recordId}
        AND a.activeFlag = 1
        GROUP BY a.recordId
    </select>

    <select id="getMaterialList" resultType="BiddingMaterial">
        SELECT
            a.*, b.`no` AS "no",
            b.`name` AS "name",
            b.specification AS "specification"
        FROM
            md_bidding_material a
        LEFT JOIN md_material b ON b.recordId = a.materialId
        WHERE
            a.biddingId = #{recordId}
        AND a.activeFlag = 1
    </select>

    <select id="getSupplierMaterialList" resultType="BiddingSupplierMaterial">
        SELECT
            a.*, d.`no` AS "biddingSupplier.no",
            d.`name` AS "biddingSupplier.name",
            d.shortName AS "biddingSupplier.shortName",
            e.`no` AS "biddingMaterial.no",
            e.`name` AS "biddingMaterial.name",
            e.specification AS "biddingMaterial.specification"
        FROM
            md_bidding_supplier_material a
            LEFT JOIN md_bidding_supplier b ON b.recordId = a.biddingSupplierId
            LEFT JOIN md_bidding_material c ON c.recordId = a.biddingMaterialId
            LEFT JOIN md_supplier d ON d.recordId = b.supplierId
            LEFT JOIN md_material e ON e.recordId = c.materialId
        WHERE
            a.biddingId = #{recordId}
        AND a.activeFlag = 1
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
        INSERT INTO `md_bidding` (
            `companyId`,
            `no`,
            `startTime`,
            `endTime`,
            `title`,
            `content`,
            `address`,
            `tenderSmsTemplate`,
            `winningSmsTemplate`,
            `startSmsTemplate`,
            `endSmsTemplate`,
            `status`,
            `activeFlag`,
            `createdBy`,
            `createdDate`,
            `remark`,
            `purchRawId`,
            `deliveryDate`
        )
        VALUES
        (
            #{companyId},
            #{no},
            #{startTime},
            #{endTime},
            #{title},
            #{content},
            #{address},
            #{tenderSmsTemplate},
            #{winningSmsTemplate},
            #{startSmsTemplate},
            #{endSmsTemplate},
            1,
            1,
            #{createdBy.recordId},
            NOW(),
            #{remark},
            #{purchRawId},
            #{deliveryDate}
        );
    </insert>

    <update id="update">
        UPDATE `md_bidding`
        SET `no` = #{no},
            `startTime` = #{startTime},
            `endTime` = #{endTime},
            `title` = #{title},
            `content` = #{content},
            `address` = #{address},
            `tenderSmsTemplate` = #{tenderSmsTemplate},
            `winningSmsTemplate` = #{winningSmsTemplate},
            `startSmsTemplate` = #{startSmsTemplate},
            `endSmsTemplate` = #{endSmsTemplate},
            `lastUpdBy` = #{lastUpdBy.recordId},
            `lastUpdDate` = NOW(),
            `remark` = #{remark},
            `deliveryDate` = #{deliveryDate}
        WHERE
            recordId = #{recordId};
    </update>

    <update id="clearSupplierList">
        UPDATE md_bidding_supplier
            SET activeFlag = 0
        WHERE
            biddingId = #{recordId}
    </update>

    <update id="clearMaterialList">
        UPDATE md_bidding_material
        SET activeFlag = 0
        WHERE
            biddingId = #{recordId}
    </update>

    <insert id="batchInsertSupplierList">
        INSERT INTO `md_bidding_supplier`
        (
            `biddingId`,
            `supplierId`,
            `biddingCode`,
            `activeFlag`,
            `createdBy`,
            `createdDate`,
            `remark`,
            `phone`,
            `materialId`,
            type
        )VALUES
        <foreach collection="supplierList" item="item" separator=",">
        (
            #{recordId},
            #{item.recordId},
            #{item.biddingCode},
            1,
            #{createdBy.recordId},
            NOW(),
            #{item.remark},
            #{item.phone},
            #{item.materialId},
            #{item.type}
        )
        </foreach>
    </insert>

    <insert id="batchInsertMaterialList">
        INSERT INTO `md_bidding_material`
        (
            `biddingId`,
            `materialId`,
            `activeFlag`,
            `createdBy`,
            `createdDate`,
            `remark`
        )VALUES
        <foreach collection="materialList" item="item" separator=",">
        (
            #{recordId},
            #{item.materialId},
            1,
            #{createdBy.recordId},
            NOW(),
            #{item.remark}
        )
        </foreach>
    </insert>

    <update id="delete">
        UPDATE md_bidding
        SET `activeFlag` = 0
        WHERE
            recordId = #{recordId}
    </update>

    <update id="editStatus">
        UPDATE md_bidding
        SET `status` = #{status}
        WHERE
            FIND_IN_SET(recordId, #{recordId})
    </update>

    <update id="updatePurchId">
        UPDATE md_bidding
        SET `purchId` = #{purchId}
        WHERE
            recordId = #{recordId}
    </update>

    <update id="editIcloudStatus">
        UPDATE icloud_md_bidding
        SET `status` = #{status}
        <if test="remark != null and remark != ''">
            ,remark = #{remark}
        </if>
        <if test="userId != null and userId != ''">
        ,lastUpdBy = #{userId},
        lastUpDate = NOW()
        </if>
        WHERE 1=1
        <if test="biddingId != null and biddingId != ''">
            AND biddingId = #{biddingId}
        </if>
        <if test="recordId != null and recordId != ''">
            AND recordId = #{recordId}
        </if>
    </update>

    <insert id="insertIcloudBidding">
        INSERT INTO `icloud_md_bidding`
        (
            `biddingId`,
            `erpCompanyId`,
            `supplierId`,
            `status`,
            `materialName`,
            `materialCraft`,
            `materialNum`,
            `createdBy`,
            `createdDate`,
            `startTime`,
            `endTime`,
            `deliveryDate`,
            `materialId`,
            `no`,
            `title`,
            `content`,
            `address`
        )VALUES
        <foreach collection="supplierList" item="item" separator=",">
            (
                #{recordId},
                #{companyId},
                #{item.icloudId},
                1,
                #{purchRaw.materialName},
                #{purchRaw.craftDescript},
                #{purchRaw.quantity},
                #{createdBy.recordId},
                NOW(),
                #{startTime},
                #{endTime},
                #{deliveryDate},
                #{item.materialId},
                #{no},
                #{title},
                #{content},
                #{address}
            )
        </foreach>
    </insert>

    <select id="getWinningList" resultType="IcloudBidding">
        SELECT
            a.*, b.`name` AS "supplierName",
            l.shortName AS "supplierShortName",
            c.`name` AS "customerName",
            d.`name` AS "icloudMaterialName",
            d.`specifications` AS "icloudMaterialSpecifications",
            d.stocks AS "icloudMaterialStocks",
            e.`value` AS "payWayVal",
            f.`value` AS "taxDescriptVal",
            g.`value` AS "currencyTypeVal",
            h.`value` AS "paycauseVal",
            j.materialId  AS "materialIdTwo",
            k.type,
            IFNULL
            (
                (
                    SELECT
                     cc.`value`
                    FROM md_material_specification_relation aa
                    LEFT JOIN md_material_specification bb ON bb.recordId = aa.specificationId
                    LEFT JOIN md_dict_value cc ON cc.itemId = bb.dictItemId AND cc.recordId = aa.`value` AND cc.activeFlag = 1
                    LEFT JOIN md_material dd ON dd.recordId = aa.materialId
                    WHERE aa.companyId = a.erpCompanyId AND aa.activeFlag = 1 AND dd.`name` = a.materialName AND dd.specification = a.materialCraft
                    AND bb.dictItemId = 2 LIMIT 1
                ),
                a.materialName
             ) AS "materialNameTwo",
             i.status AS "biddingStatus",
             GROUP_CONCAT(o.`value`) AS "erpSupplier.industry",
             m.value AS "erpSupplier.supplierLevelValue",
             n.value AS "erpSupplier.supplierTypeValue",
             GROUP_CONCAT( DISTINCT p.VALUE ) AS "erpSupplier.classificationValue",
             r.value AS "manufacturer"
        FROM
            icloud_md_bidding a
        LEFT JOIN icloud_md_company b ON b.recordId = a.supplierId
        LEFT JOIN icloud_md_company c ON c.erpCompanyId = a.erpCompanyId
        AND c.activeFlag = 1
        LEFT JOIN inter_config_supplier d ON d.recordId = a.materialId
        LEFT JOIN md_dict_value e ON e.recordId = a.payWay
        LEFT JOIN md_dict_value f ON f.recordId = a.taxDescript
        LEFT JOIN md_dict_value g ON g.recordId = a.currencyType
        LEFT JOIN md_dict_value h ON h.recordId = a.paycause
        LEFT JOIN md_bidding i ON i.recordId = a.biddingId
        LEFT JOIN pu_purchasing_apply j ON j.recordId = i.purchRawId
        LEFT JOIN md_bidding_supplier k ON k.biddingId = a.biddingId AND k.supplierId = a.supplierId AND k.activeFlag = 1
        LEFT JOIN md_supplier l ON l.icloudId = b.recordId AND l.companyId = a.erpCompanyId
        LEFT JOIN md_dict_value m ON m.recordId = l.supplierlevel
        LEFT JOIN md_dict_value n ON n.recordId = l.suppliertype
        LEFT JOIN md_dict_value o ON FIND_IN_SET(o.recordId,l.industry)
        LEFT JOIN md_dict_value p ON FIND_IN_SET( p.recordId, l.classificationId )
		LEFT JOIN md_material_specification_relation q ON q.materialId = j.materialId
		AND q.companyId = j.companyId
		AND q.specificationId = (SELECT recordId FROM md_material_specification WHERE companyId = j.companyId AND dictItemId = 70 AND activeFlag = 1 LIMIT 1)
		AND q.activeFlag = 1
		LEFT JOIN md_dict_value r ON r.recordId = q.`value`
        WHERE
            a.biddingId = #{recordId}
            GROUP BY a.recordId
    </select>

    <select id="getMaterialByIcloudId" resultType="Material">
        SELECT
            a.`no`,
            IFNULL
            (
                (
                    SELECT
                        cc.`value`
                    FROM md_material_specification_relation aa
                    LEFT JOIN md_material_specification bb ON bb.recordId = aa.specificationId
                    LEFT JOIN md_dict_value cc ON cc.itemId = bb.dictItemId AND cc.recordId = aa.`value` AND cc.activeFlag = 1
                    WHERE aa.companyId = a.companyId AND aa.activeFlag = 1 AND aa.materialId = a.recordId AND bb.dictItemId = 2 LIMIT 1
                ),
                name
            ) AS "name",
            a.specification,
            a.remark
        FROM
            md_material a
        WHERE
            a.companyId = #{companyId}
        AND a.icloudId = #{icloudId}
        AND a.activeFlag = 1
        AND a.`status` = 1
    </select>

    <select id="get" resultType="Bidding">
        SELECT
            a.*,
            b.purchApplyId,
            b.quantity,
            b.craftDescript,
            c.`no` AS "materialNo",
            c.`name` AS "materialName",
            d.`no` AS "purchNo"
        FROM md_bidding a
        LEFT JOIN pu_purchasing_apply b ON b.recordId = a.purchRawId
        LEFT JOIN md_material c ON c.recordId = b.materialId
        LEFT JOIN pu_purchasing d ON d.recordId = a.purchId
        WHERE a.recordId = #{recordId}
    </select>

    <select id="getWinning" resultType="IcloudBidding">
        SELECT
            a.*, b.`name` AS "supplierName",
            c.`name` AS "customerName",
            d.`name` AS "icloudMaterialName",
            d.`specifications` AS "icloudMaterialSpecifications",
            d.stocks AS "icloudMaterialStocks",
            e.`value` AS "payWayVal",
            f.`value` AS "taxDescriptVal",
            g.`value` AS "currencyTypeVal",
            h.`value` AS "paycauseVal",
            j.materialId AS "materialIdTwo",
            k.type
        FROM
            icloud_md_bidding a
        LEFT JOIN icloud_md_company b ON b.recordId = a.supplierId
        LEFT JOIN icloud_md_company c ON c.erpCompanyId = a.erpCompanyId
        AND c.activeFlag = 1
        LEFT JOIN inter_config_supplier d ON d.recordId = a.materialId
        LEFT JOIN md_dict_value e ON e.recordId = a.payWay
        LEFT JOIN md_dict_value f ON f.recordId = a.taxDescript
        LEFT JOIN md_dict_value g ON g.recordId = a.currencyType
        LEFT JOIN md_dict_value h ON h.recordId = a.paycause
        LEFT JOIN md_bidding i ON i.recordId = a.biddingId
        LEFT JOIN pu_purchasing_apply j ON j.recordId = i.purchRawId
        LEFT JOIN md_bidding_supplier k ON k.biddingId = a.biddingId AND k.supplierId = a.supplierId AND k.activeFlag = 1
        WHERE a.recordId = #{recordId}
        GROUP BY a.recordId
    </select>

    <select id="getShowWxData" resultType="Bidding">
        SELECT
            a.*,
            b.quantity,
            IFNULL
            (
                    (
                        SELECT
                         cc.`value`
                        FROM md_material_specification_relation aa
                        LEFT JOIN md_material_specification bb ON bb.recordId = aa.specificationId
                        LEFT JOIN md_dict_value cc ON cc.itemId = bb.dictItemId AND cc.recordId = aa.`value` AND cc.activeFlag = 1
                        WHERE aa.companyId = a.companyId AND aa.activeFlag = 1 AND aa.materialId = c.recordId AND bb.dictItemId = 2 LIMIT 1
                    ),
                    c.name
             ) AS "materialName",
            c.specification
        FROM md_bidding a
        LEFT JOIN pu_purchasing_apply b ON b.recordId = a.purchRawId
        LEFT JOIN md_material c ON c.recordId = b.materialId
        WHERE a.recordId = #{recordId} AND a.activeFlag = 1
    </select>

    <select id="getShowWxDataTwo" resultType="IcloudBidding">
        SELECT
            a.*, b.`name` AS "supplierName",
            l.shortName AS "supplierShortName",
            m.shortName AS "customerShortName",
            c.`name` AS "customerName",
            d.`name` AS "icloudMaterialName",
            d.`specifications` AS "icloudMaterialSpecifications",
            d.stocks AS "icloudMaterialStocks",
            e.`value` AS "payWayVal",
            f.`value` AS "taxDescriptVal",
            g.`value` AS "currencyTypeVal",
            h.`value` AS "paycauseVal",
            j.materialId  AS "materialIdTwo",
            k.type,
            IFNULL
            (
                (
                    SELECT
                     cc.`value`
                    FROM md_material_specification_relation aa
                    LEFT JOIN md_material_specification bb ON bb.recordId = aa.specificationId
                    LEFT JOIN md_dict_value cc ON cc.itemId = bb.dictItemId AND cc.recordId = aa.`value` AND cc.activeFlag = 1
                    LEFT JOIN md_material dd ON dd.recordId = aa.materialId
                    WHERE aa.companyId = a.erpCompanyId AND aa.activeFlag = 1 AND dd.`name` = a.materialName AND dd.specification = a.materialCraft
                    AND bb.dictItemId = 2 LIMIT 1
                ),
                a.materialName
             ) AS "materialNameTwo",
            IFNULL(GROUP_CONCAT(DISTINCT n. VALUE),"") AS "classificationValue",
            j.subjectType AS "subjectType"
        FROM
            icloud_md_bidding a
        LEFT JOIN icloud_md_company b ON b.recordId = IFNULL(a.supplierId,a.customerId)
        LEFT JOIN icloud_md_company c ON c.erpCompanyId = a.erpCompanyId
        AND c.activeFlag = 1
        LEFT JOIN inter_config_supplier d ON d.recordId = a.materialId
        LEFT JOIN md_dict_value e ON e.recordId = a.payWay
        LEFT JOIN md_dict_value f ON f.recordId = a.taxDescript
        LEFT JOIN md_dict_value g ON g.recordId = a.currencyType
        LEFT JOIN md_dict_value h ON h.recordId = a.paycause
        LEFT JOIN md_bidding i ON i.recordId = a.biddingId
        LEFT JOIN pu_purchasing_apply j ON j.recordId = i.purchRawId
        LEFT JOIN md_bidding_supplier k ON k.biddingId = a.biddingId AND k.supplierId = a.supplierId AND k.activeFlag = 1
        LEFT JOIN md_supplier l ON l.icloudCompanyId = b.recordId AND l.companyId = a.erpCompanyId
        LEFT JOIN md_dict_value n ON FIND_IN_SET( n.recordId, l.classificationId )
        LEFT JOIN md_customer m ON m.icloudId = b.recordId AND m.companyId = a.erpCompanyId
        WHERE a.recordId = #{recordId}
        GROUP BY a.recordId
    </select>

    <select id="getBiddingBypurchId" resultType="IcloudBidding">
        SELECT
           a.*
        FROM icloud_md_bidding a
        LEFT JOIN md_bidding b ON b.recordId = a.biddingId
        WHERE b.purchId = #{purchId} AND b.activeFlag = 1 AND a.`status` = 4
    </select>

    <select id="getWxSupplierList" resultType="BiddingSupplier">
        SELECT
            a.*,
            IFNULL(b.`no`,f.no) AS "no",
            IFNULL(b.`name`,f.name) AS "name",
            IFNULL(b.shortName,f.shortName) AS "shortName",
            IFNULL(b.bizPerson,f.bizPerson) AS "bizPerson",
            IFNULL(b.icloudId,f.icloudId) AS "icloudId",
            d.value AS "supplierLevelValue",
            IFNULL(GROUP_CONCAT(DISTINCT h. VALUE),GROUP_CONCAT(DISTINCT g. VALUE)) AS "industry",
            e.value AS "supplierTypeValue",
            IFNULL(b.phone,f.phone) AS "wxPhone",
            IFNULL(GROUP_CONCAT( DISTINCT i.VALUE ),GROUP_CONCAT( DISTINCT j.VALUE )) AS "classificationValue"
        FROM
            md_bidding_supplier a
        LEFT JOIN md_supplier b ON b.recordId = a.supplierId AND a.type = 1
        LEFT JOIN icloud_md_company c ON c.recordId = a.supplierId AND a.type = 2
        LEFT JOIN md_supplier f ON f.icloudId = c.recordId AND f.companyId = #{companyId}
        LEFT JOIN md_dict_value d ON d.recordId = IFNULL(b.supplierlevel,f.supplierlevel)
        LEFT JOIN md_dict_value e ON e.recordId = IFNULL(b.suppliertype,f.suppliertype)
        LEFT JOIN md_dict_value g ON FIND_IN_SET(g.recordId, f.industry)
        LEFT JOIN md_dict_value h ON FIND_IN_SET(h.recordId, b.industry)
        LEFT JOIN md_dict_value i ON FIND_IN_SET( i.recordId, f.classificationId )
        LEFT JOIN md_dict_value j ON FIND_IN_SET( j.recordId, b.classificationId )
        WHERE
            a.biddingId = #{recordId}
        AND a.activeFlag = 1
        GROUP BY a.recordId
    </select>

    <select id="getIcloudBiddingList" resultType="IcloudBidding">
        SELECT
            a.*,
            d.`name` AS "supplierName",
            d.shortName AS "supplierShortName",
            b.purchRawId AS "purchRawId",
            d.recordId AS "erpSupplierId",
            e.purchApplyId AS "purchRawNo",
            e.craftDescript,
            f.recordId AS "materialIdTwo",
            f.`no` AS "materialNo",
            f.name AS "materialName",
            g.`value` AS "currencyTypeVal",
            e.purchaseWay
        FROM icloud_md_bidding a
        LEFT JOIN md_bidding b ON b.recordId = a.biddingId
        LEFT JOIN icloud_md_company c ON c.recordId = a.supplierId
        LEFT JOIN md_supplier d ON d.icloudId = c.recordId AND d.companyId = a.erpCompanyId
        LEFT JOIN pu_purchasing_apply e ON e.recordId = b.purchRawId
        LEFT JOIN md_material f ON f.recordId = e.materialId
        LEFT JOIN md_dict_value g ON g.recordId = a.currencyType
        WHERE b.companyId = #{company.recordId} AND b.activeFlag = 1
        AND b.`status` = 3 AND a.`status` = 4 AND b.purchId IS NULL
        ORDER BY a.createdDate DESC
    </select>

    <update id="updatePurchIdTwo">
        UPDATE md_bidding SET
            purchId = NULL
        WHERE activeFlag = 1 AND purchId = #{purchId}
    </update>

    <update id="updateIcloudBiddingData">
        UPDATE icloud_md_bidding aa
        INNER JOIN
        (
            SELECT
                d.`name` AS "materialName",
                d.specification AS "materialCraft",
                a.recordId
            FROM icloud_md_bidding a
            LEFT JOIN md_bidding b ON b.recordId = a.biddingId
            LEFT JOIN pu_purchasing_apply c ON c.recordId = b.purchRawId
            LEFT JOIN md_material d ON d.recordId = c.materialId
            WHERE b.activeFlag = 1 AND b.recordId = #{recordId}
        ) bb ON bb.recordId = aa.recordId
        SET aa.materialName = bb.materialName,
        aa.materialCraft = bb.materialCraft
    </update>

    <select id="getNum" resultType="Integer">
        SELECT
            COUNT(*)
        FROM
            md_bidding
        WHERE
            FIND_IN_SET(recordId, #{recordIds})
          AND activeFlag = 1
          AND `status` = 60001
    </select>

    <update id="updateStatus">
        UPDATE md_bidding SET
            `status` = #{status},
            lastUpdBy = #{lastUpdBy.recordId},
            lastUpdDate = #{lastUpdDate}
        WHERE
            recordId = #{recordId}
    </update>

    <select id="getShowWxDataList" resultType="Bidding">
        SELECT
            a.*,
            b.quantity,
            IFNULL
                (
                    (
                        SELECT
                            cc.`value`
                        FROM md_material_specification_relation aa
                                 LEFT JOIN md_material_specification bb ON bb.recordId = aa.specificationId
                                 LEFT JOIN md_dict_value cc ON cc.itemId = bb.dictItemId AND cc.recordId = aa.`value` AND cc.activeFlag = 1
                        WHERE aa.companyId = a.companyId AND aa.activeFlag = 1 AND aa.materialId = c.recordId AND bb.dictItemId = 2 LIMIT 1
                ),
                    c.name
             ) AS "materialName",
            c.specification
        FROM md_bidding a
                 LEFT JOIN pu_purchasing_apply b ON b.recordId = a.purchRawId
                 LEFT JOIN md_material c ON c.recordId = b.materialId
        WHERE FIND_IN_SET(a.recordId,#{recordId}) AND a.activeFlag = 1
    </select>

    <select id="getBiddingList" resultType="Bidding">
        SELECT
            a.*,
            b.purchApplyId,
            b.quantity,
            b.craftDescript,
            c.`no` AS "materialNo",
            c.`name` AS "materialName",
            d.`no` AS "purchNo"
        FROM md_bidding a
                 LEFT JOIN pu_purchasing_apply b ON b.recordId = a.purchRawId
                 LEFT JOIN md_material c ON c.recordId = b.materialId
                 LEFT JOIN pu_purchasing d ON d.recordId = a.purchId
        WHERE FIND_IN_SET(a.recordId,#{recordId})
    </select>

    <update id="updateEndTime">
        UPDATE md_bidding SET
             activeFlag = 1
             <if test="endTime != null and endTime != ''">
                 ,`endTime` = #{endTime}
             </if>
            <if test="deliveryDate != null and deliveryDate != ''">
                ,`deliveryDate` = #{deliveryDate}
            </if>
        WHERE recordId = #{recordId}
    </update>

    <select id="getPurchaseWayList" resultType="IcloudBidding">
        SELECT
            a.purchApplyId AS "purchRawNo",
            b. NO AS "materialNo",
            b. NAME AS "materialName",
            a.craftDescript AS "materialCraft",
            a.quantity AS "materialNum",
            a.purchaseWay,
            a.createdDate
        FROM
            pu_purchasing_apply a
                LEFT JOIN md_material b ON b.recordId = a.materialId
        WHERE
            a.activeFlag = 1 and a.companyId = #{company.recordId} and a.status = 7 and a.purchaseWay IN (2, 3)
        ORDER BY a.createdDate DESC
    </select>

    <insert id="batchInsertCustomerList">
        INSERT INTO `md_bidding_supplier`
        (
        `biddingId`,
        `biddingCode`,
        `activeFlag`,
        `createdBy`,
        `createdDate`,
        `remark`,
        `phone`,
        `materialId`,
        type,
        customerId
        )VALUES
        <foreach collection="customerList" item="item" separator=",">
            (
            #{recordId},
            #{item.biddingCode},
            1,
            #{createdBy.recordId},
            NOW(),
            #{item.remark},
            #{item.phone},
            #{item.materialId},
            #{item.type},
            #{item.recordId}
            )
        </foreach>
    </insert>

    <select id="getCustomerList" resultType="BiddingSupplier">
        SELECT
            a.*,
            IFNULL(b.`name`,c.name) AS "name",
            IFNULL(b.shortName,c.shortName) AS "shortName"
        FROM
            md_bidding_supplier a
                LEFT JOIN icloud_md_company b ON b.recordId = a.customerId
                AND a.type = 1
                LEFT JOIN icloud_md_company c ON c.recordId = a.customerId
                AND a.type = 2
        WHERE
            a.biddingId = #{recordId}
          AND a.activeFlag = 1
        GROUP BY a.recordId
    </select>

    <insert id="insertIcloudCustomerBidding">
        INSERT INTO `icloud_md_bidding`
        (
        `biddingId`,
        `erpCompanyId`,
        `status`,
        `materialName`,
        `materialCraft`,
        `materialNum`,
        `createdBy`,
        `createdDate`,
        `startTime`,
        `endTime`,
        `deliveryDate`,
        `materialId`,
        `no`,
        `title`,
        `content`,
        `address`,
        `customerId`
        )VALUES
        <foreach collection="customerList" item="item" separator=",">
            (
            #{recordId},
            #{companyId},
            1,
            #{purchRaw.materialName},
            #{purchRaw.craftDescript},
            #{purchRaw.quantity},
            #{createdBy.recordId},
            NOW(),
            #{startTime},
            #{endTime},
            #{deliveryDate},
            #{item.materialId},
            #{no},
            #{title},
            #{content},
            #{address},
            #{item.customerId}
            )
        </foreach>
    </insert>

    <select id="getWinningListTwo" resultType="IcloudBidding">
        SELECT
            a.*,
            b.`name` AS "supplierName",
            c.`name` AS "customerName",
            d.`name` AS "icloudMaterialName",
            d.`specifications` AS "icloudMaterialSpecifications",
            d.stocks AS "icloudMaterialStocks",
            e.`value` AS "payWayVal",
            f.`value` AS "taxDescriptVal",
            g.`value` AS "currencyTypeVal",
            h.`value` AS "paycauseVal",
            j.materialId AS "materialIdTwo",
            k.type,
            IFNULL (
                    (
                        SELECT
                            cc.`value`
                        FROM
                            md_material_specification_relation aa
                                LEFT JOIN md_material_specification bb ON bb.recordId = aa.specificationId
                                LEFT JOIN md_dict_value cc ON cc.itemId = bb.dictItemId
                                AND cc.recordId = aa.`value`
                                AND cc.activeFlag = 1
                                LEFT JOIN md_material dd ON dd.recordId = aa.materialId
                        WHERE
                            aa.companyId = a.erpCompanyId
                          AND aa.activeFlag = 1
                          AND dd.`name` = a.materialName
                          AND dd.specification = a.materialCraft
                          AND bb.dictItemId = 2
                        LIMIT 1
                ),
                a.materialName
              ) AS "materialNameTwo",
            i.STATUS AS "biddingStatus",
            r.
                VALUE
                AS "manufacturer"
        FROM
            icloud_md_bidding a
                LEFT JOIN icloud_md_company b ON b.recordId = a.customerId
                LEFT JOIN icloud_md_company c ON c.erpCompanyId = a.erpCompanyId
                AND c.activeFlag = 1
                LEFT JOIN inter_config_supplier d ON d.recordId = a.materialId
                LEFT JOIN md_dict_value e ON e.recordId = a.payWay
                LEFT JOIN md_dict_value f ON f.recordId = a.taxDescript
                LEFT JOIN md_dict_value g ON g.recordId = a.currencyType
                LEFT JOIN md_dict_value h ON h.recordId = a.paycause
                LEFT JOIN md_bidding i ON i.recordId = a.biddingId
                LEFT JOIN pu_purchasing_apply j ON j.recordId = i.purchRawId
                LEFT JOIN md_bidding_supplier k ON k.biddingId = a.biddingId
                AND k.supplierId = a.customerId
                AND k.activeFlag = 1
                LEFT JOIN md_material_specification_relation q ON q.materialId = j.materialId
                AND q.companyId = j.companyId
                AND q.specificationId = ( SELECT recordId FROM md_material_specification WHERE companyId = j.companyId AND dictItemId = 70 AND activeFlag = 1 LIMIT 1 )
            AND q.activeFlag = 1
            LEFT JOIN md_dict_value r ON r.recordId = q.`value`
        WHERE
            a.biddingId = #{recordId}
        GROUP BY
            a.recordId
    </select>

    <insert id="insertAttache">
        INSERT INTO pu_supplier_billde_attachements(
            supplierBilldeId,
            orgfilename,
            realfilename,
            fileurl,
            companyId,
            createdBy,
            createdDate,
            activeFlag
        ) VALUES (
           #{supplierBilldeId},
           #{orgFileName},
           #{realFileName},
           #{fileUrl},
           #{companyId},
           #{createdBy.recordId},
           #{createdDate},
           1
        )
    </insert>

    <select id="getSupplierBiddeAttachementsList" resultType="SupplierBiddeAttache">
        SELECT * FROM pu_supplier_billde_attachements WHERE companyId = #{company.recordId} AND supplierBilldeId = #{supplierBilldeId} AND activeFlag = 1
    </select>

    <update id="deleteAttache">
        UPDATE pu_supplier_billde_attachements SET activeFlag = 0 WHERE companyId = #{company.recordId} AND recordId = #{recordId}
    </update>

    <select id="getBiddeAttachementsList" resultType="SupplierBiddeAttache">
        SELECT * FROM pu_supplier_billde_attachements WHERE supplierBilldeId = #{recordId} AND activeFlag = 1
    </select>
</mapper>

