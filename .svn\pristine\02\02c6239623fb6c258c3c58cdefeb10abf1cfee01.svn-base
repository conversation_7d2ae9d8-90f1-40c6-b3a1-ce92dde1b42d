package com.kyb.pcberp.modules.production.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

/**
 * 班次设置表
 * 
 * <AUTHOR> 2020-11-26
 *
 */
public class ProductionShiftSet extends DataEntity<ProductionShiftSet>
{
    private static final long serialVersionUID = 1L;
    
    private String shitId; // 班次id
    
    private String processId; // 工序id，同款数同工序的只能设置一次
    
    private String maxNum; // 款数限制，从小开始算
    
    private Double productArea; // 当前款数对应面积产能，如果没有款数，适应于所有
    
    private Integer type; // 类型:1工序id,2:工序值id
    
    public String getShitId()
    {
        return shitId;
    }
    
    public void setShitId(String shitId)
    {
        this.shitId = shitId;
    }
    
    public String getProcessId()
    {
        return processId;
    }
    
    public void setProcessId(String processId)
    {
        this.processId = processId;
    }
    
    public String getMaxNum()
    {
        return maxNum;
    }
    
    public void setMaxNum(String maxNum)
    {
        this.maxNum = maxNum;
    }
    
    public Double getProductArea()
    {
        return productArea;
    }
    
    public void setProductArea(Double productArea)
    {
        this.productArea = productArea;
    }

    public Integer getType()
    {
        return type;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }
    
    
}
