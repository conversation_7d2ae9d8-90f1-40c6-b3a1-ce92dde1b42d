package com.kyb.pcberp.modules.hr.group_center.service;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.hr.group_center.dao.Hr_CenterDao;
import com.kyb.pcberp.modules.hr.group_center.pojo.*;
import com.kyb.pcberp.modules.wechat.entity.IcloudUser;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatReport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class Hr_CenterService {
    @Autowired
    private Hr_CenterDao hr_centerDao;

    public Page<Hr_Report> getReports(Hr_Report hr_Report) {
        Page<Hr_Report> page = new Page<Hr_Report>();
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(hr_Report.getPageNo())) {
            pageNo = Integer.valueOf(hr_Report.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_Report.getPageSize())) {
            pageSize = Integer.valueOf(hr_Report.getPageSize());
        }
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);

        List<WechatReport> reportList = hr_centerDao.getReportList(hr_Report);
        List<Hr_Report> hrReportList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        for (WechatReport report : reportList) {
            if (StringUtils.isBlank(report.getName()) || StringUtils.isBlank(report.getEmployeeId()) || map.containsKey(report.getEmployeeId())) {
                continue;
            }
            map.put(report.getEmployeeId(), report.getEmployeeId());
            Hr_Report hrReport = new Hr_Report();
            hrReport.setName(report.getName());
            hrReport.setRecordId(report.getEmployeeId());
            hrReport.setPosition(report.getPosition());
            hrReport.setGroupName(report.getGroupName());
            hrReport.setWorkStatus(report.getWorkStatus());
            Integer dayNum = 0;
            Integer weekNum = 0;
            Integer monthNum = 0;
            List<WechatReport> list = new ArrayList<>();
            for (WechatReport wechatReport : reportList) {
                if (wechatReport.getEmployeeId().equals(hrReport.getRecordId())) {
                    list.add(wechatReport);
                    if (StringUtils.isNotBlank(wechatReport.getFlag()))
                    {
                        if (wechatReport.getFlag().equals("1")) {
                            dayNum++;
                        } else if (wechatReport.getFlag().equals("2")) {
                            weekNum++;
                        } else if (wechatReport.getFlag().equals("3")) {
                            monthNum++;
                        }
                    }
                }
            }
            hrReport.setDayNum(dayNum);
            hrReport.setWeekNum(weekNum);
            hrReport.setMonthNum(monthNum);
            hrReport.setReportList(list);
            hrReportList.add(hrReport);
        }
        Integer num = (pageNo - 1) * pageSize;
        page.setList(Lists.newArrayList());
        for(int i = num;i < hrReportList.size();i++)
        {
            if(page.getList().size() == pageSize)
            {
                break;
            }
            page.getList().add(hrReportList.get(i));
        }
        page.setCount(hrReportList.size());
        return page;
    }

    public Hr_System getSystemMsg(IcloudUser user) {
        if (user == null || StringUtils.isBlank(user.getGroupManageId())) {
            return null;
        }
        Hr_System system = hr_centerDao.getSystemMsg(user);
        return system;
    }

    @Transactional(readOnly = false)
    public String editSystemMsg(Hr_System hrSystem) {
        hr_centerDao.editSystemMsg(hrSystem);
        return "success";
    }

    public List<Hr_Db> getDbList(IcloudUser user) {
        if (user == null || StringUtils.isBlank(user.getGroupManageId())) {
            return null;
        }
        List<Hr_Db> dbList = hr_centerDao.getDbList(user);
        return dbList;
    }

    @Transactional(readOnly = false)
    public String editDbMsg(Hr_Db hrDb) {
        hr_centerDao.editDbMsg(hrDb);
        return "success";
    }

    public Page<Hr_Process> getProcessList(IcloudUser user) {
        Page<Hr_Process> page = new Page<Hr_Process>();
        Page<IcloudUser> pages = new Page<>();
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(user.getPageNo())) {
            pageNo = Integer.valueOf(user.getPageNo());
        }
        if (StringUtils.isNotBlank(user.getPageSize())) {
            pageSize = Integer.valueOf(user.getPageSize());
        }
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        pages.setPageNo(pageNo);
        pages.setPageSize(pageSize);
        user.setPage(pages);
        List<Hr_Process> groupProcess = hr_centerDao.getProcessList(user);
        page.setCount(pages.getCount());
        page.setList(groupProcess);
        return page;
    }

    //集团管控保存
    @Transactional(readOnly = false)
    public String saveProcess(Hr_Process groupProcessTwo) {
        if (StringUtils.isNotBlank(groupProcessTwo.getRecordId())) {
            groupProcessTwo.setLastUpdBy(groupProcessTwo.getUser());
            hr_centerDao.updateProcess(groupProcessTwo);
            return "success";
        } else {
            groupProcessTwo.setGroupUserId(groupProcessTwo.getUser().getRecordId());
            groupProcessTwo.setCreatedBy(groupProcessTwo.getUser());
            hr_centerDao.insertProcess(groupProcessTwo);
            return "success";
        }
    }

    //获取组织架构树图数据
    public List<Hr_DepartRelation> getList(IcloudUser user) {
        List<Hr_DepartRelation> list = hr_centerDao.getList(user);
        return list;
    }

    //删除集团管控
    @Transactional(readOnly = false)
    public String delProcess(Hr_Process groupProcessTwo) {
        hr_centerDao.delProcess(groupProcessTwo);
        return "success";
    }
}
