const workState = {
  work: {}
}
const workMutations = {
  getWorkList: function (state, args) {
	  $.ajax({
			type:"post",
		    url:ctx + "/f/wechat/kybsoftOA/getWorkList",
		    data:JSON.stringify(args),
		    contentType:"application/json",
		    success:function(data)
		    { 
		    	state.work = {}
		    	if (data.length > 0) {
		            const workList = data
		            state.work.dayThingList = []
		            state.work.kybAuditList = []
		            state.work.kybReportList = []
		            state.work.kybTaskList = []
		            state.work.noticeList = []
					state.work.batchAreaList = []
		            for (let obj of workList) {
		              switch (obj.type) {
		                // 日程安排
		                case '1':
		                  state.work.dayThingList.push(obj)
		                  break
		                // 待办审批
		                case '2':
	                	  state.work.kybAuditList.push(obj)
		                  break
		                // 未读报告
		                case '3':
		                  state.work.kybReportList.push(obj)
		                  break
		                // 未完成任务
		                case '4':
		                  state.work.kybTaskList.push(obj)
		                  break
		                // 通知公告
		                case '5':
		                  state.work.noticeList.push(obj)
					 	// 通知公告
					    case '6':
						  state.work.batchAreaList.push(obj)
		                  break
		              }
		            }
		    	}
		    }
	  })
  }
}
const workActions = {
  getWorkList (context, args) {
    context.commit('getWorkList', args)
  }
}
const workGetters = {
}
const workStore = {
  namespaced: true,
  state: workState,
  mutations: workMutations,
  actions: workActions,
  getters: workGetters
}
