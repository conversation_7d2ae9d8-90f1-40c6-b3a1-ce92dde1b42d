<%@ page contentType="text/html;charset=UTF-8"%>
<div ng-intro-options="notice.introListOptions" ng-intro-method="notice.helpList" ng-intro-autostart="notice.shouldAutoStart"></div>
<div ng-intro-options="notice.introDetailOptions" ng-intro-method="notice.helpDetail" ng-intro-autostart="notice.shouldAutoStart"></div>

<!-- BEGIN 导航-->

<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
           <a ui-sref="notice">公告管理</a>
        </li>
    </ul>
    
    <div class="page-toolbar">
    	<button class="btn btn-fit-height default pull-right" ng-click="notice.help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>
	
</div>


<!-- END 导航-->
<tabset class="tabset-margin-top"> <!-- BEGIN 公告列表 --> 
<tab heading="公告列表"
	active="notice.tabs.viewForm.active">
		<div class="rows" >
		   <div class="panel panel-default" id="step6">
					<div class="panel-heading font-blue-hoki">查询</div>
					<div class="panel-body">
	  					<form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">公告标题：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-model="notice.query.userCode.value" disable-valid-styling="true" disable-invalid-styling="true" />
                                    </div>
                                </div>
                            </div>
                        </div>
	                       
	                       <div class="row">
	                           <div class="col-sm-12">
	                           	<button class="btn btn-default btn-default-width pull-right" ng-click="notice.doQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
	                           </div>
	                       </div>
	                   </form>
					</div>
				</div>
		<div class="portlet light bordered">
			<div class="portlet-title">
				<div class="caption font-blue-hoki">公告列表</div>
					<div class="actions" id="step7">
							<div class="portlet-input input-inline input-small">
								<button type="button" class="btn green btn-default-width" ng-if="notice.right.edit" ng-click="notice.addUser()"><i class="fa fa-plus"></i>添加公告</button>
							</div>
					</div>
				
			</div>
			<div class="portlet-body">
					<div class="table-responsive" id="step1">
				<table id="contentTable"
					class="table table-striped table-bordered table-condensed table-advance table-hover" >
					<thead>
						<tr class="heading">
							<!-- <th>编&nbsp;号</th> -->
							<th id="step2" width="15%"  ng-class="{'sorting': notice.sort.title.both, 'sorting_desc': notice.sort.title.desc, 'sorting_asc': notice.sort.title.asc}" ng-click="notice.sortClick('title')">标&nbsp;题</th>
							<th>内&nbsp;容</th>
							<th>备&nbsp;注</th>
							<th ng-if="notice.right.edit || notice.right.manage" id="step3">操&nbsp;作</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-repeat="row in notice.page.data.list" ng-dblclick="notice.modUser($index)">
							<!-- <td ng-bind="row.recordId"></td> -->
							<td >
								  <a ng-bind="row.title" href="javascript:void(0)" ng-click="notice.modUser($index)"></a> 
							</td>
							<td ng-bind="row.content"></td>
							<td ng-bind="row.remark"></td>
							<td ng-if="notice.right.edit || notice.right.manage">
								<a  href="javascript:void(0)" class="btn btn-xs btn-default"  ng-click="notice.delUser($index)"><i class="fa fa-times font-red"></i> 删&nbsp;除</a>
								<a  href="javascript:void(0)" class="btn btn-xs btn-default"  ng-click="notice.sendMessage($index)" ng-if="row.receiveUser || row.department">发送消息</a>
							</td>
						</tr>
					</tbody>
				</table>
				</div>
					<div  class="row page-margin-top">
						<div class="col-md-12 col-lg-6" id="step5">
							<span class="inline">每页</span> <select class="form-control inline"
								style="margin-top: 8px; width: 100px;"
								disable-valid-styling="true" disable-invalid-styling="true"
								ng-model="notice.page.pageSize"
								ng-change="notice.pageSizeChange()"
								ng-options="pageSizeOption for pageSizeOption in notice.page.pageSizeOptions">
							</select> <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示
								{{notice.page.data.startCount}} / {{notice.page.data.endCount}}
								条，共 {{notice.page.data.count}} 条</span>
						</div>
						<div class="col-md-12 col-lg-6" id="step4">
							<paging class="pull-right" page="notice.page.data.pageNo"
								page-size="notice.page.data.pageSize"
								total="notice.page.data.count" adjacent="1" dots="..."
								scroll-top="false" hide-if-empty="false" ul-class="pagination"
								active-class="active" disabled-class="disabled"
								show-prev-next="true"
								paging-action="notice.doPage(page, pageSize, total)"> </paging>
						</div>
					</div>
			</div>
				</div>
	</div>
</div>
</tab> <!-- END 用户列表 --> <!-- BEGIN 用户编辑 --> <tab
	active="notice.tabs.editForm.active"
	ng-show="notice.tabs.editForm.show"> <tab-heading>
公告详情 <i style="cursor: pointer" class="fa fa-times"
	ng-click="notice.hideEditForm()"></i> </tab-heading>

<div class="rows" ng-if="notice.right.edit">
		<div class="portlet light bordered">
			<div class="portlet-title">
				<div class="caption font-blue-hoki">
				
						公告{{notice.editTitle}}
				</div>
			</div>
			<div class="portlet-body" id="stepDetail1">
				<form class="form-horizontal"  ng-init="notice.setFormScope(this)" name="dict_form" id="dict_form"
					novalidate="novalidate" ng-submit="notice.submitMod(dict_form);"
					ng-submit-force="true">
					<div class="form-group">
						<label class="col-sm-3 control-label"><span
							class="required">*</span>公告标题：</label>
						<div class="col-sm-7">

							<input  firstfocus="{{notice.focus.main}}" type="text" name="item_value" ng-model="notice.user.title"
								required ng-maxlength="20" ng-minlength="2" class="form-control"
								placeholder="最少2个字">
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-3 control-label">是否显示：</label>
						<div class="col-sm-7">
							<select class="form-control" ng-model="notice.user.operLevel">
								<option value="1" selected="selected">显示</option>
								<option value="2">隐藏</option>
							</select>
						</div>
					</div>
				
				    <div class="form-group">
                        <label class="col-sm-3 control-label">接收用户：</label>
                        <div class="col-sm-7">
                            <ui-select theme="bootstrap" multiple ng-model="notice.user.receiveUserList" 
                                sortable="true" ng-disabled="notice.forbidden.subscriber"
                                close-on-select="false" ng-mouseleave="notice.disableDepa()" required >
                                <ui-select-match placeholder="请选择..." >{{$item.userName}}</ui-select-match>
                                <ui-select-choices repeat="item in notice.users | filter: $select.search">
                                    <div ng-bind-html="item.userName | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>

				    <div class="form-group">
					    <label class="col-sm-3 control-label">接收部门：</label>
					    <div class="col-sm-7">
						    <ui-select register-custom-form-control
							    ng-model="notice.user.department" theme="bootstrap" 
							    on-select="notice.disableUser()" ng-disabled="notice.forbidden.depa">
						        <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
						        <ui-select-choices repeat="item in notice.departments | filter: $select.search">
						            <div ng-bind-html="item.name | highlight: $select.search"></div>
						        </ui-select-choices> 
						    </ui-select>
					    </div>
				    </div>

					<div class="form-group">
						<label class="col-sm-3 control-label">发送部门：</label>
						<div class="col-sm-7">
							<ui-select register-custom-form-control
									   ng-model="notice.user.sendDepart" theme="bootstrap">
								<ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
								<ui-select-choices repeat="item in notice.departments | filter: $select.search">
									<div ng-bind-html="item.name | highlight: $select.search"></div>
								</ui-select-choices>
							</ui-select>
						</div>
					</div>

					<div class="form-group">
						<label class="col-sm-3 control-label"><span
							class="required">*</span>公告内容：</label>

						<div class="col-sm-7">
							<textarea class="form-control" id="maxlength_textarea"
								placeholder="1-1000个字" rows="10" ng-model="notice.user.content"
								ng-maxlength="1000" required></textarea>
						</div>
					</div>

					<div class="form-group">
						<label class="col-sm-3 control-label">备注：</label>
						<div class="col-sm-7">
							<textarea class="form-control" id="maxlength_remark" rows="5"
								ng-model="notice.user.remark" ng-maxlength="200" placeholder="1-200个字"></textarea>
						</div>
					</div>


					<div class="form-group">
						<div class="col-sm-offset-4 col-sm-10"  id="stepDetail2">
								   <button type="submit"  class="btn btn-primary btn-default-width" style="margin-left:15px;" ng-disabled="dict_form.$invalid"><i class="fa fa-save"></i> 保&nbsp;存</button>
						</div>
					</div>
				</form>
			</div>
		</div>
</div>
</tab> <!-- END 公告编辑 -->


 <tab
	active="notice.tabs.SeeForm.active"
	ng-show="notice.tabs.SeeForm.show"> <tab-heading>
公告详情 <i style="cursor: pointer" class="fa fa-times"
	ng-click="notice.hideSeeForm()"></i> </tab-heading>

<div class="rows" ng-if="notice.right.view">
		<div class="portlet light bordered">
			<div class="portlet-title">
				<div class="caption font-blue-hoki">
				
						公告{{notice.editTitle}}
				</div>
			</div>
			<div class="portlet-body">
				<form class="form-horizontal" name="dict_form2"
					novalidate="novalidate" ng-submit="notice.submitMod(dict_form);"
					ng-submit-force="true">
					<div class="form-group">
						<label class="col-sm-3 control-label">公告标题：</label>
						<div class="col-sm-7">

							<input ng-disabled="true" type="text" name="item_value2" ng-model="notice.user.title"
								required ng-maxlength="20" ng-minlength="6" class="form-control"
								placeholder="6-20个字符">
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-3 control-label">是否显示：</label>
						<div class="col-sm-7">
							<select ng-disabled="true" class="form-control" ng-model="notice.user.operLevel" >
								<option value="1">显示</option>
								<option value="2">隐藏</option>
							</select>
						</div>
					</div>
					
					<div class="form-group">
                        <label class="col-sm-3 control-label">接收用户：</label>
                        <div class="col-sm-7" ng-click="">
                            <ui-select theme="bootstrap" multiple ng-model="notice.user.receiveUserList" 
                                sortable="true" ng-disabled="true"
                                close-on-select="false" required >
                                <ui-select-match placeholder="请选择..." >{{$item.userName}}</ui-select-match>
                                <ui-select-choices repeat="item in notice.users | filter: $select.search">
                                    <div ng-bind-html="item.userName | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>

				    <div class="form-group">
					    <label class="col-sm-3 control-label">接收部门：</label>
					    <div class="col-sm-7">
						    <ui-select register-custom-form-control
							    ng-model="notice.user.department" theme="bootstrap"  ng-disabled="true">
						        <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
						        <ui-select-choices repeat="item in notice.departments | filter: $select.search">
						            <div ng-bind-html="item.name | highlight: $select.search"></div>
						        </ui-select-choices> 
						    </ui-select>
					    </div>
				    </div>

					<div class="form-group">
						<label class="col-sm-3 control-label">公告内容：</label>

						<div class="col-sm-7">
							<textarea ng-disabled="true" class="form-control" id="maxlength_textarea"
								placeholder="请输入公告" rows="10" ng-model="notice.user.content"
								maxlength="1000" required></textarea>
						</div>
					</div>

					<div class="form-group">
						<label class="col-sm-3 control-label">备注：</label>
						<div class="col-sm-7">
							<textarea ng-disabled="true" class="form-control" id="maxlength_remark" rows="5"
								ng-model="notice.user.remark" maxlength="1000"></textarea>
						</div>
					</div>
				</form>
			</div>
		</div>
</div>
</tab>





 </tabset>

<div class="row">
	<div class="col-md-12">
		<div id="static" class="modal fade" tabindex="-1"
			data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true"></button>
						<h4 class="modal-title">
							提示
						</h4>
					</div>
					<div class="modal-body">
						<p>
							{{notice.message}}
						</p>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn btn-default">ok</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="staticRemove" class="modal fade" tabindex="-1"
			data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true"></button>
						<h4 class="modal-title">
							删除公告
						</h4>
					</div>
					<div class="modal-body">
						<p>
						<div class="col-sm-1 text-center">
							<i class="fa fa-warning font-yellow"
								style="font-size: 30px !important;"></i>
						</div>
							确认删除该公告吗？
						</p>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue"
							ng-click="notice.doDelUser()">确认</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="staticSendMessage" class="modal fade" tabindex="-1"
			data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true"></button>
						<h4 class="modal-title">
							发送消息
						</h4>
					</div>
					<div class="modal-body">
						<p>
						<div class="col-sm-1 text-center">
							<i class="fa fa-warning font-yellow"
								style="font-size: 30px !important;"></i>
						</div>
							确认给用户或部门发送消息？
						</p>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue"
							ng-click="notice.doSendMassage()">确认</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
