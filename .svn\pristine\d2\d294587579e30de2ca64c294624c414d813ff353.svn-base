package com.kyb.pcberp.modules.icloud.contract.pojo;

import com.kyb.pcberp.common.base.pojo.Icloud_BasePojo;

public class Icloud_GroupProcess extends Icloud_BasePojo
{
    private static final long serialVersionUID = 1L;

    private String saleId;

    private String econnmyId;

    private String factoryId;

    private String radio;

    private String groupUserId;

    private String saleName;

    private String econnmyName;

    private String factoryName;

    private String dbId;

    private String groupDbName;

    public String getSaleId()
    {
        return saleId;
    }

    public void setSaleId(String saleId)
    {
        this.saleId = saleId;
    }

    public String getEconnmyId()
    {
        return econnmyId;
    }

    public void setEconnmyId(String econnmyId)
    {
        this.econnmyId = econnmyId;
    }

    public String getFactoryId()
    {
        return factoryId;
    }

    public void setFactoryId(String factoryId)
    {
        this.factoryId = factoryId;
    }

    public String getRadio()
    {
        return radio;
    }

    public void setRadio(String radio)
    {
        this.radio = radio;
    }

    public String getGroupUserId()
    {
        return groupUserId;
    }

    public void setGroupUserId(String groupUserId)
    {
        this.groupUserId = groupUserId;
    }

    public String getSaleName()
    {
        return saleName;
    }

    public void setSaleName(String saleName)
    {
        this.saleName = saleName;
    }

    public String getEconnmyName()
    {
        return econnmyName;
    }

    public void setEconnmyName(String econnmyName)
    {
        this.econnmyName = econnmyName;
    }

    public String getFactoryName()
    {
        return factoryName;
    }

    public void setFactoryName(String factoryName)
    {
        this.factoryName = factoryName;
    }

    public String getDbId()
    {
        return dbId;
    }

    public void setDbId(String dbId)
    {
        this.dbId = dbId;
    }

    public String getGroupDbName()
    {
        return groupDbName;
    }

    public void setGroupDbName(String groupDbName)
    {
        this.groupDbName = groupDbName;
    }
}
