<%@ page contentType="text/html;charset=UTF-8" %>
<script>var ctx = "<%=request.getContextPath()%>"</script>
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">统计报表</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="report.reportScanWork">工序待产报表</a>
        </li>
    </ul>
</div>
<tabset class="tabset-margin-top"> 
  <tab heading="工序待产报表" active="reportScanWorkCtrl.tabs.viewForm.active">
  
  
  <div>
	<div class="row align-items-center" style="color: #333;">
		<div class="col-sm">
			<div id="wipWaitReport" style="height:200px;"></div>
		</div>
	</div>
	<div class="row align-items-center pt-1 alert-light" style="color: #333;">
		<div class="col-md text-center">
			<!--<button class="btn btn-secondary"  ng-click="reportScanWorkCtrl.routerCoutList()">我的过数</button>-->
			<!-- <button class="btn btn-secondary" v-on:click="testCount">测试过数</button> -->
		</div>
	</div>
	<div style="padding-top: 10rem;"></div>
	</div>
  </tab>
  
    <tab active="reportScanWorkCtrl.tabs.detailForm.active" ng-show="reportScanWorkCtrl.tabs.detailForm.show"> 
        <tab-heading>详情<i style="cursor: pointer" class="fa fa-times" ng-click="reportScanWorkCtrl.hideDetailForm()"></i></tab-heading>
		    <div class="panel panel-default">
	            <div class="panel-heading font-blue-hoki">查询</div>
	            <div class="panel-body">
	                <form class="form-horizontal">
	                
	                    <div class="row" id="step6">
	                        <div class="col-md-6 col-lg-4">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">厂编：</label>
	                                <div class="col-sm-7 col-md-8">
	                                    <input type="text" class="form-control"
	                                           ng-model="reportScanWorkCtrl.detailQuery.craftNo"
	                                           disable-valid-styling="true" disable-invalid-styling="true" />
	                                </div>
	                            </div>
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">工序：</label>
	                                <div class="col-sm-7 col-md-8">
	                                    <input type="text" class="form-control"
	                                           ng-model="reportScanWorkCtrl.detailQuery.process"
	                                           disable-valid-styling="true" disable-invalid-styling="true" />
	                                </div>
	                            </div>
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">状态：</label>
	                                <div class="col-sm-7 col-md-8">
		                                <select class="form-control" ng-model="reportScanWorkCtrl.detailQuery.madeStatus" disable-auto-validate="true">
		                                    	<option value="">待产</option>
											<option value="1">生产中</option>
											<option value="2">所有</option>
		                                </select>
	                                </div>
	                            </div>

	                        </div>
	                        </div>
	                    <div class="row">
	                        <div class="col-sm-12">
	                            <button class="btn btn-default btn-default-width pull-right"
	                                    ng-click="reportScanWorkCtrl.doDetailQuery()">
	                                <i class="fa fa-search"></i> 查&nbsp;询
	                            </button>
	                        </div>
	                    </div>
	                </form>
	                
	                
	                
	            </div>
	       	
	       	</div>
	       	
	       	 <div class="portlet light bordered" id="stepDetail3">
	       	 	 <div class="portlet-title">
                                <div class="caption font-blue-hoki">
                                	<h4 class="modal-title">
	                                	<span class="text-primary">
				                        	明细列表
				                        </span>
				                        <span>(累积面积:{{reportScanWorkCtrl.allArea}}&nbsp;㎡&emsp;累积款数:
										<span ng-if="reportScanWorkCtrl.scanWorkDetails && reportScanWorkCtrl.scanWorkDetails.length > 0">{{reportScanWorkCtrl.scanWorkDetails.length}}</span>
										<span ng-if="!reportScanWorkCtrl.scanWorkDetails || !reportScanWorkCtrl.scanWorkDetails.length > 0">0</span>
										)</span>
			                        </h4>
                                </div>
                                <div class="actions">
                                    <div class="portlet-input input-inline input-small">
				                        <form action="a/report/reportScanWorkData/export" method="post" enctype="multipart/form-data" target="hidden_frame">
				                            <input type="text" ng-show="false" name="craftNo" value="{{reportScanWorkCtrl.detailQuery.craftNo}}" /> 
				                            <input type="text" ng-show="false" name="processName" value="{{reportScanWorkCtrl.detailQuery.process}}" /> 
				                            <input type="text" ng-show="false" name="madeStatus" value="{{reportScanWorkCtrl.detailQuery.madeStatus}}" /> 
				                             <input type="text" ng-show="false" name="xIndex" value="{{reportScanWorkCtrl.xIndex}}" /> 
				                              <input type="text" ng-show="false" name="reportFlag" value="{{reportScanWorkCtrl.reportFlag}}" /> 
				                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出明细</button>
				                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
				                        </form>
				                    </div>
                 				</div>
	       	 	<div class="portlet-body">
                                <table style="margin-top: 5px;" class="table table-striped table-bordered table-condensed table-advance table-hover">
                                    <thead>
                                    <tr class="heading">
                                        <th>厂编</th>
                                        <th>生产批次</th>
                                        <th>面积</th>
                                        <th>交期</th>
                                        <th>上道工序</th>
                                        <th>交板</th>
                                        <th>转板时间</th>
                                        <th>当前工序</th>
                                        <th>接板</th>
                                        <th>加工时间</th>
                                        
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat="detail in reportScanWorkCtrl.scanWorkDetails" >
                                          <td ng-class="{'danger': detail.isDanger(detail)}" ng-bind="detail.craftNo"></td>
                                          <td ng-class="{'danger': detail.isDanger(detail)}" ng-bind="detail.batchId"></td>
                                          <td ng-class="{'danger': detail.isDanger(detail)}" ng-bind="detail.batchArea"></td>
                                          <td ng-class="{'danger': detail.isDanger(detail)}" ng-bind="detail.deliveryDateStr"></td>
                                         <!--  <td ng-if="!detail.oueDayFlag || detail.oueDayFlag != '1'" ng-class="{'danger': detail.isDanger(detail)}" ng-bind="detail.deliveryDateStr"></td>--> 
                                          <td ng-class="{'danger': detail.isDanger(detail)}" ng-bind="detail.lastCategory"></td>
                                          <td ng-class="{'danger': detail.isDanger(detail)}" ng-bind="detail.lastHandTimeStr"></td>
                                          <td ng-if="detail.waitHoursStr" ng-class="{'danger': detail.isDanger(detail)}" ng-bind="detail.waitHoursStr"></td>
                                          <td ng-if="!detail.waitHoursStr" ng-class="{'danger': detail.isDanger(detail)}" > 1分钟</td>
                                          <td ng-class="{'danger': detail.isDanger(detail)}" ng-bind="detail.category"></td>
                                          <td ng-if="detail.takeOverTimeStr" ng-class="{'danger': detail.isDanger(detail)}" ng-bind="detail.takeOverTimeStr"></td>
                                          <td ng-if="!detail.takeOverTimeStr" ng-class="{'danger': detail.isDanger(detail)}" > 无</td>
                                          <td ng-if="detail.madeHoursStr" ng-class="{'danger': detail.isDanger(detail)}" ng-bind="detail.madeHoursStr"></td>
                                          <td ng-if="!detail.madeHoursStr && item.takeOverTimeStr" ng-class="{'danger': detail.isDanger(detail)}" ng-bind="detail.madeHoursStr"> 1分钟</td>
                                          <td ng-if="!detail.madeHoursStr && !item.takeOverTimeStr" ng-class="{'danger': detail.isDanger(detail)}" > 无</td>                                          
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
	       	 
	       	 </div>
	       	
	       	</div>
	</tab>

    <tab active="reportScanWorkCtrl.tabs.scanForm.active" ng-show="reportScanWorkCtrl.tabs.scanForm.show"> 
        <tab-heading>我的过数<i style="cursor: pointer" class="fa fa-times" ng-click="reportScanWorkCtrl.hideScanForm()"></i></tab-heading>
		    <div class="panel panel-default">
	            <div class="panel-heading font-blue-hoki">查询</div>
	            <div class="panel-body">
	                <form class="form-horizontal1">
	                
	                    <div class="row" id="step6">
	                        <div class="col-md-6 col-lg-4">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">厂编：</label>
	                                <div class="col-sm-7 col-md-8">
	                                    <input type="text" class="form-control"
	                                           ng-model="reportScanWorkCtrl.scanQuery.craftNo"
	                                           disable-valid-styling="true" disable-invalid-styling="true" />
	                                </div>
	                            </div>
	                        </div>
	                      </div>
	                      <div class="row">
	                           <div class="col-md-6 col-lg-4">
	                            <div class="form-group">
	                                <div class="row align-items-center pt-1 alert-light border-bottom" style="color: #333;font-size: 1rem;">
									<div class="col-md">
										<span>&nbsp;&nbsp;&nbsp;&nbsp;今日累积产量:&nbsp;{{reportScanWorkCtrl.allScanPcs}}&nbsp;PCS&nbsp;/&nbsp;{{reportScanWorkCtrl.allScanArea}}&nbsp;㎡</span>
									</div>
								</div>
	                            </div>
	                        </div>
	                        </div>
	                      
	                    <div class="row">
	                        <div class="col-sm-12">
	                            <button class="btn btn-default btn-default-width pull-right"
	                                    ng-click="reportScanWorkCtrl.doScanQuery()">
	                                <i class="fa fa-search"></i> 查&nbsp;询
	                            </button>
	                        </div>
	                    </div>
	                </form>
	                
	            </div>
	       	
	       	 <div class="portlet light bordered" id="stepDetail3">
	       	 	 <div class="portlet-title">
                                <div class="caption font-blue-hoki">过数列表</div>
                                <div class="actions">
                                    <div class="portlet-input input-inline input-small">
				                        <form action="a/report/reportScanWorkData/exportScan" method="post" enctype="multipart/form-data" target="hidden_frame">
				                            <input type="text" ng-show="false" name="craftNo" value="{{reportScanWorkCtrl.scanQuery.craftNo}}" /> 
				                             <input type="text" ng-show="false" name="xIndex" value="{{reportScanWorkCtrl.xIndex}}" /> 
				                              <input type="text" ng-show="false" name="reportFlag" value="{{reportScanWorkCtrl.reportFlag}}" /> 
				                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出明细</button>
				                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
				                        </form>
				                    </div>
                 </div>
	       	 	<div class="portlet-body">
                                <table style="margin-top: 5px;" class="table table-striped table-bordered table-condensed table-advance table-hover">
                                    <thead>
                                    <tr class="heading">
                                        <th>厂编</th>
                                        <th>PCS</th>
                                        <th>报废</th>
                                        <th>接板时间</th>
                                        <th>交板时间</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    
                                    <tr ng-repeat="item in reportScanWorkCtrl.coutList" >
                                        <td ng-class="{'danger': danger.isDanger(item)}" class="text-right" ><span>{{item.craftNo}}({{item.process.category}})</span></td>
                                        <td ng-class="{'danger': danger.isDanger(item)}" class="text-right" ng-bind="item.takeOverQtyPcsT"></td>
                                        <td ng-class="{'danger': danger.isDanger(item)}" class="text-right" ><span>{{Number(item.handOverQtyPcsT) - Number(item.takeOverQtyPcsT)}}</span></td>
                                        <td ng-class="{'danger': danger.isDanger(item)}" class="text-right" ng-bind="item.takeOverTimeStr" ></td>
                                        <td ng-class="{'danger': danger.isDanger(item)}" class="text-right" ng-bind="item.handOverTimeStr"></td>
                                    </tr>
                                    </tbody>
                                </table>
                 </div>
	       	 
	       	 </div>
	       	
	       	</div>
	</tab>

</tabset>

