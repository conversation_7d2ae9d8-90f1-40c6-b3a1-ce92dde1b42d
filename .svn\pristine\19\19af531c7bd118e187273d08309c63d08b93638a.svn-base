/* Setup general page controller */
kybApp.controller('qualityRecordCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil', 'BaseUtil', function ($rootScope, $scope, upida, $timeout, CommonUtil, BaseUtil) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();
        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });

    var vm = this;
    
    vm.help = function(){
    	if (vm.tabs.viewForm.active){
    		vm.helpDiscard();
    	} else if (vm.tabs.reworkForm.active) {
    		vm.helpRework();
    	} else {
    		vm.helpBad();
    	}
    };
    
    vm.shouldAutoStart = false;
    vm.introDiscardOptions = {
        steps:[
        {
            element: '#step1',
            intro: "当您打开页面后会自动加载报废记录信息至列表！",
            position: 'top'
        },
        {
            element: '#step2',
            intro: "单击右边的排序按钮进行排序，箭头向上为升序，箭头向下为降序，若为上下则未排序！",
            position: 'bottom'
        },
        {
            element: '#step3',
            intro: '此处为分页区域，您可以点击您想查看的页面！',
            position: 'top'
        },
        {
            element: '#step4',
            intro: "此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！",
            position: 'top'
        },
        {
            element: '#step5',
            intro: '此处为查询条件，可设置不同的条件，点击右下角的查询来过滤信息！',
            position: 'bottom'
        },
        {
            element: '#step6',
            intro: '操作按钮区域，导出报废记录！',
            position: 'left'
        },
        {
            element: '#step7',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
    
    vm.introReworkOptions = {
        steps:[
        {
            element: '#stepR1',
            intro: "当您打开页面后会自动加载返工记录信息至列表！",
            position: 'top'
        },
        {
            element: '#stepR2',
            intro: "单击右边的排序按钮进行排序，箭头向上为升序，箭头向下为降序，若为上下则未排序！",
            position: 'bottom'
        },
        {
            element: '#stepR3',
            intro: '此处为分页区域，您可以点击您想查看的页面！',
            position: 'top'
        },
        {
            element: '#stepR4',
            intro: "此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！",
            position: 'top'
        },
        {
            element: '#stepR5',
            intro: '此处为查询条件，可设置不同的条件，点击右下角的查询来过滤信息！',
            position: 'bottom'
        },
        {
            element: '#stepR6',
            intro: '操作按钮区域，导出返工记录！',
            position: 'left'
        },
        {
            element: '#stepR7',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
    
    vm.introBadOptions = {
        steps:[
        {
            element: '#stepB1',
            intro: "当您打开页面后会自动加载不良记录信息至列表！",
            position: 'top'
        },
        {
            element: '#stepB2',
            intro: "单击右边的排序按钮进行排序，箭头向上为升序，箭头向下为降序，若为上下则未排序！",
            position: 'bottom'
        },
        {
            element: '#stepB3',
            intro: '此处为分页区域，您可以点击您想查看的页面！',
            position: 'top'
        },
        {
            element: '#stepB4',
            intro: "此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！",
            position: 'top'
        },
        {
            element: '#stepB5',
            intro: '此处为查询条件，可设置不同的条件，点击右下角的查询来过滤信息！',
            position: 'bottom'
        },
        {
            element: '#stepB6',
            intro: '操作按钮区域，导出不良记录！',
            position: 'left'
        },
        {
            element: '#stepB7',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
    
    vm.typeList = [
                   {"name":"所有 ","value":""},
                   {"name":"生产检测 ","value":1},
                   {"name":"客诉检测","value":2},
                   {"name":"修复检测","value":3}
                   ];
    
    vm.queryAll = false;

    // 分页数据
    vm.pageOfBad = {};
    // 显示数据大小
    vm.pageOfBad.pageSizeOptions = [5, 10, 30, 50];
    // 字典项分页数据
    vm.pageOfBad.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.pageOfBad.pageSize = 10;
    vm.pageOfBad.pageNo = 1;
    vm.pageOfBad.url = "quality/qualityRecord/pageshowbad";
    vm.pageOfBad.condition = []; // 条件

    // 时间范围的选项
    vm.rangeOptionsOfBad = {
        //format: "YYYY-MM-DD",
        startDate: new Date((new Date).setMonth(((new Date).getMonth() - 1))),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
    };

    // 时间范围的Model
    vm.timeOfBad = {
        start: {},
        end: {}
    };
    
    // 查询条件参数
    vm.queryOfBad = {}; // 查询对象

    vm.queryOfBad.proNo = {};
    vm.queryOfBad.proNo.name = "proNo";
    vm.queryOfBad.proNo.value = "";
    vm.queryOfBad.testType = {};
    vm.queryOfBad.testType.name = "testType";
    vm.queryOfBad.testType.value = 0;
    vm.queryOfBad.batch = {};
    vm.queryOfBad.batch.name = "batch";
    vm.queryOfBad.batch.value = "";
    vm.queryOfBad.startDate = {};
    vm.queryOfBad.startDate.name = "startDate";
    vm.queryOfBad.startDate.value = "";
    vm.queryOfBad.endDate = {};
    vm.queryOfBad.endDate.name = "endDate";
    vm.queryOfBad.endDate.value = "";
    
    vm.initDate2=function(date)
	{
		 if(date==""){
			 vm.rangeOptionsOfBad = {
		        //format: "YYYY-MM-DD",
		    	startDate: new Date(vm.queryOfBad.startDate.value),
		    	minDate:new Date(new Date(vm.queryOfBad.endDate.value).setFullYear(new Date(vm.queryOfBad.endDate.value).getFullYear()-5))
			 };
			 vm.timeOfBad= {
		       start: vm.rangeOptions.startDate,
				 end: vm.rangeOptions.minDate
			 } 
		 }
	}
    
    // 排序字段
    vm.queryOfBad.sort = {};
    vm.queryOfBad.sort.name = "orderBy";
    vm.queryOfBad.sort.value = "a.checkDate DESC";
    
    // 默认排序按时间排序排序处理
    vm.sortOfBad = {};
    vm.sortOfBad.producePcsQty = {both: true, desc: false, asc: false};
    vm.sortOfBad.checkDate = {both: false, desc: true, asc: false};
    vm.sortOfBad.produceBatchNo = {both: true, desc: false, asc: false};
    vm.sortOfBad.produceNo = {both: true, desc: false, asc: false};
    vm.sortOfBad.processName = {both: true, desc: false, asc: false};

    vm.sortClickOfBad = function (col) {
        vm.sortOfBad[col].both = false;
        if (!vm.sortOfBad[col].desc && !vm.sortOfBad[col].asc) {
            vm.sortOfBad[col].asc = true;
        } else {
            if (vm.sortOfBad[col].asc) {
                vm.sortOfBad[col].desc = true;
                vm.sortOfBad[col].asc = false;
            } else {
                vm.sortOfBad[col].desc = false;
                vm.sortOfBad[col].asc = true;
            }
        }

        for (var p in vm.sortOfBad) {
            if (p !== col) {
                vm.sortOfBad[p].desc = false;
                vm.sortOfBad[p].asc = false;
                vm.sortOfBad[p].both = true;
            }
        }
        if (col == "produceBatchNo") {
            vm.queryOfBad.sort.value = vm.sortOfBad[col].asc ? "ppb.no ASC" : "ppb.no DESC";
        } else if (col == "produceNo") {
            vm.queryOfBad.sort.value = vm.sortOfBad[col].asc ? "n.craftNo ASC" : "n.craftNo DESC";
        } else if (col == "processName") {
            vm.queryOfBad.sort.value = vm.sortOfBad[col].asc ? "eg.name ASC" : "eg.name DESC";
        } else {
            vm.queryOfBad.sort.value = vm.sortOfBad[col].asc ? col + " ASC" : col + " DESC";
        }
        vm.init(vm.pageOfBad.pageNo, vm.pageOfBad.pageSize, vm.pageOfBad.condition, vm.pageOfBad.url, 2);
    };

    vm.doPageOfBad = function (page, pageSize, total) {
    	vm.pageOfBad.pageNo = page;
    	vm.pageOfBad.pageSize = pageSize;
    	vm.init(page, pageSize, vm.pageOfBad.condition, vm.pageOfBad.url, 2);
    };
    
    vm.selectBad = function(){
    	var query = false;
    	if (!vm.pageOfBad.data || !vm.pageOfBad.data.list || vm.pageOfBad.data.list.length <= 0){
    		query = true;
    	}
    	
    	if (query){
    		if (vm.timeOfBad.start) {
    			vm.pageOfBad.condition.push({
                    name: vm.queryOfBad.startDate.name,
                    value: vm.timeOfBad.start.valueOf()
                });
    			vm.queryOfBad.startDate.value=vm.timeOfBad.start.valueOf();
            }
    		
            if (vm.timeOfBad.end) {
            	vm.pageOfBad.condition.push({
                    name: vm.queryOfBad.endDate.name,
                    value: vm.timeOfBad.end.valueOf()
                });
            	vm.queryOfBad.endDate.value=vm.timeOfBad.end.valueOf();
            }
    		vm.init(1, vm.pageOfBad.pageSize, vm.pageOfBad.condition, vm.pageOfBad.url, 2);
    	}
    };
    
        // 查询数据
    vm.doQueryOfBad = function () {
        // 设置查询条件
        var condition = [];
        if (vm.queryOfBad.proNo.value || vm.queryOfBad.batch.value) {
            var produceBatch = {};
            
            if(vm.queryOfBad.proNo.value){
            	produceBatch.notification = {};
            	produceBatch.notification.craftNo = vm.queryOfBad.proNo.value;
                condition.push({
                    name: "produceBatch",
                    value: produceBatch
                });
            }
            
            if(vm.queryOfBad.batch.value){
            	produceBatch.feed = {};
            	produceBatch.feed.no=vm.queryOfBad.batch.value;
            	condition.push({
                    name: "produceBatch",
                    value: produceBatch
                });
            }
        }

        if (vm.queryOfBad.testType.value && vm.queryOfBad.testType.value != 0) {
            condition.push({
                name: "manufType",
                value: vm.queryOfBad.testType.value
            });
        }
        
        if (vm.timeOfBad.start) {
			condition.push({
                name: vm.queryOfBad.startDate.name,
                value: vm.timeOfBad.start.valueOf()
            });
			vm.queryOfBad.startDate.value=vm.timeOfBad.start.valueOf();
        }
		
        if (vm.timeOfBad.end) {
        	condition.push({
                name: vm.queryOfBad.endDate.name,
                value: vm.timeOfBad.end.valueOf()
            });
        	vm.queryOfBad.endDate.value=vm.timeOfBad.end.valueOf();
        }

        vm.pageOfBad.pageNo = 1;
        vm.pageOfBad.condition = condition;
        
        // 查询数据
        vm.init(vm.pageOfBad.pageNo, vm.pageOfBad.pageSize, vm.pageOfBad.condition, vm.pageOfBad.url, 2);
    };
    // --
    
    // 分页数据
    vm.pageOfShow = {};
    // 显示数据大小
    vm.pageOfShow.pageSizeOptions = [5, 10, 30, 50];
    // 字典项分页数据
    vm.pageOfShow.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.pageOfShow.pageSize = 10;
    vm.pageOfShow.pageNo = 1;
    vm.pageOfShow.url = "quality/qualityRecord/pageshow";
    vm.pageOfShow.condition = []; // 条件

    // 时间范围的选项
    vm.rangeOptionsOfShow = {
        //format: "YYYY-MM-DD",
        startDate: new Date((new Date).setMonth(((new Date).getMonth() - 1))),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
    };

    // 时间范围的Model
    vm.timeOfShow = {
        start: {},
        end: {}
    };
    
    // 查询条件参数
    vm.queryOfShow = {}; // 查询对象

    vm.queryOfShow.proNo = {};
    vm.queryOfShow.proNo.name = "proNo";
    vm.queryOfShow.proNo.value = "";
    vm.queryOfShow.testType = {};
    vm.queryOfShow.testType.name = "testType";
    vm.queryOfShow.testType.value = 0;
    vm.queryOfShow.batch = {};
    vm.queryOfShow.batch.name = "batch";
    vm.queryOfShow.batch.value = "";
    vm.queryOfShow.startDate = {};
    vm.queryOfShow.startDate.name = "startDate";
    vm.queryOfShow.startDate.value = "";
    vm.queryOfShow.endDate = {};
    vm.queryOfShow.endDate.name = "endDate";
    vm.queryOfShow.startDate.value = "";
    
    vm.initDate1=function(date)
	{
		 if(date==""){
			 vm.rangeOptionsOfShow = {
		        //format: "YYYY-MM-DD",
		    	startDate: new Date(vm.queryOfShow.startDate.value),
		    	minDate:new Date(new Date(vm.queryOfShow.startDate.value).setFullYear(new Date(vm.queryOfShow.startDate.value).getFullYear()-5))
			 };
			 vm.timeOfShow= {
		       start: vm.rangeOptions.startDate,
				 end: vm.rangeOptions.minDate
			 } 
		 }
	}
    
    // 排序字段
    vm.queryOfShow.sort = {};
    vm.queryOfShow.sort.name = "orderBy";
    vm.queryOfShow.sort.value = "a.checkDate DESC";
    
    // 默认排序按时间排序排序处理
    vm.sortOfShow = {};
    vm.sortOfShow.producePcsQty = {both: true, desc: false, asc: false};
    vm.sortOfShow.checkDate = {both: false, desc: true, asc: false};
    vm.sortOfShow.produceBatchNo = {both: true, desc: false, asc: false};
    vm.sortOfShow.produceNo = {both: true, desc: false, asc: false};
    vm.sortOfShow.processName = {both: true, desc: false, asc: false};

    vm.sortClickOfShow = function (col) {
        vm.sortOfShow[col].both = false;
        if (!vm.sortOfShow[col].desc && !vm.sortOfShow[col].asc) {
            vm.sortOfShow[col].asc = true;
        } else {
            if (vm.sortOfShow[col].asc) {
                vm.sortOfShow[col].desc = true;
                vm.sortOfShow[col].asc = false;
            } else {
                vm.sortOfShow[col].desc = false;
                vm.sortOfShow[col].asc = true;
            }
        }

        for (var p in vm.sortOfShow) {
            if (p !== col) {
                vm.sortOfShow[p].desc = false;
                vm.sortOfShow[p].asc = false;
                vm.sortOfShow[p].both = true;
            }
        }
        if (col == "produceBatchNo") {
            vm.queryOfShow.sort.value = vm.sortOfShow[col].asc ? "ppb.no ASC" : "ppb.no DESC";
        } else if (col == "produceNo") {
            vm.queryOfShow.sort.value = vm.sortOfShow[col].asc ? "n.craftNo ASC" : "n.craftNo DESC";
        } else if (col == "processName") {
            vm.queryOfShow.sort.value = vm.sortOfShow[col].asc ? "eg.name ASC" : "eg.name DESC";
        } else {
            vm.queryOfShow.sort.value = vm.sortOfShow[col].asc ? col + " ASC" : col + " DESC";
        }
        vm.init(vm.pageOfShow.pageNo, vm.pageOfShow.pageSize, vm.pageOfShow.condition, vm.pageOfShow.url, 1);
    };

    vm.doPageOfShow = function (page, pageSize, total) {
    	vm.pageOfShow.pageNo = page;
    	vm.pageOfShow.pageSize = pageSize;
    	vm.init(page, pageSize, vm.pageOfShow.condition, vm.pageOfShow.url, 1);
    };
    
    vm.selectFinish = function(){
    	var query = false;
    	if (!vm.pageOfShow.data || !vm.pageOfShow.data.list || vm.pageOfShow.data.list.length <= 0){
    		query = true;
    	}
    	
    	if (query){
    		if (vm.timeOfShow.start) {
    			vm.pageOfShow.condition.push({
                    name: vm.queryOfShow.startDate.name,
                    value: vm.timeOfShow.start.valueOf()
                });
    			vm.queryOfShow.startDate.value=vm.timeOfShow.start.valueOf();
            }
    		
            if (vm.timeOfShow.end) {
            	vm.pageOfShow.condition.push({
                    name: vm.queryOfShow.endDate.name,
                    value: vm.timeOfShow.end.valueOf()
                });
            	vm.queryOfShow.endDate.value=vm.timeOfShow.end.valueOf();
            }
    		vm.init(1, vm.pageOfShow.pageSize, vm.pageOfShow.condition, vm.pageOfShow.url, 1);
    	}
    };
    
        // 查询数据
    vm.doQueryOfShow = function () {
        // 设置查询条件
        var condition = [];
        if (vm.queryOfShow.proNo.value || vm.queryOfShow.batch.value) {
            var produceBatch = {};
            
            if(vm.queryOfShow.proNo.value){
            	produceBatch.notification = {};
            	produceBatch.notification.craftNo = vm.queryOfShow.proNo.value;
                condition.push({
                    name: "produceBatch",
                    value: produceBatch
                });
            }
            
            if(vm.queryOfShow.batch.value){
            	produceBatch.feed = {};
            	produceBatch.feed.no=vm.queryOfShow.batch.value;
            	condition.push({
                    name: "produceBatch",
                    value: produceBatch
                });
            }
        }

        if (vm.queryOfShow.testType.value && vm.queryOfShow.testType.value != 0) {
            condition.push({
                name: "manufType",
                value: vm.queryOfShow.testType.value
            });
        }
        
        if (vm.timeOfShow.start) {
            condition.push({
                name: vm.queryOfShow.startDate.name,
                value: vm.timeOfShow.start.valueOf()
            });
            vm.queryOfShow.startDate.value=vm.timeOfShow.start.valueOf();
        }

        if (vm.timeOfShow.end) {
            condition.push({
                name: vm.queryOfShow.endDate.name,
                value: vm.timeOfShow.end.valueOf()
            });
            vm.queryOfShow.endDate.value=vm.timeOfShow.end.valueOf();
        }

        vm.pageOfShow.pageNo = 1;
        vm.pageOfShow.condition = condition;
        
        // 查询数据
        vm.init(vm.pageOfShow.pageNo, vm.pageOfShow.pageSize, vm.pageOfShow.condition, vm.pageOfShow.url, 1);
    };
    
    // --
    
    // 分页数据
    vm.page = {};
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    // 字典项分页数据
    vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "quality/qualityRecord/page";
    vm.page.condition = []; // 条件

    // 时间范围的选项
    vm.rangeOptions = {
        //format: "YYYY-MM-DD",
        startDate: new Date((new Date).setMonth(((new Date).getMonth() - 1))),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
    };

    // 时间范围的Model
    vm.time = {
        start: {},
        end: {}
    };
    
 // 时间范围的Model
    vm.madeTime = {
        start: {},
        end: {}
    };
    
    // 查询条件参数
    vm.query = {}; // 查询对象

    vm.query.proNo = {};
    vm.query.proNo.name = "proNo";
    vm.query.proNo.value = "";
    vm.query.testType = {};
    vm.query.testType.name = "testType";
    vm.query.testType.value = 0;
    vm.query.batch = {};
    vm.query.batch.name = "batch";
    vm.query.batch.value = "";
    vm.query.startDate = {};
    vm.query.startDate.name = "startDate";
    vm.query.startDate.value = "";
    vm.query.endDate = {};
    vm.query.endDate.name = "endDate";
    vm.query.endDate.value = "";
    vm.query.customerPo = {};
    vm.query.customerPo.name = "customerPo";
    vm.query.customerPo.value = "";
    
    vm.query.type = "";
    vm.initDate=function(date)
	{
		 if(date==""){
			 vm.rangeOptions = {
		        //format: "YYYY-MM-DD",
		    	startDate: new Date(vm.query.startDate.value),
		    	minDate:new Date(new Date(vm.query.endDate.value).setFullYear(new Date(vm.query.endDate.value).getFullYear()-5))
			 };
			 vm.time= {
		       start: vm.rangeOptions.startDate,
				 end: vm.rangeOptions.minDate
			 } 
		 }
	}
    
    // 排序字段
    vm.query.sort = {};
    vm.query.sort.name = "orderBy";
    vm.query.sort.value = "qitd.madeDate DESC";

    // tabs控制
    vm.tabs = {
        viewForm: {active: true},
        editForm: {active: false, show: false}
    };

    vm.menuDetectTypes = [];
    vm.menuDetectTypes.push({value: 0, name: "所有"});
    vm.menuDetectTypes.push({value: 1, name: "已交板"});
    vm.menuDetectTypes.push({value: 2, name: "未交板"});

    // 权限
    vm.right = {};

    // 默认排序按时间排序排序处理
    vm.sort = {};
    vm.sort.producePcsQty = {both: true, desc: false, asc: false};
    vm.sort.checkDate = {both: false, desc: true, asc: false};
    vm.sort.produceBatchNo = {both: true, desc: false, asc: false};
    vm.sort.produceNo = {both: true, desc: false, asc: false};
    vm.sort.processName = {both: true, desc: false, asc: false};

    vm.sortClick = function (col) {
        vm.sort[col].both = false;
        if (!vm.sort[col].desc && !vm.sort[col].asc) {
            vm.sort[col].asc = true;
        } else {
            if (vm.sort[col].asc) {
                vm.sort[col].desc = true;
                vm.sort[col].asc = false;
            } else {
                vm.sort[col].desc = false;
                vm.sort[col].asc = true;
            }
        }

        for (var p in vm.sort) {
            if (p !== col) {
                vm.sort[p].desc = false;
                vm.sort[p].asc = false;
                vm.sort[p].both = true;
            }
        }
        if (col == "produceBatchNo") {
            vm.query.sort.value = vm.sort[col].asc ? "ppb.no ASC" : "ppb.no DESC";
        } else if (col == "produceNo") {
            vm.query.sort.value = vm.sort[col].asc ? "n.craftNo ASC" : "n.craftNo DESC";
        } else if (col == "processName") {
            vm.query.sort.value = vm.sort[col].asc ? "eg.name ASC" : "eg.name DESC";
        } else {
            vm.query.sort.value = vm.sort[col].asc ? col + " ASC" : col + " DESC";
        }
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url, 0);
    };

    // 分页按钮单击处理
    vm.doPage = function (page, pageSize, total) {
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        vm.init(page, pageSize, vm.page.condition, vm.page.url, 0);
    };
    
    /*
     * 兼容条件查询与普通查询
     * no 当前页
     * size 当前页显示数量
     * condition 是数组，可放入多个条件对象，如{name:"type", value:"1"}  名称对应实体字段名称，值对应页面输入的值
     * url 请求数据链接
     */
    vm.init = function (no, size, condition, url, type) {
    	MainCtrl.blockUI({
            animate: true,
        });
    	
        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;
        
        if (type == 0){
        	condition.push({
        		name: vm.query.sort.name,
        		value: vm.query.sort.value
        	});
        } else if (type == 1){
        	condition.push({
        		name: vm.queryOfShow.sort.name,
        		value: vm.queryOfShow.sort.value
        	});
        } else {
        	condition.push({
        		name: vm.queryOfBad.sort.name,
        		value: vm.queryOfBad.sort.value
        	});
        }

        // 设置过滤条件
        if (condition.length > 0) {
            angular.forEach(condition, function (p) {
                reqData[p.name] = p.value;
            });
        }
        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};
            // 如果结果为空
            if (typeof result === 'undefined' || typeof result.list === 'undefined') {

                data.pageNo = 1;
                data.pageSize = 10;
                data.list = [];
                data.startCount = 0;
                data.endCount = 0;
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }
            
            if (type == 0){
            	vm.page.data = data;
            } else if (type == 1){
            	vm.pageOfShow.data = data;
            } else {
            	vm.pageOfBad.data = data;
            }
            MainCtrl.unblockUI();
        });
    };

    // 页面显示数量改变
    vm.pageSizeChange = function (type) {
    	if (type == 0) {
    		vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url, 0);
    	} else if (type == 1) {
    		vm.init(1, vm.pageOfShow.pageSize, vm.pageOfShow.condition, vm.pageOfShow.url, 1);
    	} else {
    		vm.init(1, vm.pageOfBad.pageSize, vm.pageOfBad.condition, vm.pageOfBad.url, 2);
    	}
    };

    // 查询数据
    vm.doQuery = function () {
        // 设置查询条件
        var condition = [];
        if (vm.query.proNo.value || vm.query.batch.value) {
            var produceBatch = {};
            
            if(vm.query.proNo.value){
            	produceBatch.notification = {};
            	produceBatch.notification.craftNo = vm.query.proNo.value;
                condition.push({
                    name: "produceBatch",
                    value: produceBatch
                });
            }
            
            if(vm.query.batch.value){
            	produceBatch.feed = {};
            	produceBatch.feed.no=vm.query.batch.value;
            	condition.push({
                    name: "produceBatch",
                    value: produceBatch
                });
            }
        }

        if (vm.query.testType.value && vm.query.testType.value != 0) {
            condition.push({
                name: "manufType",
                value: vm.query.testType.value
            });
        }
        
        if (vm.time.start) {
            condition.push({
                name: vm.query.startDate.name,
                value: vm.time.start.valueOf()
            });
            vm.query.startDate.value=vm.time.start.valueOf();
        }
        if (vm.time.end) {
            condition.push({
                name: vm.query.endDate.name,
                value: vm.time.end.valueOf()
            });
            vm.query.endDate.value=vm.time.end.valueOf();
        }
        
        if(vm.query.customerPo.value !== ""){
        	condition.push({
                name: vm.query.customerPo.name,
                value: vm.query.customerPo.value
            });
        }
        
        if(vm.query.rejectNo !== ""){
        	condition.push({
                name: "rejectNo",
                value: vm.query.rejectNo
            });
        }
        
        if(vm.query.type !== ""){
        	condition.push({
                name: "type",
                value: vm.query.type
            });
        }
        
        vm.page.pageNo = 1;
        vm.page.condition = condition;
        
        // 查询数据
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url, 0);
    };

    // 加载权限
    function loadRight() {
        //是否可以查看所有人的
        vm.queryAll = CommonUtil.dataRangeIsAll("10503", BaseUtil.getMenuList());
        
        MainCtrl.blockUI({
            animate: true,
        });
        
        upida.get("common/rightall?prefix=quality:record").then(function (data) {
            vm.right.view = data.view;
            vm.right.edit = data.edit;
            vm.right.manage = data.manage;

            vm.page.pageNo = 1;
            if (vm.timeOfShow.start) {
            	vm.page.condition.push({
                    name: vm.queryOfShow.startDate.name,
                    value: vm.timeOfShow.start.valueOf()
                });
            }
            if (vm.timeOfShow.end) {
            	vm.page.condition.push({
                    name: vm.queryOfShow.endDate.name,
                    value: vm.timeOfShow.end.valueOf()
                });
            }
            
            if (vm.time.start) {
            	vm.page.condition.push({
                    name: vm.query.startDate.name,
                    value: vm.time.start.valueOf()
                });
                vm.query.startDate.value=vm.time.start.valueOf();
            }
            if (vm.time.end) {
            	vm.page.condition.push({
                    name: vm.query.endDate.name,
                    value: vm.time.end.valueOf()
                });
                vm.query.endDate.value=vm.time.end.valueOf();
            }
            
            // 初始化第一页，条件为空
            vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url, 0);
        });
    }

    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
    });
}]);