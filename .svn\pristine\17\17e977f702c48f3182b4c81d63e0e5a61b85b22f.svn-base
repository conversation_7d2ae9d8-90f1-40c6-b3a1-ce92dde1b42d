/**
 * 
 */
package com.kyb.pcberp.modules.production.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.kyb.pcberp.modules.approval.vo.ContractDeailVo;
import com.kyb.pcberp.modules.eg.entity.CardA;
import com.kyb.pcberp.modules.production.entity.*;
import com.kyb.pcberp.modules.production.vo.ProduceSchedulingVo;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.sys.entity.Cockpit;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatAudit;
import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.sys.entity.Company;

/**
 * 批次明细DAO接口
 * 
 * <AUTHOR>
 * @version 2015-10-29
 */
@MyBatisDao
public interface ProduceBatchDetailDao extends CrudDao<ProduceBatchDetail>
{
    
    int deleteByProduceBatchRecordIdReal(ProduceBatch produceBatch);
    
    /**
     * 根据生产批次id查找批次明细
     * 
     * @param batchDetail
     * @return
     */
    List<ProduceBatchDetail> findProduceBatchDetail(ProduceBatchDetail batchDetail);
    
    List<ProduceBatchDetail> selectproduceBatchDetailByfeedingId(ProduceBatch produceBatch);
    
    /**
     * 根据批次编号批量删除批次明细
     * 
     * @param produceBatch
     */
    void deleteproduceBatchDetail(ProduceBatch produceBatch);
    
    int deleteByIdReal(ProduceBatchDetail produceBatchDetail);
    
    /**
     * 根据批次编号 查询批次明细
     * 
     * @param produceBatch
     * @return
     */
    List<ProduceBatchDetail> selectProduceBatchAndProduceBatchDetail(ProduceBatch produceBatch);
    
    /**
     * 查询该投料单产生多少个批次
     */
    Integer findBatchDetailByBatchIdCount(Feeding feeding);
    
    int updateQty(ProduceBatchDetail produceBatchDetail);
    
    /**
     * 更新报废数量
     * 
     * @param produceBatch
     * @return
     */
    int updateDiscardQtyById(ProduceBatchDetail produceBatchDetail);
    
    /**
     * 拆板保存批次明细
     * 
     * @param produceBatchDetail
     * @return
     */
    Integer saveProduceBatchDetail(ProduceBatchDetail produceBatchDetail);
    
    Integer updateExcreteRecordId(ProduceBatchDetail produceBatchDetail);
    
    int updateOfAdjust(ProduceBatchDetail produceBatchDetail);
    
    /**
     * 根据批次明细的编号去查询报废总数和返工总数
     * 
     * @param temp
     * @return
     */
    ProduceBatchDetail selectDiscardAndrework(ProduceBatchDetail temp);
    
    /**
     * 查询出所有未入库的批次明细
     * 
     * @param temp
     * @return
     */
    List<ProduceBatchDetail> findProduceBatchDetails(ProduceBatchDetail ppdSreach);
    
    List<ProduceBatchDetail> findProduceBatchDetailListInStock(Notification notifi);
    
    /***
     * 查询批次所有的批次明细
     */
    List<ProduceBatchDetail> findProduceBatchDetailsByPb(ProduceBatch ppdSreach);
    
    /**
     * 删除明细
     * 
     * @param recordId
     * @return
     */
    Integer deleteByProduceBatchRecordId(String recordId);
    
    void updateBatchDetailStatus(ProduceBatchDetail pdb);
    
    /**
     * 根据批次编号去查询批次明细数据
     * 
     * @param produceBatch
     * @return
     */
    List<ProduceBatchDetail> selectproduceBatchDetailByproduceBatch(ProduceBatch produceBatch);
    
    /**
     * 根据补料单编号 去查询批次明细数据
     * 
     * @param replenish
     * @return
     */
    List<ProduceBatchDetail> produceBatchDetailByproduceBatchId(Replenish replenish);
    
    /**
     * 根据批次编号去修改所有批次明细的状态
     * 
     * @param recordId
     * @return
     */
    Integer updateStatusByProduceBatchRecordId(String recordId);
    
    Integer updateStatus1ByProduceBatch(ProduceBatch produceBatch);
    
    void updateStatusByProduceBatch(ProduceBatch produceBatch);
    
    void updateQcStatFlagById(ProduceBatchDetail detail);
    
    /**
     * 根据批次明细no 和公司 and 批次编号去查询批次明细数据
     * 
     * @param detail
     * @return
     */
    ProduceBatchDetail selectDetailByNo(ProduceBatchDetail detail);
    
    /**
     * 根据批次编号去查询批次明细数据 add cq 2016-07-07
     * 
     * @param produceBatch
     * @return
     */
    List<ProduceBatchDetail> getProduceBatchDetailByProid(ProduceBatch produceBatch);
    
    List<ProduceBatchDetail> findBatchDetailByNotifId(Notification notification);
    
    List<ProduceRecord> fidProduceRecordIdByBatchDetailId(ProduceBatchDetail detail);
    
    /** tj 获取生产入库数据 */
    ProduceBatchDetail findProductionWarehousing(ProduceBatchDetail ppdSreach);
    
    /**
     * lq 2018-10-15 查询生产款数即批次明细个数当天
     */
    Integer selectDetailCount(Feeding feeding);
    
    /** zjn 2019-04-12 根据通知单获取批次明细 */
    List<ProduceBatchDetail> getBatchDetailByNotification(Notification notification);
    
    /** zjn 2019-04-16 获取待拆的批次明细列表 */
    List<ProduceBatchDetail> getListPage(ProduceBatchDetail pbd);
    
    /** zjn 2019-04-16 根据批次获取批次明细 */
    List<ProduceBatchDetail> getProduceBatchDetailListByBatch(ProduceBatch produceBatch);
    
    /** zjn 2019-04-18 批量添加批次明细 */
    void batchInsert(@Param("pbdList") List<ProduceBatchDetail> pbdList);
    
    /** zjn 2019-04-18 获取插入的拆卡批次明细 */
    List<ProduceBatchDetail> getNewInsertPbdList(ProduceBatchDetail pbd);
    
    /** zjn 2019-04-18 获取批量插入的第一个id */
    String getRecordId();
    
    /** zjn 2019-04-21 批量删除批次明细 */
    void deleteBatchPpbd(ProduceBatchDetail pbd);
    
    /** zjn 2019-05-09 获取所有的批次明细的状态 */
    List<ProduceBatchDetail> findBatchDetailStatusInfoByBatch(ProduceRecord produceRecord);
    
    /** zjn 2019-05-22 获取批次中批次明细的最大编号 */
    Integer getMaxPbdNo(ProduceBatchDetail pbd);
    
    /** zjn 2019-05-27 根据投料单获取批次明细 */
    List<ProduceBatchDetail> produceBatchDetailByFeedRecordId(Feeding feeding);
    
    /** zjn 2019-08-21 根据批次获取未生产完成的批次明细的个数 */
    Integer getPbdCountByPb(ProduceBatch produceBatch);
    
    /** fzd 2019-09-16 查询wip生产线 */
    List<ProduceBatchDetail> getWipList(ProduceRecord produceRecord);
    
    /** fzd 2019-09-18 获取过数明细 */
    List<ProduceRecord> getWipDeail(ProduceBatchDetail deail);
    
    List<ProduceRecord> getWipReportList(ProduceRecord record);
    
    List<ProduceRecord> getWipReport(ProduceRecord record);
    
    String getpbdStatusByNotiId(Notification notification);
    
    void updateFinshDate(ProduceBatchDetail deail);
    
    List<ProduceBatchDetail> getPbdListByNotiId(Notification notification);
    
    Integer getProductSortNum(Company company);
    
    Integer getNoFinishPbdCount(ProduceRecord produceRecord);
    
    void updateFinishAsDistri(Feeding feeding);
    
    void updatePbdOutStockData(Feeding feeding);

    void updatePbdOutStockDataTwo(@Param("productBatchDeailId") String productBatchDeailId);
    
    List<ProduceBatchDetail> getBatchDetailList(ProduceBatchDetail deail);
    
    ProduceBatchDetail getExpectDetail(ProduceBatchDetail deail);

    Integer checkAcDistributeDateCount(CardA cardA);

    List<ProduceBatchDetail> getProduceBatchDetailList(ProduceBatchDetail produceBatch);

    List<ProduceBatchDetail> getDetailArea(@Param("recordId") String recordId);

    //检验准备阶段是否都为已确认状态
    Integer selectExict(BatchDetailSetup batchDetailSetup);

    void updateDetail(BatchDetailSetup batchDetailSetup);

    void updateType(@Param("companyId") String companyId,@Param("feedId") String feedId,@Param("type") String type);

    List<ProduceBatchDetail> getBalanceDetailList(Company company);

    void updateStatus(@Param("cardId") String cardId,@Param("name") String name,@Param("status") String status);

    void updateDetailTwo(@Param("produceBatchDetailId") String produceBatchDetailId);

    void updateArrangeTime(@Param("recordId") String recordId,@Param("arrangeTime") Date arrangeTime);

    List<ProduceBatchDetail>showDeferredListDate(ProduceBatchDetail produceBatchDetail);

    void updateNoticStateDate(ProduceBatchDetail produceBatchDetail);

    ProduceBatchDetail getFeedStorgeDate(ProduceBatchDetail produceBatchDetail);

    List<ProduceBatchDetail> getDataBySaleDetailId(@Param("list") List<String> list);

    Integer feedingOutputCount(Notification notification);

    List<ProduceBatchDetail> getListByNoti(Notification notification);

    void updateLockSequence(ProduceBatchDetail produceBatchDetail);

    void updateOtherLockSequence(ProduceBatchDetail produceBatchDetail);

    void updateCurrentLockStatus(@Param("list") List<ProduceBatchDetail> list);

    void cleanCurrentLockStatus(Company company);

    void cancleLockSequence(ProduceBatchDetail produceBatchDetail);

    Integer getMaxLockSequence(Company company);

    void updateMaterialIssueDate(@Param("list") List<BottleneckProcessUse> list);

    Integer getSatisfyPbdStatus(@Param("recordId") String recordId);

    BigDecimal getSumCurrentLockStatusArea(Company company);

    void updateCurrentLockStatusTwo(@Param("recordId") String recordId);

    Integer getCurrentLockStatusSampleCount(Company company);

    List<String> getProductionPbdIdList(RawmaterialStock rawmaterialStock);

    Integer getCurrentLockStatus(@Param("recordId") String recordId);

    List<ProduceBatchDetail> getListByCardId(@Param("companyId") String companyId,@Param("cardId") String cardId);

    ProduceBatchDetail getProduceBatchDetail(ProduceBatchDetail produceBatchDetail);

    List<ProduceBatchDetail> getListByGroupCenterId(@Param("groupCenterId") String groupCenterId);

    List<ProduceSchedulingVo> getBlankingPbdList(Company company);

    List<ProduceBatchDetail> getTotalQtyPcsT(@Param("list") List<String> list);

    List<ProduceBatchDetail> getDataList(ProduceBatchDetail produceBatchDetail);

    List<String> getOldDetailList();

    List<ProduceSchedulingVo> getBlankingPbdTwoList(Company company);

    List<ContractDeailVo> getProcessList(@Param("recordId") String recordId);

    void updateApplyProcessIds(ProduceBatchDetail produceBatchDetail);
}