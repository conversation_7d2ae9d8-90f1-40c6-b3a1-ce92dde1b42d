<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.finance.dao.PayMuchMoneyDao">

	<sql id="payMuchMoneyColumns">
		a.recordid AS "recordId",
		a.companyid AS "company.recordId",
		a.supplierid AS "supplier.recordId",
		a.purchasingid AS "purchasing.recordId",
		a.payapplicationid AS "payApplication.recordId",
		a.period AS "period",
		a.paydate AS "payDate",
		a.amount AS "amount",
		a.billno AS "billNo",
		a.paymethod AS "payMethod",
		a.receiveaccount AS "receiveAccount",
		a.applyType AS "applyType",
		a.fundtype AS "fundType",
		a.responsibleperson AS "responsiblePerson.recordId",
		a.sourceid AS "sourceId",
		a.writeoffid AS "writeOffId",
		a.writeoffcause AS "writeOffCause",
		a.status AS "status",
		a.activeflag AS "activeFlag",
		a.createdby AS "createdBy.recordId",
		a.createddate AS "createdDate",
		a.lastupdby AS "lastUpdBy.recordId",
		a.lastupddate AS "lastUpdDate",
		a.remark AS "remark",
		a.bank AS "bank",
		a.payBank AS "payBank",
		a.payAccount AS "payAccount",
		a.dateForWriteOffPayMoney AS "dateForWriteOffPayMoney",
		a.payMoneyId AS "payMoney.recordId"
	</sql>
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO pu_pay_much_money(
			companyId,
			supplierId,
			purchasingId,
			payapplicationId,
			period,
			payDate,
			amount,
			billNo,
			payMethod,
			receiveAccount,
			applyType,
			fundType,
			responsiblePerson,
			sourceId,
			writeOffId,
			writeOffCause,
			status,
			activeFlag,
			createdby,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			remark,
			bank,
			payBank,
			payAccount,
			dateForWriteOffPayMoney,
			payMoneyId
		) VALUES (
			#{company.recordId},
			#{supplier.recordId},
			#{purchasing.recordId},
			#{payApplication.recordId},
			#{period},
			#{payDate},
			#{amount},
			#{billNo},
			#{payMethod},
			#{receiveAccount},
			#{applyType},
			#{fundType},
			#{responsiblePerson.recordId},
			#{sourceId},
			#{writeOffId},
			#{writeOffCause},
			#{status},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{bank},
			#{payBank},
			#{payAccount},
			#{dateForWriteOffPayMoney},
			#{payMoney.recordId}
		)
	</insert>
	
	<update id="update">
		UPDATE pu_pay_much_money SET 	
			supplierid = #{supplier.recordId},
			purchasingid = #{purchasing.recordId},
			payapplicationid = #{payApplication.recordId},
			period = #{period},
			paydate = #{payDate},
			amount = #{amount},
			billno = #{billNo},
			paymethod = #{payMethod},
			receiveaccount = #{receiveAccount},
			applyType = #{applyType},
			fundtype = #{fundType},
			responsibleperson = #{responsiblePerson.recordId},
			sourceid = #{sourceId},
			writeoffid = #{writeOffId},
			writeoffcause = #{writeOffCause},
			status = #{status},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate},
			remark = #{remark},
			bank = #{bank},
			payBank = #{payBank},
			payAccount = #{payAccount},
			dateForWriteOffPayMoney = #{dateForWriteOffPayMoney}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="get" resultType="PayMuchMoney">
		SELECT 
			<include refid="payMuchMoneyColumns"/>
		FROM pu_pay_much_money a
		WHERE a.recordId = #{recordId}
	</select>
	
	<!-- zjn 2019-03-01 获取分月收款记录列表 -->
	<select id="getPayMuchMoneyList" resultType="PayMuchMoney">
		SELECT 
			<include refid="payMuchMoneyColumns"/>,
			mc.name as "company.name",
			ms.no as "supplier.no",
			ms.name as "supplier.name",
			ms.shortName as "supplier.shortName",
			su.userName as  "responsiblePerson.userName"
		FROM pu_pay_much_money a
		LEFT JOIN md_company mc ON mc.recordId = a.companyId
		LEFT JOIN md_supplier ms ON ms.recordId = a.supplierId
		LEFT JOIN sm_user su on su.recordId = a.responsiblePerson
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = 1
			AND a.payMoneyId = #{recordId}
		</where>
	</select>
	
	<update id="delete">
		UPDATE pu_pay_much_money SET
			activeFlag = 2
		WHERE activeFlag = 1
		<if test="recordId != null and recordId != ''">
			AND recordId = #{recordId}
		</if>
		<if test="payMoney != null and payMoney.recordId != null and payMoney.recordId != ''">
			AND payMoneyId = #{payMoney.recordId}
		</if>
	</update>
	
</mapper>