package com.kyb.pcberp.modules.purch.web;

import com.kyb.pcberp.modules.purch.dao.SupplierDao;
import com.kyb.pcberp.modules.purch.entity.SupplierQuotation;
import com.kyb.pcberp.modules.purch.service.SupplierQuotationService;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "${adminPath}/purch/supplierQuotation")
public class SupplierQuotationController {

    @Autowired
    private SupplierQuotationService supplierQuotationService;

    @Autowired
    private SupplierDao supplierDao;

    @RequestMapping(value = "list")
    public String view()
    {
        return "modules/purch/supplierQuotation";
    }

    @RequestMapping(value = "loadSupplierQquotation", method = {RequestMethod.POST})
    @ResponseBody
    public List<SupplierQuotation>loadSupplierQquotation(@RequestBody SupplierQuotation supplierQuotation)
    {
        return supplierQuotationService.loadSupplierQquotation(supplierQuotation);
    }

    @RequestMapping(value = "delSupplierQuotation", method = {RequestMethod.POST})
    @ResponseBody
    public String delSupplierQuotation(@RequestBody SupplierQuotation supplierQuotation){

        return supplierQuotationService.delSupplierQuotation(supplierQuotation);
    }

    @RequestMapping(value = "saveSupplierQuotation", method = {RequestMethod.POST})
    @ResponseBody
    public String saveSupplierQuotation(@RequestBody SupplierQuotation supplierQuotation){

        return supplierQuotationService.saveSupplierQuotation(supplierQuotation);
    }

//    //获取供应商列表
//    @RequestMapping(value = "getSupplierList", method = {RequestMethod.POST})
//    @ResponseBody
//    public Map<String,Object>getSupplierList(@RequestBody SupplierQuotation supplierQuotation)
//    {
//        return supplierDao.findList(supplier);
//    }
    //获取供应商报价
    @RequestMapping(value = "getSupplierPrice", method = {RequestMethod.POST})
    @ResponseBody
    public BigDecimal getSupplierPrice(@RequestBody SupplierQuotation supplierQuotation)
    {
        return supplierQuotationService.getSupplierPrice(supplierQuotation);
    }

    // 供应商报价审批
    @RequestMapping(value = "approvalQuotation", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> approvalQuotation(@RequestBody List<SupplierQuotation> supplierQuotationList)
    {
        return supplierQuotationService.approvalQuotation(supplierQuotationList);
    }
}