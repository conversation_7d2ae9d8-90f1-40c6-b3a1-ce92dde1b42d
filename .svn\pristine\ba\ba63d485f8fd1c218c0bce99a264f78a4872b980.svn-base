<%@ page contentType="text/html;charset=UTF-8" %>
<div ng-intro-options="listOptions" ng-intro-method="helpList" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="listReadOnlyOptions" ng-intro-method="helpListReadOnly" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="infoOptions" ng-intro-method="helpInfo" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="listCusOptions" ng-intro-method="helpCusList" ng-intro-autostart="shouldAutoStart"></div>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">系统管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="sys.user">用户管理</a>
        </li>
    </ul>
    <div class="page-toolbar">
    	<button class="btn default btn-fit-height pull-right" ng-click="help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>
</div>
<!-- END 导航-->
<tabset class="tabset-margin-top">
    <!-- BEGIN 用户列表 -->
    <tab heading="用户列表" active="ctrl.tabs.viewForm.active">
    	<div id="listStep6" class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">登录名称：</label>

                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="ctrl.query.userCode.value"
                                           disable-valid-styling="true"
                                           disable-invalid-styling="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">姓名：</label>

                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="ctrl.query.userName.value"
                                           disable-valid-styling="true"
                                           disable-invalid-styling="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">状态：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <select class="form-control"
                                                disable-auto-validate="true"
                                                ng-model="ctrl.query.status.value"
                                                ng-options="status.value as status.name for status in ctrl.query.status.list">
                                        </select>
                                    </div>
                                </div>
                            </div>
                        <div class="col-md-6 col-lg-4">
			                 <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">创建时间：</label>
	                                <div class="col-sm-7 col-md-8">
	                                     <div class="input-prepend input-group">
											<span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
											<input type="text" class="form-control" disable-auto-validate="true"
												ng-blur="ctrl.initDate(ctrl.time)"
												kyb-daterange 
												kyb-daterange-options="ctrl.rangeOptions"
												ng-model="ctrl.time" 
												placeholder="请选择时间段">
										 </div>
	                                </div>
			                  </div>
	                     </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="ctrl.doQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                        	<button class="btn btn-default btn-default-width pull-right" ng-if="false" ng-click="ctrl.doExecNotificationMergeId()"><i class="fa fa-search"></i> 执行更新mergeId</button>
                        	<button class="btn btn-default btn-default-width pull-right" ng-if="false" ng-click="ctrl.doExecUpdateContract()"><i class="fa fa-search"></i> 更新合同是已审核合同明细全部送货的单据</button>
                            <button class="btn btn-default btn-default-width pull-right" ng-if="false" ng-click="ctrl.doExecUpdatePnlQty()"><i class="fa fa-search"></i> 执行更新生产AB板数</button>
                            <button class="btn btn-default btn-default-width pull-right" ng-if="false" ng-click="ctrl.doExecCountNextProcess()"><i class="fa fa-search"></i> 执行更新过数的下一道工序</button>
                            <button class="btn btn-default btn-default-width pull-right" ng-if="false" ng-click="ctrl.doExecDelCardData()"><i class="fa fa-search"></i> 删除工程多余的数据</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
  
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">用户列表</div>
                <div id="listStep7" class="actions">
                    <div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
                        <button type="button" class="btn green btn-default-width" ng-click="ctrl.addUser()"><i class="fa fa-plus"></i> 添加用户</button>
                    </div>
                     <div class="portlet-input input-inline input-small">
		                    <form action="a/sys/user/export" method="get" enctype="multipart/form-data" target="hidden_frame">
		                            <input type="text" ng-show="false" name="userCode" value="{{ctrl.query.userCode.value}}" /> 
		                            <input type="text" ng-show="false" name="userName" value="{{ctrl.query.userName.value}}" /> 
		                            <input type="text" type="text" ng-show="false" name="orderBy" value="{{ctrl.query.sort.value}}" />
		                            <input type="text" ng-show="false" name="sentTimeStartQr" value="{{ctrl.query.sentTimeStartQr.value}}"/>
		                            <input type="text" ng-show="false" name="sentTimeEndQr" value="{{ctrl.query.sentTimeEndQr.value}}"/>
		                            <input type="text" ng-show="false" name="status" value="{{ctrl.query.status.value}}"/>
		                            <input type="text" ng-show="false"  name="queryAll" value="{{ctrl.queryAll}}"/>
		                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出用户</button>
		                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
		                     </form>
                     </div>
                </div>
            </div>
            <div class="portlet-body">
            	<div id="listStep1" class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
                        <tr class="heading">
	                        <th id="listStep2" ng-class="{'sorting': ctrl.sort.userCode.both, 'sorting_desc': ctrl.sort.userCode.desc, 'sorting_asc': ctrl.sort.userCode.asc}" ng-click="ctrl.sortClick('userCode')">用户名&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                        <th ng-class="{'sorting': ctrl.sort.userName.both, 'sorting_desc': ctrl.sort.userName.desc, 'sorting_asc': ctrl.sort.userName.asc}" ng-click="ctrl.sortClick('userName')">姓名&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                        <th ng-class="{'sorting': ctrl.sort.departmentName.both, 'sorting_desc': ctrl.sort.departmentName.desc, 'sorting_asc': ctrl.sort.departmentName.asc}" ng-click="ctrl.sortClick('departmentName')">部门&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                        <th ng-class="{'sorting': ctrl.sort.status.both, 'sorting_desc': ctrl.sort.status.desc, 'sorting_asc': ctrl.sort.status.asc}" ng-click="ctrl.sortClick('status')">状态</th>
	                        <th>拥有角色</th>
	                        <th ng-class="{'sorting': ctrl.sort.createdDate.both, 'sorting_desc': ctrl.sort.createdDate.desc, 'sorting_asc': ctrl.sort.createdDate.asc}" ng-click="ctrl.sortClick('createdDate')">创建日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                        <th>微信关联</th>
	                        <th id="listStep3" ng-if="ctrl.right.edit">操作</th>
	                    </tr>
	                    </thead>
	                    <tbody>
	                    <tr ng-repeat="row in ctrl.page.data.list" ng-dblclick="ctrl.ctrlShowDetail($index)">
	                        <td><a href="javascript:void(0)" ng-click="ctrl.ctrlShowDetail($index)" ng-bind="row.userCode"></a></td>
	                        <td ng-bind="row.userName"></td>
	                        <td ng-bind="row.dept | deptFilter:row.deptList"></td>
	                        <td ng-bind="row.status | flagFilter: ctrl.flagsAll"></td>
	                        <td ng-bind="row.role | roleFilter:row.roleList:0"></td>
	                        <td ng-bind="row.createdDate" ></td>
	                        <td ng-bind="row.openId | wechatFilter" ></td>
	                        <td ng-if="ctrl.right.edit">
	                            <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="ctrl.loginUser.recordId !== row.recordId && row.status !== 3" ng-click="ctrl.delUser($index)"><i class="fa fa-times font-red"></i> 删除</a>
	                            <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="row.status == 3 && row.openId || row.status == 1 && row.openId" ng-click="ctrl.clearUserWeChat($index)"><i class="fa fa-weixin font-red"></i> 清除微信关联</a>
	                        </td>
	                    </tr>
	                    </tbody>
	                </table>
	            </div>
                <div class="row page-margin-top">
                    <div id="listStep5" class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select class="form-control inline" style="margin-top:8px; width:100px;"
                                disable-valid-styling="true"
                                disable-invalid-styling="true"
                                ng-model="ctrl.page.pageSize"
                                ng-change="ctrl.pageSizeChange()"
                                ng-options="pageSizeOption for pageSizeOption in ctrl.page.pageSizeOptions">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{ctrl.page.data.startCount}} / {{ctrl.page.data.endCount}} 条，共 {{ctrl.page.data.count}} 条</span>
                    </div>
                    <div id="listStep4" class="col-md-12 col-lg-6">
                        <paging
                                class="pull-right"
                                page="ctrl.page.data.pageNo"
                                page-size="ctrl.page.data.pageSize"
                                total="ctrl.page.data.count"
                                adjacent="1"
                                dots="..."
                                scroll-top="false"
                                hide-if-empty="false"
                                ul-class="pagination"
                                active-class="active"
                                disabled-class="disabled"
                                show-prev-next="true"
                                paging-action="ctrl.doPage(page, pageSize, total)">
                        </paging>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- END 用户列表 -->
    <!-- BEGIN 用户编辑 -->
    <tab active="ctrl.tabs.editForm.active" ng-show="ctrl.tabs.editForm.show">
        <tab-heading>
            用户详情 <i class="fa fa-times set-cursor-pointer" ng-click="ctrl.hideEditForm()"></i>
        </tab-heading>
        <div class="portlet light bordered protlet-edit-form">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">用户{{ctrl.editTitle}}</div>
            </div>
            <div class="portlet-body">
                <form id="infoStep1" class="form-horizontal" name="userForm" ng-init="ctrl.setFormScope(this)"
                      novalidate="novalidate" ng-submit="ctrl.submitMod(userForm);" ng-submit-force="true">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">归属部门：</label>
                        <div class="col-sm-7">
                            <ui-select ng-model="ctrl.deptSelect" firstfocus="{{ctrl.focus}}" theme="bootstrap" ng-disabled="!ctrl.right.edit || ctrl.user.status === '3'">
                                <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                <ui-select-choices repeat="item in ctrl.depts | filter: $select.search">
                                    <div ng-bind-html="item.name | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label"><span class="required">*</span>用户名：</label>
                        <div class="col-sm-7">
                        	<div class="input-group" ng-hide="!ctrl.right.edit || ctrl.user.status === '3' || !ctrl.isAdd">
								<span class="input-group-addon" ng-bind="ctrl.userCode">
								</span>
								<input type="text" class="form-control" placeholder="请输入登录名" required ng-maxlength="32" ng-model="ctrl.user.userCode">
							</div>
							<input class="form-control" type="text" placeholder="请输入登录名"
                                   ng-model="ctrl.user.userCode" 
                                   ng-if="!ctrl.isAdd"
                                   ng-disabled="!ctrl.right.edit || ctrl.user.status === '3' || !ctrl.isAdd"
                                   ng-maxlength="32"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label"><span class="required">*</span>姓名：</label>
                        <div class="col-sm-7">
                            <input class="form-control" type="text" placeholder="请输入用户姓名"
                                   ng-model="ctrl.user.userName" ng-disabled="!ctrl.right.edit || ctrl.user.status === '3'" ng-maxlength="32" required/>
                        </div>
                    </div>
                    <div ng-if="!ctrl.isAdd && ctrl.right.edit && ctrl.user.status !== '3'">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">密码：</label>
                            <div class="col-sm-7">
                                <input class="form-control" type="password" placeholder="请输入密码，若不修改密码，请留空。"
                                       ng-model="ctrl.password" ng-maxlength="100"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">确认密码：</label>
                            <div class="col-sm-7">
                                <input class="form-control" type="password" placeholder="请输入确认密码"
                                       ng-model="ctrl.confirmUserPwd" ng-Confirmpassword="{{ctrl.password}}"/>
                            </div>
                        </div>
                    </div>
                    <div ng-if="ctrl.isAdd && ctrl.right.edit">
                        <div class="form-group">
                            <label class="col-sm-3 control-label"><span class="required">*</span>密码：</label>
                            <div class="col-sm-7">
                                <input class="form-control" type="password" placeholder="请输入密码，若不修改密码，请留空。"
                                       ng-model="ctrl.password" required ng-maxlength="100"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"><span class="required">*</span>确认密码：</label>
                            <div class="col-sm-7">
                                <input class="form-control" type="password" placeholder="请输入确认密码"
                                       ng-model="ctrl.confirmUserPwd" ng-Confirmpassword="{{ctrl.password}}"
                                       required/>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">出生日期：</label>
                        <div class="col-sm-7">
                            <input type="text" class="form-control"
                            	   ng-disabled="!ctrl.right.edit || ctrl.user.status === '3'"
                                   ng-model="ctrl.user.birthday"
                                   data-date-format="yyyy年MM月dd日"
                                   data-date-type="number"
                                   data-min-date="02/10/1901"
                                   data-max-date="today"
                                   data-autoclose="1"
                                   daysOfWeekDisabled="false"
                                   name="birthday"
                                   bs-datepicker/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">职务：</label>
                        <div class="col-sm-7">
                            <input class="form-control" type="text" placeholder="请输入用户职务"
                                   ng-model="ctrl.user.position" ng-disabled="!ctrl.right.edit || ctrl.user.status === '3'" ng-maxlength="32"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">入职日期：</label>
                        <div class="col-sm-7">
                            <input type="text" class="form-control" 
                            	   ng-model="ctrl.user.hiredDate"
                            	   ng-disabled="!ctrl.right.edit || ctrl.user.status === '3'"
                                   data-date-format="yyyy年MM月dd日"
                                   data-date-type="number"
                                   data-min-date="02/10/00"
                                   data-max-date="today"
                                   data-autoclose="1"
                                   daysOfWeekDisabled="false"
                                   name="hiredDate"
                                   bs-datepicker/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">家庭电话：</label>

                        <div class="col-sm-7">
                            <input class="form-control" type="text" placeholder="请输入用户固定电话"
                                   ng-model="ctrl.user.phone" ng-maxlength="20" ng-disabled="!ctrl.right.edit || ctrl.user.status === '3'"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">手机号码：</label>
                        <div class="col-sm-7">
                            <input class="form-control" type="text" placeholder="请输入用户移动号码"
                                   ng-model="ctrl.user.mobile" ng-maxlength="20" ng-disabled="!ctrl.right.edit || ctrl.user.status === '3'"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">邮箱：</label>
                        <div class="col-sm-7">
                            <input class="form-control" type="text" placeholder="请输入用户邮箱"
                                   ng-model="ctrl.user.email" ng-Cnemail ng-disabled="!ctrl.right.edit || ctrl.user.status === '3'"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">在职状态：</label>
                        <div class="col-sm-7">
                            <select class="form-control"
                            		ng-disabled="!ctrl.right.edit || ctrl.user.status === '3'"
                                    ng-model="ctrl.user.status"
                                    ng-options="flag.flagId as flag.flagName for flag in ctrl.flags">
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">角色：</label>
                        <div class="col-sm-7">
                            <div class="input-group">
                                <input class="form-control" type="text" disabled ng-model="ctrl.roleStr"
                                       placeholder="单击右边按钮设置角色"/>
							 	<span class="input-group-btn">
							 		<button id="infoStep2" type="button" class="btn blue" ng-click="ctrl.showRoleSet()"
                                            ng-disabled="!ctrl.right.manage || ctrl.user.status === '3'">设置角色
                                    </button>
							 	</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                       <label class="col-sm-3 control-label">子公司：</label>
                       <div class="col-sm-7">
                           <ui-select theme="bootstrap" multiple ng-model="ctrl.selectBrabchs" sortable="true" close-on-select="false"  ng-disabled="!ctrl.right.edit || ctrl.user.status === '3'">
						    <ui-select-match placeholder="请选择...">{{$item.shortName}}</ui-select-match>
						    <ui-select-choices repeat="branch.recordId as branch in ctrl.branchList | filter: $select.search" refresh-delay="1000">
						      <div ng-bind-html="branch.name | highlight: $select.search"></div>
						       <small>
                             		<span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{branch.shortName}}<br></span>
                                </small>
						    </ui-select-choices>
						  </ui-select>
                         </div>
                     </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">备注：</label>
                        <div class="col-sm-7">
                                <textarea class="form-control" placeholder="备注信息"
                                     ng-model="ctrl.user.remark" ng-disabled="!ctrl.right.manage || ctrl.user.status === '3'" ng-maxlength="255"></textarea>
                        </div>
                    </div>
                    <div class="form-group" ng-hide="ctrl.isAdd">
                        <label class="col-sm-3 control-label">创建时间：</label>

                        <div class="col-sm-7">
                                <span class="form-control" Readonly
                                      ng-bind="ctrl.user.createdDate | date:'yyyy-MM-dd HH:mm:ss'"></span>
                        </div>
                    </div>
                    <div class="form-group" ng-if="ctrl.right.edit && ctrl.user.status !== '3'" id="infoStep3">
                        <div class="col-sm-offset-4 col-sm-10">
                            <button type="submit"  class="btn btn-primary btn-default-width" style="margin-left:15px;"><i class="fa fa-save"></i> 保&nbsp;存</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </tab>
    <!-- END 用户编辑 -->
    
    <!-- BEGIN 客户用户列表 -->
    <tab heading="客户用户列表" active="ctrl.tabs.viewCusForm.active">
    	<div id="listCusStep6" class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">登录名称：</label>

                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="ctrl.cusQuery.userCode.value"
                                           disable-valid-styling="true"
                                           disable-invalid-styling="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">姓名：</label>

                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="ctrl.cusQuery.userName.value"
                                           disable-valid-styling="true"
                                           disable-invalid-styling="true"/>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">客户：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select theme="bootstrap" 
	                                    	ng-model="ctrl.cusQuery.customer.value" reset-search-input="false" register-custom-form-control>
									    <ui-select-match placeholder="请选择...">{{$select.selected.shortName}}</ui-select-match>
									    <ui-select-choices repeat="item in ctrl.refer.customerListOfQuery | filter: $select.search | limitTo: ctrl.infiniteScroll.currentItems track by $index" 
									    infinite-scroll="ctrl.addMoreItems()"
									    infinite-scroll-distance="2">
									      <div ng-bind-html="item.no | highlight: $select.search"></div>
	                                      <small>
	                                          <span style="color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.shortName}}<br></span>
	                                      </small>
									    </ui-select-choices>
								    </ui-select>
								    
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4">
			                 <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">创建时间：</label>
                                <div class="col-sm-7 col-md-8">
                                     <div class="input-prepend input-group">
										<span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
										<input type="text" class="form-control" disable-auto-validate="true"
											kyb-daterange 
											kyb-daterange-options="ctrl.rangeOptions"
											ng-model="ctrl.cusTime" 
											placeholder="请选择时间段">
									 </div>
                                </div>
			                  </div>
	                    </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="ctrl.doQueryCus()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
  
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">客户用户列表</div>
                <div id="listCusStep7" class="actions">
                    <div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
                        <button type="button" class="btn green btn-default-width" ng-click="ctrl.addCusUser()"><i class="fa fa-plus"></i> 添加客户用户</button>
                    </div>
                </div>
            </div>
            <div class="portlet-body">
            	<div id="listCusStep1" class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
                        <tr class="heading">
	                        <th id="listCusStep2" ng-class="{'sorting': ctrl.cusSort.userCode.both, 'sorting_desc': ctrl.cusSort.userCode.desc, 'sorting_asc': ctrl.cusSort.userCode.asc}" ng-click="ctrl.cusSortClick('userCode')">用户名&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                        <th ng-class="{'sorting': ctrl.cusSort.userName.both, 'sorting_desc': ctrl.cusSort.userName.desc, 'sorting_asc': ctrl.cusSort.userName.asc}" ng-click="ctrl.cusSortClick('userName')">姓名&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                        <th>归属客户</th>
	                        <th>状态</th>
	                        <th>手机</th>
	                        <th>邮箱</th>
	                        <th>备注</th>
	                        <th ng-class="{'sorting': ctrl.cusSort.createdDate.both, 'sorting_desc': ctrl.cusSort.createdDate.desc, 'sorting_asc': ctrl.cusSort.createdDate.asc}" ng-click="ctrl.cusSortClick('createdDate')">创建日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                        <th id="listCusStep3" ng-if="ctrl.right.edit">操作</th>
	                    </tr>
	                    </thead>
	                    <tbody>
	                    <tr ng-repeat="row in ctrl.cusPage.data.list" >
	                        <td ng-bind="row.userCode"></td>
	                        <td ng-bind="row.userName"></td>
	                        <td ng-bind="row.customer.shortName"></td>
	                        <td ng-if="row.customer.status==100303" ><span class="label label-sm label-danger">无效</span></td>
	                        <td ng-if="row.customer.status==100302"><span class="label label-sm label-success">有效 </span></td>
                            <td ng-if="row.customer.status==100301" ><span class="label label-sm label-danger">客户未确认</span></td>
                            <td ng-if="row.customer.status==60001" ><span class="label label-sm label-danger">客户待审批</span></td>
	                        <td ng-bind="row.mobile"></td>
	                        <td ng-bind="row.email"></td>
	                        <td ng-bind="row.remark"></td>
	                        <td ng-bind="row.createdDate" ></td>
	                        <td ng-if="ctrl.right.edit">
	                            <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.delCusUser($index)"><i class="fa fa-times font-red"></i> 删除</a>
	                            <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.resetPwdCusUser($index)"><i class="fa fa-mail-reply"></i> 修改密码</a>
	                            <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="row.customer.status == 100302 && row.openId" ng-click="ctrl.clearCusUserWeChat($index)"><i class="fa fa-weixin font-red"></i> 清除微信关联</a>
	                        </td>
	                    </tr>
	                    </tbody>
	                </table>
	            </div>
                <div class="row page-margin-top">
                    <div id="listCusStep5" class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select class="form-control inline" style="margin-top:8px; width:100px;"
                                disable-valid-styling="true"
                                disable-invalid-styling="true"
                                ng-model="ctrl.cusPage.pageSize"
                                ng-change="ctrl.cusPageSizeChange()"
                                ng-options="pageSizeOption for pageSizeOption in ctrl.cusPage.pageSizeOptions">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{ctrl.cusPage.data.startCount}} / {{ctrl.cusPage.data.endCount}} 条，共 {{ctrl.cusPage.data.count}} 条</span>
                    </div>
                    <div id="listCusStep4" class="col-md-12 col-lg-6">
                        <paging
                                class="pull-right"
                                page="ctrl.cusPage.data.pageNo"
                                page-size="ctrl.cusPage.data.pageSize"
                                total="ctrl.cusPage.data.count"
                                adjacent="1"
                                dots="..."
                                scroll-top="false"
                                hide-if-empty="false"
                                ul-class="pagination"
                                active-class="active"
                                disabled-class="disabled"
                                show-prev-next="true"
                                paging-action="ctrl.doCusPage(page, pageSize, total)">
                        </paging>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- END 用户列表 -->
    <!-- BEGIN 用户编辑 -->
    <tab active="ctrl.tabs.editCusForm.active" ng-show="ctrl.tabs.editCusForm.show">
        <tab-heading>
            客户用户详情 <i class="fa fa-times set-cursor-pointer" ng-click="ctrl.hideEditCusForm()"></i>
        </tab-heading>
        <div class="portlet light bordered protlet-edit-form">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">用户{{ctrl.editTitle}}</div>
            </div>
            <div class="portlet-body">
                <form id="infoStepCus1" class="form-horizontal" name="userCusForm" ng-init="ctrl.setCusFormScope(this)"
                      novalidate="novalidate" ng-submit="ctrl.submitCusMod(userCusForm);" ng-submit-force="true">
                    <div class="form-group">
                        <label class="col-sm-3 control-label"><span class="required">*</span>客户：</label>
                        <div class="col-sm-7">
                             <ui-select theme="bootstrap"  firstfocus="{{ctrl.focus.add}}"
                                   	ng-model="ctrl.userCus.customer" reset-search-input="false" register-custom-form-control
                                   	on-select="ctrl.selectCustomer()"
                                   	ng-disabled="!ctrl.right.edit" required>
							    <ui-select-match placeholder="请选择...">{{$select.selected.shortName}}</ui-select-match>
							    <ui-select-choices repeat="item in ctrl.refer.customerList | filter: $select.search | limitTo: ctrl.infiniteScroll.currentItems track by $index" 
							    infinite-scroll="ctrl.addMoreItems()"
							    infinite-scroll-distance="2">
							      <div ng-bind-html="item.no | highlight: $select.search"></div>
                                     <small>
                                         <span style="color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.shortName}}<br></span>
                                     </small>
							    </ui-select-choices>
						    </ui-select>
                        </div>
                    </div>
                        
                    <div class="form-group">
                        <label class="col-sm-3 control-label"><span class="required">*</span>用户名：</label>
                        <div class="col-sm-7">
                        	<div class="input-group">
								<span class="input-group-addon" ng-bind="ctrl.userCodeCus">
								</span>
								<input type="text" class="form-control" placeholder="请输入登录名" required ng-maxlength="32" ng-model="ctrl.userCus.userCode">
							</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label"><span class="required">*</span>姓名：</label>
                        <div class="col-sm-7">
                            <input class="form-control" type="text" placeholder="请输入用户姓名"
                                   ng-model="ctrl.userCus.userName" ng-disabled="!ctrl.right.edit" ng-maxlength="32" required/>
                        </div>
                    </div>
                    <div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"><span class="required">*</span>密码：</label>
                            <div class="col-sm-7">
                                <input class="form-control" type="password" placeholder="请输入密码，若不修改密码，请留空。"
                                       ng-model="ctrl.passwordCus" required ng-maxlength="100"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label"><span class="required">*</span>确认密码：</label>
                            <div class="col-sm-7">
                                <input class="form-control" type="password" placeholder="请输入确认密码"
                                       ng-model="ctrl.confirmUserPwdCus" ng-Confirmpassword="{{ctrl.passwordCus}}"
                                       required/>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">手机号码：</label>
                        <div class="col-sm-7">
                            <input class="form-control" type="text" placeholder="请输入用户移动号码"
                                   ng-model="ctrl.userCus.mobile" ng-maxlength="20" ng-disabled="!ctrl.right.edit"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">邮箱：</label>
                        <div class="col-sm-7">
                            <input class="form-control" type="text" placeholder="请输入用户邮箱"
                                   ng-model="ctrl.userCus.email" ng-Cnemail ng-disabled="!ctrl.right.edit"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">备注：</label>

                        <div class="col-sm-7">
                        	<textarea class="form-control" placeholder="备注信息"
                                ng-model="ctrl.userCus.remark" ng-disabled="!ctrl.right.edit" ng-maxlength="255"></textarea>
                        </div>
                    </div>
                    <div class="form-group" ng-if="ctrl.right.edit">
                        <div class="col-sm-offset-4 col-sm-10">
                            <button type="submit"  class="btn btn-primary btn-default-width" style="margin-left:15px;"><i class="fa fa-save"></i> 保&nbsp;存</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </tab>
    <!-- END 用户编辑 -->
    
</tabset>


<div class="modal fade bs-modal-lg" id="staticRole" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">
                    <span>角色设置</span>
                </h4>
            </div>
            <div class="modal-body">
                <div class="row">
                   <div class="col-md-12">
                       <div class="form-group">
                           <div class="col-md-12">
                               <div class="col-md-4" ng-repeat="role in ctrl.tempRoles">
                                   <p class="text-primary" ng-if="role.name != '系统管理员'">
                                       <checkbox ng-model="role.check"
                                                 ng-change="ctrl.roleChange()"></checkbox>
                                       {{role.name}}
                                   </p>
                                   <p class="text-default" ng-if="role.name == '系统管理员'">
                                       <checkbox ng-model="role.check"
                                       		  ng-disabled="true"
                                                 ng-change="ctrl.roleChange()"></checkbox>
                                       {{role.name}}
                                   </p>
                               </div>
                           </div>
                       </div>
                   </div>
               </div>
               <br/>
               <br/>
               <br/>
               <div class="row">
                   <div class="col-md-12">
                       <div class="form-group">
                           <label class="control-label col-md-2">拥有角色</label>

                           <div class="col-md-10">
                               <textarea class="form-control" placeholder="角色信息" disabled row="3"
                                         ng-bind="ctrl.tempRoleStr"></textarea>
                           </div>
                       </div>
                   </div>
               </div>
            </div>
            
            <div class="modal-footer">
                <button type="submit" data-dismiss="modal" class="btn blue"
                        ng-disabled="!ctrl.checkSelectRole()" ng-click="ctrl.roleSave()">确定
                </button>
                <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
            </div>
                    
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="staticRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">删除用户</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="ctrl.delMsg"></p></div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.doDelUser()">删除</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="staticRemoveCus" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">删除客户用户</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="ctrl.delMsg"></p></div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.doDelCusUser()">删除</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="staticClearWeChat" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">清除用户微信关联</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="ctrl.clearWeChatMsg"></p></div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.doClearUserWeChat()">确认</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="staticClearCusWeChat" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">清除用户微信关联</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="ctrl.clearCusWeChatMsg"></p></div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.doClearCusUserWeChat()">确认</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-modal-lg" id="staticModPwd" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">
                    <span>修改密码</span>
                </h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" name="pwd_form" novalidate="novalidate" 
                	ng-submit="ctrl.modCusPwd(pwd_form);" ng-submit-force="true">
                        
                    <div class="row">
                        <div class="col-md-11">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>密码：</label>
                                <div class="col-sm-7 col-md-10">
                                    <input class="form-control" type="password" placeholder="请输入密码"
                                       ng-model="ctrl.passwordCus" required ng-maxlength="100"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-11">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>确认密码：</label>
                                <div class="col-sm-7 col-md-10">
                                     <input class="form-control" type="password" placeholder="请输入确认密码"
                                       ng-model="ctrl.confirmUserPwdCus" ng-Confirmpassword="{{ctrl.passwordCus}}"
                                       required/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary" ng-if="ctrl.right.edit">保存</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default" ng-if="ctrl.right.edit" ng-click="ctrl.cacelPwdForm(pwd_form)">关闭</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">提示</h4>
                    </div>
                    <div class="modal-body">
                        <p><span ng-bind="ctrl.message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>