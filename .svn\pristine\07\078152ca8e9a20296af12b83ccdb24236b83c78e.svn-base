<%@ page contentType="text/html;charset=UTF-8"%>
<!-- BEGIN 导航-->
<div class="page-bar  page-bar-margin-top" >
<ul class="page-breadcrumb">
	<li><i class="fa fa-home"></i> <a href="#/dashboard.html">主页</a>
		<i class="fa fa-angle-right"></i></li>
	<li><a href="javascript:;">生产管理</a> <i class="fa fa-angle-right"></i></li>

	<li><a ui-sref="production.ask">问客管理</a></li>
</ul>
</div>

<tabset class="tabset-margin-top">
	<tab heading="问客列表" active="ctrl.tabs.viewForm.active">
		<div id="listStep6" class="panel panel-default">
			<div class="panel-heading font-blue-hoki">查询</div>
			<div class="panel-body">
				<form class="form-horizontal">
					<div class="row">
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">通知单编号：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" ng-model="ctrl.query.notiNo" disable-auto-validate="true" />
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">生产编号：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" ng-model="ctrl.query.craftNo" disable-auto-validate="true" />
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">
									<span>问客时间 ：</span>
								</label>
								<div class="col-sm-7 col-md-8">
									<div class="input-prepend input-group">
										<span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
										<input type="text" class="form-control" disable-auto-validate="true"
											   ng-blur="ctrl.initDate(ctrl.time)"
											   kyb-daterange
											   kyb-daterange-options="ctrl.rangeOptions"
											   ng-model="ctrl.time"
											   placeholder="请选择时间段">
									</div>
								</div>
							</div>
						</div>
<%--						<div class="col-md-6 col-lg-4">--%>
<%--							<div class="form-group">--%>
<%--								<label class="col-sm-3 col-md-4 control-label">--%>
<%--									<span>反馈时间 ：</span>--%>
<%--								</label>--%>
<%--								<div class="col-sm-7 col-md-8">--%>
<%--									<div class="input-prepend input-group">--%>
<%--										<span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>--%>
<%--										<input type="text" class="form-control" disable-auto-validate="true"--%>
<%--											   ng-blur="ctrl.initDate(ctrl.query.feedbackTime)"--%>
<%--											   kyb-daterange-options="ctrl.rangeOptions"--%>
<%--											   kyb-daterange--%>
<%--											   ng-model="ctrl.query.feedbackTime"--%>
<%--											   placeholder="请选择时间段">--%>
<%--									</div>--%>
<%--								</div>--%>
<%--							</div>--%>
<%--						</div>--%>
					</div>
					<div class="row">
						<div class="col-sm-12">
							<button class="btn btn-default btn-default-width pull-right" ng-click="ctrl.doQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
						</div>
					</div>
				</form>
			</div>
		</div>

		<div class="portlet light bordered">
			<div class="portlet-title">
				<div class="caption font-blue-hoki">问客列表</div>
				<div id="listStep7" class="actions">
					<div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
						<button type="button" class="btn green btn-default-width" ng-click="ctrl.showEditForm(1)"><i class="fa fa-plus"></i> 添加问客</button>
					</div>
				</div>
			</div>
			<div class="portlet-body">
				<div id="listStep1" class="table-scrollable" style="margin-top:0px !important">
					<table class="table table-striped table-bordered table-condensed table-advance table-hover" >
						<thead>
						<tr class="heading">
							<th width="9%" ng-class="{'sorting': ctrl.sort.createdDate.both, 'sorting_desc': ctrl.sort.createdDate.desc, 'sorting_asc': ctrl.sort.createdDate.asc}" ng-click="ctrl.sortClick('createdDate')">工程创建日期</th>
							<th width="9%" ng-class="{'sorting': ctrl.sort.estimateDate.both, 'sorting_desc': ctrl.sort.estimateDate.desc, 'sorting_asc': ctrl.sort.estimateDate.asc}" ng-click="ctrl.sortClick('estimateDate')">交货日期</th>
							<th width="5%" ng-class="{'sorting': ctrl.sort.notiNo.both, 'sorting_desc': ctrl.sort.notiNo.desc, 'sorting_asc': ctrl.sort.notiNo.asc}" ng-click="ctrl.sortClick('notiNo')">通知单编号</th>
							<th width="5%" ng-class="{'sorting': ctrl.sort.craftNo.both, 'sorting_desc': ctrl.sort.craftNo.desc, 'sorting_asc': ctrl.sort.craftNo.asc}" ng-click="ctrl.sortClick('craftNo')">生产编号</th>
							<th width="10%" ng-class="{'sorting': ctrl.sort.customerModel.both, 'sorting_desc': ctrl.sort.customerModel.desc, 'sorting_asc': ctrl.sort.customerModel.asc}" ng-click="ctrl.sortClick('customerModel')">客户型号</th>
							<th width="4%" ng-class="{'sorting': ctrl.sort.orderDeailArea.both, 'sorting_desc': ctrl.sort.orderDeailArea.desc, 'sorting_asc': ctrl.sort.orderDeailArea.asc}" ng-click="ctrl.sortClick('orderDeailArea')">订单平米</th>
							<th width="9%">开始问客时间</th>
							<th width="9%">结束问客时间</th>
							<th width="9%">开始反馈时间</th>
							<th width="9%">结束反馈时间</th>
							<th id="listStep3">操作&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
						</tr>
						</thead>
						<tbody>
						<tr ng-repeat="item in ctrl.page.ask.data.list" ng-dblclick="ctrl.ctrlShowDetail($index)">
							<td ng-bind="item.engineerCreatedDate"></td>
							<td ng-bind="item.estimateDate"></td>
							<td ng-bind="item.notiNo"></td>
							<td ng-bind="item.craftNo"></td>
							<td ng-bind="item.customerModel"></td>
							<td ng-bind="item.orderDeailArea"></td>
							<td ng-bind="item.askStartDate"></td>
							<td ng-bind="item.askEndDate"></td>
							<td ng-bind="item.feedbackStartDate"></td>
							<td ng-bind="item.feedbackEndDate"></td>
							<td>
								<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.showEditForm(4,item)"><i class="fa fa-check-square-o font-black"></i>查看详情</a>
								<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.updateModal(item)"><i class="fa fa-edit font-blue"></i>编辑</a>
								<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="item.askStartDate && !item.feedbackStartDate" ng-click="ctrl.deleteModal(item.recordId)"><i class="fa fa-times font-red"></i>删除</a>
								<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="item.askStartDate && !item.feedbackStartDate" ng-click="ctrl.feedback(item)"><i class="fa fa-check font-green"></i>反馈</a>
							</td>
						</tr>
						</tbody>
					</table>
				</div>
				<div class="row page-margin-top">
					<div id="listStep5" class="col-md-12 col-lg-6">
						<span class="inline">每页</span>
						<select  class="form-control inline table-pageSize-width"
								 disable-valid-styling="true"
								 disable-invalid-styling="true"
								 ng-model="ctrl.page.ask.pageSize"
								 ng-change="ctrl.pageSizeChange(0)"
								 ng-options="option for option in ctrl.page.options">
						</select>
						<span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{ctrl.page.ask.data.startCount}} / {{ctrl.page.ask.data.endCount}} 条，共 {{ctrl.page.ask.data.count}} 条</span>
					</div>
					<div id="listStep4" class="col-md-12 col-lg-6">
						<paging
								class="pull-right"
								ul-class="pagination" active-class="active" disabled-class="disabled"
								page="ctrl.page.ask.data.pageNo" page-size="ctrl.page.ask.data.pageSize"
								total="ctrl.page.ask.data.count" adjacent="1" dots="..."
								croll-top="false" hide-if-empty="false" show-prev-next="true"
								paging-action="ctrl.doPage(page, pageSize, total)">
						</paging>
					</div>
				</div>
			</div>
		</div>
	</tab>
	<tab active="ctrl.tabs.editForm.active" ng-show="ctrl.tabs.editForm.show">
		<tab-heading>问客详情 <i class="fa fa-times set-cursor-pointer" ng-click="ctrl.hideEditForm()"></i></tab-heading>

		<div class="portlet light bordered">
			<div class="portlet-title">
				<div class="caption font-blue-hoki">{{ctrl.ask.title}}
				</div>
			</div>
			<div class="portlet-body">
				<form id="infoStep1" class="form-horizontal" name="editForm" ng-init="ctrl.setFormScope(this)" novalidate="novalidate" ng-submit="ctrl.saveAsk();" ng-submit-force="true">
					<div class="row">
						<div class="col-md-6 col-lg-4">
							<div id="infoStep3" class="form-group">
								<label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>工程编号：</label>
								<div class="col-sm-7 col-md-8">
									<ui-select  theme="bootstrap"
												ng-model="ctrl.edit.notification" register-custom-form-control
												on-select="ctrl.loadCard()"
												ng-disabled="ctrl.ask.type!=1"
												required>
										<ui-select-match placeholder="请选择...">{{$select.selected.notiNo}}</ui-select-match>
										<ui-select-choices repeat="item as item in ctrl.getCardDetailList | filter: $select.search | limitTo: ctrl.infiniteScroll.currentItemsForContract track by $index"
														   infinite-scroll="ctrl.addMoreItemsForContract()"
														   infinite-scroll-distance="3">
											<div ng-bind-html="item.notiNo | highlight: $select.search"></div>
										</ui-select-choices>
									</ui-select>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">生产编号：</label>
								<div class="col-sm-7 col-md-8">
									<input class="form-control" type="text" ng-model="ctrl.edit.craftNo" disabled/>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">客户型号：</label>
								<div class="col-sm-7 col-md-8">
									<input class="form-control" ng-model="ctrl.edit.customerModel" disabled/>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">订单平米：</label>
								<div class="col-sm-7 col-md-8">
									<input class="form-control" type="text" ng-model="ctrl.edit.orderDeailArea" disabled ng-disabled="true"/>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-md-6 col-lg-4" ng-if="ctrl.ask.type==1 || ctrl.ask.type==4 || ctrl.ask.type==2">
							<!--   问客时间   -->
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">问客开始时间：</label>
								<div class="col-sm-7 col-md-8">
									<a class="dropdown-toggle" id="dropdown2" role="button" data-toggle="dropdown" data-target="#" href="#">
										<div class="input-group" disabled>
											<span type="text" class="form-control" data-ng-bind="ctrl.edit.askStartDate | date:'yyyy-MM-dd HH:mm:ss'" disabled></span>
											<span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
										</div>
									</a>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">问客结束时间：</label>
								<div class="col-sm-7 col-md-8">
									<a class="dropdown-toggle" id="dropdown3" role="button" data-toggle="dropdown" data-target="#" href="#">
										<div class="input-group">
											<span type="text" class="form-control" data-ng-bind="ctrl.edit.askEndDate | date:'yyyy-MM-dd HH:mm:ss'" disabled></span>
											<span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
										</div>
									</a>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4" ng-if="ctrl.ask.type==3 || ctrl.ask.type==4">
							<!--   反馈时间   -->
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">反馈开始时间：</label>
								<div class="col-sm-7 col-md-8">
									<a class="dropdown-toggle" id="dropdown4" role="button" data-toggle="dropdown" data-target="#" href="#">
										<div class="input-group">
											<span type="text" class="form-control" data-ng-bind="ctrl.edit.feedbackStartDate | date:'yyyy-MM-dd HH:mm:ss'" disabled></span>
											<span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
										</div>
									</a>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">反馈结束时间：</label>
								<div class="col-sm-7 col-md-8">
									<a class="dropdown-toggle" id="dropdown5" role="button" data-toggle="dropdown" data-target="#" href="#">
										<div class="input-group">
											<span type="text" class="form-control" data-ng-bind="ctrl.edit.feedbackEndDate | date:'yyyy-MM-dd HH:mm:ss'" disabled></span>
											<span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
										</div>
									</a>
								</div>
							</div>
						</div>
					</div>
					<div class="row" ng-if="ctrl.ask.type==1 || ctrl.ask.type==4 || ctrl.ask.type==2">
						<div class="col-md-12 col-lg-8">
							<div class="form-group">
								<label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>问客内容：</label>
								<div class="col-sm-7 col-md-10">
							    	<textarea class="form-control" placeholder="内容" ng-model="ctrl.edit.ask" required ng-maxlength="255" ng-disabled="ctrl.ask.type==4"></textarea>
								</div>
							</div>
						</div>
					</div>
					<div class="row" ng-if="ctrl.ask.type==4 || ctrl.ask.type==3">
						<div class="col-md-12 col-lg-8">
							<div class="form-group">
								<label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>反馈内容：</label>
								<div class="col-sm-7 col-md-10">
									<textarea class="form-control" placeholder="内容" ng-model="ctrl.edit.feedback" required ng-maxlength="255" ng-disabled="ctrl.ask.type==4"></textarea>
								</div>
							</div>
						</div>
					</div>
					<div id="infoStep7" class="form-group">
						<div class="col-sm-offset-4 col-sm-10" ng-if="ctrl.ask.type!=4">
							<button type="submit" class="btn btn-primary btn-default-width btn-margin-left-15" ><i class="fa fa-save"></i>&nbsp;&nbsp;保&nbsp;&nbsp;存</button>
						</div>
					</div>
				</form>
			</div>
		</div>
	</tab>
</tabset>
<div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true"></button>
				<h4 class="modal-title"><span>提示</span></h4>
			</div>
			<div class="modal-body">
				<p>
					<span ng-bind="ctrl.message"></span>
				</p>
			</div>
			<div class="modal-footer">
				<button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
			</div>
		</div>
	</div>
</div>
<div class="modal fade" id="deleteModal" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="deleteModal" aria-hidden="true" style="overflow:auto!important;">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title text-danger" id="exampleModalLabel">删除后不可恢复！</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<i aria-hidden="true" class="ki ki-close"></i>
				</button>
			</div>
			<div class="modal-body text-danger">
				请慎重考虑，是否删除该问客记录？
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">取消</button>
				<button type="button" class="btn btn-danger font-weight-bold" ng-click="ctrl.deleteAsk()">删除</button>
			</div>
		</div>
	</div>
</div>
