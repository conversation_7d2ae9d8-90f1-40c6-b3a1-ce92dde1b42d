/* Setup general page controller */
kybApp.controller('stockAgeCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil','BaseUtil','$http', function ($rootScope, $scope, upida, $timeout, CommonUtil,BaseUtil,$http) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
    var vm = this;
    
    // 权限
    vm.right = {};
    
    vm.page = {}; // 分页数据
    vm.page.pageSizeOptions = [5, 10, 30, 50, 100, 150]; // 显示数据大小
    
    vm.page.infoPlace = {};
    vm.page.infoPlace.data = {};
    vm.page.infoPlace.pageSize = 10;
    vm.page.infoPlace.pageNo = 1;
    
    vm.page.stockAge = {};
    vm.page.stockAge.data = {};
    vm.page.stockAge.pageSize = 10;
    vm.page.stockAge.pageNo = 1;
    
    vm.page.infoPlace.url = "stock/rawmaterial/inoutPlacePage";
    vm.page.infoPlace.condition = []; // 条件
    
    vm.inoutPlaceQuery = {}; // 查询对象
    vm.inoutPlaceQuery.storehouse = {};
    vm.inoutPlaceQuery.storehouse.name = "storehouse";
    vm.inoutPlaceQuery.storehouse.value = "";
    vm.inoutPlaceQuery.sort = {};
    vm.inoutPlaceQuery.sort.name = "orderBy";
    vm.inoutPlaceQuery.sort.value = "operateDate DESC";
    // 查询条件参数
    vm.inoutPlaceQuery.inoutTimeStartQr = {};
    vm.inoutPlaceQuery.inoutTimeStartQr.name = "inoutTimeStartQr";
    vm.inoutPlaceQuery.inoutTimeStartQr.value = "";
    vm.inoutPlaceQuery.inoutTimeEndQr = {};
    vm.inoutPlaceQuery.inoutTimeEndQr.name = "inoutTimeEndQr";
    vm.inoutPlaceQuery.inoutTimeEndQr.value = "";
    vm.inoutPlaceQuery.inoutTimeTwoStartQr = {};
    vm.inoutPlaceQuery.inoutTimeTwoEndQr = {};
    
    // tabs控制
    vm.tabs = {
    	placeReport: {active: true},
    	stockAgeReport: {active: false}
    };
    
    vm.pageSizeChangePlace = function (flag) {
        var size = 10;
        var condition = [];
        var url = "";
        
        if(flag == 1){
        	size = vm.page.infoPlace.pageSize;
        }else if (flag == 2){
        	size = vm.page.stockAge.pageSize;
        }
        url = vm.page.infoPlace.url;
        condition = vm.page.infoPlace.condition;
        vm.initPlaceDate(1, size, condition, url, flag);
    };
    
    vm.doPagePlace = function (page, pageSize, total, flag) {
        var url = "";
        var condition = [];

        if(flag == 1){
        	vm.page.infoPlace.pageNo = page;
            vm.page.infoPlace.pageSize = pageSize;
        }else if (flag == 2){
        	vm.page.stockAge.pageNo = page;
            vm.page.stockAge.pageSize = pageSize;
        }
        url = vm.page.infoPlace.url;
        condition = vm.page.infoPlace.condition;
        
        vm.initPlaceDate(page, pageSize, condition, url, flag);
    };
    
    vm.initPlaceDate = function (no, size, condition, url, flag) {
        MainCtrl.blockUI({
            animate: true,
        });

        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;
        
        condition.push({
            name: vm.inoutPlaceQuery.sort.name,
            value: "operateDate ASC"
        });

        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condition);

        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};

            // 如果结果为空
            if (angular.isUndefined(result) || angular.isUndefined(result.list)) {
                data.pageNo = 1;
                data.pageSize = 10;
                data.startCount = 0;
                data.endCount = 0;
                data.list = [];
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }

            // 设置返回的请求数据
            if(flag == 1){
            	vm.page.infoPlace.data = data;
            }else if (flag == 2){
            	vm.page.stockAge.data = data;
            }

            MainCtrl.unblockUI();
        });
    };
    
    vm.materialNo = "";
    vm.materialName = "";
    vm.materialTypeId = "";
    vm.specification = "";
    vm.storeHouseId = "";
    
    vm.materialNoOne = "";
    vm.materialNameOne = "";
    vm.materialTypeIdOne = "";
    vm.specificationOne = "";
    vm.storeHouseIdOne = "";
    
    vm.getPlaceDataList = function(flag) {
    	// 设置查询条件
        var condit = [];
        if(flag == 1){
        	if(vm.materialNo)
            {
            	condit.push({
                    name: 'materialNo',
                    value: vm.materialNo
                });
            }
            if(vm.materialName)
            {
            	condit.push({
                    name: 'materialName',
                    value: vm.materialName
                });
            }
            if(vm.materialTypeId)
            {
            	condit.push({
                    name: 'materialTypeId',
                    value: vm.materialTypeId
                });
            }
            if(vm.specification)
            {
            	condit.push({
                    name: 'specification',
                    value: vm.specification
                });
            }
            if(vm.storeHouseId)
            {
            	condit.push({
                    name: 'storeHouseId',
                    value: vm.storeHouseId
                });
            }
        }else if(flag == 2){
        	if(vm.materialNoOne)
            {
            	condit.push({
                    name: 'materialNo',
                    value: vm.materialNoOne
                });
            }
            if(vm.materialNameOne)
            {
            	condit.push({
                    name: 'materialName',
                    value: vm.materialNameOne
                });
            }
            if(vm.materialTypeIdOne)
            {
            	condit.push({
                    name: 'materialTypeId',
                    value: vm.materialTypeIdOne
                });
            }
            if(vm.specificationOne)
            {
            	condit.push({
                    name: 'specification',
                    value: vm.specificationOne
                });
            }
            if(vm.storeHouseIdOne)
            {
            	condit.push({
                    name: 'storeHouseId',
                    value: vm.storeHouseIdOne
                });
            }
        }
        condit.push({
            name: 'reportFlag',
            value: flag
        });

        vm.page.infoPlace.pageNo = 1;
        vm.page.infoPlace.condition = condit;
        
        if(flag == 1){
        	vm.initPlaceDate(vm.page.infoPlace.pageNo, vm.page.infoPlace.pageSize, vm.page.infoPlace.condition, vm.page.infoPlace.url, flag);
        }else if(flag == 2){
        	vm.initPlaceDate(vm.page.stockAge.pageNo, vm.page.stockAge.pageSize, vm.page.infoPlace.condition, vm.page.infoPlace.url, flag);
        }
	}
    
    vm.stockAgeReportList = [];
    vm.stockAgeId = "";
    vm.openStockDeail = function(row) {
    	vm.stockAgeId = row.recordId;
    	MainCtrl.blockUI({
            animate: true,
        });
    	// 获取占用料情况以及可用物料
     	upida.post("stock/rawmaterial/stockAgeReportList",row).then(function(data){
     		vm.stockAgeReportList = data;
     		MainCtrl.unblockUI();
     		$("#openStockDeail").modal();
     	});
	};
	
	vm.fomatFloat = function(num)
    {
    	var n = 2
    	var f = parseFloat(num);
	    if(!num || isNaN(f)){
	        return null;
	    }   
	    f = Math.round(num*Math.pow(10, n))/Math.pow(10, n); // n 幂   
	    var s = f.toString();
	    return vm.toQfw(s);
    }
	
	vm.toQfw = function(str){
		var re=/(?!^)(?=(\d{3})+(?:$|\.))/g;
		str=str.replace(re,",");
		return str;
	};
    
    // 加载数据
    function loadData() {
    	 MainCtrl.blockUI({
             animate: true,
         });
    	 vm.queryAll = CommonUtil.dataRangeIsAll("10603", BaseUtil.getMenuList());
    	//是否有采购管理的菜单权限
     	upida.get("common/rightall?prefix=purch:rawmaterial").then(function(data){
            vm.right.edit = data.edit;
            vm.right.view = data.view;
            vm.right.manage = data.manage;
     	 });
    	 
    	upida.get("stock/rawmaterial/load/data?queryAll="+vm.queryAll).then(function (data) {
            // 加载物料类型
            vm.materialTypeList = data.materialTypeList && data.materialTypeList.length > 0 ? data.materialTypeList : [];
            vm.materialTypeList.splice(0, 0, {value: '所有', recordId: ''});
            
            //加载默认仓库
            vm.storehouseList = data.storehouseList && data.storehouseList.length > 0 ? data.storehouseList : [];
            vm.object = {name: '所有', recordId: ''};
            vm.storehouseList.splice(0, 0, vm.object);
            
            vm.getPlaceDataList(1);
        });
    }
    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadData();
    });
}]);