package com.kyb.pcberp.modules.hr.depart_center.pojo;

import com.kyb.pcberp.common.persistence.DataEntity;

public class Hr_TaskProgress extends DataEntity<Hr_TaskProgress> {

    private static final long serialVersionUID = 1L;

    private String taskId;

    private String completeTime;

    private String schedule;

    private String type;

    private String person;

    private Integer personId;


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(String completeTime) {
        this.completeTime = completeTime;
    }

    public String getSchedule() {
        return schedule;
    }

    public void setSchedule(String schedule) {
        this.schedule = schedule;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPerson() {
        return person;
    }

    public void setPerson(String person) {
        this.person = person;
    }

    public Integer getPersonId() {
        return personId;
    }

    public void setPersonId(Integer personId) {
        this.personId = personId;
    }
}
