/**
 *
 */
package com.kyb.pcberp.modules.production.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.contract.entity.Contract;
import com.kyb.pcberp.modules.eg.entity.CardA;
import com.kyb.pcberp.modules.eg.entity.EgProcess;
import com.kyb.pcberp.modules.production.util.machine.ProduceUtils;
import com.kyb.pcberp.modules.quality.entity.Inspect;
import com.kyb.pcberp.modules.sys.entity.User;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 生产数据记录Entity
 *
 * <AUTHOR>
 * @version 2015-09-25
 */
public class ProduceRecord extends DataEntity<ProduceRecord>
{
    private static final long serialVersionUID = 1L;

    private ProduceBatch produceBatch; // 生产批次ID

    private String produceBatchId;

    private String feedId;

    private EgProcess process; // 生产工序ID

    private String processId;

    private ProduceBatchDetail produceBatchDetail;

    private String produceBatchDetailId;

    private Date takeOverTime; // 接板时间

    private Date handOverTime; // 交板时间

    private Long operator; // 操作员

    private Long ipqc; // 质检员

    private Integer mrb; // 报废数量

    private Integer mrbA; // A板报废数量

    private Integer mrbB; // B板报废数量

    private String takeOverQtyA; // 接收数量 A

    private String takeOverQtyB; // 接收数量 B

    private String handOverQtyA; // 移交数量 A

    private String handOverQtyB; // 移交数量 B

    private String handOverFlag; // 1:未接板,2:接板,3:交板,4:最后一道工序交完

    private String recvProcessId; // recvprocessid

    private String takeOverQtySetA; // 接板A板set数

    private Integer takeOverQtyPcsA; // 接板A板pcs数

    private String takeOverQtySetB; // 接板B板set数

    private Integer takeOverQtyPcsB; // 接板B板pcs数

    private String takeOverQtySetT; // 接板总seT数

    private Integer takeOverQtyPcsT; // 接板总pcs数

    private String handOverQtySetA; // 交板A板set数

    private Integer takeCheckNumA; // 接板A板检数

    private Integer takeCheckNumB; // 接板B板检数

    private Integer handOverQtyPcsA; // 交板A板pcs数

    private String handOverQtySetB; // 交板B板set数

    private Integer handOverQtyPcsB; // 交板B板pcs数

    private String handOverQtySetT; // 交板总set数

    private Integer handOverQtyPcsT; // 交板总pcs数

    private Inspect inspect;// 检测记录

    private String skipCount; // 跳过过数，0:不是，1:是

    private Double area;// 本次过数面积

    private CardA cardA;// 流程卡

    private Long pcsQuantity; // 投料数量

    private String templateId; // 模板编号

    private String operType; // 0 接板、1 交板、2 交接板

    private String recvProcessName;

    private String popo;

    private List<User> dataOperatorList; // 工序有过数权限操作人

    private Boolean isEdit = false; // WC 2017-06-06 是否是编辑

    private String oldProduceId; // zjn 未拆卡前旧过数记录id

    private Integer totalSetCountA;// 1个pnl有几个set(A板)

    private Integer pcsCountA;// 1个pnl有几个pcs(A板)

    private Integer totalSetCountB;// 1个pnl有几个set(B板)

    private Integer pcsCountB;// 1个pnl有几个pcs(B板)

    private ProduceUtils produceUtils = new ProduceUtils();

    private String endProcessId; // 结束工序id

    private String craftNo; // 生产编号

    private String userId; // 用户id

    private String noticeNos; // 通知单号

    private String no; // 投补料编号

    private String batchType; // 投补料状态

    private String aFileUrl; // A板资料

    private String bFileUrl; // B板资料

    private String dFileUrl; // 大板资料

    private String fileUrl; // 文件列表

    private List<String> fileList; // 文件列表

    private String processCardAId; // 工程卡id

    private String rankName; // 排序人名字

    private String queryDate; // 查询时间

    private String dateType; // 查询模式

    private String processIds;

    private String checkAllQc;

    private String produce; // 1:过数，2:品检

    private String category;

    private String takeMan;

    private String handMan;

    private String nextTakeTime;

    private String cardCount;

    private String deailNo;

    private String takeHandWip; // 参数设置(0:按接板显示结存,1:按交板显示结存)

    private String mergeType;

    private String oneId;

    private String oneCust;

    private String twoId;

    private String twoCust;

    private String threeId;

    private String threeCust;

    private String customerPo;

    private String customerModel;

    private String acDistributeDate;

    private String deliveryDate;

    private Double bathArea;

    private Double orderArea;

    private String companyId;

    private String startTime;

    private String endTime;

    private String startTimeTwo;

    private String endTimeTwo;

    private Integer num;

    private Integer qtyPcsT;

    private Integer quantity;

    private String name;

    private Double pcsArea;

    private String noticeId;

    // 数据汇总统计 start

    private Date sentTimeStartQr; // 时间段查询条件 载体

    private Date sentTimeEndQr;

    private BigDecimal endProcessMrbArea; // 工序报废面积

    private Integer thenInStockCount; // 已入库款数

    private BigDecimal thenInStockArea; // 已入库面积

    private Integer feedCount; // 投料款数

    private BigDecimal feedArea; // 投料面积

    private String onlineBalanceArea; // 在线结存面积

    private String balanceArea; // 结存面积

    private BigDecimal sumInStockArea; // 累计入库面积

    private BigDecimal sumFeedArea; // 累计投料面积

    private Contract contract;

    private String date;

    private String condition; // 查询条件

    private List<EgProcess> processList;

    private String relationId;

    // 数据汇总统计 end

    private String notificationId;

    private Date issusDate;

    private String feedingNo;

    private String productBatchStr; // 生产批次

    private String cetageName;

    private BigDecimal takeOverArea; // 接板面积

    private BigDecimal handOverArea; // 交板面积

    private String splotchQuery;

    private Boolean showDiscardFlag;

    private Integer inStockCount;

    private String complaintId;

    private String batchName;

    private String lastId;

    private String materialId;

    private String customerId;

    private String storeHouseId;

    private String contractDetailId;

    private String inspectStatus;

    private Integer type; // 2:外协,其它:不良

    private Integer takePcsA;

    private Integer takePcsB;

    private Integer handPcsA;

    private Integer handPcsB;

    private Integer finishStatus;

    private Integer handPcsT;

    private String handNames;

    private BigDecimal inspectMrbArea;

    private String processValueId;

    private BigDecimal taskArea;

    private String phone;

    private String fishplateName; // 接板人

    private String alternateName; // 交板人

    private Integer modeOperation; // 操作方式：1ERP,2微信,3App

    private Integer takeModeOperation; // 接板操作方式

    private Integer handModeOperation; // 交板操作方式

    private String craftId;

    private Integer seqNum;

    private Date nextProcessTime; //下道工序接板时间

    private String feeNo;   // 投料单编号

    private Integer actualSeconds; // 耗时秒

    private Date lastHandOverTime;

    private String processManagementId;

    private String orderType;

    private BigDecimal reworkArea;

    private BigDecimal rejectApplicationArea;

    private BigDecimal sumReworkArea;

    private BigDecimal sumRejectApplicationArea;

    private String processName;

    private Date previousProcessTime; // 上道工序交板时间

    private String countOverFlag;

    private String applyProcessIds;

    public String getPopo()
    {
        return popo;
    }

    public void setPopo(String popo)
    {
        this.popo = popo;
    }

    public String getTemplateId()
    {
        return templateId;
    }

    public void setTemplateId(String templateId)
    {
        this.templateId = templateId;
    }

    public Long getPcsQuantity()
    {
        return pcsQuantity;
    }

    public void setPcsQuantity(Long pcsQuantity)
    {
        this.pcsQuantity = pcsQuantity;
    }

    public CardA getCardA()
    {
        return cardA;
    }

    public void setCardA(CardA cardA)
    {
        this.cardA = cardA;
    }

    public Double getArea()
    {
        return area;
    }

    public void setArea(Double area)
    {
        this.area = area;
    }

    public String getSkipCount()
    {
        return skipCount;
    }

    public void setSkipCount(String skipCount)
    {
        this.skipCount = skipCount;
    }

    public String getProduceBatchId()
    {
        return produceBatchId;
    }

    public void setProduceBatchId(String produceBatchId)
    {
        this.produceBatchId = produceBatchId;
    }

    public String getProcessId()
    {
        return processId;
    }

    public void setProcessId(String processId)
    {
        this.processId = processId;
    }

    public String getProduceBatchDetailId()
    {
        return produceBatchDetailId;
    }

    public void setProduceBatchDetailId(String produceBatchDetailId)
    {
        this.produceBatchDetailId = produceBatchDetailId;
    }

    public ProduceRecord()
    {
        super();
    }

    public ProduceRecord(String id)
    {
        super(id);
    }

    public ProduceBatch getProduceBatch()
    {
        return produceBatch;
    }

    public void setProduceBatch(ProduceBatch produceBatch)
    {
        this.produceBatch = produceBatch;
    }

    public EgProcess getProcess()
    {
        return process;
    }

    public void setProcess(EgProcess process)
    {
        this.process = process;
    }

    public ProduceBatchDetail getProduceBatchDetail()
    {
        return produceBatchDetail;
    }

    public void setProduceBatchDetail(ProduceBatchDetail produceBatchDetail)
    {
        this.produceBatchDetail = produceBatchDetail;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getTakeOverTime()
    {
        return takeOverTime;
    }

    public void setTakeOverTime(Date takeOverTime)
    {
        this.takeOverTime = takeOverTime;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getHandOverTime()
    {
        return handOverTime;
    }

    public void setHandOverTime(Date handOverTime)
    {
        this.handOverTime = handOverTime;
    }

    public Long getOperator()
    {
        return operator;
    }

    public void setOperator(Long operator)
    {
        this.operator = operator;
    }

    public Long getIpqc()
    {
        return ipqc;
    }

    public void setIpqc(Long ipqc)
    {
        this.ipqc = ipqc;
    }

    public Integer getMrb()
    {
        return mrb;
    }

    public void setMrb(Integer mrb)
    {
        this.mrb = mrb;
    }

    public Integer getMrbA()
    {
        return mrbA;
    }

    public void setMrbA(Integer mrbA)
    {
        this.mrbA = mrbA;
    }

    public Integer getMrbB()
    {
        return mrbB;
    }

    public void setMrbB(Integer mrbB)
    {
        this.mrbB = mrbB;
    }

    public String getTakeOverQtyA()
    {
        if (null != pcsCountA)
        {
            takeOverQtyA = produceUtils.computPnlSetA(takeOverQtyPcsA, totalSetCountA, pcsCountA, 1);
        }
        return StringUtils.isBlank(takeOverQtyA) ? "0" : takeOverQtyA;
    }

    public void setTakeOverQtyA(String takeOverQtyA)
    {
        this.takeOverQtyA = takeOverQtyA;
    }

    public String getTakeOverQtyB()
    {
        if (null != pcsCountB)
        {
            takeOverQtyB = produceUtils.computPnlSetB(takeOverQtyPcsB, totalSetCountB, pcsCountB, 1);
        }
        return StringUtils.isBlank(takeOverQtyB) ? "0" : takeOverQtyB;
    }

    public void setTakeOverQtyB(String takeOverQtyB)
    {
        this.takeOverQtyB = takeOverQtyB;
    }

    public String getHandOverQtyA()
    {
        handOverQtyA = produceUtils.computPnlSetA(handOverQtyPcsA, totalSetCountA, pcsCountA, 1);
        return StringUtils.isBlank(handOverQtyA) ? "0" : handOverQtyA;
    }

    public void setHandOverQtyA(String handOverQtyA)
    {
        this.handOverQtyA = handOverQtyA;
    }

    public String getHandOverQtyB()
    {
        handOverQtyB = produceUtils.computPnlSetB(handOverQtyPcsB, totalSetCountB, pcsCountB, 1);
        return StringUtils.isBlank(handOverQtyB) ? "0" : handOverQtyB;
    }

    public void setHandOverQtyB(String handOverQtyB)
    {
        this.handOverQtyB = handOverQtyB;
    }

    public String getHandOverFlag()
    {
        return handOverFlag;
    }

    public void setHandOverFlag(String handOverFlag)
    {
        this.handOverFlag = handOverFlag;
    }

    public String getRecvProcessId()
    {
        return recvProcessId;
    }

    public void setRecvProcessId(String recvProcessId)
    {
        this.recvProcessId = recvProcessId;
    }

    public Inspect getInspect()
    {
        return inspect;
    }

    public void setInspect(Inspect inspect)
    {
        this.inspect = inspect;
    }

    public String getTakeOverQtySetA()
    {
        if (null != pcsCountA)
        {
            takeOverQtySetA = produceUtils.computPnlSetA(takeOverQtyPcsA, totalSetCountA, pcsCountA, 2);
        }
        return StringUtils.isBlank(takeOverQtySetA) ? "0" : takeOverQtySetA;
    }

    public void setTakeOverQtySetA(String takeOverQtySetA)
    {
        this.takeOverQtySetA = takeOverQtySetA;
    }

    public Integer getTakeOverQtyPcsA()
    {
        return takeOverQtyPcsA == null ? 0 : takeOverQtyPcsA;
    }

    public void setTakeOverQtyPcsA(Integer takeOverQtyPcsA)
    {
        this.takeOverQtyPcsA = takeOverQtyPcsA;
    }

    public String getTakeOverQtySetB()
    {
        if (null != pcsCountB)
        {
            takeOverQtySetB = produceUtils.computPnlSetB(takeOverQtyPcsB, totalSetCountB, pcsCountB, 2);
        }
        return StringUtils.isBlank(takeOverQtySetB) ? "0" : takeOverQtySetB;
    }

    public void setTakeOverQtySetB(String takeOverQtySetB)
    {
        this.takeOverQtySetB = takeOverQtySetB;
    }

    public Integer getTakeOverQtyPcsB()
    {
        return takeOverQtyPcsB == null ? 0 : takeOverQtyPcsB;
    }

    public void setTakeOverQtyPcsB(Integer takeOverQtyPcsB)
    {
        this.takeOverQtyPcsB = takeOverQtyPcsB;
    }

    public String getTakeOverQtySetT()
    {
        takeOverQtySetT = produceUtils.computPnlTSetT(takeOverQtyPcsA,
            takeOverQtyPcsB,
            totalSetCountA,
            pcsCountA,
            totalSetCountB,
            pcsCountB,
            1);
        return takeOverQtySetT;
    }

    public void setTakeOverQtySetT(String takeOverQtySetT)
    {
        this.takeOverQtySetT = takeOverQtySetT;
    }

    public Integer getTakeOverQtyPcsT()
    {
        return takeOverQtyPcsT;
    }

    public void setTakeOverQtyPcsT(Integer takeOverQtyPcsT)
    {
        this.takeOverQtyPcsT = takeOverQtyPcsT;
    }

    public String getHandOverQtySetA()
    {
        handOverQtySetA = produceUtils.computPnlSetA(handOverQtyPcsA, totalSetCountA, pcsCountA, 2);
        return StringUtils.isBlank(handOverQtySetA) ? "0" : handOverQtySetA;
    }

    public void setHandOverQtySetA(String handOverQtySetA)
    {
        this.handOverQtySetA = handOverQtySetA;
    }

    public Integer getTakeCheckNumA()
    {
        return takeCheckNumA;
    }

    public void setTakeCheckNumA(Integer takeCheckNumA)
    {
        this.takeCheckNumA = takeCheckNumA;
    }

    public Integer getTakeCheckNumB()
    {
        return takeCheckNumB;
    }

    public void setTakeCheckNumB(Integer takeCheckNumB)
    {
        this.takeCheckNumB = takeCheckNumB;
    }

    public Integer getHandOverQtyPcsA()
    {
        return handOverQtyPcsA == null ? 0 : handOverQtyPcsA;
    }

    public void setHandOverQtyPcsA(Integer handOverQtyPcsA)
    {
        this.handOverQtyPcsA = handOverQtyPcsA;
    }

    public String getHandOverQtySetB()
    {
        handOverQtySetB = produceUtils.computPnlSetB(handOverQtyPcsB, totalSetCountB, pcsCountB, 2);
        return StringUtils.isBlank(handOverQtySetB) ? "0" : handOverQtySetB;
    }

    public void setHandOverQtySetB(String handOverQtySetB)
    {
        this.handOverQtySetB = handOverQtySetB;
    }

    public Integer getHandOverQtyPcsB()
    {
        return handOverQtyPcsB == null ? 0 : handOverQtyPcsB;
    }

    public void setHandOverQtyPcsB(Integer handOverQtyPcsB)
    {
        this.handOverQtyPcsB = handOverQtyPcsB;
    }

    public String getHandOverQtySetT()
    {
        handOverQtySetT = produceUtils.computPnlTSetT(handOverQtyPcsA,
            handOverQtyPcsB,
            totalSetCountA,
            pcsCountA,
            totalSetCountB,
            pcsCountB,
            1);
        return handOverQtySetT;
    }

    public void setHandOverQtySetT(String handOverQtySetT)
    {
        this.handOverQtySetT = handOverQtySetT;
    }

    public Integer getHandOverQtyPcsT()
    {
        return handOverQtyPcsT;
    }

    public void setHandOverQtyPcsT(Integer handOverQtyPcsT)
    {
        this.handOverQtyPcsT = handOverQtyPcsT;
    }

    public String getOperType()
    {
        return operType;
    }

    public void setOperType(String operType)
    {
        this.operType = operType;
    }

    public String getRecvProcessName()
    {
        return recvProcessName;
    }

    public void setRecvProcessName(String recvProcessName)
    {
        this.recvProcessName = recvProcessName;
    }

    @Override
    public ProduceRecord clone()
    {
        ProduceRecord record = null;
        try
        {
            record = (ProduceRecord)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            e.printStackTrace();
        }
        return record;
    }

    @Override
    public void preInsert()
    {
        // Add: lpjLiu 2017-03-10 增加外发记录的标志
        if (this.isNewRecord)
        {
            if (StringUtils.isBlank(this.getPopo()))
            {
                this.setPopo(TypeKey.NO.toString());
            }
        }
        super.preInsert();
    }

    public List<User> getDataOperatorList()
    {
        return dataOperatorList;
    }

    public void setDataOperatorList(List<User> dataOperatorList)
    {
        this.dataOperatorList = dataOperatorList;
    }

    public Boolean getIsEdit()
    {
        return isEdit;
    }

    public void setIsEdit(Boolean isEdit)
    {
        this.isEdit = isEdit;
    }

    public String getOldProduceId()
    {
        return oldProduceId;
    }

    public void setOldProduceId(String oldProduceId)
    {
        this.oldProduceId = oldProduceId;
    }

    public Integer getTotalSetCountA()
    {
        return totalSetCountA;
    }

    public void setTotalSetCountA(Integer totalSetCountA)
    {
        this.totalSetCountA = totalSetCountA;
    }

    public Integer getPcsCountA()
    {
        return pcsCountA;
    }

    public void setPcsCountA(Integer pcsCountA)
    {
        this.pcsCountA = pcsCountA;
    }

    public Integer getTotalSetCountB()
    {
        return totalSetCountB;
    }

    public void setTotalSetCountB(Integer totalSetCountB)
    {
        this.totalSetCountB = totalSetCountB;
    }

    public Integer getPcsCountB()
    {
        return pcsCountB;
    }

    public void setPcsCountB(Integer pcsCountB)
    {
        this.pcsCountB = pcsCountB;
    }

    public String getFeedId()
    {
        return feedId;
    }

    public void setFeedId(String feedId)
    {
        this.feedId = feedId;
    }

    public String getEndProcessId()
    {
        return endProcessId;
    }

    public void setEndProcessId(String endProcessId)
    {
        this.endProcessId = endProcessId;
    }

    public String getCraftNo()
    {
        return craftNo;
    }

    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }

    public String getUserId()
    {
        return userId;
    }

    public void setUserId(String userId)
    {
        this.userId = userId;
    }

    public String getNoticeNos()
    {
        return noticeNos;
    }

    public void setNoticeNos(String noticeNos)
    {
        this.noticeNos = noticeNos;
    }

    public String getNo()
    {
        return no;
    }

    public void setNo(String no)
    {
        this.no = no;
    }

    public String getBatchType()
    {
        return batchType;
    }

    public void setBatchType(String batchType)
    {
        this.batchType = batchType;
    }

    public String getaFileUrl()
    {
        return aFileUrl;
    }

    public void setaFileUrl(String aFileUrl)
    {
        this.aFileUrl = aFileUrl;
    }

    public String getbFileUrl()
    {
        return bFileUrl;
    }

    public void setbFileUrl(String bFileUrl)
    {
        this.bFileUrl = bFileUrl;
    }

    public String getdFileUrl()
    {
        return dFileUrl;
    }

    public void setdFileUrl(String dFileUrl)
    {
        this.dFileUrl = dFileUrl;
    }

    public String getFileUrl()
    {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl)
    {
        this.fileUrl = fileUrl;
    }

    public List<String> getFileList()
    {
        return fileList;
    }

    public void setFileList(List<String> fileList)
    {
        this.fileList = fileList;
    }

    public String getProcessCardAId()
    {
        return processCardAId;
    }

    public void setProcessCardAId(String processCardAId)
    {
        this.processCardAId = processCardAId;
    }

    public String getRankName()
    {
        return rankName;
    }

    public void setRankName(String rankName)
    {
        this.rankName = rankName;
    }

    public String getQueryDate()
    {
        return queryDate;
    }

    public void setQueryDate(String queryDate)
    {
        this.queryDate = queryDate;
    }

    public String getDateType()
    {
        return dateType;
    }

    public void setDateType(String dateType)
    {
        this.dateType = dateType;
    }

    public String getProcessIds()
    {
        return processIds;
    }

    public void setProcessIds(String processIds)
    {
        this.processIds = processIds;
    }

    public String getCheckAllQc()
    {
        return checkAllQc;
    }

    public void setCheckAllQc(String checkAllQc)
    {
        this.checkAllQc = checkAllQc;
    }

    public String getProduce()
    {
        return produce;
    }

    public void setProduce(String produce)
    {
        this.produce = produce;
    }

    public ProduceUtils getProduceUtils()
    {
        return produceUtils;
    }

    public void setProduceUtils(ProduceUtils produceUtils)
    {
        this.produceUtils = produceUtils;
    }

    public String getCategory()
    {
        return category;
    }

    public void setCategory(String category)
    {
        this.category = category;
    }

    public String getTakeMan()
    {
        return takeMan;
    }

    public void setTakeMan(String takeMan)
    {
        this.takeMan = takeMan;
    }

    public String getHandMan()
    {
        return handMan;
    }

    public void setHandMan(String handMan)
    {
        this.handMan = handMan;
    }

    public String getNextTakeTime()
    {
        return nextTakeTime;
    }

    public void setNextTakeTime(String nextTakeTime)
    {
        this.nextTakeTime = nextTakeTime;
    }

    public String getCardCount()
    {
        return cardCount;
    }

    public void setCardCount(String cardCount)
    {
        this.cardCount = cardCount;
    }

    public String getDeailNo()
    {
        return deailNo;
    }

    public void setDeailNo(String deailNo)
    {
        this.deailNo = deailNo;
    }

    public String getTakeHandWip()
    {
        return takeHandWip;
    }

    public void setTakeHandWip(String takeHandWip)
    {
        this.takeHandWip = takeHandWip;
    }

    public String getMergeType()
    {
        return mergeType;
    }

    public void setMergeType(String mergeType)
    {
        this.mergeType = mergeType;
    }

    public String getOneId()
    {
        return oneId;
    }

    public void setOneId(String oneId)
    {
        this.oneId = oneId;
    }

    public String getOneCust()
    {
        return oneCust;
    }

    public void setOneCust(String oneCust)
    {
        this.oneCust = oneCust;
    }

    public String getTwoId()
    {
        return twoId;
    }

    public void setTwoId(String twoId)
    {
        this.twoId = twoId;
    }

    public String getTwoCust()
    {
        return twoCust;
    }

    public void setTwoCust(String twoCust)
    {
        this.twoCust = twoCust;
    }

    public String getThreeId()
    {
        return threeId;
    }

    public void setThreeId(String threeId)
    {
        this.threeId = threeId;
    }

    public String getThreeCust()
    {
        return threeCust;
    }

    public void setThreeCust(String threeCust)
    {
        this.threeCust = threeCust;
    }

    public String getCustomerPo()
    {
        return customerPo;
    }

    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }

    public String getCustomerModel()
    {
        return customerModel;
    }

    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }

    public String getAcDistributeDate()
    {
        return acDistributeDate;
    }

    public void setAcDistributeDate(String acDistributeDate)
    {
        this.acDistributeDate = acDistributeDate;
    }

    public String getDeliveryDate()
    {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }

    public Double getBathArea()
    {
        return bathArea;
    }

    public void setBathArea(Double bathArea)
    {
        this.bathArea = bathArea;
    }

    public Double getOrderArea()
    {
        return orderArea;
    }

    public void setOrderArea(Double orderArea)
    {
        this.orderArea = orderArea;
    }

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    public String getStartTime()
    {
        return startTime;
    }

    public void setStartTime(String startTime)
    {
        this.startTime = startTime;
    }

    public String getEndTime()
    {
        return endTime;
    }

    public void setEndTime(String endTime)
    {
        this.endTime = endTime;
    }

    public Integer getNum()
    {
        return num;
    }

    public void setNum(Integer num)
    {
        this.num = num;
    }

    public Integer getQtyPcsT()
    {
        return qtyPcsT;
    }

    public void setQtyPcsT(Integer qtyPcsT)
    {
        this.qtyPcsT = qtyPcsT;
    }

    public Integer getQuantity()
    {
        return quantity;
    }

    public void setQuantity(Integer quantity)
    {
        this.quantity = quantity;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public Double getPcsArea()
    {
        return pcsArea;
    }

    public void setPcsArea(Double pcsArea)
    {
        this.pcsArea = pcsArea;
    }

    public String getNoticeId()
    {
        return noticeId;
    }

    public void setNoticeId(String noticeId)
    {
        this.noticeId = noticeId;
    }

    public Date getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }

    public void setSentTimeStartQr(Date sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }

    public Date getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }

    public void setSentTimeEndQr(Date sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }

    public BigDecimal getEndProcessMrbArea()
    {
        return endProcessMrbArea;
    }

    public void setEndProcessMrbArea(BigDecimal endProcessMrbArea)
    {
        this.endProcessMrbArea = endProcessMrbArea;
    }

    public Integer getThenInStockCount()
    {
        return thenInStockCount;
    }

    public void setThenInStockCount(Integer thenInStockCount)
    {
        this.thenInStockCount = thenInStockCount;
    }

    public BigDecimal getThenInStockArea()
    {
        return thenInStockArea;
    }

    public void setThenInStockArea(BigDecimal thenInStockArea)
    {
        this.thenInStockArea = thenInStockArea;
    }

    public Integer getFeedCount()
    {
        return feedCount;
    }

    public void setFeedCount(Integer feedCount)
    {
        this.feedCount = feedCount;
    }

    public BigDecimal getFeedArea()
    {
        return feedArea;
    }

    public void setFeedArea(BigDecimal feedArea)
    {
        this.feedArea = feedArea;
    }

    public String getOnlineBalanceArea()
    {
        return onlineBalanceArea;
    }

    public void setOnlineBalanceArea(String onlineBalanceArea)
    {
        this.onlineBalanceArea = onlineBalanceArea;
    }

    public String getDate()
    {
        return date;
    }

    public void setDate(String date)
    {
        this.date = date;
    }

    public String getCondition()
    {
        return condition;
    }

    public void setCondition(String condition)
    {
        this.condition = condition;
    }

    public String getBalanceArea()
    {
        return balanceArea;
    }

    public void setBalanceArea(String balanceArea)
    {
        this.balanceArea = balanceArea;
    }

    public BigDecimal getSumInStockArea()
    {
        return sumInStockArea;
    }

    public void setSumInStockArea(BigDecimal sumInStockArea)
    {
        this.sumInStockArea = sumInStockArea;
    }

    public BigDecimal getSumFeedArea()
    {
        return sumFeedArea;
    }

    public void setSumFeedArea(BigDecimal sumFeedArea)
    {
        this.sumFeedArea = sumFeedArea;
    }

    public Contract getContract()
    {
        return contract;
    }

    public void setContract(Contract contract)
    {
        this.contract = contract;
    }

    public List<EgProcess> getProcessList()
    {
        return processList;
    }

    public void setProcessList(List<EgProcess> processList)
    {
        this.processList = processList;
    }

    public String getNotificationId()
    {
        return notificationId;
    }

    public void setNotificationId(String notificationId)
    {
        this.notificationId = notificationId;
    }

    public Date getIssusDate()
    {
        return issusDate;
    }

    public void setIssusDate(Date issusDate)
    {
        this.issusDate = issusDate;
    }

    public String getFeedingNo()
    {
        return feedingNo;
    }

    public void setFeedingNo(String feedingNo)
    {
        this.feedingNo = feedingNo;
    }

    public String getProductBatchStr()
    {
        return productBatchStr;
    }

    public void setProductBatchStr(String productBatchStr)
    {
        this.productBatchStr = productBatchStr;
    }

    public String getCetageName()
    {
        return cetageName;
    }

    public void setCetageName(String cetageName)
    {
        this.cetageName = cetageName;
    }

    public BigDecimal getTakeOverArea()
    {
        return takeOverArea;
    }

    public void setTakeOverArea(BigDecimal takeOverArea)
    {
        this.takeOverArea = takeOverArea;
    }

    public BigDecimal getHandOverArea()
    {
        return handOverArea;
    }

    public void setHandOverArea(BigDecimal handOverArea)
    {
        this.handOverArea = handOverArea;
    }

    public String getSplotchQuery()
    {
        return splotchQuery;
    }

    public void setSplotchQuery(String splotchQuery)
    {
        this.splotchQuery = splotchQuery;
    }

    public Boolean getShowDiscardFlag()
    {
        return showDiscardFlag;
    }

    public void setShowDiscardFlag(Boolean showDiscardFlag)
    {
        this.showDiscardFlag = showDiscardFlag;
    }

    public Integer getInStockCount()
    {
        return inStockCount;
    }

    public void setInStockCount(Integer inStockCount)
    {
        this.inStockCount = inStockCount;
    }

    public String getCraftNoEXP()
    {
        if (null != getCraftNo() && null != getProcess())
        {
            return getCraftNo() + "(" + getProcess().getCategory() + ")";
        }
        return "";
    }

    public Integer getHandOverQtyPcsTEXP()
    {
        if (null != getHandOverQtyPcsT() && null != getTakeOverQtyPcsT())
        {
            return getHandOverQtyPcsT() - getTakeOverQtyPcsT();
        }
        return null;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getTakeOverTimeStr()
    {
        return getTakeOverTime();
    }

    public String getTakeOverTimeTwoStr()
    {
        if (takeOverTime != null)
        {
            return DateUtils.formatDate(takeOverTime, "yyyy-MM-dd HH:mm:ss");
        }
        return "";
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getHandOverTimeStr()
    {
        return getHandOverTime();
    }

    public String getComplaintId()
    {
        return complaintId;
    }

    public void setComplaintId(String complaintId)
    {
        this.complaintId = complaintId;
    }

    public String getBatchName()
    {
        return batchName;
    }

    public void setBatchName(String batchName)
    {
        this.batchName = batchName;
    }

    public String getLastId()
    {
        return lastId;
    }

    public void setLastId(String lastId)
    {
        this.lastId = lastId;
    }

    public String getMaterialId()
    {
        return materialId;
    }

    public void setMaterialId(String materialId)
    {
        this.materialId = materialId;
    }

    public String getCustomerId()
    {
        return customerId;
    }

    public void setCustomerId(String customerId)
    {
        this.customerId = customerId;
    }

    public String getStoreHouseId()
    {
        return storeHouseId;
    }

    public void setStoreHouseId(String storeHouseId)
    {
        this.storeHouseId = storeHouseId;
    }

    public String getContractDetailId()
    {
        return contractDetailId;
    }

    public void setContractDetailId(String contractDetailId)
    {
        this.contractDetailId = contractDetailId;
    }

    public String getInspectStatus()
    {
        return inspectStatus;
    }

    public void setInspectStatus(String inspectStatus)
    {
        this.inspectStatus = inspectStatus;
    }

    public Integer getType()
    {
        return type;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    public Integer getTakePcsA()
    {
        return takePcsA;
    }

    public void setTakePcsA(Integer takePcsA)
    {
        this.takePcsA = takePcsA;
    }

    public Integer getTakePcsB()
    {
        return takePcsB;
    }

    public void setTakePcsB(Integer takePcsB)
    {
        this.takePcsB = takePcsB;
    }

    public Integer getHandPcsA()
    {
        return handPcsA;
    }

    public void setHandPcsA(Integer handPcsA)
    {
        this.handPcsA = handPcsA;
    }

    public Integer getHandPcsB()
    {
        return handPcsB;
    }

    public void setHandPcsB(Integer handPcsB)
    {
        this.handPcsB = handPcsB;
    }

    public Integer getFinishStatus()
    {
        return finishStatus;
    }

    public void setFinishStatus(Integer finishStatus)
    {
        this.finishStatus = finishStatus;
    }

    public Integer getHandPcsT()
    {
        return handPcsT;
    }

    public void setHandPcsT(Integer handPcsT)
    {
        this.handPcsT = handPcsT;
    }

    public String getHandNames()
    {
        return handNames;
    }

    public void setHandNames(String handNames)
    {
        this.handNames = handNames;
    }

    public BigDecimal getInspectMrbArea()
    {
        return inspectMrbArea;
    }

    public void setInspectMrbArea(BigDecimal inspectMrbArea)
    {
        this.inspectMrbArea = inspectMrbArea;
    }

    public String getRelationId()
    {
        return relationId;
    }

    public void setRelationId(String relationId)
    {
        this.relationId = relationId;
    }

    public String getStartTimeTwo()
    {
        return startTimeTwo;
    }

    public void setStartTimeTwo(String startTimeTwo)
    {
        this.startTimeTwo = startTimeTwo;
    }

    public String getEndTimeTwo()
    {
        return endTimeTwo;
    }

    public void setEndTimeTwo(String endTimeTwo)
    {
        this.endTimeTwo = endTimeTwo;
    }

    public String getProcessValueId()
    {
        return processValueId;
    }

    public void setProcessValueId(String processValueId)
    {
        this.processValueId = processValueId;
    }

    public BigDecimal getTaskArea()
    {
        return taskArea;
    }

    public void setTaskArea(BigDecimal taskArea)
    {
        this.taskArea = taskArea;
    }

    public String getProcessValueStr()
    {
        String result = null;
        if (StringUtils.isNotBlank(processValueId))
        {
            if ("1".equals(processValueId))
            {
                result = "曝光";
            }
            else if ("2".equals(processValueId))
            {
                result = "丝印";
            }
        }
        return result;
    }

    public String getPhone()
    {
        return phone;
    }

    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getFishplateName()
    {
        return fishplateName;
    }

    public void setFishplateName(String fishplateName)
    {
        this.fishplateName = fishplateName;
    }

    public String getAlternateName()
    {
        return alternateName;
    }

    public void setAlternateName(String alternateName)
    {
        this.alternateName = alternateName;
    }

    public Integer getModeOperation()
    {
        return modeOperation;
    }

    public void setModeOperation(Integer modeOperation)
    {
        this.modeOperation = modeOperation;
    }

    public Integer getTakeModeOperation()
    {
        return takeModeOperation;
    }

    public void setTakeModeOperation(Integer takeModeOperation)
    {
        this.takeModeOperation = takeModeOperation;
    }

    public Integer getHandModeOperation()
    {
        return handModeOperation;
    }

    public void setHandModeOperation(Integer handModeOperation)
    {
        this.handModeOperation = handModeOperation;
    }

    public String getCraftId() {
        return craftId;
    }

    public void setCraftId(String craftId) {
        this.craftId = craftId;
    }

    public Integer getSeqNum()
    {
        return seqNum;
    }

    public void setSeqNum(Integer seqNum)
    {
        this.seqNum = seqNum;
    }

    public Date getNextProcessTime() {
        return nextProcessTime;
    }

    public void setNextProcessTime(Date nextProcessTime) {
        this.nextProcessTime = nextProcessTime;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getNextProcessTimeStr()
    {
        return getNextProcessTime();
    }

    public String getFeeNo()
    {
        return feeNo;
    }

    public void setFeeNo(String feeNo)
    {
        this.feeNo = feeNo;
    }

    public Integer getActualSeconds()
    {
        return actualSeconds;
    }

    public void setActualSeconds(Integer actualSeconds)
    {
        this.actualSeconds = actualSeconds;
    }

    public Date getLastHandOverTime()
    {
        return lastHandOverTime;
    }

    public void setLastHandOverTime(Date lastHandOverTime)
    {
        this.lastHandOverTime = lastHandOverTime;
    }

    public String getLastHandOverTimeStr()
    {
        if(null != lastHandOverTime)
        {
            return DateUtils.formatDate(lastHandOverTime,"yyyy-MM-dd HH:mm:ss");
        }
        return null;
    }

    public String getProcessManagementId()
    {
        return processManagementId;
    }

    public void setProcessManagementId(String processManagementId)
    {
        this.processManagementId = processManagementId;
    }

    public String getOrderType()
    {
        return orderType;
    }

    public void setOrderType(String orderType)
    {
        this.orderType = orderType;
    }

    public BigDecimal getReworkArea()
    {
        return reworkArea;
    }

    public void setReworkArea(BigDecimal reworkArea)
    {
        this.reworkArea = reworkArea;
    }

    public BigDecimal getRejectApplicationArea()
    {
        return rejectApplicationArea;
    }

    public void setRejectApplicationArea(BigDecimal rejectApplicationArea)
    {
        this.rejectApplicationArea = rejectApplicationArea;
    }

    public BigDecimal getSumReworkArea()
    {
        return sumReworkArea;
    }

    public void setSumReworkArea(BigDecimal sumReworkArea)
    {
        this.sumReworkArea = sumReworkArea;
    }

    public BigDecimal getSumRejectApplicationArea()
    {
        return sumRejectApplicationArea;
    }

    public void setSumRejectApplicationArea(BigDecimal sumRejectApplicationArea)
    {
        this.sumRejectApplicationArea = sumRejectApplicationArea;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getShowHandOverFlag()
    {
        String result = null;
        if(StringUtils.isBlank(handOverFlag))
        {
            return result;
        }
        switch (handOverFlag)
        {
            case "1":
                result = "待接板";
                break;
            case "2":
                result = "待交板";
                break;
            case "3":
                result = "已交板";
                break;
            case "4":
                result = "已交板";
                break;
        }
        return result;
    }

    public Date getPreviousProcessTime() {
        return previousProcessTime;
    }

    public void setPreviousProcessTime(Date previousProcessTime) {
        this.previousProcessTime = previousProcessTime;
    }

    public String getPreviousProcessTimeStr()
    {
        if(null != previousProcessTime)
        {
            return DateUtils.formatDate(previousProcessTime);
        }
        return null;
    }

    public String getCountOverFlag() {
        return countOverFlag;
    }

    public void setCountOverFlag(String countOverFlag) {
        this.countOverFlag = countOverFlag;
    }

    public String getApplyProcessIds() {
        return applyProcessIds;
    }

    public void setApplyProcessIds(String applyProcessIds) {
        this.applyProcessIds = applyProcessIds;
    }
}
