<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div>
	<div class="row align-items-center pt-3 pb-3 alert-light border-bottom" style="color: #333;">
		&nbsp;&nbsp;&nbsp;&nbsp;公司
		<div class="col">
			<select class="form-control" v-model="company" v-on:change="loadData">
				<option v-for="item in userInformation" :key="item.recordId" :value="item" v-if="item.status == 1 && item.company && item.company.name">
					{{item.company.name}}
				</option>
			</select>
		</div>
		搜索
		<div class="col">
			<input class="form-control" v-model="name" v-on:change="loadData">
		</div>
	</div>
	<div class="row align-items-center alert-light border-bottom pt-2 pb-2" style="color: #333;">
		<div class="col text-left">
			<span>操作人&nbsp;:&nbsp;{{userErp.name}}</span>
		</div>
		<div class="col text-right">
			<span>处理张数&nbsp;:&nbsp;{{scanNum}}</span>
		</div>
	</div>
<%--	<div class="row align-items-center pt-2 pb-2" style="color: #333;">--%>
<%--		<span class="pl-3">搜索</span>--%>
<%--		<div class="col">--%>
<%--			<input class="form-control form-control-sm" v-model="name" v-on:change="loadData">--%>
<%--		</div>--%>
<%--	</div>--%>
	<div class="row align-items-center pt-3 pb-1 alert-light" style="color: #333;">
		<div class="col">
			<div v-for="item in deliveryList">
				<div class="row border-bottom pt-2 pb-2">
					<div class="col" style="word-break: break-all">
						<span class="badge badge-success">送货单号</span>&emsp;
						<span class="badge badge-info">{{item.no}}</span>
						<div>
							{{item.custNo}}&nbsp;{{item.saleName}}&nbsp;{{item.linkMan}}&nbsp;{{item.linkPhone}}
						</div>
						<div>
							{{item.address}}
						</div>
					</div>
					<div class="col-2 text-right">
						<div><i class="icon-hand-right" v-on:click="scanDelivery(item)"></i></div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-12">
			<div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
				<div class="modal-dialog">
					<div class="modal-content">
						<div class="modal-body">
							<p>
								送货单号：{{messageNo}}
							</p>
							<p>
								快递公司：{{messageCom}}
							</p>
							<p>
								快递单号：{{messageNumber}}
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div style="padding-top: 10rem;"></div>
	<div class="row fixed-bottom alert-light">
	    <div class="col text-center">
	    	<div style="font-size: 0.3rem;">
	    		粤ICP备16000534号
	    	</div>
	    	<div style="font-size: 0.3rem;">
	    		Copyright©2021 深圳市科易博软件有限公司 版权所有
	    	</div>
	    </div>
	</div>
</div>