package com.kyb.pcberp.common.utils;

import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.modules.icloud.capacity.dao.Icloud_CapacityDao;
import com.kyb.pcberp.modules.icloud.capacity.pojo.Icloud_CapacityRecord;
import com.kyb.pcberp.modules.icloud.cycleSetup.dao.Icloud_CycleSetupDao;
import com.kyb.pcberp.modules.icloud.cycleSetup.pojo.Icloud_CycleSetup;
import com.kyb.pcberp.modules.icloud.erp.craft.dao.Icloud_ErpCraftDao;
import com.kyb.pcberp.modules.icloud.erp.craft.pojo.Icloud_MaterialCard;
import com.kyb.pcberp.modules.icloud.erp.erp.dao.Icloud_NotificationDao;
import com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_Branch;
import com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_CardA;
import com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_Notification;
import com.kyb.pcberp.modules.icloud.erp.pojo.Icloud_Company;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.util.List;

public class Icloud_EstimateDateUtiil
{
    private Icloud_ErpCraftDao erpCraftDao = SpringContextHolder.getBean(Icloud_ErpCraftDao.class);

    private Icloud_NotificationDao notificationDao = SpringContextHolder.getBean(Icloud_NotificationDao.class);

    private Icloud_CycleSetupDao cycleSetupDao = SpringContextHolder.getBean(Icloud_CycleSetupDao.class);

    private Icloud_CapacityDao capacityDao = SpringContextHolder.getBean(Icloud_CapacityDao.class);

    public Icloud_MaterialCard startGenerateOrderMsg(Icloud_Notification notification, Icloud_Company company)
    {
        // 计算预估交期，得到预估交期计算方法，计算最迟采购交期
        Icloud_CardA cardA = new Icloud_CardA();
        cardA.setCompanyId(company.getRecordId());
        cardA.setCustomerModels(notification.getCustomerModel());
        cardA.setSaleCustName(notification.getCustomerNo());
        cardA.setSaleCompanyId(notification.getCompanyId());
        cardA.setSalecustId(notification.getCustomerId());
        cardA.setErpCustomerId(notification.getCustomerId());
        cardA.setSetLength(notification.getCraft().getPnlLength());
        cardA.setSetWidth(notification.getCraft().getPnlWidth());
        cardA.setPnlDivisor(notification.getCraft().getPnlDivisor());
        cardA.setNo(notification.getEgCardNo());
        cardA.setOldDeailId(notification.getConDeail().getOldDeailId());
        cardA.setDeptId(notification.getDeptId());
        // 曝光丝印区分
        cardA.setProcessValueId(notification.getConDeail().getProcessValueId());
        // 样品量产、新单返单区分
        cardA.setReferenceType(notification.getConDeail().getReferenceType());
        cardA.setDictCardType(notification.getDictCardType());
        if (null != notification.getConDeail() && StringUtils.isNotBlank(notification.getConDeail().getRecordId()))
        {
            cardA.setContractDetailId(notification.getConDeail().getRecordId());
        }
        cardA.setDictOrderType(StringUtils.isNotBlank(cardA.getOldDeailId()) ? "3" : notification.getDictOrderType());
        Icloud_MaterialCard card = generateOrderMsg(cardA);
        card.setDepartName(notification.getDeptName());
        return card;
    }

    public Icloud_MaterialCard generateOrderMsg(Icloud_CardA cardA)
    {
        Icloud_MaterialCard result = erpCraftDao.findMaterialByCraft(cardA);
        Icloud_Branch branch = erpCraftDao.getBranchByCom(cardA.getCompanyId(), cardA.getSaleCompanyId());
        if (result == null)
        {
            result = new Icloud_MaterialCard();
        }
        else
        {
            cardA.setRecordId(result.getCardAId());
        }
        result = getCommonData(result, cardA);
        result = getCompanyPreCent(result, branch);
        result = getNeedDays(result, cardA);
        result = getCapacity(result);
        return result;
    }

    public Icloud_MaterialCard getCommonData(Icloud_MaterialCard result, Icloud_CardA cardA)
    {
        // 设置板子示意图真实路径
        URL url;
        if (StringUtils.isNotBlank(result.getBoardSketchMapPath()))
        {
            // 获取大板
            url = FileManageUtils.getUrl(result.getBoardSketchMapPath());
            if (url != null)
            {
                result.setPath(url.toString());
            }
        }
        if (StringUtils.isNotBlank(result.getPartASketchMapPath()))
        {
            url = FileManageUtils.getUrl(result.getPartASketchMapPath());
            if (url != null)
            {
                result.setPathA(url.toString());
            }
        }
        if (StringUtils.isNotBlank(result.getPartBSketchMapPath()))
        {
            url = FileManageUtils.getUrl(result.getPartBSketchMapPath());
            if (url != null)
            {
                result.setPathB(url.toString());
            }
        }
        result.setSaleCompanyId(cardA.getSaleCompanyId());
        result.setSaleCustName(cardA.getSaleCustName());
        result.setErpCustomerId(cardA.getErpCustomerId());
        result.setSalecustId(cardA.getSalecustId());
        result.setDeptId(cardA.getDeptId());
        result.setCompanyId(cardA.getCompanyId());
        result.setProcessValueId(cardA.getProcessValueId());
        return result;
    }

    public Icloud_MaterialCard getCompanyPreCent(Icloud_MaterialCard result, Icloud_Branch branch)
    {
        if (null != branch)
        {
            BigDecimal hundred = new BigDecimal(100);
            result.setSaleName(branch.getName());
            result.setSaleId(branch.getRecordId());
            result.setJxPrecent(hundred);

            BigDecimal radio = CompanyUtil.getRadio();
            BigDecimal lnPrecent = hundred;
            if (radio != null)
            {
                lnPrecent = lnPrecent.add(radio);
            }
            result.setLnPrecent(lnPrecent);
        }
        return result;
    }

    public List<Icloud_CycleSetup> getHistoryNeedDays(String versionDate, String companyId, String dictCardType,
        String dictOrderType, String processValueId)
    {
        if (StringUtils.isBlank(versionDate))
        {
            return null;
        }
        List<Icloud_CycleSetup> list =
            cycleSetupDao.getCycleList(dictCardType, dictOrderType, companyId, processValueId, versionDate);
        for (Icloud_CycleSetup cycleSetup : list)
        {
            if (StringUtils.isNotBlank(cycleSetup.getProductType()))
            {
                if (!cycleSetup.getProductType().equals(processValueId))
                {
                    continue;
                }
            }
            if (StringUtils.isNotBlank(cycleSetup.getOrderType()))
            {
                if (!cycleSetup.getOrderType().equals(dictOrderType))
                {
                    continue;
                }
            }
            if (StringUtils.isNotBlank(cycleSetup.getCardType()))
            {
                if (!cycleSetup.getCardType().equals(dictCardType))
                {
                    continue;
                }
            }
            cycleSetup.setUseFlag("1");
        }
        return list;
    }

    public Icloud_MaterialCard getNeedDays(Icloud_MaterialCard result, Icloud_CardA cardA)
    {
        if (StringUtils.isBlank(cardA.getVersionDate()))
        {
            String versionDate = cycleSetupDao.getVersionDate(cardA.getCompanyId());
            if (StringUtils.isBlank(versionDate))
            {
                return result;
            }
            cardA.setVersionDate(versionDate);
        }
        BigDecimal needDays = BigDecimal.ZERO;
        List<Icloud_CycleSetup> list = cycleSetupDao.getCycleList(cardA.getDictCardType(),
            cardA.getDictOrderType(),
            cardA.getCompanyId(),
            cardA.getProcessValueId(),
            cardA.getVersionDate());
        String needDayMsg = "";
        for (Icloud_CycleSetup cycleSetup : list)
        {
            if (StringUtils.isNotBlank(cycleSetup.getProductType()))
            {
                if (!cycleSetup.getProductType().equals(cardA.getProcessValueId()))
                {
                    continue;
                }
            }
            if (StringUtils.isNotBlank(cycleSetup.getOrderType()))
            {
                if (!cycleSetup.getOrderType().equals(cardA.getDictOrderType()))
                {
                    continue;
                }
            }
            if (StringUtils.isNotBlank(cycleSetup.getCardType()))
            {
                if (!cycleSetup.getCardType().equals(cardA.getDictCardType()))
                {
                    continue;
                }
            }
            BigDecimal nomalDays = cycleSetup.getNomalDays() == null ? BigDecimal.ZERO : cycleSetup.getNomalDays();
            needDayMsg = (StringUtils.isNotBlank(needDayMsg) ? (needDayMsg + "-") : "") + "交货周期(" + nomalDays + "天)";
            needDays = needDays.add((nomalDays).multiply(new BigDecimal(24)));
            cycleSetup.setUseFlag("1");
        }
        List<Icloud_CycleSetup> versionList = cycleSetupDao.getVersionList(cardA.getCompanyId());
        needDays = needDays.divide(new BigDecimal(24)).setScale(2, RoundingMode.UP);
        result.setNeedDayMsg(needDayMsg);
        result.setNeedDays(needDays);
        result.setCycleSetupList(list);
        result.setVersionDate(cardA.getVersionDate());
        result.setVersionList(versionList);
        return result;
    }

    public Icloud_MaterialCard getCapacity(Icloud_MaterialCard result)
    {
        CompanyUtil companyUtil = new CompanyUtil();
        String deptId = companyUtil.getGroupDeptId(result.getDeptId());
        result.setDeptId(deptId);
        Double partialArea = notificationDao.getPartialArea(result.getSaleCompanyId());
        // 获取最小批次面积
        result.setPartialArea(partialArea == null ? 0d : partialArea);
        if (StringUtils.isBlank(result.getSalecustId()) && StringUtils.isBlank(result.getDeptId()))
        {
            return result;
        }
        String versionDate = capacityDao.getVersionDate(result.getCompanyId());
        result.setCapacityVersionDate(versionDate);
        List<Icloud_CapacityRecord> list =
            capacityDao.getCapacityList(result.getCompanyId(), result.getProcessValueId(), versionDate);
        for (Icloud_CapacityRecord record : list)
        {
            if (StringUtils.isNotBlank(record.getCustomerId()) && record.getCustomerId().equals(result.getSalecustId()))
            {
                // 签订客户产能
                result.setDepartName(null);
                result.setCustomerName(record.getCustomerName());
                result.setCapacity(record.getCapacity());
                // 量产
                result.setOrderCount(record.getAmount());
                // 样品
                result.setSampleOrderCount(record.getSampleAmount());
                // 新单
                result.setNewOrderCount(record.getOrderAmount());
                return result;
            }
        }
        for (Icloud_CapacityRecord record : list)
        {
            if (StringUtils.isNotBlank(record.getDepartId()) && record.getDepartId().equals(result.getDeptId()))
            {
                // 签订部门产能
                result.setCustomerName(null);
                result.setDepartName(record.getDepartName());
                result.setCapacity(record.getCapacity());
                // 量产
                result.setOrderCount(record.getAmount());
                // 样品
                result.setSampleOrderCount(record.getSampleAmount());
                // 新单
                result.setNewOrderCount(record.getOrderAmount());
                return result;
            }
        }
        return result;
    }
}
