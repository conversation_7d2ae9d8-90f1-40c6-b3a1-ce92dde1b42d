<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.hr.payment_center.dao.Hr_ClockInDao">
    <select id="getHitCardList" resultType="Hr_ClockIn">
        select a.*,b.departmentName from hr_punch_setting a
        LEFT JOIN oa_department_relation b ON b.recordId = a.tissueId where a.activeFlag = 1
    </select>

    <update id="delHitCard">
        update hr_punch_setting set activeFlag = 2 where recordId = #{recordId}
    </update>

    <update id="updateHitCard">
        UPDATE hr_punch_setting
        SET tissueId = #{tissueId},
        versioNumber = #{versioNumber},
        startTime = #{startTime},
        endTime = #{endTime},
        lastUpdDate = #{lastUpdDate},
        lastUpdBy = #{lastUpdBy} where recordId = #{recordId}
    </update>

    <insert id="insertHitCard">
        INSERT INTO hr_punch_setting (
            tissueId,
            versioNumber,
            startTime,
            endTime,
            createdDate,
            lastUpdBy,
            lastUpdDate,
            activeFlag
            )
            values
            (
            #{tissueId},
            NOW(),
            #{startTime},
            #{endTime},
            NOW(),
            #{lastUpdBy},
            #{lastUpdDate},
            1
            )
    </insert>

    <select id="getOrganizationList" resultType="Hr_ClockIn">
        SELECT
                CASE a.departmentLevel WHEN 2 THEN a.departmentName
                WHEN 3 THEN CONCAT(b.departmentName,'-',a.departmentName)
                WHEN 4 THEN CONCAT(c.departmentName,'-',b.departmentName,'-',a.departmentName)
                ELSE NULL END AS "showName",
                CASE a.departmentLevel WHEN 2 THEN a.recordId
                WHEN 3 THEN CONCAT(b.recordId,',',a.recordId)
                WHEN 4 THEN CONCAT(c.recordId,',',b.recordId,',',a.recordId)
                ELSE NULL END AS "tissueId"
        FROM oa_department_relation a
        LEFT JOIN oa_department_relation b ON b.recordId = a.allId
        LEFT JOIN oa_department_relation c ON c.recordId = b.allId
        WHERE a.activeFlag = 1 AND FIND_IN_SET(a.departmentLevel,'2,3,4')
        AND a.groupId =
        (
            SELECT groupId FROM oa_md_employee WHERE phone = #{phone} AND activeFlag = 1
            AND workStatus = 1 AND salarySystemId IS NULL ORDER BY createdDate DESC LIMIT 1
        );
    </select>
</mapper>