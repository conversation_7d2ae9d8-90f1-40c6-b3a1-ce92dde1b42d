package com.kyb.pcberp.modules.sys.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

public class UpdateLog extends DataEntity<UpdateLog>
{
    private static final long serialVersionUID = 1L;

    private Integer type; // 类型

    private String dataId; // 具体字段id

    public Integer getType()
    {
        return type;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    public String getDataId()
    {
        return dataId;
    }

    public void setDataId(String dataId)
    {
        this.dataId = dataId;
    }

    // 释义类型
    public String getTypeStr()
    {
        String result = null;
        if(null == type)
        {
            return result;
        }
        switch (type)
        {
            case 1:
                result = "合同明细id";
                break;
            case 2:
                result = "收款对账单id";
                break;
        }
        return result;
    }
}
