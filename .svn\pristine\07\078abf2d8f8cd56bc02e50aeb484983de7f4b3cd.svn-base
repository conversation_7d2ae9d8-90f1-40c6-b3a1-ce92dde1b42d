<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.quality.dao.InspectAttachementsDao">
    <insert id="insert">
        INSERT INTO qc_inspect_attachements(
            inspectId,
            orgfilename,
            realfilename,
            fileurl,
            companyid,
            createdBy,
            createdDate
        ) VALUES (
             #{inspectId},
             #{orgFileName},
             #{realFileName},
             #{fileUrl},
             #{company.recordId},
             #{createdBy.recordId},
             #{createdDate}
                 )
    </insert>

    <select id="findInspectAttachementsList" resultType="InspectAttachements">
        SELECT
            recordId,
            companyId,
            inspectId,
            orgFileName,
            realFileName,
            fileUrl
        FROM
            qc_inspect_attachements
        WHERE
            companyId = #{company.recordId}
          AND inspectId = #{inspectId}
    </select>

    <delete id="delete">
        DELETE FROM qc_inspect_attachements
        WHERE companyid = #{company.recordId}
        AND recordId = #{recordId}
    </delete>
</mapper>