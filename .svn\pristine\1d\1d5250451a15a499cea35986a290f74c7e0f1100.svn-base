/**
 * 
 */
package com.kyb.pcberp.modules.eg.dao;

import java.util.List;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.eg.entity.DrillInfo;

/**
 * 流程卡B通知单关联DAO接口
 * 
 * <AUTHOR>
 * @version 2015-09-24
 */
@MyBatisDao
public interface DrillInfoDao extends CrudDao<DrillInfo>
{
    public List<DrillInfo> findFirstDrillListByCardB(DrillInfo drillInfo);
    
    public List<DrillInfo> findSecondDrillListByCardB(DrillInfo drillInfo);
    
    public int delByCardBAndActiveIsZero(DrillInfo drillInfo);
}