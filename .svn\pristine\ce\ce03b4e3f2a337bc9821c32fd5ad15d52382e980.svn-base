package com.kyb.pcberp.modules.report.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.crm.entity.Customer;

import java.math.BigDecimal;

/**
 * zjn 2019-01-14 新销售报表导出数据
 */

@SuppressWarnings("serial")
public class SaleNewReportData extends DataEntity<SaleNewReportData>
{
    private Customer customer;// 客户
    
    private BigDecimal orderAmount;// 下单金额
    
    private BigDecimal orderArea;// 下单面积
    
    private String payWay;// 数期(结算方式)
    
    private String surfaceProcess;// 工艺 (镀层处理)
    
    private Integer orderNum;// 下单款数
    
    private BigDecimal avgPrice;// 平均价格
    
    private BigDecimal squareMeterArea;// 平均平米
    
    private String boardThickness;// 主要板厚(板材厚度)
    
    private String mainMaterial;// 主要材料(pcb类型、覆铜板材)
    
    private Integer sampleNum;// 样品款数
    
    private Integer freeSampleNum;// 免费样品款数
    
    private Integer sampleChangeMpNum;// 样品转量产款数
    
    private String mpRate;// 转量率
    
    private Integer complaintSum;// 客诉款数
    
    private String complaintRate;// 客诉率
    
    private String deliveryFinishRate;// 准时交货率
    
    private BigDecimal engineeringFee; // 工程费
    
    private BigDecimal othersFee; // 其它费用
    
    private BigDecimal testShelfFee; // 测试架费
    
    private BigDecimal mouldFee; // 模具费

    private String deptName; // 部门名称
    
    @ExcelField(title = "客户名称", align = 2, sort = 10, fieldType= Customer.class, value = ("customer.name"))
    public Customer getCustomer()
    {
        return customer;
    }

    public void setCustomer(Customer customer)
    {
        this.customer = customer;
    }

    @ExcelField(title = "下单金额", align = 2, sort = 15)
    public BigDecimal getOrderAmount()
    {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount)
    {
        this.orderAmount = orderAmount;
    }

    @ExcelField(title = "下单面积", align = 2, sort = 20)
    public BigDecimal getOrderArea()
    {
        return orderArea;
    }

    public void setOrderArea(BigDecimal orderArea)
    {
        this.orderArea = orderArea;
    }

    @ExcelField(title = "数期", align = 2, sort = 25)
    public String getPayWay()
    {
        return payWay;
    }

    public void setPayWay(String payWay)
    {
        this.payWay = payWay;
    }

    @ExcelField(title = "镀层处理", align = 2, sort = 30)
    public String getSurfaceProcess()
    {
        return surfaceProcess;
    }

    public void setSurfaceProcess(String surfaceProcess)
    {
        this.surfaceProcess = surfaceProcess;
    }

    @ExcelField(title = "下单款数", align = 2, sort = 35)
    public Integer getOrderNum()
    {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum)
    {
        this.orderNum = orderNum;
    }

    @ExcelField(title = "平均价格", align = 2, sort = 40)
    public BigDecimal getAvgPrice()
    {
        return avgPrice;
    }

    public void setAvgPrice(BigDecimal avgPrice)
    {
        this.avgPrice = avgPrice;
    }

    @ExcelField(title = "平均平米", align = 2, sort = 45)
    public BigDecimal getSquareMeterArea()
    {
        return squareMeterArea;
    }

    public void setSquareMeterArea(BigDecimal squareMeterArea)
    {
        this.squareMeterArea = squareMeterArea;
    }

    @ExcelField(title = "主要板厚", align = 2, sort = 50)
    public String getBoardThickness()
    {
        return boardThickness;
    }

    public void setBoardThickness(String boardThickness)
    {
        this.boardThickness = boardThickness;
    }

    @ExcelField(title = "主要材料", align = 2, sort = 55)
    public String getMainMaterial()
    {
        return mainMaterial;
    }

    public void setMainMaterial(String mainMaterial)
    {
        this.mainMaterial = mainMaterial;
    }

    @ExcelField(title = "样品款数", align = 2, sort = 60)
    public Integer getSampleNum()
    {
        return sampleNum;
    }

    public void setSampleNum(Integer sampleNum)
    {
        this.sampleNum = sampleNum;
    }

    @ExcelField(title = "免费样品款数", align = 2, sort = 65)
    public Integer getFreeSampleNum()
    {
        return freeSampleNum;
    }

    public void setFreeSampleNum(Integer freeSampleNum)
    {
        this.freeSampleNum = freeSampleNum;
    }

    @ExcelField(title = "样品转量产款数", align = 2, sort = 70)
    public Integer getSampleChangeMpNum()
    {
        return sampleChangeMpNum;
    }

    public void setSampleChangeMpNum(Integer sampleChangeMpNum)
    {
        this.sampleChangeMpNum = sampleChangeMpNum;
    }

    @ExcelField(title = "转量率", align = 2, sort = 75)
    public String getMpRate()
    {
        return mpRate;
    }

    public void setMpRate(String mpRate)
    {
        this.mpRate = mpRate;
    }

    @ExcelField(title = "客诉款数", align = 2, sort = 80)
    public Integer getComplaintSum()
    {
        return complaintSum;
    }

    public void setComplaintSum(Integer complaintSum)
    {
        this.complaintSum = complaintSum;
    }

    @ExcelField(title = "客诉率", align = 2, sort = 85)
    public String getComplaintRate()
    {
        return complaintRate;
    }

    public void setComplaintRate(String complaintRate)
    {
        this.complaintRate = complaintRate;
    }

    @ExcelField(title = "准时交货率", align = 2, sort = 90)
    public String getDeliveryFinishRate()
    {
        return deliveryFinishRate;
    }

    public void setDeliveryFinishRate(String deliveryFinishRate)
    {
        this.deliveryFinishRate = deliveryFinishRate;
    }

    @ExcelField(title = "工程费", align = 2, sort = 95)
    public BigDecimal getEngineeringFee()
    {
        return engineeringFee;
    }

    public void setEngineeringFee(BigDecimal engineeringFee)
    {
        this.engineeringFee = engineeringFee;
    }

    @ExcelField(title = "其它费用", align = 2, sort = 110)
    public BigDecimal getOthersFee()
    {
        return othersFee;
    }

    public void setOthersFee(BigDecimal othersFee)
    {
        this.othersFee = othersFee;
    }

    @ExcelField(title = "测试架费", align = 2, sort = 105)
    public BigDecimal getTestShelfFee()
    {
        return testShelfFee;
    }

    public void setTestShelfFee(BigDecimal testShelfFee)
    {
        this.testShelfFee = testShelfFee;
    }

    @ExcelField(title = "模具费", align = 2, sort = 100)
    public BigDecimal getMouldFee()
    {
        return mouldFee;
    }

    public void setMouldFee(BigDecimal mouldFee)
    {
        this.mouldFee = mouldFee;
    }

    @ExcelField(title = "部门", align = 2, sort = 120)
    public String getDeptName()
    {
        return deptName;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }
}
