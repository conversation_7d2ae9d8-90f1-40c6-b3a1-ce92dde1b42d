/**
 * 
 */
package com.kyb.pcberp.modules.production.web;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.eg.entity.EgProcess;
import com.kyb.pcberp.modules.production.entity.ProduceRecordDaily;
import com.kyb.pcberp.modules.production.service.ProduceRecordDailyService;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

/**
 * 产能评估Controller
 * <AUTHOR>
 * @version 2015-10-12
 */
@Controller
@RequestMapping(value = "${adminPath}/production/capacity")
public class ProduceRecordDailyController extends BaseController {

	@Autowired
	private ProduceRecordDailyService produceRecordDailyService;
	
	
	
	@ModelAttribute
	public ProduceRecordDaily get(@RequestParam(required=false) String id) {
		ProduceRecordDaily entity = null;
		if (StringUtils.isNotBlank(id)){
			entity = produceRecordDailyService.get(id);
		}
		if (entity == null){
			entity = new ProduceRecordDaily();
		}
		return entity;
	}
	
	
	/**
	 * 页面定位
	 * @return
	 */
    @RequestMapping(value = "list")
    public String list()
    {
        return "modules/production/capacity";
    }
	
	
	 /**
     * 分页查询
     * @param notification
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("production:capacity:view")
    @RequestMapping(value = {"page"})
    @ResponseBody
    public Page<ProduceRecordDaily> getlist(@RequestBody ProduceRecordDaily produceRecordDaily, HttpServletRequest request,
        HttpServletResponse response)
    {
        // 设置查询企业编号
        if (produceRecordDaily != null)
        {
        	produceRecordDaily.setCompany(UserUtils.getUser().getCompany());
        }
        
        // 分页查询数据
        Page<ProduceRecordDaily> qpage = new Page<ProduceRecordDaily>(request, response);
        if (StringUtils.isNotBlank(produceRecordDaily.getPageNo()) && StringUtils.isNotBlank(produceRecordDaily.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(produceRecordDaily.getPageNo()));
            qpage.setPageSize(Integer.parseInt(produceRecordDaily.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        Page<ProduceRecordDaily> page = produceRecordDailyService.findPage(qpage, produceRecordDaily);
        
        return page;
    }
	
	
	
	
    /**
     * 导出
     * 
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("production:capacity:edit")
    @RequestMapping(value = "export")
    public String exportFile(HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
        	ProduceRecordDaily produceRecordDaily = new ProduceRecordDaily();
        	produceRecordDaily.setCompany(UserUtils.getUser().getCompany());
            String fileName = "工序" + DateUtils.getDate("yyyyMM") + "日产能数据.xlsx";
            
            Page<ProduceRecordDaily> page = produceRecordDailyService.findPage(new Page<ProduceRecordDaily>(request, response, -1), produceRecordDaily);
            
            new ExportExcel("工序日产能数据", ProduceRecordDaily.class, null).setDataList(page.getList())
                .write(response, fileName)
                .dispose();
                
            return null;
        }
        catch (Exception e)
        {
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
    }
    
    
    
    
    
    @RequiresPermissions("production:capacity:view")
    @RequestMapping(value = {"findProcess"})
    @ResponseBody
	public List<EgProcess> findProcess() {
    	EgProcess process = new EgProcess();
    	process.setCompany(UserUtils.getUser().getCompany());
		return produceRecordDailyService.findProcess(process);
	}
 
    
    

}