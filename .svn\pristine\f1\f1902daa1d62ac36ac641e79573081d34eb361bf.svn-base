package com.kyb.pcberp.modules.approval.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.persistence.DataEntity;

import java.math.BigDecimal;
import java.util.Date;

/** 审批数据备份 */
@SuppressWarnings("serial")
public class Backups extends DataEntity<Backups>
{
    private String craftId; // 厂编Id
    
    private String no; // 工艺要求编号
    
    private String customerModel; // 客户命名的产品类型

    private BigDecimal unitWidth; // PCS宽度，单位mm
    
    private BigDecimal unitLength; // PCS长度，单位mm
    
    private BigDecimal pnlWidth; // PNL宽度，单位mm
    
    private BigDecimal pnlLength; // PNL长度，单位mm
    
    private BigDecimal pnlDivisor; // PNL拼接数,指一个PNL中含有多少个PCS
    
    private String deliverySize; // 交货尺寸，存储格式为：11,11 表示长11mm,宽11mm
    
    private Long materialType; // 材料类别
    
    private Long boardLevel; // 板层数
    
    private Long boardThickness; // 板厚
    
    private Long surfaceProcess; // 表面加工工艺
    
    private Long copperCladThickness; // 覆铜厚度
    
    private Long solderMaskType; // 阻焊类型
    
    private Long characterType; // 字符印刷类型
    
    private Long shapingWay; // 成形方式
    
    private String status; // status
    
    private Long lingeSpacing;// 最小线宽/线距
    
    private Long smallAperture;// 最小孔径
    
    private Long halAhole;// 半孔
    
    private Long buryBlindHole;// 埋盲孔
    
    private Long resistance;// 抗阻
    
    private Long daore; // 导热
    
    private Long naiya; // 耐压
    
    private Long pliesnumber; // 层数
    
    private Long testMethod; // 产品出厂检测方法
    
    private String former; // 原数据
    
    private String revise; // 修改后数据
    
    private BigDecimal price; // 产品单价
    
    private BigDecimal mouldFee; // 模具费
    
    private BigDecimal testShelfFee; // 测试架费
    
    private BigDecimal engineeringFee; // 工程费
    
    private BigDecimal filmFee; // 菲林费用
    
    private BigDecimal othersFee; // 其它费用
    
    private BigDecimal engineeringLimit; // 工程返还费
    
    private BigDecimal mouldLimit; // 模具费返还费
    
    private BigDecimal testShelfLimit; // 测试架费返还费
    
    private BigDecimal orderLowLimit; // 单项产品限额
    
    private String frontStatus; // 提交审批前状态
    
    private String quantity; // 数量
    
    private String craftCompanyId;// 工艺对应的公司
    
    private String contractIcloud;// 云平台合同明细id
    
    private boolean showFlag = false;// 详情显示标记
    
    private Long orderType;// 订单类型
    
    private Long deliveryDays;// 交货期限
    
    private Date deliveryDate;
    
    private String area; // 面积
    
    private String specialCraft; // 特殊工艺
    
    private String throughHole; // 通口个数
    
    private String countersinkHole; // 沉头孔个数
    
    private String board; // 板材
    
    private Long inkType; // 油墨型号
    
    private Date estimateDate;
    
    private Date newDeliveryDays;
    
    private BigDecimal lnPrice; // 龙南单价
    
    private BigDecimal jxPrice; // 江西单价
    
    private String dataDetailIds; // 数据明细id组
    
    private BigDecimal processFee; // 加工费
    
    private BigDecimal materialFee; // 材料费
    
    private BigDecimal costPrice; // 成本单价
    
    private String needBord; // 需要大板
    
    private Integer replyStock; // 采购数量
    
    private String materialId; // 使用大板id
    
    private String occupiedStock; // 占用大板库存
    
    private String estimateMethod; // 交期计算方法
    
    private String productArea; // 提供产能
    
    private String estimateRemark; // 评估结果
    
    private String purchRowDays; // 采购周期
    
    private String notiNeedDays; // 生产周期
    
    private String waitDays; // 待产天数
    
    private String logisticsTime; // 物流时间
    
    private String saleCompanyId; // 销售子公司id
    
    private String totalPcses; // 一张大料可开多少pcs
    
    private String dayAreas; // 销售公司当日产能
    
    private String productionCycleId; // 生产周期id
    
    private Integer oueDays; // 延多少天
    
    private String estimateDateTwo; // 预估最晚交期时间
    
    private String saleCsutomerId; // 销售客户id
    
    private String notiCraftNo;
    
    private String genCraftNo;
    
    private BigDecimal excessStock;
    
    private String estimateStartDate;
    
    private String factoryStocks; // 尾数库存
    
    private String urgentFlag; // 加急标志 1 加急 0 不加急
    
    private Integer usePurchStocks; // 在途库存
    
    private BigDecimal prdEngineeringFee; // 外发工程费
    
    private BigDecimal prdMouldFee; // 外发模具费
    
    private BigDecimal prdTestShelfFee; // 外发测试架费
    
    private BigDecimal madePcsPrice; // 采购费用
    
    private Integer occupyMantissa; // 占用尾数
    
    private Integer loadNums; // 客诉报废补料数量
    
    private BigDecimal compensation; // 客诉赔偿金额
    
    private Integer repirNums; // 客诉修理入库数量
    
    private BigDecimal groupAmount; // 集控承担金额
    
    private Integer fedNum; // 补发数量
    
    private Integer replenishQty; // 补料数量
    
    private Integer mantissaQty; // 尾数数量
    
    private Double salePrecent;

    private BigDecimal prdFilmFee; // 采购菲林费

    private BigDecimal prdOthersFee; // 采购其它费

    private String openId; // 微信id

    private BigDecimal useRatio;

    private String cardTotalPcses;

    private String cardUseRatio;

    private String contractDetailId;

    private String manufacturer;

    private String grossProfitRate; // 毛利率

    private BigDecimal netProfitRate; // 净利率

    private String materialMsg;

    private BigDecimal areaPrice; // 平米单价

    private BigDecimal payMoney; //客户赔偿金额

    private String specification; //规格型号

    private String materialNo; //物料编号

    private String materialFeeT; //额外的材料费

    private String customerId; // 客户id

    private Date finishLossTime; // 亏损下单结束时间

    private String name;

    private BigDecimal prdAmount;

    private BigDecimal bordLength;

    private BigDecimal bordWidth;

    private String productTypeId;

    private String productType;

    private BigDecimal negotiateAreaPrice; // 议价平米单价

    private BigDecimal processCostPrice; // 议价加工单价

    private String supplierQuotationId; // 供应商报价id

    private String supplierId; // 供应商id

    private BigDecimal newSubTotal;

    private String rejectId; // 客诉单id

    private String drillinHole; // 钻孔个数

    private String packingRequirement; // 包装方式

    private String packingRequirementValue;

    private String deptId; // 部门id

    private String userId; // 业务员id

    private String custType; // 1:公司分配 2:自主研发

    public Backups() {
        super();
    }

    public Backups(String id) {
        super(id);
    }
    public String getCraftId()
    {
        return craftId;
    }
    
    public void setCraftId(String craftId)
    {
        this.craftId = craftId;
    }
    
    public String getNo()
    {
        return no;
    }
    
    public void setNo(String no)
    {
        this.no = no;
    }
    
    public String getCustomerModel()
    {
        return customerModel;
    }
    
    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }
    
    public BigDecimal getUnitWidth()
    {
        return unitWidth;
    }
    
    public void setUnitWidth(BigDecimal unitWidth)
    {
        this.unitWidth = unitWidth;
    }
    
    public BigDecimal getUnitLength()
    {
        return unitLength;
    }
    
    public void setUnitLength(BigDecimal unitLength)
    {
        this.unitLength = unitLength;
    }
    
    public BigDecimal getPnlWidth()
    {
        return pnlWidth;
    }
    
    public void setPnlWidth(BigDecimal pnlWidth)
    {
        this.pnlWidth = pnlWidth;
    }
    
    public BigDecimal getPnlLength()
    {
        return pnlLength;
    }
    
    public void setPnlLength(BigDecimal pnlLength)
    {
        this.pnlLength = pnlLength;
    }
    
    public BigDecimal getPnlDivisor()
    {
        return pnlDivisor;
    }
    
    public void setPnlDivisor(BigDecimal pnlDivisor)
    {
        this.pnlDivisor = pnlDivisor;
    }
    
    public String getDeliverySize()
    {
        return deliverySize;
    }
    
    public void setDeliverySize(String deliverySize)
    {
        this.deliverySize = deliverySize;
    }
    
    public Long getMaterialType()
    {
        return materialType;
    }
    
    public void setMaterialType(Long materialType)
    {
        this.materialType = materialType;
    }
    
    public Long getBoardLevel()
    {
        return boardLevel;
    }
    
    public void setBoardLevel(Long boardLevel)
    {
        this.boardLevel = boardLevel;
    }
    
    public Long getBoardThickness()
    {
        return boardThickness;
    }
    
    public void setBoardThickness(Long boardThickness)
    {
        this.boardThickness = boardThickness;
    }
    
    public Long getSurfaceProcess()
    {
        return surfaceProcess;
    }
    
    public void setSurfaceProcess(Long surfaceProcess)
    {
        this.surfaceProcess = surfaceProcess;
    }
    
    public Long getCopperCladThickness()
    {
        return copperCladThickness;
    }
    
    public void setCopperCladThickness(Long copperCladThickness)
    {
        this.copperCladThickness = copperCladThickness;
    }
    
    public Long getSolderMaskType()
    {
        return solderMaskType;
    }
    
    public void setSolderMaskType(Long solderMaskType)
    {
        this.solderMaskType = solderMaskType;
    }
    
    public Long getCharacterType()
    {
        return characterType;
    }
    
    public void setCharacterType(Long characterType)
    {
        this.characterType = characterType;
    }
    
    public Long getShapingWay()
    {
        return shapingWay;
    }
    
    public void setShapingWay(Long shapingWay)
    {
        this.shapingWay = shapingWay;
    }
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public Long getLingeSpacing()
    {
        return lingeSpacing;
    }
    
    public void setLingeSpacing(Long lingeSpacing)
    {
        this.lingeSpacing = lingeSpacing;
    }
    
    public Long getSmallAperture()
    {
        return smallAperture;
    }
    
    public void setSmallAperture(Long smallAperture)
    {
        this.smallAperture = smallAperture;
    }
    
    public Long getHalAhole()
    {
        return halAhole;
    }
    
    public void setHalAhole(Long halAhole)
    {
        this.halAhole = halAhole;
    }
    
    public Long getBuryBlindHole()
    {
        return buryBlindHole;
    }
    
    public void setBuryBlindHole(Long buryBlindHole)
    {
        this.buryBlindHole = buryBlindHole;
    }
    
    public Long getResistance()
    {
        return resistance;
    }
    
    public void setResistance(Long resistance)
    {
        this.resistance = resistance;
    }
    
    public Long getDaore()
    {
        return daore;
    }
    
    public void setDaore(Long daore)
    {
        this.daore = daore;
    }
    
    public Long getNaiya()
    {
        return naiya;
    }
    
    public void setNaiya(Long naiya)
    {
        this.naiya = naiya;
    }
    
    public Long getPliesnumber()
    {
        return pliesnumber;
    }
    
    public void setPliesnumber(Long pliesnumber)
    {
        this.pliesnumber = pliesnumber;
    }
    
    public String getFormer()
    {
        return former;
    }
    
    public void setFormer(String former)
    {
        this.former = former;
    }
    
    public String getRevise()
    {
        return revise;
    }
    
    public void setRevise(String revise)
    {
        this.revise = revise;
    }
    
    public Long getTestMethod()
    {
        return testMethod;
    }
    
    public void setTestMethod(Long testMethod)
    {
        this.testMethod = testMethod;
    }
    
    public BigDecimal getPrice()
    {
        return price;
    }
    
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }
    
    public BigDecimal getMouldFee()
    {
        return mouldFee;
    }
    
    public void setMouldFee(BigDecimal mouldFee)
    {
        this.mouldFee = mouldFee;
    }
    
    public BigDecimal getTestShelfFee()
    {
        return testShelfFee;
    }
    
    public void setTestShelfFee(BigDecimal testShelfFee)
    {
        this.testShelfFee = testShelfFee;
    }
    
    public BigDecimal getEngineeringFee()
    {
        return engineeringFee;
    }
    
    public void setEngineeringFee(BigDecimal engineeringFee)
    {
        this.engineeringFee = engineeringFee;
    }
    
    public BigDecimal getOthersFee()
    {
        return othersFee;
    }
    
    public void setOthersFee(BigDecimal othersFee)
    {
        this.othersFee = othersFee;
    }
    
    public BigDecimal getEngineeringLimit()
    {
        return engineeringLimit;
    }
    
    public void setEngineeringLimit(BigDecimal engineeringLimit)
    {
        this.engineeringLimit = engineeringLimit;
    }
    
    public BigDecimal getMouldLimit()
    {
        return mouldLimit;
    }
    
    public void setMouldLimit(BigDecimal mouldLimit)
    {
        this.mouldLimit = mouldLimit;
    }
    
    public BigDecimal getTestShelfLimit()
    {
        return testShelfLimit;
    }
    
    public void setTestShelfLimit(BigDecimal testShelfLimit)
    {
        this.testShelfLimit = testShelfLimit;
    }
    
    public BigDecimal getOrderLowLimit()
    {
        return orderLowLimit;
    }
    
    public void setOrderLowLimit(BigDecimal orderLowLimit)
    {
        this.orderLowLimit = orderLowLimit;
    }
    
    public String getFrontStatus()
    {
        return frontStatus;
    }
    
    public void setFrontStatus(String frontStatus)
    {
        this.frontStatus = frontStatus;
    }
    
    public BigDecimal getFilmFee()
    {
        return filmFee;
    }
    
    public void setFilmFee(BigDecimal filmFee)
    {
        this.filmFee = filmFee;
    }
    
    public String getQuantity()
    {
        return quantity;
    }
    
    public void setQuantity(String quantity)
    {
        this.quantity = quantity;
    }
    
    public String getCraftCompanyId()
    {
        return craftCompanyId;
    }
    
    public void setCraftCompanyId(String craftCompanyId)
    {
        this.craftCompanyId = craftCompanyId;
    }
    
    public String getContractIcloud()
    {
        return contractIcloud;
    }
    
    public void setContractIcloud(String contractIcloud)
    {
        this.contractIcloud = contractIcloud;
    }
    
    public boolean isShowFlag()
    {
        return showFlag;
    }
    
    public void setShowFlag(boolean showFlag)
    {
        this.showFlag = showFlag;
    }
    
    public Long getOrderType()
    {
        return orderType;
    }
    
    public void setOrderType(Long orderType)
    {
        this.orderType = orderType;
    }
    
    public Long getDeliveryDays()
    {
        return deliveryDays;
    }
    
    public void setDeliveryDays(Long deliveryDays)
    {
        this.deliveryDays = deliveryDays;
    }
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getDeliveryDate()
    {
        return deliveryDate;
    }
    
    public void setDeliveryDate(Date deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }
    
    public String getArea()
    {
        return area;
    }
    
    public void setArea(String area)
    {
        this.area = area;
    }
    
    public String getSpecialCraft()
    {
        return specialCraft;
    }
    
    public void setSpecialCraft(String specialCraft)
    {
        this.specialCraft = specialCraft;
    }

    public String getThroughHole()
    {
        return throughHole;
    }

    public void setThroughHole(String throughHole)
    {
        this.throughHole = throughHole;
    }

    public String getCountersinkHole()
    {
        return countersinkHole;
    }

    public void setCountersinkHole(String countersinkHole)
    {
        this.countersinkHole = countersinkHole;
    }

    public String getBoard()
    {
        return board;
    }

    public void setBoard(String board)
    {
        this.board = board;
    }

    public Long getInkType()
    {
        return inkType;
    }

    public void setInkType(Long inkType)
    {
        this.inkType = inkType;
    }

    public Date getEstimateDate()
    {
        return estimateDate;
    }

    public void setEstimateDate(Date estimateDate)
    {
        this.estimateDate = estimateDate;
    }

    public Date getNewDeliveryDays()
    {
        return newDeliveryDays;
    }

    public void setNewDeliveryDays(Date newDeliveryDays)
    {
        this.newDeliveryDays = newDeliveryDays;
    }

    public BigDecimal getLnPrice()
    {
        return lnPrice;
    }

    public void setLnPrice(BigDecimal lnPrice)
    {
        this.lnPrice = lnPrice;
    }

    public BigDecimal getJxPrice()
    {
        return jxPrice;
    }

    public void setJxPrice(BigDecimal jxPrice)
    {
        this.jxPrice = jxPrice;
    }

    public String getDataDetailIds()
    {
        return dataDetailIds;
    }

    public void setDataDetailIds(String dataDetailIds)
    {
        this.dataDetailIds = dataDetailIds;
    }

    public BigDecimal getProcessFee()
    {
        return processFee;
    }

    public void setProcessFee(BigDecimal processFee)
    {
        this.processFee = processFee;
    }

    public BigDecimal getMaterialFee()
    {
        return materialFee;
    }

    public void setMaterialFee(BigDecimal materialFee)
    {
        this.materialFee = materialFee;
    }

    public BigDecimal getCostPrice()
    {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice)
    {
        this.costPrice = costPrice;
    }

    public String getNeedBord()
    {
        return needBord;
    }

    public void setNeedBord(String needBord)
    {
        this.needBord = needBord;
    }

    public Integer getReplyStock()
    {
        return replyStock;
    }

    public void setReplyStock(Integer replyStock)
    {
        this.replyStock = replyStock;
    }

    public String getMaterialId()
    {
        return materialId;
    }

    public void setMaterialId(String materialId)
    {
        this.materialId = materialId;
    }

    public String getOccupiedStock()
    {
        return occupiedStock;
    }

    public void setOccupiedStock(String occupiedStock)
    {
        this.occupiedStock = occupiedStock;
    }

    public String getEstimateMethod()
    {
        return estimateMethod;
    }

    public void setEstimateMethod(String estimateMethod)
    {
        this.estimateMethod = estimateMethod;
    }

    public String getProductArea()
    {
        return productArea;
    }

    public void setProductArea(String productArea)
    {
        this.productArea = productArea;
    }

    public String getEstimateRemark()
    {
        return estimateRemark;
    }

    public void setEstimateRemark(String estimateRemark)
    {
        this.estimateRemark = estimateRemark;
    }

    public String getPurchRowDays()
    {
        return purchRowDays;
    }

    public void setPurchRowDays(String purchRowDays)
    {
        this.purchRowDays = purchRowDays;
    }

    public String getNotiNeedDays()
    {
        return notiNeedDays;
    }

    public void setNotiNeedDays(String notiNeedDays)
    {
        this.notiNeedDays = notiNeedDays;
    }

    public String getWaitDays()
    {
        return waitDays;
    }

    public void setWaitDays(String waitDays)
    {
        this.waitDays = waitDays;
    }

    public String getLogisticsTime()
    {
        return logisticsTime;
    }

    public void setLogisticsTime(String logisticsTime)
    {
        this.logisticsTime = logisticsTime;
    }

    public String getSaleCompanyId()
    {
        return saleCompanyId;
    }

    public void setSaleCompanyId(String saleCompanyId)
    {
        this.saleCompanyId = saleCompanyId;
    }

    public String getTotalPcses()
    {
        return totalPcses;
    }

    public void setTotalPcses(String totalPcses)
    {
        this.totalPcses = totalPcses;
    }

    public String getDayAreas()
    {
        return dayAreas;
    }

    public void setDayAreas(String dayAreas)
    {
        this.dayAreas = dayAreas;
    }

    public String getProductionCycleId()
    {
        return productionCycleId;
    }

    public void setProductionCycleId(String productionCycleId)
    {
        this.productionCycleId = productionCycleId;
    }

    public Integer getOueDays()
    {
        return oueDays;
    }

    public void setOueDays(Integer oueDays)
    {
        this.oueDays = oueDays;
    }

    public String getEstimateDateTwo()
    {
        return estimateDateTwo;
    }

    public void setEstimateDateTwo(String estimateDateTwo)
    {
        this.estimateDateTwo = estimateDateTwo;
    }

    public String getSaleCsutomerId()
    {
        return saleCsutomerId;
    }

    public void setSaleCsutomerId(String saleCsutomerId)
    {
        this.saleCsutomerId = saleCsutomerId;
    }

    public String getNotiCraftNo()
    {
        return notiCraftNo;
    }

    public void setNotiCraftNo(String notiCraftNo)
    {
        this.notiCraftNo = notiCraftNo;
    }

    public String getGenCraftNo()
    {
        return genCraftNo;
    }

    public void setGenCraftNo(String genCraftNo)
    {
        this.genCraftNo = genCraftNo;
    }

    public BigDecimal getExcessStock()
    {
        return excessStock;
    }

    public void setExcessStock(BigDecimal excessStock)
    {
        this.excessStock = excessStock;
    }

    public String getEstimateStartDate()
    {
        return estimateStartDate;
    }

    public void setEstimateStartDate(String estimateStartDate)
    {
        this.estimateStartDate = estimateStartDate;
    }

    public String getFactoryStocks()
    {
        return factoryStocks;
    }

    public void setFactoryStocks(String factoryStocks)
    {
        this.factoryStocks = factoryStocks;
    }

    public String getUrgentFlag()
    {
        return urgentFlag;
    }

    public void setUrgentFlag(String urgentFlag)
    {
        this.urgentFlag = urgentFlag;
    }

    public Integer getUsePurchStocks()
    {
        return usePurchStocks;
    }

    public void setUsePurchStocks(Integer usePurchStocks)
    {
        this.usePurchStocks = usePurchStocks;
    }

    public BigDecimal getPrdEngineeringFee()
    {
        return prdEngineeringFee;
    }

    public void setPrdEngineeringFee(BigDecimal prdEngineeringFee)
    {
        this.prdEngineeringFee = prdEngineeringFee;
    }

    public BigDecimal getPrdMouldFee()
    {
        return prdMouldFee;
    }

    public void setPrdMouldFee(BigDecimal prdMouldFee)
    {
        this.prdMouldFee = prdMouldFee;
    }

    public BigDecimal getPrdTestShelfFee()
    {
        return prdTestShelfFee;
    }

    public void setPrdTestShelfFee(BigDecimal prdTestShelfFee)
    {
        this.prdTestShelfFee = prdTestShelfFee;
    }

    public BigDecimal getMadePcsPrice()
    {
        return madePcsPrice;
    }

    public void setMadePcsPrice(BigDecimal madePcsPrice)
    {
        this.madePcsPrice = madePcsPrice;
    }

    public Integer getOccupyMantissa()
    {
        return occupyMantissa;
    }

    public void setOccupyMantissa(Integer occupyMantissa)
    {
        this.occupyMantissa = occupyMantissa;
    }

    public Integer getLoadNums()
    {
        return loadNums;
    }

    public BigDecimal getCompensation()
    {
        return compensation;
    }

    public Integer getRepirNums()
    {
        return repirNums;
    }

    public BigDecimal getGroupAmount()
    {
        return groupAmount;
    }

    public Integer getFedNum()
    {
        return fedNum;
    }

    public void setLoadNums(Integer loadNums)
    {
        this.loadNums = loadNums;
    }

    public void setCompensation(BigDecimal compensation)
    {
        this.compensation = compensation;
    }

    public void setRepirNums(Integer repirNums)
    {
        this.repirNums = repirNums;
    }

    public void setGroupAmount(BigDecimal groupAmount)
    {
        this.groupAmount = groupAmount;
    }

    public void setFedNum(Integer fedNum)
    {
        this.fedNum = fedNum;
    }

    public Integer getReplenishQty()
    {
        return replenishQty;
    }

    public Integer getMantissaQty()
    {
        return mantissaQty;
    }

    public void setReplenishQty(Integer replenishQty)
    {
        this.replenishQty = replenishQty;
    }

    public void setMantissaQty(Integer mantissaQty)
    {
        this.mantissaQty = mantissaQty;
    }

    public Double getSalePrecent()
    {
        return salePrecent;
    }

    public void setSalePrecent(Double salePrecent)
    {
        this.salePrecent = salePrecent;
    }

    public BigDecimal getPrdFilmFee()
    {
        return prdFilmFee;
    }

    public void setPrdFilmFee(BigDecimal prdFilmFee)
    {
        this.prdFilmFee = prdFilmFee;
    }

    public BigDecimal getPrdOthersFee()
    {
        return prdOthersFee;
    }

    public void setPrdOthersFee(BigDecimal prdOthersFee)
    {
        this.prdOthersFee = prdOthersFee;
    }

    public String getOpenId()
    {
        return openId;
    }

    public void setOpenId(String openId)
    {
        this.openId = openId;
    }

    public BigDecimal getUseRatio()
    {
        return useRatio;
    }

    public void setUseRatio(BigDecimal useRatio)
    {
        this.useRatio = useRatio;
    }

    public String getCardTotalPcses()
    {
        return cardTotalPcses;
    }

    public void setCardTotalPcses(String cardTotalPcses)
    {
        this.cardTotalPcses = cardTotalPcses;
    }

    public String getCardUseRatio()
    {
        return cardUseRatio;
    }

    public void setCardUseRatio(String cardUseRatio)
    {
        this.cardUseRatio = cardUseRatio;
    }

    public String getContractDetailId()
    {
        return contractDetailId;
    }

    public void setContractDetailId(String contractDetailId)
    {
        this.contractDetailId = contractDetailId;
    }

    public String getManufacturer()
    {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer)
    {
        this.manufacturer = manufacturer;
    }

    public String getGrossProfitRate()
    {
        return grossProfitRate;
    }

    public void setGrossProfitRate(String grossProfitRate)
    {
        this.grossProfitRate = grossProfitRate;
    }

    public String getMaterialMsg()
    {
        return materialMsg;
    }

    public void setMaterialMsg(String materialMsg)
    {
        this.materialMsg = materialMsg;
    }

    public BigDecimal getAreaPrice()
    {
        return areaPrice;
    }

    public void setAreaPrice(BigDecimal areaPrice)
    {
        this.areaPrice = areaPrice;
    }

    public BigDecimal getPayMoney() {
        return payMoney;
    }

    public void setPayMoney(BigDecimal payMoney) {
        this.payMoney = payMoney;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getMaterialNo() {
        return materialNo;
    }

    public void setMaterialNo(String materialNo) {
        this.materialNo = materialNo;
    }

    public String getMaterialFeeT() {
        return materialFeeT;
    }

    public void setMaterialFeeT(String materialFeeT) {
        this.materialFeeT = materialFeeT;
    }

    public BigDecimal getNetProfitRate() {
        return netProfitRate;
    }

    public void setNetProfitRate(BigDecimal netProfitRate) {
        this.netProfitRate = netProfitRate;
    }

    public String getCustomerId()
    {
        return customerId;
    }

    public void setCustomerId(String customerId)
    {
        this.customerId = customerId;
    }

    public Date getFinishLossTime()
    {
        return finishLossTime;
    }

    public void setFinishLossTime(Date finishLossTime)
    {
        this.finishLossTime = finishLossTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getPrdAmount() {
        return prdAmount;
    }

    public void setPrdAmount(BigDecimal prdAmount) {
        this.prdAmount = prdAmount;
    }

    public BigDecimal getBordLength() {
        return bordLength;
    }

    public void setBordLength(BigDecimal bordLength) {
        this.bordLength = bordLength;
    }

    public BigDecimal getBordWidth() {
        return bordWidth;
    }

    public void setBordWidth(BigDecimal bordWidth) {
        this.bordWidth = bordWidth;
    }

    public String getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(String productTypeId) {
        this.productTypeId = productTypeId;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public BigDecimal getNegotiateAreaPrice()
    {
        return negotiateAreaPrice;
    }

    public void setNegotiateAreaPrice(BigDecimal negotiateAreaPrice)
    {
        this.negotiateAreaPrice = negotiateAreaPrice;
    }

    public BigDecimal getProcessCostPrice()
    {
        return processCostPrice;
    }

    public void setProcessCostPrice(BigDecimal processCostPrice)
    {
        this.processCostPrice = processCostPrice;
    }

    public String getSupplierQuotationId()
    {
        return supplierQuotationId;
    }

    public void setSupplierQuotationId(String supplierQuotationId)
    {
        this.supplierQuotationId = supplierQuotationId;
    }

    public String getSupplierId()
    {
        return supplierId;
    }

    public void setSupplierId(String supplierId)
    {
        this.supplierId = supplierId;
    }

    public BigDecimal getNewSubTotal() {
        return newSubTotal;
    }

    public void setNewSubTotal(BigDecimal newSubTotal) {
        this.newSubTotal = newSubTotal;
    }

    public String getRejectId()
    {
        return rejectId;
    }

    public void setRejectId(String rejectId)
    {
        this.rejectId = rejectId;
    }

    public String getDrillinHole() {
        return drillinHole;
    }

    public void setDrillinHole(String drillinHole) {
        this.drillinHole = drillinHole;
    }

    public String getPackingRequirement() {
        return packingRequirement;
    }

    public void setPackingRequirement(String packingRequirement) {
        this.packingRequirement = packingRequirement;
    }

    public String getPackingRequirementValue() {
        return packingRequirementValue;
    }

    public void setPackingRequirementValue(String packingRequirementValue) {
        this.packingRequirementValue = packingRequirementValue;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCustType() {
        return custType;
    }

    public void setCustType(String custType) {
        this.custType = custType;
    }
}
