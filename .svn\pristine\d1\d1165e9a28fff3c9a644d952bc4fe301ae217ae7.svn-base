package com.kyb.pcberp.modules.sys.dao;

import java.util.List;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.sys.entity.Department;
import com.kyb.pcberp.modules.sys.entity.MessageSet;
import com.kyb.pcberp.modules.sys.entity.ParentMessage;
import com.kyb.pcberp.modules.sys.entity.User;

/**
 * 消息设置DAO接口
 * 
 * <AUTHOR>
 * @version 2017-03-02
 */
@MyBatisDao
public interface MessageSetDao extends CrudDao<MessageSetDao>
{
    List<MessageSet> getMsgSetList(MessageSet messageSet);
    
    List<MessageSet> getMsgSetListOne(MessageSet messageSet);
    
    MessageSet getMsgSetListCopy(MessageSet messageSet);
    
    List<MessageSet> getMsgSetListByRole(User user);
    
    List<MessageSet> getMsgSetListByRoleCopy(User user);
    
    void delMsgSetByUser(MessageSet messageSet);
    
    void delMsgSetByUserCopy(MessageSet messageSet);
    
    void delMsgSetByMoudle(MessageSet messageSet);
    
    void updateMsgSet(MessageSet messageSet);
    
    void updateMsgEditSet(MessageSet messageSet);
    
    List<MessageSet> getMsgUser(ParentMessage parentMessage);
    
    MessageSet getPermissions(MessageSet messageSet);
    
    MessageSet getPermissionsByRole(MessageSet messageSet);
    
    List<MessageSet> getMsgSetRoleList(MessageSet messageSet);
    
    MessageSet getMsgSetRole(MessageSet messageSet);
    
    List<User> getUserByDepartmentId(Department Department);
}
