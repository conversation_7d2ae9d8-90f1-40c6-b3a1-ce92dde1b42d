package com.kyb.pcberp.modules.contract.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.production.entity.ProduceBatchDetail;
import com.kyb.pcberp.modules.purch.entity.PurchRaw;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class SaleCombinaOut extends DataEntity<SaleCombinaOut>
{
    private static final long serialVersionUID = 1L;
    
    private String companyId;
    
    private String customerName; // 客户名字
    
    private String no; // 成品代码-厂编
    
    private String customerModel; // 成品名称-客户型号
    
    private String quantity; // 批次数量
    
    private Date deliveryDate; // 交付时间
    
    private String actualQty; // 实际送货数量
    
    private Date actualDeliveryDate; // 实际送货日期
    
    private String customerPo; // 客户订单号
    
    private String bathArea; // 订单面积
    
    private String contractDetailId; // 合同明细id
    
    private String craftId; // 工艺id
    
    private Integer rejectQuatity; // 客诉数量
    
    private BigDecimal normalAmount; // 正常金额
    
    private BigDecimal satisfactionAmount; // 赔偿金额
    
    private Integer alDeliveryQtys; // 客诉已送数量
    
    private String rejectCause; // 客诉原因
    
    private Date rejectLastUpdDate; // 客诉日期
    
    private BigDecimal cardPnlWidth; // 工程卡PNL宽度
    
    private BigDecimal cardPnlLength; // 工程卡PNL长度
    
    private BigDecimal cardPnlDivisor; // 工程卡PNL拼接数
    
    private String cardSet; // 工程卡尺寸
    
    private String progressDesc; // 周期
    
    private String setArea; // 单只工程set尺寸
    
    private String cardSetArea;
    
    private String notificationId;
    
    private Material material;
    
    private String feedingId;
    
    private PurchasingDetail deail;
    
    private String process;
    
    private String numArea;
    
    private long makeTime;
    
    private int overDua;
    
    private String starving;
    
    private Date distributeDate;
    
    private Date startTime;
    
    private Date endTime;
    
    private String showStatus;
    
    private String showCraftNo;
    
    private String contactDeailStatus;
    
    private String referenceTypeQuery;
    
    private String referenceType;
    
    private String alInHouseQty;
    
    private String alInHouseArea;
    
    private String actualArea;
    
    private String notifiNo;
    
    private String waitArea;
    
    private List<PurchRaw> rawList;
    
    private List<ProduceBatchDetail> batchDeailList;
    
    private String estimateRemark;
    
    private String proBathId;
    
    private Integer purchRowDays;
    
    private String oneShortName;
    
    private String twoShortName;
    
    private String threeShortName;
    
    private String contactId;
    
    private String produceBatchDetailId;
    
    private Date issusDate;
    
    private Date distributePlanDate; // 计划投料时间
    
    private Date estimateDate; // 预估交期
    
    private Integer notiNeedDays; // 生产周期
    
    private String proStoreMsg;
    
    private String proStoreArea;
    
    private Date proStoreDate;
    
    private Integer replyStock;
    
    private String productionCycleId;
    
    private String res;
    
    private String cates;
    
    private String realTime;
    
    private Date orderDate;
    
    private String craftNo;
    
    private String orderTypeVal;
    
    private Date deliveryDateCopy;
    
    private String customerId;
    
    private Date acDistributeDate;
    
    private Date estimateAcDistr;
    
    private Date purchDay;
    
    private String purchRowId;
    
    private String feedId;
    
    private String purchDeailId;
    
    private Date purchDeailCreateDate;
    
    private List<RawmaterialStock> storeList;
    
    private BigDecimal rowStoreQua;
    
    private Date rowStoreDate;
    
    private String purchStatus;
    
    private String urgentFlag;
    
    private Date cardMadeDate;
    
    private Date cardAuditDate;
    
    private Integer waitDelivery;
    
    private Integer noticeDelivery;
    
    private Integer stockDelivery;
    
    private Integer acNoticeDelivery;
    
    private Integer acStockDelivery;
    
    private Date stockDeliveryDate;
    
    private String deliverStatus;
    
    private String piciMsg;
    
    private String custShortName;
    
    private String saleId;
    
    private Integer usePurchStocks;
    
    private String usePurchNo;
    
    private String costPrice;
    
    private String status;
    
    private String documentsStatus;
    
    private String factoryNo;
    
    private String saleNo;
    
    private String saleConcatNo;
    
    private String factoryConcatNo;
    
    private String pcsQuantity;
    
    private String oldDeailId;
    
    private Integer proStoreQuantity;
    
    private Integer inspectReworkQty;
    
    private Integer inspectReworkNum;
    
    private Integer inspectDiscardQty;
    
    private Integer inspectDiscardNum;
    
    private Integer inspectInStoreQty;
    
    private Integer replenishQty;
    
    private Integer rejectReworkQty;
    
    private Integer rejectDiscardQty;
    
    private Integer rejectNum;
    
    private Integer rejectReplenishQty;
    
    private Integer rejectReplenishNum;
    
    private Date rejectOutStoreDate;
    
    private String rejectNo;// 合同单号
    
    private Integer rejectQty;// 客诉退货数量
    
    private String saleComId;
    
    private String customerNo;
    
    private String finalSaleMan;

    private String groupCenterType;

    private String madeSupplier;

    private String madeSuplierName;

    @ExcelField(title = "销售", align = 2, sort = 80)
    public String getFinalSaleMan()
    {
        return finalSaleMan;
    }

    public void setFinalSaleMan(String finalSaleMan)
    {
        this.finalSaleMan = finalSaleMan;
    }

    public String getCustomerNo()
    {
        return customerNo;
    }

    public void setCustomerNo(String customerNo)
    {
        this.customerNo = customerNo;
    }

    public String getCompanyId()
    {
        return companyId;
    }
    
    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    @ExcelField(title = "客编", align = 2, sort = 70)
    public String getCustomerName()
    {
        return customerName;
    }
    
    public void setCustomerName(String customerName)
    {
        this.customerName = customerName;
    }

    public String getNo()
    {
        return no;
    }
    
    public void setNo(String no)
    {
        this.no = no;
    }

    @ExcelField(title = "客户型号", align = 2, sort = 100)
    public String getCustomerModel()
    {
        return customerModel;
    }
    
    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }

    @ExcelField(title = "数量", align = 2, sort = 120)
    public String getQuantity()
    {
        return quantity;
    }
    
    public void setQuantity(String quantity)
    {
        this.quantity = quantity;
    }
    
    public String getActualQty()
    {
        return actualQty;
    }
    
    public void setActualQty(String actualQty)
    {
        this.actualQty = actualQty;
    }
    
    public Date getDeliveryDate()
    {
        return deliveryDate;
    }

    @ExcelField(title = "交货时间", align = 2, sort = 150)
    public String getDeliveryDateStr()
    {
        if (deliveryDate != null)
        {
            return DateUtils.formatDate(deliveryDate);
        }
        return "";
    }
    
    public void setDeliveryDate(Date deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }
    
    public String getActualDeliveryDateStr()
    {
        if (actualDeliveryDate != null)
        {
            return DateUtils.formatDate(actualDeliveryDate);
        }
        return "";
    }
    
    public Date getActualDeliveryDate()
    {
        return actualDeliveryDate;
    }
    
    public void setActualDeliveryDate(Date actualDeliveryDate)
    {
        this.actualDeliveryDate = actualDeliveryDate;
    }

    @ExcelField(title = "客户订单号", align = 2, sort = 60)
    public String getCustomerPo()
    {
        return customerPo;
    }
    
    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }

    @ExcelField(title = "面积", align = 2, sort = 130)
    public String getBathArea()
    {
        return bathArea;
    }
    
    public void setBathArea(String bathArea)
    {
        this.bathArea = bathArea;
    }
    
    public String getContractDetailId()
    {
        return contractDetailId;
    }
    
    public void setContractDetailId(String contractDetailId)
    {
        this.contractDetailId = contractDetailId;
    }
    
    public String getCraftId()
    {
        return craftId;
    }
    
    public void setCraftId(String craftId)
    {
        this.craftId = craftId;
    }
    
    public Integer getRejectQuatity()
    {
        return rejectQuatity;
    }
    
    public void setRejectQuatity(Integer rejectQuatity)
    {
        this.rejectQuatity = rejectQuatity;
    }
    
    public BigDecimal getNormalAmount()
    {
        return normalAmount;
    }
    
    public void setNormalAmount(BigDecimal normalAmount)
    {
        this.normalAmount = normalAmount;
    }
    
    public BigDecimal getSatisfactionAmount()
    {
        return satisfactionAmount;
    }
    
    public void setSatisfactionAmount(BigDecimal satisfactionAmount)
    {
        this.satisfactionAmount = satisfactionAmount;
    }
    
    public Integer getAlDeliveryQtys()
    {
        return alDeliveryQtys;
    }
    
    public void setAlDeliveryQtys(Integer alDeliveryQtys)
    {
        this.alDeliveryQtys = alDeliveryQtys;
    }
    
    public String getRejectCause()
    {
        return rejectCause;
    }
    
    public void setRejectCause(String rejectCause)
    {
        this.rejectCause = rejectCause;
    }
    
    public Date getRejectLastUpdDate()
    {
        return rejectLastUpdDate;
    }
    
    public void setRejectLastUpdDate(Date rejectLastUpdDate)
    {
        this.rejectLastUpdDate = rejectLastUpdDate;
    }
    
    public BigDecimal getCardPnlWidth()
    {
        return cardPnlWidth;
    }
    
    public void setCardPnlWidth(BigDecimal cardPnlWidth)
    {
        this.cardPnlWidth = cardPnlWidth;
    }
    
    public BigDecimal getCardPnlLength()
    {
        return cardPnlLength;
    }
    
    public void setCardPnlLength(BigDecimal cardPnlLength)
    {
        this.cardPnlLength = cardPnlLength;
    }
    
    public BigDecimal getCardPnlDivisor()
    {
        return cardPnlDivisor;
    }
    
    public void setCardPnlDivisor(BigDecimal cardPnlDivisor)
    {
        this.cardPnlDivisor = cardPnlDivisor;
    }
    
    public String getCardSet()
    {
        return cardSet;
    }
    
    public void setCardSet(String cardSet)
    {
        this.cardSet = cardSet;
    }

    public String getProgressDesc()
    {
        return progressDesc;
    }
    
    public void setProgressDesc(String progressDesc)
    {
        this.progressDesc = progressDesc;
    }
    
    public String getSetArea()
    {
        return setArea;
    }
    
    public void setSetArea(String setArea)
    {
        this.setArea = setArea;
    }
    
    public String getCardSetArea()
    {
        return cardSetArea;
    }
    
    public void setCardSetArea(String cardSetArea)
    {
        this.cardSetArea = cardSetArea;
    }
    
    public String getNotificationId()
    {
        return notificationId;
    }
    
    public void setNotificationId(String notificationId)
    {
        this.notificationId = notificationId;
    }
    
    public Material getMaterial()
    {
        return material;
    }
    
    public void setMaterial(Material material)
    {
        this.material = material;
    }
    
    public String getFeedingId()
    {
        return feedingId;
    }
    
    public void setFeedingId(String feedingId)
    {
        this.feedingId = feedingId;
    }
    
    public PurchasingDetail getDeail()
    {
        return deail;
    }
    
    public void setDeail(PurchasingDetail deail)
    {
        this.deail = deail;
    }

    public String getProcess()
    {
        return process;
    }
    
    public void setProcess(String process)
    {
        this.process = process;
    }
    
    public String getNumArea()
    {
        return numArea;
    }
    
    public void setNumArea(String numArea)
    {
        this.numArea = numArea;
    }
    
    public long getMakeTime()
    {
        return makeTime;
    }
    
    public void setMakeTime(long makeTime)
    {
        this.makeTime = makeTime;
    }
    
    public int getOverDua()
    {
        return overDua;
    }
    
    public void setOverDua(int overDua)
    {
        this.overDua = overDua;
    }
    
    public String getStarving()
    {
        return starving;
    }
    
    public void setStarving(String starving)
    {
        this.starving = starving;
    }
    
    public Date getDistributeDate()
    {
        return distributeDate;
    }
    
    public String getDistributeDateStr()
    {
        if (distributeDate != null)
        {
            return DateUtils.formatDate(distributeDate);
        }
        return "";
    }
    
    public void setDistributeDate(Date distributeDate)
    {
        this.distributeDate = distributeDate;
    }
    
    public Date getStartTime()
    {
        return startTime;
    }
    
    public void setStartTime(Date startTime)
    {
        this.startTime = startTime;
    }
    
    public Date getEndTime()
    {
        return endTime;
    }
    
    public void setEndTime(Date endTime)
    {
        this.endTime = endTime;
    }
    
    public String getShowStatus()
    {
        return showStatus;
    }
    
    public void setShowStatus(String showStatus)
    {
        this.showStatus = showStatus;
    }

    public String getShowCraftNo()
    {
        return showCraftNo;
    }
    
    public void setShowCraftNo(String showCraftNo)
    {
        this.showCraftNo = showCraftNo;
    }
    
    public String getContactDeailStatus()
    {
        return contactDeailStatus;
    }
    
    public void setContactDeailStatus(String contactDeailStatus)
    {
        this.contactDeailStatus = contactDeailStatus;
    }
    
    public String getReferenceTypeQuery()
    {
        return referenceTypeQuery;
    }
    
    public void setReferenceTypeQuery(String referenceTypeQuery)
    {
        this.referenceTypeQuery = referenceTypeQuery;
    }
    
    public String getReferenceType()
    {
        return referenceType;
    }
    
    public void setReferenceType(String referenceType)
    {
        this.referenceType = referenceType;
    }
    
    public String getAlInHouseQty()
    {
        return alInHouseQty;
    }
    
    public void setAlInHouseQty(String alInHouseQty)
    {
        this.alInHouseQty = alInHouseQty;
    }
    
    public String getAlInHouseArea()
    {
        return alInHouseArea;
    }
    
    public void setAlInHouseArea(String alInHouseArea)
    {
        this.alInHouseArea = alInHouseArea;
    }
    
    public String getActualArea()
    {
        return actualArea;
    }
    
    public void setActualArea(String actualArea)
    {
        this.actualArea = actualArea;
    }
    
    public String getNotifiNo()
    {
        return notifiNo;
    }
    
    public void setNotifiNo(String notifiNo)
    {
        this.notifiNo = notifiNo;
    }
    
    public String getWaitArea()
    {
        return waitArea;
    }
    
    public void setWaitArea(String waitArea)
    {
        this.waitArea = waitArea;
    }
    
    public List<PurchRaw> getRawList()
    {
        return rawList;
    }
    
    public void setRawList(List<PurchRaw> rawList)
    {
        this.rawList = rawList;
    }
    
    public List<ProduceBatchDetail> getBatchDeailList()
    {
        return batchDeailList;
    }
    
    public void setBatchDeailList(List<ProduceBatchDetail> batchDeailList)
    {
        this.batchDeailList = batchDeailList;
    }
    
    public String getEstimateRemark()
    {
        return estimateRemark;
    }
    
    public void setEstimateRemark(String estimateRemark)
    {
        this.estimateRemark = estimateRemark;
    }
    
    public String getProBathId()
    {
        return proBathId;
    }
    
    public void setProBathId(String proBathId)
    {
        this.proBathId = proBathId;
    }
    
    public Integer getPurchRowDays()
    {
        return purchRowDays;
    }
    
    public void setPurchRowDays(Integer purchRowDays)
    {
        this.purchRowDays = purchRowDays;
    }
    
    public String getOneShortName()
    {
        return oneShortName;
    }
    
    public void setOneShortName(String oneShortName)
    {
        this.oneShortName = oneShortName;
    }
    
    public String getTwoShortName()
    {
        return twoShortName;
    }
    
    public void setTwoShortName(String twoShortName)
    {
        this.twoShortName = twoShortName;
    }
    
    public String getThreeShortName()
    {
        return threeShortName;
    }
    
    public void setThreeShortName(String threeShortName)
    {
        this.threeShortName = threeShortName;
    }
    
    public String getContactId()
    {
        return contactId;
    }
    
    public void setContactId(String contactId)
    {
        this.contactId = contactId;
    }
    
    public String getProduceBatchDetailId()
    {
        return produceBatchDetailId;
    }
    
    public void setProduceBatchDetailId(String produceBatchDetailId)
    {
        this.produceBatchDetailId = produceBatchDetailId;
    }
    
    public Date getIssusDate()
    {
        return issusDate;
    }
    
    public void setIssusDate(Date issusDate)
    {
        this.issusDate = issusDate;
    }
    
    public Date getDistributePlanDate()
    {
        return distributePlanDate;
    }
    
    public void setDistributePlanDate(Date distributePlanDate)
    {
        this.distributePlanDate = distributePlanDate;
    }
    
    public Date getEstimateDate()
    {
        return estimateDate;
    }

    public String getEstimateDateStr()
    {
        if (estimateDate != null)
        {
            return DateUtils.formatDate(estimateDate);
        }
        return "";
    }
    
    public void setEstimateDate(Date estimateDate)
    {
        this.estimateDate = estimateDate;
    }
    
    public Integer getNotiNeedDays()
    {
        return notiNeedDays;
    }
    
    public void setNotiNeedDays(Integer notiNeedDays)
    {
        this.notiNeedDays = notiNeedDays;
    }
    
    public String getProStoreMsg()
    {
        return proStoreMsg;
    }
    
    public void setProStoreMsg(String proStoreMsg)
    {
        this.proStoreMsg = proStoreMsg;
    }

    public String getProStoreArea()
    {
        return proStoreArea;
    }
    
    public void setProStoreArea(String proStoreArea)
    {
        this.proStoreArea = proStoreArea;
    }
    
    public Date getProStoreDate()
    {
        return proStoreDate;
    }

    public String getProStoreDateStr()
    {
        if (proStoreDate != null)
        {
            return DateUtils.formatDate(proStoreDate);
        }
        else
        {
            return null;
        }
    }
    
    public void setProStoreDate(Date proStoreDate)
    {
        this.proStoreDate = proStoreDate;
    }

    @ExcelField(title = "计划采购数量", align = 2, sort = 210)
    public Integer getReplyStock()
    {
        return replyStock;
    }
    
    public void setReplyStock(Integer replyStock)
    {
        this.replyStock = replyStock;
    }
    
    public String getProductionCycleId()
    {
        return productionCycleId;
    }
    
    public void setProductionCycleId(String productionCycleId)
    {
        this.productionCycleId = productionCycleId;
    }

    public String getRes()
    {
        return res;
    }
    
    public void setRes(String res)
    {
        this.res = res;
    }
    
    public String getCates()
    {
        return cates;
    }
    
    public void setCates(String cates)
    {
        this.cates = cates;
    }

    public String getRealTime()
    {
        return realTime;
    }
    
    public void setRealTime(String realTime)
    {
        this.realTime = realTime;
    }
    
    public SaleCombinaOut deepClone()
    {
        SaleCombinaOut sale;
        try
        {
            sale = (SaleCombinaOut)super.clone();
            return sale;
        }
        catch (CloneNotSupportedException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return null;
    }
    
    public Date getOrderDate()
    {
        return orderDate;
    }

    @ExcelField(title = "订单时间", align = 2, sort = 140)
    public String getOrderDateStr()
    {
        if (orderDate != null)
        {
            return DateUtils.formatDate(orderDate);
        }
        else
        {
            return null;
        }
    }
    
    public void setOrderDate(Date orderDate)
    {
        this.orderDate = orderDate;
    }

    @ExcelField(title = "厂编", align = 2, sort = 90)
    public String getCraftNo()
    {
        return craftNo;
    }
    
    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }

    @ExcelField(title = "订单类型", align = 2, sort = 110)
    public String getOrderTypeVal()
    {
        if (StringUtils.isNotBlank(urgentFlag) && urgentFlag.equals("1"))
        {
            return "加急";
        }
        else
        {
            return orderTypeVal;
        }
    }
    
    public void setOrderTypeVal(String orderTypeVal)
    {
        this.orderTypeVal = orderTypeVal;
    }
    
    public Date getDeliveryDateCopy()
    {
        return deliveryDateCopy;
    }
    
    public void setDeliveryDateCopy(Date deliveryDateCopy)
    {
        this.deliveryDateCopy = deliveryDateCopy;
    }
    
    public String getCustomerId()
    {
        return customerId;
    }
    
    public void setCustomerId(String customerId)
    {
        this.customerId = customerId;
    }
    
    public Date getAcDistributeDate()
    {
        return acDistributeDate;
    }
    
    public void setAcDistributeDate(Date acDistributeDate)
    {
        this.acDistributeDate = acDistributeDate;
    }

    public String getAcDistributeDateStr()
    {
        if (acDistributeDate != null)
        {
            return DateUtils.formatDate(acDistributeDate);
        }
        return "";
    }
    
    public Date getEstimateAcDistr()
    {
        return estimateAcDistr;
    }
    
    public void setEstimateAcDistr(Date estimateAcDistr)
    {
        this.estimateAcDistr = estimateAcDistr;
    }

    @ExcelField(title = "计划到货时间", align = 2, sort = 180)
    public String getEstimateAcDistrStr()
    {
        if (estimateAcDistr != null)
        {
            return DateUtils.formatDate(estimateAcDistr);
        }
        return "";
    }

    public String getEstimateAcDistrTwoStr()
    {
        if (estimateAcDistr != null)
        {
            return DateUtils.formatDate(estimateAcDistr);
        }
        return "";
    }

    @ExcelField(title = "计划下单时间", align = 2, sort = 170)
    public String getPurchDayStr()
    {
        if (purchDay != null)
        {
            return DateUtils.formatDate(purchDay);
        }
        return "";
    }
    
    public Date getPurchDay()
    {
        return purchDay;
    }
    
    public void setPurchDay(Date purchDay)
    {
        this.purchDay = purchDay;
    }
    
    public String getPurchRowId()
    {
        return purchRowId;
    }
    
    public void setPurchRowId(String purchRowId)
    {
        this.purchRowId = purchRowId;
    }
    
    public String getFeedId()
    {
        return feedId;
    }
    
    public void setFeedId(String feedId)
    {
        this.feedId = feedId;
    }
    
    public String getPurchDeailId()
    {
        return purchDeailId;
    }
    
    public void setPurchDeailId(String purchDeailId)
    {
        this.purchDeailId = purchDeailId;
    }
    
    public Date getPurchDeailCreateDate()
    {
        return purchDeailCreateDate;
    }
    
    public void setPurchDeailCreateDate(Date purchDeailCreateDate)
    {
        this.purchDeailCreateDate = purchDeailCreateDate;
    }

    @ExcelField(title = "实际下单时间", align = 2, sort = 190)
    public String getPurchDeailCreateDateStr()
    {
        if (purchDeailCreateDate != null)
        {
            return DateUtils.formatDate(purchDeailCreateDate);
        }
        return "";
    }
    
    public List<RawmaterialStock> getStoreList()
    {
        return storeList;
    }
    
    public void setStoreList(List<RawmaterialStock> storeList)
    {
        this.storeList = storeList;
    }

    @ExcelField(title = "实际入库数量", align = 2, sort = 220)
    public BigDecimal getRowStoreQua()
    {
        return rowStoreQua;
    }
    
    public void setRowStoreQua(BigDecimal rowStoreQua)
    {
        this.rowStoreQua = rowStoreQua;
    }

    @ExcelField(title = "实际收货时间", align = 2, sort = 200)
    public String getRowStoreDateStr()
    {
        if (rowStoreDate != null)
        {
            return DateUtils.formatDate(rowStoreDate);
        }
        return "";
    }
    
    public Date getRowStoreDate()
    {
        return rowStoreDate;
    }
    
    public void setRowStoreDate(Date rowStoreDate)
    {
        this.rowStoreDate = rowStoreDate;
    }

    @ExcelField(title = "采购状态", align = 2, sort = 160)
    public String getPurchStatus()
    {
        return purchStatus;
    }
    
    public void setPurchStatus(String purchStatus)
    {
        this.purchStatus = purchStatus;
    }
    
    public String getUrgentFlag()
    {
        return urgentFlag;
    }
    
    public void setUrgentFlag(String urgentFlag)
    {
        this.urgentFlag = urgentFlag;
    }
    
    public Date getCardMadeDate()
    {
        return cardMadeDate;
    }
    
    public void setCardMadeDate(Date cardMadeDate)
    {
        this.cardMadeDate = cardMadeDate;
    }

    public String getCardMadeDateStr()
    {
        if (cardMadeDate != null)
        {
            return DateUtils.formatDate(cardMadeDate);
        }
        return "";
    }
    
    public Date getCardAuditDate()
    {
        return cardAuditDate;
    }
    
    public void setCardAuditDate(Date cardAuditDate)
    {
        this.cardAuditDate = cardAuditDate;
    }

    public String getCardAuditDateStr()
    {
        if (cardAuditDate != null)
        {
            return DateUtils.formatDate(cardAuditDate);
        }
        return "";
    }

    @ExcelField(title = "待送货", align = 2, sort = 250)
    public Integer getWaitDeliveryStr()
    {
        Integer waitStocks = 0;
        if (StringUtils.isNotBlank(quantity))
        {
            waitStocks = Integer.valueOf(quantity);
        }
        if (noticeDelivery != null)
        {
            waitStocks -= noticeDelivery;
        }
        if (stockDelivery != null)
        {
            waitStocks -= stockDelivery;
        }
        return waitStocks == null ? 0 : waitStocks;
    }
    
    public Integer getWaitDelivery()
    {
        return waitDelivery;
    }
    
    public void setWaitDelivery(Integer waitDelivery)
    {
        this.waitDelivery = waitDelivery;
    }

    @ExcelField(title = "已安排", align = 2, sort = 260)
    public Integer getNoticeDelivery()
    {
        return noticeDelivery;
    }
    
    public void setNoticeDelivery(Integer noticeDelivery)
    {
        this.noticeDelivery = noticeDelivery;
    }

    @ExcelField(title = "已出库", align = 2, sort = 270)
    public Integer getStockDelivery()
    {
        return stockDelivery;
    }
    
    public void setStockDelivery(Integer stockDelivery)
    {
        this.stockDelivery = stockDelivery;
    }
    
    public Integer getAcNoticeDelivery()
    {
        return acNoticeDelivery;
    }
    
    public void setAcNoticeDelivery(Integer acNoticeDelivery)
    {
        this.acNoticeDelivery = acNoticeDelivery;
    }
    
    public Integer getAcStockDelivery()
    {
        return acStockDelivery;
    }
    
    public void setAcStockDelivery(Integer acStockDelivery)
    {
        this.acStockDelivery = acStockDelivery;
    }
    
    public Date getStockDeliveryDate()
    {
        return stockDeliveryDate;
    }
    
    public void setStockDeliveryDate(Date stockDeliveryDate)
    {
        this.stockDeliveryDate = stockDeliveryDate;
    }

    @ExcelField(title = "送货时间", align = 2, sort = 280)
    public String getStockDeliveryDateStr()
    {
        if (stockDeliveryDate != null)
        {
            return DateUtils.formatDate(stockDeliveryDate);
        }
        return null;
    }

    @ExcelField(title = "送货状态", align = 2, sort = 290)
    public String getDeliverStatus()
    {
        return deliverStatus;
    }
    
    public void setDeliverStatus(String deliverStatus)
    {
        this.deliverStatus = deliverStatus;
    }
    
    public String getPiciMsg()
    {
        return piciMsg;
    }
    
    public void setPiciMsg(String piciMsg)
    {
        this.piciMsg = piciMsg;
    }
    
    public String getCustShortName()
    {
        return custShortName;
    }
    
    public void setCustShortName(String custShortName)
    {
        this.custShortName = custShortName;
    }
    
    public String getSaleId()
    {
        return saleId;
    }
    
    public void setSaleId(String saleId)
    {
        this.saleId = saleId;
    }

    @ExcelField(title = "在途库存数量", align = 2, sort = 230)
    public Integer getUsePurchStocks()
    {
        return usePurchStocks;
    }
    
    public void setUsePurchStocks(Integer usePurchStocks)
    {
        this.usePurchStocks = usePurchStocks;
    }

    @ExcelField(title = "在途采购", align = 2, sort = 240)
    public String getUsePurchNo()
    {
        return usePurchNo;
    }

    public void setUsePurchNo(String usePurchNo)
    {
        this.usePurchNo = usePurchNo;
    }

    public String getCostPrice()
    {
        return costPrice;
    }
    
    public void setCostPrice(String costPrice)
    {
        this.costPrice = costPrice;
    }

    @ExcelField(title = "状态", align = 2, sort = 50)
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }

    @ExcelField(title = "单据状态", align = 2, sort = 40)
    public String getDocumentsStatus()
    {
        return documentsStatus;
    }
    
    public void setDocumentsStatus(String documentsStatus)
    {
        this.documentsStatus = documentsStatus;
    }

    @ExcelField(title = "通知单", align = 2, sort = 20)
    public String getFactoryNo()
    {
        return factoryNo;
    }
    
    public void setFactoryNo(String factoryNo)
    {
        this.factoryNo = factoryNo;
    }

    public String getSaleNo()
    {
        return saleNo;
    }
    
    public void setSaleNo(String saleNo)
    {
        this.saleNo = saleNo;
    }

    public String getSaleConcatNo()
    {
        return saleConcatNo;
    }
    
    public void setSaleConcatNo(String saleConcatNo)
    {
        this.saleConcatNo = saleConcatNo;
    }

    @ExcelField(title = "合同", align = 2, sort = 10)
    public String getFactoryConcatNo()
    {
        return factoryConcatNo;
    }
    
    public void setFactoryConcatNo(String factoryConcatNo)
    {
        this.factoryConcatNo = factoryConcatNo;
    }

    public String getPcsQuantity()
    {
        return pcsQuantity;
    }
    
    public void setPcsQuantity(String pcsQuantity)
    {
        this.pcsQuantity = pcsQuantity;
    }
    
    public String getOldDeailId()
    {
        return oldDeailId;
    }
    
    public void setOldDeailId(String oldDeailId)
    {
        this.oldDeailId = oldDeailId;
    }
    public Integer getProStoreQuantity()
    {
        return proStoreQuantity;
    }
    
    public void setProStoreQuantity(Integer proStoreQuantity)
    {
        this.proStoreQuantity = proStoreQuantity;
    }

    public Integer getReplenishQty()
    {
        return replenishQty;
    }
    
    public void setReplenishQty(Integer replenishQty)
    {
        this.replenishQty = replenishQty;
    }

    @ExcelField(title = "制程返工数", align = 2, sort = 330)
    public Integer getInspectReworkQty()
    {
        return inspectReworkQty;
    }
    
    public void setInspectReworkQty(Integer inspectReworkQty)
    {
        this.inspectReworkQty = inspectReworkQty;
    }

    public Integer getInspectReworkHandleQty()
    {
        return inspectReworkNum;
    }

    @ExcelField(title = "制程返工次数", align = 2, sort = 340)
    public Integer getInspectReworkNum()
    {
        return inspectReworkNum;
    }
    
    public void setInspectReworkNum(Integer inspectReworkNum)
    {
        this.inspectReworkNum = inspectReworkNum;
    }

    @ExcelField(title = "制程报废数", align = 2, sort = 300)
    public Integer getInspectDiscardQty()
    {
        return inspectDiscardQty;
    }
    
    public void setInspectDiscardQty(Integer inspectDiscardQty)
    {
        this.inspectDiscardQty = inspectDiscardQty;
    }

    @ExcelField(title = "制程报废次数", align = 2, sort = 320)
    public Integer getInspectDiscardNum()
    {
        return inspectDiscardNum;
    }
    
    public void setInspectDiscardNum(Integer inspectDiscardNum)
    {
        this.inspectDiscardNum = inspectDiscardNum;
    }

    @ExcelField(title = "制程报废入库数", align = 2, sort = 310)
    public Integer getInspectInStoreQty()
    {
        return inspectInStoreQty;
    }
    
    public void setInspectInStoreQty(Integer inspectInStoreQty)
    {
        this.inspectInStoreQty = inspectInStoreQty;
    }

    @ExcelField(title = "客诉返工数", align = 2, sort = 380)
    public Integer getRejectReworkQty()
    {
        return rejectReworkQty;
    }
    
    public void setRejectReworkQty(Integer rejectReworkQty)
    {
        this.rejectReworkQty = rejectReworkQty;
    }

    @ExcelField(title = "客诉报废数", align = 2, sort = 370)
    public Integer getRejectDiscardQty()
    {
        return rejectDiscardQty;
    }
    
    public void setRejectDiscardQty(Integer rejectDiscardQty)
    {
        this.rejectDiscardQty = rejectDiscardQty;
    }
    
    public Integer getRejectNum()
    {
        return rejectNum;
    }
    
    public void setRejectNum(Integer rejectNum)
    {
        this.rejectNum = rejectNum;
    }

    @ExcelField(title = "客诉补发数", align = 2, sort = 390)
    public Integer getRejectReplenishQty()
    {
        return rejectReplenishQty;
    }
    
    public void setRejectReplenishQty(Integer rejectReplenishQty)
    {
        this.rejectReplenishQty = rejectReplenishQty;
    }

    @ExcelField(title = "客诉补发次数", align = 2, sort = 400)
    public Integer getRejectReplenishNum()
    {
        return rejectReplenishNum;
    }
    
    public void setRejectReplenishNum(Integer rejectReplenishNum)
    {
        this.rejectReplenishNum = rejectReplenishNum;
    }

    @ExcelField(title = "客诉最后时间", align = 2, sort = 410)
    public Date getRejectOutStoreDate()
    {
        return rejectOutStoreDate;
    }
    
    public void setRejectOutStoreDate(Date rejectOutStoreDate)
    {
        this.rejectOutStoreDate = rejectOutStoreDate;
    }

    @ExcelField(title = "客诉编号", align = 2, sort = 350)
    public String getRejectNo()
    {
        return rejectNo;
    }
    
    public void setRejectNo(String rejectNo)
    {
        this.rejectNo = rejectNo;
    }

    @ExcelField(title = "客诉退货数", align = 2, sort = 360)
    public Integer getRejectQty()
    {
        return rejectQty;
    }
    
    public void setRejectQty(Integer rejectQty)
    {
        this.rejectQty = rejectQty;
    }
    
    public String getSaleComId()
    {
        return saleComId;
    }
    
    public void setSaleComId(String saleComId)
    {
        this.saleComId = saleComId;
    }

    public String getGroupCenterType()
    {
        return groupCenterType;
    }

    public void setGroupCenterType(String groupCenterType)
    {
        this.groupCenterType = groupCenterType;
    }

    public String getMadeSupplier()
    {
        return madeSupplier;
    }

    public void setMadeSupplier(String madeSupplier)
    {
        this.madeSupplier = madeSupplier;
    }

    @ExcelField(title = "外发供应商", align = 2, sort = 30)
    public String getMadeSuplierName()
    {
        return madeSuplierName;
    }

    public void setMadeSuplierName(String madeSuplierName)
    {
        this.madeSuplierName = madeSuplierName;
    }
}
