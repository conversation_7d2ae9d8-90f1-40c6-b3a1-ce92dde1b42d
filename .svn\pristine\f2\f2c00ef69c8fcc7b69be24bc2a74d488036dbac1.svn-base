package com.kyb.pcberp.modules.crm.vo;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.springframework.util.StringUtils;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.crm.entity.AccountsReceivable;

public class SaleCostVo extends DataEntity<AccountsReceivable>
{
    
    /**
     * 
     */
    private static final long serialVersionUID = 3071236121894064556L;
    
    private Date sentTimeStartQr; // 时间段查询条件 载体
    
    private Date sentTimeEndQr;
    
    private String sendType;
    
    private String contractNo;
    
    private String craftNo;
    
    private String customerName;// 销售客户名称
    
    private String customerNo;
    
    private String customerModel;
    
    private String companyName;// 销售公司
    
    private String xsCustomerNo;// 销售客户编码
    
    private String inOutType;// 出入库类型
    
    private String inOutQuantity;// 出入库数量
    
    private String orderDeailArea;// 订单面积
    
    private String status;// 出入库状态
    
    private String operateDate; // 出入日期
    
    private String customerPo;
    
    private String quantity;// 订单订量
    
    private String orderSubTotal; // 订单金额
    
    private String prdSubTotal; // 采购金额
    
    private Date createdDate;// 订单创建日期
    
    private String inOutArea;
    
    private String supplierName;
    
    private String madeType;
    
    private String orderMadeCost;// 订单成本
    
    private String inOutOrderSubTotal;// 出库收入
    
    private String inOutOrderMadeCost;// 出库成本
    
    private String orderMonth;// 订单月份
    
    private String exportFlag;//
    
    private String deptName;
    
    private List<SaleCostVo> details;
    
    public Date getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }
    
    public void setSentTimeStartQr(Date sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }
    
    public Date getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }
    
    public void setSentTimeEndQr(Date sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }
    
    @ExcelField(title = "送货类型", align = 2, sort = 190)
    public String getSendType()
    {
        return sendType;
    }
    
    public void setSendType(String sendType)
    {
        this.sendType = sendType;
    }
    
    @ExcelField(title = "合同编号", align = 2, sort = 20)
    public String getContractNo()
    {
        return contractNo;
    }
    
    public void setContractNo(String contractNo)
    {
        this.contractNo = contractNo;
    }
    
    @ExcelField(title = "生产编号", align = 2, sort = 30)
    public String getCraftNo()
    {
        return craftNo;
    }
    
    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }
    
    @ExcelField(title = "客户名称", align = 2, sort = 40)
    public String getCustomerName()
    {
        return customerName;
    }
    
    public void setCustomerName(String customerName)
    {
        this.customerName = customerName;
    }
    
    @ExcelField(title = "客户型号", align = 2, sort = 60)
    public String getCustomerModel()
    {
        return customerModel;
    }
    
    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }
    
    @ExcelField(title = "销售公司", align = 2, sort = 10)
    public String getCompanyName()
    {
        return companyName;
    }
    
    public void setCompanyName(String companyName)
    {
        this.companyName = companyName;
    }
    
    @ExcelField(title = "销售客户编号", align = 2, sort = 45)
    public String getXsCustomerNo()
    {
        return xsCustomerNo;
    }
    
    public void setXsCustomerNo(String xsCustomerNo)
    {
        this.xsCustomerNo = xsCustomerNo;
    }
    
    public String getInOutType()
    {
        return inOutType;
    }
    
    public void setInOutType(String inOutType)
    {
        this.inOutType = inOutType;
    }
    
    @ExcelField(title = "出库数量", align = 2, sort = 70)
    public String getInOutQuantity()
    {
        return inOutQuantity;
    }
    
    public void setInOutQuantity(String inOutQuantity)
    {
        this.inOutQuantity = inOutQuantity;
    }
    
    public String getOrderDeailArea()
    {
        return orderDeailArea;
    }
    
    public void setOrderDeailArea(String orderDeailArea)
    {
        this.orderDeailArea = orderDeailArea;
    }
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    @ExcelField(title = "出库时间", align = 2, sort = 90)
    public String getOperateDate()
    {
        return operateDate;
    }
    
    public void setOperateDate(String operateDate)
    {
        this.operateDate = operateDate;
    }
    
    @ExcelField(title = "客户订单号", align = 2, sort = 50)
    public String getCustomerPo()
    {
        return customerPo;
    }
    
    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }
    
    @ExcelField(title = "订单数量", align = 2, sort = 82)
    public String getQuantity()
    {
        return quantity;
    }
    
    public void setQuantity(String quantity)
    {
        this.quantity = quantity;
    }
    
    @ExcelField(title = "订单金额", align = 2, sort = 84)
    public String getOrderSubTotal()
    {
        return orderSubTotal;
    }
    
    public void setOrderSubTotal(String orderSubTotal)
    {
        this.orderSubTotal = orderSubTotal;
    }
    
    @ExcelField(title = "订单成本", align = 2, sort = 86)
    public String getPrdSubTotal()
    {
        return prdSubTotal;
    }
    
    public void setPrdSubTotal(String prdSubTotal)
    {
        this.prdSubTotal = prdSubTotal;
    }
    
    public Date getCreatedDate()
    {
        return createdDate;
    }
    
    public void setCreatedDate(Date createdDate)
    {
        this.createdDate = createdDate;
    }
    
    @ExcelField(title = "出库面积", align = 2, sort = 80)
    public String getInOutArea()
    {
        if(StringUtils.isEmpty(inOutArea)&&!StringUtils.isEmpty(orderDeailArea)&&!StringUtils.isEmpty(quantity)&&!StringUtils.isEmpty(inOutQuantity))
        {
            inOutArea = new BigDecimal(orderDeailArea).multiply(new BigDecimal(inOutQuantity)).divide(new BigDecimal(quantity),4,BigDecimal.ROUND_HALF_UP).toString();
        }
        return inOutArea;
    }
    
    public void setInOutArea(String inOutArea)
    {
        this.inOutArea = inOutArea;
    }
    
    @ExcelField(title = "终端工厂", align = 2, sort = 100)
    public String getSupplierName()
    {
        return supplierName;
    }
    
    public void setSupplierName(String supplierName)
    {
        this.supplierName = supplierName;
    }
    
    @ExcelField(title = "外发状态", align = 2, sort = 110)
    public String getMadeType()
    {
        return madeType;
    }
    
    public void setMadeType(String madeType)
    {
        this.madeType = madeType;
    }
    
    public String getOrderMadeCost()
    {
        return orderMadeCost;
    }
    
    public void setOrderMadeCost(String orderMadeCost)
    {
        this.orderMadeCost = orderMadeCost;
    }
    
    @ExcelField(title = "出库收入", align = 2, sort = 87)
    public String getInOutOrderSubTotal()
    {
        if(StringUtils.isEmpty(inOutOrderSubTotal)&&!StringUtils.isEmpty(orderSubTotal)&&!StringUtils.isEmpty(quantity)&&!StringUtils.isEmpty(inOutQuantity))
        {
            inOutOrderSubTotal = new BigDecimal(orderSubTotal).multiply(new BigDecimal(inOutQuantity)).divide(new BigDecimal(quantity),4,BigDecimal.ROUND_HALF_UP).toString();
        }
        return inOutOrderSubTotal;
    }
    
    public void setInOutOrderSubTotal(String inOutOrderSubTotal)
    {
        this.inOutOrderSubTotal = inOutOrderSubTotal;
    }
    
    @ExcelField(title = "出库成本", align = 2, sort = 88)
    public String getInOutOrderMadeCost()
    {
        if(StringUtils.isEmpty(inOutOrderMadeCost)&&!StringUtils.isEmpty(prdSubTotal)&&!StringUtils.isEmpty(quantity)&&!StringUtils.isEmpty(inOutQuantity))
        {
            inOutOrderMadeCost = new BigDecimal(prdSubTotal).multiply(new BigDecimal(inOutQuantity)).divide(new BigDecimal(quantity),4,BigDecimal.ROUND_HALF_UP).toString();
        }
        return inOutOrderMadeCost;
    }
    
    public void setInOutOrderMadeCost(String inOutOrderMadeCost)
    {
        this.inOutOrderMadeCost = inOutOrderMadeCost;
    }
    
    @ExcelField(title = "订单月份", align = 2, sort = 180)
    public String getOrderMonth()
    {
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMM");
        if(StringUtils.isEmpty(orderMonth)&&null!=createdDate)
        {
            orderMonth = sf.format(createdDate);
        }
        return orderMonth;
    }
    
    public void setOrderMonth(String orderMonth)
    {
        this.orderMonth = orderMonth;
    }

    public String getCustomerNo()
    {
        return customerNo;
    }

    public void setCustomerNo(String customerNo)
    {
        this.customerNo = customerNo;
    }

    public String getExportFlag()
    {
        return exportFlag;
    }

    public void setExportFlag(String exportFlag)
    {
        this.exportFlag = exportFlag;
    }

    @ExcelField(title = "销售部门", align = 2, sort = 95)
    public String getDeptName()
    {
        return deptName;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }

    public List<SaleCostVo> getDetails()
    {
        return details;
    }

    public void setDetails(List<SaleCostVo> details)
    {
        this.details = details;
    }
    
    public SaleCostVo clone()
    {
        try
        {
            return (SaleCostVo)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return null;
    }
    
}
