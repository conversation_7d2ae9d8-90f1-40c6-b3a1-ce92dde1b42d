package com.kyb.pcberp.modules.stock.web;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.itextpdf.text.DocumentException;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums.DictItemEnum;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.approval.entity.Approval;
import com.kyb.pcberp.modules.approval.utils.ModifyUtils;
import com.kyb.pcberp.modules.eg.entity.CardBAttach;
import com.kyb.pcberp.modules.production.entity.Feeding;
import com.kyb.pcberp.modules.production.entity.Replenish;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;
import com.kyb.pcberp.modules.stock.entity.*;
import com.kyb.pcberp.modules.stock.service.MaterialService;
import com.kyb.pcberp.modules.stock.service.RawmaterialStockService;
import com.kyb.pcberp.modules.stock.vo.MaterialCheckExpVo;
import com.kyb.pcberp.modules.stock.vo.MaterialMonthReportVo;
import com.kyb.pcberp.modules.stock.vo.StockCheckVo;
import com.kyb.pcberp.modules.stock.vo.WaitingMaterialVo;
import com.kyb.pcberp.modules.sys.entity.*;
import com.kyb.pcberp.modules.sys.service.BranchService;
import com.kyb.pcberp.modules.sys.service.SystemService;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import com.kyb.pcberp.modules.wechat.entity.IcloudCompany;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping(value = "${adminPath}/stock/rawmaterial")
public class RawmaterialStockController extends BaseController
{

    @Autowired
    private MaterialService materialService;

    @Autowired
    private RawmaterialStockService rawmaterialStockService;

    @Autowired
    private BranchService branchService;

    @Autowired
    private SystemService systemService;

    @RequestMapping(value = "list")
    public String list(Material material)
    {
        return "modules/stock/rawmaterialstock";
    }

    @RequestMapping(value = "stockAgeView")
    public String stockAgeList()
    {
        return "modules/report/stockAge";
    }

    @RequestMapping(value = "stockMonthView")
    public String stockMonthReportList()
    {
        return "modules/report/stockMonthReport";
    }

    @RequestMapping(value = "waitingMaterialView")
    public String waitingMaterialReportList()
    {
        return "modules/report/notification/waitingMaterial";
    }

    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"load/data"})
    @ResponseBody
    public Map<String, Object> loadData(Boolean queryAll)
    {
        Map<String, Object> maps = Maps.newHashMap();

        // 加载物料编号
        Material material = new Material();
        material.setCompany(UserUtils.getUser().getCompany());
        maps.put("materialTypeList", DictUtils.getValuesByItem(DictItemEnum.MATERIAL_TYPE_STORE));

        User user = UserUtils.getUser();
        Company company = new Company();
        company.setRecordId(user.getCompany().getRecordId());
        if (null != queryAll && !queryAll)
        {
            company.setCreatedBy(user.clone());
        }

        // 加载申请人
        maps.put("storehouseList", materialService.selectStoreHouselist(company));

        // 加载物料规格
        maps.put("materialSpecList", materialService.findSpecs(company));

        // 加载供应商
//        maps.put("supplierList", materialService.selectSupplierlist(company));
        List<DictItem> brandList = systemService.getBrandList();
        maps.put("supplierList", brandList);

        // 权限
        maps.put("view", true);
        maps.put("edit", SecurityUtils.getSubject().isPermitted("stock:rawMaterial:edit"));
        maps.put("manage", SecurityUtils.getSubject().isPermitted("stock:rawMaterial:manage"));

        material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_RAW);
        maps.put("materialList", materialService.getMaterialQueryList(material));

        // 加载库位信息
        MaterialPlace materialPlace = new MaterialPlace();
        materialPlace.setCompanyId(company.getRecordId());
        maps.put("placeList", materialService.getMaterialPlaceList(materialPlace));

        // 加载公司
        Branch br = new Branch();
        br.setCompany(company);
        maps.put("branchList", branchService.findList(br));

        // 获取参数设置的物料价格维护授权开关值
        maps.put("materialPriceSwitchValue",materialService.getParameterSet(company,"materialPrice"));
        // 获取参数设置的物料交期维护授权开关值
        maps.put("materialDeliveryTimeSwitchValue",materialService.getParameterSet(company,"materialDeliveryTime"));

        User userTwo = new User();
        userTwo.setCompany(company);
        List<User> userList = systemService.findUser(userTwo);
        maps.put("userList", userList);
        loadMonthList(maps);
        return maps;
    }

    /**
     * 得到原料列表数据
     *
     * @param material
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"page"})
    @ResponseBody
    public Page<Material> page(@RequestBody Material material, HttpServletRequest request, HttpServletResponse response)
    {
        // 设置查询企业编号
        if (material != null)
        {
            material.setCompany(UserUtils.getUser().getCompany());
        }

        material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_RAW);

        // 分页查询数据
        Page<Material> qpage = new Page<Material>(request, response);
        if (StringUtils.isNotBlank(material.getPageNo()) && StringUtils.isNotBlank(material.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(material.getPageNo()));
            qpage.setPageSize(Integer.parseInt(material.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }

        // 设置排序
        if (StringUtils.isNotBlank(material.getOrderBy()))
        {
            qpage.setOrderBy(material.getOrderBy());
        }
        Page<Material> page = materialService.findProductMPage(qpage, material);

        return page;
    }

    /**
     * 导出所有的原料列表数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "export")
    @ResponseBody
    public String export(Material raw, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            raw.setCompany(UserUtils.getUser().getCompany());
            raw.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_RAW);
            String fileName = "原料数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            Page<Material> qPage = new Page<Material>(request, response, -1);
            // 设置排序
            if (StringUtils.isNotBlank(raw.getOrderBy()))
            {
                qPage.setOrderBy(raw.getOrderBy());
            }
            Page<Material> page = materialService.findProductMPage(new Page<Material>(request, response, -1), raw);
            new ExportExcel("原料数据", Material.class).setDataList(page.getList()).write(response, fileName).dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/modules/stock/rawmaterialstock";
    }

    /**
     * 查找某个原料的出入库记录
     *
     * @param rawMaterial
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"inout/page"})
    @ResponseBody
    public Page<RawmaterialStock> inoutPage(@RequestBody RawmaterialStock rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        if (null != rawMaterial.getQueryAll() && !rawMaterial.getQueryAll())
        {
            rawMaterial.setCreatedBy(user);
        }

        // 设置查询企业编号
        if (rawMaterial != null)
        {
            rawMaterial.setCompany(user.getCompany());
        }
        if (rawMaterial.getInoutTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rawMaterial.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            rawMaterial.setInoutTimeEndQr(deliTime.getTime());
        }
        if (rawMaterial.getInoutTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rawMaterial.getInoutTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            rawMaterial.setInoutTimeStartQr(deliTime.getTime());
        }
        // 分页查询数据
        Page<RawmaterialStock> qpage = new Page<RawmaterialStock>(request, response);
        if (StringUtils.isNotBlank(rawMaterial.getPageNo()) && StringUtils.isNotBlank(rawMaterial.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(rawMaterial.getPageNo()));
            qpage.setPageSize(Integer.parseInt(rawMaterial.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }

        // 设置排序
        if (StringUtils.isNotBlank(rawMaterial.getOrderBy()))
        {
            qpage.setOrderBy(rawMaterial.getOrderBy());
        }
        Page<RawmaterialStock> page = rawmaterialStockService.findPage(qpage, rawMaterial);
        return page;
    }

    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"inoutPlacePage"})
    @ResponseBody
    public Page<RawmaterialStock> inoutPlacePage(@RequestBody RawmaterialStock rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        if (null != rawMaterial.getQueryAll() && !rawMaterial.getQueryAll())
        {
            rawMaterial.setCreatedBy(user);
        }

        // 设置查询企业编号
        if (rawMaterial != null)
        {
            rawMaterial.setCompany(user.getCompany());
        }
        if (rawMaterial.getInoutTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rawMaterial.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            rawMaterial.setInoutTimeEndQr(deliTime.getTime());
        }
        if (rawMaterial.getInoutTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rawMaterial.getInoutTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            rawMaterial.setInoutTimeStartQr(deliTime.getTime());
        }
        // 分页查询数据
        Page<RawmaterialStock> qpage = new Page<RawmaterialStock>(request, response);
        if (StringUtils.isNotBlank(rawMaterial.getPageNo()) && StringUtils.isNotBlank(rawMaterial.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(rawMaterial.getPageNo()));
            qpage.setPageSize(Integer.parseInt(rawMaterial.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }

        // 设置排序
        if (StringUtils.isNotBlank(rawMaterial.getOrderBy()))
        {
            qpage.setOrderBy(rawMaterial.getOrderBy());
        }
        Page<RawmaterialStock> page = rawmaterialStockService.findPlacePage(qpage, rawMaterial);
        return page;
    }

    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"stockAgeReportList"})
    @ResponseBody
    public List<RawmaterialStock> stockAgeReportList(@RequestBody RawmaterialStock rawMaterial)
    {
        List<RawmaterialStock> list = rawmaterialStockService.stockAgeReportList(rawMaterial);
        return list;
    }

    /**
     * rx 修改了汇总导出
     *
     * @param rawMaterial
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "exportInoutPlace")
    @ResponseBody
    public String exportInoutPlace(RawmaterialStock rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            if (null != rawMaterial.getQueryAll() && !rawMaterial.getQueryAll())
            {
                rawMaterial.setCreatedBy(user);
            }

            // 设置查询企业编号
            if (rawMaterial != null)
            {
                rawMaterial.setCompany(user.getCompany());
            }
            String inoutTimeStartQr = request.getParameter("inoutTimeStartQr");
            String inoutTimeEndQr = request.getParameter("inoutTimeEndQr");
            if (StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Date startdate = new Date(Long.parseLong(inoutTimeStartQr));
                rawMaterial.setInoutTimeStartQr(startdate);
            }
            if (StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Date enddate = new Date(Long.parseLong(inoutTimeEndQr));
                rawMaterial.setInoutTimeEndQr(enddate);
            }
            if (rawMaterial.getInoutTimeEndQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(rawMaterial.getInoutTimeEndQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                rawMaterial.setInoutTimeEndQr(deliTime.getTime());
            }
            if (rawMaterial.getInoutTimeStartQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(rawMaterial.getInoutTimeStartQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                rawMaterial.setInoutTimeStartQr(deliTime.getTime());
            }
            String fileName = "物料台账数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";

            if (StringUtils.isNotBlank(rawMaterial.getReportFlag()) && rawMaterial.getReportFlag().equals("1"))
            {
                List<RawmaterialReportExcel> placeList =
                    rawmaterialStockService.findAllPlaceReportPageExcel(rawMaterial);
                new ExportExcel("呆滞报表", RawmaterialReportExcel.class, new Integer(0)).setDataList(placeList, 2)
                    .write(response, fileName)
                    .dispose();
            }
            else if (StringUtils.isNotBlank(rawMaterial.getReportFlag()) && rawMaterial.getReportFlag().equals("2"))
            {
                List<RawStockAgeReportExcel> placeList = rawmaterialStockService.findStockAgeReportExcel(rawMaterial);
                new ExportExcel("库存资金成本", RawStockAgeReportExcel.class, new Integer(0)).setDataList(placeList, 2)
                    .write(response, fileName)
                    .dispose();
            }
            else if (rawMaterial.getMaterial() != null && StringUtils.isNotBlank(rawMaterial.getMaterial()
                .getRecordId()))
            {
                List<RawmaterialStockPlace> placeList = rawmaterialStockService.findPlacePageExcel(rawMaterial);
                new ExportExcel("原料台账数据", RawmaterialStockPlace.class, new Integer(0)).setDataList(placeList, 2)
                    .write(response, fileName)
                    .dispose();
            }
            else
            {
                List<String> headerList = new ArrayList<String>();
                String[] headersPre = {"物料编号", "物料名称", "规格型号","实际板厚", "期初", "累积入库", "累积出库", "结余"};
                for (String s : headersPre)
                {
                    headerList.add(s);
                }
                // 添加归属公司
                Set<String> companyNameSet = new HashSet<String>();// 在途归属
                Set<String> companyNameSetStock = new HashSet<String>();// 在库归属
                String[] headersEnd = {"加权平均单价", "期初单价", "最新采购价","更新时间", "税率", "加权平均含税金额", "不含税金额", "物料类型", "单位", "仓库"};
                List<RawmaterialExcel> placeList = rawmaterialStockService.findAllPlacePageExcel(rawMaterial);
                // 查询默认公司简称
                String defComName = "";
                Branch br = new Branch();
                br.setCompany(user.getCompany());
                br.setBindErpComId(user.getCompany().getRecordId());
                List<Branch> branchs = branchService.findList(br);
                if (!CollectionUtils.isEmpty(branchs))
                {
                    defComName = branchs.get(0).getShortName();
                }
                if (!CollectionUtils.isEmpty(placeList))
                {
                    for (RawmaterialExcel raw : placeList)
                    {
                        if (StringUtils.isNoneBlank(raw.getStockSpec()))
                        {
                            String[] purchSpecS = raw.getStockSpec().replace("/", ",").split(",");
                            for (int i = 0; i < purchSpecS.length; i++)
                            {
                                if (StringUtils.isNoneBlank(purchSpecS[i])
                                    && StringUtils.isNoneBlank(purchSpecS[i].split(":")[0]))
                                {
                                    String comN = purchSpecS[i].split(":")[0];
                                    if (!companyNameSetStock.contains(comN))
                                    {
                                        companyNameSetStock.add(comN);
                                        headerList.add("在库-" + comN);// 保证顺序
                                    }
                                }

                            }
                        }
                    }
                    if (CollectionUtils.isEmpty(companyNameSetStock))
                    {
                        companyNameSet.add(defComName);// 如果公司都为空，用本厂采购公司
                        headerList.add("在库-" + defComName);// 保证顺序
                    }
                    for (RawmaterialExcel raw : placeList)
                    {
                        if (StringUtils.isNoneBlank(raw.getPurchSpec()))
                        {
                            String[] purchSpecS = raw.getPurchSpec().split(",");
                            for (int i = 0; i < purchSpecS.length; i++)
                            {
                                if (StringUtils.isNoneBlank(purchSpecS[i])
                                    && StringUtils.isNoneBlank(purchSpecS[i].split(":")[0]))
                                {
                                    String comN = purchSpecS[i].split(":")[0];
                                    if (!companyNameSet.contains(comN))
                                    {
                                        companyNameSet.add(comN);
                                        headerList.add("在途-" + comN);// 保证顺序
                                    }
                                }

                            }
                        }
                    }
                    if (CollectionUtils.isEmpty(companyNameSet))
                    {
                        companyNameSet.add(defComName);// 如果公司都为空，用本厂采购公司
                        headerList.add("在途-" + defComName);// 保证顺序
                    }
                }

                headerList.addAll(Arrays.asList(headersEnd));
                String[] headers = new String[headerList.size()];
                headerList.toArray(headers);
                ExportExcel excel = new ExportExcel("原料台账数据", headers);
                setDataTwoList(excel, placeList, headers);
                excel.write(response, fileName).dispose();
            }

            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/stock/rawmaterialStock/list";
    }

    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "exportStockAge")
    @ResponseBody
    public String exportStockAge(RawmaterialStock rawMaterial, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            String fileName = "库龄报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<StockAgeReportExcel> placeList = rawmaterialStockService.findStockAgeExcel(rawMaterial);
            new ExportExcel("移动库龄计算", StockAgeReportExcel.class, new Integer(0)).setDataList(placeList, 2)
                .write(response, fileName)
                .dispose();

            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/stock/rawmaterialStock/list";
    }

    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "exportStockAgeAll")
    @ResponseBody
    public String exportStockAgeAll(RawmaterialStock rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            if (null != rawMaterial.getQueryAll() && !rawMaterial.getQueryAll())
            {
                rawMaterial.setCreatedBy(user);
            }

            // 设置查询企业编号
            if (rawMaterial != null)
            {
                rawMaterial.setCompany(user.getCompany());
            }
            String inoutTimeStartQr = request.getParameter("inoutTimeStartQr");
            String inoutTimeEndQr = request.getParameter("inoutTimeEndQr");
            if (StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Date startdate = new Date(Long.parseLong(inoutTimeStartQr));
                rawMaterial.setInoutTimeStartQr(startdate);
            }
            if (StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Date enddate = new Date(Long.parseLong(inoutTimeEndQr));
                rawMaterial.setInoutTimeEndQr(enddate);
            }
            if (rawMaterial.getInoutTimeEndQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(rawMaterial.getInoutTimeEndQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                rawMaterial.setInoutTimeEndQr(deliTime.getTime());
            }
            if (rawMaterial.getInoutTimeStartQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(rawMaterial.getInoutTimeStartQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                rawMaterial.setInoutTimeStartQr(deliTime.getTime());
            }

            String fileName = "库龄明细报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<StockAgeReportExcel> placeList = rawmaterialStockService.findStockAgeAllExcel(rawMaterial);
            new ExportExcel("移动库龄计算", StockAgeReportExcel.class, new Integer(0)).setDataList(placeList, 2)
                .write(response, fileName)
                .dispose();

            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/stock/rawmaterialStock/list";
    }

    public void setDataTwoList(ExportExcel excel, List<RawmaterialExcel> placeList, String[] hearList)
    {
        for (RawmaterialExcel one : placeList)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList)
            {
                String val = "";
                switch (name)
                {
                    case "物料编号":
                        val = one.getMaterialNo();
                        break;
                    case "物料名称":
                        val = one.getMaterialName();
                        break;
                    case "规格型号":
                        val = one.getSpecification();
                        break;
                    case "实际板厚":
                        val = null == one.getActualThickness() ? "" : one.getActualThickness().toString();
                        break;
                    case "期初":
                        val = one.getStartStocks().toString();
                        break;
                    case "累积入库":
                        val = one.getInStocks().toString();
                        break;
                    case "累积出库":
                        val = one.getOutStocks().toString();
                        break;
                    case "结余":
                        val = one.getQuantity().toString();
                        break;
                    case "在库归属":
                        val = one.getStockSpec();
                        break;
                    // "加权平均单价","期初单价","最新采购价","物料类型","单位","仓库"
                    case "加权平均单价":
                        val = one.getPrice().toString();
                        break;
                    case "期初单价":
                        val = one.getStartPrice().toString();
                        break;
                    case "最新采购价":
                        val = one.getNewPurchPrice().toString();
                        break;
                    case "更新时间":
                        val = one.getUpdatePriceDate();
                        break;
                    case "物料类型":
                        val = one.getMaterialTypeVal();
                        break;
                    case "单位":
                        val = one.getUnitValue();
                        break;
                    case "仓库":
                        val = one.getStoreName();
                        break;
                    case "税率":
                        val = one.getTaxDescript();
                        break;
                    case "加权平均含税金额":
                        val = one.getCost() == null ? "" : one.getCost().toString();
                        break;
                    case "不含税金额":
                        val = one.getUnTaxDescriptCost() == null ? "" : one.getUnTaxDescriptCost().toString();
                        break;
                    default:
                        if (name.startsWith("在途-"))
                        {
                            // 归属公司处理
                            String spec = one.getPurchSpec();
                            if (StringUtils.isNoneBlank(spec))
                            {
                                String[] specArr = spec.split(",");
                                for (int j = 0; j < specArr.length; j++)
                                {
                                    if (StringUtils.isNotBlank(specArr[j]))
                                    {
                                        String com = specArr[j].split(":")[0];
                                        if (name.equals("在途-" + com) && specArr[j].split(":").length > 1)
                                        {
                                            val = specArr[j].split(":")[1];
                                        }
                                    }
                                }
                            }
                        }

                        if (name.startsWith("在库-"))
                        {
                            // 归属公司处理
                            String spec = one.getStockSpec();
                            if (StringUtils.isNoneBlank(spec))
                            {
                                String[] specArr = spec.replace("/", ",").split(",");
                                for (int j = 0; j < specArr.length; j++)
                                {
                                    if (StringUtils.isNotBlank(specArr[j]))
                                    {
                                        String com = specArr[j].split(":")[0];
                                        if (name.equals("在库-" + com) && specArr[j].split(":").length > 1)
                                        {
                                            val = specArr[j].split(":")[1];
                                        }
                                    }
                                }
                            }
                        }

                        break;
                }

                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    /**
     * 单价调整记录
     */
    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"adjust/page"})
    @ResponseBody
    public Page<MaterialPriceAdjust> adjustPage(@RequestBody MaterialPriceAdjust adjust, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();

        // 设置查询企业编号
        if (adjust != null)
        {
            adjust.setCompany(user.getCompany());
        }

        if (null != adjust.getQueryAll() && !adjust.getQueryAll())
        {
            adjust.setCreatedBy(user);
        }

        // 分页查询数据
        Page<MaterialPriceAdjust> qpage = new Page<MaterialPriceAdjust>(request, response);
        if (StringUtils.isNotBlank(adjust.getPageNo()) && StringUtils.isNotBlank(adjust.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(adjust.getPageNo()));
            qpage.setPageSize(Integer.parseInt(adjust.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        if (StringUtils.isNotBlank(adjust.getOrderBy()))
        {
            qpage.setOrderBy(adjust.getOrderBy());
        }
        return rawmaterialStockService.findAdjustPage(qpage, adjust);
    }

    /**
     * 导出记录
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "inout/export")
    @ResponseBody
    public String exportInout(RawmaterialStock proStore, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            String inoutTimeStartQr = request.getParameter("inoutTimeStartQr");
            String inoutTimeEndQr = request.getParameter("inoutTimeEndQr");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Date startdate = new Date(Long.parseLong(inoutTimeStartQr));
                proStore.setInoutTimeStartQr(startdate);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Date enddate = new Date(Long.parseLong(inoutTimeEndQr));
                proStore.setInoutTimeEndQr(enddate);
            }
            User user = UserUtils.getUser();
            if (null != proStore.getQueryAll() && !proStore.getQueryAll())
            {
                proStore.setCreatedBy(user);
            }
            proStore.setCompany(user.getCompany());
            String fileName = "原料出入库数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";

            // 设置排序
            Page<RawmaterialStock> qPage = new Page<RawmaterialStock>(request, response, -1);
            if (StringUtils.isNotBlank(proStore.getOrderBy()))
            {
                qPage.setOrderBy(proStore.getOrderBy());
            }

            Page<RawmaterialStock> page = rawmaterialStockService.findPage(qPage, proStore);
            if (page.getList() != null && page.getList().size() > 0)
            {
                for (RawmaterialStock raw : page.getList())
                {
                    if(Objects.isNull(raw.getStatus())){
                        raw.setExportStatus("");
                    }else{
                        if (raw.getStatus() == (TypeKey.BILL_STATUS_NORMAL.intValue()))
                        {
                            raw.setExportStatus("未冲红");
                        }
                        else if (raw.getStatus() == (TypeKey.BILL_STATUS_FOR_WRITEOFF.intValue()))
                        {
                            raw.setExportStatus("冲红单");
                        }
                        else if (raw.getStatus() == (TypeKey.BILL_STATUS_BE_WRITEOFFED.intValue()))
                        {
                            raw.setExportStatus("已冲红");
                        }
                    }
                    if (raw.getInoutType().toString().equals(TypeKey.ST_MATERIAL_INOUTTYPE_FEDING_OUT.toString()))
                    {
                        raw.setExportNo(raw.getReturnsNo());
                    }
                    if (raw.getInoutType().toString().equals(TypeKey.ST_MATERIAL_INOUTTYPE_FEEDING_OUT.toString()))
                    {
                        raw.setExportNo(raw.getCraftNo());
                    }
                }
            }

            new ExportExcel("原料出入库数据", RawmaterialStock.class, new Integer(0)).setDataList(page.getList())
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/stock/rawmaterialStock/list";
    }

    /**
     * 冲红原料
     *
     * @param raw
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:rawMaterial:edit")
    @RequestMapping(value = "inout/rushred")
    @ResponseBody
    public String rushRed(@RequestBody RawmaterialStock raw, HttpServletRequest request, HttpServletResponse response)
    {
        List<RawmaterialStock> rawmaterialStockList = new ArrayList<>();
        int result = 0;
        try
        {
            // zjn 2018-12-18 验证出入库记录对应的对账单明细是否有调整
            /*
             * String validatorStr = FinanceUtils.rawStockValidator(raw); if (!"success".equals(validatorStr)) { return
             * validatorStr; }
             */

            if (raw.getFeeding() != null)
            {
                rawmaterialStockList = rawmaterialStockService.getRawmaterialByfeedingId(raw.getFeeding());
            }
            if (raw.getFeding() != null)
            {
                rawmaterialStockList = rawmaterialStockService.getRawmaterialByfedingId(raw.getFeding());
            }
            result = rawmaterialStockService.returnRedCut(raw);

            if (result == 1)
            {
                return "已经完成冲红,不能重复冲红.";
            }
            else if (result == 2)
            {
                return "投料已生产，不能冲红.";
            }
            else if (result == 3)
            {
                return "补料已生产，不能冲红.";
            }
            else if (result == 4)
            {
                return "冲红数量超过当前库位库存，请调整冲红数量.";
            }
            else if (result == 4)
            {
                return "已经生成应付单，不能进行冲红";
            }
            else if (result == 5)
            {
                return "正在盘点中，不能进行此操作！";
            }
        }
        catch (Exception e)
        {
            return "原料“" + raw.getMaterial().getNo() + "”冲红失败！";
        }

        // zjn 2017-07-28 启用余料开关，提示冲红记录多条
        StringBuffer sbRedCut = new StringBuffer();
        sbRedCut.append("原料");
        if (raw.getFeeding() != null)
        {
            for (RawmaterialStock rawmaterialStock : rawmaterialStockList)
            {
                sbRedCut.append("'").append(rawmaterialStock.getMaterial().getNo()).append("'");
            }

        }
        else if (raw.getFeding() != null)
        {
            for (RawmaterialStock rawmaterialStock : rawmaterialStockList)
            {
                sbRedCut.append("'").append(rawmaterialStock.getMaterial().getNo()).append("'");
            }
        }
        else
        {
            sbRedCut.append("'").append(raw.getMaterial().getNo()).append("'");
        }
        sbRedCut.append("冲红成功");
        return sbRedCut.toString();

    }

    /**
     * zjn 2017-07-17 根据投料单id查询投料出入库记录
     */
    @RequiresPermissions("stock:rawMaterial:edit")
    @RequestMapping(value = "getRawmaterialByfeedingId")
    @ResponseBody
    public List<RawmaterialStock> getRawmaterialByfeedingId(@RequestBody Feeding feeding)
    {
        return rawmaterialStockService.getRawmaterialByfeedingId(feeding);
    }

    /**
     * zjn 2017-07-17 根据补料单id查询投料出入库记录
     */
    @RequiresPermissions("stock:rawMaterial:edit")
    @RequestMapping(value = "getRawmaterialByfedingId")
    @ResponseBody
    public List<RawmaterialStock> getRawmaterialByfedingId(@RequestBody Replenish replenish)
    {
        return rawmaterialStockService.getRawmaterialByfedingId(replenish);
    }

    @RequiresPermissions("stock:rawMaterial:manage")
    @RequestMapping(value = "priceadjust", method = RequestMethod.POST)
    @ResponseBody
    public String adjustPrice(@RequestBody MaterialPriceAdjust adjust)
    {
        return rawmaterialStockService.priceAdjust(adjust);
    }

    @RequiresPermissions("stock:rawMaterial:edit")
    @RequestMapping(value = "editCraft", method = RequestMethod.POST)
    @ResponseBody
    public String editCraft(@RequestBody MaterialSpecification materialSpecification)
    {
        return rawmaterialStockService.editCraft(materialSpecification);
    }

    @RequiresPermissions("stock:rawMaterial:edit")
    @RequestMapping(value = "editLeadTime", method = RequestMethod.POST)
    @ResponseBody
    public String editLeadTime(@RequestBody Material material)
    {
        return rawmaterialStockService.editLeadTime(material);
    }

    @RequiresPermissions("stock:rawMaterial:edit")
    @RequestMapping(value = "editPrice", method = RequestMethod.POST)
    @ResponseBody
    public String editPrice(@RequestBody Material material)
    {
        return rawmaterialStockService.updatePrice(material);
    }

    @RequiresPermissions("stock:rawMaterial:edit")
    @RequestMapping(value = "editSupplier", method = RequestMethod.POST)
    @ResponseBody
    public String editSupplier(@RequestBody Material material)
    {
        return rawmaterialStockService.editSupplier(material);
    }

    @RequestMapping(value = "batchPrintPdf", method = RequestMethod.POST, produces = "application/pdf")
    @ResponseBody
    public ResponseEntity<byte[]> batchPrintPdf(@RequestBody RawmaterialStock rawmaterialStock,
        HttpServletRequest request)
        throws DocumentException, IOException
    {
        Pdf pdf = rawmaterialStockService.batchPrintPdf(rawmaterialStock, request);
        byte[] contents = pdf.getOutPut().toByteArray();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/pdf"));
        String filename = CommonUtils.getDisplayPDFName(pdf.getFileName());
        headers.add("X-File-Name", filename);
        headers.setContentDispositionFormData(filename, filename);
        ResponseEntity<byte[]> response = new ResponseEntity<byte[]>(contents, headers, HttpStatus.OK);
        return response;
    }

    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"materialMonthPage"})
    @ResponseBody
    public Page<RawmaterialStock> materialMonthPage(@RequestBody RawmaterialStock rawMaterial,
        HttpServletRequest request, HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        if (null != rawMaterial.getQueryAll() && !rawMaterial.getQueryAll())
        {
            rawMaterial.setCreatedBy(user);
        }

        // 设置查询企业编号
        if (rawMaterial != null)
        {
            rawMaterial.setCompany(user.getCompany());
        }
        Calendar deliTime = Calendar.getInstance();
        if (rawMaterial.getInoutTimeEndQr() != null)
        {
            deliTime.setTime(rawMaterial.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            rawMaterial.setInoutTimeEndQr(deliTime.getTime());
        }
        Calendar deliTimeStart = Calendar.getInstance();
        if (rawMaterial.getInoutTimeStartQr() != null)
        {

            deliTimeStart.setTime(rawMaterial.getInoutTimeStartQr());
            deliTimeStart.set(Calendar.HOUR_OF_DAY, 00);
            deliTimeStart.set(Calendar.MINUTE, 00);
            deliTimeStart.set(Calendar.SECOND, 00);
            deliTimeStart.set(Calendar.MILLISECOND, 00);
            rawMaterial.setInoutTimeStartQr(deliTimeStart.getTime());
        }
        // 分页查询数据
        Page<RawmaterialStock> qpage = new Page<RawmaterialStock>(request, response);
        if (StringUtils.isNotBlank(rawMaterial.getPageNo()) && StringUtils.isNotBlank(rawMaterial.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(rawMaterial.getPageNo()));
            qpage.setPageSize(Integer.parseInt(rawMaterial.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }

        // 设置排序
        if (StringUtils.isNotBlank(rawMaterial.getOrderBy()))
        {
            qpage.setOrderBy(rawMaterial.getOrderBy());
        }
        return rawmaterialStockService.findMonthReportPage(qpage, rawMaterial);

    }

    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "exportMaterialMonth")
    @ResponseBody
    public String exportMaterialMonth(RawmaterialStock rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            if (null != rawMaterial.getQueryAll() && !rawMaterial.getQueryAll())
            {
                rawMaterial.setCreatedBy(user);
            }

            // 设置查询企业编号
            if (rawMaterial != null)
            {
                rawMaterial.setCompany(user.getCompany());
            }
            String inoutTimeStartQr = request.getParameter("inoutTimeStartQr");
            String inoutTimeEndQr = request.getParameter("inoutTimeEndQr");
            if (StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Date startdate = new Date(Long.parseLong(inoutTimeStartQr));
                rawMaterial.setInoutTimeStartQr(startdate);
            }
            if (StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Date enddate = new Date(Long.parseLong(inoutTimeEndQr));
                rawMaterial.setInoutTimeEndQr(enddate);
            }
            if (rawMaterial.getInoutTimeEndQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(rawMaterial.getInoutTimeEndQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                rawMaterial.setInoutTimeEndQr(deliTime.getTime());
            }
            if (rawMaterial.getInoutTimeStartQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(rawMaterial.getInoutTimeStartQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                rawMaterial.setInoutTimeStartQr(deliTime.getTime());
            }
            String fileName = "原料期间结存表数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<RawmaterialStock> placeList = rawmaterialStockService.findMaterialMonthExcel(rawMaterial);
            new ExportExcel("原料期间结存表数据", MaterialMonthReportVo.class, new Integer(0)).setDataList(placeList, 2)
                .write(response, fileName)
                .dispose();

            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/stock/rawmaterialStock/list";
    }

    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "exportMonthCheck")
    @ResponseBody
    public String exportMonthCheck(RawmaterialStock rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            if (null != rawMaterial.getQueryAll() && !rawMaterial.getQueryAll())
            {
                rawMaterial.setCreatedBy(user);
            }

            // 设置查询企业编号
            if (rawMaterial != null)
            {
                rawMaterial.setCompany(user.getCompany());
            }
            String inoutTimeStartQr = request.getParameter("inoutTimeStartQr");
            String inoutTimeEndQr = request.getParameter("inoutTimeEndQr");
            if (StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Date startdate = new Date(Long.parseLong(inoutTimeStartQr));
                rawMaterial.setInoutTimeStartQr(startdate);
            }
            if (StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Date enddate = new Date(Long.parseLong(inoutTimeEndQr));
                rawMaterial.setInoutTimeEndQr(enddate);
            }
            if (rawMaterial.getInoutTimeEndQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(rawMaterial.getInoutTimeEndQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                rawMaterial.setInoutTimeEndQr(deliTime.getTime());
            }
            if (rawMaterial.getInoutTimeStartQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(rawMaterial.getInoutTimeStartQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                rawMaterial.setInoutTimeStartQr(deliTime.getTime());
            }
            String fileName = "标记错误月份报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<RawmaterialStock> placeList = rawmaterialStockService.findMaterialMonthExcel(rawMaterial);
            checkMonthDate(placeList);
            new ExportExcel("标记错误月份报表", MaterialCheckExpVo.class, new Integer(0)).setDataList(placeList, 2)
                .write(response, fileName)
                .dispose();

            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/stock/rawmaterialStock/list";
    }

    /**
     * 不能检查跨年的 物流id+月份 结余 是否等于 物流id+(月份+1)期初
     *
     * @param placeList
     */
    private void checkMonthDate(List<RawmaterialStock> placeList)
    {
        //
        if (!CollectionUtils.isEmpty(placeList))
        {
            for (RawmaterialStock stock : placeList)
            {
                // 物流id+月份 结余 是否等于 物流id+(月份+1)期初
                for (RawmaterialStock next : placeList)
                {
                    if (!StringUtils.isEmpty(stock.getMaterialNo()) && stock.getMaterialNo()
                        .equals(next.getMaterialNo()))
                    {
                        if ((stock.getMonthNum() + 1) == next.getMonthNum())
                        {
                            if (stock.getQuantity().compareTo(next.getStartStocks()) != 0)
                            {
                                next.setRemark(next.getStartStocks().subtract(stock.getQuantity()).toString());
                            }
                            break;
                        }
                    }
                }
            }
        }

    }

    @RequiresPermissions("stock:rawMaterial:edit")
    @RequestMapping(value = "getMatPlaceComData", method = RequestMethod.POST)
    @ResponseBody
    public List<MaterialPlaceCom> getMatPlaceComData(@RequestBody Material material)
    {
        return rawmaterialStockService.getMatPlaceComData(material);
    }

    @RequiresPermissions("stock:rawMaterial:edit")
    @RequestMapping(value = "transferLibrary", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> transferLibrary(@RequestBody MaterialPlaceCom materialPlaceCom)
    {
        return rawmaterialStockService.transferLibrary(materialPlaceCom);
    }

    /**
     * @param rawMaterial
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"getStockCheckInfo"})
    @ResponseBody
    public List<StockCheckVo> getStockCheckInfo(@RequestBody RawmaterialStock rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        if (null != rawMaterial.getQueryAll() && !rawMaterial.getQueryAll())
        {
            rawMaterial.setCreatedBy(user);
        }

        // 设置查询企业编号
        if (rawMaterial != null)
        {
            rawMaterial.setCompany(user.getCompany());
        }
        if (rawMaterial.getInoutTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rawMaterial.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            rawMaterial.setInoutTimeEndQr(deliTime.getTime());
        }
        if (rawMaterial.getInoutTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rawMaterial.getInoutTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            rawMaterial.setInoutTimeStartQr(deliTime.getTime());
        }
        // 分页查询数据
        Page<StockCheckVo> qpage = new Page<StockCheckVo>(request, response);
        if (StringUtils.isNotBlank(rawMaterial.getPageNo()) && StringUtils.isNotBlank(rawMaterial.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(rawMaterial.getPageNo()));
            qpage.setPageSize(Integer.parseInt(rawMaterial.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }

        // 设置排序
        if (StringUtils.isNotBlank(rawMaterial.getOrderBy()))
        {
            qpage.setOrderBy(rawMaterial.getOrderBy());
        }
        String checkType = rawMaterial.getCheckType() == null ? "1" : rawMaterial.getCheckType();
        List<StockCheckVo> result = rawmaterialStockService.findStockCheckList(rawMaterial, checkType);
        return result;
    }

    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "checkDeailExport")
    @ResponseBody
    public String checkDeailExport(RawmaterialStock rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            if (null != rawMaterial.getQueryAll() && !rawMaterial.getQueryAll())
            {
                rawMaterial.setCreatedBy(user);
            }

            // 设置查询企业编号
            if (rawMaterial != null)
            {
                rawMaterial.setCompany(user.getCompany());
            }
            String inoutTimeStartQr = request.getParameter("inoutTimeStartQr");
            String inoutTimeEndQr = request.getParameter("inoutTimeEndQr");
            String checkType = request.getParameter("checkType") == null ? "1" : request.getParameter("checkType");
            if (StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Date startdate = new Date(Long.parseLong(inoutTimeStartQr));
                rawMaterial.setInoutTimeStartQr(startdate);
            }
            if (StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Date enddate = new Date(Long.parseLong(inoutTimeEndQr));
                rawMaterial.setInoutTimeEndQr(enddate);
            }
            if (rawMaterial.getInoutTimeEndQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(rawMaterial.getInoutTimeEndQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                rawMaterial.setInoutTimeEndQr(deliTime.getTime());
            }
            if (rawMaterial.getInoutTimeStartQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(rawMaterial.getInoutTimeStartQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                rawMaterial.setInoutTimeStartQr(deliTime.getTime());
            }
            String fileName = "异常数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<StockCheckVo> result = rawmaterialStockService.findStockCheckList(rawMaterial, checkType);

            List<String> headerList = new ArrayList<String>();
            String[] headersPre = {"物料编号"};
            for (String s : headersPre)
            {
                headerList.add(s);
            }

            if (!CollectionUtils.isEmpty(result))
            {
                for (StockCheckVo vo : result.get(0).getList())
                {
                    if ("2".equals(checkType))
                    {
                        headerList.add(vo.getStartMonth() + "-" + vo.getEndMonth() + "M");
                    }
                    else
                    {
                        headerList.add(vo.getQueryMonth() + "M期初");
                        headerList.add(vo.getQueryMonth() + "M结余");
                    }
                }
            }
            String[] headers = new String[headerList.size()];
            headerList.toArray(headers);
            ExportExcel excel = new ExportExcel("异常数据", headers);
            setCheckDeailTwoList(excel, result, headers, checkType);
            excel.write(response, fileName).dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/stock/rawmaterialStock/list";
    }

    private void setCheckDeailTwoList(ExportExcel excel, List<StockCheckVo> result, String[] headers, String checkType)
    {

        for (StockCheckVo one : result)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : headers)
            {
                String val = "";
                switch (name)
                {
                    case "物料编号":
                        val = one.getMaterialNo();
                        break;
                    default:
                        if ("2".equals(checkType))
                        {
                            // 开始月份
                            int monthNum =
                                StringUtils.isNotBlank(name.split("-")[0]) ? Integer.parseInt(name.split("-")[0]) : -1;
                            for (StockCheckVo v : one.getList())
                            {
                                if (v.getQueryMonth() == monthNum)
                                {
                                    // 期初:{{items.startStock}}--结余:{{items.endStock}}
                                    val = strs(v.getEndStock().toString());
                                    break;
                                }
                            }
                        }

                        if ("1".equals(checkType))
                        {
                            // 开始月份
                            int monthNum =
                                StringUtils.isNotBlank(name.split("M")[0]) ? Integer.parseInt(name.split("M")[0]) : -1;
                            for (StockCheckVo v : one.getList())
                            {
                                if (v.getQueryMonth() == monthNum)
                                {
                                    // 期初:{{items.startStock}}--结余:{{items.endStock}}
                                    if (name.contains("期初"))
                                    {
                                        val = strs(v.getStartStock().toString());
                                    }
                                    else if (name.contains("结余"))
                                    {
                                        val = strs(v.getEndStock().toString());
                                    }

                                }
                            }
                        }

                        break;
                }

                excel.addCell(row, colunm++, val, 2, null);
            }
        }

    }

    private String strs(String str)
    {
        if (str.indexOf(".") > 0)
        {
            str = str.replaceAll("0+?$", "");// 删掉尾数为0的字符
            str = str.replaceAll("[.]$", "");// 结尾如果是小数点，则去掉
        }
        return str;
    }

    public void loadMonthList(Map<String, Object> data)
    {
        Calendar cal = Calendar.getInstance();
        List<Integer> periodList = Lists.newArrayList();
        try
        {
            periodList = DateUtils.getPeriodList(null, null);
        }
        catch (ParseException e)
        {
            e.printStackTrace();
        }
        Integer period1 = null;
        Integer month = cal.get(Calendar.MONTH) + 2;
        if (month > 12)
        {
            period1 = (cal.get(Calendar.YEAR) + 1) * 100 + (month - 12);
        }
        else
        {
            period1 = cal.get(Calendar.YEAR) * 100 + (month);
        }
        periodList.add(period1);
        data.put("periodList", periodList);

        cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.MONTH, -1);
        String period = DateUtils.formatDate(cal.getTime(), "yyyyMM");
        data.put("period", period);
    }

    /**
     * 初始化月期初数据
     *
     * @throws ParseException
     */
    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "resetMonthBalance")
    @ResponseBody
    public void resetMonthBalance()
        throws ParseException
    {
        RawmaterialStock rawMaterial = new RawmaterialStock();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String beginDate = "2021-01-01 00:00:00";
        String endDate = "2021-10-31 23:59:59";
        rawMaterial.setInoutTimeStartQr(sf.parse(beginDate));
        rawMaterial.setInoutTimeEndQr(sf.parse(endDate));
        rawmaterialStockService.insertMaterialMonth(rawMaterial);
        // List<RawmaterialStock> placeList = rawmaterialStockService.findMaterialMonthExcel(rawMaterial);
        // rawmaterialStockService.resetStockMoney();
    }

    /**
     * 得到原料列表数据
     *
     * @param material
     * @param request
     * @param response
     * @return
     */
    
    @RequestMapping(value = {"waitingMaterialPage"})
    @ResponseBody
    public Page<WaitingMaterialVo> waitingMaterialPage(@RequestBody WaitingMaterialVo vo, HttpServletRequest request,
        HttpServletResponse response)
    {
        // 设置查询企业编号
        if (vo != null)
        {
            vo.setCompany(UserUtils.getUser().getCompany());
        }

        // 分页查询数据
        Page<WaitingMaterialVo> qpage = new Page<WaitingMaterialVo>(request, response);
        if (StringUtils.isNotBlank(vo.getPageNo()) && StringUtils.isNotBlank(vo.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(vo.getPageNo()));
            qpage.setPageSize(Integer.parseInt(vo.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }

        // 设置排序
        if (StringUtils.isNotBlank(vo.getOrderBy()))
        {
            qpage.setOrderBy(vo.getOrderBy());
        }
        Page<WaitingMaterialVo> page = materialService.findWaitingMaterialPage(qpage, vo);

        return page;
    }

    /**
     * 导出所有的原料列表数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "exportWaitingMaterial")
    @ResponseBody
    public String exportWaitingMaterial(WaitingMaterialVo vo, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            vo.setMaterialNo(request.getParameter("materialNo"));
            vo.setNotifiNo(request.getParameter("notifiNo"));
            vo.setWaitingType(request.getParameter("waitingType"));
            vo.setWaitingStatus(request.getParameter("waitingStatus"));
            vo.setCraftNo(request.getParameter("craftNo"));
            vo.setXsCompanyId(request.getParameter("xsCompanyId"));
            vo.setXsContractNo(request.getParameter("xsContractNo"));
            vo.setXsCustomerNo(request.getParameter("xsCustomerNo"));
            vo.setStoreStatus(request.getParameter("storeStatus"));
            vo.setCompany(UserUtils.getUser().getCompany());
            String fileName = "待料数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            Page<WaitingMaterialVo> qPage = new Page<WaitingMaterialVo>(request, response, -1);
            // 设置排序
            Page<WaitingMaterialVo> page = materialService.findWaitingMaterialPage(qPage, vo);
            new ExportExcel("待料数据", WaitingMaterialVo.class).setDataList(page.getList())
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/modules/report/notification/waitingMaterial";
    }

    @RequiresPermissions("stock:rawMaterial:edit")
    @RequestMapping(value = "bindDeliveryMsg", method = RequestMethod.POST)
    @ResponseBody
    public String bindDeliveryMsg(@RequestBody RawmaterialStock stock)
    {
        return rawmaterialStockService.bindDeliveryMsg(stock);
    }

    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"loadTotalInfo"}, method = RequestMethod.POST)
    @ResponseBody
    public RawmaterialStock loadTotalInfo(@RequestBody RawmaterialStock rawMaterial)
    {
        User user = UserUtils.getUser();
        if (null != rawMaterial.getQueryAll() && !rawMaterial.getQueryAll())
        {
            rawMaterial.setCreatedBy(user);
        }

        // 设置查询企业编号
        if (rawMaterial != null)
        {
            rawMaterial.setCompany(user.getCompany());
        }
        if (rawMaterial.getInoutTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rawMaterial.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            rawMaterial.setInoutTimeEndQr(deliTime.getTime());
        }
        if (rawMaterial.getInoutTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rawMaterial.getInoutTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            rawMaterial.setInoutTimeStartQr(deliTime.getTime());
        }
        RawmaterialStock totalObj = rawmaterialStockService.getCountTotalInfo(rawMaterial);
        return totalObj;
    }

    // 提交审批
    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = "submitApproval", method = RequestMethod.POST)
    @ResponseBody
    public String submitApproval(@RequestBody Material material)
    {
        return rawmaterialStockService.submitApproval(material);
    }

    @RequestMapping(value = "upload")
    @ResponseBody
    public Map<String, Object> uploadFile(@RequestParam("file") MultipartFile file,
        @RequestParam("uploadType") String uploadType, @RequestParam("oper") String oper, HttpServletRequest req)
    {
        Company company = UserUtils.getUser().getCompany();

        // 若存在多个通知单的情况，将其替换为下划线
        String savePath = company.getRecordId() + "/material/" + uploadType + "/";

        // 若为修改，先删除旧的
        if ("mod".equals(oper))
        {
            FileManageUtils.delFiles(savePath);
        }

        Map<String, Object> result = Maps.newHashMap();

        // 上传文件
        String url = FileManageUtils.uploadFile(file, savePath, req);
        if (url == null)
        {
            result.put("result", false);
            return result;
        }

        // 基本路径
        String sourcePath = savePath + file.getOriginalFilename();

        // 若为附件时需要构造对象
        if ("erpRawmaterialPrice".equals(uploadType))
        {
            CardBAttach attach = new CardBAttach();
            attach.setOrgFileName(file.getOriginalFilename());
            attach.setFileUrl(sourcePath);
            attach.setRealFileName(file.getOriginalFilename());
            attach.setCompany(company);
            attach.setTempUrl(url);
            result.put(uploadType, attach);
        }
        else
        {
            result.put(uploadType, savePath + file.getOriginalFilename());
        }

        result.put("result", true);

        return result;
    }

    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"approvalPage"})
    @ResponseBody
    public Page<Approval> approvalPage(@RequestBody Approval approval, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        if (null != approval.getQueryAll() && !approval.getQueryAll())
        {
            approval.setCreatedBy(user);
        }

        // 设置查询企业编号
        if (approval != null)
        {
            approval.setCompany(user.getCompany());
        }
        if (approval.getSentTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(approval.getSentTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            approval.setSentTimeEndQr(deliTime.getTime());
        }
        if (approval.getSentTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(approval.getSentTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            approval.setSentTimeStartQr(deliTime.getTime());
        }
        // 分页查询数据
        Page<Approval> qpage = new Page<Approval>(request, response);
        if (StringUtils.isNotBlank(approval.getPageNo()) && StringUtils.isNotBlank(approval.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(approval.getPageNo()));
            qpage.setPageSize(Integer.parseInt(approval.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }

        // 设置排序
        if (StringUtils.isNotBlank(approval.getOrderBy()))
        {
            qpage.setOrderBy(approval.getOrderBy());
        }
        Page<Approval> page = rawmaterialStockService.approvalPage(qpage, approval);
        return page;
    }

    @RequestMapping(value = {"getShowApprovalData"})
    @ResponseBody
    public List<Material> getShowApprovalData(@RequestBody Approval approval)
    {
        return ModifyUtils.getShowApprovalData(approval.getDataDetailId(),approval.getCreatedDate());
    }

    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"getFirstFirstOutPage"})
    @ResponseBody
    public Page<RawmaterialStock> getFirstFirstOutPage(@RequestBody RawmaterialStock stock, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        if (null != stock.getQueryAll() && !stock.getQueryAll())
        {
            stock.setCreatedBy(user);
        }

        // 设置查询企业编号
        if (stock != null)
        {
            stock.setCompany(user.getCompany());
        }
        if (stock.getSentTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(stock.getSentTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            stock.setSentTimeEndQr(deliTime.getTime());
        }
        if (stock.getSentTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(stock.getSentTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            stock.setSentTimeStartQr(deliTime.getTime());
        }
        // 分页查询数据
        Page<RawmaterialStock> qpage = new Page<RawmaterialStock>(request, response);
        if (StringUtils.isNotBlank(stock.getPageNo()) && StringUtils.isNotBlank(stock.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(stock.getPageNo()));
            qpage.setPageSize(Integer.parseInt(stock.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }

        // 设置排序
        if (StringUtils.isNotBlank(stock.getOrderBy()))
        {
            qpage.setOrderBy(stock.getOrderBy());
        }
        Page<RawmaterialStock> page = rawmaterialStockService.getFirstFirstOutPage(qpage, stock);
        return page;
    }

    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "inout/exportFirstFirstOut")
    @ResponseBody
    public String exportFirstFirstOut(RawmaterialStock stock, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            if (null != stock.getQueryAll() && !stock.getQueryAll())
            {
                stock.setCreatedBy(user);
            }

            // 设置查询企业编号
            if (stock != null)
            {
                stock.setCompany(user.getCompany());
            }
            String sentTimeStartQr = request.getParameter("sentTimeStartQr");
            String sentTimeEndQr = request.getParameter("sentTimeEndQr");
            if (StringUtils.isNotBlank(sentTimeStartQr))
            {
                Date startdate = new Date(Long.parseLong(sentTimeStartQr));
                stock.setSentTimeStartQr(startdate);
            }
            if (StringUtils.isNotBlank(sentTimeEndQr))
            {
                Date enddate = new Date(Long.parseLong(sentTimeEndQr));
                stock.setSentTimeEndQr(enddate);
            }
            if (stock.getSentTimeEndQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(stock.getSentTimeEndQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                stock.setSentTimeEndQr(deliTime.getTime());
            }
            if (stock.getSentTimeStartQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(stock.getSentTimeStartQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                stock.setSentTimeStartQr(deliTime.getTime());
            }
            List<RawmaterialStock> firstFirstOutList = rawmaterialStockService.getFirstFirstOutList(stock);
            String fileName = "先进先出数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<String> headerList = new ArrayList<String>();
            headerList.add("出入库时间");
            headerList.add("物料编号");
            headerList.add("物料名称");
            headerList.add("物料类型");
            headerList.add("物料规格");
            headerList.add("出入库类型");
            headerList.add("出入库数量");
            headerList.add("出入库单价");
            headerList.add("出入库金额");
            headerList.add("结余数量");
            headerList.add("结余单价");
            headerList.add("结余金额");
            String[] headers = new String[headerList.size()];
            headerList.toArray(headers);
            ExportExcel excel = new ExportExcel("", headers);
            rawmaterialStockService.setExportFirstFirstOut(excel, firstFirstOutList, headers);
            excel.write(response, fileName).dispose();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }
}
