<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.hr.permission_center.dao.Hr_DictDao">
    <insert id="addDict">
        INSERT INTO md_dict_item (
            itemName,
            remark,
            activeflag,
            itemStatus,
            createdBy,
            createdDate
        )
        VALUES
        (
            #{itemName},
            #{remark},
            1,
            1,
            #{createdBy.recordId},
            NOW()
        )
    </insert>
    
    <select id="getSelectDictManage" resultType="Hr_DictItem">
        SELECT
        recordId,
        itemName,
        remark,
        activeflag,
        itemStatus
        FROM
        md_dict_item
        WHERE
        activeflag = 1
        <if test = "itemName != null and itemName != ''" >
            AND REPLACE (itemName, " ", "") LIKE CONCAT('%',REPLACE (#{itemName}," ",""), '%')
        </if >

    </select>

    <update id="updateDictStatus">
        UPDATE md_dict_item
        SET itemStatus = #{itemStatus} where recordId = #{recordId}
    </update>

    <update id="updateDict">
        UPDATE md_dict_item
        SET itemName = #{itemName},remark=#{remark} where recordId = #{recordId}
    </update>

    <select id="getDictValues" resultType="Hr_DictValue">
        SELECT
            mdv.recordId,
            mdv.
                VALUE
                ,
            mdv.remark,
            mdv.seqNum,
            mdv.itemId,
            mdv.useFlag,
            mc.`name` AS companyName
        FROM
            md_dict_value mdv
                LEFT JOIN md_dict_item mdi ON mdv.itemId = mdi.recordId
                LEFT JOIN md_company mc ON mc.recordId = mdv.companyId
        WHERE
            mdv.activeflag = 1
          AND mdv.itemId = #{itemId}
        ORDER BY
            mdv.seqNum
    </select>

    <insert id="addDictValue">
        INSERT INTO md_dict_value (

            value,
            remark,
            seqNum,
            itemId,
            useFlag,
            activeflag,
            createdBy,
            createdDate
        )
        VALUES
        (
            #{value},
            #{remark},
            #{seqNum},
            #{itemId},
            1,
            1,
            #{createdBy.recordId},
            NOW()
        )
    </insert>

    <update id="updateDictValue">
        UPDATE md_dict_value
        SET `value` = #{value}, remark = #{remark} ,seqNum = #{seqNum} where recordId = #{recordId}
    </update>

    <update id="updateValueStatus">
        UPDATE md_dict_value
        SET useFlag = #{useFlag} where recordId = #{recordId}
    </update>
</mapper>