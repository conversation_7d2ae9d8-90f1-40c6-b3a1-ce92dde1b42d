<%@ page contentType="text/html;charset=UTF-8" %>
<!-- BEGIN 物料规格管理 -->
<tab heading="物料规格管理" active="materialCtrl.tabs.viewSpecForm.active" ng-click="materialCtrl.doSpecQuery()">
    <div id="listSteps7" class="panel panel-default">
        <div class="panel-heading font-blue-hoki">查询</div>
        <div class="panel-body">
            <form class="form-horizontal">
                <div class="row">
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">物料类型：</label>
                            <div class="col-sm-7 col-md-8">
                                <select class="form-control" ng-model="materialCtrl.specQuery.materialType"
                                        ng-options="a.recordId as a.value for a in materialCtrl.materialTypes" disable-auto-validate="true">
                                    <option value="">所有</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">物料规格名称：</label>
                            <div class="col-sm-7 col-md-8">
                                <input type="text" class="form-control" ng-model="materialCtrl.specQuery.name" disable-auto-validate="true"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">覆铜板材：</label>
                            <div class="col-sm-7 col-md-8">
                                <ui-select ng-model="materialCtrl.specQuery.copperSheetId" theme="bootstrap">
                                    <ui-select-match>{{$select.selected.value}}</ui-select-match>
                                    <ui-select-choices repeat="item.recordId as item in materialCtrl.copperSheetList | filter: $select.search">
                                        <div ng-bind-html="item.value | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <button class="btn btn-default btn-default-width pull-right" ng-click="materialCtrl.doSpecQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="portlet light bordered">
        <div class="portlet-title ">
            <div class="caption font-blue-hoki">物料规格列表</div>

            <div id="step7" class="actions">
                <div class="portlet-input input-inline input-small" ng-if="materialCtrl.right.edit">
                    <button type="button" class="btn green btn-default-width" ng-click="materialCtrl.addMaterialSpec()"><i class="fa fa-plus"></i> 添加物料规格</button>
                </div>
            </div>
        </div>
        <div class="portlet-body">
            <div id="listSteps1" class="table-scrollable" style="margin-top:0px !important">
                <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                    <thead>
                    <tr class="heading">
                        <th>规格名称</th>
                        <th>物料类型</th>
                        <th>覆铜板材</th>
                        <th>必填</th>
                        <th>导出</th>
                        <th>隐藏</th>
                        <th>值定义</th>
                        <th>条件</th>
                        <th>展开规格</th>
                        <th>排序</th>
                        <th>备注</th>
                        <th id="listSteps3" ng-if="materialCtrl.right.edit">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="row in materialCtrl.materialSpecList">
                        <td ><a ng-click="materialCtrl.mod2MaterialSpec($index)">{{row.name}}</a></td>
                        <td ng-bind="row.materialTypes.value"></td>
                        <td ng-bind="row.copperSheetValue"></td>
                        <td ng-bind="row.visible"></td>
                        <td ng-bind="row.export"></td>
                        <td ng-bind="row.hideflag"></td>
                        <td>
                            <span ng-if="!row.valDefinition">正常</span>
                            <span ng-if="row.valDefinition == 1">范围</span>
                            <span ng-if="row.valDefinition == 2">多选</span>
                        </td>
                        <td ng-bind="row.openScope"></td>
                        <td ng-bind="row.specCraftName"></td>
                        <td ng-bind="row.sort"></td>
                        <td ng-bind="row.remark"></td>
                        <td ng-if="materialCtrl.right.edit" >
                            <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-click="materialCtrl.delMaterialSpec($index)"><i class="fa fa-times font-red"></i>删除</a>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</tab>
<!-- BEGIN 物料规格管理 -->