package com.kyb.pcberp.common.pdf.ext;

import java.io.IOException;
import java.util.Date;

import com.kyb.pcberp.common.utils.FmtUtils;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.BaseFont;

public class ParagraphCn14Bold extends Paragraph
{
    
    private static final long serialVersionUID = -4767036786714471719L;
    
    private static Font font_cn13;
    
    static
    {
        BaseFont bfChinese = null;
        try
        {
            String path = ParagraphCn14Bold.class.getResource("/font/simsun.ttc").getPath();
            bfChinese = BaseFont.createFont(path + ",1", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            
            // String path = ParagraphCn.class.getResource("/font/MSYHMONO.ttf").getPath();
            // bfChinese = BaseFont.createFont(path, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            
            // bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        }
        catch (DocumentException e)
        {
            e.printStackTrace();
            
        }
        catch (IOException e)
        {
            e.printStackTrace();
            
        }
        catch (Exception e)
        {
            
        }
        font_cn13 = new Font(bfChinese, 14, Font.BOLD);
    }
    
    public ParagraphCn14Bold(String value)
    {
        super(value, font_cn13);
    }
    
    public ParagraphCn14Bold(Long value)
    {
        this(String.valueOf(value));
    }
    
    public ParagraphCn14Bold(Integer value)
    {
        this(String.valueOf(value));
    }
    
    public ParagraphCn14Bold(Date value)
    {
        this(FmtUtils.formatDate(value));
    }
}
