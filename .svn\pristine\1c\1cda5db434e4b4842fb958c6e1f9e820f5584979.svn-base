<%@ page contentType="text/html;charset=UTF-8" %>
<tab heading="工作规范列表" active="ctrl.tabs.viewForm.active" ng-click="ctrl.loadData()">
    <div class="portlet light bordered">
        <div class="portlet-title">
            <div class="caption" ng-if="ctrl.versionList && ctrl.versionList.length > 0">
                <div class="row">
                    <div class="col-md-12">
                        <select class="form-control"
                                ng-model="ctrl.versionDate"
                                ng-change="ctrl.loadVersionCount()"
                                ng-options="gen.versionDate as gen.versionDateStr for gen in ctrl.versionList">
                        </select>
                    </div>
                </div>
            </div>
            <div class="actions">
                <div class="row">
                    <div class="col-md-12 text-right" ng-if="ctrl.versionCount > 0 && ctrl.newVersionDate == ctrl.versionDate">
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.createVersion()" ng-if="ctrl.right.edit">
                            <i class="fa fa-adjust font-blue"></i>创建新版本
                        </a>
                    </div>
                    <div class="col-md-12 text-right" ng-if="ctrl.versionCount == 0">
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.batchHandleOpen()" ng-if="ctrl.right.edit">
                            <i class="fa fa-adjust font-blue"></i>增加行
                        </a>
                        &nbsp;
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.editCycleSetUpModal()" ng-if="ctrl.right.edit">
                            <i class="fa fa-edit font-blue"></i>修改行
                        </a>
                        &nbsp;
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.updateStatusModal(2)" ng-if="ctrl.right.edit">
                            <i class="fa fa-sign-in font-green"></i>生效
                        </a>
                        &nbsp;
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.deleteCycleSetUpModal()" ng-if="ctrl.right.edit">
                            <i class="fa fa-times font-red"></i>删除行
                        </a>
                        &nbsp;
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.updateStatusModal(1)" ng-if="ctrl.right.edit">
                            <i class="fa fa-sign-out font-red"></i>失效
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="portlet-body">
            <span>最大生产周期{{ctrl.maxProduceDay ? ctrl.maxProduceDay:0}}天</span>
            <span ng-if="ctrl.versionCount > 0 && ctrl.newVersionDate != ctrl.versionDate" class="text-warning">如要修改请前往最新的版本</span>
            <span ng-if="ctrl.versionCount > 0 && ctrl.newVersionDate == ctrl.versionDate" class="text-warning">该版本已经被使用，如要修改请创建新的版本</span>
            <div class="table-scrollable" style="margin-top:0px !important">
                <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                    <thead>
                    <tr class="heading">
                        <th>序号</th>
                        <th>规则名称</th>
                        <th>周期类型</th>
                        <th>产品类型</th>
                        <th>资料类型</th>
                        <th>订单类型</th>
                        <th>规则说明</th>
                        <th>是否生效</th>
                        <th>约定交期</th>
                        <th>生产计划</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="row in ctrl.cycleList" ng-class="row.checked?'success':''" ng-click="ctrl.changeCheked(row,'')" ng-if="row.status != 3">
                        <td>{{$index + 1}}</td>
                        <td>
                            <a href="javascript:void(0)" ng-click="ctrl.showCycleSetup(row,2)" ng-bind="row.name"></a>
                        </td>
                        <td>
                            {{row.countMethodVal}}
                        </td>
                        <td>
                            <span ng-if="row.productType == '1'">曝光</span>
                            <span ng-if="row.productType == '2'">丝印</span>
                        </td>
                        <td>
                            <span ng-if="row.cardType == '1'">新单</span>
                            <span ng-if="row.cardType == '2'">返单</span>
                        </td>
                        <td>
                            <span ng-if="row.orderType == '1'">样品</span>
                            <span ng-if="row.orderType == '2'">量产</span>
                            <span ng-if="row.orderType == '3'">补料</span>
                        </td>
                        <td ng-bind="row.remark"></td>
                        <td>
                            <span ng-if="row.status == 2" class="text-success">有效</span>
                            <span ng-if="row.status == 1" class="text-danger">无效</span>
                        </td>
                        <td ng-bind="row.nomalDays"></td>
                        <td ng-bind="row.setDays"></td>
                        <td>
                            <a href="javascript:void(0)" ng-if="ctrl.right.edit" ng-click="ctrl.upSort(row)">
                                <i class="fa fa-level-up font-green"></i>
                            </a>&emsp;
                            <a href="javascript:void(0)" ng-if="ctrl.right.edit" ng-click="ctrl.downSort(row)">
                                <i class="fa fa-level-down font-red"></i>
                            </a>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</tab>