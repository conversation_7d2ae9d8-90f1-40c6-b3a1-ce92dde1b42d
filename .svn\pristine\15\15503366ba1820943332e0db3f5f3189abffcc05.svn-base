package com.kyb.pcberp.modules.finance.web;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.kyb.pcberp.modules.contract.entity.GroupCenterBill;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.CompanyUtil;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.contract.entity.GroupCenterTwoData;
import com.kyb.pcberp.modules.contract.vo.GroupCenterCustomerExpTwoVo;
import com.kyb.pcberp.modules.contract.vo.GroupCenterCustomerExpVo;
import com.kyb.pcberp.modules.contract.vo.GroupCenterDataVo;
import com.kyb.pcberp.modules.contract.vo.GroupCenterDateExpVo;
import com.kyb.pcberp.modules.contract.vo.GroupCenterOrderExpTwoVo;
import com.kyb.pcberp.modules.contract.vo.GroupCenterOrderExpVo;
import com.kyb.pcberp.modules.contract.vo.GroupCenterSupplierExpTwoVo;
import com.kyb.pcberp.modules.contract.vo.GroupCenterSupplierExpVo;
import com.kyb.pcberp.modules.contract.vo.SynthesisExport;
import com.kyb.pcberp.modules.crm.entity.AccountsReceivable;
import com.kyb.pcberp.modules.finance.entity.Reconciliation;
import com.kyb.pcberp.modules.finance.service.ReconciliationService;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

@Controller
@RequestMapping(value = "${adminPath}/finance/reconciliation")
public class ReconciliationController extends BaseController
{
    @Autowired
    private ReconciliationService reconciliationService;
    
    @RequestMapping(value = "list")
    public String list()
    {
        return "modules/finance/reconciliation";
    }
    
    @RequestMapping(value = {"page"})
    @ResponseBody
    public Page<Reconciliation> getlist(@RequestBody Reconciliation reconciliation, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        
        // 设置查询企业编号
        reconciliation.setCompanyId(user.getCompany().getRecordId());
        
        // 设置查询范围
        if (reconciliation.getQueryAll() != null && !reconciliation.getQueryAll())
        {
            reconciliation.setCreatedBy(user);
        }
        // 分页查询数据
        Page<Reconciliation> qpage = new Page<Reconciliation>(request, response);
        if (StringUtils.isNotBlank(reconciliation.getPageNo()) && StringUtils.isNotBlank(reconciliation.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(reconciliation.getPageNo()));
            qpage.setPageSize(Integer.parseInt(reconciliation.getPageSize()));
            qpage.setOrderBy("a.lastUpdDate desc");
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        // 设置排序
        if (StringUtils.isNotBlank(reconciliation.getOrderBy()))
        {
            qpage.setOrderBy(reconciliation.getOrderBy());
        }
        return reconciliationService.findPage(qpage, reconciliation);
    }
    
    @RequestMapping(value = "load/data", method = {RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> loadData()
    {
        Map<String, Object> data = new HashMap<>();
        Calendar cal = Calendar.getInstance();
        List<Integer> periodList = Lists.newArrayList();
        try
        {
            periodList = DateUtils.getPeriodList(null, null);
        }
        catch (ParseException e)
        {
            e.printStackTrace();
        }
        Integer period1 = null;
        Integer month = cal.get(Calendar.MONTH) + 2;
        if (month > 12)
        {
            period1 = (cal.get(Calendar.YEAR) + 1) * 100 + (month - 12);
        }
        else
        {
            period1 = cal.get(Calendar.YEAR) * 100 + (month);
        }
        periodList.add(period1);
        data.put("periodList", periodList);
        
        cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.MONTH, -1);
        String period = DateUtils.formatDate(cal.getTime(), "yyyyMM");
        data.put("period", period);
        return data;
    }
    
    @RequestMapping(value = "save")
    @ResponseBody
    public String save(@RequestBody Reconciliation reconciliation)
    {
        return reconciliationService.save(reconciliation);
    }
    
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(@RequestBody Reconciliation reconciliation)
    {
        return reconciliationService.delete(reconciliation);
    }
    
    @RequestMapping(value = "getScheduleModuleData")
    @ResponseBody
    public Map<String, Object> getScheduleModuleData(@RequestBody Reconciliation reconciliation)
    {
        Map<String, Object> map = reconciliationService.getScheduleModuleData(reconciliation);
        return map;
    }
    
    @RequestMapping(value = "repairData")
    @ResponseBody
    public Map<String, Object> repairData(@RequestBody Reconciliation reconciliation)
    {
        return reconciliationService.repairData(reconciliation);
    }
    
    @RequestMapping(value = "nextHandle")
    @ResponseBody
    public Map<String, Object> nextHandle(@RequestBody Reconciliation reconciliation)
    {
        return reconciliationService.nextHandle(reconciliation);
    }
    
    @RequestMapping(value = "getReconciliationData")
    @ResponseBody
    public Map<String, Object> getReconciliationData(@RequestBody GroupCenterTwoData groupCenterTwoData)
    {
        return reconciliationService.getReconciliationData(groupCenterTwoData);
    }
    
    @RequestMapping(value = "getSYReconciliationData")
    @ResponseBody
    public Map<String, Object> getSYReconciliationData(@RequestBody GroupCenterTwoData groupCenterTwoData)
    {
        return reconciliationService.getSYReconciliationData(groupCenterTwoData);
    }
    
    @RequestMapping(value = "reSetSYHistoryData")
    @ResponseBody
    public Map<String, Object> reSetSYHistoryData(@RequestBody GroupCenterTwoData groupCenterTwoData)
    {
        return reconciliationService.reSetSYHistoryData(groupCenterTwoData);
    }
    
    @RequestMapping(value = "screenReconciliationData")
    @ResponseBody
    public List<GroupCenterTwoData> screenReconciliationData(@RequestBody GroupCenterTwoData groupCenterTwoData)
    {
        groupCenterTwoData.setFactId(CompanyUtil.getInstance().getFactId());
        groupCenterTwoData.setEcoemyId(CompanyUtil.getInstance().getEcoemyId());
        return reconciliationService.getGroupReceivableData(groupCenterTwoData);
    }
    
    @RequestMapping(value = "screenReconciliationTimeData")
    @ResponseBody
    public List<GroupCenterTwoData> screenReconciliationTimeData(@RequestBody GroupCenterTwoData groupCenterTwoData)
    {
        groupCenterTwoData.setFactId(CompanyUtil.getInstance().getFactId());
        groupCenterTwoData.setEcoemyId(CompanyUtil.getInstance().getEcoemyId());
        return reconciliationService.screenReconciliationTimeData(groupCenterTwoData);
    }
    
    @RequestMapping(value = "getReceivableByPeriod")
    @ResponseBody
    public List<AccountsReceivable> getReceivableByPeriod(@RequestBody GroupCenterTwoData groupCenterTwoData)
    {
        return reconciliationService.getReceivableByPeriod(groupCenterTwoData);
    }
    
    /**
     * 导出
     * 
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "exportInfo")
    public String exportInfo(HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            GroupCenterTwoData groupCenterTwoData = new GroupCenterTwoData();
            String period = request.getParameter("period");
            groupCenterTwoData.setPeriod(period);
            groupCenterTwoData.setCompanyId(request.getParameter("companyId"));
            groupCenterTwoData.setDefaultWay(request.getParameter("defaultWay"));
            String issue = request.getParameter("issue");
            String veidoo = request.getParameter("veidoo");
            String reconPro = request.getParameter("reconPro");
            groupCenterTwoData.setQueryType(request.getParameter("queryType"));
            groupCenterTwoData.setFactId(CompanyUtil.getInstance().getFactId());
            groupCenterTwoData.setEcoemyId(CompanyUtil.getInstance().getEcoemyId());
            List<GroupCenterTwoData> list = reconciliationService.getGroupReceivableData(groupCenterTwoData);
            List<GroupCenterTwoData> exportlist = new ArrayList<>();
            if (!CollectionUtils.isEmpty(list))
            {
                for (GroupCenterTwoData d : list)
                {
                    if ("1".equals(issue))
                    {
                        if ("金额差异".equals(d.getDifferenceFalg()))
                        {
                            exportlist.add(d);
                        }
                        // 金额差异
                        
                    }
                    else if ("2".equals(issue))
                    {
                        if ("无差异".equals(d.getDifferenceFalg()))
                        {
                            exportlist.add(d);
                        }
                    }
                    else
                    {
                        exportlist.add(d);
                    }
                }
                
            }
            
            String fileName = "对账明细" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            BigDecimal xsReceivableAmount = null;
            BigDecimal xsPayableAmount = null;
            BigDecimal lnReceivableAmount = null;
            BigDecimal lnPayableAmount = null;
            BigDecimal gcReceivableAmount = null;
            BigDecimal gcPayableAmount = null;
            
            if ("2".equals(veidoo))
            {
                // 客户
                List<GroupCenterCustomerExpVo> veidooList = new ArrayList<>();
                Map<String, GroupCenterCustomerExpVo> expMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(exportlist))
                {
                    GroupCenterCustomerExpVo temp = null;
                    for (GroupCenterTwoData data : exportlist)
                    {
                        String custKey = data.getFinalCustId();
                        if ("2".equals(reconPro))
                        {
                            // PCS数
                            xsReceivableAmount = data.getXsReceivableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getXsReceivableQuality());
                            xsPayableAmount = data.getXsPayableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getXsPayableQuality());
                            lnReceivableAmount = data.getLnReceivableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getLnReceivableQuality());
                            lnPayableAmount = data.getLnPayableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getLnPayableQuality());
                            gcReceivableAmount = data.getGcReceivableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getGcReceivableQuality());
                            gcPayableAmount = data.getGcPayableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getGcPayableQuality());
                                
                        }
                        else if ("3".equals(reconPro))
                        {
                            // 平米数
                            xsReceivableAmount =
                                data.getXsReceivableArea() == null ? BigDecimal.ZERO : data.getXsReceivableArea();
                            xsPayableAmount =
                                data.getXsPayableArea() == null ? BigDecimal.ZERO : data.getXsPayableArea();
                            lnReceivableAmount =
                                data.getLnReceivableArea() == null ? BigDecimal.ZERO : data.getLnReceivableArea();
                            lnPayableAmount =
                                data.getLnPayableArea() == null ? BigDecimal.ZERO : data.getLnPayableArea();
                            gcReceivableAmount =
                                data.getGcReceivableArea() == null ? BigDecimal.ZERO : data.getGcReceivableArea();
                            gcPayableAmount =
                                data.getGcPayableArea() == null ? BigDecimal.ZERO : data.getGcPayableArea();
                                
                        }
                        else
                        {
                            // 金额
                            xsReceivableAmount =
                                data.getXsReceivableAmount() == null ? BigDecimal.ZERO : data.getXsReceivableAmount();
                            xsPayableAmount =
                                data.getXsPayableAmount() == null ? BigDecimal.ZERO : data.getXsPayableAmount();
                            lnReceivableAmount =
                                data.getLnReceivableAmount() == null ? BigDecimal.ZERO : data.getLnReceivableAmount();
                            lnPayableAmount =
                                data.getLnPayableAmount() == null ? BigDecimal.ZERO : data.getLnPayableAmount();
                            gcReceivableAmount =
                                data.getGcReceivableAmount() == null ? BigDecimal.ZERO : data.getGcReceivableAmount();
                            gcPayableAmount =
                                data.getGcPayableAmount() == null ? BigDecimal.ZERO : data.getGcPayableAmount();
                        }
                        
                        if (expMap.containsKey(custKey))
                        {
                            temp = expMap.get(custKey);
                            
                            if ("1".equals(data.getStatus()))
                            {
                                // 内部
                                temp.setXsInnerReceivableAmount(
                                    temp.getXsInnerReceivableAmount().add(xsReceivableAmount));
                                temp.setXsInnerPayableAmount(temp.getXsInnerPayableAmount().add(xsPayableAmount));
                                temp.setLnInnerReceivableAmount(
                                    temp.getLnInnerReceivableAmount().add(lnReceivableAmount));
                                temp.setLnInnerPayableAmount(temp.getLnInnerPayableAmount().add(lnPayableAmount));
                            }
                            else if ("2".equals(data.getStatus()))
                            {
                                // 外部
                                temp.setLnExterPayableAmount(temp.getLnExterPayableAmount().add(lnPayableAmount));
                                temp.setLnExterReceivableAmount(
                                    temp.getLnExterReceivableAmount().add(lnReceivableAmount));
                                temp.setXsExterPayableAmount(temp.getXsExterPayableAmount().add(xsPayableAmount));
                                temp.setXsExterReceivableAmount(
                                    temp.getXsExterReceivableAmount().add(xsReceivableAmount));
                            }
                            // 销售
                            temp.setXsReceivableAmount(temp.getXsReceivableAmount().add(xsReceivableAmount));
                            temp.setXsPayableAmount(temp.getXsPayableAmount().add(xsPayableAmount));
                            temp.setLnReceivableAmount(temp.getLnReceivableAmount().add(lnReceivableAmount));
                            temp.setLnPayableAmount(temp.getLnPayableAmount().add(lnPayableAmount));
                            temp.setGcPayableAmount(temp.getGcPayableAmount().add(gcPayableAmount));
                            temp.setGcReceivableAmount(temp.getGcReceivableAmount().add(gcReceivableAmount));
                        }
                        else
                        {
                            temp = new GroupCenterCustomerExpVo();
                            temp.setFinalCustId(data.getFinalCustId());
                            temp.setFinalCustNo(data.getFinalCustNo());
                            temp.setCusName(data.getCusName());
                            temp.setPeriod(period);
                            if ("1".equals(data.getStatus()))
                            {
                                // 内部
                                temp.setXsInnerReceivableAmount(xsReceivableAmount);
                                temp.setXsInnerPayableAmount(xsPayableAmount);
                                temp.setLnInnerReceivableAmount(lnReceivableAmount);
                                temp.setLnInnerPayableAmount(lnPayableAmount);
                                
                                temp.setLnExterPayableAmount(BigDecimal.ZERO);
                                temp.setLnExterReceivableAmount(BigDecimal.ZERO);
                                temp.setXsExterPayableAmount(BigDecimal.ZERO);
                                temp.setXsExterReceivableAmount(BigDecimal.ZERO);
                            }
                            else if ("2".equals(data.getStatus()))
                            {
                                // 外部
                                temp.setLnExterPayableAmount(lnPayableAmount);
                                temp.setLnExterReceivableAmount(lnReceivableAmount);
                                temp.setXsExterPayableAmount(xsPayableAmount);
                                temp.setXsExterReceivableAmount(xsReceivableAmount);
                                
                                temp.setXsInnerReceivableAmount(BigDecimal.ZERO);
                                temp.setXsInnerPayableAmount(BigDecimal.ZERO);
                                temp.setLnInnerReceivableAmount(BigDecimal.ZERO);
                                temp.setLnInnerPayableAmount(BigDecimal.ZERO);
                            }
                            else
                            {
                                
                                temp.setLnExterPayableAmount(BigDecimal.ZERO);
                                temp.setLnExterReceivableAmount(BigDecimal.ZERO);
                                temp.setXsExterPayableAmount(BigDecimal.ZERO);
                                temp.setXsExterReceivableAmount(BigDecimal.ZERO);
                                
                                temp.setXsInnerReceivableAmount(BigDecimal.ZERO);
                                temp.setXsInnerPayableAmount(BigDecimal.ZERO);
                                temp.setLnInnerReceivableAmount(BigDecimal.ZERO);
                                temp.setLnInnerPayableAmount(BigDecimal.ZERO);
                                
                            }
                            // 销售
                            // 销售
                            temp.setXsReceivableAmount(xsReceivableAmount);
                            temp.setXsPayableAmount(xsPayableAmount);
                            temp.setLnReceivableAmount(lnReceivableAmount);
                            temp.setLnPayableAmount(lnPayableAmount);
                            temp.setGcPayableAmount(gcPayableAmount);
                            temp.setGcReceivableAmount(gcReceivableAmount);
                            veidooList.add(temp);
                            expMap.put(data.getFinalCustId(), temp);
                        }
                        
                    }
                }
                Class<?> classObj =
                    ("1".equals(reconPro) ? GroupCenterCustomerExpVo.class : GroupCenterCustomerExpTwoVo.class);
                if ("3".equals(veidoo))
                {
                    // 供应商
                    classObj =
                        ("1".equals(reconPro) ? GroupCenterSupplierExpVo.class : GroupCenterSupplierExpTwoVo.class);
                }
                new ExportExcel("", classObj, new Integer(1)).setDataListForNumber(veidooList)
                    .write(response, fileName)
                    .dispose();
            }
            else if ("3".equals(veidoo))
            {
                // 供应商
                List<GroupCenterCustomerExpVo> veidooList = new ArrayList<>();
                Map<String, GroupCenterCustomerExpVo> expMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(exportlist))
                {
                    GroupCenterCustomerExpVo temp = null;
                    for (GroupCenterTwoData data : exportlist)
                    {
                        if ("2".equals(reconPro))
                        {
                            // PCS数
                            xsReceivableAmount = data.getXsReceivableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getXsReceivableQuality());
                            xsPayableAmount = data.getXsPayableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getXsPayableQuality());
                            lnReceivableAmount = data.getLnReceivableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getLnReceivableQuality());
                            lnPayableAmount = data.getLnPayableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getLnPayableQuality());
                            gcReceivableAmount = data.getGcReceivableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getGcReceivableQuality());
                            gcPayableAmount = data.getGcPayableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getGcPayableQuality());
                                
                        }
                        else if ("3".equals(reconPro))
                        {
                            // 平米数
                            xsReceivableAmount =
                                data.getXsReceivableArea() == null ? BigDecimal.ZERO : data.getXsReceivableArea();
                            xsPayableAmount =
                                data.getXsPayableArea() == null ? BigDecimal.ZERO : data.getXsPayableArea();
                            lnReceivableAmount =
                                data.getLnReceivableArea() == null ? BigDecimal.ZERO : data.getLnReceivableArea();
                            lnPayableAmount =
                                data.getLnPayableArea() == null ? BigDecimal.ZERO : data.getLnPayableArea();
                            gcReceivableAmount =
                                data.getGcReceivableArea() == null ? BigDecimal.ZERO : data.getGcReceivableArea();
                            gcPayableAmount =
                                data.getGcPayableArea() == null ? BigDecimal.ZERO : data.getGcPayableArea();
                                
                        }
                        else
                        {
                            // 金额
                            xsReceivableAmount =
                                data.getXsReceivableAmount() == null ? BigDecimal.ZERO : data.getXsReceivableAmount();
                            xsPayableAmount =
                                data.getXsPayableAmount() == null ? BigDecimal.ZERO : data.getXsPayableAmount();
                            lnReceivableAmount =
                                data.getLnReceivableAmount() == null ? BigDecimal.ZERO : data.getLnReceivableAmount();
                            lnPayableAmount =
                                data.getLnPayableAmount() == null ? BigDecimal.ZERO : data.getLnPayableAmount();
                            gcReceivableAmount =
                                data.getGcReceivableAmount() == null ? BigDecimal.ZERO : data.getGcReceivableAmount();
                            gcPayableAmount =
                                data.getGcPayableAmount() == null ? BigDecimal.ZERO : data.getGcPayableAmount();
                        }
                        
                        if (expMap.containsKey(data.getFinalSupId()))
                        {
                            temp = expMap.get(data.getFinalSupId());
                            if ("1".equals(data.getStatus()))
                            {
                                // 内部
                                temp.setXsInnerReceivableAmount(
                                    temp.getXsInnerReceivableAmount().add(xsReceivableAmount));
                                temp.setXsInnerPayableAmount(temp.getXsInnerPayableAmount().add(xsPayableAmount));
                                temp.setLnInnerReceivableAmount(
                                    temp.getLnInnerReceivableAmount().add(lnReceivableAmount));
                                temp.setLnInnerPayableAmount(temp.getLnInnerPayableAmount().add(lnPayableAmount));
                            }
                            else if ("2".equals(data.getStatus()))
                            {
                                // 外部
                                temp.setLnExterPayableAmount(temp.getLnExterPayableAmount().add(lnPayableAmount));
                                temp.setLnExterReceivableAmount(
                                    temp.getLnExterReceivableAmount().add(lnReceivableAmount));
                                temp.setXsExterPayableAmount(temp.getXsExterPayableAmount().add(xsPayableAmount));
                                temp.setXsExterReceivableAmount(
                                    temp.getXsExterReceivableAmount().add(xsReceivableAmount));
                            }
                            // 销售
                            temp.setXsReceivableAmount(temp.getXsReceivableAmount().add(xsReceivableAmount));
                            temp.setXsPayableAmount(temp.getXsPayableAmount().add(xsPayableAmount));
                            temp.setLnReceivableAmount(temp.getLnReceivableAmount().add(lnReceivableAmount));
                            temp.setLnPayableAmount(temp.getLnPayableAmount().add(lnPayableAmount));
                            temp.setGcPayableAmount(temp.getGcPayableAmount().add(gcPayableAmount));
                            temp.setGcReceivableAmount(temp.getGcReceivableAmount().add(gcReceivableAmount));
                        }
                        else
                        {
                            temp = new GroupCenterCustomerExpVo();
                            temp.setFinalCustId(data.getFinalCustId());
                            temp.setFinalCustNo(data.getFinalCustNo());
                            temp.setCusName(data.getCusName());
                            temp.setFinalSupNo(data.getFinalSupNo());
                            temp.setSupName(data.getSupName());
                            temp.setPeriod(period);
                            if ("1".equals(data.getStatus()))
                            {
                                // 内部
                                temp.setXsInnerReceivableAmount(xsReceivableAmount);
                                temp.setXsInnerPayableAmount(xsPayableAmount);
                                temp.setLnInnerReceivableAmount(lnReceivableAmount);
                                temp.setLnInnerPayableAmount(lnPayableAmount);
                                
                                temp.setLnExterPayableAmount(BigDecimal.ZERO);
                                temp.setLnExterReceivableAmount(BigDecimal.ZERO);
                                temp.setXsExterPayableAmount(BigDecimal.ZERO);
                                temp.setXsExterReceivableAmount(BigDecimal.ZERO);
                            }
                            else if ("2".equals(data.getStatus()))
                            {
                                // 外部
                                temp.setLnExterPayableAmount(lnPayableAmount);
                                temp.setLnExterReceivableAmount(lnReceivableAmount);
                                temp.setXsExterPayableAmount(xsPayableAmount);
                                temp.setXsExterReceivableAmount(xsReceivableAmount);
                                
                                temp.setXsInnerReceivableAmount(BigDecimal.ZERO);
                                temp.setXsInnerPayableAmount(BigDecimal.ZERO);
                                temp.setLnInnerReceivableAmount(BigDecimal.ZERO);
                                temp.setLnInnerPayableAmount(BigDecimal.ZERO);
                            }
                            else
                            {
                                
                                temp.setLnExterPayableAmount(BigDecimal.ZERO);
                                temp.setLnExterReceivableAmount(BigDecimal.ZERO);
                                temp.setXsExterPayableAmount(BigDecimal.ZERO);
                                temp.setXsExterReceivableAmount(BigDecimal.ZERO);
                                
                                temp.setXsInnerReceivableAmount(BigDecimal.ZERO);
                                temp.setXsInnerPayableAmount(BigDecimal.ZERO);
                                temp.setLnInnerReceivableAmount(BigDecimal.ZERO);
                                temp.setLnInnerPayableAmount(BigDecimal.ZERO);
                                
                            }
                            // 销售
                            // 销售
                            temp.setXsReceivableAmount(xsReceivableAmount);
                            temp.setXsPayableAmount(xsPayableAmount);
                            temp.setLnReceivableAmount(lnReceivableAmount);
                            temp.setLnPayableAmount(lnPayableAmount);
                            temp.setGcPayableAmount(gcPayableAmount);
                            temp.setGcReceivableAmount(gcReceivableAmount);
                            veidooList.add(temp);
                            expMap.put(data.getFinalSupId(), temp);
                        }
                        
                    }
                }
                Class<?> classObj =
                    ("1".equals(reconPro) ? GroupCenterCustomerExpVo.class : GroupCenterCustomerExpTwoVo.class);
                if ("3".equals(veidoo))
                {
                    // 供应商
                    classObj =
                        ("1".equals(reconPro) ? GroupCenterSupplierExpVo.class : GroupCenterSupplierExpTwoVo.class);
                }
                new ExportExcel("", classObj, new Integer(1)).setDataListForNumber(veidooList)
                    .write(response, fileName)
                    .dispose();
            }
            else
            {
                BigDecimal warehousingQty = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(exportlist))
                {
                    for (GroupCenterTwoData data : exportlist)
                    {
                        if ("2".equals(reconPro))
                        {
                            // PCS数
                            xsReceivableAmount = data.getXsReceivableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getXsReceivableQuality());
                            xsPayableAmount = data.getXsPayableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getXsPayableQuality());
                            lnReceivableAmount = data.getLnReceivableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getLnReceivableQuality());
                            lnPayableAmount = data.getLnPayableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getLnPayableQuality());
                            gcReceivableAmount = data.getGcReceivableQuality() == null ? BigDecimal.ZERO
                                : new BigDecimal(data.getGcReceivableQuality());
                            gcPayableAmount = data.getJxSentQty() == null ? BigDecimal.ZERO : data.getJxSentQty();
                            warehousingQty =
                                data.getWarehousingQty() == null ? BigDecimal.ZERO : data.getWarehousingQty();
                            data.setOrderMoney(data.getOrderNum());
                        }
                        else if ("3".equals(reconPro))
                        {
                            // 平米数
                            xsReceivableAmount =
                                data.getXsReceivableArea() == null ? BigDecimal.ZERO : data.getXsReceivableArea();
                            xsPayableAmount =
                                data.getXsPayableArea() == null ? BigDecimal.ZERO : data.getXsPayableArea();
                            lnReceivableAmount =
                                data.getLnReceivableArea() == null ? BigDecimal.ZERO : data.getLnReceivableArea();
                            lnPayableAmount =
                                data.getLnPayableArea() == null ? BigDecimal.ZERO : data.getLnPayableArea();
                            gcReceivableAmount = data.getJxSentArea() == null ? BigDecimal.ZERO : data.getJxSentArea();
                            warehousingQty =
                                data.getWarehousingArea() == null ? BigDecimal.ZERO : data.getWarehousingArea();
                            gcPayableAmount =
                                data.getGcPayableArea() == null ? BigDecimal.ZERO : data.getGcPayableArea();
                            data.setOrderMoney(data.getOrderArea());
                            
                        }
                        else
                        {
                            // 金额
                            xsReceivableAmount =
                                data.getXsReceivableAmount() == null ? BigDecimal.ZERO : data.getXsReceivableAmount();
                            xsPayableAmount =
                                data.getXsPayableAmount() == null ? BigDecimal.ZERO : data.getXsPayableAmount();
                            lnReceivableAmount =
                                data.getLnReceivableAmount() == null ? BigDecimal.ZERO : data.getLnReceivableAmount();
                            lnPayableAmount =
                                data.getLnPayableAmount() == null ? BigDecimal.ZERO : data.getLnPayableAmount();
                            gcReceivableAmount =
                                data.getGcReceivableAmount() == null ? BigDecimal.ZERO : data.getGcReceivableAmount();
                            gcPayableAmount =
                                data.getGcPayableAmount() == null ? BigDecimal.ZERO : data.getGcPayableAmount();
                        }
                        
                        data.setXsReceivableAmount(xsReceivableAmount);
                        data.setXsPayableAmount(xsPayableAmount);
                        data.setLnReceivableAmount(lnReceivableAmount);
                        data.setLnPayableAmount(lnPayableAmount);
                        data.setGcPayableAmount(gcPayableAmount);
                        data.setGcReceivableAmount(gcReceivableAmount);
                        data.setWarehousingQty(warehousingQty);
                    }
                }

                new ExportExcel("",
                    ("1".equals(reconPro) ? GroupCenterOrderExpVo.class : GroupCenterOrderExpTwoVo.class),
                    new Integer(1)).setDataListForNumber(exportlist).write(response, fileName).dispose();
            }
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
        
    }
    
    /**
     * 导出
     * 
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "exportDateInfo")
    public String exportDateInfo(HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            GroupCenterTwoData groupCenterTwoData = new GroupCenterTwoData();
            String period = request.getParameter("period");
            groupCenterTwoData.setPeriod(period);
            groupCenterTwoData.setCompanyId(request.getParameter("companyId"));
            groupCenterTwoData.setDefaultWay(request.getParameter("defaultWay"));
            String issue = request.getParameter("issue");
            groupCenterTwoData.setFactId(CompanyUtil.getInstance().getFactId());
            groupCenterTwoData.setEcoemyId(CompanyUtil.getInstance().getEcoemyId());
            List<GroupCenterTwoData> list = reconciliationService.screenReconciliationTimeData(groupCenterTwoData);
            List<GroupCenterTwoData> exportlist = new ArrayList<>();
            if (!CollectionUtils.isEmpty(list))
            {
                for (GroupCenterTwoData d : list)
                {
                    if ("1".equals(issue))
                    {
                        if ("有差异".equals(d.getDifferenceFalg()))
                        {
                            exportlist.add(d);
                        }
                        // 金额差异
                        
                    }
                    else if ("2".equals(issue))
                    {
                        if ("无差异".equals(d.getDifferenceFalg()))
                        {
                            exportlist.add(d);
                        }
                    }
                    else
                    {
                        exportlist.add(d);
                    }
                }
                String fileName = "账单时间报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
                new ExportExcel("", GroupCenterDateExpVo.class, new Integer(1)).setDataList(exportlist)
                    .write(response, fileName)
                    .dispose();
            }
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
        
    }
    
    @RequestMapping(value = "getSaleFactoryData")
    @ResponseBody
    public List<GroupCenterTwoData> getSaleFactoryData()
    {
        return reconciliationService.getSaleFactoryData();
    }
    
    @RequestMapping(value = "exportSaleFactory")
    public String exportSaleFactory(HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            List<GroupCenterTwoData> list = reconciliationService.getSaleFactoryData();
            List<String> headerList = new ArrayList<String>();
            String[] headersPre =
                {"数量异常", "单价异常", "客户订单号", "客户型号", "生产编号", "销售金额", "销售数量", "工厂数量", "工厂金额", "工厂成本价", "工厂订单价"};
            for (String s : headersPre)
            {
                headerList.add(s);
            }
            
            String[] headers = new String[headerList.size()];
            headerList.toArray(headers);
            ExportExcel excel = new ExportExcel("销售工厂账单报表", headers);
            setDataList(excel, list, headers);
            excel.write(response, "销售工厂账单报表.xlsx").dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
    }
    
    public void setDataList(ExportExcel excel, List<GroupCenterTwoData> list, String[] hearList)
    {
        for (GroupCenterTwoData one : list)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList)
            {
                String val = "";
                switch (name)
                {
                    case "数量异常":
                        val = one.getNumRes();
                        break;
                    case "单价异常":
                        val = one.getMoneyRes();
                        break;
                    case "客户订单号":
                        val = one.getCustomerPo();
                        break;
                    case "客户型号":
                        val = one.getCustomerModel();
                        break;
                    case "生产编号":
                        val = one.getNo();
                        break;
                    case "销售金额":
                        val = one.getAmount() == null ? "" : one.getAmount().toString();
                        break;
                    case "销售数量":
                        val = one.getQuantity() == null ? "" : one.getQuantity().toString();
                        break;
                    case "工厂数量":
                        val = one.getFactQuantity() == null ? "" : one.getFactQuantity().toString();
                        break;
                    case "工厂金额":
                        val = one.getFactAmount() == null ? "" : one.getFactAmount().toString();
                        break;
                    case "工厂成本价":
                        val = one.getPrice() == null ? "" : one.getPrice().toString();
                        break;
                    case "工厂订单价":
                        val = one.getJxPrice() == null ? "" : one.getJxPrice().toString();
                        break;
                    default:
                        break;
                }
                
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }
    
    /**
     * 导出
     * 
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "exportHistoryData")
    public String exportHistoryData(HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            List<GroupCenterTwoData> list = null;
            GroupCenterTwoData groupCenterTwoData = new GroupCenterTwoData();
            if ("1".equals(request.getParameter("defaultWay")))
            {
                list = reconciliationService.getSYReconciliationDataList(groupCenterTwoData);
            }
            else
            {
                list = reconciliationService.getSYCheckGoodData(groupCenterTwoData);
            }
            
            if (!CollectionUtils.isEmpty(list))
            {
                String fileName = "对账报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
                new ExportExcel("", GroupCenterDataVo.class, new Integer(1)).setDataList(list)
                    .write(response, fileName)
                    .dispose();
            }
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
        
    }
    
    @RequestMapping(value = "checkStatusData")
    @ResponseBody
    public String checkStatusData(@RequestBody GroupCenterTwoData groupCenterTwoData)
    {
        return reconciliationService.checkStatusData(groupCenterTwoData);
    }
    
    /**
     * 导出
     * 
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "synthesisExportHistoryData")
    public String synthesisExportHistoryData(HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            List<SynthesisExport> list =  reconciliationService.getSynthesisExportData();
            if (!CollectionUtils.isEmpty(list))
            {
                String fileName = "综合统计报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
                new ExportExcel("", SynthesisExport.class, new Integer(1)).setDataList(list)
                    .write(response, fileName)
                    .dispose();
            }
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
    }

    @RequestMapping(value = "initMaterialCheckData")
    @ResponseBody
    public String initMaterialCheckData(@RequestBody GroupCenterBill groupCenterBill)
    {
        return reconciliationService.updateCheckData(groupCenterBill);
    }
}
