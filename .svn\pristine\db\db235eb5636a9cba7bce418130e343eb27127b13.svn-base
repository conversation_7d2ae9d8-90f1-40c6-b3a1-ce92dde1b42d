/**
 * 
 */
package com.kyb.pcberp.modules.quality.web;

import java.net.URL;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.aliyun.oss.OSSClient;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.oss.util.OssUtils;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;
import com.kyb.pcberp.modules.purch.service.PurchasingDetailService;
import com.kyb.pcberp.modules.quality.entity.AdviseDelivery;
import com.kyb.pcberp.modules.quality.entity.InspectAttachements;
import com.kyb.pcberp.modules.quality.entity.SourceDetectionAttachements;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.mapper.JsonMapper;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.CommonUtils;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.purch.entity.Purchasing;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import com.kyb.pcberp.modules.quality.entity.SourceDetection;
import com.kyb.pcberp.modules.quality.service.SourceDetectionService;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

/**
 * 来料检测Controller
 * 
 * <AUTHOR>
 * @version 2016-05-19
 */
@Controller
@RequestMapping(value = "${adminPath}/quality/sourcedetection")
public class SourceDetectionController extends BaseController
{
    
    private final ObjectMapper quotationMapper = new JsonMapper().enableSimple();
    
    private final MappingJackson2JsonView view = new MappingJackson2JsonView();
    
    @Autowired
    private SourceDetectionService resourceDetectionService;

    @Autowired
    private PurchasingDetailService purchasingDetailService;
    
    // @Autowired
    // private ParentMessageService parentMessageService;
    
    /**
     * 定位页面
     * 
     * @param purchasing
     * @return
     */
    @RequestMapping(value = "list")
    public String list()
    {
        return "modules/quality/sourcedetection";
    }
    
    /**
     * 分页查询来料检测记录
     * 
     * @param notification
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("quality:sourcedetection:view")
    @RequestMapping(value = {"page"})
    @ResponseBody
    public Page<SourceDetection> getlist(@RequestBody SourceDetection sourceDetection, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        // 设置查询企业编号
        sourceDetection.setCompany(user.getCompany());
        // 分页查询数据
        Page<SourceDetection> qpage = new Page<SourceDetection>(request, response);
        if (StringUtils.isNotBlank(sourceDetection.getPageNo())
            && StringUtils.isNotBlank(sourceDetection.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(sourceDetection.getPageNo()));
            qpage.setPageSize(Integer.parseInt(sourceDetection.getPageSize()));
            qpage.setOrderBy("a.no desc");
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        // 设置查询范围
        if (sourceDetection.getQueryAll() != null && !sourceDetection.getQueryAll())
        {
            sourceDetection.setCreatedBy(user);
        }
        // 设置排序
        if (StringUtils.isNotBlank(sourceDetection.getOrderBy()))
        {
            qpage.setOrderBy(sourceDetection.getOrderBy());
        }
        if (sourceDetection.getSentTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(sourceDetection.getSentTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            sourceDetection.setSentTimeEndQr(deliTime.getTime());
        }
        if (sourceDetection.getSentTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(sourceDetection.getSentTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            sourceDetection.setSentTimeStartQr(deliTime.getTime());
        }
        
        return resourceDetectionService.findPage(qpage, sourceDetection);
    }
    
    /**
     * 添加或修改来料检测
     * 
     * @param sourceDetection
     * @return
     */
    @RequiresPermissions("quality:sourcedetection:edit")
    @RequestMapping(value = "save")
    @ResponseBody
    public String save(@RequestBody SourceDetection sourceDetection, Model mode)
    {
        if (!beanValidator(mode, sourceDetection))
        {
            return "{\"result\":false,\"message\":\"" + mode.asMap().get("message") + "\"}";
        }
        boolean isNew = sourceDetection.getIsNewRecord();
        String rs = "";
        if (isNew)
        {
            rs = "{\"result\":true,\"msg\":\"来料检测单\'" + sourceDetection.getNo() + "\'添加成功.\",\"status\":"
                + sourceDetection.getStatus() + "}";
        }
        else
        {
            rs = "{\"result\":true,\"msg\":\"来料检测单\'" + sourceDetection.getNo() + "\'编辑成功.\",\"status\":"
                + sourceDetection.getStatus() + "}";
        }
        resourceDetectionService.save(sourceDetection, isNew);
        return rs;
    }
    
    /**
     * 获取来料检测编号
     * 
     * @return
     */
    @RequiresPermissions("quality:sourcedetection:view")
    @RequestMapping(value = "getno", method = {RequestMethod.GET})
    @ResponseBody
    public String getNo()
    {
        return CommonUtils.geDocumentNo(CommonEnums.CodeType.SOURCEDETECTIONS.getIndex().toString());
    }
    
    /**
     * 查询供应商采购订单信息
     * 
     * @return
     */
    @RequiresPermissions("quality:sourcedetection:view")
    @RequestMapping(value = "findPurchasings/{supplierId}/{resourceType}", method = {RequestMethod.POST})
    @ResponseBody
    public List<Purchasing> findPuNoBySupplier(@PathVariable("supplierId") String supplierId,
        @PathVariable("resourceType") String resourceType, String type)
    {
        if (type.equals(TypeKey.NO.toString()))
        {
            Purchasing purchasing = new Purchasing();
            purchasing.setSupplier(new Supplier(supplierId));
            purchasing.setCompany(UserUtils.getUser().getCompany());
            return resourceDetectionService.findPuNoBySupplier(purchasing, resourceType);
        }
        else
        {
            Purchasing purchasing = new Purchasing();
            purchasing.setSupplier(new Supplier(supplierId));
            purchasing.setCompany(UserUtils.getUser().getCompany());
            return resourceDetectionService.getReturns(purchasing, resourceType);
        }
    }
    
    /**
     * 查询采购订单的订单明细信息
     * 
     * @return
     */
    @RequiresPermissions("quality:sourcedetection:view")
    @RequestMapping(value = "findPurchasingDetails/{purchasingId}/{resourceType}", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, List<?>> findMaterialsNoByPurchasing(@PathVariable("purchasingId") String purchasingId,
        @PathVariable("resourceType") String resourceType, String type)
    {
        if (type.equals(TypeKey.NO.toString()))
        {
            return resourceDetectionService.findMaterialsNoByPurchasing(purchasingId, resourceType);
        }
        else
        {
            return resourceDetectionService.getPurchasing(purchasingId, resourceType);
        }
    }
    
    /**
     * ycy 2016-10-20 添加验证检测数量
     * 
     * @param id
     * @param resourceType
     * @return
     */
    @RequiresPermissions("quality:sourcedetection:edit")
    @RequestMapping(value = "saveTestQuantityVerification")
    @ResponseBody
    public String saveTestQuantityVerification(String id, String resourceType)
    {
        
        return resourceDetectionService.saveTestQuantityVerification(id, resourceType);
    }
    
    /**
     * ycy 2016-10-24 修改成品查询订单数量
     * 
     * @return
     */
    @RequiresPermissions("quality:sourcedetection:edit")
    @RequestMapping(value = "editTestQuantityVerification")
    @ResponseBody
    public String editTestQuantityVerification(String id)
    {
        
        return resourceDetectionService.editTestQuantityVerification(id);
    }
    
    /**
     * ycy 2016-10-24 补货数量验证
     * 
     * @return
     */
    @RequiresPermissions("quality:sourcedetection:edit")
    @RequestMapping(value = "replenishmentVerificationNumber")
    @ResponseBody
    public String replenishmentVerificationNumber(String id, String type, String number)
    {
        return resourceDetectionService.replenishmentVerificationNumber(id, type, number);
    }
    
    /**
     * 初始页面，加载数据
     * 
     * @param model
     * @return
     */
    @RequiresPermissions("quality:sourcedetection:view")
    @RequestMapping(value = "load/data")
    public View loadData(Model model, Boolean queryAll)
    {
        view.setObjectMapper(quotationMapper);
        // 返回数据
        model.addAttribute("data", resourceDetectionService.getDictValueByItems(queryAll));
        
        return view;
    }
    
    /**
     * 删除
     * 
     * @return
     */
    @RequiresPermissions("quality:sourcedetection:edit")
    @RequestMapping(value = "delete")
    @ResponseBody
    public String delete(@RequestBody SourceDetection sourceDetection)
    {
        sourceDetection.setCompany(UserUtils.getUser().getCompany());
        resourceDetectionService.delete(sourceDetection);
        return "{\"result\":true,\"message\":\"删除来料检测单\'" + sourceDetection.getNo() + "\'成功\"}";
    }
    
    /**
     * 检查编号
     * 
     * @param notification
     * @return
     */
    @RequiresPermissions("quality:sourcedetection:view")
    @RequestMapping(value = "findNoisEnable", method = RequestMethod.POST)
    @ResponseBody
    public Integer findNoisEnable(@RequestBody SourceDetection sourceDetection)
    {
        // 设置公司
        sourceDetection.setCompany(UserUtils.getUser().getCompany());
        return resourceDetectionService.findNoisEnable(sourceDetection);
    }
    
    /**
     * 导出
     * 
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("quality:sourcedetection:view")
    @RequestMapping(value = "export")
    public String exportFile(SourceDetection sourceDetection, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            // 设置查询范围
            if (sourceDetection.getQueryAll() != null && !sourceDetection.getQueryAll())
            {
                sourceDetection.setCreatedBy(UserUtils.getUser());
            }
            if (sourceDetection.getOrderBy() != null)
            {
                if (sourceDetection.getOrderBy().indexOf(".") == -1)
                {
                    sourceDetection.setOrderBy("a." + sourceDetection.getOrderBy());
                }
            }
            else
            {
                sourceDetection.setOrderBy("a.no");
            }
            String inoutTimeStartQr = request.getParameter("sentTimeStartQr");
            String inoutTimeEndQr = request.getParameter("sentTimeEndQr");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(inoutTimeStartQr)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                sourceDetection.setSentTimeStartQr(deliTime.getTime());
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(inoutTimeEndQr)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                sourceDetection.setSentTimeEndQr(deliTime.getTime());
            }
            sourceDetection.setCompany(UserUtils.getUser().getCompany());
            String fileName = "来料检测单数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            
            List<SourceDetection> sourceDetections = resourceDetectionService.findExpList(sourceDetection);
            
            new ExportExcel("来料检测单数据", SourceDetection.class, new Integer(1)).setDataList(sourceDetections)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
    }
    
    // public void sendMsg(SourceDetection sourceDetection, String msgType, String name)
    // {
    // ParentMessage parentMessage = new ParentMessage();
    // parentMessage.setMessageModuleCopy(msgType);
    // parentMessage.setMessaeDeail("单据编号为" + sourceDetection.getNo() + "的来料检测" + name);
    // if (sourceDetection.getCreatedBy() != null)
    // {
    // parentMessage.setCreateByName(sourceDetection.getCreatedBy());
    // }
    // parentMessageService.sendMessage(parentMessage);
    // }
    @RequestMapping(value = "showAdviseDelivery", method = RequestMethod.POST)
    @ResponseBody
    public List<AdviseDelivery> showAdviseDelivery(@RequestBody AdviseDelivery adviseDelivery)
    {
        adviseDelivery.setCompany(UserUtils.getUser().getCompany());
        return resourceDetectionService.showAdviseDeliveryList(adviseDelivery);
    }

    // 上传附件
    @RequiresPermissions(value = {"quality:sourcedetection:view"})
    @RequestMapping(value = "uploadInvoice")
    @ResponseBody
    public Boolean uploadInvoice(@RequestParam("file") MultipartFile file,
        @RequestParam("recordIds") String recordIds, HttpServletRequest req)
    {
        try
        {
            List<PurchasingDetail> purchasingDetailList = purchasingDetailService.getDetailId(recordIds);
            if (Collections3.isEmpty(purchasingDetailList))
            {
                return false;
            }
            for (PurchasingDetail purchasingDetail : purchasingDetailList)
            {
                String companyId = purchasingDetail.getCompanyId();

                // 文件存放路径
                String filePath = "purchasingDetail" + "/" + companyId + "/" + "purchasingDetail/proof/";
                // 文件存放路径
                String url = FileManageUtils.uploadFile(file, filePath, req);
                if (url == null)
                {
                    return false;
                }
                purchasingDetailService.savePurchasingDetailAttachements(purchasingDetail, filePath, file.getOriginalFilename());
            }
            return true;
        }
        catch (Exception r)
        {
            return false;
        }
    }

    @RequiresPermissions(value = {"quality:sourcedetection:view"})
    @RequestMapping(value = "delFile")
    @ResponseBody
    public Boolean delFile(@RequestBody SourceDetectionAttachements sourceDetectionAttachements)
    {
        // 从aliyun服务器下载
        OSSClient client =
            new OSSClient(FileManageUtils.getEndPoint(), FileManageUtils.accessKeyId, FileManageUtils.accessKeySecret);
        try
        {
            // 删除文件
            OssUtils.deleteFile(client, FileManageUtils.customFile, sourceDetectionAttachements.getRealFileName());
            // 删除记录
            purchasingDetailService.detelePurchasingDetailAttachements(sourceDetectionAttachements);
            return true;
        }
        catch (Exception e)
        {
            return false;
        }
    }

    @RequiresPermissions(value = {"quality:sourcedetection:view"})
    @RequestMapping(value = "getSourceDetectionAttachementsList/{purchasingDetailId}", method = RequestMethod.POST)
    @ResponseBody
    public List<SourceDetectionAttachements> getSourceDetectionAttachementsList(@PathVariable("purchasingDetailId") String purchasingDetailId)
    {
        SourceDetectionAttachements sourceDetectionAttachements = new SourceDetectionAttachements();
        sourceDetectionAttachements.setPurchasingDetailId(purchasingDetailId);
        sourceDetectionAttachements.setCompany(UserUtils.getUser().getCompany());
        List<SourceDetectionAttachements> list = purchasingDetailService.getSourceDetectionAttachementsList(sourceDetectionAttachements);
        return list;
    }

    @RequiresPermissions(value = {"quality:sourcedetection:view"})
    @RequestMapping(value = "getUrl")
    @ResponseBody
    public URL getUrl(@RequestBody SourceDetectionAttachements sourceDetectionAttachements)
    {
        return FileManageUtils.getUrl(sourceDetectionAttachements.getRealFileName());
    }

    @RequestMapping(value = "saveApprovalList", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> saveApprovalList(@RequestBody List<SourceDetection> saveApprovalList)
    {
        return resourceDetectionService.saveApprovalList(saveApprovalList);
    }
}