<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.hr.company_center.dao.Hr_RegimenDao">
    <select id="getRegimenManagementList" resultType="Hr_Regimen">
        SELECT * FROM hr_regimen_management WHERE activeFlag = 1
        <if test="regimenName != null and regimenName != ''">
            AND REPLACE(`regimenName`," ","") LIKE CONCAT('%', REPLACE(#{regimenName}," ",""), '%')
        </if>
        <if test="status != null and status != '' and status != '1003'">
            AND status = #{status}
        </if>
        <if test="status != null and status != '' and status == '1003'">
            AND DATE(regimenTime) <![CDATA[<]]> DATE(NOW()) AND regimenTime IS NOT NULL
        </if>
        ORDER BY
        createdDate DESC
    </select>

    <insert id="getInsertRegimen" keyProperty="recordId" useGeneratedKeys="true">
        INSERT INTO hr_regimen_management (
            regimenName,
            regimenDocument,
            regimenTime,
            createdBy,
            createdDate,
            lastUpdBy,
            lastUpdDate,
            activeFlag,
            status
        )
        VALUES
        (
            #{regimenName},
            #{regimenDocument},
            #{regimenTime},
            #{createdBy.recordId},
            NOW(),
            #{lastUpdBy.recordId},
            #{lastUpdDate},
            1,
            '1002'
        )
    </insert>

    <update id="getUpdateRegimen">
        UPDATE hr_regimen_management SET
             regimenName = #{regimenName},
             regimenDocument = #{regimenDocument},
             regimenTime = #{regimenTime},
             lastUpdBy = #{lastUpdBy.recordId},
             lastUpdDate = #{lastUpdDate}
        where recordId = #{recordId}
    </update>

    <update id="delRegimen">
        UPDATE hr_regimen_management SET
            activeFlag = 0
        where recordId = #{recordId}
    </update>

    <select id="getAttachmentList" resultType="Hr_KybAttachments">
        SELECT
            *,
            orgFileName AS "name"
        FROM hr_regimen_management_attachment
        WHERE activeFlag = 1 AND FIND_IN_SET(regimenId,#{recordId})
    </select>

    <insert id="saveRegimenAttachment">
        INSERT INTO hr_regimen_management_attachment
        (
            regimenId,
            orgFileName,
            realFileName,
            fileUrl,
            type,
            activeFlag,
            createdBy,
            createdDate
        )VALUES(
            #{regimenId},
            #{orgFileName},
            #{realFileName},
            #{fileUrl},
            #{type},
            1,
            #{createdBy.recordId},
            NOW()
        )
    </insert>

    <update id="deleteRegimenAttachment">
        UPDATE hr_regimen_management_attachment SET
            activeFlag = 2
        WHERE recordId = #{recordId}
    </update>

    <select id="getRegimenAttachmentCount" resultType="Integer">
		SELECT
			COUNT(1)
		FROM hr_regimen_management_attachment
		WHERE regimenId = #{regimenId} AND orgFileName = #{orgFileName}
	</select>

    <select id="checkName" resultType="Integer">
        SELECT
            COUNT(1)
        FROM hr_regimen_management
        WHERE activeFlag = 1 AND regimenName = #{regimenName}
        <if test="recordId != null and recordId != ''">
            AND recordId <![CDATA[<>]]> #{recordId}
        </if>
    </select>

    <select id="getRegimenList" resultType="Hr_Regimen">
        SELECT * FROM hr_regimen_management WHERE activeFlag = 1 AND status = '1002'
    </select>

    <select id="getRegimenManagementMiddleList" resultType="Hr_RegimenPublish">
        SELECT
        a.*,
        (
        SELECT
        GROUP_CONCAT(departmentName)
        FROM
        oa_department_relation
        WHERE
        FIND_IN_SET(recordId, a.architectureId)
        ) AS "path",
        b.regimenName AS "regimenName",
        d.userName AS "userName"
        FROM
        hr_regimen_management_middle a
        LEFT JOIN hr_regimen_management b ON b.recordId = a.regimenId AND b.activeFlag = 1
        LEFT JOIN icloud_sm_user d ON d.recordId = a.createdBy
        WHERE a.activeFlag = 1
        <if test="regimenName != null and regimenName != ''">
            AND REPLACE(b.regimenName," ","") LIKE CONCAT('%', REPLACE(#{regimenName}," ",""), '%')
        </if>
        <if test="status != null and status != '' and status !='1003'">
            AND a.status = #{status}
        </if>
        <if test="status != null and status != '' and status =='1003'">
            AND DATE(publishDate) <![CDATA[<]]> DATE(NOW())
        </if>
        ORDER BY a.createdDate DESC
    </select>

    <insert id="addPublishRegime" keyProperty="recordId" useGeneratedKeys="true">
        INSERT INTO hr_regimen_management_middle (
            regimenId,
            architectureId,
            publishDate,
            status,
            createdBy,
            createdDate,
            lastUpdBy,
            lastUpdDate,
            activeFlag
        )
        VALUES
        (
            #{regimenId},
            #{architectureId},
            #{publishDate},
            1002,
            #{createdBy.recordId},
            NOW(),
            #{lastUpdBy.recordId},
            NOW(),
            1
        )
    </insert>

    <update id="deletePublish">
        UPDATE hr_regimen_management_middle
        SET
            status = 1001,
            lastUpdBy = #{lastUpdBy.recordId},
            lastUpdDate = NOW()
        WHERE
            recordId = #{recordId}
    </update>

    <select id="getExecution" resultType="Integer">
        SELECT
            COUNT(1) AS "count"
        FROM
            hr_regimen_management_middle
        WHERE
            activeFlag = 1
            AND `status` = '1002'
            AND regimenId = #{recordId}
    </select>

    <update id="delCancellation">
        UPDATE hr_regimen_management SET `status` = '1001' WHERE recordId = #{recordId}
    </update>

    <update id="enableRecover">
        UPDATE hr_regimen_management SET `status` = '1002' WHERE recordId = #{recordId}
    </update>

    <update id="updatePublishRegime">
        UPDATE hr_regimen_management_middle set architectureId = #{architectureId},status = '1002',publishDate = #{publishDate} WHERE recordId = #{recordId}
    </update>

</mapper>