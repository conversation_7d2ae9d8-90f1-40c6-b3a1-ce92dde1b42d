<%@ page contentType="text/html;charset=UTF-8"%>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">生产管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="production.discards">生产线管理</a>
        </li>
    </ul>
    
    <div class="page-toolbar">
    	<button class="btn btn-fit-height default pull-right" ng-click="discardsCtrl.help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>
</div>
<!-- END 导航-->

<tabset class="tabset-margin-top">
	 <tab heading="生产线" active="discardsCtrl.tabs.viewForm.active">
		<div class="rows">
			<div class="panel panel-default">
				<div class="panel-heading font-blue-hoki">查询</div>
				<div class="panel-body">
					<form class="form-horizontal">
							<div class="col-md-6 col-lg-4">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">通知单编号：</label>
	                                <div class="col-sm-7 col-md-8">
	                                  <input type="text" class="form-control"
										ng-model="discardsCtrl.query.notification.value"
										disable-valid-styling="true" disable-invalid-styling="true" />
	                                </div>
	                            </div>
	                        </div>	
							<div class="col-md-6 col-lg-4">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">生产编号：</label>
	                                <div class="col-sm-7 col-md-8">
	                                  <input type="text" class="form-control"
										ng-model="discardsCtrl.query.feedingNo.value"
										disable-valid-styling="true" disable-invalid-styling="true" />
	                                </div>
	                            </div>
	                        </div>	
						<div class="row">
							<div class="col-sm-12">
								<button class="btn btn-default btn-default-width pull-right"
									ng-click="discardsCtrl.doQuery()">
									<i class="fa fa-search"></i> 查&nbsp;询
								</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		
			<div class="portlet light bordered">
				<div class="portlet-title">
					<div class="caption font-blue-hoki">生产线</div>
					<div class="actions" id="step7">
						<div class="portlet-input input-inline input-small">
							<button type="button" class="btn green btn-default-width"
								ng-click="discardsCtrl.addDiscards()" ng-if="discardsCtrl.right.edit">
								<i class="fa fa-plus"></i> 添加拆卡
							</button>
						</div>
					</div>
				</div>
				<div class="portlet-body">
					<div class="table-scrollable" style="margin-top:0px !important" id="step1">
						<table class="table table-striped table-bordered table-condensed table-advance table-hover">
							<thead>
								<tr class="heading">
									<th style="text-align: center;">通知单号</th>
									<th style="text-align: center;">生产编号</th>
									<th style="text-align: center;">订单面积</th>
									<th style="text-align: center;">发料面积</th>									
									<th style="text-align: center;">交货期限</th>	
									<th style="text-align: center;">下单日期</th>
									<th style="text-align: center;">结存工序</th>
									<th style="text-align: center;">结存pnl数</th>
									<th style="text-align: center;">结存pcs数</th>															
								</tr>
							</thead>
							<tbody>
								<tr ng-repeat="row in discardsCtrl.page.data.list">
									<td style="text-align: center;" ng-bind="row.notification.no"></td>
									<td style="text-align: center;" ng-bind="row.notification.craftNo"></td>
									<td style="text-align: center;">
										{{(row.orderQuantity * row.setLength * row.setWidth / row.pnlDivisor / 1000000).toFixed(6)}}
									</td>
									<td style="text-align: center;">
										{{(row.pcsQuantity * row.setLength * row.setWidth / row.pnlDivisor / 1000000).toFixed(6)}}
									</td>
									<td style="text-align: center;" ng-bind="row.notification.deliveryDate"></td>
									<td style="text-align: center;" ng-bind="row.notification.orderDate"></td>
									<td style="text-align: center;">
										<span ng-if="row.category != '' && row.category != null">
											{{row.category}}
										</span>
										<span ng-if="row.category == '' || row.category == null">
											未出库
										</span>
									</td>
									<td style="text-align: center;">
										<span ng-if="row.qtyPnlA > 0 || row.qtyPnlA != ''">A:{{row.qtyPnlA}}</span>
										<span ng-if="row.qtyPnlB > 0 || row.qtyPnlB != ''">B:{{row.qtyPnlB}}</span>
									</td>
									<td style="text-align: center;">
										<span ng-if="row.qtyPcsA > 0">A:{{row.qtyPcsA}}</span>
										<span ng-if="row.qtyPcsB > 0">B:{{row.qtyPcsB}}</span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="row page-margin-top">
						<div class="col-md-12 col-lg-6" id="step5">
							<span class="inline">每页</span> <select class="form-control inline"
								style="margin-top: 8px; width: 100px;"
								disable-valid-styling="true" disable-invalid-styling="true"
								ng-model="discardsCtrl.page.pageSize"
								ng-change="discardsCtrl.pageSizeChange()"
								ng-options="pageSizeOption for pageSizeOption in discardsCtrl.page.pageSizeOptions">
							</select> 
							<span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示{{discardsCtrl.page.data.startfeed}} /{{discardsCtrl.page.data.endfeed}} 条，共{{discardsCtrl.page.data.count}} 条</span>
						</div>
						<div class="col-md-12 col-lg-6" id="step4">
							<paging class="pull-right" page="discardsCtrl.page.data.pageNo"
								page-size="discardsCtrl.page.data.pageSize"
								total="discardsCtrl.page.data.count" adjacent="1" dots="..."
								scroll-top="false" hide-if-empty="false" ul-class="pagination"
								active-class="active" disabled-class="disabled"
								show-prev-next="true"
								paging-action="discardsCtrl.doPage(page, pageSize, total)"></paging>
						</div>
					</div>
				</div>
			</div>
		</div>
	</tab> 

	<!-- 添加拆卡 begin -->
    <tab active="discardsCtrl.tabs.editForm.active" ng-show="discardsCtrl.tabs.editForm.show">
        <tab-heading>添加拆卡 <i style="cursor: pointer" class="fa fa-times" ng-click="discardsCtrl.hideEditForm()"></i></tab-heading>
        <div class="rows" >
            <div class="portlet light bordered">
                <div class="portlet-body" >
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>通知单号:</label>
                                    <div class="col-sm-7 col-md-8">
                                          <ui-select theme="bootstrap" firstfocus="{{discardsCtrl.focus.main}}" 
   										  	 ng-model="discardsCtrl.notification" on-select="discardsCtrl.selectNotification()">
										    <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
										    <ui-select-choices refresh="discardsCtrl.seachOfData($select.search)" refresh-delay="300"
										      repeat="item in discardsCtrl.notificationList">
										      <div ng-bind-html="item.no | highlight: $select.search"></div>
										       <small> <span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.craftNo}}</span></small>
										    </ui-select-choices>
										  </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"> <span class="required">*</span>生产批次:</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select theme="bootstrap" ng-model="discardsCtrl.produceBatch" on-select="discardsCtrl.selectProduceBatch()">
                                            <ui-select-match
                                                    placeholder="请选择...">{{$select.selected.showNo}}
                                            </ui-select-match>
                                            <ui-select-choices repeat="item in discardsCtrl.produceBatchList | filter: $select.search">
                                                <div ng-bind-html="item.showNo | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"> <span class="required">*</span>批次明细:</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select theme="bootstrap" ng-model="discardsCtrl.produceBatchDetail" on-select="discardsCtrl.selectProduceBatchDetail()">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.showNo}}
                                            </ui-select-match>
                                            <ui-select-choices repeat="item in discardsCtrl.produceBatchDetailList | filter: $select.search">
                                                <div ng-bind-html="item.showNo | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                        
                        <div class="portlet-body" ng-show="discardsCtrl.showDiscardsFlag">
                        	<div class="row">
								<div class="portlet light bordered">
									<!-- 原批次明细/品质检测记录 begin-->
									<div class="row">
											<div class="portlet light bordered">
												<div class="portlet-title">
													<div class="caption font-blue-hoki">原批次明细/品质检测记录</div>
												</div>
												<div class="form-group">
														<table class="table table-striped table-bordered table-condensed table-advance table-hover">
														<thead>
															<tr class="heading">
																<th style="text-align: center;" ng-if="discardsCtrl.produceBatchDetail.qtyPcsA">A板Pcs数</th>
																<th style="text-align: center;" ng-if="discardsCtrl.produceBatchDetail.qtyPcsB">B板Pcs数</th>
																<th style="text-align: center;">总Pcs数</th>
																<th style="text-align: center;">工序名称</th>
																<th style="text-align: center;">报废数量</th>
																<th style="text-align: center;">返工数量</th>
																<th style="text-align: center;">不良数量</th>
															</tr>
														</thead>
														<tbody>
															<tr ng-repeat="row in discardsCtrl.produceBatchDetail.inspectList">
																<td rowspan="{{discardsCtrl.produceBatchDetail.inspectList.length}}" style="text-align: center;" ng-if="discardsCtrl.produceBatchDetail.qtyPcsA && $index == 0" ng-bind="discardsCtrl.produceBatchDetail.qtyPcsA"></td>
																<td rowspan="{{discardsCtrl.produceBatchDetail.inspectList.length}}" style="text-align: center;" ng-if="discardsCtrl.produceBatchDetail.qtyPcsB && $index == 0" ng-bind="discardsCtrl.produceBatchDetail.qtyPcsB"></td>
																<td rowspan="{{discardsCtrl.produceBatchDetail.inspectList.length}}" style="text-align: center;" ng-if="$index == 0" ng-bind="discardsCtrl.produceBatchDetail.qtyPcsT"></td>															
																<td style="text-align: center;" ng-bind="row.showName"></td>
																<td style="text-align: center;" ng-bind="row.discardQty"></td>
																<td style="text-align: center;" ng-bind="row.reworkQty"></td>
																<td style="text-align: center;" ng-bind="row.badQty"></td>
															</tr>
														</tbody>
													</table>
												</div>
											</div>
										</div>
										<!-- 原批次明细/品质检测记录 end -->
								</div>                        	
                        	</div>
							
	                     <form class="form-horizontal" name="discards_form" ng-init="discardsCtrl.setFormScope(this)"
                          novalidate="novalidate" ng-submit="discardsCtrl.submitMod(discards_form);"
                          ng-submit-force="true">
							<div class="row">
								<div class="portlet light bordered">
									<!-- 插卡的批次明细 begin -->
									<div class="portlet-title">
										<div class="caption font-blue-hoki">拆卡的批次明细</div>
										<div ng-if="discardsCtrl.totalFlag" class="caption">&nbsp;&nbsp;&nbsp;&nbsp;拆卡总pcs:{{discardsCtrl.disTotalPcs}}</div>
									</div>
									<div class="form-group">
										<table class="table table-striped table-bordered table-condensed table-advance table-hover">
											<thead>
												<tr class="heading">
													<th style="text-align: center;width: 5%;background-color:green;">批次明细编号</th>
													<th style="text-align: center;width: 7%" ng-if="discardsCtrl.produceBatchDetail.qtyPcsA">A板Pcs数</th>
													<th style="text-align: center;width: 7%" ng-if="discardsCtrl.produceBatchDetail.qtyPcsB">B板Pcs数</th>
													<th style="text-align: center;width: 7%">总Pcs数</th>
													<th style="text-align: center;width: 18%">操作</th>
												</tr>
											</thead>
											<tbody ng-repeat="row in discardsCtrl.discardsDeatils">
												<tr>
													<td style='text-align: center;vertical-align: middle;background-color:green;' ng-bind="$index+1"></td>
													<td style='text-align: center;vertical-align: middle;' ng-if="discardsCtrl.produceBatchDetail.qtyPcsA">
														<input class="form-control" type="text" ng-model="row.produceBatchDetail.qtyPcsA" ng-blur="discardsCtrl.comput($index,1)" required ng-onlynumberandzero/>
													</td>
													<td style='text-align: center;vertical-align: middle;' ng-if="discardsCtrl.produceBatchDetail.qtyPcsB">
														<input class="form-control" type="text" ng-model="row.produceBatchDetail.qtyPcsB" ng-blur="discardsCtrl.comput($index,2)" required ng-onlynumberandzero/>
													</td>
													<td style='text-align: center;vertical-align: middle;' ng-bind="row.produceBatchDetail.qtyPcsT"></td>
													<td style='text-align: center;vertical-align: middle;'">
														<button type="button" class="btn btn-success btn-xs" ng-click="discardsCtrl.addDiscardsDeatil()" ng-if="$index == discardsCtrl.discardsDeatils.length - 1">
		                                             		<i class="fa fa-plus"></i> 添加
		                                         		</button>
		                            					<button type="button" class="btn btn-danger btn-xs" ng-click="discardsCtrl.deleteDiscardsDeatil($index)" ng-if="$index > 0">
		                                             		<i class="fa fa-trash-o"></i> 删除
		                                         		</button>
													</td>
												</tr>
												<!-- 报废 -->
												<tr ng-if="row.discardList && row.discardList.length > 0">
													<th style="text-align: center;width: 15%">工序名称</th>
													<th style="text-align: center;width: 15%">报废数量</th>
													<th style="text-align: center;width: 15%" ng-if="discardsCtrl.produceBatchDetail.qtyPcsA">A板报废数量</th>
													<th style="text-align: center;width: 15%" ng-if="discardsCtrl.produceBatchDetail.qtyPcsB">B板报废数量</th>
												</tr>
												<tr ng-if="row.discardList && row.discardList.length > 0" ng-repeat="item in row.discardList">
													<td style='text-align: center;vertical-align: middle;' ng-bind="item.dutyProcess.category"></td>
													<td style='text-align: center;vertical-align: middle;'>
														<input class="form-control" type="text" ng-disabled="!(row.produceBatchDetail.qtyPcsA > 0 || row.produceBatchDetail.qtyPcsB > 0)" ng-model="item.discardPcsQty" ng-blur="discardsCtrl.validateInspect(item,1)" ng-onlynumberandzero/>
													</td>
													<td style='text-align: center;vertical-align: middle;' ng-if="discardsCtrl.produceBatchDetail.qtyPcsA">
														<input class="form-control" type="text" ng-disabled="!item.discardPcsQty || item.discardPcsQty == 0" ng-blur="discardsCtrl.blurDiscardPcsQtyA(item,row)" ng-model="item.discardPcsQtyA" ng-onlynumberandzero/>
													</td>
													<td style='text-align: center;vertical-align: middle;' ng-if="discardsCtrl.produceBatchDetail.qtyPcsB">
														<input class="form-control" type="text" ng-disabled="!item.discardPcsQty || item.discardPcsQty == 0" ng-blur="discardsCtrl.blurDiscardPcsQtyB(item,row)" ng-model="item.discardPcsQtyB" ng-onlynumberandzero/>
													</td>
												</tr>
												<!-- 返工 -->
												<tr ng-if="row.reworkList && row.reworkList.length > 0">
													<th style="text-align: center;width: 15%">工序名称</th>
													<th style="text-align: center;width: 15%">返工数量</th>
												</tr>
												<tr ng-if="row.reworkList && row.reworkList.length > 0" ng-repeat="item in row.reworkList">
													<td style='text-align: center;vertical-align: middle;' ng-bind="item.dutyProcess.category"></td>
													<td style='text-align: center;vertical-align: middle;'>
														<input class="form-control" type="text" ng-disabled="!(row.produceBatchDetail.qtyPcsA > 0 || row.produceBatchDetail.qtyPcsB > 0)" ng-model="item.reworkQty" ng-blur="discardsCtrl.validateInspect(item,2)" ng-onlynumberandzero/>
													</td>
												</tr>
												<!-- 不良 -->
												<tr ng-if="row.badList && row.badList.length > 0">
													<th style="text-align: center;width: 15%">工序名称</th>
													<th style="text-align: center;width: 15%">不良数量</th>
												</tr>
												<tr ng-if="row.badList && row.badList.length > 0" ng-repeat="item in row.badList">
													<td style='text-align: center;vertical-align: middle;' ng-bind="item.dutyProcess.category"></td>
													<td style='text-align: center;vertical-align: middle;'>
														<input class="form-control" type="text" ng-disabled="!(row.produceBatchDetail.qtyPcsA > 0 || row.produceBatchDetail.qtyPcsB > 0)" ng-model="item.badQty" ng-blur="discardsCtrl.validateInspect(item,3)" ng-onlynumberandzero/>
													</td>
												</tr>												
											</tbody>
										</table>
									</div>
									<!-- 插卡的批次明细 begin -->	
								</div>
							</div>
	   						<div class="row" >
						        <div class="col-sm-offset-4 col-md-6 col-lg-4">
	                                <div class="form-group">
	                                    <div class="col-sm-offset-4 col-sm-10">
	                                        <button type="submit" class="btn btn-primary btn-default-width"
	                                                style="margin-left: 15px;"
	                                                ng-if="discardsCtrl.right.edit">
	                                            <i class="fa fa-save"></i> 保&nbsp;存
	                                        </button>
	                                    </div>
	                                </div>
	                            </div>
							</div>
						</form>
                     </div>
            </div>
        </div>
    </tab>
	<!-- 添加拆卡 end -->
 
</tabset>

<div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">
                    <span>提示</span>
                </h4>
            </div>
            <div class="modal-body">
                <p>
                    <span ng-bind="discardsCtrl.message"></span>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
            </div>
        </div>
    </div>
</div>

