package com.kyb.pcberp.modules.report.web;

import com.alibaba.fastjson.JSON;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.Encodes;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.contract.entity.MaxBatchAreaConfirm;
import com.kyb.pcberp.modules.crm.entity.CustomerBillData;
import com.kyb.pcberp.modules.finance.entity.CollectMoney;
import com.kyb.pcberp.modules.finance.entity.CollectMuchMoney;
import com.kyb.pcberp.modules.production.entity.BottleneckProcessUse;
import com.kyb.pcberp.modules.production.entity.ProduceBatch;
import com.kyb.pcberp.modules.quality.entity.Inspect;
import com.kyb.pcberp.modules.quality.service.DetectionInspectService;
import com.kyb.pcberp.modules.report.entity.*;
import com.kyb.pcberp.modules.report.service.ReportService;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import com.kyb.pcberp.modules.wechat.entity.ProduceReport;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.Region;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Controller
@RequestMapping(value = "${adminPath}/report/reportData")
public class ReportController
{
    @Autowired
    private ReportService reportService;

    @Autowired
    private DetectionInspectService inspectService;

    @RequestMapping(value = "view")
    public String list()
    {
        return "modules/report/reportData";
    }

    @RequestMapping(value = "deliveryRadioReportView")
    public String deliveryRadioReportView()
    {
        return "modules/report/deliveryRadio";
    }

    @RequestMapping(value = "reconReportReportView")
    public String reconReportReportView()
    {
        return "modules/report/recon/reconReport";
    }

    @RequestMapping(value = "historyReportReportView")
    public String historyReportReportView()
    {
        return "modules/report/recon/historyReport";
    }

    @RequestMapping(value = "factSaleReportView")
    public String factSaleReportView()
    {
        return "modules/report/factSaleReport/factSaleReport";
    }

    @RequestMapping(value = "proList")
    public String proList()
    {
        return "modules/report/productionProcess/productionProcessDayList";
    }

    @RequestMapping(value = "scheduleList")
    public String scheduleList()
    {
        return "modules/report/schedule/schedule";
    }

    @RequestMapping(value = "factoryCostView")
    public String factoryCostView()
    {
        return "modules/report/factoryCost";
    }

    @RequestMapping(value = "royaltyReportView")
    public String royaltyReportView()
    {
        return "modules/report/royaltyReport";
    }

    @RequestMapping(value = "qualityDailyReportView")
    public String qualityDailyReportView()
    {
        return "modules/report/qualityDailyReport";
    }

    @RequestMapping(value = "rejectplanreportView")
    public String rejectplanreportView()
    {
        return "modules/report/rejectplan/rejectplanReport";
    }

    @RequestMapping(value = "productionReplenishmentView")
    public String productionReplenishmentView()
    {
        return "modules/report/productionReplenishment";
    }

    @RequestMapping(value = "deliveryMonitoringView")
    public String deliveryMonitoringView()
    {
        return "modules/report/deliveryMonitoring";
    }

    @RequestMapping(value = "customerStatementView")
    public String customerStatementView()
    {
        return "modules/report/customerStatement";
    }

    @RequestMapping("/getFactoryCostList")
    @ResponseBody
    public Map<String, Object> getFactoryCostList(@RequestBody RawmaterialStock rawmaterialStock)
    {
        if (rawmaterialStock.getInoutTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rawmaterialStock.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            rawmaterialStock.setInoutTimeEndQr(deliTime.getTime());
        }
        if (rawmaterialStock.getInoutTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rawmaterialStock.getInoutTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            rawmaterialStock.setInoutTimeStartQr(deliTime.getTime());
        }
        return reportService.getRawmaterialStockList(rawmaterialStock);
    }

    @RequestMapping(value = "exportFactCost")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public String exportFactCost(HttpServletRequest request, HttpServletResponse response)
        throws IOException
    {
        RawmaterialStock rawmaterialStock = new RawmaterialStock();
        String inoutTimeStartQr = request.getParameter("inoutTimeStartQr");
        String inoutTimeEndQr = request.getParameter("inoutTimeEndQr");
        String reportFlag = request.getParameter("reportFlag");
        String reportProperty = request.getParameter("reportProperty");
        String fileName = "";
        if (StringUtils.isNotBlank(reportFlag))
        {
            if (reportFlag.equals("1"))
            {
                fileName = "生产投料成本统计-区间";
            }
            else if (reportFlag.equals("2"))
            {
                fileName = "生产投料成本统计-部门";
            }
        }
        if (StringUtils.isBlank(fileName))
        {
            return null;
        }
        if (StringUtils.isNotBlank(inoutTimeStartQr))
        {
            Date startdate = new Date(Long.parseLong(inoutTimeStartQr));
            rawmaterialStock.setInoutTimeStartQr(startdate);
        }
        if (StringUtils.isNotBlank(inoutTimeEndQr))
        {
            Date enddate = new Date(Long.parseLong(inoutTimeEndQr));
            rawmaterialStock.setInoutTimeEndQr(enddate);
        }
        if (rawmaterialStock.getInoutTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rawmaterialStock.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            rawmaterialStock.setInoutTimeEndQr(deliTime.getTime());
        }
        if (rawmaterialStock.getInoutTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rawmaterialStock.getInoutTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            rawmaterialStock.setInoutTimeStartQr(deliTime.getTime());
        }

        rawmaterialStock.setReportFlag(reportFlag);
        Map<String, Object> map = reportService.getRawmaterialStockList(rawmaterialStock);
        List<String> dateList = (List<String>)map.get("dateList");
        List<String> deaprtList = (List<String>)map.get("deaprtList");
        List<FactCost> factCostList = (List<FactCost>)map.get("factCostList");

        List<String> headerList = new ArrayList<String>();
        headerList.add("分类");
        if (reportFlag.equals("1"))
        {
            for (int i = 0; i < dateList.size(); i++)
            {
                if (StringUtils.isBlank(reportProperty) || !reportProperty.equals("2"))
                {
                    headerList.add(dateList.get(i) + "数量");
                }
                if (StringUtils.isBlank(reportProperty) || !reportProperty.equals("1"))
                {
                    headerList.add(dateList.get(i) + "金额");
                }

            }
        }
        else if (reportFlag.equals("2"))
        {
            for (int i = 0; i < deaprtList.size(); i++)
            {
                if (StringUtils.isBlank(reportProperty) || !reportProperty.equals("2"))
                {
                    headerList.add(deaprtList.get(i) + "数量");
                }
                if (StringUtils.isBlank(reportProperty) || !reportProperty.equals("1"))
                {
                    headerList.add(deaprtList.get(i) + "金额");
                }
            }
        }
        if (StringUtils.isBlank(reportProperty) || !reportProperty.equals("2"))
        {
            headerList.add("汇总数量");
        }
        if (StringUtils.isBlank(reportProperty) || !reportProperty.equals("1"))
        {
            headerList.add("汇总金额");
        }
        String[] headers = new String[headerList.size()];
        headers = headerList.toArray(headers);

        ExportExcel excel = new ExportExcel(fileName, headers);
        setDataTwoList(excel, factCostList, headers, reportFlag, dateList, deaprtList, reportProperty);
        excel.write(response, fileName + ".xlsx").dispose();
        return null;
    }

    public void setDataTwoList(ExportExcel excel, List<FactCost> factCostList, String[] hearList, String reportFlag,
        List<String> dateList, List<String> deaprtList, String reportProperty)
    {
        for (FactCost one : factCostList)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList)
            {
                String val = "";
                switch (name)
                {
                    case "分类":
                        val = one.getType();
                        break;
                    case "汇总数量":
                        val = one.getNum() == null ? "0" : one.getNum().toString();
                        break;
                    case "汇总金额":
                        val = one.getMoney() == null ? "0" : one.getMoney().toString();
                        break;
                    default:
                        if (reportFlag.equals("1"))
                        {
                            for (int i = 0; i < dateList.size(); i++)
                            {
                                String numStr = dateList.get(i) + "数量";
                                String moneyStr = dateList.get(i) + "金额";
                                FactCostDeail factCostDeail = null;
                                for (int j = 0; j < one.getDeails().size(); j++)
                                {
                                    if (one.getDeails().get(j).getMonth().equals(dateList.get(i)))
                                    {
                                        factCostDeail = one.getDeails().get(j);
                                        break;
                                    }
                                }
                                if (factCostDeail == null)
                                {
                                    continue;
                                }
                                if (numStr.equals(name))
                                {
                                    val = factCostDeail.getNum() == null ? "0" : factCostDeail.getNum().toString();
                                }
                                else if (moneyStr.equals(name))
                                {
                                    val = factCostDeail.getMoney() == null ? "0" : factCostDeail.getMoney().toString();
                                }
                            }
                        }
                        else if (reportFlag.equals("2"))
                        {
                            for (int i = 0; i < deaprtList.size(); i++)
                            {
                                String numStr = deaprtList.get(i) + "数量";
                                String moneyStr = deaprtList.get(i) + "金额";
                                FactCostDeail factCostDeail = null;
                                for (int j = 0; j < one.getDeails().size(); j++)
                                {
                                    if (one.getDeails().get(j).getDepartName().equals(deaprtList.get(i)))
                                    {
                                        factCostDeail = one.getDeails().get(j);
                                        break;
                                    }
                                }
                                if (factCostDeail == null)
                                {
                                    continue;
                                }
                                if (numStr.equals(name))
                                {
                                    val = factCostDeail.getNum() == null ? "0" : factCostDeail.getNum().toString();
                                }
                                else if (moneyStr.equals(name))
                                {
                                    val = factCostDeail.getMoney() == null ? "0" : factCostDeail.getMoney().toString();
                                }
                            }
                        }
                        break;
                }

                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    // 获取时间段的所有合同数据和评估数据
    @RequestMapping("/getDeliveryReportData")
    @ResponseBody
    public List<ProduceReport> getDeliveryReportData(@RequestBody ProduceReport produceReport)
    {
        if (produceReport.getStartTime() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(produceReport.getStartTime());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            produceReport.setStartTime(deliTime.getTime());
        }
        if (produceReport.getEndTime() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(produceReport.getEndTime());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            produceReport.setEndTime(deliTime.getTime());
        }
        return reportService.getDeliveryReportData(produceReport);
    }

    @RequestMapping(value = "exportDeliveryReportData")
    public void exportDeliveryReportData(ProduceReport produceReport, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            String name = produceReport.getName();
            String exportData = produceReport.getExportData();
            List<ProduceReport> list = JSON.parseArray(exportData, ProduceReport.class);
            String fileName = name + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<String> headerList = new ArrayList<String>();
            headerList.add("通知单号");
            headerList.add("生产批次");
            headerList.add("厂编");
            headerList.add("面积");
            headerList.add("订单审批提交时间");
            headerList.add("订单审批完成时间");
/*            headerList.add("问客时间");
            headerList.add("问客回复时间");*/
            headerList.add("工程确认时间");
            headerList.add("要求交货时间");
            headerList.add("生产入库时间");
            headerList.add("最近交货时间");
            headerList.add("计划投料时间");
            headerList.add("发料时间");
            headerList.add("计划生产天数");
            headerList.add("实际生产天数");
            headerList.add("超期天数");
            headerList.add("订单类型");
            headerList.add("状态");
            String[] headers = new String[headerList.size()];
            headerList.toArray(headers);
            ExportExcel excel = new ExportExcel("", headers);
            reportService.setProduceReportDataList(excel, list, headers);
            excel.write(response, fileName).dispose();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    // 获取全部报表数据
    @RequestMapping("/getAllReportData")
    @ResponseBody
    public Map<String, Object> getAllReportData(@RequestBody Report report)
    {
        return reportService.getAllReportData(report);
    }

    /**
     * zjn 2019-01-15 销售报表导出
     *
     * @throws ParseException
     */
    @RequestMapping(value = "exportSaleReport", method = RequestMethod.POST)
    public String exportSaleReport(Report report, HttpServletRequest request, HttpServletResponse response)
        throws ParseException
    {
        Company company = UserUtils.getUser().getCompany();
        report.setCompany(company);
        String name = null;
        if (null != report.getDateType())
        {
            switch (report.getDateType())
            {
                // 日
                case 1:
                    name = report.getSentTimeStartQr() + "销售客户统计数据(" + company.getName() + ")";
                    break;
                // 周/月
                default:
                    name =
                        report.getSentTimeStartQr() + "至" + report.getSentTimeEndQr() + "销售客户统计数据(" + company.getName()
                            + ")";
                    break;
            }
        }
        else
        {
            name =
                report.getSentTimeStartQr() + "至" + report.getSentTimeEndQr() + "销售客户统计数据(" + company.getName() + ")";
        }
        List<SaleNewReportData> snrdList = reportService.getSaleNewReportDatas(report);
        String fileName = "销售客户统计数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
        try
        {
            new ExportExcel(name, SaleNewReportData.class, new Integer(1)).setDataList(snrdList)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }
        return null;
    }

    // 获取报表详情数据
    @RequestMapping(value = "/getReportDetailData")
    @ResponseBody
    public Map<String, Object> getReportDetailData(@RequestBody Report report)
    {
        return reportService.getReportDetailData(report);
    }

    // 获取区间报表数据
    @RequestMapping("/getSectionReportData")
    @ResponseBody
    public Map<String, Object> getSectionReportData(@RequestBody Report report)
    {
        return reportService.getSectionReportData(report);
    }

    @RequestMapping("/getRoyaltyData")
    @ResponseBody
    public Map<String, Object> getRoyaltyData()
    {
        return reportService.getRoyaltyData();
    }

    @RequestMapping("/getRoyaltyList")
    @ResponseBody
    public List<CollectMuchMoney> getRoyaltyList(@RequestBody CollectMoney collectMoney)
        throws ParseException
    {
        User user = UserUtils.getUser();
        if (collectMoney.getInoutTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(collectMoney.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            collectMoney.setInoutTimeEndQr(deliTime.getTime());
        }
        if (collectMoney.getInoutTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(collectMoney.getInoutTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            collectMoney.setInoutTimeStartQr(deliTime.getTime());
        }
        if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
        {
            collectMoney.setCreatedBy(user);
        }
        collectMoney.setCompany(user.getCompany());
        return reportService.getRoyaltyList(collectMoney);
    }

    @RequestMapping("/getRoyaltyDeailList")
    @ResponseBody
    public List<CollectMuchMoney> getRoyaltyDeailList(@RequestBody CollectMoney collectMoney)
    {
        User user = UserUtils.getUser();
        if (collectMoney.getInoutTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(collectMoney.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            collectMoney.setInoutTimeEndQr(deliTime.getTime());
        }
        if (collectMoney.getInoutTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(collectMoney.getInoutTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            collectMoney.setInoutTimeStartQr(deliTime.getTime());
        }
        if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
        {
            collectMoney.setCreatedBy(user);
        }
        collectMoney.setCompany(user.getCompany());
        return reportService.getRoyaltyDeailList(collectMoney);
    }

    @RequestMapping("/getRoyaltyDepartList")
    @ResponseBody
    public List<CollectMuchMoney> getRoyaltyDepartList(@RequestBody CollectMoney collectMoney)
    {
        User user = UserUtils.getUser();
        if (collectMoney.getInoutTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(collectMoney.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            collectMoney.setInoutTimeEndQr(deliTime.getTime());
        }
        if (collectMoney.getInoutTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(collectMoney.getInoutTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            collectMoney.setInoutTimeStartQr(deliTime.getTime());
        }
        if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
        {
            collectMoney.setCreatedBy(user);
        }
        collectMoney.setCompany(user.getCompany());
        return reportService.getRoyaltyDepartList(collectMoney);
    }

    @RequestMapping("/getRoyaltyMerchandiserList")
    @ResponseBody
    public List<CollectMuchMoney> getRoyaltyByMsgList(@RequestBody CollectMoney collectMoney)
    {
        User user = UserUtils.getUser();
        if (collectMoney.getInoutTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(collectMoney.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            collectMoney.setInoutTimeEndQr(deliTime.getTime());
        }
        if (collectMoney.getInoutTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(collectMoney.getInoutTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            collectMoney.setInoutTimeStartQr(deliTime.getTime());
        }
        if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
        {
            collectMoney.setCreatedBy(user);
        }
        collectMoney.setCompany(user.getCompany());
        return reportService.getRoyaltyByMsgList(collectMoney);
    }

    @RequestMapping("/getParamList")
    @ResponseBody
    public Map<String, String> getParamList()
    {
        return reportService.getParamList();
    }

    @RequestMapping(value = "exportRoyaltyList")
    public void exportRoyaltyList(CollectMoney collectMoney, HttpServletRequest request, HttpServletResponse response)
        throws IOException, ParseException
    {
        User user = UserUtils.getUser();
        if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
        {
            collectMoney.setCreatedBy(user);
        }
        // 设置时间查询范围
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        if (endTime != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(new Date(Long.parseLong(endTime)));
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            collectMoney.setInoutTimeEndQr(deliTime.getTime());
        }
        if (startTime != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(new Date(Long.parseLong(startTime)));
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            collectMoney.setInoutTimeStartQr(deliTime.getTime());
        }
        collectMoney.setCompany(user.getCompany());
        List<CollectMuchMoney> list = reportService.getRoyaltyList(collectMoney);
        // 查询所有业务员，按业务员打印
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < list.size(); i++)
        {
            if (list.get(i) != null && StringUtils.isNotBlank(list.get(i).getUserName()) && map.containsKey(list.get(i)
                .getUserName()))
            {
                continue;
            }
            map.put(list.get(i).getUserName(), list.get(i).getUserName());
        }
        ServletOutputStream out = response.getOutputStream();
        ZipOutputStream zipOutputStream = new ZipOutputStream(out);
        String zipName = "业务提成报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".zip";
        response.setContentType("application/octet-stream");
        response.setHeader("Connection", "close");
        response.setHeader("Accept-Ranges", "bytes");
        response.setHeader("Content-Disposition",
            "attachment;filename=" + new String(zipName.getBytes("GB2312"), "ISO8859-1"));
        response.setCharacterEncoding("UTF-8");
        try
        {
            for (Map.Entry<String, String> entry : map.entrySet())
            {
                String userName = entry.getKey();
                if (StringUtils.isBlank(userName))
                {
                    continue;
                }
                List<CollectMuchMoney> printList = new ArrayList<>();
                for (CollectMuchMoney money : list)
                {
                    if (money != null && StringUtils.isNotBlank(money.getUserName())
                        && userName.equals(money.getUserName()))
                    {
                        printList.add(money);
                    }
                }
                String fileName = "销售提成底稿" + userName + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
                List<String> headerList = new ArrayList<String>();
                headerList.add("业务员");
                headerList.add("回款账期");
                headerList.add("账单月");
                headerList.add("下单月");
                headerList.add("客户编号");
                headerList.add("客户名称");
                headerList.add("客户类型");
                headerList.add("结算方式");
                headerList.add("对账金额");
                headerList.add("成本费");
                headerList.add("业务费");
                headerList.add("利润");
                headerList.add("毛利率");
                headerList.add("政策");
                headerList.add("提成率");
                headerList.add("回款金额");
                headerList.add("提成");
                headerList.add("逾期扣款");
                headerList.add("未回款逾期扣款");
                headerList.add("扣款后提成");
                String[] headers = new String[headerList.size()];
                headerList.toArray(headers);
                ExportExcel excel = new ExportExcel("业务提成报表" + userName, headers, "1");
                setDataList(excel, printList, headers, 1);
                Workbook workbook = excel.getHssWb();
                ZipEntry zipEntry = new ZipEntry(fileName);
                zipOutputStream.putNextEntry(zipEntry);
                workbook.write(zipOutputStream);
            }
            zipOutputStream.flush();
            zipOutputStream.close();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "exportRoyaltyListTwo")
    public String exportRoyaltyListTwo(CollectMoney collectMoney, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
            {
                collectMoney.setCreatedBy(user);
            }
            // 设置时间查询范围
            String startTime = request.getParameter("startTime");
            String endTime = request.getParameter("endTime");
            if (endTime != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(endTime)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                collectMoney.setInoutTimeEndQr(deliTime.getTime());
            }
            if (startTime != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(startTime)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                collectMoney.setInoutTimeStartQr(deliTime.getTime());
            }
            collectMoney.setCompany(user.getCompany());
            List<CollectMuchMoney> list = reportService.getRoyaltyList(collectMoney);

            // 根据账期 + 月结方式计算出逾期月份数
            if(Collections3.isNotEmpty(list))
            {
                for(CollectMuchMoney cmm : list)
                {
                    Integer payWayRemark = null == cmm.getPayWayRemark() ? 0 : cmm.getPayWayRemark();
                    Integer period = null == cmm.getPeriod() ? 0 :  cmm.getPeriod();
                    Integer overdueMonth = 0;
                    if(payWayRemark > 0 && period > 0)
                    {
                        Date periodMonth = DateUtils.parseDate(period + "01", "yyyyMMdd");
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTime(periodMonth);
                        Integer month = 0;
                        if(payWayRemark % 30 > 0)
                        {
                            month = (payWayRemark - payWayRemark % 30) / 30;
                        }
                        else
                        {
                            month = payWayRemark / 30;
                        }
                        calendar.add(Calendar.MONTH, month);

                        Calendar calendar2 = Calendar.getInstance();
                        calendar2.setTime(new Date());

                        overdueMonth = (calendar2.get(Calendar.YEAR) - calendar.get(Calendar.YEAR)) * 12 + calendar2.get(Calendar.MONTH) - calendar.get(Calendar.MONTH);
                    }
                    cmm.setOverdueMonth(overdueMonth);
                }
            }


            String fileName = "业务提成报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";

            List<String> headerList = new ArrayList<String>();
            headerList.add("业务员");
            headerList.add("回款账期");
            headerList.add("账单月");
            headerList.add("下单月");
            headerList.add("客户编号");
            headerList.add("客户名称");
            headerList.add("客户类型");
            headerList.add("结算方式");
            headerList.add("对账金额");
            headerList.add("前3个月销售金额");
            headerList.add("部门销售金额");
            headerList.add("成本费");
            headerList.add("业务费");
            headerList.add("管销费");
            headerList.add("管理费");
            headerList.add("人工费");
            headerList.add("利润");
            headerList.add("毛利率");
            headerList.add("月净利率");
            headerList.add("政策");
            headerList.add("提成率");
            headerList.add("回款金额");
            headerList.add("提成");
            headerList.add("逾期扣款");
            headerList.add("未回款逾期扣款");
            headerList.add("扣款后提成");
            headerList.add("逾期月份数");
            String[] headers = new String[headerList.size()];
            headerList.toArray(headers);
            ExportExcel excel = new ExportExcel("", headers);
            setDataList(excel, list, headers, 1);
            excel.write(response, fileName).dispose();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "exportRoyaltyDeailList")
    public void exportRoyaltyDeailList(CollectMoney collectMoney, HttpServletRequest request,
        HttpServletResponse response)
        throws IOException
    {
        User user = UserUtils.getUser();
        if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
        {
            collectMoney.setCreatedBy(user);
        }
        // 设置时间查询范围
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        if (endTime != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(new Date(Long.parseLong(endTime)));
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            collectMoney.setInoutTimeEndQr(deliTime.getTime());
        }
        if (startTime != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(new Date(Long.parseLong(startTime)));
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            collectMoney.setInoutTimeStartQr(deliTime.getTime());
        }
        collectMoney.setCompany(user.getCompany());
        List<CollectMuchMoney> list = reportService.getRoyaltyDeailList(collectMoney);
        // 查询所有业务员，按业务员打印
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < list.size(); i++)
        {
            if (list.get(i) != null && StringUtils.isNotBlank(list.get(i).getUserName()) && map.containsKey(list.get(i)
                .getUserName()))
            {
                continue;
            }
            map.put(list.get(i).getUserName(), list.get(i).getUserName());
        }
        ServletOutputStream out = response.getOutputStream();
        ZipOutputStream zipOutputStream = new ZipOutputStream(out);
        String zipName = "销售提成底稿" + DateUtils.getDate("yyyyMMddHHmmss") + ".zip";
        response.setContentType("application/octet-stream");
        response.setHeader("Connection", "close");
        response.setHeader("Accept-Ranges", "bytes");
        response.setHeader("Content-Disposition",
            "attachment;filename=" + new String(zipName.getBytes("GB2312"), "ISO8859-1"));
        response.setCharacterEncoding("UTF-8");
        try
        {
            for (Map.Entry<String, String> entry : map.entrySet())
            {
                String userName = entry.getKey();
                if (StringUtils.isBlank(userName))
                {
                    continue;
                }
                List<CollectMuchMoney> printList = new ArrayList<>();
                for (CollectMuchMoney money : list)
                {
                    if (money != null && StringUtils.isNotBlank(money.getUserName())
                        && userName.equals(money.getUserName()))
                    {
                        printList.add(money);
                    }
                }
                String fileName = "销售提成底稿" + userName + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
                List<String> headerList = new ArrayList<String>();
                headerList.add("业务员");
                headerList.add("账单月");
                headerList.add("下单月");
                headerList.add("客户名称");
                headerList.add("客户编号");
                headerList.add("合同编号");
                headerList.add("合同明细金额");
                headerList.add("成本费");
                headerList.add("业务费");
                headerList.add("利润");
                headerList.add("毛利率");
/*                headerList.add("回款金额");
                headerList.add("库存板料");
                headerList.add("成品库存");
                headerList.add("逾期货款");
                headerList.add("风险金");
                headerList.add("累计风险金");*/
                String[] headers = new String[headerList.size()];
                headerList.toArray(headers);
                ExportExcel excel = new ExportExcel("销售提成底稿" + userName, headers, "1");
                setDataList(excel, printList, headers, 0);
                Workbook workbook = excel.getHssWb();
                ZipEntry zipEntry = new ZipEntry(fileName);
                zipOutputStream.putNextEntry(zipEntry);
                workbook.write(zipOutputStream);
            }
            zipOutputStream.flush();
            zipOutputStream.close();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "exportRoyaltyDepartList")
    public void exportRoyaltyDepartList(CollectMoney collectMoney, HttpServletRequest request,
        HttpServletResponse response)
        throws IOException, ParseException
    {
        User user = UserUtils.getUser();
        if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
        {
            collectMoney.setCreatedBy(user);
        }
        // 设置时间查询范围
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        if (endTime != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(new Date(Long.parseLong(endTime)));
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            collectMoney.setInoutTimeEndQr(deliTime.getTime());
        }
        if (startTime != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(new Date(Long.parseLong(startTime)));
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            collectMoney.setInoutTimeStartQr(deliTime.getTime());
        }
        collectMoney.setCompany(user.getCompany());
        List<CollectMuchMoney> list = reportService.getRoyaltyList(collectMoney);
        // 查询所有业务员，按业务员打印
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < list.size(); i++)
        {
            if (list.get(i) != null && StringUtils.isNotBlank(list.get(i).getDepartName())
                && map.containsKey(list.get(i).getDepartName()))
            {
                continue;
            }
            map.put(list.get(i).getDepartName(), list.get(i).getDepartName());
        }
        ServletOutputStream out = response.getOutputStream();
        ZipOutputStream zipOutputStream = new ZipOutputStream(out);
        String zipName = "1级巴负责人提成报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".zip";
        response.setContentType("application/octet-stream");
        response.setHeader("Connection", "close");
        response.setHeader("Accept-Ranges", "bytes");
        response.setHeader("Content-Disposition",
            "attachment;filename=" + new String(zipName.getBytes("GB2312"), "ISO8859-1"));
        response.setCharacterEncoding("UTF-8");
        try
        {
            for (Map.Entry<String, String> entry : map.entrySet())
            {
                String userName = entry.getKey();
                if (StringUtils.isBlank(userName))
                {
                    continue;
                }
                List<CollectMuchMoney> printList = new ArrayList<>();
                for (CollectMuchMoney money : list)
                {
                    if (money != null && StringUtils.isNotBlank(money.getDepartName())
                        && userName.equals(money.getDepartName()))
                    {
                        printList.add(money);
                    }
                }
                String fileName = "销售提成底稿" + userName + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
                List<String> headerList = new ArrayList<String>();
                headerList.add("负责人");
                headerList.add("业务员");
                headerList.add("客户名称");
                headerList.add("客户类型");
                headerList.add("合同明细金额");
                headerList.add("成本费");
                headerList.add("业务费");
                headerList.add("回款金额");
                headerList.add("利润");
                headerList.add("毛利率");
                headerList.add("提成率");
                headerList.add("提成");
                String[] headers = new String[headerList.size()];
                headerList.toArray(headers);
                ExportExcel excel = new ExportExcel("1级巴负责人提成报表" + userName, headers, "1");
                setDataList(excel, printList, headers, 2);
                Workbook workbook = excel.getHssWb();
                ZipEntry zipEntry = new ZipEntry(fileName);
                zipOutputStream.putNextEntry(zipEntry);
                workbook.write(zipOutputStream);
            }
            zipOutputStream.flush();
            zipOutputStream.close();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "exportMerchandRoyaltyListTwo")
    public String exportMerchandRoyaltyListTwo(CollectMoney collectMoney, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
            {
                collectMoney.setCreatedBy(user);
            }
            // 设置时间查询范围
            String startTime = request.getParameter("startTime");
            String endTime = request.getParameter("endTime");
            if (endTime != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(endTime)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                collectMoney.setInoutTimeEndQr(deliTime.getTime());
            }
            if (startTime != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(startTime)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                collectMoney.setInoutTimeStartQr(deliTime.getTime());
            }
            collectMoney.setCompany(user.getCompany());
            List<CollectMuchMoney> list = reportService.getRoyaltyList(collectMoney);
            String fileName = "跟单提成报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<String> headerList = new ArrayList<String>();
            headerList.add("跟单员");
            headerList.add("回款账期");
            headerList.add("账单月");
            headerList.add("下单月");
            headerList.add("客户名称");
            headerList.add("客户类型");
            headerList.add("结算方式");
            headerList.add("对账金额");
            headerList.add("回款金额");
            headerList.add("政策");
            headerList.add("提成");
            headerList.add("提成逾期考核");
            headerList.add("逾期扣款");
            headerList.add("扣款后提成");
            String[] headers = new String[headerList.size()];
            headerList.toArray(headers);
            ExportExcel excel = new ExportExcel("", headers);
            setDataList(excel, list, headers, 3);
            excel.write(response, fileName).dispose();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "exportMerchandRoyaltyList")
    public void exportMerchandRoyaltyList(CollectMoney collectMoney, HttpServletRequest request,
        HttpServletResponse response)
        throws IOException, ParseException
    {
        User user = UserUtils.getUser();
        if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
        {
            collectMoney.setCreatedBy(user);
        }
        // 设置时间查询范围
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        if (endTime != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(new Date(Long.parseLong(endTime)));
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            collectMoney.setInoutTimeEndQr(deliTime.getTime());
        }
        if (startTime != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(new Date(Long.parseLong(startTime)));
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            collectMoney.setInoutTimeStartQr(deliTime.getTime());
        }
        collectMoney.setCompany(user.getCompany());
        List<CollectMuchMoney> list = reportService.getRoyaltyList(collectMoney);
        // 查询所有业务员，按业务员打印
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < list.size(); i++)
        {
            if (list.get(i) != null && StringUtils.isNotBlank(list.get(i).getDepartName())
                && map.containsKey(list.get(i).getDepartName()))
            {
                continue;
            }
            map.put(list.get(i).getDepartName(), list.get(i).getDepartName());
        }
        ServletOutputStream out = response.getOutputStream();
        ZipOutputStream zipOutputStream = new ZipOutputStream(out);
        String zipName = "跟单员提成报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".zip";
        response.setContentType("application/octet-stream");
        response.setHeader("Connection", "close");
        response.setHeader("Accept-Ranges", "bytes");
        response.setHeader("Content-Disposition",
            "attachment;filename=" + new String(zipName.getBytes("GB2312"), "ISO8859-1"));
        response.setCharacterEncoding("UTF-8");
        try
        {
            for (Map.Entry<String, String> entry : map.entrySet())
            {
                String userName = entry.getKey();
                if (StringUtils.isBlank(userName))
                {
                    continue;
                }
                List<CollectMuchMoney> printList = new ArrayList<>();
                for (CollectMuchMoney money : list)
                {
                    if (money != null && StringUtils.isNotBlank(money.getDepartName())
                        && userName.equals(money.getDepartName()))
                    {
                        printList.add(money);
                    }
                }
                String fileName = "跟单员提成底稿" + userName + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
                List<String> headerList = new ArrayList<String>();
                headerList.add("跟单员");
                headerList.add("回款账期");
                headerList.add("账单月");
                headerList.add("下单月");
                headerList.add("客户名称");
                headerList.add("客户类型");
                headerList.add("结算方式");
                headerList.add("对账金额");
                /*headerList.add("合同明细金额");*/
                headerList.add("回款金额");
                headerList.add("政策");
                headerList.add("提成");
                headerList.add("提成逾期考核");
                headerList.add("逾期扣款");
                headerList.add("扣款后提成");

                String[] headers = new String[headerList.size()];
                headerList.toArray(headers);
                ExportExcel excel = new ExportExcel("跟单员提成报表" + userName, headers, "1");
                setDataList(excel, printList, headers, 3);
                Workbook workbook = excel.getHssWb();
                ZipEntry zipEntry = new ZipEntry(fileName);
                zipOutputStream.putNextEntry(zipEntry);
                workbook.write(zipOutputStream);
            }
            zipOutputStream.flush();
            zipOutputStream.close();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "exportMerchandManagerRoyaltyList")
    public void exportMerchandManagerRoyaltyList(CollectMoney collectMoney, HttpServletRequest request,
        HttpServletResponse response)
        throws IOException, ParseException
    {
        User user = UserUtils.getUser();
        if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
        {
            collectMoney.setCreatedBy(user);
        }
        // 设置时间查询范围
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        if (endTime != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(new Date(Long.parseLong(endTime)));
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            collectMoney.setInoutTimeEndQr(deliTime.getTime());
        }
        if (startTime != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(new Date(Long.parseLong(startTime)));
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            collectMoney.setInoutTimeStartQr(deliTime.getTime());
        }
        collectMoney.setCompany(user.getCompany());
        List<CollectMuchMoney> list = reportService.getRoyaltyList(collectMoney);
        // 查询所有业务员，按业务员打印
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < list.size(); i++)
        {
            if (list.get(i) != null && StringUtils.isNotBlank(list.get(i).getDepartName())
                && map.containsKey(list.get(i).getDepartName()))
            {
                continue;
            }
            map.put(list.get(i).getDepartName(), list.get(i).getDepartName());
        }
        ServletOutputStream out = response.getOutputStream();
        ZipOutputStream zipOutputStream = new ZipOutputStream(out);
        String zipName = "跟单经理提成报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".zip";
        response.setContentType("application/octet-stream");
        response.setHeader("Connection", "close");
        response.setHeader("Accept-Ranges", "bytes");
        response.setHeader("Content-Disposition",
            "attachment;filename=" + new String(zipName.getBytes("GB2312"), "ISO8859-1"));
        response.setCharacterEncoding("UTF-8");
        try
        {
            for (Map.Entry<String, String> entry : map.entrySet())
            {
                String userName = entry.getKey();
                if (StringUtils.isBlank(userName))
                {
                    continue;
                }
                List<CollectMuchMoney> printList = new ArrayList<>();
                for (CollectMuchMoney money : list)
                {
                    if (money != null && StringUtils.isNotBlank(money.getDepartName())
                        && userName.equals(money.getDepartName()))
                    {
                        printList.add(money);
                    }
                }
                String fileName = "跟单经理提成底稿" + userName + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
                List<String> headerList = new ArrayList<String>();
                headerList.add("跟单经理");
                headerList.add("跟单员");
                headerList.add("客户名称");
                headerList.add("客户类型");
                headerList.add("合同明细金额");
                headerList.add("回款金额");
                headerList.add("政策");
                headerList.add("提成");
                String[] headers = new String[headerList.size()];
                headerList.toArray(headers);
                ExportExcel excel = new ExportExcel("跟单经理提成报表" + userName, headers, "1");
                setDataList(excel, printList, headers, 5);
                Workbook workbook = excel.getHssWb();
                ZipEntry zipEntry = new ZipEntry(fileName);
                zipOutputStream.putNextEntry(zipEntry);
                workbook.write(zipOutputStream);
            }
            zipOutputStream.flush();
            zipOutputStream.close();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "exportSaleLeaderRoyaltyList")
    public void exportSaleLeaderRoyaltyList(CollectMoney collectMoney, HttpServletRequest request,
        HttpServletResponse response)
        throws IOException, ParseException
    {
        User user = UserUtils.getUser();
        if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
        {
            collectMoney.setCreatedBy(user);
        }
        // 设置时间查询范围
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        if (endTime != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(new Date(Long.parseLong(endTime)));
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            collectMoney.setInoutTimeEndQr(deliTime.getTime());
        }
        if (startTime != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(new Date(Long.parseLong(startTime)));
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            collectMoney.setInoutTimeStartQr(deliTime.getTime());
        }
        collectMoney.setCompany(user.getCompany());
        List<CollectMuchMoney> list = reportService.getRoyaltyList(collectMoney);
        // 查询所有业务员，按业务员打印
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < list.size(); i++)
        {
            if (list.get(i) != null && StringUtils.isNotBlank(list.get(i).getDepartName())
                && map.containsKey(list.get(i).getDepartName()))
            {
                continue;
            }
            map.put(list.get(i).getDepartName(), list.get(i).getDepartName());
        }
        ServletOutputStream out = response.getOutputStream();
        ZipOutputStream zipOutputStream = new ZipOutputStream(out);
        String zipName = "销售经理提成报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".zip";
        response.setContentType("application/octet-stream");
        response.setHeader("Connection", "close");
        response.setHeader("Accept-Ranges", "bytes");
        response.setHeader("Content-Disposition",
            "attachment;filename=" + new String(zipName.getBytes("GB2312"), "ISO8859-1"));
        response.setCharacterEncoding("UTF-8");
        try
        {
            for (Map.Entry<String, String> entry : map.entrySet())
            {
                String userName = entry.getKey();
                if (StringUtils.isBlank(userName))
                {
                    continue;
                }
                List<CollectMuchMoney> printList = new ArrayList<>();
                for (CollectMuchMoney money : list)
                {
                    if (money != null && StringUtils.isNotBlank(money.getDepartName())
                        && userName.equals(money.getDepartName()))
                    {
                        printList.add(money);
                    }
                }
                String fileName = "销售经理提成底稿" + userName + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
                List<String> headerList = new ArrayList<String>();
                headerList.add("销售经理");
                headerList.add("业务员");
                headerList.add("客户名称");
                headerList.add("客户类型");
                headerList.add("合同明细金额");
                headerList.add("前3个月销售金额");
                headerList.add("部门销售金额");
                headerList.add("成本费");
                headerList.add("业务费");
                headerList.add("回款金额");
                headerList.add("利润");
                headerList.add("毛利率");
                headerList.add("提成率");
                headerList.add("提成");
                String[] headers = new String[headerList.size()];
                headerList.toArray(headers);
                ExportExcel excel = new ExportExcel("销售经理提成报表" + userName, headers, "1");
                setDataList(excel, printList, headers, 4);
                Workbook workbook = excel.getHssWb();
                ZipEntry zipEntry = new ZipEntry(fileName);
                zipOutputStream.putNextEntry(zipEntry);
                workbook.write(zipOutputStream);
            }
            zipOutputStream.flush();
            zipOutputStream.close();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public void setDataList(ExportExcel excel, List<CollectMuchMoney> list, String[] hearList, int flag)
    {
        for (CollectMuchMoney money : list)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList)
            {
                String val = "";
                switch (name)
                {
                    case "业务员":
                        if (flag == 2 || flag == 4)
                        {
                            val = money.getOldUserName();
                        }
                        else
                        {
                            val = money.getUserName();
                        }
                        break;
                    case "回款账期":
                        val = StringUtils.isBlank(money.getCollectMoneyPeriod()) ? "-" : money.getCollectMoneyPeriod();
                        break;
                    case "账单月":
                        val = money.getPeriod() == null ? "-" : money.getPeriod().toString();
                        break;
                    case "下单月":
                        val = money.getOrderDateStr() == null ? "-" : money.getOrderDateStr();
                        break;
                    case "客户名称":
                        if (flag == 0 || flag == 1)
                        {
                            if ("合计".equals(money.getShortName()))
                            {
                                val = null == money.getShortName() ? null : money.getShortName();
                            }
                            else
                            {
                                val = null == money.getCustomer() ? null : money.getCustomer().getName();
                            }
                        }
                        else
                        {
                            val = money.getShortName() + "(" + money.getNo() + ")";
                        }
                        break;
                    case "客户编号":
                        val = null == money.getCustomer() ? null : money.getCustomer().getNo();
                        break;
                    case "合同编号":
                        val = money.getContractNo();
                        break;
                    case "合同明细金额":
                        val = money.getCondeailMoney() == null ? "-" : money.getCondeailMoney().toString();
                        break;
                    case "成本费":
                        val = money.getPrdorderMoney() == null ? "-" : money.getPrdorderMoney().toString();
                        break;
                    case "业务费":
                        val = money.getSaleFee() == null ? "-" : money.getSaleFee().toString();
                        break;
                    case "管销费":
                        val = money.getBarrelBoltFee() == null ? "-" : money.getBarrelBoltFee().toString();
                        break;
                    case "管理费":
                        val = money.getManageFee() == null ? "-" : money.getManageFee().toString();
                        break;
                    case "库存板料":
                        val = "-";
                        break;
                    case "成品库存":
                        val = "-";
                        break;
                    case "利润":
                        val = money.getProfits() == null ? "-" : money.getProfits().toString();
                        break;
                    case "毛利率":
                        val = money.getGrossProfit() == null ?
                            "-" :
                            money.getGrossProfit()
                                .multiply(new BigDecimal(100))
                                .setScale(2, BigDecimal.ROUND_HALF_UP)
                                .toString() + "%";
                        break;
                    case "月净利率":
                        val = money.getGrossProfitMonth() == null ?
                                "-" :
                                money.getGrossProfitMonth()
                                        .multiply(new BigDecimal(100))
                                        .setScale(2, BigDecimal.ROUND_HALF_UP)
                                        .toString() + "%";
                        break;
                    case "提成率":
                        val = money.getRoyaltyRate() == null ?
                            "-" :
                            money.getRoyaltyRate()
                                .multiply(new BigDecimal(100))
                                .setScale(2, BigDecimal.ROUND_HALF_UP)
                                .toString() + "%";
                        break;
                    case "执行提成比例":
                        val = money.getNewRoyaltyRate() == null ?
                            "-" :
                            money.getNewRoyaltyRate()
                                .multiply(new BigDecimal(100))
                                .setScale(2, BigDecimal.ROUND_HALF_UP)
                                .toString() + "%";
                        break;
                    case "回款金额":
                        val = money.getAmount() == null ? "-" : money.getAmount().toString();
                        break;
                    case "提成":
                        val = money.getRoyalty() == null ? "-" : money.getRoyalty().toString();
                        break;
                    case "提成逾期考核":
                        val = money.getOverdueRoyalty() == null ? "-" : money.getOverdueRoyalty().toString();
                        break;
                    case "风险金":
                        val = money.getPeriodFee() == null ? "-" : money.getPeriodFee().toString();
                        break;
                    case "累计风险金":
                        val = money.getRiskFee() == null ? "-" : money.getRiskFee().toString();
                        break;
                    case "客户类型":
                        if (!"合计".equals(money.getShortName()))
                        {
                            val = (StringUtils.isNotBlank(money.getCustType()) && money.getCustType().equals("2")) ?
                                "自主开发" :
                                "公司分配";
                        }
                        break;
                    case "业务员新单提成比例":
                        val = money.getOneRoyaltyRate() == null ?
                            "-" :
                            money.getOneRoyaltyRate()
                                .multiply(new BigDecimal(100))
                                .setScale(2, BigDecimal.ROUND_HALF_UP)
                                .toString() + "%";
                        break;
                    case "公司分配提成比例":
                        val = money.getTwoRoyaltyRate() == null ?
                            "-" :
                            money.getTwoRoyaltyRate()
                                .multiply(new BigDecimal(100))
                                .setScale(2, BigDecimal.ROUND_HALF_UP)
                                .toString() + "%";
                        break;
                    case "负责人":
                        val = money.getDepartName();
                        break;
                    case "跟单员":
                        if (flag == 5)
                        {
                            val = money.getOldUserName();
                        }
                        else
                        {
                            val = money.getCreateByName();
                        }
                        break;
                    case "销售经理":
                        val = money.getUserName();
                        break;
                    case "跟单经理":
                        val = money.getUserName();
                        break;
                    case "政策":
                        val = money.getPolicy() == null ?
                            "-" :
                            money.getPolicy()
                                .multiply(new BigDecimal(100))
                                .setScale(2, BigDecimal.ROUND_HALF_UP)
                                .toString() + "%";
                        break;
                    case "前3个月销售金额":
                        val = null == money.getFirstAmount() ? "" : money.getFirstAmount().toString();
                        break;
                    case "部门销售金额":
                        val = null == money.getDeptAmount() ? "" : money.getDeptAmount().toString();
                        break;
                    case "对账金额":
                        val = money.getReconcilingAmount() == null ? "-" : money.getReconcilingAmount().toString();
                        break;
                    case "出货月":
                        val = money.getShippingMonth();
                        break;
                    case "逾期货款":
                        val = money.getOverduePayment() == null ? "-" : money.getOverduePayment().toString();
                        break;
                    case "逾期扣款":
                        val = money.getLateDeduction() == null ? "-" : money.getLateDeduction().toString();
                        break;
                    case "结算方式":
                        val = money.getPayWayValue();
                        break;
                    case "未回款逾期扣款":
                        val = money.getLateDeductionTwo() == null ? "-" : money.getLateDeductionTwo().toString();
                        break;
                    case "扣款后提成":
                        val = money.getAfterRoyalty() == null ? "-" : money.getAfterRoyalty().toString();
                        break;
                    case "逾期月份数":
                        val = null == money.getOverdueMonth() ? "0" : money.getOverdueMonth().toString();
                        break;
                    case "人工费":
                        val = null == money.getLaborCost() ? "" : money.getLaborCost().toString();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
            if (null != money.getSumObj() && StringUtils.isNotBlank(money.getSumObj().getShortName()))
            {
                colunm = 0;
                row = excel.addRow();
                money = (CollectMuchMoney)money.getSumObj().clone();
                for (String name : hearList)
                {
                    String val = "";
                    switch (name)
                    {
/*                        case "业务员":
                            val = money.getUserName();
                            break;
                        case "账单月":
                            val = money.getPeriod() == null ? "-" : money.getPeriod().toString();
                            break;
                        case "下单月":
                            val = money.getOrderDateStr() == null ? "-" : money.getOrderDateStr();
                            break;*/
                        case "客户名称":
                            if (flag == 0 || flag == 1)
                            {
                                val = null == money.getShortName() ? null : money.getShortName();
                            }
                            else
                            {
                                val = money.getShortName() + "(" + money.getNo() + ")";
                            }
                            break;
/*                        case "客户编号":
                            val = null == money.getCustomer() ? null : money.getCustomer().getNo();
                            break;*/
                        case "合同编号":
                            val = money.getContractNo();
                            break;
                        case "对账金额":
                            val = money.getReconcilingAmount() == null ? "-" : money.getReconcilingAmount().toString();
                            break;
                        case "成本费":
                            val = money.getPrdorderMoney() == null ? "-" : money.getPrdorderMoney().toString();
                            break;
                        case "业务费":
                            val = money.getSaleFee() == null ? "-" : money.getSaleFee().toString();
                            break;
                        case "管销费":
                            val = money.getBarrelBoltFee() == null ? "-" : money.getBarrelBoltFee().toString();
                            break;
                        case "管理费":
                            val = money.getManageFee() == null ? "-" : money.getManageFee().toString();
                            break;
                        case "库存板料":
                            val = "-";
                            break;
                        case "成品库存":
                            val = "-";
                            break;
                        case "逾期货款":
                            val = "-";
                            break;
                        case "利润":
                            val = money.getProfits() == null ? "-" : money.getProfits().toString();
                            break;
                        case "毛利率":
                            val = money.getGrossProfit() == null ?
                                "-" :
                                money.getGrossProfit()
                                    .multiply(new BigDecimal(100))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP)
                                    .toString() + "%";
                            break;
                        case "提成率":
                            val = money.getRoyaltyRate() == null ?
                                "-" :
                                money.getRoyaltyRate()
                                    .multiply(new BigDecimal(100))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP)
                                    .toString() + "%";
                            break;
                        case "执行提成比例":
                            val = money.getNewRoyaltyRate() == null ?
                                "-" :
                                money.getNewRoyaltyRate()
                                    .multiply(new BigDecimal(100))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP)
                                    .toString() + "%";
                            break;
                        case "回款金额":
                            val = money.getAmount() == null ? "-" : money.getAmount().toString();
                            break;
                        case "提成":
                            val = money.getRoyalty() == null ? "-" : money.getRoyalty().toString();
                            break;
                        case "风险金":
                            val = money.getPeriodFee() == null ? "-" : money.getPeriodFee().toString();
                            break;
                        case "累计风险金":
                            val = money.getRiskFee() == null ? "-" : money.getRiskFee().toString();
                            break;
                        case "客户类型":
              /*              val = (StringUtils.isNotBlank(money.getCustType()) && money.getCustType().equals("2")) ?
                                "公司分配" :
                                "自主开发";*/
                            break;
                        case "业务员新单提成比例":
                            val = money.getOneRoyaltyRate() == null ?
                                "-" :
                                money.getOneRoyaltyRate()
                                    .multiply(new BigDecimal(100))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP)
                                    .toString() + "%";
                            break;
                        case "公司分配提成比例":
                            val = money.getTwoRoyaltyRate() == null ?
                                "-" :
                                money.getTwoRoyaltyRate()
                                    .multiply(new BigDecimal(100))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP)
                                    .toString() + "%";
                            break;
                        case "负责人":
                            val = money.getDepartName();
                            break;
                        case "跟单员":
                            val = money.getCreateByName();
                            break;
                        case "销售经理":
                            val = money.getUserName();
                            break;
                        case "跟单经理":
                            val = money.getUserName();
                            break;
                        case "政策":
                            val = money.getPolicy() == null ?
                                "-" :
                                money.getPolicy()
                                    .multiply(new BigDecimal(100))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP)
                                    .toString() + "%";
                            break;
                        case "未回款逾期扣款":
                            val = money.getLateDeductionTwo() == null ? "-" : money.getLateDeductionTwo().toString();
                            break;
                        case "逾期扣款":
                            val = money.getLateDeduction() == null ? "-" : money.getLateDeduction().toString();
                            break;
                        case "扣款后提成":
                            val = money.getAfterRoyalty() == null ? "-" : money.getAfterRoyalty().toString();
                            break;
                        case "部门销售金额":
                            val = null == money.getDeptAmount() ? "" : money.getDeptAmount().toString();
                            break;
                    }
                    excel.addCell(row, colunm++, val, 2, null);
                }
            }
        }
    }

    @RequestMapping("/getRoyaltySetUpList")
    @ResponseBody
    public List<RoyaltyRate> getRoyaltySetUpList(@RequestBody RoyaltyRate royaltyRate)
    {
        User user = UserUtils.getUser();
        if (royaltyRate.getQueryAll() == null || !royaltyRate.getQueryAll())
        {
            royaltyRate.setCreatedBy(user);
        }
        royaltyRate.setCompany(user.getCompany());
        return reportService.getRoyaltySetUpList(royaltyRate);
    }

    // 获取报表加载数据
    @RequestMapping("/loadParmData")
    @ResponseBody
    public Map<String, Object> loadParmData()
        throws ParseException
    {
        return reportService.loadParmData();
    }

    @RequestMapping("/saveRoyaltyRate")
    @ResponseBody
    public Map<String, Object> saveRoyaltyRate(@RequestBody List<RoyaltyRate> list)
    {
        return reportService.saveRoyaltyRate(list, 1);
    }

    @RequestMapping("/saveRoyaltyRateAscription")
    @ResponseBody
    public Map<String, Object> saveRoyaltyRateAscription(@RequestBody List<RoyaltyRateAscription> list)
    {
        return reportService.saveRoyaltyRate(list, 2);
    }

    @RequestMapping("/saveRoyaltyRateDetail")
    @ResponseBody
    public Map<String, Object> saveRoyaltyRateDetail(@RequestBody List<RoyaltyRateDetail> list)
    {
        return reportService.saveRoyaltyRate(list, 3);
    }

    @RequestMapping("/deleteRoyaltyRate")
    @ResponseBody
    public Map<String, Object> deleteRoyaltyRate(@RequestBody RoyaltyRate royaltyRate)
    {
        return reportService.deleteRoyaltyRate(royaltyRate, 1);
    }

    @RequestMapping("/deleteRoyaltyRateAscription")
    @ResponseBody
    public Map<String, Object> deleteRoyaltyRateAscription(@RequestBody RoyaltyRateAscription royaltyRateAscription)
    {
        return reportService.deleteRoyaltyRate(royaltyRateAscription, 2);
    }

    @RequestMapping("/deleteRoyaltyRateDetail")
    @ResponseBody
    public Map<String, Object> deleteRoyaltyRateDetail(@RequestBody RoyaltyRateDetail royaltyRateDetail)
    {
        return reportService.deleteRoyaltyRate(royaltyRateDetail, 3);
    }

    @RequestMapping("/getDayList")
    @ResponseBody
    public Map<String,Object> getDayList()
    {
        return reportService.getDayList();
    }

    @RequestMapping("/saveHandleRecord")
    @ResponseBody
    public Map<String,Object> saveHandleRecord(@RequestBody List<MaxBatchAreaConfirm> list)
    {
        return reportService.saveHandleRecord(list);
    }

    @RequestMapping(value = {"getProduceReport"})
    @ResponseBody
    public List<ProduceBatch> getProduceReport(@RequestBody Inspect inspect)
    {
        List<ProduceBatch> lists = inspectService.getPlannrePlenishmentData(inspect);
        return lists;
    }
//    /**
//     * 逾期报表导出
//     *
//     * @throws ParseException
//     */
//    @RequestMapping(value = "exportReportData", method = RequestMethod.POST)
//    public String exportReportData(Report report, HttpServletRequest request, HttpServletResponse response)
//            throws ParseException
//    {
//        Company company = UserUtils.getUser().getCompany();
//        report.setCompany(company);
//        String name = "逾期订单明细数据";
//        List<overdueData> overdueList = reportService.getExportReportData(report);
//        String fileName = "逾期款数明细" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
//        try
//        {
//            new ExportExcel(name, overdueData.class, new Integer(1)).setDataList(overdueList)
//                    .write(response, fileName)
//                    .dispose();
//            return null;
//        }
//        catch (IOException e)
//        {
//            e.printStackTrace();
//        }
//        return null;
//    }
    @RequestMapping(value = "exportReportData",method = RequestMethod.POST)
    public void exportDeliveryReportData(overdueData report, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            String fileName = "逾期订单"+DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            String exportData = report.getExportData();
            exportData = exportData.replace("&quot;","'");
            List<overdueData> list = JSON.parseArray(exportData , overdueData.class);
            List<String>headerList = new ArrayList<>();
            headerList.add("合同编号");
            headerList.add("客户编号");
            headerList.add("客户名称");
            headerList.add("客户订单号");
            headerList.add("客户型号");
            headerList.add("生产编号");
            headerList.add("面积");
            headerList.add("单价");
            headerList.add("数量");
            headerList.add("总价");
            headerList.add("下单时间");
            headerList.add("状态");
            String[] headers = new String[headerList.size()];
            headerList.toArray(headers);
            ExportExcel excel = new ExportExcel("", headers);
            reportService.setDownLoad(excel, list, headers);
            excel.write(response,fileName ).dispose();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = {"getDeliveryMonitoringList"})
    @ResponseBody
    public Page<ContractDetail> getDeliveryMonitoringList(@RequestBody ContractDetail contractDetail,HttpServletRequest request,
        HttpServletResponse response)
    {
        // 分页查询数据
        Page<ContractDetail> qpage = new Page<ContractDetail>(request, response);
        if (StringUtils.isNotBlank(contractDetail.getPageNo()) && StringUtils.isNotBlank(contractDetail.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(contractDetail.getPageNo()));
            qpage.setPageSize(Integer.parseInt(contractDetail.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        Page<ContractDetail> page = reportService.getDeliveryMonitoringList(qpage, contractDetail);
        return page;
    }

    @RequestMapping(value = {"getOrderDeailData"})
    @ResponseBody
    public Map<String,List<?>> getOrderDeailData(@RequestBody ContractDetail contractDetail)
    {
        return reportService.getOrderDeailData(contractDetail);
    }

    @RequestMapping(value = {"getCustomerStatementList"})
    @ResponseBody
    public Page<CustomerBillData> getCustomerStatementList(@RequestBody CustomerBillData customerBillData, HttpServletRequest request,
                                                         HttpServletResponse response)
    {
        // 分页查询数据
        Page<CustomerBillData> qpage = new Page<CustomerBillData>(request, response);
        if (StringUtils.isNotBlank(customerBillData.getPageNo()) && StringUtils.isNotBlank(customerBillData.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(customerBillData.getPageNo()));
            qpage.setPageSize(Integer.parseInt(customerBillData.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        Page<CustomerBillData> page = reportService.getCustomerStatementList(qpage, customerBillData);
        return page;
    }

    @RequestMapping(value = {"getDetailMessageList"})
    @ResponseBody
    public List<CustomerBillData> getDetailMessageList(@RequestBody CustomerBillData customerBillData)
    {
        return reportService.getDetailMessageList(customerBillData);
    }

    @RequestMapping(value = "exportRoyaltyListThree")
    public String exportRoyaltyListThree(CollectMoney collectMoney, HttpServletRequest request,
                                               HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            if (collectMoney.getQueryAll() == null || !collectMoney.getQueryAll())
            {
                collectMoney.setCreatedBy(user);
            }
            // 设置时间查询范围
            String startTime = request.getParameter("startTime");
            String endTime = request.getParameter("endTime");
            if (endTime != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(endTime)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                collectMoney.setInoutTimeEndQr(deliTime.getTime());
            }
            if (startTime != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(startTime)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                collectMoney.setInoutTimeStartQr(deliTime.getTime());
            }
            collectMoney.setCompany(user.getCompany());
            List<CollectMuchMoney> list = reportService.getRoyaltyList(collectMoney);
            String fileName = "未回款逾期扣款报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<String> headerList = new ArrayList<String>();
            headerList.add("业务员");
            headerList.add("逾期时间");
            headerList.add("账期");
            headerList.add("下单月");
            headerList.add("客户编号");
            headerList.add("客户名称");
            headerList.add("客户类型");
            headerList.add("月结方式");
            headerList.add("对账金额");
            headerList.add("成本费用");
            headerList.add("业务费用");
            headerList.add("管销费用");
            headerList.add("管理费用");
            headerList.add("人工费用");
            headerList.add("利润");
            headerList.add("净利率");
            headerList.add("月净利率");
            headerList.add("政策");
            headerList.add("扣除比例(%)");
            headerList.add("未回款金额");
            headerList.add("提成金额");
            headerList.add("逾期金额");
            headerList.add("未回款逾期金额");
            headerList.add("扣款后提成");
            headerList.add("部门");
            String[] headers = new String[headerList.size()];
            headerList.toArray(headers);
            ExportExcel excel = new ExportExcel("", headers);
            setDataListTwo(excel, list, headers);
            excel.write(response, fileName).dispose();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }

    public void setDataListTwo(ExportExcel excel, List<CollectMuchMoney> list, String[] hearList)
    {
        for (CollectMuchMoney money : list) {
            if (null != money.getSumObj() && Collections3.isNotEmpty(money.getSumObj().getOverdueList()))
            {
                for (OverdueDeductions ductions : money.getSumObj().getOverdueList())
                {
                    int colunm = 0;
                    Row row = excel.addRow();
                    for (String name : hearList) {
                        String val = "";
                        switch (name) {
                            case "业务员":
                                val = null == ductions.getUserName() ? "" : ductions.getUserName();
                                break;
                            case "逾期时间":
                                val = null == ductions.getOverdueDateStr() ? "" : ductions.getOverdueDateStr();
                                break;
                            case "账期":
                                val = null == ductions.getPeriod() ? "" : String.valueOf(ductions.getPeriod());
                                break;
                            case "客户名称":
                                val = null == ductions.getCusName() ? "" : ductions.getCusName();
                                break;
                            case "客户类型":
                                val = (StringUtils.isNotBlank(ductions.getCustType()) && ductions.getCustType().equals("2")) ?
                                        "自主开发" : "公司分配";
                                break;
                            case "月结方式":
                                val = null == ductions.getPayWayValue() ? "" : ductions.getPayWayValue();
                                break;
                            case "对账金额":
                                val = null == ductions.getReconcilingAmount() ? "" : String.valueOf(ductions.getReconcilingAmount());
                                break;
                            case "成本费用":
                                val = null == ductions.getPrdorderMoney() ? "" : String.valueOf(ductions.getPrdorderMoney());
                                break;
                            case "业务费用":
                                val = null == ductions.getSaleFee() ? "" : String.valueOf(ductions.getSaleFee());
                                break;
                            case "管销费用":
                                val = null == ductions.getBarrelBoltFee() ? "" : String.valueOf(ductions.getBarrelBoltFee());
                                break;
                            case "管理费用":
                                val = null == ductions.getManageFee() ? "" : String.valueOf(ductions.getManageFee());
                                break;
                            case "利润":
                                val = null == ductions.getProfits() ? "" : String.valueOf(ductions.getProfits());
                                break;
                            case "净利率":
                                val = null == ductions.getGrossProfit() ? "" : String.valueOf(ductions.getGrossProfit().multiply(new BigDecimal(100))
                                        .setScale(2, BigDecimal.ROUND_HALF_UP)
                                        .toString() + "%");
                                break;
                            case "月净利率":
                                val = null == ductions.getGrossProfitMonth() ? "" : String.valueOf(ductions.getGrossProfitMonth().multiply(new BigDecimal(100))
                                        .setScale(2, BigDecimal.ROUND_HALF_UP)
                                        .toString() + "%");
                                break;
                            case "政策":
                                val = null == ductions.getPolicy() ? "" : String.valueOf(ductions.getPolicy());
                                break;
                            case "扣除比例(%)":
                                val = null == ductions.getDeductionRate() ? "" : String.valueOf(ductions.getDeductionRate());
                                break;
                            case "未回款金额":
                                val = null == ductions.getAmount() ? "" : String.valueOf(ductions.getAmount());
                                break;
                            case "提成金额":
                                val = null == ductions.getRateAmount() ? "" : String.valueOf(ductions.getRateAmount());
                                break;
                            case "逾期金额":
                                val = null == ductions.getDeductionAmount() ? "" : String.valueOf(ductions.getDeductionAmount());
                                break;
                            case "部门":
                                val = null == ductions.getGroupOrgName() ? "" : ductions.getGroupOrgName();
                                break;
                            case "人工费用":
                                val = null == ductions.getLaborCost() ? "" : ductions.getLaborCost().toString();
                                break;
                        }
                        excel.addCell(row, colunm++, val, 2, null);
                    }
                }
            }
            else
            {
                continue;
            }
        }
    }
}
