package com.kyb.pcberp.modules.report.web;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.report.entity.*;
import com.kyb.pcberp.modules.report.service.ReportComplaintService;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

@Controller
@RequestMapping(value = "${adminPath}/report/reportComplaint")
public class ReportComplaintController extends BaseController
{
    
    @Autowired
    private ReportComplaintService reportComplaintService;
    
    @RequestMapping(value = "view")
    public String list()
    {
        return "modules/report/complaintReport/complaintReport";
    }
    
    @RequestMapping(value = "versionList", method = RequestMethod.GET)
    @ResponseBody
    public List<ReportComplaintVersion> versionList()
    {
        User user = UserUtils.getUser();
        return reportComplaintService.versionList(user.getCompany().getRecordId());
    }
    
    @RequestMapping(value = "createVersion", method = RequestMethod.GET)
    @Transactional
    @ResponseBody
    public String createVersion()
    {
        User user = UserUtils.getUser();
        ReportComplaintVersion version = new ReportComplaintVersion();
        version.setCompanyId(user.getCompany().getRecordId());
        version.setVersionDate(new Date());
        version.setSortNum(0);
        version.setCreatedBy(user);
        version.setCreatedDate(new Date());
        Integer i = reportComplaintService.createVersion(version);
        if (i > 0)
        {
            return "success";
        }
        else
        {
            return "fail";
        }
    }
    
    @RequestMapping(value = "complaintRecord", method = RequestMethod.POST)
    @ResponseBody
    public ReportComplaintRecord complaintRecord(@RequestBody String versionId)
    {
        User user = UserUtils.getUser();
        return reportComplaintService.findByVersionId(versionId, user.getCompanyId());
    }
    
    @RequestMapping(value = "complaintRecordSave", method = RequestMethod.POST)
    @Transactional
    @ResponseBody
    public Map<String, Object> complaintRecordSave(@RequestBody ReportComplaintRecord record)
    {
        Map<String, Object> data = new HashMap<>();
        if (record == null)
        {
            data.put("result", "fail");
            data.put("message", "参数错误，请刷新重试！");
            return data;
        }
        int i;
        if (StringUtils.isBlank(record.getRecordId()))
        {
            i = reportComplaintService.insertRecord(record);
        }
        else
        {
            i = reportComplaintService.updateRecord(record);
        }
        if (i > 0)
        {
            data.put("result", "success");
            data.put("message", "保存成功！");
        }
        else
        {
            data.put("result", "fail");
            data.put("message", "保存失败！");
        }
        return data;
    }
    
    @RequestMapping(value = "load/data", method = {RequestMethod.GET})
    @ResponseBody
    public Map<String, Object> loadDate()
    {
        Map<String, Object> data = new HashMap<>();
        Calendar cal = Calendar.getInstance();
        List<Integer> periodList = Lists.newArrayList();
        try
        {
            periodList = DateUtils.getPeriodList(null, null);
        }
        catch (ParseException e)
        {
            e.printStackTrace();
        }
        Integer period1 = null;
        Integer month = cal.get(Calendar.MONTH) + 2;
        if (month > 12)
        {
            period1 = (cal.get(Calendar.YEAR) + 1) * 100 + (month - 12);
        }
        else
        {
            period1 = cal.get(Calendar.YEAR) * 100 + (month);
        }
        periodList.add(period1);
        data.put("periodList", periodList);
        
        cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.MONTH, -1);
        String period = DateUtils.formatDate(cal.getTime(), "yyyyMM");
        data.put("period", period);
        return data;
    }
    
    @RequestMapping(value = "reportList", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> reportList(@RequestBody ReportComplaintCustomerVo vo)
    {
        Map<String, Object> rest = new HashMap<>();
        ReportComplaintRecord complaintRecord = reportComplaintService.getComplaintSetByMonth(vo.getApplyDate());
        if(null == complaintRecord)
        {
            return rest;
        }
        List<ReportComplaintSummaryVo> summaryVoList = new ArrayList<>();
        List<ReportComplaintCustomerVo> complaintRateList =
            reportComplaintService.customerComplaintRate(vo.getApplyDate(), vo.getDepartName());
        List<ReportComplaintDeliveryVo> deliveryList =
            reportComplaintService.deliveryList(vo.getApplyDate(), vo.getDepartName());
        List<ReportComplaintCustomerVo> rateGroupByDepart = reportComplaintService
                .customerComplaintRateGroupByDepart(vo.getApplyDate());
        for (ReportComplaintCustomerVo reportComplaintCustomerVo : rateGroupByDepart) {
            ReportComplaintSummaryVo summaryVo = new ReportComplaintSummaryVo();
            summaryVo.setComplaintRateSize(reportComplaintCustomerVo.getQuantity() == null ? 0 : Integer.valueOf(reportComplaintCustomerVo.getQuantity()));
            summaryVo.setDeliverySize(0);
            summaryVo.setDepartName(reportComplaintCustomerVo.getDepartName());
            summaryVo.setVersionId(complaintRecord.getVersionId());
            summaryVo.setArea(reportComplaintCustomerVo.getArea());
            summaryVo.setDepartId(reportComplaintCustomerVo.getDepartId());
            summaryVoList.add(summaryVo);
        }

        List<ReportComplaintDeliveryVo> deliveryGroupByDepart = reportComplaintService
                .deliveryGroupByDepart(vo.getApplyDate());

        for (ReportComplaintDeliveryVo reportComplaintDeliveryVo : deliveryGroupByDepart) {
            boolean flat = false;
            for (ReportComplaintSummaryVo summaryVo : summaryVoList) {
                if (reportComplaintDeliveryVo.getDepartName().equals(summaryVo.getDepartName())) {
                    flat = true;
                    summaryVo.setDeliverySize(reportComplaintDeliveryVo.getOrderQuantity() == null ?
                            0 :
                            Integer.valueOf(reportComplaintDeliveryVo.getOrderQuantity()));
                }
            }
            if (!flat) {
                ReportComplaintSummaryVo summaryVo = new ReportComplaintSummaryVo();
                summaryVo.setDeliverySize(reportComplaintDeliveryVo.getOrderQuantity() == null ?
                        0 :
                        Integer.valueOf(reportComplaintDeliveryVo.getOrderQuantity()));
                summaryVo.setComplaintRateSize(0);
                summaryVo.setDepartName(reportComplaintDeliveryVo.getDepartName());
                summaryVo.setVersionId(complaintRecord.getVersionId());
                summaryVo.setDepartId(reportComplaintDeliveryVo.getDepartId());
                summaryVo.setArea(0.0);
                summaryVoList.add(summaryVo);
            }
        }

        for (ReportComplaintSummaryVo summaryVo : summaryVoList)
        {
            if (summaryVo.getDeliverySize() == 0)
            {
                summaryVo.setComplaintRate("0");
                summaryVo.setPenaltyPrice(0.0);
                summaryVo.setPenaltyAmount(0.0);
            }
            else
            {
                summaryVo.setComplaintRate((Math.round(
                    Double.valueOf(summaryVo.getComplaintRateSize()) / summaryVo.getDeliverySize() * 100 * 100) / 100.0)
                    + "%");
                summaryVo.setPeriodTime(vo.getApplyDate());

                Double penaltyInit = (((Double.valueOf(summaryVo.getComplaintRateSize()) / summaryVo.getDeliverySize()) * 100.0)
                - complaintRecord.getTargetValue());
                Double penalty = new BigDecimal(penaltyInit).setScale(1, BigDecimal.ROUND_DOWN).doubleValue();
                if (penalty > 0)
                {
                    Double penaltyA =Double.valueOf(String.format( "%.2f",penalty / complaintRecord.getExceedTarget() * complaintRecord.getPunish()));
                    summaryVo.setPenaltyPrice(penaltyA);
                    Double penaltyAmountB = penaltyA * summaryVo.getArea();
                    summaryVo.setPenaltyAmount(Double.valueOf(String.format("%.2f",penaltyAmountB)));
                }else{
                    summaryVo.setPenaltyPrice(0.0);
                    summaryVo.setPenaltyAmount(0.0);
                }
            }
        }

        rest.put("summaryList", summaryVoList);
        rest.put("complaintRateList", complaintRateList);
        rest.put("complaintRateNumber", complaintRateList.size());
        rest.put("deliveryNumber", deliveryList.size());
        rest.put("deliveryList", deliveryList);
        double complaintRateSize = complaintRateList.size();
        Double area = complaintRateList.stream().mapToDouble(ReportComplaintCustomerVo::getArea).sum();
        double deliverySize = deliveryList.size();
        if (deliveryList.size() > 0)
        {
            rest.put("complaintRate", (Math.round(complaintRateSize / deliverySize * 100 * 100) / 100.0) + "%");
            rest.put("penaltyAmount", 0);
            if (complaintRecord != null)
            {
                if (complaintRecord.getTargetValue() == null)
                {
                    complaintRecord.setTargetValue(0.0);
                }
                if (complaintRecord.getPunish() == null)
                {
                    complaintRecord.setPunish(0.0);
                }
                if (complaintRecord.getExceedTarget() == null)
                {
                    complaintRecord.setExceedTarget(0.0);
                }
                Double penalty = ((complaintRateSize / deliverySize) * 100.0) - complaintRecord.getTargetValue();
                if (penalty > 0)
                {
                    rest.put("penaltyAmount",
                            String.format("%.2f", (int)(penalty / complaintRecord.getExceedTarget()) * complaintRecord.getPunish()*area));
                }
            }
        }
        else
        {
            rest.put("complaintRate", 0);
            rest.put("penaltyAmount", 0);
        }
        return rest;
    }
    
    @RequestMapping(value = "exportCustomerComplaintRate")
    public String exportCustomerComplaintRate(ReportComplaintCustomerVo vo, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            List<ReportComplaintCustomerVo> proList =
                reportComplaintService.customerComplaintRate(vo.getApplyDate(), vo.getSaleComName());
            String fileName = "客诉率报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            new ExportExcel("客诉率明细", ReportComplaintCustomerVo.class, new Integer(1)).setDataList(proList)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }
    
    @RequestMapping(value = "exportDelivery")
    public String exportDelivery(ReportComplaintCustomerVo vo, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            List<ReportComplaintDeliveryVo> proList =
                reportComplaintService.deliveryList(vo.getApplyDate(), vo.getSaleComName());
            String fileName = "送货批次报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            new ExportExcel("送货批次明细", ReportComplaintDeliveryVo.class, new Integer(1)).setDataList(proList)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }

    @RequestMapping(value = "pay", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> pay(@RequestBody List<ReportComplaintAssessment> vo)
        throws CloneNotSupportedException
    {
        Map<String, Object> data = new HashMap<>();
        if (vo == null || vo.size() == 0)
        {
            data.put("result", "fail");
            data.put("message", "没有结算记录！");
            return data;
        }
        return reportComplaintService.pay(vo);
    }

    @RequestMapping(value = "exportSummaryList")
    public String exportSummaryList(ReportComplaintCustomerVo vo, HttpServletRequest request,
                                              HttpServletResponse response)
    {
        try
        {
            ReportComplaintRecord complaintRecord = reportComplaintService.getComplaintSetByMonth(vo.getApplyDate());
            if(null == complaintRecord)
            {
                return "数据错误";
            }
            List<ReportComplaintSummaryVo> summaryVoList = new ArrayList<>();
            List<ReportComplaintCustomerVo> rateGroupByDepart = reportComplaintService.customerComplaintRateGroupByDepart(vo.getApplyDate());
            for (ReportComplaintCustomerVo reportComplaintCustomerVo : rateGroupByDepart) {
                ReportComplaintSummaryVo summaryVo = new ReportComplaintSummaryVo();
                summaryVo.setComplaintRateSize(reportComplaintCustomerVo.getQuantity() == null ? 0 : Integer.valueOf(reportComplaintCustomerVo.getQuantity()));
                summaryVo.setDeliverySize(0);
                summaryVo.setDepartName(reportComplaintCustomerVo.getDepartName());
                summaryVo.setVersionId(complaintRecord.getVersionId());
                summaryVo.setArea(reportComplaintCustomerVo.getArea());
                summaryVo.setDepartId(reportComplaintCustomerVo.getDepartId());
                summaryVoList.add(summaryVo);
            }

            List<ReportComplaintDeliveryVo> deliveryGroupByDepart = reportComplaintService
                    .deliveryGroupByDepart(vo.getApplyDate());

            for (ReportComplaintDeliveryVo reportComplaintDeliveryVo : deliveryGroupByDepart) {
                boolean flat = false;
                for (ReportComplaintSummaryVo summaryVo : summaryVoList) {
                    if (reportComplaintDeliveryVo.getDepartName().equals(summaryVo.getDepartName())) {
                        flat = true;
                        summaryVo.setDeliverySize(reportComplaintDeliveryVo.getOrderQuantity() == null ?
                                0 :
                                Integer.valueOf(reportComplaintDeliveryVo.getOrderQuantity()));
                    }
                }
                if (!flat) {
                    ReportComplaintSummaryVo summaryVo = new ReportComplaintSummaryVo();
                    summaryVo.setDeliverySize(reportComplaintDeliveryVo.getOrderQuantity() == null ?
                            0 :
                            Integer.valueOf(reportComplaintDeliveryVo.getOrderQuantity()));
                    summaryVo.setComplaintRateSize(0);
                    summaryVo.setDepartName(reportComplaintDeliveryVo.getDepartName());
                    summaryVo.setVersionId(complaintRecord.getVersionId());
                    summaryVo.setDepartId(reportComplaintDeliveryVo.getDepartId());
                    summaryVo.setArea(0.0);
                    summaryVoList.add(summaryVo);
                }
            }

            for (ReportComplaintSummaryVo summaryVo : summaryVoList)
            {
                if (summaryVo.getDeliverySize() == 0)
                {
                    summaryVo.setComplaintRate("0");
                    summaryVo.setPenaltyPrice(0.0);
                    summaryVo.setPenaltyAmount(0.0);
                }
                else
                {
                    summaryVo.setComplaintRate((Math.round(
                            Double.valueOf(summaryVo.getComplaintRateSize()) / summaryVo.getDeliverySize() * 100 * 100) / 100.0)
                            + "%");
                    summaryVo.setPeriodTime(vo.getApplyDate());

                    Double penaltyInit = (((Double.valueOf(summaryVo.getComplaintRateSize()) / summaryVo.getDeliverySize()) * 100.0)
                            - complaintRecord.getTargetValue());
                    Double penalty = new BigDecimal(penaltyInit).setScale(1, BigDecimal.ROUND_DOWN).doubleValue();
                    if (penalty > 0)
                    {
                        Double penaltyA =Double.valueOf(String.format( "%.2f",penalty / complaintRecord.getExceedTarget() * complaintRecord.getPunish()));
                        summaryVo.setPenaltyPrice(penaltyA);
                        Double penaltyAmountB = penaltyA * summaryVo.getArea();
                        summaryVo.setPenaltyAmount(Double.valueOf(String.format("%.2f",penaltyAmountB)));
                    }else{
                        summaryVo.setPenaltyPrice(0.0);
                        summaryVo.setPenaltyAmount(0.0);
                    }
                }
            }
            String fileName = "客诉考核财务报表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<String> headerList = new ArrayList<String>();
            headerList.add("部门");
            headerList.add("客诉笔数");
            headerList.add("出货笔数");
            headerList.add("客诉率");
            headerList.add("惩罚单价");
            headerList.add("面积");
            headerList.add("奖惩金额");
            headerList.add("账期月份");
            String[] headers = new String[headerList.size()];
            headerList.toArray(headers);
            ExportExcel excel = new ExportExcel("", headers);
            reportComplaintService.exportSummaryList(excel, summaryVoList, headers);
            excel.write(response, fileName).dispose();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }
}
