package com.kyb.pcberp.modules.eg.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

import java.util.List;

public class ProcessDate extends DataEntity<ProcessDate> {

    private static final long serialVersionUID = 1L;

    private String processName; //名称

    private String processId; //工序id

    private String craftId; //工艺id

    private String remark; //备注

    private String principal; //负责人

    private Integer sequence; //顺序

    private List<String> processIdList;
    private List<String> craftIdList;
    private List<String> principalList;

    private String procesName; //工序

    private String craftName; //工艺

    private String principalName; //负责人

    private Integer countOverFlag; // 过数标志

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getCraftId() {
        return craftId;
    }

    public void setCraftId(String craftId) {
        this.craftId = craftId;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public List<String> getProcessIdList() {
        return processIdList;
    }

    public void setProcessIdList(List<String> processIdList) {
        this.processIdList = processIdList;
    }

    public List<String> getCraftIdList() {
        return craftIdList;
    }

    public void setCraftIdList(List<String> craftIdList) {
        this.craftIdList = craftIdList;
    }

    public List<String> getPrincipalList() {
        return principalList;
    }

    public void setPrincipalList(List<String> principalList) {
        this.principalList = principalList;
    }

    public String getProcesName() {
        return procesName;
    }

    public void setProcesName(String procesName) {
        this.procesName = procesName;
    }

    public String getCraftName() {
        return craftName;
    }

    public void setCraftName(String craftName) {
        this.craftName = craftName;
    }

    public String getPrincipalName() {
        return principalName;
    }

    public void setPrincipalName(String principalName) {
        this.principalName = principalName;
    }

    public Integer getCountOverFlag() {
        return countOverFlag;
    }

    public void setCountOverFlag(Integer countOverFlag) {
        this.countOverFlag = countOverFlag;
    }
}
