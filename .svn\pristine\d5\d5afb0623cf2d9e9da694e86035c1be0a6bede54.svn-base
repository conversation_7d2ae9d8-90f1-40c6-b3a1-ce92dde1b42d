package com.kyb.pcberp.modules.production.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.eg.entity.CardAProcessValue;
import com.kyb.pcberp.modules.production.entity.ProduceRecord;
import com.kyb.pcberp.modules.production.entity.Reply;

@MyBatisDao
public interface ReplyDao
{
    List<Reply> getList(Reply reply);
    
    List<ProduceRecord> getBatchList(Reply reply);
    
    List<ProduceRecord> getProductList(ProduceRecord produceRecord);
    
    void take(ProduceRecord produceRecord);
    
    void hand(ProduceRecord produceRecord);
    
    Integer getWaitCountNum(ProduceRecord produceRecord);
    
    void complete(ProduceRecord produceRecord);
    
    void proInStock(ProduceRecord produceRecord);
    
    void updateLastProcess(ProduceRecord produceRecord);
    
    void inpectDiscardHandle(ProduceRecord p);
    
    List<CardAProcessValue> findListByCardA(ProduceRecord produceRecord);
    
    String getCardId(@Param("no") String no);

    String getLastId(ProduceRecord produceRecord);
}
