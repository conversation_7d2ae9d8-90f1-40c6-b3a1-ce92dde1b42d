kybApp.controller('supplychainScheduleTraceCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'BaseUtil', 'CommonUtil', function ($rootScope, $scope, upida, $timeout, BaseUtil, CommonUtil) {
    $scope.$on('$viewContentLoaded', function () {
        MainCtrl.initAjax();
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
    var vm = this;
    vm.right = {};

    // tabs控制
    vm.tabs = {
        viewForm: { active: true}
    };

    // 分页数据
    vm.page = {};
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    // 项分页数据
    vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "report/appraisal/getSupplychainScheduleTracePage";
    // 时间范围的选项
    vm.rangeOptions = {
        //format: "YYYY-MM-DD",
        startDate: new Date((new Date).setMonth(((new Date).getMonth() - 1))),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
    };

    vm.time = {
        start: {},
        end: {}
    };
    vm.initDate = function(date) {
        if(date == "") {
            vm.rangeOptions = {
                // format: "YYYY-MM-DD",
                startDate: new Date(vm.rangeOptions.startDate),
                minDate: new Date(new Date(vm.rangeOptions.minDate).setFullYear(new Date(vm.rangeOptions.minDate).getFullYear() - 5))
            };
            vm.time = {
                start: vm.rangeOptions.startDate,
                end: vm.rangeOptions.minDate
            }
        }
    };

    vm.doPage = function(page, pageSize, total)
    {
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        vm.loadData();
    };

    vm.pageSizeChange = function() {
        vm.loadData();
    };

    vm.supplychainScheduleTraceList = [];
    vm.loadData = function (){
        vm.supplychainScheduleTraceList = [];
        var obj = {};
        if(vm.time.start) {
            obj.sentTimeStartQr = vm.time.start.valueOf();
        }
        if(vm.time.end) {
            obj.sentTimeEndQr = vm.time.end.valueOf();
        }
        if (vm.deptName){
            obj.deptName = vm.deptName;
        }
        if (vm.materialSpecification){
            obj.materialSpecification = vm.materialSpecification;
        }
        obj.pageNo = vm.page.pageNo;
        obj.pageSize = vm.page.pageSize;
        // 查询订单稽核数据
        upida.post(vm.page.url, obj).then(function(data)
        {
            // 计算开始数
            data.startCount = (data.pageNo - 1) * data.pageSize + 1;
            // 计算结束数
            data.endCount = (data.pageNo - 1) * data.pageSize;
            if(data && data.list && data.list.length > 0)
            {
                vm.supplychainScheduleTraceList = data.list;
                // 计算结束数
                data.endCount = data.endCount + data.list.length;
            }
            vm.page.data = data;
        });
    };
    function loadRight() {
        vm.companyId = BaseUtil.getUser().company.recordId;
        MainCtrl.blockUI({
            animate: true
        });
        // 权限
        vm.queryAll = CommonUtil.dataRangeIsAll("10948", BaseUtil.getMenuList());
        vm.user = BaseUtil.getUser();
        upida.get("common/rightall?prefix=count:supplychainScheduleTrace").then(function(data){
            vm.right.view = data.view;
            vm.right.edit = data.edit;
            vm.right.manage = data.manage;
            vm.loadData();
            vm.page.pageNo = 1;

            // 初始化第一页，条件为空
            vm.initDate(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            MainCtrl.unblockUI();
        });
    }

    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
    });
}]);