<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.sys.dao.MessageMoudleDao">
	<select id="getSetMsgList" resultType="MessageMoudle">
	    select * from sm_messageMoudle 
	    where companyId = #{company.recordId} order by msgNode
	</select>
	
	<insert id="saveSetMsgList" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO sm_setMessage(
			companyId,
			messageModuleId,
			createdBy,
			createdDate,
			setFlag,
			editFlag,
			roleId,
			checkFlag
		) VALUES (
			#{company.recordId},
			#{recordId},
			#{createdBy.recordId},
			now(),
			#{setFlag},
			#{editFlag},
			#{roleId},
			#{checkFlag}
		)
	</insert>
	
	<select id="getMsgMoudleByParent" resultType="MessageMoudle">
	select recordId,companyId,msgNode,msgParentNode,msgModuleName from sm_messageMoudle
	    where companyId = #{company.recordId} and msgNode = #{msgNode}
    UNION select recordId,companyId,msgNode,msgParentNode,msgModuleName from sm_messageMoudle 
	    where companyId = #{company.recordId} and msgParentNode = #{msgNode}
	</select>
	
	<select id="getMoudleByNode" resultType="MessageMoudle">
	    select msgModuleName from sm_messageMoudle where companyId = #{company.recordId} and msgNode = #{msgNode}
	</select>
</mapper>