<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.purch.dao.PuPurchasingAttachementsDao">
    
	<sql id="puPurchasingAttachementsColumns">
		a.recordid AS "recordId",
		a.companyid AS "company.recordId",
		a.type AS "type",
		a.purchasingid AS "purchasingId",
		a.orgfilename AS "orgFileName",
		a.realfilename AS "realFileName",
		a.fileurl AS "fileUrl"
	</sql>
    
	<select id="get" resultType="PuPurchasingAttachements">
		SELECT 
			<include refid="puPurchasingAttachementsColumns"/>
		FROM pu_purchasing_attachements a
		WHERE a.recordId = #{recordId}
	</select>
	<!-- 根据申请单ID查询发票信息 -->
	<select id="findPayApplicationInvoices" resultType="PuPurchasingAttachements">
		SELECT 
			<include refid="puPurchasingAttachementsColumns"/>
		FROM pu_purchasing_attachements a
		WHERE a.purchasingId = #{purchasingId}
	</select>
	
	<select id="findList" resultType="PuPurchasingAttachements">
		SELECT 
			<include refid="puPurchasingAttachementsColumns"/>
		FROM pu_purchasing_attachements a
		<where>
			
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="PuPurchasingAttachements">
		SELECT 
			<include refid="puPurchasingAttachementsColumns"/>
		FROM pu_purchasing_attachements a
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<insert id="savePurchasingAttachement">
		INSERT INTO pu_purchasing_attachements(
			companyid,
			type,
			purchasingid,
			orgfilename,
			realfilename,
			fileurl
		) VALUES (
			#{company.recordId},
			#{type},
			#{purchasingId},
			#{orgFileName},
			#{realFileName},
			#{fileUrl}
		)
	</insert>
	
	<update id="update">
		UPDATE pu_purchasing_attachements SET 	
			type = #{type},
			purchasingid = #{purchasingId},
			orgfilename = #{orgFileName},
			realfilename = #{realFileName},
			fileurl = #{fileUrl}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		delete from pu_purchasing_attachements
		WHERE recordId = #{recordId}
	</update>
	
</mapper>