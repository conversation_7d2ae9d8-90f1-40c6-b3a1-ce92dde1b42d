package com.kyb.pcberp.modules.stock.web;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.modules.contract.entity.ContractCraft;
import com.kyb.pcberp.modules.crm.entity.CustomerRemarks;
import com.kyb.pcberp.modules.crm.entity.GoodsCheck;
import com.kyb.pcberp.modules.eg.entity.Parameter;
import com.kyb.pcberp.modules.eg.entity.ProcessDate;
import com.kyb.pcberp.modules.stock.entity.*;
import com.kyb.pcberp.modules.sys.entity.*;
import org.apache.poi.ss.usermodel.Row;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.collect.Maps;
import com.itextpdf.text.DocumentException;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums.DictItemEnum;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.report.utils.FinanceUtils;
import com.kyb.pcberp.modules.stock.service.MaterialService;
import com.kyb.pcberp.modules.stock.service.ProductStoreService;
import com.kyb.pcberp.modules.stock.vo.DepositVo;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

@Controller
@RequestMapping(value = "${adminPath}/stock/product")
public class ProductStockController extends BaseController
{
    
    @Autowired
    private MaterialService materialService;
    
    @Autowired
    private ProductStoreService productStoreService;
    
    @RequestMapping(value = "list")
    public String list(Material material)
    {
        return "modules/stock/productStock";
    }
    
    @RequiresPermissions(value = {"stock:proStock:view"})
    @RequestMapping(value = {"load/data"})
    @ResponseBody
    public Map<String, Object> loadData()
    {
        Map<String, Object> maps = Maps.newHashMap();
        Company company = UserUtils.getUser().getCompany();
        
        // 加载物料编号
        Material material = new Material();
        material.setCompany(company);
        maps.put("materialTypeList", DictUtils.getValuesByItem(DictItemEnum.MATERIAL_TYPE_STORE));
        
        maps.put("materialTypeListTwo", DictUtils.getValuesByItem(DictItemEnum.MATERIAL_TYPE));
        
        // 加载申请人
        maps.put("storehouseList", materialService.selectStoreHouselist(company));
        
        // 权限
        maps.put("view", true);
        maps.put("edit", SecurityUtils.getSubject().isPermitted("stock:proStock:edit"));
        maps.put("manage", SecurityUtils.getSubject().isPermitted("stock:proStock:manage"));
        maps.put("currentTime", new Date());
        
        material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_PRODUCT);
        maps.put("materialList", materialService.getMaterialQueryList(material));
        
        // 加载库位信息
        MaterialPlace materialPlace = new MaterialPlace();
        materialPlace.setCompanyId(company.getRecordId());
        materialPlace.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_PRODUCT.toString());
        maps.put("placeList", materialService.getMaterialPlaceList(materialPlace));

        //获取生产编号
        maps.put("stockNumber", productStoreService.getStockNumber(company.getRecordId()));

        // 加载存放位置A字典值列表
        DictItem qitem = new DictItem();
        qitem.setRecordId("110");
        List<DictValue> valueList = DictUtils.getValuesByItem(qitem, UserUtils.getUser().getCompany());
        maps.put("placeStoragea", valueList);
        // 加载存放位置B字典值列表
        DictItem qitemb = new DictItem();
        qitemb.setRecordId("111");
        List<DictValue> valueListb = DictUtils.getValuesByItem(qitemb, UserUtils.getUser().getCompany());
        maps.put("placeStorageb", valueListb);
        return maps;
    }
    
    /**
     * 成品列表
     * 
     * @param material
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions(value = {"stock:proStock:view"})
    @RequestMapping(value = {"page"})
    @ResponseBody
    public Page<Material> page(@RequestBody Material material, HttpServletRequest request, HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        
        // 设置查询企业编号
        if (material != null)
        {
            material.setCompany(user.getCompany());
        }
        material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_PRODUCT);
        
        // 分页查询数据
        Page<Material> qpage = new Page<Material>(request, response);
        if (StringUtils.isNotBlank(material.getPageNo()) && StringUtils.isNotBlank(material.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(material.getPageNo()));
            qpage.setPageSize(Integer.parseInt(material.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        // 设置排序
        if (StringUtils.isNotBlank(material.getOrderBy()))
        {
            qpage.setOrderBy(material.getOrderBy());
        }
        Page<Material> page = materialService.findProductMPage(qpage, material);
        
        return page;
    }
    
    /**
     * 导出成品列表数据
     * 
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:proStock:view")
    @RequestMapping(value = "export")
    @ResponseBody
    public String export(Material material, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            if (null != material.getQueryAll() && !material.getQueryAll())
            {
                material.setCreatedBy(user);
            }
            material.setCompany(user.getCompany());
            material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_PRODUCT);
            List<Material> exportList = materialService.findProductExportList(material);
            String fileName = "成品数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            new ExportExcel("成品数据", Material.class).setDataList(exportList).write(response, fileName).dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/stock/productStock/list";
    }
    
    /**
     * 成品对应的出入库记录列表
     * 
     * @param proStore
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions(value = {"stock:proStock:view"})
    @RequestMapping(value = {"inout/page"})
    @ResponseBody
    public Page<ProductStore> inoutPage(@RequestBody ProductStore proStore, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        
        // 设置查询企业编号
        if (proStore != null)
        {
            proStore.setCompany(user.getCompany());
        }
        
        if (null != proStore.getQueryAll() && !proStore.getQueryAll())
        {
            proStore.setCreatedBy(user);
        }
        if (proStore.getInoutTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(proStore.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            proStore.setInoutTimeEndQr(deliTime.getTime());
        }
        if (proStore.getInoutTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(proStore.getInoutTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            proStore.setInoutTimeStartQr(deliTime.getTime());
        }
        // 分页查询数据
        Page<ProductStore> qpage = new Page<ProductStore>(request, response);
        if (StringUtils.isNotBlank(proStore.getPageNo()) && StringUtils.isNotBlank(proStore.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(proStore.getPageNo()));
            qpage.setPageSize(Integer.parseInt(proStore.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        // 设置排序
        if (StringUtils.isNotBlank(proStore.getOrderBy()))
        {
            qpage.setOrderBy(proStore.getOrderBy());
        }
        // Page<ProductStore> page = productStoreService.findPage(qpage, proStore);
        // 在原来的findpage上添加了尺寸和面积用于界面显示
        Page<ProductStore> page = productStoreService.findPage_showArea(qpage, proStore);
        
        return page;
    }
    
    /**
     * 导出对应成品的出入库记录
     * 
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:proStock:view")
    @RequestMapping(value = "inout/export")
    @ResponseBody
    public String exportInout(ProductStore proStore, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            String inoutTimeStartQr = request.getParameter("inoutTimeStartQr");
            String inoutTimeEndQr = request.getParameter("inoutTimeEndQr");
            if (StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Date startdate = new Date(Long.parseLong(inoutTimeStartQr));
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(startdate);
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                proStore.setInoutTimeStartQr(deliTime.getTime());
            }
            if (StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Date enddate = new Date(Long.parseLong(inoutTimeEndQr));
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(enddate);
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                proStore.setInoutTimeEndQr(deliTime.getTime());
            }
            
            User user = UserUtils.getUser();
            if (null != proStore.getQueryAll() && !proStore.getQueryAll())
            {
                proStore.setCreatedBy(user);
            }
            proStore.setCompany(user.getCompany());
            String fileName = "成品出入库数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            
            List<ProductStore> list = productStoreService.getExcelList(proStore);
            new ExportExcel("成品出入库数据", ProductStore.class, new Integer(0)).setDataList(list)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/stock/productStock/list";
    }
    
    /**
     * 
     * @param product
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:proStock:edit")
    @RequestMapping(value = "inout/rushred")
    @ResponseBody
    public String rushRed(@RequestBody ProductStore product, HttpServletRequest request, HttpServletResponse response)
    {
        String result = "";
        try
        {
            // zjn 2018-12-18 验证出入库记录对应的对账单明细是否有调整
            String validatorStr = FinanceUtils.prdStockValidator(product);
            if (!"success".equals(validatorStr))
            {
                result = validatorStr;
                return result;
            }
            result = productStoreService.redCut(product);
        }
        catch (Exception e)
        {
            return "成品“" + product.getMaterial().getNo() + "”冲红失败！";
        }
        
        if (StringUtils.isBlank(result))
        {
            return "成品“" + product.getMaterial().getNo() + "”冲红成功！";
        }
        else
        {
            return "成品“" + product.getMaterial().getNo() + "”冲红失败，" + result;
        }
    }
    
    /**
     * WC 2017-03-24 成品寄存记录列表页面
     * 
     * @param proStore
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:proStock:view")
    @RequestMapping(value = "deposit/page")
    @ResponseBody
    public Page<MaterialDepositRecord> depositPage(@RequestBody MaterialDepositRecord mdr, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        
        // 设置查询企业编号
        if (mdr != null)
        {
            mdr.setCompany(user.getCompany());
        }
        
        if (null != mdr.getQueryAll() && !mdr.getQueryAll())
        {
            mdr.setCreatedBy(user);
        }
        
        // 分页查询数据
        Page<MaterialDepositRecord> qpage = new Page<MaterialDepositRecord>(request, response);
        if (StringUtils.isNotBlank(mdr.getPageNo()) && StringUtils.isNotBlank(mdr.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(mdr.getPageNo()));
            qpage.setPageSize(Integer.parseInt(mdr.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        // 设置排序
        if (StringUtils.isNotBlank(mdr.getOrderBy()))
        {
            qpage.setOrderBy(mdr.getOrderBy());
        }
        
        return productStoreService.findDepositPage(qpage, mdr);
    }
    
    /**
     * WC 25017-03-24 成品寄存记录导出
     */
    @RequiresPermissions("stock:proStock:view")
    @RequestMapping(value = "exportDeposit")
    @ResponseBody
    public String exportDeposit(MaterialDepositRecord mdr, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            String inoutTimeStartQr = request.getParameter("inoutTimeStartQr");
            String inoutTimeEndQr = request.getParameter("inoutTimeEndQr");
            if (StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Date startdate = new Date(Long.parseLong(inoutTimeStartQr));
                mdr.setInoutTimeStartQr(startdate);
            }
            if (StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Date enddate = new Date(Long.parseLong(inoutTimeEndQr));
                mdr.setInoutTimeEndQr(enddate);
            }
            User user = UserUtils.getUser();
            if (null != mdr.getQueryAll() && !mdr.getQueryAll())
            {
                mdr.setCreatedBy(user);
            }
            mdr.setCompany(user.getCompany());
            List<DepositVo> exportList = productStoreService.findList(mdr);
            String fileName = "成品寄存数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            new ExportExcel("成品寄存数据", DepositVo.class).setDataList(exportList).write(response, fileName).dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/stock/productStock/list";
    }
    
    /**
     * WC 2017-03-27 寄存解除記錄分頁頁面
     * 
     * @param mdc
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:proStock:view")
    @RequestMapping(value = "depositConv/page")
    @ResponseBody
    public Page<MaterialDepositConvert> depositConvPage(@RequestBody MaterialDepositConvert mdc,
        HttpServletRequest request, HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        
        // 设置查询企业编号
        if (mdc != null)
        {
            mdc.setCompany(user.getCompany());
        }
        
        if (null != mdc.getQueryAll() && !mdc.getQueryAll())
        {
            mdc.setCreatedBy(user);
        }
        
        // 分页查询数据
        Page<MaterialDepositConvert> qpage = new Page<MaterialDepositConvert>(request, response);
        if (StringUtils.isNotBlank(mdc.getPageNo()) && StringUtils.isNotBlank(mdc.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(mdc.getPageNo()));
            qpage.setPageSize(Integer.parseInt(mdc.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        // 设置排序
        if (StringUtils.isNotBlank(mdc.getOrderBy()))
        {
            qpage.setOrderBy(mdc.getOrderBy());
        }
        
        return productStoreService.findDepositConvPage(qpage, mdc);
    }
    
    /**
     * WC 2017-03-27 解除寄存導出
     */
    @RequiresPermissions("stock:proStock:view")
    @RequestMapping(value = "exportDepositConv")
    @ResponseBody
    public String exportDepositConvert(MaterialDepositConvert mdc, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            String startQr = request.getParameter("startQr");
            String endQr = request.getParameter("endQr");
            if (StringUtils.isNotBlank(startQr))
            {
                Date startdate = new Date(Long.parseLong(startQr));
                mdc.setStartQr(startdate);
            }
            if (StringUtils.isNotBlank(endQr))
            {
                Date enddate = new Date(Long.parseLong(endQr));
                mdc.setEndQr(enddate);
            }
            User user = UserUtils.getUser();
            if (null != mdc.getQueryAll() && !mdc.getQueryAll())
            {
                mdc.setCreatedBy(user);
            }
            mdc.setCompany(user.getCompany());
            List<MaterialDepositConvert> exportList = productStoreService.findConvList(mdc);
            String fileName = "解除寄存数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            new ExportExcel("解除寄存数据", MaterialDepositConvert.class).setDataList(exportList)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/stock/productStock/list";
    }
    
    @RequiresPermissions(value = {"stock:proStock:view"})
    @RequestMapping(value = {"inoutPlacePage"})
    @ResponseBody
    public Page<ProductStore> inoutPlacePage(@RequestBody ProductStore productStore, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        if (null != productStore.getQueryAll() && !productStore.getQueryAll())
        {
            productStore.setCreatedBy(user);
        }
        // 设置查询企业编号
        if (productStore != null)
        {
            productStore.setCompany(user.getCompany());
        }
        if (productStore.getInoutTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(productStore.getInoutTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            productStore.setInoutTimeEndQr(deliTime.getTime());
        }
        if (productStore.getInoutTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(productStore.getInoutTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            productStore.setInoutTimeStartQr(deliTime.getTime());
        }
        // 分页查询数据
        Page<ProductStore> qpage = new Page<ProductStore>(request, response);
        if (StringUtils.isNotBlank(productStore.getPageNo()) && StringUtils.isNotBlank(productStore.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(productStore.getPageNo()));
            qpage.setPageSize(Integer.parseInt(productStore.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        
        // 设置排序
        if (StringUtils.isNotBlank(productStore.getOrderBy()))
        {
            qpage.setOrderBy(productStore.getOrderBy());
        }
        Page<ProductStore> page = productStoreService.findPlacePage(qpage, productStore);
        return page;
    }
    
    @RequiresPermissions(value = {"stock:proStock:view"})
    @RequestMapping(value = "exportProductMaterial")
    @ResponseBody
    public String exportProductMaterial(InventoryMantissa mantissa, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            Page<InventoryMantissa> qpage = new Page<InventoryMantissa>();
            mantissa.setCompany(UserUtils.getUser().getCompany());
            String inoutTimeStartQr = request.getParameter("sentTimeStartQr");
            String inoutTimeEndQr = request.getParameter("sentTimeEndQr");
            String fileName = "尾数列表数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<InventoryMantissa> list = productStoreService.getInventoryMantissa(mantissa);

            List<String> headerList = new ArrayList<String>();
            headerList.add("入库日期");
            headerList.add("存放位置A");
            headerList.add("存放位置B");
            headerList.add("生产编号");
            headerList.add("入库数量");
            headerList.add("长");
            headerList.add("宽");
            headerList.add("1S=U");
            headerList.add("单只面积");
            headerList.add("出货数量");
            headerList.add("当前结存");
            headerList.add("结存面积");
            headerList.add("结存天数");
            headerList.add("备注");
            String[] headers = new String[headerList.size()];
            headerList.toArray(headers);
            ExportExcel excel = new ExportExcel("", headers);
            productStoreService.setMantissaDataList(excel, list, headers);
            excel.write(response, fileName).dispose();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }

    @RequiresPermissions(value = {"stock:proStock:view"})
    @RequestMapping(value = "exportMantissaDerivation")
    @ResponseBody
    public String exportMantissaDerivation(InventoryMantissa mantissaA, HttpServletRequest request,
                                        HttpServletResponse response)
    {
        try
        {
            mantissaA.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
            String inoutTimeStartQr = request.getParameter("sentTimeStartQr");
            String inoutTimeEndQr = request.getParameter("sentTimeEndQr");
            String fileName = "尾数出入库数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            Page<InventoryMantissa> page = productStoreService.getstockRemovalMantissa(new Page<InventoryMantissa>(request,response,-1),mantissaA);

            List<String> tabulationList = new ArrayList<String>();
            tabulationList.add("操作时间");
            tabulationList.add("出入库类型");
            tabulationList.add("领用单号");
            tabulationList.add("入库日期");
            tabulationList.add("存放位置A");
            tabulationList.add("存放位置B");
            tabulationList.add("生产编号");
            tabulationList.add("长");
            tabulationList.add("宽");
            tabulationList.add("1S=U");
            tabulationList.add("单只面积");
            tabulationList.add("数量");
            tabulationList.add("面积");
            String[] tabulations = new String[tabulationList.size()];
            tabulationList.toArray(tabulations);
            ExportExcel excel = new ExportExcel("", tabulations);
            productStoreService.setMantissaDerivationList(excel, page.getList(), tabulations);
            excel.write(response, fileName).dispose();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }
    
    public void setDataList(ExportExcel excel, List<ProductStore> list, String[] hearList)
    {
        for (ProductStore one : list)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList)
            {
                String val = "";
                switch (name)
                {
                    case "物料编号":
                        val = one.getMaterial().getNo();
                        break;
                    case "客户型号":
                        val = one.getCustomerModel();
                        break;
                    case "合同编号":
                        val = one.getContractNo();
                        break;
                    case "采购编号":
                        val = one.getPrdorderNo();
                        break;
                    case "送货单号":
                        val = one.getDeliveryShowNo();
                        break;
                    case "出入库类型":
                        val = one.getInoutTypeStr();
                        break;
                    case "入库数量":
                        val = one.getInStocks() == null ? "-" : one.getInStocks().toString();
                        break;
                    case "出库数量":
                        val = one.getOutStocks() == null ? "-" : one.getOutStocks().toString();
                        break;
                    case "结余":
                        val = one.getCurrStocks() == null ? "-" : one.getCurrStocks().toString();
                        break;
                    case "出入库时间":
                        val = one.getOperateDateStr();
                        break;
                    case "销售公司":
                        val = one.getFinalComName();
                        break;
                    case "仓库":
                        val = one.getStoreHouseName();
                        break;
                    case "库位":
                        val = one.getPlaceName();
                        break;
                    case "处理人":
                        val = one.getCreatedBy() == null ? "" : one.getCreatedBy().getUserName();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }
    
    public void setDataTwoList(ExportExcel excel, List<ProductStore> list, String[] hearList)
    {
        for (ProductStore one : list)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList)
            {
                String val = "";
                switch (name)
                {
                    case "物料编号":
                        val = one.getNo();
                        break;
                    case "客户型号":
                        val = one.getCustomerModel();
                        break;
                    case "创建时间":
                        val = one.getCreatedDateStr();
                        break;
                    case "交货尺寸":
                        val = one.getSize();
                        break;
                    case "期初":
                        val = one.getStartStocks().toString();
                        break;
                    case "累积入库":
                        val = one.getInStocks().toString();
                        break;
                    case "累积出库":
                        val = one.getOutStocks().toString();
                        break;
                    case "结余":
                        val = one.getQuantity().toString();
                        break;
                    case "临时仓库存":
                        val = one.getContractPlaceStocks()==null?"0":one.getContractPlaceStocks().toString();
                        break;
                    case "尾数":
                        val = one.getLastPlaceStocks()==null?"0":one.getLastPlaceStocks().toString();
                        break;
                    case "仓库":
                        val = one.getName();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }
    
    @RequiresPermissions(value = {"stock:proStock:view"})
    @RequestMapping(value = "exportInoutPlace")
    @ResponseBody
    public String exportInoutPlace(ProductStore productStore, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            String inoutTimeStartQr = request.getParameter("inoutTimeStartQr");
            String inoutTimeEndQr = request.getParameter("inoutTimeEndQr");
            if (StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Date startdate = new Date(Long.parseLong(inoutTimeStartQr));
                productStore.setInoutTimeStartQr(startdate);
            }
            if (StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Date enddate = new Date(Long.parseLong(inoutTimeEndQr));
                productStore.setInoutTimeEndQr(enddate);
            }
            User user = UserUtils.getUser();
            productStore.setCompany(user.getCompany());
            String fileName = "物料台账数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<ProductStockPlace> placeList = productStoreService.findPlacePageExport(productStore);
            new ExportExcel("成品出入库数据", ProductStockPlace.class, new Integer(0)).setDataList(placeList, 2)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/stock/product/list";
    }
    
    @RequestMapping(value = "batchPrintPdf", method = RequestMethod.POST, produces = "application/pdf")
    @ResponseBody
    public ResponseEntity<byte[]> batchPrintPdf(@RequestBody ProductStore productStore, HttpServletRequest request)
        throws DocumentException, IOException
    {
        Pdf pdf = productStoreService.batchPrintPdf(productStore, request);
        byte[] contents = pdf.getOutPut().toByteArray();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/pdf"));
        String filename = CommonUtils.getDisplayPDFName(pdf.getFileName());
        headers.add("X-File-Name", filename);
        headers.setContentDispositionFormData(filename, filename);
        ResponseEntity<byte[]> response = new ResponseEntity<byte[]>(contents, headers, HttpStatus.OK);
        return response;
    }
    
    @RequiresPermissions("stock:proStock:edit")
    @RequestMapping(value = "updateInStoreTime")
    @ResponseBody
    public Map<String, Object> updateInStoreTime(@RequestBody ProductStore productStore)
    {
        return productStoreService.updateInStoreTime(productStore);
    }
    
    @RequiresPermissions("stock:proStock:edit")
    @RequestMapping(value = "getSentDeliveryQty")
    @ResponseBody
    public Integer getSentDeliveryQty(@RequestBody ProductStore productStore)
    {
        return productStoreService.getSentDeliveryQty(productStore);
    }

    @RequiresPermissions("stock:proStock:edit")
    @RequestMapping(value = "sentDelivery")
    @ResponseBody
    public String sentDelivery(@RequestBody ProductStore productStore)
    {
        return productStoreService.sentDelivery(productStore);
    }

    @RequiresPermissions("stock:proStock:edit")
    @RequestMapping(value = "saveMantissa")
    @ResponseBody
    public Map<String,Object> saveMantissa(@RequestBody ProductStore productStore)
    {
        return productStoreService.saveMantissa(productStore);
    }

    @RequestMapping(value = "inoutMantissa", method = RequestMethod.POST)
    @ResponseBody
    public List<InventoryMantissa> inoutMantissa(@RequestBody InventoryMantissa inventoryMantissa)
    {
        inventoryMantissa.setCompany(UserUtils.getUser().getCompany());
        return productStoreService.getInventoryMantissa(inventoryMantissa);

    }

/*    @RequestMapping(value = "stockRemoval", method = RequestMethod.POST)
    @ResponseBody
    public List<InventoryMantissa> stockRemoval(@RequestBody InventoryMantissa inventoryMantissa)
    {
        return productStoreService.getstockRemovalMantissa(inventoryMantissa);
    }*/

    @RequestMapping(value = {"stockRemoval"})
    @ResponseBody
    public Page<InventoryMantissa> stockRemoval(@RequestBody InventoryMantissa inventoryMantissa, HttpServletRequest request, HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        inventoryMantissa.setCompanyId(user.getCompany().getRecordId());
        Page<InventoryMantissa> qpage = new Page<>(request, response);
        if(StringUtils.isNotBlank(inventoryMantissa.getPageNo()) && StringUtils.isNotBlank(inventoryMantissa.getPageSize()))
        {
            qpage.setPageNo(Integer.parseInt(inventoryMantissa.getPageNo()));
            qpage.setPageSize(Integer.parseInt(inventoryMantissa.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        Page<InventoryMantissa> page = productStoreService.getstockRemovalMantissa(qpage, inventoryMantissa);
        return page;
    }

    @RequestMapping(value = {"addMantissa"})
    @ResponseBody
    public String addMantissa(@RequestBody List<InventoryMantissa>  inventoryMantissa) {
        return productStoreService.addMantissa(inventoryMantissa);
    }

    @RequestMapping(value = {"getMantissaMaterialUseData"})
    @ResponseBody
    public List<MaterialUse> getMantissaMaterialUseData() {
        return productStoreService.getMantissaMaterialUseData();
    }

    @RequestMapping(value = {"outStore"})
    @ResponseBody
    public Map<String,Object> outStore(@RequestBody List<MantissaStore> list) {
        return productStoreService.outStore(list);
    }

    @RequestMapping(value = {"updateyMantissaRemark"})
    @ResponseBody
    public void updateyMantissaRemark(@RequestBody InventoryMantissa inventoryMantissa) {
        productStoreService.updateyMantissaRemark(inventoryMantissa);
    }

    @RequestMapping(value = "gettMantissaData", method = RequestMethod.POST)
    @ResponseBody
    public List<InventoryMantissa> gettMantissaData(@RequestBody MaterialUse materialUse)
    {
        return productStoreService.gettMantissaData(materialUse);
    }
}
