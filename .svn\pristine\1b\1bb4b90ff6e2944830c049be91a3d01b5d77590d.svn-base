package com.kyb.pcberp.modules.sys.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;

/**
 * 职位对象
 */
@SuppressWarnings("serial")
public class EmployeePosition extends DataEntity<EmployeePosition>
{
    
    private String name;
    
    private String rolestatus; // 审批角色状态
    
    private String status;// 1公司 2部门 3组织 4WIP 5 其他职位 1-4 报表权限
    
    private String framework; // 组织结构

    private String groupCenterType;

    private Integer seqNum;

    private String allocationId;

    private String allocationRoleId;

    private String concatName;
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public String getName()
    {
        return name;
    }
    
    public void setName(String name)
    {
        this.name = name;
    }
    
    public String getRolestatus()
    {
        return rolestatus;
    }
    
    public void setRolestatus(String rolestatus)
    {
        this.rolestatus = rolestatus;
    }

    public String getFramework()
    {
        return framework;
    }

    public void setFramework(String framework)
    {
        this.framework = framework;
    }

    public String getCreatedDateStr()
    {
        if(null != createdDate)
        {
            return DateUtils.formatDate(createdDate,"yyyy-MM-dd HH:mm:ss");
        }
        return null;
    }

    public String getGroupCenterType()
    {
        return groupCenterType;
    }

    public void setGroupCenterType(String groupCenterType)
    {
        this.groupCenterType = groupCenterType;
    }

    public Integer getSeqNum()
    {
        return seqNum;
    }

    public void setSeqNum(Integer seqNum)
    {
        this.seqNum = seqNum;
    }

    public String getAllocationId()
    {
        return allocationId;
    }

    public void setAllocationId(String allocationId)
    {
        this.allocationId = allocationId;
    }

    public String getAllocationRoleId()
    {
        return allocationRoleId;
    }

    public void setAllocationRoleId(String allocationRoleId)
    {
        this.allocationRoleId = allocationRoleId;
    }

    public String getConcatName()
    {
        return concatName;
    }

    public void setConcatName(String concatName)
    {
        this.concatName = concatName;
    }
}
