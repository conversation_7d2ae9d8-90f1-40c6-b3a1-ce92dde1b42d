package com.kyb.pcberp.modules.stock.entity;

import java.util.Date;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.crm.entity.Customer;

/**
 * WC 2017-03-23 物料寄存记录实体类
 */
public class MaterialDepositRecord extends DataEntity<MaterialDepositRecord>
{
    private static final long serialVersionUID = 1L;
    
    private ContractDetail contractDetail;
    
    private Customer customer;
    
    private Material material;
    
    private Integer quantity; // 寄存数量
    
    private String status;
    
    private Integer type;

    private Date inoutTimeStartQr; // 寄存时间段查询：开始时间
    
    private Date inoutTimeEndQr; // 寄存时间段查询：结束时间
    
    public ContractDetail getContractDetail()
    {
        return contractDetail;
    }

    public void setContractDetail(ContractDetail contractDetail)
    {
        this.contractDetail = contractDetail;
    }

    public Customer getCustomer()
    {
        return customer;
    }

    public void setCustomer(Customer customer)
    {
        this.customer = customer;
    }

    public Material getMaterial()
    {
        return material;
    }

    public void setMaterial(Material material)
    {
        this.material = material;
    }

    public Integer getQuantity()
    {
        return quantity;
    }

    public void setQuantity(Integer quantity)
    {
        this.quantity = quantity;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public Integer getType()
    {
        return type;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    public Date getInoutTimeStartQr()
    {
        return inoutTimeStartQr;
    }

    public void setInoutTimeStartQr(Date inoutTimeStartQr)
    {
        this.inoutTimeStartQr = inoutTimeStartQr;
    }

    public Date getInoutTimeEndQr()
    {
        return inoutTimeEndQr;
    }

    public void setInoutTimeEndQr(Date inoutTimeEndQr)
    {
        this.inoutTimeEndQr = inoutTimeEndQr;
    }

}
