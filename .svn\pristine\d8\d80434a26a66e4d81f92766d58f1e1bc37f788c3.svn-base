<%@ page contentType="text/html;charset=UTF-8"%>
<div ng-intro-options="listOptions" ng-intro-method="helpList" ng-intro-autostart="shouldAutoStart"></div>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li><i class="fa fa-home"></i> <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i></li>
        <li><a href="javascript:;">订单管理</a> <i class="fa fa-angle-right"></i>
        </li>
        <li><a ui-sref="order.sales">销售计划</a></li>
    </ul>

    <div class="page-toolbar">
        <button class="btn btn-fit-height default pull-right" ng-click="helpList()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
    </div>
</div>

<tabset class="tabset-margin-top">
    <tab heading="销售计划列表" active="salesCtrl.tabs.listForm.active">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">销售计划列表</div>
                <div id="listStep6" class="actions">
	                  <div class="portlet-input input-inline input-small" ng-if="salesCtrl.right.edit">
	                      <button type="button" class="btn green btn-default-width" ng-click="salesCtrl.addSalesPlan()"><i class="fa fa-plus"></i> 添加计划</button>
	                  </div>
	                  <div class="portlet-input input-inline input-small">
						 <form action="a/order/sales/export" method="POST" enctype="multipart/form-data" target="hidden_frame">
							 <div >
								<button  title="导出销售计划捷按钮" type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出计划</button>
								<iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
							 </div>
						  </form>
	                  </div>
                </div>
            </div>
            <div class="portlet-body" id="listStep1">
                <div class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                        <thead>
                            <tr class="heading">
                                <th>年份</th>
                                <th>接单平米/㎡</th>
                                <th>接单金额/￥</th>
                                <th>操作人</th>
                                <th>备注</th>
                                <th ng-if="salesCtrl.right.edit" id="listStep2">操作</th>
                            </tr>
                        </thead>
                        <tbody>
	                        <tr ng-repeat="row in salesCtrl.page.data.list" ng-dblclick="salesCtrl.showDetail($index)">
	                            <td><a ng-bind="row.planYear" ng-click="salesCtrl.showDetail($index)"></a></td>
	                            <td ng-bind="row.planAreaYear"></td>
	                            <td ng-bind="row.planMoneyYear"></td>
	                            <td ng-bind="row.lastUpdBy.userName"></td>
	                            <td ng-bind="row.remark"></td>
	                            <td ng-if="salesCtrl.right.edit">
							        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="row.planYear >salesCtrl.year"
							        	ng-click="salesCtrl.deleteSalesPlan($index)"><i class="fa fa-times font-red"></i> 删除</a>
	                            </td>
	                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row page-margin-top">
                    <div id="listStep4" class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select class="form-control inline"
                                style="margin-top: 8px; width: 100px;"
                                disable-valid-styling="true" disable-invalid-styling="true"
                                ng-model="salesCtrl.page.pageSize"
                                ng-change="salesCtrl.pageSizeChange()"
                                ng-options="option for option in salesCtrl.page.pageSizeOptions">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示{{salesCtrl.page.data.startCount}} /{{salesCtrl.page.data.endCount}} 条，共{{salesCtrl.page.data.count}} 条</span>
                    </div>
                    <div id="listStep3" class="col-md-12 col-lg-6">
                        <paging class="pull-right" page="salesCtrll.page.data.pageNo"
                                page-size="salesCtrl.page.data.pageSize"
                                total="salesCtrl.page.data.count" adjacent="1" dots="..."
                                scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                active-class="active" disabled-class="disabled"
                                show-prev-next="true"
                                paging-action="salesCtrl.doPage(page, pageSize, total)">
                        </paging>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    
    <!-- 添加销售计划 -->
    <tab active="salesCtrl.tabs.editForm.active" ng-show ="salesCtrl.tabs.editForm.show">
      	 <tab-heading>
                        销售计划详情 <i class="fa fa-times set-cursor-pointer" ng-click="salesCtrl.hideEditDetail(false)"></i>
        </tab-heading>
         <div class="form-horizontal" >
	         <div class="row">
	         	<div class="col-md-10 col-lg-10">
                    <div class="form-group">
                        <label class="col-sm-3 col-md-2 control-label">计划年份： </label>
                         <div class="col-sm-7 col-md-7">
                             <input class="form-control" type ="text" 
	                             ng-model="salesCtrl.item.planYear" 
	                             placeholder="例如：2017" 
	                             ng-blur= "salesCtrl.checkPlanYear(salesCtrl.item.planYear)"
	                             ng-disabled="salesCtrl.tabs.addPlan.year || !salesCtrl.right.edit">
                         </div>
                     </div>
                </div>
	         </div>
	         
	         <table class="table table-striped table-bordered table-condensed table-advance  table table-inverse">
                   <thead>
                       <tr class="heading">
                           <th></th>
                           <th>目标面积(㎡)</th>
                           <th>目标营收(￥)</th>
                       </tr>
                   </thead>
                   <tbody>
                      <tr>
	                      <td>年目标</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaYear" ng-disabled="salesCtrl.tabs.addPlan.areaYear || !salesCtrl.right.edit" ng-blur = "salesCtrl.countEveryMothArea()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneyYear" ng-disabled="salesCtrl.tabs.addPlan.moneyYear || !salesCtrl.right.edit" ng-blur = "salesCtrl.countEveryMothMoney()">
	                      </td>
	                   </tr>
                       <tr>
	                      <td>一月</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaJan" ng-disabled="salesCtrl.isUse.use1 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanAreaYear()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneyJan"  ng-disabled="salesCtrl.isUse.use1 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanMoneyYear()" >
	                      </td>
	                   </tr>
                      <tr>
	                      <td>二月</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaFeb"  ng-disabled="salesCtrl.isUse.use2 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanAreaYear()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneyFeb"  ng-disabled="salesCtrl.isUse.use2 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanMoneyYear()" >
	                      </td>
	                   </tr>
	                   <tr>
	                      <td>三月</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaMar"  ng-disabled="salesCtrl.isUse.use3 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanAreaYear()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneyMar"  ng-disabled="salesCtrl.isUse.use3 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanMoneyYear()" >
	                      </td>
	                   </tr>
	                   <tr>
	                      <td>四月</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaApr" ng-disabled="salesCtrl.isUse.use4 || !salesCtrl.right.edit"  ng-blur = "salesCtrl.initPlanAreaYear()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneyApr" ng-disabled="salesCtrl.isUse.use4 || !salesCtrl.right.edit"  ng-blur = "salesCtrl.initPlanMoneyYear()" >
	                      </td>
	                   </tr>
	                   <tr>
	                      <td>五月</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaMay"  ng-disabled="salesCtrl.isUse.use5 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanAreaYear()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneyMay"  ng-disabled="salesCtrl.isUse.use5 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanMoneyYear()" >
	                      </td>
	                   </tr>
	                   <tr>
	                      <td>六月</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaJun"  ng-disabled="salesCtrl.isUse.use6 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanAreaYear()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneyJun"  ng-disabled="salesCtrl.isUse.use6 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanMoneyYear()" >
	                      </td>
	                   </tr>
	                   <tr>
	                      <td>七月</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaJul"  ng-disabled="salesCtrl.isUse.use7 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanAreaYear()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneyJul"  ng-disabled="salesCtrl.isUse.use7 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanMoneyYear()" >
	                      </td>
	                   </tr>
	                   <tr>
	                      <td>八月</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaAug" ng-disabled="salesCtrl.isUse.use8 || !salesCtrl.right.edit"  ng-blur = "salesCtrl.initPlanAreaYear()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneyAug" ng-disabled="salesCtrl.isUse.use8 || !salesCtrl.right.edit"  ng-blur = "salesCtrl.initPlanMoneyYear()" >
	                      </td>
	                   </tr>
	                   <tr>
	                      <td>九月</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaSep"  ng-disabled="salesCtrl.isUse.use9 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanAreaYear()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneySep"  ng-disabled="salesCtrl.isUse.use9 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanMoneyYear()">
	                      </td>
	                   </tr>
	                   <tr>
	                      <td>十月</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaOct"  ng-disabled="salesCtrl.isUse.use10 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanAreaYear()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneyOct"   ng-disabled="salesCtrl.isUse.use10 || !salesCtrl.right.edit" ng-blur = "salesCtrl.initPlanMoneyYear()" >
	                      </td>
	                   </tr>
	                   <tr>
	                      <td>十一月</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaNov" ng-disabled="salesCtrl.isUse.use11 || !salesCtrl.right.edit"  ng-blur = "salesCtrl.initPlanAreaYear()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneyNov" ng-disabled="salesCtrl.isUse.use11 || !salesCtrl.right.edit"  ng-blur = "salesCtrl.initPlanMoneyYear()">
	                      </td>
	                   </tr>
	                   <tr>
	                      <td>十二月</td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planAreaDec" ng-disabled="salesCtrl.isUse.use12 || !salesCtrl.right.edit"  ng-blur = "salesCtrl.initPlanAreaYear()">
	                      </td>
	                      <td>
	                         <input type="number" ng-model="salesCtrl.item.planMoneyDec" ng-disabled="salesCtrl.isUse.use12 || !salesCtrl.right.edit"  ng-blur = "salesCtrl.initPlanMoneyYear()" >
	                      </td>
	                   </tr>
	              </tbody>
	          </table>
	          
	          <div class="row">
	         	<div class="col-md-10 col-lg-10">
                    <div class="form-group">
                        <label class="col-sm-3 col-md-2 control-label">备注： </label>
                         <div class="col-sm-7 col-md-7">
                             <textarea class="form-control"  name = "remark" placeholder="最多255个字符" ng-disabled = "!salesCtrl.right.edit || salesCtrl.tabs.addPlan.remark" 
                             ng-model="salesCtrl.item.remark" maxlength="255"/>
                             <span class="text-danger" ng-if="salesCtrl.item.remark.length==255">* 最多255个字符</span>
                         </div>
                     </div>
                </div>
	         </div>
	         
	         <div class="form-actions fluid" ng-if="salesCtrl.right.edit">
				<div class="row">
					<div class="col-md-offset-3 col-md-9">
						<button type="button" class="btn default btn-default-width" ng-click="salesCtrl.resetSalesPlan()" ng-show="salesCtrl.isAdd"><i class="fa fa-mail-reply"></i> 重&nbsp;置</button>
						<button type="button" class="btn green btn-default-width" ng-click="salesCtrl.submitMod(editForm)" ng-show="salesCtrl.isSave"><i class="fa fa-plus"></i> 保&nbsp;存</button>
					</div>
				</div>
			</div>
         </div>
    </tab>
  </tabset>
 
 
<div class="row">
    <div class="col-md-12">
        <div id="staticRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">删除年度计划</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="salesCtrl.delMsg"></p></div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="salesCtrl.doDelSalesPlan()">删除</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

 <div class="row">
    <div class="col-md-12">
        <div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">提示</h4>
                    </div>
                    <div class="modal-body">
                        <p><span ng-bind="salesCtrl.message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>