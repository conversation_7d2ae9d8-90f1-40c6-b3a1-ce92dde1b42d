package com.kyb.pcberp.common.utils;

import com.kyb.pcberp.modules.contract.entity.RadioEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.HttpClientUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * 爬取中国银行汇率
 */
public class CrawlingExchangeRateUtil
{
    public static CrawlingExchangeRateUtil getInstance()
    {
        return new CrawlingExchangeRateUtil();
    }

    // 调用方法
    public List<RadioEntity> execute(String sourceCurrency)
    {
        @SuppressWarnings("unchecked") List<RadioEntity> queryList =
            (List<RadioEntity>)getExchangeRate(sourceCurrency, "");
        //        List queryList = getExchangeRate("USD", "");
        //        System.out.println("长度:" + queryList.size());
        //        System.out.println("汇总:" + queryList);
        return queryList;
    }

       /* public static void main(String[] args)
        {
            CrawlingExchangeRateUtil crawlingExchangeRateUtil = new CrawlingExchangeRateUtil();
            crawlingExchangeRateUtil.execute("USD").get(0);
        }*/

    public RadioEntity executeNew(String sourceCurrency, Date date)
    {
        List<RadioEntity> queryList =
            (List<RadioEntity>)getExchangeRateOnePage(sourceCurrency, date == null ? "" : DateUtils.getDateDays(date));
        if (!CollectionUtils.isEmpty(queryList))
        {
            for(RadioEntity obj : queryList)
            {
                if(obj.getCurrencyName().equals("美元") && sourceCurrency.equals("USD"))
                {
                    return obj;
                }
                if(obj.getCurrencyName().equals("港币") && sourceCurrency.equals("HKD"))
                {
                    return obj;
                }
            }
        }
        return null;
    }

    public BigDecimal getMiddleRadio(String sourceCurrency, Date date)
    {
        RadioEntity radio = executeNew(sourceCurrency, date);
        if (null != radio)
        {
            return new BigDecimal(radio.getMiddleRate()).divide(new BigDecimal("100"), 4, BigDecimal.ROUND_HALF_UP);
        }
        return null;
    }

    /**
     * 获取当日传入币别汇率信息
     *
     * @param sourceCurrency 币别
     * @param date           日期
     * @return
     */
    public List getExchangeRate(String sourceCurrency, String date)
    {

        /***判断入参lsDate是否为空,若为空则赋值为当前时间**/
        String lsToday = StringUtils.isEmpty(date) ? DateUtils.getDate() : date;
        List list = new ArrayList();
        for (int page = 1; page <= 10; page++)
        {
            /**抓取时间为lsToday,币别为sourceCurrency,页数为page的中国银行网页信息*/
            String searchEnHtml = getSearchEnHtml(lsToday, sourceCurrency, String.valueOf(page));

            /**开始解析html中的汇率列表信息**/
            Map map = assembleObjByHtml(searchEnHtml, sourceCurrency, lsToday);
            String flag = (String)map.get("flag");
            String htmlPage = (String)map.get("page");
            list.add(map.get("list"));

            /**当flag为1执行成功时,或总页数等于循环查询到的页数时,则不需要再次进行查询**/
            if ("1".equals(flag) || Integer.parseInt(htmlPage) < page)
            {
                break;
            }
        }
        return list;
    }

    /**
     * 获取当日传入币别汇率信息 只查询第一页
     *
     * @param sourceCurrency 币别
     * @param date           日期
     * @return
     */
    public List<RadioEntity> getExchangeRateOnePage(String sourceCurrency, String date)
    {

        /***判断入参lsDate是否为空,若为空则赋值为当前时间**/
        String lsToday = StringUtils.isEmpty(date) ? DateUtils.getDate() : date;
        String searchEnHtml = getSearchEnHtml(lsToday, sourceCurrency, String.valueOf(1));
        /**开始解析html中的汇率列表信息**/
        Map map = assembleObjByHtml(searchEnHtml, sourceCurrency, lsToday);
        return (List<RadioEntity>)map.get("list");
    }

    /**
     * 获取整个网页的内容
     *
     * @param lsToday          传入当前时间或空
     * @param lsSourceCurrency 币种
     * @param liPage           当前查询页数
     * @return
     */
    private String getSearchEnHtml(String lsToday, String lsSourceCurrency, String liPage)
    {

        StringBuilder url = new StringBuilder("https://www.bankofchina.com/sourcedb/whpj");
/*        url.append("erectDate=").append(lsToday);
        url.append("&nothing=").append(lsToday);
        url.append("&pjname=").append(lsSourceCurrency);
        url.append("&page=").append(liPage);*/
        System.out.println("拼接好的url:" + url);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        HttpGet httpGet = new HttpGet(url.toString());
        httpGet.addHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
        httpGet.setHeader("Accept", "Accept: text/plain, */*");
        httpGet.addHeader("User-Agent",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3724.8 Safari/537.36");
        httpGet.addHeader("x-amazon-user-agent", "AmazonJavascriptScratchpad/1.0 (Language=Javascript)");
        httpGet.addHeader("X-Requested-With", "XMLHttpRequest");
        String html = "";
        try
        {
            response = httpClient.execute(httpGet);

            /**判断响应状态为200，进行处理**/
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK)
            {
                HttpEntity httpEntity = response.getEntity();
                html = EntityUtils.toString(httpEntity, "utf-8");
            }
            else
            {
                System.out.println(EntityUtils.toString(response.getEntity(), "utf-8"));
            }
        }
        catch (ClientProtocolException e)
        {
            e.printStackTrace();
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }
        finally
        {
            HttpClientUtils.closeQuietly(response);
            HttpClientUtils.closeQuietly(httpClient);
        }

        /***返回请求得到的页面**/
        return html;
    }

    /**
     * 根据取得的网页,解析html中的内容 先不做业务逻辑,全部查询
     *
     * @param html             要解析的html
     * @param lsSourceCurrency 币种
     * @param lsToday          日期
     * @return
     */
    private Map assembleObjByHtml(String html, String lsSourceCurrency, String lsToday)
    {
        /**存储数据**/
        Map map = new HashMap(5);

        /**使用Jsoup将html解析为Document对象**/
        Document document = Jsoup.parse(html);

        /**获取页面隐藏域中存放的当前页数**/
        Elements pageItem = document.getElementsByAttributeValue("name", "page");
        String pageItemValue = "";
        pageItemValue = pageItem.select("input[name=page]").val();
        map.put("page", pageItemValue);

        /**获取页面的整个table信息,这个返回的页面基本上是返回多个table,下方需要细化处理**/
        Elements tables = document.getElementsByTag("table");

        /**设置存放汇率信息的table下标为-1(默认不存在)**/
        int tableIndex = -1;

        /**从table中循环获取,查找含有Currency Name字段的table**/
        for (int i = 0; i < tables.size(); i++)
        {
            Element element = tables.get(i);
            String text = element.text();
            /**找到含有汇率信息的table,给tableIndex赋值,跳出循环**/
            if (text.indexOf("货币名称") > -1)
            {
                tableIndex = i;
                break;
            }
        }
        List<RadioEntity> list = new ArrayList();
        /**如果找到汇率列表信息**/
        if (tableIndex > -1)
        {
            Element table = tables.get(tableIndex);
            /**遍历该表格内的所有的<tr> <tr/>*/
            Elements trs = table.select("tr");
            for (int i = 1; i < trs.size(); ++i)
            {
                RadioEntity radioEntity = new RadioEntity();
                Element tr = trs.get(i);
                /**将数据放入实体对象中*/
                Elements tds = tr.select("td");
                radioEntity.setCurrencyName(tds.get(0).text());
                radioEntity.setBuyingRate(tds.get(1).text());
                radioEntity.setCashBuyingRate(tds.get(2).text());
                radioEntity.setSellingRate(tds.get(3).text());
                radioEntity.setCashSellingRate(tds.get(4).text());
                radioEntity.setMiddleRate(tds.get(5).text());
                radioEntity.setPubTime(tds.get(6).text());
                list.add(radioEntity);
            }
            map.put("list", list);
        }
        else
        {
            map.put("flag", "1");
        }
        return map;
    }

}
