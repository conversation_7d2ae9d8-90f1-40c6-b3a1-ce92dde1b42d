package com.kyb.pcberp.modules.hr.company_center.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.modules.hr.company_center.dao.Hr_RegimenDao;
import com.kyb.pcberp.modules.hr.company_center.pojo.Hr_Regimen;
import com.kyb.pcberp.modules.hr.company_center.pojo.Hr_RegimenPublish;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_KybAttachments;
import com.kyb.pcberp.modules.sys.entity.User;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Service
public class Hr_RegimenService
{
    @Autowired
    private Hr_RegimenDao hr_regimenDao;

    public Page<Hr_Regimen> getRegimenManagementList(Hr_Regimen regimen, HttpServletRequest request,
                                                     HttpServletResponse response, Integer type)
    {
        Page<Hr_Regimen> page = new Page<Hr_Regimen>(request, response,type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if(StringUtils.isNotBlank(regimen.getPageNo()))
        {
            pageNo = Integer.valueOf(regimen.getPageNo());
        }
        if(StringUtils.isNotBlank(regimen.getPageSize()))
        {
            pageSize = Integer.valueOf(regimen.getPageSize());
        }
        if (type != -1){
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            regimen.setPage(page);
        }
        List<Hr_Regimen> list = hr_regimenDao.getRegimenManagementList(regimen);

        // 获取文件
        if(Collections3.isNotEmpty(list))
        {
            String recordIds = null;
            for(Hr_Regimen rm : list)
            {
                if(StringUtils.isNotBlank(recordIds))
                {
                    recordIds = recordIds +","+ rm.getRecordId();
                }
                else
                {
                    recordIds = rm.getRecordId();
                }
            }
            Hr_Regimen query = new Hr_Regimen();
            query.setRecordId(recordIds);
            List<Hr_KybAttachments> attachmentList = hr_regimenDao.getAttachmentList(query);
            if(Collections3.isNotEmpty(attachmentList))
            {
                for(Hr_Regimen rm : list)
                {
                    rm.setAttachmentList(Lists.newArrayList());
                    for(Hr_KybAttachments atta : attachmentList)
                    {
                        if(atta.getRegimenId().equals(rm.getRecordId()))
                        {
                            rm.getAttachmentList().add(atta);
                        }
                    }
                }
            }
        }
        page.setList(list);
        return page;
    }
    public void setDataListRegimen(ExportExcel excel, List<Hr_Regimen> list, String[] hearList) {
        for (Hr_Regimen report : list) {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "制度名称":
                        val = report.getRegimenName();
                        break;
                    case "发布日期":
                        val = String.valueOf(report.getRegimenTimeStr());
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    @Transactional(readOnly = false)
    public String getInsertRegimen(MultipartFile[] files, String regimen)
    {
        Hr_Regimen rm = JSON.parseObject(regimen, Hr_Regimen.class);

        // 验证制度名称
        Integer count = hr_regimenDao.checkName(rm);
        if(null != count && count > 0)
        {
            return "failName";
        }
        if (StringUtils.isBlank(rm.getRecordId()))
        {
            rm.preInsert();
            hr_regimenDao.getInsertRegimen(rm);
        }
        else
        {
            rm.preUpdate();
            hr_regimenDao.getUpdateRegimen(rm);
        }
        if (files.length > 0)
        {
            String result = uploadFile(files, rm.getRecordId(),rm.getCreatedBy());
            if (StringUtils.isNotBlank(result))
            {
                return result;
            }
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String delRegimen(Hr_Regimen regimenManagement){
        hr_regimenDao.delRegimen(regimenManagement);
        List<Hr_KybAttachments> list = hr_regimenDao.getAttachmentList(regimenManagement);
        if(Collections3.isNotEmpty(list))
        {
            for(Hr_KybAttachments atta : list)
            {
                delAttachment(atta);
            }
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String delAttachment(Hr_KybAttachments attach)
    {
        FileManageUtils.delFiles(attach.getFileUrl());
        hr_regimenDao.deleteRegimenAttachment(attach);
        return "success";
    }

    @Transactional(readOnly = false)
    public String uploadFile(MultipartFile[] files, String id,User user)
    {
        // 每个任务一个对应地址
        String savePath = "hr" + "/regimen/" + id + "/";
        String result = FileManageUtils.uploadFile(files, savePath);
        if (result == null)
        {
            return "false";
        }
        // 上传文件
        for (int i = 0; i < files.length; i++)
        {
            // 获取是否上传了
            Integer count = hr_regimenDao.getRegimenAttachmentCount(id,files[i].getOriginalFilename());
            if(null != count && count > 0)
            {
                continue;
            }

            String sourcePath = savePath + files[i].getOriginalFilename();
            Hr_KybAttachments regimenFile = new Hr_KybAttachments();
            regimenFile.setRegimenId(id);
            regimenFile.setFileUrl(sourcePath);
            regimenFile.setOrgFileName(files[i].getOriginalFilename());
            regimenFile.setRealFileName(files[i].getName());
            regimenFile.setType(files[i].getContentType());
            regimenFile.setCreatedBy(user);
            hr_regimenDao.saveRegimenAttachment(regimenFile);
        }
        return null;
    }

    public List<Hr_Regimen> getRegimenList(Hr_Regimen hr_regimen)
    {
        return hr_regimenDao.getRegimenList(hr_regimen);
    }

    public Page<Hr_RegimenPublish> getRegimenManagementMiddleList(Hr_RegimenPublish hr_regimenPublish, HttpServletRequest request,
                                                                  HttpServletResponse response, Integer type)
    {
        Page<Hr_RegimenPublish> page = new Page<>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(hr_regimenPublish.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_regimenPublish.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_regimenPublish.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_regimenPublish.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_regimenPublish.setPage(page);
        }
        List<Hr_RegimenPublish> list = hr_regimenDao.getRegimenManagementMiddleList(hr_regimenPublish);
        // 获取文件
        if(Collections3.isNotEmpty(list))
        {
            String recordIds = null;
            for(Hr_RegimenPublish rm : list)
            {
                if(StringUtils.isNotBlank(recordIds))
                {
                    recordIds = recordIds +","+ rm.getRegimenId();
                }
                else
                {
                    recordIds = rm.getRegimenId();
                }
            }
            Hr_Regimen query = new Hr_Regimen();
            query.setRecordId(recordIds);
            List<Hr_KybAttachments> attachmentList = hr_regimenDao.getAttachmentList(query);
            if(Collections3.isNotEmpty(attachmentList))
            {
                for(Hr_RegimenPublish rm : list)
                {
                    rm.setAttachmentList(Lists.newArrayList());
                    for(Hr_KybAttachments atta : attachmentList)
                    {
                        if(atta.getRegimenId().equals(rm.getRegimenId()))
                        {
                            rm.getAttachmentList().add(atta);
                        }
                    }
                }
            }
        }
        page.setList(list);
        return page;
    }

    @Transactional(readOnly = false)
    public String addPublishRegime(Hr_RegimenPublish hr_regimenPublish)
    {
        if (StringUtils.isBlank(hr_regimenPublish.getRecordId()))
        {
            hr_regimenDao.addPublishRegime(hr_regimenPublish);
        }else
        {
            hr_regimenDao.updatePublishRegime(hr_regimenPublish);
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String deletePublish(Hr_RegimenPublish hr_regimenPublish)
    {
        hr_regimenPublish.preUpdate();
        hr_regimenDao.deletePublish(hr_regimenPublish);
        return "success";
    }

    public void setDataList(ExportExcel excel, List<Hr_RegimenPublish> list, String[] hearList) {
        for (Hr_RegimenPublish report : list) {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "制度名称":
                        if (StringUtils.isNotBlank(report.getRegimenName()))
                        {
                            val = report.getRegimenName();
                        }
                        else
                        {
                            val = "";
                        }
                        break;
                    case "制度文件":
                        if (StringUtils.isNotBlank(report.getOrgFileName()))
                        {
                            val = report.getOrgFileName();
                        }
                        else
                        {
                            val = "";
                        }
                        break;
                    case "发布日期":
                        if (StringUtils.isNotBlank(report.getPublishDateStr()))
                        {
                            val = String.valueOf(report.getPublishDateStr());
                        }
                        else
                        {
                            val = "";
                        }
                        break;
                    case "发布人":
                        if (StringUtils.isNotBlank(report.getUserName()))
                        {
                            val = report.getUserName();
                        }
                        else
                        {
                            val = "";
                        }
                        break;
                    case "发布目标":
                        if (StringUtils.isNotBlank(report.getPath()))
                        {
                            val = report.getPath();
                        }
                        else
                        {
                            val = "";
                        }
                        break;
                    case "状态":
                        if (StringUtils.isNotBlank(report.getStatus()))
                        {
                            if (report.getStatus().equals("1001")) {
                                val = "已作废";
                            } else if (report.getStatus().equals("1002")) {
                                val = "执行中";
                            }
                        }
                        else
                        {
                            val = "";
                        }
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }
    @Transactional(readOnly = false)
    public String delCancellation(Hr_Regimen hr_regimen)
    {
        //查询是否存在发布中的制度
        Integer count = hr_regimenDao.getExecution(hr_regimen);
        if (null != count && count > 0)
        {
            return "fail";
        }
        hr_regimenDao.delCancellation(hr_regimen);
        return "success";
    }

    @Transactional(readOnly = false)
    public String enableRecover(Hr_Regimen hr_regimen)
    {
        hr_regimenDao.enableRecover(hr_regimen);
        return "success";
    }
}
