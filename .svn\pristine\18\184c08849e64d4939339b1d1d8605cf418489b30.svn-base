<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isErrorPage="true"%>
<div class="p-2">
	<div class="row pt-2 pb-2 alert-light border-bottom border-bottom-secondary">
		<div class="col text-center text-primary">
			<h5>用印申请单</h5>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col text-left font-weight-bold text-primary" v-if="comList && comList.length > 0 && comList[0].name">
			提交人：{{comList[0].name}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			来&emsp;&emsp;源
			<select class="custom-select form-control h-32px" v-model="company">
				<option v-for="item in comList" :key="item.recordId" :value="item">
					{{item.departmentName}}
				</option>
			</select>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			用印公司
			<select class="custom-select form-control h-32px" v-model="audit.stampCompany" v-on:change="getDept()">
				<option v-for="item in companyList" :key="item.recordId" :value="item.recordId">
					{{item.departmentName}}
				</option>
			</select>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			用印部门
			<select class="custom-select form-control h-32px" v-model="audit.stampDept">
				<option v-for="item in deptList" :key="item.recordId" :value="item.recordId">
					{{item.departmentName}}
				</option>
			</select>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			需要印章
			<select class="custom-select form-control h-32px" v-model="audit.stamp">
				<option v-for="item in companyList" :key="item.recordId" :value="item.recordId">
					{{item.shortName}}
				</option>
			</select>
		</div>
	</div>
	<!-- startTime -->
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			申请时间
			<startDate></startDate>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			印&emsp;&emsp;章
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
			<div class="custom-control custom-checkbox">
				<input type="checkbox" class="custom-control-input" name="seal" 
					id="allMan" value="1">
					<label class="custom-control-label" for="allMan">&nbsp;&nbsp;&nbsp;&nbsp;公章</label>
			</div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<div class="custom-checkbox">
				<input type="checkbox" class="custom-control-input" name="seal" 
					id="idCard" value="2">
				<label class="custom-control-label" for="idCard">&nbsp;&nbsp;&nbsp;&nbsp;财务章</label>
			</div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<div class="custom-control custom-checkbox">
				<input type="checkbox" class="custom-control-input" name="seal" 
					id="noAll" value="3">
				<label class="custom-control-label" for="noAll">&nbsp;&nbsp;&nbsp;&nbsp;私章</label>
			</div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<div class="custom-control custom-checkbox">
				<input type="checkbox" class="custom-control-input" name="seal" 
					id="ukey" value="4">
				<label class="custom-control-label" for="ukey">&nbsp;&nbsp;&nbsp;&nbsp;Ukey</label>
			</div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<div class="custom-control custom-checkbox">
				<input type="checkbox" class="custom-control-input" name="seal" 
					id="contractSeal" value="5">
				<label class="custom-control-label" for="contractSeal">&nbsp;&nbsp;&nbsp;&nbsp;合同章</label>
			</div>
		<div class="custom-control custom-checkbox">
			<input type="checkbox" class="custom-control-input" name="seal"
				   id="accountsSeal" value="6">
			<label class="custom-control-label" for="accountsSeal">&nbsp;&nbsp;&nbsp;&nbsp;对账章</label>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			印章次数
			<input class="custom-select form-control h-32px" v-model="audit.stampCount">
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			印章专管员
			<select class="custom-select form-control h-32px" v-model="audit.passUser">
				<option v-for="item in empList" :key="item.recordId" :value="item.recordId">
					{{item.name}}
				</option>
			</select>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			印章用途
			<textarea class="form-control" v-model="audit.auditResult"></textarea>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">图片列表</div>
	</div>
	<div v-for="(item,index) in localIds">
		<div class="row alert-light pt-2 pb-2 align-items-center">
			<div class="col">
				<img :src="item" class="w-25" v-on:click="preview(item)">
			</div>
			<div class="col-2">
				<button class="btn btn-sm btn-danger" v-on:click="deleteImg(index)">删除</button>
			</div>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col"><button class="btn btn-primary" v-on:click="chooseImg">上传图片</button></div>
	</div>
	<div class="row fixed-bottom bg-light pt-2 pb-2">
		<div class="col">
			<button class="btn btn-secondary w-100" v-on:click="cancle">取消</button>
		</div>
		<div class="col">
			<button class="btn btn-primary w-100" v-on:click="submit">提交</button>
		</div>
	</div>
</div>