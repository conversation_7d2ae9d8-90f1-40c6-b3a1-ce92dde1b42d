<%@ page contentType="text/html;charset=UTF-8" %>

<div ng-intro-options="finishedmaterialsctrl.listOptions" ng-intro-method="finishedmaterialsctrl.helpList" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="finishedmaterialsctrl.introDetailOptions" ng-intro-method="finishedmaterialsctrl.infointroDetailOptions" ng-intro-autostart="shouldAutoStart"></div>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">财务管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="finance.finishedmaterialsinitialize">期初单价设置</a>
        </li>
        
    </ul>
    <div class="page-toolbar">
    		<button class="btn default btn-fit-height pull-right" ng-click="help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>
</div>
<!-- END 导航-->

<tabset class="tabset-margin-top">
    <!-- BEGIN 物料列表 -->
    <tab heading="物料列表" active="finishedmaterialsctrl.tabs.viewForm.active">
        <div class="panel panel-default" id="Step6">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div  class="panel-body">
                <form class="form-horizontal">
                    <div  class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">原料编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="finishedmaterialsctrl.query.no.value" disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">原料名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="finishedmaterialsctrl.query.name.value" disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="finishedmaterialsctrl.doQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">原料列表 </div>
            </div>
            <div class="portlet-body">
                <div id="Step1" class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
                        <tr class="heading">
                            <th>原料编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>原料名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th id="Step3">操作&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in finishedmaterialsctrl.page.data.list">
                            <td ng-bind="row.no"></td>
                            <td ng-bind="row.name"></td>
                            <td >
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="finishedmaterialsctrl.right.edit" ng-click="finishedmaterialsctrl.setMaterialPrice($index)"><i class="fa fa-magnet font-red"></i> 设置期初</a>
                            </td>
                            
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row page-margin-top">
                    <div id="Step5" class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select  class="form-control inline table-pageSize-width"
                                 ng-model="finishedmaterialsctrl.page.pageSize"
                                 ng-change="finishedmaterialsctrl.pageSizeChange()"
                                 ng-options="pageSizeOption for pageSizeOption in finishedmaterialsctrl.page.pageSizeOptions"
                                 disable-auto-validate="true">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{finishedmaterialsctrl.page.data.startCount}} / {{finishedmaterialsctrl.page.data.endCount}} 条，共 {{finishedmaterialsctrl.page.data.count}} 条</span>
                    </div>
                    <div id="Step4" class="col-md-12 col-lg-6">
                        <paging class="pull-right" page="finishedmaterialsctrl.page.data.pageNo" page-size="finishedmaterialsctrl.page.data.pageSize"
                                total="finishedmaterialsctrl.page.data.count" adjacent="1" dots="..." scroll-top="false" hide-if-empty="false"
                                ul-class="pagination" active-class="active" disabled-class="disabled"
                                show-prev-next="true" paging-action="finishedmaterialsctrl.doPage(page, pageSize, total)">
                        </paging>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </tab>
	
	
	
	<!-- BEGIN 用户编辑 -->
    <tab active="finishedmaterialsctrl.tabs.editForm.active" ng-show="finishedmaterialsctrl.tabs.editForm.show">
        <tab-heading>单价设置 <i class="fa fa-times set-cursor-pointer" ng-click="finishedmaterialsctrl.hideEditForm()"></i></tab-heading>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">原料期初单价设置</div>
            </div>
            <div id="stepDetail1" class="portlet-body">
                <form class="form-horizontal" id="editForm" name="editForm" ng-init="finishedmaterialsctrl.setFormScope(this)" novalidate="novalidate" ng-submit="finishedmaterialsctrl.submitMod(editForm);" ng-submit-force="true" >
                    <div class="row">
                        <div class="col-md-8 col-lg-8">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>原料编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input class="form-control" ng-model="finishedmaterialsctrl.item.material.no" ng-disabled = "true" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-8 col-lg-8">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>原料名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input class="form-control" ng-model="finishedmaterialsctrl.item.material.name" ng-disabled = "true" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                          <div class="col-md-8 col-lg-8">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>期初单价：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input class="form-control"  required ng-model="finishedmaterialsctrl.item.price"  ng-onlydecimals firstfocus="{{finishedmaterialsctrl.focus.main}}"/>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="stepDetail3" class="form-group" ng-if="finishedmaterialsctrl.right.edit">
                        <div class="col-sm-offset-4 col-sm-10">
                            <button type="submit"  ng-disabled="editForm.$invalid"  class="btn btn-primary btn-default-width" style="margin-left:15px;"><i class="fa fa-save"></i> 保&nbsp;存</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </tab>
</tabset>

<div id="static" class="modal fade" tabindex="-1"
     data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">
                    <span>提示</span>
                </h4>
            </div>
            <div class="modal-body">
                <p>
                    <span ng-bind="finishedmaterialsctrl.message"></span>
                </p>
            </div>
            <div class="modal-footer">
           		<button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
            </div>
        </div>
    </div>
</div>
