const supplierProduct_deail = {
    template: '#supplierProduct_deail',
    created:function(){
        this.productId = window.localStorage.getItem('productId');
        this.loadProductDeail();
    },
    data(){
        return {
            productId: "",
            configId: "",
            configList: [],
            product: {}
        }
    },
    methods:{
        loadProductDeail: function () {
            this.product = [];
            const _this = this;
            const entity = {};
            entity.recordId = this.productId;
            entity.type = 2;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/getProductDeail",
                data: JSON.stringify(entity),
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    _this.product = data;
                    _this.configId = _this.product.configId;
                    _this.loadConfigList();
                }
            })
        },
        loadConfigList: function () {
            this.configList = [];
            if (!this.configId){
                alert("请刷新重试");
                return;
            }
            const _this = this;
            const entity = {};
            entity.configId = this.configId;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/business/getConfigList",
                data: JSON.stringify(entity),
                contentType: "application/json",
                success: function(data)
                {
                    $('#loadingModal').modal('hide');
                    if (data && data.length > 0){
                        _this.configList = data;
                        if (_this.product){
                            // 给相关工艺赋值
                            _this.setCraftVal();
                        }
                    }
                }
            })
        },
        setCraftVal: function () {
            if (this.product && this.product.deailList && this.product.deailList.length > 0
                && this.configList && this.configList.length > 0){
                for (let i=0;i<this.configList.length;i++){
                    const config = this.configList[i];
                    if(!config.list){
                        continue;
                    }
                    for (let k=0;k<config.list.length;k++){
                        const deail = config.list[k];
                        deail.value = "";
                        deail.value1 = "";
                        deail.value2 = "";
                        deail.value3 = "";
                        for (let j=0;j<this.product.deailList.length;j++){
                            if (this.product.deailList[j].configDeailId == deail.recordId){
                                if (deail.inputType == 1){
                                    deail.value = this.product.deailList[j].value;
                                }else if (deail.inputType == 2){
                                    deail.value1 = this.product.deailList[j].value1;
                                    deail.value2 = this.product.deailList[j].value2;
                                }else if (deail.inputType == 3){
                                    deail.value1 = this.product.deailList[j].value1;
                                    deail.value2 = this.product.deailList[j].value2;
                                    deail.value3 = this.product.deailList[j].value3;
                                }else if (deail.inputType == 4){
                                    if (this.product.deailList[j].value){
                                        if(!deail.list){
                                            continue;
                                        }
                                        for (let z = 0; z < deail.list.length; z++) {
                                            if (deail.list[z].recordId == this.product.deailList[j].value){
                                                deail.list[z].checked = 1;
                                                break;
                                            }
                                        }
                                    }
                                }else if (deail.inputType == 5){
                                    if (this.product.deailList[j].value){
                                        const vals = this.product.deailList[j].value.split(",");
                                        for (let x=0;x<vals.length;x++){
                                            for (let z = 0; z < deail.list.length; z++) {
                                                if (deail.list[z].recordId == vals[x]){
                                                    deail.list[z].checked = 1;
                                                }
                                            }
                                        }
                                    }
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
    }
}