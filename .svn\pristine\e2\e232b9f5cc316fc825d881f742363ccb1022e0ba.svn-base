const bindErp = {
	template:'#bindErp',
	computed: {
		user: {
			get () {
				return this.$store.state.myStore.user
		    }
		}
	},
	//创建前设置
	beforeCreate () {
		this.$store.dispatch('myStore/setUserInformation')
	    document.querySelector('body').setAttribute('style', 'background-color:#B8B8DC;')
	},
	data(){
		return {
			company: {}
		}
	},
	methods:{
		isPoneAvailable: function(str) {
			var myreg=/^[1][3,4,5,7,8][0-9]{9}$/;
            if (!myreg.test(str)) {
                return false;
            } else {
                return true;
            }
        },
		sendValiCode:function(){
			if(this.user && this.user.userId){
				if(this.company && this.company.phone){
					if(!this.isPoneAvailable(this.company.phone))
					{
						alert("您填写的手机号码有误，请重新填写！");
						return ;
					}
					$.ajax({
			     		type:"post",
			     		url:ctx + "/f/wechat/kybsoft/phonevali",
			     		data:this.company.phone,
			     		contentType:"application/json",
			     		success:function(data)
			     		{ 
			     			if(data){
			     				alert(data);
			     			}else{
			     				alert("验证码发送错误");
			     			}
			     		}
			     	})
				}else{
					alert("请填写自己在该ERP中的手机号码");
				}
			}else{
				alert("请登录后再进行绑定");
			}
		},
		bindErp:function(){
			if(this.user && this.user.userId){
				var router = this.$router;
				if(this.company){
					if(this.company.name){
						if(this.company.phone){
							if(this.company.valiCode){
								var uc = {}
								uc.valiCode = this.company.valiCode
								uc.phone = this.company.phone
								uc.user = {}
								uc.user.recordId = this.user.userId
								uc.company = this.company
								$.ajax({
						     		type:"post",
						     		url:ctx + "/f/wechat/kybsoft/bindErp",
						     		data:JSON.stringify(uc),
						     		contentType:"application/json",
						     		success:function(data)
						     		{
						     			if(data.phonemsg)
						     			{
						     				alert(data.phonemsg);
						     			}
						     			else
						     			{
						     				router.push("/");
						     			}
						     		}
						     	})
							}else{
								alert("请填写手机验证码");
							}
						}else{
							alert("请填写自己在该ERP中的手机号码");
						}
					}else{
						alert("请填写公司全称");
					}
				}else{
					alert("请填写公司信息");
				}
			}else{
				alert("请登录后再进行绑定");
			}
		}
	}
}