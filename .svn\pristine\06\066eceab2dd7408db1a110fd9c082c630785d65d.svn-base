package com.kyb.pcberp.modules.production.web;

import com.kyb.pcberp.modules.production.entity.PrepareToFollowUp;
import com.kyb.pcberp.modules.production.service.PrepareService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "${adminPath}/production/prepare")
public class PrepareController {

    @Autowired
    private PrepareService prepareService;

    /**
     * 准备跟进页面定位
     *
     * @return
     */
    @RequiresPermissions("production:prepare:view")
    @RequestMapping(value = "list")
    public String prepare()
    {
        return "modules/production/prepare";
    }

    @RequestMapping(value = "prepareList", method = RequestMethod.POST)
    @ResponseBody
    public Map<String,List<?>> getEngineeringPrepare(@RequestBody PrepareToFollowUp prepare)
    {
        return prepareService.getEngineeringPrepare(prepare);
    }
}
