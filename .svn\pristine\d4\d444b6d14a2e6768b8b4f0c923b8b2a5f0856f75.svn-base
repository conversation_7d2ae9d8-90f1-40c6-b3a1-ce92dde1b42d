package com.kyb.pcberp.common.utils;

public class FeedingAux
{
    /** zjn 2019-05-17 Integer转String */
    public static String integerToString(Integer quantity)
    {
        return null == quantity ? "0" : quantity.toString();
    }
    
    /** zjn 2019-05-17 String转Integer */
    public static Integer stringToInteger(String quantity)
    {
        return StringUtils.isBlank(quantity) ? 0 : Integer.valueOf(quantity);
    }
}
