package com.kyb.pcberp.modules.sys.utils;

import cn.hutool.http.HttpUtil;
import com.kyb.pcberp.modules.sys.entity.WeatherInformation;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;


public class IPUtils {

    /**
     * 2.通过调用接口的方式获取IP
     */
    public static String getIp() {
        try {
            URL realUrl = new URL("http://whois.pconline.com.cn/ipJson.jsp");
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setUseCaches(false);
            conn.setReadTimeout(6000);
            conn.setConnectTimeout(6000);
            conn.setInstanceFollowRedirects(false);
            int code = conn.getResponseCode();
            String ip = "";
            if (code == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(
                        conn.getInputStream(),"GBK"));
                StringBuffer buffer = new StringBuffer();
                String line;
                while ((line = reader.readLine()) != null) {
                    buffer.append(line);
                }
                ip = buffer.substring(buffer.indexOf("ip") + 5, buffer.indexOf("pro") - 3);
            }
            return ip;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 3.通过调用接口根据ip获取归属地
     */
    public static String getAddress(String ip , WeatherInformation weatherInformation) {
        try {
            URL realUrl = new URL("http://whois.pconline.com.cn/ipJson.jsp?ip=" + ip + "&json=true");
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setUseCaches(false);
            conn.setReadTimeout(6000);
            conn.setConnectTimeout(6000);
            conn.setInstanceFollowRedirects(false);
            int code = conn.getResponseCode();
            String ipaddr = "";
            if (code == 200) {
                // 获取输入流
                BufferedReader reader = new BufferedReader(new InputStreamReader(
                        conn.getInputStream(), "GBK"));// 往对端写完数据对端服务器返回数据
                // ,以BufferedReader流来读取
                StringBuffer buffer = new StringBuffer();
                String line = "";
                while ((line = reader.readLine()) != null) {
                    buffer.append(line);
                }
                ipaddr = buffer.substring(buffer.indexOf("addr") + 7, buffer.indexOf("regionNames") - 6);
            }
            String weatherInfor = HttpUtil.get("http://portalweather.comsys.net.cn/weather03/api/weatherService/getDailyWeather?cityName=" + ip);
            String weather = weatherInfor.substring(weatherInfor.indexOf("text_day") + 11 , weatherInfor.indexOf("code_day") - 3);
            String windSpeed = weatherInfor.substring(weatherInfor.indexOf("wind_speed") + 13 , weatherInfor.indexOf("wind_scale") - 3);
            weatherInformation.setIpaddr(ipaddr);
            weatherInformation.setWeather(weather);
            weatherInformation.setTemperature(windSpeed);
            return ipaddr;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
