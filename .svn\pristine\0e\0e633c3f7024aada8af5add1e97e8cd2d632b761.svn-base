<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.approval.dao.BackupAttachementsDao">

	<select id="findList" resultType="BackupAttachements">
		SELECT
			a.recordId,
			a.companyId AS "company.recordId",
			a.backupId,
			a.realFileName,
			a.fileUrl,
			a.type,
			a.attachementId,
			a.activeFlag,
			a.createdBy AS "createdBy.recordId",
			a.createdDate,
			a.lastUpdBy AS "lastUpdBy.recordId",
			a.lastUpdDate,
			b.customerId AS "customerId" 
		FROM md_backup_attachements a
		LEFT JOIN md_approval_backup_two b ON b.recordId = a.backupId AND b.activeFlag = 1
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1
		<if test="backupId != null and backupId != ''">
			AND a.backupId = #{backupId}
		</if>
		<if test="type != null and type != ''">
			AND a.type = #{type}
		</if>
	</select>

	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO md_backup_attachements(
			companyId,
			backupId,
			realFileName,
			fileUrl,
			type,
			attachementId,
			activeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate 
		) VALUES (
			#{company.recordId},
			#{backupId},
			#{realFileName},
			#{fileUrl},
			#{type},
			#{attachementId},
			1,
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate}			
		)
	</insert>

	<update id="delete">
		UPDATE md_backup_attachements SET
			activeFlag = 2
		WHERE 1=1
		<if test="recordId != null and recordId != ''">
			AND recordId = #{recordId}
		</if>
		<if test="backupId != null and backupId != ''">
			AND backupId = #{backupId}
		</if>
	</update>
	
	<update id="update">
		UPDATE md_backup_attachements SET 	
			realFileName = #{realFileName},
			fileUrl = #{fileUrl}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="getAttachmments" resultType="WechatAttachments">
		SELECT 
			recordId,
			realFileName AS name,
			fileUrl,
			type AS "typeInt" 
		FROM md_backup_attachements 
		WHERE backupId = #{recordId} AND activeFlag= 1
		<if test="customerId != null and customerId != ''">
			UNION ALL
			SELECT 
				recordId,
				realFileName AS name,
				fileUrl,
				null AS "typeInt"
			FROM md_customer_attachements 
			WHERE customerId = #{customerId}
		</if>
		<if test="supplierId != null and supplierId != ''">
			UNION ALL
			SELECT 
				recordId,
				realFileName AS name,
				fileUrl,
				null AS "typeInt"
			FROM md_supplierFile 
			WHERE no = #{no}
		</if>
	</select>
	
	<!-- zjn 2020-01-11 更新备份附件id -->
	<update id="updateAttachementId">
		UPDATE md_backup_attachements SET
			attachementId = #{attachementId}
		WHERE recordId = #{recordId}
	</update>
	
</mapper>