/**
 * 
 */
package com.kyb.pcberp.modules.eg.entity;

import java.math.BigDecimal;
import java.util.List;

import org.hibernate.validator.constraints.Length;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.User;

/**
 * 工序Entity
 * 
 * <AUTHOR>
 * @version 2015-09-16
 */
public class EgProcess extends DataEntity<EgProcess>
{
    
    private static final long serialVersionUID = 1L;
    
    private String name; // 工序名称
    
    private String category; // 工序大类
    
    private String visibleFlag; // 在WIP表中是否显示
    
    private String operLevel; // 操作级别
    
    private Integer seqNum;
    
    private List<ProcessCraft> craftList;
    
    private Integer operFlag; // 操作标志 1:添加 2： 删除
    
    private Boolean isFirst = false;
    
    private String processValueId;
    
    private String skipCount; // 跳过过数，0为不跳，1为跳过
    
    private String handOverFlag;
    
    private Integer procount; // 正在完成款数(生产报表中查询字段)
    
    private String outDeal; // 外发标记 0 不外发 1 外发
    
    private String skipQc; // 跳过品质检测 0，不跳过 1，跳过
    
    private List<User> dataOperatorList; // 工序有过数权限操作人
    
    private Template template; // 工程卡模板
    
    private String area;
    
    private String type; // 1:工序，2:工序值
    
    private Double processArea;
    
    private Integer proNum;
    
    private Double proArea;
    
    private Double balanceArea; // 结存面积
    
    private Double nextBalanceArea; // 下道工序结存面积
    
    private String processIds;
    
    private String processCraftIds;
    
    private Double showBalanceArea;
    
    private String staCategory;
    
    private Integer sortNum; // 排序
    
    private Double needDays; // 需要天数
    
    private String craftId;
	
	 private String produceBatchDetailId;

     private String passingPerson; // 过数人

    private BigDecimal passArea; // 过数面积
    
    public EgProcess()
    {
        super();
    }
    
    public EgProcess(String id)
    {
        super(id);
    }
    
    public EgProcess(Company company)
    {
        this.setCompany(company);
    }
    
    @Length(min = 1, max = 100, message = "工序名称长度必须介于 1 和 100 之间")
    public String getName()
    {
        return name;
    }
    
    public void setName(String name)
    {
        this.name = name;
    }
    
    @Length(min = 1, max = 100, message = "工序大类长度必须介于 1 和 100 之间")
    public String getCategory()
    {
        return category;
    }
    
    public void setCategory(String category)
    {
        this.category = category;
    }
    
    @Length(min = 0, max = 2, message = "在WIP表中是否显示长度必须介于 0 和 2 之间")
    public String getVisibleFlag()
    {
        return visibleFlag;
    }
    
    public void setVisibleFlag(String visibleFlag)
    {
        this.visibleFlag = visibleFlag;
    }
    
    @Length(min = 0, max = 2, message = "操作级别长度必须介于 0 和 2 之间")
    public String getOperLevel()
    {
        return operLevel;
    }
    
    public void setOperLevel(String operLevel)
    {
        this.operLevel = operLevel;
    }
    
    public Integer getSeqNum()
    {
        return seqNum;
    }
    
    public void setSeqNum(Integer seqNum)
    {
        this.seqNum = seqNum;
    }
    
    public List<ProcessCraft> getCraftList()
    {
        return craftList;
    }
    
    public void setCraftList(List<ProcessCraft> craftList)
    {
        this.craftList = craftList;
    }
    
    @JsonIgnore
    public Integer getOperFlag()
    {
        return operFlag;
    }
    
    public void setOperFlag(Integer operFlag)
    {
        this.operFlag = operFlag;
    }
    
    public Boolean getIsFirst()
    {
        return isFirst;
    }
    
    public void setIsFirst(Boolean isFirst)
    {
        this.isFirst = isFirst;
    }
    
    public String getProcessValueId()
    {
        return processValueId;
    }
    
    public void setProcessValueId(String processValueId)
    {
        this.processValueId = processValueId;
    }
    
    public String getSkipCount()
    {
        return skipCount;
    }
    
    public void setSkipCount(String skipCount)
    {
        this.skipCount = skipCount;
    }
    
    public String getHandOverFlag()
    {
        return handOverFlag;
    }
    
    public void setHandOverFlag(String handOverFlag)
    {
        this.handOverFlag = handOverFlag;
    }
    
    public Integer getProcount()
    {
        return procount;
    }
    
    public void setProcount(Integer procount)
    {
        this.procount = procount;
    }
    
    public String getOutDeal()
    {
        if (outDeal == null)
        {
            outDeal = "0";
        }
        return outDeal;
    }
    
    public void setOutDeal(String outDeal)
    {
        this.outDeal = outDeal;
    }
    
    public String getSkipQc()
    {
        return skipQc;
    }
    
    public void setSkipQc(String skipQc)
    {
        this.skipQc = skipQc;
    }
    
    public List<User> getDataOperatorList()
    {
        return dataOperatorList;
    }
    
    public void setDataOperatorList(List<User> dataOperatorList)
    {
        this.dataOperatorList = dataOperatorList;
    }
    
    public Template getTemplate()
    {
        return template;
    }
    
    public void setTemplate(Template template)
    {
        this.template = template;
    }
    
    public String getArea()
    {
        return area;
    }
    
    public void setArea(String area)
    {
        this.area = area;
    }
    
    public Double getProcessArea()
    {
        return processArea;
    }
    
    public void setProcessArea(Double processArea)
    {
        this.processArea = processArea;
    }
    
    public Integer getProNum()
    {
        return proNum;
    }
    
    public void setProNum(Integer proNum)
    {
        this.proNum = proNum;
    }
    
    public Double getProArea()
    {
        return proArea;
    }
    
    public void setProArea(Double proArea)
    {
        this.proArea = proArea;
    }
    
    // 实体深拷贝
    public EgProcess clone()
    {
        EgProcess o = null;
        try
        {
            o = (EgProcess)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } // Object 中的clone()识别出你要复制的是哪一个对象。
        return o;
    }
    
    public String getType()
    {
        return type;
    }
    
    public void setType(String type)
    {
        this.type = type;
    }
    
    public Double getBalanceArea()
    {
        return balanceArea;
    }
    
    public void setBalanceArea(Double balanceArea)
    {
        this.balanceArea = balanceArea;
    }
    
    public Double getNextBalanceArea()
    {
        return nextBalanceArea;
    }
    
    public void setNextBalanceArea(Double nextBalanceArea)
    {
        this.nextBalanceArea = nextBalanceArea;
    }
    
    public String getProcessIds()
    {
        return processIds;
    }
    
    public void setProcessIds(String processIds)
    {
        this.processIds = processIds;
    }
    
    public String getProcessCraftIds()
    {
        return processCraftIds;
    }
    
    public void setProcessCraftIds(String processCraftIds)
    {
        this.processCraftIds = processCraftIds;
    }
    
    public Double getShowBalanceArea()
    {
        return showBalanceArea;
    }
    
    public void setShowBalanceArea(Double showBalanceArea)
    {
        this.showBalanceArea = showBalanceArea;
    }
    
    public String getStaCategory()
    {
        return staCategory;
    }
    
    public void setStaCategory(String staCategory)
    {
        this.staCategory = staCategory;
    }
    
    public Integer getSortNum()
    {
        return sortNum;
    }
    
    public void setSortNum(Integer sortNum)
    {
        this.sortNum = sortNum;
    }
    
    public Double getNeedDays()
    {
        return needDays;
    }
    
    public void setNeedDays(Double needDays)
    {
        this.needDays = needDays;
    }
    
    public String getCraftId()
    {
        return craftId;
    }
    
    public void setCraftId(String craftId)
    {
        this.craftId = craftId;
    }

   public String getProduceBatchDetailId()
    {
        return produceBatchDetailId;
    }

    public void setProduceBatchDetailId(String produceBatchDetailId)
    {
        this.produceBatchDetailId = produceBatchDetailId;
    }

    public String getPassingPerson() {
        return passingPerson;
    }

    public void setPassingPerson(String passingPerson) {
        this.passingPerson = passingPerson;
    }

    public BigDecimal getPassArea() {
        return passArea;
    }

    public void setPassArea(BigDecimal passArea) {
        this.passArea = passArea;
    }
}
