/* Setup general page controller */
kybApp.controller('materialRejectCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'BaseUtil', 'CommonUtil','$filter',  function ($rootScope, $scope, upida, $timeout, BaseUtil, CommonUtil,$filter) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();
        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
    var vm = this;

    $scope.shouldAutoStart = false;
    $scope.introListOptions = {
        steps:[
        {
            element: '#step1',
            intro: "当您打开页面后会自动加载退货信息至列表，对某一行数据可单击首列单据编号或双击该行进入详情页！",
            position: 'top'
        },
        {
            element: '#step2',
            intro: "单击右边的排序按钮进行排序，箭头向上为升序，箭头向下为降序，若为上下则未排序！",
            position: 'bottom'
        },
        {
            element: '#step3',
            intro: '行记录快速操作，对退货进行删除、确认以及打印等操作！',
            position: 'top'
        },
        {
            element: '#step4',
            intro: '此处为分页区域，您可以点击您想查看的页面！',
            position: 'top'
        },
        {
            element: '#step5',
            intro: "此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！",
            position: 'top'
        },
        {
            element: '#step6',
            intro: '此处为查询条件，可设置不同的条件，点击右下角的查询来过滤信息！',
            position: 'bottom'
        },
        {
            element: '#step7',
            intro: '操作按钮区域，可添加退货信息和导出信息！',
            position: 'bottom'
        },
        {
            element: '#step8',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };

    $scope.introInfoOptions = {
        steps:[
        {
            element: '#stepInfo1',
            intro: "退货信息录入区域，带 <strong style='color:red;'> *</strong> 的为必填项！",
            position: 'top'
        },
        {
            element: '#stepInfo2',
            intro: "明细列表",
            position: 'top'
        },
        {
            element: '#stepInfo3',
            intro: '行数据操作按钮区域，可对退货明细进行删除和双击行数据进去查看详情信息！',
            position: 'top'
        },
        {
            element: '#stepInfo4',
            intro: '点击保存按钮以完成本次操作！',
            position: 'top'
        },
        {
            element: '#stepInfo5',
            intro: '添加退货明细按钮,点击可添加明细信息！',
            position: 'top'
        },
        {
            element: '#stepInfo6',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
    
    $scope.introInfo1Options = {
            steps:[
            {
                element: '#stepInfo11',
                intro: "信息录入区域，带 <strong style='color:red;'> *</strong> 的为必填项！",
                position: 'top'
            },
            {
                element: '#stepInfo12',
                intro: "明细列表",
                position: 'top'
            },
            {
                element: '#stepInfo13',
                intro: '谢谢使用，再见。'
            }
            ],
            showStepNumbers: false,
            exitOnOverlayClick: true,
            exitOnEsc:true,
            nextLabel: '<strong>下一步!</strong>',
            prevLabel: '<span style="color:green">上一步</span>',
            skipLabel: '退出',
            doneLabel: '谢谢'
    };
    
    $scope.introDetailOptions = {
        steps:[
        {
            element: '#stepDetail1',
            intro: "退货明细录入区域，带<strong style='color:red;'> *</strong> 的为必填项！",
            position: 'top'
        },
        {
            element: '#stepDetail2',
            intro: "操作按钮列，可保存本次操作和重置页面数据或者编辑！",
            position: 'top'
        },
        {
            element: '#stepContact3',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
    
    $scope.help = function(){
    	if (vm.tabs.viewForm.active){
    		$scope.helpList();
    	} else if (vm.tabs.editForm.active) {
    		$scope.helpInfo();
    	}else if(vm.tabs.seeDetail.active){
    		$scope.helpInfo1();
    	} else {
    		if(vm.editTitle!="查看"){
    			$scope.helpDetail();
    		}
    	}
    };
    
    // tj 判断是否多次点击
    vm.clicks = true;
    //自动聚焦
    vm.focus = {main: 1, detail: 1};
    // 分页数据
    vm.page = {};
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    // 字典项分页数据
    vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.data.list = {};
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "purch/materialReject/page";
    vm.page.condition = []; // 条件
    vm.temppro = {}; //子表保存需要的编号储存
    // 默认排序按时间排序排序处理
    vm.sort = {};
    vm.sort.no = {both: true, desc: false, asc: false};
    vm.sort.supplierNo = {both: true, desc: false, asc: false};
    vm.sort.supplierName = {both: true, desc: false, asc: false};
    vm.sort.returnDate = {both: false, desc: true, asc: false};
    vm.sort.status = {both: true, desc: false, asc: false};

    vm.sortClick = function(col){
        vm.sort[col].both = false;
        if (!vm.sort[col].desc && !vm.sort[col].asc){
            vm.sort[col].asc = true;
        }else {
            if (vm.sort[col].asc){
                vm.sort[col].desc = true;
                vm.sort[col].asc = false;
            }else {
                vm.sort[col].desc = false;
                vm.sort[col].asc = true;
            }
        }

        for(var p in vm.sort){
            if (p !== col){
                vm.sort[p].desc = false;
                vm.sort[p].asc = false;
                vm.sort[p].both = true;
            }
        }
        if(col == "no"){
            vm.query.sort.value = vm.sort[col].asc ? "a.no" + " ASC" :  "a.no" + " DESC";
        }
        else if(col == "supplierNo"){
            vm.query.sort.value = vm.sort[col].asc ? "ms.no" + " ASC" :  "ms.no" + " DESC";
        } else if (col == "supplierName"){
            vm.query.sort.value = vm.sort[col].asc ? "ms.shortName" + " ASC" :  "ms.shortName" + " DESC";
        } else if (col == "returnDate"){
            vm.query.sort.value = vm.sort[col].asc ? "a.applyDate" + " ASC" :  "a.applyDate" + " DESC";
        }else {
        	vm.query.sort.value = vm.sort[col].asc ? col + " ASC" :  col + " DESC";
        }
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    };
    // 查询条件参数
    vm.query = {}; // 查询对象
    // 排序字段
    vm.query.sort = {};
    vm.query.sort.name = "orderBy";
    vm.query.sort.value = "a.createdDate DESC";
    
    /*vm.query.no = {};
    vm.query.no.name = "no";
    vm.query.no.value = "";
    vm.query.supplierNo = {};
    vm.query.supplierNo.name = "supplier.no";
    vm.query.supplierNo.value = "";
    vm.query.supplierName = {};
    vm.query.supplierName.name = "supplier.name";
    vm.query.supplierName.value = "";*/
    vm.edit = {};
    vm.edit.detail = {};
    vm.edit.ischangesup = false;
    vm.edit.isordercode = true;
    vm.edit.item = {};
    // tabs控制
    vm.tabs = {
        viewForm: {active: true},
        editForm: {active: false, show: false}
    };
    // 权限
    vm.right = {};
    // 编辑操作对象
    vm.finished = {};
    vm.puReturnsDetail = {};
    vm.puReturnsDetail.purchasingType = 0;
    vm.purchasingDetail = {};
    vm.purchasingDetail.purchasing = {};
    vm.puReturnsDetail.purchasingDetail={};
    vm.puReturnsDetail.prdorderDetail={};
    vm.tempList = [];//临时中间变量（子表）
    // 删除主键
    vm.delIndex = -1;
    // 编辑标题
    vm.editTitle = "";
    // 数据字典
    vm.refer = {};
    vm.refer.currencyTypeList = [];// 货币类型
    vm.refer.employeeList = []; //公司职员
    vm.refer.purchasingDetailList = [];//原料采购明细列表
    vm.refer.prdorderDetailList = [];//成品采购明细列表
    //希望处理方式
    vm.refer.desireTreatments=[];
    vm.refer.desireTreatments.push({"value": 1,"name":"补货"});
    vm.refer.desireTreatments.push({"value": 2,"name":"退货"});
    //采购类型
    vm.purchasingTypes = [{
        "value": '1',
        "name": "原料采购"
    }, {
        "value": '2',
        "name": "成品采购"
    }];
    vm.refer.purchasingNos = [];// 采购订单编码
    
    // 时间范围的选项
    vm.rangeOptions = {
        format: "YYYY-MM-DD",
        startDate: new Date((new Date).setMonth(((new Date).getMonth()-1))),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
    };
    
    // 时间范围的Model
    vm.time = {	
        start: {},
        end: {}
    };
    
    vm.query.sentTimeStartQr = {};
    vm.query.sentTimeStartQr.name = "sentTimeStartQr";
    vm.query.sentTimeStartQr.value = "";
    vm.query.sentTimeEndQr = {};
    vm.query.sentTimeEndQr.name = "sentTimeEndQr";
    vm.query.sentTimeEndQr.value = "";
    
    vm.initDate=function(date)
	{
		 if(date==""){
			 vm.rangeOptions = {
		        //format: "YYYY-MM-DD",
		    	startDate: new Date(vm.query.sentTimeStartQr.value),
		    	minDate:new Date(new Date(vm.query.sentTimeEndQr.value).setFullYear(new Date(vm.query.sentTimeEndQr.value).getFullYear()-5))
			 };
			 vm.time= {
		       start: vm.rangeOptions.startDate,
				 end: vm.rangeOptions.minDate
			 } 
		 }
	 }
    
    // 分页按钮单击处理
    vm.doPage = function (page, pageSize, total) {
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        vm.init(page, pageSize, vm.page.condition, vm.page.url);
    };
    /*
     * 兼容条件查询与普通查询
     * no 当前页
     * size 当前页显示数量
     * condition 是数组，可放入多个条件对象，如{name:"type", value:"1"}  名称对应实体字段名称，值对应页面输入的值   
     * url 请求数据链接
     */
    vm.init = function (no, size, condition, url) {
    	MainCtrl.blockUI({
    	    //进度条展示
    	    animate: true,
    	});
        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;
        condition.push({
            name: vm.query.sort.name,
            value: vm.query.sort.value
        });
        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condition);
        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};
            // 如果结果为空
            if (typeof result === 'undefined' || typeof result.list === 'undefined') {
                data.pageNo = 1;
                data.pageSize = 10;
                data.list = [];
                data.startCount = 0;
                data.endCount = 0;
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }
            vm.page.data = data;
            //进度条关闭
            MainCtrl.unblockUI();
        });
    };
    // 页面显示数量改变
    vm.pageSizeChange = function () {
        vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url);
    };
    // 查询数据
    vm.doQuery = function () {
        // 设置查询条件
        var condition = [];
        if (vm.query.no !== "") {
            condition.push({
                name: "no",
                value: vm.query.no
            });
        }
        if (vm.query.customerNo !== "") {
            condition.push({
                name: "customerNo",
                value: vm.query.customerNo
            });
        }
        if (vm.query.contractNo !== "") {
            condition.push({
                name: "contractNo",
                value: vm.query.contractNo
            });
        }
        
        //订单日期
        if (vm.time.start) {
            condition.push({
                name: vm.query.sentTimeStartQr.name,
                value: vm.time.start.valueOf()
            });
            vm.query.sentTimeStartQr.value = vm.time.start.valueOf();
        }
        //订单日期
        if (vm.time.end) {
            condition.push({
                name: vm.query.sentTimeEndQr.name,
                value: vm.time.end.valueOf()
            });
            vm.query.sentTimeEndQr.value = vm.time.end.valueOf();
        }
        
        vm.page.pageNo = 1;
        vm.page.condition = condition;
        // 查询数据
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    };
    vm.reset = function () {
        vm.query.no = "";
        
    };
    //双击事件响应的方法
    vm.showOrEditPuRtuens = function(index){
    	vm.finished = angular.copy(vm.page.data.list[index]);
    	vm.hideEditForm();
		vm.seePuReturns();
    	//vm.hideDetailForm();
    };
    // 单击编辑
    vm.modPuReturns = function () {
    	vm.focus.main += 1;
        vm.edit.isAdd = false;
        vm.editTitle = "编辑";
        vm.showEditForm();
        //去查询  主单   对应的详单
        upida.post("purch/puReturns/findPuReturnsDetailByReturnsId", vm.finished).then(function (data) {
            vm.tempList = data;
            if(vm.tempList.length>0){
            	vm.edit.ischangesup = true;
            }else{
            	vm.edit.ischangesup = false;
            }
        });
    };
    
    // 单击查看详情
    vm.seePuReturns = function (index) {
        vm.editTitle = "查看";
        vm.tabs.seeDetail.show = true;
        $timeout(function () {
        	vm.tabs.seeDetail.active = true;
        });
    };
    
    vm.onTimeSetReturnDate = function (newDate, oldDate) {
    	vm.finished.applyDate = $filter('date')(newDate,'yyyy-MM-dd HH:mm:ss');
    };
    
    // 单击添加
    vm.addPuReturns = function () {
    	vm.editTitle = "添加";
    	vm.edit.isAdd = true;
    	vm.edit.ischangesup = false;
        vm.tempList = [];
        vm.finished = {};
        vm.finished.remark = "";
        vm.focus.main += 1;
        //设置退货时间为当前时间
        vm.finished.returnDate = $filter('date')(new Date(),'yyyy-MM-dd HH:mm:ss');
        vm.finished.currencyType = BaseUtil.getDefaultData().currencyType;
        vm.finished.operator = BaseUtil.getUser();
        //获取退货订单编号
        upida.post("purch/puReturns/getNo").then(function (data) {
            vm.finished.no = data;
        });
        vm.showEditForm();
    };
    // 单击取消‘’编辑和添加
    vm.cacelBtn = function (form) {
        form.$setPristine();
        vm.hideEditForm();
    };
    vm.hideSeeDetail = function () {
        vm.tabs.seeDetail.show = false;
        vm.tabs.seeDetail.active = false;
        $timeout(function () {
        	vm.tabs.viewForm.active = true;
        });
    }
    vm.mainformScope = null;
    vm.formScope = null;
    vm.setFormScope = function(scope, type){
    	if (type == 0){
    		vm.mainformScope = scope;
    	}else {
    		vm.formScope = scope;
    	}
    };
    
    // 显示编辑
    vm.showEditForm = function () {
        vm.tabs.editForm.show = true;
        vm.tabs.editForm.active = true;
    };
    
    // 隐藏编辑
    vm.hideEditForm = function () {
        vm.tabs.editForm.show = false;
        vm.tabs.editForm.active = false;
        $timeout(function () {
        	vm.tabs.viewForm.active = true;
        });
        vm.tabs.detailForm.show = false;
        vm.mainformScope.returnsForm.$setPristine();
    };
    
    // 创建和编辑
    vm.submitReturnsFormMod = function (form) {
    	if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    
    	    form.$setDirty();
            if (form.$valid) {
            	MainCtrl.blockUI({
            		//进度条展示
            		animate: true,
            	});	
                //格式化时间
                if(vm.finished.applyDate  instanceof Date){
                	vm.finished.applyDate=CommonUtil.formatDate("yyyy-MM-dd hh:mm:ss",vm.finished.applyDate);
                }
                vm.finished.createdDate = null;
                upida.post("purch/materialReject/update", vm.finished).then(function (data) {
                    if(data=="success")
                    {
                    	// 重新查询数据
                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
                        // 初始化数据
                        vm.finished = {};
                        // 提示信息
                        vm.message = "保存成功！";
                        $('#static').modal();
                        vm.hideEditForm();
                        //清理验证痕迹
                        form.$setPristine();
                    }
                    else
                    {
                    	MainCtrl.unblockUI();
                    	vm.message = data;
                        $('#static').modal();
                    }
                    vm.clicks = true;// tj 2017-09-07  用户是否多次点击
                });
            }else
            {
            	vm.clicks = true;
            }
    	}
    };
    // 删除
    vm.delPuReturns = function (index) {
        vm.delIndex = index;
        vm.delMsg = "您确定要删除退货\"" + vm.page.data.list[index].no + "\"吗?";
        // 提示信息
        $('#staticRemove').modal();
    };
    // 做删除操作
    vm.doDelPuReturns = function () {
    	if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    
    	    MainCtrl.blockUI({
        	    animate: true
        	});
        	var reqData = {};
            reqData.recordId = vm.page.data.list[vm.delIndex].recordId;
            reqData.no = vm.page.data.list[vm.delIndex].no;
            upida.post("purch/puReturns/delete",reqData).then(function (data) {
            	if(data == "failDelete")
            	{
            		vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            		vm.message = "退货单已经被删除了！";
                    $('#static').modal();
            	}
            	else if(data == "failComfirm")
            	{
            		vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            		vm.message = "退货单已经被确认了！";
                    $('#static').modal();
            	}
            	else if(data == "success")
            	{
            		// 重新查询数据
                    vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
                    vm.delIndex = -1;
                    vm.message = data.message;
                    $('#static').modal();
            	}
            	else
            	{
            		vm.message = "系统内部错误，请刷新重试！";
                    $('#static').modal();
            	}
                vm.clicks = true;// tj 2017-09-07  用户是否多次点击
            });
    	}
    	
    };
    // 复制添加
    vm.clonePuReturns = function (index) {
    	MainCtrl.blockUI({
    	    animate: true
    	});
    	vm.focus.main += 1;
        vm.edit.isAdd = true;
        vm.editTitle = "添加";
        vm.finished = angular.copy(vm.page.data.list[index]);
        // 查出所有的明细项
        upida.post("purch/puReturns/findPuReturnsDetailByReturnsId", vm.finished).then(function (data) {
        	angular.forEach(data, function (p) {
        		//退货明细表主键清空
        		p.recordId = null;
		    });
            vm.tempList = data;
            if(vm.tempList.length>0){
            	vm.edit.ischangesup = true;
            }else{
            	vm.edit.ischangesup = false;
            }
            //退货主表主键清空
            vm.finished.recordId = null;
            //获取退货订单编号
            upida.post("purch/puReturns/getNo").then(function (data) {
            	vm.finished.no = data;
            	MainCtrl.unblockUI();
            });
        });
        vm.showEditForm();
    };

    /**
     * 做确认操作
     */
    vm.confirmFinished = function (index) {
    	if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    
    	    MainCtrl.blockUI({
        	    animate: true
        	});
            vm.confirmReco = vm.page.data.list[index];
            upida.post("purch/puReturns/updateStutas", vm.confirmReco).then(function (data) {
            	if(data == "failDelete")
            	{
            		 vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            		 vm.message = "退货单已经删除！";
                     $('#static').modal();
            	}
            	else if(data == "failConfirm")
            	{
            		 vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            		 vm.message = "退货单已经确认！";
                     $('#static').modal();
            	}
            	else if(data == "success")
            	{
            		// 重新查询数据
                    vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
                    // 初始化数据
                    vm.finished = {};
                    vm.hideEditForm();
                    // 提示信息
                    vm.message = "退货单确认成功！";
                    $('#static').modal();
            	}
            	else
            	{
            		vm.message = "系统内部错误，请刷新重试";
                    $('#static').modal();
            	}
            	vm.clicks = true;// tj 2017-09-07  用户是否多次点击
            });
    	}
    };
    
    // 设置新的时间
    vm.onTimeSetRedeliverDate = function (newDate, oldDate) {
    	vm.puReturnsDetail.redeliverDate = $filter('date')(newDate,'yyyy-MM-dd HH:mm:ss');
    };
    
    vm.addPuReturnsDetail=function(isAdd){
    	vm.puReturnsDetail = {};
    	vm.clearPrCacheData();
    	vm.purchasingDetail = {};
    	if (!vm.finished.supplier) {
    		vm.message = "请您先选择供应商，再添加退货明细。";
            $('#static').modal();
            return;
        }
    	vm.focus.detail += 1;
    	//标记是否为编辑明细
    	vm.edit.detail.ismodify = false;
    	vm.edit.isordercode = true;
    	vm.puReturnsDetail.redeliverDate = $filter('date')(new Date(),'yyyy-MM-dd HH:mm:ss');
    	vm.showDetailForm(isAdd)
    }
    vm.clearPrCacheData = function(){
    	vm.puReturnsDetail.purchasingDetail = {};
    	vm.puReturnsDetail.purchasingDetail.price = {};
    	vm.puReturnsDetail.prdorderDetail = {};
    	vm.puReturnsDetail.prdorderDetail.price = {};
    	vm.puReturnsDetail.prdorderDetail.price.price = "";
    	vm.puReturnsDetail.purchasingDetail.quantity = 0.000;
    	vm.puReturnsDetail.prdorderDetail.quantity = "";
    	vm.puReturnsDetail.rejectCause = "";
    	vm.puReturnsDetail.remark = "";
    };
    // 显示明细窗口
    vm.showDetailForm = function (isAdd, modfiy) {
        if (isAdd) {
        	if(vm.edit.detail.ismodify){
        		vm.edit.detail.btnDesc = "编辑";
        	}else{
        		vm.edit.detail.btnDesc = "添加";
        	}
            vm.edit.detail.isAdd = true;
        } else {
            if (modfiy) {
                vm.edit.detail.btnDesc = "编辑";
            } else {
                vm.edit.detail.btnDesc = "编辑添加";
            }
            vm.edit.detail.isAdd = false;
        }
        if (!vm.right.edit){
    		vm.edit.detail.btnDesc = "查看";
    	}
//        vm.puReturnsDetail.redeliverDate = new Date();
        vm.tabs.detailForm.show = true;
        $timeout(function () {
            vm.tabs.detailForm.active = true;
        });
    };
    // 隐藏明细窗口
    vm.hideDetailForm = function () {
        vm.tabs.detailForm.show = false;
        vm.tabs.detailForm.active = true;
        $timeout(function () {
            if (vm.tabs.editForm.show) {
                vm.tabs.editForm.active = true;
            } else {
                vm.tabs.seeDetail.active = true;
            }
        });
        vm.formScope.detailForm.$setPristine();
        vm.puReturnsDetail = {};
        vm.purchasingDetail = {};
    };
    /**
     * 子表添加方法
     */
    vm.submitModDetail = function (detail,form) {
    	form.$setDirty();
        if (form.$valid) {
        	MainCtrl.blockUI({
        	    animate: true
        	});
	        var tempData = {};
            vm.edit.ischangesup = true;
	        tempData.puReturns = {};
	        tempData.puReturns.recordId = "";//设置主表编号
	        tempData.amount = detail.amount;
	        tempData.rejectCause = detail.rejectCause;//数量
	        tempData.desireTreatment = detail.desireTreatment;//单价
	        tempData.purchasingNo = vm.purchasingDetail.purchasing.no;//采购单号
	        tempData.remark = detail.remark;
	        tempData.purchasingId = vm.purchasingDetail.purchasing.recordId ;
	        if(vm.puReturnsDetail.purchasingType == 1){
	        	if(vm.edit.detail.ismodify)
	        	{
	        		tempData.purchasingDetailId = detail.purchasingDetailId ;
	        	}
	        	else
	        	{
	        		tempData.purchasingDetailId = detail.purchasingDetail.recordId;
	        	}
	        	tempData.materialCustomerNo = detail.purchasingDetail.material.no ;
	        	tempData.quantity = detail.purchasingDetail.quantity;
	        	tempData.price = detail.purchasingDetail.price;
	        }else if(vm.puReturnsDetail.purchasingType == 2){
	        	if(vm.edit.detail.ismodify)
	        	{
	        		tempData.purchasingDetailId = detail.purchasingDetailId ;
	        	}
	        	else
	        	{
	        		tempData.purchasingDetailId = detail.prdorderDetail.recordId;
	        	}
	        	tempData.customerModel = detail.prdorderDetail.contractCraft.customerModel ;
	        	tempData.quantity = detail.prdorderDetail.quantity;
	        	tempData.price = detail.prdorderDetail.price.price;
	        	if(detail.prdorderDetail.contractDetail){
	        		tempData.orderNo = detail.prdorderDetail.contractDetail.contract.no;
	        	}
	        }
	        tempData.purchasingType = detail.purchasingType ;
	        //格式化时间
	        /*if(detail.redeliverDate instanceof Date){
	        	tempData.redeliverDate=CommonUtil.formatDate("yyyy-MM-dd hh:mm:ss",detail.redeliverDate);
	        }*/
	        tempData.redeliverDate = detail.redeliverDate;
	        if (vm.edit.detail.isAdd) {
	        	if(vm.edit.detail.ismodify){
	        		vm.message = "编辑退货明细成功！";
	        		vm.tempList[vm.edit.detail.modIndex] = tempData;
	        	}else{
	        		vm.message = "添加退货明细成功！";
	        		vm.tempList.push(tempData);
	        	}
	        	MainCtrl.unblockUI();
	        	$('#static').modal();
	        } else {
	            tempData.puReturns.recordId = vm.finished.recordId;
	            tempData.recordId=detail.recordId;
	            upida.post("purch/puReturns/savePuReturnsDetail", tempData).then(function (message) {
	                //是编辑状态就  重新查询子表数据
	                upida.post("purch/puReturns/findPuReturnsDetailByReturnsId", vm.finished).then(function (data) {
	                	MainCtrl.unblockUI();
	                    vm.tempList = data;
	                    vm.message = message;
	                    $('#static').modal();
	                });
	            });
	        }
	        vm.hideDetailForm();
        }    
    };
    /**
     * 显示删除详细
     */
    vm.delPuReturnsDetail = function (index) {
        vm.edit.detail.delIndex = index;
        vm.delDetailMsg = "您确定删除该退货单明细吗?";
        $('#staticDetailRemove').modal();
    };
    /**
     * 做删除详单操作
     */
    vm.doDelPuReturnsDetail = function () {
    	if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    
    	    MainCtrl.blockUI({
        	    animate: true
        	});
            //添加 状态操作子表list
            if (vm.edit.isAdd) {
                vm.tempList.splice(vm.edit.detail.delIndex, 1);
                if(vm.tempList.length>0){
                	vm.edit.ischangesup = true;
                }else{
                	vm.edit.ischangesup = false;
                }
                vm.edit.detail.delIndex = -1;
                vm.message = "删除退货明细成功！";
                $('#static').modal();
                MainCtrl.unblockUI();
            }else{
            	var delId = vm.tempList[vm.edit.detail.delIndex].recordId;
            	//根据编号删除详单
            	upida.post("purch/puReturns/deleteDetail/" + delId, null).then(function (message) {
            		// 重新查询数据
            		upida.post("purch/puReturns/findPuReturnsDetailByReturnsId", vm.finished).then(function (data) {
            			vm.tempList = data;
            			if(vm.tempList.length>0){
            				vm.edit.ischangesup = true;
            			}else{
            				vm.edit.ischangesup = false;
            			}
            			vm.edit.detail.delIndex = -1;
            			MainCtrl.unblockUI();
            			vm.message = message;
            			$('#static').modal();
            			vm.clicks = true;// tj 2017-09-07  用户是否多次点击
            		});
            	});
            }
    	}
    };
    //记忆明细编辑行
	vm.edit.detail.modIndex = 0;
	//标记是否为编辑明细
	vm.edit.detail.ismodify = false;
    // 单击显示明细编辑
    vm.modPuReturnsDetail = function (index) {
    	if (!vm.finished.supplier) {
    		vm.message = "请您先选择供应商，再编辑退货明细。";
            $('#static').modal();
            return;
        }
    	vm.focus.detail += 1;
    	//记忆明细编辑行
    	vm.edit.detail.modIndex = index;
    	//标记是否为编辑明细
    	vm.edit.detail.ismodify = true;
        // 显示detail
        vm.showDetailForm(vm.edit.isAdd, true);
        
    };
    
    // 显示编辑查看
    vm.showORModPuReturnsDetail = function(index){
    	vm.puReturnsDetail = angular.copy(vm.tempList[index]);
    	vm.puReturnsDetail.redeliverDate = $filter('date')(vm.puReturnsDetail.redeliverDate,'yyyy-MM-dd HH:mm:ss');
    	/**初始下拉框信息*/
    	vm.selectSupplierAndPuType();
    	vm.purchasingDetail.purchasing ={recordId: vm.puReturnsDetail.purchasingId, no: vm.puReturnsDetail.purchasingNo};
    	if (vm.puReturnsDetail.purchasingType==1){
    		vm.puReturnsDetail.purchasingDetail = {recordId: vm.puReturnsDetail.purchasingId,
    				quantity: vm.puReturnsDetail.quantity, price:vm.puReturnsDetail.price, material: {recordId: vm.puReturnsDetail.materialCustomerId, no: vm.puReturnsDetail.materialCustomerNo}};
    	}else {
    		vm.puReturnsDetail.prdorderDetail = {recordId: vm.puReturnsDetail.purchasingId, 
    				quantity: vm.puReturnsDetail.quantity, price:{price:vm.puReturnsDetail.price}, contractCraft: {recordId: vm.puReturnsDetail.materialCustomerId, customerModel: vm.puReturnsDetail.customerModel}};
    	}
    	vm.initPuTypeAndPurchasingId();
    	//希望处理方式为"补货时"不显示填补货日期
    	if(vm.puReturnsDetail.desireTreatment == 2){
        	vm.showRedeliverDate = false;
    	}
        vm.modPuReturnsDetail(index);
    };

    // 重置处理
//    vm.resetPuReturnsDetail = function () {
//        vm.edit.detail.isAdd = true;
//        vm.edit.detail.modIndex = -1;
//        vm.puReturnsDetail = {};
//        vm.purchasingDetail = {};
//        vm.edit.detail.btnDesc = "添加";
//    };
    
    //ycy 2017-01-05 数量改变验证
    vm.changeDate = function () 
    {
		if(!vm.puReturnsDetail.quantity)
		{
			vm.puReturnsDetail.quantity=0;
		}
		//退货出库
    	if(vm.puReturnsDetail.desireTreatment==2)
    	{
    		if(vm.puReturnsDetail.purchasingType == 1)
    		{
    			if(vm.puReturnsDetail.purchasingDetail)
    			{
    				//根据采购类型和原料采购单查询原料采购明细和关联的物料信息
            		upida.post("purch/puReturns/findPurchasingDetailInfoByType/" + vm.purchasingDetail.purchasing.recordId+"/"+vm.puReturnsDetail.desireTreatment).then(function (data) {
            			if(data.length>0)
            			{
            					//修改
                				if(vm.edit.detail.ismodify)
                	        	{
                					if (vm.edit.isAdd)
                					{
                						if(vm.tempList.length>0)
                    	    			{
                    	    				for (var i = 0; i < vm.tempList.length; i++) 
                    	    				{
                    	    					data[0].quantity=parseInt(data[0].quantity)-parseInt(vm.tempList[i].quantity);
                    	    				}
                    	    			}
                					}
                    				//修改的时候parseInt(vm.puReturnsDetail.prdorderDetail.quantity) > parseInt(data[0].quantity)+parseInt(vm.puReturnsDetail.quantity)
                					if((parseInt(data[0].quantity)+parseInt(vm.puReturnsDetail.quantity)) < parseInt(vm.puReturnsDetail.purchasingDetail.quantity))
                        			{
                        				vm.puReturnsDetail.purchasingDetail.quantity=vm.puReturnsDetail.quantity;
                            			vm.message = "退货数量不能大于采购订单数量！";
                            			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.purchasingDetail.price), '*', parseInt(vm.puReturnsDetail.purchasingDetail.quantity) , 4);
                            			$('#static').modal();
                            		}
                	        	}
                				else
                	        	{
            						if(vm.tempList.length>0)
                	    			{
                	    				for (var i = 0; i < vm.tempList.length; i++) 
                	    				{
            	    						if(vm.tempList[i].purchasingDetailId==data[0].recordId)
            	        					{
            	        						data[0].quantity=data[0].quantity-vm.tempList[i].quantity;
            	        					}
                	    				}
                	    			}
                					if(parseInt(data[0].quantity) < parseInt(vm.puReturnsDetail.purchasingDetail.quantity))
                        			{
                        				vm.puReturnsDetail.purchasingDetail.quantity=data[0].quantity;
                            			vm.message = "退货数量不能大于采购订单数量！";
                            			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.purchasingDetail.price), '*', parseFloat(vm.puReturnsDetail.purchasingDetail.quantity) , 4);
                            			$('#static').modal();
                            		}
                	        	}
            			}
            			else
            			{
            				//添加
            				if(vm.edit.detail.ismodify)
            	        	{
                				//修改的时候
            					if(parseInt(vm.puReturnsDetail.quantity) < parseInt(vm.puReturnsDetail.purchasingDetail.quantity))
                    			{
                    				vm.puReturnsDetail.purchasingDetail.quantity=vm.puReturnsDetail.quantity;
                        			vm.message = "退货数量不能大于采购订单数量！";
                        			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.purchasingDetail.price), '*', parseInt(vm.puReturnsDetail.purchasingDetail.quantity) , 4);
                        			$('#static').modal();
                        		}
            	        	}
            				else
            	        	{
            					if(parseInt(vm.puReturnsDetail.quantity) < parseInt(vm.puReturnsDetail.purchasingDetail.quantity))
                    			{
                    				vm.puReturnsDetail.purchasingDetail.quantity==vm.puReturnsDetail.quantity;
                        			vm.message = "退货数量不能大于采购订单数量！";
                        			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.purchasingDetail.price), '*', parseFloat(vm.puReturnsDetail.purchasingDetail.quantity) , 4);
                        			$('#static').modal();
                        		}
            	        	}
            			}
            		});
    			}
        	}
    		else if(vm.puReturnsDetail.purchasingType == 2 && vm.purchasingDetail.purchasing.recordId)
        	{
        		if(vm.puReturnsDetail.prdorderDetail){
        			//根据采购类型和原料采购单查询成品采购单明细和客户型号
            		upida.post("purch/puReturns/findPrdorderDetailInfoByType/" + vm.purchasingDetail.purchasing.recordId+"/"+vm.puReturnsDetail.desireTreatment).then(function (data) 
            		{
            			if(data.length>0)
            			{
                				if(vm.edit.detail.ismodify)
                	        	{
                					if (vm.edit.isAdd)
                					{
                    					if(vm.tempList.length>0)
                    	    			{
                    	    				for (var i = 0; i < vm.tempList.length; i++) 
                    	    				{
                    	    					data[0].quantity=parseInt(data[0].quantity)-parseInt(vm.tempList[i].quantity);
                    	    				}
                    	    			}
                					}
                    				//修改的时候
                					if(parseInt(vm.puReturnsDetail.prdorderDetail.quantity) > parseInt(data[0].quantity)+parseInt(vm.puReturnsDetail.quantity) )
                        			{
                        				vm.puReturnsDetail.prdorderDetail.quantity=vm.puReturnsDetail.quantity;
                            			vm.message = "退货数量不能大于采购订单数量！";
                            			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.prdorderDetail.price.price), '*', parseInt(vm.puReturnsDetail.prdorderDetail.quantity) , 4);
                            			$('#static').modal();
                            		}
                	        	}
                				else
                	        	{
                					if(vm.tempList.length>0)
                	    			{
                	    				for (var i = 0; i < vm.tempList.length; i++) 
                	    				{
            	    						if(vm.tempList[i].purchasingDetailId==data[0].recordId)
            	        					{
            	        						data[0].quantity=data[0].quantity-vm.tempList[i].quantity;
            	        					}
                	    				}
                	    			}
                					//添加
                					if(parseInt(data[0].quantity) < parseInt(vm.puReturnsDetail.prdorderDetail.quantity))
                        			{
                        				vm.puReturnsDetail.prdorderDetail.quantity=data[0].quantity;
                            			vm.message = "退货数量不能大于采购订单数量！";
                            			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.prdorderDetail.price.price), '*', parseInt(vm.puReturnsDetail.prdorderDetail.quantity) , 4);
                            			$('#static').modal();
                            		}
                	        	}
            			}
            			else
            			{
            				if(vm.edit.detail.ismodify)
            	        	{
                				//修改
            					if(parseInt(vm.puReturnsDetail.quantity) < parseInt(vm.puReturnsDetail.prdorderDetail.quantity))
                    			{
                    				vm.puReturnsDetail.prdorderDetail.quantity=vm.puReturnsDetail.quantity;
                        			vm.message = "退货数量不能大于采购订单数量！";
                        			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.prdorderDetail.price.price), '*', parseInt(vm.puReturnsDetail.prdorderDetail.quantity) , 4);
                        			$('#static').modal();
                        		}
            	        	}
            				else
            	        	{
            					//添加
            					if(parseInt(vm.puReturnsDetail.quantity) < parseInt(vm.puReturnsDetail.prdorderDetail.quantity))
                    			{
                    				vm.puReturnsDetail.prdorderDetail.quantity=vm.puReturnsDetail.quantity;
                        			vm.message = "退货数量不能大于采购订单数量！";
                        			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.prdorderDetail.price.price), '*', parseInt(vm.puReturnsDetail.prdorderDetail.quantity) , 4);
                        			$('#static').modal();
                        		}
            	        	}
            			}
            		});
        		}
        	}
    	}
    	if(vm.puReturnsDetail.desireTreatment==1)
        {
        	if(vm.puReturnsDetail.purchasingType == 1)
    		{
    			if(vm.puReturnsDetail.purchasingDetail)
    			{
    				//根据采购类型和原料采购单查询原料采购明细和关联的物料信息
            		upida.post("purch/puReturns/findPurchasingDetailInfoByType/" + vm.purchasingDetail.purchasing.recordId+"/"+vm.puReturnsDetail.desireTreatment).then(function (data) 
            		{
            			if(data.length>0)
            			{
            				if(vm.edit.detail.ismodify)
            	        	{
            					if (vm.edit.isAdd)
            					{
            						if(vm.tempList.length>0)
                	    			{
                	    				for (var i = 0; i < vm.tempList.length; i++) 
                	    				{
                	    					data[0].quantity=parseInt(data[0].quantity)-parseInt(vm.tempList[i].quantity);
                	    				}
                	    			}
            					}
                				//修改的时候
            					if(parseInt(data[0].quantity)+parseInt(vm.puReturnsDetail.quantity) < parseInt(vm.puReturnsDetail.purchasingDetail.quantity))
                    			{
                    				vm.puReturnsDetail.purchasingDetail.quantity=vm.puReturnsDetail.quantity;
                        			vm.message = "退货数量不能大于采购订单数量！";
                        			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.purchasingDetail.price), '*', parseInt(vm.puReturnsDetail.purchasingDetail.quantity) , 4);
                        			$('#static').modal();
                        		}
            	        	}
            				else
            	        	{
            					if(vm.tempList.length>0)
            	    			{
            	    				for (var i = 0; i < vm.tempList.length; i++) 
            	    				{
        	    						if(vm.tempList[i].purchasingDetailId==data[0].recordId)
        	        					{
        	        						data[0].quantity=data[0].quantity-vm.tempList[i].quantity;
        	        					}
            	    				}
            	    			}
            	        		if(parseInt(data[0].quantity) < parseInt(vm.puReturnsDetail.purchasingDetail.quantity))
                    			{
                    				vm.puReturnsDetail.purchasingDetail.quantity=data[0].quantity;
                        			vm.message = "补货数量不能大于退货数量！";
                        			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.purchasingDetail.price), '*', parseFloat(vm.puReturnsDetail.purchasingDetail.quantity) , 4);
                        			$('#static').modal();
                        		}
            	        	}
            			}
            			else
            			{
            				if(vm.edit.detail.ismodify)
            	        	{
                				//修改的时候
            					if(parseInt(vm.puReturnsDetail.quantity) < parseInt(vm.puReturnsDetail.purchasingDetail.quantity))
                    			{
                    				vm.puReturnsDetail.purchasingDetail.quantity=vm.puReturnsDetail.quantity;
                        			vm.message = "退货数量不能大于采购订单数量！";
                        			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.purchasingDetail.price), '*', parseInt(vm.puReturnsDetail.purchasingDetail.quantity) , 4);
                        			$('#static').modal();
                        		}
            	        	}
            				else
            				{
            	        		if(parseInt(data[0].quantity) < parseInt(vm.puReturnsDetail.purchasingDetail.quantity))
                    			{
                    				vm.puReturnsDetail.purchasingDetail.quantity=data[0].quantity;
                        			vm.message = "补货数量不能大于退货数量！";
                        			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.purchasingDetail.price), '*', parseFloat(vm.puReturnsDetail.purchasingDetail.quantity) , 4);
                        			$('#static').modal();
                        		}
            	        	}
            			}
            		});
    			}
        	}
    		else if(vm.puReturnsDetail.purchasingType == 2)
        	{
        		if(vm.puReturnsDetail.prdorderDetail)
        		{
        			//根据采购类型和原料采购单查询成品采购单明细和客户型号
            		upida.post("purch/puReturns/findPrdorderDetailInfoByType/" + vm.purchasingDetail.purchasing.recordId+"/"+vm.puReturnsDetail.desireTreatment).then(function (data) 
            		{
            			if(data.length>0)
            			{
            				if(vm.edit.detail.ismodify)
            	        	{
            						if(vm.tempList.length>0)
                	    			{
                	    				for (var i = 0; i < vm.tempList.length; i++) 
                	    				{
                	    					data[0].quantity=parseInt(data[0].quantity)-parseInt(vm.tempList[i].quantity);
                	    				}
                	    			}
                				//修改的时候
            					if(parseInt(data[0].quantity)+parseInt(vm.puReturnsDetail.quantity) < parseInt(vm.puReturnsDetail.prdorderDetail.quantity))
                    			{
                    				vm.puReturnsDetail.prdorderDetail.quantity=vm.puReturnsDetail.quantity;
                        			vm.message = "退货数量不能大于采购订单数量！";
                        			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.prdorderDetail.price.price), '*', parseInt(vm.puReturnsDetail.prdorderDetail.quantity) , 4);
                        			$('#static').modal();
                        		}
            	        	}
            				else
            				{
            					if(vm.tempList.length>0)
            	    			{
            	    				for (var i = 0; i < vm.tempList.length; i++) 
            	    				{
        	    						if(vm.tempList[i].purchasingDetailId==data[0].recordId)
        	        					{
        	        						data[0].quantity=data[0].quantity-vm.tempList[i].quantity;
        	        					}
            	    				}
            	    			}
            					if(parseInt(data[0].quantity) < parseInt(vm.puReturnsDetail.prdorderDetail.quantity))
                    			{
                    				vm.puReturnsDetail.prdorderDetail.quantity=data[0].quantity;
                        			vm.message = "补货数量不能大于退货订单数量！";
                        			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.prdorderDetail.price.price), '*', parseInt(vm.puReturnsDetail.prdorderDetail.quantity) , 4);
                        			$('#static').modal();
                        		}
            				}
            			}
            			else
            			{

            				if(vm.edit.detail.ismodify)
            	        	{
                				//修改的时候
            					if(parseInt(vm.puReturnsDetail.quantity) < (vm.puReturnsDetail.prdorderDetail.quantity))
                    			{
                    				vm.puReturnsDetail.prdorderDetail.quantity=vm.puReturnsDetail.quantity;
                        			vm.message = "退货数量不能大于采购订单数量！";
                        			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.prdorderDetail.price.price), '*', parseInt(vm.puReturnsDetail.prdorderDetail.quantity) , 4);
                        			$('#static').modal();
                        		}
            	        	}
            				else
            				{
            					if(parseInt(data[0].quantity) < parseInt(vm.puReturnsDetail.prdorderDetail.quantity))
                    			{
                    				vm.puReturnsDetail.prdorderDetail.quantity=data[0].quantity;
                        			vm.message = "补货数量不能大于退货订单数量！";
                        			vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.prdorderDetail.price.price), '*', parseInt(vm.puReturnsDetail.prdorderDetail.quantity) , 4);
                        			$('#static').modal();
                        		}
            				}
            			}
            		});
        		}
        	}
    	}
    }
    
    //查询总金额
    vm.setAmount = function () {
    	if(vm.puReturnsDetail.purchasingType == 1 && (!vm.puReturnsDetail.purchasingDetail || !(vm.puReturnsDetail.purchasingDetail.price && vm.puReturnsDetail.purchasingDetail.quantity))){
    		//如果单价或数量为空
    		return ;
    	}
    	if(vm.puReturnsDetail.purchasingType == 2 && (!vm.puReturnsDetail.prdorderDetail || !vm.puReturnsDetail.prdorderDetail.price || !(vm.puReturnsDetail.prdorderDetail.price.price && vm.puReturnsDetail.prdorderDetail.quantity))){
    		//如果单价或数量为空
    		return ;
    	}
    	if(!vm.puReturnsDetail.purchasingType){
    		vm.puReturnsDetail.purchasingDetail = null;
    		vm.puReturnsDetail.prdorderDetail = null;
    		vm.message = "请您先确认采购类型,和对应的采购单信息。";
            $('#static').modal();
    		return ;
    	}
    	if(vm.puReturnsDetail.purchasingType == 1 && !vm.puReturnsDetail.purchasingDetail.recordId){
    		vm.puReturnsDetail.purchasingDetail = null;
    		vm.message = "请您先确认对应的物料编号,再编辑对应的单价和数量。";
            $('#static').modal();
    		return ;
    	}
    	if(vm.puReturnsDetail.purchasingType == 2 && !vm.puReturnsDetail.prdorderDetail.recordId){
    		vm.puReturnsDetail.prdorderDetail = null;
    		vm.message = "请您先确认对应的客户型号,再编辑对应的单价和数量。";
            $('#static').modal();
    		return ;
    	}
    	if(vm.puReturnsDetail.purchasingType == 1 && vm.puReturnsDetail.purchasingDetail.price && vm.puReturnsDetail.purchasingDetail.quantity){
    		//原料处理
    		vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.purchasingDetail.price), '*', parseFloat(vm.puReturnsDetail.purchasingDetail.quantity) , 4);
    	}else if(vm.puReturnsDetail.purchasingType == 2 && vm.puReturnsDetail.prdorderDetail.price.price && vm.puReturnsDetail.prdorderDetail.quantity){
    		//成品处理
    		vm.puReturnsDetail.amount = CommonUtil.decimal(parseFloat(vm.puReturnsDetail.prdorderDetail.price.price), '*', parseInt(vm.puReturnsDetail.prdorderDetail.quantity) , 4);
    	}else{
			vm.puReturnsDetail.amount = 0;
		}
    };
    
    vm.showRedeliverDate = true;
    vm.selDesireTreatment = function(value){
    	if(value == 2){
    		vm.showRedeliverDate = false;
    	}else if(value == 1){
    		vm.showRedeliverDate = true;
    	}
    	vm.puReturnsDetail.purchasingType="";
    	vm.purchasingDetail.purchasing="";
    	vm.puReturnsDetail.rejectCause="";
    	vm.puReturnsDetail.remark="";
    	vm.puReturnsDetail.amount="";
    	vm.puReturnsDetail.purchasingDetail=[];
    	vm.puReturnsDetail.purchasingDetail=[];
    };
    
    //选择采购订单编码
    vm.selectSupplierAndPuType = function () {
        if (!vm.finished.supplier || !vm.puReturnsDetail.purchasingType) {
            return;
        }
        vm.edit.isordercode = false;
        //根据供应商查询采购订单编码
        upida.post("purch/puReturns/findPuNoBySupplier/" + vm.finished.supplier.recordId+"/"+vm.puReturnsDetail.purchasingType+"/"+vm.puReturnsDetail.desireTreatment).then(function (data) {
            vm.refer.purchasingNos = data;
        });
        //初始下拉列表数据
        vm.purchasingDetail.purchasing=null;
        vm.puReturnsDetail.purchasingDetail=null;
        vm.puReturnsDetail.prdorderDetail=null;
    };
    
    //初始采购订单编码
    vm.initPuTypeAndPurchasingId = function () {
    	if (!vm.finished.supplier || !vm.puReturnsDetail.purchasingType) {
            return;
        }
    	if(vm.puReturnsDetail.purchasingType == 1){
    		//根据采购类型和原料采购单查询原料采购明细和关联的物料信息
    		upida.post("purch/puReturns/findPurchasingDetailInfoByType/" + vm.puReturnsDetail.purchasingId+"/"+vm.puReturnsDetail.desireTreatment).then(function (data) {
    			vm.refer.purchasingDetailList = data;
    		});
    	}else if(vm.puReturnsDetail.purchasingType == 2){
    		//根据采购类型和原料采购单查询成品采购单明细和客户型号
    		upida.post("purch/puReturns/findPrdorderDetailInfoByType/" + vm.puReturnsDetail.purchasingId+"/"+vm.puReturnsDetail.desireTreatment).then(function (data) {
    			vm.refer.prdorderDetailList = data;
    		});
    	}
    };
    
    //选择采购订单编码
    vm.selectPuTypeAndPurchasingId = function () {
    	if (!vm.finished.supplier || !vm.puReturnsDetail.purchasingType) {
            return;
        }
		//重置客户型号或物料编号
		vm.puReturnsDetail.purchasingDetail=null;
		vm.puReturnsDetail.prdorderDetail=null;
    	if(vm.puReturnsDetail.purchasingType == 1 && vm.purchasingDetail.purchasing.recordId){
    		//根据采购类型和原料采购单查询原料采购明细和关联的物料信息
    		upida.post("purch/puReturns/findPurchasingDetailInfoByType/" + vm.purchasingDetail.purchasing.recordId+"/"+vm.puReturnsDetail.desireTreatment).then(function (data) {
    			if(data.length>0){
    				if(vm.edit.isAdd){
        				if(vm.tempList.length>0)
            			{
            				for (var i = 0; i < vm.tempList.length; i++) 
            				{
            					for (var k = 0; k < data.length; k++) 
            					{
            						if(vm.tempList[i].purchasingDetailId==data[k].recordId)
                					{
                						data[k].quantity=data[k].quantity-vm.tempList[i].quantity;
                					}
        						}
            				}
            			}
            			for (var i = 0; i < data.length; i++){
        					if(data[i].quantity!=0)
        					{
        						vm.refer.purchasingDetailList = data;
        					}else{
        						vm.refer.purchasingDetailList=[];
        					}
        				}
        			}
        			else
        			{
        				vm.refer.purchasingDetailList = data;
        			}
    			}else{
    				vm.refer.purchasingDetailList=[];
    			}
    		});
    	}else if(vm.puReturnsDetail.purchasingType == 2 && vm.purchasingDetail.purchasing.recordId){
    		//根据采购类型和原料采购单查询成品采购单明细和客户型号
    		upida.post("purch/puReturns/findPrdorderDetailInfoByType/" + vm.purchasingDetail.purchasing.recordId+"/"+vm.puReturnsDetail.desireTreatment).then(function (data) {
    			if(data.length>0){
    				if(vm.edit.isAdd)
        			{
        				if(vm.tempList.length>0)
            			{
            				for (var i = 0; i < vm.tempList.length; i++) 
            				{
            					for (var k = 0; k < data.length; k++) 
            					{
            						if(vm.tempList[i].purchasingDetailId==data[k].recordId)
                					{
                						data[k].quantity=data[k].quantity-vm.tempList[i].quantity;
                					}
        						}
            				}
            			}
            			for (var i = 0; i < data.length; i++) 
            			{
        					if(data[i].quantity!=0)
        					{
        						vm.refer.prdorderDetailList = data;
        					}else{
        						vm.refer.prdorderDetailList=[];
        					}
        				}
        			}
        			else
        			{
        				vm.refer.prdorderDetailList = data;
        			}
    			}else{
    				vm.refer.prdorderDetailList=[];
    			}
    		});
    	}
    };
    
    //
    vm.addReject = function()
    {
    	vm.finished = {};
    	vm.modPuReturns();
    }
    
    vm.selectCustomer = function(row)
    {
    	vm.custShowName = row.name;
        /*vm.rawmaterial.payWay = row.payWay;
        vm.rawmaterial.taxDescript = row.taxDescript;
        vm.rawmaterial.qualityStd = row.qualityStd;
        vm.rawmaterial.paycause = row.paycause;
        vm.rawmaterial.deliveryPlace = row.address;*/
    	upida.post("purch/puReturns/getContractList", {"customerId":row.recordId}).then(function (data) {
    		vm.contractList = data; // 合同信息
        });
    	
    }
    
    vm.selectContract = function(row)
    {
    	upida.post("purch/puReturns/getContractDetailList", {"purchasingId":row.recordId}).then(function (data) {
    		vm.contractDetailList = data; // 合同信息
        });
    }
    
    vm.selectMaterial = function(row)
    {
    	vm.finished.quantity = row.quantity;
    	vm.finished.price = row.price;
    	vm.finished.amount = row.amount;
    	vm.finished.materialId = row.materialId;
    	vm.finished.materialContractDetail = {};
    	vm.finished.materialContractDetail.quantity = row.quantity;
    	vm.finished.materialContractDetail.replenishQty = row.replenishQty;
    }
    
    vm.changeQuantity =function()
    {
    	//客诉数量，小于可客诉数量
    	let canReplenishQty = vm.finished.materialContractDetail.quantity;
    	if(vm.finished.materialContractDetail.replenishQty)
    	{
    		canReplenishQty = canReplenishQty - Number(vm.finished.materialContractDetail.replenishQty);
    	}
		if(Number(vm.finished.quantity)>canReplenishQty)
		{
			vm.finished.quantity = canReplenishQty;
			vm.message = "退货数量不能大于合同可客诉数量！"+canReplenishQty;
            $('#static').modal();
		}
    	
    	if(vm.finished.price&&vm.finished.quantity)
    	{
    		vm.finished.amount = Number(vm.finished.price)*Number(vm.finished.quantity);
    	}
    	if(vm.finished.replenishQty&&vm.finished.quantity&&vm.finished.replenishQty>vm.finished.quantity)
    	{
    		vm.finished.replenishQty = vm.finished.quantity;
    	}
    }
    
    vm.updateQuantity = function()
    {
    	let canReplenishQty = vm.finished.materialContractDetail.quantity;
    	if(vm.finished.materialContractDetail.replenishQty)
    	{
    		canReplenishQty = canReplenishQty - Number(vm.finished.materialContractDetail.replenishQty);
    	}
    	if(Number(vm.finished.quantity)>canReplenishQty)
    	{
    		vm.finished.quantity = canReplenishQty;
    		vm.message = "退货数量不能大于合同可客诉数量！"+canReplenishQty;
            $('#static').modal();    		
    	}
    	
    	if(Number(vm.finished.replenishQty)>Number(vm.finished.quantity))
    	{
    		vm.finished.replenishQty = Number(vm.finished.quantity);
    		vm.message = "补货数量不能大于退货数量！";
            $('#static').modal();    		
    	}
    	
    }
    
    vm.changeReplenishQty = function()
    {
    	if(!(Number(vm.finished.replenishQty)<=Number(vm.finished.quantity)))
    	{
    		vm.finished.replenishQty = vm.finished.quantity;
    		vm.message = "补货数量不能大于退货数量！";
			$('#static').modal();
    	}
    }
    
    vm.delRejectApplication = function(row)
    {
    	vm.edit.delIndex = index;
        vm.edit.delMsg = "您确定要删除客诉单\""+ vm.page.ra.data.list[index].no +"\"吗?";
        $('#staticRemove').modal();
    }
    
    /**
     * 删除
     */
    vm.doDelCard = function () {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
        var id = vm.page.data.list[vm.edit.delIndex].recordId;
        upida.post("purch/materialReject/delete/" + id).then(function (data) {
            // 重新查询数据
        	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            vm.edit.delIndex = -1;
            vm.message = data.message;
            $('#static').modal();
        });
    };
    
    vm.audRejectApplication = function(index)
    {
    	if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    MainCtrl.blockUI({
        		//进度条展示
        		animate: true,
        	});	
            upida.post("purch/materialReject/audit", vm.page.data.list[index]).then(function (data) {
                // 重新查询数据
                vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
                // 初始化数据
                vm.finished = {};
                // 提示信息
                vm.message = data;
                $('#static').modal();
                vm.clicks = true;// tj 2017-09-07  用户是否多次点击
            });
    	}
    }
    
    /**
     * 打印
     */
    vm.printReturns = function (printForm) {
        printForm.commit();
    };
    // 加载权限
    function loadRight() {
    	MainCtrl.blockUI({
    	    //进度条展示
    	    animate: true,
    	});	
    	vm.queryAll = CommonUtil.dataRangeIsAll("10703", BaseUtil.getMenuList());
        upida.get("common/rightall?prefix=purch:returns").then(function(data){
            vm.right.manage = data.manage;
            vm.right.edit = data.edit;
        	vm.right.view = data.view;
        	vm.page.pageNo = 1;
        	//订单日期
            if (vm.time.start) {
            	vm.page.condition.push({
                    name: vm.query.sentTimeStartQr.name,
                    value: vm.time.start.valueOf()
                });
                vm.query.sentTimeStartQr.value = vm.time.start.valueOf();
            }
            //订单日期
            if (vm.time.end) {
            	vm.page.condition.push({
                    name: vm.query.sentTimeEndQr.name,
                    value: vm.time.end.valueOf()
                });
                vm.query.sentTimeEndQr.value = vm.time.end.valueOf();
            }
            // 初始化第一页，条件为空
            vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            loadItemsData();
        });
    }
    //加载下拉框数据
    function loadItemsData() {
    	MainCtrl.blockUI({
    	    animate: true
    	});
        // 加载货币类型
        upida.get("purch/puReturns/load/data?queryAll="+vm.queryAll).then(function (result) {
            vm.refer.currencyTypeList = result.data.currencyTypeList;// 货币类型
            vm.employeeList = result.data.employeeList;// 查询公司的所有职员
            vm.supplierList = result.data.supplierList;// 查询公司的供应商
            vm.customerList = result.data.customerList;
            MainCtrl.unblockUI();
        });
    }
    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
    });
}]);
