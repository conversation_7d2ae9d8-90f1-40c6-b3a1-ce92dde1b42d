<%@ page contentType="text/html;charset=UTF-8"%>
<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top" ng-if="kyb.right && kyb.right.view">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">工作台</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="crm.quotation">阶段管理</a>
        </li>
    </ul>
    <div class="page-toolbar">
        <button class="btn default btn-fit-height pull-right" ng-click="help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
    </div>
</div>
<!-- END 导航-->

<tabset class="tabset-margin-top" ng-if="kyb.right && kyb.right.view">
    <!-- BEGIN 报价单列表 -->
    <tab heading="准备阶段" active="kyb.tabs.viewForm.active">
        <div id="listStep6" class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">阶段类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="kyb.stageKind" class="form-control" disable-auto-validate="true" >
                                        <option value= "0">准备阶段</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="kyb.statusA" class="form-control" disable-auto-validate="true" >
                                        <option value="">所有</option>
                                        <option value= "1001">未确认</option>
                                        <option value= "1002">已确认</option>
                                        <option value= "60001">审批中</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">批次号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="kyb.batchNoA"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">名字：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="kyb.nameA"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">时间：</label>
                                <div class="col-sm-7 col-md-8">
                                    <div class="input-prepend input-group">
                                        <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                        <input type="text" class="form-control" disable-auto-validate="true"
                                               ng-blur="kyb.initDate(kyb.time)"
                                               kyb-daterange
                                               kyb-daterange-options="kyb.rangeOptions"
                                               ng-model="kyb.time"
                                               placeholder="请选择时间段">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default btn-default-width pull-right"
                                        ng-click="kyb.stageWorkData()">
                                    <i class="fa fa-search"></i> 查&nbsp;询
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="portlet light bordered" ng-if="kyb.stageKind == 0">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">准备阶段列表</div>
<%--                <div class="actions">
                    <div class="portlet-input input-inline input-small">
                        <button type="button" class="btn green btn-default-width" ng-click="kyb.allShowFlag()"> <i class="fa fa-adjust font-blue"></i>
                            <span ng-if=" kyb.showFlag == 1 ">展开</span>
                            <span ng-if=" !kyb.showFlag ">隐藏</span>
                        </button>
                    </div>
                    <div class="portlet-input input-inline input-small">
                        <button type="button" class="btn green btn-default-width" ng-click="kyb.prepareConfirmNoti()">确认</button>
                    </div>
                </div>--%>
            </div>
            <div class="portlet-body">
                <div class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
                        <tr class="heading">
                            <th width="5%">
                                <checkbox ng-model="kyb.allPageChecked" value="1" name="test" ng-click="kyb.selectAllData()"></checkbox>
                            </th>
                            <th width="8%">投料单号</th>
                            <th width="8%">生产编号</th>
                            <th width="5%">大板数</th>
<%--                            <th>物料(库存)</th>--%>
                            <th width="8%">开料信息</th>

                            <th width="8%">投料单确认日期</th>
<%--                            <th>最早交货日期</th>--%>
                            <th  width="8%">工程尺寸</th>
                            <%--<th  width="5%">板材厚度</th>--%>
                            <th  width="5%">阻焊类型</th>
                            <%--<th  width="5%">PCB类型</th>--%>
                            <%--<th  width="5%">覆铜要求</th>--%>
                            <th  width="5%">镀层处理</th>
                            <th  width="5%">测试要求</th>

                            <th  width="5%">板面字符</th>
                            <th  width="8%">市场材料费用</th>
                            <th  width="5%">配料费用</th>
                            <th  width="5%">盈亏金额</th>
                            <%--<th>覆铜板材</th>--%>
                            <th  width="20%">操作</th>

                        </tr>
                        <tr class="heading">
                            <th></th>
                            <th colspan="1">名字</th>
                            <th colspan="1">批次号</th>
                            <th colspan="2">大板数</th>
                            <th colspan="1">使用PCS</th>
                            <th colspan="1">剩余PCS</th>
                            <th colspan="2">类型</th>
                            <th colspan="2">状态</th>
                            <th colspan="1">配料状态</th>
                            <th colspan="1">配料费用</th>
                            <th colspan="1">负责人</th>
                            <th colspan="1">确认时间</th>
                            <%--<th colspan="1">操作</th>--%>
                        </tr>
                        <tr>
                            <td></td>
                            <td><input type="text" class="form-control" ng-model="kyb.noSearchA"></td>
                            <td><input type="text" class="form-control" ng-model="kyb.craftNoSearchA"></td>
                            <td><input type="text" class="form-control" ng-model="kyb.boardQuantitySearchA"></td>
                            <%--<td><input type="text" class="form-control" ng-model="kyb.materialNoSearchA"></td>--%>
                            <td><input type="text" class="form-control" ng-model="kyb.cardCuttingSearchA"></td>

                            <td><input type="text" class="form-control" ng-model="kyb.confirmDateSearchA"></td>
<%--                            <td><input type="text" class="form-control" ng-model="kyb.proTimeSearchA"></td>--%>
                            <td><input type="text" class="form-control" ng-model="kyb.setSizeSearchA"></td>
                            <%--<td><input type="text" class="form-control" ng-model="kyb.boardThicknessSearchA"></td>--%>
                            <td><input type="text" class="form-control" ng-model="kyb.solderMaskTypeSearchA"></td>
                            <%--<td><input type="text" class="form-control" ng-model="kyb.boardLevelSearchA"></td>--%>
                            <%--<td><input type="text" class="form-control" ng-model="kyb.copperCladThicknessSearchA"></td>--%>
                            <td><input type="text" class="form-control" ng-model="kyb.surfaceProcessSearchA"></td>
                            <td><input type="text" class="form-control" ng-model="kyb.testMethodSearchA"></td>

                            <td><input type="text" class="form-control" ng-model="kyb.characterTypeSearchA"></td>

                            <td><input type="text" class="form-control" ng-model="kyb.materialFeeSearchA"></td>
                            <td><input type="text" class="form-control" ng-model="kyb.batchedCostSearchA"></td>
                            <td><input type="text" class="form-control" ng-model="kyb.profitAmountSearchA"></td>

                            <%--<td><input type="text" class="form-control" ng-model="kyb.materialTypeSearchA"></td>--%>
                            <td></td>
                        </tr>
                        </thead>
                        <tbody ng-repeat="row in kyb.stageDataList  |
                        filter:{
                            'no':kyb.noSearchA,
                            'craftNo':kyb.craftNoSearchA,
                            'boardQuantity':kyb.boardQuantitySearchA,
                            'cardCutting':kyb.cardCuttingSearchA,
                            'confirmDateStr':kyb.confirmDateSearchA,
                            'setSize':kyb.setSizeSearchA,
                            'solderMaskType':kyb.solderMaskTypeSearchA,
                            'surfaceProcess':kyb.surfaceProcessSearchA,
                            'testMethod':kyb.testMethodSearchA,
                            'characterType':kyb.characterTypeSearchA,
                            'materialFee':kyb.materialFeeSearchA,
                            'batchedCost':kyb.batchedCostSearchA,
                            'profitAmount':kyb.profitAmountSearchA
                        } track by $index">
                        <tr ng-style="row.profitAmount < 0 ? {'background-color': 'red'} : (row.profitAmount > 0 ? {'background-color': '#73c0ff'} : {})">
                            <td>
                                <%--<checkbox ng-model="row.checked" ng-change="kyb.getPrepareListA(row)"></checkbox>--%>
                            </td>
                            <td ng-bind="row.no"></td>
                            <td ng-bind="row.craftNo"></td>
                            <td ng-bind="row.boardQuantity"></td>
                            <%--<td>
                                <span ng-repeat="matNo in row.matNoStrList track by $index">
                                    <span>{{matNo}};</span><br>
                                </span>
                            </td>--%>
                            <td ng-bind="row.cardCutting"></td>


                            <td ng-bind="row.confirmDateStr"></td>
                            <td ng-bind="row.size"></td>
                            <%--<td ng-bind="row.boardThickness"></td>--%>
                            <td ng-bind="row.solderMaskType"></td>
                            <%--<td ng-bind="row.boardLevel"></td>--%>
                            <%--<td ng-bind="row.copperCladThickness"></td>--%>
                            <td ng-bind="row.surfaceProcess"></td>
                            <td ng-bind="row.testMethod"></td>

                            <td ng-bind="row.characterType"></td>

                            <td ng-bind="row.materialFee"></td>
                            <td ng-bind="row.batchedCost"></td>
                            <td ng-bind="row.profitAmount" ng-class="{'red-text': row.profitAmount < 0}"></td>
                            <%--<td ng-bind="row.materialType"></td>--%>
                            <td>
                                <%--<a href="javascript:void(0);"  class="btn btn-xs btn-default" ng-click="kyb.fullInverseSelection(row)">
                                    <i class="fa fa-check font-green"></i>全反选</a>&nbsp;--%>
                                    <a href="javascript:void(0);"  class="btn btn-xs btn-default" ng-click="kyb.dosingOpen(row)">
                                    <i class="fa fa-check font-green"></i>配料</a>&nbsp;
                                <a href="javascript:void(0);"  class="btn btn-xs btn-default" ng-click="kyb.cancleDosingOpen(row)">
                                    <i class="fa fa-check font-green"></i>取消配料</a>&nbsp;
                                <a href="javascript:void(0);"  class="btn btn-xs btn-default" ng-if="row.confirmFlag" ng-click="kyb.batchConfirmButtonOpen(row,1)">
                                    <i class="fa fa-check font-green"></i>确认</a>&nbsp;
                                <a href="javascript:void(0);"  class="btn btn-xs btn-default" ng-click="kyb.batchConfirmButtonOpen(row,2)">
                                    <i class="fa fa-check font-green"></i>取消确认</a>&nbsp;
                                <a href="javascript:void(0)" class="btn  btn-xs btn-default" ng-click="kyb.showFeed(row)" >
                                    <i class="fa fa-adjust font-blue"></i>
                                    <span ng-if=" row.showFlag == 1 ">展开</span>
                                    <span ng-if=" !row.showFlag ">隐藏</span>
                                </a>&nbsp;
                            </td>
                        </tr>
                        <tr ng-if="!row.showFlag" ng-repeat="item in row.batchDetailSetupList track by $index">
                            <td>
                                <checkbox ng-model="item.checked"></checkbox>
                            </td>
                            <td colspan="1" ng-bind="item.name"></td>
                            <td colspan="1" ng-bind="item.batchNo"></td>
                            <td colspan="2" ng-bind="item.qtyT"></td>
                            <td colspan="1" ng-bind="item.usePcs"></td>
                            <td colspan="1" ng-bind="item.remainPcs"></td>
                            <td colspan="2" ng-bind="item.typeStr"></td>
                            <td colspan="2" ng-bind="item.statusStr"></td>
                            <td colspan="1">
                                <span ng-if="item.status == 1001 && item.name == '板材确认' && item.materialOccupyList && item.materialOccupyList.length > 0">已配料</span>
                                <span ng-if="item.status == 1001 && item.name == '板材确认' && (!item.materialOccupyList || item.materialOccupyList.length == 0)">未配料</span>
                                <span ng-if="item.status == 1001 && item.name != '板材确认'">无需配料</span>
                            </td>
                            <td colspan="1" >{{item.batchedCost}}</td>
                            <td colspan="1" ng-bind="item.principalName"></td>
                            <td colspan="1" ng-bind="item.confirmDateStr"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </tab>
</tabset>
    <div class="row">
        <div class="col-md-12">
            <div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                            <h4 class="modal-title"><span>提示</span></h4>
                        </div>
                        <div class="modal-body">
                            <p><span ng-bind="kyb.message"></span></p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
