<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.oa.dao.Oa_reportDao">
	<select id="getReportList" resultType="Oa_report">
		SELECT
		a.recordId,
		b.`name`,
		a.content,
		a.contentTwo,
		a.contentThree,
		(
		CASE a.flag
		WHEN 1 THEN
		DATE(a.`day`)
		WHEN 2 THEN
		YEARWEEK(DATE(a.day),7)
		WHEN 3 THEN
		DATE_FORMAT(a.day,'%Y%m')
		WHEN 4 THEN
		DATE(a.`day`)
		END
		) AS `day`,
		a.employee AS "employeeId",
		a.createdDate,
		a.flag,
		a.generateTaskContent AS "generateTaskContent",
		d.name AS "superName",
		e.name AS "superiorsName",
		(SELECT GROUP_CONCAT( departmentName ) FROM oa_department_relation WHERE FIND_IN_SET( recordId, substring( a.architectureId, 2, 30 ) ) ) AS "departmentName",
		(SELECT
		GROUP_CONCAT(b.`name`)
		FROM
		oa_daily_share aa LEFT JOIN oa_md_employee b ON b.recordId = aa.share
		WHERE
		aa.activeFlag = 1
		AND aa.daily = a.recordId
		LIMIT 1) AS "shareName"
		FROM oa_daily a
		LEFT JOIN oa_md_employee b ON b.recordId = a.employee
		LEFT JOIN oa_md_employee d ON d.recordId = b.superEmpId
		LEFT JOIN oa_md_employee e ON e.recordId = d.superEmpId AND e.`name` != d.`name`
		WHERE a.activeFlag = 1 AND a.content IS NOT NULL
		<if test="status != null and status !=''">
			AND (
			a.recordId IN (
			SELECT
			daily
			FROM oa_daily_share
			<where>
				`share` = #{employeeId} AND status = #{status}
			</where>
			)
			OR FIND_IN_SET(
			a.employee,
			(
			SELECT
			CASE
			WHEN aa.recordId2 IS NOT NULL
			AND aa.recordId3 IS NOT NULL THEN
			CONCAT(
			aa.recordId1,
			',',
			aa.recordId2,
			',',
			aa.recordId3
			)
			WHEN aa.recordId2 IS NOT NULL THEN
			CONCAT(
			aa.recordId1,
			',',
			aa.recordId2
			)
			WHEN aa.recordId3 IS NOT NULL THEN
			CONCAT(
			aa.recordId1,
			',',
			aa.recordId3
			)
			ELSE
			NULL
			END
			FROM
			(
			SELECT
			a.recordId AS "recordId1",
			GROUP_CONCAT(DISTINCT b.recordId) AS "recordId2",
			GROUP_CONCAT(DISTINCT c.recordId) AS "recordId3"
			FROM
			oa_md_employee a
			LEFT JOIN oa_md_employee b ON b.superEmpId = a.recordId  AND b.workStatus = 1 AND b.activeFlag = 1
			LEFT JOIN oa_md_employee c ON c.superEmpId = b.recordId  AND c.workStatus = 1 AND c.activeFlag = 1
			WHERE
			a.recordId = #{employeeId}
			) aa
			)
			)
			)
			AND a.status = #{status}
		</if>
		<if test="archEmployeeId != null and archEmployeeId !=''">
			AND a.employee = #{archEmployeeId}
		</if>
		<if test="status == null || status ==''">
			AND a.employee = #{employeeId}
		</if>
		<if test="flag != null and flag !='' and flag == 1">
			AND a.flag = 1
		</if>
		<if test="flag != null and flag !='' and flag == 2">
			AND a.flag = 2
		</if>
		<if test="flag != null and flag !='' and flag == 3">
			AND a.flag = 3
		</if>
		<if test="condition != null and condition !=''">
			AND CONCAT(
			IFNULL(a.content, ""),
			IFNULL(a.contentTwo, ""),
			IFNULL(a.contentThree, "")
			)
			LIKE CONCAT('%', REPLACE(#{condition}," ",""), '%')
		</if>
		<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
			AND a.createdBy = #{createdBy.recordId}
		</if>
		<if test="recordId != null and recordId != ''">
			AND a.recordId = #{recordId}
		</if>
		<if test="startTime != null and startTime != ''">
			AND DATE(a.createdDate) <![CDATA[>=]]> DATE(#{startTime})
		</if>
		<if test="endTime != null and endTime != ''">
			AND DATE(a.createdDate) <![CDATA[<=]]> DATE(#{endTime})
		</if>
		<if test="architectureId != null and architectureId != ''">
			AND a.architectureId = #{architectureId}
		</if>
		<if test="startTimeT != null and startTimeT != ''">
			AND DATE(a.createdDate) <![CDATA[>=]]> DATE(#{startTimeT})
		</if>
		<if test="endTimeT != null and endTimeT != ''">
			AND DATE(a.createdDate) <![CDATA[<=]]> DATE(#{endTimeT})
		</if>
		ORDER BY a.createdDate DESC
	</select>
	
	<select id="getSuperEmpId" resultType="String">
		SELECT superEmpId FROM oa_md_employee WHERE recordId = #{employeeId}
	</select>
	
	<select id="getReport" resultType="Oa_report">
		SELECT
			a.recordId,
			b.`name`,
			a.content,
			a.contentTwo,
			a.contentThree,
			(
				CASE a.flag
				WHEN 1 THEN
					DATE(a.`day`)
				WHEN 2 THEN
					YEARWEEK(DATE(a.day),7)
				WHEN 3 THEN
					DATE_FORMAT(a.day,'%Y%m')
				END
			) AS `day`,
			a.createdDate,
			a.flag,
			d.name AS "superName",
			b.recordId AS employeeId,
		    e.name AS "superiorsName",
		    a.architectureId AS "architectureId",
		    a.generateTaskContent AS "generateTaskContent",
			(
				SELECT
					GROUP_CONCAT(bb.`name`)
				FROM
					oa_daily_share aa
				LEFT JOIN oa_md_employee bb ON bb.recordId = aa.`share`
				WHERE
					aa.daily = a.recordId
				  AND aa.activeFlag = 1
				  AND bb.workStatus = 1
				  AND bb.activeFlag = 1
				  AND bb.userId IS NOT NULL
			) AS "shares"
		FROM oa_daily a
		LEFT JOIN oa_md_employee b ON b.recordId = a.employee
		LEFT JOIN oa_md_employee d ON d.recordId = b.superEmpId
		LEFT JOIN oa_md_employee e ON e.recordId = d.superEmpId AND e.`name` != d.`name`
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="getReportAttach" resultType="Hr_KybAttachments">
		SELECT 
			recordId,
			orgFileName AS name,
			fileUrl,
			type
		FROM oa_daily_attachment 
		WHERE daily = #{recordId} 
		AND activeFlag = 1
	</select>
	
	<select id="getReportShare" resultType="Hr_Employee">
		SELECT
			a.`share` AS "recordId",
			b.`name` AS name
		FROM
			oa_daily_share a
		LEFT JOIN oa_md_employee b ON b.recordId = a.`share`
		WHERE
			a.daily = #{recordId}
		AND a.activeFlag = 1
		AND b.workStatus = 1
		AND b.activeFlag = 1
		AND b.userId IS NOT NULL
	</select>
	
	<select id="getReportComment" resultType="Oa_comment">
		SELECT
			a.recordId,
			a.createdBy AS "createdBy.recordId",
			b.`name`,
			a.createdDate AS "createdDate",
			a.content,
			a.commentId,
			a.activeFlag,
			a.createdBy,
			a.createdDate,
			b.`name`,
			d.`name` AS "replyName",
			a.commentFaId,
			e.fileUrl AS "employee.fileUrl",
			e.defaultImg AS "employee.defaultImg",
			b.empChar AS "employee.empChar"
		FROM oa_audit_new_comment a
		LEFT JOIN oa_md_employee b ON b.recordId = a.createdBy
		LEFT JOIN oa_audit_new_comment c ON c.recordId = a.commentId
		LEFT JOIN oa_md_employee d ON d.recordId = c.createdBy
		LEFT JOIN icloud_sm_user e ON e.recordId = b.userId
		WHERE a.dailyId = #{recordId}
		AND a.activeFlag = 1
	</select>
	
	<insert id="saveReport" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO oa_daily (
			employee,
			content,
			contentTwo,
			contentThree,
			status,
			day,
			createdBy,
			createdDate,
			flag,
			activeFlag,
			architectureId,
			generateTaskContent,
			year,
			month,
			week
		)VALUES	(
			#{employeeId},
			#{content},
			#{contentTwo},
			#{contentThree},
			1,
			#{day},
			#{employeeId},
			NOW(),
			#{flag},
			1,
			#{architectureId},
			#{generateTaskContent},
			#{year},
			#{month},
			#{week}
		)
	</insert>
	
	<insert id="uploadFile" useGeneratedKeys="true" keyProperty="recordId"  parameterType="Hr_KybAttachments">
    	INSERT INTO oa_daily_attachment(
    		daily,
    		createdDate,
    		orgFileName,
    		realFileName,
    		fileUrl,
    		type
    	)VALUES (
    		#{dailyId},
    		now(),
    		#{orgFileName},
    		#{realFileName},
    		#{fileUrl},
    		#{type}
    	)	
    </insert>
    
    <delete id="delFile">
    	DELETE FROM oa_daily_attachment WHERE recordId = #{recordId}
    </delete>
	
	<update id="deleteReport" >
    	UPDATE oa_daily SET 
    		activeFlag = 0
    	WHERE recordId = #{recordId}
    </update>
	
	<update id="updateReport" >
    	UPDATE oa_daily SET 
    		content = #{content},
    		contentTwo = #{contentTwo},
    		contentThree = #{contentThree},
    		lastUpdateDate = NOW(),
    		architectureId = #{architectureId}
    	WHERE recordId = #{recordId}
    </update>
    
    <delete id="deleteReportShares">
    	DELETE FROM oa_daily_share WHERE daily = #{recordId}
    </delete>
    
    <insert id="saveReportShares" parameterType="java.util.List">
		INSERT INTO oa_daily_share(
			daily,
			share,
			activeFlag,
			createdDate,
			status
    	)VALUES 
    	<foreach collection="list" item="item" index= "index" separator =",">
    	(
	        #{item.dataId},
			#{item.recordId},
			1,
			NOW(),
			1
   	 	)
	    </foreach>
	</insert>
	
	<!-- zjn 2019-11-04 修改报告的状态 -->
	<update id="updateStatus">
		UPDATE oa_daily a
		INNER JOIN
		(
			SELECT
				aa.recordId AS "recordId"
			FROM oa_daily aa
			LEFT JOIN oa_md_employee ome ON ome.recordId = aa.employee
			LEFT JOIN oa_md_employee c ON c.recordId = ome.superEmpId
			WHERE aa.recordId = #{recordId} AND (ome.superEmpId = #{loginEmpId} OR c.superEmpId = #{loginEmpId})
		) bb ON bb.recordId = a.recordId
		SET a.`status` = 2
	</update>
	
    <!-- zjn 2019-11-04 修改共享报告的状态 -->
    <update id="updateShareStatus">
    	UPDATE oa_daily_share SET
    		status = 2
    	WHERE daily = #{recordId} AND share = #{loginEmpId}
    </update>
    
    <select id="getReportNum" resultType="Integer">
		SELECT
			COUNT(recordId)
		FROM
			oa_daily
		WHERE
			employee = #{employeeId}
		AND `day` = #{day}
		AND activeFlag = 1
		AND flag = #{flag}
		AND monthly = #{monthly}
		AND commitStatus = 1
		<if test="recordId != null and recordId != ''">
			AND recordId <![CDATA[<>]]> #{recordId}
		</if>
	</select>

	<select id="getSuperiorLeaders" resultType="Oa_report">
		SELECT
		    b.recordId AS "superId",
			b.`name` AS "superName",
		    c.recordId AS "superiorsId",
			c.`name` AS "superiorsName"
		FROM
			oa_md_employee a
			LEFT JOIN oa_md_employee b ON b.recordId = a.superEmpId
			LEFT JOIN oa_md_employee c ON c.recordId = b.superEmpId AND c.name != b.name
		WHERE
			a.recordId = #{employeeId}
	</select>

	<select id="getTaskAllConent" resultType="String">
		select
		GROUP_CONCAT(CONCAT(bb.details,' 分数：',IFNULL(bb.score,0)) SEPARATOR '\n') AS "content"
		from (

		SELECT
		aa.aaa,
		aa.volumeRatio,
		aa.details,
		aa.test,
		ROUND((aa.test * aa.volumeRatio / 100) / aa.schedule/100/ aa.aaa,2) AS "score"
		FROM
		(
		SELECT
		a.volumeRatio,
		CONCAT('标题：' ,a.title ,'  内容:  ',a.content,'  ','进度:  ',a.schedule,'%') AS "details",
		(SELECT score FROM task_completion_progress_sheet WHERE postTaskId = a.postTaskId AND schedule <![CDATA[<=]]> a.schedule LIMIT 1) AS "test",
		a.schedule,
		CASE #{flag} WHEN 1 THEN 1
		WHEN 2 THEN 7
		WHEN 3 THEN 30
		WHEN 4 THEN 1
		ELSE 1 END	AS "aaa"
		FROM
		oa_task a
		LEFT JOIN
		post_task_sheet b ON b.recordId = a.postTaskId
		WHERE
		a.activeFlag = 1
		AND a.principals = #{employeeId}
		<if test="flag != null and flag != '' and flag == 1">
			AND (a.taskType = #{flag} OR (a.postTaskId IS NULL AND a.taskType = #{flag}))
			AND a.completeTime = #{completeTime}
		</if>
		<if test="flag != null and flag != '' and flag == 2">
			AND a.taskType = #{flag} AND YEARWEEK(a.completeTime) = #{completeTime}
		</if>
		<if test="flag != null and flag != '' and flag == 3">
			AND a.taskType = #{flag} AND DATE_FORMAT(a.completeTime, '%Y%m') = #{completeTime}
		</if>
		<if test="flag != null and flag != '' and flag == 4">
			AND (a.taskType = #{flag} OR (a.postTaskId IS NULL AND a.taskType = #{flag}))
			AND a.completeTime = #{completeTime}
		</if>
		) aa
		)bb
	</select>

	<select id="getReportWorkNum" resultType="Integer">
		SELECT
			count(1) AS 'reportNum'
		FROM
			oa_daily a
		LEFT JOIN oa_md_employee b ON b.recordId = a.employee
		WHERE
			a.activeFlag = 1
		AND a.content IS NOT NULL
		AND a.status = 2
		AND (
			a.recordId IN (
				SELECT
					daily
				FROM oa_daily_share
				WHERE `share` = #{employeeId} AND status = 2
			)
			OR (b.superEmpId = #{employeeId} AND a.status = 2)
		)
	</select>

	<select id="getPerconMoalList" resultType="Oa_report">
		SELECT
			a.*,
			d.name AS "superName",
		    e.name AS "superiorsName"
		FROM
			oa_daily a
			LEFT JOIN oa_md_employee b ON b.recordId = a.employee
			LEFT JOIN oa_md_employee d ON d.recordId = b.superEmpId
			LEFT JOIN oa_md_employee e ON e.recordId = d.superEmpId AND e.`name` != d.`name`
		WHERE
			a.activeFlag = 1 AND a.employee = #{employeeId}
		<if test="year != null and year != ''">
			AND a.year = #{year}
		</if>
		<if test="month != null and month != ''">
			AND a.month = #{month}
		</if>
		<if test="week != null and week != ''">
			AND a.week = #{week}
		</if>
		<if test="number != null and number != '' and number == 1">
			AND a.monthly = #{monthly}
		</if>
	</select>

	<insert id="insertPerconMoal" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO oa_daily (
			content,
			contentTwo,
			contentThree,
			monthly,
			diffType,
			createdDate,
			activeFlag,
			employee,
			architectureId,
			commitStatus,
			`day`,
			 flag,
			 week,
			 `month`,
			 `year`,
			 createdBy
		)VALUES(
			#{content},
			#{contentTwo},
			#{contentThree},
			#{monthly},
			1,
			NOW(),
			1,
			#{employeeId},
			#{architectureId},
			#{commitStatus},
			#{day},
			#{flag},
			#{week},
			#{month},
			#{year},
			#{employeeId}
		)
	</insert>

	<update id="updatePerconMoal">
		UPDATE oa_daily SET
		 	content = #{content},contentTwo = #{contentTwo},contentThree = #{contentThree},monthly = #{monthly},
		 	commitStatus = #{commitStatus}
		WHERE
			recordId = #{recordId}
	</update>
</mapper>