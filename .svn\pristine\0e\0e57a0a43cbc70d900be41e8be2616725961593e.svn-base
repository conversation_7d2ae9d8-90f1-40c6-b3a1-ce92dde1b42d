package com.kyb.pcberp.modules.wechat.service;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.service.BaseService;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.common.utils.alimessage.MessageUtil;
import com.kyb.pcberp.modules.approval.dao.ApprovalDao;
import com.kyb.pcberp.modules.approval.entity.Approval;
import com.kyb.pcberp.modules.approval.utils.ErpAuditUtils;
import com.kyb.pcberp.modules.approval.utils.ModifyUtils;
import com.kyb.pcberp.modules.contract.dao.*;
import com.kyb.pcberp.modules.contract.entity.*;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.hr.company_center.pojo.Hr_Regimen;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_BonusTask_Total;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_DepartMent;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_KybAttachments;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_Position;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;
import com.kyb.pcberp.modules.hr.finance_center.dao.Hr_CertificateDao;
import com.kyb.pcberp.modules.hr.finance_center.pojo.Hr_CollectPayBill;
import com.kyb.pcberp.modules.hr.group_center.pojo.Hr_DepartRelation;
import com.kyb.pcberp.modules.hr.permission_center.pojo.Hr_Approve;
import com.kyb.pcberp.modules.hr.permission_center.pojo.Oa_AuditTypeSheet;
import com.kyb.pcberp.modules.inter.entity.InterOrderConfig;
import com.kyb.pcberp.modules.oa.dao.Oa_reportDao;
import com.kyb.pcberp.modules.oa.pojo.audit.Oa_approve_record;
import com.kyb.pcberp.modules.oa.utils.ApprovalEngineUtil;
import com.kyb.pcberp.modules.production.dao.CapacityRecordDao;
import com.kyb.pcberp.modules.production.entity.CapacityChange;
import com.kyb.pcberp.modules.production.service.CapacityRecordService;
import com.kyb.pcberp.modules.purch.entity.PurchRaw;
import com.kyb.pcberp.modules.report.entity.CapacityDeail;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.dao.MaterialUseDao;
import com.kyb.pcberp.modules.stock.dao.StoreHouseDao;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.MaterialUse;
import com.kyb.pcberp.modules.stock.entity.StoreHouse;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import com.kyb.pcberp.modules.sys.dao.DepartmentDao;
import com.kyb.pcberp.modules.sys.dao.DictValueDao;
import com.kyb.pcberp.modules.sys.dao.UserDao;
import com.kyb.pcberp.modules.sys.entity.*;
import com.kyb.pcberp.modules.wechat.dao.KybDayThingDao;
import com.kyb.pcberp.modules.wechat.dao.MaintenanceDao;
import com.kyb.pcberp.modules.wechat.dao.WechatOaDao;
import com.kyb.pcberp.modules.wechat.entity.*;
import com.kyb.pcberp.modules.wechat.pojo.ExpenseType;
import com.kyb.pcberp.modules.wechat.pojo.auditConfig.AuditDataDetail;
import com.kyb.pcberp.modules.wechat.pojo.auditConfig.AuditExistDetail;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.*;
import com.kyb.pcberp.modules.wechat.utils.HttpRquestUtil;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Transactional(readOnly = true)
public class KybSoftOaService extends BaseService {
    @Autowired
    private WechatOaDao wechatOaDao;

    @Autowired
    private ContractDetailDao contractDetailDao;

    @Autowired
    private NotificationDao notificationDao;

    @Autowired
    private ApprovalDao approvalDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private KybDayThingDao kybDayThingDao;

    @Autowired
    private CompanyDao companyDao;

    @Autowired
    private MaxBatchAreaConfirmDao maxBatchAreaConfirmDao;

    @Autowired
    private CapacityRecordService capacityRecordService;

    @Autowired
    private MatPreparationDao matPreparationDao;

    @Autowired
    private MaterialDao materialDao;

    @Autowired
    private MaterialUseDao materialUseDao;

    @Autowired
    private DepartmentDao departmentDao;

    @Autowired
    private CapacityRecordDao capacityRecordDao;

    @Autowired
    private CapacityDao capacityDao;

    @Autowired
    private StoreHouseDao storeHouseDao;

    @Autowired
    private MaintenanceDao maintenanceDao;

    @Autowired
    private Hr_CertificateDao hr_certificateDao;

    @Autowired
    private Oa_reportDao oa_reportDao;

    @Autowired
    private DictValueDao dictValueDao;

    public List<IcloudUser> getContractList(IcloudUser user) {
        return wechatOaDao.getContractList(user);
    }

    /**
     * 获取员工绩效数据
     * 1. 查询直接下级已提交月报但未被评价的员工数据
     * 2. 同时如果该用户有下下级，也查询已被其上级评价但未被当前用户(作为上上级)评价的下下级员工数据
     * @param wechatUser 当前登录用户信息
     * @return 绩效数据列表
     */
    public List<Hr_BonusTask_Total> getPerformanceData(WechatUser wechatUser)
    {
        if (wechatUser == null || StringUtils.isBlank(wechatUser.getRecordId())) {
            return null;
        }
        return wechatOaDao.getPerformanceData(wechatUser);
    }

    public List<WechatAudit> getAuditList(WechatAudit wechatAudit) {
        List<WechatAudit> auditList = wechatOaDao.getAuditList(wechatAudit);
        return auditList;
    }

    @Transactional(readOnly = false)
    public String deleteAudit(WechatAudit audit) {
        wechatOaDao.deleteAudit(audit);
        return "success";
    }

    public WechatAudit getAudit(WechatAudit wechatAudit) {
        WechatAudit audit = wechatOaDao.getAudit(wechatAudit);
        if(null != audit && StringUtils.isNotBlank(audit.getRecordId()))
        {
            // 审批记录表
            audit.setRecordList(wechatOaDao.getAuditRecord(wechatAudit));
            // 审批附件表
            audit.setAttachList(wechatOaDao.getAttachmments(wechatAudit));
            // 审批评论表
            audit.setCommentList(wechatOaDao.getAuditComment(wechatAudit));
        }
        return audit;
    }

    public Oa_AuditTypeSheet getAuditConfig(WechatAudit wechatAudit)
    {
        // 获取审批配置主表
        Oa_AuditTypeSheet auditTypeSheet = wechatOaDao.getOaAuditTypeSheet(wechatAudit.getAuditTypeId());
        if (StringUtils.isNotBlank(auditTypeSheet.getRecordId()) && StringUtils.isNotBlank(auditTypeSheet.getAttachments())){
            // 获取审批配置明细表
            List<InterOrderConfig> configList = wechatOaDao.getAuditConfigList(auditTypeSheet.getRecordId(),wechatAudit.getVersion());
            auditTypeSheet.setConfigList(configList);
            // 加载数据来源
            if (StringUtils.isNotBlank(wechatAudit.getRecordId())){
                for (InterOrderConfig config : configList){
                    if (StringUtils.isNotBlank(config.getPrompt())){
                        String[] strings = config.getPrompt().split("-");
                        if (strings != null && strings.length > 1){
                            if (auditTypeSheet.getAttachments().equals("1")){
                                if (strings[0].equals("method")){
                                    String name = strings[1];
                                    auditTypeSheet.setShowAuditData(showAuditData(name, wechatAudit.getRecordId()));
                                }
                            }else if (auditTypeSheet.getAttachments().equals("2")){
                                if (strings[0].equals("table")){
                                    // 加载旧设计使用了表的数据
                                    String name = strings[1];
                                    if (name.equals("rest") && StringUtils.isNotBlank(wechatAudit.getRestGroupId())){
                                        List<WechatAuditRest> restList = wechatOaDao.getRests(wechatAudit);
                                        auditTypeSheet.setRestList(restList);
                                    }else if (name.equals("expense") && StringUtils.isNotBlank(wechatAudit.getExpenseGroupId())){
                                        List<WechatExpense> expenseList = wechatOaDao.getExpenses(wechatAudit);
                                        auditTypeSheet.setExpenseList(expenseList);
                                    }
                                }
                            }
                        }
                    }
                }
                if (auditTypeSheet.getAttachments().equals("2")){
                    // 键值对
                    List<InterConfigOaDetail> list = wechatOaDao.getConfigValueList(wechatAudit.getRecordId());
                    auditTypeSheet.setList(list);
                }
            }
        }
        return auditTypeSheet;
    }

    @Transactional(readOnly = false)
    public String draftAudit(WechatCommitApprove approve) {
        // 验证是否可以撤回
        WechatAudit audit = new WechatAudit();
        audit.setRecordId(approve.getAuditId());
        List<WechatRecord> recordList = wechatOaDao.getAuditRecord(audit);
        boolean draftFlag = false;
        if (recordList != null && recordList.size() > 0) {
            if (recordList.get(0) != null && recordList.get(0).getCreateBy() != null && recordList.get(0).getCreateBy().equals(approve.getOpertionUserId())) {
                for (int i = 0; i < recordList.size(); i++) {
                    if (recordList.get(i) != null && StringUtils.isNotBlank(recordList.get(i).getStatus()) && recordList.get(i).getLastUpdateBy() != null) {
                        if (!recordList.get(i).getLastUpdateBy().equals(approve.getOpertionUserId())) {
                            draftFlag = true;
                            break;
                        }
                    }
                }
            } else {
                draftFlag = true;
            }
        }
        if (draftFlag) {
            return "已被审批，无法撤回，请通知相关人员驳回！";
        } else {
            audit.setEmpId(approve.getOpertionUserId());
            // 修改审批为草稿状态
            wechatOaDao.updateDraftAudit(audit);
            // 删除所有有关该审批单正在审批的审批记录
            wechatOaDao.deleteDraftAuditRecord(audit);
            return "success";
        }
    }

    public List<WechatUser> getEmpList(WechatUser wechatUser) {
        return wechatOaDao.getEmpList(wechatUser);
    }

    public List<WechatUser> loadOaEmpList(WechatUser wechatUser) {
        return wechatOaDao.getOaEmpList(wechatUser);
    }

    public List<WechatAudit> getComList(WechatAudit wechatAudit) {
        return wechatOaDao.getComList(wechatAudit);
    }

    @Transactional(readOnly = false)
    public String commitAudit(WechatAudit wechatAudit) throws Exception {
        if (StringUtils.isBlank(wechatAudit.getAuditTypeId())){
            return "连接问题，请刷新重试！";
        }
        Oa_AuditTypeSheet auditTypeSheet = wechatOaDao.getOaAuditTypeSheet(wechatAudit.getAuditTypeId());
        if (auditTypeSheet == null || StringUtils.isBlank(auditTypeSheet.getNo())){
            return "当前审批配置失效，请刷新重试！";
        }
        wechatAudit.setAuditType(auditTypeSheet.getNo());
        wechatAudit.setApplicationsType("1");
        List<AuditDataDetail> addList = new ArrayList<>();
        ApprovalEngineUtil.setData(wechatAudit, addList);
        List<AuditExistDetail> expenseList = new ArrayList<>();
        List<AuditExistDetail> restList = new ArrayList<>();
        List<AuditDataDetail> detailList = new ArrayList<>();
        ApprovalEngineUtil.setDetailList(wechatAudit, expenseList, restList, detailList);
        if (StringUtils.isNotBlank(wechatAudit.getRecordId())) {
            wechatOaDao.delConfigValueList(wechatAudit.getRecordId());
            // 更新审批单及旧数
            wechatOaDao.resubmit(wechatAudit);
        } else {
            // 插入审批单及旧数
            wechatOaDao.commitAudit(wechatAudit);
            // 更新编码
            oaNumber(wechatAudit.getRecordId(), wechatAudit.getAuditType());
        }
        if (expenseList != null && expenseList.size() > 0){
            wechatOaDao.commitExpense(expenseList);
        }
        if (restList != null && restList.size() > 0){
            wechatOaDao.commitRest(restList);
        }
        // 插入addList
        if (StringUtils.isNotBlank(wechatAudit.getRecordId())){
            if (addList != null && addList.size() > 0){
                wechatOaDao.insertInterConfigOaAuditDetail(addList, wechatAudit.getRecordId(), wechatAudit.getEmpId());
            }
            if (detailList != null && detailList.size() > 0){
                List<AuditDataDetail> addDetailList = new ArrayList<>();
                for (AuditDataDetail dataDetail : detailList){
                    dataDetail.setAuditId(wechatAudit.getRecordId());
                    dataDetail.setEmpId(wechatAudit.getEmpId());
                    wechatOaDao.saveAuditDetail(dataDetail);
                    String recordId = dataDetail.getRecordId();
                    if (dataDetail.getList() != null && dataDetail.getList().size() > 0){
                        for (AuditDataDetail auditDetail : dataDetail.getList()){
                            auditDetail.setDetailId(recordId);
                            addDetailList.add(auditDetail);
                        }
                    }
                }
                if (addDetailList != null && addDetailList.size() > 0){
                    wechatOaDao.insertInterConfigOaAuditDetail(addDetailList, wechatAudit.getRecordId(), wechatAudit.getEmpId());
                }
            }
        }
        // 保存图片到阿里云服务器
        if (Collections3.isNotEmpty(wechatAudit.getServerList())) {
            String url = HttpRquestUtil.getWechatUrl();
            String savePath = "oaAudit" + "/" + wechatAudit.getRecordId() + "/" + DateUtils.formatDateTime(new Date()) + "/";
            int num = 1;
            for (String server : wechatAudit.getServerList()) {
                String webUrl = url + server;
                String name = wechatAudit.getRecordId() + DateUtils.formatDateTime(new Date()) + (num++) + ".jpg";
                String sourcePath = savePath + name;
                FileManageUtils.wxUploadImg(webUrl, sourcePath);
                // 存储路径到数据库
                WechatAttachments attach = new WechatAttachments();
                attach.setRecordId(wechatAudit.getRecordId());
                attach.setFileUrl(sourcePath);
                attach.setOrgFileName(name);
                wechatOaDao.insertAuditAttach(attach);
            }
        }
        // 提交
        WechatCommitApprove commit = new WechatCommitApprove();
        commit.setAuditId(wechatAudit.getRecordId());
        commit.setOpertionUserId(wechatAudit.getEmpId());
        commit.setCommitPersonAllId(wechatAudit.getOaDepartId());
        return ApprovalEngineUtil.commitApprove(commit);
    }

    @Transactional(readOnly = false)
    public String approveAssent(WechatCommitApprove commit) {
        return ApprovalEngineUtil.approveAssent(commit);
    }

    @Transactional(readOnly = false)
    public String commitAssent(WechatCommitApprove commit) {
        return ApprovalEngineUtil.commitApprove(commit);
    }

    @Transactional(readOnly = false)
    public String approveReject(WechatCommitApprove commit) {
        return ApprovalEngineUtil.approveReject(commit);
    }

    public List<WechatRecord> getRejectUser(WechatAudit audit){
        return wechatOaDao.getRejectUser(audit);
    }

    @Transactional(readOnly = false)
    public void oaNumber(String id, String type) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        String dt = format.format(date);
        WechatOaNumber number = new WechatOaNumber();
        number.setId(id);
        number.setType(type);
        number.setDate(dt);
        String no = "";
        String noend = "";
        Integer i = wechatOaDao.findNumber(number);
        if (null != i && i > 0) {
            i = i + 1;
        } else {
            i = 1;
        }
        if (i < 10) {
            noend = "00" + i;
        } else if (i < 100) {
            noend = "0" + i;
        } else {
            noend = "" + i;
        }
        no = dt + noend;
        number.setNo(no);
        number.setNum(noend);
        wechatOaDao.saveNumber(number);
    }

    public List<DepartmentRelationCompany> getCompanyList(WechatAudit wechatAudit) {
        List<DepartmentRelationCompany> list = wechatOaDao.getAllCompany(wechatAudit);
        return list;
    }

    @Transactional(readOnly = false)
    public String addAuditComment(WechatComment comment) {
        wechatOaDao.addAuditComment(comment);
        return "success";
    }

    @Transactional(readOnly = false)
    public List<WechatComment> addComment(WechatComment comment) {
        wechatOaDao.addAuditComment(comment);
        List<WechatComment> kybCommentList = Lists.newArrayList();
        if (StringUtils.isNotBlank(comment.getAuditId())) {
            WechatAudit kybAudit = new WechatAudit();
            kybAudit.setRecordId(comment.getAuditId());
            kybCommentList = wechatOaDao.getAuditComment(kybAudit);
        } else if (StringUtils.isNotBlank(comment.getTaskId())) {
            WechatTask kybTask = new WechatTask();
            kybTask.setRecordId(comment.getTaskId());
            kybCommentList = wechatOaDao.getTaskComment(kybTask);
        } else if (StringUtils.isNotBlank(comment.getDailyId())) {
            WechatReport kybReport = new WechatReport();
            kybReport.setRecordId(comment.getDailyId());
            kybCommentList = wechatOaDao.getReportComment(kybReport);
        } else if (StringUtils.isNotBlank(comment.getDayThingId())) {
            KybDayThing kybDayThing = new KybDayThing();
            kybDayThing.setRecordId(comment.getDayThingId());
            kybCommentList = kybDayThingDao.getDayThingComment(kybDayThing);
        }else if (StringUtils.isNotBlank(comment.getMaintenanceRecordId()))
        {
            Maintenance maintenance = new Maintenance();
            maintenance.setRecordId(comment.getMaintenanceRecordId());
            kybCommentList = maintenanceDao.getMarkSystemComment(maintenance);
        }
        return kybCommentList;
    }

    public List<WechatComment> getAuditComment(WechatAudit wechatAudit) {
        return wechatOaDao.getAuditComment(wechatAudit);
    }

    // 保存任务
    @Transactional(readOnly = false)
    public String saveTask(WechatTask wechatTask) {
        String empId = "";
        if (wechatTask.getPrincipals() != null && StringUtils.isNotBlank(wechatTask.getPrincipals().getRecordId())) {
            empId = wechatTask.getPrincipals().getRecordId();
        }
        // 保存任务
        if(StringUtils.isBlank(wechatTask.getRecordId()))
        {
            wechatOaDao.saveTask(wechatTask);
        }else{
            wechatOaDao.updateTask(wechatTask);
            // 删除所有参与共享人员
            wechatOaDao.delTaskPart(wechatTask);
            wechatOaDao.delTaskShare(wechatTask);
            if(StringUtils.isNotBlank(wechatTask.getComment())){
                wechatOaDao.insertComment(wechatTask.getComment(), wechatTask.getCreatedBy().getRecordId(), wechatTask.getRecordId(), null);
            }
        }
        // 保存参与人员
        if (Collections3.isNotEmpty(wechatTask.getParticipateList())) {
            for (WechatUser user : wechatTask.getParticipateList()) {
                user.setDataId(wechatTask.getRecordId());
                user.setCreatedBy(wechatTask.getCreatedBy());
                user.setActiveFlag("1");
                if (StringUtils.isNotBlank(empId)) {
                    empId = empId + "," + user.getRecordId();
                } else {
                    empId = user.getRecordId();
                }
            }
            wechatOaDao.saveTaskParticipate(wechatTask.getParticipateList());
        }
        // 保存共享人员
        if (Collections3.isNotEmpty(wechatTask.getSharesList())) {
            for (WechatUser user : wechatTask.getSharesList()) {
                user.setDataId(wechatTask.getRecordId());
                user.setCreatedBy(wechatTask.getCreatedBy());
                user.setActiveFlag("1");
                if (StringUtils.isNotBlank(empId)) {
                    empId = empId + "," + user.getRecordId();
                } else {
                    empId = user.getRecordId();
                }
            }
            wechatOaDao.saveTaskShares(wechatTask.getSharesList());
        }
        // 保存图片到阿里云服务器
        if (Collections3.isNotEmpty(wechatTask.getServerList())) {
            String url = HttpRquestUtil.getWechatUrl();
            String savePath = "oaTask" + "/" + wechatTask.getRecordId() + "/";
            int num = 1;
            for (String server : wechatTask.getServerList()) {
                String webUrl = url + server;
                String name = wechatTask.getRecordId() + (num++) + ".jpg";
                String sourcePath = savePath + name;
                FileManageUtils.wxUploadImg(webUrl, sourcePath);
                // 存储路径到数据库
                WechatAttachments attach = new WechatAttachments();
                attach.setRecordId(wechatTask.getRecordId());
                attach.setFileUrl(sourcePath);
                attach.setOrgFileName(name);
                wechatOaDao.insertTaskAttach(attach);
            }
        }
        MessageUtil.sendMsg(empId, "2", null);
        return "success";
    }

    // tj 修改任务进度
    @Transactional(readOnly = false)
    public String updateSchedule(WechatTask task) {
        String comment =  "更新进度:" + task.getSchedule()+"%,更新内容:" + task.getTaskContent();
        task.setComment(comment);
        //插入一条评论内容
        wechatOaDao.insertComment(comment, task.getCreatedBy().getRecordId(), task.getRecordId(), null);
        wechatOaDao.updateSchedule(task);
        return "success";
    }

    public WechatAudit getPayMsg(WechatAudit kybAudit) {
        WechatAudit audit = wechatOaDao.getPayMsg(kybAudit);
        return audit;
    }

    // tj 操作erp审批
    @Transactional(readOnly = false)
    public String examine(ErpCommit erpCommit) {
        WechatAudit audit = new WechatAudit();
        audit.setRecordId(erpCommit.getRecordId());
        Approval approval = approvalDao.getApproval(audit);
        if (!TypeKey.APPROVAL_STATUS_WAIT.toString().equals(approval.getStatus())) {
            return "该审批单已审批完成！";
        }
        approval.setOpinion(erpCommit.getOpinion());
        approval.setExamine(erpCommit.getExamine());
        User user = new User();
        user.setMobile(erpCommit.getPhone());
        user.setCompany(erpCommit.getCompany());
        user = userDao.getUserByMobile(user);
        return ModifyUtils.examine(approval, user);
    }

    @Transactional(readOnly = false)
    public String messageBidding(ErpCommit erpCommit) {
        User user = new User();
        user.setMobile(erpCommit.getPhone());
        user.setCompany(erpCommit.getCompany());
        user = userDao.getUserByMobile(user);
        return ModifyUtils.messageBidding(erpCommit, user);
    }

    public List<CapacityChange> getCapacityChangeData(MaxBatchAreaConfirm maxBatchAreaConfirm, User user) {
        List<CapacityChange> list = Lists.newArrayList();
        if (null == maxBatchAreaConfirm) {
            return list;
        }
        CapacityChange change = new CapacityChange();
        change.setCompanyId(user.getCompany().getRecordId());
        change.setProTime(maxBatchAreaConfirm.getUseDate());
        change.setConfirmId(maxBatchAreaConfirm.getRecordId());
        if (StringUtils.isNotBlank(maxBatchAreaConfirm.getCustomerId())) {
            change.setCustomerId(maxBatchAreaConfirm.getCustomerId());
        } else if (StringUtils.isNotBlank(maxBatchAreaConfirm.getDeptId())) {
            change.setDepartId(maxBatchAreaConfirm.getDeptId());
        }
        change.setProductType(maxBatchAreaConfirm.getProductType());

        // 样品款数
        change.setSampleNum(maxBatchAreaConfirm.getSampleNum());
        // 量产款数
        change.setMassProductionNum(maxBatchAreaConfirm.getMassProductionNum());
        // 新单款数
        change.setNewOrderNum(maxBatchAreaConfirm.getNewOrderNum());
        // 产能
        if (StringUtils.isNotBlank(maxBatchAreaConfirm.getArea())) {
            change.setCapacity(new BigDecimal(maxBatchAreaConfirm.getArea()));
        }

        change.setCreatedBy(user);
        if ("2".equals(maxBatchAreaConfirm.getType())) {
            change.setType(3);
            list.add(change);
        } else if ("3".equals(maxBatchAreaConfirm.getType())) {
            change.setType(2);
            CapacityChange clone = change.clone();
            if (null != change.getSampleNum()) {
                change.setSampleNum(change.getSampleNum() * -1);
            }
            if (null != change.getMassProductionNum()) {
                change.setMassProductionNum(change.getMassProductionNum() * -1);
            }
            if (null != change.getNewOrderNum()) {
                change.setNewOrderNum(change.getNewOrderNum() * -1);
            }
            if (null != change.getCapacity()) {
                change.setCapacity(change.getCapacity().multiply(new BigDecimal(-1)));
            }
            list.add(change);

            clone.setType(1);
            if (StringUtils.isNotBlank(maxBatchAreaConfirm.getCustomerAfterId())) {
                clone.setCustomerId(maxBatchAreaConfirm.getCustomerAfterId());
            } else if (StringUtils.isNotBlank(maxBatchAreaConfirm.getDeptAfterId())) {
                clone.setDepartId(maxBatchAreaConfirm.getDeptAfterId());
            }
            list.add(clone);
        }
        return list;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> batchAreaConfirm(ErpCommit erpCommit) {
        Map<String, Object> data = new HashMap<>();
        if (null == erpCommit) {
            data.put("result", "fail");
            data.put("message", "参数失效，请联系管理员!");
            return data;
        }
        String num = erpCommit.getExamine();
        MaxBatchAreaConfirm query = new MaxBatchAreaConfirm();
        query.setRecordId(erpCommit.getRecordId());
        MaxBatchAreaConfirm confirm = maxBatchAreaConfirmDao.get(query);
        if (null == confirm) {
            data.put("result", "fail");
            data.put("message", "系统错误，请联系管理员!");
            return data;
        }

        confirm.setConfirmReason(erpCommit.getOpinion());

        User user = new User();
        user.setMobile(erpCommit.getPhone());
        user.setCompany(erpCommit.getCompany());
        user = userDao.getUserByMobile(user);
        confirm.setLastUpdBy(user);
        confirm.setLastUpdDate(new Date());

        ConfirmRecord record = new ConfirmRecord();
        if (StringUtils.isNotBlank(erpCommit.getConfirmRecordId())) {
            record.setRecordId(erpCommit.getConfirmRecordId());
            record = maxBatchAreaConfirmDao.getConfirmRecord(record);
            record.setStatus("0");
            record.setLastUpdBy(user);
            record.setLastUpdDate(new Date());
            maxBatchAreaConfirmDao.updateConfirmRecord(record);
        }
        MatPreparation matPreparation = new MatPreparation();
        matPreparation.setRecordId(confirm.getDataId());
        matPreparation = matPreparationDao.get(matPreparation);
        if (StringUtils.isNotBlank(confirm.getUpdateFlag()) && "1".equals(confirm.getUpdateFlag())) {
            // 更新备料采购周期
            if (StringUtils.isNotBlank(erpCommit.getUpdateValue())) {
                matPreparation.setLeadTime(Integer.valueOf(erpCommit.getUpdateValue()));
                matPreparationDao.updateLeadTime(matPreparation);

                // 更新物料采购周期
                Material mat = new Material();
                mat.setRecordId(matPreparation.getMaterialId());
                mat.setLeadTime(Integer.valueOf(erpCommit.getUpdateValue()));
                materialDao.updateLeadTime(mat);
            }
        }
        Boolean flag = true;

        // 同意
        if ("1".equals(num)) {
            if (StringUtils.isNotBlank(erpCommit.getConfirmRecordId())) {
                ConfirmRecord afterRecord = maxBatchAreaConfirmDao.getAfterConfirmRecord(record);
                if (null != afterRecord) {
                    afterRecord.setStatus("1");
                    maxBatchAreaConfirmDao.updateConfirmRecord(afterRecord);
                    flag = false;
                } else {
                    confirm.setStatus("1002");
                    maxBatchAreaConfirmDao.updateStatusTwo(confirm);
                }
            } else {
                confirm.setStatus("1002");
                maxBatchAreaConfirmDao.updateStatusTwo(confirm);
            }
            if (flag) {
                List<CapacityChange> changeList = getCapacityChangeData(confirm, user);
                switch (confirm.getType()) {
                    // 批次面积确认
                    case "1":
                        ContractDetail detail = confirm.getDetail();
                        detail.setStatus(confirm.getBckups().getFrontStatus());
                        contractDetailDao.updateStatus(detail);
                        if (StringUtils.isNotBlank(confirm.getBckups().getArea())) {
                            detail.setMaxBatchArea(new BigDecimal(confirm.getBckups().getArea()));
                        }
                        contractDetailDao.updateMaxBatchArea(detail);
                        break;
                    // 申请调整
                    case "2":
                        capacityRecordService.saveCapacityChange(changeList, user);
                        break;
                    // 申请借用
                    case "3":
                        // 借用记录是否已添加
                        Integer addNum = capacityRecordDao.getConfirmChangeCount(confirm);
                        if (null == addNum || addNum == 0) {
                            capacityRecordService.saveCapacityChange(changeList, user);
                        }
                        break;
                    // 市场备料确认
                    case "4":
                        matPreparation.setStatus("1003");
                        matPreparation.setConfirmTime(new Date());
                        matPreparation.setLastUpdBy(user);
                        matPreparationDao.updateStatusTwo(matPreparation);
                        break;
                }
            }
            data.put("message", "同意成功!");
        }
        // 拒绝
        else if ("2".equals(num)) {
            confirm.setStatus("1003");
            maxBatchAreaConfirmDao.updateStatusTwo(confirm);

            switch (confirm.getType()) {
                // 批次面积确认
                case "1":
                    ContractDetail detail = confirm.getDetail();
                    detail.setStatus(confirm.getBckups().getFrontStatus());
                    contractDetailDao.updateStatus(detail);
                    break;
                // 申请调整
                case "2":
                    break;
                // 申请借用
                case "3":
                    // 清除借用记录
                    capacityRecordDao.delCapacityChangeList(confirm);
                    if (StringUtils.isNotBlank(confirm.getGroupCenterId())) {
                        GroupCenter groupCenter = new GroupCenter();
                        groupCenter.setRecordId(confirm.getGroupCenterId());
                        groupCenter = contractDetailDao.getGroupCenterTwo(groupCenter);

                        // 备料使用情况
                        notificationDao.delMaterialArea(groupCenter);
                        // 备料明细使用情况
                        notificationDao.delMaterialAreaDetail(groupCenter);

                        // 生产批次的全部删除
                        capacityDao.deleteCapacityDeail(groupCenter.getContactDeailId());

                        // 更新合同明细的一键集控id
                        ContractDetail cd = new ContractDetail(groupCenter.getContactDeailId());
                        cd.setGroupCenterId(null);
                        contractDetailDao.updateGroupCenterId(cd);

                        // 一键集控的删除
                        contractDetailDao.delGroupCenter(cd);
                    }


                    break;
                // 市场备料确认
                case "4":
                    matPreparation.setRecordId(confirm.getDataId());
                    matPreparation.setStatus(confirm.getBckups().getFrontStatus());
                    matPreparationDao.updateStatus(matPreparation);
                    break;
            }
            data.put("message", "拒绝成功!");
        }
        data.put("result", "success");
        return data;
    }

    // tj 获取答案
    public String getAnswer(String problem) {
        return wechatOaDao.getAnswer(problem);
    }

    // 获取报告列表
    public List<WechatReport> getReportList(WechatUser wechatUser) {
        return wechatOaDao.getReportList(wechatUser);
    }

    @Transactional(readOnly = false)
    public WechatReport getReport(WechatReport wechatReport) {
        // 获取报告
        WechatReport report = wechatOaDao.getReport(wechatReport);
        if (report != null && StringUtils.isNotBlank(report.getRecordId())) {
            // 获取附件
            report.setAttachList(wechatOaDao.getReportAttach(report));
            // 获取评论回复
            report.setCommentList(wechatOaDao.getReportComment(report));
        }
        // 修改报告的状态
        wechatOaDao.updateStatusOne(wechatReport);
        wechatOaDao.updateStatusTwo(wechatReport);
        // 修改共享报告的状态
        wechatOaDao.updateShareStatus(wechatReport);
        return report;
    }

    @Transactional(readOnly = false)
    public String saveReport(WechatReport report) {
        // 验证报告是否已经添加
        Integer reportNum = wechatOaDao.getReportNum(report);
        if (reportNum > 0) {
            return "你已经上传了该时间的报告";
        }
        if (report != null && StringUtils.isNotBlank(report.getRecordId())) {
            wechatOaDao.updateReport(report);
            wechatOaDao.delReportShares(report);
            if(StringUtils.isNotBlank(report.getComment())){
                wechatOaDao.insertComment(report.getComment(), report.getEmployeeId(), null, report.getRecordId());
            }
        } else {
            wechatOaDao.saveReport(report);
        }
        String empId = "";
        List<Hr_Employee> list = new ArrayList<>();
        String shareIds = report.getShareIds();
        // 插入共享
        if(StringUtils.isNotBlank(shareIds)){
            String[] strArrays = shareIds.split(",");
            for (String shareId : strArrays){
                empId = StringUtils.isBlank(empId) ? shareId : empId + "," + shareId;
                Hr_Employee employee = new Hr_Employee();
                employee.setRecordId(shareId);
                employee.setDataId(report.getRecordId());
                list.add(employee);
            }
        }
        if (list != null && list.size() > 0){
            oa_reportDao.saveReportShares(list);
        }
        // 保存图片到阿里云服务器
        if (Collections3.isNotEmpty(report.getServerList())) {
            String url = HttpRquestUtil.getWechatUrl();
            String savePath = "oaDaily" + "/" + report.getRecordId() + "/";
            int num = 1;
            for (String server : report.getServerList()) {
                String webUrl = url + server;
                String name = report.getRecordId() + (num++) + ".jpg";
                String sourcePath = savePath + name;
                FileManageUtils.wxUploadImg(webUrl, sourcePath);
                // 存储路径到数据库
                WechatAttachments attach = new WechatAttachments();
                attach.setRecordId(report.getRecordId());
                attach.setFileUrl(sourcePath);
                attach.setOrgFileName(name);
                wechatOaDao.insertReportAttach(attach);
            }
        }
        MessageUtil.sendMsg(empId, "3", null);
        return "success";
    }

    // 获取任务列表
    public List<WechatTask> getTaskList(WechatUser wechatUser) {
        return wechatOaDao.getTaskList(wechatUser);
    }

    // 获取任务详情
    public WechatTask getTask(WechatTask wechatTask) {
        // 获取任务
        WechatTask task = wechatOaDao.getTask(wechatTask);
        // 获取附件
        task.setAttachList(wechatOaDao.getTaskAttach(task));
        // 获取评论回复
        task.setCommentList(wechatOaDao.getTaskComment(task));
        return task;
    }

    @Transactional(readOnly = false)
    public String finishTask(WechatTask wechatTask)
    {
        wechatOaDao.finishTask(wechatTask);
        return "success";

    }

    @Transactional(readOnly = false)
    public String delDayThing(WechatDayThing wechatDayThing)
    {
        wechatOaDao.delDayThing(wechatDayThing);
        return "success";
    }

    @Transactional(readOnly = false)
    public String delReport(WechatReport wechatReport)
    {
        wechatOaDao.delReport(wechatReport);
        return "success";
    }

    public List<KybWork> getWorkList(KybWork work) {
        return wechatOaDao.getWorkList(work);
    }

    @Transactional(readOnly = false)
    public String selDefaultImg(WechatUser wechatUser) {
        if (StringUtils.isNotBlank(wechatUser.getFileUrl())) {
            FileManageUtils.delFiles(wechatUser.getFileUrl());
        }
        wechatUser.setFileUrl(null);
        wechatOaDao.updateEmpImg(wechatUser);
        return "success";
    }

    @Transactional(readOnly = false)
    public String uploadFile(WechatUser wechatUser) {
        String savePath = "oa/emp" + "/" + wechatUser.getRecordId() + "/";
        String url = HttpRquestUtil.getWechatUrl();
        String webUrl = url + wechatUser.getServerId();
        String name = wechatUser.getRecordId() + 1 + ".jpg";
        String sourcePath = savePath + name;
        if (StringUtils.isNotBlank(wechatUser.getFileUrl())) {
            // 先删除存储的文件
            FileManageUtils.delFiles(wechatUser.getFileUrl());
        }
        FileManageUtils.wxUploadImg(webUrl, sourcePath);
        // 存储路径到数据库
        wechatUser.setFileUrl(sourcePath);
        wechatUser.setDefaultImg(null);
        wechatOaDao.updateEmpImg(wechatUser);
        return "success";
    }

    /**
     * zjn 2019-11-27 删除任务附件
     */
    @Transactional(readOnly = false)
    public String delTaskFile(WechatAttachments attach) {
        FileManageUtils.delFiles(attach.getFileUrl());
        wechatOaDao.delTaskFile(attach);
        return "success";
    }

    /**
     * zjn 2019-11-28 删除报告附件
     */
    @Transactional(readOnly = false)
    public String delReportFile(WechatAttachments attach) {
        FileManageUtils.delFiles(attach.getFileUrl());
        wechatOaDao.delReportFile(attach);
        return "success";
    }

    /**
     * 删除日程附件
     * @param attach
     * @return
     */
    @Transactional(readOnly = false)
    public String delDayThingFile(WechatAttachments attach) {
        FileManageUtils.delFiles(attach.getFileUrl());
        wechatOaDao.delDayThingFile(attach);
        return "success";
    }

    /**
     * zjn 2019-11-28 删除审批附件
     */
    @Transactional(readOnly = false)
    public String delAuditFile(WechatAttachments attach) {
        FileManageUtils.delFiles(attach.getFileUrl());
        wechatOaDao.delAuditFile(attach);
        return "success";
    }

    public WechatAudit getDepartureEmpInfo(WechatUser wechatUser) {
        return wechatOaDao.getDepartureEmpInfo(wechatUser);
    }

    public List<ExpenseType> getExpenseTypeList() {
        return wechatOaDao.getExpenseTypeList();
    }

    @Transactional(readOnly = false)
    public Map<String, List<?>> loadData(Material material) {
        Map<String, List<?>> map = new HashMap<>();
        material.setCompany(new Company(material.getCompanyId()));
        material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_RAW);
        material.setWlwCompanyId("23");
        List<Material> materialList = new ArrayList<>();
        if (null != material.getProduct() && StringUtils.isNotBlank(material.getProduct()) && material.getProduct().equals("1"))
        {
            materialList = materialDao.getMaterialLists(material);

        }
        else if (null != material.getProduct() && StringUtils.isNotBlank(material.getProduct()) && material.getProduct().equals("2"))
        {
            // 大生态圈产品
            materialList = materialDao.getInterProductList();
        }
        map.put("materialList", materialList);

        Department department = new Department();
        department.setCompanyId(material.getCompanyId());
        department.setPhone(material.getApplicanter().getPhone());

        // 获取员工的部门
        List<Department> defaultDepartList = departmentDao.getDefaultDepartList(department);

        // 获取公司的所有部门
        List<Department> departList = departmentDao.getDepartList(department);
        if (null != defaultDepartList && Collections3.isNotEmpty(defaultDepartList) && Collections3.isNotEmpty(departList))
        {
            Department department1 = defaultDepartList.get(0);
            if (null != department1)
            {
                int index = departList.indexOf(department1);
                departList.add(0,departList.remove(index));
            }
        }

        map.put("departList", departList);

        User user = new User();
        user.clone();
        user.setCompanyId(material.getCompanyId());
        user.setUserType(ConstKey.USER_TYPE_EMP);
        user.setStatus("(" + ConstKey.JOB_IN + ConstKey.COMMA + ConstKey.JOB_ADMIN + ")");
        List<User> userList = userDao.getMaterialUserList(user);
        map.put("userList", userList);

        // 获取仓库
        StoreHouse storeHouse = new StoreHouse();
        storeHouse.setCompanyId(material.getCompanyId());
        List<StoreHouse> storeHouseList = storeHouseDao.getStoreHouseList(storeHouse);
        map.put("storeHouseList", storeHouseList);
        return map;
    }

    /**
     * fzl 2023-06-29 小助手领用单审批
     */
    @Transactional(readOnly = false)
    public Map<String,Object> materialAudit(MaterialUse materialUse) {
        Map<String,Object> data = new HashMap<>();

        // 判断参数是否为空
        if (null == materialUse || Collections3.isEmpty(materialUse.getMaterialUseList()))
        {
            data.put("result","fail");
            data.put("fail","请完整物料领用!");
            return data;
        }
        // 创建新集合
        List<MaterialUse> materialUseList = new ArrayList<>();

        // 循环处理物料领用
        for (int i = 0; i < materialUse.getMaterialUseList().size(); i++)
        {
            MaterialUse materialUse1 = materialUse.getMaterialUseList().get(i).clone();
            materialUse1.setCompany(materialUse.getCompany());
            materialUse1.setDepart(materialUse.getDepart());
            if (null == materialUse.getCreatedBy() || StringUtils.isBlank(materialUse.getCreatedBy().getRecordId()) || materialUse.getCreatedBy().getRecordId().equals(""))
            {
                materialUse1.setCreatedBy(materialUse.getUser());
            }
            else
            {
                materialUse1.setCreatedBy(materialUse.getCreatedBy());
            }
            materialUse1.setType(materialUse.getType());
            materialUse1.setUser(materialUse.getUser());
            materialUse1.setStockPlaceComId(materialUse.getStockPlaceComId());
            materialUse1.setTypeA(materialUse.getTypeA());
            materialUse1.setTypeId(materialUse.getTypeId());
            materialUse1.setStoreHouse(materialUse.getStoreHouse());
            // 领用单编号
            String materialNo =  CommonUtils.geDocumentNo(CommonEnums.CodeType.MATERIALUSE.getIndex().toString(),materialUse.getCompany());
            Integer noNum = materialUseDao.findMaterialUseNo(materialNo, materialUse.getCompany().getRecordId());
            while(noNum !=null && noNum >0){
                CommonUtils.updateNextNo(CommonEnums.CodeType.MATERIALUSE.getIndex(), materialUse.getCompany());
                materialNo = CommonUtils.geDocumentNo(CommonEnums.CodeType.MATERIALUSE.getIndex().toString(),materialUse.getCompany());
                noNum = materialUseDao.findMaterialUseNo(materialNo,materialUse.getCompany().getRecordId());
            }
            materialUse1.setNo(materialNo);

            // 获取物料库位id
            PurchRaw purchRaw = new PurchRaw();
            purchRaw.setCompany(materialUse.getCompany());
            purchRaw.setStockPlaceId(materialUse.getStockPlaceId());
            purchRaw.setMaterial(materialUse.getMaterialUseList().get(i).getMaterial());

            RawStockUtil rawStockUtil = new RawStockUtil();
            String id = rawStockUtil.genStockPlaceCom(purchRaw);
            materialUse1.setStockPlaceComId(id);

            // 确认领用单
            materialUse1.setStatus(TypeKey.MATERIAL_USE_STATUS_CONFIRMED);
            materialUse1.setApplyDate(DateUtils.parseDate(DateUtils.getDate()));
            // 获取物料单价
            BigDecimal price = BigDecimal.ZERO;
            if (null != materialUse.getProduct() && StringUtils.isNotBlank(materialUse.getProduct()) && materialUse.getProduct().equals("2"))
            {
                price = materialDao.getRawmaterialPrice(materialUse1.getMaterial().getRecordId());
            }
            else
            {
                price = rawStockUtil.getWAAPrice(materialUse1.getMaterial().getRecordId(), materialUse1.getApplyDate());
            }
            materialUse1.setPrice(price);
            materialUse1.setProduct(materialUse.getProduct());
            materialUse1.preInsert();
            materialUseDao.insert(materialUse1);
            CommonUtils.updateNextNo(CommonEnums.CodeType.MATERIALUSE.getIndex());

            /*MaterialUse mu = materialUseDao.getReceipt(materialUse1);*/
            // 判断出需要进行审批的物料领用单，进行审批
            if(!materialUse1.getCompany().getRecordId().equals("23"))
            {
                if (materialUse1.getMaterial() != null && (materialUse1.getMaterial().getMaterialType() != null
                    && materialUse1.getMaterial().getMaterialType().getMaterialRequisition() != null
                    && materialUse1.getMaterial().getMaterialType().getMaterialRequisition().equals("1") || materialUse1.getProduct().equals("2"))) {
                    materialUseList.add(materialUse1);
                }
            }
            else
            {
                if (materialUse1.getMaterial() != null && materialUse1.getMaterial().getMaterialType() != null) {
                    materialUseList.add(materialUse1);
                }
            }
        }
        String result = ModifyUtils.wxMaterialApproval(materialUseList);
        if(StringUtils.isNotBlank(result) && !"fail".equals(result))
        {
            data.put("result","success");
            data.put("message",result);
        }
        else
        {
            data.put("result","success");
            data.put("message","保存成功！");
        }
        return data;
    }

    public WechatUser getSuperiorLeaders(WechatUser wechatUser) {
        wechatUser.setPhone(wechatUser.getPhone());
        WechatUser superiorLeaders = wechatOaDao.getSuperiorLeaders(wechatUser);
        return superiorLeaders;
    }

    public Map<String,List<?>> getSupplychainApplyData()
    {
        Map<String,List<?>> data = new HashMap<>();

        List<Hr_DepartRelation> groupCompanyList = wechatOaDao.getGroupDepartmentRelationList();
        data.put("groupCompanyList",groupCompanyList);

        if(Collections3.isNotEmpty(groupCompanyList))
        {
            String companyIds = null;
            for(Hr_DepartRelation group : groupCompanyList)
            {
                if(StringUtils.isBlank(group.getBindErpId()))
                {
                    continue;
                }
                if(StringUtils.isBlank(group.getSupplyFlag()))
                {
                    if(StringUtils.isNotBlank(companyIds))
                    {
                        companyIds = companyIds +","+ group.getBindErpId();
                    }
                    else
                    {
                        companyIds = group.getBindErpId();
                    }
                }
            }
            if(StringUtils.isNotBlank(companyIds))
            {
                Customer cus = new Customer();
                cus.setCompanyId(companyIds);
                List<Customer> customerList = wechatOaDao.getCustomerList(cus);
                data.put("customerList",customerList);
            }
        }
        return data;
    }
    public Map<String,Object> loadOrderDeliveryTimeData(Company company)
    {
        Map<String,Object> data = new HashMap<>();
        if(null == company)
        {
            return data;
        }
        List<Company> factCompanyList = companyDao.getProduceCompanyList(company);
        data.put("factCompanyList",factCompanyList);

        // 根据手机号码匹配用户归属与的销售部门
        List<GroupOrgRelation> groupList = departmentDao.getGroupListByPhone(company.getPhone());
        if(Collections3.isEmpty(groupList))
        {
            groupList = departmentDao.getGroupList(new User());
        }
        if(Collections3.isNotEmpty(groupList) && groupList.size() == 1)
        {
            data.put("groupId",groupList.get(0).getGroupOrgId());
        }
        else
        {
            data.put("groupList",groupList);
        }
        return data;
    }

    public Object showAuditData(String name, String auditId)
    {
        if (StringUtils.isBlank(name) || StringUtils.isBlank(auditId)){
            System.out.println("调用方法名字不能为空");
            return null;
        }
        try {
            ErpAuditUtils util = new ErpAuditUtils();
            Method method = ErpAuditUtils.class.getMethod(name, String.class);
            return method.invoke(util, auditId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<ContractDetail> getOrderDetailList(ContractDetail contractDetail)
    {
        return contractDetailDao.getOrderDetailList(contractDetail);
    }

    public List<Hr_DepartRelation> getSaleCompanyList(WechatAudit audit)
    {
        return companyDao.getSaleCompanyList(audit);
    }

    public List<Customer> getSaleCustomerList(Customer customer)
    {
        return companyDao.getSaleCustomerList(customer);
    }

    public List<Hr_DepartMent> getOrganizationAuditList() {
        List<Hr_DepartMent> departList = wechatOaDao.getOrganizationAuditList();
        departList.sort((x, y) -> ((null == x.getDepartmentLevel() ? "0" : x.getDepartmentLevel())
                .compareTo(null == y.getDepartmentLevel() ? "0" : y.getDepartmentLevel())));
        return departList;
    }

    public List<WechatAudit> getCategoryAuditList()
    {
        return wechatOaDao.getCategoryAuditList();
    }

    public List<CapacityDeail> getCapacityDeailList(Date deliveryDate)
    {
        if (null != deliveryDate)
        {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String deliveryDateSte = dateFormat.format(deliveryDate);
            return contractDetailDao.getCapacityDeailList(deliveryDateSte);
        }
        return null;
    }

    public List<Hr_Regimen> getRegimenManagementList(Hr_Regimen hr_regimen)
    {
        List<Hr_Regimen> list = wechatOaDao.getRegimenManagementList(hr_regimen);
        //制度附件
        List<Hr_KybAttachments> attachmentList = wechatOaDao.getAttachmentList(list);
        if(Collections3.isNotEmpty(attachmentList)) {
            for(Hr_Regimen regimen : list) {
                regimen.setAttachmentList(Lists.newArrayList());
                for(Hr_KybAttachments atta : attachmentList) {
                    if(atta.getRegimenId().equals(regimen.getRecordId()))
                    {
                        regimen.getAttachmentList().add(atta);
                    }
                }
            }
        }
        return list;
    }

    @Transactional(readOnly = false)
    public String addCasherMange (Hr_CollectPayBill hr_collectPayBill)
    {
        if (null == hr_collectPayBill)
        {
            return "数据错误，请刷新重试!";
        }
        if (Collections3.isEmpty(hr_collectPayBill.getCollectPayDetailList()))
        {
            return "请填写需要保存记录!";
        }
        if (StringUtils.isNotBlank(hr_collectPayBill.getRecordId()))
        {
            hr_certificateDao.updateCollectPayBill(hr_collectPayBill);
        }else{
            hr_certificateDao.insertCollectPayBill(hr_collectPayBill);
        }

        for(Hr_CollectPayBill collectPayBill : hr_collectPayBill.getCollectPayDetailList())
        {
            collectPayBill.setReceiptPaymentId(hr_collectPayBill.getRecordId());
            collectPayBill.setEmployeeId(hr_collectPayBill.getEmployeeId());
            if (StringUtils.isNotBlank(collectPayBill.getRecordId()))
            {
                hr_certificateDao.updateCashManage(collectPayBill);
            }else{
                hr_certificateDao.addCashManage(collectPayBill);
            }
        }
        return "保存成功!";
    }

    public List<DictValue> getCategoryList(WechatAudit audit){
        DictValue dictValue = new DictValue();
        dictValue.setItemId("183");
        return dictValueDao.getCategoryList(dictValue);
    }
}
