package com.kyb.pcberp.modules.wechat.web.front;

import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.*;
import com.kyb.pcberp.modules.wechat.service.KybSoftMsgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Controller
@RequestMapping(value = "${frontPath}/wechat/kybsoftMsg/")
public class KybSoftMsgController
{
    @Autowired
    private KybSoftMsgService kybSoftMsgService;
    
    @RequestMapping(value = "")
    @ResponseBody
    public void getMsgList(HttpServletRequest request, HttpServletResponse response)
    {
        
    }
    
    /** tj 我的圈子 */
    @RequestMapping(value = "myCircle")
    @ResponseBody
    public List<WechatCircle> myCircle(@RequestBody WechatCircle circle, HttpServletRequest request,
        HttpServletResponse response)
    {
        return kybSoftMsgService.myCircle(circle);
    }
    
    /** tj 圈子动态 */
    @RequestMapping(value = "circleDynamic")
    @ResponseBody
    public List<WechatPost> circleDynamic(@RequestBody WechatPost wechatPost, HttpServletRequest request,
        HttpServletResponse response)
    {
        return kybSoftMsgService.circleDynamic(wechatPost);
    }
    
    /** tj 获取我的云平台公司 */
    @RequestMapping(value = "myCompany")
    @ResponseBody
    public List<Company> myCompany(@RequestBody WechatUser wechatUser, HttpServletRequest request,
        HttpServletResponse response)
    {
        return kybSoftMsgService.myCompany(wechatUser);
    }
    
    /** tj 发布动态 */
    @RequestMapping(value = "savePost")
    @ResponseBody
    public String savePost(@RequestBody WechatPost wechatPost, HttpServletRequest request, HttpServletResponse response)
    {
        return kybSoftMsgService.savePost(wechatPost);
    }
    
    /** tj 动态明细 */
    @RequestMapping(value = "getPost")
    @ResponseBody
    public WechatPost getPost(@RequestBody WechatPost wechatPost, HttpServletRequest request,
        HttpServletResponse response)
    {
        return kybSoftMsgService.getPost(wechatPost);
    }
    
    /** tj 保存动态评论回复 */
    @RequestMapping(value = "addPostComment")
    @ResponseBody
    public String addPostComment(@RequestBody WechatComment wechatComment, HttpServletRequest request,
        HttpServletResponse response)
    {
        return kybSoftMsgService.addPostComment(wechatComment);
    }
    
    /** 添加/修改圈子 */
    @RequestMapping(value = "saveCircle", method = RequestMethod.POST)
    @ResponseBody
    public String saveCircle(@RequestBody WechatCircle wechatCircle)
    {
        return kybSoftMsgService.saveCircle(wechatCircle);
    }
    
    /** 加入圈子 */
    @RequestMapping(value = "joinCircle", method = RequestMethod.POST)
    @ResponseBody
    public String joinCircle(@RequestBody WechatCircle circle)
    {
        return kybSoftMsgService.joinCircle(circle);
    }
    
    /** 退出圈子 */
    @RequestMapping(value = "outCircle", method = RequestMethod.POST)
    @ResponseBody
    public String outCircle(@RequestBody WechatCircle circle)
    {
        return kybSoftMsgService.outCircle(circle);
    }
    
    /** 删除圈子 */
    @RequestMapping(value = "delCircle", method = RequestMethod.POST)
    @ResponseBody
    public String delCircle(@RequestBody WechatCircle circle)
    {
        return kybSoftMsgService.delCircle(circle);
    }
    
    /** tj 2019-08-05 获取圈子编号 */
    @RequestMapping(value = "getCircleNo", method = RequestMethod.POST)
    @ResponseBody
    public String getCircleNo()
    {
        return kybSoftMsgService.getCircleNo();
    }
    
    /** tj 2019-08-05 验证圈子编号是否存在 */
    @RequestMapping(value = "verificationNo", method = RequestMethod.POST)
    @ResponseBody
    public String verificationNo(@RequestBody WechatCircle circle)
    {
        return kybSoftMsgService.verificationNo(circle);
    }
    
    /** tj 2019-08-05 获取圈子所有用户 */
    @RequestMapping(value = "getCircleUserList", method = RequestMethod.POST)
    @ResponseBody
    public List<WechatCircleUser> getCircleUserList(@RequestBody WechatCircle circle)
    {
        return kybSoftMsgService.getCircleUserList(circle);
    }
    
    /** tj 2019-08-05 获取圈子*/
    @RequestMapping(value = "getCircle", method = RequestMethod.POST)
    @ResponseBody
    public WechatCircle getCircle(@RequestBody WechatCircle wechatCircle)
    {
        return kybSoftMsgService.getCircle(wechatCircle);
    }
    
    /** tj 2019-08-05 查询圈子里的所有帖子 */
    @RequestMapping(value = "getCirclePost", method = RequestMethod.POST)
    @ResponseBody
    public List<WechatPost> getCirclePost(@RequestBody WechatCircle wechatCircle)
    {
        return kybSoftMsgService.getCirclePost(wechatCircle);
    }
    
    /** tj 2019-08-05 增加浏览量 */
    @RequestMapping(value = "updateBrowse", method = RequestMethod.POST)
    @ResponseBody
    public void updateBrowse(@RequestBody WechatPost wechatPost)
    {
        kybSoftMsgService.updateBrowse(wechatPost);
    }
    
    /** tj 2019-08-05 增加浏览量且标记已读 */
    @RequestMapping(value = "updateBrowseRead", method = RequestMethod.POST)
    @ResponseBody
    public void updateBrowseRead(@RequestBody WechatComment wechatComment)
    {
        kybSoftMsgService.updateBrowseRead(wechatComment);
    }
    
    /** tj 2019-08-05 我的私信 */
    @RequestMapping(value = "privateLetter", method = RequestMethod.POST)
    @ResponseBody
    public List<WechatComment> privateLetter(@RequestBody WechatUser wechatUser)
    {
        return kybSoftMsgService.privateLetter(wechatUser);
    }
    
    /** tj 2019-08-05 全部已读 */
    @RequestMapping(value = "allRead", method = RequestMethod.POST)
    @ResponseBody
    public String allRead(@RequestBody List<WechatComment> commentList)
    {
        return kybSoftMsgService.allRead(commentList);
    }
    
    /** tj 2019-08-05 未读公告 */
    @RequestMapping(value = "getNoticeList", method = RequestMethod.POST)
    @ResponseBody
    public List<WechatComment> getNoticeList(@RequestBody WechatUser wechatUser)
    {
        return kybSoftMsgService.getNoticeList(wechatUser);
    }
    
    /** tj 2019-08-06 获取所有圈子 */
    @RequestMapping(value = "getAllCircle", method = RequestMethod.POST)
    @ResponseBody
    public List<WechatCircle> getAllCircle(@RequestBody WechatCircle wechatCircle)
    {
        return kybSoftMsgService.getAllCircle(wechatCircle);
    }
    
    /** tj 2019-08-06 帖子结案 */
    @RequestMapping(value = "closingACase", method = RequestMethod.POST)
    @ResponseBody
    public String closingACase(@RequestBody WechatPost wechatPost)
    {
        return kybSoftMsgService.closingACase(wechatPost);
    }
    
    /** tj 2019-08-06 申请合作（帖子） */
    @RequestMapping(value = "cooperation", method = RequestMethod.POST)
    @ResponseBody
    public String cooperation(@RequestBody WechatComment wechatComment)
    {
        return kybSoftMsgService.cooperation(wechatComment);
    }
    
    /** tj 2019-08-07 申请合作（成员） */
    @RequestMapping(value = "cooperationMan", method = RequestMethod.POST)
    @ResponseBody
    public String cooperationMan(@RequestBody WechatSupCom sc)
    {
        return kybSoftMsgService.cooperationMan(sc);
    }
    
    /** tj 2019-08-07 申请合作通过（成员） */
    @RequestMapping(value = "cooperationManAdopt", method = RequestMethod.POST)
    @ResponseBody
    public String cooperationManAdopt(@RequestBody WechatSupCom sc)
    {
        return kybSoftMsgService.cooperationManAdopt(sc);
    }
    
    /** tj 2019-08-08 入圈申请 */
    @RequestMapping(value = "enterCircleApply", method = RequestMethod.POST)
    @ResponseBody
    public String enterCircleApply(@RequestBody WechatCircleUser circleUser)
    {
        return kybSoftMsgService.enterCircleApply(circleUser);
    }
    
    /** tj 2019-08-08 申请加入圈子人员 */
    @RequestMapping(value = "getApplyCircleList", method = RequestMethod.POST)
    @ResponseBody
    public List<WechatCircleUser> getApplyCircleList(@RequestBody WechatCircle circle)
    {
        return kybSoftMsgService.getApplyCircleList(circle);
    }
    
    /** tj 2019-08-08 同意加入圈子 */
    @RequestMapping(value = "agreeJoin", method = RequestMethod.POST)
    @ResponseBody
    public String agreeJoin(@RequestBody WechatCircleUser circleUser)
    {
        return kybSoftMsgService.agreeJoin(circleUser);
    }
}
