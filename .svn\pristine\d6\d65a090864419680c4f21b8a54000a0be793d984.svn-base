<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.contract.dao.DeliveryDetailDao">
    <resultMap id="DeliveryDetailMap" type="DeliveryDetail">
		<id property="recordId" column="recordId" />
		<result property="quantity" column="quantity" />
		<result property="spares" column="spares" />
		<result property="amount" column="amount" />
		<result property="actualRecQty" column="actualRecQty" />
		<result property="actualAmount" column="actualAmount" />
		<result property="createdDate" column="createdDate" />
		<result property="lastUpdDate" column="lastUpdDate" />
		<result property="status" column="status" />
		<result property="remark" column="remark" />
		<result property="cycle" column="cycle" />
		<result property="saleName" column="saleName" />
		<result property="finalNo" column="finalNo" />
		<result property="terminalDetailId" column="terminalDetailId" />
		<result property="oldDelDetailId" column="oldDelDetailId" />
		<result property="saleComId" column="saleComId" />
		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
			<result property="name" column="company.name"/>
		</association>
		<association property="createdBy" column="createdBy" javaType="User">
			<id property="recordId" column="createdBy.recordId"/>
		</association>
		<association property="lastUpdBy" column="lastUpdBy" javaType="User">
			<id property="recordId" column="lastUpdBy.recordId"/>
		</association>
		<association property="delivery" column="deliveryId" javaType="Delivery">
			<id property="recordId" column="delivery.recordId"/>
			<result property="no" column="delivery.no"/>
			<result property="itemType" column="delivery.itemType"/>
			<result property="lastUpdDate" column="delivery.lastUpdDate"/>
			<association property="customer" column="customerId" javaType="Customer">
				<id property="recordId" column="delivery.customerId"/>
			</association>
		</association>
		<association property="contractDetail" column="contractDetail.recordId" javaType="ContractDetail" select="selectContractDetailById"/>
		<association property="material" column="material.recordId" javaType="Material" select="selectMaterialById"/>
		
		<association property="complaint" javaType="RejectApplication">
			<id property="recordId" column="complaint.recordId"/>
			<result property="no" column="complaint.no"/>
			<result property="alDeliveryQtys" column="complaint.alDeliveryQtys"/>
			<result property="quantity" column="complaint.quantity"/>
			<result property="amount" column="complaint.amount"/>
			<result property="desireTreatment" column="complaint.desireTreatment"/>
			<result property="style" column="complaint.style"/>
			<association property="contractDetail" javaType="ContractDetail">
				<id property="recordId" column="complaint.contractDetail.recordId"/>
				<id property="alInHouseRejQty" column="complaint.contractDetail.alInHouseRejQty"/>
				<id property="alDeliveryRejQty" column="complaint.contractDetail.alDeliveryRejQty"/>
				<id property="oaRejQty" column="complaint.contractDetail.oaRejQty"/>
			</association>
			<association property="material" column="complaint.material.recordId" javaType="Material" select="selectMaterialById"/>
		</association>
	</resultMap>
	
    <resultMap id="contractDetailMap" type="ContractDetail">
		<id property="recordId" column="recordId" />
		<result property="company.recordId" column="companyId" />
		<!-- <result property="price" column="price" /> -->
		<result property="quantity" column="quantity" />
		<result property="subTotal" column="subTotal" />
		<result property="deliveryDays" column="deliveryDays" />
		<result property="customerFileName" column="customerFileName" />
		<result property="customerMaterialNo" column="customerMaterialNo" />
		<result property="status" column="status" />
		<result property="referenceType" column="referenceType" /> 
		<result property="referenceDesc" column="referenceDesc" />
		<result property="deliveryQty" column="deliveryQty" />
		<result property="stockQty" column="stockQty" />
		<result property="alInHouseQty" column="alInHouseQty" />
		<result property="alDeliveryQty" column="alDeliveryQty" />
		<result property="oaQty" column="oaQty" />
		<result property="createdDate" column="createdDate" />
		<result property="lastUpdDate" column="lastUpdDate" />
		<result property="remark" column="remark" />
		<result property="deposit" column="deposit" />
		<result property="deliveryDate" column="deliveryDate" />
		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
			<result property="name" column="company.name"/>
		</association>
		<association property="contract" column="contractId" javaType="Contract" select="selectContractById"/>
		<association property="craft" column="craftId" javaType="ContractCraft" select="selectContractCraftById"/>
		<association property="pricees" column="priceId" javaType="Price" select="selectPriceByContractId"/>
	</resultMap>
	
	<resultMap id="contractMap" type="Contract">
		<id property="recordId" column="recordId" />
		<result property="company.recordId" column="companyId" />
		<result property="no" column="no" />
		<result property="customerPo" column="customerPo" />
		<result property="orderDate" column="orderDate" />
		<result property="totalAmt" column="totalAmt" />
		<result property="taxDescript" column="taxDescript" />
		<result property="currencyType" column="currencyType" />
		<result property="payWay" column="payWay" />
		<result property="payDays" column="payDays" /> 
		<result property="prePayAmount" column="prePayAmount" />
		<result property="prePayBalance" column="prePayBalance" />
		<result property="deliveryWay" column="deliveryWay" />
		<result property="deliveryPlace" column="deliveryPlace" />
		<result property="freightWay" column="freightWay" /> 
		<result property="chkAcptWay" column="chkAcptWay" />
		<result property="chkDissentDays" column="chkDissentDays" />
		<result property="qualityStd" column="qualityStd" />
		<result property="assureDays" column="assureDays" />
		<result property="packagingStd" column="packagingStd" /> 
		<result property="status" column="status" />
		<result property="cancelReason" column="cancelReason" />
		<!-- <result property="contractId" column="contractId" /> -->
		<result property="notificationId" column="notificationId" />
		<result property="createdDate" column="createdDate" />
		<result property="lastUpdDate" column="lastUpdDate" />
		<result property="remark" column="remark" />
		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
			<result property="name" column="company.name"/>
		</association>
		
		<association property="createdBy" column="createdBy" javaType="User">
			<id property="recordId" column="createdBy.recordId"/>
		</association>
		
		<association property="lastUpdBy" column="lastUpdBy" javaType="User">
			<id property="recordId" column="lastUpdBy.recordId"/>
		</association>
		<association property="customer" column="customerId" javaType="Customer" select="selectCustomerById"/>
	</resultMap>
    
    <resultMap id="materialMap" type="Material">
		<id property="recordId" column="recordId" />
		<result property="company.recordId" column="companyId" />
		<result property="no" column="no" />
		<result property="name" column="name" />
		<result property="specification" column="specification" />
		<result property="manufacturer" column="manufacturer" />
		<result property="lowerLimit" column="lowerLimit" />
		<result property="version" column="version" />
		<result property="stocks" column="stocks" />
		<result property="status" column="status" />
		<result property="availableQty" column="availableQty" />
		<result property="applicant" column="applicant" /> 
		<result property="majorFlag" column="majorFlag" />
		<result property="materialKind" column="materialKind" />
		<result property="customerNo" column="customerNo" />
		<result property="createdDate" column="createdDate" />
		<result property="lastUpdDate" column="lastUpdDate" />
		<result property="remark" column="remark" />
		<result property="deliveryOaQty" column="deliveryOaQty" />
		<result property="depositQty" column="depositQty" />
		<result property="depositOaQty" column="depositOaQty" />
		<result property="deliverySize" column="deliverySize" />
		<result property="status" column="status" />
		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
			<result property="name" column="company.name"/>
		</association>
		<association property="createdBy" column="createdBy" javaType="User">
			<id property="recordId" column="createdBy.recordId"/>
		</association>
		
		<association property="lastUpdBy" column="lastUpdBy" javaType="User">
			<id property="recordId" column="lastUpdBy.recordId"/>
		</association>
		<association property="storehouse" column="storehouseId" javaType="StoreHouse" select="selectStoreHouseById"/>
	</resultMap>
	
    <resultMap id="DeliveryDetailByIdMap" type="DeliveryDetail">
		<id property="recordId" column="recordId" />
		<result property="quantity" column="quantity" />
		<result property="spares" column="spares" />
		<result property="actualRecQty" column="actualRecQty" />
		<result property="status" column="status" />
		<result property="remark" column="remark" />
		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
		</association>
		<association property="delivery" column="deliveryId" javaType="Delivery">
			<id property="recordId" column="delivery.recordId"/>
		</association>
		<association property="material" column="materialId" javaType="Material">
			<id property="recordId" column="material.recordId"/>
			<result property="deliveryOaQty" column="material.deliveryOaQty"/>
			<result property="stocks" column="material.stocks"/>
			<result property="depositQty" column="material.depositQty"/>
			<result property="depositOaQty" column="material.depositOaQty"/>
		</association>
		<association property="complaint" javaType="RejectApplication">
			<id property="recordId" column="complaint.recordId"/>
			<result property="style" column="complaint.style" />
			<association property="contractDetail" javaType="ContractDetail">
				<id property="recordId" column="complaint.contractDetail.recordId"/>
				<result property="alInHouseRejQty" column="complaint.contractDetail.alInHouseRejQty" />
				<result property="alDeliveryRejQty" column="complaint.contractDetail.alDeliveryRejQty" />
				<result property="oaRejQty" column="complaint.contractDetail.oaRejQty" />
			</association>
		</association>
		<association property="contractDetail" column="contractDetail.recordId" javaType="ContractDetail" select="findContractDetailById"/>
	</resultMap>
	
    <resultMap id="contractDetailByIdMap" type="ContractDetail">
		<id property="recordId" column="recordId" />
		<result property="company.recordId" column="companyId" />
		<result property="quantity" column="quantity" />
		<result property="subTotal" column="subTotal" />
		<result property="status" column="status" />
		<result property="deliveryQty" column="deliveryQty" />
		<result property="alInHouseQty" column="alInHouseQty" />
		<result property="alDeliveryQty" column="alDeliveryQty" />
		<result property="alInHouseRejQty" column="alInHouseRejQty" />
		<result property="alDeliveryRejQty" column="alDeliveryRejQty" />
		<result property="oaQty" column="oaQty" />
		<result property="oaRejQty" column="oaRejQty" />
		<result property="stockQty" column="stockQty" />
		<association property="pricees" column="priceId" javaType="Price" select="selectPriceByContractId"/>
	</resultMap>
    
     <select id="findContractDetailById" parameterType="Long" resultMap="contractDetailByIdMap">
    	select * from sl_contract_detail where recordId=#{recordId}
    </select>
     <select id="selectContractDetailById" parameterType="Long" resultMap="contractDetailMap">
    	select * from sl_contract_detail where recordId=#{recordId}
    </select>
     <select id="selectContractById" parameterType="Long" resultMap="contractMap">
    	select * from sl_contract where recordId=#{recordId}
    </select>
    <select id="selectContractCraftById" parameterType="Long" resultType="ContractCraft">
    	select * from sl_contract_craft where recordId=#{recordId}
    </select>
    <select id="selectPriceByContractId" parameterType="Long" resultType="Price">
    	select * from sl_price where recordId=#{recordId}
    </select>
    <select id="selectCustomerById" parameterType="Long" resultType="Customer">
    	select recordId,no,name,shortName from md_customer where recordId=#{recordId}
    </select>
    
    <select id="selectMaterialById" parameterType="Long" resultMap="materialMap">
    	select * from md_material where recordId=#{recordId}
    </select>
    <select id="selectStoreHouseById" parameterType="Long" resultType="StoreHouse">
    	select recordId,name,type from md_store_house where recordId=#{recordId}
    </select>
    
	<sql id="slDeliveryDetailColumns">
		a.recordid AS "recordId",
		a.companyid AS "company.recordId",
		a.deliveryid AS "delivery.recordId",
		a.materialid AS "material.recordId",
		a.contractdetailid AS "contractDetail.recordId",
		a.quantity AS "quantity",
		a.spares AS "spares",
		a.amount AS "amount",
		a.actualrecqty AS "actualRecQty",
		a.actualamount AS "actualAmount",
		a.activeflag AS "activeFlag",
		a.createdby AS "createdBy.recordId",
		a.createddate AS "createdDate",
		a.lastupdby AS "lastUpdBy.recordId",
		a.lastupddate AS "lastUpdDate",
		a.status AS "status",
		a.remark AS "remark",
		a.complaintId AS "complaint.recordId",
		a.cycle AS "cycle",
		a.terminalDetailId,
		a.oldDelDetailId
	</sql>
	
	<sql id="slDeliveryDetailJoins">
		LEFT JOIN md_company mc ON mc.recordId = a.companyId
	</sql>
	
	<!-- 客诉送货单 打印 -->
	
	<select id="findDeliveryDetailByDeliveryAndRejectApplication" resultType="DeliveryDetail">
		SELECT  
			a.recordid AS "recordId",
			a.companyid AS "company.recordId",
			a.deliveryid AS "delivery.recordId",
			a.materialid AS "material.recordId",
			a.contractdetailid AS "contractDetail.recordId",
			a.quantity AS "quantity",
			a.spares AS "spares",
			a.amount AS "amount",
			a.actualrecqty AS "actualRecQty",
			a.actualamount AS "actualAmount",
			a.activeflag AS "activeFlag",
			a.createdby AS "createdBy.recordId",
			a.createddate AS "createdDate",
			a.lastupdby AS "lastUpdBy.recordId",
			a.lastupddate AS "lastUpdDate",
			a.status as "status",
			a.remark AS "remark",
			a.complaintId as "complaint.recordId",
			
			b.no AS "complaint.no",
			b.materialId AS "complaint.material.recordId",
			b.customerId AS "complaint.customer.recordId",
			b.quantity as "complaint.quantity",
			
			c.customerFileName AS "contractDetail.customerFileName",
			
			d.no AS "contractDetail.craft.no",
			d.unitWidth AS "contractDetail.craft.unitWidth",
			d.unitLength AS "contractDetail.craft.unitLength",
			d.materialType AS "contractDetail.craft.materialType",
			d.boardThickness AS "contractDetail.craft.boardThickness",
			d.copperCladThickness AS "contractDetail.craft.copperCladThickness",
			d.daore AS "contractDetail.craft.daore",
			d.customerModel AS "contractDetail.craft.customerModel",
			
			f.price AS "contractDetail.pricees.price",
			f.engineeringFee AS "contractDetail.pricees.engineeringFee",
			
			e.customerPo AS "contractDetail.contract.customerPo",
			e.payWay AS "contractDetail.contract.payWay",
			e.taxDescript AS "contractDetail.contract.taxDescript"
		FROM sl_delivery_detail a
		LEFT JOIN st_reject_application b on a.complaintId = b.recordId
		LEFT JOIN sl_contract_detail c on b.contractDetailId = c.recordId
		LEFT JOIN sl_contract_craft d on c.craftId = d.recordId
		LEFT JOIN sl_price f on f.recordId = c.priceId
		LEFT JOIN sl_contract e ON e.recordId = c.contractId
		WHERE a.deliveryId = #{recordId}
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	
    
	<select id="get" resultType="DeliveryDetail">
		SELECT 
			<include refid="slDeliveryDetailColumns"/>
		FROM sl_delivery_detail a
		<include refid="slDeliveryDetailJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="getQty" resultType="String">
	    select sum(a.quantity) 
	    from sl_delivery_detail a
	    left join sl_delivery b on b.recordId = a.deliveryId
	    where a.contractDetailId =#{recordId} 
	    and a.activeFlag = #{DEL_FLAG_NORMAL} 
	    and b.itemType = 1
	</select>
	
	<select id="findList" resultMap="DeliveryDetailMap">
		SELECT 
			<include refid="slDeliveryDetailColumns"/>,
			mc.name as "company.name",
			dly.no as "delivery.no",
			dly.customerId as "delivery.customerId",
			dly.itemType as "delivery.itemType",
			dly.lastUpdDate as "delivery.lastUpdDate",
			slco.no as "contractDetail.contract.no",
			scd.deposit as "contractDetail.deposit",
			scd.status as "contractDetail.status",
			ra.style as "complaint.style",
			mc2.`no` AS "finalNo",
			mb.`name` AS "saleName",
			mb.recordId AS "saleComId"
		FROM sl_delivery_detail a
		<include refid="slDeliveryDetailJoins"/>
		LEFT JOIN sl_delivery dly ON dly.recordId = a.deliveryId AND dly.companyId = a.companyId AND dly.activeFlag = 1
		LEFT JOIN md_material mmd ON mmd.recordId = a.materialId AND mmd.companyId = a.companyId AND mmd.activeFlag = 1
		LEFT JOIN sl_contract_detail scd ON scd.recordId = a.contractdetailid AND scd.companyId = a.companyId AND scd.activeFlag = 1
		LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId AND scc.companyId = a.companyId AND scc.activeFlag = 1
		LEFT JOIN sl_contract slco ON slco.recordId = scd.contractId AND slco.companyId = a.companyId AND slco.activeFlag = 1
		LEFT JOIN st_reject_application ra ON ra.recordId = a.complaintId AND ra.companyId = a.companyId AND ra.activeFlag = 1
		LEFT JOIN md_customer cus ON cus.recordId = dly.customerId AND cus.companyId = a.companyId AND cus.activeFlag = 1
		LEFT JOIN icloud_group_center igc ON igc.recordId = scd.groupCenterId
		LEFT JOIN sl_contract_detail scd2 ON scd2.recordId = igc.contactDeailId
		LEFT JOIN sl_contract sc2 ON sc2.recordId = scd2.contractId
		LEFT JOIN md_customer mc2 ON mc2.recordId = sc2.customerId
		LEFT JOIN md_branch mb ON mb.bindErpComId = mc2.companyId AND mb.companyId=#{company.recordId} AND mb.activeFlag=1
		<where>
		    a.companyId = #{company.recordId} AND a.activeFlag =1
			<if test="status!=null and status!=''">
			    AND dly.status=#{status} AND mmd.materialKind=100702
			</if>
			<if test="contractCraftNo!=null and contractCraftNo!=''">
				AND scc.no like CONCAT('%', #{contractCraftNo}, '%')
			</if>
			<if test="finalNo!=null and finalNo!=''">
				AND mc2.`no` like CONCAT('%', #{finalNo}, '%')
			</if>
			<if test="saleName!=null and saleName!=''">
				AND mb.`name` like CONCAT('%', #{saleName}, '%')
			</if>
			<if test="removalType!=null and removalType!='' and removalType==1">
				AND (SELECT IFNULL(sum(s.quantity), 0) FROM st_product_store s
				WHERE s.companyId = a.companyId
					AND STATUS <![CDATA[<>]]> 99999902
					AND s.deliveryDetailId = a.recordid
					AND s.materialId=a.materialId
				) <![CDATA[<]]> (a.quantity) 
			</if>
			<if test="removalType!=null and removalType!='' and removalType==2">
				and (SELECT IFNULL(sum(s.quantity), 0) FROM st_product_store s
				WHERE s.companyId = a.companyId
					AND STATUS <![CDATA[<>]]> 99999902
					AND s.deliveryDetailId = a.recordId
					AND s.materialId=a.materialId
				) <![CDATA[>=]]> (a.quantity) 
			</if>
			<if test="delivery!=null and delivery.no!=null and delivery.no!=''">
			    AND REPLACE(dly.no," ","") LIKE CONCAT('%',REPLACE(#{delivery.no}," ",""),'%')
			</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (
					dly.createdBy = #{createdBy.recordId}
					OR cus.createdBy = #{createdBy.recordId}
					OR(!ISNULL(cus.salesman) AND cus.salesman = #{createdBy.recordId})
					OR cus.recordId IN (SELECT customerId FROM sm_customer_salesAssistant WHERE userId = #{createdBy.recordId} AND activeFlag = 1)
				)
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findInitDelOaList" resultType="DeliveryDetail">
		SELECT 
			<include refid="slDeliveryDetailColumns"/>
		FROM sl_delivery_detail a
		<where>
		    a.companyid = #{company.recordId}
			and a.activeFlag = #{DEL_FLAG_NORMAL}
		</where>
	</select>
	
	<select id="queryDeliveryDetails" resultType="DeliveryDetail">
		SELECT 
			<include refid="slDeliveryDetailColumns"/>,
			dly.no as "delivery.no",
			mmd.no as "material.no"
		FROM sl_delivery_detail a
		LEFT JOIN sl_delivery dly ON dly.recordId = a.deliveryId
		LEFT JOIN md_material mmd ON mmd.recordId = a.materialId
		LEFT JOIN md_customer mc ON mc.recordId = dly.customerId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1
		AND dly.status=#{status} AND mmd.materialKind=100702
		<if test="delivery!=null and delivery.no!=null">
			AND REPLACE(dly.no," ","") LIKE CONCAT('%',REPLACE(#{delivery.no}," ",""),'%')
		</if>
		<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
			AND (
				dly.createdBy = #{createdBy.recordId}
				OR mc.createdBy = #{createdBy.recordId}
				OR (ISNULL(!mc.salesman) AND mc.salesman = #{createdBy.recordId})
				OR mc.recordId IN (SELECT customerId FROM sm_customer_salesAssistant WHERE userId = #{createdBy.recordId} AND activeFlag = 1)
			)
		</if>
		ORDER BY a.createdDate desc
		limit ${pageNo}, ${pageSize}
	</select>
	
	<select id="findAllList" resultType="DeliveryDetail">
		SELECT 
			<include refid="slDeliveryDetailColumns"/>
		FROM sl_delivery_detail a
		<include refid="slDeliveryDetailJoins"/>
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<!-- 根据送货单号查询详单 -->
	<select id="findDeliveryDetailByDelivery"  resultMap="DeliveryDetailMap">
		select 
			<include refid="slDeliveryDetailColumns"/>
		FROM sl_delivery_detail a
		WHERE a.deliveryId = #{recordId} 
			and a.companyid = #{company.recordId}
			and a.activeFlag = #{DEL_FLAG_NORMAL}									
	</select>
	
	<!-- 根据送货单号查询详单 -->
	<select id="findDeliDetailByDeliveryId"  resultMap="DeliveryDetailMap">
		select 
		    scc.materialType as "contractDetail.craft.materialType",
			<include refid="slDeliveryDetailColumns"/>
		FROM sl_delivery_detail a
		LEFT JOIN sl_contract_detail scd on scd.recordId=a.contractdetailid
		LEFT JOIN sl_contract_craft scc on scc.recordId=scd.craftid
		WHERE a.deliveryId = #{recordId} 
			and a.companyid = #{company.recordId}
			and a.activeFlag = #{activeFlag}									
	</select>
	
	<!-- 根据送货单ID查询详单 -->
	<select id="findDeliveryDetailByDeliveryId"  resultMap="DeliveryDetailMap">
		select 
			<include refid="slDeliveryDetailColumns"/>,
			sd.itemType AS "delivery.itemType",
			c.no AS "material.no",
			c.name AS "material.name",
			c.stocks - c.deliveryOaQty AS "material.stocks",
			c.deliveryOaQty AS "material.deliveryOaQty",
			c.specification AS "material.specification",
			c.depositQty AS "material.depositQty",
			c.depositOaQty AS "material.depositOaQty",
			c.deliverySize AS "material.deliverySize",
			a.quantity as "quantity",
			sc.no AS "contractDetail.contract.no",
			sc.recordId AS "contractDetail.contract.recordId",
			sc.customerPo AS "contractDetail.contract.customerPo",
			scd.customerMaterialNo AS "contractDetail.customerMaterialNo",
			scd.status AS "contractDetail.status",
			scd.recordId as  "contractDetail.recordId",
			IFNULL(scd.deposit,false) AS "contractDetail.deposit",
			IFNULL(scd.alInHouseQty,0) AS "contractDetail.alInHouseQty",
			IFNULL(scd.alDeliveryQty,0) AS "contractDetail.alDeliveryQty",
			IFNULL(scd.quantity,0) AS "contractDetail.quantity",
			IFNULL(scd.oaQty,0) AS "contractDetail.oaQty",
			
			scc.customerModel AS "contractDetail.craft.customerModel",
			scc.recordId AS "contractDetail.craft.recordId",
			scc.no AS "contractDetail.craft.no",
			scc.pnlLength as "contractDetail.craft.pnlLength",
			scc.pnlWidth as "contractDetail.craft.pnlWidth",
			scc.pnlDivisor as "contractDetail.craft.pnlDivisor",
			ra.recordId as "complaint.recordId",
			ra.companyId AS "company.recordId",
			ra.no AS "complaint.no",
			ra.alDeliveryQtys as "complaint.alDeliveryQtys",
			ra.materialId AS "complaint.material.recordId",
			ra.customerId AS "customer.recordId",
			ra.quantity as "complaint.quantity",
			ra.amount as "complaint.amount",
			ra.contractDetailId as "complaint.contractDetail.recordId",
			ra.style AS "complaint.style",
			
			d.alInHouseRejQty  as "complaint.contractDetail.alInHouseRejQty",
   			d.alDeliveryRejQty  as "complaint.contractDetail.alDeliveryRejQty",
   			d.oaRejQty  as "complaint.contractDetail.oaRejQty",
			ra.desireTreatment as "complaint.desireTreatment"
		FROM sl_delivery_detail a
		<include refid="slDeliveryDetailJoins"/> 
		LEFT JOIN sl_delivery sd on sd.recordId=a.deliveryId
		LEFT JOIN md_material c on c.recordId=a.materialId
		LEFT JOIN sl_contract_detail scd on scd.recordId=a.contractdetailid
		LEFT JOIN sl_contract_craft scc on scc.recordId=scd.craftid
		LEFT JOIN sl_contract sc on sc.recordId=scd.contractid
		LEFT JOIN st_reject_application ra on a.complaintId = ra.recordId
		LEFT JOIN sl_contract_detail d on d.recordId = ra.contractdetailid
		WHERE a.deliveryId = #{recordId} 
		AND a.activeFlag = #{DEL_FLAG_NORMAL}									
	</select>

	<select id="findMaterialDeliveryDetailByDeliveryId"  resultMap="DeliveryDetailMap">

		select
		<include refid="slDeliveryDetailColumns"/>,
			sd.itemType AS "delivery.itemType",
			c.no AS "material.no",
			c.name AS "material.name",
			c.stocks - c.deliveryOaQty AS "material.stocks",
			c.deliveryOaQty AS "material.deliveryOaQty",
			c.specification AS "material.specification",
			c.depositQty AS "material.depositQty",
			c.depositOaQty AS "material.depositOaQty",
			c.deliverySize AS "material.deliverySize",
			a.quantity as "quantity",
			sc.no AS "contractDetail.contract.no",
			sc.recordId AS "contractDetail.contract.recordId",
			sc.customerPo AS "contractDetail.contract.customerPo",
			IFNULL(scd.quantity,0) AS "contractDetail.quantity",

			ra.recordId as "complaint.recordId",
			ra.companyId AS "company.recordId",
			ra.no AS "complaint.no",
			ra.materialId AS "complaint.material.recordId",
			ra.customerId AS "customer.recordId",
			ra.quantity as "complaint.quantity",
			ra.amount as "complaint.amount",
			ra.contractDetailId as "complaint.contractDetail.recordId",
			ra.style AS "complaint.style",

			d.alInHouseRejQty  as "complaint.contractDetail.alInHouseRejQty",
   			d.alDeliveryRejQty  as "complaint.contractDetail.alDeliveryRejQty",
   			d.oaRejQty  as "complaint.contractDetail.oaRejQty",
			ra.desireTreatment as "complaint.desireTreatment"
		FROM sl_delivery_detail a
		<include refid="slDeliveryDetailJoins"/>
		LEFT JOIN sl_delivery sd on sd.recordId=a.deliveryId
		LEFT JOIN md_material c on c.recordId=a.materialId
		LEFT JOIN sl_material_contract_detail scd on scd.recordId=a.contractdetailid
		LEFT JOIN sl_material_contract sc on sc.recordId=scd.materialContractId
		LEFT JOIN st_reject_application ra on a.complaintId = ra.recordId
		LEFT JOIN sl_contract_detail d on d.recordId = ra.contractdetailid
		WHERE a.deliveryId = #{recordId}
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	<!-- 根据送货单ID查询送货明细列表 -->
	<select id="findDeliveryDetailListByDlId"  resultType="DeliveryDetail">
		select 
			<include refid="slDeliveryDetailColumns"/>,
			mmd.deliveryOaQty AS 'material.deliveryOaQty',
			mmd.depositQty AS "material.depositQty",
			mmd.stocks AS "material.stocks",
			mmd.depositOaQty AS "material.depositOaQty",
			
			d.alInHouseQty AS 'contractDetail.alInHouseQty',
			d.alDeliveryQty AS 'contractDetail.alDeliveryQty',
			d.alInHouseRejQty AS 'contractDetail.alInHouseRejQty',
			d.alDeliveryRejQty AS 'contractDetail.alDeliveryRejQty',
			d.oaQty AS 'contractDetail.oaQty',
			d.oaRejQty AS 'contractDetail.oaRejQty',
			ra.contractDetailId as "complaint.contractDetail.recordId",
			ra.style AS "complaint.style",
			ra.alDeliveryQtys as "complaint.alDeliveryQty"
		FROM sl_delivery_detail a
		LEFT JOIN md_material mmd ON mmd.recordId = a.materialId
		LEFT JOIN sl_contract_detail d ON d.recordId = a.contractDetailId
		LEFT JOIN st_reject_application ra on a.complaintId = ra.recordId
		WHERE a.deliveryId = #{recordId}
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	<!-- 根据送货单明细ID查询送货明细 -->
	<select id="findDeliveryDetailById"  resultMap="DeliveryDetailByIdMap">
		select 
			<include refid="slDeliveryDetailColumns"/>,
			mmd.deliveryOaQty AS 'material.deliveryOaQty',
			mmd.depositQty AS "material.depositQty",
			mmd.depositOaQty AS "material.depositOaQty",
			mmd.stocks AS "material.stocks",
			d.recordId as "complaint.recordId",
			d.alInHouseRejQty as "complaint.contractDetail.alInHouseRejQty",
			d.alDeliveryRejQty as "complaint.contractDetail.alDeliveryRejQty",
			d.oaRejQty as "complaint.contractDetail.oaRejQty",
			ra.contractDetailId as "complaint.contractDetail.recordId",
			ra.style AS "complaint.style"
		FROM sl_delivery_detail a
		LEFT JOIN md_material mmd on mmd.recordId = a.materialId
		LEFT JOIN st_reject_application ra on a.complaintId = ra.recordId
		LEFT JOIN sl_contract_detail d on ra.contractDetailId = d.recordId
		WHERE a.recordId = #{recordId} 
		AND a.activeFlag = #{DEL_FLAG_NORMAL}									
	</select>
	<!-- 查询某型号的已送货完成的数量 -->
	<select id="findReceivedProductQuantity" resultType="Integer">
	select sum(dtl.quantity) 
		from sl_delivery_detail dtl 
		LEFT JOIN sl_delivery sd ON sd.recordId = dtl.deliveryId
		where dtl.companyId = #{company.recordId} 
			and dtl.activeFlag = #{DEL_FLAG_NORMAL} AND sd.itemType = 1
			<if test="material != null and material.recordId != '' and material.recordId != null">
					and dtl.materialId = #{material.recordId} 
			</if>
			<if test="contractDetail != null and contractDetail.recordId != '' and contractDetail.recordId != null">
					and dtl.contractdetailid = #{contractDetail.recordId} 
			</if>
	</select>
	
	<select id="findReceivedProductCount" resultType="Long">
	select count(dtl.recordId) 
		from sl_delivery_detail dtl 
		where dtl.companyId = #{company.recordId} 
			and dtl.activeFlag = #{DEL_FLAG_NORMAL}
			<if test="material != null and material.recordId != '' and material.recordId != null">
					and dtl.materialId = #{material.recordId} 
			</if>
			<if test="contractDetail != null and contractDetail.recordId != '' and contractDetail.recordId != null">
					and dtl.contractdetailid = #{contractDetail.recordId} 
			</if>
	</select>
	
	<!-- 查询某型号的已送货完成和备用数量总和 -->
	<select id="findDelQtySum" resultType="Integer">
		SELECT 
			sum(dtl.quantity + IFNULL(dtl.spares,0)) 
		FROM sl_delivery_detail dtl 
		JOIN sl_delivery sd on sd.recordId = dtl.deliveryId
		WHERE dtl.companyId = #{company.recordId} 
			AND dtl.activeFlag = #{DEL_FLAG_NORMAL}
			AND dtl.contractdetailid = #{recordId} 
			<if test="status != '' and status != null">
				AND sd.status = #{status}
			</if>
	</select>
	<!-- 查询出该型号已送货的总数量条件为:查询该型号已确认的送货单 -->
	<select id="getComfirmContractDetailCount" resultType="DeliveryDetail">
	select sum(quantity) quantity , sum(spares) spares 
		from sl_delivery_detail dtl 
		LEFT JOIN sl_delivery sd on sd.recordId = dtl.deliveryId
		where dtl.companyId = #{company.recordId} 
			and dtl.activeFlag = #{DEL_FLAG_NORMAL}
			and dtl.contractdetailid = #{recordId}
			and dtl.status = #{status}
	</select>
	
	<!-- 查询出该型号已送货的总数量条件为:查询该型号已确认的送货单 -->
	<select id="getContractDetailCount" resultType="Integer">
	select sum(quantity) 
		from sl_delivery_detail dtl 
		where dtl.companyId = #{company.recordId} 
			and dtl.activeFlag = #{DEL_FLAG_NORMAL}
			and dtl.contractdetailid = #{recordId}
	</select>
	
	<!-- 未确认的送货数量 -->
	<select id="getUnconfimQtyByContractDetail" resultType="Integer">
	select SUM(c.quantity) from sl_delivery b
        LEFT JOIN sl_delivery_detail c on b.recordId = c.deliveryId
        where 
            c.materialId = #{material.recordId} and  b.status = '500516' and c.contractDetailId > 0
	</select>
	
	<!-- 查询这一条条合同明细的物料id -->
	<select id="getOneDeliveryDetail" resultType="DeliveryDetail">
	select a.materialId as "material.recordId" from sl_delivery_detail a 
	    where 
	        a.contractDetailId =  #{recordId} and a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL} 
	    LIMIT 1
	</select>
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO sl_delivery_detail(
			companyid,
			deliveryid,
			materialid,
			contractdetailid,
			quantity,
			spares,
			amount,
			actualrecqty,
			actualamount,
			activeflag,
			createdby,
			createddate,
			lastupdby,
			lastupddate,
			status,
			remark,
			complaintId,
			cycle,
			groupCenterId
		) VALUES (
			#{company.recordId},
			#{delivery.recordId},
			#{material.recordId},
			#{contractDetail.recordId},
			#{quantity},
			#{spares},
			#{amount},
			#{actualRecQty},
			#{actualAmount},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{status},
			#{remark},
			#{complaint.recordId},
			#{cycle},
			#{groupCenterId}
		)
	</insert>
	
	<insert id="runBatchInsert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO sl_delivery_detail(
			companyId,
			deliveryId,
			materialId,
			contractdetailId,
			quantity,
			spares,
			activeFlag,
			createdBy,
			createdDate,
			status,
			remark,
			sortNum,
			groupCenterId
		) VALUES 
		<foreach collection="contractDetails" item="item" index= "index" separator =",">
		(
			#{company.recordId},
			#{recordId},
			#{item.materialId},
			#{item.recordId},
			#{item.deliceryStocks},
			#{item.spares},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			500516,
			#{item.remark},
			#{item.sortNum},
			#{item.groupCenterId}
		)
		</foreach>
	</insert>
	
	<insert id="InsertDeliveryDeail" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO sl_delivery_detail(
			companyId,
			deliveryId,
			materialId,
			contractdetailId,
			quantity,
			spares,
			activeFlag,
			createdBy,
			createdDate,
			status,
			remark,
			sortNum,
			groupCenterId
		) VALUES (
			#{company.recordId},
			#{deliveryId},
			#{materialId},
			#{recordId},
			#{deliceryStocks},
			#{spares},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			500516,
			#{remark},
			#{sortNum},
			#{groupCenterId}
		)
	</insert>
	
	<update id="updateDeliveryDeail">
		UPDATE sl_delivery_detail SET 	
			quantity = #{deliceryStocks},
			spares = #{spares},
			sortNum = #{sortNum},
			lastupdBy = #{lastUpdBy.recordId},
			lastupdDate = #{lastUpdDate}
		WHERE recordId = #{deliveryDeailId}
	</update>
	
	<update id="delDeliveryDeail">
		UPDATE sl_delivery_detail SET 	
			activeFlag = 0
		WHERE recordId = #{deliveryDeailId}
	</update>
	
	<update id="update">
		UPDATE sl_delivery_detail SET 	
			companyid = #{company.recordId},
			deliveryid = #{delivery.recordId},
			materialid = #{material.recordId},
			contractdetailid = #{contractDetail.recordId},
			quantity = #{quantity},
			spares = #{spares},
			amount = #{amount},
			actualrecqty = #{actualRecQty},
			actualamount = #{actualAmount},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate},
			status=#{status},
			remark = #{remark},
			complaintId = #{complaint.recordId},
			cycle = #{cycle}
		WHERE recordId = #{recordId}
	</update>
	<!-- 更新该合同明细所有已送货数量 -->
	<update id="updateActualRecQty">
		UPDATE sl_delivery_detail SET 	
			actualrecqty = #{actualRecQty},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate}
		WHERE contractdetailid = #{contractDetail.recordId}
		and activeFlag = #{DEL_FLAG_NORMAL}
	</update>
	
	<update id="delete">
		UPDATE sl_delivery_detail SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="deleteForDeliveryId">
		UPDATE sl_delivery_detail SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE deliveryId = #{recordId}
	</update>
	
	<update id="updateStatus">
		UPDATE sl_delivery_detail SET 
			status = #{status}
		WHERE recordId = #{recordId}
	</update>
	<!-- 查询某合同明细的所有送货数量，不含已删除，含备份数量 -->
	<select id="getAllSendGoods" resultType="Integer">
	select sum(dtl.quantity+dtl.spares) 
		from sl_delivery_detail dtl 
		where dtl.companyId = #{company.recordId} 
			and dtl.activeFlag = #{DEL_FLAG_NORMAL}
			<if test="material != null and material.recordId != '' and material.recordId != null">
					and dtl.materialId = #{material.recordId} 
			</if>
			<if test="contractDetail != null and contractDetail.recordId != '' and contractDetail.recordId != null">
					and dtl.contractdetailid = #{contractDetail.recordId} 
			</if>
	</select>
	<!-- 查询某合同明细已出库的送货数量，不含备份数量 -->
	<select id="getSendGoods" resultType="Integer">
	select sum(dtl.quantity) 
		from sl_delivery_detail dtl 
		where dtl.companyId = #{company.recordId} 
			and dtl.activeFlag = #{DEL_FLAG_NORMAL}
			and dtl.status = 500518
			and dtl.contractdetailid = #{recordId} 
	</select>
	
	<!-- WC 2017-01-14 查询该合同明细是否存在多个未确认的送货单 -->
    <select id="getHasUnsureDeliveryNum" resultType="Integer">
        SELECT COUNT(*) FROM sl_delivery sd
		LEFT JOIN sl_delivery_detail sdd ON sd.recordId = sdd.deliveryId AND sdd.activeFlag = #{DEL_FLAG_NORMAL}
		WHERE sd.activeFlag = #{DEL_FLAG_NORMAL}
		AND sdd.contractDetailId = #{contractDetail.recordId}
		AND sd.status = 500516
    </select>
    
    <!-- WC 017-01-25 更新送货明细的送货数量 -->
    <update id="updateQuantity">
    	UPDATE sl_delivery_detail SET
    		quantity = #{quantity},
    		lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
    </update>
    
    <!-- WC 2017-02-14 寄存送货单打印时 ：根据送货单ID查询送货明细及物料信息 -->
    <select id="getDelDtlAndMaterialInfoByDelId" resultType="DeliveryDetail">
	    SELECT  
			a.recordid AS "recordId",
			a.companyid AS "company.recordId",
			a.deliveryid AS "delivery.recordId",
			a.materialid AS "material.recordId",
			a.contractdetailid AS "contractDetail.recordId",
			a.quantity AS "quantity",
			a.spares AS "spares",
			a.amount AS "amount",
			a.actualrecqty AS "actualRecQty",
			a.actualamount AS "actualAmount",
			a.activeflag AS "activeFlag",
			a.createdby AS "createdBy.recordId",
			a.createddate AS "createdDate",
			a.lastupdby AS "lastUpdBy.recordId",
			a.lastupddate AS "lastUpdDate",
			a.status as "status",
			a.remark AS "remark",
			a.complaintId as "complaint.recordId",

			b.no AS "material.no",
			b.specification AS "material.specification",
			b.deliverySize AS "material.deliverySize"
		FROM sl_delivery_detail a
		LEFT JOIN md_material b on a.materialId = b.recordId
		WHERE a.deliveryId = #{recordId}
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
    </select>
    
    <!-- WC 2017-02-21 查询送货数量 -->
    <select id="getQuantity" resultType="Integer">
    	SELECT quantity FROM sl_delivery_detail WHERE recordId = #{recordId}
    </select>
    
    <!-- WC 2017-03-28 查询是合同明细是否有成品采购或生产 -->
    <select id="countConDetailFromPurchingOrProduct" resultType="Integer">
    	SELECT 
			(SELECT COUNT(1) 
			        FROM st_product_store 
			        WHERE contractDetailId = #{recordId} 
			 		AND companyId = #{company.recordId} 
			 		AND activeFlag = #{DEL_FLAG_NORMAL} 
			 		AND inOutType = '14'
			 		AND IFNULL(quantity,0) <![CDATA[>]]> 0)
 			+ 
			(SELECT COUNT(1) 
					FROM sl_notification 
					WHERE contractDetailId = #{recordId} 
					AND companyId = #{company.recordId} AND activeFlag = #{DEL_FLAG_NORMAL} AND IFNULL(completedQty,0) <![CDATA[>]]> 0);
    </select>
    
    <!-- WC 2017-01-14 查询该合同明细是否存在未确认的送货单 -->
    <select id="getHasUnsureDeliveryDetailNum" resultType="Integer">
        SELECT 
        	COUNT(*) 
        FROM sl_delivery_detail a 
        LEFT JOIN sl_delivery b ON a.deliveryId = b.recordId
		WHERE a.activeFlag = #{DEL_FLAG_NORMAL}
		AND a.contractDetailId = #{recordId}
		AND a.status = 500516 
		AND b.itemType = 1
    </select>
    
    <!-- WC 2017-09-15 根据送货单ID更新送货单明细状态 -->
    <update id="updateStatusByDeliveryId">
    	UPDATE sl_delivery_detail SET 
			status = #{status}
		WHERE deliveryId = #{recordId} AND activeFlag = #{DEL_FLAG_NORMAL}
    </update>
	<!-- 获取下游入库记录 -->
	<select id="getQuantityCount"  resultType="Integer">
		SELECT
			sum(quantity)
		FROM
			st_product_store
		WHERE
			contractDetailId IN (
				SELECT
					recordId
				FROM
					sl_contract_detail
				WHERE
					contractId = (
						SELECT
							contractId
						FROM
							sl_contract_detail
						WHERE
							recordId =  #{recordId}
					)
			)
	</select>
	<!-- 已出库数量 -->
	<select id="getQuantityCount2"  resultType="Integer">
		select sum(quantity) from sl_delivery_detail    where deliveryId =  #{recordId} AND status = 500518
	</select>
	<!-- 已出库数量 -->
	<select id="getQuantityCountByContract"  resultType="Integer">
		select sum(quantity) from sl_delivery_detail    where contractDetailId =  #{recordId} AND status = 500518
	</select>

	<!-- 送货单总数 -->
	<select id="getQuantitySum"  resultType="Integer">
		select sum(quantity) from sl_delivery_detail    where deliveryId =  #{recordId}
	</select>

	<select id="selectDeliveryDetails" resultType="com.kyb.pcberp.modules.contract.entity.DeliveryDetail">
		SELECT distinct
			<include refid="slDeliveryDetailColumns"/>,
			dly.no as "delivery.no",
			mmd.no as "material.no",
			smc.no as "contract.no",
			smcd.quantity AS "materialContractTotal",
			msh.recordId AS "storeHouse.recordId",
			msh.name  as "storeHouse.name",
			msh.type  as "storeHouse.type",
			smcd.price as "contractDetail.unitPrice"
		FROM sl_delivery_detail a
		LEFT JOIN sl_delivery dly ON dly.recordId = a.deliveryId
		LEFT JOIN md_material mmd ON mmd.recordId = a.materialId
		LEFT JOIN sl_material_contract_detail smcd ON smcd.recordId= a.contractDetailId
		LEFT JOIN sl_material_contract smc ON smcd.materialContractId = smc.recordId
		LEFT JOIN md_store_house msh ON msh.recordId= mmd.storeHouseId
		LEFT JOIN md_customer cus ON cus.recordId = dly.customerId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1
		AND a.status=#{status} AND dly.itemType=4
		<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
			AND (
				dly.createdBy = #{createdBy.recordId}
				OR cus.createdBy = #{createdBy.recordId}
				OR (!ISNULL(cus.salesman) AND cus.salesman = #{createdBy.recordId})
				OR cus.recordId IN (SELECT customerId FROM sm_customer_salesAssistant WHERE userId = #{createdBy.recordId} AND activeFlag = 1)
			)
		</if>
		ORDER BY a.createdDate desc
	</select>
    <select id="selectDeliveryStatus" resultType="java.lang.Integer">
		SELECT STATUS FROM
		 sl_delivery_detail
		 WHERE recordId=#{recordId}
	</select>
	
	<select id="getDeliDeailByConDe" resultType="com.kyb.pcberp.modules.contract.entity.DeliveryDetail">
		SELECT quantity,createdDate,`status`,lastUpdDate FROM
			sl_delivery_detail
		WHERE contractDetailId=#{contractDetailId}
	</select>

	<select id="getMaterialContract" resultType="java.lang.String">
		SELECT smc.`no`
		 FROM sl_delivery_detail sdd
		LEFT JOIN sl_material_contract_detail smcd on smcd.recordId=sdd.contractDetailId
		LEFT JOIN sl_material_contract smc on smc.recordId=smcd.materialContractId
		WHERE sdd.recordId=#{recordId}
	</select>
	
	<!--zjn 2018-10-17 合同明细对应确认的送货单的送货数量 -->
	<select id="getComfirmRawContractDetailCount" resultType="DeliveryDetail">
		select 
		sum(quantity) quantity 
		from sl_delivery_detail dtl 
		LEFT JOIN sl_delivery sd on sd.recordId = dtl.deliveryId
		where dtl.companyId = #{company.recordId} 
		and dtl.activeFlag = #{DEL_FLAG_NORMAL}
		and dtl.contractdetailid = #{recordId}
		and sd.status = #{status}
	</select>
	<!--找到对应的送货单明细-->
    <select id="getDeliveryByReject" resultType="com.kyb.pcberp.modules.contract.entity.DeliveryDetail">
		SELECT  a.lastUpdDate,
		a.createdBy as "createdBy.recordId",
		sc.createdBy as "merchandiser.recordId",
		mc.salesman as "salesman.recordId"
		FROM sl_delivery_detail  a
LEFT JOIN sl_contract_detail scd on scd.recordId=a.contractDetailId
LEFT JOIN sl_contract sc on sc.recordId = scd.contractId
LEFT JOIN md_material mm on mm.recordId=a.materialId
LEFT JOIN sl_delivery sd on sd.recordId=a.deliveryId
LEFT JOIN md_customer mc on mc.recordId=sd.customerId
WHERE scd.`recordId`=#{contractDetail.recordId}
and mm.recordId=#{material.recordId}
and sd.customerId=#{customer.recordId}
and a.companyId=#{company.recordId}
and  a.activeFlag=1
and sd.itemType = 1
ORDER BY  a.lastUpdDate desc  LIMIT 0,1
	</select>

	<!--lq 2018-11-27 根据报表客户查询送货单-->
    <select id="getDeliveryByCustomerId" resultType="com.kyb.pcberp.modules.contract.entity.DeliveryDetail">
		SELECT e.customerModel as "contractDetail.craft.customerModel",
		FORMAT((d.orderDeailArea/d.quantity) * a.quantity ,4) as "orea",
		p.price as "pricem",
		a.quantity,
		b.no as "delivery.no",
		f.no as "material.no",
		c.no as "contractDetail.cntractNo",
		a.contractDetailId as "contractDetail.recordId"
		<if test="allType!=null and   allType!=''">
			<if test="allType==2 or  allType=='2'">
				<if test="valuedId == 177 and valuedId == '177'  ">
					,sum(FORMAT((d.orderDeailArea/d.quantity) * a.quantity ,4)) as "tempData"
				</if>
				<if test="valuedId == 178 and valuedId == '178'  ">
					,sum(FORMAT((p.price * a.quantity) ,4)) as "tempData"
				</if>
			</if>
			<if test="allType==1 or  allType=='1'">
                ,sum(FORMAT((p.price * a.quantity) ,4)) as "tempData2"
				,sum(FORMAT((d.orderDeailArea/d.quantity) * a.quantity ,4)) as "tempData"

			</if>

		</if>



		from sl_delivery_detail a
		LEFT JOIN  sl_delivery b on b.recordId=a.deliveryId
		LEFT JOIN sl_contract_detail d on d.recordId=a.contractDetailId
		LEFT JOIN sl_contract c  on c.recordId= d.contractId
		LEFT join  sl_contract_craft e on e.recordId= d.craftId
		LEFT join  md_material  f  on  f.recordId=a.materialId
		LEFT JOIN  sl_price p on p.recordId = d.priceId

		<where>
			1=1
			and b.customerId=#{recordId}
			and a.companyId in (${company.recordId})
			AND a.activeFlag=1 and b.activeFlag=1 and a.`status`!=500516
			<!-- 1:日,2:周,3:月 -->
			<if test="dateType != null and dateType!= ''">
				<if test="dateType == 1">
					<if test="queryDate != null and queryDate != ''">
						AND DATE(b.lastUpdDate)= DATE(#{queryDate})
					</if>
					<if test="queryDate == null or queryDate ==''">
						AND DATE(b.lastUpdDate)=CURDATE()
					</if>
				</if>
				<if test="dateType == 2">
					<if test="queryDate != null and queryDate != ''">
						AND YEARWEEK(date_format(b.lastUpdDate,'%Y%m%d')) =
						YEARWEEK(#{queryDate})
					</if>
					<if test="queryDate == null or queryDate ==''">
						AND YEARWEEK(date_format(b.lastUpdDate,'%Y%m%d')) = YEARWEEK(now())
					</if>
				</if>
				<if test="dateType == 3">
					<if test="queryDate != null and queryDate != ''">
						AND DATE_FORMAT(b.lastUpdDate,'%Y%m') =
						DATE_FORMAT(#{queryDate},'%Y%m')
					</if>
					<if test="queryDate == null or queryDate ==''">
						AND DATE_FORMAT(b.lastUpdDate,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m')
					</if>
				</if>
			</if>
		</where>


	</select>


    <!--lq 2018-11-27 根据报表业务员查询送货单-->
    <select id="getDeliveryByUserId" resultType="com.kyb.pcberp.modules.contract.entity.DeliveryDetail">
        SELECT e.customerModel as "contractDetail.craft.customerModel",
		convert(FORMAT((d.orderDeailArea/d.quantity) * a.quantity ,4),decimal ) as "orea",
        p.price as "pricem",
        a.quantity,
        b.no as "delivery.no",
        f.no as "material.no",
        c.no as "contractDetail.cntractNo",
        a.contractDetailId as "contractDetail.recordId"
		<if test="allType!=null and   allType!=''">
			<if test="allType==2 or  allType=='2'">
				<if test="valuedId == 178 and valuedId == '178'  ">
					,sum(FORMAT((d.orderDeailArea/d.quantity) * a.quantity ,4)) as "tempData"
				</if>
				<if test="valuedId == 177 and valuedId == '177'  ">
					,sum(FORMAT((p.price * a.quantity) ,4)) as "tempData"
				</if>
			</if>
			<if test="allType==1 or  allType=='1'">
                ,sum(FORMAT((p.price * a.quantity) ,4)) as "tempData2"
                ,sum(FORMAT((d.orderDeailArea/d.quantity) * a.quantity ,4)) as "tempData"
			</if>

		</if>
        from sl_delivery_detail a
        LEFT JOIN  sl_delivery b on b.recordId=a.deliveryId
        LEFT JOIN sl_contract_detail d on d.recordId=a.contractDetailId
        LEFT JOIN sl_contract c  on c.recordId= d.contractId
        LEFT join  sl_contract_craft e on e.recordId= d.craftId
        LEFT join  md_material  f  on  f.recordId=a.materialId
        LEFT JOIN  sl_price p on p.recordId = d.priceId

        <where>
            1=1
            and c.userId=#{recordId}
            and a.companyId in (${company.recordId})
            AND a.activeFlag=1 and b.activeFlag=1 and a.`status`!=500516
            <!-- 1:日,2:周,3:月 -->
            <if test="dateType != null and dateType!= ''">
                <if test="dateType == 1">
                    <if test="queryDate != null and queryDate != ''">
                        AND DATE(b.lastUpdDate)= DATE(#{queryDate})
                    </if>
                    <if test="queryDate == null or queryDate ==''">
                        AND DATE(b.lastUpdDate)=CURDATE()
                    </if>
                </if>
                <if test="dateType == 2">
                    <if test="queryDate != null and queryDate != ''">
                        AND YEARWEEK(date_format(b.lastUpdDate,'%Y%m%d')) =
                        YEARWEEK(#{queryDate})
                    </if>
                    <if test="queryDate == null or queryDate ==''">
                        AND YEARWEEK(date_format(b.lastUpdDate,'%Y%m%d')) = YEARWEEK(now())
                    </if>
                </if>
                <if test="dateType == 3">
                    <if test="queryDate != null and queryDate != ''">
                        AND DATE_FORMAT(b.lastUpdDate,'%Y%m') =
                        DATE_FORMAT(#{queryDate},'%Y%m')
                    </if>
                    <if test="queryDate == null or queryDate ==''">
                        AND DATE_FORMAT(b.lastUpdDate,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m')
                    </if>
                </if>
            </if>

        </where>
    </select>


    <select id="getDeliveryByCreatedId" resultType="com.kyb.pcberp.modules.contract.entity.DeliveryDetail">
		SELECT e.customerModel as "contractDetail.craft.customerModel",
		convert(FORMAT((d.orderDeailArea/d.quantity) * a.quantity ,4),decimal ) as "orea",
		p.price as "pricem",
		a.quantity,
		b.no as "delivery.no",
		f.no as "material.no",
		c.no as "contractDetail.cntractNo",
		a.contractDetailId as "contractDetail.recordId"
		<if test="allType!=null and   allType!=''">
			<if test="allType==2 or  allType=='2'">
				<if test="valuedId == 178 and valuedId == '178'  ">
					,sum(FORMAT((d.orderDeailArea/d.quantity) * a.quantity ,4)) as "tempData"
				</if>
				<if test="valuedId == 177 and valuedId == '177'  ">
					,sum(FORMAT((p.price * a.quantity) ,4)) as "tempData"
				</if>
			</if>
			<if test="allType==1 or  allType=='1'">
                ,sum(FORMAT((p.price * a.quantity) ,4)) as "tempData2"
                ,sum(FORMAT((d.orderDeailArea/d.quantity) * a.quantity ,4)) as "tempData"
			</if>

		</if>
		from sl_delivery_detail a
		LEFT JOIN  sl_delivery b on b.recordId=a.deliveryId
		LEFT JOIN sl_contract_detail d on d.recordId=a.contractDetailId
		LEFT JOIN sl_contract c  on c.recordId= d.contractId
		LEFT join  sl_contract_craft e on e.recordId= d.craftId
		LEFT join  md_material  f  on  f.recordId=a.materialId
		LEFT JOIN  sl_price p on p.recordId = d.priceId

		<where>
			1=1
			and c.createdBy=#{recordId}
			and a.companyId in (${company.recordId})
			AND a.activeFlag=1 and b.activeFlag=1 and a.`status`!=500516
			<!-- 1:日,2:周,3:月 -->
			<if test="dateType != null and dateType!= ''">
				<if test="dateType == 1">
					<if test="queryDate != null and queryDate != ''">
						AND DATE(b.lastUpdDate)= DATE(#{queryDate})
					</if>
					<if test="queryDate == null or queryDate ==''">
						AND DATE(b.lastUpdDate)=CURDATE()
					</if>
				</if>
				<if test="dateType == 2">
					<if test="queryDate != null and queryDate != ''">
						AND YEARWEEK(date_format(b.lastUpdDate,'%Y%m%d')) =
						YEARWEEK(#{queryDate})
					</if>
					<if test="queryDate == null or queryDate ==''">
						AND YEARWEEK(date_format(b.lastUpdDate,'%Y%m%d')) = YEARWEEK(now())
					</if>
				</if>
				<if test="dateType == 3">
					<if test="queryDate != null and queryDate != ''">
						AND DATE_FORMAT(b.lastUpdDate,'%Y%m') =
						DATE_FORMAT(#{queryDate},'%Y%m')
					</if>
					<if test="queryDate == null or queryDate ==''">
						AND DATE_FORMAT(b.lastUpdDate,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m')
					</if>
				</if>
			</if>
		</where>
	</select>
	
	<select id="findQuantity" resultType="DeliveryDetail">
    	SELECT 
    		a.recordId,
    		a.status,
    		a.quantity 
    	FROM sl_delivery_detail a
    	WHERE a.contractDetailId = #{recordId}
    	AND a.activeFlag = 1 
    </select>
    
    <select id="getProduceBatchCount" resultType="Integer">
    	SELECT
			COUNT(1)
		FROM sl_notification a
		LEFT JOIN sl_contract_detail b ON b.recordId = a.contractDetailId AND b.companyId = a.companyId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND b.status = 200204
		AND
		(
			a.recordId = 
			(
				SELECT
					notificationId
				FROM pd_produce_batch 
				WHERE recordId = #{recordId} LIMIT 1
			)
			OR
			a.mergeId = 
			(
				SELECT
					notificationId
				FROM pd_produce_batch 
				WHERE recordId = #{recordId} LIMIT 1
			)
		) 
    </select>
    
    <select id="checkDetailCount" resultType="Integer">
    	SELECT
			COUNT(1)
		FROM sl_delivery_detail
		WHERE activeFlag = 1 AND FIND_IN_SET(contractDetailId,#{recordId})
    </select>
    
    <select id="checkDetailCountTwo" resultType="Integer">
    	SELECT
			COUNT(1)
		FROM sl_delivery_detail
		WHERE activeFlag = 1 AND companyId = #{ecoemyId} AND contractDetailId IN
		(
			SELECT
				recordId
			FROM sl_contract_detail
			WHERE activeFlag = 1 AND FIND_IN_SET(groupCenterId,#{recordId})
		)
    </select>

	<select id = "findDeliveryDetailByDId" resultType="DeliveryDetail">
		SELECT
			b.recordId AS "delivery.recordId",
			b.companyId AS "delivery.company.recordId",
			b.customerId AS "delivery.customer.recordId",
			b.no AS "delivery.no",
			b.sentTime AS "delivery.sentTime",
			b.createdBy AS "delivery.createdBy.recordId",
			b.createdDate AS "delivery.createdDate",
			b.remark AS "delivery.remark",
			b.linkman AS "delivery.linkman",
			b.address AS "delivery.address",
			b.deliveryWay AS "delivery.deliveryWay",
			b.phone AS "delivery.phone",
			b.branchId AS "delivery.branch.recordId",
			b.itemType AS "delivery.itemType",
			a.recordId AS recordId,
			a.companyId AS "company.recordId",
			a.materialId AS "material.recordId",
			a.contractdetailId AS "contractDetail.recordId",
			a.quantity AS quantity,
			a.spares AS spares,
			a.createdDate AS createdDate,
			a.remark AS remark,
			a.sortNum AS "contractDetail.sortNum",
			a.groupCenterId AS "contractDetail.groupCenterId",
			d.customerId AS lnCustomerId,
			d.branchId AS lnBranchId,
			f.customerId AS jxCustomerId,
			f.branchId AS jxBranchId,
			c.recordId AS lnContractDetailId,
			e.recordId AS jxContractDetailId,
			mdv2.recordId AS lnDeliveryWay,
			mdv3.recordId AS jxDeliveryWay,
			scc2.`no` AS lnno,
			md1.recordId AS lnMaterialId,
			scc3.`no` AS jxno,
			md2.recordId AS jxMaterialId,
			d.recordId AS lnContractId,
			f.recordId AS jxContractId,
			sn.customerModel AS lnCustomerModel,
			sn2.customerModel AS jxCustomerModel
		FROM sl_delivery_detail a
		LEFT JOIN sl_delivery b ON b.recordId = a.deliveryId
		LEFT JOIN sl_contract_detail g ON g.recordId = a.contractDetailId
		LEFT JOIN sl_contract_craft scc1 ON scc1.recordId = g.craftId
		LEFT JOIN sl_contract_detail c ON c.groupCenterId = g.groupCenterId AND c.companyId = #{ecoemyId} and c.activeFlag = 1
		LEFT JOIN md_dict_value mdv on b.deliveryWay = mdv.recordId
		LEFT JOIN md_dict_value mdv2 ON mdv.`value` = mdv2.`value` and mdv2.companyId = c.companyId
		LEFT JOIN sl_contract_craft scc2 ON scc2.recordId = c.craftId
		LEFT JOIN md_material md1 ON md1.`no` = scc2.`no` AND md1.companyId = scc2.companyId AND md1.activeFlag = 1
		LEFT JOIN sl_contract d ON d.recordId = c.contractId
		LEFT JOIN sl_notification sn on sn.contractDetailId = c.recordId
		LEFT JOIN sl_contract_detail e ON e.groupCenterId = g.groupCenterId AND e.companyId = #{factoryComId} and e.activeFlag = 1
		LEFT JOIN sl_contract_craft scc3 ON scc3.recordId = e.craftId
		LEFT JOIN md_dict_value mdv3 ON mdv.`value` = mdv3.`value` and mdv3.companyId = e.companyId
		LEFT JOIN md_material md2 ON md2.`no` = scc3.`no` AND md2.companyId = scc3.companyId AND md2.activeFlag = 1
		LEFT JOIN sl_contract f ON f.recordId = e.contractId
		LEFT JOIN sl_notification sn2 on sn2.contractDetailId = e.recordId
		WHERE b.recordId = #{recordId} AND a.activeFlag = 1 AND b.activeFlag = 1 
		AND a.companyId = #{company.recordId}
	</select>

	<insert id="runBatchAdd" >
		INSERT INTO sl_delivery_detail(
			companyId,
			deliveryId,
			materialId,
			contractdetailId,
			quantity,
			spares,
			activeFlag,
			createdBy,
			createdDate,
			status,
			remark,
			sortNum,
			groupCenterId,
			terminalDetailId
		) VALUES
		<foreach collection="list" item="deliveryDetail" index= "index" separator =",">
			(
			#{deliveryDetail.company.recordId},
			#{deliveryDetail.delivery.recordId},
			#{deliveryDetail.material.recordId},
			#{deliveryDetail.contractDetail.recordId},
			#{deliveryDetail.quantity},
			#{deliveryDetail.spares},
			#{deliveryDetail.DEL_FLAG_NORMAL},
			#{deliveryDetail.delivery.createdBy.recordId},
			#{deliveryDetail.delivery.createdDate},
			500517,
			#{deliveryDetail.remark},
			#{deliveryDetail.contractDetail.sortNum},
			#{deliveryDetail.contractDetail.groupCenterId},
			#{deliveryDetail.terminalDetailId}
			)
		</foreach>
	</insert>
    
    <select id="getDelDetailListByGroupCenterId" resultType="DeliveryDetail">
    	SELECT
			a.recordId,
			a.quantity,
			a.companyId AS "company.recordId",
			a.spares,
			a.groupCenterId,
			d.recordId AS "material.recordId",
			d.storeHouseId AS "material.storehouse.recordId",
			c.recordId AS "contractDetail.recordId",
			c.customerFileName AS "contractDetail.customerFileName",
			c.`status` AS "contractDetail.status",
			c.companyId AS "contractDetail.company.recordId",
			c.quantity AS "contractDetail.quantity",
			b.recordId AS "delivery.recordId",
			b.`no` AS "delivery.no",
			b.itemType AS "delivery.itemType",
			b.sentTime AS "delivery.sentTime",
			b.companyId AS "delivery.company.recordId",
			e.recordId AS "delivery.customer.recordId",
			e.checkDate AS "delivery.customer.checkDate",
			f.recordId AS "contractDetail.craft.recordId",
			f.`no` AS "contractDetail.craft.no",
			f.unitLength AS "contractDetail.craft.unitLength",
			f.unitWidth AS "contractDetail.craft.unitWidth",
			f.boardThickness AS "contractDetail.craft.boardThickness",
			f.customerModel AS "contractDetail.craft.customerModel",
			h.recordId AS "contractDetail.contract.recordId",
			h.`no` AS "contractDetail.contract.no",
			h.customerPo AS "contractDetail.contract.customerPo",
			h.payWay AS "contractDetail.contract.payWay",
			h.prePayBalance AS "contractDetail.contract.prePayBalance",
			h.prePayAmount AS "contractDetail.contract.prePayAmount",
			g.recordId AS "contractDetail.pricees.recordId",
			g.price AS "contractDetail.pricees.price",
			g.engineeringFee AS "contractDetail.pricees.engineeringFee",
			g.mouldFee AS "contractDetail.pricees.mouldFee",
			g.testShelfFee AS "contractDetail.pricees.testShelfFee",
			g.filmFee AS "contractDetail.pricees.filmFee",
			g.othersFee AS "contractDetail.pricees.othersFee",
			g.engineeringLimit AS "contractDetail.pricees.engineeringLimit",
			g.mouldLimit AS "contractDetail.pricees.mouldLimit",
			g.testShelfLimit AS "contractDetail.pricees.testShelfLimit",
			i.recordId AS "jxContractDetailId"
		FROM sl_delivery_detail a
		LEFT JOIN sl_delivery b ON b.recordId = a.deliveryId AND b.companyId = a.companyId
		LEFT JOIN sl_contract_detail c ON c.recordId = a.contractDetailId AND c.companyId = a.companyId
		LEFT JOIN md_material d ON d.recordId = a.materialId AND d.companyId = a.companyId
		LEFT JOIN md_customer e ON e.recordId = b.customerId AND e.companyId = a.companyId
		LEFT JOIN sl_contract_craft f ON f.recordId = c.craftId AND f.companyId = a.companyId
		LEFT JOIN sl_price g ON g.recordId = c.priceId AND g.companyId = c.companyId
		LEFT JOIN sl_contract h ON h.recordId = c.contractId AND h.companyId = a.companyId
		LEFT JOIN sl_contract_detail i ON i.groupCenterId = a.groupCenterId AND i.companyId = #{factoryComId} AND i.activeFlag = 1
		WHERE a.groupCenterId IN(${groupCenterId}) AND a.activeFlag = 1 AND a.companyId <![CDATA[<>]]> #{company.recordId}
		AND a.terminalDetailId IN (SELECT terminalDetailId FROM sl_delivery_detail WHERE recordId IN (${deliveryDetailIds}))
    </select>
    
    <select id="loadDeailList" resultType="DeliveryDetail">
		SELECT
			a.recordId,
			a.quantity,
			a.spares,
			a.sortNum,
			d.`no` AS "contractNo",
			d.customerPo,
			c.customerModel,
			c.`no` AS "craftNo",
			b.customerMaterialNo,
			0 + CAST(c.pnlLength AS CHAR) as "pnlLength",
			0 + CAST(c.pnlWidth AS CHAR) as "pnlDivisor",
			0 + CAST(c.pnlDivisor AS CHAR) as "pnlDivisor",
			a.signBackNum,
			a.signBackDate,
			f.userName AS "signBackUserName",
			a.deliveryId
		FROM
			sl_delivery_detail a
		LEFT JOIN sl_contract_detail b ON b.recordId = a.contractdetailid
		AND b.companyId = a.companyId
		LEFT JOIN sl_contract_craft c ON c.recordId = b.craftid
		AND c.companyId = a.companyId
		LEFT JOIN sl_contract d ON d.recordId = b.contractid
		AND d.companyId = a.companyId
		LEFT JOIN st_product_store e ON e.outBoundId = a.terminalDetailId AND e.contractDetailId = a.contractDetailId AND e.outBoundStocks = a.quantity
		AND e.groupCenterId = a.groupCenterId AND e.activeFlag = 1 AND e.companyId = a.companyId
		LEFT JOIN sm_user f ON f.recordId = e.signBackUserId
		WHERE a.activeFlag = #{DEL_FLAG_NORMAL}
		AND a.companyId = #{company.recordId}
		<if test="deliveryId != null and deliveryId != ''">
			AND a.deliveryId IN (${deliveryId})
		</if>
		GROUP BY a.recordId
		ORDER BY a.sortNum,a.createdDate								
	</select>
	
	<select id="getDelDetailCountByNotiId" resultType="Integer">
		SELECT
			COUNT(1)
		FROM sl_delivery_detail a
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 
		AND a.contractDetailId = (SELECT contractDetailId FROM sl_notification WHERE recordId = #{recordId})
	</select>
	
	<select id="getGroupCenterId" resultType="String">
		SELECT
			GROUP_CONCAT(groupCenterId)
		FROM sl_delivery_detail 
		WHERE deliveryId = #{recordId} AND activeFlag = 1
	</select>
	
	<update id="delDetailByGroupCenterId">
		UPDATE sl_delivery_detail SET
			activeFlag = 0
		WHERE activeFlag = 1 AND groupCenterId = #{groupCenterId} 
		AND companyId <![CDATA[<>]]> #{company.recordId}
	</update>
	
	<update id="updateTerminalDetailId">
		UPDATE sl_delivery_detail SET
			terminalDetailId = #{terminalDetailId}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="getDeliveryDetailVoList" resultType="com.kyb.pcberp.modules.contract.vo.DeliveryDetailVo">
		SELECT
			@rownum := @rownum+1 AS "index",
			b.`no` AS "deliveryNo",
			b.sentTime AS "sentTime",
			e.`no` AS "customerNo",
			e.shortName AS "cusShortName",
			f.`no` AS "craftNo",
			d.`no` AS "contractNo",
			f.customerModel AS "customerModel",
			"pcs" AS "unit",
			a.quantity AS "quantity",
			CASE WHEN extractNumber(h.`value`) <![CDATA[<>]]> 0 AND g.price IS NOT NULL AND g.price > 0 THEN ROUND(g.price / (extractNumber(h.`value`) + 100) * 100,4)
			ELSE g.price END AS "salesPriceExclTax",
			g.price AS "salesPrice",
			ROUND(IFNULL(f.pnlLength,0) * IFNULL(f.pnlWidth,0) / IFNULL(f.pnlDivisor,1) / 1000000 * (IFNULL(a.quantity,0) + IFNULL(a.spares,0)),4) AS "sentArea",
			CASE WHEN extractNumber(h.`value`) <![CDATA[<>]]> 0 AND g.price IS NOT NULL AND g.price > 0 THEN ROUND(g.price / (extractNumber(h.`value`) + 100)* 100 * (IFNULL(a.quantity,0) + IFNULL(a.spares,0)),4)
			ELSE ROUND(g.price * (IFNULL(a.quantity,0) + IFNULL(a.spares,0)),4) END AS "sentAmoutExclTax",
			CASE WHEN g.price IS NOT NULL AND g.price > 0 THEN ROUND(g.price * (IFNULL(a.quantity,0) + IFNULL(a.spares,0)),2)
			ELSE 0 END AS "sentAmout",
			CASE WHEN g.price IS NULL OR g.price = 0 THEN ROUND(c.subTotal,2)
			ELSE 0 END AS "otherFee",
			d.customerPo AS "cusotmerPo",
			so.courierNumber AS "courierNumber",
			CASE b.itemType WHEN 1 THEN '正常送货单'
			WHEN 2 THEN '客诉送货单'
			ELSE NULL END AS "itemTypeStr",
			DATE_FORMAT(a.signBackDate,'%Y-%m-%d %h:%i:%s') AS "signBackDate",
			su.userName AS "signBackUserName",
			i.value AS "courierComName",
			a.signBackNum,
			a.spares,
			CONCAT(
				IFNULL(m.`name`,l.`name`),
				"-",
				IFNULL(m.`phone`,l.`phone`),
				"-",
				IFNULL(m.`address`,l.`address`)
			) AS "linkmanMsg",
			a.recordId AS "deliveryDetailId"
		FROM (SELECT @rownum:=0)r,sl_delivery_detail a
		LEFT JOIN sl_delivery b ON b.recordId = a.deliveryId AND b.companyId = a.companyId
		LEFT JOIN sl_contract_detail c ON c.recordId = a.contractDetailId AND c.companyId = a.companyId
		LEFT JOIN sl_contract d ON d.recordId = c.contractId AND d.companyId = a.companyId
		LEFT JOIN md_customer e ON e.recordId = d.customerId AND e.companyId = a.companyId
		LEFT JOIN sl_contract_craft f ON f.recordId = c.craftId AND f.companyId = a.companyId
		LEFT JOIN sl_price g ON g.recordId = c.priceId AND g.companyId = a.companyId
		LEFT JOIN md_dict_value h ON h.recordId = d.taxDescript AND h.itemId = 19 AND h.companyId = a.companyId
		LEFT JOIN sl_outbound so ON so.recordId = a.terminalDetailId AND so.companyId = a.companyId
		LEFT JOIN md_dict_value i ON i.recordId = so.courierCompanyId
		
		LEFT JOIN st_product_store j ON j.outBoundId = a.terminalDetailId AND j.contractDetailId = a.contractDetailId AND j.outBoundStocks = a.quantity
		AND j.groupCenterId = a.groupCenterId AND j.activeFlag = 1 AND j.companyId = a.companyId
		LEFT JOIN sm_user su ON su.recordId = j.signBackUserId

		LEFT JOIN md_customer k ON k.recordId = so.erpCustomerId
		LEFT JOIN md_customer_contact l ON l.recordId = k.bizPerson
		LEFT JOIN md_customer_contact m ON m.recordId = so.linkman
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1
		<!-- 送货单编号 -->
		<if test="no != null and no != ''">
			AND REPLACE(b.no,' ','') LIKE CONCAT('%', #{no}, '%')
		</if>
		<!-- 客户编号 -->
		<if test="customerNo != null and customerNo != ''">
			AND REPLACE(e.no,' ','') LIKE CONCAT('%', #{customerNo}, '%')
		</if>
		<!-- 客户名称 -->
		<if test="customerName != null and customerName != ''">
			AND REPLACE(e.name,' ','') LIKE CONCAT('%', #{customerName}, '%')
		</if>
		<!-- 状态 -->
		<if test="status != null and status != ''">
			AND a.status = #{status}
		</if>
		<!-- 合同编号 -->
		<if test="contractNo != null and contractNo !=''">
			AND REPLACE(d.no,' ','') like CONCAT('%', #{contractNo}, '%')
		</if>
		<!-- 客户型号 -->
		<if test="customerModel !=null and customerModel !=''">
			AND REPLACE(f.customerModel,' ','') like CONCAT('%', #{customerModel}, '%')
		</if>
		<!-- 生产编号 -->
		<if test="contractCraftNo !=null and contractCraftNo !=''">
			AND REPLACE(f.no,' ','') like CONCAT('%', #{contractCraftNo}, '%')
		</if>
		<!-- 客户订单号 -->
		<if test="customerPo !=null and customerPo !=''">
			AND REPLACE(d.customerPo,' ','') like CONCAT('%', #{customerPo}, '%')
		</if>
		<if test="outBoundNo != null and outBoundNo != ''">
			AND REPLACE(so.no,' ','') LIKE CONCAT('%', REPLACE(#{outBoundNo},' ',''), '%')
		</if>
		<if test="courierNumberFlag != null and courierNumberFlag != ''">
			<if test="courierNumberFlag == 1">
				AND so.courierNumber IS NOT NULL
			</if> 
			<if test="courierNumberFlag == 0">
				AND so.courierNumber IS NULL
			</if> 
		</if>
		<if test="courierNumber != null and courierNumber != ''">
			AND REPLACE(so.courierNumber,' ','') LIKE CONCAT('%', REPLACE(#{courierNumber},' ',''), '%')
		</if>
		<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
			AND b.sentTime <![CDATA[>=]]> #{sentTimeStartQr}
		</if>
		<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
			AND b.sentTime <![CDATA[<=]]> #{sentTimeEndQr}
		</if>
	</select>
	
	<select id="getDelDetailGroupCenterId" resultType="String">
		SELECT
			GROUP_CONCAT(groupCenterId)
		FROM sl_delivery_detail
		WHERE deliveryId = #{recordId} AND activeFlag = 1 AND groupCenterId IS NOT NULL
	</select>
	
	<select id="getCustomerIdList" resultType="String">
		SELECT DISTINCT 
			b.customerId
		FROM sl_delivery_detail a
		LEFT JOIN sl_delivery b ON b.recordId = a.deliveryId
		WHERE a.activeFlag = 1 
		<if test="groupCenterId != null and groupCenterId != ''">
			AND FIND_IN_SET(a.groupCenterId,#{groupCenterId})
		</if>
		<if test="groupCenterId == null || groupCenterId == ''">
			AND a.deliveryId = #{deliveryId}
		</if>
	</select>
	
	<update id="updateSignBackDate">
		UPDATE sl_delivery_detail SET
			signBackDate = #{signBackDate},
			signBackNum = IFNULL(signBackNum,0) + #{signBackNum}
		WHERE recordId IN (${recordId})
	</update>
	
	<update id="cleanDataByIds">
		UPDATE sl_delivery_detail SET
			activeFlag = 2
		WHERE FIND_IN_SET(recordId,#{recordId})
	</update>

	<select id="getDelDetailList" resultType="DeliveryDetail">
		SELECT
			a.recordId,
			a.quantity,
			CASE a.`status` WHEN 500516 THEN '未确认'
							WHEN 500517 THEN '已确认'
							WHEN 500518 THEN '已出库'
							ELSE '无' END AS "showStatus",
			b.recordId AS "delivery.recordId",
			b.`no` AS "delivery.no",
			b.sentTime AS "delivery.sentTime",
			b.address AS "delivery.address",
			f.`value` AS "delivery.deliveryWayValue",
			c.`no` AS "delivery.customer.no",
			c.shortName AS "delivery.customer.shortName",
			g.shortName AS "delivery.branch.shortName",
			h.`no` AS "contract.no",
			h.customerPo AS "contract.customerPo",
			e.`no` AS "contractDetail.craft.no",
			e.customerModel AS "contractDetail.craft.customerModel",
			CONCAT(CAST(IFNULL(e.pnlLength,0) AS CHAR)+0,'*',CAST(IFNULL(e.pnlWidth,0) AS CHAR)+0,'/',CAST(IFNULL(e.pnlDivisor,1) AS CHAR) + 0) AS "contractDetail.deliveSize"
		FROM sl_delivery_detail a
				 LEFT JOIN sl_delivery b ON b.recordId = a.deliveryId AND b.companyId = a.companyId
				 LEFT JOIN md_customer c ON c.recordId = b.customerId AND c.companyId = a.companyId
				 LEFT JOIN sl_contract_detail d ON d.recordId = a.contractDetailId AND d.companyId = a.companyId
				 LEFT JOIN sl_contract_craft e ON e.recordId = d.craftId AND e.companyId = a.companyId
				 LEFT JOIN md_dict_value f ON f.recordId = b.deliveryWay AND f.companyId = a.companyId
				 LEFT JOIN md_branch g ON g.recordId = b.branchId AND g.companyId = a.companyId
				 LEFT JOIN sl_contract h ON h.recordId = d.contractId AND h.companyId = a.companyId
		WHERE b.recordId = #{recordId} AND a.activeFlag = 1
	</select>

	<select id="getReportDetail" resultType="DeliveryDetail">
			SELECT
				ROUND(i.pnlLength * i.pnlWidth * a.quantity / i.pnlDivisor / 1000000,4) AS "area",
				a.quantity AS "quantity",
				ROUND(a.quantity * j.price,2) AS "amount",
				i.`no` AS "craftNo",
				i.customerModel AS "customerModel",
				e.customerPo AS "customerPo"
			FROM st_product_store a
			LEFT JOIN sl_contract_detail b ON b.recordId = a.contractDetailId
			LEFT JOIN icloud_group_center c ON c.recordId = b.groupCenterId
			LEFT JOIN sl_contract_detail d ON d.recordId = IFNULL(c.contactDeailId,b.groupCenterId)
			LEFT JOIN sl_contract e ON e.recordId = d.contractId
			LEFT JOIN icloud_group_org_relation f ON f.deptId = e.deptId AND f.activeFlag = 1
			LEFT JOIN icloud_group_org igo ON igo.recordId = f.groupOrgId
			LEFT JOIN md_customer g ON g.recordId = e.customerId
			LEFT JOIN sm_user h ON h.recordId = IFNULL(e.userId,g.salesman)
			LEFT JOIN sl_contract_craft i ON i.recordId = d.craftId
			LEFT JOIN sl_price j ON j.recordId = d.priceId
			WHERE a.activeFlag = 1 AND (CASE WHEN b.recordId = d.recordId THEN 1 ELSE 2 END) = 1
			AND a.`status`  =99999901  AND a.inOutType = 12
			<if test="groupOrgId != null and groupOrgId != ''">
				AND FIND_IN_SET(f.groupOrgId,#{groupOrgId})
			</if>
			<if test="craftNo != null and craftNo != ''">
				AND REPLACE(i.`no`," ","") LIKE CONCAT('%', REPLACE(#{craftNo}," ",""), '%')
			</if>
			<if test="customerModel != null and customerModel != ''">
				AND REPLACE(i.customerModel," ","") LIKE CONCAT('%', REPLACE(#{customerModel}," ",""), '%')
			</if>
			<if test="customerPro != null and customerPro != ''">
				AND REPLACE(e.customerPo," ","") LIKE CONCAT('%', REPLACE(#{customerPro}," ",""), '%')
			</if>
			<if test="dimensionType != null and dimensionType != ''">
				<!-- 客户 -->
				<if test="dimensionType == 1">
					AND g.name = #{departName}
				</if>
				<!-- 部门 -->
				<if test="dimensionType == 2">
					AND igo.name = #{departName}
				</if>
				<!-- 供应商 -->
				<if test="dimensionType == 3">

				</if>
				<!-- 业务员 -->
				<if test="dimensionType == 4">
					AND h.userName = #{departName}
				</if>
			</if>
			<if test="dateType != null and dateType != ''">
				<!-- 日 -->
				<if test="dateType == 1">
					AND DATE(a.operateDate) IN (${queryDateStrs})
				</if>
				<!-- 周 -->
				<if test="dateType == 2">
					AND YEARWEEK(DATE(a.operateDate),7) IN (${queryDateStrs})
				</if>
				<!-- 月 -->
				<if test="dateType == 3">
					AND DATE_FORMAT(a.operateDate,'%Y%m') IN (${queryDateStrs})
				</if>
				<!-- 自定义 -->
				<if test="dateType == 4">
					AND a.operateDate >= #{sentTimeStartQr} AND a.createdDate <![CDATA[<=]]> #{sentTimeEndQr}
				</if>
			</if>
			GROUP BY b.recordId,a.recordId
	</select>
	<select id="selectDeliveryList" resultType="DeliveryDetail">
			SELECT
				recordId,
				conDetId,
				orderDate,
				quantity,
				deliveryDate,
				orderArea
			FROM
				sl_contract_detail_deliverys
			WHERE
				activeFlag = 1
				AND conDetId = #{conDetId}
				AND companyId = #{companyId}

	</select>

	<update id="updateDelivery">
		update sl_contract_detail_deliverys set quantity = #{quantity},deliveryDate = #{deliveryDate},
		orderArea = #{orderArea} WHERE recordId = #{recordId}
	</update>

	<insert id="insertDelivery">
		INSERT INTO sl_contract_detail_deliverys(
				companyId,
				conDetId,
				orderDate,
				quantity,
				deliveryDate,
				orderArea,
				activeFlag,
				createdDate,
				createdBy,
				lastUpdDate,
				lastUpdBy)
				VALUES
				(
					#{company.recordId},
					#{contractDetailId},
					#{orderDateTwo},
					#{quantity},
					#{deliveryDate},
					#{orderArea},
					1,
					NOW(),
					#{createdBy.recordId},
					NOW(),
					#{lastUpdBy.recordId}
				)
	</insert>
	<select id="getSchedulingDeliveryList" resultType="DeliveryDetail">
		SELECT
			a.createdDate,
			ROUND((IFNULL( a.quantity, 0 )* IFNULL( c.pnlWidth, 0 )* IFNULL( c.pnlLength, 0 ))/ IFNULL( c.pnlDivisor, 1 )/ 1000000,4 ) AS "area"
		FROM
			sl_delivery_detail a
			LEFT JOIN sl_contract_detail b ON b.recordId = a.contractDetailId
			LEFT JOIN sl_contract_craft c ON c.recordId = b.craftId
		WHERE
			a.activeFlag = 1
			AND a.companyId = #{company.recordId}
			AND b.recordId IN (
			SELECT
				c.recordId
			FROM
				sl_notification a
				LEFT JOIN sl_notification b ON b.mergeId = a.recordId
				LEFT JOIN sl_contract_detail c ON c.recordId = IFNULL( a.contractDetailId, b.contractDetailId )
			WHERE
				a.`no` = #{no}
			)
		ORDER BY
			a.createdDate DESC
	</select>
	
	<select id="getCostExpense" resultType="DeliveryDetail">
		SELECT
			ROUND((CASE WHEN aa.inOutFlag IS NULL OR aa.inOutFlag = 0 THEN -aa.estimateCost ELSE aa.estimateCost END) * IFNULL(aa.exchangeRate,1),2) AS "costExpense",
			aa.deliveryDetailId AS "deliveryDetailId"
		FROM
		(
			SELECT
				IF
				(
					(
					SELECT
					COUNT( aa.recordId )
					FROM sl_delivery_detail aa
					WHERE aa.contractDetailId = b.contractDetailId AND aa.activeFlag = 1
					AND aa.`status` = 500518 AND aa.recordId <![CDATA[<]]> b.recordId
					)= 0,
					IF(a.inOutFlag = 1,IFNULL(IFNULL(pp.mouldFee,a.mouldFee), 0)+ IFNULL(IFNULL(pp.testShelfFee,a.testShelfFee), 0 )+ IFNULL(IFNULL(pp.engineeringFee,a.engineeringFee),0)
					+ IFNULL(IFNULL(pp.othersFee,a.othersFee),0) + IFNULL(IFNULL(pp.filmFee,a.filmFee),0),0) +
					ROUND(IFNULL(IFNULL(pp.price,i.jxPrice) * a.quantity,0)*IF(i.recordId IS NOT NULL,1,(j.quantity/IFNULL( e.quantity, h.quantity ))),2),
					ROUND(IFNULL(IFNULL(pp.price,i.jxPrice) * a.quantity ,0)*IF(i.recordId IS NOT NULL,1,(j.quantity/IFNULL( e.quantity, h.quantity ))),2)
				) +ROUND(IF(a.inOutFlag = 0,- IFNULL(a.originalAmount,a.amount)+a.price*a.quantity, 0),2) AS "estimateCost",
				a.inOutFlag AS "inOutFlag",
				b.recordId AS "deliveryDetailId",
				l.exchangeRate AS "exchangeRate"
			FROM sl_goods_check a
			LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId AND a.inOutFlag = 1
			LEFT JOIN st_reject_application c ON c.recordId = b.complaintId
			LEFT JOIN sl_outbound d ON d.recordId = b.terminalDetailId
			LEFT JOIN sl_contract_detail e ON e.recordId = b.contractDetailId
			LEFT JOIN st_product_store f ON f.recordId = a.sourceId AND a.inOutFlag = 0 AND f.complaintId IS NOT NULL
			LEFT JOIN st_reject_application g ON g.recordId = f.complaintId
			LEFT JOIN sl_contract_detail h ON h.recordId = f.contractDetailId
			LEFT JOIN icloud_group_center i ON i.recordId = IFNULL(e.groupCenterId,h.groupCenterId)
			LEFT JOIN pu_prdorder_detail j ON j.contractDetailId = IFNULL(e.recordId,h.recordId) AND j.companyId = a.companyId AND j.activeFlag = 1
			LEFT JOIN pu_material_check k ON k.prdorderDetailId = j.recordId AND k.activeFlag = 1 AND k.companyId = a.companyId
			AND CASE WHEN a.inOutFlag = 1 THEN k.oldOutBoundId = d.oldOutBoundId ELSE 1=1 END
			AND CASE WHEN IFNULL(c.recordId,g.recordId) IS NOT NULL
			THEN k.complaintId = IFNULL(c.recordId,g.recordId) ELSE 1=1 END AND k.period = a.period AND k.quantity = a.quantity AND k.inOutFlag = a.inOutFlag
			LEFT JOIN pu_price pp ON pp.recordId = j.priceId
			LEFT JOIN sl_contract l ON l.recordId = e.contractId
			WHERE a.activeFlag = 1 AND b.recordId IN (${deliveryDetailId})
		) aa
	</select>

	<select id="getDeliveryDetailList" resultType="DeliveryDetail">
			SELECT
				a.contractDetailId,
				MIN(a.createdDate) AS "firstDate",
				MAX(a.createdDate) AS "currentDate",
				SUM(a.quantity) AS "sumQuotation",
				SUM(ROUND(IFNULL(a.quantity,0) * IFNULL(scc.pnlLength,0) * IFNULL(scc.pnlWidth,0) / IFNULL(scc.pnlDivisor,1) / 1000000,4)) AS "deliveryArea",
				scd.quantity AS "quantity"
			FROM
				sl_delivery_detail a
				LEFT JOIN sl_contract_detail scd ON scd.recordId = a.contractDetailId
				LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftid
			WHERE
				a.activeFlag = 1
				AND FIND_IN_SET(a.contractDetailId,#{contractDetailIds})
				GROUP BY a.contractDetailId
	</select>

	<select id="getDeliveryDetailData" resultType="DeliveryDetail">
		SELECT
			b.`no` AS "delivery.no",
			b.sentTime AS "delivery.sentTime",
			a.quantity,
			a.spares,
			d.`no` AS "craftNo",
			c.userName AS "saleName",
			a.createdDate,
			ROUND(f.pnlLength * f.pnlWidth * a.quantity / f.pnlDivisor / 1000000 ,4) AS "area"
		FROM sl_delivery_detail a
		LEFT JOIN sl_delivery b ON b.recordId = a.deliveryId
		LEFT JOIN sm_user c ON c.recordId = a.createdBy
		LEFT JOIN md_material d ON d.recordId = a.materialId
		LEFT JOIN sl_contract_detail e ON e.recordId = a.contractDetailId
		LEFT JOIN sl_contract_craft f ON f.recordId = e.craftId
		WHERE a.recordId = #{recordId}
	</select>

	<select id="getListByContractDetailId" resultType="DeliveryDetail">
		SELECT
			b.`no` AS "deliveryNo",
			b.itemType,
			a.recordId,
			a.contractDetailId,
			a.quantity,
			a.spares,
			a.createdDate,
			c.userName AS "deliveryName"
		FROM sl_delivery_detail a
		LEFT JOIN sl_delivery b ON b.recordId = a.deliveryId
		LEFT JOIN sm_user c ON c.recordId = b.createdBy
		WHERE a.activeFlag = 1 AND (a.contractDetailId) IN
		<foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
			#{item.finalContractDetail}
		</foreach>
		ORDER BY a.createdDate ASC
	</select>

</mapper>