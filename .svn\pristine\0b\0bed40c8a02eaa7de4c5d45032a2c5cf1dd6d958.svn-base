# 当前使用的 账套ID(即数据中心id)

# 第三方系统登录授权的账套ID（即open.kingdee.com网站的第三方系统登录授权中的数据中心标识）

# 在第三方系统登录授权页面点击“生成测试链接”按钮后即可查看

X-KDApi-AcctID = 67dcbf61bbb6a9

# 第三方系统登录授权的 集成用户名称

# 补丁版本为PT-146894 [7.7.0.202111]及后续的版本，则为指定用户登录列表中任一用户

# 若第三方系统登录授权已勾选“允许全部用户登录”，则无以上限制

X-KDApi-UserName = LSJ1

# 第三方系统登录授权的应用ID

X-KDApi-AppID = 307311_R5dP5dDE7lp5Waxp243PU60uUqQa6NLF

# 第三方系统登录授权的应用密钥

X-KDApi-AppSec = 842d7a3860c74b908ef12df25934dc30

# 账套语系，默认2052

# X-KDApi-LCID = 2052

# 组织编码，启用多组织时配置对应的组织编码才有效

# X-KDApi-OrgNum = *******

# 服务Url地址(私有云和公有云都须配置金蝶云星空产品地址，K3Cloud/结尾)

X-KDApi-ServerUrl = http://120.76.98.212:8090/K3Cloud
