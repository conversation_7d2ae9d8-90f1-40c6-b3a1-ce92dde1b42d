package com.kyb.pcberp.modules.oa.pojo.audit;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;

@SuppressWarnings("serial")
public class Oa_auditRecord extends DataEntity<Oa_auditRecord>
{
    private String auditId;
    
    private String status;
    
    private String approveLevel;
    
    private String turn;
    
    private String createBy;
    
    private String createByRecordId;
    
    private String createByName;
    
    private String createDate;
    
    private String lastUpdateBy;
    
    private String lastUpdateByName;
    
    private String lastUpdateDate;
    
    private String approveUsers;
    
    private String approveUsersName;
    
    private String handelPositionId;
    
    private String handelPositionIdName;
    
    private String maxTurn;
    
    private String currApproveLevel;
    
    /** 审批时的评论 **/
    private String content;
    
    /** erp审批单Id */
    private String approvalId;
    
    /** erp审批角色Id */
    private String positionId;
    
    /** erp上一个审批流程Id */
    private String previousId;
    
    /** erp审批 */
    private String framework;
    
    private String createdByName;
    
    private String lastUpdByName;
    
    private Hr_Employee employee;
    
    public String getAuditId()
    {
        return auditId;
    }
    
    public void setAuditId(String auditId)
    {
        this.auditId = auditId;
    }
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public String getApproveLevel()
    {
        return approveLevel;
    }
    
    public void setApproveLevel(String approveLevel)
    {
        this.approveLevel = approveLevel;
    }
    
    public String getTurn()
    {
        return turn;
    }
    
    public void setTurn(String turn)
    {
        this.turn = turn;
    }
    
    public String getCreateBy()
    {
        return createBy;
    }
    
    public void setCreateBy(String createBy)
    {
        this.createBy = createBy;
    }
    
    public String getCreateByRecordId()
    {
        return createByRecordId;
    }
    
    public void setCreateByRecordId(String createByRecordId)
    {
        this.createByRecordId = createByRecordId;
    }
    
    public String getCreateByName()
    {
        return createByName;
    }
    
    public void setCreateByName(String createByName)
    {
        this.createByName = createByName;
    }
    
    public String getCreateDate()
    {
        return createDate;
    }
    
    public void setCreateDate(String createDate)
    {
        this.createDate = createDate;
    }
    
    public String getLastUpdateBy()
    {
        return lastUpdateBy;
    }
    
    public void setLastUpdateBy(String lastUpdateBy)
    {
        this.lastUpdateBy = lastUpdateBy;
    }
    
    public String getLastUpdateByName()
    {
        return lastUpdateByName;
    }
    
    public void setLastUpdateByName(String lastUpdateByName)
    {
        this.lastUpdateByName = lastUpdateByName;
    }
    
    public String getLastUpdateDate()
    {
        return lastUpdateDate;
    }
    
    public void setLastUpdateDate(String lastUpdateDate)
    {
        this.lastUpdateDate = lastUpdateDate;
    }
    
    public String getApproveUsers()
    {
        return approveUsers;
    }
    
    public void setApproveUsers(String approveUsers)
    {
        this.approveUsers = approveUsers;
    }
    
    public String getApproveUsersName()
    {
        return approveUsersName;
    }
    
    public void setApproveUsersName(String approveUsersName)
    {
        this.approveUsersName = approveUsersName;
    }
    
    public String getHandelPositionId()
    {
        return handelPositionId;
    }
    
    public void setHandelPositionId(String handelPositionId)
    {
        this.handelPositionId = handelPositionId;
    }
    
    public String getHandelPositionIdName()
    {
        return handelPositionIdName;
    }
    
    public void setHandelPositionIdName(String handelPositionIdName)
    {
        this.handelPositionIdName = handelPositionIdName;
    }
    
    public String getMaxTurn()
    {
        return maxTurn;
    }
    
    public void setMaxTurn(String maxTurn)
    {
        this.maxTurn = maxTurn;
    }
    
    public String getCurrApproveLevel()
    {
        return currApproveLevel;
    }
    
    public void setCurrApproveLevel(String currApproveLevel)
    {
        this.currApproveLevel = currApproveLevel;
    }
    
    public String getContent()
    {
        return content;
    }
    
    public void setContent(String content)
    {
        this.content = content;
    }
    
    public String getApprovalId()
    {
        return approvalId;
    }
    
    public void setApprovalId(String approvalId)
    {
        this.approvalId = approvalId;
    }
    
    public String getPositionId()
    {
        return positionId;
    }
    
    public void setPositionId(String positionId)
    {
        this.positionId = positionId;
    }
    
    public String getPreviousId()
    {
        return previousId;
    }
    
    public void setPreviousId(String previousId)
    {
        this.previousId = previousId;
    }
    
    public String getFramework()
    {
        return framework;
    }
    
    public void setFramework(String framework)
    {
        this.framework = framework;
    }
    
    public String getCreatedByName()
    {
        return createdByName;
    }
    
    public void setCreatedByName(String createdByName)
    {
        this.createdByName = createdByName;
    }
    
    public String getLastUpdByName()
    {
        return lastUpdByName;
    }
    
    public void setLastUpdByName(String lastUpdByName)
    {
        this.lastUpdByName = lastUpdByName;
    }

    public Hr_Employee getEmployee()
    {
        return employee;
    }

    public void setEmployee(Hr_Employee employee)
    {
        this.employee = employee;
    }
    
}
