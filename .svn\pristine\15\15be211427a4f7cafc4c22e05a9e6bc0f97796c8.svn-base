<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.crm.dao.ReceiveTaskDao">
    <select id="getSingleReceivableList" resultType="Hr_CollectPayBill">
        SELECT
        a.recordId,
        a.status,
        a.no AS "no",
        a.period AS "period",
        a.companyId AS "companyId",
        a.recordId AS "collectionPaymentId",
        b.recordId AS "undertakeId",
        b.departmentName AS "departmentName",
        "应收账款" AS "categoryName",
        c.name AS "customerName",
        c.no AS "customerNo",
        "ERP" AS "typeName",
        "应收单" AS "documentType",
        a.createdDate,
        "1" AS "typeStatus",
        (
        SELECT
        su.userName
        FROM
        md_approval aa
        LEFT JOIN sm_user su ON su.recordId = aa.createdBy
        WHERE aa.activeFlag = 1 AND aa.typeId = 18
        AND aa.status = "60002"
        AND aa.companyId = a.companyId
        AND aa.dataId = sar.recordId ORDER BY aa.createdDate DESC LIMIT 1
        ) AS "commitName",
        (
        SELECT
        su.userName
        FROM
        md_approval bb
        LEFT JOIN sm_user su ON su.recordId = bb.lastUpdBy
        WHERE bb.activeFlag = 1 AND bb.typeId = 18
        AND bb.status = "60002"
        AND bb.companyId = a.companyId
        AND bb.dataId = sar.recordId ORDER BY bb.createdDate DESC LIMIT 1
        ) AS "employeeName",
        IFNULL(a.sentGoodsValue,0) + IFNULL(a.adjustValue,0) - IFNULL(a.returnGoodsValue,0) - IFNULL(e.amountReceivable,0) - IFNULL(g.settlementAmount, 0) AS "amount",
        IFNULL(a.sentGoodsValue,0) + IFNULL(a.adjustValue,0) - IFNULL(a.returnGoodsValue,0) AS "totalAmount",
        IFNULL(e.amountReceivable,0) AS "receivedAmount",
        (SELECT count(1) FROM sl_collect_much_money WHERE activeFlag = 1 AND period = a.period AND customerId = a.customerId AND companyId = a.companyId) AS "number",
        a.receivedAmount AS "receivedAmountT",
        f.name AS "deptName",
        a.singleReceivableDate AS "singleReceivableDate",
        IFNULL(g.settlementAmount,0) AS "settlementAmount",
        a.settlementStatus AS "settlementStatus"
        FROM
        sl_single_receivable a
        LEFT JOIN sl_accounts_receivable sar ON sar.customerId = a.customerId AND sar.period = a.period AND sar.companyId = a.companyId
        LEFT JOIN oa_department_relation b ON b.companyId = a.companyId
        AND b.activeFlag = 1 AND b.salarySystemId IS NULL
        LEFT JOIN md_customer c ON c.recordId = a.customerId
        LEFT JOIN sm_user d ON d.recordId = a.createdBy
        LEFT JOIN (
        SELECT
        a.receivedPaidId AS "receivedPaidId",
        SUM(IFNULL(a.amountReceivable,0) + IFNULL(a.procedureFree,0) + IFNULL(a.otherFree,0)) AS "amountReceivable"
        FROM
        hr_receipt_payment_records a
        LEFT JOIN hr_receipt_payment_bill b ON b.recordId = a.receiptPaymentId
        WHERE a.activeFlag = 1
        GROUP BY a.receivedPaidId
        )e ON e.receivedPaidId = a.recordId
        LEFT JOIN md_department f ON f.recordId = c.deptId
        LEFT JOIN (
            SELECT SUM(IFNULL(expensePrice,0)) AS "settlementAmount",collectTaskId FROM oa_audit WHERE activeFlag = 1
                AND (applicationsType = 2 OR applicationsType = 3)
                AND (applicationsResult IS NULL OR applicationsResult = 'assent')
                AND collectTaskId IS NOT NULL
            GROUP BY collectTaskId
        ) g ON g.collectTaskId = a.recordId
        WHERE
        a.activeFlag = 1 AND b.companyId IS NOT NULL AND b.companyId = #{companyId}
        <if test="sentTimeStartQr != null and sentTimeStartQr != ''">
            AND DATE_FORMAT(a.singleReceivableDate, '%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{sentTimeStartQr}, '%Y-%m-%d')
        </if>
        <if test="sentTimeEndQr != null and sentTimeEndQr != ''">
            AND DATE_FORMAT(a.singleReceivableDate, '%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{sentTimeEndQr}, '%Y-%m-%d')
        </if>
        <if test="customerName != null and customerName != ''">
            AND REPLACE(c.name," ","") LIKE CONCAT('%',REPLACE(#{customerName}," ",""),'%')
        </if>
        <if test="customerNo != null and customerNo != ''">
            AND REPLACE(c.no," ","") LIKE CONCAT('%',REPLACE(#{customerNo}," ",""),'%')
        </if>
        <if test="no != null and no != ''">
            AND REPLACE(a.no," ","") LIKE CONCAT('%',REPLACE(#{no}," ",""),'%')
        </if>
        <if test="departmentId != null and departmentId != ''">
            AND c.deptId = #{departmentId}
        </if>
        <!-- 年-->
        <if test="dateType == 1">
            <if test="period != null">
                AND LEFT(a.period,4) LIKE CONCAT('%',#{period},'%')
            </if>
        </if>
        <!-- 月 -->
        <if test="dateType == 2">
            <if test="period != null">
                AND RIGHT(a.period,2) LIKE CONCAT('%',#{period}, '%')
            </if>
        </if>
        <!-- 年月 -->
        <if test="dateType == 3">
            <if test="period != null">
                AND a.period =#{period}
            </if>
        </if>
        ORDER BY a.period
    </select>

    <update id="updateSingleStatus">
        UPDATE sl_single_receivable SET settlementStatus = #{settlementStatus} WHERE recordId = #{recordId}
    </update>

    <select id="loadSettlementDetailList" resultType="Hr_CollectPayBill">
        SELECT
            a.createdDate AS "createdDate",
            a.expensePrice AS "settlementAmount",
            a.auditResult AS "auditResult",
            a.applicationsType AS "applicationsType",
            a.applicationsResult AS "applicationsResult",
            a.recordId,
            b.no AS "no"
        FROM
            oa_audit a
            LEFT JOIN oa_number b ON b.id = a.recordId
        WHERE
            a.activeFlag = 1 AND a.collectTaskId = #{recordId}
    </select>

    <insert id="saveReceiveAttachement">
        INSERT INTO pu_receivetask_attachements(
            companyId,
            type,
            receiveTaskId,
            orgfilename,
            realfilename,
            fileurl,
            auditId,
            auditAttachId
        ) VALUES (
             #{company.recordId},
             #{type},
             #{receiveTaskId},
             #{orgFileName},
             #{realFileName},
             #{fileUrl},
             #{auditId},
             #{auditAttachId}
        )
    </insert>

    <select id="findPayApplicationInvoices" resultType="ReceiveTaskAttach">
        SELECT * FROM pu_receivetask_attachements WHERE auditId = #{auditId};
    </select>

    <update id="delete">
        DELETE FROM pu_receivetask_attachements WHERE recordId = #{recordId};
    </update>

    <select id="getAuditAttachId" resultType="String">
        SELECT auditAttachId FROM pu_receivetask_attachements WHERE recordId = #{recordId};
    </select>

</mapper>