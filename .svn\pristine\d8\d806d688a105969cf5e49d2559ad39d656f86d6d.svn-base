kybApp.controller('customerStatementCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'BaseUtil', 'CommonUtil', function ($rootScope, $scope, upida, $timeout, BaseUtil, CommonUtil) {
    $scope.$on('$viewContentLoaded', function () {
        MainCtrl.initAjax();
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
    var vm = this;
    vm.right = {};

    // tabs控制
    vm.tabs = {
        viewForm: { active: true}
    };
    // 时间范围的选项
    vm.rangeOptions = {
        // format: "YYYY-MM-DD",
        startDate: new Date((new Date).setMonth(((new Date).getMonth() - 1))),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() -5)))
    };

    // 时间范围的Model
    vm.time = {
        start: {},
        end: {}
    };

    vm.initDate = function(date) {
        if(date == "") {
            vm.rangeOptions = {
                // format: "YYYY-MM-DD",
                startDate: new Date(vm.rangeOptions.startDate),
                minDate: new Date(new Date(vm.rangeOptions.minDate).setFullYear(new Date(vm.rangeOptions.minDate).getFullYear()-5))
            };
            vm.time = {
                start: vm.rangeOptions.startDate,
                end: vm.rangeOptions.minDate
            }
        }
    };
    // 分页数据
    vm.page = {};
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    // 项分页数据
    vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "report/reportData/getCustomerStatementList";

    vm.doPage = function(page, pageSize, total)
    {
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        vm.getCustomerStatementList();
    };

    vm.pageSizeChange = function() {
        vm.getCustomerStatementList();
    };

    vm.customerProQuery = "";
    vm.craftNoQuery = "";
    vm.productionTypeQuery = "";
    vm.getCustomerStatementList = function () {
        let query = {};
        query.customerName = vm.customerName;
        query.type = vm.type;
        if(vm.time.start) {
            query.sentTimeStartQr = vm.time.start.valueOf();
        }
        if(vm.time.end) {
            query.sentTimeEndQr = vm.time.end.valueOf();
        }
        query.pageNo = vm.page.pageNo;
        query.pageSize = vm.page.pageSize;
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post(vm.page.url, query).then(function (data) {
            // 计算开始数
            data.startCount = (data.pageNo - 1) * data.pageSize + 1;
            // 计算结束数
            data.endCount = (data.pageNo - 1) * data.pageSize;
            if(data && data.list && data.list.length > 0)
            {
                // 计算结束数
                data.endCount = data.endCount + data.list.length;
            }
            vm.page.data = data;
            MainCtrl.unblockUI();
        });
    };

    vm.showDetailMessage = function (row) {
        const query = {};
        query.recordId = row.recordId;
        query.contractDetailId = row.contractDetailId;
        query.deliveryId = row.deliveryId;
        MainCtrl.blockUI({
            animate: true,
        });
        vm.showDetailMessageList = [];
        upida.post("report/reportData/getDetailMessageList",query).then(function (data) {
            vm.showDetailMessageList = data;
            $('#detailMessage').modal();
            MainCtrl.unblockUI();
        });
    };

    function loadRight() {
        vm.queryAll = CommonUtil.dataRangeIsAll("10950", BaseUtil.getMenuList());
        upida.get("common/rightall?prefix=count:customerStatement").then(function(data){
            vm.right.edit = data.edit;
            vm.right.view = data.view;
            vm.right.manage = data.manage;
            vm.getCustomerStatementList();
            vm.page.pageNo = 1;

            // 初始化第一页，条件为空
            vm.initDate(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            MainCtrl.unblockUI();

        });
    }

    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
        // // 初始化第一页，条件为空
        // vm.initDate("");
        // vm.getCustomerStatementList();
    });
}]);