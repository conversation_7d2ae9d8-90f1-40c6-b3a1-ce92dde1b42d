package com.kyb.pcberp.modules.production.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;

public class DiscardExportVo
{
    private String date; // 日期
    
    private String processName; // 工序名称
    
    private BigDecimal area; // 面积
    
    private Date lastUpdDate;

    private String inspectNo; // 品质单号

    private String batchDetailNo; // 批次明细编号

    private String craftNo; // 生产编号
    
    @ExcelField(title = "交板时间", align = 2, sort = 10)
    public String getDate()
    {
        return date;
    }

    public void setDate(String date)
    {
        this.date = date;
    }
    
    @ExcelField(title = "交板日期", align = 2, sort = 15)
    public String getDateStr()
    {
        if(null != date) 
        {
            return DateUtils.formatDate(DateUtils.parseDate(date), "yyyy-MM-dd");
        }
        return null;
    }

    @ExcelField(title = "工序名称", align = 2, sort = 20)
    public String getProcessName()
    {
        return processName;
    }

    public void setProcessName(String processName)
    {
        this.processName = processName;
    }

    @ExcelField(title = "面积", align = 2, sort = 30)
    public BigDecimal getArea()
    {
        return area;
    }

    public void setArea(BigDecimal area)
    {
        this.area = area;
    }

    public Date getLastUpdDate()
    {
        return lastUpdDate;
    }

    public void setLastUpdDate(Date lastUpdDate)
    {
        this.lastUpdDate = lastUpdDate;
    }

    @ExcelField(title = "品检审核时间", align = 2, sort = 40)
    public String getLastUpdDateStr() 
    {
        if(null != lastUpdDate) 
        {
            return DateUtils.formatDate(lastUpdDate, "yyyy-MM-dd HH:mm:ss");
        }
        return null;
    }
    
    @ExcelField(title = "品检审核日期", align = 2, sort = 50)
    public String getLastUpdDateStrTwo()
    {
        if(null != lastUpdDate) 
        {
            return DateUtils.formatDate(lastUpdDate, "yyyy-MM-dd");
        }
        return null;
    }

    @ExcelField(title = "品检单号", align = 2, sort = 60)
    public String getInspectNo()
    {
        return inspectNo;
    }

    public void setInspectNo(String inspectNo)
    {
        this.inspectNo = inspectNo;
    }

    @ExcelField(title = "批次号", align = 2, sort = 70)
    public String getBatchDetailNo()
    {
        return batchDetailNo;
    }

    public void setBatchDetailNo(String batchDetailNo)
    {
        this.batchDetailNo = batchDetailNo;
    }

    @ExcelField(title = "生产编号", align = 2, sort = 80)
    public String getCraftNo()
    {
        return craftNo;
    }

    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }
}
