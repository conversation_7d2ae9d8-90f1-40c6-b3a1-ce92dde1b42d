/**
 * 
 */
package com.kyb.pcberp.modules.finance.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;

/**
 * 收款管理导出Entity
 * 
 * <AUTHOR>
 * @version 2015-10-14
 */
public class CollectMoneyVo
{
    
    private String billNo; // 单据编号
    
    private String customerNo;// 客户编号
    
    private String customerShortName;// 客户名称
    
    private String fundType; // 款项类别
    
//    private String contractNo;// 合同编号
//    
//    private BigDecimal contractTotalAmt;// 合同金额
    
    private String collectWay; // 收款方式
    
    private BigDecimal amount; // 收款金额
    
    private Date collectedDate; // 收款日期
    
    private String bank; // 收款银行
    
    private String receiveAccount; // 收款账号
    
    private String userName;// 经办人
    
    private String remark; // 备注
    
    private String writeOffCause;       // 冲红原因
    
    private String status;     // 冲红状态
    
    
    @ExcelField(title = "冲红原因", align = 2, sort = 290)
    public String getWriteOffCause()
    {
        return writeOffCause;
    }

    public void setWriteOffCause(String writeOffCause)
    {
        this.writeOffCause = writeOffCause;
    }
    @ExcelField(title = "冲红状态", align = 2, sort = 270)
    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    @ExcelField(title = "单据编号", align = 2, sort = 10)
    public String getBillNo()
    {
        return billNo;
    }

    public void setBillNo(String billNo)
    {
        this.billNo = billNo;
    }
    @ExcelField(title = "客户编号", align = 2, sort = 30)
    public String getCustomerNo()
    {
        return customerNo;
    }

    public void setCustomerNo(String customerNo)
    {
        this.customerNo = customerNo;
    }
    @ExcelField(title = "客户名称", align = 2, sort = 50)
    public String getCustomerShortName()
    {
        return customerShortName;
    }

    public void setCustomerShortName(String customerShortName)
    {
        this.customerShortName = customerShortName;
    }
    @ExcelField(title = "款项类别", align = 2, sort = 70)
    public String getFundType()
    {
        return fundType;
    }

    public void setFundType(String fundType)
    {
        this.fundType = fundType;
    }
//    @ExcelField(title = "合同编号", align = 2, sort = 90)
//    public String getContractNo()
//    {
//        return contractNo;
//    }
//
//    public void setContractNo(String contractNo)
//    {
//        this.contractNo = contractNo;
//    }
//    @ExcelField(title = "合同金额", align = 2, sort = 110)
//    public BigDecimal getContractTotalAmt()
//    {
//        return contractTotalAmt;
//    }
//
//    public void setContractTotalAmt(BigDecimal contractTotalAmt)
//    {
//        this.contractTotalAmt = contractTotalAmt;
//    }
    @ExcelField(title = "收款方式", align = 2, sort = 130)
    public String getCollectWay()
    {
        return collectWay;
    }

    public void setCollectWay(String collectWay)
    {
        this.collectWay = collectWay;
    }
    @ExcelField(title = "收款金额", align = 2, sort = 150)
    public BigDecimal getAmount()
    {
        return amount;
    }

    public void setAmount(BigDecimal amount)
    {
        this.amount = amount;
    }
    @ExcelField(title = "收款日期", align = 2, sort = 170)
    public Date getCollectedDate()
    {
        return collectedDate;
    }

    public void setCollectedDate(Date collectedDate)
    {
        this.collectedDate = collectedDate;
    }
    @ExcelField(title = "收款银行", align = 2, sort = 190)
    public String getBank()
    {
        return bank;
    }

    public void setBank(String bank)
    {
        this.bank = bank;
    }
    @ExcelField(title = " 收款账号", align = 2, sort = 210)
    public String getReceiveAccount()
    {
        return receiveAccount;
    }

    public void setReceiveAccount(String receiveAccount)
    {
        this.receiveAccount = receiveAccount;
    }
    @ExcelField(title = "经办人", align = 2, sort = 230)
    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }
    @ExcelField(title = "备注", align = 2, sort = 300)
    public String getRemark()
    {
        return remark;
    }

    public void setRemark(String remark)
    {
        this.remark = remark;
    }
    
    
    
}