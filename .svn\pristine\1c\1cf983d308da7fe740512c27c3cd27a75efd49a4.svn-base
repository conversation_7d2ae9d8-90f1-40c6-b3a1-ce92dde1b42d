package com.kyb.pcberp.modules.wechat.service;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ListenableScheduledFuture;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.BaseService;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.CompanyUtil;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.contract.dao.ContractDao;
import com.kyb.pcberp.modules.contract.dao.ContractDetailDao;
import com.kyb.pcberp.modules.contract.entity.Contract;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.contract.entity.SaleCombina;
import com.kyb.pcberp.modules.contract.utils.ChangeDataUtils;
import com.kyb.pcberp.modules.crm.dao.QuotationDetailDao;
import com.kyb.pcberp.modules.crm.entity.QuotationDetail;
import com.kyb.pcberp.modules.crm.service.QuotationService;
import com.kyb.pcberp.modules.inter.dao.ConfigCustomerAttachementsDao;
import com.kyb.pcberp.modules.inter.entity.ConfigCustomerAttachements;
import com.kyb.pcberp.modules.production.dao.ProduceBatchDetailDao;
import com.kyb.pcberp.modules.production.entity.ProduceRecord;
import com.kyb.pcberp.modules.purch.dao.*;
import com.kyb.pcberp.modules.purch.entity.*;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.service.MaterialService;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.wechat.dao.AccountBindDao;
import com.kyb.pcberp.modules.wechat.dao.ProductDao;
import com.kyb.pcberp.modules.wechat.entity.*;
import com.kyb.pcberp.modules.wechat.utils.HttpRquestUtil;
import com.sun.org.apache.xpath.internal.operations.Quo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = true)
public class ProductService extends BaseService
{
    @Autowired
    private ProductDao productDao;

    @Autowired
    private MaterialDao materialDao;

    @Autowired
    private AccountBindDao accountBindDao;

    @Autowired
    private ProduceBatchDetailDao produceBatchDetailDao;

    @Autowired
    private ConfigCustomerAttachementsDao configCustomerAttachementsDao;

    @Autowired
    private KybReportService kybReportService;

    @Autowired
    private SupplierDao supplierDao;

    @Autowired
    private MaterialService materialService;

    @Autowired
    private QuotationService quotationService;

    @Autowired
    private QuotationDetailDao quotationDetailDao;

    public List<InterProduct> getProductList(InterProduct interProduct)
    {
        // userId 和 openId 取其一
        if (StringUtils.isBlank(interProduct.getUserId()) && StringUtils.isNotBlank(interProduct.getFinalFlag()) && interProduct.getFinalFlag().equals("1"))
        {
            String openId = "";
            IcloudUser user = accountBindDao.getUser(openId);
            interProduct.setPhone(user.getPhone());
        }
        // 供应商下单 OR 客户下单
        if (StringUtils.isNotBlank(interProduct.getType()))
        {
            // 查询内容
            if (StringUtils.isNotBlank(interProduct.getSerchMsg()))
            {
                String[] msgStrs = interProduct.getSerchMsg().split(" ");
                int len = msgStrs.length;
                if (len > 0)
                {
                    interProduct.setSerchMsg1(msgStrs[0]);
                }
                if (len > 1)
                {
                    interProduct.setSerchMsg2(msgStrs[1]);
                }
                if (len > 2)
                {
                    interProduct.setSerchMsg3(msgStrs[2]);
                }
                if (len > 3)
                {
                    interProduct.setSerchMsg4(msgStrs[3]);
                }
                if (len > 4)
                {
                    interProduct.setSerchMsg5(msgStrs[4]);
                }
                if (len > 5)
                {
                    interProduct.setSerchMsg6(msgStrs[5]);
                }
                if (len > 6)
                {
                    interProduct.setSerchMsg7(msgStrs[6]);
                }
                if (len > 7)
                {
                    interProduct.setSerchMsg8(msgStrs[7]);
                }
                if (len > 8)
                {
                    interProduct.setSerchMsg9(msgStrs[8]);
                }
                if (len > 9)
                {
                    interProduct.setSerchMsg10(msgStrs[9]);
                }
            }
            List<InterProduct> result = new ArrayList<>();
            if (interProduct.getType().equals("1"))
            {
                // 客户下单->加载所有订单->加载对应所有的订单明细->分配明细数据->返回
                result = productDao.getCustomerProductList(interProduct);
                // 获取id集合
                String ids = "";
                for(InterProduct product : result)
                {
                    ids = StringUtils.isBlank(ids) ? product.getRecordId() : (ids + "," + product.getRecordId());
                }
                List<InterProductDeail> deailList = productDao.getCustomerProductDeailList(ids);

                // 附件
                ConfigCustomerAttachements query = new ConfigCustomerAttachements();
                query.setRecordId(ids);
                List<ConfigCustomerAttachements> attachmentList = configCustomerAttachementsDao.getAttachmentList(query);
                for(InterProduct product : result)
                {
                    String id = product.getRecordId();
                    if (deailList != null && deailList.size() > 0)
                    {
                        // 产品列表，只显示配置了列表显示内容
                        product.setDeailList(deailList.stream().filter(d-> (StringUtils.isNotBlank(d.getMainId()) && id.equals(d.getMainId()))).collect(
                            Collectors.toList()));
                    }
                    if(Collections3.isNotEmpty(attachmentList))
                    {
                        product.setAttaList(attachmentList.stream().filter(d-> (StringUtils.isNotBlank(d.getConfigCustomerId()) && id.equals(d.getConfigCustomerId()))).collect(
                            Collectors.toList()));
                    }
                    // 查询订单进度
                    if (null != product.getContractDetail() && StringUtils.isNotBlank(product.getContractDetail().getRecordId()))
                    {
                        ContractDetail contractDetail = new ContractDetail();
                        contractDetail.setRecordId(product.getContractDetail().getRecordId());
                        product.setContractDetail(kybReportService.getProgress(contractDetail));
                    }
                }
            }
            else if (interProduct.getType().equals("2"))
            {
                // 供应商产品->加载所有订单->加载对应所有的订单明细->分配明细数据->返回
                result = productDao.getSupplierProductList(interProduct);
                // 获取id集合
                String ids = "";
                for(InterProduct product : result)
                {
                    ids = StringUtils.isBlank(ids) ? product.getRecordId() : (ids + "," + product.getRecordId());
                }
                List<InterProductDeail> deailList = productDao.getSupplierProductDeailList(ids);
                if (deailList != null && deailList.size() > 0)
                {
                    for(InterProduct product : result)
                    {
                        String id = product.getRecordId();
                        // 产品列表，只显示配置了列表显示内容
                        product.setDeailList(deailList.stream().filter(d-> (StringUtils.isNotBlank(d.getMainId()) && id.equals(d.getMainId()) &&
                                "1".equals(d.getListFlag()))).collect(Collectors.toList()));
                    }
                }
            }
            return result;
        }
        return null;
    }

    public List<InterProduct> getProductList(List<InterProduct> productList)
    {
        List<InterProduct> resList = new ArrayList<>();
        if (productList != null && productList.size() > 0)
        {
            for (int i = 0; i < productList.size(); i++)
            {
                InterProduct product = productList.get(i);
                if (StringUtils.isNotBlank(product.getStatus()) && product.getStatus().equals("1"))
                {
                    product.setType("2");
                    InterProduct entity = getProductDeail(product);
                    resList.add(entity);
                }
            }
        }
        return resList;
    }

    public InterProduct getProductDeail(InterProduct interProduct)
    {
        InterProduct product = new InterProduct();
        List<InterProductDeail> list = new ArrayList<>();
        if (StringUtils.isNotBlank(interProduct.getType()))
        {
            if (interProduct.getType().equals("1"))
            {
                product = productDao.getCustomerProduct(interProduct);
                list = productDao.getCustomerProductDeail(interProduct);
            }
            else if (interProduct.getType().equals("2"))
            {
                product = productDao.getSupplierProduct(interProduct);
                list = productDao.getSupplierProductDeail(interProduct);
            }
        }
        if (list != null && list.size() > 0)
        {
            for (InterProductDeail productDeail : list)
            {
                if (productDeail.getInputType().equals("6"))
                {
                    List<InterAttachMent> attachMents = productDao.getAttachMentList(productDeail);
                    productDeail.setAttachMents(attachMents);
                }
            }
        }
        product.setDeailList(list);
        return product;
    }

    @Transactional(readOnly = false)
    public String updateProductFlag(InterProduct interProduct)
    {
        if (StringUtils.isNotBlank(interProduct.getType()))
        {
            if (interProduct.getType().equals("1"))
            {
                productDao.updateCustomerProductFlag(interProduct);
            }
            else if (interProduct.getType().equals("2"))
            {
                productDao.updateSupplierProductFlag(interProduct);
            }
        }
        else
        {
            return "fail";
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String saveProduct(InterProduct interProduct, String openId, MultipartFile[] files)
    {
        if (StringUtils.isBlank(interProduct.getUserId()))
        {
            // 微信端
            IcloudUser user = accountBindDao.getUser(openId);
            interProduct.setUserId(user.getRecordId());
        }
        else
        {
            // 线上下单端
            interProduct.setCompanyId(interProduct.getUserId());
        }
        if (StringUtils.isNotBlank(interProduct.getType()))
        {
            String savePath = "";
            if (interProduct.getType().equals("1"))
            {
                // 客户下单
                if (StringUtils.isNotBlank(interProduct.getRecordId()))
                {
                    productDao.updateCustomerProduct(interProduct);
                }
                else
                {
                    // 编号赋值
                    interProduct.setNo(getCustomerProductNo());
                    productDao.insertCustomerProduct(interProduct);
                }
                productDao.deleteCustomerDeailList(interProduct);
                if (interProduct.getDeailList() != null && interProduct.getDeailList().size() > 0)
                {
                    productDao.insertCustomerDeailList(interProduct);
                }
                savePath = "is" + "/configCustomer/" + interProduct.getRecordId() + "/";
            }
            else if (interProduct.getType().equals("2"))
            {
                // 供应商产品
                if (StringUtils.isNotBlank(interProduct.getRecordId()))
                {
                    // 获取产品之前的单价，周期
                    InterProduct interProductTwo = new InterProduct();
                    interProductTwo.setRecordId(interProduct.getRecordId());
                    interProductTwo = productDao.getInterProduct(interProductTwo);
                    // 插入产品维护
                    if (null != interProduct.getPrice() || null != interProduct.getLeadTime() || null != interProduct.getStocks())
                    {
                        InterProductStockRecord record = new InterProductStockRecord();
                        record.setInoutPlace("产品维护");
                        record.setProductId(interProduct.getRecordId());
                        if (null != interProductTwo.getPrice())
                        {
                            record.setInitPrice(interProductTwo.getPrice());
                        }
                        else
                        {
                            record.setInitPrice(BigDecimal.ZERO);
                        }
                        if (null != interProductTwo.getStocks())
                        {
                            record.setInitStocks(interProductTwo.getStocks());
                        }
                        else
                        {
                            record.setInitStocks(BigDecimal.ZERO);
                        }
                        if (null != interProductTwo.getLeadTime())
                        {
                            record.setInitLeadTime(interProductTwo.getLeadTime());
                        }
                        else
                        {
                            record.setInitLeadTime(0);
                        }
                        if (null != interProduct.getPrice())
                        {
                            record.setPrice(interProduct.getPrice());
                        }
                        else
                        {
                            record.setPrice(BigDecimal.ZERO);
                        }
                        if (null != interProduct.getLeadTime())
                        {
                            record.setLeadTime(interProduct.getLeadTime());
                        }
                        else
                        {
                            record.setLeadTime(0);
                        }
                        if (null != interProduct.getStocks())
                        {
                            record.setStocks(interProduct.getStocks());
                        }
                        else
                        {
                            record.setStocks(BigDecimal.ZERO);
                        }
                        // 期初
                        BigDecimal initStocks = interProductTwo.getStocks() == null ? BigDecimal.ZERO : interProductTwo.getStocks();
                        // 结余
                        BigDecimal stocks = interProduct.getStocks() == null ? BigDecimal.ZERO : interProduct.getStocks();
                        BigDecimal changeStocks;
                        if (stocks.compareTo(initStocks) >= 0)
                        {
                            record.setInoutType("1");
                            changeStocks = stocks.subtract(initStocks);

                        }
                        else
                        {
                            record.setInoutType("2");
                            changeStocks = initStocks.subtract(stocks);
                        }
                        record.setChangeStocks(changeStocks);
                        productDao.insertSupplierStockRecord(record);
                    }
                    else
                    {
                        // 插入产品维护
                        if (null != interProduct.getPrice() || null != interProduct.getLeadTime() || null != interProduct.getStocks())
                        {
                            InterProductStockRecord record = new InterProductStockRecord();
                            record.setProductId(interProduct.getRecordId());
                            record.setInoutPlace("产品维护");
                            record.setInitPrice(BigDecimal.ZERO);
                            record.setInitStocks(BigDecimal.ZERO);
                            record.setInitLeadTime(0);
                            if (null != interProduct.getPrice())
                            {
                                record.setPrice(interProduct.getPrice());
                            }
                            else
                            {
                                record.setPrice(BigDecimal.ZERO);
                            }
                            if (null != interProduct.getLeadTime())
                            {
                                record.setLeadTime(interProduct.getLeadTime());
                            }
                            else
                            {
                                record.setLeadTime(0);
                            }
                            if (null != interProduct.getStocks())
                            {
                                record.setChangeStocks(interProduct.getStocks());
                            }
                            else
                            {
                                record.setChangeStocks(BigDecimal.ZERO);
                            }
                            record.setInoutType("1");
                            productDao.insertSupplierStockRecord(record);
                        }
                    }
                    productDao.updateSupplierProduct(interProduct);
                }
                else
                {
                    // 更新价格
                    productDao.insertSupplierProduct(interProduct);
                    // 插入产品维护
                    if (null != interProduct.getPrice() || null != interProduct.getLeadTime() || null != interProduct.getStocks())
                    {
                        InterProductStockRecord record = new InterProductStockRecord();
                        record.setProductId(interProduct.getRecordId());
                        record.setInoutPlace("产品维护");
                        record.setInitPrice(BigDecimal.ZERO);
                        record.setInitStocks(BigDecimal.ZERO);
                        record.setInitLeadTime(0);
                        if (null != interProduct.getPrice())
                        {
                            record.setPrice(interProduct.getPrice());
                        }
                        else
                        {
                            record.setPrice(BigDecimal.ZERO);
                        }
                        if (null != interProduct.getLeadTime())
                        {
                            record.setLeadTime(interProduct.getLeadTime());
                        }
                        else
                        {
                            record.setLeadTime(0);
                        }
                        if (null != interProduct.getStocks())
                        {
                            record.setChangeStocks(interProduct.getStocks());
                        }
                        else
                        {
                            record.setChangeStocks(BigDecimal.ZERO);
                        }
                        record.setInoutType("1");
                        productDao.insertSupplierStockRecord(record);
                    }
                }
                productDao.deleteSupplierDeailList(interProduct);
                if (interProduct.getDeailList() != null && interProduct.getDeailList().size() > 0)
                {
                    productDao.insertSupplierDeailList(interProduct);
                }

                List<InterProductDeail> list = productDao.getSupplierProductDeail(interProduct);
                // 规格型号转换
                setCraftSpecification(list, interProduct);
                productDao.updateSupplierProductSpe(interProduct);
                savePath = "icloudProduct/" + interProduct.getRecordId() + "/";
            }
            // 微信文件上传
            if (interProduct.getImgLocalList() != null)
            {
                for (ImgLocal imgLocal : interProduct.getImgLocalList())
                {
                    if (StringUtils.isBlank(imgLocal.getId()))
                    {
                        continue;
                    }
                    if (Collections3.isNotEmpty(imgLocal.getList()))
                    {
                        String url = HttpRquestUtil.getWechatUrl();
                        savePath = savePath + imgLocal.getId() + "/" + DateUtils.formatDateTime(new Date()) + "/";
                        int num = 1;
                        for (String server : imgLocal.getList())
                        {
                            String webUrl = url + server;
                            String name =
                                    interProduct.getRecordId() + imgLocal.getId() + DateUtils.formatDateTime(new Date())
                                            + (num++) + ".jpg";
                            String sourcePath = savePath + name;
                            FileManageUtils.wxUploadImg(webUrl, sourcePath);
                            // 存储路径到数据库
                            InterAttachMent interAttachMent = new InterAttachMent();
                            interAttachMent.setProductId(interProduct.getRecordId());
                            interAttachMent.setConfigId(imgLocal.getId());
                            interAttachMent.setFileUrl(sourcePath);
                            productDao.insertAttachMent(interAttachMent);
                        }
                    }
                }
            }
            // 储存上传的资料信息
            if (files != null && files.length > 0 && StringUtils.isNotBlank(savePath)) {
                // 资料信息储存路径
                String result = FileManageUtils.uploadFile(files, savePath);
                if (result == null)
                {
                    return "false";
                }
                // 上传文件
                for (int i = 0; i < files.length; i++)
                {
                    // 判断文件是否已上传
                    Integer count = configCustomerAttachementsDao.getAttachmentCount(interProduct.getRecordId(),files[i].getOriginalFilename());
                    if(null != count && count > 0)
                    {
                        continue;
                    }
                    String sourcePath = savePath + files[i].getOriginalFilename();
                    ConfigCustomerAttachements attachMent = new ConfigCustomerAttachements();
                    attachMent.setConfigCustomerId(interProduct.getRecordId());
                    attachMent.setFileUrl(sourcePath);
                    attachMent.setOrgFileName(files[i].getOriginalFilename());
                    attachMent.setRealFileName(files[i].getName());
                    attachMent.setType(files[i].getContentType());
                    configCustomerAttachementsDao.saveAttachment(attachMent);
                }
            }
        }
        else
        {
            return "请刷新重试";
        }
        return "success";
    }

    public List<InterProductStockRecord> getStockHistory(InterProduct interProduct)
    {
        return productDao.getStockHistory(interProduct);
    }

    @Transactional(readOnly = false)
    public String delAttachMent(String recordId)
    {
        productDao.delAttachMent(recordId);
        return "success";
    }

    @Transactional(readOnly = false)
    public String expProduct(InterProduct interProduct, String openId)
    {
        if (interProduct.getProductList() == null || interProduct.getProductList().size() == 0)
        {
            return "请选择要添加的产品";
        }
        if (StringUtils.isNotBlank(interProduct.getType()))
        {
            IcloudUser user = accountBindDao.getUser(openId);
            if (interProduct.getType().equals("1"))
            {
                // 客户暂时不管
            }
            else if (interProduct.getType().equals("2"))
            {
                interProduct.setUserId(user.getRecordId());
                productDao.expSupplierProductList(interProduct.getProductList(), user.getRecordId());
                String recordId = produceBatchDetailDao.getRecordId();
                if (StringUtils.isNotBlank(recordId))
                {
                    Integer id = Integer.valueOf(recordId) - 1;
                    for (InterProduct product : interProduct.getProductList())
                    {
                        id++;
                        product.setMainId(id.toString());
                    }
                }
                String deailIds = "";
                for (InterProduct product : interProduct.getProductList())
                {
                    deailIds =
                        StringUtils.isBlank(deailIds) ? product.getRecordId() : deailIds + "," + product.getRecordId();
                }
                if (StringUtils.isNotBlank(deailIds))
                {
                    List<InterProductDeail> addList = new ArrayList<>();
                    List<InterProductDeail> list = productDao.getSupplierProductDeailList(deailIds);
                    for (InterProductDeail deail : list)
                    {
                        for (InterProduct product : interProduct.getProductList())
                        {
                            if (StringUtils.isNotBlank(product.getRecordId())
                                && StringUtils.isNotBlank(deail.getMainId()) && deail.getMainId()
                                .equals(product.getRecordId()))
                            {
                                if (StringUtils.isNotBlank(product.getMainId()))
                                {
                                    deail.setMainId(product.getMainId());
                                    addList.add(deail);
                                }
                            }
                        }
                    }
                    productDao.expSupplierProductDeailList(addList);
                }
            }
        }
        else
        {
            return "请刷新重试";
        }
        return "success";
    }

    public void setCraftSpecification(List<InterProductDeail> list, InterProduct interProduct)
    {
        String thickness = "";
        String copperClad = "";
        String length = "";
        String width = "";
        String daore = "";
        String naiya = "";
        String film = "";
        String pcbType = "";
        String simple = "";
        String gum = "";
        String insulat = "";
        String cuprum = "";
        String special = "";
        String area = "";
        if (list != null && list.size() > 0)
        {
            for (InterProductDeail interProductDeail : list)
            {
                if (StringUtils.isNotBlank(interProductDeail.getDictValue()))
                {
                    if (interProductDeail.getSourceId().equals("3"))
                    {
                        thickness = interProductDeail.getDictValue();
                    }
                    else if (interProductDeail.getSourceId().equals("58"))
                    {
                        daore = interProductDeail.getDictValue();
                    }
                    else if (interProductDeail.getSourceId().equals("59"))
                    {
                        naiya = interProductDeail.getDictValue();
                    }
                    else if (interProductDeail.getSourceId().equals("31"))
                    {
                        copperClad = interProductDeail.getDictValue();
                    }
                    else if (interProductDeail.getSourceId().equals("71"))
                    {
                        film = interProductDeail.getDictValue();
                    }
                    else if (interProductDeail.getSourceId().equals("1"))
                    {
                        pcbType = interProductDeail.getDictValue();
                    }
                    else if (interProductDeail.getSourceId().equals("94"))
                    {
                        area = interProductDeail.getDictValue();
                    }
                }
                else if (interProductDeail.getInputType().equals("2") || interProductDeail.getInputType().equals("3"))
                {
                    length = interProductDeail.getValue1();
                    width = interProductDeail.getValue2();
                }
                else if (StringUtils.isNotBlank(interProductDeail.getName()))
                {
                    if (interProductDeail.getName().contains("样品"))
                    {
                        simple = interProductDeail.getValue();
                    }
                    else if (interProductDeail.getName().contains("胶"))
                    {
                        gum = interProductDeail.getValue();
                    }
                    else if (interProductDeail.getName().contains("绝缘层"))
                    {
                        insulat = interProductDeail.getValue();
                    }
                    else if (interProductDeail.getName().contains("无铜"))
                    {
                        cuprum = interProductDeail.getValue();
                    }
                    else if (interProductDeail.getName().contains("特殊要求"))
                    {
                        special = interProductDeail.getValue();
                    }
                }
            }
        }
        String specification = "";
        if (StringUtils.isNotBlank(thickness))
        {
            String thicknessStr = thickness.replace("mm", "厚");
            specification = thicknessStr;
        }
        if (StringUtils.isNotBlank(copperClad))
        {
            copperClad = copperClad.toLowerCase();
            String copperCladStr = "";
            if (copperClad == "2/0")
            {
                copperCladStr = "70um";
            }
            else if (copperClad == "1/0")
            {
                copperCladStr = "35um";
            }
            else if (copperClad == "h/0")
            {
                copperCladStr = "18um";
            }
            else if (copperClad == "18um")
            {
                copperCladStr = "18um";
            }
            else if (copperClad == "3/0")
            {
                copperCladStr = "105um";
            }
            else
            {
                copperCladStr = copperClad;
            }
            specification = specification + copperCladStr;
        }
        if (StringUtils.isNotBlank(specification))
        {
            specification = specification + " ";
        }
        if (StringUtils.isNotBlank(area))
        {
            specification = specification + area;
        }
        else
        {
            if (StringUtils.isNotBlank(length))
            {
                specification = specification + length + "*";
            }
            if (StringUtils.isNotBlank(width))
            {
                specification = specification + width + " ";
            }
        }

        if (StringUtils.isNotBlank(daore))
        {
            specification = specification + daore + " ";
        }
        if (StringUtils.isNotBlank(naiya))
        {
            specification = specification + naiya + " ";
        }
        if (StringUtils.isNotBlank(film))
        {
            specification = specification + film;
        }
        if (StringUtils.isNotBlank(pcbType))
        {
            specification = specification + pcbType;
        }

        if (StringUtils.isNotBlank(simple))
        {
            specification = specification + simple;
        }
        if (StringUtils.isNotBlank(gum))
        {
            specification = specification + gum;
        }
        if (StringUtils.isNotBlank(insulat))
        {
            specification = specification + insulat;
        }
        if (StringUtils.isNotBlank(cuprum))
        {
            specification = specification + cuprum;
        }
        if (StringUtils.isNotBlank(special))
        {
            specification = specification + special;
        }
        if (StringUtils.isNotBlank(specification))
        {
            interProduct.setSpecifications(specification);
        }
    }

    @Transactional(readOnly = false)
    public String confirmOrder(List<InterProduct> list)
    {
        for(InterProduct interProduct : list)
        {
            productDao.confirmOrder(interProduct);
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String deleteOrder(InterProduct interProduct)
    {
        productDao.deleteOrder(interProduct);

        // 删除附件
        ConfigCustomerAttachements query = new ConfigCustomerAttachements();
        query.setRecordId(interProduct.getRecordId());
        List<ConfigCustomerAttachements> attachmentList = configCustomerAttachementsDao.getAttachmentList(query);
        if(Collections3.isNotEmpty(attachmentList))
        {
            for(ConfigCustomerAttachements attach : attachmentList)
            {
                FileManageUtils.delFiles(attach.getFileUrl());
                configCustomerAttachementsDao.deleteAttachment(attach);
            }
        }
        return "success";
    }

    public Page<InterProduct> getInterProductPage(Page<InterProduct> page,InterProduct interProduct)
    {
        // 根据明细填的内容获取订单id
        String orderIds = null;
        if(StringUtils.isNotBlank(interProduct.getConcatValue()))
        {
            List<String> orderIdList = productDao.getOrderIdList(interProduct);
            if(Collections3.isNotEmpty(orderIdList))
            {
                for(String orderId : orderIdList)
                {
                    if(StringUtils.isBlank(orderId))
                    {
                        continue;
                    }
                    if(StringUtils.isNotBlank(orderIds))
                    {
                        orderIds = orderIds +","+ orderId;
                    }
                    else
                    {
                        orderIds = orderId;
                    }
                }
            }
        }
        interProduct.setRecordId(orderIds);
        interProduct.setPage(page);

        // 客户下单->加载所有订单->加载对应所有的订单明细->分配明细数据->返回
        List<InterProduct> result = productDao.getCustomerProductPage(interProduct);

        // 获取id集合
        String ids = "";
        for(InterProduct product : result)
        {
            ids = StringUtils.isBlank(ids) ? product.getRecordId() : (ids + "," + product.getRecordId());
        }

        // 明细
        List<InterProductDeail> deailList = productDao.getCustomerProductDeailList(ids);

        // 附件
        ConfigCustomerAttachements query = new ConfigCustomerAttachements();
        query.setRecordId(ids);
        List<ConfigCustomerAttachements> attachmentList = configCustomerAttachementsDao.getAttachmentList(query);

        for(InterProduct product : result)
        {
            String id = product.getRecordId();
            if (deailList != null && deailList.size() > 0)
            {
                // 产品列表，只显示配置了列表显示内容
                product.setDeailList(deailList.stream().filter(d-> (StringUtils.isNotBlank(d.getMainId()) && id.equals(d.getMainId()))).collect(
                    Collectors.toList()));
            }
            if(Collections3.isNotEmpty(attachmentList))
            {
                product.setAttaList(attachmentList.stream().filter(d-> (StringUtils.isNotBlank(d.getConfigCustomerId()) && id.equals(d.getConfigCustomerId()))).collect(
                    Collectors.toList()));
            }
        }
        page.setList(result);
        return page;
    }

    public String getCustomerProductNo()
    {
        // 获取当天下了多少订单
        Integer orderCount = productDao.getCustomerProductCount();
        orderCount = orderCount + 1;
        String orderCountStr = orderCount.toString();
        switch (orderCountStr.length())
        {
            case 1:
                orderCountStr = "000" + orderCountStr;
                break;
            case 2:
                orderCountStr = "00" + orderCountStr;
                break;
            case 3:
                orderCountStr = "0" + orderCountStr;
                break;
        }
        String no = DateUtils.formatDate(new Date(),"yyyyMMdd") + orderCountStr;
        return no;
    }

    public InterProduct selectOnlineWindow(InterProduct interProduct)
    {
        return productDao.selectOnlineWindow(interProduct);
    }
    @Transactional(readOnly = false)
    public String adjustStocks(InterProduct interProduct)
    {
        if (interProduct.getList() != null && interProduct.getList().size() > 0)
        {
            for (InterProductStockRecord record : interProduct.getList())
            {
                record.setInoutPlace("产品维护");
                // 期初
                BigDecimal initStocks = record.getInitStocks() == null ? BigDecimal.ZERO : record.getInitStocks();
                // 结余
                BigDecimal stocks = record.getStocks() == null ? BigDecimal.ZERO : record.getStocks();
                BigDecimal changeStocks = BigDecimal.ZERO;
                if (stocks.compareTo(initStocks) >= 0) {
                    record.setInoutType("1");
                    changeStocks = stocks.subtract(initStocks);
                } else {
                    record.setInoutType("2");
                    changeStocks = initStocks.subtract(stocks);
                }
                record.setChangeStocks(changeStocks);
            }
            // 批量插入出入库记录
            productDao.batchInsertStockRecordList(interProduct);
        }
        return "success";
    }

    public List<Material> getCustomerProductList(InterProduct interProduct)
    {
        if(null == interProduct || StringUtils.isBlank(interProduct.getSupplierId()) || StringUtils.isBlank(interProduct.getErpId())) {
            return null;
        }
        // 获取ERP供应商
        Supplier supplier = supplierDao.getErpSupplier(interProduct);
        // 获取材料
        Material material = new Material();
        if (supplier != null)
        {
            material.setManufacturerId(StringUtils.isNotBlank(supplier.getSupplyBrandId()) ? supplier.getSupplyBrandId() : supplier.getManufacturerId());
        }
        material = addMaterialItems(material, interProduct.getSerchMsg());
        material.setPageNo(StringUtils.isNotBlank(interProduct.getPageNo()) ? interProduct.getPageNo() : "0");
        material.setPageSize(StringUtils.isNotBlank(interProduct.getPageSize()) ? interProduct.getPageSize() : "50");
        material.setCompanyId(interProduct.getErpId());
        List<Material> materialList = materialDao.getApplyMatListThree(material);
        // 获取库存的调整记录
        List<InterProductStockRecord> recordList = productDao.getStockRecordList(interProduct.getSupplierId(), interProduct.getErpId(),null);
        // 给物料赋予报价数据，以最新的赋予
        if(Collections3.isNotEmpty(materialList))
        {
            for(Material mat :materialList)
            {
                mat.setStocks(BigDecimal.ZERO);
                mat.setPrice(BigDecimal.ZERO);
                mat.setLeadTime(0);
                mat.setMaterialKind(1);
                mat.setErpId(interProduct.getErpId());
                for(InterProductStockRecord record : recordList)
                {
                    if(StringUtils.isNotBlank(record.getProductId()) && record.getProductId().equals(mat.getRecordId()))
                    {
                        mat.setStocks(record.getStocks());
                        mat.setPrice(record.getPrice());
                        mat.setLeadTime(record.getLeadTime());
                        break;
                    }
                }
            }
        }
        return materialList;
    }

    public Material addMaterialItems(Material material, String serchMsg)
    {
        material.setCondition1(null);
        material.setCondition2(null);
        material.setCondition3(null);
        material.setCondition4(null);
        material.setCondition5(null);
        material.setCondition6(null);
        material.setCondition7(null);
        material.setCondition8(null);
        if (StringUtils.isNotBlank(serchMsg))
        {
            String[] nos = serchMsg.split(" ");
            if (null != nos && nos.length > 0)
            {
                for (int i = 0; i < nos.length; i++)
                {
                    String condition = nos[i];
                    switch (i)
                    {
                        case 0:
                            material.setCondition1(condition);
                            break;
                        case 1:
                            material.setCondition2(condition);
                            break;
                        case 2:
                            material.setCondition3(condition);
                            break;
                        case 3:
                            material.setCondition4(condition);
                            break;
                        case 4:
                            material.setCondition5(condition);
                            break;
                        case 5:
                            material.setCondition6(condition);
                            break;
                        case 6:
                            material.setCondition7(condition);
                            break;
                        case 7:
                            material.setCondition8(condition);
                            break;
                    }
                }
            }
        }
        return material;
    }

    public Material getCustomerProduct(InterProduct interProduct)
    {
        if(null == interProduct)
        {
            return null;
        }
        Material query = new Material();
        query.setRecordId(interProduct.getRecordId());
        Material material = materialDao.get(query);
        // 获取库存的调整记录
        List<InterProductStockRecord> recordList = productDao.getStockRecordList(interProduct.getCompanyId(), interProduct.getErpId(), interProduct.getRecordId());
        if(material != null)
        {
            material.setStocks(BigDecimal.ZERO);
            material.setPrice(BigDecimal.ZERO);
            material.setLeadTime(0);
            for(InterProductStockRecord record : recordList)
            {
                if(StringUtils.isNotBlank(record.getProductId()) && record.getProductId().equals(material.getRecordId()))
                {
                    material.setStocks(record.getStocks());
                    material.setPrice(record.getPrice());
                    material.setLeadTime(record.getLeadTime());
                    break;
                }
            }
        }
        material.setStockRecordList(recordList);
        return material;
    }

    @Transactional(readOnly = false)
    public String saveDemand(IcloudDemand icloudDemand)
    {
         productDao.saveDemand(icloudDemand);
         return "success";
    }

    public List<IcloudNotice> getCurrNotice(IcloudNotice icloudNotice)
    {
        return productDao.getCurrNotice(icloudNotice);
    }

    @Transactional(readOnly = false)
    public String saveFeedback(IcloudFeedback icloudFeedback)
    {
         productDao.saveFeedback(icloudFeedback);
         return "success";
    }

    public List<IcloudFeedback> getHistoryBackList(IcloudFeedback icloudFeedback)
    {
        return productDao.getHistoryBackList(icloudFeedback);
    }

    public List<IcloudDemand> getHistoryDemandList(IcloudDemand icloudDemand)
    {
        return productDao.getHistoryDemandList(icloudDemand);
    }

    @Transactional(readOnly = false)
    public String finishBack(IcloudFeedback icloudFeedback)
    {
        icloudFeedback.setStatus("1002"); //已结案
        productDao.finishBack(icloudFeedback);
        return "success";
    }

}
