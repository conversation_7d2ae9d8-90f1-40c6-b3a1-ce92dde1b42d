package com.kyb.pcberp.modules.production.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.contract.entity.NotificationScheduling;

import java.util.Date;

/**
 * @ClassName Ask
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/12/1 10:11
 * @Version 1.0
 **/
public class Ask extends DataEntity<Ask>
{
    
    private static final long serialVersionUID = 1L;
    
    // 公司ID
    private String companyId;
    
    // 被引用通知单编号
    private String notificationId;

    private NotificationScheduling notification;
    
    // 问客内容
    private String ask;
    
    // 问客开始时间
    private Date askStartDate;
    
    // 问客结束时间
    private Date askEndDate;
    
    // 反馈内容
    private String feedback;
    
    // 反馈开始时间
    private Date feedbackStartDate;
    
    // 反馈结束时间
    private Date feedbackEndDate;

    private String engineerCreatedDate;
    private String estimateDate;
    private String notiNo;
    private String craftNo;
    private String customerModel;
    private String orderDeailArea;
    private Date arrangePlanDate;

    public Date getArrangePlanDate() {
        return arrangePlanDate;
    }

    public void setArrangePlanDate(Date arrangePlanDate) {
        this.arrangePlanDate = arrangePlanDate;
    }

    public String getEngineerCreatedDate() {
        return engineerCreatedDate;
    }

    public void setEngineerCreatedDate(String engineerCreatedDate) {
        this.engineerCreatedDate = engineerCreatedDate;
    }

    public String getEstimateDate() {
        return estimateDate;
    }

    public void setEstimateDate(String estimateDate) {
        this.estimateDate = estimateDate;
    }

    public String getNotiNo() {
        return notiNo;
    }

    public void setNotiNo(String notiNo) {
        this.notiNo = notiNo;
    }

    public String getCraftNo() {
        return craftNo;
    }

    public void setCraftNo(String craftNo) {
        this.craftNo = craftNo;
    }

    public String getCustomerModel() {
        return customerModel;
    }

    public void setCustomerModel(String customerModel) {
        this.customerModel = customerModel;
    }

    public String getOrderDeailArea() {
        return orderDeailArea;
    }

    public void setOrderDeailArea(String orderDeailArea) {
        this.orderDeailArea = orderDeailArea;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getNotificationId() {
        return notificationId;
    }

    public void setNotificationId(String notificationId) {
        this.notificationId = notificationId;
    }

    public String getAsk() {
        return ask;
    }

    public void setAsk(String ask) {
        this.ask = ask;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getAskStartDate() {
        return askStartDate;
    }

    public void setAskStartDate(Date askStartDate) {
        this.askStartDate = askStartDate;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getAskEndDate() {
        return askEndDate;
    }

    public void setAskEndDate(Date askEndDate) {

        this.askEndDate = askEndDate;
    }

    public String getFeedback() {
        return feedback;
    }

    public void setFeedback(String feedback) {
        this.feedback = feedback;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getFeedbackStartDate() {
        return feedbackStartDate;
    }

    public void setFeedbackStartDate(Date feedbackStartDate) {
        this.feedbackStartDate = feedbackStartDate;
    }
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getFeedbackEndDate() {
        return feedbackEndDate;
    }

    public void setFeedbackEndDate(Date feedbackEndDate) {
        this.feedbackEndDate = feedbackEndDate;
    }

    public NotificationScheduling getNotification() {
        return notification;
    }

    public void setNotification(NotificationScheduling notification) {
        this.notification = notification;
    }
}
