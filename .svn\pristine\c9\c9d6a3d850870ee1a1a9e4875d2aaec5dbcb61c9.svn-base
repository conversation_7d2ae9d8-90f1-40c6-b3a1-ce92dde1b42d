package com.kyb.pcberp.modules.wechat.service;

import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_ActivateAccount;
import com.kyb.pcberp.modules.sys.entity.Employee;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.security.Digests;
import com.kyb.pcberp.common.service.BaseService;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.common.utils.alimessage.MessageUtil;
import com.kyb.pcberp.modules.report.entity.Report;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.wechat.dao.AccountBindDao;
import com.kyb.pcberp.modules.wechat.dao.IcloudCooperationDao;
import com.kyb.pcberp.modules.wechat.entity.*;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.UserCompany;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatUser;
import com.kyb.pcberp.modules.wechat.utils.HttpRquestUtil;

/**
 * ycy 2016-11-2 账号绑定
 *
 * <AUTHOR>
 */
@Service
@Transactional(readOnly = true)
public class AccountBindService extends BaseService
{
    @Autowired
    private AccountBindDao accountBindDao;

    @Autowired
    private IcloudCooperationDao icloudCooperationDao;

    public static final int HASH_INTERATIONS = 1024;

    public List<WechatUser> getAllAccountByOpenId(String openId)
    {
        return accountBindDao.getAllAccountByOpenId(openId);
    }

    public List<Company> getErpList(IcloudUser user)
    {
        return accountBindDao.getErpList(user);
    }

    @Transactional(readOnly = false)
    public void logOut(HttpSession session)
    {
        if (session.getAttribute("kybOpenId") != null)
        {
            String openId = session.getAttribute("kybOpenId").toString();
            accountBindDao.logOut(openId);
        }
    }

    @Transactional(readOnly = false)
    public void logOut(String openId)
    {
        accountBindDao.logOut(openId);
    }

    public String login(IcloudUser user)
    {
        if (user != null && StringUtils.isNotBlank(user.getPhone()) && StringUtils.isNotBlank(user.getPassword())) {
            IcloudUser icloudUser = accountBindDao.getIcloudUser(user.getPhone());
            if (icloudUser == null || StringUtils.isBlank(icloudUser.getPassword())){
                return "该账户未曾设置过密码，请通过手机验证登录";
            }
            boolean psdFlag = validatePassword(user.getPassword(), icloudUser.getPassword());
            if (psdFlag)
            {
                return "success";
            }
        }
        return "密码错误";
    }

    public Integer getBindNum(IcloudUser user)
    {
        return accountBindDao.getBindNum(user);
    }

    @Transactional(readOnly = false)
    public void bindUser(IcloudUser user)
    {
        // 绑定微信
        accountBindDao.bindUser(user);
    }

    public boolean validatePassword(String plainPassword, String password)
    {
        byte[] salt = Encodes.decodeHex(password.substring(0, 16));
        byte[] hashPassword = Digests.sha1(plainPassword.getBytes(), salt, HASH_INTERATIONS);
        return password.equals(Encodes.encodeHex(salt) + Encodes.encodeHex(hashPassword));
    }

    public String validatePhone(String phone)
    {
        IcloudUser user = new IcloudUser();
        user.setPhone(phone);
        Integer phoneNum = accountBindDao.icloudLoginPhone(user);
        if (phoneNum > 0)
        {
            return "手机号码已经注册，请不要重复注册";
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String register(IcloudUser user, String smsCode)
    {
        if (!StringUtils.equalsIgnoreCase(user.getPhoneCode(), smsCode))
        {
            return "短信验证码错误";
        }
        else
        {
            if (StringUtils.isNotBlank(user.getUserName()) && StringUtils.isNotBlank(user.getPhone()))
            {
                Integer phoneNum = accountBindDao.icloudLoginPhone(user);
                if (phoneNum > 0)
                {
                    return "success";
                }
                // 开始注册
                if (StringUtils.isBlank(user.getPassword()))
                {
                    Random random = new Random();
                    int randomNum = random.nextInt(900000) + 100000;
                    user.setPassword(String.valueOf(randomNum));
                }
                String pwd = Encodes.encryptPassword(user.getPassword());
                user.setPassword(pwd);
                accountBindDao.icloudRegister(user);
                //查询员工是否存在员工信息
                Employee employeeMessage = accountBindDao.getOaEmployeeMessage(user.getPhone());
                if (null != employeeMessage)
                {
                    WechatUser wechatUser = new WechatUser();
                    wechatUser.setRecordId(user.getRecordId());
                    wechatUser.setPhone(user.getPhone());
                    accountBindDao.updateEmpUserId(wechatUser);
                }
                return "success";
            }
            else
            {
                return "请确认手机号码是否填写正确";
            }
        }
    }

    @Transactional(readOnly = false)
    public String invite(IcloudUser user)
    {
        // 根据邀请码查询租户id
        String tenId = accountBindDao.getTenId(user.getInviteCode());
        if (StringUtils.isBlank(tenId))
        {
            return "邀请码失效，请联系本公司系统负责人重试";
        }
        user.setTenId(tenId);
        IcloudUser valiteUser = accountBindDao.getTenUser(user);
        if (valiteUser != null && StringUtils.isNotBlank(valiteUser.getRecordId()))
        {
            if (valiteUser.getActiveFlag().equals("1"))
            {
                return "你已经激活，请不要重复激活";
            }
            else
            {
                return "你已经被禁止登录该租户的系统";
            }
        }
        accountBindDao.deleteInviteByPhone(user);
        accountBindDao.bindInvite(user);
        return "恭喜你激活成功，可以进行登录erp系统了";
    }

    @Transactional(readOnly = false)
    public String editPwd(WechatUser user, String sessionCode)
    {
        if (!StringUtils.equalsIgnoreCase(user.getPhoneCode(), sessionCode))
        {
            return "短信验证码错误";
        }
        else
        {
            if (StringUtils.isNotBlank(user.getPhone()) && StringUtils.isNotBlank(user.getPassword()))
            {
                // 开始修改
                String pwd = Encodes.encryptPassword(user.getPassword());
/*                user.setName(user.getPhone());
                WechatUser userOnline = accountBindDao.getIcloudUser(user);
                boolean psdFlag =this.validatePassword(user.getOldpassword(), userOnline.getPassword());
                if (!psdFlag)
                {
                    return "旧密码输入有误";
                }*/
                user.setPassword(pwd);
                accountBindDao.updateIcloudUser(user);
                return "success";
            }
            else
            {
                return "请确认手机号码/密码是否填写正确";
            }
        }
    }

    public WechatUser setUserDeail(String openId)
    {
        return accountBindDao.setUserDeail(openId);
    }

    @Transactional(readOnly = false)
    public String updateUserDeail(WechatUser user)
    {
        if (user != null && StringUtils.isNotBlank(user.getRecordId()))
        {
            accountBindDao.updateUserDeail(user);
            return "success";
        }
        return "fail";
    }

    @Transactional(readOnly = false)
    public String updateUserAuth(IcloudUser user)
    {
        Integer count = accountBindDao.checkRealName(user);
        if(null != count && count > 0)
        {
            return "实名验证真实姓名重复了，请确定是否输入正确!";
        }
        if (user != null && StringUtils.isNotBlank(user.getRecordId()))
        {
            accountBindDao.updateUserAuth(user);
            return "success";
        }
        return "fail";
    }

    @Transactional(readOnly = false)
    public String updateUserMsg(IcloudUser user)
    {
        if (user != null && StringUtils.isNotBlank(user.getRecordId()))
        {
            int count = accountBindDao.icloudLoginCode(user.getCode(),user.getRecordId());
            if (count > 0)
            {
                return "个人识别码已存在";
            }
            // 保存图片到阿里云服务器
            if (Collections3.isNotEmpty(user.getServerList()))
            {
                String url = HttpRquestUtil.getWechatUrl();
                String savePath =
                    "icloudUser" + "/" + user.getRecordId() + "/" + DateUtils.formatDateTime(new Date()) + "/";
                String server = user.getServerList().get(0);
                String webUrl = url + server;
                String name = user.getRecordId() + DateUtils.formatDateTime(new Date()) + ".jpg";
                String sourcePath = savePath + name;
                FileManageUtils.wxUploadImg(webUrl, sourcePath);
                // 存储路径到数据库
                user.setFileUrl(sourcePath);
            }
            accountBindDao.updateUserMsg(user);
            return "success";
        }
        return "fail";
    }

    @SuppressWarnings("unchecked")
    public Map<String, String> erpRegister(String openId, Company company)
    {
        String pcberpadmin = Global.getPcberpadmin();
        Map<String, String> data = new HashMap<>();
        String url = pcberpadmin + "/adminicoud/saveWxCompany";
        Map<String, Object> map = new HashMap<>();
        map.put("name", company.getName());
        map.put("phone", company.getPhone());
        map.put("legalPerson", company.getLegalPerson());
        data = HttpApi.icloud(map, url);
        if (null == data)
        {
            data = new HashMap<>();
            data.put("msg", "申请公司失败，请稍后再试！101");
            return data;
        }
        String name = data.get("name") == null ? "" : data.get("name");
        String password = data.get("password") == null ? "" : data.get("password");
        if ((StringUtils.isBlank(name) || StringUtils.isBlank(password)) && StringUtils.isNotBlank(data.get("msg")))
        {
            return data;
        }
        if (StringUtils.isBlank(name) || StringUtils.isBlank(password))
        {
            data.put("msg", "申请公司失败，请稍后再试！102");
            return data;
        }
        // 给用户发送登录名和密码到微信
        MessageUtil.sendCompanyMsg(data, openId);
        return data;
    }

    public List<Person> getPersonList(Report report)
    {
        if (StringUtils.isNotBlank(report.getRole()))
        {
            if (report.getRole().equals("1"))
            {
                // 客户
                return accountBindDao.getCustomerList(report);
            }
            else if (report.getRole().equals("2") || report.getRole().equals("3"))
            {
                // 业务员 or 创建者
                return accountBindDao.getUserList(report);
            }
            else if (report.getRole().equals("4"))
            {
                // 供应商
                return accountBindDao.getSupplierList(report);
            }
        }
        return null;
    }

    public AccountBind getBindDbName(String openId)
    {
        return accountBindDao.getBindDbName(openId);
    }

    public String getPhone(String openId)
    {
        return accountBindDao.getPhone(openId);
    }

    @Transactional(readOnly = false)
    public String bindErp(String openId, UserCompany uc)
    {
        Company company = uc.getCompany();
        // ERP是否添加了该公司
        Company erpCompany = accountBindDao.findCompanyByName(company);
        // 云平台是否添加了公司
        Company icloudCompany = accountBindDao.findCompanyCount(company);
        if (erpCompany != null && StringUtils.isNotBlank(erpCompany.getRecordId()))
        {
            // 判断存在的公司用户是否存在，存在返回3，不存在返回2
            User erpUser = accountBindDao.findUserInfo(uc.getPhone(), erpCompany.getRecordId());
            if (erpUser != null && StringUtils.isNotBlank(erpUser.getRecordId()))
            {
                // 查询公司和用户是否已经绑定
                uc.setErpCompanyId(erpCompany.getRecordId());
                uc.setErpUserCode(erpUser.getUserCode());
                uc.setErpUserId(erpUser.getRecordId());
                company = erpCompany;
                company.setErpCompanyId(company.getRecordId());
                if (icloudCompany != null && StringUtils.isNotBlank(icloudCompany.getRecordId()))
                {
                    uc.getCompany().setRecordId(icloudCompany.getRecordId());
                }
                Integer num = accountBindDao.getUserCompanyNum(uc);
                if (num > 0)
                {
                    return "alReady";
                }
            }
            else
            {
                return "noUser";
            }
        }
        else
        {
            if (icloudCompany != null && StringUtils.isNotBlank(icloudCompany.getRecordId()))
            {
                return "exist";
            }
        }
        if (icloudCompany == null || StringUtils.isBlank(icloudCompany.getRecordId()))
        {
            accountBindDao.insertIcloudCompany(company);
            uc.getCompany().setRecordId(company.getRecordId());
        }
        accountBindDao.insertUserCompany(uc);
        return "success";
    }

    public IcloudUser getUser(String openId)
    {
        return accountBindDao.getUser(openId);
    }

    public List<IcloudCompany> getCompanyList(String recordId)
    {
        return accountBindDao.getCompanyList(recordId);
    }

    public IcloudCompany getCompany(String recordId)
    {
        return accountBindDao.getCompany(recordId);
    }

    @Transactional(readOnly = false)
    public String saveCompany(IcloudCompany icloudCompany)
    {
        // 验证绑定公司
        if (StringUtils.isNotBlank(icloudCompany.getErpCompanyId()))
        {
            Integer count = accountBindDao.valiteErpCompany(icloudCompany.getErpCompanyId(), icloudCompany.getRecordId());
            if (count > 0)
            {
                return "exitErp";
            }
        }
        // 验证公司信息不重复
        List<IcloudCompany> companyList = accountBindDao.valiteCompany(icloudCompany);
        if (companyList != null && companyList.size() > 0)
        {
            if (StringUtils.isBlank(icloudCompany.getRecordId())){
                // 已存在该公司，自动向其申请加入
                IcloudUser user = new IcloudUser();
                user.setRecordId(icloudCompany.getUserId());
                user.setCompanyId(companyList.get(0).getRecordId());
                addCompanyUserTwo(user);
            }
            return "exit";
        }
        if (StringUtils.isNotBlank(icloudCompany.getRecordId()))
        {
            accountBindDao.updateCompany(icloudCompany);
        }
        else
        {
            accountBindDao.insertCompany(icloudCompany);
            IcloudCompanyUser icloudCompanyUser = new IcloudCompanyUser();
            icloudCompanyUser.setCompanyId(icloudCompany.getRecordId());
            icloudCompanyUser.setUserId(icloudCompany.getUserId());
            icloudCompanyUser.setStatus("1");
            icloudCompanyUser.setPosition("创建者");
            accountBindDao.addCompanyUser(icloudCompanyUser);
            // 和默认公司自动合作
            List<IcloudCompany> list = accountBindDao.getDefaultCompany();
            for (IcloudCompany company : list){
                IcloudCooperation cooperation = new IcloudCooperation();
                cooperation.setApplyFlag("4");
                cooperation.setStatus("2");
                IcloudCompany customer = company.getDefaultValue() == 2 ? company : icloudCompany;
                IcloudCompany supplier = company.getDefaultValue() == 2 ? icloudCompany : company;
                cooperation.setSupplierId(supplier.getRecordId());
                cooperation.setCustomerId(customer.getRecordId());
                cooperation.setMessage(icloudCompany.getName() + "向" + company.getName() + "申请合作自动生成");
                icloudCooperationDao.apply(cooperation);
            }
        }
        // 保存图片到阿里云服务器
//        String url = HttpRquestUtil.getWechatUrl();
//        if (Collections3.isNotEmpty(icloudCompany.getServerList()))
//        {
//            String savePath =
//                "icloudCompanyLogo" + "/" + icloudCompany.getRecordId() + "/" + DateUtils.formatDateTime(new Date())
//                    + "/";
//            String server = icloudCompany.getServerList().get(0);
//            String webUrl = url + server;
//            String name = icloudCompany.getRecordId() + DateUtils.formatDateTime(new Date()) + ".jpg";
//            String sourcePath = savePath + name;
//            FileManageUtils.wxUploadImg(webUrl, sourcePath);
//            // 存储路径到数据库
//            icloudCompany.setLogo(sourcePath);
//            accountBindDao.updateCompanyLogo(icloudCompany);
//        }
//        if (Collections3.isNotEmpty(icloudCompany.getServerTwoList()))
//        {
//            String savePath =
//                "icloudCompanyFile" + "/" + icloudCompany.getRecordId() + "/" + DateUtils.formatDateTime(new Date())
//                    + "/";
//            String server = icloudCompany.getServerTwoList().get(0);
//            String webUrl = url + server;
//            String name = icloudCompany.getRecordId() + DateUtils.formatDateTime(new Date()) + ".jpg";
//            String sourcePath = savePath + name;
//            FileManageUtils.wxUploadImg(webUrl, sourcePath);
//            // 存储路径到数据库
//            icloudCompany.setImgUrl(sourcePath);
//            accountBindDao.updateCompanyImgUrl(icloudCompany);
//        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String updateUserId(String userId, String phone)
    {
        accountBindDao.updateUserId(userId, phone);
        return "success";
    }

    @Transactional(readOnly = false)
    public String deleteCompany(String recordId)
    {
        accountBindDao.deleteCompany(recordId);
        return "success";
    }

    @Transactional(readOnly = false)
    public String useCom(String recordId)
    {
        accountBindDao.useCom(recordId);
        return "success";
    }

    public List<IcloudUser> getCompanyUserList(IcloudCompanyUser icloudCompanyUser)
    {
        return accountBindDao.getCompanyUserList(icloudCompanyUser);
    }

    @Transactional(readOnly = false)
    public String deleteCompanyUser(IcloudCompanyUser icloudCompanyUser)
    {
        accountBindDao.deleteCompanyUser(icloudCompanyUser);
        return "success";
    }

    @Transactional(readOnly = false)
    public String addCompanyUser(IcloudCompanyUser icloudCompanyUser)
    {
        if (StringUtils.isBlank(icloudCompanyUser.getUserId()))
        {
            IcloudUser user = accountBindDao.getIcloudUser(icloudCompanyUser.getPhone());
            if (user != null && StringUtils.isNotBlank(user.getRecordId()))
            {
                icloudCompanyUser.setUserId(user.getRecordId());
            }
        }
        if (StringUtils.isBlank(icloudCompanyUser.getRecordId()))
        {
            // 验证添加的人不重复
            Integer count = accountBindDao.valiteCompanyUser(icloudCompanyUser);
            if (count > 0)
            {
                return "exit";
            }
            accountBindDao.addCompanyUser(icloudCompanyUser);
        }
        else
        {
            accountBindDao.updateCompanyUser(icloudCompanyUser);
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String addCompanyUserByCode(String companyCode, String openId)
    {
        // 根据企业识别码获取企业
        String comId = accountBindDao.getComIdByCode(companyCode);
        if (StringUtils.isBlank(comId))
        {
            return "exitCom";
        }
        IcloudUser user = accountBindDao.getUser(openId);
        IcloudCompanyUser icloudCompanyUser = new IcloudCompanyUser();
        icloudCompanyUser.setPhone(user.getPhone());
        icloudCompanyUser.setCompanyId(comId);
        // 验证添加的人不重复
        Integer count = accountBindDao.valiteCompanyUser(icloudCompanyUser);
        if (count > 0)
        {
            return "exit";
        }
        icloudCompanyUser.setFlag("1");
        accountBindDao.addCompanyUser(icloudCompanyUser);
        return "success";
    }

    @Transactional(readOnly = false)
    public String addCompanyUserTwo(IcloudUser user)
    {
        IcloudCompanyUser icloudCompanyUser = new IcloudCompanyUser();
        icloudCompanyUser.setUserId(user.getRecordId());
        icloudCompanyUser.setCompanyId(user.getCompanyId());
        // 验证添加的人不重复
        Integer count = accountBindDao.valiteCompanyUser(icloudCompanyUser);
        if (count > 0)
        {
            return "exit";
        }
        icloudCompanyUser.setFlag("1");
        accountBindDao.addCompanyUser(icloudCompanyUser);
        return "success";
    }

    @Transactional(readOnly = false)
    public String setManage(IcloudCompanyUser icloudCompanyUser)
    {
        if (StringUtils.isBlank(icloudCompanyUser.getStatus()))
        {
            icloudCompanyUser.setStatus(null);
        }
        accountBindDao.setManage(icloudCompanyUser);
        return "success";
    }

    @Transactional(readOnly = false)
    public String updateCompanyUserFlag(IcloudCompanyUser icloudCompanyUser)
    {
        accountBindDao.updateCompanyUserFlag(icloudCompanyUser);
        return "success";
    }

    public List<IcloudCompany> getCompanyByCode(String code)
    {
        List<IcloudCompany> list = accountBindDao.getCompanyByCode(code);
        return list;
    }

    public List<IcloudCompany> loadMyCompanyList(IcloudCompany icloudCompany)
    {
        return accountBindDao.loadMyCompanyList(icloudCompany);
    }

    public Map<String, Integer> getDataList(String openId)
    {
        Map<String, Integer> map = new HashMap<>();
        IcloudUser user = accountBindDao.getUser(openId);
        if (user == null || StringUtils.isBlank(user.getPhone()))
        {
            return null;
        }

        map.put("myWaitNum", icloudCooperationDao.getCooperationWaitNum(user.getPhone(), "1"));
        map.put("icloudWaitNum", icloudCooperationDao.getCooperationWaitNum(user.getPhone(), "2"));
        // map.put("coopList",);
        // map.put("coopChanceList",);
        return map;
    }

    public List<IcloudUser> getPhoneBookList(IcloudUser icloudUser, String openId)
    {
        IcloudUser user = accountBindDao.getUser(openId);
        return accountBindDao.getPhoneBookList(user.getPhone(), icloudUser.getSerchMsg(),icloudUser.getPageNo(),icloudUser.getPageSize());
    }

    public String getQcCodeByCode(String code, String realPath)
    {
        int width3 = 200, height3 = 200;
        String name = "qcCode";
        ZxingHandler.encode2(code, width3, height3, realPath + "assets/global/img/card/" + name + ".png");
        return "assets/global/img/card/" + name + ".png";
    }

    public List<IcloudUser> getAllCompanyUserList(String comIds)
    {
        return accountBindDao.getAllCompanyUserList(comIds);
    }

    public List<IcloudCompany> loadMySupplierList(String openId)
    {
        IcloudUser user = accountBindDao.getUser(openId);
        return accountBindDao.loadMySupplierList(user.getPhone());
    }

    @Transactional(readOnly = false)
    public String hrInvite(WechatUser user)
    {
        /*String tenId = accountBindDao.getTenId(user.getInviteCode());
        if (StringUtils.isBlank(tenId))
        {
            return "邀请码失效，请联系本公司系统负责人重试";
        }
        user.setTenId(tenId);
        *//*WechatUser valiteUser = accountBindDao.getTenUser(user);*/
        if (StringUtils.isNotBlank(user.getRecordId()))
        {
            user.preUpdate();
            accountBindDao.updateUser(user);
        }
        else
        {
            /*accountBindDao.deleteInviteByPhone(user);*/
            accountBindDao.getInsertInvite(user);
        }
        return "账号激活成功!";
    }

    public Page<WechatUser> getHrUserList(WechatUser wechatUser, HttpServletRequest request, HttpServletResponse response,Integer type)
    {
        Page<WechatUser> page = new Page<WechatUser>(request, response,type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if(StringUtils.isNotBlank(wechatUser.getPageNo()))
        {
            pageNo = Integer.valueOf(wechatUser.getPageNo());
        }
        if(StringUtils.isNotBlank(wechatUser.getPageSize()))
        {
            pageSize = Integer.valueOf(wechatUser.getPageSize());
        }
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        wechatUser.setPage(page);
        List<WechatUser> wechatList = accountBindDao.getHrUserList(wechatUser);
        page.setList(wechatList);
        return page;
    }

    @Transactional(readOnly = false)
    public String delUserAccount(WechatUser wechatUser)
    {
        accountBindDao.getDelUserAccount(wechatUser);
        return "success";
    }

    public void setDataList(ExportExcel excel, List<Hr_ActivateAccount> list, String[] hearList) {
        for (Hr_ActivateAccount user : list) {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "用户":
                        val = user.getUserName();
                        break;
                    case "状态":
                        String workStatus = "离职";
                        if(StringUtils.isNotBlank(user.getWorkStatus()))
                        {
                            if(StringUtils.isNotBlank(user.getWorkStatus()))
                            {
                                workStatus = "在职";
                            }
                        }
                        val = workStatus;
                        break;
                    case "服务":
                        String flag = "无效";
                        if(StringUtils.isNotBlank(user.getEnableId()) && "1".equals(user.getEnableId()))
                        {
                            flag = "有效";
                        }
                        val = flag;
                        break;
                    case "手机号码":
                        val = user.getPhone();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    @Transactional(readOnly = false)
    public void updateEmpUserId(WechatUser user)
    {
        if(null != user && StringUtils.isNotBlank(user.getRecordId()) && StringUtils.isNotBlank(user.getPhone()))
        {
            accountBindDao.updateEmpUserId(user);
        }
    }

    public List<IcloudCompany> loadCustomerList(IcloudCompany icloudCompany)
    {
        return accountBindDao.loadCustomerList(icloudCompany);
    }

    public List<IcloudCompany> getCompanyListT(IcloudUser user)
    {
        return accountBindDao.getCompanyListT(user);
    }
}
