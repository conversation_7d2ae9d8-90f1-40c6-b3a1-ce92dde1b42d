const salesSystemMenu = {
    template:'#salesSystemMenu',
    data(){
        return{
            mainClass: 'nav-link',
            personClass: 'nav-link'
        }
    },
    created () {
        this.changeClass(1);
    },
    computed: {
        emp: {
            get () {
                return this.$store.state.myStore.emp;
            }
        }
    },
    methods:{
        changeClass:function(item){
            this.initClass();
            if(item === 1){
                this.mainClass = 'nav-link active';
            }else if(item === 2){
                this.personClass = 'nav-link active';
            }
        },
        initClass:function(){
            this.mainClass = 'nav-link';
            this.personClass = 'nav-link';
        },
    }
}