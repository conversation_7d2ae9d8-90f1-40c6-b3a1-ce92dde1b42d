/**
 *
 */
package com.kyb.pcberp.modules.purch.web;

import com.aliyun.oss.OSSClient;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.mapper.JsonMapper;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.oss.util.OssUtils;
import com.kyb.pcberp.common.pdf.PdfResult;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.approval.utils.ModifyUtils;
import com.kyb.pcberp.modules.contract.entity.MatPreparationVersion;
import com.kyb.pcberp.modules.contract.service.MatPreparationService;
import com.kyb.pcberp.modules.contract.utils.ChangeDataUtils;
import com.kyb.pcberp.modules.purch.entity.*;
import com.kyb.pcberp.modules.purch.service.PurchRawService;
import com.kyb.pcberp.modules.purch.service.PurchasingDetailService;
import com.kyb.pcberp.modules.purch.service.PurchasingService;
import com.kyb.pcberp.modules.purch.utils.IngredientsUtils;
import com.kyb.pcberp.modules.purch.vo.PurchasingExceVo;
import com.kyb.pcberp.modules.purch.vo.PurchasingVo;
import com.kyb.pcberp.modules.quality.entity.SourceDetection;
import com.kyb.pcberp.modules.quality.entity.SourceDetectionAttachements;
import com.kyb.pcberp.modules.quality.service.SourceDetectionService;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.service.MaterialService;
import com.kyb.pcberp.modules.sys.entity.*;
import com.kyb.pcberp.modules.sys.service.CompanyService;
import com.kyb.pcberp.modules.sys.utils.CockpitUtilsOne;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import com.kyb.pcberp.modules.wechat.entity.IcloudBidding;
//import javafx.scene.control.TextFormatter;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URL;
import java.text.ParseException;
import java.util.*;

/**
 * 原料采购Controller
 *
 * <AUTHOR>
 * @version 2015-09-15
 */
@Controller
@RequestMapping(value = "${adminPath}/purch/rawmaterial")
public class PurchasingController extends BaseController
{

    private final ObjectMapper quotationMapper = new JsonMapper().enableSimple();

    private final MappingJackson2JsonView view = new MappingJackson2JsonView();

    @JsonIgnoreProperties({"company", "userPwd", "remark", "lastUpdDate", "lastUpdBy", "createdBy", "createdDate",
        "status"})
    private static class UserFilter extends User
    {
        private static final long serialVersionUID = 1L;
    }

    @JsonIgnoreProperties({"seqNum", "company", "remark", "lastUpdDate", "lastUpdBy", "createdBy", "createdDate",
        "operLevel"})
    private static class DictValueFilter extends DictValue
    {
        private static final long serialVersionUID = 1L;
    }

    @Autowired
    private PurchasingService purchasingService;

    @Autowired
    private PurchasingDetailService purchasingDetailService;

    @Autowired
    private SourceDetectionService sourceDetectionService;

    /**
     * 物料service
     */
    @Autowired
    private MaterialService materialService;

    /**
     * 原料申购单
     */
    @Autowired
    private PurchRawService purchRawService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private MatPreparationService matPreparationService;

    // @Autowired
    // private ParentMessageService parentMessageService;

    @ModelAttribute
    public Purchasing get(@RequestParam(required = false) String id)
    {
        Purchasing entity = null;
        if (StringUtils.isNotBlank(id))
        {
            entity = purchasingService.get(id);
        }
        if (entity == null)
        {
            entity = new Purchasing();
        }
        return entity;
    }

    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "form")
    public String form(Purchasing puPurchasing, Model model)
    {
        model.addAttribute("puPurchasing", puPurchasing);
        return "modules/stock/puPurchasingForm";
    }

    /**
     * 删除原料采购单
     *
     * @param puPurchasing
     * @param redirectAttributes
     * @return
     */
    @RequiresPermissions("purch:rawmaterial:edit")
    @RequestMapping(value = "delete")
    public String delete(Purchasing puPurchasing, RedirectAttributes redirectAttributes)
    {
        Purchasing puPurchasingtemp = new Purchasing();
        purchasingService.delete(puPurchasing);
        puPurchasingtemp = purchasingService.get(puPurchasing);
        addMessage(redirectAttributes, "删除原料采购单\"" + puPurchasingtemp.getNo() + "\"成功！");
        return "redirect:" + Global.getAdminPath() + "/stock/puPurchasing/?repage";
    }

    /**
     * 定位页面
     *
     * @param purchasing
     * @return
     */
    @RequestMapping(value = "list")
    public String list(Purchasing purchasing)
    {
        return "modules/purch/purchasing";
    }

    @RequestMapping(value = "model/list")
    public String modelList(Purchasing purchasing)
    {
        return "modules/purch/purchasingmodel";
    }

    /**
     * 分页查询
     *
     * @param purchasing
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = {"page"})
    @ResponseBody
    public Page<Purchasing> getlist(@RequestBody Purchasing purchasing, HttpServletRequest request,
        HttpServletResponse response)
    {

        User user = UserUtils.getUser();
        // 设置查询范围
        if (purchasing.getQueryAll() != null && !purchasing.getQueryAll())
        {
            purchasing.setCreatedBy(user);
        }

        // 设置查询企业编号
        if (purchasing != null)
        {
            purchasing.setCompany(UserUtils.getUser().getCompany());
        }

        // 设置时间查询范围
        /*
         * String inoutTimeStartQr = request.getParameter("sentTimeStartQr"); String inoutTimeEndQr =
         * request.getParameter("sentTimeEndQr"); if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeStartQr))
         * { Date startdate = new Date(Long.parseLong(inoutTimeStartQr)); purchasing.setSentTimeStartQr(startdate); } if
         * (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeEndQr)) { Date enddate = new
         * Date(Long.parseLong(inoutTimeEndQr)); purchasing.setSentTimeEndQr(enddate); }
         */
        if (purchasing.getSentTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(purchasing.getSentTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            purchasing.setSentTimeEndQr(deliTime.getTime());
        }
        if (purchasing.getSentTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(purchasing.getSentTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            purchasing.setSentTimeStartQr(deliTime.getTime());
        }

        // 分页查询数据
        Page<Purchasing> qpage = new Page<Purchasing>(request, response);
        if (StringUtils.isNotBlank(purchasing.getPageNo()) && StringUtils.isNotBlank(purchasing.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(purchasing.getPageNo()));
            qpage.setPageSize(Integer.parseInt(purchasing.getPageSize()));
            if (StringUtils.isNotBlank(purchasing.getOrderBy()))
            {
                qpage.setOrderBy(purchasing.getOrderBy());
            }
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        if (StringUtils.isEmpty(purchasing.getType()))
        {
            purchasing.setType("1");
        }
        Page<Purchasing> page = purchasingService.findPage(qpage, purchasing);

        return page;
    }

    /**
     * 添加
     *
     * @param purchasing
     * @return
     */
    @RequiresPermissions("purch:rawmaterial:edit")
    @RequestMapping(value = "save", method = RequestMethod.POST)
    @ResponseBody
    public String save(@RequestBody Purchasing purchasing)
    {
        // 设置公司
        purchasing.setCompany(UserUtils.getUser().getCompany());
        if (purchasing.getIsNewRecord())
        {
            purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_UNCONFIRM);
        }
        purchasingService.save(purchasing);
        // sendMsg(purchasing, TypeKey.MESSAGE_PURCHASINGCREATE.toString(), "已经创建！");
        return "添加原料采购单\"" + purchasing.getNo() + "\"成功！";
    }

    @RequiresPermissions("purch:rawmaterial:edit")
    @RequestMapping(value = "deleteDetail", method = RequestMethod.POST)
    @ResponseBody
    public String deleteDetail(@RequestBody PurchasingDetail detail)
    {
        // 设置公司
        detail.setCompany(UserUtils.getUser().getCompany());
        if (detail.getIsNewRecord())
        {
            return "";
        }
        String result = purchasingDetailService.deleteDetail(detail);
        if(StringUtils.isBlank(result))
        {
            result = "成功！";
        }
        // sendMsg(purchasing, TypeKey.MESSAGE_PURCHASINGCREATE.toString(), "已经创建！");
        return result;
    }

    /**
     * 获取采购订单编号
     *
     * @return
     */
    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "getNo", method = {RequestMethod.POST})
    @ResponseBody
    public String getNo()
    {
        return CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString());
    }

    /**
     * 查询公司下面的供应商
     *
     * @return
     */
    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "findListOfSupplier", method = {RequestMethod.POST})
    @ResponseBody
    public List<Supplier> findListOfSupplier()
    {
        Supplier supplier = new Supplier();
        supplier.setCompany(UserUtils.getUser().getCompany());
        supplier.setStatus(TypeKey.MD_SUPPLIER_STATUS_NORMAL);
        return purchasingService.findListOfSupplier(supplier);
    }

    /**
     * 查询公司下所有的物料(原料)
     */
    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "findListOfMaterial", method = {RequestMethod.POST})
    @ResponseBody
    public List<Material> findListOfMaterial()
    {
        Material material = new Material();
        material.setCompany(UserUtils.getUser().getCompany());
        material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_RAW);
        return materialService.findList(material);
    }

    /**
     * 加载数据
     *
     * @param model
     * @return
     */
    @RequiresPermissions(value = {"purch:rawmaterial:view", "purch.rawApply:view"}, logical = Logical.OR)
    @RequestMapping(value = "load/data")
    public View loadData(Model model, Boolean queryAll)
        throws ParseException
    {
        view.setObjectMapper(quotationMapper);
        // 查询数据字典
        Map<String, List<?>> data = purchasingService.getDictValueByItems(queryAll);
        // zjn 2017-07-28 物料不包括余料
        Material material = new Material();
        material.setCompany(UserUtils.getUser().getCompany());
        material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_RAW);
        material.setRemain(ConstKey.MATERIAL_REMAIN_NO);
        List<Material> materialList = materialService.findListNoRemain(material);
        // 返回数据
        model.addAttribute("data", data);
        model.addAttribute("materialList", materialList);

        boolean auditFlag = ModifyUtils.allocationExist(CompanyUtil.getInstance().getFactId(), material.getCompany());
        if (!auditFlag)
        {
            auditFlag = ModifyUtils.allocationExist("16", material.getCompany());
        }
        model.addAttribute("auditFlag", auditFlag);

        Company jxCom = companyService.get(new Company(CompanyUtil.getInstance().getFactId()));
        model.addAttribute("jxCom", jxCom);

        List<GroupOrgRelation> relationList = CompanyUtil.getInstance().getGroupList();
        model.addAttribute("relationList", relationList);

        String ecoemyId = CompanyUtil.getInstance().getEcoemyId();
        Company ecoemy = new Company(ecoemyId);
        MatPreparationVersion matPreparationVersion = new MatPreparationVersion();
        matPreparationVersion.setCompany(ecoemy);
        List<MatPreparationVersion> versionList = matPreparationService.getVersionList(matPreparationVersion);
        model.addAttribute("versionList", versionList);

        // 获取年月列表
        List<Integer> periodList = DateUtils.getPeriodList(null, null);
        model.addAttribute("periodList", periodList);

        return view;
    }

    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "loadMaterial")
    @ResponseBody
    public List<Material> loadMaterial(@RequestBody String no)
    {
        Material material = new Material();
        material.setCompany(UserUtils.getUser().getCompany());
        material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_RAW);
        material.setRemain(ConstKey.MATERIAL_REMAIN_NO);
        material.setNo(no);
        List<Material> materialList = materialService.findListNoRemain(material);
        return materialList;
    }

    /**
     * 加载原料申请采购单数据 lh
     */
    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "getPurchRawList", method = {RequestMethod.POST})
    @ResponseBody
    public List<PurchRaw> getPurchRawList(@RequestBody PurchRaw purchRaw)
    {
        return purchasingService.getPurchRawList(purchRaw);
    }

    /**
     * 删除原料采购单
     *
     * @param id
     * @return
     */
    @RequiresPermissions("purch:rawmaterial:edit")
    @RequestMapping(value = "delete/{id}", method = {RequestMethod.POST})
    @ResponseBody
    public String delete(@PathVariable("id") String id)
    {
        purchasingService.updatePurchRawStatus(id, 1);

        Purchasing purchasing = new Purchasing(id);
        purchasing.setCompany(UserUtils.getUser().getCompany());
        String result = purchasingService.deletePur(purchasing);
        purchasing = purchasingService.get(purchasing);
        if(StringUtils.isNotBlank(result))
        {
            return result;
        }
        else
        {
            return "删除原料采购单\"" + purchasing.getNo() + "\"成功！";
        }

    }

    /**
     * 删除子表 根据子表编号
     *
     * @param id
     * @return
     */
    @RequiresPermissions("purch:rawmaterial:edit")
    @RequestMapping(value = "deleteDetail/{id}", method = {RequestMethod.POST})
    @ResponseBody
    public String deleteDetail(@PathVariable("id") String id)
    {
        PurchasingDetail PurchasingDetail = new PurchasingDetail(id);
        PurchasingDetail.setCompany(UserUtils.getUser().getCompany());
        // PurchasingDetail purchasingDetailCopy = purchasingDetailService.findPurchasingDetailbyId(PurchasingDetail);
        // 将该原料采购明细的原料申购单回滚

        CockpitUtilsOne cockpit = new CockpitUtilsOne();
        cockpit.addOperatingRecord(4,PurchasingDetail,2);
        purchasingDetailService.delete(PurchasingDetail);

        purchasingService.updatePurchRawStatus(id, 2);
        // sendMsg(purchasingDetailCopy,
        // TypeKey.MESSAGE_PURCHASINGDELETE.toString(),
        // "原料采购明细" + purchasingDetailCopy.getMaterial().getNo() + "已经删除！");
        return "原料采购明细删除成功！";
    }

    /**
     * 单独添加子表
     */
    @RequiresPermissions("purch:rawmaterial:edit")
    @RequestMapping(value = "savePurchasingDetail", method = RequestMethod.POST)
    @ResponseBody
    public String savePurchasingDetail(@RequestBody PurchasingDetail puPurchasingDetail)
    {
        SourceDetection sd = new SourceDetection();
        sd.setOrderId(puPurchasingDetail.getRecordId());
        sd.setCompany(UserUtils.getUser().getCompany());
        sd.setStatus(TypeKey.ST_SOURCEDETECTION_STATUS_AUDITED);
        sd.setResourceType(ConstKey.PA_PURCHASING_FLAG);
        List<SourceDetection> sourList = sourceDetectionService.getSourceDetectionList(sd);
        boolean isStorage = false;

        if (Collections3.isNotEmpty(sourList))
        {
            for (SourceDetection sourceDetection : sourList)
            {
                // 判断是否已入库
                if (sourceDetection.getStatus().intValue() == TypeKey.ST_SOURCEDETECTION_STATUS_INHOUSE.intValue())
                {
                    isStorage = true;
                    break;
                }
            }
        }
        CockpitUtilsOne cockpit = new CockpitUtilsOne();
        String rs = "";
        if (!isStorage)
        {
            // 修改之前的
            PurchasingDetail puPurchasing = purchasingDetailService.get(puPurchasingDetail);
            if (puPurchasingDetail.getIsNewRecord())
            {
                rs = "原料采购明细添加成功!";
            }
            else
            {
                purchasingService.updatePurchRawStatus(puPurchasingDetail.getRecordId(), 2);
                rs = "原料采购明细编辑成功!";
            }
            // 添加原料采购明细将原料申购修改成已完成
            if (StringUtils.isNotBlank(puPurchasingDetail.getPurchRawIds()))
            {
                puPurchasingDetail.setPurchRaw(new PurchRaw());
                puPurchasingDetail.getPurchRaw().setRecordId(puPurchasingDetail.getPurchRawIds());
                puPurchasingDetail.getPurchRaw().setStatus("5");
                purchRawService.updatePurRaw(puPurchasingDetail.getPurchRaw());
            }
            puPurchasingDetail.setCompany(UserUtils.getUser().getCompany());
            if (puPurchasing != null)
            {
                if (puPurchasing.getStatus().intValue() == TypeKey.PU_PURCHASING_STATUS_TEN.intValue())
                {
                    puPurchasingDetail.setStatus(TypeKey.PU_PURCHASING_STATUS_TEN);
                }
                else
                {
                    puPurchasingDetail.setStatus(TypeKey.PU_PURCHASING_STATUS_UNCONFIRM);
                }
            }
            else
            {
                puPurchasingDetail.setStatus(TypeKey.PU_PURCHASING_STATUS_UNCONFIRM);
            }

            purchasingDetailService.save(puPurchasingDetail);

            Purchasing purchasing = purchasingService.get(puPurchasingDetail.getPurchasing());
            // 已审核
            if (purchasing.getStatus().intValue() == TypeKey.PU_PURCHASING_STATUS_APPROVE.intValue())
            {
                Material material = puPurchasingDetail.getMaterial();
                if (material != null)
                {
                    material = materialService.get(material);
                    puPurchasingDetail.setMaterial(material);
                    List<PurchasingDetail> list = new ArrayList<PurchasingDetail>();
                    list.add(puPurchasingDetail);
                    purchasing.setPurchasingDetailList(list);
                }
            }
        }
        else
        {
            rs = "采购明细已进行入库操作，不能修改单价！";
        }
        return rs;
    }

    /**
     * 采购单状态修改
     *
     * @param operatingStatus
     * @return
     */
    @RequiresPermissions("purch:rawmaterial:edit")
    @RequestMapping(value = "updateStatus/{operatingStatus}", method = RequestMethod.POST)
    @ResponseBody
    public String updateStutas(@PathVariable("operatingStatus") String operatingStatus,
        @RequestBody Purchasing purchasing)
        throws ParseException
    {
        String rs = "";
        if (operatingStatus.equals("0"))
        {
            /*rs = purchasingService.purchasingApproval(purchasing);
            if (!"fail".equals(rs))
            {
                return rs;
            }*/
            rs = "确认成功！";
            // 确认
            purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_CONFIRMED);

            // sendMsg(purchasing, TypeKey.MESSAGE_PURCHASINGGCOMMIT.toString(), "已经确认！");
        }
        else if (operatingStatus.equals("1"))
        {
            rs = purchasingService.purchasingApproval(purchasing);
            if (!"fail".equals(rs))
            {
                return rs;
            }
            rs = "审核成功！";
            if (StringUtils.isNotBlank(purchasing.getType()) && purchasing.getType().equals("1"))
            {
                IngredientsUtils ingredientsUtils = new IngredientsUtils();
                String bindErpId = purchasingService.isSupplierCenter(purchasing);
                Ingredients ingredients = ingredientsUtils.getIngredients(purchasing);
                if(null != ingredients)
                {
                    ingredientsUtils.genaricMaterialContract(purchasing,ingredients);
                    purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_APPROVE);
                }
                else if(StringUtils.isNotBlank(bindErpId))
                {
                    // 生成供应链公司的销售订单，并修改状态
                    purchasingService.genaricMaterialContract(purchasing, bindErpId);
                    purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_SUPPLIER);
                }
                else
                {
                    purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_APPROVE);
                }
            }

            // 认证公司是否是供应链公司
/*            String bindErpId = purchasingService.isSupplierCenter(purchasing);
            if (StringUtils.isNotBlank(bindErpId))
            {
                // 生成供应链公司的销售订单，并修改状态
                purchasingService.genaricMaterialContract(purchasing, bindErpId);
                purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_SUPPLIER);
            }
            else
            {
                purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_APPROVE);
            }*/
        }
        else if (operatingStatus.equals("2"))
        {

            rs = "已被取消！";
            // 取消采购单
            purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_CANCLE);
            purchasingService.updatePurchRawStatus(purchasing.getRecordId(), 1);
            // sendMsg(purchasing, TypeKey.MESSAGE_PURCHASINGCANCLE.toString(), "已经取消！");
        }
        purchasingService.updateStatus(purchasing);
        return "原料采购单\"" + purchasing.getNo() + "\"" + rs;
    }

    @RequestMapping(value = "genaricSupplierMsg", method = RequestMethod.POST)
    @ResponseBody
    public String genaricSupplierMsg(@RequestBody Purchasing purchasing)
        throws ParseException
    {
        IngredientsUtils ingredientsUtils = new IngredientsUtils();
        Ingredients ingredients = ingredientsUtils.getIngredients(purchasing);
        if (null != ingredients)
        {
            ingredientsUtils.genaricMaterialContract(purchasing, ingredients);
        }
        return "success";
    }

    /**
     * 根据原料采购单的编号去 查询子单
     *
     */
    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "getPurchasingDetailByPurchasingId", method = RequestMethod.POST)
    @ResponseBody
    public List<PurchasingDetail> getPurchasingDetailByPurchasingId(@RequestBody Purchasing purchasing)
    {
        return purchasingDetailService.getPurchasingDetailByPurchasingId(purchasing);
    }

    /**
     * 打印
     *
     * @param recordId
     * @param request
     * @param response
     * @return
     */

    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "print")
    public ResponseEntity<byte[]> printPDF(String recordId, HttpServletRequest request, HttpServletResponse response)
    {
        Purchasing purchasing = new Purchasing(recordId);
        purchasing.setCompany(UserUtils.getUser().getCompany());
        Pdf pdf = purchasingService.getPdf(purchasing, request.getSession().getServletContext().getRealPath("/"), "");
        if (pdf == null)
        {
            ResponseEntity<byte[]> responseEntity = null;
            return responseEntity;
        }
        else
        {
            ResponseEntity<byte[]> responseEntity = null;
            if (null != pdf.getOutPut() && null != pdf.getOutPut().toByteArray())
            {
                byte[] contents = pdf.getOutPut().toByteArray();
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.parseMediaType("application/pdf"));
                String filename = CommonUtils.getDisplayPDFName(pdf.getShowName());
                headers.add("X-File-Name", filename);
                headers.setContentDispositionFormData(filename, filename);
                responseEntity = new ResponseEntity<byte[]>(contents, headers, HttpStatus.OK);
                return responseEntity;
            }
            else
            {
                try
                {
                    new PdfResult().doExecute(pdf.getFileName(),
                        request,
                        response,
                        pdf.getDataMap(),
                        pdf.getShowName());
                }
                catch (IOException e)
                {
                    e.printStackTrace();
                }
            }
            return responseEntity;
        }
    }

    /**
     * WC 2017-09-13 打印已入库单
     */
    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "printInHouse")
    public String printInHousePDF(String recordId, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            Purchasing purchasing = new Purchasing(recordId);
            Pdf pdf =
                purchasingService.getInHousePdf(purchasing, request.getSession().getServletContext().getRealPath("/"));
            new PdfResult().doExecute(pdf.getFileName(), request, response, pdf.getDataMap());
            return null;
        }
        catch (Exception e)
        {
        }
        return null;
    }

    /**
     * 导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "export")
    public String exportFile(Purchasing purchasing, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            // 设置查询范围
            if (purchasing.getQueryAll() != null && !purchasing.getQueryAll())
            {
                purchasing.setCreatedBy(user);
            }
            // 设置查询企业编号
            if (purchasing != null)
            {
                purchasing.setCompany(user.getCompany());
            }
            // 设置时间查询范围
            String inoutTimeStartQr = request.getParameter("sentTimeStartQr");
            String inoutTimeEndQr = request.getParameter("sentTimeEndQr");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(inoutTimeStartQr)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                purchasing.setSentTimeStartQr(deliTime.getTime());
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(inoutTimeEndQr)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                purchasing.setSentTimeEndQr(deliTime.getTime());
            }
            if (!StringUtils.isEmpty(request.getParameter("type")))
            {
                purchasing.setType(request.getParameter("type"));
            }
            else
            {
                purchasing.setType("1");
            }
            List<PurchasingVo> purchasingVoList = purchasingService.exportPurchasingData(purchasing);
            String fileName = "原料采购订单" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            new ExportExcel("原料采购订单", PurchasingVo.class, new Integer(1)).setDataList(purchasingVoList)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
    }

    /**
     * 调整原料采购数量 MOD:WC 2017-08-03
     */
    @RequiresPermissions("purch:rawmaterial:edit")
    @RequestMapping(value = "adjustRawNum", method = {RequestMethod.POST})
    @ResponseBody
    public String adjustRawNum(@RequestBody PurchasingDetail puPurchasingDetail)
    {
        purchasingService.adjustNum(puPurchasingDetail);
        // Purchasing purchasing = (Purchasing)map.get("puuchasing");
        // Integer adjustQty = (Integer)map.get("adjustQty");
        // if (adjustQty.compareTo(new Integer(0)) == -1)
        // {
        // sendMsg(purchasing, TypeKey.MESSAGE_PURCHASINGADJUST.toString(), "往下调整了" + (-adjustQty) + "个原料");
        // }
        // else
        // {
        // sendMsg(purchasing, TypeKey.MESSAGE_PURCHASINGADJUST.toString(), "往上调整了" + adjustQty + "个原料");
        // }
        return "调整原料采购成功";
    }

    /**
     * 根据原料采购单的编号去 查询可以调整的子单
     *
     */
    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "getRawDetail", method = RequestMethod.POST)
    @ResponseBody
    public List<PurchasingDetail> getRawDetail(@RequestBody Purchasing purchasing)
    {
        purchasing.setCompany(UserUtils.getUser().getCompany());
        return purchasingDetailService.getRawDetail(purchasing);
    }

    // public void sendMsg(Purchasing puPurchasing, String msgType, String name)
    // {
    // ParentMessage parentMessage = new ParentMessage();
    // parentMessage.setMessageModuleCopy(msgType);
    // parentMessage.setMessaeDeail("编号为" + puPurchasing.getNo() + "的原料采购单" + name);
    // if (puPurchasing.getCreatedBy() != null)
    // {
    // parentMessage.setCreateByName(puPurchasing.getCreatedBy());
    // }
    // parentMessageService.sendMessage(parentMessage);
    // }

    // public void sendMsg(PurchasingDetail purchasingDetail, String msgType, String name)
    // {
    // ParentMessage parentMessage = new ParentMessage();
    // parentMessage.setMessageModuleCopy(msgType);
    // parentMessage.setMessaeDeail("编号为" + purchasingDetail.getPurchasing().getNo() + "的原料采购单" + name);
    // if (purchasingDetail.getCreatedBy() != null)
    // {
    // parentMessage.setCreateByName(purchasingDetail.getCreatedBy());
    // }
    // parentMessageService.sendMessage(parentMessage);
    // }

    /**
     * WC 2017-08-02 修改采购单保存时重新计算明细的金额
     */
    @RequiresPermissions("purch:rawmaterial:edit")
    @RequestMapping(value = "updateDetailsAmout", method = RequestMethod.POST)
    @ResponseBody
    public Boolean updateDetailsAmout(@RequestBody Purchasing puPurchasing)
    {
        return purchasingService.updateDetailsAmout(puPurchasing);
    }

    /** tj 2019-03-18 获取原料采购入库数量 */
    @RequestMapping(value = "getWarehousingSum", method = {RequestMethod.POST})
    @ResponseBody
    public Integer getWarehousingSum(@RequestBody String recordId)
    {
        String companyId = UserUtils.getUser().getCompany().getRecordId();
        Integer sum = purchasingService.getWarehousingSum(recordId, companyId);
        return sum;
    }

    /** lq 2019-04-23 */
    @RequestMapping(value = "checkSupplierNo", method = {RequestMethod.POST})
    @ResponseBody
    public String checkSupplierNo(@RequestBody Purchasing purchasing)
    {
        return purchasingService.checkSupplierNo(purchasing);
    }

    /** tj 采购结案 2019-05-05 */
    @RequestMapping(value = "closingCase", method = {RequestMethod.POST})
    @ResponseBody
    public String closingCase(@RequestBody PurchasingDetail detail)
    {
        return purchasingService.closingCase(detail);
    }

    /** tj 采购合同结案信息 */
    @RequestMapping(value = "closingCaseInfo")
    @ResponseBody
    public List<PurchasingDetail> closingCaseInfo(@RequestBody Purchasing purchasing)
    {
        return purchasingService.closingCaseInfo(purchasing);
    }

    // 反审
    @RequestMapping(value = "counterTrial")
    @ResponseBody
    public Map<String, Object> counterTrial(@RequestBody Purchasing purchasing)
    {
        return purchasingService.counterTrial(purchasing);
    }

    // 采购入库后修改订单数量/单价
    @RequestMapping(value = "updatePurDetailData")
    @ResponseBody
    public Map<String, Object> updatePurDetailData(@RequestBody PurchasingDetail purchasingDetail)
    {
        return purchasingService.updatePurDetailData(purchasingDetail);
    }

    /**
     * 导出采购状况一览表
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "exportPurWholeInfo")
    public String exportPurWholeInfo(Purchasing purchasing, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            // 设置查询范围
            if (purchasing.getQueryAll() != null && !purchasing.getQueryAll())
            {
                purchasing.setCreatedBy(user);
            }
            // 设置查询企业编号
            if (purchasing != null)
            {
                purchasing.setCompany(user.getCompany());
            }
            // 设置时间查询范围
            String inoutTimeStartQr = request.getParameter("sentTimeStartQr");
            String inoutTimeEndQr = request.getParameter("sentTimeEndQr");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(inoutTimeStartQr)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                purchasing.setSentTimeStartQr(deliTime.getTime());
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(inoutTimeEndQr)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                purchasing.setSentTimeEndQr(deliTime.getTime());
            }

            if (!StringUtils.isEmpty(request.getParameter("type")))
            {
                purchasing.setType(request.getParameter("type"));
            }
            else
            {
                purchasing.setType("1");
            }
            List<PurchasingExceVo> purchasingVoList = purchasingService.exportPurWholeInfo(purchasing);
            String fileName = "采购状况一览表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            new ExportExcel("采购状况一览表", PurchasingExceVo.class, new Integer(1)).setDataList(purchasingVoList)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
    }

    // fzd 2022-04-14 导入上传凭证
    @RequestMapping(value = "uploadProof")
    @ResponseBody
    public Map<String, String> uploadProof(@RequestBody MultipartFile f, HttpServletRequest request)
        throws Exception
    {
        Map<String, String> map = new HashMap<>();
        Company company = UserUtils.getUser().getCompany();
        String supplierId = request.getParameter("supplierId");
        String purchasingId = request.getParameter("purchasingId");
        // MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest)request;
        System.out.println("通过传统方式form表单提交方式导入excel文件！" + supplierId + purchasingId);

        // 将文件上传至文件库备份
        String savePath =
            "default" + "/" + company.getRecordId() + "/" + supplierId + "/" + purchasingId + "/" + "proof/";

        // 上传文件
        String url = FileManageUtils.uploadFile(f, savePath, request);
        if (url == null)
        {
            map.put("result", "fail");
            map.put("message", "文件上传不成功，请刷新重试");
            return map;
        }
        // 基本路径
        String sourcePath = savePath + f.getOriginalFilename();
        // 该处可调用service相应方法进行数据保存到数据库中，现只对数据输出
        return purchasingService.inData(purchasingId, sourcePath);
    }

    @RequestMapping(value = "batchUpdateStatus", method = RequestMethod.POST)
    @ResponseBody
    public String batchUpdateStatus(@RequestBody List<Purchasing> purchasingList)
    {
        return purchasingService.batchUpdateStatus(purchasingList);
    }
    
    @RequestMapping(value = "initSupplyChainData", method = RequestMethod.POST)
    @ResponseBody
    public String initSupplyChainData(@RequestBody Purchasing Purchasing)
    {
        ChangeDataUtils changeDataUtils = new ChangeDataUtils();
        changeDataUtils.initSupplyChainData(Purchasing.getRecordId());
        return "success";
    }

    // 反审
    @RequestMapping(value = "counterTrialDetail")
    @ResponseBody
    public Map<String, Object> counterTrialDetail(@RequestBody PurchasingDetail purchasingDetail)
    {
        return purchasingService.counterTrialDetail(purchasingDetail);
    }

    // 撤回审批
    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "doCancelAudit/{id}", method = RequestMethod.POST)
    @ResponseBody
    public String doCancelAudit(@PathVariable("id") String id)
    {
        return purchasingService.doCancelAudit(id);
    }

    @RequestMapping(value = "getPurchasingDetailStatus", method = RequestMethod.POST)
    @ResponseBody
    public String getPurchasingDetailStatus(@RequestBody PurchasingDetail purchasingDetail)
    {
        return purchasingDetailService.getPurchasingDetailStatus(purchasingDetail);
    }

    // 上传附件
    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "uploadInvoice")
    @ResponseBody
    public Boolean uploadInvoice(@RequestParam("file") MultipartFile file,
        @RequestParam("recordId") String recordId, HttpServletRequest req)
    {
        try
        {
            PurchasingDetail purchasingDetail = purchasingDetailService.get(recordId);
            if (null == purchasingDetail || null == purchasingDetail.getRecordId())
            {
                return false;
            }
            String companyId = purchasingDetail.getCompany().getRecordId();

            // 文件存放路径
            String filePath = "purchasingDetail" + "/" + companyId + "/" + "purchasingDetail/proof/";
            // 文件存放路径
            String url = FileManageUtils.uploadFile(file, filePath, req);
            if (url == null)
            {
                return false;
            }
            purchasingDetailService.addPurchasingDetailAttachements(purchasingDetail,filePath, file.getOriginalFilename());
            return true;
        }
        catch (Exception r)
        {
            return false;
        }
    }

    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "getPurchasingDetailAttachementsList/{purchasingDetailId}", method = RequestMethod.POST)
    @ResponseBody
    public List<PurchasingDetailAttachements> getPurchasingDetailAttachementsList(@PathVariable("purchasingDetailId") String purchasingDetailId)
    {
        PurchasingDetailAttachements purchasingDetailAttachements = new PurchasingDetailAttachements();
        purchasingDetailAttachements.setPurchasingDetailId(purchasingDetailId);
        purchasingDetailAttachements.setCompany(UserUtils.getUser().getCompany());
        List<PurchasingDetailAttachements> list = purchasingDetailService.getPurchasingDetailAttachementsList(purchasingDetailAttachements);
        return list;
    }

    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "delFile")
    @ResponseBody
    public Boolean delFile(@RequestBody PurchasingDetailAttachements purchasingDetailAttachements)
    {
        // 从aliyun服务器下载
        OSSClient client =
            new OSSClient(FileManageUtils.getEndPoint(), FileManageUtils.accessKeyId, FileManageUtils.accessKeySecret);
        try
        {
            // 删除文件
            OssUtils.deleteFile(client, FileManageUtils.customFile, purchasingDetailAttachements.getRealFileName());
            // 删除记录
            purchasingDetailService.delPurchasingDetailAttachements(purchasingDetailAttachements);
            return true;
        }
        catch (Exception e)
        {
            return false;
        }
    }

    @RequiresPermissions("purch:rawmaterial:view")
    @RequestMapping(value = "getUrl")
    @ResponseBody
    public URL getUrl(@RequestBody PurchasingDetailAttachements purchasingDetailAttachements)
    {
        return FileManageUtils.getUrl(purchasingDetailAttachements.getRealFileName());
    }

    @RequestMapping(value = "getWaitingList")
    @ResponseBody
    public List<PurchRaw> getWaitingList()
    {
        return purchasingDetailService.getWaitingList();
    }

    @RequestMapping(value = "sentPruch")
    @ResponseBody
    public String sentPruch(@RequestBody List<PurchRaw> list)
    {
        return purchasingDetailService.sentPruch(list);
    }

    @RequestMapping(value = "getOpenData")
    @ResponseBody
    public Map<String,Object> getOpenData(@RequestBody PurchRaw purchRaw)
    {
        return purchasingDetailService.getOpenData(purchRaw);
    }

    @RequiresPermissions("purch:rawmaterial:edit")
    @RequestMapping(value = "approveClosedPurchasing", method = RequestMethod.POST)
    @ResponseBody
    public String approveClosedPurchasing(@RequestBody Purchasing purchasing)
    {
        return purchasingDetailService.approveClosedPurchasing(purchasing);
    }

    @RequestMapping(value = "getMaterialList", method = RequestMethod.POST)
    @ResponseBody
    public List<Material> getMaterialList(@RequestBody String type)
    {
        Material material = new Material();
        material.setCompany(UserUtils.getUser().getCompany());
        material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_RAW);
        material.setRemain(ConstKey.MATERIAL_REMAIN_NO);
        material.setSubjectType(type);
        return materialService.findListNoRemain(material);
    }

    @RequestMapping(value = "getInternalSupplyChainList", method = RequestMethod.POST)
    @ResponseBody
    public List<Supplier> getInternalSupplyChain(@RequestBody Supplier supplier)
    {
        return purchasingService.getInternalSupplyChainList(supplier);
    }

    @RequestMapping(value = "delDeliveryList", method = RequestMethod.POST)
    @ResponseBody
    public String delDeliveryList(@RequestBody PurchasingDetailDeliverys deliverys)
    {
        return purchasingDetailService.delDeliveryList(deliverys);
    }

    @RequestMapping(value = "getPurchasingDetailDeliverysList/{purchasingDetailId}", method = RequestMethod.POST)
    @ResponseBody
    public List<PurchasingDetailDeliverys> getPurchasingDetailDeliverysList(@PathVariable("purchasingDetailId") String purchasingDetailId)
    {
        PurchasingDetailDeliverys deliverys = new PurchasingDetailDeliverys();
        deliverys.setPurchasingDetailId(purchasingDetailId);
        deliverys.setCompany(UserUtils.getUser().getCompany());
        List<PurchasingDetailDeliverys> list = purchasingDetailService.getPurchasingDetailDeliverysList(deliverys);
        return list;
    }

}