/**
 * 
 */
package com.kyb.pcberp.modules.sys.web;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.kyb.pcberp.common.config.ConfigKey;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.service.CompanyService;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

/**
 * 公司Controller
 * 
 * <AUTHOR>
 * @version 2015-08-11
 */
@Controller
@RequestMapping(value = "${adminPath}/sys/company")
public class CompanyController extends BaseController
{
    
    @Autowired
    private CompanyService companyService;
    
   /* @Autowired
    private ParentMessageService parentMessageService;*/
    
    @RequestMapping(value = "view")
    public String view()
    {
        return "modules/sys/company";
    }
    
    /**
     * 添加(不带文件)
     * 
     * @param company
     * @return
     */
    @RequiresPermissions("sys:company:edit")
    @RequestMapping(value = "save", method = RequestMethod.POST)
    @ResponseBody
    public String saveBranch(@RequestBody Company company, HttpServletRequest request, HttpServletResponse response)
    {
        companyService.save(company);
        return "保存公司“" + company.getShortName() + "”成功！";
    }
    
    /**
     * 保存公司信息
     * 
     * @param
     * @return
     */
    @RequiresPermissions("sys:company:edit")
    @RequestMapping(value = "saveCompany", method = RequestMethod.POST)
    @ResponseBody
    public String saveCompany(Company company, MultipartFile file, HttpServletRequest request)
    {
        if (FileManageUtils.isLocal())
        {
            // 本地上传
            String status = companyService.setCompanyLogoAndUpload(file, company, request);
            if (status.equals(ConfigKey.ERROR))
            {
                return "上传公司标志失败！";
            }
        }
        else
        {
            // 上传至aliyun
            String filePath = companyService.getRelativePath(company.getRecordId());
            
            // 创建路径
            // 上传公司LOGO至云服务
            String url = FileManageUtils.uploadFile(file, filePath, request);
            
            if (null == url)
            {
                return "上传公司标志失败！";
            }
            else
            {
                company.setLogo(filePath + file.getOriginalFilename());
            }
        }
        companyService.saveCompanyAndLogo(company);
        return "保存公司“" + company.getShortName() + "”成功！";
    }
    
    /**
     * 获取用户所在公司信息
     * 
     * @param mdCompany
     * @param model
     * @return
     */
    @RequestMapping(value = "listData", method = RequestMethod.POST)
    @ResponseBody
    public Company listData(@RequestBody Company company)
    {
        // 根据登录之后用户所在的公司编号来查询公司信息
        Company cp = companyService.get(UserUtils.getUser().getCompany().getRecordId().toString());
        
        if (StringUtils.isNotBlank(cp.getLogo()))
        {
            if (FileManageUtils.isLocal())
            {
                cp.setLogo(FileManageUtils.getLocalOrOssUrlAsString(cp.getLogo()));
            }
            else
            {
                cp.setLogo(FileManageUtils.getUrl(cp.getLogo()).toString());
            }
        }
        
        return cp;
    }
    
    /**
     * 校验公司编码唯一
     * 
     * @param
     * @return
     */
    @RequestMapping(value = "uniqueCode", method = RequestMethod.POST)
    @ResponseBody
    public String uniqueCode(@RequestBody String code)
    {
        boolean unique = companyService.uniqueCode(code, UserUtils.getUser().getCompany().getRecordId());
        
        return "{\"result\":" + unique + "}";
    }
    
    /**
     * 更新公司编码
     * 
     * @param
     * @return
     */
    @RequestMapping(value = "updatePreCode", method = RequestMethod.POST)
    @ResponseBody
    public String updatePreCode()
    {
        Company cp = companyService.get(UserUtils.getUser().getCompany().getRecordId().toString());
        companyService.updatePreCode(cp);
        
        return "{\"result\":true}";
    }
    
    /**
     * 校验公司编码唯一
     * 
     * @param
     * @return
     */
    @RequestMapping(value = "getPreCode", method = RequestMethod.POST)
    @ResponseBody
    public String getPreCode()
    {
        Company cp = companyService.get(UserUtils.getUser().getCompany().getRecordId().toString());
        
        return cp.getCode();
    }
    
    /**
     * 查询所有企业
     * @return
     */
    @RequestMapping(value = "findAll")
    @ResponseBody
    public List<Company> findAll()
    {
    	return companyService.findAll();
    }
    
  /*  public void sendMsg(User user,String msgType, String deail)
    {
        ParentMessage parentMessage = new ParentMessage();
        parentMessage.setMessageModuleCopy(msgType);
        parentMessage.setMessaeDeail(deail);
        parentMessage.setCreateByName(user);
        parentMessageService.sendMessage(parentMessage);
    }*/





    /**
     * <AUTHOR>
     * @Description  改变公司角色与权限模板
     * @Date 14:59 2018-09-25
     * @Param [type] 1生产模板   2贸易模板
     * @return
     **/
    @RequestMapping(value = "setCompanyModel", method = RequestMethod.POST)
    @ResponseBody
    public int setCompanyModel(@RequestBody Integer type)
    {
      //根据登录用户来获取公司的Id
        String companyId= UserUtils.getUser().getCompany().getRecordId();
        companyService.changeCompanyModel(type,companyId);
        return type;
    }




}