/* Setup general page controller */
kybApp.controller('drCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil','BaseUtil','$http', function ($rootScope, $scope, upida, $timeout, CommonUtil,BaseUtil,$http) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
    var vm = this;
    vm.right = {};
    
    vm.startTime = "";
	vm.endTime = "";
    
    // 时间范围的选项
	vm.rangeOptions = {
		format: "YYYY-MM-DD",
		startDate: new Date((new Date).setDate("01")),
		minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
	};

	// 时间范围的Model
    vm.time = {
		start: new Date((new Date).setDate("01")),
		end: new Date()
	};
    
    vm.initDate = function(date) {
		if(date == "") {
			vm.rangeOptions = {
				startDate: new Date(vm.startTime),
				minDate: new Date(new Date(vm.endTime).setFullYear(new Date(vm.endTime).getFullYear() - 5))
			};
			vm.time = {
				start: vm.rangeOptions.startDate,
				end: vm.rangeOptions.minDate
			}
		}
	};
    
    vm.tabs = {
    	deliveryRadio: {active: true}
    };
    
    vm.deliveryRadioList = [];
    vm.loadDeliveryRadioList = function() {
    	MainCtrl.blockUI({
            animate: true,
        });
    	var report = {};
    	if(vm.time.start && vm.time.start != {}) {
			vm.startTime = vm.time.start.valueOf();
		}
		if(vm.time.end && vm.time.end != {}) {
			vm.endTime = vm.time.end.valueOf();
		}
    	if(vm.startTime){
    		report.startTime = vm.startTime;
    	}else{
    		report.startTime = new Date((new Date).setDate("01"));
    	}
    	if(vm.endTime){
    		report.endTime = vm.endTime;
    	}else{
    		report.endTime = new Date;
    	}
     	upida.post("report/reportData/getDeliveryReportData",report).then(function(data){
     		vm.deliveryRadioList = data;
     		// 报表数据处理
     		vm.dealDeliveryRaioData();
     		MainCtrl.unblockUI();
     	});
	};
	
	// 交货延期
	vm.deliveryOueList = [];
	
	vm.deliveryOueProList = [];
	vm.deliveryOuePurList = [];
	vm.deliveryOueStoList = [];
	
	// 生产延期
	vm.produceOueList = [];
	// 采购延期
	vm.purchOueList = [];
	// 提前生产
	vm.earyList = [];
	// 到期未送货延期
	vm.stockOueList = [];
	// 采购生产双延期
	vm.purProList = [];
	// 采购到期未送货双延期
	vm.purStoList = [];
	// 生产到期未送货双延期
	vm.proStoList = [];
	// 采购生产到期未送货三延期
	vm.purProStoList = [];
	
	// 交货正常
	vm.deliveryNomalList = [];
	vm.produceNomalList = [];
	vm.otherList = [];
	vm.dealDeliveryRaioData = function() {
		vm.deliveryOueList = [];
		
		vm.deliveryOueProList = [];
		vm.deliveryOuePurList = [];
		vm.deliveryOueStoList = [];
		vm.deliveryOueSaleList = [];
		vm.deliveryOueCardList = [];
		
		vm.produceOueList = [];
		vm.purchOueList = [];
		vm.earyList = [];
		vm.stockOueList = [];
		
		vm.purProList = [];
		vm.purStoList = [];
		vm.proStoList = [];
		vm.purProStoList = [];
		
		vm.deliveryNomalList = [];
		vm.produceNomalList = [];
		vm.otherList = [];
		vm.earyNomalList = [];
		vm.stockNomalList = [];
		if(vm.deliveryRadioList && vm.deliveryRadioList.length > 0){
			// 数据处理
			for(let i=0;i<vm.deliveryRadioList.length;i++){
				if(vm.deliveryRadioList[i].produceFlag){
					if(vm.deliveryRadioList[i].produceFlag == "1") {
						vm.produceOueList.push(vm.deliveryRadioList[i]);
					}else if(vm.deliveryRadioList[i].produceFlag == "2"){
						vm.earyList.push(vm.deliveryRadioList[i]);
					}
				}
				if(vm.deliveryRadioList[i].produceFlag != "1"){
					vm.produceNomalList.push(vm.deliveryRadioList[i]);
				}
				if(vm.deliveryRadioList[i].produceFlag != "2"){
					vm.earyNomalList.push(vm.deliveryRadioList[i]);
				}
				if(vm.deliveryRadioList[i].purFlag && vm.deliveryRadioList[i].purFlag == "1"){
					vm.purchOueList.push(vm.deliveryRadioList[i]);
				}
				if(vm.deliveryRadioList[i].stockFlag && vm.deliveryRadioList[i].stockFlag == "1"){
					vm.stockOueList.push(vm.deliveryRadioList[i]);
				}else{
					vm.stockNomalList.push(vm.deliveryRadioList[i]);
				}
				if(vm.deliveryRadioList[i].deliveryFlag && vm.deliveryRadioList[i].deliveryFlag == "1"){
					vm.deliveryOueList.push(vm.deliveryRadioList[i]);
					
					if(vm.deliveryRadioList[i].stockFlag && vm.deliveryRadioList[i].stockFlag == "1"){
						vm.deliveryOueStoList.push(vm.deliveryRadioList[i]);
					}
					if(vm.deliveryRadioList[i].purFlag && vm.deliveryRadioList[i].purFlag == "1"){
						vm.deliveryOuePurList.push(vm.deliveryRadioList[i]);
					}
					if(vm.deliveryRadioList[i].produceFlag && vm.deliveryRadioList[i].produceFlag == "1"){
						vm.deliveryOueProList.push(vm.deliveryRadioList[i]);
					}
					if(vm.deliveryRadioList[i].saleFlag && vm.deliveryRadioList[i].saleFlag == "1"){
						vm.deliveryOueSaleList.push(vm.deliveryRadioList[i]);
					}
					if(vm.deliveryRadioList[i].cardFlag && vm.deliveryRadioList[i].cardFlag == "1"){
						vm.deliveryOueCardList.push(vm.deliveryRadioList[i]);
					}
				}else{
					vm.deliveryNomalList.push(vm.deliveryRadioList[i]);
				}
				
				if(vm.deliveryRadioList[i].produceFlag && vm.deliveryRadioList[i].produceFlag == "1"
					&& vm.deliveryRadioList[i].purFlag && vm.deliveryRadioList[i].purFlag == "1"
						&& vm.deliveryRadioList[i].stockFlag && vm.deliveryRadioList[i].stockFlag == "1"){
					vm.purProStoList.push(vm.deliveryRadioList[i]);
				}else if(vm.deliveryRadioList[i].produceFlag && vm.deliveryRadioList[i].produceFlag == "1"
					&& vm.deliveryRadioList[i].purFlag && vm.deliveryRadioList[i].purFlag == "1"){
					vm.purProList.push(vm.deliveryRadioList[i]);
				}else if(vm.deliveryRadioList[i].produceFlag && vm.deliveryRadioList[i].produceFlag == "1"
					&& vm.deliveryRadioList[i].purFlag && vm.deliveryRadioList[i].stockFlag == "1"){
					vm.proStoList.push(vm.deliveryRadioList[i]);
				}else if(vm.deliveryRadioList[i].produceFlag && vm.deliveryRadioList[i].stockFlag == "1"
					&& vm.deliveryRadioList[i].purFlag && vm.deliveryRadioList[i].purFlag == "1"){
					vm.purStoList.push(vm.deliveryRadioList[i]);
				}
			}
		}
		vm.initDeliveryEcharts();
	};
	
	vm.showName = "";
	vm.showList = [];
	// 饼图渲染
	vm.initDeliveryEcharts = function(){
		const _this = this;
		if ($('#wipDeliveryReport').is(':visible')) {
			echarts.dispose(document.getElementById('wipDeliveryReport'));
			var myChart = echarts.init(document.getElementById('wipDeliveryReport'));
			var text = "交货达成率";
			var datas1 = [
				    { name: '交货正常', value: vm.deliveryNomalList.length
				    ,itemStyle: {color: '#66CC66'}
				    },
				    { name: '交货延期', value: vm.deliveryOueList.length
				    ,itemStyle: {color: '#FF3300'}
				    }
				  ];
			var datas2 =[
				    { name: '生产正常', value: vm.produceNomalList.length
				    ,itemStyle: {color: '#66CC66'}
				    },
				    { name: '生产延期', value: vm.produceOueList.length
				    ,itemStyle: {color: '#FF3300'}
				    }
				  ];
			var datas3 =[
				    { name: '提前生产', value: vm.earyList.length
				    ,itemStyle: {color: '#FF3300'}},
				    { name: '正常生产', value: vm.earyNomalList.length
				    ,itemStyle: {color: '#66CC66'}}
				  ];
			var datas4 =[
			    { name: '市场交货延期', value: vm.stockOueList.length
			    ,itemStyle: {color: '#FF3300'}},
			    { name: '正常交货', value: vm.stockNomalList.length
			    ,itemStyle: {color: '#66CC66'}}
			  ];
			option = {
					title: [
					    {
					      subtext: '交货达成率',
					      left: '12%',
					      textAlign: 'center'
					    },
					    {
					      subtext: '生产达成率',
					      left: '37%',
					      textAlign: 'center'
					    },
					    {
					      subtext: '提前生产率',
					      left: '62%',
					      textAlign: 'center'
					    },
					    {
					      subtext: '市场交货延期率',
					      left: '87%',
					      textAlign: 'center'
					    }
					  ],
						  series: [
							    {
							      type: 'pie',
							      radius: '25%',
							      center: ['50%', '20%'],
							      data: datas1,
							      label: {
							    	  alignTo: 'edge',
							          formatter: '{{d}%}',
							          minMargin: 5,
							          edgeDistance: 10,
							          lineHeight: 15,
							          rich: {
							            time: {
							              fontSize: 10,
							              color: '#999'
							            }
							          }
							      },
							      left: 0,
							      right: '75%',
							      top: 0,
							      bottom: 0
							    },
							    {
							      type: 'pie',
							      radius: '25%',
							      center: ['50%', '20%'],
							      data: datas2,
							      label: {
							    	  alignTo: 'edge',
							          formatter: '{{d}%}',
							          minMargin: 5,
							          edgeDistance: 10,
							          lineHeight: 15,
							          rich: {
							            time: {
							              fontSize: 10,
							              color: '#999'
							            }
							          }
							      },
							      left: '25%',
							      right: '50%',
							      top: 0,
							      bottom: 0
							    },
							    {
							      type: 'pie',
							      radius: '25%',
							      center: ['50%', '20%'],
							      data: datas3,
							      label: {
							    	  alignTo: 'edge',
							          formatter: '{{d}%}',
							          minMargin: 5,
							          edgeDistance: 10,
							          lineHeight: 15,
							          rich: {
							            time: {
							              fontSize: 10,
							              color: '#999'
							            }
							          }
							      },
							      left: '50%',
							      right: '25%',
							      top: 0,
							      bottom: 0
							    },
							    {
								      type: 'pie',
								      radius: '25%',
								      center: ['50%', '20%'],
								      data: datas4,
								      label: {
								    	  alignTo: 'edge',
								          formatter: '{{d}%}',
								          minMargin: 5,
								          edgeDistance: 10,
								          lineHeight: 15,
								          rich: {
								            time: {
								              fontSize: 10,
								              color: '#999'
								            }
								          }
								      },
								      left: '75%',
								      right: 0,
								      top: 0,
								      bottom: 0
								    }
							  ]
			};
	        // 使用刚指定的配置项和数据显示图表。
	        myChart.setOption(option);
	        myChart.on('click', function (param) {
	        	if(param && param.data && param.data.name){
	        		vm.showName = "";
	        		vm.showList = [];
	        		vm.openDeliveryRadio(param.data.name);
	        	}
	        });
	        vm.initDeliveryWidthTime();
		}
	};

	vm.exportMap = {};
	vm.openDeliveryRadio = function(name) {
		vm.showName = name;
		if(name == "交货正常"){
			vm.showList = vm.deliveryNomalList;
		}else if(name == "交货延期"){
			vm.showList = vm.deliveryOueList;
		}else if(name == "生产正常"){
			vm.showList = vm.produceNomalList;
		}else if(name == "生产延期"){
			vm.showList = vm.produceOueList;
		}else if(name == "提前生产"){
			vm.showList = vm.earyList;
		}else if(name == "市场交货延期"){
			vm.showList = vm.stockOueList;
		}else if(name == "正常生产"){
			vm.showList = vm.earyNomalList;
		}else if(name == "正常交货"){
			vm.showList = vm.stockNomalList;
		}else {
			vm.showList = [];
		}

		for(let i=0;i<vm.showList.length;i++){
			if(vm.showList[i].finalStockTimeStr){
				vm.showList[i].realNeedDays = vm.dealTimeDay(vm.showList[i].acDistributeDateStr,
						vm.showList[i].finalStockTimeStr);
			}else{
				vm.showList[i].realNeedDays = vm.dealTimeDay(vm.showList[i].acDistributeDateStr,
						"");
			}
		}
		// 导出使用
		vm.exportMap.name = name;
		vm.exportMap.exportData = JSON.stringify(vm.showList);
		$scope.$apply();
		if(name == "交货延期"){
			$("#openDeliveryOueRadio").modal();
		}else{
			$("#openDeliveryRadio").modal();
		}
	};
	
	vm.dealTimeDay = function(date1, date2) {
		var one = new Date();
		if(date1){
			one = new Date(date1)
		}
		var two = new Date();
		if(date2){
			two = new Date(date2);
		}
		var s1 = one.getTime(),s2 = two.getTime();
		var total = (s2 - s1)/1000;
		var day = parseInt(total / (24*60*60));
		return day;
	};
	
	vm.loadDeliveryOue = function(num) {
		if(num == 1){
			vm.showName = "交货延期";
			vm.showList = vm.deliveryOueList;
			for(let i=0;i<vm.showList.length;i++){
				if(vm.showList[i].status != '200204'){
					vm.showList[i].moreDays = vm.dealTimeDay(vm.showList[i].estimateDateStr,
							new Date());
				}else{
					vm.showList[i].moreDays = vm.dealTimeDay(vm.showList[i].estimateDateStr,
							vm.showList[i].finalDeliveryDateStr);
				}
			}
		}else if(num == 2){
			vm.showName = "生产延期";
			vm.showList = vm.deliveryOueProList;
			for(let i=0;i<vm.showList.length;i++){
				vm.showList[i].moreDays = vm.showList[i].realNeedDays - vm.showList[i].notiNeedDays;
			}
		}else if(num == 3){
			vm.showName = "采购延期";
			vm.showList = vm.deliveryOuePurList;
			for(let i=0;i<vm.showList.length;i++){
				if(vm.showList[i].acDistributeDate){
					vm.showList[i].moreDays = vm.dealTimeDay(vm.showList[i].endPurchDateTwoStr,
							vm.showList[i].acDistributeDateStr);
				}else{
					vm.showList[i].moreDays = vm.showList[i].oueDay.replace("天","");
				}
			}
		}else if(num == 4){
			vm.showName = "出库延期";
			vm.showList = vm.deliveryOueStoList;
			for(let i=0;i<vm.showList.length;i++){
				vm.showList[i].moreDays = vm.dealTimeDay(vm.showList[i].estimateDateStr,
						new Date());
			}
		}else if(num == 5){
			vm.showName = "市场延期";
			vm.showList = vm.deliveryOueSaleList;
			for(let i=0;i<vm.showList.length;i++){
				vm.showList[i].moreDays = vm.dealTimeDay(vm.showList[i].estimateDateStr,
					new Date());
			}
		}else if(num == 6){
			vm.showName = "工程延期";
			vm.showList = vm.deliveryOueCardList;
			for(let i=0;i<vm.showList.length;i++){
				vm.showList[i].moreDays = vm.dealTimeDay(vm.showList[i].estimateDateStr,
					new Date());
			}
		}
		for(let i=0;i<vm.showList.length;i++){
			vm.showList[i].moreDays = vm.showList[i].moreDays > 0?vm.showList[i].moreDays:0;
		}
		// 导出使用
		vm.exportMap.name = vm.showName;
		vm.exportMap.exportData = JSON.stringify(vm.showList);
		$("#openDeliveryRadio").modal();
	};
	
	vm.initDeliveryWidthTime = function() {
		var screenWidth = document.body.clientWidth;
		if(screenWidth != this.screenWidth){
			this.screenWidth = screenWidth;
			this.initDeliveryEcharts();
		}
		const _this = this;
		setTimeout(function () { _this.initDeliveryWidthTime() }, 500);
	}
	
    function loadData() {
    	MainCtrl.blockUI({
    		animate: true,
        });
    	vm.queryAll = CommonUtil.dataRangeIsAll("10913", BaseUtil.getMenuList());
     	upida.get("common/rightall?prefix=purch:rawmaterial").then(function(data){
	        vm.right.edit = data.edit;
	        vm.right.view = data.view;
	        vm.right.manage = data.manage;
	        vm.loadDeliveryRadioList();
	        MainCtrl.unblockUI();
     	});
    };
    vm.fomatFloat = function(num)
    {
    	var n = 2
    	var f = parseFloat(num);
	    if(!num || isNaN(f)){
	        return null;
	    }   
	    f = Math.round(num*Math.pow(10, n))/Math.pow(10, n); // n 幂   
	    var s = f.toString();
	    return vm.toQfw(s);
    };
	vm.toQfw = function(str){
		var re=/(?!^)(?=(\d{3})+(?:$|\.))/g;
		str=str.replace(re,",");
		return str;
	};
    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadData();
    });
}]);