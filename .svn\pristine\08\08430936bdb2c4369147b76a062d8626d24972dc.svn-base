package com.kyb.pcberp.modules.icloud.cycleSetup.pojo;

import com.kyb.pcberp.common.base.pojo.Icloud_BasePojo;

import java.math.BigDecimal;
import java.util.List;

public class Icloud_CycleSetup extends Icloud_BasePojo
{
    private static final long serialVersionUID = 1L;

    private String companyId;

    private String versionDate;

    private String name;

    private String countMethod;

    private Integer sortNum;

    private String status;

    private String productType;

    private String cardType;

    private String orderType;

    private String checked;

    private List<Icloud_CycleSetupDetail> list;

    private List<Icloud_CycleSetupColumn> columList;

    private String createdByName;

    private String countMethodVal;

    private String useFlag;

    private BigDecimal nomalDays;

    private BigDecimal setDays;

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    public String getVersionDate()
    {
        return versionDate;
    }

    public void setVersionDate(String versionDate)
    {
        this.versionDate = versionDate;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getCountMethod()
    {
        return countMethod;
    }

    public void setCountMethod(String countMethod)
    {
        this.countMethod = countMethod;
    }

    public Integer getSortNum()
    {
        return sortNum;
    }

    public void setSortNum(Integer sortNum)
    {
        this.sortNum = sortNum;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getProductType()
    {
        return productType;
    }

    public void setProductType(String productType)
    {
        this.productType = productType;
    }

    public String getCardType()
    {
        return cardType;
    }

    public void setCardType(String cardType)
    {
        this.cardType = cardType;
    }

    public String getOrderType()
    {
        return orderType;
    }

    public void setOrderType(String orderType)
    {
        this.orderType = orderType;
    }

    public String getChecked()
    {
        return checked;
    }

    public void setChecked(String checked)
    {
        this.checked = checked;
    }

    public List<Icloud_CycleSetupDetail> getList()
    {
        return list;
    }

    public void setList(List<Icloud_CycleSetupDetail> list)
    {
        this.list = list;
    }

    public List<Icloud_CycleSetupColumn> getColumList()
    {
        return columList;
    }

    public void setColumList(List<Icloud_CycleSetupColumn> columList)
    {
        this.columList = columList;
    }

    public String getCreatedByName()
    {
        return createdByName;
    }

    public void setCreatedByName(String createdByName)
    {
        this.createdByName = createdByName;
    }

    public String getCountMethodVal()
    {
        return countMethodVal;
    }

    public void setCountMethodVal(String countMethodVal)
    {
        this.countMethodVal = countMethodVal;
    }

    public String getUseFlag()
    {
        return useFlag;
    }

    public void setUseFlag(String useFlag)
    {
        this.useFlag = useFlag;
    }

    public BigDecimal getNomalDays()
    {
        return nomalDays;
    }

    public void setNomalDays(BigDecimal nomalDays)
    {
        this.nomalDays = nomalDays;
    }

    public BigDecimal getSetDays()
    {
        return setDays;
    }

    public void setSetDays(BigDecimal setDays)
    {
        this.setDays = setDays;
    }
}
