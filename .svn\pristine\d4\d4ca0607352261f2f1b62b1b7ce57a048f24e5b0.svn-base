/* Setup general page controller */
kybApp.controller('warehouseCtrl', ['$rootScope', '$scope', 'upida', '$timeout','$filter','CommonUtil','BaseUtil',function($rootScope, $scope, upida, $timeout,$filter,CommonUtil,BaseUtil) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        MainCtrl.initAjax();
        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });

    var vm = this;
    vm.focus = 1;
	$scope.shouldAutoStart = false;
    $scope.listOptions = {
        steps:[
        {
            element: '#listStep1',
            intro: "当您打开页面后会自动加载仓库信息至列表，修改某条仓库信息，可单击首列仓库名称或双击该行进入详情页！",
            position: 'top'
        },
        {
            element: '#listStep2',
            intro: "单击右边的排序按钮进行排序，箭头向上为升序，箭头向下为降序，若为上下则未排序！",
            position: 'bottom'
        },
        {
            element: '#listStep3',
            intro: "对仓库进行快速删除操作！",
            position: 'left'
        },
        {
            element: '#listStep4',
            intro: "此处为分页区域，您可以点击您想查看的页面！",
            position: 'left'
        },
        {
            element: '#listStep5',
            intro: "此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！",
            position: 'right'
        },
        {
            element: '#listStep6',
            intro: "此处为查询条件，可设置不同的条件，点击右下角的查询来过滤信息！",
            position: 'bottom'
        },
        {
            element: '#listStep7',
            intro: "操作按钮区域，可添加仓库信息！",
            position: 'left'
        },
        {
            element: '#listStep8',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
    
    $scope.readOnlyListOptions = {
            steps:[
            {
                element: '#listStep1',
                intro: "当您打开页面后会自动加载仓库信息至列表，查看某条仓库信息，可单击首列仓库名称或双击该行进入详情页！",
                position: 'top'
            },
            {
                element: '#listStep2',
                intro: "单击右边的排序按钮进行排序，箭头向上为升序，箭头向下为降序，若为上下则未排序！",
                position: 'bottom'
            },
            {
                element: '#listStep4',
                intro: "此处为分页区域，您可以点击您想查看的页面！",
                position: 'left'
            },
            {
                element: '#listStep5',
                intro: "此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！",
                position: 'right'
            },
            {
                element: '#listStep6',
                intro: "此处为查询条件，可设置不同的条件，点击右下角的查询来过滤信息！",
                position: 'bottom'
            },
            {
                element: '#listStep8',
                intro: '谢谢使用，再见。'
            }
            ],
            showStepNumbers: false,
            exitOnOverlayClick: true,
            exitOnEsc:true,
            nextLabel: '<strong>下一步!</strong>',
            prevLabel: '<span style="color:green">上一步</span>',
            skipLabel: '退出',
            doneLabel: '谢谢'
        };

    $scope.infoOptions = {
        steps:[
        {
            element: '#infoStep1',
            intro: "基本信息录入，带 * 的为必填项！",
            position: 'top'
        },
        {
            element: '#infoStep2',
            intro: "您输入完成后，请点击保存！",
            position: 'top'
        },
        {
            element: '#infoStep3',
            intro: '谢谢使用，再见。'
        }],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
    
    $scope.help = function(){
    	if (vm.tabs.viewForm.active){
    		if (vm.right.edit){
    			$scope.helpList();
    		} else {
    			$scope.helpReadOnlyList();
    		}
    	} else {
    		if (vm.right.edit) {
    			$scope.helpInfo();
    		} 
    	}
    };
    
    vm.queryAll = false;
    
    // tj 判断是否多次点击
    vm.clicks = true;
    // 分页数据
    vm.page = {};
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    // 字典项分页数据
    vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "stock/warehouse/page";
    vm.page.condition = []; // 条件

    // 查询条件参数
    vm.query = {}; // 查询对象

    vm.query.name = {};
    vm.query.name.name = "name";
    vm.query.name.value = "";
    vm.query.location = {};
    vm.query.location.name = "location";
    vm.query.location.value = "";
    
    // 销售仓库分页数据
    vm.pageSales = {};
    vm.pageSales.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.pageSales.pageSize = 10;
    vm.pageSales.pageNo = 1;
    vm.pageSales.url = "stock/warehouse/pageSales";
    vm.pageSales.condition = []; // 条件
    
    //销售仓库查询条件
    vm.query.salesName = {};
    vm.query.salesName.name = "name";
    vm.query.salesName.value = "";
    vm.query.salesLocation = {};
    vm.query.salesLocation.name = "location";
    vm.query.salesLocation.value = "";
    
    vm.salesFlag = false;//控制添加仓库时的仓库类型
    
    // 共享仓库分页数据
    vm.pageShare = {};
    vm.pageShare.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.pageShare.pageSize = 10;
    vm.pageShare.pageNo = 1;
    vm.pageShare.url = "stock/warehouse/pageShare";
    vm.pageShare.condition = []; // 条件
    
    
    //共享仓库查询条件
    vm.query.shareName = {};
    vm.query.shareName.name = "name";
    vm.query.shareName.value = "";
    
    vm.query.shareCompany = {};
    vm.query.shareCompany.name = "shareCompany.name";
    vm.query.shareCompany.value = "";
    
    vm.query.shareUser = {};
    vm.query.shareUser.name = "user.userName";
    vm.query.shareUser.value = "";
    
    // 共享物料分页数据
    vm.pageShareMat = {};
    vm.pageShareMat.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.pageShareMat.pageSize = 10;
    vm.pageShareMat.pageNo = 1;
    vm.pageShareMat.url = "stock/warehouse/pageShareMat";
    vm.pageShareMat.condition = []; // 条件
    
    
    //共享物料查询条件
    vm.query.materialNo = {};
    vm.query.materialNo.name = "no";
    vm.query.materialNo.value = "";
    
    vm.query.materialName = {};
    vm.query.materialName.name = "name";
    vm.query.materialName.value = "";
    
    // 分配人员分页数据
    vm.pageShareUser = {};
    vm.pageShareUser.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.pageShareUser.pageSize = 10;
    vm.pageShareUser.pageNo = 1;
    vm.pageShareUser.url = "stock/warehouse/pageShareUser";
    vm.pageShareUser.condition = []; // 条件
    
    //分配人员查询条件
    vm.query.userCode = {};
    vm.query.userCode.name = "userCode";
    vm.query.userCode.value = "";
    
    vm.query.userName = {};
    vm.query.userName.name = "userName";
    vm.query.userName.value = "";
    
    // 排序字段
    vm.query.sort = {};
    vm.query.sort.name = "orderBy";
    vm.query.sort.value = "createdDate DESC";

    vm.query.materialKindList = [
    	{
    		recordId: "",
    		name: "所有"
    	},
    	{
    		recordId: "100701",
    		name: "原料"
    	},
    	{
    		recordId: "100702",
    		name: "成品"
    	},
    ];
    
    
    // tabs控制
    vm.tabs = {
        viewForm: {active:true},
        editForm: {active:false, show: false},
        salesViewForm: {active:false},
        editSalesForm: {active:false, show: false},
        shareViewForm: {active:false},
        editShareForm: {active:false, show: false}
    };

    // 权限
    vm.right = {};
    // 编辑操作对象
    vm.whouse = {};
    // 删除主键
    vm.delRecordId= -1;
    // 修改标题
    vm.editTitle = "";
    // 角色
    vm.roles = [];

    vm.setFormScope= function(scope){
        vm.formScope = scope;
    };

    // 显示编辑
    vm.showEditForm = function () {
        vm.tabs.editForm.show = true;
        $timeout(function(){
            vm.tabs.editForm.active = true;
            vm.tabs.viewForm.active = true;
        });
        vm.nameMessage="";
        vm.saveflag=false;
    };

    // 隐藏编辑
    vm.hideEditForm = function () {
        vm.formScope.whouse_form.$setPristine();
        vm.tabs.editForm.show = false;
        $timeout(function(){
            vm.tabs.viewForm.active = true;
        });
    };

    // 销售仓库显示编辑
    vm.showSalesEditForm = function () {
        vm.tabs.editSalesForm.show = true;
        $timeout(function(){
            vm.tabs.editSalesForm.active = true;
            vm.tabs.salesViewForm.active = true;
        });
        vm.nameMessage="";
        vm.saveflag=false;
    };

    // 销售仓库隐藏编辑
    vm.hideSalesEditForm = function () {
        vm.formScope.whouse_salesForm.$setPristine();
        vm.tabs.editSalesForm.show = false;
        $timeout(function(){
            vm.tabs.salesViewForm.active = true;
        });
    };
    
    // 共享仓库显示编辑
    vm.showShareEditForm = function () {
        vm.tabs.editShareForm.show = true;
        $timeout(function(){
            vm.tabs.editShareForm.active = true;
            vm.tabs.shareViewForm.active = true;
        });
        vm.nameMessage="";
        vm.saveflag=false;
    };

    // 共享仓库隐藏编辑
    vm.hideShareEditForm = function () {
//        vm.formScope.whouse_salesForm.$setPristine();
        vm.tabs.editShareForm.show = false;
        $timeout(function(){
            vm.tabs.shareViewForm.active = true;
        });
    };
    
    // 分页按钮单击处理
    vm.doPage = function(page, pageSize, total){
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        vm.init(page, pageSize, vm.page.condition, vm.page.url);
    };
    
    //销售仓库分页按钮单击处理
    vm.doSalesPage = function(page, pageSize, total){
        vm.pageSales.pageNo = page;
        vm.pageSales.pageSize = pageSize;
        vm.init(page, pageSize, vm.pageSales.condition, vm.pageSales.url);
    };
    
    //共享仓库分页按钮单击处理
    vm.doSharePage = function(page, pageSize, total){
        vm.pageShare.pageNo = page;
        vm.pageShare.pageSize = pageSize;
        vm.init(page, pageSize, vm.pageShare.condition, vm.pageShare.url);
    };
    
    //共享物料分页按钮单击处理
    vm.doShareMatPage = function(page, pageSize, total){
        vm.pageShareMat.pageNo = page;
        vm.pageShareMat.pageSize = pageSize;
        vm.init(page, pageSize, vm.pageShareMat.condition, vm.pageShareMat.url);
    };
    
    //分配用户分页按钮单击处理
    vm.doShareUserPage = function(page, pageSize, total){
        vm.pageShareUser.pageNo = page;
        vm.pageShareUser.pageSize = pageSize;
        vm.init(page, pageSize, vm.pageShareUser.condition, vm.pageShareUser.url);
    };
    
    // 默认排序按时间排序排序处理
    vm.sort = {};
    vm.sort.name = {both: true, desc: false, asc: false};
    vm.sort.createdDate = {both: false, desc: true, asc: false};
    vm.sortClick = function(col){
        vm.sort[col].both = false;
        if (!vm.sort[col].desc && !vm.sort[col].asc){
            vm.sort[col].asc = true;
        }else {
            if (vm.sort[col].asc){
                vm.sort[col].desc = true;
                vm.sort[col].asc = false;
            }else {
                vm.sort[col].desc = false;
                vm.sort[col].asc = true;
            }
        }

        for(var p in vm.sort){
            if (p !== col){
                vm.sort[p].desc = false;
                vm.sort[p].asc = false;
                vm.sort[p].both = true;
            }
        }

        vm.query.sort.value = vm.sort[col].asc ? col + " ASC" :  col + " DESC";
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    };
    /*
     * 兼容条件查询与普通查询
     * no 当前页
     * size 当前页显示数量
     * condition 是数组，可放入多个条件对象，如{name:"type", value:"1"}  名称对应实体字段名称，值对应页面输入的值
     * url 请求数据链接
     */
    vm.init = function(no, size, condition, url){
        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;
        condition.push({
            name: vm.query.sort.name,
            value: vm.query.sort.value
        });
        // 设置过滤条件
        if (condition.length > 0)
        {
            angular.forEach(condition, function(p){
                reqData[p.name] = p.value;
            });
        }
        MainCtrl.blockUI({
    	    animate: true,
    	});
        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};
            // 如果结果为空
            if (typeof result === 'undefined' || typeof result.list === 'undefined')
            {

                data.pageNo = 1;
                data.pageSize = 10;
                data.list = [];
                data.startCount = 0;
                data.endCount = 0;
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }

            // 仓库列表
            if(url == vm.page.url)
            {
            	vm.page.data = data;
            }
            //销售仓库列表
            else if(url == vm.pageSales.url)
            {
            	vm.pageSales.data = data;
            }
            // 共享仓库列表
            else if(url == vm.pageShare.url)
            {
            	vm.pageShare.data = data;
            }
            //共享物料列表
            else if(url == vm.pageShareMat.url)
            {
            	vm.pageShareMat.data = data;
            }
            //分配人员列表
            else if(url == vm.pageShareUser.url)
            {
            	vm.pageShareUser.data = data;
            }
            MainCtrl.unblockUI();
        });
    };

    // 页面显示数量改变
    vm.pageSizeChange = function(){
        vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url);
    };
    
    // 销售仓库页面显示数量改变
    vm.pageSizeChangeSales = function(){
        vm.init(1, vm.pageSales.pageSize, vm.pageSales.condition, vm.pageSales.url);
    };
    
    // 共享仓库页面显示数量改变
    vm.pageSizeChangeShare = function(){
        vm.init(1, vm.pageShare.pageSize, vm.pageShare.condition, vm.pageShare.url);
    };
    
    // 共享物料页面显示数量改变
    vm.pageSizeChangeShareMat = function(){
        vm.init(1, vm.pageShareMat.pageSize, vm.pageShareMat.condition, vm.pageShareMat.url);
    };

    // 分配用户页面显示数量改变
    vm.pageSizeChangeShareUser = function(){
        vm.init(1, vm.pageShareUser.pageSize, vm.pageShareUser.condition, vm.pageShareUser.url);
    };
    
    vm.finalQuery = "1";
    
    // 查询数据
    vm.doQuery = function(){
        // 设置查询条件
        var condition = [];

        if (vm.query.name.value !== "")
        {
            condition.push({
                name: vm.query.name.name,
                value: vm.query.name.value
            });
        }

        if (vm.query.location.value !== "")
        {
            condition.push({
                name: vm.query.location.name,
                value: vm.query.location.value
            });
        }
        
        if (vm.finalQuery !== "")
        {
            condition.push({
                name: "finalQuery",
                value: vm.finalQuery
            });
        }

        vm.page.pageNo = 1;
        vm.page.condition = condition;

        // 查询数据
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    };
    
    // 销售仓库查询数据
    vm.doSalesQuery = function(){
        // 设置查询条件
        var condition = [];

        if (vm.query.salesName.value !== "")
        {
            condition.push({
                name: vm.query.salesName.name,
                value: vm.query.salesName.value
            });
        }

        if (vm.query.salesLocation.value !== "")
        {
            condition.push({
                name: vm.query.salesLocation.name,
                value: vm.query.salesLocation.value
            });
        }

        vm.pageSales.pageNo = 1;
        vm.pageSales.condition = condition;

        // 查询数据
        vm.init(vm.pageSales.pageNo, vm.pageSales.pageSize, vm.pageSales.condition, vm.pageSales.url);
    };
    
    // 共享仓库查询数据
    vm.doShareQuery = function(){
    	
        // 设置查询条件
        var condition = [];

        //仓库名称
        var storeHouse = {};
        if (vm.query.shareName.value !== "")
        {
        	storeHouse.name = vm.query.shareName.value;
            condition.push({
                name: 'storeHouse',
                value: storeHouse
            });
        }

        //公司名称
        var shareCompany={};
        if (vm.query.shareCompany.value !== "")
        {
        	shareCompany.name = vm.query.shareCompany.value;
            condition.push({
                name: 'shareCompany',
                value: shareCompany
            });
        }
        
        //分配人
        var user={};
        if (vm.query.shareUser.value !== "")
        {
        	user.userName = vm.query.shareUser.value;
            condition.push({
                name: 'user',
                value: user
            });
        }

        vm.pageShare.pageNo = 1;
        vm.pageShare.condition = condition;

        // 查询数据
        vm.init(vm.pageShare.pageNo, vm.pageShare.pageSize, vm.pageShare.condition, vm.pageShare.url);
    };
    
    // 共享物料查询数据
    vm.doShareMatQuery = function(){
    	
        // 设置查询条件
        var condition = [];
        
        condition.push({
        	name:'salesStoreHouse',
        	value:vm.shareHouse.storeHouse
        });
        
        condition.push({
        	name:'company',
        	value:vm.shareHouse.shareCompany
        });
        
        condition.push({
        	name:'shareCompany',
        	value:vm.shareHouse.company
        });

        //物料编号
        if (vm.query.materialNo.value !== "")
        {
            condition.push({
                name: vm.query.materialNo.name,
                value: vm.query.materialNo.value
            });
        }

        //物料名称
        if (vm.query.materialName.value !== "")
        {
            condition.push({
                name: vm.query.materialName.name,
                value: vm.query.materialName.value
            });
        }
        
        vm.pageShareMat.pageNo = 1;
        vm.pageShareMat.condition = condition;

        // 查询数据
        vm.init(vm.pageShareMat.pageNo, vm.pageShareMat.pageSize, vm.pageShareMat.condition, vm.pageShareMat.url);
    };
    
    // 分配用户查询数据
    vm.doShareMatQuery = function(){
    	
        // 设置查询条件
        var condition = [];
        
        condition.push({
        	name:'shareHouseId',
        	value:vm.shareHouse.recordId
        });
        
        //用户名
        if (vm.query.userCode.value !== "")
        {
            condition.push({
                name: vm.query.userCode.name,
                value: vm.query.userCode.value
            });
        }

        //姓名
        if (vm.query.userName.value !== "")
        {
            condition.push({
                name: vm.query.userName.name,
                value: vm.query.userName.value
            });
        }
        
        vm.pageShareUser.pageNo = 1;
        vm.pageShareUser.condition = condition;

        // 查询数据
        vm.init(vm.pageShareUser.pageNo, vm.pageShareUser.pageSize, vm.pageShareUser.condition, vm.pageShareUser.url);
    };
    
    vm.reset=function(){
        vm.query.name.value = "";
        vm.query.location.value = "";

    };
    // 单击修改
    vm.modStoreHouse = function(index){
    	
        vm.editTitle = vm.right.edit ? "修改" : "查看";
        vm.focus += 1;
        vm.whouse = angular.copy(vm.page.data.list[index]);
        vm.loginUser = vm.whouse.applicanter;
        vm.showEditForm();

    };
    
    // 销售仓库单击修改
    vm.modSalesStoreHouse = function(index){
        vm.editTitle = vm.right.edit ? "修改" : "查看";
        vm.focus += 1;
        vm.whouse = angular.copy(vm.pageSales.data.list[index]);
        vm.loginUser = vm.whouse.applicanter;
        vm.showSalesEditForm();

    };
    
    // 共享仓库单击修改
    vm.shareUserList = [];//分配用户列表
    vm.shareMatList = [];//共享物料列表
    vm.modShareStoreHouse = function(index){
        vm.shareHouse = angular.copy(vm.pageShare.data.list[index]);
        
        //加载共享物料列表
        
        // 设置查询条件
        var conditionMat = [];
        
        conditionMat.push({
        	name:'salesStoreHouse',
        	value:vm.shareHouse.storeHouse
        });
        
        conditionMat.push({
        	name:'company',
        	value:vm.shareHouse.shareCompany
        });
        
        conditionMat.push({
        	name:'shareCompany',
        	value:vm.shareHouse.company
        });

        vm.pageShareMat.pageNo = 1;
        vm.pageShareMat.condition = conditionMat;

        // 查询数据
        vm.init(vm.pageShareMat.pageNo, vm.pageShareMat.pageSize, vm.pageShareMat.condition, vm.pageShareMat.url);
        
        //加载分配用户列表
        
        // 设置查询条件
        var conditionUser = [];
        
        conditionUser.push({
        	name:'shareHouseId',
        	value:vm.shareHouse.recordId
        });
        
        vm.pageShareUser.pageNo = 1;
        vm.pageShareUser.condition = conditionUser;

        // 查询数据
        vm.init(vm.pageShareUser.pageNo, vm.pageShareUser.pageSize, vm.pageShareUser.condition, vm.pageShareUser.url);
        
        vm.showShareEditForm();
    };

    //名称焦点离开时触发
    vm.nameBlur=function(){
        vm.saveflag=false;
        if(typeof vm.whouse.name !=="undefined"){
    		var link = (typeof vm.whouse.recordId !=="undefined") ? "stock/warehouse/querySameName?recordId="+vm.whouse.recordId+"&name="+vm.whouse.name : "stock/warehouse/querySameName?name="+vm.whouse.name;
    		MainCtrl.blockUI({
        	    animate: true,
        	});
    		upida.get(link).then(function (data) {
    			vm.saveflag = data>0;
    			MainCtrl.unblockUI();
        	});
    	}
    };

    // 单击添加
    vm.addStoreHouse = function(){
        vm.whouse = {};
        vm.editTitle = "添加";
        vm.showEditForm();
        vm.focus += 1;
        vm.loginUser = BaseUtil.getUser();
    };
    
    // 销售仓库单击添加
    vm.addSalesStoreHouse = function(){
        vm.whouse = {};
        vm.whouse.type = 3;
        vm.editTitle = "添加";
        vm.showSalesEditForm();
        vm.focus += 1;
        vm.loginUser = BaseUtil.getUser();
    };

    // 单击取消
    vm.cacelBtn = function(form){
        form.$setPristine();
        vm.hideEditForm();
    };
    
    // 创建和修改仓库
    vm.submitMod = function(form){
    	if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    
    	    form.$setDirty();
            if(!form.$valid){
            	vm.clicks = true;// tj 2017-09-07  用户是否多次点击
                return;
            }
          
            MainCtrl.blockUI({
        	    animate: true,
        	});
            
            upida.post("stock/warehouse/save", vm.whouse).then(function (data) {
            	if (!data.result)
            	{
            		vm.message = data.message;
                    $('#static').modal();
                    MainCtrl.unblockUI();
            	} else {
            		MainCtrl.unblockUI();
            		// 重新查询数据
            		vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            		
            		// 初始化数据
            		vm.whouse = {};
            		vm.hideEditForm();
            		vm.saveflag=false;
            		
            		// 提示信息
            		vm.message = data.message;
                    $('#static').modal();
            	}
            	vm.clicks = true;// tj 2017-09-07  用户是否多次点击
            });
    	}
    };
    
    // 创建和修改销售仓库
    vm.submitSalesMod = function(form){
    	if(vm.clicks){
    	    vm.clicks = false; 
    	    
    	    form.$setDirty();
            if(!form.$valid){
            	vm.clicks = true;
                return;
            }
          
            MainCtrl.blockUI({
        	    animate: true,
        	});
            
            upida.post("stock/warehouse/save", vm.whouse).then(function (data) {
            	if (!data.result)
            	{
            		vm.message = data.message;
                    $('#static').modal();
                    MainCtrl.unblockUI();
            	} else {
            		MainCtrl.unblockUI();
            		// 重新查询数据
            		vm.init(vm.pageSales.pageNo, vm.pageSales.pageSize, vm.pageSales.condition, vm.pageSales.url);
            		
            		// 初始化数据
            		vm.whouse = {};
            		vm.hideSalesEditForm();
            		vm.saveflag=false;
            		
            		// 提示信息
            		vm.message = data.message;
                    $('#static').modal();
            	}
            	vm.clicks = true;// tj 2017-09-07  用户是否多次点击
            });
    	}
    };

    // 删除仓库
    vm.delStoreHouse = function(index){
        vm.delRecordId = vm.page.data.list[index].recordId;
        vm.delwhouseName=vm.page.data.list[index].name;
        // 提示信息
        $('#staticRemove').modal();
    };

    // 删除销售仓库
    vm.delSalesStoreHouse = function(index){
        vm.delRecordId = vm.pageSales.data.list[index].recordId;
        vm.delwhouseName=vm.pageSales.data.list[index].name;
        
        if(vm.pageSales.data.list[index].materialInternetFlag)
    	{
    		vm.materialInternetFlag = vm.delwhouseName+"在物联网设置了金额,";
    	}
    	else
    	{
    		vm.materialInternetFlag="";
    	}
        
        // 提示信息
        $('#staticSalesRemove').modal();
    };
    
    // 做仓库的删除操作
    vm.doDelhouse = function(){
    	if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    
    	    MainCtrl.blockUI({
        	    animate: true,
        	});
            upida.post("stock/warehouse/delete/" + vm.delRecordId, null).then(function (data) {
            	if(data){
            		// 重新查询数据
            		vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            		vm.delRecordId= -1;
            		vm.message = "删除仓库成功！";
            		$('#static').modal();
            	}else{
            		vm.message = "亲，仓库里面堆满了物料,不能删除！";
            		MainCtrl.unblockUI();
            		$('#static').modal();
            	}
            	vm.clicks = true;// tj 2017-09-07  用户是否多次点击
            });
    	}
    };
    
    // 做销售仓库的删除操作
    vm.doDelSalesHouse = function(){
    	if(vm.clicks){
    	    vm.clicks = false;
    	    
    	    MainCtrl.blockUI({
        	    animate: true,
        	});
            upida.post("stock/warehouse/deleteSales/" + vm.delRecordId, null).then(function (data) {
            	if(data == "failDelete")
            	{
            		vm.message = "该销售仓库已经被删除，请刷新重试!";
            		$('#static').modal();
            	}
            	else if(data == "success")
            	{
            		// 重新查询数据
            		vm.init(vm.pageSales.pageNo, vm.pageSales.pageSize, vm.pageSales.condition, vm.pageSales.url);
            		vm.delRecordId= -1;
            		vm.message = "删除销售仓库成功！";
            		$('#static').modal();
            	}
            	else
            	{
            		vm.message = "系统错误，请联系管理员!";
            		$('#static').modal();
            	}
            	MainCtrl.unblockUI();
            	vm.clicks = true;
            });
    	}
    };
    //一键设置销售仓库
    vm.SetUpSalesWarehouse = function(index)
    {
        vm.whouse = angular.copy(vm.pageSales.data.list[index]);

        vm.delwhouseName="您确定要把未放入销售仓库的原料一键设置加入"+vm.whouse.name+"吗？";
        $('#staticInstallShare').modal();
    }

    //设置销售仓库共享可见不可见
    vm.installShare = function(index)
    {
    	vm.whouse = angular.copy(vm.pageSales.data.list[index]);
    	
    	var shareStr = "";
    	if(vm.whouse.shareFlag)
    	{
    		shareStr = "是";
    	}
    	else
    	{
    		shareStr = "否";
    	}
    	vm.delwhouseName="您确定要把"+vm.whouse.name+"共享设置为"+shareStr+"?";
    	$('#staticInstallShare').modal();
    }
    
    vm.installShareWareHouse = function()
    {
    	if(vm.clicks){
    	    vm.clicks = false;
    	    
    	    MainCtrl.blockUI({
        	    animate: true,
        	});
    	    
    	    vm.whouse.shareFlag = !vm.whouse.shareFlag;
            upida.post("stock/warehouse/installShareWareHouse",vm.whouse).then(function (data) {
            	if(data == "fail")
            	{
            		vm.message = "参数传递错误！";
            		$('#static').modal();
            	}
            	else if(data == "failDelete")
            	{
            		vm.message = "销售仓库已经被删除！";
            		$('#static').modal();
            	}
//            	else if(data == "failShareMaterial")
//            	{
//            		vm.message = "销售仓库的某个物料已经添加过物料共享，请先删除物料共享记录才能进行修改！";
//            		$('#static').modal();
//            	}
//            	else if(data == "failShareStoreHouse")
//            	{
//            		vm.message = "销售仓库已经添加过物料共享，请先删除物料共享才能进行修改！";
//            		$('#static').modal();
//            	}
            	else if(data == "success")
            	{
            		// 重新查询数据
            		vm.init(vm.pageSales.pageNo, vm.pageSales.pageSize, vm.pageSales.condition, vm.pageSales.url);
            		vm.message = "设置销售仓库成功！";
            		$('#static').modal();
            		vm.whouse = {};
            	}
            	else
            	{
            		vm.message = "系统错误，请联系管理员";
            		$('#static').modal();
            	}
            	MainCtrl.unblockUI();
            	vm.clicks = true;
            });
    	}
    }
    
    //设置默认仓库
    vm.installDefault = function(index)
    {
    	vm.whouse = angular.copy(vm.page.data.list[index]);
    	
    	if(!vm.whouse.defaultFlag)
    	{
    		vm.delwhouseName="您确定要把"+vm.whouse.name+"设置为默认仓库？";
    	}
    	else
    	{
    		vm.delwhouseName="您确定要把"+vm.whouse.name+"取消为默认仓库？";
    	}
    	$('#staticInstallDefault').modal();
    }
    
    vm.installDefaultWareHouse = function()
    {
    	if(vm.clicks)
    	{
    	    vm.clicks = false;
    	    
    	    MainCtrl.blockUI({
        	    animate: true,
        	});
    	    vm.whouse.defaultFlag = !vm.whouse.defaultFlag;
    	    
    	    if(vm.whouse.defaultFlag)
    	    {
    	    	for(var i=0;i<vm.page.data.list.length;i++)
    	    	{
    	    		if(vm.page.data.list[i].defaultFlag)
    	    		{
    	    			vm.message = "亲，只能设置一个默认仓库！";
                		$('#static').modal();
                		return;
    	    		}
    	    	}
    	    }
    	    
            upida.post("stock/warehouse/installDefaultWareHouse",vm.whouse).then(function (data) {
            	if(data == "fail")
            	{
            		vm.message = "参数传递错误！";
            		$('#static').modal();
            	}
            	else if(data == "failDelete")
            	{
            		vm.message = "仓库已经被删除！";
            		$('#static').modal();
            	}
            	else if(data == "success")
            	{
            		// 重新查询数据
            		vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            		vm.message = "设置默认仓库成功！";
            		$('#static').modal();
            		vm.whouse = {};
            	}
            	else
            	{
            		vm.message = "系统错误，请联系管理员";
            		$('#static').modal();
            	}
            	MainCtrl.unblockUI();
            	vm.clicks = true;
            });
    	}
    }
    
    //zjn 2018-08-20 一键设置原料入销售仓
    vm.setUpSales = function(index)
    {
    	vm.whouse = angular.copy(vm.pageSales.data.list[index]);
    	vm.delwhouseName="您确定要把原料一键设置在"+vm.whouse.name+"？";
    	$("#staticSetUpSales").modal();
    }
    
    vm.setUpSalesWarehouse = function()
    {
    	if(vm.clicks)
    	{
    		vm.clicks = false;
    		
    		MainCtrl.blockUI({
        	    animate: true,
        	});
    		
    		upida.post("stock/warehouse/setUpSalesWarehouse",vm.whouse).then(function(data){
    			if(data == "fail")
            	{
            		vm.message = "参数传递错误！";
            		$('#static').modal();
            	}
            	else if(data == "failDelete")
            	{
            		vm.message = "销售仓库已经被删除！";
            		$('#static').modal();
            	}
            	else if(data == "failNoMaterialList")
            	{
            		vm.message = "系统没有找到未放入销售仓库的原料！";
            		$('#static').modal();
            	}
            	else if(data == "success")
            	{
            		// 重新查询数据
            		vm.init(vm.pageSales.pageNo, vm.pageSales.pageSize, vm.pageSales.condition, vm.pageSales.url);
            		vm.message = "一键设置原料入销售仓库成功！";
            		$('#static').modal();
            		vm.whouse = {};
            	}
            	else
            	{
            		vm.message = "系统错误，请联系管理员";
            		$('#static').modal();
            	}
            	MainCtrl.unblockUI();
            	vm.clicks = true;
    		});
    	}
    }
    
    vm.showSalesPage = function()
    {
    	 // 销售仓库初始化第一页，条件为空
        vm.pageSales.pageNo = 1;
        vm.init(vm.pageSales.pageNo, vm.pageSales.pageSize, [], vm.pageSales.url);
    }
    
    // zjn 2019-03-14 获取共享仓库
    vm.showSharePage = function()
    {
    	upida.get("stock/warehouse/updateShareHouse").then(function(data){
       	 	// 共享仓库初始化第一页，条件为空
            vm.pageShare.pageNo = 1;
            vm.init(vm.pageShare.pageNo, vm.pageShare.pageSize, [], vm.pageShare.url);
    	});
    }
    
    vm.shareHouse={};// 共享仓库对象
    
    //设置时弹出提示
    vm.openUpdateShareHouse = function(obj,num)
    {
    	vm.shareHouse = angular.copy(obj);
    	//自动化
    	if(num == 1)
    	{
    		vm.delwhouseName="您确定把共享仓库"+vm.shareHouse.storeHouse.name+"自动化领用？";
    	}
    	//取消自动化
    	else if(num == 2)
    	{
    		vm.delwhouseName="您确定把共享仓库"+vm.shareHouse.storeHouse.name+"取消自动化领用？";
    	}
    	$("#staticUpdateShareHouse").modal();
    }
    
    //zjn 2019-03-12 设置共享仓库自动领用
    vm.confirmShareHouse = function()
    {
    	if(vm.clicks)
    	{
    		vm.clicks = false;
    		vm.shareHouse.status = "1002";
    		
    		MainCtrl.blockUI({
        	    animate: true,
        	});
        	upida.post("stock/warehouse/updateShareStaus",vm.shareHouse).then(function(data){
        		if(data == "fail")
        		{
            		vm.message = "系统错误，请联系管理员";
            		$('#static').modal();
        		}
        		else if(data == "equalFail")
        		{
            		vm.message = "该仓库已经被设置为自动领用，请刷新页面重试!";
            		$('#static').modal();
        		}
        		else if(data == "success")
        		{
            		// 重新查询数据
        			$('#staticUpdateShareHouse').modal('hide')
        			vm.init(vm.pageShare.pageNo, vm.pageShare.pageSize, vm.pageShare.condition, vm.pageShare.url);
            		vm.message = "自动领用设置成功！";
            		$('#static').modal();
            		vm.shareHouse = {};
        		}
            	MainCtrl.unblockUI();
            	vm.clicks = true;
        	});
    	}
    };
    
    //zjn 2019-03-12 设置共享仓库取消自动领用
    vm.cancleShareHouse = function()
    {
    	if(vm.clicks)
    	{
    		vm.clicks = false;
    		vm.shareHouse.status = "1001";
    		MainCtrl.blockUI({
        	    animate: true,
        	});
        	upida.post("stock/warehouse/updateShareStaus",vm.shareHouse).then(function(data){
        		if(data == "fail")
        		{
            		vm.message = "系统错误，请联系管理员";
            		$('#static').modal();
        		}
        		else if(data == "equalFail")
        		{
            		vm.message = "该仓库已经取消自动领用，请刷新页面重试!";
            		$('#static').modal();
        		}
        		else if(data == "success")
        		{
            		// 重新查询数据
        			$('#staticUpdateShareHouse').modal('hide')
        			vm.init(vm.pageShare.pageNo, vm.pageShare.pageSize, vm.pageShare.condition, vm.pageShare.url);
            		vm.message = "取消自动领用设置成功！";
            		$('#static').modal();
            		vm.shareHouse = {};
        		}
            	MainCtrl.unblockUI();
            	vm.clicks = true;
        	});
    	}
    }
    
    // zjn 2019-03-12 分配人
    
    vm.userList = [];//所有用户
    vm.userListCopy = [];
    vm.allotUser = function(index)
    {
    	if(vm.clicks)
    	{
    		vm.clicks = false;
    		
    		vm.shareHouse = angular.copy(vm.pageShare.data.list[index]);
    		
    		//初始化26个字母筛选
    		initChar();
    		
    		MainCtrl.blockUI({
        		animate: true,
        	});
    		// 加载所有用户
    		upida.get("sys/user/showall?queryAll=" + vm.queryAll).then(function (data)
    		{
    			vm.userList = data;
    			vm.userListCopy = data;
    			vm.showUserSet();
    			vm.clicks = true;
    			MainCtrl.unblockUI();
    		});
    	}
    }
    
	// 初始化字母
	vm.userChar = [];
	function initChar()
	{
		vm.userChar = [];
		vm.userChar = [
          {'name':"所有","flag":true},{'name':"A","flag":false},{'name':"B","flag":false},{'name':"C","flag":false},{'name':"D","flag":false},{'name':"E","flag":false},{'name':"F","flag":false},{'name':"G","flag":false},
          {'name':"H","flag":false},{'name':"I","flag":false},{'name':"J","flag":false},{'name':"K","flag":false},{'name':"L","flag":false},{'name':"M","flag":false},{'name':"N","flag":false},
          {'name':"O","flag":false},{'name':"P","flag":false},{'name':"Q","flag":false},{'name':"R","flag":false},{'name':"S","flag":false},{'name':"T","flag":false},{'name':"U","flag":false},
          {'name':"V","flag":false},{'name':"W","flag":false},{'name':"X","flag":false},{'name':"Y","flag":false},{'name':"Z","flag":false}
		]
	}
	
	// 显示用户分配设置窗口
	vm.showUserSet = function ()
	{
		var list = [];
		
		//从所有用户中获取所有员工用户(userType:0 员工 ，1 客户)
		angular.forEach(vm.userListCopy,function(p){
			if(p.userType == "0")
			{
				list.push(p);
			}
		});
		vm.userList = list;
		
		// 初始化所有用户选择项
		angular.forEach(vm.userList,function(p){
			p.check = false;
		});
		
		vm.tempRoleStr = $filter('userNameFilter')("",vm.userList);
		$('#staticRole').modal();
	};
    
	// 选择首字母过滤用户
	vm.getUserByChar = function(name)
	{
		MainCtrl.blockUI({
    	    animate: true,
    	});
		angular.forEach(vm.userChar, function (p)
		{
		    if(p.name == name)
		    {
		    	p.flag = true;
		    }
		    else
		    {
		    	p.flag = false;
		    }
		});
		
		vm.userList = [];
		var user = {};
		user.userChar = name;
		user.queryAll = vm.queryAll;
		upida.post("sys/user/showallByChar",user).then(function (data){
			vm.userList = data;
			vm.userListCopy = data;
			vm.showUserSet();
			MainCtrl.unblockUI();
		});
	}
	
	//保存用户关联共享仓库
	vm.saveUserHouse = function()
	{
		if(vm.clicks)
		{
			vm.clicks = false;
			
			//获取选中的用户
			var list = [];
			angular.forEach(vm.userList,function(p){
				if(p.check)
				{
					list.push(p);
				}
			});
			vm.shareHouse.userList = list;
			
			MainCtrl.blockUI({
	    	    animate: true,
	    	});
			upida.post("stock/warehouse/saveUserHouse",vm.shareHouse).then(function(data){
				if(data.result == "success")
				{
            		vm.message = data.message;
            		$('#static').modal();
            		vm.shareHouse = {};
				}
				else if(data.result == "fail")
				{
            		vm.message = data.message;
            		$('#static').modal();
				}
				else
				{
            		vm.message = "系统错误，请联系管理员";
            		$('#static').modal();
				}
				vm.clicks = true;
				MainCtrl.unblockUI();
			});
		}
	}
	
	// zjn 2019-03-14  移除用户提示弹窗
	vm.delShareUserOpen = function(index)
	{
		var user = angular.copy(vm.pageShareUser.data.list[index]);
		vm.shareHouse.user = user;
		vm.delwhouseName = "您确定要移除"+user.userName+"?";
		$('#staticDelShareUser').modal();
	}
	
	// zjn 2019-03-14  移除用户
	vm.delShareUser = function()
	{
		if(vm.clicks)
		{
			vm.clicks = false;
			MainCtrl.blockUI({
	    	    animate: true,
	    	});
			upida.post("stock/warehouse/delShareUser",vm.shareHouse).then(function(data){
				if(data == "fail")
				{
            		vm.message = "改用户已经移除，请刷新重试";
            		$('#static').modal();
				}
				else if(data == "success")
				{
					vm.init(vm.pageShareUser.pageNo, vm.pageShareUser.pageSize, vm.pageShareUser.condition, vm.pageShareUser.url);
            		vm.message = "移除用户成功!";
            		$('#static').modal();
            		vm.shareHouse.user = {};
				}
				else
				{
            		vm.message = "系统错误，请刷新页面重试!";
            		$('#static').modal();
				}
				vm.clicks = true;
				MainCtrl.unblockUI();
			});
		}
	}
	
	vm.storeHouseNameQu = "";
	vm.storeIdQu = "";
	vm.materialPlaceList = [];
	vm.storeHousePList = [];
	
	vm.page.placeData = {};
	vm.page.placePageSize = 10;
	vm.page.placePageNo = 1;
	
	vm.pagePlaceSizeChange = function() {
		vm.getStorePlace();
	};
	
	vm.doPlacePage = function(page, pageSize, total) 
	{
		vm.page.placePageNo = page;
		vm.page.placePageSize = pageSize;
		vm.getStorePlace();
	};
	
	vm.getStorePlace = function(storePlace){
		MainCtrl.blockUI({
    	    animate: true,
    	});
		if(!storePlace){
			storePlace = {};
		}
		storePlace.storeId = vm.storeIdQu;
		storePlace.name = vm.storeHouseNameQu;
		
		storePlace.pageNo = vm.page.placePageNo;
		storePlace.pageSize = vm.page.placePageSize;
		
		upida.post("stock/material/getMaterialPlaceList",storePlace).then(function (data) {
			if(data) 
			{
				vm.storeHousePList = data.storeList;
				vm.allLandscape = data.allLandscape;
				vm.allPortait = data.allPortait;
				vm.allLineScape = data.allLineScape;
				
				// 获取最大坐标
				vm.portaitData = 0;
				vm.landscapeData = 0;
				vm.lineData = 0;
				if(vm.allLandscape != null) {
					for(let i=0;i<vm.allLandscape.length;i++){
						var charNum = vm.getCharNum(vm.allLandscape[i]);
						if(Number(charNum) > Number(vm.landscapeData)){
							vm.landscapeData = charNum;
						}
					}
				}
				if(vm.allPortait != null) {
					for(let i=0;i<vm.allPortait.length;i++){
						if(Number(vm.allPortait[i]) > Number(vm.portaitData)){
							vm.portaitData = vm.allPortait[i];
						}
					}
				}
				if(vm.allLineScape != null) {
					for(let i=0;i<vm.allLineScape.length;i++){
						if(Number(vm.allLineScape[i]) > Number(vm.lineData)){
							vm.lineData = vm.allLineScape[i];
						}
					}
				}
				vm.setPortAlllandList();
				
				vm.page.placeData = {};
				vm.materialPlaceList = [];
				if(data.data && data.data.list && data.data.list.length > 0) {
					vm.materialPlaceList = data.data.list;
					if(vm.materialPlaceList != null && vm.storeHousePList != null){
						for(let j=0;j<vm.materialPlaceList.length;j++){
							for(let i=0;i<vm.storeHousePList.length;i++){
								if(vm.storeHousePList[i].recordId == vm.materialPlaceList[j].storeId){
									vm.materialPlaceList[j].store = vm.storeHousePList[i];
								}
							}
						}
					}
					
					// 计算开始数
					data.data.startCount = (data.data.pageNo - 1) * data.data.pageSize + 1;
					// 计算结束数
					data.data.endCount = (data.data.pageNo - 1) * data.data.pageSize + data.data.list.length;
					
					vm.page.placeData = data.data;
				}
			}
        	MainCtrl.unblockUI();
        });
	}
	
	vm.addStorePlace = function(){
		var storePlace = {};
		if(!vm.materialPlaceList) {
			vm.materialPlaceList = [];
		}
		vm.materialPlaceList.push(storePlace);
	}
	
	vm.materialKindQu = "100701";
	vm.storeHouseNameQu = "";
	vm.materialNameQu = "";
	vm.saleCompanyQu = "";
    vm.groupDeptQuery = "";
	vm.materialPlaceComList = [];
	vm.placeList = [];
	vm.branchs = [];
	
	vm.page.placeComData = {};
	vm.page.placeComPageSize = 10;
	vm.page.placeComPageNo = 1;
	
	vm.pagePlaceComSizeChange = function() {
		vm.getStorePlaceCom();
	};
	
	vm.doPlaceComPage = function(page, pageSize, total) 
	{
		vm.page.placeComPageNo = page;
		vm.page.placeComPageSize = pageSize;
		vm.getStorePlaceCom();
	};
	
	vm.getStorePlaceCom = function(){
		MainCtrl.blockUI({
    	    animate: true,
    	});
		var storePlaceCom = {};
		storePlaceCom.materialKind = vm.materialKindQu;
		storePlaceCom.placeName = vm.storeHouseNameQu;
		storePlaceCom.materialName = vm.materialNameQu;
		storePlaceCom.brachName = vm.saleCompanyQu;
        storePlaceCom.groupDeptId = vm.groupDeptQuery;
		
		storePlaceCom.pageNo = vm.page.placeComPageNo;
		storePlaceCom.pageSize = vm.page.placeComPageSize;
		
		
		upida.post("stock/material/getMaterialPlaceComList",storePlaceCom).then(function (data) {
			if(data) 
			{
				vm.placeList = data.placeList;
				vm.branchs = data.branchs;
				vm.storePlaceComInit = data.result;
				
				vm.materialPlaceComList = [];
				vm.page.placeComData = {};
				if(data.data && data.data.list && data.data.list.length > 0) {
					vm.materialPlaceComList = data.data.list;
					if(vm.materialPlaceComList != null){
						for(let j=0;j<vm.materialPlaceComList.length;j++){
							if(vm.placeList != null){
								for(let i=0;i<vm.placeList.length;i++){
									if(vm.placeList[i].recordId == vm.materialPlaceComList[j].materPlaceId){
										vm.materialPlaceComList[j].stockPlace = vm.placeList[i];
										break;
									}
								}
							}
							if(vm.materialPlaceComList[j].materials != null){
								for(let i=0;i<vm.materialPlaceComList[j].materials.length;i++){
									if(vm.materialPlaceComList[j].materials[i].recordId == vm.materialPlaceComList[j].materialId){
										vm.materialPlaceComList[j].material = vm.materialPlaceComList[j].materials[i];
										break;
									}
								}
							}
							if(vm.branchs != null){
								for(let i=0;i<vm.branchs.length;i++){
									if(vm.branchs[i].recordId == vm.materialPlaceComList[j].saleCompanyId){
										vm.materialPlaceComList[j].branch = vm.branchs[i];
										break;
									}
								}
							}
						}
					}
					
					// 计算开始数
					data.data.startCount = (data.data.pageNo - 1) * data.data.pageSize + 1;
					// 计算结束数
					data.data.endCount = (data.data.pageNo - 1) * data.data.pageSize + data.data.list.length;
					
					vm.page.placeComData = data.data;
				}
			}
        	MainCtrl.unblockUI();
        });
	}
	
	vm.addStorePlaceCom = function(){
		var storePlaceCom = {};
		if(!vm.materialPlaceComList) {
			vm.materialPlaceComList = [];
		}
		vm.materialPlaceComList.push(storePlaceCom);
	}
	
	vm.manageStorePlace = function(){
		vm.changeStocks();
		$("#manageStorePlace").modal();
	}
	
	vm.landscapeAdd = function(){
		vm.setPortAlllandList();
//		$("#landscapeAdd").modal();
	}
	
	vm.portaitAdd = function(){
		vm.setPortAlllandList();
//		$("#portaitAdd").modal();
	}
	
	vm.portaitData = "";
	vm.landscapeAddConfim = function(){
		var row = {};
		if(vm.chageStock && vm.chageStock.recordId){
			row.storeId = vm.chageStock.recordId;
		}else{
			vm.message = "请选择仓库!";
    		$('#static').modal();
    		return;
		}
		if(!vm.portaitData){
			vm.message = "请输入纵向坐标!";
    		$('#static').modal();
    		return;
		}
		row.portait = vm.portaitData;
		MainCtrl.blockUI({
    	    animate: true,
    	});
		upida.post("stock/material/updateLandscape",row).then(function (data) {
			if(data && data == "success") 
			{
				vm.changeStocks();
				vm.getStorePlace();
				vm.message = "保存成功!";
	    		$('#static').modal();
	    		return;
			}
        	MainCtrl.unblockUI();
        });
	}
	
	vm.landscapeData = "";
	vm.lineData = "";
	vm.portaitAddConfim = function(){
		var row = {};
		if(vm.chageStock && vm.chageStock.recordId){
			row.storeId = vm.chageStock.recordId;
		}else{
			vm.message = "请选择仓库!";
    		$('#static').modal();
    		return;
		}
		if(!vm.landscapeData){
			vm.message = "请输入区数量!";
    		$('#static').modal();
    		return;
		}
		row.landscape = vm.landscapeData;
		MainCtrl.blockUI({
    	    animate: true,
    	});
		upida.post("stock/material/updatePortait",row).then(function (data) {
			if(data && data == "success") 
			{
				vm.changeStocks();
				vm.getStorePlace();
				vm.message = "保存成功!";
	    		$('#static').modal();
	    		return;
			}
        	MainCtrl.unblockUI();
        });
	}
	
	vm.confimGenPlace = function() {
		var nameList = [];
		for(let i=0;i<vm.landscapeRwoList.length;i++){
			for(let j=0;j<vm.portaitRwoList.length;j++){
				if(vm.landscapeRwoList[i] && vm.portaitRwoList[j]){
					var name = {};
					var landLine = vm.landscapeRwoList[i];
					
					name.landscape = landLine.land;
					name.lineScape = landLine.line;
						
					name.portait = vm.portaitRwoList[j];
					name.portaitNum = vm.portaitRwoList[j];
					
					name.landscapeNum = vm.getCharNum(name.landscape);
					if(name.lineScape){
						name.value = name.landscape + vm.portaitRwoList[j] + "-" + name.lineScape;
					}else{
						name.value = name.landscape + "-" + vm.portaitRwoList[j];
					}
					nameList.push(name);
				}
			}
		}
		var row = {};
		if(vm.chageStock && vm.chageStock.recordId){
			row.storeId = vm.chageStock.recordId;
		}else{
			vm.message = "请选择仓库!";
    		$('#static').modal();
    		return;
		}
		if(nameList.length == 0){
			vm.message = "请输入纵横坐标!";
    		$('#static').modal();
    		return;
		}
		row.nameList = nameList;
		MainCtrl.blockUI({
    	    animate: true,
    	});
		upida.post("stock/material/updateAllNameList",row).then(function (data) {
			if(data && data == "success") 
			{
				vm.changeStocks();
				vm.getStorePlace();
				vm.message = "保存成功!";
	    		$('#static').modal();
	    		return;
			}
        	MainCtrl.unblockUI();
        });
	}
	
	vm.delStorePlace = function(row,index){
		if(row.recordId){
			upida.post("stock/material/deleteMaterialPlace",row).then(function (data) {
				if(data && data == "success") 
				{
					vm.getStorePlace();
					vm.message = "删除成功!";
		    		$('#static').modal();
		    		return;
				}else{
					vm.message = "有被使用，不能删除!";
		    		$('#static').modal();
				}
	        	MainCtrl.unblockUI();
	        });
		}else{
			vm.materialPlaceList.splice(index, 1);
		}
	}
	
	vm.delStorePlaceCom = function(row,index){
		if(row.recordId){
			upida.post("stock/material/deleteMaterialPlaceCom",row).then(function (data) {
				if(data && data == "success") 
				{
					vm.getStorePlaceCom();
					vm.message = "删除成功!";
		    		$('#static').modal();
		    		return;
				}
	        	MainCtrl.unblockUI();
	        });
		}else{
			vm.materialPlaceComList.splice(index, 1);
		}
	}
	
	vm.chageStock = {};
	vm.materialPlaceManageList = [];
	vm.allLandscape = [];
	vm.allPortait = [];
	vm.landscapeList=["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];
	vm.portaitList=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30];
	vm.lineList=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30];
	
	vm.landscapeRwoList = [];
	vm.portaitRwoList = [];
	vm.changeStocks = function(){
		if(vm.chageStock && vm.chageStock.recordId){
			MainCtrl.blockUI({
	    	    animate: true,
	    	});
			var storePlace = {};
			storePlace.storeId = vm.chageStock.recordId;
			vm.getStorePlace(storePlace);
		}
	}
	
	vm.setPortAlllandList = function() {
		vm.landscapeRwoList = [];
		vm.portaitRwoList = [];
		if(vm.landscapeData > 0) {
			for(let i=0;i<vm.landscapeData;i++){
				if(vm.lineData > 0) {
					for(let j=0;j<vm.lineData;j++){
						var landLine = {};
						landLine.land = vm.landscapeList[i];
						landLine.line = vm.lineList[j];
						vm.landscapeRwoList.push(landLine);
					}
				}else{
					var landLine = {};
					landLine.land = vm.landscapeList[i];
					landLine.name = vm.landscapeList[i];
					vm.landscapeRwoList.push(landLine);
				}
			}
		}
		if(vm.portaitData > 0) {
			for(let i=0;i<vm.portaitData;i++){
				vm.portaitRwoList.push(vm.portaitList[i]);
			}
		}
	}
	
	vm.getCharNum=function(item){
		if(item == "A"){
			return 1;
		}
		if(item == "B"){
			return 2;
		}
		if(item == "C"){
			return 3;
		}
		if(item == "D"){
			return 4;
		}
		if(item == "E"){
			return 5;
		}
		if(item == "F"){
			return 6;
		}
		if(item == "G"){
			return 7;
		}
		if(item == "H"){
			return 8;
		}
		if(item == "I"){
			return 9;
		}
		if(item == "J"){
			return 10;
		}
		if(item == "K"){
			return 11;
		}
		if(item == "L"){
			return 12;
		}
		if(item == "M"){
			return 13;
		}
		if(item == "N"){
			return 14;
		}
		if(item == "O"){
			return 15;
		}
		if(item == "P"){
			return 16;
		}
		if(item == "Q"){
			return 17;
		}
		if(item == "R"){
			return 18;
		}
		if(item == "S"){
			return 19;
		}
		if(item == "T"){
			return 20;
		}
		if(item == "U"){
			return 21;
		}
		if(item == "V"){
			return 22;
		}
		if(item == "W"){
			return 23;
		}
		if(item == "X"){
			return 24;
		}
		if(item == "Y"){
			return 25;
		}
		if(item == "Z"){
			return 26;
		}
	}
	
	vm.saveStorePlaceComStocks = function(row,sta){
		row.inveDate = new Date();
		vm.saveStorePlaceCom(row,sta);
	}
	
	vm.saveStorePlaceCom = function(row,sta){
		row.materialKind = vm.materialKindQu;
		if(row.recordId || sta == 1){
			if(row.stockPlace && row.stockPlace.recordId){
				row.materPlaceId = row.stockPlace.recordId;
				row.storeId = row.stockPlace.storeId;
			}else{
				vm.message = "请选择库位!";
	    		$('#static').modal();
	    		return;
			}
			if(row.material && row.material.recordId){
				row.materialId = row.material.recordId;
			}else{
				vm.message = "请选择物料!";
	    		$('#static').modal();
	    		return;
			}
			if(row.branch && row.branch.recordId){
				row.saleCompanyId = row.branch.recordId;
			}
//			MainCtrl.blockUI({
//	    	    animate: true,
//	    	});
			row.materials = [];
			upida.post("stock/material/insertMaterialPlaceCom",row).then(function (data) {
				if(data && data.result == "success") 
				{
					vm.getStorePlaceCom();
					if(row.stockPlace) {
						row.materials = data.materials;
					}
//					vm.message = "保存成功!";
//		    		$('#static').modal();
		    		return;
				}else{
					vm.message = "物料、库位、子公司三者不唯一，请确认!";
		    		$('#static').modal();
		    		return;
				}
//	        	MainCtrl.unblockUI();
	        });
		}else if(sta == 2){
			if(row.stockPlace && row.stockPlace.recordId){
				row.materPlaceId = row.stockPlace.recordId;
				row.storeId = row.stockPlace.storeId;
				row.materials = [];
				upida.post("stock/material/getMaterialComList",row).then(function (data) {
					if(data) 
					{
						if(row.stockPlace) {
							row.materials = data;
						}
					}
		        });
			}
		}
	}
	
	vm.updateDeailName = function(row) {
		var name = "";
		if(row.lineScape && row.landscape && row.portait){
            name = row.landscape + row.portait + "-" + row.lineScape;
		}else{
		    if (row.landscape && row.portait){
                name = row.landscape + "-" + row.portait;
            }
		}
		row.name = name;
		vm.saveStorePlace(row);
	}
	
	vm.storePlaceCom = {};
	vm.openStorePlaceCom = function(row) {
		vm.storePlaceCom = {};
		let otherStocks = row.stocks;
		if(row.rawInoutList && row.rawInoutList.length > 0){
			for(let i=0;i<row.rawInoutList.length;i++){
				if(row.rawInoutList[i].quantity){
					if(row.rawInoutList[i].inoutType == 3 || row.rawInoutList[i].inoutType == 4 || row.rawInoutList[i].inoutType == 5 || row.rawInoutList[i].inoutType == 6 || row.rawInoutList[i].inoutType == 9){
						otherStocks = otherStocks - row.rawInoutList[i].quantity;
					}else if(row.rawInoutList[i].inoutType == 1 || row.rawInoutList[i].inoutType == 2 || row.rawInoutList[i].inoutType == 7 || row.rawInoutList[i].inoutType == 10){
						otherStocks = otherStocks + row.rawInoutList[i].quantity;
					}
					row.rawInoutList[i].otherStocks = otherStocks;
				}
			}
		}
		vm.storePlaceCom = row;
		$("#openStorePlaceCom").modal();
	}
	
	vm.saveStorePlace = function(row,sta){
		if(row.recordId || sta == 1){
			if(row.store && row.store.recordId){
				row.storeId = row.store.recordId;
			}else{
				vm.message = "请选择仓库!";
	    		$('#static').modal();
	    		return;
			}
			if(!row.name){
				vm.message = "请输入名字!";
	    		$('#static').modal();
	    		return;
			}
			MainCtrl.blockUI({
	    	    animate: true,
	    	});
			upida.post("stock/material/insertMaterialPlace",row).then(function (data) {
				if(data && data == "success") 
				{
					vm.getStorePlace();
					vm.message = "保存成功!";
		    		$('#static').modal();
				}else{
					vm.message = "库位名字重复，请确认!";
		    		$('#static').modal();
				}
	        	MainCtrl.unblockUI();
	        });
		}
	}
	
	vm.storePlaceComInit = "";
	vm.initStorePlaceCom = function() {
		MainCtrl.blockUI({
    		animate: true,
    	});
		upida.post("stock/material/initStorePlaceCom").then(function (data) {
			if(data && data == "1") 
			{
				vm.storePlaceComInit = "1";
				vm.message = "可以开始初始化了!";
	    		$('#static').modal();
	    		return;
			}else{
				vm.getStorePlaceCom();
				vm.storePlaceComInit = "";
				vm.message = "初始化结束!";
	    		$('#static').modal();
	    		return;
			}
        	MainCtrl.unblockUI();
        });
	}
	
	vm.updateMaterialPlaceComStatus = function(row) {
		MainCtrl.blockUI({
    		animate: true,
    	});
		upida.post("stock/material/updateMaterialPlaceComStatus",row).then(function (data) {
			if(data && data == "success") 
			{
				vm.getStorePlaceCom();
				vm.message = "设置成功";
	    		$('#static').modal();
	    		return;
			}
        	MainCtrl.unblockUI();
        });
	}
	
	vm.uploadExcel = function() {
    	var file = $("#upfile")[0].files[0];
    	var form = new FormData();
    	form.append("f",file);
    	MainCtrl.blockUI({
    		animate: true,
    	});
    	$.ajax({
     		type:"post",
     		url: 'a/stock/material/uploadExcel',
     		data: form,
     		contentType:"application/json",
            processData: false,  //这些不能忽略
            contentType: false,  
     		success:function(data)
     		{
     			if(data = "result")
     			{
     				vm.message = "导入成功！";
			        $('#static').modal();
			        $("#upfile").val("");
			        // 初始化第一页，条件为空
			        vm.getStorePlaceCom();
     			}else{
     				vm.message = "导入失败，请刷新重试！";
			        $('#static').modal();
			        $("#upfile").val("");
			        // 初始化第一页，条件为空
			        vm.getStorePlaceCom();
     			}
     			MainCtrl.unblockUI();
     		}
     	})
    	
    }
	
    // 加载权限
    function loadRight(){
        //是否可以查看所有人的
        vm.queryAll = CommonUtil.dataRangeIsAll("10608", BaseUtil.getMenuList());

        MainCtrl.blockUI({
    	    animate: true,
    	});
        upida.get("common/rightall?prefix=stock:warehouse").then(function(data){
            vm.right.view = data.view;
            vm.right.edit = data.edit;
            vm.right.manage = data.manage;

            loadItemsData();

            var condition = [];
            condition.push({
                name: "finalQuery",
                value: vm.finalQuery
            });
            
            // 初始化第一页，条件为空
            vm.page.pageNo = 1;
            vm.init(vm.page.pageNo, vm.page.pageSize, condition, vm.page.url);
        });
    }

    vm.relationList = [];

    function loadItemsData() {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
        //加载仓库管理员
        upida.get("stock/warehouse/getUser").then(function(data) {
            vm.adminer = data;
            MainCtrl.unblockUI();
        });
        upida.post("stock/warehouse/loadData").then(function(data) {
            vm.relationList = data.relationList;
        });

    }
    
    vm.addMaterialItems = function(no,detail)
    {
    	var material = {};
    	material.no = no;
    	material.materialKind = vm.materialKindQu;
    	upida.post("stock/material/addMaterialItems",material).then(function(data){
    		detail.materials = data;
    	});
    }
    
    vm.initProductInOutRecords = function()
    {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	upida.post("stock/warehouse/initProductInOutRecords").then(function(data){
    		if(data == "success")
    		{
 				vm.message = "初始化成功！";
    		}
    		else
    		{
    			vm.message = "初始化失败！";
    		}
    		$('#static').modal();
    		MainCtrl.unblockUI();
    	});
    }
    
    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
    });
}]);