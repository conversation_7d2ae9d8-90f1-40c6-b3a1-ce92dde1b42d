package com.kyb.pcberp.modules.crm.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

import java.math.BigDecimal;
import java.util.List;

public class QuoteSetupDetail extends DataEntity<QuoteSetupDetail>
{
    private static final long serialVersionUID = 1L;

    private String companyId;

    private String setupId;

    private Integer sortNum;

    private BigDecimal price;

    private String status;

    private List<QuoteSetupDetailColumn> list;

    private String useFlag;

    private BigDecimal windowPcsPrice;

    private BigDecimal salePcsPrice;

    private BigDecimal avgWindowPrice; // 评估材料费

    private BigDecimal salePrcessPrice; //销售加工平米单价

    private BigDecimal prcessPrice; //系统加工平米单价

    private BigDecimal avgSalePrice; // 销售材料费

    private BigDecimal adjustProcessPrice;

    private String sectionValue;

    private String conditionFlag;

    private String orderType;

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    public String getSetupId()
    {
        return setupId;
    }

    public void setSetupId(String setupId)
    {
        this.setupId = setupId;
    }

    public Integer getSortNum()
    {
        return sortNum;
    }

    public void setSortNum(Integer sortNum)
    {
        this.sortNum = sortNum;
    }

    public BigDecimal getPrice()
    {
        return price;
    }

    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public List<QuoteSetupDetailColumn> getList()
    {
        return list;
    }

    public void setList(List<QuoteSetupDetailColumn> list)
    {
        this.list = list;
    }

    public String getUseFlag()
    {
        return useFlag;
    }

    public void setUseFlag(String useFlag)
    {
        this.useFlag = useFlag;
    }

    public BigDecimal getWindowPcsPrice()
    {
        return windowPcsPrice;
    }

    public void setWindowPcsPrice(BigDecimal windowPcsPrice)
    {
        this.windowPcsPrice = windowPcsPrice;
    }

    public BigDecimal getSalePcsPrice()
    {
        return salePcsPrice;
    }

    public void setSalePcsPrice(BigDecimal salePcsPrice)
    {
        this.salePcsPrice = salePcsPrice;
    }

    public BigDecimal getAvgWindowPrice()
    {
        return avgWindowPrice;
    }

    public void setAvgWindowPrice(BigDecimal avgWindowPrice)
    {
        this.avgWindowPrice = avgWindowPrice;
    }

    public BigDecimal getSalePrcessPrice()
    {
        return salePrcessPrice;
    }

    public void setSalePrcessPrice(BigDecimal salePrcessPrice)
    {
        this.salePrcessPrice = salePrcessPrice;
    }

    public BigDecimal getPrcessPrice()
    {
        return prcessPrice;
    }

    public void setPrcessPrice(BigDecimal prcessPrice)
    {
        this.prcessPrice = prcessPrice;
    }

    public BigDecimal getAvgSalePrice()
    {
        return avgSalePrice;
    }

    public void setAvgSalePrice(BigDecimal avgSalePrice)
    {
        this.avgSalePrice = avgSalePrice;
    }

    public BigDecimal getAdjustProcessPrice() {
        return adjustProcessPrice;
    }

    public void setAdjustProcessPrice(BigDecimal adjustProcessPrice) {
        this.adjustProcessPrice = adjustProcessPrice;
    }


    // 实体深拷贝
    public QuoteSetupDetail clone()
    {
        QuoteSetupDetail o = null;
        try
        {
            o = (QuoteSetupDetail)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } // Object 中的clone()识别出你要复制的是哪一个对象。
        return o;
    }

    public String getSectionValue() {
        return sectionValue;
    }

    public void setSectionValue(String sectionValue) {
        this.sectionValue = sectionValue;
    }

    public String getConditionFlag()
    {
        return conditionFlag;
    }

    public void setConditionFlag(String conditionFlag)
    {
        this.conditionFlag = conditionFlag;
    }

    public String getOrderType()
    {
        return orderType;
    }

    public void setOrderType(String orderType)
    {
        this.orderType = orderType;
    }
}
