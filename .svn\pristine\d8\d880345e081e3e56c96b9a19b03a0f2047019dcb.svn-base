package com.kyb.pcberp.modules.crm.web;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.mapper.JsonMapper;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.pdf.PdfResult;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.persistence.PageNoInterceptor;
import com.kyb.pcberp.common.utils.CommonUtils;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.contract.entity.Contract;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.contract.service.ContractDetailService;
import com.kyb.pcberp.modules.contract.service.ContractService;
import com.kyb.pcberp.modules.contract.service.DeliveryDetailService;
import com.kyb.pcberp.modules.contract.vo.RejectTraceReportExpVo;
import com.kyb.pcberp.modules.contract.vo.RejectTraceReportVo;
import com.kyb.pcberp.modules.crm.entity.ComplaintFile;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.crm.entity.RejectApplication;
import com.kyb.pcberp.modules.crm.service.ComplaintService;
import com.kyb.pcberp.modules.crm.service.CustomerService;
import com.kyb.pcberp.modules.crm.vo.RejectPlanVo;
import com.kyb.pcberp.modules.eg.entity.EgProcess;
import com.kyb.pcberp.modules.quality.entity.Discard;
import com.kyb.pcberp.modules.quality.entity.Rework;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.service.MaterialService;
import com.kyb.pcberp.modules.sys.entity.Branch;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.DictItem;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import com.kyb.pcberp.modules.sys.entity.Pdf;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.service.BranchService;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

// 客户投诉
@Controller
@RequestMapping(value = "${adminPath}/crm/complaint")
public class ComplaintController extends BaseController
{
    private final ObjectMapper complaintMapper = new JsonMapper().enableSimple();
    
    private final MappingJackson2JsonView view = new MappingJackson2JsonView();
    
    @Autowired
    private ComplaintService complaintService;
    
    @Autowired
    private CustomerService customerService;
    
    @Autowired
    private ContractDetailService contractDetailService;
    
    @Autowired
    private MaterialService materialService;
    
    @Autowired
    private ContractService contractService;
    
    @Autowired
    private DeliveryDetailService deliveryDetailService;
    
    @Autowired
    private BranchService branchService;
    
    @RequestMapping(value = {"view"})
    public String list()
    {
        return "modules/crm/complaint";
    }
    
    @RequiresPermissions("crm:rejectApplication:view")
    @RequestMapping(value = "page")
    @ResponseBody
    public Page<RejectApplication> page(@RequestBody RejectApplication rejectApplication, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        rejectApplication.setCompany(user.getCompany());
        Page<RejectApplication> qpage = new Page<RejectApplication>(request, response);
        qpage.setPageNo(Integer.parseInt(rejectApplication.getPageNo()));
        qpage.setPageSize(Integer.parseInt(rejectApplication.getPageSize()));
        
        if (rejectApplication.getSentTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rejectApplication.getSentTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            rejectApplication.setSentTimeEndQr(deliTime.getTime());
        }
        if (rejectApplication.getSentTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rejectApplication.getSentTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            rejectApplication.setSentTimeStartQr(deliTime.getTime());
        }
        
        // 设置排序
        if (StringUtils.isNotBlank(rejectApplication.getOrderBy()))
        {
            qpage.setOrderBy(rejectApplication.getOrderBy());
        }
        
        // 设置查询范围
        if (rejectApplication.getQueryAll() != null && !rejectApplication.getQueryAll())
        {
            rejectApplication.setCreatedBy(user);
        }
        
        Page<RejectApplication> page = complaintService.findPage(qpage, rejectApplication);
        return page;
    }
    
    @RequiresPermissions("crm:rejectApplication:edit")
    @RequestMapping(value = "save")
    @ResponseBody
    public Map<String, Object> save(@RequestBody RejectApplication rejectApplication, Model model)
    {
        rejectApplication.setCompany(UserUtils.getUser().getCompany());
        Map<String, Object> data = complaintService.save(rejectApplication);
        return data;
    }
    
    @RequiresPermissions("crm:rejectApplication:edit")
    @RequestMapping(value = "delete/{id}")
    @ResponseBody
    public String delete(@PathVariable("id") String id)
    {
        RejectApplication rejectApplication = new RejectApplication(id);
        rejectApplication.setCompany(UserUtils.getUser().getCompany());
        complaintService.delete(rejectApplication);
        return "{\"result\":true,\"message\":\"删除客诉单成功\"}";
    }
    
    @RequiresPermissions("crm:rejectApplication:view")
    @RequestMapping(value = "returngoods")
    @ResponseBody
    public String returngoods(@RequestParam("contractDetailId") String recordId,
        @RequestParam("quantity") Integer quantity)
    {
        Boolean flag = false;
        Integer availableReturnQty = 0;// 可客诉数量
        ContractDetail contractDetail = new ContractDetail(recordId);
        contractDetail.setCompany(UserUtils.getUser().getCompany());
        // 获取该合同所有已经出库的送货单数量,包含正常送货出库以及客诉送货出库
        Integer sendGoods = deliveryDetailService.getSendGoods(contractDetail);
        if (sendGoods == null)
        {
            sendGoods = 0;
        }
        if (quantity != null)
        {
            sendGoods = sendGoods + quantity;
        }
        // 获取客诉单已添加的客诉数量（有效）
        Integer custComplaintGoods = complaintService.getCustComplaintGoods(contractDetail);
        if (custComplaintGoods == null)
        {
            custComplaintGoods = 0;
        }
        // 客诉数量
        Integer custNoNum = custComplaintGoods;
        String resultMSG = "";
        if (sendGoods < 0)
        {
            resultMSG = "没有可以客诉退货的数量！";
        }
        Integer canCustSendGoods = sendGoods - custNoNum;
        if (canCustSendGoods < 0)
        {
            resultMSG = "没有可以客诉退货的数量！";
        }
        if (sendGoods >= 0 && canCustSendGoods >= 0)
        {
            availableReturnQty = canCustSendGoods;
            flag = true;
        }
        return "{\"result\":" + flag + ",\"availableReturnQty\":\"" + availableReturnQty + "\",\"resultMSG\":\""
            + resultMSG + "\"}";
    }
    
    @RequiresPermissions("crm:rejectApplication:view")
    @RequestMapping(value = "load/data")
    public View loadData(boolean reqData, Model model)
    {
        view.setObjectMapper(complaintMapper);
        
        Map<String, List<?>> data = Maps.newHashMap();
        
        User user = UserUtils.getUser();
        
        // 查询客户
        Customer qCus = new Customer();
        qCus.setCompany(user.getCompany());
        if (!reqData)
        {
            qCus.setCreatedBy(UserUtils.getUser());
        }
        List<Customer> customerList = customerService.findAuditCustomerList(qCus);
        
        data.put("customerList", customerList);
        
        Branch branch = new Branch();
        branch.setCompany(user.getCompany());
        List<Branch> branchList = branchService.findList(branch);
        data.put("branchList", branchList);
        
        // 返回数据
        model.addAttribute("data", data);
        return view;
    }
    
    @RequiresPermissions("crm:rejectApplication:view")
    @RequestMapping(value = "print")
    public String printPDF(String recordId, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            RejectApplication reject = new RejectApplication(recordId);
            reject.setCompany(UserUtils.getUser().getCompany());
            Pdf pdf = complaintService.getPdf(reject, request.getSession().getServletContext().getRealPath("/"));
            new PdfResult().doExecute(pdf.getFileName(), request, response, pdf.getDataMap());
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }
    
    @RequiresPermissions("crm:rejectApplication:edit")
    @RequestMapping(value = "no")
    @ResponseBody
    public String getNo()
    {
        return CommonUtils.geDocumentNo(CommonEnums.CodeType.COMPLAINT.getIndex().toString());
    }
    
    // 根据客户加载所有可客诉的合同
    @RequiresPermissions("crm:rejectApplication:view")
    @RequestMapping(value = "getContractByCustomer")
    @ResponseBody
    public List<Contract> getContractByCustomer(String customerId)
    {
        List<Contract> list = contractService.getContractByCustomer(customerId);
        return list;
    }
    
    @RequiresPermissions("crm:rejectApplication:view")
    @RequestMapping(value = "getContractDetailListByContact")
    @ResponseBody
    public List<ContractDetail> getContractDetailListByContact(String contractId)
    {
        return contractDetailService.getContractDeailByConId(contractId);
    }
    
    @RequiresPermissions("crm:rejectApplication:view")
    @RequestMapping(value = "material")
    @ResponseBody
    public Material getMaterialByContractCraft(String craftNo)
    {
        Material data = null;
        try
        {
            data = materialService.findMaterialByNo(new Material(craftNo, UserUtils.getUser().getCompany()));
        }
        catch (Exception e)
        {
            return null;
        }
        return data;
    }
    
    @RequiresPermissions("crm:rejectApplication:edit")
    @RequestMapping(value = "getAllQtyByDelDtlId")
    @ResponseBody
    public Integer getCustComplaintGoodsByDelDtlId(String deliveryDetailId)
    {
        return complaintService.getCustComplaintGoodsByDelDtlId(deliveryDetailId);
    }
    
    @RequiresPermissions("crm:rejectApplication:view")
    @RequestMapping(value = "export")
    public String exportFile(RejectApplication rejectApplication, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            rejectApplication.setCompany(user.getCompany());
            
            if (null != rejectApplication.getStatus() && "-1".equals(rejectApplication.getStatus().toString()))
            {
                rejectApplication.setStatus(null);
            }
            
            Page<RejectApplication> qpage = new Page<RejectApplication>(request, response, -1);
            
            if (null != rejectApplication.getQueryAll() && !rejectApplication.getQueryAll())
            {
                rejectApplication.setCreatedBy(user);
            }
            
            // 设置排序
            if (StringUtils.isNotBlank(rejectApplication.getOrderBy()))
            {
                qpage.setOrderBy(rejectApplication.getOrderBy());
            }
            
            // 设置查询范围
            if (rejectApplication.getQueryAll() != null && !rejectApplication.getQueryAll())
            {
                rejectApplication.setCreatedBy(user);
            }
            String inoutTimeStartQr = request.getParameter("sentTimeStartQr");
            String inoutTimeEndQr = request.getParameter("sentTimeEndQr");
            if (StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(inoutTimeStartQr)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                rejectApplication.setSentTimeStartQr(deliTime.getTime());
            }
            if (StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(inoutTimeEndQr)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                rejectApplication.setSentTimeEndQr(deliTime.getTime());
            }
            String fileName = "客诉数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            Page<RejectApplication> page = complaintService.findPage(qpage, rejectApplication);
            new ExportExcel("客诉数据", RejectApplication.class).setDataList(page.getList())
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            // return null;
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
    }
    
    /**
     * 上传客诉附件
     */
    @RequiresPermissions("crm:rejectApplication:edit")
    @RequestMapping(value = "uploadComplaintFile")
    @ResponseBody
    public Map<String, Object> uploadComplaintFile(@RequestParam("file") MultipartFile file,
        @RequestParam("no") String no, HttpServletRequest req)
    {
        Company company = UserUtils.getUser().getCompany();
        
        // 公司id+客诉单+资料
        String savePath = company.getRecordId() + "/complaint/" + no + "/";
        Map<String, Object> result = Maps.newHashMap();
        
        // 上传文件
        String url = FileManageUtils.uploadFile(file, savePath, req);
        if (url == null)
        {
            result.put("result", false);
            return result;
        }
        
        // 基本路径
        String sourcePath = savePath + file.getOriginalFilename();
        
        ComplaintFile complaintFile = new ComplaintFile();
        complaintFile.setNo(no);
        complaintFile.setCompany(company);
        complaintFile.setFileUrl(sourcePath);
        complaintFile.setRealFileName(file.getOriginalFilename());
        complaintFile.setTempUrl(url);
        complaintFile.setType("1");
        complaintService.uploadFile(complaintFile);
        result.put("complaintFile", complaintFile);
        result.put("result", true);
        return result;
    }
    
    /**
     * 获取客诉上传的文件
     *
     * @param request
     * @return
     */
    @RequiresPermissions("crm:rejectApplication:view")
    @RequestMapping(value = "getFile", method = RequestMethod.POST)
    @ResponseBody
    public List<ComplaintFile> getFile(@RequestBody RejectApplication rejectApplication)
    {
        rejectApplication.setCompany(UserUtils.getUser().getCompany());
        return complaintService.getFile(rejectApplication);
        
    }
    
    /**
     * 移除客诉的附件
     *
     * @param request
     * @return
     */
    @RequiresPermissions("crm:rejectApplication:view")
    @RequestMapping(value = "deleteFile", method = RequestMethod.POST)
    @ResponseBody
    public String deleteFile(@RequestBody ComplaintFile complaintFile)
    {
        complaintFile.setCompany(UserUtils.getUser().getCompany());
        complaintService.clearUpload(complaintFile);
        
        Company company = UserUtils.getUser().getCompany();
        // 客诉资料命名 公司id+客诉单编号+资料
        String savePath = company.getRecordId() + "/complaint/" + complaintFile.getNo() + "/";
        FileManageUtils.delFiles(savePath);
        return "移除客诉文件成功";
    }
    
    @RequiresPermissions("crm:rejectApplication:edit")
    @RequestMapping(value = "updateQualitity", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> updateQualitity(@RequestBody RejectApplication rejectApplication)
    {
        return complaintService.updateQualitity(rejectApplication);
    }
    
    // 根据客户加载所有可客诉的合同
    @RequiresPermissions("crm:rejectApplication:edit")
    @RequestMapping(value = "getProcessList", method = RequestMethod.POST)
    @ResponseBody
    public List<EgProcess> getProcessList(@RequestBody RejectApplication rejectApplication)
    {
        List<EgProcess> list = complaintService.getProcessList(rejectApplication);
        return list;
    }
    
    @RequiresPermissions("crm:rejectApplication:edit")
    @RequestMapping(value = "saveRework")
    @ResponseBody
    public String saveRework(@RequestBody Rework rework)
    {
        String saveMsg = complaintService.saveRework(rework);
        if ("success".equals(saveMsg))
        {
            return "{\"result\":true,\"message\":\"保存修理记录成功\"}";
        }
        else
        {
            return "{\"result\":false,\"message\":\"" + saveMsg + "\"}";
        }
        
    }
    
    @RequiresPermissions("crm:rejectApplication:edit")
    @RequestMapping(value = "saveDiscard")
    @ResponseBody
    public String saveDiscard(@RequestBody Discard discard)
    {
        String saveMsg = complaintService.saveDiscard(discard);
        if ("success".equals(saveMsg))
        {
            return "{\"result\":true,\"message\":\"保存报废记录成功\"}";
        }
        else
        {
            return "{\"result\":false,\"message\":\"" + saveMsg + "\"}";
        }
        
    }
    
    /**
     * 检测记录中查找可用的已确认，已审核的客诉单
     * 
     * @return
     */
    @RequiresPermissions("quality:detection:view")
    @RequestMapping(value = {"findReject"})
    @ResponseBody
    public List<RejectApplication> findReject(@RequestBody RejectApplication rejectApplication)
    {
        return complaintService.findReject(rejectApplication);
    }
    
    /**
     * 
     * 
     * @return
     */
    @RequiresPermissions("quality:detection:view")
    @RequestMapping(value = {"getRejectHandleTypeList"})
    @ResponseBody
    public List<DictValue> getRejectHandleTypeList()
    {
        DictItem qitem = new DictItem();
        qitem.setRecordId("73");
        return DictUtils.getValuesByItem(qitem, UserUtils.getUser().getCompany());
    }
    
    @RequestMapping(value = {"getSaleReportList"})
    @ResponseBody
    public PageNoInterceptor<RejectTraceReportVo> getSaleReportList(@RequestBody RejectTraceReportVo saleCombina,
        HttpServletRequest request, HttpServletResponse response)
    {
        if (saleCombina.getStartTime() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(saleCombina.getStartTime());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            saleCombina.setStartTime(deliTime.getTime());
        }
        if (saleCombina.getEndTime() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(saleCombina.getEndTime());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            saleCombina.setEndTime(deliTime.getTime());
        }
        PageNoInterceptor<RejectTraceReportVo> qpage = new PageNoInterceptor<RejectTraceReportVo>(request, response);
        if (StringUtils.isNotBlank(saleCombina.getPageNo()) && StringUtils.isNotBlank(saleCombina.getPageSize()))
        {
            qpage.setPageNo(Integer.parseInt(saleCombina.getPageNo()));
            qpage.setPageSize(Integer.parseInt(saleCombina.getPageSize()));
            Integer no = Integer.parseInt(saleCombina.getPageNo());
            Integer size = Integer.parseInt(saleCombina.getPageSize());
            // 分页查询数据
            if (no >= 1)
            {
                no = (no - 1) * size;
                saleCombina.setPageNo(no.toString());
            }
        }
        else
        {
            saleCombina.setPageNo(Global.PAGE_NO.toString());
            saleCombina.setPageSize(Global.PAGE_SIZE.toString());
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        PageNoInterceptor<RejectTraceReportVo> page = complaintService.getSaleReportList(qpage, saleCombina);
        return page;
    }
    
    /**
     * 导出
     * 
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("order:contract:view")
    @RequestMapping(value = "exportReportList")
    public String exportReportList(RejectTraceReportVo saleCombina, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            String startTime = request.getParameter("startTime");
            String endTime = request.getParameter("endTime");
            if (StringUtils.isNotBlank(startTime))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(startTime)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                saleCombina.setStartTime(deliTime.getTime());
            }
            if (StringUtils.isNotBlank(endTime))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(endTime)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                saleCombina.setEndTime(deliTime.getTime());
            }
            String showStatus = request.getParameter("showStatus");
            String showCraftNo = request.getParameter("showCraftNo");
            saleCombina.setShowStatus(showStatus);
            saleCombina.setShowCraftNo(showCraftNo);
            List<RejectTraceReportVo> list = complaintService.getSaleReportExcelList(saleCombina);
            String fileName = "业务数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            new ExportExcel("业务数据", RejectTraceReportExpVo.class, new Integer(1)).setDataList(list)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
        
    }
    
    @RequiresPermissions("crm:rejectApplication:edit")
    @RequestMapping(value = "doRollBack")
    @ResponseBody
    public Map<String, Object> doRollBack(@RequestBody RejectApplication rejectApplication)
    {
        rejectApplication.setCompany(UserUtils.getUser().getCompany());
        Map<String, Object> data = complaintService.doRollBack(rejectApplication);
        return data;
    }
    
    // @RequestMapping(value = "sentRejectApplicationData")
    // @ResponseBody
    // public String sentRejectApplicationData()
    // {
    // List<RejectApplication> list = complaintService.getNeedSentDataList();
    // for (RejectApplication r : list)
    // {
    // r.setOutsourceFlag(false);
    // ModifyUtils.sentRejectApplicationData(r, r.getLastUpdBy(), 3);
    // }
    // return "sucesss";
    // }
    
    
    @RequestMapping(value = "rejectPlanReport")
    @ResponseBody
    public Page<RejectPlanVo> rejectPlanReport(@RequestBody RejectPlanVo rp, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        rp.setCompany(user.getCompany());
        Page<RejectPlanVo> qpage = new Page<RejectPlanVo>(request, response);
        qpage.setPageNo(Integer.parseInt(rp.getPageNo()));
        qpage.setPageSize(Integer.parseInt(rp.getPageSize()));
        
        if (rp.getSentTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rp.getSentTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            rp.setSentTimeEndQr(deliTime.getTime());
        }
        if (rp.getSentTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(rp.getSentTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            rp.setSentTimeStartQr(deliTime.getTime());
        }
        
        // 设置排序
        if (StringUtils.isNotBlank(rp.getOrderBy()))
        {
            qpage.setOrderBy(rp.getOrderBy());
        }
        
        // 设置查询范围
        if (rp.getQueryAll() != null && !rp.getQueryAll())
        {
            rp.setCreatedBy(user);
        }
        
        Page<RejectPlanVo> page = complaintService.rejectPlanReport(qpage, rp);
        return page;
    }
    
    @RequestMapping(value = "exportRejectPlanReport")
    public String exportRejectPlanReport(RejectPlanVo rp, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            rp.setCompany(user.getCompany());
            rp.setNo(request.getParameter("no"));
            rp.setFinalRjectNo(request.getParameter("finalRjectNo"));
            rp.setCraftNo(request.getParameter("craftNo"));
            rp.setSentTimeEndQr(new Date(Long.parseLong(request.getParameter("sentTimeEndQr"))));
            rp.setSentTimeStartQr(new Date(Long.parseLong(request.getParameter("sentTimeStartQr"))));
            if (rp.getSentTimeEndQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(rp.getSentTimeEndQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                rp.setSentTimeEndQr(deliTime.getTime());
            }
            if (rp.getSentTimeStartQr() != null)
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(rp.getSentTimeStartQr());
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                rp.setSentTimeStartQr(deliTime.getTime());
            }
            
            
            // 设置查询范围
            if (rp.getQueryAll() != null && !rp.getQueryAll())
            {
                rp.setCreatedBy(user);
            }
            List<RejectPlanVo> list = complaintService.rejectPlanReportList(rp);
            
            
            String fileName = "客诉品质进度数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            new ExportExcel("", RejectPlanVo.class).setDataList(list)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            // return null;
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
    }

    @RequestMapping(value = "cancellation")
    @ResponseBody
    public String cancellation(@RequestBody RejectApplication rejectApplication)
    {
        return complaintService.cancellation(rejectApplication);
    }
}