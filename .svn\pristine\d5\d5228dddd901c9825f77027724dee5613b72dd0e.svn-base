const work = {
	template:'#work',
	// 初始化加载方法
	created:function(){
		this.getWxMsg()
	},
	beforeCreate () {
		this.$store.dispatch('myStore/setUserInformation')
		document.querySelector('body').setAttribute('style', 'background-color:#F0F0F0;font-family: 华文楷体;color:#333;')
	},
	computed: {
		userErp:{
			get () {
				return this.$store.state.myStore.userErp
			}
		},
		userInformation:{
			get () {
				return this.$store.state.myStore.userInformation
		    }
		}
	},
	watch:{
		userErp:function(){
			var company = eval('(' + window.localStorage.getItem('company') + ')')
			if(company)
			{
				this.company = company
			}
			else
			{
				this.company = this.userErp
			}
			this.loadData()
		}
	},
	data(){
		return{
			company: {},
			deliveryList: [],
			messageNo: "",
			messageCom: "",
			messageNumber: "",
			scanNum: 0,
			name: ""
		}
	},
	// 方法编写
	methods:{
		loadData:function()
	    {
			this.scanNum = 0
			var _this = this
			window.localStorage.setItem('company',JSON.stringify(this.company))
			var com = {};
			var comId = eval('(' + this.company.recordId + ')')
			com.recordId = comId;
			if(this.company.userId)
			{
				com.erpUserId = this.company.userId
			}
			else
			{
				com.erpUserId = this.userErp.userId
			}
			com.name = this.name
			// 加载绑定物料
			$('#loadingModal').modal();
    		$.ajax({
				type:"post",
			    url:ctx + "/f/wechat/produce/getDeliveryList",
			    data:JSON.stringify(com),
			    contentType:"application/json",
			    success:function(data)
			    { 
			    	_this.deliveryList = data.list;
			    	_this.scanNum = data.scanNum == null ? 0:data.scanNum;
			    	$('#loadingModal').modal('hide');
			    }
			  })
	    },
	    scanDelivery: function(item) {
	    	var _this = this
	        wx.scanQRCode({
	          needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
	          scanType: ['qrCode'], // 可以指定扫二维码qrCode还是一维码barCode，默认二者都有
	          success: function (res) {
	        	  const result = res.resultStr // 当needResult 为 1 时，扫码返回的结果
	        	  // 进入明细
	        	  if(result){
	        		  _this.messageNo = "";
	        		  _this.messageCom = "";
	        		  _this.messageNumber = "";
	        		  var message = "";
//	        		  alert(result);
	        		  let flag = 0;
	        		  // 拆分result进行验证快递公司
	        		  if(result.indexOf("deppon") != -1){
	        			  // 德邦快递
	        			  item.courierCompanyId = 70662;
	        			  message = "德邦快递";
	        		  }else if(result.indexOf("ky-express") != -1){
	        			  // 跨越速运
	        			  item.courierCompanyId = 70663;
	        			  message = "跨越速运";
	        		  }else if(result.indexOf("sf-express") != -1){
	        			  // 顺丰速运
	        			  item.courierCompanyId = 70664;
	        			  message = "顺丰速运";
	        		  }else if(result.indexOf("padtf") != -1){
	        			  // 平安达
	        			  item.courierCompanyId = 70949;
	        			  message = "平安达";
	        		  }else if(result.indexOf("uc56") != -1){
	        			  // 优速快递
	        			  message = "优速快递请扫条码";
	        			  _this.messageCom = message;
	        			  $('#static').modal()
	        			  return;
	        		  }else{
	        			  item.courierCompanyId = 70953;
	        			  message = "优速快递";
	        			  flag = 1;
	        		  }
	        		  if(flag == 0){
	        			  // 绑定快递单号
		        		  item.courierNumber = result.substring(result.indexOf("=") + 1);
		        		  if(result.indexOf("ky-express") != -1 && item.courierNumber.length > 4){
		        			  // 跨越速运截取后四位不要
		        			  item.courierNumber = item.courierNumber.substring(0,item.courierNumber.length-4);
		        		  }
	        		  }else{
	        			  if(result.indexOf(",") != -1){
	        				  var splitRes = result.split(",");
	        				  if(splitRes.length > 1){
	        					  item.courierNumber = splitRes[1];
	        				  }
	        			  }
	        		  } 
	        		  item.scanUserId = _this.userErp.userId;
	        		  $.ajax({
	  					type:"post",
	  				    url:ctx + "/f/wechat/produce/updateCourierNumber",
	  				    data:JSON.stringify(item),
	  				    contentType:"application/json",
	  				    success:function(data)
	  				    { 
	  				    	_this.loadData();
	  				    	_this.messageNo = item.no;
	  				    	_this.messageCom = message;
	  				    	_this.messageNumber = item.courierNumber;
	  				    	$('#static').modal()
	  				    	$('#loadingModal').modal('hide');
	  				    }
	  				  })
	        	  }
	          },
	          error: function () {
	            console.log('系统错误')
	          }
	        })
		},
		getWxMsg:function()
		{
			var config = {}
			config.url = location.href.split('#')[0]
			var _this = this
			$.ajax({
	     		type:"post",
	     		url:ctx + "/f/wechat/produce/getWxMsg",
	     		data:JSON.stringify(config),
	     		contentType:"application/json",
	     		success:function(data)
	     		{
	     			_this.wxConfig(data)
	     			_this.wxError()
	     			_this.wxReady()
	     		}
	     	})
		},
	 	wxConfig: function (data) {
	      wx.config({
	        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端console.log出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
	        appId: data.appId, // 必填，公众号的唯一标识
	        timestamp: data.timestamp, // 必填，生成签名的时间戳
	        nonceStr: data.nonceStr, // 必填，生成签名的随机串
	        signature: data.signature, // 必填，签名
	        jsApiList: [
	          'checkJsApi', 'startRecord', 'stopRecord', 'translateVoice', 'scanQRCode', 'openCard'
	          // 微信扫一扫接口
	        ] // 必填，需要使用的JS接口列表
	      })
	    },
	    wxError: function () {
	      wx.error(function (res) {
	        console.log('出错了：' + res.errorMsg)
	        // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
	      })
	    },
	    wxReady: function () {
	      wx.ready(function () {
	        wx.checkJsApi({
	          jsApiList: ['scanQRCode'],
	          success: function (res) {
	             console.log(res)
	          }
	        })
	        // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
	      })
	    }
	}
}