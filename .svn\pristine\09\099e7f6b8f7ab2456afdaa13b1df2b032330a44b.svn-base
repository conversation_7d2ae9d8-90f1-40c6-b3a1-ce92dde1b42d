/**
 * 
 */
package com.kyb.pcberp.modules.sys.web;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.kyb.pcberp.modules.api.service.ApiService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.eg.entity.EgProcess;
import com.kyb.pcberp.modules.sys.entity.DictItem;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import com.kyb.pcberp.modules.sys.entity.DictValueCause;
import com.kyb.pcberp.modules.sys.service.DictService;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

/**
 * 字典Controller
 * 
 * @version 2014-05-16
 */
@Controller
@RequestMapping(value = "${adminPath}/sys/dict")
public class DictController extends BaseController
{
    
    @Autowired
    private DictService dictService;

    @Autowired
    private ApiService apiService;
    
    private List<DictValue> findValuePage = null;

    
    @RequestMapping(value = {"list"})
    public String list()
    {
        return "modules/sys/dict";
    }
    
    @RequestMapping(value = {"itempage"})
    @ResponseBody
    public Page<DictItem> itemPage(@RequestBody DictItem item, HttpServletRequest request, HttpServletResponse response)
    {
        return dictService.findItemPage(item, null, request, response);
    }
    
    @RequestMapping(value = {"valuepage"})
    @ResponseBody
    public Page<DictValue> valuePage(@RequestBody DictValue value, HttpServletRequest request,
        HttpServletResponse response)
    {
        // 设置查询企业编号
        if (value != null)
        {
            value.setCompany(UserUtils.getUser().getCompany());
        }
        
        findValuePage = dictService.findItemAllList(value);
        return dictService.findValuePage(value, request, response);
    }
    
    @RequestMapping(value = {"items"})
    @ResponseBody
    public List<DictItem> getItems()
    {
        return dictService.findItems();
    }
    
    /*
     * 获取该字典项的所有字典值 zjn 2017-05-22
     **/
    @RequestMapping(value = {"Values"})
    @ResponseBody
    public List<DictValue> getValues(@RequestBody DictItem item)
    {
        findValuePage = DictUtils.getValuesByItem(item, UserUtils.getUser().getCompany());
        return findValuePage;
    }
    
    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "save") // @Valid
    @ResponseBody
    public String save(@RequestBody DictValue dict)
    {
        // 设置Company
        if (dict != null && dict.getCompany() == null)
        {
            dict.setCompany(UserUtils.getUser().getCompany());
        }
        
        String oper = StringUtils.isNotBlank(dict.getRecordId()) ? "修改" : "添加";
        dictService.saveValue(dict);
        return oper + "字典值“" + dict.getValue() + "”成功！";
    }
    
    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "delete/{id}", method = {RequestMethod.POST})
    @ResponseBody
    public String delete(@PathVariable("id") String id)
    {
        DictValue dict = new DictValue(id);
        dict.setCompany(UserUtils.getUser().getCompany());
        
        if (!dictService.isQuote(dict))
        {
            dictService.deleteValue(dict);
            
        }
        else
        {
            return "删除字典值失败,字典值有引用！";
        }
        return "删除字典值成功！";
    }
    
    /**
     * 找出字典项的最大字典值
     * 
     * @param itemId
     * @return
     */
    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "maxseqnum")
    @ResponseBody
    public Integer getMaxSeqnum(Integer itemId)
    {
        return dictService.getMaxSeqnum(itemId);
    }
    
    /**
     * 查看是否有同名的字典值
     * 
     * @param request
     * @return
     */
    @RequiresPermissions(value = {"sys:dict:view"})
    @RequestMapping(value = "querySameshortName")
    @ResponseBody
    public int querySameshortName(@RequestBody DictValue dictValue, HttpServletRequest request)
    {
        if (dictValue == null || dictValue.getValue() == null)
        {
            return 0;
        }
        
        DictValue dict = new DictValue();
        dict.setCompany(UserUtils.getUser().getCompany());
        dict.setValue(dictValue.getValue());
        dict.setRecordId(dictValue.getRecordId());
        int count = 0;
        if (dictValue.getValue().contains("-"))
        {
            for (DictValue d : findValuePage)
            {
                if (d.getValue().contains("-"))
                {
                    String[] split = d.getValue().split("-");
                    String[] valueSplit = dictValue.getValue().split("-");
                    try
                    {
                        double charOne = Double.parseDouble(valueSplit[0]);
                        double charTwo = Double.parseDouble(valueSplit[1]);
                        double charThree = Double.parseDouble(split[0]);
                        double charFour = Double.parseDouble(split[1]);
                        if ((charOne < charFour && charOne > charThree) || (charTwo < charFour && charTwo > charThree)
                            || (charFour < charTwo && charFour > charOne)
                            || (charThree < charTwo && charThree > charOne) && charTwo < charOne)
                        {
                            count = 1;
                            return count;
                        }
                    }
                    catch (Exception e)
                    {
                        count = dictService.querySameshortName(dict);
                        return count;
                    }
                }
            }
        }
        count = dictService.querySameshortName(dict);
        return count;
    }
    
    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "insertDictCompany/{id}", method = {RequestMethod.POST})
    @ResponseBody
    public String insertDictCompany(@PathVariable("id") String id)
    {
        DictValue dict = new DictValue(id);
        dict.setCompany(UserUtils.getUser().getCompany());
        
        if (!dictService.isQuote(dict))
        {
            dictService.insertDictCompany(id);
        }
        else
        {
            return "隐藏字典值失败,字典值有引用！";
        }
        return "隐藏字典值成功！";
    }
    
    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "deleteDictCompany/{id}", method = {RequestMethod.POST})
    @ResponseBody
    public String deleteDictCompany(@PathVariable("id") String id)
    {
        dictService.deleteDictCompany(id);
        DictValue dict = new DictValue(id);
        dict.setCompany(UserUtils.getUser().getCompany());
        return "显示字典值成功！";
    }
	
	@RequestMapping(value = "copyCompany")
    @ResponseBody
    public void copyCompany()
    {
        dictService.copyCompany();
    }
    
    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "getNameList", method = {RequestMethod.POST})
    @ResponseBody
    public List<EgProcess> getNameList(@RequestBody DictItem dictItem)
    {
        return dictService.getNameList(dictItem);
    }
    
    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "saveCause") // @Valid
    @ResponseBody
    public Map<String, Object> saveCause(@RequestBody DictValueCause dict)
    {
        // 设置Company
        Map<String, Object> result = new HashMap<>();
        if (dict != null && dict.getCompany() == null)
        {
            dict.setCompany(UserUtils.getUser().getCompany());
        }
        if (!CollectionUtils.isEmpty(dict.getTestTypeList()))
        {
            String types = "";
            for (String type : dict.getTestTypeList())
            {
                if ("".equals(types))
                {
                    types = type;
                }
                else
                {
                    types = types + "," + type;
                }
            }
            dict.setTestTypeIds(types);
        }
        String info = dictService.saveCause(dict);
        if (StringUtils.isEmpty(info))
        {
            result.put("success", true);
            result.put("obj", dict);
        }
        else
        {
            result.put("msg", info);
        }
        return result;
    }
    
    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "getCauseList") // @Valid
    @ResponseBody
    public List<DictValueCause> getCauseList(@RequestBody DictValueCause dict)
    {
        // 设置Company
        if (dict != null && dict.getCompany() == null)
        {
            dict.setCompany(UserUtils.getUser().getCompany());
        }
        List<DictValueCause> list = dictService.getCauseList(dict);
        return list;
    }
    
    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "setPrecent") 
    @ResponseBody
    public String setPrecent(@RequestBody DictValue dictValue)
    {
        return dictService.setPrecent(dictValue);
    }

    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "setAttributeSave")
    @ResponseBody
    public String setAttributeSave(@RequestBody DictValue dictValue)
    {
        return dictService.setAttributeSave(dictValue);
    }

    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "saveItem")
    @ResponseBody
    public Map<String,Object> saveItem(@RequestBody DictItem dictItem)
    {
        return dictService.saveItem(dictItem);
    }

    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "deleteItem")
    @ResponseBody
    public Map<String,Object> deleteItem(@RequestBody DictItem dictItem)
    {
        return dictService.deleteItem(dictItem);
    }

    @RequestMapping(value = "getProductSchedule")
    @ResponseBody
    public Map<String,Object> getProductSchedule()
    {
        return apiService.getProductSchedule();
    }
}
