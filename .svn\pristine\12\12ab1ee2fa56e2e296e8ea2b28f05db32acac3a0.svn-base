<%@ page contentType="text/html;charset=UTF-8"%>
<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">统计报表</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="report.deliveryassessment">交货达成率报表</a>
        </li>
    </ul>
</div>

<tabset class="tabset-margin-top">
    <tab heading="交货达成率报表" active="ctrl.tabs.viewForm.active">
        <div class="panel panel-default">
			<div class="panel-heading font-blue-hoki">查询</div>
			<div class="panel-body">
				<form class="form-horizontal">
					<div class="row">
						<div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">账期月份：</label>
                            <div class="col-sm-7 col-md-8">
                                <ui-select ng-model="ctrl.periodQuery" theme="bootstrap">
                                    <ui-select-match placeholder="请选择...">{{$select.selected}}</ui-select-match>
                                    <ui-select-choices repeat="item in ctrl.periodList | filter: $select.search">
                                        <div ng-bind-html="item | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>
<%--						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">组织类型：</label>
								<div class="col-sm-7 col-md-8">
									<select  class="form-control" ng-model="ctrl.organizationTypeQuery">
										<option value="1">销售公司</option>
										<option value="2">部门</option>
									</select>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4" ng-if="ctrl.organizationTypeQuery == '1'">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">销售公司：</label>
								<div class="col-sm-7 col-md-8">
									<ui-select ng-model="ctrl.saleComIdQuery" theme="bootstrap">
										<ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
										<ui-select-choices repeat="item.recordId as item in ctrl.saleComList | filter: $select.search">
											<div ng-bind-html="item.name | highlight: $select.search"></div>
										</ui-select-choices>
									</ui-select>
								</div>
							</div>
						</div>--%>
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">下单部门：</label>
								<div class="col-sm-7 col-md-8">
									<ui-select ng-model="ctrl.deptQuery" theme="bootstrap">
										<ui-select-match placeholder="请选择...">{{$select.selected.groupOrgName}}</ui-select-match>
										<ui-select-choices repeat="item.groupOrgId as item in ctrl.deptList | filter: $select.search">
											<div ng-bind-html="item.groupOrgName | highlight: $select.search"></div>
										</ui-select-choices>
									</ui-select>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">合同编号：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" ng-model="ctrl.contratNoQuery" disable-auto-validate="true" />
								</div>
							</div>
						</div>
		
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">生产编号：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" ng-model="ctrl.craftNoQuery" disable-auto-validate="true" />
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">客户型号：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" ng-model="ctrl.customerModuleQuery" />
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">曝光/丝印：</label>
								<div class="col-sm-7 col-md-8">
									<select  class="form-control" ng-model="ctrl.processValueIdQuery">
										<option value="">所有</option>
										<option value="1">曝光</option>
										<option value="2">丝印</option>
									</select>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-12">
								<button class="btn btn-default btn-default-width pull-right"
									ng-click="ctrl.handlePageData()">
									<i class="fa fa-search"></i> 查&nbsp;询
								</button>
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>
		<div class="portlet light bordered">
			<div class="portlet-title">
				<div class="caption font-blue-hoki">
					<%--{{ctrl.showTitle}}&nbsp;&nbsp;--%>
						部门汇总列表
<%--					<span ng-if="ctrl.sumData">
						<span ng-if="ctrl.sumData.totalAmountshipment">
							出货总款数:{{ctrl.sumData.totalAmountshipment}}(样板总款数：{{ctrl.sumData.totalModels}},量产总款数：{{ctrl.sumData.totalBatchProduction}})
						</span>
						<span ng-if="ctrl.sumData.totalAreashipment">
							出货总面积:{{ctrl.sumData.totalAreashipment}}(曝光总面积：{{ctrl.sumData.totalShippedArea}},丝印总面积：{{ctrl.sumData.totalSilkScreenArea}})
						</span>
					</span>--%>

				</div><br><br><br><br>
					<span>
							交货达成率 = ( 达标款数 / 订单款数 * 100 )
					</span>&nbsp;&nbsp;&nbsp;&nbsp;
					<span>
							奖惩金额 = ( ( 交货达成率-交货达成率目标值 ) / ((四舍五入取整(奖 / 惩超出值 )) * ( 奖 / 惩平米价 ) * 订单面积)
					</span>
				<div id="InvenMasterStep2" class="actions">
					<div class="portlet-input input-inline input-small" ng-if="ctrl.right.manage && !ctrl.checkOutFlag && ctrl.btFlag">
						<button type="button" class="btn green btn-default-width" ng-click="ctrl.addCheckoutOpen()"><i class="fa fa-check font-green"></i>结账</button>
					</div>
					<div class="portlet-input input-inline input-small">
						<form action="a/report/appraisal/exportDeliveryAssessmentDetailList" method="post" enctype="multipart/form-data" target="hidden_frame">
							<button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出</button>
							<input type="text" ng-show="false" name="period" value="{{ctrl.periodQuery}}"/>
							<input type="text" ng-show="false" name="type" value="1"/>
							<input type="text" ng-show="false" name="contractNo" value="{{ctrl.contratNoQuery}}"/>
							<input type="text" ng-show="false" name="craftNo" value="{{ctrl.craftNoQuery}}"/>
							<input type="text" ng-show="false" name="customerModule" value="{{ctrl.customerModuleQuery}}"/>
							<input type="text" ng-show="false" name="organizationType" value="{{ctrl.organizationTypeQuery}}"/>
							<input type="text" ng-show="false" name="saleComId" value="{{ctrl.saleComIdQuery}}"/>
							<input type="text" ng-show="false" name="deptId" value="{{ctrl.deptQuery}}"/>
							<input type="text" ng-show="false" name="processValueId" value="{{ctrl.processValueIdQuery}}"/>
							<input type="text" ng-show="false" name="queryAll" value="{{ctrl.queryAll}}"/>
							<input type="text" ng-show="false" name="orderBy" value="c.orderDate DESC"/>
							<iframe name="hidden_frame1" style="display:none"></iframe>
						</form>
					</div>
				</div>
			</div>
			<div class="panel panel-default">
				<div class="panel-body">
							<span>
								目标:交货达成率：{{ctrl.targetValue.value}}%&nbsp;&nbsp;(含)以上，每天出货不低于 {{ctrl.shippedDaliyArea.value + ctrl.silkScreenDaliyArea.value}} 平米(其中曝光工艺每天不低于 {{ctrl.shippedDaliyArea.value}} 平米，丝印工艺每天不低于 {{ctrl.silkScreenDaliyArea.value}} 平米)。
							每天出货 {{ctrl.sampleDaliyCount.value + ctrl.batchProductionDaliyCount.value}}款，样板每天{{ctrl.sampleDaliyCount.value}}款，量产每天{{ctrl.batchProductionDaliyCount.value}}款;
						</span><br><br>
						<span>
							考核:交货达成率要求达到 {{ctrl.targetValue.value}}%，交货达成率每降低 {{ctrl.belowRate.value}}%，减 {{ctrl.targetValuePunishment.value}}元/平米，每提高 {{ctrl.excessRate.value}}% 奖励 {{ctrl.targetValueReward.value}}元/平米(此奖励由销售公司支付);
						</span>
				</div>
			</div>
			<div class="portlet-body">
				<div class="table-scrollable" style="margin-top:0px !important">
					<table class="table table-striped table-bordered table-condensed table-advance table-hover" >
						<thead>
						<tr class="heading">
							<th>组织名称</th>
							<th>订单面积(㎡)</th>
							<th>订单款数</th>
							<th>未送货款数</th>
							<th>逾期款数</th>
							<th>达标款数</th>
							<th>交货达成率</th>
							<th>奖惩金额</th>
							<th>原因</th>
						</tr>
						</thead>
						<tbody>
						<tr ng-repeat="row in ctrl.organizationSumList">
							<td ng-bind="row.name"></td>
							<td ng-bind="row.deliveryArea"></td>
							<td ng-bind="row.deliveryPayment"></td>
							<td ng-bind="row.normalAmount"></td>
							<td ng-bind="row.overduePayments"></td>
							<td ng-bind="row.eligibleFunds"></td>
							<td ng-bind="row.deliveryAchievementRate"></td>
							<td ng-bind="row.rewardsPunishmentAmount"></td>
							<td ng-bind="row.reason"></td>
						</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
        <div class="portlet light bordered">
            <div class="portlet-title">
				<div class="caption font-blue-hoki">交货达成率列表</div>
				<div class="caption" ng-if="ctrl.page.data.list && ctrl.page.data.list.length> 0">
					&nbsp;&nbsp;&nbsp;&nbsp;样品首批平均交期:&nbsp;{{ctrl.sampleAveDays}}&nbsp;
					&nbsp;&nbsp;&nbsp;&nbsp;新单首批平均交期:&nbsp;{{ctrl.newOrderAveDays}}&nbsp;
					&nbsp;&nbsp;&nbsp;&nbsp;返单首批平均交期:&nbsp;{{ctrl.reorderAveDays}}&nbsp;
				</div>
                <div class="actions">
					<div class="portlet-input input-inline input-small">
						<form action="a/report/appraisal/deliveryassessmentExport" method="POST" enctype="multipart/form-data" target="hidden_frame1">
							<div>
								<button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出</button>
								<input type="text" ng-show="false" name="period" value="{{ctrl.periodQuery}}"/>
								<input type="text" ng-show="false" name="contractNo" value="{{ctrl.contratNoQuery}}"/>
								<input type="text" ng-show="false" name="craftNo" value="{{ctrl.craftNoQuery}}"/>
								<input type="text" ng-show="false" name="customerModule" value="{{ctrl.customerModuleQuery}}"/>
								<input type="text" ng-show="false" name="organizationType" value="{{ctrl.organizationTypeQuery}}"/>
								<input type="text" ng-show="false" name="saleComId" value="{{ctrl.saleComIdQuery}}"/>
								<input type="text" ng-show="false" name="deptId" value="{{ctrl.deptQuery}}"/>
								<input type="text" ng-show="false" name="processValueId" value="{{ctrl.processValueIdQuery}}"/>
								<input type="text" ng-show="false" name="queryAll" value="{{ctrl.queryAll}}"/>
								<input type="text" ng-show="false" name="orderBy" value="c.orderDate DESC"/>
								<iframe name="hidden_frame1" style="display:none"></iframe>
							</div>
						</form>
					</div>
                </div>
            </div>
            <div class="portlet-body">
                <div class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
						<tr class="heading">
							<th style="text-align: center;" colspan="2">销售信息</th>
							<th style="text-align: center;" colspan="10">订单信息</th>
							<th style="text-align: center;" colspan="11">交货信息</th>
						</tr>
                        <tr>
							<th style="text-align: center;" colspan="1">销售公司</th>
							<th style="text-align: center;" colspan="1">终端客户</th>

                            <th  style="text-align: center;" colspan="1">合同编号</th>
							<th  style="text-align: center;" colspan="1">订单日期</th>
							<th  style="text-align: center;" colspan="1">客户订单号</th>
							<th  style="text-align: center;" colspan="1">生产编号</th>
							<th  style="text-align: center;" colspan="1">订单面积(㎡)</th>
							<th  style="text-align: center;" colspan="1">客户型号</th>
							<th  style="text-align: center;" colspan="1">曝光/丝印</th>
							<th  style="text-align: center;" colspan="1">订单类型</th>
							<th  style="text-align: center;" colspan="1">部门</th>
                            <th  style="text-align: center;" colspan="1">业务员</th>

							<th  style="text-align: center;" colspan="1">评估开始日期</th>
							<th  style="text-align: center;" colspan="1">首批交期</th>
							<th  style="text-align: center;" colspan="1">首批天数</th>
                            <th  style="text-align: center;" colspan="1">最终交期</th>
							<th  style="text-align: center;" colspan="1">最终天数</th>
							<th  style="text-align: center;" colspan="1">首批送货时间</th>
							<th  style="text-align: center;" colspan="1">首批实际天数</th>
							<th  style="text-align: center;" colspan="1">首批超期天数</th>
							<th  style="text-align: center;" colspan="1">最终送货时间</th>
							<th  style="text-align: center;" colspan="1">最终实际天数</th>
							<th  style="text-align: center;" colspan="1">最终超期天数</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in ctrl.page.data.list">
							<td style="text-align: center;" rowspan="1" ng-bind="row.saleComName"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.saleCus"></td>

                        	<td style="text-align: center;" rowspan="1" ng-bind="row.contractNo"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.orderDateStr"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.customerPo"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.craftNo"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.orderArea"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.customerModule"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.processValue"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.orderTypeValue"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.deptName"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.saleUserName"></td>

							<td style="text-align: center;" rowspan="1" ng-bind="row.estimateStartDateStr"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.firstBatchDateStr"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.firstDays"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.estimateDateStr"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.finalDays"></td>
							<td style="text-align: center;" rowspan="1">
								<a ng-bind="row.firstSentTimeStr" href="javascript:void(0)" ng-click="ctrl.showProductStoreData(row)"></a>
							</td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.firstSentDay"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.firstOverdueDays"></td>
							<td style="text-align: center;" rowspan="1">
								<a ng-bind="row.sentTimeStr" href="javascript:void(0)" ng-click="ctrl.showProductStoreData(row)"></a>
							</td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.actualDays"></td>
							<td style="text-align: center;" rowspan="1" ng-bind="row.overdueDays"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
				<div class="row page-margin-top">
					<div class="col-md-12 col-lg-6" id="step5">
						<span class="inline">每&nbsp;页</span>
						<select
								class="form-control inline" style="margin-top: 8px; width: 100px;"
								disable-valid-styling="true" disable-invalid-styling="true"
								ng-model="ctrl.page.pageSize"
								ng-change="ctrl.pageSizeChange()"
								ng-options="pageSizeOption for pageSizeOption in ctrl.page.pageSizeOptions">
						</select>
						<span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;
                    	当前显示{{ctrl.page.data.startCount}} / {{ctrl.page.data.endCount}}条，共 {{ctrl.page.data.count}} 条</span>
					</div>
					<div class="col-md-12 col-lg-6">
						<paging class="pull-right" page="ctrl.page.data.pageNo"
								page-size="ctrl.page.data.pageSize"
								total="ctrl.page.data.count" adjacent="1" dots="..."
								scroll-top="false" hide-if-empty="false" ul-class="pagination"
								active-class="active" disabled-class="disabled"
								show-prev-next="true"
								paging-action="ctrl.doPage(page, pageSize, total)"></paging>
					</div>
				</div>
            </div>
        </div>
    </tab>
  <tab heading="交货达成率规则设置" active="ctrl.tabs.ruleForm.active" ng-click="ctrl.loadRuleData()">
        <div class="portlet light bordered">
			<div class="portlet-title">
				<div class="caption font-blue-hoki">版本信息
				</div>
				<div class="actions">
					<div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
						<button type="button" class="btn btn-primary btn-default-width" ng-click="ctrl.saveVesionOpen(1)"><i class="fa fa-save"></i> 保&nbsp;存</button>
					</div>
					<div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
						<button type="button" class="btn btn-primary btn-default-width" ng-click="ctrl.saveVesionOpen(2)"><i class="fa fa-save"></i>创建新版本</button>
					</div>
				</div>
			</div>
			<div class="portlet-body">
				<div class="form-horizontal">
					<div class="row">
						<div class="col-md-12 col-lg-12">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">版本：</label>
								<div class="col-sm-7 col-md-2">
									<ui-select theme="bootstrap" ng-model="ctrl.version">
										<ui-select-match  placeholder="选择版本...">{{$select.selected.versionDateStr}}</ui-select-match>
										<ui-select-choices repeat="version in ctrl.versionList | filter: $select.search">
											<div ng-bind-html="version.versionDateStr | highlight: $select.search"></div>
										</ui-select-choices>
									</ui-select>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
        </div>
	  <form class="form-horizontal">
		  <div class="portlet light bordered">
			  <div class="portlet-title">
				  <div class="caption font-blue-hoki">交货达成率目标值</div>
			  </div>
			  <div class="portlet-body">
				  <div class="form-horizontal">
					  <div class="row" ng-if="ctrl.targetValue && ctrl.targetValue.recordId">
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">
									  <span class="text-danger">*</span>交货达成率目标值
									  <span ng-if="ctrl.targetValue.unit">({{ctrl.targetValue.unit}})</span>:
								  </label>
								  <div class="col-sm-7 col-md-2">
									  <input class="form-control" ng-model="ctrl.targetValue.value" ng-onlydecimals/>
								  </div>
							  </div>
						  </div>
					  </div>
					  <div class="row" ng-if="ctrl.excessRate && ctrl.excessRate.recordId">
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">
									  <span class="text-danger">*</span>超出率
									  <span ng-if="ctrl.excessRate.unit">({{ctrl.excessRate.unit}})</span>:
								  </label>
								  <div class="col-sm-7 col-md-2">
									  <input class="form-control" ng-model="ctrl.excessRate.value" ng-onlydecimals/>
								  </div>
							  </div>
						  </div>
					  </div>
					  <div class="row" ng-if="ctrl.targetValueReward && ctrl.targetValueReward.recordId">
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">
									  <span class="text-danger">*</span>超出目标值每平米奖励
									  <span ng-if="ctrl.targetValueReward.unit">({{ctrl.targetValueReward.unit}})</span>:
								  </label>
								  <div class="col-sm-7 col-md-2">
									  <input class="form-control" ng-model="ctrl.targetValueReward.value" ng-onlydecimals/>
								  </div>
							  </div>
						  </div>
					  </div>
					  <div class="row" ng-if="ctrl.belowRate && ctrl.belowRate.recordId">
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">
									  <span class="text-danger">*</span>低于率
									  <span ng-if="ctrl.belowRate.unit">({{ctrl.belowRate.unit}})</span>:
								  </label>
								  <div class="col-sm-7 col-md-2">
									  <input class="form-control" ng-model="ctrl.belowRate.value" ng-onlydecimals/>
								  </div>
							  </div>
						  </div>
					  </div>
					  <div class="row" ng-if="ctrl.targetValuePunishment && ctrl.targetValuePunishment.recordId">
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">
									  <span class="text-danger">*</span>低于目标值每平米惩罚
									  <span ng-if="ctrl.targetValuePunishment.unit">({{ctrl.targetValuePunishment.unit}})</span>:
								  </label>
								  <div class="col-sm-7 col-md-2">
									  <input class="form-control" ng-model="ctrl.targetValuePunishment.value" ng-onlydecimals/>
								  </div>
							  </div>
						  </div>
					  </div>
				  </div>
			  </div>
		  </div>
		  <div class="portlet light bordered">
			  <div class="portlet-title">
				  <div class="caption font-blue-hoki">曝光/丝印日出货平米数</div>
			  </div>
			  <div class="portlet-body">
				  <div class="form-horizontal">
					  <div class="row" ng-if="ctrl.shippedDaliyArea && ctrl.shippedDaliyArea.recordId">
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">
									  <span class="text-danger">*</span>曝光日出货平米数
									  <span ng-if="ctrl.shippedDaliyArea.unit">({{ctrl.shippedDaliyArea.unit}})</span>:
								  </label>
								  <div class="col-sm-7 col-md-2">
									  <input class="form-control" ng-model="ctrl.shippedDaliyArea.value" ng-onlydecimals/>
								  </div>
							  </div>
						  </div>
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">判断条件:</label>
								  <div class="col-sm-7 col-md-2">
									  <ui-select theme="bootstrap" ng-model="ctrl.shippedDaliyArea.judgingCondition">
										  <ui-select-match  placeholder="选择条件...">{{$select.selected.value}}</ui-select-match>
										  <ui-select-choices repeat="item.id as item in ctrl.judgingConditionList | filter: $select.search">
											  <div ng-bind-html="item.value | highlight: $select.search"></div>
										  </ui-select-choices>
									  </ui-select>
								  </div>
							  </div>
						  </div>
					  </div>
					  <div class="row" ng-if="ctrl.silkScreenDaliyArea && ctrl.silkScreenDaliyArea.recordId">
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">
									  <span class="text-danger">*</span>丝印日出货平米数
									  <span ng-if="ctrl.silkScreenDaliyArea.unit">({{ctrl.silkScreenDaliyArea.unit}})</span>:
								  </label>
								  <div class="col-sm-7 col-md-2">
									  <input class="form-control" ng-model="ctrl.silkScreenDaliyArea.value" ng-onlydecimals/>
								  </div>
							  </div>
						  </div>
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">判断条件:</label>
								  <div class="col-sm-7 col-md-2">
									  <ui-select theme="bootstrap" ng-model="ctrl.silkScreenDaliyArea.judgingCondition">
										  <ui-select-match  placeholder="选择条件...">{{$select.selected.value}}</ui-select-match>
										  <ui-select-choices repeat="item.id as item in ctrl.judgingConditionList | filter: $select.search">
											  <div ng-bind-html="item.value | highlight: $select.search"></div>
										  </ui-select-choices>
									  </ui-select>
								  </div>
							  </div>
						  </div>
					  </div>
				  </div>
			  </div>
		  </div>
		  <div class="portlet light bordered">
			  <div class="portlet-title">
				  <div class="caption font-blue-hoki">量产/样品日出货款数</div>
			  </div>
			  <div class="portlet-body">
				  <div class="form-horizontal">
					  <div class="row" ng-if="ctrl.batchProductionDaliyCount && ctrl.batchProductionDaliyCount.recordId">
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">
									  <span class="text-danger">*</span>量产日出货款数
									  <span ng-if="ctrl.batchProductionDaliyCount.unit">({{ctrl.batchProductionDaliyCount.unit}})</span>:
								  </label>
								  <div class="col-sm-7 col-md-2">
									  <input class="form-control" ng-model="ctrl.batchProductionDaliyCount.value" ng-onlydecimals/>
								  </div>
							  </div>
						  </div>
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">判断条件:</label>
								  <div class="col-sm-7 col-md-2">
									  <ui-select theme="bootstrap" ng-model="ctrl.batchProductionDaliyCount.judgingCondition">
										  <ui-select-match  placeholder="选择条件...">{{$select.selected.value}}</ui-select-match>
										  <ui-select-choices repeat="item.id as item in ctrl.judgingConditionList | filter: $select.search">
											  <div ng-bind-html="item.value | highlight: $select.search"></div>
										  </ui-select-choices>
									  </ui-select>
								  </div>
							  </div>
						  </div>
					  </div>
					  <div class="row" ng-if="ctrl.sampleDaliyCount && ctrl.sampleDaliyCount.recordId">
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">
									  <span class="text-danger">*</span>样品日出货款数
									  <span ng-if="ctrl.sampleDaliyCount.unit">({{ctrl.sampleDaliyCount.unit}})</span>:
								  </label>
								  <div class="col-sm-7 col-md-2">
									  <input class="form-control" ng-model="ctrl.sampleDaliyCount.value" ng-onlydecimals/>
								  </div>
							  </div>
						  </div>
						  <div class="col-md-6 col-lg-6">
							  <div class="form-group">
								  <label class="col-sm-3 col-md-4 control-label">判断条件:</label>
								  <div class="col-sm-7 col-md-2">
									  <ui-select theme="bootstrap" ng-model="ctrl.sampleDaliyCount.judgingCondition">
										  <ui-select-match  placeholder="选择条件...">{{$select.selected.value}}</ui-select-match>
										  <ui-select-choices repeat="item.id as item in ctrl.judgingConditionList | filter: $select.search">
											  <div ng-bind-html="item.value | highlight: $select.search"></div>
										  </ui-select-choices>
									  </ui-select>
								  </div>
							  </div>
						  </div>
					  </div>
				  </div>
			  </div>
		  </div>
	  </form>
    </tab>
</tabset>


<div class="row">
	<div class="col-md-12">
		<div id="vesionstatic" class="modal fade" tabindex="-1" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">提示</h4>
					</div>
					<div class="modal-body">
						<p><span ng-bind="ctrl.message"></span></p>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn btn-default" ng-click="ctrl.saveVesion()">确定</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="addCheckoutStatic" class="modal fade" tabindex="-1" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">提示</h4>
					</div>
					<div class="modal-body">
						<p><span ng-bind="ctrl.message"></span></p>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn btn-default" ng-click="ctrl.addCheckOut()">确定</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div class="modal fade" id="showProductStoreStatic" tabindex="-1" role="dialog" aria-hidden="true">
			<div class="modal-dialog modal-full">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title"><span class="text-primary">
                        	出入库记录
                        </span></h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 5px;">
						<form class="form-horizontal">
							<div class="portlet light bordered">
								<div class="portlet-body">
									<div class="row form-group">
										<div class="table-scrollable">
											<table class="table table-striped table-bordered table-condensed table-advance table-hover">
												<thead>
												<tr class="heading">
													<th>出入库类型</th>
													<th>数量</th>
												</tr>
												<tr>
													<td>
														<input type="text" class="form-control" ng-model="ctrl.exportInoutTypeQuery">
													</td>
													<td>
														<input type="text" class="form-control" ng-model="ctrl.quantityQuery">
													</td>
												</tr>
												</thead>
												<tbody>
												<tr ng-repeat="item in ctrl.showProductStoreList | filter:{'exportInoutType':ctrl.exportInoutTypeQuery,'quantity':ctrl.quantityQuery}" class="success">
													<td>{{item.exportInoutType}}</td>
													<td>{{item.quantity}}</td>
												</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">提示</h4>
                    </div>
                    <div class="modal-body">
                        <p><span ng-bind="ctrl.message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>