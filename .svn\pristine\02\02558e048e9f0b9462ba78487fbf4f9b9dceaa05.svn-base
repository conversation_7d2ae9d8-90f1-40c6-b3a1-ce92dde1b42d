package com.kyb.pcberp.modules.icloud.erp.erp.dao;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.icloud.contract.pojo.Icloud_ContractVo;
import com.kyb.pcberp.modules.icloud.contract.pojo.Icloud_GroupCenter;
import com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_ContractDetailErp;

@MyBatisDao
public interface Icloud_ContractDetailErpDao
{
    void updateStatus(Icloud_ContractDetailErp contractDetailErp);

    Icloud_ContractDetailErp getDetialByGroupCenter(Icloud_GroupCenter groupCenter);

    Icloud_ContractDetailErp getContractDetailErpData(Icloud_ContractDetailErp contractDetail);

    Icloud_ContractDetailErp getUrgentApprovalData(Icloud_ContractDetailErp contractDetail);

    Double getSalePrecent(Icloud_ContractVo contractVo);

    Icloud_ContractDetailErp getSumFeeAmount(Icloud_ContractVo contractVo);

    Icloud_ContractDetailErp getNegotiatedApprovalData(Icloud_ContractDetailErp contractDetailErp);
}
