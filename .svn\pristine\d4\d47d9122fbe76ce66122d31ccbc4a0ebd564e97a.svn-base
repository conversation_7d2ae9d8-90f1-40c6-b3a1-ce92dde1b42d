<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.stock.dao.MaterialDepositRecordDao">
	<sql id="materialDepositRecordColumns">
		a.recordId AS "recordId",
		a.companyId AS "company.recordId",
		a.contractDetailId AS "contractDetail.recordId",
		a.customerId AS "customer.recordId",
		a.materialId AS "material.recordId",
		a.quantity AS "quantity",
		a.type AS "type",
		a.status AS "status",
		a.activeFlag AS "activeFlag",
		a.createdBy AS "createdBy.recordId",
		a.createdDate AS "createdDate"
	</sql>
	
	<resultMap type="MaterialDepositRecord" id="materialDepositResult">
	
	</resultMap>
	
	<select id="findList" resultType="MaterialDepositRecord">
		SELECT 
			<include refid="materialDepositRecordColumns"/>,
			c.recordId AS "contractDetail.craft.recordId",
			c.customerModel AS "contractDetail.craft.customerModel",
			c.no AS "contractDetail.craft.no",
			d.recordId AS "contractDetail.contract.recordId",
			d.no AS "contractDetail.contract.no",
			e.shortName AS "customer.shortName",
			g.userName AS "createdBy.userName"
		FROM md_store_deposit a
		LEFT JOIN sl_contract_detail b ON a.contractDetailId = b.recordId
		LEFT JOIN sl_contract_craft c ON c.recordId = b.craftId
		LEFT JOIN sl_contract d ON d.recordId = b.contractId
		LEFT JOIN md_customer e ON a.customerId = e.recordId
		LEFT JOIN md_material f ON f.recordId = a.materialId
		LEFT JOIN sm_user g ON g.recordId = a.createdBy
		<where>
		    AND a.activeFlag = #{DEL_FLAG_NORMAL}
		    <if test="company != null and company.recordId != null">
			   AND a.companyId = #{company.recordId}
			</if> 
			<if test="contractDetail != null and contractDetail.craft != null and contractDetail.craft.no != null ">
			   AND REPLACE(c.no," ","") LIKE CONCAT('%',REPLACE(#{contractDetail.craft.no}," ",""),'%') 
			</if>
			<if test="contractDetail != null and contractDetail.contract != null and contractDetail.contract.no != null">
			   AND REPLACE(d.no," ","") LIKE CONCAT('%',REPLACE(#{contractDetail.contract.no}," ",""),'%')
			</if>
			<if test="customer != null and customer.shortName != null">
			   AND REPLACE(e.shortName," ","") LIKE CONCAT('%',REPLACE(#{customer.shortName}," ",""),'%')
			</if>
			<if test="inoutTimeStartQr != null and inoutTimeStartQr != ''">
				AND a.createdDate >= #{inoutTimeStartQr}
			</if>
			<if test="inoutTimeEndQr != null and inoutTimeEndQr != ''">
				AND a.createdDate <![CDATA[<=]]> #{inoutTimeEndQr}
			</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (
					a.createdBy = #{createdBy.recordId}
					OR d.createdBy = #{createdBy.recordId}
					OR e.createdBy = #{createdBy.recordId}
					OR (!ISNULL(e.salesman) AND e.salesman = #{createdBy.recordId})
					OR e.recordId IN (SELECT customerId FROM sm_customer_salesAssistant WHERE userId = #{createdBy.recordId} AND activeFlag = 1)
				)
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="export" resultType="com.kyb.pcberp.modules.stock.vo.DepositVo">
		SELECT 
			a.quantity AS "quantity",
			CASE a.type
			WHEN '1' THEN '确认送货单' 
			WHEN '2' THEN '生产入库' 
			WHEN '3' THEN '采购入库' 
			WHEN '4' THEN '退货入库' 
			WHEN '5' THEN '补货入库' END AS "type",
			a.createdDate AS "depositDate",
			c.no AS "materialNo",
			d.no AS "contractNo",
			e.shortName AS "customerShortName",
			g.userName AS "opUser"
		FROM md_store_deposit a
		LEFT JOIN sl_contract_detail b ON a.contractDetailId = b.recordId
		LEFT JOIN sl_contract_craft c ON c.recordId = b.craftId
		LEFT JOIN sl_contract d ON d.recordId = b.contractId
		LEFT JOIN md_customer e ON a.customerId = e.recordId
		LEFT JOIN md_material f ON f.recordId = a.materialId
		LEFT JOIN sm_user g ON g.recordId = a.createdBy
		<where>
		    AND a.activeFlag = #{DEL_FLAG_NORMAL}
		    <if test="company != null and company.recordId != null">
			   AND a.companyId = #{company.recordId}
			</if> 
			<if test="contractDetail != null and contractDetail.craft != null and contractDetail.craft.no != null ">
			   AND REPLACE(c.no," ","") LIKE CONCAT('%',REPLACE(#{contractDetail.craft.no}," ",""),'%') 
			</if>
			<if test="contractDetail != null and contractDetail.contract != null and contractDetail.contract.no != null">
			   AND REPLACE(d.no," ","") LIKE CONCAT('%',REPLACE(#{contractDetail.contract.no}," ",""),'%')
			</if>
			<if test="customer != null and customer.shortName != null">
			   AND REPLACE(e.shortName," ","") LIKE CONCAT('%',REPLACE(#{customer.shortName}," ",""),'%')
			</if>
			<if test="inoutTimeStartQr != null and inoutTimeStartQr != ''">
				AND a.createdDate >= #{inoutTimeStartQr}
			</if>
			<if test="inoutTimeEndQr != null and inoutTimeEndQr != ''">
				AND a.createdDate <![CDATA[<=]]> #{inoutTimeEndQr}
			</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (
					a.createdBy = #{createdBy.recordId}
					OR d.createdBy = #{createdBy.recordId}
					OR e.createdBy = #{createdBy.recordId}
					OR (!ISNULL(e.salesman) AND e.salesman = #{createdBy.recordId})
					OR e.recordId IN (SELECT customerId FROM sm_customer_salesAssistant WHERE userId = #{createdBy.recordId} AND activeFlag = 1)
				)
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<insert id="insert">
		INSERT INTO md_store_deposit(
			companyId,
			contractDetailId,
			customerId,
			materialId,
			quantity,
			type,
			status,
			activeFlag,
			createdBy,
			createdDate
		)VALUES(
			#{company.recordId},
			#{contractDetail.recordId},
			#{customer.recordId},
			#{material.recordId},
			#{quantity},
			#{type},
			#{status},
			#{activeFlag},
			#{createdBy.recordId},
			#{createdDate}
		)
	
	</insert>
</mapper>