package com.kyb.pcberp.modules.purch.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

/**
 * 供应商文件表
 * 
 * <AUTHOR>
 * @version 2017-05-12
 */
public class SupplierFile extends DataEntity<SupplierFile>
{
    private static final long serialVersionUID = 1L;
    
    private String orgFileName;
    
    private String realFileName;
    
    private String fileUrl;
    
    private String tempUrl;
    
    private String no; // 供应商
    
    private String type;// 1是供应商 2是客诉单
    
    private String period;// 对账周期
    
    private String payAccountId;// 对账单Id
    
    private String attId;
    
    private Integer typeInt;//1:供应商附件，2:备份的供应商附件
    
    private boolean approvalFlag;
    
    public String getOrgFileName()
    {
        return orgFileName;
    }
    
    public String getRealFileName()
    {
        return realFileName;
    }
    
    public String getFileUrl()
    {
        return fileUrl;
    }
    
    public String getTempUrl()
    {
        return tempUrl;
    }
    
    public void setOrgFileName(String orgFileName)
    {
        this.orgFileName = orgFileName;
    }
    
    public void setRealFileName(String realFileName)
    {
        this.realFileName = realFileName;
    }
    
    public void setFileUrl(String fileUrl)
    {
        this.fileUrl = fileUrl;
    }
    
    public void setTempUrl(String tempUrl)
    {
        this.tempUrl = tempUrl;
    }
    
    public String getNo()
    {
        return no;
    }
    
    public void setNo(String no)
    {
        this.no = no;
    }
    
    public String getType()
    {
        return type;
    }
    
    public void setType(String type)
    {
        this.type = type;
    }
    
    public String getPeriod()
    {
        return period;
    }
    
    public void setPeriod(String period)
    {
        this.period = period;
    }
    
    public String getPayAccountId()
    {
        return payAccountId;
    }
    
    public void setPayAccountId(String payAccountId)
    {
        this.payAccountId = payAccountId;
    }

    public String getAttId()
    {
        return attId;
    }

    public void setAttId(String attId)
    {
        this.attId = attId;
    }

    public Integer getTypeInt()
    {
        return typeInt;
    }

    public void setTypeInt(Integer typeInt)
    {
        this.typeInt = typeInt;
    }

    public boolean getApprovalFlag()
    {
        return approvalFlag;
    }

    public void setApprovalFlag(boolean approvalFlag)
    {
        this.approvalFlag = approvalFlag;
    }
    
}
