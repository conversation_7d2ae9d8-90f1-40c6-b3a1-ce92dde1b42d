package com.kyb.pcberp.modules.report.entity;

import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ReportCapacityRecordVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/2/14 8:46
 * @Version 1.0
 **/
public class ReportCapacityRecordVo {

    private String recordId;

    private String departId;

    private String customerId;

    private String organizationName;

    private String yearMonth;

    private String processValueId;

    private BigDecimal capacityBuy;

    private Double capacityUsed;

    private Double discountPrice;

    private Double discountAmount;

    private Double punishAmount;

    private Double reconciliationAmount;

    private String versionDate;

    private String versionId;

    private Integer sumCapacity;

    private Double borrowedCapacity; // 已借产能

    private String customerPo;

    private String craftNo;

    private String customerModel;

    private Date sentTimeStartQr; // 时间段查询条件 载体

    private Date sentTimeEndQr;

    private String section;

    private String companyId;

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    private List<ReportCapacityRecordVo> usedDetailList;

    @ExcelField(title = "产能使用组织", align = 4, sort = 10)
    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    @ExcelField(title = "月份", align = 2, sort = 20)
    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    @ExcelField(title = "产品类型", align = 2, sort = 30)
    public String getProcessValueId() {
        return processValueId;
    }

    public void setProcessValueId(String processValueId) {
        this.processValueId = processValueId;
    }

    @ExcelField(title = "购买产能", align = 3, sort = 40)
    public BigDecimal getCapacityBuy() {
        return capacityBuy;
    }

    public void setCapacityBuy(BigDecimal capacityBuy) {
        this.capacityBuy = capacityBuy;
    }

    @ExcelField(title = "已使用产能", align = 3, sort = 50)
    public Double getCapacityUsed() {
        return capacityUsed;
    }

    public void setCapacityUsed(Double capacityUsed) {
        this.capacityUsed = capacityUsed;
    }

    @ExcelField(title = "单价优惠", align = 3, sort = 60)
    public Double getDiscountPrice() {
        return discountPrice;
    }

    public void setDiscountPrice(Double discountPrice) {
        this.discountPrice = discountPrice;
    }

    @ExcelField(title = "优惠金额", align = 3, sort = 70)
    public Double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }

    @ExcelField(title = "惩罚金额", align = 3, sort = 80)
    public Double getPunishAmount() {
        return punishAmount;
    }

    public void setPunishAmount(Double punishAmount) {
        this.punishAmount = punishAmount;
    }

    @ExcelField(title = "对账金额", align = 3, sort = 90)
    public Double getReconciliationAmount() {
        return reconciliationAmount;
    }

    public void setReconciliationAmount(Double reconciliationAmount) {
        this.reconciliationAmount = reconciliationAmount;
    }

    public List<ReportCapacityRecordVo> getUsedDetailList() {
        if(usedDetailList==null)
        {
            usedDetailList = new ArrayList<>();
        }
        return usedDetailList;
    }

    public void setUsedDetailList(List<ReportCapacityRecordVo> usedDetailList) {
        this.usedDetailList = usedDetailList;
    }

    public String getVersionDate() {
        return versionDate;
    }

    public void setVersionDate(String versionDate) {
        this.versionDate = versionDate;
    }

    public String getVersionId() {
        return versionId;
    }

    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }

    public String getDepartId() {
        return departId;
    }

    public void setDepartId(String departId) {
        this.departId = departId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public Integer getSumCapacity() {
        return sumCapacity;
    }

    public void setSumCapacity(Integer sumCapacity) {
        this.sumCapacity = sumCapacity;
    }

    @ExcelField(title = "借用产能", align = 3, sort = 42)
    public Double getBorrowedCapacity()
    {
        return borrowedCapacity;
    }

    public void setBorrowedCapacity(Double borrowedCapacity)
    {
        this.borrowedCapacity = borrowedCapacity;
    }

    public String getCustomerPo()
    {
        return customerPo;
    }

    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }

    public String getCraftNo()
    {
        return craftNo;
    }

    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }

    public String getCustomerModel()
    {
        return customerModel;
    }

    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }

    public Date getSentTimeStartQr(Date time) {
        return sentTimeStartQr;
    }

    public void setSentTimeStartQr(Date sentTimeStartQr) {
        this.sentTimeStartQr = sentTimeStartQr;
    }

    public Date getSentTimeEndQr() {
        return sentTimeEndQr;
    }

    public void setSentTimeEndQr(Date sentTimeEndQr) {
        this.sentTimeEndQr = sentTimeEndQr;
    }

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }
}
