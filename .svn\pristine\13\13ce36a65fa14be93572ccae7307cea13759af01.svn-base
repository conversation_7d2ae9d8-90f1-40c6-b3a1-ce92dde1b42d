
/* 产能评估 */
kybApp.controller('capacityCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'BaseUtil','$filter',function($rootScope, $scope, upida, $timeout,BaseUtil,$filter) {
	$scope.$on('$viewContentLoaded', function() {   
    	// initialize core components
		MainCtrl.initAjax();
    	// set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
	 
	var vm = this;
  
    // 分页数据
    vm.page = {};
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    // 项分页数据
    vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "production/capacity/page";
    
    vm.page.condition = []; // 条件
    vm.process = [];
    // 查询条件参数
    vm.query = {}; // 查询对象
  
    vm.query.data = {};
    vm.query.data.name = "processDate";
    vm.query.data.value = "";
    
    vm.query.userCode = {};
    vm.query.userCode.name = "process.name";
    vm.query.userCode.value = "";

    // tabs控制
    vm.tabs = {
        viewForm: {active:true},
        editForm: {active:false, show: false}
    };
    
    // 权限
    vm.right = {};
    
    
    // 分页按钮单击处理
    vm.doPage = function(page, pageSize, total){
		vm.page.pageNo = page;
		vm.page.pageSize = pageSize;
		vm.init(page, pageSize, vm.page.condition, vm.page.url);
    };
    
    /*
     * 兼容条件查询与普通查询
     * no 当前页
     * size 当前页显示数量
     * condition 是数组，可放入多个条件对象，如{name:"type", value:"1"}  名称对应实体字段名称，值对应页面输入的值   
     * url 请求数据链接
     */
    vm.init = function(no, size, condition, url){
    	// 请求数据
    	var reqData = {};
    	reqData.pageNo = no;
		reqData.pageSize = size;
		// 设置过滤条件
    	if (condition.length > 0)
    	{
    		angular.forEach(condition, function(p){
    			reqData[p.name] = p.value;
    		});
    	}
    	
    	// 请求分页数据
    	upida.post(url, reqData).then(function (result) {
    		var data = {};
    		// 如果结果为空
    		if (typeof result === 'undefined' || typeof result.list === 'undefined')
    		{
    			
    			data.pageNo = 1;
    			data.pageSize = 10;
    			data.list = [];
        		data.startCount = 0;
        		data.endCount = 0;
    		} else {
    			data = result;
    			// 计算开始数
        		data.startCount = (data.pageNo - 1) * data.pageSize + 1;
        		// 计算结束数
        		data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
    		}
    		
    		vm.page.data = data;
    	});
    };
    
    // 页面显示数量改变
    vm.pageSizeChange = function(){
		vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url);
    };
    
    // 查询数据
    vm.doQuery = function(){
    	// 设置查询条件
    	var condition = [];
    	var process = {};
    	if (vm.query.data.value !== "")
    	{
    		var myJsDate=$filter('date')(vm.query.data.value,'yyyy-MM-dd');
    		condition.push({
    			name: vm.query.data.name, 
    			value: myJsDate
    		});
    	}
    	
    	if (vm.query.userCode.value !== "")
    	{
    		if(vm.query.userCode.value === "全部"){
    			
    		}else{
    			process.name = vm.query.userCode.value ;
    			condition.push({
                    name: 'process',
                    value: process
                });
    		}
    		
    	}
    	vm.page.pageNo = 1;
    	vm.page.condition = condition;
    	// 查询数据
    	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    };
    vm.reset=function(){
    	vm.query.data.value = "";
    };

    
    
    
    // 加载权限
    function loadRight(){
    	 upida.get("common/rightall?prefix=production:capacity").then(function(data){
             vm.right.view = data.view;
             vm.right.edit = data.edit;
             vm.right.manage = data.manage;
             
             if(vm.right.view)
     		{
     			vm.page.pageNo = 1;
 	    		// 初始化第一页，条件为空
 	    	    vm.init(vm.page.pageNo, vm.page.pageSize, [], vm.page.url);
     		}
             
             if(vm.right.edit){
            	 loadItemsData();
             }
         });
    }
    
    
    //加载下拉框数据
    function loadItemsData() {
        // 加载工序
//       upida.get("production/capacity/findProcess").then(function(data) {
//          vm.process = data;
//          vm.process.splice(0,0,{name:'全部',recordId:''});
//          
//        });
      }

    
   
    $scope.$on("$stateChangeSuccess", function () {
    	
        upida.setScope($scope);
        loadRight();
        
    });
}]);
