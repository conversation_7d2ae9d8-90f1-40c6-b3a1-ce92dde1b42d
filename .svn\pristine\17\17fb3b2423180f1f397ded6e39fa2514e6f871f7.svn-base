package com.kyb.pcberp.modules.hr.depart_center.service;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.modules.hr.depart_center.dao.Hr_OrganizationDao;
import com.kyb.pcberp.modules.hr.depart_center.pojo.*;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;
import com.kyb.pcberp.modules.hr.group_center.pojo.Hr_DepartRelation;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import com.kyb.pcberp.modules.sys.dao.DictValueDao;
import com.kyb.pcberp.modules.sys.dao.UserDao;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.GroupOrgRelation;
import com.kyb.pcberp.modules.sys.entity.Role;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import com.kyb.pcberp.modules.wechat.entity.IcloudUser;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@Service
public class Hr_OrganizationService {
    @Autowired
    private Hr_OrganizationDao hr_organizationDao;

    @Autowired
    private CompanyDao companyDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private DictValueDao dictValueDao;

    public Hr_DepartMent getDepartOrg(String groupManageId, String defaultDb, String phobe, String salarySystemId) {
        Hr_DepartMent departMent = new Hr_DepartMent();
        List<Hr_DepartMent> departMentList = hr_organizationDao.getOaGroup(groupManageId, phobe, salarySystemId);
        List<Hr_Employee> empList = hr_organizationDao.getSalarySystemEmpTwoList(salarySystemId, "1");
        for (Hr_DepartMent hrDepartMent : departMentList) {
            hrDepartMent.setDefaultDb(defaultDb);
            hrDepartMent.setGroupManageId(groupManageId);
            List<Hr_DepartMent> list = new ArrayList<>();
            for (Hr_DepartMent depart : departMentList) {
                if (StringUtils.isNotBlank(depart.getAllId()) && depart.getAllId().equals(hrDepartMent.getRecordId())) {
                    list.add(depart);
                }
            }
            hrDepartMent.setDepartList(list);
            if (Collections3.isEmpty(list)) {
                for (Hr_Employee emp : empList) {
                    if (StringUtils.isNotBlank(emp.getAllId()) && emp.getAllId().equals(hrDepartMent.getRecordId())) {
                        Hr_DepartMent dept = new Hr_DepartMent();
                        dept.setDepartmentName(emp.getName());
                        list.add(dept);
                    }
                }
                hrDepartMent.setDepartList(list);
            }
            if (StringUtils.isBlank(hrDepartMent.getAllId())) {
                departMent = hrDepartMent;
            }
        }
        return departMent;
    }

    public Hr_DepartMent getDepart(IcloudUser user) {
        Hr_DepartMent depart = getDepartOrg(user.getGroupManageId(), user.getDefaultDb(), user.getPhone(), user.getSalarySystemId());
        return depart;
    }

    public Page<Hr_Employee> loadOaEmpList(Hr_DepartMent depart, HttpServletRequest request, HttpServletResponse response, Integer type) {
        Page<Hr_Employee> page = new Page<Hr_Employee>(request, response, type);
        Page<Hr_DepartMent> pageTwo = new Page<Hr_DepartMent>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(depart.getPageNo())) {
            pageNo = Integer.valueOf(depart.getPageNo());
        }
        if (StringUtils.isNotBlank(depart.getPageSize())) {
            pageSize = Integer.valueOf(depart.getPageSize());
        }
        if (type != -1) {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            pageTwo.setPageNo(pageNo);
            pageTwo.setPageSize(pageSize);
        }
        depart.setPage(pageTwo);
        List<Hr_Employee> employeeList = hr_organizationDao.getOaEmpList(depart);
        if (Collections3.isNotEmpty(employeeList)) {
            String recordIds = null;
            for (Hr_Employee emp : employeeList) {
                if (StringUtils.isNotBlank(recordIds)) {
                    recordIds = recordIds + "," + emp.getRecordId();
                } else {
                    recordIds = emp.getRecordId();
                }
            }
            List<Hr_Employee> otherPathList = hr_organizationDao.getEmployeeOtherPath(recordIds);
            if (Collections3.isNotEmpty(otherPathList)) {
                for (Hr_Employee emp : employeeList) {
                    for (Hr_Employee other : otherPathList) {
                        if (emp.getRecordId().equals(other.getRecordId())) {
                            emp.setOtherPath(other.getOtherPath());
                            break;
                        }
                    }
                }
            }
        }
        page.setList(employeeList);
        page.setCount(pageTwo.getCount());
        return page;
    }

    @Transactional(readOnly = false)
    public String addDepart(Hr_DepartMent depart) {
        // 同层级该架构名称是否已经添加
        Integer num = hr_organizationDao.getDepartNum(depart);
        if (num > 0) {
            return "exit";
        } else {
            hr_organizationDao.addDepart(depart);
            return "success";
        }
    }

    @Transactional(readOnly = false)
    public String editDepart(Hr_DepartMent depart) {
        // 同层级该架构名称是否已经存在
        Integer num = hr_organizationDao.getDepartNum(depart);
        if (num > 0) {
            return "exit";
        } else {
            hr_organizationDao.editDepart(depart);
            return "success";
        }
    }

    @Transactional(readOnly = false)
    public String delDepart(Hr_DepartMent depart) {
        // 当前架构下是否有属下架构或者在职员工
        Integer num = hr_organizationDao.getDepartEmpNum(depart);
        if (num > 0) {
            return "exit";
        } else {
            // 删除相关绑定的职位
            hr_organizationDao.delPoEmpByDept(depart);
            // 删除架构
            hr_organizationDao.delDepart(depart);
            return "success";
        }
    }

    public Map<String, Object> getPosition(Hr_Employee emp) {
        Map<String, Object> map = new HashMap<>();
        List<Hr_Position> poList = hr_organizationDao.getPositionList(emp);
        map.put("poList", poList);
        List<Hr_PositionEmployee> poEmpList = hr_organizationDao.getPoEmpList(emp);
        map.put("poEmpList", poEmpList);
        return map;
    }

    @Transactional(readOnly = false)
    public String delPoEmp(Hr_PositionEmployee poEmp) {
        hr_organizationDao.delPoEmp(poEmp);
        return "success";
    }

    @Transactional(readOnly = false)
    public String addPoEmp(Hr_PositionEmployee poEmp) {
        // 查询该用户是否已经绑定了该架构的职务
        Integer num = hr_organizationDao.getPoEmpNum(poEmp);
        if (num > 0) {
            return "exist";
        } else {
            hr_organizationDao.addPoEmp(poEmp);
            return "success";
        }
    }

    public Page<Hr_Position> getAllPosition(IcloudUser user, HttpServletRequest request, HttpServletResponse response, Integer type) {
        Page<Hr_Position> page = new Page<Hr_Position>(request, response, type);
        Page<IcloudUser> pageTwo = new Page<IcloudUser>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(user.getPageNo())) {
            pageNo = Integer.valueOf(user.getPageNo());
        }
        if (StringUtils.isNotBlank(user.getPageSize())) {
            pageSize = Integer.valueOf(user.getPageSize());
        }
        if (type != -1) {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            pageTwo.setPageNo(pageNo);
            pageTwo.setPageSize(pageSize);
            user.setPage(pageTwo);
        }
        List<Hr_Position> positionList = hr_organizationDao.getAllPosition(user);
        page.setList(positionList);
        page.setCount(pageTwo.getCount());
        return page;
    }

    @Transactional(readOnly = false)
    public String addPosition(Hr_Position position) {
        // 保存职位
        Integer nums = hr_organizationDao.getPositionNum(position);
        if (nums == null || nums == 0) {
            String groupId = hr_organizationDao.getGroupId(position.getGroupManageId());
            position.setGroupId(groupId);
            hr_organizationDao.addPosition(position);
            return "success";
        } else {
            return "exist";
        }
    }

    @Transactional(readOnly = false)
    public String editPosition(Hr_Position position) {
        // 保存职位
//        Integer nums = hr_organizationDao.getPositionNum(position);
//        if (nums == null || nums == 0)
//        {
        hr_organizationDao.editPosition(position);
        return "success";
//        }
//        else
//        {
//            return "exist";
//        }
    }

    @Transactional(readOnly = false)
    public String delPosition(Hr_Position position) {
        Integer nums = hr_organizationDao.getApprovePo(position);
        if (nums == null || nums == 0) {
            hr_organizationDao.delPosition(position);
            return "success";
        } else {
            return "exist";
        }
    }

    @Transactional(readOnly = false)
    public String saveAssignmentEmp(Hr_Employee emp) {
        Integer num = hr_organizationDao.checkAssignmentEmp(emp);
        if (null != num && num > 0) {
            return "exit";
        } else {
            hr_organizationDao.saveAssignmentEmp(emp);
            return "success";
        }
    }

    @Transactional(readOnly = false)
    public String deleteAssignmentEmp(Hr_Employee emp) {
        hr_organizationDao.deleteAssignmentEmp(emp);
        return "success";
    }

    public List<Hr_Employee> getEmpAssignmentList(Hr_Employee emp) {
        return hr_organizationDao.getEmpAssignmentList(emp);
    }

    public Page<Hr_OrganizationRetrospect> getOrganizationRetrospectList(IcloudUser user, HttpServletRequest request, HttpServletResponse response, Integer type) {
        Page<Hr_OrganizationRetrospect> page = new Page<Hr_OrganizationRetrospect>(request, response, type);

        Page<IcloudUser> pageTwo = new Page<IcloudUser>(request, response);

        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(user.getPageNo())) {
            pageNo = Integer.valueOf(user.getPageNo());
        }
        if (StringUtils.isNotBlank(user.getPageSize())) {
            pageSize = Integer.valueOf(user.getPageSize());
        }
        if (type != -1) {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            pageTwo.setPageNo(pageNo);
            pageTwo.setPageSize(pageSize);
            user.setPage(pageTwo);
        }
        List<Hr_OrganizationRetrospect> organList = hr_organizationDao.getOrganizationRetrospectList(user);
        page.setList(organList);
        page.setCount(pageTwo.getCount());
        return page;
    }

    public void setDataListThree(ExportExcel excel, List<Hr_OrganizationRetrospect> list, String[] hearList) {
        for (Hr_OrganizationRetrospect report : list) {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "架构时间":
                        val = report.getFrameTimeStr();
                        break;
                    case "创建人":
                        val = report.getFrameName();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    @Transactional(readOnly = false)
    public String backupVersion(IcloudUser user) {
        // 保存组织追溯
        Hr_OrganizationRetrospect hor = new Hr_OrganizationRetrospect();
        hor.setFrameTime(new Date());
        hor.setCreatedBy(new User(user.getRecordId()));
        hor.setCreatedDate(new Date());
        hr_organizationDao.insertOrganizationRetrospect(hor);

        // 生成新的部门数据
        hr_organizationDao.backupDepartmentRelation(hor.getRecordId(), user.getRecordId());

        // 更新备份部门的allId
        hr_organizationDao.updateDepartmentRelationAllId(hor.getRecordId());

        // 生成新的员工数据
        hr_organizationDao.backupEmployee(hor.getRecordId(), user.getRecordId());

        // 更新员工的上级员工
        hr_organizationDao.updateEmployeeData(hor.getRecordId());

        // 更新员工的组织架构
        List<Hr_Employee> salarySystemEmpList = hr_organizationDao.getSalarySystemEmpList(hor.getRecordId());
        List<Hr_DepartMent> salarySystemDepartmentList = hr_organizationDao.getSalarySystemDepartmentList(hor.getRecordId());
        if (Collections3.isNotEmpty(salarySystemEmpList) && Collections3.isNotEmpty(salarySystemDepartmentList)) {
            List<Hr_Employee> updateList = Lists.newArrayList();
            for (Hr_Employee emp : salarySystemEmpList) {
                if (StringUtils.isBlank(emp.getGroupId())) {
                    continue;
                }
                String groupId = null;
                String[] groupIdStrs = emp.getGroupId().split(",");
                for (int i = 0; i < groupIdStrs.length; i++) {
                    Boolean flag = false;
                    for (Hr_DepartMent dept : salarySystemDepartmentList) {
                        if (dept.getOldId().equals(groupIdStrs[i])) {
                            flag = true;
                            if (StringUtils.isNotBlank(groupId)) {
                                groupId = groupId + "," + dept.getRecordId();
                            } else {
                                groupId = dept.getRecordId();
                            }
                            break;
                        }
                    }
                    if (!flag) {
                        if (StringUtils.isNotBlank(groupId)) {
                            groupId = groupId + "," + groupIdStrs[i];
                        } else {
                            groupId = groupIdStrs[i];
                        }
                    }
                }
                emp.setGroupId(groupId);
                if (StringUtils.isNotBlank(groupId)) {
                    updateList.add(emp);
                }
            }
            if (Collections3.isNotEmpty(updateList)) {
                hr_organizationDao.batchUpdateEmpGroupId(updateList);
            }
        }

        // 生成新的员工职位数据
        hr_organizationDao.backupPositionEmployee(hor.getRecordId(), user.getRecordId());

        // 生成新的员工与部门数据
        hr_organizationDao.backupDepartmentRelationEmployee(hor.getRecordId());

        return "success";
    }

    ;

    @Transactional(readOnly = false)
    public String insertFramework(Hr_OrganizationRetrospect organizationRetrospect) {
        if (StringUtils.isBlank(organizationRetrospect.getRecordId())) {
            organizationRetrospect.preInsert();
            hr_organizationDao.getInsertFramework(organizationRetrospect);
        } else {
            organizationRetrospect.preUpdate();
            hr_organizationDao.getUpdateFramework(organizationRetrospect);
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String delFramework(Hr_OrganizationRetrospect organizationRetrospect) {
        hr_organizationDao.getDelFramework(organizationRetrospect);
        return "success";
    }

    @Transactional(readOnly = false)
    public String addAccount(Hr_DepartRelation groupDepartRelation) {
        // 名称、简称在财务组织不重复
        Integer num = hr_organizationDao.getNameNum(groupDepartRelation);
        if (num != null && num > 0) {
            return "fail";
        }
        //公司表添加数据
        groupDepartRelation.preInsert();
        companyDao.insertCompanyData(groupDepartRelation);

        String companyId = groupDepartRelation.getRecordId();

        //管理用户Id
        User user = new User();
        user.setCompanyId(groupDepartRelation.getRecordId());
        user.preInsert();
        String userCode = "admin" + companyId;
        user.setUserCode(userCode);
        userDao.insertUserData(user);
        String userId = user.getRecordId();

        Role role = new Role();
        role.setCompanyId(groupDepartRelation.getRecordId());
        //管理角色
        role.preInsert();
        userDao.insertRoleData(role);
        String roleId = role.getRecordId();

        // 管理员用户角色关联
        userDao.insertUserRoleData(companyId, userId, roleId);

        String companyIdTwo = "17";

        //根据江西领德辉管理员角色模块生成新角色模块数据
        userDao.insertRoleItem(companyId, companyIdTwo);

        //根据江西领德辉数据字典生成新数据字典数据
        dictValueDao.insertDictValue(companyId, companyIdTwo);

        //根据江西领德辉编码生成新编码数据
        userDao.insertCodegenData(companyId, companyIdTwo);

        groupDepartRelation.setGroupUserId(groupDepartRelation.getUser().getRecordId());
        groupDepartRelation.setCreatedBy(groupDepartRelation.getUser());
        groupDepartRelation.setBindErpId(companyId);
        //集团组织架构
        hr_organizationDao.insert(groupDepartRelation);
        return "success";
    }

    public List<Hr_TaskManagement> getTaskManagementList(Hr_TaskManagement hr_taskManagement) {
        return hr_organizationDao.getTaskManagementList(hr_taskManagement);
    }

    public String deleteMission(Hr_TaskManagement hr_taskManagement) {
        hr_organizationDao.deleteMission(hr_taskManagement);
        return "success";
    }

    public List<Hr_ManagementTarget> getManagementObjectivesList(Hr_ManagementTarget hr_managementTarget) {
        return hr_organizationDao.getManagementObjectivesList(hr_managementTarget);
    }

    public String insertManagement(Hr_TaskManagement hr_taskManagement) {
        hr_organizationDao.insertManagement(hr_taskManagement);
        return "success";
    }

    public void setDataList(ExportExcel excel, List<Hr_OrganizationRetrospect> list, String[] hearList) {
        for (Hr_OrganizationRetrospect emp : list) {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "架构时间":
                        val = emp.getFrameTimeStr();
                        break;
                    case "创建人":
                        val = emp.getFrameName();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    public void setDataListTwo(ExportExcel excel, List<Hr_Position> list, String[] hearList) {
        for (Hr_Position pos : list) {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "岗位":
                        val = pos.getName();
                        break;
                    case "任命人员":
                        val = pos.getDepartAllName();
                        break;
                    case "岗位职责":
                        val = pos.getMatter();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    public List<Company> getConnectionAList(User user) {
        return hr_organizationDao.getConnectionAList(user);
    }

    @Transactional(readOnly = false)
    public String bindeErpCompany(GroupOrgRelation groupOrgRelation) {
        hr_organizationDao.bindeErpCompany(groupOrgRelation);
        return "suceess";
    }

    public Page<OrganizationBudget> selectBudget(OrganizationBudget organizationBudget, HttpServletRequest request, HttpServletResponse response, Integer type) {
        Page<OrganizationBudget> page = new Page<OrganizationBudget>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(organizationBudget.getPageNo())) {
            pageNo = Integer.valueOf(organizationBudget.getPageNo());
        }
        if (StringUtils.isNotBlank(organizationBudget.getPageSize())) {
            pageSize = Integer.valueOf(organizationBudget.getPageSize());
        }
        if (type != -1) {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            organizationBudget.setPage(page);
        }
        List<OrganizationBudget> budgetList = hr_organizationDao.selectBudget(organizationBudget);
        page.setList(budgetList);
        return page;
    }

    public void setDataListTwoBudget(ExportExcel excel, List<OrganizationBudget> list, String[] hearList) {
        for (OrganizationBudget report : list) {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "组织":
                        val = report.getDepartmentName() == null ? "" : report.getDepartmentName();
                        break;
                    case "计划投入":
                        val = report.getPlanninput().toString() == null ? "0" : report.getPlanninput().toString();
                        break;
                    case "盈亏金额":
                        val = report.getAmount() == null ? "0" : report.getAmount().toString();
                        break;
                    case "预警状态":
                        val = report.getStatus() == null ? "" : report.getStatus();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    @Transactional(readOnly = false)
    public String deleteBudget(OrganizationBudget organizationBudget) {
        hr_organizationDao.deleteBudget(organizationBudget);
        return "success";
    }

    @Transactional(readOnly = false)
    public String insertUpeBudget(OrganizationBudget organizationBudget) {
        organizationBudget.setCompany(UserUtils.getUser().getCompany());
        if (StringUtils.isBlank(organizationBudget.getRecordId())) {
            organizationBudget.preInsert();
            hr_organizationDao.insertBudget(organizationBudget);
        } else {
            organizationBudget.preUpdate();
            hr_organizationDao.updateBudget(organizationBudget);
        }
        return "success";
    }
}
