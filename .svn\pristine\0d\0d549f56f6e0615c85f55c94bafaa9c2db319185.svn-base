<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isErrorPage="true"%>
<div class="p-2">
	<div class="row pt-2 pb-2 alert-light border-bottom border-bottom-secondary">
		<div class="col text-center text-primary">
			<h5>报销申请单</h5>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col text-left font-weight-bold text-primary" v-if="comList && comList.length > 0 && comList[0].name">
			提交人：{{comList[0].name}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			来&emsp;&emsp;源
			<select class="custom-select form-control h-32px" v-model="company">
				<option v-for="item in comList" :key="item.recordId" :value="item">
					{{item.departmentName}}
				</option>
			</select>
		</div>
	</div>
<%--	<div class="row alert-light pt-2 pb-2 align-items-center">--%>
<%--		<div class="col">--%>
<%--			报销类型--%>
<%--			<select v-model="expenseTypeId" class="custom-select form-control h-32px selectpicker" data-max-options="1"--%>
<%--					data-none-selected-text="报销类型" data-live-search="true" id="expenseType" data-size="6">--%>
<%--			</select>--%>
<%--		</div>--%>
<%--	</div>--%>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			<div class="row">
				<div class="col text-right">
					<span class="text-primary" v-on:click="add">增加+</span>
				</div>
			</div>
			<div v-for="(item,index) in expenseList">
				<div class="row border-bottom pb-1 pt-1" :key="index">
					<div class="col">
						<div class="row pt-2">
							<div class="col">
								财务类目
								<select class="custom-select form-control selectpicker" data-live-search="true"
										v-model="item.categoryId" :id="item.itemId" v-on:change="loadAmoebaIdValue(item.categoryId,item.id)"></select>
							</div>
							<div class="col">
								阿米巴类目
								<select class="custom-select form-control selectpicker" data-live-search="true"
										v-model="item.amoebaId" :id="item.amoebaItemId"></select>
							</div>
						</div>
						<div class="row pt-2">
							<div class="col">
								报销金额
								<input class="custom-select form-control h-32px" v-model="item.expenseSum" v-on:change="countMoney(item)">
							</div>
							<div class="col">
								单&emsp;&emsp;据
								<input class="custom-select form-control h-32px"  v-model="item.expenseNum">
							</div>
						</div>
						<div class="row align-items-center pt-2">
							<div class="col">
								备注
								<textarea class="form-control" rows="1" v-model="item.expenseDesc"></textarea>
							</div>
						</div>
						<div class="row align-items-center pt-2">
							<div class="col-12 text-right">
								<button class="btn btn-sm btn-danger" v-on:click="reduce(index)">&nbsp;-&nbsp;</button>
							</div>
						</div>

					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			合&emsp;&emsp;计
			<span>{{audit.money}}</span>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			报销原因
			<textarea class="form-control" v-model="audit.auditResult"></textarea>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">图片列表</div>
	</div>
	<div v-for="(item,index) in localIds">
		<div class="row alert-light pt-2 pb-2 align-items-center">
			<div class="col">
				<img :src="item" class="w-25" v-on:click="preview(item)">
			</div>
			<div class="col-2">
				<button class="btn btn-sm btn-danger" v-on:click="deleteImg(index)">删除</button>
			</div>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			<button class="btn btn-primary" v-on:click="chooseImg">上传图片</button>
		</div>
	</div>
    <div class="modal fade" id="reimbursementWindow" tabindex="-1" role="dialog" aria-hidden="true">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-body">
	        <div class="row pt-3">
	        	<div class="col-12">
					特别说明
	        		<textarea class="form-control" v-model="audit.reimbursementFor"></textarea>
	        	</div>
	        </div>
	      </div>
	      <div class="modal-footer">
	        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
	        <button type="button" class="btn btn-primary" v-on:click="reimbursementSubmit">确认</button>
	      </div>
	    </div>
	  </div>
	 </div>
	<div class="modal fade" id="openCatgortPage" tabindex="-1" role="dialog" aria-labelledby="staticBackdrop" aria-hidden="true">
		<div class="modal-dialog" style="max-width:800px" role="document">
			<div class="modal-content">
				<!-- 设置滚动条的容器 -->
				<div class="modal-body" style="max-height: 500px; overflow-y: auto;">
					<div class="row pb-3 align-items-center">
						<div class="col-sm">
							<div class="row alert-light pt-2 pb-2 pl-2 align-items-center">
								<div class="col-sm">
									<div class="row border-bottom pt-3 pb-3">
										<div class="col" style="color: black;">
											<h5><span>承担主体列表</span></h5>
										</div>
									</div>
									<div class="row alert-light pt-2 pb-2 pl-2 align-items-center font-weight-bolder border-bottom">
										<div class="col-1 text-center">
											<input type="checkbox" v-model="selectFlag" value="1" v-on:click="setAllSelectFlag" />
										</div>
										<div class="col-7 text-center">组织架构</div>
										<div class="col-4 text-center">承担比例(%)</div>
									</div>
									<div class="row alert-light pt-2 pb-2 pl-2 align-items-center border-bottom" v-for="item in organizationAuditList" :key="item.recordId">
										<div class="col-1 text-center">
											<input type="checkbox" v-model="item.selectFlag" v-on:click="selectValue(item)" />
										</div>
										<div class="col-7 text-center">{{item.departmentName}}</div>
										<div class="col-4 text-center">{{item.scale}}</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">关闭</button>
				</div>
			</div>
		</div>
	</div>
	<div class="row fixed-bottom bg-light pt-2 pb-2">
		<div class="col">
			<button class="btn btn-secondary w-100" v-on:click="cancle">取消</button>
		</div>
		<div class="col">
			<button class="btn btn-primary w-100" v-on:click="submit('2')">提交</button>
		</div>
		<div class="col">
			<button class="btn btn-primary w-100" v-on:click="submit('1')">保存</button>
		</div>
	</div>
</div>
