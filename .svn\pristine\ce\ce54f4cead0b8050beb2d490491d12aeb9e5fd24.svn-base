/**
 * 
 */
package com.kyb.pcberp.modules.purch.entity;

import org.hibernate.validator.constraints.Length;

import com.kyb.pcberp.common.persistence.DataEntity;

/**
 * 采购发票附件表Entity
 * <AUTHOR>
 * @version 2015-11-21
 */
public class PuPurchasingAttachements extends DataEntity<PuPurchasingAttachements> {
	
	private static final long serialVersionUID = 1L;
	private Integer type;		// type
	private String purchasingId;		// 采购类型,1原料采购,2成品采购
	private String orgFileName;		// orgfilename
	private String realFileName;		// realfilename
	private String fileUrl;		// 文件路径
	
	public PuPurchasingAttachements() {
		super();
	}

	public PuPurchasingAttachements(String id){
		super(id);
	}

	@Length(min=0, max=2, message="type长度必须介于 0 和 2 之间")
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
	
	public String getPurchasingId() {
		return purchasingId;
	}

	public void setPurchasingId(String purchasingId) {
		this.purchasingId = purchasingId;
	}
	
	@Length(min=0, max=200, message="orgfilename长度必须介于 0 和 200 之间")
	public String getOrgFileName() {
		return orgFileName;
	}

	public void setOrgFileName(String orgFileName) {
		this.orgFileName = orgFileName;
	}
	
	@Length(min=0, max=200, message="realfilename长度必须介于 0 和 200 之间")
	public String getRealFileName() {
		return realFileName;
	}

	public void setRealFileName(String realFileName) {
		this.realFileName = realFileName;
	}
	
	@Length(min=0, max=500, message="文件路径长度必须介于 0 和 500 之间")
	public String getFileUrl() {
		return fileUrl;
	}

	public void setFileUrl(String fileUrl) {
		this.fileUrl = fileUrl;
	}
	
}