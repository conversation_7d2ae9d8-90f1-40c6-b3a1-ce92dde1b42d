package com.kyb.pcberp.modules.hr.finance_center.pojo;

import com.kyb.pcberp.common.persistence.DataEntity;

import java.math.BigDecimal;

public class Hr_WriteOffs extends DataEntity<Hr_WriteOffs>
{
    private static final long serialVersionUID = 1L;

    private String sourceId;

    private String deailId;

    private BigDecimal amount;

    private String type;

    public String getSourceId()
    {
        return sourceId;
    }

    public void setSourceId(String sourceId)
    {
        this.sourceId = sourceId;
    }

    public String getDeailId()
    {
        return deailId;
    }

    public void setDeailId(String deailId)
    {
        this.deailId = deailId;
    }

    public BigDecimal getAmount()
    {
        return amount;
    }

    public void setAmount(BigDecimal amount)
    {
        this.amount = amount;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }
}
