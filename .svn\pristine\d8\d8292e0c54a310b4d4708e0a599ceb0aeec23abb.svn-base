<%@ page contentType="text/html;charset=UTF-8" %>
<div class="rows">
    <div class="panel panel-default" id="step6">
        <div class="panel-heading font-blue-hoki">查询</div>
        <div class="panel-body">
            <form class="form-horizontal">
                <div class="row">
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">单据编号：</label>
                            <div class="col-sm-7 col-md-8">
                                <input type="text" class="form-control"
                                       ng-model="payApplyCtrl.query.no.value"
                                       disable-auto-validate="true"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">供应商编号：</label>
                            <div class="col-sm-7 col-md-8">
                                <input type="text" class="form-control"
                                       ng-model="payApplyCtrl.query.supplierNo.value"
                                       disable-auto-validate="true"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">支付对象：</label>
                            <div class="col-sm-7 col-md-8">
                                <input type="text" class="form-control"
                                       ng-model="payApplyCtrl.query.supplierName.value"
                                       disable-auto-validate="true"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">状态：</label>
                            <div class="col-sm-7 col-md-8">
                                <select class="form-control"
                                        disable-auto-validate="true"
                                        ng-model="payApplyCtrl.query.statusSelected"
                                        ng-options="status.value as status.name for status in payApplyCtrl.query.statusList">
                                    <option value="">所有</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">款项类型：</label>
                            <div class="col-sm-7 col-md-8">
                                <select class="form-control"
                                        disable-auto-validate="true"
                                        ng-model="payApplyCtrl.query.fundTypeSelected"
                                        ng-options="status.value as status.name for status in payApplyCtrl.query.fundTypeList">
                                    <option value="">所有</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">申请日期：</label>
                            <div class="col-sm-7 col-md-8">
                                <div class="input-prepend input-group">
                                    <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                    <input type="text" class="form-control" disable-auto-validate="true"
                                           ng-blur="payApplyCtrl.initDate(payApplyCtrl.time)"
                                           kyb-daterange
                                           kyb-daterange-options="payApplyCtrl.rangeOptions"
                                           ng-model="payApplyCtrl.time"
                                           placeholder="请选择时间段">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <button class="btn btn-default btn-default-width pull-right"
                                ng-click="payApplyCtrl.doQuery()"><i class="fa fa-search"></i> 查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="portlet light bordered">
        <div class="portlet-title">
            <div class="caption font-blue-hoki">付款申请单列表</div>
            <div class="actions" id="step7" >
                <div class="portlet-input input-inline input-small" ng-if="payApplyCtrl.right.edit">
                    <button type="button" class="btn green btn-default-width"
                            ng-click="payApplyCtrl.addPayApplication()"><i class="fa fa-plus"></i> 添加付款申请单
                    </button>
                </div>
                <div class="portlet-input input-inline input-small">
                    <form action="a/purch/payApplication/export" method="get" enctype="multipart/form-data" target="hidden_frame">
                        <input type="text" ng-show="false" name="no" value="{{payApplyCtrl.query.no.value}}" />
                        <input type="text" ng-show="false" name="supplier.no" value="{{payApplyCtrl.query.supplierNo.value}}" />
                        <input type="text" ng-show="false" name="supplier.name" value="{{payApplyCtrl.query.supplierName.value}}" />
                        <input type="text" ng-show="false" name="status" value="{{payApplyCtrl.query.statusSelected}}" />
                        <input type="text" ng-show="false" name="fundType" value="{{payApplyCtrl.query.fundTypeSelected}}" />
                        <input type="text" ng-show="false" name="sentTimeStartQr" value="{{payApplyCtrl.query.sentTimeStartQr.value}}"/>
                        <input type="text" ng-show="false" name="sentTimeEndQr" value="{{payApplyCtrl.query.sentTimeEndQr.value}}"/>
                        <input type="text" ng-show="false" name="orderBy" value="{{payApplyCtrl.query.sort.value}}"/>
                        <input type="text" ng-show="false" name="queryAll" value="{{payApplyCtrl.queryAll}}"/>
                        <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出付款申请单</button>
                        <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
                    </form>
                </div>
            </div>
        </div>
        <div class="portlet-body" id="step1">
            <div class="table-scrollable" style="margin-top:0px !important">
                <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                    <thead>
                    <tr class="heading">
                        <th  id="step2" ng-class="{'sorting': payApplyCtrl.sort.no.both, 'sorting_desc': payApplyCtrl.sort.no.desc, 'sorting_asc': payApplyCtrl.sort.no.asc}"
                             ng-click="payApplyCtrl.sortClick('no')">单据编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <th  ng-class="{'sorting': payApplyCtrl.sort.supplierNo.both, 'sorting_desc': payApplyCtrl.sort.supplierNo.desc, 'sorting_asc': payApplyCtrl.sort.supplierNo.asc}"
                             ng-click="payApplyCtrl.sortClick('supplierNo')">供应商编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <th  ng-class="{'sorting': payApplyCtrl.sort.supplierName.both, 'sorting_desc': payApplyCtrl.sort.supplierName.desc, 'sorting_asc': payApplyCtrl.sort.supplierName.asc}"
                             ng-click="payApplyCtrl.sortClick('supplierName')">支付对象&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <th  ng-class="{'sorting': payApplyCtrl.sort.fundType.both, 'sorting_desc': payApplyCtrl.sort.fundType.desc, 'sorting_asc': payApplyCtrl.sort.fundType.asc}"
                             ng-click="payApplyCtrl.sortClick('fundType')">款项类型&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <th>付款账期</th>
                        <th>财务类目</th>
                        <th>申请金额</th>
                        <th>待付金额</th>
                        <th>价税合计金额</th>
                        <th  ng-class="{'sorting': payApplyCtrl.sort.status.both, 'sorting_desc': payApplyCtrl.sort.status.desc, 'sorting_asc': payApplyCtrl.sort.status.asc}"
                             ng-click="payApplyCtrl.sortClick('status')">状态&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <th  ng-class="{'sorting': payApplyCtrl.sort.applyDate.both, 'sorting_desc': payApplyCtrl.sort.applyDate.desc, 'sorting_asc': payApplyCtrl.sort.applyDate.asc}"
                             ng-click="payApplyCtrl.sortClick('applyDate')">申请日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <th id="step3">操作&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="row in payApplyCtrl.page.data.list"
                        ng-dblclick="payApplyCtrl.showOrEditPayApplication($index)">
                        <td><a ng-click="payApplyCtrl.showOrEditPayApplication($index)">{{row.no}}</a></td>
                        <td ng-bind="row.supplier.no"></td>
                        <td ng-bind="row.supplier.name"></td>
                        <td>
                            <span class="label label-sm label-danger" ng-if="row.fundType == '99999911'">预付款</span>
                            <span class="label label-sm label-success" ng-if="row.fundType == '99999912'">应付款</span>
                        </td>
                        <td ng-bind="row.showPeriod"></td>
                        <td ng-bind="row.categoryName"></td>
                        <td class="text-right" ng-bind="row.amount | currency:'':2"></td>
                        <td class="text-right" ng-bind="row.nonePayAmount | currency:'':2"></td>
                        <td class="text-right" ng-bind="row.adValoremAmount | currency:'':2"></td>
                        <td>
                            <span class="label label-sm label-default" ng-if="row.status == '500506'">未生效</span>
                            <span class="label label-sm label-danger" ng-if="row.status == '500507' && row.applicationsType == '1'">草稿审批</span>
                            <span class="label label-sm label-danger" ng-if="row.status == '500507' && row.applicationsType == '3' && row.applicationsResult == 'reject'">审批驳回</span>
                            <span class="label label-sm label-warning" ng-if="row.status == '500507' && !(row.applicationsType == '1') && !(row.applicationsType == '3' && row.applicationsResult == 'reject')">审批中</span>
                            <span class="label label-sm label-success" ng-if="row.status == '500508'">审批通过 </span>
                            <span class="label label-sm label-success" ng-if="row.status == '500509'">已完成 </span>
                        </td>
                        <td ng-bind="row.applyDate"></td>
                        <td>
                            <a href="javascript:void(0)" class="btn btn-xs btn-default"
                               ng-if="payApplyCtrl.right.edit && (row.status =='500506' || (row.status == '500507' && row.applicationsType == '3' && row.applicationsResult == 'reject'))"
                               ng-click="payApplyCtrl.delPayApplication($index)"><i
                                    class="fa fa-times font-red"></i> 删除</a>
                            <a href="javascript:void(0)" class="btn btn-xs btn-default"
                               ng-if="payApplyCtrl.right.edit && row.status =='500506'"
                               ng-click="payApplyCtrl.oaAudit($index)"><i
                                    class="fa fa-check font-green"></i> 生成审批</a>
                            <a href="javascript:void(0)" class="btn btn-xs btn-default"
                               ng-if="payApplyCtrl.right.edit && ((row.status =='500507' && row.applicationsType == '1') || (row.status == '500507' && row.applicationsType == '3' && row.applicationsResult == 'reject'))"
                               ng-click="payApplyCtrl.oaAudit($index)"><i
                                    class="fa fa-check font-green"></i> 提交审批</a>
                            <a title="打印" href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="payApplyCtrl.download(row.recordId)"><i class="fa fa-print font-blue-hoki"></i> 打印</a>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="row page-margin-top">
                <div class="col-md-12 col-lg-6" id="step5">
                    <span class="inline">每页</span>
                    <select class="form-control inline" style="margin-top:8px; width:100px;"
                            disable-valid-styling="true"
                            disable-invalid-styling="true"
                            ng-model="payApplyCtrl.page.pageSize"
                            ng-change="payApplyCtrl.pageSizeChange()"
                            ng-options="option for option in payApplyCtrl.page.pageSizeOptions">
                    </select>
                    <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{payApplyCtrl.page.data.startCount}} / {{payApplyCtrl.page.data.endCount}} 条，共 {{payApplyCtrl.page.data.count}} 条</span>
                </div>
                <div class="col-md-12 col-lg-6" id="step4">
                    <paging
                            class="pull-right"
                            page="payApplyCtrl.page.data.pageNo"
                            page-size="payApplyCtrl.page.data.pageSize"
                            total="payApplyCtrl.page.data.count"
                            adjacent="1"
                            dots="..."
                            scroll-top="false"
                            hide-if-empty="false"
                            ul-class="pagination"
                            active-class="active"
                            disabled-class="disabled"
                            show-prev-next="true"
                            paging-action="payApplyCtrl.doPage(page, pageSize, total)">
                    </paging>
                </div>
            </div>
        </div>
    </div>
</div>