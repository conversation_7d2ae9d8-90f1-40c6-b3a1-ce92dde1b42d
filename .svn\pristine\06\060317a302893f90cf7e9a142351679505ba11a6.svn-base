package com.kyb.pcberp.modules.sys.dao;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.report.entity.Report;
import com.kyb.pcberp.modules.sys.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

@MyBatisDao
public interface CockpitSheetDao {
    //驾驶舱部门统计表
    List<CockpitSheet> departList (CockpitSheet cockpitSheet);

    void departInsert(CockpitSheet cockpitSheet);

    void departDelete(@Param("recordId") String recordId);

    void departUpdate(CockpitSheet cockpitSheet);

    //驾驶舱客户统计表
    List<CockpitSheet> customertList (CockpitSheet cockpitSheet);

    void customerInsert(CockpitSheet cockpitSheet);

    void customerDelete(@Param("recordId") String recordId);

    void customerUpdate(CockpitSheet cockpitSheet);

    //驾驶舱业务员统计表
    List<CockpitSheet> salesmanList (CockpitSheet cockpitSheet);

    void salesmanInsert(CockpitSheet cockpitSheet);

    void salesmanDelete(@Param("recordId") String recordId);

    void salesmanUpdate(CockpitSheet cockpitSheet);

    //驾驶舱供应商统计表
    List<CockpitSheet> supplierList (CockpitSheet cockpitSheet);

    void supplierInsert(CockpitSheet cockpitSheet);

    void supplierDelete(@Param("recordId") String recordId);

    void supplierUpdate(CockpitSheet cockpitSheet);


    // 清除统计报表
    void departClean(Map<String,Object> map);

    void customerClean(Map<String,Object> map);

    void salesmanClean(Map<String,Object> map);

    void supplierClean(Map<String,Object> map);

    List<CockpitSheet> getDeptData(Map<String,Object> map);

    List<CockpitSheet> getCustomerData(Map<String,Object> map);

    List<CockpitSheet> getSaleData(Map<String,Object> map);

    List<CockpitSheet> getSupplierData(Map<String,Object> map);

    void batchInsertDept(@Param("list") List<CockpitSheet> list);

    void batchInsertCustomer(@Param("list") List<CockpitSheet> list);

    void batchInsertSale(@Param("list") List<CockpitSheet> list);

    void batchInsertSupplier(@Param("list") List<CockpitSheet> list);

    List<CockpitSheet> getDeptSheetList(Map<String,Object> map);

    List<CockpitSheet> getCusSheetList(Map<String,Object> map);

    List<CockpitSheet> getSaleSheetList(Map<String,Object> map);

    List<CockpitSheet> getDeptDataList(Report report);
    List<CockpitSheet> getSalesDataList(Report report);

    // 订单
    List<CockpitSheet> getDeptOrderList(Map<String,Object> map);
    List<CockpitSheet> getCusOrderList(Map<String,Object> map);
    List<CockpitSheet> getSaleOrderList(Map<String,Object> map);

    // 出货
    List<CockpitSheet> getDeptStoreList(Map<String,Object> map);
    List<CockpitSheet> getCusStoreList(Map<String,Object> map);
    List<CockpitSheet> getSaleStoreList(Map<String,Object> map);

    // 回款
    List<CockpitSheet> getDeptReturnedMoneyList(Map<String,Object> map);
    List<CockpitSheet> getCusReturnedMoneyList(Map<String,Object> map);
    List<CockpitSheet> getSaleReturnedMoneyList(Map<String,Object> map);

    // 原料采购
    List<CockpitSheet> getDeptPurDetailList(Map<String,Object> map);
    List<CockpitSheet> getSupPurDetailList(Map<String,Object> map);

    // 成品采购
    List<CockpitSheet> getDeptPrdDetailList(Map<String,Object> map);
    List<CockpitSheet> getCusPrdDetailList(Map<String,Object> map);
    List<CockpitSheet> getSalePrdDetailList(Map<String,Object> map);
    List<CockpitSheet> getSupPrdDetailList(Map<String,Object> map);

    //客诉
    List<CockpitSheet> getDeptRejectList(Map<String,Object> map);
    List<CockpitSheet> getCusRejectList(Map<String,Object> map);
    List<CockpitSheet> getSaleRejectList(Map<String,Object> map);

    // 品质
    List<CockpitSheet> getDeptInspectList(Map<String,Object> map);
    List<CockpitSheet> getCusInspectList(Map<String,Object> map);
    List<CockpitSheet> getSaleInspectList(Map<String,Object> map);

    List<CockpitSheet> getWipData(Report report);

    List<CockpitSheet> getDepartListTwo(Report report);
    List<CockpitSheet>getDepartListTotalDetail(Report report);

    List<CockpitSheet> getCustomListTotalDetail(Report report);
    List<CockpitSheet> getSalesListTotalDetail(Report report);
    List<CockpitSheet> getSupplierListTotalDetail(Report report);

    //获取公司列表
    List<Company> getCompanyLists(Company company);
    //获取部门列表
    List<Department> getOrgDepartLists(Department department);
    //获取业务员名称
    List<User> getSalesLists(User user);

    Employee getEmployeeUserName(Employee employee);
}
