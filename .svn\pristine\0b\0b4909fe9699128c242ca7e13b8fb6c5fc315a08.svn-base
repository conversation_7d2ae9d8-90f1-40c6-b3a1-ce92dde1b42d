<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="d-flex flex-column flex-root">
    <div class="d-flex flex-row flex-column-fluid page">
        <div class="d-flex flex-column flex-row-fluid wrapper">
            <div class="content d-flex flex-column flex-column-fluid"
                 style="background-image: url(${pageContext.request.contextPath}/assets/global/metronic/assets/media/bg/bgMain.jpg);height: 180px;">
                <div class="subheader subheader-transparent">
                    <div class="container d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <h5 class="text-white my-2 mr-5">完成清单</h5>
                            </div>
                        </div>
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <router-link to="/maintenance" class="btn btn-transparent-warning font-weight-bold mr-2">
                                    返回主页
                                </router-link>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-column-fluid">
                    <div class="container">
                        <div class="row">
                            <div class="col-12">
                                <div class="card card-custom gutter-b">
                                    <div class="card-body d-flex flex-column p-3">
                                        <div class="row pb-3">
                                            <div class="col-12">
                                                <input type="text" class="form-control" placeholder="请输入要搜索的设备信息" v-model="searchInfo" v-on:blur="getMyWaiteRepairList()"/>
                                            </div>
                                        </div>
                                        <template v-for="item in myWaiteRepairList">
                                            <div class="row pb-2 border-bottom" :key="item.recordId">
                                                <div class="col-xl-12">
                                                    <div class="row pt-2 pb-2">
                                                        <div class="col-8">
                                                            {{item.preserveNo}}
                                                        </div>
                                                        <div class="col text-right">
                                                            <span class="badge badge-primary" v-if="item.repairStatus == 1001">未受理</span>
                                                            <span class="badge badge-success" v-if="item.repairStatus == 1002">维修中</span>
                                                            <span class="badge badge-success" v-if="item.repairStatus == 1003">已结束</span>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col">
                                                            申报：<span class="font-weight-bold">{{item.applyCompany ? item.applyCompany.name : null}} - {{item.applyName}}</span>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col">
                                                            维保：<span class="font-weight-bold">{{item.repairCompany ? item.repairCompany.name : null}} <span v-if="item.repairName">- {{item.repairName}}</span></span>
                                                        </div>
                                                    </div>
                                                    <div class="row pt-1">
                                                        <div class="col text-left">
                                                            设备：<span class="font-weight-bold">{{item.materialName}}</span>
                                                        </div>
                                                        <div class="col text-right">
                                                            <span class="font-weight-bold">{{item.materialType}}</span>
                                                        </div>
                                                    </div>
                                                    <div class="row pt-1">
                                                        <div class="col">
                                                            规格：<span class="font-weight-bold">{{item.specification}}</span>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col">
                                                            维保日期：<span class="font-weight-bold">{{item.startDate}} 到 <span v-if="item.endDate">{{item.endDate}}</span> </span>
                                                        </div>
                                                    </div>
                                                    <div class="row pt-1">
                                                        <div class="col">
                                                            问题描述：<span class="font-weight-bold">{{item.problem}}</span>&nbsp;&nbsp;
                                                        </div>
                                                    </div>
                                                    <div class="row pt-1">
                                                        <div class="col">
                                                            原因：<span class="font-weight-bold">{{item.remark}}</span>&nbsp;&nbsp;
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                        <div class="row">
                                            <div class="col-xl-12">
                                                <div style="height: 6rem;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>