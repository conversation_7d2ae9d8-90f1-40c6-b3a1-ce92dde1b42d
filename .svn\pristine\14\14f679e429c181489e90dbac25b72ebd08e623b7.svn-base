package com.kyb.pcberp.modules.hr.group_center.dao;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.hr.group_center.pojo.*;
import com.kyb.pcberp.modules.wechat.entity.IcloudUser;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatReport;

import java.util.List;

@MyBatisDao
public interface Hr_CenterDao
{
    Hr_System getSystemMsg(IcloudUser user);

    void editSystemMsg(Hr_System hrSystem);

    List<Hr_Db> getDbList(IcloudUser user);

    void editDbMsg(Hr_Db hrDb);

    List<WechatReport> getReportList(Hr_Report hr_Report);

    List<Hr_Process> getProcessList(IcloudUser user);

    void updateProcess(Hr_Process groupProcessTwo);

    void insertProcess(Hr_Process groupProcessTwo);

    //获取组织架构树图数据
    List<Hr_DepartRelation> getList(IcloudUser user);

    //删除集团管控数据
    void delProcess(Hr_Process groupProcessTwo);
}
