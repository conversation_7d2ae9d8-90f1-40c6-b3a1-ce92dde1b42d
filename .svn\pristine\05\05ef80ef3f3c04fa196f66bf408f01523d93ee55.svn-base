package com.kyb.pcberp.modules.crm.dao;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.crm.entity.ReceiveTaskAttach;
import com.kyb.pcberp.modules.hr.finance_center.pojo.Hr_CollectPayBill;

import java.util.List;

@MyBatisDao
public interface ReceiveTaskDao {

    List<Hr_CollectPayBill> getSingleReceivableList(Hr_CollectPayBill hr_collectPayBill);

    void updateSingleStatus(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> loadSettlementDetailList(Hr_CollectPayBill hr_collectPayBill);

    void saveReceiveAttachement(ReceiveTaskAttach receiveTaskAttach);

    List<ReceiveTaskAttach> findPayApplicationInvoices(ReceiveTaskAttach receiveTaskAttach);

    void delete(ReceiveTaskAttach receiveTaskAttach);

    String getAuditAttachId(ReceiveTaskAttach receiveTaskAttach);
}
