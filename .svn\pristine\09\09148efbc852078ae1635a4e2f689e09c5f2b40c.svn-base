const addleave = {
	template:'#addleave',
	computed: {
		comList: {
			get () {
				return this.$store.state.auditStore.comList
		    }
		},
		empList: {
			get () {
				return this.$store.state.auditStore.empList
		    }
		},
		emp: {
			get () {
				return this.$store.state.myStore.emp
		    }
		}
	},
	created(){
		this.getWxMsg()
	},
	data(){
		return{
			audit: {days:null},
			company: {},
			localIds: [],
			serverList: [],
			localIdsCopy: [],
			clickFlag:false
		}
	},
	methods:{
		getWxMsg:function()
		{
			var config = {}
			config.url = location.href.split('#')[0]
			var _this = this
			$.ajax({
	     		type:"post",
	     		url:ctx + "/f/wechat/produce/getWxMsg",
	     		data:JSON.stringify(config),
	     		contentType:"application/json",
	     		success:function(data)
	     		{
	     			_this.wxConfig(data)
	     		}
	     	})
		},
		wxConfig: function (data) {
			wx.config({
		        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端console.log出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
		        appId: data.appId, // 必填，公众号的唯一标识
		        timestamp: data.timestamp, // 必填，生成签名的时间戳
		        nonceStr: data.nonceStr, // 必填，生成签名的随机串
		        signature: data.signature, // 必填，签名
		        jsApiList: [
		          'checkJsApi', 'chooseImage', 'previewImage', 'uploadImage', 'downloadImage'
		        ]
			})
	    },
		chooseImg:function(){
			var _this = this
			wx.chooseImage({
				count: 9, // 默认9
				sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
				success: function (res) {
					if(_this.localIds && _this.localIds.length > 0){
						for(var i=0;i< res.localIds.length;i++){
							_this.localIds.push(res.localIds[i])
						}
					}else{
						_this.localIds = res.localIds // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
					}
					// 上传图片
					_this.serverList = []
					_this.localIdsCopy = []
					for (let i = 0; i < _this.localIds.length; i++) {
						_this.localIdsCopy.push(_this.localIds[i])
					}
					_this.uploadImage()
				}
			});
		},
		preview:function(item){
			var _this = this
			wx.previewImage({
				current: item, // 当前显示图片的http链接
				urls: _this.localIds // 需要预览的图片http链接列表
			});
		},
		uploadImage:function(){
			var vm = this
			if(vm.localIdsCopy && vm.localIdsCopy.length > 0){
				wx.uploadImage({
					localId: vm.localIdsCopy.pop(), // 需要上传的图片的本地ID，由chooseImage接口获得
					isShowProgressTips: 1, // 默认为1，显示进度提示
					success: function (res) {
						vm.serverList.push(res.serverId)
						vm.uploadImage()
					}
				});
			}
		},
		deleteImg:function(index){
			this.localIds.splice(index,1)
		},
		submit:function(){
			if(this.clickFlag)
			{
				alert("请勿多次点击！");
				return;
			}
			var start =  $('#start').val();
			var end =  $('#end').val();
			if(!start){
				alert("请选择开始时间");
				return;
			}
			if(!end){
				alert("请选择结束时间");
				return;
			}
			if(end < start){
				alert("结束时间要大于开始时间");
				return;
			}
			if(!this.audit.childType){
				alert("请选择请假类型");
				return;
			}
			if(!this.audit.passUser){
				alert("请选择代理人");
				return;
			}
			if(!this.audit.days){
				alert("请填写请假天数");
				return;
			}
			if(isNaN(this.audit.days) || this.audit.days <= 0){
				alert("请假天数必须为大于0的数字");
				this.$set(this.audit,"days",null)
				return;
			}
			if(!this.audit.auditResult){
				alert("请填写请假原因");
				return;
			}
			if(!this.company || !this.company.oaDepartId){
				alert("请选择来源");
				return;
			}
			this.audit.oaDepartId = this.company.oaDepartId;
			this.audit.empId = this.emp.recordId;
			this.audit.groupId = this.emp.company.recordId;
			this.audit.startTime = start;
			this.audit.endTime = end;
			this.audit.auditType = "22001";
			this.audit.serverList = this.serverList
			this.clickFlag = true;
			var router = this.$router;
			$('#loadingModal').modal();
			var _this = this;
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftOA/commitAudit",
		     	data:JSON.stringify(this.audit),
		     	contentType:"application/json",
		     	success:function(data)
		     	{ 
		     		if(data == "success"){
		     			alert("提交成功");
		     			$('#loadingModal').modal('hide')
		     			router.push('/submit');
		     		}else{
		     			alert("提交失败");
		     			$('#loadingModal').modal('hide')
		     		}
					_this.clickFlag = false;
		     	}
		    })
		},
		cancle:function(){
			this.$router.push('/submit');
		}
	}
}