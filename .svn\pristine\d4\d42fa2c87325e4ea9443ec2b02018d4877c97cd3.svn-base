<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.sys.dao.NoticeDao">

	<sql id="noticeColumns">
		a.recordId ,
		a.companyId AS "company.recordId",
		a.title,
		a.content,
		a.operLevel,
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.activeFlag,
		a.remark,
		a.receiveUser,
		a.receiveDepa AS
		"department.recordId",
		a.sendDepart AS
		"sendDepart.recordId"
	</sql>

	<sql id="noticeJoins">
		LEFT JOIN md_department depart ON depart.recordId = a.receiveDepa
		LEFT JOIN md_department sd ON sd.recordId = a.sendDepart
		LEFT JOIN sm_user su ON FIND_IN_SET(
			su.recordId,
			a.receiveUser
		)
	</sql>

	<select id="get" resultType="Notice">
		SELECT
		<include refid="noticeColumns" />
		FROM sm_notice a
		<include refid="noticeJoins" />
		WHERE a.recordId = #{recordId}
	</select>

	<!-- <select id="findList" resultType="Notice"> SELECT <include refid="noticeColumns" 
		/> , u.userName AS "createdBy.userName" FROM sm_notice a LEFT JOIN sm_user 
		u on a.createdBy = u.recordId <include refid="noticeJoins" /> <where> a.companyId 
		= #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL} <if test="title 
		!= null and title != ''"> AND title LIKE <if test="dbName == 'oracle'">'%'||#{title}||'%'</if> 
		<if test="dbName == 'mysql'">CONCAT('%', #{title}, '%')</if> </if> <if test="createdBy 
		!= null and createdBy.recordId != null and createdBy.recordId != ''"> AND 
		a.createdBy = #{createdBy.recordId} </if> </where> <choose> <when test="page 
		!=null and page.orderBy != null and page.orderBy != ''"> ORDER BY ${page.orderBy} 
		</when> <otherwise> </otherwise> </choose> </select> -->

	<select id="findAllList" resultType="Notice">
		SELECT
		<include refid="noticeColumns" />
		FROM sm_notice a
		<include refid="noticeJoins" />
		<where>
			a.companyId = #{company.recordId} and a.activeFlag =
			#{DEL_FLAG_NORMAL}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>

	<insert id="insert">
		INSERT INTO sm_notice(
		companyId,
		title,
		content,
		operLevel,
		createdBy,
		createdDate,
		lastUpdBy,
		lastUpdDate,
		activeFlag,
		remark,
		receiveUser,
		receiveDepa,
		sendDepart
		) VALUES (
		#{company.recordId},
		#{title},
		#{content},
		#{operLevel},
		#{createdBy.recordId},
		#{createdDate},
		#{lastUpdBy.recordId},
		#{lastUpdDate},
		#{DEL_FLAG_NORMAL},
		#{remark},
		#{receiveUser},
		#{department.recordId},
		#{sendDepart.recordId}
		)
	</insert>

	<update id="update">
		UPDATE sm_notice SET
		companyId = #{company.recordId},
		title = #{title},
		content = #{content},
		operLevel = #{operLevel},
		lastUpdBy = #{createdBy.recordId},
		lastUpdDate = #{lastUpdDate},
		remark = #{remark},
		receiveUser = #{receiveUser},
		receiveDepa = #{department.recordId},
		sendDepart = #{sendDepart.recordId}
		WHERE recordId = #{recordId}
	</update>

	<update id="delete">
		UPDATE sm_notice
		SET activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>

	<!-- 2017-05-17 tj -->
	<select id="findListNotice" resultType="Notice">
		SELECT
		<include refid="noticeColumns" />
		,
		u.userName AS "createdBy.userName"
		FROM sm_notice a
		LEFT JOIN sm_user u
		on
		a.createdBy = u.recordId
		<include refid="noticeJoins" />
		<where>
			a.companyId = #{company.recordId} and a.activeFlag =
			#{DEL_FLAG_NORMAL}
			<if test="title != null and title != ''">
				AND title LIKE
				<if test="dbName == 'oracle'">'%'||#{title}||'%'</if>
				<if test="dbName == 'mysql'">CONCAT('%', #{title}, '%')</if>
			</if>
			<if
				test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND a.createdBy = #{createdBy.recordId}
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>

			</otherwise>
		</choose>
	</select>

	<select id="findList" resultType="Notice">
		SELECT * from(SELECT
		<include refid="noticeColumns" />
		FROM sm_notice a
		LEFT JOIN sm_user u
		on
		a.createdBy = u.recordId
		<if test="queryAll==null or !queryAll">
			<include refid="noticeJoins" />
		</if>
		<where>
			a.companyId = #{company.recordId} and a.activeFlag =
			#{DEL_FLAG_NORMAL}
			<if test="queryAll==null or !queryAll">
				<if test="user.recordId!=null and user.recordId!=''">
					AND (FIND_IN_SET(#{user.recordId},receiveUser)
					<if test="department.recordId!=null and department.recordId!=''">
						or receiveDepa = #{department.recordId}
					</if>
					)
				</if>
			</if>
			<if test="title != null and title != ''">
				AND title LIKE
				<if test="dbName == 'oracle'">'%'||#{title}||'%'</if>
				<if test="dbName == 'mysql'">CONCAT('%', #{title}, '%')</if>
			</if>
			<if test="appFlag != null and appFlag != ''">
				AND receiveDepa IS NULL
				AND receiveUser IS NULL
			</if>
			<if
				test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND a.createdBy = #{createdBy.recordId}
			</if>
		</where>
		UNION SELECT
		<include refid="noticeColumns" />
		FROM sm_notice a
		<if test="queryAll==null or !queryAll">
			<include refid="noticeJoins" />
		</if>
		<where>
			a.companyId = #{company.recordId}
			AND a.activeFlag = 1
			AND a.receiveDepa IS NULL
			AND a.receiveUser IS NULL
			<if test="queryAll==null or !queryAll">
				<if test="user.recordId!=null and user.recordId!=''">
					AND (FIND_IN_SET(#{user.recordId},receiveUser)
					<if test="department.recordId!=null and department.recordId!=''">
						or receiveDepa = #{department.recordId}
					</if>
					)
				</if>
			</if>
			<if test="title != null and title != ''">
				AND a.title LIKE
				<if test="dbName == 'oracle'">'%'||#{title}||'%'</if>
				<if test="dbName == 'mysql'">CONCAT('%', #{title}, '%')</if>
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
		) e GROUP BY e.recordId
		<if test="page !=null and page.orderBy != null and page.orderBy != ''">
			ORDER BY ${page.orderBy}
		</if>
	</select>
	<select id="findListApp" resultType="Notice">
		SELECT
		<include refid="noticeColumns" />
		FROM sm_notice a WHERE activeFlag = 1 
		AND receiveUser IS NULL 
		AND receiveDepa IS NULL
	</select>

	<select id="getNoticeNew" resultType="Notice">
		SELECT
		notice.`recordId`,
		notice.`companyId`,
		notice.`title`,
		notice.`content`,
		notice.`operLevel`,
		notice.`activeFlag`,
		notice.`createdBy`,
		notice.`createdDate`,
		notice.`lastUpdBy`,
		notice.`lastUpdDate`,
		notice.`remark`,
		notice.`receiveDepa`,
		notice.`sendDepart` AS "sendDepart.recordId",
		sd.`name` AS "sendDepart.name",
		IFNULL(GROUP_CONCAT( su.userName),depart.NAME) AS `receiveUser`
		FROM
			sm_notice notice
			LEFT JOIN md_department depart ON depart.recordId = notice.receiveDepa
			LEFT JOIN md_department sd ON sd.recordId = notice.sendDepart
			LEFT JOIN sm_user su ON FIND_IN_SET(
				su.recordId,
				notice.receiveUser
			)
		WHERE notice.activeFlag = 1 AND
			  (FIND_IN_SET(#{userId},receiveUser)
			  or receiveDepa = #{departId})
		GROUP BY
			notice.recordId
		ORDER BY
			notice.createdDate DESC
			LIMIT 1
	</select>
</mapper>