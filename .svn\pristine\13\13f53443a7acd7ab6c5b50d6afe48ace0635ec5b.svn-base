<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isErrorPage="true"%>
<div>
    <div class="row pt-2 pb-2 alert-light border-bottom">
        <div class="col text-center font-weight-bolder text-primary">
            <span v-if="audit.auditType === '22029'">记账凭证申请单({{audit.no}})</span>
            <span v-else>反审记账凭证申请单({{audit.no}})</span>
        </div>
    </div>
    <div class="row alert-light pt-2 pb-2 align-items-center text-primary">
        <div class="col">
            提交人:{{audit.name}}
        </div>
        <div class="col text-right">
            提交时间:{{audit.createdDateStr}}
        </div>
    </div>
    <div class="row alert-light pt-2 pb-2 align-items-center">
        <div class="col">
            来源:{{audit.departmentName}}
        </div>
    </div>
    <div v-if="audit.certificateList && audit.certificateList.length > 0">
        <template v-for="(item, index) in audit.certificateList" :key="index">
            <div class="row alert-light pt-1 pb-1 align-items-center text-primary">
                <div class="col">原始单据</div>
                <div class="col text-right">
                    <button class="btn btn-sm btn-primary px-1 py-1" v-on:click="openClose(item)">
                        <span v-if="!item.showFlag">展开凭证清单</span>
                        <span v-else>收起</span>
                    </button>
                </div>
            </div>
            <div class="pt-1 pb-1 border-bottom">
                <div class="row alert-light pt-1 pb-1 align-items-center">
                    <div class="col-12">承担主体:{{item.departmentName}}({{item.typeStatusStr}})</div>
                </div>
                <div class="row alert-light pt-1 pb-1 align-items-center">
                    <div class="col-6">{{item.typeName}}-{{item.documentType}}</div>
                    <div class="col-6 text-right">{{item.categoryName}}</div>
                </div>
                <div class="row alert-light pt-1 pb-1 align-items-center">
                    <div class="col-6">{{item.typeStatusStr}}方:{{item.targetName}}</div>
                    <div class="col-6 text-right">单据编号:{{item.no}}</div>
                </div>
                <div class="row alert-light pt-1 pb-1 align-items-center">
                    <div class="col-3">确认人:{{item.employeeName}}</div>
                    <div class="col-3 text-danger">金额:{{item.amount}}</div>
                    <div class="col-6 text-right">创建时间:{{item.createdDate}}</div>
                </div>
                <div class="row alert-light pt-1 pb-1 align-items-center" v-if="item.certificateDetailList && item.certificateDetailList[0]">
                    <div class="col">账簿:{{item.certificateDetailList[0].accountbookName}} 期间:{{item.certificateDetailList[0].certificateWordName}} 凭证号:{{item.certificateDetailList[0].period}} 制单:{{item.certificateDetailList[0].employeeName}}</div>
                </div>
                <template v-for="(row, index) in item.certificateDetailList" :key="index" v-if="item.showFlag">
                    <div class="pl-3 pr-3 alert-primary">
                        <div class="row pb-1 align-items-center">
                            <div class="col-4">{{row.subjectDeteilName}}-{{row.fullName}}</div>
                            <div class="col-8 text-right">摘要:{{row.absractMessage}}</div>
                        </div>
                        <div class="row pb-1 align-items-center">
                            <div class="col">核算维度:{{row.dimensionName}}</div>
                        </div>
                        <div class="row pb-1 align-items-center">
                            <div class="col-4">币别:{{row.currencyName}}</div>
                            <div class="col-4">汇率类型:{{row.exchangeRateType}}</div>
                            <div class="col-4 text-right">汇率:{{row.exchangeRate}}</div>
                        </div>
                        <div class="row pb-1 align-items-center">
                            <div class="col text-danger">原币金额:{{row.borrowAmount ? row.borrowAmount : row.loanAmount}}</div>
                            <div class="col text-danger text-right" v-if="row.borrowAmount">借方金额:{{row.borrowAmount}}</div>
                            <div class="col text-danger text-right" v-if="row.loanAmount">贷方金额:{{row.loanAmount}}</div>
                        </div>
                    </div>
                </template>
            </div>
        </template>
    </div>
    <div class="row alert-light pt-2 pb-2 align-items-center" v-if="audit.auditType === '22030'">
        <div class="col-12">
            反审原因:{{audit.auditResult}}
        </div>
    </div>
</div>