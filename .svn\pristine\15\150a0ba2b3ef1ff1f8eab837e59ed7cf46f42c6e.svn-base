<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.quality.dao.InspectDao">
    
    <resultMap id="inspect_batch_notifMap" type="Inspect">
		<id property="recordId" column="recordId" />
		<result property="company.recordId" column="companyId" />
		<result property="manufType" column="manufType" />
		<result property="pcsEsperPnl" column="pcsEsperPnl" />
		<result property="producePcsQty" column="producePcsQty" />
		<result property="replaceQty" column="replaceQty" />
		<result property="passPcsQty" column="passPcsQty" />
		<result property="failedPcsQty" column="failedPcsQty" />
		<result property="checkDate" column="checkDate" />
		<result property="createdDate" column="createdDate" />
		<result property="lastUpdDate" column="lastUpdDate" />
		<result property="remark" column="remark" />
		<result property="handOverFlag" column="handOverFlag" />
		<result property="status" column="status" />
		<result property="auditStatus" column="auditStatus" />
		<result property="type" column="type" />
		<result property="rejectNo" column="rejectNo" />
		<result property="inspectCause" column="inspectCause" />
		<result property="rejectHandleType" column="rejectHandleType" />
		<result property="replenishQty" column="replenishQty" />
		<result property="mantissaQty" column="mantissaQty" />
		<result property="fedNum" column="fedNum" />
		<result property="returnQty" column="returnQty" />
		<result property="groupCenterId" column="groupCenterId"/>
		<result property="contractDeailId" column="contractDeailId"/>
		<result property="notificationNo" column="notificationNo"/>
		<result property="mergeType" column="mergeType"/>
		<result property="replenishMergeId" column="replenishMergeId"/>
		<result property="replenishMergeType" column="replenishMergeType"/>
		<result property="orderQty" column="orderQty"/>
		<result property="orderDeailArea" column="orderDeailArea"/>
		<association property="operator" javaType="User">
			<id property="recordId" column="operator.recordId"/>
			<result property="userName" column="operator.userName"/>
		</association>
		
		<association property="checker" javaType="User">
			<id property="recordId" column="checker.recordId"/>
			<result property="userName" column="checker.userName"/>
		</association>
		
		<association property="company" javaType="Company">
			<id property="recordId" column="company.recordId"/>
			<result property="name" column="company.name"/>
		</association>
		
		<association property="createdBy" javaType="User">
			<id property="recordId" column="createdBy.recordId"/>
		</association>
		
		<association property="lastUpdBy" javaType="User">
			<id property="recordId" column="lastUpdBy.recordId"/>
		</association>
		
		<association property="produceBatch" column="produceBatchId" javaType="ProduceBatch">
			<id property="recordId" column="produceBatch.recordId"/>
			<result property="no" column="produceBatch.no"/>
			<result property="discardQty" column="produceBatch.discardQty"/>
			
			<association property="notification" column="notificationId" javaType="Notification">
				<id property="recordId" column="produceBatch.notification.recordId"/>
				<result property="no" column="produceBatch.notification.no"/>
				<result property="craftNo" column="produceBatch.notification.craftNo"/>
				<result property="mergeType" column="produceBatch.notification.mergeType"/>
				
				<association property="cardA" javaType="CardA">
					<id property="recordId" column="produceBatch.notification.cardA.recordId"/>
				</association>
			</association>
			
			<association property="feed" javaType="Feeding">
				<id property="recordId" column="produceBatch.feed.recordId"/>
				<result property="no" column="produceBatch.feed.no"/>
			</association>
			
			<association property="replenish" javaType="Replenish">
				<id property="recordId" column="produceBatch.replenish.recordId"/>
				<result property="no" column="produceBatch.replenish.no"/>
			</association>
		</association>
		<association property="process" column="processId" javaType="EgProcess">
			<id property="recordId" column="process.recordId"/>
			<result property="name" column="process.name"/>
		</association>
		<association property="produceBatchDetail" column="produceBatchDetailId" javaType="ProduceBatchDetail">
			<id property="recordId" column="produceBatchDetail.recordId"/>
			<result property="no" column="produceBatchDetail.no"/>
			<result property="discardQty" column="produceBatchDetail.discardQty"/>
		</association>
		
		<collection property="reworkList" column="recordId" select="selectReworkList"/>
		<collection property="discardList" column="recordId" select="selectDiscardList"/>
		<collection property="badList" column="recordId" select="selectBadList"/>
	</resultMap>
	
    <resultMap id="inspect_batch_notifMap_show_export" type="Inspect">
	</resultMap>
    
    <resultMap id="user_reworkResult" type="Rework">
        <id property="recordId" column="recordId" />
		<result property="company.recordId" column="companyId" />
		<result property="reworkQty" column="reworkQty" />
		<result property="inspectId" column="inspectId" />
		<result property="craftNo" column="craftNo" />
		<result property="inspectId" column="inspectId" />
		<result property="reworkCause" column="reworkCause" />
		<result property="reworkResult" column="reworkResult" />
		<result property="createdDate" column="createdDate" />
		<result property="lastUpdDate" column="lastUpdDate" />
		<result property="remark" column="remark" />
		<result property="dutyProcessMore" column="dutyProcessMore" />
		<result property="dutyProcessMoreName" column="dutyProcessMoreName" />
		<result property="reworkType" column="reworkType" />
		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
			<result property="name" column="company.name"/>
		</association>
		<association property="boardPartCraft" javaType="BoardPartCraft">
			<id property="recordId" column="boardPartCraft.recordId"/>
			<result property="craftNo" column="boardPartCraft.craftNo" />
		</association>
		<association property="dutyProcess" javaType="EgProcess">
			<id property="recordId" column="dutyProcess.recordId"/>
			<result property="name" column="dutyProcess.name" />
			<result property="category" column="dutyProcess.category" />
		</association>
		<association property="createdBy" column="createdBy" javaType="User">
			<id property="recordId" column="createdBy.recordId"/>
		</association>
		<association property="lastUpdBy" column="lastUpdBy" javaType="User">
			<id property="recordId" column="lastUpdBy.recordId"/>
		</association>
		<association property="reworker" column="reworkerId" javaType="User">
			<id property="recordId" column="reworker.recordId"/>
			<result property="userName" column="reworker.userName"/>
		</association>
		<association property="checker" column="checkerId" javaType="User">
			<id property="recordId" column="checker.recordId"/>
			<result property="userName" column="checker.userName"/>
		</association>
    </resultMap>
    
    <!-- 通过一个ID查询一条报废登记记录 -->
	<select id="selectDiscardList" resultType="Discard">
		SELECT 
			a.recordid AS "recordId",
			a.companyid AS "company.recordId",
			a.inspectid AS "inspectId",
			a.boardPartCraftId AS "boardPartCraft.recordId",
			a.craftNo as "boardPartCraft.craftNo",
			a.craftNo AS "craftNo",
			a.discardPcsQty AS "discardPcsQty",
			a.discardPcsQtyA AS "discardPcsQtyA",
			a.discardPcsQtyB AS "discardPcsQtyB",
			a.discardcause AS "discardCause",
			a.discardCauseOne AS "discardCauseOne",
			a.discardType AS "discardType",
			a.dutyProcess AS "dutyProcess.recordId",
			IFNULL(pro.name,
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(p.name," ",""))
			WHEN 2 THEN CONCAT(REPLACE(p.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(p.name," ","") END
			) AS "dutyProcess.name",
			IFNULL(pro.name,CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(p.category," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(p.category," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(p.category," ","") END ) AS "dutyProcess.category",
			a.activeflag AS "activeFlag",
			a.createdby AS "createdBy.recordId",
			a.createddate AS "createdDate",
			a.lastupdby AS "lastUpdBy.recordId",
			a.lastupddate AS "lastUpdDate",
			a.remark AS "remark"
		FROM qc_discard a 
		LEFT JOIN qc_inspect qi ON qi.recordId = a.inspectId AND qi.activeFlag = 1
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = qi.produceBatchId AND ppb.activeFlag = 1
		LEFT JOIN eg_process p on p.recordId = a.dutyProcess
		LEFT JOIN eg_carda_process_value ecpv on ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = p.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci ON epci.processId = p.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		LEFT JOIN (
			SELECT  mdv.companyId,mdv.itemId,mdv.`value`,mdv.type,mdv.processIds,mdv.recordId,IFNULL(epcv.`name`,ep.category) AS 'name' FROM md_dict_value mdv
			LEFT JOIN eg_process ep ON ep.recordId = mdv.processIds AND mdv.type = 1
			LEFT JOIN eg_process_craft_values epcv ON epcv.recordId = mdv.processIds AND mdv.type = 2
			WHERE  mdv.itemId = 77
			GROUP BY mdv.companyId,mdv.itemId,mdv.`value`
		) pro ON pro.companyId = a.companyId AND pro.`value` = a.discardCauseOne
		
		WHERE a.inspectId IN (
			SELECT
				IF
					( qi.replenishMergeType = 200602, qim.recordId, qi.recordId ) AS 'recordId' 
				FROM
					qc_inspect qi
					LEFT JOIN qc_inspect qim ON qim.replenishMergeId = qi.recordId 
					AND qim.replenishMergeType = 200603 
					AND qim.activeFlag = 1 
					AND qi.replenishMergeType = 200602 
				WHERE FIND_IN_SET(qi.recordId,#{recordId})
		) and a.activeFlag=1
	</select>
	
	<select id="selectDiscardListExport" parameterType="Long" resultType="Discard">
		select 
			a.recordid AS "recordId",
			a.discardpcsqty AS "discardPcsQty",
		from qc_discard a 
		where a.inspectId=#{recordId} and a.activeFlag=1
	</select>

	<select id="selectReworkListExport" parameterType="Long" resultType="Rework">
		select 
			a.recordid AS "recordId",
			a.reworkqty AS "reworkQty"
		from qc_rework a 
		where a.inspectId=#{recordId} and a.activeFlag=1
	</select>
	
	<!-- 通过一个ID查询一条报废登记记录 -->
	<select id="selectBadList" resultType="Bad">
		SELECT 
			a.recordid AS "recordId",
			a.companyid AS "company.recordId",
			a.inspectid AS "inspectId",
			a.boardPartCraftId AS "boardPartCraft.recordId",
			a.craftNo as "boardPartCraft.craftNo",
			a.craftNo AS "craftNo",
			a.badQty AS "badQty",
			a.badCause AS "badCause",
			a.dutyProcess AS "dutyProcess.recordId",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(p.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(p.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(p.name ," ","") END AS "dutyProcess.name",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(p.category," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(p.category," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(p.category ," ","") END AS "dutyProcess.category",
			a.activeflag AS "activeFlag",
			a.createdby AS "createdBy.recordId",
			a.createddate AS "createdDate",
			a.lastupdby AS "lastUpdBy.recordId",
			a.lastupddate AS "lastUpdDate",
			a.remark AS "remark"
		FROM qc_bad a 
		LEFT JOIN qc_inspect qi ON qi.recordId = a.inspectId AND qi.activeFlag = 1
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = qi.produceBatchId AND ppb.activeFlag = 1
		LEFT JOIN eg_process p on p.recordId = a.dutyProcess
		LEFT JOIN eg_carda_process_value ecpv on ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = p.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci ON epci.processId = p.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		WHERE FIND_IN_SET(a.inspectId,#{recordId}) AND a.activeFlag=1
	</select>
	
    <select id="selectUserById" parameterType="Long" resultType="User">
    	select recordId,userName from sm_user where recordId=#{recordId}
    </select>
    
    <select id="selectFeedingById" parameterType="Long" resultType="Feeding">
    	select recordId,no,orderQuantity,pcsQuantity,status from pd_feeding where recordId=#{recordId}
    </select>
    
    <select id="getCountByBatchDetail" resultType="java.lang.Integer">
		select count(1) from qc_inspect where produceBatchDetailId = #{produceBatchDetail.recordId}  and companyId = #{company.recordId}
    </select>
    
    <select id="isNeedAuditByBatchDetail" resultType="Inspect">
	    select 
	    	a.recordId,
	    	SUM(r.reworkQty) as "Aqty", 
	    	SUM(d.discardPcsQty) as "Bqty", 
	    	SUM(b.badQty) as "failedPcsQty", 
	    	a.lastupdby as "lastUpdBy.recordId",
			a.lastupddate as "lastUpdDate"
	    from qc_inspect a
		left join qc_rework r on r.inspectId = a.recordId and r.activeFlag = #{DEL_FLAG_NORMAL} 
		left join qc_discard d on d.inspectId = a.recordId and d.activeFlag = #{DEL_FLAG_NORMAL}
		left join qc_bad b on b.inspectId = a.recordId and b.activeFlag = #{DEL_FLAG_NORMAL}
		where a.companyId = #{company.recordId} 
			and a.produceBatchDetailId = #{produceBatchDetail.recordId} 
			and processId = #{process.recordId}
    </select>
    
	<sql id="qcInspectColumns">
		a.recordid AS "recordId",
		a.companyid AS "company.recordId",
		a.producebatchid AS "produceBatch.recordId",
		a.produceBatchDetailId as "produceBatchDetail.recordId",
		a.processid AS "process.recordId",
		a.manuftype AS "manufType",
		a.pcsesperpnl AS "pcsEsperPnl",
		a.producepcsqty AS "producePcsQty",
		a.passpcsqty AS "passPcsQty",
		a.replaceQty,
		a.failedpcsqty AS "failedPcsQty",
		a.operator AS "operator.recordId",
		a.checker AS "checker.recordId",
		a.checkdate AS "checkDate",
		a.activeflag AS "activeFlag",
		a.createdby AS "createdBy.recordId",
		a.createddate AS "createdDate",
		a.lastupdby AS "lastUpdBy.recordId",
		a.lastupddate AS "lastUpdDate",
		a.remark AS "remark",
		a.status AS "status", 
		a.auditStatus AS "auditStatus", 
		mc.name as "company.name",
		a.onlyReplenish,
		a.replenishMergeType,
		a.replenishMergeId
	</sql>
	
	<sql id="qcInspectJoins">
		JOIN md_company mc on mc.recordId = a.companyId
	</sql>
	
	<select id="get" resultType="Inspect">
		SELECT 
			<include refid="qcInspectColumns"/>,
			a.type,
   			ppb.no AS "produceBatch.no",
			ppb.status AS "produceBatch.status",
			ppb.discardQty AS "produceBatch.discardQty",
			n.recordId AS "produceBatch.notification.recordId",
			n.no AS "produceBatch.notification.no",
			n.craftNo AS "produceBatch.notification.craftNo",
			a.replenishQty 
		FROM qc_inspect a
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId AND ppb.`status` IN (900301,900305,900306)	
		LEFT JOIN sl_notification n ON n.recordId = ppb.notificationId AND n.`status` IN (200403,200404,200405)
		<include refid="qcInspectJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="getByIdTest" resultMap="inspect_batch_notifMap">
		SELECT 
			<include refid="qcInspectColumns"/>,
			n.recordId AS "produceBatch.notification.recordId",
			n.no AS "produceBatch.notification.no",
			n.craftNo AS "produceBatch.notification.craftNo",
			ppbd.qcStatFlag AS "produceBatchDetail.qcStatFlag",
			so.userName AS "operator.userName",
			sc.userName AS "checker.userName"
		FROM qc_inspect a
		<include refid="qcInspectJoins"/>
		JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId  and ppb.activeFlag = #{DEL_FLAG_NORMAL} 
		JOIN pd_produce_batch_detail ppbd ON ppbd.recordId = a.produceBatchDetailId and ppbd.activeFlag = #{DEL_FLAG_NORMAL}   
		JOIN sl_notification n ON n.recordId = ppb.notificationId and n.activeFlag = #{DEL_FLAG_NORMAL}  
		LEFT JOIN sm_user so ON so.recordId = a.operator
		LEFT JOIN sm_user sc ON sc.recordId = a.checker
		WHERE a.activeFlag = #{DEL_FLAG_NORMAL} and a.status = #{status} and a.companyid = #{company.recordId}
	</select>
	
	<select id="findList" resultMap="inspect_batch_notifMap">
		SELECT * FROM (
		SELECT 
			DISTINCT
			<include refid="qcInspectColumns"/>,
			ppb.no AS "produceBatch.no",
			ppb.status AS "produceBatch.status",
			ppb.discardQty AS "produceBatch.discardQty",
			n.recordId AS "produceBatch.notification.recordId",
			n.no AS "produceBatch.notification.no",
			n.craftNo AS "produceBatch.notification.craftNo",
			n.mergeType AS "produceBatch.notification.mergeType",
			n.processCardAId AS "produceBatch.notification.cardA.recordId",
			f.recordId AS "produceBatch.feed.recordId",
			f.no AS "produceBatch.feed.no",
			r.recordId AS "produceBatch.replenish.recordId",
			r.no AS "produceBatch.replenish.no",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(eg.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(eg.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(eg.name ," ","") END AS "process.name",
			pbd.no AS "produceBatchDetail.no",
			pbd.status AS "produceBatchDetail.status",
			pbd.discardQty AS "produceBatchDetail.discardQty",
			ppr.handOverFlag AS "handOverFlag",
			so.userName AS "operator.userName",
			sc.userName AS "checker.userName",
			a.type,
			NULL AS "rejectNo",
			a.inspectCause AS "inspectCause",
			a.rejectHandleType,
			a.replenishQty,
			a.mantissaQty,
			0 AS 'fedNum',
			0 AS "returnQty",
			a.mergeType,
-- 			bb.recordId AS 'replenishIds',
			rscd.orderDeailArea AS "orderDeailArea",
			n.quantity AS "orderQty"
<!-- 			rscd.recordId AS 'contractDeailId',
			rscd.groupCenterId AS 'groupCenterId',
			sn2.no AS 'notificationNo' -->
		FROM qc_inspect a
		<include refid="qcInspectJoins"/>
		JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId AND ppb.`status` IN (900301,900305,900306) AND a.type in (1,4)
		JOIN pd_produce_batch_detail pbd ON pbd.recordId = a.produceBatchDetailId AND pbd.`status` IN (900301,900305,900306)
		JOIN eg_process eg ON eg.recordId = a.processId
		JOIN sl_notification n ON n.recordId = ppb.notificationId AND n.`status` IN (200403,200404,200405)
		JOIN pd_produce_record ppr ON ppr.produceBatchId = a.produceBatchId AND ppr.produceBatchDetailId = a.produceBatchDetailId AND ppr.processId = a.processId
		LEFT JOIN sm_user so ON so.recordId = a.operator
		LEFT JOIN sm_user sc ON sc.recordId = a.checker
		LEFT JOIN pd_feeding f ON f.recordId = ppb.feedingNo AND f.notificationId = ppb.notificationId AND f.activeFlag = 1 AND f.status = 700102
		LEFT JOIN pd_replenish r ON r.produceBatchNo = ppb.recordId AND r.notificationId = ppb.notificationId AND r.activeFlag = 1 AND r.status = 700402
		LEFT JOIN qc_rework qrk ON a.recordId = qrk.inspectId AND qrk.activeFlag = 1
		LEFT JOIN eg_carda_process_value ecpv ON ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = eg.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci ON epci.processId = eg.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1 
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		LEFT JOIN sl_contract_detail rscd ON rscd.inspectId = a.recordId
<!-- 		LEFT JOIN st_reject_application sra ON sra.companyId = a.companyId AND sra.recordId = a.produceBatchId AND a.type IN (2,3)
		LEFT JOIN sl_contract_detail rscd ON rscd.inspectId = a.recordId AND rscd.companyId = a.companyId
		LEFT JOIN sl_notification sn2 ON sn2.contractDetailId = rscd.recordId AND sn2.activeFlag = 1 AND sn2.companyId = a.companyId -->
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}  AND a.type in (1,4)
			<if test="produceBatch!=null and produceBatch.notification!=null and produceBatch.notification.craftNo!=null and produceBatch.notification.craftNo!=''">
			 	AND REPLACE(n.craftNo," ","") LIKE CONCAT('%', REPLACE(#{produceBatch.notification.craftNo}," ",""), '%')
			 </if>
			 <if test="produceBatch!=null and produceBatch.feed!=null and produceBatch.feed.no!=null and produceBatch.feed.no!=''">
			 	AND (REPLACE(f.no," ","") LIKE CONCAT('%', REPLACE(#{produceBatch.feed.no}," ",""), '%') OR REPLACE(r.no," ","") LIKE CONCAT('%', REPLACE(#{produceBatch.feed.no}," ",""), '%'))
			 </if>
			 <if test="startDate!=null and startDate!='' and endDate!=null and endDate!=''">
			 	AND a.lastupddate BETWEEN #{startDate} AND #{endDate}
			 </if>
			 <if test="manufType==1"><!-- 这里用manufType存了检测类型 -->
			 	AND ppr.handOverFlag = 1
			 </if>
			 <if test="manufType==2">
			 	AND ppr.handOverFlag = 0
			 </if>
			 <if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} or a.operator = #{createdBy.recordId} or a.checker = #{createdBy.recordId} or qrk.reworkChecker = #{createdBy.recordId} or qrk.reworker = #{createdBy.recordId})
			</if>
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<if test="auditStatus != null and auditStatus != ''">
				AND a.auditStatus = #{auditStatus}
			</if>
			<if test="searchType != null and searchType != ''">
				<if test="searchType == 1">
					AND a.type = 1
				</if>
				<if test="searchType == 2">
					AND a.type in (2,3)
				</if>
			</if>
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="rejectNo != null and rejectNo != ''">
				AND a.produceBatchId IN 
				(
					SELECT 
						recordId 
					FROM st_reject_application 
					WHERE companyId = a.companyId AND activeFlag = 1 
					AND REPLACE(`no`," ","") LIKE CONCAT('%',REPLACE(#{rejectNo}," ",""),'%')
				)
			</if>
			<if test="recordId != null and recordId != ''">
				AND a.recordId = #{recordId}
			</if>
		</where>
		
		UNION ALL 

		SELECT DISTINCT
		a.recordid AS "recordId",
		a.companyid AS "company.recordId",
		a.producebatchid AS "produceBatch.recordId",
		a.produceBatchDetailId AS "produceBatchDetail.recordId",
		a.processid AS "process.recordId",
		a.manuftype AS "manufType",
		a.pcsesperpnl AS "pcsEsperPnl",
		a.producepcsqty AS "producePcsQty",
		a.passpcsqty AS "passPcsQty",
		a.replaceQty,
		a.failedpcsqty AS "failedPcsQty",
		a.operator AS "operator.recordId",
		a.checker AS "checker.recordId",
		a.checkdate AS "checkDate",
		a.activeflag AS "activeFlag",
		a.createdby AS "createdBy.recordId",
		a.createddate AS "createdDate",
		a.lastupdby AS "lastUpdBy.recordId",
		a.lastupddate AS "lastUpdDate",
		a.remark AS "remark",
		a.STATUS AS "status",
		a.auditStatus AS "auditStatus",
		mc.NAME AS "company.name",
		a.onlyReplenish,
		a.replenishMergeType,
		a.replenishMergeId,
		a.producebatchid AS "produceBatch.no",
		'' AS "produceBatch.status",
		'' AS "produceBatch.discardQty",
		sra.recordId AS "produceBatch.notification.recordId",
		sra.`no` AS "produceBatch.notification.no",
		m.`no` AS "produceBatch.notification.craftNo",
		'' AS "produceBatch.notification.mergeType",
		'' AS "produceBatch.notification.cardA.recordId",
		'' AS "produceBatch.feed.recordId",
		'' AS "produceBatch.feed.no",
		'' AS "produceBatch.replenish.recordId",
		'' AS "produceBatch.replenish.no",
		(SELECT `name` FROM eg_process WHERE recordId = a.processid LIMIT 1) AS "process.name",
		a.produceBatchDetailId AS "produceBatchDetail.no",
		'' AS "produceBatchDetail.status",
		'' AS "produceBatchDetail.discardQty",
		'' AS "handOverFlag",
		so.userName AS "operator.userName",
		sc.userName AS "checker.userName",
		a.type,
		sra.no AS "rejectNo",
		a.inspectCause AS "inspectCause",
		a.rejectHandleType,
		a.replenishQty,
		a.mantissaQty,
		b.orderDeailArea AS "orderDeailArea",
		sra.fedNum AS 'fedNum',
		sra.quantity AS "returnQty",
		a.mergeType,
		b.quantity AS "orderQty"
<!-- 		rscd.recordId AS 'contractDeailId',
		rscd.groupCenterId AS 'groupCenterId',
		sn2.no AS 'notificationNo' -->
		FROM qc_inspect a
		JOIN md_company mc ON mc.recordId = a.companyId
		LEFT JOIN sm_user so ON so.recordId = a.operator
		LEFT JOIN sm_user sc ON sc.recordId = a.checker
		LEFT JOIN qc_rework qrk ON a.recordId = qrk.inspectId 
		AND qrk.activeFlag = 1
		LEFT JOIN st_reject_application sra ON sra.recordId = a.produceBatchId AND sra.companyId = a.companyId
		LEFT JOIN md_material m ON m.recordId = sra.materialId
		LEFT JOIN sl_contract_detail b ON b.recordId = sra.recordId
<!-- 		LEFT JOIN sl_contract_detail rscd ON rscd.inspectId = a.recordId AND rscd.companyId = a.companyId
		LEFT JOIN sl_notification sn2 ON sn2.contractDetailId = rscd.recordId AND sn2.activeFlag = 1 AND sn2.companyId = a.companyId -->
		<where>
			a.companyid = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL} AND a.type in( 2,3)
			<if test="produceBatch!=null and produceBatch.notification!=null and produceBatch.notification.craftNo!=null and produceBatch.notification.craftNo!=''">
			 	AND REPLACE(m.`no`," ","") LIKE CONCAT('%', REPLACE(#{produceBatch.notification.craftNo}," ",""), '%')
			 </if>
			 <if test="produceBatch!=null and produceBatch.feed!=null and produceBatch.feed.no!=null and produceBatch.feed.no!=''">
			 	AND 1 = 2
			 </if>
			 <if test="startDate!=null and startDate!='' and endDate!=null and endDate!=''">
			 	AND a.lastupddate between #{startDate} AND #{endDate}
			 </if>
			 <if test="manufType==1"><!-- 这里用manufType存了检测类型 -->
			 	AND 1 = 2
			 </if>
			 <if test="manufType==2">
			 	AND 1 = 2
			 </if>
			 <if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} OR a.operator = #{createdBy.recordId} OR a.checker = #{createdBy.recordId} OR qrk.reworkChecker = #{createdBy.recordId} OR qrk.reworker = #{createdBy.recordId})
			</if>
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<if test="auditStatus != null and auditStatus != ''">
				AND a.auditStatus = #{auditStatus}
			</if>
			<if test="searchType != null and searchType != ''">
				<if test="searchType == 1">
					AND a.type = 1
				</if>
				<if test="searchType == 2">
					AND a.type in (2,3)
				</if>
			</if>
			<if test="type != null and type != ''">
				AND a.type = #{type}
			</if>
			<if test="rejectNo != null and rejectNo != ''">
				AND a.produceBatchId IN 
				(
					SELECT 
						recordId 
					FROM st_reject_application 
					WHERE companyId = a.companyId AND activeFlag = 1 
					AND REPLACE(`no`," ","") LIKE CONCAT('%',REPLACE(#{rejectNo}," ",""),'%')
				)
			</if>
			<if test="recordId != null and recordId != ''">
				AND a.recordId = #{recordId}
			</if>
		</where>
			) r
		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
<!--		<select id="findListExport" resultMap="inspect_batch_notifMap_show_export">
		SELECT 
			a.recordid AS "recordId",
			a.producebatchid AS "produceBatch.recordId",
			a.processid AS "process.recordId",
			a.producepcsqty AS "producePcsQty",
			a.checkdate AS "checkDate",
			a.activeflag AS "activeFlag",
			ppb. NO AS "produceBatch.no",
			n.recordId AS "produceBatch.notification.recordId",
			n.no AS "produceBatch.notification.no",
            IFNULL
            (
                n.craftNo,
                (
                    SELECT
                    aa.craftNo
                    FROM sl_notification aa
                    LEFT JOIN sl_contract_detail bb ON bb.recordId = aa.contractDetailId
                    WHERE aa.companyId = a.companyId AND bb.inspectId = a.recordId
                    AND aa.activeFlag = 1 LIMIT 1
                )
            ) AS "produceBatch.notification.craftNo",
			eg.recordId as "process.recordId",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(eg.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(eg.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(eg.name ," ","") END AS "process.name",
			a.checker AS "checker.recordId",
			sc.userName AS "checker.userName",
			sum(qrk.reworkQty) as "reworkQty",
			sum(qrd.discardPcsQty) as "discardQty",
			a.replenishQty AS "replenishQty",
			sra.no AS "rejectNo",
			a.type,
            (
                SELECT
                aa.`no` AS "noticationNo"
                FROM sl_notification aa
                LEFT JOIN sl_contract_detail bb ON bb.recordId = aa.contractDetailId
                WHERE aa.companyId = a.companyId AND bb.inspectId = a.recordId
                AND aa.activeFlag = 1 LIMIT 1
            ) AS "noticationNo"
		FROM qc_inspect a
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId AND ppb.`status` IN (900301,900305,900306)
		LEFT JOIN pd_produce_batch_detail pbd ON pbd.recordId = a.produceBatchDetailId AND pbd.`status` IN (900301,900305,900306)
		LEFT JOIN eg_process eg ON eg.recordId = a.processId
		LEFT JOIN sl_notification n ON n.recordId = ppb.notificationId AND n.`status` IN (200403,200404,200405)
		LEFT JOIN pd_produce_record ppr ON ppr.produceBatchId = a.produceBatchId AND ppr.produceBatchDetailId = a.produceBatchDetailId AND ppr.processId = a.processId
		LEFT JOIN sm_user so ON so.recordId = a.operator
		LEFT JOIN sm_user sc ON sc.recordId = a.checker
		LEFT JOIN pd_feeding f ON f.recordId = ppb.feedingNo AND f.notificationId = ppb.notificationId AND f.activeFlag = 1 AND f.status = 700102
		LEFT JOIN pd_replenish r ON r.produceBatchNo = ppb.recordId AND r.notificationId = ppb.notificationId AND r.activeFlag = 1 AND r.status = 700402
		LEFT JOIN qc_rework qrk ON a.recordId = qrk.inspectId AND qrk.activeFlag = 1
		LEFT JOIN qc_discard qrd on qrd.inspectId = a.recordId AND qrd.activeFlag = 1
		LEFT JOIN eg_carda_process_value ecpv ON ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = eg.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci ON epci.processId = eg.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1 
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		LEFT JOIN st_reject_application sra ON sra.recordId = a.produceBatchId AND a.type IN (2,3)
		<where>
			a.companyid = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
			<if test="produceBatch!=null and produceBatch.notification!=null and produceBatch.notification.craftNo!=null and produceBatch.notification.craftNo!=''">
			 	and n.craftNo like CONCAT('%', #{produceBatch.notification.craftNo}, '%')
			 </if>
			 <if test="produceBatch!=null and produceBatch.feed!=null and produceBatch.feed.no!=null and produceBatch.feed.no!=''">
			 	and (f.no like CONCAT('%', #{produceBatch.feed.no}, '%') or r.no like CONCAT('%', #{produceBatch.feed.no}, '%'))
			 </if>
			 <if test="startDate!=null and startDate!='' and endDate!=null and endDate!=''">
			 	and a.lastupddate between #{startDate} and #{endDate}
			 </if>
			 <if test="manufType==1">&lt;!&ndash; 这里用manufType存了检测类型 &ndash;&gt;
			 	and ppr.handOverFlag = 1
			 </if>
			 <if test="manufType==2">
			 	and ppr.handOverFlag = 0
			 </if>
			 <if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} or a.operator = #{createdBy.recordId} or a.checker = #{createdBy.recordId} or qrk.reworkChecker = #{createdBy.recordId} or qrk.reworker = #{createdBy.recordId})
			</if>
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<if test="auditStatus != null and auditStatus != ''">
				AND a.auditStatus = #{auditStatus}
			</if>
			<if test="type == 1 and type != ''">
				AND a.type = 1
			</if>
			<if test="type == 2 and type != ''">
				AND a.type in(2,3)
			</if>
			<if test="rejectNo != null and rejectNo != ''">
				AND sra.no like CONCAT('%', #{rejectNo}, '%') 
			</if>
			<if test="recordId != null and recordId != ''">
				AND a.recordId = #{recordId}
			</if>
		</where>
		group by a.recordId
		ORDER BY a.checkdate DESC
	</select>-->
	
	<select id="findListOfShow" resultMap="inspect_batch_notifMap">
		SELECT 
			<include refid="qcInspectColumns"/>,
			ppb. NO AS "produceBatch.no",
			ppb. STATUS AS "produceBatch.status",
			ppb.discardQty AS "produceBatch.discardQty",
			n.recordId AS "produceBatch.notification.recordId",
			n.no AS "produceBatch.notification.no",
			n.craftNo AS "produceBatch.notification.craftNo",
			n.mergeType AS "produceBatch.notification.mergeType",
			f.recordId AS "produceBatch.feed.recordId",
			f.no AS "produceBatch.feed.no",
			r.recordId AS "produceBatch.replenish.recordId",
			r.no AS "produceBatch.replenish.no",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(eg.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(eg.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(eg.name ," ","") END AS "process.name",
			pbd.no AS "produceBatchDetail.no",
			pbd.status AS "produceBatchDetail.status",
			pbd.discardQty AS "produceBatchDetail.discardQty",
			ppr.handOverFlag AS "handOverFlag",
			so.userName AS "operator.userName",
			sc.userName AS "checker.userName"
		FROM qc_inspect a
		<include refid="qcInspectJoins"/>
		JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId and ppb.status <![CDATA[<>]]> #{produceBatch.status} and ppb.activeFlag = #{DEL_FLAG_NORMAL}
		JOIN pd_produce_batch_detail pbd ON pbd.recordId = a.produceBatchDetailId and pbd.`status` = #{produceBatchDetail.status}
		JOIN eg_process eg ON eg.recordId = a.processId
		JOIN sl_notification n ON n.recordId = ppb.notificationId and n.status in ${produceBatch.notification.status}
		JOIN pd_produce_record ppr ON ppr.produceBatchId = a.produceBatchId AND ppr.produceBatchDetailId = a.produceBatchDetailId AND ppr.processId = a.processId
		LEFT JOIN sm_user so ON so.recordId = a.operator
		LEFT JOIN sm_user sc ON sc.recordId = a.checker
		LEFT JOIN pd_feeding f ON f.recordId = ppb.feedingNo and f.notificationId = ppb.notificationId and f.activeFlag = #{DEL_FLAG_NORMAL} and f.status in ${produceBatch.feed.status}
		LEFT JOIN pd_replenish r ON r.produceBatchNo = ppb.recordId and r.notificationId = ppb.notificationId and r.activeFlag = #{DEL_FLAG_NORMAL} and r.status in ${produceBatch.replenish.status}
		LEFT JOIN qc_rework qrk ON a.recordId = qrk.inspectId and qrk.activeFlag = #{DEL_FLAG_NORMAL}
		LEFT JOIN eg_carda_process_value ecpv on ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = eg.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci ON epci.processId = eg.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		<where>
			a.companyid = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
			<if test="produceBatch!=null and produceBatch.notification!=null and produceBatch.notification.craftNo!=null and produceBatch.notification.craftNo!=''">
			 	and n.craftNo like CONCAT('%', #{produceBatch.notification.craftNo}, '%')
			 </if>
			 <if test="produceBatch!=null and produceBatch.feed!=null and produceBatch.feed.no!=null and produceBatch.feed.no!=''">
			 	and (f.no like CONCAT('%', #{produceBatch.feed.no}, '%') or r.no like CONCAT('%', #{produceBatch.feed.no}, '%'))
			 </if>
			 <if test="startDate!=null and startDate!='' and endDate!=null and endDate!=''">
			 	and a.checkDate between #{startDate} and #{endDate}
			 </if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} or a.operator = #{createdBy.recordId} or a.checker = #{createdBy.recordId}
				or qrk.reworkChecker = #{createdBy.recordId} or qrk.reworker = #{createdBy.recordId})
			</if>
		</where>
		group by a.recordId
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findListOfShowExport" resultMap="inspect_batch_notifMap_show_export">
		SELECT 
			a.recordid AS "recordId",
			a.producebatchid AS "produceBatch.recordId",
			a.processid AS "process.recordId",
			a.producepcsqty AS "producePcsQty",
			a.checkdate AS "checkDate",
			a.activeflag AS "activeFlag",
		
			ppb. NO AS "produceBatch.no",
			
			n.recordId AS "produceBatch.notification.recordId",
			n.no AS "produceBatch.notification.no",
			n.craftNo AS "produceBatch.notification.craftNo",
			eg.recordId as "process.recordId",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(eg.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(eg.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(eg.name ," ","") END AS "process.name",
			a.checker AS "checker.recordId",
			sc.userName AS "checker.userName",
			sum(qrk.reworkQty) as "reworkQty",
			sum(qrd.discardPcsQty) as "discardQty"
		FROM qc_inspect a
		JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId and ppb.status <![CDATA[<>]]> #{produceBatch.status} and ppb.activeFlag = #{DEL_FLAG_NORMAL}
		JOIN pd_produce_batch_detail pbd ON pbd.recordId = a.produceBatchDetailId and pbd.`status` = #{produceBatchDetail.status}
		JOIN eg_process eg ON eg.recordId = a.processId
		JOIN sl_notification n ON n.recordId = ppb.notificationId and n.status in ${produceBatch.notification.status}
		JOIN pd_produce_record ppr ON ppr.produceBatchId = a.produceBatchId AND ppr.produceBatchDetailId = a.produceBatchDetailId AND ppr.processId = a.processId
		LEFT JOIN sm_user sc ON sc.recordId = a.checker
		LEFT JOIN pd_feeding f ON f.recordId = ppb.feedingNo and f.notificationId = ppb.notificationId and f.activeFlag = #{DEL_FLAG_NORMAL} and f.status in ${produceBatch.feed.status}
		LEFT JOIN pd_replenish r ON r.produceBatchNo = ppb.recordId and r.notificationId = ppb.notificationId and r.activeFlag = #{DEL_FLAG_NORMAL} and r.status in ${produceBatch.replenish.status}
		
		LEFT JOIN qc_rework qrk ON qrk.inspectId = a.recordId and qrk.activeFlag = #{DEL_FLAG_NORMAL}
		LEFT JOIN qc_discard qrd on qrd.inspectId = a.recordId and qrd.activeFlag = #{DEL_FLAG_NORMAL}
		LEFT JOIN eg_carda_process_value ecpv on ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = eg.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci ON epci.processId = eg.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		<where>
			 a.companyid = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
			<if test="produceBatch!=null and produceBatch.notification!=null and produceBatch.notification.craftNo!=null and produceBatch.notification.craftNo!=''">
			 	and n.craftNo like CONCAT('%', #{produceBatch.notification.craftNo}, '%')
			 </if>
			 <if test="produceBatch!=null and produceBatch.feed!=null and produceBatch.feed.no!=null and produceBatch.feed.no!=''">
			 	and (f.no like CONCAT('%', #{produceBatch.feed.no}, '%') or r.no like CONCAT('%', #{produceBatch.feed.no}, '%'))
			 </if>
			 <if test="startDate!=null and startDate!='' and endDate!=null and endDate!=''">
			 	and a.checkDate between #{startDate} and #{endDate}
			 </if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} or a.operator = #{createdBy.recordId} or a.checker = #{createdBy.recordId} or qrk.reworkChecker = #{createdBy.recordId} or qrk.reworker = #{createdBy.recordId})
			</if>
		</where>
		group by a.recordId
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="Inspect">
		SELECT 
			<include refid="qcInspectColumns"/>
		FROM qc_inspect a
		<include refid="qcInspectJoins"/>
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<!-- zjn 2019-09-09 获取当前批次的过数工序列表 -->
	<select id="findProcessByBatch" resultType="EgProcess">
		SELECT
			ep.recordId,
			ep.category,
			ep.`name`
		FROM pd_produce_record a
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId AND ppb.companyId = a.companyId
		LEFT JOIN eg_carda ec ON ec.recordId = ppb.processCardAId AND ec.companyId = a.companyId
		LEFT JOIN eg_template_process_relation et ON et.templateId = ec.templateId AND et.processId = a.processId AND et.companyId = a.companyId
		LEFT JOIN eg_process ep ON ep.recordId  = a.processId AND ep.activeFlag = 1
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND a.produceBatchId = #{produceBatch.recordId} 
		AND a.produceBatchDetailId = #{produceBatchDetail.recordId} AND a.handOverFlag <![CDATA[<>]]> 1
		AND (ISNULL(et.skipCount) OR et.skipCount = 0) 
	</select>
	
	<resultMap id="inspect_Rework_Map" type="Inspect">
		<id property="recordId" column="recordId" />
		<result property="manufType" column="manufType" />
		<result property="pcsEsperPnl" column="pcsEsperPnl" />
		<result property="producePcsQty" column="producePcsQty" />
		<result property="passPcsQty" column="passPcsQty" />
		<result property="replaceQty" column="replaceQty" />
		<result property="failedPcsQty" column="failedPcsQty" />
		<result property="checkDate" column="checkDate" />
		<result property="status" column="status" />
		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
		</association>
		<association property="produceBatch" column="produceBatchId" javaType="ProduceBatch">
			<id property="recordId" column="produceBatch.recordId"/>
			<result property="createdDate" column="produceBatch.createdDate" />
		</association>
		<association property="produceBatchDetail" column="produceBatchDetailId" javaType="ProduceBatchDetail">
			<id property="recordId" column="produceBatchDetail.recordId"/>
		</association>
		<association property="process" column="processId" javaType="EgProcess">
			<id property="recordId" column="process.recordId"/>
		</association>
		<association property="operator" column="operator" javaType="User">
			<id property="recordId" column="operator.recordId"/>
		</association>
		<association property="checker" column="processId" javaType="User">
			<id property="recordId" column="checker.recordId"/>
		</association>
		
		<collection property="discardList" ofType="Discard">
			<id property="recordId" column="discard.recordId" />
			<result property="inspectId" column="discard.inspectId" />
			<result property="craftNo" column="discard.craftNo" />
			<result property="discardPcsQty" column="discard.discardPcsQty" />
			<result property="discardCause" column="discard.discardCause" />
			<result property="discardCauseOne" column="discard.discardCauseOne" />
			
			<result property="discardType" column="discard.discardType" />
			<result property="remark" column="discard.remark" />
			
			<association property="company" javaType="Company">
				<id property="recordId" column="discard.company.recordId"/>
			</association>
			<association property="boardPartCraft" javaType="BoardPartCraft">
				<id property="recordId" column="discard.boardPartCraft.recordId"/>
			</association>
			<association property="dutyProcess" javaType="EgProcess">
				<id property="recordId" column="discard.dutyProcess.recordId"/>
			</association>
		</collection>
			
		<collection property="reworkList" ofType="Rework">
			<id property="recordId" column="rework.recordId" />
			<result property="inspectId" column="rework.inspectId"/>
			<result property="craftNo" column="rework.craftNo" />
			<result property="reworkQty" column="rework.reworkQty"/>
			<result property="reworkCause" column="rework.reworkCause"/>
			<result property="reworkResult" column="rework.reworkResult"/>
			<result property="remark" column="rework.remark" />
			
			<association property="company" javaType="Company">
				<id property="recordId" column="rework.company.recordId"/>
			</association>
			
			<association property="reworker" javaType="User">
				<id property="recordId" column="rework.reworker.recordId"/>
			</association>
			
			<association property="checker" javaType="User">
				<id property="recordId" column="rework.checker.recordId"/>
			</association>
		
			<association property="boardPartCraft" javaType="BoardPartCraft">
				<id property="recordId" column="rework.boardPartCraft.recordId"/>
			</association>
			
			<association property="dutyProcess" javaType="EgProcess">
				<id property="recordId" column="rework.dutyProcess.recordId"/>
			</association>
		</collection>
			
		<collection property="badList" ofType="Bad">
			<id property="recordId" column="bad.recordId" />
			<result property="inspectId" column="bad.inspectId"/>
			<result property="craftNo" column="bad.craftNo" />
			<result property="badQty" column="bad.badQty"/>
			<result property="badCause" column="bad.badCause"/>
			<result property="remark" column="bad.remark" />
			
			<association property="company" javaType="Company">
				<id property="recordId" column="bad.company.recordId"/>
			</association>
			
			<association property="boardPartCraft" javaType="BoardPartCraft">
				<id property="recordId" column="bad.boardPartCraft.recordId"/>
			</association>
			
			<association property="dutyProcess" javaType="EgProcess">
				<id property="recordId" column="bad.dutyProcess.recordId"/>
			</association>
		</collection>
	</resultMap>
	
	<select id="selectList" resultMap="inspect_Rework_Map">
		 SELECT 
			a.recordId,
			a.companyId AS "company.recordId",
			a.producebatchId AS "produceBatch.recordId",
			a.createdDate AS "produceBatch.createdDate",
			a.produceBatchDetailId as "produceBatchDetail.recordId",
			a.processId AS "process.recordId",
			a.manufType,
			a.pcsEsperPnl,
			a.producePcsQty,
			a.passPcsQty,
		    a.replaceQty,
			a.failedPcsQty,
			a.operator AS "operator.recordId",
			a.checker AS "checker.recordId",
			a.checkdate,
			a.status,
			
			qd.recordid AS "discard.recordId",
			qd.companyId AS "discard.company.recordId",
			qd.inspectid AS "discard.inspectId",
			qd.craftNo AS "discard.craftNo",
			qd.discardpcsqty AS "discard.discardPcsQty",
			qd.discardcause AS "discard.discardCause",
			qd.discardCauseOne AS "discard.discardCauseOne",
			qd.discardType AS "discard.discardType",
			qd.remark AS "discard.remark", 
			qd.boardpartcraftid AS "discard.boardPartCraft.recordId", 
			qd.dutyProcess AS "discard.dutyProcess.recordId", 
			
			qr.recordId AS "rework.recordId",
			qr.companyId AS "rework.company.recordId",
			qr.inspectId AS "rework.inspectId",
			qr.craftNo AS "rework.craftNo",
			qr.reworkQty AS "rework.reworkQty",
			qr.reworkCause AS "rework.reworkCause",
			qr.reworkResult AS "rework.reworkResult",
			qr.remark AS "rework.remark",
			qr.reworker AS "rework.reworker.recordId",
			qr.reworkchecker AS "rework.checker.recordId",
			qr.boardpartcraftid AS "rework.boardPartCraft.recordId",
			qr.dutyProcess AS "rework.dutyProcess.recordId",
			
			qb.recordid AS "bad.recordId",
			qb.companyId AS "bad.company.recordId",
			qb.inspectid AS "bad.inspectId",
			qb.craftNo AS "bad.craftNo",
			qb.badQty AS "bad.badQty",
			qb.badCause AS "bad.badCause",
			qb.remark AS "bad.remark",
			qb.boardpartcraftid AS "bad.boardPartCraft.recordId",
			qb.dutyProcess AS "bad.dutyProcess.recordId"
		FROM qc_inspect a 
		JOIN pd_produce_batch pb on pb.recordId = a.producebatchId
		LEFT JOIN qc_rework qr on qr.inspectId = a.recordId AND qr.activeFlag = #{DEL_FLAG_NORMAL} 
		LEFT JOIN qc_discard qd on qd.inspectId = a.recordId AND qd.activeFlag = #{DEL_FLAG_NORMAL} 
		LEFT JOIN qc_bad qb on qb.inspectId = a.recordId AND qb.activeFlag = #{DEL_FLAG_NORMAL} 
	    WHERE a.companyId = #{company.recordId} 
	    	AND a.produceBatchDetailId = #{recordId} 
	    	AND a.activeFlag = #{DEL_FLAG_NORMAL} 
	</select>
	
	<select id="getByProcessAndBatchDetail" resultMap="inspect_Rework_Map">
	    SELECT 
			a.recordId,
			a.companyId AS "company.recordId",
			a.producebatchId AS "produceBatch.recordId",
			a.createdDate AS "produceBatch.createdDate",
			a.produceBatchDetailId as "produceBatchDetail.recordId",
			a.processId AS "process.recordId",
			a.manufType,
			a.pcsEsperPnl,
			a.producePcsQty,
			a.passPcsQty,
	        a.replaceQty,
			a.failedPcsQty,
			a.operator AS "operator.recordId",
			a.checker AS "checker.recordId",
			a.checkdate,
			a.status,
			
			qd.recordid AS "discard.recordId",
			qd.companyId AS "discard.company.recordId",
			qd.inspectid AS "discard.inspectId",
			qd.craftNo AS "discard.craftNo",
			qd.discardpcsqty AS "discard.discardPcsQty",
			qd.discardcause AS "discard.discardCause",
			qd.discardCauseOne AS "discard.discardCauseOne",
			qd.discardType AS "discard.discardType",
			qd.remark AS "discard.remark", 
			qd.boardpartcraftid AS "discard.boardPartCraft.recordId", 
			qd.dutyProcess AS "discard.dutyProcess.recordId", 
			
			qr.recordId AS "rework.recordId",
			qr.companyId AS "rework.company.recordId",
			qr.inspectId AS "rework.inspectId",
			qr.craftNo AS "rework.craftNo",
			qr.reworkQty AS "rework.reworkQty",
			qr.reworkCause AS "rework.reworkCause",
			qr.reworkResult AS "rework.reworkResult",
			qr.remark AS "rework.remark",
			qr.reworker AS "rework.reworker.recordId",
			qr.reworkchecker AS "rework.checker.recordId",
			qr.boardpartcraftid AS "rework.boardPartCraft.recordId",
			qr.dutyProcess AS "rework.dutyProcess.recordId",
			
			qb.recordid AS "bad.recordId",
			qb.companyId AS "bad.company.recordId",
			qb.inspectid AS "bad.inspectId",
			qb.craftNo AS "bad.craftNo",
			qb.badQty AS "bad.badQty",
			qb.badCause AS "bad.badCause",
			qb.remark AS "bad.remark",
			qb.boardpartcraftid AS "bad.boardPartCraft.recordId",
			qb.dutyProcess AS "bad.dutyProcess.recordId"
		FROM qc_inspect a 
		JOIN pd_produce_batch pb on pb.recordId = a.producebatchId
		LEFT JOIN qc_rework qr on qr.inspectId = a.recordId AND qr.activeFlag = #{DEL_FLAG_NORMAL}
		LEFT JOIN qc_discard qd on qd.inspectId = a.recordId AND qd.activeFlag = #{DEL_FLAG_NORMAL}
		LEFT JOIN qc_bad qb on qb.inspectId = a.recordId AND qb.activeFlag = #{DEL_FLAG_NORMAL} 
	    WHERE a.companyId = #{company.recordId} 
	    	AND a.produceBatchDetailId = #{produceBatchDetail.recordId} 
	    	AND a.processId = #{process.recordId} 
	    	AND a.activeFlag = #{DEL_FLAG_NORMAL} 
	</select>
	
	<select id="getDiscardQtyByInspect" resultType="Inspect">
	    select 
			a.recordId as "recordId",
			a.producePcsQty as "producePcsQty",
			a.processId as "process.recordId",
			a.checkDate as "checkDate",
			SUM(d.discardPcsQty) as "failedPcsQty",
			a.producePcsQty as "producePcsQty",
			a.produceBatchId as "produceBatch.recordId",
			pb.notificationId as "produceBatch.notification.recordId",
			n.no as "produceBatch.notification.no",
			n.recordId as "produceBatch.notification.recordId",
			n.craftNo as "produceBatch.notification.craftNo",
			a.produceBatchDetailId as "produceBatchDetail.recordId"
		from qc_inspect a
		join qc_discard d on d.inspectId = a.recordId and d.activeFlag = #{DEL_FLAG_NORMAL}
		join pd_produce_batch pb on pb.recordId = a.produceBatchId
		join sl_notification n on n.recordId = pb.notificationId
  		where a.recordId = #{recordId} and a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	
	<select id="getCanDealQtyOfDiscard" resultType="java.lang.String">
		select 
			SUM(d.discardPcsQty) as "qty" 
		from qc_inspect a
		join qc_discard d on d.inspectId = a.recordId and d.activeFlag = #{DEL_FLAG_NORMAL}
		where a.companyId = #{company.recordId} and produceBatchDetailId = #{recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	
	<!--  tj 2017-12-06 查询报废记录 -->
	<select id="getDiscardList" resultType="Discard">
		select 
			a.recordId AS "inspectId",
			d.recordId AS "recordId",
			d.discardPcsQty AS "discardPcsQty",
			d.unit AS "unit"
		from qc_inspect a
		join qc_discard d on d.inspectId = a.recordId and d.activeFlag = #{DEL_FLAG_NORMAL}
		where a.companyId = #{company.recordId} 
		and produceBatchDetailId = #{recordId} 
		and a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	<select id="getDiscardPcsQtySum" resultType="java.lang.Integer">
		select 
			SUM(d.discardPcsQty) AS "qty"
		from qc_inspect a
		join qc_discard d on d.inspectId = a.recordId AND d.activeFlag = #{DEL_FLAG_NORMAL}
 		where a.companyId = #{company.recordId}
 		AND produceBatchDetailId = #{recordId}
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
		AND d.unit = 2
	</select>
	
	<select id="getCanDealQtyOfDiscardByCraftNo" resultType="java.lang.String">
		select 
			SUM(d.discardPcsQty) as "qty" 
		from qc_inspect a
		join qc_discard d on d.inspectId = a.recordId and d.activeFlag = #{DEL_FLAG_NORMAL}
		left join eg_cardb_board_part_craft b on b.recordId = d.boardPartCraftId 
		where a.companyId = #{company.recordId} 
			and produceBatchDetailId = #{produceBatchDetail.recordId} 
			and a.activeFlag = #{DEL_FLAG_NORMAL} 
			and d.craftNo = #{craftNo} and b.notificationId = #{notificationId} 
	</select>
	
	<select id="getCountByProcessAndBatchDetail" resultType="java.lang.Integer">
	    SELECT 
			count(1)
		FROM qc_inspect a 
	    WHERE a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL} 
	   		AND a.produceBatchId = #{produceBatch.recordId} AND a.produceBatchDetailId = #{produceBatchDetail.recordId} AND a.processId = #{process.recordId} 
	</select>
	
	<select id="getPopoByProcessAndBatchDetail" resultType="java.lang.Integer">
	    SELECT 
			popo
		FROM pd_produce_record a 
	    WHERE a.companyId = #{company.recordId} 
	    	AND a.produceBatchDetailId = #{produceBatchDetail.recordId} AND a.processId = #{process.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL} 
	</select>
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO qc_inspect(
			companyid,
			producebatchid,
			produceBatchDetailId,
			processid,
			manuftype,
			pcsesperpnl,
			producepcsqty,
			passpcsqty,
			failedpcsqty,
			operator,
			checker,
			checkdate,
			activeflag,
			createdby,
			createddate,
			lastupdby,
			lastupddate,
			remark,
			status,
			auditStatus,
			type,
			rejectHandleType,
			replenishQty,
			mantissaQty,
			taskId,
			mergeType,
			replenishMergeType,
			replenishMergeId,
			replaceQty
		) VALUES (
			#{company.recordId},
			#{produceBatch.recordId},
			#{produceBatchDetail.recordId},
			#{process.recordId},
			#{manufType},
			#{pcsEsperPnl},
			#{producePcsQty},
			#{passPcsQty},
			#{failedPcsQty},
			#{operator.recordId},
			#{checker.recordId},
			#{checkDate},
			#{activeFlag},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{status},
			#{auditStatus},
			#{type},
			#{rejectHandleType},
			#{replenishQty},
			#{mantissaQty},
			#{taskId},
			#{mergeType},
			#{replenishMergeType},
			#{replenishMergeId},
		    #{replaceQty}
		)
	</insert>
	
	<insert id="insertReworkList">
		INSERT INTO qc_rework(
			companyid,
			inspectid,
			reworkqty,
			reworkcause,
			reworkresult,
			reworker,
			reworkchecker,
			reworkdate,
			activeflag,
			createdby,
			createddate,
			lastupdby,
			lastupddate,
			remark
		)
		<foreach collection="reworkList" item="prd" separator=" union all ">
			SELECT  #{company.recordId}, #{recordId}, #{prd.reworkQty}, #{prd.reworkCause}, #{prd.reworkResult},#{prd.reworker.recordId}, 
			 #{prd.checker.recordId}, #{prd.reworkDate}, #{DEL_FLAG_NORMAL},#{prd.createdBy.recordId},#{prd.createdDate},
			#{prd.lastUpdBy.recordId},#{prd.lastUpdDate},#{prd.remark} FROM dual
		</foreach>
	</insert>
	
	<insert id="insertDiscardList">
		INSERT INTO qc_discard(
			companyid,
			inspectid,
			discardpcsqty,
			discardcause,
			discardCauseOne,
			discardType,
			activeflag,
			createdby,
			createddate,
			lastupdby,
			lastupddate,
			remark
		)
		<foreach collection="discardList" item="prd" separator=" union all ">
			SELECT  #{company.recordId}, #{recordId}, #{prd.discardPcsQty}, #{prd.discardCause}, #{prd.discardCauseOne}, #{prd.discardType}, #{DEL_FLAG_NORMAL},
			 #{prd.createdBy.recordId},#{prd.createdDate},
			#{prd.lastUpdBy.recordId},#{prd.lastUpdDate},#{prd.remark} FROM dual
		</foreach>
	</insert>
	
	<update id="update">
		UPDATE qc_inspect SET 	
			companyid = #{company.recordId},
			producebatchid = #{produceBatch.recordId},
			produceBatchDetailId = #{produceBatchDetail.recordId},
			processid = #{process.recordId},
			manuftype = #{manufType},
			pcsesperpnl = #{pcsEsperPnl},
			producepcsqty = #{producePcsQty},
			passpcsqty = #{passPcsQty},
			failedpcsqty = #{failedPcsQty},
			operator = #{operator.recordId},
			checker = #{checker.recordId},
			checkdate = #{checkDate},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate},
			remark = #{remark},
			rejectHandleType = #{rejectHandleType},
			replenishQty = #{replenishQty},
			mantissaQty = #{mantissaQty}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateInspect">
		UPDATE qc_inspect SET 	
			manuftype = #{manufType},
			operator = #{operator.recordId},
			checker = #{checker.recordId},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate},
			remark = #{remark}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateStatus">
		UPDATE qc_inspect SET 	
			auditStatus = #{auditStatus},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateReplenishMergeStatus">
		UPDATE qc_inspect SET 	
			auditStatus = #{auditStatus},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate}
		WHERE replenishMergeId = #{recordId} AND activeFlag = 1 AND replenishMergeType = 200603
	</update>
	
	<update id="updateFailedAndPassPcsQty">
		UPDATE qc_inspect SET 	
			passpcsqty = #{passPcsQty},
			failedpcsqty = #{failedPcsQty},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE qc_inspect SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId} AND companyId = #{company.recordId}
	</update>
	
	<select id="findNotificationList" resultType="Notification">
	    SELECT
			n.recordId AS "recordId",
			n. NO AS "no",
			n.craftNo AS "craftNo",
			n.failedPcsQty AS "failedPcsQty",
			n.feedPcsQty AS "feedPcsQty",
			n.deliveryDate AS "deliveryDate"
		FROM
			qc_inspect qi
		JOIN pd_produce_batch pb ON pb.recordId = qi.produceBatchId AND pb.activeFlag = #{company.DEL_FLAG_NORMAL} 
		JOIN pd_feeding f ON f.recordId = pb.feedingNo and f.activeFlag = #{company.DEL_FLAG_NORMAL} and f.`status` != #{feeStatus}
		JOIN sl_notification n ON n.recordId = f.notificationId AND n.STATUS != #{status} AND n.activeFlag = #{company.DEL_FLAG_NORMAL} 
		WHERE
			qi.companyId = #{company.recordId} AND  qi.checkDate between #{startTime} and #{endTime} 
		UNION 
		SELECT
			n.recordId AS "recordId",
			n. NO AS "no",
			n.craftNo AS "craftNo",
			n.failedPcsQty AS "failedPcsQty",
			n.feedPcsQty AS "feedPcsQty",
			n.deliveryDate AS "deliveryDate"
		FROM
			qc_inspect qi
		JOIN pd_produce_batch pb ON pb.recordId = qi.produceBatchId AND pb.activeFlag = #{company.DEL_FLAG_NORMAL} 
		JOIN pd_replenish r ON r.produceBatchNo = pb.recordId and r.activeFlag = #{company.DEL_FLAG_NORMAL} and r.`status` != #{repStatus}
		JOIN sl_notification n ON n.recordId = r.notificationId AND n.STATUS != #{status} AND n.activeFlag = #{company.DEL_FLAG_NORMAL}
		WHERE
			qi.companyId = #{company.recordId} AND  qi.checkDate between #{startTime} and #{endTime} 
	</select>
	
	<select id="findInspectSumList" resultType="Inspect">
	    SELECT 
	    	qi.produceBatchDetailId as 'produceBatchDetail.recordId',SUM(qd.discardPcsQty) AS 'failedPcsQty'
		FROM qc_inspect qi
		LEFT JOIN qc_discard qd ON qd.inspectId = qi.recordId
		LEFT JOIN pd_produce_batch_detail d ON qi.produceBatchDetailId = d.recordId
		LEFT JOIN pd_produce_batch b ON d.produceBatchId = b.recordId
		LEFT JOIN sl_notification s ON s.recordId = b.notificationId
		<where>
			s.companyId = #{company.recordId} 
			AND s.activeFlag = #{DEL_FLAG_NORMAL}
			<!-- 只查询出已投料状态的 -->
			AND s.status = #{status}
		</where>
		GROUP BY qi.produceBatchDetailId
	</select>
	
    <resultMap id="inspect_DetailMap" type="Inspect">
		<id property="recordId" column="recordId" />		
		<result property="manufType" column="manufType" />
		<result property="pcsEsperPnl" column="pcsEsperPnl" />
		<result property="producePcsQty" column="producePcsQty" />
		<result property="passPcsQty" column="passPcsQty" />
		<result property="replaceQty" column="replaceQty" />
		<result property="failedPcsQty" column="failedPcsQty" />
		<result property="checkDate" column="checkDate" />
		<result property="remark" column="remark" />
		<result property="status" column="status" />
		
		<association property="operator" javaType="User">
			<id property="recordId" column="operator.recordId"/>				
		</association>
		
		<association property="checker" javaType="User">
			<id property="recordId" column="checker.recordId"/>				
		</association>
		
		<association property="produceBatch" javaType="ProduceBatch">
			<id property="recordId" column="produceBatch.recordId"/>
			<result property="no" column="produceBatch.no"/>
			
			<association property="notification" javaType="Notification">
				<id property="recordId" column="produceBatch.notification.recordId"/>
				<result property="no" column="produceBatch.notification.no"/>
				<result property="craftNo" column="produceBatch.notification.craftNo"/>
			</association>
			
			<association property="feed" javaType="Feeding">
				<id property="recordId" column="feeding.recordId"/>
				<result property="no" column="feeding.no"/>
				<result property="pcsQuantity" column="feeding.pcsQuantity"/>
			</association>
			
			<association property="replenish" javaType="Replenish">
				<id property="recordId" column="replenish.recordId"/>
				<result property="no" column="replenish.no"/>
				<result property="discardQty" column="replenish.discardQty"/>
			</association>
		</association>
		
		<association property="produceBatchDetail" javaType="ProduceBatchDetail">
			<id property="recordId" column="produceBatchDetail.recordId"/>
			<id property="discardQty" column="produceBatchDetail.discardQty"/>
		</association>
		
		<association property="process" javaType="EgProcess">
			<id property="recordId" column="inspect.process.recordId"/>		
			<result property="name" column="process.name"/>
			<result property="category" column="process.category"/>		
		</association>
		
		<collection property="discardList" ofType="Discard">
			<id property="recordId" column="discardList.recordId" />
			<result property="inspectId" column="discardList.inspectId" />
			<result property="craftNo" column="discardList.craftNo" />
			<result property="discardPcsQty" column="discardList.discardPcsQty" />
			<result property="discardCause" column="discardList.discardCause" />
			<result property="discardCauseOne" column="discardList.discardCauseOne" />
			
			<result property="discardType" column="discardList.discardType" />
			<result property="remark" column="discardList.remark" />
			
			<association property="boardPartCraft" javaType="BoardPartCraft">
				<id property="recordId" column="discardList.boardPartCraft.recordId"/>
			</association>
			
			<association property="dutyProcess" javaType="EgProcess">
				<id property="recordId" column="discardList.dutyProcess.recordId"/>
				<result property="category" column="discardList.dutyProcess.category" />				
			</association>
		</collection>
		
		<collection property="reworkList" ofType="Rework">
			<id property="recordId" column="reworkList.recordId" />
			<result property="inspectId" column="reworkList.inspectId" />
			<result property="craftNo" column="reworkList.craftNo" />
			<result property="reworkQty" column="reworkList.reworkQty" />
			<result property="reworkCause" column="reworkList.reworkCause" />
			<result property="reworkResult" column="reworkList.reworkResult" />
			
			<association property="boardPartCraft" javaType="BoardPartCraft">
				<id property="recordId" column="reworkList.boardPartCraft.recordId"/>
			</association>
			
			<association property="dutyProcess" javaType="EgProcess">
				<id property="recordId" column="reworkList.dutyProcess.recordId"/>
				<result property="category" column="reworkList.dutyProcess.category" />				
			</association>
			
			<association property="reworker" javaType="User">
				<id property="recordId" column="reworkList.reworker.recordId"/>	
				<result property="userName" column="reworkList.reworker.userName" />			
			</association>
		
			<association property="checker" javaType="User">
				<id property="recordId" column="reworkList.checker.recordId"/>
				<result property="userName" column="reworkList.checker.userName" />				
			</association>
		</collection>
		
		<collection property="badList" ofType="Bad">
			<id property="recordId" column="badList.recordId" />
			<result property="inspectId" column="badList.inspectId" />
			<result property="badQty" column="badList.badQty" />
			<result property="badCause" column="badList.badCause" />
			<result property="remark" column="badList.remark" />
			
			<result property="craftNo" column="badList.craftNo" />
			
			<association property="dutyProcess" javaType="EgProcess">
				<id property="recordId" column="badList.dutyProcess.recordId"/>				
				<id property="category" column="badList.dutyProcess.category"/>				
			</association>
			
			<association property="boardPartCraft" javaType="BoardPartCraft">
				<id property="recordId" column="badList.boardPartCraft.recordId"/>
			</association>
		</collection>
	</resultMap>
		
    <!-- App显示通知单的品质检测明细信息 -->
	<select id="findNotificationDetailList" resultMap="inspect_DetailMap">
	    select 
			qi.recordid AS "recordId",
			qi.manuftype AS "manufType",
			qi.pcsesperpnl AS "pcsEsperPnl",
			qi.producepcsqty AS "producePcsQty",
			qi.passpcsqty AS "passPcsQty",
	        qi.replaceQty,
			qi.failedpcsqty AS "failedPcsQty",
			qi.operator AS "operator.recordId",
			qi.checker AS "checker.recordId",
			qi.checkdate AS "checkDate",
			qi.remark AS "remark",
			qi.status AS "status",
			
			pb.recordId as "produceBatch.recordId",
			pb.no as "produceBatch.no",
			pb.processCardAId as "produceBatch.processCardAId",
			
			n.recordId as "produceBatch.notification.recordId",
			n.no as "produceBatch.notification.no",
			n.craftNo as "produceBatch.notification.craftNo",
			
			f.recordId as "produceBatch.feed.recordId",
			f.no as "produceBatch.feed.no",
			f.pcsQuantity as "produceBatch.feed.pcsQuantity",
			
			NULL as "produceBatch.replenish.recordId",
			NULL as "produceBatch.replenish.no",
			NULL as "produceBatch.replenish.discardQty",
			
			pbd.recordId as "produceBatchDetail.recordId",
			pbd.discardQty as "produceBatchDetail.discardQty",
			
			ep.recordId as "process.recordId",
			ep.name as "process.name",
			ep.category as "process.category",
			
			qd.recordid AS "discardList.recordId",
			qd.inspectid AS "discardList.inspectId",
			qd.craftNo AS "discardList.craftNo",
			qd.discardpcsqty AS "discardList.discardPcsQty",
			qd.discardcause AS "discardList.discardCause",
			qd.discardCauseOne AS "discardList.discardCauseOne",
			
			qd.discardType AS "discardList.discardType",
			qd.remark AS "discardList.remark", 
			qd.boardpartcraftid AS "discardList.boardPartCraft.recordId", 
			qd.dutyProcess AS "discardList.dutyProcess.recordId", 
			qdp.category AS "discardList.dutyProcess.category", 
			
			qr.recordid AS "reworkList.recordId",
			qr.inspectid AS "reworkList.inspectId",
			qr.craftNo AS "reworkList.craftNo",
			qr.reworkqty AS "reworkList.reworkQty",
			qr.reworkcause AS "reworkList.reworkCause",
			qr.reworkresult AS "reworkList.reworkResult",
			qr.boardpartcraftid AS "reworkList.boardPartCraft.recordId", 
			qr.dutyProcess AS "reworkList.dutyProcess.recordId", 
			qrp.category AS "reworkList.dutyProcess.category", 
			qr.reworker AS "reworkList.reworker.recordId",
			wo.userName AS "reworkList.reworker.userName",
			qr.reworkchecker AS "reworkList.checker.recordId",	
			ch.userName AS "reworkList.checker.userName",
			
			qb.recordid AS "badList.recordId",
			qb.inspectid AS "badList.inspectId",
			qb.badQty AS "badList.badQty",
			qb.badCause AS "badList.badCause",
			qb.craftNo AS "badList.craftNo",
			qb.boardpartcraftid AS "badList.boardPartCraft.recordId",
			qb.dutyProcess AS "badList.dutyProcess.recordId",
			qbp.category AS "badList.dutyProcess.category",
			qb.remark AS "badList.remark"
			
		from qc_inspect qi
		join pd_produce_batch pb on pb.recordId = qi.produceBatchId and pb.activeFlag = #{company.DEL_FLAG_NORMAL}
		join pd_produce_batch_detail pbd on pbd.recordId = qi.produceBatchDetailId and pbd.activeFlag = #{company.DEL_FLAG_NORMAL}
		join eg_process ep on ep.recordId = qi.processId 
		join pd_feeding f on f.recordId = pb.feedingNo and f.activeFlag = #{company.DEL_FLAG_NORMAL} and f.status != #{feeStatus}
		join sl_notification n on n.recordId = f.notificationId and n.`status` != #{status} and n.activeFlag = #{company.DEL_FLAG_NORMAL}
		left join qc_discard qd on qd.inspectId = qi.recordId 
		left join qc_rework qr on qr.inspectId = qi.recordId 
		left join qc_bad qb on qb.inspectId = qi.recordId
		left join sm_user wo on wo.recordId = qr.reworker
		left join sm_user ch on ch.recordId = qr.reworkchecker
		left join eg_process qdp on qdp.recordId = qd.dutyProcess
		left join eg_process qrp on qrp.recordId = qr.dutyProcess
		left join eg_process qbp on qbp.recordId = qb.dutyProcess
		where qi.companyId = #{company.recordId} and n.recordId = #{notificationId}
		union 
		select 
			qi.recordid AS "recordId",
			qi.manuftype AS "manufType",
			qi.pcsesperpnl AS "pcsEsperPnl",
			qi.producepcsqty AS "producePcsQty",
			qi.passpcsqty AS "passPcsQty",
		    qi.replaceQty,
			qi.failedpcsqty AS "failedPcsQty",
			qi.operator AS "operator.recordId",
			qi.checker AS "checker.recordId",
			qi.checkdate AS "checkDate",
			qi.remark AS "remark",
			qi.status AS "status",

			pb.recordId as "produceBatch.recordId",
			pb.no as "produceBatch.no",
			pb.processCardAId as "produceBatch.processCardAId",
		
			n.recordId as "produceBatch.notification.recordId",
			n.no as "produceBatch.notification.no",
			n.craftNo as "produceBatch.notification.craftNo",
			
			NULL as "produceBatch.feed.recordId",
			NULL as "produceBatch.feed.no",
			NULL as "produceBatch.feed.pcsQuantity",
			
			r.recordId as "produceBatch.replenish.recordId",
			r.no as "produceBatch.replenish.no",
			r.discardQty as "produceBatch.replenish.discardQty",
			pbd.recordId as "produceBatchDetail.recordId",
			pbd.discardQty as "produceBatchDetail.discardQty",

			ep.recordId as "process.recordId",
			ep.name as "process.name",
			ep.category as "process.category",
			
			qd.recordid AS "discardList.recordId",
			qd.inspectid AS "discardList.inspectId",
			qd.craftNo AS "discardList.craftNo",
			qd.discardpcsqty AS "discardList.discardPcsQty",
			qd.discardcause AS "discardList.discardCause",
			qd.discardCauseOne AS "discardList.discardCauseOne",
			
			qd.discardType AS "discardList.discardType",
			qd.remark AS "discardList.remark", 
			qd.boardpartcraftid AS "discardList.boardPartCraft.recordId", 
			qd.dutyProcess AS "discardList.dutyProcess.recordId", 
			qdp.category AS "discardList.dutyProcess.category", 
			
			qr.recordid AS "reworkList.recordId",
			qr.inspectid AS "reworkList.inspectId",
			qr.craftNo AS "reworkList.craftNo",
			qr.reworkqty AS "reworkList.reworkQty",
			qr.reworkcause AS "reworkList.reworkCause",
			qr.reworkresult AS "reworkList.reworkResult",
			qr.boardpartcraftid AS "reworkList.boardPartCraft.recordId", 
			qr.dutyProcess AS "reworkList.dutyProcess.recordId", 
			qrp.category AS "reworkList.dutyProcess.category", 
			qr.reworker AS "reworkList.reworker.recordId",
			wo.userName AS "reworkList.reworker.userName",
			qr.reworkchecker AS "reworkList.checker.recordId",	
			ch.userName AS "reworkList.checker.userName",
			
			qb.recordid AS "badList.recordId",
			qb.inspectid AS "badList.inspectId",
			qb.badQty AS "badList.badQty",
			qb.badCause AS "badList.badCause",
			qb.craftNo AS "badList.craftNo",
			qb.boardpartcraftid AS "badList.boardPartCraft.recordId",
			qb.dutyProcess AS "badList.dutyProcess.recordId",
			qbp.category AS "badList.dutyProcess.category",
			qb.remark AS "badList.remark"
			
		from qc_inspect qi
		join pd_produce_batch pb on pb.recordId = qi.produceBatchId and pb.activeFlag = #{company.DEL_FLAG_NORMAL}
		join pd_produce_batch_detail pbd on pbd.recordId = qi.produceBatchDetailId and pbd.activeFlag = #{company.DEL_FLAG_NORMAL}
		join eg_process ep on ep.recordId = qi.processId 
		left join pd_replenish r on r.produceBatchNo = pb.recordId and r.activeFlag = #{company.DEL_FLAG_NORMAL} and r.status != #{repStatus}
		join sl_notification n on n.recordId = r.notificationId and n.`status` != #{status} and n.activeFlag = #{company.DEL_FLAG_NORMAL}
		left join qc_discard qd on qd.inspectId = qi.recordId 
		left join qc_rework qr on qr.inspectId = qi.recordId 
		left join qc_bad qb on qb.inspectId = qi.recordId
		left join sm_user wo on wo.recordId = qr.reworker
		left join sm_user ch on ch.recordId = qr.reworkchecker
		left join eg_process qdp on qdp.recordId = qd.dutyProcess
		left join eg_process qrp on qrp.recordId = qr.dutyProcess
		left join eg_process qbp on qbp.recordId = qb.dutyProcess
		where qi.companyId = #{company.recordId} and n.recordId = #{notificationId}
	</select>
	
	<!-- 模板设置跳过工序的时候如果工序有返工不可设置 -->
	<select id="getReworkQtyByTemplateAndProcess" resultType="java.lang.Integer">
		select SUM(r.reworkQty) from sl_notification a
		join eg_carda ca on ca.recordId = a.processCardAId and ca.templateId = #{templateId} and ca.activeFlag = #{company.DEL_FLAG_NORMAL}
		join pd_produce_batch pb on pb.notificationId = a.recordId and pb.activeFlag = #{company.DEL_FLAG_NORMAL} and pb.`status` in ${batchStatus}
		join pd_produce_batch_detail pbd on pbd.produceBatchId = pb.recordId and pbd.activeFlag = #{company.DEL_FLAG_NORMAL} and pbd.`status` in ${batchStatus}
		join qc_inspect i on i.produceBatchId = pb.recordId and i.produceBatchDetailId = pbd.recordId and i.processId = #{processId}
		join qc_rework r on r.inspectId = i.recordId and ISNULL(r.reworkResult) and r.activeFlag = #{company.DEL_FLAG_NORMAL}
		where a.companyId = #{company.recordId} and a.activeFlag = #{company.DEL_FLAG_NORMAL} and a.status in ${notiStatus}
	</select>
	
	<select id="findInitInspectSum" resultType="java.lang.Integer">
		select 
			count(1)
		from qc_inspect a
		where a.companyId = #{company.recordId} 
		and a.activeFlag = #{DEL_FLAG_NORMAL} 
		and a.`status` = #{status}
		and a.produceBatchDetailId = #{produceBatchDetail.recordId}
	</select>
	
	<!-- ycy 2016-10-29 删除品质统计 -->
	<delete id="deleteQcDay">
		DELETE from re_qc_day where companyId=#{recordId}
	</delete>
	<!-- ycy 2016-10-29 删除品质统计 -->
	<delete id="deleteQcCraftDay">
		DELETE from re_qc_day_craft where companyId=#{recordId}
	</delete>
	<!-- ycy 2016-10-29 删除品质统计 -->
	<delete id="deleteQcMonth">
		DELETE from re_qc_month where companyId=#{recordId}
	</delete>
	<!-- ycy 2016-10-29 删除品质统计 -->
	<delete id="deleteQcWeek">
		DELETE from re_qc_week where companyId=#{recordId}
	</delete>
	
	<select id="getPnlDivisor" resultType="java.lang.Integer">
		SELECT ec.pnlDivisor 
		FROM pd_produce_batch_detail a
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId
		LEFT JOIN sl_notification sn ON sn.recordId = ppb.notificationId
		LEFT JOIN eg_carda ec ON ec.recordId = sn.processCardAId
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="getInspectByProcessAndBatchDetaiId" resultType="Inspect">
		SELECT 
			a.recordid AS "recordId",
			a.companyid AS "company.recordId",
			a.producebatchid AS "produceBatch.recordId",
			a.produceBatchDetailId as "produceBatchDetail.recordId",
			a.processid AS "process.recordId",
			a.manuftype AS "manufType",
			a.pcsesperpnl AS "pcsEsperPnl",
			a.producepcsqty AS "producePcsQty",
			a.passpcsqty AS "passPcsQty",
		    a.replaceQty,
			a.failedpcsqty AS "failedPcsQty",
			a.operator AS "operator.recordId",
			a.checker AS "checker.recordId",
			a.checkdate AS "checkDate",
			a.activeflag AS "activeFlag",
			a.createdby AS "createdBy.recordId",
			a.createddate AS "createdDate",
			a.lastupdby AS "lastUpdBy.recordId",
			a.lastupddate AS "lastUpdDate",
			a.remark AS "remark",
			a.status AS "status", 
		FROM qc_inspect a
		WHERE a.companyId = #{company.recordId}
		AND a.produceBatchDetailId = #{produceBatchDetail.recordId}
		AND a.processId = #{process.recordId} 
		AND a.activeFlag = #{DEL_FLAG_NORMAL} 
		AND a.status = #{status}
	</select>

	<!--2018-10-10 lq 查询最多不良原因-->
	<select id="selectCauseBad" resultType="java.lang.String">
			SELECT q.badCause
 			FROM qc_bad q
  			LEFT JOIN qc_inspect qi on qi.recordId=q.inspectId
  			WHERE qi.createdBy=#{createdBy.recordId}
  			AND qi.companyId=#{company.recordId}
  			AND DATE(q.createdDate)=CURDATE()
			group by q.badCause
			order by count(1) desc LIMIT 0,1
	</select>


	<!--2018-10-10 lq 查询最多报废原因 -->
	<select id="selectCauseScrap" resultType="java.lang.String">
		SELECT q.discardCause
 			FROM qc_discard q
  			LEFT JOIN qc_inspect qi on qi.recordId=q.inspectId
  			WHERE qi.createdBy=#{createdBy.recordId}
  			AND qi.companyId=#{company.recordId}
  			AND DATE(q.createdDate)=CURDATE()
			group by q.discardCause
			order by count(1) desc LIMIT 0,1
	</select>

	<!--2018-10-10 lq 查询最多返工原因 -->
	<select id="selectCauseReWork" resultType="java.lang.String">
		SELECT q.reworkCause
 			FROM qc_rework q
  			LEFT JOIN qc_inspect qi on qi.recordId=q.inspectId
  			WHERE qi.createdBy=#{createdBy.recordId}
  			AND qi.companyId=#{company.recordId}
  			AND DATE(q.createdDate)=CURDATE()
			group by q.reworkCause
			order by count(1) desc LIMIT 0,1
	</select>

	<!--lq 2018-10-11 当天报废最多工序-->
	<select id="selectCauseScrapProcess" resultType="java.lang.String">

		SELECT `name` FROM eg_process WHERE recordId
			IN (SELECT q.dutyProcess
 			FROM qc_discard q
  			LEFT JOIN qc_inspect qi on qi.recordId=q.inspectId
  			WHERE qi.createdBy=#{createdBy.recordId}
  			AND qi.companyId=#{company.recordId}
  			AND DATE(q.createdDate)=CURDATE()
			group by q.dutyProcess
			order by count(1) desc ) LIMIT 0,1
	</select>
	<!--lq 2018-10-11 当天返工最多工序-->
	<select id="selectCauseReWorkProcess" resultType="java.lang.String">

			SELECT `name` FROM eg_process WHERE recordId
			IN (SELECT q.dutyProcess
 			FROM qc_rework q
  			LEFT JOIN qc_inspect qi on qi.recordId=q.inspectId
  			WHERE qi.createdBy=#{createdBy.recordId}
  			AND qi.companyId=#{company.recordId}
  			AND DATE(q.createdDate)=CURDATE()
			group by q.dutyProcess
			order by count(1) desc ) LIMIT 0,1

	</select>
	<!--lq 2018-10-11 当天不良最多工序-->
	<select id="selectCauseBadProcess" resultType="java.lang.String">
			SELECT `name` FROM eg_process WHERE recordId
			IN (SELECT q.dutyProcess
 			FROM qc_bad q
  			LEFT JOIN qc_inspect qi on qi.recordId=q.inspectId
  			WHERE qi.createdBy=#{createdBy.recordId}
  			AND qi.companyId=#{company.recordId}
  			AND DATE(q.createdDate)=CURDATE()
			group by q.dutyProcess
			order by count(1) desc ) LIMIT 0,1
	</select>

	<!-- lq 2018-11-28  根据品检员查询品质检测记录-->
    <select id="getInspectByChecker" resultType="com.kyb.pcberp.modules.quality.entity.Inspect">
		SELECT a.producePcsQty,a.passPcsQty,a.replaceQty,a.failedPcsQty,
		a.operator,a.checker,a.checkDate,
		IFNULL(qb.badQty,0)as "bad.badQty",
		left (qb.badCause,100) as "bad.badCause",
		IFNULL(qr.reworkQty,0) as "rework.reworkQty",
		left (qr.reworkCause,100) as "rework.reworkCause",
		IFNULL(qd.discardPcsQty,0) as"discard.discardPcsQty",
		left (qd.discardCause,100)  as "discard.discardCause",
		a.`status`,
		ep.`name` as "process.`name`",
		su.userName as "checker.userName",
		sr.userName AS "operator.userName",
		sn.`no` as "notificationNo",
        FORMAT((scd.orderDeailArea/scd.quantity) * a.producePcsQty ,4) as "quantitySumArea",
		date(a.createdDate) as "createdDate",
		FORMAT((scd.orderDeailArea/scd.quantity) * qd.discardPcsQty ,4) as "discardArea",
		FORMAT((scd.orderDeailArea/scd.quantity) * qr.reworkQty ,4) as "reworkArea",
		FORMAT((scd.orderDeailArea/scd.quantity) * qb.badQty ,4) as "badArea",
		scd.recordId as "contractDetail.recordId",
		scc.customerModel as "contractDetail.craft.customerModel",
        sc.no as "contractDetail.cntractNo"
		<if test="allType!=null and   allType!=''">
			<if test="allType==2 or  allType=='2'">
				<if test="valuedId == 186 and valuedId == '186'  ">
					,sum(FORMAT((scd.orderDeailArea/scd.quantity) * qd.discardPcsQty ,4)) as "tempData"
				</if>
				<if test="valuedId == 189 and valuedId == '189'  ">
					,sum(FORMAT((scd.orderDeailArea/scd.quantity) * qr.reworkQty ,4)) as "tempData"
				</if>
				<if test="valuedId == 192 and valuedId == '192'  ">
					,sum(FORMAT((scd.orderDeailArea/scd.quantity) * qb.badQty ,4)) as "tempData"
				</if>
				<if test="valuedId == 215 and valuedId == '215'  ">
					,sum(FORMAT((scd.orderDeailArea/scd.quantity) * a.producePcsQty ,4)) as "tempData"
				</if>
			</if>
			<if test="allType==1 or  allType=='1'">
				,sum(FORMAT((scd.orderDeailArea/scd.quantity) * qd.discardPcsQty ,4)) as "tempData"
				,sum(FORMAT((scd.orderDeailArea/scd.quantity) * a.producePcsQty ,4)) as "tempData2"

			</if>

		</if>

		FROM qc_inspect a
		LEFT JOIN qc_discard qd on qd.inspectId= a.recordId
		LEFT JOIN sm_user su on su.recordId=a.checker
		LEFT JOIN sm_user sr on sr.recordId=a.operator
		LEFT JOIN qc_bad qb on qb.inspectId =a.recordId
		LEFT JOIN qc_rework qr on qr.inspectId= a.recordId
		LEFT JOIN eg_process ep on ep.recordId=a.processId
		LEFT JOIN pd_produce_batch ppd on ppd.recordId=a.produceBatchId
		LEFT JOIN sl_notification sn on sn.recordId=ppd.notificationId
		LEFT JOIN sl_contract_detail scd on scd.recordId=sn.contractDetailId
		LEFT JOIN sl_contract_craft scc on scc.recordId=scd.craftId
        LEFT JOIN sl_contract sc on sc.recordId = scd.contractId
		<where>
			a.activeFlag=1
            and a.companyId  in (${company.recordId})
			and a.status=900605
			<!-- 1:日,2:周,3:月 -->
			<if test="dateType != null and dateType!= ''">
				<if test="dateType == 1">
					<if test="queryDate != null and queryDate != ''">
						AND DATE(a.lastUpdDate)= DATE(#{queryDate})
					</if>
					<if test="queryDate == null or queryDate ==''">
						AND DATE(a.lastUpdDate)=CURDATE()
					</if>
				</if>
				<if test="dateType == 2">
					<if test="queryDate != null and queryDate != ''">
						AND YEARWEEK(date_format(a.lastUpdDate,'%Y%m%d')) =
						YEARWEEK(#{queryDate})
					</if>
					<if test="queryDate == null or queryDate ==''">
						AND YEARWEEK(date_format(a.lastUpdDate,'%Y%m%d')) = YEARWEEK(now())
					</if>
				</if>
				<if test="dateType == 3">
					<if test="queryDate != null and queryDate != ''">
						AND DATE_FORMAT(a.lastUpdDate,'%Y%m') =
						DATE_FORMAT(#{queryDate},'%Y%m')
					</if>
					<if test="queryDate == null or queryDate ==''">
						AND DATE_FORMAT(a.lastUpdDate,'%Y%m') = DATE_FORMAT(CURDATE(),'%Y%m')
					</if>
				</if>

			</if>
            and  a.checker=#{recordId}
            ORDER BY quantitySumArea desc
		</where>

	</select>
	<!--lq 2018-12-03 查检测员-->
	<select id="getChecker" resultType="java.lang.String">
		select  checker as "checkerId"   from qc_inspect where  recordId=#{recordId}
	</select>
	
	<!-- zjn 2019-04-19 根据批次明细获取报废品质信息 -->
	<select id="getDiscardDataByBatchDetail" resultType="com.kyb.pcberp.modules.quality.vo.InspectVo">
		SELECT
			a.recordId AS "discard.recordId",
			a.companyId AS "discard.company.recordId",
			a.inspectId AS "discard.inspect.recordId",
			a.boardPartCraftId AS "discard.boardPartCraft.recordId",
			a.craftNo AS "discard.craftNo",
			a.discardPcsQty AS "discard.discardPcsQty",
			a.discardPcsQtyA AS "discard.discardPcsQtyA",
			a.discardPcsQtyB AS "discard.discardPcsQtyB",
			a.discardCause AS "discard.discardCause",
			a.discardCauseOne AS "discard.discardCauseOne",
			a.discardType AS "discard.discardType",
			ep1.recordId AS "discard.dutyProcess.recordId",
			
			CASE epci1.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv1.processCraftValue," ",""),REPLACE(ep1.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(ep1.name," ",""),'(',IFNULL(REPLACE(ecpcv1.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep1.name," ","") END AS "discard.dutyProcess.name",
			
			CASE epci1.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv1.processCraftValue," ",""),REPLACE(ep1.category," ",""))  
			WHEN 2 THEN CONCAT(REPLACE(ep1.category," ",""),'(',IFNULL(REPLACE(ecpcv1.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep1.category," ","") END AS "discard.dutyProcess.category",
			
			a.activeFlag AS "discard.activeFlag",
			a.createdBy AS "discard.createdBy.recordId",
			a.createdDate AS "discard.createdDate",
			a.lastUpdBy AS "discard.lastUpdBy.recordId",
			a.lastUpdDate AS "discard.lastUpdDate",
			a.remark AS "discard.remark",
			b.companyId AS "discard.inspect.company.recordId",
			b.produceBatchId AS "discard.inspect.produceBatch.recordId",
			b.produceBatchDetailId AS "discard.inspect.produceBatchDetail.recordId",
			b.processId AS "discard.inspect.process.recordId",
			b.manufType AS "discard.inspect.manufType",
			b.pcsesPerPnl AS "discard.inspect.pcsesPerPnl",
			b.producePcsQty AS "discard.inspect.producePcsQty",
			b.passPcsQty AS "discard.inspect.passPcsQty",
		    b.replaceQty AS "discard.inspect.replaceQty",
			b.failedPcsQty AS "discard.inspect.failedPcsQty",
			b.operator AS "discard.inspect.operator.recordId",
			b.checker AS "discard.inspect.checker.recordId",
			b.checkDate AS "discard.inspect.checkDate",
			b.activeFlag AS "discard.inspect.activeFlag",
			b.createdBy AS "discard.inspect.createdBy.recordId",
			b.createdDate AS "discard.inspect.createdDate",
			b.lastUpdBy AS "discard.inspect.lastUpdBy.recordId",
			b.lastUpdDate AS "discard.inspect.lastUpdDate",
			b.remark AS "discard.inspect.remark",
			b.`status` AS "discard.inspect.status",
			
			CASE epci2.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv2.processCraftValue," ",""),REPLACE(ep2.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(ep2.name," ",""),'(',IFNULL(REPLACE(ecpcv2.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep2.name," ","") END AS "discard.inspect.process.name",
			
			CASE epci2.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv2.processCraftValue," ",""),REPLACE(ep2.category," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(ep2.category," ",""),'(',IFNULL(REPLACE(ecpcv2.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep2.category," ","") END AS "discard.inspect.process.category"
			
		FROM qc_discard a
		LEFT JOIN qc_inspect b ON b.recordId = a.inspectId AND b.activeFlag = 1
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = b.produceBatchId AND ppb.activeFlag = 1
		LEFT JOIN eg_process ep1 ON ep1.recordId = a.dutyProcess AND ep1.activeFlag = 1
		LEFT JOIN eg_process ep2 ON ep2.recordId = b.processId AND ep2.activeFlag = 1
		
		LEFT JOIN eg_carda_process_value ecpv1 ON ecpv1.processCardAId = ppb.processCardAId AND ecpv1.processId = ep1.recordId AND ecpv1.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci1 ON epci1.processId = ep1.recordId AND epci1.companyId = a.companyId AND epci1.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv1 ON ecpcv1.processValueId = ecpv1.recordId AND ecpcv1.processCraftId = epci1.craftId AND ecpcv1.activeFlag = 1
		
		LEFT JOIN eg_carda_process_value ecpv2 ON ecpv2.processCardAId = ppb.processCardAId AND ecpv2.processId = ep2.recordId AND ecpv2.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci2 ON epci2.processId = ep2.recordId AND epci2.companyId = a.companyId AND epci2.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv2 ON ecpcv2.processValueId = ecpv2.recordId AND ecpcv2.processCraftId = epci2.craftId AND ecpcv2.activeFlag = 1
		
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = 1
			<if test="produceBatchDetail != null and produceBatchDetail.recordId != null and produceBatchDetail.recordId != ''">
				AND b.produceBatchDetailId = #{produceBatchDetail.recordId}
			</if>
			<if test="status != null and status != ''">
				 AND b.`status` =#{status}
			</if>
		</where>
	</select>
	
	<!-- zjn 2019-04-19 根据批次明细获取返工品质信息 -->
	<select id="getReworkDataByBatchDetail" resultType="com.kyb.pcberp.modules.quality.vo.InspectVo">
		SELECT
			a.recordId AS "rework.recordId",
			a.companyId AS "rework.company.recordId",
			a.inspectId AS "rework.inspect.recordId",
			a.boardPartCraftId AS "rework.boardPartCraft.recordId",
			a.craftNo AS "rework.craftNo",
			a.reworkQty AS "rework.reworkQty",
			a.reworkCause AS "rework.reworkCause",
			a.reworkResult AS "rework.reworkResult",
			a.reworker AS "rework.reworker.recordId",
			a.reworkChecker AS "rework.checker.recordId",
			a.reworkDate AS "rework.reworkDate",
			ep1.recordId AS "rework.dutyProcess.recordId",
			
			CASE epci1.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv1.processCraftValue," ",""),REPLACE(ep1.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(ep1.name," ",""),'(',IFNULL(REPLACE(ecpcv1.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep1.name," ","") END AS "rework.dutyProcess.name",
			
			CASE epci1.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv1.processCraftValue," ",""),REPLACE(ep1.category," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(ep1.category," ",""),'(',IFNULL(REPLACE(ecpcv1.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep1.category," ","") END AS "rework.dutyProcess.category",
			
		  	a.activeFlag AS "rework.activeFlag",
			a.createdBy AS "rework.createdBy.recordId",
			a.createdDate AS "rework.createdDate",
			a.lastUpdBy AS "rework.lastUpdBy.recordId",
			a.lastUpdDate AS "rework.lastUpdDate",
			a.remark AS "rework.remark",
		
			b.companyId AS "rework.inspect.company.recordId",
			b.produceBatchId AS "rework.inspect.produceBatch.recordId",
			b.produceBatchDetailId AS "rework.inspect.produceBatchDetail.recordId",
			b.processId AS "rework.inspect.process.recordId",
			b.manufType AS "rework.inspect.manufType",
			b.pcsesPerPnl AS "rework.inspect.pcsesPerPnl",
			b.producePcsQty AS "rework.inspect.producePcsQty",
			b.passPcsQty AS "rework.inspect.passPcsQty",
		    b.replaceQty AS "rework.inspect.replaceQty",
			b.failedPcsQty AS "rework.inspect.failedPcsQty",
			b.operator AS "rework.inspect.operator.recordId",
			b.checker AS "rework.inspect.checker.recordId",
			b.checkDate AS "rework.inspect.checkDate",
			b.activeFlag AS "rework.inspect.activeFlag",
			b.createdBy AS "rework.inspect.createdBy.recordId",
			b.createdDate AS "rework.inspect.createdDate",
			b.lastUpdBy AS "rework.inspect.lastUpdBy.recordId",
			b.lastUpdDate AS "rework.inspect.lastUpdDate",
			b.remark AS "rework.inspect.remark",
			b.`status` AS "rework.inspect.status",
			
			CASE epci2.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv2.processCraftValue," ",""),REPLACE(ep2.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(ep2.name," ",""),'(',IFNULL(REPLACE(ecpcv2.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep2.name," ","") END AS "rework.inspect.process.name",
			
			CASE epci2.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv2.processCraftValue," ",""),REPLACE(ep2.category," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(ep2.category," ",""),'(',IFNULL(REPLACE(ecpcv2.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep2.category," ","") END AS "rework.inspect.process.category"
			
		FROM qc_rework a
		LEFT JOIN qc_inspect b ON b.recordId = a.inspectId AND b.activeFlag = 1
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = b.produceBatchId AND ppb.activeFlag = 1
		LEFT JOIN eg_process ep1 ON ep1.recordId = a.dutyProcess AND ep1.activeFlag = 1
		LEFT JOIN eg_process ep2 ON ep2.recordId = b.processId AND ep2.activeFlag = 1
		
		LEFT JOIN eg_carda_process_value ecpv1 ON ecpv1.processCardAId = ppb.processCardAId AND ecpv1.processId = ep1.recordId AND ecpv1.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci1 ON epci1.processId = ep1.recordId AND epci1.companyId = a.companyId AND epci1.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv1 ON ecpcv1.processValueId = ecpv1.recordId AND ecpcv1.processCraftId = epci1.craftId AND ecpcv1.activeFlag = 1
		
		LEFT JOIN eg_carda_process_value ecpv2 ON ecpv2.processCardAId = ppb.processCardAId AND ecpv2.processId = ep2.recordId AND ecpv2.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci2 ON epci2.processId = ep2.recordId AND epci2.companyId = a.companyId AND epci2.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv2 ON ecpcv2.processValueId = ecpv2.recordId AND ecpcv2.processCraftId = epci2.craftId AND ecpcv2.activeFlag = 1
			
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = 1
			<if test="produceBatchDetail != null and produceBatchDetail.recordId != null and produceBatchDetail.recordId != ''">
				AND b.produceBatchDetailId = #{produceBatchDetail.recordId}
			</if>
			<if test="status != null and status != ''">
				 AND b.`status` =#{status}
			</if>
		</where>
	</select>
	
	<!-- zjn 2019-04-19 根据批次明细获取不良品质信息 -->
	<select id="getBadDataByBatchDetail" resultType="com.kyb.pcberp.modules.quality.vo.InspectVo">
		SELECT
			a.recordId AS "bad.recordId",
			a.companyId AS "bad.company.recordId",
			a.inspectId AS "bad.inspect.recordId",
			a.boardPartCraftId AS "bad.boardPartCraft.recordId",
			a.craftNo AS "bad.craftNo",
			a.badQty AS "bad.badQty",
			a.badCause AS "bad.badCause",
			ep1.recordId AS "bad.dutyProcess.recordId",
			
			CASE epci1.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv1.processCraftValue," ",""),REPLACE(ep1.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(ep1.name," ",""),'(',IFNULL(REPLACE(ecpcv1.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep1.name," ","") END AS "bad.dutyProcess.name",
			
			CASE epci1.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv1.processCraftValue," ",""),REPLACE(ep1.category," ",""))  
			WHEN 2 THEN CONCAT(REPLACE(ep1.category," ",""),'(',IFNULL(REPLACE(ecpcv1.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep1.category," ","") END AS "bad.dutyProcess.category",
			
			a.activeFlag AS "bad.activeFlag",
			a.createdBy AS "bad.createdBy.recordId",
			a.createdDate AS "bad.createdDate",
			a.lastUpdBy AS "bad.lastUpdBy.recordId",
			a.lastUpdDate AS "bad.lastUpdDate",
			a.remark AS "bad.remark",
			
			b.companyId AS "bad.inspect.company.recordId",
			b.produceBatchId AS "bad.inspect.produceBatch.recordId",
			b.produceBatchDetailId AS "bad.inspect.produceBatchDetail.recordId",
			b.processId AS "bad.inspect.process.recordId",
			b.manufType AS "bad.inspect.manufType",
			b.pcsesPerPnl AS "bad.inspect.pcsesPerPnl",
			b.producePcsQty AS "bad.inspect.producePcsQty",
			b.passPcsQty AS "bad.inspect.passPcsQty",
			b.replaceQty AS "bad.inspect.replaceQty",
			b.failedPcsQty AS "bad.inspect.failedPcsQty",
			b.operator AS "bad.inspect.operator.recordId",
			b.checker AS "bad.inspect.checker.recordId",
			b.checkDate AS "bad.inspect.checkDate",
			b.activeFlag AS "bad.inspect.activeFlag",
			b.createdBy AS "bad.inspect.createdBy.recordId",
			b.createdDate AS "bad.inspect.createdDate",
			b.lastUpdBy AS "bad.inspect.lastUpdBy.recordId",
			b.lastUpdDate AS "bad.inspect.lastUpdDate",
			b.remark AS "bad.inspect.remark",
			b.`status` AS "bad.inspect.status",
			
			CASE epci2.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv2.processCraftValue," ",""),REPLACE(ep2.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(ep2.name," ",""),'(',IFNULL(REPLACE(ecpcv2.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep2.name," ","") END AS "bad.inspect.process.name",
			
			CASE epci2.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv2.processCraftValue," ",""),REPLACE(ep2.category," ",""))  
			WHEN 2 THEN CONCAT(REPLACE(ep2.category," ",""),'(',IFNULL(REPLACE(ecpcv2.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep2.category," ","") END AS "bad.inspect.process.category"
			
		FROM qc_bad a
		LEFT JOIN qc_inspect b ON b.recordId = a.inspectId AND b.activeFlag = 1
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = b.produceBatchId AND ppb.activeFlag = 1
		LEFT JOIN eg_process ep1 ON ep1.recordId = a.dutyProcess AND ep1.activeFlag = 1
		LEFT JOIN eg_process ep2 ON ep2.recordId = b.processId AND ep2.activeFlag = 1
		
		LEFT JOIN eg_carda_process_value ecpv1 ON ecpv1.processCardAId = ppb.processCardAId AND ecpv1.processId = ep1.recordId AND ecpv1.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci1 ON epci1.processId = ep1.recordId AND epci1.companyId = a.companyId AND epci1.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv1 ON ecpcv1.processValueId = ecpv1.recordId AND ecpcv1.processCraftId = epci1.craftId AND ecpcv1.activeFlag = 1
	
		LEFT JOIN eg_carda_process_value ecpv2 ON ecpv2.processCardAId = ppb.processCardAId AND ecpv2.processId = ep2.recordId AND ecpv2.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci2 ON epci2.processId = ep2.recordId AND epci2.companyId = a.companyId AND epci2.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv2 ON ecpcv2.processValueId = ecpv2.recordId AND ecpcv2.processCraftId = epci2.craftId AND ecpcv2.activeFlag = 1
	
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = 1
			<if test="produceBatchDetail != null and produceBatchDetail.recordId != null and produceBatchDetail.recordId != ''">
				AND b.produceBatchDetailId = #{produceBatchDetail.recordId}
			</if>
			<if test="status != null and status != ''">
				 AND b.`status` =#{status}
			</if>
		</where>
	</select>
	
	<!-- zjn 2019-04-19 批量添加品质检测记录 -->
	<insert id="batchInsert" parameterType="java.util.List">
		INSERT INTO qc_inspect(
			companyid,
			producebatchid,
			produceBatchDetailId,
			processid,
			manuftype,
			pcsesperpnl,
			producepcsqty,
			passpcsqty,
			failedpcsqty,
			operator,
			checker,
			checkdate,
			activeflag,
			createdby,
			createddate,
			lastupdby,
			lastupddate,
			remark,
			status,
			oldInspectId
		) VALUES
			<foreach collection="inspectList" item="item" index= "index" separator =",">
			 (
				#{item.company.recordId},
				#{item.produceBatch.recordId},
				#{item.produceBatchDetail.recordId},
				#{item.process.recordId},
				#{item.manufType},
				#{item.pcsEsperPnl},
				#{item.producePcsQty},
				#{item.passPcsQty},
				#{item.failedPcsQty},
				#{item.operator.recordId},
				#{item.checker.recordId},
				#{item.checkDate},
				1,
				#{item.createdBy.recordId},
				#{item.createdDate},
				#{item.lastUpdBy.recordId},
				#{item.lastUpdDate},
				#{item.remark},
				#{item.status},
				#{item.oldInspectId}
			)
		</foreach>
	</insert>
	
	<!-- zjn 2019-04-21 批量删除品质信息 -->
	<update id="deleteBatchInspect">
		UPDATE qc_inspect SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId in (${recordId})
	</update>
	
	<!-- zjn 2019-05-17 批量更新批次明细id -->
	<update id="updateBatchPbdId">
		UPDATE qc_inspect SET 
			produceBatchDetailId = #{produceBatchDetail.recordId},
			producePcsQty = #{producePcsQty}
		WHERE recordId in (${recordId})
	</update>
	
	<!-- zjn 2019-05-20 更新品质检查拆卡信息 -->
	<update id="updateInspectExamine">
		UPDATE qc_inspect SET 
			producePcsQty = #{producePcsQty},
			failedPcsQty = #{failedPcsQty},
			produceBatchDetailId = #{produceBatchDetail.recordId},
			status = #{status}
		WHERE recordId = #{recordId}
	</update>
	
	<!-- zjn 2019-05-22 查询未审核的品质检测记录 -->
	<select id="getNoExamineCount" resultType="Integer">
		SELECT 
			count(recordId) 
		FROM qc_inspect
		<where>
			companyId = #{company.recordId} AND activeFlag = 1
			<if test="produceBatchDetail != null and produceBatchDetail.recordId != null and produceBatchDetail.recordId != ''">
				AND produceBatchDetailId = #{produceBatchDetail.recordId}
			</if>
			<if test="status != null and status != ''">
				AND status = #{status}
			</if>
		</where> 
	</select>
	
	<!-- zjn 2019-05-22 获取报废与返工数量的总数 -->
	<select id="getSumInspectQty" resultType="Integer">
		SELECT
			SUM(ee.qty)
		FROM
		(
			SELECT
				SUM(discardPcsQty) AS "qty"
			FROM qc_discard 
			WHERE companyId = #{company.recordId} AND inspectId = #{recordId}
			AND activeFlag = 1
			UNION ALL
			SELECT
				SUM(reworkQty) AS "qty"
			FROM qc_rework 
			WHERE companyId = #{company.recordId} AND inspectId = #{recordId}
			AND activeFlag = 1
		) ee
	</select>
	
	<!-- zjn 2019-06-03 根据批次和批次明细获取品检测记录 -->
	<select id="getInspectList" resultType="Inspect">
		SELECT 
			<include refid="qcInspectColumns"/>
		FROM qc_inspect a
		<include refid="qcInspectJoins"/>
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId AND ppb.activeFlag = 1
		LEFT JOIN eg_carda_process_value cpv ON cpv.processCardAId = ppb.processCardAId AND cpv.activeFlag = 1
		LEFT JOIN eg_process ep ON ep.recordId = cpv.processId AND ep.recordId = a.processId AND ep.activeFlag = 1
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = 1
			<!-- 批次 -->
			<if test="produceBatch != null and produceBatch.recordId != null and produceBatch.recordId != ''">
				AND a.produceBatchId = #{produceBatch.recordId}
			</if>
			<!-- 批次明细 -->
			<if test="produceBatchDetail != null and produceBatchDetail.recordId != null and produceBatchDetail.recordId != ''">
				AND a.produceBatchDetailId = #{produceBatchDetail.recordId}
			</if>
			<!-- 状态 -->
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			AND !ISNULL(ep.recordId) 
			GROUP BY ep.recordId
			ORDER BY cpv.seqNum ASC
		</where>
	</select>
	
	<!-- zjn 2019-05-17 批量更新品质记录 -->
	<update id="updateBatchInspect"  parameterType="java.util.List">  
	    <foreach collection="list" item="item" index="index" open="" close="" separator=";">
	        update qc_inspect
	        <set>
	            passPcsQty = #{item.passPcsQty},
	            producePcsQty = #{item.producePcsQty},
	        </set>
	        where recordId = #{item.recordId}
	    </foreach>      
	</update>
	
	<!-- zjn 2019-06-03 获取工序的品质记录条数 -->
	<select id="getInspectCount" resultType="Integer">
		SELECT
			COUNT(recordId) 
		FROM qc_inspect
		WHERE companyId = #{company.recordId} AND activeFlag = 1
		AND produceBatchId = #{produceBatch.recordId}
		AND produceBatchDetailId = #{produceBatchDetail.recordId}
		AND processId = #{process.recordId}
	</select>
	
	<!-- zjn 2019-09-03 获取批次明细数量和总报废数量 -->
	<select id="getQtyPcsTAndSumDiscardPcsQty" resultType="Inspect">
		SELECT
			a.qtyPcsT AS "producePcsQty",
			SUM(qd.discardPcsQty) AS "discardQty",
			SUM(IFNULL(qd.discardPcsQtyA,0)) AS "discardPcsQtyA",
			SUM(IFNULL(qd.discardPcsQtyB,0)) AS "discardPcsQtyB"
		FROM pd_produce_batch_detail a
		LEFT JOIN qc_inspect qi ON qi.produceBatchDetailId = a.recordId AND qi.companyId = a.companyId AND qi.activeFlag = 1
		LEFT JOIN qc_discard qd ON qd.inspectId = qi.recordId AND qd.companyId = a.companyId AND qd.activeFlag = 1
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 
		AND a.recordId = #{produceBatchDetail.recordId} AND a.produceBatchId = #{produceBatch.recordId} 
	</select>
	
	<!-- zjn 2019-09-06 批量更新检测数量和合格数量 -->
	<update id="updateBatchQty" parameterType="java.util.List">
		<foreach collection="inspectList" item="item" index= "index" separator =";">
			UPDATE qc_inspect SET
				producePcsQty = #{item.producePcsQty},
				passPcsQty = #{item.passPcsQty}
			WHERE recordId = #{item.recordId}
		</foreach>
	</update>
	
	<!-- zjn 2019-09-11 更新品质的数量 -->
	<update id="updateQty">
		UPDATE qc_inspect SET
			passPcsQty = #{passPcsQty},
			failedPcsQty = #{failedPcsQty}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="deleteInspect">
		UPDATE qc_inspect SET 
			activeFlag = 2,
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateRejectInfo">
		update st_reject_application set repirNums=
		(SELECT SUM(qr.reworkQty)
			FROM qc_inspect qi
			LEFT JOIN qc_rework qr ON qi.recordId = qr.inspectId AND qr.activeFlag = 1
			WHERE qi.activeFlag = 1 AND qi.`status` = 900605 AND type = 2 AND qi.produceBatchId = #{produceBatch.recordId}),
			loadNums= 
			(SELECT SUM(qd.discardPcsQty)
			FROM qc_inspect qi
			LEFT JOIN qc_discard qd ON qi.recordId = qd.inspectId AND qd.activeFlag = 1
			WHERE qi.activeFlag = 1 AND qi.`status` = 900605 AND type = 2 AND qi.produceBatchId = #{produceBatch.recordId}),
			replyStatus = 1
		WHERE recordId = #{produceBatch.recordId}
	</update>
	

	<select id="getRejectDiscardAndReworkQty" resultType="Integer">
	SELECT SUM(IFNULL((SELECT SUM(discardPcsQty) FROM qc_discard WHERE qi.recordId = inspectId AND activeFlag = 1),0)+
			IFNULL((SELECT SUM(reworkQty) FROM qc_rework WHERE qi.recordId = inspectId AND activeFlag = 1),0))
			FROM qc_inspect qi
			WHERE 
			 qi.companyId = #{company.recordId} AND qi.produceBatchId = #{produceBatch.recordId} AND qi.type = 2 AND qi.activeFlag = 1
	</select>
	
	<select id="getRejectDiscardQty" resultType="Integer">
	SELECT SUM(IFNULL((SELECT SUM(discardPcsQty) FROM qc_discard WHERE qi.recordId = inspectId AND activeFlag = 1),0))
			FROM qc_inspect qi
			WHERE 
			 qi.companyId = #{company.recordId} AND qi.produceBatchId = #{produceBatch.recordId} AND qi.type = 2 AND qi.activeFlag = 1
	</select>
	
	<select id="getRejectReworkQty" resultType="Integer">
	SELECT SUM(IFNULL((SELECT SUM(reworkQty) FROM qc_rework WHERE qi.recordId = inspectId AND activeFlag = 1),0))
			FROM qc_inspect qi
			WHERE 
			 qi.companyId = #{company.recordId} AND qi.produceBatchId = #{produceBatch.recordId} AND qi.type = 2 AND qi.activeFlag = 1
	</select>
	

	<update id="updateStatusTwo">
		UPDATE qc_inspect SET
			<if test="inspectCause != null and inspectCause != ''">
				inspectCause = #{inspectCause},
			</if>
			auditStatus = #{auditStatus}
		WHERE companyId = #{company.recordId} AND activeFlag = 1
		AND produceBatchId = #{produceBatch.recordId} AND type = 2
	</update>
	
	<select id="getReworkList" resultType="Rework">
		SELECT * FROM qc_rework WHERE inspectId in(
		SELECT recordId FROM qc_inspect WHERE companyId = #{company.recordId} AND activeFlag = 1
		AND produceBatchId = #{produceBatch.recordId} AND type = 2
		) AND activeFlag = 1
		AND dutyProcessMore NOT IN(SELECT processId FROM pd_produce_record WHERE produceBatchId = #{produceBatch.recordId} AND activeFlag = 1 AND complaintId IS NOT NULL)
	</select>
	
	<select id="getReworkListTwo" resultType="Rework">
		SELECT * FROM qc_rework WHERE inspectId = #{recordId} AND activeFlag = 1
	
	</select>
	
	
	<update id="addRepirDiscardNum">
		UPDATE st_reject_application SET repirDiscardNum = (IFNULL(repirDiscardNum,0)+#{loadNums}) 
		<if test="status != null and status != ''">
				, replyStatus = 1
		</if>
		WHERE recordId = #{recordId}
	</update>
	
	<select id="getProduceRecordMaxIndex" resultType="Integer">
		SELECT IFNULL(max(produceBatchDetailId),0) FROM pd_produce_record WHERE complaintId = #{recordId}
	</select>
	
	<select id="getRefContractDetailByNoti" resultType="ContractDetail"> 
		SELECT r.* FROM (
		SELECT scd.*,
		scd.customerOrderDetailId as "customerOrderDetailId.recordId",
		 e.pnlLength AS 'contractCraftList.pnlLength' ,e.pnlWidth AS 'contractCraftList.pnlWidth' , e.pnlDivisor AS 'contractCraftList.pnlDivisor'
		  FROM qc_inspect a
		JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId AND ppb.`status` IN (900301,900305,900306) 
		JOIN pd_produce_batch_detail pbd ON pbd.recordId = a.produceBatchDetailId AND pbd.`status` IN (900301,900305,900306)
		JOIN sl_notification n ON n.recordId = ppb.notificationId AND n.`status` IN (200403,200404,200405) AND n.mergeType <![CDATA[<>]]> 200602
		JOIN sl_contract_detail scd ON scd.recordId = n.contractDetailId
		LEFT JOIN sl_contract_craft e ON e.recordId = scd.craftId
		AND e.companyId = a.companyId
		AND e.activeFlag = 1
		WHERE a.type in( 1,4) 
		AND a.recordId = #{recordId}
		UNION ALL 
		SELECT scd.*,
		scd.customerOrderDetailId as "customerOrderDetailId.recordId",
		 e.pnlLength AS 'contractCraftList.pnlLength' ,e.pnlWidth AS 'contractCraftList.pnlWidth' , e.pnlDivisor AS 'contractCraftList.pnlDivisor'
		  FROM qc_inspect a
		JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId AND ppb.`status` IN (900301,900305,900306) 
		JOIN pd_produce_batch_detail pbd ON pbd.recordId = a.produceBatchDetailId AND pbd.`status` IN (900301,900305,900306)
		JOIN sl_notification n ON n.recordId = ppb.notificationId AND n.`status` IN (200403,200404,200405) AND n.mergeType = 200602
		JOIN sl_notification n2 ON  n.recordId = n2.mergeId AND n2.activeFlag = 1
		JOIN sl_contract_detail scd ON scd.recordId = n2.contractDetailId
		LEFT JOIN sl_contract_craft e ON e.recordId = scd.craftId
		AND e.companyId = a.companyId
		AND e.activeFlag = 1
		WHERE a.type in ( 1,4) 
		AND a.recordId = #{recordId}
		) r
		limit 1
		
	</select>
	
	<select id="getRefContractDetailByReject" resultType="ContractDetail"> 
		SELECT scd.*,
		scd.customerOrderDetailId as "customerOrderDetailId.recordId",
		sp.price AS 'pricees.price',
		sp.recordId AS 'pricees.recordId',
		 e.pnlLength AS 'contractCraftList.pnlLength' ,e.pnlWidth AS 'contractCraftList.pnlWidth' , e.pnlDivisor AS 'contractCraftList.pnlDivisor' 
		 FROM qc_inspect a
		JOIN st_reject_application sra ON sra.recordId = a.produceBatchId
		JOIN sl_contract_detail scd ON scd.recordId = sra.contractDetailId
		LEFT JOIN sl_price sp ON sp.recordId = scd.priceId AND sp.activeFlag = 1
		LEFT JOIN sl_contract_craft e ON e.recordId = scd.craftId
		AND e.companyId = a.companyId
		AND e.activeFlag = 1
		WHERE a.type in (2,3)
		AND a.recordId = #{recordId}
	</select>
	
	<select id="getInpectConDetail" resultType="ContractDetail">
		SELECT scd.*,
		scd.customerOrderDetailId as "customerOrderDetailId.recordId",
		sp.price AS 'pricees.price',
		sp.recordId AS 'pricees.recordId',
		igc.productArea AS  'pcsArea', 
		igc.recordId AS 'deailIds',
		e.no AS 'craft.no',
		e.customerModel AS 'craft.customerModel',
		 e.pnlLength AS 'contractCraftList.pnlLength' ,e.pnlWidth AS 'contractCraftList.pnlWidth' , e.pnlDivisor AS 'contractCraftList.pnlDivisor'
		 ,old.`status` AS 'oldDetailStatus'
		  FROM qc_inspect a
		  JOIN sl_contract_detail scd ON scd.inspectId = a.recordId
		  LEFT JOIN sl_price sp ON sp.recordId = scd.priceId AND sp.activeFlag = 1
		  LEFT JOIN sl_contract_craft e ON e.recordId = scd.craftId
			AND e.companyId = a.companyId
			AND e.activeFlag = 1
		  LEFT JOIN icloud_group_center igc ON  igc.recordId = scd.groupCenterId AND igc.activeFlag = 1
		  LEFT JOIN sl_contract_detail old ON old.recordId = scd.oldDeailId
		WHERE  a.recordId = #{recordId} AND scd.activeFlag = 1 GROUP BY scd.recordId
	</select>
	
	<update id="updateContractDetailHistory">
		UPDATE sl_contract_detail SET activeFlag = 2
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateReplenishQtyInfo">
		UPDATE sl_contract_detail SET quantity = #{quantity}, subTotal= #{subTotal}, orderDeailArea = #{orderDeailArea},activeFlag = 1
		WHERE recordId = #{recordId}
	</update>
	
	<select id="getPageData" resultType="Inspect">
		SELECT
			b.recordId,
			a.recordId AS "contractDeailId",
			a.groupCenterId AS "groupCenterId",
			c.`no` AS "notificationNo",
			d.`status`
		FROM sl_contract_detail a
		LEFT JOIN qc_inspect b ON b.recordId = a.inspectId 
		LEFT JOIN sl_notification c ON c.companyId = a.companyId AND c.contractDetailId = a.recordId
		 AND c.activeFlag = 1 AND c.`status` <![CDATA[<>]]> 200406
		LEFT JOIN sl_contract_detail d ON d.recordId = a.oldDeailId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND a.oldDeailId IS NOT NULL
		AND FIND_IN_SET(b.recordId,#{recordId})
	</select>
	
	<update id="updateToHistory">
		UPDATE qc_inspect SET activeFlag = 2 WHERE recordId = #{recordId}
	</update>
	
	<select id="getContractDetailsByInspect" resultType="ContractDetail"> 
		SELECT scd.*,
		scd.customerOrderDetailId as "customerOrderDetailId.recordId",
		 e.pnlLength AS 'contractCraftList.pnlLength' ,e.pnlWidth AS 'contractCraftList.pnlWidth' , e.pnlDivisor AS 'contractCraftList.pnlDivisor'
		  FROM qc_inspect a
		JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId AND ppb.`status` IN (900301,900305,900306) 
		JOIN pd_produce_batch_detail pbd ON pbd.recordId = a.produceBatchDetailId AND pbd.`status` IN (900301,900305,900306)
		JOIN sl_notification n ON n.recordId = ppb.notificationId AND n.`status` IN (200403,200404,200405) AND n.mergeType = 200602
		JOIN sl_notification n2 ON  n.recordId = n2.mergeId AND n2.activeFlag = 1
		JOIN sl_contract_detail scd ON scd.recordId = n2.contractDetailId
		LEFT JOIN sl_contract_craft e ON e.recordId = scd.craftId
		AND e.companyId = a.companyId
		AND e.activeFlag = 1
		WHERE a.type in ( 1,4) 
		AND a.recordId = #{recordId}
		AND scd.status NOT IN (200204,200205)
	</select>
	
	<select id="getByRejectId" resultType="Inspect">
		SELECT * FROM qc_inspect WHERE produceBatchId = #{rejectId} AND type = 2 AND activeFlag = 1
	</select>
	
	<select id="checkContractCloseStatus" resultType="Integer">
		SELECT
			COUNT( 1 ) 
		FROM
			sl_contract_detail scd
			LEFT JOIN qc_inspect qi ON qi.recordId = scd.inspectId
			LEFT JOIN sl_contract_detail old ON old.recordId = scd.oldDeailId 
		WHERE
			qi.recordId = #{recordId}  
			AND scd.activeFlag = 1 
			AND old.`status` IN (
				200204,
			200210)
		
		<!-- SELECT
			COUNT( 1 ) 
		FROM
			pd_produce_batch ppb
			LEFT JOIN sl_notification sn ON ppb.notificationId = sn.recordId
			LEFT JOIN sl_contract_detail scd ON scd.recordId = sn.contractDetailId 
			AND scd.activeFlag = 1 
			AND sn.mergeType = 200601
			LEFT JOIN sl_notification sn2 ON sn2.mergeId = sn.recordId 
			AND sn.mergeType = 200602 
			AND sn2.mergeType = 200603 
			AND sn2.activeFlag = 1
			LEFT JOIN sl_contract_detail scd2 ON scd2.recordId = sn2.contractDetailId 
			AND scd2.activeFlag = 1 
		WHERE
			ppb.recordId = #{recordId} 
		AND
		IF
			( sn.mergeType = 200601, scd.`status`, scd2.`status` ) NOT IN (
				200204,
				200205,
			200210) -->
	</select>
	
	<select id="getResetData" resultType="Inspect">
		SELECT 
			<include refid="qcInspectColumns"/>,
			a.type,
   			ppb.no AS "produceBatch.no",
			ppb.status AS "produceBatch.status",
			ppb.discardQty AS "produceBatch.discardQty",
			n.recordId AS "produceBatch.notification.recordId",
			n.no AS "produceBatch.notification.no",
			n.craftNo AS "produceBatch.notification.craftNo"
		FROM qc_inspect a
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId AND ppb.`status` IN (900301,900305,900306)	
		LEFT JOIN sl_notification n ON n.recordId = ppb.notificationId AND n.`status` IN (200403,200404,200405)
		LEFT JOIN qc_discard qd ON a.recordId = qd.inspectId 
		LEFT JOIN pd_produce_record pr ON pr.produceBatchId = a.produceBatchId AND pr.produceBatchDetailId = a.produceBatchDetailId AND pr.processId = qd.dutyProcess
		<include refid="qcInspectJoins"/>
		WHERE a.`status` <![CDATA[<>]]> 900605 AND a.activeFlag = 1 AND qd.activeFlag = 1 
		AND qd.discardPcsQty<![CDATA[>]]>0 AND a.type = 1 AND a.createdDate <![CDATA[>=]]> #{createdDate} AND a.companyId = #{company.recordId}
		AND pr.handOverQtyPcsA <![CDATA[>]]> 0
	</select>
	
	<select id="getReplenishMergeList" resultType="Inspect">
		SELECT
		DISTINCT
			<include refid="qcInspectColumns"/>,
			ppb.no AS "produceBatch.no",
			ppb.status AS "produceBatch.status",
			ppb.discardQty AS "produceBatch.discardQty",
			n.recordId AS "produceBatch.notification.recordId",
			n.no AS "produceBatch.notification.no",
			n.craftNo AS "produceBatch.notification.craftNo",
			n.mergeType AS "produceBatch.notification.mergeType",
			n.processCardAId AS "produceBatch.notification.cardA.recordId",
			f.recordId AS "produceBatch.feed.recordId",
			f.no AS "produceBatch.feed.no",
			r.recordId AS "produceBatch.replenish.recordId",
			r.no AS "produceBatch.replenish.no",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(eg.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(eg.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(eg.name ," ","") END AS "process.name",
			pbd.no AS "produceBatchDetail.no",
			pbd.status AS "produceBatchDetail.status",
			pbd.discardQty AS "produceBatchDetail.discardQty",
			ppr.handOverFlag AS "handOverFlag",
			so.userName AS "operator.userName",
			sc.userName AS "checker.userName",
			a.type,
			NULL AS "rejectNo",
			a.inspectCause AS "inspectCause",
			a.rejectHandleType,
			a.replenishQty,
			a.mantissaQty,
			0 AS 'fedNum',
			0 AS "returnQty",
			a.mergeType,
			IFNULL((SELECT SUM(discardPcsQty) FROM qc_discard WHERE activeFlag = 1 AND inspectId = a.recordId),0) AS 'discardQty',
			IFNULL((SELECT SUM(reworkQty) FROM qc_rework WHERE activeFlag = 1 AND inspectId = a.recordId),0) AS 'reworkQty'
<!-- 			rscd.recordId AS 'contractDeailId',
			rscd.groupCenterId AS 'groupCenterId',
			sn2.no AS 'notificationNo' -->
		FROM qc_inspect a
		<include refid="qcInspectJoins"/>
		JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId AND ppb.`status` IN (900301,900305,900306) AND a.type in (1,4)
		JOIN pd_produce_batch_detail pbd ON pbd.recordId = a.produceBatchDetailId AND pbd.`status` IN (900301,900305,900306)
		JOIN eg_process eg ON eg.recordId = a.processId
		JOIN sl_notification n ON n.recordId = ppb.notificationId AND n.`status` IN (200403,200404,200405)
		JOIN pd_produce_record ppr ON ppr.produceBatchId = a.produceBatchId AND ppr.produceBatchDetailId = a.produceBatchDetailId AND ppr.processId = a.processId
		LEFT JOIN sm_user so ON so.recordId = a.operator
		LEFT JOIN sm_user sc ON sc.recordId = a.checker
		LEFT JOIN pd_feeding f ON f.recordId = ppb.feedingNo AND f.notificationId = ppb.notificationId AND f.activeFlag = 1 AND f.status = 700102
		LEFT JOIN pd_replenish r ON r.produceBatchNo = ppb.recordId AND r.notificationId = ppb.notificationId AND r.activeFlag = 1 AND r.status = 700402
		LEFT JOIN qc_rework qrk ON a.recordId = qrk.inspectId AND qrk.activeFlag = 1
		LEFT JOIN eg_carda_process_value ecpv ON ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = eg.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci ON epci.processId = eg.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1 
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
<!-- 		LEFT JOIN st_reject_application sra ON sra.companyId = a.companyId AND sra.recordId = a.produceBatchId AND a.type IN (2,3)
		LEFT JOIN sl_contract_detail rscd ON rscd.inspectId = a.recordId AND rscd.companyId = a.companyId
		LEFT JOIN sl_notification sn2 ON sn2.contractDetailId = rscd.recordId AND sn2.activeFlag = 1 AND sn2.companyId = a.companyId -->
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}  AND a.type in (1,4) AND a.auditStatus = 900601   
			AND FIND_IN_SET(a.recordId,#{replenishIds}) AND IFNULL(a.replenishMergeType,0) NOT IN (200602,200603)
		</where>
	</select>
	
	<update id="updateReplenishMergeId">
			UPDATE qc_inspect 
			SET 
				replenishMergeId = #{replenishMergeId},
				replenishMergeType = #{replenishMergeType}, 
				lastupdby = #{lastUpdBy.recordId},
				lastupddate = #{lastUpdDate}
			WHERE FIND_IN_SET( recordId,#{replenishIds})
	</update>
	
	<select id="getContractDetailsByInspectId" resultType="ContractDetail"> 
		SELECT scd.*,
		scd.customerOrderDetailId as "customerOrderDetailId.recordId",
		 e.pnlLength AS 'contractCraftList.pnlLength' ,e.pnlWidth AS 'contractCraftList.pnlWidth' , e.pnlDivisor AS 'contractCraftList.pnlDivisor'
		FROM  sl_contract_detail scd 
		LEFT JOIN sl_contract_craft e ON e.recordId = scd.craftId
		AND e.companyId = scd.companyId
		AND e.activeFlag = 1
		WHERE scd.inspectId = #{inspectId} AND scd.activeFlag = 1
	</select>
	
	<select id="getDayDisCardInfo" resultType="QualityDailyReportVo">
		SELECT
			b.`value` AS 'discardCauseOne',
			b.planRate,
			IFNULL(
			ROUND
			(
				SUM(
					CASE WHEN a.sourceType = 2 THEN
					(
						a.unGoodQty * h.pnlLength * h.pnlWidth / h.pnlDivisor / 1000000
					)
					ELSE a.unGoodQty * e.setWidth * e.setLength / e.pnlDivisor / 1000000
					END
				)
				,4
			),0)
			AS "discardArea"
		FROM qc_insect_task_detail a
		LEFT JOIN md_dict_value b ON b.companyId = a.companyId AND b.`value` = a.discardUnit AND b.itemId = 77 AND b.activeFlag = 1
		LEFT JOIN pd_produce_batch c ON c.recordId = a.produceBatchId AND c.`status` IN (900301, 900305, 900306) AND a.sourceType IN (1, 3)
		LEFT JOIN sl_notification d ON d.recordId = c.notificationId AND d.`status` IN (200403, 200404, 200405)
		LEFT JOIN eg_carda e ON e.recordId = d.processCardAId
		LEFT JOIN st_reject_application f ON f.recordId = a.produceBatchId AND a.sourceType = 2 AND f.companyId = a.companyId AND f.activeFlag = 1
		LEFT JOIN sl_contract_detail g ON g.recordId = f.contractDetailId
		LEFT JOIN sl_contract_craft h ON h.recordId = g.craftId
		LEFT JOIN qc_discard i ON i.taskDetailId = a.recordId
		LEFT JOIN qc_inspect j ON j.recordId = i.inspectId
		WHERE a.companyId = #{company.recordId}  AND a.activeFlag = 1
		AND a.inspectType = 1 AND a.STATUS = 3
		AND a.madeDate BETWEEN #{sentTimeStartQr} AND #{sentTimeEndQr}
		AND a.discardUnit IS NOT NULL AND b.`value` IS NOT NULL
		AND i.activeFlag = 1
		AND j.activeFlag = 1
		<if test="sourceType != null and sourceType != ''">
			AND a.sourceType = #{sourceType}
		</if>
		GROUP BY a.discardUnit
		ORDER BY discardArea DESC ,b.seqNum
	</select>
	
	<select id="getMonthDisCardInfo" resultType="QualityDailyReportVo">
		SELECT
			b.`value` AS 'discardCauseOne',
			IFNULL(
			ROUND
			(
				SUM(
					CASE WHEN a.sourceType = 2 THEN
					(
						a.unGoodQty * h.pnlLength * h.pnlWidth / h.pnlDivisor / 1000000
					)
					ELSE a.unGoodQty * e.setWidth * e.setLength / e.pnlDivisor / 1000000
					END
				)
				,4
			),0)
			AS "discardArea"
		FROM qc_insect_task_detail a
		LEFT JOIN md_dict_value b ON b.companyId = a.companyId AND b.`value` = a.discardUnit AND b.itemId = 77 AND b.activeFlag = 1
		LEFT JOIN pd_produce_batch c ON c.recordId = a.produceBatchId AND c.`status` IN (900301, 900305, 900306) AND a.sourceType IN (1, 3)
		LEFT JOIN sl_notification d ON d.recordId = c.notificationId AND d.`status` IN (200403, 200404, 200405)
		LEFT JOIN eg_carda e ON e.recordId = d.processCardAId
		LEFT JOIN st_reject_application f ON f.recordId = a.produceBatchId AND a.sourceType = 2 AND f.companyId = a.companyId AND f.activeFlag = 1
		LEFT JOIN sl_contract_detail g ON g.recordId = f.contractDetailId
		LEFT JOIN sl_contract_craft h ON h.recordId = g.craftId
		WHERE a.companyId = #{company.recordId}  AND a.activeFlag = 1
		AND a.inspectType = 1 AND a.STATUS = 3
		AND a.madeDate <![CDATA[<=]]> #{sentTimeEndQr}
		AND DATE_FORMAT(a.madeDate,'%Y%m') =  DATE_FORMAT(#{sentTimeEndQr},'%Y%m')
		AND a.discardUnit IS NOT NULL AND b.`value` IS NOT NULL
		<if test="sourceType != null and sourceType != ''">
			AND a.sourceType = #{sourceType}
		</if>
		GROUP BY a.discardUnit
		ORDER BY b.seqNum
	</select>
	
	<select id="getDayInstoreArea" resultType="BigDecimal">
	SELECT 
		SUM(IFNULL( ROUND(st.quantity * ec.setWidth * ec.setLength / ec.pnlDivisor / 1000000, 4 ), 0 )) AS 'dayInstoreArea'
	 FROM st_product_store st
	LEFT JOIN sl_notification sn ON sn.recordId = st.notificationId
	LEFT JOIN eg_carda ec ON ec.recordId = sn.processCardAId 
	WHERE st.inOutType = 11 AND st.companyId = #{company.recordId} AND st.activeFlag = 1 AND st.`status` = 99999901
	AND st.operateDate BETWEEN  #{sentTimeStartQr} AND #{sentTimeEndQr}
	</select>
	
	<select id="getMonthInstoreArea" resultType="BigDecimal">
	SELECT 
		SUM(IFNULL( ROUND(st.quantity * ec.setWidth * ec.setLength / ec.pnlDivisor / 1000000, 4 ), 0 )) AS 'dayInstoreArea'
	 FROM st_product_store st
	LEFT JOIN sl_notification sn ON sn.recordId = st.notificationId
	LEFT JOIN eg_carda ec ON ec.recordId = sn.processCardAId 
	WHERE st.inOutType = 11 AND st.companyId = #{company.recordId} AND st.activeFlag = 1 AND st.`status` = 99999901
    AND DATE_FORMAT(st.operateDate,'%Y%m') = DATE_FORMAT(#{sentTimeEndQr},'%Y%m')
	AND st.operateDate <![CDATA[<=]]> #{sentTimeEndQr}
	</select>
	
	<select id="getFiveDiscardCause" resultType="QualityDailyReportVo">
		SELECT
			*
		FROM
		(
			SELECT
				b.`value` AS 'discardCause',
				IFNULL(
				ROUND
				(
					SUM(
						CASE WHEN a.sourceType = 2 THEN
						(
							a.unGoodQty * h.pnlLength * h.pnlWidth / h.pnlDivisor / 1000000
						)
						ELSE a.unGoodQty * e.setWidth * e.setLength / e.pnlDivisor / 1000000
						END
					)
					,4
				),0)
				AS "discardArea"
			FROM qc_insect_task_detail a
			LEFT JOIN md_dict_value b ON b.companyId = a.companyId AND b.`value` = a.discardUnit AND b.itemId = 77 AND b.activeFlag = 1
			LEFT JOIN pd_produce_batch c ON c.recordId = a.produceBatchId AND c.`status` IN (900301, 900305, 900306) AND a.sourceType IN (1, 3)
			LEFT JOIN sl_notification d ON d.recordId = c.notificationId AND d.`status` IN (200403, 200404, 200405)
			LEFT JOIN eg_carda e ON e.recordId = d.processCardAId
			LEFT JOIN st_reject_application f ON f.recordId = a.produceBatchId AND a.sourceType = 2 AND f.companyId = a.companyId AND f.activeFlag = 1
			LEFT JOIN sl_contract_detail g ON g.recordId = f.contractDetailId
			LEFT JOIN sl_contract_craft h ON h.recordId = g.craftId
			WHERE a.companyId = #{company.recordId}  AND a.activeFlag = 1
			AND a.inspectType = 1 AND a.STATUS = 3
			AND a.madeDate BETWEEN #{sentTimeStartQr} AND #{sentTimeEndQr}
			AND IFNULL(a.discardCause,'') <![CDATA[<>]]> ''
			<if test="sourceType != null and sourceType != ''">
				AND a.sourceType = #{sourceType}
			</if>
			GROUP BY a.discardUnit
		) r
		ORDER BY r.discardArea DESC LIMIT 5
	</select>
	
	<select id="getFiveDiscardCraft" resultType="QualityDailyReportVo">
		SELECT
			*
		FROM
		(
			SELECT
				IFNULL(e.`no`,h.`no`) AS "craftNo",
				IFNULL(
				ROUND
				(
					SUM(
						CASE WHEN a.sourceType = 2 THEN
						(
							a.unGoodQty * h.pnlLength * h.pnlWidth / h.pnlDivisor / 1000000
						)
						ELSE a.unGoodQty * e.setWidth * e.setLength / e.pnlDivisor / 1000000
						END
					)
					,4
				),0)
				AS "craftNoArea"
			FROM qc_insect_task_detail a
			LEFT JOIN md_dict_value b ON b.companyId = a.companyId AND b.`value` = a.discardUnit AND b.itemId = 77 AND b.activeFlag = 1
			LEFT JOIN pd_produce_batch c ON c.recordId = a.produceBatchId AND c.`status` IN (900301, 900305, 900306) AND a.sourceType IN (1, 3)
			LEFT JOIN sl_notification d ON d.recordId = c.notificationId AND d.`status` IN (200403, 200404, 200405)
			LEFT JOIN eg_carda e ON e.recordId = d.processCardAId
			LEFT JOIN st_reject_application f ON f.recordId = a.produceBatchId AND a.sourceType = 2 AND f.companyId = a.companyId AND f.activeFlag = 1
			LEFT JOIN sl_contract_detail g ON g.recordId = f.contractDetailId
			LEFT JOIN sl_contract_craft h ON h.recordId = g.craftId
			WHERE a.companyId = #{company.recordId}   AND a.activeFlag = 1
			AND a.inspectType = 1 AND a.STATUS = 3
			AND a.madeDate BETWEEN #{sentTimeStartQr} AND #{sentTimeEndQr}
			<if test="sourceType != null and sourceType != ''">
				AND a.sourceType = #{sourceType}
			</if>
			GROUP BY IFNULL(e.`no`,h.`no`)
		) r
		ORDER BY r.craftNoArea DESC LIMIT 5
	</select>
	
	<select id="getTwoPartList" resultType="QualityDailyReportVo">
		SELECT
			mdv.`value` AS 'testTypeName',
			SUM( checkQty ) AS 'checkQuality',
			SUM( goodQty ) AS 'goodQuality',
			ROUND( SUM( goodQty )* 100 / SUM( checkQty ), 4 )  AS 'percentOfPass',
		(SELECT ROUND( SUM( goodQty )* 100 / SUM( checkQty ), 4 ) FROM qc_insect_task_detail qitd WHERE qitd.testWay = mdv.recordId AND DATE_FORMAT( qitd.madeDate, '%Y%m' ) = DATE_FORMAT( #{queryDate}, '%Y%m' )
	AND qitd.activeFlag = 1 ) AS 'monthPercentOfPass',
			mdv.planRate
		FROM
			md_dict_value mdv
			LEFT JOIN qc_insect_task_detail qt ON qt.testWay = mdv.recordId
			AND qt.madeDate BETWEEN #{sentTimeStartQr} AND #{sentTimeEndQr}
			AND qt.activeFlag = 1
			<if test="sourceType != null and sourceType != ''">
				AND qt.sourceType = #{sourceType}
			</if>
		WHERE
			mdv.itemId = 78 
			AND mdv.companyId =  #{company.recordId} 
			AND mdv.activeFlag = 1 
			AND mdv.`value` <![CDATA[<>]]> '客诉'
		GROUP BY
			mdv.recordId
	UNION ALL	
		SELECT
	'来料检查' AS 'testTypeName',
	SUM( a.detectionNum ) AS 'checkQuality',
	(SUM( a.detectionNum )- SUM( a.poorNum )) AS 'goodQuality',
	ROUND( (SUM( a.detectionNum )- SUM( a.poorNum ))* 100 / SUM( a.detectionNum ) , 4 ) AS 'percentOfPass' ,
	(SELECT ROUND( (SUM( detectionNum )- SUM( poorNum ))* 100 / SUM( detectionNum ) , 4 ) FROM qc_source_detection WHERE DATE_FORMAT( detectiondate, '%Y%m' ) = DATE_FORMAT( #{queryDate}, '%Y%m' )  AND activeFlag = 1 AND companyId = #{company.recordId}) AS 'monthPercentOfPass'
	,null
	FROM
		qc_source_detection a
	WHERE
		a.activeFlag = 1 
		AND a.companyId = #{company.recordId}
		AND a.detectiondate BETWEEN #{sentTimeStartQr} AND #{sentTimeEndQr}
	<!-- UNION ALL
		SELECT
			'客诉' AS 'testTypeName',
			SUM( checkQty ) AS 'checkQuality',
			SUM( goodQty ) AS 'goodQuality',
			ROUND( SUM( goodQty )* 100 / SUM( checkQty ), 4 )  AS 'percentOfPass',
			(SELECT ROUND( SUM( goodQty )* 100 / SUM( checkQty ), 4 ) FROM qc_insect_task_detail qitd WHERE qitd.sourceType = 2 AND DATE_FORMAT( qitd.madeDate, '%Y%m' ) = DATE_FORMAT( #{queryDate}, '%Y%m' ) 
	AND qitd.activeFlag = 1 ) AS 'monthPercentOfPass',
			mdv.planRate
		FROM
			md_dict_value mdv
			LEFT JOIN qc_insect_task_detail qt ON qt.sourceType = 2
			AND DATE_FORMAT( qt.madeDate, '%Y%m%d' ) = DATE_FORMAT( #{queryDate}, '%Y%m%d' ) 
			AND qt.activeFlag = 1 
		WHERE
			mdv.itemId = 78 
			AND mdv.companyId =  #{company.recordId} 
			AND mdv.activeFlag = 1 
			AND mdv.`value` = '客诉'
		GROUP BY
			mdv.recordId -->
	
	</select>
	
	<select id="getThreePartOne" resultType="QualityDailyReportVo">
	SELECT 
		COUNT( outBoundId) AS 'outBatchNum',
		(
			SELECT
				COUNT(1)
			FROM qc_insect_task_detail
			WHERE companyId = #{company.recordId} AND activeFlag = 1 AND sourceType = 2
			AND madeDate BETWEEN #{sentTimeStartQr} AND #{sentTimeEndQr}
			<if test="sourceType != null and sourceType != ''">
				AND sourceType = #{sourceType}
			</if>
		) AS 'complaintNum',
		(
			SELECT
				COUNT( outBoundId)
			FROM  st_product_store st1
			LEFT JOIN sl_contract_detail scd1 ON scd1.recordId = st1.contractDetailId
			LEFT JOIN sl_contract_craft scc1 ON scc1.recordId = scd1.craftId
			LEFT JOIN icloud_group_center igc1 ON igc1.recordId = scd1.groupCenterId
			WHERE st1.inOutType = 12 AND st1.companyId = #{company.recordId}  AND st1.activeFlag = 1 AND st1.`status` = 99999901
			AND igc1.recordId IS NOT NULL
			AND DATE_FORMAT(st1.operateDate,'%Y%m') = DATE_FORMAT(#{sentTimeEndQr},'%Y%m')
			AND st1.operateDate <![CDATA[<=]]>  #{sentTimeEndQr}
		) AS 'monthOutBatchNum',
		(
			SELECT
				COUNT(1)
			FROM qc_insect_task_detail
			WHERE  companyId = #{company.recordId} AND activeFlag = 1 AND sourceType = 2
			AND DATE_FORMAT(madeDate,'%Y%m') = DATE_FORMAT(#{sentTimeEndQr},'%Y%m')
			AND madeDate <![CDATA[<=]]>  #{sentTimeEndQr}
			<if test="sourceType != null and sourceType != ''">
				AND sourceType = #{sourceType}
			</if>
		)
		 AS 'monthComplaintNum'
	 FROM st_product_store st
	LEFT JOIN sl_contract_detail scd ON scd.recordId = st.contractDetailId
	LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId
	LEFT JOIN icloud_group_center igc ON igc.recordId = scd.groupCenterId
	WHERE st.inOutType = 12 AND st.companyId =#{company.recordId}  AND st.activeFlag = 1 AND st.`status` = 99999901
	AND igc.recordId IS NOT NULL AND st.operateDate BETWEEN #{sentTimeStartQr} AND #{sentTimeEndQr}


	</select>
	
	<select id="getThreePartList" resultType="QualityDailyReportVo">
		SELECT  
			mdv.`value` AS 'discardCauseOne',
			COUNT(qitd.recordId) AS 'complaintNum'
			FROM 
			md_dict_value mdv 
			LEFT JOIN qc_insect_task_detail qitd  ON mdv.`value` = qitd.discardUnit
			AND DATE_FORMAT(qitd.madeDate,'%Y%m') = DATE_FORMAT(#{sentTimeEndQr},'%Y%m')
			AND qitd.madeDate <![CDATA[<=]]> #{sentTimeEndQr}
			AND qitd.activeFlag = 1 AND sourceType = 2
			<if test="sourceType != null and sourceType != ''">
				AND qitd.sourceType = #{sourceType}
			</if>
			WHERE mdv.activeFlag = 1 AND mdv.companyId = #{company.recordId} AND mdv.itemId = 77
			GROUP BY mdv.recordId
			ORDER BY mdv.seqNum
					
	
	</select>
	
	<select id="getCompalintContentList" resultType="QualityDailyReportVo">
		SELECT  
		CONCAT(sra.`no`,'-',scc.`no`,'-',sra.rejectCause,'-',qitd.discardCause) AS 'complaintContents'
		FROM 
		md_dict_value mdv 
		LEFT JOIN qc_insect_task_detail qitd  ON mdv.`value` = qitd.discardUnit
		AND qitd.madeDate BETWEEN #{sentTimeStartQr} AND #{sentTimeEndQr}
		AND qitd.activeFlag = 1 AND sourceType = 2
		<if test="sourceType != null and sourceType != ''">
			AND qitd.sourceType = #{sourceType}
		</if>
		LEFT JOIN st_reject_application sra ON sra.recordId = qitd.produceBatchId
		LEFT JOIN  sl_contract_detail scd ON scd.recordId = sra.contractDetailId
		LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId
		WHERE mdv.activeFlag = 1 AND mdv.companyId = #{company.recordId} AND mdv.itemId = 77 AND sra.recordId IS NOT NULL
		GROUP BY sra.recordId
	</select>
	
	<select id="getCompalintHandle" resultType="QualityDailyReportVo">
	SELECT  
		IF(SUM(qi.replenishQty)<![CDATA[>]]> 0,'1、补料处理','') AS 'complaintContents'
		FROM 
		md_dict_value mdv 
		LEFT JOIN qc_insect_task_detail qitd  ON mdv.`value` = qitd.discardUnit
		AND qitd.madeDate BETWEEN #{sentTimeStartQr} AND #{sentTimeEndQr}
		AND qitd.activeFlag = 1 AND sourceType = 2
		<if test="sourceType != null and sourceType != ''">
			AND qitd.sourceType = #{sourceType}
		</if>
		LEFT JOIN qc_inspect qi ON qi.produceBatchId = qitd.produceBatchId AND qi.produceBatchDetailId = qitd.produceBatchDetailId AND qi.type = 2 AND qi.activeFlag = 1
		LEFT JOIN st_reject_application sra ON sra.recordId = qitd.produceBatchId
		WHERE mdv.activeFlag = 1 AND mdv.companyId = #{company.recordId} AND mdv.itemId = 77 AND sra.recordId IS NOT NULL;
	</select>
	
	<select id="getFourPartOneList"  resultType="QualityDailyReportVo">
		SELECT
			scc.`no` AS 'craftNo',
			scd.quantity AS 'replenishQuality',
			scd.orderDeailArea AS 'replenishiArea',
			(SELECT GROUP_CONCAT( DISTINCT discardCause) FROM qc_discard WHERE inspectId = qc.recordId AND activeFlag = 1) AS 'discardCause',
			(SELECT GROUP_CONCAT( DISTINCT discardCauseOne) FROM qc_discard WHERE inspectId = qc.recordId AND activeFlag = 1)  AS 'discardCauseOne'
		 FROM qc_inspect qc
		INNER  JOIN qc_insect_task qit ON  qit.activeFlag = 1
		INNER JOIN sl_contract_detail scd ON scd.inspectId = qc.recordId AND scd.activeFlag = 1
		LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId
		WHERE qc.companyId = #{company.recordId} AND qc.activeFlag = 1 AND qc.taskId = qit.recordId
		AND qc.replenishQty <![CDATA[>]]>0
		AND scd.quantity <![CDATA[>]]> 0
		AND qc.auditStatus = 900605
		AND qc.lastUpdDate BETWEEN #{sentTimeStartQr} AND #{sentTimeEndQr}
		AND qc.companyId = #{company.recordId} AND IFNULL(qc.replenishMergeType,200602) = 200602
		AND qc.type <![CDATA[<>]]> 2 AND qc.replenishMergeType IS NULL
		<if test="sourceType != null and sourceType != ''">
			AND qc.type = #{sourceType}
		</if>
		UNION ALL

		SELECT
			aa.craftNo,
			aa.replenishQuality,
			aa.replenishiArea,
			aa.discardCause,
			aa.discardCauseOne
		FROM
		(
			SELECT
				MAX(f.discardPcsQty) AS "discardPcsQty",
				f.discardCause AS "discardCause",
				f.discardCauseOne AS "discardCauseOne",
				e.`no` AS 'craftNo',
				d.quantity AS 'replenishQuality',
				d.orderDeailArea AS 'replenishiArea'
			FROM qc_inspect a
			LEFT JOIN qc_inspect b ON b.recordId = a.replenishMergeId
			LEFT JOIN sl_contract_detail d ON d.inspectId = b.recordId AND d.activeFlag = 1
			LEFT JOIN sl_contract_craft e ON e.recordId = d.craftId
			LEFT JOIN
			(
				SELECT
					inspectId AS "inspectId",
					SUM(discardPcsQty) AS "discardPcsQty",
					GROUP_CONCAT( DISTINCT discardCause) AS "discardCause",
					GROUP_CONCAT( DISTINCT discardCauseOne) AS "discardCauseOne"
				FROM qc_discard
				WHERE companyId = #{company.recordId} AND activeFlag = 1
				GROUP BY inspectId
			) f ON f.inspectId = a.recordId
			WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND a.replenishQty > 0
			AND d.quantity > 0 AND a.auditStatus = 900605 AND a.replenishMergeType = 200603
			AND b.lastUpdDate BETWEEN  #{sentTimeStartQr} AND #{sentTimeEndQr}
			<if test="sourceType != null and sourceType != ''">
				AND b.type = #{sourceType}
			</if>
			GROUP BY a.replenishMergeId
		) aa
	</select>
	
	<select id="getFourObjeOne"  resultType="QualityDailyReportVo">
		SELECT COUNT(DISTINCT qc.recordId) AS 'replenishBatch',
			(
				SELECT COUNT(DISTINCT qc.recordId)
				 FROM
				qc_inspect qc
				LEFT JOIN qc_insect_task qit ON  qit.activeFlag = 1 AND qit.recordId = qc.taskId
				WHERE qc.activeFlag = 1 AND qc.replenishQty >0
				AND ((qc.taskId = qit.recordId AND qc.replenishMergeType IS NULL) OR qc.replenishMergeType = 200602)
				AND qc.auditStatus = 900605 AND qc.lastUpdDate BETWEEN  #{sentTimeStartQr} AND #{sentTimeEndQr}
				<if test="sourceType != null and sourceType != ''">
					AND qc.type = #{sourceType}
				</if>
				AND qc.type <![CDATA[<>]]> 2 AND qc.recordId IN
				(
					SELECT DISTINCT inspectId FROM qc_discard WHERE companyId = #{company.recordId} AND activeFlag = 1
				)
			) AS 'monthReplenishBatch'
			 FROM 
			qc_inspect qc 
			LEFT JOIN qc_insect_task qit ON  qit.activeFlag = 1 AND qit.recordId = qc.taskId
			WHERE qc.activeFlag = 1 AND qc.replenishQty >0
			AND ((qc.taskId = qit.recordId AND qc.replenishMergeType IS NULL) OR qc.replenishMergeType = 200602)
			AND qc.auditStatus = 900605 AND qc.lastUpdDate BETWEEN  #{sentTimeStartQr} AND #{sentTimeEndQr}
			<if test="sourceType != null and sourceType != ''">
				AND qc.type = #{sourceType}
			</if>
			AND qc.type <![CDATA[<>]]> 2 AND qc.recordId IN
			(
				SELECT DISTINCT inspectId FROM qc_discard WHERE companyId = #{company.recordId} AND activeFlag = 1
			)
	</select>
	
	<select id="fourPartList" resultType="QualityDailyReportVo">
	SELECT mdv.`value` AS 'discardCauseOne',IFNULL(n.num,0) AS 'replenishQuality' FROM md_dict_value mdv 
		LEFT JOIN (
			SELECT
				aa.discardCauseOne,
				COUNT(aa.recordId) AS "num"
			FROM
			(
				SELECT
					a.discardCauseOne AS "discardCauseOne",
					MAX(a.discardPcsQty),
					b.recordId AS 'recordId'
				FROM qc_discard a
				LEFT JOIN qc_inspect b ON b.recordId = a.inspectId
				LEFT JOIN qc_insect_task c ON c.recordId = b.taskId AND c.activeFlag = 1
				WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND b.replenishQty > 0
				AND b.auditStatus = 900605 AND b.type <![CDATA[<>]]> 2
				AND b.lastUpdDate BETWEEN  #{sentTimeStartQr} AND #{sentTimeEndQr}
				<if test="sourceType != null and sourceType != ''">
					AND b.type = #{sourceType}
				</if>
				AND IFNULL(b.replenishMergeType,200602) = 200602
				GROUP BY b.recordId
			) aa
			GROUP BY aa.discardCauseOne
		) n ON n.discardCauseOne = mdv.`value`
		WHERE mdv.itemId = 77 AND mdv.activeFlag = 1 AND mdv.activeFlag = 1 AND mdv.companyId =  #{company.recordId}
		ORDER BY mdv.seqNum
	</select>
	
	<select id="getRevertDiscardCheck" resultType="Inspect">
		SELECT 
			qi.auditStatus,qi.mergeType,scd.`status`,qi.recordId,qitd.inspectType
		FROM qc_insect_task_detail qitd
		LEFT JOIN qc_discard qd ON qd.taskDetailId = qitd.recordId AND qd.activeFlag = 1
		LEFT JOIN qc_inspect qi ON (
		   qi.recordId = qd.inspectId
		   OR (
			   (qitd.inspectType = 3 OR qitd.inspectType = 4)
			   AND qi.taskId = qitd.taskId
			   AND qi.produceBatchDetailId = qitd.produceBatchDetailId
			   AND qi.replaceQty > 0
			)
	   	)
		AND qi.activeFlag = 1
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = qitd.produceBatchId 
		LEFT JOIN sl_notification sn ON sn.recordId = ppb.notificationId AND sn.activeFlag = 1
		LEFT JOIN sl_notification sn2 ON sn2.mergeId = sn.recordId AND sn.mergeType = 200602 AND sn2.activeFlag = 1
		LEFT JOIN sl_contract_detail scd ON scd.recordId = IFNULL(sn.contractDetailId,sn2.contractDetailId) AND scd.activeFlag = 1
		WHERE qitd.recordId = #{recordId} 
	
	</select>
	
	<update id="updateFailedPcsQty">
		UPDATE qc_inspect SET failedPcsQty = #{failedPcsQty},passPcsQty = #{passPcsQty}, activeFlag = #{activeFlag},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId} 
	
	</update>
	
	<select id="getReplenishContractDetail" resultType="ContractDetail">
		SELECT * FROM sl_contract_detail WHERE inspectId = #{recordId} AND activeFlag = 1
	</select>
	
	<update id="updateBackContractDetailId">
		UPDATE qc_inspect SET backContractDetailId = #{contractDeailId} WHERE recordId = #{recordId} 
	</update>
	
	<update id="updateContractOldDetailId">
		UPDATE sl_contract_detail SET 
		oldDeailId =  #{backContractDetailId},
		contractId = #{contractId}
		WHERE inspectId = #{recordId} AND activeFlag = 1 
	</update>
	
	<select id="getReplenishBackwardList" resultType="ContractDetail">
		SELECT
			sn.`no` AS 'notificationNo',
			scc.`no` AS 'craftNo',
			sc.customerPo,
			scc.customerModel,
			scd.quantity,
			scd.recordId  
		FROM
			sl_contract_detail scd
			INNER JOIN st_product_store st ON st.contractDetailId = scd.recordId 
			AND st.inOutType = 11 
			AND st.activeFlag = 1 
			AND st.quantity > 0
			LEFT JOIN sl_contract sc ON sc.recordId = scd.contractId
			LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId
			LEFT JOIN sl_notification sn ON sn.contractDetailId = scd.recordId 
			AND sn.activeFlag = 1 
		WHERE
			scd.`status` = 200207 
			AND scd.companyId = 17 
			AND st.materialId = ( SELECT DISTINCT materialId FROM st_product_store WHERE contractDetailId = ( SELECT oldDeailId FROM sl_contract_detail WHERE recordId = #{contractDeailId} ) AND inOutType = 11 LIMIT 1 )
	
	</select>
	
	<select id="getRepeatDate" resultType="InsectTaskDetail">
	SELECT DISTINCT taskDetailId AS 'recordId' FROM qc_discard WHERE inspectId IN 
		 (
		  361050,
		361045,
		361038,
		361042,
		361043,
		361024,
		361039,
		361022,
		361028,
		361046,
		361049,
		361026,
		361047,
		361055,
		361044,
		361023,
		361037,
		361036,
		361025,
		361053,
		361032,
		361029,
		361048,
		361054,
		361052,
		361027,
		361033,
		361040,
		361051,
		361031,
		361034,
		361035,
		361021,
		361041,
		361030,
		361112,
		361100,
		361110,
		361114,
		361103,
		361113,
		361098,
		361111,
		361099,
		361101,
		361096,
		361102,
		361108,
		361104,
		361097,
		361106,
		361105,
		361109,
		361107
		 )
	
	</select>
	
	<select id="getInsOccupiedMantissa" resultType="Inspect">
		SELECT
			a.recordId AS "recordId",
			j.`no` AS "craftNo",
			SUM(IFNULL(a.mantissaQty,0)) AS "mantissaQty"
		FROM qc_inspect a
		LEFT JOIN pd_produce_batch_detail b ON b.recordId = a.produceBatchDetailId AND a.type = 1
		LEFT JOIN st_reject_application c ON c.recordId = a.produceBatchId AND a.type = 2
		LEFT JOIN pd_produce_batch d ON d.recordId = b.produceBatchId
		LEFT JOIN sl_notification e ON e.recordId = d.notificationId
		LEFT JOIN sl_notification f ON f.mergeId = e.recordId AND f.companyId = a.companyId AND f.activeFlag = 1
		LEFT JOIN sl_contract_detail g ON g.recordId = f.recordId
		LEFT JOIN sl_contract_detail h ON h.recordId = e.contractDetailId
		LEFT JOIN sl_contract_detail i ON i.recordId = c.contractDetailId
		LEFT JOIN sl_contract_craft j ON j.recordId = IFNULL(IFNULL(g.craftId,h.craftId),i.craftId)
		LEFT JOIN st_mantissa_manage k ON k.dataId = a.recordId AND k.companyId = a.companyId AND k.operationType = 2 AND k.type = 2
		AND k.activeFlag = 1
		WHERE a.companyId = #{companyId} AND a.activeFlag = 1
		AND REPLACE(j.no," ","") = REPLACE(#{craftNo}," ","") AND a.mantissaQty IS NOT NULL
		AND (k.recordId IS NULL OR k.status = 1001)
		<if test="recordId != null and recordId != ''">
			AND a.recordId <![CDATA[<>]]> #{recordId}
		</if>
		GROUP BY j.`no`
	</select>

	<select id="getInspectIdByReject" resultType="String">
		SELECT
			recordId
		FROM qc_inspect
		WHERE companyId = #{company.recordId} AND activeFlag = 1
		  AND produceBatchId = #{produceBatch.recordId} AND type = 2
	</select>

	<select id="getShowWxInspect" resultType="Inspect">
		SELECT
			a.recordId,
			c.`no` AS "feedNo",
			IFNULL(d.`no`,sn.no) AS "notificationNo",
			IFNULL(d.craftNo,sn.craftNo) AS "craftNo",
			a.checkDate,
			e.userName AS "checker.userName",
			f.userName AS "operator.userName",
			a.produceBatchId AS "produceBatch.recordId",
			a.produceBatchDetailId AS "produceBatchDetail.recordId",
			a.taskId,
			a.replaceQty,
		    a.companyId AS "company.recordId"
		FROM qc_inspect a
				 LEFT JOIN pd_produce_batch b ON b.recordId = a.produceBatchId AND a.type NOT IN (2,3)
				 LEFT JOIN pd_feeding c ON c.recordId = b.feedingNo
				 LEFT JOIN sl_notification d ON d.recordId = b.notificationId
				 LEFT JOIN st_reject_application sra ON sra.recordId = a.produceBatchId AND a.type IN (2,3)
				 LEFT JOIN sl_contract_detail scd ON scd.recordId = sra.contractDetailId
				 LEFT JOIN sl_notification sn ON sn.contractDetailId = scd.recordId AND sn.companyId = a.companyId
			AND sn.activeFlag = 1 AND sn.status <![CDATA[<>]]> 200406
				 LEFT JOIN sm_user e ON e.recordId = a.checker
				 LEFT JOIN sm_user f ON f.recordId = a.operator
		WHERE a.recordId = #{recordId}
		GROUP BY a.recordId
	</select>

	<select id="selectReworkList" resultMap="user_reworkResult">
		select
			a.recordid AS "recordId",
			a.companyid AS "company.recordId",
			a.inspectid AS "inspectId",
			a.boardPartCraftId AS "boardPartCraft.recordId",
			a.craftNo as "boardPartCraft.craftNo",
			a.craftNo AS "craftNo",
			a.reworkqty AS "reworkQty",
			a.reworkcause AS "reworkCause",
			a.reworkresult AS "reworkResult",
			a.reworker AS "reworker.recordId",
			a.reworkchecker AS "checker.recordId",
			a.dutyProcess AS "dutyProcess.recordId",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(p.name," ",""))
							  WHEN 2 THEN CONCAT(REPLACE(p.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
							  ELSE REPLACE(p.name ," ","") END AS "dutyProcess.name",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(p.category," ",""))
							  WHEN 2 THEN CONCAT(REPLACE(p.category," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
							  ELSE REPLACE(p.category ," ","") END AS "dutyProcess.category",
			a.activeflag AS "activeFlag",
			(
				SELECT userName FROM sm_user WHERE recordId = a.reworker LIMIT 1
			) AS "reworker.userName",
			(
				SELECT userName FROM sm_user WHERE recordId = a.reworkchecker LIMIT 1
			) AS "checker.userName",
			(
				SELECT
					GROUP_CONCAT(DISTINCT category)
				FROM eg_process
				WHERE FIND_IN_SET(recordId,a.dutyProcessMore) AND activeFlag = 1
			) AS "dutyProcessMoreName"
		FROM qc_rework a
			LEFT JOIN qc_inspect qi ON qi.recordId = a.inspectId AND qi.activeFlag = 1
			LEFT JOIN pd_produce_batch ppb ON ppb.recordId = qi.produceBatchId AND ppb.activeFlag = 1
			LEFT JOIN eg_process p on p.recordId = a.dutyProcess
			LEFT JOIN eg_carda_process_value ecpv on ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = p.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
			LEFT JOIN eg_process_craft_install epci ON epci.processId = p.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1
			LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		WHERE a.inspectId IN (
			SELECT
			IF
			( qi.replenishMergeType = 200602, qim.recordId, qi.recordId ) AS 'recordId'
			FROM
			qc_inspect qi
			LEFT JOIN qc_inspect qim ON qim.replenishMergeId = qi.recordId
		  AND qim.replenishMergeType = 200603
		  AND qim.activeFlag = 1
		  AND qi.replenishMergeType = 200602
			WHERE FIND_IN_SET(qi.recordId,#{recordId})
			) AND a.activeFlag=1
	</select>

	<select id="getTaskDetailList" resultType="InsectTaskDetail">
		SELECT
			discardUnit,
			discardCause,
			inspectType,
			remark
		FROM qc_insect_task_detail
		WHERE produceBatchId = #{produceBatch.recordId} AND produceBatchDetailId = #{produceBatchDetail.recordId}
		AND taskId = #{taskId} AND activeFlag = 1 AND inspectType IN(3,4) AND `status` = 3
	</select>

	<select id="getreplenishIdsList" resultType="Inspect">
		SELECT
			GROUP_CONCAT( DISTINCT qi.recordId ) AS 'recordId'
		FROM qc_inspect qi
		INNER JOIN qc_discard qd ON qd.inspectId = qi.recordId AND qd.activeFlag = 1 AND qd.discardPcsQty <![CDATA[>]]> 0
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = qi.produceBatchId
		WHERE qi.companyId = #{company.recordId} AND qi.type = 1 AND qi.activeFlag = 1
		AND qi.auditStatus = 900601
		AND IFNULL(qi.replenishMergeType,0) NOT IN (200602,200603)
		GROUP BY ppb.notificationId
		HAVING COUNT(DISTINCT qi.recordId) <![CDATA[>]]> 1
	</select>

	<select id="findListExport" resultType="Inspect">
		SELECT * FROM (
		SELECT
		DISTINCT
		<include refid="qcInspectColumns"/>,
		ppb.no AS "produceBatch.no",
		ppb.status AS "produceBatch.status",
		ppb.discardQty AS "produceBatch.discardQty",
		n.recordId AS "produceBatch.notification.recordId",
		n.no AS "produceBatch.notification.no",
		n.craftNo AS "produceBatch.notification.craftNo",
		n.mergeType AS "produceBatch.notification.mergeType",
		n.processCardAId AS "produceBatch.notification.cardA.recordId",
		f.recordId AS "produceBatch.feed.recordId",
		f.no AS "produceBatch.feed.no",
		r.recordId AS "produceBatch.replenish.recordId",
		r.no AS "produceBatch.replenish.no",
		CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(eg.name," ",""))
		WHEN 2 THEN CONCAT(REPLACE(eg.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
		ELSE REPLACE(eg.name ," ","") END AS "process.name",
		pbd.no AS "produceBatchDetail.no",
		pbd.status AS "produceBatchDetail.status",
		pbd.discardQty AS "produceBatchDetail.discardQty",
		ppr.handOverFlag AS "handOverFlag",
		so.userName AS "operator.userName",
		sc.userName AS "checker.userName",
		a.type,
		NULL AS "rejectNo",
		a.inspectCause AS "inspectCause",
		a.rejectHandleType,
		a.replenishQty,
		a.mantissaQty,
		0 AS 'fedNum',
		0 AS "returnQty",
		a.mergeType,
		n.quantity AS "orderQty"
		<!-- 			rscd.recordId AS 'contractDeailId',
                    rscd.groupCenterId AS 'groupCenterId',
                    sn2.no AS 'notificationNo' -->
		FROM qc_inspect a
		<include refid="qcInspectJoins"/>
		JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId AND ppb.`status` IN (900301,900305,900306) AND a.type in (1,4)
		JOIN pd_produce_batch_detail pbd ON pbd.recordId = a.produceBatchDetailId AND pbd.`status` IN (900301,900305,900306)
		JOIN eg_process eg ON eg.recordId = a.processId
		JOIN sl_notification n ON n.recordId = ppb.notificationId AND n.`status` IN (200403,200404,200405)
		JOIN pd_produce_record ppr ON ppr.produceBatchId = a.produceBatchId AND ppr.produceBatchDetailId = a.produceBatchDetailId AND ppr.processId = a.processId
		LEFT JOIN sm_user so ON so.recordId = a.operator
		LEFT JOIN sm_user sc ON sc.recordId = a.checker
		LEFT JOIN pd_feeding f ON f.recordId = ppb.feedingNo AND f.notificationId = ppb.notificationId AND f.activeFlag = 1 AND f.status = 700102
		LEFT JOIN pd_replenish r ON r.produceBatchNo = ppb.recordId AND r.notificationId = ppb.notificationId AND r.activeFlag = 1 AND r.status = 700402
		LEFT JOIN qc_rework qrk ON a.recordId = qrk.inspectId AND qrk.activeFlag = 1
		LEFT JOIN eg_carda_process_value ecpv ON ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = eg.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci ON epci.processId = eg.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		<!-- 		LEFT JOIN st_reject_application sra ON sra.companyId = a.companyId AND sra.recordId = a.produceBatchId AND a.type IN (2,3)
                LEFT JOIN sl_contract_detail rscd ON rscd.inspectId = a.recordId AND rscd.companyId = a.companyId
                LEFT JOIN sl_notification sn2 ON sn2.contractDetailId = rscd.recordId AND sn2.activeFlag = 1 AND sn2.companyId = a.companyId -->
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}  AND a.type in (1,4)
			<if test="produceBatch!=null and produceBatch.notification!=null and produceBatch.notification.craftNo!=null and produceBatch.notification.craftNo!=''">
				AND REPLACE(n.craftNo," ","") LIKE CONCAT('%', REPLACE(#{produceBatch.notification.craftNo}," ",""), '%')
			</if>
			<if test="produceBatch!=null and produceBatch.feed!=null and produceBatch.feed.no!=null and produceBatch.feed.no!=''">
				AND (REPLACE(f.no," ","") LIKE CONCAT('%', REPLACE(#{produceBatch.feed.no}," ",""), '%') OR REPLACE(r.no," ","") LIKE CONCAT('%', REPLACE(#{produceBatch.feed.no}," ",""), '%'))
			</if>
			<if test="startDate!=null and startDate!='' and endDate!=null and endDate!=''">
				AND a.lastupddate BETWEEN #{startDate} AND #{endDate}
			</if>
			<if test="manufType==1"><!-- 这里用manufType存了检测类型 -->
				AND ppr.handOverFlag = 1
			</if>
			<if test="manufType==2">
				AND ppr.handOverFlag = 0
			</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} or a.operator = #{createdBy.recordId} or a.checker = #{createdBy.recordId} or qrk.reworkChecker = #{createdBy.recordId} or qrk.reworker = #{createdBy.recordId})
			</if>
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<if test="auditStatus != null and auditStatus != ''">
				AND a.auditStatus = #{auditStatus}
			</if>
			<if test="searchType != null and searchType != ''">
				<if test="searchType == 1">
					AND a.type = 1
				</if>
				<if test="searchType == 2">
					AND a.type in (2,3)
				</if>
			</if>
			<if test="rejectNo != null and rejectNo != ''">
				AND a.produceBatchId IN
				(
				SELECT
				recordId
				FROM st_reject_application
				WHERE companyId = a.companyId AND activeFlag = 1
				AND REPLACE(`no`," ","") LIKE CONCAT('%',REPLACE(#{rejectNo}," ",""),'%')
				)
			</if>
			<if test="recordId != null and recordId != ''">
				AND a.recordId = #{recordId}
			</if>
		</where>

		UNION ALL

		SELECT DISTINCT
		a.recordid AS "recordId",
		a.companyid AS "company.recordId",
		a.producebatchid AS "produceBatch.recordId",
		a.produceBatchDetailId AS "produceBatchDetail.recordId",
		a.processid AS "process.recordId",
		a.manuftype AS "manufType",
		a.pcsesperpnl AS "pcsEsperPnl",
		a.producepcsqty AS "producePcsQty",
		a.passpcsqty AS "passPcsQty",
		a.replaceQty,
		a.failedpcsqty AS "failedPcsQty",
		a.operator AS "operator.recordId",
		a.checker AS "checker.recordId",
		a.checkdate AS "checkDate",
		a.activeflag AS "activeFlag",
		a.createdby AS "createdBy.recordId",
		a.createddate AS "createdDate",
		a.lastupdby AS "lastUpdBy.recordId",
		a.lastupddate AS "lastUpdDate",
		a.remark AS "remark",
		a.STATUS AS "status",
		a.auditStatus AS "auditStatus",
		mc.NAME AS "company.name",
		a.onlyReplenish,
		a.replenishMergeType,
		a.replenishMergeId,
		a.producebatchid AS "produceBatch.no",
		'' AS "produceBatch.status",
		'' AS "produceBatch.discardQty",
		sra.recordId AS "produceBatch.notification.recordId",
		sra.`no` AS "produceBatch.notification.no",
		m.`no` AS "produceBatch.notification.craftNo",
		'' AS "produceBatch.notification.mergeType",
		'' AS "produceBatch.notification.cardA.recordId",
		'' AS "produceBatch.feed.recordId",
		'' AS "produceBatch.feed.no",
		'' AS "produceBatch.replenish.recordId",
		'' AS "produceBatch.replenish.no",
		(SELECT `name` FROM eg_process WHERE recordId = a.processid LIMIT 1) AS "process.name",
		a.produceBatchDetailId AS "produceBatchDetail.no",
		'' AS "produceBatchDetail.status",
		'' AS "produceBatchDetail.discardQty",
		'' AS "handOverFlag",
		so.userName AS "operator.userName",
		sc.userName AS "checker.userName",
		a.type,
		sra.no AS "rejectNo",
		a.inspectCause AS "inspectCause",
		a.rejectHandleType,
		a.replenishQty,
		a.mantissaQty,
		sra.fedNum AS 'fedNum',
		sra.quantity AS "returnQty",
		a.mergeType,
		b.quantity AS "orderQty"
		<!-- 		rscd.recordId AS 'contractDeailId',
                rscd.groupCenterId AS 'groupCenterId',
                sn2.no AS 'notificationNo' -->
		FROM qc_inspect a
		JOIN md_company mc ON mc.recordId = a.companyId
		LEFT JOIN sm_user so ON so.recordId = a.operator
		LEFT JOIN sm_user sc ON sc.recordId = a.checker
		LEFT JOIN qc_rework qrk ON a.recordId = qrk.inspectId
		AND qrk.activeFlag = 1
		LEFT JOIN st_reject_application sra ON sra.recordId = a.produceBatchId AND sra.companyId = a.companyId
		LEFT JOIN md_material m ON m.recordId = sra.materialId
		LEFT JOIN sl_contract_detail b ON b.recordId = sra.recordId
		<!-- 		LEFT JOIN sl_contract_detail rscd ON rscd.inspectId = a.recordId AND rscd.companyId = a.companyId
                LEFT JOIN sl_notification sn2 ON sn2.contractDetailId = rscd.recordId AND sn2.activeFlag = 1 AND sn2.companyId = a.companyId -->
		<where>
			a.companyid = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL} AND a.type in( 2,3)
			<if test="produceBatch!=null and produceBatch.notification!=null and produceBatch.notification.craftNo!=null and produceBatch.notification.craftNo!=''">
				AND REPLACE(m.`no`," ","") LIKE CONCAT('%', REPLACE(#{produceBatch.notification.craftNo}," ",""), '%')
			</if>
			<if test="produceBatch!=null and produceBatch.feed!=null and produceBatch.feed.no!=null and produceBatch.feed.no!=''">
				AND 1 = 2
			</if>
			<if test="startDate!=null and startDate!='' and endDate!=null and endDate!=''">
				AND a.lastupddate between #{startDate} AND #{endDate}
			</if>
			<if test="manufType==1"><!-- 这里用manufType存了检测类型 -->
				AND 1 = 2
			</if>
			<if test="manufType==2">
				AND 1 = 2
			</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (a.createdBy = #{createdBy.recordId} OR a.operator = #{createdBy.recordId} OR a.checker = #{createdBy.recordId} OR qrk.reworkChecker = #{createdBy.recordId} OR qrk.reworker = #{createdBy.recordId})
			</if>
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<if test="auditStatus != null and auditStatus != ''">
				AND a.auditStatus = #{auditStatus}
			</if>
			<if test="searchType != null and searchType != ''">
				<if test="searchType == 1">
					AND a.type = 1
				</if>
				<if test="searchType == 2">
					AND a.type in (2,3)
				</if>
			</if>
			<if test="rejectNo != null and rejectNo != ''">
				AND a.produceBatchId IN
				(
				SELECT
				recordId
				FROM st_reject_application
				WHERE companyId = a.companyId AND activeFlag = 1
				AND REPLACE(`no`," ","") LIKE CONCAT('%',REPLACE(#{rejectNo}," ",""),'%')
				)
			</if>
			<if test="recordId != null and recordId != ''">
				AND a.recordId = #{recordId}
			</if>
		</where>
		) r

		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>

	<select id="getListByProduceBatch" resultType="Inspect">
		SELECT DISTINCT
			<include refid="qcInspectColumns"/>,
			ppb.no AS "produceBatch.no",
			ppb.status AS "produceBatch.status",
			ppb.discardQty AS "produceBatch.discardQty",
			n.recordId AS "produceBatch.notification.recordId",
			n.no AS "produceBatch.notification.no",
			n.craftNo AS "produceBatch.notification.craftNo",
			n.mergeType AS "produceBatch.notification.mergeType",
			n.processCardAId AS "produceBatch.notification.cardA.recordId",
			f.recordId AS "produceBatch.feed.recordId",
			f.no AS "produceBatch.feed.no",
			r.recordId AS "produceBatch.replenish.recordId",
			r.no AS "produceBatch.replenish.no",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(eg.name," ",""))
			WHEN 2 THEN CONCAT(REPLACE(eg.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(eg.name ," ","") END AS "process.name",
			pbd.no AS "produceBatchDetail.no",
			pbd.status AS "produceBatchDetail.status",
			pbd.discardQty AS "produceBatchDetail.discardQty",
			ppr.handOverFlag AS "handOverFlag",
			so.userName AS "operator.userName",
			sc.userName AS "checker.userName",
			a.type,
			a.inspectCause AS "inspectCause",
			a.rejectHandleType,
			a.replenishQty,
			a.mantissaQty,
			a.mergeType
		FROM qc_inspect a
		<include refid="qcInspectJoins"/>
		JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId AND ppb.`status` IN (900301,900305,900306) AND a.type IN (1,4)
		JOIN pd_produce_batch_detail pbd ON pbd.recordId = a.produceBatchDetailId AND pbd.`status` IN (900301,900305,900306)
		JOIN eg_process eg ON eg.recordId = a.processId
		JOIN sl_notification n ON n.recordId = ppb.notificationId AND n.`status` IN (200403,200404,200405)
		JOIN pd_produce_record ppr ON ppr.produceBatchId = a.produceBatchId AND ppr.produceBatchDetailId = a.produceBatchDetailId AND ppr.processId = a.processId
		LEFT JOIN sm_user so ON so.recordId = a.operator
		LEFT JOIN sm_user sc ON sc.recordId = a.checker
		LEFT JOIN pd_feeding f ON f.recordId = ppb.feedingNo AND f.notificationId = ppb.notificationId AND f.activeFlag = 1 AND f.status = 700102
		LEFT JOIN pd_replenish r ON r.produceBatchNo = ppb.recordId AND r.notificationId = ppb.notificationId AND r.activeFlag = 1 AND r.status = 700402
		LEFT JOIN qc_rework qrk ON a.recordId = qrk.inspectId AND qrk.activeFlag = 1
		LEFT JOIN eg_carda_process_value ecpv ON ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = eg.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci ON epci.processId = eg.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		<where>
			a.companyId = #{inspect.company.recordId} AND a.activeFlag = 1 AND a.type IN (1,4) AND FIND_IN_SET(a.produceBatchId,#{produceBatchId})
			<if test="inspect.produceBatch!=null and inspect.produceBatch.notification!=null and inspect.produceBatch.notification.craftNo!=null and inspect.produceBatch.notification.craftNo!=''">
				AND REPLACE(n.craftNo," ","") LIKE CONCAT('%', REPLACE(#{inspect.produceBatch.notification.craftNo}," ",""), '%')
			</if>
			<if test="inspect.produceBatch!=null and inspect.produceBatch.feed!=null and inspect.produceBatch.feed.no!=null and inspect.produceBatch.feed.no!=''">
				AND (REPLACE(f.no," ","") LIKE CONCAT('%', REPLACE(#{inspect.produceBatch.feed.no}," ",""), '%') OR REPLACE(r.no," ","") LIKE CONCAT('%', REPLACE(#{inspect.produceBatch.feed.no}," ",""), '%'))
			</if>
			<if test="inspect.manufType==1"><!-- 这里用manufType存了检测类型 -->
				AND ppr.handOverFlag = 1
			</if>
			<if test="inspect.manufType==2">
				AND ppr.handOverFlag = 0
			</if>
			<if test="inspect.createdBy != null and inspect.createdBy.recordId != null and inspect.createdBy.recordId != ''">
				AND (a.createdBy = #{inspect.createdBy.recordId} or a.operator = #{inspect.createdBy.recordId} or a.checker = #{inspect.createdBy.recordId} or qrk.reworkChecker = #{inspect.createdBy.recordId} or qrk.reworker = #{inspect.createdBy.recordId})
			</if>
			<if test="inspect.status != null and inspect.status != ''">
				AND a.status = #{inspect.status}
			</if>
			<if test="inspect.auditStatus != null and inspect.auditStatus != ''">
				AND a.auditStatus = #{inspect.auditStatus}
			</if>

		</where>
	</select>


    <select id="selectDiscardListTwo" resultType="Discard">
		SELECT
			a.recordid AS "recordId",
			a.companyid AS "company.recordId",
			a.inspectid AS "inspectId",
			a.boardPartCraftId AS "boardPartCraft.recordId",
			a.craftNo as "boardPartCraft.craftNo",
			a.craftNo AS "craftNo",
			a.discardPcsQty AS "discardPcsQty",
			a.discardPcsQtyA AS "discardPcsQtyA",
			a.discardPcsQtyB AS "discardPcsQtyB",
			a.discardcause AS "discardCause",
			a.discardCauseOne AS "discardCauseOne",
			a.discardType AS "discardType",
			a.dutyProcess AS "dutyProcess.recordId",
			IFNULL(pro.name,
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(p.name," ",""))
			WHEN 2 THEN CONCAT(REPLACE(p.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(p.name," ","") END
			) AS "dutyProcess.name",
			IFNULL(pro.name,CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(p.category," ",""))
			WHEN 2 THEN CONCAT(REPLACE(p.category," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(p.category," ","") END ) AS "dutyProcess.category",
			a.activeflag AS "activeFlag",
			a.createdby AS "createdBy.recordId",
			a.createddate AS "createdDate",
			a.lastupdby AS "lastUpdBy.recordId",
			a.lastupddate AS "lastUpdDate",
			a.remark AS "remark"
		FROM qc_discard a
		LEFT JOIN qc_inspect qi ON qi.recordId = a.inspectId AND qi.activeFlag = 1
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = qi.produceBatchId AND ppb.activeFlag = 1
		LEFT JOIN eg_process p on p.recordId = a.dutyProcess
		LEFT JOIN eg_carda_process_value ecpv on ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = p.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci ON epci.processId = p.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		LEFT JOIN (
			SELECT  mdv.companyId,mdv.itemId,mdv.`value`,mdv.type,mdv.processIds,mdv.recordId,IFNULL(epcv.`name`,ep.category) AS 'name' FROM md_dict_value mdv
			LEFT JOIN eg_process ep ON ep.recordId = mdv.processIds AND mdv.type = 1
			LEFT JOIN eg_process_craft_values epcv ON epcv.recordId = mdv.processIds AND mdv.type = 2
			WHERE  mdv.itemId = 77
			GROUP BY mdv.companyId,mdv.itemId,mdv.`value`
		) pro ON pro.companyId = a.companyId AND pro.`value` = a.discardCauseOne
		WHERE a.inspectId IN (${recordId}) and a.activeFlag=1
	</select>
	<select id="getReportInspect" resultType="Inspect">
		SELECT
            ROUND(c.setLength * c.setWidth * a.failedPcsQty / c.pnlDivisor / 1000000,4) AS "area",
            a.failedPcsQty AS "quantity",
			h.customerPo AS "customerPo",
			j.`no` AS "craftNo",
			j.customerModel AS "customerModel",
			l.`name`
        FROM qc_inspect a
        LEFT JOIN pd_produce_batch b ON b.recordId = a.produceBatchId
        LEFT JOIN eg_carda c ON c.recordId = b.processCardAId
        LEFT JOIN sl_contract_detail d ON d.inspectId = a.recordId AND d.activeFlag = 1 AND d.companyId = a.companyId
        LEFT JOIN sl_contract_detail e ON e.recordId = IFNULL(d.oldDeailId,d.recordId)
        LEFT JOIN icloud_group_center f ON f.recordId = e.groupCenterId
        LEFT JOIN sl_contract_detail g ON g.recordId = IFNULL(f.contactDeailId,e.groupCenterId)
		LEFT JOIN sl_contract h ON h.recordId = g.contractId
		LEFT JOIN sl_contract_craft j ON j.recordId = g.craftId
		LEFT JOIN icloud_group_org_relation k ON k.deptId = h.deptId AND k.activeFlag = 1
		LEFT JOIN icloud_group_org l ON l.recordId = k.groupOrgId
		LEFT JOIN md_customer m ON m.recordId = h.customerId
		LEFT JOIN sm_user n ON n.recordId = IFNULL(h.userId,m.salesman)
        WHERE a.activeFlag = 1
		<if test="groupOrgId != null and groupOrgId != ''">
			AND FIND_IN_SET(k.groupOrgId,#{groupOrgId})
		</if>
		<if test="craftNo != null and craftNo != ''">
			AND REPLACE(j.`no`," ","") LIKE CONCAT('%', REPLACE(#{craftNo}," ",""), '%')
		</if>
		<if test="customerModel != null and customerModel != ''">
			AND REPLACE(j.customerModel," ","") LIKE CONCAT('%', REPLACE(#{customerModel}," ",""), '%')
		</if>
		<if test="customerPro != null and customerPro != ''">
			AND REPLACE(h.customerPo," ","") LIKE CONCAT('%', REPLACE(#{customerPro}," ",""), '%')
		</if>
		<if test="dimensionType != null and dimensionType != ''">
			<!-- 客户 -->
			<if test="dimensionType == 1">
				AND m.name = #{departName}
			</if>
			<!-- 部门 -->
			<if test="dimensionType == 2">
				AND l.name = #{departName}
			</if>
			<!-- 供应商 -->
			<if test="dimensionType == 3">

			</if>
			<!-- 业务员 -->
			<if test="dimensionType == 4">
				AND n.userName = #{departName}
			</if>
		</if>
		<if test="dateType != null and dateType != ''">
			<!-- 日 -->
			<if test="dateType == 1">
				AND DATE(a.createdDate) IN (${queryDateStrs})
			</if>
			<!-- 周 -->
			<if test="dateType == 2">
				AND YEARWEEK(DATE(a.createdDate),7) IN (${queryDateStrs})
			</if>
			<!-- 月 -->
			<if test="dateType == 3">
				AND DATE_FORMAT(a.createdDate,'%Y%m') IN (${queryDateStrs})
			</if>
			<!-- 自定义 -->
			<if test="dateType == 4">
				AND a.createdDate >= #{sentTimeStartQr} AND a.createdDate <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
		</if>
	</select>

	<update id="rollBackAudit">
		UPDATE qc_inspect SET
			auditStatus = #{auditStatus}
	  	WHERE  recordId = #{recordId}
	</update>

	<update id="updateApprove">
		UPDATE qc_inspect SET
		  	auditStatus = #{auditStatus},
		  	lastUpdBy = #{lastUpdBy.recordId},
		  	lastUpdDate = NOW()
		WHERE recordId =  #{recordId}
	</update>

	<select id="getInspectListTwo" resultType="Inspect">
		SELECT
			a.recordId,
			b.recordId AS "rejectId",
			c.recordId AS "conDetId",
			d.recordId AS "notifiId"
		FROM
			qc_inspect a
			LEFT JOIN st_reject_application b ON b.recordId = a.produceBatchId
			LEFT JOIN sl_contract_detail c ON c.inspectId = a.recordId
			LEFT JOIN sl_notification d ON d.contractDetailId = c.recordId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND a.recordId = #{recordId}
	</select>

	<select id="getInspectDataList" resultType="Inspect">
		SELECT
			ROUND(c.setLength * c.setWidth * IFNULL(a.replenishQty,0) / c.pnlDivisor / 1000000,4) AS "area",
			d.userName AS "operator.userName",
			e.userName AS "checker.userName",
			f.category,
			a.*
		FROM qc_inspect a
		LEFT JOIN pd_produce_batch b ON b.recordId = a.produceBatchId
		LEFT JOIN eg_carda c ON c.recordId = b.processCardAId
		LEFT JOIN sm_user d ON d.recordId = a.operator
		LEFT JOIN sm_user e ON e.recordId = a.checker
		LEFT JOIN eg_process f ON f.recordId = a.processId
		WHERE a.activeFlag = 1 AND b.feedingNo = #{feedingNo}
	</select>

</mapper>