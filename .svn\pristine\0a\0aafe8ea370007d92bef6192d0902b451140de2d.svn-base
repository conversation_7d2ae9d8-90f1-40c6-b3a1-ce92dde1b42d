<%@ page contentType="text/html;charset=UTF-8" %>

<div ng-intro-options="listOptions" ng-intro-method="helpList" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="readOnlyListOptions" ng-intro-method="helpReadOnlyList" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="infoOptions" ng-intro-method="helpInfo" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="infoRedOptions" ng-intro-method="helpRedInfo" ng-intro-autostart="shouldAutoStart"></div>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">财务管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="finance.collect">收款管理</a>
        </li>
    </ul>
    <div class="page-toolbar">
    	<button class="btn default btn-fit-height pull-right" ng-click="help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>
</div>
<!-- END 导航-->

<tabset class="tabset-margin-top">
    <!-- BEGIN 收款记录列表 -->
    <tab heading="收款记录" active="collectCtrl.tabs.viewForm.active">
        <div class="rows">
            <div id="listStep6" class="panel panel-default">
                <div class="panel-heading font-blue-hoki">查询</div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">客户编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="collectCtrl.query.customerNo.value"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">客户名称：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="collectCtrl.query.customerName.value"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">状态：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <select class="form-control"
                                                disable-auto-validate="true"
                                                ng-model="collectCtrl.query.status.value"
                                                ng-options="status.flagId as status.flagName for status in collectCtrl.query.statusList">
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                        	   <div class="col-md-6 col-lg-4">
		                            <div class="form-group">
		                                <label class="col-sm-3 col-md-4 control-label">收款时间：</label>
		                                <div class="col-sm-7 col-md-8">
		                                     <div class="input-prepend input-group">
												<span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
												<input type="text" class="form-control" disable-auto-validate="true"
													ng-blur="collectCtrl.initDate(collectCtrl.time)"
													kyb-daterange 
													kyb-daterange-options="collectCtrl.rangeOptions"
													ng-model="collectCtrl.time" 
													placeholder="请选择时间段">
											</div>
		                                </div>
		                            </div>
                        		</div>
                        		<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">收款月份：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" placeholder="请输入对账月份，如：201511" 
                                               ng-model="collectCtrl.query.period.value"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">类型：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <select class="form-control" ng-model="collectCtrl.typeQuery">
                                            <option value="">所有</option>
                                            <option value="1">手动</option>
                                            <option value="2">导入</option>
                                            <option value="3">导入已修改</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">操作状态：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <select class="form-control" ng-model="collectCtrl.statusQuery">
                                            <option value="">所有</option>
                                            <option value="1001">未确认</option>
                                            <option value="1002">已确认</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">导入日期：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input ng-model="collectCtrl.exportDateQuery"
                                               placeholder="请选择入库时间" type="text" class="form-control"
                                               data-date-format="yyyy-MM-dd"
                                               data-date-type="number"
                                               data-max-date="30.12.2099" data-autoclose="1"
                                               daysOfWeekDisabled="false" bs-datepicker>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default btn-default-width pull-right"
                                        ng-click="collectCtrl.doQuery()"><i class="fa fa-search"></i> 查询
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">收款记录列表</div>
                    <div id="listStep7" class="actions">
                        <div class="portlet-input input-inline input-small" ng-if="collectCtrl.right.edit">
                            <button type="button" class="btn green btn-default-width"
                                    ng-click="collectCtrl.addCollectMoney()"><i class="fa fa-plus"></i> 添加收款
                            </button>
                        </div>
                        <div class="portlet-input input-inline input-small" ng-if="collectCtrl.right.edit">
                            <button class="btn green blue-hoki btn-default-width" ng-click="collectCtrl.importCollectMoneyData()" ><i class="fa fa-upload"></i>收款记录导入</button>
                        </div>
                        <div class="portlet-input input-inline input-small" ng-if="collectCtrl.right.edit && collectCtrl.typeQuery == 2">
                            <button class="btn blue-hoki btn-default-width" ng-click="collectCtrl.batchOperate(1)"><i class="fa fa-check font-green"></i>批量确认</button>
                        </div>
                        <div class="portlet-input input-inline input-small" ng-if="collectCtrl.right.edit && collectCtrl.typeQuery == 2">
                            <button class="btn blue-hoki btn-default-width" ng-click="collectCtrl.batchOperate(2)"><i class="fa fa-mail-reply"></i>批量取消确认</button>
                        </div>
                        <div class="portlet-input input-inline input-small" ng-if="collectCtrl.right.edit && collectCtrl.typeQuery == 2">
                            <button class="btn blue-hoki btn-default-width" ng-click="collectCtrl.batchOperate(3)"><i class="fa fa-times font-red"></i>批量删除</button>
                        </div>
<!--                         <div class="portlet-input input-inline input-small" ng-if="collectCtrl.right.edit">
                            <button type="button" class="btn green btn-default-width"
                                    ng-click="collectCtrl.addMuchCollectMoney()"><i class="fa fa-plus"></i> 添加多月收款
                            </button>
                        </div> -->
                        <div class="portlet-input input-inline input-small">
		                        <form action="a/finance/collect/export" method="post" enctype="multipart/form-data" target="hidden_frame">
		                            <input type="text" ng-show="false" name="customer.no" value="{{collectCtrl.query.customerNo.value}}" /> 
		                            <input type="text" ng-show="false" name="customer.shortName" value="{{collectCtrl.query.customerName.value}}" /> 
		                            <input type="text" ng-show="false" name="orderBy" value="{{collectCtrl.query.sort.value}}" />
		                           	<input type="text" ng-show="false" name="status" value="{{collectCtrl.query.status.value}}" />
                                    <input type="text" ng-show="false" name="type" value="{{collectCtrl.typeQuery}}" />
                                    <input type="text" ng-show="false" name="operateStatus" value="{{collectCtrl.statusQuery}}" />
                                    <input type="text" ng-show="false"  name="queryAll" value="{{collectCtrl.queryAll}}"/>
		                            <input type="text" ng-show="false" name="sentTimeStartQr" value="{{collectCtrl.query.sentTimeStartQr.value}}"/>
		                            <input type="text" ng-show="false" name="sentTimeEndQr" value="{{collectCtrl.query.sentTimeEndQr.value}}"/>
                                    <input type="text" ng-show="false" name="exportDate" value="{{collectCtrl.exportDateQuery}}"/>
                                    <input type="text" ng-show="false" name="period" value="{{collectCtrl.query.period.value}}"/>
		                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出收款记录</button>
		                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
		                        </form>
                    	</div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div id="listStep1" class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading">
                                <th ng-if="collectCtrl.right.edit && collectCtrl.typeQuery == 2">
                                    <checkbox ng-model="collectCtrl.allPageChecked" value="1" name="test" ng-click="collectCtrl.selectAllPage()"></checkbox>
                                </th>
                                <th id="listStep2" ng-class="{'sorting': collectCtrl.sort.customerNo.both, 'sorting_desc': collectCtrl.sort.customerNo.desc, 'sorting_asc': collectCtrl.sort.customerNo.asc}" 
                                			ng-click="collectCtrl.sortClick('customerNo')">客户编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th ng-class="{'sorting': collectCtrl.sort.customerShortName.both, 'sorting_desc': collectCtrl.sort.customerShortName.desc, 'sorting_asc': collectCtrl.sort.customerShortName.asc}" 
                                			ng-click="collectCtrl.sortClick('customerShortName')">客户名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th ng-class="{'sorting': collectCtrl.sort.fundType.both, 'sorting_desc': collectCtrl.sort.fundType.desc, 'sorting_asc': collectCtrl.sort.fundType.asc}" 
                                			ng-click="collectCtrl.sortClick('fundType')">款项类型&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>金额</th>
                                <th ng-class="{'sorting': collectCtrl.sort.collectedDate.both, 'sorting_desc': collectCtrl.sort.collectedDate.desc, 'sorting_asc': collectCtrl.sort.collectedDate.asc}" 
                                			ng-click="collectCtrl.sortClick('collectedDate')">收款日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>对账月份</th>
                                <th>创建日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                               	<th>经办人</th>
                                <th ng-class="{'sorting': collectCtrl.sort.status.both, 'sorting_desc': collectCtrl.sort.status.desc, 'sorting_asc': collectCtrl.sort.status.asc}" 
                                			ng-click="collectCtrl.sortClick('status')">状态&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>手续费</th>
                                <th>其它费用</th>
                                <th>类型</th>
                                <th>操作状态</th>
                                <th>导入错误原因</th>
                                <th id="listStep3" ng-if="collectCtrl.right.edit">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in collectCtrl.page.data.list"
                                ng-dblclick="collectCtrl.seeCollectMoney($index)">
                                <td ng-if="collectCtrl.right.edit && collectCtrl.typeQuery == 2">
                                    <checkbox ng-model="row.checked" ng-change="collectCtrl.getCheckListTwo()"></checkbox>
                                </td>
                                <td><a ng-click="collectCtrl.seeCollectMoney($index)">{{row.customer.no}}</a></td>
                                <td ng-bind="row.customer.shortName"></td>
                                <td ng-if="row.fundType == '99999913'"> <span
									class="label label-sm label-success">预收款</span></td>
								<td ng-if="row.fundType == '********'"><span
									class="label label-sm label-danger">应收款</span></td>
								<td ng-if="row.fundType != '99999913' && row.fundType != '********'"><span
									class="label label-sm label-default">状态异常 </span></td>
                                <td class="text-right" ng-bind="row.amount  | currency:'':2"></td>
                                <td ng-bind="row.collectedDate"></td>
                                <td ng-bind="row.showPeriod"></td>
                                <td ng-bind="row.createdDate"></td>
                                <td ng-bind="row.responsiblePerson.userName"></td>
                                <td ng-if="row.status === 99999901">
	                                <span class="label label-sm label-default">未冲红</span>
	                            </td>
	                            <td ng-if="row.status === 99999902">
	                                <span class="label label-sm label-success">冲红单 </span>
	                            </td>
	                            <td ng-if="row.status === 99999903">
	                                <span class="label label-sm label-danger">已冲红</span>
	                            </td>
                                <td ng-if="row.status === 99999904">
                                    <span class="label label-sm label-info">已结账</span>
                                </td>
	                            <td ng-bind="row.changeFee | currency:'':2"></td>
	                            <td ng-bind="row.otherFee | currency:'':2"></td>
                                <td ng-bind="row.typeStr"></td>
                                <td ng-bind="row.operateStatusStr"></td>
                                <td ng-bind="row.exportFailCause"></td>
                                <td ng-if="collectCtrl.right.edit">
                                    <a href="javascript:void(0)" class="btn btn-xs btn-default"
                                       ng-show="collectCtrl.isCanRushRed(row.status) && row.operateStatus == '1002' && row.type == '1'"
                                       ng-click="collectCtrl.doWriteRed($index)"><i class="glyphicon glyphicon-trash font-red"></i> 冲红</a>
                                    <a ng-if="collectCtrl.right.edit && (row.type == '2' || row.type == '3') && row.operateStatus == '1001' && !row.exportInRemark" href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="collectCtrl.affirmCollectMoney(row)">
                                        <i class="fa fa-check font-green"></i> 确&nbsp;认</a>
                                    <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-if="collectCtrl.right.edit && (row.type == '2' || row.type == '3') && row.operateStatus == '1002' && !row.exportInRemark" ng-click="collectCtrl.cancleAffirmCollectMoney(row)">
                                        <i class="icon-trash font-red"></i> 取消确认</a>
                                    <a class="btn btn-xs btn-default" ng-if="collectCtrl.right.edit && (row.type == '2' || row.type == '3') && row.operateStatus == '1001'" href="javascript:void(0)" ng-click="collectCtrl.delCollectMoney(row)">
                                        <i class="fa fa-times font-red"></i> 删&nbsp;除</a>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row page-margin-top">
                        <div id="listStep5" class="col-md-12 col-lg-6">
                            <span class="inline">每页</span>
                            <select class="form-control inline" style="margin-top:8px; width:100px;"
                                    disable-valid-styling="true"
                                    disable-invalid-styling="true"
                                    ng-model="collectCtrl.page.pageSize"
                                    ng-change="collectCtrl.pageSizeChange()"
                                    ng-options="option for option in collectCtrl.page.pageSizeOptions">
                            </select>
                            <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{collectCtrl.page.data.startCount}} / {{collectCtrl.page.data.endCount}} 条，共 {{collectCtrl.page.data.count}} 条</span>
                        </div>
                        <div id="listStep4" class="col-md-12 col-lg-6">
                            <paging
                                    class="pull-right"
                                    page="collectCtrl.page.data.pageNo"
                                    page-size="collectCtrl.page.data.pageSize"
                                    total="collectCtrl.page.data.count"
                                    adjacent="1"
                                    dots="..."
                                    scroll-top="false"
                                    hide-if-empty="false"
                                    ul-class="pagination"
                                    active-class="active"
                                    disabled-class="disabled"
                                    show-prev-next="true"
                                    paging-action="collectCtrl.doPage(page, pageSize, total)">
                            </paging>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- END 收款记录列表 -->

    <!-- BEGIN 收款记录编辑 -->
    <tab active="collectCtrl.tabs.editForm.active" ng-show="collectCtrl.tabs.editForm.show">
        <tab-heading>收款记录详情 <i class="fa fa-times set-cursor-pointer" ng-click="collectCtrl.hideEditForm()"></i></tab-heading>
        <div class="rows" ng-if="collectCtrl.right.edit">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">收款记录{{collectCtrl.editTitle}}</div>
                </div>
                <div class="portlet-body">
                    <form id="infoStep1" class="form-horizontal" name="collectedMoneyForm" novalidate="novalidate"
                          ng-submit="collectCtrl.submitCollectMoneyFormMod(collectedMoneyForm);"
                          ng-init="collectCtrl.setFormScope(this)" ng-submit-force="true">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>单据编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input required type="text" class="form-control"
                                               ng-model="collectCtrl.finished.billNo" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>客户编码：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="collectCtrl.finished.customer" theme="bootstrap"
                                                   register-custom-form-control ng-disabled="!collectCtrl.right.edit"
                                                   on-select="collectCtrl.selectCustomers($select.selected.recordId)"
                                                   firstfocus="{{collectCtrl.focus.collect}}" required>
                                            <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
                                            <ui-select-choices
                                                    repeat="item in collectCtrl.refer.customerList | filter: $select.search | limitTo: collectCtrl.infiniteScroll.currentItems track by $index"
                                                    infinite-scroll="collectCtrl.addMoreItems()"
									    			infinite-scroll-distance="2">
                                                <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                <small>
                                                    <span style="color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.shortName}}<br></span>
                                                </small>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">客户名称：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" disabled
                                                  ng-bind="collectCtrl.finished.customer.shortName"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>款项类别：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="collectCtrl.finished.fundType" theme="bootstrap"
                                        			register-custom-form-control on-select="collectCtrl.selectFundTypes()" required>
                                            <ui-select-match placeholder="请选择...">{{$select.selected.name}}
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="item.value as item in collectCtrl.refer.fundTypeList">
                                                <div ng-bind-html="item.name"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="collectCtrl.finished.fundType == 99999913">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required" ng-show="collectCtrl.edit.selFtFlag">*</span>合同编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-disabled="collectCtrl.edit.contract" register-custom-form-control on-select="collectCtrl.selectContract()"
                                                   ng-model="collectCtrl.finished.contract" theme="bootstrap"
                                                   ng-required="collectCtrl.edit.selFtFlag">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.no}}
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="item in collectCtrl.contracts | filter: $select.search">
                                                <div ng-bind-html="item.no | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="collectCtrl.finished.fundType == 99999913">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">合同金额：</label>
                                    <div class="col-sm-7 col-md-8">
                                       <span class="form-control" disabled
                                             ng-bind="collectCtrl.finished.contract.totalAmt | currency:'':2"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="collectCtrl.finished.fundType == 99999913">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">合同已收金额：</label>
                                    <div class="col-sm-7 col-md-8">
                                       <span class="form-control" disabled
                                             ng-bind="collectCtrl.finished.contract.paidAmount | currency:'':2"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>收款方式：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <ui-select required ng-model="collectCtrl.finished.collectWay"
                                                 register-custom-form-control  theme="bootstrap" required>
                                            <ui-select-match
                                                    placeholder="请选择...">{{$select.selected.value}}
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="item.recordId as item in collectCtrl.refer.collectWayList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>经办人：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <ui-select required ng-model="collectCtrl.finished.responsiblePerson"
                                                 register-custom-form-control  theme="bootstrap">
                                            <ui-select-match
                                                    placeholder="请选择...">{{$select.selected.userName}}
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="item in collectCtrl.refer.employeeList | filter: $select.search">
                                                <div ng-bind-html="item.userName | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>收款日期：</label>
                                    <div class="col-sm-7 col-md-8">
	                                    <input type="text" class="form-control ng-valid ng-touched" ng-model="collectCtrl.finished.collectedDate"
			                            data-date-format="yyyy-MM-dd"
			                            data-date-type="number"
			                            data-min-date="02/10/1901"
			                            data-max-date="today"
			                            data-autoclose="1"
			                            daysOfWeekDisabled="false"
			                            bs-datepicker required/>
                                    </div>
                                </div>
                            </div>
                        	<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">收款银行：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control ng-valid ng-touched"
                                        		typeahead="state.bank for state in collectCtrl.refer.receiveAccounts | filter:$viewValue | limitTo:8"
                                               ng-model="collectCtrl.finished.bank" ng-maxlength="50">       
                                    </div>
                                </div>
                            </div>
                        	<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">收款账号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control ng-valid ng-touched" 
                                        	   typeahead="state.receiveAccount for state in collectCtrl.refer.receiveAccounts | filter:$viewValue | limitTo:8"
                                               ng-model="collectCtrl.finished.receiveAccount" ng-Banknumber>       
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4" ng-if="collectCtrl.finished.type && (collectCtrl.finished.type == 2 || collectCtrl.finished.type == 3)">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">账期：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="collectCtrl.periods" theme="bootstrap" multiple>
                                            <ui-select-match placeholder="请选择...">{{$item}}</ui-select-match>
                                            <ui-select-choices repeat="item in collectCtrl.refer.periodList | filter: $select.search">
                                                <div ng-bind-html="item | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">收款金额：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control ng-valid ng-touched"
                                               ng-model="collectCtrl.finished.amount"  ng-disabled = "collectCtrl.finished.fundType == ******** || !collectCtrl.finished.type || collectCtrl.finished.type == 1">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4" ng-if="collectCtrl.finished.fundType == ******** && (!collectCtrl.finished.type || collectCtrl.finished.type == 1)">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">手续费：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control ng-valid ng-touched" ng-model="collectCtrl.finished.changeFee" ng-checkmoneymoredragon>       
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="collectCtrl.finished.fundType == ******** && (!collectCtrl.finished.type || collectCtrl.finished.type == 1)">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">其它费用：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control ng-valid ng-touched" ng-model="collectCtrl.finished.otherFee" ng-checkmoneymoredragon>       
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-show="collectCtrl.finished.fundType == '********' && (!collectCtrl.finished.type || collectCtrl.finished.type == 1)">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">核销对账：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <label class="radio-inline">
<%--											  <input type="radio" value="true"  ng-model="collectCtrl.finished.verificationSheetStatus" ng-click="collectCtrl.radioFundTypes()"/>--%>
											    是
										</label>
<%--										<label class="radio-inline">
											  <input type="radio" value="false" ng-model="collectCtrl.finished.verificationSheetStatus" ng-click="collectCtrl.radioFundTypes()"/>
											  否
										</label>--%>
                                    </div>
                                </div>
                            </div>
			                  <div class="col-md-6 col-lg-4" ng-if="!collectCtrl.edit.period && collectCtrl.finished.verificationSheetStatus && (!collectCtrl.finished.type || collectCtrl.finished.type == 1)">
			                        <div class="form-group">
			                            <label class="col-sm-3 col-md-4 control-label"><span class="required" ng-show="!collectCtrl.edit.period && collectCtrl.finished.verificationSheetStatus">*</span>收款账期：</label>
			                            <div class="col-sm-7 col-md-8">
			                                <ui-select on-select="collectCtrl.selectPeriod(collectCtrl.finished,1)" ng-model="collectCtrl.singleReceivableIds" multiple on-remove="collectCtrl.setShowList($item.recordId)"
			                                           theme="bootstrap">
			                                    <ui-select-match placeholder="请选择...">{{$item.no +'-'+ $item.period + "-" + $item.rcvAmount + "元"}} 
			                                    </ui-select-match>
			                                    <ui-select-choices repeat="item.recordId as item in collectCtrl.customerPeriodList | filter: $select.search">
			                                            <div ng-bind-html="item.no + '-' + item.period +'-'+ item.rcvAmount + '元' | highlight: $select.search"></div>
			                                    </ui-select-choices>
			                                </ui-select>
			                            </div>
			                        </div>
			                    </div>
                        </div>
                        <div class="row">
                            <div class="col-md-8 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>

                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control"
                                                      ng-model="collectCtrl.finished.remark"
                                                      ng-maxlength="255"></textarea>            
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <div class="col-sm-offset-4 col-sm-10">
                                    <button type="submit" class="btn btn-primary btn-default-width"
                                            style="margin-left:15px;"><i class="fa fa-save"></i> 保&nbsp;存
                                    </button>
                                    <button type="button" class="btn default ng-scope"
                                            ng-click="collectCtrl.cacelBtn(collectedMoneyForm)">取消
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                         <!-- 应收明细列表  start-->
	                     <div class="row form-group" style="margin-bottom: 15px;" ng-if="collectCtrl.customerPeriodDetailList && collectCtrl.customerPeriodDetailList.length > 0">
	                     	<div class="col-md-12">
	                           	<table class="table table-bordered table-condensed">
	                           		<thead>
	                           			<tr class="heading">
	                           				<th><checkbox ng-model="collectCtrl.allPageChecked" value="1" name="test" ng-click="collectCtrl.selectAllPage()"></checkbox></th>
	                           				<th>账期</th>
	                           				<th>类型</th>
	                           				<th>客户订单号</th>
	                           				<th>规格型号</th>
	                           				<th>应收金额</th>
	                           				<th>确认应收金额</th>
	                           			</tr>
	                           			<tr>
	                           				<td></td>
	                           				<td><input type="text" class="form-control" ng-model="ctrl.periodSearch"></td>
											<td><input type="text" class="form-control" ng-model="ctrl.typeSearch"></td>
											<td><input type="text" class="form-control" ng-model="ctrl.customerPoSearch"></td>
											<td><input type="text" class="form-control" ng-model="ctrl.specificationSearch"></td>
											<td></td>
											<td></td>
	                           			</tr>
	                           		</thead>
	                           		<tbody ng-repeat="row in collectCtrl.customerPeriodDetailList | filter:{
	                           				'period':ctrl.periodSearch,
											'typeStr':ctrl.typeSearch,
											'customerPo':ctrl.customerPoSearch,
											'specification':ctrl.specificationSearch}">
	                           			<tr>
	                           				<td><checkbox ng-model="row.checked" ng-change="collectCtrl.getCheckList(row)"></checkbox></td>
	                           				<td ng-bind="row.period"></td>
	                           				<th ng-if="row.type != 4 && row.type != 5">{{row.typeStr}}</th>
	                           				<th ng-if="!(row.type != 4 && row.type != 5)" class="success">{{row.typeStr}}</th>
	                           				<td ng-bind="row.customerPo"></td>
	                           				<td ng-bind="row.specification"></td>
	                           				<td ng-bind="row.singAmount"></td>
	                           				<td>
	                           					<input class="form-control" type="text" ng-model="row.confirmAmount" ng-blur="collectCtrl.validConfirmAmount(row)" ng-disabled="row.type == 4 || row.type == 5"/>
	                           				</td>
	                           			</tr>
	                           			
	                           		</tbody>
	                           	</table>
	                         </div>
	                     </div>
	                     <!-- 应收明细列表 end -->
                    </form>
                </div>
            </div>
        </div>
    </tab>
    <!-- END 收款记录编辑 -->
    
    <!-- BEGIN 收款记录详情 -->
    <tab active="collectCtrl.tabs.seeDetail.active" ng-show="collectCtrl.tabs.seeDetail.show">
        <tab-heading>收款管理详情<i class="fa fa-times set-cursor-pointer" ng-click="collectCtrl.hideSeeDetail()"></i></tab-heading>
        <div class="rows">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">收款管理{{collectCtrl.editTitle}}</div>
                </div>
                <div class="portlet-body">
                    <form class="form-horizontal" name="writeRedForm"
                          ng-submit="collectCtrl.submitWriteRed(writeRedForm)" ng-init="collectCtrl.setFormScope(this)"
                          ng-submit-force="true">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>单据编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-disabled="true"
                                               ng-model="collectCtrl.finished.billNo"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>客户编码：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-disabled="true"
                                               ng-model="collectCtrl.finished.customer.no"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">客户名称：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" disabled
                                                  ng-bind="collectCtrl.finished.customer.shortName"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>款项类别：</label>
                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="collectCtrl.finished.fundType | clFundTypeFilter"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="collectCtrl.finished.fundType == 99999913">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>合同编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-disabled="true"
                                               ng-model="collectCtrl.finished.contract.no"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="collectCtrl.finished.fundType == 99999913">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">合同金额：</label>
                                    <div class="col-sm-7 col-md-8">
                                       <span class="form-control" disabled
                                             ng-bind="collectCtrl.finished.contract.totalAmt | currency:'':2"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>收款方式：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-disabled="true"
                                               ng-model="collectCtrl.collectWay.value"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>收款金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" disabled
                                             ng-bind="collectCtrl.finished.amount | currency:'':2"></span>       
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>收款日期：</label>
                                    <div class="col-sm-7 col-md-8">
										<span class="form-control" disabled ng-bind="collectCtrl.finished.collectedDate">
                                    </div>
                                </div>
                            </div>
                        	<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">收款银行：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control ng-valid ng-touched"
                                               ng-disabled="true" ng-model="collectCtrl.finished.bank">
                                    </div>
                                </div>
                            </div>
                        	<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">收款账号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control ng-valid ng-touched"
                                               ng-disabled="true" ng-model="collectCtrl.finished.receiveAccount">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>经办人：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-disabled="true"
                                               ng-model="collectCtrl.finished.responsiblePerson.userName"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="collectCtrl.finished.fundType == ********">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">手续费：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" disabled
                                             ng-bind="collectCtrl.finished.changeFee | currency:'':2"></span>       
                                    </div>
                                </div>
                            </div>
                             <div class="col-md-6 col-lg-4" ng-if="collectCtrl.finished.fundType == ********">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">其它费用：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" disabled
                                             ng-bind="collectCtrl.finished.otherFee | currency:'':2"></span>       
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                        <div class="col-md-2 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">对账日期：</label>
                                     <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control ng-valid ng-touched"
                                        	   ng-disabled="true"
                                               ng-model="collectCtrl.finished.period" ng-maxlength="50">       
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>

                                    <div class="col-sm-7 col-md-10">
                                           <textarea class="form-control" ng-disabled="true"
                                                      ng-model="collectCtrl.finished.remark"
                                                      maxlength="255"></textarea>           
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" ng-if="collectCtrl.finished.status != collectCtrl.rushRedStatus.unRush.flagId">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>冲红原因：</label>
                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-model="collectCtrl.finished.writeOffCause"
                                                      ng-disabled="true" ng-maxlength="50" required></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row"ng-if="collectCtrl.edit.isRushRed  && collectCtrl.finished.status == collectCtrl.rushRedStatus.unRush.flagId">
                            <div class="col-md-12 col-lg-8">
                                <div id="infoRedStep1" class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">冲红原因：<span class="required">*</span></label>
                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-model="collectCtrl.finished.writeOffCause"
                                                      firstfocus="{{collectCtrl.focus.writered}}" ng-maxlength="50" required></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-offset-4">
                        	<button id="infoRedStep2" type="submit"  class="btn btn-primary btn-default-width" style="margin-left:15px;" ng-disabled="writeRedForm.$invalid" 
                                			ng-if="collectCtrl.edit.isRushRed && collectCtrl.finished.status == collectCtrl.rushRedStatus.unRush.flagId">
                                			<i class="fa fa-save"></i> 冲&nbsp;红</button>
                        </div>          			
                    </form>
                </div>
                </div>
                <!-- 分月记录列表 -->
				<div class="portlet light bordered" ng-if="collectCtrl.finished.cmmList && collectCtrl.finished.cmmList.length > 0">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">多月收款记录列表</div>
                </div>
                <div class="portlet-body">
                    <div id="listStep1" class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading">
                                <th id="listStep2" ng-class="{'sorting': collectCtrl.sort.customerNo.both, 'sorting_desc': collectCtrl.sort.customerNo.desc, 'sorting_asc': collectCtrl.sort.customerNo.asc}" 
                                			ng-click="collectCtrl.sortClick('customerNo')">客户编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th ng-class="{'sorting': collectCtrl.sort.customerShortName.both, 'sorting_desc': collectCtrl.sort.customerShortName.desc, 'sorting_asc': collectCtrl.sort.customerShortName.asc}" 
                                			ng-click="collectCtrl.sortClick('customerShortName')">客户名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th ng-class="{'sorting': collectCtrl.sort.fundType.both, 'sorting_desc': collectCtrl.sort.fundType.desc, 'sorting_asc': collectCtrl.sort.fundType.asc}" 
                                			ng-click="collectCtrl.sortClick('fundType')">款项类型&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>金额</th>
                                <th ng-class="{'sorting': collectCtrl.sort.collectedDate.both, 'sorting_desc': collectCtrl.sort.collectedDate.desc, 'sorting_asc': collectCtrl.sort.collectedDate.asc}" 
                                			ng-click="collectCtrl.sortClick('collectedDate')">收款日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>对账月份</th>
                                <th>创建日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                               	<th>经办人</th>
                                <th ng-class="{'sorting': collectCtrl.sort.status.both, 'sorting_desc': collectCtrl.sort.status.desc, 'sorting_asc': collectCtrl.sort.status.asc}" 
                                			ng-click="collectCtrl.sortClick('status')">状态&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>手续费</th>
                                <th>其它费用</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in collectCtrl.finished.cmmList"
                                ng-dblclick="collectCtrl.seeCollectMuchMoney($index)">
                                <td><a ng-click="collectCtrl.seeCollectMuchMoney($index)">{{row.customer.no}}</a></td>
                                <td ng-bind="row.customer.shortName"></td>
                                <td ng-if="row.fundType == '99999913'"> <span
									class="label label-sm label-success">预收款</span></td>
								<td ng-if="row.fundType == '********'"><span
									class="label label-sm label-danger">应收款</span></td>
								<td ng-if="row.fundType != '99999913' && row.fundType != '********'"><span
									class="label label-sm label-default">状态异常 </span></td>
                                <td class="text-right" ng-bind="row.amount  | currency:'':2"></td>
                                <td ng-bind="row.collectedDate"></td>
                                <td ng-bind="row.period"></td>
                                <td ng-bind="row.createdDate"></td>
                                <td ng-bind="row.responsiblePerson.userName"></td>
                                <td ng-if="row.status === 99999901">
	                                <span class="label label-sm label-default">未冲红</span>
	                            </td>
	                            <td ng-if="row.status === 99999902">
	                                <span class="label label-sm label-success">冲红单 </span>
	                            </td>
	                            <td ng-if="row.status === 99999903">
	                                <span class="label label-sm label-danger">已冲红</span>
	                            </td>
	                            <td ng-bind="row.changeFee | currency:'':2"></td>
	                            <td ng-bind="row.otherFee | currency:'':2"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- 详情列表 -->
    </tab>
	<!-- END 收款记录详情 -->

    <!-- BEGIN 多月收款记录编辑 -->
	<tab active="collectCtrl.tabs.editMuchForm.active" ng-show="collectCtrl.tabs.editMuchForm.show">
	        <tab-heading>收款记录详情 <i class="fa fa-times set-cursor-pointer" ng-click="collectCtrl.hideMuchEditForm()"></i></tab-heading>
	        <div class="rows" ng-if="collectCtrl.right.edit">
	            <div class="portlet light bordered">
	                <div class="portlet-title">
	                    <div class="caption font-blue-hoki">收款记录{{collectCtrl.editTitle}}</div>
	                </div>
	                <div class="portlet-body">
	                    <form class="form-horizontal" name="much_collectedMoneyForm" novalidate="novalidate"
	                          ng-submit="collectCtrl.submitCollectMuchMoneyFormMod(much_collectedMoneyForm);"
	                          ng-init="collectCtrl.setFormMuchScope(this)" ng-submit-force="true">
	                        <div class="row">
	                            <div class="col-md-6 col-lg-4">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>单据编号：</label>
	
	                                    <div class="col-sm-7 col-md-8">
	                                        <input required type="text" class="form-control"
	                                               ng-model="collectCtrl.finished.billNo" ng-disabled="true"/>
	                                    </div>
	                                </div>
	                            </div>
	                            <div class="col-md-6 col-lg-4">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>客户编码：</label>
	
	                                    <div class="col-sm-7 col-md-8">
	                                        <ui-select ng-model="collectCtrl.finished.customer" theme="bootstrap"
	                                                   register-custom-form-control ng-disabled="!collectCtrl.right.edit"
	                                                   on-select="collectCtrl.selectCustomers($select.selected.recordId)"
	                                                   firstfocus="{{collectCtrl.focus.collect}}" required>
	                                            <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
	                                            <ui-select-choices
	                                                    repeat="item in collectCtrl.refer.customerList | filter: $select.search | limitTo: collectCtrl.infiniteScroll.currentItems track by $index"
	                                                    infinite-scroll="collectCtrl.addMoreItems()"
										    			infinite-scroll-distance="2">
	                                                <div ng-bind-html="item.no | highlight: $select.search"></div>
	                                                <small>
	                                                    <span style="color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.shortName}}<br></span>
	                                                </small>
	                                            </ui-select-choices>
	                                        </ui-select>
	                                    </div>
	                                </div>
	                            </div>
	                            <div class="col-md-6 col-lg-4">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label">客户名称：</label>
	
	                                    <div class="col-sm-7 col-md-8">
	                                            <span class="form-control" disabled
	                                                  ng-bind="collectCtrl.finished.customer.shortName"></span>
	                                    </div>
	                                </div>
	                            </div>
	                            <div class="col-md-6 col-lg-4">
	                                <div id="infoStep2" class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>款项类别：</label>
	
	                                    <div class="col-sm-7 col-md-8">
	                                        <ui-select ng-model="collectCtrl.finished.fundType" theme="bootstrap"
	                                        			register-custom-form-control on-select="collectCtrl.selectMuchFundType()" required>
	                                            <ui-select-match placeholder="请选择...">{{$select.selected.name}}
	                                            </ui-select-match>
	                                            <ui-select-choices
	                                                    repeat="item.value as item in collectCtrl.refer.fundTypeList">
	                                                <div ng-bind-html="item.name"></div>
	                                            </ui-select-choices>
	                                        </ui-select>
	                                    </div>
	                                </div>
	                            </div>
	                            <div class="col-md-6 col-lg-4">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label"><span class="required" ng-show="collectCtrl.edit.selFtFlag">*</span>合同编号：</label>
	
	                                    <div class="col-sm-7 col-md-8">
	                                        <ui-select ng-disabled="collectCtrl.edit.contract" register-custom-form-control
	                                                   ng-model="collectCtrl.finished.contract" theme="bootstrap"
	                                                   ng-required="collectCtrl.edit.selFtFlag">
	                                            <ui-select-match placeholder="请选择...">{{$select.selected.no}}
	                                            </ui-select-match>
	                                            <ui-select-choices
	                                                    repeat="item in collectCtrl.contracts | filter: $select.search">
	                                                <div ng-bind-html="item.no | highlight: $select.search"></div>
	                                            </ui-select-choices>
	                                        </ui-select>
	                                    </div>
	                                </div>
	                            </div>
	                            <div class="col-md-6 col-lg-4">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label">合同金额：</label>
	                                    <div class="col-sm-7 col-md-8">
	                                       <span class="form-control" disabled
	                                             ng-bind="collectCtrl.finished.contract.totalAmt | currency:'':2"></span>
	                                    </div>
	                                </div>
	                            </div>
	                            <div class="col-md-6 col-lg-4">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>收款方式：</label>
	
	                                    <div class="col-sm-7 col-md-8">
	                                        <ui-select required ng-model="collectCtrl.finished.collectWay"
	                                                 register-custom-form-control  theme="bootstrap" required>
	                                            <ui-select-match
	                                                    placeholder="请选择...">{{$select.selected.value}}
	                                            </ui-select-match>
	                                            <ui-select-choices
	                                                    repeat="item.recordId as item in collectCtrl.refer.collectWayList | filter: $select.search">
	                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
	                                            </ui-select-choices>
	                                        </ui-select>
	                                    </div>
	                                </div>
                            	</div>
	                            <div class="col-md-6 col-lg-4">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>总收款金额：</label>
	                                    <div class="col-sm-7 col-md-8">
	                                        <input ng-if="collectCtrl.rcvAmount" type="text" class="form-control ng-valid ng-touched" placeholder="当前总未结清金额{{collectCtrl.rcvAmount}}元"
	                                               ng-model="collectCtrl.finished.amount" ng-blur="collectCtrl.validTotalAmount()" ng-checkmoneymoredragon required/>
	                                        <input ng-if="!collectCtrl.rcvAmount" type="text" class="form-control ng-valid ng-touched"
	                                               ng-model="collectCtrl.finished.amount" ng-blur="collectCtrl.validTotalAmount()" ng-checkmoneymoredragon required/>    
	                                    </div>
	                                </div>
                           		 </div>
                           		 <div class="col-md-6 col-lg-4">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>收款日期：</label>
	                                    <div class="col-sm-7 col-md-8">
		                                    <input type="text" class="form-control ng-valid ng-touched" ng-model="collectCtrl.finished.collectedDate"
				                            data-date-format="yyyy-MM-dd"
				                            data-date-type="number"
				                            data-min-date="02/10/1901"
				                            data-max-date="today"
				                            data-autoclose="1"
				                            daysOfWeekDisabled="false"
				                            bs-datepicker required/>
	                                    </div>
	                                </div>
                            	</div>
                            	<div class="col-md-6 col-lg-4">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label">收款银行：</label>
	
	                                    <div class="col-sm-7 col-md-8">
	                                        <input type="text" class="form-control ng-valid ng-touched"
	                                        		typeahead="state.bank for state in collectCtrl.refer.receiveAccounts | filter:$viewValue | limitTo:8"
	                                               ng-model="collectCtrl.finished.bank" ng-maxlength="50">       
	                                    </div>
	                                </div>
                           	 	</div>
	                        	<div class="col-md-6 col-lg-4">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label">收款账号：</label>
	
	                                    <div class="col-sm-7 col-md-8">
	                                        <input type="text" class="form-control ng-valid ng-touched" 
	                                        	   typeahead="state.receiveAccount for state in collectCtrl.refer.receiveAccounts | filter:$viewValue | limitTo:8"
	                                               ng-model="collectCtrl.finished.receiveAccount" ng-Banknumber>       
	                                    </div>
	                                </div>
	                            </div>
	                            <div class="col-md-6 col-lg-4">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>经办人：</label>
	
	                                    <div class="col-sm-7 col-md-8">
	                                        <ui-select required ng-model="collectCtrl.finished.responsiblePerson"
	                                                 register-custom-form-control  theme="bootstrap">
	                                            <ui-select-match
	                                                    placeholder="请选择...">{{$select.selected.userName}}
	                                            </ui-select-match>
	                                            <ui-select-choices
	                                                    repeat="item in collectCtrl.refer.employeeList | filter: $select.search">
	                                                <div ng-bind-html="item.userName | highlight: $select.search"></div>
	                                            </ui-select-choices>
	                                        </ui-select>
	                                    </div>
	                                </div>
	                            </div>
       	                      <!-- 多月收款页面  start-->
		                      <div class="row form-group" style="margin-bottom: 15px;" ng-if="collectCtrl.collectMoneyList && collectCtrl.collectMoneyList.length > 0">
		                     	<div class="col-md-12">
	                            	<table class="table table-bordered table-condensed">
	                            		<thead>
	                            			<tr class="heading">
	                            				<th width="10%">序号</th>
	                            				<th width="10%"><span class="required text-danger">*</span>收款金额</th>
	                            				<th width="10%">手续费</th>
	                            				<th width="10%">其它费用</th>
	                            				<th width="10%"><span class="required text-danger">*</span>收款账期</th>
	                            				<th width="15%">操作</th>
	                            			</tr>
	                            		</thead>
	                            		<tbody>
	                            			<tr ng-repeat="row in collectCtrl.collectMoneyList">
	                            				<td style='vertical-align: middle;'>{{$index + 1}}</td>
	                            				<!-- 收款金额 -->
	                            				<td style='vertical-align: middle;'>
	                            					<input class="form-control" type="text" ng-model="row.amount" ng-blur="collectCtrl.validBatchAmount($index)" ng-maxlength="50" ng-checkmoneymoredragon/>
	                            				</td>
	                            				<!-- 手续费 -->
	                            				<td style='vertical-align: middle;'>
	                                        		<input type="text" class="form-control ng-valid ng-touched" ng-model="row.changeFee" ng-blur="collectCtrl.validAmount($index)" ng-checkmoneymoredragon />       
	                            				</td>
             					                <!-- 其它费用 -->
	                            				<td style='vertical-align: middle;'>
	                                        		<input type="text" class="form-control ng-valid ng-touched" ng-model="row.otherFee" ng-blur="collectCtrl.validOtherFee($index)" ng-checkmoneymoredragon />       
	                            				</td>
	                            				<!-- 收款账期  -->
	                            				<td style='vertical-align: middle;'>
		                                        	<select  class="form-control"
			                                             ng-change="collectCtrl.validateBatchAble($index)"
			                                             ng-model="row.singleReceivableId"
			                                             ng-options="item.recordId as item.concat for item in collectCtrl.customerPeriodList" required>
				                                        <option value="">--请选择--</option>
			                                        </select>
	                            				</td>
	                            				<td style='vertical-align: middle;'">
	                            					<button type="button" class="btn btn-success btn-xs" ng-click="collectCtrl.addBatch()" ng-if="$index == collectCtrl.collectMoneyList.length - 1">
	                                             		<i class="fa fa-plus"></i> 添加
	                                         		</button>
	                            					<button type="button" class="btn btn-danger btn-xs" ng-click=" collectCtrl.deleteBatch($index)" ng-if="$index > 0">
	                                             		<i class="fa fa-trash-o"></i> 删除
	                                         		</button>
	                            				</td>
	                            			</tr>
	                            		</tbody>
	                            	</table>
		                         </div>
		                     </div>
		                     <!-- 多月收款页面 end -->
	                        </div>
	                        <div class="row">
	                            <div class="col-md-8 col-lg-8">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>
	
	                                    <div class="col-sm-7 col-md-10">
	                                            <textarea class="form-control"
	                                                      ng-model="collectCtrl.finished.remark"
	                                                      ng-maxlength="255"></textarea>            
	                                    </div>
	                                </div>
	                            </div>
	                        </div>
	                        <div class="row">
	                            <div class="form-group">
	                                <div class="col-sm-offset-4 col-sm-10">
	                                    <button id="infoStep3" type="submit" class="btn btn-primary btn-default-width"
	                                            style="margin-left:15px;"><i class="fa fa-save"></i> 保&nbsp;存
	                                    </button>
	                                    <!-- <button type="button" class="btn default ng-scope"
	                                            ng-click="collectCtrl.cacelBtn(collectedMoneyForm)">取消
	                                    </button> -->
	                                </div>
	                            </div>
	                        </div>
	                    </form>
	                </div>
	            </div>
	        </div>
	    </tab>
	<!-- END  多月收款记录编辑-->
	
	<!--BEGIN 分月收款记录详情 -->
    <tab active="collectCtrl.tabs.seeMuchDetail.active" ng-show="collectCtrl.tabs.seeMuchDetail.show">
        <tab-heading>分月收款管理详情<i class="fa fa-times set-cursor-pointer" ng-click="collectCtrl.hideSeeMuchDetail()"></i></tab-heading>
        <div class="rows">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">分月收款管理{{collectCtrl.editTitle}}</div>
                </div>
                <div class="portlet-body">
                    <form class="form-horizontal" ng-submit-force="true">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>单据编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-disabled="true"
                                               ng-model="collectCtrl.collectMuchMoney.billNo"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>客户编码：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-disabled="true"
                                               ng-model="collectCtrl.collectMuchMoney.customer.no"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">客户名称：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" disabled
                                                  ng-bind="collectCtrl.collectMuchMoney.customer.shortName"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>款项类别：</label>
                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="collectCtrl.collectMuchMoney.fundType | clFundTypeFilter"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>合同编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-disabled="true"
                                               ng-model="collectCtrl.collectMuchMoney.contract.no"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">合同金额：</label>
                                    <div class="col-sm-7 col-md-8">
                                       <span class="form-control" disabled
                                             ng-bind="collectCtrl.collectMuchMoney.contract.totalAmt | currency:'':2"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>收款方式：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-disabled="true"
                                               ng-model="collectCtrl.collectWay.value"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>收款金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" disabled
                                             ng-bind="collectCtrl.collectMuchMoney.amount | currency:'':2"></span>       
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>收款日期：</label>
                                    <div class="col-sm-7 col-md-8">
										<span class="form-control" disabled ng-bind="collectCtrl.collectMuchMoney.collectedDate">
                                    </div>
                                </div>
                            </div>
                        	<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">收款银行：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control ng-valid ng-touched"
                                               ng-disabled="true" ng-model="collectCtrl.collectMuchMoney.bank">
                                    </div>
                                </div>
                            </div>
                        	<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">收款账号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control ng-valid ng-touched"
                                               ng-disabled="true" ng-model="collectCtrl.collectMuchMoney.receiveAccount">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>经办人：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-disabled="true"
                                               ng-model="collectCtrl.collectMuchMoney.responsiblePerson.userName"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">手续费：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" disabled
                                             ng-bind="collectCtrl.collectMuchMoney.changeFee | currency:'':2"></span>       
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">其它费用：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" disabled
                                             ng-bind="collectCtrl.collectMuchMoney.otherFee | currency:'':2"></span>       
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                        <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>对账日期：</label>
                                     <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control ng-valid ng-touched"
                                        	   ng-disabled="true"
                                               ng-model="collectCtrl.collectMuchMoney.period" ng-maxlength="50">       
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
	                            <div class="form-group">
									<label class="col-sm-3 col-md-4 control-label">收款账期：</label>
									<div class="col-sm-7 col-md-8">
										<ui-select ng-model="collectCtrl.collectMuchMoney.singleReceivableId"
												   theme="bootstrap" ng-disabled="true">
											<ui-select-match placeholder="请选择...">{{$select.selected.no +'-'+ $select.selected.period + "-" + collectCtrl.collectMuchMoney.amount + "元"}}
											</ui-select-match>
											<ui-select-choices repeat="item.recordId as item in collectCtrl.customerPeriodList | filter: $select.search">
													<div ng-bind-html="item.no + '-' + item.period +'-'+ collectCtrl.collectMuchMoney.amount + '元' | highlight: $select.search"></div>
											</ui-select-choices>
										</ui-select>
									</div>
								</div>
                            </div>
                        </div>
                        <div class="col-md-12 col-lg-8">
                             <div class="form-group">
                                 <label class="col-sm-3 col-md-2 control-label">备注：</label>

                                 <div class="col-sm-7 col-md-10">
                                        <textarea class="form-control" ng-disabled="true"
                                                   ng-model="collectCtrl.collectMuchMoney.remark"
                                                   maxlength="255"></textarea>           
                                 </div>
                             </div>
                         </div>
                   		<!-- 应收明细列表  start-->
						 <div class="row form-group" style="margin-bottom: 15px;" ng-if="collectCtrl.singleDetailCollectList && collectCtrl.singleDetailCollectList.length > 0">
							<div class="col-md-12">
								<table class="table table-bordered table-condensed">
									<thead>
										<tr class="heading">
											<th>类型</th>
											<th>客户订单号</th>
											<th>规格型号</th>	
											<th>已收金额</th>
										</tr>
									</thead>
									<tbody ng-repeat="row in collectCtrl.singleDetailCollectList">
										<tr>
											<th ng-if="row.type != 4 && row.type != 5">{{row.typeStr}}</th>
											<th ng-if="!(row.type != 4 && row.type != 5)" class="success">{{row.typeStr}}</th>
											<td ng-bind="row.customerPo"></td>
											<td ng-bind="row.specification"></td>
											<td ng-bind="row.amount"></td>
										</tr>
									</tbody>
								</table>
							 </div>
						 </div>
						 <!-- 应收明细列表 end --> 
                        <div class="row" ng-if="collectCtrl.finished.status != collectCtrl.rushRedStatus.unRush.flagId">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>冲红原因：</label>
                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-model="collectCtrl.collectMuchMoney.writeOffCause"
                                                      ng-disabled="true" ng-maxlength="50" required></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row"ng-if="collectCtrl.edit.isRushRed  && collectCtrl.finished.status == collectCtrl.rushRedStatus.unRush.flagId">
                            <div class="col-md-12 col-lg-8">
                                <div id="infoRedStep1" class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">冲红原因：<span class="required">*</span></label>
                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-model="collectCtrl.collectMuchMoney.writeOffCause"
                                                      firstfocus="{{collectCtrl.focus.writered}}" ng-maxlength="50" required></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
              </div>
        </div>
    </tab>
    <!--END 分月收款记录详情 -->

<%--    <tab heading="收款导入记录" active="collectCtrl.tabs.viewExportForm.active" ng-click="collectCtrl.getCollectMoneyData()">
        <div class="rows">
            <div class="panel panel-default">
                <div class="panel-heading font-blue-hoki">查询</div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">导入时间：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <div class="input-prepend input-group">
                                            <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                            <input type="text" class="form-control" disable-auto-validate="true"
                                                   ng-blur="collectCtrl.initDateTwo(collectCtrl.timeTwo)"
                                                   kyb-daterange
                                                   kyb-daterange-options="collectCtrl.rangeOptions"
                                                   ng-model="collectCtrl.timeTwo"
                                                   placeholder="请选择时间段">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default btn-default-width pull-right"
                                        ng-click="collectCtrl.getCollectMoneyData()"><i class="fa fa-search"></i> 查询
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">收款导入记录列表</div>
                    <div class="actions">
                        <div class="row">
                            <div class="col-md-12 text-right">
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="collectCtrl.importCollectMoneyData()" ng-if="collectCtrl.right.edit">
                                    <i class="fa fa-adjust font-blue"></i>收款记录导入
                                </a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="collectCtrl.editCollectMoneyData()" ng-if="collectCtrl.right.edit">
                                    <i class="fa fa-edit font-blue"></i>批量编辑
                                </a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="collectCtrl.saveCollectMoneyData()" ng-if="collectCtrl.right.edit">
                                    <i class="fa fa-sign-in font-green"></i>批量保存
                                </a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="collectCtrl.deleteCollectMoneyData()" ng-if="collectCtrl.right.edit">
                                    <i class="fa fa-times font-red"></i>批量删除
                                </a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="collectCtrl.statusConfirm()" ng-if="collectCtrl.right.edit">
                                    <i class="fa fa-sign-out font-red"></i>批量确认
                                </a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="collectCtrl.statusUnConfirm()" ng-if="collectCtrl.right.edit">
                                    <i class="fa fa-sign-out font-red"></i>批量取消确认
                                </a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="collectCtrl.sentData()" ng-if="collectCtrl.right.edit">
                                    <i class="fa fa-sign-out font-red"></i>批量生成
                                </a>
                            </div>
                        </div>
                        <div class="portlet-input input-inline input-small">
                            <button class="btn green blue-hoki btn-default-width" ng-click="collectCtrl.importCollectMoneyData()" ><i class="fa fa-upload"></i>收款记录导入</button>
                        </div>
                     </div>
                <div class="portlet-body">
                    <div class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading">
                                <th>
                                    <checkbox ng-model="collectCtrl.allPageChecked" value="1" ng-click="collectCtrl.selectAllPage()"></checkbox>
                                </th>
                                <th>客户名称</th>
                                <th>款项类型</th>
                                <th>收款日期</th>
                                <th>货币类型</th>
                                <th>收款方式</th>
                                <th>收款金额</th>
                                <th>收款银行</th>
                                <th>收款账号</th>
                                <th>收款账期</th>
                                <th>收款组织</th>
                                <th>状态</th>
                                <th>创建人</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                            <tr class="heading">
                                <td></td>
                                <td>
                                    <input type="text" class="form-control" ng-model="collectCtrl.customerNameQuery">
                                </td>
                                <td>
                                    <input type="text" class="form-control" ng-model="collectCtrl.fundTypeQuery">
                                </td>
                                <td>
                                    <input type="text" class="form-control" ng-model="collectCtrl.collectedDateQuery">
                                </td>
                                <td>
                                    <input type="text" class="form-control" ng-model="collectCtrl.currencyTypeQuery">
                                </td>
                                <td>
                                    <input type="text" class="form-control" ng-model="collectCtrl.collectWayQuery">
                                </td>
                                <td>
                                    <input type="text" class="form-control" ng-model="collectCtrl.amountQuery">
                                </td>
                                <td>
                                    <input type="text" class="form-control" ng-model="collectCtrl.bankQuery">
                                </td>
                                <td>
                                    <input type="text" class="form-control" ng-model="collectCtrl.receiveAccountQuery">
                                </td>
                                <td>
                                    <input type="text" class="form-control" ng-model="collectCtrl.periodQuery">
                                </td>
                                <td></td>
                                <td>
                                    <input type="text" class="form-control" ng-model="collectCtrl.statusStrQuery">
                                </td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in collectCtrl.collectMoneyDataList">
                                <td>
                                    <checkbox ng-model="row.checked" value="1" ng-click="collectCtrl.getRecordList()"></checkbox>
                                </td>
                                <td>
                                    <span ng-if="!row.editFlag">{{row.customerName}}</span>
                                    <input ng-if="row.editFlag" ng-model="row.customerName"/>
                                </td>
                                <td>
                                    <span ng-if="!row.editFlag">{{row.fundType}}</span>
                                    <input ng-if="row.editFlag" ng-model="row.fundType"/>
                                </td>
                                <td>
                                    <span ng-if="!row.editFlag">{{row.collectedDate}}</span>
                                    <input ng-if="row.editFlag" ng-model="row.collectedDate"/>
                                </td>
                                <td>
                                    <span ng-if="!row.editFlag">{{row.currencyType}}</span>
                                    <input ng-if="row.editFlag" ng-model="row.currencyType"/>
                                </td>
                                <td>
                                    <span ng-if="!row.editFlag">{{row.collectWay}}</span>
                                    <input ng-if="row.editFlag" ng-model="row.collectWay"/>
                                </td>
                                <td>
                                    <span ng-if="!row.editFlag">{{row.amount}}</span>
                                    <input ng-if="row.editFlag" ng-model="row.amount"/>
                                </td>
                                <td>
                                    <span ng-if="!row.editFlag">{{row.bank}}</span>
                                    <input ng-if="row.editFlag" ng-model="row.bank"/>
                                </td>
                                <td>
                                    <span ng-if="!row.editFlag">{{row.receiveAccount}}</span>
                                    <input ng-if="row.editFlag" ng-model="row.receiveAccount"/>
                                </td>
                                <td>
                                    <span ng-if="!row.editFlag">{{row.period}}</span>
                                    <input ng-if="row.editFlag" ng-model="row.period"/>
                                </td>
                                <td>
                                    <span ng-if="!row.editFlag">{{row.companyName}}</span>
                                    <input ng-if="row.editFlag" ng-model="row.companyName"/>
                                </td>
                                <td ng-bind="row.statusStr"></td>
                                <td ng-bind="row.createdBy.userName"></td>
                                <td ng-bind="row.createdDate"></td>
                                <td>

                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </tab>--%>

</tabset>

<div id="exportData" class="modal fade" tabindex="-1" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">数据引入</h4>
            </div>
            <div class="modal-body clearfix">
                <div class="row">
                    <div class="col-md-12">
                        <button class="btn btn-secondary">引入模式</button>
                    </div>
                </div>
                <div class="row portlet light bordered">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-4">
                                        <input type="radio" disabled/>追加
                                    </div>
                                    <div class="col-md-4">
                                        <input type="checkbox" disabled>仅暂存
                                    </div>
                                    <div class="col-md-4">
                                        <input type="checkbox" disabled>暂存引入失败数据
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="padding-top: 1rem;">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-4">
                                        <input type="radio" checked disabled/>覆盖
                                    </div>
                                    <div class="col-md-4">
                                        <input type="checkbox" disabled>运行追加
                                    </div>
                                    <div class="col-md-4">
                                        <input type="checkbox" checked disabled>匹配字段
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        请选择要引入的数据文件
                        <form name="form1" enctype="multipart/form-data" id="form1" ng-submit="collectCtrl.uploadExcel()" style="padding-top: 0.5rem;">
                            <div class="row" style="padding-bottom: 1rem;">
                                <div class="col-md-12">
                                    <span class="btn btn-default" style="position: relative;display: inline-block;overflow: hidden;">
                                        <input id="upfile" name="upfile" type="file"/>
                                    </span>
                                </div>
                            </div>
                        </form>
                        <div class="row portlet light bordered">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-9">
                                        说明：数据文件需符合模板要求，若无模板请下载
                                    </div>
                                    <div class="col-md-3">
                                        <div class="portlet-input input-inline input-small">
                                            <form action="a/crm/uploadSetting/export"
                                                  method="post" enctype="multipart/form-data" target="hidden_frame">
                                                <input type="text" ng-show="false" name="orderType"
                                                       value="5" />
                                                <button type="submit"  style="border: none;background-color: #fff;color: dodgerblue;">
                                                    <i class="fa fa-download"></i> 引入模板
                                                </button>
                                                <iframe name="hidden_frame" id="hidden_frame"
                                                        style="display: none"></iframe>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12" style="padding-top: 1rem;">
                                <div class="row">
                                    <div class="col-md-9">
                                        &emsp;&emsp;&emsp;引入失败的数据，可下载修复后重新引入
                                    </div>
                                    <div class="col-md-3">
                                        <form id="exportForm" action="a/finance/collect/failCollectMoneyDataExport" method="POST" enctype="multipart/form-data" target="hidden_frame">
                                            <input type="text" ng-show="false" name="type" value="1"/>
                                            <div ng-if="collectCtrl.right.view" >
                                                <button type="submit" style="border: none;background-color: #fff;color: dodgerblue;"><i class="fa fa-download"></i> 失败数据</button>
                                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12" style="padding-top: 1rem;">
                                <div class="row">
                                    <div class="col-md-9">
                                        &emsp;&emsp;&emsp;引入成功的数据，可下载查看
                                    </div>
                                    <div class="col-md-3">
                                        <form action="a/finance/collect/failCollectMoneyDataExport" method="POST" enctype="multipart/form-data" target="hidden_frame">
                                            <input type="text" ng-show="false" name="type" value="2"/>
                                            <div ng-if="collectCtrl.right.view" >
                                                <button type="submit" style="border: none;background-color: #fff;color: dodgerblue;"><i class="fa fa-download"></i> 成功数据</button>
                                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 text-right">
                                <button type="button" class="btn btn-default" ng-click="collectCtrl.uploadExcel();">引入数据</button>
                                <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                            </div>
                        </div>
                        <div class="row" style="padding-top: 1rem;" ng-if="collectCtrl.processFlag && collectCtrl.processFlag == '1'">
                            <div class="col-md-12">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" ng-style="{ 'width': collectCtrl.processNum + '%' }">
                                        <span class="sr-only">{{collectCtrl.processNum}}%</span>
                                    </div>
                                </div>
                                <div>
                                    <span>{{collectCtrl.dataNowMsg==""?"数据处理中":collectCtrl.dataNowMsg}}...{{collectCtrl.dataNowNum}}/{{collectCtrl.dataSumNum}}</span>
                                </div>
                            </div>
                        </div>
                        <div style="height: 50px; padding-top: 1rem;">
                            <span class="text-danger">{{collectCtrl.exportMessage}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="downStatic" class="modal fade" tabindex="-1" data-keyboard="false" data-backdrop="static">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">导入数据提示</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;" ng-bind="collectCtrl.exportMessage"></div>
                    <div class="modal-footer">
                        <form action="a/finance/collect/failCollectMoneyDataExport" method="POST" enctype="multipart/form-data" target="hidden_frame">
                            <input type="text" ng-show="false" name="type" value="1"/>
                            <div ng-if="ctrl.right.view" >
                                <button type="submit" class="btn btn-default" ng-click="collectCtrl.confimExcel()" ng-if="collectCtrl.needDownErrorFile">确认</button>
                                <button type="button" class="btn btn-default" ng-click="collectCtrl.confimExcel()" ng-if="!collectCtrl.needDownErrorFile">确认</button>
                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div id="static" class="modal fade" tabindex="-1"
         data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-hidden="true"></button>
                    <h4 class="modal-title">
                        提示
                    </h4>
                </div>
                <div class="modal-body">
                    <p>
                        <span ng-bind="collectCtrl.message"></span>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                </div>
            </div>
        </div>
    </div>
</div>
