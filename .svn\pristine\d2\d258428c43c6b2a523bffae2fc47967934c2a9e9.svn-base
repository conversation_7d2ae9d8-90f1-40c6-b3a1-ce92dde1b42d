package com.kyb.pcberp.modules.purch.web;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.kyb.pcberp.modules.approval.utils.ModifyUtils;
import com.kyb.pcberp.modules.approval.utils.QueryApprovalUtils;
import com.kyb.pcberp.modules.sys.entity.DictItem;
import com.kyb.pcberp.modules.wechat.entity.IcloudCompany;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.google.common.collect.Maps;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.enums.CommonEnums.DictItemEnum;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.CommonUtils;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import com.kyb.pcberp.modules.purch.entity.SupplierFile;
import com.kyb.pcberp.modules.purch.service.SupplierService;
import com.kyb.pcberp.modules.sys.entity.Area;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.service.CompanyService;
import com.kyb.pcberp.modules.sys.service.SystemService;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

@Controller
@RequestMapping(value = "${adminPath}/purch/supplier")
public class SupplierController extends BaseController
{
    
    @Autowired
    private SupplierService supplierService;
    
    @Autowired
    private SystemService systemService;
    
    @Autowired
    private CompanyService companyService;
    
    // @Autowired
    // private ParentMessageService parentMessageService;
    
    @RequestMapping(value = "list")
    public String list(Supplier supplier)
    {
        return "modules/purch/supplier";
    }
    
    @RequiresPermissions(value = {"purch:supplier:view"})
    @RequestMapping(value = {"load/data"})
    @ResponseBody
    public Map<String, Object> loadData(Boolean queryAll)
    {
        Map<String, Object> maps = Maps.newHashMap();
        
        // 加载结算方式
        maps.put("payWays", DictUtils.getValuesByItem(DictItemEnum.PAY_WAYS));
        // 送货方式
        maps.put("deliveryWays", DictUtils.getValuesByItem(DictItemEnum.DELIVERY_WAYS));
        // 交货方式
        maps.put("freightWays", DictUtils.getValuesByItem(DictItemEnum.FREIGHT_WAYS));
        // 税类说明
        maps.put("taxDescriptList", DictUtils.getValuesByItem(DictItemEnum.TAX_DESCRIPTS));
        // 保质期限
        maps.put("assureDaysList", DictUtils.getValuesByItem(DictItemEnum.ASSURE_DAYS));
        // 省份列表
        maps.put("provinceList", UserUtils.getAreaListByLevel("1"));
        // 供应商等级
        maps.put("supplierlevel", DictUtils.getValuesByItem(DictItemEnum.SUPPLIERLEVEL));
        // 供应商类型
        maps.put("suppliertype", DictUtils.getValuesByItem(DictItemEnum.SUPPLIERTYPE));
        // 付款方式
        maps.put("paycauseList", DictUtils.getValuesByItem(DictItemEnum.PAY_CAUSE));
        // 行业
        maps.put("professionList", DictUtils.getValuesByItem(DictItemEnum.INDUSTRYINVOLVED));
        // 供应商分类
        maps.put("supplierClassifysList", DictUtils.getValuesByItem(DictItemEnum.SUPPLIERCLASSIFY));
        // 结算货币
        maps.put("currencyTypeList", DictUtils.getValuesByItem(DictItemEnum.CURRENCY_TYPE));
        // 权限
        maps.put("view", true);
        maps.put("edit", SecurityUtils.getSubject().isPermitted("purch:supplier:edit"));
        maps.put("manage", SecurityUtils.getSubject().isPermitted("purch:supplier:manage"));
        
        // zjn 2019-12-31 是否设置供应商资料审批配置
        boolean approvalFlag = ModifyUtils.getAllocation("15");
        maps.put("approvalFlag", approvalFlag);
        
        User user = UserUtils.getUser();
        user.setCreatedBy(null);
        if (null != queryAll && !queryAll)
        {
            user.setCreatedBy(user.clone());
        }
        List<User> userList = systemService.findAllUser(user);
        maps.put("userList", userList);
        
        List<Company> companyList = companyService.getUseCompanyList();
        maps.put("companyList", companyList);

        // 指定品牌
        List<DictItem> brandList = systemService.getBrandList();
        maps.put("brandList", brandList);

        // 指定生态圈
        List<IcloudCompany> ecosphereList = systemService.getEcosphereList();
        maps.put("ecosphereList", ecosphereList);
        return maps;
    }
    
    @RequiresPermissions(value = {"purch:supplier:view"})
    @RequestMapping(value = {"page"})
    @ResponseBody
    public Page<Supplier> getlist(@RequestBody Supplier supplier, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        supplier.setCompany(user.getCompany());
        
        // 分页查询数据
        Page<Supplier> qpage = new Page<Supplier>(request, response);
        if (StringUtils.isNotBlank(supplier.getPageNo()) && StringUtils.isNotBlank(supplier.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(supplier.getPageNo()));
            qpage.setPageSize(Integer.parseInt(supplier.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        // 设置排序
        if (StringUtils.isNotBlank(supplier.getOrderBy()))
        {
            qpage.setOrderBy(supplier.getOrderBy());
        }
        // 设置查询范围
        if (supplier.getQueryAll() != null && !supplier.getQueryAll())
        {
            supplier.setCreatedBy(user);
        }
        if (supplier.getSentTimeEndQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(supplier.getSentTimeEndQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            supplier.setSentTimeEndQr(deliTime.getTime());
        }
        if (supplier.getSentTimeStartQr() != null)
        {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(supplier.getSentTimeStartQr());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            supplier.setSentTimeStartQr(deliTime.getTime());
        }
        
        Page<Supplier> page = supplierService.findPage(qpage, supplier);
        
        return page;
    }
    
    /**
     * 增加或修改供应商
     * 
     * @param house
     * @return
     */
    @RequiresPermissions("purch:supplier:edit")
    @RequestMapping(value = "save", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> add(@RequestBody Supplier supplier)
    {
        return supplierService.saveSupplier(supplier);
    }
    
    /**
     * 删除供应商
     * 
     * @param id
     * @return
     */
    @RequiresPermissions("purch:supplier:edit")
    @RequestMapping(value = "delete/{id}", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> delete(@PathVariable("id") String id)
    {
        Supplier supplier = supplierService.get(id);
        supplier.setCompany(UserUtils.getUser().getCompany());
        return supplierService.deleteSupplier(supplier);
    }
    
    /**
     * 查看是否有同简称的供应商
     * 
     * @param request
     * @return
     */
    @RequiresPermissions(value = {"purch:supplier:view"})
    @RequestMapping(value = "querySameshortName")
    @ResponseBody
    public int querySameshortName(String recordId, String shortName, HttpServletRequest request)
    {
        Supplier supplier = new Supplier();
        supplier.setCompany(UserUtils.getUser().getCompany());
        supplier.setRecordId(recordId);
        supplier.setShortName(shortName);
        return supplierService.querySameshortName(supplier);
    }
    
    /**
     * 查看是否有同全称的供应商
     * 
     * @param request
     * @return
     */
    @RequiresPermissions(value = {"purch:supplier:view"})
    @RequestMapping(value = "querySameName")
    @ResponseBody
    public int querySameName(String recordId, String name, HttpServletRequest request)
    {
        Supplier supplier = new Supplier();
        supplier.setCompany(UserUtils.getUser().getCompany());
        supplier.setRecordId(recordId);
        supplier.setName(name);
        return supplierService.querySameName(supplier);
    }
    
    /**
     * 查询所有的供应商
     * 
     * @param request
     * @return
     */
    @RequiresPermissions("purch:supplier:view")
    @RequestMapping(value = "queryAllSupplier")
    @ResponseBody
    public List<Supplier> queryAllSupplier(HttpServletRequest request)
    {
        return supplierService.queryAllSupplier(UserUtils.getUser().getCompany());
    }
    
    /**
     * 查找供应商编号
     * 
     * @param request
     * @return
     */
    @RequiresPermissions("purch:supplier:view")
    @RequestMapping(value = "getNo", method = {RequestMethod.POST})
    @ResponseBody
    public String getSupplierNo(HttpServletRequest request)
    {
        return CommonUtils.geDocumentNo(CommonEnums.CodeType.SUPPLIER.getIndex().toString());
    }
    
    @RequiresPermissions("purch:supplier:view")
    @RequestMapping(value = {"areainfo"})
    @ResponseBody
    public Map<String, Area> getAreaInfo(String areaCode)
    {
        return UserUtils.getAreaInfoByAreaCodey(areaCode);
    }
    
    /**
     * 导出
     * 
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("purch:supplier:view")
    @RequestMapping(value = "export")
    public String exportFile(Supplier supplier, HttpServletRequest request, HttpServletResponse response)
    {
        try
        {
            User user = UserUtils.getUser();
            supplier.setCompany(user.getCompany());
            // 设置排序
            Page<Supplier> qpage = null;
            if (StringUtils.isNotBlank(supplier.getOrderBy()))
            {
                qpage = new Page<Supplier>();
                qpage.setOrderBy(supplier.getOrderBy());
                supplier.setPage(qpage);
            }
            // 设置查询范围
            if (supplier.getQueryAll() != null && !supplier.getQueryAll())
            {
                supplier.setCreatedBy(user);
            }
            // 设置时间查询范围
            String inoutTimeStartQr = request.getParameter("sentTimeStartQr");
            String inoutTimeEndQr = request.getParameter("sentTimeEndQr");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeStartQr))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(inoutTimeStartQr)));
                deliTime.set(Calendar.HOUR_OF_DAY, 00);
                deliTime.set(Calendar.MINUTE, 00);
                deliTime.set(Calendar.SECOND, 00);
                deliTime.set(Calendar.MILLISECOND, 00);
                supplier.setSentTimeStartQr(deliTime.getTime());
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(inoutTimeEndQr))
            {
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(new Date(Long.parseLong(inoutTimeEndQr)));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                supplier.setSentTimeEndQr(deliTime.getTime());
            }
            if ("3".equals(supplier.getStatus().toString()))
            {
                supplier.setStatus(null);
            }
            // 查询供应商列表数据
            List<Supplier> supplierList = supplierService.findList(supplier);
            String fileName = "供应商列表" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            new ExportExcel("供应商列表", Supplier.class, new Integer(1)).setDataList(supplierList)
                .write(response, fileName)
                .dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
    }
    
    /**
     * 上传供应商附件
     */
    @RequiresPermissions("purch:supplier:edit")
    @RequestMapping(value = "uploadSupplierFile")
    @ResponseBody
    public Map<String, Object> uploadSupplierFile(@RequestParam("file") MultipartFile file,
        @RequestParam("no") String no, @RequestParam("approvalFlag") Boolean approvalFlag, HttpServletRequest req)
    {
        Map<String, Object> result = Maps.newHashMap();
        Company company = UserUtils.getUser().getCompany();
        if (approvalFlag)
        {
            String resultStr = QueryApprovalUtils.handleAddAttachements(file, no, company, 2, req);
            if ("fail".equals(resultStr))
            {
                // 供应商资料命名 公司id+供应商编号+资料
                String savePath = company.getRecordId() + "/supplier/" + no + "/";

                // 上传文件
                String url = FileManageUtils.uploadFile(file, savePath, req);
                if (url == null)
                {
                    result.put("result", false);
                    return result;
                }

                // 基本路径
                String sourcePath = savePath + file.getOriginalFilename();

                SupplierFile supplierFile = new SupplierFile();
                supplierFile.setNo(no);
                supplierFile.setCompany(company);
                supplierFile.setFileUrl(sourcePath);
                supplierFile.setRealFileName(file.getOriginalFilename());
                supplierFile.setTempUrl(url);
                supplierFile.setType("1");
                supplierService.uploadFile(supplierFile);
                result.put("supplierFile", supplierFile);
                result.put("result", false);
                return result;
            }
        }
        else
        {
            // 供应商资料命名 公司id+供应商编号+资料
            String savePath = company.getRecordId() + "/supplier/" + no + "/";
            
            // 上传文件
            String url = FileManageUtils.uploadFile(file, savePath, req);
            if (url == null)
            {
                result.put("result", false);
                return result;
            }
            
            // 基本路径
            String sourcePath = savePath + file.getOriginalFilename();
            
            SupplierFile supplierFile = new SupplierFile();
            supplierFile.setNo(no);
            supplierFile.setCompany(company);
            supplierFile.setFileUrl(sourcePath);
            supplierFile.setRealFileName(file.getOriginalFilename());
            supplierFile.setTempUrl(url);
            supplierFile.setType("1");
            supplierService.uploadFile(supplierFile);
            result.put("supplierFile", supplierFile);
        }
        result.put("result", true);
        return result;
    }
    
    /**
     * 获取供应商上传的文件
     * 
     * @param request
     * @return
     */
    @RequiresPermissions("purch:supplier:view")
    @RequestMapping(value = "getFile", method = RequestMethod.POST)
    @ResponseBody
    public List<SupplierFile> getFile(@RequestBody Supplier supplier)
    {
        supplier.setCompany(UserUtils.getUser().getCompany());
        return supplierService.getFile(supplier);
    }
    
    /**
     * 移除供应商的附件
     * 
     * @param request
     * @return
     */
    @RequiresPermissions("purch:supplier:view")
    @RequestMapping(value = "clearUpload", method = RequestMethod.POST)
    @ResponseBody
    public String clearUpload(@RequestBody SupplierFile supplierFile)
    {
        return supplierService.clearUpload(supplierFile);
    }
    
    /**
     * lh 取消供应商默认
     * 
     * @param request
     * @return
     */
    @RequiresPermissions("purch:supplier:edit")
    @RequestMapping(value = "cancelIsDefault", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> cancelIsDefault(@RequestBody Supplier supplier)
    {
        return supplierService.cancelIsDefault(supplier);
    }
    
    /**
     * lh 设置供应商默认
     * 
     * @param request
     * @return
     */
    @RequiresPermissions("purch:supplier:edit")
    @RequestMapping(value = "updateIsDefault", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> updateIsDefault(@RequestBody Supplier supplier)
    {
        return supplierService.updateIsDefault(supplier);
    }
    
    // 手动审批
    @RequiresPermissions("purch:supplier:edit")
    @RequestMapping(value = "produceApproval", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> produceApproval(@RequestBody Supplier supplier)
    {
        return supplierService.produceApproval(supplier);
    }
    
    // 保存供应商助理
    @RequiresPermissions("purch:supplier:edit")
    @RequestMapping(value = "saveSupplierUser", method = RequestMethod.POST)
    @ResponseBody
    public String saveSupplierUser(@RequestBody Supplier supplier)
    {
        return supplierService.saveSupplierUser(supplier);
    }
    
    @RequestMapping(value = "saveDistributionSup", method = RequestMethod.POST)
    @ResponseBody
    public String saveDistributionSup(@RequestBody Supplier supplier)
    {
        return supplierService.saveDistributionSup(supplier);
    }
    
    @RequestMapping(value = "batchDistributionSup", method = RequestMethod.POST)
    @ResponseBody
    public String batchDistributionSup(@RequestBody List<Supplier> supplierList)
    {
        return supplierService.batchDistributionSup(supplierList);
    }

    @RequestMapping(value = "registerMacroecology", method = RequestMethod.POST)
    @ResponseBody
    public String registerMacroecology(@RequestBody Supplier supplier)
    {
        return supplierService.registerMacroecology(supplier);
    }

/*    @RequestMapping(value = "checkManufacturer", method = RequestMethod.POST)
    @ResponseBody
    public Integer checkManufacturer(@RequestBody Supplier supplier)
    {
        supplier.setCompany(UserUtils.getUser().getCompany());
        return supplierService.checkManufacturer(supplier);
    }*/

    @RequestMapping(value = "checkIcloudCompany", method = RequestMethod.POST)
    @ResponseBody
    public Integer checkIcloudCompany(@RequestBody Supplier supplier)
    {
        supplier.setCompany(UserUtils.getUser().getCompany());
        return supplierService.checkIcloudCompany(supplier);
    }

    @RequestMapping(value = "bindManufacturer", method = RequestMethod.GET)
    @ResponseBody
    public String bindManufacturer()
    {
        return supplierService.bindManufacturer();
    }

    @RequestMapping(value = "bindIcloudCompany", method = RequestMethod.GET)
    @ResponseBody
    public String bindIcloudCompany()
    {
        return supplierService.bindIcloudCompany();
    }
}
