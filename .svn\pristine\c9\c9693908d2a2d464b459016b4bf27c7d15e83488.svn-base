<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isErrorPage="true"%>
<div style="color:#333">
	<router-view></router-view>
	<div class="pt-7" v-if="fileList && audit.recordId && audit.recordId != ''">
    	<div class="row pt-2 pb-2 alert-light align-items-center border-bottom">
        	<div class="col">附件</div>
        </div>
        <div v-for="(item,index) in fileList" :key="index">
        	<div class="row pt-2 pb-2 align-items-center alert-light border-bottom" v-if="item.name">
            	<div class="col-7">{{item.name}}</div>
               	<div class="col text-right">
                 	<a class="btn btn-sm btn-success" :href="item.preUrl" v-if="item.preUrl">预览</a>
                 	<button class="btn btn-sm btn-success" v-if="item.imgUrl" v-on:click="preview(item.imgUrl)">预览</button>
                 	<a class="btn btn-sm btn-success" :href="item.pdfUrl" v-if="item.pdfUrl">预览</a>
               	</div>
            </div>
        </div>
	</div>
	<div v-if="audit.auditType === '22027' && audit.applyWay === '2' && audit.recordList && audit.recordList.length > 0 && user.position === '保安'
		&& audit.applicationsType !== '3'">
		<div class="row alert-light pt-2 pb-2 align-items-center">
			<div class="col">放行附件</div>
		</div>
		<div v-for="(item,index) in localIdsPass">
			<div class="row alert-light pt-2 pb-2 align-items-center">
				<div class="col">
					<img :src="item" class="w-25" v-on:click="previewTwo(item)">
				</div>
				<div class="col-2">
					<button class="btn btn-sm btn-danger" v-if="audit.applicationsType !== '3'" v-on:click="deleteImg(index)">删除</button>
				</div>
			</div>
		</div>
		<div class="row alert-light pt-2 pb-2 align-items-center">
			<div class="col"><button class="btn btn-primary" v-on:click="chooseImg">上传图片</button></div>
		</div>
	</div>

	<div v-if="fileListTwo && audit.recordId && audit.recordId != '' && audit.auditType === '22027' && audit.applyWay === '2' && audit.applicationsType === '3'">
		<div class="row pt-2 pb-2 alert-light align-items-center border-bottom">
			<div class="col">放行附件</div>
		</div>
		<div v-for="(item,index) in fileListTwo" :key="index">
			<div class="row pt-2 pb-2 align-items-center alert-light border-bottom" v-if="item.name">
				<div class="col-7">{{item.name}}</div>
				<div class="col text-right">
					<a class="btn btn-sm btn-success" :href="item.preUrl" v-if="item.preUrl">预览</a>
					<button class="btn btn-sm btn-success" v-if="item.imgUrl" v-on:click="previewTwo(item.imgUrl)">预览</button>
					<a class="btn btn-sm btn-success" :href="item.pdfUrl" v-if="item.pdfUrl">预览</a>
				</div>
			</div>
		</div>
	</div>
	<div v-if="audit && audit.recordList">
		<div class="row alert-light pt-2 pb-2 align-items-center border-bottom">
			<div class="col-9">评论
				<span v-if="audit.commentList && audit.commentList.length > 0">
					({{audit.recordList.length + audit.commentList.length}})
				</span>
				<span v-else>
					({{audit.recordList.length}})
				</span>
			</div>
			<div class="col text-right">
				<button class="btn btn-sm btn-primary" data-toggle="modal" data-target="#comment">评论</button>
			</div>
		</div>
		<div class="row align-items-center pt-2 pb-2 alert-light border-bottom">
			<div class="col-2">
				<div v-if="audit.employee && audit.employee.downloadUrl">
					<img :src="audit.employee.downloadUrl" class="rounded-circle" style="width: 3rem">
				</div>
			</div>
			<div class="col">
				<div v-if="audit.typeId == '18'">
					<div>确认人:{{audit.reConfirmName}}&nbsp;&nbsp;&nbsp;&nbsp;复核人:{{audit.recordList[0].createByName}}</div>
					<div class="row">
						<div class="col-9">对账时间:{{audit.reConfirmDate}}</div>
					</div>
					<div>复核，提交了审批</div>
				</div>
				<div v-else-if="audit.typeId == '19'">
					<div>确认人:{{audit.apConfirmName}}&nbsp;&nbsp;&nbsp;&nbsp;复核人:{{audit.recordList[0].createByName}}</div>
					<div class="row">
						<div class="col-9">对账时间:{{audit.apConfirmDate}}</div>
					</div>
					<div>复核，提交了审批</div>
				</div>
				<div v-else>
					<div v-if="audit.auditType !== '22026'">{{audit.recordList[0].createByName}}</div>
					<div>提交了审批</div>
					<div>{{audit.reimbursementFor}}</div>
				</div>
				<div class="row">
					<div class="col-9">{{audit.recordList[0].createDate}}</div>
				</div>
			</div>
		</div>
		<div v-for="item in audit.recordList">
			<div class="row align-items-center pt-2 pb-2 alert-light border-bottom" v-if="item.approveUsers">
				<div class="col-2">
					<div v-if="item.employee && item.employee.downloadUrl">
						<img :src="item.employee.downloadUrl" class="rounded-circle" style="width: 3rem">
					</div>
				</div>
				<div class="col-8">
					<div>
						<div v-if="item.status">{{item.approveUsersName}}{{item.handelPositionIdName}}</div>
						<div v-else>待{{item.handelPositionIdName}}审批</div>
					</div>
					<div class="row" v-if="item.content">
						<div class="col">{{item.content}}</div>
					</div>
					<div class="row align-items-center">
						<div class="col-9">
							<div v-if="item.status">
								<div v-if="item.status == 'assent'">同意</div>
								<div v-if="item.status == 'reject'">驳回</div>
							</div>
							<div v-else>
								<div v-if="item.approveUsersName">审批人：{{item.approveUsersName}}</div>
								<div v-else>该职位审批人不存在，请联系相关人员配置该职位的审批人</div>
							</div>
							{{item.lastUpdateDate}}
						</div>
					</div>
				</div>
				<div class="col-2">
					<div>
						<button class="btn btn-sm btn-outline-primary" v-on:click="reply(item)">回复</button>
					</div>
					<div class="pt-2">
						<button class="btn btn-sm btn-outline-primary" v-on:click="close(item)">
							<div v-if="!item.commentFlag">收起</div>
							<div v-else>展开</div>
						</button>
					</div>
				</div>
			</div>
			<div v-for="commen in audit.commentList" v-if="!item.commentFlag">
				<div class="row align-items-center pt-1 pb-1 alert-dark bg-bottom text-secondary border-bottom border-light"
					v-if="commen.auditRecordId && commen.auditRecordId == item.recordId">
					<div class="col-2">
					</div>
					<div class="col-8" style="word-break: break-word">
						<div>
							<span>{{commen.name}}&nbsp;</span>回复
							<span v-if="commen.replyName">{{commen.replyName}}</span>
							<span v-else>
								<span v-if="item.status">{{item.lastUpdateByName}}</span>
								<span v-else>{{item.approveUsersName}}</span>
							</span>
							<span>：&nbsp;{{commen.content}}</span>
						</div>
						<div class="row align-items-center">
							<div class="col-9">{{commen.createDateStr}}</div>
						</div>
					</div>
					<div class="col-2">
						<button class="btn btn-sm btn-outline-secondary" v-on:click="replyCom(item,commen)">回复</button>
					</div>
				</div>
			</div>
		</div>
		<div v-for="item in audit.commentList">
			<div class="row align-items-center pt-2 pb-2 alert-light border-bottom" v-if="!item.auditRecordId && !item.commentId && !item.commentFaId">
				<div class="col-2">
					<div v-if="item.employee && item.employee.downloadUrl">
						<img :src="item.employee.downloadUrl" class="rounded-circle" style="width: 3rem">
					</div>
				</div>
				<div class="col" style="word-break: break-word">
					<div>{{item.name}}&nbsp;评论&nbsp;{{item.content}}</div>
					<div class="row align-items-center">
						<div class="col-9">{{item.createDateStr}}</div>
					</div>
				</div>
				<div class="col-2">
					<div>
						<button class="btn btn-sm btn-outline-primary" v-on:click="replyComment(item)">回复</button>
					</div>
					<div class="pt-2">
						<button class="btn btn-sm btn-outline-primary" v-on:click="close(item)">
							<div v-if="!item.commentFlag">收起</div>
							<div v-else>展开</div>
						</button>
					</div>
				</div>
			</div>
			<div v-for="comme in audit.commentList" v-if="!item.commentFlag">
				<div class="row align-items-center pt-1 pb-1 alert-dark bg-bottom text-secondary border-bottom border-light" v-if="comme.commentFaId == item.recordId">
					<div class="col-2">
					</div>
					<div class="col-8" style="word-break: break-word" >
						<span>
							{{comme.name}}&nbsp;回复&nbsp;
							<span v-if="comme.replyName">{{comme.replyName}}</span>
							<span v-else>{{item.name}}</span>
							&nbsp;{{comme.content}}
						</span>
						<span class="row align-items-center">
							<span class="col-9">{{comme.createDateStr}}</span>
						</span>
					</div>
					<div class="col-2">
						<button class="btn btn-sm btn-outline-secondary" v-on:click="replyComment(item,comme)">回复</button>
					</div>
				</div>
			</div>
		</div>
		<div style="padding-top: 4rem;"></div>
		<div class="row fixed-bottom bg-light pt-2 pb-2" v-if="audit.applicationsType == 2">
			<div class="col-12" v-if="audit.auditFlag  === '1'">
				<div class="row">
					<div class="col" v-if="audit.recordList[audit.recordList.length - 1].handelPositionIdName != '出纳'">
						<button class="btn btn-secondary w-100" data-toggle="modal" data-target="#reject" v-on:click="rejectContent">驳回</button>
					</div>
					<div class="col">
						<button class="btn btn-primary w-100" data-toggle="modal" data-target="#assent" v-on:click="assentContent">通过</button>
					</div>
				</div>
			</div>
			<div class="col-12" v-else-if="audit.draftFlag && audit.draftFlag === '1'">
              <button class="btn btn-primary w-100" v-on:click="draftAudit">撤回成草稿</button>
            </div>
		</div>
		<div class="row fixed-bottom bg-light pt-2 pb-2" v-if="audit.butFalg == 1">
			<div class="col" v-if="audit.recordList[audit.recordList.length - 1].handelPositionIdName != '出纳'">
				<button class="btn btn-secondary w-100" data-toggle="modal" data-target="#erpreject" v-on:click="rejectContent">驳回</button>
			</div>
			<div class="col">
				<button class="btn btn-primary w-100" data-toggle="modal" data-target="#erpassent" v-on:click="assentContent">通过</button>
			</div>
		</div>
	</div>
	<div v-if="audit.empId == emp.recordId && (audit.applicationsType==1 || (audit.applicationsType==3 && audit.applicationsResult=='reject'))">
		<div style="padding-top: 4rem;"></div>
		<div class="row fixed-bottom bg-light pt-2 pb-2">
			<div class="col" v-if="audit.recordList[audit.recordList.length - 1].handelPositionIdName != '出纳'">
				<button class="btn btn-secondary w-100" data-toggle="modal" data-target="#erpreject" v-on:click="rejectContent">驳回</button>
			</div>
			<div class="col">
				<button class="btn btn-primary w-100" data-toggle="modal" data-target="#erpassent" v-on:click="assentContent">通过</button>
			</div>
		</div>
	</div>
	<div v-if="audit.auditType == 200 && audit.maxBatchAreaConfirm && audit.maxBatchAreaConfirm.status == 1001 && audit.butFalg == 1 ">
		<div style="padding-top: 4rem;"></div>
		<div class="row fixed-bottom bg-light pt-2 pb-2">
			<div class="col">
				<button class="btn btn-secondary w-100" data-toggle="modal" data-target="#erprejectTwo" v-on:click="rejectContentTwo">拒绝</button>
			</div>
			<div class="col">
				<button class="btn btn-primary w-100" data-toggle="modal" data-target="#erpassentTwo" v-on:click="assentContentTwo">同意</button>
			</div>
		</div>
	</div>
	<div class="modal fade" id="assent" tabindex="-1" role="dialog" aria-hidden="true">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-body">
	        <div class="row pt-3">
	        	<div class="col-12">通过理由</div>
	        	<div class="col-12 pt-1">
	        		<textarea class="form-control" v-model="content"></textarea>
	        	</div>
	        </div>
	      </div>
	      <div class="modal-footer">
	        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
	        <button type="button" class="btn btn-primary" v-if="audit.auditType > 2000" v-on:click="assent">确定</button>
	      </div>
	    </div>
	  </div>
	</div>
	<div class="modal fade" id="reject" tabindex="-1" role="dialog" aria-hidden="true">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-header">
	        <h5 class="modal-title" id="exampleModalLabel">驳回至</h5>
	      </div>
	      <div class="modal-body">
	        <div class="row">
	        	<div class="col text-center">
	        		<select class="custom-select form-control" v-model="auditRecord">
	        			<option v-for="item in audit.rejectUserList" :key="item.recordId" :value="item">
		        			<div v-if="item.approveLevel == 0">
		        				提交人
		        			</div>
		        			<div v-else>第{{item.approveLevel}}层审批：</div>
		        			{{item.createByName}}
	        			</option>
	        		</select>
	        	</div>
	        </div>
	        <div class="row pt-3">
	        	<div class="col-12">驳回理由</div>
	        	<div class="col-12 pt-1">
	        		<textarea class="form-control" v-model="content"></textarea>
	        	</div>
	        </div>
	      </div>
	      <div class="modal-footer">
	        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
	        <button type="button" class="btn btn-primary" v-on:click="reject">确定</button>
	      </div>
	    </div>
	  </div>
	</div>
	<div class="modal fade" id="comment" tabindex="-1" role="dialog" aria-hidden="true">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-body">
	        <div class="row pt-3">
	        	<div class="col-12">评论内容</div>
	        	<div class="col-12 pt-1">
	        		<textarea class="form-control" v-model="comments.content"></textarea>
	        	</div>
	        </div>
	      </div>
	      <div class="modal-footer">
	        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
	        <button type="button" class="btn btn-primary" v-on:click="commit">发布</button>
	      </div>
	    </div>
	  </div>
	</div>
	<div class="modal fade" id="erpassent" tabindex="-1" role="dialog" aria-hidden="true">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-body">
	        <div class="row pt-3">
	        	<div class="col-12">通过理由</div>
	        	<div class="col-12 pt-1">
	        		<textarea class="form-control" v-model="content"></textarea>
	        	</div>
	        </div>
	      </div>
	      <div class="modal-footer">
	        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
	        <button type="button" class="btn btn-primary" v-on:click="examine(1)">确定</button>
	      </div>
	    </div>
	  </div>
	</div>
	<div class="modal fade" id="erpassentThree" tabindex="-1" role="dialog" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-body">
					<div class="row pt-3">
						<div class="col-12">审批成功</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
					<button type="button" class="btn btn-primary" v-on:click="messageBidding(1)">确定</button>
				</div>
			</div>
		</div>
	</div>
	<div class="modal fade" id="erpreject" tabindex="-1" role="dialog" aria-hidden="true">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-body">
	        <div class="row pt-3">
	        	<div class="col-12">驳回理由</div>
	        	<div class="col-12 pt-1">
	        		<textarea class="form-control" v-model="content"></textarea>
	        	</div>
	        </div>
	      </div>
	      <div class="modal-footer">
	        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
	        <button type="button" class="btn btn-primary" v-on:click="examine(2)">确定</button>
	      </div>
	    </div>
	  </div>
	</div>

	<div class="modal fade" id="erpassentTwo" tabindex="-1" role="dialog" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-body">
					<div class="row pt-3">
						<div class="col-12">通过理由</div>
						<div class="col-12 pt-1">
							<textarea class="form-control" v-model="content"></textarea>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
					<button type="button" class="btn btn-primary" v-on:click="batchAreaConfirm(1)">通过</button>
				</div>
			</div>
		</div>
	</div>
	<div class="modal fade" id="erprejectTwo" tabindex="-1" role="dialog" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-body">
					<div class="row pt-3">
						<div class="col-2">拒绝理由</div>
						<div class="col-12 pt-1">
							<textarea class="form-control" v-model="content"></textarea>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
					<button type="button" class="btn btn-primary" v-on:click="batchAreaConfirm(2)">拒绝</button>
				</div>
			</div>
		</div>
	</div>
</div>