package com.kyb.pcberp.modules.wechat.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.report.entity.Report;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.Employee;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.wechat.entity.*;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.UserCompany;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatAudit;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatUser;
import org.apache.ibatis.annotations.Param;

import javax.jws.soap.SOAPBinding;
import java.util.List;
import java.util.Map;

/**
 * ycy 2016-10-19 微信账号绑定接口
 *
 * <AUTHOR>
 */
@MyBatisDao
public interface AccountBindDao extends CrudDao<AccountBind>
{
    /**
     * fzd 2017-04-20 发送消息获取用户的openId
     */
    List<AccountBind> findUserOpenId(User user);

    List<AccountBind> findEmpOpenId(User user);

    /**
     * tj 2019-07-20 发送消息获取erp用户的openId
     */
    List<AccountBind> findErpUserOpenId(User user);

    List<WechatUser> getAllAccountByOpenId(@Param("openId") String openId);

    void logOut(@Param("openId") String openId);

    void bindUser(IcloudUser user);

    IcloudUser getIcloudUser(@Param("phone") String phone);

    Integer getBindNum(IcloudUser user);

    Integer icloudLoginCode(@Param("code") String code, @Param("recordId") String recordId);

    Integer icloudLoginName(WechatUser user);

    Integer icloudLoginPhone(IcloudUser user);

    void icloudRegister(IcloudUser user);

    void updateIcloudUser(WechatUser user);

    WechatUser setUserDeail(@Param("openId") String openId);

    void updateUserDeail(WechatUser user);

    void updateUserAuth(IcloudUser user);

    void updateUserMsg(IcloudUser user);

    List<Person> getCustomerList(Report report);

    List<Person> getUserList(Report report);

    /**
     * zjn 2019-08-16 供应商列表
     */
    List<Person> getSupplierList(Report report);

    /**
     * 根据名字去ERP中查找公司
     */
    Company findCompanyByName(Company company);

    AccountBind getBindDbName(@Param("openId") String openId);

    String getPhone(@Param("openId") String openId);

    /**
     * 根据名字去云平台中查找公司
     */
    Company findCompanyCount(Company company);

    /**
     * 根据手机号码去ERP中查找用户
     */
    User findUserInfo(@Param("phone") String phone, @Param("recordId") String recordId);

    Integer getUserCompanyNum(UserCompany uc);

    void insertIcloudCompany(Company company);

    void insertUserCompany(UserCompany userCompany);

    // 获取审批单当前审批人userId集合
    List<String> getNextApprove(Map<String, String> map);

    // TJ 2018-07-18 获取待审批数量
    String getTodoAuditCount(@Param("empId") String empId);

    // TJ 2018-07-20 获取审批
    WechatAudit findAuditById(String id);

    /**
     * zjn 2019-11-29 获取抄送人和登录员工名称
     */
    WechatAudit getCopyforEmpName(WechatAudit wechatAudit);

    String getTenId(@Param("code") String code);

    IcloudUser getTenUser(IcloudUser user);

    void deleteInviteByPhone(IcloudUser user);

    void bindInvite(IcloudUser user);

    IcloudUser getUser(@Param("openId") String openId);

    List<IcloudCompany> getCompanyList(@Param("recordId") String recordId);

    IcloudCompany getCompany(@Param("recordId") String recordId);

    Integer valiteErpCompany(@Param("erpCompanyId") String erpCompanyId, @Param("recordId") String recordId);

    List<IcloudCompany> valiteCompany(IcloudCompany icloudCompany);

    Integer valiteCompanyCode(IcloudCompany icloudCompany);

    void insertCompany(IcloudCompany icloudCompany);

    void updateCompany(IcloudCompany icloudCompany);

    void deleteCompany(@Param("recordId") String recordId);

    void useCom(@Param("recordId") String recordId);

    List<IcloudUser> getCompanyUserList(IcloudCompanyUser icloudCompanyUser);

    void deleteCompanyUser(IcloudCompanyUser icloudCompanyUser);

    Integer valiteCompanyUser(IcloudCompanyUser icloudCompanyUser);

    void addCompanyUser(IcloudCompanyUser icloudCompanyUser);

    void updateCompanyUser(IcloudCompanyUser icloudCompanyUser);

    void setManage(IcloudCompanyUser icloudCompanyUser);

    String getDefalutDbName(@Param("phone") String phone);

    List<ErpCompany> getErpCompanyList(@Param("dbDefaultName") String dbDefaultName);

    void updateCompanyUserFlag(IcloudCompanyUser icloudCompanyUser);

    String getComIdByCode(@Param("code") String code);

    List<IcloudCompany> getCompanyByCode(@Param("code") String code);

    List<IcloudCompany> loadMyCompanyList(IcloudCompany icloudCompany);

    List<IcloudUser> getPhoneBookList(@Param("phone") String phone, @Param("serchMsg") String serchMsg,@Param("pageNo") String pageNo,@Param("pageSize") String pageSize);

    void updateCompanyLogo(IcloudCompany icloudCompany);

    void updateCompanyImgUrl(IcloudCompany icloudCompany);

    List<IcloudUser> getAllCompanyUserList(@Param("comIds") String comIds);

    List<IcloudCompany> loadMySupplierList(@Param("phone") String phone);

    List<IcloudCompany> getIcloudCompanyList(@Param("companyId") String companyId);

    List<WechatUser> getHrUserList(WechatUser wechatUser);

    void updateUser(WechatUser wechatUser);

    void getInsertInvite(WechatUser wechatUser);

    void getDelUserAccount(WechatUser wechatUser);

    Integer checkRealName(IcloudUser user);

    List<IcloudCompany> getMaintenanceCompanyList(IcloudCompany icloudCompany);

    Integer getMaintenanceRole(User user);

    void updateEmpUserId(WechatUser wechatUser);

    List<IcloudCompany> getEcosphereList(@Param("companyId") String companyId);

    List<IcloudCompany> loadCustomerList(IcloudCompany icloudCompany);

    List<IcloudCompany> getListByShortName(IcloudCompany icloudCompany);

    String getIcloudCompanyId(@Param("recordId") String recordId);

    Integer checkUserCompanyCount(UserCompany userCompany);

    List<IcloudCompany> getDefaultCompany();

    List<IcloudCompany> getMyRepairCompanyList(Maintenance maintenance);

    List<Maintenance> materialEquimentList(@Param("companyIds") String companyIds,@Param("searchInfo") String searchInfo,@Param("applyCompanyId") String applyCompanyId,
                                           @Param("erpCompanyId") String erpCompanyId);

    List<Maintenance> getMaterialEquimentData(@Param("materialIds") String materialIds);

    List<IcloudCompany> getApplyCustomList(@Param("repaircompanyIds") String repaircompanyIds);

    //获取维保公司
    List<IcloudCompany> loadRepairCompanyList(@Param("userId") String userId);

    List<IcloudUser> getRepairCompanyUserList(@Param("repairCompanyIds") String repairCompanyIds);

    Employee getOaEmployeeMessage(@Param("phone") String phone);

    List<Company> getErpList(IcloudUser user);

    void updateUserId(@Param("userId") String userId, @Param("phone") String phone);

    List<IcloudCompany> getCompanyListT(IcloudUser user);
}
