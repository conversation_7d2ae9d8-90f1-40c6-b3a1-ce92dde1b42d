package com.kyb.pcberp.modules.production.util.holiday;

/**
 * @ClassName HolidayUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/12/19 21:10
 * @Version 1.0
 **/

import cn.hutool.core.date.ChineseDate;
import cn.hutool.core.date.DateUtil;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.modules.production.vo.HolidayConstantVo;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @program: demo-test
 * @description: 假期工具类
 * @author: czchen
 * @date: 2022-02-14 11:46:05
 */
public class HolidayUtil {

    /**
     * 获取假期时间
     * @param holidayName 假期名称
     * @param year 当前年份
     * @return
     */
    public static Date getHoliday(String holidayName, int year) {
        switch (holidayName) {
        case HolidayConstantVo.NEWYEARDAY:
            return DateUtil.parse(year + "-1-1");
        case HolidayConstantVo.CHINESENEWYEARGREGORIANFESTIVAL:
            ChineseDate chinesenewYearFestival = new ChineseDate(year, 1, 1);
            return chinesenewYearFestival.getGregorianDate();
        case HolidayConstantVo.QINGMINGFESTIVAL:
            int param = year - 2000;
            int qingmingDay = (int) (param * 0.2422 + 4.81) - param / 4;
            return DateUtil.parse(year + "-4-" + qingmingDay);
        case HolidayConstantVo.LABORDAY:
            return DateUtil.parse(year + "-5-1");
        case HolidayConstantVo.DRAGONBOATGREGORIANFESTIVAL:
            ChineseDate dragonBoatFestival = new ChineseDate(year, 5, 5);
            return dragonBoatFestival.getGregorianDate();
        case HolidayConstantVo.MIDAUTUMNGREGORIANFESTIVAL:
            ChineseDate midAutumnFestival = new ChineseDate(year, 8, 15);
            return midAutumnFestival.getGregorianDate();
        case HolidayConstantVo.NATIONALDAY:
            return DateUtil.parse(year + "-10-1");
        default:
            return new Date();
        }
    }

    /**
     * 获取年所有假期时间
     * @param year 当前年份
     * @return
     */
    public static Map<String,String> getHoliday(int year) {
        Map<String,String> dateStr = new HashMap<>();
        dateStr.put("元旦节", DateUtils.formatDateTime(DateUtil.parse(year + "-1-1")));
        ChineseDate chinesenewYearFestival = new ChineseDate(year, 1, 1);
        dateStr.put("春节",DateUtils.formatDateTime(chinesenewYearFestival.getGregorianDate()));
        int param = year - 2000;
        int qingmingDay = (int) (param * 0.2422 + 4.81) - param / 4;
        dateStr.put("清明节",DateUtils.formatDateTime(DateUtil.parse(year + "-4-" + qingmingDay)));
        dateStr.put("劳动节",DateUtils.formatDateTime(DateUtil.parse(year + "-5-1")));
        ChineseDate dragonBoatFestival = new ChineseDate(year, 5, 5);
        dateStr.put("端午节",DateUtils.formatDateTime(dragonBoatFestival.getGregorianDate()));
        ChineseDate midAutumnFestival = new ChineseDate(year, 8, 15);
        dateStr.put("中秋节",DateUtils.formatDateTime(midAutumnFestival.getGregorianDate()));
        dateStr.put("国庆节",DateUtils.formatDateTime(DateUtil.parse(year + "-10-1")));
        return dateStr;
    }

    /**
     * <AUTHOR>
     * @Description //TODO 获取年月的所有week周
     * @Date 9:40 2022/12/20
     * @Param [year, month]
     * @return java.util.List<java.lang.String>
     */
    public static List<String> getMonthWeek(Integer year,Integer month,int week){
        List<String> dateList=new ArrayList<>();
        SimpleDateFormat simdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);// 不设置的话默认为当年
        calendar.set(Calendar.MONTH, month);// 设置月份
        calendar.set(Calendar.DAY_OF_MONTH, 1);// 设置为当月第一天
        int daySize = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);// 当月最大天数
        for (int i = 0; i < daySize; i++) {
            int weekDay = calendar.get(Calendar.DAY_OF_WEEK);
            if(week == weekDay)
            {
                dateList.add(simdf.format(calendar.getTime()));
            }
            calendar.add(Calendar.DATE, 1);//在第一天的基础上加1
        }
        return dateList;
    }

}
