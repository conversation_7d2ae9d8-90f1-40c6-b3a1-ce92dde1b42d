package com.kyb.pcberp.modules.sys.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

/**
 * 消息设置表
 * 
 * <AUTHOR>
 * @version 2017-03-01
 */
public class MessageSet extends DataEntity<MessageSet>
{
    private static final long serialVersionUID = 1L;
    
    private User user; // 用户
    
    private MessageMoudle messageMoudle; // 对应的消息模块权限
    
    private String setFlag; // 区分个人所有的消息
    
    private String editFlag; // 区分是否可以设置消息
    
    private String roleId; // 角色id
    
    private String checkFlag; // 是否选择
    
    public User getUser()
    {
        return user;
    }
    
    public MessageMoudle getMessageMoudle()
    {
        return messageMoudle;
    }
    
    public void setUser(User user)
    {
        this.user = user;
    }
    
    public void setMessageMoudle(MessageMoudle messageMoudle)
    {
        this.messageMoudle = messageMoudle;
    }
    
    public String getSetFlag()
    {
        return setFlag;
    }
    
    public String getEditFlag()
    {
        return editFlag;
    }
    
    public String getRoleId()
    {
        return roleId;
    }
    
    public void setSetFlag(String setFlag)
    {
        this.setFlag = setFlag;
    }
    
    public void setEditFlag(String editFlag)
    {
        this.editFlag = editFlag;
    }
    
    public void setRoleId(String roleId)
    {
        this.roleId = roleId;
    }
    
    public String getCheckFlag()
    {
        return checkFlag;
    }
    
    public void setCheckFlag(String checkFlag)
    {
        this.checkFlag = checkFlag;
    }
    
}
