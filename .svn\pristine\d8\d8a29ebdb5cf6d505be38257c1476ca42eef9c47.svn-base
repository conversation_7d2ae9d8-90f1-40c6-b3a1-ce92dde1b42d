/**
 *
 */
package com.kyb.pcberp.modules.finance.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.contract.entity.Contract;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.report.entity.OverdueDeductions;
import com.kyb.pcberp.modules.sys.entity.User;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/** zjn 2019-02-28 收款记录分月实体类*/
public class CollectMuchMoney extends DataEntity<CollectMuchMoney>
{

    private static final long serialVersionUID = 1L;

    private Customer customer; // 客户ID

    private Contract contract; // 售销合同ID

    private Integer period; // period

    private Date collectedDate; // 收款日期

    private BigDecimal amount; // 收款金额

    private BigDecimal amounttemp; // 收款金额

    private String billNo; // 单据编号

    private Integer collectWay; // 收款方式

    private String receiveAccount; // 接收账号

    private String bank; // 接收账号

    private Integer fundType; // 款项类别

    private User responsiblePerson; // 经办人

    private String sourceId; // 当程序自动生产收款记录时的源记录ID

    private Long writeOffId; // 被冲红的记录ID或用于冲红的记录ID

    private String writeOffCause; // 冲红原因

    private Integer status; // 冲红状态

    private Date sentTimeStartQr; // 时间段查询条件 载体

    private Date sentTimeEndQr;

    private Integer dateForWriteOffCollectMoney;// 冲红对账日期

    /*
     * ojh 2016.9.28 begin 只是用于接收页面上传过来的标识是否核销对账单 数据库中是否表示这条收款记录是否是核销对账单的记录，只需要看是否priod为空就可以判断,
     * 如果priod为空，就表示这条记录是核销对账单的记录，做了往对账单中关联的数据操作， 如果priod不为空，就表示这条记录不是核销对账单的记录，没有做往对账单中关联的数据操作，
     * 只是作为一条收款记录，其对账单收款数和这边的收款记录是没有对等关系的
     */

    private Boolean verificationSheetStatus;

    private BigDecimal changeFee;// 手续费

    private CollectMoney collectMoney;// 收款记录总表

    private BigDecimal batchRcvAmount;// 单月未结清金额

    private BigDecimal otherFee; // zjn 其它费用

    private String singleReceivableId; // 应收单id

    private String userName;

    private String shortName;

    private String no;

    private String name;

    private String contractNo;

    private BigDecimal reconcilingAmount;

    private BigDecimal condeailMoney;

    private BigDecimal prdorderMoney;

    private BigDecimal profits;

    private BigDecimal grossProfit;

    private BigDecimal royaltyRate;

    private BigDecimal newRoyaltyRate;

    private BigDecimal royalty;

    private String custType;

    private Integer saleCount;

    private String custSupStock;

    private BigDecimal computAmount;

    private BigDecimal salePrecent;

    private BigDecimal saleFee;

    private BigDecimal riskFee;

    private BigDecimal periodFee;

    private String userId;

    private BigDecimal oneRoyaltyRate;

    private BigDecimal twoRoyaltyRate;

    private List<SingleReceivableDetail> detailList; // 应收明细列表

    private Date orderDate;

    private String departName;

    private String reportStatus;

    private String createByName;

    private String deptId;

    private String supDeptId;

    private String contractDetailId;

    private String paymentMonth; // 回款月

    private BigDecimal policy; // 政策

    private CollectMuchMoney sumObj;

    private Date collectMoneyDate;

    private BigDecimal singleAmount;

    private String type;

    private String oldUserName;

    private BigDecimal firstAmount;

    private BigDecimal deptAmount;

    private String collectMoneyPeriod;

    private String createdById;

    private BigDecimal adjustAmount;

    private String shippingMonth; // 出货月

    private BigDecimal overduePayment; // 逾期货款

    private BigDecimal lateDeduction; // 逾期扣款

    private Integer checkDate; // 对账日期

    private String goodCheckId; // 对账单明细id

    private BigDecimal goodAmount; // 对账单明细金额

    private String payWayValue;

    private BigDecimal afterRoyalty;

    private Date shippingDate;

    private String deptType; // 部门类型

    private String empType; // 员工类型

    private String finalId;

    private String singleReceivableDetailId; // 应收单明细id

    private BigDecimal barrelBoltFee; // 管销费

    private BigDecimal manageFee; // 管理费

    private String collectWayValue;

    private Integer payWayRemark; // 月结天数

    private Integer differenceMonth; //

    private String groupOrgId;

    private String groupOrgName;

    private List<CollectMuchMoney> childList;

    private List<OverdueDeductions> overdueList;

    private BigDecimal lateDeductionTwo;

    private Integer overdueMonth; // 逾期月份数

    private BigDecimal overdueRoyalty; // 提成逾期考核

    private BigDecimal deptCondeailMoney; // 部门对账金额

    private BigDecimal deptPrdorderMoney; // 部门成本

    private BigDecimal deptSaleFee; // 部门业务费

    private BigDecimal deptBarrelBoltFee; // 部门管销费

    private BigDecimal deptManageFee; // 部门管理费

    private BigDecimal grossProfitMonth; // 月净利率

    private BigDecimal laborCost; // 人工费用

    private BigDecimal deptLaborCost; // 部门人工费用

    public BigDecimal getAmounttemp()
    {
        return amounttemp;
    }

    public void setAmounttemp(BigDecimal amounttemp)
    {
        this.amounttemp = amounttemp;
    }

    public CollectMuchMoney()
    {
        super();
    }

    public CollectMuchMoney(String id)
    {
        super(id);
    }

    public Date getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }

    public void setSentTimeStartQr(Date sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }

    public Date getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }

    public void setSentTimeEndQr(Date sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "收款日期不能为空")
    public Date getCollectedDate()
    {
        return collectedDate;
    }

    public void setCollectedDate(Date collectedDate)
    {
        this.collectedDate = collectedDate;
    }

    @Length(min = 0, max = 200, message = "单据编号长度必须介于 0 和 200 之间")
    public String getBillNo()
    {
        return billNo;
    }

    public void setBillNo(String billNo)
    {
        this.billNo = billNo;
    }

    public Integer getCollectWay()
    {
        return collectWay;
    }

    public void setCollectWay(Integer collectWay)
    {
        this.collectWay = collectWay;
    }

    @Length(min = 0, max = 30, message = "接收账号长度必须介于 0 和 30 之间")
    public String getReceiveAccount()
    {
        return receiveAccount;
    }

    public void setReceiveAccount(String receiveAccount)
    {
        this.receiveAccount = receiveAccount;
    }

    public String getBank()
    {
        return bank;
    }

    public void setBank(String bank)
    {
        this.bank = bank;
    }

    public User getResponsiblePerson()
    {
        return responsiblePerson;
    }

    public void setResponsiblePerson(User responsiblePerson)
    {
        this.responsiblePerson = responsiblePerson;
    }

    public String getSourceId()
    {
        return sourceId;
    }

    public void setSourceId(String sourceId)
    {
        this.sourceId = sourceId;
    }

    public Long getWriteOffId()
    {
        return writeOffId;
    }

    public void setWriteOffId(Long writeOffId)
    {
        this.writeOffId = writeOffId;
    }

    @Length(min = 0, max = 255, message = "冲红原因长度必须介于 0 和 255 之间")
    public String getWriteOffCause()
    {
        return writeOffCause;
    }

    public void setWriteOffCause(String writeOffCause)
    {
        this.writeOffCause = writeOffCause;
    }

    public Integer getStatus()
    {
        return status;
    }

    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Customer getCustomer()
    {
        return customer;
    }

    public void setCustomer(Customer customer)
    {
        this.customer = customer;
    }

    public Contract getContract()
    {
        return contract;
    }

    public void setContract(Contract contract)
    {
        this.contract = contract;
    }

    public Integer getPeriod()
    {
        return period;
    }

    public void setPeriod(Integer period)
    {
        this.period = period;
    }

    public BigDecimal getAmount()
    {
        return amount;
    }

    public void setAmount(BigDecimal amount)
    {
        this.amount = amount;
    }

    public Integer getFundType()
    {
        return fundType;
    }

    public void setFundType(Integer fundType)
    {
        this.fundType = fundType;
    }

    @Override
    public Object clone()
    {
        CollectMuchMoney collectMoney = null;
        try
        {
            collectMoney = (CollectMuchMoney)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            e.printStackTrace();
        }
        return collectMoney;
    }

    public Boolean getVerificationSheetStatus()
    {
        return verificationSheetStatus;
    }

    public void setVerificationSheetStatus(Boolean verificationSheetStatus)
    {
        this.verificationSheetStatus = verificationSheetStatus;
    }

    public Integer getDateForWriteOffCollectMoney()
    {
        return dateForWriteOffCollectMoney;
    }

    public void setDateForWriteOffCollectMoney(Integer dateForWriteOffCollectMoney)
    {
        this.dateForWriteOffCollectMoney = dateForWriteOffCollectMoney;
    }

    public BigDecimal getChangeFee()
    {
        return changeFee;
    }

    public void setChangeFee(BigDecimal changeFee)
    {
        this.changeFee = changeFee;
    }

    public CollectMoney getCollectMoney()
    {
        return collectMoney;
    }

    public void setCollectMoney(CollectMoney collectMoney)
    {
        this.collectMoney = collectMoney;
    }

    public BigDecimal getBatchRcvAmount()
    {
        return batchRcvAmount;
    }

    public void setBatchRcvAmount(BigDecimal batchRcvAmount)
    {
        this.batchRcvAmount = batchRcvAmount;
    }

    public BigDecimal getOtherFee()
    {
        return otherFee;
    }

    public void setOtherFee(BigDecimal otherFee)
    {
        this.otherFee = otherFee;
    }

    public String getSingleReceivableId()
    {
        return singleReceivableId;
    }

    public void setSingleReceivableId(String singleReceivableId)
    {
        this.singleReceivableId = singleReceivableId;
    }

    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getShortName()
    {
        return shortName;
    }

    public void setShortName(String shortName)
    {
        this.shortName = shortName;
    }

    public String getNo()
    {
        return no;
    }

    public void setNo(String no)
    {
        this.no = no;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getContractNo()
    {
        return contractNo;
    }

    public void setContractNo(String contractNo)
    {
        this.contractNo = contractNo;
    }

    public BigDecimal getReconcilingAmount()
    {
        return reconcilingAmount;
    }

    public void setReconcilingAmount(BigDecimal reconcilingAmount)
    {
        this.reconcilingAmount = reconcilingAmount;
    }

    public BigDecimal getCondeailMoney()
    {
        return condeailMoney;
    }

    public void setCondeailMoney(BigDecimal condeailMoney)
    {
        this.condeailMoney = condeailMoney;
    }

    public BigDecimal getPrdorderMoney()
    {
        return prdorderMoney;
    }

    public void setPrdorderMoney(BigDecimal prdorderMoney)
    {
        this.prdorderMoney = prdorderMoney;
    }

    public BigDecimal getProfits()
    {
        return profits;
    }

    public void setProfits(BigDecimal profits)
    {
        this.profits = profits;
    }

    public BigDecimal getGrossProfit()
    {
        return grossProfit;
    }

    public void setGrossProfit(BigDecimal grossProfit)
    {
        this.grossProfit = grossProfit;
    }

    public BigDecimal getRoyaltyRate()
    {
        return royaltyRate;
    }

    public void setRoyaltyRate(BigDecimal royaltyRate)
    {
        this.royaltyRate = royaltyRate;
    }

    public BigDecimal getNewRoyaltyRate()
    {
        return newRoyaltyRate;
    }

    public void setNewRoyaltyRate(BigDecimal newRoyaltyRate)
    {
        this.newRoyaltyRate = newRoyaltyRate;
    }

    public BigDecimal getRoyalty()
    {
        return royalty;
    }

    public void setRoyalty(BigDecimal royalty)
    {
        this.royalty = royalty;
    }

    public String getCustType()
    {
        return custType;
    }

    public void setCustType(String custType)
    {
        this.custType = custType;
    }

    public Integer getSaleCount()
    {
        return saleCount;
    }

    public void setSaleCount(Integer saleCount)
    {
        this.saleCount = saleCount;
    }

    public String getCustSupStock()
    {
        return custSupStock;
    }

    public void setCustSupStock(String custSupStock)
    {
        this.custSupStock = custSupStock;
    }

    public BigDecimal getComputAmount()
    {
        return computAmount;
    }

    public void setComputAmount(BigDecimal computAmount)
    {
        this.computAmount = computAmount;
    }

    public BigDecimal getSalePrecent()
    {
        return salePrecent;
    }

    public void setSalePrecent(BigDecimal salePrecent)
    {
        this.salePrecent = salePrecent;
    }

    public BigDecimal getSaleFee()
    {
        return saleFee;
    }

    public void setSaleFee(BigDecimal saleFee)
    {
        this.saleFee = saleFee;
    }

    public BigDecimal getRiskFee()
    {
        return riskFee;
    }

    public void setRiskFee(BigDecimal riskFee)
    {
        this.riskFee = riskFee;
    }

    public BigDecimal getPeriodFee()
    {
        return periodFee;
    }

    public void setPeriodFee(BigDecimal periodFee)
    {
        this.periodFee = periodFee;
    }

    public List<SingleReceivableDetail> getDetailList()
    {
        return detailList;
    }

    public void setDetailList(List<SingleReceivableDetail> detailList)
    {
        this.detailList = detailList;
    }

    public String getUserId()
    {
        return userId;
    }

    public void setUserId(String userId)
    {
        this.userId = userId;
    }

    public BigDecimal getOneRoyaltyRate()
    {
        return oneRoyaltyRate;
    }

    public void setOneRoyaltyRate(BigDecimal oneRoyaltyRate)
    {
        this.oneRoyaltyRate = oneRoyaltyRate;
    }

    public BigDecimal getTwoRoyaltyRate()
    {
        return twoRoyaltyRate;
    }

    public void setTwoRoyaltyRate(BigDecimal twoRoyaltyRate)
    {
        this.twoRoyaltyRate = twoRoyaltyRate;
    }

    public Date getOrderDate()
    {
        return orderDate;
    }

    public void setOrderDate(Date orderDate)
    {
        this.orderDate = orderDate;
    }

    public String getOrderDateStr()
    {
        if (orderDate == null)
        {
            return null;
        }
        return DateUtils.formatDate(orderDate, "yyyyMM");
    }

    public String getDepartName()
    {
        return departName;
    }

    public void setDepartName(String departName)
    {
        this.departName = departName;
    }

    public String getReportStatus()
    {
        return reportStatus;
    }

    public void setReportStatus(String reportStatus)
    {
        this.reportStatus = reportStatus;
    }

    public String getCreateByName()
    {
        return createByName;
    }

    public void setCreateByName(String createByName)
    {
        this.createByName = createByName;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public String getSupDeptId()
    {
        return supDeptId;
    }

    public void setSupDeptId(String supDeptId)
    {
        this.supDeptId = supDeptId;
    }

    public String getContractDetailId()
    {
        return contractDetailId;
    }

    public void setContractDetailId(String contractDetailId)
    {
        this.contractDetailId = contractDetailId;
    }

    public String getPaymentMonth()
    {
        return paymentMonth;
    }

    public void setPaymentMonth(String paymentMonth)
    {
        this.paymentMonth = paymentMonth;
    }

    public BigDecimal getPolicy()
    {
        return policy;
    }

    public void setPolicy(BigDecimal policy)
    {
        this.policy = policy;
    }

    public CollectMuchMoney getSumObj()
    {
        return sumObj;
    }

    public void setSumObj(CollectMuchMoney sumObj)
    {
        this.sumObj = sumObj;
    }

    public Date getCollectMoneyDate()
    {
        return collectMoneyDate;
    }

    public void setCollectMoneyDate(Date collectMoneyDate)
    {
        this.collectMoneyDate = collectMoneyDate;
    }

    public BigDecimal getSingleAmount()
    {
        return singleAmount;
    }

    public void setSingleAmount(BigDecimal singleAmount)
    {
        this.singleAmount = singleAmount;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public String getOldUserName()
    {
        return oldUserName;
    }

    public void setOldUserName(String oldUserName)
    {
        this.oldUserName = oldUserName;
    }

    public BigDecimal getFirstAmount()
    {
        return firstAmount;
    }

    public void setFirstAmount(BigDecimal firstAmount)
    {
        this.firstAmount = firstAmount;
    }

    public BigDecimal getDeptAmount()
    {
        return deptAmount;
    }

    public void setDeptAmount(BigDecimal deptAmount)
    {
        this.deptAmount = deptAmount;
    }

    public String getCollectMoneyPeriod()
    {
        return collectMoneyPeriod;
    }

    public void setCollectMoneyPeriod(String collectMoneyPeriod)
    {
        this.collectMoneyPeriod = collectMoneyPeriod;
    }

    public String getCreatedById() {
        return createdById;
    }

    public void setCreatedById(String createdById) {
        this.createdById = createdById;
    }

    public BigDecimal getAdjustAmount() {
        return adjustAmount;
    }

    public void setAdjustAmount(BigDecimal adjustAmount) {
        this.adjustAmount = adjustAmount;
    }

    public String getShippingMonth()
    {
        return shippingMonth;
    }

    public void setShippingMonth(String shippingMonth)
    {
        this.shippingMonth = shippingMonth;
    }

    public BigDecimal getOverduePayment()
    {
        return overduePayment;
    }

    public void setOverduePayment(BigDecimal overduePayment)
    {
        this.overduePayment = overduePayment;
    }

    public BigDecimal getLateDeduction()
    {
        return lateDeduction;
    }

    public void setLateDeduction(BigDecimal lateDeduction)
    {
        this.lateDeduction = lateDeduction;
    }

    public Integer getCheckDate()
    {
        return checkDate;
    }

    public void setCheckDate(Integer checkDate)
    {
        this.checkDate = checkDate;
    }

    public String getGoodCheckId()
    {
        return goodCheckId;
    }

    public void setGoodCheckId(String goodCheckId)
    {
        this.goodCheckId = goodCheckId;
    }

    public BigDecimal getGoodAmount()
    {
        return goodAmount;
    }

    public void setGoodAmount(BigDecimal goodAmount)
    {
        this.goodAmount = goodAmount;
    }

    public String getPayWayValue()
    {
        return payWayValue;
    }

    public void setPayWayValue(String payWayValue)
    {
        this.payWayValue = payWayValue;
    }

    public Integer getPayWayDays()
    {
        Integer days = 0;
        if(StringUtils.isBlank(payWayValue))
        {
            return days;
        }
        switch (payWayValue)
        {
            case "现金":
                days = 0;
                break;
            case "货到付款":
                days = 0;
                break;
            case "月结":
                days = 30;
                break;
            case "月结30天":
                days = 30;
                break;
            case "月结45天":
                days = 45;
                break;
            case "月结60天":
                days = 60;
                break;
            case "月结75天":
                days = 75;
                break;
            case "月结90天":
                days = 90;
                break;
            case "月结120天":
                days = 120;
                break;
            case "预付-月结":
                 days = 30;
            break;
            case "预付-月结30天":
                days = 30;
                break;
            case "预付-月结60天":
                days = 60;
                break;
            case "预付-月结90天":
                days = 90;
                break;
            case "预付-月结120天":
                days = 120;
                break;
            case "票到月结30天":
                days = 30;
                break;
            case "票到月结60天":
                days = 60;
                break;
            case "票到月结90天":
                days = 90;
                break;
            case "票到月结120天":
                days = 120;
                break;
            case "票到30天":
                days = 30;
                break;
            case "票到60天":
                days = 60;
                break;
            case "票到90天":
                days = 90;
                break;
            case "票到120天":
                days = 120;
                break;
        }
        return days;
    }

    public BigDecimal getAfterRoyalty()
    {
        return afterRoyalty;
    }

    public void setAfterRoyalty(BigDecimal afterRoyalty)
    {
        this.afterRoyalty = afterRoyalty;
    }

    public Date getShippingDate()
    {
        return shippingDate;
    }

    public void setShippingDate(Date shippingDate)
    {
        this.shippingDate = shippingDate;
    }

    public String getShippingDateStr()
    {
        if(null != shippingDate)
        {
            return DateUtils.formatDate(shippingDate);
        }
        return null;
    }

    public String getDeptType()
    {
        return deptType;
    }

    public void setDeptType(String deptType)
    {
        this.deptType = deptType;
    }

    public String getEmpType()
    {
        return empType;
    }

    public void setEmpType(String empType)
    {
        this.empType = empType;
    }

    public String getFinalId()
    {
        return finalId;
    }

    public void setFinalId(String finalId)
    {
        this.finalId = finalId;
    }

    public String getSingleReceivableDetailId()
    {
        return singleReceivableDetailId;
    }

    public void setSingleReceivableDetailId(String singleReceivableDetailId)
    {
        this.singleReceivableDetailId = singleReceivableDetailId;
    }

    public BigDecimal getBarrelBoltFee() {
        return barrelBoltFee;
    }

    public void setBarrelBoltFee(BigDecimal barrelBoltFee) {
        this.barrelBoltFee = barrelBoltFee;
    }

    public BigDecimal getManageFee() {
        return manageFee;
    }

    public void setManageFee(BigDecimal manageFee) {
        this.manageFee = manageFee;
    }

    public String getCollectWayValue() {
        return collectWayValue;
    }

    public void setCollectWayValue(String collectWayValue) {
        this.collectWayValue = collectWayValue;
    }

    public Integer getPayWayRemark() {
        return payWayRemark;
    }

    public void setPayWayRemark(Integer payWayRemark) {
        this.payWayRemark = payWayRemark;
    }

    public Integer getDifferenceMonth() {
        return differenceMonth;
    }

    public void setDifferenceMonth(Integer differenceMonth) {
        this.differenceMonth = differenceMonth;
    }

    public String getGroupOrgId() {
        return groupOrgId;
    }

    public void setGroupOrgId(String groupOrgId) {
        this.groupOrgId = groupOrgId;
    }

    public String getGroupOrgName() {
        return groupOrgName;
    }

    public void setGroupOrgName(String groupOrgName) {
        this.groupOrgName = groupOrgName;
    }

    public List<CollectMuchMoney> getChildList() {
        return childList;
    }

    public void setChildList(List<CollectMuchMoney> childList) {
        this.childList = childList;
    }

    public List<OverdueDeductions> getOverdueList() {
        return overdueList;
    }

    public void setOverdueList(List<OverdueDeductions> overdueList) {
        this.overdueList = overdueList;
    }

    public BigDecimal getLateDeductionTwo() {
        return lateDeductionTwo;
    }

    public void setLateDeductionTwo(BigDecimal lateDeductionTwo) {
        this.lateDeductionTwo = lateDeductionTwo;
    }

    public Integer getOverdueMonth() {
        return overdueMonth;
    }

    public void setOverdueMonth(Integer overdueMonth) {
        this.overdueMonth = overdueMonth;
    }

    public BigDecimal getOverdueRoyalty() {
        return overdueRoyalty;
    }

    public void setOverdueRoyalty(BigDecimal overdueRoyalty) {
        this.overdueRoyalty = overdueRoyalty;
    }

    public BigDecimal getDeptCondeailMoney() {
        return deptCondeailMoney;
    }

    public void setDeptCondeailMoney(BigDecimal deptCondeailMoney) {
        this.deptCondeailMoney = deptCondeailMoney;
    }

    public BigDecimal getDeptPrdorderMoney() {
        return deptPrdorderMoney;
    }

    public void setDeptPrdorderMoney(BigDecimal deptPrdorderMoney) {
        this.deptPrdorderMoney = deptPrdorderMoney;
    }

    public BigDecimal getDeptSaleFee() {
        return deptSaleFee;
    }

    public void setDeptSaleFee(BigDecimal deptSaleFee) {
        this.deptSaleFee = deptSaleFee;
    }

    public BigDecimal getDeptBarrelBoltFee() {
        return deptBarrelBoltFee;
    }

    public void setDeptBarrelBoltFee(BigDecimal deptBarrelBoltFee) {
        this.deptBarrelBoltFee = deptBarrelBoltFee;
    }

    public BigDecimal getDeptManageFee() {
        return deptManageFee;
    }

    public void setDeptManageFee(BigDecimal deptManageFee) {
        this.deptManageFee = deptManageFee;
    }

    public BigDecimal getGrossProfitMonth() {
        return grossProfitMonth;
    }

    public void setGrossProfitMonth(BigDecimal grossProfitMonth) {
        this.grossProfitMonth = grossProfitMonth;
    }

    public BigDecimal getLaborCost() {
        return laborCost;
    }

    public void setLaborCost(BigDecimal laborCost) {
        this.laborCost = laborCost;
    }

    public BigDecimal getDeptLaborCost() {
        return deptLaborCost;
    }

    public void setDeptLaborCost(BigDecimal deptLaborCost) {
        this.deptLaborCost = deptLaborCost;
    }
}