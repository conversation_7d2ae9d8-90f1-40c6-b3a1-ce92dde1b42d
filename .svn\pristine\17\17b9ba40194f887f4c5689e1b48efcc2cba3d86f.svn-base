<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="d-flex flex-column flex-root">
    <div class="d-flex flex-row flex-column-fluid page">
        <div class="d-flex flex-column flex-row-fluid wrapper">
            <div class="content d-flex flex-column flex-column-fluid"
                 style="background-image: url(${pageContext.request.contextPath}/assets/global/metronic/assets/media/bg/bgMain.jpg);height: 180px;">
                <div class="subheader subheader-transparent" id="kt_subheader">
                    <div class="container d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <h5 class="text-white font-weight-bold my-2 mr-5">产品管理</h5>
                            </div>
                        </div>
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <router-link to="/salesSystem" class="btn btn-transparent-warning font-weight-bold mr-2">
                                    返回主页
                                </router-link>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-column-fluid">
                    <div class="container">
                        <div class="row">
                            <div class="col-12">
                                <div class="card card-custom gutter-b">
                                    <div class="card-body pt-3 pl-3 pr-3">
                                        <div class="row pb-2">
                                            <div class="col">
                                                <select class="form-control form-control-solid" v-model="companyId" v-on:change="queryList()">
                                                    <option v-for="item in companyList" :key="item.recordId" :value="item.recordId">
                                                        {{item.name}}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row pt-2 pb-2">
                                            <div class="col-12">
                                                <div class="input-icon">
                                                    <input type="text" class="form-control" placeholder="请输入要搜索的产品" v-model="serchMsg" v-on:blur="queryList()"/>
                                                    <span><i class="flaticon2-search-1 icon-md"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row align-items-center pt-2 pb-2 border-bottom" v-if="companyId">
                                            <div class="col text-right">
                                                <button class="btn btn-light-primary" v-on:click="addProduct('')">添加产品</button>
                                                <button class="btn btn-light-primary" v-on:click="expProductModal('')">导入产品</button>
                                            </div>
                                        </div>
                                        <div class="pt-3">
                                            <div class="row align-items-center pt-2 pb-2 border-bottom" v-for="item in productList" :key="item.recordId">
                                                <div class="col-12">
                                                    <div class="row font-weight-bolder text-primary" v-on:click="addProduct(item.recordId)">
                                                        <div class="col">
                                                            YL{{item.recordId}}-{{item.name}}
                                                        </div>
                                                    </div>
                                                    <div class="pt-2">
                                                        <div class="row">
                                                            <div class="col-12">
                                                                公司：{{item.companyName}}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="pt-2">
                                                        <div class="row">
                                                            <div class="col-12">
                                                                规格型号：{{item.specifications}}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="pt-2">
                                                        <div class="row">
                                                            <div class="col-6">
                                                                库存：{{item.stocks == null ?0:item.stocks}}
                                                            </div>
                                                            <div class="col-6 text-right">
                                                                单价：{{item.price == null ?0:item.price}}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row pt-2" v-if="item.deailList && item.deailList.length > 0">
                                                        <div class="col-12">
                                                                     <span v-for="detail in item.deailList">
                                                                         <div class="row pt-1" v-if="detail.inputType==6">
                                                                            <div class="col">
                                                                                {{detail.name}}:
                                                                                            <img alt="Pic"
                                                                                                 v-for="(fileUrl,index) in (detail.fileUrl.split(','))" :key="index" v-if="detail.fileUrl"
                                                                                                 :src="fileUrl" class="w-25"/>
                                                                            </div>
                                                                         </div>
                                                                        <span v-else>
                                                                            <span v-if="!detail.dictValue && !detail.dictValue"></span>
                                                                            <span v-else>{{detail.name}}：{{detail.dictValue?detail.dictValue:detail.value}} &nbsp;&nbsp;&nbsp;</span>
                                                                        </span>
                                                                    </span>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-12 text-right">
                                                            <button class="btn btn-light-danger btn-sm" v-on:click="deleteProduct(item.recordId)">删除</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="expProductModal" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="expProductModal" aria-hidden="true" style="overflow:auto!important;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        导入产品
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i aria-hidden="true" class="ki ki-close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row pb-3" v-if="expProductList && expProductList.length > 0">
                        <div class="col">
                            <div class="row pt-2 pb-2">
                                <div class="col-12 text-right">
                                    <button type="button" class="btn btn-light-primary font-weight-bold" v-on:click="expProduct">开始导入</button>
                                </div>
                            </div>
                            <div class="row pt-2 pb-2">
                                <div class="col-12">
                                    <div class="input-icon">
                                        <input type="text" class="form-control" placeholder="请输入要搜索的产品" v-model="serchMsgExport" v-on:blur="getExpProductList()"/>
                                        <span><i class="flaticon2-search-1 icon-md"></i></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row pt-3 pb-3 border-top border-left border-right">
                                <div class="col">
                                    <div class="checkbox-inline">
                                        <label class="checkbox checkbox-success">
                                            <input type="checkbox" v-model="allChecked" v-on:change="setAllCheckedFun()"/>
                                            <span></span>
                                        </label>
                                        <span class="font-size-h5">产品列表-全选</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row border pt-3 pb-3" v-for="item in expProductList" :key="item.recordId">
                                <div class="col">
                                    <div class="checkbox-inline">
                                        <label class="checkbox checkbox-success">
                                            <input type="checkbox" v-model="item.checked"/>
                                            <span></span>
                                        </label>
                                        <span class="font-weight-bolder text-primary" v-on:click="showDeail(item)">YL{{item.recordId}}-{{item.name}}</span>
                                    </div>
                                    <div class="row pt-1">
                                        <div class="col">规格：{{item.specifications}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="showDeail" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="showDeail" aria-hidden="true" style="overflow:auto!important;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        产品详情
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i aria-hidden="true" class="ki ki-close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="pt-1">
                        <div class="row">
                            <div class="col">
                                <div class="row pt-3 pb-3">
                                    <div class="col font-weight-bolder">
                                        基本信息
                                    </div>
                                </div>
                                <div class="row pt-2 pb-2">
                                    <div class="col font-weight-bolder">
                                        <div class="input-group align-items-center">
                                            <div class="input-group-append">
                                                产品：
                                            </div>
                                            <input class="form-control" v-model="product.configName" disabled>
                                        </div>
                                    </div>
                                </div>
                                <div class="row pt-2 pb-2">
                                    <div class="col font-weight-bolder">
                                        <div class="input-group align-items-center">
                                            <div class="input-group-append">
                                                名字：
                                            </div>
                                            <input class="form-control" v-model="product.name" disabled>
                                        </div>
                                    </div>
                                </div>
                                <div class="row pt-2 pb-2">
                                    <div class="col font-weight-bolder">
                                        <div class="input-group align-items-center">
                                            <div class="input-group-append">
                                                规格：
                                            </div>
                                            <input class="form-control" v-model="product.specifications" disabled>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" v-for="item in configList" :key="item.recordId">
                            <div class="col">
                                <div class="row pt-3 pb-3">
                                    <div class="col font-weight-bolder">
                                        {{item.name}}
                                    </div>
                                </div>
                                <div class="row pt-2 pb-2" v-for="row in item.list" :key="row.recordId" v-if="item.list">
                                    <div class="col font-weight-bolder">
                                        {{row.name}}:
                                        <div class="row" v-if="row.inputType == 1">
                                            <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                                <div class="input-group">
                                                    <input class="form-control" v-model="row.value" disabled>
                                                    <div class="input-group-append" v-if="row.unitType">
                                                        <button class="btn btn-outline-secondary">{{row.unitType}}</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" v-if="row.inputType == 2">
                                            <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                                <div class="input-group">
                                                    <input class="form-control" placeholder="长" v-model="row.value1" disabled>
                                                    <div class="input-group-append">
                                                        <button class="btn btn-outline-secondary">*</button>
                                                    </div>
                                                    <input class="form-control" placeholder="宽" v-model="row.value2" disabled>
                                                    <div class="input-group-append" v-if="row.unitType">
                                                        <button class="btn btn-outline-secondary">{{row.unitType}}</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" v-if="row.inputType == 3">
                                            <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                                <div class="input-group">
                                                    <input class="form-control" placeholder="长" v-model="row.value1" disabled>
                                                    <div class="input-group-append">
                                                        <button class="btn btn-outline-secondary">*</button>
                                                    </div>
                                                    <input class="form-control" placeholder="宽" v-model="row.value2" disabled>
                                                    <div class="input-group-append">
                                                        <button class="btn btn-outline-secondary">/</button>
                                                    </div>
                                                    <input class="form-control" placeholder="拼接数" v-model="row.value3" disabled>
                                                    <div class="input-group-append" v-if="row.unitType">
                                                        <button class="btn btn-outline-secondary">{{row.unitType}}</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" v-if="row.inputType == 4">
                                            <div class="col-12">
                                                <button :class="craft.checked == 1? 'btn btn-outline-warning mr-1 mb-1': 'btn btn-outline-secondary mr-1 mb-1'"
                                                        v-for="craft in row.list" :key="craft.recordId">
                                                    {{ craft.value }}
                                                </button>
                                            </div>
                                        </div>
                                        <div class="row" v-if="row.inputType == 5">
                                            <div class="col-12">
                                                <button :class="craft.checked == 1? 'btn btn-outline-warning mr-1 mb-1': 'btn btn-outline-secondary mr-1 mb-1'"
                                                        v-for="craft in row.list" :key="craft.recordId">
                                                    {{ craft.value }}
                                                </button>
                                            </div>
                                        </div>
                                        <div class="row" v-if="row.inputType == 6">
                                            <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                                <button class="btn btn-primary">上传文件</button>
                                            </div>
                                        </div>
                                        <div class="row" v-if="row.inputType == 7">
                                            <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                                <textarea class="form-control" v-model="row.value" disabled></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>