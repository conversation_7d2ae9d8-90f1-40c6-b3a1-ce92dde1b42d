package com.kyb.pcberp.modules.stock.service;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.kyb.pcberp.common.service.ErrorCode;
import com.kyb.pcberp.common.service.ServiceException;
import com.kyb.pcberp.modules.purch.dao.*;
import com.kyb.pcberp.modules.purch.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums.DictItemEnum;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.MathUtils;
import com.kyb.pcberp.common.utils.ProductStockUtil;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.contract.dao.ContractCraftDao;
import com.kyb.pcberp.modules.contract.dao.ContractDao;
import com.kyb.pcberp.modules.contract.dao.ContractDetailDao;
import com.kyb.pcberp.modules.contract.dao.DeliveryDao;
import com.kyb.pcberp.modules.contract.dao.DeliveryDetailDao;
import com.kyb.pcberp.modules.contract.dao.NotificationDao;
import com.kyb.pcberp.modules.contract.entity.Contract;
import com.kyb.pcberp.modules.contract.entity.ContractCraft;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.contract.entity.Delivery;
import com.kyb.pcberp.modules.contract.entity.DeliveryDetail;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.crm.dao.AccountsReceivableDao;
import com.kyb.pcberp.modules.crm.dao.GoodsCheckDao;
import com.kyb.pcberp.modules.crm.dao.RejectApplicationDao;
import com.kyb.pcberp.modules.crm.entity.AccountsReceivable;
import com.kyb.pcberp.modules.crm.entity.GoodsCheck;
import com.kyb.pcberp.modules.crm.entity.RejectApplication;
import com.kyb.pcberp.modules.eg.dao.BoardCuttingDao;
import com.kyb.pcberp.modules.eg.dao.BoardPartCraftDao;
import com.kyb.pcberp.modules.eg.dao.BoardPartDao;
import com.kyb.pcberp.modules.eg.dao.CardADao;
import com.kyb.pcberp.modules.eg.dao.CardAProcessValueDao;
import com.kyb.pcberp.modules.eg.dao.CardBDao;
import com.kyb.pcberp.modules.eg.entity.BoardPartCraft;
import com.kyb.pcberp.modules.eg.entity.CardA;
import com.kyb.pcberp.modules.eg.entity.CardAProcessValue;
import com.kyb.pcberp.modules.finance.dao.PayMoneyDao;
import com.kyb.pcberp.modules.finance.entity.PayMoney;
import com.kyb.pcberp.modules.production.dao.CraftNoDiscardDao;
import com.kyb.pcberp.modules.production.dao.FeedingDao;
import com.kyb.pcberp.modules.production.dao.ProduceBatchDao;
import com.kyb.pcberp.modules.production.dao.ProduceBatchDetailCraftDao;
import com.kyb.pcberp.modules.production.dao.ProduceBatchDetailDao;
import com.kyb.pcberp.modules.production.dao.ReplenishDao;
import com.kyb.pcberp.modules.production.entity.CraftNoDiscard;
import com.kyb.pcberp.modules.production.entity.Feeding;
import com.kyb.pcberp.modules.production.entity.ProduceBatch;
import com.kyb.pcberp.modules.production.entity.ProduceBatchDetail;
import com.kyb.pcberp.modules.production.entity.ProduceBatchDetailCraft;
import com.kyb.pcberp.modules.quality.dao.InspectDao;
import com.kyb.pcberp.modules.quality.dao.SourceDetectionDao;
import com.kyb.pcberp.modules.quality.entity.Inspect;
import com.kyb.pcberp.modules.quality.entity.SourceDetection;
import com.kyb.pcberp.modules.report.utils.FinanceUtils;
import com.kyb.pcberp.modules.report.utils.ReportAllUtils;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.dao.MaterialDepositRecordDao;
import com.kyb.pcberp.modules.stock.dao.ProductStoreDao;
import com.kyb.pcberp.modules.stock.entity.ForStockObject;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.MaterialDepositRecord;
import com.kyb.pcberp.modules.stock.entity.ProductStore;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

@Service
@Transactional(readOnly = true)
public class ProductInoutService extends CrudService<ProductStoreDao, ProductStore>
{
    @Autowired
    private CraftNoDiscardDao craftNoDiscardDao;
    
    @Autowired
    private MaterialDao materialDao;
    
    @Autowired
    private ProductStoreDao pstoreDao;
    
    @Autowired
    private NotificationDao notifiDao;
    
    @Autowired
    private ContractDetailDao contractDetailDao;
    
    @Autowired
    private ContractCraftDao contractCraftDao;
    
    @Autowired
    private CardADao cardADao;
    
    @Autowired
    private FeedingDao feedingDao;
    
    @Autowired
    private ProduceBatchDetailDao batchDetailDao;
    
    @Autowired
    private InspectDao inspectDao;
    
    @Autowired
    private ProduceBatchDao produceBatchDao;
    
    @Autowired
    private DeliveryDetailDao deliveryDetailDao;
    
    @Autowired
    private PrdorderDetailDao proDetailDao;
    
    @Autowired
    private PrdorderDao prdorderDao;
    
    @Autowired
    private RejectApplicationDao rejectApplicationDao;
    
    @Autowired
    private AccountsReceivableDao accountReceivableDao;
    
    @Autowired
    private GoodsCheckDao goodsCheckDao;
    
    @Autowired
    private PuReturnsDetailDao returnDetailDao;
    
    @Autowired
    private PayMoneyDao payMoneyDao;
    
    @Autowired
    private ProduceBatchDetailCraftDao pbdCraftDao;
    
    @Autowired
    private SourceDetectionDao sourceDetectionDao;
    
    @Autowired
    private DeliveryDao deliveryDao;
    
    @Autowired
    private MaterialDepositRecordDao mdrDao;
    
    @Autowired
    private ReplenishDao replenishDao;
    
    @Autowired
    private ContractDao contractDao;
    
    @Autowired
    private NotificationDao notificationDao;
    
    @Autowired
    private CardBDao cardBDao;
    
    @Autowired
    private CardAProcessValueDao processValueDao;
    
    @Autowired
    private BoardPartDao boardPartDao;
    
    @Autowired
    private BoardPartCraftDao boardPartCraftDao;
    
    @Autowired
    private BoardCuttingDao boardCuttingDao;
    
    @Autowired
    private ProduceBatchDetailDao produceBatchDetailDao;
    
    @Autowired
    private AccountsPayableDao accountsPayableDao;
    
    @Autowired
    private MaterialCheckDao materialCheckDao;
    
    @Autowired
    private PayApplicationDao payApplicationDao;
    
    public ProductStore get(String id)
    {
        return super.get(id);
    }
    
    public List<ProductStore> findList(ProductStore material)
    {
        return super.findList(material);
    }
    
    public Page<ProductStore> findPage(Page<ProductStore> page, ProductStore material)
    {
        return super.findPage(page, material);
    }
    
    @Transactional(readOnly = false)
    public void save(ProductStore material)
    {
        super.save(material);
    }
    
    @Transactional(readOnly = false)
    public void delete(ProductStore material)
    {
        super.delete(material);
    }
    
    /**
     * 退货出库的数据查询
     * 
     * @param page
     * @param obj
     * @return
     */
    public Page<ForStockObject> queryProductReturnInout(Page<ForStockObject> page, ForStockObject obj)
    {
        PuReturnsDetail productReturnDetail = new PuReturnsDetail();
        User user = UserUtils.getUser();
        productReturnDetail.setCompany(user.getCompany());
        if (null != obj.getQueryAll() && !obj.getQueryAll())
        {
            productReturnDetail.setCreatedBy(user);
        }
        Page<PuReturnsDetail> productPurDetailPage = new Page<PuReturnsDetail>();
        productPurDetailPage.setPageNo(page.getPageNo());
        productPurDetailPage.setPageSize(page.getPageSize());
        if (page.getOrderBy() != null && page.getOrderBy() != "")
        {
            productPurDetailPage.setOrderBy(page.getOrderBy());
        }
        productReturnDetail.setPage(productPurDetailPage);// 设置退货明细page信息
        
        PuReturns returns = new PuReturns();
        // 设置退货单供应商
        if (obj.getSupplier() != null && obj.getSupplier().getRecordId() != null
            && obj.getSupplier().getRecordId() != "")
        {
            returns.setSupplier(obj.getSupplier());
        }
        // 已确认状态的退货
        returns.setStatus(TypeKey.PU_PURETURNS_STATUS_CONFIRMED.toString());
        productReturnDetail.setPuReturns(returns);
        // 采购类型：原料退货 1原料,2成品
        productReturnDetail.setPurchasingType(2);
        productReturnDetail.setDesireTreatment("2");
        productReturnDetail.getPage().setOrderBy("a.createddate DESC");
        // 查询出所有的原料采购明细
        List<PuReturnsDetail> list = returnDetailDao.findReturnsPrdorderListInStock(productReturnDetail);
        List<ForStockObject> objList = new LinkedList<ForStockObject>();
        productPurDetailPage.setList(list);
        if (list != null && list.size() > 0)
        {
            for (PuReturnsDetail returnsDetail : list)
            {
                ForStockObject object = new ForStockObject();
                object.setSaleComId(returnsDetail.getSaleComId());
                object.setParam1(returnsDetail.getPuReturns().getNo());
                // 与送货共用一个数据库字段，送货对应的是其编号,而采购退货对应的是明细ID
                object.setDeliveryNo(returnsDetail.getRecordId());
                // 设置合同编号
                if (returnsDetail.getPrdorderDetail() != null)
                {
                    if (returnsDetail.getPrdorderDetail().getContractDetail() != null)
                    {
                        ProduceBatchDetail produceBatchDetail = new ProduceBatchDetail();
                        Notification notification = new Notification();
                        notification.setContractDetail(returnsDetail.getPrdorderDetail().getContractDetail());
                        produceBatchDetail.setNotification(notification);
                        object.setProduceBatchDetail(produceBatchDetail);
                        
                        // 判断是否有寄存WC 2017-02-23
                        if (returnsDetail.getPrdorderDetail().getContractDetail().getDeposit()
                            && returnsDetail.getPrdorderDetail()
                                .getContractDetail()
                                .getStatus()
                                .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
                        {
                            object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_YES);
                        }
                        else
                        {
                            object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_NO);
                        }
                    }
                }
                
                String materialNo = returnsDetail.getMaterial().getNo();
                Material material = materialDao.findMaterialByNo(new Material(materialNo, user.getCompany()));
                if (material != null)
                {
                    Map<String, Integer> availableStockMaps =
                        this.quaryAvailableStocked(returnsDetail.getQuantity().intValue(),
                            material.getRecordId(),
                            null,
                            null,
                            returnsDetail.getRecordId(),
                            null,
                            UserUtils.getUser().getCompany().getRecordId());
                    Integer availCount = availableStockMaps.get(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_A);
                    if (availCount.compareTo(0) <= 0)
                    {
                        // 已完成出库
                        object.setStatus(ConstKey.IN_OUT_FINISH);
                    }
                    object.setMaterialId(material.getRecordId());
                    object.setMaterialNo(material.getNo());
                    object.setMaterialName(material.getName());
                    object.setSpecification(material.getSpecification());
                    object.setStocks(material.getStocks());
                    object.setCustomerNo(material.getCustomerNo());
                    object.setStorehouse(material.getStorehouse());
                    // 待出库数量
                    object.setAvailCount(availCount);
                    // 已出库数量
                    object.setUnavailCount(availableStockMaps.get(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_UNA));
                    object.setDepositQty(material.getDepositQty() == null ? 0 : material.getDepositQty()); // WC
                                                                                                           // 2017-03-31
                                                                                                           // 寄存数量
                }
                else
                {
                    object.setAvailCount(this.decimalToInteger(returnsDetail.getQuantity().toString()));
                }
                // 退货数量
                object.setAvailSumCount(this.decimalToInteger(returnsDetail.getQuantity().toString()));
                
                // 退货面积和尺寸
                // 退货面积inAreaReport
                Map<String, Object> areaAndPrDimensionMaps = getPuReturnsAreaAndPrDimension(returnsDetail,
                    this.decimalToInteger(returnsDetail.getQuantity().toString()));
                object.setInAreaReport(
                    new BigDecimal(areaAndPrDimensionMaps.get(ConstKey.PRODUCT_IN_OUT_AREA).toString()));
                // 成品尺寸prDimension
                object.setPrDimension((areaAndPrDimensionMaps.get(ConstKey.PRODUCT_IN_OUT_DIMENSION)).toString());
                objList.add(object);
            }
        }
        page.setCount(productPurDetailPage.getCount());
        page.setList(objList);
        return page;
    }
    
    /**
     * 补货入库的数据查询
     * 
     * @param page
     * @param obj
     * @return
     */
    public Page<ForStockObject> queryProductReturnInEnter(Page<ForStockObject> page, ForStockObject obj)
    {
        PuReturnsDetail productReturnDetail = new PuReturnsDetail();
        User user = UserUtils.getUser();
        productReturnDetail.setCompany(user.getCompany());
        if (null != obj.getQueryAll() && !obj.getQueryAll())
        {
            productReturnDetail.setCreatedBy(user);
        }
        Page<PuReturnsDetail> productPurDetailPage = new Page<PuReturnsDetail>();
        productPurDetailPage.setPageNo(page.getPageNo());
        productPurDetailPage.setPageSize(page.getPageSize());
        if (page.getOrderBy() != null && page.getOrderBy() != "")
        {
            productPurDetailPage.setOrderBy(page.getOrderBy());
        }
        productReturnDetail.setPage(productPurDetailPage);// 设置退货明细page信息
        
        PuReturns returns = new PuReturns();
        // 设置补货单供应商
        if (obj.getSupplier() != null && obj.getSupplier().getRecordId() != null
            && obj.getSupplier().getRecordId() != "")
        {
            returns.setSupplier(obj.getSupplier());
        }
        // 已确认状态的退货
        returns.setStatus(TypeKey.PU_PURCHASING_STATUS_AUDITED.toString());
        productReturnDetail.setPuReturns(returns);
        // 采购类型：原料补货 1原料,2成品
        productReturnDetail.setPurchasingType(2);
        productReturnDetail.setDesireTreatment("1");
        // 查询出所有的原料采购明细
        List<PuReturnsDetail> list = returnDetailDao.findReturnsPrdorderListInStock(productReturnDetail);
        List<ForStockObject> objList = new LinkedList<ForStockObject>();
        productPurDetailPage.setList(list);
        if (list != null && list.size() > 0)
        {
            for (PuReturnsDetail returnsDetail : list)
            {
                ForStockObject object = new ForStockObject();
                object.setSaleComId(returnsDetail.getSaleComId());
                object.setParam1(returnsDetail.getPuReturns().getNo());
                // 与送货共用一个数据库字段，送货对应的是其编号,而采购退货对应的是明细ID
                object.setDeliveryNo(returnsDetail.getRecordId());
                // 设置合同编号
                if (returnsDetail.getPrdorderDetail() != null)
                {
                    if (returnsDetail.getPrdorderDetail().getContractDetail() != null)
                    {
                        ProduceBatchDetail produceBatchDetail = new ProduceBatchDetail();
                        Notification notification = new Notification();
                        notification.setContractDetail(returnsDetail.getPrdorderDetail().getContractDetail());
                        produceBatchDetail.setNotification(notification);
                        object.setProduceBatchDetail(produceBatchDetail);
                        // WC 2017-03-29 修复有补货入库有寄存时保存寄存记录报错问题
                        object.setContractDetailId(returnsDetail.getPrdorderDetail().getContractDetail().getRecordId());
                        if (returnsDetail.getPrdorderDetail().getContractDetail().getContract() != null)
                        {
                            object.setCustomerId(returnsDetail.getPrdorderDetail()
                                .getContractDetail()
                                .getContract()
                                .getCustomer()
                                .getRecordId());
                        }
                        // 判断是否有寄存WC 2017-02-23
                        if (returnsDetail.getPrdorderDetail().getContractDetail().getDeposit()
                            && returnsDetail.getPrdorderDetail()
                                .getContractDetail()
                                .getStatus()
                                .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
                        {
                            object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_YES);
                        }
                        else
                        {
                            object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_NO);
                        }
                    }
                }
                
                String materialNo = returnsDetail.getMaterial().getNo();
                Material material = materialDao.findMaterialByNo(new Material(materialNo, user.getCompany()));
                if (material != null)
                {
                    Map<String, Integer> availableStockMaps =
                        this.quaryAvailableStocked(returnsDetail.getQuantity().intValue(),
                            material.getRecordId(),
                            null,
                            null,
                            returnsDetail.getRecordId(),
                            null,
                            UserUtils.getUser().getCompany().getRecordId());
                    Integer availCount = availableStockMaps.get(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_A);
                    SourceDetection sd = new SourceDetection();
                    sd.setOrderId(returnsDetail.getRecordId());
                    sd.setCompany(user.getCompany());
                    sd.setResourceType("4");
                    sd.setStatus(TypeKey.ST_SOURCEDETECTION_STATUS_AUDITED);
                    
                    SourceDetection detection = sourceDetectionDao.getNumSum(sd);
                    BigDecimal poorCount = detection.getPoorNum() != null ? detection.getPoorNum() : BigDecimal.ZERO;
                    if (poorCount == null)
                    {
                        poorCount = BigDecimal.ZERO;
                    }
                    // ycy 2016-10-10 成品来料检测数量
                    // ycy 2016-10-10 成品来料检测数量
                    BigDecimal detectionNum =
                        detection.getDetectionNum() != null ? detection.getDetectionNum() : BigDecimal.ZERO;
                    if (detectionNum == null)
                    {
                        detectionNum = BigDecimal.ZERO;
                    }
                    // ycy 2016-10-10 待入库数量
                    BigDecimal detectionNums =
                        new BigDecimal(MathUtils.sub(detectionNum.doubleValue(), poorCount.doubleValue(), 0));
                    // ycy 2016-10-10 待入库数量==0 不加载
                    if (detectionNums.intValue() <= 0)
                    {
                        continue;
                    }
                    // 待入库数量需要减去不良数量
                    availCount = availCount - poorCount.intValue();
                    if (availCount.compareTo(0) <= 0)
                    {
                        // 已完成出库
                        object.setStatus(ConstKey.IN_OUT_FINISH);
                    }
                    object.setMaterialId(material.getRecordId());
                    object.setMaterialNo(material.getNo());
                    object.setMaterialName(material.getName());
                    object.setSpecification(material.getSpecification());
                    object.setStocks(material.getStocks());
                    object.setCustomerNo(material.getCustomerNo());
                    object.setStorehouse(material.getStorehouse());
                    // 待出库数量
                    object.setAvailCount(detectionNums.intValue());
                    object.setPrdPoorCount(poorCount);
                    // 赠品数量
                    object.setGiveNum(detection.getGiveNum() != null ? detection.getGiveNum() : BigDecimal.ZERO);
                    // 已出库数量
                    object.setUnavailCount(availableStockMaps.get(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_UNA));
                }
                else
                {
                    object.setAvailCount(this.decimalToInteger(returnsDetail.getQuantity().toString()));
                }
                // 退货数量
                object.setAvailSumCount(this.decimalToInteger(returnsDetail.getQuantity().toString()));
                
                // 补货面积和尺寸
                // 补货面积inAreaReport
                Map<String, Object> areaAndPrDimensionMaps = getPuReturnsAreaAndPrDimension(returnsDetail,
                    this.decimalToInteger(returnsDetail.getQuantity().toString()));
                object.setInAreaReport(
                    new BigDecimal(areaAndPrDimensionMaps.get(ConstKey.PRODUCT_IN_OUT_AREA).toString()));
                // 成品尺寸prDimension
                object.setPrDimension((areaAndPrDimensionMaps.get(ConstKey.PRODUCT_IN_OUT_DIMENSION)).toString());
                objList.add(object);
            }
        }
        page.setCount(productPurDetailPage.getCount());
        page.setList(objList);
        return page;
    }
    
    // 计算退货出库面积和尺寸
    public Map<String, Object> getPuReturnsAreaAndPrDimension(PuReturnsDetail puReturnsDetail, Integer availCount)
    {
        
        // 成品退货面积inAreaReport
        // 成品尺寸prDimension
        Map<String, Object> areaAndPrDimensionMaps = new HashMap<String, Object>();
        BigDecimal area = BigDecimal.ZERO;
        String prDemesion = "";
        
        if (puReturnsDetail.getPrdorderDetail() != null
            && puReturnsDetail.getPrdorderDetail().getContractCraft() != null)
        {
            ProductCraft productCraft = puReturnsDetail.getPrdorderDetail().getContractCraft();
            if (productCraft.getDeliverySize() != null && productCraft.getPnlDivisor() != null)
            {
                // 就按照交货尺寸来算面积
                String[] arrayForLengthAndWidth = productCraft.getDeliverySize().split(",");
                // 尺寸
                String length = arrayForLengthAndWidth[0];
                String width = arrayForLengthAndWidth[1];
                prDemesion = length + "x" + width + "/" + productCraft.getPnlDivisor();
                
                // 面积
                area = new BigDecimal(length).multiply(new BigDecimal(width))
                    .multiply(new BigDecimal(availCount))
                    .divide(productCraft.getPnlDivisor(), 4, BigDecimal.ROUND_HALF_UP)
                    .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
                
            }
            else if (productCraft.getPnlLength() != null && productCraft.getPnlLength().compareTo(BigDecimal.ZERO) == 1
                && productCraft.getPnlWidth() != null && productCraft.getPnlWidth().compareTo(BigDecimal.ZERO) == 1
                && productCraft.getPnlDivisor() != null)
            {
                // 采购尺寸不为空
                BigDecimal pnlLength = productCraft.getPnlLength();
                BigDecimal pnlWidth = productCraft.getPnlWidth();
                BigDecimal pnlDivisor = productCraft.getPnlDivisor();
                // 采购尺寸
                prDemesion = pnlLength + "x" + pnlWidth + "/ " + pnlDivisor;
                
                area = pnlLength.multiply(pnlWidth)
                    .multiply(new BigDecimal(availCount))
                    .divide(pnlDivisor, 4, BigDecimal.ROUND_HALF_UP)
                    .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
            }
            else if (productCraft.getUnitLength() != null && productCraft.getUnitWidth() != null)
            {
                // 采购尺寸为空,取单位尺寸
                BigDecimal unitLength = productCraft.getUnitLength();
                BigDecimal unitWidth = productCraft.getUnitWidth();
                area = unitLength.multiply(unitWidth)
                    .multiply(new BigDecimal(availCount))
                    .divide(productCraft.getPnlDivisor(), 4, BigDecimal.ROUND_HALF_UP)
                    .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
                
                // 采购尺寸
                prDemesion = unitLength + "x" + unitWidth + "/ " + productCraft.getPnlDivisor();
            }
            
        }
        areaAndPrDimensionMaps.put(ConstKey.PRODUCT_IN_OUT_AREA, area);
        areaAndPrDimensionMaps.put(ConstKey.PRODUCT_IN_OUT_DIMENSION, prDemesion);
        
        return areaAndPrDimensionMaps;
    }
    
    /**
     * 初始所有已投料的通知单入库信息
     * 
     * @param page
     * @return
     */
    public List<Notification> findNotificationListInStock()
    {
        Notification notifi = new Notification();
        notifi.setCompany(UserUtils.getUser().getCompany());
        notifi.setStatus(TypeKey.SL_NOTIFICATION_STATUS_FEED.toString());
        // 查询出已投料的通知单
        return notifiDao.findNotificationListInStock(notifi);
    }
    
    /**
     * 初始所有已投料的批次入库信息
     * 
     * @param page
     * @return
     */
    public List<ProduceBatchDetail> findProduceBatchDetailListInStock(Boolean queryAll)
    {
        Notification notiSreach = new Notification();
        User user = UserUtils.getUser();
        if (null != queryAll && !queryAll)
        {
            notiSreach.setCreatedBy(user);
        }
        notiSreach.setCompany(user.getCompany());
        // 通知单已投料状态
        notiSreach.setStatus(TypeKey.SL_NOTIFICATION_STATUS_FEED.toString());
        // 批次明细非已入库状态
        notiSreach.setStatus3(TypeKey.PRODUCE_BATCH_STATUS_OPERATION.toString());
        // 查询出已投料的批次
        return batchDetailDao.findProduceBatchDetailListInStock(notiSreach);
    }
    
    /**
     * 生产入库的分页查看所有的通知单和物料
     * 
     * @param page
     * @return
     */
    public Page<ForStockObject> queryNotifaMaterials(Page<ForStockObject> page, ForStockObject obj)
    {
        Notification notifi = new Notification();
        Company company = UserUtils.getUser().getCompany();
        notifi.setCompany(company);
        Page<Notification> notifiPage = new Page<Notification>();
        notifiPage.setPageNo(page.getPageNo());
        notifiPage.setPageSize(page.getPageSize());
        if (page.getOrderBy() != null && page.getOrderBy() != "")
        {
            notifiPage.setOrderBy(page.getOrderBy());
        }
        notifi.setPage(notifiPage);
        notifi.setStatus(TypeKey.SL_NOTIFICATION_STATUS_FEED.toString());
        if (obj.getParam1() != null && !"".equals(obj.getParam1()) && !"所有".equals(obj.getParam1()))
        {
            notifi.setNo(obj.getParam1().trim());
        }
        // 查询出已投料的通知单
        List<Notification> list = notifiDao.findAllFeedInStock(notifi);
        notifiPage.setList(list);
        List<ForStockObject> objList = new LinkedList<ForStockObject>();
        if (list != null && list.size() > 0)
        {
            for (Notification noti : list)
            {
                ForStockObject object = new ForStockObject();
                object.setParam1(noti.getNo());
                // 总报废数量
                Integer failedPcsQty = 0;
                // 总投料数量
                Integer feedPcsQty = 0;
                String notificationId = "";
                if (noti.getMergeType().compareTo(TypeKey.SL_NOTIFICATION_MERGE_NORMAL) == 0)
                {
                    // 正常通知单入库
                    feedPcsQty = noti.getFeedPcsQty() == null ? 0 : noti.getFeedPcsQty();
                    notificationId = noti.getRecordId();
                    // 总报废数量
                    failedPcsQty = noti.getFailedPcsQty() == null ? 0 : noti.getFailedPcsQty();
                }
                else if (noti.getMergeType().compareTo(TypeKey.SL_NOTIFICATION_BE_MERGED) == 0)
                {
                    // 查询合单信息
                    noti.setMergeType(TypeKey.SL_NOTIFICATION_MERGE_NEW);
                    List<Notification> notifications = notifiDao.findMergeNotiToValiInStrock(noti);
                    if (notifications != null && notifications.size() > 0)
                    {
                        // 判断是否过数
                        Notification beMergedNoti = notifications.get(0);
                        feedPcsQty = beMergedNoti.getFeedPcsQty();
                        notificationId = beMergedNoti.getRecordId();
                        // 总报废数量
                        failedPcsQty = beMergedNoti.getFailedPcsQty() == null ? 0 : beMergedNoti.getFailedPcsQty();
                    }
                    else
                    {
                        feedPcsQty = Integer.valueOf(noti.getQuantity());
                    }
                }
                Material material = materialDao.findMaterialByNo(new Material(noti.getCraftNo(), company));
                if (material != null)
                {
                    Map<String, Integer> availableStockMaps = this.quaryAvailableStocked(feedPcsQty,
                        null,
                        notificationId,
                        null,
                        null,
                        null,
                        noti.getCompany().getRecordId());
                    object.setMaterialId(material.getRecordId());
                    object.setMaterialNo(material.getNo());
                    object.setStatus(noti.getMergeType().toString());
                    object.setMaterialName(material.getName());
                    object.setSpecification(material.getSpecification());
                    object.setStocks(material.getStocks());
                    object.setStorehouse(material.getStorehouse());
                    // 投料单总数量-已入库的数量-报废数量 = 通知单：待入库数量
                    object.setAvailCount(availableStockMaps.get(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_A) - failedPcsQty);
                }
                else
                {
                    // 物料第一次入库才被创建，从来没入库的将通知单的工艺编号作为物料编号保存
                    object.setMaterialNo(noti.getCraftNo());
                    object.setAvailCount(feedPcsQty - failedPcsQty);
                }
                Integer completedQty = noti.getCompletedQty() == null ? 0 : noti.getCompletedQty();
                // 已入库数量
                object.setUnavailCount(completedQty);
                // 通知单总数
                String notiQuantity = noti.getQuantity() == null ? "0" : noti.getQuantity();
                object.setAvailSumCount(Integer.parseInt(notiQuantity));
                // 报废数量
                object.setReturnsCount(failedPcsQty);
                object.setNotificationId(noti.getRecordId());
                if (null != noti.getContractDetail())
                {
                    object.setContractDetailId(noti.getContractDetail().getRecordId());
                    if (null != noti.getContractDetail().getContract()
                        && null != noti.getContractDetail().getContract().getCustomer())
                    {
                        object.setCustomerNo(noti.getContractDetail().getContract().getCustomer().getNo());
                        object.setCustomerId(noti.getContractDetail().getContract().getCustomer().getRecordId());
                    }
                }
                objList.add(object);
            }
        }
        page.setCount(notifiPage.getCount());
        page.setList(objList);
        return page;
    }
    
    /**
     * 生产入库:查询所有待入库的批次明细
     * 
     * @param page
     * @return
     */
    public Page<ForStockObject> findNotificationBatchDetails(Page<ForStockObject> page, ForStockObject obj)
    {
        // 查询所有
        ProduceBatchDetail ppdSreach = new ProduceBatchDetail();
        User user = UserUtils.getUser();
        if (null != obj.getQueryAll() && !obj.getQueryAll())
        {
            ppdSreach.setCreatedBy(user);
        }
        ppdSreach.setCompany(user.getCompany());
        Page<ProduceBatchDetail> ppdPage = new Page<ProduceBatchDetail>();
        ppdPage.setPageNo(page.getPageNo());
        ppdPage.setPageSize(page.getPageSize());
        if (page.getOrderBy() != null && page.getOrderBy() != "")
        {
            ppdPage.setOrderBy(page.getOrderBy());
        }
        ppdSreach.setPage(ppdPage);
        Notification notiSreach = new Notification();
        // 通知单已投料状态
        notiSreach.setStatus(TypeKey.SL_NOTIFICATION_STATUS_FEED.toString());
        // 投料单已确认状态
        notiSreach.setStatus1(TypeKey.PD_FEEDING_STATUS_CONFIRMED.toString());
        // 补料单已确认状态
        notiSreach.setStatus2(TypeKey.PD_REPLENISH_STATUS_ACKNOWLEDGED.toString());
        // 批次明细非已入库状态
        notiSreach.setStatus3(TypeKey.PRODUCE_BATCH_STATUS_OPERATION.toString());
        // 不需要查出被合单
        notiSreach.setMergeType(TypeKey.SL_NOTIFICATION_BE_MERGED);
        if (null != obj.getParam1() && !"".equals(obj.getParam1()) && !"所有".equals(obj.getParam1()))
        {
            notiSreach.setNo(obj.getParam1().trim());
        }
        ppdSreach.setNotification(notiSreach);
        notiSreach.setCompany(user.getCompany());
        // 查询出所有未入库的批次明细
        List<ProduceBatchDetail> produceBatchDetailList = batchDetailDao.findProduceBatchDetails(ppdSreach);
        ppdPage.setList(produceBatchDetailList);
        List<ForStockObject> objList = new LinkedList<ForStockObject>();
        if (produceBatchDetailList != null && produceBatchDetailList.size() > 0)
        {
            for (ProduceBatchDetail ppd : produceBatchDetailList)
            {
                Notification noti = ppd.getNotification();
                ForStockObject object = new ForStockObject();
                object.setParam1(noti.getNo());
                // 总报废数量
                Integer failedPcsQty = ppd.getDiscardQty() == null ? 0 : Integer.valueOf(ppd.getDiscardQty());
                // 待入库数量
                Integer availablePcsQty = ppd.getQtyPcsT() == null ? 0 : ppd.getQtyPcsT();
                // 通知单总数
                Integer notiQuantity = noti.getQuantity() == null ? 0 : Integer.valueOf(noti.getQuantity());
                // 合单已入库数量
                Integer completedQty = 0;
                if (noti.getMergeType().compareTo(TypeKey.SL_NOTIFICATION_MERGE_NEW) == 0)
                {
                    // 1.查询已入库数量:查询该合单的库存出入记录
                    ProductStore mergedProductStore = new ProductStore();
                    mergedProductStore.setCompany(UserUtils.getUser().getCompany());
                    mergedProductStore.setActiveFlag(ProductStore.DEL_FLAG_NORMAL);
                    // 不查出冲红单数据
                    mergedProductStore.setStatus(TypeKey.BILL_STATUS_FOR_WRITEOFF);
                    mergedProductStore.setNotificationId(noti.getRecordId());
                    mergedProductStore.setProduceBatchDetail(ppd);
                    // 根据公司,通知单,批次明细查询正常入库和叉板入库总数量
                    completedQty = pstoreDao.quaryStockedAndxboardByPbd(mergedProductStore);
                    if (completedQty == null)
                    {
                        completedQty = 0;
                    }
                    // 已入库数量
                    object.setUnavailCount(completedQty);
                    object.setAvailCount(availablePcsQty - failedPcsQty - completedQty);
                }
                else
                {
                    object.setAvailCount(availablePcsQty - failedPcsQty);
                }
                object.setMaterialNo(noti.getCraftNo());
                object.setStatus(noti.getMergeType().toString());
                if (null != ppd.getMaterial() && null != ppd.getMaterial().getRecordId())
                {
                    object.setMaterialId(ppd.getMaterial().getRecordId());
                    object.setMaterialName(ppd.getMaterial().getName());
                    object.setCustomerNo(ppd.getMaterial().getCustomerNo());
                    object.setSpecification(ppd.getMaterial().getSpecification());
                    object.setStocks(ppd.getMaterial().getStocks());
                    object.setStorehouse(ppd.getMaterial().getStorehouse());
                    object.setXboardQty(ppd.getMaterial().getXboardQty());
                }
                // 该批次过数结果-报废数量 = 通知单：待入库数量
                object.setAvailSumCount(notiQuantity);
                // 报废数量
                object.setReturnsCount(failedPcsQty);
                // 批次明细
                object.setNotificationId(noti.getRecordId());
                object.setProduceBatchDetail(ppd);
                
                /* ojh 2016.9.23 增加生产入库面积和生产入库尺寸主要用于界面上可看到 begin */
                
                ContractDetail contractDetail = noti.getContractDetail();
                Notification notification = notifiDao.get(noti.getRecordId());
                String processCardAId = notification.getCardA().getRecordId();
                
                CardA cardA = cardADao.get(processCardAId);
                // 生产入库面积
                BigDecimal inAreaReport = getArea(contractDetail,
                    object.getAvailCount(),
                    processCardAId,
                    object.getMaterialNo(),
                    ConstKey.IN_OUT_AREA_TYPE_TOTAL,
                    ppd.getRecordId());
                // 生产入库尺寸 流程卡交货尺寸
                String prDimension = cardA.getSetLength() + "x" + cardA.getSetWidth() + " / " + cardA.getPnlDivisor();
                object.setInAreaReport(inAreaReport);
                object.setPrDimension(prDimension);
                /* ojh 2016.9.23 增加生产入库面积和生产入库尺寸主要用于界面上可看到 end */
                
                // WC 2017-01-18 判断是否寄存，前台不再判断
                // 1.区分生产入库的是投料单还是补料单
                if (ppd.getBatchDetailType().toString().equals(TypeKey.PRODUCE_BATCH_OPERATION_TYPE_FEED.toString()))
                {
                    if (contractDetail != null && contractDetail.getDeposit() != null
                        && contractDetail.getStatus() != null)
                    {
                        if (contractDetail.getDeposit() && contractDetail.getStatus()
                            .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
                        {
                            object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_YES);
                        }
                        else
                        {
                            object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_NO);
                        }
                    }
                }
                else
                {
                    // WC 2017-04-26 区分补料单是客诉补料还是正常补料
                    if (ppd.getFeeding() != null && ppd.getFeeding().getRecordId() != null)
                    {
                        Integer replenishType = replenishDao.getReplenishType(ppd.getFeeding().getRecordId());
                        if (replenishType != null && replenishType == 1)
                        {
                            // 正常补料单
                            if (contractDetail != null && contractDetail.getDeposit() != null
                                && contractDetail.getStatus() != null)
                            {
                                if (contractDetail.getDeposit() && contractDetail.getStatus()
                                    .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
                                {
                                    object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_YES);
                                }
                                else
                                {
                                    object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_NO);
                                }
                            }
                        }
                        else
                        {
                            object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_NO);
                        }
                    }
                    else
                    {
                        object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_NO);
                    }
                    
                }
                
                objList.add(object);
            }
        }
        page.setCount(ppdPage.getCount());
        page.setList(objList);
        return page;
    }
    
    /**
     * 如果是第一次入库则添加物料 如果不是第一次，修改库存数量 MD WC 2017-04-26 相同生产编号合单生产入库特殊处理
     */
    public int modifyMaterialStocks(ProductStore product, Material material, Notification not)
    {
        if (material == null || material.getRecordId() == null || material.getRecordId() == "")
        {
            material = new Material();
            material.setNo(not.getCraftNo());
            material.setCompany(product.getCompany());
            material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_PRODUCT);
            Material mc = materialDao.getMaterialByNo(material);
            if (null == mc)
            {
                material.setStorehouse(product.getStorehouse());
                material.setAvailableQty(BigDecimal.ZERO);
                material.setStocks(new BigDecimal(product.getQuantity()));
                if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_RETURN) == 0)
                {
                    material.setAvailableQty(new BigDecimal(product.getQuantity()));
                }
                material.setXboardQty(product.getXboardQty());
                material.setMaterialType(returnMaterialType());
                material.preInsert();
                materialDao.insert(material);
                product.setMaterial(material);
            }
            else
            {
                product.setMaterial(mc);
            }
        }
        else
        {
            material.setCompany(product.getCompany());
            material.setStorehouse(product.getStorehouse());
            if (material.getStocks() == null)
            {
                material.setStocks(BigDecimal.ZERO);
            }
            
            // 送货出库
            if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_OUT) == 0)
            {
                // 如果出库数大于库存量
                if (material.getStocks().compareTo(new BigDecimal(product.getQuantity())) < 0)
                {
                    return 3;
                }
                else
                {
                    // 库存数量 = 库存数量 - 送货数量
                    material.setStocks(material.getStocks().subtract(new BigDecimal(product.getQuantity())));
                    // 获取送货单 WC 2017-02-07
                    Delivery delivery = deliveryDao.getDeliveryByDetailId(product.getDeliveryDetailId());
                    if (delivery != null && delivery.getItemType() == 3)
                    {
                        // 是寄存送货单 更新占用寄存数量、库存数量
                        material.setDepositOaQty(product.getQuantity());
                        materialDao.updateStocksAndDepositOaQty(material);
                    }
                    else if (delivery != null && delivery.getItemType() == 1)
                    {
                        // 正常送货单
                        material.setDeliveryOaQty(product.getQuantity());
                        materialDao.updateStocksAndDdlOaQty(material);
                    }
                    else if (delivery != null && delivery.getItemType() == 2)
                    {
                        // 客诉单
                        RejectApplication complaint =
                            rejectApplicationDao.getComplaintByDelDtlId(product.getDeliveryDetailId());
                        if (complaint.getStyle().equals(TypeKey.COMPLAINT_STYLE_DEPOSIT))
                        {
                            // 若是寄存客诉单则更新送货占用寄存数量、库存数量
                            material.setDepositOaQty(product.getQuantity());
                            materialDao.updateStocksAndDepositOaQty(material);
                        }
                        else
                        {
                            material.setDeliveryOaQty(product.getQuantity());
                            materialDao.updateStocksAndDdlOaQty(material);
                        }
                    }
                }
            }
            // 采购退货出库
            else if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_OUT) == 0)
            {
                // 如果出库数大于库存量
                if (material.getStocks().compareTo(new BigDecimal(product.getQuantity())) < 0)
                {
                    return 3;
                }
                else
                {
                    if (product.getIsDeposit() != null && product.getIsDeposit())
                    {
                        // 如采购有寄存，则还要减掉寄存数量
                        material
                            .setStocks(new BigDecimal(product.getQuantity().toString()).multiply(new BigDecimal("-1")));
                        material.setDepositQty(product.getQuantity() * (-1));
                        material.setXboardQty(0);
                        materialDao.updateDepositAndXboardQty(material);
                    }
                    else
                    {
                        // 如果是成品出库 库存数量=原有库存数量-出库数量
                        material.setStocks(material.getStocks().subtract(new BigDecimal(product.getQuantity())));
                        materialDao.updateStocks(material);
                    }
                }
            }
            else if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_ENTER) == 0)
            {
                if (product.getIsDeposit() != null && product.getIsDeposit())
                {
                    // 如采购有寄存
                    material.setStocks(new BigDecimal(product.getQuantity() == null ? 0 : product.getQuantity()));
                    material.setDepositQty(product.getQuantity());
                    material.setXboardQty(0);
                    materialDao.updateDepositAndXboardQty(material);
                    
                    // 保存物料寄存記錄WC 2017-03-23
                    MaterialDepositRecord mdr = new MaterialDepositRecord();
                    mdr.setCompany(UserUtils.getUser().getCompany());
                    mdr.setMaterial(material);
                    mdr.setContractDetail(product.getContractDetail());
                    mdr.setCustomer(product.getCustomer());
                    mdr.setQuantity(product.getQuantity());
                    mdr.setType(TypeKey.DEPOSIT_TIME_REPLENISHMENT_INHOUSE); // 寄存時機：補貨入庫
                    mdr.preInsert();
                    mdrDao.insert(mdr);
                }
                else
                {
                    // 如果是成品出库 库存数量=原有库存数量+入库数量
                    material.setStocks(material.getStocks().add(new BigDecimal(product.getQuantity())));
                    materialDao.updateStocks(material);
                }
            }
            else
            {
                // 客诉退货入库
                if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_RETURN) == 0)
                {
                    // 如果是退货，需将库存金额改成负数
                    BigDecimal amount = product.getAmount() == null ? new BigDecimal(0) : product.getAmount();
                    product.setAmount(amount.multiply(new BigDecimal(-1)));
                    if (product.getIsDeposit() != null && product.getIsDeposit())
                    { // 如果客诉的是寄存送货单则退货还原到寄存数量中WC 2017-02-16
                        material.setStocks(new BigDecimal(product.getQuantity() == null ? 0 : product.getQuantity()));// 现库存数量
                        material.setDepositQty(product.getQuantity() == null ? 0 : product.getQuantity());// 寄存数量
                        material.setXboardQty(0);// 打叉板数量
                        material.preUpdate();
                        materialDao.updateDepositAndXboardQty(material);
                        
                        // 保存物料寄存記錄WC 2017-03-23
                        MaterialDepositRecord mdr = new MaterialDepositRecord();
                        mdr.setCompany(UserUtils.getUser().getCompany());
                        mdr.setMaterial(material);
                        mdr.setContractDetail(product.getContractDetail());
                        mdr.setCustomer(product.getCustomer());
                        mdr.setQuantity(product.getQuantity());
                        mdr.setType(TypeKey.DEPOSIT_TIME_BACKGOODS_INHOUSE); // 寄存時機：退貨入庫
                        mdr.preInsert();
                        mdrDao.insert(mdr);
                    }
                    else
                    {
                        // 退货入库，自动转为库存可用数量
                        BigDecimal availableQty =
                            material.getAvailableQty() == null ? BigDecimal.ZERO : material.getAvailableQty();
                        material.setAvailableQty(new BigDecimal(product.getQuantity()).add(availableQty));
                        material.setStocks(new BigDecimal(product.getQuantity()).add(material.getStocks()));
                        materialDao.updateStocksAndAvailableQtyToo(material);
                    }
                }
                // 生产入库
                else if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_IN) == 0)
                {
                    // WC 2017-04-26 判断此次入库的是否是相同生产编号合单
                    if (not.getMergeType().compareTo(TypeKey.SL_NOTIFICATION_MERGE_NEW) == 0)
                    {
                        if (StringUtils.isNotBlank(product.getBatchDetailCraftId()))
                        {
                            if (product.getIsDeposit() != null && product.getIsDeposit())
                            { // 如果生产入库：是寄存WC 2017-01-19
                                material.setStocks(
                                    new BigDecimal(product.getQuantity() == null ? 0 : product.getQuantity()));// 现库存数量
                                material.setDepositQty(product.getQuantity() == null ? 0 : product.getQuantity());// 寄存数量
                                material.setXboardQty(product.getXboardQty() == null ? 0 : product.getXboardQty());// 打叉板数量
                                material.preUpdate();
                                materialDao.updateDepositAndXboardQty(material);
                                
                                // 保存物料寄存記錄WC 2017-03-23
                                MaterialDepositRecord mdr = new MaterialDepositRecord();
                                mdr.setCompany(UserUtils.getUser().getCompany());
                                mdr.setMaterial(material);
                                mdr.setContractDetail(product.getContractDetail());
                                mdr.setCustomer(product.getCustomer());
                                mdr.setQuantity(product.getQuantity());
                                mdr.setType(TypeKey.DEPOSIT_TIME_PRODUCE_INHOUSE); // 寄存時機：生产入庫
                                mdr.preInsert();
                                mdrDao.insert(mdr);
                            }
                            else
                            {
                                // 如果是生产入库 库存数量 = 入库数量 + 原有库存数量 打叉板数量 = 打叉板数量 + 原有打叉板数量
                                material.setStocks(new BigDecimal(product.getQuantity()).add(material.getStocks()));
                                Integer materialxboardQty =
                                    material.getXboardQty() == null ? 0 : material.getXboardQty();
                                material.setXboardQty(materialxboardQty + product.getXboardQty());
                                materialDao.updateStocksAndxboardQty(material);
                            }
                        }
                        else
                        {
                            // 如果是生产入库 库存数量 = 入库数量 + 原有库存数量 打叉板数量 = 打叉板数量 + 原有打叉板数量
                            material.setStocks(new BigDecimal(product.getQuantity()).add(material.getStocks()));
                            Integer materialxboardQty = material.getXboardQty() == null ? 0 : material.getXboardQty();
                            material.setXboardQty(materialxboardQty + product.getXboardQty());
                            materialDao.updateStocksAndxboardQty(material);
                        }
                    }
                    else
                    {
                        if (product.getIsDeposit() != null && product.getIsDeposit())
                        { // 如果生产入库：是寄存WC 2017-01-19
                            material
                                .setStocks(new BigDecimal(product.getQuantity() == null ? 0 : product.getQuantity()));// 现库存数量
                            material.setDepositQty(product.getQuantity() == null ? 0 : product.getQuantity());// 寄存数量
                            material.setXboardQty(product.getXboardQty() == null ? 0 : product.getXboardQty());// 打叉板数量
                            material.preUpdate();
                            materialDao.updateDepositAndXboardQty(material);
                            
                            // 保存物料寄存記錄WC 2017-03-23
                            MaterialDepositRecord mdr = new MaterialDepositRecord();
                            mdr.setCompany(UserUtils.getUser().getCompany());
                            mdr.setMaterial(material);
                            mdr.setContractDetail(product.getContractDetail());
                            mdr.setCustomer(product.getCustomer());
                            mdr.setQuantity(product.getQuantity());
                            mdr.setType(TypeKey.DEPOSIT_TIME_PRODUCE_INHOUSE); // 寄存時機：生产入庫
                            mdr.preInsert();
                            mdrDao.insert(mdr);
                        }
                        else
                        {
                            // 如果是生产入库 库存数量 = 入库数量 + 原有库存数量 打叉板数量 = 打叉板数量 + 原有打叉板数量
                            material.setStocks(new BigDecimal(product.getQuantity()).add(material.getStocks()));
                            Integer materialxboardQty = material.getXboardQty() == null ? 0 : material.getXboardQty();
                            material.setXboardQty(materialxboardQty + product.getXboardQty());
                            materialDao.updateStocksAndxboardQty(material);
                        }
                    }
                }
                // 采购入库
                else
                {
                    // 库存数量=入库数量+原有库存数量 【采购入库也要考虑有寄存】
                    if (product.getIsDeposit() != null && product.getIsDeposit())
                    {
                        material.setStocks(new BigDecimal(product.getQuantity() == null ? 0 : product.getQuantity()));// 现库存数量
                        material.setDepositQty(product.getQuantity());// 寄存数量
                        material.setXboardQty(0);// 打叉板数量
                        material.preUpdate();
                        materialDao.updateDepositAndXboardQty(material);
                        
                        // 保存物料寄存記錄WC 2017-03-23
                        MaterialDepositRecord mdr = new MaterialDepositRecord();
                        mdr.setCompany(UserUtils.getUser().getCompany());
                        mdr.setMaterial(material);
                        mdr.setContractDetail(product.getContractDetail());
                        mdr.setCustomer(product.getCustomer());
                        mdr.setQuantity(product.getQuantity());
                        mdr.setType(TypeKey.DEPOSIT_TIME_PURCHASING_INHOUSE); // 寄存時機：采购入庫
                        mdr.preInsert();
                        mdrDao.insert(mdr);
                    }
                    else
                    {
                        material.setStocks(new BigDecimal(product.getQuantity()).add(material.getStocks()));
                        materialDao.updateStocks(material);
                    }
                }
            }
        }
        return 0;
    }
    
    public void saveProductStore(ProductStore product)
    {
        
        if (product.getStatus() == null)
        {
            product.setStatus(TypeKey.BILL_STATUS_NORMAL);
        }
        
        if (product.getSupplier() != null && StringUtils.isBlank(product.getSupplier().getRecordId()))
        {
            product.setSupplier(null);
        }
        
        product.preInsert();
        pstoreDao.insert(product);
    }
    
    /**
     * 2016.10.14 ojh
     * 
     * @param contractDetail 合同明细
     * @param quantity 出入库数量
     * @param processCardAId 流程卡id
     * @param materialNo 物料编号
     * @param inOutAreaType 合单切割出入库总面积计算，合单切割出入库其中的一个单个面积计算
     * @param produceBatchDetailId 批次明细id，用来查询批次明细对应的批次明细工艺
     */
    public BigDecimal getArea(ContractDetail contractDetail, Integer quantity, String processCardAId, String materialNo,
        String inOutAreaType, String produceBatchDetailId)
    {
        BigDecimal area = BigDecimal.ZERO;
        if (null == processCardAId)
        {
            return area;
        }
        // 查询该通知单工程卡资料
        CardA ka = new CardA();
        ka.setRecordId(processCardAId);
        CardA cardA = cardADao.getCardaAndBoardPartCraftListById(ka);// 查询cardA并且关联去查找eg_cardb_board_part_craft里面的工艺集合
        if (cardA != null)
        {
            // 拼版数
            BigDecimal pnlDiv = BigDecimal.ZERO;
            List<BoardPartCraft> boardPartCraftList = cardA.getBoardPartCraftList();// 板切割后的资料对应工艺集合
            if (boardPartCraftList == null || boardPartCraftList.size() == 0) // 不用切割，就是同一个型号的合单，所有数量都是同一个型号
            {
                pnlDiv = new BigDecimal(cardA.getPnlDivisor());
                // 计算入库面积
                area = (new BigDecimal(quantity).multiply(cardA.getSetLength().multiply(cardA.getSetWidth())))
                    .divide(pnlDiv, 4, BigDecimal.ROUND_HALF_UP)
                    .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
            }
            else// 切割
            {
                /* 合单切割出入库其中的一个单个面积计算 begin */
                if (inOutAreaType != null && inOutAreaType.equals(ConstKey.IN_OUT_AREA_TYPE_ONE))
                {
                    for (BoardPartCraft bpc : boardPartCraftList)
                    {
                        if (bpc.getCraftNo().equals(materialNo))
                        {
                            pnlDiv = new BigDecimal(bpc.getPnlDivisor());
                            // 计算该合单的入库面积
                            area = (new BigDecimal(quantity).multiply(
                                new BigDecimal(bpc.getSetLength()).multiply(new BigDecimal(bpc.getSetWidth()))))
                                    .divide(pnlDiv, 4, BigDecimal.ROUND_HALF_UP)
                                    .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
                            break;
                        }
                    }
                }
                /* 合单切割出入库其中的一个单个面积计算 end */
                
                /* 合单切割出入库总面积计算 begin */
                if (inOutAreaType != null && inOutAreaType.equals(ConstKey.IN_OUT_AREA_TYPE_TOTAL))
                {
                    /* 需要切割，这样子就需要去pd_produce_batch_detail_craft里面去查每一个工艺对应的数量了 尺寸就用eg_cardb_board_part_craft里面的set */
                    BigDecimal _area = BigDecimal.ZERO;
                    
                    /* 根据批次明细获取对应的所有批次明细的工艺 begin */
                    ProduceBatchDetail produceBatchDetail = new ProduceBatchDetail(produceBatchDetailId);
                    produceBatchDetail.setCompany(UserUtils.getUser().getCompany());
                    
                    /*
                     * 切割工艺，有几个通知单就有几个切割工艺明细，不管是不是相同craftno工艺，所以有可能其中两个工艺或者几个工艺相同 批次工艺，为合同明细工艺=对应的通知单的工艺。
                     */
                    List<ProduceBatchDetailCraft> produceBatchDetailCraftList =
                        pbdCraftDao.findPbdCraftsByPbdId(produceBatchDetail);
                    for (ProduceBatchDetailCraft produceBatchDetailCraft : produceBatchDetailCraftList)
                    {
                        boolean flag = false;
                        for (BoardPartCraft bpc : boardPartCraftList)
                        {
                            // 匹配对应的工艺切割工艺，对应的每一种工艺尺寸*相应的批次明细工艺的数量
                            if (produceBatchDetailCraft.getCraftNo().equals(bpc.getCraftNo())
                                && produceBatchDetailCraft.getProduceBatchDetail()
                                    .getRecordId()
                                    .equals(produceBatchDetailId))
                            {
                                
                                /* 查询这个工艺的已入库和打叉入库的数量 */
                                ProductStore mergedProductStore = new ProductStore();
                                mergedProductStore.setCompany(UserUtils.getUser().getCompany());
                                mergedProductStore.setActiveFlag(ProductStore.DEL_FLAG_NORMAL);
                                mergedProductStore.setStatus(TypeKey.BILL_STATUS_FOR_WRITEOFF);// 不查出冲红单数据
                                mergedProductStore.setBatchDetailCraftId(produceBatchDetailCraft.getRecordId());
                                /* 叉板总数量+已入库总数量 */
                                Integer unValiableQty = pstoreDao.quaryDelTotalQtys(mergedProductStore) == null ? 0
                                    : pstoreDao.quaryDelTotalQtys(mergedProductStore);
                                
                                /* 查询这个工艺的待入库的数量 */
                                Integer ValiableQuantity = produceBatchDetailCraft.getQtyPcsT() == null ? 0
                                    : produceBatchDetailCraft.getQtyPcsT();
                                /* 查询这个工艺的报废的数量 */
                                Integer disQuantity = produceBatchDetailCraft.getDiscardQty() == null ? 0
                                    : produceBatchDetailCraft.getDiscardQty();
                                /* 算出这个合单切割工艺的待入库面积 */
                                if (ValiableQuantity - disQuantity - unValiableQty > 0)
                                {
                                    _area = (
                                    /* 大板待入库pcs数*（set长*set宽）)/PnlDivisor/1000000 */
                                    new BigDecimal(ValiableQuantity - disQuantity - unValiableQty).multiply(
                                        new BigDecimal(bpc.getSetLength()).multiply(new BigDecimal(bpc.getSetWidth()))))
                                            .divide(new BigDecimal(bpc.getPnlDivisor()), 4, BigDecimal.ROUND_HALF_UP)
                                            .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP)
                                            .add(_area);
                                    flag = true;
                                    break;// 有可能是其中两个工艺或者几个工艺相同，直接跳出本单次循环，由于工艺是一样的，所以算面积长宽是一样的没有问题。
                                }
                            }
                        }
                        if (flag)
                            continue;
                    }
                    area = _area;
                    /* 根据批次明细获取对应的合同下的所有批次明细的工艺 end */
                }
                /* 合单切割出入库总面积计算 end */
            }
        }
        else if (contractDetail != null && contractDetail.getCraft() != null)
        {
            ContractCraft craft = contractDetail.getCraft();
            if (craft != null)
            {
                // 其pnldivisor之前数据可能没有填为空，所以现在要用的话需要纠正和判断，默认最小值为1
                if (craft.getPnlDivisor() == null || craft.getPnlDivisor().toString() == "")
                {
                    craft.setPnlDivisor(new BigDecimal(1));
                }
                
                if (craft.getPnlLength() != null && craft.getPnlLength().compareTo(BigDecimal.ZERO) == 1
                    && craft.getPnlWidth() != null && craft.getPnlWidth().compareTo(BigDecimal.ZERO) == 1)
                {
                    // 交货尺寸不为空
                    BigDecimal pnlLength = craft.getPnlLength();
                    BigDecimal pnlWidth = craft.getPnlWidth();
                    BigDecimal pnlDivisor = craft.getPnlDivisor();
                    area = pnlLength.multiply(pnlWidth)
                        .multiply(new BigDecimal(quantity))
                        .divide(pnlDivisor, 4, BigDecimal.ROUND_HALF_UP)
                        .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
                }
                else if (craft.getUnitLength() != null && craft.getUnitLength().compareTo(BigDecimal.ZERO) == 1
                    && craft.getUnitWidth() != null && craft.getUnitWidth().compareTo(BigDecimal.ZERO) == 1)
                {
                    // 交货尺寸为空,取单位尺寸
                    BigDecimal unitLength = craft.getUnitLength();
                    BigDecimal unitWidth = craft.getUnitWidth();
                    BigDecimal pnlDivisor = craft.getPnlDivisor();
                    area = unitLength.multiply(unitWidth)
                        .multiply(new BigDecimal(quantity))
                        .divide(pnlDivisor, 4, BigDecimal.ROUND_HALF_UP)
                        .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
                }
            }
        }
        return area;
    }
    
    /**
     * 入库
     * 
     * @param product
     * @return
     */
    @Transactional(readOnly = false)
    public int productAdd(ProductStore product)
    {
        // zjn 2019-01-25 一键送货出库(复制送货单明细修改时间，用来处理仓库统计报表数据)
        Date lastUpdDate = product.getLastUpdDate();
        
        // 返回结果,校验
        int result = 0;
        Company company = product.getCompany();
        ProductStockUtil productStockUtil = new ProductStockUtil();
        product.setSaleComId(product.getBranchId());
        if (null != product.getMaterial())
        {
            product.setStockPlaceComId(productStockUtil.returnStockPlaceComId(product.getStockPlaceId(),
                product.getMaterial().getRecordId(),
                product.getSaleComId()));
        }
        
        // 批次明细
        ProduceBatchDetail pdb = null;
        // 通知单
        Notification not = null;
        // 通知单
        Notification notReal = null;
        // 合同明细
        ContractDetail contractDetail = null;
        // 流程卡
        String processCardAId = "";
        
        // 生产入库:正常入库数量
        Integer inQty = 0;
        // 生产入库:叉板入库数量
        Integer inxboardQty = 0;
        // 生产入库:总入库数量
        @SuppressWarnings("unused")
        Integer allQty = 0;
        // 合同
        Contract contract = null;
        Integer temQuanty = 0;
        if (product.getGiveNum() != null)
        {
            temQuanty = product.getQuantity();
            product.setQuantity(product.getQuantity() + product.getGiveNum().intValue());
        }
        // 生产入库
        if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_IN) == 0)
        {
            // 批次明细
            pdb = product.getProduceBatchDetail();
            
            // 查询该批次明细的最新状态，判断是否是已经入库
            ProduceBatchDetail validPbd = batchDetailDao.get(pdb.getRecordId());
            if (validPbd.getStatus().compareTo(TypeKey.PRODUCE_BATCH_STATUS_FANISH) == 0)
            {
                return 9;
            }
            
            // 查询是否存在品质检测未审核的
            Inspect inspect = new Inspect();
            inspect.setCompany(company);
            inspect.setProduceBatchDetail(pdb);
            inspect.setStatus(TypeKey.QC_STATUS_INIT.toString());
            Integer initInspectSum = inspectDao.findInitInspectSum(inspect);
            if (initInspectSum.compareTo(0) == 1)
            {
                return 11;
            }
            
            inQty = product.getQuantity() == null ? 0 : product.getQuantity();
            inxboardQty = product.getXboardQty() == null ? 0 : product.getXboardQty();
            allQty = inQty + inxboardQty;
            
            // 通知单
            not = pdb.getNotification();
            // 根据通知单获取合同
            contract = contractDao.getSwitchByNotifi(not);
            // 查询通知单关联的合同信息
            notReal = new Notification();
            notReal.setCompany(company);
            
            // 若为合单
            if (not.getMergeType().compareTo(TypeKey.SL_NOTIFICATION_MERGE_NEW) == 0)
            {
                // 相同编号合单
                if (StringUtils.isBlank(product.getBatchDetailCraftId()))
                {
                    // 取第一个未入库的通知单
                    notReal.setCraftNo(product.getMaterial().getNo());
                    notReal.setMergeType(TypeKey.SL_NOTIFICATION_BE_MERGED);
                    notReal.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                    notReal = notifiDao.getNotificationByCraftNoFilterFinished(notReal);
                    contract = contractDao.getSwitchByNotifi(notReal);
                }
                else
                {
                    // 取对应的通知单
                    notReal = notifiDao.getNotificationByBatchDetailCraftId(product.getBatchDetailCraftId());
                    contract = contractDao.getSwitchByNotifi(notReal);
                }
                if (notReal == null)
                {
                    return 10;
                }
                // 二个页面操作：限制重复入库
                List<ProduceBatchDetailCraft> pbdCrafts = pbdCraftDao.findPbdCraftsByPbdId(pdb);
                for (ProduceBatchDetailCraft produceBatchDetailCraft : pbdCrafts)
                {
                    if (product.getBatchDetailCraftId().equals(produceBatchDetailCraft.getRecordId()))
                    {
                        if (produceBatchDetailCraft.getPutStorage() == TypeKey.YES.intValue())
                        {
                            // 已经完成入库,不能重复入库.
                            return 9;
                        }
                    }
                }
            }
            else
            {
                notReal.setRecordId(product.getNotificationId());
                notReal = notifiDao.findNotificationById(notReal);
            }
            
            contractDetail = notReal.getContractDetail();
            processCardAId = notReal.getCardA().getRecordId();
            if (null != contractDetail)
            {
                product.setContractDetail(notReal.getContractDetail());
            }
            if (null != contractDetail.getContract() && null != contractDetail.getContract().getCustomer())
            {
                product.setCustomer(contractDetail.getContract().getCustomer());
            }
            
            product.setQuantity(inQty);
            product.setXboardQty(inxboardQty);
        }
        
        // 如果是送货出库
        if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_OUT) == 0)
        {//
            DeliveryDetail deliveryDetail = deliveryDetailDao.get(product.getDeliveryDetailId());
            if (deliveryDetail.getStatus().compareTo(TypeKey.PU_ORDER_DELIVERY_STATUS_COMPLETE) == 0)
            {
                return 12;
            }
            
        }
        
        // 如果是采购入库
        if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_PURCHASING_IN) == 0
            && product.getPrdOrderDetailId() != null)
        {
            PrdorderDetail prdorderDetail = new PrdorderDetail();
            prdorderDetail.setCompany(company);
            prdorderDetail.setRecordId(product.getPrdOrderDetailId().toString());
            PrdorderDetail poDtl = proDetailDao.getProductOrderDetailById(prdorderDetail);
            contract = contractDao.getSwitchByDeail(poDtl.getContractDetail());
            if (poDtl.getStatus().equals(TypeKey.PU_PURCHASING_STATUS_ALREADY_IN.toString()))
            {
                return 13;
            }
            SourceDetection sd = new SourceDetection();
            sd.setOrderId(prdorderDetail.getRecordId());
            sd.setCompany(company);
            sd.setResourceType(ConstKey.PA_PRDPRODUCT_FLAG);
            sd.setStatus(TypeKey.ST_SOURCEDETECTION_STATUS_AUDITED);
            // 查询来料检测记录
            List<SourceDetection> soulist = sourceDetectionDao.getSourceDetection(sd);
            if (Collections3.isEmpty(soulist))
            {
                return 13;
            }
        }
        
        // 如果是采购退货
        if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_OUT) == 0
            && product.getDeliveryNo() != null)
        {
            PuReturnsDetail puReturnsDetail = returnDetailDao.get(product.getDeliveryNo());
            int qty = 0;
            Material material = materialDao.get(product.getMaterial()); // WC 2017-03-31 检查可库存是否满足退货
            if (puReturnsDetail.getStatus().compareTo(TypeKey.PU_PURETURNS_STATUS_THELIBRARY) == 0)
            {
                return 14;
            }
            else
            {
                material = materialDao.get(product.getMaterial());
                BigDecimal stocks = material.getStocks() == null ? BigDecimal.ZERO : material.getStocks(); // 现库存数量
                BigDecimal depositQty =
                    material.getDepositQty() == null ? BigDecimal.ZERO : new BigDecimal(material.getDepositQty()); // 寄存数量
                BigDecimal quantity =
                    product.getQuantity() == null ? BigDecimal.ZERO : new BigDecimal(product.getQuantity().toString()); // 退货出库数量
                if (stocks.compareTo(quantity) < 0)
                { // 出库数量大于寄存数量
                    return 16;
                }
                if (depositQty.compareTo(quantity) < 0 && product.getIsDeposit())
                { // 出库数量大于寄存数量
                    return 17;
                }
                
                // 获取订单的库存
                ContractDetail detail = product.getContractDetail();
                if (null == detail || StringUtils.isBlank(detail.getRecordId()))
                {
                    if (null != product.getProduceBatchDetail()
                        && null != product.getProduceBatchDetail().getNotification())
                    {
                        detail = product.getProduceBatchDetail().getNotification().getContractDetail();
                    }
                }
                productStockUtil.setProductInoutList(detail, company);
                if (null != product.getQuantity())
                {
                    quantity = new BigDecimal(product.getQuantity());
                }
                if (null == detail.getStocks() || detail.getStocks().compareTo(quantity) < 0)
                {
                    return 17;
                }
                
                List<ProductStore> prod = pstoreDao.getProductStore(product);
                if (Collections3.isNotEmpty(prod))
                {
                    for (ProductStore productStore : prod)
                    {
                        qty += productStore.getQuantity();
                    }
                }
                qty += product.getQuantity();
                if (qty >= puReturnsDetail.getQuantity().intValue())
                {
                    puReturnsDetail.setStatus(TypeKey.PU_PURETURNS_STATUS_THELIBRARY);
                    returnDetailDao.updateStatus(puReturnsDetail);
                }
            }
            
            PrdorderDetail prdorderDetail = new PrdorderDetail();
            prdorderDetail.setCompany(company);
            prdorderDetail.setRecordId(puReturnsDetail.getPurchasingDetailId().toString());
            PrdorderDetail poDtl = proDetailDao.getProductOrderDetailById(prdorderDetail);
             Integer quantity = poDtl.getQuantity();// 订单数量
            
            // 合同
            ContractDetail cd = poDtl.getContractDetail();
            contract = contractDao.getSwitchByDeail(cd);
            // 增加采购入库的数量累加到合同明细已入库数量中
            if (cd != null)
            {
                cd = contractDetailDao.get(cd);
                // 送货完成
                if (product.getQuantity() == null)
                {
                    product.setQuantity(0);
                }
                if (cd.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()) && !cd.getDeposit())
                {
                    // 合同已送货，合同入库数不处理，库存减少、可用库存减少
                    // Material material = materialDao.get(product.getMaterial());
                    material.setAvailableQty(new BigDecimal(product.getQuantity() * -1));
                    materialDao.updateAQtyById(material);
                    
                }
                else
                {
                    if (cd.getAlInHouseQty() == null)
                    {
                        cd.setAlInHouseQty(0);
                    }
                    // 没有送货完成 合同入库数减入库数量
                    cd.setAlInHouseQty(cd.getAlInHouseQty() - product.getQuantity());
                    // 更新合同明细已入库数量
                    contractDetailDao.updateAlInHouseQtyTow(cd);
                    if (contract != null && StringUtils.isNotBlank(contract.getInventorySwitch())
                        && contract.getInventorySwitch().equals("1"))
                    {
                        material.setAvailableQty(new BigDecimal(product.getQuantity() * -1));
                        materialDao.updateAQtyById(material);
                        contract.getContractDetailList().setAlInHouseQty(cd.getAlInHouseQty());
                        // TODO WC 2018-01-10库存订单分批交货数量要减掉
                        // operBatchDeliveryQty(cd, product.getQuantity(), ConstKey.ADJUST_MIN);
                    }
                }
            }
            
            ProductStore productStore = new ProductStore();
            productStore.setCompany(UserUtils.getUser().getCompany());
            productStore.setInoutType(TypeKey.ST_PRODUCT_INOUTTYPE_PURCHASING_IN);
            productStore.setPrdOrderDetailId(Long.parseLong(prdorderDetail.getRecordId()));
            List<ProductStore> prods = pstoreDao.getPurchasingStore(productStore);
            Integer num = 0;
            // 采购入库数量
            if (Collections3.isNotEmpty(prods))
            {
                for (ProductStore productStore1 : prods)
                {
                    num += productStore1.getQuantity();
                }
            }
            puReturnsDetail.setCompany(UserUtils.getUser().getCompany());
            List<PuReturnsDetail> puList = returnDetailDao.getReturnDetailsAll(puReturnsDetail);
            if (Collections3.isNotEmpty(puList))
            {
                for (PuReturnsDetail puReturnsDetail2 : puList)
                {
                    // 补货入库数量
                    productStore.setInoutType(TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_ENTER);
                    productStore.setDeliveryNo(puReturnsDetail2.getRecordId());
                    prods = pstoreDao.getProductStore(productStore);
                    if (Collections3.isNotEmpty(prods))
                    {
                        for (ProductStore productStore1 : prods)
                        {
                            num += productStore1.getQuantity();
                        }
                    }
                    // 退货出库
                    productStore.setInoutType(TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_OUT);
                    prods = pstoreDao.getProductStore(productStore);
                    if (Collections3.isNotEmpty(prods))
                    {
                        for (ProductStore productStore1 : prods)
                        {
                            // 入库数量-退货出库
                            num = num - productStore1.getQuantity();
                        }
                    }
                }
            }
            // 入库数量-订单数量-本次退货数量
            Integer total = (num - quantity) - qty;
            // 如果大于0生成对账单
            if (total < 0)
            {
                // 生成成品采购退货对账单
                if (num > quantity)
                {
                    executeGenerateProductRtsCheck(product.getDeliveryNo(), Math.abs(total), "2");
                }
                else
                {
                    executeGenerateProductRtsCheck(product.getDeliveryNo(), qty, "2");
                }
            }
        }
        
        // 如果是采购补货入库
        if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_ENTER) == 0
            && product.getDeliveryNo() != null)
        {
            // 生成成品采购退货对账单
            PuReturnsDetail puReturnsDetail = returnDetailDao.get(product.getDeliveryNo());
            int qty = 0;
            if (puReturnsDetail.getStatus().compareTo(TypeKey.ST_SOURCEDETECTION_STATUS_INHOUSE) == 0)
            {
                return 14;
            }
            SourceDetection sd = new SourceDetection();
            sd.setOrderId(puReturnsDetail.getRecordId());
            sd.setCompany(company);
            sd.setResourceType("4");
            sd.setStatus(TypeKey.ST_SOURCEDETECTION_STATUS_AUDITED);
            // 查询来料检测记录
            List<SourceDetection> soulist = sourceDetectionDao.getSourceDetection(sd);
            if (Collections3.isNotEmpty(soulist))
            {
                for (SourceDetection sourceDetection : soulist)
                {
                    // 修改来料检测状态
                    sourceDetection.setStatus(TypeKey.ST_SOURCEDETECTION_STATUS_INHOUSE);
                    sourceDetectionDao.updateStatus(sourceDetection);
                }
            }
            else
            {
                return 14;
            }
            List<ProductStore> prod = pstoreDao.getProductStore(product);
            if (Collections3.isNotEmpty(prod))
            {
                for (ProductStore productStore : prod)
                {
                    qty += productStore.getQuantity();
                }
            }
            qty += product.getQuantity();
            if (qty >= puReturnsDetail.getQuantity().intValue())
            {
                puReturnsDetail.setStatus(TypeKey.ST_SOURCEDETECTION_STATUS_INHOUSE);
                returnDetailDao.updateStatus(puReturnsDetail);
            }
            PrdorderDetail prdorderDetail = new PrdorderDetail();
            prdorderDetail.setCompany(company);
            prdorderDetail.setRecordId(puReturnsDetail.getPurchasingDetailId().toString());
            PrdorderDetail poDtl = proDetailDao.getProductOrderDetailById(prdorderDetail);
             Integer quantity = poDtl.getQuantity();// 订单数量
            
            // 合同
            ContractDetail cd = poDtl.getContractDetail();
            contract = contractDao.getSwitchByDeail(cd);
            // 增加采购入库的数量累加到合同明细已入库数量中
            if (cd != null)
            {
                // 送货完成
                if (cd.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()) && !cd.getDeposit())
                {
                    // 更新合同明细可用数量入库数量
                    Material material = product.getMaterial();
                    material.setAvailableQty(
                        product.getQuantity() == null ? BigDecimal.ZERO : new BigDecimal(product.getQuantity()));
                    materialDao.updateAQtyById(material);
                    
                }
                else
                {
                    // 没有送货完成
                    cd.setAlInHouseQty(product.getQuantity() == null ? 0 : product.getQuantity());
                    // 更新合同明细已入库数量
                    contractDetailDao.updateAlInHouseQty(cd);
                    if (contract != null && StringUtils.isNotBlank(contract.getInventorySwitch())
                        && contract.getInventorySwitch().equals("1"))
                    {
                        Material material = product.getMaterial();
                        material.setAvailableQty(
                            product.getQuantity() == null ? BigDecimal.ZERO : new BigDecimal(product.getQuantity()));
                        materialDao.updateAQtyById(material);
                        contract.getContractDetailList()
                            .setAlInHouseQty(
                                contract.getContractDetailList().getAlInHouseQty() + product.getQuantity());
                        // TODO 采购入库处理库存订单的分批交货
                        // operBatchDeliveryQty(cd,product.getQuantity(),ConstKey.ADJUST_ADD);
                    }
                }
            }
            
            ProductStore productStore = new ProductStore();
            productStore.setCompany(UserUtils.getUser().getCompany());
            productStore.setInoutType(TypeKey.ST_PRODUCT_INOUTTYPE_PURCHASING_IN);
            productStore.setPrdOrderDetailId(Long.parseLong(prdorderDetail.getRecordId()));
            List<ProductStore> prods = pstoreDao.getPurchasingStore(productStore);
            Integer num = 0;
            // 采购入库
            if (Collections3.isNotEmpty(prods))
            {
                for (ProductStore productStore1 : prods)
                {
                    num += productStore1.getQuantity();
                }
            }
            puReturnsDetail.setCompany(UserUtils.getUser().getCompany());
            List<PuReturnsDetail> puList = returnDetailDao.getReturnDetailsAll(puReturnsDetail);
            if (Collections3.isNotEmpty(puList))
            {
                for (PuReturnsDetail puReturnsDetail2 : puList)
                {
                    // 补货入库数量
                    productStore.setInoutType(TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_ENTER);
                    productStore.setDeliveryNo(puReturnsDetail2.getRecordId());
                    prods = pstoreDao.getProductStore(productStore);
                    if (Collections3.isNotEmpty(prods))
                    {
                        for (ProductStore productStore1 : prods)
                        {
                            num += productStore1.getQuantity();
                        }
                    }
                    // 退货出库
                    productStore.setInoutType(TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_OUT);
                    prods = pstoreDao.getProductStore(productStore);
                    if (Collections3.isNotEmpty(prods))
                    {
                        for (ProductStore productStore1 : prods)
                        {
                            // 入库数量-退货出库
                            num = num - productStore1.getQuantity();
                        }
                    }
                }
            }
            if (product.getGiveNum() != null && temQuanty < product.getQuantity())
            {
                qty = qty - product.getGiveNum().intValue();
                product.setQuantity(product.getQuantity() - product.getGiveNum().intValue());
            }
            // 入库数量-订单数量-本次退货数量
             Integer total = (num - quantity) - qty;
             // 如果大于0生成对账单
             if (total < 0)
             {
             if (num < quantity)
             {
             qty = quantity - num;
             if (product.getQuantity().intValue() > qty)
             {
             executeGenerateProductRtsCheck(product.getDeliveryNo(), qty, "1");
             }
             else
             {
             executeGenerateProductRtsCheck(product.getDeliveryNo(), product.getQuantity(), "1");
             }
             }
             }
        }
        
        if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_RETURN) == 0)
        
        {
            // 将对应退货申请的的处理状态改为已入库
            RejectApplication rejectApp = rejectApplicationDao.get(product.getRejectApplication().getRecordId());
            if (rejectApp.getStatus().compareTo(TypeKey.ST_REJECTAPPLICATION_STATUS_STORED) == 0)
            {
                return 15;
            }
        }
        
        Material material = product.getMaterial();
        
        if (product.getGiveNum() != null)
        {
            if (!product.getQuantity().equals(temQuanty + product.getGiveNum().intValue()))
            {
                product.setQuantity(temQuanty + product.getGiveNum().intValue());
            }
        }
        
        // 修改/添加物料,若为退货入库则返回合计金额
        result = modifyMaterialStocks(product, material, not);
        // 如果出库数大于库存量
        if (result == 3)
        {
            return 3;
        }
        
        if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_OUT) == 0)
        {
            // 如果是退货出库保存合同明细
            ContractDetail ct = contract.getContractDetailList();
            product.setContractDetail(ct);
        }
        
        // 保存成品入库记录
        if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_IN) == 0)
        {
            // 若是相同编号的合单，此处不添加出入库记录，在修改通知单的地方插入，可能插入多条
            if (not.getMergeType().compareTo(TypeKey.SL_NOTIFICATION_MERGE_NEW) == 0)
            {
                if (StringUtils.isNotBlank(product.getBatchDetailCraftId()))
                {
                    saveProductStore(product);
                }
            }
            else
            {
                if (product.getGiveNum() != null)
                {
                    product.setQuantity(product.getQuantity() - product.getGiveNum().intValue());
                }
                saveProductStore(product);
            }
        }
        else
        {
            if (product.getGiveNum() != null)
            {
                product.setQuantity(product.getQuantity() - product.getGiveNum().intValue());
            }
            
            saveProductStore(product);
        }
        
        // 生产入库后对通知单的修改及统计信息
        if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_IN) == 0)
        {
            modifyNotifi(pdb, not, product, notReal);
            
            // 入库面积
            /** 投料报表统计:后台入库面积统计（不能将打叉板纳入）2016-08-24 wc */
            BigDecimal inAreaReport = getArea(contractDetail,
                inQty,
                processCardAId,
                null == material ? product.getMaterial().getNo() : material.getNo(),
                ConstKey.IN_OUT_AREA_TYPE_ONE,
                null);
            
            // 将计算出来负数的面积取绝对值
            BigDecimal inAreaReports = inAreaReport.abs();
            product.setProductionInStoreArea(inAreaReports);
            
            // 根据生产入库记录生成关联公司的采购数据
            // ChangeDataUtils changeDataUtils = new ChangeDataUtils();
            // List<ProductStore> proStoreList = Lists.newArrayList();
            // proStoreList.add(product);
            // changeDataUtils.getPrdorderDetailList(proStoreList, 1);
        }
        
        // 如果是采购入库
        if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_PURCHASING_IN) == 0
            && product.getPrdOrderDetailId() != null)
        {
            PrdorderDetail prdorderDetail = new PrdorderDetail();
            prdorderDetail.setCompany(company);
            prdorderDetail.setRecordId(product.getPrdOrderDetailId().toString());
            PrdorderDetail poDtl = proDetailDao.getProductOrderDetailById(prdorderDetail);
            
            if (product.getGiveNum() != null && temQuanty < product.getQuantity())
            {
                
                product.setQuantity(product.getQuantity() - product.getGiveNum().intValue());
            }
            ContractDetail cd = poDtl.getContractDetail();
            
            /** 2016-08-24 wc 成品采购入库后，后台公司生产产能统计中入库面积相应增加 */
            // 入库数量
            BigDecimal caiGoInQuantity = new BigDecimal(product.getQuantity() == null ? 0 : product.getQuantity());
            // 成品采购入库面积计算
            BigDecimal inAreaCaigo = FinanceUtils.getCaigoInArea(poDtl, caiGoInQuantity);
            
            // 将计算出来负数的面积取绝对值 lq 2018-10-23
            BigDecimal inareaCaigos = inAreaCaigo.abs();
            product.setPurchaseInStoreArea(inareaCaigos);
            if (product.getGiveNum() != null && temQuanty.equals(product.getQuantity()))
            {
                
                product.setQuantity(product.getQuantity() + product.getGiveNum().intValue());
            }
            // 增加采购入库的数量累加到合同明细已入库数量中
            if (cd != null)
            {
                // 送货完成
                if (cd.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()) && !cd.getDeposit())
                {
                    // 更新合同明细可用数量入库数量
                    material.setAvailableQty(
                        product.getQuantity() == null ? BigDecimal.ZERO : new BigDecimal(product.getQuantity()));
                    materialDao.updateAQtyById(material);
                    
                }
                else
                {
                    // 没有送货完成
                    cd.setAlInHouseQty(product.getQuantity() == null ? 0 : product.getQuantity());
                    // 更新合同明细已入库数量
                    contractDetailDao.updateAlInHouseQty(cd);
                    if (contract != null && StringUtils.isNotBlank(contract.getInventorySwitch())
                        && contract.getInventorySwitch().equals("1"))
                    {
                        material.setAvailableQty(
                            product.getQuantity() == null ? BigDecimal.ZERO : new BigDecimal(product.getQuantity()));
                        materialDao.updateAQtyById(material);
                        contract.getContractDetailList()
                            .setAlInHouseQty(
                                contract.getContractDetailList().getAlInHouseQty() + product.getQuantity());
                        // WC 2018-01-11 采购入库处理库存订单的分批交货
                        // operBatchDeliveryQty(cd,product.getQuantity(),ConstKey.ADJUST_ADD);
                    }
                }
            }
            
            // 查询该采购订单的不良品数量
            SourceDetection sd = new SourceDetection();
            sd.setOrderId(prdorderDetail.getRecordId());
            sd.setCompany(company);
            sd.setResourceType(ConstKey.PA_PRDPRODUCT_FLAG);
            sd.setStatus(TypeKey.ST_SOURCEDETECTION_STATUS_AUDITED);
            BigDecimal poorCount = sourceDetectionDao.getPoorNumSumTo(sd);// 来料检测不良数量
            if (poorCount == null)
            {
                poorCount = BigDecimal.ZERO;
            }
            // 查询来料检测记录
            List<SourceDetection> soulist = sourceDetectionDao.getSourceDetection(sd);
            if (Collections3.isNotEmpty(soulist))
            {
                for (SourceDetection sourceDetection : soulist)
                {
                    // 修改来料检测状态
                    sourceDetection.setStatus(TypeKey.ST_SOURCEDETECTION_STATUS_INHOUSE);
                    sourceDetectionDao.updateStatus(sourceDetection);
                }
            }
            // 查询已入库数量
            Integer alInProductNum = this.quaryAvailableStocked(product.getQuantity(),
                product.getMaterial().getRecordId(),
                null,
                product.getPrdOrderDetailId().toString(),
                null,
                null,
                product.getCompany().getRecordId()).get(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_UNA);
            // 入库数量需要减去不良数量
            Integer finishInProductNum = alInProductNum + poorCount.intValue();
            
            // 根据采购明细单的数量
            Integer quantity = poDtl.getQuantity() == null ? 0 : poDtl.getQuantity();
            // 入库总数-订单数量
            BigDecimal storageQty = new BigDecimal(MathUtils.sub(alInProductNum, quantity));
            BigDecimal isquantityQty = BigDecimal.ZERO;
            
            if (product.getGiveNum() != null && temQuanty < product.getQuantity())
            {
                
                product.setQuantity(product.getQuantity() - product.getGiveNum().intValue());
            }
            // 入库总数大于0
            if (storageQty.intValue() > 0)
            {
                // 本次入库数量=本次入库数量-入库总数
                isquantityQty =
                    new BigDecimal(MathUtils.sub(product.getQuantity().doubleValue(), storageQty.doubleValue()));
            }
            else
            {
                isquantityQty = new BigDecimal(product.getQuantity());
            }
            
            // ycy 2016-10-11 根据待入库数量分批添加对账单，入库数量大于采购数量不添加对账单
            if (poDtl != null && !poDtl.getStatus().equals(TypeKey.PU_PURCHASING_STATUS_ALREADY_IN.toString())
                && isquantityQty.intValue() >= 0)
            {
                // 1.入库数量>采购单数量,表示该采购单已经完成，可以根据采购单数量生成对账单
                // 2.入库数量<采购数量，表示还未完全收到货物，不生成采购单
                // 生成成品采购对账单
                AccountsPayable ap =
                    executeGenerateProductCheck(product, poDtl, isquantityQty.intValue(), finishInProductNum);
                String payWay = poDtl.getPrdorder().getPayWay().toString();
                List<DictValue> payWayList = DictUtils.getValuesByItem(DictItemEnum.PAY_WAYS);
                // 该采购单所有费用 包含了采购物料的费用+其他额外费用
                BigDecimal amount = BigDecimal.ZERO;
                // fzd 2016-12-28 采购明细未入库算其它费用，入过库了就不算了
                List<ProductStore> proStockList = pstoreDao.getProStoreNum(product);
                if (Collections3.isNotEmpty(proStockList) && proStockList.size() > 1)
                {
                    amount = poDtl.getAmountTwo(isquantityQty.intValue());
                }
                else
                {
                    amount = poDtl.getAmountNew(isquantityQty.intValue());
                }
                if (amount.compareTo(BigDecimal.ZERO) == 1)
                {
                    for (DictValue pw : payWayList)
                    {
                        // 有预付款的采购订单才添加付款记录
                        if (payWay.equals(pw.getRecordId()))
                        {
                            if (ConstKey.PAY_WAY_PROMPTCASH.equals(pw.getValue())
                                || ConstKey.PAY_WAY_DAP.equals(pw.getValue())
                                || ConstKey.PAY_WAY_PROMPTPAY.equals(pw.getValue()))
                            {
                                // 如果是有预付款的采购订单，则应添加相应的付款记录，并扣减相应的预付款余额
                                // (入库数据，此采购明细，对账单，付款方式，入库金额（包含了物料费用+其他费用）,（入库的数量+本次的入库数量+之前的不良数量+本次的不良数量）,本次入库数量)
                                executeGenerateProductPayMoney(product,
                                    poDtl,
                                    ap,
                                    payWay,
                                    amount,
                                    finishInProductNum,
                                    isquantityQty);
                                break;
                            }
                        }
                    }
                }
            }
            if (finishInProductNum >= quantity
                && !poDtl.getStatus().equals(TypeKey.PU_PURCHASING_STATUS_ALREADY_IN.toString()))
            {
                // 更新成品采购明细状态
                poDtl.preUpdate();
                poDtl.setStatus(TypeKey.PU_PURCHASING_STATUS_ALREADY_IN.toString());
                proDetailDao.updateStutas(poDtl);
                // 根据成品采购单查询所有明细
                List<PrdorderDetail> poDtls = proDetailDao.findProdtlStatusByParentId(poDtl);
                if (poDtls != null && poDtls.size() == 1 && TypeKey.PU_PURCHASING_STATUS_ALREADY_IN
                    .compareTo(Integer.valueOf(poDtls.get(0).getStatus())) == 0)
                {
                    Prdorder prdorder = poDtl.getPrdorder();
                    prdorder.preUpdate();
                    // 修改成品采购的状态:已入库
                    prdorder.setStatus(TypeKey.PU_PURCHASING_STATUS_ALREADY_IN.toString());
                    prdorderDao.updateStatus(prdorder);
                }
            }
            
        }
        // 如果是送货出库
        if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_OUT) == 0)
        {
            ContractDetail contractDtl = product.getContractDetail();
            if (contractDtl != null)
            {
                contractDtl.setCompany(company);
                // 查询合同明细相关数据
                ContractDetail cd = contractDetailDao.findCustomerByContractId(contractDtl);
                if (cd != null)
                {
                    BigDecimal inArea = getOrderOperationArea(cd, new BigDecimal(product.getQuantity()));
                    
                    // 将计算出来负数的面积取绝对值 lq 2018-10-23
                    BigDecimal inAreas = inArea.abs();
                    
                    product.setDeliveryOutStoreArea(inAreas);
                    // 修改送货单状态为已出库
                    DeliveryDetail deliveryDetail = new DeliveryDetail(product.getDeliveryDetailId());
                    deliveryDetail.setStatus(TypeKey.PU_ORDER_DELIVERY_STATUS_COMPLETE);
                    deliveryDetailDao.updateStatus(deliveryDetail);
                    
                    contract = cd.getContract();
                    contract.setCompany(company);
                    contract = contractDao.findContractById(contract);
                }
            }
            
        }
        // 如果是客诉退货
        if (product.getInoutType().compareTo(TypeKey.ST_PRODUCT_INOUTTYPE_RETURN) == 0)
        {
            // WC 2017-02-16 寄存客诉单不生成退货对账单
            if (product.getIsDeposit() != null && !product.getIsDeposit())
            {
                // 生成退货对账单
                executeGenerateGoodsCheck(product);
                
            }
            
            // 将对应退货申请的的处理状态改为已入库
            RejectApplication rejectApp = product.getRejectApplication();
            if (rejectApp.getRecordId() != null && rejectApp.getRecordId() != "")
            {
                rejectApp.setCompany(product.getCompany());
                rejectApp.setStatus(TypeKey.ST_REJECTAPPLICATION_STATUS_STORED);
                rejectApp.preUpdate();
                rejectApplicationDao.updateStatus(rejectApp);
            }
        }
        if (!product.getInoutType().toString().equals(TypeKey.ST_PRODUCT_INOUTTYPE_IN.toString()))
        {
            if (contract != null && StringUtils.isNotBlank(contract.getInventorySwitch())
                && contract.getInventorySwitch().equals("1"))
            {
                if (contract.getContractDetailList() != null)
                {
                    if (contract.getContractDetailList().getStatus() == null || !contract.getContractDetailList()
                        .getStatus()
                        .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
                    {
                        updateContractStatus(contract);
                    }
                }
            }
        }
        
        // zjn 2019-01-25 一键送货出库使用
        if (null != lastUpdDate)
        {
            product.setLastUpdDate(lastUpdDate);
        }
        
        // 新增统计 lq 2018-10-18
        ReportAllUtils.countInOrOutStore(product, 1);
        return result;
    }
    
    private static BigDecimal getOrderOperationArea(ContractDetail contractDetail, BigDecimal outQty)
    {
        
        BigDecimal area = BigDecimal.ZERO;
        ContractCraft craft = contractDetail.getCraft();
        if (craft != null)
        {
            if (craft.getPnlLength() != null && craft.getPnlLength().compareTo(BigDecimal.ZERO) == 1
                && craft.getPnlWidth() != null && craft.getPnlWidth().compareTo(BigDecimal.ZERO) == 1
                && craft.getPnlDivisor() != null)
            {
                BigDecimal pnlLength = craft.getPnlLength();
                BigDecimal pnlWidth = craft.getPnlWidth();
                BigDecimal pnlDivisor = craft.getPnlDivisor();
                area = pnlLength.multiply(pnlWidth)
                    .multiply(outQty)
                    .divide(pnlDivisor, 4, BigDecimal.ROUND_HALF_UP)
                    .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
            }
            else if (craft.getUnitLength() != null && craft.getUnitWidth() != null)
            {
                BigDecimal unitLength = craft.getUnitLength();
                BigDecimal unitWidth = craft.getUnitWidth();
                area = unitLength.multiply(unitWidth)
                    .multiply(outQty)
                    .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
            }
        }
        return area.negate();
    }
    
    @SuppressWarnings("unused")
    private int checkIsProduceRecord(Notification noti, ProductStore product)
    {
        // 查询通知单对应的流程卡的最后一道工序
        Integer lastProduce = notifiDao.findLastProduceVal(noti);
        if (lastProduce == null)
        {
            // 工程资料错误
            return 4;
        }
        List<Feeding> feedings = noti.getFeedings();
        if (feedings == null || feedings.size() == 0)
        {
            // 未发现投料信息
            return 5;
        }
        Feeding feeding = feedings.get(0);
        feeding.setLastProduce(lastProduce);
        feeding.setCompany(UserUtils.getUser().getCompany());
        // 查询该通知单最后一道工序有多少批次
        Integer batchDetail = feedingDao.findFeedBatchDetailCount(feeding);
        // 是否已过完数
        if (batchDetail.compareTo(new Integer(0)) == 0)
        {
            // 还没有任何一批过完数
            return 6;
        }
        Integer failedPcsQty = noti.getFailedPcsQty() == null ? 0 : noti.getFailedPcsQty();
        // 总的投料数量+报废数量
        Integer feedingPcsQuantity = 0;
        Integer completedQty = noti.getCompletedQty() == null ? 0 : noti.getCompletedQty();
        completedQty += product.getQuantity() == null ? 0 : product.getQuantity();
        feedingPcsQuantity = completedQty + failedPcsQty;
        if (feedingPcsQuantity.compareTo(noti.getFeedPcsQty()) >= 0
            && completedQty.compareTo(new Integer(noti.getQuantity())) >= 0)
        {
            // 条件满足1.通知单完成数量+报废数量>=总的投料数量 普通通知单
            // 条件满足2.通知单完成数量+报废数量>=总的通知单数量 合单通知单
            // 查询该投料单产生多少个批次
            Integer batchDetailCount = batchDetailDao.findBatchDetailByBatchIdCount(feeding);
            if (batchDetailCount.compareTo(batchDetail) == 1)
            {
                // 还有板未过完，不能完全入库
                return 7;
            }
        }
        return 0;
    }
    
    // private void modifyNotifiAndFeeding(ProductStore product, Notification noti)
    // {
    // Integer failedPcsQty = noti.getFailedPcsQty() == null ? 0 : noti.getFailedPcsQty();
    // // 总的投料数量+报废数量
    // Integer feedingPcsQuantity = 0;
    // Integer completedQty = noti.getCompletedQty() == null ? 0 : noti.getCompletedQty();
    // completedQty += product.getQuantity() == null ? 0 : product.getQuantity();
    // noti.setCompletedQty(completedQty);
    // feedingPcsQuantity = completedQty + failedPcsQty;
    // if (noti.getMergeType().compareTo(TypeKey.SL_NOTIFICATION_MERGE_NORMAL) == 0)
    // {
    // if (feedingPcsQuantity.compareTo(noti.getFeedPcsQty()) >= 0
    // && completedQty.compareTo(new Integer(noti.getQuantity())) >= 0)
    // {
    // // 通知单完成数量+报废数量>=总的投料数量 普通通知单
    // noti.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
    // }
    // // 更改通知单已完成数量和状态
    // notifiDao.updateNotifiComplQtyAndStatus(noti);
    // }
    // else if (noti.getMergeType().compareTo(TypeKey.SL_NOTIFICATION_MERGE_NEW) == 0)
    // {
    // if (feedingPcsQuantity.compareTo(noti.getFeedPcsQty()) >= 0
    // && completedQty.compareTo(new Integer(noti.getQuantity())) >= 0)
    // {
    // // 通知单完成数量+报废数量>=总的投料数量 普通通知单
    // noti.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
    // }
    // // 完成入库更新该合单所有关联通知单的状态
    // notifiDao.updateMergeNotifStatus(noti);
    // // checkUpdateMergeStatus(noti);
    // }
    // }
    
    // 相同编号合单处理
    public void alikeMergeNo(Notification noti, List<Notification> list, ProductStore product, ProduceBatchDetail pdb)
    {
        // ycy 2016-10-13 总入库数量
        ProduceBatch pb = pdb.getProduceBatch();
        Integer total = product.getQuantity();
        
        Integer totalQuantity = 0;
        totalQuantity = total + (noti.getCompletedQty() == null ? 0 : noti.getCompletedQty());
        
        // ycy 2016-10-11 入库总数量小于通知单订单数量继续投补料
        if (totalQuantity < Integer.parseInt(noti.getQuantity()))
        {
            // 判断是否有补料单
            // 查询通知单下所有补料数量
            String replenishDiscardQty = notifiDao.getReplenishNum(noti);
            if (replenishDiscardQty != null)
            {
                if (noti.getFailedPcsQty() == null)
                {
                    noti.setFailedPcsQty(0);
                }
                if (Integer.parseInt(replenishDiscardQty) < noti.getFailedPcsQty())
                {
                    noti.setIsReplenish(TypeKey.YES);// 可以继补料
                }
            }
        }
        
        // 更新合单通知单的已入库数（累加）
        noti.setCompletedQty(total);
        notifiDao.updateCompletedQtyById(noti);
        
        int cQty = total;
        int otherQty = 0;
        for (int i = 0; i < list.size(); i++)
        {
            Notification temp = list.get(i);
            
            // 若通知单已完成入库，跳过处理
            if (temp.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString()))
            {
                continue;
            }
            
            int currCompQty = 0;
            
            // 第一个通知单的入库数
            int completedQty = temp.getCompletedQty() == null ? 0 : temp.getCompletedQty();
            
            // 若是最后一条通知单，直接入完
            if (i == list.size() - 1)
            {
                currCompQty = cQty;
            }
            else
            {
                // 若已入库数 + 本次入库数 > 订单数，需要将剩余的入库到另外的通知单
                int orderQty = Integer.parseInt(temp.getQuantity());
                // 订单数量减去占用库存
                int oaQty = temp.getOccupiedAvailableQty() == null ? 0 : temp.getOccupiedAvailableQty();
                if ((completedQty + cQty) > (orderQty - oaQty))
                {
                    // 其它通知单的入库数量为 已完成数量 + 当前入库数量 - 订单数量
                    otherQty = completedQty + cQty - orderQty;
                    cQty = otherQty;
                    // 本次完成入库的数量 = 订单数量 - 已入库数量
                    currCompQty = orderQty - completedQty;
                }
                else
                {
                    currCompQty = cQty;
                }
            }
            
            if (currCompQty == 0)
            {
                continue;
            }
            
            // 获取合同明细
            ContractDetail cd = temp.getContractDetail();
            
            // 设置数量
            product.setQuantity(currCompQty);
            // 设置合同明细
            product.setContractDetail(cd);
            
            if (null != cd.getContract() && null != cd.getContract().getCustomer())
            {
                product.setCustomer(cd.getContract().getCustomer());
            }
            
            // 插入出入库记录
            saveProductStore(product);
            
            // 更新实际通知单的已入库数（累加）
            temp.setCompletedQty(currCompQty);
            notifiDao.updateCompletedQtyById(temp);
            
            ProductStore pp = new ProductStore();
            pp.setQuantity(currCompQty);
            Contract contract = contractDao.getSwitchByNotifi(list.get(i));
            
            // 更新合同明细的入库数量，合单的通知单不可能是客诉类型
            if (cd != null && cd.getRecordId() != null)
            {
                if (cd.getStatus() == null)
                {
                    cd.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
                }
                
                // 若合同明细为送货完成，不需要更新已入库数量，但是需要累加可用库存
                if (cd.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
                {
                    // 前面已经累加了库存，当前仅需要累加可用库存
                    Material material = (Material)product.getMaterial().clone();
                    if (material != null && StringUtils.isNotBlank(material.getRecordId()) && currCompQty > 0)
                    {
                        // WC 2017-01-19 若有寄存，因为寄存数量已经存放到物料的寄存数量里，此处不再操作
                        if (!cd.getDeposit())
                        {
                            material.setAvailableQty(new BigDecimal(currCompQty));
                            materialDao.updateAQtyById(material);
                            
                        }
                        else
                        {
                            // 若有寄存，要将此次入库的数量存放到寄存中
                            material.setDepositQty(currCompQty); // 寄存数量
                            material.preUpdate();
                            materialDao.udpateDepositQtyById(material); // 更新寄存数量
                        }
                    }
                }
                else
                {
                    // 更新合同的正常已入库数量
                    cd.setAlInHouseQty(currCompQty);
                    contractDetailDao.updateAlInHouseQty(cd);
                    if (contract != null && StringUtils.isNotBlank(contract.getInventorySwitch())
                        && contract.getInventorySwitch().equals("1"))
                    {
                        Material material = new Material();
                        material.setRecordId(product.getMaterial().getRecordId());
                        material.setAvailableQty(new BigDecimal(currCompQty));
                        materialDao.updateAQtyById(material);
                        contract.getContractDetailList()
                            .setAlInHouseQty(contract.getContractDetailList().getAlInHouseQty() + cd.getAlInHouseQty());
                        // TODO 生产入库处理库存订单分批交货
                        // operBatchDeliveryQty(cd,product.getQuantity(),ConstKey.ADJUST_ADD);
                    }
                }
            }
            
            // 若本次的入库数已入库完毕，不在继续处理
            if (otherQty == 0)
            {
                break;
            }
        }
        
        // 批次明细的状态
        pdb.setStatus(TypeKey.PRODUCE_BATCH_STATUS_FANISH);
        batchDetailDao.updateBatchDetailStatus(pdb);
        
        // 如果整个批次全部完成，修改该批次的状态
        List<ProduceBatchDetail> pdbs = batchDetailDao.findProduceBatchDetailsByPb(pb);
        // 若所有批次均已完成
        boolean pbFlag = true;
        for (ProduceBatchDetail bd : pdbs)
        {
            if (null == bd.getStatus() || bd.getStatus().compareTo(TypeKey.PRODUCE_BATCH_STATUS_FANISH) != 0)
            {
                pbFlag = false;
                break;
            }
        }
        
        if (pbFlag)
        {
            // 修改批状态
            pb.setStatus(TypeKey.PRODUCE_BATCH_STATUS_FANISH.toString());
            produceBatchDao.updateBatchInStatus(pb);
            
            // 判断是否合单的所有合同明细都是已送货
            boolean update = true;
            for (Notification n : list)
            {
                if (n.getContractDetail() == null || StringUtils.isBlank(n.getContractDetail().getRecordId())
                    || StringUtils.isBlank(n.getContractDetail().getStatus()))
                {
                    update = false;
                    break;
                }
                
                Notification notif = notifiDao.getAllCompletedQty(n.getContractDetail());
                // 无寄存
                if (!n.getContractDetail().getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())
                    && !notif.getContractDetail().getDeposit())
                {
                    update = false;
                    break;
                }
                // 有寄存并且已送货并且（已入库数量+占可用库存数量）未达到订单数量 WC 2017-01-19
                if (notif != null && notif.getContractDetail().getDeposit()
                    && notif.getContractDetail()
                        .getStatus()
                        .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())
                    && notif.getCompletedQty() + notif.getOccupiedAvailableQty() < Integer
                        .parseInt(notif.getQuantity()))
                {
                    update = false;
                    break;
                }
                Contract contract = contractDao.getSwitchByNotifi(notif);
                if (contract != null && StringUtils.isNotBlank(contract.getInventorySwitch())
                    && contract.getInventorySwitch().equals("1"))
                {
                    update = false;
                    break;
                }
            }
            
            if (update)
            {
                // 判断是否还有投料单未生产完成
                Notification qn = new Notification(noti.getRecordId());
                qn.setCompany(noti.getCompany());
                qn.setStatus(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString() + ConstKey.COMMA
                    + TypeKey.PRODUCE_BATCH_STATUS_PAUSE.toString() + ConstKey.COMMA
                    + TypeKey.PRODUCE_BATCH_STATUS_LOSS.toString() + ConstKey.COMMA
                    + TypeKey.PRODUCE_BATCH_STATUS_OPERATION.toString());
                
                // 判断过数记录
                List<ProduceBatch> batchs = notifiDao.getBatchNoFinish(qn);
                if (Collections3.isEmpty(batchs))
                {
                    // 删除该合单未确认的投料单
                    List<Feeding> noConfirmfeeds = feedingDao.findNoConfirmByNotification(noti);
                    if (Collections3.isNotEmpty(noConfirmfeeds))
                    {
                        for (Feeding feeding : noConfirmfeeds)
                        {
                            feedingDao.delete(feeding);
                            
                            // 根据投料单编号 去删除批次信息
                            ProduceBatch date = produceBatchDao.getByFeefingNo(feeding.getRecordId());
                            
                            // 根据批次编号再去删除批次明细
                            produceBatchDao.delete(date);
                            batchDetailDao.deleteByProduceBatchRecordId(date.getRecordId());
                            
                            // 减去投料添加的统计信息
                            CraftNoDiscard craftNoDiscard = new CraftNoDiscard();
                            craftNoDiscard.setCraftNo(noti.getCraftNo());
                            craftNoDiscard.setCompany(product.getCompany());
                            
                            // 减去预投率中的总投料数
                            craftNoDiscard = craftNoDiscardDao.selectCount(craftNoDiscard);
                            if (null != craftNoDiscard)
                            {
                                Integer sum = Integer.parseInt(craftNoDiscard.getProductionTotal())
                                    - feeding.getPcsQuantity().intValue();
                                craftNoDiscard.setProductionTotal(sum.toString());
                                craftNoDiscard.preUpdate();
                                craftNoDiscardDao.update(craftNoDiscard);
                            }
                        }
                    }
                    
                    // 将已投料投料单的设置取消
                    List<Feeding> confirmfeeds = feedingDao.findConfirmByNotification2(noti);
                    if (Collections3.isNotEmpty(confirmfeeds))
                    {
                        for (Feeding feeding : confirmfeeds)
                        {
                            // 更新投料单状态
                            feeding.setStatus(TypeKey.PD_FEEDING_STATUS_CANCELED.toString());
                            feedingDao.updateStatusById(feeding);
                            
                            // 根据投料去查询批次信息
                            ProduceBatch pro = produceBatchDao.getByFeefingNo(feeding.getRecordId());
                            pro.setStatus(TypeKey.PRODUCE_BATCH_STATUS_CANCELED.toString());
                            produceBatchDao.updateStatus(pro);
                            
                            // 根据批次去修改所有本批次下面的批次明细
                            batchDetailDao.updateStatusByProduceBatch(pro);
                            
                            // 要把通知单 中的已投数量减去~ 再把通知单的状态改变成 已确认
                            Notification noti1 = notifiDao.get(feeding.getNotification());
                            String temp = feeding.getPcsQuantity().toString();
                            
                            // 设置为之前通知单的投料数量减去本次投料单的投料数量
                            noti1.setFeedPcsQty(noti1.getFeedPcsQty() - Integer.valueOf(temp));
                            noti1.setStatus(null);
                            notifiDao.updateFeedPcsQtyAndStatus(noti1);
                        }
                    }
                    
                    // 更新合单通知单的状态
                    noti.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                    notifiDao.updateStatusById(noti);
                    
                    // 更新所有被合单的通知单的状态
                    notifiDao.updateStatusByMergeId(noti);
                    
                }
            }
            else
            {
                // 3.如果该通知单所有批次全部完成入库，则该通知单的为完成入库
                // 查询该通知单对应的所有批次
                List<ProduceBatch> pbs = produceBatchDao.findPbsByNotification(noti);
                boolean notFlag = true;
                for (ProduceBatch pd : pbs)
                {
                    if (null == pd.getStatus()
                        || Integer.valueOf(pd.getStatus()).compareTo(TypeKey.PRODUCE_BATCH_STATUS_FANISH) != 0)
                    {
                        // 如果包含没有完成的明细,则该批次没有完成
                        notFlag = false;
                        break;
                    }
                }
                
                if (notFlag)
                {
                    for (Notification n : list)
                    {
                        if (n.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString()))
                        {
                            continue;
                        }
                        
                        // 查询该通知单已入库数量
                        ProductStore productStore = new ProductStore();
                        productStore.setCompany(product.getCompany());
                        productStore.setStatus(TypeKey.BILL_STATUS_FOR_WRITEOFF); // 不查出冲红单数据
                        productStore.setNotificationId(noti.getRecordId());
                        
                        // 可能有BUG，可能一个合同明细存在多个通知单，并且合单了，查询到的入库数量可能就会不准
                        productStore.setContractDetail(n.getContractDetail());
                        Integer quantitys = pstoreDao.quaryStockedByProduceBatchDetail(productStore);
                        if (null == quantitys)
                        {
                            quantitys = 0;
                        }
                        
                        int oQty = n.getOccupiedAvailableQty() == null ? 0 : n.getOccupiedAvailableQty();
                        quantitys += oQty;
                        if (quantitys.compareTo(Integer.valueOf(n.getQuantity())) >= 0)
                        {
                            // 通知单完成状态
                            n.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                            notifiDao.updateStatusById(n);
                            
                            // zjn 2020-12-02
                            handleOccupiedStock(n, pdb);
                        }
                    }
                    
                    // 判断是否需要更新合单的通知单的状态
                    boolean updateMerge = true;
                    for (Notification n : list)
                    {
                        if (!n.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString()))
                        {
                            updateMerge = false;
                        }
                    }
                    
                    // 更新合单的通知单的状态
                    if (updateMerge)
                    {
                        noti.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                        notifiDao.updateStatusById(noti);
                    }
                }
            }
        }
        for (int i = 0; i < list.size(); i++)
        {
            Contract contract = contractDao.getSwitchByNotifi(list.get(i));
            if (contract != null && StringUtils.isNotBlank(contract.getInventorySwitch())
                && contract.getInventorySwitch().equals("1"))
            {
                if (contract.getContractDetailList() != null)
                {
                    if (contract.getContractDetailList().getStatus() == null || !contract.getContractDetailList()
                        .getStatus()
                        .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
                    {
                        updateContractStatus(contract);
                    }
                }
            }
        }
    }
    
    public void handleContractDetail(ProductStore product, ContractDetail cd, Integer total)
    {
        // 更新合同明细的入库数量，合单的通知单不可能是客诉类型
        Contract contract = contractDao.getSwitchByDeail(cd);
        if (cd != null && cd.getRecordId() != null)
        {
            if (cd.getStatus() == null)
            {
                cd.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
            }
            
            // 若合同明细为送货完成，不需要更新已入库数量，但是需要累加可用库存
            if (cd.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
            {
                // 前面已经累加了库存，当前仅需要累加可用库存
                Material material = (Material)product.getMaterial().clone();
                if (material != null && StringUtils.isNotBlank(material.getRecordId()) && total.intValue() > 0)
                {
                    // 如果有寄存，则不需要累加到可用库存中
                    if (!cd.getDeposit())
                    {
                        material.setAvailableQty(new BigDecimal(total));
                        materialDao.updateAQtyById(material);
                    }
                }
            }
            else
            {
                // 更新合同的正常已入库数量
                cd.setAlInHouseQty(total);
                contractDetailDao.updateAlInHouseQty(cd);
                if (contract != null && StringUtils.isNotBlank(contract.getInventorySwitch())
                    && contract.getInventorySwitch().equals("1"))
                {
                    Material material = new Material();
                    material.setRecordId(product.getMaterial().getRecordId());
                    material.setAvailableQty(new BigDecimal(total));
                    materialDao.updateAQtyById(material);
                    contract.getContractDetailList()
                        .setAlInHouseQty(contract.getContractDetailList().getAlInHouseQty() + total);
                    
                }
                
            }
        }
        
        if (contract != null && StringUtils.isNotBlank(contract.getInventorySwitch())
            && contract.getInventorySwitch().equals("1"))
        {
            if (contract.getStatus() == null
                || !contract.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
            {
                updateContractStatus(contract);
            }
        }
    }
    
    // 不相同编号合单处理
    public void unAlikeMergeNo(Notification noti, Notification realNoti, ProductStore product, ProduceBatchDetail pdb,
        List<Notification> list)
    {
        Integer total = product.getQuantity();
        ProduceBatch pb = pdb.getProduceBatch();
        
        // 更新实际通知单的已入库数（累加）
        Integer realqty = null == realNoti.getQuantity() ? 0 : Integer.parseInt(realNoti.getQuantity());// 订单数量
        Integer realCompletedQty = null == realNoti.getCompletedQty() ? 0 : realNoti.getCompletedQty(); // 已入库数量
        Integer occupyQty = realNoti.getOccupiedAvailableQty() == null ? 0 : realNoti.getOccupiedAvailableQty();// 占用数量
        Integer unCompletedQty = realqty - realCompletedQty - occupyQty; // 还能入库数量
        
        if (unCompletedQty >= total)
        {
            realNoti.setCompletedQty(total);
            notifiDao.updateCompletedQtyById(realNoti);
            handleContractDetail(product, realNoti.getContractDetail(), total);
        }
        else
        {
            if (unCompletedQty > 0)
            {
                realNoti.setCompletedQty(unCompletedQty);
                notifiDao.updateCompletedQtyById(realNoti);
                handleContractDetail(product, realNoti.getContractDetail(), unCompletedQty);
            }
            Integer totalTwo = total - unCompletedQty;
            Boolean flag = false;
            for (int i = 0; i < list.size(); i++)
            {
                Notification notification = list.get(i);
                if (realNoti.getRecordId().equals(notification.getRecordId()))
                {
                    continue;
                }
                realqty = null == notification.getQuantity() ? 0 : Integer.parseInt(notification.getQuantity());// 订单数量
                realCompletedQty = null == notification.getCompletedQty() ? 0 : notification.getCompletedQty(); // 已入库数量
                occupyQty = notification.getOccupiedAvailableQty() == null ? 0 : notification.getOccupiedAvailableQty();// 占用数量
                unCompletedQty = realqty - realCompletedQty - occupyQty; // 还能入库数量
                if (unCompletedQty <= 0)
                {
                    continue;
                }
                if (unCompletedQty >= totalTwo)
                {
                    notification.setCompletedQty(totalTwo);
                    notifiDao.updateCompletedQtyById(notification);
                    handleContractDetail(product, notification.getContractDetail(), totalTwo);
                    flag = true;
                    break;
                }
                else
                {
                    if (i == list.size() - 1)
                    {
                        notification.setCompletedQty(totalTwo);
                        handleContractDetail(product, notification.getContractDetail(), totalTwo);
                    }
                    else
                    {
                        notification.setCompletedQty(unCompletedQty);
                        handleContractDetail(product, notification.getContractDetail(), unCompletedQty);
                    }
                    notifiDao.updateCompletedQtyById(notification);
                    totalTwo = totalTwo - unCompletedQty;
                    flag = true;
                    continue;
                }
            }
            // 和单未保存投料数量，已入库数量一般会大于订单数量，如果所有通知单已入库数量达到了订单数量，多出的部分处理
            if (!flag)
            {
                realNoti.setCompletedQty(totalTwo);
                notifiDao.updateCompletedQtyById(realNoti);
                handleContractDetail(product, realNoti.getContractDetail(), totalTwo);
            }
        }
        
        // 更新合单通知单的已入库数（累加）
        noti.setCompletedQty(total);
        notifiDao.updateCompletedQtyById(noti);
        
        // 更新批次明细工艺的入库状态
        if (StringUtils.isNotBlank(product.getBatchDetailCraftId()))
        {
            ProduceBatchDetailCraft pbdc = pbdCraftDao.get(product.getBatchDetailCraftId());
            Integer discardQty = pbdc.getDiscardQty() == null ? 0 : pbdc.getDiscardQty();
            pbdc.setStorageQuantity(total + (pbdc.getStorageQuantity() == null ? 0 : pbdc.getStorageQuantity()));
            pbdCraftDao.updateStorageQuantity(pbdc);
            if (pbdc.getStorageQuantity() + discardQty - pbdc.getQtyPcsT() == 0)
            {
                pbdCraftDao.updatePutStorage(product.getBatchDetailCraftId());
            }
        }
        
        // 判断当前批次明细的其它工艺是否都已入库，若已入库，就继续处理
        Integer count = pbdCraftDao.getNotPutStorageCountByDetailId(pdb);
        if (null != count && count > 0)
        {
            return;
        }
        
        // 批次明细的状态
        Integer qty = pdb.getQtyPcsT();
        // 减报废和已入库数量
        qty = qty - (StringUtils.isNotBlank(pdb.getDiscardQty()) ? Integer.valueOf(pdb.getDiscardQty()) : 0)
            - (product.getUnavailCount() == null ? 0 : product.getUnavailCount());
        if (qty.compareTo(total) == 0)
        {
            pdb.setStatus(TypeKey.PRODUCE_BATCH_STATUS_FANISH);
            batchDetailDao.updateBatchDetailStatus(pdb);
        }
        
        // 查询批次明细，判断是否所有的批次明细是否完成，若完成需要修改批次的状态
        List<ProduceBatchDetail> pdbs = batchDetailDao.findProduceBatchDetailsByPb(pb);
        boolean pbFlag = true;
        for (ProduceBatchDetail bd : pdbs)
        {
            if (null == bd.getStatus() || bd.getStatus().compareTo(TypeKey.PRODUCE_BATCH_STATUS_FANISH) != 0)
            {
                pbFlag = false;
                break;
            }
        }
        
        if (pbFlag)
        {
            // 修改批次状态
            pb.setStatus(TypeKey.PRODUCE_BATCH_STATUS_FANISH.toString());
            produceBatchDao.updateBatchInStatus(pb);
            
            // 2017-01-19 WC 若不是寄存但送货完成或者是寄存且所有入库数量达到订单数量(是寄存的条件：deposit = true && status = 200204)
            ContractDetail cd = realNoti.getContractDetail();
            Notification notif = notifiDao.getAllCompletedQty(cd);
            if ((cd != null && cd.getRecordId() != null
                && cd.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()) && !cd.getDeposit())
                || (notif != null && notif.getContractDetail().getDeposit()
                    && notif.getContractDetail()
                        .getStatus()
                        .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())
                    && notif.getCompletedQty() + notif.getOccupiedAvailableQty() >= Integer
                        .parseInt(notif.getQuantity())))
            {
                // 判断是否还有投料单未生产完成
                Notification qn = new Notification(noti.getRecordId());
                qn.setCompany(noti.getCompany());
                qn.setStatus(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString() + ConstKey.COMMA
                    + TypeKey.PRODUCE_BATCH_STATUS_PAUSE.toString() + ConstKey.COMMA
                    + TypeKey.PRODUCE_BATCH_STATUS_LOSS.toString() + ConstKey.COMMA
                    + TypeKey.PRODUCE_BATCH_STATUS_OPERATION.toString());
                
                // 判断过数记录
                List<ProduceBatch> batchs = notifiDao.getBatchNoFinish(qn);
                if (Collections3.isEmpty(batchs))
                {
                    // 判断其它合单的合同明细是否是已入库，是已入库才继续判断
                    boolean update = true;
                    for (Notification n : list)
                    {
                        if (n.getContractDetail() == null || StringUtils.isBlank(n.getContractDetail().getRecordId())
                            || StringUtils.isBlank(n.getContractDetail().getStatus()))
                        {
                            update = false;
                            break;
                        }
                        Notification notific = notifiDao.getAllCompletedQty(n.getContractDetail());
                        // 不是寄存未送货完成；是寄存，所有通知单入库数量未达到订单数量
                        if ((!n.getContractDetail()
                            .getStatus()
                            .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())
                            && !notific.getContractDetail().getDeposit())
                            || (notific.getContractDetail().getDeposit()
                                && notific.getContractDetail()
                                    .getStatus()
                                    .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())
                                && notific != null && notific.getCompletedQty()
                                    + notific.getOccupiedAvailableQty() < Integer.parseInt(notific.getQuantity())))
                        {
                            update = false;
                            break;
                        }
                    }
                    
                    if (update)
                    {
                        // 更新合单通知单的状态
                        noti.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                        notifiDao.updateStatusById(noti);
                        
                        // 更新所有被合单的通知单的状态
                        notifiDao.updateStatusByMergeId(noti);
                    }
                }
            }
            else
            {
                // 3.如果该通知单所有批次全部完成入库，则该通知单的为完成入库
                // 查询该通知单对应的所有批次
                List<ProduceBatch> pbs = produceBatchDao.findPbsByNotification(noti);
                boolean notFlag = true;
                for (ProduceBatch pd : pbs)
                {
                    if (null == pd.getStatus()
                        || Integer.valueOf(pd.getStatus()).compareTo(TypeKey.PRODUCE_BATCH_STATUS_FANISH) != 0)
                    {
                        // 如果包含没有完成的明细,则该批次没有完成
                        notFlag = false;
                        break;
                    }
                }
                
                if (notFlag)
                {
                    List<Notification> notiList = notifiDao.findBeMergeNotifsByMergeId(noti);
                    if (Collections3.isNotEmpty(notiList))
                    {
                        // 所有通知单
                        for (Notification n : notiList)
                        {
                            if (n.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString()))
                            {
                                continue;
                            }
                            
                            // 查询该通知单已入库数量
                            /*
                             * ProductStore productStore = new ProductStore();
                             * productStore.setCompany(product.getCompany());
                             * productStore.setStatus(TypeKey.BILL_STATUS_FOR_WRITEOFF); // 不查出冲红单数据
                             * productStore.setNotificationId(noti.getRecordId()); //
                             * 可能有BUG，可能一个合同明细存在多个通知单，并且合单了，查询到的入库数量可能就会不准
                             * productStore.setContractDetail(n.getContractDetail()); Integer quantitys =
                             * pstoreDao.quaryStockedByProduceBatchDetail(productStore);
                             */
                            Integer quantitys = n.getCompletedQty();
                            if (null == quantitys)
                            {
                                quantitys = 0;
                            }
                            int oQty = n.getOccupiedAvailableQty() == null ? 0 : n.getOccupiedAvailableQty();
                            quantitys += oQty;
                            if (quantitys.compareTo(Integer.valueOf(n.getQuantity())) >= 0)
                            {
                                // 通知单完成状态
                                n.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                                notifiDao.updateStatusById(n);
                                
                                // zjn 2020-12-02
                                handleOccupiedStock(n, pdb);
                            }
                        }
                        
                        // 判断是否需要更新合单的通知单的状态
                        boolean updateMerge = true;
                        for (Notification n : notiList)
                        {
                            if (!n.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString()))
                            {
                                updateMerge = false;
                            }
                        }
                        
                        // 更新合单的通知单的状态
                        if (updateMerge)
                        {
                            noti.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                            notifiDao.updateStatusById(noti);
                        }
                    }
                }
            }
        }
        
        /*
         * Notification tempNotification = notifiDao.get(noti); Integer totalQuantity = 0; if
         * (tempNotification.getCompletedQty() != null) { totalQuantity = tempNotification.getCompletedQty(); }
         * 
         * // ycy 2016-10-11 入库总数量小于通知单订单数量继续投补料 if (totalQuantity < Integer.parseInt(noti.getQuantity())) { // 判断是否有补料单
         * // 查询通知单下所有补料数量 String replenishDiscardQty = notifiDao.getReplenishNum(noti); if (replenishDiscardQty !=
         * null) { if (noti.getFailedPcsQty() == null) { noti.setFailedPcsQty(0); } if
         * (Integer.parseInt(replenishDiscardQty) < noti.getFailedPcsQty()) { noti.setIsReplenish(TypeKey.YES); } } else
         * { noti.setIsFeeding(TypeKey.YES);// 可以继续投料 } if (noti.getIsFeeding() != null || noti.getIsReplenish() !=
         * null) { notifiDao.updateIsFeeding(noti); } } else { boolean isOk = true; for (Notification n : list) { if
         * (!n.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString())) { isOk = false; } }
         * 
         * if (!isOk) { // 判断是否有补料单 // 查询通知单下所有补料数量 String replenishDiscardQty = notifiDao.getReplenishNum(noti); if
         * (replenishDiscardQty != null) { if (noti.getFailedPcsQty() == null) { noti.setFailedPcsQty(0); } if
         * (Integer.parseInt(replenishDiscardQty) < noti.getFailedPcsQty()) { noti.setIsReplenish(TypeKey.YES); } } else
         * { noti.setIsFeeding(TypeKey.YES);// 可以继续投料 } if (noti.getIsFeeding() != null || noti.getIsReplenish() !=
         * null) { notifiDao.updateIsFeeding(noti); } } }
         */
    }
    
    // 非合单处理
    public void wrongMergeNo(Notification noti, ProductStore product, ProduceBatchDetail pdb)
    {
        // 如果是普通通知单
        Integer total = product.getQuantity();
        ProduceBatch pb = pdb.getProduceBatch();
        ContractDetail cd = product.getContractDetail();
        Contract contract = contractDao.getSwitchByDeail(cd);
        cd.setDeposit(contractDetailDao.getDepositByConDtlId(cd.getRecordId()).getDeposit());
        if (cd != null && cd.getRecordId() != null)
        {
            if (noti.getNotificationType().compareTo(Notification.NOTIFICATION_NOMARL_TYPE) == 0)
            {
                if (cd.getStatus() == null)
                {
                    cd.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
                }
                
                // 若合同明细为送货完成，不需要更新已入库数量，但是需要累加可用库存
                // WC 2017-01-19 不是寄存，明细为送货完成，需要累加可用库存 || 是寄存，不需要累加到可用库存
                if (cd.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())) // 正常通知单
                {
                    // 前面已经累加了库存，当前仅需要累加可用库存
                    Material material = (Material)product.getMaterial().clone();
                    if (material != null && StringUtils.isNotBlank(material.getRecordId()) && total.intValue() > 0)
                    {
                        if (!cd.getDeposit())
                        { // 前面已经判断了状态是送货完成，现在只用判断是否寄存即可WC 2017-01-19
                            material.setAvailableQty(new BigDecimal(total));
                            materialDao.updateAQtyById(material);
                            
                        }
                    }
                }
                else
                {
                    // 更新合同的正常已入库数量
                    cd.setAlInHouseQty(total);
                    contractDetailDao.updateAlInHouseQty(cd);
                    if (contract != null && StringUtils.isNotBlank(contract.getInventorySwitch())
                        && contract.getInventorySwitch().equals("1"))
                    {
                        Material material = new Material();
                        material.setRecordId(product.getMaterial().getRecordId());
                        material.setAvailableQty(new BigDecimal(total));
                        materialDao.updateAQtyById(material);
                        contract.getContractDetailList()
                            .setAlInHouseQty(contract.getContractDetailList().getAlInHouseQty() + total);
                        // TODO WC 2018-01-11 处理生产入库库存订单的分批交货
                        // operBatchDeliveryQty(contract.getContractDetailList(), total, ConstKey.ADJUST_ADD);
                    }
                    
                }
            }
            else if (noti.getNotificationType().compareTo(Notification.NOTIFICATION_REJECT_TYPE) == 0) // 客诉通知单
                                                                                                       // 客诉寄存未处理WC
            {
                // 判断已送货数量是否大于客诉数量，若大于，将不在处理了
                cd.setAlInHouseRejQty(total);
                
                // 更新合同的客诉已入库数量
                contractDetailDao.updateAlInHouseRejQty(cd);
            }
        }
        
        // 1.修改该批次明细的状态
        pdb.setStatus(TypeKey.PRODUCE_BATCH_STATUS_FANISH);
        batchDetailDao.updateBatchDetailStatus(pdb);
        
        // 2.如果整个批次全部完成，修改该批次的状态
        // 查询该批次下面所有的批次明细
        List<ProduceBatchDetail> pdbs = batchDetailDao.findProduceBatchDetailsByPb(pb);
        boolean pbFlag = true;
        for (ProduceBatchDetail bd : pdbs)
        {
            if (null == bd.getStatus() || bd.getStatus().compareTo(TypeKey.PRODUCE_BATCH_STATUS_FANISH) != 0)
            {
                // 如果包含没有完成的明细,则该批次没有完成
                pbFlag = false;
                break;
            }
        }
        
        if (pbFlag)
        {
            // 修改批状态
            pb.setStatus(TypeKey.PRODUCE_BATCH_STATUS_FANISH.toString());
            produceBatchDao.updateBatchInStatus(pb);
            
            // 若为非寄存，正常通知单，且合同明细为已送货 || 若为寄存，正常通知单，且明细下所有通知单入库数量 + 占用可用库存数量 >= 订单数量
            Notification notific = notifiDao.getAllCompletedQty(cd);
            if ((cd != null && cd.getRecordId() != null
                && noti.getNotificationType().compareTo(Notification.NOTIFICATION_NOMARL_TYPE) == 0
                && cd.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())
                && !notific.getContractDetail().getDeposit())
                || (notific.getContractDetail().getDeposit()
                    && noti.getNotificationType().compareTo(Notification.NOTIFICATION_NOMARL_TYPE) == 0
                    && notific.getContractDetail()
                        .getStatus()
                        .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())
                    && notific != null && notific.getCompletedQty() + notific.getOccupiedAvailableQty() >= Integer
                        .parseInt(notific.getQuantity())))
            {
                // 判断是否还有投料单未生产完成
                Notification qn = new Notification(noti.getRecordId());
                qn.setCompany(noti.getCompany());
                qn.setStatus(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString() + ConstKey.COMMA
                    + TypeKey.PRODUCE_BATCH_STATUS_PAUSE.toString() + ConstKey.COMMA
                    + TypeKey.PRODUCE_BATCH_STATUS_LOSS.toString() + ConstKey.COMMA
                    + TypeKey.PRODUCE_BATCH_STATUS_OPERATION.toString());
                
                // 判断过数记录
                List<ProduceBatch> batchs = notifiDao.getBatchNoFinish(qn);
                if (Collections3.isEmpty(batchs))
                {
                    noti.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                }
            }
            else
            {
                // 3.如果该通知单所有批次全部完成入库，则该通知单的为完成入库
                // 查询该通知单对应的所有批次
                List<ProduceBatch> pbs = produceBatchDao.findPbsByNotification(noti);
                boolean notFlag = true;
                for (ProduceBatch pd : pbs)
                {
                    if (null == pd.getStatus()
                        || Integer.valueOf(pd.getStatus()).compareTo(TypeKey.PRODUCE_BATCH_STATUS_FANISH) != 0)
                    {
                        // 如果包含没有完成的明细,则该批次没有完成
                        notFlag = false;
                        break;
                    }
                }
                
                // 若所有批次都已完成
                if (notFlag)
                {
                    // 查询该通知单已入库数量
                    ProductStore productStore = new ProductStore();
                    productStore.setCompany(UserUtils.getUser().getCompany());
                    // 不查出冲红单数据
                    productStore.setStatus(TypeKey.BILL_STATUS_FOR_WRITEOFF);
                    productStore.setNotificationId(noti.getRecordId());
                    Integer quantitys = pstoreDao.quaryStockedByProduceBatchDetail(productStore);
                    if (null == quantitys)
                    {
                        quantitys = 0;
                    }
                    
                    // 占用数量 + 已入库数量 》= 订单数量，表示当前通知单已完成
                    quantitys += noti.getOccupiedAvailableQty() == null ? 0 : noti.getOccupiedAvailableQty();
                    if (quantitys.compareTo(Integer.valueOf(noti.getQuantity())) >= 0)
                    {
                        // 通知单完成状态
                        noti.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                        
                        // zjn 2020-12-02
                        handleOccupiedStock(noti, pdb);
                    }
                }
            }
        }
        
        Integer completedQty = noti.getCompletedQty() == null ? 0 : noti.getCompletedQty();
        completedQty += total;
        noti.setCompletedQty(completedQty);
        
        // ycy 2016-10-11 入库总数量小于通知单订单数量继续投补料
        if (completedQty < Integer.parseInt(noti.getQuantity()))
        {
            // 判断是否有补料单
            // 查询通知单下所有补料数量
            String replenishDiscardQty = notifiDao.getReplenishNum(noti);
            if (replenishDiscardQty != null)
            {
                if (noti.getFailedPcsQty() == null)
                {
                    noti.setFailedPcsQty(0);
                }
                if (Integer.parseInt(replenishDiscardQty) < noti.getFailedPcsQty())
                {
                    noti.setIsReplenish(TypeKey.YES);
                }
            }
            /*
             * else { noti.setIsFeeding(TypeKey.YES);// 可以继续投料 } if (noti.getIsFeeding() != null ||
             * noti.getIsReplenish() != null) { notifiDao.updateIsFeeding(noti); }
             */
        }
        // 更改通知单已完成数量和状态
        notifiDao.updateNotifiComplQtyAndStatus(noti);
        if (contract != null && StringUtils.isNotBlank(contract.getInventorySwitch())
            && contract.getInventorySwitch().equals("1"))
        {
            if (contract.getStatus() == null
                || !contract.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
            {
                updateContractStatus(contract);
            }
        }
    }
    
    // 通知单为入库状态时，需处理
    public void handleOccupiedStock(Notification notification, ProduceBatchDetail pbd)
    {
        // 更新批次明细完成入库时间
        pbd.setFinshDate(new Date());
        produceBatchDetailDao.updateFinshDate(pbd);
    }
    
    /**
     * 处理通知单、合同明细
     * 
     * @param pdb 批次明细
     * @param noti 当前入库的通知单
     * @param product 入库记录
     * @param realNoti 实际处理的通知单(合单的时候)
     */
    public void modifyNotifi(ProduceBatchDetail pdb, Notification noti, ProductStore product, Notification realNoti)
    {
        /** 修改通知单完成数量和状态 */
        if (noti.getMergeType().compareTo(TypeKey.SL_NOTIFICATION_MERGE_NEW) == 0)
        {
            // 合单区分，是否是相同编号合单，是否是不同编号合单
            boolean isSameFlag = StringUtils.isBlank(product.getBatchDetailCraftId());
            
            // 查询出所有被合单的信息
            noti.setCompany(product.getCompany());
            List<Notification> list = notifiDao.findBeMergeNotifsByMergeId(noti);
            
            // 相同生产编号合单
            if (isSameFlag)
            {
                alikeMergeNo(noti, list, product, pdb);
            }
            // 不同生产编号合单
            else
            {
                unAlikeMergeNo(noti, realNoti, product, pdb, list);
            }
        }
        else
        {
            wrongMergeNo(noti, product, pdb);
        }
    }
    
    /**
     * 生成付款记录，并修改采购订单的预付款余额
     * 
     * @param productStore 成品出入库记录
     * @param poDtl 成品采购详单
     * @param ap 付款对账单
     * @param payWay 付款方式
     * @param amount 该采购单所有费用
     */
    private void executeGenerateProductPayMoney(ProductStore productStore, PrdorderDetail poDtl, AccountsPayable ap,
        String payWay, BigDecimal amount, Integer finishInProductNum, BigDecimal isquantityQty)
    {
        Prdorder prdorder = poDtl.getPrdorder();
        BigDecimal zero = new BigDecimal(0);
        BigDecimal prePayBalance = prdorder.getPrePayBalance() == null ? zero : prdorder.getPrePayBalance();// 预付款余额
        BigDecimal prePayAmount = prdorder.getPrePayAmount() == null ? zero : prdorder.getPrePayAmount();// 预付款总额
        // 如果是正常记录且预付款余额还大于零时才添加付款记录
        if (prePayBalance.compareTo(zero) > 0
            && productStore.getStatus().intValue() == TypeKey.BILL_STATUS_NORMAL.intValue())
        {
            // 查询原来预付款对应的付款申请单
            PayApplication pa = new PayApplication();
            pa.setSupplier(prdorder.getSupplier());
            Purchasing purchasing = new Purchasing();
            purchasing.setRecordId(prdorder.getRecordId());
            pa.setPurchasing(purchasing);
            // 采购类型为成品采购
            pa.setPurchasingType(2);
            pa.setFundType(TypeKey.FUND_TYPE_PREPAYMENT.toString());
            pa.setCompany(productStore.getCompany());
            PayApplication payApplication = new PayApplication();
            List<PayApplication> paList = payApplicationDao.getPayApplicationListByBean(pa);
            if (paList != null && paList.size() > 0)
            {
                payApplication = paList.get(0);
            }
            else
            {
                payApplication.setRecordId("-1");
            }
            // 计算此批进货的对账周期
            Calendar cal = Calendar.getInstance();
            Date operateDate =
                productStore.getOperateDate() == null ? productStore.getCreatedDate() : productStore.getOperateDate();
            operateDate = operateDate == null ? new Date() : operateDate;
            cal.setTime(operateDate);
            Integer period = cal.get(Calendar.YEAR) * 100 + (cal.get(Calendar.MONTH) + 1);// 收货年月
            Integer checkDay =
                new Integer(prdorder.getSupplier().getCheckDate() == null ? 1 : prdorder.getSupplier().getCheckDate());// 约定的对账日
            if (checkDay < 15 && cal.get(Calendar.DATE) < checkDay)
            {
                // 如果仓库收货日期小于约定的对账日期且对账日期小于15日则划到上一个对周期，
                // 如果仓库收货月份正好是1月份，则上个对周期是上个年度的12月份
                period--;
                if (period % 100 == 0)
                {
                    period = (period / 100 - 1) * 100 + 12;
                }
            }
            else if (checkDay >= 15 && cal.get(Calendar.DATE) > checkDay)
            {
                // 如果仓库收货日期大于约定的对账日期且对账日期大于等于15日则划到下一个周期
                // 如果仓库收货月份正好是12月份，则下一下周期是下一年度的1月份
                // 其余的情况当划到本月对账周期内
                period++;
                if (period % 100 == 13)
                {
                    period = (period / 100 + 1) * 100 + 1;
                }
            }
            // 添加付款记录
            PayMoney pm = new PayMoney();
            pm.setCompany(productStore.getCompany());
            pm.setSupplier(prdorder.getSupplier());
            // 与原料区分
            Purchasing insertPurchasing = new Purchasing();
            purchasing.setRecordId("-" + insertPurchasing.getRecordId());
            pm.setPurchasing(insertPurchasing);
            pm.setPayApplication(payApplication);
            pm.setPeriod(period);
            pm.setPayDate(new Date());
            pm.setBillNo(ConstKey.AUTO_GENERATE_RECORD);
            pm.setPayMethod(payApplication.getPayMethod());
            pm.setReceiveAccount(payApplication.getReceiveAccount());
            pm.setFundType(TypeKey.FUND_TYPE_DUEPAYMENT.toString());
            pm.setStatus(productStore.getStatus());
            pm.setSourceId(Long.parseLong(productStore.getRecordId()));
            pm.setRemark("从成品采购单\'" + prdorder.getNo() + "\'的预付款中支付");
            pm.setPayMethod(String.valueOf(payWay));
            // 对账单中的已付金额
            BigDecimal paiedAmount = ap.getPaiedAmount() == null ? zero : ap.getPaiedAmount();
            if (prePayBalance.compareTo(amount) > 0)
            {
                // 预付款余额大于进货价值金额
                pm.setAmount(amount);
                prdorder.setPrePayBalance(prePayBalance.subtract(amount));
                prdorder.setPrePayAmount(prePayAmount.subtract(amount));
                ap.setPaiedAmount(paiedAmount.add(amount));
            }
            else
            {
                pm.setAmount(prePayBalance);
                prdorder.setPrePayBalance(zero);
                prdorder.setPrePayAmount(zero);
                ap.setPaiedAmount(paiedAmount.add(prePayBalance));
            }
            // 增加付款记录
            pm.preInsert();
            payMoneyDao.insert(pm);
            // 更新预付款记录
            prdorder.preUpdate();
            prdorderDao.updatePrdorderPrePayAmountOrBalance(prdorder);
            // 更新对账单已付款金额
            accountsPayableDao.updateApPaiedAmount(ap);
        }
        // 如是用于冲红的原材料入库记录
        if (productStore.getStatus().intValue() == (TypeKey.BILL_STATUS_FOR_WRITEOFF.intValue()))
        {
            PayMoney pm = new PayMoney();
            pm.setCompany(productStore.getCompany());
            pm.setSupplier(prdorder.getSupplier());
            // 与原料区分
            Purchasing insertPurchWrite = new Purchasing();
            insertPurchWrite.setRecordId(prdorder.getRecordId());
            pm.setPurchasing(insertPurchWrite);
            pm.setSourceId(productStore.getWriteOffId());
            List<PayMoney> pmList = payMoneyDao.findPayMoneyListByBean(pm);
            if (pmList != null && pmList.size() > 0)
            {
                PayMoney payMoney = pmList.get(0);
                payMoney.setBillNo("");
                payMoney.setWriteOffCause(productStore.getWriteOffCause());
                this.executeWriteOffPayMoney(payMoney);
                prdorder.setPrePayBalance(prePayBalance.add(amount));
                prdorderDao.updatePrdorderPrePayAmountOrBalance(prdorder);
            }
        }
    }
    
    /**
     * 
     * @param payMoney
     */
    public void executeWriteOffPayMoney(PayMoney payMoney)
    {
        // 先检查参数
        PayMoney pmBeWrtOff = payMoneyDao.get(payMoney.getRecordId());
        if (pmBeWrtOff.getStatus() != null
            && pmBeWrtOff.getStatus().equals(TypeKey.BILL_STATUS_BE_WRITEOFFED.toString()))
        {
            
            // String a="您指定的欲被冲红的记录已经被冲红过了!";
            return;
        }
        
        if (ConstKey.AUTO_GENERATE_RECORD.equals(pmBeWrtOff.getBillNo()))
        {
            
            // String b= "系统自动生产的记录不能被冲红!";
            return;
        }
        
        // 添加用于冲红的记录
        PayMoney pmForWrtOff = new PayMoney();
        try
        {
            pmForWrtOff = (PayMoney)pmBeWrtOff.clone();
        }
        catch (Exception e)
        {
            e.getMessage();
        }
        
        pmForWrtOff.setRecordId(null);
        pmForWrtOff.setWriteOffId(pmBeWrtOff.getRecordId());
        pmForWrtOff.setWriteOffCause(payMoney.getWriteOffCause());
        pmForWrtOff.setStatus(TypeKey.BILL_STATUS_FOR_WRITEOFF);
        BigDecimal amount = pmForWrtOff.getAmount() == null ? new BigDecimal(0) : pmForWrtOff.getAmount();
        pmForWrtOff.setAmount(amount.multiply(new BigDecimal(-1)));
        pmForWrtOff.preInsert();
        payMoneyDao.insert(pmForWrtOff);
        
        // 修改被冲红的记录
        pmBeWrtOff.setStatus(TypeKey.BILL_STATUS_BE_WRITEOFFED);
        pmBeWrtOff.setWriteOffId(pmForWrtOff.getRecordId());
        pmBeWrtOff.setWriteOffCause(payMoney.getWriteOffCause());
        pmBeWrtOff.preUpdate();
        payMoneyDao.update(pmBeWrtOff);
        
    }
    
    /** 生成成品退货对账单 */
    private void executeGenerateProductRtsCheck(String returnsDetailId, Integer returnsQuantity, String type)
    {
        PuReturnsDetail puReturnsDetail = new PuReturnsDetail();
        puReturnsDetail.setCompany(UserUtils.getUser().getCompany());
        puReturnsDetail.setRecordId(returnsDetailId);
        PuReturnsDetail returnsDetail = returnDetailDao.getById(puReturnsDetail);
        // 获取退货单主表
        PuReturns returnsChk = returnsDetail.getPuReturns();
        // 计算此批进货的对账周期
        Calendar cal = Calendar.getInstance();
        Date recDate =
            returnsChk.getReceivedDate() == null ? returnsChk.getCreatedDate() : returnsChk.getReceivedDate();
        cal.setTime(recDate);
        Integer period = cal.get(Calendar.YEAR) * 100 + (cal.get(Calendar.MONTH) + 1);// 收货年月
        Integer checkDay =
            returnsChk.getSupplier().getCheckDate() == null ? 1 : returnsChk.getSupplier().getCheckDate();// 约定的对账日
        if (checkDay < 15 && cal.get(Calendar.DATE) < checkDay)
        {
            // 如果仓库收货日期小于约定的对账日期且对账日期小于15日则划到上一个对周期，
            // 如果仓库收货月份正好是1月份，则上个对周期是上个年度的12月份
            period--;
            if (period % 100 == 0)
            {
                period = (period / 100 - 1) * 100 + 12;
            }
        }
        else if (checkDay >= 15 && cal.get(Calendar.DATE) > checkDay)
        {
            // 如果仓库收货日期大于约定的对账日期且对账日期大于等于15日则划到下一个周期
            // 如果仓库收货月份正好是12月份，则下一下周期是下一年度的1月份
            // 其余的情况当划到本月对账周期内
            period++;
            if (period % 100 == 13)
            {
                period = (period / 100 + 1) * 100 + 1;
            }
        }
        
        /* Begin 获得该对账周期相应的应付账款记录 */
        
        AccountsPayable ap = new AccountsPayable();
        ap.setCompany(returnsChk.getCompany());
        ap.setSupplier(returnsChk.getSupplier());
        ap.setPeriod(period);
        //
        List<AccountsPayable> apList = accountsPayableDao.findAccountsPayableListByBean(ap);
        if (apList != null && apList.size() > 0)
        {
            // 如果该对账周期的应付账款记录已有了，则在原记录是修改
            ap = apList.get(0);
            if (!ap.getAdjustFlagTwo())
            {
                ap.setAdjustFlagTwo(true);
                accountsPayableDao.updateStatus(ap);
            }
        }
        else
        {
            // 初始化其它属性
            ap.setPaiedAmount(new BigDecimal(0));
            ap.setRecvMaterialValue(new BigDecimal(0));
            ap.setRetnMaterialValue(new BigDecimal(0));
            ap.setAdjustValue(new BigDecimal(0));
            ap.setCompleteFlag(TypeKey.FALSE);
            ap.setStatus(TypeKey.PAYABLE_STATUS_UNCONFIRMEND);
            ap.setAdjustFlagTwo(true);
            ap.preInsert();
            /* End 如果无该对账周期的应付账款记录，则新生成一条 */
        }
        // 将退货单中的数据导入采购进货/退货对账单中
        MaterialCheck mc = new MaterialCheck();
        mc.setCompany(returnsChk.getCompany());
        mc.setSupplier(returnsChk.getSupplier());
        // 根据成品ID查询成品信息，获取物料
        PrdorderDetail prdorderDetail = new PrdorderDetail();
        prdorderDetail.setCompany(returnsDetail.getCompany());
        prdorderDetail.setRecordId(returnsDetail.getPurchasingDetailId().toString());
        PrdorderDetail prdoDetail = proDetailDao.getProductOrderDetailById(prdorderDetail);
        if (prdoDetail == null || prdoDetail.getRecordId() == null)
        {
            throw new ServiceException(ErrorCode.ERROR_STORE_ILLEGALARGUMENT_VIOLATEBUSINESSRULE.toString(),
                "您所指定的采购订单明细项不存在!");
        }
        Material material =
            materialDao.findMaterialByNo(new Material(prdoDetail.getContractCraft().getNo(), prdoDetail.getCompany()));
        if (material == null || material.getRecordId() == null)
        {
            throw new ServiceException(ErrorCode.ERROR_STORE_ILLEGALARGUMENT_VIOLATEBUSINESSRULE.toString(), "物料不存在!");
        }
        mc.setMaterial(material);
        if (type.equals(TypeKey.MATERIAL_INOUTFLAG_IN.toString()))
        {
            mc.setInOutFlag(TypeKey.MATERIAL_INOUTFLAG_IN);
        }
        else
        {
            mc.setInOutFlag(TypeKey.MATERIAL_INOUTFLAG_RETURN);
        }
        mc.setOperateStatus(TypeKey.MATERIAL_STATUS_UNCONFIRM);
        mc.setPeriod(period);
        mc.setReceivedDate(recDate);
        mc.setBillNo(returnsChk.getNo());
        BigDecimal price = returnsDetail.getPrice() == null ? new BigDecimal(0) : returnsDetail.getPrice();
        // 仅对采购成品退货数量生成对账单
        mc.setQuantity(new BigDecimal(returnsQuantity));
        mc.setPrice(price);
        mc.setAmount(MathUtils.getBigDecimal(price.multiply(new BigDecimal(returnsQuantity))));
        mc.setSourceId(returnsDetail.getRecordId());// 产生此条记录的源记录ID
        mc.setStatus(TypeKey.BILL_STATUS_NORMAL);// 冲红状态也产生此条记录的源记录一致
        mc.preInsert();
        // 修改采购应付款对账单的数据
        if (type.equals(TypeKey.MATERIAL_INOUTFLAG_IN.toString()))
        {
            ap.setRecvMaterialValue(MathUtils.getBigDecimal(ap.getRecvMaterialValue().add(mc.getAmount())));
        }
        else
        {
            ap.setRetnMaterialValue(MathUtils.getBigDecimal(ap.getRetnMaterialValue().add(mc.getAmount())));
        }
        // 保存退货对账记录
        materialCheckDao.insert(mc);
        
        // 修改或添加采购庆付款对账单记录
        if (ap.getIsNewRecord())
        {
            accountsPayableDao.insert(ap);
        }
        else
        {
            ap.preUpdate();
            accountsPayableDao.updateAccountsPayable(ap);
        }
    }
    
    /** 生成成品采购对账单 */
    private AccountsPayable executeGenerateProductCheck(ProductStore productStore, PrdorderDetail poDtl,
        Integer quantity, Integer alInProductNum)
    {
        // 计算此批进货的对账周期
        Calendar cal = Calendar.getInstance();
        Date operateDate =
            productStore.getOperateDate() == null ? productStore.getCreatedDate() : productStore.getOperateDate();
        cal.setTime(operateDate);
        Integer period = cal.get(Calendar.YEAR) * 100 + (cal.get(Calendar.MONTH) + 1);// 收货年月
        Integer checkDay = poDtl.getPrdorder().getSupplier().getCheckDate() == null ? 1
            : poDtl.getPrdorder().getSupplier().getCheckDate();// 约定的对账日
        if (checkDay < 15 && cal.get(Calendar.DATE) < checkDay)
        {
            // 如果仓库收货日期小于约定的对账日期且对账日期小于15日则划到上一个对周期，
            // 如果仓库收货月份正好是1月份，则上个对周期是上个年度的12月份
            period--;
            if (period % 100 == 0)
            {
                period = (period / 100 - 1) * 100 + 12;
            }
        }
        else if (checkDay >= 15 && cal.get(Calendar.DATE) > checkDay)
        {
            // 如果仓库收货日期大于约定的对账日期且对账日期大于等于15日则划到下一个周期
            // 如果仓库收货月份正好是12月份，则下一下周期是下一年度的1月份
            // 其余的情况当划到本月对账周期内
            period++;
            if (period % 100 == 13)
            {
                period = (period / 100 + 1) * 100 + 1;
            }
        }
        /* Begin 获得该对账周期相应的应付账款记录 */
        AccountsPayable ap = new AccountsPayable();
        ap.setCompany(poDtl.getCompany());
        ap.setSupplier(poDtl.getPrdorder().getSupplier());
        ap.setPeriod(period);
        //
        List<AccountsPayable> apList = accountsPayableDao.findAccountsPayableListByBean(ap);
        if (apList != null && apList.size() > 0)
        {
            // 如果该对账周期的应付账款记录已有了，则在原记录是修改
            ap = apList.get(0);
            if (!ap.getAdjustFlagTwo())
            {
                ap.setAdjustFlagTwo(true);
                accountsPayableDao.updateStatus(ap);
            }
        }
        else
        {
            // 初始化其它属性
            ap.setPaiedAmount(BigDecimal.ZERO);
            ap.setRecvMaterialValue(BigDecimal.ZERO);
            ap.setRetnMaterialValue(BigDecimal.ZERO);
            ap.setAdjustValue(BigDecimal.ZERO);
            ap.setCompleteFlag(TypeKey.FALSE);
            ap.setStatus(TypeKey.PAYABLE_STATUS_UNCONFIRMEND);
            ap.setAdjustFlagTwo(true);
            ap.preInsert();
            /* End 如果无该对账周期的应付账款记录，则新生成一条 */
        }
        /* End 获得该对账周期相应的应收账款记录 */
        // 将采购订单中的数据导入到“原材料进货/退货对账单”表中
        MaterialCheck mc = new MaterialCheck();
        mc.setCompany(poDtl.getCompany());
        mc.setSupplier(poDtl.getPrdorder().getSupplier());
        Material material =
            materialDao.findMaterialByNo(new Material(poDtl.getContractCraft().getNo(), poDtl.getCompany()));
        mc.setMaterial(material);
        mc.setInOutFlag(TypeKey.MATERIAL_INOUTFLAG_IN);
        mc.setOperateStatus(TypeKey.MATERIAL_STATUS_UNCONFIRM);
        mc.setPeriod(period);
        mc.setReceivedDate(operateDate);
        mc.setBillNo(poDtl.getPrdorder().getNo());
        
        // zjn 2018-07-11 赋值费用:模具费、其它费用、测试架费、工程费
        mc.setMouldFee(poDtl.getPrice().getMouldFee());
        mc.setOthersFee(poDtl.getPrice().getOthersFee());
        mc.setTestShelfFee(poDtl.getPrice().getTestShelfFee());
        mc.setEngineeringFee(poDtl.getPrice().getEngineeringFee());
        
        // 根据采购单数量生成对账单
        mc.setQuantity(new BigDecimal(quantity));
        mc.setPrice(poDtl.getPrice().getPrice());
        // fzd 2016-12-28 采购明细未入库算其它费用，入过库了就不算了
        List<ProductStore> proStockList = pstoreDao.getProStoreNum(productStore);
        if (Collections3.isNotEmpty(proStockList) && proStockList.size() > 1)
        {
            mc.setAmount(poDtl.getAmountTwo(quantity));
        }
        else
        {
            mc.setAmount(poDtl.getAmountNew(quantity));
        }
        mc.setAmount(MathUtils.getBigDecimal(mc.getAmount()));
        mc.setSourceId(productStore.getRecordId());// 产生此条记录的源记录ID
        mc.setStatus(Integer.valueOf(productStore.getStatus()));// 冲红状态也产生此条记录的源记录一致
        mc.setWriteOffCause(productStore.getWriteOffCause()); // 冲红原因与产生此条记录的源记录一致
        
        mc.preInsert();
        // 保存进货记录
        materialCheckDao.insert(mc);
        // 保存或修改应收账款记录
        BigDecimal recvMaterialValue = ap.getRecvMaterialValue() == null ? BigDecimal.ZERO : ap.getRecvMaterialValue();
        // 收到原材料总金额
        ap.setRecvMaterialValue(MathUtils.getBigDecimal(recvMaterialValue.add(mc.getAmount())));
        // ap.setClosingBalance(ap.getClosingBalance().add(mc.getAmount()));
        if (ap.getRecordId() == null)
        {
            accountsPayableDao.insert(ap);
        }
        else
        {
            accountsPayableDao.updateAccountsPayable(ap);
        }
        
        return ap;
    }
    
    /**
     * 根据供应商查找采购明细
     * 
     * @param page
     * @param recordId
     * @return
     */
    public Page<ForStockObject> queryPurchaseMaterial(Page<ForStockObject> page, ForStockObject obj)
    {
        PrdorderDetail prochDetl = new PrdorderDetail();
        User user = UserUtils.getUser();
        prochDetl.setCompany(user.getCompany());
        if (null != obj.getQueryAll() && !obj.getQueryAll())
        {
            prochDetl.setCreatedBy(user);
        }
        Page<PrdorderDetail> chdetlPage = new Page<PrdorderDetail>();
        chdetlPage.setPageNo(page.getPageNo());
        chdetlPage.setPageSize(page.getPageSize());
        if (page.getOrderBy() != null && page.getOrderBy() != "")
        {
            chdetlPage.setOrderBy(page.getOrderBy());
        }
        prochDetl.setPage(chdetlPage);
        if (obj.getSupplier() != null && obj.getSupplier().getRecordId() != null
            && obj.getSupplier().getRecordId() != "")
        {
            // 设置供应商查询条件
            prochDetl.setSupplierId(obj.getSupplier().getRecordId());
        }
        prochDetl.setStatus(TypeKey.PU_PURCHASING_STATUS_CONFIRMED.toString());
        prochDetl.setStatus1(TypeKey.PU_PURCHASING_STATUS_AUDITED.toString());
        
        List<ForStockObject> objList = new LinkedList<ForStockObject>();
        
        dealPage(prochDetl, chdetlPage, user.getCompany(), objList);
        
        page.setCount(chdetlPage.getCount());
        page.setList(objList);
        return page;
    }
    
    private void dealPage(PrdorderDetail prochDetl, Page<PrdorderDetail> chdetlPage, Company company,
        List<ForStockObject> objList)
    {
        List<PrdorderDetail> list = proDetailDao.findChaDetlBySupplier(prochDetl);
        chdetlPage.setList(list);
        int count = 0;
        if (list != null && list.size() > 0)
        {
            for (PrdorderDetail pro : list)
            {
                ForStockObject object = new ForStockObject();
                object.setSaleComId(pro.getSaleComId());
                String materialNo = pro.getContractCraft().getNo();
                object.setParam1(pro.getPrdorder().getNo());
                object.setPuPrice(pro.getPrice().getPrice());
                // 设置合同编号
                if (pro.getContractDetail() != null)
                {
                    ProduceBatchDetail produceBatchDetail = new ProduceBatchDetail();
                    Notification notification = new Notification();
                    notification.setContractDetail(pro.getContractDetail());
                    produceBatchDetail.setNotification(notification);
                    object.setProduceBatchDetail(produceBatchDetail);
                    
                    // 判断是否需要寄存WC 20170222
                    if (pro.getContractDetail().getDeposit() && pro.getContractDetail()
                        .getStatus()
                        .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
                    {
                        object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_YES);
                    }
                    else
                    {
                        object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_NO);
                    }
                }
                
                Material material = materialDao.findMaterialByNo(new Material(materialNo, company));
                if (material != null)
                {
                    Map<String, Integer> availableStockMaps = this.quaryAvailableStocked(pro.getQuantity(),
                        material.getRecordId(),
                        null,
                        pro.getRecordId(),
                        null,
                        null,
                        pro.getCompany().getRecordId());
                    Integer availCount = availableStockMaps.get(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_A);
                    // 查询该采购订单的不良品数量
                    SourceDetection sd = new SourceDetection();
                    sd.setOrderId(pro.getRecordId());
                    sd.setCompany(company);
                    sd.setResourceType(ConstKey.PA_PRDPRODUCT_FLAG);
                    sd.setStatus(TypeKey.ST_SOURCEDETECTION_STATUS_AUDITED);
                    SourceDetection detection = sourceDetectionDao.getNumSum(sd);
                    BigDecimal poorCount = detection.getPoorNum() != null ? detection.getPoorNum() : BigDecimal.ZERO;
                    if (poorCount == null)
                    {
                        poorCount = BigDecimal.ZERO;
                    }
                    // ycy 2016-10-10 成品来料检测数量
                    BigDecimal detectionNum =
                        detection.getDetectionNum() != null ? detection.getDetectionNum() : BigDecimal.ZERO;
                    
                    if (detectionNum == null)
                    {
                        detectionNum = BigDecimal.ZERO;
                    }
                    // ycy 2016-10-10 待入库数量
                    BigDecimal detectionNums =
                        new BigDecimal(MathUtils.sub(detectionNum.doubleValue(), poorCount.doubleValue(), 0));
                    // ycy 2016-10-10 待入库数量==0 不加载
                    // 待入库数量需要减去不良数量
                    availCount = availCount - poorCount.intValue();
                    if (availCount.compareTo(0) <= 0)
                    {
                        // 已完成入库
                        object.setStatus(ConstKey.IN_OUT_FINISH);
                    }
                    object.setMaterialId(material.getRecordId());
                    object.setMaterialNo(material.getNo());
                    object.setMaterialName(material.getName());
                    object.setSpecification(material.getSpecification());
                    object.setStocks(material.getStocks());
                    object.setStorehouse(material.getStorehouse());
                    // 不良数量
                    object.setPrdPoorCount(poorCount);
                    // 待入库数量
                    object.setAvailCount(detectionNums.intValue());
                    // 已入库数量
                    object.setUnavailCount(availableStockMaps.get(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_UNA));
                    // 赠品数量
                    object.setGiveNum(detection.getGiveNum() != null ? detection.getGiveNum() : BigDecimal.ZERO);
                    // 成品出入库面积inAreaReport
                    Map<String, Object> areaAndPrDimensionMaps =
                        getAreaAndPrDimension(pro.getContractDetail(), detectionNums.intValue());
                    object.setInAreaReport(
                        new BigDecimal(areaAndPrDimensionMaps.get(ConstKey.PRODUCT_IN_OUT_AREA).toString()));
                    // 成品尺寸prDimension
                    object.setPrDimension((areaAndPrDimensionMaps.get(ConstKey.PRODUCT_IN_OUT_DIMENSION)).toString());
                }
                else
                {
                    object.setAvailCount(pro.getQuantity());
                    object.setMaterialNo(materialNo);
                }
                
                object.setAvailSumCount(pro.getQuantity());
                object.setPurOrderDetailId(pro.getRecordId());
                object.setPurchModel(pro.getContractCraft().getCustomerModel());
                object.setCustomerNo(pro.getContractDetail().getContract().getCustomer().getNo());
                object.setCustomerId(pro.getContractDetail().getContract().getCustomer().getRecordId());
                object.setContractDetailId(pro.getContractDetail().getRecordId());
                
                if (objList.size() < chdetlPage.getPageSize())
                {
                    count++;
                    objList.add(object);
                }
                else
                {
                    break;
                }
            }
        }
        
        if (count < 0)
        {
            chdetlPage.setPageNo(chdetlPage.getPageNo() + 1);
            chdetlPage.setCount(chdetlPage.getCount() - chdetlPage.getPageSize());
            dealPage(prochDetl, chdetlPage, company, objList);
        }
    }
    
    // 计算待入库面积和尺寸
    public Map<String, Object> getAreaAndPrDimension(ContractDetail contractDetail, Integer availCount)
    {
        
        // 成品出入库面积inAreaReport
        // 成品尺寸prDimension
        Map<String, Object> areaAndPrDimensionMaps = new HashMap<String, Object>();
        BigDecimal area = BigDecimal.ZERO;
        String prDemesion = "";
        
        ContractDetail contractDetailEntity = contractDetailDao.get(contractDetail);
        if (contractDetailEntity != null && contractDetailEntity.getCraft() != null)
        {
            ContractCraft craft = contractDetailEntity.getCraft();
            if (craft != null)
            {
                if (craft.getPnlLength() != null && craft.getPnlLength().compareTo(BigDecimal.ZERO) == 1
                    && craft.getPnlWidth() != null && craft.getPnlWidth().compareTo(BigDecimal.ZERO) == 1
                    && craft.getPnlDivisor() != null)
                {
                    // 采购尺寸不为空
                    BigDecimal pnlLength = craft.getPnlLength();
                    BigDecimal pnlWidth = craft.getPnlWidth();
                    BigDecimal pnlDivisor = craft.getPnlDivisor();
                    // 采购尺寸
                    prDemesion = pnlLength + "x" + pnlWidth + "/" + pnlDivisor;
                    
                    area = pnlLength.multiply(pnlWidth)
                        .multiply(new BigDecimal(availCount))
                        .divide(pnlDivisor, 4, BigDecimal.ROUND_HALF_UP)
                        .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
                }
                else if (craft.getUnitLength() != null && craft.getUnitWidth() != null)
                {
                    // 采购尺寸为空,取单位尺寸
                    BigDecimal unitLength = craft.getUnitLength();
                    BigDecimal unitWidth = craft.getUnitWidth();
                    BigDecimal pnlDivisor = null == craft.getPnlDivisor() ? new BigDecimal("1") : craft.getPnlDivisor();
                    area = unitLength.multiply(unitWidth)
                        .multiply(new BigDecimal(availCount))
                        .divide(pnlDivisor, 4, BigDecimal.ROUND_HALF_UP)
                        .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
                    
                    // 采购尺寸
                    prDemesion = unitLength + "x" + unitWidth + "/" + pnlDivisor;
                }
            }
        }
        areaAndPrDimensionMaps.put(ConstKey.PRODUCT_IN_OUT_AREA, area);
        areaAndPrDimensionMaps.put(ConstKey.PRODUCT_IN_OUT_DIMENSION, prDemesion);
        
        return areaAndPrDimensionMaps;
    }
    
    // 计算待入库数量
    public Map<String, Integer> quaryAvailableStocked(Integer quantity, String materialId, String notificationId,
        String prdOrderDetailId, String deliveryNo, String deliveryDetailId, String companyId)
    {
        // availCount:待入库数量
        // unavailCount:已入库数量
        Map<String, Integer> availableStocksMap = new HashMap<String, Integer>();
        // 不查出冲红单数据
        Integer status = TypeKey.BILL_STATUS_FOR_WRITEOFF;
        List<Integer> quantitys = pstoreDao.quaryStocked(materialId,
            notificationId,
            prdOrderDetailId,
            deliveryNo,
            deliveryDetailId,
            status,
            companyId);
        if (quantitys != null && quantitys.size() > 0)
        {
            if (quantitys.size() == 1)
            {
                availableStocksMap.put(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_UNA, quantitys.get(0));
                Integer s = quantity - quantitys.get(0);
                availableStocksMap.put(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_A, s);
            }
            else
            {
                Integer sum = 0;
                for (Integer i : quantitys)
                {
                    sum += i;
                }
                availableStocksMap.put(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_UNA, sum);
                Integer s1 = quantity - sum;
                availableStocksMap.put(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_A, s1);
            }
        }
        else
        {
            availableStocksMap.put(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_UNA, 0);
            availableStocksMap.put(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_A, quantity);
        }
        return availableStocksMap;
    }
    
    /**
     * //成品出库分页数据查看
     * 
     * @param page
     * @return
     */
    public List<ForStockObject> queryDeliveryDetails(String no, String start, Boolean queryAll)
    {
        DeliveryDetail deliveryDetail = new DeliveryDetail();
        User user = UserUtils.getUser();
        if (null != queryAll && !queryAll)
        {
            deliveryDetail.setCreatedBy(user);
        }
        deliveryDetail.setCompany(user.getCompany());
        /** 查询已确认过的送货单 */
        deliveryDetail.setStatus(TypeKey.PU_ORDER_DELIVERY_STATUS_CONFIRMED);
        // 查询出所有的送货明细单
        deliveryDetail.setPageNo(start == null ? "0" : start);
        deliveryDetail.setPageSize(ConstKey.Max_SHOW_SIZE);
        if (no != null && !no.equals("undefined"))
        {
            Delivery deliery = new Delivery();
            deliery.setNo(no);
            deliveryDetail.setDelivery(deliery);
        }
        List<DeliveryDetail> list = deliveryDetailDao.queryDeliveryDetails(deliveryDetail);
        List<ForStockObject> objList = new LinkedList<ForStockObject>();
        for (DeliveryDetail delyDetail : list)
        {
            ForStockObject object = new ForStockObject();
            Material material = delyDetail.getMaterial();
            object.setMaterialNo(material.getNo());
            object.setDeliveryNo(delyDetail.getDelivery().getNo());
            objList.add(object);
        }
        return objList;
    }
    
    /**
     * 成品出库分页数据查看
     * 
     * @param page
     * @return
     */
    public Page<ForStockObject> queryDeliveryMaterial(Page<ForStockObject> page, ForStockObject obj)
    {
        DeliveryDetail deliveryDetail = new DeliveryDetail();
        User user = UserUtils.getUser();
        if (null != obj.getQueryAll() && !obj.getQueryAll())
        {
            deliveryDetail.setCreatedBy(user);
        }
        deliveryDetail.setCompany(user.getCompany());
        
        Page<DeliveryDetail> deliveryDetailPage = new Page<DeliveryDetail>();
        deliveryDetailPage.setPageNo(page.getPageNo());
        deliveryDetailPage.setPageSize(page.getPageSize());
        if (page.getOrderBy() != null && !"".equals(page.getOrderBy()))
        {
            deliveryDetailPage.setOrderBy(page.getOrderBy());
        }
        // 送货出库查询生产编号条件
        if (obj.getContractCraftNo() != null)
        {
            deliveryDetail.setContractCraftNo(obj.getContractCraftNo());
        }
        if (obj.getFinalNo() != null)
        {
            deliveryDetail.setFinalNo(obj.getFinalNo());
        }
        if (obj.getSaleName() != null)
        {
            deliveryDetail.setSaleName(obj.getSaleName());
        }
        if (obj.getRemovalType() != null)
        {
            deliveryDetail.setRemovalType(obj.getRemovalType());
        }
        else
        {
            deliveryDetail.setRemovalType("1");
        }
        deliveryDetail.setPage(deliveryDetailPage);
        /** 查询已确认过的送货单 */
        deliveryDetail.setStatus(TypeKey.PU_ORDER_DELIVERY_STATUS_CONFIRMED);
        // 查询出所有的送货明细单
        if (obj.getParam1() != null && !"".equals(obj.getParam1()) && !"所有".equals(obj.getParam1()))
        {
            Delivery dl = new Delivery();
            dl.setNo(obj.getParam1().trim());
            deliveryDetail.setDelivery(dl);
        }
        List<DeliveryDetail> list = deliveryDetailDao.findList(deliveryDetail);
        List<ForStockObject> objList = new LinkedList<ForStockObject>();
        deliveryDetailPage.setList(list);
        if (list != null && list.size() > 0)
        {
            for (DeliveryDetail delyDetail : list)
            {
                ForStockObject object = new ForStockObject();
                object.setSaleComId(delyDetail.getSaleComId());
                object.setFinalNo(delyDetail.getFinalNo());
                object.setSaleName(delyDetail.getSaleName());
                object.setParam1(delyDetail.getDelivery().getNo());
                // 设置合同编号
                if (delyDetail.getContractDetail() != null)
                {
                    ProduceBatchDetail produceBatchDetail = new ProduceBatchDetail();
                    Notification notification = new Notification();
                    notification.setContractDetail(delyDetail.getContractDetail());
                    produceBatchDetail.setNotification(notification);
                    object.setProduceBatchDetail(produceBatchDetail);
                }
                // 判断是否寄存wc 2017-02-23
                if (delyDetail.getDelivery().getItemType() == 3)
                {
                    // 如果是寄存送货单
                    object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_YES);
                }
                else if (delyDetail.getDelivery().getItemType() == 2)
                {
                    // 如果是客诉送货单
                    if (delyDetail.getComplaint() != null && "1".equals(delyDetail.getComplaint().getStyle()))
                    {
                        // 如果是寄存客诉单
                        object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_YES);
                    }
                    else
                    {
                        object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_NO);
                    }
                }
                else
                {
                    object.setIsDeposit(ConstKey.CUSTOMER_DEPOSIT_NO);
                }
                
                Material material = delyDetail.getMaterial();
                Integer spares = delyDetail.getSpares() == null ? 0 : delyDetail.getSpares();
                Integer allAvailmount = delyDetail.getQuantity() + spares;
                if (material == null || StringUtils.isBlank(material.getRecordId()))
                {
                    material = new Material();
                }
                Map<String, Integer> availableStockMaps = this.quaryAvailableStocked(allAvailmount,
                    material.getRecordId(),
                    null,
                    null,
                    null,
                    delyDetail.getRecordId(),
                    delyDetail.getCompany().getRecordId());
                Integer availCount = availableStockMaps.get(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_A);
                if (availCount.compareTo(0) <= 0)
                {
                    // 已完成出库
                    object.setStatus(ConstKey.IN_OUT_FINISH);
                }
                object.setMaterialId(material.getRecordId());
                object.setMaterialNo(material.getNo());
                object.setMaterialName(material.getName());
                object.setSpecification(material.getSpecification());
                object.setStocks(material.getStocks());
                object.setDeliveryOaQty(material.getDeliveryOaQty());
                object.setStorehouse(material.getStorehouse());
                // 待送货数量
                object.setAvailCount(availCount);
                // 已送货数量
                object.setUnavailCount(availableStockMaps.get(ConstKey.PRODUCT_IN_OUT_COUNT_FLAG_UNA));
                // 送货总数量
                object.setAvailSumCount(allAvailmount);
                object.setDeliveryDetailId(delyDetail.getRecordId());
                
                // zjn 2019-01-25 送货单明细修改时间
                object.setLastUpdDate(delyDetail.getDelivery().getLastUpdDate());
                
                if (delyDetail.getContractDetail() != null)
                {
                    object.setCustomerNo(delyDetail.getContractDetail().getContract().getCustomer().getNo());
                    object.setCustomerId(delyDetail.getContractDetail().getContract().getCustomer().getRecordId());
                    object.setContractDetailId(delyDetail.getContractDetail().getRecordId());
                }
                // 成品待送货面积inAreaReport 送货分为客诉送货和正常送货，数据库中客诉送货是关联合同明细的，客诉送货只关联物料合同明细为空
                if (delyDetail.getContractDetail() != null)
                {
                    Map<String, Object> areaAndPrDimensionMaps =
                        getAreaAndPrDimension(delyDetail.getContractDetail(), availCount);
                    object.setInAreaReport(
                        new BigDecimal(areaAndPrDimensionMaps.get(ConstKey.PRODUCT_IN_OUT_AREA).toString()));
                    // 成品待送货尺寸prDimension
                    object.setPrDimension((areaAndPrDimensionMaps.get(ConstKey.PRODUCT_IN_OUT_DIMENSION)).toString());
                    objList.add(object);
                }
                else if (delyDetail.getMaterial() != null)
                {
                    Map<String, Object> areaAndPrDimensionMaps =
                        getRejectAreaAndPrDimension(delyDetail.getMaterial(), availCount);
                    object.setInAreaReport(
                        new BigDecimal(areaAndPrDimensionMaps.get(ConstKey.PRODUCT_IN_OUT_AREA).toString()));
                    // 成品待送货尺寸prDimension
                    object.setPrDimension((areaAndPrDimensionMaps.get(ConstKey.PRODUCT_IN_OUT_DIMENSION)).toString());
                    objList.add(object);
                }
                
            }
        }
        page.setCount(deliveryDetailPage.getCount());
        page.setList(objList);
        return page;
    }
    
    /**
     * 客诉送货 2016.10.25 ojh
     * 
     * @param material 客诉送货关联的物料
     * @param availCount 客诉送货的数量
     */
    private Map<String, Object> getRejectAreaAndPrDimension(Material material, Integer availCount)
    {
        // 成品出入库面积inAreaReport
        // 成品尺寸prDimension
        Map<String, Object> areaAndPrDimensionMaps = new HashMap<String, Object>();
        BigDecimal area = BigDecimal.ZERO;
        String prDemesion = "";
        
        ContractCraft craft = contractCraftDao.getContractCraft(material);
        // 成品库存面积inAreaReport
        if (craft != null)
        {
            // 其pnldivisor之前数据可能没有填为空，所以现在要用的话需要纠正和判断，默认最小值为1
            if (craft.getPnlDivisor() == null || craft.getPnlDivisor().toString() == "")
            {
                craft.setPnlDivisor(new BigDecimal(1));
            }
            
            if (craft.getPnlLength() != null && craft.getPnlLength().compareTo(BigDecimal.ZERO) == 1
                && craft.getPnlWidth() != null && craft.getPnlWidth().compareTo(BigDecimal.ZERO) == 1)
            {
                // 交货尺寸不为空
                BigDecimal pnlLength = craft.getPnlLength();
                BigDecimal pnlWidth = craft.getPnlWidth();
                BigDecimal pnlDivisor = craft.getPnlDivisor();
                area = pnlLength.multiply(pnlWidth)
                    .multiply(new BigDecimal(availCount))
                    .divide(pnlDivisor, 4, BigDecimal.ROUND_HALF_UP)
                    .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
                
                prDemesion = pnlLength + "x" + pnlWidth + "/" + pnlDivisor;
            }
            else if (craft.getUnitLength() != null && craft.getUnitLength().compareTo(BigDecimal.ZERO) == 1
                && craft.getUnitWidth() != null && craft.getUnitWidth().compareTo(BigDecimal.ZERO) == 1)
            {
                // 交货尺寸为空,取单位尺寸
                BigDecimal unitLength = craft.getUnitLength();
                BigDecimal unitWidth = craft.getUnitWidth();
                BigDecimal pnlDivisor = craft.getPnlDivisor();
                area = unitLength.multiply(unitWidth)
                    .multiply(new BigDecimal(availCount))
                    .divide(pnlDivisor, 4, BigDecimal.ROUND_HALF_UP)
                    .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
                
                prDemesion = unitLength + "x" + unitWidth + "/" + pnlDivisor;
            }
        }
        
        areaAndPrDimensionMaps.put(ConstKey.PRODUCT_IN_OUT_AREA, area);
        areaAndPrDimensionMaps.put(ConstKey.PRODUCT_IN_OUT_DIMENSION, prDemesion);
        
        return areaAndPrDimensionMaps;
    }
    
    /**
     * 获取所有的客诉单
     * 
     * @return
     */
    public List<RejectApplication> queryRejectApplicats(Boolean queryAll)
    {
        RejectApplication reject = new RejectApplication();
        User user = UserUtils.getUser();
        if (null != queryAll && !queryAll)
        {
            reject.setCreatedBy(user);
        }
        reject.setCompany(user.getCompany());
        reject.setStatus(TypeKey.ST_REJECTAPPLICATION_STATUS_RETURNAUDITED);
        return rejectApplicationDao.queryRejectApplicats(reject);
    }
    
    /** 生成客户退货对账单 */
    private void executeGenerateGoodsCheck(ProductStore productStore)
    {
        // 计算此退货单的对账周期
        Calendar cal = Calendar.getInstance();
        cal.setTime(productStore.getOperateDate());
        Integer period = cal.get(Calendar.YEAR) * 100 + (cal.get(Calendar.MONTH) + 1);// 收货年月
        Integer checkDay =
            new Integer(StringUtils.isEmpty(productStore.getRejectApplication().getCustomer().getCheckDate()) ? "1"
                : productStore.getRejectApplication().getCustomer().getCheckDate());// 约定的对账日
        if (checkDay < 15 && cal.get(Calendar.DATE) < checkDay)
        {
            // 如果仓库收货日期小于约定的对账日期且对账日期小于15日则划到上一个对周期，
            // 如果仓库收货月份正好是1月份，则上个对周期是上个年度的12月份
            period--;
            if (period % 100 == 0)
            {
                period = (period / 100 - 1) * 100 + 12;
            }
        }
        else if (checkDay >= 15 && cal.get(Calendar.DATE) > checkDay)
        {
            // 如果仓库收货日期大于约定的对账日期且对账日期大于等于15日则划到下一个周期
            // 如果仓库收货月份正好是12月份，则下一下周期是下一年度的1月份
            // 其余的情况当划到本月对账周期内
            period++;
            if (period % 100 == 13)
            {
                period = (period / 100 + 1) * 100 + 1;
            }
        }
        /* Begin 获得该对账周期相应的应收账款记录 */
        AccountsReceivable ar = new AccountsReceivable();
        ar.setCompany(productStore.getCompany());
        ar.setCustomer(productStore.getContractDetail().getContract().getCustomer());// 取得客户方式和老版不一样
        ar.setPeriod(period);
        List<AccountsReceivable> arList = accountReceivableDao.findAccountsReceivableListByBean(ar);
        if (arList != null && arList.size() > 0)
        {
            // 如果该对账周期的应收账款记录已有了，则在原记录是修改
            ar = arList.get(0);
            if (!ar.getAdjustFlag())
            {
                ar.setAdjustFlag(true);
                accountReceivableDao.updateStatus(ar);
            }
        }
        else
        {
            /* 如果无该对账周期的应收账款记录，则新生成一条 */
            // 初始化其它属性
            ar.setSentGoodsValue(new BigDecimal(0));
            ar.setReturnGoodsValue(new BigDecimal(0));
            ar.setReceivedAmount(new BigDecimal(0));
            ar.setAdjustValue(new BigDecimal(0));
            ar.setCompleteFlag(TypeKey.FALSE);
            ar.setStatus(TypeKey.RECEIVABLE_STATUS_UNCONFIRMEND);
            ar.setAdjustFlag(true);
            ar.preInsert();
        }
        /* End 获得该对账周期相应的应收账款记录 */
        // 将退货单中的数据导入到“货物对账单”表中
        GoodsCheck gc = new GoodsCheck();
        gc.setCompany(productStore.getCompany());
        gc.setCustomer(productStore.getCustomer());
        gc.setInOutFlag(TypeKey.GOODS_INOUTFLAG_RETURN);
        gc.setOperateStatus(TypeKey.GOODSCHECK_STATUS_UNCONFIRM);
        gc.setReceivedDate(productStore.getOperateDate());
        gc.setPeriod(period);
        gc.setCustomerFileName(productStore.getContractDetail().getCustomerFileName());
        gc.setCraftNo(productStore.getContractDetail().getCraft().getNo());
        // WC 2017-05-19 耐立德对账单打印数据需要
        gc.setUnitWidth(productStore.getContractDetail().getCraft().getUnitWidth());
        gc.setUnitLength(productStore.getContractDetail().getCraft().getUnitLength());
        gc.setBoardThickness(productStore.getContractDetail().getCraft().getBoardThickness());
        String customerPo = productStore.getContractDetail().getContract().getCustomerPo();
        if (StringUtils.isNotBlank(customerPo))
        {
            gc.setCustomerPo(customerPo);
        }
        else
        {
            gc.setCustomerPo(productStore.getContractDetail().getContract().getNo());
        }
        gc.setSpecification(productStore.getContractDetail().getCraft().getCustomerModel());
        Integer qty = productStore.getRejectApplication().getQuantity() == null ? 0
            : productStore.getRejectApplication().getQuantity();
        gc.setQuantity(qty);
        gc.setSourceId(Long.parseLong(productStore.getRecordId()));
        // 冲红原因与生产本条记录的源记录保持一致
        gc.setStatus(new Integer(productStore.getStatus()));
        gc.setWriteOffCause(productStore.getWriteOffCause());
        BigDecimal price = productStore.getContractDetail().getPricees().getPrice();
        price = price == null ? new BigDecimal(0) : price;
        gc.setPrice(price.multiply(new BigDecimal(-1)));
        
        // zjn 2018-10-29 客诉金额、备注赋值
        gc.setAmount(MathUtils.getBigDecimal(productStore.getRejectApplication().getAmount()));
        gc.setRemark(productStore.getRejectApplication().getRejectCause());
        
        gc.preInsert();
        // 保存送货记录
        goodsCheckDao.insert(gc);
        // 保存或修改应收账款记录
        if (ar.getReturnGoodsValue() == null)
        {
            ar.setReturnGoodsValue(MathUtils.getBigDecimal(gc.getAmount()));
        }
        else
        {
            ar.setReturnGoodsValue(MathUtils.getBigDecimal(ar.getReturnGoodsValue().add(gc.getAmount())));
        }
        if (ar.getRecordId() == null)
        {
            accountReceivableDao.insert(ar);
        }
        else
        {
            if (BigDecimal.ZERO.compareTo(ar.getRcvAmount()) >= 0)
            {
                ar.setCompleteFlag(TypeKey.TRUE);
            }
            else
            {
                ar.setCompleteFlag(TypeKey.FALSE);
            }
            accountReceivableDao.update(ar);
        }
    }
    
    /**
     * 去掉小数点如（将50.23转化为50）
     * 
     * @param decimal
     * @return
     */
    public Integer decimalToInteger(String decimal)
    {
        Integer integer = 0;
        if (decimal == null || decimal == "")
        {
            integer = 0;
        }
        else
        {
            if (decimal.indexOf(".") != -1)
            {
                integer = new Integer(decimal.substring(0, decimal.indexOf(".")));
            }
            else
            {
                integer = new Integer(decimal);
            }
        }
        return integer;
    }
    
    // 最新合单已分好数量，获取合单的数量
    public Map<String, Object> findBeMergeNotifications(ForStockObject fso)
    {
        // 返回map对象
        Map<String, Object> productInMaps = Maps.newHashMap();
        List<Notification> notificationList = Lists.newArrayList();
        
        ProduceBatchDetail pbd = fso.getProduceBatchDetail();
        if (null == pbd)
        {
            return null;
        }
        
        // 拆卡后的和单
        if (StringUtils.isNotBlank(pbd.getOldDetailId()))
        {
            pbd.setRecordId(pbd.getOldDetailId());
        }
        List<ProduceBatchDetailCraft> pbdcList = pbdCraftDao.getPbdCrafts(pbd);
        
        // 手动和单
        if (Collections3.isNotEmpty(pbdcList))
        {
            notificationList = getHeDanNotifications(pbdcList, pbd);
        }
        // 自动和单
        else
        {
            notificationList = findSameCraftNoMergeNotifications(pbd);
        }
        productInMaps.put("notifications", notificationList);
        return productInMaps;
    }
    
    // 做库存订单入库后标识完成
    public void updateContractStatus(Contract contract)
    {
        // 判断该合同的明细的入库数量是否达到要求
        if (contract.getContractDetailList() != null)
        {
            Integer quantity = 0;
            Integer alInHouseQty = 0;
            if (contract.getContractDetailList().getQuantity() != null)
            {
                quantity = Integer.parseInt(contract.getContractDetailList().getQuantity());
            }
            if (contract.getContractDetailList().getAlInHouseQty() != null)
            {
                alInHouseQty = contract.getContractDetailList().getAlInHouseQty();
            }
            if (quantity <= alInHouseQty)
            {
                contract.getContractDetailList().setStatus(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString());
                contract.getContractDetailList().setActiveFlag(TypeKey.ACTIVE.toString());
                contract.getContractDetailList().setAlDeliveryQty(alInHouseQty);
                contractDetailDao.updateContractIn(contract.getContractDetailList());
                // 判断该合同的合同明细是否都是已入库
                List<ContractDetail> list = contractDetailDao.getContractDeail(contract);
                int flag = 0;
                for (int i = 0; i < list.size(); i++)
                {
                    if (!list.get(i).getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
                    {
                        if (!list.get(i).getRecordId().equals(contract.getContractDetailList().getRecordId()))
                        {
                            flag = 1;
                        }
                    }
                }
                if (flag == 0)
                {
                    contract.setStatus(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString());
                    contractDao.updateContractStatus(contract);
                }
                this.handleFinshedConDtl(contract.getContractDetailList());
                contract.getContractDetailList().setOaQty(0);
                contractDetailDao.updateNum(contract.getContractDetailList());
            }
        }
    }
    
    /**
     * WC 2017-02-10 处理已完成合同明细通知单、流程卡、投料单
     */
    public void handleFinshedConDtl(ContractDetail conDtl)
    {
        // 查询所有的通知单，如果是合单查询合单的信息
        conDtl.setCompany(UserUtils.getUser().getCompany());
        List<Notification> notiList = notificationDao.findAllNotificationsByConDtlId(conDtl);
        if (!Collections3.isEmpty(notiList))
        {
            for (Notification slNot : notiList)
            {
                // 非当前合同明细的跳过
                if (slNot.getContractDetail() == null
                    || !slNot.getContractDetail().getRecordId().equals(conDtl.getRecordId()))
                {
                    continue;
                }
                
                // 当前通知单是否是被合单的通知单
                boolean isMerged = slNot.getMergeType().compareTo(TypeKey.SL_NOTIFICATION_BE_MERGED) == 0;
                if (isMerged)
                {
                    // 找到合单的新通知单
                    Notification mergeNot = null;
                    
                    Boolean isSend = true;
                    
                    // 找到合单的其它通知单
                    List<Notification> beMergeList = Lists.newArrayList();
                    for (Notification temp : notiList)
                    {
                        if (slNot.getMergeId().equals(temp.getRecordId()))
                        {
                            mergeNot = temp;
                            continue;
                        }
                        
                        if (slNot.getMergeId().equals(temp.getMergeId()))
                        {
                            beMergeList.add(temp);
                            
                            // 判断该合单的其它合同明细是否是已就算
                            if (temp.getContractDetail() == null || temp.getContractDetail().getStatus() == null
                                || !temp.getContractDetail()
                                    .getStatus()
                                    .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()))
                            {
                                isSend = false;
                            }
                        }
                    }
                    
                    // 若合单后的单据为空，直接更新本通知单
                    if (mergeNot == null)
                    {
                        // 查询是否存在生产，存在生产不关闭，需要判端是否存在过数记录
                        Notification qn = new Notification(slNot.getRecordId());
                        qn.setCompany(UserUtils.getUser().getCompany());
                        qn.setStatus(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString() + ConstKey.COMMA
                            + TypeKey.PRODUCE_BATCH_STATUS_PAUSE.toString() + ConstKey.COMMA
                            + TypeKey.PRODUCE_BATCH_STATUS_LOSS.toString() + ConstKey.COMMA
                            + TypeKey.PRODUCE_BATCH_STATUS_OPERATION.toString());
                        
                        Integer count = notificationDao.getBatchNoFinishCount(qn);
                        if (count == null || count <= 0)
                        {
                            // 更新合单后的状态
                            slNot.preUpdate();
                            slNot.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                            notificationDao.updateStatusById(slNot);
                        }
                    }
                    else
                    {
                        // 合单若作废，跳过处理
                        if (mergeNot.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_CANCELED.toString()))
                        {
                            continue;
                        }
                        // 若是已入库，更新所有的被合单的状态
                        else if (mergeNot.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString()))
                        {
                            // 更新被合单的状态
                            for (Notification temp : beMergeList)
                            {
                                if (temp.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString()))
                                {
                                    continue;
                                }
                                
                                temp.preUpdate();
                                temp.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                                notificationDao.updateStatusById(temp);
                            }
                        }
                        // 若是已确认，直接更新
                        else if (mergeNot.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_CONFIRMED.toString()))
                        {
                            // 合单的各个通知单对应的合同明细不是已送货，跳过处理
                            if (!isSend)
                            {
                                continue;
                            }
                            
                            // 更新被合单的状态
                            for (Notification temp : beMergeList)
                            {
                                if (temp.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString()))
                                {
                                    continue;
                                }
                                
                                temp.preUpdate();
                                temp.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                                notificationDao.updateStatusById(temp);
                            }
                            
                            // 删除未确认的工程卡
                            if (mergeNot.getCardA() != null && mergeNot.getCardA().getStatus() != null
                                && mergeNot.getCardA().getStatus().intValue() == TypeKey.EG_PROCESSCARD_STATUS_DRAFT
                                    .intValue())
                            {
                                deleteCardA(mergeNot.getCardA());
                            }
                            
                            // 更新合单后的状态
                            mergeNot.preUpdate();
                            mergeNot.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                            notificationDao.updateStatusById(mergeNot);
                        }
                        // 若是已投料或补料中
                        else
                        {
                            // 合单的各个通知单对应的合同明细不是已送货，跳过处理
                            if (!isSend)
                            {
                                continue;
                            }
                            
                            // 判断是否还有投料单未生产完成
                            Notification qn = new Notification(mergeNot.getRecordId());
                            qn.setCompany(conDtl.getCompany());
                            qn.setStatus(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString() + ConstKey.COMMA
                                + TypeKey.PRODUCE_BATCH_STATUS_PAUSE.toString() + ConstKey.COMMA
                                + TypeKey.PRODUCE_BATCH_STATUS_LOSS.toString() + ConstKey.COMMA
                                + TypeKey.PRODUCE_BATCH_STATUS_OPERATION.toString());
                            
                            // 判断过数记录
                            Integer count = notificationDao.getBatchNoFinishCount(qn);
                            if (count == null || count <= 0)
                            {
                                // 更新被合单的状态
                                for (Notification temp : beMergeList)
                                {
                                    if (temp.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString()))
                                    {
                                        continue;
                                    }
                                    
                                    temp.preUpdate();
                                    temp.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                                    notificationDao.updateStatusById(temp);
                                }
                                
                                // 删除该合单未确认的投料单
                                List<Feeding> noConfirmfeeds = feedingDao.findNoConfirmByNotification(mergeNot);
                                if (Collections3.isNotEmpty(noConfirmfeeds))
                                {
                                    for (Feeding feeding : noConfirmfeeds)
                                    {
                                        deleteFeeding(mergeNot.getCraftNo(), feeding, conDtl.getCompany());
                                    }
                                }
                                
                                // 将已投料投料单的设置取消
                                List<Feeding> confirmfeeds = feedingDao.findConfirmByNotification2(mergeNot);
                                if (Collections3.isNotEmpty(confirmfeeds))
                                {
                                    for (Feeding feeding : confirmfeeds)
                                    {
                                        cancelFeeding(feeding);
                                    }
                                }
                                
                                // 更新合单后的状态
                                mergeNot.preUpdate();
                                mergeNot.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                                notificationDao.updateStatusById(mergeNot);
                            }
                        }
                    }
                }
                else
                {
                    // 若通知单是已入库或已取消，跳过不在处理状态
                    if (slNot.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString())
                        || slNot.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_CANCELED.toString()))
                    {
                        continue;
                    }
                    // 若通知单是未确认，直接更新状态为已入库，不在处理
                    else if (slNot.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_DRAFT.toString()))
                    {
                        // 更新通知单的状态
                        slNot.preUpdate();
                        slNot.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                        notificationDao.updateStatusById(slNot);
                    }
                    // 若通知单是已确认，直接更新状态为已入库，不在处理，并且删除未确认的工程卡
                    else if (slNot.getStatus().equals(TypeKey.SL_NOTIFICATION_STATUS_CONFIRMED.toString()))
                    {
                        if (slNot.getCardA() != null && slNot.getCardA().getStatus() != null
                            && slNot.getCardA().getStatus().intValue() == TypeKey.EG_PROCESSCARD_STATUS_DRAFT
                                .intValue())
                        {
                            // 删除未确认的工程卡
                            deleteCardA(slNot.getCardA());
                        }
                        
                        // 更新通知单的状态
                        slNot.preUpdate();
                        slNot.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                        notificationDao.updateStatusById(slNot);
                    }
                    // 若通知单是已投料或补料中，判断是否还有生在生产的批次
                    else
                    {
                        // 判断是否存在生产
                        Notification qn = new Notification(slNot.getRecordId());
                        qn.setCompany(conDtl.getCompany());
                        qn.setStatus(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString() + ConstKey.COMMA
                            + TypeKey.PRODUCE_BATCH_STATUS_PAUSE.toString() + ConstKey.COMMA
                            + TypeKey.PRODUCE_BATCH_STATUS_LOSS.toString() + ConstKey.COMMA
                            + TypeKey.PRODUCE_BATCH_STATUS_OPERATION.toString());
                        Integer count = notificationDao.getBatchNoFinishCount(qn);
                        if (count == null || count <= 0)
                        {
                            // 删除该合单未确认的投料单
                            List<Feeding> noConfirmfeeds = feedingDao.findNoConfirmByNotification(slNot);
                            if (Collections3.isNotEmpty(noConfirmfeeds))
                            {
                                for (Feeding feeding : noConfirmfeeds)
                                {
                                    deleteFeeding(slNot.getCraftNo(), feeding, conDtl.getCompany());
                                }
                            }
                            
                            // 将已投料投料单的设置取消
                            List<Feeding> confirmfeeds = feedingDao.findConfirmByNotification2(slNot);
                            if (Collections3.isNotEmpty(confirmfeeds))
                            {
                                for (Feeding feeding : confirmfeeds)
                                {
                                    cancelFeeding(feeding);
                                }
                            }
                            slNot.preUpdate();
                            slNot.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
                            notificationDao.updateStatusById(slNot);
                        }
                    }
                }
            }
        }
    }
    
    @Transactional(readOnly = false)
    private Boolean deleteCardA(CardA cardA)
    {
        CardA cardAll = cardADao.getById(cardA);
        
        // 已经删除不在处理
        if (cardAll == null || cardAll.getActiveFlag().equals(CardA.DEL_FLAG_DELETE))
        {
            return true;
        }
        
        // 判断是否更新过通知单，若已经更新过，不可删除
        Notification noti = new Notification();
        noti.setCompany(cardA.getCompany());
        noti.setCardA(cardA);
        if (notificationDao.selectCountByCardA(noti) > 0)
        {
            return false;
        }
        
        // 删除工序的工艺与值记录
        for (CardAProcessValue processValue : cardAll.getProcessValueList())
        {
            processValue.setCompany(cardA.getCompany());
            processValueDao.deleteCardAProcessCraftValue(processValue);
        }
        
        // 删除工序记录
        cardADao.deleteCardAProcessValue(cardAll);
        
        // 删除钻孔信息
        cardBDao.deleteCardBDrillInfo(cardAll.getCardB());
        
        // 删除附件记录
        cardBDao.deleteCardBAttach(cardAll.getCardB());
        
        // 删除开料信息A
        boardPartDao.delete(cardAll.getCardB().getBoardCutting().getPartA());
        
        // 删除开料新系B
        boardPartDao.delete(cardAll.getCardB().getBoardCutting().getPartB());
        
        // 删除开料新系
        boardCuttingDao.delete(cardAll.getCardB().getBoardCutting());
        
        // 删除流程卡B
        cardBDao.delete(cardAll.getCardB());
        
        // 删除多工序的工艺
        boardPartCraftDao.deleteByCard(cardAll);
        
        // 复位通知单
        String[] notificationNos = cardAll.getNoticeNos().split(",");
        CardA cardDel = new CardA();
        cardDel.setRecordId(null);
        
        // 已经确认并且有合单
        boolean hadUpdateMarge = StringUtils.isNotBlank(cardAll.getCancelCause()) && notificationNos.length > 1;
        for (String no : notificationNos)
        {
            Notification notification = new Notification();
            notification.setCompany(cardA.getCompany());
            notification.setNo(no);
            notification.setCardA(cardDel);
            
            // 更新合单状态
            if (hadUpdateMarge)
            {
                notification.setMergeId(null);
                notification.setMergeType(TypeKey.SL_NOTIFICATION_MERGE_NORMAL);
                notificationDao.updateCardAAndMergeTypeByNo(notification);
            }
            else
            {
                notificationDao.updateCardAByNo(notification);
            }
        }
        
        // 删除合单
        if (hadUpdateMarge)
        {
            Notification notification = new Notification();
            notification.setCompany(cardA.getCompany());
            notification.setNo(cardAll.getNoticeNos());
            notificationDao.deleteMerge(notification);
        }
        
        // 删除流程卡A
        cardADao.delete(cardA);
        
        return true;
    }
    
    @Transactional(readOnly = false)
    private void deleteFeeding(String craftNo, Feeding pdFeeding, Company company)
    {
        // 根据投料单编号 去删除批次信息
        ProduceBatch date = produceBatchDao.getByFeefingNo(pdFeeding.getRecordId());
        
        // 根据批次编号再去删除批次明细
        produceBatchDao.delete(date);
        produceBatchDetailDao.deleteByProduceBatchRecordId(date.getRecordId());
        
        // 删除统计数据
        // 减去投料添加的统计信息
        CraftNoDiscard craftNoDiscard = new CraftNoDiscard();
        craftNoDiscard.setCraftNo(craftNo);
        craftNoDiscard.setCompany(company);
        
        // 减去预投率中的总投料数
        craftNoDiscard = craftNoDiscardDao.selectCount(craftNoDiscard);
        if (null != craftNoDiscard)
        {
            Integer sum = Integer.parseInt(craftNoDiscard.getProductionTotal()) - pdFeeding.getPcsQuantity().intValue();
            craftNoDiscard.setProductionTotal(sum.toString());
            craftNoDiscard.preUpdate();
            craftNoDiscardDao.update(craftNoDiscard);
        }
    }
    
    /**
     * 取消投料单
     */
    public void cancelFeeding(Feeding feeding)
    {
        // 更新投料单状态
        feeding.setStatus(TypeKey.PD_FEEDING_STATUS_CANCELED.toString());
        feedingDao.updateStatusById(feeding);
        
        // 根据投料去查询批次信息
        ProduceBatch pro = produceBatchDao.getByFeefingNo(feeding.getRecordId());
        pro.setStatus(TypeKey.PRODUCE_BATCH_STATUS_CANCELED.toString());
        produceBatchDao.updateStatus(pro);
        
        // 根据批次去修改所有本批次下面的批次明细
        produceBatchDetailDao.updateStatusByProduceBatch(pro);
        
        // 要把通知单 中的已投数量减去~ 再把通知单的状态改变成 已确认
        Notification noti = notificationDao.get(feeding.getNotification());
        String temp = feeding.getPcsQuantity().toString();
        noti.setFeedPcsQty(noti.getFeedPcsQty() - Integer.valueOf(temp)); // 设置为 之前通知单的投料数量 减去 本次投料单的投料数量
        noti.setStatus(null);
        notificationDao.updateFeedPcsQtyAndStatus(noti);
        
        // 查看工程状态
        CardA cardA = new CardA();
        cardA.setNoticeNos(noti.getNo());
        cardA.setCompany(feeding.getCompany());
        cardA = cardADao.getProcessNotification(cardA);
        
        // 若工程卡的状态为已确认，就更新通知单的是否可以投料为可以
        if (cardA.getStatus().intValue() == TypeKey.EG_PROCESSCARD_STATUS_CONFIRMED.intValue()
            && noti.getIsFeeding().intValue() != TypeKey.YES.intValue())
        {
            noti.setIsFeeding(TypeKey.YES);
            noti.setCompany(feeding.getCompany());
            notificationDao.updateIsFeedingDate(noti);
        }
    }
    
    // public void sendMsg(Notification notification, String msgType, String deail, ProductStore product)
    // {
    // String contractNo = "";
    // String name = "";
    // String no = "";
    // String material = "";
    // notification.setCompany(UserUtils.getUser().getCompany());
    // notification = notifiDao.getMsgByNo(notification);
    // ParentMessage parentMessage = new ParentMessage();
    // if (notification != null)
    // {
    // if (notification != null)
    // {
    // if (StringUtils.isNotBlank(notification.getNo()))
    // {
    // no = "的通知单" + notification.getNo();
    // }
    // if (notification.getContract() != null)
    // {
    // if (StringUtils.isNotBlank(notification.getContract().getNo()))
    // {
    // contractNo = "的合同" + notification.getContract().getNo();
    // }
    // if (notification.getContract().getCustomer() != null
    // && StringUtils.isNotBlank(notification.getContract().getCustomer().getName()))
    // {
    // name = "客户" + notification.getContract().getCustomer().getName();
    // }
    // if (notification.getContract().getUser() != null
    // && StringUtils.isNotBlank(notification.getContract().getUser().getRecordId()))
    // {
    // parentMessage.setUser(notification.getContract().getUser());
    // }
    // }
    // }
    // }
    // if (product != null)
    // {
    // if (product.getMaterial() != null && StringUtils.isNotBlank(product.getMaterial().getNo()))
    // {
    // material = deail + "库了成品" + product.getMaterial().getNo() + "共" + product.getQuantity() + "个！";
    // }
    // else
    // {
    // material = deail + "库了成品共" + product.getQuantity() + "个！";
    // }
    // }
    // parentMessage.setCreateByName(UserUtils.getUser());
    // parentMessage.setMessaeDeail(name + contractNo + no + material);
    // parentMessage.setMessageModuleCopy(msgType);
    // MessageUtil.sendMessage(parentMessage);
    // }
    
    // zjn 2017-09-14 返回成品的物料类型
    public DictValue returnMaterialType()
    {
        DictValue materialType = new DictValue();
        List<DictValue> dictValueList = DictUtils.getValuesByItem(DictItemEnum.MATERIAL_TYPE_STORE);
        for (DictValue dv : dictValueList)
        {
            if (StringUtils.isNotBlank(dv.getValue()) && dv.getValue().equals("成品"))
            {
                materialType = dv;
            }
        }
        return materialType;
    }
    
    /** zjn 2019-05-26 获取和单后的通知单 */
    public List<Notification> getHeDanNotifications(List<ProduceBatchDetailCraft> pbdcList, ProduceBatchDetail pbd)
    {
        List<Notification> notificationList = Lists.newArrayList();
        ProductStore ps = new ProductStore();
        if (Collections3.isNotEmpty(pbdcList))
        {
            for (ProduceBatchDetailCraft pbdc : pbdcList)
            {
                // 若已入库，跳过本工艺
                if (pbdc.getPutStorage().intValue() == TypeKey.YES.intValue())
                {
                    continue;
                }
                
                Integer pcsQty = pbdc.getQtyPcsT() == null ? 0 : pbdc.getQtyPcsT();
                Integer discardQty = pbdc.getDiscardQty() == null ? 0 : pbdc.getDiscardQty();
                Integer storageQuantity = pbdc.getStorageQuantity() == null ? 0 : pbdc.getStorageQuantity();// 已入库数量
                
                ps.setCompany(pbdc.getCompany());
                if (null != pbdc.getProduceBatch() && null != pbdc.getProduceBatch().getNotification()
                    && StringUtils.isNotBlank(pbdc.getProduceBatch().getNotification().getRecordId()))
                {
                    ps.setNotificationId(pbdc.getProduceBatch().getNotification().getRecordId());
                }
                ps.setProduceBatchDetail(pbd);
                ps.setProduceBatchDetailCraft(pbdc);
                ps.setMaterial(pbdc.getMaterial());
                
                // 获取已入库数量
                Integer completedQty = pstoreDao.quaryStockedAndxboardByNoti(ps);
                
                if (null == completedQty)
                {
                    completedQty = 0;
                }
                
                Integer readyQty = pcsQty - discardQty - completedQty - storageQuantity;
                
                Notification notification = new Notification();
                if (readyQty.compareTo(0) > 0)
                {
                    notification.setCraftNo(pbdc.getCraftNo());
                    notification.setReadyQty(readyQty);
                    notification.setMaterial(pbdc.getMaterial());
                    notification.setNo(pbdc.getNotificationNo());
                    notification.setProduceBatchDetail(pbd);
                    
                    // Add:lpjliu 2016-06-24 用于临时存储明细工艺的id
                    notification.setCustomerModel(pbdc.getRecordId());
                    
                    // 不同生产编号合单，分别判断每一个通知单对应合同明细是否寄存PCS
                    ContractDetail detail = notifiDao.getConDetailDepositByProBatDetailCraftId(pbdc.getRecordId());
                    if (detail != null)
                    {
                        notification.setContractDetail(detail);
                    }
                    notificationList.add(notification);
                }
            }
        }
        return notificationList;
    }
    
    /** zjn 2019-05-30 查询自动合单的待入库信息 */
    public List<Notification> findSameCraftNoMergeNotifications(ProduceBatchDetail pbd)
    {
        List<Notification> list = Lists.newArrayList();
        Notification notifi = pbd.getNotification();
        if (notifi == null)
        {
            return null;
        }
        
        // 查询当前需要入库的汇总信息，过滤掉已入库的通知单
        Notification notifiSreach = new Notification();
        notifiSreach.setNo(notifi.getNo());
        notifiSreach.setCompany(UserUtils.getUser().getCompany());
        notifiSreach.setMergeId(notifi.getRecordId());
        notifiSreach.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
        Notification totalInfo = notifiDao.getTotalInfoOfSameCraftMerge(notifiSreach);
        if (totalInfo == null)
        {
            return null;
        }
        
        ProductStore ps = new ProductStore();
        ps.setCompany(pbd.getCompany());
        if (null != pbd.getNotification() && StringUtils.isNotBlank(pbd.getNotification().getRecordId()))
        {
            ps.setNotificationId(pbd.getNotification().getRecordId());
        }
        ps.setProduceBatchDetail(pbd);
        
        // 获取已入库数量
        Integer completedQty = pstoreDao.quaryStockedAndxboardByNoti(ps);
        
        if (null == completedQty)
        {
            completedQty = 0;
        }
        Integer producePcsQty = pbd.getQtyPcsT() == null ? 0 : pbd.getQtyPcsT();
        Integer failedPcsQty = pbd.getDiscardQty() == null ? 0 : Integer.valueOf(pbd.getDiscardQty());
        Integer budgetQty = producePcsQty - failedPcsQty - completedQty;
        if (budgetQty > 0)
        {
            totalInfo.setReadyQty(budgetQty);
            totalInfo.setProduceBatchDetail(pbd);
            
            // WC 2017-01-19 将筛选后的通知单对应合同明细加载进来
            totalInfo.setContractDetail(notifiDao.getConDetailDepositByNotifiNo(totalInfo.getRecordId()));
            list.add(totalInfo);
        }
        return list;
    }
    
    /** zjn 2019-12-25 获取多个合同通知单信息 */
    public Map<String, Object> getBeMergeNotifications(List<ForStockObject> fsoList)
    {
        // 返回map对象
        Map<String, Object> data = Maps.newHashMap();
        List<Notification> notificationList = Lists.newArrayList();
        List<Notification> notfList = Lists.newArrayList();
        for (ForStockObject fso : fsoList)
        {
            ProduceBatchDetail pbd = fso.getProduceBatchDetail();
            if (null == pbd)
            {
                return null;
            }
            // 拆卡后的和单
            if (StringUtils.isNotBlank(pbd.getOldDetailId()))
            {
                pbd.setRecordId(pbd.getOldDetailId());
            }
            List<ProduceBatchDetailCraft> pbdcList = pbdCraftDao.getPbdCrafts(pbd);
            
            // 手动和单
            if (Collections3.isNotEmpty(pbdcList))
            {
                notfList = getHeDanNotifications(pbdcList, pbd);
                if (Collections3.isNotEmpty(notfList))
                {
                    notificationList.addAll(notfList);
                }
            }
            // 自动和单
            else
            {
                notfList = findSameCraftNoMergeNotifications(pbd);
                if (Collections3.isNotEmpty(notfList))
                {
                    notificationList.addAll(notfList);
                }
            }
        }
        data.put("notifications", notificationList);
        return data;
    }
    
    /** zjn 2019-12-25 zjn 2019-01-26 一键送货出库/生产入库操作 */
    @Transactional(readOnly = false)
    public String productAddList(List<ProductStore> productList)
    {
        Company company = UserUtils.getUser().getCompany();
        Date now = new Date();
        Integer completedQty = 0;
        ProductStore mergedProductStore = new ProductStore();
        mergedProductStore.setCompany(company);
        mergedProductStore.setActiveFlag(ProductStore.DEL_FLAG_NORMAL);
        // 不查出冲红单数据
        mergedProductStore.setStatus(TypeKey.BILL_STATUS_FOR_WRITEOFF);
        
        if (Collections3.isNotEmpty(productList))
        {
            for (ProductStore product : productList)
            {
                mergedProductStore.setProduceBatchDetail(product.getProduceBatchDetail());
                mergedProductStore.setNotificationId(product.getNotificationId());
                // 根据公司,通知单,批次明细查询正常入库和叉板入库总数量
                completedQty = pstoreDao.quaryStockedAndxboardByPbd(mergedProductStore);
                product.setUnavailCount(null == completedQty ? 0 : completedQty);
                product.setCompany(company);
                product.setOperateDate(now);
                product.setMaterial(materialDao.get(product.getMaterial()));
                product.getProduceBatchDetail()
                    .setNotification(notificationDao.get(product.getProduceBatchDetail().getNotification()));
                int result = productAdd(product);
                if (result != 0)
                {
                    if (result == 3)
                    {
                        return "成品'" + product.getMaterial().getNo() + "'出库数量超过了库存数量,请输入正确的出库数量.";
                    }
                    else if (result == 9)
                    {
                        return "成品'" + product.getMaterial().getNo() + "'已经完成入库,不能重复入库.";
                    }
                    else if (result == 10)
                    {
                        return "成品'" + product.getMaterial().getNo() + "'数据不完整,未找到对应的通知单信息,请您确认.";
                    }
                    else if (result == 11)
                    {
                        return "成品'" + product.getMaterial().getNo() + "'有未审核的品质检测内容,请您找相关责任人处理.";
                    }
                    else if (result == 12)
                    {
                        return "成品'" + product.getMaterial().getNo() + "'已经完成送货出库,不能重复出库.";
                    }
                    else
                    {
                        return "亲,系统工作开了小差,请联系系统管理员.";
                    }
                }
            }
        }
        return "success";
    }
}
