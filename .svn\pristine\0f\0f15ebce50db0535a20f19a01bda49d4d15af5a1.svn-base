<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="d-flex flex-column flex-root">
    <div class="d-flex flex-row flex-column-fluid page">
        <div class="d-flex flex-column flex-row-fluid wrapper">
            <div class="content d-flex flex-column flex-column-fluid"
                 style="background-image: url(${pageContext.request.contextPath}/assets/global/metronic/assets/media/bg/bgMain.jpg);height: 180px;">
                <div class="subheader subheader-transparent" id="kt_subheader">
                    <div class="container d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <h5 class="text-white font-weight-bold my-2 mr-5">订单管理</h5>
                            </div>
                        </div>
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <router-link to="/salesSystem" class="btn btn-transparent-warning font-weight-bold mr-2">
                                    返回主页
                                </router-link>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-column-fluid">
                    <div class="container">
                        <div class="row">
                            <div class="col-12">
                                <div class="card card-custom gutter-b">
                                    <div class="card-body p-0 d-flex flex-column">
                                        <div class="flex-grow-1" style="padding: 0 2.25rem !important;">
                                            <div class="row pt-3 pb-3 border-bottom">
                                                <div class="col-12">
                                                    <div class="input-icon">
                                                        <input type="text" class="form-control" v-model="serchMsg" placeholder="请输入要搜索的客户" v-on:click="queryList"/>
                                                        <span><i class="flaticon2-search-1 icon-md"></i></span>
                                                    </div>
                                                    <div class="row pt-2 pb-1 alert-light align-items-center">
                                                        <div class="col-12">开始时间</div>
                                                        <div class="col-12">
                                                            <input id="startTime" class="form-control">
                                                        </div>
                                                    </div>
                                                    <div class="row pt-2 pb-2 alert-light align-items-center">
                                                        <div class="col-12">结束时间</div>
                                                        <div class="col-12">
                                                            <input id="endTime" class="form-control">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row row-paddingless pt-3 pb-3 border-bottom" v-for="item in orderBiddingList" :key="item.recordId" v-if="orderBiddingList && orderBiddingList.length > 0">
                                                <div class="col">
                                                    <div class="row pt-3">
                                                        <div class="col">
                                                            <div class="d-flex align-items-center mr-2">
                                                                <div class="symbol symbol-success pr-3" v-if="item.customer.logo">
                                                                    <img alt="Pic" :src="item.customer.logoStr"/>
                                                                </div>
                                                                <div class="symbol symbol-45 symbol-light-danger mr-4 flex-shrink-0" v-else>
                                                                    <div class="symbol-label">
                                                                        logo
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <div class="font-size-h5 text-dark-75 font-weight-bolder">
                                                                        {{item.customerName}}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row pt-3">
                                                        <div class="col-8">
                                                            {{item.supplierName}}
                                                        </div>
                                                        <div class="col text-right">
                                                            {{item.materialNum}}
                                                        </div>
                                                    </div>
                                                    <div class="row pt-1">
                                                        <div class="col-12">
                                                            {{item.materialName}}&emsp;{{item.materialCraft}}&emsp;交期：{{item.deliveryDateStr}}
                                                        </div>
                                                    </div>
                                                    <div class="row pt-1 pb-3 align-items-center">
                                                        <div class="col">
                                                            <span class="label label-inline label-success">交易中</span>
                                                        </div>
                                                        <div class="col-9 text-right">
                                                            <button class="btn btn-sm btn-light-primary" v-on:click="biddingModal(item)">
                                                                详情
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="biddingModal" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="biddingModal" aria-hidden="true" style="overflow:auto!important;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">订单详情</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i aria-hidden="true" class="ki ki-close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col">
                            <div class="row text-muted font-weight-bolder border-bottom pb-3">
                                <div class="col">
                                    <div class="row pb-1">
                                        <div class="col">
                                            客户：{{bidding.customerName}}
                                        </div>
                                    </div>
                                    <div class="row pb-1">
                                        <div class="col">
                                            供应商：{{bidding.supplierName}}
                                        </div>
                                    </div>
                                    <div class="row pb-1">
                                        <div class="col">
                                            {{bidding.materialName}}&emsp;{{bidding.materialCraft}}&emsp;招标数量：{{bidding.materialNum}}
                                        </div>
                                    </div>
                                    <div class="row pb-1">
                                        <div class="col">
                                            到货日期：{{bidding.deliveryDateStr}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row font-weight-bolder border-bottom pb-3">
                                <div class="col">
                                    <div class="row p-1">
                                        <div class="col">
                                            单价：
                                            <input class="form-control" v-model="bidding.price" disabled>
                                        </div>
                                        <div class="col">
                                            运费：
                                            <input class="form-control" v-model="bidding.freight" disabled>
                                        </div>
                                    </div>
                                    <div class="row p-1">
                                        <div class="col">
                                            结款方式：
                                            <select class="form-control" v-model="bidding.payWay" disabled>
                                                <option v-for="item in payWayList" :key="item.recordId" :value="item.recordId">
                                                    {{item.value}}
                                                </option>
                                            </select>
                                        </div>
                                        <div class="col">
                                            含税说明：
                                            <select class="form-control" v-model="bidding.taxDescript" disabled>
                                                <option v-for="item in taxDescriptList" :key="item.recordId" :value="item.recordId">
                                                    {{item.value}}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row p-1">
                                        <div class="col">
                                            货币类型：
                                            <select class="form-control" v-model="bidding.currencyType" disabled>
                                                <option v-for="item in currencyTypeList" :key="item.recordId" :value="item.recordId">
                                                    {{item.value}}
                                                </option>
                                            </select>
                                        </div>
                                        <div class="col">
                                            付款方式：
                                            <select class="form-control" v-model="bidding.paycause" disabled>
                                                <option v-for="item in paycauseList" :key="item.recordId" :value="item.recordId">
                                                    {{item.value}}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row pt-1 pb-3">
                                        <div class="col">
                                            产品库存：
                                            <input class="form-control" v-model="bidding.icloudMaterialStocks" disabled>
                                        </div>
                                        <div class="col">
                                            使用库存：
                                            <input class="form-control" v-model="bidding.useStocks" disabled>
                                        </div>
                                    </div>
                                    <div class="row pt-3 pb-3 pl-1 pr-1 border">
                                        <div class="col-12">
                                            <div class="row pb-1">
                                                <div class="col">具体交期：</div>
                                            </div>
                                            <div class="row pt-3 border-top" v-for="(item,index) in deliveryList" :key="index">
                                                <div class="col-6">
                                                    数量：
                                                    <input class="form-control" v-model="item.quantity" disabled>
                                                </div>
                                                <div class="col-6">
                                                    交期：
                                                    <select class="form-control" v-model="item.deliveryDay" disabled>
                                                        <option v-for="item in deliveryDayList" :key="item.recordId" :value="item.recordId">
                                                            {{item.value}}
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
</div>