/**
 * 
 */
package com.kyb.pcberp.modules.production.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

/**
 * 生产型号报废表Entity
 * <AUTHOR>
 * @version 2016-03-02
 */
public class CraftNoDiscard extends DataEntity<CraftNoDiscard> {
	
	private static final long serialVersionUID = 1L;
	
	private String craftNo;		// 生产编号
	private String productionTotal;		// 生产总数
	private Integer scrapTotal;		// 报废总数
	
	public CraftNoDiscard() {
		super();
	}

	public CraftNoDiscard(String id){
		super(id);
	}


	public String getCraftNo() {
		return craftNo;
	}

	public void setCraftNo(String craftNo) {
		this.craftNo = craftNo;
	}
	
	public String getProductionTotal() {
		return productionTotal;
	}

	public void setProductionTotal(String productionTotal) {
		this.productionTotal = productionTotal;
	}

    public Integer getScrapTotal()
    {
        return scrapTotal;
    }

    public void setScrapTotal(Integer scrapTotal)
    {
        this.scrapTotal = scrapTotal;
    }
	

	
}