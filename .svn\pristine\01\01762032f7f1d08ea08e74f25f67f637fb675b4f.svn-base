package com.kyb.pcberp.modules.purch.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.approval.vo.ContractDeailVo;
import com.kyb.pcberp.modules.approval.vo.ContractVo;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetailClose;

import java.util.List;

@MyBatisDao
public interface PurchasingDetailCloseDao extends CrudDao<PurchasingDetailClose>
{
    List<ContractDeailVo> getPurchasingByDetailId(ContractVo contractVo);
}
