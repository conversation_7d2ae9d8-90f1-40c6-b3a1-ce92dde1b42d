package com.kyb.pcberp.modules.stock.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.persistence.DataEntity;

import java.util.Date;

public class MantissaStore extends DataEntity<MantissaStore>
{
    private static final long serialVersionUID = 1L;

    private String materialUseId;

    private Integer quantity;

    private Date operateDate;

    private String status;

    public String getMaterialUseId() {
        return materialUseId;
    }

    public void setMaterialUseId(String materialUseId) {
        this.materialUseId = materialUseId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
