package com.kyb.pcberp.modules.stock.service;

import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.RawStockUtil;
import com.kyb.pcberp.modules.approval.utils.ModifyUtils;
import com.kyb.pcberp.modules.stock.dao.InitMaterialDao;
import com.kyb.pcberp.modules.stock.dao.MaterialPriceDao;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.MaterialPrice;
import com.kyb.pcberp.modules.stock.entity.MaterialPriceInit;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Transactional(readOnly = true)
public class MaterialPriceService extends CrudService<MaterialPriceDao, MaterialPrice>
{
    @Autowired
    private MaterialPriceDao materialPriceDao;

    @Autowired
    private InitMaterialDao initMaterialDao;

    public Page<MaterialPrice> findMaterialPricePage(Page<MaterialPrice> qpage, MaterialPrice price)
    {
        price.setPage(qpage);
        List<MaterialPrice> prices = materialPriceDao.findMaterialPricePage(price);
        qpage.setList(prices);
        return qpage;
    }

    public List<MaterialPrice> findMaterialPriceList(MaterialPrice rawMaterial)
    {
        return materialPriceDao.findMaterialPricePage(rawMaterial);
    }

    public Page<MaterialPrice> adjustPricePage(Page<MaterialPrice> qpage, MaterialPrice price)
    {

        price.setPage(qpage);
        List<MaterialPrice> prices = materialPriceDao.adjustPricePage(price);
        qpage.setList(prices);
        return qpage;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> saveAdjust(MaterialPrice price)
    {
        Map<String, Object> result = new HashMap<String, Object>();
        if (price.getIsNewRecord())
        {
            price.preInsert();
            materialPriceDao.insertAdjust(price);
        }
        else
        {
            price.preUpdate();
            materialPriceDao.updateAdjust(price);
        }
        result.put("result", "success");
        result.put("obj", price);
        return result;
    }

    @Transactional(readOnly = false)
    public List<MaterialPrice> dataHandle(List<List<Object>> listob, String recordId)
    {
        List<MaterialPrice> redList = Collections.emptyList();
        Company company = UserUtils.getUser().getCompany();
        List<MaterialPrice> allPriceInfo =
            materialPriceDao.getAllPriceInfo(UserUtils.getUser().getCompany().getRecordId());
        // 查出所有的盘点明细，物料no， recordId,companyId
        /*
         * List<MaterialPrice> priceDetail = materialPriceDao.findPriceDetailList(recordId);
         */
        List<MaterialPrice> insertList = new ArrayList<MaterialPrice>();
        // 循环明细，对比更新录入盘点的数量，放入到更新的list
        Map<String, BigDecimal> inData = new HashMap<String, BigDecimal>();
        BigDecimal price = null;
        if (Collections3.isNotEmpty(listob))
        {
            String materialNoCellTitle = "物料编号";
            String priceCellTitle = "价格";
            Integer materialNoCellNum = null;
            Integer priceCellNum = null;
            Integer maxCellNum = 0;
            for (int i = 0; i < listob.size(); i++)
            {
                List<Object> lo = listob.get(i);
                // 先读取物料编号和实盘库存所在的列
                if (null == materialNoCellNum || null == priceCellNum)
                {
                    for (int n = 0; n < lo.size(); n++)
                    {
                        String content = lo.get(n) == null ? "" : lo.get(n).toString();
                        if (null == materialNoCellNum && materialNoCellTitle.equals(content.trim()))
                        {
                            materialNoCellNum = n;
                            maxCellNum = maxCellNum > n ? maxCellNum : n;
                        }
                        if (null == priceCellNum && priceCellTitle.equals(content.trim()))
                        {
                            priceCellNum = n;
                            maxCellNum = maxCellNum > n ? maxCellNum : n;
                        }
                    }
                }
                else
                {
                    if (lo.size() > maxCellNum && !StringUtils.isEmpty(lo.get(materialNoCellNum))
                        && !StringUtils.isEmpty(lo.get(priceCellNum)))
                    {
                        String materialNo = lo.get(materialNoCellNum).toString();

                        if (null != priceCellNum && !StringUtils.isEmpty(lo.get(priceCellNum).toString().trim()))
                        {
                            price = new BigDecimal(lo.get(priceCellNum).toString().trim());
                            inData.put(materialNo, price);
                        }
                    }
                }
            }
            if (inData.size() == 0)
            {
                return redList;
            }
            if (inData.size() > 0)
            {
                for (String mNo : inData.keySet())
                {
                    for (MaterialPrice mr : allPriceInfo)
                    {
                        // YLBM00000004 12位 前缀YLBM
                        if (mr.getMaterialNo().equals(getMaterialNo(12, "YLBM", mNo, "0")) || mr.getMaterialNo()
                            .trim()
                            .equals(mNo.trim()))
                        {
                            // YLBM原材料开头
                            mr.setAfterPrice(new BigDecimal(inData.get(mNo).toString().trim()));
                            mr.setAdjustId(recordId);
                            mr.setCompany(company);
                            mr.preInsert();
                            // 开始单价直接查出来、物料要全
                            insertList.add(mr);
                            break;
                        }
                    }
                }
            }
        }

        // 批量更新盘点数量
        if (insertList.size() == 0)
        {
            return redList;
        }
        if (insertList.size() > 0)
        {

            materialPriceDao.updateDetailToHistory(recordId);
            materialPriceDao.batchInsertList(insertList);
        }

        return redList;

    }

    /**
     * @param length 长度
     * @param prex   前缀
     * @param endx   后缀
     * @param addStr 补位
     * @return
     */
    public String getMaterialNo(int length, String prex, String endx, String addStr)
    {
        String result = endx;
        if (!endx.startsWith("YLBM"))
        {
            int num = length - prex.length() - endx.length();
            if (num > 0)
            {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < num; i++)
                {
                    sb.append(addStr);
                }
                result = prex + sb.toString() + result;
            }
            else
            {
                result = prex + result;
            }
        }
        return result;
    }

    public Page<MaterialPrice> adjustPriceDetailPage(Page<MaterialPrice> qpage, MaterialPrice price)
    {
        price.setPage(qpage);
        if (!StringUtils.isEmpty(price.getAdjustId()))
        {
            List<MaterialPrice> prices = materialPriceDao.adjustPriceDetailPage(price);
            qpage.setList(prices);
        }
        else
        {
            qpage.setList(Collections.emptyList());
        }
        return qpage;
    }

    public List<MaterialPrice> getAdjustPriceDetailList(MaterialPrice rawMaterial)
    {
        return materialPriceDao.adjustPriceDetailPage(rawMaterial);
    }

    @Transactional(readOnly = false)
    public Map<String, Object> updateAdjust(MaterialPrice price, int type)
    {
        Map<String, Object> result = new HashMap<String, Object>();
        User user = UserUtils.getUser();
        price.setCompany(user.getCompany());
        MaterialPrice oldPrice = materialPriceDao.getAdjust(price);
        if (type - Integer.parseInt(oldPrice.getStatus()) != 1)
        {
            result.put("result", "fail");
            result.put("message", "单价状态异常，请刷新");
            return result;
        }
        price.setStatus(type + "");
        price.preUpdate();
        if (type == 3)
        {
            String resultStr = ModifyUtils.MaterialPlaceApproval(price, price.getCompany(), user);
            if ("fail".equals(resultStr))
            {
                result.put("result", "fail");
                result.put("message", "请配置期初单价审批!");
                return result;
            }
            else
            {
                result.put("result", "success");
                result.put("message", resultStr);
                return result;
            }
        }
        else if (type == 4)
        {
            resetNewInitPrices(price);

        }
        materialPriceDao.updateAdjustStatus(price);

        result.put("result", "success");
        switch (type)
        {
            case 2:
                result.put("message", "确认成功!");
                break;
            case 3:
                result.put("message", "审核成功!");
                break;
            case 4:
                result.put("message", "完成初始化成功!");
                break;
        }
        return result;
    }

    /**
     * 1、将大于等于2022-01-01号的 价格滚存全部重置为历史状态; 2、插入2022-01-01期初单价; 3、对重置为历史状态的日期(排除2022-01-01期初)，生成新的加权平均价；不能少，也不能多;
     *
     * @param price 默认重置从2022-01-01开始的价格
     */
    private void resetNewInitPrices(MaterialPrice price)
    {
        // 查询重置历史状态的日期组
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        Date initDate = null;
        try
        {
            initDate = sf.parse("2022-01-01");
        }
        catch (ParseException e)
        {
            e.printStackTrace();
        }
        List<Date> dates = materialPriceDao.findInitDateList(initDate);
        // 重置为历史数据
        price.setOldInitDate(initDate);
        materialPriceDao.setInitPriceToHistoryByNew(price);
        // 跟进当前价格生成期初记录
        price.setAdjustId(price.getRecordId());
        List<MaterialPrice> detailList = materialPriceDao.adjustPriceDetailPage(price);
        List<MaterialPriceInit> insertInitList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(detailList))
        {
            MaterialPriceInit mpi = null;
            String companyId = UserUtils.getUser().getCompany().getRecordId();
            for (MaterialPrice pr : detailList)
            {
                mpi = new MaterialPriceInit();
                mpi.setCompanyId(companyId);
                mpi.setMaterialId(pr.getMaterialId());
                mpi.setInitDate(initDate);
                mpi.setPrice(pr.getAfterPrice());
                // TODO moneyRadio
                insertInitList.add(mpi);
            }
            if (!CollectionUtils.isEmpty(dates))
            {
                // 大于期初的所有的采购入库记录
                List<RawmaterialStock> purInoutList = materialPriceDao.getRawmaterialPriceList(price);
                // 大于等于开始时间，小于结束时间 之间的采购记录，有就要重算
                Date startDate = initDate;
                List<RawmaterialStock> currStock = new ArrayList<>();
                for (Date endDate : dates)
                {
                    for (RawmaterialStock rs : purInoutList)
                    {
                        if (startDate.compareTo(rs.getOperateDate()) <= 0 && endDate.compareTo(rs.getOperateDate()) > 0)
                        {
                            currStock.add(rs);
                        }
                    }
                    cumulativePriceToInit(detailList, currStock, startDate, endDate, insertInitList);
                    currStock.clear();
                    startDate = endDate;
                }

            }
            if (!CollectionUtils.isEmpty(insertInitList))
            {
                initMaterialDao.batchResetMaterialPrice(insertInitList);
            }
        }

    }

    private void cumulativePriceToInit(List<MaterialPrice> detailList, List<RawmaterialStock> currStock, Date startDate,
        Date endDate, List<MaterialPriceInit> insertInitList)
    {
        BigDecimal price = null;
        Material queryMaterial = new Material();
        MaterialPriceInit mpi = null;
        RawStockUtil rowUtil = new RawStockUtil();
        Company company = detailList.get(0).getCompany();
        for (MaterialPrice mp : detailList)
        {
            price = mp.getAfterPrice() == null ? BigDecimal.ZERO : mp.getAfterPrice();
            queryMaterial.setRecordId(mp.getMaterialId());
            queryMaterial.setCompanyId(mp.getCompany().getRecordId());
            for (RawmaterialStock rs : currStock)
            {
                if (mp.getMaterialId().equals(rs.getMaterialId()))
                {
                    // 进行计算加权平均单价，移动加权平均单价计算方法：（（上次加权平均价*剩余数量）+（本次采购单价*本次采购数量））/总数量
                    if (rs.getQuantity() != null && rs.getQuantity().compareTo(BigDecimal.ZERO) > 0)
                    {

                        // 总数量
                        BigDecimal num = BigDecimal.ZERO;
                        // 总金额
                        BigDecimal money = BigDecimal.ZERO;
                        // 本次采购成本
                        money = rs.getCost();
                        num = num.add(rs.getQuantity());
                        // 获取截止到该采购时间该物料的剩余数量
                        BigDecimal stockNum = rowUtil.getMaterialStock(queryMaterial, rs.getOperateDate(), company);
                        // 减去本次采购入库数量
                        stockNum = stockNum.subtract(num);
                        // 进行本次采购后加权平均单价计算
                        if (price.compareTo(BigDecimal.ZERO) > 0 && stockNum.compareTo(BigDecimal.ZERO) > 0)
                        {
                            money = money.add(price.multiply(stockNum));
                            num = num.add(stockNum);
                        }
                        price = money.divide(num, 4, BigDecimal.ROUND_HALF_UP);
                    }
                }
            }
            mpi = new MaterialPriceInit();
            mpi.setCompanyId(company.getRecordId());
            mpi.setMaterialId(mp.getMaterialId());
            mpi.setInitDate(endDate);
            mpi.setPrice(price);
            insertInitList.add(mpi);
            mp.setAfterPrice(price);
        }

    }

    @Transactional(readOnly = false)
    public Map<String, Object> canceConfirmAdjust(MaterialPrice price, int type)
    {
        Map<String, Object> result = new HashMap<String, Object>();
        User user = UserUtils.getUser();
        price.setCompany(user.getCompany());
        MaterialPrice oldPrice = materialPriceDao.getAdjust(price);
        if (type - Integer.parseInt(oldPrice.getStatus()) != -1)
        {
            result.put("result", "fail");
            result.put("message", "单价状态异常，请刷新");
            return result;
        }
        price.setStatus(type + "");
        price.preUpdate();
        materialPriceDao.updateAdjustStatus(price);
        result.put("result", "success");
        switch (type)
        {
            case 1:
                result.put("message", "取消确认成功!");
                break;
            case 3:
                result.put("message", "反审核成功!");
                break;
        }
        return result;
    }

}
