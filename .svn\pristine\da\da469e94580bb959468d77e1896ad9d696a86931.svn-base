package com.kyb.pcberp.modules.report.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

/**
 * @ClassName ReportDeliveryCapacity
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/2/22 11:40
 * @Version 1.0
 **/
public class ReportComplaintCustomerAssessmentDetail extends DataEntity<ReportComplaintCustomerAssessmentDetail> {

    private String no;

    private String contractNo;

    private String materialNo;

    private String customerModel;

    private String departName;

    private String deptId;

    private String saleComName;

    private String salesmanName;

    private String quantity;

    private String fedNum;

    private String applyDate;

    private String versionId;

    private Double area;

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getMaterialNo() {
        return materialNo;
    }

    public void setMaterialNo(String materialNo) {
        this.materialNo = materialNo;
    }

    public String getCustomerModel() {
        return customerModel;
    }

    public void setCustomerModel(String customerModel) {
        this.customerModel = customerModel;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getSaleComName() {
        return saleComName;
    }

    public void setSaleComName(String saleComName) {
        this.saleComName = saleComName;
    }

    public String getSalesmanName() {
        return salesmanName;
    }

    public void setSalesmanName(String salesmanName) {
        this.salesmanName = salesmanName;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getFedNum() {
        return fedNum;
    }

    public void setFedNum(String fedNum) {
        this.fedNum = fedNum;
    }

    public String getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate;
    }

    public String getVersionId() {
        return versionId;
    }

    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }
}
