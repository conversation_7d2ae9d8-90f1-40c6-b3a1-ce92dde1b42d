package com.kyb.pcberp.modules.icloud.erp.erp.pojo;

import com.kyb.pcberp.common.base.pojo.Icloud_BasePojo;
import com.kyb.pcberp.modules.icloud.sys.pojo.Icloud_User;

public class Icloud_StoreHouse extends Icloud_BasePojo
{
    private static final long serialVersionUID = 1L;

    /** 仓库名称 */
    private String name;

    /** 仓库位置 */
    private String location;

    /** 仓管员 */
    private Icloud_User administrator;

    /** 仓管员电话 */
    private String phone;

    /** 类型：1 原料仓，2 成品仓，3 销售仓 */
    private Integer type;

    /** false:共享仓库后显示，true：共享仓库后隐藏 */
    private boolean shareFlag;

    /** 0:不是默认仓，1:是默认仓 */
    private boolean defaultFlag;

    /** true 物料在物联网设置金额，false 物料没在物联网设置金额 */
    private boolean materialInternetFlag;

    /** 是否相同 */
    private boolean equalFlag = false;

    public Icloud_StoreHouse()
    {
        super();
    }

    public Icloud_StoreHouse(String id)
    {
        super(id);
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getLocation()
    {
        return location;
    }

    public void setLocation(String location)
    {
        this.location = location;
    }

    public Icloud_User getAdministrator()
    {
        return administrator;
    }

    public void setAdministrator(Icloud_User administrator)
    {
        this.administrator = administrator;
    }

    public String getPhone()
    {
        return phone;
    }

    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public Integer getType()
    {
        return type;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    public boolean isShareFlag()
    {
        return shareFlag;
    }

    public void setShareFlag(boolean shareFlag)
    {
        this.shareFlag = shareFlag;
    }

    public boolean isDefaultFlag()
    {
        return defaultFlag;
    }

    public void setDefaultFlag(boolean defaultFlag)
    {
        this.defaultFlag = defaultFlag;
    }

    public boolean isMaterialInternetFlag()
    {
        return materialInternetFlag;
    }

    public void setMaterialInternetFlag(boolean materialInternetFlag)
    {
        this.materialInternetFlag = materialInternetFlag;
    }

    public boolean isEqualFlag()
    {
        return equalFlag;
    }

    public void setEqualFlag(boolean equalFlag)
    {
        this.equalFlag = equalFlag;
    }
}
