<%@ page contentType="text/html;charset=UTF-8"%>
<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">生产管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="production.reply">客诉修复</a>
        </li>
    </ul>
</div>

<tabset class="tabset-margin-top">
    <tab heading="客诉修复" active="ctrl.tabs.reply.active" ng-click="ctrl.loadReplyList()">
    	<div class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">客诉编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="ctrl.no"
                                           disable-valid-styling="true" disable-invalid-styling="true"/>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">修复状态：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="ctrl.replyStatus" class="form-control">
                                        <option value="">所有</option>
                                        <option value="1">修复中</option>
                                        <option value="2">已入库</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">客户订单号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="ctrl.customerPo"
                                           disable-valid-styling="true" disable-invalid-styling="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">生产编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="ctrl.craftNo"
                                           disable-valid-styling="true" disable-invalid-styling="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">合同编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="ctrl.contractNo"
                                           disable-valid-styling="true" disable-invalid-styling="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">客户型号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="ctrl.customerModel"
                                           disable-valid-styling="true" disable-invalid-styling="true"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right"
                                    ng-click="ctrl.loadReplyList()">
                                <i class="fa fa-search"></i> 查&nbsp;询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="portlet light bordered">
            <div class="portlet-body">
                <div class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
                        <tr class="heading">
                        	<th>操作</th>
                        	<th>客诉编号</th>
                        	<th>过数状态</th>
                        	<th>生成日期</th>
                        	<th>客户编号</th>
                        	<th>客户订单号</th>
                        	<th>客户型号</th>
                        	<th>生产编号</th>
                        	<th>合同编号</th>
                        	<th>修理数量</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in ctrl.replyList">
                        	<td>
                        		<a title="打印流程卡" href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.download(row.recordId)"><i class="fa fa-print font-blue-hoki"></i> 打印流程卡</a>
                        	</td>
                        	<td>
                        		<a href="#" ng-click="ctrl.openDeail(row)">{{row.no}}</a>
                        	</td>
                        	<td>{{row.replyStatusStr}}</td>
                        	<td>{{row.lastUpdDateStr}}</td>
                        	<td>{{row.custNo}}</td>
                        	<td>{{row.customerPo}}</td>
                        	<td>{{row.customerModel}}</td>
                        	<td>{{row.craftNo}}</td>
                        	<td>{{row.contractNo}}</td>
                        	<td>{{row.repirNums}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row page-margin-top">
                    <div class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select class="form-control inline table-pageSize-width"
                                ng-model="ctrl.page.pageSize"
                                ng-change="ctrl.pageSizeChange()"
                                disable-auto-validate="true"
                                ng-options="pageSizeOption for pageSizeOption in ctrl.page.pageSizeOptions">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示{{ctrl.page.data.startCount}} / {{ctrl.page.data.endCount}}条，共 {{ctrl.page.data.count}} 条</span>
                    </div>
                    <div class="col-md-12 col-lg-6">
                        <paging class="pull-right" page="ctrl.page.data.pageNo"
                                page-size="ctrl.page.data.pageSize"
                                total="ctrl.page.data.count" adjacent="1" dots="..."
                                scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                active-class="active" disabled-class="disabled" show-prev-next="true"
                                paging-action="ctrl.doPage( page, pageSize, total)"> </paging>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <tab active="ctrl.tabs.replyDeail.active" ng-show="ctrl.tabs.replyDeail.show">
    	<tab-heading>客户详情 <i class="fa fa-times set-cursor-pointer" ng-click="ctrl.hideEditForm()"></i></tab-heading>
        <div class="panel panel-default">
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">修复批次：</label>
                                <div class="col-sm-7 col-md-8">
                                	<select  class="form-control" 
                                    		 ng-model="ctrl.batchId"
                                             ng-options="gen.produceBatchDetailId as gen.batchName for gen in ctrl.batchList"
                                             disable-auto-validate="true"
                                             ng-change="ctrl.getBatchCountList()">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="portlet light bordered">
            <div class="portlet-body">
                <div class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
                        <tr class="heading">
                        	<th style="text-align: center;">序号</th>
				        	<th style="text-align: center;">生产工序 </th>
				            <th style="text-align: center;">接板数量</th>
				            <th style="text-align: center;">接板时间</th>
				            <th style="text-align: center;">接板操作员</th>
				            <th style="text-align: center;">交板数量</th>
				            <th style="text-align: center;">交板时间</th>
				            <th style="text-align: center;">入库数量</th>
				            <th style="text-align: center;">交板操作员</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in ctrl.countList">
                       		<td style="text-align: center;" ng-bind="$index + 1"></td>
					        <td style="text-align: center;" ng-bind="row.category" ></td>
					        <td><span ng-if="row.takeOverQtyPcsT">{{row.takeOverQtyPcsT}}PCS</span></td>
					        <td style="text-align: center;">
				                <div class="form-group" style="margin-left: 5px;" >
				                    <input style="width: 100px;"
				                           type="text"
				                           ng-model="row.takeOverTime"
				                           disabled="disabled"
				                           data-date-format="yyyy-MM-dd"
				                           data-date-type="number"
				                           data-min-date="02/10/1901"
				                           data-max-date="today"
				                           data-autoclose="1"
				                           daysOfWeekDisabled="false"
				                           name="birthday"
				                           placeholder="过数日期"
				                           bs-datepicker/>
				                </div>
				            </td>
				            <td style="text-align: center;width: 180px;">
				            	<div class="form-group">
	                                <ui-select theme="bootstrap" ng-model="row.takeMan"
	                                 style="width: 170px;position:absolute" disabled="disabled">
                                         <ui-select-match
                                                 placeholder="">{{$select.selected.userName}}
                                         </ui-select-match>
                                         <ui-select-choices repeat="item.recordId as item in row.dataOperatorList | filter: $select.search" >
                                             <div ng-bind-html="item.userName | highlight: $select.search"></div>
                                         </ui-select-choices>
                                     </ui-select>
				            	</div>
				            </td>
				            <td>
				            	<span ng-if="row.handOverQtyPcsT">{{row.handOverQtyPcsT}}PCS</span>
				            	<span class="text-danger" ng-if="row.mrbA && row.mrbA > 0">报废数:{{row.mrbA}}</span>
				            	<span class="text-danger" ng-if="row.mrbB && row.mrbB > 0">返工数:{{row.mrbB}}</span>
				            </td>
				            <td style="text-align: center; ">
				                <div class="form-group" style="margin-left: 5px;">
				                    <input style="width: 100px;"
				                              type="text"
				                              ng-model="row.handOverTime"
				                              disabled="disabled"
				                              data-date-format="yyyy-MM-dd"
				                              data-date-type="number"
				                              data-min-date="02/10/1901"
				                              data-max-date="today"
				                              data-autoclose="1"
				                              daysOfWeekDisabled="false"
				                              placeholder="具体日期"
				                              name="birthday"
				                              bs-datepicker/>
				                </div>
				            </td>
				            <td style="text-align: center;">
				            	<input style="width: 50px;" ng-if="row.category == '终检入库'  && row.handOverFlag == 2"   
				            	ng-model="row.storageQuantity" disabled="disabled"/>
				            </td>
				            <td style="text-align: center;width: 180px;">
				            	<div class="form-group">
	                                <ui-select theme="bootstrap" ng-model="row.handMan" on-select="" style="width: 170px;position:absolute" 
	                                	disabled="disabled">
                                         <ui-select-match
                                                 placeholder="">{{$select.selected.userName}}
                                         </ui-select-match>
                                         <ui-select-choices repeat="item.recordId as item in row.dataOperatorList | filter: $select.search">
                                             <div ng-bind-html="item.userName | highlight: $select.search"></div>
                                         </ui-select-choices>
                                     </ui-select>
				            	</div>
				            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </tab>
</tabset>
<div class="row">
    <div class="col-md-12">
        <div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">提示</h4>
                    </div>
                    <div class="modal-body">
                        <p><span ng-bind="ctrl.message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>