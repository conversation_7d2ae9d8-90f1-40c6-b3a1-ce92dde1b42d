<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.quality.dao.ReworkDao">
    
	<sql id="qcReworkColumns">
		a.recordid AS "recordId",
		a.companyid AS "company.recordId",
		a.inspectid AS "inspectId",
		a.boardPartCraftId AS "boardPartCraft.recordId",
		a.craftNo AS "craftNo",
		a.reworkqty AS "reworkQty",
		a.reworkcause AS "reworkCause",
		a.reworkresult AS "reworkResult",
		a.reworker AS "reworker.recordId",
		a.reworkchecker AS "checker.recordId",
		a.reworkdate AS "reworkDate",
		a.dutyProcess AS "dutyProcess.recordId",
		a.activeflag AS "activeFlag",
		a.createdby AS "createdBy.recordId",
		a.createddate AS "createdDate",
		a.lastupdby AS "lastUpdBy.recordId",
		a.lastupddate AS "lastUpdDate",
		a.remark AS "remark",
		a.reworkType
	</sql>
	
	<sql id="qcReworkJoins">
	</sql>
    
	<select id="get" resultType="Rework">
		SELECT 
			<include refid="qcReworkColumns"/>
		FROM qc_rework a
		<include refid="qcReworkJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="findList" resultType="Rework">
		SELECT 
			<include refid="qcReworkColumns"/>
		FROM qc_rework a
		<include refid="qcReworkJoins"/>
		<where>
			
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="Rework">
		SELECT 
			<include refid="qcReworkColumns"/>
		FROM qc_rework a
		<include refid="qcReworkJoins"/>
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<resultMap id="reworkRs" type="Rework">
        <id property="recordId" column="recordId" />
		<result property="craftNo" column="craftNo" />
		<result property="reworkQty" column="reworkQty" />
		<result property="reworkCause" column="reworkCause" />
		<result property="reworkResult" column="reworkResult" />
		<result property="reworkDate" column="reworkDate" />
		<result property="activeFlag" column="activeFlag" />
		<result property="createdDate" column="createdDate" />
		<result property="lastUpdDate" column="lastUpdDate" />
		<result property="remark" column="remark" />
		<association property="company" column="companyId" javaType="Company">
			<id property="recordId" column="company.recordId"/>
			<result property="name" column="company.name"/>
		</association>
		<association property="createdBy" column="createdBy" javaType="User">
			<id property="recordId" column="createdBy.recordId"/>
		</association>
		<association property="dutyProcess" javaType="EgProcess">
			<id property="recordId" column="dutyProcess.recordId"/>
			<result property="category" column="dutyProcess.category" />
		</association>
		<association property="lastUpdBy" column="lastUpdBy" javaType="User">
			<id property="recordId" column="lastUpdBy.recordId"/>
		</association>
		<association property="reworker" column="reworker.recordId" javaType="User" select="selectUserById"/>
		<association property="checker" column="checker.recordId" javaType="User" select="selectUserById"/>
		<association property="inspect" column="inspectId" javaType="Inspect">
			<id property="recordId" column="inspect.recordId" />
			<result property="company.recordId" column="inspect.companyId" />
			<result property="manufType" column="inspect.manufType" />
			<result property="pcsEsperPnl" column="inspect.pcsEsperPnl" />
			<result property="producePcsQty" column="inspect.producePcsQty" />
			<result property="passPcsQty" column="inspect.passPcsQty" />
			<result property="failedPcsQty" column="inspect.failedPcsQty" />
			<result property="checkDate" column="inspect.checkDate" />
			<result property="createdDate" column="inspect.createdDate" />
			<result property="lastUpdDate" column="inspect.lastUpdDate" />
			<result property="remark" column="inspect.remark" />
			<association property="company" column="companyId" javaType="Company">
				<id property="recordId" column="inspect.company.recordId"/>
				<result property="name" column="inspect.company.name"/>
			</association>
			<association property="createdBy" column="createdBy" javaType="User">
				<id property="recordId" column="inspect.createdBy.recordId"/>
			</association>
			<association property="lastUpdBy" column="lastUpdBy" javaType="User">
				<id property="recordId" column="inspect.lastUpdBy.recordId"/>
			</association>
			<association property="produceBatch" column="produceBatchId" javaType="ProduceBatch">
				<id property="recordId" column="inspect.produceBatch.recordId"/>
				<result property="no" column="inspect.produceBatch.no"/>
				<result property="discardQty" column="inspect.produceBatch.discardQty"/>
				<association property="notification" column="notificationId" javaType="Notification">
					<id property="recordId" column="inspect.produceBatch.notification.recordId"/>
					<result property="no" column="inspect.produceBatch.notification.no"/>
					<result property="craftNo" column="inspect.produceBatch.notification.craftNo"/>
					<result property="mergeType" column="inspect.produceBatch.notification.mergeType"/>
				</association>
				<association property="feed" javaType="Feeding">
					<id property="recordId" column="inspect.produceBatch.feed.recordId"/>
					<result property="no" column="inspect.produceBatch.feed.no"/>
				</association>
				
				<association property="replenish" javaType="Replenish">
					<id property="recordId" column="inspect.produceBatch.replenish.recordId"/>
					<result property="no" column="inspect.produceBatch.replenish.no"/>
				</association>
			</association>
			<association property="process" column="processId" javaType="EgProcess">
				<id property="recordId" column="inspect.process.recordId"/>
				<result property="name" column="inspect.process.name"/>
			</association>
			<association property="produceBatchDetail" column="produceBatchDetailId" javaType="ProduceBatchDetail">
				<id property="recordId" column="inspect.produceBatchDetail.recordId"/>
				<result property="no" column="inspect.produceBatchDetail.no"/>
				<result property="discardQty" column="inspect.produceBatchDetail.discardQty"/>
			</association>
		</association>
    </resultMap>
	
	<select id="findReworkList" resultMap="reworkRs">
		SELECT 
			qr.recordid AS "recordId",
			qr.companyid AS "company.recordId",
			qr.inspectid AS "inspectId",
			IF(a.type=1,qr.craftNo,mm.`no`) AS "craftNo",
			qr.reworkqty AS "reworkQty",
			qr.reworkcause AS "reworkCause",
			qr.reworkresult AS "reworkResult",
			qr.reworker AS "reworker.recordId",
			qr.reworkchecker AS "checker.recordId",
			qr.reworkdate AS "reworkDate",
			qr.dutyProcess AS "dutyProcess.recordId",
	IF( a.type = 1, qeg.category,(SELECT GROUP_CONCAT(`name`) FROM eg_process WHERE FIND_IN_SET(recordId,qr.dutyProcessMore)) ) AS "dutyProcess.category",
			qr.activeflag AS "activeFlag",
			qr.createdby AS "createdBy.recordId",
			qr.createddate AS "createdDate",
			qr.lastupdby AS "lastUpdBy.recordId",
			qr.lastupddate AS "lastUpdDate",
			qr.remark AS "remark",
			a.producebatchid AS "inspect.produceBatch.recordId",
			a.produceBatchDetailId as "inspect.produceBatchDetail.recordId",
			a.processid AS "inspect.process.recordId",
			a.manuftype AS "inspect.manufType",
			a.pcsesperpnl AS "inspect.pcsEsperPnl",
			a.producepcsqty AS "inspect.producePcsQty",
			a.passpcsqty AS "inspect.passPcsQty",
			a.failedpcsqty AS "inspect.failedPcsQty",
			a.operator AS "inspect.operator.recordId",
			a.checker AS "inspect.checker.recordId",
			a.checkdate AS "inspect.checkDate",
			a.createdby AS "inspect.createdBy.recordId",
			a.createddate AS "inspect.createdDate",
			a.lastupdby AS "inspect.lastUpdBy.recordId",
			a.lastupddate AS "inspect.lastUpdDate",
			a.remark AS "inspect.remark",
			IF(a.type=1,ppb.NO,a.produceBatchDetailId) AS "inspect.produceBatch.no",
			ppb.status AS "inspect.produceBatch.status",
			ppb.discardQty AS "inspect.produceBatch.discardQty",
			n.recordId AS "inspect.produceBatch.notification.recordId",
			n.no AS "inspect.produceBatch.notification.no",
			IF(a.type=1,n.craftNo,mm.`no`)  AS "inspect.produceBatch.notification.craftNo",
			n.mergeType AS "inspect.produceBatch.notification.mergeType",
			f.recordId AS "inspect.produceBatch.feed.recordId",
			IF(a.type=1,f.NO,sra.`no`) AS "inspect.produceBatch.feed.no",
			r.recordId AS "inspect.produceBatch.replenish.recordId",
			r.no AS "inspect.produceBatch.replenish.no",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(eg.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(eg.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(eg.name," ","") END AS "inspect.process.name",
			pbd.no AS "inspect.produceBatchDetail.no",
			pbd.status AS "inspect.produceBatchDetail.status",
			pbd.discardQty AS "inspect.produceBatchDetail.discardQty"
		FROM qc_rework qr
		LEFT JOIN eg_process qeg ON qeg.recordId = qr.dutyProcess and qeg.activeFlag = #{DEL_FLAG_NORMAL}
		JOIN qc_inspect a ON qr.inspectId = a.recordId and qr.activeFlag = #{DEL_FLAG_NORMAL}
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = a.produceBatchId and ppb.activeFlag = #{DEL_FLAG_NORMAL} AND a.type = 1
		LEFT JOIN pd_produce_batch_detail pbd ON pbd.recordId = a.produceBatchDetailId and pbd.activeFlag = #{DEL_FLAG_NORMAL} AND a.type = 1
		LEFT JOIN eg_process eg ON eg.recordId = a.processId and eg.activeFlag = #{DEL_FLAG_NORMAL}
		LEFT JOIN sl_notification n ON n.recordId = ppb.notificationId and n.activeFlag = #{DEL_FLAG_NORMAL}
		LEFT JOIN pd_feeding f ON f.recordId = ppb.feedingNo and f.notificationId = ppb.notificationId and f.activeFlag = #{DEL_FLAG_NORMAL}
		LEFT JOIN pd_replenish r ON r.produceBatchNo = ppb.recordId and r.notificationId = ppb.notificationId and r.activeFlag = #{DEL_FLAG_NORMAL}
		LEFT JOIN eg_carda_process_value ecpv on ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = eg.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci ON epci.processId = eg.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		
		LEFT JOIN st_reject_application sra ON sra.recordId = a.produceBatchId AND a.type in(2,3)
		LEFT JOIN md_customer b ON b.recordId = sra.customerId
		LEFT JOIN sl_contract_detail c ON c.recordId = sra.contractDetailId
		LEFT JOIN sl_contract d ON d.recordId = c.contractId
		LEFT JOIN sl_contract_craft e ON e.recordId = c.craftId
		LEFT JOIN md_material mm ON mm.`no` = e.`no`
		AND mm.activeFlag = 1
		AND mm.companyId = a.companyId
		
		<where>
			AND a.companyid = #{inspect.company.recordId} 
			AND a.activeFlag = #{DEL_FLAG_NORMAL}
			<if test="inspect.produceBatch!=null and inspect.produceBatch.notification!=null 
			    		and inspect.produceBatch.notification.craftNo!=null and inspect.produceBatch.notification.craftNo!=''">
			 	and IF(a.type=1,n.craftNo,mm.`no`) like CONCAT('%', #{inspect.produceBatch.notification.craftNo}, '%')
			 </if>
			 <if test="inspect.produceBatch!=null and inspect.produceBatch.feed!=null and inspect.produceBatch.feed.no!=null and inspect.produceBatch.feed.no!=''">
			 	and (f.no like CONCAT('%', #{inspect.produceBatch.feed.no}, '%') or r.no like CONCAT('%', #{inspect.produceBatch.feed.no}, '%'))
			 </if>
			 <if test="inspect.startDate!=null and inspect.startDate!='' and inspect.endDate!=null and inspect.endDate!=''">
			 	and a.checkDate between #{inspect.startDate} and #{inspect.endDate}
			 </if>
			 <if test="inspect.startDate!=null and inspect.startDate!='' and (inspect.endDate==null or inspect.endDate=='')">
			 	and a.checkDate &gt;= #{inspect.startDate}
			 </if>
			 <if test="inspect.endDate!=null and inspect.endDate!='' and (inspect.startDate==null or inspect.startDate=='')">
			 	and a.checkDate &lt;= #{inspect.endDate}
			 </if>
			 <if test="inspect.createdBy != null and inspect.createdBy.recordId != null and inspect.createdBy.recordId != ''">
				AND (a.createdBy = #{inspect.createdBy.recordId} or a.operator = #{inspect.createdBy.recordId} or a.checker = #{inspect.createdBy.recordId}
				or qr.reworker = #{inspect.createdBy.recordId} or qr.reworkChecker = #{inspect.createdBy.recordId})
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="selectUserById" parameterType="Long" resultType="User">
    	select recordId,userName from sm_user where recordId=#{recordId}
    </select>
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO qc_rework(
			companyid,
			inspectid,
			boardPartCraftId,
			craftNo,
			reworkqty,
			reworkcause,
			reworkresult,
			reworker,
			reworkchecker,
			reworkdate,
			dutyProcess,
			activeflag,
			createdby,
			createddate,
			lastupdby,
			lastupddate,
			remark,
			dutyProcessMore,
			reworkType
		) VALUES (
			#{company.recordId},
			#{inspectId},
			#{boardPartCraft.recordId},
			#{craftNo},
			#{reworkQty},
			#{reworkCause},
			#{reworkResult},
			#{reworker.recordId},
			#{checker.recordId},
			#{reworkDate},
			#{dutyProcess.recordId},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{dutyProcessMore},
			#{reworkType}
		)
	</insert>
	
	<update id="update">
		UPDATE qc_rework SET 	
			companyid = #{company.recordId},
			inspectid = #{inspectId},
			boardPartCraftId = #{boardPartCraft.recordId},
			craftNo = #{craftNo},
			reworkqty = #{reworkQty},
			reworkcause = #{reworkCause},
			reworkresult = #{reworkResult},
			reworker = #{reworker.recordId},
			reworkchecker = #{checker.recordId},
			reworkdate = #{reworkDate},
			dutyProcess = #{dutyProcess.recordId},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate},
			remark = #{remark},
			dutyProcessMore =  #{dutyProcessMore},
			reworkType = #{reworkType}
		WHERE recordid = #{recordId}
	</update>
	
	<update id="updateResult">
		UPDATE qc_rework SET 	
			reworkresult = #{reworkResult},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate}
		WHERE recordid = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE qc_rework SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="deleteReal">
		DELETE FROM qc_rework WHERE recordId = #{recordId}
	</update>
	
	<!-- zjn 2019-04-19 批量添加返工记录 -->
	<insert id="batchInsert" parameterType="java.util.List">
		INSERT INTO qc_rework(
			companyid,
			inspectid,
			boardPartCraftId,
			craftNo,
			reworkqty,
			reworkcause,
			reworkresult,
			reworker,
			reworkchecker,
			reworkdate,
			dutyProcess,
			activeflag,
			createdby,
			createddate,
			lastupdby,
			lastupddate,
			remark,
			dutyProcessMore,
			reworkType,
			taskDetailId
		) VALUES 
			<foreach collection="reworkList" item="item" index= "index" separator =",">
			(
				#{item.company.recordId},
				#{item.inspect.recordId},
				#{item.boardPartCraft.recordId},
				#{item.craftNo},
				#{item.reworkQty},
				#{item.reworkCause},
				#{item.reworkResult},
				#{item.reworker.recordId},
				#{item.checker.recordId},
				#{item.reworkDate},
				#{item.dutyProcess.recordId},
				1,
				#{item.createdBy.recordId},
				#{item.createdDate},
				#{item.lastUpdBy.recordId},
				#{item.lastUpdDate},
				#{item.remark},
				#{item.dutyProcessMore},
				#{item.reworkType},
				#{item.taskDetailId}
			)
			</foreach>
	</insert>
	
	<!-- zjn 2019-04-21 批量删除返工信息 -->
	<update id="deleteBatchRework">
		UPDATE qc_rework SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId in (${recordId})
	</update>
	
	<!-- zjn 2019-05-20 批量更新返工对应的品检记录id -->
	<update id="updateInsId">
		UPDATE qc_rework SET 
			inspectId = #{inspect.recordId}
		where recordId in (${recordId})
	</update>
	
	<!-- zjn 2019-09-10 根据品质记录获取返工记录 -->
	<select id="getReworkList" resultType="Rework">
		SELECT
			<include refid="qcReworkColumns"/>,
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(ep.category," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(ep.category," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep.category," ","") END AS "dutyProcess.category",
			CASE epci.showWay WHEN 1 THEN IFNULL(REPLACE(ecpcv.processCraftValue," ",""),REPLACE(ep.name," ","")) 
			WHEN 2 THEN CONCAT(REPLACE(ep.name," ",""),'(',IFNULL(REPLACE(ecpcv.processCraftValue," ",""),'无'),')')
			ELSE REPLACE(ep.name," ","") END AS "dutyProcess.name"
		FROM qc_rework a
		LEFT JOIN qc_inspect qi ON qi.recordId = a.inspectId AND qi.activeFlag = 1
		LEFT JOIN pd_produce_batch ppb ON ppb.recordId = qi.produceBatchId AND ppb.activeFlag = 1
		LEFT JOIN eg_process ep ON ep.recordId = a.dutyProcess AND ep.activeFlag = 1
		LEFT JOIN eg_carda_process_value ecpv on ecpv.processCardAId = ppb.processCardAId AND ecpv.processId = ep.recordId AND ecpv.companyId = a.companyId AND ecpv.activeFlag = 1
		LEFT JOIN eg_process_craft_install epci ON epci.processId = ep.recordId AND epci.companyId = a.companyId AND epci.activeFlag = 1
		LEFT JOIN eg_carda_process_craft_value ecpcv ON ecpcv.processValueId = ecpv.recordId AND ecpcv.processCraftId = epci.craftId AND ecpcv.activeFlag = 1
		WHERE a.inspectId = #{recordId} AND a.companyId = #{company.recordId} AND a.activeFlag = 1
	</select>
	
	<update id="deleteReworkByInspect">
		UPDATE qc_rework SET
			activeFlag = 2
		WHERE inspectId = #{recordId}
	</update>

	<select id="getReworkDataList" resultType="Rework">
		SELECT
			ROUND(d.setLength * d.setWidth * a.reworkQty / d.pnlDivisor / 1000000,4) AS "reworkArea",
			e.userName AS "operator",
			a.*
		FROM qc_rework a
		LEFT JOIN qc_inspect b ON b.recordId = a.inspectId
		LEFT JOIN pd_produce_batch c ON c.recordId = b.produceBatchId
		LEFT JOIN eg_carda d ON d.recordId = c.processCardAId
		LEFT JOIN sm_user e ON e.recordId = a.createdBy
		WHERE a.activeFlag = 1 AND b.recordId = #{recordId}
	</select>
	
</mapper>