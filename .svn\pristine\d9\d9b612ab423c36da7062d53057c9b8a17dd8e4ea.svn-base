package com.kyb.pcberp.modules.hr.finance_center.service;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.material.MaterialTimeUtils;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.common.utils.alimessage.MessageUtil;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.yunxingkong.CloudySkyUtil;
import com.kyb.pcberp.modules.contract.dao.ContractDetailDao;
import com.kyb.pcberp.modules.contract.entity.Contract;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.crm.dao.AccountsReceivableDao;
import com.kyb.pcberp.modules.crm.dao.CraftNoAdjustDao;
import com.kyb.pcberp.modules.crm.dao.CustomerDao;
import com.kyb.pcberp.modules.crm.dao.GoodsCheckDao;
import com.kyb.pcberp.modules.crm.entity.AccountsReceivable;
import com.kyb.pcberp.modules.crm.entity.CraftNoAdjust;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.crm.entity.GoodsCheck;
import com.kyb.pcberp.modules.finance.dao.*;
import com.kyb.pcberp.modules.finance.entity.*;
import com.kyb.pcberp.modules.finance.service.CollectMoneyService;
import com.kyb.pcberp.modules.finance.util.PayMoneyUtil;
import com.kyb.pcberp.modules.hr.company_center.dao.Hr_RecruitDao;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_DepartMent;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_KybAttachments;
import com.kyb.pcberp.modules.hr.emp_center.dao.Hr_EmployeeDao;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;
import com.kyb.pcberp.modules.hr.finance_center.pojo.*;
import com.kyb.pcberp.modules.hr.finance_center.dao.Hr_CertificateDao;
import com.kyb.pcberp.modules.hr.finance_center.utils.Hr_ReportUtils;
import com.kyb.pcberp.modules.oa.dao.Oa_auditDao;
import com.kyb.pcberp.modules.oa.dao.Oa_dao;
import com.kyb.pcberp.modules.oa.pojo.audit.Oa_audit;
import com.kyb.pcberp.modules.oa.utils.ApprovalEngineUtil;
import com.kyb.pcberp.modules.purch.dao.AccountsPayableDao;
import com.kyb.pcberp.modules.purch.dao.MaterialCheckDao;
import com.kyb.pcberp.modules.purch.dao.SupplierDao;
import com.kyb.pcberp.modules.purch.entity.AccountsPayable;
import com.kyb.pcberp.modules.purch.entity.MaterialCheck;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.dao.RawmaterialStockDao;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import com.kyb.pcberp.modules.sys.dao.DepartmentDao;
import com.kyb.pcberp.modules.sys.dao.DictValueDao;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import com.kyb.pcberp.modules.sys.entity.GroupOrgRelation;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import com.kyb.pcberp.modules.wechat.dao.WechatOaDao;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatAudit;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatCommitApprove;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatOaNumber;
import com.kyb.pcberp.modules.wechat.service.KybSoftOaService;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class Hr_CertificateService
{
    @Autowired
    private Hr_CertificateDao hr_certificateDao;

    @Autowired
    private SingleReceivableDao singleReceivableDao;

    @Autowired
    private AccountsReceivableDao accountsReceivableDao;

    @Autowired
    private SingleReceivableDetailDao singleReceivableDetailDao;

    @Autowired
    private CollectMoneyDao collectMoneyDao;

    @Autowired
    private CollectMuchMoneyDao collectMuchMoneyDao;

    @Autowired
    private CollectMoneyService collectMoneyServicel;

    @Autowired
    private SupplierDao supplierDao;

    @Autowired
    private PayMoneyDao payMoneyDao;

    @Autowired
    private SinglePayableDetailDao singlePayableDetailDao;

    @Autowired
    private SinglePayableDao singlePayableDao;

    @Autowired
    private AccountsPayableDao accountsPayableDao;

    @Autowired
    private CompanyDao companyDao;

    @Autowired
    private DepartmentDao departmentDao;

    @Autowired
    private DictValueDao dictValueDao;

    @Autowired
    private Oa_dao oa_dao;

    @Autowired
    private MaterialDao materialDao;

    @Autowired
    private GoodsCheckDao goodsCheckDao;

    @Autowired
    private MaterialCheckDao materialCheckDao;

    @Autowired
    private RawmaterialStockDao rawmaterialStockDao;

    @Autowired
    private ContractDetailDao contractDetailDao;

    @Autowired
    private WechatOaDao wechatOaDao;

    @Autowired
    private KybSoftOaService kybSoftOaService;

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private Hr_RecruitDao hr_RecruitDao;

    @Autowired
    private Hr_EmployeeDao hr_employeeDao;

    @Autowired
    private Oa_auditDao oa_auditDao;

    public Page<Hr_ManualBill> getCertificateList(Hr_ManualBill hr_manualBill, HttpServletRequest request,
                                                   HttpServletResponse response, Integer type)
    {
        Page<Hr_ManualBill> page = new Page<>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(hr_manualBill.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_manualBill.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_manualBill.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_manualBill.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
        }
        List<Hr_ManualBill> list = hr_certificateDao.getCertificateList(hr_manualBill);
        //查询客户收款
        List<Hr_ManualBill> customCollectionList = hr_certificateDao.getCustomCollectionList(hr_manualBill);
        //查询付款
        List<Hr_ManualBill> supplierCollectionList = hr_certificateDao.getSupplierPaymentList(hr_manualBill);
        list.addAll(customCollectionList);
        list.addAll(supplierCollectionList);
        if (Collections3.isNotEmpty(list))
        {
            List<Hr_ManualBill> certificateDetailList = hr_certificateDao.getCertificateDetailList(list);

            if (Collections3.isNotEmpty(certificateDetailList))
            {
                for(Hr_ManualBill certificateObj : list)
                {
                    List<Hr_ManualBill> detailList = Lists.newArrayList();
                    for(Hr_ManualBill certificateDetail : certificateDetailList)
                    {
                        if (certificateObj.getRecordId().equals(certificateDetail.getManualBillId()))
                        {
                            detailList.add(certificateDetail);
                        }
                    }
                    certificateObj.setCertificateDetailList(detailList);
                    if (Collections3.isEmpty(detailList))
                    {
                        certificateObj.setAuditStatus("22001");
                    }else {
                        certificateObj.setAuditStatus(detailList.get(0).getAuditStatus());
                    }
                }
            }
        }
        List<Hr_ManualBill> resultList = Lists.newArrayList();

        for (Hr_ManualBill resultBill : list) {
                if (Collections3.isEmpty(resultBill.getCertificateDetailList()))
                {
                    if (null != hr_manualBill.getSecondStatus() && null != hr_manualBill.getThirdStatus())
                    {
                        continue;
                    }
                    if (null != hr_manualBill.getSecondStatus() && !hr_manualBill.getAuditStatus().equals("22001"))
                    {
                        continue;
                    }else if (null != hr_manualBill.getSecondStatus() && hr_manualBill.getAuditStatus().equals("22001"))
                    {
                        resultList.add(resultBill);
                    }else {
                        resultList.add(resultBill);
                    }
                }
                if (Collections3.isNotEmpty(resultBill.getCertificateDetailList()))
                {
                    for (Hr_ManualBill detail : resultBill.getCertificateDetailList()) {
                        // 审批状态
                        if (testA(detail.getAuditStatus(), hr_manualBill.getAuditStatus(), 1)) {
                            continue;
                        }
                        // 账簿
                        if (testA(detail.getAccountbookName(), hr_manualBill.getAccountbookName(), 2)) {
                            continue;
                        }
                        // 制单
                        if (testA(detail.getEmployeeName(), hr_manualBill.getEmployeeName(), 2)) {
                            continue;
                        }
                        // 科目编码
                        if (testA(detail.getSubjectDeteilName(), hr_manualBill.getSubjectDeteilName(), 2)) {
                            continue;
                        }
                        // 科目全称
                        if (testA(detail.getFullName(), hr_manualBill.getFullName(), 2)) {
                            continue;
                        }
                        // 摘要
                        if (testA(detail.getTogetherMessage(), hr_manualBill.getTogetherMessage(), 2)) {
                            continue;
                        }
                        // 核算维度
                        if (testA(detail.getDimensionName(), hr_manualBill.getDimensionName(), 2)) {
                            continue;
                        }
                        //币别
                        if (testA(detail.getCurrencyName(), hr_manualBill.getCurrencyName(), 1)) {
                            continue;
                        }
                        //类型
                        if (testA(detail.getExchangeRateType(), hr_manualBill.getExchangeRateType(), 1)) {
                            continue;
                        }
                        // 借方金额
                        if (testA(detail.getBorrowAmount(), hr_manualBill.getBorrowAmount(), 2)) {
                            continue;
                        }
                        // 贷方金额
                        if (testA(detail.getLoanAmount(), hr_manualBill.getLoanAmount(), 2)) {
                            continue;
                        }
                        resultList.add(resultBill);
                        break;
                    }
                }
            }
        List<Hr_ManualBill> listTwo  =new ArrayList<>();
        Integer num = (pageNo-1) * pageSize;
        for(int i = num; i < resultList.size();i++){
            if (listTwo.size() == pageSize){
                break;
            }
            listTwo.add(resultList.get(i));
        }
        page.setList(listTwo);
        page.setCount(resultList.size());
        return page;
    }

    public Map<String,Object> getCollectionPaidList(Hr_CollectPayBill hr_collectPayBill)
    {
        Map<String,Object> data = new HashMap<>();
        List<Hr_CollectPayBill> singleReceivableList = Lists.newArrayList();
        List<Hr_CollectPayBill> singlePayableList = Lists.newArrayList();
        if(StringUtils.isNotBlank(hr_collectPayBill.getDimensionId()) && hr_collectPayBill.getDimensionId().equals("1"))
        {
            //查询应收单
            List<Hr_CollectPayBill> list = hr_certificateDao.getSingleReceivableList(hr_collectPayBill);
            for(Hr_CollectPayBill singleReceive : list)
            {
                if (singleReceive.getNumber() > 0)
                {
                    BigDecimal amountT = singleReceive.getAmount().subtract(singleReceive.getSettlementAmount()).subtract(singleReceive.getReceivedAmountT());
                    if (amountT.compareTo(BigDecimal.ZERO) > 0)
                    {
                        singleReceive.setAmount(amountT);
                        BigDecimal receivedAmountT = singleReceive.getReceivedAmount().add(singleReceive.getReceivedAmountT()).add(singleReceive.getSettlementAmount());
                        singleReceive.setReceivedAmount(receivedAmountT);
                        singleReceivableList.add(singleReceive);
                    }
                }else if (singleReceive.getNumber() == 0 && singleReceive.getAmount().compareTo(BigDecimal.ZERO) > 0){
                    BigDecimal amountTwo = singleReceive.getAmount().subtract(singleReceive.getSettlementAmount());
                    if (amountTwo.compareTo(BigDecimal.ZERO) > 0)
                    {
                        singleReceive.setAmount(amountTwo);
                        BigDecimal receivedAmountTwo = singleReceive.getReceivedAmount().add(singleReceive.getSettlementAmount());
                        singleReceive.setReceivedAmount(receivedAmountTwo);
                        singleReceivableList.add(singleReceive);
                    }
                }
            }
        }else if (StringUtils.isNotBlank(hr_collectPayBill.getDimensionId()) && hr_collectPayBill.getDimensionId().equals("2"))
        {
            //查询应付单
            hr_collectPayBill.setNumber(2);
            List<Hr_CollectPayBill> listTwo = hr_certificateDao.getSinglePayableListTwo(hr_collectPayBill);
            for(Hr_CollectPayBill  singlePayable : listTwo)
            {
                if (singlePayable.getAmount().compareTo(BigDecimal.ZERO) > 0)
                {
                    singlePayableList.add(singlePayable);
                }
            }
        }
        data.put("singleReceivableList",singleReceivableList);
        data.put("singlePayableList",singlePayableList);
        return data;
    }

    //出纳管理列表
    public Page<Hr_CollectPayBill> getCasherManageList(Hr_CollectPayBill hr_collectPayBill, HttpServletRequest request,
                                                       HttpServletResponse response, Integer type)
    {
        Page<Hr_CollectPayBill> page = new Page<>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(hr_collectPayBill.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_collectPayBill.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_collectPayBill.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_collectPayBill.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
        }
        List<Hr_CollectPayBill> collectionPaidList =  hr_certificateDao.getCollectionPaidList(hr_collectPayBill);
        if (Collections3.isNotEmpty(collectionPaidList))
        {
            List<Hr_CollectPayBill> collectionBillList = Lists.newArrayList();
            List<Hr_CollectPayBill> paymentBillList = Lists.newArrayList();
            List<Hr_CollectPayBill> list = Lists.newArrayList();
            for(Hr_CollectPayBill collectPayBill : collectionPaidList)
            {
                if (collectPayBill.getDimensionId().equals("1"))
                {
                    collectionBillList.add(collectPayBill);
                }else if (collectPayBill.getDimensionId().equals("2"))
                {
                    paymentBillList.add(collectPayBill);
                }
            }
            if(Collections3.isNotEmpty(collectionBillList))
            {
                List<Hr_CollectPayBill> collectionRecordList = hr_certificateDao.getCollectionRecordList(collectionBillList);
                list.addAll(collectionRecordList);
            }
            if (Collections3.isNotEmpty(paymentBillList))
            {
                List<Hr_CollectPayBill> paymentRecordList = hr_certificateDao.getPaymentRecordList(paymentBillList);
                list.addAll(paymentRecordList);
            }
            for(Hr_CollectPayBill bill : collectionPaidList)
            {
                List<Hr_CollectPayBill> detailList = Lists.newArrayList();
                for(Hr_CollectPayBill record : list)
                {
                    if (bill.getRecordId().equals(record.getReceiptPaymentId()))
                    {
                        detailList.add(record);
                    }
                }
                bill.setCollectionPayRecordList(detailList);
            }

            for(Hr_CollectPayBill billTwo : collectionPaidList)
            {
                if (billTwo.getDimensionId().equals("1"))
                {
                    for(Hr_CollectPayBill recordTwo : billTwo.getCollectionPayRecordList())
                    {
                        if (StringUtils.isBlank(recordTwo.getReceivedPaidId()))
                        {
                          Integer count = hr_certificateDao.getSingleReceivableCount(billTwo);
                          if (null != count && count > 0)
                          {
                              billTwo.setCancelStatus("1");
                              break;
                          }
                        }
                    }
                }
            }
        }
        List<Hr_CollectPayBill> listTwo  =new ArrayList<>();
        Integer num = (pageNo-1) * pageSize;
        for(int i = num; i < collectionPaidList.size();i++){
            if (listTwo.size() == pageSize){
                break;
            }
            listTwo.add(collectionPaidList.get(i));
            page.setList(listTwo);
        }
        page.setList(listTwo);
        page.setCount(collectionPaidList.size());
        return page;
    }

    public Boolean testA(Object value1,Object value2,Integer type)
    {
        Boolean flag = true;
        if ((value1 == null && value2 != null) || type == null)
        {
            return flag;
        }
        switch (type)
        {
            case 1:
                if (value2 == "" || null == value2 || value1.equals(value2))
                {
                    flag = false;
                }
                break;
            case 2:
                if (value2 == "" || null == value2 || value1.toString().contains(value2.toString().trim()))
                {
                    flag = false;
                }
                break;
        }
        return flag;
    }

    @Transactional(readOnly = false)
    public String deleteCertificateData(Hr_CertificateDetail hr_certificateDetail)
    {
        hr_certificateDao.deleteCertificateData(hr_certificateDetail);
        return "success";
    }

    public Page<Hr_Certificate> getExportCertificateList(Hr_Certificate hr_certificate, HttpServletRequest request,
        HttpServletResponse response, Integer type)
    {
        Page<Hr_Certificate> page = new Page<>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(hr_certificate.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_certificate.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_certificate.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_certificate.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_certificate.setPage(page);
        }
        List<Hr_Certificate> list = hr_certificateDao.getExportCertificateList(hr_certificate);
        page.setList(list);
        return page;
    }



    public Page<Hr_ManualBill> getExportCertificateDetailList(Hr_ManualBill hr_manualBill, HttpServletRequest request,
                                                         HttpServletResponse response, Integer type)
    {
        Page<Hr_ManualBill> page = new Page<>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(hr_manualBill.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_manualBill.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_manualBill.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_manualBill.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_manualBill.setPage(page);
        }
        List<Hr_ManualBill> list = hr_certificateDao.getCertificateList(hr_manualBill);
        //查询客户收款
        List<Hr_ManualBill> customCollectionList = hr_certificateDao.getCustomCollectionList(hr_manualBill);
        //查询付款
        List<Hr_ManualBill> supplierCollectionList = hr_certificateDao.getSupplierPaymentList(hr_manualBill);
        list.addAll(customCollectionList);
        list.addAll(supplierCollectionList);
        if (Collections3.isNotEmpty(list))
        {
            List<Hr_ManualBill> certificateDetailList = hr_certificateDao.getExportCertificateDetailListTwo(list);

            if (Collections3.isNotEmpty(certificateDetailList))
            {
                for(Hr_ManualBill certificateObj : list)
                {
                    List<Hr_ManualBill> detailList = Lists.newArrayList();
                    for(Hr_ManualBill certificateDetail : certificateDetailList)
                    {
                        if (certificateObj.getRecordId().equals(certificateDetail.getMajorNoId()))
                        {
                            detailList.add(certificateDetail);
                        }
                    }
                    certificateObj.setCertificateDetailList(detailList);
                    if (Collections3.isEmpty(detailList))
                    {
                        certificateObj.setAuditStatus("22001");
                    }else {
                        certificateObj.setAuditStatus(detailList.get(0).getAuditStatus());
                    }
                }
            }
        }
        List<Hr_ManualBill> resultList = Lists.newArrayList();
        List<Hr_ManualBill> certificateDetailList = Lists.newArrayList();
        for (Hr_ManualBill resultBill : list) {
            if (resultBill.getCertificateDetailList().size() <= 0)
            {
                if (null != hr_manualBill.getSecondStatus() && null != hr_manualBill.getThirdStatus())
                {
                    continue;
                }
                if (null != hr_manualBill.getSecondStatus() && !hr_manualBill.getAuditStatus().equals("22001"))
                {
                    continue;
                }else if (null != hr_manualBill.getSecondStatus() && hr_manualBill.getAuditStatus().equals("22001"))
                {
                    resultList.add(resultBill);
                }else {
                    resultList.add(resultBill);
                }
            }
            for (Hr_ManualBill detail : resultBill.getCertificateDetailList()) {
                // 审批状态
                if (testA(detail.getAuditStatus(), hr_manualBill.getAuditStatus(), 1)) {
                    continue;
                }
                // 账簿
                if (testA(detail.getAccountbookName(), hr_manualBill.getAccountbookName(), 2)) {
                    continue;
                }
                // 制单
                if (testA(detail.getEmployeeName(), hr_manualBill.getEmployeeName(), 2)) {
                    continue;
                }
                // 科目编码
                if (testA(detail.getSubjectDeteilName(), hr_manualBill.getSubjectDeteilName(), 2)) {
                    continue;
                }
                // 科目全称
                if (testA(detail.getFullName(), hr_manualBill.getFullName(), 2)) {
                    continue;
                }
                // 摘要
                if (testA(detail.getTogetherMessage(), hr_manualBill.getTogetherMessage(), 2)) {
                    continue;
                }
                // 核算维度
                if (testA(detail.getDimensionName(), hr_manualBill.getDimensionName(), 2)) {
                    continue;
                }
                //币别
                if (testA(detail.getCurrencyName(), hr_manualBill.getCurrencyName(), 1)) {
                    continue;
                }
                //类型
                if (testA(detail.getExchangeRateType(), hr_manualBill.getExchangeRateType(), 1)) {
                    continue;
                }
                // 借方金额
                if (testA(detail.getBorrowAmount(), hr_manualBill.getBorrowAmount(), 2)) {
                    continue;
                }
                // 贷方金额
                if (testA(detail.getLoanAmount(), hr_manualBill.getLoanAmount(), 2)) {
                    continue;
                }
                resultList.add(resultBill);
                certificateDetailList.addAll(resultBill.getCertificateDetailList());
                break;
            }
        }
        Set<String> clearId = new HashSet<>();
        for (Hr_ManualBill bill : certificateDetailList) {
            if (clearId.contains(bill.getMajorNoId())) {
                bill.setMajorNoId("");
                bill.setBookNo("");
                bill.setAccountbookName("");
                bill.setPeriod("");
                bill.setVoucherNo("");
                bill.setCertificateTime("");
                bill.setCertificateWordNo("");
                bill.setCertificateWordName("");
                bill.setDistributionIds("");
                bill.setUseOrganizationName("");
            } else {
                clearId.add(bill.getMajorNoId());
            }
        }
        page.setList(certificateDetailList);
        return page;
    }

    public Page<Hr_SinglePayable> getCombinationList(Hr_SinglePayable hr_singlePayable, HttpServletRequest request,
        HttpServletResponse response, Integer type)
    {
        Page<Hr_SinglePayable> page = new Page<>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 20;
        if (StringUtils.isNotBlank(hr_singlePayable.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_singlePayable.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_singlePayable.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_singlePayable.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_singlePayable.setPage(page);
        }
        List<Hr_SinglePayable> list = hr_certificateDao.getCombinationList(hr_singlePayable);
        page.setList(list);
        return page;
    }

    public Map<String, List<?>> getFinancialList()
    {
        Map<String,List<?>> data = new HashMap<>();

        // 获取摘要列表
        List<Hr_FinancialAbstract> financialAbstractList = hr_certificateDao.getFinancialAbstractList();
        data.put("financialAbstractList",financialAbstractList);

        // 获取科目列表
        List<Hr_StandardAccountDetail> standardAccountDetailList = hr_certificateDao.getStandardAccountDetailList();
        data.put("standardAccountDetailList",standardAccountDetailList);

        // 获取凭证字列表
        List<Hr_FinancialCertificateWord> financialCertificateWordList = hr_certificateDao.getFinancialCertificateWordList();
        data.put("financialCertificateWordList",financialCertificateWordList);

        // 获取账簿列表
        List<Hr_FinancialAccountbook> financialAccountbookList = hr_certificateDao.getFinancialAccountbookList();
        data.put("financialAccountbookList",financialAccountbookList);

        // 获取结算方式列表
        List<Hr_FinancialSettlementMethod> financialSettlementMethodList = hr_certificateDao.getFinancialSettlementMethodList();
        data.put("financialSettlementMethodList",financialSettlementMethodList);
        return data;
    }

    @Transactional(readOnly = false)
    public String addCertificate(Hr_ManualBill hr_manualBill)
    {
        if (null == hr_manualBill)
        {
            return "数据错误，请刷新重试!";
        }
        if (Collections3.isEmpty(hr_manualBill.getAccountBillList()))
        {
            return "请填写需要保存的凭证!";
        }
        if (StringUtils.isBlank(hr_manualBill.getAccountbookId()))
        {
            return "请选择账簿！!";
        }

        for(Hr_ManualBill detail : hr_manualBill.getAccountBillList())
        {
            // 删除核算维度
            if (StringUtils.isNotBlank(detail.getRecordId()))
            {
                Hr_CertificateDimension certificateDimension = new Hr_CertificateDimension();
                certificateDimension.setCertificateDetailId(detail.getRecordId());
                hr_certificateDao.delCertificateDimension(certificateDimension);
            }
        }
        hr_certificateDao.deleteAccountBill(hr_manualBill.getAccountBillList().get(0).getManualBillId());
        List<Hr_CertificateDimension> insertList = Lists.newArrayList();
        for(Hr_ManualBill detail : hr_manualBill.getAccountBillList())
        {
            detail.setAccountbookId(hr_manualBill.getAccountbookId());
            detail.setCertificateTime(hr_manualBill.getCertificateTime());
            detail.setBusinessTime(hr_manualBill.getBusinessTime());
            detail.setVoucherNo(hr_manualBill.getVoucherNo());
            detail.setCertificateWordId(hr_manualBill.getCertificateWordId());
            detail.setAuditStatus("22001");
            hr_certificateDao.addAccountBillTwo(detail);
            if(Collections3.isNotEmpty(detail.getDimensionList()))
            {
                for(Hr_CertificateDimension dimension : detail.getDimensionList())
                {
                    dimension.setCertificateDetailId(detail.getRecordId());
                    insertList.add(dimension);
                }
            }
        }
        if(Collections3.isNotEmpty(insertList))
        {
            hr_certificateDao.batchInsertCertificateDimension(insertList);
        }
        if (StringUtils.isNotBlank(hr_manualBill.getTypeName()) && hr_manualBill.getTypeName().equals("电子承兑"))
        {
            hr_manualBill.setManualBillId(hr_manualBill.getAccountBillList().get(0).getManualBillId());
            //更改电子承兑状态
            hr_certificateDao.updateManualBillStatus(hr_manualBill);
        }
        return "保存成功!";
    }

    @Transactional(readOnly = false)
    public String updateCertificate(Hr_Certificate hr_certificate)
    {
        if (null == hr_certificate)
        {
            return "数据错误，请刷新重试";
        }
        if (hr_certificate.getIsNewRecord())
        {
            hr_certificate.preInsert();
            hr_certificateDao.addCertificate(hr_certificate);
        }
        else
        {
            hr_certificate.preUpdate();
            hr_certificateDao.updaCertificate(hr_certificate);
        }
        return "保存成功!";
    }

    /*public List<Hr_CertificateDetail> getCertificateDetailList(Hr_CertificateDetail hrCertificateDetail)
    {
        List<Hr_CertificateDetail> list = hr_certificateDao.getCertificateDetailList(hrCertificateDetail);
        return list;
    }*/

    public String addCertificateDetailList(List<Hr_CertificateDetail> hrCertificateDetailList)
    {
        if (Collections3.isEmpty(hrCertificateDetailList))
        {
            return "数据错误，请刷新重试!";
        }
        for (Hr_CertificateDetail hrCertificateDetail : hrCertificateDetailList)
        {
            if (hrCertificateDetail.getIsNewRecord())
            {
                hrCertificateDetail.preInsert();
                hr_certificateDao.addCertificateDetail(hrCertificateDetail);
            }
            else
            {
                hrCertificateDetail.preUpdate();
                hr_certificateDao.updCertificateDetail(hrCertificateDetail);
            }
        }
        return "保存成功";
    }

    public Map<String, List<?>> getFinancialList(String accountingDimension, String departmentId,String organizationId)
    {
        Map<String,List<?>> data = new HashMap<>();
        if (accountingDimension != null && accountingDimension.contains("客户"))
        {
            List<Hr_FinancialCustomer> hrFinancialCustomerList = hr_certificateDao.getHrFinancialCustomerList();
            data.put("customerList", hrFinancialCustomerList);
        }
        if (accountingDimension != null && accountingDimension.contains("部门"))
        {
            List<Hr_FinancialDepartment> hrFinancialDepartmentList = hr_certificateDao.getHrFinancialDepartmentList(departmentId);
            data.put("departmentList", hrFinancialDepartmentList);
        }
        if (accountingDimension != null && accountingDimension.contains("组织机构"))
        {
            List<Hr_FinancialOrganization> hrFinancialOrganizationList = hr_certificateDao.getHrFinancialOrganizationList();
            data.put("organizationList", hrFinancialOrganizationList);
        }
        if (accountingDimension != null && accountingDimension.contains("供应商"))
        {
            List<Hr_FinancialSupplier> hrFinancialSupplierList = hr_certificateDao.getHrFinancialSupplierList();
            data.put("supplierList", hrFinancialSupplierList);
        }
        if (accountingDimension != null && accountingDimension.contains("收入分类"))
        {
            List<Hr_FinancialIncomeclassify> hrFinancialIncomeclassifyList = hr_certificateDao.getHrFinancialIncomeclassifyList();
            data.put("incomeclassifyList", hrFinancialIncomeclassifyList);
        }
        if (accountingDimension != null && accountingDimension.contains("银行"))
        {
            List<Hr_FinancialBank> hrFinancialBankList = hr_certificateDao.getHrFinancialBankList();
            data.put("hrFinancialBankList", hrFinancialBankList);

            List<Hr_FinancialBankaccount> hrFinancialBankaccountList = hr_certificateDao.getHrFinancialBankaccountList(organizationId);
            data.put("hrFinancialBankaccountList", hrFinancialBankaccountList);
        }
        return data;
    }

    public Page<Hr_Receivables> getReceivablesList(Hr_Receivables hr_receivables, HttpServletRequest request,
                                                   HttpServletResponse response, Integer type)
    {
        Page<Hr_Receivables> page = new Page<>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(hr_receivables.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_receivables.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_receivables.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_receivables.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_receivables.setPage(page);
        }
        List<Hr_Receivables> list = hr_certificateDao.getReceivablesList(hr_receivables);
        // 计算平米单价
        if (Collections3.isNotEmpty(list))
        {
            String recordIds = null;
            for (Hr_Receivables receivables : list)
            {
                if (null != recordIds && StringUtils.isNotBlank(recordIds))
                {
                    recordIds = recordIds + "," + receivables.getCompany().getRecordId();
                }
                else
                {
                    recordIds = receivables.getCompany().getRecordId();
                }
                if (null != receivables.getSetLength() && receivables.getSetLength().compareTo(BigDecimal.ZERO) != 0
                        && null != receivables.getSetWidth() && receivables.getSetWidth().compareTo(BigDecimal.ZERO) != 0
                        && null != receivables.getPnlDivisor() && receivables.getPnlDivisor().compareTo(BigDecimal.ZERO) != 0)
                {
                    receivables.setArea(receivables.getSetWidth().multiply(receivables.getSetLength()).divide(receivables.getPnlDivisor(), 4, BigDecimal.ROUND_HALF_UP).divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP));
                }
                if (null != receivables.getPrice() && receivables.getPrice().compareTo(BigDecimal.ZERO) != 0 && null != receivables.getArea() && receivables.getArea().compareTo(BigDecimal.ZERO) != 0)
                {
                    receivables.setAreaPrice(receivables.getPrice().divide(receivables.getArea(), 4, BigDecimal.ROUND_HALF_UP));
                }
            }
            List<DictValue> valueList = hr_certificateDao.getPaymentTermList(recordIds);
            if (Collections3.isNotEmpty(valueList))
            {
                for (Hr_Receivables hrReceivables : list)
                {
                    hrReceivables.setDictValueList(Lists.newArrayList());
                    for (DictValue dictValue : valueList)
                    {
                        if (dictValue.getCompany().getRecordId().equals(hrReceivables.getCompany().getRecordId()))
                        {
                            hrReceivables.getDictValueList().add(dictValue);
                        }
                    }
                }
            }
        }
        page.setList(list);
        return page;
    }

    public String addReceivablesList(List<Hr_Receivables> hr_receivablesList)
    {
        for (Hr_Receivables hrReceivables : hr_receivablesList)
        {
            // 收款记录编号
            String documentNo = CommonUtils.geHrDocumentNo(CommonEnums.CodeType.PAYMENTRECORD.getIndex().toString(), hrReceivables.getCompany());
            Integer noNum = collectMoneyDao.findDocumentNo(documentNo, hrReceivables.getCompany().getRecordId());
            while (null != noNum && noNum > 0)
            {
                CommonUtils.updateNextNo(CommonEnums.CodeType.PAYMENTRECORD.getIndex(), hrReceivables.getCompany());
                documentNo = CommonUtils.geHrDocumentNo(CommonEnums.CodeType.PAYMENTRECORD.getIndex().toString(), hrReceivables.getCompany());
                noNum = collectMoneyDao.findDocumentNo(documentNo, hrReceivables.getCompany().getRecordId());
            }
            CollectMoney collectMoney = new CollectMoney();
            collectMoney.setType("1");
            collectMoney.setBillNo(documentNo);
            collectMoney.setOperateStatus("1002");
            collectMoney.setPeriod(hrReceivables.getPeriod());
            collectMoney.setStatusCollectFlag("1");
            collectMoney.setCollectedDate(new Date());
            collectMoney.setCollectWay(Integer.valueOf(hrReceivables.getPayWayId()));
            if (collectMoney.getStatus() == null)
            {
                collectMoney.setStatus(TypeKey.BILL_STATUS_NORMAL);
            }
            // 默认为应收款
            if (collectMoney.getFundType() == null)
            {
                collectMoney.setFundType(TypeKey.FUND_TYPE_RECEVIABLES);
            }
            collectMoney.setCompany(hrReceivables.getCompany());

            // 收款金额
            collectMoney.setAmount(hrReceivables.getWriteOffsAmount());

            if (collectMoney.getFundType().equals(TypeKey.FUND_TYPE_RECEVIABLES))
            {
                collectMoney.setContract(new Contract("-1"));
            }
            collectMoney.setCustomer(hrReceivables.getCustomer());

            BigDecimal muchAmount = hrReceivables.getWriteOffsAmount();
            if (null == muchAmount)
            {
                continue;
            }
            // 获取应收单
            SingleReceivable singleReceivable = new SingleReceivable();
            singleReceivable.setRecordId(hrReceivables.getSingleReceivableId());
            SingleReceivable single = singleReceivableDao.get(singleReceivable);
            if (null != single)
            {
                // 如果款项类型为应收款时，不关联合同信息
                AccountsReceivable ac = new AccountsReceivable();
                ac.setCompany(hrReceivables.getCompany());
                ac.setCustomer(single.getCustomer());
                ac.setPeriod(single.getPeriod());
                // 如果收款金额大于0 就正常添加
                // 查询该客户的传过来的核销日期对应的的收款对账单
                List<AccountsReceivable> al = accountsReceivableDao.findAccountsReceivableListByBeanOrderByPeriod(ac);
                AccountsReceivable accountsReceivable = al.get(0);
                // 期末余款 就是还需要收取的金额
                BigDecimal amt = single.getRcvAmount() == null ? BigDecimal.ZERO : single.getRcvAmount();
                // 本期已收收款金额
                BigDecimal receivedAmount = single.getReceivedAmount() == null ? BigDecimal.ZERO : single.getReceivedAmount();
                if (amt.compareTo(muchAmount) == 1)
                {
                    // 对账单收到的款项+现在付的款项=现在收到的总的款项
                    accountsReceivable.setReceivedAmount(receivedAmount.add(muchAmount));
                    // 现在收到的款项-还需要收的款项=现在还需要收的总的款项
                    muchAmount = muchAmount.subtract(amt);
                    // 此对账单没有完成
                    accountsReceivable.setCompleteFlag(TypeKey.YES);
                    // 更新对账单
                    accountsReceivableDao.updateAccountsReceivable(accountsReceivable);
                }
                // 如果余款等于于现在的收款
                else if (amt.compareTo(muchAmount) <= 0)
                {
                    accountsReceivable.setReceivedAmount(receivedAmount.add(amt));
                    accountsReceivable.setCompleteFlag(TypeKey.FALSE);
                    accountsReceivableDao.updateAccountsReceivable(accountsReceivable);

                    single.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_FINISH.toString());
                    singleReceivableDao.updateStatus(single);
                }
                SingleReceivableDetail detail = new SingleReceivableDetail();
                detail.setRecordId(hrReceivables.getRecordId());
                detail.setConfirmAmount(muchAmount);
                detail.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_DETAIL_CONFIRM.toString());
                detail.preUpdate();
                singleReceivableDetailDao.updateStatus(detail);
                singleReceivableDao.updateOtherAmountData(single);
            }
            if (collectMoney.getIsNewRecord())
            {
                collectMoney.preInsert();
                collectMoneyDao.insert(collectMoney);
                // 添加成功后吧对象编码的nextNo修改为1
                CommonUtils.updateNextNo(15);
                CollectMuchMoney collectMuchMoney = new CollectMuchMoney();
                collectMuchMoney.setCompany(hrReceivables.getCompany());
                collectMuchMoney.setCollectMoney(collectMoney);
                collectMuchMoney.setCustomer(hrReceivables.getCustomer());
                collectMuchMoney.setContract(collectMoney.getContract());
                collectMuchMoney.setBillNo(collectMoney.getBillNo());
                collectMuchMoney.setPeriod(single.getPeriod());
                collectMuchMoney.setCollectedDate(new Date());
                collectMuchMoney.setAmount(muchAmount);
                collectMuchMoney.setCollectWay(Integer.valueOf(hrReceivables.getPayWayId()));
                collectMuchMoney.setFundType(collectMoney.getFundType());
                collectMuchMoney.setSingleReceivableId(hrReceivables.getSingleReceivableId());
                collectMuchMoney.setSingleReceivableDetailId(hrReceivables.getRecordId());
                collectMuchMoney.setType(hrReceivables.getType());
                collectMuchMoney.preInsert();
                collectMuchMoneyDao.insert(collectMuchMoney);

                // 批量增加中间表数据
                collectMoney.preInsert();
                collectMoneyDao.insertSingleDetailCollect(collectMuchMoney);
            }
            else
            {
                collectMoney.preUpdate();
                collectMoneyDao.update(collectMoney);
            }
            // 创建报表统计
            collectMoneyServicel.saveCollectionTotalReport(collectMoney);

            Hr_WriteOffs hr_writeOffs = new Hr_WriteOffs();
            hr_writeOffs.setSourceId(hrReceivables.getSingleReceivableId());
            hr_writeOffs.setDeailId(hrReceivables.getRecordId());
            hr_writeOffs.setAmount(hrReceivables.getWriteOffsAmount());
            hr_writeOffs.setType("1");
            hr_writeOffs.preInsert();
            hr_certificateDao.addWriteOffs(hr_writeOffs);
        }
        return "销账成功";
    }

    public Page<Hr_SinglePayable> getErpSinglePayableList(Hr_SinglePayable hr_SinglePayable, HttpServletRequest request,
                                                          HttpServletResponse response, Integer type)
    {
        Page<Hr_SinglePayable> page = new Page<Hr_SinglePayable>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;

        if (StringUtils.isNotBlank(hr_SinglePayable.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_SinglePayable.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_SinglePayable.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_SinglePayable.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_SinglePayable.setPage(page);
        }
        // 查询erp应付单
        List<Hr_SinglePayable> list = hr_certificateDao.getSinglePayableList(hr_SinglePayable);
        page.setList(list);
        return page;
    }

    public Page<Hr_SinglePayable> getOaSinglePayableList(Hr_SinglePayable hr_SinglePayable, HttpServletRequest request,
                                                         HttpServletResponse response, Integer type)
    {
        Page<Hr_SinglePayable> page = new Page<Hr_SinglePayable>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;

        if (StringUtils.isNotBlank(hr_SinglePayable.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_SinglePayable.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_SinglePayable.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_SinglePayable.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_SinglePayable.setPage(page);
        }
        // 查询oa
        List<Hr_SinglePayable> list = hr_certificateDao.getOaSinglePayableList(hr_SinglePayable);
        if (Collections3.isNotEmpty(list))
        {
            String recordIds = null;
            for (Hr_SinglePayable singlePayable : list)
            {
                if (StringUtils.isNotBlank(recordIds))
                {
                    recordIds = recordIds + "," + singlePayable.getRecordId();
                }
                else
                {
                    recordIds = singlePayable.getRecordId();
                }
            }
            // 获取oa附件列表
            Hr_KybAttachments hr_auditAttachment = new Hr_KybAttachments();
            hr_auditAttachment.setAuditId(recordIds);
            List<Hr_KybAttachments> attachmentList = hr_certificateDao.getAuditAttachmentList(hr_auditAttachment);
            if (Collections3.isNotEmpty(attachmentList))
            {
                for (Hr_SinglePayable singlePayable : list)
                {
                    singlePayable.setAttachmentList(Lists.newArrayList());
                    for (Hr_KybAttachments attachment : attachmentList)
                    {
                        if (null != attachment.getAuditId() && StringUtils.isNotBlank(attachment.getAuditId()) && attachment.getAuditId().equals(singlePayable.getRecordId()))
                        {
                            singlePayable.getAttachmentList().add(attachment);
                        }
                    }
                }
            }
        }
        page.setList(list);
        return page;
    }

    public String addOaSinglePaybleList(List<Hr_SinglePayable> hr_singlePayableList)
    {
        if (null == hr_singlePayableList)
        {
            return "系统错误，请刷新重试!";
        }
        for (Hr_SinglePayable hrSinglePayable : hr_singlePayableList)
        {
            Hr_WriteOffs hr_writeOffs = new Hr_WriteOffs();
            hr_writeOffs.setSourceId(hrSinglePayable.getRecordId());
            hr_writeOffs.setAmount(hrSinglePayable.getWriteOffsAmount());
            hr_writeOffs.setType("3");
            hr_certificateDao.addOaWriteOffs(hr_writeOffs);
        }
        return "销账成功!";
    }

    public String addErpSinglePaybleList(List<Hr_SinglePayable> hr_singlePayableList)
    {
        if (null == hr_singlePayableList)
        {
            return "系统错误，请刷新重试!";
        }
        for (Hr_SinglePayable hr_singlePayable : hr_singlePayableList)
        {
            // 获取供应商的开户行信息
            Supplier supplier = supplierDao.get(hr_singlePayable.getSupplier());
            if (null == supplier)
            {
                return "系统错误，请刷新重试!";
            }
            // 付款记录编码
            String documentNo = CommonUtils.geDocumentNo(CommonEnums.CodeType.PAYMENTRECORDS.getIndex().toString(),hr_singlePayable.getCompany());
            Integer noNum = payMoneyDao.findDocumentNo(documentNo,hr_singlePayable.getCompany().getRecordId());
            while (null != noNum && noNum > 0)
            {
                CommonUtils.updateNextNo(CommonEnums.CodeType.PAYMENTRECORDS.getIndex(),hr_singlePayable.getCompany());
                documentNo = CommonUtils.geHrDocumentNo(CommonEnums.CodeType.PAYMENTRECORDS.getIndex().toString(), hr_singlePayable.getCompany());
                noNum = payMoneyDao.findDocumentNo(documentNo,hr_singlePayable.getCompany().getRecordId());
            }
            // 生成付款记录
            PayMoney payMoney = new PayMoney();
            payMoney.setSupplier(supplier);
            payMoney.setPeriod(Integer.valueOf(hr_singlePayable.getPeriod()));
            payMoney.setStatusCollectFlag("1");
            payMoney.setPayDate(new Date());
            payMoney.setAmount(hr_singlePayable.getWriteOffsAmount());
            payMoney.setBillNo(documentNo);
            payMoney.setPayMethod(hr_singlePayable.getPayWayVale());
            payMoney.setApplyType("0");
            payMoney.setFundType(String.valueOf(TypeKey.FUND_TYPE_DUEPAYMENT));
            payMoney.setReceiveAccount(supplier.getAccount());
            payMoney.setBank(supplier.getBank());
            payMoney.setPurchasing(null);
            payMoney.setPayApplication(null);
            payMoney.setStatus(********);
            payMoney.setRemark("通过核销生成");
            payMoneyDao.insertPayMoney(payMoney);
            // 更新应付单明细已付金额
            SinglePayableDetail singlePayableDetail = singlePayableDetailDao.getSinglePayableDetail(hr_singlePayable.getRecordId());
            if (null != singlePayableDetail)
            {
                if (singlePayableDetail.getConfirmAmount().compareTo(BigDecimal.ZERO) != 0)
                {
                    BigDecimal amount = BigDecimal.ZERO;
                    amount = singlePayableDetail.getConfirmAmount().add(hr_singlePayable.getWriteOffsAmount());
                    if (amount.compareTo(singlePayableDetail.getAmount()) == 0)
                    {
                        singlePayableDetail.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_DETAIL_CONFIRM.toString());
                    }
                    else
                    {
                        singlePayableDetail.setStatus(singlePayableDetail.getStatus());
                    }
                    singlePayableDetail.setConfirmAmount(amount);
                }
                else
                {
                    if (hr_singlePayable.getWriteOffsAmount().compareTo(singlePayableDetail.getAmount()) == 0)
                    {
                        singlePayableDetail.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_DETAIL_CONFIRM.toString());
                    }
                    else
                    {
                        singlePayableDetail.setStatus(singlePayableDetail.getStatus());
                    }
                    singlePayableDetail.setConfirmAmount(hr_singlePayable.getWriteOffsAmount());
                }
                singlePayableDetail.preUpdate();
                singlePayableDetailDao.updateConfirmAmount(singlePayableDetail);
                // 根据明细查应收单，更新收款金额
                SinglePayable singlePayable = new SinglePayable();
                singlePayable.setRecordId(singlePayableDetail.getSinglePayableId());
                singlePayable.setPeriod(Integer.valueOf(hr_singlePayable.getPeriod()));
                SinglePayable sing = singlePayableDao.get(singlePayable);
                if (null != sing)
                {
                    if (null != sing.getPaiedAmount() && sing.getPaiedAmount().compareTo(BigDecimal.ZERO) != 0)
                    {
                        BigDecimal amount = BigDecimal.ZERO;
                        amount = sing.getPaiedAmount().add(hr_singlePayable.getWriteOffsAmount());
                        if (amount.compareTo(sing.getRecvMaterialValue()) == 0)
                        {
                            sing.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_CONFIRM.toString());
                        }
                        else
                        {
                            sing.setStatus(sing.getStatus());
                        }
                        sing.setPaiedAmount(amount);
                    }
                    else
                    {
                        if (hr_singlePayable.getWriteOffsAmount().compareTo(sing.getRecvMaterialValue()) == 0)
                        {
                            sing.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_CONFIRM.toString());
                        }
                        else
                        {
                            sing.setStatus(sing.getStatus());
                        }
                        sing.setPaiedAmount(hr_singlePayable.getWriteOffsAmount());
                    }
                    singlePayableDao.updatePaiedAmount(sing);
                }
                // 查找对账单
                AccountsPayable accountsPayable = new AccountsPayable();
                accountsPayable.setSupplier(hr_singlePayable.getSupplier());
                accountsPayable.setPeriod(Integer.valueOf(hr_singlePayable.getPeriod()));
                accountsPayable.setCompany(hr_singlePayable.getCompany());
                AccountsPayable acter = accountsPayableDao.getAccountsPayable(accountsPayable);
                if (null != acter)
                {
                    if (acter.getPaiedAmount().compareTo(BigDecimal.ZERO) != 0)
                    {
                        BigDecimal amount = BigDecimal.ZERO;
                        amount = sing.getPaiedAmount().add(hr_singlePayable.getWriteOffsAmount());
                        acter.setPaiedAmount(amount);
                    }
                    else
                    {
                        acter.setPaiedAmount(hr_singlePayable.getWriteOffsAmount());
                    }
                }
                accountsPayableDao.updatePaiedAmount(acter);
            }
        }
        return "";
    }

    //部门数据
    public List<GroupOrgRelation> getdeptList(String groupDepartId)
    {
        User user = UserUtils.getUser();
        user.setGroupOrgId(groupDepartId);
        List<GroupOrgRelation> deptList = departmentDao.getGroupListTwo(user);
        return deptList;
    }

    public Boolean checkFlag(String idfirst,String idAfter)
    {
        Boolean flag = false;
        if(StringUtils.isNotBlank(idfirst) && StringUtils.isNotBlank(idAfter) && idfirst.equals(idAfter))
        {
            flag = true;
        }
        return flag;
    }

    public List<Company> getCollectionList(AccountsReceivable accountsReceivable)
    {
        List<Company> dataList = Lists.newArrayList();

        //获取集团层
        Hr_DepartMent query = new Hr_DepartMent();
        query.setGroupId(accountsReceivable.getGroupId());
        query.setDepartmentLevel("1");
        List<Hr_DepartMent> groupList = oa_dao.getGroupList(query);
        if(Collections3.isEmpty(groupList))
        {
            return null;
        }
        //获取公司数据
        List<Company> erpCompanyList = companyDao.getErpCompanyList(accountsReceivable.getCompanyIdList());
        //部门
        List<GroupOrgRelation> deptList = getdeptList(accountsReceivable.getGroupDepartId());
        if(Collections3.isEmpty(deptList))
        {
            deptList = Lists.newArrayList();
        }
        GroupOrgRelation deptObj = new GroupOrgRelation();
        deptObj.setGroupOrgId("-1");
        deptObj.setGroupOrgName("无");
        deptList.add(deptObj);

        // 赋值三层数据
        dataList = handDataList(groupList,erpCompanyList,deptList);

        Integer innerFlag = 1;
        // 单个公司不查终端
        if(null != accountsReceivable && Collections3.isNotEmpty(accountsReceivable.getCompanyIdList())
                && accountsReceivable.getCompanyIdList().size() == 1)
        {
            innerFlag = 0;
        }
        accountsReceivable.setInnerFlag(innerFlag);

        //获取各公司累计收款金额
        List<AccountsReceivable> collectionList = accountsReceivableDao.getCollectionCompany(accountsReceivable);

        //获取各公司应收金额、已收金额
        List<AccountsReceivable> amountReceivableList = accountsReceivableDao.getAmountReceivableList(accountsReceivable);

        //各公司累计未对账金额
        List<AccountsReceivable> notReconciliationList = accountsReceivableDao.getNotReconciliationList(accountsReceivable);

        //各公司累计应收未收金额
        List<AccountsReceivable> receivableNotList = accountsReceivableDao.getReceivableNotList(accountsReceivable);

        for(Company group : dataList)
        {
            BigDecimal unreconciledAmountTotalGroup = BigDecimal.ZERO; //累计未对账（收款）
            BigDecimal amountReceivableGroup = BigDecimal.ZERO; //应收金额
            BigDecimal amountReceivedGroup = BigDecimal.ZERO; //实收金额
            BigDecimal recevialAmountGroup = BigDecimal.ZERO; //收款对账金额
            BigDecimal amountNotReceivableTotalGroup = BigDecimal.ZERO; //累计应收未收金额

            if(Collections3.isNotEmpty(group.getChildList()))
            {
                for(Company company : group.getChildList())
                {
                    BigDecimal unreconciledAmountTotalCompany = BigDecimal.ZERO; //累计未对账（收款）
                    BigDecimal amountReceivableCompany = BigDecimal.ZERO; //应收金额
                    BigDecimal amountReceivedCompany = BigDecimal.ZERO; //实收金额
                    BigDecimal recevialAmountCompany = BigDecimal.ZERO; //收款对账金额
                    BigDecimal amountNotReceivableTotalCompany = BigDecimal.ZERO; //累计应收未收金额
                    if(Collections3.isNotEmpty(company.getChildList()))
                    {
                        for(Company dept : company.getChildList())
                        {
                            BigDecimal unreconciledAmountTotal = BigDecimal.ZERO; //累计未对账（收款）
                            BigDecimal amountReceivable = BigDecimal.ZERO; //应收金额
                            BigDecimal amountReceived = BigDecimal.ZERO; //实收金额
                            BigDecimal recevialAmount = BigDecimal.ZERO; //收款对账金额
                            BigDecimal amountNotReceivableTotal = BigDecimal.ZERO; //累计应收未收金额
                            //累计未对账金额
                            if (Collections3.isNotEmpty(notReconciliationList))
                            {
                                for(AccountsReceivable notReconciliation : notReconciliationList)
                                {
                                    if(!checkFlag(company.getRecordId(),notReconciliation.getCompanyId()))
                                    {
                                        continue;
                                    }
                                    if(!checkFlag(dept.getRecordId(),notReconciliation.getGroupOrgId()))
                                    {
                                        continue;
                                    }
                                    unreconciledAmountTotal = unreconciledAmountTotal.add(notReconciliation.getUnreconciledAmountTotal());
                                }
                            }
                            //公司累计应收未收金额
                            if (Collections3.isNotEmpty(receivableNotList))
                            {
                                for(AccountsReceivable receivableNot : receivableNotList)
                                {
                                    if(!checkFlag(company.getRecordId(),receivableNot.getCompanyId()))
                                    {
                                        continue;
                                    }
                                    if(!checkFlag(dept.getRecordId(),receivableNot.getGroupOrgId()))
                                    {
                                        continue;
                                    }
                                    amountNotReceivableTotal = amountNotReceivableTotal.add(receivableNot.getAmountNotReceivableTotal());
                                }
                            }
                            //公司对账金额
                            if (Collections3.isNotEmpty(collectionList))
                            {
                                for(AccountsReceivable collectionCompany : collectionList)
                                {
                                    if(!checkFlag(company.getRecordId(),collectionCompany.getCompanyId()))
                                    {
                                        continue;
                                    }
                                    if(!checkFlag(dept.getRecordId(),collectionCompany.getGroupOrgId()))
                                    {
                                        continue;
                                    }
                                    recevialAmount = recevialAmount.add(collectionCompany.getAmount());
                                }
                            }
                            //公司应收、已收金额
                            if (Collections3.isNotEmpty(amountReceivableList))
                            {
                                for(AccountsReceivable obj : amountReceivableList)
                                {
                                    if(!checkFlag(company.getRecordId(),obj.getCompanyId()))
                                    {
                                        continue;
                                    }
                                    if(!checkFlag(dept.getRecordId(),obj.getGroupOrgId()))
                                    {
                                        continue;
                                    }
                                    amountReceivable = amountReceivable.add(obj.getAmountReceivable());
                                    amountReceived = amountReceived.add(obj.getAmountReceived());
                                }
                            }
                            dept.setUnreconciledAmountTotal(unreconciledAmountTotal);
                            dept.setAmountNotReceivableTotal(amountNotReceivableTotal);
                            dept.setRecevialAmount(recevialAmount);
                            dept.setAmountReceivable(amountReceivable);
                            dept.setAmountReceived(amountReceived);

                            unreconciledAmountTotalCompany = unreconciledAmountTotalCompany.add(unreconciledAmountTotal);
                            amountReceivableCompany = amountReceivableCompany.add(amountReceivable);
                            amountReceivedCompany = amountReceivedCompany.add(amountReceived);
                            recevialAmountCompany = recevialAmountCompany.add(recevialAmount);
                            amountNotReceivableTotalCompany = amountNotReceivableTotalCompany.add(amountNotReceivableTotal);
                        }
                    }

                    company.setUnreconciledAmountTotal(unreconciledAmountTotalCompany);
                    company.setAmountNotReceivableTotal(amountNotReceivableTotalCompany);
                    company.setRecevialAmount(amountReceivedCompany);
                    company.setAmountReceivable(amountReceivedCompany);
                    company.setAmountReceived(recevialAmountCompany);

                    unreconciledAmountTotalGroup = unreconciledAmountTotalGroup.add(unreconciledAmountTotalCompany);
                    amountReceivableGroup = amountReceivableGroup.add(amountReceivedCompany);
                    amountReceivedGroup = amountReceivedGroup.add(amountReceivedCompany);
                    recevialAmountGroup = recevialAmountGroup.add(recevialAmountCompany);
                    amountNotReceivableTotalGroup = amountNotReceivableTotalGroup.add(amountNotReceivableTotalCompany);
                }
            }
            group.setUnreconciledAmountTotal(unreconciledAmountTotalGroup);
            group.setAmountNotReceivableTotal(amountNotReceivableTotalGroup);
            group.setRecevialAmount(amountReceivableGroup);
            group.setAmountReceivable(amountReceivedGroup);
            group.setAmountReceived(recevialAmountGroup);
        }
        return dataList;
    }

    public Page<AccountsReceivable> getCollectionDetailDataList(AccountsReceivable accountsReceivable,HttpServletRequest request, HttpServletResponse response,Integer type)
    {
        if (accountsReceivable == null)
        {
            return null;
        }
        Page<AccountsReceivable> page = new Page<>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(accountsReceivable.getPageNo()))
        {
            pageNo = Integer.valueOf(accountsReceivable.getPageNo());
        }
        if (StringUtils.isNotBlank(accountsReceivable.getPageSize()))
        {
            pageSize = Integer.valueOf(accountsReceivable.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            accountsReceivable.setPage(page);
        }
        List<AccountsReceivable> collectionDetailDataList = new ArrayList<>();
        if (StringUtils.isNotBlank(accountsReceivable.getType()))
        {
            switch (accountsReceivable.getType())
            {
                // 累计未对账明细
                case "1":
                    collectionDetailDataList = accountsReceivableDao.getNotReconciliationDetailList(accountsReceivable);
                    break;
                // 应收未收明细
                case "2":
                    collectionDetailDataList = accountsReceivableDao.getReceivableNotDetailList(accountsReceivable);
                    break;
                // 收款对账明细
                case "3":
                    collectionDetailDataList = accountsReceivableDao.getCollectionCompanyDetailList(accountsReceivable);
                    break;
                // 应收明细
                case "4":
                    collectionDetailDataList = accountsReceivableDao.getAmountReceivableDetailList(accountsReceivable);
                    break;
                // 实收明细
                case "5":
                    collectionDetailDataList = accountsReceivableDao.getAmountReceivableDetailList(accountsReceivable);
                    break;
            }
        }
        page.setList(collectionDetailDataList);
        return page;
    }

    public List<Company> handDataList(List<Hr_DepartMent> groupList,List<Company> erpCompanyList,List<GroupOrgRelation> deptList)
    {
        List<Company> dataList = Lists.newArrayList();
        for(Hr_DepartMent obj : groupList)
        {
            Company group = new Company();
            group.setRecordId(obj.getRecordId());
            group.setName(obj.getDepartmentName());
            group.setChildList(Lists.newArrayList());
            if (Collections3.isNotEmpty(erpCompanyList))
            {
                for(Company erpCompany : erpCompanyList)
                {
                    erpCompany.setChildList(Lists.newArrayList());
                    for (GroupOrgRelation depart : deptList)
                    {
                        Company dept = new Company();
                        dept.setRecordId(depart.getGroupOrgId());
                        dept.setName(depart.getGroupOrgName());
                        dept.setCompanyId(erpCompany.getRecordId());
                        erpCompany.getChildList().add(dept.clone());
                    }
                    group.getChildList().add(erpCompany.clone());
                }
            }
            dataList.add(group);
        }
        return dataList;
    }

    //付款集合
    public List<Company> getPayMentList(AccountsPayable accountsPayable)
    {
        List<Company> dataList = Lists.newArrayList();

        //获取集团层
        Hr_DepartMent query = new Hr_DepartMent();
        query.setGroupId(accountsPayable.getGroupId());
        query.setDepartmentLevel("1");
        List<Hr_DepartMent> groupList = oa_dao.getGroupList(query);
        if(Collections3.isEmpty(groupList))
        {
            return null;
        }
        //获取公司数据
        List<Company> erpCompanyList = companyDao.getErpCompanyList(accountsPayable.getCompanyIdList());
        //部门
        List<GroupOrgRelation> deptList = getdeptList(accountsPayable.getGroupDepartId());
        if(Collections3.isEmpty(deptList))
        {
            deptList = Lists.newArrayList();
        }
        GroupOrgRelation groupOrgRelation = new GroupOrgRelation();
        groupOrgRelation.setGroupOrgId("99");
        groupOrgRelation.setGroupOrgName("其他");
        deptList.add(groupOrgRelation);

        // 赋值三层数据
        dataList = handDataList(groupList,erpCompanyList,deptList);

        Integer innerFlag = 1;
        // 单个公司不查终端
        if(null != accountsPayable && Collections3.isNotEmpty(accountsPayable.getCompanyIdList())
                && accountsPayable.getCompanyIdList().size() == 1)
        {
            innerFlag = 0;
        }
        accountsPayable.setInnerFlag(innerFlag);

        //累计未对账(付款)金额
        List<AccountsPayable> paymentReconciledTotalList = accountsPayableDao.getPaymentReconciledTotalList(accountsPayable);

        //累计应付未付金额
        List<AccountsPayable> unpaidPayableTotalList = accountsPayableDao.getUnpaidPayableTotalList(accountsPayable);

        //付款对账金额
        List<AccountsPayable> paymentReconciliationList = accountsPayableDao.getPaymentReconciliationList(accountsPayable);

        //付款应付、实付
        List<AccountsPayable> amountPayableList = accountsPayableDao.getAmountPayableList(accountsPayable);

        for(Company group : dataList)
        {
              BigDecimal paymentReconciledGroup = BigDecimal.ZERO; //累计未对账（付款）集团
              BigDecimal unpaidPayableGroup = BigDecimal.ZERO; //累计应付未付金额 集团
              BigDecimal paymentReconciliationGroup = BigDecimal.ZERO; //付款对账金额 集团
              BigDecimal amountPayableGroup = BigDecimal.ZERO; //应付金额 集团
              BigDecimal amountPaidGroup = BigDecimal.ZERO; //已付金额 集团

            if(Collections3.isNotEmpty(group.getChildList()))
            {
                for(Company company : group.getChildList())
                {
                    BigDecimal paymentReconciledCompany = BigDecimal.ZERO; //累计未对账（付款）
                    BigDecimal unpaidPayableCompany = BigDecimal.ZERO; //累计应付未付金额
                    BigDecimal paymentReconciliationCompany = BigDecimal.ZERO; //付款对账金额
                    BigDecimal amountPayableCompany = BigDecimal.ZERO; //应付金额
                    BigDecimal amountPaidCompany = BigDecimal.ZERO; //已付金额

                    if(Collections3.isNotEmpty(company.getChildList()))
                    {
                        for(Company dept : company.getChildList())
                        {
                            BigDecimal paymentReconciledTotal = BigDecimal.ZERO; //累计未对账（付款）
                            BigDecimal unpaidPayableTotal = BigDecimal.ZERO; //累计应付未付金额
                            BigDecimal paymentReconciliation = BigDecimal.ZERO; //付款对账金额
                            BigDecimal amountPayable = BigDecimal.ZERO; //应付金额
                            BigDecimal amountPaid = BigDecimal.ZERO; //已付金额

                            //累计未对账金额
                            if (Collections3.isNotEmpty(paymentReconciledTotalList))
                            {
                                for(AccountsPayable paymentReconcil : paymentReconciledTotalList)
                                {
                                    if(!checkFlag(company.getRecordId(),paymentReconcil.getCompanyId()))
                                    {
                                        continue;
                                    }
                                    if(!checkFlag(dept.getRecordId(),paymentReconcil.getGroupOrgId()))
                                    {
                                        continue;
                                    }
                                    paymentReconciledTotal = paymentReconciledTotal.add(paymentReconcil.getPaymentReconciledTotal());
                                }
                            }
                            //公司累计应收未收金额
                            if (Collections3.isNotEmpty(unpaidPayableTotalList))
                            {
                                for(AccountsPayable unpaid : unpaidPayableTotalList)
                                {
                                    if(!checkFlag(company.getRecordId(),unpaid.getCompanyId()))
                                    {
                                        continue;
                                    }
                                    if(!checkFlag(dept.getRecordId(),unpaid.getGroupOrgId()))
                                    {
                                        continue;
                                    }
                                    unpaidPayableTotal = unpaidPayableTotal.add(unpaid.getUnpaidPayableTotal());
                                }
                            }
                            //公司对账金额
                            if (Collections3.isNotEmpty(paymentReconciliationList))
                            {
                                for(AccountsPayable payment : paymentReconciliationList)
                                {
                                    if(!checkFlag(company.getRecordId(),payment.getCompanyId()))
                                    {
                                        continue;
                                    }
                                    if(!checkFlag(dept.getRecordId(),payment.getGroupOrgId()))
                                    {
                                        continue;
                                    }
                                    paymentReconciliation = paymentReconciliation.add(payment.getPaymentReconciliation());
                                }
                            }
                            //公司应收、已收金额
                            if (Collections3.isNotEmpty(amountPayableList))
                            {
                                for(AccountsPayable amountPay : amountPayableList)
                                {
                                    if(!checkFlag(company.getRecordId(),amountPay.getCompanyId()))
                                    {
                                        continue;
                                    }
                                    if(!checkFlag(dept.getRecordId(),amountPay.getGroupOrgId()))
                                    {
                                        continue;
                                    }
                                    amountPayable = amountPayable.add(amountPay.getAmountPayable());
                                    amountPaid = amountPaid.add(amountPay.getAmountPaid());
                                }
                            }
                            dept.setPaymentReconciledTotal(paymentReconciledTotal);
                            dept.setUnpaidPayableTotal(unpaidPayableTotal);
                            dept.setPaymentReconciliation(paymentReconciliation);
                            dept.setAmountPayable(amountPayable);
                            dept.setAmountPaid(amountPaid);


//                            paymentReconciledCompany = paymentReconciledCompany.add(paymentReconciledTotal);
//                            unpaidPayableCompany = unpaidPayableCompany.add(unpaidPayableTotal);
                            paymentReconciliationCompany = paymentReconciliationCompany.add(paymentReconciliation);
                            amountPayableCompany = amountPayableCompany.add(amountPayable);
                            amountPaidCompany = amountPaidCompany.add(amountPaid);
                        }
                    }
                    //累计未对账金额
                    if (Collections3.isNotEmpty(paymentReconciledTotalList))
                    {
                        for(AccountsPayable paymentReconcil : paymentReconciledTotalList)
                        {
                            if(!checkFlag(company.getRecordId(),paymentReconcil.getCompanyId()))
                            {
                                continue;
                            }
                            paymentReconciledCompany = paymentReconciledCompany.add(paymentReconcil.getPaymentReconciledTotal());
                        }
                    }
                    //公司累计应收未收金额
                    if (Collections3.isNotEmpty(unpaidPayableTotalList))
                    {
                        for(AccountsPayable unpaid : unpaidPayableTotalList)
                        {
                            if(!checkFlag(company.getRecordId(),unpaid.getCompanyId()))
                            {
                                continue;
                            }
                            unpaidPayableCompany = unpaidPayableCompany.add(unpaid.getUnpaidPayableTotal());
                        }
                    }

                    company.setPaymentReconciledTotal(paymentReconciledCompany);
                    company.setUnpaidPayableTotal(unpaidPayableCompany);
                    company.setPaymentReconciliation(paymentReconciliationCompany);
                    company.setAmountPayable(amountPayableCompany);
                    company.setAmountPaid(amountPaidCompany);


                    paymentReconciledGroup = paymentReconciledGroup.add(paymentReconciledCompany);
                    unpaidPayableGroup = unpaidPayableGroup.add(unpaidPayableCompany);
                    paymentReconciliationGroup = paymentReconciliationGroup.add(paymentReconciliationCompany);
                    amountPayableGroup = amountPayableGroup.add(amountPayableCompany);
                    amountPaidGroup = amountPaidGroup.add(amountPaidCompany);
                }
            }
            group.setPaymentReconciledTotal(paymentReconciledGroup);
            group.setUnpaidPayableTotal(unpaidPayableGroup);
            group.setPaymentReconciliation(paymentReconciliationGroup);
            group.setAmountPayable(amountPayableGroup);
            group.setAmountPaid(amountPaidGroup);
        }
        return dataList;
    }

    //获取付款明细数据
    public Page<AccountsPayable> getPaymentDetailDataList(AccountsPayable accountsPayable,HttpServletRequest request, HttpServletResponse response,Integer type)
    {
        if (accountsPayable == null)
        {
            return null;
        }
        Page<AccountsPayable> page = new Page<>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(accountsPayable.getPageNo()))
        {
            pageNo = Integer.valueOf(accountsPayable.getPageNo());
        }
        if (StringUtils.isNotBlank(accountsPayable.getPageSize()))
        {
            pageSize = Integer.valueOf(accountsPayable.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            accountsPayable.setPage(page);
        }
        List<AccountsPayable> paymentDetailDataList = new ArrayList<>();
        if (StringUtils.isNotBlank(accountsPayable.getType()))
        {
            switch (accountsPayable.getType())
            {
                case "1":
                    //未对账(付款)明细
                    paymentDetailDataList = accountsPayableDao.getPaymentReconciledDetailList(accountsPayable);
                    break;
                case "2":
                    //应付未付明细
                    paymentDetailDataList = accountsPayableDao.getUnpaidPayableDetailList(accountsPayable);
                    break;
                case "3":
                    //对账金额
                    paymentDetailDataList = accountsPayableDao.getPaymentReconciliationDetailList(accountsPayable);
                    break;
                case "4":
                    // 应收明细
                    paymentDetailDataList = accountsPayableDao.getAmountPayableDetailList(accountsPayable);
                    break;
                case "5":
                    // 实收明细
                    paymentDetailDataList = accountsPayableDao.getAmountPayableDetailList(accountsPayable);
                    break;
            }
        }
        page.setList(paymentDetailDataList);
        return page;
    }



    //财务总账应收付单据
    public Page<Hr_ReceiptPayment> getReceiptPaymentList(Hr_ReceiptPayment hr_receiptPayment,HttpServletRequest request,
                                                         HttpServletResponse response, Integer type)
    {
        Page<Hr_ReceiptPayment> page = new Page<Hr_ReceiptPayment>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;

        if (StringUtils.isNotBlank(hr_receiptPayment.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_receiptPayment.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_receiptPayment.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_receiptPayment.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_receiptPayment.setPage(page);
        }
        List<Hr_ReceiptPayment> receiptPaymentList = hr_certificateDao.getReceiptPaymentList(hr_receiptPayment);
        page.setList(receiptPaymentList);
        return page;
    }

    @Transactional(readOnly = false)
    public String delReceiptPayment(Hr_ReceiptPayment hr_receiptPayment)
    {
        hr_certificateDao.delReceiptPayment(hr_receiptPayment);
        return "success";
    }

    @Transactional(readOnly = false)
    public String addOrUpdateReceiptPayment(Hr_ReceiptPayment hr_receiptPayment)
    {
        if (null != hr_receiptPayment)
        {
            if (StringUtils.isNotBlank(hr_receiptPayment.getRecordId()))
            {
                hr_certificateDao.updReceiptPayment(hr_receiptPayment);
            }else{
                hr_certificateDao.addReceiptPayment(hr_receiptPayment);
            }
        }
        return "success";
    }

    public List<DictValue> getDocumentTypeList()
    {
        String itemId = "147";
        String companyId = "17";
        return dictValueDao.getDocumentTypeList(itemId,companyId);
    }

    //在线成本
    public List<Company> getOnlineInventoryList(Hr_ReceiptPayment hr_receiptPayment)
    {
        List<Company> dataList = Lists.newArrayList();
        //获取集团层
        Hr_DepartMent query = new Hr_DepartMent();
        query.setGroupId(hr_receiptPayment.getGroupId());
        query.setDepartmentLevel("1");
        List<Hr_DepartMent> groupList = oa_dao.getGroupList(query);
        if(Collections3.isEmpty(groupList))
        {
            return dataList;
        }
        List<Company> erpCompanyListT = companyDao.getErpCompanyList(hr_receiptPayment.getCompanyIdList());

        List<Company> erpCompanyList = Lists.newArrayList();
        for(Company company : erpCompanyListT)
        {
            if (company.getRecordId().equals("17"))
            {
                erpCompanyList.add(company);
                break;
            }
        }
        //集团克隆公司数据
        for (Hr_DepartMent obj : groupList)
        {
            Company group = new Company();
            group.setRecordId(obj.getRecordId());
            group.setName(obj.getDepartmentName());
            group.setChildList(Lists.newArrayList());
            if (Collections3.isNotEmpty(erpCompanyList))
            {
                for(Company erpCompany : erpCompanyList)
                {
                    group.getChildList().add(erpCompany.clone());
                }
            }
            dataList.add(group);
        }

        for(Company groupObj : dataList)
        {
            if (Collections3.isNotEmpty(erpCompanyList))
            {
                BigDecimal materialFirstInOutTotalTaxGroup = BigDecimal.ZERO; //先进先出金额(含税)
                BigDecimal weightedAmountsTaxGroup = BigDecimal.ZERO; //加权金额(含税)
                for(Company erpCompanyObj : erpCompanyList)
                {
                    BigDecimal materialFirstInOutTotalTaxCompany = BigDecimal.ZERO;//先进先出金额(含税)
                    BigDecimal weightedAmountsTaxCompany = BigDecimal.ZERO;//加权金额(含税)

                    List<DictValue> dictValueMatList = new ArrayList<>();// 物料类型
                    List<DictValue> dictValueList = DictUtils.getValuesByItem(CommonEnums.DictItemEnum.MATERIAL_TYPE_STORE,erpCompanyObj);
                    if(Collections3.isEmpty(dictValueList))
                    {
                        continue;
                    }
                    for (DictValue dictValue : dictValueList) {
                        if (!dictValue.getValue().equals(ConstKey.MATERIAL_REMAIN)) {
                            dictValueMatList.add(dictValue);
                        }
                    }
                    RawmaterialStock rawmaterialStock = new RawmaterialStock();
                    rawmaterialStock.setInoutTimeStartQr(hr_receiptPayment.getSentTimeStartQr());
                    rawmaterialStock.setInoutTimeEndQr(hr_receiptPayment.getSentTimeEndQr());
                    rawmaterialStock.setCompany(erpCompanyObj);

                    MaterialTimeUtils materialTimeUtils = new MaterialTimeUtils();
                    List<RawmaterialStock> materialLlist =  materialTimeUtils.dealAllPlace(rawmaterialStock,erpCompanyObj);
                    erpCompanyObj.setMaterialLlist(materialLlist);

                    for(DictValue dictValue : dictValueMatList)
                    {
                        BigDecimal materialFirstInOutTotalTax = BigDecimal.ZERO; //先进先出金额(含税)
                        BigDecimal materialFirstInOutTotal = BigDecimal.ZERO; //先进先出金额(不含税)
                        BigDecimal weightedAmountsTax = BigDecimal.ZERO; //加权金额(含税)
                        BigDecimal weightedAmounts = BigDecimal.ZERO; //加权金额(不含税)
                        for(RawmaterialStock stock : materialLlist)
                        {
                            if (null == stock.getFirstInFirstAmountTotal())
                            {
                                stock.setFirstInFirstAmountTotal(BigDecimal.ZERO);
                            }
                            if (StringUtils.isNotBlank(dictValue.getValue()) && StringUtils.isNotBlank(stock.getMaterialTypeVal())
                                    && dictValue.getValue().equals(stock.getMaterialTypeVal()))
                            {
                                //先进先出金额(含税)
                                BigDecimal firstInFirstAmountTotalTax = (stock.getFirstInFirstAmountTotalTax() == null || stock.getFirstInFirstAmountTotalTax().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO : stock.getFirstInFirstAmountTotalTax();

                                materialFirstInOutTotalTax = materialFirstInOutTotalTax.add(firstInFirstAmountTotalTax);

                                //先进先出金额(不含税)
                                BigDecimal firstInFirstAmountTotal = (stock.getFirstInFirstAmountTotal() == null || stock.getFirstInFirstAmountTotal().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO : stock.getFirstInFirstAmountTotal();

                                materialFirstInOutTotal = materialFirstInOutTotal.add(firstInFirstAmountTotal);

                                //加权金额(含税)
                                BigDecimal cost = (stock.getCost() == null || stock.getCost().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO : stock.getCost();

                                weightedAmountsTax = weightedAmountsTax.add(cost);

                                //加权金额(不含税)
                                BigDecimal unTaxDescriptCost = (stock.getUnTaxDescriptCost() == null || stock.getUnTaxDescriptCost().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO : stock.getUnTaxDescriptCost();

                                weightedAmounts = weightedAmounts.add(unTaxDescriptCost);
                            }
                        }
                        dictValue.setMaterialFirstInOutTotalTax(materialFirstInOutTotalTax);
                        dictValue.setMaterialFirstInOutTotal(materialFirstInOutTotal);
                        dictValue.setWeightedAmountsTax(weightedAmountsTax);
                        dictValue.setWeightedAmounts(weightedAmounts);

                        materialFirstInOutTotalTaxCompany = materialFirstInOutTotalTaxCompany.add(dictValue.getMaterialFirstInOutTotalTax());
                        weightedAmountsTaxCompany = weightedAmountsTaxCompany.add(dictValue.getWeightedAmountsTax());
                    }
                    erpCompanyObj.setMaterialTypeList(dictValueMatList);
                    erpCompanyObj.setMaterialFirstInOutTotalTax(materialFirstInOutTotalTaxCompany);
                    erpCompanyObj.setWeightedAmountsTax(weightedAmountsTaxCompany);
                    materialFirstInOutTotalTaxGroup = materialFirstInOutTotalTaxGroup.add(materialFirstInOutTotalTaxCompany);
                    weightedAmountsTaxGroup = weightedAmountsTaxGroup.add(weightedAmountsTaxCompany);
                }
                groupObj.setMaterialFirstInOutTotalTax(materialFirstInOutTotalTaxGroup);
                groupObj.setWeightedAmountsTax(weightedAmountsTaxGroup);
                groupObj.setChildList(erpCompanyList);
            }
        }
        return dataList;
    }

    //在线成本明细
    public Page<RawmaterialStock> getOnlineInventoryDetailList(RawmaterialStock rawmaterialStock,HttpServletRequest request,
                                                               HttpServletResponse response, Integer type)
    {
        Page<RawmaterialStock> page = new Page<RawmaterialStock>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;

        if (StringUtils.isNotBlank(rawmaterialStock.getPageNo()))
        {
            pageNo = Integer.valueOf(rawmaterialStock.getPageNo());
        }
        if (StringUtils.isNotBlank(rawmaterialStock.getPageSize()))
        {
            pageSize = Integer.valueOf(rawmaterialStock.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            rawmaterialStock.setPage(page);
        }
        if (rawmaterialStock == null || Collections3.isEmpty(rawmaterialStock.getChildList()))
        {
            return null;
        }
        List<RawmaterialStock> materialList = Lists.newArrayList();
        for(Company companyMessage : rawmaterialStock.getChildList())
        {
            if (rawmaterialStock.getSelectStatus().equals("1"))
            {
                materialList.addAll(companyMessage.getMaterialLlist());
            }else if (rawmaterialStock.getSelectStatus().equals("2") || rawmaterialStock.getSelectStatus().equals("3"))
            {
                if (rawmaterialStock.getErpCompanyId().equals(companyMessage.getRecordId()))
                {
                    materialList = companyMessage.getMaterialLlist();
                }
            }
        }
        List<RawmaterialStock> onlineInventoryDetailList = new ArrayList<>();
        switch (rawmaterialStock.getSelectStatus())
        {
            case "1":
                onlineInventoryDetailList = materialList;
                break;
            case "2":
                onlineInventoryDetailList = materialList;
                break;
            case "3":
                if (Collections3.isNotEmpty(materialList)){
                    for(RawmaterialStock stock : materialList)
                    {
                        if (StringUtils.isNotBlank(stock.getMaterialTypeVal()) && StringUtils.isNotBlank(rawmaterialStock.getMaterialTypeVal())
                                && stock.getMaterialTypeVal().equals(rawmaterialStock.getMaterialTypeVal()))
                        {
                            onlineInventoryDetailList.add(stock);
                        }
                    }
                }
                break;
        }
        Integer num = (pageNo - 1) * pageSize;
        page.setList(Lists.newArrayList());
        for(int i = num;i < onlineInventoryDetailList.size();i++)
        {
            if(page.getList().size() == pageSize)
            {
                break;
            }
            page.getList().add(onlineInventoryDetailList.get(i));
        }
        page.setCount(onlineInventoryDetailList.size());
        return page;
    }

    //已用成本
    public List<Company> getSpentCostList(Hr_ReceiptPayment hr_receiptPayment)
    {
        List<Company> dataList = Lists.newArrayList();

        //获取集团层
        Hr_DepartMent query = new Hr_DepartMent();
        query.setGroupId("3");
        query.setDepartmentLevel("1");
        List<Hr_DepartMent> groupList = oa_dao.getGroupList(query);
        if(Collections3.isEmpty(groupList))
        {
            return dataList;
        }

        //公司
        List<Company> erpCompanyList = companyDao.getErpCompanyList(hr_receiptPayment.getCompanyIdList());

        //获取各公司的投料领料成本
        List<Hr_ReceiptPayment> spentCostList = hr_certificateDao.getSpentCostList(hr_receiptPayment);

        //集团克隆公司数据
        for (Hr_DepartMent obj : groupList)
        {
            Company group = new Company();
            group.setRecordId(obj.getRecordId());
            group.setName(obj.getDepartmentName());
            group.setChildList(Lists.newArrayList());
            if (Collections3.isNotEmpty(erpCompanyList))
            {
                for (Company companyMessage : erpCompanyList)
                {
                    group.getChildList().add(companyMessage.clone());
                }
            }
            dataList.add(group);
        }

        for(Company groupObj : dataList)
        {
            if (Collections3.isNotEmpty(groupObj.getChildList()))
            {
                BigDecimal feedingAverageAmontGroup = BigDecimal.ZERO;
                BigDecimal feedingFirstInOutAmountGroup = BigDecimal.ZERO;
                BigDecimal materialAverageAmontGroup = BigDecimal.ZERO;
                BigDecimal materialFirstInOutAmountGroup = BigDecimal.ZERO;
                BigDecimal inventoryAverageAmountGroup = BigDecimal.ZERO;
                BigDecimal inventoryFirstInOutAmountGroup = BigDecimal.ZERO;
                BigDecimal inventoryLossAverageAmountGroup = BigDecimal.ZERO;
                BigDecimal inventoryLossFirstInOutAmountGroup = BigDecimal.ZERO;
                for(Company com : groupObj.getChildList())
                {
                    if (Collections3.isNotEmpty(spentCostList))
                    {
                        for (Hr_ReceiptPayment spentCost : spentCostList)
                        {
                            if (spentCost == null)
                            {
                                continue;
                            }
                            if (StringUtils.isNotBlank(com.getRecordId()) && StringUtils.isNotBlank(spentCost.getCompanyId())
                                    && com.getRecordId().equals(spentCost.getCompanyId()))
                            {
                                com.setFeedingAverageAmontTotal(spentCost.getFeedingAverageAmontTotal());
                                com.setFeedingFirstInOutAmountTotal(spentCost.getFeedingFirstInOutAmountTotal());
                                com.setMaterialAverageAmontTotal(spentCost.getMaterialAverageAmontTotal());
                                com.setMaterialFirstInOutAmountTotal(spentCost.getMaterialFirstInOutAmountTotal());
                                com.setInventoryAverageAmountTotal(spentCost.getInventoryAverageAmountTotal());
                                com.setInventoryFirstInOutAmountTotal(spentCost.getInventoryFirstInOutAmountTotal());
                                com.setInventoryLossAverageAmountTotal(spentCost.getInventoryLossAverageAmountTotal());
                                com.setInventoryLossFirstInOutAmountTotal(spentCost.getInventoryLossFirstInOutAmountTotal());
                            }
                        }
                    }
                    if (com.getFeedingAverageAmontTotal() != null)
                    {
                        feedingAverageAmontGroup = feedingAverageAmontGroup.add(com.getFeedingAverageAmontTotal());
                    }
                    if (com.getFeedingFirstInOutAmountTotal() != null)
                    {
                        feedingFirstInOutAmountGroup = feedingFirstInOutAmountGroup.add(com.getFeedingFirstInOutAmountTotal());
                    }
                    if (com.getMaterialAverageAmontTotal() != null)
                    {
                        materialAverageAmontGroup = materialAverageAmontGroup.add(com.getMaterialAverageAmontTotal());
                    }
                    if (com.getMaterialFirstInOutAmountTotal() != null)
                    {
                        materialFirstInOutAmountGroup = materialFirstInOutAmountGroup.add(com.getMaterialFirstInOutAmountTotal());
                    }
                    if (com.getInventoryAverageAmountTotal() != null)
                    {
                        inventoryAverageAmountGroup = inventoryAverageAmountGroup.add(com.getInventoryAverageAmountTotal());
                    }
                    if (com.getInventoryFirstInOutAmountTotal() != null)
                    {
                        inventoryFirstInOutAmountGroup = inventoryFirstInOutAmountGroup.add(com.getInventoryFirstInOutAmountTotal());
                    }
                    if (com.getInventoryLossAverageAmountTotal() != null)
                    {
                        inventoryLossAverageAmountGroup = inventoryLossAverageAmountGroup.add(com.getInventoryLossAverageAmountTotal());
                    }
                    if (com.getInventoryLossFirstInOutAmountTotal() != null)
                    {
                        inventoryLossFirstInOutAmountGroup = inventoryLossFirstInOutAmountGroup.add(com.getInventoryLossFirstInOutAmountTotal());
                    }
                }
                groupObj.setFeedingAverageAmontTotal(feedingAverageAmontGroup);
                groupObj.setFeedingFirstInOutAmountTotal(feedingFirstInOutAmountGroup);
                groupObj.setMaterialAverageAmontTotal(materialAverageAmontGroup);
                groupObj.setMaterialFirstInOutAmountTotal(materialFirstInOutAmountGroup);
                groupObj.setInventoryAverageAmountTotal(inventoryAverageAmountGroup);
                groupObj.setInventoryFirstInOutAmountTotal(inventoryFirstInOutAmountGroup);
                groupObj.setInventoryLossAverageAmountTotal(inventoryLossAverageAmountGroup);
                groupObj.setInventoryLossFirstInOutAmountTotal(inventoryLossFirstInOutAmountGroup);
            }
        }
        return dataList;
    }
    //已用成本明细
    public Page<RawmaterialStock> getSpentCostDetailList(RawmaterialStock rawmaterialStock,HttpServletRequest request,HttpServletResponse response,Integer type)
    {
        if (rawmaterialStock == null)
        {
            return null;
        }
        Page<RawmaterialStock> page = new Page<RawmaterialStock>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(rawmaterialStock.getPageNo()))
        {
            pageNo = Integer.valueOf(rawmaterialStock.getPageNo());
        }
        if (StringUtils.isNotBlank(rawmaterialStock.getPageSize()))
        {
            pageSize = Integer.valueOf(rawmaterialStock.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            rawmaterialStock.setPage(page);
        }
        List<RawmaterialStock> spentCostDetailList = hr_certificateDao.getSpentCostDetailList(rawmaterialStock);
        page.setList(spentCostDetailList);
        return page;
    }

    //Oa单据
    public List<Company> getOaDocumentList(Hr_ReceiptPayment hr_receiptPayment)
    {
        List<Company> dataList = Lists.newArrayList();

        //获取集团层
        Hr_DepartMent query = new Hr_DepartMent();
        query.setGroupId("3");
        query.setDepartmentLevel("1");
        List<Hr_DepartMent> groupList = oa_dao.getGroupList(query);
        if(Collections3.isEmpty(groupList))
        {
            return null;
        }

        // 获取公司
        query.setDepartmentLevel("2");
        query.setList(hr_receiptPayment.getCompanyIdList()); // 公司多选
        List<Hr_DepartMent> companyList = oa_dao.getGroupList(query);

        // 获取部门
        query.setDepartmentLevel("3");
        query.setList(Lists.newArrayList());
        // query.getList().add(); // 部门单选
        List<Hr_DepartMent> deptList = oa_dao.getGroupList(query);

        // 赋值三层数据
        for(Hr_DepartMent obj : groupList)
        {
            Company group = new Company();
            group.setRecordId(obj.getRecordId());
            group.setName(obj.getDepartmentName());
            group.setChildList(Lists.newArrayList());
            if (Collections3.isNotEmpty(companyList))
            {
                for(Hr_DepartMent obj2 : companyList)
                {
                    if (StringUtils.isNotBlank(obj2.getAllId()))
                    {
                        if(!obj2.getAllId().equals(obj.getRecordId()))
                        {
                            continue;
                        }
                        Company company = new Company();
                        company.setRecordId(obj2.getRecordId());
                        company.setName(obj2.getDepartmentName());
                        company.setCompanyId(obj2.getCompanyId());
                        company.setChildList(Lists.newArrayList());
                        for (Hr_DepartMent obj3 : deptList)
                        {
                            if(!obj3.getAllId().equals(obj2.getRecordId()))
                            {
                                continue;
                            }
                            Company dept = new Company();
                            dept.setRecordId(obj3.getRecordId());
                            dept.setName(obj3.getDepartmentName());
                            company.getChildList().add(dept.clone());
                        }
                        group.getChildList().add(company.clone());
                    }
                }
            }
            dataList.add(group);
        }

        //获取OA单据
        List<Hr_ReceiptPayment> oaDocumentList = hr_certificateDao.getOaDocumentList(hr_receiptPayment);

        for(Company group : dataList)
        {
            BigDecimal purchasingAmountGroup = BigDecimal.ZERO; //采购金额
            BigDecimal reimburseAmountGroup = BigDecimal.ZERO; //报销金额
            BigDecimal loanAmountGroup = BigDecimal.ZERO; //付款金额
            BigDecimal paymentAmountGroup = BigDecimal.ZERO; //借款金额
            BigDecimal bonusAmountGroup = BigDecimal.ZERO; //奖金分配
            for(Company company : group.getChildList())
            {
                BigDecimal purchasingAmountCompany = BigDecimal.ZERO; //采购金额
                BigDecimal reimburseAmountCompany = BigDecimal.ZERO; //报销金额
                BigDecimal loanAmountCompany = BigDecimal.ZERO; //付款金额
                BigDecimal paymentAmountCompany = BigDecimal.ZERO; //借款金额
                BigDecimal bonusAmountCompany = BigDecimal.ZERO; //奖金分配
                for(Company dept : company.getChildList())
                {
                    BigDecimal purchasingAmount = BigDecimal.ZERO; //采购金额
                    BigDecimal reimburseAmount = BigDecimal.ZERO; //报销金额
                    BigDecimal loanAmount = BigDecimal.ZERO; //付款金额
                    BigDecimal paymentAmount = BigDecimal.ZERO; //借款金额
                    BigDecimal bonusAmount = BigDecimal.ZERO; //奖金分配
                    for(Hr_ReceiptPayment oaDocument : oaDocumentList)
                    {
                        if (StringUtils.isNotBlank(oaDocument.getDeptId()) && dept.getRecordId().equals(oaDocument.getDeptId()))
                        {
                            ///采购单
                            if (StringUtils.isNotBlank(oaDocument.getAuditType()) && oaDocument.getAuditType().equals("22002"))
                            {
                                purchasingAmount = purchasingAmount.add(oaDocument.getPurchasingAmount());
                            }else if (StringUtils.isNotBlank(oaDocument.getAuditType()) && oaDocument.getAuditType().equals("22003"))
                            {
                                reimburseAmount = reimburseAmount.add(oaDocument.getReimburseAmount());
                            }else if (StringUtils.isNotBlank(oaDocument.getAuditType()) && oaDocument.getAuditType().equals("22013"))
                            {
                                loanAmount = loanAmount.add(oaDocument.getLoanAmount());
                            }else if (StringUtils.isNotBlank(oaDocument.getAuditType()) && oaDocument.getAuditType().equals("22014"))
                            {
                                paymentAmount = paymentAmount.add(oaDocument.getPaymentAmount());
                            }else if (StringUtils.isNotBlank(oaDocument.getAuditType()) && oaDocument.getAuditType().equals("22024"))
                            {
                                bonusAmount = bonusAmount.add(oaDocument.getBonusAmount());
                            }
                        }
                    }
                    dept.setPurchasingAmount(purchasingAmount);
                    dept.setReimburseAmount(reimburseAmount);
                    dept.setLoanAmount(loanAmount);
                    dept.setPaymentAmount(paymentAmount);
                    dept.setBonusAmount(bonusAmount);

//                    purchasingAmountCompany = purchasingAmountCompany.add(purchasingAmount);
//                    reimburseAmountCompany = reimburseAmountCompany.add(reimburseAmount);
//                    loanAmountCompany = loanAmountCompany.add(loanAmount);
//                    paymentAmountCompany = paymentAmountCompany.add(paymentAmount);
//                    bonusAmountCompany = bonusAmountCompany.add(bonusAmount);
                }
                for(Hr_ReceiptPayment oaDocument : oaDocumentList)
                {
                    if (StringUtils.isNotBlank(oaDocument.getCompanyId()) && StringUtils.isNotBlank(company.getCompanyId())
                            && company.getCompanyId().equals(oaDocument.getCompanyId()))
                    {
                        ///采购单
                        if (StringUtils.isNotBlank(oaDocument.getAuditType()) && oaDocument.getAuditType().equals("22002"))
                        {
                            purchasingAmountCompany = purchasingAmountCompany.add(oaDocument.getPurchasingAmount());
                        }else if (StringUtils.isNotBlank(oaDocument.getAuditType()) && oaDocument.getAuditType().equals("22003"))
                        {
                            reimburseAmountCompany = reimburseAmountCompany.add(oaDocument.getReimburseAmount());
                        }else if (StringUtils.isNotBlank(oaDocument.getAuditType()) && oaDocument.getAuditType().equals("22013"))
                        {
                            loanAmountCompany = loanAmountCompany.add(oaDocument.getLoanAmount());
                        }else if (StringUtils.isNotBlank(oaDocument.getAuditType()) && oaDocument.getAuditType().equals("22014"))
                        {
                            paymentAmountCompany = paymentAmountCompany.add(oaDocument.getPaymentAmount());
                        }else if (StringUtils.isNotBlank(oaDocument.getAuditType()) && oaDocument.getAuditType().equals("22024"))
                        {
                            bonusAmountCompany = bonusAmountCompany.add(oaDocument.getBonusAmount());
                        }
                    }
                }
                company.setPurchasingAmount(purchasingAmountCompany);
                company.setReimburseAmount(reimburseAmountCompany);
                company.setLoanAmount(loanAmountCompany);
                company.setPaymentAmount(paymentAmountCompany);
                company.setBonusAmount(bonusAmountCompany);

                purchasingAmountGroup = purchasingAmountGroup.add(purchasingAmountCompany);
                reimburseAmountGroup = reimburseAmountGroup.add(reimburseAmountCompany);
                loanAmountGroup = loanAmountGroup.add(loanAmountCompany);
                paymentAmountGroup = paymentAmountGroup.add(paymentAmountCompany);
                bonusAmountGroup = bonusAmountGroup.add(bonusAmountCompany);
            }
            group.setPurchasingAmount(purchasingAmountGroup);
            group.setReimburseAmount(reimburseAmountGroup);
            group.setLoanAmount(loanAmountGroup);
            group.setPaymentAmount(paymentAmountGroup);
            group.setBonusAmount(bonusAmountGroup);
        }
        return dataList;
    }

    public Page<Oa_audit> getOaDocumentDetailDataList(Oa_audit audit, HttpServletRequest request, HttpServletResponse response, Integer type)
    {
        if (audit == null)
        {
            return null;
        }
        Page<Oa_audit> page = new Page<Oa_audit>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(audit.getPageNo()))
        {
            pageNo = Integer.valueOf(audit.getPageNo());
        }
        if (StringUtils.isNotBlank(audit.getPageSize()))
        {
            pageSize = Integer.valueOf(audit.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            audit.setPage(page);
        }
        List<Oa_audit> oaDocumentDetailDataList = hr_certificateDao.getOaDocumentDetailDataList(audit);
        page.setList(oaDocumentDetailDataList);
        return page;
    }


    public List<Company> getOtherExpensesList(Hr_ReceiptPayment hr_receiptPayment)
    {
        List<Company> dataList = Lists.newArrayList();

        List<Hr_ReceiptPayment> receiptPaymentList = hr_certificateDao.getReceiptPaymentList(hr_receiptPayment);
        if(Collections3.isEmpty(receiptPaymentList))
        {
            return dataList;
        }

        //获取集团层
        Hr_DepartMent query = new Hr_DepartMent();
        query.setGroupId("3");
        query.setDepartmentLevel("1");
        List<Hr_DepartMent> groupList = oa_dao.getGroupList(query);
        if(Collections3.isEmpty(groupList))
        {
            return null;
        }

        // 获取公司
        query.setDepartmentLevel("2");
        query.setList(hr_receiptPayment.getCompanyIdList()); // 公司多选
        List<Hr_DepartMent> companyList = oa_dao.getGroupList(query);

        // 获取部门
        query.setDepartmentLevel("3");
        query.setList(Lists.newArrayList());
        // query.getList().add(); // 部门单选
        List<Hr_DepartMent> deptList = oa_dao.getGroupList(query);

        // 赋值三层数据
        for(Hr_DepartMent obj : groupList)
        {
            Company group = new Company();
            group.setRecordId(obj.getRecordId());
            group.setName(obj.getDepartmentName());
            group.setChildList(Lists.newArrayList());
            if (Collections3.isNotEmpty(companyList))
            {
                for(Hr_DepartMent obj2 : companyList)
                {
                    if (StringUtils.isNotBlank(obj2.getAllId()))
                    {
                        if(!obj2.getAllId().equals(obj.getRecordId()))
                        {
                            continue;
                        }
                        Company company = new Company();
                        company.setRecordId(obj2.getRecordId());
                        company.setName(obj2.getDepartmentName());
                        company.setChildList(Lists.newArrayList());
                        for (Hr_DepartMent obj3 : deptList)
                        {
                            if(!obj3.getAllId().equals(obj2.getRecordId()))
                            {
                                continue;
                            }
                            Company dept = new Company();
                            dept.setRecordId(obj3.getRecordId());
                            dept.setName(obj3.getDepartmentName());
                            company.getChildList().add(dept.clone());
                        }
                        group.getChildList().add(company.clone());
                    }
                }
            }
            dataList.add(group);
        }
        for(Company group : dataList)
        {
            BigDecimal amountPayableGroup = BigDecimal.ZERO; //应付金额
            BigDecimal amountReceivableGroup = BigDecimal.ZERO; //应收金额
            for(Company company : group.getChildList())
            {
                BigDecimal amountPayableCompany = BigDecimal.ZERO; //应付金额
                BigDecimal amountReceivableCompany = BigDecimal.ZERO; //应收金额
                for(Company dept : company.getChildList())
                {
                    BigDecimal amountPayable = BigDecimal.ZERO; //应付金额
                    BigDecimal amountReceivable = BigDecimal.ZERO; //应收金额
                    for(Hr_ReceiptPayment data : receiptPaymentList)
                    {
                        if(null != data.getAmount())
                        {
                            if(StringUtils.isNotBlank(data.getStatus()))
                            {
                                if("1".equals(data.getStatus()))
                                {
                                    amountReceivable = amountReceivable.add(data.getAmount());
                                }
                                else if("2".equals(data.getStatus()))
                                {
                                    amountPayable = amountPayable.add(data.getAmount());
                                }
                            }
                        }
                    }
                    dept.setAmountReceivable(amountReceivable);
                    dept.setAmountPayable(amountPayable);

                    amountReceivableCompany = amountReceivableCompany.add(amountReceivable);
                    amountPayableCompany = amountPayableCompany.add(amountPayable);

                }
                company.setAmountReceivable(amountReceivableCompany);
                company.setAmountPayable(amountPayableCompany);

                amountReceivableGroup = amountReceivableGroup.add(amountReceivableCompany);
                amountPayableGroup = amountPayableGroup.add(amountPayableCompany);
            }
            group.setAmountReceivable(amountReceivableGroup);
            group.setAmountPayable(amountPayableGroup);
        }
        return dataList;
    }

    //其他费用明细
    public Page<Hr_ReceiptPayment> getOtherExpensesDetailDataList(Hr_ReceiptPayment hr_receiptPayment,HttpServletRequest request,
                                                                  HttpServletResponse response, Integer type)
    {
        if (hr_receiptPayment == null)
        {
            return null;
        }
        Page<Hr_ReceiptPayment> page = new Page<Hr_ReceiptPayment>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(hr_receiptPayment.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_receiptPayment.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_receiptPayment.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_receiptPayment.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_receiptPayment.setPage(page);
        }
        List<Hr_ReceiptPayment> otherExpensesDetailList = new ArrayList<>();
        otherExpensesDetailList = hr_certificateDao.getOtherExpensesDetailDataListT(hr_receiptPayment);
        page.setList(otherExpensesDetailList);
        return page;
    }


    public void setExportCollection(ExportExcel excel, List<AccountsReceivable> list, String[] hearList,String type) {
        for (AccountsReceivable collection : list) {
            int colunm = 0;
            Row row = excel.addRow();
            if (type.equals("1") || type.equals("2"))
            {
                for (String name : hearList) {
                    String val = "";
                    switch (name) {
                        case "客户名称":
                            val = collection.getCustomer().getName();
                            break;
                        case "收款金额":
                            val = (collection.getReceivedAmount() == null || collection.getReceivedAmount().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getReceivedAmount().toString();
                            break;
                        case "送货金额":
                            val = (collection.getSentGoodsValue() == null || collection.getSentGoodsValue().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getSentGoodsValue().toString();
                            break;
                        case "退货金额":
                            val = (collection.getReturnGoodsValue() == null || collection.getReturnGoodsValue().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getReturnGoodsValue().toString();
                            break;
                        case "调整金额":
                            val = (collection.getAdjustValue() == null || collection.getAdjustValue().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getAdjustValue().toString();
                            break;
                        case "未对账(收款)金额":
                            val = (collection.getUnreconciledAmountTotal() == null || collection.getUnreconciledAmountTotal().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getUnreconciledAmountTotal().toString();
                            break;
                        case "应收未收金额":
                            val = (collection.getAmountNotReceivableTotal() == null || collection.getAmountNotReceivableTotal().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getAmountNotReceivableTotal().toString();
                            break;
                    }
                    excel.addCell(row, colunm++, val, 2, null);
                }
            }else if (type.equals("3") || type.equals("4") || type.equals("5"))
            {
                for (String name : hearList) {
                    String val = "";
                    switch (name) {
                        case "客户名称":
                            val = collection.getCustomer().getName();
                            break;
                        case "合同号":
                            val = (collection.getContractNo() == null) ? "" : collection.getContractNo();
                            break;
                        case "产编编号":
                            val = (collection.getCraftNo() == null) ? "" : collection.getCraftNo();
                            break;
                        case "客户订单号":
                            val = (collection.getCustomerPo() == null) ? "" : collection.getCustomerPo();
                            break;
                        case "客户型号":
                            val = (collection.getCustomerModel() == null) ? "" : collection.getCustomerModel();
                            break;
                        case "数量":
                            val = (collection.getQuantity() == null) ? "0" : collection.getQuantity().toString();
                            break;
                        case "单价":
                            val = (collection.getPrice() == null || collection.getPrice().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getPrice().toString();
                            break;
                        case "对账金额":
                            val = (collection.getAmount() == null || collection.getAmount().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getAmount().toString();
                            break;
                        case "应收金额":
                            val = (collection.getAmountReceivable() == null || collection.getAmountReceivable().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getAmountReceivable().toString();
                            break;
                        case "实收金额":
                            val = (collection.getAmountReceived() == null || collection.getAmountReceived().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getAmountReceived().toString();
                            break;
                    }
                    excel.addCell(row, colunm++, val, 2, null);
                }
            }
        }
    }

    public void setExportPayment(ExportExcel excel, List<AccountsPayable> list, String[] hearList,String type) {
        for (AccountsPayable collection : list) {
            int colunm = 0;
            Row row = excel.addRow();
            if (type.equals("1") || type.equals("2"))
            {
                for (String name : hearList) {
                    String val = "";
                    switch (name) {
                        case "供应商名称":
                            val = collection.getSupplier().getName();
                            break;
                        case "已付款总金额":
                            val = (collection.getPaiedAmount() == null || collection.getPaiedAmount().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getPaiedAmount().toString();
                            break;
                        case "收到原材料总金额":
                            val = (collection.getRecvMaterialValue() == null || collection.getRecvMaterialValue().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getRecvMaterialValue().toString();

                            break;
                        case "退回原材料总金额":
                            val = (collection.getRetnMaterialValue() == null || collection.getRetnMaterialValue().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getRetnMaterialValue().toString();
                            break;
                        case "调整金额":
                            val = (collection.getAdjustValue() == null || collection.getAdjustValue().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getAdjustValue().toString();
                            break;
                        case "未对账(付款)金额":
                            val = (collection.getPaymentReconciledTotal() == null || collection.getPaymentReconciledTotal().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getPaymentReconciledTotal().toString();
                            break;
                        case "应付未付金额":
                            val = (collection.getUnpaidPayableTotal() == null || collection.getUnpaidPayableTotal().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getUnpaidPayableTotal().toString();
                            break;
                    }
                    excel.addCell(row, colunm++, val, 2, null);
                }
            }else if (type.equals("3") || type.equals("4") || type.equals("5"))
            {
                for (String name : hearList) {
                    String val = "";
                    switch (name) {
                        case "供应商名称":
                            val = collection.getSupplier().getName();
                            break;
                        case "物料编号":
                            val = (collection.getMaterialNo() == null) ? "" : collection.getMaterialNo();
                            break;
                        case "物料名称":
                            val = (collection.getMaterialName() == null) ? "" : collection.getMaterialName();
                            break;
                        case "规格":
                            val = (collection.getSpecification() == null) ? "" : collection.getSpecification();
                            break;
                        case "数量":
                            val = (collection.getQuantity() == null) ? "0" : collection.getQuantity().toString();
                            break;
                        case "单价":
                            val = (collection.getPrice() == null || collection.getPrice().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getPrice().toString();
                            break;
                        case "对账金额":
                            val = (collection.getPaymentReconciliation() == null || collection.getPaymentReconciliation().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getPaymentReconciliation().toString();
                            break;
                        case "应付金额":
                            val = (collection.getAmountPayable() == null || collection.getAmountPayable().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getAmountPayable().toString();
                            break;
                        case "实付金额":
                            val = (collection.getAmountPaid() == null || collection.getAmountPaid().compareTo(BigDecimal.ZERO) == 0) ? "0" : collection.getAmountPaid().toString();
                            break;
                    }
                    excel.addCell(row, colunm++, val, 2, null);
                }
            }
        }
    }
    public void setExportOtherExpenses(ExportExcel excel, List<Hr_ReceiptPayment> list, String[] hearList) {
        for (Hr_ReceiptPayment otherExpenses : list) {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "公司":
                        val = otherExpenses.getCompanyName();
                        break;
                    case "单据类别":
                        val = (otherExpenses.getDictValue() == null) ? "" : otherExpenses.getDictValue();
                        break;
                    case "类型":
                        val = (otherExpenses.getStatusStr() == null) ? "" : otherExpenses.getStatusStr();

                        break;
                    case "金额":
                        val = (otherExpenses.getAmount() == null || otherExpenses.getAmount().compareTo(BigDecimal.ZERO) == 0) ? "0" : otherExpenses.getAmount().toString();
                        break;
                    case "原因":
                        val = (otherExpenses.getReason() == null) ? "" : otherExpenses.getReason();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }

        }
    }
    public void setExportCollectPayment(ExportExcel excel, List<Hr_CollectPayBill> list, String[] hearList) {
        for (Hr_CollectPayBill collectPayBill : list) {
            List<Hr_CollectPayBill> detailList = collectPayBill.getCollectionPayRecordList(); // 获取明细
            int rowSpan = detailList.size(); // 明细行数

            for (int detailIndex = 0; detailIndex < detailList.size(); detailIndex++) {
                Hr_CollectPayBill detail = detailList.get(detailIndex);
                Row row = excel.addRow(); // 为每一个明细创建新行
                int colunm = 0;

                // 写入收款单和收款明细信息
                for (String name : hearList) {
                    String val = "";
                    switch (name) {
                        // 收款单信息部分
                        case "单据编号":
                            if (detailIndex == 0) { // 只在第一行写入收款单信息
                                String formattedId = "";
                                if (collectPayBill.getDimensionId().equals("1")) {
                                    formattedId = "SKD" + String.format("%08d", Integer.valueOf(collectPayBill.getRecordId()));
                                } else {
                                    formattedId = "FKD" + String.format("%08d", Integer.valueOf(collectPayBill.getRecordId()));
                                }
                                val = formattedId;
                            }
                            break;
                        case "收款方":
                            if (detailIndex == 0) {
                                if (collectPayBill.getDimensionId().equals("1")) {
                                    val = (collectPayBill.getClearOrganizationName() == null) ? "" : collectPayBill.getClearOrganizationName();
                                } else if (collectPayBill.getDimensionId().equals("2")) {
                                    val = (collectPayBill.getCorrespondentName() == null) ? "" : collectPayBill.getCorrespondentName();
                                }
                            }
                            break;
                        case "付款方":
                            if (detailIndex == 0) {
                                if (collectPayBill.getDimensionId().equals("1")) {
                                    val = (collectPayBill.getCorrespondentName() == null) ? "" : collectPayBill.getCorrespondentName();
                                } else if (collectPayBill.getDimensionId().equals("2")) {
                                    val = (collectPayBill.getClearOrganizationName() == null) ? "" : collectPayBill.getClearOrganizationName();
                                }
                            }
                            break;
                        case "金额":
                            if (detailIndex == 0) {
                                val = (collectPayBill.getAmountReceivableHead() == null) ? "0" : collectPayBill.getAmountReceivableHead().toString();
                            }
                            break;
                        case "币别":
                            if (detailIndex == 0) {
                                val = (collectPayBill.getCurrencyName() == null) ? "" : collectPayBill.getCurrencyName();
                            }
                            break;
                        case "表头-备注":
                            if (detailIndex == 0) {
                                val = (collectPayBill.getRemarkHead() == null) ? "" : collectPayBill.getRemarkHead();
                            }
                            break;
                        // 收款明细信息部分
                        case "原始单据":
                            val = (detail != null && detail.getDocumentType() != null) ? detail.getDocumentType() : "";
                            break;
                        case "单据编码":
                            val = (detail != null ? detail.getTypeName() + "-" + detail.getNo() : "");
                            break;
                        case "创建人":
                            val = (detail != null && detail.getCommitName() != null) ? detail.getCommitName() : "";
                            break;
                        case "表体-金额":
                            val = (detail != null && detail.getAmountReceivable() != null) ? detail.getAmountReceivable().toString() : "0";
                            break;
                        case "表体-备注":
                            val = (detail != null && detail.getRemark() != null) ? detail.getRemark() : "";
                            break;
                    }
                    excel.addCell(row, colunm++, val, 2, null);
                }

                // 合并收款单信息的单元格，仅在有多个明细时执行
                if (rowSpan > 1 && detailIndex == 0) {
                    for (int i = 0; i < hearList.length; i++) {
                        // 只合并收款单部分的列
                        if (i < 6) { // 假设收款单信息占前6列
                            excel.mergeCells(row.getRowNum(), row.getRowNum() + rowSpan - 1, i, i);
                        }
                    }
                }
            }
        }
    }

    public void setExportReceivePaid(ExportExcel excel, List<Hr_CollectPayBill> list, String[] hearList) {
        for (Hr_CollectPayBill receivePaid : list) {
            List<Hr_CollectPayBill> detailList = receivePaid.getCollectionPayRecordList(); // 获取明细
            int rowSpan = detailList.isEmpty() ? 1 : detailList.size(); // 明细行数
            for (int detailIndex = 0; detailIndex < rowSpan; detailIndex++) {
                Hr_CollectPayBill detail = detailIndex < detailList.size() ? detailList.get(detailIndex) : null;
                Row row = excel.addRow(); // 为每一个明细创建新行
                int colunm = 0;
                // 写入收款单和收款明细信息
                for (String name : hearList) {
                    String val = "";
                    switch (name) {
                        // 收款单信息部分
                        case "单据编号":
                            if (detailIndex == 0) { // 只在第一行写入收款单信息
                                val = receivePaid.getNo() == null ? "" : receivePaid.getNo();
                            }
                            break;
                        case "单据类型":
                            if (detailIndex == 0) { // 只在第一行写入收款单信息
                                if (receivePaid.getTypeStatus().equals("1")) {
                                    val = receivePaid.getDocumentType() == null ? "" : receivePaid.getDocumentType();
                                }else if (receivePaid.getTypeStatus().equals("2"))
                                {
                                    val = receivePaid.getAuditTypeName() == null ? "" : receivePaid.getAuditTypeName();
                                }
                            }
                            break;
                        case "收款方":
                            if (detailIndex == 0) {
                                if (receivePaid.getTypeStatus().equals("1")) {
                                    val = (receivePaid.getDepartmentName() == null) ? "" : receivePaid.getDepartmentName();
                                } else if (receivePaid.getTypeStatus().equals("2")) {
                                    val = (receivePaid.getTargetName() == null) ? "" : receivePaid.getTargetName();
                                }
                            }
                            break;
                        case "付款方":
                            if (detailIndex == 0) {
                                if (receivePaid.getTypeStatus().equals("1")) {
                                    val = (receivePaid.getTargetName() == null) ? "" : receivePaid.getTargetName();
                                } else if (receivePaid.getTypeStatus().equals("2")) {
                                    val = (receivePaid.getDepartmentName() == null) ? "" : receivePaid.getDepartmentName();
                                }
                            }
                            break;
                        case "来源":
                            if (detailIndex == 0) {
                                val = receivePaid.getTypeName();
                                if (StringUtils.isNotBlank(receivePaid.getCategoryName()))
                                {
                                    val = val + "-" + receivePaid.getCategoryName();
                                }
                            }
                            break;
                        case "表头-金额":
                            if (detailIndex == 0) {
                                val = (receivePaid.getTotalAmount() == null) ? "0" : receivePaid.getTotalAmount().setScale(2,BigDecimal.ROUND_HALF_UP).toString();
                            }
                            break;
                        case "已处理":
                            if (detailIndex == 0) {
                                val = (receivePaid.getReceivedAmount() == null) ? "0" : receivePaid.getReceivedAmount().setScale(2,BigDecimal.ROUND_HALF_UP).toString();
                            }
                            break;
                        case "待处理":
                            if (detailIndex == 0) {
                                val = (receivePaid.getAmount() == null) ? "0" : receivePaid.getAmount().setScale(2,BigDecimal.ROUND_HALF_UP).toString();
                            }
                            break;
                        case "提交人":
                            if (detailIndex == 0) {
                                val = (receivePaid.getCommitName() == null) ? "" : receivePaid.getCommitName();
                            }
                            break;
                        case "终批人":
                            if (detailIndex == 0) {
                                val = (receivePaid.getEmployeeName() == null) ? "" : receivePaid.getEmployeeName();
                            }
                            break;
                        case "终批时间/账期":
                            if (detailIndex == 0) {
                                if (receivePaid.getTypeStatus().equals("1")) {
                                    val = (receivePaid.getPeriod() == null) ? "" : receivePaid.getPeriod();
                                } else if (receivePaid.getTypeStatus().equals("2")) {
                                    val = (receivePaid.getCreatedDate() == null) ? "" : DateUtils.formatDateTime(receivePaid.getCreatedDate());
                                }
                            }
                            break;
                        case "状态":
                            if (detailIndex == 0) {
                                if (receivePaid.getTypeStatus().equals("1")) {
                                    val = receivePaid.getStatus().equals("2001") ? "未收款" : "已收款";
                                } else if (receivePaid.getTypeStatus().equals("2")) {
                                    if (StringUtils.isNotBlank(receivePaid.getStatus()))
                                    {
                                        val = receivePaid.getStatus().equals("1") ? "未付款" : "已付款";
                                    }else{
                                        val = "未付款";
                                    }
                                }
                            }
                            break;
                        // 收款明细信息部分
                        case "结算方式":
                            val = detail != null && detail.getSettlementName() != null ? detail.getSettlementName() : "";
                            break;
                        case "用途":
                            val = detail != null && detail.getCollectPurposeName() != null ? detail.getCollectPurposeName() : "";
                            break;
                        case "表体-金额":
                            val = detail != null && detail.getAmountReceivable() != null ? detail.getAmountReceivable().setScale(2,BigDecimal.ROUND_HALF_UP).toString() : "0";
                            break;
                        case "备注":
                            val = detail != null && detail.getRemark() != null ? detail.getRemark() : "";
                            break;
                        case "创建人":
                            val = detail != null && detail.getCreateName() != null ? detail.getCreateName() : "";
                            break;
                        case "创建时间":
                            val = detail != null && detail.getCreatedDate() != null ? DateUtils.formatDateTime(receivePaid.getCreatedDate()) : "";
                            break;
                    }
                    excel.addCell(row, colunm++, val, 2, null);
                }
                // 合并收款单信息的单元格，仅在有多个明细时执行
                if (rowSpan > 1 && detailIndex == 0) {
                    for (int i = 0; i < hearList.length; i++) {
                        // 只合并收款单部分的列
                        if (i < 12) { // 假设收款单信息占前12列
                            excel.mergeCells(row.getRowNum(), row.getRowNum() + rowSpan - 1, i, i);
                        }
                    }
                }
            }
        }
    }

    //OA单据导出
    public void setExportOaDocument(ExportExcel excel, List<Oa_audit> list, String[] hearList) {
        for (Oa_audit audit : list) {
            int colunm = 0;
            Row row = excel.addRow();
            if (audit.getAuditType().equals("22002"))
            {
                for (String name : hearList) {
                    String val = "";
                    switch (name) {
                        case "审批编号":
                            val = audit.getNo();
                            break;
                        case "名称":
                            val = (audit.getShowName() == null) ? "" : audit.getShowName();
                            break;
                        case "采购物品":
                            val = (audit.getPurchaseName() == null) ? "" : audit.getPurchaseName();
                            break;
                        case "采购数量":
                            val = (audit.getPurchaseNum() == null) ? "0" : audit.getPurchaseNum();
                            break;
                        case "采购价格":
                            val = (audit.getPurchasePrice() == null) ? "0" : audit.getPurchasePrice();
                            break;
                        case "采购事由":
                            val = (audit.getAuditResult() == null) ? "0" : audit.getAuditResult();
                            break;

                    }
                    excel.addCell(row, colunm++, val, 2, null);
                }
            }else if (audit.getAuditType().equals("22003"))
            {
                for (String name : hearList) {
                    String val = "";
                    switch (name) {
                        case "审批编号":
                            val = audit.getNo();
                            break;
                        case "名称":
                            val = (audit.getShowName() == null) ? "" : audit.getShowName();
                            break;
                        case "报销类型":
                            val = (audit.getExpenseTypeName() == null) ? "" : audit.getExpenseTypeName();
                            break;
                        case "报销金额":
                            val = (audit.getMoney() == null) ? "0" : audit.getMoney();
                            break;
                        case "报销原因":
                            val = (audit.getAuditResult() == null) ? "0" : audit.getAuditResult();
                            break;
                    }
                    excel.addCell(row, colunm++, val, 2, null);
                }
            }else if (audit.getAuditType().equals("22013"))
            {
                for (String name : hearList) {
                    String val = "";
                    switch (name) {
                        case "审批编号":
                            val = audit.getNo();
                            break;
                        case "名称":
                            val = (audit.getShowName() == null) ? "" : audit.getShowName();
                            break;
                        case "付款金额":
                            val = (audit.getExpensePrice() == null) ? "" : audit.getExpensePrice();
                            break;
                        case "付款方式":
                            val = (audit.getPayMethod() == null) ? "0" : audit.getPayMethod();
                            break;
                        case "币种":
                            if (StringUtils.isNotBlank(audit.getChildType()))
                            {
                                if (audit.getChildType().equals("1"))
                                {
                                    val = "人民币";
                                }else if (audit.getChildType().equals("2"))
                                {
                                    val = "港币";
                                }else if (audit.getChildType().equals("3"))
                                {
                                    val = "美金";
                                }
                            }
                            break;
                        case "支付对象":
                            val = (audit.getPayObject() == null) ? "0" : audit.getPayObject();
                            break;
                        case "开户行":
                            val = (audit.getBank() == null) ? "0" : audit.getBank();
                            break;
                        case "银行帐号":
                            val = (audit.getBankAccount() == null) ? "0" : audit.getBankAccount();
                            break;
                        case "付款原因":
                            val = (audit.getAuditResult() == null) ? "0" : audit.getAuditResult();
                            break;

                    }
                    excel.addCell(row, colunm++, val, 2, null);
                }
            }else if (audit.getAuditType().equals("22014"))
            {
                for (String name : hearList) {
                    String val = "";
                    switch (name) {
                        case "审批编号":
                            val = audit.getNo();
                            break;
                        case "名称":
                            val = (audit.getShowName() == null) ? "" : audit.getShowName();
                            break;
                        case "借款金额":
                            val = (audit.getExpensePrice() == null) ? "" : audit.getExpensePrice();
                            break;
                        case "币种":
                            if (StringUtils.isNotBlank(audit.getChildType()))
                            {
                                if (audit.getChildType().equals("1"))
                                {
                                    val = "人民币";
                                }else if (audit.getChildType().equals("2"))
                                {
                                    val = "港币";
                                }else if (audit.getChildType().equals("3"))
                                {
                                    val = "美金";
                                }
                            }
                            break;
                        case "借款事由":
                            val = (audit.getAuditResult() == null) ? "0" : audit.getAuditResult();
                            break;

                    }
                    excel.addCell(row, colunm++, val, 2, null);
                }

            }else if (audit.getAuditType().equals("22024"))
            {
                for (String name : hearList) {
                    String val = "";
                    switch (name) {
                        case "审批编号":
                            val = audit.getNo();
                            break;
                        case "名称":
                            val = (audit.getShowName() == null) ? "" : audit.getShowName();
                            break;
                        case "月份":
                            val = (audit.getMonthly() == null) ? "" : audit.getMonthly();
                            break;
                        case "金额":
                            val = (audit.getTotalMoney() == null || audit.getTotalMoney().compareTo(BigDecimal.ZERO) == 0) ? "0" : audit.getTotalMoney().toString();
                            break;
                    }
                    excel.addCell(row, colunm++, val, 2, null);
                }
            }
        }
    }


    //已用成本导出
    public void setExportSpentCostDetail(ExportExcel excel, List<RawmaterialStock> list, String[] hearList) {
        for (RawmaterialStock stock : list) {
            int colunm = 0;
            Row row = excel.addRow();

                for (String name : hearList) {
                    String val = "";
                    switch (name) {
                        case "物料编号":
                            val = (stock.getMaterialNo() == null) ? "" : stock.getMaterialNo();
                            break;
                        case "物料名称":
                            val = (stock.getMaterialName() == null) ? "" : stock.getMaterialName();
                            break;
                        case "规格型号":
                            val = (stock.getSpecification() == null) ? "" : stock.getSpecification();
                            break;
                        case "数量":
                            val = (stock.getQuantity() == null || stock.getQuantity().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getQuantity().toString();
                            break;
                        case "价格":
                            val = (stock.getPrice() == null || stock.getPrice().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getPrice().toString();
                            break;
                        case "投料加权平均金额":
                            val = (stock.getFeedingAverageAmont() == null || stock.getFeedingAverageAmont().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getFeedingAverageAmont().toString();
                            break;
                        case "投料先进先出金额":
                            val = (stock.getFeedingFirstInOutAmount() == null || stock.getFeedingFirstInOutAmount().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getFeedingFirstInOutAmount().toString();
                            break;
                        case "领料加权平均金额":
                            val = (stock.getMaterialAverageAmont() == null || stock.getMaterialAverageAmont().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getMaterialAverageAmont().toString();
                            break;
                        case "领料先进先出金额":
                            val = (stock.getMaterialFirstInOutAmount() == null || stock.getMaterialFirstInOutAmount().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getMaterialFirstInOutAmount().toString();
                            break;
                        case "盘盈加权平均金额":
                            val = (stock.getInventoryAverageAmount() == null || stock.getInventoryAverageAmount().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getInventoryAverageAmount().toString();
                            break;
                        case "盘盈先进先出金额":
                            val = (stock.getInventoryFirstInOutAmount() == null || stock.getInventoryFirstInOutAmount().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getInventoryFirstInOutAmount().toString();
                            break;
                        case "盘亏加权平均金额":
                            val = (stock.getInventoryLossAverageAmount() == null || stock.getInventoryLossAverageAmount().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getInventoryLossAverageAmount().toString();
                            break;
                        case "盘亏先进先出金额":
                            val = (stock.getInventoryLossFirstInOutAmount() == null || stock.getInventoryLossFirstInOutAmount().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getInventoryLossFirstInOutAmount().toString();
                            break;
                    }
                    excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }
    //在线成本导出
    public void setExportOnlineInventoryDetail(ExportExcel excel, List<RawmaterialStock> list, String[] hearList) {
        for (RawmaterialStock stock : list) {
            int colunm = 0;
            Row row = excel.addRow();

            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "物料编号":
                        val = (stock.getMaterialNo() == null) ? "" : stock.getMaterialNo();
                        break;
                    case "物料名称":
                        val = (stock.getMaterialName() == null) ? "" : stock.getMaterialName();
                        break;
                    case "规格型号":
                        val = (stock.getSpecification() == null) ? "" : stock.getSpecification();
                        break;
                    case "库存数量":
                        val = (stock.getQuantity() == null || stock.getQuantity().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getQuantity().toString();
                        break;
                    case "加权平均单价":
                        val = (stock.getPrice() == null || stock.getPrice().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getPrice().toString();
                        break;
                    case "加权平均金额金额":
                        val = (stock.getFirstInFirstAmountTotalTax() == null || stock.getFirstInFirstAmountTotalTax().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getFirstInFirstAmountTotalTax().toString();
                        break;
                    case "先进先出金额":
                        val = (stock.getCost() == null || stock.getCost().compareTo(BigDecimal.ZERO) == 0) ? "0" : stock.getCost().toString();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    public Map<String,Object> getLoadGroupList(Hr_DepartMent hr_DepartMent)
    {
        //集团部门
        Map<String, Object> data = new HashMap<>();
        List<GroupOrgRelation> deptList = CompanyUtil.getInstance().getGroupList();
        if(Collections3.isEmpty(deptList))
        {
            deptList = Lists.newArrayList();
        }
        GroupOrgRelation deptObj = new GroupOrgRelation();
        deptObj.setGroupOrgId("-1");
        deptObj.setGroupOrgName("无");
        deptList.add(deptObj);
        data.put("deptList",deptList);

        //组织架构
        List<Hr_DepartMent> groupList = oa_dao.getGroupList(hr_DepartMent);
        data.put("groupList",groupList);

        // 部门组织架构
        hr_DepartMent.setDepartmentLevel("3");
        List<Hr_DepartMent> deptGroupList = oa_dao.getGroupList(hr_DepartMent);
        data.put("deptGroupList",deptGroupList);

        return data;
    }

    public Map<String,Object> getReceivableManageMap(AccountsReceivable accountsReceivable)
    {
        Map<String,Object> data = new HashMap<>();
        if(null == accountsReceivable || StringUtils.isBlank(accountsReceivable.getType()) || StringUtils.isBlank(accountsReceivable.getRecordId()))
        {
            return data;
        }
        return data;
    }

    public Map<String,Object> getHr_ReportDataList(Hr_ReportData reportData)
    {
        Map<String,Object> data = new HashMap<>();
        if(null == reportData || null == reportData.getModuleType())
        {
            return data;
        }
        Integer innerFlag = 1; // 1合并，2不合并
        String companyId = null; // erp公司id
        String groupDepartId = null; // 集团部门id
        Date startDate = null; // 开始时间
        Date endDate = null; // 结束时间
        String hrDeptId = null; // hr组织架构id

        // 如果是公司等级，获取ERP公司id进行匹配
        if(null != reportData.getArchitectureObj() && StringUtils.isNotBlank(reportData.getArchitectureObj().getDepartmentLevel())
                && "2".equals(reportData.getArchitectureObj().getDepartmentLevel()) && StringUtils.isNotBlank(reportData.getArchitectureObj().getCompanyId()))
        {
            innerFlag = 2;
        }
        if(null != reportData.getYear())
        {
            // 转换为 LocalDate 并格式化输出
            LocalDate firstDayOfYear = LocalDate.of(reportData.getYear(), 1, 1);
            LocalDate lastDayOfYear = LocalDate.of(reportData.getYear(), 12, 31);
            startDate = Date.from(firstDayOfYear.atStartOfDay(ZoneId.systemDefault()).toInstant());
            endDate = Date.from(lastDayOfYear.atStartOfDay(ZoneId.systemDefault()).toInstant());
        }
        if(null != reportData.getArchitectureObj())
        {
            companyId = reportData.getArchitectureObj().getCompanyId();
            groupDepartId = reportData.getArchitectureObj().getGroupDepartId();
            hrDeptId = reportData.getArchitectureObj().getRecordId();
        }
        switch (reportData.getModuleType())
        {
            // 收入
            case 1:
                // 获取收款数据
                List<Hr_ReportData> collectionList = Hr_ReportUtils.getInstance().getCollectionData(innerFlag,companyId,groupDepartId,startDate,endDate);
                data.put("collectionList",collectionList);
                // 获取类目数据
                List<Hr_ReportData> categoryList = Hr_ReportUtils.getInstance().getCategoryData(reportData.getType(),hrDeptId,startDate,endDate);
                data.put("categoryList",categoryList);
                break;
            // 支出
            case 2:
                // 获取付款数据
                List<Hr_ReportData> paymentList = Hr_ReportUtils.getInstance().getPaymentData(innerFlag,companyId,groupDepartId,startDate,endDate);
                data.put("paymentList",paymentList);
                // 获取类目数据
                categoryList = Hr_ReportUtils.getInstance().getCategoryData(reportData.getType(),hrDeptId,startDate,endDate);
                data.put("categoryList",categoryList);
                break;
            // 库存使用
            case 3:
                // 获取成本数据
                List<Hr_ReportData> costList = Hr_ReportUtils.getInstance().getCostData(companyId,startDate,endDate);
                data.put("costList",costList);
                break;
            // 在线库存
            case 4:
                // 获取成本数据
                costList = Hr_ReportUtils.getInstance().getCostData(companyId,startDate,endDate);
                data.put("costList",costList);
                break;
            // 对账利润表
            case 5:
                // 获取订单数据
                List<Hr_ReportData> contractDetailList = Hr_ReportUtils.getInstance().getDetailData(companyId,startDate,endDate);
                data.put("contractDetailList",contractDetailList);
                costList = Hr_ReportUtils.getInstance().getCostData(companyId,startDate,endDate);
                data.put("costList",costList);
                break;
        }
        return data;
    }

    @Transactional(readOnly = false)
    public void handOnlineInventoryData()
    {
        // 账单重置
        singleReceivableDao.deleteAccountInit();
        singleReceivableDao.insertAccountInit();

        // 获取当前的LocalDateTime
        LocalDateTime now = LocalDateTime.now();
        // 格式化LocalDateTime为字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentTime = now.format(formatter);
        // 将LocalDateTime转换为Date
        Date date = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        List<String> chooseCompanyList = Lists.newArrayList();
        List<Company> erpCompanyListT = companyDao.getErpCompanyList(chooseCompanyList);
        for(Company company : erpCompanyListT)
        {
            List<DictValue> dictValueMatList = new ArrayList<>();// 物料类型
            List<DictValue> dictValueList = DictUtils.getValuesByItem(CommonEnums.DictItemEnum.MATERIAL_TYPE_STORE,company);
            if(Collections3.isEmpty(dictValueList))
            {
                continue;
            }
            for (DictValue dictValue : dictValueList) {
                if (!dictValue.getValue().equals(ConstKey.MATERIAL_REMAIN)) {
                    dictValueMatList.add(dictValue);
                }
            }
            RawmaterialStock rawmaterialStock = new RawmaterialStock();
            Date currentDate = new Date();
//            long oneDayMillis = 24 * 60 * 60 * 1000;
//            // 将当前时间往前推一天
//            Date previousDay = new Date(currentDate.getTime() - oneDayMillis);

            rawmaterialStock.setInoutTimeStartQr(currentDate);
            rawmaterialStock.setInoutTimeEndQr(currentDate);
            rawmaterialStock.setCompany(company);
            MaterialTimeUtils materialTimeUtils = new MaterialTimeUtils();
            List<RawmaterialStock> materialLlist =  materialTimeUtils.dealAllPlace(rawmaterialStock,company);

            if (Collections3.isNotEmpty(dictValueMatList))
            {
                for(DictValue valueObj : dictValueMatList)
                {
                    if (Collections3.isNotEmpty(materialLlist))
                    {
                        BigDecimal materialFirstInOutTotalTax = BigDecimal.ZERO;
                        BigDecimal weightedAmountsTax = BigDecimal.ZERO;
                        BigDecimal quantity = BigDecimal.ZERO;
                        for(RawmaterialStock stock : materialLlist)
                        {
                            if (StringUtils.isNotBlank(stock.getMaterialTypeId()) && stock.getMaterialTypeId().equals(valueObj.getRecordId()))
                            {
                                //先进先出金额(含税)
                                BigDecimal firstInFirstAmountTotalTax = (stock.getFirstInFirstAmountTotalTax() == null || stock.getFirstInFirstAmountTotalTax().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO : stock.getFirstInFirstAmountTotalTax();

                                materialFirstInOutTotalTax = materialFirstInOutTotalTax.add(firstInFirstAmountTotalTax);

                                //加权金额(含税)
                                BigDecimal cost = (stock.getCost() == null || stock.getCost().compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO : stock.getCost();

                                weightedAmountsTax = weightedAmountsTax.add(cost);

                                quantity = quantity.add(null == stock.getQuantity() ? BigDecimal.ZERO : stock.getQuantity());
                            }
                        }
                        valueObj.setMaterialFirstInOutTotalTax(materialFirstInOutTotalTax);
                        valueObj.setWeightedAmountsTax(weightedAmountsTax);
                        valueObj.setCompanyId(rawmaterialStock.getCompany().getRecordId());
                        valueObj.setResetDate(date);
                        valueObj.setQuantity(quantity);
                    }
                }
            }
            List<DictValue> dictMaterialList = Lists.newArrayList();
            if (Collections3.isNotEmpty(dictValueMatList))
            {
                for(DictValue dictValueObj : dictValueMatList)
                {
                    if ((dictValueObj.getMaterialFirstInOutTotal() != null &&
                            dictValueObj.getMaterialFirstInOutTotal().compareTo(new BigDecimal(0)) > 0)
                            || (dictValueObj.getWeightedAmountsTax() != null &&
                            dictValueObj.getWeightedAmountsTax().compareTo(new BigDecimal(0)) > 0))
                    {
                        dictMaterialList.add(dictValueObj);
                    }
                }
            }
            if (Collections3.isNotEmpty(dictMaterialList))
            {
                materialDao.batchInsertTwo(dictMaterialList);
            }
        }
    }
    @Transactional(readOnly = false)
    public String saveCertificateDetailData(Hr_Certificate hr_certificate)
    {
        if (hr_certificate == null)
        {
            return "请刷新数据重试！";
        }
        if (Collections3.isEmpty(hr_certificate.getHrCertificateDetailList()))
        {
            return "数据异常！";
        }
        hr_certificateDao.bathSaveCertificate(hr_certificate.getHrCertificateDetailList());

        //更改凭证拆分状态
        String certificateIds = "";
        for(Hr_CertificateDetail detailObj : hr_certificate.getHrCertificateDetailList())
        {
            if (StringUtils.isNotBlank(certificateIds))
            {
                certificateIds = certificateIds + "," + detailObj.getCertificateId();
            }else{
                certificateIds = detailObj.getCertificateId();
            }
        }
        hr_certificateDao.updateSplitStatus(certificateIds); //更改拆分状态
        return "success";
    }

    @Transactional(readOnly = false)
    public String cancelSplitData(Hr_Certificate hr_certificate)
    {
         hr_certificateDao.cancelSplitData(hr_certificate);
         hr_certificateDao.cancelSplitDetailData(hr_certificate);
         return "success";
    }


    //手工账单
    public Page<Hr_ManualBill> getManualBillList(Hr_ManualBill hr_manualBill,HttpServletRequest request,HttpServletResponse response,Integer type)
    {
        if (hr_manualBill == null)
        {
            return null;
        }
        Page<Hr_ManualBill> page = new Page<Hr_ManualBill>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(hr_manualBill.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_manualBill.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_manualBill.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_manualBill.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_manualBill.setPage(page);
        }
        List<Hr_ManualBill> manualBillList = hr_certificateDao.getManualBillList(hr_manualBill);
        List<Hr_ManualBill> certificateDetailList = hr_certificateDao.getCertificateDetailListTwo(manualBillList);
        if (Collections3.isNotEmpty(certificateDetailList))
        {
            for(Hr_ManualBill certificateObj : manualBillList)
            {
                List<Hr_ManualBill> detailList = Lists.newArrayList();
                for(Hr_ManualBill certificateDetail : certificateDetailList)
                {
                    if (certificateObj.getRecordId().equals(certificateDetail.getManualBillId()))
                    {
                        detailList.add(certificateDetail);
                    }
                }
                certificateObj.setCertificateDetailList(detailList);
            }
        }
        page.setList(manualBillList);
        return page;
    }

    @Transactional(readOnly = false)
    public String addManualBill(Hr_ManualBill hr_manualBill)
    {
        hr_certificateDao.addManualBill(hr_manualBill);

        //保存背书数据
        if (null != hr_manualBill && Collections3.isNotEmpty(hr_manualBill.getEndorseList()))
        {
            for(Hr_ManualBill endorse : hr_manualBill.getEndorseList())
            {
                if (StringUtils.isBlank(endorse.getRecordId()))
                {
                    endorse.setManualBillId(hr_manualBill.getRecordId());
                    hr_certificateDao.insertEndorse(endorse);
                }else{
                    hr_certificateDao.updateEndorse(endorse);
                }
            }
        }
        //自动生成做账明细
        if (StringUtils.isNotBlank(hr_manualBill.getDimensionId()))
        {
            if (hr_manualBill.getDimensionId().equals("1"))
            {
                hr_manualBill.setLoanAmount(hr_manualBill.getAmount());
                hr_certificateDao.insertManualBillData(hr_manualBill);

            }else if (hr_manualBill.getDimensionId().equals("2")){
                hr_manualBill.setBorrowAmount(hr_manualBill.getAmount());
                hr_certificateDao.insertManualBillData(hr_manualBill);
            }
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String updateManualBill(Hr_ManualBill hr_manualBill)
    {
        hr_certificateDao.updateManualBill(hr_manualBill);

        //保存背书数据
        if (null != hr_manualBill && Collections3.isNotEmpty(hr_manualBill.getEndorseList()))
        {
            for(Hr_ManualBill endorse : hr_manualBill.getEndorseList())
            {
                if (StringUtils.isBlank(endorse.getRecordId()))
                {
                    endorse.setManualBillId(hr_manualBill.getRecordId());
                    hr_certificateDao.insertEndorse(endorse);
                }else{
                    hr_certificateDao.updateEndorse(endorse);
                }
            }
        }
        //删除自动生成做账明细数据
        hr_certificateDao.deleteAccountBill(hr_manualBill.getRecordId());
        if (StringUtils.isNotBlank(hr_manualBill.getDimensionId()))
        {
            if (hr_manualBill.getDimensionId().equals("1"))
            {
                hr_manualBill.setLoanAmount(hr_manualBill.getAmount());
                hr_certificateDao.insertManualBillData(hr_manualBill);

            }else if (hr_manualBill.getDimensionId().equals("2")){
                hr_manualBill.setBorrowAmount(hr_manualBill.getAmount());
                hr_certificateDao.insertManualBillData(hr_manualBill);
            }
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String delManualBill(Hr_ManualBill hr_manualBill)
    {
        hr_certificateDao.delManualBill(hr_manualBill);
        return "success";
    }

    public List<Hr_CertificateDimension>getDimensionList(Hr_CertificateDimension hr_CertificateDimension)
    {
        return hr_certificateDao.getDimensionList(hr_CertificateDimension);
    }

    @Transactional(readOnly = false)
    public String deleteCertificateDataT(Hr_ManualBill hr_manualBill)
    {
        hr_certificateDao.deleteCertificateDataT(hr_manualBill);
        return "success";
    }

    public Map<String,Object> getHr_ReportDatailData(Hr_ReportData reportData)
    {
        Map<String,Object> data = new HashMap<>();
        if(null == reportData.getModuleType() || null == reportData.getModuleChildType())
        {
            return data;
        }

        Integer innerFlag = 1; // 1合并，2不合并
        String companyId = null; // erp公司id
        String groupDepartId = null; // 集团部门id
        Date startDate = null; // 开始时间
        Date endDate = null; // 结束时间
        String hrDeptId = null; // hr组织架构id

        // 如果是公司等级，获取ERP公司id进行匹配
        if(null != reportData.getArchitectureObj() && StringUtils.isNotBlank(reportData.getArchitectureObj().getDepartmentLevel())
                && "2".equals(reportData.getArchitectureObj().getDepartmentLevel()) && StringUtils.isNotBlank(reportData.getArchitectureObj().getCompanyId()))
        {
            innerFlag = 2;
        }
        if(null != reportData.getYear())
        {
            // 转换为 LocalDate 并格式化输出
            LocalDate firstDayOfYear = LocalDate.of(reportData.getYear(), 1, 1);
            LocalDate lastDayOfYear = LocalDate.of(reportData.getYear(), 12, 31);
            startDate = Date.from(firstDayOfYear.atStartOfDay(ZoneId.systemDefault()).toInstant());
            endDate = Date.from(lastDayOfYear.atStartOfDay(ZoneId.systemDefault()).toInstant());
        }
        if(null != reportData.getArchitectureObj())
        {
            companyId = reportData.getArchitectureObj().getCompanyId();
            groupDepartId = reportData.getArchitectureObj().getGroupDepartId();
            hrDeptId = reportData.getArchitectureObj().getRecordId();
        }
        reportData.setInnerFlag(innerFlag);
        reportData.setCompanyId(companyId);
        reportData.setGroupDepartId(groupDepartId);
        if(null != startDate && endDate != null)
        {
            reportData.setSentTimeStartQr(startDate);
            reportData.setSentTimeEndQr(endDate);
        }
        List<String> queryList = Lists.newArrayList();
        if(StringUtils.isNotBlank(hrDeptId))
        {
            queryList.add(hrDeptId);
            Hr_DepartMent depart = new Hr_DepartMent();
            depart.setAllId(hrDeptId);
            List<Hr_DepartMent> list = oa_dao.getDepart(depart);
            Hr_ReportUtils.getInstance().getChildDeptList(list,queryList);
        }
        reportData.setQueryList(queryList);
        switch (reportData.getModuleType())
        {
            // 收入
            case 1:
                switch (reportData.getModuleChildType())
                {
                    // 累计
                    case 1:
                        // 对账未完成
                        if(reportData.getSpecificType().compareTo(1) == 0)
                        {
                            List<GoodsCheck> gcList = goodsCheckDao.getUnReconciliationList(reportData);
                            data.put("gcList",gcList);
                        }
                        // 应收未收
                        else if(reportData.getSpecificType().compareTo(2) == 0)
                        {
                            List<SingleReceivableDetail> srList = singleReceivableDao.getReceivableList(reportData);
                            data.put("srList",srList);
                        }
                        break;
                    // 时间区间
                    case 2:
                        // 对账未完成
                        if(reportData.getSpecificType().compareTo(1) == 0)
                        {
                            List<GoodsCheck> gcList = goodsCheckDao.getUnReconciliationList(reportData);
                            data.put("gcList",gcList);
                        }
                        // 应收未收
                        else if(reportData.getSpecificType().compareTo(2) == 0)
                        {
                            List<SingleReceivableDetail> srList = singleReceivableDao.getReceivableList(reportData);
                            data.put("srList",srList);
                        }
                        // 实收
                        else if(reportData.getSpecificType().compareTo(3) == 0)
                        {
                            List<SingleReceivableDetail> srList = singleReceivableDao.getReceivableList(reportData);
                            data.put("srTwoList",srList);
                        }
                        // 汇总
                        else if(reportData.getSpecificType().compareTo(4) == 0)
                        {
                            List<GoodsCheck> gcList = goodsCheckDao.getUnReconciliationList(reportData);
                            data.put("gcList",gcList);
                            reportData.setSpecificType(2);
                            List<SingleReceivableDetail> srList = singleReceivableDao.getReceivableList(reportData);
                            data.put("srList",srList);
                            reportData.setSpecificType(3);
                            List<SingleReceivableDetail> srTwoList = singleReceivableDao.getReceivableList(reportData);
                            data.put("srTwoList",srTwoList);
                            reportData.setSpecificType(4);
                        }
                        break;
                    // 类目
                    case 3:
                        // 应收账款
                        if(reportData.getSpecificType().compareTo(1) == 0)
                        {
                            List<GoodsCheck> gcList = goodsCheckDao.getUnReconciliationList(reportData);
                            data.put("gcList",gcList);
                            reportData.setSpecificType(2);
                            List<SingleReceivableDetail> srList = singleReceivableDao.getReceivableList(reportData);
                            data.put("srList",srList);
                            reportData.setSpecificType(3);
                            List<SingleReceivableDetail> srTwoList = singleReceivableDao.getReceivableList(reportData);
                            data.put("srTwoList",srTwoList);
                            reportData.setSpecificType(3);
                        }
                        // 销售费用-其它费用
                        else if(reportData.getSpecificType().compareTo(2) == 0)
                        {
                            List<Oa_audit> oaList = oa_dao.getAuditList(reportData);
                            data.put("oaList",oaList);
                        }
                        break;
                }
                break;
            // 支出
            case 2:
                switch (reportData.getModuleChildType())
                {
                    // 累计
                    case 1:
                        // 对账未完成
                        if(reportData.getSpecificType().compareTo(1) == 0)
                        {
                            List<MaterialCheck> mcList = materialCheckDao.getUnReconciliationList(reportData);
                            data.put("mcList",mcList);
                        }
                        // 应付未付
                        else if(reportData.getSpecificType().compareTo(2) == 0)
                        {
                            List<SinglePayableDetail> spList = singlePayableDao.getReceivableList(reportData);
                            data.put("spList",spList);
                        }
                        break;
                    // 时间区间
                    case 2:
                        // 对账未完成
                        if(reportData.getSpecificType().compareTo(1) == 0)
                        {
                            List<MaterialCheck> mcList = materialCheckDao.getUnReconciliationList(reportData);
                            data.put("mcList",mcList);
                        }
                        // 应付未付
                        else if(reportData.getSpecificType().compareTo(2) == 0)
                        {
                            List<SinglePayableDetail> spList = singlePayableDao.getReceivableList(reportData);
                            data.put("spList",spList);
                        }
                        // 实付
                        else if(reportData.getSpecificType().compareTo(3) == 0)
                        {
                            List<SinglePayableDetail> spTwoList = singlePayableDao.getReceivableList(reportData);
                            data.put("spTwoList",spTwoList);
                        }
                        // 汇总
                        else if(reportData.getSpecificType().compareTo(4) == 0)
                        {
                            List<MaterialCheck> mcList = materialCheckDao.getUnReconciliationList(reportData);
                            data.put("mcList",mcList);
                            reportData.setSpecificType(2);
                            List<SinglePayableDetail> spList = singlePayableDao.getReceivableList(reportData);
                            data.put("spList",spList);
                            reportData.setSpecificType(3);
                            List<SinglePayableDetail> spTwoList = singlePayableDao.getReceivableList(reportData);
                            data.put("spTwoList",spTwoList);
                            reportData.setSpecificType(4);
                        }
                        break;
                    // 类目
                    case 3:
                        if(null != reportData.getSpecificType() && reportData.getSpecificType().compareTo(1) == 0)
                        {
                            List<MaterialCheck> mcList = materialCheckDao.getUnReconciliationList(reportData);
                            data.put("mcList",mcList);
                            reportData.setSpecificType(2);
                            List<SinglePayableDetail> spList = singlePayableDao.getReceivableList(reportData);
                            data.put("spList",spList);
                            reportData.setSpecificType(3);
                            List<SinglePayableDetail> spTwoList = singlePayableDao.getReceivableList(reportData);
                            data.put("spTwoList",spTwoList);
                            reportData.setSpecificType(1);
                        }
                        else
                        {
                            List<Oa_audit> oaList = oa_dao.getAuditList(reportData);
                            data.put("oaList",oaList);
                        }
                        break;
                }
                break;
            // 库存使用
            case 3:
                switch (reportData.getModuleChildType())
                {
                    case 2:
                        //先进先出
                        if(reportData.getSpecificType().compareTo(1) == 0)
                        {
                            List<RawmaterialStock> firstInFirstOutList = rawmaterialStockDao.getUseCostFeeFirstInFirstOutList(reportData);
                            data.put("firstInFirstOutList",firstInFirstOutList);
                        }
                        //加权平均
                        else if(reportData.getSpecificType().compareTo(2) == 0)
                        {
                            List<RawmaterialStock> weightedAverageList = rawmaterialStockDao.getWeightedAverageDetailList(reportData);
                            data.put("weightedAverageList",weightedAverageList);
                        }
                        // 汇总
                        else if(reportData.getSpecificType().compareTo(3) == 0)
                        {
                            List<RawmaterialStock> firstInFirstOutList = rawmaterialStockDao.getUseCostFeeFirstInFirstOutList(reportData);
                            data.put("firstInFirstOutList",firstInFirstOutList);
                            reportData.setSpecificType(2);
                            List<RawmaterialStock> weightedAverageList = rawmaterialStockDao.getWeightedAverageDetailList(reportData);
                            data.put("weightedAverageList",weightedAverageList);
                        }
                        break;
                    case 3:
                        List<RawmaterialStock> firstInFirstOutList = rawmaterialStockDao.getUseCostFeeFirstInFirstOutList(reportData);
                        data.put("firstInFirstOutList",firstInFirstOutList);
                }
                break;
            // 在线库存
            case 4:
                List<RawmaterialStock> materialList = Lists.newArrayList();
                Date date = new Date();
                List<String> chooseCompanyList = Lists.newArrayList();
                if(StringUtils.isNotBlank(companyId))
                {
                    chooseCompanyList.add(companyId);
                }
                List<Company> erpCompanyList = companyDao.getErpCompanyList(chooseCompanyList);
                if(Collections3.isNotEmpty(erpCompanyList))
                {
                    for(int i = 0; i < erpCompanyList.size(); i++)
                    {
                        Company comapny = erpCompanyList.get(i);
                        RawmaterialStock search = new RawmaterialStock();
                        search.setInoutTimeStartQr(date);
                        search.setCompany(comapny);
                        search.setMaterialTypeId(reportData.getConcreteType());
                        MaterialTimeUtils materialTimeUtils = new MaterialTimeUtils();
                        List<RawmaterialStock> materialLlist =  materialTimeUtils.dealAllPlace(search,comapny);
                        if(Collections3.isNotEmpty(materialLlist))
                        {
                            for(RawmaterialStock rs : materialLlist)
                            {
                                rs.setCompanyName(comapny.getShortName());
                                materialList.add(rs);
                            }
                        }
                    }
                }
                data.put("materialList",materialList);
                break;
            // 对账利润表
            case 5:
                switch (reportData.getSpecificType())
                {
                    // 订单面积
                    case 1:
                        List<GoodsCheck> gcList = goodsCheckDao.getGoodsCheckDataList(reportData);
                        List<ContractDetail> detailList = contractDetailDao.getOrderDetailDataList(gcList,reportData);
                        data.put("detailList",detailList);
                        break;
                    // 对账金额
                    case 2:
                        gcList = goodsCheckDao.getGoodsCheckDataList(reportData);
                        data.put("gcList",gcList);
                        break;
                    // 其它收入
                    case 3:
                        List<Oa_audit> oaList = oa_dao.getAuditList(reportData);
                        data.put("oaList",oaList);
                        break;
                    // 实际材料费用
                    case 4:
                        List<RawmaterialStock> rsList =rawmaterialStockDao.getDetailListTwo(reportData);
                        // 计算成本
                        MaterialTimeUtils materialTimeUtils = new MaterialTimeUtils();
                        materialTimeUtils.genCost(rsList);

                        //先进先出金额
                        List<RawmaterialStock> firstInOutList = materialDao.getFirstInOutAmountTotalList(reportData);

                        for(RawmaterialStock materialStock : rsList)
                        {
                            BigDecimal tax = BigDecimal.ZERO;
                            BigDecimal cent = new BigDecimal("100");
                            if (Collections3.isNotEmpty(firstInOutList))
                            {
                                for(RawmaterialStock inout : firstInOutList)
                                {
                                    if (inout == null)
                                    {
                                        continue;
                                    }
                                    if (StringUtils.isNotBlank(materialStock.getRecordId()) && StringUtils.isNotBlank(inout.getRecordId())
                                            && materialStock.getRecordId().equals(inout.getRecordId()))
                                    {
                                        materialStock.setFirstInFirstAmountTotalTax(inout.getFirstInFirstAmountTotalTax());

                                        String taxDescript = materialStock.getTaxDescript();
                                        if (!StringUtils.isEmpty(taxDescript))
                                        {
                                            if (taxDescript.contains("含税"))
                                            {
                                                if (taxDescript.split("含税").length > 1)
                                                {
                                                    taxDescript = taxDescript.split("含税")[1];
                                                }
                                            }

                                            if (taxDescript.contains("%"))
                                            {
                                                taxDescript = taxDescript.split("%")[0];
                                                try
                                                {
                                                    tax = new BigDecimal(taxDescript.trim());
                                                    if (tax.compareTo(BigDecimal.ZERO) > 0 && tax.compareTo(cent) < 0)
                                                    {
                                                        materialStock.setFirstInFirstAmountTotal(inout.getFirstInFirstAmountTotalTax().divide((cent.add(tax)),2, RoundingMode.HALF_UP).multiply(cent).setScale(2,RoundingMode.HALF_UP));
                                                    }
                                                }
                                                catch (Exception e)
                                                {
                                                    e.printStackTrace();
                                                }
                                            }
                                        }
                                        if (materialStock.getFirstInFirstAmountTotal() == null && materialStock.getFirstInFirstAmountTotalTax() != null)
                                        {
                                            materialStock.setFirstInFirstAmountTotal(materialStock.getFirstInFirstAmountTotalTax());
                                        }
                                    }
                                }
                            }
                        }
                        data.put("rsList",rsList);
                        break;
                    // 成本费用
                    case 5:
                        List<RawmaterialStock> firstInFirstOutList = rawmaterialStockDao.getUseCostFeeFirstInFirstOutList(reportData);
                        data.put("firstInFirstOutList",firstInFirstOutList);
                        reportData.setSpecificType(2);
                        List<RawmaterialStock> weightedAverageList = rawmaterialStockDao.getWeightedAverageDetailList(reportData);
                        data.put("weightedAverageList",weightedAverageList);
                        break;
                }
                break;
        }
        return data;
    }

    public List<Hr_ReportDetailData> getHr_AuditManagementData(Hr_ReportDetailData reportDetailData)
    {
        List<Hr_ReportDetailData> dataList = Lists.newArrayList();
        if(null == reportDetailData)
        {
            return dataList;
        }
        dataList = Hr_ReportUtils.getInstance().getHr_AuditManagementData(reportDetailData.getTypeId(),reportDetailData.getDataId());
        return dataList;
    }

    public List<Hr_ManualBill> getDimensionDataList(List<Hr_ManualBill> manualBillList) {
        List<Hr_ManualBill> certificateList = Lists.newArrayList();
        if (Collections3.isNotEmpty(manualBillList))
        {
            for(Hr_ManualBill manualObj : manualBillList)
            {
                Hr_CertificateDimension dimension = new Hr_CertificateDimension();
                dimension.setCertificateDetailId(manualObj.getRecordId());
                List<Hr_CertificateDimension> dimensionList =  hr_certificateDao.getDimensionList(dimension);
                manualObj.setDimensionList(dimensionList);
                if (Collections3.isNotEmpty(dimensionList))
                {
                    String dimensionName = "";
                    for(Hr_CertificateDimension dimensionObj : dimensionList)
                    {
                        if (StringUtils.isNotBlank(dimensionName))
                        {
                            dimensionName = dimensionName + ";"+ dimensionObj.getShareIds() + "/" + dimensionObj.getValueName();
                        }else {
                            dimensionName = dimensionObj.getShareIds() + "/" + dimensionObj.getValueName();
                        }
                    }
                    manualObj.setDimensionName(dimensionName);
                }
                certificateList.add(manualObj);
            }
        }
        return certificateList;
    }
    @Transactional(readOnly = false)
    public String commitAuit(Hr_ManualBill hr_manualBill)
    {
        if (null == hr_manualBill)
        {
            return "系统内部错误，请刷新重试";
        }
        if (Collections3.isEmpty(hr_manualBill.getChooseCertificateList()))
        {
            return "请选择需要审批的凭证!";
        }
        // 查询用户，赋予信息
        if (!(hr_manualBill != null && StringUtils.isNotBlank(hr_manualBill.getEmployeeId()) && StringUtils.isNotBlank(
                hr_manualBill.getDepartId()) && StringUtils.isNotBlank(hr_manualBill.getArchitectureId())))
        {
            return "用户信息获取不到，请确认OA系统中你是否在该公司";
        }
        WechatAudit wechatAudit =  new WechatAudit();
        wechatAudit.setApplicationsType(TypeKey.APPLICATION_DRAFT);
        if (StringUtils.isNotBlank(hr_manualBill.getNum()) && hr_manualBill.getNum().equals("1"))
        {
            wechatAudit.setAuditType(TypeKey.OAAUDDITTYPE_ACCOUNTVOUCHER);
        }else{
            wechatAudit.setAuditType(TypeKey.OAAUDDITTYPE_ACCOUNTVOUCHERTURN);
            wechatAudit.setAuditResult(hr_manualBill.getAuditResult());
        }
        wechatAudit.setEmpId(hr_manualBill.getEmployeeId());
        wechatAudit.setGroupId(hr_manualBill.getDepartId());
        wechatAudit.setOaDepartId(hr_manualBill.getOaDepartId());
        wechatAudit.setPassUser(hr_manualBill.getEmployeeId());
        if (StringUtils.isNotBlank(wechatAudit.getAuditType())) {
            if (StringUtils.isNotBlank(wechatAudit.getRecordId())) {
                wechatOaDao.resubmit(wechatAudit);
            } else {
                wechatOaDao.commitAudit(wechatAudit);
                for (Hr_ManualBill  manualBill : hr_manualBill.getChooseCertificateList())
                {
                   switch (manualBill.getTypeName())
                   {
                       case "电子承兑":
                           manualBill.setCertificateType("1");
                           break;
                       case "ERP":
                           manualBill.setCertificateType("2");
                           break;
                       case "OA":
                           manualBill.setCertificateType("3");
                           break;
                   }
                   manualBill.setAuditId(wechatAudit.getRecordId());
                   manualBill.setCertificateId(manualBill.getRecordId());
                }
                //批量插入凭证审批数据
                wechatOaDao.batchInsertCertificate(hr_manualBill.getChooseCertificateList());

                //批量更新凭证明细状态
                wechatOaDao.batchUpdateStatus(hr_manualBill.getChooseCertificateList());
                oaNumber(wechatAudit.getRecordId(), wechatAudit.getAuditType());
            }
            commitToApprove(wechatAudit);
        }
        sendCcAuditMsg(wechatAudit);
        return "已为您自动生成OA审批单";
    }

    @Transactional(readOnly = false)
    public String commitAuitTwo(Hr_CollectPayBill hr_collectPayBill)
    {
        if (null == hr_collectPayBill)
        {
            return "系统内部错误，请刷新重试";
        }

        if (!(hr_collectPayBill != null && StringUtils.isNotBlank(hr_collectPayBill.getEmployeeId()) && StringUtils.isNotBlank(
                hr_collectPayBill.getDepartId()) && StringUtils.isNotBlank(hr_collectPayBill.getArchitectureId())))
        {
            return "用户信息获取不到，请确认OA系统中你是否在该公司";
        }
        WechatAudit wechatAudit =  new WechatAudit();
        wechatAudit.setApplicationsType(TypeKey.APPLICATION_DRAFT);
        wechatAudit.setAuditType(TypeKey.OAAUDDITTYPE_COLLECTPAYBILL);
        wechatAudit.setEmpId(hr_collectPayBill.getEmployeeId());
        wechatAudit.setGroupId(hr_collectPayBill.getDepartId());
        wechatAudit.setOaDepartId(hr_collectPayBill.getOaDepartId());
        wechatAudit.setPassUser(hr_collectPayBill.getEmployeeId());
        wechatAudit.setHrCollectPayId(hr_collectPayBill.getRecordId());
        wechatAudit.setRecordId(hr_collectPayBill.getAuditId());
        String message = "";
        if (StringUtils.isNotBlank(wechatAudit.getAuditType())) {
            String auditStatus = "";
            if (StringUtils.isNotBlank(wechatAudit.getRecordId())) {
                //更改收付款单状态
                auditStatus = "22001";
                wechatOaDao.updateCollectPayStatus(wechatAudit.getHrCollectPayId(),auditStatus);
                //删除审批单
                wechatOaDao.updateAuditStatus(wechatAudit);
                message = "反审成功！";
                //反审销账金额还原
                hr_collectPayBill.setOperateType("2");
                verificationData(hr_collectPayBill);
            } else {
                wechatOaDao.commitAudit(wechatAudit);
                //更改收付款单状态
                auditStatus = "22003";
                wechatOaDao.updateCollectPayStatus(wechatAudit.getHrCollectPayId(),auditStatus);
                oaNumber(wechatAudit.getRecordId(), wechatAudit.getAuditType());

                //自动生成金蝶收付款单数据
                Oa_audit audit = new Oa_audit();
                audit.setHrCollectPayId(wechatAudit.getHrCollectPayId());

                Hr_CollectPayBill collectionPaidObj = oa_auditDao.getCollectionPaidList(audit);
                List<Hr_CollectPayBill> collectPayRecordList = Lists.newArrayList();
                if (collectionPaidObj.getDimensionId().equals("1"))
                {
                    collectPayRecordList = oa_auditDao.getCollectionRecordList(collectionPaidObj);
                }else if (collectionPaidObj.getDimensionId().equals("2"))
                {
                    collectPayRecordList = oa_auditDao.getPaymentRecordList(collectionPaidObj);
                }
                collectionPaidObj.setCollectPayDetailList(collectPayRecordList);
                collectionPaidObj.setBusinessType(hr_collectPayBill.getBusinessType());
                //自动生成至金蝶系统
                CloudySkyUtil.getInstance().saveReceiptPayBill(collectionPaidObj);
                message =  "已为您自动生成OA审批单";
                commitToApprove(wechatAudit);
                //进行销账
                hr_collectPayBill.setOperateType("1");
                verificationData(hr_collectPayBill);
            }
        }
        if (hr_collectPayBill.getDimensionId().equals("2"))
        {
            for(Hr_CollectPayBill bill : hr_collectPayBill.getCollectionPayRecordList())
            {
                Hr_CollectPayBill payment = hr_certificateDao.getSumPaymentAccount(bill);
                String auditStatusT = "";
                if (null != payment && payment.getSumPayAmount().compareTo(payment.getMoneyAmount()) == 0)
                {
                    auditStatusT = "3";
                }else{
                    auditStatusT = "1";
                }
                String auditId = payment != null && payment.getAuditId() != null ? payment.getAuditId() : bill.getReceivedPaidId();
                hr_certificateDao.updateAuditStatus(auditId,auditStatusT);
            }
        }
        return message;
    }

    @Transactional(readOnly = false)
    public void oaNumber(String id, String type) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        String dt = format.format(date);
        WechatOaNumber number = new WechatOaNumber();
        number.setId(id);
        number.setType(type);
        number.setDate(dt);
        String no = "";
        String noend = "";
        Integer i = wechatOaDao.findNumber(number);
        if (null != i && i > 0) {
            i = i + 1;
        } else {
            i = 1;
        }
        if (i < 10) {
            noend = "00" + i;
        } else if (i < 100) {
            noend = "0" + i;
        } else {
            noend = "" + i;
        }
        no = dt + noend;
        number.setNo(no);
        number.setNum(noend);
        wechatOaDao.saveNumber(number);
    }

    @Transactional(readOnly = false)
    public String commitToApprove(WechatAudit wechatAudit) {
        WechatCommitApprove commitApprove = new WechatCommitApprove();
        commitApprove.setAuditId(wechatAudit.getRecordId());
        commitApprove.setOpertionUserId(wechatAudit.getEmpId());
        commitApprove.setCommitPersonAllId(wechatAudit.getOaDepartId());
        ApprovalEngineUtil.commitApprove(commitApprove);
        return commitApprove.getResult();
    }

    public void endAudit(WechatCommitApprove commit) {
        if (commit == null || StringUtils.isBlank(commit.getAuditId())) {
            return;
        }
        String approveId = wechatOaDao.isEndAudit(commit);
        if (StringUtils.isNotBlank(approveId)) {
            MessageUtil.sendMsg(approveId, "7", null);
            wechatOaDao.updateApplicationStutas(commit.getAuditId());
            wechatOaDao.updateSinglePayableStatus(commit.getAuditId());
        }
    }

    public void sendCcAuditMsg(WechatAudit wechatAudit)
    {
        WechatAudit wa = wechatOaDao.getCopyforEmpName(wechatAudit);
        String empId = null;
        String name = null;
        if (null != wa)
        {
            empId = wa.getEmpId();
            name = wa.getName();
        }
        if (StringUtils.isNotBlank(wechatAudit.getAuditType()))
        {
            String message = null;
            switch (wechatAudit.getAuditType())
            {
                // 付款申请单
                case TypeKey.OAAUDDITTYPE_BOUND:
                    message = "“" + name + "”提交了记账凭证申请单";
                    break;
                default:
                    message = "你有新的抄送审批，请及时查阅";
                    break;
            }
            MessageUtil.sendMsg(empId, "5", message);
        }
    }

    public List<Hr_ManualBill> getElectronicAcceptanceList(Hr_ManualBill hr_manualBill)
    {
        return hr_certificateDao.getElectronicAcceptanceList(hr_manualBill);
    }

    public String delEndorse(Hr_ManualBill hr_manualBill)
    {
         hr_certificateDao.delEndorse(hr_manualBill);
         return "success";
    }

    public List<Hr_ManualBill> getEndorseList(Hr_ManualBill hr_manualBill)
    {
        return hr_certificateDao.getEndorseList(hr_manualBill);
    }

    public String updateCollectSubmitData(Hr_ManualBill hr_manualBill)
    {
        if (Collections3.isNotEmpty(hr_manualBill.getChooseCollectionList()))
        {
            String chooseCollectionIds = "";
            for(Hr_ManualBill chooseCollection : hr_manualBill.getChooseCollectionList())
            {
                if (StringUtils.isNotBlank(chooseCollectionIds))
                {
                    chooseCollectionIds = chooseCollectionIds + "," + chooseCollection.getRecordId();
                }else{
                    chooseCollectionIds = chooseCollection.getRecordId();
                }
            }
            //反审更改为创建
            if (hr_manualBill.getDataStateId().equals("1002"))
            {
                for(Hr_ManualBill manual : hr_manualBill.getChooseCollectionList())
                {
                    if (manual.getDataStateId().equals("1004"))
                    {
                        //查询是否存在记账凭证，如果存在删除后才可以反审
                        Integer num = hr_certificateDao.judgeExtisData(manual.getRecordId());
                        if (null != num && num > 0)
                        {
                            return "fail";
                        }
                    }
                }
            }
            hr_certificateDao.updateCollectSubmitData(chooseCollectionIds,hr_manualBill.getDataStateId());
            //审批完成自动生成记账凭证
            for(Hr_ManualBill choose : hr_manualBill.getChooseCollectionList())
            {
                if (choose.getDataStateId().equals("1004"))
                {

                }
            }
        }
        return "success";
    }

    public List<Hr_DepartMent> getOrganizationDepartList(Hr_DepartMent hr_departMent)
    {
        return hr_RecruitDao.getOrganizationDepartList(hr_departMent);
    }

    public List<Hr_Employee> getShowEmployeeList(Hr_Employee hr_employee)
    {
        return hr_employeeDao.getShowEmployeeList(hr_employee);
    }

    @Transactional(readOnly = false)
    public String delCasherMangeDetail(Hr_CollectPayBill hr_collectPayBill)
    {
        hr_certificateDao.delCasherMangeDetail(hr_collectPayBill);
        return "success";
    }

    @Transactional(readOnly = false)
    public String addCasherMange (Hr_CollectPayBill hr_collectPayBill)
    {
        if (null == hr_collectPayBill)
        {
            return "数据错误，请刷新重试!";
        }
        if (Collections3.isEmpty(hr_collectPayBill.getCollectPayDetailList()))
        {
            return "请填写需要保存记录!";
        }
        String writeOffStatus = null;
        for(Hr_CollectPayBill record : hr_collectPayBill.getCollectPayDetailList())
        {
            if (StringUtils.isBlank(record.getReceivedPaidId()))
            {
                writeOffStatus = "1";
                break;
            }
        }
        hr_collectPayBill.setWriteOffStatus(writeOffStatus);

        if (StringUtils.isNotBlank(hr_collectPayBill.getRecordId()))
        {
            hr_certificateDao.updateCollectPayBill(hr_collectPayBill);
        }else{
            hr_certificateDao.insertCollectPayBill(hr_collectPayBill);
        }

        for(Hr_CollectPayBill collectPayBill : hr_collectPayBill.getCollectPayDetailList())
        {
            collectPayBill.setReceiptPaymentId(hr_collectPayBill.getRecordId());
            collectPayBill.setEmployeeId(hr_collectPayBill.getEmployeeId());
            if (StringUtils.isNotBlank(collectPayBill.getRecordId()))
            {
                hr_certificateDao.updateCashManage(collectPayBill);
            }else{
                hr_certificateDao.addCashManage(collectPayBill);
            }
        }

        if (StringUtils.isNotBlank(hr_collectPayBill.getRecordId()))
        {
            //针对预付款选择应收单保存时进行销账
            if (StringUtils.isNotBlank(hr_collectPayBill.getCancelStatus()))
            {
                //进行销账
                hr_collectPayBill.setOperateType("1");
                hr_collectPayBill.setCollectionPayRecordList(hr_collectPayBill.getCollectPayDetailList());
                verificationData(hr_collectPayBill);
            }
        }
        return "保存成功!";
    }
    @Transactional(readOnly = false)
    public String delCollectionPay(Hr_CollectPayBill hr_collectPayBill)
    {
        //删除收付款单、收付款记录
        hr_certificateDao.delCollectionPay(hr_collectPayBill);
        hr_certificateDao.delCollectionPayRecord(hr_collectPayBill);
        return "success";
    }

    public List<Hr_CollectPayBill> loadReceivedAmountList(Hr_CollectPayBill hr_collectPayBill)
    {
        List<Hr_CollectPayBill> loadReceivedAmountList = Lists.newArrayList();
        if (hr_collectPayBill.getDimensionId().equals("1"))
        {
            loadReceivedAmountList = hr_certificateDao.loadCollectAmountList(hr_collectPayBill);
        }else if (hr_collectPayBill.getDimensionId().equals("2"))
        {
            loadReceivedAmountList = hr_certificateDao.loadPayAmountList(hr_collectPayBill);
        }
        return loadReceivedAmountList;
    }

    public List<Hr_CollectPayBill> loadCollectPayBillList(Hr_CollectPayBill hr_collectPayBill)
    {
        List<Hr_CollectPayBill> loadCollectPayBillList = Lists.newArrayList();
        if (hr_collectPayBill.getDimensionId().equals("1"))
        {
            loadCollectPayBillList = hr_certificateDao.loadCollectBillList(hr_collectPayBill);
        }else if (hr_collectPayBill.getDimensionId().equals("2"))
        {
            loadCollectPayBillList = hr_certificateDao.loadPayBillList(hr_collectPayBill);
        }
        return loadCollectPayBillList;
    }

    public List<Hr_CollectPayBill> getPaidAmount(Hr_CollectPayBill hr_collectPayBill)
    {
        List<Hr_CollectPayBill> list = new ArrayList<>();
        if(hr_collectPayBill.getDimensionId().equals("1"))
        {
            list = hr_certificateDao.getReceiveAmount(hr_collectPayBill);
        }else if (hr_collectPayBill.getDimensionId().equals("2"))
        {
            list = hr_certificateDao.getPaidAmount(hr_collectPayBill);
        }
       return list;
    }

    public Page<Hr_CollectPayBill> getReceiveManageList(Hr_CollectPayBill hr_collectPayBill,HttpServletRequest request,HttpServletResponse response,Integer type)
    {
        if (hr_collectPayBill == null)
        {
            return null;
        }
        Page<Hr_CollectPayBill> page = new Page<Hr_CollectPayBill>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(hr_collectPayBill.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_collectPayBill.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_collectPayBill.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_collectPayBill.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_collectPayBill.setPage(page);
        }
        List<Hr_CollectPayBill> receiveManageList = hr_certificateDao.getReceiveManageList(hr_collectPayBill);
        page.setList(receiveManageList);
        return page;
    }

    public Page<Hr_CollectPayBill> getPaymentManageList(Hr_CollectPayBill hr_collectPayBill,HttpServletRequest request,HttpServletResponse response,Integer type)
    {
        if (hr_collectPayBill == null)
        {
            return null;
        }
        Page<Hr_CollectPayBill> page = new Page<Hr_CollectPayBill>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(hr_collectPayBill.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_collectPayBill.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_collectPayBill.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_collectPayBill.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_collectPayBill.setPage(page);
        }
        List<Hr_CollectPayBill> receiveManageList = hr_certificateDao.getPaymentManageList(hr_collectPayBill);
        page.setList(receiveManageList);
        return page;
    }

    public Page<Hr_CollectPayBill> getReceivePayableList(Hr_CollectPayBill hr_collectPayBill, HttpServletRequest request,
                                                         HttpServletResponse response, Integer type)
    {
        Page<Hr_CollectPayBill> page = new Page<>(request, response, type);
        Integer pageNo = 1;
        Integer pageSize = 10;
        if (StringUtils.isNotBlank(hr_collectPayBill.getPageNo()))
        {
            pageNo = Integer.valueOf(hr_collectPayBill.getPageNo());
        }
        if (StringUtils.isNotBlank(hr_collectPayBill.getPageSize()))
        {
            pageSize = Integer.valueOf(hr_collectPayBill.getPageSize());
        }
        if (type != -1)
        {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            hr_collectPayBill.setPage(page);
        }
        List<Hr_CollectPayBill> singleReceivePayList = Lists.newArrayList();
        if (StringUtils.isNotBlank(hr_collectPayBill.getDocumentType()) && hr_collectPayBill.getDocumentType().equals("1"))
        {
            //查询应收单
            singleReceivePayList = hr_certificateDao.getSingleReceivableList(hr_collectPayBill);
            for(Hr_CollectPayBill singleReceive : singleReceivePayList)
            {
                BigDecimal settlementAmount = singleReceive.getSettlementAmount() == null ? BigDecimal.ZERO : singleReceive.getSettlementAmount();
                if (singleReceive.getNumber() > 0)
                {
                    BigDecimal amountT = singleReceive.getAmount().subtract(singleReceive.getReceivedAmountT()).subtract(settlementAmount);
                    singleReceive.setAmount(amountT);
                    BigDecimal receivedAmountT = singleReceive.getReceivedAmount().add(singleReceive.getReceivedAmountT());
                    singleReceive.setReceivedAmount(receivedAmountT);
                }else{
                    BigDecimal amountT = singleReceive.getAmount().subtract(settlementAmount);
                    singleReceive.setAmount(amountT);
                }
            }
        }
        if (StringUtils.isNotBlank(hr_collectPayBill.getDocumentType()) && hr_collectPayBill.getDocumentType().equals("2"))
        {
            //查询应付单
            hr_collectPayBill.setNumber(1);
            singleReceivePayList = hr_certificateDao.getSinglePayableListTwo(hr_collectPayBill);
        }
        //获取收付款记录
        List<Hr_CollectPayBill> collectPayableList = hr_certificateDao.getCollectPayableList(singleReceivePayList);

        if (Collections3.isNotEmpty(singleReceivePayList))
        {
            for (Hr_CollectPayBill collectPayBill : singleReceivePayList)
            {
                List<Hr_CollectPayBill> collectPayRecordList = Lists.newArrayList();
                if (Collections3.isNotEmpty(collectPayableList))
                {
                    for (Hr_CollectPayBill recordBill : collectPayableList)
                    {
                        if (collectPayBill.getRecordId().equals(recordBill.getReceivedPaidId()))
                        {
                            collectPayRecordList.add(recordBill);
                        }
                    }
                }
                collectPayBill.setCollectionPayRecordList(collectPayRecordList);
            }
            page.setList(singleReceivePayList);
        }
        return page;
    }
    /**
     * zjn 2024-02-26 核销应收应付单
     * @param hr_CollectPayBill 参数对象
     */
    @Transactional(readOnly = false)
    public String verificationData(Hr_CollectPayBill hr_CollectPayBill)
    {
        if(null == hr_CollectPayBill || StringUtils.isBlank(hr_CollectPayBill.getDimensionId()) || StringUtils.isBlank(hr_CollectPayBill.getOperateType())
        || Collections3.isEmpty(hr_CollectPayBill.getCollectionPayRecordList()))
        {
            return "fail";
        }
        String operateType = hr_CollectPayBill.getOperateType();
        // 核销
        if("1".equals(operateType))
        {
            hr_CollectPayBill.setBillStatus("1002");
        }
        // 取消核销
        else if("2".equals(operateType))
        {
            hr_CollectPayBill.setBillStatus("1001");
        }
        hr_certificateDao.updatePaymentBillStatus(hr_CollectPayBill);
        for(Hr_CollectPayBill billDetail : hr_CollectPayBill.getCollectionPayRecordList())
        {
            if(StringUtils.isBlank(billDetail.getCompanyId()) && StringUtils.isBlank(hr_CollectPayBill.getErpCompanyId()))
            {
                continue;
            }
            if(StringUtils.isBlank(billDetail.getPeriod()))
            {
                continue;
            }
            Company company = new Company(hr_CollectPayBill.getErpCompanyId());
            String paymentRecordId = billDetail.getRecordId();
            Integer period = Integer.valueOf(billDetail.getPeriod()); // 账期
            BigDecimal amount = billDetail.getAmountReceivable();; // 金额
            if("1".equals(hr_CollectPayBill.getDimensionId()))
            {
                String customerId = billDetail.getCustomerId(); // 客户
                PayMoneyUtil.getInstance().writeOffReceivables(company,customerId,period,amount,operateType,paymentRecordId);
            }
            else if("2".equals(hr_CollectPayBill.getDimensionId()))
            {
                String supplierId = billDetail.getSupplierId(); // 供应商
                PayMoneyUtil.getInstance().writeOffPayable(company,supplierId,period,amount,operateType,paymentRecordId);
            }
        }


        return "success";
    }

    public void setExportReceiveManage(ExportExcel excel, List<Hr_CollectPayBill> list, String[] hearList) {
        for (Hr_CollectPayBill bill : list) {
            int colunm = 0;
            Row row = excel.addRow();

            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "客户编码":
                        val = (bill.getCustomerNo() == null) ? "" : bill.getCustomerNo();
                        break;
                    case "付款方":
                        val = (bill.getCustomerName() == null) ? "" : bill.getCustomerName();
                        break;
                    case "收款方":
                        val = (bill.getCompanyName() == null) ? "" : bill.getCompanyName();
                        break;
                    case "款项类型":
                        val = (bill.getDocumentType() == null)  ? "" : bill.getDocumentType();
                        break;
                    case "金额":
                        val = (bill.getAmountReceivable() == null || bill.getAmountReceivable().compareTo(BigDecimal.ZERO) == 0) ? "0" : bill.getAmountReceivable().toString();
                        break;
                    case "单据编码":
                        val = (bill.getNo() == null) ? "" : bill.getNo();
                        break;
                    case "收款日期":
                        val = (bill.getBusinessDate() == null) ? "" : DateUtils.formatDate(bill.getBusinessDate());
                        break;
                    case "对账月份":
                        val = (bill.getPeriod() == null) ? "" : bill.getPeriod();
                        break;
                    case "创建时间":
                        val = (bill.getCreatedDate() == null) ? "" : DateUtils.formatDateTime(bill.getCreatedDate());
                        break;
                    case "手续费":
                        val = (bill.getProcedureFree() == null || bill.getProcedureFree().compareTo(BigDecimal.ZERO) == 0) ? "0" : bill.getProcedureFree().toString();
                        break;
                    case "其他费":
                        val = (bill.getOtherFree() == null || bill.getOtherFree().compareTo(BigDecimal.ZERO) == 0) ? "0" : bill.getOtherFree().toString();
                        break;
                    case "结算方式":
                        val = (bill.getSettlementName() == null) ? "" : bill.getSettlementName();
                        break;
                    case "收款用途":
                        val = (bill.getCollectPurposeName() == null) ? "" : bill.getCollectPurposeName();
                        break;
                    case "备注":
                        val = (bill.getRemark() == null) ? "" : bill.getRemark();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }


    public void setExportPaymentManage(ExportExcel excel, List<Hr_CollectPayBill> list, String[] hearList) {
        for (Hr_CollectPayBill bill : list) {
            int colunm = 0;
            Row row = excel.addRow();

            for (String name : hearList) {
                String val = "";
                switch (name) {
                    case "审批编码":
                        val = (bill.getNo() == null) ? "" : bill.getNo();
                        break;
                    case "单据类型":
                        val = (bill.getAuditTypeName() == null) ? "" : bill.getAuditTypeName();
                        break;
                    case "收款单位":
                        val = (bill.getTargetName() == null) ? "" : bill.getTargetName();
                        break;
                    case "付款组织":
                        val = (bill.getDepartmentName() == null)  ? "" : bill.getDepartmentName();
                        break;
                    case "金额":
                        val = (bill.getAmountReceivable() == null || bill.getAmountReceivable().compareTo(BigDecimal.ZERO) == 0) ? "0" : bill.getAmountReceivable().toString();
                        break;
                    case "付款日期":
                        val =  (bill.getBusinessDate() == null) ? "" : DateUtils.formatDate(bill.getBusinessDate());
                        break;
                    case "手续费":
                        val = (bill.getProcedureFree() == null || bill.getProcedureFree().compareTo(BigDecimal.ZERO) == 0) ? "0" : bill.getProcedureFree().toString();
                        break;
                    case "其他费":
                        val = (bill.getOtherFree() == null || bill.getOtherFree().compareTo(BigDecimal.ZERO) == 0) ? "0" : bill.getOtherFree().toString();
                        break;
                    case "结算方式":
                        val = (bill.getSettlementName() == null) ? "" : bill.getSettlementName();
                        break;
                    case "付款用途":
                        val = (bill.getCollectPurposeName() == null) ? "" : bill.getCollectPurposeName();
                        break;
                    case "提交人":
                        val = (bill.getCommitName() == null) ? "" : bill.getCommitName();
                        break;
                    case "终批人":
                        val = (bill.getEmployeeName() == null) ? "" : bill.getEmployeeName();
                        break;
                    case "终批时间":
                        val = (bill.getLastUpdDate() == null) ? "" : DateUtils.formatDateTime(bill.getLastUpdDate());
                        break;
                    case "备注":
                        val = (bill.getRemark() == null) ? "" : bill.getRemark();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }
}
