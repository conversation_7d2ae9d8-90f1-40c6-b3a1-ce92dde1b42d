/**
 * 
 */
package com.kyb.pcberp.common.utils.excel;

import java.io.Closeable;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.modules.hr.finance_center.pojo.Hr_Certificate;
import com.kyb.pcberp.modules.hr.finance_center.pojo.Hr_ManualBill;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.utils.Encodes;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.production.entity.Wip;
import com.kyb.pcberp.modules.production.entity.WipProcessStop;

/**
 * 
 * @ClassName: ExportOther
 * @Description: 导出所有表到一个EXCEL，仅限小数据一万条左右
 *
 * @author: LPJ
 * @Date: 2016年07月21日
 *
 */
public class ExportOther
{
    
    /**
     * 工作薄对象
     */
    private XSSFWorkbook wb;
    
    /**
     * 工作表对象
     */
    private XSSFSheet sheet;
    
    /**
     * 样式列表
     */
    private Map<String, XSSFCellStyle> styles;
    
    public ExportOther exportWip(String title, List<String> fieldTitles, List<Wip> wips,
        Map<String, WipProcessStop> stops, String totalArea, List<String> processNameList, String sheetName,
        String processNames, String start)
        throws Exception
    {
        this.wb = new XSSFWorkbook();
        this.styles = createStyles(wb);
        this.sheet = this.wb.createSheet(title);
        
        generateSheet(title, fieldTitles, wips, stops, totalArea, processNameList, processNames, start);
        
        return this;
    }
    
    /**
     * @Title createStyles
     * @Description 创建样式
     * @param wb
     * @return Map<String,XSSFCellStyle>
     */
    private static Map<String, XSSFCellStyle> createStyles(XSSFWorkbook wb)
    {
        Map<String, XSSFCellStyle> styles = new HashMap<String, XSSFCellStyle>();
        
        // 目录链接标题的样式
        XSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        XSSFFont titleFont = wb.createFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short)16);
        titleFont.setBoldweight(XSSFFont.BOLDWEIGHT_BOLD);
        
        style.setFont(titleFont);
        styles.put("title", style);
        
        // cell
        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        
        XSSFFont dataFont = wb.createFont();
        dataFont.setFontHeightInPoints((short)10);
        dataFont.setFontName("Arial");
        style.setFont(dataFont);
        styles.put("data", style);
        
        // 进度延期
        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        // style.setFillForegroundColor(new XSSFColor(new Color(228, 86, 86)));
        style.setFillForegroundColor(IndexedColors.CORAL.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styles.put("data1", style);
        
        // 存在风险
        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        // style.setFillForegroundColor(new XSSFColor(new Color(252, 248, 227)));
        style.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styles.put("data2", style);
        
        // 进度正常
        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        // style.setFillForegroundColor(new XSSFColor(new Color(223, 240, 216)));
        style.setFillForegroundColor(IndexedColors.LIME.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styles.put("data3", style);
        
        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,##0"));
        style.setFillForegroundColor(IndexedColors.LIME.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        styles.put("dataNum", style);
        
        // header
        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        XSSFFont headerFont = wb.createFont();
        headerFont.setFontName("Arial");
        headerFont.setFontHeightInPoints((short)10);
        headerFont.setBoldweight(XSSFFont.BOLDWEIGHT_BOLD);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(headerFont);
        styles.put("header", style);
        
        return styles;
    }
    
    private void generateSheet(String title, List<String> titles, List<Wip> datas, Map<String, WipProcessStop> stops,
        String totalArea, List<String> processNameList, String processNames, String start)
        throws Exception
    {
        // 合并第一行的列为一个单元格
        int size = titles.size();
        size = size + (stops == null ? 0 : stops.size() - 1);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, size - 1));
        
        XSSFCell cell;
        XSSFRow row;
        
        // 创建表标题单元格
        cell = sheet.createRow(0).createCell((short)0);
        cell.setCellValue(title);
        cell.setCellStyle(styles.get("title"));
        int tempIndex = 0;
        
        // 工序按Id拍一下序
        List<String> titleSets = Lists.newArrayList();
        for (String processName : processNameList)
        {
            for (String string : stops.keySet())
            {
                String cleanStopName = string.replaceAll("^(曝光-|丝印-)", "");
                if (cleanStopName.equals(processName))
                {
                    titleSets.add(string);
                    continue;
                }
            }
        }
        if(Collections3.isNotEmpty(titleSets))
        {
            for (String string : stops.keySet())
            {
                Boolean flag = false;
                for(String titleStr : titleSets)
                {
                    if(string.equals(titleStr))
                    {
                        flag = true;
                        break;
                    }
                }
                if(!flag)
                {
                    if(string.equals("仓库"))
                    {
                        titleSets.add(0,string);
                    }
                    else
                    {
                        titleSets.add(1,string);
                    }
                }
            }
        }
//        for (String processName : processNameList)
//        {
//            for (String string : stops.keySet())
//            {
//                if (string.equals(processName))
//                {
//                    titleSets.add(string + "㎡");
//                    break;
//                }
//            }
//        }
        
        // 产生表标题行
        int rowIndex = 1;
        row = sheet.createRow((short)rowIndex);
        tempIndex = 0;
        for (short i = 0; i < titles.size(); i++)
        {
            if (titles.get(i).equals("工序"))
            {
                for (String key : titleSets)
                {
                    sheet.setColumnWidth(tempIndex, 5 * 800);
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("header"));
                    if (key.contains("㎡"))
                    {
                        cell.setCellValue(key);
                    }
                    else
                    {
                        cell.setCellValue(stops.get(key).getCategory());
                    }
                    tempIndex++;
                }
            }
            else
            {
                if (i == titles.size() - 1)
                {
                    sheet.setColumnWidth(tempIndex, 8 * 800);
                }
                else
                {
                    sheet.setColumnWidth(tempIndex, 5 * 800);
                }
                cell = row.createCell(tempIndex);
                cell.setCellStyle(styles.get("header"));
                cell.setCellValue(titles.get(i));
                
                tempIndex++;
            }
        }
        
        // 数据为空不再处理
        if (datas == null)
        {
            return;
        }
        
        // 结存面积(㎡)
        tempIndex = 0;
        row = sheet.createRow((short)2);
        for (int i = 0; i < titles.size(); i++)
        {
            String field = titles.get(i);
            switch (field)
            {
                case "报废告警":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "序号":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "生产编号":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "进度":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "客户型号":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "客户订单号":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "单据状态":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "曝光/丝印":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "状态":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "订单类型":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "当前工序":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "生产类型":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "订单/投料":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
/*                case "生产数量":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;*/
                case "生产数(PCS)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "生产数(PNL)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "在线结存(PCS)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "结存面积(㎡)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "结存总面积(㎡)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "生产结论":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "首批交期":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "最终交期":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("面积(㎡):");
                    tempIndex++;
                    break;
                case "工序":
                    for (String key : titleSets)
                    {
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get("data"));
                        cell.setCellValue(stops.get(key.replace("㎡", "")).getArea());
                        tempIndex++;
                    }
                    break;
                case "入库数量":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue(totalArea);
                    tempIndex++;
                    break;
                case "下单/补料(日期)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "发料日期":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "通知单号":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "加急":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "投(补)料单号":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
            }
        }
        
        tempIndex = 0;
        row = sheet.createRow((short)3);
        for (int i = 0; i < titles.size(); i++)
        {
            String field = titles.get(i);
            switch (field)
            {
                case "报废告警":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "序号":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "生产编号":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "进度":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "客户型号":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "客户订单号":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "单据状态":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "曝光/丝印":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "状态":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "订单类型":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "当前工序":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "生产类型":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "订单/投料":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "生产数(PCS)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "生产数(PNL)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "在线结存(PCS)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "结存面积(㎡)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "结存总面积(㎡)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "生产结论":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "首批交期":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "最终交期":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("结存:");
                    tempIndex++;
                    break;
                case "工序":
                    for (String key : titleSets)
                    {
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get("data"));
                        if (StringUtils.isNotBlank(start) && start.equals("1"))
                        {
                            cell.setCellValue(stops.get(key.replace("㎡", "")).getAbPcsQty());
                        }
                        else
                        {
                            if (StringUtils.isNotBlank(processNames))
                            {
                                if (processNames.indexOf(key.replace("㎡", "")) != -1)
                                {
                                    cell.setCellValue(stops.get(key.replace("㎡", "")).getAbPcsQty());
                                }
                                else
                                {
                                    cell.setCellValue(stops.get(key.replace("㎡", "")).getAbQty());
                                }
                            }
                            else
                            {
                                cell.setCellValue(stops.get(key.replace("㎡", "")).getAbQty());
                            }
                        }
                        tempIndex++;
                    }
                    break;
                case "入库数量":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "下单/补料(日期)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "发料日期":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "通知单号":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "加急":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
                case "投(补)料单号":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("");
                    tempIndex++;
                    break;
            }
        }
        
        // 数据行起始索引
        int index = 3;
        
        // 行数据
        Wip rowData = null;
        String textValue = null;
        
        // 插入内容，遍历集合数据，产生数据行
        Iterator<Wip> it = datas.iterator();
        while (it.hasNext())
        {
            // 创建行索引
            index++;
            
            // 创建行
            row = sheet.createRow(index);
            
            // 获取一行数据
            rowData = (Wip)it.next();
            
            String style = "data3";
            if (rowData.getState().equals("进度延期"))
            {
                style = "data1";
            }
            else if (rowData.getState().equals("存在风险"))
            {
                style = "data2";
            }
            else if (rowData.getState().equals("还未发料"))
            {
                style = "data";
            }
            
            // 循环导出字段
            tempIndex = 0;
            for (int i = 0; i < titles.size(); i++)
            {
                String field = titles.get(i);
                switch (field)
                {
                    case "报废告警":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getWarn() == null ? "否" : rowData.getWarn() ? "是" : "否";
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "序号":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getNum() == null ? "" : rowData.getNum();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "生产编号":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getCraftNo() == null ? "" : rowData.getCraftNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "进度":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getState() == null ? "" : rowData.getState();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "客户型号":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getCustomerModel() == null ? "" : rowData.getCustomerModel();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "客户订单号":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if (null != rowData.getNotification()
                            && StringUtils.isNotBlank(rowData.getNotification().getCustomerPo()))
                        {
                            textValue = rowData.getNotification().getCustomerPo();
                        }
                        else
                        {
                            textValue = "";
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "单据状态":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if (null != rowData.getFeeding()
                            && StringUtils.isNotBlank(rowData.getFeeding().getDocumentsStatus()))
                        {
                            textValue = rowData.getFeeding().getDocumentsStatus();
                        }
                        else
                        {
                            textValue = "";
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "曝光/丝印":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if(StringUtils.isNotBlank(rowData.getProcessValue()))
                        {
                            textValue = rowData.getProcessValue();
                        }
                        else
                        {
                            textValue = "";
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "状态":
                        textValue = "确认";
                        if (rowData.getBitSuspend().equals("2"))
                        {
                            textValue = "缺料";
                        }
                        else if (rowData.getBitSuspend().equals("3"))
                        {
                            textValue = "暂停";
                        }
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "订单类型":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = StringUtils.isBlank(rowData.getOrderTypeName()) ? null : rowData.getOrderTypeName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "当前工序":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = StringUtils.isBlank(rowData.getEgname()) ? null : rowData.getEgname();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "生产类型":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = StringUtils.isBlank(rowData.getProduceType()) ? null : rowData.getProduceType();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "订单/投料":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getOrderFeedPcsQuantity() == null ? "" : rowData.getOrderFeedPcsQuantity();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "生产数(PCS)":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getBoardABPcsQty() == null ? "" : rowData.getBoardABPcsQty();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "生产数(PNL)":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getBoardABPnlQty() == null ? "" : rowData.getBoardABPnlQty();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "在线结存(PCS)":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get("dataNum"));
                        cell.setCellValue(rowData.getCateNum() == null ? 0 : rowData.getCateNum());
                        tempIndex++;
                        break;
                    case "结存面积(㎡)":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getIcloudArea() == null ? "" : rowData.getIcloudArea();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "结存总面积(㎡)":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getIcloudTotalArea() == null ? "" : rowData.getIcloudTotalArea();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "生产结论":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = StringUtils.isBlank(rowData.getProductResult()) ? null : rowData.getProductResult();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "首批交期":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if(null != rowData.getFeeding() && null != rowData.getFeeding().getFirstBatchDate())
                        {
                            textValue = rowData.getFeeding().getFirstBatchDateStr();
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "最终交期":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getDeliveryDate() == null ? "" : rowData.getDeliveryDate();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "工序":
                        
                        for (String key : titleSets)
                        {
                            cell = row.createCell(tempIndex);
                            cell.setCellStyle(styles.get(style));
                            WipProcessStop wipStop = rowData.getWipProcessStopList().get(key.replace("㎡", ""));
                            
                            if (key.contains("㎡"))
                            {
                                textValue = "";
                                if (wipStop != null)
                                {
                                    textValue = "A:" + wipStop.getaArea() + "/B:" + wipStop.getbArea();
                                }
                            }
                            else
                            {
                                if (StringUtils.isNotBlank(start) && start.equals("1"))
                                {
                                    textValue = wipStop == null ? "" : wipStop.getAbPcsQty();
                                }
                                else
                                {
                                    if (StringUtils.isNotBlank(processNames))
                                    {
                                        if (processNames.indexOf(key) != -1)
                                        {
                                            textValue = wipStop == null ? "" : wipStop.getAbPcsQty();
                                        }
                                        else
                                        {
                                            textValue = wipStop == null ? "" : wipStop.getAbQty();
                                        }
                                    }
                                    else
                                    {
                                        textValue = wipStop == null ? "" : wipStop.getAbQty();
                                    }
                                }
                            }
                            cell.setCellValue(textValue);
                            tempIndex++;
                        }
                        
                        break;
                    case "入库数量":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getStocks() == null ? "" : rowData.getStocks();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "下单/补料(日期)":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getOrderDate() == null ? "" : rowData.getOrderDate();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "发料日期":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getAcDistributeDateStr() == null ? "" : rowData.getAcDistributeDateStr();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "通知单号":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getNno() == null ? "" : rowData.getNno();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "加急":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getUrgentFlag() == null ? "" : rowData.getUrgentFlag();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "投(补)料单号":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getFno() == null ? "" : rowData.getFno();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                }
            }
        }
    }

    public ExportOther exportCertificate(List<Hr_Certificate> list, List<String> hearList)
        throws Exception
    {
        this.wb = new XSSFWorkbook();
        this.styles = createStyles(wb);
        this.sheet = this.wb.createSheet("凭证#单据头(FBillHead)");

        generaCertificate(list,hearList);

        return this;
    }

    public void generaCertificate(List<Hr_Certificate> list, List<String> hearList) throws Exception
    {
        // 合并第一行的列为一个单元格
        /*int size = hearList.size();
        size = size + (hearList == null ? 0 : hearList.size() - 1);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, size - 1));*/

        XSSFCell cell;
        XSSFRow row;

        // 产生标题行
        int rowIndex = 0;
        row = sheet.createRow((short)rowIndex);
        int tempIndex = 0;
        for (short i = 0; i < hearList.size(); i++)
        {
            if (i == hearList.size() - 1)
            {
                sheet.setColumnWidth(tempIndex, 8 * 800);
            }
            else
            {
                sheet.setColumnWidth(tempIndex, 5 * 800);
            }
            cell = row.createCell(tempIndex);
            cell.setCellStyle(styles.get("header"));
            cell.setCellValue(hearList.get(i));

            tempIndex++;
        }

        // 数据为空不再处理
        if (list == null)
        {
            return;
        }

        tempIndex = 0;
        row = sheet.createRow((short)1);
        for (int i = 0; i < hearList.size(); i++)
        {
            String field = hearList.get(i);
            switch (field)
            {
                case "FBillHead(GL_VOUCHER)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*单据头(序号)");
                    tempIndex++;
                    break;
                case "FAccountBookID":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(单据头)账簿#编码");
                    tempIndex++;
                    break;
                case "FAccountBookID#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(单据头)账簿#名称");
                    tempIndex++;
                    break;
                case "FDate":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(单据头)日期");
                    tempIndex++;
                    break;
                case "FVOUCHERGROUPID":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(单据头)凭证字#编码");
                    tempIndex++;
                    break;
                case "FVOUCHERGROUPID#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(单据头)凭证字#名称");
                    tempIndex++;
                    break;
                case "FVOUCHERGROUPNO":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(单据头)凭证号");
                    tempIndex++;
                    break;
                case "FACCBOOKORGID":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(单据头)核算组织#编码");
                    tempIndex++;
                    break;
                case "FACCBOOKORGID#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(单据头)核算组织#名称");
                    tempIndex++;
                    break;
                case "*Split*1":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("间隔列");
                    tempIndex++;
                    break;
                case "FEntity":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*分录(序号)");
                    tempIndex++;
                    break;
                case "FEXPLANATION":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)摘要");
                    tempIndex++;
                    break;
                case "FACCOUNTID":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(分录)科目编码#编码");
                    tempIndex++;
                    break;
                case "FACCOUNTID#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)科目编码#名称");
                    tempIndex++;
                    break;
                case "FDETAILIDCOMBINATION":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)核算维度组合");
                    tempIndex++;
                    break;
                case "FDetailID#FFLEX14":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)银行#编码");
                    tempIndex++;
                    break;
                case "FDetailID#FFLEX14#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)银行#名称(Null)");
                    tempIndex++;
                    break;
                case "FDetailID#FFLEX15":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)银行账号#编码");
                    tempIndex++;
                    break;
                case "FDetailID#FFLEX15#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)银行账号#名称(Null)");
                    tempIndex++;
                    break;
                case "FDetailID#FF100005":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)收入分类#编码");
                    tempIndex++;
                    break;
                case "FDetailID#FF100005#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)收入分类#名称(Null)");
                    tempIndex++;
                    break;
                case "FDetailID#FFlex6":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)客户#编码");
                    tempIndex++;
                    break;
                case "FDetailID#FFlex6#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)客户#名称(Null)");
                    tempIndex++;
                    break;
                case "FDetailID#FFlex5":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)部门#编码");
                    tempIndex++;
                    break;
                case "FDetailID#FFlex5#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)部门#名称(Null)");
                    tempIndex++;
                    break;
                case "FDetailID#FFlex4":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)供应商#编码");
                    tempIndex++;
                    break;
                case "FDetailID#FFlex4#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)供应商#名称(Null)");
                    tempIndex++;
                    break;
                case "FDetailID#FFLEX11":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)组织机构#编码");
                    tempIndex++;
                    break;
                case "FDetailID#FFLEX11#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)组织机构#名称(Null)");
                    tempIndex++;
                    break;
                case "FCURRENCYID":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(分录)币别#编码");
                    tempIndex++;
                    break;
                case "FCURRENCYID#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(分录)币别#名称");
                    tempIndex++;
                    break;
                case "FEXCHANGERATETYPE":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(分录)汇率类型#编码");
                    tempIndex++;
                    break;
                case "FEXCHANGERATETYPE#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)汇率类型#名称");
                    tempIndex++;
                    break;
                case "FAMOUNTFOR":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)原币金额");
                    tempIndex++;
                    break;
                case "FDEBIT":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)借方金额");
                    tempIndex++;
                    break;
                case "FCREDIT":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)贷方金额");
                    tempIndex++;
                    break;
            }
        }

        // 数据行起始索引
        int index = 1;

        // 行数据
        Hr_Certificate rowData = null;
        String textValue = null;

        // 在循环处理导出字段之前声明一个变量用于保存上一次的数据
        Hr_Certificate lastRowData = null;

        // 插入内容，遍历集合数据，产生数据行
        Iterator<Hr_Certificate> it = list.iterator();
        while (it.hasNext())
        {
            // 创建行索引
            index++;

            // 创建行
            row = sheet.createRow(index);

            // 获取一行数据
            rowData = (Hr_Certificate)it.next();

            String style = "data3";

            // 循环导出字段
            tempIndex = 0;
            for (int i = 0; i < hearList.size(); i++)
            {
                String field = hearList.get(i);
                switch (field)
                {
                    case "FBillHead(GL_VOUCHER)":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if (lastRowData != null && lastRowData.getRecordId().equals(rowData.getRecordId()))
                        {
                            textValue = null;
                        }
                        else
                        {
                            textValue = rowData.getRecordId();
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FAccountBookID":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if (lastRowData != null && lastRowData.getRecordId().equals(rowData.getRecordId()))
                        {
                            textValue = null;
                        }
                        else
                        {
                            textValue = rowData.getHrFinancialAccountbook() == null ? "" : rowData.getHrFinancialAccountbook().getNo();
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FAccountBookID#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if (lastRowData != null && lastRowData.getRecordId().equals(rowData.getRecordId()))
                        {
                            textValue = null;
                        }
                        else
                        {
                            textValue = rowData.getHrFinancialAccountbook() == null ? "" : rowData.getHrFinancialAccountbook().getName();
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDate":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if (lastRowData != null && lastRowData.getRecordId().equals(rowData.getRecordId()))
                        {
                            textValue = null;
                        }
                        else
                        {
                            textValue = rowData.getCreatedDateStr() == null ? "" : rowData.getCreatedDateStr();
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FVOUCHERGROUPID":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if (lastRowData != null && lastRowData.getRecordId().equals(rowData.getRecordId()))
                        {
                            textValue = null;
                        }
                        else
                        {
                            textValue = rowData.getHrStandardAccount() == null ? "" : rowData.getHrFinancialCurrency().getNumber();;
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FVOUCHERGROUPID#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if (lastRowData != null && lastRowData.getRecordId().equals(rowData.getRecordId()))
                        {
                            textValue = null;
                        }
                        else
                        {
                            textValue = rowData.getHrFinancialCertificateWord() == null ? "" : rowData.getHrFinancialCertificateWord().getCertificateWord();
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FVOUCHERGROUPNO":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if (lastRowData != null && lastRowData.getRecordId().equals(rowData.getRecordId()))
                        {
                            textValue = null;
                        }
                        else
                        {
                            textValue = rowData.getVoucherNumber() == null ? "" : rowData.getVoucherNumber();
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FACCBOOKORGID":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if (lastRowData != null && lastRowData.getRecordId().equals(rowData.getRecordId()))
                        {
                            textValue = null;
                        }
                        else
                        {
                            textValue = rowData.getHrFinancialOrganization() == null ? "" : rowData.getHrFinancialOrganization().getNo();
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FACCBOOKORGID#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        if (lastRowData != null && lastRowData.getRecordId().equals(rowData.getRecordId()))
                        {
                            textValue = null;
                        }
                        else
                        {
                            textValue = rowData.getHrFinancialOrganization() == null ? "" : rowData.getHrFinancialOrganization().getName();
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "*Split*1":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = null;
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FEntity":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrCertificateDetail() == null ? "" : rowData.getHrCertificateDetail().getRecordId();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FEXPLANATION":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        String query = null;
                        if (null != rowData.getHrFinancialAbstract() && null != rowData.getHrFinancialAbstract().getName()
                            && null != rowData.getHrCertificateDetail() && null != rowData.getHrCertificateDetail().getAbsractMessage())
                        {
                            query = rowData.getHrFinancialAbstract().getName() + rowData.getHrCertificateDetail().getAbsractMessage();
                        }
                        else
                        {
                            if (null != rowData.getHrFinancialAbstract() && null != rowData.getHrFinancialAbstract().getName())
                            {
                                query = rowData.getHrFinancialAbstract().getName();
                            }
                            if (null != rowData.getHrCertificateDetail() && null != rowData.getHrCertificateDetail().getAbsractMessage())
                            {
                                query = rowData.getHrCertificateDetail().getAbsractMessage();
                            }
                        }
                        textValue = query;
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FACCOUNTID":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrStandardAccountDetail() == null ? "" : rowData.getHrStandardAccountDetail().getNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FACCOUNTID#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrStandardAccountDetail() == null ? "" : rowData.getHrStandardAccountDetail().getName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDETAILIDCOMBINATION":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrCertificateDetail() == null ? "" : rowData.getHrCertificateDetail().getAccountingDimension();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FFLEX14":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialBank() == null ? "" : rowData.getHrFinancialBank().getNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FFLEX14#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialBank() == null ? "" : rowData.getHrFinancialBank().getName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FFLEX15":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialBankaccount() == null ? "" : rowData.getHrFinancialBankaccount().getBankaccount();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FFLEX15#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialBankaccount() == null ? "" : rowData.getHrFinancialBankaccount().getName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FF100005":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialIncomeclassify() == null ? "" : rowData.getHrFinancialIncomeclassify().getNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FF100005#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialIncomeclassify() == null ? "" : rowData.getHrFinancialIncomeclassify().getName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FFlex6":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialCustomer() == null ? "" : rowData.getHrFinancialCustomer().getNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FFlex6#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialCustomer() == null ? "" : rowData.getHrFinancialCustomer().getName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FFlex5":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialDepartment() == null ? "" : rowData.getHrFinancialDepartment().getNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FFlex5#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialDepartment() == null ? "" : rowData.getHrFinancialDepartment().getName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FFlex4":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialSupplier() == null ? "" : rowData.getHrFinancialSupplier().getNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FFlex4#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialSupplier() == null ? "" : rowData.getHrFinancialSupplier().getName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FFLEX11":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialOrganization() == null ? "" : rowData.getHrFinancialOrganization().getNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDetailID#FFLEX11#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialOrganization() == null ? "" : rowData.getHrFinancialOrganization().getName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FCURRENCYID":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialCurrency() == null ? "" : rowData.getHrFinancialCurrency().getNumber();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FCURRENCYID#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialCurrency() == null ? "" : rowData.getHrFinancialCurrency().getName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FEXCHANGERATETYPE":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialRateType() == null ? "" : rowData.getHrFinancialRateType().getNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FEXCHANGERATETYPE#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getHrFinancialRateType() == null ? "" : rowData.getHrFinancialRateType().getName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FAMOUNTFOR":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = null;
                        if (rowData.getHrCertificateDetail() != null && rowData.getHrCertificateDetail().getBorrowAmount() != null)
                        {
                            textValue = String.valueOf(rowData.getHrCertificateDetail().getBorrowAmount());
                        }
                        if (rowData.getHrCertificateDetail() != null && rowData.getHrCertificateDetail().getCreditAmount() != null)
                        {
                            textValue = String.valueOf(rowData.getHrCertificateDetail().getCreditAmount());
                        }
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDEBIT":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        BigDecimal borrowAmount = rowData.getHrCertificateDetail() == null ? null : rowData.getHrCertificateDetail().getBorrowAmount();
                        textValue = borrowAmount == null ? "" : String.valueOf(borrowAmount);
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FCREDIT":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        BigDecimal creditAmount = rowData.getHrCertificateDetail() == null ? null : rowData.getHrCertificateDetail().getCreditAmount();
                        textValue = creditAmount == null ? "" : String.valueOf(creditAmount);
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                }
            }
            lastRowData = rowData;
        }
    }




    public ExportOther exportCertificateDetail(List<Hr_ManualBill> list, List<String> hearList)
            throws Exception
    {
        this.wb = new XSSFWorkbook();
        this.styles = createStyles(wb);
        this.sheet = this.wb.createSheet("凭证#单据头(FBillHead)");

        generaCertificateDetail(list,hearList);

        return this;
    }

    public void generaCertificateDetail(List<Hr_ManualBill> list, List<String> hearList) throws Exception
    {
        // 合并第一行的列为一个单元格
        /*int size = hearList.size();
        size = size + (hearList == null ? 0 : hearList.size() - 1);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, size - 1));*/

        XSSFCell cell;
        XSSFRow row;

        // 产生标题行
        int rowIndex = 0;
        row = sheet.createRow((short)rowIndex);
        int tempIndex = 0;
        for (short i = 0; i < hearList.size(); i++)
        {
            if (i == hearList.size() - 1)
            {
                sheet.setColumnWidth(tempIndex, 8 * 800);
            }
            else
            {
                sheet.setColumnWidth(tempIndex, 5 * 800);
            }
            cell = row.createCell(tempIndex);
            cell.setCellStyle(styles.get("header"));
            cell.setCellValue(hearList.get(i));

            tempIndex++;
        }

        // 数据为空不再处理
        if (list == null)
        {
            return;
        }

        tempIndex = 0;
        row = sheet.createRow((short)1);
        for (int i = 0; i < hearList.size(); i++)
        {
            String field = hearList.get(i);
            switch (field)
            {
                case "FBillHead(GL_VOUCHER)":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*单据头(序号)");
                    tempIndex++;
                    break;
                case "FAccountBookID":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(单据头)账簿#编码");
                    tempIndex++;
                    break;
                case "FAccountBookID#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(单据头)账簿#名称");
                    tempIndex++;
                    break;
                case "FDate":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(单据头)日期");
                    tempIndex++;
                    break;
                case "FPERIOD":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(单据头)期间");
                    tempIndex++;
                    break;
                case "FVOUCHERGROUPID":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(单据头)凭证字#编码");
                    tempIndex++;
                    break;
                case "FVOUCHERGROUPID#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(单据头)凭证字#名称");
                    tempIndex++;
                    break;
                case "FVOUCHERGROUPNO":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(单据头)凭证号");
                    tempIndex++;
                    break;
                case "FACCBOOKORGID":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(单据头)核算组织#编码");
                    tempIndex++;
                    break;
                case "FACCBOOKORGID#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(单据头)核算组织#名称");
                    tempIndex++;
                    break;
                case "FSystemID":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(单据头)来源系统#编码");
                    tempIndex++;
                    break;
                case "FSystemID#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(单据头)来源系统#名称");
                    tempIndex++;
                    break;
                case "*Split*1":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("间隔列");
                    tempIndex++;
                    break;
                case "FEntity":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*分录(序号)");
                    tempIndex++;
                    break;
                case "FEXPLANATION":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)摘要");
                    tempIndex++;
                    break;
                case "FACCOUNTID":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(分录)科目编码#编码");
                    tempIndex++;
                    break;
                case "FACCOUNTID#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)科目编码#名称");
                    tempIndex++;
                    break;
                case "FAcctFullName":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)科目全名");
                    tempIndex++;
                    break;
                case "FDETAILIDCOMBINATION":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)核算维度组合");
                    tempIndex++;
                    break;
                case "FCURRENCYID":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(分录)币别#编码");
                    tempIndex++;
                    break;
                case "FCURRENCYID#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(分录)币别#名称");
                    tempIndex++;
                    break;
                case "FEXCHANGERATETYPE":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("*(分录)汇率类型#编码");
                    tempIndex++;
                    break;
                case "FEXCHANGERATETYPE#Name":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)汇率类型#名称");
                    tempIndex++;
                    break;
                case "FAMOUNTFOR":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)原币金额");
                    tempIndex++;
                    break;
                case "FDEBIT":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)借方金额");
                    tempIndex++;
                    break;
                case "FCREDIT":
                    cell = row.createCell(tempIndex);
                    cell.setCellStyle(styles.get("data"));
                    cell.setCellValue("(分录)贷方金额");
                    tempIndex++;
                    break;
            }
        }

        // 数据行起始索引
        int index = 1;

        // 行数据
        Hr_ManualBill rowData = null;
        String textValue = null;

        // 在循环处理导出字段之前声明一个变量用于保存上一次的数据
        Hr_ManualBill lastRowData = null;

        int test = 2;
        // 插入内容，遍历集合数据，产生数据行
        Iterator<Hr_ManualBill> it = list.iterator();
        while (it.hasNext())
        {
            // 创建行索引
            index++;
            test++;
            // 创建行
            row = sheet.createRow(index);

            // 获取一行数据
            rowData = (Hr_ManualBill)it.next();

            String style = "data3";

            // 循环导出字段
            tempIndex = 0;
            for (int i = 0; i < hearList.size(); i++)
            {
                String field = hearList.get(i);
                switch (field)
                {
                    case "FBillHead(GL_VOUCHER)":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getMajorNoId() == null ? "" : rowData.getMajorNoId();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FAccountBookID":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getBookNo() == null ? "" : rowData.getBookNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FAccountBookID#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getAccountbookName() == null ? "" : rowData.getAccountbookName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDate":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getCertificateTime() == null ? "" : rowData.getCertificateTime();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FPERIOD":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getPeriod() == null ? "" : rowData.getPeriod();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FVOUCHERGROUPID":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getCertificateWordNo() == null ? "" : rowData.getCertificateWordNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FVOUCHERGROUPID#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getCertificateWordName() == null ? "" : rowData.getCertificateWordName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FVOUCHERGROUPNO":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getVoucherNo() == null ? "" : rowData.getVoucherNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FACCBOOKORGID":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getDistributionIds() == null ? "": rowData.getDistributionIds();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FACCBOOKORGID#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getUseOrganizationName() == null ? "" : rowData.getUseOrganizationName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FSystemID":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = "";
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FSystemID#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getSourceSystem() == null ? "" : rowData.getSourceSystem();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "*Split*1":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = null;
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FEntity":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getSubdirectoryId() == null ? "" : rowData.getSubdirectoryId();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FEXPLANATION":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getTogetherMessage() == null ? "" : rowData.getTogetherMessage();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FACCOUNTID":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getSubjectNo() == null ? "" : rowData.getSubjectNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FACCOUNTID#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getSubjectName() == null ? "" : rowData.getSubjectName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FAcctFullName":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = "";
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDETAILIDCOMBINATION":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getAccountDimension() == null ? "" : rowData.getAccountDimension();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FCURRENCYID":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getCurrencyNo() == null ? "" : rowData.getCurrencyNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FCURRENCYID#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getCurrencyName() == null ? "" : rowData.getCurrencyName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FEXCHANGERATETYPE":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getExchangeRateTypeNo() == null ? "" : rowData.getExchangeRateTypeNo();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FEXCHANGERATETYPE#Name":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getExchangeRateTypeName() == null ? "" : rowData.getExchangeRateTypeName();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;

                    case "FAMOUNTFOR":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        BigDecimal originalAmount = rowData.getBorrowAmount() == null ? rowData.getLoanAmount() : rowData.getBorrowAmount();
                        textValue = originalAmount.toString();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FDEBIT":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getBorrowAmount() == null ? "" : rowData.getBorrowAmount().toString();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                    case "FCREDIT":
                        cell = row.createCell(tempIndex);
                        cell.setCellStyle(styles.get(style));
                        textValue = rowData.getLoanAmount() == null ? "" : rowData.getLoanAmount().toString();
                        cell.setCellValue(textValue);
                        tempIndex++;
                        break;
                }
            }
            lastRowData = rowData;
        }
    }
    
    /**
     * 输出数据流
     * 
     * @param os 输出数据流
     */
    public ExportOther write(OutputStream os)
        throws IOException
    {
        wb.write(os);
        return this;
    }
    
    /**
     * 输出到客户端
     * 
     * @param fileName 输出文件名
     */
    public ExportOther write(HttpServletResponse response, String fileName)
        throws IOException
    {
        response.reset();
        response.setContentType("application/octet-stream; charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + Encodes.urlEncode(fileName));
        write(response.getOutputStream());
        return this;
    }
    
    /**
     * 输出到文件
     * 
     * @param fileName 输出文件名
     */
    public ExportOther writeFile(String name)
        throws FileNotFoundException, IOException
    {
        FileOutputStream os = new FileOutputStream(name);
        this.write(os);
        return this;
    }
    
    /**
     * 清理临时文件
     * 
     * @throws IOException
     */
    public ExportOther dispose()
        throws IOException
    {
        ((Closeable)wb).close();
        return this;
    }
}
