const produceTypeList = {
	template:'#produceTypeList',
	// 初始化加载方法
	created:function(){
		document.querySelector('body').setAttribute('style', 'background-color:#FFFFFF;font-family: 华文楷体;color:#333;')
		this.$store.dispatch('myStore/setUserInformation');
	},
	computed: {
		userErp:{
			get () {
				return this.$store.state.myStore.userErp;
			}
		},
		userInformation:{
			get () {
				return this.$store.state.myStore.userInformation;
		    }
		}
	},
	watch:{
		userErp:function(){
			var company = eval('(' + window.localStorage.getItem('company') + ')');
			if(company)
			{
				this.company = company;
			}
			else
			{
				this.company = this.userErp;
			}
			this.loadData();
		}
	},
	data(){
		return{
			company: {},
			coutList: [],
			bottleneckProcessesList:[],
			craftNo: "",
			cetageName: "",
			allArea: 0,
			allPcs: 0
		};
	},
	// 方法编写
	methods:{
		loadData:function()
		{
			this.bottleneckProcessesList = [];
			if(this.company && this.company.userId){
				// 加载当天的过数记录
				$('#loadingModal').modal();
				const pro = {};
				pro.company = this.company;
				pro.userId = this.company.userId;
				pro.craftNo = this.craftNo;
				const _this = this;
				$.ajax({
					type:"post",
					url:ctx + "/f/wechat/produce/loadBottleneckProcessesList",
					data:JSON.stringify(pro),
					contentType:"application/json",
					success:function(data)
					{
						_this.bottleneckProcessesList = data;
						$('#loadingModal').modal('hide');
					}
				});
			}
		},
	}
};