package com.kyb.pcberp.modules.purch.service;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.crm.entity.ReceiveTaskAttach;
import com.kyb.pcberp.modules.hr.finance_center.pojo.Hr_CollectPayBill;
import com.kyb.pcberp.modules.oa.utils.ApprovalEngineUtil;
import com.kyb.pcberp.modules.purch.dao.PaymentTaskDao;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import com.kyb.pcberp.modules.wechat.dao.WechatOaDao;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.*;
import com.kyb.pcberp.modules.wechat.service.KybSoftOaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class PaymentTaskService {

    @Autowired
    private PaymentTaskDao paymentTaskDao;

    @Autowired
    private WechatOaDao wechatOaDao;

    @Autowired
    private KybSoftOaService kybSoftOaService;

    public Page<Hr_CollectPayBill> getList(Page<Hr_CollectPayBill> page, Hr_CollectPayBill hr_collectPayBill)
    {
//        hr_collectPayBill.setPage(page);
        Integer pageSize = page.getPageSize();
        Integer pageNo = page.getPageNo();
        List<Hr_CollectPayBill> paymentableList = Lists.newArrayList();
        //查询应付单
        List<Hr_CollectPayBill> singlePaymentableList = paymentTaskDao.getSinglePayableListTwo(hr_collectPayBill);
        if (Collections3.isNotEmpty(singlePaymentableList))
        {
            for(Hr_CollectPayBill payBill : singlePaymentableList)
            {
                if (payBill.getAmount().compareTo(BigDecimal.ZERO) > 0 || StringUtils.isNotBlank(payBill.getSettlementStatus()))
                {
                    paymentableList.add(payBill);
                }
            }
        }
        page.setCount(paymentableList.size());
        List<Hr_CollectPayBill> listTwo  =new ArrayList<>();
        Integer num = (page.getPageNo()-1) * page.getPageSize();
        for(int i = num; i < paymentableList.size();i++){
            if (listTwo.size() == page.getPageSize()){
                break;
            }
            listTwo.add(paymentableList.get(i));
        }
        page.setList(listTwo);
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        return page;
    }

    @Transactional(readOnly = false)
    public String commitAudit(Hr_CollectPayBill hr_collectPayBill)
    {
        // 查询用户，赋予信息
        User us = UserUtils.getUser();
        us.setCompany(us.getCompany());
        WechatUser wechatUser = wechatOaDao.getEmployeeByUser(us);
        if (!(wechatUser != null && StringUtils.isNotBlank(wechatUser.getRecordId()) && StringUtils.isNotBlank(
                wechatUser.getDepartId()) && StringUtils.isNotBlank(wechatUser.getGroupId())))
        {
            // 查询兼职分配的
            wechatUser = wechatOaDao.getEmployeeInnerByUser(us);
            if (!(wechatUser != null && StringUtils.isNotBlank(wechatUser.getRecordId()) && StringUtils.isNotBlank(
                    wechatUser.getDepartId()) && StringUtils.isNotBlank(wechatUser.getGroupId())))
            {
                return "用户信息获取不到，请确认OA系统中你是否在该公司";
            }
        }
        // 获取正在等待的审批
        WechatAudit wechatAudit = new WechatAudit();
        // 适配器转换
        wechatAudit = paymentTaskToWechatAudit(hr_collectPayBill, wechatUser, wechatAudit);
//        wechatAudit.setMoney(wechatAudit.getExpensePrice());
        wechatOaDao.commitAudit(wechatAudit);

        // 添加附件表
        if (Collections3.isNotEmpty(hr_collectPayBill.getAttacheList()))
        {
            for (ReceiveTaskAttach attachements : hr_collectPayBill.getAttacheList())
            {
                //保存OA审批附件信息
                WechatAttachments wechatAttach = new WechatAttachments();
                wechatAttach.setFileUrl(attachements.getFileUrl());
                wechatAttach.setRecordId(wechatAudit.getRecordId());
                wechatAttach.setOrgFileName(attachements.getOrgFileName());
                wechatOaDao.insertAuditAttach(wechatAttach);

                // 保存发票附件信息
                ReceiveTaskAttach attach = new ReceiveTaskAttach();
                attach.setCompany(UserUtils.getUser().getCompany());
                attach.setFileUrl(attachements.getTempUrl());
                attach.setAuditId(wechatAudit.getRecordId());
                attach.setType(1);
                attach.setRealFileName(attachements.getFileUrl());
                attach.setOrgFileName(attachements.getOrgFileName());
                attach.setAuditAttachId(wechatAttach.getRecordId());
                attach.preInsert();
                paymentTaskDao.saveReceiveAttachement(attach);
            }
        }

        oaNumber(wechatAudit.getRecordId(), wechatAudit.getAuditType());
        commitToApprove(wechatAudit);

        //更改审批单状态
        hr_collectPayBill.setSettlementStatus("2002");
        paymentTaskDao.updatePayableStatus(hr_collectPayBill);
        return "已为您自动生成OA审批单";
    }

    @Transactional(readOnly = false)
    public String commitToApprove(WechatAudit wechatAudit)
    {
        WechatCommitApprove commitApprove = new WechatCommitApprove();
        commitApprove.setAuditId(wechatAudit.getRecordId());
        commitApprove.setOpertionUserId(wechatAudit.getEmpId());
        commitApprove.setCommitPersonAllId(wechatAudit.getOaDepartId());
        ApprovalEngineUtil.commitApprove(commitApprove);
        return commitApprove.getResult();
    }


    public WechatAudit paymentTaskToWechatAudit(Hr_CollectPayBill hr_collectPayBill, WechatUser wechatUser,
                                                WechatAudit wechatAudit)
    {
        if (wechatAudit == null)
        {
            wechatAudit = new WechatAudit();
        }
//        wechatAudit.setAuditResult(hr_collectPayBill.getSettlementReason());
//        wechatAudit.setExpensePrice(hr_collectPayBill.getSettlementAmount().toString()); //结案金额
//        wechatAudit.setPayObject(hr_collectPayBill.getSupplierName());
//        if (StringUtils.isBlank(wechatAudit.getRecordId()))
//        {
//            wechatAudit.setApplicationsType(TypeKey.APPLICATION_AUDITING);
//            wechatAudit.setPaymentTaskId(hr_collectPayBill.getRecordId());
//            wechatAudit.setAuditType(TypeKey.OAAUDDITTYPE_PAYMENTTASK);
//            wechatAudit.setEmpId(wechatUser.getRecordId());
//            wechatAudit.setGroupId(wechatUser.getDepartId());
//            String[] strs = wechatUser.getGroupId().split(",");
//            wechatAudit.setOaDepartId(strs[strs.length - 1]);
//        }
//        wechatAudit.setPaymentTaskId(hr_collectPayBill.getRecordId());
        return wechatAudit;
    }


    @Transactional(readOnly = false)
    public void oaNumber(String id, String type) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        String dt = format.format(date);
        WechatOaNumber number = new WechatOaNumber();
        number.setId(id);
        number.setType(type);
        number.setDate(dt);
        String no = "";
        String noend = "";
        Integer i = wechatOaDao.findNumber(number);
        if (null != i && i > 0) {
            i = i + 1;
        } else {
            i = 1;
        }
        if (i < 10) {
            noend = "00" + i;
        } else if (i < 100) {
            noend = "0" + i;
        } else {
            noend = "" + i;
        }
        no = dt + noend;
        number.setNo(no);
        number.setNum(noend);
        wechatOaDao.saveNumber(number);
    }

    public List<Hr_CollectPayBill> loadSettlementDetailList(Hr_CollectPayBill hr_collectPayBill)
    {
        return paymentTaskDao.loadSettlementDetailList(hr_collectPayBill);
    }

    @Transactional(readOnly = false)
    public void saveReceiveAttachement(ReceiveTaskAttach receiveTaskAttach, String savePath, String realFileName,
                                       String originalFilename)
    {
        //保存OA审批附件信息
        WechatAttachments wechatAttach = new WechatAttachments();
        wechatAttach.setFileUrl(realFileName + originalFilename);
        wechatAttach.setRecordId(receiveTaskAttach.getRecordId());
        wechatAttach.setOrgFileName(originalFilename);
        wechatOaDao.insertAuditAttach(wechatAttach);

        // 保存发票附件信息
        ReceiveTaskAttach attach = new ReceiveTaskAttach();
        attach.setCompany(UserUtils.getUser().getCompany());
        attach.setFileUrl(savePath);
        attach.setAuditId(receiveTaskAttach.getRecordId());
        attach.setType(1);
        attach.setRealFileName(realFileName + originalFilename);
        attach.setOrgFileName(originalFilename);
        attach.setAuditAttachId(wechatAttach.getRecordId());
        attach.preInsert();
        paymentTaskDao.saveReceiveAttachement(attach);

    }

    public List<ReceiveTaskAttach> findPayApplicationInvoices(ReceiveTaskAttach receiveTaskAttach)
    {
        return paymentTaskDao.findPayApplicationInvoices(receiveTaskAttach);
    }


    public void delete(ReceiveTaskAttach receiveTaskAttach)
    {
        //获取对应的审批附件Id
        String auditAttachId = paymentTaskDao.getAuditAttachId(receiveTaskAttach);
        paymentTaskDao.delete(receiveTaskAttach);
        //删除OA附件信息
        WechatAttachments wechatAttach = new WechatAttachments();
        wechatAttach.setRecordId(auditAttachId);
        wechatOaDao.delAuditFile(wechatAttach);
    }

}
