<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.eg.dao.SysProcessCraftDefValueDao">

	<sql id= "sysCraftDefValueColumns">
			a.recordId,
			a.companyId AS "company.recordId",
			a.sysCraftId AS "craft.recordId",
			a.defaultValue,
			a.createdBy AS "createdBy.recordId",
			a.createdDate,
			a.lastUpdBy AS "lastUpdBy.recordId",
			a.lastUpdDate
	</sql>
	
	<insert id = "insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO eg_process_syscraft_defaultvalue (
			companyId,
			sysCraftId,
			defaultValue,
			activeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate
		)VALUES(
			#{company.recordId},
			#{craft.recordId},
			#{defaultValue},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate}
		)

	</insert>

	<update id = "update">
		UPDATE eg_process_syscraft_defaultvalue SET
			companyId = #{company.recordId},
			sysCraftId = #{craft.recordId},
			defaultValue = #{defaultValue},
			createdBy = #{createdBy.recordId},
			createdDate = #{createdDate},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
	</update>
	
	<update id="delete">
		UPDATE eg_process_syscraft_defaultvalue SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="findSysDefValueByCompanyAndCraft" resultType = "SysProcessCraftDefValue">
		SELECT 
			<include refid = "sysCraftDefValueColumns"/>
		FROM eg_process_syscraft_defaultvalue a
		WHERE a.companyId = #{company.recordId}
		AND a.sysCraftId = #{craft.recordId} limit 0,1
	</select>
	
	<update id="UpdateDefValue">
		UPDATE eg_process_syscraft_defaultvalue SET
			defaultValue = #{defaultValue},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="findSysDefValueByCompany" resultType = "SysProcessCraftDefValue">
		SELECT 
			<include refid = "sysCraftDefValueColumns"/>
		FROM eg_process_syscraft_defaultvalue a
		WHERE a.companyId = #{recordId}
	</select>
</mapper>