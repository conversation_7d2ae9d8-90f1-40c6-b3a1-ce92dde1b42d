<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.crm.dao.CustomerProductTypeDao">

    <select id="getList" resultType="CustomerProductType">
        SELECT
            b.`value` AS "customerGradeValue",
            c.`value` AS "productTypeValue",
            a.*
        FROM md_customer_productType a
        LEFT JOIN md_dict_value b ON b.recordId = a.customerGrade
        LEFT JOIN md_dict_value c ON c.recordId = a.productTypeId
        WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1
        ORDER BY a.customerGrade,a.productTypeId ASC
    </select>

    <insert id="batchInsert">
        INSERT INTO md_customer_productType
        (
            companyId,
            customerGrade,
            productTypeId,
            rate,
            activeFlag,
            createdBy,
            createdDate
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.company.recordId},
            #{item.customerGrade},
            #{item.productTypeId},
            #{item.rate},
            1,
            #{item.createdBy.recordId},
            #{item.createdDate}
        )
        </foreach>
    </insert>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE md_customer_productType SET
                customerGrade = #{item.customerGrade},
                productTypeId = #{item.productTypeId},
                rate = #{item.rate},
                lastUpdBy = #{item.lastUpdBy.recordId},
                lastUpdDate = #{item.lastUpdDate}
            WHERE recordId = #{item.recordId}
        </foreach>
    </update>

    <update id="batchDelete">
        UPDATE md_customer_productType SET
        activeFlag = 0
        WHERE activeFlag = 1 AND
        (
            recordId
        ) IN
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item.recordId}
        </foreach>
    </update>

    <select id="getCustomerProductType" resultType="CustomerProductType">
        SELECT
            a.*,
            CAST(rate AS char)+0 AS "rateValue"
        FROM md_customer_productType a
        WHERE a.companyId = #{companyId} AND a.productTypeId = #{productTypeId} AND a.customerGrade = #{customerGrade} AND a.activeFlag = 1
        LIMIT 1
    </select>
</mapper>