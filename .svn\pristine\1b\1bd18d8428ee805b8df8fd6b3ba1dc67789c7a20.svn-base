<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.production.dao.ProduceStatisticsDao">
    
	<sql id="produceStatisticsColumns">
		a.recordId,
		a.companyId AS "company.recordId",
		a.processid AS "process.recordId",
		a.month,
		a.monthCountA,
		a.monthCountB,
		a.operator AS "operator.recordId",
		a.activeFlag,
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.remark
	</sql>
	
	<sql id="produceStatisticsJoins">
	</sql>
    
	<select id="get" resultType="ProduceStatistics">
		SELECT 
			<include refid="produceStatisticsColumns"/>
		FROM pd_produce_statistics a
		<include refid="produceStatisticsJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="findList" resultType="ProduceStatistics">
		SELECT 
			<include refid="produceStatisticsColumns"/>
		FROM pd_produce_statistics a
		<include refid="produceStatisticsJoins"/>
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}	
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="ProduceStatistics">
		SELECT 
			<include refid="produceStatisticsColumns"/>
		FROM pd_produce_statistics a
		<include refid="produceStatisticsJoins"/>
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findByProcessAndMonth" resultType="ProduceStatistics">
		SELECT 
			<include refid="produceStatisticsColumns"/>
		FROM pd_produce_statistics a
		<include refid="produceStatisticsJoins"/>
		<where>
		    a.companyId = #{stat.company.recordId} 
		    AND a.processId = #{stat.process.recordId} 
		    AND a.month between #{startTime} and #{endTime} 
		</where>		
	</select>
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO pd_produce_statistics(
			companyId,
			processId,
			month,
			monthCountA,
			monthCountB,
			operator,
			activeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			remark
		) VALUES (
			#{company.recordId},
			#{process.recordId},
			#{month},
			#{monthCountA},
			#{monthCountB},
			#{operator.recordId},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark}
		)
	</insert>
	
	<update id="update">
		UPDATE pd_produce_statistics SET 	
			processid = #{process.recordId},
			month = #{month},
			monthcountA = #{monthCountA},
			monthcountB = #{monthCountB},
			operator = #{operator.recordId},
			lastupdby = #{lastUpdBy.recordId},
			lastupddate = #{lastUpdDate},
			remark = #{remark}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE pd_produce_statistics SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>
	
</mapper>