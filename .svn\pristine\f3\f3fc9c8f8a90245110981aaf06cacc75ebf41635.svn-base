/* Setup general page controller */
kybApp.controller('inventoryCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil','BaseUtil','$http',function($rootScope, $scope, upida, $timeout,CommonUtil,BaseUtil,$http) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        MainCtrl.initAjax();
        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
    var vm = this;
    
    vm.help = function () {
        if (vm.tabs.snapshotForm.active) {
            //主界面
            vm.helpList();
        } else if (vm.tabs.material.active) {
            //库存盘点物料列表
            vm.helpDetail();
        } else if (vm.tabs.snapDeail.active) {
            //库存盘点物料明细
            vm.helpInventoryDetail();
        }else if(vm.tabs.materialDeail.active){
        	//物料详细
        	vm.helpMaterDeail();
        }
    };
    
    vm.shouldAutoStart = false;
    vm.InvenListOptions = {
            steps: [
                {
                    element: '#InvenStep1',
                    intro: "您可以从库存盘点日期中选择要查询的库存盘点时间段，然后再点击查询就能查出这个时间段的库存盘点！",
                    position: 'top'
                },
                {
                    element: '#InvenStep2',
                    intro: "当你点击开始盘点的时候，会给你自动备份一份当前物料列表，生产过诉，出入库，品检将不能使用，按钮也会变成盘点完成按钮，只有点击盘点完成才会恢复上述功能的使用！",
                    position: 'bottom'
                },
                {
                    element: '#InvenStep3',
                    intro: '此处为您历史库存盘点的时间，可以通过点击查看库存盘点来查看具体的物料库存！',
                    position: 'top'
                },
                {
                    element: '#InvenStep4',
                    intro: '此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！',
                    position: 'top'
                },
                {
                    element: '#InvenStep5',
                    intro: '此处为分页区域，您可以点击您想查看的页面！',
                    position: 'bottom'
                },
                {
                    element: '#InvenStep',
                    intro: '谢谢使用，再见。'
                }
            ],
            showStepNumbers: false,
            exitOnOverlayClick: true,
            exitOnEsc: true,
            nextLabel: '<strong>下一步!</strong>',
            prevLabel: '<span style="color:green">上一步</span>',
            skipLabel: '退出',
            doneLabel: '谢谢'
        };
    
    vm.InvenMaterOptions = {
            steps: [
                {
                    element: '#InvenMasterStep1',
                    intro: "输入查询条件，点击查询，您可以查询到你想要的物料盘点信息",
                    position: 'top'
                },
                {
                    element: '#InvenMasterStep2',
                    intro: "点击查看明细可以查看库存盘点后变动的物料信息！",
                    position: 'bottom'
                },
                {
                    element: '#InvenMasterStep3',
                    intro: '库存盘点时的物料列表！',
                    position: 'top'
                },
                {
                    element: '#InvenMasterStep4',
                    intro: '此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！',
                    position: 'top'
                },
                {
                    element: '#InvenMasterStep5',
                    intro: '此处为分页区域，您可以点击您想查看的页面！',
                    position: 'bottom'
                },
                {
                    element: '#InvenMasterStep',
                    intro: '谢谢使用，再见。'
                }
            ],
            showStepNumbers: false,
            exitOnOverlayClick: true,
            exitOnEsc: true,
            nextLabel: '<strong>下一步!</strong>',
            prevLabel: '<span style="color:green">上一步</span>',
            skipLabel: '退出',
            doneLabel: '谢谢'
        };
    
    vm.InvenDeailOptions = {
            steps: [
                {
                    element: '#InvenDeailStep1',
                    intro: "此处为库存盘点后变动的物料信息！",
                    position: 'bottom'
                },
                {
                    element: '#InvenDeailStep2',
                    intro: '此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！',
                    position: 'top'
                },
                {
                    element: '#InvenDeailStep3',
                    intro: '此处为分页区域，您可以点击您想查看的页面！',
                    position: 'bottom'
                },
                {
                    element: '#InvenDeailStep',
                    intro: '谢谢使用，再见。'
                }
            ],
            showStepNumbers: false,
            exitOnOverlayClick: true,
            exitOnEsc: true,
            nextLabel: '<strong>下一步!</strong>',
            prevLabel: '<span style="color:green">上一步</span>',
            skipLabel: '退出',
            doneLabel: '谢谢'
        };
    
    vm.materDeailOptions = {
            steps: [
                {
                    element: '#infoStep1',
                    intro: "此处为物料的详细信息！",
                    position: 'bottom'
                },
                {
                    element: '#infoStep',
                    intro: '谢谢使用，再见。'
                }
            ],
            showStepNumbers: false,
            exitOnOverlayClick: true,
            exitOnEsc: true,
            nextLabel: '<strong>下一步!</strong>',
            prevLabel: '<span style="color:green">上一步</span>',
            skipLabel: '退出',
            doneLabel: '谢谢'
        };
    
    vm.printTitle = "打印内容：当前有库存成品";
    
    // 权限
    vm.right = {};
    
    //物料种类的下拉值
    vm.materialKinds=[];
    
    // 分页数据
    vm.page = {};
    
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    
    // 库存盘点分页数据
    vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.historyData = {};
    vm.page.pageSize = 50;
    vm.page.pageNo = 1;
    vm.page.url = "stock/inventory/page";
    vm.page.condition = []; // 条件
    
    // 查询库存盘点时的数据
    vm.page.materData = {}; //物料列表的数据
    vm.page.materUrl = "stock/inventory/getMaterList";
    vm.page.materCondition = []; // 条件
    
    //查询WIP库存盘点时的数据
    vm.page.wipCopyUrl = "stock/inventory/getWipCopyList";
    
    //查询盘点明细的条件
    vm.page.materDataDeail = {}; //物料列表的数据
    vm.page.materUrlDeail = "stock/inventory/getSnapDifferenceList";
    vm.page.materConditionDeail = []; // 条件
    
    vm.page.diffWipCopyUrl = "stock/inventory/getWipSnapDiffList";
    
    vm.page.materDataHandle = {}; //物料列表的数据
    
    // 查询条件参数
    vm.query = {}; // 查询对象
    
    // 排序字段
    vm.query.sort = {};
    vm.query.sort.name = "orderBy";
    vm.query.sort.value = "snapshotDate DESC";
    
    //查询库存盘点的条件
    vm.query.sentTimeStartQr = {};
    vm.query.sentTimeStartQr.name = "sentTimeStartQr";
    vm.query.sentTimeStartQr.value = "";
    vm.query.sentTimeEndQr = {};
    vm.query.sentTimeEndQr.name = "sentTimeEndQr";
    vm.query.sentTimeEndQr.value = "";
    
    //根据时间查询物料列表的条件
    vm.query.snapshotDate = {};
    vm.query.snapshotDate.name = "snapshotDate";
    vm.query.snapshotDate.value = "";
    
    vm.query.snapMaterFlag = {};
    vm.query.snapMaterFlag.name = "snapMaterFlag";
    vm.query.snapMaterFlag.value = "";
    
    vm.query.snapCustomerNo = {};
    vm.query.snapCustomerNo.name = "customerNo";
    vm.query.snapCustomerNo.value = "";
    
    vm.query.snapNo = {};
    vm.query.snapNo.name = "no";
    vm.query.snapNo.value = "";
    
    vm.query.snapName = {};
    vm.query.snapName.name = "name";
    vm.query.snapName.value = "";
    
    vm.query.snapMaterialKind = {};
    vm.query.snapMaterialKind.name = "materialKind";
    vm.query.snapMaterialKind.value = "";
    
    vm.query.snapMaterialType = {};
    vm.query.snapMaterialType.name = "materialType";
    vm.query.snapMaterialType.value = "";
    
    vm.query.invenFlag = "";
    
    vm.query.snapIsIncludeStock = {};
    vm.query.snapIsIncludeStock.value = false;
    vm.query.snapIsIncludeStock.name = "safetystock";
    
    // tabs控制
    vm.tabs = {
    	snapshotForm: {active:true},
    	material: {active:false, show: false},
    	snapDeail: {active:false, show: false},
    	materialDeail: {active:false, show: false},
    	snapSchde: {active:false, show: false},
    	inOutDeail:{active:false, show: false}
    };
    
    // 查询库存盘点数据
    vm.doQuery = function(){
        // 设置查询条件
        var condition = [];

        vm.page.pageNo = 1;
        vm.page.condition = condition;
        //未完成
        vm.page.condition.push({
            name: "dealFlag",
            value: 10000
        });
        // 查询数据
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url,1);
    };
    
 // 查询库存盘点数据
    vm.doQueryH = function(){
        // 设置查询条件
        var condition = [];
        
        if(vm.startMonth){
        	condition.push({
        		name:"startMonth",
        		value:vm.startMonth
        	});
        }
        
        if(vm.endMonth){
        	condition.push({
        		name:"endMonth",
        		value:vm.endMonth
        	});
        }
        
        vm.page.pageNo = 1;
        vm.page.condition = condition;
        //未完成
        vm.page.condition.push({
            name: "dealFlag",
            value: 4
        });
        // 查询数据
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url,4);
    };
    
    /*
     * 兼容条件查询与普通查询
     * no 当前页
     * size 当前页显示数量
     * condition 是数组，可放入多个条件对象，如{name:"type", value:"1"}  名称对应实体字段名称，值对应页面输入的值   
     * url 请求数据链接
     */
    vm.cateNameList = [];
    vm.init = function (no, size, condition, url,state) {
    	MainCtrl.blockUI({
    		//进度条展示
            animate: true,
    	});
        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;
        // 设置过滤条件
        condition.push({
            name: vm.query.sort.name,
            value: vm.query.sort.value
        });
        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condition);

        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};
            // 如果结果为空
            if (typeof result === 'undefined' || typeof result.list === 'undefined') {
                data.pageNo = 1;
                data.pageSize = 10;
                data.list = [];
                data.startCount = 0;
                data.endCount = 0;
            } else {
            	if(state == 2)
            	{
            		for(var i=0;i<result.list.length;i++){
                		if(result.list[i].materialType!=null&&result.list[i].materialType.recordId!=null){
                			result.list[i].materialType=CommonUtil.getObjectById(result.list[i].materialType.recordId, vm.materialTypes);
                		}
                		if(result.list[i].unit!=null&&result.list[i].unit.recordId!=null){
                			result.list[i].unit=CommonUtil.getObjectById(result.list[i].unit.recordId, vm.units);
                		}
                		if(result.list[i].storehouse!=null&&result.list[i].storehouse.recordId!=null){
                			result.list[i].storehouse=CommonUtil.getObjectById(result.list[i].storehouse.recordId, vm.storehouse);
                		}
                		
                		
                	}
            		
            		
            	}
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }
            
            if(state == 1)
            {
            	vm.page.data = data;
            }
            else if(state == 2)
            {
            	if(vm.addRedDataFlag){
            		for(var i = 0 ;i<vm.addRedData.length;i++){
            			data.list.unshift(vm.addRedData[i]);
					}
            		vm.addRedDataFlag = false;
            		vm.addRedData = [];
            	}
            	vm.page.materData = data;
            }
            else if(state == 3)
            {
            	vm.page.materDataDeail = data;
            }
            else if(state == 4)
            {
            	vm.page.historyData = data;
            }
            else if(state == 5)
            {
            	vm.page.materDataHandle = data;
            }
            //进度条关闭
            MainCtrl.unblockUI();
        });
    };
    
    //初始化盘点数据
    vm.initInventory = function ()
    {
    	 
          //未完成
          vm.page.condition.push({
              name: "dealFlag",
              value: 10000
          });
          vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url,1);	
         /* if (vm.time.start) {
            	vm.page.condition.push({
                    name: vm.query.sentTimeStartQr.name,
                    value: vm.time.start.valueOf()
                });
                vm.query.sentTimeStartQr.value = vm.time.start.valueOf();
            }
            //订单日期
            if (vm.time.end) {
            	vm.page.condition.push({
                    name: vm.query.sentTimeEndQr.name,
                    value: vm.time.end.valueOf()
                });
                vm.query.sentTimeEndQr.value = vm.time.end.valueOf();
            }*/
            //查完成状态
            vm.page.condition.push({
                name: "dealFlag",
                value: 4
            });
          vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url,4);
    }
    
    //盘点启动确认
    vm.newProductList = [];
    vm.addCopyMaterial = function () 
    {
    	var param = angular.copy(vm.snapshotData);
    	param.no = "";
    	upida.post("stock/inventory/getNewProductList",param).then(function(data) {
    		vm.newProductList = data;
    		var obj = {name: '所有', recordId: ''};
    		vm.newProductList.splice(0, 0, obj);
    		$('#addMaterial').modal();
		});
    	
    	
    }
    
    vm.addMaterialItems = function(no)
    {
    	var param = angular.copy(vm.snapshotData);
    	param.no = no;
    	upida.post("stock/inventory/getNewProductList",param).then(function(data){
    		if(data && data.length > 0)
    		{
    			vm.newProductList = data;
        		var obj = {name: '所有', recordId: ''};
        		vm.newProductList.splice(0, 0, obj);
    		}
    	});
    }
    
    //盘点启动确认
    vm.check = function () 
    {
    	$('#copyMater').modal();
    	upida.post("stock/inventory/getSysTime").then(function(data) {
    		vm.snapshotDate = data;
		});
    }
    
    //盘点完成确认
    vm.checkFinsh = function ()
    {
    	$('#copyMaterFinsh').modal();
    }
    
    //启动盘点
    vm.copyMaterial = function () 
    {
    	if(vm.storeId==-1)
    	{
    		$('#showWipCopySet').modal();
    		return;
    	}
    	MainCtrl.blockUI({
        	animate: true
        });
    	var material = {};
    	material.storeId = vm.storeId;
    	material.snapshotDate = vm.snapshotDate;
    	upida.post("stock/inventory/copyMaterial",material).then(function (data) {
            // 初始化数据
            if(data){
            	// 提示信息
            	vm.message = data;
            	vm.initInventory();
            }else{
            	// 提示信息
            	vm.message = data;
            }
            
            $('#static').modal();
          //进度条关闭
			MainCtrl.unblockUI();
        });
    }; 
    
    //完成盘点
    vm.copyM = function (index) 
    {
    	MainCtrl.blockUI({
        	animate: true
        });
    	upida.post("stock/inventory/copyM",vm.page.data.list[index]).then(function (data) {
            // 初始化数据
            if(data){
            	// 提示信息
            	vm.message = data;
            	vm.initInventory();
            }else{
            	// 提示信息
            	vm.message = data;
            }
            
            $('#static').modal();
          //进度条关闭
			MainCtrl.unblockUI();
        });
    }
    
    vm.auditSnap = function(){
    	if(vm.snapshotData.dealFlag != 60002){
    		vm.message = "财务处理需要是审批通过状态！";
        	$('#static').modal();
        	return;
    	}
    	MainCtrl.blockUI({
        	animate: true
        });
    	upida.post("stock/inventory/auditSnap",vm.snapshotData).then(function (data) {
            // 初始化数据
            if(data&&data=='success'){
            	// 提示信息
            	vm.message = "处理完成";
            	vm.initInventory();
            	vm.snapshotData.dealFlag = 4;
            }else{
            	// 提示信息
            	vm.message = data;
            }
            
            $('#static').modal();
          //进度条关闭
			MainCtrl.unblockUI();
        });
    }
    
    vm.editMaterial = {};
    vm.changeStocks = function (index) 
    {
    	vm.editMaterial = {};
    	vm.editMaterial.recordId = vm.page.materData.list[index].materialId;
    	vm.editMaterial.stocks = vm.page.materData.list[index].snapStrocks;
    	 $('#changeStocks').modal();
    }
    
    vm.editWipStockStart = function(index)
    {
    	//完成状态或者审批通过 不能修改
    	if(vm.snapshotData.dealFlag == 4||vm.snapshotData.dealFlag== 60002)
    	{
    		return;
    	}
    	vm.editCopy = {};
    	vm.editCopy.recordId = vm.page.materData.list[index].recordId;
    	//vm.editMaterial.snapStrocks = vm.page.materData.list[index].snapStrocks;
    	vm.editCopy.qtyPnlAFirst = vm.page.materData.list[index].qtyPnlAFirst;
    	vm.editCopy.qtyPnlBFirst = vm.page.materData.list[index].qtyPnlBFirst;
    	vm.editCopy.qtyPnlASecond = vm.page.materData.list[index].qtyPnlASecond;
    	vm.editCopy.qtyPnlBSecond = vm.page.materData.list[index].qtyPnlBSecond;
    	
    	if(vm.editCopy.qtyPnlASecond!=null){
    		//没有初盘数，不让填
    		if(vm.editCopy.qtyPnlAFirst==null){
    			vm.page.materData.list[index].qtyPnlASecond = null;
    			return;
    		}
    		vm.editCopy.snapQtyPnlA = vm.editCopy.qtyPnlASecond;
    	}else if(vm.editCopy.qtyPnlAFirst!=null){
    		vm.editCopy.snapQtyPnlA = vm.editCopy.qtyPnlAFirst;
    	}else{
    		vm.editCopy.snapQtyPnlA = null;
    	}
    	vm.page.materData.list[index].snapQtyPnlA = vm.editCopy.snapQtyPnlA;
    	
    	if(vm.editCopy.qtyPnlBSecond!=null){
    		//没有初盘数，不让填
    		if(vm.editCopy.qtyPnlBFirst==null){
    			vm.page.materData.list[index].qtyPnlBSecond = null;
    			return;
    		}
    		vm.editCopy.snapQtyPnlB = vm.editCopy.qtyPnlBSecond;
    	}else if(vm.editCopy.qtyPnlBFirst!=null){
    		vm.editCopy.snapQtyPnlB = vm.editCopy.qtyPnlBFirst;
    	}else{
    		vm.editCopy.snapQtyPnlB = null;
    	}
    	vm.page.materData.list[index].snapQtyPnlB = vm.editCopy.snapQtyPnlB;
    	//vm.editStock();
    	vm.editWipCopyStock();
    }
    
    vm.editStockStart = function (index) 
    {
    	//完成状态或者审批通过 不能修改
    	if(vm.snapshotData.dealFlag == 4||vm.snapshotData.dealFlag== 60002){
    		return;
    	}
    	vm.editMaterial = {};
    	vm.editMaterial.recordId = vm.page.materData.list[index].recordId;
    	//vm.editMaterial.snapStrocks = vm.page.materData.list[index].snapStrocks;
    	vm.editMaterial.firstStrocks = vm.page.materData.list[index].firstStrocks;
    	vm.editMaterial.sencondStrocks = vm.page.materData.list[index].sencondStrocks;
    	if(vm.editMaterial.sencondStrocks!=null){
    		//没有初盘数，不让填
    		if(vm.editMaterial.firstStrocks==null){
    			vm.page.materData.list[index].sencondStrocks = null;
    			return;
    		}
    		vm.editMaterial.snapStrocks = vm.editMaterial.sencondStrocks;
    	}else if(vm.editMaterial.firstStrocks!=null){
    		vm.editMaterial.snapStrocks = vm.editMaterial.firstStrocks;
    	}else{
    		vm.editMaterial.snapStrocks = null;
    	}
    	vm.page.materData.list[index].snapStrocks = vm.editMaterial.snapStrocks;
    	//vm.editStock();
    	vm.editCopyStock();
    }
    
    vm.editWipCopyStock = function()
    {
    	MainCtrl.blockUI({
        	animate: true
        });
    	upida.post("stock/inventory/editWipCopyStock",vm.editCopy).then(function (data) {
            // 初始化数据
            if(data && data=="success"){
            	// 提示信息
//            	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materCondition, vm.page.materUrl,2);
            }else{
            	vm.message = "修改库存失败";
            	$('#static').modal();
            }
          //进度条关闭
			MainCtrl.unblockUI();
        });
    }
    
    vm.editCopyStock = function(){
    	MainCtrl.blockUI({
        	animate: true
        });
    	upida.post("stock/inventory/editCopyStock",vm.editMaterial).then(function (data) {
            // 初始化数据
            if(data && data=="success"){
            	// 提示信息
//            	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materCondition, vm.page.materUrl,2);
            }else{
            	vm.message = "修改库存失败";
            	$('#static').modal();
            }
          //进度条关闭
			MainCtrl.unblockUI();
        });
    }
    
    vm.editStock = function () 
    {
    	MainCtrl.blockUI({
        	animate: true
        });
    	upida.post("stock/inventory/editStock",vm.editMaterial).then(function (data) {
            // 初始化数据
            if(data && data=="success"){
            	// 提示信息
//            	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materCondition, vm.page.materUrl,2);
            }else{
            	vm.message = "修改库存失败";
            	$('#static').modal();
            }
          //进度条关闭
			MainCtrl.unblockUI();
        });
    }
    
    vm.changeInvenStatus = function (index) 
    {
    	MainCtrl.blockUI({
        	animate: true
        });
    	upida.post("stock/inventory/chageMaterialStatus",vm.page.materData.list[index]).then(function (data) {
            // 初始化数据
            if(data && data=="success"){
            	// 提示信息
            	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materCondition, vm.page.materUrl,2);
            }else{
            	vm.message = "修改库存失败";
            	$('#static').modal();
            }
          //进度条关闭
			MainCtrl.unblockUI();
        });
    }
    
    //查看当前盘点的物料列表
    vm.snFlag = true;
    vm.query.recRecordId = "";
    vm.materList = function ()
    {
    	vm.snFlag = true;
    	vm.query.snapshotDate.value = angular.copy(vm.snapshotData);
    	vm.selectedCopyRec.snapshotDate = vm.snapshotData.snapshotDate;
    	vm.selectedCopyRec.storeId = vm.snapshotData.storeId;
    	vm.query.recRecordId = vm.snapshotData.recordId;
    	if(vm.snapshotData && vm.snapshotData.inventoryFlag && vm.snapshotData.inventoryFlag == '0'){
    		vm.snFlag = false;
    	}
    	// 设置查询条件
        var condition = [];
        
        MainCtrl.blockUI({
        	animate: true
        });
        
        //库存盘点的时间
        if (vm.query.snapshotDate.value != null && vm.query.snapshotDate.value != "") {
            condition.push({
                name: vm.query.snapshotDate.name,
                value: vm.query.snapshotDate.value.snapshotDate
            });
        }
        //盘点仓库
        if (vm.snapshotData.storeId != null && vm.snapshotData.storeId != "") {
            condition.push({
                name: "storeHouseId",
                value: vm.snapshotData.storeId
            });
        }
        vm.query.snapMaterFlag.value = "201602";
        condition.push({
            name: vm.query.snapMaterFlag.name,
            value: vm.query.snapMaterFlag.value
        });
        condition.push({
            name: "recordId",
            value: vm.snapshotData.recordId
        });
        
        vm.page.pageNo = 1;
        vm.page.materCondition = condition;
        // 查询数据
        if(-1==vm.snapshotData.storeId)
        {
        	condition.push({
                name: "copyRecId",
                value: vm.snapshotData.recordId
            });
        	vm.loadCateNameList();
        	 vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materCondition, vm.page.wipCopyUrl,2);
        }
        else
        {
        	 vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materCondition, vm.page.materUrl,2);
        }
       
        //loadItemsData();
        vm.showMaterList();
        MainCtrl.unblockUI();
    };
    vm.cateNameSortList = ["开料","烤板","钻孔","图形转移","光板测试","阻焊","文字","印灰油","喷锡","沉镍金","电脑V-cut","手V","切边加电脑V-CUT","激光锣板","锣","啤","成品测试","抗氧化","FQC","终检入库"];
    vm.loadCateNameList = function()
    {
    	let qp = null;
    	if(vm.snapshotData.recordId)
    	{
    		qp = vm.snapshotData.recordId;
    	}
    	else
    	{
    		qp = vm.historyData.recordId;
    	}
    	upida.post("stock/inventory/getCateNameList",{"recordId":qp}).then(function (data) {
            // 初始化数据
    		 vm.cateNameList = [];
    		 
    		if(data&&data.length>0 ){
            	// 提示信息
    			for(var i=0;i<vm.cateNameSortList.length;i++){
    				if(data.indexOf(vm.cateNameSortList[i])!=-1){
            			vm.cateNameList.push(vm.cateNameSortList[i]);
            		 }
            	}
    			
    			for(var i=0;i<data.length;i++){
    				if(vm.cateNameSortList.indexOf(data[i])==-1){
            			vm.cateNameList.push(data[i]);
            		 }
            	}
            }
    		
        });
    }
    
    //查看盘盈亏明细
    vm.snapDifferenceDeail = function (index)
    {
    	vm.historyData = {};
    	// 设置查询条件
        var condition = [];
        
        MainCtrl.blockUI({
        	animate: true
        });
        if(typeof(index) == "number"){
        	vm.snapshotData = angular.copy(vm.page.data.list[index]);
        }
    	//库存盘点的时间
        if (vm.snapshotData!= null) {
            condition.push({
                name: vm.query.snapshotDate.name,
                value: vm.snapshotData.snapshotDate
            });
            
            condition.push({
                name: "storeHouseId",
                value: vm.snapshotData.storeId
            });
            condition.push({
                name: "recordId",
                value: vm.snapshotData.recordId
            });
        }
        
        
        //condition.recordId = vm.page.materData.list[0].recordId;
        vm.page.pageNo = 1;
        vm.page.materConditionDeail = condition;
        // 查询数据
        if(-1==vm.snapshotData.storeId)
        {
        	condition.push({
                name: "copyRecId",
                value: vm.snapshotData.recordId
            });
        	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materConditionDeail, vm.page.diffWipCopyUrl,3);
        }
        else
        {
        	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materConditionDeail, vm.page.materUrlDeail,3);
        }
        
        vm.showSnapDeail();
        MainCtrl.unblockUI();
    	
    }
    
  //查看盘盈亏明细
    vm.historyData = {};
    vm.snapDifferenceDeailHistory = function (index)
    {
    	// 设置查询条件
        var condition = [];
        
        MainCtrl.blockUI({
        	animate: true
        });
        vm.historyData =  angular.copy(vm.page.historyData.list[index]);
    	//库存盘点的时间
        if (vm.snapshotData!= null) {
            condition.push({
                name: vm.query.snapshotDate.name,
                value: vm.historyData.snapshotDate
            });
            
            condition.push({
                name: "storeHouseId",
                value: vm.historyData.storeId
            });
            condition.push({
                name: "recordId",
                value: vm.historyData.recordId
            });
            condition.push({
                name: "invenFlag",//dealFlag
                value: vm.historyData.dealFlag
            });
        }
        
        
        //condition.recordId = vm.page.materData.list[0].recordId;
        vm.page.pageNo = 1;
        vm.page.materConditionDeail = condition;
        // 查询数据
        if(-1==vm.historyData.storeId)
        {
        	condition.push({
                name: "copyRecId",
                value: vm.historyData.recordId
            });
        	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materConditionDeail, vm.page.diffWipCopyUrl,3);
        }
        else
        {
        	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materConditionDeail, vm.page.materUrlDeail,3);
        }
        vm.showSnapDeail();
        MainCtrl.unblockUI();
    	
    }
    
    //查看盘点明细
    vm.snapDeail = function ()
    {
    	// 设置查询条件
        var condition = [];
        
        MainCtrl.blockUI({
        	animate: true
        });
        
        //库存盘点的时间
        if (vm.query.snapshotDate.value != null && vm.query.snapshotDate.value != "") {
            condition.push({
                name: vm.query.snapshotDate.name,
                value: vm.query.snapshotDate.value.snapshotDate
            });
        }
        
        vm.query.snapMaterFlag.value = "201611";
        
        condition.push({
            name: vm.query.snapMaterFlag.name,
            value: vm.query.snapMaterFlag.value
        });
        
        vm.page.pageNo = 1;
        vm.page.materConditionDeail = condition;
        // 查询数据
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materConditionDeail, vm.page.materUrl,3);
        vm.showSnapDeail();
        MainCtrl.unblockUI();
    	
    }
    
    //加载下拉框数据
    function loadItemsData() {
    	MainCtrl.blockUI({
        	animate: true
        });
        // 加载数据
        upida.get("stock/material/load/data").then(function(data) {
            //物料类型
            vm.materialTypes = data.materialTypes;
            vm.switchValue = data.switchValue;
            //进度条关闭
			MainCtrl.unblockUI();
        });
    }
    vm.selectedCopyRec = {};
    //根据条件查询物料列表的数据
    vm.doQueryMaster = function ()
    {
    	 // 设置查询条件
        var condition = [];


        if (vm.query.snapNo.value !== "")
        {
            condition.push({
                name: vm.query.snapNo.name,
                value: vm.query.snapNo.value
            });
        }

        if (vm.query.snapName.value !== "")
        {
            condition.push({
                name: vm.query.snapName.name,
                value: vm.query.snapName.value
            });
        }
        if (vm.query.snapMaterialKind.value !== "")
        {
            condition.push({
                name: vm.query.snapMaterialKind.name,
                value: vm.query.snapMaterialKind.value
            });
        }
        if (vm.query.snapMaterialType.value !== "")
        {
            condition.push({
                name: vm.query.snapMaterialType.name,
                value: vm.query.snapMaterialType.value
            });
        }
        
        //库存盘点的时间
        if (vm.query.snapshotDate.value != null && vm.query.snapshotDate.value != "") {
            condition.push({
                name: vm.query.snapshotDate.name,
                value: vm.query.snapshotDate.value.snapshotDate
            });
        }
        
	    condition.push({
    		name: vm.query.snapIsIncludeStock.name,
    		value: vm.query.snapIsIncludeStock.value
    	});
	    
	    
	    //仓库id
	    condition.push({
    		name: "storeHouseId",
    		value: vm.snapshotData.storeId
    	});
	   
	    condition.push({
            name: "recordId",
            value: vm.snapshotData.recordId
        });
        
        vm.page.pageNo = 1;
        vm.page.materCondition = condition;
        
        // 查询数据
        if(-1==vm.snapshotData.storeId)
        {
        	if(vm.query.cateName)
            {
            	condition.push({
                    name: "cateName",
                    value: vm.query.cateName
                });
            }
            
            if(vm.query.craftNo)
            {
            	condition.push({
                    name: "craftNo",
                    value: vm.query.craftNo
                });
            }
            
            condition.push({
                name: "copyRecId",
                value: vm.snapshotData.recordId
            });
        	 vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materCondition, vm.page.wipCopyUrl,2);
        }
        else
        {
        	 vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materCondition, vm.page.materUrl,2);
        }
    }
    
    // 查看物料详情
    vm.material = {};
    vm.getMaterialDeail = function(index){
    	//获取当前的物料详情
        vm.material = angular.copy(vm.page.materData.list[index]);
        //显示物料详情页面
        vm.showMaterialDeail();
    };
    
    vm.snapshotData = {};
    vm.snapshotSchude = function (index)
    {
    	vm.snapshotData = angular.copy(vm.page.data.list[index]);
    	if(vm.snapshotData&&!vm.snapshotData.dealFlag){
    		vm.snapshotData.dealFlag = 0;
    	}
    	vm.showMaterSchude();
    };
    
    vm.updateDealFlag = function(){
    	 // 请求分页数据
    	/*var reqData = {};
    	reqData.storeHouseId = vm.snapshotData.storeId;
    	reqData.snapshotDate = vm.snapshotData.snapshotDate;
    	reqData.downInfoStatus = 1;
    	reqData.recDealFlag = vm.snapshotData.dealFlag;
    	reqData.recRecordId = vm.snapshotData.recordId;
        upida.post("stock/inventory/exportCopyDetail", reqData).then(function (result) {
        	
        });*/
    	if(!vm.snapshotData.dealFlag||vm.snapshotData.dealFlag==0){
    		vm.snapshotData.dealFlag = 1;
    	}
    };
    vm.updateRecDealFlag = function(flag){
    	var reqData = {};
    	reqData.dealFlag = flag;
    	reqData.recordId = vm.snapshotData.recordId;
    	if(vm.snapshotData.dealFlag&&(vm.snapshotData.dealFlag ==60001||vm.snapshotData.dealFlag==4 )){
    		return;
    	}
        upida.post("stock/inventory/copyM", reqData).then(function (result) {
        	vm.doQuery();
        	vm.snapshotData.dealFlag = flag;
        	$timeout(function(){
        		vm.tabs.snapSchde.active = true;
            });
        });
    }
    
    
    
    // 显示盘点进度
    vm.showMaterSchude = function () 
    {
    	vm.tabs.snapDeail.show = false;
    	vm.tabs.snapDeail.active = false;
    	vm.tabs.material.show = false;
        vm.tabs.material.active = false;
    	vm.tabs.snapSchde.show = true;
        vm.tabs.snapSchde.active = true;
    }
    
    vm.hiddenSchde = function() {
    	vm.tabs.snapDeail.show = false;
    	vm.tabs.snapDeail.active = false;
    	vm.tabs.materialDeail.show = false;
    	vm.tabs.materialDeail.active = false;
    	vm.tabs.material.show = false;
    	vm.tabs.material.active = false;
    	vm.tabs.snapSchde.show = false;
        vm.tabs.snapSchde.active = false;
    	 $timeout(function(){
             vm.tabs.snapshotForm.active = true;
         });
	}
    
    //显示当前盘点的物料列表
    vm.showMaterList = function () 
    {
    	vm.tabs.materialDeail.show = false;
    	vm.tabs.materialDeail.active = false;
    	vm.tabs.material.show = true;
        vm.tabs.material.active = true;
    }
    
    //隐藏物料列表
    vm.hiddenMaster = function ()
    {
    	vm.tabs.materialDeail.show = false;
    	vm.tabs.materialDeail.active = false;
    	vm.tabs.material.show = false;
    	vm.tabs.material.active = false;
    	 $timeout(function(){
             vm.tabs.snapSchde.active = true;
         });
    }
    
    //显示当前盘点的明细
    vm.showSnapDeail = function ()
    {
    	vm.tabs.snapDeail.show = true;
        vm.tabs.snapDeail.active = true;
    }
    
    vm.showWipArea = function ()
    {
    	vm.tabs.snapWipArea.show = true;
        vm.tabs.snapWipArea.active = true;
    }
    
    vm.hiddenSnapWipArea = function ()
    {
    	vm.tabs.snapWipArea.show = false;
        vm.tabs.snapWipArea.active = false;
        $timeout(function(){
   		 if(vm.tabs.snapSchde.show){
   			 vm.tabs.snapSchde.active = true;
   		 }else{
   			 vm.tabs.snapshotForm.active = true;
   		 }
   		 
        });
        
    }
    
    //显示当前盘点的明细
    vm.showSnapDiffHandle = function ()
    {
    	vm.tabs.snapDiffHandle.show = true;
        vm.tabs.snapDiffHandle.active = true;
    }
    
  //显示当前盘点的明细
    vm.showDiffInoutDeail = function ()
    {
    	vm.tabs.inOutDeail.show = true;
        vm.tabs.inOutDeail.active = true;
    }
    
    //隐藏当前盘点的明细
    vm.hiddenSnapDeail = function ()
    {
    	vm.tabs.snapDeail.show = false;
    	vm.tabs.snapDeail.active = false;
    	 $timeout(function(){
    		 if(vm.tabs.snapSchde.show){
    			 vm.tabs.snapSchde.active = true;
    		 }else{
    			 vm.tabs.snapshotForm.active = true;
    		 }
    		 
         });
    }
    
    vm.hiddenSnapDiffHandle = function ()
    {
    	vm.tabs.snapDiffHandle.show = false;
    	vm.tabs.snapDiffHandle.active = false;
    	 $timeout(function(){
    		 if(vm.tabs.snapSchde.show){
    			 vm.tabs.snapSchde.active = true;
    		 }else{
    			 vm.tabs.snapshotForm.active = true;
    		 }
    		 
         });
    }
    //隐藏出入库明细
    vm.hiddenInOutDeail = function ()
    {
    	vm.tabs.inOutDeail.show = false;
    	vm.tabs.inOutDeail.active = false;
    	 $timeout(function(){
    		 if(vm.tabs.snapDeail.show){
    			 vm.tabs.snapDeail.active = true;
    		 }else{
    			 vm.tabs.snapshotForm.active = true;
    		 }
    		 
         });
    }
    
    //隐藏出入库明细
    vm.hiddenPlanceInfo = function ()
    {
    	vm.tabs.planceInfo.show = false;
    	vm.tabs.planceInfo.active = false;
    	 $timeout(function(){
    		 if(vm.tabs.snapDeail.show){
    			 vm.tabs.snapDeail.active = true;
    		 }else{
    			 vm.tabs.snapshotForm.active = true;
    		 }
    		 
         });
    }
    
    //隐藏出入库明细
    vm.hiddenbatchInfo = function ()
    {
    	vm.tabs.batchInfo.show = false;
    	vm.tabs.batchInfo.active = false;
    	 $timeout(function(){
    		 if(vm.tabs.snapDeail.show){
    			 vm.tabs.snapDeail.active = true;
    		 }else{
    			 vm.tabs.snapshotForm.active = true;
    		 }
    		 
         });
    }
    
    
    
    //显示当前物料详情
    vm.showMaterialDeail = function ()
    {
    	vm.tabs.materialDeail.show = true;
    	vm.tabs.materialDeail.active = true;
    }
    //隐藏当前物料详情
    vm.hideMaterialDeail = function ()
    {
    	vm.tabs.materialDeail.show = false;
    	vm.tabs.materialDeail.active = false;
    	$timeout(function(){
            vm.tabs.material.active = true;
        });
    }
    
    // 库存盘点分页按钮单击处理
    vm.doPage = function(page, pageSize, total){
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        vm.init(page, pageSize, vm.page.condition, vm.page.url,1);
    };
    
    // 库存盘点物料列表分页按钮单击处理
    vm.doMaterPage = function(page, pageSize, total){
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        if(-1==vm.snapshotData.storeId)
        {
        	 vm.init(page, pageSize, vm.page.materCondition, vm.page.wipCopyUrl,2);
        }
        else
        {
        	 vm.init(page, pageSize, vm.page.materCondition, vm.page.materUrl,2);
        }
    };
    
    // 盘点明细分页按钮单击处理
    vm.doMaterPageDeail = function(page, pageSize, total){
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        if(-1==vm.snapshotData.storeId)
        {
        	vm.init(page, pageSize, vm.page.materConditionDeail, vm.page.diffWipCopyUrl,3);
        }
        else
        {
        	vm.init(page, pageSize, vm.page.materConditionDeail, vm.page.materUrlDeail,3);
        }
    };
    
    // 库存盘点页面显示数量改变
    vm.pageSizeChange = function(){
        vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url,1);
    };
    
    // 库存盘点物料列表页面显示数量改变
    vm.pageMasterSizeChange = function(){
    	if(-1==vm.snapshotData.storeId)
        {
        	 vm.init(1, vm.page.pageSize, vm.page.materCondition, vm.page.wipCopyUrl,2);
        }
        else
        {
        	 vm.init(1, vm.page.pageSize, vm.page.materCondition, vm.page.materUrl,2);
        }
    };
    
    // 盘点明细页面显示数量改变
    vm.pageMasterSizeChangeDeail = function(){
    	 if(-1==vm.snapshotData.storeId)
         {
    		 vm.init(1, vm.page.pageSize, vm.page.materConditionDeail, vm.page.diffWipCopyUrl,3);
         }
         else
         {
        	 vm.init(1, vm.page.pageSize, vm.page.materConditionDeail, vm.page.materUrlDeail,3);
         }
        
    };
    
    // 时间范围的Model
    vm.time = {	
        start: {},
        end: {}
    };
    
    // 时间范围的选项
    vm.rangeOptions = {
        format: "YYYY-MM-DD",
        startDate: new Date((new Date).setMonth(((new Date).getMonth()-1))),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
    };
    
    vm.initDate=function(date)
	{
		 if(date==""){
			 vm.rangeOptions = {
		        //format: "YYYY-MM-DD",
		    	startDate: new Date(vm.query.sentTimeStartQr.value),
		    	minDate:new Date(new Date(vm.query.sentTimeEndQr.value).setFullYear(new Date(vm.query.sentTimeEndQr.value).getFullYear()-5))
			 };
			 vm.time= {
		       start: vm.rangeOptions.startDate,
				 end: vm.rangeOptions.minDate
			 } 
		 }
	 }
    
    // 加载权限
    function loadRight() {
    	MainCtrl.blockUI({
    	    //进度条展示
    	    animate: true
    	});
    	loadItemsData();
    	vm.queryAll = CommonUtil.dataRangeIsAll("10610", BaseUtil.getMenuList());
        upida.get("common/rightall?prefix=stock.inventory").then(function (data) {
        	MainCtrl.unblockUI();
        	 vm.right.view = data.view;
             vm.right.edit = data.edit;
             vm.right.manage = data.manage;
            // 初始化第一页，条件为空
             vm.initInventory();
        });
    }
    
    vm.storeId = "";
    function loadItemsData() {
    	MainCtrl.blockUI({
        	animate: true
        });
        // 加载数据
    	vm.getMouthQueryList();
    	//设置开始月份、结束月份默认值
    	vm.setMonthDefault();
        upida.get("stock/material/load/data").then(function(data) {
            //物料类型
            vm.materialTypes = data.materialTypes;
            //vm.materialTypes.splice(0,0,{value:'请选择...',recordId:''});
            //加载计量单位
            vm.units = data.units;
            //加载默认仓库
            vm.storehouse = data.storeHouseList;
            if(vm.storehouse&&vm.storehouse.length>0)
            {
            	angular.forEach(vm.storehouse,function(p){
            		if(p.name == "成品仓")
            		{
            			p.name= "成品-临时仓";
            		}
            		
            	});
            }
            
            vm.storehouse.push({"name":"WIP","recordId":-1,"type":-1});
            vm.storehouse.push({"name":"成品-尾数仓","recordId":-44,"type":-44});
            /*for(var i =0;i<vm.storehouse.length;i++){
            	if(vm.storehouse[i].name == '成品仓'){
            		vm.storehouse.splice(i,1)
            	}
            }*/
            vm.materialKinds.push({"value":100701,"name":"原料"});
            vm.materialKinds.push({"value":100702,"name":"成品"});
            vm.materialSpecList = data.materialSpecList;

            vm.switchValue = data.switchValue;
           
            //进度条关闭
			MainCtrl.unblockUI();
        });
        upida.get("stock/inventory/getWipSet").then(function(data) {
            //物料类型
            vm.cateSetList = data;
            for(var i = 0;i<data.length;i++)
            {
            	if(data[i].cateName=="showFlag")
            	{
            		vm.showFlag = data[i].unitType;
            		break;
            	}
            }
        });
        
    }
    vm.showFlag = 2;
    vm.uploadCopyDetailExcel = function(){
    	//完成状态或者审批通过 不能修改
    	if(vm.snapshotData.dealFlag == 4||vm.snapshotData.dealFlag== 60002){
    		return;
    	}
    	var file = $("#upfile")[0].files[0];
        if(!file || !file.type || !(file.type == "application/vnd.ms-excel" || file.type == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")){
            vm.message = "上传的文件必须为xls或者xlsx的excel格式";
            $('#static').modal();
            return;
        }
    	var form = new FormData();
    	form.append("f",file);
    	form.append("recRecordId",vm.query.recRecordId);
    	form.append("storeId",vm.snapshotData.storeId);
    	MainCtrl.blockUI({
    		animate: true,
    	});
    	$.ajax({
     		type:"post",
     		//url: 'a/stock/material/uploadExcel',
     		url: 'a/stock/inventory/uploadCopyDetailExcel',
     		data: form,
     		contentType:"application/json",
            processData: false,  //这些不能忽略
            contentType: false,  
     		success:function(data)
     		{
     			if(data.status == 1)
     			{
     				vm.message = "导入成功！";
     				if(data.insertNum)
     				{
     					vm.message=vm.message+"引入"+data.insertNum+"条有效数据。"
     				}
     				else
     				{
     					vm.message=vm.message+"引入0条有效数据。"
     				}
     				if(data.updateNum)
     				{
     					vm.message=vm.message+"修改"+data.updateNum+"条数据。"
     				}
     				//追加显示到页面
     				if(data.dataList&&data.dataList.length>0){
     				    vm.addRedDataFlag = true;
     				    vm.addRedData = data.dataList;
     				}
     				vm.doQueryMaster();
			        $('#static').modal();
			        $("#upfile").val("");
			      
			        
     			}else{
     				vm.message = "导入失败！"+data.msg;
			        $('#static').modal();
			        $("#upfile").val("");
     			}
     			MainCtrl.unblockUI();
     		}
     	})
    };
    vm.addRedDataFlag = false;
    vm.addRedData = [];
    vm.isRed = function(row){
    	if(row.storehouse.name){
    		return false;
    	}else{
    		return true;
    	}
    }
    
    vm.doQueryDetailByCon = function(){
    	// 设置查询条件
        var condition = [];
        
        MainCtrl.blockUI({
        	animate: true
        });
        //库存盘点的时间
        if (vm.query.snapshotDate.value != null && vm.query.snapshotDate.value != "") {
            condition.push({
                name: vm.query.snapshotDate.name,
                value: vm.query.snapshotDate.value.snapshotDate
            });
            
            condition.push({
                name: "storeHouseId",
                value: vm.query.snapshotDate.value.storeId
            });
            
            condition.push({
                name: "invenFlag",//dealFlag
                value: vm.query.snapshotDate.value.dealFlag
            });
        }
        
        condition.push({
            name: "no",
            value: vm.copyDiff.query.No
        });
        
        
        //condition.recordId = vm.page.materData.list[0].recordId;
        vm.page.pageNo = 1;
        vm.page.materConditionDeail = condition;
        // 查询数据
        if(-1==vm.snapshotData.storeId)
        {
        	condition.push({
                name: "copyRecId",
                value: vm.query.snapshotDate.value.recordId
            });
        	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materConditionDeail, vm.page.diffWipCopyUrl,3);
        }
        else
        {
        	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materConditionDeail, vm.page.materUrlDeail,3);
        }
        vm.showSnapDeail();
        MainCtrl.unblockUI();
    };
    
    vm.editdiffRetail = function(index){
    	//完成状态或者审批通过 不能修改
    	if(vm.snapshotData.dealFlag == 4||vm.snapshotData.dealFlag== 60002){
    		return;
    	}
    	vm.editMaterial = {};
    	vm.editMaterial.recordId = vm.page.materDataDeail.list[index].recordId;
    	vm.editMaterial.diffExplain = vm.page.materDataDeail.list[index].diffExplain;
    	vm.editMaterial.storeHouseId = vm.snapshotData.storeId;
    	MainCtrl.blockUI({
        	animate: true
        });
    	upida.post("stock/inventory/editCopyRemark",vm.editMaterial).then(function (data) {
            // 初始化数据
            if(data && data=="success"){
            	// 提示信息
//            	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materCondition, vm.page.materUrl,2);
            }else{
            	vm.message = "修改差异说明失败";
            	$('#static').modal();
            }
          //进度条关闭
			MainCtrl.unblockUI();
        });
    };
    
    vm.showApproveDialog = function(){
    	$('#approveDialog').modal();
    };
    
    vm.approvalCopyFlag = true;
    vm.approvalCopy = function(){
    	if(!vm.snapshotData.recordId){
    		vm.message = "提交审批需要先选择盘点记录！";
        	$('#static').modal();
        	return;
    	}
    	if(!vm.snapshotData.dealFlag||!(vm.snapshotData.dealFlag == 3 || vm.snapshotData.dealFlag == 60003)){
    		if(vm.snapshotData.dealFlag == 60001){
    			vm.message = "审批已经提交，不用重复提交！";
    		}else if(vm.snapshotData.dealFlag == 60002){
    			vm.message = "审批已经通过，不用重复提交！";
    		}else{
    			vm.message = "提交审批需要是盈亏确认或者审批驳回状态！";
    		}
        	$('#static').modal();
        	return;
    	}
    	if(!vm.approvalCopyFlag)
    	{
    		return;
    	}
    	vm.approvalCopyFlag = false;
    	MainCtrl.blockUI({
        	animate: true
        });
    	upida.post("approval/appmodify/materialCopyApproval",vm.snapshotData).then(function (data) {
            // 初始化数据
            if(data && data.result=="success"){
            	// 提示信息
//            	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materCondition, vm.page.materUrl,2);
            //	vm.doQueryMaster();
            	vm.snapshotData.dealFlag = 60001;
            	vm.message = data.message;
            	$('#static').modal();
            	upida.post("stock/inventory/getAuditRole",vm.snapshotData).then(function (data) {
                    // 初始化数据
                    vm.snapshotData.auditRole = data;
                    MainCtrl.unblockUI();
            	});
            	vm.approvalCopyFlag = true;
            }else{
            	vm.message = data.message;
            	$('#static').modal();
            	//进度条关闭
    			MainCtrl.unblockUI();
    			vm.approvalCopyFlag = true;
            }
          
        });
    };
    
    vm.printCopyFlag = true;
    vm.printCopyBill = function(printForm){
    	//printForm.commit();
    	vm.printCopyFlag = false;
    	//{'recordId': vm.snapshotData.recordId,"storeId": vm.snapshotData.storeId,"downInfoStatus":15}
    	$http.post('a/stock/inventory/printCopyPDF',{'recordId': vm.snapshotData.recordId,"storeId": vm.snapshotData.storeId},{responseType: 'arraybuffer'})
        .success(function (response, status, headers, config) {
            vm.printCopyFlag = true;
            var a = document.createElement('a');
            var file = new Blob([response], {type: 'application/pdf'});
            var fileURL = (window.URL || window.webkitURL).createObjectURL(file);
            //window.open(fileURL);
            a.href = fileURL;
            a.download = headers('X-File-Name');
            document.body.appendChild(a);
            a.click();
            $timeout(function () {
                document.body.removeChild(a);
                URL.revokeObjectURL(fileURL);
            }, 100);
            vm.updateDealFlag();
        }).error(function (data) {
        vm.printCopyFlag = true;
        vm.message = "Sorry，打印失败！";
        $('#static').modal();
    });
    }
    
    //查看出入库明细
    vm.diffInOutStoreList = [];
    vm.showInOutStore = function (index)
    {
    	// 设置查询条件
    	
        MainCtrl.blockUI({
        	animate: true
        });
        if(typeof(index) == "number"&&vm.page.materDataDeail.list[index].stockList){
        	vm.diffInOutStoreList = angular.copy(vm.page.materDataDeail.list[index].stockList);
        }else{
        	vm.diffInOutStoreList = [];
        }
        vm.showDiffInoutDeail();
        MainCtrl.unblockUI();
    	
    }
    
    //查看出入库明细
    vm.setPlanceDetail = {};
    vm.showPlanceInfo = function (index)
    {
    	// 设置查询条件  库位大于2
    	vm.setPlanceDetail = vm.page.materDataDeail.list[index];
        MainCtrl.blockUI({
        	animate: true
        });
        if(-1==vm.historyData.storeId||-1==vm.snapshotData.storeId)
        {
        	vm.tabs.batchInfo.show = true;
            vm.tabs.batchInfo.active = true;
        }
        else
        {
        	vm.tabs.planceInfo.show = true;
            vm.tabs.planceInfo.active = true;	
        }
        MainCtrl.unblockUI();
    	
    }
    vm.savePlanceFlag = true;
    vm.savePlanceInfo = function(){
    	//验证数量是否都大于等于0;相加等于实盘数
    	var snapStrocks = vm.setPlanceDetail.snapStrocks;
    	var countStocks = 0;
    	var flag = true;
    	angular.forEach(vm.setPlanceDetail.materialPlaceComList,function(p){
    		if(!p.changeStocks)
    		{
    			p.changeStocks= 0;
    		}
    		var changeStocks = Number(p.changeStocks);
    		if(changeStocks<0)
    		{
    			vm.message = "盘点数不能小余0";
   			 	$('#static').modal();
   			 	flag = false;
   			 	return;
    		}
    		countStocks = countStocks+changeStocks;
    	});
    	if(countStocks!=snapStrocks){
    		vm.message = "物料实盘库存要等于库位盘点数之和";
			 	$('#static').modal();
			 	return;
    	}
    	if(!flag){
    		return;
    	}
    	if(!vm.savePlanceFlag){
    		return;
    	}
    	 vm.savePlanceFlag = false; 
    	upida.post("stock/inventory/savePlanceInfo", vm.setPlanceDetail).then(function (data) {
    		 if(data) 
	 			{
	 				if(data.result == "fail")
	 				{
	 					vm.message = data.message;
	 					$('#static').modal();
	 				}
	 				else
	 				{
	 					vm.message = "保存成功";
	 					$('#static').modal();
	 				}
	 			}
	 			vm.savePlanceFlag = true;
	 			MainCtrl.unblockUI();
         	
         });
    };
    vm.saveBatchFlag = true;
    vm.saveBatchInfo = function()
    {
    	//验证数量是否都大于等于0;相加等于实盘数
    	var snapQtyPnlA = vm.setPlanceDetail.snapQtyPnlA;
    	var snapQtyPnlB = vm.setPlanceDetail.snapQtyPnlB;
    	if(!snapQtyPnlA)
    	{
    		snapQtyPnlA = 0;
    	}
    	if(!snapQtyPnlB)
    	{
    		snapQtyPnlB = 0;
    	}
    	var countPnlA = 0;
    	var countPnlB = 0;
    	var flag = true;
    	angular.forEach(vm.setPlanceDetail.copyInfoList,function(p){
    		if(!p.snapQtyPnlA)
    		{
    			p.snapQtyPnlA= 0;
    		}
    		if(!p.snapQtyPnlB)
    		{
    			p.snapQtyPnlB= 0;
    		}
    		var infoSnapQtyPnlA = Number(p.snapQtyPnlA);
    		var infoSnapQtyPnlB = Number(p.snapQtyPnlB);
    		if(infoSnapQtyPnlA<0||infoSnapQtyPnlB<0)
    		{
    			vm.message = "盘点数不能小余0";
   			 	$('#static').modal();
   			 	flag = false;
   			 	return;
    		}
    		countPnlA = countPnlA+infoSnapQtyPnlA;
    		countPnlB = countPnlB+infoSnapQtyPnlB;
    	});
    	if(snapQtyPnlA!=countPnlA||snapQtyPnlB!=countPnlB){
    		vm.message = "实盘库存要等于批次明细盘点数之和";
			 	$('#static').modal();
			 	return;
    	}
    	if(!flag){
    		return;
    	}
    	if(!vm.saveBatchFlag){
    		return;
    	}
    	 vm.savePlanceFlag = false; 
    	upida.post("stock/inventory/saveBatchInfo", vm.setPlanceDetail).then(function (data) {
    		 if(data) 
	 			{
	 				if(data.result == "fail")
	 				{
	 					vm.message = data.message;
	 					$('#static').modal();
	 				}
	 				else
	 				{
	 					vm.message = "保存成功";
	 					$('#static').modal();
	 					vm.hiddenbatchInfo()
	 				}
	 			}
	 			vm.saveBatchFlag = true;
	 			MainCtrl.unblockUI();
         	
         });
    }
    
    vm.mouthQueryList = [];
    vm.getMouthQueryList = function(){
    	var date = new Date();
    	var year = date.getFullYear(); //获取完整的年份(4位)
    	var month = date.getMonth()+1; //获取当前月份
    	//往前推1年 12个月
    	vm.mouthQueryList.push("");
    	for(var i = 0;i<12;i++){
    		if(month>=1&&month<10){
    			vm.mouthQueryList.push(year+"年-0"+month+"月");
    		}else if(month>=10){
    			vm.mouthQueryList.push(year+"年-"+month+"月");
    		}else{
    			year=year-1;
    			month = 12
    			vm.mouthQueryList.push(year+"年-"+month+"月");
    		}
    		month=month-1;
    	}
    };
    
    vm.setMonthDefault = function(){
    	if(vm.mouthQueryList.length>1){
    		vm.startMonth = vm.mouthQueryList[vm.mouthQueryList.length-1];
    		vm.endMonth = vm.mouthQueryList[1];
    	}
    }
    
    vm.changeStartMonth = function(){
    	/*if(!vm.endMonth){
    		vm.endMonth = vm.startMonth;
    	}*/
    	if(vm.startMonth!=""&&vm.getMonthIndex(vm.startMonth)<vm.getMonthIndex(vm.endMonth)){
    		vm.endMonth = vm.startMonth;
    	}
    }
    
    vm.changeEndMonth = function(){
    	/*if(!vm.startMonth){
    		vm.startMonth = vm.endMonth;
    	}*/
    	if(vm.endMonth!=""&&vm.getMonthIndex(vm.startMonth)<vm.getMonthIndex(vm.endMonth)){
    		vm.startMonth = vm.endMonth;
    	}
    }
    
    vm.getMonthIndex = function(month){
    	for(var i = 0;i<vm.mouthQueryList.length;i++){
    		if(vm.mouthQueryList[i]==month){
    			return i;
    		}
    	}
    }
    
    //num 1:盘点单;2:录入数据;3:盈亏确认;4:提交审批;5:财务处理;
    //f  buttton:1
    vm.checkDealFlag = function(num, f){
    	if(num == 4)
    	{//提交审批
    		if(f == 1)
    		{ //提交审批按钮
    			if(vm.snapshotData.dealFlag == 3 || vm.snapshotData.dealFlag == 60003){
    				return 1;
    			}else{
    				return 0;
    			}
    		}
    	}
    	
    	if(num == 5)
    	{//财务处理
    		if(f == 1)
    		{ //财务处理按钮
    			if(vm.snapshotData.dealFlag == 60002){
    				return 1;//审核通过
    			}else{
    				return 0;
    			}
    		}
    	}
    };
    vm.checkDiffFlag = true;
    vm.checkDiff = function(){
    	//验证设置多库位盘点数，是否与总盘点数相等
    	if(!vm.checkDiffFlag){
    		return;
    	}
    	 vm.checkDiffFlag = false;
    	 MainCtrl.blockUI({
         	animate: true
         });
    	upida.post("stock/inventory/checkDiffPlance", vm.snapshotData).then(function (data) {
    		 if(data) 
	 			{
	 				if(data.result == "fail")
	 				{
	 					vm.message = data.message;
	 					$('#static').modal();
	 				}
	 				else
	 				{
	 					vm.message = "盈亏确认已完成";
	 					$('#static').modal();
	 					vm.doQuery();
	 		        	vm.snapshotData.dealFlag = 3;
	 		        	$timeout(function(){
	 		        		vm.tabs.snapSchde.active = true;
	 		            });
	 				}
	 			}
	 			vm.checkDiffFlag = true;
	 			MainCtrl.unblockUI();
         	
         });
    }
    
    vm.snapDiffHandle = function()
    {
    	vm.historyData = {};
    	// 设置查询条件
        var condition = [];
        
        MainCtrl.blockUI({
        	animate: true
        });
        if(typeof(index) == "number"){
        	vm.snapshotData = angular.copy(vm.page.data.list[index]);
        }
    	//库存盘点的时间
        if (vm.snapshotData!= null) {
            condition.push({
                name: vm.query.snapshotDate.name,
                value: vm.snapshotData.snapshotDate
            });
            
            condition.push({
                name: "storeHouseId",
                value: vm.snapshotData.storeId
            });
            condition.push({
                name: "recordId",
                value: vm.snapshotData.recordId
            });
        }
        
        
        //condition.recordId = vm.page.materData.list[0].recordId;
        vm.page.pageNo = 1;
        vm.page.materConditionDeail = condition;
        // 查询数据
        condition.push({
            name: "copyRecId",
            value: vm.snapshotData.recordId
        });
        
        upida.post("stock/inventory/getHandleInfo", vm.snapshotData).then(function (data) {
   		 if(data) 
	 			{
	 				if(data&&data.length>0)
	 				{
	 					angular.forEach(data,function(p){
	 			    		if(!p.snapQtyPnlA)
	 			    		{
	 			    			p.snapQtyPnlA = 0;
	 			    		}
	 			    		if(!p.snapQtyPnlB)
	 			    		{
	 			    			p.snapQtyPnlB = 0;
	 			    		}
	 			    		if(!p.qtyA)
	 			    		{
	 			    			p.qtyA = 0;
	 			    		}
	 			    		if(!p.qtyB)
	 			    		{
	 			    			p.qtyB = 0;
	 			    		}
	 			    		
	 			    	});
	 					vm.page.materDataHandle.list = data;
	 				}
	 				else
	 				{
	 					vm.page.materDataHandle.list = [];
	 				}
	 			}
        	
        });
    	vm.showSnapDiffHandle();
        MainCtrl.unblockUI();
    }
    vm.handleTypeListOne = [{"recordId":1000,"name":"开补料单"},{"recordId":1001,"name":"报废冲红"}];
    vm.handleTypeListTwo = [{"recordId":1003,"name":"报废处理"}];
    vm.editDiffHandleInfo = function(row)
    {
    	MainCtrl.blockUI({
        	animate: true
        });
    	upida.post("stock/inventory/editDiffHandleInfo",row).then(function (data) {
            // 初始化数据
            if(data && data=="success"){
            	// 提示信息
//            	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materCondition, vm.page.materUrl,2);
            }else{
            	vm.message = "修改盘差处理失败";
            	$('#static').modal();
            }
          //进度条关闭
			MainCtrl.unblockUI();
        });
    };
    
    vm.compleWipDiff = function()
    {
    	var reqData = {};
    	reqData.recordId = vm.snapshotData.recordId;
    	var flag = true;
    	angular.forEach(vm.page.materDataHandle.list,function(p){
    		if(!p.handleType&&p.handleType!="")
    		{
    			vm.message = p.craftNo+"盘点处理方式不能为空";
   			 	$('#static').modal();
   			 	flag = false;
   			 	return;
    		}
    		if(!p.handleOrderNo&&p.handleOrderNo=="")
    		{
    			vm.message = p.craftNo+"处理单号不能为空";
   			 	$('#static').modal();
   			 	flag = false;
   			 	return;
    		}
    		
    	});
    	if(!flag){
    		return;
    	}
    	vm.updateRecDealFlag(4);
    };
    
    vm.unitTypeList=[{"id":1,"name":"PCS"},{"id":2,"name":"PNL"}];
    
    vm.cateSetList = [];
    
    vm.showFlagList = [{"id":1,"name":"是"},{"id":2,"name":"否"}];
    
    vm.saveWipCopyUnit = function()
    {
    	MainCtrl.blockUI({
        	animate: true
        });
    	upida.post("stock/inventory/saveWipSet",{"copyInfoList":vm.cateSetList,"showFlag":vm.showFlag}).then(function (data) {
    		$('#showWipCopySet').modal('hide');
    		var material = {};
        	material.storeId = vm.storeId;
        	material.snapshotDate = vm.snapshotDate;
        	upida.post("stock/inventory/copyMaterial",material).then(function (data) {
                // 初始化数据
                if(data){
                	// 提示信息
                	vm.message = data;
                	vm.initInventory();
                }else{
                	// 提示信息
                	vm.message = data;
                }
                
                $('#static').modal();
              //进度条关闭
    			MainCtrl.unblockUI();
    			vm.hiddenSnapDiffHandle();
    			vm.doQuery();
            });
          //进度条关闭
        });
    }
    
    // 默认排序按时间排序排序处理
	vm.sort = {};
	vm.sort.no = {
		both: true,
		desc: false,
		asc: false
	};
	
	vm.sort.craftNo = {
			both: true,
			desc: false,
			asc: false
		};
	vm.sort.cateName = {
			both: true,
			desc: false,
			asc: false
		};
    
    vm.sortClick = function(col) {
		vm.sort[col].both = false;
		if(!vm.sort[col].desc && !vm.sort[col].asc) {
			vm.sort[col].asc = true;
		} else {
			if(vm.sort[col].asc) {
				vm.sort[col].desc = true;
				vm.sort[col].asc = false;
			} else {
				vm.sort[col].desc = false;
				vm.sort[col].asc = true;
			}
		}

		for(var p in vm.sort) {
			if(p !== col) {
				vm.sort[p].desc = false;
				vm.sort[p].asc = false;
				vm.sort[p].both = true;
			}
		}
		
		// 查询数据
        if(-1==vm.snapshotData.storeId)
        {
    		if(col=='cateName')
    		{
    			vm.query.sort.value = vm.sort[col].asc ? "bb.seqNum  ASC,mwc.cateName" : "bb.seqNum  DESC,mwc.cateName";
    		}
    		else if(col=='craftNo')
    		{
    			vm.query.sort.value = vm.sort[col].asc ? "mwc.craftNo ASC" : "mwc.craftNo  DESC";
    		}
    		else
    		{
    			vm.query.sort.value = vm.sort[col].asc ? "mwc."+col + " ASC" : "mwc."+col + " DESC";
    		}
    		
        	
        	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materConditionDeail, vm.page.diffWipCopyUrl,3);
        }
        else
        {
        	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.materConditionDeail, vm.page.materUrlDeail,3);
        }
	};
	vm.addCopyDetailFlag = true; 
	vm.addCopyDetail = function()
	{
		if(!vm.addCopyDetailFlag)
		{
			return;
		}
		if(!vm.addMaterial||!vm.addMaterial.recordId)
		{
			vm.message = "请先选择新增的物料";
			$('#static').modal();
		}
		else
		{
			MainCtrl.blockUI({
	        	animate: true
	        });
			vm.addCopyDetailFlag = false;
			vm.addParam = {};
			vm.addParam.recordId = vm.addMaterial.recordId;
			vm.addParam.materialCopyId = vm.snapshotData.recordId;
			vm.addParam.snapshotDate = vm.snapshotData.snapshotDate;
			vm.addParam.stocks = vm.addMaterial.snapStrocks;
			upida.post("stock/inventory/addCopyDetail", vm.addParam).then(function (data) {
	    		 if(data) 
		 			{
		 				if(data.result == "fail")
		 				{
		 					vm.message = data.message;
		 					$('#static').modal();
		 				}
		 				else
		 				{
		 					vm.message = "保存成功";
		 					$('#static').modal();
		 				}
		 			}
		 			vm.addCopyDetailFlag = true;
		 			vm.addMaterial = null;
		 			vm.doQueryMaster();
		 			MainCtrl.unblockUI();
	         	
	         });
		}
	}


	//查看WIP各工序面积
	vm.clickS = true;
	vm.cateAreaInfo = [];
	vm.snapAreaTotal = 0;
	vm.pcsAreaTotal = 0;
    vm.showWipCateArea = function ()
    {
    	if(!vm.clickS)
    	{
    		return;
    	}
    	vm.clickS = false;
    	cateAreaInfo = [];
    	vm.snapAreaTotal = 0;
    	vm.pcsAreaTotal = 0;
    	vm.loadCateNameList();
    	var param = null;
    	if(vm.snapshotData.recordId)
    	{
    		param = vm.snapshotData;
    	}
    	else
    	{
    		param = vm.historyData;
    	}
    	upida.post("stock/inventory/getAllCateAreaInfo", param).then(function (data) {
   		 if(data) 
   		 {
   			vm.cateAreaInfo = data;
   			angular.forEach(vm.cateAreaInfo,function(p){
   				vm.snapAreaTotal += p.snapArea;
   				vm.pcsAreaTotal += p.pcsArea;
   	    	});
	 	 }
	 			vm.clickS = true;
	 			vm.showWipArea();
        	
        });
    }
	
    vm.backTwoEdit = function(){
    	//关闭盈亏确认
    	vm.hiddenSnapDeail();
    	vm.updateRecDealFlag(1);
    }
    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
    });
    /* =============================库存盘点 end 2016.11.21 fzd================================ */ 
}]);