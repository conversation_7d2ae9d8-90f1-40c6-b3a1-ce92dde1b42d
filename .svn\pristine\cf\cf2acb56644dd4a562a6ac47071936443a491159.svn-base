const bus_message = {
    template: '#bus_message',
    destroyed () {
        window.removeEventListener('scroll',this.scrollEvent,false);
    },
    beforeRouteLeave(to, from, next){
        window.removeEventListener('scroll',this.scrollEvent,false);
        next();
    },
    activated(){
        this.scroll();
    },
    created:function(){
        this.$parent.changeClass(2);
        this.getMessageList();
        this.scroll();
    },
    data(){
        return {
            pageFlag: 1,
            serchMsg: "",
            phoneBookList: [],
            messageList: [],
            pageNo: 0,
            pageSize: 20,
            isLoading: false,
            isMore: true
        }
    },
    methods: {
        queryList(){
            this.pageNo = 0;
            this.isLoading = false;
            if(this.pageFlag==1){
                this.getMessageList();
            }
        },
        scroll(){
            window.addEventListener('scroll',this.scrollEvent);
        },
        scrollEvent(){
            const top = document.documentElement.scrollTop ? document.documentElement.scrollTop : document.body.scrollTop;
            const bottomOfWindow = document.body.scrollHeight - top - window.innerHeight <= 100;
            if (bottomOfWindow && this.isMore && !this.isLoading){
                this.isLoading = true;
                this.pageNo = this.pageNo + 1;
                if(this.pageFlag==1){
                    this.getMessageList();
                }else if (this.pageFlag==2){
                    this.getPhoneBook();
                }
            }
        },
        getMessageList: function (){
            if (this.pageNo == 0) {
                this.messageList = [];
            }
            const entity = {};
            entity.pageNo = this.pageNo * this.pageSize;
            entity.pageSize = this.pageSize;
            entity.message = this.serchMsg;
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type:"post",
                url:ctx + "/f/wechat/business/getMessageList",
                data:JSON.stringify(entity),
                contentType:"application/json",
                success:function(data)
                {
                    $('#loadingModal').modal('hide');
                    if (_this.pageNo > 0) {
                        for (let i=0;i<data.length;i++){
                            _this.messageList.push(data[i]);
                        }
                    }else {
                        _this.messageList = data;
                    }
                    _this.isLoading = false;
                }
            })
        },
        updateFlag: function (item) {
            const entity = {};
            entity.flag = 2;
            entity.recordId = item.recordId;
            $('#loadingModal').modal();
            $.ajax({
                type:"post",
                url:ctx + "/f/wechat/business/updateFlag",
                data:JSON.stringify(entity),
                contentType:"application/json",
                success:function(data)
                {
                    $('#loadingModal').modal('hide');
                    item.flag = 2;
                }
            })
        },
        getDataList: function () {
            if (this.pageFlag == 1){
                this.getMessageList();
            }else if (this.pageFlag == 2){
                this.getPhoneBook();
            }
        },
        changeDataList: function (flag) {
            this.pageNo = 0;
            this.isLoading = false;
            this.pageFlag = flag;
            window.scrollTo(0, 0);
            if (flag == 1){
                this.getMessageList();
            }else if (flag == 2){
                this.getPhoneBook();
            }
        },
        getPhoneBook: function () {
            if (this.pageNo == 0) {
                this.phoneBookList = [];
            }
            const entity = {};
            entity.serchMsg = this.serchMsg;
            entity.pageNo = this.pageNo * this.pageSize;
            entity.pageSize = this.pageSize;
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type:"post",
                url:ctx + "/f/wechat/business/getPhoneBookList",
                data:JSON.stringify(entity),
                contentType:"application/json",
                success:function(data)
                {
                    $('#loadingModal').modal('hide');
                    if (_this.pageNo > 0) {
                        for (let i=0;i<data.length;i++){
                            _this.phoneBookList.push(data[i]);
                        }
                    }else {
                        _this.phoneBookList = data;
                    }
                    _this.isLoading = false;
                }
            })
        },
        quickOperation: function () {
            $('#quickOperation').modal();
        }
    }
}

