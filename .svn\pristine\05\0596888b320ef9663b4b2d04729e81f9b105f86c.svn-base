package com.kyb.pcberp.modules.hr.payment_center.pojo;

import com.kyb.pcberp.common.persistence.DataEntity;

public class Hr_ClockIn extends DataEntity<Hr_ClockIn> {

    private String tissueId;

    private String versioNumber; //版本号

    private String startTime; //开始时间

    private  String endTime; //结束时间

    private String showName; //组织架构名称

    private String phone;

    private String departmentName;

    public String getTissueId() {
        return tissueId;
    }

    public void setTissueId(String tissueId) {
        this.tissueId = tissueId;
    }

    public String getVersioNumber() {
        return versioNumber;
    }

    public void setVersioNumber(String versioNumber) {
        this.versioNumber = versioNumber;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }
}
