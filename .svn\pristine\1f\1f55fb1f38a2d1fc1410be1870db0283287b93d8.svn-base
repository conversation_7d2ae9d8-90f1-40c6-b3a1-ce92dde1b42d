package com.kyb.pcberp.modules.finance.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.modules.crm.entity.AdjustBalance;

@SuppressWarnings("serial")
public class SingleReceivableDetail extends DataEntity<SingleReceivableDetail>
{
    private String companyId;
    
    private String singleReceivableId; // 应收单id
    
    private SingleReceivable singleReceivable; // 应收单
    
    private String type; // 类型：1送货出库，2客诉送货出库，3客诉退货入库，4.账单明细调整记录，5.账单调整记录
    
    private String goodsCheckId; // 源记录id(type区分)
    
    private String customerPo; // 客户订单号
    
    private String specification; // 产品规格型号
    
    private Integer quantity; // 数量
    
    private BigDecimal price; // 单价
    
    private BigDecimal engineeringFee; // 工程费
    
    private BigDecimal sampleFee; // 样品费
    
    private BigDecimal testShelfFee; // 测试架费
    
    private BigDecimal mouldFee; // 模具费
    
    private BigDecimal filmFee; // 菲林费用
    
    private BigDecimal othersFee; // 其它费用
    
    private BigDecimal amount; // 总价
    
    private Integer spares; // 备品
    
    private String craftNo; // 生产编号
    
    private BigDecimal pnlLength; // PNL长度
    
    private BigDecimal pnlWidth; // PNL宽
    
    private BigDecimal pnlDivisor; // 拼接数
    
    private BigDecimal confirmAmount; // 确认收款金额
    
    private String status; // 状态
    
    private BigDecimal singAmount; // 应收金额
    
    private String currencyType; // 货币类型
    
    private BigDecimal precent; // 汇率
    
    private String currencyTypeValue;
    
    private List<AdjustBalance> balanceList;
    
    private String detailId;
    
    private String collectId;

    private BigDecimal area;

    private BigDecimal amont;

    private String customerModel;

    private String departName;

    private String customerName;

    private String period; //对账月份

    private Date collectedDate; //收款日期

    private String paymentTerm; //收款账期

    private String billNo;

    private String name;

    private String companyName;

    private String custName;

    private String deptName;

    private Integer inOutFlag;

    public String getCompanyId()
    {
        return companyId;
    }
    
    public String getSingleReceivableId()
    {
        return singleReceivableId;
    }
    
    public SingleReceivable getSingleReceivable()
    {
        return singleReceivable;
    }
    
    public String getType()
    {
        return type;
    }
    
    public String getGoodsCheckId()
    {
        return goodsCheckId;
    }
    
    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }
    
    public void setSingleReceivableId(String singleReceivableId)
    {
        this.singleReceivableId = singleReceivableId;
    }
    
    public void setSingleReceivable(SingleReceivable singleReceivable)
    {
        this.singleReceivable = singleReceivable;
    }
    
    public void setType(String type)
    {
        this.type = type;
    }
    
    public void setGoodsCheckId(String goodsCheckId)
    {
        this.goodsCheckId = goodsCheckId;
    }
    
    public String getCustomerPo()
    {
        return customerPo;
    }
    
    public String getSpecification()
    {
        return specification;
    }
    
    public Integer getQuantity()
    {
        return quantity;
    }
    
    public BigDecimal getPrice()
    {
        return price;
    }
    
    public BigDecimal getEngineeringFee()
    {
        return engineeringFee;
    }
    
    public BigDecimal getSampleFee()
    {
        return sampleFee;
    }
    
    public BigDecimal getTestShelfFee()
    {
        return testShelfFee;
    }
    
    public BigDecimal getMouldFee()
    {
        return mouldFee;
    }
    
    public BigDecimal getFilmFee()
    {
        return filmFee;
    }
    
    public BigDecimal getOthersFee()
    {
        return othersFee;
    }
    
    public BigDecimal getAmount()
    {
        return amount;
    }
    
    public Integer getSpares()
    {
        return spares;
    }
    
    public String getCraftNo()
    {
        return craftNo;
    }
    
    public BigDecimal getPnlLength()
    {
        return pnlLength;
    }
    
    public BigDecimal getPnlWidth()
    {
        return pnlWidth;
    }
    
    public BigDecimal getPnlDivisor()
    {
        return pnlDivisor;
    }
    
    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }
    
    public void setSpecification(String specification)
    {
        this.specification = specification;
    }
    
    public void setQuantity(Integer quantity)
    {
        this.quantity = quantity;
    }
    
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }
    
    public void setEngineeringFee(BigDecimal engineeringFee)
    {
        this.engineeringFee = engineeringFee;
    }
    
    public void setSampleFee(BigDecimal sampleFee)
    {
        this.sampleFee = sampleFee;
    }
    
    public void setTestShelfFee(BigDecimal testShelfFee)
    {
        this.testShelfFee = testShelfFee;
    }
    
    public void setMouldFee(BigDecimal mouldFee)
    {
        this.mouldFee = mouldFee;
    }
    
    public void setFilmFee(BigDecimal filmFee)
    {
        this.filmFee = filmFee;
    }
    
    public void setOthersFee(BigDecimal othersFee)
    {
        this.othersFee = othersFee;
    }
    
    public void setAmount(BigDecimal amount)
    {
        this.amount = amount;
    }
    
    public void setSpares(Integer spares)
    {
        this.spares = spares;
    }
    
    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }
    
    public void setPnlLength(BigDecimal pnlLength)
    {
        this.pnlLength = pnlLength;
    }
    
    public void setPnlWidth(BigDecimal pnlWidth)
    {
        this.pnlWidth = pnlWidth;
    }
    
    public void setPnlDivisor(BigDecimal pnlDivisor)
    {
        this.pnlDivisor = pnlDivisor;
    }
    
    public String getTypeStr()
    {
        String result = null;
        if (type != null)
        {
            switch (type)
            {
                case "1":
                    result = "送货出库";
                    break;
                case "2":
                    result = "客诉送货出库";
                    break;
                case "3":
                    result = "客诉退货入库";
                    break;
                case "4":
                    result = "对账单明细调整记录";
                    break;
                case "5":
                    result = "对账单调整记录";
                    break;
            }
        }
        return result;
    }
    
    public BigDecimal getConfirmAmount()
    {
        return confirmAmount;
    }
    
    public void setConfirmAmount(BigDecimal confirmAmount)
    {
        this.confirmAmount = confirmAmount;
    }
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public String getStatusStr()
    {
        String result = null;
        if (status != null)
        {
            switch (status)
            {
                case "1001":
                    result = "未收款";
                    break;
                case "1002":
                    result = "已收款";
                    break;
                case "1003":
                    result = "已结清";
                    break;
            }
        }
        return result;
    }
    
    public BigDecimal getSingAmount()
    {
        return singAmount;
    }
    
    public void setSingAmount(BigDecimal singAmount)
    {
        this.singAmount = singAmount;
    }
    
    public String getCurrencyType()
    {
        return currencyType;
    }
    
    public void setCurrencyType(String currencyType)
    {
        this.currencyType = currencyType;
    }
    
    public BigDecimal getPrecent()
    {
        return precent;
    }
    
    public String getCurrencyTypeValue()
    {
        return currencyTypeValue;
    }
    
    public void setPrecent(BigDecimal precent)
    {
        this.precent = precent;
    }
    
    public void setCurrencyTypeValue(String currencyTypeValue)
    {
        this.currencyTypeValue = currencyTypeValue;
    }
    
    public BigDecimal getPrecentAmount()
    {
        if (null == precent)
        {
            precent = new BigDecimal(1);
        }
        if (null == amount)
        {
            amount = BigDecimal.ZERO;
        }
        BigDecimal result = amount.multiply(precent).setScale(4, BigDecimal.ROUND_HALF_UP);
        return result;
    }
    
    public List<AdjustBalance> getBalanceList()
    {
        return balanceList;
    }
    
    public void setBalanceList(List<AdjustBalance> balanceList)
    {
        this.balanceList = balanceList;
    }
    
    public String getDetailId()
    {
        return detailId;
    }
    
    public String getCollectId()
    {
        return collectId;
    }
    
    public void setDetailId(String detailId)
    {
        this.detailId = detailId;
    }
    
    public void setCollectId(String collectId)
    {
        this.collectId = collectId;
    }
    
    // 实体深拷贝
    public SingleReceivableDetail clone()
    {
        SingleReceivableDetail o = null;
        try
        {
            o = (SingleReceivableDetail)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } // Object 中的clone()识别出你要复制的是哪一个对象。
        return o;
    }

    public BigDecimal getArea()
    {
        return area;
    }

    public void setArea(BigDecimal area)
    {
        this.area = area;
    }

    public BigDecimal getAmont() {
        return amont;
    }

    public void setAmont(BigDecimal amont) {
        this.amont = amont;
    }

    public String getCustomerModel() {
        return customerModel;
    }

    public void setCustomerModel(String customerModel) {
        this.customerModel = customerModel;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Date getCollectedDate() {
        return collectedDate;
    }

    public void setCollectedDate(Date collectedDate) {
        this.collectedDate = collectedDate;
    }
    public String getCollectedDateStr() {
        if (null != collectedDate) {
            return DateUtils.formatDate(collectedDate, "yyyy-MM-dd");
        }
        return null;
    }

    public String getPaymentTerm() {
        return paymentTerm;
    }

    public void setPaymentTerm(String paymentTerm) {
        this.paymentTerm = paymentTerm;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Integer getInOutFlag() {
        return inOutFlag;
    }

    public void setInOutFlag(Integer inOutFlag) {
        this.inOutFlag = inOutFlag;
    }
}
