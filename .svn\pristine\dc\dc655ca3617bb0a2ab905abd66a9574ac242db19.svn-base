<%@ page contentType="text/html;charset=UTF-8"%>
<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;" ui-sref="selfhelp">快速下单</a>
        </li>
    </ul>
</div>

<tabset  class="tabset-margin-top">
	 <tab    heading="下单列表" active="selfhelpCtrl.tabs.viewForm.active" >
        <tab-heading>
            快速下单
        </tab-heading>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">快速下单{{ctrl.edit.title}}</div>
            </div>
            <div class="portlet-body">
                <form id="infoStep1" class="form-horizontal" name="editForm" ng-init="selfhelpCtrl.setFormScope(this)" novalidate="novalidate" ng-submit="selfhelpCtrl.submitMod();" ng-submit-force="true">
                  	    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" disabled ng-bind="selfhelpCtrl.edit.item.no"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                   <br/>
                  
                    <div class="row">
                           <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>客户：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select theme="bootstrap"  firstfocus="{{selfhelpCtrl.focus.main}}"
                                disabled="true"
	                                    	ng-model="selfhelpCtrl.edit.item.customer" reset-search-input="false"
	                                    	 on-select="selfhelpCtrl.validSelect()" required>
									    <ui-select-match placeholder="请选择...">{{$select.selected.shortName}}</ui-select-match>
									    <ui-select-choices repeat="item in selfhelpCtrl.refer.customerList | filter: $select.search | limitTo: selfhelpCtrl.infiniteScroll.currentItems track by $index" 
									    infinite-scroll="selfhelpCtrl.addMoreItems()"
									    infinite-scroll-distance="2">
									      <div ng-bind-html="item.no | highlight: $select.search"></div>
	                                      <small>
	                                          <span style="color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.shortName}}<br></span>
	                                      </small>
									    </ui-select-choices>
								    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">客户全称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" disabled ng-bind="selfhelpCtrl.edit.item.customer.name"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">地址：</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" disabled ng-bind="selfhelpCtrl.edit.item.customer.address"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">联系人：</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" disabled ng-bind="selfhelpCtrl.edit.item.customer.bizPerson.name"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">电话：</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" disabled ng-bind="selfhelpCtrl.edit.item.customer.phone"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">传真：</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" disabled ng-bind="selfhelpCtrl.edit.item.customer.fax"></span>
                                </div>
                            </div>
                        </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>送货方式：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select register-custom-form-control
                                                   ng-model="selfhelpCtrl.edit.item.deliveryWay" theme="bootstrap"
                                                   required> 
                                        	<ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match> 
	                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.deliverywaysList | filter: $select.search">
	                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
	                                        </ui-select-choices> 
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>货币类型：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select register-custom-form-control
                                                   ng-model="selfhelpCtrl.edit.item.currencyType" theme="bootstrap" required> 
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match> 
	                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.currencyTypeList | filter: $select.search">
	                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
	                                        </ui-select-choices> 
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>税种说明：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select register-custom-form-control on-select="selfhelpCtrl.doAllInvoicePrice()"
                                                   ng-model="selfhelpCtrl.edit.item.taxDescript" theme="bootstrap" required> 
                                        	<ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match> 
                                        	<ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.taxDescriptList | filter: $select.search">
                                            	<div ng-bind-html="item.value | highlight: $select.search"></div>
                                        	</ui-select-choices> 
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                           <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span
                                            class="required">*</span>收货地点：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input required class="form-control" type="text" placeholder=""
                                               ng-model="selfhelpCtrl.edit.item.deliveryPlace" ng-maxlength="50">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">订单号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input ng-model="selfhelpCtrl.edit.item.customerPo" class="form-control" ng-maxlength="30"  type="text" placeholder="请输入客户订单号">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>
                                    <div class="col-sm-7 col-md-10">
										<textarea  class="form-control"
                                           placeholder="备注信息" ng-model="selfhelpCtrl.edit.item.remark"
                                           ng-maxlength="255"></textarea>
                                    </div>
                                </div>
                        </div>
                    </div>
                    <div id="infoStep5" class="form-group" >
                        <div class="pull-right">
                            <button type="submit" class="btn btn-primary btn-default-width" style="margin-right:40px;"  ><i class="fa fa-save"></i> 保&nbsp;存</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">下单明细列表</div>
                <div id="infoStep4" class="actions">
		            <div class="portlet-input input-inline input-small" >
						<button type="button" class="btn btn-success btn-default-width"  ng-click="selfhelpCtrl.showDetailForm(true)"  ><i class="fa fa-plus"></i> 添加明细</button>
					</div>
                </div>
            </div>
            <div class="portlet-body">
            	 <div id="infoStep2" class="table-responsive">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                    	<thead>
                        <tr class="heading">
	                        <th>客户型号</th>
	                        <th>外型规格</th>
	                        <th>PCB类型</th>
	                        <th>覆铜板材</th>
	                        <th>板材厚度</th>
	                        <th>数&nbsp;量</th>
	                        <th>平米</th>
	                        <th id="infoStep3" ng-if="selfhelpCtrl.isshow">操作</th>
	                    </tr>
	                    </thead>
	                    <tbody>
	                    <tr ng-repeat="row in selfhelpCtrl.page.quotationDetail.data.list" ng-dblclick="selfhelpCtrl.ctrlShowQuotationDetail($index)">
	                        <td><a ng-click="selfhelpCtrl.ctrlShowQuotationDetail($index)" ng-bind="row.customerModel"></a></td>
	                        <td ng-bind="row.pnlSize"></td>
	                        <td ng-bind="row.boardLevel | dictFilter: selfhelpCtrl.refer.boardLevelList"></td>
	                        <td ng-bind="row.materialType | dictFilter: selfhelpCtrl.refer.materialTypeList"></td>
	                        <td ng-bind="row.boardThickness | dictFilter: selfhelpCtrl.refer.boardThicknessList"></td>
	                     	<td class="text-right" ng-bind="row.sampleAmt | currency:'':3"></td>
	                     	<td class="text-right" ng-bind="row.deailArea"></td>
	                        <td ng-if="selfhelpCtrl.isshow">
	                            <!-- 无编辑权限时仅能查看 -->
	                            <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="selfhelpCtrl.delQuotationDetail($index)"><i class="fa fa-times font-red"></i> 删除</a>
	                        </td>
	                    </tr>
	                    </tbody>
	                </table>
	            </div>
            </div>
            <div class="panel-body" style="border: 1px solid #cccccc;">
	                   <div class="row">
	                       <div class="col-md-12 col-lg-8">
	                           <div class="form-group">
	                               <h3><label class="col-sm-3 col-md-2 control-label">税前(订单总金额)：</label></h3>
	                               <div class="col-sm-7 col-md-3">
	                               <input class="form-control" type="text" ng-model="selfhelpCtrl.edit.item.totalAmtBefore" disabled />
	                               </div>
	                           </div>
	                       </div>
	                       <div class="col-md-12 col-lg-8">
	                           <div class="form-group">
	                               <h3><label class="col-sm-3 col-md-2 control-label">税后(订单总金额)：</label></h3>
	                               <div class="col-sm-7 col-md-3">
	                               <input class="form-control" type="text" ng-model="selfhelpCtrl.edit.item.totalAmt" disabled />
	                               </div>
	                           </div>
	                       </div>
	                    </div>	
                </div>
        </div>
    </tab>
	
	 <tab active="selfhelpCtrl.tabs.detailForm.active" ng-show="selfhelpCtrl.tabs.detailForm.show">
        <tab-heading >
           下单明细详情 <i class="fa fa-times set-cursor-pointer" ng-click="selfhelpCtrl.hideDetailForm()"></i>
        </tab-heading>
        <div class="portlet light bordered ">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">下单明细{{selfhelpCtrl.edit.detail.btnTitle}}</div>
            </div>
            <div class="portlet-body form">
                <form id="detailInfoStep1" class="form-horizontal" role="form" name="detailForm" novalidate="novalidate"
                      ng-submit="selfhelpCtrl.submitModDetail();" ng-submit-force="true" ng-init="selfhelpCtrl.setFormScopeDetail(this)" >
                    <div class="row">
                        <div class="col-md-12 col-lg-8">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label">单号</label>
                                <div class="col-sm-7 col-md-10">
                                    <span class="form-control" disabled ng-bind="selfhelpCtrl.edit.item.no"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 col-lg-4 control-label"><span class="required">*</span>PCS尺寸(mm)：</label>
                                <div class="col-sm-7 col-md-10 col-lg-8">
                                	<div class="row">
                                		<div class="col-sm-1-custom-first"><label class="control-label">长</label></div>
                                		<div class="col-sm-5-custom-unit">
                                			<input class="form-control" type="text" name="unitLength" 
                                                   ng-model="selfhelpCtrl.edit.detail.item.unitLength" ng-blur="selfhelpCtrl.doSetPrice()" required ng-Pcslengthwidth ng-maxlength="12" />
                                		</div>
                                		<div class="col-sm-1-custom-second "><label class="control-label">宽</label></div>
                                		<div class="col-sm-5-custom-unit">
                                			<input class="form-control" type="text" name="unitWidth" 
                                                   ng-model="selfhelpCtrl.edit.detail.item.unitWidth" ng-blur="selfhelpCtrl.doSetPrice()" required ng-Pcslengthwidth ng-maxlength="12" />
                                		</div>
                                	</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 col-lg-8">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label">交货尺寸(mm)：</label>
                                <div class="col-sm-7 col-md-10">
                                	<div class="row">
                                		<div class="col-sm-1"><label class="control-label">长</label></div>
                                		<div class="col-sm-3" style="padding-left: 0px !important">
                                			<input class="form-control" type="text" ng-blur="selfhelpCtrl.doSetPrice()"
                                               ng-model="selfhelpCtrl.edit.detail.item.pnlLength" ng-Pcslengthwidth />
                                		</div>
                                		<div class="col-sm-1-custom" ><label class="control-label">宽</label></div>
                                		<div class="col-sm-3" style="padding-left: 0px !important">
                                			 <input class="form-control" type="text"  ng-blur="selfhelpCtrl.doSetPrice()"
                                                   ng-model="selfhelpCtrl.edit.detail.item.pnlWidth" ng-Pcslengthwidth />
                                		</div>
                                		<div class="col-sm-1-custom"><label class="control-label">/</label></div>
                                		<div class="col-sm-3" style="padding-left: 0px !important">
                                			  <input class="form-control" type="text" ng-blur="selfhelpCtrl.doSetPrice()"
                                                   ng-model="selfhelpCtrl.edit.detail.item.pnlDivisor" ng-onlynumbe />
                                		</div>
                                		<div class="col-sm-1-custom"><label class="control-label">PCS</label></div>
                                	</div>
                                </div>
                            </div>
                        </div>
                    </div>
					
					<div class="row">
                        <div class="col-md-12 col-lg-8">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>客户型号：</label>
                                <div class="col-sm-7 col-md-10">
                                     <input class="form-control" type="text" 
                                     placeholder="请输入客户型号"  ng-blur="selfhelpCtrl.changecustomerModel(selfhelpCtrl.edit.detail.item.customerModel)"
                                           ng-model="selfhelpCtrl.edit.detail.item.customerModel"  required ng-maxlength="130" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>PCS数量：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input class="form-control" type="text" required
                                     ng-model="selfhelpCtrl.edit.detail.item.sampleAmt"
                                     ng-blur="selfhelpCtrl.doSetPrice()"
                                           ng-onlynumber />
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>PCB类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.boardLevel" theme="bootstrap" firstfocus="{{selfhelpCtrl.focus.detail}}"
                                               on-select="selfhelpCtrl.doSetPrice()" required >
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.boardLevelList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>覆铜板材：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.materialType" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()" required >
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.materialTypeList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>板材厚度：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.boardThickness" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()" required >
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.boardThicknessList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>覆铜要求：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.copperCladThickness" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()" required >
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.copperCladThicknessList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>镀层处理：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.surfaceProcess" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()" required >
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.surfaceProcessList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>阻焊类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.solderMaskType" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()" required >
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.solderMaskTypeList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>板面字符：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.characterType" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()" required >
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.characterTypeList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>成型方式：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.shapingWay" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()" required >
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.shapingWayList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>测试要求：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.testMethod" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()" required >
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.testMethodList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">最小线宽/线距：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.lingeSpacing" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.lingeSpacingList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">最小孔径：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.smallAperture" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()" >
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.smallApertureList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">半孔：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.halAhole" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()" >
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.halAholeList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                       <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">埋盲孔：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.buryBlindHole" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.buryBlindHoleList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">抗阻：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.resistance" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.resistanceList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
						                      
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">是否加急：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.deliveryUrgent" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.deliveryUrgentList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">导热：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.daore" theme="bootstrap" 
                                               on-select="selfhelpCtrl.doSetPrice()">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.daoreList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">耐压：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.naiya" theme="bootstrap"
                                               on-select="selfhelpCtrl.doSetPrice()">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.naiyaList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">收货期限：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="selfhelpCtrl.edit.detail.item.deliveryDays" theme="bootstrap">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in selfhelpCtrl.refer.deliverydaysList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                    </div>

					<div class="row">
                        <div class="col-md-12 col-lg-8">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label">备注：</label>
                                <div class="col-sm-7 col-md-10">
                                    <textarea class="form-control" placeholder="备注信息" ng-model="selfhelpCtrl.edit.detail.item.remark" ng-maxlength="255"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="panel-body" style="border: 1px solid #cccccc;">
	                   <div class="row">
	                       <div class="col-md-12 col-lg-8">
	                           <div class="form-group">
	                               <h3><label class="col-sm-3 col-md-2 control-label">工艺/㎡价格：</label></h3>
	                               <div class="col-sm-7 col-md-3">
	                               <input class="form-control" type="text" ng-model="selfhelpCtrl.edit.detail.item.price" disabled />
	                               </div>
	                           </div>
	                       </div>
	                       <div class="col-md-12 col-lg-8">
	                           <div class="form-group">
	                               <h3><label class="col-sm-3 col-md-2 control-label">工程费/㎡价格：</label></h3>
	                               <div class="col-sm-7 col-md-3">
	                               <input class="form-control" type="text" ng-model="selfhelpCtrl.edit.detail.item.engineeringFee" disabled />
	                               </div>
	                           </div>
	                       </div>
	                       <div class="col-md-12 col-lg-8">
	                           <div class="form-group">
	                               <h3><label class="col-sm-3 col-md-2 control-label">总平米：</label></h3>
	                               <div class="col-sm-7 col-md-3">
	                               <input class="form-control" type="text" ng-model="selfhelpCtrl.edit.detail.item.deailArea" disabled />
	                               </div>
	                           </div>
	                       </div>
	                       <div class="col-md-12 col-lg-8">
	                           <div class="form-group">
	                           
	                               <h3><label class="col-sm-3 col-md-2 control-label">总金额：</label></h3>
	                               <div class="col-sm-7 col-md-3">
	                               <input class="form-control" type="text" ng-model="selfhelpCtrl.edit.detail.item.totalAmtBefore" disabled />
	                               </div>
	                           </div>
	                       </div>
	                    </div>	
                    </div>
                    <br/><br/><br/>
                    <hr ng-if="selfhelpCtrl.right.edit && selfhelpCtrl.isshow === false"/>
                    <div id="detailInfoStep2" class="row">
	                    <div class="col-sm-offset-4">
	                        <button type="reset" class="btn btn-default btn-default-width" style="margin-left:15px;"  ng-click="selfhelpCtrl.resetQuotationDetailCopy()" ><i class="fa fa-mail-reply"></i> 重&nbsp;置</button>
	                        <button type="submit" class="btn btn-default-width" style="margin-left:15px;" ng-class="{'green': selfhelpCtrl.edit.detail.isAdd, 'purple-plum': !selfhelpCtrl.edit.detail.isAdd}" ><i class="fa" ng-class="{'fa-plus':selfhelpCtrl.edit.detail.isAdd, 'fa-edit': !selfhelpCtrl.edit.detail.isAdd}"></i> {{selfhelpCtrl.edit.detail.btnDesc}}</button>
	                    </div>
                    </div>
                </form>
            </div>
        </div>
    </tab>
</tabset>

<div class="row">
    <div class="col-md-12">
        <div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">提示</h4>
                    </div>
                    <div class="modal-body">
                        <p><span ng-bind="selfhelpCtrl.message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="staticDetailRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">删除下单明细</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="selfhelpCtrl.edit.detail.delMsg"></p></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="selfhelpCtrl.doDelQuotationDetail()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>	

<div class="row">
	<div class="col-md-12">
		<div id="staticNois" class="modal fade" tabindex="-1"
			data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true"></button>
						<h4 class="modal-title">操作提示</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="col-sm-1 text-center">
							<i class="fa fa-warning font-yellow"
								style="font-size: 30px !important;"></i>
						</div>
						<div class="col-sm-11">当前编号已经被用将自动为您累加，是否可以？</div>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue"
							ng-click="selfhelpCtrl.insertQuo()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>