<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div>
    <div class="container_title">
        <span class="container_title_text">
           <slot name="text">{{reportName}}详情</slot>
        </span>
    </div>
    <%--<div class="row">
        <div class="col">
            <div class="card card-custom card-stretch">
                <div class="card-body">
                    <div class="row align-items-center pt-2">
                        生产编号等
                        <div class="col">
                            <input class="form-control"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>--%>
    <div class="row">
        <div class="col">
            <div class="card card-custom card-stretch">
                <div class="card-body">
                    <div class="timeline timeline-5">
                        <div class="timeline-items" v-if="reportName == '订单线'">
                            <div class="timeline-item" v-if="reportList && reportList.contractDetailName">
                                <div class="timeline-media bg-light-primary">合</div>
                                <div class="timeline-desc timeline-desc-light-primary">
                                    <span class="font-weight-bolder text-primary">{{reportList.createdDate}}</span>
                                    <p class="font-weight-normal text-dark-50 pb-2">
                                        {{reportList.contractDetailName}}创建了合同明细 <%--<a href="#" class="text-primary">PO0001/SCBH0001</a>--%>
                                    </p>
                                </div>
                            </div>
                            <div class="timeline-item" v-if="reportList && reportList.approvalName">
                                <div class="timeline-media bg-light-success">审</div>
                                <div class="timeline-desc timeline-desc-light-primary">
                                    <span class="font-weight-bolder text-primary">{{reportList.lastUpdDate}}</span>
                                    <p class="font-weight-normal text-dark-50 pb-2">
                                        {{reportList.approvalName}}通过了审批
                                    </p>
                                </div>
                            </div>
                            <div class="timeline-item" v-if="reportList && reportList.cardName">
                                <div class="timeline-media bg-light-primary">卡</div>
                                <div class="timeline-desc timeline-desc-light-primary">
                                    <span class="font-weight-bolder text-primary">{{reportList.cardDateStr}}</span>
                                    <p class="font-weight-normal text-dark-50 pb-2">
                                        {{reportList.cardName}}制作了工程卡
                                    </p>
                                </div>
                            </div>
                            <div class="timeline-item" v-if="reportList && reportList.feedingName">
                                <div class="timeline-media bg-light-success">料</div>
                                <div class="timeline-desc timeline-desc-light-primary">
                                    <span class="font-weight-bolder text-primary">{{reportList.feedingDateStr}}</span>
                                    <p class="font-weight-normal text-dark-50 pb-2">
                                        {{reportList.feedingName}}进行投料
                                    </p>
                                </div>
                            </div>
                            <div class="timeline-item"v-if="reportList && reportList.countName">
                                <div class="timeline-media bg-light-primary">过数</div>
                                <div class="timeline-desc timeline-desc-light-primary">
                                    <span class="font-weight-bolder text-primary">{{reportList.countDateStr}}</span>
                                    <p class="font-weight-normal text-dark-50 pb-2">
                                        {{reportList.countName}}对工序{{reportList.processCategory}}进行过数
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="timeline-items" v-if="reportName == 'WIP'">
                            <div v-for="item in reportList" :key="item.recordId">
                                <div class="timeline-item" v-if="item.fishplateName">
                                    <div class="timeline-media bg-light-primary">接</div>
                                    <div class="timeline-desc timeline-desc-light-primary">
                                        <span class="font-weight-bolder text-primary">{{ item.takeOverTimeStr }}</span>
                                        <p class="font-weight-normal text-dark-50 pb-2">
                                            {{ item.fishplateName }}对工序{{ item.process.name }}进行接板
                                        </p>
                                    </div>
                                </div>
                                <div class="timeline-item" v-if="item.alternateName">
                                    <div class="timeline-media bg-light-success">交</div>
                                    <div class="timeline-desc timeline-desc-light-primary">
                                        <span class="font-weight-bolder text-primary">{{ item.handOverTimeStr }}</span>
                                        <p class="font-weight-normal text-dark-50 pb-2">
                                            {{ item.alternateName }}对工序{{ item.process.name }}进行交板
                                        </p>
                                    </div>
                                </div>
                                <div class="timeline-item" v-if="!item.fishplateName && item.process.name == '开料'">
                                    <div class="timeline-media bg-light-success">工</div>
                                    <div class="timeline-desc timeline-desc-light-primary">
                                        <p class="font-weight-normal text-dark-50 pb-2">
                                            正在对工序{{ item.process.name }}进行接板
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>