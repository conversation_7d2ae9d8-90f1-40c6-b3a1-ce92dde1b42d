const routes = [
	{
		path: '/workDeail',
    	component: workDeail,
    	children:[
    		{
    			path: '',
	  	    	name: 'produce',
	  	    	component: produce
	  	    },
	  	    {
	  			path: 'inspection',
	  	    	name: 'inspection',
	  	    	component: inspection
	  	    },
	  	    {
			    path: 'information',
				name: 'information',
				component: information
	  	    }
	  	]
    },
	{
	    path: '/',
	    component: produceMenu,
	    children:[
  		  {
  			  path: '',
  	    	  name: 'work',
  	    	  component: work
  	      },
  	      {
  	    	  path: '/rank',
  	    	  component: rank
  	      },
  	      {
		      path: 'my',
			  name: 'my',
			  component: my
  	      }
  	    ]
	},
	{
	    path: '/scanWork',
	    component: scanWork
	},
	{
	    path: '/coutList',
	    component: coutList
	},
	{
		path: '/produceTypeList',
		component: produceTypeList
	},
	{
		path: '/preparationTask',
		component: preparationTask
	},
	{
	    path: '/waitProDeail',
	    component: waitProDeail
	},
	{
	    path: '/person',
	    component: person
	},
	{
	    path: '/login',
	    component: login
	},
	{
	    path: '/register',
	    component: register
	},
	{
	    path: '/editPwd',
	    component: editPwd
	},
	{
		path: '/taskList',
		component: taskList
	},
]
const router = new VueRouter({
	routes
})