<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.sys.dao.ParameterSetDao">
    
	<sql id="ParameterSetColumns">
		a.recordId AS "recordId",
		a.companyId AS "company.recordId",
		a.parameterName,
		a.parameterValue,
		a.unit,
		a.jianPin,
		a.start,
		a.createdBy AS "createdBy.recordId",
		a.createdDate
	</sql>
	
	<sql id="codegenJoins"></sql>
    
	<select id="get" resultType="ParameterSet">
		SELECT 
			<include refid="ParameterSetColumns"/>
		FROM md_codegen a
		<include refid="codegenJoins"/>
		WHERE a.companyId = #{company.recordId} and a.codeType = #{codeType}
	</select>
	
	<select id="getParameterSetByCompanyAndjianPin" resultType="ParameterSet">
		SELECT 
			<include refid="ParameterSetColumns"/>
		FROM md_parameterset a
		<where>
			a.companyId = #{company.recordId}
			<if test="jianPin != null and jianPin != ''">
				AND a.jianPin = #{jianPin} 
			</if>
		</where>
	</select>
	
	<select id="findList" resultType="ParameterSet">
		SELECT 
			<include refid="ParameterSetColumns"/>
		FROM md_parameterset a
		<where>
			a.companyId = #{company.recordId}
			<if test="jianPin != null and jianPin != ''">
				AND a.jianPin = #{jianPin} 
			</if>
			<if test="parameterValue != null and parameterValue != ''">
				AND a.parameterValue = #{parameterValue} 
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="ParameterSet">
		SELECT 
			<include refid="ParameterSetColumns"/>
		FROM md_codegen a
		<include refid="codegenJoins"/>
		<where>
			a.companyId = #{company.recordId} 
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<insert id="insert">
		INSERT INTO md_parameterset(
			companyId,
			parameterName,
			parameterValue,
			jianPin,
			unit,
			createdBy,
			createdDate
		) VALUES (
			#{company.recordId},
			#{parameterName},
			#{parameterValue},
			#{jianPin},
			#{unit},
			#{createdBy.recordId},
			#{createdDate}
		)
	</insert>
	
	<update id="update">
		UPDATE md_parameterset SET 	
			parameterValue = #{parameterValue},
			start = #{start}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateStart">
		UPDATE md_parameterset SET 	
			start = #{start}
		WHERE recordId = #{recordId}
	</update>


	<select id="findAllCapacity" resultType="Double">
		select
			parameterValue
		from
			md_parameterset
		where
			jianPin = 'allCapacity' and companyId = #{recordId}
	</select>
	<select id = "getRawCompanyAndJianPin" resultType="Integer">
	SELECT
		`start` AS "start"
	FROM
		md_parameterset
	WHERE
		companyId = #{company.recordId}
		AND jianPin = #{jianPin}
	</select>
</mapper>