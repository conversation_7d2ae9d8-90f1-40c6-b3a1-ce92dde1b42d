const finishList = {
    template: '#finishList',
    created:function(){
        this.$store.dispatch('myStore/setUserInformation');
    },
    data(){
        return {
            myWaiteRepairList:[],
            searchInfo:'',
            waiteRepair:{},
            message:'',
            manualRepairT:{},
        }
    },
    computed: {
        user: {
            get () {
                return this.$store.state.myStore.user
            }
        },
        userInformation:{
            get () {
                return this.$store.state.myStore.userInformation
            }
        },
    },
    watch:{
        userInformation:function () {
            this.getMyWaiteRepairList();
        },
    },

    methods: {
        getMyWaiteRepairList: function () {
            const _this = this;
            const query = {};
            query.searchInfo = this.searchInfo;
            query.userId = this.user.userId;
            query.moduleId = "3";
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/getMyWaiteRepairList",
                data: JSON.stringify(query),
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    if (data) {
                        _this.myWaiteRepairList = data;
                    }
                }
            });
        },
    }
}