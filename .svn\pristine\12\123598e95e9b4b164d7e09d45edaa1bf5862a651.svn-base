<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.sys.dao.CodegenDao">
    
	<sql id="codegenColumns">
		a.recordId AS "recordId",
		a.companyId AS "company.recordId",
		a.codeType AS "codeType",
		a.regex AS "regex",
		a.nextno AS "nextno",
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.activeFlag,
		a.remark,
		a.selfDefaultCodegen
		
	</sql>
	
	<sql id="codegenJoins">
	</sql>
    
	<select id="get" resultType="Codegen">
		SELECT 
			<include refid="codegenColumns"/>
		FROM md_codegen a
		<include refid="codegenJoins"/>
		WHERE a.companyId = #{company.recordId} and a.codeType = #{codeType}
	</select>
	
	
	<select id="getByCodeType" resultType="Codegen">
		SELECT 
			<include refid="codegenColumns"/>
		FROM md_codegen a
		WHERE a.companyId = #{company.recordId} and a.codeType = #{codeType} and a.activeFlag = #{DEL_FLAG_NORMAL}
		limit 1
	</select>
	
	
	<select id="findList" resultType="Codegen">
		SELECT 
			<include refid="codegenColumns"/>
		FROM md_codegen a
		<where>
			a.companyId = #{company.recordId} and a.codeType = #{codeType}
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="Codegen">
		SELECT 
			<include refid="codegenColumns"/>
		FROM md_codegen a
		<include refid="codegenJoins"/>
		<where>
			a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
		</where>		
		ORDER BY a.codeType
	</select>
	
	<insert id="insert">
		INSERT INTO md_codegen(
			companyId,
			codeType,
			regex,
			nextno,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			activeFlag,
			remark,
			selfDefaultCodegen
		) VALUES (
			#{company.recordId},
			#{codeType},
			#{regex},
			#{nextno},
			#{createdBy.recordId},
			#{createdDate},
			#{createdBy.recordId},
			#{lastUpdDate},
			#{DEL_FLAG_NORMAL},
			#{remark},
			#{selfDefaultCodegen}
		)
	</insert>
	
	<update id="update">
		UPDATE md_codegen SET 	
			companyId = #{company.recordId},
			codeType = #{codeType},
			regex = #{regex},
			nextno = #{nextno},
			lastUpdBy = #{createdBy.recordId},
			lastUpdDate = 	#{lastUpdDate},
			remark = #{remark},
			selfDefaultCodegen = #{selfDefaultCodegen}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="addNextNo">
		UPDATE  md_codegen  SET
			nextno = IFNULL(nextno , 0) + #{nextno}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		delete from md_codegen
		WHERE codeType = #{codeType} and companyId = #{company.recordId} 
	</update>
	
</mapper>