const addPay = {
	template:'#addPay',
	computed: {
		comList: {
			get () {
				return this.$store.state.auditStore.comList
		    }
		},
		emp: {
			get () {
				return this.$store.state.myStore.emp
		    }
		},
		auditGroupDetailList:{
			get () {
				return this.$store.state.auditStore.auditGroupDetailList
		    }
		},
		organizationAuditList:{
			get () {
				return this.$store.state.auditStore.organizationAuditList
			}
		},
		categoryAuditList:{
			get () {
				return this.$store.state.auditStore.categoryAuditList
			}
		},
	},
	watch: {
		categoryAuditList:function ()
		{
			this.handCategoryAuditList();
		}
	},
	created(){
		this.getWxMsg()
		this.$store.dispatch("auditStore/getOrganizationAuditList");
		this.$store.dispatch("auditStore/getCategoryAuditList");
	},
	data(){
		return{
			audit: {expensePrice:''},
			company: {},
			localIds: [],
			serverList: [],
			localIdsCopy: [],
			clickFlag:false,
			chooseList:[],
			departmentName:"",
			categoryId:"",
			selectFlag: false,
		}
	},
	methods:{
		getWxMsg:function()
		{
			var config = {}
			config.url = location.href.split('#')[0]
			var _this = this
			$.ajax({
	     		type:"post",
	     		url:ctx + "/f/wechat/produce/getWxMsg",
	     		data:JSON.stringify(config),
	     		contentType:"application/json",
	     		success:function(data)
	     		{
	     			_this.wxConfig(data)
	     		}
	     	})
		},
		wxConfig: function (data) {
			wx.config({
		        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端console.log出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
		        appId: data.appId, // 必填，公众号的唯一标识
		        timestamp: data.timestamp, // 必填，生成签名的时间戳
		        nonceStr: data.nonceStr, // 必填，生成签名的随机串
		        signature: data.signature, // 必填，签名
		        jsApiList: [
		          'checkJsApi', 'chooseImage', 'previewImage', 'uploadImage', 'downloadImage'
		        ]
			})
	    },
		chooseImg:function(){
			var _this = this
			wx.chooseImage({
				count: 9, // 默认9
				sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
				success: function (res) {
					if(_this.localIds && _this.localIds.length > 0){
						for(var i=0;i< res.localIds.length;i++){
							_this.localIds.push(res.localIds[i])
						}
					}else{
						_this.localIds = res.localIds // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
					}
					// 上传图片
					_this.serverList = []
					_this.localIdsCopy = []
					for (let i = 0; i < _this.localIds.length; i++) {
						_this.localIdsCopy.push(_this.localIds[i])
					}
					_this.uploadImage()
				}
			});
		},
		preview:function(item){
			var _this = this
			wx.previewImage({
				current: item, // 当前显示图片的http链接
				urls: _this.localIds // 需要预览的图片http链接列表
			});
		},
		uploadImage:function(){
			var vm = this
			if(vm.localIdsCopy && vm.localIdsCopy.length > 0){
				wx.uploadImage({
					localId: vm.localIdsCopy.pop(), // 需要上传的图片的本地ID，由chooseImage接口获得
					isShowProgressTips: 1, // 默认为1，显示进度提示
					success: function (res) {
						vm.serverList.push(res.serverId)
						vm.uploadImage()
					}
				});
			}
		},
		deleteImg:function(index){
			this.localIds.splice(index,1)
		},
		submit:function(){
			if(this.clickFlag)
			{
				alert("请勿多次点击！");
				return;
			}
			var start =  $('#start').val();
			if(!this.audit.expensePrice){
				alert("请填写付款金额！");
				return;
			}
			if(isNaN(this.audit.expensePrice) || this.audit.expensePrice <= 0){
				alert("付款金额必须为大于0的数字");
				this.$set(this.audit,"expensePrice",null)
				return;
			}
			if(!this.audit.childType){
				alert("请选择币种！");
				return;
			}
			if(!this.audit.payMethod){
				alert("请填付款方式！");
				return;
			}
			if(!start){
				alert("请选择支付时间！");
				return;
			}
			if(!this.audit.payObject){
				alert("请填写支付对象！");
				return;
			}
			if(!this.audit.bank){
				alert("请填写开户行！");
				return;
			}
			if(!this.audit.bankAccount){
				alert("请填写银行账号！");
				return;
			}
			if(!this.audit.auditResult){
				alert("请填写付款原因！");
				return;
			}
			if(!this.company || !this.company.oaDepartId){
				alert("请选择来源！");
				return;
			}
			if (!this.categoryId)
			{
				alert("请选择财务类目");
				return;
			}
			if(this.auditGroupDetailList && this.auditGroupDetailList.length > 0){
		        var total = 0
		        var list = [] // 保存的分摊记录(金额大于0的)
	            for (let obj of this.auditGroupDetailList) {
            	  if(obj.amount && Number(obj.amount) > 0){
                      list.push(obj)
                      total += Number(obj.amount)
                  }
	            }
		        total = Number(total.toFixed(4))
	            if (total === 0) {
	              // 不分摊
	            } else if (total != Number(this.audit.expensePrice)) {
	              alert('所有分摊的金额要等于总金额')
	              return
	            } else {
	              this.audit.auditGroupDetailList = list
	            }
			}
			$('#loadingModal').modal()
			this.audit.categoryId = this.categoryId;
			this.audit.oaDepartId = this.company.oaDepartId;
			this.audit.empId = this.emp.recordId;
			this.audit.groupId = this.emp.company.recordId;
			this.audit.startTime = start;
			this.audit.auditType = "22013";
			this.audit.serverList = this.serverList
			this.clickFlag = true;
			var router = this.$router;
			var _this = this;
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftOA/commitAudit",
		     	data:JSON.stringify(this.audit),
		     	contentType:"application/json",
		     	success:function(data)
		     	{ 
		     		if(data == "success"){
		     			alert("提交成功");
		     			$('#loadingModal').modal('hide')
		     			router.push('/submit');
		     		}else{
		     			alert("提交失败");
		     			$('#loadingModal').modal('hide')
		     		}
					_this.clickFlag = false;
		     	}
		    })
		},
		cancle:function(){
			this.$router.push('/submit');
		},
		getPayMsg: function () {
			if (this.audit && this.audit.payObject && this.emp && this.emp.recordId) {
		        const item = {}
		        item.empId = this.emp.recordId
		        item.payObject = this.audit.payObject
		        const _this = this
		        $.ajax({
					type:"post",
			     	url:ctx + "/f/wechat/kybsoftOA/getPayMsg",
			     	data:JSON.stringify(item),
			     	contentType:"application/json",
			     	success:function(data)
			     	{
			     		if (data && data.bank) {
			     			_this.$set(_this.audit, 'bank', data.bank)
			            }
			     		if (data && data.bankAccount) {
			     			_this.$set(_this.audit, 'bankAccount', data.bankAccount)
			            }
			     	}
			    })
			}
	    },
		setAuditData:function(){
			const map = {}
			map.amount = this.audit.expensePrice
			this.$store.dispatch('auditStore/setAuditData',map)
		},
		handCategoryAuditList: function() {
			$('#categoryIdT').empty();
			let option = "";
			option += "<option value=''>" + "请选择" + "</option>";
			this.categoryAuditList.forEach(el => {
				option += "<option value='" + el.recordId + "'>" + el.categoryName + "</option>";
			});
			$("#categoryIdT").append(option);
			$("#categoryIdT").selectpicker("val", this.categoryId);
			$("#categoryIdT").selectpicker("render");
			$("#categoryIdT").selectpicker("refresh");
			$("#categoryIdT").selectpicker();
		},
		//检验数据
		verifyDate()
		{
			this.chooseList = [];
			if (this.organizationAuditList && this.organizationAuditList.length > 0)
			{
				for(let organization of this.organizationAuditList)
				{
					if (organization.allId === this.company.allId)
					{
						this.$set(organization, "selectFlag", true);
						this.$set(organization,'money',"");
						this.chooseList.push(organization);
					}else{
						this.$set(organization, "selectFlag", false);
					}
				}
			}
		},
		openCatgory()
		{
			$("#openCatgortPage").modal();
		},
		setAllSelectFlag() {
			for (let i=0;i<this.organizationAuditList.length;i++){
				if (this.selectFlag){
					this.$set(this.organizationAuditList[i], "selectFlag", false);
					if (this.organizationAuditList && this.organizationAuditList.length > 0)
					{
						let indexRemove = this.chooseList.findIndex(item => item.allId === this.organizationAuditList[i].allId);
						if (indexRemove !== -1) {
							this.chooseList.splice(indexRemove, 1);
						}
					}
				}else {
					this.$set(this.organizationAuditList[i], "selectFlag", true);
					for (let i = 0; i < this.organizationAuditList.length; i++) {
						const audit = this.organizationAuditList[i];
						const exists = this.chooseList.some(item => item.recordId === audit.allId);
						if (!exists) {
							this.chooseList.push(audit);
						}
					}
				}
			}
		},
		selectValue(item)
		{
			this.chooseList  = [];
			let amountTotal = this.audit.expensePrice;
			if (this.organizationAuditList && this.organizationAuditList.length > 0)
			{
				for(let orange of this.organizationAuditList)
				{
					if (item.allId === orange.allId)
					{
						orange.selectFlag = !item.selectFlag;
					}
					if (orange.selectFlag)
					{
						if (!amountTotal || amountTotal <= 0)
						{
							this.$set(orange,'money',"");
						}else{
							this.$set(orange,'money',(orange.scale * amountTotal) / 100);
						}
						this.chooseList.push(orange);
					}else{
						let indexRemove = this.chooseList.findIndex(item => item.allId === orange.allId);
						if (indexRemove !== -1) {
							this.chooseList.splice(indexRemove, 1);
						}
					}
				}
			}
		},
		delChooseData(index,recordId)
		{
			this.chooseList.splice(index, 1);
			if (this.organizationAuditList && this.organizationAuditList.length > 0)
			{
				for(let audit of this.organizationAuditList)
				{
					if (audit.allId === recordId)
					{
						audit.selectFlag = false;
					}
				}
			}
		},
	}
}