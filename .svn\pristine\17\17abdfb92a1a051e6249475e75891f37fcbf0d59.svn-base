/**
 * 
 */
package com.kyb.pcberp.modules.finance.entity;

import java.math.BigDecimal;

import com.kyb.pcberp.common.persistence.DataEntity;

/**
 * 收款走势Entity
 * <AUTHOR>
 * @version 2015-11-02
 */
public class Trend extends DataEntity<Trend> {
	
	private static final long serialVersionUID = 1L;
	
	private BigDecimal money;		// 收款数
	private String time;		// 收款时间
	
	private String type;
	
	private String year;
	
	
	
	public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public String getYear()
    {
        return year;
    }

    public void setYear(String year)
    {
        this.year = year;
    }

    public Trend() {
		super();
	}

	public Trend(String id){
		super(id);
	}

	public BigDecimal getMoney() {
		return money;
	}

	public void setMoney(BigDecimal money) {
		this.money = money;
	}

    public String getTime()
    {
        return time;
    }

    public void setTime(String time)
    {
        this.time = time;
    }

	


	
}