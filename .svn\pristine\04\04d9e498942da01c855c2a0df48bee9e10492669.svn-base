/**
 * 
 */
package com.kyb.pcberp.modules.sys.entity;

import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;

import com.kyb.pcberp.common.persistence.DataEntity;

/**
 * 版本Entity
 * 
 * <AUTHOR>
 * @version 2015-08-10
 */
public class Pdf extends DataEntity<Pdf>
{
    
    private static final long serialVersionUID = 1L;
    
    private String fileName;
    
    private Map<String, Object> dataMap;
    
    private ByteArrayOutputStream outPut;
    
    private List<ByteArrayOutputStream> outPutList; // 流list
    
    private String showName; // zjn pdf文件显示名称
    
    public Pdf()
    {
        super();
    }
    
    public List<ByteArrayOutputStream> getOutPutList()
    {
        return outPutList;
    }
    
    public void setOutPutList(List<ByteArrayOutputStream> outPutList)
    {
        this.outPutList = outPutList;
    }
    
    public String getFileName()
    {
        return fileName;
    }
    
    public void setFileName(String fileName)
    {
        this.fileName = fileName;
    }
    
    public Map<String, Object> getDataMap()
    {
        return dataMap;
    }
    
    public void setDataMap(Map<String, Object> dataMap)
    {
        this.dataMap = dataMap;
    }
    
    public ByteArrayOutputStream getOutPut()
    {
        return outPut;
    }
    
    public void setOutPut(ByteArrayOutputStream outPut)
    {
        this.outPut = outPut;
    }
    
    public String getShowName()
    {
        return showName;
    }
    
    public void setShowName(String showName)
    {
        this.showName = showName;
    }
    
}