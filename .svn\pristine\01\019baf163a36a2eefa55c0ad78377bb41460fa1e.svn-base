package com.kyb.pcberp.modules.quality.entity;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.eg.entity.EgProcess;
import com.kyb.pcberp.modules.production.entity.Popo;
import com.kyb.pcberp.modules.production.entity.ProduceBatch;
import com.kyb.pcberp.modules.production.entity.ProduceBatchDetail;
import com.kyb.pcberp.modules.sys.entity.User;

/**
 * WC 2018-05-25 外发品检
 *
 */
public class PopoInspect extends DataEntity<PopoInspect>
{
    
    private static final long serialVersionUID = 1L;
    
    private Notification notification; // 通知单
    
    private ProduceBatch produceBatch;
    
    private ProduceBatchDetail produceBatchDetail; // 批次明细
    
    private EgProcess process;
    
    private User checker;// 检验员
    
    private Date checkDate; // 检测日期
    
    private String reworkCause;// 返工原因
    
    private String badCause; // 不良原因
    
    private String discardCause; // 报废项目
    
    private String discardCauseOne; // 报废单位
    
    private String status;
    
    private Popo popo; // 外发单
    
    private List<ReworkBadDiscard> reworkList; // 返工列表
    
    private List<ReworkBadDiscard> badList; // 返工列表
    
    private List<ReworkBadDiscard> discardList; // 返工列表
    
    private Date startDate;
    
    private Date endDate;
    
    public Notification getNotification()
    {
        return notification;
    }
    
    public void setNotification(Notification notification)
    {
        this.notification = notification;
    }
    
    public ProduceBatch getProduceBatch()
    {
        return produceBatch;
    }
    
    public void setProduceBatch(ProduceBatch produceBatch)
    {
        this.produceBatch = produceBatch;
    }
    
    public ProduceBatchDetail getProduceBatchDetail()
    {
        return produceBatchDetail;
    }
    
    public void setProduceBatchDetail(ProduceBatchDetail produceBatchDetail)
    {
        this.produceBatchDetail = produceBatchDetail;
    }
    
    public EgProcess getProcess()
    {
        return process;
    }
    
    public void setProcess(EgProcess process)
    {
        this.process = process;
    }
    
    public User getChecker()
    {
        return checker;
    }
    
    public void setChecker(User checker)
    {
        this.checker = checker;
    }
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getCheckDate()
    {
        return checkDate;
    }
    
    public void setCheckDate(Date checkDate)
    {
        this.checkDate = checkDate;
    }
    
    public String getReworkCause()
    {
        return reworkCause;
    }
    
    public void setReworkCause(String reworkCause)
    {
        this.reworkCause = reworkCause;
    }
    
    public String getBadCause()
    {
        return badCause;
    }
    
    public void setBadCause(String badCause)
    {
        this.badCause = badCause;
    }
    
    public String getDiscardCause()
    {
        return discardCause;
    }
    
    public void setDiscardCause(String discardCause)
    {
        this.discardCause = discardCause;
    }
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public Popo getPopo()
    {
        return popo;
    }
    
    public void setPopo(Popo popo)
    {
        this.popo = popo;
    }
    
    public List<ReworkBadDiscard> getReworkList()
    {
        return reworkList;
    }
    
    public void setReworkList(List<ReworkBadDiscard> reworkList)
    {
        this.reworkList = reworkList;
    }
    
    public List<ReworkBadDiscard> getBadList()
    {
        return badList;
    }
    
    public void setBadList(List<ReworkBadDiscard> badList)
    {
        this.badList = badList;
    }
    
    public List<ReworkBadDiscard> getDiscardList()
    {
        return discardList;
    }
    
    public void setDiscardList(List<ReworkBadDiscard> discardList)
    {
        this.discardList = discardList;
    }
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getStartDate()
    {
        return startDate;
    }
    
    public void setStartDate(Date startDate)
    {
        this.startDate = startDate;
    }
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getEndDate()
    {
        return endDate;
    }
    
    public void setEndDate(Date endDate)
    {
        this.endDate = endDate;
    }
    
    public String getDiscardCauseOne()
    {
        return discardCauseOne;
    }
    
    public void setDiscardCauseOne(String discardCauseOne)
    {
        this.discardCauseOne = discardCauseOne;
    }
}
