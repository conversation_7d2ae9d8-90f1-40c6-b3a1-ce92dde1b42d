package com.kyb.pcberp.modules.purch.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.hr.group_center.pojo.Hr_DepartRelation;

import java.math.BigDecimal;

// 原料组织业务中间层
public class IngredientsMiddle extends DataEntity<IngredientsMiddle>
{
    private static final long serialVersionUID = 1L;

    private String ingredientsId;

    private String relationId;

    private String level; // 第几层

    private Hr_DepartRelation relation;

    private BigDecimal radio;

    private String payWay; // 结算方式

    private Integer radioType; // 费率类型：1：月，2：季，3：年

    public String getIngredientsId()
    {
        return ingredientsId;
    }

    public void setIngredientsId(String ingredientsId)
    {
        this.ingredientsId = ingredientsId;
    }

    public String getRelationId()
    {
        return relationId;
    }

    public void setRelationId(String relationId)
    {
        this.relationId = relationId;
    }

    public String getLevel()
    {
        return level;
    }

    public void setLevel(String level)
    {
        this.level = level;
    }

    public Hr_DepartRelation getRelation()
    {
        return relation;
    }

    public void setRelation(Hr_DepartRelation relation)
    {
        this.relation = relation;
    }

    public BigDecimal getRadio()
    {
        return radio;
    }

    public void setRadio(BigDecimal radio)
    {
        this.radio = radio;
    }

    public String getPayWay() {
        return payWay;
    }

    public void setPayWay(String payWay) {
        this.payWay = payWay;
    }

    public Integer getRadioType() {
        return radioType;
    }

    public void setRadioType(Integer radioType) {
        this.radioType = radioType;
    }

    public String getRadioTypeStr()
    {
        String result = null;
        if(null == radioType)
        {
            return result;
        }
        switch (radioType)
        {
            // 月费率
            case 1:
                result = "月费率";
                break;
            // 季费率
            case 2:
                result = "季费率";
                break;
            // 年费率
            case 3:
                result = "年费率";
                break;
        }
        return result;
    }
}
