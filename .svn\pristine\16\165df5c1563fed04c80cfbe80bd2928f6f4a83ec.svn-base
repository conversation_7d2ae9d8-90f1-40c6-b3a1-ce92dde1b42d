<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.inter.dao.ConfigCustomerAttachementsDao">

    <select id="getAttachmentList" resultType="ConfigCustomerAttachements">
        SELECT
            *,
            orgFileName AS "name"
        FROM inter_config_customer_attachements
        WHERE activeFlag = 1 AND FIND_IN_SET(configCustomerId,#{recordId})
    </select>

    <insert id="saveAttachment">
        INSERT INTO inter_config_customer_attachements
        (
            configCustomerId,
            orgFileName,
            realFileName,
            fileUrl,
            type,
            activeFlag,
            createdBy,
            createdDate
        )VALUES(
            #{configCustomerId},
            #{orgFileName},
            #{realFileName},
            #{fileUrl},
            #{type},
            1,
            #{createdBy.recordId},
            NOW()
        )
    </insert>

    <update id="deleteAttachment">
        UPDATE inter_config_customer_attachements SET
            activeFlag = 2
        WHERE recordId = #{recordId}
    </update>

    <select id="getAttachmentCount" resultType="Integer">
		SELECT
			COUNT(1)
		FROM inter_config_customer_attachements
		WHERE configCustomerId = #{configCustomerId} AND orgFileName = #{orgFileName}
	</select>

    <select id="getOrderAgreementList" resultType="OrderAgreement">
        SELECT * FROM icloud_order_agreement WHERE activeFlag = 1 ORDER BY createdDate DESC
    </select>

    <select id="getAttachementsList" resultType="ConfigCustomerAttachements">
        SELECT * FROM inter_config_customer_attachements WHERE activeFlag = 1 AND configCustomerId = #{configCustomerId}
    </select>

</mapper>