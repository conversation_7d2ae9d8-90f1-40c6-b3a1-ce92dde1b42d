<%@ page contentType="text/html;charset=UTF-8" %>
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">客户管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="crm.customer">客户资源</a>
        </li>
    </ul>
</div>
<!-- END 导航-->

<tabset class="tabset-margin-top">
    <!-- 客户列表 -->
    <%@ include file="/WEB-INF/views/modules/crm/customer/list.jsp"%>
    <!-- 客户详情 -->
    <%@ include file="/WEB-INF/views/modules/crm/customer/deail.jsp"%>
    <!-- 联系人 -->
    <%@ include file="/WEB-INF/views/modules/crm/customer/linkman.jsp"%>
    <!-- 子公司 -->
    <%@ include file="/WEB-INF/views/modules/crm/customer/company.jsp"%>
    <!-- 业务助理 -->
    <%@ include file="/WEB-INF/views/modules/crm/customer/businessassistant.jsp"%>
	<!-- 业务员 -->
	<%@ include file="/WEB-INF/views/modules/crm/customer/customerSalesMan.jsp"%>
	<!-- 客户备注 -->
	<%@ include file="/WEB-INF/views/modules/crm/customer/customerRemarks.jsp"%>
	<!-- 客户-产品类型 -->
	<%@ include file="/WEB-INF/views/modules/crm/customer/customerProductType.jsp"%>
    <!-- 客户-业务员 -->
<%--    <%@ include file="/WEB-INF/views/modules/crm/customer/customersale.jsp"%>--%>
</tabset>

<div class="row">
    <div class="col-md-12">
        <div id="staticRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">删除客户</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="ctrl.delMsg"></p></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.doDelCustomer()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="staticManRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">删除客户联系人</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="ctrl.delManItemMsg"></p></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.doDelContact()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="staticManRemove_khzgs" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">删除客户子公司</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="ctrl.delManItemMsg"></p></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.doDelContact_khzgs()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="staticDisCustomer" class="modal fade" tabindex="-1"
			data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true"></button>
						<h4 class="modal-title">分配客户到其它公司</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="form-horizontal">
							<div class="row">
								<div class="col-md-12">
			                        <div class="form-group">
			                            <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>公司：</label>
			                            <div class="col-sm-7 col-md-8">
			                                <ui-select  ng-model="ctrl.disCustomer.disCompany" theme="bootstrap"  required>
			                                    <ui-select-match placeholder="请选择..." >{{$select.selected.name}}</ui-select-match>
			                                    <ui-select-choices repeat="item in ctrl.companyList | filter: $select.search">
			                                        <div ng-bind-html="item.userName | highlight: $select.search">
			                                        </div>
			                                        <small>
			                                            <span style="color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.name}}<br></span>
			                                        </small>
			                                    </ui-select-choices>
			                                </ui-select>
			                            </div>
			                        </div>
	                            </div>
							</div>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.distributionCus()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div class="modal fade" id="batchDistributionCusStatic" tabindex="-1" role="dialog" aria-hidden="true">
		    <div class="modal-dialog modal-full">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span class="text-primary">
                        	批量分配客户
                        </span></h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 5px;">
						<form class="form-horizontal">
							<div class="portlet light bordered">
								<div class="actions">
				                    <div class="col-md-12">
				                        <div class="form-group">
				                            <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>公司：</label>
				                            <div class="col-sm-7 col-md-8">
				                                <ui-select ng-model="ctrl.disCompany" theme="bootstrap" required>
				                                    <ui-select-match placeholder="请选择..." >{{$select.selected.name}}</ui-select-match>
				                                    <ui-select-choices repeat="item in ctrl.companyList | filter: $select.search">
				                                        <div ng-bind-html="item.userName | highlight: $select.search">
				                                        </div>
				                                        <small>
				                                            <span style="color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.name}}<br></span>
				                                        </small>
				                                    </ui-select-choices>
				                                </ui-select>
				                            </div>
				                        </div>
				                    </div>
		               		    </div>
                 				<div class="portlet-body">
									<div class="row form-group">
										 <div class="table">
					                      <table class="table table-striped table-bordered table-condensed table-advance table-hover">
					                          <thead>
					                          <tr class="heading">
					                            <th>客户编号</th>
					                            <th>客户名称</th>
					                            <th>客户简称</th>
					                          </tr>
					                          </thead>
					                          <tbody>
					                          	<tr ng-repeat="record in ctrl.recordList" class="success">
													<td ng-bind="record.no"></td>
													<td ng-bind="record.name"></td>
													<td ng-bind="record.shortName"></td>
					                          	</tr>
					                          </tbody>
					                      </table>
					                  </div>
									</div>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn blue" ng-click="ctrl.batchDistributionCus()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div class="modal fade" id="saveCusSalesManStatic" tabindex="-1" role="dialog" aria-hidden="true">
			<div class="modal-dialog modal-full">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">任职区间管理</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 5px;">
						<form class="form-horizontal">
							<div class="portlet light bordered">
								<div class="actions">
									<div class="row">
										<div class="col-md-12 text-right">
											<a href="javascript:void(0)" ng-if="ctrl.openFlag == 1" class="btn btn-xs btn-default" ng-click="ctrl.addRecord()"><i class="fa fa-adjust font-blue"></i>增加行</a>
											<a href="javascript:void(0)" ng-if="ctrl.openFlag == 1" class="btn btn-xs btn-default" ng-click="ctrl.delRecord()"><i class="fa fa-times font-red"></i>删除行</a>
											<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.saveRecord()"><i class="fa fa-save font-green"></i>保存</a>
										</div>
									</div>
								</div>
								<div class="portlet-body">
									<div class="row form-group">
										<div class="table">
											<table class="table table-striped table-bordered table-condensed table-advance table-hover">
												<thead>
												<tr class="heading">
													<th>客户</th>
													<th>业务员</th>
													<th>部门</th>
													<th>客户类型</th>
													<th>开始时间</th>
													<th ng-if="ctrl.openFlag == 2" width="20%">结束时间</th>
												</tr>
												</thead>
												<tbody>
												<tr ng-repeat="row in ctrl.recordList" ng-class="row.checked?'success':''" ng-click="ctrl.changeCheked(row,2)">
													<td>
														<ui-select theme="bootstrap" ng-model="row.customerId" on-select="ctrl.setCustType(row)">
															<ui-select-match placeholder="请选择...">{{$select.selected.no}}（{{$select.selected.shortName}}）</ui-select-match>
															<ui-select-choices repeat="item.recordId as item in ctrl.selectCusList | filter: $select.search">
																<div ng-bind-html="item.no | highlight: $select.search"></div>
																<small>
																	<span style="color: red;">简称：{{item.shortName}}<br></span>
																</small>
															</ui-select-choices>
														</ui-select>
													</td>
													<td>
														<ui-select theme="bootstrap" ng-model="row.userId">
															<ui-select-match placeholder="请选择...">{{$select.selected.userName}}
															</ui-select-match>
															<ui-select-choices repeat="item.recordId as item in ctrl.users | filter: $select.search">
																<div ng-bind-html="item.userName | highlight: $select.search"></div>
															</ui-select-choices>
														</ui-select>
													</td>
													<td>
														<ui-select theme="bootstrap" ng-model="row.deptId">
															<ui-select-match placeholder="请选择...">{{$select.selected.name}}
															</ui-select-match>
															<ui-select-choices repeat="item.recordId as item in ctrl.deptListCopy| filter: $select.search">
																<div ng-bind-html="item.no | highlight: $select.search"></div>
																<div ng-bind-html="item.name | highlight: $select.search"></div>
															</ui-select-choices>
														</ui-select>
													</td>
													<td>
														<select class="form-control" ng-model="row.custType">
															<option value="1">公司分配</option>
															<option value="2">自主研发</option>
														</select>
													</td>
													<td>
														<input type="text" class="form-control"
															   ng-model="row.startDate"
															   data-date-format="yyyy-MM-dd"
															   data-date-type="number"
															   data-autoclose="1"
															   daysOfWeekDisabled="false"
															   ng-click="ctrl.checkDate(row)"
															   bs-datepicker  />
													</td>
													<td ng-if="ctrl.openFlag == 2">
														<input type="text" class="form-control"
															   ng-model="row.endDate"
															   data-date-format="yyyy-MM-dd"
															   data-date-type="number"
															   data-autoclose="1"
															   daysOfWeekDisabled="false"
															   ng-click="ctrl.checkDate(row)"
															   bs-datepicker  />
													</td>
												</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="delCusSalesManStatic" class="modal fade" tabindex="-1"
			 data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">
							<span>删除业务员设置</span>
						</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="col-sm-1 text-center">
							<i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i>
						</div>
						<div class="col-sm-11">{{ctrl.message}}</div>
					</div>
					<div class="modal-footer">
						<button  type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.deleteCusSalesMan()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

	<div class="row">
		<div class="col-md-12">
			<div id="orderSalesManInitStatic" class="modal fade" tabindex="-1"
				 data-backdrop="static" data-keyboard="false">
				<div class="modal-dialog">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
							<h4 class="modal-title">
								<span>订单业务员重置</span>
							</h4>
						</div>
						<div class="modal-body clearfix" style="padding-top: 25px;">
							<div class="col-sm-1 text-center">
								<i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i>
							</div>
							<div class="col-sm-11">{{ctrl.message}}</div>
						</div>
						<div class="modal-footer">
							<button  type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.orderSalesManInit()">确定</button>
							<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

<div class="row">
    <div class="col-md-12">
        <div id="delCustomerRemarks" class="modal fade" tabindex="-1"
             data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">
                            <span>删除客户备注设置</span>
                        </h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="col-sm-1 text-center">
                            <i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i>
                        </div>
                        <div class="col-sm-11">{{ctrl.message}}</div>
                    </div>
                    <div class="modal-footer">
                        <button  type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.deleteCustomerSale()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
	<div class="col-md-12">
		<div class="modal fade" id="saveCustomerRemarks" tabindex="-1" role="dialog" aria-hidden="true">
			<div class="modal-dialog modal-full">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title"><span class="text-primary">
						<span>客户备注保存</span>
					</span></h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 5px;">
						<form class="form-horizontal">
							<div class="portlet light bordered">
								<div class="actions">
									<div class="col-md-12 text-right">
										<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="ctrl.openFlag == 1" ng-click="ctrl.addShiftDetailRecord()"><i class="fa fa-adjust font-blue"></i>增加行</a>&nbsp;
										<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="ctrl.openFlag == 1" ng-click="ctrl.delShiftDetailRecord()"><i class="fa fa-times font-red"></i>删除行</a>&nbsp;
										<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.saveShiftDetailRecord()"><i class="fa fa-save font-green"></i>保存</a>
									</div>
								</div>
								<div class="portlet-body">
									<div class="row form-group">
										<div class="table">
											<table class="table table-striped table-bordered table-condensed table-advance table-hover">
												<thead>
												<tr class="heading">
													<th style="width: 10%">客户名称</th>
													<th style="width: 10%">客户备注</th>
												</tr>
												</thead>
												<tbody>
												<tr ng-repeat="row in ctrl.customerRemarksList" ng-class="row.checked?'success':''" ng-click="ctrl.changeCheked(row,2)">
													<td style="width: 10%">
														<ui-select theme="bootstrap" ng-model="row.customerId">
															<ui-select-match placeholder="请选择...">{{$select.selected.no}}（{{$select.selected.shortName}}）</ui-select-match>
															<ui-select-choices repeat="item.recordId as item in ctrl.selectCusList | filter: $select.search">
																<div ng-bind-html="item.no | highlight: $select.search"></div>
																<small>
																	<span style="color: red;">简称：{{item.shortName}}<br></span>
																</small>
															</ui-select-choices>
														</ui-select>
													</td>
													<td style="width: 10%">
														<input type="text" class="form-control" ng-model="row.remark"/>
													</td>
												</tr>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="LockSinglePage" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">客户：{{ctrl.lockObj.name}}锁单</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="form-horizontal">
							<div class="row">
								<div class="col-md-12">
									<div class="form-group">
										<label class="control-label col-md-3"><span class="required">*</span>锁单原因：</label>
										<div class="col-md-8">
											<textarea  class="form-control" placeholder="锁单原因" ng-model="ctrl.lockingReason"></textarea>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.definitiveLock()">提交</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="appCusSalesManStatic" class="modal fade" tabindex="-1"
			 data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">
							<span>审批业务员设置</span>
						</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="col-sm-1 text-center">
							<i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i>
						</div>
						<div class="col-sm-11">{{ctrl.message}}</div>
					</div>
					<div class="modal-footer">
						<button  type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.approvalCusSalesMan()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">提示</h4>
                    </div>
                    <div class="modal-body">
                        <p><span ng-bind="ctrl.message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>