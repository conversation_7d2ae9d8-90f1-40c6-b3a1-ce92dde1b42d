package com.kyb.pcberp.modules.wechat.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

import java.math.BigDecimal;

public class InterProductStockRecord extends DataEntity<InterProductStockRecord>
{
    private String productId;

    private String inoutType;

    private String inoutPlace;

    private String userId;

    private String userName;

    private BigDecimal initStocks;

    private BigDecimal changeStocks;

    private BigDecimal stocks;

    private BigDecimal initPrice;

    private BigDecimal price;

    private Integer initLeadTime;

    private Integer leadTime;

    private String erpId;

    private String icloudCompanyId;

    private Integer materialKind;

    private String status;

    public String getProductId()
    {
        return productId;
    }

    public void setProductId(String productId)
    {
        this.productId = productId;
    }

    public String getInoutType()
    {
        return inoutType;
    }

    public void setInoutType(String inoutType)
    {
        this.inoutType = inoutType;
    }

    public String getInoutPlace()
    {
        return inoutPlace;
    }

    public void setInoutPlace(String inoutPlace)
    {
        this.inoutPlace = inoutPlace;
    }

    public String getUserId()
    {
        return userId;
    }

    public void setUserId(String userId)
    {
        this.userId = userId;
    }

    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public BigDecimal getInitStocks()
    {
        return initStocks;
    }

    public void setInitStocks(BigDecimal initStocks)
    {
        this.initStocks = initStocks;
    }

    public BigDecimal getChangeStocks()
    {
        return changeStocks;
    }

    public void setChangeStocks(BigDecimal changeStocks)
    {
        this.changeStocks = changeStocks;
    }

    public BigDecimal getStocks()
    {
        return stocks;
    }

    public void setStocks(BigDecimal stocks)
    {
        this.stocks = stocks;
    }

    public BigDecimal getInitPrice() {
        return initPrice;
    }

    public void setInitPrice(BigDecimal initPrice) {
        this.initPrice = initPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getInitLeadTime()
    {
        return initLeadTime;
    }

    public void setInitLeadTime(Integer initLeadTime)
    {
        this.initLeadTime = initLeadTime;
    }

    public Integer getLeadTime()
    {
        return leadTime;
    }

    public void setLeadTime(Integer leadTime)
    {
        this.leadTime = leadTime;
    }

    public String getErpId()
    {
        return erpId;
    }

    public void setErpId(String erpId)
    {
        this.erpId = erpId;
    }

    public String getIcloudCompanyId()
    {
        return icloudCompanyId;
    }

    public void setIcloudCompanyId(String icloudCompanyId)
    {
        this.icloudCompanyId = icloudCompanyId;
    }

    public Integer getMaterialKind() {
        return materialKind;
    }

    public void setMaterialKind(Integer materialKind) {
        this.materialKind = materialKind;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }
}
