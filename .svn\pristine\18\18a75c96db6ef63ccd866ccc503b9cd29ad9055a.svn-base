package com.kyb.pcberp.modules.inter.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_Position;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatAudit;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatUser;

import java.util.List;

public class InterOrderConfig extends DataEntity<InterOrderConfig>
{
    private static final long serialVersionUID = 1L;

    private String configId;

    private String allId;

    private String name;

    private String pageLevel; // 1：模块，2：工艺，3：工序

    private String prompt;

    private String rowFlag; // 1：单行，2：半行，3：1/3行，4：2/3行

    private String inputType; // 1、输入框，2、PCS输入，3、PNL输入，4、单选框，5、多选框

    private String listFlag; // 是否在列表中显示1是0否

    private String sourceId;

    private String disableFlag;

    private String sortNum;

    private String unitType;

    private String inputWidth;

    private String promptPlace;

    private String promptName;

    private String promptWidth;

    private List<InterOrderConfig> list;

    private String type;

    private String value;

    private String configName;

    private String alias;

    private String enName;

    private String defaultDb;

    private String groupManageId;

    private String userId;

    private String itemId; // 字典项

    private String level;

    private String decimalPoint;

    private String defaultValue;

    private String description;

    private Integer checkStatus;

    private String dictValue;

    private String defaultValueT;

    private String itemName;

    private String mainId;

    private String no;

    private String auditTypeName;

    private String code;

    private List<InterOrderConfig> valueList;

    private List<WechatUser> userList;

    private String auditValue;

    private String detailStatus;

    private String id;

    List<WechatAudit> categoryAuditList;

    private String configDeailId;

    List<Hr_Position> positionList;

    private String auditId;

    private String version;

    public String getConfigId()
    {
        return configId;
    }

    public void setConfigId(String configId)
    {
        this.configId = configId;
    }

    public String getAllId()
    {
        return allId;
    }

    public void setAllId(String allId)
    {
        this.allId = allId;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getPageLevel()
    {
        return pageLevel;
    }

    public void setPageLevel(String pageLevel)
    {
        this.pageLevel = pageLevel;
    }

    public String getPrompt()
    {
        return prompt;
    }

    public void setPrompt(String prompt)
    {
        this.prompt = prompt;
    }

    public String getRowFlag()
    {
        return rowFlag;
    }

    public void setRowFlag(String rowFlag)
    {
        this.rowFlag = rowFlag;
    }

    public String getInputType()
    {
        return inputType;
    }

    public void setInputType(String inputType)
    {
        this.inputType = inputType;
    }

    public String getInputTypeStr() {
        String result = null;
        if (inputType == null){
            return "";
        }
        switch (inputType)
        {
            case "1":
                result = "单行文本";
                break;
            case "2":
                result = "多行文本";
                break;
            case "3":
                result = "数字";
                break;
            case "4":
                result = "整数";
                break;
            case "5":
                result = "金额";
                break;
            case "6":
                result = "单选框组";
                break;
            case "7":
                result = "复选框组";
                break;
            case "8":
                result = "日期选择器(时分秒)";
                break;
            case "9":
                result = "日期选择器(天)";
                break;
            case "10":
                result = "单选下拉框";
                break;
            case "11":
                result = "搜索下拉框";
                break;
            case "12":
                result = "多选下拉框";
                break;
            case "13":
                result = "文件上传";
                break;
            case "14":
                result = "评分";
                break;
            case "15":
                result = "员工选择框";
                break;

        }
        return result;
    }

    public String getSourceId()
    {
        return sourceId;
    }

    public void setSourceId(String sourceId)
    {
        this.sourceId = sourceId;
    }

    public String getDisableFlag()
    {
        return disableFlag;
    }

    public void setDisableFlag(String disableFlag)
    {
        this.disableFlag = disableFlag;
    }

    public String getSortNum()
    {
        return sortNum;
    }

    public void setSortNum(String sortNum)
    {
        this.sortNum = sortNum;
    }

    public String getUnitType()
    {
        return unitType;
    }

    public void setUnitType(String unitType)
    {
        this.unitType = unitType;
    }

    public String getInputWidth()
    {
        return inputWidth;
    }

    public void setInputWidth(String inputWidth)
    {
        this.inputWidth = inputWidth;
    }

    public String getPromptPlace()
    {
        return promptPlace;
    }

    public void setPromptPlace(String promptPlace)
    {
        this.promptPlace = promptPlace;
    }

    public String getPromptName()
    {
        return promptName;
    }

    public void setPromptName(String promptName)
    {
        this.promptName = promptName;
    }

    public String getPromptWidth()
    {
        return promptWidth;
    }

    public void setPromptWidth(String promptWidth)
    {
        this.promptWidth = promptWidth;
    }

    public List<InterOrderConfig> getList()
    {
        return list;
    }

    public void setList(List<InterOrderConfig> list)
    {
        this.list = list;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public String getValue()
    {
        return value;
    }

    public void setValue(String value)
    {
        this.value = value;
    }

    public String getConfigName()
    {
        return configName;
    }

    public void setConfigName(String configName)
    {
        this.configName = configName;
    }

    public String getListFlag() {
        return listFlag;
    }

    public void setListFlag(String listFlag) {
        this.listFlag = listFlag;
    }

    public String getAlias()
    {
        return alias;
    }

    public void setAlias(String alias)
    {
        this.alias = alias;
    }

    public String getEnName()
    {
        return enName;
    }

    public void setEnName(String enName)
    {
        this.enName = enName;
    }

    public String getDefaultDb()
    {
        return defaultDb;
    }

    public void setDefaultDb(String defaultDb)
    {
        this.defaultDb = defaultDb;
    }

    public String getGroupManageId()
    {
        return groupManageId;
    }

    public void setGroupManageId(String groupManageId)
    {
        this.groupManageId = groupManageId;
    }

    public String getUserId()
    {
        return userId;
    }

    public void setUserId(String userId)
    {
        this.userId = userId;
    }

    public String getItemId()
    {
        return itemId;
    }

    public void setItemId(String itemId)
    {
        this.itemId = itemId;
    }

    public String getLevel()
    {
        return level;
    }

    public void setLevel(String level)
    {
        this.level = level;
    }

    public String getDecimalPoint() {
        return decimalPoint;
    }

    public void setDecimalPoint(String decimalPoint) {
        this.decimalPoint = decimalPoint;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getCheckStatusStr() {
        String result = null;
        if (checkStatus == null){
            return "";
        }
        switch (checkStatus)
        {
            case 1:
                result = "只读";
                break;
            case 2:
                result = "隐藏";
                break;
            case 3:
                result = "必填";
                break;
        }
        return result;
    }

    public String getDictValue() {
        return dictValue;
    }

    public void setDictValue(String dictValue) {
        this.dictValue = dictValue;
    }

    public String getDefaultValueT() {
        return defaultValueT;
    }

    public void setDefaultValueT(String defaultValueT) {
        this.defaultValueT = defaultValueT;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getMainId() {
        return mainId;
    }

    public void setMainId(String mainId) {
        this.mainId = mainId;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getAuditTypeName() {
        return auditTypeName;
    }

    public void setAuditTypeName(String auditTypeName) {
        this.auditTypeName = auditTypeName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<InterOrderConfig> getValueList() {
        return valueList;
    }

    public void setValueList(List<InterOrderConfig> valueList) {
        this.valueList = valueList;
    }

    public List<WechatUser> getUserList() {
        return userList;
    }

    public void setUserList(List<WechatUser> userList) {
        this.userList = userList;
    }

    public String getAuditValue() {
        return auditValue;
    }

    public void setAuditValue(String auditValue) {
        this.auditValue = auditValue;
    }

    public String getDetailStatus() {
        return detailStatus;
    }

    public void setDetailStatus(String detailStatus) {
        this.detailStatus = detailStatus;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<WechatAudit> getCategoryAuditList() {
        return categoryAuditList;
    }

    public void setCategoryAuditList(List<WechatAudit> categoryAuditList) {
        this.categoryAuditList = categoryAuditList;
    }

    public String getConfigDeailId() {
        return configDeailId;
    }

    public void setConfigDeailId(String configDeailId) {
        this.configDeailId = configDeailId;
    }

    public List<Hr_Position> getPositionList() {
        return positionList;
    }

    public void setPositionList(List<Hr_Position> positionList) {
        this.positionList = positionList;
    }

    public String getAuditId() {
        return auditId;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
