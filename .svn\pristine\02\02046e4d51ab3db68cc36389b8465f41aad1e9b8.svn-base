package com.kyb.pcberp.modules.oa.pojo.audit;

import com.kyb.pcberp.common.persistence.DataEntity;

@SuppressWarnings("serial")
public class Oa_approve_record extends DataEntity<Oa_approve_record> {
    private String auditId; // 审批id

    private String recordType; // 记录类型 A 审批记录，R 查阅记录 ，U 表单修改记录

    private String status; // 审批状态 为空待审批，assent:同意，reject:驳回

    private String approveLevel; // 审批次数 1,2,3 3为最后一次审批 同意结束审批流程

    private String turn; // 审批轮次

    private String approveUsers; // 审批员工

    private String handelPositionId; // 审批岗位

    private String content; // 审批描述

    private String empId;

    public String getAuditId() {
        return auditId;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(String approveLevel) {
        this.approveLevel = approveLevel;
    }

    public String getTurn() {
        return turn;
    }

    public void setTurn(String turn) {
        this.turn = turn;
    }

    public String getApproveUsers() {
        return approveUsers;
    }

    public void setApproveUsers(String approveUsers) {
        this.approveUsers = approveUsers;
    }

    public String getHandelPositionId() {
        return handelPositionId;
    }

    public void setHandelPositionId(String handelPositionId) {
        this.handelPositionId = handelPositionId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }
}
