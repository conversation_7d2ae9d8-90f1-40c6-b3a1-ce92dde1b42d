const login = {
	template:'#login',
	data () {
		return {
			name: '',
			password: '',
			agreeFlag: ''
		}
	},
	methods:{
		login: function () {
			if(!this.name){
				alert("请填写用户名！");
				return;
			}
			if(!this.password){
				alert("请填写密码！");
				return;
			}
			if(!this.agreeFlag){
				alert("服务条款阅读后请勾选！");
				return;
			}
			var user = {};
			user.name = this.name;
			user.password = this.password;
			var router = this.$router;
			$('#loadingModal').modal();
			$.ajax({
				type:"post",
				url:ctx + "/f/wechat/kybsoft/login",
				data:JSON.stringify(user),
				contentType:"application/json",
				success:function(data)
				{
					$('#loadingModal').modal('hide');
					if(data == "success"){
						router.push("/");
					}else{
						alert("帐号或者密码错误");
					}
				}
			})
		}
	}
}