<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.report.dao.ReportComplaintDao">

	<sql id="reportComplaintVersionColumns">
		a.recordId AS "recordId",
		a.companyId AS "company.recordId",
		a.versionDate AS "versionDate",
		a.sortNum AS "sortNum",
		a.activeFlag AS "activeFlag",
		a.createdBy AS "createdBy.recordId",
		a.createdDate AS "createdDate",
		a.lastupdby AS "lastUpdBy.recordId",
		a.lastupddate AS "lastUpdDate",
		a.remark AS "remark",
		a.useFlag AS "useFlag"
	</sql>

	<sql id="reportComplaintRecordColumns">
		a.recordId AS "recordId",
		a.companyId AS "company.recordId",
		a.versionId AS "versionId",
		a.targetValue AS "targetValue",
		a.exceedTarget AS "exceedTarget",
		a.punish AS "punish",
		a.activeFlag AS "activeFlag",
		a.createdBy AS "createdBy.recordId",
		a.createdDate AS "createdDate",
		a.lastupdby AS "lastUpdBy.recordId",
		a.lastupddate AS "lastUpdDate",
		a.remark AS "remark"
	</sql>

	<sql id="materialJoins">
		JOIN md_company mc on mc.recordId = a.companyId
	</sql>

	<select id="versionList" resultType="ReportComplaintVersion">
		SELECT
			<include refid="reportComplaintVersionColumns" />
		FROM
			md_report_complaint_version a
		WHERE
			activeFlag = 1
			AND companyId = #{companyId}
		ORDER BY versionDate DESC
	</select>

	<insert id="createVersion">
		INSERT INTO `md_report_complaint_version` (
			`companyId`,
			`versionDate`,
			`sortNum`,
			`activeFlag`,
			`createdBy`,
			`createdDate`,
			`remark`,
			`useFlag`
		)VALUES(
			#{version.companyId},
			#{version.versionDate},
			#{version.sortNum},
			1,
			#{version.createdBy.recordId},
			#{version.createdDate},
			#{version.remark},
			0
		);
	</insert>

	<insert id="insertRecord">
		INSERT INTO `md_report_complaint_record` (
			`companyId`,
			`versionId`,
			`targetValue`,
			`exceedTarget`,
			`punish`,
			`activeFlag`,
			`createdBy`,
			`createdDate`,
			`remark`
		) VALUES (
			#{companyId},
			#{versionId},
			#{targetValue},
			#{exceedTarget},
			#{punish},
			1,
			#{createdBy.recordId},
			#{createdDate},
			#{remark}
		)
	</insert>

	<update id="updateRecord">
		UPDATE `md_report_complaint_record`
		SET `companyId` = #{companyId},
		`versionId` = #{versionId},
		`targetValue` = #{targetValue},
		`exceedTarget` = #{exceedTarget},
		`punish` = #{punish},
		`lastUpdBy` = #{lastUpdBy.recordId},
		`lastUpdDate` = #{lastUpDate},
		`remark` = #{remark}
		WHERE
		`recordId` = #{recordId}
	</update>

	<select id="findByVersionId" resultType="ReportComplaintRecord">
		SELECT
			 <include refid="reportComplaintRecordColumns"/>
		FROM
			md_report_complaint_record a
		WHERE
			a.companyId = #{companyId}
			AND a.activeFlag = 1
			AND a.versionId = #{versionId}
	</select>

	<select id="customerComplaintRate" resultType="ReportComplaintCustomerVo">
		SELECT
			a.NO,
			c.NO AS contractNo,
			m.NO AS materialNo,
			cc.customerModel AS customerModel,
			depart.`name` departName,
			depart.recordId AS departId,
			IFNULL(
				h.shortName,
				i.shortName
			) AS saleComName,
			su.userName AS salesmanName,
			a.quantity,
			a.fedNum,
			DATE_FORMAT( a.applyDate, '%Y%m' ) applyDate,
			IFNULL(cc.pnlLength,0)* IFNULL(cc.pnlWidth,0)* a.quantity / 1000000 / IFNULL(cc.pnlDivisor,1) AS area
		FROM
			st_reject_application a
			LEFT JOIN sl_contract_detail cd ON cd.recordId = a.contractDetailId
			LEFT JOIN md_supplier ms ON ms.recordId = cd.madeSupplierId
			LEFT JOIN sl_contract c ON c.recordId = cd.contractId
			LEFT JOIN sl_contract_craft cc ON cc.recordId = cd.craftId
			LEFT JOIN md_material m ON m.recordId = a.materialId
			LEFT JOIN md_customer cus ON cus.recordId = a.customerId
			LEFT JOIN icloud_group_center d ON d.recordId = cd.groupCenterId
			LEFT JOIN sl_contract_detail e ON e.recordId = d.contactDeailId
			LEFT JOIN sl_contract f ON f.recordId = e.contractId
			LEFT JOIN md_customer g ON g.recordId = f.customerId
			LEFT JOIN md_company h ON h.recordId = g.companyId
			LEFT JOIN md_company i ON i.recordId = a.companyId
			LEFT JOIN sm_user su ON su.recordId = cus.salesman
			LEFT JOIN md_department depart ON depart.recordId = IFNULL(c.deptId,f.deptId)
			LEFT JOIN md_material_specification spe ON spe.NAME = '长' AND spe.companyId = 17
			LEFT JOIN md_material_specification_relation s ON s.specificationId = spe.recordId AND s.materialId = m.recordId
			LEFT JOIN md_material_specification spe2 ON spe2.NAME = '宽' AND spe2.companyId = 17
			LEFT JOIN md_material_specification_relation sr ON sr.specificationId = spe2.recordId AND sr.materialId = m.recordId
			where
				a.activeFlag = 1 AND cd.companyId = #{companyId}
				AND DATE_FORMAT( a.applyDate, '%Y%m' ) = #{yearMonth}
				<if test="departName != null and departName != '' and departName != '所有'">
					AND depart.`name` like CONCAT('%', #{departName}, '%')
				</if>
			GROUP BY a.recordId
	</select>

	<select id="deliveryList" resultType="ReportComplaintDeliveryVo">
		SELECT
			sc.`no` AS contractNo,
			scc.`no` AS craftNo,
			scc.customerModel AS customerModel,
			depart.`name` AS departName,
			f.deptId AS departId,
			IFNULL(
				h.shortName,
				i.shortName
			) AS saleComName,
			su.userName AS salesmanName,
			e.quantity AS orderQuantity,
			sdd.quantity AS sendQuantity,
			a.sentTime,
			ec.setLength AS  setLength,
			ec.setWidth AS setWidth,
			ec.pnlDivisor AS phlDivisor,
			ec.setWidth*ec.setLength/ec.pnlDivisor*sdd.quantity/1000000 AS sendArea,
			DATE_FORMAT( a.sentTime, '%Y%m' ) periodTime
		FROM
			sl_delivery_detail sdd
			LEFT JOIN sl_delivery a ON a.recordId = sdd.deliveryId
			AND a.companyId = sdd.companyId
			AND a.activeFlag = 1
			LEFT JOIN sl_contract_detail scd ON scd.recordId = sdd.contractDetailId
			AND scd.companyId = sdd.companyId
			AND scd.activeFlag = 1
			LEFT JOIN sl_contract sc ON sc.recordId = scd.contractId
			AND sc.companyId = sdd.companyId
			AND sc.activeFlag = 1
			LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId
			AND scc.companyId = sdd.companyId
			AND scc.activeFlag = 1
			LEFT JOIN icloud_group_center d ON d.recordId = scd.groupCenterId
			LEFT JOIN sl_contract_detail e ON e.recordId = d.contactDeailId
			LEFT JOIN sl_contract f ON f.recordId = e.contractId
			LEFT JOIN md_customer g ON g.recordId = f.customerId
			LEFT JOIN md_company h ON h.recordId = g.companyId
			LEFT JOIN md_company i ON i.recordId = a.companyId
			LEFT JOIN md_department depart ON depart.recordId = f.deptId
			LEFT JOIN sm_user su ON su.recordId = f.userId
			LEFT JOIN sl_notification sn On sn.contractDetailId = sdd.contractDetailId
			LEFT JOIN eg_carda ec ON ec.recordId = sn.processCardAId
		where
			a.activeFlag = 1 AND sdd.companyId = #{companyId} AND scd.groupCenterId is not null
			AND DATE_FORMAT( a.sentTime, '%Y%m' ) = #{yearMonth}
			<if test="departName != null and departName != '' and departName != '所有'">
				AND depart.`name` like CONCAT('%', #{departName}, '%')
			</if>
	</select>

	<select id="getComplaintSetByMonth" resultType="ReportComplaintRecord">
		SELECT
			<include refid="reportComplaintRecordColumns"/>
		FROM
			md_report_complaint_record a
		WHERE
			a.activeFlag = 1
			AND a.versionId = IFNULL((
			SELECT
			version.recordId
			FROM
			md_report_complaint_version version
			WHERE
			DATE_FORMAT( version.versionDate, '%Y%m' ) &lt; #{yearMonth}
			AND version.activeFlag = 1
			ORDER BY
			version.versionDate DESC
			LIMIT 1)
			,(select max(recordId) from md_report_complaint_version))
	</select>


	<select id="customerComplaintRateGroupByDepart" resultType="ReportComplaintCustomerVo">
		SELECT departName,count(*) quantity,ROUND(SUM(a.area),4) AS "area",deptId FROM
		(SELECT
			f.deptId,
			IFNULL( z.`name`, '未知' ) departName,
			IFNULL(cc.pnlLength,0)* IFNULL(cc.pnlWidth,0)* a.quantity / 1000000 / IFNULL(cc.pnlDivisor,1) AS area
		FROM
			st_reject_application a
			LEFT JOIN sl_contract_detail cd ON cd.recordId = a.contractDetailId
			LEFT JOIN md_supplier ms ON ms.recordId = cd.madeSupplierId
			LEFT JOIN sl_contract c ON c.recordId = cd.contractId
			LEFT JOIN sl_contract_craft cc ON cc.recordId = cd.craftId
			LEFT JOIN md_material m ON m.recordId = a.materialId
			LEFT JOIN md_customer cus ON cus.recordId = a.customerId
			LEFT JOIN icloud_group_center d ON d.recordId = cd.groupCenterId
			LEFT JOIN sl_contract_detail e ON e.recordId = d.contactDeailId
			LEFT JOIN sl_contract f ON f.recordId = e.contractId
			LEFT JOIN md_customer g ON g.recordId = f.customerId
			LEFT JOIN md_company h ON h.recordId = g.companyId
			LEFT JOIN md_company i ON i.recordId = a.companyId
			LEFT JOIN sm_user su ON su.recordId = cus.salesman
			LEFT JOIN md_material_specification spe ON spe.NAME = '长' AND spe.companyId = 17
			LEFT JOIN md_material_specification_relation s ON s.specificationId = spe.recordId AND s.materialId = m.recordId
			LEFT JOIN md_material_specification spe2 ON spe2.NAME = '宽' AND spe2.companyId = 17
			LEFT JOIN md_material_specification_relation sr ON sr.specificationId = spe2.recordId AND sr.materialId = m.recordId
-- 			LEFT JOIN md_department depart ON depart.recordId = IFNULL(f.deptId,c.deptId)
			LEFT JOIN icloud_group_org_relation k ON k.deptId = f.deptId AND k.activeFlag = 1
			LEFT JOIN icloud_group_org z ON z.recordId = k.groupOrgId
		WHERE
			a.activeFlag = 1 AND cd.companyId = #{companyId}
			AND DATE_FORMAT( a.applyDate, '%Y%m' ) = #{yearMonth}
		GROUP BY
			a.recordId
		) a
		GROUP BY
			a.`departName`
	</select>

	<select id="deliveryGroupByDepart" resultType="ReportComplaintDeliveryVo">
		SELECT departName,count(*) orderQuantity,deptId FROM (
			SELECT
				f.deptId,
				IFNuLL(z.`name`,'未知') AS departName
			FROM
				sl_delivery_detail sdd
				LEFT JOIN sl_delivery a ON a.recordId = sdd.deliveryId
				AND a.companyId = sdd.companyId
				AND a.activeFlag = 1
				LEFT JOIN sl_contract_detail scd ON scd.recordId = sdd.contractDetailId
				AND scd.companyId = sdd.companyId
				AND scd.activeFlag = 1
				LEFT JOIN sl_contract sc ON sc.recordId = scd.contractId
				AND sc.companyId = sdd.companyId
				AND sc.activeFlag = 1
				LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId
				AND scc.companyId = sdd.companyId
				AND scc.activeFlag = 1
				LEFT JOIN icloud_group_center d ON d.recordId = scd.groupCenterId
				LEFT JOIN sl_contract_detail e ON e.recordId = d.contactDeailId
				LEFT JOIN sl_contract f ON f.recordId = e.contractId
				LEFT JOIN md_customer g ON g.recordId = f.customerId
				LEFT JOIN md_company h ON h.recordId = g.companyId
				LEFT JOIN md_company i ON i.recordId = a.companyId
-- 				LEFT JOIN md_department depart ON depart.recordId = f.deptId
				LEFT JOIN icloud_group_org_relation k ON k.deptId = f.deptId AND k.activeFlag = 1
				LEFT JOIN icloud_group_org z ON z.recordId = k.groupOrgId
				LEFT JOIN sm_user su ON su.recordId = f.userId
			WHERE
				a.activeFlag = 1
				AND sdd.companyId = #{companyId}
				AND scd.groupCenterId IS NOT NULL
				AND DATE_FORMAT( a.sentTime, '%Y%m' ) = #{yearMonth}
				AND f.deptId IS NOT NULL
		) a
		GROUP BY
			a.`departName`
	</select>

	<update id="updateVesionUseFlag">
		UPDATE md_report_complaint_version SET
		useFlag = #{useFlag}
		WHERE recordId = #{recordId}
	</update>

	<insert id="insertComplaintAssessment">
		INSERT INTO `sl_report_complaint_assessment` (
			`companyId`,
			`departId`,
			`versionId`,
			`yearMonth`,
			`complaintRateSize`,
			`deliverySize`,
			`complaintRate`,
			`penaltyPrice`,
			`area`,
			`penaltyAmount`,
			`activeFlag`,
			`createdBy`,
			`createdDate`,
			`remark`
		)VALUES(
			#{company.recordId},
			#{departId},
			#{versionId},
			#{periodTime},
			#{complaintRateSize},
			#{deliverySize},
			#{complaintRate},
			#{penaltyPrice},
			#{area},
			#{penaltyAmount},
			1,
			#{createdBy.recordId},
			#{createdDate},
			#{remark}
		)
	</insert>

	<insert id="insertComplaintCustomerAssessment">
		INSERT INTO `sl_report_complaint_customer_assessment_detail` (
			`companyId`,
			`no`,
			`contractNo`,
			`materialNo`,
			`customerModel`,
			`departId`,
			`saleComName`,
			`salesmanName`,
			`quantity`,
			`fedNum`,
			`area`,
			`applyDate`,
			`activeFlag`,
			`createdBy`,
			`createdDate`,
			`remark`
		)VALUES
		<foreach collection="list" item="item" separator=",">
		(
			#{item.company.recordId},
			#{item.no},
			#{item.contractNo},
			#{item.materialNo},
			#{item.customerModel},
			#{item.deptId},
			#{item.saleComName},
			#{item.salesmanName},
			#{item.quantity},
			#{item.fedNum},
			#{item.area},
			#{item.applyDate},
			1,
			#{item.createdBy.recordId},
			#{item.createdDate},
			#{item.remark}
		)
		</foreach>
	</insert>

	<insert id="insertDeliveryAssessmentDetail">
		INSERT INTO `sl_report_complaint_delivery_assessment_detail` (
			`companyId`,
			`activeFlag`,
			`createdBy`,
			`createdDate`,
			`remark`,
			`contractNo`,
			`craftNo`,
			`customerModel`,
			`departId`,
			`saleComName`,
			`salesmanName`,
			`orderQuantity`,
			`sendQuantity`,
			`sentTime`,
			`periodTime`
		)VALUES
		<foreach collection="list" item="item" separator=",">
		(
			#{item.company.recordId},
			1,
			#{item.createdBy.recordId},
			#{item.createdDate},
			#{item.remark},
			#{item.contractNo},
			#{item.craftNo},
			#{item.customerModel},
			#{item.departId},
			#{item.saleComName},
			#{item.salesmanName},
			#{item.orderQuantity},
			#{item.sendQuantity},
			#{item.sentTime},
			#{item.periodTime}
		)
		</foreach>
	</insert>
</mapper>