﻿<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.sys.dao.RoleDao">
	
	<resultMap id="roleResult" type="Role">
		<id property="recordId" column="recordId" />
		<result property="company.recordId" column="companyId" />
		<result property="name" column="name" />
		<collection property="menuList" ofType="Menu">
			<id property="recordId" column="menuList.recordId" />
		</collection>
	</resultMap>
	
	<sql id="roleColumns">
		a.recordId,
		a.companyId AS "company.recordId",
		a.name,
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.activeFlag,
		a.remark,
		a.roleType
		
	</sql>
	
	<select id="get" resultMap="roleResult">
		SELECT 
			<include refid="roleColumns"/>,
			ri.itemId AS "menuList.recordId"
		FROM sm_role a
		JOIN sm_role_item ri ON ri.roleId = a.recordId
		WHERE a.recordId = #{recordId}
	</select>
	<select id="getRole" resultType="Role">
		SELECT 
			<include refid="roleColumns"/>
		FROM sm_role a
		WHERE a.recordId = #{recordId}
	</select>
		
	<!-- 添加之前的判断  看是否有重复 的 -->	
	<select id="isExistOfRole" resultType="Integer">
		select count(*) from sm_role a where a.name = #{name} AND a.activeFlag = #{DEL_FLAG_NORMAL} AND a.companyId = #{company.recordId}
	</select>
	
		
	<select id="getByName" resultType="Role">
		SELECT
			<include refid="roleColumns"/>
		FROM sm_role a
		WHERE a.name = #{name} AND a.activeFlag = #{DEL_FLAG_NORMAL} AND a.companyId = #{company.recordId}
	</select>
	
	<select id="findList" resultType="Role">
		SELECT <!-- DISTINCT -->
			<include refid="roleColumns"/>
		FROM sm_role a
		<if test="user !=null">
			LEFT JOIN sm_user_role ur ON ur.roleId = a.recordId
			LEFT JOIN sm_user u ON u.recordId = ur.userId
		</if>
		WHERE a.activeFlag = #{DEL_FLAG_NORMAL} AND a.companyId = #{company.recordId}
		<if test="userId != null and userId != ''">
			AND a.roleType = 1
		</if>
		<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
			AND a.createdBy = #{createdBy.recordId}
		</if>
		<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
			AND a.createdBy = #{createdBy.recordId}
		</if>
		<if test="user != null and user.recordId != null and user.recordId != ''">
			AND u.recordId = #{user.recordId}
		</if>
		<if test="user != null and user.userCode != null and user.userCode != ''">
			AND u.userCode = #{user.userCode}
		</if>
		<if test="name != null and name != ''">
			AND REPLACE(a.name," ","") LIKE CONCAT('%',REPLACE(#{name}," ",""), '%')
		</if>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.createdDate DESC 
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="Role">
		SELECT
			<include refid="roleColumns"/>
		FROM sm_role a
		WHERE a.activeFlag = #{DEL_FLAG_NORMAL} AND a.companyId = #{company.recordId} and a.roleType=1
		ORDER BY a.name
	</select>
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO sm_role(
			companyId,
			name,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			activeFlag,
			remark,
			roleType
		) VALUES (
			#{company.recordId},
			#{name},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{DEL_FLAG_NORMAL},
			#{remark},
			#{roleType}
		)
	</insert>
	
	<update id="update">
		UPDATE sm_role SET 	
			companyId = #{company.recordId},
			name = #{name},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			remark = #{remark}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE sm_role SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>
	
	<delete id="deleteRoleMenu">
		DELETE FROM sm_role_item WHERE roleId = #{recordId} AND companyId = #{company.recordId}
	</delete>
	
	<insert id="insertRoleMenu">
		INSERT INTO sm_role_item(companyId, roleId, itemId, permission, dataRange, createdBy, createdDate, lastUpdBy, lastUpdDate, activeFlag)
		<foreach collection="menuList" item="menu" separator=" union all ">
			SELECT #{company.recordId}, #{recordId}, #{menu.recordId}, #{menu.permission}, #{menu.dataRange}, #{createdBy.recordId}, #{createdDate}, 
			#{lastUpdBy.recordId}, #{lastUpdDate}, #{DEL_FLAG_NORMAL} FROM dual
		</foreach>
	</insert>
	
	<insert id="insertUserRole">
		INSERT INTO sm_user_role(companyId,userId, roleId,createdBy, createdDate, lastUpdBy, lastUpdDate, activeFlag)VALUES(
		#{company.recordId},
		#{userId},
		#{roleId},
		#{createdBy.recordId},
		#{createdDate}, 
		#{lastUpdBy.recordId},
		#{lastUpdDate},
		#{DEL_FLAG_NORMAL}
		
		)
	</insert>
	
	<!-- 根据多条件查询用户角色表中是否有数据 -->
	<select id="getRoleUserById" resultType="User">
		select * from sm_user_role a where  a.roleId=#{roleId}  and a.activeFlag = 1 AND a.companyId = #{company.recordId} and a.userId = #{userId}
	</select>
	
	<select id="findRoleUserList" resultType="User">
		select a.*, b.recordId as "userRoleId" from 
		sm_user a JOIN  sm_user_role b ON a.recordId=b.userId
		where   b.roleId=#{roleId}  and a.activeFlag = 1 AND a.companyId = #{company.recordId}
	</select>
	
	<delete id="deleteUserRoleById">
		DELETE FROM sm_user_role WHERE recordId = #{userRoleId}
	</delete>
	
	<select id="getItemByRole" resultType="Menu" parameterType="MessageSet">
	    select 
			si.nodeId as "nodeId"
		from sm_role a
		left join sm_role_item ri on ri.roleId = a.recordId
		left join sm_item si on ri.itemId = si.recordId
		where  a.activeFlag = #{DEL_FLAG_NORMAL}
		and a.recordId= #{roleId}
		order by si.nodeId
	</select>
	<select id="findRoleNameByUserRoleId" resultType="Role">
	SELECT
	a.recordId,
	a.`name`,
	a.createdBy AS "createdBy.recordId"
	FROM sm_role a
	LEFT JOIN sm_user_role b ON b.roleId = a.recordId where
	b.recordId = #{recordId}
	</select>
	
	<delete id="deleteRoleMenuByUserId">
		DELETE FROM sm_user_role WHERE userId = #{recordId}
	</delete>
	
	<select id="findNotAdminList" resultType="Role">
		SELECT
			a.recordId,
			a.companyId AS "company.recordId",
			a.name
		FROM sm_role a
		WHERE a.activeFlag = #{DEL_FLAG_NORMAL} 
		AND a.companyId = #{company.recordId} 
		AND a.roleType = 1
		AND a.name <![CDATA[ <> ]]> "系统管理员"
		ORDER BY a.name
	</select>
</mapper>