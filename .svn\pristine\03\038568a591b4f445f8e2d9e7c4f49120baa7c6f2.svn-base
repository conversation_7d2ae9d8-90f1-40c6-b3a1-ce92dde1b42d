package com.kyb.pcberp.common.utils;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MathUtils
{
    public static BigDecimal doubleToDecimal(double d)
    {
        return new BigDecimal(Double.toString(d));
    }
    
    public static double add(double d1, double d2, int len)
    {
        // 进行加法运算
        return round(add(d1, d2), len);
    }
    
    public static double add(double d1, double d2)
    {
        // 进行加法运算
        BigDecimal b1 = new BigDecimal(Double.toString(d1));
        BigDecimal b2 = new BigDecimal(Double.toString(d2));
        return b1.add(b2).doubleValue();
    }
    
    public static String addAsStr(double d1, double d2, int len)
    {
        return roundAsString(add(d1, d2), len);
    }
    
    public static double sub(double d1, double d2, int len)
    {
        // 进行减法运算
        return round(sub(d1, d2), len);
    }
    
    public static double sub(double d1, double d2)
    { // 进行减法运算
        BigDecimal b1 = new BigDecimal(Double.toString(d1));
        BigDecimal b2 = new BigDecimal(Double.toString(d2));
        return b1.subtract(b2).doubleValue();
    }
    
    public static String subAsStr(double d1, double d2, int len)
    {
        return roundAsString(sub(d1, d2), len);
    }
    
    public static double mul(double d1, double d2, int len)
    {
        // 进行乘法运算
        return round(mul(d1, d2), len);
    }
    
    public static double mul(double d1, double d2)
    { // 进行乘法运算
        BigDecimal b1 = new BigDecimal(Double.toString(d1));
        BigDecimal b2 = new BigDecimal(Double.toString(d2));
        return b1.multiply(b2).doubleValue();
    }
    
    public static String mulAsStr(double d1, double d2, int len)
    {
        return roundAsString(mul(d1, d2), len);
    }
    
    public static double div(double d1, double d2, int len)
    {// 进行除法运算
        BigDecimal b1 = new BigDecimal(Double.toString(d1));
        BigDecimal b2 = new BigDecimal(Double.toString(d2));
        return b1.divide(b2, len, BigDecimal.ROUND_HALF_UP).doubleValue();
    }
    
    public static String divAsStr(double d1, double d2, int len)
    {
        return Double.toString(div(d1, d2, len));
    }
    
    public static double round(double d, int len)
    {
        // 进行四舍五入操作
        BigDecimal b1 = new BigDecimal(Double.toString(d));
        BigDecimal b2 = new BigDecimal(1);
        // 任何一个数字除以1都是原数字
        // ROUND_HALF_UP是BigDecimal的一个常量，表示进行四舍五入的操作
        return b1.divide(b2, len, BigDecimal.ROUND_HALF_UP).doubleValue();
    }
    
    public static String roundAsString(double d, int len)
    {
        return Double.toString(round(d, len));
    }
    
    // WC 2017-06-16获取字符串中的数字(含小数)
    public static String getMathFromStr(String str)
    {
        String result = "";
        if (StringUtils.isNotBlank(str))
        {
            char[] c = str.toCharArray();
            for (int i = 0; i < c.length; i++)
            {
                if (("0123456789.").indexOf(c[i] + "") != -1)
                {
                    result += c[i];
                }
            }
        }
        return result;
    }
    
    public static void main(String[] args)
    {
        System.out.println("加法运算：" + MathUtils.round(MathUtils.add(10.345, 3.333), 1));
        System.out.println("乘法运算：" + MathUtils.round(MathUtils.mul(10.345, 3.333), 3));
        System.out.println("除法运算：" + MathUtils.div(10.345, 3.333, 3));
        System.out.println("减法运算：" + MathUtils.round(MathUtils.sub(10.345, 3.333), 3));
    }
    
    /**
     * 判断字符串是否为数字或者小数
     * 
     * @param str
     * @return
     */
    public static boolean isNumeric(String str)
    {
        Pattern pattern = Pattern.compile("(^([0-9]{1,}[.][0-9]*)$)|(^([0-9]{1,})$)");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches())
        {
            return false;
        }
        return true;
    }
    
    public static BigDecimal getBigDecimal(BigDecimal bg)
    {
        if (null != bg)
        {
            return bg.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        else
        {
            return BigDecimal.ZERO;
        }
    }
}
