const addExpense = {
	template:'#addExpense',
	computed: {
		comList: {
			get () {
				return this.$store.state.auditStore.comList
		    }
		},
		emp: {
			get () {
				return this.$store.state.myStore.emp
		    }
		},
		auditGroupDetailList:{
			get () {
				return this.$store.state.auditStore.auditGroupDetailList
		    }
		},
		expenseTypeList:{
			get () {
				return this.$store.state.auditStore.expenseTypeList
		    }
		},
		organizationAuditList:{
			get () {
				return this.$store.state.auditStore.organizationAuditList
			}
		},
		categoryAuditList:{
			get () {
				return this.$store.state.auditStore.categoryAuditList
			}
		},

	},
	watch: {
	    expenseTypeList:function(){
	        if(!this.expenseTypeList || this.expenseTypeList.length === 0){
	          return
	        }
	        if(!this.expenseTypeId || this.expenseTypeId === ''){
	          if(this.audit.expenseTypeId){
	            this.expenseTypeId = this.audit.expenseTypeId
	          }else{
	            this.expenseTypeId = this.expenseTypeList[0].recordId
	          }
	        }
	        var id = this.expenseTypeId;
	        if ($('#expenseType').is(':visible')) {
	          this.initSelectTwo('expenseType',this.expenseTypeId)
	        } else {
	          if (this.temp > 50) {
	            this.temp = 0
	          }
	          this.temp++
	          // 递归 等待dom渲染完毕
	          var _this = this
	          setTimeout(function () { _this.initSelectTwo('expenseType',id) }, 500)
	        }
	      },

		categoryAuditList:function ()
		{
			this.handCategoryAuditList();
		},
	},
	created(){
		this.getWxMsg()
		this.$store.dispatch("auditStore/getOrganizationAuditList");
		this.$store.dispatch("auditStore/getCategoryAuditList");
	},
	data(){
		return{
			audit: {},
			company: {},
			expenseList: [],
			localIds: [],
			serverList: [],
			localIdsCopy: [],
			expenseTypeId:'1',
			clickFlag:false,
			chooseList:[],
			departmentName:"",
			categoryId:"",
			selectFlag: false,
			expense:"",
			shareStatus:"",
		}
	},
	methods:{
		initSelectTwo: function (id,typeId) {
	      $('#'+id).empty()
	      let option = ''
	      option += "<option value=''>" + '请选择' + '</option>'
	      this.expenseTypeList.forEach(el => {
	        option += "<option value='" + el.recordId + "'>"+el.no+" " + el.name + '</option>'
	      })
	      $('#'+id).append(option)
	      $('#'+id).selectpicker('val', typeId)
	      $('#'+id).selectpicker('render')
	      $('#'+id).selectpicker('refresh')
	      $('#'+id).selectpicker()
	      if(!typeId){
	        typeId = this.expenseTypeList[0].recordId
	      }
	      $('#'+id).selectpicker('val', typeId);
	    },
		getWxMsg:function()
		{
			var config = {}
			config.url = location.href.split('#')[0]
			var _this = this
			$.ajax({
	     		type:"post",
	     		url:ctx + "/f/wechat/produce/getWxMsg",
	     		data:JSON.stringify(config),
	     		contentType:"application/json",
	     		success:function(data)
	     		{
	     			_this.wxConfig(data)
	     		}
	     	})
		},
		wxConfig: function (data) {
			wx.config({
		        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端console.log出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
		        appId: data.appId, // 必填，公众号的唯一标识
		        timestamp: data.timestamp, // 必填，生成签名的时间戳
		        nonceStr: data.nonceStr, // 必填，生成签名的随机串
		        signature: data.signature, // 必填，签名
		        jsApiList: [
		          'checkJsApi', 'chooseImage', 'previewImage', 'uploadImage', 'downloadImage'
		        ]
			})
	    },
		chooseImg:function(){
			var _this = this
			wx.chooseImage({
				count: 9, // 默认9
				sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
				success: function (res) {
					if(_this.localIds && _this.localIds.length > 0){
						for(var i=0;i< res.localIds.length;i++){
							_this.localIds.push(res.localIds[i])
						}
					}else{
						_this.localIds = res.localIds // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
					}
					// 上传图片
					_this.serverList = []
					_this.localIdsCopy = []
					for (let i = 0; i < _this.localIds.length; i++) {
						_this.localIdsCopy.push(_this.localIds[i])
					}
					_this.uploadImage()
				}
			});
		},
		preview:function(item){
			var _this = this
			wx.previewImage({
				current: item, // 当前显示图片的http链接
				urls: _this.localIds // 需要预览的图片http链接列表
			});
		},
		uploadImage:function(){
			var vm = this
			if(vm.localIdsCopy && vm.localIdsCopy.length > 0){
				wx.uploadImage({
					localId: vm.localIdsCopy.pop(), // 需要上传的图片的本地ID，由chooseImage接口获得
					isShowProgressTips: 1, // 默认为1，显示进度提示
					success: function (res) {
						vm.serverList.push(res.serverId)
						vm.uploadImage()
					}
				});
			}
		},
		deleteImg:function(index){
			this.localIds.splice(index,1)
		},
		add:function(){
			let index = this.expenseList.length + 1;
			this.expense = {expenseDesc:'',expenseSum:'',expenseNum:'',categoryId:'',itemId: "categoryIdT" + index,amoebaId:"",amoebaItemId:"amoebaIdT" + index}
			if (!this.expenseList || this.expenseList.length <= 0)
			{
				this.expense.id = 1;
			}else{
				this.expense.id = this.expenseList.length + 1;
			}
			this.expenseList.push(this.expense);
			this.loadSelect();
		},
		loadSelect:function(){
			if (!this.expenseList || this.expenseList.length == 0)
			{
				return;
			}
			let item = this.expense;
			if ($('#'+item.itemId) && $('#'+item.itemId)[0]) {
				this.initSelectTT(item.itemId);
			}else {
				if (this.temp > 50) {
					this.temp = 0;
				}
				this.temp++;
				// 递归 等待dom渲染完毕
				var _this = this;
				setTimeout(function () { _this.loadSelect(); }, 500);
			}

			if ($('#'+item.itemId) && $('#'+item.itemId)[0]) {
				this.initSelectTT(item.amoebaItemId);
			}else {
				if (this.temp > 50) {
					this.temp = 0;
				}
				this.temp++;
				// 递归 等待dom渲染完毕
				var _this = this;
				setTimeout(function () { _this.loadSelect(); }, 500);
			}
		},
		initSelectTT:function(id){
			if(!id)
			{
				return;
			}
			$('#'+id).empty();
			let option = '';
			option += "<option value=''>" + '请选择' + '</option>'
			this.categoryAuditList.forEach(el => {
				option += "<option value='" + el.recordId + "'>" + el.categoryName + "</option>";
			});
			$('#'+id).append(option);
			$('#'+id).selectpicker('render');
			$('#'+id).selectpicker('refresh');
			$('#'+id).selectpicker();
		},
		initSelectT:function(id,value){
			if(!id)
			{
				return;
			}
			$('#'+id).empty();
			let option = '';value
			option += "<option value=''>" + '请选择' + '</option>'
			this.categoryAuditList.forEach(el => {
				option += "<option value='" + el.recordId + "'>" + el.categoryName + "</option>";
			});
			$('#'+id).append(option);
			if (value)
			{
				$('#'+id).selectpicker("val", value);
			}
			$('#'+id).selectpicker('render');
			$('#'+id).selectpicker('refresh');
			$('#'+id).selectpicker();
		},
		reduce:function(index){
			if(this.expenseList && this.expenseList.length > 1){
				this.expenseList.splice(index, 1); 
			}else{
				alert("必须要有一个报销事项");
			}
		},
		submit:function(type){
			if(this.clickFlag)
			{
				alert("请勿多次点击！");
				return;
			}
			if(this.expenseList && this.expenseList.length > 0){
				for(var i=0;i<this.expenseList.length;i++){
					if(this.expenseList[i].expenseDesc && this.expenseList[i].expenseSum && this.expenseList[i].expenseNum
					&& this.expenseList[i].categoryId && this.expenseList[i].amoebaId){
						continue;
					}else{
						alert("报销明细的内容需要填写完整！");
						return;
					}
				}
			}else{
				alert("请填报销明细！");
				return;
			}
			this.audit.expenseList = this.expenseList;
			this.audit.chooseList = this.chooseList;
			if(!this.audit.auditResult){
				alert("请填写报销原因！");
				return;
			}
			if(!this.company || !this.company.oaDepartId){
				alert("请选择来源！");
				return;
			}
			this.audit.applicationsType = type;
			this.audit.expenseTypeId = this.expenseTypeId
			if(type == '2')
			{
	            if(!this.audit.reimbursementFor){
	              this.$set(this.audit, 'reimbursementFor', '提交')
	            }
	            $("#reimbursementWindow").modal()
	            return;
			}
			if(this.auditGroupDetailList && this.auditGroupDetailList.length > 0){
		        var total = 0
		        var list = [] // 保存的分摊记录(金额大于0的)
	            for (let obj of this.auditGroupDetailList) {
            	  if(obj.amount && Number(obj.amount) > 0){
                    list.push(obj)
                    total += Number(obj.amount)
                  }
	            }
		        total = Number(total.toFixed(4))
	            if (total === 0) {
	              // 不分摊
	            } else if (total != Number(this.audit.money)) {
	              alert('所有分摊的金额要等于总金额')
	              return
	            } else {
	              this.audit.auditGroupDetailList = list
	            }
			}
			this.audit.oaDepartId = this.company.oaDepartId;
			this.audit.empId = this.emp.recordId;
			this.audit.groupId = this.emp.company.recordId;
			this.audit.auditType = "22003";
			this.audit.serverList = this.serverList
			this.clickFlag = true;
			var router = this.$router;
			$('#loadingModal').modal();
			var _this = this;
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftOA/commitAudit",
		     	data:JSON.stringify(this.audit),
		     	contentType:"application/json",
		     	success:function(data)
		     	{ 
		     		if(data == "success"){
		     			alert("提交成功");
		     			$('#loadingModal').modal('hide')
		     			router.push('/submit');
		     		}else{
		     			alert("提交失败");
		     			$('#loadingModal').modal('hide')
		     		}
					_this.clickFlag = false;
		     	}
		    })
		},
		cancle:function(){
			this.$router.push('/submit');
		},
		countMoney: function(item){
			if(isNaN(item.expenseSum) || item.expenseSum <= 0){
				alert("报销金额必须为大于0的数字");
				this.$set(item,"expenseSum",null)
				const map = {}
				map.amount = money
				this.$store.dispatch('auditStore/setAuditData',map)
				return;
			}
			var money = 0;
			for(var i=0;i<this.expenseList.length;i++){
				if(this.expenseList[i].expenseSum){
					money = money + Number(this.expenseList[i].expenseSum)
					this.$set(this.audit,"money",money)
				}
			}
			const map = {}
			map.amount = money
			this.$store.dispatch('auditStore/setAuditData',map)
		},
		reimbursementSubmit:function()
		{
			if(this.clickFlag)
			{
				alert("请勿多次点击！");
				return;
			}
	       if(!this.audit.reimbursementFor || this.audit.reimbursementFor === ''){
	          alert('请填写评论内容！')
	          return
	        }
			this.audit.oaDepartId = this.company.oaDepartId;
			this.audit.empId = this.emp.recordId;
			this.audit.groupId = this.emp.company.recordId;
			this.audit.auditType = "22003";
			this.audit.serverList = this.serverList
			this.clickFlag = true;
			var router = this.$router;
			$('#loadingModal').modal()
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftOA/commitAudit",
		     	data:JSON.stringify(this.audit),
		     	contentType:"application/json",
		     	success:function(data)
		     	{ 
		     		$('#reimbursementWindow').modal('hide')
		     		if(data == "success"){
		     			alert("提交成功");
		     			$('#loadingModal').modal('hide')
		     			router.push('/submit');
		     		}else{
		     			alert("提交失败");
		     			$('#loadingModal').modal('hide')
		     		}
		     	}
		    })
		},
		handCategoryAuditList: function() {
			$('#categoryIdT').empty();
			let option = "";
			option += "<option value=''>" + "请选择" + "</option>";
			this.categoryAuditList.forEach(el => {
				option += "<option value='" + el.recordId + "'>" + el.categoryName + "</option>";
			});
			$("#categoryIdT").append(option);
			$("#categoryIdT").selectpicker("val", this.categoryId);
			$("#categoryIdT").selectpicker("render");
			$("#categoryIdT").selectpicker("refresh");
			$("#categoryIdT").selectpicker();
		},
		//检验数据
		verifyDate()
		{
			this.chooseList = [];
			if (this.organizationAuditList && this.organizationAuditList.length > 0)
			{
				for(let organization of this.organizationAuditList)
				{
					if (organization.allId === this.company.allId)
					{
						this.$set(organization, "selectFlag", true);
						this.$set(organization,'money',"");
						this.chooseList.push(organization);
					}else{
						this.$set(organization, "selectFlag", false);
					}
				}
			}
		},
		openCatgory()
		{
			$("#openCatgortPage").modal();
		},
		setAllSelectFlag() {
			for (let i=0;i<this.organizationAuditList.length;i++){
				if (this.selectFlag){
					this.$set(this.organizationAuditList[i], "selectFlag", false);
					if (this.organizationAuditList && this.organizationAuditList.length > 0)
					{
						let indexRemove = this.chooseList.findIndex(item => item.allId === this.organizationAuditList[i].allId);
						if (indexRemove !== -1) {
							this.chooseList.splice(indexRemove, 1);
						}
					}
				}else {
					this.$set(this.organizationAuditList[i], "selectFlag", true);
					for (let i = 0; i < this.organizationAuditList.length; i++) {
						const audit = this.organizationAuditList[i];
						const exists = this.chooseList.some(item => item.recordId === audit.allId);
						if (!exists) {
							this.chooseList.push(audit);
						}
					}
				}
			}
		},
		selectValue(item)
		{
			this.chooseList  = [];
			let amountTotal = this.audit.money;
			if (this.organizationAuditList && this.organizationAuditList.length > 0)
			{
				for(let orange of this.organizationAuditList)
				{
					if (item.allId === orange.allId)
					{
						orange.selectFlag = !item.selectFlag;
					}
					if (orange.selectFlag)
					{
						if (!amountTotal || amountTotal <= 0)
						{
							this.$set(orange,'money',"");
						}else{
							this.$set(orange,'money',(orange.scale * amountTotal) / 100);
						}
						this.chooseList.push(orange);
					}else{
						let indexRemove = this.chooseList.findIndex(item => item.allId === orange.allId);
						if (indexRemove !== -1) {
							this.chooseList.splice(indexRemove, 1);
						}
					}
				}
			}
		},
		delChooseData(index,recordId)
		{
			this.chooseList.splice(index, 1);
			if (this.organizationAuditList && this.organizationAuditList.length > 0)
			{
				for(let audit of this.organizationAuditList)
				{
					if (audit.allId === recordId)
					{
						audit.selectFlag = false;
					}
				}
			}
		},
		loadAmoebaIdValue(categoryId,id)
		{
			if (categoryId) {
				for(let category of this.categoryAuditList)
				{
					if (category.recordId === categoryId) {

						if (this.expenseList && this.expenseList.length > 0)
						{
							for(let expenseObj of this.expenseList)
							{
								if(id === expenseObj.id && !expenseObj.amoebaId)
								{
									this.$set(expenseObj,"amoebaId",categoryId);
									this.initSelectT(expenseObj.amoebaItemId,categoryId);
									break;
								}
							}
						}
					}
				}
			}
		}
	}
}