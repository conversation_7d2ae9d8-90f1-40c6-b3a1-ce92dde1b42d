/**
 *
 */
package com.kyb.pcberp.modules.crm.service;

import com.drew.lang.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.enums.CommonEnums.DictItemEnum;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.pdf.ext.ParagraphCn12;
import com.kyb.pcberp.common.pdf.ext.ParagraphCn9;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.BaseService;
import com.kyb.pcberp.common.sms.util.DateUtil;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.modules.approval.dao.ApprovalDao;
import com.kyb.pcberp.modules.approval.dao.BackupTwoDao;
import com.kyb.pcberp.modules.approval.entity.Approval;
import com.kyb.pcberp.modules.approval.entity.BackupTwo;
import com.kyb.pcberp.modules.approval.utils.ModifyUtils;
import com.kyb.pcberp.modules.approval.utils.QueryApprovalUtils;
import com.kyb.pcberp.modules.approval.vo.QueryVo;
import com.kyb.pcberp.modules.contract.dao.ContractCraftDao;
import com.kyb.pcberp.modules.contract.entity.ContractCraft;
import com.kyb.pcberp.modules.contract.entity.Delivery;
import com.kyb.pcberp.modules.crm.dao.*;
import com.kyb.pcberp.modules.crm.entity.*;
import com.kyb.pcberp.modules.production.dao.ProductionSetDao;
import com.kyb.pcberp.modules.production.entity.ProductionCapacityAllocation;
import com.kyb.pcberp.modules.purch.entity.AccountsPayable;
import com.kyb.pcberp.modules.stock.dao.OutBoundDao;
import com.kyb.pcberp.modules.stock.entity.OutBound;
import com.kyb.pcberp.modules.stock.entity.OutBoundDeail;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import com.kyb.pcberp.modules.sys.dao.EmployeeDao;
import com.kyb.pcberp.modules.sys.dao.ParameterSetDao;
import com.kyb.pcberp.modules.sys.entity.*;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import com.kyb.pcberp.modules.wechat.entity.IcloudUser;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.swing.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户Service
 *
 * <AUTHOR>
 * @version 2015-08-27
 */
@Service
@Transactional(readOnly = true)
public class CustomerService extends BaseService implements InitializingBean
{
    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private CustomerContactDao contactDao;

    @Autowired
    private CustomerBranchDao customerBranchDao;

    @Autowired
    private ContractCraftDao contractCraftDao;

    @Autowired
    private BackupTwoDao backupTwoDao;

    @Autowired
    private CustomerAttachementsDao customerAttachementsDao;

    @Autowired
    private ProductionSetDao productionSetDao;

    @Autowired
    private ParameterSetDao parameterSetDao;

    @Autowired
    private CompanyDao companyDao;

    @Autowired
    private OutBoundDao outBoundDao;

    @Autowired
    private EmployeeDao employeeDao;

    @Autowired
    private ApprovalDao approvalDao;

    @Autowired
    private CustomerSalesmanDao customerSalesmanDao;

    @Autowired
    private CustomerProductTypeDao customerProductTypeDao;

    public Customer getCustomer(String id)
    {
        return customerDao.get(id);
    }

    public List<Customer> findAllCustomerList(Customer customer)
    {
        return customerDao.findAllList(customer);
    }

    public List<Customer> findAuditCustomerList(Customer customer)
    {
        return customerDao.findAuditList(customer);
    }

    public Customer findCustomerById(Customer customer)
    {
        return customerDao.findCustomerById(customer);
    }

    public List<Customer> findAuditCustomerListAll(Customer customer)
    {
        return customerDao.findAuditListAll(customer);
    }

    public List<Customer> findCustomerList(Customer customer)
    {
        List<Customer> list = customerDao.findList(customer);
        return list;
    }

    public Page<Customer> findCustomerPage(Page<Customer> page, Customer customer)
    {
        // 获取当前登录人
        customer.setPage(page);
        List<Customer> list = customerDao.findList(customer);
        // 处理占用平方米
        for (int i = 0; i < list.size(); i++)
        {
            if (list.get(i).getSafekeeping() != null && list.get(i).getSafekeeping().equals("1"))
            {
                Customer customer1 = list.get(i).clone();
                Customer mer = new Customer();
                mer.setRecordId(customer1.getRecordId());
                mer.setCompany(customer1.getCompany());
                mer.setNo(customer1.getNo());
                mer.setName(customer1.getName());
                mer.setShortName(customer1.getShortName());
                mer.setSafekeeping(customer1.getSafekeeping());
                list.set(i,mer);
            }
            else
            {
                if (list.get(i).getCustomerGrade() != null)
                {
                    Integer num = customerDao.countCustomerGrade(list.get(i).getCustomerGrade());
                    list.get(i).setCustomerGradeNum(num);
                }
                if (list.get(i).getCapacityPrecent() != null)
                {
                    list.get(i).setCustomerGradeRemark(list.get(i).getCapacityPrecent());
                }
                else
                {
                    if (list.get(i).getCustomerGradeRemark() != null && list.get(i).getCustomerGradeNum() > 0)
                    {
                        Double capacityPrecent = list.get(i).getCustomerGradeRemark() / list.get(i).getCustomerGradeNum();
                        NumberFormat format = NumberFormat.getInstance();
                        format.setMaximumFractionDigits(4);
                        String capacityPrecentStr = format.format(capacityPrecent);
                        list.get(i).setCapacityPrecent(Double.parseDouble(capacityPrecentStr));
                        list.get(i).setCustomerGradeRemark(list.get(i).getCapacityPrecent());
                    }
                }
            }
        }
        // 查询出所有符合业务员助理信息(lh)
        //        CustomerSalesAssistant customerSalesAssistant = new CustomerSalesAssistant();
        //        customerSalesAssistant.setUserId(user.getRecordId());
        //        customerSalesAssistant.setActiveFlag(CustomerSalesAssistant.DEL_FLAG_NORMAL);
        //        customerSalesAssistant.setCompany(user.getCompany());
        //        List<CustomerSalesAssistant> assistantList =
        //            customerSalesAssistantDao.findalesAssistant(customerSalesAssistant);

        //        if (Collections3.isNotEmpty(list))
        //        {
        //            list.forEach(item -> {
        //
        //                // 只能查看业务员是自己的客户，没有设置查询范围 可以查看所有的客户
        //                if ((customer.getQueryAll() != null && customer.getQueryAll()) || item.getSalesman() == null
        //                    || item.getSalesman().getRecordId().equals(user.getRecordId()))
        //                {
        //                    item.setSelf(true);
        //                }
        //
        //
        //                // 当业务员助理是这个客户是进行标记 (lh)
        //                if (Collections3.isNotEmpty(assistantList) && assistantList.size() > 0)
        //                {
        //                    for (CustomerSalesAssistant assistant : assistantList)
        //                    {
        //                        String customerId = assistant.getCustomerId().toString();
        //                        if ((customer.getQueryAll() != null && customer.getQueryAll())
        //                            || (null != customerId && item.getRecordId().equals(customerId)))
        //                        {
        //                            item.setAssistantSelf(true);
        //                            break;
        //                        }
        //                    }
        //                }
        //
        //            });
        //        }

        page.setList(list);
        return page;
    }

    public String getGiveCapa(User user)
    {
        Company company = UserUtils.getUser().getCompany();
        ProductionCapacityAllocation productionCapacityAllocation = productionSetDao.getProCapacity(company);
        if (productionCapacityAllocation != null && productionCapacityAllocation.getCapacityArea() != null)
        {
            BigDecimal allCapa = new BigDecimal(0);
            ParameterSet param = new ParameterSet();
            Company com = new Company(productionCapacityAllocation.getCompanyId());
            param.setCompany(com);
            param.setJianPin("allCapacity");
            List<ParameterSet> params = parameterSetDao.findList(param);
            if (params.size() > 0 && StringUtils.isNotBlank(params.get(0).getParameterValue()))
            {
                allCapa = new BigDecimal(params.get(0).getParameterValue());
                if (null == productionCapacityAllocation.getCapacityPercent())
                {
                    productionCapacityAllocation.setCapacityPercent(new Double(0));
                }
                BigDecimal precent = new BigDecimal(productionCapacityAllocation.getCapacityPercent());
                if (allCapa.compareTo(new BigDecimal(0)) == 1)
                {
                    // 计算给当前公司的产能
                    BigDecimal result =
                        precent.multiply(allCapa).divide(new BigDecimal(100)).setScale(4, BigDecimal.ROUND_HALF_UP);
                    return result.toString();
                }
            }
        }
        return null;
    }

    public String getWaitCapa(User user)
    {
        // 查询所有调整过的产能和对应等级的产能列表
        List<Customer> list = customerDao.findCapaticyList(user.getCompany());
        // 循环列表得到产能差，正数为减少了，负数为增加了，将列表正负数汇总，如果为正数说明这是可被其它客户调整产能，负数说明有问题，提醒用户进行设置
        Double adjustPrecent = 0.00;
        for (int i = 0; i < list.size(); i++)
        {
            if (list.get(i).getCapacityPrecent() != null && list.get(i).getCustomerGradeRemark() != null
                && list.get(i).getCustomerGradeNum() > 0)
            {
                Double precent =
                    (list.get(i).getCustomerGradeRemark() / list.get(i).getCustomerGradeNum()) - list.get(i)
                        .getCapacityPrecent();
                adjustPrecent = adjustPrecent + precent;
                adjustPrecent = (double)Math.round(adjustPrecent * 10000) / 10000;
            }
        }
        return adjustPrecent.toString();
    }

    @Transactional(readOnly = false)
    public Map<String, Object> saveCustomerContact(Customer customer)
    {
        Map<String, Object> data = new HashMap<>();
        if (customer.getRecordId() == null || customer.getBizPerson() == null)
        {
            data.put("result", false);
            data.put("message", "设置默认联系人失败!");
            return data;
        }
        Customer cus = customerDao.get(customer);
        cus.setBizPerson(customer.getBizPerson());
        if (null != cus && null != cus.getStatus() && cus.getStatus().equals(TypeKey.MD_CUSTOMER_STATUS_NORMAL))
        {
            QueryVo queryVo = new QueryVo();
            queryVo.setCustomer(cus);
            String str = QueryApprovalUtils.customerInfoApproval(queryVo, TypeKey.APPROVAL_CUSTOMER_CONCAT_DEFAULT);
            if (!"fail".equals(str))
            {
                data.put("result", true);
                data.put("message", str);
                return data;
            }
        }

        customerDao.setContact(cus);

        data.put("result", true);
        data.put("message", "设置默认联系人成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> saveCustomer(Customer customer)
    {
        Map<String, Object> data = new HashMap<>();
        customer.setCompany(UserUtils.getUser().getCompany());

        // 检验简称和全称是否重复
        if (selectRepeatShortName(customer) > 0 || selectRepeatName(customer) > 0)
        {
            data.put("result", false);
            data.put("message", "简称和全称不能重复!");
            return data;
        }

        // 审核不调用审批
        Customer cus = customerDao.get(customer);
        if (null != cus && !(StringUtils.isNotBlank(customer.getStorageMode()) && "1".equals(customer.getStorageMode())))
        {
            if (TypeKey.MD_CUSTOMER_STATUS_NORMAL.compareTo(cus.getStatus()) == 0 || (StringUtils.isNotBlank(customer.getCustomerType()) && "1".equals(customer.getCustomerType())))
            {
                QueryVo queryVo = new QueryVo();
                queryVo.setCustomer(customer);
                String str = QueryApprovalUtils.customerInfoApproval(queryVo, TypeKey.APPROVAL_CUSTOMER_INFO);
                if (!"fail".equals(str))
                {
                    data.put("result", true);
                    data.put("approval", true);
                    data.put("message", str);
                    return data;
                }
            }
        }

        if (customer.getIsNewRecord())
        {
            // 新创建的客户分配了业务员
            if (null != customer.getSalesman())
            {
                customer.setInitializeDate(new Date());
            }
            Integer noNum = customerDao.findCustomerNoisEnable(customer); // 查询编号是否依旧存在
            while (noNum != null && noNum > 0)
            {
                // 将对应编码的nextNo 修改为+1
                customer.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CUSTOMER.getIndex().toString()));
                noNum = customerDao.findCustomerNoisEnable(customer); // 查询编号是否依旧存在
                if (null != noNum && noNum > 0)
                {
                    CommonUtils.updateNextNo(CommonEnums.CodeType.CUSTOMER.getIndex());
                }
            }
            // 添加仅为初步合作
            customer.setStatus(TypeKey.MD_CUSTOMER_STATUS_INCHOATE);
            customer.preInsert();
            customerDao.insert(customer);

            // 添加成功把对应编码的nextNo 修改为+1
            CommonUtils.updateNextNo(1);
        }
        else
        {
            // 已经存在的客户分配了业务员
            if (null != customer.getSalesman())
            {
                Customer cunzai = customerDao.get(customer.getRecordId());
                User salesman = cunzai.getSalesman();// 原先的业务员
                if (null != salesman) // 已经存在的客户有分配业务员
                {
                    // 已经存在的客户分配的业务员和现在分配的业务员是同一个人
                    if (salesman.getRecordId().equals(customer.getSalesman().getRecordId()))
                    {
                        customer.setInitializeDate(cunzai.getInitializeDate());
                    }
                    else
                    {
                        customer.setInitializeDate(new Date());
                    }
                }
                else// 原先没有分配业务员，现在分配了，设置跟单期限
                {
                    customer.setInitializeDate(new Date());
                }
            }
            else
            {
                customer.setInitializeDate(null);
            }
            customer.preUpdate();
            customerDao.update(customer);

        }

        // 如果当前的联系人列表不为空时，需要插入联系人列表
        if (StringUtils.isNotBlank(customer.getRecordId()))
        {
            if (Collections3.isNotEmpty(customer.getContactList()))
            {
                List<CustomerContact> ccList = Lists.newArrayList();
                for (CustomerContact cc : customer.getContactList())
                {
                    if (StringUtils.isBlank(cc.getRecordId()))
                    {
                        ccList.add(cc);
                    }
                }
                customer.setContactList(ccList);
                customerDao.insertCustomerContact(customer);
                if (customer.getBizPerson() != null)
                {
                    CustomerContact c = new CustomerContact();
                    c.setName(customer.getBizPerson().getName());
                    c.setCompany(customer.getCompany());
                    c.setCustomer(customer);

                    // 查询设置的联系人ID
                    List<CustomerContact> r = contactDao.getByName(c);
                    customer.setBizPerson(r.get(0));
                    customerDao.update(customer);
                }
            }
            if (Collections3.isNotEmpty(customer.getCustomerbranchList()))
            {
                List<CustomerBranch> list = customer.getCustomerbranchList();
                for (CustomerBranch customerBranch : list)
                {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(customerBranch.getRecordId()))
                    {
                        continue;
                    }
                    customerBranch.setCustomerId(customer.getRecordId());
                    customerBranch.setCompany(customer.getCompany());
                    customerBranchDao.insert(customerBranch);
                }
            }
        }
        data.put("result", true);
        data.put("message", "保存客户成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> deleteCustomer(Customer customer, Customer customerCopy)
    {
        Map<String, Object> data = new HashMap<>();
        if (null != customerCopy)
        {
            if (TypeKey.INACTIVE.toString().equals(customerCopy.getActiveFlag()))
            {
                data.put("result", false);
                data.put("message", "客户已经删除了，请刷新页面重试!");
                return data;
            }
            else if (TypeKey.MD_CUSTOMER_STATUS_NORMAL.equals(customerCopy.getStatus()))
            {
                data.put("result", false);
                data.put("message", "客户已经审核了，请刷新页面重试!");
            }
            else
            {
                if (TypeKey.MD_CUSTOMER_STATUS_NORMAL.compareTo(customer.getStatus()) == 0)
                {
                    QueryVo queryVo = new QueryVo();
                    queryVo.setCustomer(customer);
                    String str = QueryApprovalUtils.customerInfoApproval(queryVo, TypeKey.APPROVAL_CUSTOMER_INFO_DELETE);
                    if (!"fail".equals(str))
                    {
                        data.put("result", true);
                        data.put("message", str);
                        return data;
                    }
                }
                customerDao.delete(customer);
            }
        }
        data.put("result", true);
        data.put("message", "删除客户成功!");
        return data;
    }

    public Page<CustomerContact> findContactPage(Page<CustomerContact> page, CustomerContact contact)
    {
        contact.setPage(page);
        page.setList(contactDao.findList(contact));
        return page;
    }

    public List<CustomerContact> findContactList(CustomerContact contact)
    {
        return contactDao.findAllList(contact);
    }

    public List<CustomerContact> getContactByName(CustomerContact contact)
    {
        return contactDao.getByName(contact);
    }

    @Transactional(readOnly = false)
    public Map<String, Object> saveContact(CustomerContact contact)
    {
        Map<String, Object> data = new HashMap<>();
        contact.setCompany(UserUtils.getUser().getCompany());
        if (null != contact.getCustomer() && null != contact.getCustomer().getStatus() && contact.getCustomer().getStatus().equals(TypeKey.MD_CUSTOMER_STATUS_NORMAL))
        {
            QueryVo queryVo = new QueryVo();
            queryVo.setCustomerContact(contact);
            String str = QueryApprovalUtils.customerInfoApproval(queryVo, TypeKey.APPROVAL_CUSTOMER_CONCAT);
            if (!"fail".equals(str))
            {
                data.put("result", true);
                data.put("message", str);
                return data;
            }
        }
        if (contact.getIsNewRecord())
        {
            contact.preInsert();
            contactDao.insert(contact);
        }
        else
        {
            contact.preUpdate();
            contactDao.update(contact);
        }

        data.put("result", true);
        data.put("message", "保存客户联系人成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> deleteContact(CustomerContact customerContact)
    {
        Map<String, Object> data = new HashMap<>();
        customerContact.setCompany(UserUtils.getUser().getCompany());
        if (null != customerContact.getType() && customerContact.getType().compareTo(1) == 0)
        {
            CustomerContact contact = contactDao.get(customerContact);
            if (null == contact)
            {
                data.put("result", false);
                data.put("message", "数据错误!");
                return data;
            }
            if (!"1".equals(contact.getActiveFlag()))
            {
                data.put("result", false);
                data.put("message", "客户联系人已经删除，请刷新重试!");
                return data;
            }
        }
        QueryVo queryVo = new QueryVo();
        queryVo.setCustomerContact(customerContact);
        String str = QueryApprovalUtils.customerInfoApproval(queryVo, TypeKey.APPROVAL_CUSTOMER_CONCAT_DELETE);
        if (!"fail".equals(str))
        {
            data.put("result", true);
            data.put("message", str);
            return data;
        }
        contactDao.delete(customerContact);

        data.put("result", true);
        data.put("message", "删除客户联系人成功!");
        return data;
    }

    public Map<String, List<?>> getDictValueByItems()
    {
        // 返回结果
        Map<String, List<?>> result = Maps.newHashMap();

        // 货币类型
        result.put("currencyType", DictUtils.getValuesByItem(DictItemEnum.CURRENCY_TYPE));

        // 结算方式
        result.put("payWays", DictUtils.getValuesByItem(DictItemEnum.PAY_WAYS));

        // 税类说明
        result.put("taxDescripts", DictUtils.getValuesByItem(DictItemEnum.TAX_DESCRIPTS));

        // 交货方式
        result.put("freightWays", DictUtils.getValuesByItem(DictItemEnum.FREIGHT_WAYS));

        // 送货方式
        result.put("deliveryWays", DictUtils.getValuesByItem(DictItemEnum.DELIVERY_WAYS));

        // 员工数量
        result.put("empCount", DictUtils.getValuesByItem(DictItemEnum.EMP_COUNT));

        // 年销售额
        result.put("yearSalerooms", DictUtils.getValuesByItem(DictItemEnum.ANNUAL_SALES));

        // 区域省份列表
        result.put("provinceList", UserUtils.getAreaListByLevel("1"));

        // WC 2017-05-27 ADD
        // 签订地点
        result.put("signplaceList", DictUtils.getValuesByItem(DictItemEnum.SIGN_PLACE));
        // 交货地点
        result.put("deliverycityList", DictUtils.getValuesByItem(DictItemEnum.DELIVERY_CITY));
        // 运费承担
        result.put("frightbearList", DictUtils.getValuesByItem(DictItemEnum.FREIGHT_BEAR));
        // 检验标准
        result.put("inspectionstandardList", DictUtils.getValuesByItem(DictItemEnum.INSPECTION_STANDARD));
        // 票据形式
        result.put("billformulaList", DictUtils.getValuesByItem(DictItemEnum.BILL_FORMULA));
        // 如有问题
        result.put("hasquestionList", DictUtils.getValuesByItem(DictItemEnum.HAS_QUESTION));
        // 付款方式
        result.put("paycauseList", DictUtils.getValuesByItem(DictItemEnum.PAY_CAUSE));
        // 包装方式
        result.put("packingrequirementList", DictUtils.getValuesByItem(DictItemEnum.PACKING_REQUIREMENT));

        // PCB类型
        result.put("boardLevelList", DictUtils.getValuesByItem(DictItemEnum.BOARD_LEVEL));

        // 覆铜板材
        result.put("materialTypeList", DictUtils.getValuesByItem(DictItemEnum.MATERIAL_TYPE));

        // 板材厚度
        result.put("boardThicknessList", DictUtils.getValuesByItem(DictItemEnum.BOARD_THICKNESS));

        // 覆铜要求
        result.put("copperCladThicknessList", DictUtils.getValuesByItem(DictItemEnum.COPPER_CLAD_THICKNESS));

        // 镀层处理
        result.put("surfaceProcessList", DictUtils.getValuesByItem(DictItemEnum.SURFACE_PROCESS));

        // 阻焊类型
        result.put("solderMaskTypeList", DictUtils.getValuesByItem(DictItemEnum.SOLDER_MASK_TYPE));

        // 板面字符
        result.put("characterTypeList", DictUtils.getValuesByItem(DictItemEnum.CHARACTER_TYPE));

        // 成型方式
        result.put("shapingWayList", DictUtils.getValuesByItem(DictItemEnum.SHAPING_WAY));

        // 测试要求
        result.put("testMethodList", DictUtils.getValuesByItem(DictItemEnum.TEST_METHOD));

        // 最小线宽/线距
        result.put("lingeSpacingList", DictUtils.getValuesByItem(DictItemEnum.LINE_SPACING));

        // 最小孔径
        result.put("smallApertureList", DictUtils.getValuesByItem(DictItemEnum.SMALL_APERTURE));

        // 半孔
        result.put("halAholeList", DictUtils.getValuesByItem(DictItemEnum.HALF_AHOLE));

        // 埋盲孔
        result.put("buryBlindHoleList", DictUtils.getValuesByItem(DictItemEnum.BURY_BLIND_HOLE));

        // 抗阻
        result.put("resistanceList", DictUtils.getValuesByItem(DictItemEnum.RESISTANCE));

        // 发货加急
        result.put("deliveryUrgentList", DictUtils.getValuesByItem(DictItemEnum.DELIVERYTIME));
        // 导热
        result.put("daoreList", DictUtils.getValuesByItem(DictItemEnum.DAORE));
        // 耐压
        result.put("naiyaList", DictUtils.getValuesByItem(DictItemEnum.NAIYA));
        // 客户等级
        result.put("customerGradeList", DictUtils.getValuesByItem(DictItemEnum.CUSTOMER_GRADE));
        // 质保期限
        result.put("payDaysList", DictUtils.getValuesByItem(DictItemEnum.ASSURE_DAYS));

        // 产品类型
        result.put("productTypeList", DictUtils.getValuesByItem(DictItemEnum.PRODUCTTYPE));

        return result;
    }

    @Override
    public void afterPropertiesSet()
        throws Exception
    {

    }

    /**
     * 提供对外接口
     *
     * @param customer
     * @return
     */
    public List<Customer> findCustomers(Customer customer)
    {
        return customerDao.findCustomers(customer);
    }

    public Integer selectRepeatNo(Customer customer)
    {
        return customerDao.selectRepeatNo(customer);
    }

    public Integer selectRepeatShortName(Customer customer)
    {
        return customerDao.selectRepeatShortName(customer);
    }

    public Integer selectRepeatName(Customer customer)
    {
        return customerDao.selectRepeatName(customer);
    }

    /**
     * 设置客户预投率
     *
     * @param customer
     * @return
     */
    @Transactional(readOnly = false)
    public void updateScrappage(Customer customer)
    {
        customerDao.updateScrappage(customer);
    }

    @Transactional(readOnly = false)
    public void updateSaleManAndInitDate(Customer customer)
    {
        customerDao.updateSaleManAndInitDate(customer);
    }

    @Transactional(readOnly = false)
    public void updateInitDate(Customer customer)
    {
        customerDao.updateInitDate(customer);
    }

    @Transactional(readOnly = false)
    public void updateCreateDateAndInitDate(Customer customer)
    {
        customerDao.updateCreateDateAndInitDate(customer);
    }

    public Customer getCusByUser(User user)
    {
        return customerDao.getCusByUser(user);
    }

    @Transactional(readOnly = false)
    public void audit(Customer customer)
    {
        customer.preUpdate();
        customerDao.update(customer);
    }

    public Customer getCustomerByNo(Customer customer)
    {
        return customerDao.getCustomerByNo(customer);
    }

    @Transactional(readOnly = false)
    public String saveQutoPrice(ContractCraft contractCraft)
    {
        // 修改前后值是否相同
        String message = "";
        if (contractCraft != null && contractCraft.getCustomodelPrice() != null)
        {
            contractCraft.getCustomodelPrice().setStatus(null);
            contractCraft.getCustomodelPrice().setDiscountRate(null);
            contractCraft.getCustomodelPrice().setOldDiscountRate(null);

            if (StringUtils.isNotBlank(contractCraft.getQuoteFlag()) && contractCraft.getQuoteFlag().equals("2"))
            {
                contractCraftDao.partInQuotation(contractCraft);
            }
            CustomodelPrice customodelPrice = contractCraft.getCustomodelPrice();
            customodelPrice.setCraft(contractCraft);
            customodelPrice.setCompany(UserUtils.getUser().getCompany());

            if (StringUtils.isNotBlank(contractCraft.getCustomodelPrice().getRecordId()))
            {
                // 已有报价，只修改该工艺的报价价格
                contractCraftDao.updateCustQuoPrice(contractCraft.getCustomodelPrice());
            }
            else
            {
                // 无报价，添加该工艺型号的报价
                contractCraftDao.saveCustQuoPrice(contractCraft.getCustomodelPrice());
            }
        }
        if ("".equals(message))
        {
            message = "设置成功!!!";
        }
        return message;

    }

    @Transactional(readOnly = false)
    public void partInQuotation(ContractCraft contractCraft)
    {
        contractCraftDao.partInQuotation(contractCraft);
    }

    /**
     * lh 单个更新协议价优惠率
     */
    @Transactional(readOnly = false)
    public String updateQuoPriceDiscount(ContractCraft craft)
    {
        // 修改前后值是否相同
        int compare = 1;
        CustomodelPrice customodelPrice = contractCraftDao.getCustomodelPriceById(craft.getCustomodelPrice());
        if (craft != null && null != craft.getCustomodelPrice() && null != craft.getCustomodelPrice().getDiscountRate())
        {
            craft.getCustomodelPrice().setStatus(TypeKey.MD_CUSTOMER_QUOTE_AUDIT.toString());
            // 判断是否已经生产该条数据
            if (customodelPrice != null)
            {
                // 判断该修改价格是否和上次一样
                if (null != customodelPrice.getOldDiscountRate())
                {
                    compare =
                        craft.getCustomodelPrice().getDiscountRate().compareTo(customodelPrice.getOldDiscountRate());
                }
                if (compare == 0)
                {

                    return "优惠率不变不提交审核!!!";
                }
                craft.getCustomodelPrice().setDiscountRate(craft.getCustomodelPrice().getDiscountRate());

                contractCraftDao.updateCustQuoPrice(craft.getCustomodelPrice());
            }
            else
            {
                craft.getCustomodelPrice().setCompany(UserUtils.getUser().getCompany());
                craft.getCustomodelPrice().setCraft(craft);
                // craft.getCustomodelPrice().setOldDiscountRate(craft.getCustomodelPrice().getDiscountRate());
                contractCraftDao.saveCustQuoPrice(craft.getCustomodelPrice());
            }
        }

        return "单个优惠率设置成功!!!";
    }

    /**
     * lh 更新全部协议价优惠率
     */
    @Transactional(readOnly = false)
    public String updateALlQuoPriceDiscount(ContractCraft craft)
    {
        if (craft.getCustomodelPrice() != null && null != craft.getCustomodelPrice().getDiscountRate())
        {

            craft.setCompany(UserUtils.getUser().getCompany());
            List<ContractCraft> contractCraftList = contractCraftDao.getCraftByCustomer(craft);
            for (ContractCraft contractCraft : contractCraftList)
            {
                if (null != contractCraft.getCustomodelPrice() && null != contractCraft.getRecordId())
                {
                    // 判断该修改价格是否和上次一样
                    int compare = 1;
                    if (null != contractCraft.getCustomodelPrice().getOldDiscountRate())
                    {
                        compare = craft.getCustomodelPrice()
                            .getDiscountRate()
                            .compareTo(contractCraft.getCustomodelPrice().getOldDiscountRate());
                    }
                    if (compare != 0)
                    {
                        contractCraft.getCustomodelPrice().setStatus(TypeKey.MD_CUSTOMER_QUOTE_AUDIT.toString());
                        contractCraft.getCustomodelPrice()
                            .setDiscountRate(craft.getCustomodelPrice().getDiscountRate());
                        contractCraft.getCustomodelPrice().setCraft(contractCraft);
                        contractCraftDao.updateCustQuoPriceByCraftId(contractCraft.getCustomodelPrice());
                    }

                }
                else
                {
                    CustomodelPrice customodelPriceInof = new CustomodelPrice();
                    contractCraft.setCustomodelPrice(customodelPriceInof);
                    contractCraft.getCustomodelPrice().setCompany(UserUtils.getUser().getCompany());
                    contractCraft.getCustomodelPrice().setDiscountRate(craft.getCustomodelPrice().getDiscountRate());
                    contractCraft.getCustomodelPrice().setStatus(TypeKey.MD_CUSTOMER_QUOTE_AUDIT.toString());
                    contractCraft.getCustomodelPrice().setCraft(contractCraft);
                    contractCraftDao.saveCustQuoPrice(contractCraft.getCustomodelPrice());
                }
            }
        }

        return "全部优惠率设置成功!!!";

    }

    /**
     * lh 协议价审核
     */
    @Transactional(readOnly = false)
    public void updateQuoPriceStatus(CustomodelPrice customodelPrice)
    {
        contractCraftDao.updateCustQuoPrice(customodelPrice);

    }

    // lh 查询客户
    @Transactional(readOnly = false)
    public List<Customer> findCustomerByCompanyId(Customer customer)
    {
        return customerDao.findCustomerByCompanyId(customer);

    }

    // 设置客户为有效时验证公司名
    public Integer selectName(Customer customer)
    {
        Integer name = 0;
        Integer repeatName = customerDao.selectRepeatName(customer);
        Integer shortName = customerDao.selectRepeatShortName(customer);
        if (repeatName > 0 || shortName > 0)
        {
            name = 1;
        }
        return name;
    }

    // 根据客户id获取业务员名字和id
    public User getSaleMan(Customer customer)
    {
        return customerDao.getSaleMan(customer);
    }

    // 设置客户的难度系数
    @Transactional(readOnly = false)
    public Map<String, Object> setDiffiCoff(Customer customer)
    {
        Map<String, Object> data = new HashMap<>();
        customer.setCompany(UserUtils.getUser().getCompany());
        QueryVo queryVo = new QueryVo();
        queryVo.setCustomer(customer);
        String str = QueryApprovalUtils.customerInfoApproval(queryVo, TypeKey.APPROVAL_CUSTOMER_DIFFICOFF);
        if (!"fail".equals(str))
        {
            data.put("result", true);
            data.put("approval", true);
            data.put("message", str);
            return data;
        }
        customerDao.setDiffiCoff(customer);
        data.put("result", true);
        data.put("message", "难度系数设置成功");
        return data;
    }

    /** zjn 2019-01-27 导出客户数据 */
    public List<Customer> exportCustomerList(Page<Customer> page, Customer customer)
    {
        customer.setPage(page);
        return customerDao.exportCustomerList(customer);
    }

    /** zjn 2019-12-30 手动生成审批记录 */
    @Transactional(readOnly = false)
    public Map<String, Object> produceApproval(Customer customer)
    {
        Map<String, Object> data = new HashMap<>();
        customer.setCompany(UserUtils.getUser().getCompany());
        BackupTwo backupTwo = new BackupTwo();
        backupTwo.setCompany(customer.getCompany());
        backupTwo.setCustomerId(customer.getRecordId());
        List<BackupTwo> btList = backupTwoDao.getBackupList(backupTwo);
        if (Collections3.isEmpty(btList))
        {
            data.put("result", false);
            data.put("message", "您没有需要生成审批的记录");
            return data;
        }
        // 获取客户资料待审批记录
        Approval checkParma = new Approval();
        checkParma.setTypeId("14");
        checkParma.setCompany(customer.getCompany());
        checkParma.setDataId(customer.getRecordId());
        Integer checkNum = approvalDao.checkIsApproved(checkParma);
        if (null != checkNum && checkNum > 0)
        {
            data.put("result", false);
            data.put("message", "客户资料在审批中，请勿重复提交");
            return data;
        }
        String result = QueryApprovalUtils.produceCustomerApproval(btList, customer);
        data.put("result", true);
        data.put("approval", true);
        data.put("message", result);
        data.put("waitCapa", getWaitCapa(UserUtils.getUser()));
        return data;
    }

    /** zjn 2020-01-11 移除客户附件 */
    @Transactional(readOnly = false)
    public String clearUploadAtt(CustomerAttachements customerAttachements)
    {
        Company company = UserUtils.getUser().getCompany();
        customerAttachements.setCompany(company);
        String result = null;
        if (customerAttachements.getApprovalFlag())
        {
            QueryVo queryVo = new QueryVo();
            queryVo.setCustomerAttachements(customerAttachements);
            result = QueryApprovalUtils.customerInfoApproval(queryVo, TypeKey.APPROVAL_CUSTOMER_DELETE_ATT);
        }
        else
        {
            Customer customer = customerDao.get(customerAttachements.getCustomerId());
            if (null != customer)
            {
                String savePath =
                    company.getRecordId() + "/" + customer.getNo() + "/" + customerAttachements.getRealFileName();
                FileManageUtils.delFiles(savePath);
            }
            customerAttachementsDao.delete(customerAttachements);
            result = "移除客户附件成功";
        }
        return result;
    }

    public Map<String, Object> updateAdjustData()
    {
        Map<String, Object> data = new HashMap<>();
        // Company company = UserUtils.getUser().getCompany();
        data.put("result", "success");
        data.put("message", "平米和比例调整成功!");
        return data;
    }

    public ByteArrayOutputStream selectPrintWord(OutBound outBound, String printStatus, HttpServletRequest request)
        throws DocumentException, IOException
    {
        Map<String, String> kybMap = new HashMap<String, String>();
        outBound.setPrintStatus(printStatus);
        OutBound outBoundData = outBoundDao.loadOutBoundById(outBound);
        String inputUrl = "wordPrint/common/test0.docx";
        if (StringUtils.isNotBlank(outBoundData.getFileUrl()))
        {
            inputUrl = outBoundData.getFileUrl();
        }

        inputUrl = FileManageUtils.getUrl(inputUrl).toString();
        if (StringUtils.isBlank(outBoundData.getPrintTemId()))
        {
            // 使用默认模板
            outBoundData = outBoundDao.loadOutBoundByIdTwo(outBound);
        }
        List<OutBoundDeail> deailList = outBoundDao.getOutBoundDeailList(outBoundData);
        ByteArrayOutputStream output = new ByteArrayOutputStream();

        Company cp = new Company();
        if (StringUtils.isBlank(printStatus) && outBoundData != null
            && StringUtils.isNotBlank(outBoundData.getBindErpComId()))
        {
            cp = companyDao.get(outBoundData.getBindErpComId());
        }
        else
        {
            cp = companyDao.get(UserUtils.getUser().getCompany().getRecordId());
        }

        kybMap.put("键公司全称", cp.getName() == null ? "" : cp.getName());
        kybMap.put("键公司简称", cp.getShortName() == null ? "" : cp.getShortName());
        kybMap.put("键公司英文", cp.getNameEn() == null ? "" : cp.getNameEn());
        kybMap.put("键公司地址", cp.getAddress() == null ? "" : cp.getAddress());
        kybMap.put("键公司手机", cp.getPhone() == null ? "" : cp.getPhone());
        kybMap.put("键公司传真", cp.getFax() == null ? "" : cp.getFax());
        kybMap.put("键公司网站", cp.getWebsite() == null ? "" : cp.getWebsite());
        kybMap.put("键公司邮箱", cp.getEmail() == null ? "" : cp.getEmail());
        kybMap.put("键客户全称", outBoundData.getSaleCustName() == null ? "" : outBoundData.getSaleCustName());
        kybMap.put("键送货单号", outBoundData.getNo() == null ? "" : outBoundData.getNo());
        kybMap.put("键联系人名字", outBoundData.getName() == null ? "" : outBoundData.getName());
        kybMap.put("键联系人电话", outBoundData.getPhone() == null ? "" : outBoundData.getPhone());
        kybMap.put("键送货日期", outBoundData.getCreateDateStr() == null ? "" : outBoundData.getCreateDateStr());
        kybMap.put("键客户地址", outBoundData.getAddress() == null ? "" : outBoundData.getAddress());

        Integer sortNo = 0;
        for (int i = 0; i < deailList.size(); i++)
        {
            sortNo += 1;
            String craftNo = "";
            if (StringUtils.isNotBlank(deailList.get(i).getNo()))
            {
                craftNo = deailList.get(i).getNo();
            }
            if (StringUtils.isNotBlank(deailList.get(i).getRemark()))
            {
                if (StringUtils.isBlank(craftNo))
                {
                    craftNo = deailList.get(i).getRemark();
                }
                else
                {
                    craftNo = craftNo + "(" + deailList.get(i).getRemark() + ")";
                }
            }
            String customerModel = deailList.get(i).getCustomerModel();
            if (customerModel.indexOf("/") >= 0)
            {
                customerModel = customerModel.split("/")[1];
            }
            Integer quatity = deailList.get(i).getOutBoundStocks();
            if (quatity == null)
            {
                if (deailList.get(i).getDonateStocks() == null)
                {
                    quatity = deailList.get(i).getQuantity().intValue();
                }
                else
                {
                    quatity = 0;
                }
            }
            String remark = deailList.get(i).getRemark();
            String customerModels = deailList.get(i).getCustomerModel();
            if (customerModels.indexOf("/") >= 0)
            {
                remark = customerModels.split("/")[0];
            }
            Date now = new Date();
            Calendar cal = Calendar.getInstance();
            cal.setTime(now);
            cal.add(Calendar.YEAR, +1);
            String nowDate = DateUtils.formatDate(cal.getTime(), "yyyy-MM-dd");
            kybMap.put("键" + i + "序号", sortNo.toString());
            kybMap.put("键" + i + "客户订单号",
                deailList.get(i).getCustomerPo() == null ? "" : deailList.get(i).getCustomerPo());
            kybMap.put("键" + i + "生产编号", deailList.get(i).getNo() == null ? "" : deailList.get(i).getNo());
            kybMap.put("键" + i + "客户物料号",
                deailList.get(i).getCustomerMaterialNo() == null ? "" : deailList.get(i).getCustomerMaterialNo());
            kybMap.put("键" + i + "客户型号",
                deailList.get(i).getCustomerModel() == null ? "" : deailList.get(i).getCustomerModel());
            kybMap.put("键" + i + "客户型号拆分后", customerModel);
            kybMap.put("键" + i + "数量", quatity.toString());
            kybMap.put("键" + i + "备品",
                deailList.get(i).getDonateStocks() == null ? "0" : deailList.get(i).getDonateStocks().toString());

            kybMap.put("键" + i + "备注", deailList.get(i).getRemark() == null ? "" : deailList.get(i).getRemark());
            kybMap.put("键" + i + "备注", deailList.get(i).getRemark() == null ? "" : deailList.get(i).getRemark());
            kybMap.put("键" + i + "客户型号拆分前", remark);
            kybMap.put("键" + i + "有效期", nowDate);
            kybMap.put("键" + i + "单位", "PCS");
        }
        // 模板文件地址
        output = BokeWordUtils.changWord(inputUrl, kybMap);
        return output;
    }

    public Pdf selectToPrint(OutBound outBound, String printStatus, HttpServletRequest request)
        throws DocumentException, IOException
    {
        outBound.setPrintStatus(printStatus);
        OutBound outBoundData = outBoundDao.loadOutBoundById(outBound);
        if (StringUtils.isBlank(outBoundData.getPrintTemId()))
        {
            // 使用默认模板
            outBoundData = outBoundDao.loadOutBoundByIdTwo(outBound);
        }
        List<OutBoundDeail> deailList = outBoundDao.getOutBoundDeailList(outBoundData);
        // 计算送货总金额
        BigDecimal deliveryAmount = BigDecimal.ZERO;
        for (OutBoundDeail outBoundDeail : deailList)
        {
            deliveryAmount = deliveryAmount.add(outBoundDeail.getPrice().multiply(BigDecimal.valueOf(outBoundDeail.getOutBoundStocks())));
        }
        Pdf pdf = null;
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        Rectangle rectPageSize = new Rectangle(PageSize.A4);
        Document document = new Document(rectPageSize, 30, 30, 6, 0);
        ByteArrayOutputStream page = new ByteArrayOutputStream();
        @SuppressWarnings("unused") PdfWriter writer = PdfWriter.getInstance(document, page);
        document.open();
        BaseFont chinese =
            BaseFont.createFont("/font/simsun.ttc" + ",1", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED); // 系统默认中文（宋体）
        Font FontChinese1 = new Font(chinese, 18);
        Font FontChinese11 = new Font(chinese, 11);
        Font FontChinese12 = new Font(chinese, 13);

        Company cp = new Company();
        if (StringUtils.isBlank(printStatus) && outBoundData != null
            && StringUtils.isNotBlank(outBoundData.getBindErpComId()))
        {
            cp = companyDao.get(outBoundData.getBindErpComId());
        }
        else
        {
            cp = companyDao.get(UserUtils.getUser().getCompany().getRecordId());
        }
        outBoundData.setHeadLogoFlag("1");
        if (StringUtils.isNotBlank(outBoundData.getHeadLogoFlag()) && outBoundData.getHeadLogoFlag().equals("1"))
        {
            String path = null;
            if (StringUtils.isNotBlank(cp.getLogo()))
            {
                if (FileManageUtils.isLocal())
                {
                    cp.setLogo(FileManageUtils.getLocalOrOssUrlAsString(cp.getLogo()));
                }
                else
                {
                    cp.setLogo(FileManageUtils.getUrl(cp.getLogo()).toString());
                }
                path = cp.getLogo();
            }
            Image img = null;
            try
            {
                img = Image.getInstance(path);
            }
            catch (Exception e)
            {
                path = null;
            }
            img.scaleAbsolute(50, 50);// 直接设定显示尺寸
            // img.scalePercent(60);
            img.setAbsolutePosition(83, 790);
            document.add(img);
        }
        if (StringUtils.isNotBlank(outBoundData.getHeadNameFlag()) && outBoundData.getHeadNameFlag().equals("1"))
        {
            Paragraph par = null;
            String headerNameJoin = (cp.getName() == null ? "" : cp.getName());
            String headerName = String.join(" ", headerNameJoin.split(""));
            par = new Paragraph(headerName, FontChinese1);
            if (par != null)
            {
                par.setAlignment(Element.ALIGN_CENTER);
                document.add(par);
            }
        }
        if (StringUtils.isNotBlank(outBoundData.getHeadShortNameFlag()) && outBoundData.getHeadShortNameFlag()
            .equals("1"))
        {
            Paragraph par = null;
            String headerNameJoin = (cp.getShortName() == null ? "" : cp.getShortName());
            String headerName = String.join("  ", headerNameJoin.split(""));
            par = new Paragraph(headerName, FontChinese1);
            if (par != null)
            {
                par.setAlignment(Element.ALIGN_CENTER);
                document.add(par);
            }
        }
        if (StringUtils.isNotBlank(outBoundData.getHeadNameEnFlag()) && outBoundData.getHeadNameEnFlag().equals("1"))
        {
            Paragraph par11 = new Paragraph((cp.getNameEn() == null ? "" : cp.getNameEn()), FontChinese11);
            if (par11 != null)
            {
                par11.setAlignment(Element.ALIGN_CENTER);
                document.add(par11);
            }
        }
        if (StringUtils.isNotBlank(outBoundData.getHeadAddressFlag()) && outBoundData.getHeadAddressFlag().equals("1"))
        {
            Paragraph par12 =
                new Paragraph("Add:" + (cp.getAddress() == null ? "" : cp.getAddress()) + "\n", FontChinese12);
            if (StringUtils.isNotBlank(outBoundData.getHeadPhoneFlag()) && outBoundData.getHeadPhoneFlag().equals("1"))
            {
                par12.add("Tel:" + (cp.getPhone() == null ? "" : cp.getPhone()) + "     ");
            }
            if (StringUtils.isNotBlank(outBoundData.getHeadFaxFlag()) && outBoundData.getHeadFaxFlag().equals("1"))
            {
                par12.add("Fax:" + (cp.getFax() == null ? "" : cp.getFax()) + "\n");
            }
            if (StringUtils.isNotBlank(outBoundData.getHeadWebEmailFlag()) && outBoundData.getHeadWebEmailFlag()
                .equals("1"))
            {
                par12.add((cp.getWebsite() == null ? "" : cp.getWebsite()) + "     e-mail:" + (cp.getEmail() == null ?
                    "" :
                    cp.getEmail()) + "\n");
            }
            if (par12 != null)
            {
                par12.setAlignment(Element.ALIGN_CENTER);
                document.add(par12);
            }
        }
        // Paragraph par13 = new Paragraph("DRLIVERY NOTE 送貨單" + "\n", FontChinese1);
        // if (par13 != null)
        // {
        // par13.setAlignment(Element.ALIGN_CENTER);
        // document.add(par13);
        // }
        if (StringUtils.isNotBlank(outBoundData.getCustNameFlag()) && outBoundData.getCustNameFlag().equals("1"))
        {
            PdfPTable tableHeader = new PdfPTable(7);
            tableHeader.setTotalWidth(520f);
            tableHeader.setLockedWidth(true);
            tableHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
            tableHeader.getDefaultCell().setBorder(PdfPCell.NO_BORDER);

            PdfPCell cellHeader = new PdfPCell(new Phrase("", FontChinese11));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setBorder(PdfPCell.NO_BORDER);
            cellHeader.setColspan(7);
            cellHeader.setFixedHeight(5);
            tableHeader.addCell(cellHeader);

            String name = "客户名称 NAME";
            if (StringUtils.isNotBlank(outBoundData.getCustName()))
            {
                name = outBoundData.getCustName();
            }
            cellHeader = new PdfPCell(new Phrase(name, FontChinese11));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(1);
            cellHeader.setFixedHeight(20);
            tableHeader.addCell(cellHeader);

            cellHeader =
                new PdfPCell(new Phrase((outBoundData.getSaleCustName() == null ? "" : outBoundData.getSaleCustName()),
                    FontChinese11));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(3);
            cellHeader.setFixedHeight(20);
            tableHeader.addCell(cellHeader);

            name = "送货单号";
            if (StringUtils.isNotBlank(outBoundData.getNoStr()))
            {
                name = outBoundData.getNoStr();
            }
            cellHeader = new PdfPCell(new Phrase(name, FontChinese11));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(1);
            cellHeader.setFixedHeight(20);
            tableHeader.addCell(cellHeader);

            cellHeader =
                new PdfPCell(new Phrase((outBoundData.getNo() == null ? "" : outBoundData.getNo()), FontChinese11));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(2);
            cellHeader.setFixedHeight(20);
            tableHeader.addCell(cellHeader);

            name = "联系人 LINKMAN";
            if (StringUtils.isNotBlank(outBoundData.getLinkManStr()))
            {
                name = outBoundData.getLinkManStr();
            }
            cellHeader = new PdfPCell(new Phrase(name, FontChinese11));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(1);
            cellHeader.setFixedHeight(20);
            tableHeader.addCell(cellHeader);

            cellHeader =
                new PdfPCell(new Phrase((outBoundData.getName() == null ? "" : outBoundData.getName()), FontChinese11));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setBorder(PdfPCell.NO_BORDER);
            cellHeader.setColspan(1);
            cellHeader.setFixedHeight(20);
            tableHeader.addCell(cellHeader);

            cellHeader =
                new PdfPCell(new Phrase("电话：" + (outBoundData.getPhone() == null ? "" : outBoundData.getPhone()),
                    FontChinese11));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setBorder(PdfPCell.NO_BORDER);
            cellHeader.setColspan(2);
            cellHeader.setFixedHeight(20);
            tableHeader.addCell(cellHeader);

            name = "送货日期";
            if (StringUtils.isNotBlank(outBoundData.getDateName()))
            {
                name = outBoundData.getDateName();
            }
            cellHeader = new PdfPCell(new Phrase(name, FontChinese11));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(1);
            cellHeader.setFixedHeight(20);
            tableHeader.addCell(cellHeader);

            cellHeader = new PdfPCell(new Phrase((outBoundData.getCreateDateStr() == null ?
                "" :
                outBoundData.getCreateDateStr()), FontChinese11));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(2);
            cellHeader.setFixedHeight(20);
            tableHeader.addCell(cellHeader);

            name = "客户地址 ADD";
            if (StringUtils.isNotBlank(outBoundData.getCustAdd()))
            {
                name = outBoundData.getCustAdd();
            }
            cellHeader = new PdfPCell(new Phrase(name, FontChinese11));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(1);
            cellHeader.setFixedHeight(20);
            tableHeader.addCell(cellHeader);

            cellHeader = new PdfPCell(new Phrase((outBoundData.getAddress() == null ? "" : outBoundData.getAddress()),
                FontChinese11));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(6);
            cellHeader.setFixedHeight(20);
            tableHeader.addCell(cellHeader);

            if (tableHeader != null)
            {
                document.add(tableHeader);
            }
        }

        List<CustPrintKey> bodyList = new ArrayList<CustPrintKey>();
        Integer num = 0;
        if (StringUtils.isNotBlank(outBoundData.getDeailNoFlag()) && outBoundData.getDeailNoFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("deailNoFlag");
            key.setSortNum(outBoundData.getDeailNoSortNum());
            bodyList.add(key);
            num = num + 1;
        }
        if (StringUtils.isNotBlank(outBoundData.getDeailCustNoFlag()) && outBoundData.getDeailCustNoFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("deailCustNoFlag");
            key.setSortNum(outBoundData.getDeailCustSortNum());
            bodyList.add(key);
            num = num + 2;
        }
        if (StringUtils.isNotBlank(outBoundData.getCraftNoFlag()) && outBoundData.getCraftNoFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("craftNoFlag");
            key.setSortNum(outBoundData.getCraftNoSortNum());
            bodyList.add(key);
            num = num + 2;
        }
        if (StringUtils.isNotBlank(outBoundData.getDeailMaterNoFlag()) && outBoundData.getDeailMaterNoFlag()
            .equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("deailMaterNoFlag");
            key.setSortNum(outBoundData.getDeailMaterNoSortNum());
            bodyList.add(key);
            num = num + 2;
        }
        if (StringUtils.isNotBlank(outBoundData.getCustModalFlag()) && outBoundData.getCustModalFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("custModalFlag");
            key.setSortNum(outBoundData.getCustModalSortNum());
            bodyList.add(key);
            num = num + 4;
        }
        if (StringUtils.isNotBlank(outBoundData.getQualitityFlag()) && outBoundData.getQualitityFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("qualitityFlag");
            key.setSortNum(outBoundData.getQualititySortNum());
            bodyList.add(key);
            num = num + 2;
        }
        if (StringUtils.isNotBlank(outBoundData.getDoonateFlag()) && outBoundData.getDoonateFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("doonateFlag");
            key.setSortNum(outBoundData.getDoonateSortNum());
            bodyList.add(key);
            num = num + 2;
        }
        if (StringUtils.isNotBlank(outBoundData.getRemarkFlag()) && outBoundData.getRemarkFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("remarkFlag");
            key.setSortNum(outBoundData.getRemarkSortNum());
            bodyList.add(key);
            num = num + 4;
        }
        if (StringUtils.isNotBlank(outBoundData.getDeliverySizeFlag()) && outBoundData.getDeliverySizeFlag()
            .equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("deliverySizeFlag");
            key.setSortNum(outBoundData.getDeliverySizeSortNum());
            bodyList.add(key);
            num = num + 2;
        }
        if (StringUtils.isNotBlank(outBoundData.getDeailDateFlag()) && outBoundData.getDeailDateFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("deailDateFlag");
            key.setSortNum(outBoundData.getDeailDateSortNum());
            bodyList.add(key);
            num = num + 3;
        }
        if (StringUtils.isNotBlank(outBoundData.getDeailProductNameFlag()) && outBoundData.getDeailProductNameFlag()
            .equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("deailProductNameFlag");
            key.setSortNum(outBoundData.getDeailProductNameSortNum());
            bodyList.add(key);
            num = num + 2;
            FontChinese12 = new Font(chinese, 8);
        }
        boolean lhFlag = false;
        bodyList.sort((x, y) -> Integer.compare(StringUtils.isBlank(x.getSortNum()) ?
                0 :
                Integer.valueOf(x.getSortNum()),
            StringUtils.isBlank(y.getSortNum()) ? 0 : Integer.valueOf(y.getSortNum())));
        if (bodyList != null && bodyList.size() > 0)
        {
            PdfPTable tableBody = new PdfPTable(num);
            tableBody.setTotalWidth(520f);
            tableBody.setLockedWidth(true);
            tableBody.setHorizontalAlignment(Element.ALIGN_CENTER);
            tableBody.getDefaultCell().setBorder(PdfPCell.NO_BORDER);

            for (int i = 0; i < bodyList.size(); i++)
            {
                PdfPCell cellBody = null;
                if (bodyList.get(i).getName().equals("deailNoFlag"))
                {
                    String name = "序号";
                    if (StringUtils.isNotBlank(outBoundData.getDeailNo()))
                    {
                        name = outBoundData.getDeailNo();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese11));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(1);
                    cellBody.setFixedHeight(20);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("deailCustNoFlag"))
                {
                    String name = "订单号";
                    if (StringUtils.isNotBlank(outBoundData.getDeailCustNo()))
                    {
                        name = outBoundData.getDeailCustNo();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese11));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(15);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("craftNoFlag"))
                {
                    String name = "厂编";
                    if (StringUtils.isNotBlank(outBoundData.getCraftNo()))
                    {
                        name = outBoundData.getCraftNo();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese11));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(20);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("deailMaterNoFlag"))
                {
                    String name = "客户物料号";
                    if (StringUtils.isNotBlank(outBoundData.getDeailMaterNo()))
                    {
                        name = outBoundData.getDeailMaterNo();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese11));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(20);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("custModalFlag"))
                {
                    String name = "客户型号";
                    if (StringUtils.isNotBlank(outBoundData.getCustModal()))
                    {
                        name = outBoundData.getCustModal();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese11));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(4);
                    cellBody.setFixedHeight(20);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("qualitityFlag"))
                {
                    String name = "送货数量";
                    if (StringUtils.isNotBlank(outBoundData.getQualitity()))
                    {
                        name = outBoundData.getQualitity();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese11));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(20);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("doonateFlag"))
                {
                    String name = "备品数量";
                    if (StringUtils.isNotBlank(outBoundData.getDoonate()))
                    {
                        name = outBoundData.getDoonate();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese11));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(20);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("remarkFlag"))
                {
                    String name = "备注";
                    if (StringUtils.isNotBlank(outBoundData.getRemarkName()))
                    {
                        name = outBoundData.getRemarkName();
                    }
                    if (name.equals("料号"))
                    {
                        lhFlag = true;
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese11));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(4);
                    cellBody.setFixedHeight(20);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("deliverySizeFlag"))
                {
                    String name = "交货尺寸";
                    if (StringUtils.isNotBlank(outBoundData.getDeliverySize()))
                    {
                        name = outBoundData.getDeliverySize();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese11));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(20);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("deailDateFlag"))
                {
                    String name = "有效日期";
                    if (StringUtils.isNotBlank(outBoundData.getDeailDate()))
                    {
                        name = outBoundData.getDeailDate();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese11));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(3);
                    cellBody.setFixedHeight(20);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("deailProductNameFlag"))
                {
                    String name = "货物名称";
                    if (StringUtils.isNotBlank(outBoundData.getDeailProductName()))
                    {
                        name = outBoundData.getDeailProductName();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese11));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(20);
                    tableBody.addCell(cellBody);
                }
            }
            // 根据出库id获取出库明细
            int samNum = 0;
            Integer sortNo = 0;
            for (int i = 0; i < deailList.size(); i++)
            {
                for (int j = 0; j < bodyList.size(); j++)
                {
                    PdfPCell cellBody = null;
                    if (bodyList.get(j).getName().equals("deailNoFlag"))
                    {
                        sortNo += 1;
                        cellBody = new PdfPCell(new Phrase(sortNo.toString(), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(1);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("deailCustNoFlag"))
                    {
                        cellBody = new PdfPCell(new Phrase((deailList.get(i).getCustomerPo() == null ?
                            "" :
                            deailList.get(i).getCustomerPo()), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("craftNoFlag"))
                    {
                        String craftNo = "";
                        if (StringUtils.isNotBlank(deailList.get(i).getNo()))
                        {
                            craftNo = deailList.get(i).getNo();
                        }
                        if (StringUtils.isNotBlank(deailList.get(i).getRemark()))
                        {
                            if (StringUtils.isBlank(craftNo))
                            {
                                craftNo = deailList.get(i).getRemark();
                            }
                            else
                            {
                                craftNo = craftNo + "(" + deailList.get(i).getRemark() + ")";
                            }
                        }
                        cellBody = new PdfPCell(new Phrase(craftNo, FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("deailMaterNoFlag"))
                    {
                        cellBody = new PdfPCell(new Phrase((deailList.get(i).getCustomerMaterialNo() == null ?
                            "" :
                            deailList.get(i).getCustomerMaterialNo()), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("custModalFlag"))
                    {
                        String customerModel = deailList.get(i).getCustomerModel();
                        if (lhFlag)
                        {
                            if (customerModel.indexOf("/") >= 0)
                            {
                                customerModel = customerModel.split("/")[1];
                            }
                        }
                        cellBody =
                            new PdfPCell(new Phrase((customerModel == null ? "" : customerModel), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(4);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("qualitityFlag"))
                    {
                        Integer quatity = deailList.get(i).getOutBoundStocks() == null ?
                            deailList.get(i).getQuantity().intValue() :
                            deailList.get(i).getOutBoundStocks();
                        cellBody = new PdfPCell(new Phrase(quatity.toString(), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("doonateFlag"))
                    {
                        Integer quatity = deailList.get(i).getDonateStocks();
                        if (quatity == null)
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                        }
                        else
                        {
                            cellBody = new PdfPCell(new Phrase(quatity.toString(), FontChinese12));
                        }
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("remarkFlag"))
                    {
                        String remark = deailList.get(i).getRemark();
                        if (lhFlag)
                        {
                            String customerModel = deailList.get(i).getCustomerModel();
                            if (customerModel.indexOf("/") >= 0)
                            {
                                remark = customerModel.split("/")[0];
                            }
                        }
                        if ("蚌埠崧欣电子科技有限公司".equals(outBoundData.getSaleCustName()))
                        {
                            if (null != deailList.get(i).getSubTotal()
                                && deailList.get(i).getSubTotal().compareTo(BigDecimal.ZERO) > 0)
                            {
                                remark = "收费样品";
                            }
                        }
                        cellBody = new PdfPCell(new Phrase((remark == null ? "" : remark), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(4);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("deliverySizeFlag"))
                    {
                        cellBody = new PdfPCell(new Phrase((deailList.get(i).getDeliverySize() == null ?
                            "" :
                            deailList.get(i).getDeliverySize()), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("deailDateFlag"))
                    {
                        Date now = new Date();
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(now);
                        cal.add(Calendar.YEAR, +1);
                        String nowDate = DateUtils.formatDate(cal.getTime(), "yyyy-MM-dd");
                        cellBody = new PdfPCell(new Phrase(nowDate, FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(3);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("deailProductNameFlag"))
                    {
                        cellBody = new PdfPCell(new Phrase(deailList.get(i).getProductName() == null ?
                            "*印制电路板*线路板" :
                            deailList.get(i).getProductName(), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }

                }
                if (StringUtils.isNotBlank(outBoundData.getOldCustomerNumber()) && outBoundData.getOldCustomerNumber().equals("A503") && StringUtils.isNotBlank(deailList.get(i).getXsComId())
                    && deliveryAmount.compareTo(BigDecimal.valueOf(1000)) < 0)
                {
                    samNum = samNum + 1;
                    for (int j = 0; j < bodyList.size(); j++)
                    {
                        PdfPCell cellBodyTwo = null;
                        if (bodyList.get(j).getName().equals("deailNoFlag"))
                        {
                            sortNo += 1;
                            cellBodyTwo = new PdfPCell(new Phrase(sortNo.toString(), FontChinese12));
                            cellBodyTwo.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBodyTwo.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBodyTwo.setColspan(1);
                            cellBodyTwo.setFixedHeight(0);
                            cellBodyTwo.setMinimumHeight(30);
                            tableBody.addCell(cellBodyTwo);
                        }
                        else if (bodyList.get(j).getName().equals("deailCustNoFlag"))
                        {
                            cellBodyTwo = new PdfPCell(new Phrase((deailList.get(i).getCustomerPo() == null ?
                                "" :
                                deailList.get(i).getCustomerPo()), FontChinese12));
                            cellBodyTwo.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBodyTwo.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBodyTwo.setColspan(2);
                            cellBodyTwo.setFixedHeight(0);
                            cellBodyTwo.setMinimumHeight(30);
                            tableBody.addCell(cellBodyTwo);
                        }
                        else if (bodyList.get(j).getName().equals("craftNoFlag"))
                        {
                            String craftNo = "";
                            if (StringUtils.isNotBlank(deailList.get(i).getNo()))
                            {
                                craftNo = deailList.get(i).getNo();
                            }
                            if (StringUtils.isNotBlank(deailList.get(i).getRemark()))
                            {
                                if (StringUtils.isBlank(craftNo))
                                {
                                    craftNo = deailList.get(i).getRemark();
                                }
                                else
                                {
                                    craftNo = craftNo + "(" + deailList.get(i).getRemark() + ")";
                                }
                            }
                            cellBodyTwo = new PdfPCell(new Phrase(craftNo, FontChinese12));
                            cellBodyTwo.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBodyTwo.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBodyTwo.setColspan(2);
                            cellBodyTwo.setFixedHeight(0);
                            cellBodyTwo.setMinimumHeight(30);
                            tableBody.addCell(cellBodyTwo);
                        }
                        else if (bodyList.get(j).getName().equals("deailMaterNoFlag"))
                        {
                            cellBodyTwo = new PdfPCell(new Phrase((deailList.get(i).getCustomerMaterialNo() == null ?
                                "" :
                                deailList.get(i).getCustomerMaterialNo()), FontChinese12));
                            cellBodyTwo.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBodyTwo.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBodyTwo.setColspan(2);
                            cellBodyTwo.setFixedHeight(0);
                            cellBodyTwo.setMinimumHeight(30);
                            tableBody.addCell(cellBodyTwo);
                        }
                        else if (bodyList.get(j).getName().equals("custModalFlag"))
                        {
                            String customerModel = "99030000000006(工程费)";
                            cellBodyTwo =
                                new PdfPCell(new Phrase((customerModel == null ? "" : customerModel), FontChinese12));
                            cellBodyTwo.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBodyTwo.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBodyTwo.setColspan(4);
                            cellBodyTwo.setFixedHeight(0);
                            cellBodyTwo.setMinimumHeight(30);
                            tableBody.addCell(cellBodyTwo);
                        }
                        else if (bodyList.get(j).getName().equals("qualitityFlag"))
                        {
                            cellBodyTwo = new PdfPCell(new Phrase("1", FontChinese12));
                            cellBodyTwo.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBodyTwo.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBodyTwo.setColspan(2);
                            cellBodyTwo.setFixedHeight(0);
                            cellBodyTwo.setMinimumHeight(30);
                            tableBody.addCell(cellBodyTwo);
                        }
                        else if (bodyList.get(j).getName().equals("doonateFlag"))
                        {
                            cellBodyTwo = new PdfPCell(new Phrase("0", FontChinese12));
                            cellBodyTwo.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBodyTwo.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBodyTwo.setColspan(2);
                            cellBodyTwo.setFixedHeight(0);
                            cellBodyTwo.setMinimumHeight(30);
                            tableBody.addCell(cellBodyTwo);
                        }
                        else if (bodyList.get(j).getName().equals("remarkFlag"))
                        {
                            String remark = deailList.get(i).getRemark();
                            if (lhFlag)
                            {
                                String customerModel = deailList.get(i).getCustomerModel();
                                if (customerModel.indexOf("/") >= 0)
                                {
                                    remark = customerModel.split("/")[0];
                                }
                            }
                            cellBodyTwo = new PdfPCell(new Phrase((remark == null ? "" : remark), FontChinese12));
                            cellBodyTwo.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBodyTwo.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBodyTwo.setColspan(4);
                            cellBodyTwo.setFixedHeight(0);
                            cellBodyTwo.setMinimumHeight(30);
                            tableBody.addCell(cellBodyTwo);
                        }
                        else if (bodyList.get(j).getName().equals("deliverySizeFlag"))
                        {
                            cellBodyTwo = new PdfPCell(new Phrase((deailList.get(i).getDeliverySize() == null ?
                                "" :
                                deailList.get(i).getDeliverySize()), FontChinese12));
                            cellBodyTwo.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBodyTwo.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBodyTwo.setColspan(2);
                            cellBodyTwo.setFixedHeight(0);
                            cellBodyTwo.setMinimumHeight(30);
                            tableBody.addCell(cellBodyTwo);
                        }
                        else if (bodyList.get(j).getName().equals("deailDateFlag"))
                        {
                            cellBodyTwo = new PdfPCell(new Phrase("半年", FontChinese12));
                            cellBodyTwo.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBodyTwo.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBodyTwo.setColspan(3);
                            cellBodyTwo.setFixedHeight(0);
                            cellBodyTwo.setMinimumHeight(30);
                            tableBody.addCell(cellBodyTwo);
                        }
                        else if (bodyList.get(j).getName().equals("deailProductNameFlag"))
                        {
                            cellBodyTwo = new PdfPCell(new Phrase((deailList.get(i).getProductName() == null ?
                                "" :
                                deailList.get(i).getProductName()), FontChinese12));
                            cellBodyTwo.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBodyTwo.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBodyTwo.setColspan(2);
                            cellBodyTwo.setFixedHeight(0);
                            cellBodyTwo.setMinimumHeight(30);
                            tableBody.addCell(cellBodyTwo);
                        }
                    }
                }
            }

            Integer raw = 5 - deailList.size() - samNum;
            if (raw > 0)
            {
                for (int i = 0; i < raw; i++)
                {
                    for (int j = 0; j < bodyList.size(); j++)
                    {
                        PdfPCell cellBody = null;
                        if (bodyList.get(j).getName().equals("deailNoFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(1);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("deailCustNoFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("craftNoFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("deailMaterNoFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("custModalFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(4);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("qualitityFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("doonateFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("remarkFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(4);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("deliverySizeFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("deailDateFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(3);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("deailProductNameFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                    }
                }
            }

            if (tableBody != null)
            {
                document.add(tableBody);
            }
        }

        if (StringUtils.isNotBlank(outBoundData.getBottomOneFlag()) && outBoundData.getBottomOneFlag().equals("1"))
        {
            PdfPTable tableBottom = new PdfPTable(3);
            tableBottom.setTotalWidth(510f);
            tableBottom.setLockedWidth(true);
            tableBottom.setHorizontalAlignment(Element.ALIGN_CENTER);
            tableBottom.getDefaultCell().setBorder(PdfPCell.NO_BORDER);

            PdfPCell cellBottom = new PdfPCell(new Phrase("", FontChinese12));
            cellBottom.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellBottom.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellBottom.setBorder(PdfPCell.NO_BORDER);
            cellBottom.setColspan(3);
            cellBottom.setFixedHeight(10);
            tableBottom.addCell(cellBottom);

            String name = "发货人：";
            if (StringUtils.isNotBlank(outBoundData.getBottomOneName()))
            {
                name = outBoundData.getBottomOneName();
            }
            if (StringUtils.isNotBlank(outBoundData.getUserName()))
            {
                name = name + outBoundData.getUserName();
            }
            else
            {
                name = name + UserUtils.getUser().getUserName();
            }
            cellBottom = new PdfPCell(new Phrase(name, FontChinese11));
            cellBottom.setBorder(PdfPCell.NO_BORDER);
            cellBottom.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellBottom.setVerticalAlignment(Element.ALIGN_CENTER);
            cellBottom.setColspan(2);
            cellBottom.setFixedHeight(30);
            tableBottom.addCell(cellBottom);

            name = "收货单位：";
            if (StringUtils.isNotBlank(outBoundData.getBottomTwoName()))
            {
                name = outBoundData.getBottomTwoName();
            }
            cellBottom = new PdfPCell(new Phrase(name, FontChinese11));
            cellBottom.setBorder(PdfPCell.NO_BORDER);
            cellBottom.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellBottom.setVerticalAlignment(Element.ALIGN_CENTER);
            cellBottom.setColspan(1);
            cellBottom.setFixedHeight(30);
            tableBottom.addCell(cellBottom);

            if (tableBottom != null)
            {
                document.add(tableBottom);
            }
        }
        Font FontChinese13 = new Font(chinese, 9);
        if (StringUtils.isNotBlank(outBoundData.getBottomTwoFlag()) && outBoundData.getBottomTwoFlag().equals("1"))
        {
            String name = "（一） 回单(白)Reurn receipt （二） 客户(蓝)Client（三）记账(绿)Finance（四）请款(黄)payment（五）存根(红)counteroil";
            if (StringUtils.isNotBlank(outBoundData.getBottomThreeName()))
            {
                name = outBoundData.getBottomThreeName();
            }
            Paragraph bottomPar = new Paragraph(name, FontChinese13);

            if (bottomPar != null)
            {
                bottomPar.setAlignment(Element.ALIGN_LEFT);
                document.add(bottomPar);
            }
        }

        document.close();
        output = page;
        pdf = new Pdf();
        pdf.setOutPut(output);
        pdf.setFileName("/delivery.pdf");
        return pdf;
    }

    public Pdf selectToPrint(Delivery delivery, String printStatus, HttpServletRequest request)
        throws DocumentException, IOException
    {
        delivery.setPrintStatus(printStatus);
        OutBound outBoundData = outBoundDao.loadDeliveryById(delivery);
        if (StringUtils.isBlank(outBoundData.getPrintTemId()))
        {
            // 使用默认模板
            outBoundData = outBoundDao.loadDeliveryByIdTwo(delivery);
        }
        List<OutBoundDeail> deailList = outBoundDao.getOutBoundDeailListTwo(outBoundData);
        Pdf pdf = null;
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        Rectangle rectPageSize = new Rectangle(PageSize.A4);
        Document document = new Document(rectPageSize, 30, 30, 6, 0);
        ByteArrayOutputStream page = new ByteArrayOutputStream();
        @SuppressWarnings("unused") PdfWriter writer = PdfWriter.getInstance(document, page);
        document.open();
        BaseFont chinese =
            BaseFont.createFont("/font/simsun.ttc" + ",1", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED); // 系统默认中文（宋体）
        Font FontChinese1 = new Font(chinese, 18);
        Font FontChinese11 = new Font(chinese, 10);
        Font FontChinese12 = new Font(chinese, 10);

        Company cp = new Company();
        if (outBoundData != null && StringUtils.isNotBlank(outBoundData.getBindErpComId()))
        {
            cp = companyDao.get(outBoundData.getBindErpComId());
        }
        else
        {
            cp = companyDao.get(UserUtils.getUser().getCompany().getRecordId());
        }
        outBoundData.setHeadLogoFlag("1");
        if (StringUtils.isNotBlank(outBoundData.getHeadLogoFlag()) && outBoundData.getHeadLogoFlag().equals("1"))
        {
            String path = null;
            if (StringUtils.isNotBlank(cp.getLogo()))
            {
                if (FileManageUtils.isLocal())
                {
                    cp.setLogo(FileManageUtils.getLocalOrOssUrlAsString(cp.getLogo()));
                }
                else
                {
                    cp.setLogo(FileManageUtils.getUrl(cp.getLogo()).toString());
                }
                path = cp.getLogo();
            }
            Image img = null;
            try
            {
                img = Image.getInstance(path);
            }
            catch (Exception e)
            {
                path = null;
            }
            img.scaleAbsolute(50, 50);// 直接设定显示尺寸
            // img.scalePercent(60);
            img.setAbsolutePosition(83, 790);
            document.add(img);
        }
        if (StringUtils.isNotBlank(outBoundData.getHeadNameFlag()) && outBoundData.getHeadNameFlag().equals("1"))
        {
            Paragraph par = null;
            String headerNameJoin = (cp.getName() == null ? "" : cp.getName());
            String headerName = String.join(" ", headerNameJoin.split(""));
            par = new Paragraph(headerName, FontChinese1);
            if (par != null)
            {
                par.setAlignment(Element.ALIGN_CENTER);
                document.add(par);
            }
        }
        if (StringUtils.isNotBlank(outBoundData.getHeadShortNameFlag()) && outBoundData.getHeadShortNameFlag()
            .equals("1"))
        {
            Paragraph par = null;
            String headerNameJoin = (cp.getShortName() == null ? "" : cp.getShortName());
            String headerName = String.join("  ", headerNameJoin.split(""));
            par = new Paragraph(headerName, FontChinese1);
            if (par != null)
            {
                par.setAlignment(Element.ALIGN_CENTER);
                document.add(par);
            }
        }
        if (StringUtils.isNotBlank(outBoundData.getHeadNameEnFlag()) && outBoundData.getHeadNameEnFlag().equals("1"))
        {
            Paragraph par11 = new Paragraph((cp.getNameEn() == null ? "" : cp.getNameEn()), FontChinese11);
            if (par11 != null)
            {
                par11.setAlignment(Element.ALIGN_CENTER);
                document.add(par11);
            }
        }
        if (StringUtils.isNotBlank(outBoundData.getHeadAddressFlag()) && outBoundData.getHeadAddressFlag().equals("1"))
        {
            Paragraph par12 =
                new Paragraph("Add:" + (cp.getAddress() == null ? "" : cp.getAddress()) + "\n", FontChinese12);
            if (StringUtils.isNotBlank(outBoundData.getHeadPhoneFlag()) && outBoundData.getHeadPhoneFlag().equals("1"))
            {
                par12.add("Tel:" + (cp.getPhone() == null ? "" : cp.getPhone()) + "     ");
            }
            if (StringUtils.isNotBlank(outBoundData.getHeadFaxFlag()) && outBoundData.getHeadFaxFlag().equals("1"))
            {
                par12.add("Fax:" + (cp.getFax() == null ? "" : cp.getFax()) + "\n");
            }
            if (StringUtils.isNotBlank(outBoundData.getHeadWebEmailFlag()) && outBoundData.getHeadWebEmailFlag()
                .equals("1"))
            {
                par12.add((cp.getWebsite() == null ? "" : cp.getWebsite()) + "     e-mail:" + (cp.getEmail() == null ?
                    "" :
                    cp.getEmail()) + "\n");
            }
            if (par12 != null)
            {
                par12.setAlignment(Element.ALIGN_CENTER);
                document.add(par12);
            }
        }
        // Paragraph par13 = new Paragraph("DRLIVERY NOTE 送貨單" + "\n", FontChinese1);
        // if (par13 != null)
        // {
        // par13.setAlignment(Element.ALIGN_CENTER);
        // document.add(par13);
        // }
        if (StringUtils.isNotBlank(outBoundData.getCustNameFlag()) && outBoundData.getCustNameFlag().equals("1"))
        {
            PdfPTable tableHeader = new PdfPTable(7);
            tableHeader.setTotalWidth(520f);
            tableHeader.setLockedWidth(true);
            tableHeader.setHorizontalAlignment(Element.ALIGN_CENTER);
            tableHeader.getDefaultCell().setBorder(PdfPCell.NO_BORDER);

            PdfPCell cellHeader = new PdfPCell(new Phrase("", FontChinese12));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setBorder(PdfPCell.NO_BORDER);
            cellHeader.setColspan(7);
            cellHeader.setFixedHeight(5);
            tableHeader.addCell(cellHeader);

            String name = "客户名称 NAME";
            if (StringUtils.isNotBlank(outBoundData.getCustName()))
            {
                name = outBoundData.getCustName();
            }
            cellHeader = new PdfPCell(new Phrase(name, FontChinese12));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(1);
            cellHeader.setFixedHeight(15);
            tableHeader.addCell(cellHeader);

            cellHeader =
                new PdfPCell(new Phrase((outBoundData.getSaleCustName() == null ? "" : outBoundData.getSaleCustName()),
                    FontChinese12));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(3);
            cellHeader.setFixedHeight(15);
            tableHeader.addCell(cellHeader);

            name = "送货单号";
            if (StringUtils.isNotBlank(outBoundData.getNoStr()))
            {
                name = outBoundData.getNoStr();
            }
            cellHeader = new PdfPCell(new Phrase(name, FontChinese12));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(1);
            cellHeader.setFixedHeight(15);
            tableHeader.addCell(cellHeader);

            cellHeader =
                new PdfPCell(new Phrase((outBoundData.getNo() == null ? "" : outBoundData.getNo()), FontChinese12));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(2);
            cellHeader.setFixedHeight(15);
            tableHeader.addCell(cellHeader);

            name = "联系人 LINKMAN";
            if (StringUtils.isNotBlank(outBoundData.getLinkManStr()))
            {
                name = outBoundData.getLinkManStr();
            }
            cellHeader = new PdfPCell(new Phrase(name, FontChinese12));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(1);
            cellHeader.setFixedHeight(15);
            tableHeader.addCell(cellHeader);

            cellHeader =
                new PdfPCell(new Phrase((outBoundData.getName() == null ? "" : outBoundData.getName()), FontChinese12));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setBorder(PdfPCell.NO_BORDER);
            cellHeader.setColspan(1);
            cellHeader.setFixedHeight(15);
            tableHeader.addCell(cellHeader);

            cellHeader =
                new PdfPCell(new Phrase("电话：" + (outBoundData.getPhone() == null ? "" : outBoundData.getPhone()),
                    FontChinese12));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setBorder(PdfPCell.NO_BORDER);
            cellHeader.setColspan(2);
            cellHeader.setFixedHeight(15);
            tableHeader.addCell(cellHeader);

            name = "送货日期";
            if (StringUtils.isNotBlank(outBoundData.getDateName()))
            {
                name = outBoundData.getDateName();
            }
            cellHeader = new PdfPCell(new Phrase(name, FontChinese12));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(1);
            cellHeader.setFixedHeight(15);
            tableHeader.addCell(cellHeader);

            cellHeader = new PdfPCell(new Phrase((outBoundData.getCreateDateStr() == null ?
                "" :
                outBoundData.getCreateDateStr()), FontChinese12));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(2);
            cellHeader.setFixedHeight(15);
            tableHeader.addCell(cellHeader);

            name = "客户地址 ADD";
            if (StringUtils.isNotBlank(outBoundData.getCustAdd()))
            {
                name = outBoundData.getCustAdd();
            }
            cellHeader = new PdfPCell(new Phrase(name, FontChinese12));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(1);
            cellHeader.setFixedHeight(15);
            tableHeader.addCell(cellHeader);

            cellHeader = new PdfPCell(new Phrase((outBoundData.getAddress() == null ? "" : outBoundData.getAddress()),
                FontChinese12));
            cellHeader.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellHeader.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellHeader.setColspan(6);
            cellHeader.setFixedHeight(15);
            tableHeader.addCell(cellHeader);

            if (tableHeader != null)
            {
                document.add(tableHeader);
            }
        }

        List<CustPrintKey> bodyList = new ArrayList<CustPrintKey>();
        Integer num = 0;
        if (StringUtils.isNotBlank(outBoundData.getDeailNoFlag()) && outBoundData.getDeailNoFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("deailNoFlag");
            key.setSortNum(outBoundData.getDeailNoSortNum());
            bodyList.add(key);
            num = num + 1;
        }
        if (StringUtils.isNotBlank(outBoundData.getDeailCustNoFlag()) && outBoundData.getDeailCustNoFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("deailCustNoFlag");
            key.setSortNum(outBoundData.getDeailCustSortNum());
            bodyList.add(key);
            num = num + 2;
        }
        if (StringUtils.isNotBlank(outBoundData.getCraftNoFlag()) && outBoundData.getCraftNoFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("craftNoFlag");
            key.setSortNum(outBoundData.getCraftNoSortNum());
            bodyList.add(key);
            num = num + 2;
        }
        if (StringUtils.isNotBlank(outBoundData.getDeailMaterNoFlag()) && outBoundData.getDeailMaterNoFlag()
            .equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("deailMaterNoFlag");
            key.setSortNum(outBoundData.getDeailMaterNoSortNum());
            bodyList.add(key);
            num = num + 2;
        }
        if (StringUtils.isNotBlank(outBoundData.getCustModalFlag()) && outBoundData.getCustModalFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("custModalFlag");
            key.setSortNum(outBoundData.getCustModalSortNum());
            bodyList.add(key);
            num = num + 4;
        }
        if (StringUtils.isNotBlank(outBoundData.getQualitityFlag()) && outBoundData.getQualitityFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("qualitityFlag");
            key.setSortNum(outBoundData.getQualititySortNum());
            bodyList.add(key);
            num = num + 2;
        }
        if (StringUtils.isNotBlank(outBoundData.getDoonateFlag()) && outBoundData.getDoonateFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("doonateFlag");
            key.setSortNum(outBoundData.getDoonateSortNum());
            bodyList.add(key);
            num = num + 2;
        }
        if (StringUtils.isNotBlank(outBoundData.getRemarkFlag()) && outBoundData.getRemarkFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("remarkFlag");
            key.setSortNum(outBoundData.getRemarkSortNum());
            bodyList.add(key);
            num = num + 4;
        }
        if (StringUtils.isNotBlank(outBoundData.getDeliverySizeFlag()) && outBoundData.getDeliverySizeFlag()
            .equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("deliverySizeFlag");
            key.setSortNum(outBoundData.getDeliverySizeSortNum());
            bodyList.add(key);
            num = num + 2;
        }
        if (StringUtils.isNotBlank(outBoundData.getDeailDateFlag()) && outBoundData.getDeailDateFlag().equals("1"))
        {
            CustPrintKey key = new CustPrintKey();
            key.setName("deailDateFlag");
            key.setSortNum(outBoundData.getDeailDateSortNum());
            bodyList.add(key);
            num = num + 3;
        }
        bodyList.sort((x, y) -> Integer.compare(StringUtils.isBlank(x.getSortNum()) ?
                0 :
                Integer.valueOf(x.getSortNum()),
            StringUtils.isBlank(y.getSortNum()) ? 0 : Integer.valueOf(y.getSortNum())));
        if (bodyList != null && bodyList.size() > 0)
        {
            PdfPTable tableBody = new PdfPTable(num);
            tableBody.setTotalWidth(520f);
            tableBody.setLockedWidth(true);
            tableBody.setHorizontalAlignment(Element.ALIGN_CENTER);
            tableBody.getDefaultCell().setBorder(PdfPCell.NO_BORDER);

            for (int i = 0; i < bodyList.size(); i++)
            {
                PdfPCell cellBody = null;
                if (bodyList.get(i).getName().equals("deailNoFlag"))
                {
                    String name = "序号";
                    if (StringUtils.isNotBlank(outBoundData.getDeailNo()))
                    {
                        name = outBoundData.getDeailNo();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese12));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(1);
                    cellBody.setFixedHeight(15);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("deailCustNoFlag"))
                {
                    String name = "订单号";
                    if (StringUtils.isNotBlank(outBoundData.getDeailCustNo()))
                    {
                        name = outBoundData.getDeailCustNo();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese12));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(15);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("craftNoFlag"))
                {
                    String name = "厂编";
                    if (StringUtils.isNotBlank(outBoundData.getCraftNo()))
                    {
                        name = outBoundData.getCraftNo();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese12));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(15);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("deailMaterNoFlag"))
                {
                    String name = "客户物料号";
                    if (StringUtils.isNotBlank(outBoundData.getDeailMaterNo()))
                    {
                        name = outBoundData.getDeailMaterNo();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese12));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(15);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("custModalFlag"))
                {
                    String name = "客户型号";
                    if (StringUtils.isNotBlank(outBoundData.getCustModal()))
                    {
                        name = outBoundData.getCustModal();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese12));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(4);
                    cellBody.setFixedHeight(15);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("qualitityFlag"))
                {
                    String name = "送货数量";
                    if (StringUtils.isNotBlank(outBoundData.getQualitity()))
                    {
                        name = outBoundData.getQualitity();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese12));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(15);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("doonateFlag"))
                {
                    String name = "备品数量";
                    if (StringUtils.isNotBlank(outBoundData.getDoonate()))
                    {
                        name = outBoundData.getDoonate();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese12));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(15);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("remarkFlag"))
                {
                    String name = "备注";
                    if (StringUtils.isNotBlank(outBoundData.getRemark()))
                    {
                        name = outBoundData.getRemark();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese12));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(4);
                    cellBody.setFixedHeight(15);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("deliverySizeFlag"))
                {
                    String name = "交货尺寸";
                    if (StringUtils.isNotBlank(outBoundData.getDeliverySize()))
                    {
                        name = outBoundData.getDeliverySize();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese12));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(2);
                    cellBody.setFixedHeight(15);
                    tableBody.addCell(cellBody);
                }
                else if (bodyList.get(i).getName().equals("deailDateFlag"))
                {
                    String name = "有效日期";
                    if (StringUtils.isNotBlank(outBoundData.getDeailDate()))
                    {
                        name = outBoundData.getDeailDate();
                    }
                    cellBody = new PdfPCell(new Phrase(name, FontChinese12));
                    cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    cellBody.setColspan(3);
                    cellBody.setFixedHeight(15);
                    tableBody.addCell(cellBody);
                }
            }
            // 根据出库id获取出库明细
            for (int i = 0; i < deailList.size(); i++)
            {
                for (int j = 0; j < bodyList.size(); j++)
                {
                    PdfPCell cellBody = null;
                    if (bodyList.get(j).getName().equals("deailNoFlag"))
                    {
                        Integer no = i + 1;
                        cellBody = new PdfPCell(new Phrase(no.toString(), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(1);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("deailCustNoFlag"))
                    {
                        cellBody = new PdfPCell(new Phrase((deailList.get(i).getCustomerPo() == null ?
                            "" :
                            deailList.get(i).getCustomerPo()), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("craftNoFlag"))
                    {
                        cellBody =
                            new PdfPCell(new Phrase((deailList.get(i).getNo() == null ? "" : deailList.get(i).getNo()),
                                FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("deailMaterNoFlag"))
                    {
                        cellBody = new PdfPCell(new Phrase((deailList.get(i).getCustomerMaterialNo() == null ?
                            "" :
                            deailList.get(i).getCustomerMaterialNo()), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("custModalFlag"))
                    {
                        cellBody = new PdfPCell(new Phrase((deailList.get(i).getCustomerModel() == null ?
                            "" :
                            deailList.get(i).getCustomerModel()), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(4);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("qualitityFlag"))
                    {
                        BigDecimal quatity = deailList.get(i).getQuantity();
                        if (quatity == null)
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                        }
                        else
                        {
                            cellBody = new PdfPCell(new Phrase(quatity.toString(), FontChinese12));
                        }
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("doonateFlag"))
                    {
                        Integer quatity = deailList.get(i).getDonateStocks();
                        if (quatity == null)
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                        }
                        else
                        {
                            cellBody = new PdfPCell(new Phrase(quatity.toString(), FontChinese12));
                        }
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("remarkFlag"))
                    {
                        cellBody = new PdfPCell(new Phrase((deailList.get(i).getRemark() == null ?
                            "" :
                            deailList.get(i).getRemark()), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(4);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("deliverySizeFlag"))
                    {
                        cellBody = new PdfPCell(new Phrase((deailList.get(i).getDeliverySize() == null ?
                            "" :
                            deailList.get(i).getDeliverySize()), FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(2);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                    else if (bodyList.get(j).getName().equals("deailDateFlag"))
                    {
                        cellBody = new PdfPCell(new Phrase("半年", FontChinese12));
                        cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                        cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        cellBody.setColspan(3);
                        cellBody.setFixedHeight(0);
                        cellBody.setMinimumHeight(30);
                        tableBody.addCell(cellBody);
                    }
                }
            }

            Integer raw = 5 - deailList.size();
            if (raw > 0)
            {
                for (int i = 0; i < raw; i++)
                {
                    for (int j = 0; j < bodyList.size(); j++)
                    {
                        PdfPCell cellBody = null;
                        if (bodyList.get(j).getName().equals("deailNoFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(1);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("deailCustNoFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("craftNoFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("deailMaterNoFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("custModalFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(4);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("qualitityFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("doonateFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("remarkFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(4);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("deliverySizeFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(2);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                        else if (bodyList.get(j).getName().equals("deailDateFlag"))
                        {
                            cellBody = new PdfPCell(new Phrase("", FontChinese12));
                            cellBody.setHorizontalAlignment(Element.ALIGN_CENTER);
                            cellBody.setVerticalAlignment(Element.ALIGN_MIDDLE);
                            cellBody.setColspan(3);
                            cellBody.setFixedHeight(30);
                            tableBody.addCell(cellBody);
                        }
                    }
                }
            }

            if (tableBody != null)
            {
                document.add(tableBody);
            }
        }

        if (StringUtils.isNotBlank(outBoundData.getBottomOneFlag()) && outBoundData.getBottomOneFlag().equals("1"))
        {
            PdfPTable tableBottom = new PdfPTable(3);
            tableBottom.setTotalWidth(510f);
            tableBottom.setLockedWidth(true);
            tableBottom.setHorizontalAlignment(Element.ALIGN_CENTER);
            tableBottom.getDefaultCell().setBorder(PdfPCell.NO_BORDER);

            PdfPCell cellBottom = new PdfPCell(new Phrase("", FontChinese12));
            cellBottom.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellBottom.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cellBottom.setBorder(PdfPCell.NO_BORDER);
            cellBottom.setColspan(3);
            cellBottom.setFixedHeight(10);
            tableBottom.addCell(cellBottom);

            String name = "发货人：";
            if (StringUtils.isNotBlank(outBoundData.getBottomOneName()))
            {
                name = outBoundData.getBottomOneName();
            }
            cellBottom = new PdfPCell(new Phrase(name, FontChinese11));
            cellBottom.setBorder(PdfPCell.NO_BORDER);
            cellBottom.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellBottom.setVerticalAlignment(Element.ALIGN_CENTER);
            cellBottom.setColspan(2);
            cellBottom.setFixedHeight(30);
            tableBottom.addCell(cellBottom);

            name = "收货单位：";
            if (StringUtils.isNotBlank(outBoundData.getBottomTwoName()))
            {
                name = outBoundData.getBottomTwoName();
            }
            cellBottom = new PdfPCell(new Phrase(name, FontChinese11));
            cellBottom.setBorder(PdfPCell.NO_BORDER);
            cellBottom.setHorizontalAlignment(Element.ALIGN_LEFT);
            cellBottom.setVerticalAlignment(Element.ALIGN_CENTER);
            cellBottom.setColspan(1);
            cellBottom.setFixedHeight(30);
            tableBottom.addCell(cellBottom);

            if (tableBottom != null)
            {
                document.add(tableBottom);
            }
        }
        Font FontChinese13 = new Font(chinese, 9);
        if (StringUtils.isNotBlank(outBoundData.getBottomTwoFlag()) && outBoundData.getBottomTwoFlag().equals("1"))
        {
            String name = "（一） 回单(白)Reurn receipt （二） 客户(蓝)Client（三）记账(绿)Finance（四）请款(黄)payment（五）存根(红)counteroil";
            if (StringUtils.isNotBlank(outBoundData.getBottomThreeName()))
            {
                name = outBoundData.getBottomThreeName();
            }
            Paragraph bottomPar = new Paragraph(name, FontChinese13);

            if (bottomPar != null)
            {
                bottomPar.setAlignment(Element.ALIGN_LEFT);
                document.add(bottomPar);
            }
        }

/*        outBoundData.setHeadLogoFlag("1");
        if (StringUtils.isNotBlank(outBoundData.getHeadLogoFlag()) && outBoundData.getHeadLogoFlag().equals("1"))
        {
            String path = null;
            if (StringUtils.isNotBlank(cp.getLogo()))
            {
                if (FileManageUtils.isLocal())
                {
                    cp.setLogo(FileManageUtils.getLocalOrOssUrlAsString(cp.getLogo()));
                }
                else
                {
                    cp.setLogo(FileManageUtils.getUrl(cp.getLogo()).toString());
                }
                path = cp.getLogo();
            }
            Image img = null;
            try
            {
                img = Image.getInstance(path);
            }
            catch (Exception e)
            {
                path = null;
            }
            // img.scaleAbsolute(50, 50);// 直接设定显示尺寸
            img.scalePercent(60);
            img.setAbsolutePosition(83, 790);
            document.add(img);
        }*/

        document.close();
        output = page;
        pdf = new Pdf();
        pdf.setOutPut(output);
        pdf.setFileName("/delivery.pdf");
        return pdf;
    }

    public List<CustPrintSet> getPrintTemplate(CustPrintSet custPrintSet)
    {
        custPrintSet.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        List<CustPrintSet> list = customerDao.getPrintTemplate(custPrintSet);
        return list;
    }

    @Transactional(readOnly = false)
    public String setPrintTemplate(CustPrintSet custPrintSet)
    {
        if (!(StringUtils.isNotBlank(custPrintSet.getPrintStatus()) && custPrintSet.getPrintStatus().equals("2")))
        {
            custPrintSet.setPrintStatus(null);
        }
        // 验证模板名字是不是重复
        custPrintSet.setRecordId(null);
        Integer count = customerDao.getPrintTemCount(custPrintSet);
        if (count != null && count > 0)
        {
            return "fail";
        }
        custPrintSet.setTempalteFlag(1);
        custPrintSet.setCustomerId(null);
        customerDao.insertApplyPrint(custPrintSet);
        return "success";
    }

    @Transactional(readOnly = false)
    public String applyPrint(CustPrintSet custPrintSet)
    {
        if (!(StringUtils.isNotBlank(custPrintSet.getPrintStatus()) && custPrintSet.getPrintStatus().equals("2")))
        {
            custPrintSet.setPrintStatus(null);
        }
        // 根据id清除掉所有应用的集合
        customerDao.clearApplyPrint(custPrintSet);
        String[] customerIds = custPrintSet.getCustomerIds().split(",");
        List<CustPrintSet> printList = new ArrayList<CustPrintSet>();
        for (int i = 0; i < customerIds.length; i++)
        {
            CustPrintSet cuPrintSet = custPrintSet.clone();
            cuPrintSet.setTempalteFlag(null);
            cuPrintSet.setCustomerId(customerIds[i]);
            cuPrintSet.setUseTemplateId(custPrintSet.getRecordId());
            printList.add(cuPrintSet);
        }
        custPrintSet.setPrintList(printList);
        custPrintSet.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        custPrintSet.preInsert();
        // 对这些id进行数据插入批量插入
        customerDao.batchApplyPrint(custPrintSet);
        return "success";
    }

    @Transactional(readOnly = false)
    public String savePrintSet(CustPrintSet custPrintSet)
    {
        if (!(StringUtils.isNotBlank(custPrintSet.getPrintStatus()) && custPrintSet.getPrintStatus().equals("2")))
        {
            custPrintSet.setPrintStatus(null);
        }
        if (StringUtils.isNotBlank(custPrintSet.getTemplateName()) && null == custPrintSet.getTempalteFlag())
        {
            custPrintSet.setTempalteFlag(1);
        }
        custPrintSet.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        if (StringUtils.isNotBlank(custPrintSet.getRecordId()))
        {
            customerDao.updateApplyPrint(custPrintSet);
        }
        else
        {
            customerDao.insertApplyPrint(custPrintSet);
        }
        return "success";
    }

    public List<Customer> getCustomerList(Customer customer)
    {
        customer.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        return customerDao.getCustomerList(customer);
    }

    public List<Customer> getAllCustomerList(Customer customer)
    {
        if (StringUtils.isNotBlank(customer.getPrintStatus()) && customer.getPrintStatus().equals("2"))
        {
            customer.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        }
        else if (StringUtils.isNotBlank(customer.getPrintStatus()) && customer.getPrintStatus().equals("3"))
        {
            customer.setCompanyId(CompanyUtil.getInstance().getSaleIds());
        }
        else
        {
            customer.setCompanyId(CompanyUtil.getInstance().getFactSaleIds());
        }
        return customerDao.getAllCustomerList(customer);
    }

    public CustPrintSet getPrintTemplateByCust(Customer customer)
    {
        customer.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        return customerDao.getPrintTemplateByCust(customer);
    }

    @Transactional(readOnly = false)
    public String delPrintTemp(CustPrintSet custPrintSet)
    {
        customerDao.delPrintTemp(custPrintSet);
        return "success";
    }

    public List<CustomerContact> getCustomerConcatList(Customer customer)
    {
        return customerDao.getCustomerConcatList(customer);
    }

    public List<CustPrintSet> getPrintTemplateTwo(CustPrintSet custPrintSet)
    {
        custPrintSet.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        List<CustPrintSet> list = customerDao.getPrintTemplateTwo(custPrintSet);
        return list;
    }

    @Transactional(readOnly = false)
    public void updateFileUrl(Customer customer)
    {
        customerDao.updateFileUrl(customer);
    }

    @Transactional(readOnly = false)
    public void batchUpdateCheckBilDate(ParameterSet parameterSet)
    {
        customerDao.batchUpdateCheckBilDate(parameterSet);
    }

    public Map<String, List<?>> selectCustSaleForm(Customer customer)
    {
        customer.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        Map<String, List<?>> map = new HashMap<>();
        List<Customer> customerShareList = customerDao.getCustomerShareList(customer);
        List<Employee> saleManShareList = employeeDao.getEmployeeShareList(customer);
        map.put("customerShareList", customerShareList);
        map.put("saleManShareList", saleManShareList);
        return map;
    }

    @Transactional(readOnly = false)
    public String changeCustType(Customer customer)
    {
        customerDao.changeCustType(customer);
        return "success";
    }

    @Transactional(readOnly = false)
    public String changeSaleMan(Customer customer)
    {
        if (customer != null && customer.getSalesman() != null && StringUtils.isNotBlank(customer.getSalesman()
            .getRecordId()))
        {
            customerDao.changeSaleMan(customer);
            return "success";
        }
        return "fail";
    }

    @Transactional(readOnly = false)
    public String saveDistributionCus(Customer customer)
    {
        List<String> list = Lists.newArrayList();
        ShareDataUtil shareDataUtil = new ShareDataUtil();
        Company company = UserUtils.getUser().getCompany();
        Company disCompany = customer.getDisCompany();
        list.add(company.getRecordId());
        list.add(disCompany.getRecordId());
        shareDataUtil.saveCustomer(customer, 1, list);
        return "success";
    }

    @Transactional(readOnly = false)
    public String batchDistributionCus(List<Customer> customerList)
    {
        if (Collections3.isEmpty(customerList) || null == customerList.get(0).getDisCompany())
        {
            return "fail";
        }
        List<String> list = Lists.newArrayList();
        Company company = UserUtils.getUser().getCompany();
        Company disCompany = customerList.get(0).getDisCompany();
        list.add(company.getRecordId());
        list.add(disCompany.getRecordId());
        for (Customer customer : customerList)
        {
            ShareDataUtil shareDataUtil = new ShareDataUtil();
            shareDataUtil.saveCustomer(customer, 1, list);
        }
        return "success";
    }

    public Page<CustomerSalesman> findCustomerSalesManPage(Page<CustomerSalesman> page,
        CustomerSalesman customerSalesman)
    {
        // 获取当前登录人
        customerSalesman.setPage(page);
        List<CustomerSalesman> list = customerSalesmanDao.findList(customerSalesman);
        page.setList(list);
        return page;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> saveCustomerSalesMan(List<CustomerSalesman> list)
    {
        Map<String, Object> data = new HashMap<>();
        if (Collections3.isEmpty(list))
        {
            data.put("result", "fail");
            data.put("message", "参数失效，请刷新重试!");
            return data;
        }
        for (CustomerSalesman customerSalesman : list)
        {
            if (null == customerSalesman)
            {
                continue;
            }
            if (customerSalesman.getIsNewRecord())
            {
                customerSalesman.setCompany(UserUtils.getUser().getCompany());
                customerSalesman.setStatus(String.valueOf(TypeKey.MD_CUSTOMER_SALESMAN_NOTPASS));
                customerSalesman.preInsert();
                customerSalesmanDao.insert(customerSalesman);
            }
            else
            {
                customerSalesman.setStatus(String.valueOf(TypeKey.MD_CUSTOMER_SALESMAN_NOTPASS));
                customerSalesman.preUpdate();
                customerSalesmanDao.update(customerSalesman);
            }

            /*// 更新结束时间
            customerSalesmanDao.updateEndDate(customerSalesman);

            // 获取最新的用户和客户类型
            CustomerSalesman newData = customerSalesmanDao.getNewData(customerSalesman);
            if (null != newData)
            {
                // 更新业务员
                Customer cus = new Customer();
                cus.setRecordId(customerSalesman.getCustomerId());
                cus.setSalesman(new User(newData.getUserId()));
                cus.setCustType(newData.getCustType());
                if (null != newData.getDeptId() && StringUtils.isNotBlank(newData.getDeptId()))
                {
                    cus.setDeptId(newData.getDeptId());
                }
                else
                {
                    cus.setDeptId(customerSalesman.getDeptId());
                }
                customerDao.updateSaleMan(cus);
            }*/
        }
        data.put("result", "success");
        data.put("message", "保存成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> deleteCustomerSalesMan(List<CustomerSalesman> list)
    {
        Map<String, Object> data = new HashMap<>();
        if (Collections3.isEmpty(list))
        {
            data.put("result", "fail");
            data.put("message", "参数失效，请刷新重试!");
            return data;
        }
        for (CustomerSalesman customerSalesman : list)
        {
            customerSalesman.preUpdate();
            customerSalesmanDao.delete(customerSalesman);

            // 更新结束时间
            customerSalesmanDao.updateEndDate(customerSalesman);

            // 获取最新的用户和客户类型
            CustomerSalesman newData = customerSalesmanDao.getNewData(customerSalesman);
            if (null != newData)
            {
                // 更新业务员
                Customer cus = new Customer();
                cus.setRecordId(customerSalesman.getCustomerId());
                cus.setSalesman(new User(newData.getUserId()));
                cus.setCustType(newData.getCustType());
                customerDao.updateSaleMan(cus);
            }
        }
        data.put("result", "success");
        data.put("message", "删除成功!");
        return data;
    }

    public List<Customer> getCsutomerList(Company company)
    {
        Customer cus = new Customer();
        cus.setCompanyId(company.getRecordId());
        List<Customer> customerList = customerDao.getCustomerList(cus);
        return customerList;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> orderSalesManInit(CustomerSalesman customerSalesman)
    {
        Map<String, Object> data = new HashMap<>();
        if (null == customerSalesman)
        {
            data.put("result", "fail");
            data.put("message", "参数失效，请刷新重试!");
            return data;
        }
        customerSalesman.setCompany(UserUtils.getUser().getCompany());
        customerSalesmanDao.updateOrderUserId(customerSalesman);
        data.put("result", "success");
        data.put("message", "订单业务员重置成功!");
        return data;
    }

    public void setCusSalesManDataList(ExportExcel excel, List<CustomerSalesman> list, String[] hearList)
    {
        for (CustomerSalesman cs : list)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList)
            {
                String val = "";
                switch (name)
                {
                    case "客户编号":
                        String no = null;
                        if (null != cs.getCustomer())
                        {
                            no = cs.getCustomer().getNo();
                        }
                        val = no;
                        break;
                    case "客户名称":
                        String shortName = null;
                        if (null != cs.getCustomer())
                        {
                            shortName = cs.getCustomer().getShortName();
                        }
                        val = shortName;
                        break;
                    case "客户类型":
                        val = cs.getCustTypeStr();
                        break;
                    case "业务员名称":
                        String userName = null;
                        if (null != cs.getUser())
                        {
                            userName = cs.getUser().getUserName();
                        }
                        val = userName;
                        break;
                    case "开始日期":
                        val = cs.getStartDateStr();
                        break;
                    case "结束日期":
                        val = cs.getEndDateStr();
                        break;
                    case "创建人":
                        userName = null;
                        if (null != cs.getCreatedBy())
                        {
                            userName = cs.getCreatedBy().getUserName();
                        }
                        val = userName;
                        break;
                    case "创建时间":
                        val = null == cs.getCreatedDate() ? "" : DateUtils.formatDate(cs.getCreatedDate());
                        break;
                    case "归属部门":
                        val = cs.getDeptName();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    public Page<CustomerRemarks> customersale(Page<CustomerRemarks> page, CustomerRemarks customerRemarks)
    {
        customerRemarks.setPage(page);
        List<CustomerRemarks> list = customerSalesmanDao.customersale(customerRemarks);
        page.setList(list);
        return page;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> delTemplateOpen(List<CustomerRemarks> list)
    {
        Map<String, Object> data = new HashMap<>();
        if (Collections3.isEmpty(list)){
            data.put("result", "fail");
            data.put("message", "参数失效，请刷新重试!");
            return data;
        }
        for (CustomerRemarks customerRemarks : list){
            customerRemarks.preUpdate();
            customerSalesmanDao.deleteRemarks(customerRemarks);
        }
        data.put("result", "success");
        data.put("message", "删除成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> addRemarksOpen(List<CustomerRemarks> list) {
        Map<String, Object> data = new HashMap<>();
        if (Collections3.isEmpty(list)){
            data.put("result", "fail");
            data.put("message", "参数失败请重试!");
        }
        for (CustomerRemarks customerRemarks : list)
        {
            if (null == customerRemarks){
                continue;
            }
            if (StringUtils.isBlank(customerRemarks.getRecordId()))
            {
                customerRemarks.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
                customerRemarks.preInsert();
                customerSalesmanDao.insertRemarks(customerRemarks);
            }
            else
            {
                customerRemarks.preUpdate();
                customerSalesmanDao.updateRemarks(customerRemarks);
            }
        }

        data.put("result", "success");
        data.put("message", "保存成功!");
        return data;
    }

    public Integer arriveDateExist(Customer customer){
        if (customer == null){
            return 0;
        }
        customer.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        Integer count = customerDao.getArrivePay(customer);
        Integer arriveStatus = customerDao.getArriveStatus(customer);
        if (count != null && count == 0 && arriveStatus != null && arriveStatus == 1){
            return 1;
        }
        return 0;
    }

    @Transactional(readOnly = false)
    public Map<String,Object> adjustArriveDate(@RequestBody Customer customer) throws ParseException {
        Map<String,Object> data = new HashMap<>();
        if (customer == null){
            data.put("result", "fail");
            data.put("message", "参数失败请重试!");
            return data;
        }
        Customer checkDateA= customerDao.getCheckDate(customer);
        String dateString = customer.getAdjustDate();
        SimpleDateFormat formatterA = new SimpleDateFormat("yyyy-MM-dd");
        String checkDate = checkDateA.getCheckDate();
        String period = customer.getPeriod();
        String year = period.substring(0,4);
        String month = period.substring(4, 6);
        if(checkDate.length() == 1){
            checkDate = "0" + checkDate;
        }
        String checkDateTotal = year+'-'+ month +'-'+ checkDate;
        String datetype  = dateString;
        Date datetime= formatterA.parse(datetype);
        int comparisonResult = checkDateTotal.compareTo(datetype);
        Integer checkDay = 0;
         if (comparisonResult < 0){
             datetype = DateUtils.getDateAfter(datetime, 1, 3);
             if(StringUtils.isNotBlank(checkDateA.getPayWayValue())){
                 switch (checkDateA.getPayWayValue()){
                     case "即时付款":
                         checkDay = 0;
                         break;
                     case "现金":
                         checkDay = 0;
                         break;
                     case "货到付款":
                         checkDay = 0;
                         break;
                     case "月结":
                         checkDay = checkDay + 30;
                         break;
                     case "月结30天":
                         checkDay = checkDay + 30;
                         break;
                     case "月结45天":
                         checkDay = checkDay + 45;
                         break;
                     case "月结60天":
                         checkDay = checkDay +60;
                         break;
                     case "月结90天":
                         checkDay = checkDay + 90;
                         break;
                     case "月结120天":
                         checkDay = checkDay + 120;
                         break;
                     case "票到60天":
                         checkDay = checkDay + 60;
                         break;
                     case "预付款":
                         checkDay = 0;
                         break;
                     case "票到月结30天":
                         checkDay = 0;
                         break;
                     case "单月回款":
                         checkDay = 0;
                         break;
                 }
             }
             Date singDate = formatterA.parse(datetype);
             datetype= DateUtils.getDateAfter(singDate,checkDay,1);
             customer.setAdjustDate(datetype);
             customerDao.updateSingDate(customer);
         }
        data.put("result", "success");
        data.put("message", "日期调整成功!");
        return data;
    }
    public Integer closeExist(Customer customer){
        if (customer == null){
            return 2;
        }
        Integer inCompanyCount = customerDao.getIngroupCompany(customer);
        if (inCompanyCount != null && inCompanyCount > 0){
            Integer count = customerDao.closeExist(customer);
            if (count != null && count > 0){
                return 1;
            }else{
                return 2;
            }
        }
        return 3;
    }

    public Customer getRejectResource(Customer customer)
    {
        Customer customer1 = customerDao.getRejectResource(customer);
        return customer1;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> definitiveLock(Customer customer) {
        Map<String, Object> data = new HashMap<>();
        if (null == customer) {
            data.put("result", "fail");
            data.put("message", "参数失效，请刷新重试!");
            return data;
        }
        customer.preUpdate();
        customerDao.updateLock(customer);
        data.put("result", "success");
        data.put("message", "锁单成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> definitiveLockUnlock(Customer customer) {
        Map<String, Object> data = new HashMap<>();
        if (null == customer) {
            data.put("result", "fail");
            data.put("message", "参数失效，请刷新重试!");
            return data;
        }
        customer.preUpdate();
        customerDao.updateLockUnlock(customer);
        data.put("result", "success");
        data.put("message", "取消锁单成功!");
        return data;
    }

    public List<CustomerProductType> selectProductType()
    {
        Company company = UserUtils.getUser().getCompany();
        CustomerProductType productType = new CustomerProductType();
        productType.setCompany(company);
        return customerProductTypeDao.getList(productType);
    }

    @Transactional(readOnly = false)
    public Map<String,Object> deleteProductType(List<CustomerProductType> list)
    {
        Map<String,Object> data = new HashMap<>();
        if(Collections3.isEmpty(list))
        {
            data.put("result","fail");
            data.put("message","参数异常!");
            return data;
        }
        customerProductTypeDao.batchDelete(list);
        data.put("result","success");
        data.put("message","删除成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String,Object> saveProductType(List<CustomerProductType> list)
    {
        Map<String,Object> data = new HashMap<>();
        if(Collections3.isEmpty(list))
        {
            data.put("result","fail");
            data.put("message","参数异常!");
            return data;
        }
        Company company = UserUtils.getUser().getCompany();
        List<CustomerProductType> insertList = Lists.newArrayList();
        List<CustomerProductType> updateList = Lists.newArrayList();
        for(CustomerProductType sw : list)
        {
            if(StringUtils.isNotBlank(sw.getRecordId()))
            {
                sw.preUpdate();
                updateList.add(sw);
            }
            else
            {
                sw.setCompany(company);
                sw.preInsert();
                insertList.add(sw);
            }
        }
        if(Collections3.isNotEmpty(insertList))
        {
            customerProductTypeDao.batchInsert(insertList);
        }
        if(Collections3.isNotEmpty(updateList))
        {
            customerProductTypeDao.batchUpdate(updateList);
        }
        data.put("result","success");
        data.put("message","保存成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> approvalCusSalesMan(List<CustomerSalesman> list)
    {
        Map<String, Object> data = new HashMap<>();
        if (Collections3.isEmpty(list))
        {
            data.put("result", "fail");
            data.put("message", "参数失效，请刷新重试!");
            return data;
        }
        // 如果客户没有业务员择不需要审批
        List<CustomerSalesman> listTwo = new ArrayList<>(); // 走审批的集合
        List<CustomerSalesman> newList = new ArrayList<>(); // 不走审批的集合
        List<Customer> appCustomerList = customerDao.getAppCustomerList(list);
        if (Collections3.isNotEmpty(appCustomerList))
        {
            Set<String> appCustomerIds = appCustomerList.stream().map(Customer::getRecordId).collect(Collectors.toSet());

            // 2. 遍历 list，根据 customerId 是否在 Set 中分类
            for (CustomerSalesman customerSalesman : list)
            {
                if (appCustomerIds.contains(customerSalesman.getCustomerId()))
                {
                    listTwo.add(customerSalesman); // 匹配到审批条件
                }
                else
                {
                    newList.add(customerSalesman); // 不匹配
                }
            }
        }
        else
        {
            // 如果 appCustomerList 为空，所有数据都不需要审批
            newList.addAll(list);
        }
        if (Collections3.isNotEmpty(newList))
        {
            for (CustomerSalesman salesman : newList)
            {
                salesman.setStatus(TypeKey.MD_CUSTOMER_SALESMAN_PASS.toString());
                customerSalesmanDao.updateStatus(salesman);
                customerSalesmanDao.updateEndDate(salesman);

                // 获取最新的用户和客户类型
                CustomerSalesman newData = customerSalesmanDao.getNewData(salesman);
                if (null != newData)
                {
                    // 更新业务员
                    Customer cus = new Customer();
                    cus.setRecordId(salesman.getCustomerId());
                    cus.setSalesman(new User(newData.getUserId()));
                    cus.setCustType(newData.getCustType());
                    if (null != newData.getDeptId() && StringUtils.isNotBlank(newData.getDeptId()))
                    {
                        cus.setDeptId(newData.getDeptId());
                    }
                    else
                    {
                        cus.setDeptId(salesman.getDeptId());
                    }
                    customerDao.updateSaleMan(cus);
                }
            }
        }
        if (Collections3.isNotEmpty(listTwo))
        {
            data = ModifyUtils.approvalCusSalesMan(listTwo);
            return data;
        }
        data.put("result", "success");
        data.put("message", "审批成功!");
        return data;
    }
}