package com.kyb.pcberp.modules.sys.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;

import java.util.Date;

/**
 * zjn 2018-09-26 
 * 部门或者组织关联员工实体
 */
@SuppressWarnings("serial")
public class RelationEmployee extends DataEntity<RelationEmployee>{

	/** 部门*/
	private Department dept;
	
	/** 组织*/
	private Organization organization;
	
	/** 员工*/
	private Employee employee;
	
	/** 1:部门,2:组织*/
	private Integer status;

	private String empName;

	private String userId;

	private String deptId;

	private Date startTime;

	private Date endTime;

	public Department getDept() {
		return dept;
	}

	public void setDept(Department dept) {
		this.dept = dept;
	}

	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}

	public Employee getEmployee() {
		return employee;
	}

	public void setEmployee(Employee employee) {
		this.employee = employee;
	}

    public Integer getStatus()
    {
        return status;
    }

    public void setStatus(Integer status)
    {
        this.status = status;
    }

	public String getEmpName() {
		return empName;
	}

	public void setEmpName(String empName) {
		this.empName = empName;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getDeptId() {
		return deptId;
	}

	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}

	public Date getStartTime()
	{
		return startTime;
	}

	public void setStartTime(Date startTime)
	{
		this.startTime = startTime;
	}

	public String getStartTimeStr()
	{
		if(null != startTime)
		{
			return DateUtils.formatDate(startTime);
		}
		return null;
	}

	public Date getEndTime()
	{
		return endTime;
	}

	public void setEndTime(Date endTime)
	{
		this.endTime = endTime;
	}

	public String getEndTimeStr()
	{
		if(null != endTime)
		{
			return DateUtils.formatDate(endTime);
		}
		return null;
	}
}
