package com.kyb.pcberp.modules.oa.pojo.common;

import com.kyb.pcberp.common.persistence.DataEntity;

// 工作台
@SuppressWarnings("serial")
public class Oa_work extends DataEntity<Oa_work>
{
    private String empId;
    
    private String content;
    
    private String status;
    
    private String type; // 1:日程安排,2:审批,3:报告,4:任务,5:通知公告
    
    public String getEmpId()
    {
        return empId;
    }
    
    public void setEmpId(String empId)
    {
        this.empId = empId;
    }
    
    public String getContent()
    {
        return content;
    }
    
    public void setContent(String content)
    {
        this.content = content;
    }
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public String getType()
    {
        return type;
    }
    
    public void setType(String type)
    {
        this.type = type;
    }
}
