package com.kyb.pcberp.modules.hr.depart_center.pojo;

import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.StringUtils;

@SuppressWarnings("serial")
public class Hr_KybAttachments extends DataEntity<Hr_KybAttachments>
{
    private String orgFileName;
    
    private String name;
    
    private String type;
    
    private String fileUrl;

    private String groupId;

    private String dailyId;

    private String realFileName;

    private String dayThingId;

    private String taskId;

    private String auditId;

    private String regimenId;
    
    public String getDownloadUrl()
    {
        if (StringUtils.isNotBlank(fileUrl))
        {
            return FileManageUtils.getUrl(fileUrl).toString();
        }
        return null;
    }
    
    public String getName()
    {
        return name;
    }
    
    public void setName(String name)
    {
        this.name = name;
    }
    
    public String getOrgFileName()
    {
        return orgFileName;
    }
    
    public void setOrgFileName(String orgFileName)
    {
        this.orgFileName = orgFileName;
    }
    
    public String getFileUrl()
    {
        return fileUrl;
    }
    
    public void setFileUrl(String fileUrl)
    {
        this.fileUrl = fileUrl;
    }
    
    public String getType()
    {
        return type;
    }
    
    public void setType(String type)
    {
        this.type = type;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getDailyId() {
        return dailyId;
    }

    public void setDailyId(String dailyId) {
        this.dailyId = dailyId;
    }

    public String getRealFileName() {
        return realFileName;
    }

    public void setRealFileName(String realFileName) {
        this.realFileName = realFileName;
    }

    public String getDayThingId() {
        return dayThingId;
    }

    public void setDayThingId(String dayThingId) {
        this.dayThingId = dayThingId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getAuditId() {
        return auditId;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

    public String getRegimenId() {
        return regimenId;
    }

    public void setRegimenId(String regimenId) {
        this.regimenId = regimenId;
    }
}
