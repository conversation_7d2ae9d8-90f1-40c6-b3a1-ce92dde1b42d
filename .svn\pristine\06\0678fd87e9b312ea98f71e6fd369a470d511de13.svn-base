/**
 * 
 */
package com.kyb.pcberp.modules.production.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;

/**
 * 生产投料
 * 
 * @version 2015-09-22
 */
public class FeedingVo
{
    private String notificationNo;// 通知单号
    
    private String no;// 投料单号
    
    private String status;//投料状态
    
    private String craftNo;// 生产编号
    
    private String customerModel;// 客户型号
    
    private Date deliveryDate;// 交货日期
    
    private Date distributeDate;// 发料日期
    
    private String orderQuantity;// 订单数量(PCS)
    
    private Long pcsQuantity;// 投料数量(PCS)
    
    private String boardAPnlQty;// 投料PNL数(A板)
    
    private BigDecimal pnlLengthA; // A板的pnl长度
    
    private BigDecimal pnlWidthA; // A板的pnl宽度
    
    private String boardBPnlQty;// 投料PNL数(B板)
    
    private BigDecimal pnlLengthB; // B板的pnl长度
    
    private BigDecimal pnlWidthB; // B板的pnl宽度
    
    private BigDecimal setWidth; // SET宽度，单位mm
    
    private BigDecimal setLength; // SET长度，单位mm 
    
    private Integer boardQuantity;// 大板数
    
    private Integer cardCount;// 流程卡张数
    
    private Integer occupiedAvailableQty;//占用库存数量
    
    private String userName;// 发料员
    
    private String remark;// 备注
    
    private String showPurStatus;
    
    private String purchDeailId;
    
    private Date purDate;
    
    private Integer replyStock;
    
    private String purStatus;
    
    @ExcelField(title = "通知单号", align = 2, sort = 30)
    public String getNotificationNo()
    {
        return notificationNo;
    }
    
    public void setNotificationNo(String notificationNo)
    {
        this.notificationNo = notificationNo;
    }
    
    @ExcelField(title = "投料单号", align = 2, sort = 60)
    public String getNo()
    {
        return no;
    }
    
    public void setNo(String no)
    {
        this.no = no;
    }
    
    @ExcelField(title = "投料状态", align = 2, sort = 90)
    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    @ExcelField(title = "生产编号", align = 2, sort = 100)
    public String getCraftNo()
    {
        return craftNo;
    }
    
    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }
    
    @ExcelField(title = "客户型号", align = 2, sort = 130)
    public String getCustomerModel()
    {
        return customerModel;
    }
    
    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }
    
    @ExcelField(title = "交货日期", align = 2, sort = 150)
    public Date getDeliveryDate()
    {
        return deliveryDate;
    }
    
    public void setDeliveryDate(Date deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }
    
    @ExcelField(title = "发料日期", align = 2, sort = 180)
    public Date getDistributeDate()
    {
        return distributeDate;
    }
    
    public void setDistributeDate(Date distributeDate)
    {
        this.distributeDate = distributeDate;
    }
    
    @ExcelField(title = "订单数量(PCS)", align = 2, sort = 200)
    public String getOrderQuantity()
    {
        return orderQuantity;
    }
    
    public void setOrderQuantity(String orderQuantity)
    {
        this.orderQuantity = orderQuantity;
    }
    
    @ExcelField(title = "投料数量(PCS)", align = 2, sort = 220)
    public Long getPcsQuantity()
    {
        return pcsQuantity;
    }
    
    public void setPcsQuantity(Long pcsQuantity)
    {
        this.pcsQuantity = pcsQuantity;
    }
    
    @ExcelField(title = "投料PNL数(A板)", align = 2, sort = 240)
    public String getBoardAPnlQty()
    {
        return boardAPnlQty;
    }
    
    public void setBoardAPnlQty(String boardAPnlQty)
    {
        this.boardAPnlQty = boardAPnlQty;
    }
    
    @ExcelField(title = "PNL长(A板)", align = 2, sort = 250)
    public BigDecimal getPnlLengthA() {
        return pnlLengthA;
    }

    public void setPnlLengthA(BigDecimal pnlLengthA) {
        this.pnlLengthA = pnlLengthA;
    }

    @ExcelField(title = "PNL宽(A板)", align = 2, sort = 260)
    public BigDecimal getPnlWidthA() {
        return pnlWidthA;
    }

    public void setPnlWidthA(BigDecimal pnlWidthA) {
        this.pnlWidthA = pnlWidthA;
    }

    @ExcelField(title = "投料PNL数(B板)", align = 2, sort = 270)
    public String getBoardBPnlQty()
    {
        return boardBPnlQty;
    }
    
    public void setBoardBPnlQty(String boardBPnlQty)
    {
        this.boardBPnlQty = boardBPnlQty;
    }
    
    @ExcelField(title = "PNL长(B板)", align = 2, sort = 280)
    public BigDecimal getPnlLengthB() {
        return pnlLengthB;
    }

    public void setPnlLengthB(BigDecimal pnlLengthB) {
        this.pnlLengthB = pnlLengthB;
    }

    @ExcelField(title = "PNL宽(B板)", align = 2, sort = 290)
    public BigDecimal getPnlWidthB() {
        return pnlWidthB;
    }
    
    public void setPnlWidthB(BigDecimal pnlWidthB) {
        this.pnlWidthB = pnlWidthB;
    }
    
    @ExcelField(title = "SET长", align = 2, sort = 300)
    public BigDecimal getSetLength() {
        return setLength;
    }

    public void setSetLength(BigDecimal setLength) {
        this.setLength = setLength;
    }
    
    @ExcelField(title = "SET宽", align = 2, sort = 310)
    public BigDecimal getSetWidth() {
        return setWidth;
    }

    public void setSetWidth(BigDecimal setWidth) {
        this.setWidth = setWidth;
    }
    
    @ExcelField(title = "大板数", align = 2, sort = 320)
    public Integer getBoardQuantity()
    {
        return boardQuantity;
    }
    
    public void setBoardQuantity(Integer boardQuantity)
    {
        this.boardQuantity = boardQuantity;
    }
    
    @ExcelField(title = "流程卡张数", align = 2, sort = 330)
    public Integer getCardCount()
    {
        return cardCount;
    }
    
    public void setCardCount(Integer cardCount)
    {
        this.cardCount = cardCount;
    }
    
    @ExcelField(title = "占可用库存数量", align = 2, sort = 340)
    public Integer getOccupiedAvailableQty()
    {
        return occupiedAvailableQty;
    }

    public void setOccupiedAvailableQty(Integer occupiedAvailableQty)
    {
        this.occupiedAvailableQty = occupiedAvailableQty;
    }
    
    @ExcelField(title = "发料员", align = 2, sort = 350)
    public String getUserName()
    {
        return userName;
    }
    
    public void setUserName(String userName)
    {
        this.userName = userName;
    }
    
    @ExcelField(title = "备注", align = 2, sort = 360)
    public String getRemark()
    {
        return remark;
    }
    
    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    @ExcelField(title = "采购状态", align = 2, sort = 370)
    public String getShowPurStatus()
    {
        return showPurStatus;
    }

    public void setShowPurStatus(String showPurStatus)
    {
        this.showPurStatus = showPurStatus;
    }

    public String getPurchDeailId()
    {
        return purchDeailId;
    }

    public void setPurchDeailId(String purchDeailId)
    {
        this.purchDeailId = purchDeailId;
    }

    public Date getPurDate()
    {
        return purDate;
    }

    public void setPurDate(Date purDate)
    {
        this.purDate = purDate;
    }

    public Integer getReplyStock()
    {
        return replyStock;
    }

    public void setReplyStock(Integer replyStock)
    {
        this.replyStock = replyStock;
    }

    public String getPurStatus()
    {
        return purStatus;
    }

    public void setPurStatus(String purStatus)
    {
        this.purStatus = purStatus;
    }
    
}