package com.kyb.pcberp.modules.purch.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

import java.math.BigDecimal;

public class MiddleCondition extends DataEntity<MiddleCondition>
{
    private static final long serialVersionUID = 1L;

    private String middleId;

    private String supplierId;

    private BigDecimal minValue;

    private BigDecimal maxValue;

    private BigDecimal radio;

    public String getMiddleId()
    {
        return middleId;
    }

    public void setMiddleId(String middleId)
    {
        this.middleId = middleId;
    }

    public String getSupplierId()
    {
        return supplierId;
    }

    public void setSupplierId(String supplierId)
    {
        this.supplierId = supplierId;
    }

    public BigDecimal getMinValue()
    {
        return minValue;
    }

    public void setMinValue(BigDecimal minValue)
    {
        this.minValue = minValue;
    }

    public BigDecimal getMaxValue()
    {
        return maxValue;
    }

    public void setMaxValue(BigDecimal maxValue)
    {
        this.maxValue = maxValue;
    }

    public BigDecimal getRadio()
    {
        return radio;
    }

    public void setRadio(BigDecimal radio)
    {
        this.radio = radio;
    }
}
