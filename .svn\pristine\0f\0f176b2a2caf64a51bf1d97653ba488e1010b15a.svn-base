package com.kyb.pcberp.modules.hr.finance_center.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_BoundMain;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_KybAttachments;
import com.kyb.pcberp.modules.hr.finance_center.pojo.*;
import com.kyb.pcberp.modules.oa.pojo.audit.Oa_audit;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@MyBatisDao
public interface Hr_CertificateDao extends CrudDao<Hr_Certificate>
{

    List<Hr_ManualBill> getCertificateList(Hr_ManualBill hr_manualBill);

    List<Hr_Certificate> getExportCertificateList(Hr_Certificate hr_certificate);

    List<Hr_FinancialAbstract> getFinancialAbstractList();

    List<Hr_StandardAccountDetail> getStandardAccountDetailList();

    List<Hr_FinancialSettlementMethod> getFinancialSettlementMethodList();

    List<Hr_FinancialCertificateWord> getFinancialCertificateWordList();

    List<Hr_FinancialAccountbook> getFinancialAccountbookList();

    void addCertificate(Hr_Certificate hr_certificate);

    void updaCertificate(Hr_Certificate hr_certificate);

    void updateCertificateDetail(Hr_CertificateDetail hrCertificateDetail);

    List<Hr_ManualBill> getCertificateDetailList(@Param("list") List<Hr_ManualBill> list);

    List<Hr_ManualBill> getCertificateDetailListTwo(@Param("list") List<Hr_ManualBill> list);

    void addCertificateDetail(Hr_CertificateDetail hrCertificateDetail);

    void updCertificateDetail(Hr_CertificateDetail hrCertificateDetail);

    List<Hr_FinancialCustomer> getHrFinancialCustomerList();

    List<Hr_FinancialDepartment> getHrFinancialDepartmentList(String departmentId);

    List<Hr_FinancialOrganization> getHrFinancialOrganizationList();

    List<Hr_FinancialSupplier> getHrFinancialSupplierList();

    List<Hr_FinancialIncomeclassify> getHrFinancialIncomeclassifyList();

    List<Hr_FinancialBank> getHrFinancialBankList();

    List<Hr_FinancialBankaccount> getHrFinancialBankaccountList(String organizationId);

    List<Hr_Receivables> getReceivablesList(Hr_Receivables hr_receivables);

    List<DictValue> getPaymentTermList(String recordIds);

    void addWriteOffs(Hr_WriteOffs hr_writeOffs);

    List<Hr_SinglePayable> getSinglePayableList(Hr_SinglePayable hr_singlePayable);

    List<Hr_SinglePayable> getOaSinglePayableList(Hr_SinglePayable hr_singlePayable);

    List<Hr_KybAttachments> getAuditAttachmentList(Hr_KybAttachments hr_auditAttachment);

    void addOaWriteOffs(Hr_WriteOffs hr_writeOffs);

    List<Hr_SinglePayable> getCombinationList(Hr_SinglePayable hr_singlePayable);

    //财务总账应收付单据
    List<Hr_ReceiptPayment> getReceiptPaymentList(Hr_ReceiptPayment hr_receiptPayment);

    void addReceiptPayment(Hr_ReceiptPayment hr_receiptPayment);

    void delReceiptPayment(Hr_ReceiptPayment hr_receiptPayment);

    void updReceiptPayment(Hr_ReceiptPayment hr_receiptPayment);

    //OA单据
    Hr_ReceiptPayment getOtherReceiptPayment(Hr_ReceiptPayment hr_receiptPayment);

    List<Hr_ReceiptPayment> getOaDocumentList(Hr_ReceiptPayment hr_receiptPayment);

    //已用成本
    List<Hr_ReceiptPayment> getSpentCostList(Hr_ReceiptPayment hr_receiptPayment);

    //其他费用
    List<Hr_ReceiptPayment> getOtherExpensesList(Hr_ReceiptPayment hr_receiptPayment);

    //其他费用明细
    List<Hr_ReceiptPayment> getOtherExpensesDetailDataListT(Hr_ReceiptPayment hr_receiptPayment);

    //Oa单据明细
    List<Oa_audit> getOaDocumentDetailDataList(Oa_audit oa_audit);

    //已用成本明细
    List<RawmaterialStock> getSpentCostDetailList(RawmaterialStock rawmaterialStock);

    //保存凭证
    void addCertificateManage(Hr_CertificateDetail hr_certificateDetail);

    void deleteCertificateData(Hr_CertificateDetail hr_certificateDetail);

    void updateCertificateMange(Hr_CertificateDetail hr_certificateDetail);

    void updateRelationId(Hr_CertificateDetail hr_certificateDetail);

    void bathSaveCertificate(@Param("list") List<Hr_CertificateDetail> list);

    List<Hr_CertificateDetail> certificateDetailList(@Param("certificateId") String certificateId);

    void updateSplitStatus(@Param("certificateIds") String certificateIds);

    void cancelSplitData(Hr_Certificate hr_certificate);

    void cancelSplitDetailData(Hr_Certificate hr_certificate);

    List<Hr_ManualBill> getCustomCollectionList(Hr_ManualBill hr_manualBill);

    List<Hr_ManualBill> getSupplierPaymentList(Hr_ManualBill hr_manualBill);

    //手工账单
    List<Hr_ManualBill> getManualBillList(Hr_ManualBill hr_manualBill);

    void addManualBill(Hr_ManualBill hr_manualBill);

    void updateManualBill(Hr_ManualBill hr_manualBill);

    void delManualBill(Hr_ManualBill hr_manualBill);

    void addAccountBill(@Param("list") List<Hr_ManualBill> list, @Param("accountbookId")String accountbookId);

    void deleteAccountBill(@Param("manualBillId") String manualBillId);

    void batchInsertCertificateDimension(@Param("list") List<Hr_CertificateDimension> list);

    void delCertificateDimension(Hr_CertificateDimension hr_CertificateDimension);

    List<Hr_CertificateDimension> getDimensionList(Hr_CertificateDimension hr_CertificateDimension);

    void addAccountBillTwo(Hr_ManualBill bill);

    void deleteCertificateDataT(Hr_ManualBill bill);

    List<Hr_ManualBill> getExportCertificateDetailList(Hr_ManualBill hr_manualBill);

    void insertManualBillData(Hr_ManualBill hr_manualBill);

    List<Hr_ManualBill> getExportCertificateDetailListTwo(@Param("list") List<Hr_ManualBill> list);

    void updateManualBillStatus(Hr_ManualBill hr_manualBill);

    List<Hr_ManualBill> getElectronicAcceptanceList(Hr_ManualBill hr_manualBill);

    void delEndorse(Hr_ManualBill hr_manualBill);

    void insertEndorse(Hr_ManualBill hr_manualBill);

    void updateEndorse(Hr_ManualBill hr_manualBill);

    List<Hr_ManualBill> getEndorseList(Hr_ManualBill hr_manualBill);

    void updateCollectSubmitData(@Param("chooseCollectionIds") String chooseCollectionIds,@Param("dataStateId") String dataStateId);

    Integer judgeExtisData(@Param("manualBillId") String manualBillId);

    //出纳管理
    void delCasherMangeDetail(Hr_CollectPayBill hr_collectPayBill);

    void addCashManage(Hr_CollectPayBill hr_collectPayBill);

    void updateCashManage(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> getCollectPayDetailList(@Param("list") List<Hr_ManualBill> list);

    void updateCollectPayBill(Hr_CollectPayBill hr_collectPayBill);

    void insertCollectPayBill(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> getSingleReceivableList(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> getSinglePayableListTwo(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> getCollectionPaidList(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> getCollectionRecordList(@Param("list") List<Hr_CollectPayBill> list);

    List<Hr_CollectPayBill> getPaymentRecordList(@Param("list") List<Hr_CollectPayBill> list);

    void batchInsertRecordDetail(@Param("list") List<Hr_RecordDetail> list);

    List<Hr_RecordDetail> getRecordDetailList(Hr_RecordDetail recordDetail);

    void deleteSingleDetailRecords(Hr_RecordDetail hr_RecordDetail);

    void delCollectionPay(Hr_CollectPayBill hr_collectPayBill);

    void delCollectionPayRecord(Hr_CollectPayBill hr_collectPayBill);

    void updatePaymentBillStatus(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> loadReceivedAmountList(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> loadCollectAmountList(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> loadPayAmountList(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> loadCollectBillList(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> loadPayBillList(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> getCollectPayableList(@Param("list") List<Hr_CollectPayBill> list);

    Hr_CollectPayBill getSumPaymentAccount(Hr_CollectPayBill hr_collectPayBill);

    void updateAuditStatus(@Param("auditId") String auditId,@Param("auditStatus") String auditStatus);

    List<Hr_CollectPayBill> getPaidAmount(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> getReceiveAmount(Hr_CollectPayBill hr_collectPayBill);

    Integer getSingleReceivableCount(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> getReceiveManageList(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> getPaymentManageList(Hr_CollectPayBill hr_collectPayBill);
}
