/* userCtrl */
kybApp.controller('roleCtrl', [ '$rootScope', '$scope', 'upida', '$timeout', '$filter', 'CommonUtil', 'BaseUtil', function ($rootScope, $scope, upida, $timeout, $filter, CommonUtil, BaseUtil)
{

	$scope.$on('$viewContentLoaded', function ()
	{
		// initialize core components
		MainCtrl.initAjax();

		// set default layout mode
		$rootScope.settings.layout.pageBodySolid = false;
		$rootScope.settings.layout.pageSidebarClosed = false;
	});

	// 全局变量声明
	var vm = this;
	
	 
    vm.help = function(){
    	if (vm.tabs.viewForm.active){
    		vm.helpList();
    	} else if (vm.tabs.editForm.active) {
    		vm.helpDetail();
    	}else if(vm.tabs.menuForm.active){
    		vm.helpSubmitmenu();
    	}else{
    		vm.helpRolemenu();
    	}
    };
    
    vm.shouldAutoStart = false;
    vm.introListOptions = {
        steps:[
        {
            element: '#step1',
            intro: "当您打开页面后会自动加载角色信息至列表，对某一行数据可单击首列角色名称中的<strong style='color:red;'>蓝色链接</strong>或双击该行进入详情页！",
            position: 'top'
        },
        {
            element: '#step2',
            intro: '行记录快速操作，对投料单进行删除、设置权限、分配人员等操作！',
            position: 'top'
        },
        {
            element: '#step3',
            intro: '此处为分页区域，您可以点击您想查看的页面！',
            position: 'top'
        },
        {
            element: '#step4',
            intro: "此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！",
            position: 'top'
        },
        {
            element: '#step5',
            intro: '此处为查询条件，可设置不同的条件，点击右下角的查询来过滤信息！',
            position: 'bottom'
        },
        {
            element: '#step6',
            intro: '操作按钮区域，可添加通知单！',
            position: 'bottom'
        },
        {
            element: '#step7',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
    
    
    vm.introDetailOptions = {
   	 steps:[
   	        {
   	            element: '#stepDetail1',
   	            intro: "角色信息录入区域，带<span style='color:red;'> *</span> 的为必填或必选项！",
   	            position: 'top'
   	        },
   	        {
   	            element: '#stepDetail2',
   	            intro: '操作按钮区域，点击保存完成本次操作！',
   	            position: 'top'
   	        },
   	        {
   	            element: '#stepDetail6',
   	            intro: '谢谢使用，再见。'
   	        }
   	        ],
   	        showStepNumbers: false,
   	        exitOnOverlayClick: true,
   	        exitOnEsc:true,
   	        nextLabel: '<strong>下一步!</strong>',
   	        prevLabel: '<span style="color:green">上一步</span>',
   	        skipLabel: '退出',
   	        doneLabel: '谢谢'
       };
    
    vm.introSubmitmenuOptions = {
      	 steps:[
      	        {
      	            element: '#stepMenu1',
      	            intro: "角色菜单权限录入区域！",
      	            position: 'top'
      	        },
      	        {
      	            element: '#stepMenu2',
      	            intro: '总控制,菜单权限和菜单的操作权限 以及数据权限！',
      	            position: 'top'
      	        },
      	        {
      	            element: '#stepMenu3',
      	            intro: '操作按钮区域，点击保存完成本次操作！',
      	            position: 'top'
      	        },
      	        {
      	            element: '#stepMenu4',
      	            intro: '谢谢使用，再见。'
      	        }
      	        ],
      	        showStepNumbers: false,
      	        exitOnOverlayClick: true,
      	        exitOnEsc:true,
      	        nextLabel: '<strong>下一步!</strong>',
      	        prevLabel: '<span style="color:green">上一步</span>',
      	        skipLabel: '退出',
      	        doneLabel: '谢谢'
          };
    
    
    vm.introRoleOptions = {
     	 steps:[
     	        {
     	            element: '#stepRole1',
     	            intro: "角色分配人员录入区域！",
     	            position: 'top'
     	        },
     	        {
     	            element: '#stepRole2',
     	            intro: '角色名称！',
     	            position: 'top'
     	        },
     	        {
     	            element: '#stepRole3',
     	            intro: '对应拥有改角色的用户列表！',
     	            position: 'top'
     	        },
     	       {
     	            element: '#stepRole4',
     	            intro: '对行数据进行快捷操作！',
     	            position: 'top'
     	        },
     	       {
     	            element: '#stepRole5',
     	            intro: '对该角色分配已有的用户！',
     	            position: 'top'
     	        },
     	        {
     	            element: '#stepRole6',
     	            intro: '谢谢使用，再见。'
     	        }
     	        ],
     	        showStepNumbers: false,
     	        exitOnOverlayClick: true,
     	        exitOnEsc:true,
     	        nextLabel: '<strong>下一步!</strong>',
     	        prevLabel: '<span style="color:green">上一步</span>',
     	        skipLabel: '退出',
     	        doneLabel: '谢谢'
         };
    
    
	vm.focus = {main: 1, detail: 1}; //焦点
	vm.loginUser = BaseUtil.getUser();
	vm.usersOfRole = [];
	vm.roletouser = {};
	vm.roleandUser = []; // 角色下面拥有的用户列表
	vm.menulist = [];// 菜单list
	vm.roleItem = [];// 菜单权限回传数据

	vm.tmepRoleItems = [];
	vm.newRoleItems = [];
	vm.delRoleItems = [];
	// 方法实现
	// 类似靠近原则，特别是有逻辑关系
	/*
	 * function(） { 变量声明 return foreach{ //过程变量定义 } }
	 * 
	 */

	// 分页数据
	vm.page = {};
	// 显示数据大小
	vm.page.pageSizeOptions = [ 5, 10, 30, 50 ];
	// 字典项分页数据
	vm.page.data = {}; // 指令输入数据（包含 data=>page 和
	// condition =>条件）
	vm.page.pageSize = 10;
	vm.page.pageNo = 1;
	vm.page.url = "sys/role/page";
	vm.page.condition = []; // 条件
	// 查询条件参数
	vm.query = {}; // 查询对象
	vm.query.rolename = {};
	vm.query.rolename.name = "name";
	vm.query.rolename.value = "";
	// 排序字段
	vm.query.sort = {};
	vm.query.sort.name = "orderBy";
	vm.query.sort.value = "createdDate DESC";
	vm.tabs = {
		viewForm : {
			active : true
		},
		editForm : {
			active : false,
			show : false
		}
	};
	// 权限
	vm.right = {};
	// 激活标志
	vm.flags = [ {
		"flagId" : 1,
		"flagName" : "在职"
	}, {
		"flagId" : 2,
		"flagName" : "离职"
	} ];


    // tj 判断是否多次点击
    vm.clicks = true;
	// 编辑操作对象
	vm.user = {};
	// 删除主键
	vm.delRecordId = -1;
	// 修改标题
	vm.editTitle = "";
	// 角色
	vm.role = {};
	vm.roleStr = "";
	vm.tempRoles = {};
	vm.tempRoleStr = ""; // 选中人员的字符串
	vm.edit = {};
	vm.edit.formScope = null;
	vm.roleEdit = {};
	vm.roleEdit.formScope = null;
	// 显示编辑
	vm.showEditForm = function ()
	{
		if (vm.right.edit || vm.right.manage)
		{
			vm.edit.formScope.user_form.$setPristine();
		}
		vm.tabs.editForm.show = true;
		vm.tabs.editForm.active = true;
	};
	// 显示查看
	vm.showSeeForm = function ()
	{
		vm.tabs.SeeForm.show = true;
		vm.tabs.SeeForm.active = true;
	};
	// 隐藏查看
	vm.hideSeeForm = function ()
	{
		vm.tabs.SeeForm.show = false; // 分配
		$timeout(function ()
		{
			vm.tabs.viewForm.active = true;// 分配
		});
	};
	// 显示分配
	vm.showForm = function ()
	{
		vm.tabs.showForm.show = true;
		vm.tabs.showForm.active = true;
	};
	// 显示设置菜单
	vm.menuForm = function ()
	{
		vm.tabs.menuForm.show = true;
		vm.tabs.menuForm.active = true;
	};
	// 显示用户分配设置窗口
	vm.showRoleSet = function ()
	{
		vm.tempRolestemp = [];
		if(vm.user.roleType == 2){
			for (var i = 0; i < vm.tempCustRoles.length; i++)
			{
				if(vm.tempCustRoles[i].userType === "1"){
					vm.tempRolestemp.push(vm.tempCustRoles[i]);
				}
			}
			vm.usersOfRole = vm.tempRolestemp;
			vm.tempRoles = angular.copy(vm.tempRolestemp);
		}else{
			// 加载所有用户
				for (var i = 0; i < vm.tempUserRoles.length; i++)
				{
					if(vm.tempUserRoles[i].userType === "0"){
						vm.tempRolestemp.push(vm.tempUserRoles[i]);
					}
				}
				
				vm.usersOfRole = vm.tempRolestemp;
				vm.tempRoles = angular.copy(vm.tempRolestemp);
			
			// 设置用户列表中的选中项
			clearRoleCheck();
			initRoleCheck();
			
		}
		
		vm.tempRoleStr = $filter('roleFilter')("", vm.tempRoles, 0);
		$('#staticRole').modal();
	};
	
	
	
	vm.hideshowForm = function ()
	{
		vm.tabs.showForm.show = false; // 分配
		$timeout(function ()
		{
			vm.tabs.showForm.active = true;// 分配
		});
	};
	// 隐藏编辑
	vm.hideEditForm = function ()
	{
		if (vm.right.edit || vm.right.manage)
		{
			vm.edit.formScope.user_form.$setPristine();
		}
		vm.tabs.editForm.show = false;
		vm.tabs.showForm.show = false; // 分配
		vm.tabs.menuForm.show = false; // 菜单权限
		vm.isAdd = false;
		$timeout(function ()
		{
			vm.tabs.viewForm.active = true;
			vm.tabs.showForm.active = true;// 分配
			vm.tabs.menuForm.active = true;// 菜单权限
		});
	};
	vm.isAdd = false;
	// 分页按钮单击处理
	vm.doPage = function (page, pageSize, total)
	{
		vm.page.pageNo = page;
		vm.page.pageSize = pageSize;
		vm.init(page, pageSize, vm.page.condition, vm.page.url);
	};

	/*
	 * 兼容条件查询与普通查询 no 当前页 size 当前页显示数量 condition 是数组，可放入多个条件对象，如{name:"type",
	 * value:"1"} 名称对应实体字段名称，值对应页面输入的值 url 请求数据链接
	 */
	vm.init = function (no, size, condition, url)
	{
		// 请求数据
		var reqData = {};
		reqData.pageNo = no;
		reqData.pageSize = size;
		reqData.queryAll = vm.queryAll;
		condition.push({
			name : vm.query.sort.name,
			value : vm.query.sort.value
		});
		// 设置过滤条件
		if (condition.length > 0)
		{
			angular.forEach(condition, function (p)
			{
				reqData[p.name] = p.value;
			});
		}
		
		MainCtrl.blockUI({
    	    animate: true,
    	});

		// 请求分页数据
		upida.post(url, reqData).then(function (result)
		{
			var data = {};
			// 如果结果为空
			if (typeof result === 'undefined' || typeof result.list === 'undefined')
			{
				data.pageNo = 1;
				data.pageSize = 10;
				data.list = [];
				data.startCount = 0;
				data.endCount = 0;
			}
			else
			{
				data = result;
				// 计算开始数
				data.startCount = (data.pageNo - 1) * data.pageSize + 1;
				// 计算结束数
				data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
			}
			vm.page.data = data;
			MainCtrl.unblockUI();
		});
	};
	// 默认排序按时间排序排序处理
	vm.sort = {};
	vm.sort.name = {
		both : true,
		desc : false,
		asc : false
	};

	vm.sortClick = function (col)
	{
		vm.sort[col].both = false;
		if (!vm.sort[col].desc && !vm.sort[col].asc)
		{
			vm.sort[col].asc = true;
		}
		else
		{
			if (vm.sort[col].asc)
			{
				vm.sort[col].desc = true;
				vm.sort[col].asc = false;
			}
			else
			{
				vm.sort[col].desc = false;
				vm.sort[col].asc = true;
			}
		}
		for ( var p in vm.sort)
		{
			if (p !== col)
			{
				vm.sort[p].desc = false;
				vm.sort[p].asc = false;
				vm.sort[p].both = true;
			}
		}
		vm.query.sort.value = vm.sort[col].asc ? col + " ASC" : col + " DESC";
		vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
	};
	// 页面显示数量改变
	vm.pageSizeChange = function ()
	{
		vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url);
	};
	// 查询数据
	vm.doQuery = function ()
	{
		// 设置查询条件
		var condition = [];
		if (vm.query.rolename.value !== "")
		{
			condition.push({
				name : vm.query.rolename.name,
				value : vm.query.rolename.value
			});
		}
		vm.page.pageNo = 1;
		vm.page.condition = condition;
		// 查询数据
		vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
	};
	// 单击修改
	vm.modUser = function (index)
	{
		if (vm.right.manage)
		{
			vm.editTitle = "管理";
			vm.isAdd = false;
			vm.user = angular.copy(vm.page.data.list[index]);
			if (vm.user.name == "系统管理员")
			{
				vm.showSeeForm();
			}
			else
			{
				vm.focus.main +=1;
				vm.showEditForm();
			}
		}
		else if (vm.right.edit)
		{
			vm.editTitle = "修改";
			vm.isAdd = false;
			vm.user = angular.copy(vm.page.data.list[index]);
			if (vm.user.name == "系统管理员")
			{
				vm.showSeeForm();
			}
			else
			{
				vm.focus.main +=1;
				vm.showEditForm();
			}
		}
		else if (vm.right.view)
		{
			vm.editTitle = "查看";
			vm.isAdd = false;
			vm.user = angular.copy(vm.page.data.list[index]);
			vm.showSeeForm();
		}
	};
	// 单击分配
	vm.modshow = function (index)
	{
		MainCtrl.blockUI({
    	    animate: true,
    	});
		vm.user = angular.copy(vm.page.data.list[index]);
		vm.showForm();
		vm.roletouser.roleId = vm.user.recordId; // 设置用户角色表中的角色编号
		upida.post("sys/role/findRoleUserList", vm.roletouser).then(function (data){
			vm.roleandUser = data;
			MainCtrl.unblockUI();
		});
	};
	
	
	
	//点击设置客户角色菜单
	vm.menushowcus = function(index){

		// 设置菜单总控制初始化
		vm.aaaaa = "";
		
		vm.isshowb = false;
		vm.isshowc = false;
		// ------------

		vm.sortData = [];
		// 清空表单
		vm.roleEdit.formScope.menu_form.$setPristine();
		vm.roledata = [];
		vm.user = angular.copy(vm.page.data.list[index]);
		vm.menuForm();
		vm.roletouser.recordId = vm.user.recordId;
		MainCtrl.blockUI({
    	    animate: true,
    	});
		upida.post("sys/role/selectRoleItemcus", vm.roletouser).then(function (data){
			vm.roledata = data;
			angular.forEach(data, function (p)
			{
				if (angular.isDefined(p.parent))
				{
					if (p.parent.recordId === "1")
					{
						// 一级菜单
						// 设置操作权限
						if (p.hasOwnProperty('permission'))
						{
							// 若果有permission
							// 才做以下操作
							var a = p.permission;
							if (a.indexOf(":manage") > 0)
							{
								p.dealRight = "manage";
							}
							else if (a.indexOf(":edit") > 0)
							{
								p.dealRight = "edit";
							}
							else
							{
								p.dealRight = "view";
							}
						}
						else
						{
							p.dealRight = "view";
						}

						// 数据权限
						if (p.hasOwnProperty('dataRange'))
						{
							if (p.dataRange == "0")
							{
								p.dataRange = "0";
							}
							else if (p.dataRange == "1")
							{
								p.dataRange = "1";
							}
						}
						else
						{
							p.dataRange = "0";
						}
						var menu = angular.copy(p);
						var subMenu = [];
						angular.forEach(data, function (o)
						{
							if (angular.isDefined(o.parent))
							{
								if (o.parent.recordId === p.recordId)
								{
									if (o.hasOwnProperty('permission'))
									{
										var temp = o.permission.replace(/\s+/g, "");
										if (temp.indexOf("manage") != "-1")
										{
											o.dealRight = "manage";
										}
										else if (temp.indexOf("edit") != "-1")
										{
											o.dealRight = "edit";
										}
										else if (temp.indexOf("view") != "-1")
										{
											o.dealRight = "view";
										}
									}
									else
									{
										o.dealRight = p.dealRight;
									}

									if (o.hasOwnProperty('dataRange'))
									{
										if (o.dataRange == "0")
										{
											o.dataRange = "0";
										}
										else if (o.dataRange == "1")
										{
											o.dataRange = "1";
										}
									}
									else
									{
										o.dataRange = p.dataRange;
									}
									subMenu.push(o);
								}
							}
						});
						menu.subMenu = subMenu;
						vm.sortData.push(menu);
					}
				}
			});
			MainCtrl.unblockUI();
		});
	
		
		
	};
	
	
	
	
	vm.sortData = [];
	// 单击设置菜单
	vm.menushow = function (index)
	{
		// 设置菜单总控制初始化
		vm.aaaaa = "";
		vm.bbbbb = "view";
		vm.ccccc = "0";
		
		vm.isshowb = true;
		vm.isshowc = true;
		// ------------

		vm.sortData = [];
		// 清空表单
		vm.roleEdit.formScope.menu_form.$setPristine();
		vm.roledata = [];
		vm.user = angular.copy(vm.page.data.list[index]);
		vm.menuForm();
		vm.roletouser.recordId = vm.user.recordId;
		MainCtrl.blockUI({
    	    animate: true,
    	});
		upida.post("sys/role/selectRoleItem", vm.roletouser).then(function (data){
			vm.roledata = data;
			angular.forEach(data, function (p)
			{
				if (angular.isDefined(p.parent))
				{
					if (p.parent.recordId === "1")
					{
						// 一级菜单
						// 设置操作权限
						if (p.hasOwnProperty('permission'))
						{
							// 若果有permission
							// 才做以下操作
							var a = p.permission;
							if (a.indexOf(":manage") > 0)
							{
								p.dealRight = "manage";
							}
							else if (a.indexOf(":edit") > 0)
							{
								p.dealRight = "edit";
							}
							else
							{
								p.dealRight = "view";
							}
						}
						else
						{
							p.dealRight = "view";
						}

						// 数据权限
						if (p.hasOwnProperty('dataRange'))
						{
							if (p.dataRange == "0")
							{
								p.dataRange = "0";
							}
							else if (p.dataRange == "1")
							{
								p.dataRange = "1";
							}
						}
						else
						{
							p.dataRange = "0";
						}
						var menu = angular.copy(p);
						var subMenu = [];
						angular.forEach(data, function (o)
						{
							if (angular.isDefined(o.parent))
							{
								if (o.parent.recordId === p.recordId)
								{
									if (o.hasOwnProperty('permission'))
									{
										var temp = o.permission.replace(/\s+/g, "");
										if (temp.indexOf("manage") != "-1")
										{
											o.dealRight = "manage";
										}
										else if (temp.indexOf("edit") != "-1")
										{
											o.dealRight = "edit";
										}
										else if (temp.indexOf("view") != "-1")
										{
											o.dealRight = "view";
										}
									}
									else
									{
										o.dealRight = p.dealRight;
									}

									if (o.hasOwnProperty('dataRange'))
									{
										if (o.dataRange == "0")
										{
											o.dataRange = "0";
										}
										else if (o.dataRange == "1")
										{
											o.dataRange = "1";
										}
									}
									else
									{
										o.dataRange = p.dataRange;
									}
									subMenu.push(o);
								}
							}
						});
						menu.subMenu = subMenu;
						vm.sortData.push(menu);
					}
				}
			});
			MainCtrl.unblockUI();
		});
	};

	/**
	 * 整理数据
	 */
	function getSubimtData ()
	{
		var data = [];
		angular.forEach(vm.sortData, function (p)
		{
			if (p.menuChecked === "1")
			{
				var sup = {};
				// 公告管理的特殊处理
				if (p.name == "通知公告")
				{
					if (p.dealRight === "view")
					{
						p.url = p.url + "view";
					}
					if (p.dealRight === "edit")
					{
						p.url = p.url + "view," + p.url + "edit";
					}
					// 有管理权限 就有钱两个权限
					if (p.dealRight === "manage")
					{
						p.url = p.url + "view," + p.url + "edit," + p.url + "manage";
					}
					sup.recordId = p.recordId;
					sup.permission = p.url;
					sup.dataRange = p.dataRange;
				}
				else if (p.name == "文件存储")
				{
					// dealRight
					if (p.dealRight === "view")
					{
						p.url = p.url + "view";
					}
					if (p.dealRight === "edit")
					{
						p.url = p.url + "view," + p.url + "edit";
					}
					// 有管理权限 就有钱两个权限
					if (p.dealRight === "manage")
					{
						p.url = p.url + "view," + p.url + "edit," + p.url + "manage";
					}
					sup.recordId = p.recordId;
					sup.permission = p.url;
					sup.dataRange = p.dataRange;
				}
				else
				{
					// 853412#
					if (p.dealRight === "view")
					{
						p.url = p.url + "view";
					}
					else if (p.dealRight === "edit")
					{
						p.url = p.url + "view," + p.url + "edit";
					}
					else if (p.dealRight === "manage")
					{
						p.url = p.url + "view," + p.url + "edit," + p.url + "manage";
					}
					sup.recordId = p.recordId;
					sup.permission = p.url;
					sup.dataRange = p.dataRange;
				}
				data.push(sup);
				angular.forEach(p.subMenu, function (o)
				{
					if (o.menuChecked === "1")
					{
						var sub = {};
						// 设置权限
						if (o.dealRight === "view")
						{
							o.url = o.url + "view";
						}

						if (o.dealRight === "edit")
						{
							o.url = o.url + "view," + o.url + "edit";
						}
						// 有管理权限
						// 就有钱两个权限
						if (o.dealRight === "manage")
						{
							o.url = o.url + "view," + o.url + "edit," + o.url + "manage";
						}
						sub.recordId = o.recordId;
						sub.permission = o.url;
						sub.dataRange = o.dataRange;
						data.push(sub);
					}
				});
			}
		});
		var role = {};
		if (data.length > 0)
		{
			role.recordId = vm.user.recordId;
			role.menuList = data;
		}
		return role;
	}
	// 菜单权限设置保存
	vm.submitmenu = function ()
	{
		if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    
    	    // 保存角色菜单中间表
    		MainCtrl.blockUI({
           	   animate: true,
    		});
    		var data = getSubimtData();
    		if(typeof data.menuList=="undefined"){
    			// 提示信息
    			vm.message = "请设置权限";
    			$('#static').modal();
    			MainCtrl.unblockUI();
    			vm.clicks = true;// tj 2017-09-07  用户是否多次点击
    			return;
    		}
    		
    		upida.post("sys/role/saveRoleItems", data).then(function (result){
    			// 重新查询
    			vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    			// 提示信息
    			vm.message = result;
    			$('#static').modal();
    			// 关闭本tab
    			vm.hideEditForm();
    			MainCtrl.unblockUI();
    			vm.clicks = true;// tj 2017-09-07  用户是否多次点击
    		});
    	}
	};
	
	// 单击添加
	vm.addUser = function (index){
		vm.edit.formScope.user_form.$setPristine();
		vm.isAdd = true;
		vm.user = {};
		vm.user.name = "";
		vm.user.remark = "";
		vm.user.status = 1;
		vm.user.roleType = 1;
		vm.deptSelect = {};
		vm.editTitle = "添加";
		vm.focus.main +=1;
		vm.showEditForm();
	};
	
	
	vm.addRoleUser = function (index){
		vm.edit.formScope.user_form.$setPristine();
		vm.isAdd = true;
		vm.user = {};
		vm.user.name = "";
		vm.user.remark = "";
		vm.user.status = 1;
		vm.user.roleType = 2;
		vm.deptSelect = {};
		vm.editTitle = "添加";
		vm.focus.main +=1;
		vm.showEditForm();
		
	};
	
	
	
	
	
	// 单击取消
	vm.cacelBtn = function (form){
		form.$setPristine();// 重置表单
		vm.hideEditForm();
		vm.showForm();
	};
	
	vm.cacelBtnShow = function (form)
	{
		vm.hideshowForm();
		vm.showForm();
	};
	
	vm.showDeptSet = function (){
		$('#staticDept').modal();
	};

	/**
	 * 子菜单选中事件
	 */
	vm.clicklist = function (a)
	{

		// 不选中 》 选中
		angular.forEach(vm.sortData, function (p)
		{
			if (parseInt(p.recordId) == parseInt(a.submenu.parent.recordId))
			{
				p.menuChecked = "1";
				return;
			}

		});

	};

	/**
	 * 选择总操作权限
	 */
	vm.clicksdealRight = function (row)
	{
		angular.forEach(vm.sortData, function (p)
		{
			p.dealRight = row;
			angular.forEach(p.subMenu, function (j)
			{
				j.dealRight = row;
			});
		});
	};

	/**
	 * 总数据权限
	 */
	vm.clicksdataRange = function (row)
	{
		angular.forEach(vm.sortData, function (p)
		{
			p.dataRange = row;
			angular.forEach(p.subMenu, function (j)
			{
				j.dataRange = row;
			});
		});
	};

	/**
	 * 全选 反选
	 */
	vm.clicksall = function (a)
	{
		if (a == 1)
		{
			// 全部不选中
			angular.forEach(vm.sortData, function (p)
			{
				p.menuChecked = "0";
				angular.forEach(p.subMenu, function (j)
				{
					j.menuChecked = "0";
				});
			});
		}
		else
		{
			// 全部选中
			angular.forEach(vm.sortData, function (p)
			{
				p.menuChecked = "1";
				angular.forEach(p.subMenu, function (j)
				{
					j.menuChecked = "1";
				});
			});
		}
	};

	/**
	 * 父菜单选中事件
	 */
	vm.clicksup = function (a)
	{
		// 选中 》 不选中
		if (a.menu.menuChecked == "1")
		{
			// a.menu.subMenu.menuChecked
			angular.forEach(a.menu.subMenu, function (p)
			{
				if (p.menuChecked === "1")
				{
					angular.forEach(vm.sortData, function (j)
					{
						if (j.recordId === a.menu.recordId)
						{
							// 设置
							// 选中属性为
							// 不选中
							p.menuChecked = "0";
						}
					});

				}
			});
		}
		else if (a.menu.menuChecked == "0")
		{
			// 不选中 》 选中
			angular.forEach(a.menu.subMenu, function (p)
			{
				if (p.menuChecked === "0")
				{
					angular.forEach(vm.sortData, function (j)
					{
						if (j.recordId === a.menu.recordId)
						{
							// 设置
							// 选中属性为
							// 不选中
							p.menuChecked = "1";
						}
					});
				}
			});
		}
	};
	// 改变父菜单权限子菜单做相应变化
	vm.clicksupdealRight = function (row)
	{
		var deal = row.menu.dealRight;
		angular.forEach(row.menu.subMenu, function (p)
		{
			if (p.dealRight === "view" || p.dealRight === "edit" || p.dealRight === "manage")
			{
				angular.forEach(vm.sortData, function (j)
				{
					if (j.recordId === row.menu.recordId)
					{
						// 设置
						// 选中属性为
						// 不选中
						p.dealRight = deal;
					}
				});
			}
		});
	};
	// 改变父菜单的数据权限 子菜单做相应变化
	vm.clicksupdataRange = function (row)
	{
		var dataRange = row.menu.dataRange;
		angular.forEach(row.menu.subMenu, function (p)
		{
			if (p.dataRange == 0 || p.dataRange == 1)
			{
				angular.forEach(vm.sortData, function (j)
				{
					if (j.recordId === row.menu.recordId)
					{
						// 设置
						// 选中属性为
						// 不选中
						p.dataRange = dataRange;
					}
				});

			}
		});
	};
	// 点击check box
	/*
	 * vm.click = function(recoedId){ var str=""; var a =
	 * document.menu_form.subbox;
	 * 
	 * var n = a.length; for (var i = 0; i < n; i++) { if (a[i].check) { str +=
	 * ","+a[i].value; } }
	 * 
	 *  };
	 */

	/*
	 * 1.取出所有role 2.设置已有权限checkbox选中 3.响应选中事件，将已经选中的roleitem添加进数组
	 * 4.保存，将数组传回后台进行保存 保存有3种方案
	 * 4.1将所有的角色全部删除，批量重新新增所有角色，这种方案可以使用初始加载的数组，checkbox事件只是改变数组的选中值
	 * 4.2将数组进行循环，如果是选中，用户数据库存在该角色id，则无动作；如果不存在则添加。如果是取消选中，数据库存在，则删除，否则无动作
	 * 4.3checkbox事件新建2个数组（新增项列表和删除项列表），将原数组不存在选中的角色添加进去，将原有的去除则添加到另外一个数组，统一对这2个数组生成批量sql进行一次性操作
	 * 
	 */
	vm.click = function (e)
	{
		// 方案1
		// 直接post vm.sortData回去，删除后循环插入

		// 方案2
		// post DAO层for(post vm.sortData)，判断是否该新增还是删除

		// 方案3
		// js里for(post vm.sortData)，判断哪些是新增还是哪些要删除
		// post 2个数组,循环，生成SQL，sql动态生成
		// delete in (ids),批量加

		if (e.menu.menuChecked == "0")
		{
			// 选中子菜单
			// e.menu.check=false;
		}
		else
		{
			// 设子菜单为false

		}

		if (e.submenu.check)
		{
			vm.roleSelectedItems.push(e.submenu.recordId, e.submenu.url, e.submenu.dataRange);
			// 如果选中该checkbox，如果nodeid在vm.sortData如果是没有选中就把给设置成选中

		}
		else
		{
			// 如果取消选中，则将数组中该id去除
			// vm.roleSelectedItems.remove(e.submenu.recordId,e.submenu.url,e.submenu.dataRange);
		}
	};
	// 创建和修改
	vm.submitMod = function (form)
	{
		if(vm.clicks){
    	    form.$setDirty();
    		if (form.$valid)
    		{
    			vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    			MainCtrl.blockUI({
    				animate : true,
    			});
    			if (vm.user.name === "系统管理员")
    			{
    				vm.message = "不可添加该角色名！";
    				$('#static').modal();
    				
    				MainCtrl.unblockUI();
    				vm.clicks = true;// tj 2017-09-07  用户是否多次点击
    				return;
    			}
    			
    			//添加之前做判断 是否是有本角色
    			upida.post("sys/role/isExist", vm.user).then(function (data){
    				if(data > 0){
    					vm.message = "已有相同的角色,请更换角色名！";
    					$('#static').modal();
    					MainCtrl.unblockUI();
    					vm.clicks = true;// tj 2017-09-07  用户是否多次点击
    					return;
    				}else{
    					// 提交数据
    					upida.post("sys/role/save", vm.user).then(function (data)
    					{
    						
    						vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);

    						// 初始化数据
    						vm.user = {};
    						vm.hideEditForm();

    						// 提示信息
							vm.message = data;
    						$('#static').modal();
    						vm.clicks = true;// tj 2017-09-07  用户是否多次点击
    					});
    				}
    			});
    		}
    	}
	};

	// 移除用户角色
	vm.delUserRole = function (index)
	{
		vm.delUserroleId = vm.roleandUser[index].userRoleId;
		$('#staticRemoveUserRole').modal();
	};

	// 做删除操作

	vm.doDelUserRole = function ()
	{
		if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    
    	    MainCtrl.blockUI({
    	     	   animate: true,
    			});
    			upida.post("sys/role/deleteUserRoleById/" + vm.delUserroleId, null).then(function (data)
    			{
    				// 重新查询数据
    				upida.post("sys/role/findRoleUserList", vm.roletouser).then(function (data)
    				{
    					vm.roleandUser = data;
    					MainCtrl.unblockUI();
    				});
    				vm.message = data;
    				$('#static').modal();
    				MainCtrl.unblockUI();
    				vm.clicks = true;// tj 2017-09-07  用户是否多次点击
    			});
    	}
	};
	
	// 初始化消息权限设置
    vm.setMessageList = "";
    vm.initSetMessage = function ()
    {
        MainCtrl.blockUI({
    	    animate: true,
    	});
        vm.setMessageList = "";
        upida.post("sys/message/getSetRoleMsgList",vm.msgRoleId).then(function (data) {
        	vm.setMessageList = data;
            MainCtrl.unblockUI();
        });
    }
	
	// 显示设置消息权限
    vm.msgRoleId = "";
    vm.showSetMessage = function (index)
    {
    	vm.tabs.messageSet.active = true;
    	vm.tabs.messageSet.show = true;
    	vm.msgRoleId = "";
    	vm.permissions = "1";
    	vm.msgRoleId = vm.page.data.list[index].recordId;
        vm.initSetMessage();
    }
    
    // 隐藏消息设置权限
    vm.hiddenMessageSet = function()
    {
    	vm.tabs.messageSet.active = false;
    	vm.tabs.messageSet.show = false;
    	$timeout(function(){
            vm.tabs.viewForm.active = true;
        });
    }
    
    // 接收所有消息
    vm.selectAll = function()
    {
    	if(vm.allMsgFlag){
    		if(vm.allMsgFlag == 1){
        	    angular.forEach(vm.setMessageList, function (p)
        		    {
        			    p.checkFlag = "0";
        				angular.forEach(p.messageList, function (j)
        				{
        				    j.checkFlag = "0";
        				});
        		    });    		
        	}else{
        		angular.forEach(vm.setMessageList, function (p)
        		    {
        				p.checkFlag = "1";
        				angular.forEach(p.messageList, function (j)
        				{
        				    j.checkFlag = "1";
        				});
        			});
        	}
    	}
    }
    
    // 设置用户是否可以设置消息
    vm.selectSetAll = function()
    {
    	if(vm.checkAllSetFlag){
    		if(vm.checkAllSetFlag == 1){
        	    angular.forEach(vm.setMessageList, function (p)
        		    {
        			    p.editFlag = "0";
        				angular.forEach(p.messageList, function (j)
        				{
        				    j.editFlag = "0";
        				});
        		    });    		
        	}else{
        		angular.forEach(vm.setMessageList, function (p)
        		    {
        				p.editFlag = "1";
        				angular.forEach(p.messageList, function (j)
        				{
        				    j.editFlag = "1";
        				});
        			});
        	}
    	}
    }
    
    // 设置接收是本人的还是所有
    vm.selectAllPermissions = function()
    {
    	if(vm.permissions){
    		if(vm.permissions == 1){
        	    angular.forEach(vm.setMessageList, function (p)
        		    {
        			    p.setFlag = "1";
        				angular.forEach(p.messageList, function (j)
        				{
        				    j.setFlag = "1";
        				});
        		    });    		
        	}else{
        		angular.forEach(vm.setMessageList, function (p)
        		    {
        				p.setFlag = "0";
        				angular.forEach(p.messageList, function (j)
        				{
        				    j.setFlag = "0";
        				});
        			});
        	}
    	}
    }
    
    //选择父节点
    vm.selectParent = function(a)
    {
    	if (a.msgParent.checkFlag == "1")
		{
			angular.forEach(a.msgParent.messageList, function (p)
			{
				if (p.checkFlag === "1")
				{
					angular.forEach(vm.setMessageList, function (j)
					{
						if (j.recordId === a.msgParent.recordId)
						{
							p.checkFlag = "0";
						}
					});

				}
			});
		}
		else if (a.msgParent.checkFlag == "0")
		{
			angular.forEach(a.msgParent.messageList, function (p)
			{
				if (p.checkFlag === "0")
				{
					angular.forEach(vm.setMessageList, function (j)
					{
						if (j.recordId === a.msgParent.recordId)
						{
							p.checkFlag = "1";
						}
					});
				}
			});
		}
    }
    
    //设置用户是否可以设置消息
    vm.selectSetParent = function(a)
    {
    	if (a.msgParent.editFlag == "1")
		{
			angular.forEach(a.msgParent.messageList, function (p)
			{
				if (p.editFlag === "1")
				{
					angular.forEach(vm.setMessageList, function (j)
					{
						if (j.recordId === a.msgParent.recordId)
						{
							p.editFlag = "0";
						}
					});

				}
			});
		}
		else if (a.msgParent.editFlag == "0")
		{
			angular.forEach(a.msgParent.messageList, function (p)
			{
				if (p.editFlag === "0")
				{
					angular.forEach(vm.setMessageList, function (j)
					{
						if (j.recordId === a.msgParent.recordId)
						{
							p.editFlag = "1";
						}
					});
				}
			});
		}
    }
    
    //选择消息接收是本人还是所有
    vm.selectParentPermissions = function(a)
    {
    	if (a.msgParent.setFlag == "0")
		{
			angular.forEach(a.msgParent.messageList, function (p)
			{
				angular.forEach(vm.setMessageList, function (j)
				{
					if (j.recordId === a.msgParent.recordId)
					{
						p.setFlag = "0";
					}
				});

			});
		}
		else if (a.msgParent.setFlag == "1")
		{
			angular.forEach(a.msgParent.messageList, function (p)
			{
			    angular.forEach(vm.setMessageList, function (j)
				{
				    if (j.recordId === a.msgParent.recordId)
					    {
							p.setFlag = "1";
						}
					});
			});
		}
    }
    
    // 选择子节点
    vm.selectChild = function (a)
	{
		angular.forEach(vm.setMessageList, function (p)
		{
			if (parseInt(p.msgNode) == parseInt(a.msgChild.msgParentNode))
			{
				p.checkFlag = "1";
				return;
			}

		});
	};
	
	// 选择子节点
    vm.selectSetChild = function (a)
	{
		angular.forEach(vm.setMessageList, function (p)
		{
			if (parseInt(p.msgNode) == parseInt(a.msgChild.msgParentNode))
			{
				p.editFlag = "1";
				return;
			}

		});
	};
	
	//获取接收消息设置的数据
    vm.setMessageData = [];
    vm.getSetMessage = function()
    {
    	vm.setMessageData = [];
        angular.forEach(vm.setMessageList, function (p)
        {
    	    if(p.checkFlag == 1 || p.editFlag == 1){
    	    	p.roleId = vm.msgRoleId;
    	    	vm.setMessageData.push(p);
    	    	angular.forEach(p.messageList, function (j)
    	    	{
    	    		if(j.checkFlag == 1 || j.editFlag == 1){
    	    			j.roleId = vm.msgRoleId;
    	    			vm.setMessageData.push(j);
    	    		}
    	    	});
    	    }		  
        });   
    }
	
	//保存接收消息设置
    vm.msgData = [];
    vm.submitSetMessage = function()
    {
    	if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    
    	    vm.msgData = [];
        	MainCtrl.blockUI({
        	    animate: true,
        	});
        	vm.getSetMessage();
        	if(vm.setMessageData && vm.setMessageData.length > 0){
        		vm.msgData = vm.setMessageData;
        	}else{
        		vm.noCheck = {};
        		vm.noCheck.roleId = "";
        		vm.noCheck.roleId = vm.msgRoleId;
        		vm.msgData.push(vm.noCheck);
        	}
            upida.post("sys/message/saveSetMsgList",vm.msgData).then(function (data) {
            	vm.setMessageList = data;
            	vm.setMessageData = [];
            	vm.msgData = [];
            	vm.hiddenMessageSet();
                MainCtrl.unblockUI();
                vm.clicks = true;// tj 2017-09-07  用户是否多次点击
            });
    	}
    }
	
	// 设置form域
	vm.setFormScope = function (scope)
	{
		vm.edit.formScope = scope;
	};
	// 删除
	vm.delRole = function (index)
	{
		vm.delRecordId = vm.page.data.list[index].recordId;
		vm.user = angular.copy(vm.page.data.list[index]);
		for (var i = 0; i < vm.loginUser.roleList.length; i++)
		{
			if (vm.user.recordId === vm.loginUser.roleList[i].recordId)
			{
				vm.message = "您自己的角色,不能删除哦！";
				$('#static').modal();
				return;
			}
		}
		MainCtrl.blockUI({
    	   animate: true,
    	});
		vm.roletouser.roleId = vm.user.recordId; // 设置用户角色表中的角色编号
		upida.post("sys/role/findRoleUserList", vm.roletouser).then(function (data)
		{
			vm.roleandUser = data;
			if (data.length > 0)
			{
				vm.message = "角色已经有引用，不能删除！";
				$('#static').modal();
				MainCtrl.unblockUI();
				return;
			}
			else
			{
				// 提示信息
				$('#staticRemove').modal();
				MainCtrl.unblockUI();
			}
			
		});

	};
	// 做字典值的删除操作
	vm.doDelRole = function ()
	{
		if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    
    	    MainCtrl.blockUI({
    	    	   animate: true,
    	    	});
    			// 删除前先判断是否有被引用 （用户角色中间表）
    			upida.post("sys/role/judge/" + vm.delRecordId, null).then(function (data)
    			{
    				if (data > 0)
    				{
    					// 有引用

    				}
    				else
    				{
    					// 没有
    				}
    				MainCtrl.unblockUI();
    			});
    			
    			upida.post("sys/role/delete/" + vm.delRecordId, null).then(function (data)
    			{
    				// 重新查询数据
    				vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    				vm.delRecordId = -1;
    				vm.message = data;
    				$('#static').modal();
    				vm.clicks = true;// tj 2017-09-07  用户是否多次点击
    			});
    	}
	};
	// 加载权限
	function loadRight ()
	{
		vm.queryAll = CommonUtil.dataRangeIsAll("11103", BaseUtil.getMenuList());
		MainCtrl.blockUI({
			animate: true,
		});
		upida.get("common/rightall?prefix=sys:role").then(function (data)
		{
			vm.right.view = data.view;
			vm.right.edit = data.edit;
			vm.right.manage = data.manage;
			if (vm.right.edit)
			{
				loadData();
			}
			
			vm.page.pageNo = 1;
			// 初始化第一页，条件为空
			vm.init(vm.page.pageNo, vm.page.pageSize, [], vm.page.url);
			initChar();
		});
	}
	// 角色保存
	vm.roleSave = function ()
	{
		if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    
    	    vm.roleStr = vm.usersOfRole;
    		vm.tepmarray = [];
    		angular.forEach(vm.usersOfRole, function (p)
    		{
    			if (p.check)
    			{
    				// 给编号赋值
    				vm.roletouser.userId = p.recordId;
    				vm.tepmobj = {};
    				vm.tepmobj.roleId = vm.roletouser.roleId;
    				vm.tepmobj.userId = p.recordId;
    				vm.tepmarray.push(vm.tepmobj);
    				vm.clicks = true;// tj 2017-09-07  用户是否多次点击
    				return;
    			}
    		});
    		for(var i = 0;i < vm.tepmarray.length;i++){
    			MainCtrl.blockUI({
    	           animate: true,
    	    	});
    			upida.post("sys/role/saveOfroleuser", vm.tepmarray[i]).then(function (data){
    				// 再去查询
    				upida.post("sys/role/findRoleUserList", vm.roletouser).then(function (data){
    					vm.roleandUser = data;
    					MainCtrl.unblockUI();
    					vm.clicks = true;// tj 2017-09-07  用户是否多次点击
    				});
    			});
    		}
    	}
	};

	// 设置Form的域
	vm.setRoleFormScope = function (scope)
	{
		vm.roleEdit.formScope = scope;
	};
	// 清空角色选择项
	function clearRoleCheck ()
	{
		angular.forEach(vm.usersOfRole, function (p)
		{
			p.check = false;
		});
	}
	// 初始化角色选择项
	function initRoleCheck ()
	{
		angular.forEach(vm.user.roleList, function (p)
		{
			setCheck(p, vm.tempRoles);
		});
	}
	function loadData ()
	{	
		MainCtrl.blockUI({
    		animate: true,
    	});
		// 加载所有用户
		upida.get("sys/user/showall?queryAll=" + vm.queryAll).then(function (data)
		{
			vm.usersOfRole = data;
			vm.tempRoles = angular.copy(data);
			vm.tempCustRoles = angular.copy(data);
			vm.tempUserRoles = angular.copy(data);
			
			MainCtrl.unblockUI();
		});
	}
	// 初始化字母
	vm.userChar = [];
	function initChar()
	{
		vm.userChar = [];
		vm.userChar = [
          {'name':"所有","flag":true},{'name':"A","flag":false},{'name':"B","flag":false},{'name':"C","flag":false},{'name':"D","flag":false},{'name':"E","flag":false},{'name':"F","flag":false},{'name':"G","flag":false},
          {'name':"H","flag":false},{'name':"I","flag":false},{'name':"J","flag":false},{'name':"K","flag":false},{'name':"L","flag":false},{'name':"M","flag":false},{'name':"N","flag":false},
          {'name':"O","flag":false},{'name':"P","flag":false},{'name':"Q","flag":false},{'name':"R","flag":false},{'name':"S","flag":false},{'name':"T","flag":false},{'name':"U","flag":false},
          {'name':"V","flag":false},{'name':"W","flag":false},{'name':"X","flag":false},{'name':"Y","flag":false},{'name':"Z","flag":false}
		]
	}
	
	vm.getUserByChar = function(name)
	{
		MainCtrl.blockUI({
    	    animate: true,
    	});
		angular.forEach(vm.userChar, function (p)
		{
		    if(p.name == name){
		    	p.flag = true;
		    }else{
		    	p.flag = false;
		    }
		});
		vm.tempRolestemp = [];
		vm.usersOfRole = [];
		vm.tempRoles = [];
		vm.tempCustRoles = [];
		vm.tempUserRoles = [];
		vm.userCharDeail = {};
		vm.userCharDeail.userChar = name;
		vm.userCharDeail.queryAll = vm.queryAll;
		upida.post("sys/user/showallByChar", vm.userCharDeail).then(function (data){
			vm.tempRoles = angular.copy(data);
			vm.tempCustRoles = angular.copy(data);
			vm.tempUserRoles = angular.copy(data);
			vm.showRoleSet();
			MainCtrl.unblockUI();
		});
	}
	
	vm.showRoleSetInit = function()
	{
		initChar();
		MainCtrl.blockUI({
    		animate: true,
    	});
		// 加载所有用户
		upida.get("sys/user/showall?queryAll=" + vm.queryAll).then(function (data)
		{
			vm.usersOfRole = data;
			vm.tempRoles = angular.copy(data);
			vm.tempCustRoles = angular.copy(data);
			vm.tempUserRoles = angular.copy(data);
			vm.showRoleSet();
			MainCtrl.unblockUI();
		});
	}
	
	$scope.$on("$stateChangeSuccess", function ()
	{
		upida.setScope($scope);
		loadRight();
	});
} ]);