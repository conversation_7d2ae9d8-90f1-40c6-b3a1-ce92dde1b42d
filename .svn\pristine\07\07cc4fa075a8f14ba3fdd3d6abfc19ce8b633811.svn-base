package com.kyb.pcberp.common.utils;

import com.kyb.pcberp.modules.eg.entity.EgProcess;
import com.kyb.pcberp.modules.production.entity.ProduceBatch;
import com.kyb.pcberp.modules.production.entity.ProduceBatchDetail;
import com.kyb.pcberp.modules.quality.entity.Discard;
import com.kyb.pcberp.modules.quality.entity.InsectTaskDetail;
import com.kyb.pcberp.modules.quality.entity.Inspect;
import com.kyb.pcberp.modules.quality.entity.Rework;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

import java.util.*;

/**
 * <AUTHOR> 品质检测工具类 来源： 1、生产 产线信息（批次明细id、工序） 2、客诉
 * <p>
 * 3、修复
 */
public class InspectUtils {

    public List<Inspect> generateInspectByTasks(List<InsectTaskDetail> taskDetail,User user,Company company) {
        List<Inspect> result = new ArrayList<>();
        // 按批次+批次明细分组
        Map<String, List<InsectTaskDetail>> taskMap = new HashMap<>();
        List<InsectTaskDetail> mapTemp = null;
        for (InsectTaskDetail detail : taskDetail) {
            String key = detail.getProduceBatchId() + "-" + detail.getProduceBatchDetailId();
            if (taskMap.containsKey(key)) {
                taskMap.get(key).add(detail);
            } else {
                mapTemp = new ArrayList<>();
                mapTemp.add(detail);
                taskMap.put(key, mapTemp);
            }
        }
        for (String mapK : taskMap.keySet()) {
            result.add(generateInspectByOneBatch(taskMap.get(mapK),user,company));
        }
        return result;
    }

    /**
     * 同一个生产批次+检测类型，生成品质检测记录
     *
     * @param taskDetail
     * @return
     */
    public Inspect generateInspectByOneBatch(List<InsectTaskDetail> taskDetail,User user,Company company) {
        // 一个批次相同信息
        String produceBatchid = taskDetail.get(0).getProduceBatchId();
        String produceBatchDetailId = taskDetail.get(0).getProduceBatchDetailId();
        User createdBy = user;
        User madeUser = new User();
        madeUser.setRecordId(taskDetail.get(0).getMadeUserId());
        // Date checkedDate = taskDetail.get(0).getMadeDate();
        int type = taskDetail.get(0).getSourceType() == null ? 1 : taskDetail.get(0).getSourceType();
        if (!StringUtils.isEmpty(taskDetail.get(0).getComplaintId())) {
            type = 3;
        }
        int reworkType = 4;// 生产中
        if (type == 2) {
            reworkType = 1;// 客诉提仓
        }
        int checkType = taskDetail.get(0).getTestWay() == null ? 1 : taskDetail.get(0).getTestWay();
        String taskId = taskDetail.get(0).getTaskId();
        int producePcsQty = 0;
        int discardQty = 0;
        // int reworkQty = 0;
        // 设置报废集合
        List<Discard> discardList = new ArrayList<>();
        List<Rework> reworkList = new ArrayList<>();
        Discard discard = null;
        Rework rework = null;
        EgProcess process = null;
        int replaceQty = 0;
        for (InsectTaskDetail task : taskDetail) {
            int badQty = task.getUnGoodQty() == null ? 0 : task.getUnGoodQty();
            int checkedQty = task.getCheckQty() == null ? 0 : task.getCheckQty();
            producePcsQty += checkedQty;
            if (task.getInspectType() != null && task.getInspectType() == 1) {
                discardQty += badQty;
            } else if (task.getInspectType() != null && task.getInspectType() == 3) {
                replaceQty += checkedQty;
            } else if (task.getInspectType() != null && task.getInspectType() == 4) {
                replaceQty += checkedQty;
            }
            /*
             * else { reworkQty+=badQty; }
             */

            // reworkQty+=reQty;
            if (task.getInspectType() != null && task.getInspectType() == 1) {
                discard = new Discard();
                discard.setCompany(company);
                discard.setDiscardPcsQty(badQty);
                //报废A、B板选择  默认优先报废A板
                if (task.getTakeOverQtyPcsA() != null && task.getTakeOverQtyPcsA() >= badQty) {
                    //扣A板
                    discard.setDiscardPcsQtyA(badQty);
                } else if (task.getTakeOverQtyPcsB() != null && task.getTakeOverQtyPcsB() >= badQty) {
                    //扣B板
                    discard.setDiscardPcsQtyB(badQty);
                } else if (task.getTakeOverQtyPcsB() != null && task.getTakeOverQtyPcsB() > 0) {
                    //将B板数扣完，其它默认扣A板
                    discard.setDiscardPcsQtyB(task.getTakeOverQtyPcsB());
                    discard.setDiscardPcsQtyA(badQty - task.getTakeOverQtyPcsB());
                } else {
                    //扣A板
                    discard.setDiscardPcsQtyA(badQty);
                }
                discard.setDiscardCause(task.getDiscardCause());
                discard.setDiscardCauseOne(task.getDiscardUnit());
                process = new EgProcess();
                process.setRecordId(task.getProcessId());
                discard.setDutyProcess(process);
                discard.setCreatedBy(createdBy);
                discard.setRemark(task.getRemark());
                discard.setTaskDetailId(task.getRecordId());
                discardList.add(discard);
            } else if (task.getInspectType() != null && task.getInspectType() == 2) {
                rework = new Rework();
                rework.setCompany(company);
                rework.setReworkQty(badQty);
                rework.setReworkCause(task.getDiscardCause());
                process = new EgProcess();
                process.setRecordId(task.getProcessId());
                rework.setDutyProcess(process);
                rework.setCreatedBy(createdBy);
                rework.setReworkType(reworkType);
                rework.setReworker(createdBy);
                rework.setChecker(new User(task.getMadeUserId()));
                rework.setRemark(task.getRemark());
                rework.setTaskDetailId(task.getRecordId());
                reworkList.add(rework);
            }
        }

        Inspect inspect = new Inspect();
        inspect.setCompany(company);
        inspect.setType(type);
        ProduceBatch produceBatch = new ProduceBatch();
        produceBatch.setNo(taskDetail.get(0).getCraftNo());
        produceBatch.setRecordId(produceBatchid);
        inspect.setProduceBatch(produceBatch);
        ProduceBatchDetail pd = new ProduceBatchDetail();
        pd.setRecordId(produceBatchDetailId);
        inspect.setProduceBatchDetail(pd);
        // 获取过数记录最新的工序
        process = new EgProcess();
        process.setRecordId(taskDetail.get(0).getCurrProcessId());
        inspect.setProcess(process);
        inspect.setManufType(600101);
        inspect.setProducePcsQty(producePcsQty);
        inspect.setPassPcsQty(producePcsQty - discardQty);
        inspect.setFailedPcsQty(discardQty);
        inspect.setOperator(createdBy);
        inspect.setChecker(madeUser);
        inspect.setCheckDate(new Date());
        inspect.setRemark(null);
        inspect.setStatus("900605");
        inspect.setCheckType(checkType);
        inspect.setTaskId(taskId);
        inspect.setReplenishQty(discardQty);
        inspect.setDiscardList(discardList);
        inspect.setReworkList(reworkList);
        inspect.setReplaceQty(replaceQty);
        return inspect;
    }

}
