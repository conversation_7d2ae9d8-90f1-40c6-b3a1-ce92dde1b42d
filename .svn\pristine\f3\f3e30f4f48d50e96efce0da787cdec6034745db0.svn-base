const auditState = {
	auditList: [],
	audit: {},
	comList: [],
	empList: [],
	companyList: [],
	taskList: [],
	task: {},
	circleList: [],
	postList: [],
	myComList: [],
	postDeail: {},
	circle: {},
	cirUserList: [],
	commentList: [],
	noticeList: [],
	applyUserList: [],
	consultList:[],
	reportList: [],
	report: {},
	shareList: [],
	auditGroupDetailList: [],
	auditGroupDetailFlag:true,
	auditData:{},
	empInfo:{},
	expenseTypeList:[],

	erpCompanyList: [],
	supplychainComList: [],
	customerList: [],
	saleCompanyList:[],
	organizationAuditList:[],
	categoryAuditList:[],
}
const auditMutations = {
	setAuditList (state, args) {
		// 将查询条件存入缓存
		window.localStorage.setItem('auditConditions', JSON.stringify(args));
		var emp = store.state.myStore.emp;
		var user = store.state.myStore.user;
		var wechatAudit = {};
		if(args){
			wechatAudit = args;
		}
		wechatAudit.empId = emp.recordId;
		if(!wechatAudit.flag){
			wechatAudit.flag = 1;
		}
		wechatAudit.phone = user.phone;
		$.ajax({
			type:"post",
	     	url:ctx + "/f/wechat/kybsoftOA/getAuditList",
	     	data:JSON.stringify(wechatAudit),
	     	contentType:"application/json",
	     	success:function(data)
	     	{
	     		state.auditList = data;
	     	}
	    })
	},
	setAudit (state, args) {
		var emp = store.state.myStore.emp;
		var user = store.state.myStore.user;
		let audit = JSON.parse(window.localStorage.getItem('audit'))
		audit.auditUserId = emp.recordId;
		if(user.phone){
			audit.phone = user.phone;
		}
		$.ajax({
			type:"post",
	     	url:ctx + "/f/wechat/kybsoftOA/getAudit",
	     	data:JSON.stringify(audit),
	     	contentType:"application/json",
	     	success:function(data)
	     	{
	     		state.audit = data;
	     	}
	    })
	},
	setEmpList (state, args) {
		args.departId = args.company.recordId
		$.ajax({
			type:"post",
	     	url:ctx + "/f/wechat/kybsoftOA/getEmpList",
	     	data:JSON.stringify(args),
	     	contentType:"application/json",
	     	success:function(data)
	     	{
	     		state.empList = data;
	     	}
	    })
	},
	setComList (state, args) {
		if(args && args.recordId){
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftOA/getComList",
		     	data:JSON.stringify(args),
		     	contentType:"application/json",
		     	success:function(data)
		     	{
		     		state.comList = data;
		     	}
		    })
		}
	},
	setCompanyList (state, args){
		if(args && args.recordId){
			var depart = {}
			depart.recordId = args.company.recordId
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftOA/getCompanyList",
		     	data:JSON.stringify(depart),
		     	contentType:"application/json",
		     	success:function(data)
		     	{
		     		state.companyList = data;
		     	}
		    })
		}
	},
	setTaskList (state, args) {
		var phone = store.state.myStore.phone;
		if(phone){
			var search = {};
			if(args){
				search = args;
			}
			search.phone = phone;
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftOA/getTaskList",
		     	data:JSON.stringify(search),
		     	contentType:"application/json",
		     	success:function(data)
		     	{
		     		state.taskList = data;
		     	}
		    })
		}
	},
	setTask (state, args) {
		$.ajax({
			type:"post",
	     	url:ctx + "/f/wechat/kybsoftOA/getTask",
	     	data:window.localStorage.getItem('task'),
	     	contentType:"application/json",
	     	success:function(data)
	     	{
	     		state.task = data;
	     	}
	    })
	},
	setCircleList: function(state, args){
		var user = store.state.myStore.user;
		if(user && user.userId){
			var report = {};
			if(args){
				report = args;
			}
			report.userId = user.userId;
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftMsg/myCircle",
		     	data:JSON.stringify(report),
		     	contentType:"application/json",
		     	success:function(data)
		     	{
		     		state.circleList = data;
		     	}
		    })
		}
	},
	setPostList: function(state, args){
		var user = store.state.myStore.user;
		if(user && user.userId){
			var report = {};
			if(args){
				report = args;
			}
			report.userId = user.userId;
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftMsg/circleDynamic",
		     	data:JSON.stringify(report),
		     	contentType:"application/json",
		     	success:function(data)
		     	{
		     		state.postList = data;
		     	}
		    })
		}
	},
	setMyComList: function(state, args) {
		var user = store.state.myStore.user;
		if(user && user.userId){
			var com = {};
			if(args){
				com = args;
			}
			com.userId = user.userId;
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftMsg/myCompany",
		     	data:JSON.stringify(com),
		     	contentType:"application/json",
		     	success:function(data)
		     	{
		     		state.myComList = data;
		     	}
		    })
		}
	},
	setPostDeail: function(state, args){
		$.ajax({
			type:"post",
	     	url:ctx + "/f/wechat/kybsoftMsg/getPost",
	     	data:window.localStorage.getItem('postDeail'),
	     	contentType:"application/json",
	     	success:function(data)
	     	{
	     		state.postDeail = data;
	     	}
	    })
	},
	setCirPostList: function(state, args){
		this.postList = [];
		var cir = {};
		cir.recordId = window.localStorage.getItem('circleId');
		$.ajax({
			type:"post",
	     	url:ctx + "/f/wechat/kybsoftMsg/getCirclePost",
	     	data:JSON.stringify(cir),
	     	contentType:"application/json",
	     	success:function(data)
	     	{
	     		state.postList = data;
	     	}
	    })
	},
	setCirUserList: function(state, args){
		var cir = {};
		cir.recordId = window.localStorage.getItem('circleId');
		$.ajax({
			type:"post",
	     	url:ctx + "/f/wechat/kybsoftMsg/getCircleUserList",
	     	data:JSON.stringify(cir),
	     	contentType:"application/json",
	     	success:function(data)
	     	{
	     		state.cirUserList = data;
	     	}
	    })
	},
	setCommentList: function(state, args){
		if(args && args.userId){
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftMsg/privateLetter",
		     	data:JSON.stringify(args),
		     	contentType:"application/json",
		     	success:function(data)
		     	{
		     		state.commentList = data;
		     	}
		    })
		}
	},
	setNoticeList: function(state, args){
		if(args && args.userId){
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftMsg/getNoticeList",
		     	data:JSON.stringify(args),
		     	contentType:"application/json",
		     	success:function(data)
		     	{
		     		state.noticeList = data;
		     	}
		    })
		}
	},
	setAllCircle: function(state, args){
		var wechatCircle = {};
		if(args){
			wechatCircle = args;
		}
		$.ajax({
			type:"post",
	     	url:ctx + "/f/wechat/kybsoftMsg/getAllCircle",
	     	data:JSON.stringify(wechatCircle),
	     	contentType:"application/json",
	     	success:function(data)
	     	{
	     		state.circleList = data;
	     	}
	    })
	},
	setCircle: function(state, args){
		if(args && args.userId){
			var cir = {};
			cir.userId = args.userId;
			cir.recordId = window.localStorage.getItem('circleId');
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftMsg/getCircle",
		     	data:JSON.stringify(cir),
		     	contentType:"application/json",
		     	success:function(data)
		     	{
		     		state.circle = data;
		     	}
		    })
		}
	},
	setApplyUserList: function(state, args){
		var cir = {};
		cir.recordId = window.localStorage.getItem('circleId');
		$.ajax({
			type:"post",
	     	url:ctx + "/f/wechat/kybsoftMsg/getApplyCircleList",
	     	data:JSON.stringify(cir),
	     	contentType:"application/json",
	     	success:function(data)
	     	{
	     		state.applyUserList = data;
	     	}
	    })
	},
	setConsultList: function(state, args){
		var list = JSON.parse(window.localStorage.getItem('consultList'));
		if(!list || list.length < 1){
			list = [];
		}
		state.consultList = list;
	},
	setReportList (state, args) {
		var phone = store.state.myStore.phone;
		if(phone){
			var search = {};
			if(args){
				search = args;
			}
			search.phone = phone;
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftOA/getReportList",
		     	data:JSON.stringify(search),
		     	contentType:"application/json",
		     	success:function(data)
		     	{
		     		state.reportList = data;
		     	}
		    })
		}
	},
	setReport (state, args) {
		args.superEmpId = store.state.myStore.emp.recordId;
		state.report = args
 		state.shareList = []
		$.ajax({
			type:"post",
	     	url:ctx + "/f/wechat/kybsoftOA/getReport",
	     	data:JSON.stringify(args),
	     	contentType:"application/json",
	     	success:function(data)
	     	{
	     		if(data && data.recordId){
	     			state.report = data
	     			if(data.sharesList && data.sharesList.length > 0){
		     			state.shareList = data.sharesList;
		     		}
	     		}
	     	}
	    })
	},
	setAuditGroupDetailFlag(state, args) {
		state.auditGroupDetailFlag = !state.auditGroupDetailFlag
	},
	setAuditData(state, args) {
		state.auditData = {}
		if(args.qty){
			state.auditData.qty = Number(args.qty)
		}
		if(args.amount){
			state.auditData.amount = Number(args.amount)
		}
	},
	setAuditGroupDetailList(state, args) {
		state.auditGroupDetailList = args
	},
	setEmpInfo(state, args) {
		if(args && args.recordId){
			$.ajax({
				type:"post",
		     	url:ctx + "/f/wechat/kybsoftOA/getDepartureEmpInfo",
		     	data:JSON.stringify(args),
		     	contentType:"application/json",
		     	success:function(data)
		     	{
		     		state.empInfo = data
		     	}
		    })
		}
	},
	getExpenseTypeList (state, args) {
		$.ajax({
			type:"post",
	     	url:ctx + "/f/wechat/kybsoftOA/getExpenseTypeList",
	     	data:JSON.stringify(args),
	     	contentType:"application/json",
	     	success:function(data)
	     	{
	     		state.expenseTypeList = data
	     	}
	    })
	},
	getSupplychainApplyData (state, args) {
		state.erpCompanyList = [];
		state.supplychainList = [];
		state.customerList = [];
		$.ajax({
			type:"post",
			url:ctx + "/f/wechat/kybsoftOA/getSupplychainApplyData",
			data:JSON.stringify(args),
			contentType:"application/json",
			success:function(data)
			{
				if (data) {
					state.customerList = data.customerList
					if (data.groupCompanyList && data.groupCompanyList.length > 0) {
						for (let groupCompany of data.groupCompanyList) {
							if (groupCompany.supplyFlag) {
								state.supplychainComList.push(groupCompany)
							} else {
								state.erpCompanyList.push(groupCompany)
							}
						}
					}
				}
			}
		});
	},
	getSaleCompanyList(state) {
		state.saleCompanyList = [];
		$.ajax({
			type:"post",
			url:ctx + "/f/wechat/kybsoftOA/getSaleCompanyList",
			contentType:"application/json",
			success:function(data)
			{
				if (data) {
					state.saleCompanyList = data
				}
			}
		});
	},
	getOrganizationAuditList(state){
		state.organizationAuditList = [];
		$.ajax({
			type:"post",
			url:ctx + "/f/wechat/kybsoftOA/getOrganizationAuditList",
			contentType:"application/json",
			success:function(data)
			{
				if (data) {
					state.organizationAuditList = data
				}
			}
		});
	},
	getCategoryAuditList(state){
		state.categoryAuditList = [];
		$.ajax({
			type:"post",
			url:ctx + "/f/wechat/kybsoftOA/getCategoryAuditList",
			contentType:"application/json",
			success:function(data)
			{
				if (data) {
					state.categoryAuditList = data
				}
			}
		});
	}

}
const auditActions = {
	setAuditList (context, args) {
		context.commit('setAuditList', args)
	},
	setAudit (context, args) {
		context.commit('setAudit', args)
	},
	setEmpList (context, args) {
		context.commit('setEmpList', args)
	},
	setComList (context, args) {
		context.commit('setComList', args)
	},
	setCompanyList (context, args) {
		context.commit('setCompanyList', args)
	},
	setTaskList (context, args) {
		context.commit('setTaskList', args)
	},
	setTask (context, args) {
		context.commit('setTask', args)
	},
	setCircleList (context, args) {
		context.commit('setCircleList', args)
	},
	setPostList (context, args) {
		context.commit('setPostList', args)
	},
	setMyComList (context, args) {
		context.commit('setMyComList', args)
	},
	setPostDeail (context, args) {
		context.commit('setPostDeail', args)
	},
	setCirPostList (context, args) {
		context.commit('setCirPostList', args)
	},
	setCirUserList (context, args) {
		context.commit('setCirUserList', args)
	},
	setCommentList (context, args) {
		context.commit('setCommentList', args)
	},
	setNoticeList (context, args) {
		context.commit('setNoticeList', args)
	},
	setAllCircle (context, args) {
		context.commit('setAllCircle', args)
	},
	setCircle (context, args) {
		context.commit('setCircle', args)
	},
	setApplyUserList(context, args){
		context.commit('setApplyUserList', args)
	},
	setConsultList(context, args){
		context.commit('setConsultList', args)
	},
	setReportList (context, args) {
		context.commit('setReportList', args)
	},
	setReport (context, args) {
		context.commit('setReport', args)
	},
	setAuditGroupDetailFlag(context, args) {
		context.commit('setAuditGroupDetailFlag', args)
	},
	setAuditData(context, args) {
		context.commit('setAuditData', args)
	},
	setAuditGroupDetailList(context, args) {
		context.commit('setAuditGroupDetailList', args)
	},
	setEmpInfo(context, args) {
		context.commit('setEmpInfo', args)
	},
	getExpenseTypeList(context, args) {
		context.commit('getExpenseTypeList', args)
	},
	getSupplychainApplyData (context, args) {
		context.commit('getSupplychainApplyData')
	},
	getSaleCompanyList (context) {
		context.commit('getSaleCompanyList')
	},
	getOrganizationAuditList (context) {
		context.commit('getOrganizationAuditList')
	},
	getCategoryAuditList (context) {
		context.commit('getCategoryAuditList')
	},
}
const auditGetters = {
}

const auditStore = {
  namespaced: true,
  state: auditState,
  mutations: auditMutations,
  actions: auditActions,
  getters: auditGetters
}
