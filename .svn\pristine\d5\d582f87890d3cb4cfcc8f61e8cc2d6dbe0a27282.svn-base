<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.icloud.erp.erp.dao.Icloud_ContractDetailErpDao">
    <update id="updateStatus">
		UPDATE sl_contract_detail SET
			status = #{status}
		WHERE recordId = #{recordId}
	</update>

    <select id="getDetialByGroupCenter" resultType="com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_ContractDetailErp">
       	SELECT
			b.recordId,
			b.companyId AS "company.recordId",
			b.subTotal,
			b.orderDeailArea,
			b.contractId AS "contractId",
			c.no AS "cntractNo"
		FROM icloud_group_center a
		LEFT JOIN sl_contract_detail b ON b.recordId = a.contactDeailId AND b.activeFlag = 1
		LEFT JOIN sl_contract c ON c.recordId = b.contractId AND c.activeFlag = 1
		WHERE a.recordId = #{recordId} LIMIT 1

    </select>

    <select id="getContractDetailErpData" resultType="com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_ContractDetailErp">
		SELECT
			a.recordId,
			a.quantity,
			a.deliveryDays,
			a.customerfilename,
			a.customermaterialno,
			a.referenceType,
			a.referenceDesc,
			a.remark,
			a.deliveryDate,
			a.batchNumber,
			a.groupCenterId,
			a.madePcsPrice,
			a.madeSupplierId,
			a.splitDetailId,
			a.prdEngineeringFee,
			a.prdMouldFee,
			a.prdTestShelfFee,
			a.prdDetailRemark,
			a.lnPrice,
			ms.name AS "madeSupplierName",
			c.recordId AS "contractCraftList.recordId",
			c.`no` AS "contractCraftList.no",
			c.customerModel AS "contractCraftList.customerModel",
			c.unitWidth AS "contractCraftList.unitWidth",
			c.unitLength AS "contractCraftList.unitLength",
			c.pnlWidth AS "contractCraftList.pnlWidth",
			c.pnlLength AS "contractCraftList.pnlLength",
			c.pnlDivisor AS "contractCraftList.pnlDivisor",
			c.deliverySize AS "contractCraftList.deliverySize",
			c.filmDescript AS "contractCraftList.filmDescript",
			c.referenceType AS "contractCraftList.referenceType",
			c.referenceDesc AS "contractCraftList.referenceDesc",
			c.materialType AS "contractCraftList.materialType",
			c.boardLevel AS "contractCraftList.boardLevel",
			c.boardThickness AS "contractCraftList.boardThickness",
			c.surfaceProcess AS "contractCraftList.surfaceProcess",
			c.copperCladThickness AS "contractCraftList.copperCladThickness",
			c.solderMaskType AS "contractCraftList.solderMaskType",
			c.characterType AS "contractCraftList.characterType",
			c.shapingWay AS "contractCraftList.shapingWay",
			c.drillingWay AS "contractCraftList.drillingWay",
			c.holeSolderMask AS "contractCraftList.holeSolderMask",
			c.packingType AS "contractCraftList.packingType",
			c.testMethod AS "contractCraftList.testMethod",
			c.blueFilmFlag AS "contractCraftList.blueFilmFlag",
			c.newMouldFlag AS "contractCraftList.newMouldFlag",
			c.newShelfFlag AS "contractCraftList.newShelfFlag",
			c.holeThiaOilFlag AS "contractCraftList.holeThiaOilFlag",
			c.remark AS "contractCraftList.remark",
			c.`status` AS "contractCraftList.status",
			c.lingeSpacing AS "contractCraftList.lingeSpacing",
			c.smallAperture AS "contractCraftList.smallAperture",
			c.halAhole AS "contractCraftList.halAhole",
			c.buryBlindHole AS "contractCraftList.buryBlindHole",
			c.resistance AS "contractCraftList.resistance",
			c.deliveryUrgent AS "contractCraftList.deliveryUrgent",
			c.banLiaoSupplier AS "contractCraftList.banLiaoSupplier.recordId",
			c.youMoSupplier AS "contractCraftList.youMoSupplier.recordId",
			c.daore AS "contractCraftList.daore",
			c.naiya AS "contractCraftList.naiya",
			c.pliesnumber AS "contractCraftList.pliesnumber",
			c.throughHole AS "contractCraftList.throughHole",
			c.countersinkHole AS "contractCraftList.countersinkHole",
			c.shippingAddress AS "contractCraftList.shippingAddress",
			c.board AS "contractCraftList.board",
			c.inkType AS "contractCraftList.inkType",
			c.specialCraft AS "contractCraftList.specialCraft",
			d.recordId AS "pricees.recordId",
			d.price AS "pricees.price",
			d.engineeringFee AS "pricees.engineeringFee",
			d.sampleFee AS "pricees.sampleFee",
			d.othersFee AS "pricees.othersFee",
			d.testShelfFee AS "pricees.testShelfFee",
			d.mouldFee AS "pricees.mouldFee",
			d.filmFee AS "pricees.filmFee",
			d.orderLowLimit AS "pricees.orderLowLimit",
			d.mouldLimit AS "pricees.mouldLimit",
			d.testShelfLimit AS "pricees.testShelfLimit",
			d.engineeringLimit AS "pricees.engineeringLimit",
			sc.recordId AS "contract.recordId",
			sc.customerId AS "contract.customerId",
			sc.branchId AS "contract.branchId",
			sc.`no` AS "contract.no",
			sc.customerPo AS "contract.customerPo",
			sc.orderDate AS "contract.orderDate",
			sc.taxDescript AS "contract.taxDescript",
			sc.currencyType AS "contract.currencyType",
			sc.payWay AS "contract.payWay",
			sc.deliveryWay AS "contract.deliveryWay",
			sc.deliveryPlace AS "contract.deliveryPlace",
			sc.freightWay AS "contract.freightWay",
			sc.remark AS "contract.remark",
			sc.paycause AS "contract.paycause",
			sc.status AS "contract.status",
			IFNULL(igc.notiCraftNo,a.notiCraftNo) AS "notiCraftNo",
			IFNULL(igc.genCraftNo,a.genCraftNo) AS "genCraftNo",
			igc.urgentFlag,
			(
				SELECT COUNT(1) FROM sl_notification WHERE contractDetailId = a.recordId AND companyId = a.companyId AND a.status <![CDATA[<>]]> 200406
			) AS "genNotiFlag"
		FROM sl_contract_detail a
		LEFT JOIN sl_contract_craft c ON c.recordId = a.craftId
		AND c.companyId = a.companyId
		AND c.activeFlag = 1
		LEFT JOIN sl_price d ON a.priceId = d.recordId
		AND d.companyId = a.companyId
		AND d.activeFlag = 1
		LEFT JOIN sl_contract sc ON sc.recordId = a.contractId
		AND sc.companyId = a.companyId
		AND sc.activeFlag = 1
		LEFT JOIN md_supplier ms ON ms.recordId = a.madeSupplierId
		LEFT JOIN icloud_group_center igc ON igc.recordId = a.groupCenterId
		LEFT JOIN md_dict_value bb ON bb.recordId = a.referenceType
		WHERE a.recordId = #{recordId} AND a.activeflag = 1
    </select>

    <select id="getUrgentApprovalData" resultType="com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_ContractDetailErp">
		SELECT
			a.orderDeailArea,
			c.`value` AS "remark",
			d.estimateDate AS "estimatedDate",
			IFNULL(d.genCraftNo,e.no) AS "contractCraftList.no"
		FROM sl_contract_detail a
		LEFT JOIN sl_contract_craft b ON b.recordId = a.craftId
		LEFT JOIN md_dict_value c ON c.recordId = b.shapingWay
		LEFT JOIN icloud_group_center d ON d.recordId = a.groupCenterId AND d.activeFlag = 1
		LEFT JOIN sl_contract_craft e ON e.recordId = d.notiCraftNo AND e.activeFlag = 1
		WHERE a.recordId = #{recordId}
	</select>

    <select id="getSalePrecent" resultType="Double">
        SELECT
        IFNULL(d.salePrecent,f.salePrecent)
        FROM sl_contract_detail a
        LEFT JOIN icloud_group_center igc ON igc.recordId = a.groupCenterId AND igc.activeFlag = 1
        LEFT JOIN sl_contract_detail b ON b.recordId = igc.contactDeailId
        LEFT JOIN sl_contract c ON c.recordId = b.contractId
        LEFT JOIN md_customer d ON d.recordId = c.customerId
        LEFT JOIN sl_contract e ON e.recordId = a.contractId
        LEFT JOIN md_customer f ON f.recordId = e.customerId
        WHERE a.activeFlag = 1 AND a.status <![CDATA[<>]]> 200205
        <if test="notiId != null and notiId != ''">
            AND a.recordId IN (SELECT contractDetailId FROM sl_notification WHERE recordId = #{notiId})
        </if>
        <if test="recordId != null and recordId != ''">
            AND a.contractId = #{recordId}
        </if>
        <if test="deailId != null and deailId != ''">
            AND a.recordId = #{deailId}
        </if>
        <if test="dataDetailIds != null and dataDetailIds != ''">
            AND a.recordId IN (${dataDetailIds})
        </if>
        LIMIT 1
    </select>

    <select id="getSumFeeAmount" resultType="com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_ContractDetailErp">
        SELECT
        SUM(IFNULL(a.subTotal,0)) AS "subTotal",
        SUM(CASE WHEN b.recordId IS NOT NULL THEN IFNULL(b.processFee,0) + IFNULL(b.materialFee,0) ELSE ROUND(IFNULL(a.madePcsPrice,0) * IFNULL(a.quantity,0) + IFNULL(a.prdEngineeringFee,0) + IFNULL(a.prdMouldFee,0) + IFNULL(a.prdTestShelfFee,0) + IFNULL(a.prdFilmFee,0) + IFNULL(a.prdOthersFee,0),2) END) AS "costFee"
        FROM sl_contract_detail a
        LEFT JOIN icloud_group_center b ON b.recordId = a.groupCenterId AND b.contactDeailId = a.recordId AND b.activeFlag = 1
        LEFT JOIN sl_contract_detail c ON c.recordId = b.contactDeailId
        LEFT JOIN md_dict_value d ON d.recordId = a.prdTaxDescript
        WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND a.`status` <![CDATA[<>]]> 200205
        AND (b.estimateRemark IS NOT NULL OR b.recordId IS NULL)
        <if test="recordId != null and recordId != ''">
            AND a.contractId = #{recordId}
        </if>
        <if test="deailId != null and deailId != ''">
            AND a.recordId = #{deailId}
        </if>
    </select>

    <select id="getNegotiatedApprovalData" resultType="com.kyb.pcberp.modules.icloud.erp.erp.pojo.Icloud_ContractDetailErp">
		SELECT
			b.`no` AS "contract.no",
			b.customerPo AS "contract.customerPo",
			c.customerModel AS "contractCraftList.customerModel",
			a.orderDeailArea AS "orderDeailArea"
		FROM
			sl_contract_detail a
				LEFT JOIN sl_contract b ON b.recordId = a.contractId
				LEFT JOIN sl_contract_craft c ON c.recordId = a.craftId
		WHERE a.recordId = #{recordId}
	</select>
</mapper>
