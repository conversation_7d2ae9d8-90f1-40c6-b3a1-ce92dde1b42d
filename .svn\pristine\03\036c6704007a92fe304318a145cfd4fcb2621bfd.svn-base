package com.kyb.pcberp.modules.approval.utils;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.modules.approval.dao.ApprovalDao;
import com.kyb.pcberp.modules.approval.dao.BackupAttachementsDao;
import com.kyb.pcberp.modules.approval.dao.BackupTwoDao;
import com.kyb.pcberp.modules.approval.entity.Approval;
import com.kyb.pcberp.modules.approval.entity.BackupTwo;
import com.kyb.pcberp.modules.approval.entity.Backups;
import com.kyb.pcberp.modules.approval.vo.ContractDeailVo;
import com.kyb.pcberp.modules.approval.vo.ContractVo;
import com.kyb.pcberp.modules.contract.dao.ContractDetailDao;
import com.kyb.pcberp.modules.contract.dao.NotificationDao;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.crm.dao.*;
import com.kyb.pcberp.modules.crm.entity.*;
import com.kyb.pcberp.modules.eg.entity.CardA;
import com.kyb.pcberp.modules.hr.depart_center.dao.Hr_MarkSorceDao;
import com.kyb.pcberp.modules.oa.dao.Oa_auditDao;
import com.kyb.pcberp.modules.production.dao.ProduceBatchDetailDao;
import com.kyb.pcberp.modules.production.entity.ProduceBatch;
import com.kyb.pcberp.modules.purch.dao.*;
import com.kyb.pcberp.modules.purch.entity.*;
import com.kyb.pcberp.modules.purch.service.PurchasingDetailService;
import com.kyb.pcberp.modules.purch.utils.IngredientsUtils;
import com.kyb.pcberp.modules.quality.dao.InspectDao;
import com.kyb.pcberp.modules.quality.entity.Discard;
import com.kyb.pcberp.modules.quality.entity.InsectTaskDetail;
import com.kyb.pcberp.modules.quality.entity.Inspect;
import com.kyb.pcberp.modules.quality.entity.Rework;
import com.kyb.pcberp.modules.stock.dao.InventoryDao;
import com.kyb.pcberp.modules.stock.dao.MaterialUseDao;
import com.kyb.pcberp.modules.stock.dao.RawmaterialStockDao;
import com.kyb.pcberp.modules.stock.entity.*;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import com.kyb.pcberp.modules.sys.dao.CustomerSalesAssistantDao;
import com.kyb.pcberp.modules.sys.dao.ParameterSetDao;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.CustomerSalesAssistant;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.wechat.dao.WechatOaDao;
import com.kyb.pcberp.modules.wechat.entity.IcloudBidding;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatAttachments;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatAudit;
import org.apache.shiro.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ErpAuditUtils
{
    private static Oa_auditDao oa_auditDao = SpringContextHolder.getBean(Oa_auditDao.class);

    private static ContractDetailDao contractDetailDao = SpringContextHolder.getBean(ContractDetailDao.class);

    private static NotificationDao notificationDao = SpringContextHolder.getBean(NotificationDao.class);

    private static CustomerProductTypeDao customerProductTypeDao = SpringContextHolder.getBean(CustomerProductTypeDao.class);

    private static ParameterSetDao parameterSetDao = SpringContextHolder.getBean(ParameterSetDao.class);

    private static PurchasingDetailDao purchasingDetailDao = SpringContextHolder.getBean(PurchasingDetailDao.class);

    private static IngredientsDao ingredientsDao = SpringContextHolder.getBean(IngredientsDao.class);

    private static PurchasingDao purchasingDao = SpringContextHolder.getBean(PurchasingDao.class);

    private static BiddingDao biddingDao = SpringContextHolder.getBean(BiddingDao.class);

    private static PurchasingDetailDeliverysDao purchasingDetailDeliverysDao = SpringContextHolder.getBean(PurchasingDetailDeliverysDao.class);

    private static PurchRawDao purchRawDao = SpringContextHolder.getBean(PurchRawDao.class);

    private static BackupTwoDao backupTwoDao = SpringContextHolder.getBean(BackupTwoDao.class);

    private static BackupAttachementsDao backupAttachementsDao = SpringContextHolder.getBean(BackupAttachementsDao.class);

    private static CustomerDao customerDao = SpringContextHolder.getBean(CustomerDao.class);

    private static CustomerContactDao customerContactDao = SpringContextHolder.getBean(CustomerContactDao.class);

    private static CustomerBranchDao customerBranchDao = SpringContextHolder.getBean(CustomerBranchDao.class);

    private static CustomerSalesAssistantDao customerSalesAssistantDao = SpringContextHolder.getBean(CustomerSalesAssistantDao.class);

    private static SupplierDao supplierDao = SpringContextHolder.getBean(SupplierDao.class);

    private static AccountsReceivableDao accountsReceivableDao = SpringContextHolder.getBean(AccountsReceivableDao.class);

    private static GoodsCheckDao goodsCheckDao = SpringContextHolder.getBean(GoodsCheckDao.class);

    private static AdjustBalanceDao adjustBalanceDao = SpringContextHolder.getBean(AdjustBalanceDao.class);

    private static AccountsPayableDao accountsPayableDao = SpringContextHolder.getBean(AccountsPayableDao.class);

    private static MaterialCheckDao materialCheckDao = SpringContextHolder.getBean(MaterialCheckDao.class);

    private static AdjustPayableDao adjustPayableDao = SpringContextHolder.getBean(AdjustPayableDao.class);

    private static InventoryDao inventoryDao = SpringContextHolder.getBean(InventoryDao.class);

    private static RejectApplicationDao rejectApplicationDao = SpringContextHolder.getBean(RejectApplicationDao.class);

    private static CompanyDao companyDao = SpringContextHolder.getBean(CompanyDao.class);

    private static InspectDao inspectDao = SpringContextHolder.getBean(InspectDao.class);

    private static BusinessRatesDao businessRatesDao = SpringContextHolder.getBean(BusinessRatesDao.class);

    private static ApprovalDao approvalDao = SpringContextHolder.getBean(ApprovalDao.class);

    private static MaterialUseDao materialUseDao = SpringContextHolder.getBean(MaterialUseDao.class);

    private static RawmaterialStockDao rawmaterialStockDao = SpringContextHolder.getBean(RawmaterialStockDao.class);

    private static PurchasingDetailCloseDao purchasingDetailCloseDao = SpringContextHolder.getBean(PurchasingDetailCloseDao.class);

    private static ProduceBatchDetailDao produceBatchDetailDao = SpringContextHolder.getBean(ProduceBatchDetailDao.class);

    private static WechatOaDao wechatOaDao = SpringContextHolder.getBean(WechatOaDao.class);

    private static Hr_MarkSorceDao hr_markSorceDao = SpringContextHolder.getBean(Hr_MarkSorceDao.class);

    // ERP审批校验数据源接口
    public static Approval checkAuditFlag(String auditId) {
        if(StringUtils.isBlank(auditId)) {
            System.out.println("OA审批单必须要有数据源!");
            return null;
        }
        // 根据审批id获取ERP审批需要字段
        Approval approval = oa_auditDao.getErpAudit(auditId);
        if(null == approval || StringUtils.isBlank(approval.getDataId()) || StringUtils.isBlank(approval.getTypeId()))
        {
            System.out.println(auditId + "ERP数据源不能为空!");
            return null;
        }
        return approval;
    }

    // 订单数据获取
    public static Map<String, Object> handleContractById(String auditId) {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval) {
            return null;
        }
        String recordId = null;
        String detailId = null;
        String notifiId = null;
        String flag = null;
        if (approval.getTypeId().equals("7") || approval.getTypeId().equals("5") || approval.getTypeId().equals("20")){
            detailId = StringUtils.isNotBlank(approval.getDataId()) ? approval.getDataId() : null;
            if (approval.getTypeId().equals("7"))
            {
                flag = "1";
            }
        }else if (approval.getTypeId().equals("24")){
            notifiId = StringUtils.isNotBlank(approval.getDataId()) ? approval.getDataId() : null;
        }else {
            recordId = StringUtils.isNotBlank(approval.getDataId()) ? approval.getDataId() : null;
            detailId = StringUtils.isNotBlank(approval.getDataDetailId()) ? approval.getDataDetailId() : null;
        }
        List<ContractDeailVo> list = contractDetailDao.getContractDeailById(recordId, detailId, notifiId, flag);
        data.put("showAuditData", list);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    // 过数解禁审批
    public static Map<String, Object> handleLoanTransferApplicationAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval) {
            return null;
        }
        // 查询厂编、交期、面积、 投料单号、工序列表
        List<ContractDeailVo> list = produceBatchDetailDao.getProcessList(approval.getRecordId());
        data.put("showAuditData", list);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    // 客诉单申诉审批
    public static Map<String, Object> handleComplaintInvalidAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval) {
            return null;
        }
        RejectApplication reject = rejectApplicationDao.getShowWxReject(approval.getDataId());
        data.put("showAuditData", reject);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    // 业务员设置审批数据
    public static Map<String, Object> handleSalespersonSetupAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval) {
            return null;
        }
        // 修改前数据
        List<WechatAudit> editBeforeList = approvalDao.getEditBeforeList(approval.getRecordId());
        // 修改后数据
        List<WechatAudit> editAfterList = approvalDao.getEditAfterList(approval.getRecordId());
        Customer customer = new Customer();
        for (WechatAudit editBefore : editBeforeList) {
            for (WechatAudit editAfter : editAfterList) {
                if (editBefore.getCustomerId().equals(editAfter.getCustomerId())) {
                    customer.setShortName(editAfter.getCustomerName());
                    customer.setEditBeforeDepartmentName(editBefore.getDepartmentName()); // 修改前部门
                    customer.setEditBeforeSalesmanName(editBefore.getSalesmanName()); // 修改前业务员
                    customer.setEditAfterDepartmentName(editAfter.getDepartmentName()); // 修改后部门
                    customer.setEditAfterSalesmanName(editAfter.getSalesmanName()); // 修改后业务员
                    customer.setEditAfterCustType(editAfter.getCustType()); // 修改前客户类型
                    customer.setEditBeforeCustType(editBefore.getCustType()); // 修改后客户类型
                    customer.setCompanyName(editBefore.getName());
                    break;
                }
            }
        }
        data.put("showAuditData", customer);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    //材料最新价格、周期审批
    public static Map<String, Object> handleMaterialsPriceAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval) {
            return null;
        }
        List<Material> materialList = ModifyUtils.getShowApprovalData(approval.getDataDetailId(), approval.getCreatedDate());
        data.put("showAuditData", materialList);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    // 客户资料
    public static Map<String, Object> handleCustomerAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval)
        {
            return null;
        }
        ContractDeailVo contractDeailVo = new ContractDeailVo();
        BackupTwo backupTwo = new BackupTwo();
        backupTwo.setCompany(new Company(approval.getCompanyId()));
        if (StringUtils.isNotBlank(approval.getBackupsId()))
        {
            backupTwo.setRecordId(approval.getBackupsId());
            backupTwo.setSaveType(TypeKey.APPROVAL_CUSTOMER_INFO);
            List<BackupTwo> backupTwoList = backupTwoDao.getBackupTwoWxList(backupTwo);
            contractDeailVo.setBackupTwoList(backupTwoList);
            if (Collections3.isNotEmpty(backupTwoList)) {
                for (BackupTwo bt : backupTwoList) {
                    if (TypeKey.APPROVAL_CUSTOMER_INFO.compareTo(bt.getSaveType()) == 0
                            || TypeKey.APPROVAL_CUSTOMER_DELETE_ATT.compareTo(bt.getSaveType()) == 0) {
                        bt.setCustomerId(null);
                        List<WechatAttachments> attachList = backupAttachementsDao.getAttachmments(bt);
                        if (Collections3.isNotEmpty(attachList)) {
                            StringBuffer stringBuffer = new StringBuffer();
                            for (WechatAttachments wa : attachList) {
                                if (null != wa.getTypeInt()) {
                                    switch (wa.getTypeInt()) {
                                        // 客户附件
                                        case "1":
                                            stringBuffer.append("添加附件" + wa.getName() + ";");
                                            break;
                                        // 删除客户附件
                                        case "3":
                                            stringBuffer.append("删除附件" + wa.getName() + ";");
                                            break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        String customerId = approval.getDataId();
        Customer customer = new Customer(customerId);
        customer.setCompany(backupTwo.getCompany());
        Customer cus = customerDao.getWxCustomerData(customer);
        if (null != cus) {
            // 客户联系人
            CustomerContact customerContact = new CustomerContact();
            customerContact.setCustomer(customer);
            customerContact.setType(1);
            List<CustomerContact> ccList = customerContactDao.findList(customerContact);
            cus.setContactList(ccList);

            // 客户子公司
            CustomerBranch customerBranch = new CustomerBranch();
            customerBranch.setCustomerId(customer.getRecordId());
            customerBranch.setType(1);
            List<CustomerBranch> cbList = customerBranchDao.findAllList(customerBranch);
            cus.setCustomerbranchList(cbList);

            // 业务员助理
            CustomerSalesAssistant customerSalesAssistant = new CustomerSalesAssistant();
            customerSalesAssistant.setCustomerId(customer.getRecordId());
            customerSalesAssistant.setType(1);
            List<CustomerSalesAssistant> csaList =
                    customerSalesAssistantDao.findalesAssistantByCustomerId(customerSalesAssistant);
            cus.setCsaList(csaList);
        }
        contractDeailVo.setCustomer(cus);
        data.put("showAuditData", contractDeailVo);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    // 供应商资料
    public static Map<String, Object> handleSupplierAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval)
        {
            return null;
        }
        ContractDeailVo contractDeailVo = new ContractDeailVo();
        List<DictValue> manufacturerList = DictUtils.getValuesByItem(CommonEnums.DictItemEnum.MADESUPPLIER,approval.getCompanyId());
        if (StringUtils.isNotBlank(approval.getBackupsId()))
        {
            BackupTwo backupTwo = new BackupTwo();
            backupTwo.setCompany(new Company(approval.getCompanyId()));
            backupTwo.setRecordId(approval.getBackupsId());
            backupTwo.setSaveType(TypeKey.APPROVAL_SUPPLIER_INFO);
            List<BackupTwo> backupTwoList = backupTwoDao.getBackupTwoWxList(backupTwo);
            if (Collections3.isNotEmpty(backupTwoList)) {
                for (BackupTwo bt : backupTwoList) {
                    if(StringUtils.isNotBlank(bt.getSupplyBrandId()))
                    {
                        String supplyBrandName = null;
                        if (StringUtils.isNotBlank(backupTwo.getSupplyBrandId()))
                        {
                            String[] supplyBrandIds = backupTwo.getSupplyBrandId().split(",");
                            for(String supplyBrandId : supplyBrandIds)
                            {
                                for(DictValue manufacturer : manufacturerList)
                                {
                                    if(manufacturer.getRecordId().equals(supplyBrandId))
                                    {
                                        if(StringUtils.isNotBlank(supplyBrandName))
                                        {
                                            supplyBrandName = supplyBrandName  +","+ manufacturer.getValue();
                                        }
                                        else
                                        {
                                            supplyBrandName = manufacturer.getValue();
                                        }
                                        break;
                                    }
                                }
                            }
                            bt.setSupplyBrandName(supplyBrandName);
                        }
                    }
                    if (TypeKey.APPROVAL_SUPPLIER_INFO.compareTo(bt.getSaveType()) == 0
                            || TypeKey.APPROVAL_SUPPLIER_DELETE_ATT.compareTo(bt.getSaveType()) == 0) {
                        bt.setSupplierId(null);
                        List<WechatAttachments> attachList = backupAttachementsDao.getAttachmments(bt);
                        if (Collections3.isNotEmpty(attachList)) {
                            StringBuffer stringBuffer = new StringBuffer();
                            for (WechatAttachments wa : attachList) {
                                if (null != wa.getTypeInt()) {
                                    switch (wa.getTypeInt()) {
                                        // 客户附件
                                        case "2":
                                            stringBuffer.append("添加附件" + wa.getName() + ";");
                                            break;
                                        // 删除客户附件
                                        case "4":
                                            stringBuffer.append("删除附件" + wa.getName() + ";");
                                            break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            contractDeailVo.setBackupTwoList(backupTwoList);
        }
        Supplier supplier = supplierDao.getWxSupplierData(approval.getDataId());
        if(null != supplier && StringUtils.isNotBlank(supplier.getSupplyBrandId()))
        {
            String supplyBrandName = null;
            String[] supplyBrandIds = supplier.getSupplyBrandId().split(",");
            for(String supplyBrandId : supplyBrandIds)
            {
                for(DictValue manufacturer : manufacturerList)
                {
                    if(manufacturer.getRecordId().equals(supplyBrandId))
                    {
                        if(StringUtils.isNotBlank(supplyBrandName))
                        {
                            supplyBrandName = supplyBrandName  +","+ manufacturer.getValue();
                        }
                        else
                        {
                            supplyBrandName = manufacturer.getValue();
                        }
                        break;
                    }
                }
            }
            supplier.setSupplyBrandName(supplyBrandName);
        }
        contractDeailVo.setSupplier(supplier);
        data.put("showAuditData", contractDeailVo);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    /**
     * zjn 2025-07-03 原料采购订单审批(auditType:16)
     * @param auditId 审批id
     * @return data
     */
    public static Map<String, Object> handlePurchaseOrderAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval)
        {
            return null;
        }
        ContractVo contractVo = new ContractVo();
        contractVo.setRecordId(approval.getDataId());
        contractVo.setCompany(new Company(approval.getCompanyId()));
        List<ContractDeailVo> list = purchasingDetailDao.getPurchasingByDetailId(contractVo);

        // 获取供应链设置流程
        Purchasing queryA = new Purchasing();
        queryA.setCompany(new Company(approval.getCompanyId()));
        queryA.setRecordId(approval.getDataId());
        Supplier supplier = new Supplier();
        supplier.setRecordId(list.get(0).getInternalSupplyChainId());
        queryA.setInternalSupplyChain(supplier);
        IngredientsUtils ingredientsUtils = new IngredientsUtils();
        Ingredients ingre = ingredientsUtils.getIngredients(queryA);
        List<IngredientsMiddle> middleList = ingredientsDao.getMiddleList(ingre);

        // 获取工程采购单
        Purchasing pur = purchasingDao.get(approval.getDataId());
        List<IcloudBidding> biddingList = new ArrayList<>();
        if (null != pur && null != pur.getTotalAmount() && pur.getTotalAmount().compareTo(BigDecimal.ZERO) > 0
                && StringUtils.isNotBlank(pur.getSourceNo())) {
            biddingList = biddingDao.getBiddingBypurchId(pur.getRecordId());
            BigDecimal amount = pur.getTotalAmount();
            // 获取是模具还是测试架
            CardA query = new CardA();
            query.setRecordId(pur.getSourceNo());
            query.setCompany(new Company(approval.getCompanyId()));
            query.setPurId(pur.getRecordId());
            List<Purchasing> purList = purchasingDao.getCardPurList(query);
            if (Collections3.isNotEmpty(purList)) {
                Purchasing dataTwo = purList.get(0);
                Notification noti = notificationDao.getListByCardTwo(query);
                if (null != noti) {
                    List<Notification> notiList = Lists.newArrayList();
                    noti.setUseAmount(amount);
                    notiList.add(noti);
                    dataTwo.setShowNotiList(notiList);
                }

                for (ContractDeailVo cdVo : list) {
                    if (null != cdVo.getPurchasing() && cdVo.getPurchasing().getRecordId().equals(dataTwo.getRecordId())) {
                        cdVo.setPurchasing(dataTwo);
                        break;
                    }
                }
            }
        }
        String purId = null;
        if (Collections3.isNotEmpty(list)) {
            for (ContractDeailVo detailVo : list) {
                detailVo.setMiddleList(middleList);
                if(null == detailVo.getPurchasing())
                {
                    detailVo.setPurchasing(pur);
                }
                if(Collections3.isNotEmpty(biddingList))
                {
                    for(IcloudBidding bidding : biddingList)
                    {
                        if(StringUtils.isNotBlank(detailVo.getIcloudBiddingId()) && detailVo.getIcloudBiddingId().equals(bidding.getRecordId()))
                        {
                            detailVo.setIcloudBidding(bidding);
                            break;
                        }
                    }
                }
                if (null != detailVo.getPurchasing() && StringUtils.isNotBlank(detailVo.getPurchasing()
                        .getType()) && "2".equals(detailVo.getPurchasing().getType())) {
                    if (StringUtils.isNotBlank(purId)) {
                        purId = purId + "," + detailVo.getPurchasing().getRecordId();
                    } else {
                        purId = detailVo.getPurchasing().getRecordId();
                    }
                }
            }
        }

        if (Collections3.isNotEmpty(list))
        {
            PurchasingDetailDeliverys deliverys = new PurchasingDetailDeliverys();
            deliverys.setList(list);
            deliverys.setCompany(new Company(approval.getCompanyId()));
            List<PurchasingDetailDeliverys> deliverysList = purchasingDetailDeliverysDao.findLists(deliverys);
            if (Collections3.isNotEmpty(deliverysList))
            {
                for (ContractDeailVo contractDeailVo : list)
                {
                    contractDeailVo.setDeliverysList(new ArrayList<>());
                    for (PurchasingDetailDeliverys detailDeliverys : deliverysList)
                    {
                        if (contractDeailVo.getRecordId().equals(detailDeliverys.getPurchasingDetailId()))
                        {
                            contractDeailVo.getDeliverysList().add(detailDeliverys);
                        }
                    }
                }
            }
        }
        if (Collections3.isNotEmpty(list))
        {
            for (ContractDeailVo contractDeailVo : list)
            {
                contractDeailVo.setPriceAlterList(purchasingDetailDao.getPriceAlterList(contractDeailVo));
            }
        }
        data.put("showAuditData", list);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }


    /**
     * zjn 2025-07-03 收款对账单确认(auditType:18)
     * @param auditId 审批id
     * @return data
     */
    public static Map<String, Object> handleReceiptConfirmAudit(String auditId)
    {
        Map<String, Object> map = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval)
        {
            return null;
        }
        WechatAudit data = new WechatAudit();
        ContractDeailVo contractDeailVo = new ContractDeailVo();
        AccountsReceivable accountsReceivable = new AccountsReceivable();
        accountsReceivable.setRecordId(approval.getDataId());
        accountsReceivable = accountsReceivableDao.approveAccountsReceivable(accountsReceivable);
        data.setUpdateLog(
                accountsReceivable.getCustomer().getShortName() + ":" + accountsReceivable.getPeriod() + " "
                        + approval.getAuditResult());
        List<GoodsCheck> goodsCheckList = goodsCheckDao.getWxGoodsChecks(accountsReceivable);
        String recordIds = null;
        if (Collections3.isNotEmpty(goodsCheckList)) {
            String contractId = "";
            for (GoodsCheck goodsCheck : goodsCheckList) {
                if (StringUtils.isNotBlank(contractId)) {
                    contractId = contractId + "," + goodsCheck.getContractId();
                } else {
                    contractId = goodsCheck.getContractId();
                }
            }
            GoodsCheck check = new GoodsCheck();
            check.setCompany(accountsReceivable.getCompany());
            check.setContractId(contractId);
            check.setCustomer(accountsReceivable.getCustomer());
            check.setPeriod(accountsReceivable.getPeriod());
            List<GoodsCheck> detailsList = goodsCheckDao.getWxGoodsCheckDetails(check);
            if (Collections3.isNotEmpty(detailsList)) {
                for (GoodsCheck goodsCheck : goodsCheckList) {
                    goodsCheck.setDetailList(Lists.newArrayList());
                    for (GoodsCheck detail : detailsList) {
                        if (StringUtils.isNotBlank(goodsCheck.getContractId())
                                && StringUtils.isNotBlank(detail.getContractId()) && goodsCheck.getContractId()
                                .equals(detail.getContractId())) {
                            goodsCheck.getDetailList().add(detail);
                        }
                        if (StringUtils.isNotBlank(recordIds))
                        {
                            recordIds = recordIds + "," + detail.getRecordId();
                        }
                        else
                        {
                            recordIds = detail.getRecordId();
                        }
                    }
                }
            }
        }
        contractDeailVo.setAccountsReceivable(accountsReceivable);
        contractDeailVo.setGoodsCheckList(goodsCheckList);
        List<ContractDeailVo> list = Lists.newArrayList();
        list.add(contractDeailVo);
        data.setVoList(list);
        AdjustBalance adjustBalance = new AdjustBalance();
        adjustBalance.setGoodsCheckId(recordIds);
        adjustBalance.setCustomer(goodsCheckList.get(0).getDetailList().get(0).getCustomer());
        adjustBalance.setPeriod(goodsCheckList.get(0).getDetailList().get(0).getPeriod());
        List<AdjustBalance> adjustBalanceList = adjustBalanceDao.getAdjustBalanceListTwo(adjustBalance);
        List<AdjustBalance> accountDetailList = adjustBalanceDao.getAccountDetailList(adjustBalance);
        contractDeailVo.setAdjustBalanceList(adjustBalanceList);
        contractDeailVo.setAccountDetailList(accountDetailList);
        List<ContractDeailVo> listTwo = Lists.newArrayList();
        List<ContractDeailVo> listThree = Lists.newArrayList();
        listTwo.add(contractDeailVo);
        listThree.add(contractDeailVo);
        data.setAdjustBalanceList(listTwo);
        data.setAccountDetailList(listThree);
        map.put("showAuditData", data);
        map.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return map;
    }

    /**
     * zjn 2025-07-03 付款对账单确认(auditType:19)
     * @param auditId 审批id
     * @return data
     */
    public static Map<String, Object> handlePaymentconfirmAudit(String auditId)
    {
        Map<String, Object> map = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval)
        {
            return null;
        }
        WechatAudit data = new WechatAudit();
        ContractDeailVo contractDeailVo = new ContractDeailVo();
        AccountsPayable accountsPayable = new AccountsPayable();
        accountsPayable.setRecordId(approval.getDataId());
        accountsPayable = accountsPayableDao.get(accountsPayable);
        data.setUpdateLog(accountsPayable.getSupplier().getName() + ":" + accountsPayable.getPeriod() + " " + approval
                .getAuditResult());
        List<MaterialCheck> materialCheckList = materialCheckDao.getWxMaterialChecks(accountsPayable);
        String recordIds = null;
        if (Collections3.isNotEmpty(materialCheckList)) {
            String orderRecordIds = "";
            for (MaterialCheck materialCheck : materialCheckList) {
                if (StringUtils.isNotBlank(orderRecordIds)) {
                    orderRecordIds = orderRecordIds + "," + materialCheck.getOrderRecordId();
                } else {
                    orderRecordIds = materialCheck.getOrderRecordId();
                }
            }
            MaterialCheck check = new MaterialCheck();
            check.setCompany(accountsPayable.getCompany());
            check.setOrderRecordId(orderRecordIds);
            check.setSupplier(accountsPayable.getSupplier());
            check.setPeriod(accountsPayable.getPeriod());
            List<MaterialCheck> detailsList = materialCheckDao.getWxMaterialCheckDetails(check);
            if (Collections3.isNotEmpty(detailsList)) {
                for (MaterialCheck materialCheck : materialCheckList) {
                    materialCheck.setDetailList(Lists.newArrayList());
                    for (MaterialCheck detail : detailsList) {
                        if (StringUtils.isNotBlank(materialCheck.getOrderRecordId()) && StringUtils.isNotBlank(
                                detail.getOrderRecordId()) && materialCheck.getOrderRecordId()
                                .equals(detail.getOrderRecordId())) {
                            materialCheck.getDetailList().add(detail);
                        }
                        if (StringUtils.isNotBlank(recordIds))
                        {
                            recordIds = recordIds + "," + detail.getRecordId();
                        }
                        else
                        {
                            recordIds = detail.getRecordId();
                        }
                    }
                }
            }
        }
        contractDeailVo.setMaterialCheckList(materialCheckList);
        contractDeailVo.setCompany(accountsPayable.getCompany());
        contractDeailVo.setSupplier(accountsPayable.getSupplier());
        List<ContractDeailVo> list = Lists.newArrayList();
        list.add(contractDeailVo);
        data.setVoList(list);
        AdjustPayable adjustPayable = new AdjustPayable();
        adjustPayable.setPeriod(materialCheckList.get(0).getDetailList().get(0).getPeriod());
        adjustPayable.setSupplier(materialCheckList.get(0).getDetailList().get(0).getSupplier());
        adjustPayable.setMaterialCheckId(recordIds);
        List<AdjustPayable> adjustPayableList = adjustPayableDao.getAdjustPayableList(adjustPayable);
        List<AdjustPayable> adjustDetailList = adjustPayableDao.getAdjustDetailList(adjustPayable);
        contractDeailVo.setAdjustPayableList(adjustPayableList);
        contractDeailVo.setAdjustDetailList(adjustDetailList);
        List<ContractDeailVo> listTwo = Lists.newArrayList();
        List<ContractDeailVo> listThree = Lists.newArrayList();
        listTwo.add(contractDeailVo);
        listThree.add(contractDeailVo);
        data.setAdjustPayableList(listTwo);
        data.setAdjustDetailList(listThree);
        map.put("showAuditData", data);
        map.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return map;
    }

    /**
     * zjn 2025-07-03 库存盘点(auditType:26)
     * @param auditId 审批id
     * @return data
     */
    public static Map<String, Object> handleInventoryCheckdAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval)
        {
            return null;
        }
        // 盘点单
        List<ContractDeailVo> list = null;
        Inventory query = new Inventory();
        query.setRecordId(approval.getDataId());
        Inventory in = inventoryDao.get(query);
        BigDecimal allAmount = BigDecimal.ZERO;
        int allNum = 0;
        BigDecimal eqAmount = BigDecimal.ZERO;
        int eqNum = 0;
        BigDecimal moreAmount = BigDecimal.ZERO;
        int moreNum = 0;
        BigDecimal lossAmount = BigDecimal.ZERO;
        int lossNum = 0;
        list = new ArrayList<>();
        if (null != in && null != in.getStoreId() && -1 == in.getStoreId()) {
            // wip
            WipCopy q = new WipCopy();
            Company com = new Company();
            com.setRecordId(in.getCompanyId() + "");
            q.setCompany(com);
            q.setCopyRecId(Integer.parseInt(in.getRecordId()));
            List<WipCopy> wipList = inventoryDao.findWipCopyPage(q);
            if (!CollectionUtils.isEmpty(wipList)) {
                allNum = wipList.size();
                for (WipCopy w : wipList) {
                    int qtyA = w.getQtyA() == null ? 0 : w.getQtyA();
                    int qtyB = w.getQtyB() == null ? 0 : w.getQtyB();
                    int snapA = w.getSnapQtyPnlA() == null ? 0 : w.getSnapQtyPnlA();
                    int snapB = w.getSnapQtyPnlB() == null ? 0 : w.getSnapQtyPnlB();
                    if (qtyA == snapA && qtyB == snapB) {
                        // 相等
                        eqNum++;
                        eqAmount = eqAmount.add(w.getPcsArea());
                    } else if (w.getSnapArea().compareTo(w.getPcsArea()) > 0) {
                        moreNum++;
                        moreAmount = moreAmount.add(w.getSnapArea().subtract(w.getPcsArea()));
                    } else {
                        lossNum++;
                        lossAmount = lossAmount.add(w.getPcsArea().subtract(w.getSnapArea()));
                    }
                    allAmount = allAmount.add(w.getPcsArea());
                }
                ContractDeailVo vo = new ContractDeailVo();
                Material r = new Material();
                r.setXboardQty(allNum);
                r.setBeConvertxboardQty(eqNum);
                r.setConvertStocks(moreNum);
                r.setDeliveryOaQty(lossNum);
                r.setTotalPcsQuantity(FmtUtils.formatDecimal(allAmount));
                r.setLowPrice(FmtUtils.formatDecimal(eqAmount));
                r.setUnitArea(FmtUtils.formatDecimal(moreAmount));
                r.setTotalArea(FmtUtils.formatDecimal(lossAmount));
                r.setStoreHouseId(in.getStoreId());
                vo.setMaterial(r);
                list.add(vo);
            }

        } else {
            List<Material> copyList = inventoryDao.getAllCopy(query);
            if (!CollectionUtils.isEmpty(copyList)) {
                allNum = copyList.size();
                for (Material m : copyList) {
                    if (m.getInStocks().compareTo(m.getStocks()) > 0) {
                        moreNum++;
                        moreAmount =
                                moreAmount.add(m.getInStocks().subtract(m.getStocks()).multiply(m.getInitPrice()));
                    } else if (m.getInStocks().compareTo(m.getStocks()) == 0) {
                        eqNum++;
                        eqAmount = eqAmount.add(m.getInStocks().multiply(m.getInitPrice()));
                    } else {
                        lossAmount =
                                lossAmount.add(m.getStocks().subtract(m.getInStocks()).multiply(m.getInitPrice()));
                        lossNum++;
                    }
                    allAmount = allAmount.add(m.getInStocks().multiply(m.getInitPrice()));
                }
                ContractDeailVo vo = new ContractDeailVo();
                Material r = new Material();
                r.setXboardQty(allNum);
                r.setBeConvertxboardQty(eqNum);
                r.setConvertStocks(moreNum);
                r.setDeliveryOaQty(lossNum);
                r.setTotalPcsQuantity(FmtUtils.formatDecimal(allAmount));
                r.setLowPrice(FmtUtils.formatDecimal(eqAmount));
                r.setUnitArea(FmtUtils.formatDecimal(moreAmount));
                r.setTotalArea(FmtUtils.formatDecimal(lossAmount));
                r.setStoreHouseId(in.getStoreId());
                vo.setMaterial(r);
                list.add(vo);
            }
        }
        if (allNum != eqNum) {
            list.addAll(inventoryDao.getSnapDiffByRecId(approval.getDataId()));
        }
        data.put("showAuditData", list);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    /**
     * zjn 2025-07-03 品质审批(auditType:28)
     * @param auditId 审批id
     * @return data
     */
    public static Map<String, Object> handleQualityAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval) {
            return null;
        }
        List<ContractDeailVo> list = Lists.newArrayList();

        // 审核时审批
        if (TypeKey.APPROVAL_COMPLAINT_ONE.toString().equals(approval.getCustomerType())) {
            RejectApplication reject = rejectApplicationDao.getShowWxReject(approval.getDataId());
            ContractDeailVo contractDeailVo = new ContractDeailVo();
            contractDeailVo.setCompany(companyDao.get(new Company(reject.getCompanyId())));
            contractDeailVo.setReject(reject);
            contractDeailVo.setCustomerType(approval.getCustomerType());
            list.add(contractDeailVo);
        }
        // 品质确认审批
        else if (TypeKey.APPROVAL_COMPLAINT_TWO.toString().equals(approval.getCustomerType())) {
            ContractDeailVo contractDeailVo = new ContractDeailVo();
            RejectApplication reject = rejectApplicationDao.getShowWxReject(approval.getDataId());
            Inspect inspect = new Inspect();
            inspect.setCompany(new Company(approval.getCompanyId()));
            ProduceBatch produceBatch = new ProduceBatch();
            produceBatch.setRecordId(reject.getRecordId());
            inspect.setProduceBatch(produceBatch);
            String inspectId = inspectDao.getInspectIdByReject(inspect);
            if (StringUtils.isNotBlank(inspectId)) {
                Inspect ins = inspectDao.getShowWxInspect(inspectId);
                List<Rework> reworkList = inspectDao.selectReworkList(ins);
                List<Discard> discardList = inspectDao.selectDiscardList(ins.getRecordId());
                List<InsectTaskDetail> taskDetailList = inspectDao.getTaskDetailList(ins);
                ins.setReworkList(reworkList);
                ins.setDiscardList(discardList);
                ins.setTaskDetailList(taskDetailList);
                contractDeailVo.setInspect(ins);
            }
            contractDeailVo.setCompany(companyDao.get(new Company(reject.getCompanyId())));
            contractDeailVo.setReject(reject);
            contractDeailVo.setCustomerType(approval.getCustomerType());
            list.add(contractDeailVo);
        }
        // 生产检测审批
        else if (TypeKey.APPROVAL_COMPLAINT_THREE.toString().equals(approval.getCustomerType())) {
            Inspect inspect = inspectDao.getShowWxInspect(approval.getDataId());
            if (null != inspect) {
                List<Rework> reworkList = inspectDao.selectReworkList(inspect);
                List<Discard> discardList = inspectDao.selectDiscardList(inspect.getRecordId());
                List<InsectTaskDetail> taskDetailList = inspectDao.getTaskDetailList(inspect);
                inspect.setReworkList(reworkList);
                inspect.setDiscardList(discardList);
                inspect.setTaskDetailList(taskDetailList);
            }
            ContractDeailVo contractDeailVo = new ContractDeailVo();
            contractDeailVo.setCompany(companyDao.get(inspect.getCompany()));
            contractDeailVo.setInspect(inspect);
            contractDeailVo.setCustomerType(approval.getCustomerType());
            list.add(contractDeailVo);
        }
        // 修复检测审批
        else if (TypeKey.APPROVAL_COMPLAINT_FOUR.toString().equals(approval.getCustomerType())) {
            ContractDeailVo contractDeailVo = new ContractDeailVo();
            Inspect ins = inspectDao.getShowWxInspect(approval.getDataId());
            if (null != ins) {
                if (null != ins.getProduceBatch()) {
                    RejectApplication reject =
                            rejectApplicationDao.getShowWxReject(ins.getProduceBatch().getRecordId());
                    contractDeailVo.setReject(reject);
                }
                List<Rework> reworkList = inspectDao.selectReworkList(ins);
                List<Discard> discardList = inspectDao.selectDiscardList(ins.getRecordId());
                ins.setReworkList(reworkList);
                ins.setDiscardList(discardList);
                contractDeailVo.setInspect(ins);
            }
            contractDeailVo.setCompany(companyDao.get(ins.getCompany()));
            contractDeailVo.setCustomerType(approval.getCustomerType());
            list.add(contractDeailVo);
        }
        data.put("showAuditData", list);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    /**
     * zjn 2025-07-03 业务费率审批(auditType:29)
     * @param auditId 审批id
     * @return data
     */
    public static Map<String, Object> handleBusinessRateAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval)
        {
            return null;
        }
        Approval approvalT = new Approval();
        Backups backups = new Backups(approval.getBackupsId());
        approvalT.setBackups(backups);
        List<BusinessRates> brList = businessRatesDao.getShowBusinessRates(approvalT);
        data.put("showAuditData", brList);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    /**
     * zjn 2025-07-03 发布招标审批(auditType:34)
     * @param auditId 审批id
     * @return data
     */
    public static Map<String, Object> handlePublishTenderAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval)
        {
            return null;
        }
        Bidding query = new Bidding();
        if (StringUtils.isNotBlank(approval.getDataDetailId()))
        {
            query.setRecordId(approval.getDataDetailId());
        }
        else
        {
            query.setRecordId(approval.getDataId());
        }
        List<Bidding> biddingList = biddingDao.getShowWxDataList(query);
        if (Collections3.isNotEmpty(biddingList))
        {
            for (Bidding bidd: biddingList)
            {
                if (null != bidd && StringUtils.isNotBlank(bidd.getRecordId()))
                {
                    bidd.setSupplierList(biddingDao.getWxSupplierList(bidd));
                }
            }
        }
        data.put("showAuditData", biddingList);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    /**
     * zjn 2025-07-03 中标审批(auditType:35)
     * @param auditId 审批id
     * @return data
     */
    public static Map<String, Object> handleWinningBidAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval)
        {
            return null;
        }
        List<ContractDeailVo> list = Lists.newArrayList();
        IcloudBidding icloudBidding = biddingDao.getShowWxDataTwo(approval.getDataId());
        if(null != icloudBidding)
        {
            Backups bac = new Backups();
            bac.setRecordId(approval.getBackupsId());
            Backups backups = approvalDao.getBackups(bac);
            if(null != backups)
            {
                icloudBidding.setRemark(backups.getRemark());
            }
            Bidding bid = biddingDao.getShowWxData(icloudBidding.getBiddingId());
            List<IcloudBidding> biddingList = ModifyUtils.getIcloudBiddingList(icloudBidding.getBiddingId(),icloudBidding.getSubjectType());
            ContractDeailVo contractDeailVo = new ContractDeailVo();
            contractDeailVo.setBidding(bid);
            contractDeailVo.setIcloudBidding(icloudBidding);
            contractDeailVo.setIcloudBiddingList(biddingList);
            list.add(contractDeailVo);
        }
        data.put("showAuditData", list);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    /**
     * zjn 2025-07-03 原料采购反审(auditType:36)
     * @param auditId 审批id
     * @return data
     */
    public static Map<String, Object> handleRawmaterialReverseAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval)
        {
            return null;
        }
        ContractVo contractVo = new ContractVo();
        contractVo.setRecordId(approval.getDataId());
        contractVo.setDeailId(approval.getDataDetailId());
        contractVo.setCompany(new Company(approval.getCompanyId()));
        List<ContractDeailVo> list = purchasingDetailDao.getPurchasingByDetailId(contractVo);

        // 获取供应链设置流程
        Purchasing queryA = new Purchasing();
        queryA.setCompany(new Company(approval.getCompanyId()));
        queryA.setRecordId(approval.getDataId());
        Supplier supplier = new Supplier();
        supplier.setRecordId(list.get(0).getInternalSupplyChainId());
        queryA.setInternalSupplyChain(supplier);
        IngredientsUtils ingredientsUtils = new IngredientsUtils();
        Ingredients ingre = ingredientsUtils.getIngredients(queryA);
        List<IngredientsMiddle> middleList = ingredientsDao.getMiddleList(ingre);

        // 获取工程采购单
        Purchasing pur = purchasingDao.get(approval.getDataId());
        List<IcloudBidding> biddingList = new ArrayList<>();
        if (null != pur && null != pur.getTotalAmount() && pur.getTotalAmount().compareTo(BigDecimal.ZERO) > 0
                && StringUtils.isNotBlank(pur.getSourceNo())) {
            biddingList = biddingDao.getBiddingBypurchId(pur.getRecordId());
            BigDecimal amount = pur.getTotalAmount();
            // 获取是模具还是测试架
            CardA query = new CardA();
            query.setRecordId(pur.getSourceNo());
            query.setCompany(new Company(approval.getCompanyId()));
            query.setPurId(pur.getRecordId());
            List<Purchasing> purList = purchasingDao.getCardPurList(query);
            if (Collections3.isNotEmpty(purList)) {
                Purchasing dataTwo = purList.get(0);
                Notification noti = notificationDao.getListByCardTwo(query);
                if (null != noti) {
                    List<Notification> notiList = Lists.newArrayList();
                    noti.setUseAmount(amount);
                    notiList.add(noti);
                    dataTwo.setShowNotiList(notiList);
                }
                for (ContractDeailVo cdVo : list) {
                    if (null != cdVo.getPurchasing() && cdVo.getPurchasing().getRecordId().equals(dataTwo.getRecordId())) {
                        cdVo.setPurchasing(dataTwo);
                        break;
                    }
                }
            }
        }
        String purId = null;
        if (Collections3.isNotEmpty(list)) {
            for (ContractDeailVo detailVo : list) {
                detailVo.setMiddleList(middleList);
                if(null == detailVo.getPurchasing())
                {
                    detailVo.setPurchasing(pur);
                }
                if(Collections3.isNotEmpty(biddingList))
                {
                    for(IcloudBidding bidding : biddingList)
                    {
                        if(StringUtils.isNotBlank(detailVo.getIcloudBiddingId()) && detailVo.getIcloudBiddingId().equals(bidding.getRecordId()))
                        {
                            detailVo.setIcloudBidding(bidding);
                            break;
                        }
                    }
                }
                if (null != detailVo.getPurchasing() && StringUtils.isNotBlank(detailVo.getPurchasing()
                        .getType()) && "2".equals(detailVo.getPurchasing().getType())) {
                    if (StringUtils.isNotBlank(purId)) {
                        purId = purId + "," + detailVo.getPurchasing().getRecordId();
                    } else {
                        purId = detailVo.getPurchasing().getRecordId();
                    }
                }
            }
        }
        if (Collections3.isNotEmpty(list))
        {
            PurchasingDetailDeliverys deliverys = new PurchasingDetailDeliverys();
            deliverys.setList(list);
            deliverys.setCompany(new Company(approval.getCompanyId()));
            List<PurchasingDetailDeliverys> deliverysList = purchasingDetailDeliverysDao.findLists(deliverys);
            if (Collections3.isNotEmpty(deliverysList))
            {
                for (ContractDeailVo contractDeailVo : list)
                {
                    contractDeailVo.setDeliverysList(new ArrayList<>());
                    for (PurchasingDetailDeliverys detailDeliverys : deliverysList)
                    {
                        if (contractDeailVo.getRecordId().equals(detailDeliverys.getPurchasingDetailId()))
                        {
                            contractDeailVo.getDeliverysList().add(detailDeliverys);
                        }
                    }
                }
            }
            list = handPurchMethodList(list);
        }
        data.put("showAuditData", list);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    public static List<ContractDeailVo> handPurchMethodList(List<ContractDeailVo> list)
    {
        for(ContractDeailVo detailVo : list)
        {
            PurchRaw purchRaw =  new PurchRaw();
            purchRaw.setRecordId(detailVo.getPurchRaw().getRecordId());
            purchRaw.setCompany(detailVo.getCompany());
            purchRaw.setManufacturer(detailVo.getPurchRaw().getManufacturer());
            purchRaw.setActualThickness(detailVo.getPurchRaw().getActualThickness());
            if(StringUtils.isBlank(detailVo.getPurchRaw().getPurchaseWay()) || detailVo.getPurchRaw().getPurchaseWay().equals("1"))
            {
                purchRaw.setBiddingId(detailVo.getPurchRaw().getBiddingId());
                purchRaw.setPurchaseWay("1");
            }
            else if(detailVo.getPurchRaw().getPurchaseWay().equals("2")  || detailVo.getPurchRaw().getPurchaseWay().equals("3"))
            {
                purchRaw.setMaterialId(detailVo.getMaterial().getRecordId());
                purchRaw.setPurchaseWay(detailVo.getPurchRaw().getPurchaseWay());
            }
            PurchasingDetailService purch = new PurchasingDetailService();
            Map<String,Object> data = purch.getOpenData(purchRaw);
            detailVo.setMaterialList((List<Material>) data.get("list"));
        }
        return list;
    }

    /**
     * zjn 2025-07-03 原料采购单结案审批(auditType:53)
     * @param auditId 审批id
     * @return data
     */
    public static Map<String, Object> handlePurchaseOrderClosedAudit(String auditId)
    {
        Map<String, Object> data = new HashMap<>();
        Approval approval = checkAuditFlag(auditId);
        if(null == approval)
        {
            return null;
        }
        ContractVo contractVo = new ContractVo();
        contractVo.setRecordId(approval.getDataId());
        contractVo.setCompany(new Company(approval.getCompanyId()));

        contractVo.setDeailId(approval.getDataDetailId());
        contractVo.setCompany(new Company(approval.getCompanyId()));
        List<ContractDeailVo> list = purchasingDetailDao.getPurchasingByDetailIdTwo(contractVo);
        if (Collections3.isNotEmpty(list))
        {
            // 获取入库数量
            for (ContractDeailVo contractDeailVo : list)
            {
                BigDecimal warehousingNum =
                        rawmaterialStockDao.getWarehousingSum(contractDeailVo.getRecordId(), contractDeailVo.getCompany().getRecordId());
                contractDeailVo.setWarehousingNum(warehousingNum != null ? warehousingNum : BigDecimal.ZERO);
                contractDeailVo.setNowarehousingNum(new BigDecimal(contractDeailVo.getQuantity()).subtract(contractDeailVo.getWarehousingNum()));
            }
        }
        else
        {
            // 获取结案记录
            list = purchasingDetailCloseDao.getPurchasingByDetailId(contractVo);
            if (Collections3.isNotEmpty(list))
            {
                String recordIds = null;
                for (ContractDeailVo contractDeailVo : list)
                {
                    if (StringUtils.isNotBlank(recordIds))
                    {
                        recordIds = recordIds + "," + contractDeailVo.getRecordId();
                    }
                    else
                    {
                        recordIds = contractDeailVo.getRecordId();
                    }
                }
                // 获取入库集合
                List<RawmaterialStock> stockList = rawmaterialStockDao.getWarehousingSumList(recordIds,list.get(0).getCompany().getRecordId());
                // 获取入库数量
                for (ContractDeailVo contractDeailVo : list)
                {
                    if (Collections3.isNotEmpty(stockList))
                    {
                        for (RawmaterialStock stock : stockList)
                        {
                            if (contractDeailVo.getRecordId().equals(stock.getPurchasingDetailId()))
                            {
                                contractDeailVo.setWarehousingNum(stock.getQuantity() != null ? stock.getQuantity() : BigDecimal.ZERO);
                                contractDeailVo.setNowarehousingNum(new BigDecimal(contractDeailVo.getQuantity()).subtract(contractDeailVo.getWarehousingNum()));
                                break;
                            }
                        }
                    }
                }
            }
            else
            {
                list = purchasingDetailDao.getPurchasingByDetailId(contractVo);
                if (Collections3.isNotEmpty(list))
                {
                    String recordIds = null;
                    for (ContractDeailVo contractDeailVo : list)
                    {
                        if (StringUtils.isNotBlank(recordIds))
                        {
                            recordIds = recordIds + "," + contractDeailVo.getRecordId();
                        }
                        else
                        {
                            recordIds = contractDeailVo.getRecordId();
                        }
                    }
                    // 获取入库集合
                    List<RawmaterialStock> stockList = rawmaterialStockDao.getWarehousingSumList(recordIds, list.get(0).getCompany().getRecordId());
                    // 获取入库数量
                    for (ContractDeailVo contractDeailVo : list)
                    {
                        if (Collections3.isNotEmpty(stockList))
                        {
                            for (RawmaterialStock stock : stockList)
                            {
                                if (contractDeailVo.getRecordId().equals(stock.getPurchasingDetailId()))
                                {
                                    contractDeailVo.setWarehousingNum(stock.getQuantity() != null ? stock.getQuantity() : BigDecimal.ZERO);
                                    contractDeailVo.setNowarehousingNum(new BigDecimal(contractDeailVo.getQuantity()).subtract(contractDeailVo.getWarehousingNum()));
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }

        // 获取供应链设置流程
        Purchasing queryA = new Purchasing();
        queryA.setCompany(new Company(approval.getCompanyId()));
        queryA.setRecordId(approval.getDataId());
        Supplier supplier = new Supplier();
        supplier.setRecordId(list.get(0).getInternalSupplyChainId());
        queryA.setInternalSupplyChain(supplier);
        IngredientsUtils ingredientsUtils = new IngredientsUtils();
        Ingredients ingre = ingredientsUtils.getIngredients(queryA);
        List<IngredientsMiddle> middleList = ingredientsDao.getMiddleList(ingre);

        // 获取工程采购单
        Purchasing pur = purchasingDao.get(approval.getDataId());
        List<IcloudBidding> biddingList = new ArrayList<>();
        if (null != pur && null != pur.getTotalAmount() && pur.getTotalAmount().compareTo(BigDecimal.ZERO) > 0
                && StringUtils.isNotBlank(pur.getSourceNo())) {
            biddingList = biddingDao.getBiddingBypurchId(pur.getRecordId());
            BigDecimal amount = pur.getTotalAmount();
            // 获取是模具还是测试架
            CardA query = new CardA();
            query.setRecordId(pur.getSourceNo());
            query.setCompany(new Company(approval.getCompanyId()));
            query.setPurId(pur.getRecordId());
            List<Purchasing> purList = purchasingDao.getCardPurList(query);
            if (Collections3.isNotEmpty(purList)) {
                Purchasing dataTwo = purList.get(0);
                Notification noti = notificationDao.getListByCardTwo(query);
                if (null != noti) {
                    List<Notification> notiList = Lists.newArrayList();
                    noti.setUseAmount(amount);
                    notiList.add(noti);
                    dataTwo.setShowNotiList(notiList);
                }

                for (ContractDeailVo cdVo : list) {
                    if (null != cdVo.getPurchasing() && cdVo.getPurchasing().getRecordId().equals(dataTwo.getRecordId())) {
                        cdVo.setPurchasing(dataTwo);
                        break;
                    }
                }
            }
        }
        String purId = null;
        if (Collections3.isNotEmpty(list)) {
            for (ContractDeailVo detailVo : list) {
                detailVo.setMiddleList(middleList);
                if(null == detailVo.getPurchasing())
                {
                    detailVo.setPurchasing(pur);
                }
                if(Collections3.isNotEmpty(biddingList))
                {
                    for(IcloudBidding bidding : biddingList)
                    {
                        if(StringUtils.isNotBlank(detailVo.getIcloudBiddingId()) && detailVo.getIcloudBiddingId().equals(bidding.getRecordId()))
                        {
                            detailVo.setIcloudBidding(bidding);
                            break;
                        }
                    }
                }
                if (null != detailVo.getPurchasing() && StringUtils.isNotBlank(detailVo.getPurchasing()
                        .getType()) && "2".equals(detailVo.getPurchasing().getType())) {
                    if (StringUtils.isNotBlank(purId)) {
                        purId = purId + "," + detailVo.getPurchasing().getRecordId();
                    } else {
                        purId = detailVo.getPurchasing().getRecordId();
                    }
                }
            }
        }

        if (Collections3.isNotEmpty(list))
        {
            PurchasingDetailDeliverys deliverys = new PurchasingDetailDeliverys();
            deliverys.setList(list);
            deliverys.setCompany(new Company(approval.getCompanyId()));
            List<PurchasingDetailDeliverys> deliverysList = purchasingDetailDeliverysDao.findLists(deliverys);
            if (Collections3.isNotEmpty(deliverysList))
            {
                for (ContractDeailVo contractDeailVo : list)
                {
                    contractDeailVo.setDeliverysList(new ArrayList<>());
                    for (PurchasingDetailDeliverys detailDeliverys : deliverysList)
                    {
                        if (contractDeailVo.getRecordId().equals(detailDeliverys.getPurchasingDetailId()))
                        {
                            contractDeailVo.getDeliverysList().add(detailDeliverys);
                        }
                    }
                }
            }
            list = handPurchMethodList(list);
        }
        data.put("showAuditData", list);
        data.put("attachList", getAttachList(approval.getTypeId(), approval.getRecordId()));
        return data;
    }

    public static List<WechatAttachments> getAttachList(String auditTypeId, String auditId)
    {
        List<WechatAttachments> attachList = new ArrayList<>();
        // 客户资料审批附件
        if (auditTypeId.equals("14"))
        {
            attachList = wechatOaDao.getAttachmmentLsit(auditId, 1);
        }
        // 供应商资料审批附件
        else if (auditTypeId.equals("15"))
        {
            attachList = wechatOaDao.getAttachmmentLsit(auditId, 2);
        }
        // 原料采购单审批附件
        else if (auditTypeId.equals("16"))
        {
            attachList = wechatOaDao.getPurAttachmments(auditId);
        }
        // 收款对账单确认审批附件
        else if (auditTypeId.equals("18"))
        {
            attachList = wechatOaDao.getAccountsAttachmments(auditId, 1);
        }
        // 付款对账单确认审批附件
        else if (auditTypeId.equals("19"))
        {
            attachList = wechatOaDao.getAccountsAttachmments(auditId, 2);
        }
        // 订单审批附件
        else if (auditTypeId.equals("23"))
        {
            attachList = wechatOaDao.getContractAttachmments(auditId, 1);
            if (Collections3.isEmpty(attachList)) {
                attachList = wechatOaDao.getContractAttachmments(auditId, 2);
            }
        }
        // 品质审批附件
        else if (auditTypeId.equals("28"))
        {
            attachList = wechatOaDao.getRejectAttachmments(auditId);
            if(Collections3.isEmpty(attachList))
            {
                attachList = wechatOaDao.getRejectAttachmmentsTwo(auditId);
            }
        }
        // 最新采购单价审批附件
        else if (auditTypeId.equals("51"))
        {
            attachList = wechatOaDao.getMaterialAttachements(auditId);
        }
        return attachList;
    }

}
