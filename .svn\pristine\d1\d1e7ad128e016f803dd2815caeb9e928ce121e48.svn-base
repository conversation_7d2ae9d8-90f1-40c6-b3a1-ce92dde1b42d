<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="text-light">
	<div class="row pt-4">
		<div class="col text-center">
			<img src="${pageContext.request.contextPath}/assets/wechat/img/erpperson/kyblogo.png" width="20%;">
		</div>
	</div>
	<div class="row pt-1">
		<div class="col text-center" style="font-size: 1.2rem; color: black">
			科易博云平台绑定ERP账号
		</div>
	</div>
	<div class="row pt-5">
		<div class="col  text-black-50">
			公司全称&emsp;
			<input style="width: 70%;height: 2.6rem;border: 1px solid #000000;background: none;border-radius: 80px;
				padding-left: 2rem;font-size: 1rem;color: #000000;" placeholder="请输入公司全称" v-model="company.name">
		</div>
	</div>
	<div class="row pt-3">
		<div class="col  text-black-50">
			手机号码&emsp;
			<input style="width: 70%;height: 2.6rem;border: 1px solid #000000;background: none;border-radius: 80px;
				padding-left: 2rem;font-size: 1rem;color: #000000;" placeholder="请输入手机号码" v-model="company.phone">
		</div>
	</div>
	<div class="row pt-3  pl-3" style="margin-left: -2.2rem">
		<div class="col text-black-50" >
			&emsp;验证码&emsp;
			<input style="width: 50%;height: 2.6rem;border:  1px solid #000000;background:none;border-radius: 80px;color: #000000;" placeholder="请输入验证码" v-model="company.valiCode" >
			<button style="border: none;height: 1.8rem;border-radius: 20px;background-color: #fff;font-size: 0.7rem;width: 20%;" v-on:click="sendValiCode">
				发送
			</button>
		</div>
	</div>
	<div class="row pt-4">
		<div class="col text-center pt-2 pb-2">
			<button class="btn btn-light w-100 text-primary" style="border-radius: 80px;" v-on:click="bindErp">绑定</button>
		</div>
	</div>
	<div class="row fixed-bottom">
		<div class="col text-center" style="font-size: 0.8rem;">
			<span>版权所有 © 2019 kybsoft.com | 深圳市科易博软件有限公司</span>
		</div>
	</div>
</div>