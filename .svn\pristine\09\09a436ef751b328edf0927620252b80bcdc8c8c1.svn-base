package com.kyb.pcberp.modules.contract.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;

import java.util.Date;
import java.util.List;

public class MatPreparationVersion extends DataEntity<MatPreparationVersion>
{
    private static final long serialVersionUID = 1L;

    private String no;

    private Date startDate;

    private Date endDate;

    private List<MatPreparation> matPreparationList;

    private Date sentTimeStartQr; // 时间段查询条件 载体

    private Date sentTimeEndQr;

    private String name;

    public String getNo()
    {
        return no;
    }

    public void setNo(String no)
    {
        this.no = no;
    }

    public Date getStartDate()
    {
        return startDate;
    }

    public void setStartDate(Date startDate)
    {
        this.startDate = startDate;
    }

    public Date getEndDate()
    {
        return endDate;
    }

    public void setEndDate(Date endDate)
    {
        this.endDate = endDate;
    }

    public List<MatPreparation> getMatPreparationList()
    {
        return matPreparationList;
    }

    public void setMatPreparationList(List<MatPreparation> matPreparationList)
    {
        this.matPreparationList = matPreparationList;
    }

    public Date getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }

    public void setSentTimeStartQr(Date sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }

    public Date getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }

    public void setSentTimeEndQr(Date sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }

    public String getStartDateStr()
    {
        if(null != startDate)
        {
            return DateUtils.formatDate(startDate,"yyyy-MM-dd");
        }
        return null;
    }

    public String getEndDateStr()
    {
        if(null != endDate)
        {
            return DateUtils.formatDate(endDate,"yyyy-MM-dd");
        }
        return null;
    }

    public String getCreatedDateStr()
    {
        if(null != createdDate)
        {
            return DateUtils.formatDate(createdDate,"yyyy-MM-dd");
        }
        return null;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }
}
