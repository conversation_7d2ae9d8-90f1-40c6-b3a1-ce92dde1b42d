package com.kyb.pcberp.modules.production.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

public class FeedAttach extends DataEntity<FeedAttach>
{
    private static final long serialVersionUID = 1L;

    private String companyId;

    private String orgFileName; // 文件名称

    private String realFileName; // 目录文件名称

    private String fileUrl; // 文件地址

    private Integer useFlag;

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    public String getOrgFileName()
    {
        return orgFileName;
    }

    public void setOrgFileName(String orgFileName)
    {
        this.orgFileName = orgFileName;
    }

    public String getRealFileName()
    {
        return realFileName;
    }

    public void setRealFileName(String realFileName)
    {
        this.realFileName = realFileName;
    }

    public String getFileUrl()
    {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl)
    {
        this.fileUrl = fileUrl;
    }

    public Integer getUseFlag()
    {
        return useFlag;
    }

    public void setUseFlag(Integer useFlag)
    {
        this.useFlag = useFlag;
    }

    public String getUseFlagStr()
    {
        String result = null;
        if(null != useFlag)
        {
            switch (useFlag)
            {
                case 0:
                    result = "否";
                    break;
                case 1:
                    result = "是";
                    break;
            }
        }
        return result;
    }
}
