/**
 * 
 */
package com.kyb.pcberp.modules.eg.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.eg.entity.ProcessCraft;

/**
 * 工艺DAO接口
 * 
 * <AUTHOR>
 * @version 2015-09-16
 */
@MyBatisDao
public interface ProcessCraftDao extends CrudDao<ProcessCraft>
{
    List<ProcessCraft> findListAndValues(ProcessCraft craft);
    /**
     * WC 2016-12-17 更新工艺默认值
     */
    public void updateCraftDefaultValue(ProcessCraft craft);
    
    /**
     * WC 2016-12-20 根据ID查询工艺
     */
    public ProcessCraft findCraftByRecordId(@Param("recordId") String recordId);
}