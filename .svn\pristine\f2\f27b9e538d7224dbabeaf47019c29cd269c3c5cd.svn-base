package com.kyb.pcberp.modules.icloud.erp.erp.pojo;

import com.kyb.pcberp.common.base.pojo.Icloud_BasePojo;

public class Icloud_BoardPartCraft extends Icloud_BasePojo
{
    private static final long serialVersionUID = 1L;

    private String notifiId;

    private String companyId;

    private String materialId;

    private Integer useStocks;

    private Double boardScale;

    private String name;

    private Integer useStocksCopy;

    private Integer useStock;

    private Integer feedStocks;

    private Integer purchRawNum;

    private Integer purchNum;

    private Integer purchStockNum;

    private Integer availableQty;

    private Integer useStocksUse;

    private Integer availableQtyUse;

    private Double remainQty;

    private String status;

    public String getNotifiId()
    {
        return notifiId;
    }

    public void setNotifiId(String notifiId)
    {
        this.notifiId = notifiId;
    }

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    public String getMaterialId()
    {
        return materialId;
    }

    public void setMaterialId(String materialId)
    {
        this.materialId = materialId;
    }

    public Integer getUseStocks()
    {
        return useStocks;
    }

    public void setUseStocks(Integer useStocks)
    {
        this.useStocks = useStocks;
    }

    public Double getBoardScale()
    {
        return boardScale;
    }

    public void setBoardScale(Double boardScale)
    {
        this.boardScale = boardScale;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public Integer getUseStocksCopy()
    {
        return useStocksCopy;
    }

    public void setUseStocksCopy(Integer useStocksCopy)
    {
        this.useStocksCopy = useStocksCopy;
    }

    public Integer getUseStock()
    {
        return useStock;
    }

    public void setUseStock(Integer useStock)
    {
        this.useStock = useStock;
    }

    public Integer getFeedStocks()
    {
        return feedStocks;
    }

    public void setFeedStocks(Integer feedStocks)
    {
        this.feedStocks = feedStocks;
    }

    public Integer getPurchRawNum()
    {
        return purchRawNum;
    }

    public void setPurchRawNum(Integer purchRawNum)
    {
        this.purchRawNum = purchRawNum;
    }

    public Integer getPurchNum()
    {
        return purchNum;
    }

    public void setPurchNum(Integer purchNum)
    {
        this.purchNum = purchNum;
    }

    public Integer getPurchStockNum()
    {
        return purchStockNum;
    }

    public void setPurchStockNum(Integer purchStockNum)
    {
        this.purchStockNum = purchStockNum;
    }

    public Integer getAvailableQty()
    {
        return availableQty;
    }

    public void setAvailableQty(Integer availableQty)
    {
        this.availableQty = availableQty;
    }

    public Integer getUseStocksUse()
    {
        return useStocksUse;
    }

    public void setUseStocksUse(Integer useStocksUse)
    {
        this.useStocksUse = useStocksUse;
    }

    public Integer getAvailableQtyUse()
    {
        return availableQtyUse;
    }

    public void setAvailableQtyUse(Integer availableQtyUse)
    {
        this.availableQtyUse = availableQtyUse;
    }

    public Double getRemainQty()
    {
        return remainQty;
    }

    public void setRemainQty(Double remainQty)
    {
        this.remainQty = remainQty;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }
}
