<%@ page contentType="text/html;charset=UTF-8" %>
<tab ng-click="ctrl.getDataList()">
    <tab-heading>生产排单池</tab-heading>
    <div class="panel-body">
        <div class="form-horizontal">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-3 control-label">时间段：</label>
                        <div class="col-md-9">
                            <div class="input-prepend input-group">
                                <span class="add-on input-group-addon"><i
                                        class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                <input type="text" class="form-control" disable-auto-validate="true"
                                       ng-blur="ctrl.initDateTwo(ctrl.time)"
                                       kyb-daterange-after-today
                                       kyb-daterange-options="ctrl.rangeOptionsTwo"
                                       ng-model="ctrl.timeTwo"
                                       placeholder="请选择时间段">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-sm-1 col-md-1 control-label">工序：</label>
                        <div class="col-md-11">
                            <ui-select theme="bootstrap" multiple sortable="true" close-on-select="false"
                                       ng-model="ctrl.processQuery" disable-auto-validate="true"
                                       append-to-body="true">
                                <ui-select-match placeholder="请选择...">{{$item.processName}}</ui-select-match>
                                <ui-select-choices repeat="item in ctrl.processTwoList | filter: $select.search">
                                    <div ng-bind-html="item.processName | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <button class="btn btn-default btn-default-width pull-right" ng-click="ctrl.getDataList()">
                        <i class="fa fa-search"></i> 查&nbsp;询
                    </button>
                </div>&nbsp;&nbsp;
            </div>
        </div>
    </div>
    <div class="panel panel-default">
    </div>
    <div class="rows">
        <div class="table-scrollable">
            <table id="steps1" class="table table-striped table-bordered table-condensed table-advance table-hover">
                <thead>
                <tr class="heading">
                    <th width="5%" style="text-align: center;">日期</th>
                    <th style="text-align: center;" ng-repeat="process in ctrl.showProcessList">
                        	<span ng-repeat="processName in ctrl.showProcessName($index)">
                        		{{processName}}<br/>
                        	</span>
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr ng-repeat="row in ctrl.dataPage.data.list track by $index"
                    ng-class="{'success': dataCtrl.showColor($index),'warning': ctrl.showColorTwo($index)}">
                    <td width="5%" style="text-align: center;">{{row.date}}</td>
                    <td style="text-align: center;" ng-repeat="processData in row.processList">
                        <a ng-click="ctrl.showOpenList(row,processData)">
                            <span>{{processData.useNum ? processData.useNum : 0}} / {{processData.maxNum ? processData.maxNum : 0}}</span><br/>&nbsp;&nbsp;
                            <span>{{processData.useArea ? processData.useArea : 0}} / {{processData.productArea ? processData.productArea : 0}}</span>
                        </a>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
<%--        <div class="row page-margin-top">
            <div id="steps4" class="col-md-12 col-lg-6">
                <span class="inline">每页</span>
                <select class="form-control inline" style="margin-top: 8px; width: 100px;"
                        disable-valid-styling="true"
                        disable-invalid-styling="true"
                        ng-model="ctrl.dataPage.pageSize"
                        ng-change="ctrl.pageSizeChangeForData()"
                        ng-options="pageSizeOption for pageSizeOption in ctrl.dataPage.pageSizeOptions">
                </select>
                <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示{{ctrl.dataPage.data.startCount}} / {{ctrl.dataPage.data.endCount}}条，共 {{ctrl.dataPage.data.count}} 条</span>
            </div>
            <div class="col-md-12 col-lg-6">
                <paging class="pull-right" page="ctrl.dataPage.data.pageNo"
                        page-size="ctrl.dataPage.data.pageSize"
                        total="ctrl.dataPage.data.count" adjacent="1" dots="..."
                        scroll-top="false" hide-if-empty="false" ul-class="pagination"
                        active-class="active" disabled-class="disabled"
                        show-prev-next="true"
                        paging-action="ctrl.doDataPage(page, pageSize, total)"></paging>
            </div>
        </div>--%>
    </div>
</tab>