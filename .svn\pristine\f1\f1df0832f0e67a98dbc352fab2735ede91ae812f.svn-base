/**
 * 
 */
package com.kyb.pcberp.modules.crm.entity;

import java.math.BigDecimal;

import com.kyb.pcberp.common.persistence.DataEntity;

/**
 * 工艺单价实体
 * 
 * <AUTHOR>
 * @version 2015-08-27
 */
public class CraftPrice extends DataEntity<CraftPrice>
{
    private static final long serialVersionUID = 1L;
    
    private Long itemId;
    
    private Long valueId;
    
    private BigDecimal price;
    
    public CraftPrice()
    {
        super();
    }
    
    public CraftPrice(String id)
    {
        super(id);
    }
    
    public Long getItemId()
    {
        return itemId;
    }
    
    public void setItemId(Long itemId)
    {
        this.itemId = itemId;
    }
    
    public Long getValueId()
    {
        return valueId;
    }
    
    public void setValueId(Long valueId)
    {
        this.valueId = valueId;
    }
    
    public BigDecimal getPrice()
    {
        return price;
    }
    
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }
    
}