package com.kyb.pcberp.modules.purch.vo;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.purch.entity.Purchasing;

public class PurchasingExceVo extends Purchasing
{
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    
    private String purchaseNo;// 采购订单号
    
    private BigDecimal price; // 价格
    
    private BigDecimal quantity; // 数量
    
    private BigDecimal amount; // 金额
    
    private Date createdDate; // 采购时间
    
    private String materialNo; // 物料编号
    
    private String materialName;// 物料名称
    
    private String specification;// 规格
    
    private String materailTypeName;// 物料类型
    
    private BigDecimal inNum; // 入库数量
    
    private Date inDate;// 入库时间
    
    private String applyNos;// 申请单号
    
    private String applyNums;// 申请数量
    
    private String applyDates;// 申请时间
    
    private String noticeNos;// 通知单号
    
    private String noticeNums;// 通知单数量
    
    private BigDecimal pcsLength; // pcs长
    
    private BigDecimal pcsWidth; // pcs宽
    
    private BigDecimal diameter; // 圆直径
    
    @ExcelField(title = "采购单号", align = 2, sort = 10)
    public String getPurchaseNo()
    {
        return purchaseNo;
    }
    
    public void setPurchaseNo(String purchaseNo)
    {
        this.purchaseNo = purchaseNo;
    }
    
    @ExcelField(title = "单价", align = 2, sort = 20)
    public BigDecimal getPrice()
    {
        return price;
    }
    
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }
    
    @ExcelField(title = "数量", align = 2, sort = 30)
    public BigDecimal getQuantity()
    {
        return quantity;
    }
    
    public void setQuantity(BigDecimal quantity)
    {
        this.quantity = quantity;
    }
    
    @ExcelField(title = "总金额", align = 2, sort = 40)
    public BigDecimal getAmount()
    {
        return amount;
    }
    
    public void setAmount(BigDecimal amount)
    {
        this.amount = amount;
    }
    
    @ExcelField(title = "采购时间", align = 2, sort = 50)
    public String getCreatedDateStr()
    {
        return createdDate==null?"":new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(createdDate);
    }
    
    public void setCreatedDate(Date createdDate)
    {
        this.createdDate = createdDate;
    }
    
    @ExcelField(title = "采物料编号", align = 2, sort = 60)
    public String getMaterialNo()
    {
        return materialNo;
    }
    
    public void setMaterialNo(String materialNo)
    {
        this.materialNo = materialNo;
    }
    
    @ExcelField(title = "物料名称", align = 2, sort = 70)
    public String getMaterialName()
    {
        return materialName;
    }
    
    public void setMaterialName(String materialName)
    {
        this.materialName = materialName;
    }
    
    @ExcelField(title = "规格型号", align = 2, sort = 80)
    public String getSpecification()
    {
        return specification;
    }
    
    public void setSpecification(String specification)
    {
        this.specification = specification;
    }
    
    @ExcelField(title = "物料类型", align = 2, sort = 90)
    public String getMaterailTypeName()
    {
        return materailTypeName;
    }
    
    public void setMaterailTypeName(String materailTypeName)
    {
        this.materailTypeName = materailTypeName;
    }
    
    @ExcelField(title = "入库数量", align = 2, sort = 100)
    public BigDecimal getInNum()
    {
        return inNum;
    }
    
    public void setInNum(BigDecimal inNum)
    {
        this.inNum = inNum;
    }
    
    @ExcelField(title = "入库时间", align = 2, sort = 110)
    public String getInDate()
    {
        return inDate==null?"":new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(inDate);
    }
    
    public void setInDate(Date inDate)
    {
        this.inDate = inDate;
    }
    
    @ExcelField(title = "申请单编号", align = 2, sort = 120)
    public String getApplyNos()
    {
        return applyNos;
    }
    
    public void setApplyNos(String applyNos)
    {
        this.applyNos = applyNos;
    }
    
    @ExcelField(title = "申请数量", align = 2, sort = 130)
    public String getApplyNums()
    {
        return applyNums;
    }
    
    public void setApplyNums(String applyNums)
    {
        this.applyNums = applyNums;
    }
    
    @ExcelField(title = "申请时间", align = 2, sort = 140)
    public String getApplyDates()
    {
        return applyDates;
    }
    
    public void setApplyDates(String applyDates)
    {
        this.applyDates = applyDates;
    }
    
    @ExcelField(title = "通知单编号", align = 2, sort = 140)
    public String getNoticeNos()
    {
        return noticeNos;
    }
    
    public void setNoticeNos(String noticeNos)
    {
        this.noticeNos = noticeNos;
    }
    
    @ExcelField(title = "申请采购数量", align = 2, sort = 140)
    public String getNoticeNums()
    {
        return noticeNums;
    }
    
    public void setNoticeNums(String noticeNums)
    {
        this.noticeNums = noticeNums;
    }

    @ExcelField(title = "pcs长", align = 2, sort = 150)
    public BigDecimal getPcsLength()
    {
        return pcsLength;
    }

    @ExcelField(title = "pcs宽", align = 2, sort = 160)
    public BigDecimal getPcsWidth()
    {
        return pcsWidth;
    }

    @ExcelField(title = "圆直径", align = 2, sort = 170)
    public BigDecimal getDiameter()
    {
        return diameter;
    }

    public void setPcsLength(BigDecimal pcsLength)
    {
        this.pcsLength = pcsLength;
    }

    public void setPcsWidth(BigDecimal pcsWidth)
    {
        this.pcsWidth = pcsWidth;
    }

    public void setDiameter(BigDecimal diameter)
    {
        this.diameter = diameter;
    }
    
}
