package com.kyb.pcberp.modules.icloud.contract.pojo;

import com.kyb.pcberp.common.base.pojo.Icloud_BasePojo;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.modules.icloud.capacity.pojo.Icloud_CapacityDeail;
import com.kyb.pcberp.modules.icloud.material.pojo.Icloud_MaterialArea;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class Icloud_GroupCenter extends Icloud_BasePojo
{
    private static final long serialVersionUID = 1L;

    private BigDecimal processFee;

    private BigDecimal materialFee;

    private BigDecimal costPrice;

    private BigDecimal lnPrice;

    private BigDecimal jxPrice;

    // 需要大板
    private String needBord;

    // 采购数量
    private Integer replyStock;

    // 使用大板id
    private String materialId;

    // 占用大板库存
    private String occupiedStock;

    // 订单最终交期
    private Date estimateDate;

    // 交期计算方法
    private String estimateMethod;

    // 提供产能
    private String productArea;

    // 评估结果
    private String estimateRemark;

    // 采购周期
    private String purchRowDays;

    // 生产周期
    private String notiNeedDays;

    // 待产天数
    private String waitDays;

    // 物流时间
    private String logisticsTime;

    // 销售子公司id
    private String saleCompanyId;

    // 一张大料可开多少pcs
    private String totalPcses;

    // 销售公司当日产能
    private String dayAreas;

    // 生产周期id
    private String productionCycleId;

    // 延多少天
    private Integer oueDays;

    // 预估最晚交期时间
    private String estimateDateTwo;

    // 销售客户id
    private String saleCsutomerId;

    // 协调交期
    private String newDeliveryDays;

    private String notiCraftNo;

    private String genCraftNo;

    private String urgentApproval; // 加急标志

    private BigDecimal excessStock;

    private String estimateStartDate;

    private String contactDeailId;

    private String factoryStocks; // 尾数库存

    private String notifiId;

    private String craftNo;

    private String urgentFlag; // 加急标志 1 加急 0 不加急

    private String companyId; // 生产工厂id

    private String userId;

    private Integer usePurchStocks;

    private Date custDeliveryDate;

    private Date acDistributeDate;

    private BigDecimal engineeringFee;

    private BigDecimal mouldFee;

    private BigDecimal testShelfFee;

    private BigDecimal engineeringFeeCopy;

    private BigDecimal mouldFeeCopy;

    private BigDecimal testShelfFeeCopy;

    private String priceId;

    private String materialIdCopy;

    private String feedId;

    private String cardId;

    private String notifitionId;

    private String confimFlag;

    private String cardUseRatio;

    private String cardTotalPcses;

    private Double bordLength;

    private Double bordWidth;

    private BigDecimal price;

    private BigDecimal prdEngineeringFee;

    private BigDecimal prdMouldFee;

    private BigDecimal prdTestShelfFee;

    private BigDecimal prdFilmFee;

    private BigDecimal prdOthersFee;

    private BigDecimal prdEngineeringFeeCopy;

    private BigDecimal prdMouldFeeCopy;

    private BigDecimal prdTestShelfFeeCopy;

    private BigDecimal prdFilmFeeCopy;

    private BigDecimal prdOthersFeeCopy;

    private String factoryComId;

    private String paramId;

    private String qcFlag;

    private String saleDetialStatus;

    private BigDecimal useRatio;

    public List<Icloud_CapacityDeail> addCapacityList;

    private String versionDate;

    private List<Icloud_MaterialArea> areas;

    private String manufacturer;

    private String useAgeData; // 按单订料

    private String materialMsg; // 材料算法

    private BigDecimal saleFee;

    private BigDecimal manageFee;

    private BigDecimal grossProfitRate;

    private BigDecimal netProfitRate;

    private BigDecimal areaPrice; // 平米单价

    private String confirmId;

    private BigDecimal appointMaterialFee; // 指定材料费

    private BigDecimal negotiateAreaPrice; // 议价平米单价

    private BigDecimal processCostPrice; // 议价加工单价

    private BigDecimal prdAmount; // 采购费用

    private BigDecimal rawMaterialCost; // 议价前材料费

    private BigDecimal originalProcessingFee; // 议价前加工费

    private BigDecimal squareMetrePrice; // 加工费平米单价

    private String deptId;

    private String proTime;

    private String materialUse; // 加急用料方式

    private String approveType;

    private BigDecimal length; // 长

    private BigDecimal width; // 宽

    private String boardThicknessId; // 板材厚度

    private String copperCladThicknessId; // 覆铜要求

    private String materialNo;

    private BigDecimal maxBatchArea;

    private String processFeeRule; // 加工费计算规则

    private String materialFeeRule; // 材料费计算规则

    private String materialRateRule; // 材料利率计算规则

    private String useMaterialId; // 使用材料id

    private Date firstBatchDate; // 首批交期

    private String versionCapacityDate; // 产能版本时间

    private String customerLevel; // 客户等级

    private String productGrade; // 产品等级

    private BigDecimal productProfitMargin; // 产品利润率

    private BigDecimal netSalesProfit; // 销售净利润率

    public BigDecimal getProcessFee()
    {
        return processFee;
    }

    public void setProcessFee(BigDecimal processFee)
    {
        this.processFee = processFee;
    }

    public BigDecimal getMaterialFee()
    {
        return materialFee;
    }

    public void setMaterialFee(BigDecimal materialFee)
    {
        this.materialFee = materialFee;
    }

    public BigDecimal getCostPrice()
    {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice)
    {
        this.costPrice = costPrice;
    }

    public BigDecimal getLnPrice()
    {
        return lnPrice;
    }

    public void setLnPrice(BigDecimal lnPrice)
    {
        this.lnPrice = lnPrice;
    }

    public BigDecimal getJxPrice()
    {
        return jxPrice;
    }

    public void setJxPrice(BigDecimal jxPrice)
    {
        this.jxPrice = jxPrice;
    }

    public String getNeedBord()
    {
        return needBord;
    }

    public void setNeedBord(String needBord)
    {
        this.needBord = needBord;
    }

    public Integer getReplyStock()
    {
        return replyStock;
    }

    public void setReplyStock(Integer replyStock)
    {
        this.replyStock = replyStock;
    }

    public String getMaterialId()
    {
        return materialId;
    }

    public void setMaterialId(String materialId)
    {
        this.materialId = materialId;
    }

    public String getOccupiedStock()
    {
        return occupiedStock;
    }

    public void setOccupiedStock(String occupiedStock)
    {
        this.occupiedStock = occupiedStock;
    }

    public Date getEstimateDate()
    {
        return estimateDate;
    }

    public String getEstimateDateStr()
    {
        if (estimateDate != null)
        {
            return DateUtils.formatDate(estimateDate);
        }
        return null;
    }

    public void setEstimateDate(Date estimateDate)
    {
        this.estimateDate = estimateDate;
    }

    public String getEstimateMethod()
    {
        return estimateMethod;
    }

    public void setEstimateMethod(String estimateMethod)
    {
        this.estimateMethod = estimateMethod;
    }

    public String getProductArea()
    {
        return productArea;
    }

    public void setProductArea(String productArea)
    {
        this.productArea = productArea;
    }

    public String getEstimateRemark()
    {
        return estimateRemark;
    }

    public void setEstimateRemark(String estimateRemark)
    {
        this.estimateRemark = estimateRemark;
    }

    public String getPurchRowDays()
    {
        return purchRowDays;
    }

    public void setPurchRowDays(String purchRowDays)
    {
        this.purchRowDays = purchRowDays;
    }

    public String getNotiNeedDays()
    {
        return notiNeedDays;
    }

    public void setNotiNeedDays(String notiNeedDays)
    {
        this.notiNeedDays = notiNeedDays;
    }

    public String getWaitDays()
    {
        return waitDays;
    }

    public void setWaitDays(String waitDays)
    {
        this.waitDays = waitDays;
    }

    public String getLogisticsTime()
    {
        return logisticsTime;
    }

    public void setLogisticsTime(String logisticsTime)
    {
        this.logisticsTime = logisticsTime;
    }

    public String getSaleCompanyId()
    {
        return saleCompanyId;
    }

    public void setSaleCompanyId(String saleCompanyId)
    {
        this.saleCompanyId = saleCompanyId;
    }

    public String getTotalPcses()
    {
        return totalPcses;
    }

    public void setTotalPcses(String totalPcses)
    {
        this.totalPcses = totalPcses;
    }

    public String getDayAreas()
    {
        return dayAreas;
    }

    public void setDayAreas(String dayAreas)
    {
        this.dayAreas = dayAreas;
    }

    public String getProductionCycleId()
    {
        return productionCycleId;
    }

    public void setProductionCycleId(String productionCycleId)
    {
        this.productionCycleId = productionCycleId;
    }

    public Integer getOueDays()
    {
        return oueDays;
    }

    public void setOueDays(Integer oueDays)
    {
        this.oueDays = oueDays;
    }

    public String getEstimateDateTwo()
    {
        return estimateDateTwo;
    }

    public void setEstimateDateTwo(String estimateDateTwo)
    {
        this.estimateDateTwo = estimateDateTwo;
    }

    public String getSaleCsutomerId()
    {
        return saleCsutomerId;
    }

    public void setSaleCsutomerId(String saleCsutomerId)
    {
        this.saleCsutomerId = saleCsutomerId;
    }

    public String getNewDeliveryDays()
    {
        return newDeliveryDays;
    }

    public void setNewDeliveryDays(String newDeliveryDays)
    {
        this.newDeliveryDays = newDeliveryDays;
    }

    public String getNotiCraftNo()
    {
        return notiCraftNo;
    }

    public void setNotiCraftNo(String notiCraftNo)
    {
        this.notiCraftNo = notiCraftNo;
    }

    public String getGenCraftNo()
    {
        return genCraftNo;
    }

    public void setGenCraftNo(String genCraftNo)
    {
        this.genCraftNo = genCraftNo;
    }

    public String getUrgentApproval()
    {
        return urgentApproval;
    }

    public void setUrgentApproval(String urgentApproval)
    {
        this.urgentApproval = urgentApproval;
    }

    public BigDecimal getExcessStock()
    {
        return excessStock;
    }

    public void setExcessStock(BigDecimal excessStock)
    {
        this.excessStock = excessStock;
    }

    public String getEstimateStartDate()
    {
        return estimateStartDate;
    }

    public void setEstimateStartDate(String estimateStartDate)
    {
        this.estimateStartDate = estimateStartDate;
    }

    public String getContactDeailId()
    {
        return contactDeailId;
    }

    public void setContactDeailId(String contactDeailId)
    {
        this.contactDeailId = contactDeailId;
    }

    public String getFactoryStocks()
    {
        return factoryStocks;
    }

    public void setFactoryStocks(String factoryStocks)
    {
        this.factoryStocks = factoryStocks;
    }

    public String getNotifiId()
    {
        return notifiId;
    }

    public void setNotifiId(String notifiId)
    {
        this.notifiId = notifiId;
    }

    public String getCraftNo()
    {
        return craftNo;
    }

    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }

    public String getUrgentFlag()
    {
        return urgentFlag;
    }

    public void setUrgentFlag(String urgentFlag)
    {
        this.urgentFlag = urgentFlag;
    }

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    public String getUserId()
    {
        return userId;
    }

    public void setUserId(String userId)
    {
        this.userId = userId;
    }

    public Integer getUsePurchStocks()
    {
        return usePurchStocks;
    }

    public void setUsePurchStocks(Integer usePurchStocks)
    {
        this.usePurchStocks = usePurchStocks;
    }

    public Date getCustDeliveryDate()
    {
        return custDeliveryDate;
    }

    public String getCustDeliveryDateStr()
    {
        if (custDeliveryDate != null)
        {
            return DateUtils.formatDate(custDeliveryDate);
        }
        return null;
    }

    public void setCustDeliveryDate(Date custDeliveryDate)
    {
        this.custDeliveryDate = custDeliveryDate;
    }

    public Date getAcDistributeDate()
    {
        return acDistributeDate;
    }

    public String getAcDistributeDateStr()
    {
        if (acDistributeDate != null)
        {
            return DateUtils.formatDate(acDistributeDate);
        }
        return null;
    }

    public void setAcDistributeDate(Date acDistributeDate)
    {
        this.acDistributeDate = acDistributeDate;
    }

    public BigDecimal getEngineeringFee()
    {
        return engineeringFee;
    }

    public void setEngineeringFee(BigDecimal engineeringFee)
    {
        this.engineeringFee = engineeringFee;
    }

    public BigDecimal getMouldFee()
    {
        return mouldFee;
    }

    public void setMouldFee(BigDecimal mouldFee)
    {
        this.mouldFee = mouldFee;
    }

    public BigDecimal getTestShelfFee()
    {
        return testShelfFee;
    }

    public void setTestShelfFee(BigDecimal testShelfFee)
    {
        this.testShelfFee = testShelfFee;
    }

    public String getPriceId()
    {
        return priceId;
    }

    public void setPriceId(String priceId)
    {
        this.priceId = priceId;
    }

    public BigDecimal getEngineeringFeeCopy()
    {
        return engineeringFeeCopy;
    }

    public void setEngineeringFeeCopy(BigDecimal engineeringFeeCopy)
    {
        this.engineeringFeeCopy = engineeringFeeCopy;
    }

    public BigDecimal getMouldFeeCopy()
    {
        return mouldFeeCopy;
    }

    public void setMouldFeeCopy(BigDecimal mouldFeeCopy)
    {
        this.mouldFeeCopy = mouldFeeCopy;
    }

    public BigDecimal getTestShelfFeeCopy()
    {
        return testShelfFeeCopy;
    }

    public void setTestShelfFeeCopy(BigDecimal testShelfFeeCopy)
    {
        this.testShelfFeeCopy = testShelfFeeCopy;
    }

    public String getMaterialIdCopy()
    {
        return materialIdCopy;
    }

    public void setMaterialIdCopy(String materialIdCopy)
    {
        this.materialIdCopy = materialIdCopy;
    }

    public String getFeedId()
    {
        return feedId;
    }

    public void setFeedId(String feedId)
    {
        this.feedId = feedId;
    }

    public String getCardId()
    {
        return cardId;
    }

    public void setCardId(String cardId)
    {
        this.cardId = cardId;
    }

    public String getNotifitionId()
    {
        return notifitionId;
    }

    public void setNotifitionId(String notifitionId)
    {
        this.notifitionId = notifitionId;
    }

    public String getConfimFlag()
    {
        return confimFlag;
    }

    public void setConfimFlag(String confimFlag)
    {
        this.confimFlag = confimFlag;
    }

    public String getCardUseRatio()
    {
        return cardUseRatio;
    }

    public void setCardUseRatio(String cardUseRatio)
    {
        this.cardUseRatio = cardUseRatio;
    }

    public String getCardTotalPcses()
    {
        return cardTotalPcses;
    }

    public void setCardTotalPcses(String cardTotalPcses)
    {
        this.cardTotalPcses = cardTotalPcses;
    }

    public Double getBordLength()
    {
        return bordLength;
    }

    public void setBordLength(Double bordLength)
    {
        this.bordLength = bordLength;
    }

    public Double getBordWidth()
    {
        return bordWidth;
    }

    public void setBordWidth(Double bordWidth)
    {
        this.bordWidth = bordWidth;
    }

    public BigDecimal getPrice()
    {
        return price;
    }

    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    public BigDecimal getPrdEngineeringFee()
    {
        return prdEngineeringFee;
    }

    public void setPrdEngineeringFee(BigDecimal prdEngineeringFee)
    {
        this.prdEngineeringFee = prdEngineeringFee;
    }

    public BigDecimal getPrdMouldFee()
    {
        return prdMouldFee;
    }

    public void setPrdMouldFee(BigDecimal prdMouldFee)
    {
        this.prdMouldFee = prdMouldFee;
    }

    public BigDecimal getPrdTestShelfFee()
    {
        return prdTestShelfFee;
    }

    public void setPrdTestShelfFee(BigDecimal prdTestShelfFee)
    {
        this.prdTestShelfFee = prdTestShelfFee;
    }

    public BigDecimal getPrdEngineeringFeeCopy()
    {
        return prdEngineeringFeeCopy;
    }

    public void setPrdEngineeringFeeCopy(BigDecimal prdEngineeringFeeCopy)
    {
        this.prdEngineeringFeeCopy = prdEngineeringFeeCopy;
    }

    public BigDecimal getPrdMouldFeeCopy()
    {
        return prdMouldFeeCopy;
    }

    public void setPrdMouldFeeCopy(BigDecimal prdMouldFeeCopy)
    {
        this.prdMouldFeeCopy = prdMouldFeeCopy;
    }

    public BigDecimal getPrdTestShelfFeeCopy()
    {
        return prdTestShelfFeeCopy;
    }

    public void setPrdTestShelfFeeCopy(BigDecimal prdTestShelfFeeCopy)
    {
        this.prdTestShelfFeeCopy = prdTestShelfFeeCopy;
    }

    public String getFactoryComId()
    {
        return factoryComId;
    }

    public void setFactoryComId(String factoryComId)
    {
        this.factoryComId = factoryComId;
    }

    public String getParamId()
    {
        return paramId;
    }

    public void setParamId(String paramId)
    {
        this.paramId = paramId;
    }

    public String getQcFlag()
    {
        return qcFlag;
    }

    public void setQcFlag(String qcFlag)
    {
        this.qcFlag = qcFlag;
    }

    public BigDecimal getPrdFilmFee()
    {
        return prdFilmFee;
    }

    public BigDecimal getPrdOthersFee()
    {
        return prdOthersFee;
    }

    public BigDecimal getPrdFilmFeeCopy()
    {
        return prdFilmFeeCopy;
    }

    public BigDecimal getPrdOthersFeeCopy()
    {
        return prdOthersFeeCopy;
    }

    public void setPrdFilmFee(BigDecimal prdFilmFee)
    {
        this.prdFilmFee = prdFilmFee;
    }

    public void setPrdOthersFee(BigDecimal prdOthersFee)
    {
        this.prdOthersFee = prdOthersFee;
    }

    public void setPrdFilmFeeCopy(BigDecimal prdFilmFeeCopy)
    {
        this.prdFilmFeeCopy = prdFilmFeeCopy;
    }

    public void setPrdOthersFeeCopy(BigDecimal prdOthersFeeCopy)
    {
        this.prdOthersFeeCopy = prdOthersFeeCopy;
    }

    public String getSaleDetialStatus()
    {
        return saleDetialStatus;
    }

    public void setSaleDetialStatus(String saleDetialStatus)
    {
        this.saleDetialStatus = saleDetialStatus;
    }

    public BigDecimal getUseRatio()
    {
        return useRatio;
    }

    public void setUseRatio(BigDecimal useRatio)
    {
        this.useRatio = useRatio;
    }

    public List<Icloud_CapacityDeail> getAddCapacityList()
    {
        return addCapacityList;
    }

    public void setAddCapacityList(List<Icloud_CapacityDeail> addCapacityList)
    {
        this.addCapacityList = addCapacityList;
    }

    public String getVersionDate()
    {
        return versionDate;
    }

    public void setVersionDate(String versionDate)
    {
        this.versionDate = versionDate;
    }

    public List<Icloud_MaterialArea> getAreas()
    {
        return areas;
    }

    public void setAreas(List<Icloud_MaterialArea> areas)
    {
        this.areas = areas;
    }

    public String getManufacturer()
    {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer)
    {
        this.manufacturer = manufacturer;
    }

    public String getUseAgeData()
    {
        return useAgeData;
    }

    public void setUseAgeData(String useAgeData)
    {
        this.useAgeData = useAgeData;
    }

    public String getMaterialMsg()
    {
        return materialMsg;
    }

    public void setMaterialMsg(String materialMsg)
    {
        this.materialMsg = materialMsg;
    }

    public BigDecimal getSaleFee()
    {
        return saleFee;
    }

    public void setSaleFee(BigDecimal saleFee)
    {
        this.saleFee = saleFee;
    }

    public BigDecimal getManageFee()
    {
        return manageFee;
    }

    public void setManageFee(BigDecimal manageFee)
    {
        this.manageFee = manageFee;
    }

    public BigDecimal getGrossProfitRate()
    {
        return grossProfitRate;
    }

    public void setGrossProfitRate(BigDecimal grossProfitRate)
    {
        this.grossProfitRate = grossProfitRate;
    }

    public BigDecimal getNetProfitRate()
    {
        return netProfitRate;
    }

    public void setNetProfitRate(BigDecimal netProfitRate)
    {
        this.netProfitRate = netProfitRate;
    }

    public BigDecimal getAreaPrice()
    {
        return areaPrice;
    }

    public void setAreaPrice(BigDecimal areaPrice)
    {
        this.areaPrice = areaPrice;
    }

    public String getConfirmId()
    {
        return confirmId;
    }

    public void setConfirmId(String confirmId)
    {
        this.confirmId = confirmId;
    }

    public BigDecimal getAppointMaterialFee()
    {
        return appointMaterialFee;
    }

    public void setAppointMaterialFee(BigDecimal appointMaterialFee)
    {
        this.appointMaterialFee = appointMaterialFee;
    }

    public BigDecimal getNegotiateAreaPrice()
    {
        return negotiateAreaPrice;
    }

    public void setNegotiateAreaPrice(BigDecimal negotiateAreaPrice)
    {
        this.negotiateAreaPrice = negotiateAreaPrice;
    }

    public BigDecimal getProcessCostPrice()
    {
        return processCostPrice;
    }

    public void setProcessCostPrice(BigDecimal processCostPrice)
    {
        this.processCostPrice = processCostPrice;
    }

    public BigDecimal getPrdAmount()
    {
        return prdAmount;
    }

    public void setPrdAmount(BigDecimal prdAmount)
    {
        this.prdAmount = prdAmount;
    }

    public BigDecimal getRawMaterialCost()
    {
        return rawMaterialCost;
    }

    public void setRawMaterialCost(BigDecimal rawMaterialCost)
    {
        this.rawMaterialCost = rawMaterialCost;
    }

    public BigDecimal getOriginalProcessingFee()
    {
        return originalProcessingFee;
    }

    public void setOriginalProcessingFee(BigDecimal originalProcessingFee)
    {
        this.originalProcessingFee = originalProcessingFee;
    }

    public BigDecimal getSquareMetrePrice()
    {
        return squareMetrePrice;
    }

    public void setSquareMetrePrice(BigDecimal squareMetrePrice)
    {
        this.squareMetrePrice = squareMetrePrice;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public String getProTime()
    {
        return proTime;
    }

    public void setProTime(String proTime)
    {
        this.proTime = proTime;
    }

    public String getMaterialUse()
    {
        return materialUse;
    }

    public void setMaterialUse(String materialUse)
    {
        this.materialUse = materialUse;
    }

    public String getApproveType()
    {
        return approveType;
    }

    public void setApproveType(String approveType)
    {
        this.approveType = approveType;
    }

    public BigDecimal getLength()
    {
        return length;
    }

    public void setLength(BigDecimal length)
    {
        this.length = length;
    }

    public BigDecimal getWidth()
    {
        return width;
    }

    public void setWidth(BigDecimal width)
    {
        this.width = width;
    }

    public String getBoardThicknessId()
    {
        return boardThicknessId;
    }

    public void setBoardThicknessId(String boardThicknessId)
    {
        this.boardThicknessId = boardThicknessId;
    }

    public String getCopperCladThicknessId()
    {
        return copperCladThicknessId;
    }

    public void setCopperCladThicknessId(String copperCladThicknessId)
    {
        this.copperCladThicknessId = copperCladThicknessId;
    }

    public String getMaterialNo()
    {
        return materialNo;
    }

    public void setMaterialNo(String materialNo)
    {
        this.materialNo = materialNo;
    }

    public BigDecimal getMaxBatchArea()
    {
        return maxBatchArea;
    }

    public void setMaxBatchArea(BigDecimal maxBatchArea)
    {
        this.maxBatchArea = maxBatchArea;
    }

    public String getProcessFeeRule()
    {
        return processFeeRule;
    }

    public void setProcessFeeRule(String processFeeRule)
    {
        this.processFeeRule = processFeeRule;
    }

    public String getMaterialFeeRule()
    {
        return materialFeeRule;
    }

    public void setMaterialFeeRule(String materialFeeRule)
    {
        this.materialFeeRule = materialFeeRule;
    }

    public String getMaterialRateRule()
    {
        return materialRateRule;
    }

    public void setMaterialRateRule(String materialRateRule)
    {
        this.materialRateRule = materialRateRule;
    }

    public String getUseMaterialId()
    {
        return useMaterialId;
    }

    public void setUseMaterialId(String useMaterialId)
    {
        this.useMaterialId = useMaterialId;
    }

    public Date getFirstBatchDate()
    {
        return firstBatchDate;
    }

    public void setFirstBatchDate(Date firstBatchDate)
    {
        this.firstBatchDate = firstBatchDate;
    }

    public String getVersionCapacityDate()
    {
        return versionCapacityDate;
    }

    public void setVersionCapacityDate(String versionCapacityDate)
    {
        this.versionCapacityDate = versionCapacityDate;
    }

    public String getCustomerLevel() {
        return customerLevel;
    }

    public void setCustomerLevel(String customerLevel) {
        this.customerLevel = customerLevel;
    }

    public String getProductGrade() {
        return productGrade;
    }

    public void setProductGrade(String productGrade) {
        this.productGrade = productGrade;
    }

    public BigDecimal getProductProfitMargin() {
        return productProfitMargin;
    }

    public void setProductProfitMargin(BigDecimal productProfitMargin) {
        this.productProfitMargin = productProfitMargin;
    }

    public BigDecimal getNetSalesProfit() {
        return netSalesProfit;
    }

    public void setNetSalesProfit(BigDecimal netSalesProfit) {
        this.netSalesProfit = netSalesProfit;
    }
}
