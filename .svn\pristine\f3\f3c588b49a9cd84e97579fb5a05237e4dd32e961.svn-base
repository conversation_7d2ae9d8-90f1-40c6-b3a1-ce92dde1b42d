/**
 * 
 */
package com.kyb.pcberp.modules.eg.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.BaseService;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.modules.eg.dao.CardADao;
import com.kyb.pcberp.modules.eg.dao.ProcessCraftDao;
import com.kyb.pcberp.modules.eg.dao.ProcessCraftValueDao;
import com.kyb.pcberp.modules.eg.dao.ProcessDao;
import com.kyb.pcberp.modules.eg.dao.SysProcessCraftDefValueDao;
import com.kyb.pcberp.modules.eg.dao.TemplateDao;
import com.kyb.pcberp.modules.eg.entity.EgProcess;
import com.kyb.pcberp.modules.eg.entity.ProcessCraft;
import com.kyb.pcberp.modules.eg.entity.ProcessCraftValue;
import com.kyb.pcberp.modules.eg.entity.SysProcessCraftDefValue;
import com.kyb.pcberp.modules.eg.entity.Template;
import com.kyb.pcberp.modules.eg.utils.EgUtils;
import com.kyb.pcberp.modules.production.dao.PopoDao;
import com.kyb.pcberp.modules.quality.dao.InspectDao;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

/**
 * 流程卡模版Service
 * 
 * <AUTHOR>
 * @version 2015-09-16
 */
@Service
@Transactional(readOnly = true)
public class TemplateService extends BaseService implements InitializingBean
{
    @Autowired
    private TemplateDao templateDao;
    
    @Autowired
    private ProcessDao processDao;
    
    @Autowired
    private ProcessCraftDao craftDao;
    
    @Autowired
    private ProcessCraftValueDao craftValueDao;
    
    @Autowired
    private InspectDao inspectDao;
    
    @Autowired
    private SysProcessCraftDefValueDao sysDefValueDao;
    
    @Autowired
    private PopoDao popoDao;
    
    @Autowired
    private CardADao cardADao;
    
    final static String DEF_ID = "-1";
    
    @Override
    public void afterPropertiesSet()
        throws Exception
    {
        
    }
    
    public Template getTemplate(String id)
    {
        return templateDao.get(id);
    }
    
    public Integer getTemplateByName(Template template)
    {
        return templateDao.getByName(template);
    }
    
    public Integer getCardAByTemplate(Template template)
    {
        return templateDao.getCardAByTemplate(template);
    }
    
    public List<Template> findTemplateList(Template template)
    {
        return templateDao.findList(template);
    }
    
    public Page<Template> findTemplatePage(Page<Template> page, Template template)
    {
        template.setPage(page);
        page.setList(templateDao.findList(template));
        return page;
    }
    
    public List<Template> findAllTemplateAndRelationList(Template template)
    {
        List<Template> list = templateDao.findListAndRelationList(template);
        return list;
    }
    
    public List<Template> findAllTemplateAndRelationListForTemplate(Template template)
    {
        List<Template> list = templateDao.findListAndRelationListForTemplate(template);
        return list;
    }
    
    @Transactional(readOnly = false)
    public Map<String, Object> saveTemplateRelation(Template template)
    {
        Map<String, Object> data = new HashMap<>();
        
        // 添加模版
        template.setCompany(UserUtils.getUser().getCompany());
        
        // 验证模板是否有未生产完的数据
        List<String> checkProduceDataList = templateDao.checkUpdateTemplate(template);
        if (Collections3.isNotEmpty(checkProduceDataList))
        {
            data.put("result", "checkFail");
            data.put("message", "不能对模板进行修改或删除，还有未生产完的记录!");
            data.put("checkProduceDataList", checkProduceDataList);
            return data;
        }
        
        if (DEF_ID.equals(template.getRecordId()))
        {
            template.setRecordId(null);
            saveTemplate(template);
        }
        
        // 当前模版没有工序或将工序删除了，那么需删除模版工序关联
        if (Collections3.isEmpty(template.getProcessList()))
        {
            // 删除模版工序关联
            templateDao.deleteTemplateProcessRelation(template);
            return null;
        }
        
        for (EgProcess process : template.getProcessList())
        {
            // 新工序，添加工序
            process.setCompany(template.getCompany());
            if (DEF_ID.equals(process.getRecordId()))
            {
                process.setRecordId(null);
                saveProcess(process);
            }
            else
            {
                process.preInsert();
            }
            
            // 工艺为空时，删除关联并清空工序的缓存
            if (Collections3.isEmpty(process.getCraftList()))
            {
                // 删除当前工序工艺关联
                processDao.deleteProcessCraftRelation(process);
                continue;
            }
            
            // 新工艺需要添加关联，旧工艺不需要
            List<ProcessCraft> tempRelaCraft = Lists.newArrayList();
            
            // 循环添加工艺以及工艺值
            for (ProcessCraft craft : process.getCraftList())
            {
                boolean isNewCraft = false;
                // 添加工艺
                if (DEF_ID.equals(craft.getRecordId()))
                {
                    isNewCraft = true;
                    craft.setCompany(template.getCompany());
                    craft.setRecordId(null);
                    saveProcessCraft(craft);
                    tempRelaCraft.add(craft);
                }
                else
                {
                    // 判断当前工序是否已经存在当前工艺，不存在时需要添加
                    ProcessCraft c = processDao.getProcessCraftRelationCount(process.getRecordId(),
                        craft.getRecordId(),
                        template.getCompany().getRecordId());
                    if (c == null || (c.getCompany() != null))
                    {
                        tempRelaCraft.add(craft);
                    }
                    
                    // wc 2016-12-17 更新或添加工艺默认值
                    operCraftDefValue(craft, template);
                    
                }
                
                // 工艺无工艺值时跳过工艺值添加
                if (Collections3.isEmpty(craft.getCraftValueList()))
                {
                    continue;
                }
                craft.setCompany(UserUtils.getUser().getCompany());
                // 删除所有工艺值
                for (ProcessCraftValue value : craft.getCraftValueList())
                {
                    // 新工艺时所有值都需要添加，旧工艺时仅添加Id为-1的值
                    if (isNewCraft || DEF_ID.equals(value.getRecordId()))
                    {
                        // 添加工艺值
                        value.setCompany(template.getCompany());
                        value.setRecordId(null);
                        value.setProcessCraftId(Long.parseLong(craft.getRecordId()));
                        saveProcessCraftValue(value);
                    }
                }
            }
            
            // 删除当前工序工艺关联
            processDao.deleteProcessCraftRelation(process);
            
            // 添加当前工序工艺关联
            if (!Collections3.isEmpty(tempRelaCraft))
            {
                process.setCraftList(tempRelaCraft);
                processDao.insertProcessCraftRelation(process);
            }
        }
        
        // 删除模版工序关联
        templateDao.deleteTemplateProcessRelation(template);
        // 添加模版工序关联
        templateDao.insertTemplateProcessRelation(template);
        
        // 删除缓存
        EgUtils.removeProcess(template.getCompany());
        EgUtils.removeProcessCraft(template.getCompany());
        
        data.put("result", "success");
        data.put("message", "保存模版成功！");
        return data;
    }
    
    /**
     * WC 2016-12-20 更新非系统工艺默认值、添加或更新系统工艺默认值
     * 
     * @param craft
     */
    private void operCraftDefValue(ProcessCraft craft, Template template)
    {
        // 查询该工艺是系统工艺还是自定义工艺
        ProcessCraft findCraft = craftDao.findCraftByRecordId(craft.getRecordId());
        // 如果是系统工艺，工艺默认值保存到系统工艺默认值表中
        if (findCraft.getCompany() == null)
        {
            SysProcessCraftDefValue defValueObj = new SysProcessCraftDefValue();
            defValueObj.setCompany(template.getCompany());
            defValueObj.setCraft(craft);
            // 按公司、系统工艺ID去系统工艺默认值表中查询是否存在，存在更新，不存在添加
            SysProcessCraftDefValue newDefValueObj = sysDefValueDao.findSysDefValueByCompanyAndCraft(defValueObj);
            if (newDefValueObj == null)
            {
                defValueObj.setDefaultValue(craft.getDefaultValue() == null ? "" : craft.getDefaultValue());
                defValueObj.preInsert();
                sysDefValueDao.insert(defValueObj);
            }
            else
            {
                newDefValueObj.setDefaultValue(craft.getDefaultValue() == null ? "" : craft.getDefaultValue());
                newDefValueObj.preUpdate();
                sysDefValueDao.UpdateDefValue(newDefValueObj);
            }
            
        }
        else
        {
            craft.preUpdate();
            craftDao.updateCraftDefaultValue(craft);
        }
    }
    
    @Transactional(readOnly = false)
    public void saveTemplate(Template template)
    {
        if (template.getIsNewRecord())
        {
            template.preInsert();
            templateDao.insert(template);
        }
        else
        {
            template.preUpdate();
            templateDao.update(template);
        }
    }
    
    @Transactional(readOnly = false)
    public Map<String, Object> deleteTemplate(String id)
    {
        Map<String, Object> data = new HashMap<>();
        Template template = new Template(id);
        template.setCompany(UserUtils.getUser().getCompany());
        int count = cardADao.countCardA(template);
        if (count > 0)
        {
            data.put("result", "countFail");
            data.put("message", "模板已引用，不能被删除！");
            return data;
        }
        
        // 验证模板是否有未生产完的数据
        List<String> checkProduceDataList = templateDao.checkUpdateTemplate(template);
        if (Collections3.isNotEmpty(checkProduceDataList))
        {
            data.put("result", "checkFail");
            data.put("message", "不能对模板进行修改或删除，还有未生产完的记录!");
            data.put("checkProduceDataList", checkProduceDataList);
            return data;
        }
        
        // 删除模版
        templateDao.delete(template);
        
        // 删除关联数据
        templateDao.deleteTemplateProcessRelation(template);
        
        data.put("result", "success");
        data.put("message", "删除模版成功！");
        return data;
    }
    
    /**
     * 获取所有的工序
     * 
     * @param process
     * @return
     */
    public List<EgProcess> findAllProcessList()
    {
        return EgUtils.getProcessList();
    }
    
    @Transactional(readOnly = false)
    public void saveProcess(EgProcess process)
    {
        if (process.getIsNewRecord())
        {
            process.preInsert();
            processDao.insert(process);
        }
        else
        {
            process.preUpdate();
            processDao.update(process);
        }
    }
    
    @Transactional(readOnly = false)
    public void deleteProcess(EgProcess process)
    {
        processDao.delete(process);
    }
    
    public List<ProcessCraft> findAllProcessCraftAndValues()
    {
        return EgUtils.getCraftList();
    }
    
    @Transactional(readOnly = false)
    public void saveProcessCraft(ProcessCraft craft)
    {
        if (craft.getIsNewRecord())
        {
            craft.preInsert();
            craftDao.insert(craft);
        }
        else
        {
            craft.preUpdate();
            craftDao.update(craft);
        }
    }
    
    @Transactional(readOnly = false)
    public void deleteProcessCraft(ProcessCraft craft)
    {
        craftDao.delete(craft);
    }
    
    @Transactional(readOnly = false)
    public void saveProcessCraftValue(ProcessCraftValue craftValue)
    {
        if (craftValue.getIsNewRecord())
        {
            craftValue.preInsert();
            craftValueDao.insert(craftValue);
        }
        else
        {
            craftValue.preUpdate();
            craftValueDao.update(craftValue);
        }
    }
    
    /**
     * 是否可以跳过工序，存在返工不可跳过
     * 
     * @param templateId
     * @param processId
     * @return
     */
    public Boolean isContinueProcess(String templateId, String processId)
    {
        Map<String, Object> params = Maps.newHashMap();
        params.put("notiStatus",
            "(" + TypeKey.SL_NOTIFICATION_STATUS_FEED + "," + TypeKey.SL_NOTIFICATION_STATUS_SUPPLEMENTARYFOOD + ")");
        params.put("company", UserUtils.getUser().getCompany());
        params.put("templateId", templateId);
        params.put("processId", processId);
        params.put("batchStatus",
            "(" + TypeKey.PRODUCE_BATCH_STATUS_INIT + "," + TypeKey.PRODUCE_BATCH_STATUS_PAUSE + ","
                + TypeKey.PRODUCE_BATCH_STATUS_LOSS + ")");
        Integer reworkQty = inspectDao.getReworkQtyByTemplateAndProcess(params);
        return (reworkQty == null) || (reworkQty <= 0);
    }
    
    /**
     * 是否有外发
     * 
     * @param templateId
     * @param processId
     * @return
     */
    public Boolean isOutwardProcessing(String templateId, String processId)
    {
        Integer count = popoDao
            .getCountByTemplateIdAndProcess(templateId, UserUtils.getUser().getCompany().getRecordId(), processId);
        return (count == null) || (count <= 0);
    }
    
    /**
     * ycy 2017-06-08 删除工艺验证
     * 
     * @param template
     * @return
     */
    public Integer craftVerification(Template template)
    {
        return templateDao.craftVerification(template);
    }
    
    @Transactional(readOnly = false)
    public String updateShowFlag(Template template)
    {
        templateDao.updateShowFlag(template);
        return "success";
    }
    
    @Transactional(readOnly = false)
    public String updateShowWay(ProcessCraft processCraft)
    {
        processCraft.setCompany(UserUtils.getUser().getCompany());
        processDao.deleteInstallData(processCraft);
        processCraft.preInsert();
        processDao.insertInstall(processCraft);
        return "success";
    }
}