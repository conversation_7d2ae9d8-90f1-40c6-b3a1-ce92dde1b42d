package com.kyb.pcberp.common.utils;

import java.util.List;

import org.apache.shiro.util.CollectionUtils;

import com.kyb.pcberp.modules.contract.dao.ContractCraftDao;
import com.kyb.pcberp.modules.contract.entity.ContractCraft;
import com.kyb.pcberp.modules.crm.dao.CustomerContactDao;
import com.kyb.pcberp.modules.crm.dao.CustomerDao;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.crm.entity.CustomerContact;
import com.kyb.pcberp.modules.purch.dao.SupplierDao;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import com.kyb.pcberp.modules.sys.dao.BranchDao;
import com.kyb.pcberp.modules.sys.dao.DepartmentDao;
import com.kyb.pcberp.modules.sys.entity.Branch;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.Department;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

public class ContractUtils
{

    private CustomerDao customerDao = SpringContextHolder.getBean(CustomerDao.class);
    
    private SupplierDao supplierDao = SpringContextHolder.getBean(SupplierDao.class);
    
    private BranchDao branchDao = SpringContextHolder.getBean(BranchDao.class);
    
    private CustomerContactDao customerContactDao = SpringContextHolder.getBean(CustomerContactDao.class);
    
    private DepartmentDao departmentDao = SpringContextHolder.getBean(DepartmentDao.class);
    
    private ContractCraftDao contractCraftDao = SpringContextHolder.getBean(ContractCraftDao.class);
    
    public static ContractUtils getInstance()
    {
        return new ContractUtils();
    }
    
    public Customer getCustomerInfo(String customerId)
    {
        Customer customer = new Customer();
        customer.setRecordId(customerId);
        return customerDao.get(customer);
    }
    
    public Company getCompanyInfo(String Name)
    {
        Company c = new Company();
        return c;
    }
    
    public CustomerContact getCustomerContact(String name,Customer customer)
    {
        customer.setCompany(UserUtils.getUser().getCompany());
        List<CustomerContact> list = customerContactDao.getCustomerContactList(customer);
        if(CollectionUtils.isEmpty(list))
        {
            return null;
        }
        if(StringUtils.isEmpty(name))
        {
            if(customer.getBizPerson()!=null)
            {
                for(CustomerContact cc:list)
                {
                    if(cc.getRecordId().equals(customer.getBizPerson().getRecordId()))
                    {
                        return cc;
                    }
                }
            }
            return list.get(0);
        }
        else
        {
            for(CustomerContact cc:list)
            {
                if(name.equals(cc.getName()))
                {
                    return cc;
                }
            }
        }
        return null;
    }
    
    public Department getDepartment(String name)
    {
        if(StringUtils.isEmpty(name))
        {
            return null;
        }
        Department dept = new Department();
        dept.setCompany(UserUtils.getUser().getCompany());
        dept.setName(name);
        List<Department> deptList = departmentDao.findList(dept);
        if(!CollectionUtils.isEmpty(deptList))
        {
            return deptList.get(0);
        }
        return null;
    }
    
    
    public Boolean validateIsOutOrder(String madeSupplierName)
    {
        return false;
    }
    
    public Branch getBranch(String name)
    {
        Branch branch = new Branch();
        branch.setCompany(UserUtils.getUser().getCompany());
        List<Branch> bs = branchDao.findList(branch);
        if(!CollectionUtils.isEmpty(bs))
        {
            for(Branch s:bs)
            {
                if(s.getShortName().equals(name)||s.getName().equals(name))
                {
                    return s;
                }
            }
        }
        return null;
    }
    
    public Supplier getSupplier(String name)
    {
        List<Supplier> suppliers = supplierDao.queryAllSupplier(UserUtils.getUser().getCompany());
        if(!CollectionUtils.isEmpty(suppliers))
        {
            for(Supplier s:suppliers)
            {
                if(s.getShortName().equals(name)||s.getName().equals(name))
                {
                    return s;
                }
            }
        }
        return null; 
    }
    
    public ContractCraft getCraft(String no)
    {
        ContractCraft query = new ContractCraft();
        query.setCompany(UserUtils.getUser().getCompany());
        query.setNo(no);
        List<ContractCraft> cc = contractCraftDao.getContractCraftByNo(query);
        if(!CollectionUtils.isEmpty(cc))
        {
            return cc.get(0);
        }
        return null;
    }
}
