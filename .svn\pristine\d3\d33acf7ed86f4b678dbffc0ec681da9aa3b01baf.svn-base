<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.oa.dao.Oa_dao">
	<select id="getUpdateList" resultType="Oa_update">
		SELECT
		a.recordId,
		a.content,
		a.no,
		a.createdBy,
		a.createdDate,
		a.activeFlag,
		b.`name`
		FROM
		oa_sysUpdate a
		LEFT JOIN oa_md_employee b ON b.recordId = a.createdBy
		WHERE
		1=1
		<if test="createdBy != null and createdBy.recordId != '' and createdBy.recordId != null">
			AND a.createdBy = #{createdBy.recordId}
		</if>
		<if test="condition != null and condition != ''">
			AND a.content LIKE CONCAT('%', #{condition}, '%')
		</if>
		AND a.activeFlag = 1
        ORDER BY a.createdDate DESC
	</select>

	<select id="getNoticeList" resultType="Notice">
		SELECT
		a.recordId,
		a.groupId,
		a.departId,
		a.content,
		a.createdBy,
		a.createdDate,
		a.activeFlag,
		b.`name`,
		(
		SELECT
		GROUP_CONCAT(departmentName)
		FROM
		oa_department_relation
		WHERE
		FIND_IN_SET(recordId, a.departId)
		) AS "path"
		FROM
		oa_notice a
		LEFT JOIN oa_md_employee b ON b.recordId = a.createdBy
		WHERE
		a.groupId = #{groupId}
		<if test="departId != null and departId != ''">
			AND #{departId} LIKE CONCAT("%",a.departId,"%")
		</if>
		<if test="createdBy != null and createdBy.recordId != '' and createdBy.recordId != null">
			AND a.createdBy = #{createdBy.recordId}
		</if>
		<if test="condition != null and condition != ''">
			AND a.content LIKE CONCAT('%', #{condition}, '%')
		</if>
		AND a.activeFlag = 1
	</select>

	<!--审批-->
	<sql id="kybAuditSql">
		SELECT
			a.recordId,
			CONCAT(
				IFNULL(
					f.name,
					''
				),
				" ",
				IFNULL(d.`no`, ''),
				" ",
				IFNULL(b.`name`, ''),
				" ",
				IFNULL(a.createdDate, '')
			) AS "content",
			e.`name` AS "status",
			"2" AS "type",
			a.createdDate,
			d.`no` AS "no",
			f.name AS "name",
			NULL AS "volumeRatio",
			NULL AS "taskType",
			NULL AS "requireStatus",
			NULL AS "dayType"
		FROM
			oa_approve_record c
		JOIN oa_audit a ON a.recordId = c.auditId
		LEFT JOIN oa_md_employee b ON b.recordId = a.empId
		LEFT JOIN oa_number d ON d.id = a.recordId
		LEFT JOIN oa_position e ON e.recordId = c.handelPositionId
		LEFT JOIN oa_audit_type_sheet f ON f.no = a.auditType
		WHERE
			c.`status` IS NULL
		AND b.`name` IS NOT NULL
		AND a.activeFlag = 1
		AND a.recordId IN (
				SELECT
					auditId
				FROM oa_approve_record
				WHERE #{empId} IN (approveUsers)
				AND `status` IS NULL
			)
	</sql>

	<!-- 报告 -->
	<sql id="kybReportSql">
		SELECT
			a.recordId AS "recordId",
			a.content AS "content",
			b.name AS "status",
			"3" AS "type",
			a.createdDate,
			NULL AS "no",
			NULL AS "name",
			NULL AS "volumeRatio",
			NULL AS "taskType",
			NULL AS "requireStatus",
			IFNULL(
					(
						CASE a.flag
						WHEN 1 THEN
							DATE_FORMAT(
								DATE(a.`day`),
								'%Y年%m月%d日'
							)
						WHEN 2 THEN
							CONCAT(
								LEFT (YEARWEEK(DATE(a.day), 7), 4),
								'年',
								RIGHT (YEARWEEK(DATE(a.day), 7), 2),
								'周'
							)
						WHEN 3 THEN
							DATE_FORMAT(a.day, '%Y年%m月')
						END
					),'') AS "dayType"
		FROM
			oa_daily a
		LEFT JOIN oa_md_employee b ON b.recordId = a.employee
		WHERE
			a.activeFlag = 1
		AND a.content IS NOT NULL
		AND a.status = 1
		AND (
			a.recordId IN (
				SELECT
					daily
				FROM oa_daily_share
				WHERE `share` = #{empId} AND status = 1
			)
			OR (b.superEmpId = #{empId} AND a.status = 1)
		)
	</sql>

	<!-- 任务 -->
	<sql id="KybTaskSql">
		SELECT
			a.recordId,
			a.content AS "content",
			(
				SELECT
					GROUP_CONCAT(q.`name`)
				FROM
					oa_task_participate p
				LEFT JOIN oa_md_employee q ON q.recordId = p.participate
				WHERE
					p.taskId = a.recordId
			) AS "status",
			"4" AS "type",
			a.createdDate,
			NULL AS "no",
			b.name AS "name",
			a.volumeRatio AS "volumeRatio",
			a.taskType AS "taskType",
			a.requireStatus AS "requireStatus",
			NULL AS "dayType"
		FROM
			oa_task a
		LEFT JOIN oa_md_employee b ON b.recordId = a.principals
		<!--AND a.`schedule` <![CDATA[<]]> 100-->
		WHERE a.activeFlag = 1  AND a.status = 1
		AND (a.createdBy = #{empId}
			OR a.recordId IN (
				SELECT
					taskId
				FROM oa_task_participate
				WHERE activeFlag = 1 AND participate = #{empId}
			)
			OR FIND_IN_SET(a.principals,(SELECT
			CASE

			WHEN
			aa.recordId2 IS NOT NULL
			AND aa.recordId3 IS NOT NULL THEN
			CONCAT( aa.recordId1, ',', aa.recordId2, ',', aa.recordId3 )
			WHEN aa.recordId2 IS NOT NULL THEN
			CONCAT( aa.recordId1, ',', aa.recordId2 )
			WHEN aa.recordId3 IS NOT NULL THEN
			CONCAT( aa.recordId1, ',', aa.recordId3 ) ELSE NULL
			END
			FROM
			(
			SELECT
			a.recordId AS "recordId1",
			GROUP_CONCAT( DISTINCT b.recordId ) AS "recordId2",
			GROUP_CONCAT( DISTINCT c.recordId ) AS "recordId3"
			FROM
			oa_md_employee a
			LEFT JOIN oa_md_employee b ON b.superEmpId = a.recordId AND b.workStatus = 1 AND b.activeFlag = 1
			LEFT JOIN oa_md_employee c ON c.superEmpId = b.recordId AND c.workStatus = 1 AND c.activeFlag = 1
			WHERE
			a.recordId = #{empId}
			) aa) )
		)
		GROUP BY a.recordId
	</sql>

	<!-- 通知公告 -->
	<sql id="noticeSql">
		SELECT
			a.recordId AS "recordId",
			a.content AS "content",
			DATE_FORMAT(
				DATE(a.createdDate),
				'%Y年%m月%d日'
			) AS "status",
			"5" AS "type",
			a.createdDate,
			NULL AS "no",
			NULL AS "name",
			NULL AS "volumeRatio",
			NULL AS "taskType",
			NULL AS "requireStatus",
			NULL AS "dayType"
		FROM
			oa_notice a
		WHERE a.activeFlag = 1 AND FIND_IN_SET(a.departId,
			(SELECT
					groupId
				FROM
					oa_md_employee
				WHERE
					recordId = #{empId}
			)
		)
	</sql>

	<select id="getWorkList" resultType="KybWork">
		SELECT
		aa.recordId,
		aa.content,
		aa.status,
		aa.type AS "type",
-- 		aa.createdDate,
		aa.no,
		aa.name,
		aa.volumeRatio,
		aa.taskType,
		aa.requireStatus,
		aa.dayType
		FROM
		(
		<include refid="kybAuditSql"/>
		UNION
		<include refid="kybReportSql"/>
		UNION
		<include refid="KybTaskSql"/>
		UNION
		<include refid="noticeSql"/>
		) aa
		ORDER BY aa.createdDate DESC
	</select>

	<select id="getDepart" resultType="Hr_DepartMent">
		SELECT *
		FROM oa_department_relation a
		WHERE a.allId = #{recordId}
	  AND a.activeFlag = 1
	</select>

	<select id="getPositionList" resultType="Position">
		SELECT
			recordId,
			`name`
		FROM
			oa_position
		WHERE
			activeFlag = 1
		  AND (groupId = #{departId} OR groupId IS NULL)
	</select>

	<select id="getComList" resultType="Hr_DepartMent">
		SELECT
			(
				CASE
					WHEN c.departmentLevel = 2 THEN
						c.departmentName
					WHEN d.departmentLevel = 2 THEN
						CONCAT(
								d.departmentName,
								'-',
								c.departmentName
							)
					WHEN e.departmentLevel = 2 THEN
						CONCAT(
								e.departmentName,
								'-',
								d.departmentName,
								'-',
								c.departmentName
							)
					END
				) AS "departmentName",
			a.allId,
			(
				CASE
					WHEN c.departmentLevel = 2 THEN
						c.recordId
					WHEN d.departmentLevel = 2 THEN
						d.recordId
					WHEN e.departmentLevel = 2 THEN
						e.recordId
					END
				) AS "oaCompanyId",
			(
				CASE
					WHEN c.departmentLevel = 2 THEN
						c.departmentName
					WHEN d.departmentLevel = 2 THEN
						d.departmentName
					WHEN e.departmentLevel = 2 THEN
						e.departmentName
					END
				) AS "oaCompanyName",
			c.recordId AS "oaDepartId",
			(
				CASE
					WHEN c.departmentLevel = 3 OR c.departmentLevel= 4 THEN
						c.shareStatus
					WHEN d.departmentLevel = 3 OR c.departmentLevel= 4 THEN
						d.shareStatus
					WHEN e.departmentLevel = 3 OR c.departmentLevel= 4 THEN
						e.shareStatus
					END
				) AS "shareStatus"
		FROM
			oa_department_relation_employee a
				LEFT JOIN oa_department_relation c ON c.recordId = a.allId
				LEFT JOIN oa_department_relation d ON d.recordId = c.allId
				LEFT JOIN oa_department_relation e ON e.recordId = d.allId
		WHERE
			a.employeeId = #{recordId}
		  AND a.activeFlag = 1
	</select>

	<select id="getExpenseTypeList" resultType="Oa_expenseType">
		SELECT
			*
		FROM oa_expense_type
		WHERE activeFlag = 1
		ORDER BY seqNum
	</select>

	<select id="getGroupDepartmentRelationList" resultType="Hr_DepartRelation">
		SELECT
			*
		FROM group_department_relation
		WHERE activeFlag = 1 AND bindErpId IS NOT NULL
	</select>

	<select id="getCustomerList" resultType="Customer">
		SELECT
			recordId,
			companyId,
			no,
			`name`,
			shortName
		FROM md_customer
		WHERE FIND_IN_SET(companyId,#{companyId}) AND activeFlag = 1 AND `status` = 100302
	</select>

	<select id="getAuditWorkNum" resultType="Integer">
		SELECT
			count(1) AS "auditNum"
		FROM
			oa_approve_record c
		JOIN oa_audit a ON a.recordId = c.auditId
		LEFT JOIN oa_md_employee b ON b.recordId = a.empId
		LEFT JOIN oa_number d ON d.id = a.recordId
		LEFT JOIN oa_position e ON e.recordId = c.handelPositionId
		LEFT JOIN oa_audit_type_sheet f ON f.no = a.auditType
		WHERE
			c.`status` IS NOT NULL AND c.`status` = 'assent'
		AND b.`name` IS NOT NULL
		AND a.activeFlag = 1
		AND a.recordId IN (
				SELECT
					auditId
				FROM oa_approve_record
				WHERE #{employeeId} IN (approveUsers)
				AND (`status` IS NOT NULL AND `status` = 'assent')

			)
	</select>

	<select id="getAuditTypeName" resultType="Oa_audit">
		SELECT
			recordId,
			no AS "auditType",
			`name` AS "auditTypeCh",
			iconClass AS "iconClass",
			color AS "color",
			routerName AS "routerName"
		FROM
			oa_audit_type_sheet
		WHERE
		<!--未被删除，并且是有效的数据-->
		activeFlag = 1 AND status = 1 AND attachments = 2
		ORDER BY no
	</select>

	<select id="getSaleCompanyList" resultType="Hr_DepartRelation">
		SELECT
			b.*
		FROM group_department_relation a
		LEFT JOIN md_company b ON b.recordId = a.bindErpId
		WHERE a.activeFlag = 1 AND b.name IS NOT NULL
		AND a.salesFlag IS NOT NULL AND a.salesFlag <![CDATA[<>]]> ''
		GROUP BY b.recordId
	</select>

	<select id="getSaleCustomerList" resultType="Customer">
		select * from md_customer WHERE activeFlag = 1 AND companyId = #{companyId}
	</select>

	<select id="getOaEmpList" resultType="Hr_Employee">
		SELECT
		a.recordId,
		a.`name`,
		a.phone,
		a.position,
		a.userId,
		b.fileUrl,
		b.defaultImg,
		b.recordId AS "user.recordId",
		b.userName AS "user.userName",
		(
		SELECT
		GROUP_CONCAT(departmentName)
		FROM
		oa_department_relation
		WHERE
		FIND_IN_SET(recordId, a.groupId)
		) AS "path",
		a.departId,
		a.manage,
		a.workStatus
		FROM
		oa_md_employee a
		LEFT JOIN icloud_sm_user b ON b.recordId = a.userId
		WHERE
		a.activeFlag = 1
		AND FIND_IN_SET(#{recordId}, a.groupId)
		AND a.salarySystemId IS NULL
		<choose>
			<when test="workStatus == 1">
				AND a.workStatus = 1
			</when>
			<otherwise>
				AND a.workStatus <![CDATA[<>]]> 1
			</otherwise>
		</choose>
		<if test="condition != null and condition != ''">
			AND CONCAT(
			a.`name`,
			(
			SELECT
			GROUP_CONCAT(departmentName)
			FROM
			oa_department_relation
			WHERE
			FIND_IN_SET(recordId, a.groupId)
			)
			) LIKE CONCAT('%', #{condition}, '%')
		</if>
	</select>

	<select id="getAuditHandOver" resultType="Hr_Employee">
		SELECT
			b.recordId,
			b.name
		FROM
			oa_handover a
		LEFT JOIN oa_md_employee b ON b.recordId=a.employeeId
		WHERE
			a.auditId = #{employeeId}
		AND a.activeFlag = 1
	</select>

	<select id="getOrganizationAuditList" resultType="Hr_DepartMent">
		SELECT
			a.departmentLevel AS "departmentLevel",
			a.recordId AS "allId",
			a.recordId AS "recordId",
			a.scale,
			CASE a.departmentLevel WHEN 4 THEN
		    CONCAT(IFNULL(c.shortName,c.departmentName),'-',IFNULL(b.shortName,b.departmentName),'-',IFNULL(a.shortName,a.departmentName))
		    WHEN 3 THEN CONCAT(IFNULL(b.shortName,b.departmentName),'-',IFNULL(a.shortName,a.departmentName))
		    WHEN 2 THEN IFNULL(a.shortName,a.departmentName)
		    ELSE NULL END AS "departmentName"
		FROM oa_department_relation a
	    LEFT JOIN oa_department_relation b ON b.recordId = a.allId
	    LEFT JOIN oa_department_relation c ON c.recordId = b.allId
		WHERE a.activeFlag = 1 AND a.departmentLevel IN(2,3,4)
		  AND a.salarySystemId IS NULL
		  AND a.groupId = 3
		  AND a.scale IS NOT NULL
	</select>

	<select id="getCategoryAuditList" resultType="Oa_audit">
		SELECT
			a.recordId AS "recordId",
			CASE a.categoryLevel WHEN 1 THEN a.categoryName WHEN 2 THEN CONCAT(b.categoryName,'-',a.categoryName)
			WHEN 3 THEN CONCAT(c.categoryName,'-',b.categoryName,'-',a.categoryName)  ELSE NULL END AS "categoryName",
			a.categoryLevel AS "categoryLevel",
			a.type AS "type",
			a.type AS "dimensionId"
		FROM hr_internal_category a
		LEFT JOIN hr_internal_category b ON b.recordId = a.allId
		LEFT JOIN hr_internal_category c ON c.recordId = b.allId
		WHERE a.activeFlag = 1
		<if test="type != null and type != ''">
			AND a.type = #{type}
		</if>
		ORDER BY a.categoryLevel DESC
	</select>

	<select id="getGroupList" resultType="Hr_DepartMent">
		SELECT
			CASE a.departmentLevel WHEN 3 THEN CONCAT(b.departmentName,'-',a.departmentName)
			WHEN 4 THEN CONCAT(c.departmentName,'-',b.departmentName,'-',a.departmentName)
			ELSE a.departmentName END AS "name",
			a.*
		FROM oa_department_relation a
		LEFT JOIN oa_department_relation b ON b.recordId = a.allId
		LEFT JOIN oa_department_relation c ON c.recordId = b.allId
		WHERE a.activeFlag = 1 AND a.salarySystemId IS NULL
		<if test="groupId != null and groupId != ''">
			AND a.groupId = #{groupId}
		</if>
		<if test="departmentLevel != null and departmentLevel != ''">
			AND a.departmentLevel = #{departmentLevel}
		</if>
		<if test="list != null and list.size > 0">
			AND (
				a.recordId
			) IN
			<foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="getAuditList" resultType="Oa_audit">
		SELECT
			b.`name`,
			CASE WHEN e.recordId IS NOT NULL THEN CONCAT(e.categoryName,'-',d.categoryName,'-',c.categoryName)
			WHEN d.recordId IS NOT NULL THEN CONCAT(d.categoryName,'-',c.categoryName)
			ELSE c.categoryName END AS "categoryName",
			CASE WHEN i.recordId IS NOT NULL THEN CONCAT(i.departmentName,'-',h.departmentName,'-',g.departmentName,'-',f.departmentName)
			WHEN h.recordId IS NOT NULL THEN CONCAT(h.departmentName,'-',g.departmentName,'-',f.departmentName)
			WHEN g.recordId IS NOT NULL THEN CONCAT(g.departmentName,'-',f.departmentName)
			ELSE f.departmentName END AS "departmentName",
			j.no,
			CASE a.auditType WHEN 22002 THEN ROUND(IFNULL(a.purchaseNum, 0) * IFNULL(a.purchasePrice, 0),2)
			WHEN 22003 THEN IFNULL(a.money, 0)
			WHEN 22013 THEN IFNULL(a.expensePrice, 0)
			WHEN 22014 THEN IFNULL(a.expensePrice, 0)
			WHEN 22021 THEN IFNULL(a.expensePrice, 0)
			WHEN 22022 THEN IFNULL(a.expensePrice, 0)
			WHEN 22024 THEN IFNULL(k.totalMoney, 0)
			ELSE IFNULL(a.money, 0) END AS "money",
			a.*
		FROM oa_audit a
		LEFT JOIN oa_md_employee b ON b.recordId = a.empId
		LEFT JOIN hr_internal_category c ON c.recordId = a.categoryId
		LEFT JOIN hr_internal_category d ON d.recordId = c.allId
		LEFT JOIN hr_internal_category e ON e.recordId = d.allId
		LEFT JOIN oa_department_relation f ON f.recordId = a.oaDepartId
		LEFT JOIN oa_department_relation g ON g.recordId = f.allId
		LEFT JOIN oa_department_relation h ON h.recordId = g.allId
		LEFT JOIN oa_department_relation i ON i.recordId = h.allId
		LEFT JOIN oa_number j ON j.id = a.recordId AND j.type = a.auditType
		LEFT JOIN hr_bonus_allocation k ON k.recordId = a.concatHrId
		WHERE a.activeFlag = 1 AND a.categoryId = #{categoryId}
		<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
			AND DATE_FORMAT(a.createdDate, '%Y-%m-%d' ) >= DATE_FORMAT(#{sentTimeStartQr},'%Y-%m-%d')
		</if>
		<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
			AND DATE_FORMAT(a.createdDate, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT(#{sentTimeEndQr},'%Y-%m-%d')
		</if>
		<if test="queryList != null and queryList.size > 0">
			AND (
				a.oaDepartId
			) IN
			<foreach collection="queryList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="loadAllOaEmpList" resultType="Hr_Employee">
		SELECT
		a.recordId,
		a.`name`,
		a.phone,
		a.position,
		a.userId,
		b.fileUrl,
		b.defaultImg,
		b.recordId AS "user.recordId",
		b.userName AS "user.userName",
		(
		SELECT
		GROUP_CONCAT(departmentName)
		FROM
		oa_department_relation
		WHERE
		FIND_IN_SET(recordId, a.groupId)
		) AS "path",
		a.departId,
		a.manage,
		a.workStatus
		FROM
		oa_md_employee a
		LEFT JOIN icloud_sm_user b ON b.recordId = a.userId
		WHERE
		a.activeFlag = 1
		AND FIND_IN_SET(#{recordId}, a.groupId)
		AND a.salarySystemId IS NULL
		<if test="condition != null and condition != ''">
			AND CONCAT(
			a.`name`,
			(
			SELECT
			GROUP_CONCAT(departmentName)
			FROM
			oa_department_relation
			WHERE
			FIND_IN_SET(recordId, a.groupId)
			)
			) LIKE CONCAT('%', #{condition}, '%')
		</if>
	</select>

</mapper>