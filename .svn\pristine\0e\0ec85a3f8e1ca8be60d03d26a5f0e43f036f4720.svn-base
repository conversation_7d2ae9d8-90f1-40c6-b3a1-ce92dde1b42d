const timePackage = {
    template: '#timePackage',
    created:function(){
        this.$store.dispatch('myStore/setUserInformation');
    },
    data(){
        return {
            searchInfo: '',
            message: '',
            repairCompanyId: null,
            timePageList: [
                {
                    recordId: "1", hours: 200, money: 10000,content:"套餐一"
                },
                {
                    recordId: "2", hours: 300, money: 13500,content:"套餐二"
                },
                {
                    recordId: "3", hours: 600, money: 21000,content:"套餐三"
                },
            ],
            monthlyList: [],
            monthly: "",
            timePage: {},
            showUseHourPackList:[],
            repaircompanyName:"",
            buyTimePage:{},
            applyCompanyId:null,
            applycompanyName:"",
            usetimePageList:[],
            applyCustomList:[],
            applyCompanyIdTwo:null,
            checkedStarsOne: 0,
            checkedStarsTwo:0,
            checkedStarsThree:0,
            efficiencyList:[
                {"recordId" : "1", "value": 1},
                {"recordId" : "2", "value": 2},
                {"recordId" : "3", "value": 3},
                {"recordId" : "4", "value": 4},
                {"recordId" : "5", "value": 5}
            ],
            massScoreList:[
                {"recordId" : "1", "value": 1},
                {"recordId" : "2", "value": 2},
                {"recordId" : "3", "value": 3},
                {"recordId" : "4", "value": 4},
                {"recordId" : "5", "value": 5}
            ],
            attitudeScoreList:[
                {"recordId" : "1", "value": 1},
                {"recordId" : "2", "value": 2},
                {"recordId" : "3", "value": 3},
                {"recordId" : "4", "value": 4},
                {"recordId" : "5", "value": 5}
            ],
            purchasePlan:"2",
            currentDate: "",
            cashTotalHours:"",
            cashUnpaidHours:"",
            cashReceivedHours:"",
            maintenanceHours:"",
            customerTotalHoursObj:{},
            sparePartsCostObj:{},
        }
    },
    computed: {
        user: {
            get () {
                return this.$store.state.myStore.user;
            }
        },
        userInformation:{
            get () {
                return this.$store.state.myStore.userInformation;
            }
        },
    },
    watch:{
        userInformation:function () {
            this.loadData();
        },
    },

    methods: {
        initSelect: function (id, list) {
            $('#'+id).empty();
            if (list && list.length > 0) {
                let option = '';
                option += "<option value=''>" + '请选择' + '</option>';
                list.forEach(el => {
                    option += "<option value='" + el.recordId + "'>" + el.name + '</option>';
                });
                $('#'+ id).empty();
                $('#'+ id).append(option);
                $('#'+id).selectpicker('val', this.repairCompanyId);
                $('#'+ id).selectpicker('render');
                $('#'+ id).selectpicker('refresh');
                $('#'+ id).selectpicker();
                if (!this.repairCompanyId && list) {
                    this.repairCompanyId = list[0].recordId;
                }
                $("#" + id).selectpicker("val", this.repairCompanyId);
                this.loadApplyCustomList();
            }
        },
        initSelectTwo: function (id, list) {
            $('#'+id).empty();
            if (list && list.length > 0) {
                let option = '';
                option += "<option value=''>" + '请选择' + '</option>';
                list.forEach(el => {
                    option += "<option value='" + el.recordId + "'>" + el.name + '</option>';
                });
                $('#'+ id).empty();
                $('#'+ id).append(option);
                $('#'+id).selectpicker('val', this.applyCompanyId);
                $('#'+ id).selectpicker('render');
                $('#'+ id).selectpicker('refresh');
                $('#'+ id).selectpicker();
                if (!this.applyCompanyId && list) {
                    this.applyCompanyId = list[0].recordId;
                }
                $("#" + id).selectpicker("val", this.applyCompanyId);
                this.loadUserHoursPage();
            }
        },
        initSelectThree: function (id, list) {
            $('#'+id).empty();
            if (list && list.length > 0) {
                let option = '';
                option += "<option value=''>" + '请选择' + '</option>';
                list.forEach(el => {
                    option += "<option value='" + el.recordId + "'>" + el.name + '</option>';
                });
                $('#'+ id).empty();
                $('#'+ id).append(option);
                $('#'+id).selectpicker('val', this.applyCompanyId);
                $('#'+ id).selectpicker('render');
                $('#'+ id).selectpicker('refresh');
                $('#'+ id).selectpicker();
                if (!this.applyCompanyIdTwo && list) {
                    this.applyCompanyIdTwo = list[0].recordId;
                }
                $("#" + id).selectpicker("val", this.applyCompanyIdTwo);
            }
        },

        initSelectT: function (id, list) {
            $('#'+id).empty();
            if (list && list.length > 0) {
                let option = '';
                option += "<option value=''>" + '请选择' + '</option>';
                list.forEach(el => {
                    option += "<option value='" + el + "'>" + el + '</option>';
                });
                $('#'+ id).empty();
                $('#'+ id).append(option);
                $('#'+id).selectpicker('val', this.monthly);
                $('#'+ id).selectpicker('render');
                $('#'+ id).selectpicker('refresh');
                $('#'+ id).selectpicker();
                var currentDate = new Date();
                currentDate.setMonth(currentDate.getMonth());
                this.monthly = "" + currentDate.getFullYear() + (currentDate.getMonth() < 10 ? '0' + (currentDate.getMonth() + 1) : (currentDate.getMonth() + 1));
                $("#" + id).selectpicker("val", this.monthly);
            }
        },

        loadData: function () {
            const query = {};
            query.userId = this.user.userId;
            query.phone = this.user.phone;
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/getUserList",
                data: JSON.stringify(query),
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    if (data) {
                        _this.repairCompanyList = data.repairCompanyList;
                        _this.applyCompanyList = data.applyCompanyList;
                        //获取月份
                        _this.monthlyList = data.buyMonthlyList;
                        _this.initSelect('repairCompanyId', _this.repairCompanyList);
                    }
                }
            });
        },

        loadUserHoursPage:function () {
            const query = {};
            query.userId = this.user.userId;
            query.repairCompanyId = this.repairCompanyId;
            query.applyCompanyId = this.applyCompanyId;
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/loadUserHoursPage",
                data: JSON.stringify(query),
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    _this.usetimePageList = data.buyPackageList;
                    _this.cashUnpaidHours = data.cashUnpaidHours;
                    _this.cashTotalHours = data.cashTotalHours;
                    _this.cashReceivedHours = data.cashReceivedHours;
                    _this.maintenanceHours = data.maintenanceHours;
                    _this.customerTotalHoursObj = data.customerTotalHoursObj;
                    _this.sparePartsCostObj = data.sparePartsCostObj;
                    var currentDate = new Date();
                    currentDate.setMonth(currentDate.getMonth());
                    _this.currentDate = "" + currentDate.getFullYear() + (currentDate.getMonth() < 10 ? '0' + (currentDate.getMonth() + 1) : (currentDate.getMonth() + 1));
                }
            });
        },
        showUseHourPacked:function (item,num) {
            const query = {};
            query.userId = this.user.userId;
            query.repairCompanyId = this.repairCompanyId;
            query.applyCompanyId = this.applyCompanyId;
            if (this.purchasePlan === '1')
            {
                query.recordId = item.recordId;
            }else if (this.purchasePlan === '2'){
                query.monthly = item.monthly;
            }
            query.num = num;
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/showUseHourPackList",
                data: JSON.stringify(query),
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    if (data) {
                        _this.showUseHourPackList = data;
                        for (let scoreT of _this.showUseHourPackList) {
                            if (scoreT.efficiency) {
                                scoreT.checkedStarsOne = scoreT.efficiency;
                            }
                            if (scoreT.massScore) {
                                scoreT.checkedStarsTwo = scoreT.massScore;
                            }
                            if (scoreT.attitudeScore) {
                                scoreT.checkedStarsThree = scoreT.attitudeScore;
                            }
                        }

                        $('#showUseHoursDetail').modal();
                    }
                }
            });
        },
        loadApplyCustomList:function()
        {
            this.applyCompanyId = "";
            const query = {};
            query.phone = this.user.phone;
            query.repairCompanyId = this.repairCompanyId;
            //依据维保工时获取对应的客户
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/getApplyCustomList",
                data: JSON.stringify(query),
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    _this.applyCustomList = data;
                    _this.initSelectTwo('applyCompanyId', _this.applyCustomList);
                }
            });
        },

        buyHoursPackage:function (item) {
            for(let company of this.repairCompanyList)
            {
                if (this.repairCompanyId === company.recordId)
                {
                    this.repaircompanyName = company.name;
                    this.purchasePlan = company.purchasePlan;
                    break;
                }
            }
            this.buyTimePage = item;
            this.initSelectThree('applyCompanyIdTwo', this.applyCustomList);
            this.initSelectT('monthly',this.monthlyList);
            $('#showChooseApply').modal();
        },
        confirmButton:function () {
            for(let companyTwo of this.applyCompanyList)
            {
                if (this.applyCompanyId === companyTwo.recordId)
                {
                    this.applycompanyName = companyTwo.name;
                    break;
                }
            }
            this.buyTimePage.applycompanyName = this.applycompanyName;
            this.buyTimePage.repaircompanyName = this.repaircompanyName;
            this.buyTimePage.userId= this.user.userId;
            this.buyTimePage.applyCompanyId = this.applyCompanyId;
            this.buyTimePage.repairCompanyId = this.repairCompanyId;
            if (this.purchasePlan === "1"){
                //工时套餐Id
                this.buyTimePage.packageId = this.buyTimePage.recordId;
            }else if (this.purchasePlan === "2"){
                this.buyTimePage.monthly = this.monthly;
            }
            this.buyTimePage.phone = this.user.phone;
            this.buyTimePage.applyName = this.user.name;
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/repair/saveAudit",
                data: JSON.stringify(this.buyTimePage),
                contentType: "application/json",
                success: function (data) {
                    $('#loadingModal').modal('hide');
                    if (!data){
                        alert("生成审批成功！");
                    }else{
                        alert(data);
                    }
                    $('#showChooseApply').modal('hide');
                    _this.loadUserHoursPage();
                }
            });
        },
        closeWindow()
        {
            $('#showChooseApply').modal('hide');
            $('#showUseHoursDetail').modal('hide');
        },

    }
}