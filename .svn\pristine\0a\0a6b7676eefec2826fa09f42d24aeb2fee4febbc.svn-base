package com.kyb.pcberp.modules.stock.service;

import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.modules.stock.dao.MaterialAttachementsDao;
import com.kyb.pcberp.modules.stock.entity.MaterialAttachements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: fzl
 * @author: <PERSON>
 * @create: 2024/8/27
 */
@Service
@Transactional(readOnly = true)
public class MaterialAttachementsService extends CrudService<MaterialAttachementsDao, MaterialAttachements>
{
    @Autowired
    private MaterialAttachementsDao dao;

}