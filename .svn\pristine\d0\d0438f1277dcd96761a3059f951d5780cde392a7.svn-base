package com.kyb.pcberp.modules.inter.dao;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.inter.entity.ConfigCustomerAttachements;
import com.kyb.pcberp.modules.inter.entity.OrderAgreement;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface ConfigCustomerAttachementsDao
{
    List<ConfigCustomerAttachements> getAttachmentList(ConfigCustomerAttachements atta);

    void saveAttachment(ConfigCustomerAttachements atta);

    void deleteAttachment(ConfigCustomerAttachements atta);

    Integer getAttachmentCount(@Param("configCustomerId") String configCustomerId,@Param("orgFileName") String orgFileName);

    List<OrderAgreement> getOrderAgreementList();

    List<ConfigCustomerAttachements> getAttachementsList(@Param("configCustomerId") String configCustomerId);
}
