package com.kyb.pcberp.modules.hr.permission_center.dao;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.hr.permission_center.pojo.Hr_Approve;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_DepartMent;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_KybAttachments;
import com.kyb.pcberp.modules.hr.permission_center.pojo.Oa_AuditTypeSheet;
import com.kyb.pcberp.modules.sys.entity.Position;
import com.kyb.pcberp.modules.sys.entity.PositionEmployee;

import java.util.List;

@MyBatisDao
public interface Hr_ApproveDao {

    List<Hr_Approve> getAuditConfigList(Hr_Approve config);

    List<Hr_Approve> getAuditConfigDeail(Hr_Approve config);

    void delAuditConfigList(Hr_Approve config);

    void saveAuditConfig(Hr_Approve config);

    Hr_Approve getConfigDeail(Hr_Approve config);

    List<Hr_DepartMent> getOaGroup(Hr_DepartMent depart);

    List<Hr_Employee> getOaEmpList(Hr_DepartMent depart);

    Hr_KybAttachments getAttachmments();

    List<Position> getPositionList(Hr_Employee emp);

    List<PositionEmployee> getPoEmpList(Hr_Employee emp);

    List<Oa_AuditTypeSheet> getAuditTypeName();

    List<Hr_Employee> getTaskHandOver(Hr_Employee emp);

    List<Hr_Employee> getDayThingHandOver(Hr_Employee emp);

    List<Hr_Employee> getReportHandOver(Hr_Employee emp);

    List<Hr_Employee> getAuditHandOver(Hr_Employee emp);

    Integer getAuditApproveCount(Hr_Approve config);

    String getOldApproveIds(Hr_Approve config);
}
