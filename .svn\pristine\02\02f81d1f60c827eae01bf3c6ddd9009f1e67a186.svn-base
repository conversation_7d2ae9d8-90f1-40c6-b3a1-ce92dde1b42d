/**
 * 
 */
package com.kyb.pcberp.modules.production.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.modules.production.entity.CraftNoDiscard;
import com.kyb.pcberp.modules.production.dao.CraftNoDiscardDao;

/**
 * 生产型号报废表Service
 * <AUTHOR>
 * @version 2016-03-02
 */
@Service
@Transactional(readOnly = true)
public class CraftNoDiscardService extends CrudService<CraftNoDiscardDao, CraftNoDiscard> {
    
    @Autowired
    private CraftNoDiscardDao craftNoDiscardDao;
    

	public CraftNoDiscard get(String id) {
		return super.get(id);
	}
	
	public List<CraftNoDiscard> findList(CraftNoDiscard craftNoDiscard) {
		return super.findList(craftNoDiscard);
	}
	
	public Page<CraftNoDiscard> findPage(Page<CraftNoDiscard> page, CraftNoDiscard craftNoDiscard) {
		return super.findPage(page, craftNoDiscard);
	}
	
	@Transactional(readOnly = false)
	public void save(CraftNoDiscard craftNoDiscard) {
		super.save(craftNoDiscard);
	}
	
	@Transactional(readOnly = false)
	public void delete(CraftNoDiscard craftNoDiscard) {
		super.delete(craftNoDiscard);
	}
	
	public CraftNoDiscard selectCraft(CraftNoDiscard craftNoDiscard){
	    return craftNoDiscardDao.selectCount(craftNoDiscard);
	}
	
}