<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.eg.dao.CardADictDao">
    
	<sql id="cardADictColumns">
		a.recordId,
		a.companyid AS "company.recordId",
		a.processCardAId AS "cardA.recordId",
		a.dictId AS "dictValue.recordId",
		a.seqNum,
		a.activeFlag,
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.remark
	</sql>
	
	<sql id="cardADictJoins">
	</sql>
    
	<select id="get" resultType="CardADict">
		SELECT 
			<include refid="cardADictColumns"/>
		FROM eg_carda_dict_relation a
		<include refid="cardADictJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="findList" resultType="CardADict">
		SELECT 
			<include refid="cardADictColumns"/>
		FROM eg_carda_dict_relation a
		<include refid="cardADictJoins"/>
		<where>
			
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="CardADict">
		SELECT 
			<include refid="cardADictColumns"/>
		FROM eg_carda_dict_relation a
		<include refid="cardADictJoins"/>
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO eg_carda_dict_relation(
			companyId,
			processCardAId,
			dictId,
			seqNum,
			activeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			remark
		) VALUES (
			#{companyId},
			#{cardA.recordId},
			#{dictValue.recordId},
			#{seqNum},
			#{DEL_FLAG_NORMAL},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark}
		)
	</insert>
	
	<update id="update">
		UPDATE eg_carda_dict_relation SET 	
			companyId = #{companyId},
			processCardAId = #{processCardAId},
			dictId = #{dictValue.recordId},
			seqNum = #{seqNum},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			remark = #{remark}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE eg_carda_dict_relation SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>
	
</mapper>