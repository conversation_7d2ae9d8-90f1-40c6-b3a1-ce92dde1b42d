const taskDeail = {
	template:'#taskDeail',
	computed: {
		task: {
			get () {
				return this.$store.state.auditStore.task
		    }
		},
		user: {
			get () {
				return this.$store.state.myStore.user
		    }
		},
		emp: {
			get () {
				return this.$store.state.myStore.emp
		    }
		}
	},
	data(){
		return{
			comments: {},
			fileList: [],
			editStatus:true,
			localIds: [],
			clickFlag:false
		}
	},
	created(){
		this.$store.dispatch('myStore/setUserInformation')
		this.getWxMsg()
	},
	watch: {
		user: function () {
			$('#loadingModal').modal()
			this.$store.dispatch('auditStore/setTask')
	    },
	    task:function(){
	    	this.localIds = []
	    	if(this.task.createdBy && this.task.createdBy.recordId == this.emp.recordId)
	    	{
	    		this.editStatus = true
	    	}
	    	else
	    	{
	    		this.editStatus = false
	    	}
			if(this.task.attachList) {
				this.fileList = this.task.attachList
				for(var i=0;i<this.fileList.length;i++){
					  const file = this.fileList[i]
					  const type = ['image/jpg', 'image/jpeg', 'image/png']
					  const typePdf = ['application/pdf']
					  if (file && type.indexOf(file.type) >= 0) {
						  this.fileList[i].imgUrl = file.downloadUrl
						  this.localIds.push(file.downloadUrl)
					  } else if (file && typePdf.indexOf(file.type) >= 0) {
						  this.fileList[i].pdfUrl = file.downloadUrl
					  } else {
						  if (file.downloadUrl) {
							  if (file.type) {
								  this.fileList[i].preUrl = "https://view.officeapps.live.com/op/view.aspx?src=" + 
								  encodeURIComponent(file.downloadUrl)
							  } else {
								  const strs = file.name.split('.')
								  if (strs[1] === 'jpg' || strs[1] === 'JPG' || strs[1] === 'png' || strs[1] === 'PNG' || strs[1] === 'jpeg' ||
										  strs[1] === 'JPEG' || strs[1] === 'bmp' || strs[1] === 'BMP' || strs[1] === 'gif' || strs[1] === 'GIF') {
									  this.fileList[i].imgUrl = file.downloadUrl
									  this.localIds.push(file.downloadUrl)
								  } else if (strs[1] === 'pdf' || strs[1] === 'PDF') {
									  this.fileList[i].pdfUrl = file.downloadUrl
								  } else {
									  this.fileList[i].preUrl = "https://view.officeapps.live.com/op/view.aspx?src=" + 
									  encodeURIComponent(file.downloadUrl)
								  } 
							  }
						 }
					}
				}
			 }
	    	 $('#loadingModal').modal('hide')
	    }
	},
	methods:{
		commit: function(){
			if(this.clickFlag)
			{
				alert("请勿多次点击！");
				return;
			}
			if(!this.comments || !this.comments.content){
				alert("请填写评论内容");
			}
			if(!this.task || !this.task.recordId){
				alert("当前页面失效，请退出重进");
			}
			if(!this.user && !this.user.userId){
				alert("用户未登陆，请登录");
			}
			this.comments.taskId = this.task.recordId;
			this.comments.createdBy = this.emp.recordId;
			this.clickFlag = true;
			var store = this.$store;
			var _this = this;
			$.ajax({
	     		type:"post",
	     		url:ctx + "/f/wechat/kybsoftOA/addAuditComment",
	     		data:JSON.stringify(this.comments),
	     		contentType:"application/json",
	     		success:function(data)
	     		{
	     			if(data == "success"){
	     				alert('评论成功！');
	     				ths.comments = {};
	     				store.dispatch('auditStore/setTask')
	                }else{
						alert('评论失败！');
	                }
	     			$("#comment").modal('hide')
					_this.clickFlag = false;
	     		}
	     	})
		},
		replyComment:function(item){
			if(item && item.recordId){
				this.comments.commentFaId = item.recordId;
			}
			$("#comment").modal()
		},
		replyCom:function(item,commen){
			if(item && item.recordId){
				this.comments.commentFaId = item.recordId;
			}
			if(commen && commen.recordId){
				this.comments.commentId = commen.recordId;
			}
			$("#comment").modal()
		},
		close:function(item){
			if(item.commentFlag){
				this.$set(item, "commentFlag", false)
			}else{
				this.$set(item, "commentFlag", true)
			}
		},
		openSchedule: function(){
			$("#scheduleModal").modal()
		},
		updateSchedule: function(){
			var store = this.$store;
			$.ajax({
	     		type:"post",
	     		url:ctx + "/f/wechat/kybsoftOA/updateSchedule",
	     		data:JSON.stringify(this.task),
	     		contentType:"application/json",
	     		success:function(data)
	     		{
	     			if(data == "success"){
	     				alert('修改成功！');
	                }else{
						alert('修改失败！');
	                }
	     			$("#scheduleModal").modal('hide')
	     		}
	     	})
		},
	    delFile: function (index) {
	        if (this.fileList[index] && this.fileList[index].downloadUrl) {
	        	// 删除后台关联及存储
	        	_this = this
	        	$.ajax({
					type:"post",
				    url:ctx + "/f/wechat/kybsoftOA/delTaskFile",
				    data:JSON.stringify(this.fileList[index]),
				    contentType:"application/json",
				    success:function(data)
				    {
				    	if (data) {
			              if (data === 'success') {
			                alert('删除成功')
			                _this.fileList.splice(index, 1)
			              } else {
			                alert('系统错误')
			              }
			            }
				    }
		        })
	        }
	    },
	    getWxMsg:function()
		{
			var config = {}
			config.url = location.href.split('#')[0]
			var _this = this
			$.ajax({
	     		type:"post",
	     		url:ctx + "/f/wechat/produce/getWxMsg",
	     		data:JSON.stringify(config),
	     		contentType:"application/json",
	     		success:function(data)
	     		{
	     			_this.wxConfig(data)
	     		}
	     	})
		},
		wxConfig: function (data) {
			wx.config({
		        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端console.log出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
		        appId: data.appId, // 必填，公众号的唯一标识
		        timestamp: data.timestamp, // 必填，生成签名的时间戳
		        nonceStr: data.nonceStr, // 必填，生成签名的随机串
		        signature: data.signature, // 必填，签名
		        jsApiList: [
		          'checkJsApi', 'chooseImage', 'previewImage', 'uploadImage', 'downloadImage'
		        ]
			})
	    },
		preview:function(item){
			var _this = this
			wx.previewImage({
				current: item, // 当前显示图片的http链接
				urls: _this.localIds // 需要预览的图片http链接列表
			});
		}
	}
}