package com.kyb.pcberp.modules.contract.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.modules.production.entity.ProduceRecord;
import com.kyb.pcberp.modules.purch.entity.Purchasing;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class OrderMsg extends DataEntity<OrderMsg>
{
    private static final long serialVersionUID = 1L;

    private String contractNo;

    private String customerPo;

    private String craftNo;

    private String customerModel;

    private String quantity;

    private BigDecimal orderDeailArea;

    private String subTotal;

    private String price;

    private Date factoryDate;

    private String notifiNo;

    private String cardStatus;

    private String contractDeailStatus;

    private String feedStatus;

    private String acDistributeDate;

    private String cardId;

    private String notifiId;

    private String notifiOrgId;

    private String feedingId;

    private String oldDeailId;

    private String urgentFlag;

    private List<Purchasing> purchasingList;

    private List<PurchasingDetail> purchasingDetailList;

    private List<ProduceRecord> produceRecordList;

    private Double hours;

    private String orderStatus;

    private String progressFlag; // 1：订单未审核 2：订单审批中 3：工程资料 4：材料确认 5：生产投料 6：投料出库 7：生产工序 8：送货出库

    private String deliveryStatus; // 1：正常 2：有风险 3：已逾期 4：加急

    private BigDecimal setLength;

    private BigDecimal setWidth;

    private BigDecimal pnlDivisor;

    private String progressName;

    private String departId;

    private String workStatus;

    private String workType;

    private String custName;

    private String custPo;

    private String custCraft;

    private String companyId;

    private String custNo;

    private String custShortName;

    private String orderType;

    private String materialType;

    private String orderTypeVal;

    private String deptName;

    public String getContractNo()
    {
        return contractNo;
    }

    public void setContractNo(String contractNo)
    {
        this.contractNo = contractNo;
    }

    public String getCustomerPo()
    {
        return customerPo;
    }

    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }

    public String getCraftNo()
    {
        return craftNo;
    }

    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }

    public String getCustomerModel()
    {
        return customerModel;
    }

    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }

    public String getQuantity()
    {
        return quantity;
    }

    public void setQuantity(String quantity)
    {
        this.quantity = quantity;
    }

    public BigDecimal getOrderDeailArea()
    {
        return orderDeailArea;
    }

    public void setOrderDeailArea(BigDecimal orderDeailArea)
    {
        this.orderDeailArea = orderDeailArea;
    }

    public String getSubTotal()
    {
        return subTotal;
    }

    public void setSubTotal(String subTotal)
    {
        this.subTotal = subTotal;
    }

    public String getPrice()
    {
        return price;
    }

    public void setPrice(String price)
    {
        this.price = price;
    }

    public Date getFactoryDate()
    {
        return factoryDate;
    }

    public String getFactoryDateStr()
    {
        if (factoryDate != null)
        {
            return DateUtils.formatDate(factoryDate);
        }
        return "";
    }

    public String getCreatedDateStr()
    {
        if (createdDate != null)
        {
            return DateUtils.formatDate(createdDate);
        }
        return "";
    }

    public void setFactoryDate(Date factoryDate)
    {
        this.factoryDate = factoryDate;
    }

    public String getNotifiNo()
    {
        return notifiNo;
    }

    public void setNotifiNo(String notifiNo)
    {
        this.notifiNo = notifiNo;
    }

    public String getCardStatus()
    {
        return cardStatus;
    }

    public void setCardStatus(String cardStatus)
    {
        this.cardStatus = cardStatus;
    }

    public String getContractDeailStatus()
    {
        return contractDeailStatus;
    }

    public void setContractDeailStatus(String contractDeailStatus)
    {
        this.contractDeailStatus = contractDeailStatus;
    }

    public String getFeedStatus()
    {
        return feedStatus;
    }

    public void setFeedStatus(String feedStatus)
    {
        this.feedStatus = feedStatus;
    }

    public String getAcDistributeDate()
    {
        return acDistributeDate;
    }

    public void setAcDistributeDate(String acDistributeDate)
    {
        this.acDistributeDate = acDistributeDate;
    }

    public String getCardId()
    {
        return cardId;
    }

    public void setCardId(String cardId)
    {
        this.cardId = cardId;
    }

    public String getNotifiId()
    {
        return notifiId;
    }

    public void setNotifiId(String notifiId)
    {
        this.notifiId = notifiId;
    }

    public String getNotifiOrgId()
    {
        return notifiOrgId;
    }

    public void setNotifiOrgId(String notifiOrgId)
    {
        this.notifiOrgId = notifiOrgId;
    }

    public String getFeedingId()
    {
        return feedingId;
    }

    public void setFeedingId(String feedingId)
    {
        this.feedingId = feedingId;
    }

    public String getOldDeailId()
    {
        return oldDeailId;
    }

    public void setOldDeailId(String oldDeailId)
    {
        this.oldDeailId = oldDeailId;
    }

    public String getUrgentFlag()
    {
        return urgentFlag;
    }

    public void setUrgentFlag(String urgentFlag)
    {
        this.urgentFlag = urgentFlag;
    }

    public List<Purchasing> getPurchasingList()
    {
        return purchasingList;
    }

    public void setPurchasingList(List<Purchasing> purchasingList)
    {
        this.purchasingList = purchasingList;
    }

    public List<PurchasingDetail> getPurchasingDetailList()
    {
        return purchasingDetailList;
    }

    public void setPurchasingDetailList(List<PurchasingDetail> purchasingDetailList)
    {
        this.purchasingDetailList = purchasingDetailList;
    }

    public List<ProduceRecord> getProduceRecordList()
    {
        return produceRecordList;
    }

    public void setProduceRecordList(List<ProduceRecord> produceRecordList)
    {
        this.produceRecordList = produceRecordList;
    }

    public Double getHours()
    {
        return hours;
    }

    public void setHours(Double hours)
    {
        this.hours = hours;
    }

    public String getOrderStatus()
    {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus)
    {
        this.orderStatus = orderStatus;
    }

    public String getProgressFlag()
    {
        return progressFlag;
    }

    public void setProgressFlag(String progressFlag)
    {
        this.progressFlag = progressFlag;
    }

    public String getDeliveryStatus()
    {
        return deliveryStatus;
    }

    public void setDeliveryStatus(String deliveryStatus)
    {
        this.deliveryStatus = deliveryStatus;
    }

    public BigDecimal getSetLength()
    {
        return setLength;
    }

    public void setSetLength(BigDecimal setLength)
    {
        this.setLength = setLength;
    }

    public BigDecimal getSetWidth()
    {
        return setWidth;
    }

    public void setSetWidth(BigDecimal setWidth)
    {
        this.setWidth = setWidth;
    }

    public BigDecimal getPnlDivisor()
    {
        return pnlDivisor;
    }

    public void setPnlDivisor(BigDecimal pnlDivisor)
    {
        this.pnlDivisor = pnlDivisor;
    }

    public String getProgressName()
    {
        return progressName;
    }

    public void setProgressName(String progressName)
    {
        this.progressName = progressName;
    }

    public String getDepartId()
    {
        return departId;
    }

    public void setDepartId(String departId)
    {
        this.departId = departId;
    }

    public String getWorkStatus()
    {
        return workStatus;
    }

    public void setWorkStatus(String workStatus)
    {
        this.workStatus = workStatus;
    }

    public String getWorkType()
    {
        return workType;
    }

    public void setWorkType(String workType)
    {
        this.workType = workType;
    }

    public String getCustName()
    {
        return custName;
    }

    public void setCustName(String custName)
    {
        this.custName = custName;
    }

    public String getCustPo()
    {
        return custPo;
    }

    public void setCustPo(String custPo)
    {
        this.custPo = custPo;
    }

    public String getCustCraft()
    {
        return custCraft;
    }

    public void setCustCraft(String custCraft)
    {
        this.custCraft = custCraft;
    }

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    public String getCustNo()
    {
        return custNo;
    }

    public void setCustNo(String custNo)
    {
        this.custNo = custNo;
    }

    public String getCustShortName()
    {
        return custShortName;
    }

    public void setCustShortName(String custShortName)
    {
        this.custShortName = custShortName;
    }

    public String getOrderType()
    {
        return orderType;
    }

    public void setOrderType(String orderType)
    {
        this.orderType = orderType;
    }

    public String getMaterialType()
    {
        return materialType;
    }

    public void setMaterialType(String materialType)
    {
        this.materialType = materialType;
    }

    public String getOrderTypeVal()
    {
        return orderTypeVal;
    }

    public void setOrderTypeVal(String orderTypeVal)
    {
        this.orderTypeVal = orderTypeVal;
    }

    public String getDeptName()
    {
        return deptName;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }
}
