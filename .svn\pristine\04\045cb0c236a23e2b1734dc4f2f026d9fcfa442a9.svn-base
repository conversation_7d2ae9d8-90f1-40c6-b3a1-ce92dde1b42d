package com.kyb.pcberp.modules.wechat.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.modules.report.entity.ProTime;

import java.util.Date;
import java.util.List;
import java.util.Map;

public class ProduceReport extends DataEntity<ProduceReport>
{
    private static final long serialVersionUID = 1L;
    
    private String batchArea;
    
    private String takeMan;
    
    private String handMan;
    
    private Date takeOverTime;
    
    private Date handOverTime;
    
    private String category;
    
    private Integer hours;
    
    private Date startTime;
    
    private Date endTime;
    
    private String companyId;
    
    private String userIds;
    
    private String currUserId;
    
    private String reportFlag;
    
    private String processIds;
    
    private Integer diffHours;
    
    private List<ProTime> list;
    
    private Integer madeHours;
    
    private String craftNo;
    
    private String batchId;
    
    private String lastCategory;
    
    private Date lastHandTime;
    
    private String status;
    
    private Date estimateDate;
    
    private Date finalDeliveryDate;
    
    private Integer notiNeedDays;
    
    private Date acDistributeDate;
    
    private String custNo;
    
    private String shortName;
    
    private String customerModel;
    
    private String customerPo;
    
    private String queryFlag;
    
    private String deliveryFlag; // 1:未开工延期，2：交货延期，3：在制延期，4：未开工正常，5：交货正常，6：在制正常
    
    private Integer alNeedDays;
    
    private String batchStatus;
    
    private Date deliveryDate;
    
    private String notifiNo;
    
    private String oueDay;
    
    private Integer logisticsTime;
    
    private Integer purchRowDays;
    
    private String purchOueDay;
    
    private Date finalStockTime;
    
    private String productDays;
    
    private String productDeail;

    private String ProcessName;

    private String madeStatus;

    private Integer xIndex;

    private String earyFlag; // 提前生产

    private String purFlag; // 采购延期

    private String produceFlag; // 生产延期

    private String stockFlag; // 到期未送货

    private String orderTypeValue;

    private String productBatchNo;

    private Date orderApprovalTime; // 订单审批提交时间

    private Date orderApprovalFinshTime; // 订单审批完成时间

    private Date askGuestTime; // 问客时间

    private Date askGuestReplyTime; // 问客回复时间

    private Date cardAuditDate; // 工程确认时间

    private Integer realNeedDays; // 实际生产天数

    private Integer moreDays; // 超期天数

    private String saleFlag; // 市场延期

    private String cardFlag; // 工程延期

    private String name;

    private String exportData;

    private String dictOrderType;

    private String orderType;

    public String getBatchArea()
    {
        return batchArea;
    }
    
    public void setBatchArea(String batchArea)
    {
        this.batchArea = batchArea;
    }
    
    public String getTakeMan()
    {
        return takeMan;
    }
    
    public void setTakeMan(String takeMan)
    {
        this.takeMan = takeMan;
    }
    
    public String getHandMan()
    {
        return handMan;
    }
    
    public void setHandMan(String handMan)
    {
        this.handMan = handMan;
    }
    
    public Date getTakeOverTime()
    {
        return takeOverTime;
    }
    
    public String getTakeOverTimeStr()
    {
        if (takeOverTime != null)
        {
            return DateUtils.formatDateTime(takeOverTime);
        }
        return null;
    }
    
    public void setTakeOverTime(Date takeOverTime)
    {
        this.takeOverTime = takeOverTime;
    }
    
    public Date getHandOverTime()
    {
        return handOverTime;
    }
    
    public void setHandOverTime(Date handOverTime)
    {
        this.handOverTime = handOverTime;
    }
    
    public String getCategory()
    {
        return category;
    }
    
    public void setCategory(String category)
    {
        this.category = category;
    }
    
    public Integer getHours()
    {
        return hours;
    }
    
    public void setHours(Integer hours)
    {
        this.hours = hours;
    }
    
    public Date getStartTime()
    {
        return startTime;
    }
    
    public void setStartTime(Date startTime)
    {
        this.startTime = startTime;
    }
    
    public Date getEndTime()
    {
        return endTime;
    }
    
    public void setEndTime(Date endTime)
    {
        this.endTime = endTime;
    }
    
    public String getCompanyId()
    {
        return companyId;
    }
    
    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }
    
    public String getUserIds()
    {
        return userIds;
    }
    
    public void setUserIds(String userIds)
    {
        this.userIds = userIds;
    }
    
    public String getCurrUserId()
    {
        return currUserId;
    }
    
    public void setCurrUserId(String currUserId)
    {
        this.currUserId = currUserId;
    }
    
    public String getReportFlag()
    {
        return reportFlag;
    }
    
    public void setReportFlag(String reportFlag)
    {
        this.reportFlag = reportFlag;
    }
    
    public String getProcessIds()
    {
        return processIds;
    }
    
    public void setProcessIds(String processIds)
    {
        this.processIds = processIds;
    }
    
    public Integer getDiffHours()
    {
        return diffHours;
    }
    
    public void setDiffHours(Integer diffHours)
    {
        this.diffHours = diffHours;
    }
    
    public List<ProTime> getList()
    {
        return list;
    }
    
    public void setList(List<ProTime> list)
    {
        this.list = list;
    }
    
    public Integer getMadeHours()
    {
        return madeHours;
    }
    
    public String getMadeHoursStr()
    {
        // 当前时间减去接板时间
        if (takeOverTime != null)
        {
            return DateUtils.getTimeStr(takeOverTime, new Date());
        }
        return null;
    }
    
    public void setMadeHours(Integer madeHours)
    {
        this.madeHours = madeHours;
    }
    
    public String getCraftNo()
    {
        return craftNo;
    }
    
    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }
    
    public String getBatchId()
    {
        return batchId;
    }
    
    public void setBatchId(String batchId)
    {
        this.batchId = batchId;
    }
    
    public String getLastCategory()
    {
        return lastCategory;
    }
    
    public void setLastCategory(String lastCategory)
    {
        this.lastCategory = lastCategory;
    }
    
    public Date getLastHandTime()
    {
        return lastHandTime;
    }
    
    public String getLastHandTimeStr()
    {
        if (lastHandTime != null)
        {
            return DateUtils.formatDateTime(lastHandTime);
        }
        return null;
    }
    
    public void setLastHandTime(Date lastHandTime)
    {
        this.lastHandTime = lastHandTime;
    }
    
    public Integer getWaitHours()
    {
        diffHours = diffHours == null ? 0 : diffHours;
        madeHours = madeHours == null ? 0 : madeHours;
        return diffHours - madeHours;
    }
    
    public String getWaitHoursStr()
    {
        // 当前时间减去接板时间
        if (lastHandTime != null)
        {
            if (takeOverTime != null)
            {
                return DateUtils.getTimeStr(lastHandTime, takeOverTime);
            }
            else
            {
                return DateUtils.getTimeStr(lastHandTime, new Date());
            }
        }
        return null;
    }
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public Date getEstimateDate()
    {
        return estimateDate;
    }
    
    public String getEstimateDateStr()
    {
        if (estimateDate != null)
        {
            return DateUtils.formatDate(estimateDate);
        }
        return null;
    }
    
    public void setEstimateDate(Date estimateDate)
    {
        this.estimateDate = estimateDate;
    }
    
    public Date getFinalDeliveryDate()
    {
        return finalDeliveryDate;
    }
    
    public String getFinalDeliveryDateStr()
    {
        if (finalDeliveryDate != null)
        {
            return DateUtils.formatDate(finalDeliveryDate);
        }
        return null;
    }
    
    public void setFinalDeliveryDate(Date finalDeliveryDate)
    {
        this.finalDeliveryDate = finalDeliveryDate;
    }
    
    public Integer getNotiNeedDays()
    {
        return notiNeedDays;
    }
    
    public void setNotiNeedDays(Integer notiNeedDays)
    {
        this.notiNeedDays = notiNeedDays;
    }
    
    public Date getAcDistributeDate()
    {
        return acDistributeDate;
    }
    
    public String getAcDistributeDateStr()
    {
        if (acDistributeDate != null)
        {
            return DateUtils.formatDate(acDistributeDate);
        }
        return null;
    }
    
    public void setAcDistributeDate(Date acDistributeDate)
    {
        this.acDistributeDate = acDistributeDate;
    }
    
    public String getCustNo()
    {
        return custNo;
    }
    
    public void setCustNo(String custNo)
    {
        this.custNo = custNo;
    }
    
    public String getShortName()
    {
        return shortName;
    }
    
    public void setShortName(String shortName)
    {
        this.shortName = shortName;
    }
    
    public String getCustomerModel()
    {
        return customerModel;
    }
    
    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }
    
    public String getCustomerPo()
    {
        return customerPo;
    }
    
    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }
    
    public String getQueryFlag()
    {
        return queryFlag;
    }
    
    public void setQueryFlag(String queryFlag)
    {
        this.queryFlag = queryFlag;
    }
    
    public String getDeliveryFlag()
    {
        return deliveryFlag;
    }
    
    public void setDeliveryFlag(String deliveryFlag)
    {
        this.deliveryFlag = deliveryFlag;
    }
    
    public Integer getAlNeedDays()
    {
        return alNeedDays;
    }
    
    public void setAlNeedDays(Integer alNeedDays)
    {
        this.alNeedDays = alNeedDays;
    }
    
    public String getBatchStatus()
    {
        return batchStatus;
    }
    
    public void setBatchStatus(String batchStatus)
    {
        this.batchStatus = batchStatus;
    }
    
    public Date getDeliveryDate()
    {
        return deliveryDate;
    }
    
    public String getDeliveryDateStr()
    {
        if (deliveryDate != null)
        {
            return DateUtils.formatDate(deliveryDate);
        }
        return null;
    }
    
    public void setDeliveryDate(Date deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }
    
    public String getOueDayFlag()
    {
        if (deliveryDate != null)
        {
            Date now = new Date();
            if (deliveryDate.getTime() < now.getTime())
            {
                return "1";
            }
        }
        return "0";
    }
    
    public String getOueDay()
    {
        return oueDay;
    }
    
    public void setOueDay(String oueDay)
    {
        this.oueDay = oueDay;
    }
    
    public String getNotifiNo()
    {
        return notifiNo;
    }
    
    public void setNotifiNo(String notifiNo)
    {
        this.notifiNo = notifiNo;
    }
    
    public Integer getLogisticsTime()
    {
        return logisticsTime;
    }
    
    public void setLogisticsTime(Integer logisticsTime)
    {
        this.logisticsTime = logisticsTime;
    }
    
    public Integer getPurchRowDays()
    {
        return purchRowDays;
    }
    
    public void setPurchRowDays(Integer purchRowDays)
    {
        this.purchRowDays = purchRowDays;
    }
    
    public String getEndPurchDateTwoStr()
    {
        String result = null;
        if (estimateDate != null && purchRowDays != null)
        {
            Date endPurchDate = estimateDate;
            if (notiNeedDays != null)
            {
                endPurchDate = DateUtils.date_add(endPurchDate, -notiNeedDays);
            }
            // 减去物流时间
            if (logisticsTime != null)
            {
                endPurchDate = DateUtils.date_add(endPurchDate, -logisticsTime);
            }
            if (estimateDate != null)
            {
                result = DateUtils.formatDate(endPurchDate, "yyyy-MM-dd");
            }
        }
        return result;
    }
    
    public Date getEndPurchDateTwo()
    {
        if (estimateDate != null && purchRowDays != null)
        {
            Date endPurchDate = estimateDate;
            if (notiNeedDays != null)
            {
                endPurchDate = DateUtils.date_add(endPurchDate, -notiNeedDays);
            }
            // 减去物流时间
            if (logisticsTime != null)
            {
                endPurchDate = DateUtils.date_add(endPurchDate, -logisticsTime);
            }
            return endPurchDate;
        }
        return null;
    }
    
    public String getPurchOueDay()
    {
        return purchOueDay;
    }
    
    public void setPurchOueDay(String purchOueDay)
    {
        this.purchOueDay = purchOueDay;
    }
    
    public Date getFinalStockTime()
    {
        return finalStockTime;
    }
    
    public String getFinalStockTimeStr()
    {
        if (finalStockTime != null)
        {
            return DateUtils.formatDate(finalStockTime);
        }
        return null;
    }
    
    public void setFinalStockTime(Date finalStockTime)
    {
        this.finalStockTime = finalStockTime;
    }
    
    public String getProductDays()
    {
        return productDays;
    }
    
    public void setProductDays(String productDays)
    {
        this.productDays = productDays;
    }
    
    public String getProductDeail()
    {
        return productDeail;
    }
    
    public void setProductDeail(String productDeail)
    {
        this.productDeail = productDeail;
    }

    public String getProcessName() {
        return ProcessName;
    }

    public void setProcessName(String processName) {
        ProcessName = processName;
    }

    public String getMadeStatus() {
        return madeStatus;
    }

    public void setMadeStatus(String madeStatus) {
        this.madeStatus = madeStatus;
    }

    public Integer getxIndex() {
        return xIndex;
    }

    public void setxIndex(Integer xIndex) {
        this.xIndex = xIndex;
    }

    public String getEaryFlag() {
        return earyFlag;
    }

    public void setEaryFlag(String earyFlag) {
        this.earyFlag = earyFlag;
    }

    public String getPurFlag() {
        return purFlag;
    }

    public void setPurFlag(String purFlag) {
        this.purFlag = purFlag;
    }

    public String getProduceFlag() {
        return produceFlag;
    }

    public void setProduceFlag(String produceFlag) {
        this.produceFlag = produceFlag;
    }

    public String getStockFlag() {
        return stockFlag;
    }

    public void setStockFlag(String stockFlag) {
        this.stockFlag = stockFlag;
    }

    public String getOrderTypeValue() {
        return orderTypeValue;
    }

    public void setOrderTypeValue(String orderTypeValue) {
        this.orderTypeValue = orderTypeValue;
    }

    public String getProductBatchNo() {
        return productBatchNo;
    }

    public void setProductBatchNo(String productBatchNo) {
        this.productBatchNo = productBatchNo;
    }

    public Date getOrderApprovalTime()
    {
        return orderApprovalTime;
    }

    public void setOrderApprovalTime(Date orderApprovalTime)
    {
        this.orderApprovalTime = orderApprovalTime;
    }

    public Date getOrderApprovalFinshTime()
    {
        return orderApprovalFinshTime;
    }

    public void setOrderApprovalFinshTime(Date orderApprovalFinshTime)
    {
        this.orderApprovalFinshTime = orderApprovalFinshTime;
    }

    public Date getAskGuestTime()
    {
        return askGuestTime;
    }

    public void setAskGuestTime(Date askGuestTime)
    {
        this.askGuestTime = askGuestTime;
    }

    public Date getAskGuestReplyTime()
    {
        return askGuestReplyTime;
    }

    public void setAskGuestReplyTime(Date askGuestReplyTime)
    {
        this.askGuestReplyTime = askGuestReplyTime;
    }

    public Date getCardAuditDate()
    {
        return cardAuditDate;
    }

    public void setCardAuditDate(Date cardAuditDate)
    {
        this.cardAuditDate = cardAuditDate;
    }

    public String getOrderApprovalTimeStr()
    {
        if (orderApprovalTime != null)
        {
            return DateUtils.formatDate(orderApprovalTime,"yyyy-MM-dd HH:mm:ss");
        }
        return null;
    }

    public String getOrderApprovalFinshTimeStr()
    {
        if (orderApprovalFinshTime != null)
        {
            return DateUtils.formatDate(orderApprovalFinshTime,"yyyy-MM-dd HH:mm:ss");
        }
        return null;
    }

    public String getAskGuestTimeStr()
    {
        if (askGuestTime != null)
        {
            return DateUtils.formatDate(askGuestTime,"yyyy-MM-dd HH:mm:ss");
        }
        return null;
    }

    public String getAskGuestReplyTimeStr()
    {
        if (askGuestReplyTime != null)
        {
            return DateUtils.formatDate(askGuestReplyTime,"yyyy-MM-dd HH:mm:ss");
        }
        return null;
    }

    public String getCardAuditDateStr()
    {
        if (cardAuditDate != null)
        {
            return DateUtils.formatDate(cardAuditDate,"yyyy-MM-dd HH:mm:ss");
        }
        return null;
    }

    public Integer getRealNeedDays()
    {
        return realNeedDays;
    }

    public void setRealNeedDays(Integer realNeedDays)
    {
        this.realNeedDays = realNeedDays;
    }

    public Integer getMoreDays()
    {
        return moreDays;
    }

    public void setMoreDays(Integer moreDays)
    {
        this.moreDays = moreDays;
    }

    public String getSaleFlag()
    {
        return saleFlag;
    }

    public void setSaleFlag(String saleFlag)
    {
        this.saleFlag = saleFlag;
    }

    public String getCardFlag()
    {
        return cardFlag;
    }

    public void setCardFlag(String cardFlag)
    {
        this.cardFlag = cardFlag;
    }

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getExportData()
    {
        return exportData;
    }

    public void setExportData(String exportData)
    {
        this.exportData = exportData;
    }

    public String getDictOrderType() {
        return dictOrderType;
    }

    public void setDictOrderType(String dictOrderType) {
        this.dictOrderType = dictOrderType;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }
}
