<%@ page contentType="text/html;charset=UTF-8" %>
<tab heading="业务员设置" active="ctrl.tabs.cusSalesManForm.active" ng-click="ctrl.cusSalesQuery()">
        <div class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">客户编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="ctrl.cusNoQuery"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">客户名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="ctrl.cusNameQuery"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">业务员名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="ctrl.salesNameQuery" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">业务员名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="ctrl.custTypeQuery">
                                        <option value="">所有</option>
                                        <option value="1">公司分配</option>
                                        <option value="2">自主研发</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">部门：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select  ng-model="ctrl.deptIdQueryTwo" theme="bootstrap">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in ctrl.deptListCopy | filter: $select.search">
                                            <div ng-bind-html="item.no | highlight: $select.search"></div>
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">创建时间：</label>
                                <div class="col-sm-7 col-md-8">
                                    <div class="input-prepend input-group">
                                        <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                        <input type="text" class="form-control" disable-auto-validate="true"
                                               ng-blur="ctrl.initDateTwo(ctrl.timeTwo)"
                                               kyb-daterange
                                               kyb-daterange-options="ctrl.rangeOptionsTwo"
                                               ng-model="ctrl.timeTwo"
                                               placeholder="请选择时间段">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right"
                                    ng-click="ctrl.cusSalesQuery()">
                                <i class="fa fa-search"></i> 查&nbsp;询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">客户业务员列表</div>
                <div class="actions">
                    <div class="portlet-input input-inline">
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.selectAll()" ng-if="ctrl.right.edit">
                            <i class="fa fa-check font-blue"></i>&nbsp;全选</a>&nbsp;
                    </div>
                    <div class="portlet-input input-inline">
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.cancelSelectAll()" ng-if="ctrl.right.edit">
                            <i class="fa fa-times font-red"></i>&nbsp;取消全选</a>&nbsp;
                    </div>
                    <div class="portlet-input input-inline">
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.saveOpen(1)" ng-if="ctrl.right.edit"><i class="fa fa-plus-square-o font-blue"></i>&nbsp;添加</a>&nbsp;
                    </div>
                    <div class="portlet-input input-inline">
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.saveOpen(2)" ng-if="ctrl.right.edit"><i class="fa fa-adjust font-blue"></i>编辑</a>&nbsp;
                    </div>
                    <div class="portlet-input input-inline">
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.deleteOpen()" ng-if="ctrl.right.edit"><i class="fa fa-times font-red"></i>删除</a>&nbsp;
                    </div>
                    <div class="portlet-input input-inline">
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.commitAuditOpen()" ng-if="ctrl.right.edit"><i class="fa fa-check-square-o font-green"></i>批量审批</a>&nbsp;
                    </div>
                    <div class="portlet-input input-inline">
                        <form action="a/crm/customer/exportCusSalesMan" method="POST" enctype="multipart/form-data" target="hidden_frame">
                            <input type="text" ng-show="false" name="customer.no" value="{{ctrl.cusNoQuery}}" />
                            <input type="text" ng-show="false" name="customer.shortName" value="{{ctrl.cusNameQuery}}" />
                            <input type="text" ng-show="false" name="user.userName" value="{{ctrl.salesNameQuery}}" />
                            <input type="text" ng-show="false" name="custType" value="{{ctrl.custType}}" />
                            <input type="text" ng-show="false" name="sentTimeStartQr" value="{{ctrl.sentTimeStartQrValue}}"/>
                            <input type="text" ng-show="false" name="sentTimeEndQr" value="{{ctrl.sentTimeEndQrValue}}"/>
                            <input type="text" ng-show="false" name="queryAll" value="{{ctrl.queryAll}}"/>
                            <div ng-if="ctrl.right.view" >
                                <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出</button>
                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                            </div>
                        </form>
<%--                        <form action="a/crm/customer/exportCusSalesMan" method="POST" enctype="multipart/form-data" target="hidden_frame">
                            <input type="text" ng-show="false" name="customer.no" value="{{ctrl.cusNoQuery}}" />
                            <input type="text" ng-show="false" name="customer.shortName" value="{{ctrl.cusNameQuery}}" />
                            <input type="text" ng-show="false" name="user.userName" value="{{ctrl.salesNameQuery}}" />
                            <input type="text" ng-show="false" name="custType" value="{{ctrl.custType}}" />
                            <input type="text" ng-show="false" name="sentTimeStartQr" value="{{ctrl.sentTimeStartQrValue}}"/>
                            <input type="text" ng-show="false" name="sentTimeEndQr" value="{{ctrl.sentTimeEndQrValue}}"/>
                            <input type="text" ng-show="false" name="queryAll" value="{{ctrl.queryAll}}"/>
                            <a type="submit" href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="ctrl.right.edit"><i class="fa fa-download"></i> 导出</a>&nbsp;
                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
                        </form>--%>
                    </div>
                </div>
            </div>
            <div class="portlet-body">
                <div class="table-scrollable" style="margin-top:0px !important">
                    <table id="contentTable" class="table table-striped table-bordered table-condensed table-advance table-hover">
                        <thead>
                        <tr class="heading">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <th width="8%">客户编号</th>
                            <th width="15%">客户名称</th>
                            <th width="8%">客户类型</th>
                            <th width="8%">业务员名称</th>
                            <th width="8%">开始日期</th>
                            <th width="8%">结束日期</th>
                            <th width="8%">创建人</th>
                            <th width="8%">创建时间</th>
                            <th width="8%">归属部门</th>
                            <th width="8%">状态</th>
                            <th width="20%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in ctrl.cusSalesManPage.data.list" ng-class="row.checked?'success':''" ng-click="ctrl.changeCheked(row,1)">
                            <td ng-bind="row.customer.no"></td>
                            <td ng-bind="row.customer.shortName"></td>
                            <td ng-bind="row.custTypeStr"></td>
                            <td ng-bind="row.user.userName"></td>
                            <td ng-bind="row.startDateStr"></td>
                            <td ng-bind="row.endDateStr"></td>
                            <td ng-bind="row.createdBy.userName"></td>
                            <td ng-bind="row.createdDate"></td>
                            <td ng-bind="row.deptName"></td>
                            <td ng-if="row.status === '1001'">
                                <span class="label label-sm label-default">未确认</span>
                            </td>
                            <td ng-if="row.status === '1002'">
                                <span class="label label-sm label-success">已确认 </span>
                            </td>
                            <td ng-if="!row.status"></td>
                            <td ng-if="row.status === '60001'">
                                <span class="label label-sm label-danger">审批中</span>
                            </td>
                            <td>
                                <a href="javascript:void(0);" ng-if="ctrl.right.edit" class="btn btn-xs btn-default" ng-click="ctrl.orderSalesManInitOpen(row)">
                                    <i class="fa fa-check font-green"></i>同步订单数据
                                </a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row page-margin-top">
                    <div  class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select  class="form-control inline table-pageSize-width"
                                 ng-model="ctrl.cusSalesManPage.pageSize"
                                 ng-change="ctrl.cusSalesManChange()"
                                 ng-options="pageSizeOption for pageSizeOption in ctrl.cusSalesManPage.pageSizeOptions"
                                 disable-auto-validate="true">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{ctrl.cusSalesManPage.data.startCount}} / {{ctrl.cusSalesManPage.data.endCount}} 条，共 {{ctrl.cusSalesManPage.data.count}} 条</span>
                    </div>
                    <div  class="col-md-12 col-lg-6">
                        <paging class="pull-right" page="ctrl.cusSalesManPage.data.pageNo" page-size="ctrl.cusSalesManPage.data.pageSize"
                                total="ctrl.cusSalesManPage.data.count" adjacent="1" dots="..." scroll-top="false" hide-if-empty="false"
                                ul-class="pagination" active-class="active" disabled-class="disabled"
                                show-prev-next="true" paging-action="ctrl.docusSalesManPage(page, pageSize, total)">
                        </paging>
                    </div>
                </div>
            </div>
        </div>
    </tab>
</tab>