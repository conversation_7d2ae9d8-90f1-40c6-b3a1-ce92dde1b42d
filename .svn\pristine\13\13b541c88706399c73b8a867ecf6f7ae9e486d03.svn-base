const myState = {
	userInformation: [],
	user: {},
	userErp: {},
	userDeail: {},
	contractList: [],
	yearList: [],
	showSel: true,
	year: "",
	month: "",
	week: "",
	emp: {},
	phone: "",
	loginStatus: "",
	maintenanceFlag:false
}
const myMutations = {
	setUserInformation (state, args) {
		state.loginStatus = "";
		$.ajax({
     		type:"post",
     		url:ctx + "/f/wechat/kybsoft/setUserInformation",
     		contentType:"application/json",
     		success:function(data)
     		{ 
     			var empGroupId = window.localStorage.getItem('empGroupId')
     			if(data){
     				state.loginStatus = "success";
     				state.userInformation = data
     				var flag = 0
         			for(var i=0;i<data.length;i++){
         				if(data[i].status == 1 && flag==0){
         					flag = 1
         					state.userErp = data[i]
         				}
         				if(data[i].status == 2){
         					state.user = data[i]
         					state.phone = data[i].phone
         				}
         				if(data[i].status == 3){
         					if(empGroupId){
         						if(data[i].company 
         								&& data[i].company.recordId == empGroupId){
         							state.emp = data[i]
         							break
         						}
         					}else{
         						state.emp = data[i]
             					break
         					}
         				}
         			}
     			}else{
     				state.loginStatus = "fail";
     			}
     		}
     	})
	},
	logOut (state, args) {
		$.ajax({
     		type:"post",
     		url:ctx + "/f/wechat/kybsoft/logOut",
     		contentType:"application/json",
     		success:function(data)
     		{ 
     			state.userInformation = []
     			state.user = {}
     		}
     	})
	},
	setUserDeail (state, args) {
		$.ajax({
     		type:"post",
     		url:ctx + "/f/wechat/kybsoft/setUserDeail",
     		contentType:"application/json",
     		success:function(data)
     		{ 
     			state.userDeail = data
     		}
     	})
	},
	getContractList (state, args) {
		$.ajax({
     		type:"post",
     		url:ctx + "/f/wechat/kybsoftOA/",
     		data:JSON.stringify(args),
     		contentType:"application/json",
     		success:function(data)
     		{ 
     			if(data && data.length > 0){
     				var reply = [];
     				for(var i=0; i<data.length;i++){
     					data[i].phone = 'tel:' + data[i].phone;
     					if(reply.indexOf(data[i].empChar) == -1){
     						reply.push(data[i].empChar);
     					}else{
     						data[i].empChar = '';
     					}
     					if(data[i].sex && data[i].sex == 1)
     					{
     						data[i].sexClass = 'icon-user text-primary';
     					}else{
     						data[i].sexClass = 'icon-user text-danger';
     					}
     				}
     			}
     			state.contractList = data;
     		}
     	})
	},
	setYearList (state, args) {
		var list = [];
		var date = new Date();
		var y = date.getFullYear();
		state.year = y;
		for(var i = 0; i <= 10; i++){
			var year = {};
			year.id = y - i;
			year.name = y - i + "年";
			// 一年中的月
		    var months = [];
		    for(var k = 0; k < 12; k++){
		    	var mon = {};
		    	mon.id = k + 1;
		    	mon.name = mon.id + "月";
		    	if(mon.id < 10){
		    		mon.queryDate = year.id + "-0" + mon.id + '-01'
		    	}else{
		    		mon.queryDate = year.id + '-' + mon.id + '-01'
		    	}
		    	if(year.id == y && k == date.getMonth()){
		    		state.month = k + 1;
		    	}
		    	months.push(mon);
		    }
			year.monthList = months;
		    
			// 一年第一个周一
		    var firstDay = new Date(year.id, 0, 1) 
			while (firstDay.getDay() != 1) {
				firstDay.setDate(firstDay.getDate() + 1);
		    }
		    var to = new Date(year.id, 0, 1) 
		    while (to.getDay() != 1) {
		    	to.setDate(to.getDate() + 1);
		    }
		    // 下一年的周一
		    var lastDay = new Date(year.id + 1, 0, 1) 
		    while (lastDay.getDay() != 1) {
		    	lastDay.setDate(lastDay.getDate() + 1);
		    }
		    // 这一年共多少天
		    var days = Math.floor((lastDay - firstDay) / (24 * 3600 * 1000));
		    // 这一年共多少周
		    var w = days / 7;
		    // 保存周集合
		    var weeks = [];
		    var j = 0
		    for(var from = firstDay; from < lastDay; ){
		    	var week = {};
		    	week.id = j + 1;
		    	week.name = "第" + week.id + "周";
		    	to.setDate(to.getDate() + 7)
		    	if(date > from && date < to){
		    		state.week = week.id;
		    	}
		    	// 计算每周的开始结束日期
		    	var startTime = year.id+"-"+((from.getMonth() + 1) > 9 ? (from.getMonth() + 1) : '0'+(from.getMonth() + 1))+"-"+(from.getDate() > 9 ? from.getDate() : '0'+from.getDate());
		    	var endTime = null;
		    	var time = year.id + "年第" + week.id + "周 " + (from.getMonth() + 1) + "月" + from.getDate() + "日 - ";
		    	
		    	from.setDate(from.getDate() + 6);
		        if (from < lastDay) {
		        	time += (from.getMonth() + 1) + "月" + from.getDate() + "日";
		        	endTime = year.id+"-"+((from.getMonth() + 1) > 9 ? (from.getMonth() + 1) : '0'+(from.getMonth() + 1)) + "-" + (from.getDate() > 9 ? from.getDate() : '0'+from.getDate());
		        	if(startTime > endTime){
		        		endTime = year.id+1+"-"+((from.getMonth() + 1) > 9 ? (from.getMonth() + 1) : '0'+(from.getMonth() + 1)) + "-" + (from.getDate() > 9 ? from.getDate() : '0'+from.getDate());
		        	}
		            from.setDate(from.getDate() + 1);
		        } else {
		        	time += (lastDay.getMonth() + 1) + "月" + lastDay.getDate() + "";
		        	endTime = year.id+"-"+((lastDay.getMonth() + 1) > 9 ? (lastDay.getMonth() + 1) : '0'+(lastDay.getMonth() + 1)) + "-" + (lastDay.getDate() > 9 ? lastDay.getDate() : '0'+lastDay.getDate());
		        	if(startTime > endTime){
		        		endTime = year.id+1+"-"+((lastDay.getMonth() + 1) > 9 ? (lastDay.getMonth() + 1) : '0'+(lastDay.getMonth() + 1)) + "-" + (lastDay.getDate() > 9 ? lastDay.getDate() : '0'+lastDay.getDate());
		        	}
		        	lastDay.setDate(lastDay.getDate() - 1);
		        }
		        week.time = time;
		        week.startTime = startTime;
		        week.endTime = endTime;
		    	weeks.push(week);
		    	j++
		    }
		    year.weekList = weeks;
			list.push(year);
		}
		state.yearList = list;
	},
	showSelClick (state, args) {
		state.showSel = !state.showSel
	},
	getMaintenanceRole (state, args) {
		state.maintenanceFlag = null;
		$.ajax({
			type:"post",
			url:ctx + "/f/wechat/repair/getMaintenanceRole",
			data:JSON.stringify(args),
			contentType:"application/json",
			success:function(data)
			{
				state.maintenanceFlag = data && data > 0 ? true : false;
			}
		})
	}
}
const myActions = {
	setUserInformation (context, args) {
		context.commit('setUserInformation', args)
	},
	logOut (context, args) {
		context.commit('logOut', args)
	},
	setUserDeail (context, args) {
		context.commit('setUserDeail', args)
	},
	getContractList (context, args) {
		context.commit('getContractList', args)
	},
	setYearList (context, args) {
		context.commit('setYearList', args)
	},
	showSelClick (context, args) {
		context.commit('showSelClick', args)
	},
	getMaintenanceRole (context, args) {
		context.commit('getMaintenanceRole', args)
	}
}
const myGetters = {
}

const myStore = {
  namespaced: true,
  state: myState,
  mutations: myMutations,
  actions: myActions,
  getters: myGetters
}