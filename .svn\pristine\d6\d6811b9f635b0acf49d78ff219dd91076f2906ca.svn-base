package com.kyb.pcberp.modules.stock.service;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.modules.contract.dao.ContractCraftDao;
import com.kyb.pcberp.modules.contract.dao.ContractDetailDao;
import com.kyb.pcberp.modules.contract.dao.DeliveryDetailDao;
import com.kyb.pcberp.modules.contract.dao.NotificationDao;
import com.kyb.pcberp.modules.contract.entity.ContractCraft;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.contract.entity.DeliveryDetail;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.contract.service.MatPreparationService;
import com.kyb.pcberp.modules.crm.dao.RejectApplicationDao;
import com.kyb.pcberp.modules.crm.entity.RejectApplication;
import com.kyb.pcberp.modules.purch.dao.PurchasingDetailDao;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import com.kyb.pcberp.modules.stock.dao.*;
import com.kyb.pcberp.modules.stock.entity.*;
import com.kyb.pcberp.modules.stock.vo.WaitingMaterialVo;
import com.kyb.pcberp.modules.sys.dao.BranchDao;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import com.kyb.pcberp.modules.sys.dao.ParameterSetDao;
import com.kyb.pcberp.modules.sys.entity.Branch;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.ParameterSet;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import com.kyb.pcberp.modules.wechat.entity.InterProductStockRecord;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Transactional(readOnly = true)
public class MaterialService extends CrudService<MaterialDao, Material> {

    @Autowired
    private MaterialDao materialDao;

    @Autowired
    private MaterialSpecificationDao materialSpecificationDao;

    @Autowired
    private StoreHouseDao storeHouseDao;

    @Autowired
    private DeliveryDetailDao deliveryDetailDao;

    @Autowired
    private ProductStoreDao productStoreDao;

    @Autowired
    private ContractDetailDao contractDetailDao;

    @Autowired
    private ContractCraftDao contractCraftDao;

    @Autowired
    private NotificationDao notificationDao;

    @Autowired
    private RejectApplicationDao rejectApplicationDao;

    @Autowired
    private MaterialDepositConvertDao mdcDao;

    @Autowired
    private MaterialSpecificationService materialSpecificationService;

    @Autowired
    private MaterialInternetDao materialInternetDao;

    @Autowired
    private CompanyDao companyDao;

    @Autowired
    private PurchasingDetailDao purchasingDetailDao;

    @Autowired
    private BranchDao branchDao;

    @Autowired
    private ParameterSetDao parameterSetDao;

    @Autowired
    private MatPreparationService matPreparationService;

    /*
     * public Material get(String id) { return super.get(id); }
     */

    public List<Material> findList(Material material) {
        return super.findList(material);
    }

    public List<Material> findAllList(Material material) {
        return materialDao.findAllList(material);
    }

    public Page<Material> findPage(Page<Material> page, Material material) {
        material.setPage(page);
        page.setList(dao.findList(material));
        return page;
    }

    // zjn 2017-08-23 查询不包括余料的物料集合
    public Page<Material> findPageNoRemain(Page<Material> page, Material material) {
        material.setPage(page);
        page.setList(dao.findListNoRemain(material));
        return page;
    }

    /**
     * 由于新生成的企业需要具备板厚，板料等固定属性，所以为了不耦合后台添加企业，在这里添加企业的物料规格固定属性。 此为查看关联物料规格属性的第一步，此加最为合适。且其固定物料规格不可以删除，所以只要有一个属性就代表此企业已经
     * 初始化物料规格的时候已经添加了其规格，或者已经不是新建的企业,也就是说只有点击物料规格列表之后，才会有这个新增的 企业的固定规格属性数据，如果不点是没有的，只能作为固定的规则自己去判断去加
     */
    // private void addSpecForCompany()
    // {
    // List<String> enNames = Arrays.asList("banLiao", "banHou", "daoRe", "width", "Length");// 固定属性
    // List<String> names = Arrays.asList("板料", "板厚", "导热系数", "宽", "长");// 固定属性
    //
    // User adminUser = userDao.getAdmin(UserUtils.getUser().getCompany());// 获取管理员
    //
    // MaterialSpecification materialSpecification = new MaterialSpecification();
    // materialSpecification.setVisible("1");// 可见
    // materialSpecification.setExport("1");// 导出
    // materialSpecification.setActiveFlag("1");// 正常
    // materialSpecification.setSystemFlag("1");// 系统生成
    //
    // materialSpecification.setCompany(UserUtils.getUser().getCompany());
    // for (int i = 0; i < names.size(); i++)
    // {// 重置的固定属性
    // materialSpecification.setName(names.get(i));
    // materialSpecification.setEnName(enNames.get(i));
    //
    // // 由于数据表里面有加了的相同名字的物料规格，所以需要判断，如果有相同的名字了就更新，并且把其变成系统固定的属性，如果没有就添加
    // MaterialSpecification mSpecification = null;
    // MaterialSpecification mSpecification1 = null;// 厚
    // MaterialSpecification mSpecification2 = null;// 板厚
    //
    // if (names.get(i).equals("板厚"))
    // { // 企业有可能把
    // mSpecification1 = materialSpecificationDao.getSpecByChinese1(materialSpecification);
    // }
    // mSpecification2 = materialSpecificationDao.getSpecByChinese(materialSpecification);
    //
    // if (mSpecification1 != null)
    // {
    // mSpecification = mSpecification1;
    // }
    // if (mSpecification2 != null)
    // {
    // mSpecification = mSpecification2;
    // }
    // if (mSpecification != null)
    // {
    // // 把其变成系统属性
    // mSpecification.setName(names.get(i));
    // mSpecification.setEnName(enNames.get(i));
    // mSpecification.setLastUpdBy(adminUser);
    // mSpecification.setLastUpdDate(new Date());
    // mSpecification.setSystemFlag("1");// 系统生成
    // materialSpecificationDao.updateSpec(mSpecification);// 有数据就初始化成系统属性
    // }
    // else if (materialSpecificationDao.getSpec(materialSpecification) == null)
    // {// 由于现在规格和规格数据重置分开 而且固定属性不可以删除，所以如果没有此规格属性这里就修复增加
    // materialSpecification.setLastUpdBy(adminUser);
    // materialSpecification.setCreatedBy(adminUser);
    // materialSpecification.setLastUpdDate(new Date());
    // materialSpecification.setCreatedDate(new Date());
    // materialSpecificationDao.insert(materialSpecification);
    // }
    // }
    // }
    public List<MaterialSpecification> findSpecs(Company company) {
        MaterialSpecification spec = new MaterialSpecification();
        spec.setCompany(UserUtils.getUser().getCompany());
        return materialSpecificationService.findAllList(spec);
    }

    /**
     * 添加原料规格集合和规格数据集合
     */
    private List<Material> deal2AddSpec(List<Material> materialList) {
        if(Collections3.isEmpty(materialList))
        {
            return materialList;
        }
        String materialIds = null;
        for(Material mat : materialList)
        {
            if(null == mat.getMaterialType() || StringUtils.isBlank(mat.getMaterialType().getValue()))
            {
                continue;
            }
            if(StringUtils.isNotBlank(materialIds))
            {
                materialIds = materialIds +","+ mat.getRecordId();
            }
            else
            {
                materialIds = mat.getRecordId();
            }
        }
        if(StringUtils.isBlank(materialIds))
        {
            return materialList;
        }

        // 获取规格项和值
        Material query = new Material();
        query.setRecordId(materialIds);
        query.setCompany(UserUtils.getUser().getCompany());
        List<MaterialSpecification> specs = materialSpecificationDao.findSpecsByMaterial(query);
        if (specs != null && specs.size() > 0) {
            specs = materialSpecificationService.getValues(specs);
        }

        // 获取库位组
        List<MaterialPlaceCom> mpcList = storeHouseDao.geMpcIdsByMat(materialIds);

        for(Material mat : materialList)
        {
            if(Collections3.isNotEmpty(specs))
            {
                mat.setMaterialSpecList(Lists.newArrayList());
                for(MaterialSpecification spe : specs)
                {
                    if(spe.getMaterialId().equals(mat.getRecordId()))
                    {
                        mat.getMaterialSpecList().add(spe);
                    }
                }
            }
            if(Collections3.isNotEmpty(mpcList))
            {
                for(MaterialPlaceCom mpc : mpcList)
                {
                    if(mpc.getMaterialId().equals(mat.getRecordId()))
                    {
                        mat.setStockPlaceId(mpc.getMaterPlaceId());
                        break;
                    }
                }
            }
        }
        // 对原料page进行加工
/*        if (Collections3.isNotEmpty(materialList)) {
            for (Material material : materialList) {
                if (material != null && material.getMaterialType() != null
                        && StringUtils.isNotBlank(material.getMaterialType().getValue())) {
                    List<MaterialSpecification> specs = materialSpecificationDao.findSpecsByMaterial(material);
                    if (specs != null && specs.size() > 0) {
                        specs = materialSpecificationService.getValues(specs);
                    }
                    material.setMaterialSpecList(specs);
                }
            }
        }*/
        return materialList;
    }

    /**
     * zjn 2017-06-16 投/补料出库原料列表
     */
    @Transactional(readOnly = false)
    public Page<Material> rowInoutGetList(Page<Material> page, Material material) {
        material.setPage(page);
        List<Material> materialList = new ArrayList<>();
        Material materialCopy = null;
        if (StringUtils.isNotBlank(material.getNo())) {
            String[] strNo = material.getNo().split(",");
            for (int i = 0, len = strNo.length; i < len; i++) {
                Material materialNoCopy = new Material();
                materialNoCopy.setNo(strNo[i].toString());
                materialNoCopy.setCompany(UserUtils.getUser().getCompany());
                materialCopy = materialDao.getByMaterialNo(materialNoCopy);

                if (StringUtils.isNotBlank(material.getPcsQuantity())) {
                    String[] strPcsQuantity = material.getPcsQuantity().split(",");
                    materialCopy.setPcsQuantity(strPcsQuantity[i]);
                }
                if (StringUtils.isNotBlank(material.getQuantity())) {
                    String[] strQuantity = material.getQuantity().split(",");
                    if (strQuantity.length >= i) {
                        if (StringUtils.isNotBlank(strQuantity[i])) {
                            materialCopy.setQuantity(strQuantity[i]);
                        }
                    }
                }
                if (StringUtils.isNotBlank(material.getTotalPcsQuantity())) {
                    String[] strTotalPcsQuantity = material.getTotalPcsQuantity().split(",");
                    if (strTotalPcsQuantity.length >= i) {
                        if (StringUtils.isNotBlank(strTotalPcsQuantity[i])) {
                            materialCopy.setTotalPcsQuantity(strTotalPcsQuantity[i]);
                        }
                    }
                }
                materialList.add(materialCopy);
            }
        }
        materialList = deal2AddSpec(materialList);// 添加原料规格集合和规格数据集合
        page.setList(materialList);
        return page;
    }

    /**
     * 物料显示 在page基础上多添加了物料规格
     */
    @Transactional(readOnly = false)
    public Page<Material> materialFindPage(Page<Material> page, Material material) {
        material.setPage(page);
        if (StringUtils.isNotBlank(material.getSpecification()))
        {
            String[] specifications = material.getSpecification().split(" ");
            material.setCondition1(null);
            material.setCondition2(null);
            material.setCondition3(null);
            material.setCondition4(null);
            material.setCondition5(null);
            material.setCondition6(null);
            if (null != specifications && specifications.length > 0)
            {
                for (int i = 0; i < specifications.length; i++)
                {
                    String condition = specifications[i];
                    switch (i)
                    {
                        case 0:
                            material.setCondition1(condition);
                            break;
                        case 1:
                            material.setCondition2(condition);
                            break;
                        case 2:
                            material.setCondition3(condition);
                            break;
                        case 3:
                            material.setCondition4(condition);
                            break;
                        case 4:
                            material.setCondition5(condition);
                            break;
                        case 5:
                            material.setCondition6(condition);
                            break;
                    }
                }
            }
        }
        List<Material> materialList = materialDao.safetyStockList(material);
        // 获取物料结余
        RawStockUtil rawStockUtil = new RawStockUtil();
        if (Collections3.isNotEmpty(materialList))
        {
            for (Material material1 : materialList)
            {
                BigDecimal stocks = rawStockUtil.getStockPlaceComStocks(null, material1.getRecordId());
                material1.setStocks(stocks);
            }
        }
        materialList = deal2AddSpec(materialList);// 添加原料规格集合和规格数据集合
        page.setList(materialList);
        return page;
    }

    @Transactional(readOnly = false)
    public void save(Material material) {
        if (material.getIsNewRecord()) {
            material.preInsert();
            dao.insert(material);
            if (material.getMaterialKind() == 100701) {
                // 添加成功把对应编码的nextNo 原料
                CommonUtils.updateNextNo(11);
            } else {
                // 添加成功把对应编码的nextNo 成品
                CommonUtils.updateNextNo(18);
            }
        } else {
            material.preUpdate();
            dao.update(material);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Material material) {
        super.delete(material);
    }

    /**
     * 查找默认仓库
     *
     * @param company
     * @return
     */
    public List<StoreHouse> selectStoreHouselist(Company company) {
        return storeHouseDao.selectStoreHouselist(company);
    }

    /**
     * 查找索引供应商
     *
     * @param company
     * @return
     */
    public List<Supplier> selectSupplierlist(Company company) {
        return storeHouseDao.selectSupplierlist(company);
    }

    /**
     * wc:2016-12-01 查询原料类型仓库列表
     *
     * @param company
     * @return
     */
    public List<StoreHouse> selectRawMaterialStoreHouselist(Company company) {
        return storeHouseDao.selectRawMaterialStoreHouselist(company);
    }

    /**
     * 修改或添加物料 kindFlag 物料种类 sysFlag 成品物料是否修改过
     *
     * @param material
     * @return
     */
    @Transactional(readOnly = false)
    public String saveMaterial(Material material, boolean kindFlag, boolean sysFlag) {
        // 查询是否存在
        Material mt = materialDao.findMaterialByNo(material);
        boolean isExistNo = true;
        if (mt == null) {
            isExistNo = false;
        }
        Integer codeType = null;
        if (null != material.getMaterialKind()) {
            switch (material.getMaterialKind()) {
                case 100701:
                    codeType = CommonEnums.CodeType.MANAGEMENT.getIndex();
                    break;
                case 100702:
                    codeType = CommonEnums.CodeType.PRODUCTMATERIALS.getIndex();
                    break;
                case 100703:
                    codeType = CommonEnums.CodeType.LARGETYPE.getIndex();
                    break;
            }
        }

        String rs = "";
        if (kindFlag) {
            // 如果是修改生产编号则进行校验
            if (sysFlag) {
                if (material.getIsNewRecord()) {
                    if (isExistNo) {
                        // 如果是自动生成的生产编号
                        getMaterialNo(material, codeType);
                    }
                    material.preInsert();
                    dao.insert(material);
                    CommonUtils.updateNextNo(codeType);
                } else {
                    material.preUpdate();
                    dao.update(material);
                }
                if (isExistNo) {
                    rs = "初始编号已存在,系统成功自动分配了新的编号为:" + material.getNo();
                } else {
                    rs = "'" + material.getNo() + "'成功!";
                }
            } else {
                // 如果不是自动生成的生产编号
                if (mt != null) {
                    rs = "{\"flag\":true,\"rs\":\"该生产编号已存在,不能被重复添加,请确认后修改生产编号!\"}";
                } else {
                    if (material.getIsNewRecord()) {
                        material.preInsert();
                        // 添加成功把对应编码的nextNo 修改为+1
                        if (isExistNo) {
                            // 如果是自动生成的生产编号
                            getMaterialNo(material, codeType);
                        }
                        dao.insert(material);
                        CommonUtils.updateNextNo(codeType);
                    } else {
                        material.preUpdate();
                        dao.update(material);
                    }
                    if (isExistNo) {
                        rs = "初始编号已存在,系统成功自动分配了新的编号为:" + material.getNo();
                    } else {
                        rs = "'" + material.getNo() + "'成功!";
                    }
                }
            }
        } else {
            if (material.getIsNewRecord()) {
                material.preInsert();
                if (isExistNo) {
                    // 添加成功把对应编码的nextNo 修改为+1
                    getMaterialNo(material, codeType);
                }
                dao.insert(material);
                CommonUtils.updateNextNo(codeType);
            } else {
                material.preUpdate();
                dao.update(material);
            }
            if (isExistNo) {
                rs = "初始编号已存在,系统成功自动分配了新的编号为:" + material.getNo();
            } else {
                rs = "'" + material.getNo() + "'成功!";
            }
        }
        return rs;
    }

    private void getMaterialNo(Material material, Integer type) {
        Material mt = materialDao.findMaterialByNo(material);
        if (mt != null) {
            CommonUtils.updateNextNo(type, -1);
            String no = CommonUtils.geDocumentNo(type.toString());
            material.setNo(no);
            getMaterialNo(material, type);
        }
    }

    @Transactional(readOnly = false)
    public String saveMaterialAndCraft(Material material) {
        material.setCompany(UserUtils.getUser().getCompany());
        int type = 1;
        if (!material.getIsNewRecord()) {
            type = 2;
        }
        // 查询是否存在
        String rs = "";
        if (material.getIsNewRecord()) {
            material.preInsert();
            // 判断编号是否被引用了 是就累加
            Material materialNo = dao.getByMaterialNo(material); // 查询编号是否依旧存在
            while (materialNo != null && StringUtils.isNotBlank(materialNo.getRecordId())) {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.MANAGEMENT.getIndex());
                material.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.MANAGEMENT.getIndex().toString()));
                materialNo = dao.getByMaterialNo(material); // 查询编号是否依旧存在
            }
            dao.insert(material);
            CommonUtils.updateNextNo(CommonEnums.CodeType.MANAGEMENT.getIndex());
        } else {
            material.preUpdate();
            dao.update(material);
        }
        if (Collections3.isNotEmpty(material.getMaterialSpecList())) {
            material.getMaterialSpecList().forEach(item -> {
                item.preInsert();
            });
            // 先删除
            materialSpecificationDao.deleteRelationByMaterial(material);

            // 后添加
            materialSpecificationDao.insertRelationByMaterial(material);
        }

        // 保存物料库位
        saveMaterialChildcompany(material);

        rs = "'" + material.getNo() + "'成功!";
//        ShareDataUtil.getInstance().saveData(material, 6, type);
        return rs;
    }

    /**
     * 修改或添加余料 kindFlag 物料种类 sysFlag 成品物料是否修改过 zjn 2017-06-09
     *
     * @param material
     * @return
     */
    @Transactional(readOnly = false)
    public String saveRemainMaterialAndCraft(Material material, boolean kindFlag, boolean sysFlag) {
        material.setRemain(ConstKey.MATERIAL_REMAIN_YES);
        String rs = "";
        if (material.getIsNewRecord()) {
            material.preInsert();
            // 判断编号是否被引用了 是就累加
            if (material.getMaterialNoIsEnable() == 1) {
                material.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.MANAGEMENT.getIndex().toString()));
            }
            dao.insert(material);
        } else {
            material.preUpdate();
            dao.update(material);
        }

        if (Collections3.isNotEmpty(material.getMaterialSpecList())) {
            material.getMaterialSpecList().forEach(item -> {
                item.preInsert();
            });

            // 先删除
            materialSpecificationDao.deleteRelationByMaterial(material);

            // 后添加
            materialSpecificationDao.insertRelationByMaterial(material);
        }
        rs = "'" + material.getNo() + "'成功!";
        return rs;
    }

    /**
     * 成品或原料的分页页面
     *
     * @param qpage
     * @param material
     * @return
     */
    @Transactional(readOnly = false)
    public Page<Material> findProductMPage(Page<Material> qpage, Material material) {
        material.setPage(qpage);
        if (StringUtils.isNotBlank(material.getSpecification()))
        {
            String[] specifications = material.getSpecification().split(" ");
            material.setCondition1(null);
            material.setCondition2(null);
            material.setCondition3(null);
            material.setCondition4(null);
            material.setCondition5(null);
            material.setCondition6(null);
            if (null != specifications && specifications.length > 0)
            {
                for (int i = 0; i < specifications.length; i++)
                {
                    String condition = specifications[i];
                    switch (i)
                    {
                        case 0:
                            material.setCondition1(condition);
                            break;
                        case 1:
                            material.setCondition2(condition);
                            break;
                        case 2:
                            material.setCondition3(condition);
                            break;
                        case 3:
                            material.setCondition4(condition);
                            break;
                        case 4:
                            material.setCondition5(condition);
                            break;
                        case 5:
                            material.setCondition6(condition);
                            break;
                    }
                }
            }
        }
        /* ojh 2016.10.12 增加库存面积和尺寸 begin */
        List<Material> materialList = new ArrayList<>();
        materialList = materialDao.findProductList(material);

        if (materialList.size() > 0) {
            for (Material materialEntity : materialList) {
                // if (materialEntity.getMaterialKind() == 100701)
                // {
                // RawStockUtil rawStockUtil = new RawStockUtil();
                // RawmaterialStock rawmaterialStock =
                // rawStockUtil.getMaterialStocks(materialEntity.getRecordId(), null);
                // BigDecimal stocks = rawmaterialStock.getQuantity();
                // materialEntity.setStocks(stocks);
                //
                // materialEntity.setPrice(rawStockUtil.getPriceByMaterial(materialEntity, null));
                // }
                // 成品库存面积inAreaReport
                Map<String, Object> areaAndPrDimensionMaps =
                        getAreaAndPrDimension(materialEntity, materialEntity.getStocks());
                materialEntity.setStockAreaReport(new BigDecimal(areaAndPrDimensionMaps.get(ConstKey.PRODUCT_STOCK_AREA_REPORT)
                        .toString()));
                // 成品尺寸prDimension
                materialEntity.setStockPrDimension((areaAndPrDimensionMaps.get(ConstKey.PRODUCT_STOCK_DIMENSION)).toString());

                // 获取物料关联的规格信息
                List<MaterialSpecification> specs = materialSpecificationDao.findSpecsByMaterialAll(materialEntity);
                if (specs != null && specs.size() > 0) {
                    specs = materialSpecificationService.getValues(specs);
                }
                materialEntity.setMaterialSpecList(specs);
            }
            for (Material material1 : materialList)
            {
                // 获取产品维护表单价
                /*material1.setSupRawmaterialPrice(materialDao.getSupRawmaterialPrice(material1));
                material1.setErpRawmaterialPrice(materialDao.getErpRawmaterialPrice(material1));
                material1.setSupRawmaterialLeadTime(materialDao.getSupRawmaterialLeadTime(material1));
                material1.setErpRawmaterialLeadTime(materialDao.getErpRawmaterialLeadTime(material1));*/
                InterProductStockRecord interProductStockRecord = new InterProductStockRecord();
                interProductStockRecord.setProductId(material1.getRecordId());
                interProductStockRecord = materialDao.getInterProductStockRecord(interProductStockRecord);
                if (null != interProductStockRecord)
                {
                    material1.setInterProductStockRecord(interProductStockRecord);
                    if (null != interProductStockRecord.getPrice())
                    {
                        material1.setErpRawmaterialPrice(interProductStockRecord.getPrice().doubleValue());
                        if (null != material1.getRawmaterialPrice())
                        {
                            if (!material1.getErpRawmaterialPrice().equals(material1.getRawmaterialPrice()))
                            {
                                material1.setMarkedRed(1);
                            }
                            if (null == material1.getRawmaterialPrice())
                            {
                                material1.setMarkedRed(1);
                            }
                        }
                    }
                    if (null != interProductStockRecord.getLeadTime())
                    {
                        material1.setErpRawmaterialLeadTime(interProductStockRecord.getLeadTime());
                        if (null != material1.getLeadTime())
                        {
                            if (!material1.getErpRawmaterialLeadTime().equals(material1.getLeadTime()))
                            {
                                material1.setLeadTimeRed(1);
                            }
                            if (null == material1.getLeadTime())
                            {
                                material1.setLeadTimeRed(1);
                            }
                        }
                    }
                }
            }
        }

        qpage.setList(materialList);
        return qpage;
    }

    /**
     * 计算成品库存面积和尺寸
     *
     * @param materialEntity 成品entity
     * @param availCount     成品库存数量
     * @return
     */
    public Map<String, Object> getAreaAndPrDimension(Material materialEntity, BigDecimal availCount) {

        // 成品退货面积inAreaReport
        // 成品尺寸prDimension
        Map<String, Object> areaAndPrDimensionMaps = new HashMap<String, Object>();
        BigDecimal area = BigDecimal.ZERO;
        String prDemesion = "";

        if (materialEntity != null) {
            /*
             * 通过物料编号来查询合同工艺明细 因为都已经成品入库了所以都存在md_material.no == sl_notification.craftno = sl_contract_craft.no
             * 因为物料要么直接新加，没有no,要么在通知单的时候添加生产编码投料的时候同时会设置到物料的no中去，
             * 一个生产编码对应一个合同明细，一个合同明细对应一个contractCraft,contractCraft在合同明细保存的时候生成，所以物料no=contractCraft.no
             */
            ContractCraft contractCraft = new ContractCraft();
            contractCraft.setCompany(UserUtils.getUser().getCompany());
            contractCraft.setActiveFlag(TypeKey.ACTIVE.toString());
            contractCraft.setNo(materialEntity.getNo());
            List<ContractCraft> ContractCraftList = contractCraftDao.getContractCraftByNo(contractCraft);
            // 只有一个合同明细
            if (ContractCraftList.size() > 0) {
                contractCraft = ContractCraftList.get(0);
            }
            if (contractCraft.getPnlLength() != null && contractCraft.getPnlLength().compareTo(BigDecimal.ZERO) == 1
                    && contractCraft.getPnlWidth() != null && contractCraft.getPnlWidth().compareTo(BigDecimal.ZERO) == 1
                    && contractCraft.getPnlDivisor() != null
                    && contractCraft.getPnlDivisor().compareTo(BigDecimal.ZERO) == 1) {
                // 采购尺寸不为空
                BigDecimal pnlLength = contractCraft.getPnlLength();
                BigDecimal pnlWidth = contractCraft.getPnlWidth();
                BigDecimal pnlDivisor = contractCraft.getPnlDivisor();
                // 采购尺寸
                prDemesion = pnlLength + "x" + pnlWidth + "/" + pnlDivisor;

                if (availCount != null && availCount.compareTo(BigDecimal.ZERO) == 1) {
                    area = pnlLength.multiply(pnlWidth)
                            .multiply(availCount)
                            .divide(pnlDivisor, 4, BigDecimal.ROUND_HALF_UP)
                            .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
                }
            } else if (contractCraft.getUnitLength() != null
                    && contractCraft.getUnitLength().compareTo(BigDecimal.ZERO) == 1 && contractCraft.getUnitWidth() != null
                    && contractCraft.getUnitWidth().compareTo(BigDecimal.ZERO) == 1 && contractCraft.getPnlDivisor() != null
                    && contractCraft.getPnlDivisor().compareTo(BigDecimal.ZERO) == 1) {
                // 采购尺寸为空,取单位尺寸
                BigDecimal unitLength = contractCraft.getUnitLength();
                BigDecimal unitWidth = contractCraft.getUnitWidth();
                BigDecimal pnlDivisor = contractCraft.getPnlDivisor();
                // 采购尺寸
                prDemesion = unitLength + "x" + unitWidth + "/" + pnlDivisor;

                if (availCount != null && availCount.compareTo(BigDecimal.ZERO) == 1) {
                    area = unitLength.multiply(unitWidth)
                            .multiply(availCount)
                            .divide(pnlDivisor, 4, BigDecimal.ROUND_HALF_UP)
                            .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
                }
            }

        }
        areaAndPrDimensionMaps.put(ConstKey.PRODUCT_STOCK_AREA_REPORT, area);
        areaAndPrDimensionMaps.put(ConstKey.PRODUCT_STOCK_DIMENSION, prDemesion);

        return areaAndPrDimensionMaps;
    }

    /**
     * 成品库存导出
     *
     * @param qpage
     * @param material
     * @return
     */
    public List<Material> findProductExportList(Material material) {
        return materialDao.findProductExportList(material);
    }

    /**
     * 查询所有物料编号
     *
     * @param material
     * @return
     */
    public List<Material> queryAllMaterialNo(Material material) {
        return materialDao.queryAllMaterialNo(material);
    }

    /**
     * 查找所有物料包括余料，有最大限制（起始为pageNo，终止为pageSize），可根据编号搜索
     *
     * @param material
     * @return
     */
    public List<Material> queryMaterialsByNoAndLimit(Material material) {
        // WC 2017-08-01 过滤掉作废的物料
        material.setStatus(ConstKey.MATERIAL_NO_CANCLE_STATUS);
        return materialDao.queryMaterialsByNoAndLimit(material);
    }

    /**
     * WC 2017-07-13 ADD 查找所有物料，有最大限制（起始为pageNo，终止为pageSize），可根据编号/名称搜索
     */
    public List<Material> queryMaterialsByNoOrNameAndLimit(Material material) {
        // WC 2017-08-01 过滤掉作废的物料
        material.setStatus(ConstKey.MATERIAL_NO_CANCLE_STATUS);
        return materialDao.queryMaterialsByNoOrNameAndLimit(material);
    }

    public List<Material> getMaterialListByBean(Material material) {
        return materialDao.getMaterialListByBean(material);
    }

    public List<Material> findNeedSetMonthPriceList(Material material) {
        return materialDao.findNeedSetMonthPriceList(material);
    }

    /**
     * 原料出入库的其它入库中需要的查询显示编号的原料
     *
     * @param material
     * @return
     */
    public List<Material> queryMaterialNo(Material material) {
        /*return materialDao.queryMaterialNo(material);*/
        return materialDao.queryMaterialNoTwo(material);
    }

    /**
     * 查询显示编号的原料不包括余料
     *
     * @param material
     * @return
     */
    public List<Material> queryMaterialIsNo(Material material) {
        material.setRemain(ConstKey.MATERIAL_REMAIN_NO);
        return materialDao.queryMaterialIsNo(material);
    }

    /**
     * 原料出入库的其它入库中需要的查询显示编号的余料 zjn 2017-06-05
     *
     * @param material
     * @return
     */
    public List<Material> queryMaterialRemainNo(Material material) {
        material.setRemain(ConstKey.MATERIAL_REMAIN_YES);
        return materialDao.queryMaterialRemainNo(material);
    }

    public Material findMaterialByNo(Material material) {
        return materialDao.findMaterialByNo(material);
    }

    public boolean getMaterialIsLinked(Material material) {
        // 查询该物料是否被使用
        List<Integer> counts = materialDao.getMaterialIsLinked(material);
        for (Integer count : counts) {
            if (count.intValue() > 0) {
                return false;
            }
        }
        return true;
    }

    public Integer findNoisEnable(Material material, boolean kindFlag) {
        Integer isNos = 0;
        // 设置公司
        material.setCompany(UserUtils.getUser().getCompany());
        if (kindFlag) {
            // 如果是成品
            isNos = materialDao.findCraftNoisEnable(material);
        } else {
            // 如果是原料
            isNos = materialDao.findMaterialNoisEnable(material);
        }
        return isNos;
    }

    @Transactional(readOnly = false)
    public String convertToStocks(Material material) {
        Material newMaterial = materialDao.findMaterialByNo(material);
        Integer newxboardQty = newMaterial.getXboardQty() == null ? 0 : newMaterial.getXboardQty();
        if (material.getBeConvertxboardQty().compareTo(newxboardQty) == 1
                || material.getXboardQty().compareTo(newxboardQty) == 1) {
            return "亲,该叉板可能已经被折算过,请确认后重新折算!";
        } else {
            // 更新叉板数量，库存数量以及库存可用数量
            materialDao.updateMaterialWithConvert(material);
        }
        return "叉板折算成功！";
    }

    /**
     * 初始
     */
    @Transactional(readOnly = false)
    public String initDelOaQty() {
        Company company = UserUtils.getUser().getCompany();
        // 初始所有的物料已占用数量
        materialDao.updateInitDeliveryOaQty(company.getRecordId(), Company.DEL_FLAG_NORMAL);
        // 1.查询所有添加的送货单
        DeliveryDetail deliveryDetail = new DeliveryDetail();
        deliveryDetail.setCompany(company);
        List<DeliveryDetail> initDelOas = deliveryDetailDao.findInitDelOaList(deliveryDetail);
        StringBuffer sb = new StringBuffer();
        for (DeliveryDetail del : initDelOas) {
            // 送货数量
            Integer delSum = del.getQuantity();
            delSum += del.getSpares() == null ? 0 : del.getSpares();
            // 2.查询所有该送货单明细的出库记录
            Integer delQtys = productStoreDao.quaryDelQtys(del.getRecordId(), company.getRecordId());
            if (delQtys == null || delQtys.compareTo(delSum) == -1) {
                // 更新该物料的已送货数量
                Integer updateDelOaQty = delSum;
                updateDelOaQty -= delQtys == null ? 0 : delQtys;
                if (del.getContractDetail() != null && StringUtils.isNotBlank(del.getContractDetail().getRecordId())) {
                    // 正常送货
                    ContractDetail contractDetail =
                            contractDetailDao.getCraftNoById(del.getContractDetail().getRecordId());
                    if (contractDetail != null && contractDetail.getCraft() != null
                            && contractDetail.getCraft().getNo() != null) {
                        // 3.已送货占用数量 = 送货单明细的数量+备用数量-该送货单明细出库记录的总数量
                        materialDao.updateDeliveryOaQtyByNo(updateDelOaQty,
                                contractDetail.getCraft().getNo(),
                                company.getRecordId(),
                                DeliveryDetail.DEL_FLAG_NORMAL);
                    } else {
                        sb.append("   送货明细ID：" + del.getRecordId() + " 对应的合同明细或合同工艺异常;");
                    }
                } else if (del.getComplaint() != null && StringUtils.isNotBlank(del.getComplaint().getRecordId())) {
                    // 客诉送货
                    // 4.根据客诉单查询对应的合同和合同工艺
                    RejectApplication rejectApplication = new RejectApplication(del.getComplaint().getRecordId());
                    rejectApplication = rejectApplicationDao.getByIdForDelivery(rejectApplication);
                    if (rejectApplication != null && rejectApplication.getContractDetail() != null
                            && rejectApplication.getContractDetail().getCraft() != null
                            && rejectApplication.getContractDetail().getCraft().getNo() != null) {
                        // 3.已送货占用数量 = 送货单明细的数量+备用数量-该送货单明细出库记录的总数量
                        materialDao.updateDeliveryOaQtyByNo(updateDelOaQty,
                                rejectApplication.getContractDetail().getCraft().getNo(),
                                company.getRecordId(),
                                DeliveryDetail.DEL_FLAG_NORMAL);
                    } else {
                        sb.append("   客诉送货明细ID：" + del.getRecordId() + " 对应的合同明细或合同工艺异常;");
                    }
                } else {
                    sb.append("   客诉送货明细ID：" + del.getRecordId() + " 数据异常;");
                }
            }
        }
        sb.append("   已送货占用库存数量初始完成!!!");
        return sb.toString();
    }

    /**
     * 初始可用库存数量
     */
    @Transactional(readOnly = false)
    public String initAvailableQty() {
        Company company = UserUtils.getUser().getCompany();
        StringBuffer sb = new StringBuffer();
        Material material = new Material();
        material.setCompany(company);
        material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_PRODUCT);
        // 初始可用库存为当前库存
        materialDao.initAvailableQty(material);
        // 1.查询所有库存>0的物料
        List<Material> materials = materialDao.findInitAvailableQty(material);
        for (Material mt : materials) {
            // 2.查询该物料对应的合同工艺,查询出对应的工艺合同明细列表
            ContractCraft conCraft = new ContractCraft();
            conCraft.setCompany(company);
            conCraft.setNo(mt.getNo());
            List<ContractDetail> conDtls = contractDetailDao.findContractDetailByCraftNo(conCraft);
            if (conDtls != null && conDtls.size() > 0) {
                for (ContractDetail cd : conDtls) {
                    if (cd.getStatus() == null || !cd.getStatus()
                            .equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())) {
                        // 先查询已入库的通知单
                        // 可用库存数量 = 库存可用数量-通知单占用数量
                        // 根据合同明细查询关联的通知单
                        cd.setCompany(company);
                        Notification noti = notificationDao.findNotificationByConDtlId(cd);
                        Integer oaQty = 0;
                        if (noti != null) {
                            Integer notiOaQty =
                                    noti.getOccupiedAvailableQty() == null ? 0 : noti.getOccupiedAvailableQty();
                            Integer completedQty = noti.getCompletedQty() == null ? 0 : noti.getCompletedQty();
                            oaQty = completedQty + notiOaQty;
                        }
                        // 获取该合同明细已送货占用数量
                        oaQty += cd.getOaQty() == null ? 0 : cd.getOaQty();
                        if (oaQty.compareTo(0) != 0) {
                            // 更新可用库存
                            mt.setAvailableQty(new BigDecimal(oaQty * -1));
                            materialDao.updateAQty(mt);
                        }
                    }
                }
            }
        }
        Material resetMaterial = new Material();
        resetMaterial.setCompany(company);
        resetMaterial.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_PRODUCT);
        // 重置可用库存为当前库存,可用库存小于0
        materialDao.resetAvailableQty(resetMaterial);
        sb.append("   可用库存数量初始完成!!!");
        return sb.toString();
    }

    /**
     * 初始合同明细已入库已送货数量,目前客诉的还未正式上线，未处理
     */
    @Transactional(readOnly = false)
    public String initContractQty() {
        Company company = UserUtils.getUser().getCompany();
        // 1.查询所有已审核的合同对应的未送货完成的合同明细
        List<ContractDetail> conDtls = contractDetailDao.findNoneDeliveryContractDetails(company);
        if (conDtls != null && conDtls.size() > 0) {
            for (ContractDetail cd : conDtls) {
                // 2.合同明细的已入库数量:查询合同明细对应的所有通知单,查询已完成数量和占用库存数量总数
                cd.setCompany(company);
                Notification noti = notificationDao.findNotificationByConDtlId(cd);
                if (noti != null) {
                    Integer alInHouseQty = 0;
                    Integer notiOaQty = noti.getOccupiedAvailableQty() == null ? 0 : noti.getOccupiedAvailableQty();
                    Integer completedQty = noti.getCompletedQty() == null ? 0 : noti.getCompletedQty();
                    // 该合同明细已入库数量 = 对应的通知单所有已完成入库的数量+通知单占用数量
                    alInHouseQty = completedQty + notiOaQty;
                    cd.setAlInHouseQty(alInHouseQty);
                    cd.setStatus(null);
                    // 3.合同明细的已出库数量:查询该合同明细对应的所有送货单明细的送货数量和备用数量
                    Integer delQtySum = deliveryDetailDao.findDelQtySum(cd);
                    delQtySum = delQtySum == null ? 0 : delQtySum;
                    cd.setAlDeliveryQty(delQtySum);
                    // 更新该合同明细已入库数量
                    if (alInHouseQty.compareTo(0) == 1 || delQtySum.compareTo(0) == 1) {
                        contractDetailDao.updateAlInHouseAndDeliveryQty(cd);
                    }
                }
            }
        }
        return "初始合同明细已入库已送货数量完成!";
    }

    /**
     * 导出
     *
     * @param material
     * @return
     */
    public List<Material> findExpList(Material material) {

        return materialDao.findExpList(material);
    }

    /**
     * 根据物料编号检查是否此物料编号关联的物料已经作废
     *
     * @param materialNo 物料编号 可能是合单的物料编号用逗号隔开,现在直接把不合单的也当作合单处理 企业+material.no是唯一的
     */
    public Boolean getMaterialNotCanceled(String materialNo) {
        Boolean flag = true;
        if (materialNo != null && materialNo != "") {
            String[] materialNoArray = materialNo.split(",");
            for (int i = 0; i < materialNoArray.length; i++) {
                Material material = new Material();
                material.setCompany(UserUtils.getUser().getCompany());
                material.setNo(materialNoArray[i]);
                Material mater = materialDao.getMaterial(material);
                if (mater != null) {
                    if (mater.getStatus().equals(0)) {
                        flag = false;
                        break;
                    }
                }
            }
        }
        return flag;
    }

    // 作废物料更新物料status状态
    @Transactional(readOnly = false)
    public void updateStatus(Material material) {
        materialDao.updateStatus(material);
    }

    @Transactional(readOnly = false)
    public String editStock(Material material) {
        materialDao.editStock(material);
        return "success";
    }

    @Transactional(readOnly = false)
    public String chageMaterialStatus(Material material) {
        if (StringUtils.isNotBlank(material.getInvenFlag()) && material.getInvenFlag().equals("1")) {
            material.setInvenFlag("0");
        } else {
            material.setInvenFlag("1");
        }
        materialDao.chageMaterialStatus(material);
        return "success";
    }

    /**
     * 获取物料不要和基类冲突
     */
    public Material getMaterial(Material material) {
        return materialDao.getMaterial(material);
    }

    /**
     * 查询需要导出的物料
     */
    @Transactional(readOnly = false)
    public List<Material> findExportExpList(Material material) {
        List<Material> list = null;
        /**
         * Mod:lpjLiu 2017-03-07 物料导出的存储过程查询的结果集中不带单价和规格，导出没用
         *
         * if (material.getCompany() != null && StringUtils.isNotBlank(material.getCustomerNo()) ||
         * StringUtils.isNotBlank(material.getNo()) || StringUtils.isNotBlank(material.getName()) ||
         * material.getMaterialKind() != null || material.getMaterialType() != null &&
         * StringUtils.isNotBlank(material.getMaterialType().getRecordId()) || material.getCreatedBy() != null &&
         * StringUtils.isNotBlank(material.getCreatedBy().getRecordId()) || material.isXb() || material.isSafetystock())
         * { list = materialDao.findExportExpList(material); } else { list = materialDao.getMaterial_Proc(material); }
         */
        list = materialDao.findExportExpList(material);
        return list;
    }

    /**
     * 库存盘点根据时间查看物料列表
     */
    @Transactional(readOnly = false)
    public Page<Material> materList(Page<Material> page, Material material) {
        material.setPage(page);
        List<Material> materialList = null;
        if (StringUtils.isNotBlank(material.getSnapshotDate())) {
            if (StringUtils.isNotBlank(material.getSnapMaterFlag())) {
                if (material.getSnapMaterFlag().equals("201602")) {
                    materialList = materialDao.materList(material);

                } else if (material.getSnapMaterFlag().equals("201611")) {
                    materialList = materialDao.materListDeail(material);
                } else {
                    materialList = materialDao.materList(material);
                }
            } else {
                materialList = materialDao.materList(material);
            }

            for (int i = 0; i < materialList.size(); i++) {
                double snapsto = StringUtils.toDouble(materialList.get(i).getSnapStrocks());
                double sto = StringUtils.toDouble(materialList.get(i).getStocks());
                String result = "";
                if (snapsto > sto) {
                    double stockResult = snapsto - sto;
                    result = "库存上调了" + stockResult;
                }
                if (snapsto < sto) {
                    double stockResult = sto - snapsto;
                    result = "库存下调了" + stockResult;
                }
                materialList.get(i).setStockChange(result);

                if (materialList.get(i).getMaterialKind() == 100701) {
                    // 当前时间减一个月
                    Calendar c = Calendar.getInstance();
                    c.add(Calendar.MONTH, -1);
                    Date time = c.getTime();
                    String startTime = DateUtils.formatDate(time, "yyyy-MM");

                    CostMaterialPriceMonth costM = new CostMaterialPriceMonth();
                    costM.setCompany(materialList.get(i).getCompany());
                    costM.setMaterialId(materialList.get(i).getMaterialId());
                    costM.setStartDate(startTime);

                    List<CostMaterialPriceMonth> costMaterList = new ArrayList<CostMaterialPriceMonth>();
                    // costMaterialPriceMonthDao.getPriceList(costM);
                    if (Collections3.isNotEmpty(costMaterList)) {
                        double price = StringUtils.toDouble(costMaterList.get(0).getPrice());
                        double priceStart = sto * price;
                        double priceEnd = snapsto * price;
                        materialList.get(i).setStockStartPrice("" + priceStart);
                        materialList.get(i).setStockEndPrice("" + priceEnd);
                    } else {
                        materialList.get(i).setStockStartPrice("" + 0);
                        materialList.get(i).setStockEndPrice("" + 0);
                    }
                }

            }
        }

        materialList = deal2AddSpec(materialList);
        page.setList(materialList);
        // addSpecForCompany();

        return page;
    }

    /**
     * 确认解除寄存 WC 2017-01-03
     *
     * @param material
     * @return
     */
    @Transactional(readOnly = false)
    public String convertToAvailableQty(Material material) {
        Material newMaterial = materialDao.findMaterialByNo(material);
        Integer depositQty = newMaterial.getDepositQty() == null ? 0 : newMaterial.getDepositQty();
        if (depositQty == 0) {
            return "操作无效：该物料已被解除寄存了!";
        }
        if (material.getConvertAvailableQty().compareTo(depositQty) == 1) {
            return "操作无效：您输入的数量大于寄存数量!";
        } else {
            // 更新寄存数量减少，可用库存数量增加，现库存数量不变，原因：现库存数量已经包含寄存数量
            material.preUpdate();
            materialDao.updateMaterialAvailableQty(material);

            // 保存解除寄存記錄
            MaterialDepositConvert mdc = new MaterialDepositConvert();
            mdc.setCompany(UserUtils.getUser().getCompany());
            mdc.setMaterial(newMaterial);
            mdc.setBefoDepositQty(depositQty); // 解除前寄存數量
            mdc.setQuantity(material.getConvertAvailableQty()); // 解除的寄存數量
            mdc.setCustomerNo(newMaterial.getCustomerNo());
            mdc.preInsert();
            mdcDao.insert(mdc);
        }
        return "解除寄存成功！";
    }

    /**
     * zjn 2017-06-12 获取所有的余料尺寸
     */
    public List<MaterialSpecification> getRemainSpcList(MaterialSpecification spec) {
        return materialSpecificationDao.getRemainSpcList(spec);
    }

    /**
     * zjn 2017-06-20 根据物料编号获得对应的长宽尺寸
     */
    public List<MaterialSpecification> getRemainSpcLengthWidth(Material material) {
        if (material.getCompany() == null) {
            material.setCompany(UserUtils.getUser().getCompany());
        }
        return materialSpecificationDao.getRemainSpcLengthWidth(material);
    }

    /**
     * zjn 2017-06-20 根据物料编号获得对应的尺寸
     */
    public List<MaterialSpecification> getRemainSpc(Material material) {
        List<MaterialSpecification> specs = materialSpecificationDao.getRemainSpc(material);
        if (specs != null && specs.size() > 0) {
            specs = materialSpecificationService.getValues(specs);
        }
        return specs;
    }

    /**
     * zjn 2017-07-28 查询不包括余料的物料集合
     */
    public List<Material> findListNoRemain(Material material) {
        List<Material> materList = materialDao.findListNoRemainTwo(material);
        RawStockUtil edu = new RawStockUtil();
        if (Collections3.isNotEmpty(materList)) {
            String materialIds = null;
            for (Material mat : materList) {
                if (StringUtils.isNotBlank(materialIds)) {
                    materialIds = materialIds + "," + mat.getRecordId();
                } else {
                    materialIds = mat.getRecordId();
                }
            }
            Material query = new Material();
            query.setCompany(material.getCompany());
            query.setRecordId(materialIds);
            List<Material> list = materialDao.getMaterialListByIds(query);
            list = edu.getMaterialList(list, null);
            if (Collections3.isNotEmpty(list)) {
                for (Material stocksMat : list) {
                    for (Material mat : materList) {
                        if (stocksMat.getRecordId().equals(mat.getRecordId())) {
                            mat.setStocks(stocksMat.getStocks());
                        }
                    }
                }
            }
        }
        return materList;
    }

    // tj 2018-07-30 获取云平台物料
    public List<Material> findMaterial() {
        List<Material> list = materialDao.findAll();
        return list;
    }

    /**
     * zjn 2018-07-24 物料管理中查找销售仓库
     */
    public List<StoreHouse> selectSalesStoreHouselist(Company company) {
        return storeHouseDao.selectSalesStoreHouselist(company);
    }

    /**
     * zjn 2018-09-05 验证物料名称和规格不能重复
     */
    public String validateMaterial(Material material) {
        String result = "";

        if (null == material) {
            result = "fail";
            return result;
        }

        material.setCompany(UserUtils.getUser().getCompany());
        Material materialCopy = materialDao.getMaterialByName(material);
        if (materialCopy != null && !materialCopy.getRecordId().equals(material.getRecordId())) {
            result = "failValidate";
            return result;
        }

        result = "success";
        return result;
    }

    /**
     * zjn 2018-09-07 修改物料销售仓，验证该物料的共享金额记录
     */
    public Map<String, String> selectSalesStoreHouse(Material material) {
        Map<String, String> data = new HashMap<>();

        if (null == material) {
            data.put("result", "fail");
            data.put("remark", "参数传递错误!");
        }

        // 根据仓库id和公司获取共享金额记录
        MaterialInternet materialInternet = new MaterialInternet();
        materialInternet.setCompany(UserUtils.getUser().getCompany());
        materialInternet.setStoreHouse(material.getSalesStoreHouse());
        List<MaterialInternet> miList = materialInternetDao.getMiByMaterial(materialInternet);

        if (Collections3.isNotEmpty(miList)) {
            Integer num = 0;
            for (MaterialInternet mi : miList) {
                materialInternet.setStoreHouse(null);
                materialInternet.setMaterial(material);

                // 存在共享公司
                if (null != mi.getShareCompany() && StringUtils.isNotBlank(mi.getShareCompany().getRecordId())) {
                    materialInternet.setShareCompany(mi.getShareCompany());
                    num = materialInternetDao.getMaterialInternetCount(materialInternet);
                    if (null != num && num > 0) {
                        Company shareCompany = companyDao.get(mi.getShareCompany());

                        data.put("result", "fail");
                        data.put("remark",
                                material.getNo() + "不能与修改后的" + material.getSalesStoreHouse().getName() + "同时向"
                                        + shareCompany.getName() + "设置金额，请重新选择销售仓库或者去物联网管理进行修改");
                        return data;
                    }

                }
                // 不存在共享公司
                else {
                    // 1.物料是对所有公司共享金额
                    num = materialInternetDao.getMaterialInternetCountAll(materialInternet);
                    if (null != num && num > 0) {
                        data.put("result", "fail");
                        data.put("remark",
                                "修改后" + material.getSalesStoreHouse().getName() + "已经对所有公司共享金额与" + material.getNo()
                                        + "与所有公司设置了金额产生冲突，请重新选择销售仓库或者去物联网管理进行修改");
                        return data;
                    }

                    // 2.物料是对某个公司共享金额
                    num = materialInternetDao.getMaterialInternetCount(materialInternet);
                    if (null != num && num > 0) {
                        data.put("result", "fail");
                        data.put("remark",
                                "修改后" + material.getSalesStoreHouse().getName() + "已经对所有公司共享金额与" + material.getNo()
                                        + "与某个公司设置了金额产生冲突，请重新选择销售仓库或者去物联网管理进行修改");
                        return data;
                    }
                }
            }
        }

        data.put("result", "success");
        return data;
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 查询所有原料改公司
     * @Date 16:02 2018-09-18
     * @Param
     **/
    public List<Material> findAllRaw(Material material) {

        return materialDao.findAllRawMaterial(material);
    }

    /**
     * 原料的分页页面
     *
     * @param qpage
     * @param material
     * @return
     */
    public Page<Material> findAllRawPage(Page<Material> qpage, Material material) {
        material.setPage(qpage);

        List<Material> materialList = materialDao.findAllRawMaterial(material);
        qpage.setList(materialList);
        return qpage;
    }

    /**
     * tj 2019-03-14 获取全部板材供应商
     */
    public List<String> findManufacturer(Company company) {
        return materialDao.findManufacturer(company);
    }

    public Map<String, Object> getMaterialPlaceList(Page<MaterialPlace> qpage, MaterialPlace materialPlace) {
        materialPlace.setPage(qpage);

        Company company = UserUtils.getUser().getCompany();
        Map<String, Object> map = new HashMap<String, Object>();
        materialPlace.setCompanyId(company.getRecordId());
        // 加载仓库信息
        StoreHouse storeHo = new StoreHouse();
        storeHo.setCompany(company);
        List<StoreHouse> storeList = storeHouseDao.findList(storeHo);
        map.put("storeList", storeList);
        if (StringUtils.isNotBlank(materialPlace.getStoreId())) {
            // 加载该仓库的所有横向坐标
            List<String> allLandscape = storeHouseDao.allLandscape(materialPlace);
            // 加载该仓库的所有纵向坐标
            List<String> allPortait = storeHouseDao.allPortait(materialPlace);
            List<String> allLineScape = storeHouseDao.allLineScape(materialPlace);
            map.put("allLandscape", allLandscape);
            map.put("allPortait", allPortait);
            map.put("allLineScape", allLineScape);
        }
        List<MaterialPlace> placeList = storeHouseDao.getMaterialPlaceList(materialPlace);
        qpage.setList(placeList);
        map.put("data", qpage);
        return map;
    }

    public Map<String, Object> getMaterialPlaceComList(Page<MaterialPlaceCom> qpage, MaterialPlaceCom materialPlaceCom) {
        materialPlaceCom.setPage(qpage);

        Company company = UserUtils.getUser().getCompany();
        Map<String, Object> map = new HashMap<String, Object>();
        materialPlaceCom.setCompanyId(company.getRecordId());
        List<MaterialPlaceCom> placeComList = storeHouseDao.getMaterialPlaceComList(materialPlaceCom);
        // 加载库位信息
        MaterialPlace materialPlace = new MaterialPlace();
        materialPlace.setCompanyId(company.getRecordId());

        // materialPlace.setMaterialKind(materialPlaceCom.getMaterialKind());
        // 成品不能增加库位物料管理，只有原料才能增加
        materialPlace.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_RAW.toString());
        List<MaterialPlace> placeList = storeHouseDao.getMaterialPlaceList(materialPlace);
        map.put("placeList", placeList);

        // 加载原材料信息
        Material material = new Material();
        material.setCompany(company);
        if (StringUtils.isNotBlank(materialPlaceCom.getMaterialKind())) {
            material.setMaterialKind(Integer.valueOf(materialPlaceCom.getMaterialKind()));
        }
        List<Material> materials = materialDao.findRawMaterial(material);
        // 加载出入库记录(库位需要加入时间条件)
        for (int i = 0; i < placeComList.size(); i++) {
            MaterialPlaceCom maCom = placeComList.get(i);

            // 功能变更，修改期初库存和期初时间为出入库的期初出入库
            maCom.setStocks(null);
            List<Material> maList = new ArrayList<Material>();
            for (int j = 0; j < materials.size(); j++) {
                if (materials.get(j).getStoreHouseId() != null && materials.get(j)
                        .getStoreHouseId()
                        .toString()
                        .equals(placeComList.get(i).getStoreId())) {
                    maList.add(materials.get(j));
                }
            }
            placeComList.get(i).setMaterials(maList);
        }
        qpage.setList(placeComList);
        map.put("data", qpage);
        // 加载子公司信息
        Branch branch = new Branch();
        branch.setCompany(company);
        List<Branch> branchs = branchDao.findList(branch);
        map.put("branchs", branchs);
        ParameterSet param = new ParameterSet();
        param.setCompany(company);
        param.setJianPin("initStorePlaceCom");
        // 查询参数设置中是否已经对该公司的库存盘点进行设置
        List<ParameterSet> params = parameterSetDao.findList(param);
        String result = "";
        if (params.size() > 0) {
            ParameterSet paramStock = params.get(0);
            result = paramStock.getParameterValue();
        }
        map.put("result", result);
        return map;
    }

    // 横向更新
    @Transactional(readOnly = false)
    public String updateLandscape(MaterialPlace materialPlace) {
        Company company = UserUtils.getUser().getCompany();
        materialPlace.setCompany(company);
        materialPlace.setCompanyId(company.getRecordId());
        if (StringUtils.isNotBlank(materialPlace.getPortait())) {
            List<String> allLandscape = storeHouseDao.allLandscape(materialPlace);
            for (int i = 0; i < allLandscape.size(); i++) {
                if (StringUtils.isNotBlank(allLandscape.get(i))) {
                    String name = allLandscape.get(i) + materialPlace.getPortait();
                    materialPlace.setName(name);
                    materialPlace.setLandscape(allLandscape.get(i));
                    // 验证库位名称是否可行
                    Integer count = storeHouseDao.isMaterialPlace(materialPlace);
                    if (count == 0) {
                        materialPlace.preInsert();
                        storeHouseDao.insertMaterialPlace(materialPlace);
                    }
                }
            }
        }
        return "success";
    }

    // 纵向更新
    @Transactional(readOnly = false)
    public String updatePortait(MaterialPlace materialPlace) {
        Company company = UserUtils.getUser().getCompany();
        materialPlace.setCompany(company);
        materialPlace.setCompanyId(company.getRecordId());
        if (StringUtils.isNotBlank(materialPlace.getLandscape())) {
            List<String> allPortait = storeHouseDao.allPortait(materialPlace);
            for (int i = 0; i < allPortait.size(); i++) {
                if (StringUtils.isNotBlank(allPortait.get(i))) {
                    String name = materialPlace.getLandscape() + allPortait.get(i);
                    materialPlace.setName(name);
                    materialPlace.setPortait(allPortait.get(i));
                    // 验证库位名称是否可行
                    Integer count = storeHouseDao.isMaterialPlace(materialPlace);
                    if (count == 0) {
                        materialPlace.preInsert();
                        storeHouseDao.insertMaterialPlace(materialPlace);
                    }
                }
            }
        }
        return "success";
    }

    // 纵向更新
    @Transactional(readOnly = false)
    public String updateAllNameList(MaterialPlace materialPlace) {
        Company company = UserUtils.getUser().getCompany();
        materialPlace.setCompany(company);
        materialPlace.setCompanyId(company.getRecordId());
        if (materialPlace.getNameList() != null && materialPlace.getNameList().size() > 0) {
            for (int i = 0; i < materialPlace.getNameList().size(); i++) {
                MaterialName name = materialPlace.getNameList().get(i);
                materialPlace.setName(name.getValue());
                materialPlace.setPortait(name.getPortait());
                materialPlace.setLandscape(name.getLandscape());
                materialPlace.setLineScape(name.getLineScape());
                // 验证库位名称是否可行
                Integer count = storeHouseDao.isMaterialPlace(materialPlace);
                if (count == 0) {
                    materialPlace.preInsert();
                    storeHouseDao.insertMaterialPlace(materialPlace);
                }
            }
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String insertMaterialPlace(MaterialPlace materialPlace) {
        Company company = UserUtils.getUser().getCompany();
        materialPlace.setCompany(company);
        materialPlace.setCompanyId(company.getRecordId());
        // 验证库位名称是否可行
        Integer count = storeHouseDao.isMaterialPlace(materialPlace);
        if (count > 0) {
            return "false";
        }
        if (StringUtils.isNotBlank(materialPlace.getRecordId())) {
            materialPlace.preUpdate();
            storeHouseDao.updateMaterialPlace(materialPlace);
        } else {
            materialPlace.preInsert();
            storeHouseDao.insertMaterialPlace(materialPlace);
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public Map<String, Object> insertMaterialPlaceCom(MaterialPlaceCom materialPlaceCom) {
        Map<String, Object> map = new HashMap<String, Object>();
        Company company = UserUtils.getUser().getCompany();
        materialPlaceCom.setCompany(company);
        materialPlaceCom.setCompanyId(company.getRecordId());
        // 验证库位、物料、下单公司是否唯一
        Integer count = storeHouseDao.isMaterialPlaceCom(materialPlaceCom);
        if (count > 0) {
            map.put("result", "false");
            return map;
        }
        if (StringUtils.isNotBlank(materialPlaceCom.getRecordId())) {
            materialPlaceCom.preUpdate();
            storeHouseDao.updateMaterialPlaceCom(materialPlaceCom);
        } else {
            materialPlaceCom.preInsert();
            storeHouseDao.insertMaterialPlaceCom(materialPlaceCom);
        }
        if (StringUtils.isNotBlank(materialPlaceCom.getStoreId())) {
            // 加载原材料信息
            Material material = new Material();
            material.setCompany(company);
            if (StringUtils.isNotBlank(materialPlaceCom.getMaterialKind())) {
                material.setMaterialKind(Integer.valueOf(materialPlaceCom.getMaterialKind()));
            }
            material.setStoreHouseId(Integer.valueOf(materialPlaceCom.getStoreId()));
            List<Material> materials = materialDao.findRawMaterial(material);
            map.put("materials", materials);
        }

        map.put("result", "success");
        return map;
    }

    public List<Material> getMaterialComList(MaterialPlaceCom materialPlaceCom) {
        Company company = UserUtils.getUser().getCompany();
        // 加载原材料信息
        Material material = new Material();
        material.setCompany(company);
        if (StringUtils.isNotBlank(materialPlaceCom.getMaterialKind())) {
            material.setMaterialKind(Integer.valueOf(materialPlaceCom.getMaterialKind()));
        }
        material.setStoreHouseId(Integer.valueOf(materialPlaceCom.getStoreId()));
        List<Material> materials = materialDao.getMaterialQueryList(material);
        return materials;
    }

    @Transactional(readOnly = false)
    public String deleteMaterialPlace(MaterialPlace materialPlace) {
        // 验证该库位是否有被使用
        Integer count = storeHouseDao.isMaterialPlaceComExit(materialPlace);
        if (count > 0) {
            return "false";
        }
        storeHouseDao.deleteMaterialPlace(materialPlace);
        return "success";
    }

    @Transactional(readOnly = false)
    public String deleteMaterialPlaceCom(MaterialPlaceCom materialPlaceCom) {
        // 验证该库位关联是否有采购信息或者库存信息
        storeHouseDao.deleteMaterialPlaceCom(materialPlaceCom);
        return "success";
    }

    public List<Material> getMaterialList(Material material) {
        return materialDao.getMaterialByCraftTwo(material);
    }

    @Transactional(readOnly = false)
    public String initStorePlaceCom() {
        Company company = UserUtils.getUser().getCompany();
        ParameterSet param = new ParameterSet();
        param.setCompany(company);
        param.setJianPin("initStorePlaceCom");
        // 查询参数设置中是否已经对该公司的库存盘点进行设置
        String result = "";
        List<ParameterSet> params = parameterSetDao.findList(param);
        if (params.size() > 0) {
            ParameterSet paramStock = params.get(0);
            if (StringUtils.isNotBlank(paramStock.getParameterValue())) {
                paramStock.setParameterValue(null);
                // 更新每个库位的期初时间
                company.setInveDate(new Date());
                materialDao.updateStockPlaceComInveDate(company);
            } else {
                paramStock.setParameterValue("1");
                result = "1";
            }
            parameterSetDao.update(paramStock);
        } else {
            // 插入备料提前期
            ParameterSet paramStock = new ParameterSet();
            paramStock.setCompany(company);
            paramStock.preInsert();
            paramStock.setJianPin("initStorePlaceCom");
            paramStock.setParameterName("库位物料初始化");
            paramStock.setParameterValue("1");
            result = "1";
            paramStock.setUnit(" ");
            parameterSetDao.insert(paramStock);
        }
        return result;
    }

    public List<Material> getMaterialQueryList(Material material) {
        if (material.getMaterialKind() == 100702) {
            return materialDao.getMaterialQueryTwoList(material);
        }
        return materialDao.getMaterialQueryList(material);
    }

    public List<MaterialPlace> getMaterialPlaceList(MaterialPlace materialPlace) {
        return storeHouseDao.getMaterialPlaceList(materialPlace);
    }

    @Transactional(readOnly = false)
    public String updateMaterialPlaceComStatus(MaterialPlaceCom materialPlaceCom) {
        materialPlaceCom.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        // 按照销售公司、物料、库位更新所有该物料库位上的默认状态
        storeHouseDao.updateMaterialPlaceComStatusAll(materialPlaceCom);
        // 设置该库位为默认
        storeHouseDao.updateMaterialPlaceComStatus(materialPlaceCom);
        return "success";
    }

    @Transactional(readOnly = false)
    public String updateStockRestQty(Material material) {
        BigDecimal stocks = null == material.getStocks() ? BigDecimal.ZERO : material.getStocks();
        BigDecimal deliveryOaQty =
                null == material.getDeliveryOaQty() ? BigDecimal.ZERO : new BigDecimal(material.getDeliveryOaQty());
        BigDecimal availableQty = null == material.getAvailableQty() ? BigDecimal.ZERO : material.getAvailableQty();
        if (stocks.compareTo(deliveryOaQty) < 0) {
            deliveryOaQty = stocks;
        } else {
            availableQty = stocks.subtract(deliveryOaQty);
        }
        material.setDeliveryOaQty(deliveryOaQty.intValue());
        material.setAvailableQty(availableQty);
        materialDao.updateStocksData(material);
        return "success";
    }

    @Transactional(readOnly = false)
    public List<Material> findCopyDetailList(Material material) {
        List<Material> materialList = new ArrayList<Material>();
        if (StringUtils.isNotBlank(material.getSnapshotDate())) {
            materialList = materialDao.materList(material);
            for (int i = 0; i < materialList.size(); i++) {
                double snapsto = StringUtils.toDouble(materialList.get(i).getSnapStrocks());
                double sto = StringUtils.toDouble(materialList.get(i).getStocks());
                String result = "";
                if (snapsto > sto) {
                    double stockResult = snapsto - sto;
                    result = "库存上调了" + stockResult;
                }
                if (snapsto < sto) {
                    double stockResult = sto - snapsto;
                    result = "库存下调了" + stockResult;
                }
                materialList.get(i).setStockChange(result);

                if (materialList.get(i).getMaterialKind() == 100701) {
                    // 当前时间减一个月
                    Calendar c = Calendar.getInstance();
                    c.add(Calendar.MONTH, -1);
                    Date time = c.getTime();
                    String startTime = DateUtils.formatDate(time, "yyyy-MM");

                    CostMaterialPriceMonth costM = new CostMaterialPriceMonth();
                    costM.setCompany(materialList.get(i).getCompany());
                    costM.setMaterialId(materialList.get(i).getMaterialId());
                    costM.setStartDate(startTime);

                    List<CostMaterialPriceMonth> costMaterList = new ArrayList<CostMaterialPriceMonth>();
                    // costMaterialPriceMonthDao.getPriceList(costM);
                    if (Collections3.isNotEmpty(costMaterList)) {
                        double price = StringUtils.toDouble(costMaterList.get(0).getPrice());
                        double priceStart = sto * price;
                        double priceEnd = snapsto * price;
                        materialList.get(i).setStockStartPrice("" + priceStart);
                        materialList.get(i).setStockEndPrice("" + priceEnd);
                    } else {
                        materialList.get(i).setStockStartPrice("" + 0);
                        materialList.get(i).setStockEndPrice("" + 0);
                    }
                }

            }
        }

        materialList = deal2AddSpec(materialList);
        return materialList;
    }

    public Page<WaitingMaterialVo> findWaitingMaterialPage(Page<WaitingMaterialVo> qpage, WaitingMaterialVo vo) {
        // vo.setPage(qpage);
        if (qpage != null) {
            vo.setOrderBy(qpage.getOrderBy());
        }
        List<WaitingMaterialVo> resultList = new ArrayList<>();
        String factoryComId = CompanyUtil.getInstance().getFactId();
        vo.setFactoryComId(factoryComId);
        if (!factoryComId.equals(UserUtils.getUser().getCompany().getRecordId())) {
            vo.setXsCompanyId(UserUtils.getUser().getCompany().getRecordId());
        }
        List<WaitingMaterialVo> list = materialDao.findWaitingMaterialList(vo);
        if (!CollectionUtils.isEmpty(list)) {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            Date currDate;
            try {
                currDate = sf.parse(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
                // 查下 1、末入库数量 2、延期天数
                for (WaitingMaterialVo wa : list) {
                    if ("Y".equals(vo.getStoreStatus()) && !checkStoreStatus(wa)) {
                        continue;
                    } else if ("N".equals(vo.getStoreStatus()) && checkStoreStatus(wa)) {
                        continue;
                        // 有库存
                    }

                    wa.setPlanFeedDate(sf.parse(wa.getEndPurchDateTwoStr()));
                    if (currDate.compareTo(sf.parse(wa.getEndPurchDateTwoStr())) > 0) {
                        wa.setWaitingType("投料延期");
                        wa.setDelayDay(DateUtils.getDays(wa.getPlanFeedDate(), new Date()) + "");
                    } else {
                        wa.setWaitingType("投料末到期");
                    }
                    if (!StringUtils.isEmpty(wa.getFeedNo())) {
                        wa.setWaitingStatus("投料未出库");
                    } else if (StringUtils.isEmpty(wa.getApplyNo()) || TypeKey.PU_PURCHASING_STATUS_ALREADY_IN.toString()
                            .equals(wa.getApplyStatus()) || (wa.getPurQty() != null && wa.getStoreQty() != null
                            && wa.getPurQty().equals(wa.getStoreQty()))) {
                        wa.setWaitingStatus("库存未投料");
                    } else if (!StringUtils.isEmpty(wa.getPurNo())) {
                        wa.setWaitingStatus("采购未入库");
                    } else {
                        wa.setWaitingStatus("申请未采购");
                    }
                    if (!StringUtils.isEmpty(wa.getMaterialName())) {
                        String[] names = wa.getMaterialName().split(" ");
                        wa.setMaterialSupplier(names[0]);
                        if (names.length > 1) {
                            wa.setMaterialFutong(names[1]);
                        }
                    }
                    if (!StringUtils.isEmpty(wa.getFeedNo()) && wa.getFeedBoardQuantity() != null
                            && wa.getFeedBoardQuantity() > 0) {
                        if (wa.getOutBoardQuantity() != null) {
                            wa.setUnOutBoardQuantity(wa.getFeedBoardQuantity() - wa.getOutBoardQuantity());
                        } else {
                            wa.setUnOutBoardQuantity(wa.getFeedBoardQuantity());
                        }

                        if (wa.getUnOutBoardQuantity() > 0) {
                            resultList.add(wa);
                        }
                    } else {
                        resultList.add(wa);
                    }
                    //
                    if (!StringUtils.isEmpty(wa.getPurNo())) {
                        wa.setHasPurStatus("是");
                    } else {
                        wa.setHasPurStatus("否");
                    }

                    //增加材料占用情况
                    //addMaterailInfo(wa);

                }
            } catch (ParseException e) {
                e.printStackTrace();
            }

        }
        qpage.setList(resultList);
        return qpage;
    }

    /**
     * 有库存返回true 采购中返回false
     *
     * @param vo
     * @return
     */
    private boolean checkStoreStatus(WaitingMaterialVo wa) {
        if (!StringUtils.isEmpty(wa.getApplyNo())) {
            if (wa.getPurQty() != null && wa.getStoreQty() != null && wa.getPurQty().equals(wa.getStoreQty())) {
                // 采购数量等于入库数量返回
                return true;
            }
            return false;
        } else {
            return true;
        }
    }

    public List<Material> getMatCardList(Notification notification) {
        notification.setCompany(UserUtils.getUser().getCompany());
        return materialDao.getMatCardList(notification);
    }

    public List<MaterialSpecification> getMaterialSpecificationList(String companyId) {
        return materialDao.getMaterialSpecificationListTwo(companyId);
    }

    public Integer checkExistMaterial(@Param("specifications") List<String> specifications,
                                      @Param("companyId") String companyId) {
        return materialDao.checkExistMaterial(specifications, companyId);
    }

    public List<Material> addMaterialItems(Material material,Company company)
    {
        material.setCompany(company);
        if (StringUtils.isNotBlank(material.getNo()))
        {
            String[] nos = material.getNo().split(" ");
            material.setCondition1(null);
            material.setCondition2(null);
            material.setCondition3(null);
            material.setCondition4(null);
            material.setCondition5(null);
            material.setCondition6(null);
            material.setCondition7(null);
            material.setCondition8(null);
            if (null != nos && nos.length > 0)
            {
                for (int i = 0; i < nos.length; i++)
                {
                    String condition = nos[i];
                    switch (i)
                    {
                        case 0:
                            material.setCondition1(condition);
                            break;
                        case 1:
                            material.setCondition2(condition);
                            break;
                        case 2:
                            material.setCondition3(condition);
                            break;
                        case 3:
                            material.setCondition4(condition);
                            break;
                        case 4:
                            material.setCondition5(condition);
                            break;
                        case 5:
                            material.setCondition6(condition);
                            break;
                        case 6:
                            material.setCondition7(condition);
                            break;
                        case 7:
                            material.setCondition8(condition);
                            break;
                    }
                }
            }
        }
        if(null == material.getPageNo())
        {
            material.setPageNo("0");
        }
        if(null == material.getPageSize())
        {
            material.setPageSize("50");
        }
        List<Material> materialList = materialDao.getApplyMatListTwo(material);
        RawStockUtil rawUtil = new RawStockUtil();
        List<Material> matList = rawUtil.getMaterialList(materialList, null);
        return matList;
    }

    /**
     * 保存物料库位
     * @param material 物料对象
     */
    public void saveMaterialChildcompany(Material material)
    {
        // 更新物料的库位(添加/删除)
        if(Collections3.isNotEmpty(material.getMaterialPlaceComList()))
        {
            List<MaterialPlaceCom> insertList = Lists.newArrayList();

            // 获取当前物料的库位列表
            Material mat = new Material();
            mat.setRecordId(material.getRecordId());
            mat.setCompanyId(material.getCompany().getRecordId());
            List<MaterialPlaceCom> mpcList = storeHouseDao.getMaterialPlaceComByMaterId(mat);
            if(Collections3.isNotEmpty(mpcList))
            {
                for(MaterialPlaceCom mpcOne : material.getMaterialPlaceComList())
                {
                    Boolean flag = false;
                    for(MaterialPlaceCom mpcTwo : mpcList)
                    {
                        if(mpcOne.getMaterPlaceId().equals(mpcTwo.getMaterPlaceId()))
                        {
                            flag = true;
                        }
                    }
                    if(!flag)
                    {
                        insertList.add(mpcOne);
                    }
                }
            }
            else
            {
                insertList = material.getMaterialPlaceComList();
            }
            // 添加
            material.preInsert();
            material.setMaterialPlaceComList(insertList);
            storeHouseDao.batchInsertMaterialPlaceCom(material);

            // 删除
            String deleteIds = null;
            for(MaterialPlaceCom mpcOne : mpcList)
            {
                if(mpcOne.getUseFlag())
                {
                    continue;
                }
                Boolean flag = false;
                for(MaterialPlaceCom mpcTwo : material.getMaterialPlaceComList())
                {
                    if(mpcOne.getMaterPlaceId().equals(mpcTwo.getMaterPlaceId()))
                    {
                        flag = true;
                    }
                }
                if(!flag)
                {
                    if(StringUtils.isNotBlank(deleteIds))
                    {
                        deleteIds = deleteIds +","+ mpcOne.getRecordId();
                    }
                    else
                    {
                        deleteIds = mpcOne.getRecordId();
                    }
                }
            }
            if(StringUtils.isNotBlank(deleteIds))
            {
                storeHouseDao.batchDeleteMaterialPlaceCom(deleteIds);
            }
        }
    }
    public String selectToPrint(Material material, String realPath)
    {
        if(null == material || StringUtils.isBlank(material.getRecordId()))
        {
            return null;
        }
        String companyId = UserUtils.getUser().getCompany().getRecordId();
        // 根据公司id获取集团管控id
        String groupId = companyDao.getRecordId(companyId);
        if(StringUtils.isBlank(groupId))
        {
            return null;
        }
        String content = "A"+groupId+"B"+companyId+"C"+material.getRecordId();

        String srialnum = null;
        Material mat = get(material);
        if(null != mat)
        {
            if(StringUtils.isNotBlank(mat.getName()) && StringUtils.isNotBlank(mat.getSpecification()))
            {
                srialnum = mat.getSpecification() +"-"+ mat.getName();
            }
            else if(StringUtils.isNotBlank(mat.getName()))
            {
                srialnum = mat.getName();
            }
            else if(StringUtils.isNotBlank(mat.getSpecification()))
            {
                srialnum = mat.getSpecification();
            }
        }

        // 条形码
        int width3 = 300, height3 = 360;
        // 组合二维码内容(集团管控id + 公司id + 物料id)
        String name = "qcCode";
        String savePath = realPath + "assets/global/img/card/" + name + ".png";
/*        ZxingHandler.encode2(content, width3, height3, realPath + "assets/global/img/card/" + name + ".png");*/
//        QRCodeUtil.getQRCode(content,srialnum,savePath,width3,height3);
        String url = realPath + "assets/global/img/card/" + name + ".png";
        return url;
    }

    public List<Material> handleMatSpecification()
    {
        Material mat = new Material();
        mat.setCompany(UserUtils.getUser().getCompany());
        List<Material> materialList = materialDao.safetyStockList(mat);
        materialList = deal2AddSpec(materialList);// 添加原料规格集合和规格数据集合
        return materialList;
    }

    @Transactional(readOnly = false)
    public String updateMatSpecification(List<Material> list)
    {
        materialDao.batchUpdateTwo(list);
        return "success";
    }

    public Material get(Material material)
    {
        return materialDao.get(material);
    }

    // 获取参数设置的库存盘点开关值
    public String getParameterSet(Company company, String jianPin){
        ParameterSet ps = new ParameterSet();
        ps.setCompany(company);
        ps.setJianPin(jianPin);
        return null == parameterSetDao.getParameterSetByCompanyAndjianPin(ps) ? null : parameterSetDao.getParameterSetByCompanyAndjianPin(ps).getParameterValue();
    }

    public List<Material> findMaterialList(String manufacturerId){
        if (null == manufacturerId){
            return null;
        }
        Material material = new Material();
        material.setCompany(UserUtils.getUser().getCompany());
        material.setManufacturerId(manufacturerId);
        return materialDao.getApplyMatListTwo(material);
    }

    @Transactional(readOnly = false)
    public String addBoardSupermarket(Material material)
    {
        if (null != material)
        {
            // 获取物料结余
            RawStockUtil rawStockUtil = new RawStockUtil();
            BigDecimal stocks = rawStockUtil.getStockPlaceComStocks(null, material.getRecordId());
            if (null != stocks && stocks.compareTo(material.getSupermarketQuantity()) < 0)
            {
                return "板材超市数量不能大于剩余数量!";
            }
            else
            {
                materialDao.updateSupermarket(material);
                return "上架成功!";
            }
        }
        else
        {
            return "数据错误，请刷新重试";
        }
    }

    @Transactional(readOnly = false)
    public String delistSupermarket(Material material)
    {
        if (null != material)
        {
            materialDao.delistSupermarket(material);
            return "下架成功!";
        }
        else
        {
            return "数据错误，请刷新重试";
        }
    }

    public List<Material> getSupermarketMaterialList(Material material)
    {
        if (null == material || StringUtils.isBlank(material.getCompanyId()))
        {
            return null;
        }
        if (StringUtils.isNotBlank(material.getSpecification()))
        {
            String[] specifications = material.getSpecification().split(" ");
            material.setCondition1(null);
            material.setCondition2(null);
            material.setCondition3(null);
            material.setCondition4(null);
            material.setCondition5(null);
            material.setCondition6(null);
            if (null != specifications && specifications.length > 0)
            {
                for (int i = 0; i < specifications.length; i++)
                {
                    String condition = specifications[i];
                    switch (i)
                    {
                        case 0:
                            material.setCondition1(condition);
                            break;
                        case 1:
                            material.setCondition2(condition);
                            break;
                        case 2:
                            material.setCondition3(condition);
                            break;
                        case 3:
                            material.setCondition4(condition);
                            break;
                        case 4:
                            material.setCondition5(condition);
                            break;
                        case 5:
                            material.setCondition6(condition);
                            break;
                    }
                }
            }
        }
        List<Material> supermarketMaterialList =  materialDao.getSupermarketMaterialList(material);
        return supermarketMaterialList;
    }
}
