package com.kyb.pcberp.modules.icloud.erp.approval.pojo;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.base.pojo.Icloud_BasePojo;
import com.kyb.pcberp.modules.icloud.erp.pojo.Icloud_Company;
import com.kyb.pcberp.modules.icloud.sys.pojo.Icloud_User;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Icloud_Approval extends Icloud_BasePojo
{
    private static final long serialVersionUID = 1L;

    private Icloud_Company company;

    private String no; // 编号

    private Icloud_Allocation allocation; // 审批配置

    private Icloud_ApprovalType type; // 审批类型

    private Icloud_Backups backups; // 备份数据

    private String num; // 编号数据

    private String date; // 编号日期

    private String dataId; // 修改源Id

    private String dataNo; // 修改源编号

    private String status; // 审批状态

    private String approvalBy; // 审批人

    private Icloud_User user;

    private String butFalg;

    private String userName; // 审批人名称

    private String examine; // 审核

    private String opinion; // 处理意见

    private String framework; // 组织架构

    private String updateLog; // 修改日志

    private List<Icloud_AllocationRole> allocationRoleList = Lists.newArrayList(); // 审批角色

    private List<Icloud_CraftChange> craftChangeList = Lists.newArrayList(); // 工艺改动

    private String frontStatus; // 提交审批前状态

    private String icloud; // 云平台标记

    private Map<String, String> map = new HashMap<>(); // 附件

    private String flag;// 1 是合同， 2 是合同明细， 3是采购，4是采购明细,5是原料采购

    private String jsonString;// 字典值

    private List<Icloud_Backups> backupsList;

    private String icloudContractId; // 云平台合同ID

    private String contractId; // 合同Id

    private String dataDetailId;

    private String customerType; // 同一个流程配置，区分不同审批操作

    private String companyId;

    private String typeId;

    private String deptId;

    private String deptQuery;

    public Icloud_Company getCompany()
    {
        return company;
    }

    public void setCompany(Icloud_Company company)
    {
        this.company = company;
    }

    public String getNo()
    {
        return no;
    }

    public void setNo(String no)
    {
        this.no = no;
    }

    public Icloud_Allocation getAllocation()
    {
        return allocation;
    }

    public void setAllocation(Icloud_Allocation allocation)
    {
        this.allocation = allocation;
    }

    public Icloud_ApprovalType getType()
    {
        return type;
    }

    public void setType(Icloud_ApprovalType type)
    {
        this.type = type;
    }

    public Icloud_Backups getBackups()
    {
        return backups;
    }

    public void setBackups(Icloud_Backups backups)
    {
        this.backups = backups;
    }

    public String getNum()
    {
        return num;
    }

    public void setNum(String num)
    {
        this.num = num;
    }

    public String getDate()
    {
        return date;
    }

    public void setDate(String date)
    {
        this.date = date;
    }

    public String getDataNo()
    {
        return dataNo;
    }

    public void setDataNo(String dataNo)
    {
        this.dataNo = dataNo;
    }

    public List<Icloud_AllocationRole> getAllocationRoleList()
    {
        return allocationRoleList;
    }

    public void setAllocationRoleList(List<Icloud_AllocationRole> allocationRoleList)
    {
        this.allocationRoleList = allocationRoleList;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getApprovalBy()
    {
        return approvalBy;
    }

    public void setApprovalBy(String approvalBy)
    {
        this.approvalBy = approvalBy;
    }

    public Icloud_User getUser()
    {
        return user;
    }

    public void setUser(Icloud_User user)
    {
        this.user = user;
    }

    public String getButFalg()
    {
        return butFalg;
    }

    public void setButFalg(String butFalg)
    {
        this.butFalg = butFalg;
    }

    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    public String getExamine()
    {
        return examine;
    }

    public void setExamine(String examine)
    {
        this.examine = examine;
    }

    public String getOpinion()
    {
        return opinion;
    }

    public void setOpinion(String opinion)
    {
        this.opinion = opinion;
    }

    public String getDataId()
    {
        return dataId;
    }

    public void setDataId(String dataId)
    {
        this.dataId = dataId;
    }

    public String getFramework()
    {
        return framework;
    }

    public void setFramework(String framework)
    {
        this.framework = framework;
    }

    public List<Icloud_CraftChange> getCraftChangeList()
    {
        return craftChangeList;
    }

    public void setCraftChangeList(List<Icloud_CraftChange> craftChangeList)
    {
        this.craftChangeList = craftChangeList;
    }

    public String getUpdateLog()
    {
        return updateLog;
    }

    public void setUpdateLog(String updateLog)
    {
        this.updateLog = updateLog;
    }

    public String getFrontStatus()
    {
        return frontStatus;
    }

    public void setFrontStatus(String frontStatus)
    {
        this.frontStatus = frontStatus;
    }

    public String getIcloud()
    {
        return icloud;
    }

    public void setIcloud(String icloud)
    {
        this.icloud = icloud;
    }

    public Map<String, String> getMap()
    {
        return map;
    }

    public void setMap(Map<String, String> map)
    {
        this.map = map;
    }

    public String getFlag()
    {
        return flag;
    }

    public void setFlag(String flag)
    {
        this.flag = flag;
    }

    public String getJsonString()
    {
        return jsonString;
    }

    public void setJsonString(String jsonString)
    {
        this.jsonString = jsonString;
    }

    public List<Icloud_Backups> getBackupsList()
    {
        return backupsList;
    }

    public void setBackupsList(List<Icloud_Backups> backupsList)
    {
        this.backupsList = backupsList;
    }

    public String getIcloudContractId()
    {
        return icloudContractId;
    }

    public void setIcloudContractId(String icloudContractId)
    {
        this.icloudContractId = icloudContractId;
    }

    public String getContractId()
    {
        return contractId;
    }

    public void setContractId(String contractId)
    {
        this.contractId = contractId;
    }

    public String getDataDetailId()
    {
        return dataDetailId;
    }

    public void setDataDetailId(String dataDetailId)
    {
        this.dataDetailId = dataDetailId;
    }

    public String getCustomerType()
    {
        return customerType;
    }

    public void setCustomerType(String customerType)
    {
        this.customerType = customerType;
    }

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    public String getTypeId()
    {
        return typeId;
    }

    public void setTypeId(String typeId)
    {
        this.typeId = typeId;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public String getDeptQuery()
    {
        return deptQuery;
    }

    public void setDeptQuery(String deptQuery)
    {
        this.deptQuery = deptQuery;
    }
}
