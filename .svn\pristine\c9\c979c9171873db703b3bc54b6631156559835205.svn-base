<%@ page contentType="text/html;charset=UTF-8" %>

<div ng-intro-options="billListOptions" ng-intro-method="helpBillList" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="billDetailOptions" ng-intro-method="helpBillDetail" ng-intro-autostart="shouldAutoStart"></div>

<div ng-intro-options="adjustOptions" ng-intro-method="helpAdjust" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="adjustReadOnlyOptions" ng-intro-method="helpReadOnlyAdjust" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="adjustNoQryOptions" ng-intro-method="helpNoQryAdjust" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="adjustNoQryReadOnlyOptions" ng-intro-method="helpNoQryReadOnlyAdjust" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="infoOptions" ng-intro-method="infoOptionsShow" ng-intro-autostart="shouldAutoStart"></div>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">采购管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="purch.payable">付款对账单</a>
        </li>
    </ul>
    <div class="page-toolbar">
    	<button class="btn default btn-fit-height pull-right" ng-click="help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>
</div>
<!-- END 导航-->

<tabset class="tabset-margin-top">
    <!-- BEGIN 对账单-->
    <tab heading="对账单" active="ctrl.tabs.rForm.active" ng-click="ctrl.loadOutSourceData(1)">
        <div class="rows">
            <div id="billStep6" class="panel panel-default">
                <div class="panel-heading font-blue-hoki">查询</div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商编号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="ctrl.query.supplier.no.value"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商名称：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="ctrl.query.supplier.name.value"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">对账月份：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                        	   placeholder="请输入对账月份，年/月/年月"
                                               ng-model="ctrl.query.period.value"
                                               ng-blur="ctrl.loadPeriodList(1)"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
							    <div class="form-group">
								    <label class="col-sm-3 col-md-4 control-label">状态：</label>
								    <div class="col-sm-7 col-md-8">
									     <select  class="form-control"
	                                    		 ng-model="ctrl.query.status.value"
	                                             ng-options="gen.value as gen.name for gen in ctrl.query.statusList"
	                                             disable-auto-validate="true">
	                                    </select>
								    </div>
							    </div>
							</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default btn-default-width pull-right" ng-click="ctrl.doQuery()">
                                    <i class="fa fa-search"></i> 查询
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">对账单列表</div><div class="caption" ng-if="ctrl.showTotalFlag">&nbsp;&nbsp;&nbsp;&nbsp;未付金额:{{ctrl.totalRcvAmount | number:3}}</div>
                    <div id="billStep7" class="actions">
                        <div class="portlet-input input-inline input-small">
                            <button type="button" class="btn btn-primary btn-default-width"
                                    ng-click="ctrl.queryAdjust()"><i class="fa fa-search-minus"></i> 查询调整
                            </button>
                        </div>
                        <div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
                            <button type="button" class="btn green btn-default-width" ng-click="ctrl.addAdjust(1)"><i
                                    class="fa fa-plus"></i> 新增调整
                            </button>
                        </div>
                        <div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
                            <button type="button" class="btn green btn-default-width" ng-click="ctrl.addAdjust(2)"><i
                                    class="fa fa-plus"></i> 新增明细调整
                            </button>
                        </div>
                        <div class="portlet-input input-inline input-small">
	                        <form action="a/purch/payable/exportPayable" method="POST" enctype="multipart/form-data" target="hidden_frame">
	                            <input type="text" ng-show="false"  name="supplier.no" value="{{ctrl.query.supplier.no.value}}"/>
	                            <input type="text" ng-show="false"  name="supplier.name" value="{{ctrl.query.supplier.name.value}}"/>
	                            <input type="text" ng-show="false"  name="period" value="{{ctrl.query.period.value}}"/>
	                            <input type="text" ng-show="false"  name="taxName" value="{{ctrl.taxQuery}}"/>
	                            <input type="text" ng-show="false" name="status" value="{{ctrl.query.status.value}}"/>
                              	<input type="text" ng-show="false" name="dateType" value="{{ctrl.dateType}}"/>
	                            <input type="text" ng-show="false"  name="queryAll" value="{{ctrl.queryAll}}"/>
	                            <input type="text" ng-show="false"  name="lnCompanyId" value="{{ctrl.lnCompanyId}}"/>
	                            <div ng-if="ctrl.right.view" >
	                                <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i>导出付款对账单</button>
	                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
	                            </div>
	                        </form>
                    	</div>

                    	<div class="portlet-input input-inline input-small">
	                        <form action="a/purch/payable/exportPayableAllDetail" method="POST" enctype="multipart/form-data" target="hidden_frame">
	                            <input type="text" ng-show="false"  name="supplier.no" value="{{ctrl.query.supplier.no.value}}"/>
	                            <input type="text" ng-show="false"  name="supplier.name" value="{{ctrl.query.supplier.name.value}}"/>
	                            <input type="text" ng-show="false"  name="period" value="{{ctrl.query.period.value}}"/>
	                            <input type="text" ng-show="false"  name="taxName" value="{{ctrl.taxQuery}}"/>
	                            <input type="text" ng-show="false" name="status" value="{{ctrl.query.status.value}}"/>
                              	<input type="text" ng-show="false" name="dateType" value="{{ctrl.dateType}}"/>
	                            <input type="text" ng-show="false"  name="queryAll" value="{{ctrl.queryAll}}"/>
	                            <input type="text" ng-show="false"  name="lnCompanyId" value="{{ctrl.lnCompanyId}}"/>
	                            <div ng-if="ctrl.right.view" >
	                                <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i>导出付款对账单明细</button>
	                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
	                            </div>
	                        </form>
                    	</div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div id="billStep1" class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading">
                                <th id="billStep2" ng-class="{'sorting': ctrl.sort.supplierNo.both, 'sorting_desc': ctrl.sort.supplierNo.desc, 'sorting_asc': ctrl.sort.supplierNo.asc}"
                                			ng-click="ctrl.sortClick('supplierNo')">供应商编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                            <th ng-class="{'sorting': ctrl.sort.supplierName.both, 'sorting_desc': ctrl.sort.supplierName.desc, 'sorting_asc': ctrl.sort.supplierName.asc}"
	                            			ng-click="ctrl.sortClick('supplierName')">供应商名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th ng-class="{'sorting': ctrl.sort.period.both, 'sorting_desc': ctrl.sort.period.desc, 'sorting_asc': ctrl.sort.period.asc}"
                                			ng-click="ctrl.sortClick('period')">对账月份&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>本期货款</th>
                                <th>内部货款</th>
                            	<th>外协货款</th>
                            	<th>账单属性</th>
                                <th id="billStep3">调整金额</th>
                                <th>发票</th>
                                <th>状态</th>
                                <th>本期已付</th>
                                <th>本期未付</th>
                                <th>逾期天数</th>
                                <th>平米数</th>
                                <th>PCS数</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in ctrl.page.r.data.list" ng-dblclick="ctrl.disRDetail($index)" ng-click="ctrl.showRDetailTotal($index)" ng-class="{'danger':!row.rcvFlag,'info':row.statusFlag}" ng-if="row.adjustFlag">
                                <td><a ng-click="ctrl.disRDetail($index)">{{row.supplier.no}}</a></td>
                                <td ng-bind="row.supplier.name"></td>
                                <td ng-bind="row.period"></td>
                                <td class="text-right" ng-bind="row.materialValue | currency:'':2"></td>
                                <td class="text-right" ng-bind="row.jxAmount | currency:'':2"></td>
                            	<td class="text-right" ng-bind="row.otherAmount | currency:'':2"></td>
                            	<td ng-bind="row.materialProperty"></td>
                                <td class="text-right"><a href="#" ng-click="ctrl.queryAdjust($index)">{{row.adjustValue | currency:'':2}}</a></td>
                                <td ng-if="row.invoiceFlag=='1'" style="color: #b53232" >有</td>
                                <td ng-if="!row.invoiceFlag || row.invoiceFlag=='2'">无</td>
	                            <td ng-if="row.status == 2001">未确认</td>
	                            <td ng-if="row.status == 2002">已确认</td>
	                            <td ng-if="row.status == 2003">已完成</td>
	                            <td ng-if="row.status == 2004">未复核</td>
	                            <td ng-if="row.status == 2005">已复核</td>
	                            <td ng-if="row.status == 60001">审批中</td>
                                <td class="text-right" ng-bind="row.paiedAmount | currency:'':2"></td>
                                <td class="text-right" ng-bind="row.rcvAmount | currency:'':2"></td>
                                <td class="text-right" ng-bind="row.overDueDaysStr"></td>
                                <td class="text-right" ng-bind="row.area"></td>
                                <td class="text-right" ng-bind="row.quantity"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row page-margin-top">
                        <div id="billStep5" class="col-md-12 col-lg-6">
                            <span class="inline">每页</span>
                            <select class="form-control inline" style="margin-top:8px; width:100px;"
                                    disable-valid-styling="true"
                                    disable-invalid-styling="true"
                                    ng-model="ctrl.page.r.pageSize"
                                    ng-change="ctrl.pageSizeChange(0)"
                                    ng-options="option for option in ctrl.page.options">
                            </select>
                            <span class="inline">条详情&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{ctrl.page.r.data.startCount}} / {{ctrl.page.r.data.endCount}} 条，共 {{ctrl.page.r.data.count}} 条</span>
                        </div>
                        <div id="billStep4" class="col-md-12 col-lg-6">
                            <paging
                                    class="pull-right"
                                    page="ctrl.page.r.data.pageNo"
                                    page-size="ctrl.page.r.data.pageSize"
                                    total="ctrl.page.r.data.count"
                                    adjacent="1"
                                    dots="..."
                                    scroll-top="false"
                                    hide-if-empty="false"
                                    ul-class="pagination"
                                    active-class="active"
                                    disabled-class="disabled"
                                    show-prev-next="true"
                                    paging-action="ctrl.doPage(0, page, pageSize)">
                            </paging>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <ul id="receStep8" class="list-inline">
            <li><a class="list-group-item list-group-item-info btn-default-width" ng-click="ctrl.filterBy(2003)">已完成</a></li>
        </ul>
    </tab>
    <!-- END 对账单 -->
    <!-- BEGIN 对账单详情 -->
    <tab active="ctrl.tabs.rDForm.active" ng-show="ctrl.tabs.rDForm.show">
        <tab-heading>
            对账单详情 <i class="fa fa-times set-cursor-pointer" ng-click="ctrl.closeRDForm()"></i>
        </tab-heading>
        <div class="rows">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">对账单信息</div>
                </div>
                <div class="portlet-body">
                    <form name="check_form" class="form-horizontal" ng-init="ctrl.setFormScope(this)" novalidate="novalidate" ng-submit="ctrl.submitMod(check_form);" ng-submit-force="true">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.r.item.supplier.no"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商名称：</label>
                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.r.item.supplier.name"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">对账月份：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.r.item.period"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">收货金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.r.item.recvMaterialValue | currency:'':2"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">付款金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.r.item.paiedAmount | currency:'':2"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">退货金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.r.item.retnMaterialValue | currency:'':2"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div id="billDetailStep2" class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">调整金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" ng-disabled="true" ng-bind="ctrl.edit.r.item.adjustValue | currency:'':2" >
<!-- 	                                    	<a ng-click="ctrl.queryAdjust(-1)" ng-bind="ctrl.edit.r.item.adjustValue | currency:'':3"></a> -->
	                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">本期未付：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.r.item.rcvAmount | currency:'':2"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">创建者：</label>
	                                <div class="col-sm-7 col-md-8">
	                                    <span class="form-control" disabled ng-bind="ctrl.edit.r.item.createdBy.userName"></span>
	                                </div>
	                            </div>
                       		 </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.r.item.confirmUserName">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">确认人：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" disabled ng-bind="ctrl.edit.r.item.confirmUserName"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.r.item.confirmDateStr">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">确认时间：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" disabled ng-bind="ctrl.edit.r.item.confirmDateStr"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.r.item.checkUserName">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">复核人：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" disabled ng-bind="ctrl.edit.r.item.checkUserName"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.r.item.checkDateStr">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">复核时间：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <span class="form-control" disabled ng-bind="ctrl.edit.r.item.checkDateStr"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" ng-if="ctrl.right.edit && ctrl.edit.r.item.status != 2001">
                        <div class="col-md-12 col-lg-8">
                            <div id="infoStep2" class="form-group">
                                <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>提交结果：</label>
                                <div class="col-sm-7 col-md-10" ng-if="!ctrl.showCheckFlag">
                                    <span class="form-control" disabled ng-if="ctrl.edit.r.item.status==2002">未复核</span>
                                    <span class="form-control" disabled ng-if="ctrl.edit.r.item.status==2003">已复核</span>
                                    <span class="form-control" disabled ng-if="ctrl.edit.r.item.status==60001">审批中</span>
                                </div>
                                <div class="col-sm-7 col-md-10" ng-if="ctrl.showCheckFlag">
                                     <select ng-model="ctrl.edit.r.item.flag" placeholder="请选择"  class="form-control" ng-change="ctrl.checkStatus()" required>
                                        <option value="">请选择</option>
                                        <option value="2002">不通过</option>
                                        <option value="2003">通过</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" ng-if="ctrl.right.edit && ctrl.edit.r.item.status != 2001">
                    	<div class="col-md-12 col-lg-8">
                            <div id="infoStep3" class="form-group">
                                <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>提交意见:</label>
                                <div class="col-sm-7 col-md-10">
                                    <textarea class="form-control" rows="3" ng-model="ctrl.edit.r.item.checkCause" ng-disabled="!ctrl.showCheckFlag" maxlength="1000" required></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                     <div class="row">
                         <div class="col-md-12 col-lg-8">
                             <div class="form-group">
                                 <label class="col-sm-3 col-md-2 control-label">备注：</label>

                                 <div class="col-sm-7 col-md-10">
                                         <textarea class="form-control" ng-bind="ctrl.edit.r.item.remark" disabled></textarea>
                                 </div>
                             </div>
                         </div>
                     </div>
<!--                      <div class="row"> -->
<!--                         <div class="col-md-12 col-lg-8"> -->
<!--                             <div class="form-group"> -->
<!--                                 <label class="col-sm-3 col-md-2 control-label">原始凭据：</label> -->
<!--                                 <div class="col-sm-7 col-md-10"> -->
<!--                                     <div class="portlet-input input-inline input-small"> -->
<!--                                             <span class="btn btn-default" style="position: relative;display: inline-block;overflow: hidden;"> -->
<!--                                                 <input id="upfile1" name="upfile1" type="file"/> -->
<!--                                             </span> -->
<!--                                     </div> -->
<!--                                     &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp; -->
<!--                                     <div class="portlet-input input-inline input-small" ng-if="ctrl.edit.r.item.status != 60001 && (ctrl.edit.r.item.status == 2005 || ctrl.edit.r.item.status == 2002) && ctrl.accountsFlag && ctrl.roleFlag"> -->
<!--                                         <button type="button" class="btn btn-success" ng-click="ctrl.uploadProof();">上传</button> -->
<!--                                     </div> -->
<!--                                     <div class="portlet-input input-inline input-small" ng-if="ctrl.edit.r.item.proofPath"> -->
<!--                                         <a ng-click="ctrl.runProofDownloadFile()">下载</a> -->
<!--                                     </div> -->
<!--                                 </div> -->
<!--                             </div> -->
<!--                         </div> -->
<!--                     </div> -->
                     <div id="infoStep4" class="row" ng-if="ctrl.right.edit && ctrl.showCheckFlag && ctrl.edit.r.item.status != 2001">
                        <div class="form-group">
                            <div class="col-sm-offset-4 col-sm-10">
                                <button type="submit" class="btn btn-primary btn-default-width"><i class="fa fa-save"></i> 保&nbsp;存</button>
                            </div>
                        </div>
                    </div>
                    </form>
                    <div class="portlet light bordered" ng-if="ctrl.edit.r.item.status == 60001 || (ctrl.edit.r.item.status == 2001 || ctrl.edit.r.item.status == 2002 || ctrl.edit.r.item.status == 2005 || ctrl.edit.r.item.status == 2004 || ctrl.edit.r.item.status == 2003) && ctrl.accountsFlag">
					    <div class="portlet-title">
					        <div class="caption font-green-sharp">
					            <i class="icon-cloud-upload font-green-sharp"></i>
					            <span class="caption-subject bold uppercase">原始凭据列表</span>
					        </div>
					        <div class="actions">
					            <div class="portlet-input input-inline input-small">
					                <button type="button" class="btn green btn-default-width" ng-if="((ctrl.edit.r.item.status == 2001 || ctrl.edit.r.item.status == 2002) && !ctrl.roleFlag) || ((ctrl.edit.r.item.status == 2004 || ctrl.edit.r.item.status == 2005) && ctrl.roleFlag)" ng-click="ctrl.addInvoiceForAccounts()"><i class="fa fa-plus"></i> 添加原始凭据</button>
					            </div>
					        </div>
					    </div>
					    <div class="portlet-body">
					        <div class="table-scrollable" style="margin-top:0px !important" id="stepInfo2">
					            <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
					                <thead>
					                <tr class="heading">
					                    <th width="50%">文件名</th>
                                        <th width="30%">上传人</th>
					                    <th id="stepInfo3">操作</th>
					                </tr>
					                </thead>
					                <tbody>
					                <tr ng-repeat="row in ctrl.accountsAttachementsList" ng-dblclick="ctrl.getViewUrl($index,row.orgFileName)">
					                    <td><a ng-click="ctrl.getViewUrl($index,row.orgFileName)">{{row.orgFileName}}</a></td>
                                        <td>{{row.createdByName}}</td>
					                    <td>
					                    	<form class="inline" ng-form-commit name="downForm" action="a/purch/payable/down" method="Post" target="hidden_frame">
					                            <input type="text" id="realFileName" ng-show="false" name="realFileName" ng-model="row.realFileName"  />
					                            <a class="btn default btn-xs green-stripe" ng-click="ctrl.submitDown(downForm)">下载</a>
					                            <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
					                        </form>
					                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="ctrl.validateIsImage(row.orgFileName)"
					                           ng-click="ctrl.getViewUrl($index,null)"><i class="fa fa-view font-green"></i> 查看</a>
					                        <a href="javascript:void(0)" class="btn default btn-xs red-stripe" ng-if="ctrl.right.edit && ((ctrl.edit.r.item.status == 2001 || ctrl.edit.r.item.status == 2002) && !ctrl.roleFlag) || ((ctrl.edit.r.item.status == 2004 || ctrl.edit.r.item.status == 2005) && ctrl.roleFlag)"
					                           ng-click="ctrl.showDelFile($index)">删除 </a>

					                    </td>
					                </tr>
					                </tbody>
					            </table>
					        </div>
					    </div>
					</div>
                </div>
            </div>
        </div>
        <div class="rows">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">对账单明细</div>
                    <div id="billDetailStep6" class="actions" ng-if="ctrl.right.manage">
                        <div class="portlet-input input-inline input-small" ng-if="ctrl.resultType == 1">
                            <button class="btn blue-hoki btn-default-width" ng-click="ctrl.payArrive()">票到日期</button>
                        </div>
	                    	 <div class="portlet-input input-inline input-small">
	                        	<button class="btn blue-hoki btn-default-width" ng-click="ctrl.openReconData()" ng-if="!ctrl.edit.r.item.status || ctrl.edit.r.item.status == 2001||ctrl.edit.r.item.status == 2002">对账工具</button>
	                   		 </div>
	                    	<div class="portlet-input input-inline input-small" ng-if="ctrl.edit.r.item.status != 60001 && ctrl.edit.r.item.status == 2005 && ctrl.accountsFlag && ctrl.roleFlag && (ctrl.lnCompanyId || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)">
	                			<button class="btn blue-hoki btn-default-width" ng-click="ctrl.checkPayable()"><i class="fa fa-check"></i>提交</button>
	                		</div>
	                		<div class="portlet-input input-inline input-small" ng-if="ctrl.edit.r.item.status != 60001 && ctrl.edit.r.item.status == 2001 && ctrl.accountsFlag && !ctrl.roleFlag && (ctrl.lnCompanyId || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)">
	                			<button class="btn blue-hoki btn-default-width" ng-click="ctrl.batchOperate(1)"><i class="fa fa-check font-green"></i>批量确认</button>
	                		</div>
	                		<div class="portlet-input input-inline input-small" ng-if="ctrl.edit.r.item.status != 60001 && ctrl.edit.r.item.status == 2002 && ctrl.accountsFlag && !ctrl.roleFlag && (ctrl.lnCompanyId || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)">
	                			<button class="btn blue-hoki btn-default-width" ng-click="ctrl.batchOperate(2)"><i class="fa fa-mail-reply"></i>批量取消确认</button>
	                		</div>
	                		<div class="portlet-input input-inline input-small" ng-if="ctrl.edit.r.item.status != 60001 && ctrl.edit.r.item.status == 2002 && ctrl.accountsFlag && !ctrl.roleFlag && (ctrl.lnCompanyId || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)">
	                			<button class="btn blue-hoki btn-default-width" ng-click="ctrl.batchPayableStatus(1)"><i class="fa fa-save"></i>提交</button>
	                		</div>
	                		<div class="portlet-input input-inline input-small" ng-if="ctrl.edit.r.item.status != 60001 && ctrl.edit.r.item.status == 2004 && ctrl.accountsFlag && ctrl.roleFlag && (ctrl.lnCompanyId || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)">
	                			<button class="btn blue-hoki btn-default-width" ng-click="ctrl.batchPayableStatus(2)"><i class="fa fa-save"></i>打回</button>
	                		</div>
	                		<div class="portlet-input input-inline input-small" ng-if="ctrl.edit.r.item.status != 60001 && ctrl.edit.r.item.status == 2004 && ctrl.accountsFlag && ctrl.roleFlag && (ctrl.lnCompanyId || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)">
	                			<button class="btn blue-hoki btn-default-width" ng-click="ctrl.batchOperate(3)"><i class="fa fa-check font-green"></i>复核</button>
	                		</div>
	                		<div class="portlet-input input-inline input-small" ng-if="ctrl.edit.r.item.status != 60001 && ctrl.edit.r.item.status == 2005 && ctrl.accountsFlag && ctrl.roleFlag && (ctrl.lnCompanyId || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)">
	                			<button class="btn blue-hoki btn-default-width" ng-click="ctrl.batchOperate(4)"><i class="fa fa-mail-reply"></i>取消复核</button>
	                    	</div>
		                    <div class="portlet-input input-inline input-small" ng-if="ctrl.edit.r.item.status != 60001 && ctrl.edit.r.item.status == 2001 && ctrl.accountsFlag && (ctrl.lnCompanyId || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)">
	                			<button class="btn blue-hoki btn-default-width" ng-click="ctrl.nextPeriodAdjustOpen()"><i class="fa fa-check font-green"></i></i>批量下期调整</button>
	                		</div>
                            <div class="portlet-input input-inline input-small" ng-if="ctrl.edit.r.item.status != 60001 && ctrl.edit.r.item.status == 2001 && ctrl.accountsFlag && (ctrl.lnCompanyId || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)">
                                <button class="btn blue-hoki btn-default-width" ng-click="ctrl.lastPeriodAdjustOpen()"><i class="fa fa-check font-green"></i></i>批量上期调整</button>
                            </div>
	                		<div class="portlet-input input-inline input-small" ng-if="ctrl.edit.r.item.status != 60001 && ctrl.edit.r.item.status == 2003 && ctrl.accountsFlag && ctrl.roleFlag && ctrl.edit.r.item.counterTrialFlag && ctrl.edit.r.item.payApplyFlag && (ctrl.lnCompanyId || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)">
	                			<button class="btn blue-hoki btn-default-width" ng-click="ctrl.returnPayableOpen()"><i class="fa fa-save"></i>反审</button>
	                		</div>
                    	    <div class="portlet-input input-inline input-small">
                            <form action="a/purch/payable/detail/export" method="POST" enctype="multipart/form-data" target="hidden_frame">
                                <input type="text" name="supplier.recordId" ng-show="false"
                                       ng-model="ctrl.edit.r.item.supplier.recordId"/>
                                <input type="text" name="period" ng-show="false" ng-model="ctrl.edit.r.item.period"/>
                                <input type="text" name="lnCompanyId" ng-show="false" ng-model="ctrl.lnCompanyId"/>
                                <input type="text" name="orderBy" ng-show="false" value="lastUpdDate DESC"/>

                                <div>
                                    <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-file-excel-o"></i> 导出对账单明细
                                    </button>
                                    <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div id="billDetailStep3" class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading">
                                <th ng-if="ctrl.edit.r.item.status != 60001 && ctrl.right.manage && ctrl.accountsFlag && ctrl.edit.r.item.status != 2003">
                        			<checkbox ng-model="ctrl.allPageChecked" value="1" name="test" ng-click="ctrl.selectAllPage()"></checkbox>
                        		</th>
                        		<th width="10%" ng-if="ctrl.edit.r.item.status != 60001 && ctrl.right.edit && ctrl.edit.r.item.status != 2003 && (ctrl.lnCompanyId  || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)">操作</th>
                        		<th>序号</th>
                        		<th>状态</th>
                        		<th>单据编号</th>
                                <th ng-if="ctrl.lnCompanyId">销售公司</th>

                        		<th>销售单据编号</th>
                                <th>终端供应商</th>
                            	<th>对账属性</th>
                                <th>物料编号</th>
                                <th>物料名称</th>

                                <th>规格型号</th>
                                <th>出入库时间</th>
                                <th>出入库数量</th>
                                <th>单价</th>
                                <th>金额</th>

                                <th>类型</th>
                                <th>送货单号</th>
                                <th>送货时间</th>
                                <th>操作类型</th>
                                <th>平米数</th>

                                <th>销售部门</th>
                                <th>PCB类型</th>
                                <th>覆铜板材</th>
                            </tr>
                            <tr>
	                            <td ng-if="ctrl.edit.r.item.status != 60001 && ctrl.right.manage && ctrl.accountsFlag && ctrl.edit.r.item.status != 2003"></td>
	                            <td ng-if="ctrl.edit.r.item.status != 60001 && ctrl.right.manage && ctrl.edit.r.item.status != 2003 && (ctrl.lnCompanyId  || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)"></td>
	                            <td></td>
	                            <td>
	                           		 <input type="text" class="form-control" ng-model="ctrl.operateStatusStrQuery">
	                            </td>
	                            <td>
	                            	<input type="text" class="form-control" ng-model="ctrl.billNoQuery">
	                            </td>
                                <td ng-if="ctrl.lnCompanyId"></td>
	                            <td>
	                            	<input type="text" class="form-control" ng-model="ctrl.xsBillNoQuery">
	                            </td>
	                            <td>
	                            	<input type="text" class="form-control" ng-model="ctrl.supplierNameQuery">
	                            </td>
	                            <td>
	                            	<input type="text" class="form-control" ng-model="ctrl.checkPropertyQuery">
	                            </td>
	                            <td>
	                            	<input type="text" class="form-control" ng-model="ctrl.materialNoQuery">
	                            </td>
	                            <td>
	                            	<input type="text" class="form-control" ng-model="ctrl.materialNameQuery">
	                            </td>
	                            <td>
	                            	<input type="text" class="form-control" ng-model="ctrl.specificationQuery">
	                            </td>
	                            <td></td>
	                            <td></td>
	                            <td></td>
	                            <td></td>
	                            <td></td>
                                <td>
                                    <input type="text" class="form-control" ng-model="ctrl.deliveryNoStr">
                                </td>
                                <td></td>
	                            <td>
	                            	<input type="text" class="form-control" ng-model="ctrl.materialStrQuery">
	                            </td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
	                        </tr>
                            </thead>
                            <tbody ng-repeat="row in ctrl.page.rD.data.list | 
		                        filter:{
		                        'operateStatusStr':ctrl.operateStatusStrQuery,
		                        'billNo':ctrl.billNoQuery,
		                        'xsBillno':ctrl.xsBillNoQuery,
		                        'supplierName':ctrl.supplierNameQuery,
		                        'checkProperty':ctrl.checkPropertyQuery,
		                        'matNo':ctrl.materialNoQuery,
		                        'matName':ctrl.materialNameQuery,
		                        'specification':ctrl.specificationQuery,
		                        'deliveryNo':ctrl.deliveryNoStr}">
                            <tr ng-dblclick="ctrl.disRDDetail(row)" ng-class="{'danger':row.periodFirstFlag,'success':row.periodAfterFlag}" ng-if="row.adjustCheckFlag != 12">
                                <td ng-if="ctrl.edit.r.item.status != 60001 && ctrl.right.manage && ctrl.accountsFlag && ctrl.edit.r.item.status != 2003">
                        			<checkbox ng-if="((!row.adjustFlag && !row.adjustCheckFlag) || row.adjustFlag == 1) && row.editFlag && row.editFlag == '1' && (!ctrl.lnCompanyId || (ctrl.lnCompanyId && ctrl.companyId == row.xsCom.recordId))" ng-model="row.checked" ng-change="ctrl.getCheckList()"></checkbox>
                        		</td>
                        		<td ng-if="ctrl.edit.r.item.status != 60001 && ctrl.right.edit && ctrl.edit.r.item.status != 2003 &&(ctrl.lnCompanyId  || !ctrl.edit.r.item.supplier.erpId || ctrl.edit.r.item.supplier.erpId || ctrl.companyId == ctrl.ValiteCompanyUtil.factId)">
                                    <span ng-if="row.editFlag && row.editFlag == '1' && ctrl.accountsFlag && row.operateStatus == 1001 && ((!row.adjustFlag && !row.adjustCheckFlag) || row.adjustFlag == 1) && !ctrl.roleFlag && (!row.xsCom || !row.xsCom.recordId || ctrl.companyId == row.xsCom.recordId)">
		                                <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-click="ctrl.materialConfirm(row)">
		                                	<i class="fa fa-check font-green"></i>确&nbsp;认
		                                </a>
	                                </span>
	                                <span ng-if="row.editFlag && row.editFlag == '1' && ctrl.accountsFlag && row.operateStatus == 1002 && ctrl.edit.r.item.status != 2004 && ((!row.adjustFlag && !row.adjustCheckFlag) || row.adjustFlag == 1) && !ctrl.roleFlag && (!row.xsCom || !row.xsCom.recordId || ctrl.companyId == row.xsCom.recordId)">
		                                <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-click="ctrl.materialCancleConfirm(row)">
		                                	<i class="fa fa-reply font-red"></i>取消确认
		                                </a>
	                                </span>
	                                <span ng-if="row.editFlag && row.editFlag == '1' && ctrl.accountsFlag && row.operateStatus == 1003 && ((!row.adjustFlag && !row.adjustCheckFlag) || row.adjustFlag == 1) && ctrl.roleFlag && (!row.xsCom || !row.xsCom.recordId || ctrl.companyId == row.xsCom.recordId)">
		                                <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-click="ctrl.batchCheck(row)">
		                                	<i class="fa fa-check font-green"></i>复&nbsp;核
		                                </a>
	                                </span>
	                                <span ng-if="row.editFlag && row.editFlag == '1' && ctrl.accountsFlag && row.operateStatus == 1004 && ((!row.adjustFlag && !row.adjustCheckFlag) || row.adjustFlag == 1) && ctrl.roleFlag && (!row.xsCom || !row.xsCom.recordId || ctrl.companyId == row.xsCom.recordId)">
		                                <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-click="ctrl.batchCancleCheck(row)">
		                                	<i class="fa fa-reply font-red"></i>取消复核
		                                </a>
	                                </span>
	                                <span ng-if="row.editFlag && row.editFlag == '1' && row.operateStatus == 1001 && ((!row.adjustFlag && !row.adjustCheckFlag) || row.adjustFlag == 1) && (!row.xsCom || !row.xsCom.recordId || ctrl.companyId == row.xsCom.recordId)">
		                                <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-click="ctrl.lastPeriodAdjustOpen(row)">
		                                	<i class="fa fa-check font-green"></i>转上期
		                                </a>
		                                <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-click="ctrl.nextPeriodAdjustOpen(row)">
		                                	<i class="fa fa-check font-green"></i>转下期
		                                </a>
	                                </span>
                             	</td>
                             	<td ng-bind="$index+1"></td>
                             	<td ng-bind="row.operateStatusStr"></td>
                                <td><a ng-click="ctrl.disRDDetail(row)">{{row.billNo}}</a></td>
                                <td ng-if="ctrl.lnCompanyId" ng-bind="row.xsCom.shortName"></td>
                                <td ng-bind="row.xsBillno"></td>
                                <td ng-bind="row.supplierName"></td>
                                <td ng-bind="row.checkProperty"></td>
                                <td ng-bind="row.matNo"></td>
                                <td ng-bind="row.matName"></td>
                                <td ng-bind="row.specification"></td>
                                <td ng-bind="row.receivedDate | date:'yyyy-MM-dd HH:mm:ss'"></td>
                                <td ng-bind="row.quantity | number:4"></td>
                                <td ng-bind="row.price | currency:'':3"></td>
                                <td>
                                    <span ng-if="row.inOutFlag == 0 " style="color:red">{{row.displayAmount | currency:'':2}}</span>
                                    <span ng-if="row.inOutFlag != 0 " >{{row.displayAmount | currency:'':2}}</span>
                                </td>
                                <td ng-bind="row.inOutFlagAsStr"></td>
                                <td ng-bind="row.deliveryNo"></td>
                                <td ng-bind="row.deliveryDateStr"></td>
                                <td ng-bind="row.materialStr"></td>
                                <td ng-bind="row.area"></td>
                                <td ng-bind="row.departName"></td>
                                <td ng-bind="row.boardLevelValue"></td>
                                <td ng-bind="row.materialTypeValue"></td>
                            </tr>

                           	<tr ng-if="row.adjustPayableList && row.adjustPayableList.length > 0" ng-repeat = "item in row.adjustPayableList" ng-dblclick="ctrl.toShowAdjust(row.recordId)">
	                            <td ng-if="ctrl.edit.r.item.status != 60001 && ctrl.right.manage && ctrl.accountsFlag && ctrl.edit.r.item.status != 2003"></td>
	                        	<td ng-if="ctrl.edit.r.item.status != 60001 && ctrl.right.manage && ctrl.edit.r.item.status != 2003"></td>
	                            <td></td>
	                            <td></td>
                                <td ng-if="ctrl.lnCompanyId"></td>
	                            <td></td>
	                            <td ng-bind="row.supplierName"></td>
	                            <td>对账单明细调整</td>
	                            <td></td>
	                            <td></td>
	                            <td></td>
	                            <td></td>
	                            <td></td>
	                            <td>
	                            	<td><a href="javascript:void(0)" ng-click="ctrl.toShowAdjust(row.recordId)" ng-bind="item.amount | currency:'':2"></a></td>
	                            </td>
	                            <td></td>
	                            <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
	                        </tr>
                            </tbody>
                            <tbody ng-if="ctrl.edit.r.item.sumAdjustAmount">
		                        <tr ng-dblclick="ctrl.toShowAdjust()">
		                            <td ng-if="ctrl.edit.r.item.status != 60001 && ctrl.right.manage && ctrl.accountsFlag && ctrl.edit.r.item.status != 2003"></td>
		                        	<td ng-if="ctrl.edit.r.item.status != 60001 && ctrl.right.manage && ctrl.edit.r.item.status != 2003"></td>
		                            <td></td>
		                            <td></td>
                                    <td ng-if="ctrl.lnCompanyId"></td>
		                            <td></td>
		                            <td></td>
		                            <td>对账单调整汇总</td>
		                            <td></td>
		                            <td></td>
		                            <td></td>
		                            <td></td>
		                            <td></td>
		                            <td></td>
		                            <td>
		                            </td>
		                            <td>
                                        <a href="javascript:void(0)" ng-click="ctrl.toShowAdjust()" ng-bind="ctrl.edit.r.item.sumAdjustAmount | currency:'':2"></a>
                                    </td>
		                            <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
		                        </tr>
	                        </tbody>
                            <tbody ng-if="ctrl.edit.r.item.sumPayServiceFee">
                            <tr ng-dblclick="ctrl.toShowAdjust()">
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td ng-if="ctrl.lnCompanyId"></td>
                                <td></td>
                                <td></td>
                                <td>服务费调整</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td>
                                </td>
                                <td>
                                    <a href="javascript:void(0)" ng-click="ctrl.toShowAdjust()" ng-bind="ctrl.edit.r.item.sumPayServiceFee | currency:'':2"></a>
                                </td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- END 对账单详情 -->

    <!-- BEGIN 对账单明细详情 -->
    <tab active="ctrl.tabs.rDDForm.active" ng-show="ctrl.tabs.rDDForm.show">
        <tab-heading>
            对账单明细详情 <i class="fa fa-times set-cursor-pointer" ng-click="ctrl.closeRDDForm()"></i>
        </tab-heading>
        <div class="rows">
            <div class="portlet light bordered ">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki"> 对账单明细详情</div>
                </div>
                <div class="portlet-body form">
                    <form class="form-horizontal" role="form">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.rD.item.supplier.no"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商名称：</label>
                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.rD.item.supplier.name ? ctrl.edit.rD.item.supplier.name : ctrl.edit.rD.item.supplier.shortName"></span>
                                    </div>
                                </div>
                            </div>
                        	<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">对账月份：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.rD.item.period"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">单据编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.rD.item.billNo"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4"  ng-if="ctrl.edit.rD.item.material.materialKind == 100701">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">物料名称：</label>

                                    <div class="col-sm-7 col-md-8">
                                   		<!-- 原料 -->
                                        <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.rD.item.material.name"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.rD.item.material.materialKind == 100702">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">生产编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                   		<!-- 成品 -->
                                        <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.rD.item.material.no" ></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">收货日期：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.rD.item.receivedDate"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.rD.item.rawPoDetailId || ctrl.edit.rD.item.prPoDetailId">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">订单数量：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" disabled
                                                  ng-bind="ctrl.edit.rD.item.purchasingQuantity | number:4"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">出入库数量：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" disabled
                                                  ng-bind="ctrl.edit.rD.item.quantity | number:4"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.rD.item.rawPoDetailId || ctrl.edit.rD.item.prPoDetailId">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">未入库数量：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" disabled
                                                  ng-bind="ctrl.edit.rD.item.unStorageQuantity | number:4"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.rD.item.inOutFlag != 2">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">单价：</label>
                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" disabled
                                                  ng-bind="ctrl.edit.rD.item.price | currency:'':3"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.rD.item.inOutFlag != 2">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">总金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" disabled
                                                  ng-bind="ctrl.edit.rD.item.amount | currency:'':2"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.rD.item.prdorderDetailId || ctrl.edit.rD.item.prPdDeteilId">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">客户型号：</label>
                                    <div class="col-sm-7 col-md-8">
                                          <span class="form-control" disabled ng-bind="ctrl.edit.rD.item.specification"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.rD.item.prdorderDetailId || ctrl.edit.rD.item.prPdDeteilId">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">模具费：</label>
                                    <div class="col-sm-7 col-md-8">
                                          <span class="form-control" disabled ng-bind="ctrl.edit.rD.item.mouldFee"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.rD.item.prdorderDetailId || ctrl.edit.rD.item.prPdDeteilId">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">其它费用：</label>
                                    <div class="col-sm-7 col-md-8">
                                          <span class="form-control" disabled ng-bind="ctrl.edit.rD.item.othersFee"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.rD.item.prdorderDetailId || ctrl.edit.rD.item.prPdDeteilId">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">测试架费：</label>
                                    <div class="col-sm-7 col-md-8">
                                          <span class="form-control" disabled ng-bind="ctrl.edit.rD.item.testShelfFee"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.rD.item.prdorderDetailId || ctrl.edit.rD.item.prPdDeteilId">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">工程费：</label>
                                    <div class="col-sm-7 col-md-8">
                                          <span class="form-control" disabled ng-bind="ctrl.edit.rD.item.engineeringFee"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>

                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-disabled="true"
                                                      ng-bind="ctrl.edit.rD.item.remark"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--  <div class="col-sm-offset-5">
                             <button type="button" class="btn default" ng-click="ctrl.closeRDDForm()">返回</button>
                         </div> -->
                         <div class="row" ng-if="ctrl.edit.rD.item.inOutFlag == 2 && ctrl.edit.rD.item.costList.length > 0">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">费用表：</label>
                                    <div class="col-sm-7 col-md-10">
                                        <table class="table table-bordered">
			                          		<thead>
			                          			<tr class="heading">
			                          				<th width="20%">类型</th>
			                          				<th width="20%">费目</th>
			                          				<th width="20%">金额</th>
			                          				<th width="20%">数量</th>
			                          				<th width="20%">小计</th>
			                          			</tr>
			                          		</thead>
			                          		<tbody>
			                          			<tr ng-repeat="item in ctrl.edit.rD.item.costList">
			                          				<td>
			                          					<span ng-if="item.type == 1">单价</span>
			                          					<span ng-if="item.type == 2">固定费用</span>
			                          					<span ng-if="item.type == 3">品质扣费</span>
			                          				</td>
			                          				<td ng-bind="item.name"></td>
			                          				<td ng-bind="item.value"></td>
			                          				<td ng-bind="item.quantity"></td>
			                          				<td ng-bind="item.sum"></td>
			                          			</tr>
			                          			<tr>
			                          				<td></td>
			                          				<td>合计:</td>
			                          				<td>{{ctrl.edit.rD.item.amount | currency:'':2}}</td>
			                          				<td></td>
			                          				<td></td>
			                          			</tr>
			                          		</tbody>
		                               </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
        <div class="rows" ng-if="ctrl.page.materialCheck.data.list && ctrl.page.materialCheck.data.list.length > 0" >
            <div class="portlet light bordered ">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki"> 对账单明细调整记录</div>
                </div>
                 <div class="portlet-body">
                    <div id="adjustStep1" class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading">
                                <th width="10%">采购单编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                            <th width="10%">物料编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th width="10%" ng-class="{'sorting': ctrl.sortA.supplierNo.both, 'sorting_desc': ctrl.sortA.supplierNo.desc, 'sorting_asc': ctrl.sortA.supplierNo.asc}"
                                			ng-click="ctrl.sortAClick('supplierNo')">供应商编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                            <th width="10%" ng-class="{'sorting': ctrl.sortA.supplierName.both, 'sorting_desc': ctrl.sortA.supplierName.desc, 'sorting_asc': ctrl.sortA.supplierName.asc}"
	                            			ng-click="ctrl.sortAClick('supplierName')">供应商名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                            <th width="10%">调整时间</th>
	                            <th width="10%">调整原因</th>
	                            <th width="10%" ng-class="{'sorting': ctrl.sortA.createdByUserName.both, 'sorting_desc': ctrl.sortA.createdByUserName.desc, 'sorting_asc': ctrl.sortA.createdByUserName.asc}"
	                            			ng-click="ctrl.sortAClick('createdByUserName')">调整人&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in ctrl.page.materialCheck.data.list">
                                <td ng-bind="row.materialCheck.billNo"></td>
                                <td ng-bind="row.materialCheck.material.no"></td>
                                <td ng-bind="row.supplier.no"></td>
                                <td ng-bind="row.supplier.name"></td>
                                 <td ng-bind="row.createdDate"></td>
                                <td ng-bind="row.adjustReason"></td>
                                <td ng-bind="row.createdBy.userName"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row page-margin-top">
                        <div class="col-md-12 col-lg-6">
                            <span class="inline">每页</span>
                            <select class="form-control inline" style="margin-top:8px; width:100px;"
                                    disable-valid-styling="true"
                                    disable-invalid-styling="true"
                                    ng-model="ctrl.page.materialCheck.pageSize"
                                    ng-change="ctrl.pageSizeChange(3)"
                                    ng-options="option for option in ctrl.page.options">
                            </select>
                            <span class="inline">条详情&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{ctrl.page.materialCheck.data.startCount}} / {{ctrl.page.materialCheck.data.endCount}} 条，共 {{ctrl.page.materialCheck.data.count}} 条</span>
                        </div>
                        <div id="adjustStep4" class="col-md-12 col-lg-6">
                            <paging
                                    class="pull-right"
                                    page="ctrl.page.materialCheck.data.pageNo"
                                    page-size="ctrl.page.materialCheck.data.pageSize"
                                    total="ctrl.page.materialCheck.data.count"
                                    adjacent="1"
                                    dots="..."
                                    scroll-top="false"
                                    hide-if-empty="false"
                                    ul-class="pagination"
                                    active-class="active"
                                    disabled-class="disabled"
                                    show-prev-next="true"
                                    paging-action="ctrl.doPage(3, page, pageSize)">
                            </paging>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- END 对账单明细详情 -->
    <!-- BEGIN 调整详情-->
    <tab active="ctrl.tabs.aForm.active" ng-show="ctrl.tabs.aForm.show">
        <tab-heading>
            手工调整记录 <i class="fa fa-times set-cursor-pointer" ng-click="ctrl.closeAForm()"></i>
        </tab-heading>
            <div id="adjustStep6" class="panel panel-default"  ng-show="ctrl.query.a.isAll">
                <div class="panel-heading font-blue-hoki">查询</div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="ctrl.query.a.supplier.no.value"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商名称：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="ctrl.query.a.supplier.name.value"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">对账期间：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                        	   placeholder="请输入对账期间，如：201511"
                                               ng-model="ctrl.query.a.period.value"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">状态：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <select class="form-control"
                                                disable-auto-validate="true"
                                                ng-model="ctrl.query.a.status.value"
                                                ng-options="status.flagId as status.flagName for status in ctrl.query.a.statusList">
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default btn-default-width pull-right"
                                        ng-click="ctrl.doQueryAdjust()"><i class="fa fa-search"></i> 查&nbsp;询
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">手工调整记录</div>
                    <div id="adjustStep7" class="actions">
                        <div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
                            <button type="button" class="btn green btn-default-width" ng-click="ctrl.addAdjust(1)"><i
                                    class="fa fa-plus"></i> 新&nbsp;增
                            </button>
                        </div>
                        <div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
                            <button type="button" class="btn green btn-default-width" ng-click="ctrl.addAdjust(2)"><i
                                    class="fa fa-plus"></i> 新增对账明细
                            </button>
                        </div>
                    </div>

                </div>
                <div class="portlet-body">
                    <div id="adjustStep1" class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading">
                                <th id="adjustStep2" width="15%" ng-class="{'sorting': ctrl.sortA.supplierNo.both, 'sorting_desc': ctrl.sortA.supplierNo.desc, 'sorting_asc': ctrl.sortA.supplierNo.asc}"
                                			ng-click="ctrl.sortAClick('supplierNo')">供应商编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                            <th width="15%" ng-class="{'sorting': ctrl.sortA.supplierName.both, 'sorting_desc': ctrl.sortA.supplierName.desc, 'sorting_asc': ctrl.sortA.supplierName.asc}"
	                            			ng-click="ctrl.sortAClick('supplierName')">供应商名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                            <th width="10%" ng-class="{'sorting': ctrl.sortA.period.both, 'sorting_desc': ctrl.sortA.period.desc, 'sorting_asc': ctrl.sortA.period.asc}"
	                            			ng-click="ctrl.sortAClick('period')">对账期间&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                            <th width="15%" ng-class="{'sorting': ctrl.sortA.amount.both, 'sorting_desc': ctrl.sortA.amount.desc, 'sorting_asc': ctrl.sortA.amount.asc}"
	                            			ng-click="ctrl.sortAClick('amount')">调整金额&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                            <th width="15%">调整原因</th>
	                            <th width="10%" ng-class="{'sorting': ctrl.sortA.createdByUserName.both, 'sorting_desc': ctrl.sortA.createdByUserName.desc, 'sorting_asc': ctrl.sortA.createdByUserName.asc}"
	                            			ng-click="ctrl.sortAClick('createdByUserName')">调整人&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                            <th width="15%" ng-class="{'sorting': ctrl.sortA.status.both, 'sorting_desc': ctrl.sortA.status.desc, 'sorting_asc': ctrl.sortA.status.asc}"
	                            			ng-click="ctrl.sortAClick('status')">状态&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th id="adjustStep3">操作&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in ctrl.page.a.data.list" ng-dblclick="ctrl.disADetial($index)">
                                <td><a ng-click="ctrl.disADetial($index)">{{row.supplier.no}}</a></td>
                                <td ng-bind="row.supplier.name"></td>
                                <td ng-bind="row.period"></td>
                                <td ng-bind="row.amount | currency:'':2"></td>
                                <td ng-bind="row.adjustReason"></td>
                                <td ng-bind="row.createdBy.userName"></td>
                                <td ng-if="row.status === 99999901">
	                                <span class="label label-sm label-default">未冲红</span>
	                            </td>
	                            <td ng-if="row.status === 99999902">
	                                <span class="label label-sm label-success">冲红单 </span>
	                            </td>
	                            <td ng-if="row.status === 99999903">
	                                <span class="label label-sm label-danger">已冲红</span>
	                            </td>
                                <td>
                                    <a href="javascript:void(0)" class="btn btn-xs btn-default"
                                       ng-show="ctrl.isCanRushRed(row.status)" ng-if="ctrl.right.edit && row.feeFlag != 1"
                                       ng-click="ctrl.doWriteRed($index)"><i
                                            class="glyphicon glyphicon-trash font-red"></i> 冲红</a>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row page-margin-top">
                        <div id="adjustStep5" class="col-md-12 col-lg-6">
                            <span class="inline">每页</span>
                            <select class="form-control inline" style="margin-top:8px; width:100px;"
                                    disable-valid-styling="true"
                                    disable-invalid-styling="true"
                                    ng-model="ctrl.page.a.pageSize"
                                    ng-change="ctrl.pageSizeChange(2)"
                                    ng-options="option for option in ctrl.page.options">
                            </select>
                            <span class="inline">条详情&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{ctrl.page.a.data.startCount}} / {{ctrl.page.a.data.endCount}} 条，共 {{ctrl.page.a.data.count}} 条</span>
                        </div>
                        <div id="adjustStep4" class="col-md-12 col-lg-6">
                            <paging
                                    class="pull-right"
                                    page="ctrl.page.a.data.pageNo"
                                    page-size="ctrl.page.a.data.pageSize"
                                    total="ctrl.page.a.data.count"
                                    adjacent="1"
                                    dots="..."
                                    scroll-top="false"
                                    hide-if-empty="false"
                                    ul-class="pagination"
                                    active-class="active"
                                    disabled-class="disabled"
                                    show-prev-next="true"
                                    paging-action="ctrl.doPage(2, page, pageSize)">
                            </paging>
                        </div>
                    </div>
                </div>
            </div>
    </tab>
    <!-- END 调整详情 -->

     <!-- BEGIN 异常处理-->
    <tab active="ctrl.tabs.exceptionForm.active" ng-show="ctrl.tabs.exceptionForm.show">
        <tab-heading>对账工具<i class="fa fa-times set-cursor-pointer" ng-click="ctrl.closeExceptionForm()"></i></tab-heading>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">
                    <select class="form-control" ng-change="ctrl.changeShowList()" ng-model="ctrl.dealStatus">
                        <option value="">待核对</option>
                        <option value="1">已核对</option>
                    </select>
                </div>
                <div class="actions">
                    <div class="actions">
                    <div class="portlet-input input-inline input-small">
						<button type="button" class="btn green btn-default-width" ng-click="ctrl.batchConfirmException()">批量确认
						</button>
					</div>
                    <div class="portlet-input input-inline input-small">
						<button type="button" class="btn green btn-default-width" ng-click="ctrl.addAdjust(1)">新增调整
						</button>
					</div>
					<div class="portlet-input input-inline input-small">
						<button type="button" class="btn green btn-default-width" ng-click="ctrl.addAdjust(2)"> 新增明细调整
						</button>
					</div>
                        <div class="portlet-input input-inline input-small">
                            <button class="btn btn-primary btn-default-width" ng-click="ctrl.dealData()" ng-disabled="!ctrl.exportExceptionList || ctrl.exportExceptionList.length == 0">对账</button>
                        </div>
                        <div class="portlet-input input-inline input-small">
                            <button class="btn btn-primary btn-default-width" ng-click="ctrl.exportData()">导入</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="portlet-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="row" style="padding-bottom: 1rem;">
                            <div class="col-md-1">
                            </div>
                            <div class="col-md-11">
                                <span style="font-size: 2rem;">导入数据</span>
                            </div>
                        </div>
                        <div class="row" ng-repeat="item in ctrl.exportShowExceptionList">
                            <div class="col-md-1" style="padding-top: 1rem;">
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.showExceptionDeail(item)" ng-if="!item.goodCheckId">
                                    <span ng-if="!item.showFlag">+</span>
                                    <span ng-if="item.showFlag">-</span>
                                </a>
                            </div>
                            <div class="col-md-11">
                                <div class="table-scrollable" style="margin-top:0px !important">
                                    <table class="table table-primary table-bordered table-condensed table-primary table-hover">
                                        <thead style="background-color: #fff;">
                                        <tr>
                                            <th width="30%">采购编号</th>
                                            <th width="40%">物料编号</th>
                                            <th width="15%">数量</th>
                                            <th width="15%">金额</th>
                                            <th width="15%">对账日期</th>
                                            <th width="15%">对账类型</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td width="30%" height="40px;">{{item.customerpo}}</td>
                                            <td width="40%">{{item.cutomermodal}}</td>
                                            <td width="15%">{{item.num}}</td>
                                            <td width="15%">{{item.money}}</td>
                                            <td width="15%">{{item.opearDate}}</td>
                                            <td width="15%">{{item.olderType}}</td>
                                        </tr>
                                        <tr class="danger" ng-repeat="row in item.list" ng-if="item.showFlag">
                                            <td width="30%">{{row.customerpo}}</td>
                                            <td width="40%">{{row.cutomermodal}}</td>
                                            <td width="15%">{{row.num}}</td>
                                            <td width="15%">{{row.money}}</td>
                                            <td width="15%">{{row.opearDate}}</td>
                                            <td width="15%">{{row.olderType}}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="row" style="padding-bottom: 1rem;">
                            <div class="col-md-11">
                                <span style="font-size: 2rem;">对账数据</span>
                            </div>
                        </div>
                        <div class="row" ng-repeat="item in ctrl.receivableShowExceptionList" ng-if="ctrl.edit.r.item.receivableStatus">
                            <div class="col-md-12" ng-if="item && item.list && item.list.length > 0">
                                <div class="table-scrollable" style="margin-top:0px !important">
                                    <table class="table table-primary table-bordered table-condensed table-primary table-hover">
                                        <thead style="background-color: #fff;">
                                        <tr>
                                            <th width="30%">采购编号</th>
                                            <th width="40%">物料编号</th>
                                            <th width="10%">数量</th>
                                            <th width="10%">金额</th>
                                            <th width="10%">对账日期</th>
                                            <th width="10%">对账类型</th>
                                            <th width="10%" ng-if="!ctrl.dealStatus && ctrl.edit.r.item.receivableStatus">操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td width="30%" height="40px;">{{item.customerpo}}</td>
                                            <td width="40%">{{item.cutomermodal}}</td>
                                            <td width="10%">{{item.num}}</td>
                                            <td width="10%">{{item.money}}</td>
                                            <td width="10%">{{item.opearDate}}</td>
                                            <td width="10%">{{item.olderType}}</td>
                                            <td width="10%" ng-if="!ctrl.dealStatus && ctrl.edit.r.item.receivableStatus">
                                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.confimException(item)" ng-if="item.confimFlag == 1">确认</a>
                                            </td>
                                        </tr>
                                        <tr class="danger" ng-repeat="row in item.list" ng-if="item.showFlag">
                                            <td width="30%">{{row.customerPo}}</td>
                                            <td width="40%">{{row.specification}}</td>
                                            <td width="10%">{{row.quantity}}</td>
                                            <td width="10%">{{row.amount}}</td>
                                            <td width="10%">{{row.receivedDateStr}}</td>
                                            <td width="10%">{{row.complainRemark}}</td>
                                            <td width="10%" ng-if="!ctrl.dealStatus && ctrl.edit.r.item.receivableStatus">
                                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.splitPeriodNum(row)">拆分</a>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-12" ng-if="!(item && item.list && item.list.length > 0)" style="padding-top: 15px;padding-bottom: 15px;">
                                <div class="table-scrollable" style="margin-top:0px !important">
                                    <table class="table table-primary table-bordered table-condensed table-primary table-hover">
                                        <thead style="background-color: #fff;">
                                        <tr>
                                            <th width="30%" class="text-danger" height="40px;"> </th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="row" ng-repeat="item in ctrl.receivableExceptionList" ng-if="!ctrl.edit.r.item.receivableStatus">
                            <div class="col-md-12">
                                <div class="table-scrollable" style="margin-top:0px !important">
                                    <table class="table table-primary table-bordered table-condensed table-primary table-hover">
                                        <thead style="background-color: #fff;">
                                        <tr>
                                            <th width="30%">采购编号</th>
                                            <th width="40%">物料编号</th>
                                            <th width="10%">数量</th>
                                            <th width="10%">金额</th>
                                            <th width="10%">对账日期</th>
                                            <th width="10%">对账类型</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td width="30%" height="40px;">{{item.customerPo}}</td>
                                            <td width="40%">{{item.specification}}</td>
                                            <td width="10%">{{item.quantity}}</td>
                                            <td width="10%">{{item.amount}}</td>
                                            <td width="10%">{{row.receivedDateStr}}</td>
                                            <td width="10%">{{row.complainRemark}}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="row" ng-repeat="item in ctrl.receivableExceptionList" ng-if="!ctrl.dealStatus && ctrl.edit.r.item.receivableStatus && !item.userFlag">
                            <div class="col-md-12">
                                <div class="table-scrollable" style="margin-top:0px !important">
                                    <table class="table table-primary table-bordered table-condensed table-primary table-hover">
                                        <thead style="background-color: #fff;">
                                        <tr>
                                            <th width="30%">采购编号</th>
                                            <th width="40%">物料编号</th>
                                            <th width="10%">数量</th>
                                            <th width="10%">金额</th>
                                            <th width="10%">对账日期</th>
                                            <th width="10%">对账类型</th>
                                            <th width="10%">操作</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td width="30%" height="40px;">{{item.customerPo}}</td>
                                            <td width="40%">{{item.specification}}</td>
                                            <td width="10%">{{item.quantity}}</td>
                                            <td width="10%">{{item.amount}}</td>
                                            <td width="10%">{{row.receivedDateStr}}</td>
                                            <td width="10%">{{row.complainRemark}}</td>
                                            <td width="10%"></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- END 异常处理 -->

    <!-- BEGIN 调整详情详情/编辑 -->
    <tab active="ctrl.tabs.aDForm.active" ng-show="ctrl.tabs.aDForm.show">
        <tab-heading>
            手工调整信息 <i class="fa fa-times set-cursor-pointer" ng-click="ctrl.closeADForm()"></i>
        </tab-heading>
        <div class="rows" ng-if="ctrl.tabs.aDForm.show">
            <div class="portlet light bordered ">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">手工调整信息</div>
                </div>
                <div class="portlet-body form">
                    <form id="infoStep1" class="form-horizontal" role="form" name="adjustEditForm" novalidate="novalidate"
                          ng-submit="ctrl.submitAdjust(adjustEditForm);" ng-submit-force="true" ng-init="ctrl.setFormScope(this)">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span
                                            class="required">*</span>供应商：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="ctrl.edit.a.item.supplier" theme="bootstrap" register-custom-form-control
                                                   firstfocus="{{ctrl.focus.adjust}}" ng-disabled="!ctrl.right.edit || !ctrl.edit.a.isAdd"
                                                   on-select="ctrl.validSelect()" required>
                                            <ui-select-match placeholder="请选择...">{{$select.selected.name}}
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="item in ctrl.refer.supplierList | filter: $select.search">
                                                <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                <small>
                                                    <span style="color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.name}}<br></span>
                                                </small>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>对账期间：</label>
                                    <div class="col-sm-7 col-md-8">
                                         <select class="form-control"
                                                ng-model="ctrl.edit.a.item.period"
                                                ng-disabled="!ctrl.right.edit || !ctrl.edit.a.isAdd"
                                                ng-change="ctrl.selectPeriod(ctrl.edit.a.item,1)"
                                                ng-options="option for option in ctrl.refer.periodList" required>
                                                 <option value="">--请选择--</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>调整金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" ng-model="ctrl.edit.a.item.amount"
                                               ng-show="ctrl.edit.a.isAdd"
                                               ng-checkmoney required/>
                                         <span class="form-control" ng-disabled="true" ng-show="!ctrl.edit.a.isAdd"
                                                  ng-bind="ctrl.edit.a.item.amount | currency:'':2"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>调整理由：</label>

                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-model="ctrl.edit.a.item.adjustReason"
                                                      ng-disabled="!ctrl.right.edit || !ctrl.edit.a.isAdd" required
                                                      ng-maxlength="255"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>

                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-model="ctrl.edit.a.item.remark"
                                                      ng-disabled="!ctrl.right.edit || !ctrl.edit.a.isAdd"
                                                      ng-maxlength="255"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8" ng-if="!ctrl.edit.a.isAdd">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label"><span
                                            class="required">*</span>状态：</label>

                                    <div class="col-sm-7 col-md-10">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.a.item.status | rushRedFilter : ctrl.rushRedStatus"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8"
                                 ng-if="ctrl.edit.a.isRushRed || (ctrl.edit.a.isOnlyDis && ctrl.edit.a.item.status != ctrl.rushRedStatus.unRush.flagId)">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>冲红原因：</label>

                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-model="ctrl.edit.a.item.writeOffCause"
                                                      firstfocus="{{ctrl.focus.writeoff}}" ng-disabled="!ctrl.right.edit || ctrl.edit.a.isOnlyDis"
                                                      ng-maxlength="50" required></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="infoStep2" class="col-sm-offset-4">
                            <button type="reset" class="btn btn-default btn-default-width" style="margin-left:15px;"
                                    ng-if="ctrl.edit.a.isAdd" ng-click="ctrl.resetAdjust(1)"><i
                                    class="fa fa-mail-reply"></i> 重&nbsp;置
                            </button>
                            <button type="submit" class="btn btn-primary btn-default-width" style="margin-left:15px;"
                                    ng-if="ctrl.edit.a.isAdd || ctrl.edit.a.isRushRed"><i class="fa fa-save"></i>
                                {{ctrl.edit.a.submitTitle}}
                            </button>
                            <!-- <button type="reset" class="btn default" ng-if="!ctrl.edit.a.isAdd"
                                    ng-click="ctrl.closeADForm()">关闭
                            </button> -->
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </tab>
    <!-- END 调整详情/编辑 -->

    <!-- 调整明细编辑 -->
     <tab active="ctrl.tabs.aDDetailForm.active" ng-show="ctrl.tabs.aDDetailForm.show">
        <tab-heading>
            手工调整信息 <i class="fa fa-times set-cursor-pointer" ng-click="ctrl.closeAdDetailForm()"></i>
        </tab-heading>
        <div class="rows" ng-if="ctrl.tabs.aDDetailForm.show">
            <div class="portlet light bordered ">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">手工调整信息</div>
                </div>
                <div class="portlet-body form">
                    <form id="infoStep1" class="form-horizontal" role="form" name="adjustDetailEditForm" novalidate="novalidate"
                          ng-submit="ctrl.submitAdjustDetail(adjustDetailEditForm);" ng-submit-force="true" ng-init="ctrl.setFormScope(this)">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span
                                            class="required">*</span>供应商：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="ctrl.edit.a.item.supplier" theme="bootstrap" register-custom-form-control
                                                   firstfocus="{{ctrl.focus.adjust}}" ng-disabled="!ctrl.right.edit || !ctrl.edit.a.isAdd"
                                                   on-select="ctrl.validSelectDetail()" required>
                                            <ui-select-match placeholder="请选择...">{{$select.selected.name}}
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="item in ctrl.refer.supplierList | filter: $select.search">
                                                <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                <small>
                                                    <span style="color:blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.name}}<br></span>
                                                </small>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>对账期间：</label>
                                    <div class="col-sm-7 col-md-8">
                                         <select class="form-control"
                                                ng-model="ctrl.edit.a.item.period"
                                                ng-disabled="!ctrl.right.edit || !ctrl.edit.a.isAdd"
                                                ng-change="ctrl.selectPeriod(ctrl.edit.a.item,2)"
                                                ng-options="option for option in ctrl.refer.periodList" required>
                                                 <option value="">--请选择--</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                               <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span
                                            class="required">*</span>对账明细：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="ctrl.edit.a.item.materialCheck" theme="bootstrap"
                                         ng-disabled="!ctrl.right.edit || !ctrl.edit.a.isAdd" on-select="ctrl.selectDetailPeriod()" required>
                                            <ui-select-match placeholder="请选择...">{{$select.selected.material.no}}
                                            </ui-select-match>
                                            <ui-select-choices
                                                repeat="item in ctrl.materialCheckList | filter: $select.search">
                                                <small>
                                                    编号：<span style="color:blue;">{{item.billNo}}<br></span>
                                                    物料编号：<span style="color:blue;">{{item.material.no}}<br></span>
                                                    物料名称：<span style="color:blue;">{{item.material.name}}<br></span>
                                                    规格型号：<span style="color:blue;">{{item.specification}}<br></span>
                                                    出入库数量：<span style="color:blue;">{{item.quantity}}<br></span>
                                                    单价：<span style="color:blue;">{{item.price}}<br></span>
                                                    金额：<span style="color:blue;">{{item.amount}}<br></span>
                                                </small>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row" ng-if="!ctrl.showDetailAdjustFlag">
                        	<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">金额：</label>
                                    <div class="col-sm-7 col-md-8">
                                       <input class="form-control" disabled="disabled" ng-model="ctrl.edit.a.item.materialCheck.amount" ng-show="ctrl.edit.a.isAdd" ng-checkmoney/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">数量：</label>
                                    <div class="col-sm-7 col-md-8">
                                       <input class="form-control" disabled="disabled" ng-model="ctrl.edit.a.item.materialCheck.quantity" ng-show="ctrl.edit.a.isAdd"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">单价：</label>
	                                <div class="col-sm-7 col-md-8">
	                                    <input class="form-control" type="text" placeholder="单价" ng-model="ctrl.edit.a.item.materialCheck.price" ng-disabled="ctrl.edit.a.item.materialCheck.inOutFlag != 1" ng-show="ctrl.edit.a.isAdd" ng-blur="ctrl.doOthersPrice()">
	                                </div>
	                            </div>
                        	</div>
                        </div>

                        <div class="row" ng-if="ctrl.showDetailAdjustFlag">
                        	<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">调整金额：</label>
                                    <div class="col-sm-7 col-md-8">
                                       <input class="form-control" ng-model="ctrl.edit.a.item.amount" disabled="disabled" ng-checkmoney/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">数量：</label>
                                    <div class="col-sm-7 col-md-8">
                                       <input class="form-control" disabled="disabled" ng-model="ctrl.edit.a.item.materialCheck.quantity"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.a.item.adjustPriceStr">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">调整单价：</label>
	                                <div class="col-sm-7 col-md-8">
	                                    <input class="form-control" type="text" placeholder="单价" ng-model="ctrl.edit.a.item.adjustPriceStr" disabled="disabled" ng-blur="ctrl.doOthersPrice()">
	                                </div>
	                            </div>
                        	</div>
                        </div>

                        <div class="row" ng-if="!ctrl.showDetailAdjustFlag">
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.a.item.materialCheck.materialProperty == '成品' && ctrl.edit.a.item.materialCheck.inOutFlag == 1">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">工程费用：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" placeholder="工程费用" ng-model="ctrl.edit.a.item.materialCheck.engineeringFee" ng-show="ctrl.edit.a.isAdd" ng-blur="ctrl.doOthersPrice()">
                                    </div>
                                </div>
                         	 </div>
                        	 <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.a.item.materialCheck.materialProperty == '成品' && ctrl.edit.a.item.materialCheck.inOutFlag == 1">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">模具费用：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" placeholder="模具费用" ng-model="ctrl.edit.a.item.materialCheck.mouldFee" ng-show="ctrl.edit.a.isAdd" ng-blur="ctrl.doOthersPrice()" ng-onlydecimals>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.a.item.materialCheck.materialProperty == '成品' && ctrl.edit.a.item.materialCheck.inOutFlag == 1">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">测试架费：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" placeholder="测试架费" ng-model="ctrl.edit.a.item.materialCheck.testShelfFee" ng-show="ctrl.edit.a.isAdd" ng-blur="ctrl.doOthersPrice()" ng-onlydecimals>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.a.item.materialCheck.materialProperty == '成品' && ctrl.edit.a.item.materialCheck.inOutFlag == 1">
                               <div class="form-group">
                                   <label class="col-sm-3 col-md-4 control-label">其它费用：</label>
                                   <div class="col-sm-7 col-md-8">
                                       <input class="form-control" type="text" placeholder="其它费用" ng-model="ctrl.edit.a.item.materialCheck.othersFee" ng-show="ctrl.edit.a.isAdd" ng-blur="ctrl.doOthersPrice()" ng-onlydecimals>
                                   </div>
                               </div>
                         	</div>
 	                         <div class="col-md-6 col-lg-4">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">费用：</label>
	                                <div class="col-sm-7 col-md-8">
	                                    <input class="form-control" type="text" placeholder="费用" ng-model="ctrl.edit.a.item.fee" ng-show="ctrl.edit.a.isAdd" ng-blur="ctrl.doOthersPrice()">
	                                </div>
	                            </div>
                        	</div>
                        </div>

                        <div class="row" ng-if="ctrl.showDetailAdjustFlag">
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.a.item.adjustEngineeringFeeStr && ctrl.edit.a.item.materialCheck.materialProperty == '成品' && ctrl.edit.a.item.materialCheck.inOutFlag == 1">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">调整工程费用：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" placeholder="工程费用" ng-model="ctrl.edit.a.item.adjustEngineeringFeeStr" disabled="disabled" ng-blur="ctrl.doOthersPrice()">
                                    </div>
                                </div>
                         	 </div>
                        	 <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.a.item.adjustMouldFeeStr && ctrl.edit.a.item.materialCheck.materialProperty == '成品' && ctrl.edit.a.item.materialCheck.inOutFlag == 1">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">调整模具费用：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" placeholder="调整模具费用" ng-model="ctrl.edit.a.item.adjustMouldFeeStr" disabled="disabled">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.a.item.adjustTestShelfFeeStr && ctrl.edit.a.item.materialCheck.materialProperty == '成品' && ctrl.edit.a.item.materialCheck.inOutFlag == 1">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">调整测试架费：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" placeholder="调整测试架费" ng-model="ctrl.edit.a.item.adjustTestShelfFeeStr" disabled="disabled">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="ctrl.edit.a.item.adjustOthersFeeStr && ctrl.edit.a.item.materialCheck.materialProperty == '成品' && ctrl.edit.a.item.materialCheck.inOutFlag == 1">
                               <div class="form-group">
                                   <label class="col-sm-3 col-md-4 control-label">调整其它费用：</label>
                                   <div class="col-sm-7 col-md-8">
                                       <input class="form-control" type="text" placeholder="调整其它费用" ng-model="ctrl.edit.a.item.adjustOthersFeeStr" disabled="disabled">
                                   </div>
                               </div>
                         	</div>
                         	<div class="col-md-6 col-lg-4">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">调整费用：</label>
	                                <div class="col-sm-7 col-md-8">
	                                    <input class="form-control" type="text" placeholder="调整费用" ng-model="ctrl.edit.a.item.fee" disabled="disabled">
	                                </div>
	                            </div>
                        	</div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>调整理由：</label>

                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-model="ctrl.edit.a.item.adjustReason"
                                                      ng-disabled="!ctrl.right.edit || !ctrl.edit.a.isAdd" required
                                                      ng-maxlength="255"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>

                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-model="ctrl.edit.a.item.remark"
                                                      ng-disabled="!ctrl.right.edit || !ctrl.edit.a.isAdd"
                                                      ng-maxlength="255"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8" ng-if="!ctrl.edit.a.isAdd">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label"><span
                                            class="required">*</span>状态：</label>

                                    <div class="col-sm-7 col-md-10">
                                            <span class="form-control" ng-disabled="true"
                                                  ng-bind="ctrl.edit.a.item.status | rushRedFilter : ctrl.rushRedStatus"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8"
                                 ng-if="ctrl.edit.a.isRushRed || (ctrl.edit.a.isOnlyDis && ctrl.edit.a.item.status != ctrl.rushRedStatus.unRush.flagId)">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>冲红原因：</label>

                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control" ng-model="ctrl.edit.a.item.writeOffCause"
                                                      firstfocus="{{ctrl.focus.writeoff}}" ng-disabled="!ctrl.right.edit || ctrl.edit.a.isOnlyDis"
                                                      ng-maxlength="50" required></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="infoStep2" class="col-sm-offset-4">
                            <button type="reset" class="btn btn-default btn-default-width" style="margin-left:15px;"
                                    ng-if="ctrl.edit.a.isAdd" ng-click="ctrl.resetAdjust(2)"><i
                                    class="fa fa-mail-reply"></i> 重&nbsp;置
                            </button>
                            <button type="submit" class="btn btn-primary btn-default-width" style="margin-left:15px;"
                                    ng-if="ctrl.edit.a.isAdd || ctrl.edit.a.isRushRed"><i class="fa fa-save"></i>
                                {{ctrl.edit.a.submitTitle}}
                            </button>
                            <!-- <button type="reset" class="btn default" ng-if="!ctrl.edit.a.isAdd"
                                    ng-click="ctrl.closeADForm()">关闭
                            </button> -->
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </tab>

	<!-- 总部经济对账单 -->
    <tab heading="总部经济外协对账单" ng-if="ctrl.ValiteCompanyUtil.valiteSaleCompany" active="ctrl.tabs.outForm.active" ng-click="ctrl.loadOutSourceData(2)">
        <div class="rows">
            <div id="billStep6" class="panel panel-default">
                <div class="panel-heading font-blue-hoki">查询</div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商编号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-model="ctrl.supNoQuery" disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商名称：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-model="ctrl.supNameQuery" disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">对账月份：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" placeholder="请输入对账月份，年/月/年月" ng-model="ctrl.periodQuery" ng-blur="ctrl.loadPeriodList(2)" disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
							    <div class="form-group">
								    <label class="col-sm-3 col-md-4 control-label">状态：</label>
								    <div class="col-sm-7 col-md-8">
									     <select class="form-control" ng-model="ctrl.query.status.value" ng-options="gen.value as gen.name for gen in ctrl.query.statusList" disable-auto-validate="true"> </select>
								    </div>
							    </div>
							</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default btn-default-width pull-right" ng-click="ctrl.doOutQuery()">
                                    <i class="fa fa-search"></i> 查询
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">对账单列表</div>
                    <div id="billStep7" class="actions">
                        <div class="portlet-input input-inline input-small">
                            <button type="button" class="btn btn-primary btn-default-width"
                                    ng-click="ctrl.queryAdjust()"><i class="fa fa-search-minus"></i> 查询调整
                            </button>
                        </div>
                        <div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
                            <button type="button" class="btn green btn-default-width" ng-click="ctrl.addAdjust(1)"><i
                                    class="fa fa-plus"></i> 新增调整
                            </button>
                        </div>
                        <div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
                            <button type="button" class="btn green btn-default-width" ng-click="ctrl.addAdjust(2)"><i
                                    class="fa fa-plus"></i> 新增明细调整
                            </button>
                        </div>
                        <div class="portlet-input input-inline input-small">
	                        <form action="a/purch/payable/exportPayable" method="POST" enctype="multipart/form-data" target="hidden_frame">
	                            <input type="text" ng-show="false"  name="supplier.no" value="{{ctrl.supNoQuery}}"/>
	                            <input type="text" ng-show="false"  name="supplier.name" value="{{ctrl.supNameQuery}}"/>
	                            <input type="text" ng-show="false"  name="period" value="{{ctrl.periodQuery}}"/>
  	                            <input type="text" ng-show="false" name="status" value="{{ctrl.query.status.value}}"/>
                              	<input type="text" ng-show="false" name="dateType" value="{{ctrl.dateType}}"/>
	                            <input type="text" ng-show="false"  name="queryAll" value="{{ctrl.queryAll}}"/>
	                            <input type="text" ng-show="false"  name="lnCompanyId" value="{{ctrl.lnCompanyId}}"/>
	                            <div ng-if="ctrl.right.view" >
	                                <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i>导出付款对账单</button>
	                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
	                            </div>
	                        </form>
                    	</div>

                    	<div class="portlet-input input-inline input-small">
	                        <form action="a/purch/payable/exportPayableAllDetail" method="POST" enctype="multipart/form-data" target="hidden_frame">
	                            <input type="text" ng-show="false"  name="supplier.no" value="{{ctrl.supNoQuery}}"/>
	                            <input type="text" ng-show="false"  name="supplier.name" value="{{ctrl.supNameQuery}}"/>
	                            <input type="text" ng-show="false"  name="period" value="{{ctrl.periodQuery}}"/>
	                            <input type="text" ng-show="false" name="status" value="{{ctrl.query.status.value}}"/>
                              	<input type="text" ng-show="false" name="dateType" value="{{ctrl.dateType}}"/>
	                            <input type="text" ng-show="false"  name="queryAll" value="{{ctrl.queryAll}}"/>
	                            <input type="text" ng-show="false"  name="lnCompanyId" value="{{ctrl.lnCompanyId}}"/>
	                            <div ng-if="ctrl.right.view" >
	                                <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i>导出付款对账单明细</button>
	                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
	                            </div>
	                        </form>
                    	</div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div id="billStep1" class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading">
                                <th id="billStep2" ng-class="{'sorting': ctrl.sort.supplierNo.both, 'sorting_desc': ctrl.sort.supplierNo.desc, 'sorting_asc': ctrl.sort.supplierNo.asc}"
                                			ng-click="ctrl.sortClick('supplierNo')">供应商编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
	                            <th ng-class="{'sorting': ctrl.sort.supplierName.both, 'sorting_desc': ctrl.sort.supplierName.desc, 'sorting_asc': ctrl.sort.supplierName.asc}"
	                            			ng-click="ctrl.sortClick('supplierName')">供应商名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th ng-class="{'sorting': ctrl.sort.period.both, 'sorting_desc': ctrl.sort.period.desc, 'sorting_asc': ctrl.sort.period.asc}"
                                			ng-click="ctrl.sortClick('period')">对账月份&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>本期货款</th>
                                <th>内部货款</th>
                            	<th>外协货款</th>
                            	<th>账单属性</th>
                                <th id="billStep3">调整金额</th>
                                <th>发票</th>
                                <th>状态</th>
                                <th>本期已付</th>
                                <th>本期未付</th>
                                <th>逾期天数</th>
                                <th>平米数</th>
                                <th>PCS数</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in ctrl.page.r.data.list" ng-dblclick="ctrl.disRDetail($index)" ng-click="ctrl.showRDetailTotal($index)" ng-class="{'danger':!row.rcvFlag,'info':row.statusFlag}" ng-if="row.adjustFlag">
                                <td><a ng-click="ctrl.disRDetail($index)">{{row.supplier.no}}</a></td>
                                <td ng-bind="row.supplier.name"></td>
                                <td ng-bind="row.period"></td>
                                <td class="text-right" ng-bind="row.materialValue | currency:'':2"></td>
                                <td class="text-right" ng-bind="row.jxAmount | currency:'':2"></td>
                            	<td class="text-right" ng-bind="row.otherAmount | currency:'':2"></td>
                            	<td ng-bind="row.materialProperty"></td>
                                <td class="text-right"><a href="#" ng-click="ctrl.queryAdjust($index)">{{row.adjustValue | currency:'':2}}</a></td>
                                <td ng-if="row.invoiceFlag=='1'" style="color: #b53232" >有</td>
                                <td ng-if="!row.invoiceFlag || row.invoiceFlag=='2'">无</td>
	                            <td ng-if="row.status == 2001">未确认</td>
	                            <td ng-if="row.status == 2002">已确认</td>
	                            <td ng-if="row.status == 2003">已完成</td>
	                            <td ng-if="row.status == 2004">未复核</td>
	                            <td ng-if="row.status == 2005">已复核</td>
	                            <td ng-if="row.status == 60001">审批中</td>
                                <td class="text-right" ng-bind="row.paiedAmount | currency:'':2"></td>
                                <td class="text-right" ng-bind="row.rcvAmount | currency:'':2"></td>
                                <td class="text-right" ng-bind="row.overDueDaysStr"></td>
                                <td class="text-right" ng-bind="row.area"></td>
                                <td class="text-right" ng-bind="row.quantity"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row page-margin-top">
                        <div id="billStep5" class="col-md-12 col-lg-6">
                            <span class="inline">每页</span>
                            <select class="form-control inline" style="margin-top:8px; width:100px;"
                                    disable-valid-styling="true"
                                    disable-invalid-styling="true"
                                    ng-model="ctrl.page.r.pageSize"
                                    ng-change="ctrl.pageSizeChange(0)"
                                    ng-options="option for option in ctrl.page.options">
                            </select>
                            <span class="inline">条详情&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{ctrl.page.r.data.startCount}} / {{ctrl.page.r.data.endCount}} 条，共 {{ctrl.page.r.data.count}} 条</span>
                        </div>
                        <div id="billStep4" class="col-md-12 col-lg-6">
                            <paging
                                    class="pull-right"
                                    page="ctrl.page.r.data.pageNo"
                                    page-size="ctrl.page.r.data.pageSize"
                                    total="ctrl.page.r.data.count"
                                    adjacent="1"
                                    dots="..."
                                    scroll-top="false"
                                    hide-if-empty="false"
                                    ul-class="pagination"
                                    active-class="active"
                                    disabled-class="disabled"
                                    show-prev-next="true"
                                    paging-action="ctrl.doPage(0, page, pageSize)">
                            </paging>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <ul id="receStep8" class="list-inline">
            <li><a class="list-group-item list-group-item-info btn-default-width" ng-click="ctrl.filterBy(2003)">已完成</a></li>
        </ul>
    </tab>

</tabset>

<div id="exportData" class="modal fade" tabindex="-1" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">数据引入</h4>
            </div>
            <div class="modal-body clearfix">
                <div class="row">
                    <div class="col-md-12">
                        <button class="btn btn-secondary">引入模式</button>
                    </div>
                </div>
                <div class="row portlet light bordered">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-4">
                                        <input type="radio" disabled/>追加
                                    </div>
                                    <div class="col-md-4">
                                        <input type="checkbox" disabled>仅暂存
                                    </div>
                                    <div class="col-md-4">
                                        <input type="checkbox" disabled>暂存引入失败数据
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="padding-top: 1rem;">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-4">
                                        <input type="radio" checked disabled/>覆盖
                                    </div>
                                    <div class="col-md-4">
                                        <input type="checkbox" disabled>运行追加
                                    </div>
                                    <div class="col-md-4">
                                        <input type="checkbox" checked disabled>匹配字段
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        请选择要引入的数据文件
                        <form name="form1" enctype="multipart/form-data" id="form1" ng-submit="ctrl.uploadExcel();" style="padding-top: 0.5rem;">
                            <div class="row" style="padding-bottom: 1rem;">
                                <div class="col-md-12">
                                    <span class="btn btn-default" style="position: relative;display: inline-block;overflow: hidden;">
                                        <input id="upfile" name="upfile" type="file"/>
                                    </span>
                                </div>
                            </div>
                        </form>
                        <div class="row portlet light bordered">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-9">
                                        说明：数据文件需符合模板要求，若无模板请下载
                                    </div>
                                    <div class="col-md-3">
                                       <div class="portlet-input input-inline input-small">
											<form action="a/crm/uploadSetting/export"
												method="post" enctype="multipart/form-data" target="hidden_frame">
												<input type="text" ng-show="false" name="customerId"
													value="{{ctrl.edit.r.item.supplier.recordId}}" />
												<input type="text" ng-show="false" name="orderType"
													value="2" />
												<button type="submit"  style="border: none;background-color: #fff;color: dodgerblue;">
													<i class="fa fa-download"></i> 引入模板
												</button>
												<iframe name="hidden_frame" id="hidden_frame"
													style="display: none"></iframe>
											</form>
										</div>

                                        <!-- <button ng-click="ctrl.runDownloadFile()" style="border: none;background-color: #fff;color: dodgerblue;"><i class="fa fa-download"></i> 引入模板</button> -->
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12" style="padding-top: 1rem;">
                                <div class="row">
                                    <div class="col-md-9">
                                        &emsp;&emsp;&emsp;引入失败的数据，可下载修复后重新引入
                                    </div>
                                    <div class="col-md-3">
                                        <form id="exportForm" action="a/purch/payable/failReconciliationDataExport" method="POST" enctype="multipart/form-data" target="hidden_frame">
                                            <input type="text" ng-show="false" name="customerId" value="{{ctrl.edit.r.item.supplier.recordId}}"/>
                                            <input type="text" ng-show="false" name="period" value="{{ctrl.edit.r.item.period}}"/>
                                            <input type="text" ng-show="false" name="type" value="1"/>
                                            <div ng-if="ctrl.right.view" >
                                                <button type="submit" style="border: none;background-color: #fff;color: dodgerblue;"><i class="fa fa-download"></i> 失败数据</button>
                                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12" style="padding-top: 1rem;">
                                <div class="row">
                                    <div class="col-md-9">
                                        &emsp;&emsp;&emsp;引入成功的数据，可下载查看
                                    </div>
                                    <div class="col-md-3">
                                        <form action="a/purch/payable/failReconciliationDataExport" method="POST" enctype="multipart/form-data" target="hidden_frame">
                                            <input type="text" ng-show="false"  name="customerId" value="{{ctrl.edit.r.item.supplier.recordId}}"/>
                                            <input type="text" ng-show="false"  name="period" value="{{ctrl.edit.r.item.period}}"/>
                                            <input type="text" ng-show="false" name="type" value="2"/>
                                            <div ng-if="ctrl.right.view" >
                                                <button type="submit" style="border: none;background-color: #fff;color: dodgerblue;"><i class="fa fa-download"></i> 成功数据</button>
                                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 text-right">
                                <button type="button" class="btn btn-default" ng-click="ctrl.uploadExcel();">引入数据</button>
                                <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                            </div>
                        </div>
                        <div class="row" style="padding-top: 1rem;" ng-if="ctrl.processFlag && ctrl.processFlag == '1'">
                            <div class="col-md-12">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" ng-style="{ 'width': ctrl.processNum + '%' }">
                                        <span class="sr-only">{{ctrl.processNum}}%</span>
                                    </div>
                                </div>
                                <div>
                                    <span>{{ctrl.dataNowMsg==""?"数据处理中":ctrl.dataNowMsg}}...{{ctrl.dataNowNum}}/{{ctrl.dataSumNum}}</span>
                                </div>
                            </div>
                        </div>
                        <div style="height: 50px; padding-top: 1rem;">
                            <span class="text-danger">{{ctrl.exportMessage}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="downStatic" class="modal fade" tabindex="-1" data-keyboard="false" data-backdrop="static">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">导入数据提示</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;" ng-bind="ctrl.exportMessage"></div>
                    <div class="modal-footer">
                        <form action="a/purch/payable/failReconciliationDataExport" method="POST" enctype="multipart/form-data" target="hidden_frame">
                            <input type="text" ng-show="false" name="customerId" value="{{ctrl.edit.r.item.supplier.recordId}}"/>
                            <input type="text" ng-show="false" name="period" value="{{ctrl.edit.r.item.period}}"/>
                            <input type="text" ng-show="false" name="type" value="1"/>
                            <div ng-if="ctrl.right.view" >
                                <button type="submit" class="btn btn-default" ng-click="ctrl.confimExcel()" ng-if="ctrl.needDownErrorFile">确认</button>
                                <button type="button" class="btn btn-default" ng-click="ctrl.confimExcel()" ng-if="!ctrl.needDownErrorFile">确认</button>
                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="openAdjust" class="modal fade" tabindex="-1" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                异常单据处理
            </div>
            <div class="modal-body clearfix" style="padding-top: 25px;">
                <div class="form-horizontal">
                    <div class="row" ng-if="ctrl.adjustFlag == 1">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="control-label col-md-3">单据编号：</label>
                                <div class="col-md-8">
                                    <input class="form-control" type="text" placeholder="单据编号" value="TZ0001" readonly="readonly">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-md-3">本次对账数量：</label>
                                <div class="col-md-8">
                                    <input class="form-control" type="text" placeholder="单据编号" value="7000">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-default">确定</button>
                <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
            </div>
        </div>
    </div>
</div>

<div class="row">
	<div class="col-md-12">
		<div class="modal fade" id="showGroupCustomer" tabindex="-1" role="dialog" aria-hidden="true">
		    <div class="modal-dialog modal-full">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span class="text-primary">
                        	{{ctrl.groupShortName}}异常客户表
                        </span></h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 5px;">
						<form class="form-horizontal">
							<div class="portlet light bordered">
                 				<div class="portlet-body">
									<div class="row form-group">
										 <div class="table-scrollable">
					                      <table class="table table-striped table-bordered table-condensed table-advance table-hover">
					                          <thead>
					                          <tr class="heading">
					                          	<th>终端客编</th>
					                          	<th>异常金额</th>
					                          	<!-- <th>异常工艺</th> -->
					                          </tr>
					                          </thead>
					                          <tbody>
					                          	<tr ng-repeat="item in ctrl.groupCustList" class="success">
					                          		<td>{{item.custNo}}</td>
					                          		<td>
					                          			<span ng-if="item.moneyQuo && item.moneyQuo.length > 0">
					                          				<a href="javascript:void(0)" ng-click="ctrl.showGroupMoneyQuo(item.moneyQuo,1)">
						                        				{{item.moneyQuo.length}}
						                        			</a>
						                        		</span>
					                          		</td>
					                          		<!-- <td>
					                          			<span ng-if="item.craftQuo && item.craftQuo.length > 0">
					                          				<a href="javascript:void(0)" ng-click="ctrl.showGroupMoneyQuo(item.craftQuo,2)">
						                        				{{item.craftQuo.length}}
						                        			</a>
						                        		</span>
					                          		</td> -->
					                          	</tr>
					                          </tbody>
					                      </table>
					                  </div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div class="modal fade" id="showGroupMoneyQuo" tabindex="-1" role="dialog" aria-hidden="true">
		    <div class="modal-dialog modal-full">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span class="text-primary">
                        	{{ctrl.groupShortName}}异常{{ctrl.groupName}}表
                        </span></h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 5px;">
						<form class="form-horizontal">
							<div class="portlet light bordered">
                 				<div class="portlet-body">
									<div class="row form-group">
										 <div class="table-scrollable">
					                      <table class="table table-primary table-bordered table-condensed table-primary table-hover">
					                          <thead style="background-color: #fffefe;">
					                          <tr>
					                         	<th colspan='1' class="text-center">异常</th>
					                         	<th colspan="6" class="text-center">销售</th>
					                         	<th colspan="6" class="text-center">龙南</th>
					                         	<th colspan="6" class="text-center">江西</th>
					                         </tr>
					                          <tr class="heading">
					                          	<th>异常{{ctrl.groupName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					                          	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
					                          	<th>销售公司合同</th>
					                          	<th>销售公司厂编</th>
					                          	<th>销售公司型号</th>
					                          	<th>销售公司价格</th>
					                          	<th>销售公司数量</th>
					                          	<th>销售公司金额</th>
					                          	<th>龙南合同</th>
					                          	<th>龙南厂编</th>
					                          	<th>龙南型号</th>
					                          	<th>龙南价格</th>
					                          	<th>龙南数量</th>
					                          	<th>龙南金额</th>
					                          	<th>江西合同</th>
					                          	<th>江西厂编</th>
					                          	<th>江西型号</th>
					                          	<th>江西价格</th>
					                          	<th>江西数量</th>
					                          	<th>江西金额</th>
					                          </tr>
					                          </thead>
					                          <tbody>
					                          	<tr ng-repeat="item in ctrl.groupMoneyQuoList" class="success">
					                          		<td ng-if="ctrl.groupName == '金额'">
					                          			<span class="text-danger"
					                						ng-if="item.priceException && item.priceException == '1'">
					                						销售单价：{{item.xsprice}}<br>
					                						销售采购价：{{item.xsPurchPrice}}<br>
					                						龙南单价：{{item.lnprice}}<br>
					                						龙南单价=成本价{{item.sourcePrice}} * {{item.lnPrecent}}%<br>
					                						龙南采购价：{{item.lnPurchPrice}}<br>
					                						生产单价：{{item.jxprice}}<br>
					                						生产单价=成本价{{item.sourcePrice}} * {{item.jxPrecent}}%<br>
					                					</span>
					                					<span class="text-danger"
					                						ng-if="item.engineeringFeeException && item.engineeringFeeException == '1'">
					                						销售工程费：{{item.xsengineeringFee}}<br>
					                						龙南工程费：{{item.lnengineeringFee}}<br>
					                						生产工程费：{{item.jxengineeringFee}}<br>
					                					</span>
					                					<span class="text-danger"
					                						ng-if="item.testShelfFeeException && item.testShelfFeeException == '1'">
					                						销售测试架费：{{item.xstestShelfFee}}<br>
					                						龙南测试架费：{{item.lntestShelfFee}}<br>
					                						生产测试架费：{{item.jxtestShelfFee}}<br>
					                					</span>
					                					<span class="text-danger"
					                						ng-if="item.mouldFeeException && item.mouldFeeException == '1'">
					                						销售模具费：{{item.xsmouldFee}}<br>
					                						龙南模具费：{{item.lnmouldFee}}<br>
					                						生产模具费：{{item.jxmouldFee}}<br>
					                					</span>
					                					<span class="text-danger"
					                						ng-if="item.filmFeeException && item.filmFeeException == '1'">
					                						销售菲林费：{{item.xsfilmFee}}<br>
					                						龙南菲林费：{{item.lnfilmFee}}<br>
					                						生产菲林费：{{item.jxfilmFee}}<br>
					                					</span>
					                					<span class="text-danger"
					                						ng-if="item.othersFeeException && item.othersFeeException == '1'">
					                						销售其它费：{{item.xsothersFee}}<br>
					                						龙南其它费：{{item.lnothersFee}}<br>
					                						生产其它费：{{item.jxothersFee}}<br>
					                					</span>
					                					<span class="text-danger"
					                						ng-if="item.engineeringLimitException && item.engineeringLimitException == '1'">
					                						销售工程返还费：{{item.xsengineeringLimit}}<br>
					                						龙南工程返还费：{{item.lnengineeringLimit}}<br>
					                						生产工程返还费：{{item.jxengineeringLimit}}<br>
					                					</span>
					                					<span class="text-danger"
					                						ng-if="item.mouldLimitException && item.mouldLimitException == '1'">
					                						销售模具返还费：{{item.xsmouldLimit}}<br>
					                						龙南模具返还费：{{item.lnmouldLimit}}<br>
					                						生产模具返还费：{{item.jxmouldLimit}}<br>
					                					</span>
					                					<span class="text-danger"
					                						ng-if="item.testShelfLimitException && item.testShelfLimitException == '1'">
					                						销售测试架返还费：{{item.xstestShelfLimit}}<br>
					                						龙南测试架返还费：{{item.lntestShelfLimit}}<br>
					                						生产测试架返还费：{{item.jxtestShelfLimit}}<br>
					                					</span>
					                          		</td>
					                          		<td ng-if="ctrl.groupName == '工艺'">
					                					<span class="text-danger"
					                						ng-if="item.outStockException && item.outStockException == '1'">
					                						销售出库：{{item.xsoutStock}}<br>
					                						龙南出库：{{item.lnoutStock}}<br>
					                						生产出库：{{item.jxoutStock}}<br>
					                					</span>
					                          		</td>
					                          		<td>{{item.xsConNo}}</td>
					                          		<td>{{item.xsno}}</td>
					                          		<td>
					                          			<span ng-attr-title="{{item.xscustomerModel}}" ng-if="item.xscustomerModel && item.xscustomerModel.length > 20">
						                            		{{item.xscustomerModel.substring(0,20)}}...
						                            	</span>
						                            	<span ng-if="!(item.xscustomerModel && item.xscustomerModel.length > 20)">
						                            		{{item.xscustomerModel}}
						                            	</span>
						                            </td>
					                          		<td>{{item.xsprice}}</td>
					                          		<td>{{item.xsquantity}}</td>
					                          		<td>{{item.xssubTotal}}</td>
					                          		<td>{{item.lnConNo}}</td>
					                          		<td>{{item.lnno}}</td>
					                          		<td>
					                          			<span ng-attr-title="{{item.lncustomerModel}}" ng-if="item.lncustomerModel && item.lncustomerModel.length > 20">
						                            		{{item.lncustomerModel.substring(0,20)}}...
						                            	</span>
						                            	<span ng-if="!(item.lncustomerModel && item.lncustomerModel.length > 20)">
						                            		{{item.lncustomerModel}}
						                            	</span>
						                            </td>
					                          		<td>{{item.lnprice}}</td>
					                          		<td>{{item.lnquantity}}</td>
					                          		<td>{{item.lnsubTotal}}</td>
					                          		<td>{{item.jxConNo}}</td>
					                          		<td>{{item.jxno}}</td>
					                          		<td>
					                          			<span ng-attr-title="{{item.jxcustomerModel}}" ng-if="item.jxcustomerModel && item.jxcustomerModel.length > 20">
						                            		{{item.jxcustomerModel.substring(0,20)}}...
						                            	</span>
						                            	<span ng-if="!(item.jxcustomerModel && item.jxcustomerModel.length > 20)">
						                            		{{item.jxcustomerModel}}
						                            	</span>
						                            </td>
					                          		<td>{{item.jxprice}}</td>
					                          		<td>{{item.jxquantity}}</td>
					                          		<td>{{item.jxsubTotal}}</td>
					                          	</tr>
					                          </tbody>
					                      </table>
					                  </div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div class="modal fade" id="nextPeriodAdjustStatic" tabindex="-1" role="dialog" aria-hidden="true">
		    <div class="modal-dialog modal-full">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span class="text-primary">
                        	转期调整{{ctrl.edit.r.item.period}}至{{ctrl.adjustPeriod}}
                        </span></h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 5px;">
						<form class="form-horizontal">
							<div class="portlet light bordered">
								<div class="actions">
									<div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
										<button type="button" class="btn green btn-default-width" ng-click="ctrl.confirmAdjust()">确认调整</button>
									</div>
		               		    </div>
                 				<div class="portlet-body">
									<div class="row form-group">
										 <div class="table-scrollable">
					                      <table class="table table-striped table-bordered table-condensed table-advance table-hover">
					                          <thead>
					                          <tr class="heading">
					                          	<th>单据编号</th>
					                            <th>终端供应商</th>
					                            <th>物料编号</th>
					                            <th>物料名称</th>
					                            <th>数量</th>
					                            <th>调整数量</th>
					                            <th>剩余数量</th>
					                          </tr>
									          <tr>
									          	<td>
													<input type="text" class="form-control" ng-model="ctrl.billNoSearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="ctrl.supplierNameSearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="ctrl.matNoSearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="ctrl.matNameSearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="ctrl.quantitySearch">
												</td>
												<td></td>
												<td></td>
											 </tr>
					                          </thead>
					                          <tbody>
					                          	<tr ng-repeat="item in ctrl.nextAdjustList |
													filter:{
														'billNo':ctrl.billNoSearch,
														'supplierName':ctrl.supplierNameSearch,
														'matNo':ctrl.matNoSearch,
														'matName':ctrl.matNameSearch,
														'quantity':ctrl.quantitySearch
													}" class="success">
													<td>{{item.billNo}}</td>
													<td>{{item.supplierName}}</td>
					                          		<td>{{item.matNo}}</td>
					                          		<td>{{item.matName}}</td>
					                          		<td>{{item.quantity}}</td>
					                          		<td><input type="text" class="form-control" ng-model="item.adjustQty" ng-blur="ctrl.changeAdjustQty(item)" ng-onlynumberandzero></td>
					                          		<td>{{item.residueQty}}</td>
					                          	</tr>
					                          </tbody>
					                      </table>
					                  </div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

 <div class="row">
    <div id="staticAdjust" class="modal fade" tabindex="-1" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                 <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true" ng-click="ctrl.cancel()"></button>
                </div>
                <div class="modal-body">
                    <p><span ng-bind="ctrl.message"></span></p>
                </div>
                <div class="modal-footer">
                    <button type="button" data-dismiss="modal" class="btn btn-default" ng-click="ctrl.periodSelect()">确定</button>
                    <button type="button" data-dismiss="modal" class="btn btn-default" ng-click="ctrl.cancel()">取消</button>
                </div>
            </div>
        </div>
    </div>
</div>

 <div class="row">
    <div id="staticReturnPayable" class="modal fade" tabindex="-1" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                 <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title">反审账单</h4>
                </div>
				<div class="modal-body clearfix" style="padding-top: 25px;">
					<div class="form-horizontal">
						<div class="row">
							<div class="col-md-12">
								<div class="form-group">
									<label class="control-label col-md-3"><span class="required">*</span>反审原因：</label>
									<div class="col-md-8">
										<textarea  class="form-control" placeholder="反审原因" ng-model="ctrl.edit.r.item.counterTrialCause"></textarea>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
                <div class="modal-footer">
                    <button type="button" data-dismiss="modal" class="btn btn-default" ng-click="ctrl.returnPayable()">确定</button>
                    <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="uploadInvoiceDiv" class="modal fade bs-example-modal-lg in" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-hidden="true" >
	<div class="modal-dialog modal-lg">
        <div class="modal-content">
        	<div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" ng-disabled="ctrl.edit.upload"  ng-click="ctrl.confirmUplodFile()"></button> <h4 class="modal-title">添加附件</h4>
            </div>
            <div class="modal-body">
			    <div class="portlet light bordered">
			            <div class="portlet-title">
			                <div class="caption font-green-sharp">
			                    <i class="icon-cloud-upload font-green-sharp"></i>
			                    <span class="caption-subject bold uppercase">上传列表</span>
			                </div>
			                <div class="actions">
			                    <div class="portlet-input input-inline input-small">
			                        <button class="btn green" ng-if="ctrl.right.edit"
			                                ngf-select="ctrl.uploadFiles($files, $invalidFiles)" multiple
			                                ngf-max-size="500MB" ng-disabled="ctrl.edit.upload" >
			                            <span class="glyphicon glyphicon-cloud-upload"></span>上传原始凭据
			                        </button>
			                    </div>
			                </div>
			            </div>
			            <div class="portlet-body">
			                <div class="table-scrollable table-scrollable-borderless">
			                    <table class="table table-hover table-light">
			                        <thead>
			                        <tr class="uppercase">
			                            <th width="50%">文件名</th>
			                            <th>大小</th>
			                            <th>进度</th>
			                            <th class="text-center">状态</th>
			                        </tr>
			                        </thead>
			                        <tbody>
			                        <tr ng-repeat="item in ctrl.uFiles">
			                            <td><strong>{{ item.name }}</strong></td>
			                            <td>{{ item.size/1024|number:3 }} KB</td>
			                            <td>
			                                <div class="progress progress-sm" style="margin-bottom: 0;">
			                                    <div class="progress-bar progress-bar-info" role="progressbar" ng-style="{ 'width': item.progress + '%' }"></div>
			                                </div>
			                            </td>
			                            <td class="text-center">
			                                <span ng-show="item.status < 1 || item.status == 200" class="text-success"><i class="glyphicon glyphicon-ok"></i></span>
			                                <span ng-show="item.status == 0" class="text-info"><i class="glyphicon glyphicon-ban-circle"></i></span>
			                                <span ng-show="item.status > 200" class="text-danger"><i class="glyphicon glyphicon-remove"></i></span>
			                            </td>
			                        </tr>
			                        </tbody>
			                    </table>
			                </div>
			            </div>
			        </div>
			    </div>
			    <div class="modal-footer">
                    <button type="button"  class="btn btn-primary btn-default-width" data-dismiss="modal" ng-disabled="ctrl.edit.upload" ng-click="ctrl.confirmUplodFile()"  style="margin-left:45px;">确&nbsp;定</button>
                </div>
	        </div>
        </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="staticInvoiceRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">删除原始凭据</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="ctrl.delMsg"></p></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.doDelFile()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div id="payAdjustArriveDate" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">票到日期</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="form-horizontal">
                            <div class="row">
                                <div class="col-md-6 col-lg-6">
                                    <div class="form-group">
                                        <label class="col-sm-3 col-md-5 control-label"><span class="required">*</span>选择日期：</label>
                                        <div class="col-sm-7 col-md-7">
                                            <input ng-model="ctrl.adjustDate"
                                                   placeholder="请选择日期" type="text" class="form-control"
                                                   data-date-format="yyyy-MM-dd" required
                                                   data-date-type="number"
                                                   data-max-date="30.12.2099" data-autoclose="1"
                                                   daysOfWeekDisabled="false" bs-datepicker limit-date>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn blue" ng-click="ctrl.paySingUpdate()">提交</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="viewInvoiceImg" class="modal fade" tabindex="-1"
     data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"
                        aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <img height="100%" width="100%" ng-src="{{ctrl.imageSrc}}" >
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title">
                        	提示
                    </h4>
                </div>
                <div class="modal-body">
                    <p><span ng-bind="ctrl.message"></span></p>
                </div>
                <div class="modal-footer">
                    <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                </div>
            </div>
        </div>
    </div>
</div>
