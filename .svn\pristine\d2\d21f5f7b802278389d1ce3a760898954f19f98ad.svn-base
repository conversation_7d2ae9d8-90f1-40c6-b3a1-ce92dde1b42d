package com.kyb.pcberp.modules.stock.entity;

import java.math.BigDecimal;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;

public class RawStockAgeReportExcel extends DataEntity<RawStockAgeReportExcel>
{
    private static final long serialVersionUID = 1L;
    
    private String materialNo;
    
    private String materialName;
    
    private String specification;
    
    private BigDecimal quantity;
    
    private BigDecimal price;
    
    private BigDecimal money;
    
    private String storeName;
    
    @ExcelField(title = "物料编号", align = 2, sort = 10)
    public String getMaterialNo()
    {
        return materialNo;
    }
    
    @ExcelField(title = "物料名称", align = 2, sort = 20)
    public String getMaterialName()
    {
        return materialName;
    }
    
    @ExcelField(title = "规格型号", align = 2, sort = 30)
    public String getSpecification()
    {
        return specification;
    }
    
    @ExcelField(title = "结余", align = 2, sort = 40)
    public BigDecimal getQuantity()
    {
        return quantity;
    }
    
    @ExcelField(title = "移动加权平均单价", align = 2, sort = 50)
    public BigDecimal getPrice()
    {
        return price;
    }
    
    @ExcelField(title = "库存金额", align = 2, sort = 60)
    public BigDecimal getMoney()
    {
        return money;
    }
    
    @ExcelField(title = "仓库", align = 2, sort = 70)
    public String getStoreName()
    {
        return storeName;
    }
    
    public void setMaterialNo(String materialNo)
    {
        this.materialNo = materialNo;
    }
    
    public void setMaterialName(String materialName)
    {
        this.materialName = materialName;
    }
    
    public void setSpecification(String specification)
    {
        this.specification = specification;
    }
    
    public void setQuantity(BigDecimal quantity)
    {
        this.quantity = quantity;
    }
    
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }
    
    public void setMoney(BigDecimal money)
    {
        this.money = money;
    }
    
    public void setStoreName(String storeName)
    {
        this.storeName = storeName;
    }
    
    public void copyRawMater(RawmaterialStock rawmaterialStock)
    {
        this.materialNo = rawmaterialStock.getMaterialNo();
        this.materialName = rawmaterialStock.getMaterialName();
        this.specification = rawmaterialStock.getSpecification();
        this.storeName = rawmaterialStock.getStoreName();
        this.quantity = rawmaterialStock.getCurrStocks();
        this.price = rawmaterialStock.getPrice();
        this.money = rawmaterialStock.getMoneyRadio();
    }
}