# Bootstrap Directional Buttons
<a href="https://badge.fury.io/js/bootstrap-directional-buttons
" target="_blank"><img height="21" style='border:0px;height:21px;' border='0' src="https://badge.fury.io/js/bootstrap-directional-buttons.svg" alt="Gem Version"></a>
<a href='https://www.npmjs.org/package/bootstrap-directional-buttons
' target='_blank'><img height='21' style='border:0px;height:21px;' src='https://img.shields.io/npm/dt/bootstrap-directional-buttons.svg?label=NPM+Downloads' border='0' alt='NPM Downloads' /></a>
<a href='https://ko-fi.com/A5071NK' target='_blank'><img height='22' style='border:0px;height:22px;' src='https://az743702.vo.msecnd.net/cdn/kofi1.png?v=a' border='0' alt='Buy Me a Coffee' /></a> 

Directional / Arrow buttons for Bootstrap

## Usage:
Just add class .btn-arrow-left or .btn-arrow-right to bootstrap buttons. Use inside a .btn-group to remove space between button.

```HTML
<button type="button" class="btn btn-primary btn-arrow-left">Left Arrow Button</button>
<button type="button" class="btn btn-success btn-arrow-right">Right Arrow Button</button>
```

## Demo: 
<a href="https://jsbin.com/soweca/edit?html,css,output" target="_blank">https://jsbin.com/soweca/edit?html,css,output</a>

![Preview](https://raw.githubusercontent.com/westonganger/bootstrap-directional-buttons/master/preview.png)

## Install

#### Yarn, NPM, Bower
```
yarn add bootstrap-directional-buttons

npm install bootstrap-directional-buttons

bower install bootstrap-directional-buttons
```

#### Rails
```ruby
# Gemfile
source 'https://rails-assets.org' do
  gem 'rails-assets-bootstrap-directional-buttons'
end


# app/assets/stylesheets/application.scss
/*
 *= require bootstrap-directional-buttons
*/
```

# Credits

Enhanced and Updated by Weston Ganger - @westonganger

Created by Leonid Komarovsky - @shpoont
