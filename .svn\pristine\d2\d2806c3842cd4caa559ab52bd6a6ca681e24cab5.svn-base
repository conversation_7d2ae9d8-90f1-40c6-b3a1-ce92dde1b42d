/**
 * 
 */
package com.kyb.pcberp.modules.sys.dao;

import java.util.List;

import com.kyb.pcberp.modules.sys.entity.*;
import jdk.nashorn.internal.runtime.linker.LinkerCallSite;
import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;

/**
 * 部门DAO接口
 * 
 * <AUTHOR>
 * @version 2015-08-10
 */
@MyBatisDao
public interface DepartmentDao extends CrudDao<Department>
{

	/**
	 * 查找本公司部门
	 * @return
	 */
	List<Department> findParentDepts(Department department);
	
	/**
	 *查询用户部门表中 本部门是否被引用
	 * @param mdDepartment
	 * @return
	 */
	public Integer selectUserDepartment(Department mdDepartment);
	
	
	/**
	 * 根据部门编号 去查询用户
	 * @param department
	 * @return
	 */
	 public List<User> selectDep(Department department);

	/**
	 * 查询部门是否被引用
	 * @param mdDepartment
	 * @return  大于0，表示被引用，0表示没有引用
	 *
	 * <AUTHOR>
	 */
	public Integer findQuoteValue(Department mdDepartment);
	
    /**
     * ycy 查询部门编号是否存在
     * @param company
     * @return
     */
    public Integer getTestingNo(Department mdDepartment);

    /**
     * wc 2016-12-13 根据userId查询出部门名称
     */
    public String findDepartmentNameByUserId(@Param("userId")String userId, @Param("companyId")String companyId,@Param("activeFlag")String activeFlag);

	/**
	 * wc 2016-12-13 根据userId查询出部门Id
	 */
	public String findDepartmentIdByUserId(@Param("userId")String userId, @Param("companyId")String companyId,@Param("activeFlag")String activeFlag);
    
    /**
     * tj 2017-05-22
     * 查询公告的接收部门
     */
    public Department findDepartmentByNotice(Notice notice);

	/**
	 * tj 2017-05-22
	 * 查询通知单发送部门
	 */
	public Department findSendDepartmentByNotice(Notice notice);
    
    /**
     * zjn 2017-10-10  是否存在子集部门
     */
    public List<Department> findDepartment(Department department); 
    
    /**
     * zjn 2018-09-26 
     * 根据公司获取所有部门
     */
    public List<Department> getDeptListByCompany(Company company);

    /** tj 2018-11-26 获取部门Id */
    public Department finDeptIdByName(Employee emp);
    
    String getComDeptId(@Param("recordId")String recordId, @Param("companyId")String companyId);

    List<Department> getDeptListByPosition(Department department);

    List<Department> getDepartmentCapacity();

    String getGroupDeptId(@Param("erpDeptId") String erpDeptId);

    List<GroupOrgRelation> getRelationList();

	List<GroupOrgRelation> getGroupList(User user);

	List<GroupOrgRelation> getGroupListTwo(User user);

	String getErpUserId(@Param("groupOrgId") String groupOrgId,@Param("erpCompanyId") String erpCompanyId);

	List<GroupOrgRelation> getGroupOrgSaleAmount();

	List<GroupOrgRelation> getGroupSaleComData(@Param("saleComIds") String saleComIds);

	String getChildDeptIds(@Param("deptId") String deptId);

	List<Department> getDepartList(Department department);

	List<Department> getDefaultDepartList(Department department);

	List<GroupOrgRelation> getGroupListByPhone(@Param("phone") String phone);
}