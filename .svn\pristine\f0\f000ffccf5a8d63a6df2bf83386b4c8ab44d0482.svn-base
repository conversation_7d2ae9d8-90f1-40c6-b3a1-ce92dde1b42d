<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.stock.dao.MaterialInternetDao">
	
	<sql id="materialInternetColumns">
		a.recordId,
		a.companyId AS "company.recordId",
  		a.shareCompanyId AS "shareCompany.recordId",
  		a.storeHouseId AS "storeHouse.recordId",
 		a.materialId AS "material.recordId",
  		a.istallAmount,
  		a.percentageAmount,
  		a.activeFlag,
 		a.createdBy AS "createdBy.recordId",
 		a.createdDate,
  		a.lastUpdBy AS "lastUpdBy.recordId",
  		a.lastUpdDate
	</sql>
	
	<insert id="newInsert" parameterType="java.util.List">
		INSERT INTO md_material_share
		(
			companyId,
			shareCompanyId,
			storeHouseId,
			materialId,
			istallAmount,
			percentageAmount,
			activeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate
		)
		VALUES
		<foreach collection="miList" item="item" index= "index" separator =",">
    	(
        #{item.company.recordId},
        #{item.shareCompany.recordId},
        #{item.storeHouse.recordId},
        #{item.material.recordId},
        #{item.istallAmount},
        #{item.percentageAmount},
        1,
        #{item.createdBy.recordId},
        #{item.createdDate},
        #{item.lastUpdBy.recordId},
        #{item.lastUpdDate}
   		)
    	</foreach>
	</insert>
	
	<update id="update">
		UPDATE md_material_share SET
			companyId = #{company.recordId},
			shareCompanyId = #{shareCompany.recordId},
			storeHouseId = #{storeHouse.recordId},
			materialId = #{material.recordId},
			istallAmount = #{istallAmount},
			percentageAmount = #{percentageAmount},
			createdDate = #{createdDate},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		where recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE md_material_share SET
			activeFlag = #{activeFlag}
		where recordId = #{recordId}
	</update>
	
	<select id="get" resultType="MaterialInternet">
		select
		<include refid="materialInternetColumns"/>
		from md_material_share a
		where a.recordId = #{recordId}
	</select>
	
	<select id="findList" resultType="MaterialInternet">
		select
		<include refid="materialInternetColumns"/>,
		b.userName AS "createdBy.userName",
		c.no AS "material.no",
		c.materialKind AS "material.materialKind",
		c.salesStoreHouseId AS "material.salesStoreHouse.recordId",
		d.name AS "storeHouse.name",
		e.name AS "shareCompany.name"
		from md_material_share a
		LEFT JOIN sm_user b on b.recordId = a.createdBy
		LEFT JOIN md_material c on c.recordId = a.materialId
		LEFT JOIN md_store_house d on d.recordId = a.storeHouseId
		LEFT JOIN md_company e on e.recordId = a.shareCompanyId
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
			<!-- 物料编号 -->
			<if test="material != null and material.no != null and material.no != ''">
				AND REPLACE(c.no,' ','') like concat('%',#{material.no},'%')
			</if>
			<!-- 仓库名称 -->
			<if test="storeHouse != null and storeHouse.name != null and storeHouse.name != ''">
				AND REPLACE(d.name,' ','') like concat('%',#{storeHouse.name},'%')
			</if>
			<!-- 共享公司名称 -->
			<if test="shareCompany != null and shareCompany.name !=null and shareCompany.name != ''">
				AND REPLACE(e.name,' ','') like concat('%',#{shareCompany.name},'%')
			</if>
		</where>
		 <choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
		</choose>
	</select>
	
	<!-- zjn 2018-07-26 获取共享物料记录的数量 -->
	<select id="getMaterialInternetCount" resultType="Integer">
		select
		count(recordId)
		from md_material_share
		<where>
			companyId = #{company.recordId} AND activeFlag = #{DEL_FLAG_NORMAL}
			<if test="shareCompany != null and shareCompany.recordId !=null and shareCompany.recordId != ''">
				AND shareCompanyId = #{shareCompany.recordId}
			</if>
			<if test="storeHouse != null and storeHouse.recordId != null and storeHouse.recordId != ''">
				AND storeHouseId = #{storeHouse.recordId}
			</if>
			<if test="material != null and material.recordId != null and material.recordId != ''">
				AND materialId = #{material.recordId}
			</if>
			<if test="recordId != null and recordId != ''">
				AND recordId <![CDATA[ <> ]]> #{recordId}
			</if>
		</where>
	</select>
	
	<!-- zjn 2018-07-26 获取共享物料共享给所有公司的数量 -->
	<select id="getMaterialInternetCountAll" resultType="Integer">
		select
		count(recordId)
		from md_material_share
		<where>
			companyId = #{company.recordId} AND activeFlag = #{DEL_FLAG_NORMAL}
			<if test="null == shareCompany">
				AND ISNULL(shareCompanyId)
			</if>
			<if test="storeHouse != null and storeHouse.recordId != null and storeHouse.recordId != ''">
				AND storeHouseId = #{storeHouse.recordId}
			</if>
			<if test="material != null and material.recordId != null and material.recordId != ''">
				AND materialId = #{material.recordId}
			</if>
			<if test="recordId != null and recordId != ''">
				AND recordId <![CDATA[ <> ]]> #{recordId}
			</if>
		</where>
	</select>
	
	<!-- zjn 2018-08-15 根据公司和共享公司获取共享记录id -->
	<select id="getMaterialInternetId" resultType="String">
		select
		recordId
		from md_material_share
		where companyId = #{company.recordId} AND activeFlag = #{DEL_FLAG_NORMAL}
		AND shareCompanyId = #{shareCompany.recordId}
	</select>
	
	<!-- zjn 2018-08-15 删除共享组记录 -->
	<update id="deleteShareId">
		UPDATE md_material_share SET
			activeFlag = #{activeFlag}
		where recordId in (${recordId})
	</update>
	
	<!-- zjn 2018-09-07 获取物料是否在物联网设置金额 -->
	<select id="getMaterialInternetFlag" resultType="boolean">
		select
		count(1)
		from md_material_share 
		WHERE companyId = #{company.recordId} AND activeFlag = #{DEL_FLAG_NORMAL}
		<if test="material != null and material.recordId != null and material.recordId != ''">
			AND materialId = #{material.recordId}
		</if>
		<if test="storeHouse != null and storeHouse.recordId != null and storeHouse.recordId != ''">
			AND storeHouseId = #{storeHouse.recordId}
		</if>
	</select>
	
	<!-- zjn 2018-09-07 根据物料id或者仓库id和公司获取共享金额记录 -->
	<select id="getMiByMaterial" resultType="MaterialInternet">
		select
		<include refid="materialInternetColumns"/>
		from md_material_share a
		where a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
		<if test="material != null and material.recordId != null and material.recordId != ''">
			AND a.materialId = #{material.recordId}
		</if>
		<if test="storeHouse != null and storeHouse.recordId != null and storeHouse.recordId != ''">
			AND a.storeHouseId = #{storeHouse.recordId}
		</if>
	</select>
	
	<!-- zjn 2018-09-08 根据物料的仓库id获取所有仓库物料共享金额记录 -->
	<select id="getMIByMaterialSalesStoreHouseId" resultType="MaterialInternet">
		SELECT 
		a.recordId
		FROM md_material_share a
		LEFT JOIN md_material b ON b.recordId = a.materialId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
		AND b.salesStoreHouseId = #{recordId}
	</select>
</mapper>