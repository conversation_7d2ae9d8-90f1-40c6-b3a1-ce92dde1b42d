package com.kyb.pcberp.modules.report.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

/** zjn 2019-09-19 报表显示值实体*/
@SuppressWarnings("serial")
public class ReportValue extends DataEntity<ReportValue>
{
    private String name;// 属性名
    
    private String nameValue;// 属性名的值
    
    private String value1;// 当前的值
    
    private String value2;// 上一个的值
    
    private Integer status;// 报表模块
    
    private Integer typeValue;// 1:字符串型，2:整型或浮点型

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getNameValue()
    {
        return nameValue;
    }

    public void setNameValue(String nameValue)
    {
        this.nameValue = nameValue;
    }

    public String getValue1()
    {
        return value1;
    }

    public void setValue1(String value1)
    {
        this.value1 = value1;
    }

    public String getValue2()
    {
        return value2;
    }

    public void setValue2(String value2)
    {
        this.value2 = value2;
    }

    public Integer getStatus()
    {
        return status;
    }

    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getTypeValue()
    {
        return typeValue;
    }

    public void setTypeValue(Integer typeValue)
    {
        this.typeValue = typeValue;
    }
    
}
