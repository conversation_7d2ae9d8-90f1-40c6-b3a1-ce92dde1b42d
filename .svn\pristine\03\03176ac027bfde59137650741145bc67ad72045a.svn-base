package com.kyb.pcberp.modules.oa.controller;

import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_DepartMent;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_Position;
import com.kyb.pcberp.modules.hr.group_center.pojo.Hr_DepartRelation;
import com.kyb.pcberp.modules.oa.pojo.audit.Oa_audit;
import com.kyb.pcberp.modules.oa.pojo.common.Oa_expenseType;
import com.kyb.pcberp.modules.oa.pojo.common.Oa_update;
import com.kyb.pcberp.modules.oa.service.Oa_service;
import com.kyb.pcberp.modules.sys.entity.DictItem;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import com.kyb.pcberp.modules.sys.entity.Employee;
import com.kyb.pcberp.modules.sys.entity.Notice;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.KybWork;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "kybOa/")
public class Oa_controller
{
    @Autowired
    private Oa_service oa_service;

    // 获取系统更新
    @RequestMapping(value = "getUpdateList", method = RequestMethod.POST)
    @ResponseBody
    public List<Oa_update> getUpdateList(@RequestBody Oa_update update)
    {
        return oa_service.getUpdateList(update);
    }

    // 获取通知公告
    @RequestMapping(value = "getNoticeList", method = RequestMethod.POST)
    @ResponseBody
    public List<Notice> getNoticeList(@RequestBody Notice notice)
    {
        return oa_service.getNoticeList(notice);
    }

    //获取工作台数据
    @RequestMapping(value = "getWorkList", method = RequestMethod.POST)
    @ResponseBody
    public List<KybWork> getWorkList(@RequestBody KybWork work)
    {
        return oa_service.getWorkList(work);
    }

    @RequestMapping(value = "getCompanyList", method = RequestMethod.POST)
    @ResponseBody
    public List<Hr_DepartMent> getCompanyList(@RequestBody Hr_DepartMent depart)
    {
        return oa_service.getCompanyList(depart);
    }

    // 获取所有职务
    @RequestMapping(value = "getAllPositionList", method = RequestMethod.POST)
    @ResponseBody
    public List<Hr_Position> getAllPositionList(@RequestBody Hr_Employee emp)
    {
        return oa_service.getAllPositionList(emp);
    }

    // 获取当前员工的公司集合
    @RequestMapping(value = "getComList", method = RequestMethod.POST)
    @ResponseBody
    public List<Hr_DepartMent> getComList(@RequestBody Hr_Employee emp)
    {
        return oa_service.getComList(emp);
    }

    @RequestMapping(value = "getExpenseTypeList", method = RequestMethod.POST)
    @ResponseBody
    public List<Oa_expenseType> getExpenseTypeList()
    {
        return oa_service.getExpenseTypeList();
    }

    @RequestMapping(value = "getSupplychainApplyData", method = RequestMethod.POST)
    @ResponseBody
    public Map<String,List<?>> getSupplychainApplyData()
    {
        return oa_service.getSupplychainApplyData();
    }

    @RequestMapping(value = "getAuditWorkNum", method = RequestMethod.POST)
    @ResponseBody
    public Integer getAuditWorkNum(@RequestBody Oa_audit audit)
    {
        return oa_service.getAuditWorkNum(audit);
    }

    //获取审批单类型
    @RequestMapping(value = "getAuditTypeName", method = RequestMethod.POST)
    @ResponseBody
    List<Oa_audit> getAuditTypeName()
    {
        return oa_service.getAuditTypeName();
    }

    //获取销售公司列表
    @RequestMapping(value = "getSaleCompanyList", method = RequestMethod.POST)
    @ResponseBody
    public List<Hr_DepartRelation> getSaleCompanyList()
    {
        return oa_service.getSaleCompanyList();
    }

    //获取Oa组织架构
    @RequestMapping(value = {"getOrganizationAuditList"},method = RequestMethod.POST)
    @ResponseBody
    public List<Hr_DepartMent> getOrganizationAuditList()
    {
        return oa_service.getOrganizationAuditList();
    }

    //获取具体类目
    @RequestMapping(value = {"getCategoryAuditList"},method = RequestMethod.POST)
    @ResponseBody
    public List<Oa_audit> getCategoryAuditList()
    {
        return oa_service.getCategoryAuditList();
    }

    //加载销售公司客户列表
    @RequestMapping(value = "getSaleCustomerList", method = RequestMethod.POST)
    @ResponseBody
    public List<Customer> getSaleCustomerList(@RequestBody Customer customer)
    {
        return oa_service.getSaleCustomerList(customer);
    }

    @RequestMapping(value = "loadOaEmpList", method = RequestMethod.POST)
    @ResponseBody
    public List<Hr_Employee> loadOaEmpList(@RequestBody Hr_DepartMent depart)
    {
        return oa_service.loadOaEmpList(depart);
    }
    // 查询审批交接人列表
    @RequestMapping(value = "getAuditHandOver", method = RequestMethod.POST)
    @ResponseBody
    public List<Hr_Employee> getAuditHandOver(@RequestBody Hr_Employee emp)
    {
        return oa_service.getAuditHandOver(emp);
    }

    // 获取数据字典值
    @RequestMapping(value = "getAbstractList", method = RequestMethod.POST)
    @ResponseBody
    public List<DictValue> getAbstractList(@RequestBody DictValue dictValue)
    {
        return oa_service.getAbstractList(dictValue);
    }

    // 获取核算维度字典项
    @RequestMapping(value = "getAccountingDimensionList", method = RequestMethod.POST)
    @ResponseBody
    public List<DictItem> getAccountingDimensionList()
    {
        return oa_service.getAccountingDimensionList();
    }

    @RequestMapping(value = "loadAllOaEmpList", method = RequestMethod.POST)
    @ResponseBody
    public List<Hr_Employee> loadAllOaEmpList(@RequestBody Hr_DepartMent depart)
    {
        return oa_service.loadAllOaEmpList(depart);
    }
}
