<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.contract.dao.ContractAttachementsDao">
    
	<sql id="contractAttachementsColumns">
		a.recordid AS "recordId",
		a.contractid AS "contractId",
		a.contractdetailid AS "contractDetailId",
		a.orgfilename AS "orgFileName",
		a.realfilename AS "realFileName",
		a.fileurl AS "fileUrl",
		a.companyid AS "company.recordId"
	</sql>
	
	<sql id="contractAttachementsJoins">
	</sql>
    
	<select id="get" resultType="ContractAttachements">
		SELECT 
			<include refid="contractAttachementsColumns"/>
		FROM sl_contract_attachements a
		<include refid="contractAttachementsJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="findList" resultType="ContractAttachements">
		SELECT 
			<include refid="contractAttachementsColumns"/>
		FROM sl_contract_attachements a
		<include refid="contractAttachementsJoins"/>
		<where>
			
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="ContractAttachements">
		SELECT 
			<include refid="contractAttachementsColumns"/>
		FROM sl_contract_attachements a
		<include refid="contractAttachementsJoins"/>
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<insert id="insert">
		INSERT INTO sl_contract_attachements(
			contractid,
			contractdetailid,
			orgfilename,
			realfilename,
			fileurl,
			companyid
		) VALUES (
			#{contractId},
			#{contractDetailId},
			#{orgFileName},
			#{realFileName},
			#{fileUrl},
			#{company.recordId}
		)
	</insert>
	
	<update id="update">
		UPDATE sl_contract_attachements SET 	
			contractid = #{contractId},
			contractdetailid = #{contractDetailId},
			orgfilename = #{orgFileName},
			realfilename = #{realFileName},
			fileurl = #{fileUrl},
			companyid = #{company.recordId}
		WHERE a.recordId = #{recordId}
	</update>
	
	<delete id="delete">
		DELETE FROM sl_contract_attachements
		WHERE companyid = #{company.recordId}
		<if test="contractId != null and contractId != ''">
			AND contractId=#{contractId}
		</if>
		<if test="contractDetailId != null and contractDetailId != ''">
			AND contractdetailid=#{contractDetailId}
		</if>
	</delete>
	
	<delete id="deleteAttachements">
		DELETE FROM sl_contract_attachements
		WHERE recordId = #{recordId} and companyid = #{company.recordId}
	</delete>
	<!-- 通过合同和合同明细查询合同明细的附件 -->
	<select id="findContractAttachementsList" parameterType="ContractAttachements" resultType="ContractAttachements">
	    SELECT
	     <include refid="contractAttachementsColumns"/>
	    FROM sl_contract_attachements a
	    WHERE a.companyId = #{company.recordId}
	    <if test="contractId != null and contractId != ''">
			AND a.contractid = #{contractId}
		</if>
	    <if test="contractDetailId != null and contractDetailId != ''">
			AND a.contractdetailid = #{contractDetailId}
		</if>
	</select>
</mapper>