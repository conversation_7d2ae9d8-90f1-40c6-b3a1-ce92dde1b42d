<%@ page language="java" contentType="text/html; charset=UTF-8"
pageEncoding="UTF-8"%>
<div class="page-bar">
    <ul class="page-breadcrumb">
        <li><i class="fa fa-home"></i> <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i></li>
        <li><a href="javascript:;">系统设置</a><i class="fa fa-angle-right"></i></li>
        <li><a ui-sref="sys.userProfile">个人信息</a></li>
    </ul>
</div>
<div class="row">
    <div class="col-md-12" style="margin-top: -10px;">
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">
                    个人信息
                </div>
            </div>
            <div class="portlet-body" style="border: 0px solid red;">
                <form id="form-username" class="form-horizontal form-bordered"
                      role="form" name="editForm" ng-submit="mod(editForm)">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">姓名：</label>
                        <div class="col-sm-7">
                            <input class="form-control" id="userName"
                                   ng-model="smuser.userName"/>
                        </div>
                    </div>
                    <div class="form-group" style="border: 0px solid red;">
                        <label class="col-sm-3 control-label">登录账号：</label>
                        <div class="col-sm-7">
                            <input class="form-control" id="" ng-model="smuser.userCode"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">职位：</label>
                        <div class="col-sm-7">
                            <input class="form-control" type="text" id="position"
                                   ng-model="smuser.position"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">出生日期：</label>
                        <div class="col-sm-7">
                            <input type="text" class="form-control" id="birthdateTime"
                                   ng-model="smuser.birthday"
                                   data-date-format="yyyy年MM月dd日"
                                   data-date-type="number"
                                   data-min-date="02/10/1901"
                                   data-max-date="today"
                                   data-autoclose="1"
                                   daysOfWeekDisabled="false"
                                   name="order_date_from"
                                   bs-datepicker/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">固定电话：</label>

                        <div class="col-sm-7">
                            <input class="form-control" type="text" id="phone"
                                   ng-model="smuser.phone" ng-maxlength="20"/>
                        </div>
                    </div>
                    <!-- 公司邮编-->
                    <div class="form-group">
                        <label class="col-sm-3 control-label">手机号码：</label>

                        <div class="col-sm-7">
                            <input class="form-control" type="text" id="mobile"
                                   ng-model="smuser.mobile" ng-maxlength="20"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">邮箱：</label>
                        <div class="col-sm-7">
                            <input class="form-control" type="text" id="email"
                                   ng-model="smuser.email" ng-Cnemail/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">角色：</label>
                        <div class="col-sm-7">
                            <input class="form-control" type="text" disabled="disabled" ng-model="smuser.roleNames"/>
                        </div>
                    </div>
                    <div class="form-actions">
                        <div class="row">
                            <div class="col-md-offset-3 col-md-9">
								<button class="btn btn-success" type="submit">保存  </button>
                                <button class="btn btn-default" ng-click=""
                                        onclick="javascript:history.go(-1);" type="button">取消
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
</div>
<div class="row">
    <div class="col-md-12">
        <div id="static" class="modal fade" tabindex="-1"
             data-keyboard="false" data-backdrop="static">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"
                                aria-hidden="true"></button>
                        <h4 class="modal-title">
                       		    提示
                        </h4>
                    </div>
                    <div class="modal-body">
                        <p>
                            {{message}}
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-primary">ok</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>