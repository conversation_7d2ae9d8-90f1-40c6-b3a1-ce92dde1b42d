
const addOrderDeliveryTime = {
    template: '#addOrderDeliveryTime',
    computed: {
        comList: {
            get () {
                return this.$store.state.auditStore.comList
            }
        },
        emp: {
            get () {
                return this.$store.state.myStore.emp
            }
        }
    },
    created:function(){
        this.getWxMsg();
    },
    mounted:function(){
        this.loadData();
    },
    data() {
        return {
            audit: {},
            clickFlag:true,
            factCompanyList: [], // 工厂公司
            factCompanyId:"",
            groupId:"",
            groupList:[],
            orderDetailList:[], // 订单明细
            customerPoQuery:"",
            cntractNoQuery:"",
            craftNoQuery:"",
            customerModelQuery:"",
            shortNameQuery:"",
            statusQuery:"1",
            orderDeliveryTimeList:[],
            allChecked:false,
            temp:0,
            localIds: [],
            serverList: [],
            localIdsCopy: [],
            company:{},
            statusList: [{id:1,value:'预估延期'},{id:2,value:'已延期'},{id:3,value:'未延期'}],
            capacityDeailList:[],
            triggerFlag:false
        }
    },
    // 方法编写
    methods: {
        getWxMsg:function()
        {
            var config = {}
            config.url = location.href.split('#')[0]
            var _this = this
            $.ajax({
                type:"post",
                url:ctx + "/f/wechat/produce/getWxMsg",
                data:JSON.stringify(config),
                contentType:"application/json",
                success:function(data)
                {
                    _this.wxConfig(data)
                }
            })
        },
        wxConfig: function (data) {
            wx.config({
                debug: false, // 开启调试模式,调用的所有api的返回值会在客户端console.log出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                appId: data.appId, // 必填，公众号的唯一标识
                timestamp: data.timestamp, // 必填，生成签名的时间戳
                nonceStr: data.nonceStr, // 必填，生成签名的随机串
                signature: data.signature, // 必填，签名
                jsApiList: [
                    'checkJsApi', 'chooseImage', 'previewImage', 'uploadImage', 'downloadImage'
                ]
            })
        },
        loadData() {
            let query = {};
            query.factId = "1";
            query.phone = this.emp.phone;
            const _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/kybsoftOA/loadOrderDeliveryTimeData",
                contentType: "application/json",
                data: JSON.stringify(query),
                success: function (data) {
                    _this.factCompanyList = data.factCompanyList;
                    _this.groupId = data.groupId || null;
                    _this.groupList = data.groupList || [];
                    $('#loadingModal').modal('hide');
                }
            });
        },
        getOrderDetailList(num){
            if(this.triggerFlag)
            {
                return;
            }
            this.triggerFlag = true;
            let query = {};
            query.groupId = this.groupId;
            if(this.factCompanyList && this.factCompanyList.length > 0)
            {
                this.factCompanyId = this.factCompanyList[0].recordId;
            }
            query.companyId = this.factCompanyId;
            query.customerPo = this.customerPoQuery;
            query.cntractNo = this.cntractNoQuery;
            query.craftNo = this.craftNoQuery;
            query.customerModel = this.customerModelQuery;
            query.shortName = this.shortNameQuery;
            query.status = this.statusQuery;
            const list = [];
            for (let i=0;i<this.orderDetailList.length;i++){
                let orderDetail = this.orderDetailList[i];
                if (orderDetail.checked){
                    const obj = {};
                    obj.contractDetailId = orderDetail.recordId;
                    obj.oldDeliveryDate = orderDetail.deliveryDate;
                    obj.contractDetail = orderDetail;
                    list.push(obj);
                    let deliveryDateId = "#deliveryDate" + (list.length-1);
                    this.initDate(deliveryDateId);
                }
            }
            list.forEach(item => {
                if (!this.orderDeliveryTimeList.some(existingItem => existingItem.contractDetailId === item.contractDetailId)) {
                    this.orderDeliveryTimeList.push(item);
                }
            });
            var _this = this;
            $('#loadingModal').modal();
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/kybsoftOA/getOrderDetailList",
                contentType: "application/json",
                data: JSON.stringify(query),
                success: function (data) {
                    _this.triggerFlag = false;
                    _this.orderDetailList = data;
                    if(_this.orderDeliveryTimeList && _this.orderDeliveryTimeList.length > 0 && _this.orderDetailList && _this.orderDetailList.length > 0)
                    {
                        for(let orderDeliveryTime of _this.orderDeliveryTimeList)
                        {
                            for(let orderDetail of _this.orderDetailList)
                            {
                                if(orderDeliveryTime.contractDetailId == orderDetail.recordId)
                                {
                                    orderDetail.checked = true;
                                    break;
                                }
                            }
                        }
                    }
                    if(num)
                    {
                        if(!_this.factCompanyId)
                        {
                            _this.factCompanyId = _this.factCompanyList[0].recordId;
                        }
                        _this.loadSelect('factId',_this.factCompanyId,_this.factCompanyList,"recordId","name","请选择公司");
                        if(!_this.groupId && _this.groupList && _this.groupList.length > 0)
                        {
                            _this.loadSelect('groupId',null,_this.groupList,"groupOrgId","groupOrgName","请选择部门");
                        }
                        _this.loadSelect('status',_this.statusQuery,_this.statusList,"id","value","请选择状态");
                        $('#orderDeliveryTimeModal').modal();
                    }
                    $('#loadingModal').modal('hide');
                }
            });
        },
        loadSelect:function (id,value,list,showId,showValue,remark)
        {
            if ($('#'+id).is(':visible')) {
                this.initSelect(id,value,list,showId,showValue,remark);
            }else {
                if (this.temp > 50) {
                    this.temp = 0;
                }
                this.temp++;
                // 递归 等待dom渲染完毕
                var _this = this;
                setTimeout(function () { _this.initSelect(id,value,list,showId,showValue,remark); }, 500);
            }
        },
        initSelect:function(id,value,list,showId,showValue,remark){
            if(!id)
            {
                return;
            }
            $('#'+id).empty();
            let option = '';
            option += "<option value=''>" + remark + '</option>'
            list.forEach(el => {
                option += "<option value='" + eval("el."+showId) + "'>" + eval("el."+showValue) +'</option>'
            });
            $('#'+id).append(option);
            $('#'+id).selectpicker('val', value);
            $('#'+id).selectpicker('render');
            $('#'+id).selectpicker('refresh');
            $('#'+id).selectpicker();
        },
        setAllCheckedFun: function () {
            for (let i=0;i<this.orderDetailList.length;i++){
                this.$set(this.orderDetailList[i], 'checked', this.allChecked);
            }
        },
        augment:function(){
            const list = [];
            for (let i=0;i<this.orderDetailList.length;i++){
                let orderDetail = this.orderDetailList[i];
                if (orderDetail.checked){
                    const obj = {};
                    obj.contractDetailId = orderDetail.recordId;
                    obj.oldDeliveryDate = orderDetail.deliveryDate;
                    obj.contractDetail = orderDetail;
                    obj.sumDeailArea = orderDetail.sumDeailArea;
                    obj.capacityDeailList = orderDetail.capacityDeailList;
                    obj.deailArea = orderDetail.deailArea;
                    obj.companyId = orderDetail.companyId;
                    list.push(obj);
                    let deliveryDateId = "#deliveryDate" + (list.length-1);
                    obj.dateId = deliveryDateId;
                    this.initDate(deliveryDateId);
                }
            }
            if(!list || list.length == 0)
            {
                alert("请选择订单明细!");
            }
            this.orderDeliveryTimeList = list;
            for (let i = 0; i < this.orderDeliveryTimeList.length; i++)
            {
                let orderDeliveryTime = this.orderDeliveryTimeList[i];
                this.getCapacityDeailList(orderDeliveryTime.dateId, new Date());
            }
            $('#orderDeliveryTimeModal').modal('hide');
        },
        deleteOrderDelivery:function(index)
        {
            this.orderDeliveryTimeList.splice(index,1);
        },
        initDate: function (id) {
            var _this = this;
            if($(id).is(':visible')){
                $(id).daterangepicker({
                    "singleDatePicker": true,
                    "showDropdowns": true,
                    "timePicker": true,
                    "timePicker24Hour": false,
                    "startDate": moment().hours(0).minutes(0).seconds(0), //设置开始日期
                    "opens": "center",
                    "drops": "down",
                    "locale": {
                        "format": "YYYY-MM-DD",
                        "separator": " - ",
                        "applyLabel": "确定",
                        "cancelLabel": "取消",
                        "fromLabel": "From",
                        "toLabel": "到",
                        "customRangeLabel": "Custom",
                        "weekLabel": "W",
                        "daysOfWeek": [
                            "日",
                            "一",
                            "二",
                            "三",
                            "四",
                            "五",
                            "六"
                        ],
                        "monthNames": [
                            "一月",
                            "二月",
                            "三月",
                            "四月",
                            "五月",
                            "六月",
                            "七月",
                            "八月",
                            "九月",
                            "十月",
                            "十一月",
                            "十二月"
                        ],
                        "firstDay": 1
                    }
                }, function(start, end, label) {
                    _this.getCapacityDeailList(id,end);
                });
            }else{
                if(this.temp > 50){
                    alert("系统错误，请刷新重试");
                    this.temp = 0;
                }
                this.temp ++ ;
                //递归 等待dom渲染完毕
                setTimeout(function(){_this.initDate(id)},500);
            }
        },
        cancle: function () {
            this.$router.push('/submit');
        },
        submit: function () {
            if(!this.clickFlag)
            {
                alert("请勿多次点击！");
                return;
            }
            if(!this.orderDeliveryTimeList || this.orderDeliveryTimeList.length == 0)
            {
                alert("请添加订单交期！");
                return;
            }
            if(!this.company.oaDepartId)
            {
                alert("请选择来源！");
                return;
            }
            let flag = false;
            let massage = null;
            for (let i = 0; i < this.orderDeliveryTimeList.length; i++)
            {
                let orderDeliveryTime = this.orderDeliveryTimeList[i];
                var deliveryDateId = "#deliveryDate" + i;
                var deliveryDate =  $(deliveryDateId).val();
                if(!deliveryDate)
                {
                    flag = true;
                    massage = "修改交期不能为空!";
                    break;
                }
                if(!orderDeliveryTime.remark)
                {
                    flag = true;
                    massage = "原因不能为空!";
                    break;
                }
                orderDeliveryTime.deliveryDate = deliveryDate;
            }
            if(flag)
            {
                alert(massage);
                return;
            }
            this.audit.factCompanyId = this.factCompanyId;
            this.audit.orderDeliveryTimeList = this.orderDeliveryTimeList;
            this.audit.oaDepartId = this.company.oaDepartId;
            this.audit.empId = this.emp.recordId;
            this.audit.groupId = this.emp.company.recordId;
            this.audit.auditType = "22025";
            this.audit.serverList = this.serverList
            this.clickFlag = false;
            var router = this.$router;
            $('#loadingModal').modal();
            var _this = this;
            $.ajax({
                type: "post",
                url: ctx + "/f/wechat/kybsoftOA/commitAudit",
                data: JSON.stringify(this.audit),
                contentType: "application/json",
                success: function (data) {
                    if (data == "success") {
                        alert("提交成功");
                        router.push('/submit');
                    } else {
                        alert(data);
                    }
                    $('#loadingModal').modal('hide');
                    _this.clickFlag = true;
                }
            });
        },
        chooseImg:function(){
            var _this = this
            wx.chooseImage({
                count: 9, // 默认9
                sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
                success: function (res) {
                    if(_this.localIds && _this.localIds.length > 0){
                        for(var i=0;i< res.localIds.length;i++){
                            _this.localIds.push(res.localIds[i])
                        }
                    }else{
                        _this.localIds = res.localIds // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
                    }
                    // 上传图片
                    _this.serverList = []
                    _this.localIdsCopy = []
                    for (let i = 0; i < _this.localIds.length; i++) {
                        _this.localIdsCopy.push(_this.localIds[i])
                    }
                    _this.uploadImage()
                }
            });
        },
        preview:function(item){
            var _this = this
            wx.previewImage({
                current: item, // 当前显示图片的http链接
                urls: _this.localIds // 需要预览的图片http链接列表
            });
        },
        uploadImage:function(){
            var vm = this
            if(vm.localIdsCopy && vm.localIdsCopy.length > 0){
                wx.uploadImage({
                    localId: vm.localIdsCopy.pop(), // 需要上传的图片的本地ID，由chooseImage接口获得
                    isShowProgressTips: 1, // 默认为1，显示进度提示
                    success: function (res) {
                        vm.serverList.push(res.serverId)
                        vm.uploadImage()
                    }
                });
            }
        },
        deleteImg:function(index){
            this.localIds.splice(index,1)
        },
        // 获取产能占用
        getCapacityDeailList: function (dateId,deliveryDate){
            if (dateId)
            {
                if (deliveryDate)
                {
                    var _this = this
                    $.ajax({
                        type: "post",
                        url: ctx + "/f/wechat/kybsoftOA/getCapacityDeailList",
                        data: JSON.stringify(deliveryDate),
                        contentType: "application/json",
                        success: function (data) {
                            if (data)
                            {
                                for (let i = 0; i < _this.orderDeliveryTimeList.length; i++)
                                {
                                    let orderDeliveryTime = _this.orderDeliveryTimeList[i];
                                    if (dateId == orderDeliveryTime.dateId)
                                    {
                                        let sumDeailArea = 0;
                                        for (let i = 0; i < data.length; i++)
                                        {
                                            if (data[i].deailArea && null != data[i].deailArea)
                                            {
                                                sumDeailArea += data[i].deailArea;
                                            }
                                        }
                                        orderDeliveryTime.sumDeailArea = sumDeailArea;
                                        orderDeliveryTime.capacityDeailList = data;
                                        break;
                                    }
                                }
                            }
                        }
                    });
                }
            }
        },
        openCapacityDeailList: function(row)
        {
            this.capacityDeailList = row.capacityDeailList;
            $('#occupation').modal('show');
        }
    }
};