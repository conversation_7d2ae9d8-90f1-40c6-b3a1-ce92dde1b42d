/**
 * 
 */
package com.kyb.pcberp.modules.contract.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.crm.entity.CustomodelPrice;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import com.kyb.pcberp.modules.sys.entity.Company;

/**
 * contractEntity 生产工艺明细表（合同明细的明细表）
 * 
 * <AUTHOR>
 * @version 2015-09-06
 */
public class ContractCraft extends DataEntity<ContractCraft>
{
    
    private static final long serialVersionUID = 1L;
    
    private Customer customer; // 客户ID
    
    private String no; // 工艺要求编号
    
    private String customerModel; // 客户命名的产品类型
    
    private BigDecimal unitWidth; // PCS宽度，单位mm
    
    private BigDecimal unitLength; // PCS长度，单位mm
    
    private BigDecimal pnlWidth; // PNL宽度，单位mm
    
    private BigDecimal pnlLength; // PNL长度，单位mm
    
    private BigDecimal pnlDivisor; // PNL拼接数,指一个PNL中含有多少个PCS
    
    private String deliverySize; // 交货尺寸，存储格式为：11,11 表示长11mm,宽11mm
    
    private Long filmDescript; // 菲林(胶片)描述
    
    private Long referenceType; // 工艺资料类别
    
    private String referenceDesc; // 工艺资料描述
    
    private Long materialType; // 材料类别
    
    private Long boardLevel; // 板层数
    
    private Long boardThickness; // 板厚
    
    private Long surfaceProcess; // 表面加工工艺
    
    private Long copperCladThickness; // 覆铜厚度
    
    private Long solderMaskType; // 阻焊类型
    
    private Long characterType; // 字符印刷类型
    
    private Long shapingWay; // 成形方式
    
    private Long drillingWay; // 钻孔方式
    
    private Long holeSolderMask; // 过孔阻焊类型
    
    private Long packingType; // 包装方法类
    
    private Long testMethod; // 产品出厂检测方法
    
    private Integer blueFilmFlag; // 是否要盖蓝胶
    
    private Integer newMouldFlag; // 是否要开新模
    
    private Integer newShelfFlag; // 是否要开新测试架
    
    private Integer holeThiaOilFlag; // 是否要过孔噻油
    
    private String status; // status
    
    private String contractDetailId; // status
    
    private Integer totalSets; // 整个大板SET数量 为了 wip 查询数据方便 加的
    
    private String pnlsize;// 工艺PCS尺寸：100*100格式
    
    private Long lingeSpacing;// 最小线宽/线距
    
    private Long smallAperture;// 最小孔径
    
    private Long halAhole;// 半孔
    
    private Long buryBlindHole;// 埋盲孔
    
    private Long resistance;// 抗阻
    
    private Long deliveryUrgent;// 发货加急
    
    private Supplier banLiaoSupplier; // WC 2017-05-03 指定板料供应商
    
    private Supplier youMoSupplier; // WC 2017-05-03 指定油墨供应商
    
    private Long daore; // 导热
    
    private Long naiya; // 耐压
    
    private Long pliesnumber; // 层数
    
    private String customerId;
    
    private CustomodelPrice customodelPrice; // 客户型号报价
    
    private String quoteFlag; // 其它：自动提示是否参与报价，1:不参与报价，2：参与报价
    
    private Company quiltCombinationCompany;
    
    private String combinationFlag;// 公司合并标识
    
    private boolean RepeatFlag = false;// zjn 2018-03-17 判断被合并公司工艺编号是否重复与合并公司工艺编号
    
    private String addCraftFlag; // fzd 2018-07-12 新加工艺，还未正式化为1，其它状态不限
    
    private String price; // 最近的接单价格
    
    private String throughHole; // 通口个数
    
    private String countersinkHole; // 沉头孔个数
    
    private String shippingAddress; // 出货地点
    
    private String board; // 板材
    
    private Long inkType; // 油墨型号
    
    private Date sentTimeStartQr; // 时间段查询条件 载体
    
    private Date sentTimeEndQr;
    
    private String lowPrice;
    
    private String specialCraft; // 特殊工艺，可多选
    
    private BigDecimal cardUnitWidth; // 工程卡PCS宽度
    
    private BigDecimal cardUnitLength; // 工程卡PCS长度
    
    private BigDecimal cardPnlWidth; // 工程卡PNL宽度
    
    private BigDecimal cardPnlLength; // 工程卡PNL长度
    
    private BigDecimal cardPnlDivisor; // 工程卡PNL拼接数
    
    private String cardSet; // 工程卡尺寸
    
    private String progressDesc; // 周期
    
    private String setArea; // 单只工程set尺寸
    
    private String boardLevelValue; // PCB类型
    
    private String materialTypeValue; // 覆铜板材
    
    private String boardThicknessValue; // 板材厚度
    
    private String copperCladThicknessValue; // 覆铜要求
    
    private String surfaceProcessValue; // 镀层处理
    
    private String solderMaskTypeValue; // 阻焊类型
    
    private String characterTypeValue; // 板面字符
    
    private String shapingWayValue; // 成型方式
    
    private String testMethodValue; // 测试要求
    
    private String lingeSpacingValue; // 最小线宽/线距
    
    private String smallApertureValue; // 最小孔径
    
    private String halAholeValue; // 半孔
    
    private String buryBlindHoleValue; // 埋盲孔
    
    private String resistanceValue; // 抗阻
    
    private String deliveryUrgentValue; // 加急费
    
    private String daoreValue; // 导热
    
    private String naiyaValue; // 耐压
    
    private String specialCraftValue; // 特殊工艺
    
    private String size;
    
    private String finalId;
    
    private String createType;
    
    private String newCusMatNo; // 上次最新客户物料号
    
    private Company disCompany; // 分配公司
    
    private Customer disCustomer;//分配客户

    private String noOne;

    private String noTwo;

    private String productTypeId;

    private String productType; //产品类型

    private String insulationThickness; // 绝缘层厚度

    private Integer backColor; // 板材背面颜色

    private Integer plateType; // 板材类型

    private String packingRequirement; // 包装要求

    private String packingRequirementValue;

    private String drillinHole; // 钻孔个数
    
    public Customer getDisCustomer()
    {
        return disCustomer;
    }

    public void setDisCustomer(Customer disCustomer)
    {
        this.disCustomer = disCustomer;
    }

    public Company getDisCompany()
    {
        return disCompany;
    }

    public void setDisCompany(Company disCompany)
    {
        this.disCompany = disCompany;
    }

    public String getCreateType()
    {
        return createType;
    }

    public void setCreateType(String createType)
    {
        this.createType = createType;
    }

    public String getPnlsize()
    {
        return pnlsize;
    }
    
    public void setPnlsize(String pnlsize)
    {
        this.pnlsize = pnlsize;
    }
    
    public Integer getTotalSets()
    {
        return totalSets;
    }
    
    public void setTotalSets(Integer totalSets)
    {
        this.totalSets = totalSets;
    }
    
    public ContractCraft()
    {
        super();
    }
    
    public ContractCraft(String id)
    {
        super(id);
    }
    
    public Customer getCustomer()
    {
        return customer;
    }
    
    public void setCustomer(Customer customer)
    {
        this.customer = customer;
    }
    
    public String getNo()
    {
        return no;
    }
    
    public void setNo(String no)
    {
        this.no = no;
    }
    
    public String getContractDetailId()
    {
        return contractDetailId;
    }
    
    public void setContractDetailId(String contractDetailId)
    {
        this.contractDetailId = contractDetailId;
    }
    
    public String getCustomerModel()
    {
        return customerModel;
    }
    
    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }
    
    public BigDecimal getUnitWidth()
    {
        return unitWidth;
    }
    
    public void setUnitWidth(BigDecimal unitWidth)
    {
        this.unitWidth = unitWidth;
    }
    
    public BigDecimal getUnitLength()
    {
        return unitLength;
    }
    
    public void setUnitLength(BigDecimal unitLength)
    {
        this.unitLength = unitLength;
    }
    
    public BigDecimal getPnlWidth()
    {
        return pnlWidth;
    }
    
    public void setPnlWidth(BigDecimal pnlWidth)
    {
        this.pnlWidth = pnlWidth;
    }
    
    public BigDecimal getPnlLength()
    {
        return pnlLength;
    }
    
    public void setPnlLength(BigDecimal pnlLength)
    {
        this.pnlLength = pnlLength;
    }
    
    public BigDecimal getPnlDivisor()
    {
        return pnlDivisor;
    }
    
    public void setPnlDivisor(BigDecimal pnlDivisor)
    {
        this.pnlDivisor = pnlDivisor;
    }
    
    public String getDeliverySize()
    {
        return deliverySize;
    }
    
    public void setDeliverySize(String deliverySize)
    {
        this.deliverySize = deliverySize;
    }
    
    public Long getFilmDescript()
    {
        return filmDescript;
    }
    
    public void setFilmDescript(Long filmDescript)
    {
        this.filmDescript = filmDescript;
    }
    
    public Long getReferenceType()
    {
        return referenceType;
    }
    
    public void setReferenceType(Long referenceType)
    {
        this.referenceType = referenceType;
    }
    
    public String getReferenceDesc()
    {
        return referenceDesc;
    }
    
    public void setReferenceDesc(String referenceDesc)
    {
        this.referenceDesc = referenceDesc;
    }
    
    public Long getMaterialType()
    {
        return materialType;
    }
    
    public void setMaterialType(Long materialType)
    {
        this.materialType = materialType;
    }
    
    public Long getBoardLevel()
    {
        return boardLevel;
    }
    
    public void setBoardLevel(Long boardLevel)
    {
        this.boardLevel = boardLevel;
    }
    
    public Long getBoardThickness()
    {
        return boardThickness;
    }
    
    public void setBoardThickness(Long boardThickness)
    {
        this.boardThickness = boardThickness;
    }
    
    public Long getSurfaceProcess()
    {
        return surfaceProcess;
    }
    
    public void setSurfaceProcess(Long surfaceProcess)
    {
        this.surfaceProcess = surfaceProcess;
    }
    
    public Long getCopperCladThickness()
    {
        return copperCladThickness;
    }
    
    public void setCopperCladThickness(Long copperCladThickness)
    {
        this.copperCladThickness = copperCladThickness;
    }
    
    public Long getSolderMaskType()
    {
        return solderMaskType;
    }
    
    public void setSolderMaskType(Long solderMaskType)
    {
        this.solderMaskType = solderMaskType;
    }
    
    public Long getCharacterType()
    {
        return characterType;
    }
    
    public void setCharacterType(Long characterType)
    {
        this.characterType = characterType;
    }
    
    public Long getShapingWay()
    {
        return shapingWay;
    }
    
    public void setShapingWay(Long shapingWay)
    {
        this.shapingWay = shapingWay;
    }
    
    public Long getDrillingWay()
    {
        return drillingWay;
    }
    
    public void setDrillingWay(Long drillingWay)
    {
        this.drillingWay = drillingWay;
    }
    
    public Long getHoleSolderMask()
    {
        return holeSolderMask;
    }
    
    public void setHoleSolderMask(Long holeSolderMask)
    {
        this.holeSolderMask = holeSolderMask;
    }
    
    public Long getPackingType()
    {
        return packingType;
    }
    
    public void setPackingType(Long packingType)
    {
        this.packingType = packingType;
    }
    
    public Long getTestMethod()
    {
        return testMethod;
    }
    
    public void setTestMethod(Long testMethod)
    {
        this.testMethod = testMethod;
    }
    
    public Integer getBlueFilmFlag()
    {
        return blueFilmFlag;
    }
    
    public void setBlueFilmFlag(Integer blueFilmFlag)
    {
        this.blueFilmFlag = blueFilmFlag;
    }
    
    public Integer getNewMouldFlag()
    {
        return newMouldFlag;
    }
    
    public void setNewMouldFlag(Integer newMouldFlag)
    {
        this.newMouldFlag = newMouldFlag;
    }
    
    public Integer getNewShelfFlag()
    {
        return newShelfFlag;
    }
    
    public void setNewShelfFlag(Integer newShelfFlag)
    {
        this.newShelfFlag = newShelfFlag;
    }
    
    public Integer getHoleThiaOilFlag()
    {
        return holeThiaOilFlag;
    }
    
    public void setHoleThiaOilFlag(Integer holeThiaOilFlag)
    {
        this.holeThiaOilFlag = holeThiaOilFlag;
    }
    
    public String getRemark()
    {
        return remark;
    }
    
    public void setRemark(String remark)
    {
        this.remark = remark;
    }
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public Long getLingeSpacing()
    {
        return lingeSpacing;
    }
    
    public Long getSmallAperture()
    {
        return smallAperture;
    }
    
    public Long getHalAhole()
    {
        return halAhole;
    }
    
    public Long getBuryBlindHole()
    {
        return buryBlindHole;
    }
    
    public Long getResistance()
    {
        return resistance;
    }
    
    public Long getDeliveryUrgent()
    {
        return deliveryUrgent;
    }
    
    public void setLingeSpacing(Long lingeSpacing)
    {
        this.lingeSpacing = lingeSpacing;
    }
    
    public void setSmallAperture(Long smallAperture)
    {
        this.smallAperture = smallAperture;
    }
    
    public void setHalAhole(Long halAhole)
    {
        this.halAhole = halAhole;
    }
    
    public void setBuryBlindHole(Long buryBlindHole)
    {
        this.buryBlindHole = buryBlindHole;
    }
    
    public void setResistance(Long resistance)
    {
        this.resistance = resistance;
    }
    
    public void setDeliveryUrgent(Long deliveryUrgent)
    {
        this.deliveryUrgent = deliveryUrgent;
    }
    
    public Supplier getBanLiaoSupplier()
    {
        return banLiaoSupplier;
    }
    
    public void setBanLiaoSupplier(Supplier banLiaoSupplier)
    {
        this.banLiaoSupplier = banLiaoSupplier;
    }
    
    public Supplier getYouMoSupplier()
    {
        return youMoSupplier;
    }
    
    public void setYouMoSupplier(Supplier youMoSupplier)
    {
        this.youMoSupplier = youMoSupplier;
    }
    
    public Long getDaore()
    {
        return daore;
    }
    
    public void setDaore(Long daore)
    {
        this.daore = daore;
    }
    
    public Long getNaiya()
    {
        return naiya;
    }
    
    public void setNaiya(Long naiya)
    {
        this.naiya = naiya;
    }
    
    public Long getPliesnumber()
    {
        return pliesnumber;
    }
    
    public void setPliesnumber(Long pliesnumber)
    {
        this.pliesnumber = pliesnumber;
    }
    
    public String getCustomerId()
    {
        return customerId;
    }
    
    public void setCustomerId(String customerId)
    {
        this.customerId = customerId;
    }
    
    public CustomodelPrice getCustomodelPrice()
    {
        return customodelPrice;
    }
    
    public void setCustomodelPrice(CustomodelPrice customodelPrice)
    {
        this.customodelPrice = customodelPrice;
    }
    
    public String getQuoteFlag()
    {
        return quoteFlag;
    }
    
    public void setQuoteFlag(String quoteFlag)
    {
        this.quoteFlag = quoteFlag;
    }
    
    public Company getQuiltCombinationCompany()
    {
        return quiltCombinationCompany;
    }
    
    public void setQuiltCombinationCompany(Company quiltCombinationCompany)
    {
        this.quiltCombinationCompany = quiltCombinationCompany;
    }
    
    public String getCombinationFlag()
    {
        return combinationFlag;
    }
    
    public void setCombinationFlag(String combinationFlag)
    {
        this.combinationFlag = combinationFlag;
    }
    
    public boolean getRepeatFlag()
    {
        return RepeatFlag;
    }
    
    public void setRepeatFlag(boolean repeatFlag)
    {
        RepeatFlag = repeatFlag;
    }
    
    public String getAddCraftFlag()
    {
        return addCraftFlag;
    }
    
    public void setAddCraftFlag(String addCraftFlag)
    {
        this.addCraftFlag = addCraftFlag;
    }
    
    public String getPrice()
    {
        return price;
    }
    
    public void setPrice(String price)
    {
        this.price = price;
    }
    
    public String getThroughHole()
    {
        return throughHole;
    }
    
    public void setThroughHole(String throughHole)
    {
        this.throughHole = throughHole;
    }
    
    public String getCountersinkHole()
    {
        return countersinkHole;
    }
    
    public void setCountersinkHole(String countersinkHole)
    {
        this.countersinkHole = countersinkHole;
    }
    
    public String getShippingAddress()
    {
        return shippingAddress;
    }
    
    public void setShippingAddress(String shippingAddress)
    {
        this.shippingAddress = shippingAddress;
    }
    
    public String getBoard()
    {
        return board;
    }
    
    public void setBoard(String board)
    {
        this.board = board;
    }
    
    public Long getInkType()
    {
        return inkType;
    }
    
    public void setInkType(Long inkType)
    {
        this.inkType = inkType;
    }
    
    public Date getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }
    
    public void setSentTimeStartQr(Date sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }
    
    public Date getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }
    
    public void setSentTimeEndQr(Date sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }
    
    public String getLowPrice()
    {
        return lowPrice;
    }
    
    public void setLowPrice(String lowPrice)
    {
        this.lowPrice = lowPrice;
    }
    
    public String getSpecialCraft()
    {
        return specialCraft;
    }
    
    public void setSpecialCraft(String specialCraft)
    {
        this.specialCraft = specialCraft;
    }
    
    public BigDecimal getCardUnitWidth()
    {
        return cardUnitWidth;
    }
    
    public void setCardUnitWidth(BigDecimal cardUnitWidth)
    {
        this.cardUnitWidth = cardUnitWidth;
    }
    
    public BigDecimal getCardUnitLength()
    {
        return cardUnitLength;
    }
    
    public void setCardUnitLength(BigDecimal cardUnitLength)
    {
        this.cardUnitLength = cardUnitLength;
    }
    
    public BigDecimal getCardPnlWidth()
    {
        return cardPnlWidth;
    }
    
    public void setCardPnlWidth(BigDecimal cardPnlWidth)
    {
        this.cardPnlWidth = cardPnlWidth;
    }
    
    public BigDecimal getCardPnlLength()
    {
        return cardPnlLength;
    }
    
    public void setCardPnlLength(BigDecimal cardPnlLength)
    {
        this.cardPnlLength = cardPnlLength;
    }
    
    public BigDecimal getCardPnlDivisor()
    {
        return cardPnlDivisor;
    }
    
    public void setCardPnlDivisor(BigDecimal cardPnlDivisor)
    {
        this.cardPnlDivisor = cardPnlDivisor;
    }
    
    public String getCardSet()
    {
        return cardSet;
    }
    
    public void setCardSet(String cardSet)
    {
        this.cardSet = cardSet;
    }
    
    public String getProgressDesc()
    {
        return progressDesc;
    }
    
    public void setProgressDesc(String progressDesc)
    {
        this.progressDesc = progressDesc;
    }
    
    public String getSetArea()
    {
        return setArea;
    }
    
    public void setSetArea(String setArea)
    {
        this.setArea = setArea;
    }
    
    // 实体深拷贝
    public ContractCraft clone()
    {
        ContractCraft o = null;
        try
        {
            o = (ContractCraft)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } // Object 中的clone()识别出你要复制的是哪一个对象。
        return o;
    }
    
    public String getBoardLevelValue()
    {
        return boardLevelValue;
    }
    
    public String getMaterialTypeValue()
    {
        return materialTypeValue;
    }
    
    public String getBoardThicknessValue()
    {
        return boardThicknessValue;
    }
    
    public String getCopperCladThicknessValue()
    {
        return copperCladThicknessValue;
    }
    
    public String getSurfaceProcessValue()
    {
        return surfaceProcessValue;
    }
    
    public String getSolderMaskTypeValue()
    {
        return solderMaskTypeValue;
    }
    
    public String getCharacterTypeValue()
    {
        return characterTypeValue;
    }
    
    public String getShapingWayValue()
    {
        return shapingWayValue;
    }
    
    public String getTestMethodValue()
    {
        return testMethodValue;
    }
    
    public String getLingeSpacingValue()
    {
        return lingeSpacingValue;
    }
    
    public String getSmallApertureValue()
    {
        return smallApertureValue;
    }
    
    public String getHalAholeValue()
    {
        return halAholeValue;
    }
    
    public String getBuryBlindHoleValue()
    {
        return buryBlindHoleValue;
    }
    
    public String getResistanceValue()
    {
        return resistanceValue;
    }
    
    public String getDeliveryUrgentValue()
    {
        return deliveryUrgentValue;
    }
    
    public String getDaoreValue()
    {
        return daoreValue;
    }
    
    public String getNaiyaValue()
    {
        return naiyaValue;
    }
    
    public String getSpecialCraftValue()
    {
        return specialCraftValue;
    }
    
    public void setBoardLevelValue(String boardLevelValue)
    {
        this.boardLevelValue = boardLevelValue;
    }
    
    public void setMaterialTypeValue(String materialTypeValue)
    {
        this.materialTypeValue = materialTypeValue;
    }
    
    public void setBoardThicknessValue(String boardThicknessValue)
    {
        this.boardThicknessValue = boardThicknessValue;
    }
    
    public void setCopperCladThicknessValue(String copperCladThicknessValue)
    {
        this.copperCladThicknessValue = copperCladThicknessValue;
    }
    
    public void setSurfaceProcessValue(String surfaceProcessValue)
    {
        this.surfaceProcessValue = surfaceProcessValue;
    }
    
    public void setSolderMaskTypeValue(String solderMaskTypeValue)
    {
        this.solderMaskTypeValue = solderMaskTypeValue;
    }
    
    public void setCharacterTypeValue(String characterTypeValue)
    {
        this.characterTypeValue = characterTypeValue;
    }
    
    public void setShapingWayValue(String shapingWayValue)
    {
        this.shapingWayValue = shapingWayValue;
    }
    
    public void setTestMethodValue(String testMethodValue)
    {
        this.testMethodValue = testMethodValue;
    }
    
    public void setLingeSpacingValue(String lingeSpacingValue)
    {
        this.lingeSpacingValue = lingeSpacingValue;
    }
    
    public void setSmallApertureValue(String smallApertureValue)
    {
        this.smallApertureValue = smallApertureValue;
    }
    
    public void setHalAholeValue(String halAholeValue)
    {
        this.halAholeValue = halAholeValue;
    }
    
    public void setBuryBlindHoleValue(String buryBlindHoleValue)
    {
        this.buryBlindHoleValue = buryBlindHoleValue;
    }
    
    public void setResistanceValue(String resistanceValue)
    {
        this.resistanceValue = resistanceValue;
    }
    
    public void setDeliveryUrgentValue(String deliveryUrgentValue)
    {
        this.deliveryUrgentValue = deliveryUrgentValue;
    }
    
    public void setDaoreValue(String daoreValue)
    {
        this.daoreValue = daoreValue;
    }
    
    public void setNaiyaValue(String naiyaValue)
    {
        this.naiyaValue = naiyaValue;
    }
    
    public void setSpecialCraftValue(String specialCraftValue)
    {
        this.specialCraftValue = specialCraftValue;
    }
    
    public String getSize()
    {
        return size;
    }
    
    public void setSize(String size)
    {
        this.size = size;
    }

    public String getFinalId()
    {
        return finalId;
    }

    public void setFinalId(String finalId)
    {
        this.finalId = finalId;
    }

    public String getNewCusMatNo()
    {
        return newCusMatNo;
    }

    public void setNewCusMatNo(String newCusMatNo)
    {
        this.newCusMatNo = newCusMatNo;
    }

    public String getNoOne() {
        return noOne;
    }

    public void setNoOne(String noOne) {
        this.noOne = noOne;
    }

    public String getNoTwo() {
        return noTwo;
    }

    public void setNoTwo(String noTwo) {
        this.noTwo = noTwo;
    }

    public String getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(String productTypeId) {
        this.productTypeId = productTypeId;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getInsulationThickness()
    {
        return insulationThickness;
    }

    public void setInsulationThickness(String insulationThickness)
    {
        this.insulationThickness = insulationThickness;
    }

    public Integer getBackColor()
    {
        return backColor;
    }

    public void setBackColor(Integer backColor)
    {
        this.backColor = backColor;
    }

    public Integer getPlateType()
    {
        return plateType;
    }

    public void setPlateType(Integer plateType)
    {
        this.plateType = plateType;
    }

    public String getPackingRequirement() {
        return packingRequirement;
    }

    public void setPackingRequirement(String packingRequirement) {
        this.packingRequirement = packingRequirement;
    }

    public String getPackingRequirementValue() {
        return packingRequirementValue;
    }

    public void setPackingRequirementValue(String packingRequirementValue) {
        this.packingRequirementValue = packingRequirementValue;
    }

    public String getDrillinHole() {
        return drillinHole;
    }

    public void setDrillinHole(String drillinHole) {
        this.drillinHole = drillinHole;
    }
}