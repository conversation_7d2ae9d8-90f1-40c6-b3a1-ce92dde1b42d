package com.kyb.pcberp.modules.production.web;

import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.modules.contract.entity.NotificationScheduling;
import com.kyb.pcberp.modules.eg.service.CardService;
import com.kyb.pcberp.modules.production.entity.Ask;
import com.kyb.pcberp.modules.production.service.AskService;
import com.kyb.pcberp.modules.production.vo.AskVo;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ClassName AskController
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/11/29 19:01
 * @Version 1.0
 **/

@Controller
@RequestMapping(value = "${adminPath}/production/ask")
public class AskController {

    @Autowired
    private AskService askService;

    @Autowired
    private CardService cardService;

    @RequestMapping(value = "view")
    public String view()
    {
        return "modules/production/ask";
    }


    @RequestMapping(value = "load/data")
    @ResponseBody
    public Page<Ask> loadData(@RequestBody Ask ask,HttpServletRequest request, HttpServletResponse response) {
        User user = UserUtils.getUser();
        ask.setCompany(user.getCompany());
        ask.setCompanyId(user.getCompany().getRecordId());
        Page<Ask> qpage = new Page<>(request, response);
        qpage.setPageNo(Integer.parseInt(ask.getPageNo()));
        qpage.setPageSize(Integer.parseInt(ask.getPageSize()));
        qpage.setOrderBy("createdDate DESC");
        // 设置查询范围
        if (ask.getQueryAll() != null && !ask.getQueryAll())
        {
            ask.setCreatedBy(user);
        }
        ask.setOrderBy("ask.createdDate DESC");
        Page<Ask> page = askService.findPage(qpage, ask);
        return page;
    }

    @RequestMapping(value = "getCardDetailList")
    @ResponseBody
    public List<NotificationScheduling> getCardDetailList() {
        NotificationScheduling ns = new NotificationScheduling();
        ns.setType(1);
        return askService.findNotificationList(ns);
    }

    @RequestMapping(value = "saveAsk")
    @ResponseBody
    public String saveAsk(@RequestBody AskVo vo) {
        Ask ask = vo.converAsk(vo);
        if(vo.getType()==1 && StringUtils.isBlank(vo.getRecordId())){
            return askService.insert(ask);
        }else{
            return askService.update(ask);
        }
    }

    @RequestMapping(value = "deleteAsk")
    @ResponseBody
    public String saveAsk(@RequestParam("recordId") String recordId) {
        return askService.deleteAsk(recordId);
    }
}
