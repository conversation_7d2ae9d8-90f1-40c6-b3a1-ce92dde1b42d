package com.kyb.pcberp.modules.production.web;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.production.entity.SupplierProcessPopo;
import com.kyb.pcberp.modules.production.service.SupplierProcessPopoService;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

/**
 * 供应商外发工序设置Controller
 * 
 * <AUTHOR> 2018-05-08
 *         
 */
@Controller
@RequestMapping(value = "${adminPath}/production/poposet")
public class SupplierProcessPopoController extends BaseController
{
    
    @Autowired
    private SupplierProcessPopoService popoService;
    
    @RequestMapping(value = "list")
    public String list()
    {
        return "modules/production/poposet";
    }
    
    /**
     * 加载页面所需要的数据
     */
    @RequestMapping(value = "load/data")
    @RequiresPermissions(value = "poposet:view")
    @ResponseBody
    public Map<String, List<?>> loadData()
    {
        return popoService.loadData();
    }
    
    /**
     * 加载页面列表
     */
    @RequestMapping(value = "page", method = RequestMethod.POST)
    @RequiresPermissions(value = "poposet:view")
    @ResponseBody
    public Page<SupplierProcessPopo> page(@RequestBody SupplierProcessPopo poposet, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        if (poposet.getQueryAll() != null & !poposet.getQueryAll())
        {
            poposet.setCreatedBy(user);
        }
        poposet.setCompany(user.getCompany());
        Page<SupplierProcessPopo> poposetPage = new Page<SupplierProcessPopo>(request, response);
        if (StringUtils.isNotBlank(poposet.getPageNo()) && StringUtils.isNotBlank(poposet.getPageSize()))
        {
            poposetPage.setPageNo(Integer.parseInt(poposet.getPageNo()));
            poposetPage.setPageSize(Integer.parseInt(poposet.getPageSize()));
            if (StringUtils.isNotBlank(poposet.getOrderBy()))
            {
                poposetPage.setOrderBy(poposet.getOrderBy());
            }
        }
        else
        {
            poposetPage.setPageNo(Global.PAGE_NO);
            poposetPage.setPageSize(Global.PAGE_SIZE);
        }
        return popoService.findPage(poposetPage, poposet);
    }
    
    /**
     * 保存设置
     */
    @RequestMapping(value = "save", method = RequestMethod.POST)
    @RequiresPermissions(value = "poposet:edit")
    @ResponseBody
    public String save(@RequestBody SupplierProcessPopo popo)
    {
        return popoService.savePopo(popo);
    }
    
    /**
     * 保存设置
     */
    @RequestMapping(value = "savesecond", method = RequestMethod.POST)
    @RequiresPermissions(value = "poposet:edit")
    @ResponseBody
    public String savesecond(@RequestBody SupplierProcessPopo popo)
    {
        return popoService.savePopoSecond(popo);
    }
    
    /**
     * 更新状态
     */
    @RequestMapping(value = "update", method = RequestMethod.POST)
    @RequiresPermissions(value = "poposet:edit")
    @ResponseBody
    public String updateStatus(@RequestBody SupplierProcessPopo popo)
    {
        return popoService.updateStatus(popo);
    }
    
    /**
     * 查询设置详情
     */
    @RequestMapping(value = "getPoposet", method = RequestMethod.POST)
    @RequiresPermissions(value = "poposet:view")
    @ResponseBody
    public SupplierProcessPopo getPoposet(@RequestBody SupplierProcessPopo popo)
    {
        return popoService.getPoposetDetails(popo);
    }
}
