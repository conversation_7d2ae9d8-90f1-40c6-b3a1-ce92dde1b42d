/**
 * @license angular-bootstrap-datetimepicker
 * Copyright 2016 Knight Rider Consulting, Inc. http://www.knightrider.com
 * License: MIT
 */
.datetimepicker {
  border-radius: 4px;
  direction: ltr;
  display: block;
  margin-top: 1px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 320px;
}
.datetimepicker > div {
  display: none;
}
.datetimepicker .hour,
.datetimepicker .minute {
  height: 34px;
  line-height: 34px;
  margin: 0;
  width: 25%;
}
.datetimepicker .table {
  margin: 0;
}
.datetimepicker .table td,
.datetimepicker .table th {
  border: 0;
  border-radius: 4px;
  height: 20px;
  text-align: center;
}
.datetimepicker .minute:hover,
.datetimepicker .hour:hover,
.datetimepicker .day:hover,
.datetimepicker .switch:hover,
.datetimepicker .left:hover,
.datetimepicker .right:hover {
  background: #eee;
  cursor: pointer;
}
.datetimepicker .disabled,
.datetimepicker .disabled:hover {
  background: none;
  color: #ebebeb;
  cursor: default;
}
.datetimepicker .active,
.datetimepicker .active:hover,
.datetimepicker .active.disabled,
.datetimepicker .active.disabled:hover {
  background-color: #04c;
  background-image: linear-gradient(to bottom, #08c, #04c);
  background-repeat: repeat-x;
  border-color: #04c #04c #002a80;
  color: #fff;
  -webkit-filter: progid:dximagetransform.microsoft.gradient(startColorstr='#08c', endColorstr='#04c', GradientType=0);
  filter: progid:dximagetransform.microsoft.gradient(startColorstr='#08c', endColorstr='#04c', GradientType=0);
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.datetimepicker .current,
.datetimepicker .current:hover,
.datetimepicker .current.disabled,
.datetimepicker .current.disabled:hover {
  background-color: #e5e5e5;
}
.datetimepicker .active:hover,
.datetimepicker .active:hover:hover,
.datetimepicker .active.disabled:hover,
.datetimepicker .active.disabled:hover:hover,
.datetimepicker .active:active,
.datetimepicker .active:hover:active,
.datetimepicker .active.disabled:active,
.datetimepicker .active.disabled:hover:active,
.datetimepicker .active.active,
.datetimepicker .active:hover.active,
.datetimepicker .active.disabled.active,
.datetimepicker .active.disabled:hover.active,
.datetimepicker .active.disabled,
.datetimepicker .active:hover.disabled,
.datetimepicker .active.disabled.disabled,
.datetimepicker .active.disabled:hover.disabled,
.datetimepicker td.active[disabled],
.datetimepicker td.active:hover[disabled],
.datetimepicker td.active.disabled[disabled],
.datetimepicker td.active.disabled:hover[disabled],
.datetimepicker span.active[disabled],
.datetimepicker span.active:hover[disabled],
.datetimepicker span.active.disabled[disabled],
.datetimepicker span.active.disabled:hover[disabled] {
  background-color: #04c;
}
.datetimepicker span {
  border-radius: 4px;
  cursor: pointer;
  display: block;
  float: left;
  height: 54px;
  line-height: 54px;
  margin: 1%;
  width: 23%;
}
.datetimepicker span:hover {
  background: #eee;
}
.datetimepicker .past,
.datetimepicker .future {
  color: #999;
}

/*# sourceMappingURL=datetimepicker.css.map */
