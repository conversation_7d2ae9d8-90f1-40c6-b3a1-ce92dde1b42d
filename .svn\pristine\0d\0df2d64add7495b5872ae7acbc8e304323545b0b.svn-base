<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.purch.dao.PurchasingDetailAttachementsDao">
    <insert id="insert">
        INSERT INTO pu_purchasing_detail_attachements(
            purchasingDetailId,
            orgfilename,
            realfilename,
            fileurl,
            companyId,
            createdBy,
            createdDate,
            activeFlag
        ) VALUES (
                     #{purchasingDetailId},
                     #{orgFileName},
                     #{realFileName},
                     #{fileUrl},
                     #{company.recordId},
                     #{createdBy.recordId},
                     #{createdDate},
                     1
                 )
    </insert>

    <update id="delete">
        UPDATE pu_purchasing_detail_attachements SET activeFlag = 0 WHERE companyId = #{company.recordId} AND recordId = #{recordId}
    </update>

    <select id="findList" resultType="PurchasingDetailAttachements">
        SELECT * FROM pu_purchasing_detail_attachements WHERE companyId = #{company.recordId} AND purchasingDetailId = #{purchasingDetailId} AND activeFlag = 1
    </select>
</mapper>