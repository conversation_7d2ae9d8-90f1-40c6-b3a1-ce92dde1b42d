package com.kyb.pcberp.modules.stock.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;

import java.math.BigDecimal;
import java.util.Date;

public class InventoryMantissa extends DataEntity<InventoryMantissa>
{
    private static final long serialVersionUID = 1L;

    private String companyId; //公司id

    private Date dateOfEntry; //入库日期

    private String placeForStoragea; //存放位置

    private String placeForStorageb;

    private String numberId; //生产编号

    private BigDecimal storageQuantity; //入库数量

    private String placeForA;

    private String placeForB;

    private String stockNumber;

    private BigDecimal areaLength; //长

    private BigDecimal areaWidth; //宽

    private BigDecimal numberPieces; //pnl数

    private BigDecimal shippingQty; //出货数量

    private BigDecimal singleArea; //单只面积

    private BigDecimal currentBalance; //当前结存

    private BigDecimal areaBalance; //结存面积

    private Integer day; //结存天数

    private Integer markedRed;//标红

    private Integer inputOutputType; //出入库类型

    private BigDecimal acreage; //面积

    private String materialUseNo;

    private Integer surplusQuantity;  // 剩余数量

    private Integer occupiedQuantity; // 占用数量

    private Integer useQty; // 使用数量

    private String remark;  //备注

    private Integer availableQty;

    private String deailArea; //面积

    private BigDecimal storageArea; // 入库面积

    private BigDecimal shippingArea; // 出库面积

    private BigDecimal area;

    private String haveStockFlag;

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public Date getDateOfEntry() {
        return dateOfEntry;
    }

    public String getDateOfEntryStr(){
        if (dateOfEntry == null){
            return "";
        }
        return DateUtils.formatDate(dateOfEntry);
    }

    public String getCreatedDateStr(){
        if (createdDate == null){
            return "";
        }
        return DateUtils.formatDate(createdDate);
    }

    public void setDateOfEntry(Date dateOfEntry) {
        this.dateOfEntry = dateOfEntry;
    }

    public String getPlaceForStoragea() {
        return placeForStoragea;
    }

    public void setPlaceForStoragea(String placeForStoragea) {
        this.placeForStoragea = placeForStoragea;
    }

    public String getNumberId() {
        return numberId;
    }

    public void setNumberId(String numberId) {
        this.numberId = numberId;
    }

    public BigDecimal getStorageQuantity() {
        return storageQuantity;
    }

    public void setStorageQuantity(BigDecimal storageQuantity) {
        this.storageQuantity = storageQuantity;
    }

    public String getPlaceForStorageb() {
        return placeForStorageb;
    }

    public void setPlaceForStorageb(String placeForStorageb) {
        this.placeForStorageb = placeForStorageb;
    }

    public String getPlaceForA() {
        return placeForA;
    }

    public void setPlaceForA(String placeForA) {
        this.placeForA = placeForA;
    }

    public String getPlaceForB() {
        return placeForB;
    }

    public void setPlaceForB(String placeForB) {
        this.placeForB = placeForB;
    }

    public String getStockNumber() {
        return stockNumber;
    }

    public void setStockNumber(String stockNumber) {
        this.stockNumber = stockNumber;
    }

    public BigDecimal getAreaLength() {
        return areaLength;
    }

    public void setAreaLength(BigDecimal areaLength) {
        this.areaLength = areaLength;
    }

    public BigDecimal getAreaWidth() {
        return areaWidth;
    }

    public void setAreaWidth(BigDecimal areaWidth) {
        this.areaWidth = areaWidth;
    }

    public BigDecimal getNumberPieces() {
        return numberPieces;
    }

    public void setNumberPieces(BigDecimal numberPieces) {
        this.numberPieces = numberPieces;
    }

    public BigDecimal getShippingQty() {
        return shippingQty;
    }

    public void setShippingQty(BigDecimal shippingQty) {
        this.shippingQty = shippingQty;
    }

    public BigDecimal getSingleArea() {
        return singleArea;
    }

    public void setSingleArea(BigDecimal singleArea) {
        this.singleArea = singleArea;
    }

    public BigDecimal getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(BigDecimal currentBalance) {
        this.currentBalance = currentBalance;
    }

    public BigDecimal getAreaBalance() {
        return areaBalance;
    }

    public void setAreaBalance(BigDecimal areaBalance) {
        this.areaBalance = areaBalance;
    }

    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public Integer getMarkedRed() {
        return markedRed;
    }

    public void setMarkedRed(Integer markedRed) {
        this.markedRed = markedRed;
    }

    public Integer getInputOutputType() {
        return inputOutputType;
    }

    public void setInputOutputType(Integer inputOutputType) {
        this.inputOutputType = inputOutputType;
    }

    public String getInputOutputTypeStr()
    {
        String result = null;
        if(null == inputOutputType)
        {
            return result;
        }
        switch (inputOutputType)
        {
            case 1:
                result = "入库";
                break;
            case 2:
                result = "出库";
                break;
        }
        return result;
    }

    public BigDecimal getAcreage() {
        return acreage;
    }

    public void setAcreage(BigDecimal acreage) {
        this.acreage = acreage;
    }

    public String getMaterialUseNo() {
        return materialUseNo;
    }

    public void setMaterialUseNo(String materialUseNo) {
        this.materialUseNo = materialUseNo;
    }

    public Integer getSurplusQuantity() {
        return surplusQuantity;
    }

    public void setSurplusQuantity(Integer surplusQuantity) {
        this.surplusQuantity = surplusQuantity;
    }

    public Integer getOccupiedQuantity() {
        return occupiedQuantity;
    }

    public void setOccupiedQuantity(Integer occupiedQuantity) {
        this.occupiedQuantity = occupiedQuantity;
    }

    public Integer getAvailableQty() {
        return availableQty;
    }

    public void setAvailableQty(Integer availableQty) {
        this.availableQty = availableQty;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDeailArea() {
        return deailArea;
    }

    public void setDeailArea(String deailArea) {
        this.deailArea = deailArea;
    }

    public BigDecimal getStorageArea() {
        return storageArea;
    }

    public void setStorageArea(BigDecimal storageArea) {
        this.storageArea = storageArea;
    }

    public BigDecimal getShippingArea() {
        return shippingArea;
    }

    public void setShippingArea(BigDecimal shippingArea) {
        this.shippingArea = shippingArea;
    }

    public BigDecimal getArea() {
        return area;
    }

    public void setArea(BigDecimal area) {
        this.area = area;
    }

    public String getHaveStockFlag()
    {
        return haveStockFlag;
    }

    public void setHaveStockFlag(String haveStockFlag)
    {
        this.haveStockFlag = haveStockFlag;
    }
}
