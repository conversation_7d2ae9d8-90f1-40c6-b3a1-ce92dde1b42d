package com.kyb.pcberp.modules.stock.vo;

import java.math.BigDecimal;
import java.util.List;

import com.kyb.pcberp.common.persistence.DataEntity;

public class StockCheckVo extends DataEntity<StockCheckVo>
{
    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    
    private String materialNo;// 物料no
    
    private BigDecimal startStock;// 期初
    
    private BigDecimal endStock;// 结余
    
    private BigDecimal inStock;// 累计入库
    
    private BigDecimal outStock;// 累计出库
    
    private Integer queryMonth; // 查下月份
    
    private Integer startMonth;
    
    private Integer endMonth;
    
    private Integer flag;// 是否存在问题；1、存在。
    
    List<StockCheckVo> list = null;
    
    public BigDecimal getStartStock()
    {
        return startStock;
    }
    
    public void setStartStock(BigDecimal startStock)
    {
        this.startStock = startStock;
    }
    
    public BigDecimal getEndStock()
    {
        return endStock;
    }
    
    public void setEndStock(BigDecimal endStock)
    {
        this.endStock = endStock;
    }
    
    public BigDecimal getInStock()
    {
        return inStock;
    }
    
    public void setInStock(BigDecimal inStock)
    {
        this.inStock = inStock;
    }
    
    public BigDecimal getOutStock()
    {
        return outStock;
    }
    
    public void setOutStock(BigDecimal outStock)
    {
        this.outStock = outStock;
    }
    
    public Integer getQueryMonth()
    {
        return queryMonth;
    }
    
    public void setQueryMonth(Integer queryMonth)
    {
        this.queryMonth = queryMonth;
    }
    
    public Integer getFlag()
    {
        return flag;
    }
    
    public void setFlag(Integer flag)
    {
        this.flag = flag;
    }
    
    public String getMaterialNo()
    {
        return materialNo;
    }
    
    public void setMaterialNo(String materialNo)
    {
        this.materialNo = materialNo;
    }
    
    public Integer getStartMonth()
    {
        return startMonth;
    }
    
    public void setStartMonth(Integer startMonth)
    {
        this.startMonth = startMonth;
    }
    
    public Integer getEndMonth()
    {
        return endMonth;
    }
    
    public void setEndMonth(Integer endMonth)
    {
        this.endMonth = endMonth;
    }

    public List<StockCheckVo> getList()
    {
        return list;
    }

    public void setList(List<StockCheckVo> list)
    {
        this.list = list;
    }
    
    
    
}
