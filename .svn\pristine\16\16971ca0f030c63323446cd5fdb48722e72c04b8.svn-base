
/* dictCtrl */
kybApp.controller('dictCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'BaseUtil' , 'CommonUtil', function($rootScope, $scope, upida, $timeout,BaseUtil, CommonUtil) {
    $scope.$on('$viewContentLoaded', function() {
        // initialize core components
        MainCtrl.initAjax();

        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });

    var vm = this;

    $scope.shouldAutoStart = false;
    $scope.itemOptions = {
        steps:[
        {
            element: '#itemStep1',
            intro: "当您打开页面后会自动加载字典项至列表，查看字典项的字典值信息，可单击首列字典项名称或双击该行进入！",
            position: 'top'
        },
        {
            element: '#itemStep2',
            intro: "单击右边的排序按钮进行排序，箭头向上为升序，箭头向下为降序，若为上下则未排序！",
            position: 'bottom'
        },
        {
            element: '#itemStep3',
            intro: '此处为分页区域，您可以点击您想查看的页面！',
            position: 'left'
        },
        {
            element: '#itemStep4',
            intro: "此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！",
            position: 'right'
        },
        {
            element: '#itemStep5',
            intro: '此处为查询条件，可设置不同的条件，点击右下角的查询来过滤信息！',
            position: 'bottom'
        },
        {
            element: '#itemStep6',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
    
	$scope.valueOptions = {
        steps:[
        {
            element: '#valueStep1',
            intro: "字典值列表，非系统级别的数据可以修改，系统级别的数据不可修改！",
            position: 'top'
        },
        {
            element: '#valueStep2',
            intro: "若为用户级别的数据（亲，您自己添加的字典值），可以快速删除！",
            position: 'bottom'
        },
        {
            element: '#valueStep3',
            intro: '此处为分页区域，您可以点击您想查看的页面！',
            position: 'left'
        },
        {
            element: '#valueStep4',
            intro: "此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！",
            position: 'right'
        },
        {
            element: '#valueStep5',
            intro: '点击可添加字典值，为用户级别的数据！',
            position: 'left'
        },
        {
            element: '#valueStep6',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
	
	$scope.valueReadOnlyOptions = {
		steps:[
	       {
	    	   element: '#valueStep1',
	    	   intro: "字典值列表，非系统级别的数据不可修改，若为用户级别的数据（亲，您自己添加的字典值），可单击首列字典值或双击该行进入修改！",
	    	   position: 'top'
	       },
	       {
	    	   element: '#valueStep3',
	    	   intro: '此处为分页区域，您可以点击您想查看的页面！',
	    	   position: 'left'
	       },
	       {
	    	   element: '#valueStep4',
	    	   intro: "此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！",
	    	   position: 'right'
	       },
	       {
	    	   element: '#valueStep6',
	    	   intro: '谢谢使用，再见。'
	       }
	       ],
	       showStepNumbers: false,
	       exitOnOverlayClick: true,
	       exitOnEsc:true,
	       nextLabel: '<strong>下一步!</strong>',
	       prevLabel: '<span style="color:green">上一步</span>',
	       skipLabel: '退出',
	       doneLabel: '谢谢'
	};
	
	
	 $scope.infoOptions = {
            steps:[
            {
                element: '#infoStep1',
                intro: "字典值详细信息录入区域，带 * 的为必填项",
                position: 'top'
            },
            {
                element: '#infoStep2',
                intro: "您输入完成后，请点击保存！",
                position: 'top'
            },
            {
                element: '#infoStep3',
                intro: '谢谢使用，再见。'
            }
            ],
            showStepNumbers: false,
            exitOnOverlayClick: true,
            exitOnEsc:true,
            nextLabel: '<strong>下一步!</strong>',
            prevLabel: '<span style="color:green">上一步</span>',
            skipLabel: '退出',
            doneLabel: '谢谢'
	   };
	 
    $scope.help = function(){
    	if (vm.tabs.item.active){
    		$scope.helpItem();
    	} else if (vm.tabs.value.active) {
    		if (vm.right.edit){
    			$scope.helpValue();
    		} else {
    			$scope.helpValueReadOnly();
    		}
    	}else if(vm.tabs.valueForm.active){
    		$scope.infoOptionsShow();
    	}
    };
    
    vm.focus = 1;
   
    vm.saveflag = false;
    
    //用于字典项修改前的值
    vm.dictAfterValue = "";
    
    // tj 判断是否多次点击
    vm.clicks = true;
    
    // 权限
    vm.right = {};
    
    //当前登录用户
    vm.loginUser = {};
    
    // 分页数据
    vm.page = {};

    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];

    // 字典项分页数据
    vm.page.dictItem = {};
    vm.page.dictItem.data = {}; // 对象
    vm.page.dictItem.condition = []; // 条件
    vm.page.dictItem.pageSize = 10;  // 显示数量
    vm.page.dictItem.pageNo = 1;
    vm.page.dictItem.url = "sys/dict/itempage";

    // 字典值分页数据
    vm.page.dictValue = {};
    vm.page.dictValue.data = {}; // page对象
    vm.page.dictValue.condition = [];
    vm.page.dictValue.pageSize = 10; // 每页显示数量
    vm.page.dictValue.pageNo = 1;
    vm.page.dictValue.url = "sys/dict/valuepage";

    // 查询条件参数
    vm.query = {}; // 查询对象
    vm.query.dictItem = {}; // 字典项
    vm.query.dictItem.itemName = {};
    vm.query.dictItem.itemName.name = "itemName";
    vm.query.dictItem.itemName.value = "";
    vm.query.dictItem.sort = {};
    vm.query.dictItem.sort.name = "orderBy";
    vm.query.dictItem.sort.value = "itemName ASC";
    

    vm.query.dictValue = {}; // 字典值
    vm.query.dictValue.value = {};
    vm.query.dictValue.value.name = "value";
    vm.query.dictValue.value.value = "";

    // tabs控制
    vm.tabs = {
        item: {active:true},
        value: {active:false, show: false, hasClose: false},
        valueForm: {active:false, show: false}
    };

    // 默认排序按时间排序排序处理
    vm.sort = {};
    vm.sort.itemName = {both: false, desc: true, asc: false}; 
    vm.sortClick = function(col){
        vm.sort[col].both = false;
        if (!vm.sort[col].desc && !vm.sort[col].asc){
            vm.sort[col].asc = true;
        }else {
            if (vm.sort[col].asc){
                vm.sort[col].desc = true;
                vm.sort[col].asc = false;
            }else {
                vm.sort[col].desc = false;
                vm.sort[col].asc = true;
            }
        }

        for(var p in vm.sort){
            if (p !== col){
                vm.sort[p].desc = false;
                vm.sort[p].asc = false;
                vm.sort[p].both = true;
            }
        }

        vm.query.dictItem.sort.value = vm.sort[col].asc ? col + " ASC" :  col + " DESC";

        // 查询数据
        vm.init(vm.page.dictItem.pageNo,
                vm.page.dictItem.pageSize,
                vm.page.dictItem.condition,
                vm.page.dictItem.url,
                vm.type.item);
    };
    
    // 数据级别
    vm.flags = [
        {
            "flagId": 0,
            "flagName": "用户"
        },
        {
            "flagId": 1,
            "flagName": "系统"
        }
    ];

    vm.effectiveTypeList = [
        {
            id:4,
            value:'无'
        }, {
        id:1,
        value:"年"
    },{
        id:2,
        value:"月"
    },{
        id:3,
        value:"日"
    }];

    // 编辑对象
    vm.item = {};

    // 字典项列表
    vm.dictItems = [];

    // 字典值列表
    vm.dictValues= [];
    
    // 删除字典值主键Id
    vm.delRecordId= -1;
    vm.delMsg = "";
    vm.delRecordItem = {};

    // 修改标题
    vm.modTitle = "";

    // 当前类型、字典项或字典值
    vm.type= {item:0, value:1};

    // 显示字典值
    vm.showValue = function () {
        vm.tabs.value.active = true;
        vm.tabs.value.show = true;
        vm.tabs.value.hasClose = false;
    };

    // 隐藏字典值
    vm.hideValue = function () {
        vm.tabs.value.show = false;
        // 当前字典值编辑处理激活状态时，关闭字典值列表时不进行跳转
        vm.tabs.value.hasClose = true;
        if(!vm.tabs.valueForm.active)
        {
            $timeout(function(){
                vm.tabs.item.active = true;
            });
        }else {
            $timeout(function(){
                vm.tabs.valueForm.active = true;
            });
        }

    };

    // 显示字典值编辑
    vm.showValueForm = function () {
    	vm.formScope.editForm.$setPristine();
        vm.tabs.valueForm.show = true;
        vm.tabs.valueForm.active = true;
    };

    // 隐藏字典值编辑
    vm.hideValueForm = function () {
        vm.tabs.valueForm.show = false;

        // 字典值已经关闭后，当关闭字典值编辑的时候直接跳到字典项列表
        if(vm.tabs.value.hasClose)
        {
            $timeout(function(){
                vm.tabs.item.active = true;
            });
        } else {
            $timeout(function(){
                vm.tabs.value.active = true;
            });
        }
    };

    // 分页按钮单击处理
    vm.doPage = function(type, page, pageSize, total)
    {
        if (type === vm.type.item)
        {
            vm.page.dictItem.pageNo = page;
            vm.init(page, pageSize, vm.page.dictItem.condition, vm.page.dictItem.url, vm.type.item);
        }else {
            vm.page.dictValue.pageNo = page;
            vm.init(page, pageSize, vm.page.dictValue.condition, vm.page.dictValue.url, vm.type.value, vm.item.item);
        }
    };
    
    //字典值焦点离开时触发
    vm.save = function()
    {	
    	vm.saveflag = true;
    	if (typeof vm.item.value !== "undefined")
    	{
    		if(vm.item.item.recordId == 49){
    			if(vm.item.value.indexOf("-") >= 0){
    				vm.check = vm.item.value.split("-");
    				vm.checkOne = vm.check[0];
    				vm.checkTwo = vm.check[1];
    				if(vm.check.length > 2){
    					vm.saveflag = false;
    				    vm.message = "格式不对，请填写0-10这种格式！";
   		                $('#static').modal();
   		                return;
    				}
    				if(isNaN(vm.checkOne) || isNaN(vm.checkTwo)){
    					vm.saveflag = false;
    					 vm.message = "格式不对，请填写数字，比如：0-10！";
    		             $('#static').modal();
    		             return;
    				}
    				if(Number(vm.checkOne) >= Number(vm.checkTwo)){
    					vm.saveflag = false;
    					vm.message = "前一个平米数必须大于后一个平米数，比如：0-10！";
   		                $('#static').modal();
   		                return;
    				}
    			}else{
    			    vm.saveflag = false;
    				vm.message = "格式不对，请填写0-10这种格式！";
		            $('#static').modal();
		            return;
    			}
    		}
    		if(vm.item.item.recordId == 64){
    			let prenct = 0;
    			if(vm.page.dictValue.data.list && vm.page.dictValue.data.list.length > 0)
    			{
        			for(let i=0;i<vm.page.dictValue.data.list.length;i++){
        				if(vm.page.dictValue.data.list[i].remark && vm.item.recordId != vm.page.dictValue.data.list[i].recordId){
        					prenct = Number(prenct) + Number(vm.page.dictValue.data.list[i].remark);
        				}
        			}
    			}
    			prenct = Number(prenct) + Number(vm.item.remark);
    			if(prenct > 100) {
    				vm.message = "产能分配百分比不能大于100%！";
		            $('#static').modal();
		            return;
    			}
    		}
    		if(vm.item.item.recordId == 77||vm.item.item.recordId == 78)
    		{
    			if(Number(vm.item.planRate) > 100) {
    				vm.message = "目标值百分比不能大于100%！";
		            $('#static').modal();
		            return;
    			}
    		}
    		
    		MainCtrl.blockUI({
        	    animate: true,
        	});
    		var obj = {};
    		obj.recordId = vm.item.item.recordId;
    		obj.value = vm.item.value;
    		upida.post("sys/dict/querySameshortName",obj).then(function (data) {
    			if(vm.modTitle == "修改")
    			{	
    				vm.saveflag = data==0 || vm.item.value == vm.dictAfterValue;
    				vm.submitMod();
    			} else {
    				vm.saveflag = (data==0 && vm.item.value);
    				vm.submitMod();
    			}
    		    MainCtrl.unblockUI();
    		})
    		
    	}
    };

    /*
     * 兼容条件查询与普通查询
     * no 当前页
     * size 当前页显示数量
     * condition 是数组，可放入多个条件对象，如{name:"type", value:"1"}  名称对应实体字段名称，值对应页面输入的值
     * url 链接
     * type 类型
     */
    vm.init = function(no, size, condition, url, type, needItem){
    	MainCtrl.blockUI({
    		animate: true,
    	});
    	
    	// 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;

        // 设置字典项
        if (type === vm.type.value)
        {
            reqData.item = needItem;
        }else {
        	condition.push({
                name: vm.query.dictItem.sort.name,
                value: vm.query.dictItem.sort.value
            });
        }
        
        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condition);

        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
        	var data = {};
            data.url = url;
            data.data = result;
            data.condition = condition;
            data.pageSize = size;
            data.pageNo = no;
            // 如果结果为空
            if (typeof data.data === 'undefined' || typeof data.data.list === 'undefined')
            {
                data.data.startCount = 0;
                data.data.endCount = 0;
            } else {
                // 计算开始数
                data.data.startCount = (result.pageNo - 1) * result.pageSize + 1;
                // 计算结束数
                data.data.endCount = (result.pageNo - 1) * result.pageSize + result.list.length;
                if (type === vm.type.value){
                    // 排序
                    data.data.list.sort(CommonUtil.compare("seqNum", true))
                }
            }

            if (type === vm.type.item)
            {
                vm.page.dictItem = data;
            }else {
                // 排序显示
                vm.page.dictValue = data;
            }
            MainCtrl.unblockUI();
        });
    };

    // 页面显示数量改变
    vm.pageSizeChange = function(type){
        if (type === vm.type.item)
        {
            vm.init(1,
                vm.page.dictItem.pageSize,
                vm.page.dictItem.condition,
                vm.page.dictItem.url,
                type);
        }else {
            vm.init(1,
                vm.page.dictValue.pageSize,
                vm.page.dictValue.condition,
                vm.page.dictValue.url,
                type,
                vm.item.item);
        }
    };

    //添加用户字典项时，选择了一类字典项加载它的最大排序
    vm.loadDictSeqnum = function(){
    	upida.get("sys/dict/maxseqnum?itemId="+vm.item.item.recordId).then(function (data) {
    		vm.item.seqNum = data + 10;
        });
    	
    };
    // 查询数据
    vm.doQueryItem = function(){
        // 设置查询条件
        var condition = [];
        if (vm.query.dictItem.itemName.value !== "")
        {
            condition.push({
                name: vm.query.dictItem.itemName.name,
                value: vm.query.dictItem.itemName.value
            });
        }

        // 查询数据
        vm.init(1,
            vm.page.dictItem.pageSize,
            condition,
            vm.page.dictItem.url,
            vm.type.item);
    };

    vm.setFormScope = function (scope) {
        vm.formScope = scope;
    };
    
    vm.processList = [];
    vm.showDetial = function(index){
    	// 得到待编辑的数据字典值对象
        vm.item = {};
        vm.item.item = angular.copy(vm.page.dictItem.data.list[index]);
        
        // 初始化第一页，条件为空
        vm.init(1,
            vm.page.dictValue.pageSize,
            [],
            vm.page.dictValue.url,
            vm.type.value,
            vm.item.item);
        
        // 责任方/测试工序加载工序
        vm.processList = [];
        if("77" == vm.item.item.recordId || "78" == vm.item.item.recordId||"79" == vm.item.item.recordId)
        {
        	MainCtrl.blockUI({
        		animate: true,
        	});
    		upida.post("sys/dict/getNameList",vm.item.item).then(function (data) {
    			vm.processList = data;
                var obj = {category:'无',recordId:null};
                if(vm.processList && vm.processList.length > 0)
                {
                    vm.processList.splice(0,0,obj);
                }
    	        // 显示值视图
    	        vm.showValue();
    	        MainCtrl.unblockUI();
    		})
        }
        else
        {
            // 显示值视图
            vm.showValue();
        }
    };

    // 单击修改
    vm.modDictValue = function(index){
    	if (!vm.right.edit){
    		return;
    	}
    	
        // 从服务器加载字典项列表
        loadItems();
        
        //根据字典项index加载字典值
        loadValues();
        
        vm.focus += 1;
        
        // 原始的字典项
        var sItem = angular.copy(vm.item.item);

        // 待修改的字典值对象
        vm.item = angular.copy(vm.page.dictValue.data.list[index]);
        vm.item.item = sItem;
        vm.dictAfterValue = vm.item.value;
        
        vm.processIdList = [];
        if(vm.item.processIds)
        {
        	let processIds = vm.item.processIds.split(",");
    		for(let processId of processIds)
    		{
            	for(let process of vm.processList)
            	{
        			if(process.recordId == processId)
        			{
        				vm.processIdList.push(process);
        				break;
        			}
            	}
    		}
        }
        
        vm.testTypes = [];
        if(vm.item.testTypeIds)
        {
        	let testTypeIdss = vm.item.testTypeIds.split(",");
    		for(let testType of testTypeIdss)
    		{
            	for(let tt of vm.testTypeList)
            	{
        			if(tt.recordId == testType)
        			{
        				vm.testTypes.push(tt);
        				break;
        			}
            	}
    		}
        }
        //加载原因
        vm.loadCause();
        // 显示添加窗口
        vm.modTitle = "修改";
        vm.showValueForm();
    };

    // 单击添加
    vm.addDictValue = function(){
    	// 加载字典项
        loadItems();
        //加载字典值
        loadValues();
        
        vm.focus += 1;
        
        // 原始的字典项
        var sItem = angular.copy(vm.item.item);
        vm.item = {};
        vm.processIdList = [];
        vm.testTypes = [];
        // TODO 默认用户数据级别
        vm.item.operLevel = 0;
        vm.item.item = sItem;
        upida.get("sys/dict/maxseqnum?itemId="+sItem.recordId).then(function (data) {
    		vm.item.seqNum = data + 10;
        });
        
        // 显示添加窗口
        vm.modTitle = "添加";
        vm.showValueForm();
    };

    // 单击添加
    vm.saveItem = {};
    vm.saveDictItemOpen = function(row){
        if(row)
        {
            vm.saveItem = angular.copy(row);
        }
        else
        {
            vm.saveItem = {};
        }
        $('#saveItemOpen').modal();
    };

    vm.saveDictItem = function()
    {
        if(!vm.saveItem.itemName)
        {
            vm.message = "键名不能为空！";
            $('#static').modal();
            return;
        }
        if(!vm.clicks)
        {
            vm.message = "请勿重复点击！";
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("sys/dict/saveItem",vm.saveItem).then(function (data) {
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                vm.saveItem = {};
                // 初始化第一页，条件为空
                vm.init(1, vm.page.dictItem.pageSize, [], vm.page.dictItem.url, vm.type.item);
            }
            vm.clicks = true;
            MainCtrl.unblockUI();
        });
    }

    vm.delDictItem = function(row)
    {
        if(!vm.clicks)
        {
            vm.message = "请勿重复点击！";
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("sys/dict/deleteItem",row).then(function (data) {
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                vm.saveItem = {};
                // 初始化第一页，条件为空
                vm.init(1, vm.page.dictItem.pageSize, [], vm.page.dictItem.url, vm.type.item);
            }
            vm.clicks = true;
            MainCtrl.unblockUI();
        });
    }


    // 单击取消
    vm.cacelBtn = function(){
        vm.hideValueForm()
    };

    // 创建和修改字典
    vm.submitMod = function(){
    	if(vm.clicks){
    	    vm.formScope.editForm.$setDirty();
            if(vm.formScope.editForm.$valid){
            	vm.clicks = false; // tj 2017-09-07  用户是否多次点击
            	dictValue={};
            	for(var i =0; i<vm.dictValues.length; i++)
            	{
            		dictValue=vm.dictValues[i];
            		if (dictValue.recordId != vm.item.recordId && dictValue.value == vm.item.value) 
                    {
                    	vm.message = "字典值格式不正确或者已存在！";
                    	$('#static').modal();
                    	vm.clicks = true;// tj 2017-09-07  用户是否多次点击
                    	return;
                    }
            	}
            	
            	MainCtrl.blockUI({
            		animate: true,
            	});
            	if(!vm.item.processFlag){
            		if(vm.processIdList && vm.processIdList.length > 0)
                	{
                		let processIds = null;
                		let name = null;
                		angular.forEach(vm.processIdList,function(p){
                			if(processIds)
                			{
                				processIds = processIds +","+ p.recordId;
                			}
                			else
                			{
                				processIds = p.recordId;
                			}
                			
                			if(name)
                			{
                				name = name +","+ p.category;
                			}
                			else
                			{
                				name = p.category;
                			}
                		});
                		vm.item.processIds = processIds;
                		vm.item.name = name;
                	}
            	}
            	
            	if(vm.testTypes && vm.testTypes.length > 0)
            	{
            		let testTypeIds = null;
            		angular.forEach(vm.testTypes,function(p){
            			if(testTypeIds)
            			{
            				if(p.recordId)
            				{
            					testTypeIds = testTypeIds +","+ p.recordId;
            				}
            				else
            				{
            					testTypeIds = testTypeIds +","+ p;
            				}
            				
            			}
            			else
            			{
            				if(p.recordId)
            				{
            					testTypeIds = p.recordId;
            				}
            				else
            				{
            					testTypeIds = p;
            				}
            				
            			}
            		});
            		vm.item.testTypeIds = testTypeIds;
            	}
            	
            	
            		upida.post("sys/dict/save", vm.item).then(function (data) {
            		// 重新查询数据
            		vm.init(vm.page.dictValue.pageNo,
            				vm.page.dictValue.pageSize,
            				vm.page.dictValue.condition,
            				vm.page.dictValue.url,
            				vm.type.value,
            				vm.item.item);
            			
            		// 初始化数据
            		var sItem = angular.copy(vm.item.item);
            		vm.item = {};
            		vm.item.item = sItem;
            		vm.hideValueForm();
            			
            		// 提示信息
            		vm.message = data;
            		$('#static').modal();
            		vm.clicks = true;// tj 2017-09-07  用户是否多次点击
            	});
            }
    	}
    };
    
    // 删除字典值
    vm.deleteDictValue = function(index){
        vm.delRecordId = vm.page.dictValue.data.list[index].recordId;
        vm.delRecordItem = angular.copy(vm.page.dictValue.data.list[index].item);
        if(!vm.queryAll && vm.loginUser != null && vm.loginUser.recordId 
           != vm.page.dictValue.data.list[index].createdBy.recordId || vm.queryAll == "undefined")
        {
          	 vm.message = "您没有权限删除此字典项！";
               $('#static').modal();
               return;
        }
        
        vm.delMsg = "删除字典值“"+ vm.page.dictValue.data.list[index].value +"”会删除工艺组关联信息确定继续吗？";
        // 提示信息
        $('#staticRemove').modal();
    };

    // 做字典值的删除操作
    vm.doDeleteDictValue = function(){
    	MainCtrl.blockUI({
    		animate: true,
    	});
    	
        upida.post("sys/dict/delete/" + vm.delRecordId, null).then(function (data) {
            // 重新查询数据
            vm.init(vm.page.dictValue.data.pageNo,
                vm.page.dictValue.data.pageSize,
                vm.page.dictValue.condition,
                vm.page.dictValue.url,
                vm.type.value,
                vm.delRecordItem);

            vm.delRecordId= -1;
            vm.delRecordItem = {};
            vm.message = data;
            $('#static').modal();
        });
    };
    
    vm.copyCompany = function() {
    	upida.post("sys/dict/copyCompany").then(function (data) {
         });
	};
    
    // 字典值的隐藏
    vm.hiddenDictValue = function(index){
        vm.delRecordId = vm.page.dictValue.data.list[index].recordId;
        
        vm.delMsg = "隐藏字典值“"+ vm.page.dictValue.data.list[index].value +"会删除工艺组关联信息确定继续”吗？";
        // 提示信息
        $('#staticHideValue').modal();
    }
    //做字典值的隐藏操作
    vm.doHiddenDictValue = function(index){
    	if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
	    MainCtrl.blockUI({
    		animate: true,
    	});
    	upida.post("sys/dict/insertDictCompany/" + vm.delRecordId, null).then(function (data) {
    		// 重新查询数据
    		vm.init(vm.page.dictValue.pageNo,
    				vm.page.dictValue.pageSize,
    				vm.page.dictValue.condition,
    				vm.page.dictValue.url,
    				vm.type.value,
    				vm.item.item);
    			
    		// 初始化数据
    		var sItem = angular.copy(vm.item.item);
    		vm.item = {};
    		vm.item.item = sItem;
    		vm.hideValueForm();
    			
    		// 提示信息
    		vm.message = data;
    		$('#static').modal();
    		vm.clicks = true;// tj 2017-09-07  用户是否多次点击
        	MainCtrl.unblockUI();

    	});
    	
    	}
    }

    vm.setAttribute = function(index){
        vm.attribute = angular.copy(vm.page.dictValue.data.list[index]);
        $('#setAttribute').modal();
    }
    vm.setAttributeSave = function(){
        if(!vm.clicks)
        {
            vm.message = "请勿重复点击!";
            $('#static').modal();
            return;
        }
        if(!vm.attribute.materialType)
        {
            vm.message = "请填写资料类型!";
            $('#static').modal();
            return;
        }
        if(!vm.attribute.orderType)
        {
            vm.message = "请填写订单类型!";
            $('#static').modal();
            return;
        }
        vm.clicks = false;
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("sys/dict/setAttributeSave",vm.attribute).then(function (data) {
            if(data == "success")
            {
                vm.message = "设置成功!";
                $('#static').modal();
                vm.attribute = {};
                vm.init(1,vm.page.dictValue.pageSize,[],vm.page.dictValue.url,vm.type.value,vm.item.item);
            }
            else
            {
                vm.message = "设置失败!";
                $('#static').modal();
            }
            MainCtrl.unblockUI();
            vm.clicks = true;
        });
    }

    // 做字典值的显示操作
    vm.showDictValue = function(index){
    	MainCtrl.blockUI({
    		animate: true,
    	});
    	vm.delRecordId = vm.page.dictValue.data.list[index].recordId;
    	upida.post("sys/dict/deleteDictCompany/" + vm.delRecordId, null).then(function (data) {
    		// 重新查询数据
    		vm.init(vm.page.dictValue.pageNo,
    				vm.page.dictValue.pageSize,
    				vm.page.dictValue.condition,
    				vm.page.dictValue.url,
    				vm.type.value,
    				vm.item.item);
    			
    		// 初始化数据
    		var sItem = angular.copy(vm.item.item);
    		vm.item = {};
    		vm.item.item = sItem;
    		vm.hideValueForm();
    			
    		// 提示信息
    		vm.message = data;
    		$('#static').modal();
    	});
    }

    // 加载权限
    function loadRight(){
    	vm.queryAll = CommonUtil.dataRangeIsAll("11101", BaseUtil.getMenuList());
    	MainCtrl.blockUI({
    		animate: true,
    	});
    	
        vm.loginUser = BaseUtil.getUser();

    	upida.get("common/rightall?prefix=sys:dict").then(function(data){
    		vm.right.view = data.view;
    		vm.right.edit = data.edit;
    		vm.right.manage = data.manage;
    		
    		// 初始化第一页，条件为空
            vm.init(1,
                vm.page.dictItem.pageSize,
                [],
                vm.page.dictItem.url,
                vm.type.item);
    	});
    	vm.loadShowColunmList();
    	vm.loadDutyTypeList();
    	vm.loadtestTypeList();
    }

    // 获取字典项
    function loadItems()
    {
        // 查询Item
        if (vm.dictItems.length <= 0)
        {
            $timeout(function(){
                upida.get("sys/dict/items").then(function (data) {
                    vm.dictItems = data;
                });
            });
        }
    }
    // 获取字典值
    function loadValues()
    {           
	      if(vm.dictValues.length >=0)
		   {
	    		$timeout(function(){
	    	 upida.post("sys/dict/Values",vm.item.item).then(function (data) {
	         	 vm.dictValues = data;
	          });
	        });
		 }        
    }
    
    vm.loadCause = function()
    {
    	upida.post("sys/dict/getCauseList",{"itemValueId":vm.item.recordId}).then(function (data) {
        	 vm.item.causeList = data;
         });
    }
    
    vm.selectCategory = function(process)
    {
    	vm.item.processIds = process.recordId;
    	vm.item.name = process.category;
    	vm.item.processFlag = 1;
    	vm.item.type = process.type;
    }
    
    vm.addCause = function()
    {
    	if(!vm.item.causeList)
    	{
    		vm.item.causeList = [];
    	}
    	var isNew = false;
    	angular.forEach(vm.item.causeList,function(p){
			if(!p.recordId)
			{
				isNew = true;
			}
		});
    	if(isNew)
    	{
    		vm.message = "请先保存";
    		$('#static').modal();
    		return;
    	}
    	vm.item.causeList.push({"cause":"","status":1});
    }
    vm.causeClick = true;
    vm.saveCause = function(row)
    {
    	if(!vm.causeClick)
    	{
    		return;
    	}
    	vm.causeClick = false;
    	MainCtrl.blockUI({
    		animate: true,
    	});
    	row.itemValueId= vm.item.recordId;
    	row.status = 1;
    	upida.post("sys/dict/saveCause",row).then(function (data) {
			if(data&&data.success)
			{
				row.recordId = data.obj.recordId;
				vm.message = "保存成功";
	    		$('#static').modal();
			}else if(data&&data.msg)
			{
				vm.message = data.msg;
	    		$('#static').modal();
			}else
			{
				vm.message = "保存失败";
	    		$('#static').modal();
			}
		    MainCtrl.unblockUI();
		    vm.causeClick = true;
		});
    }
    
    vm.reworkCause = function(row)
    {
    	if(!vm.causeClick)
    	{
    		return;
    	}
    	vm.causeClick = false;
    	row.status = 1;
    	upida.post("sys/dict/saveCause",row).then(function (data) {
			if(data&&data.success)
			{
				row = data.obj;
				vm.message = "恢复成功";
	    		$('#static').modal();
			}else if(data&&data.msg)
			{
				vm.message = data.msg;
	    		$('#static').modal();
			}else
			{
				vm.message = "恢复失败";
	    		$('#static').modal();
			}
		    MainCtrl.unblockUI();
		    vm.causeClick = true;
		});
    	
    }
    
    vm.delCause = function(row)
    {
    	if(!vm.causeClick)
    	{
    		return;
    	}
    	vm.causeClick = false;
    	row.status = 2;
    	upida.post("sys/dict/saveCause",row).then(function (data) {
			if(data&&data.success)
			{
				row = data.obj;
				vm.message = "作废成功";
	    		$('#static').modal();
			}else if(data&&data.msg)
			{
				vm.message = data.msg;
	    		$('#static').modal();
			}else
			{
				vm.message = "作废失败";
	    		$('#static').modal();
			}
		    MainCtrl.unblockUI();
		    vm.causeClick = true;
		});
    }
    vm.checkTypeList = [
                   {"name":"高压","value":1},
                   {"name":"耐压","value":3},
                   {"name":"其它","value":2}
                   ];
    // 获取字典值
    vm.showColunmList = [];
    vm.loadShowColunmList = function()
    {           
    	upida.post("sys/dict/Values",{"recordId":80}).then(function (data) {
    		vm.showColunmList = data;
         });       
    }
    
    vm.dutyTypeList = [];
    vm.loadDutyTypeList = function()
    {
    	upida.post("sys/dict/Values",{"recordId":81}).then(function (data) {
    		vm.dutyTypeList = data;
         });   
    }
    
    vm.testTypeList = [];
    vm.loadtestTypeList = function()
    {
    	upida.post("sys/dict/Values",{"recordId":78}).then(function (data) {
    		vm.testTypeList = data;
         });   
    }
    
    vm.dictValue = {};
    vm.setPrecentOpen = function(index)
    {
    	vm.dictValue = angular.copy(vm.page.dictValue.data.list[index]);
    	$('#setPrecentStatic').modal();
    }
    
    vm.setPrecent = function()
    {
    	if(!vm.clicks)
    	{
			vm.message = "请勿重复点击!";
    		$('#static').modal();
    		return;
    	}
    	if(!vm.dictValue.precent)
    	{
			vm.message = "请填写费率!";
    		$('#static').modal();
    		return;
    	}
    	vm.clicks = false;
    	MainCtrl.blockUI({
    		animate: true,
    	});
    	upida.post("sys/dict/setPrecent",vm.dictValue).then(function (data) {
    		if(data == "success")
    		{
    			vm.message = "设置费率成功!";
        		$('#static').modal();
        		vm.dictValue = {};
                vm.init(1,vm.page.dictValue.pageSize,[],vm.page.dictValue.url,vm.type.value,vm.item.item);
    		}
    		else
    		{
    			vm.message = "设置费率失败!";
        		$('#static').modal();
    		}
    		MainCtrl.unblockUI();
    		vm.clicks = true;
         });  
    }

    vm.productSchedule = function()
    {
        upida.post("sys/dict/getProductSchedule").then(function (data) {
        });
    }
    
    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
    });
}]);
