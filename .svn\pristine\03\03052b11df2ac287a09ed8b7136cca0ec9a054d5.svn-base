package com.kyb.pcberp.modules.production.util.adapter;

import com.kyb.pcberp.modules.production.entity.ProduceExecuteRecord;
import com.kyb.pcberp.modules.production.entity.ProduceRecord;

/**
 * 生产过数适配器 
 */
public class PdAdapter
{
    /** zjn 2019-05-09 生产记录对象转生产执行记录对象*/
    public ProduceExecuteRecord produceRecordToProduceExecuteRecord(ProduceRecord pr) 
    {
        ProduceExecuteRecord per = new ProduceExecuteRecord();
        per.setProduceBatch(pr.getProduceBatch());
        per.setCompany(pr.getCompany());
        per.setProcess(pr.getProcess());
        per.setProduceBatchDetail(pr.getProduceBatchDetail());
        per.setProduceRecord(pr);
        return per;
    }
}
