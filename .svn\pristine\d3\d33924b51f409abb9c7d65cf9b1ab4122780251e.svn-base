<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.wechat.dao.MessageDao">
    <select id="getMessageList" resultType="IcloudMessage">
        SELECT
            *
        FROM
            icloud_sm_message
        WHERE
            receiveId = #{receiveId}
        <if test="message != null and message != ''">
            AND message LIKE CONCAT("%", #{message}, "%")
        </if>
        <if test="flag != null and flag != ''">
            AND flag = #{flag}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY createdDate DESC
        <if test="pageNo != null and pageNo != '' and pageSize != null and pageSize != ''">
            LIMIT ${pageNo},${pageSize};
        </if>
    </select>

    <update id="updateFlag">
        UPDATE icloud_sm_message
            SET flag = 2,
            lastUpdDate = NOW()
        WHERE
            recordId = #{recordId}
    </update>

    <insert id="addMessage" useGeneratedKeys="true" keyProperty="recordId">
        INSERT INTO icloud_sm_message (
            message,
            flag,
            status,
            createdId,
            createdDate,
            receiveId
        )VALUES (
            #{message},
            #{flag},
            #{status},
            #{createdId},
            NOW(),
            #{receiveId}
        )
    </insert>
</mapper>