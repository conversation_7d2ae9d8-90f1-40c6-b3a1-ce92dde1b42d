package com.kyb.pcberp.modules.wechat.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.wechat.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface ProductDao extends CrudDao<ProductDao>
{
    InterProduct getCustomerProduct(InterProduct interProduct);

    InterProduct getSupplierProduct(InterProduct interProduct);

    List<InterProduct> getCustomerProductList(InterProduct interProduct);

    List<InterProduct> getSupplierProductList(InterProduct interProduct);

    List<InterProductDeail> getCustomerProductDeail(InterProduct interProduct);

    List<InterProductDeail> getSupplierProductDeail(InterProduct interProduct);

    List<InterProductDeail> getCustomerProductDeailList(@Param("ids") String ids);

    List<InterProductDeail> getSupplierProductDeailList(@Param("ids") String ids);

    void updateCustomerProductFlag(InterProduct interProduct);

    void updateSupplierProductFlag(InterProduct interProduct);

    Integer getCustomerProductNum(InterProduct interProduct);

    Integer getSupplierProductNum(InterProduct interProduct);

    void updateCustomerProduct(InterProduct interProduct);

    void updateSupplierProduct(InterProduct interProduct);

    void updateSupplierProductSpe(InterProduct interProduct);

    void insertCustomerProduct(InterProduct interProduct);

    void expSupplierProductList(@Param("list") List<InterProduct> list, @Param("userId") String userId);

    void expSupplierProductDeailList(@Param("list") List<InterProductDeail> list);

    void insertSupplierProduct(InterProduct interProduct);

    void deleteCustomerDeailList(InterProduct interProduct);

    void deleteSupplierDeailList(InterProduct interProduct);

    void insertCustomerDeailList(InterProduct interProduct);

    void insertSupplierDeailList(InterProduct interProduct);

    void batchInsertStockRecordList(InterProduct interProduct);

    void batchUpdateCustomerStockList(@Param("list") List<InterProductStockRecord> list);

    void batchUpdateSupplierStockList(@Param("list") List<InterProductStockRecord> list);

    List<InterProductStockRecord> getStockHistory(InterProduct interProduct);

    void insertAttachMent(InterAttachMent interAttachMent);

    List<InterAttachMent> getAttachMentList(InterProductDeail interProductDeail);

    void delAttachMent(@Param("recordId") String recordId);

    Integer checkProductCount(InterProduct interProduct);

    void confirmOrder(InterProduct interProduct);

    void deleteOrder(InterProduct interProduct);

    List<InterProduct> getCustomerProductPage(InterProduct interProduct);

    List<String> getOrderIdList(InterProduct interProduct);

    Integer getCustomerProductCount();

    InterProduct selectOnlineWindow(InterProduct interProduct);

    List<InterProductStockRecord> getStockRecordList(@Param("icloudCompanyId") String icloudCompanyId, @Param("erpId") String erpId, @Param("productId") String productId);

    void insertSupplierStockRecord(InterProductStockRecord interProductStockRecord);

    InterProduct getInterProduct(InterProduct interProduct);

    void updateSupplierStocks(InterProduct interProduct);

    void saveDemand(IcloudDemand icloudDemand);

    List<InterProduct> getCustomerProductTwoList(InterProduct interProduct);

    List<IcloudNotice> getCurrNotice(IcloudNotice icloudNotice);

    void saveFeedback(IcloudFeedback icloudFeedback);

    List<IcloudFeedback> getHistoryBackList(IcloudFeedback icloudFeedback);

    List<IcloudDemand> getHistoryDemandList(IcloudDemand icloudDemand);

    void finishBack(IcloudFeedback icloudFeedback);

    List<InterProductDeail> getCustomerProductDeailListTwo(@Param("list") List<InterProduct> list);
}
