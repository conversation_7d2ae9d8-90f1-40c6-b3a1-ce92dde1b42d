/* here you can put your own css to customize and override the theme */

/***
Rounded Portlets
***/
/*
.portlet {
	border-radius: 4px !important;
}

.portlet .portlet-title {
	border-radius: 4px 4px 0px 0px !important;
}

.portlet .portlet-body,
.portlet .portlet-body .form-actions  {
	border-radius: 0px 0px 4px 4px !important;
}
*/

/*
Change Quick Sidebar Width
*/

/*
.page-quick-sidebar-wrapper {
  right: -370px;
  width: 370px;
}

.page-quick-sidebar-open.page-quick-sidebar-push-content .page-sidebar-wrapper {
  margin-left: -370px;
}

.page-quick-sidebar-open.page-quick-sidebar-push-content .page-footer {
  margin-right: 370px;
  margin-left: -370px;
}

.page-sidebar-reversed.page-quick-sidebar-open.page-quick-sidebar-push-content .page-sidebar-wrapper {
  margin-right: 370px;
}

.page-quick-sidebar-open.page-quick-sidebar-push-content.page-quick-sidebar-full-height .page-header {
  margin-left: -370px;
}

.page-quick-sidebar-wrapper .page-quick-sidebar .page-quick-sidebar-list {
  width: 370px !important;
}

.page-quick-sidebar-wrapper .page-quick-sidebar .page-quick-sidebar-item {
  width: 370px !important;
  margin-left: 370px;
}

.page-quick-sidebar-wrapper .page-quick-sidebar .page-quick-sidebar-content-item-shown .page-quick-sidebar-list {
  margin-left: -370px;
}
*/

.pagination {margin:8px 0;} .pagination .controls a{border:0;}
.pagination .controls input{border:0;color:#999;width:30px;padding:0;margin:-3px 0 0 0;text-align:center;}



@media (max-width: 991px) {
  .form .form-bordered .form-group .control-label {
    /* 991px */
    padding-top: 10px;
  }
}

.header-companyName{
	padding-right: 20px;
	line-height:46px;
	height:46px;
	font-size:20px;
	color:#c6cfda;	
}

@media (max-width: 580px) {
  .companyName-hide-on-mobile {
    display: none;
  }
}

.btn-margin-left-15 {
	margin-left:15px;
}

.btn-margin-left-30 {
	margin-left:30px;
}

.table-pageSize-width{
	margin-top:8px; 
	width:100px;
}

.margin-top-custom5{
	margin-top:5px;
}

.page-bar-margin-top{
	margin-top:-15px;
}

.tabset-margin-top{
	margin-top:-15px;
}

.list-margin-top{
	margin-top:-10px;
}

.page-margin-top{
	margin-top:-10px;
}

.set-cursor-pointer{
	cursor: pointer;	
}

.btn-default-width{
	min-width: 120px;
}

.btn-fast-default-width{
	min-width: 70px;
}

.sorting_desc {
    background: url('../img/sort/sort_desc.png') no-repeat center right;
}

.sorting_asc {
    background: url('../img/sort/sort_asc.png') no-repeat center right;
}


.sorting {
	background: url('../img/sort/sort_both2.png') no-repeat center right;
}

.sorting, .sorting_asc, .sorting_desc {
    padding-right: 18px;
    cursor: pointer;
}

.nav-tabs {
    margin-bottom: 0;
}

.tab-pane {
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    border-radius: 0px 0px 5px 5px;
    padding: 10px;
}

.col-sm-1-custom-first {
  float: left;
  width: 5.33333333%;
  position: relative;
  min-height: 1px;
  padding-right: 8px;
  padding-left: 15px;
}

.col-sm-1-custom-second {
  float: left;
  width: 5.33333333%;
  position: relative;
  min-height: 1px;
  padding-right: 1px;
  padding-left: 1px;
}

.col-sm-5-custom-unit {
  float: left;
  width: 41.33333333%;
  position: relative;
  min-height: 1px;
  padding-right: 3px;
  padding-left: 8px;
}

.col-sm-1-custom {
  float: left;
  width: 5.33333333%;
  position: relative;
  min-height: 1px;
  padding-right: 5px;
  padding-left: 5px;
}

.table > tbody > tr.colorFlagInspect > td {
   	background: #f2dede;
}
    


.grid {
  width: 100%;
  height: 550px;
}

.grid .ui-grid-row .success {
    background: #dff0d8;
    color: #3c763d;
}

.grid .ui-grid-row .warning {
  	background: #fcf8e3;
    color: #8a6d3b;
}

.grid .ui-grid-row .danger {
    background: #f2dede;
    color: #a94442;
}
