<%@ page contentType="text/html;charset=UTF-8"%>
<div ng-intro-options="contract.introListOptions" ng-intro-method="contract.helpList" ng-intro-autostart="contract.shouldAutoStart"></div>
<div ng-intro-options="contract.introDetailOptions" ng-intro-method="contract.helpDetail" ng-intro-autostart="contract.shouldAutoStart"></div>
<div ng-intro-options="contract.introContractDetailOptions" ng-intro-method="contract.helpContractDetail" ng-intro-autostart="contract.shouldAutoStart"></div>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li><i class="fa fa-home"></i> <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i></li>
        <li><a href="javascript:;">订单管理</a> <i class="fa fa-angle-right"></i>
        </li>
        <li><a ui-sref="order.contract">成品订单管理</a></li>
    </ul>

    <div class="page-toolbar">
        <button class="btn btn-fit-height default pull-right" ng-click="contract.help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
    </div>
</div>

<!-- END 导航-->

<tabset class="tabset-margin-top">
    <tab heading="合同列表" active="contract.tabs.viewForm.active">
        <div class="panel panel-default">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption" ng-click="contract.loadOnlineOrderList(1)"><span style="color: red">线上下单清单{{contract.onlineOrderList.length ? contract.onlineOrderList.length : 0}}款</span></div>
                </div>
            </div>
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">

                    <div class="row" id="step6">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">合同编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="contract.query.userCode.value"
                                           disable-valid-styling="true" disable-invalid-styling="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">客户编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="contract.query.cusCode.value"
                                           disable-valid-styling="true" disable-invalid-styling="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">客户名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="contract.query.cusname.value"
                                           disable-valid-styling="true" disable-invalid-styling="true" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">

                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">客户订单号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="contract.query.cusorderNo.value"
                                           disable-valid-styling="true" disable-invalid-styling="true" />
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">审核时间：</label>
                                <div class="col-sm-7 col-md-8">
                                    <div class="input-prepend input-group">
                                        <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                        <input type="text" class="form-control" disable-auto-validate="true"
                                               ng-blur="contract.initDate(contract.time)"
                                               kyb-daterange
                                               kyb-daterange-options="contract.rangeOptions"
                                               ng-model="contract.time"
                                               placeholder="请选择时间段">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">状态：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select  class="form-control"
                                             ng-model="contract.query.genSelected"
                                             ng-options="gen.value as gen.name for gen in contract.query.genList"
                                             disable-auto-validate="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">客户物料号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="contract.query.cusorderNo.customerMaterialNo"
                                           disable-valid-styling="true" disable-invalid-styling="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">客户型号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="contract.query.customerModel.value"
                                           disable-valid-styling="true" disable-invalid-styling="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">订单类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select  class="form-control"
                                             ng-model="contract.query.switchSelected"
                                             ng-options="gen.value as gen.name for gen in contract.query.switchList"
                                             disable-auto-validate="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">生产编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="contract.query.craftNo.value"
                                           disable-valid-styling="true" disable-invalid-styling="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">业务员：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="contract.query.salesMan.value"
                                           disable-valid-styling="true" disable-invalid-styling="true" />
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">销售部门：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select  class="form-control"
                                             ng-model="contract.query.deptId"
                                             ng-options="gen.recordId as gen.name for gen in contract.deptListShow"
                                             disable-auto-validate="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">合同来源：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select  class="form-control"
                                             ng-model="contract.query.erpOrIcloudSelected"
                                             ng-options="gen.value as gen.name for gen in contract.erpOrIcloudList"
                                             disable-auto-validate="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">逾期合同：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select  class="form-control"
                                             ng-model="contract.query.overDueSelected"
                                             ng-options="item.value as item.name for item in contract.query.overDueList"
                                             disable-auto-validate="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">终端合同号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="contract.finalContractNo"
                                           disable-valid-styling="true" disable-invalid-styling="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">逾期状态：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="contract.overdueStatusQuery">
                                        <option value="">所有</option>
                                        <option value="1">正常</option>
                                        <option value="2">关闭</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">生产状态：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="contract.productionStatusQuery">
                                        <option value="">所有</option>
                                        <option value="1">已投料</option>
                                        <option value="2">未生产</option>
                                        <option value="3">已入库</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right"
                                    ng-click="contract.doQuery()">
                                <i class="fa fa-search"></i> 查&nbsp;询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">合同列表</div>
                <div class="actions" id="step7">
                    <!-- <div class="portlet-input input-inline input-small">
	                        <button class="btn green blue-hoki btn-default-width" ng-click="contract.importContractData()" ><i class="fa fa-upload"></i>合同导入</button>
	                </div> -->
                    <div class="portlet-input input-inline input-small">
                        <button type="button" class="btn green btn-default-width"
                                ng-click="contract.addContract(1)" ng-if="contract.right.edit && contract.addBtnFlag">
                            <i class="fa fa-plus"></i> 添加合同
                        </button>
                    </div>
                    <div class="portlet-input input-inline input-small">
                        <button type="button" class="btn green btn-default-width"
                                ng-click="contract.sentPrdorderOpen()" ng-if="contract.right.edit">
                            <i class="fa fa-plus"></i> 生成采购合同
                        </button>
                    </div>
                    <div class="portlet-input input-inline input-small">
                        <form action="a/order/contract/export" method="post" enctype="multipart/form-data" target="hidden_frame">
                            <input type="text" ng-show="false" name="no" value="{{contract.query.userCode.value}}" />
                            <input type="text" ng-show="false" name="customer.no" value="{{contract.query.cusCode.value}}" />
                            <input type="text" ng-show="false" name="customer.name" value="{{contract.query.cusname.value}}" />
                            <input type="text" ng-show="false" name="customerPo" value="{{contract.query.cusorderNo.value}}" />
                            <input type="text" ng-show="false" name="orderBy" value="{{contract.query.sort.value}}" />
                            <input type="text" ng-show="false" name="status" value="{{contract.query.genSelected}}" />
                            <input type="text" ng-show="false" name="sentTimeStartQr" value="{{contract.query.sentTimeStartQr.value}}"/>
                            <input type="text" ng-show="false" name="sentTimeEndQr" value="{{contract.query.sentTimeEndQr.value}}"/>
                            <input type="text" ng-show="false" name="queryAll" value="{{contract.queryAll}}"/>
                            <input type="text" ng-show="false" name="customerMaterialNo" value="{{contract.query.cusorderNo.customerMaterialNo}}"/>
                            <input type="text" ng-show="false" name="customerModel" value="{{contract.query.customerModel.value}}"/>
                            <input type="text" ng-show="false" name="inventorySwitch" value="{{contract.query.switchSelected}}"/>
                            <input type="text" ng-show="false" name="salesMan" value="{{contract.query.salesMan.value}}"/>
                            <input type="text" ng-show="false" name="craftNo" value="{{contract.query.craftNo.value}}"/>
                            <input type="text" ng-show="false" name="icloudType" value="{{contract.query.erpOrIcloudSelected}}"/>
                            <input type="text" ng-show="false" name="overDueStr" value="{{contract.query.overDueSelected}}"/>
                            <input type="text" ng-show="false" name="closingCaseQuery" value="{{contract.closingCaseQuery}}"/>
                            <input type="text" ng-show="false" name="branchQuery" value="{{contract.user.branchId}}"/>
                            <input type="text" ng-show="false" name="deptId" value="{{contract.query.deptId}}"/>
                            <input type="text" ng-show="false" name="finalContractNo" value="{{contract.finalContractNo}}"/>
                            <input type="text" ng-show="false" name="overdueStatus" value="{{contract.overdueStatusQuery}}"/>
                            <input type="text" ng-show="false" name="productionStatus" value="{{contract.productionStatusQuery}}"/>
                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出合同</button>
                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
                        </form>
                    </div>

                    <div class="portlet-input input-inline input-small">
                        <button class="btn green blue-hoki btn-default-width" ng-click="contract.openGroupCenterList()" ng-if="contract.right.edit" >清单</button>
                    </div>

                    <div class="portlet-input input-inline input-small">
                        <button type="button" class="btn green btn-default-width" ng-click="contract.lossOrders()">
                            <i class="fa fa-plus"></i> 亏损下单
                        </button>
                    </div>
                </div>
            </div>
            <span class="label text-default" style="background-color:yellow;margin-left:15 px">未生成通知单</span>
            <div class="portlet-body">
                <div class="table-scrollable" style="margin-top:0px !important" id="step1">
                    <table id="contentTable"
                           class="table table-striped table-bordered table-condensed table-advance table-hover">
                        <thead>
                        <tr class="heading" id="step2">
                            <th>
                                <checkbox ng-model="contract.allPageChecked" value="1" name="test" ng-click="contract.selectAllPage()"></checkbox>
                            </th>
                            <th id="step3">操&nbsp;作&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th width="15%"
                                ng-class="{'sorting': contract.sort.no.both, 'sorting_desc': contract.sort.no.desc, 'sorting_asc': contract.sort.no.asc}"
                                ng-click="contract.sortClick('no')">合同编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <!-- 	<th>引用报价单</th> -->
                            <th width="15%"
                                ng-class="{'sorting': contract.sort.totalAmt.both, 'sorting_desc': contract.sort.totalAmt.desc, 'sorting_asc': contract.sort.totalAmt.asc}"
                                ng-click="contract.sortClick('totalAmt')">合同金额&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th width="15%">合同平米</th>
                            <th>销售公司&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>销售部门</th>
                            <th width="15%"
                                ng-class="{'sorting': contract.sort.customerNo.both, 'sorting_desc': contract.sort.customerNo.desc, 'sorting_asc': contract.sort.customerNo.asc}"
                                ng-click="contract.sortClick('customerNo')">终端客户编号</th>
                            <th width="15%"
                                ng-class="{'sorting': contract.sort.customerNo.both, 'sorting_desc': contract.sort.customerNo.desc, 'sorting_asc': contract.sort.customerNo.asc}"
                                ng-click="contract.sortClick('customerNo')">客户编号</th>
                            <th width="15%"
                                ng-class="{'sorting': contract.sort.customerName.both, 'sorting_desc': contract.sort.customerName.desc, 'sorting_asc': contract.sort.customerName.asc}"
                                ng-click="contract.sortClick('customerName')">客户名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th width="15%">业务员</th>
                            <th width="15%">跟单员</th>
                            <th
                                    ng-class="{'sorting': contract.sort.customerPo.both, 'sorting_desc': contract.sort.customerPo.desc, 'sorting_asc': contract.sort.customerPo.asc}"
                                    ng-click="contract.sortClick('customerPo')">客户订单号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>含税状态</th>
<%--                            <th>成本费用</th>--%>
                            <th width="10%"
                                ng-class="{'sorting': contract.sort.createdDate.both, 'sorting_desc': contract.sort.createdDate.desc, 'sorting_asc': contract.sort.createdDate.asc}"
                                ng-click="contract.sortClick('createdDate')">下单时间&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th width="10%"
                                ng-class="{'sorting': contract.sort.createdDate.both, 'sorting_desc': contract.sort.createdDate.desc, 'sorting_asc': contract.sort.createdDate.asc}"
                            >审核时间&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th width="5%" ng-class="{'sorting': contract.sort.status.both, 'sorting_desc': contract.sort.status.desc, 'sorting_asc': contract.sort.status.asc}"
                                ng-click="contract.sortClick('status')">合同状态&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>终端合同号</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in contract.page.data.list"
                            ng-dblclick="contract.showContract($index)">

                            <td>
                                <checkbox ng-if="row.showCheckFlag" ng-model="row.checked" ng-change="contract.getRecordIds()"></checkbox>
                            </td>
                            <td ng-class="{danger: row.dangerPrice}">
                                <a class="btn btn-xs btn-default" href="javascript:void(0)"   title="复制添加合同"
                                   ng-if="contract.right.edit && row.status !== '200205'  && row.status != '1' && row.status != '2' && row.status != '4' && !row.interCustomRecordId" ng-click="contract.cloneCheckRepet($index)">
                                    <i class="fa fa-copy font-green"></i> 复制添加</a>
                                <form class="inline" ng-form-commit name="printForm" action="a/order/contract/print" method="get" target="hidden_frame">
                                    <input type="text" id="recordId" ng-show="false" name="recordId" ng-model="row.recordId" />
                                    <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="row.status != '1' && row.status != '2' && row.status != '4'" ng-click="contract.printContract(printForm)" title="打印合同">
                                        <i class="fa fa-print font-blue-hoki"></i> 打&nbsp;印</a>
                                    <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
                                </form>
                                <a class="btn btn-xs btn-default" href="javascript:void(0)" ng-if="row.status == '200201' && contract.right.edit" ng-click="contract.commitAuditOpen($index)">
                                    <i class="fa fa-check-square-o font-green"></i> 审&nbsp;批</a>
                                <a class="btn btn-xs btn-default" href="javascript:void(0)"   title="删除合同"
                                   ng-if="contract.right.edit  && row.status == '200201'" ng-click="contract.delContractOpen($index)">
                                    <i class="fa fa-times font-red"></i> 删&nbsp;除</a>
                                <a class="btn btn-xs btn-default" href="javascript:void(0)"   title="作废合同"
                                   ng-if="contract.right.edit && row.status == '200207' && (!row.groupCenterCount || row.groupCenterCount == 0 || row.groupCenterCount == 1)" ng-click="contract.showInvalidCheckRepet($index)">
                                    <i class="fa fa-undo font-red"></i> 作&nbsp;废</a>
                                <a class="btn btn-xs btn-default" ui-sref="order.notification({contractId: row.recordId})"  titie="生产通知单快速链接"
                                   ng-if="contract.right.edit && row.status == '200207' && row.isnotification !=='能' && contract.showNotification === true && !contract.ValiteCompanyUtil.valiteSaleCompany">
                                    <i class="fa fa-fighter-jet"></i>生成通知单</a>

                                <a class="btn btn-xs btn-default" ng-if="contract.right.edit && ((row.groupCenterCount && row.groupCenterCount > 0) || !row.groupCenterCount || row.groupCenterCount == 0) && (row.status == 200202 || row.status == 200203 || row.status == 200207 || row.status == 200204)&& row.status != 200210" ng-click="contract.editCustomerPoOpen($index)"> <i class="fa fa-save"></i> 修改订单号</a>

                                <a class="btn btn-xs btn-default" ng-if="contract.right.edit && ((row.groupCenterCount && row.groupCenterCount > 0 && contract.ValiteCompanyUtil.valiteSaleCompany) || !row.groupCenterCount || row.groupCenterCount == 0) && (row.status == 200202 || row.status == 200203 || row.status == 200207)"
                                   ng-click="contract.splitDeail($index)"> <i class="fa fa-save"></i> 分拆明细</a>
                                <a class="btn btn-xs btn-default" href="javascript:void(0)" ng-if="row.status != '200201' && row.status != 200205 && row.status != '200209' && row.status != '200204' && row.status != 200210 && (contract.ValiteCompanyUtil.valiteSaleCompany || ((row.saleComId == contract.ValiteCompanyUtil.factId || !row.saleComId) && contract.companyId == contract.ValiteCompanyUtil.factId) && contract.right.edit)" ng-click="contract.commitReflashAuditOpen($index)">
                                    <i class="fa fa-check-square-o font-green"></i> 反&nbsp;审</a>
                                <a class="btn btn-xs btn-default" href="javascript:void(0)" ng-if="(row.status == '60001'||row.status == '200209') && (contract.ValiteCompanyUtil.valiteSaleCompany || (row.saleComId == contract.ValiteCompanyUtil.factId && contract.companyId == contract.ValiteCompanyUtil.factId))" ng-click="contract.cancelAuditOpen($index)">
                                    <i class="fa fa-undo font-red"></i> 撤回审批</a>
                            </td>
                            <td ng-if="row.noNotification" bgcolor="yellow"><a ng-bind="row.no" href="javascript:void(0)" ng-click="contract.showContract($index)"></a></td>
                            <td ng-if="!row.noNotification" ng-class="{danger: row.dangerPrice}"><a ng-bind="row.no" href="javascript:void(0)" ng-click="contract.showContract($index)"></a></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.totalAmt  | currency:'':2" style="text-align: right;"></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.orderArea"></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.saleName"></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.deptName"></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.customerNo" ng-if="row.customerNo"></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.customer.no" ng-if="!row.customerNo"></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.customer.no" ></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.customer.name"  ></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.user.userName"  ></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.createdBy.userName"></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.customerPo"></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.taxDescriptValue"></td>
<%--                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.costFee"></td>--%>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.orderDate"></td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.lastUpdDate"></td>
                            <td>
                                <span ng-if="row.statusStr === '未确认' " class="label label-sm label-default">未确认</span>
                                <span ng-if="row.statusStr === '已确认' " class="label label-sm label-success">已确认</span>
                                <span ng-if="row.statusStr === '已生产' " class="label label-sm label-info">已生产</span>
                                <span ng-if="row.statusStr === '关闭' " class="label label-sm label-primary">关闭</span>
                                <span ng-if="row.statusStr === '对账关闭' " class="label label-sm label-primary">对账关闭</span>
                                <span ng-if="row.statusStr === '已完成' " class="label label-sm label-success">已完成</span>
                                <span ng-if="row.statusStr === '已审核' " class="label label-sm label-danger">已审核</span>
                                <span ng-if="row.statusStr === '已作废' " class="label label-sm label-warning">已作废</span>
                                <span ng-if="row.statusStr === '审批中' " class="label label-sm label-danger">审批中</span>
                                <span ng-if="row.statusStr !== '审批中'
                            	&& row.statusStr !== '已作废'
                            	&& row.statusStr !== '已审核'
                            	&& row.statusStr !== '已完成'
                            	&& row.statusStr !== '已送货'
                            	&& row.statusStr !== '关闭'
                            	&& row.statusStr !== '已生产'
                            	&& row.statusStr !== '已确认'
                            	&& row.statusStr !== '未确认'
                            	&& row.statusStr !== '对账关闭'
                            	" class="label label-sm label-danger">
                            		{{row.statusStr}}
                            	</span>
                            </td>
                            <td ng-class="{danger: row.dangerPrice}" ng-bind="row.finalContractNo"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row page-margin-top">
                    <div class="col-md-12 col-lg-6" id="step5">
                        <span class="inline">每&nbsp;页</span>
                        <select
                                class="form-control inline" style="margin-top: 8px; width: 100px;"
                                disable-valid-styling="true" disable-invalid-styling="true"
                                ng-model="contract.page.pageSize"
                                ng-change="contract.pageSizeChange()"
                                ng-options="pageSizeOption for pageSizeOption in contract.page.pageSizeOptions">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;
                    	当前显示{{contract.page.data.startCount}} / {{contract.page.data.endCount}}条，共 {{contract.page.data.count}} 条</span>
                    </div>
                    <div class="col-md-12 col-lg-6" id="step4">
                        <paging class="pull-right" page="contract.page.data.pageNo"
                                page-size="contract.page.data.pageSize"
                                total="contract.page.data.count" adjacent="1" dots="..."
                                scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                active-class="active" disabled-class="disabled"
                                show-prev-next="true"
                                paging-action="contract.doPage(page, pageSize, total)"></paging>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <tab heading="下单统计" ng-click="contract.orderInquiry()" ng-if="contract.right.manage">
        <div class="panel panel-default">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="panel-heading font-blue-hoki">下单统计</div>
                    <div class="panel-body">
                        <form class="form-horizontal">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">统计时间：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <div class="input-prepend input-group">
                                            <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                            <input type="text" class="form-control" disable-auto-validate="true"
                                                   ng-blur="contract.initDate(contract.time)"
                                                   kyb-daterange
                                                   kyb-daterange-options="contract.rangeOptions"
                                                   ng-model="contract.time"
                                                   placeholder="请选择时间段">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <button class="btn btn-default btn-default-width pull-right"
                                            ng-click="contract.orderInquiry()">
                                        <i class="fa fa-search"></i> 查&nbsp;询
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="table-scrollable" style="margin-top:0px !important" id="step1">
                        <table id="contentTable"
                               class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading" id="step2">
                                <th width="15%">跟单员&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th width="15%">总款数&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th width="15%">单日款数</th>
                                <th>合同明细金额&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in contract.orderStatisticsList">
                                <td ng-bind="row.userName"></td>
                                <td ng-bind="row.num"></td>
                                <td>{{(row.singleDayPayment).toFixed(2)}}</td>
                                <td ng-bind="row.subTotal"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <%--<tab heading="客户下单统计" ng-click="contract.customerOrder()" ng-if="contract.right.manage">
        <div class="panel panel-default">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="panel-heading font-blue-hoki">客户下单统计</div>
                </div>
                <div class="portlet-body">
                    <div class="table-scrollable" style="margin-top:0px !important" id="step1">
                        <table id="contentTable"
                               class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading" id="step2">
                                <th>合同号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>通知单状态&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>下单时间</th>
                                <th>交货时间</th>
                                <th>客户订单号</th>
                                <th>生产编号</th>
                                <th>客户型号</th>
                                <th>订单类型</th>
                                <th>订单数量</th>
                                <th>覆铜板材</th>
                                <th>板材厚度</th>
                                <th>覆铜要求</th>
                                <th>订单平米数</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in contract.customerOrderList">
                                <td>{{row.contractNumber}}</td>
                                <td>{{row.notificationStatus}}</td>
                                <td>{{row.orderTime}}</td>
                                <td>{{row.deliveryTime}}</td>
                                <td>{{row.customerNo}}</td>
                                <td>{{row.craftNo}}</td>
                                <td>{{row.customerModel}}</td>
                                <td>{{row.orderType}}</td>
                                <td>{{row.orderQuantity}}</td>
                                <td>{{row.materialType}}</td>
                                <td>{{row.boardThicknessValue}}</td>
                                <td>{{row.copperCladThickness}}</td>
                                <td>{{row.orderDeailArea}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row page-margin-top">
                        <div class="col-md-12 col-lg-6">
                            <span class="inline">每页</span>
                            <select class="form-control inline table-pageSize-width"
                                    ng-model="contract.page.customerPageSize"
                                    ng-change="contract.customerOrder()"
                                    disable-auto-validate="true"
                                    ng-options="pageSizeOption for pageSizeOption in contract.page.pageSizeOptions">
                            </select>
                            <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示{{contract.page.customerData.startCount}} / {{contract.page.customerData.endCount}}条，共 {{contract.page.customerData.count}} 条</span>
                        </div>
                        <div class="col-md-12 col-lg-6">
                            <paging class="pull-right" page="contract.page.stockOutData.pageNo"
                                    page-size="contract.page.stockOutData.pageSize"
                                    total="contract.page.stockOutData.count" adjacent="1" dots="..."
                                    scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                    active-class="active" disabled-class="disabled" show-prev-next="true"
                                    paging-action="contract.dostockOutPage( page, pageSize, total)"> </paging>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </tab>--%>
    <!-- 添加或修改合同 -->
    <tab active="contract.tabs.editForm.active" ng-show="contract.tabs.editForm.show">
        <tab-heading>合同详情<i style="cursor: pointer" class="fa fa-times" ng-click="contract.hideEditForm()"></i></tab-heading>

        <div class="rows">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">合同{{contract.editTitle}}</div>
                </div>
                <!-- 页面主体 -->
                <div class="portlet-body" id="stepDetail1">
                    <form class="form-horizontal" id="contract_form" name="contract_form" novalidate="novalidate" ng-submit-force="true" ng-init="contract.setFormScope(this)">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span
                                            class="required">*</span>合同编号：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input required class="form-control" type="text" placeholder=""
                                               ng-model="contract.contract.no" readonly="readonly">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>订单日期：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <div class="dropdown">
                                            <a class="dropdown-toggle" id="dropdown2" role="button" data-toggle="dropdown" data-target="#" href="#">
                                                <div class="input-group">
                                                    <span type="text" class="form-control" data-ng-bind="contract.contract.orderDate | date:'yyyy-MM-dd HH:mm:ss'" ng-disabled="contract.contract.recordId != null && contract.contract.recordId != '' && contract.contract.status != '200201'"></span><span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
                                                </div>
                                            </a>
                                            <ul class="dropdown-menu" role="menu" aria-labelledby="dLabel" ng-hide="contract.contract.recordId != null
										    && contract.contract.recordId != '' && contract.contract.status != '200201'">
                                                <datetimepicker data-ng-model="contract.contract.orderDate"
                                                                data-datetimepicker-config="{ dropdownSelector: '#dropdown2' }"
                                                                data-on-set-time="contract.onTimeSet(newDate, oldDate)"
                                                />
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4" id="stepDetail6">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>客户：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select register-custom-form-control on-select="contract.checkSelectCustomer()" firstfocus="{{contract.focus.main}}"
                                                   ng-model="contract.contract.customer" theme="bootstrap" required
                                                   ng-disabled="contract.contract.recordId != null
                                                   && contract.contract.recordId != '' && contract.contract.status != '200201'"
                                        >
                                            <ui-select-match placeholder="">{{$select.selected.name}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.customers | filter: $select.search| limitTo: contract.infiniteScrollcus.currentItemscus track by $index"
                                                               infinite-scroll="contract.addMoreItemscus()"
                                                               infinite-scroll-distance="2">
                                                <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                <small><span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.shortName}}<span style="color: red;" ng-if="item.status ==60001">(审核中)</span><br></span>

                                                </small>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        <a ng-if="contract.customerPoFalg" ng-style="{color:'red'}">客户订单号：</a>
                                        <label ng-if="!contract.customerPoFalg">客户订单号：</label>
                                    </label>
                                    <div class="col-sm-7 col-md-7" ng-if="!contract.customerPoFalg">
                                        <input ng-model="contract.contract.customerPo"
                                               ng-blur="contract.queryCustomerPoNum(contract.contract.customerPo)"
                                               class="form-control" ng-maxlength="30" type="text" placeholder="请输入客户订单号"
                                               ng-disabled="contract.contract.recordId != null &&
                                        	contract.contract.recordId != '' && contract.contract.status != '200201'">
                                        <div ng-if="contract.customerPoMsg != '' && contract.customerPoMsg != null" class="">
                                            <label class="" style="color: #FF9800">{{contract.customerPoMsg}}</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>客户等级：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select register-custom-form-control
                                                   ng-model="contract.contract.customerGrade" theme="bootstrap" required
                                                   ng-disabled="contract.contract.recordId != null
                                                   && contract.contract.recordId != '' && contract.contract.status != '200201'">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.customerGradeList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        <a ng-if="contract.payWayFalg" ng-style="{color:'red'}">结算方式：</a>
                                        <label ng-if="!contract.payWayFalg"><span class="required">*</span>结算方式：</label>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select register-custom-form-control on-select="contract.updateContract()"
                                                   ng-model="contract.contract.payWay" theme="bootstrap" required
                                                   ng-disabled="contract.contract.recordId != null
                                                   && contract.contract.recordId != '' && contract.contract.status != '200201'">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.payWayList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        <a ng-if="contract.taxDescriptFalg" ng-style="{color:'red'}">
                                            <span class="required">*</span>税种说明：</a>
                                        <label ng-if="!contract.taxDescriptFalg"><span class="required">*</span>税种说明：</label>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select register-custom-form-control
                                                   ng-model="contract.contract.taxDescript" theme="bootstrap" on-select="contract.updateContract()"
                                                   on-select="contract.doInvoicePrice()" required
                                                   ng-disabled="contract.contract.recordId != null
                                                   && contract.contract.recordId != '' && contract.contract.status != '200201'">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.taxDescriptList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        <a ng-if="contract.currencyTypeFalg" ng-style="{color:'red'}">
                                            <span class="required">*</span>货币类型：</a>
                                        <label ng-if="!contract.currencyTypeFalg"><span class="required">*</span>货币类型：</label>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select register-custom-form-control on-select="contract.getExchangeRate()"
                                                   ng-model="contract.contract.currencyType" theme="bootstrap" required
                                                   ng-disabled="contract.contract.recordId != null
                                                   && contract.contract.recordId != '' && contract.contract.status != '200201'">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.currencyTypeList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contract.exchangeType">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">汇率规则：</label>
                                    <label class="col-sm-7 col-md-7">
                                        <span class="form-control font-weight-bolder">{{contract.contract.exchangeRate}}({{contract.contract.exchangeDateStr}})</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <%--<div class="row">

                        </div>--%>
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        <a ng-if="contract.deliveryPlaceFalg" ng-style="{color:'red'}">
                                            <span class="required">*</span>交货地：</a>
                                        <label ng-if="!contract.deliveryPlaceFalg"><span class="required">*</span>交货地：</label>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <input required class="form-control" type="text" placeholder="" ng-blur="contract.updateContract()"
                                               ng-model="contract.contract.deliveryPlace" ng-maxlength="50"
                                               ng-disabled="contract.contract.recordId != null
                                               && contract.contract.recordId != '' && contract.contract.status != '200201'">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        <a ng-if="contract.freightWayFalg" ng-style="{color:'red'}">
                                            <span class="required">*</span>交货方式：</a>
                                        <label ng-if="!contract.freightWayFalg"><span class="required">*</span >交货方式：</label>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select register-custom-form-control
                                                   ng-model="contract.contract.freightWay" theme="bootstrap" required on-select="contract.updateContract()"
                                                   ng-disabled="contract.contract.recordId != null
                                                   && contract.contract.recordId != '' && contract.contract.status != '200201'">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.freightwaysList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        <a ng-if="contract.deliveryWayFalg" ng-style="{color:'red'}">
                                            <span class="required">*</span>送货方式：</a>
                                        <label ng-if="!contract.deliveryWayFalg"><span class="required">*</span>送货方式：</label>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select register-custom-form-control
                                                   ng-model="contract.contract.deliveryWay" theme="bootstrap" on-select="contract.updateContract()"
                                                   required
                                                   ng-disabled="contract.contract.recordId != null
                                                   && contract.contract.recordId != '' && contract.contract.status != '200201'">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.deliverywaysList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>承接公司：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select register-custom-form-control on-select="contract.updateContract()"
                                                   ng-model="contract.contract.branch" theme="bootstrap" required
                                                   ng-disabled="contract.contract.recordId != null
                                                   && contract.contract.recordId != '' && contract.contract.status != '200201'">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.shortName}}
                                            </ui-select-match> <ui-select-choices repeat="item in contract.branchs | filter: $select.search">
                                            <div ng-bind-html="item.shortName | highlight: $select.search"></div>
                                        </ui-select-choices> </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>业务员：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select register-custom-form-control on-select="contract.updateContract()"
                                                   required
                                                   ng-model="contract.contract.user" theme="bootstrap"
                                                   ng-disabled="contract.contract.recordId != null
                                                   && contract.contract.recordId != '' && contract.contract.status != '200201'">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.userName}}
                                            </ui-select-match> <ui-select-choices repeat="item in contract.salesManList | filter: $select.search">
                                            <div ng-bind-html="item.userName | highlight: $select.search"></div>
                                        </ui-select-choices> </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>客户联系人：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select register-custom-form-control on-select="contract.updateContract()"
                                                   required
                                                   ng-model="contract.contract.customerContact" theme="bootstrap"
                                                   ng-disabled="contract.contract.recordId != null
                                                   && contract.contract.recordId != '' && contract.contract.status != '200201'">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.name}}
                                            </ui-select-match> <ui-select-choices repeat="item in contract.customerContactList | filter: $select.search">
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices> </ui-select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">联系电话：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-maxlength="20"
                                               ng-model="contract.contract.customerContact.phone" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>销售部门：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select  ng-model="contract.contract.deptId" required theme="bootstrap" ng-disabled="contract.contract.recordId != null
                                                   && contract.contract.recordId != '' && contract.contract.status != '200201'">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.deptList | filter: $select.search">
                                                <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                <div ng-bind-html="item.name | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <row class="row" >

                            <div  class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">客户地址：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input style="width: 126%;" class="form-control" type="text"
                                               ng-model="contract.contract.customerContact.address" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                        </row>
                        <div class="row" ng-if="contract.contract.status != '200201'">
                            <div class="col-md-6 col-lg-4" ng-if="contract.contract.processFee && contract.contract.processFee > 0">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">加工费(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contract.processFee" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contract.materialFee && contract.contract.materialFee > 0">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">材料费(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contract.materialFee" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                            <%--                            <div class="col-md-6 col-lg-4" ng-if="!(contract.contract.processFee && contract.contract.processFee > 0 && contract.contract.materialFee && contract.contract.materialFee > 0) && contract.contract.sumCostFee && contract.contract.sumCostFee > 0">
                                                            <div class="form-group">
                                                                <label class="col-sm-3 col-md-4 control-label">采购费(元)：</label>
                                                                <div class="col-sm-7 col-md-8">
                                                                    <input class="form-control" type="text" ng-model="contract.contract.sumCostFee" ng-disabled="true"/>
                                                                </div>
                                                            </div>
                                                        </div>--%>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contract.sumCostFee && contract.contract.sumCostFee > 0">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">成本(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contract.sumCostFee" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                            <%--                            <div class="col-md-6 col-lg-4" ng-if="contract.contract.grossProfitMargin">
                                                            <div class="form-group">
                                                                <label ng-if="0 > contract.contract.grossProfitMargin" class="col-sm-3 col-md-4 control-label"><span class="required">毛利润率(%)：</span></label>
                                                                <label ng-if="contract.contract.grossProfitMargin >= 0" class="col-sm-3 col-md-4 control-label">毛利润率(%)：</label>
                                                                <div class="col-sm-7 col-md-8">
                                                                    <input class="form-control" type="text" ng-model="contract.contract.grossProfitMargin" ng-disabled="true"></input>
                                                                </div>
                                                            </div>
                                                        </div>--%>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contract.saleFee && contract.contract.saleFee > 0">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">业务费(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contract.saleFee" ng-disabled="true"></input>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contract.manageFee && contract.contract.manageFee > 0">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">管理费(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contract.manageFee" ng-disabled="true"></input>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contract.netCostFee && contract.contract.netCostFee > 0">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">净成本(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contract.netCostFee" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contract.netGrossProfitMargin">
                                <div class="form-group">
                                    <label ng-if="0 > contract.contract.netGrossProfitMargin" class="col-sm-3 col-md-4 control-label"><span class="required">利润率(%)：</span></label>
                                    <label ng-if="contract.contract.netGrossProfitMargin >= 0" class="col-sm-3 col-md-4 control-label">利润率(%)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contract.netGrossProfitMargin" ng-disabled="true"></input>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>
                                    <div class="col-sm-7 col-md-10">
										<textarea  class="form-control"
                                                   placeholder="备注信息" ng-model="contract.contract.remark" ng-blur="contract.updateContract()"
                                                   ng-disabled="contract.contract.recordId != null
                                           && contract.contract.recordId != '' && contract.contract.status != '200201'"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="actions" style="float: right;">
                                <button type="button" class="btn green btn-default-width"
                                        ng-click="contract.addPreservation()"  ng-if="contract.contract.recordId != null && contract.contract.status == '200201' && contract.right.edit"><i class="fa fa-plus"></i> 保存
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- 合同明细 	-->
                    <div class="portlet light bordered" id="stepDetail3">
                        <div class="portlet-title">
                            <div class="caption font-blue-hoki">明细列表</div>
                            <div class="actions"
                                 ng-if="contract.contract.recordId == null || contract.contract.recordId == '' || contract.contract.status == '200201' && contract.right.edit">
                                <div class="portlet-input input-inline input-small" ng-if="!contract.contract.interCustomRecordId">
                                    <button class="btn green blue-hoki btn-default-width" ng-click="contract.importContractData()" ><i class="fa fa-upload"></i>合同明细导入</button>
                                </div>
                                <div class="portlet-input input-inline input-small">
                                    <button type="button" class="btn btn-success btn-default-width" id="stepDetail4" ng-if="!contract.contract.interCustomRecordId"
                                            ng-click="contract.addContractDetail()">
                                        <i class="fa fa-plus"></i> 添加合同明细
                                    </button>
                                </div>
                                <div class="portlet-input input-inline input-small">
                                    <button type="button" class="btn btn-success btn-default-width" id="stepDetail4"
                                            ng-click="contract.copyRemark()">
                                        <i class="fa"></i> 一键复制备注
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="portlet-body">
                            <table style="margin-top: 5px;" class="table table-striped table-bordered table-condensed table-advance table-hover">
                                <thead>
                                <tr class="heading">
                                    <th>客户型号</th>
                                    <th>规格(mm)</th>
                                    <th>板材厚度</th>
                                    <th>单&nbsp;价</th>
                                    <th>PCS成本价</th>
                                    <th>价格(元/㎡)</th>
                                    <th>平米</th>
                                    <th>数&nbsp;量</th>
                                    <th>金&nbsp;额</th>
                                    <th>客户预期</th>
                                    <th>生产编号</th>
                                    <th>状态</th>
                                    <th>逾期状态</th>
                                    <th>生产状态</th>
                                    <th>操&nbsp;作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="detail in contract.contract.contractDetails" ng-dblclick="contract.modContractDetail($index)">
                                    <td ng-if="!detail.hasNotification" bgcolor="yellow"><a href="javascript:void(0)" ng-bind="detail.contractCraftList.customerModel" ng-click="contract.modContractDetail($index)"></a></td>
                                    <td ng-if="detail.hasNotification"  ng-class="{'danger': contract.isDanger(detail)}"><a href="javascript:void(0)" ng-bind="detail.contractCraftList.customerModel" ng-click="contract.modContractDetail($index)"></a></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" ng-bind="detail.contractCraftList.pnlsize"></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" ng-bind="detail.contractCraftList.boardThickness | dictFilter: contract.refer.boardThicknessList"></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" class="text-right" ng-bind="detail.pricees.price | currency:'':6 "></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" class="text-right" ng-bind="detail.jxMadePrice | currency:'':6 "></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" class="text-right" ng-bind="detail.squareMeterPrice | currency:'':6 "></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" class="text-right" ng-bind="detail.orderDeailArea"></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" class="text-right" ng-bind="detail.quantity | number:0" ></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" class="text-right" ng-bind="detail.subTotal | currency:'':2 "></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" class="text-right" ng-bind="detail.deliveryDate | dateYMD"></td>
                                    <td>{{ detail.genCraftNo ? detail.genCraftNo : detail.craftNo }}</td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" ng-if="detail.status === '200205'"><span class="label label-sm label-warning">已作废 </span></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" ng-if="detail.status === '200204'"><span class="label label-sm label-primary">已送货 </span></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" ng-if="detail.status === '200210'"><span class="label label-sm label-primary">对账关闭</span></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" ng-if="detail.status === '200209'"><span class="label label-sm label-danger">审批中</span></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" ng-if="detail.status !== '200205' && detail.status !== '200204'&& detail.status !== '200210' && detail.status !== '200209'"><span class="label label-sm label-success">正在处理 </span></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" ng-bind="detail.showOverdueStatus"></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" ng-bind="detail.showProductionStatus"></td>
                                    <td ng-class="{'danger': contract.isDanger(detail)}" >
                                        <a class="btn btn-xs btn-default" href="javascript:void(0)"
                                           ng-click="contract.delContractDetail($index)" ng-if="contract.addContractFlag || contract.contract.icloudId == null && (contract.contract.slCustomerOrder == null || contract.contract.slCustomerOrder.recordId == null) && detail.status == '200201' && contract.contract.status != '200209' && contract.right.edit">
                                            <i class="fa fa-times font-red"></i> 删&nbsp;除</a>

                                        <a class="btn btn-xs btn-default" href="javascript:void(0)"
                                           ng-click="contract.openAddressForm($index)" >
                                            <i class="glyphicon glyphicon-road"></i> 收货地址</a>

                                        <a class="btn btn-xs btn-default"
                                           ng-if="detail.recordId && detail.status == '200201' && contract.contract.status == '200201' && contract.contract.status != '200211'
                                                &&( (detail.factoryName
                                                && detail.factoryName.indexOf('江西领德辉') >= 0)||(!detail.factoryName && detail.company.recordId == contract.ValiteCompanyUtil.factId))"
                                           style="margin-left: 15px;" ng-click="contract.routerGroupCenter(detail.recordId)">
                                            <i class="fa fa-save"></i> 一键集控
                                        </a>
                                        <a class="btn btn-xs btn-default"
                                           ng-if="detail.recordId && detail.status == '200209'
                                                &&( (detail.factoryName
                                                && detail.factoryName.indexOf('江西领德辉') >= 0)||(!detail.factoryName && detail.company.recordId == contract.ValiteCompanyUtil.factId))"
                                           style="margin-left: 15px;" ng-click="contract.routerGroupCenter(detail.recordId)">
                                            <i class="fa fa-save"></i> 查看一键集控
                                        </a>
                                        <a class="btn btn-xs btn-default"
                                           ng-if="detail.groupCenterId && detail.status == '200201' && contract.contract.status == '200201'"  && contract.contract.status != '200211'
                                           style="margin-left: 15px;" ng-click="contract.clearGroupCenter(detail)">
                                            <i class="fa fa-save"></i> 清除集控
                                        </a>
                                        <a class="btn btn-xs btn-default"
                                           ng-if="contract.right.edit && detail.status !== '200205'
                                                && (contract.ValiteCompanyUtil.valiteSaleCompany || ((detail.saleComId == contract.ValiteCompanyUtil.factId || !detail.saleComId) && contract.companyId == contract.ValiteCompanyUtil.factId))
                                                && detail.status != '200201' && detail.status != 200205 && detail.status != '200209' && detail.status != '200204' && detail.status !== '200211'"
                                           style="margin-left: 15px;" ng-click="contract.detailReflashAuditOpen(detail)">
                                            <i class="fa fa-save"></i> 反审
                                        </a>
                                        <a class="btn btn-xs btn-default"
                                           ng-if="detail.recordId == null || detail.recordId == ''"
                                           style="margin-left: 15px;" ng-click="contract.modContractDetailTwo($index)">
                                            <i class="fa fa-save"></i> 保存
                                        </a>
                                        <a class="btn btn-xs btn-default"
                                           ng-if="contract.right.edit && detail.status !== '200205'
                                                && (contract.ValiteCompanyUtil.valiteSaleCompany || (detail.company.recordId == detail.saleComId))
                                                && detail.status != '200201' && detail.status != 200205 && detail.status != '200209' && detail.status != '200204' && detail.valueOrderType && detail.valueOrderType == 1  && detail.groupCenterId"
                                           style="margin-left: 15px;" ng-click="contract.modSimpleQuantityOpen(detail)">
                                            <i class="fa fa-pencil-square-o"></i> 样品改量
                                        </a>
                                        <a class="btn btn-xs btn-default"
                                           ng-if="contract.right.edit && detail.status !== '200205'
                                                    && (contract.ValiteCompanyUtil.valiteSaleCompany || (detail.company.recordId == detail.saleComId))
                                                    && detail.status != '200209' && detail.status != '200204' && detail.maxBatchArea > contract.maxBatchArea && detail.oldMaxBatchArea != detail.maxBatchArea && !detail.maxBatchAreaFlag"
                                           style="margin-left: 15px;" ng-click="contract.applyMaxBatchAreaOpen(detail)">
                                            <i class="fa fa-pencil-square-o"></i> 申请
                                        </a>
                                        <a class="btn btn-xs btn-default"
                                           ng-if="contract.right.edit && detail.status !== '200205'
                                                        && (contract.ValiteCompanyUtil.valiteSaleCompany || (detail.company.recordId == detail.saleComId))
                                                        && detail.status == '200211'"
                                           style="margin-left: 15px;" ng-click="contract.recallDetail(detail)">
                                            <i class="fa fa-pencil-square-o"></i> 撤回
                                        </a>
                                        <a class="btn btn-xs btn-default" ng-if="contract.contract.no == contract.contract.finalContractNo || null == contract.contract.finalContractNo"
                                           style="margin-left: 15px;" ng-click="contract.deliveryWindow($index)">
                                            <i class="fa fa-pencil-square-o"></i> 分批交货
                                        </a>
                                        <form class="inline" ng-form-commit name="downJpForm" action="a/order/newContract/downSourceAddress" method="Post" target="hidden_frame">
                                            <input type="text" id="fileAddress" ng-show="false" name="fileAddress" ng-model="detail.fileAddress" />
                                            <a class="btn default btn-xs green-stripe" ng-if="detail.fileAddress" ng-click="contract.submitDown(downJpForm)">附件下载</a>
                                            <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                                        </form>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="panel-body" ng-if="contract.contract.recordId != null && contract.contract.recordId != '' && contract.contract.status != '200205' && contract.contract.status != '200209'">
                            <div align="center" class="row">
                                <button class="btn green" ngf-select="contract.selectUploadOrderFile($file, $invalidFile)"
                                        ngf-max-size="10MB" >
                                    <span class="glyphicon glyphicon-cloud-upload"></span> 上传文件
                                </button>
                                <h6>仅支持.jpg,.png,.jpeg,.pdf文件</h6>
                            </div>
                            <br/>
                            <div class="row" class="form-group" style="margin-bottom: 5px;" ng-if="contract.contract.attachList != null && contract.contract.attachList != ''">
                                <!-- 显示附件 -->
                                <div class="col-md-6 col-lg-4" ng-repeat="item in contract.contract.attachList">
                                    <div class="form-group">
                                        <div class="col-sm-8 col-md-8 control-label">
                                            <label>附件{{$index + 1}}：</label>
                                            <a ng-click="contract.preview(item)">{{item.orgFileName}} 预览</a>
                                            <a ng-click="contract.deleteOrderFile(item.recordId)" ng-if="contract.right.edit">移除</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group pt-3">
                            <div class="pull-right">
                                <button class="btn btn-primary btn-default-width"
                                        ng-if="contract.contract.recordId != null && contract.contract.recordId != '' && contract.contract.status == '200201' && contract.ValiteCompanyUtil.valiteSaleCompany"
                                        ng-click="contract.commitAuditOpen()">
                                    <i class="fa fa-save"></i> 提交审批
                                </button>
                                <button class="btn btn-primary btn-default-width"
                                        style="margin-right: 25px;"
                                        ng-if="contract.contract.recordId != null && contract.contract.recordId != '' && contract.contract.status != '200205' && contract.contract.status != '200209' && contract.ValiteCompanyUtil.valiteSaleCompany" ng-click="contract.reflshContract()">
                                    <i class="fa fa-save"></i> 新&nbsp;增
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </tab>

    <!-- 合同明细查看详情(添加) -->
    <tab active="contract.tabs.addDetailForm.active" ng-show="contract.tabs.addDetailForm.show">
        <tab-heading>合同明细详情<i style="cursor: pointer" class="fa fa-times" ng-click="contract.hideAddDetailForm()"></i></tab-heading>
        <div class="rows">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">合同明细</div>
                </div>
                <div class="modal-body" id="stepContractDetail1">
                    <div class="alert alert-warning" ng-if="contract.complaint.hadComplaint">
                        <strong>您好，您当前选择的客户型号或生产编号存在客诉!</strong><br/>
                        客诉原因：<span ng-bind-html="contract.complaint.info"></span>
                    </div>

                    <form class="form-horizontal" id="addcraft_form" name="addcraft_form"
                          novalidate="novalidate" ng-submit-force="true" ng-init="contract.setDetailFormScope(this)">

                        <!-- 从报价单导入数据 -->
                        <div class="panel-body" style="border: 1px solid #cccccc;"
                             ng-if="contract.contractDetail.recordId == null || contract.contractDetail.recordId == ''">
                            <div class="row">
                                <div class="col-md-6 col-lg-4" id="stepContractDetail2">
                                    <div class="form-group">
                                        <label class="col-sm-3 col-md-4 control-label"></label>
                                        <div class="col-sm-7 col-md-7" >
                                            <input type="radio" name = "radio" value="0" ng-click="contract.quotationRadioClick()" ng-checked="contract.checkQuo"/><label class="font-red" type="radio" ng-click="contract.quotationRadioClick()" >从报价单导入数据</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="col-sm-3 col-md-4 control-label">报价单编号：</label>
                                        <div class="col-sm-7 col-md-7">
                                            <ui-select theme="bootstrap"  firstfocus="{{contract.focus.detail}}"  ng-disabled="contract.quotationDisabled"
                                                       on-select="contract.selectQuotationForDetail($select.selected.recordId)"
                                                       ng-model="contract.contract.quotation" on-select="contract.selectQuotationForMain($select.selected.recordId)">
                                                <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
                                                <ui-select-choices refresh="contract.seachQuo($select.search)" refresh-delay="1000"
                                                                   repeat="item.recordId as item in contract.quotationsCopy"
                                                                   infinite-scroll='contract.addMoreItemsOfquoes()' infinite-scroll-distance='3'>
                                                    <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                    <small>
                                                        <span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.customer.shortName}}<br></span>
                                                    </small>
                                                </ui-select-choices>
                                            </ui-select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="col-sm-3 col-md-4 control-label">客户型号：</label>
                                        <div class="col-sm-7 col-md-7">
                                            <ui-select theme="bootstrap" ng-disabled="contract.customerDisabled"
                                                       on-select="contract.selectQuotationDetail($select.selected.recordId)"
                                                       ng-model="contract.customerModelSelected">
                                                <ui-select-match placeholder="请选择">{{$select.selected.customerModel}}</ui-select-match>
                                                <ui-select-choices repeat="item in contract.quoationDetails | filter: $select.search">
                                                    <div ng-bind-html="item.customerModel | highlight: $select.search"></div>
                                                </ui-select-choices>
                                            </ui-select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 从生产编号导入数据 -->
                            <div class="row" style="margin-top: 5px;">
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="col-sm-3 col-md-4 control-label"></label>
                                        <div class="col-sm-7 col-md-7">
                                            <input type="radio" name = "radio" value="1"  ng-click="contract.craftRadioClick()" ng-checked="contract.checkPro"/><label class="font-red" type="radio"  ng-click="contract.craftRadioClick()">从生产编号导入数据</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-4">
                                    <div class="form-group">
                                        <label class="col-sm-3 col-md-4 control-label">生产编号：</label>
                                        <div class="col-sm-7 col-md-7">
                                            <ui-select theme="bootstrap" ng-disabled="!contract.CraftNoDisabled"
                                                       ng-model="contract.craftNoSelected" on-select="contract.selectNotification($select.selected)">
                                                <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
                                                <ui-select-choices refresh="contract.seachCraft($select.search)" refresh-delay="1000"
                                                                   repeat="item.recordId as item in contract.notifications | filter: $select.search"
                                                                   infinite-scroll='contract.addMoreItems()' infinite-scroll-distance='3'>
                                                    <small>
                                                        <span style="color: blue;">生产编号：&nbsp;&nbsp;&nbsp;&nbsp;{{item.no}}<br></span>
                                                        <span style="color: red;">客户型号：&nbsp;&nbsp;&nbsp;&nbsp;{{item.customerModel}}<br></span>
                                                    </small>
                                                </ui-select-choices>
                                            </ui-select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="margin-top: 5px;"  id="stepContractDetail3">
                            <div class="col-md-12 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 col-lg-4 control-label" ng-if="!contract.pcsSizeFlag"><span class="required">*</span>PCS尺寸(mm)：</label>
                                    <label class="col-sm-3 col-md-2 col-lg-4 control-label" ng-if="contract.pcsSizeFlag" ng-click="contract.getCraftRecordTwo(contract.contractDetail.contractCraftList.unitLength +'*'+ contract.contractDetail.contractCraftList.unitWidth,contract.contractDetail.craftClone.unitLength +'*'+ contract.contractDetail.craftClone.unitWidth,'PCS尺寸(mm)')">
                                        <span class="required">PCS尺寸(mm)：</span></label>
                                    <div class="col-sm-7 col-md-7">
                                        <div class="row">
                                            <div class="col-sm-1-custom-first">
                                                <label class="control-label">长</label>
                                            </div>
                                            <div class="col-sm-5-custom-unit">
                                                <input  class="form-control" type="text"
                                                        name="unitLength" ng-blur="contract.doAreaPrice()"
                                                        ng-model="contract.craft.unitLength" required
                                                        ng-Pcslengthwidth ng-maxlength="12"
                                                        ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                                />
                                            </div>
                                            <div class="col-sm-1-custom-second">
                                                <label class="control-label">宽</label>
                                            </div>
                                            <div class="col-sm-5-custom-unit">
                                                <input  class="form-control" type="text"
                                                        name="unitWidth" ng-blur="contract.doAreaPrice()"
                                                        ng-model="contract.craft.unitWidth" required
                                                        ng-Pcslengthwidth ng-maxlength="12"
                                                        ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 col-lg-4 control-label" ng-if="!contract.pnlSizeFlag"><span class="required">*</span>交货尺寸(mm)：</label>
                                    <label class="col-sm-3 col-md-2 col-lg-4 control-label" ng-if="contract.pnlSizeFlag" ng-click="contract.getCraftRecordTwo(contract.contractDetail.contractCraftList.pnlLength +'*'+ contract.contractDetail.contractCraftList.pnlWidth +'/'+ contract.contractDetail.contractCraftList.pnlDivisor,contract.contractDetail.craftClone.pnlLength +'*'+ contract.contractDetail.craftClone.pnlWidth +'/'+ contract.contractDetail.craftClone.pnlDivisor,'交货尺寸(mm)')">
                                        <span class="required">交货尺寸(mm)：</span></label>
                                    <div class="col-sm-7 col-md-7 ">
                                        <div class="row">
                                            <div class="col-sm-1-custom-first">
                                                <label class="control-label">长</label>
                                            </div>
                                            <div class="col-sm-3" style="padding-left: 0px !important">
                                                <input  class="form-control" type="text" ng-blur="contract.doAreaPrice()"
                                                        ng-model="contract.craft.pnlLength"
                                                        ng-Pcslengthwidth
                                                        ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                                />
                                            </div>
                                            <div class="col-sm-1-custom">
                                                <label class="control-label">宽</label>
                                            </div>
                                            <div class="col-sm-3" style="padding-left: 0px !important">
                                                <input  class="form-control" type="text" ng-blur="contract.doAreaPrice()"
                                                        ng-model="contract.craft.pnlWidth"
                                                        ng-Pcslengthwidth ng-maxlength="12"
                                                        ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                                />
                                            </div>
                                            <div class="col-sm-1-custom">
                                                <label class="control-label">/</label>
                                            </div>
                                            <div class="col-sm-3" style="padding-left: 0px !important">
                                                <input  class="form-control" type="text" ng-blur="contract.doAreaPrice(1)"
                                                        ng-model="contract.craft.pnlDivisor"
                                                        ng-onlynumber
                                                        ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                                />
                                            </div>
                                            <div class="col-sm-1-custom">
                                                <label class="control-label">PCS</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>客户型号：</label>
                                    <div class="col-sm-7 col-md-10" style="width:79%;">
                                        <input required class="form-control" type="text" placeholder=""
                                               ng-model="contract.craft.customerModel" ng-maxlength="200"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                        >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.craft.cardSet">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">型号工程卡尺寸(mm)：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-model="contract.craft.cardSet" disabled="disabled" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.craft.cardSet">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">实际工程卡尺寸：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <span class="form-control" ng-if="contract.contractDetail.cardLength && contract.contractDetail.cardWidth && contract.contractDetail.cardPnlDivisor">{{contract.contractDetail.cardLength}}*{{contract.contractDetail.cardWidth}}/{{contract.contractDetail.cardPnlDivisor}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        <span class="required">*</span>PCS数量：
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <input required class="form-control" type="text"
                                               placeholder="请输入数量" ng-blur="contract.doAreaPrice(1)"
                                               ng-model="contract.contractDetail.quantity"
                                               style="text-align: right;" ng-onlynumberandzero
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        订单单位：
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <select class="form-control" ng-model="contract.contractDetail.unitType" ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' || (contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <option value="">PCS</option>
                                            <option value="1">SET</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        SET数量：
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.setQuantity" ng-blur="contract.doAreaPrice(2)" style="text-align: right;"
                                               ng-onlynumberandzero ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"/>
                                    </div>
                                </div>
                            </div>

                            <%--                            <div class="col-md-6 col-lg-4" ng-if="!(contract.contractDetail.recordId --%>
                            <%--                                        				&& contract.contract.status != '200201')">--%>
                            <%--                                <div class="form-group">--%>
                            <%--                                    <label class="col-sm-3 col-md-4 control-label">备品比例(%)：</label>--%>
                            <%--                                    <div class="col-sm-7 col-md-7">--%>
                            <%--                                        <ui-select ng-model="contract.contractDetail.spareRate" on-select="contract.doSpareRate()"--%>
                            <%--                                         theme="bootstrap" ng-disabled="contract.contractDetail.recordId --%>
                            <%--                                        				&& contract.contract.status != '200201'"> --%>
                            <%--                                            <ui-select-match placeholder="请选择..." >{{$select.selected.value}}</ui-select-match> --%>
                            <%--	                                        <ui-select-choices repeat="item.value as item in contract.spareRateList | filter: $select.search">--%>
                            <%--	                                            <div ng-bind-html="item.value | highlight: $select.search"></div>--%>
                            <%--	                                        </ui-select-choices> --%>
                            <%--                                        </ui-select>--%>
                            <%--                                    </div>--%>
                            <%--                                </div>--%>
                            <%--                            </div>--%>
                            <%--                            <div class="col-md-6 col-lg-4">--%>
                            <%--                                <div class="form-group">--%>
                            <%--                                    <label class="col-sm-3 col-md-4 control-label">--%>
                            <%--                                        备品数量：--%>
                            <%--                                    </label>--%>
                            <%--                                    <div class="col-sm-7 col-md-7">--%>
                            <%--                                        <input  class="form-control" type="text"--%>
                            <%--                                                placeholder="请输入备品数量" ng-blur="contract.changeSpareQuantity()"--%>
                            <%--                                                ng-model="contract.contractDetail.spareQuantity"--%>
                            <%--                                                style="text-align: right;" ng-onlynumberandzero--%>
                            <%--                                                ng-disabled="contract.contractDetail.recordId != null--%>
                            <%--                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||--%>
                            <%--                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != '') ||--%>
                            <%--                                        				!contract.contract.sparestatus)"--%>
                            <%--                                        />--%>
                            <%--                                    </div>--%>
                            <%--                                </div>--%>
                            <%--                            </div>--%>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>产品类型：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select theme="bootstrap" ng-model="contract.craft.productTypeId" ng-disabled="!contract.right.edit" required>
                                            <ui-select-match style="overflow: hidden;" placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.productTypeList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">客户物料号：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="请输入客户物料号"
                                               ng-model="contract.contractDetail.customerMaterialNo" ng-blur="contract.saveContractDetailData(1)"
                                               ng-maxlength="100"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && contract.contract.status != '200207' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">批号：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="请输入批号"
                                               ng-model="contract.contractDetail.batchNumber"
                                               ng-maxlength="30"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>
                                        <span ng-if="!contract.boardLevelFlag">PCB类型：</span>
                                        <a ng-if="contract.boardLevelFlag" ng-click="contract.getCraftRecord(contract.contractDetail.contractCraftList.boardLevel,contract.contractDetail.craftClone.boardLevel,'PCB类型')"><span class="required">PCB类型：</span></a>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <input  type = "hidden"  ng-model="contract.craft.boardLevel" required/>
                                        <ui-select ng-model="contract.craft.boardLevel" on-select="contract.doSetPrice()"
                                                   theme="bootstrap" ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择..." >{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.boardLevelList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>
                                        <span ng-if="!contract.materialTypeFlag">覆铜板材：</span>
                                        <a ng-if="contract.materialTypeFlag" ng-click="contract.getCraftRecord(contract.contractDetail.contractCraftList.materialType,contract.contractDetail.craftClone.materialType,'覆铜板材')"><span class="required">覆铜板材：</span></a>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select required ng-model="contract.craft.materialType" on-select="contract.doSetPrice($select.selected)"
                                                   theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.materialTypeList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.plateType == 2">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>
                                        <span>板材背面颜色：</span>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select required ng-model="contract.craft.backColor" on-select="contract.doSetPrice()"
                                                   theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.backColorList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>
                                        <span ng-if="!contract.boardThicknessFlag">板材厚度：</span>
                                        <a ng-if="contract.boardThicknessFlag" ng-click="contract.getCraftRecord(contract.contractDetail.contractCraftList.boardThickness,contract.contractDetail.craftClone.boardThickness,'板材厚度')"><span class="required">板材厚度：</span></a>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select required ng-model="contract.craft.boardThickness" on-select="contract.doSetPrice()"
                                                   theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}
                                            </ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.boardThicknessList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>
                                        <span ng-if="!contract.copperCladThicknessFlag">覆铜要求：</span>
                                        <a ng-if="contract.copperCladThicknessFlag" ng-click="contract.getCraftRecord(contract.contractDetail.contractCraftList.copperCladThickness,contract.contractDetail.craftClone.copperCladThickness,'覆铜要求')"><span class="required">覆铜要求：</span></a>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select required ng-model="contract.craft.copperCladThickness" on-select="contract.doSetPrice()"
                                                   theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.copperCladThicknessList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>
                                        <span ng-if="!contract.surfaceProcessFlag">镀层处理：</span>
                                        <a ng-if="contract.surfaceProcessFlag" ng-click="contract.getCraftRecord(contract.contractDetail.contractCraftList.surfaceProcess,contract.contractDetail.craftClone.surfaceProcess,'镀层处理')"><span class="required">镀层处理：</span></a>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select required ng-model="contract.craft.surfaceProcess" on-select="contract.doSetPrice()"
                                                   theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contract.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.surfaceProcessList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>
                                        <span ng-if="!contract.solderMaskTypeFlag">阻焊类型：</span>
                                        <a ng-if="contract.solderMaskTypeFlag" ng-click="contract.getCraftRecord(contract.contractDetail.contractCraftList.solderMaskType,contract.contractDetail.craftClone.solderMaskType,'阻焊类型')"><span class="required">阻焊类型：</span></a>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select required ng-model="contract.craft.solderMaskType" on-select="contract.doSetPrice()"
                                                   theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contract.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}
                                            </ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.solderMaskTypeList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>
                                        <span ng-if="!contract.characterTypeFlag">板面字符：</span>
                                        <a ng-if="contract.characterTypeFlag" ng-click="contract.getCraftRecord(contract.contractDetail.contractCraftList.characterType,contract.contractDetail.craftClone.characterType,'板面字符')"><span class="required">板面字符：</span></a>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select required ng-model="contract.craft.characterType" on-select="contract.doSetPrice()"
                                                   theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contract.groupCenterId != ''))">
                                            <ui-select-match
                                                    placeholder="请选择...">{{$select.selected.value}}
                                            </ui-select-match> <ui-select-choices
                                                repeat="item.recordId as item in contract.refer.characterTypeList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices> </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>
                                        <span ng-if="!contract.shapingWayFlag">成型方式：</span>
                                        <a ng-if="contract.shapingWayFlag" ng-click="contract.getCraftRecord(contract.contractDetail.contractCraftList.shapingWay,contract.contractDetail.craftClone.shapingWay,'成型方式')"><span class="required">成型方式：</span></a>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select required ng-model="contract.craft.shapingWay" on-select="contract.doSetPrice()"
                                                   theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contract.groupCenterId != ''))">
                                            <ui-select-match
                                                    placeholder="请选择...">{{$select.selected.value}}
                                            </ui-select-match> <ui-select-choices
                                                repeat="item.recordId as item in contract.refer.shapingWayList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices> </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>
                                        <span ng-if="!contract.testMethodFlag">测试要求：</span>
                                        <a ng-if="contract.testMethodFlag" ng-click="contract.getCraftRecord(contract.contractDetail.contractCraftList.testMethod,contract.contractDetail.craftClone.testMethod,'测试要求')"><span class="required">测试要求：</span></a>
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select required ng-model="contract.craft.testMethod" on-select="contract.doSetPrice()"
                                                   theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"> <ui-select-match
                                                placeholder="请选择...">{{$select.selected.value}}
                                        </ui-select-match> <ui-select-choices
                                                repeat="item.recordId as item in contract.refer.testMethodList | filter: $select.search">
                                            <div ng-bind-html="item.value | highlight: $select.search"></div>
                                        </ui-select-choices> </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">导热：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select ng-model="contract.craft.daore" on-select="contract.doSetPrice()" theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">
                                                {{$select.selected.value}}
                                            </ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.daoreList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">耐压：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select ng-model="contract.craft.naiya" on-select="contract.doSetPrice()" theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">
                                                {{$select.selected.value}}
                                            </ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.naiyaList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>曝光/丝印：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select required ng-model="contract.contractDetail.processValueId" on-select="contract.selectProcessValue()" theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.processValueList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.processValueId">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>制作方式：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select required ng-model="contract.contractDetail.processValueNextId" theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.nextShowList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">最小线宽/线距：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select ng-model="contract.craft.lingeSpacing" theme="bootstrap"
                                                   on-select="contract.doSetPrice()"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.lingeSpacingList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">最小孔径：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select ng-model="contract.craft.smallAperture" theme="bootstrap"
                                                   on-select="contract.doSetPrice()"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.smallApertureList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">半孔：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select ng-model="contract.craft.halAhole" theme="bootstrap"
                                                   on-select="contract.doSetPrice()"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.halAholeList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">埋盲孔：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select ng-model="contract.craft.buryBlindHole" theme="bootstrap"
                                                   on-select="contract.doSetPrice()"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.buryBlindHoleList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">抗阻：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select ng-model="contract.craft.resistance" theme="bootstrap"
                                                   on-select="contract.doSetPrice()"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.resistanceList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.craft.materialTypeValue && contract.craft.materialTypeValue.toLowerCase().indexOf('铝基') !== -1">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>绝缘层厚度：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="" required
                                               ng-model="contract.craft.insulationThickness" ng-maxlength="50"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">通孔个数（PCS）：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input ng-model="contract.craft.throughHole" ng-blur="contract.throughHoleSquareMeter()" class="form-control" type="text" placeholder=""
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">通孔个数（平米）：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input ng-model="contract.craft.throughHole2" class="form-control" type="text" placeholder="" disabled="disabled">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">沉头孔个数：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="" ng-model="contract.craft.countersinkHole"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">出货地点：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="" ng-model="contract.craft.shippingAddress"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">油墨型号：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select ng-model="contract.craft.inkType" theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.inkTypeList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>包装要求：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select multiple ng-model="contract.craft.packingRequirements" theme="bootstrap"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))" required>
                                            <ui-select-match placeholder="请选择...">{{$item.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.packingRequirementList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">拼板规格：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder=""
                                               ng-model="contract.craft.deliverySize" ng-maxlength="15"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">特殊工艺：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select theme="bootstrap" multiple
                                                   ng-model="contract.craft.specialCraftList" sortable="true" close-on-select="false"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match  placeholder="选择工艺...">{{$item.value}}</ui-select-match>
                                            <ui-select-choices
                                                    repeat="item.recordId as item in contract.refer.specialTypeList | filter: $select.search"
                                                    infinite-scroll-distance="3">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="!(contract.contractDetail.recordId
                                        				&& contract.contract.status != '200201')">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">备品设置：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <span class="form-control">{{contract.contract.spareRate}}
                                            <span ng-if="contract.contract.sparestatus">&emsp;可调整</span>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        备品数量：
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <input  class="form-control" type="text"
                                                placeholder="请输入备品数量" ng-blur="contract.changeSpareQuantity()"
                                                ng-model="contract.contractDetail.spareQuantity"
                                                style="text-align: right;" ng-onlynumberandzero
                                                ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != '') ||
                                        				!contract.contract.sparestatus)"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">钻孔个数：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="" ng-model="contract.craft.drillinHole"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">最大批次面积㎡：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder=""
                                               ng-model="contract.contractDetail.maxBatchArea" ng-maxlength="15" ng-onlynumberandzero
                                               ng-disabled="contract.contractDetail.recordId != null
                                                            && contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                                            (contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">新开测试架：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <checkbox ng-model="contract.contractDetail.newTestRack" value="1" name="test"></checkbox>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">新开模具：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <checkbox ng-model="contract.contractDetail.makeNewMold" value="1" name="test"></checkbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>
                                    <div class="col-sm-7 col-md-10">
								<textarea style="width: 95%;" class="form-control" rows="5" cols="2"
                                          id="maxlength_remark" placeholder="备注信息" ng-blur="contract.saveContractDetailData(2)"
                                          ng-model="contract.contractDetail.remark"
                                          ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row" style="margin-top: 5px;">
                            <div class="col-md-7 col-lg-4">
                                <div class="col-sm-5" ng-if="contract.jxDeferencePrice != null && contract.jxDeferencePrice!=''" ng-show="tooltipVisible" style="position: absolute; background-color: white; border: 1px solid black; border-radius: 5px !important; height: 23px; width: 35.8%">
                                    <span ng-if="contract.jxDeferencePrice > 0">PCS单价增加:{{contract.jxDeferencePrice}}</span>
                                    <span ng-if="contract.jxDeferencePrice < 0">PCS单价减少：{{contract.jxDeferencePrice * -1}}</span>
                                </div>
                                <br/>
                                <br/>
                                <div class="form-group">
                                    <label class="col-sm-4"><span class="required">*</span>
                                    <span ng-style="{'color': contract.jxDeferencePrice >0 || contract.jxDeferencePrice < 0  ? 'red' : ''}"
                                          ng-mouseover="tooltipVisible=true"
                                          ng-mouseleave="tooltipVisible=false">PCS单价 ({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</span>
                                    </label>
                                    <span style="color: red;" ng-if="contract.price.oldPrice && contract.price.oldPrice>0">上次返单单价{{contract.price.oldPrice}}</span>
                                    <span ng-if="contract.contract.exchangeType && contract.price.price">
                                        参考RMB:{{(contract.price.price * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input  class="form-control" type="text" placeholder="" required ng-blur="contract.doTotalPcsPrice()"
                                                ng-model="contract.price.price"
                                                ng-disabled="contract.startNum == '1' || contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>
                            <br/>
                            <br/>
                            <div class="col-md-5 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">SET单价({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.setPrice">
                                        参考RMB:{{(contract.setPrice * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="" ng-model="contract.setPrice" ng-blur="contract.setAndPcsChangePrice(2)"
                                               ng-disabled="contract.startNum == '1' || contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="margin-top: 5px;">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">	平米单价({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span style="color: green;" ng-if="contract.price.oldSquareMeterPrice && contract.price.oldSquareMeterPrice>0">上次返单平米单价{{contract.price.oldSquareMeterPrice}}</span>
                                    <span ng-if="contract.contract.exchangeType && contract.contractDetail.squareMeterPrice">
                                        参考RMB:{{(contract.contractDetail.squareMeterPrice * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input  class="form-control" type="text" placeholder=""
                                                ng-model="contract.contractDetail.squareMeterPrice" ng-blur="contract.getSqurePriceByCraft()"
                                                ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="col-sm-4" ng-show="tooltipVisibleTwo" ng-if="contract.jxDeferenceMoney != null && contract.jxDeferenceMoney!=''" style="position: absolute; background-color: white; border: 1px solid black; border-radius: 5px !important;height: 23px;text-align: center;">
                                    <span ng-if="contract.jxDeferenceMoney > 0" style="text-align: center;">总金额增加:{{contract.jxDeferenceMoney}}</span>
                                    <span ng-if="contract.jxDeferenceMoney < 0" style="text-align: center;">总金额减少:{{contract.jxDeferenceMoney *-1}}</span>
                                </div>
                                <br/>
                                <br/>
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        <span ng-style="{'color': contract.jxDeferenceMoney > 0 || contract.jxDeferenceMoney < 0  ? 'red' : ''}"
                                              ng-mouseover="tooltipVisibleTwo=true"
                                              ng-mouseleave="tooltipVisibleTwo=false">总金额({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：
                                        </span>
                                        </label>

                                    <span ng-if="contract.contract.exchangeType && contract.contractDetail.subTotal">
                                        参考RMB:{{(contract.contractDetail.subTotal * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.subTotal" disabled />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">总平米：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.orderDeailArea" disabled />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="margin-top: 5px;">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">客户文件名：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder=""
                                               ng-model="contract.contractDetail.customerFileName" ng-maxlength="30"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">模具费用({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.price.mouldFee">
                                        参考RMB:{{(contract.price.mouldFee * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="" ng-blur="contract.doOthersPrice()"
                                               ng-model="contract.price.mouldFee" ng-onlydecimals
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != ''  && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">测试架费({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.price.testShelfFee">
                                        参考RMB:{{(contract.price.testShelfFee * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="" ng-blur="contract.doOthersPrice()"
                                               ng-model="contract.price.testShelfFee" ng-onlydecimals
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != ''  && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">工程费用({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.price.engineeringFee">
                                        参考RMB:{{(contract.price.engineeringFee * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-blur="contract.doOthersPrice()"
                                               ng-model="contract.price.engineeringFee"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != ''  && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">菲林费用({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.price.filmFee">
                                        参考RMB:{{(contract.price.filmFee * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="" ng-blur="contract.doOthersPrice()"
                                               ng-model="contract.price.filmFee" ng-onlydecimals
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != ''  && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">其它费用({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.price.othersFee">
                                        参考RMB:{{(contract.price.othersFee * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="" ng-blur="contract.doOthersPrice()"
                                               ng-model="contract.price.othersFee" ng-onlydecimals
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != ''  && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">工程返还费({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.price.engineeringLimit">
                                        参考RMB:{{(contract.price.engineeringLimit * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="" ng-blur="contract.doOthersPrice()"
                                               ng-model="contract.price.engineeringLimit" ng-onlydecimals
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != ''  && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">模具费返还费({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.price.mouldLimit">
                                        参考RMB:{{(contract.price.mouldLimit * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="" ng-blur="contract.doOthersPrice()"
                                               ng-model="contract.price.mouldLimit" ng-onlydecimals
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != ''  && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">测试架费返还费({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.price.testShelfLimit">
                                        参考RMB:{{(contract.price.testShelfLimit * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder="" ng-blur="contract.doOthersPrice()"
                                               ng-model="contract.price.testShelfLimit" ng-onlydecimals
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="margin-bottom: 5px;">
                            <div class="col-md-6 col-lg-4" ng-show="true" >
                                <div class="form-group" >
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>按照：</label>
                                    <div class="col-sm-7 col-md-7" style="width: 50%;">
                                        <ui-select theme="bootstrap" required ng-model="contract.contractDetail.referenceType" on-select="contract.loadCraft()" ng-disabled="contract.contractDetail.recordId != null
                           				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                           				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.productionTypeList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                    <div style="padding-top: 10px;">生产</div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>交货日期：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input ng-model="contract.contractDetail.deliveryDate"
                                               placeholder="请选择本批次交货日期" type="text" class="form-control"
                                               data-date-format="yyyy-MM-dd" required
                                               data-date-type="number" data-min-date="{{contract.contract.orderDate}}"
                                               data-max-date="30.12.2099" data-autoclose="1"
                                               daysOfWeekDisabled="false" bs-datepicker
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-show="true" ng-if="contract.refer.econnmyStatus">
                                <div class="form-group" >
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>总部经济：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select theme="bootstrap" required ng-model="contract.contractDetail.econnmyId" on-select="contract.loadCraft()" ng-disabled="contract.contractDetail.recordId != null
                           				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                           				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.econnmyName}}</ui-select-match>
                                            <ui-select-choices repeat="item.econnmyId as item in contract.refer.econnmyList | filter: $select.search">
                                                <div ng-bind-html="item.econnmyName | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.companyId != contract.ValiteCompanyUtil.factId">
                                <div class="form-group" ng-if="!contract.contractDetail.groupCenterId || (contract.contractDetail.groupCenterId && contract.companyId == contract.contractDetail.saleComId)">
                                    <label class="col-sm-3 col-md-4 control-label">生产工厂：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select ng-model="contract.contractDetail.madeSupplierId"
                                                   theme="bootstrap" on-select="contract.getFactory($select.selected)"
                                                   ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match
                                                    placeholder="请选择...">{{$select.selected.name}}
                                            </ui-select-match> <ui-select-choices
                                                repeat="item.recordId as item in contract.refer.supplierList | filter: $select.search">
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices> </ui-select>
                                    </div>
                                </div>
                                <div class="form-group" ng-if="contract.contractDetail.groupCenterId && contract.companyId != contract.contractDetail.saleComId">
                                    <label class="col-sm-3 col-md-4 control-label">生产工厂：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.factoryName" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4" ng-if="contract.companyId==contract.ValiteCompanyUtil.factId">
                                <div class="form-group" ng-if="!contract.contractDetail.groupCenterId || (contract.contractDetail.groupCenterId && contract.companyId == contract.contractDetail.saleComId)">
                                    <label class="col-sm-3 col-md-4 control-label">生产工厂：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select  ng-model="contract.contractDetail.madeSupplierId"
                                                    theme="bootstrap" on-select="contract.getFactory($select.selected)"
                                                    ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match
                                                    placeholder="请选择...">{{$select.selected.name}}
                                            </ui-select-match> <ui-select-choices
                                                repeat="item.recordId as item in contract.refer.supplierList | filter: $select.search">
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices> </ui-select>
                                    </div>
                                </div>
                                <div class="form-group" ng-if="contract.contractDetail.groupCenterId && contract.companyId != contract.contractDetail.saleComId">
                                    <label class="col-sm-3 col-md-4 control-label">生产工厂：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.factoryName" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4" ng-if="!contract.factoryFlag && contract.contractDetail.madeSupplierId != null
                            	&& contract.contractDetail.madeSupplierId != ''">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">
                                        <span class="required">*</span>采购税种说明：
                                    </label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select register-custom-form-control ng-model="contract.contractDetail.prdTaxDescript" theme="bootstrap" on-select="contract.selectPrdTaxDescript()" required
                                                   ng-disabled="contract.contractDetail.recordId != null && contract.contractDetail.recordId != '' && contract.contract.status != '200201'">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.refer.taxDescriptList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4" ng-if="(!contract.factoryFlag && contract.contractDetail.madeSupplierId != null
                            	&& contract.contractDetail.madeSupplierId != '') || !contract.contractDetail.madeSupplierId">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>采购单价({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.contractDetail.madePcsPrice">
                                        参考RMB:{{(contract.contractDetail.madePcsPrice * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" required
                                               ng-model="contract.contractDetail.madePcsPrice" ng-blur="contract.checkMadePcsPrice()"
                                               ng-disabled="(contract.contractDetail.recordId != null && contract.contractDetail.recordId != '' && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204')" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="!contract.factoryFlag && contract.contractDetail.madeSupplierId != null
                            	&& contract.contractDetail.madeSupplierId != '' && contract.ValiteCompanyUtil.valiteSaleCompany">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">总部经济价格({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.contractDetail.lnPrice">
                                        参考RMB:{{(contract.contractDetail.lnPrice * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.lnPrice" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="!contract.factoryFlag && contract.contractDetail.madeSupplierId != null
                            	&& contract.contractDetail.madeSupplierId != '' && contract.ValiteCompanyUtil.valiteSaleCompany">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">不含税采购价格({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.contractDetail.notaxPrdPrice">
                                        参考RMB:{{(contract.contractDetail.notaxPrdPrice * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.notaxPrdPrice" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="!contract.factoryFlag && contract.contractDetail.madeSupplierId != null && contract.contractDetail.madeSupplierId != '' && contract.ValiteCompanyUtil.valiteSaleCompany">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">采购数量：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.prdQuantity" ng-disabled="contract.contractDetail.recordId != null && contract.contractDetail.recordId != '' && contract.contract.status != '200201'" />
                                    </div>
                                </div>
                            </div>
                            <!-- zjn 2021-05-19 新增属性 start -->
                            <div class="col-md-6 col-lg-4" ng-if="((!contract.factoryFlag && contract.contractDetail.madeSupplierId != null
                            && contract.contractDetail.madeSupplierId != '' && contract.ValiteCompanyUtil.valiteSaleCompany) || !contract.contractDetail.madeSupplierId) && contract.craftList && contract.craftList.length > 0">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>生产编号：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <ui-select theme="bootstrap" ng-model="contract.contractDetail.notiCraftNo" required ng-disabled="contract.contractDetail.recordId != null
                               				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                               				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in contract.craftList">
                                                <small>
                                                    <span style="color: blue;">生产编号：&nbsp;&nbsp;&nbsp;&nbsp;{{item.no}}<br></span>
                                                    <span style="color: red;">客户型号：&nbsp;&nbsp;&nbsp;&nbsp;{{item.customerModel}}<br></span>
                                                </small>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4" ng-if="((!contract.factoryFlag && contract.contractDetail.madeSupplierId != null
                            && contract.contractDetail.madeSupplierId != '' && (contract.ValiteCompanyUtil.valiteSaleCompany||contract.companyId == contract.ValiteCompanyUtil.factId)) || !contract.contractDetail.madeSupplierId) && (!contract.craftList || contract.craftList.length == 0)">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>生产编号：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" required
                                               ng-model="contract.contractDetail.genCraftNo"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.factoryFlag && contract.contractDetail.madeSupplierId != null && contract.contractDetail.madeSupplierId != ''">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">采购数量：</label>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.prdQuantity" ng-disabled="contract.contractDetail.recordId != null && contract.contractDetail.recordId != '' && contract.contract.status != '200201'" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.madeSupplierId != null
                            && contract.contractDetail.madeSupplierId != ''">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">采购模具费用({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.contractDetail.prdMouldFee">
                                        参考RMB:{{(contract.contractDetail.prdMouldFee * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder=""
                                               ng-model="contract.contractDetail.prdMouldFee" ng-onlydecimals
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != ''  && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.madeSupplierId != null
                            && contract.contractDetail.madeSupplierId != ''">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">采购测试架费({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.contractDetail.prdTestShelfFee">
                                        参考RMB:{{(contract.contractDetail.prdTestShelfFee * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" placeholder=""
                                               ng-model="contract.contractDetail.prdTestShelfFee" ng-onlydecimals
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != ''  && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.madeSupplierId != null
                            && contract.contractDetail.madeSupplierId != ''">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">采购工程费用({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.contractDetail.prdEngineeringFee">
                                        参考RMB:{{(contract.contractDetail.prdEngineeringFee * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-onlydecimals
                                               ng-model="contract.contractDetail.prdEngineeringFee"
                                               ng-disabled="(contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != ''  && contract.contract.status != '200201'
                                        				&& contract.contract.status != '200207' && contract.contract.status != '200204')
                                        				|| (contract.contractDetail.recordId != contract.contractDetail.groupCenterId && contract.contract.status != '200201'
                                                        && contract.contractDetail.valueOrderType == '1')
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.madeSupplierId != null
                            && contract.contractDetail.madeSupplierId != ''">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">采购菲林费用({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.contractDetail.prdFilmFee">
                                        参考RMB:{{(contract.contractDetail.prdFilmFee * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-onlydecimals
                                               ng-model="contract.contractDetail.prdFilmFee"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != ''  && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.madeSupplierId != null
                            && contract.contractDetail.madeSupplierId != ''">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">采购其它费用({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.contractDetail.prdOthersFee">
                                        参考RMB:{{(contract.contractDetail.prdOthersFee * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text" ng-onlydecimals
                                               ng-model="contract.contractDetail.prdOthersFee"
                                               ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != ''  && contract.contract.status != '200201' && contract.contract.status != '200207' && contract.contract.status != '200204'
                                        				"
                                        />
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.pricees.cardMouldFee">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">工程模具费用({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.contractDetail.pricees.cardMouldFee">
                                        参考RMB:{{(contract.contractDetail.pricees.cardMouldFee * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text"
                                               ng-model="contract.contractDetail.pricees.cardMouldFee" ng-onlydecimals
                                               ng-disabled="true"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.pricees.cardTestShelfFee">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">工程测试架费用({{contract.contract.exchangeType?contract.contract.exchangeType:"RMB"}})：</label>
                                    <span ng-if="contract.contract.exchangeType && contract.contractDetail.pricees.cardTestShelfFee">
                                        参考RMB:{{(contract.contractDetail.pricees.cardTestShelfFee * contract.contract.exchangeRate).toFixed(4)}}
                                    </span>
                                    <div class="col-sm-7 col-md-7">
                                        <input class="form-control" type="text"
                                               ng-model="contract.contractDetail.pricees.cardTestShelfFee" ng-onlydecimals
                                               ng-disabled="true"
                                        />
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">出库存：</label>
                                    <div class="col-sm-7 col-md-8" style="padding-top: 0.8rem;">
                                        <input type="checkbox" ng-model="contract.contractDetail.outInventory"
                                               ng-disabled="contract.contractDetail.recordId != null && contract.contractDetail.recordId != ''
                                               && contract.contract.status != '200201'" ng-init="contract.contractDetail.outInventory = '1'"
                                               ng-click="contract.outInventory()">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12 col-lg-8" ng-if="contract.contractDetail.madeSupplierId != null
                            && contract.contractDetail.madeSupplierId != ''">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">采购明细备注：</label>
                                    <div class="col-sm-7 col-md-10">
								<textarea style="width: 95%;" class="form-control"
                                          placeholder="采购明细备注"
                                          ng-model="contract.contractDetail.prdDetailRemark"
                                          ng-disabled="contract.contractDetail.recordId != null
                                        				&& contract.contractDetail.recordId != '' && (contract.contract.status != '200201' ||
                                        				(contract.contractDetail.groupCenterId != null && contract.contractDetail.groupCenterId != ''))"></textarea>
                                    </div>
                                </div>
                            </div>
                            <!-- zjn 2021-05-19 新增属性 end -->
                        </div>

                        <div class="portlet light bordered" ng-if="!contract.contractDetail.madeSupplierId">
                            <div class="portlet-title">
                                <div class="caption font-blue-hoki">占用库存列表</div>
                                <div class="actions" ng-if="!contract.contractDetail.recordId || contract.contractDetail.status == '200201' && contract.right.edit">
                                    <div class="portlet-input input-inline input-small">
                                        <button type="button" class="btn btn-success btn-default-width"
                                                ng-click="contract.outStocksOpen()">
                                            <i class="fa fa-plus"></i> 出库存
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="portlet-body">
                                <table style="margin-top: 5px;" class="table table-striped table-bordered table-condensed table-advance table-hover">
                                    <thead>
                                    <tr class="heading">
                                        <th>客户</th>
                                        <th>合同号</th>
                                        <th>客户订单号</th>
                                        <th>类型</th>
                                        <th>数量</th>
                                        <th>单价</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat="use in contract.contractDetail.stocksUseList">
                                        <td ng-bind="use.cusConcatValue"></td>
                                        <td ng-bind="use.contratNo"></td>
                                        <td ng-bind="use.customerPo"></td>
                                        <td ng-bind="use.typeStr"></td>
                                        <td ng-bind="use.quantity"></td>
                                        <td ng-bind="use.price"></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>


                        <div class="row" ng-if="contract.contractDetail.status != '200201'">
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.processFee && contract.contractDetail.processFee > 0 && contract.contractDetail.valueOrderType == 2">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">加工费(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.processFee" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.materialFee && contract.contractDetail.materialFee > 0 && contract.contractDetail.valueOrderType == 2">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">材料费(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.materialFee" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.prdAmountFee && contract.contractDetail.prdAmountFee > 0 && contract.contractDetail.valueOrderType == 2">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">采购费(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.prdAmountFee" ng-disabled="true"></input>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.costFee && contract.contractDetail.costFee > 0">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">成本(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.costFee" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                            <%--                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.grossProfitMargin">
                                                            <div class="form-group">
                                                                <label ng-if="0 > contract.contractDetail.grossProfitMargin" class="col-sm-3 col-md-4 control-label"><span class="required">毛利润率(%)：</span></label>
                                                                <label ng-if="contract.contractDetail.grossProfitMargin >= 0" class="col-sm-3 col-md-4 control-label">毛利润率(%)：</label>
                                                                <div class="col-sm-7 col-md-8">
                                                                    <input class="form-control" type="text" ng-model="contract.contractDetail.grossProfitMargin" ng-disabled="true"></input>
                                                                </div>
                                                            </div>
                                                        </div>--%>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.saleFee && contract.contractDetail.saleFee > 0 && contract.contractDetail.valueOrderType == 2">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">业务费(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.saleFee" ng-disabled="true"></input>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.manageFee && contract.contractDetail.manageFee > 0 && contract.contractDetail.valueOrderType == 2">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">管理费(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.manageFee" ng-disabled="true"></input>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.netCostFee && contract.contractDetail.netCostFee > 0">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">净成本(元)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.netCostFee" ng-disabled="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.netGrossProfitMargin">
                                <div class="form-group">
                                    <label ng-if="0 > contract.contractDetail.netGrossProfitMargin" class="col-sm-3 col-md-4 control-label"><span class="required">净利润率(%)：</span></label>
                                    <label ng-if="contract.contractDetail.netGrossProfitMargin >= 0" class="col-sm-3 col-md-4 control-label">净利润率(%)：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.netGrossProfitMargin" ng-disabled="true"></input>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row" ng-if="contract.contractDetail.copy">
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.copy.materialFee">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">工程材料费</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.copy.materialFee" ng-disabled="true"></input>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.copy.estimateDateStr">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">工程交期</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.copy.estimateDateStr" ng-disabled="true"></input>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="contract.contractDetail.copy.useRatio">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">工程利用率(%)</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input class="form-control" type="text" ng-model="contract.contractDetail.copy.useRatio" ng-disabled="true"></input>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <%--                       <div class="panel-body" ng-if="contract.contractDetail.recordId
                                                                && contract.contractDetail.status == '200201' && contract.contract.status == '200201'">
                                                   <div align="center" class="row">
                                                          <button class="btn green" ngf-select="contract.selectPrintFile($file, $invalidFile)"
                                                             ngf-max-size="2MB" >
                                                           <span class="glyphicon glyphicon-cloud-upload"></span> 上传PCB资料
                                                          </button>
                                                          <span ng-if="contract.contractDetail.fileName">
                                                          上传成功{{contract.contractDetail.fileName}}</span>
                                                   </div>
                                               </div>--%>

                        <div class="row" style="margin-bottom: 5px;">
                            <div class="col-md-12 text-center" ng-if="contract.right.edit">
                                <button type="submit" class="btn btn-primary btn-default-width"
                                        style="margin-left: 15px;"
                                        ng-click="contract.priceVerification(addcraft_form)"
                                        ng-disabled="addcraft_form.$invalid"
                                        ng-if="!(contract.contractDetail.recordId != null
                                 		&& contract.contractDetail.recordId != '') ||
                                 		contract.contract.status == '200201'">
                                    <i class="fa fa-save"></i> 保&nbsp;存
                                </button>
                                <button class="btn btn-primary btn-default-width" ng-click="contract.addContractDetail()"
                                        style="margin-left: 15px;"
                                        ng-if="contract.contractDetail.recordId != null
                                         && contract.contractDetail.recordId != '' && contract.contract.status != '200209' && contract.contract.status == '200201' && !contract.contract.interCustomRecordId">
                                    <i class="fa fa-save"></i> 继续添加
                                </button>
                                <button class="btn btn-primary btn-default-width"
                                        ng-if="contract.contractDetail && contract.contractDetail.recordId
                                 		&& contract.contractDetail.status == '200201'
                                 		&& contract.contract.status == '200201'
                                 		&&((contract.contractDetail.factoryName &&
                                 		contract.contractDetail.factoryName.indexOf('江西领德辉') >= 0)||(!contract.contractDetail.factoryName && contract.companyId == contract.ValiteCompanyUtil.factId))"
                                        style="margin-left: 15px;" ng-click="contract.routerGroupCenter(contract.contractDetail.recordId)">
                                    <i class="fa fa-save"></i> 一键集控
                                </button>
                                <button class="btn btn-primary btn-default-width"
                                        ng-if="contract.contractDetail.recordId != null
                                 		&& contract.contractDetail.recordId != ''
                                 		&& (contract.contract.status == '200207' || contract.contract.status == '200204')"
                                        style="margin-left: 15px;" ng-click="contract.addFeeApproval(contract.contractDetail)">
                                    <i class="fa fa-save"></i> 提交费用审批
                                </button>
                            </div>
                        </div>
                    </form>
                    <div class="portlet light bordered" ng-if="contract.contractDetail.recordId && contract.right.edit">
                        <div class="portlet-title">
                            <div class="caption font-green-sharp">
                                <i class="icon-cloud-upload font-green-sharp"></i>
                                <span class="caption-subject bold uppercase">原始凭据列表</span>
                            </div>
                            <div class="actions">
                                <div class="portlet-input input-inline input-small">
                                    <button type="button" class="btn green btn-default-width" ng-if="contract.contractDetail.status == '200201'" ng-click="contract.addInvoiceForDetail()"><i class="fa fa-plus"></i> 添加原始凭据</button>
                                </div>
                            </div>
                        </div>
                        <div class="portlet-body">
                            <div class="table-scrollable" style="margin-top:0px !important">
                                <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                                    <thead>
                                    <tr class="heading">
                                        <th width="50%">文件名</th>
                                        <th id="stepInfo3">操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat="row in contract.contDetialAttachementsList" ng-dblclick="contract.getViewUrl($index,row.orgFileName)">
                                        <td><a ng-click="contract.getViewUrl($index,row.orgFileName)">{{row.orgFileName}}</a></td>
                                        <td>
                                            <form class="inline" ng-form-commit name="downForm" action="a/order/newContract/down" method="Post" target="hidden_frame">
                                                <input type="text" id="realFileName" ng-show="false" name="realFileName" ng-model="row.realFileName"  />
                                                <a class="btn default btn-xs green-stripe" ng-click="contract.submitDown(downForm)">下载</a>
                                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                                            </form>
                                            <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="contract.validateIsImage(row.orgFileName)"
                                               ng-click="contract.getViewUrl($index,null)"><i class="fa fa-view font-green"></i> 查看</a>
                                            <a href="javascript:void(0)" class="btn default btn-xs red-stripe" ng-if="contract.contractDetail.status == '200201' && !contract.contract.interCustomRecordId"
                                               ng-click="contract.showDelFile($index)">删除 </a>

                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <div class="row">
        <div class="col-md-12">
            <div id="staticCommitAuditOpen" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                            <h4 class="modal-title">合同审批</h4>
                        </div>
                        <div class="modal-body clearfix" style="padding-top:25px;">
                            <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                            <div class="col-sm-11" ><p ng-bind="contract.message"></p></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.commitAudit()">确定</button>
                            <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div id="staticCommitReflashAudit" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                            <h4 class="modal-title">合同反审</h4>
                        </div>
                        <div class="modal-body clearfix" style="padding-top: 25px;">
                            <div class="form-horizontal">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="control-label col-md-3"><span class="required">*</span>反审原因：</label>
                                            <div class="col-md-8">
                                                <textarea  class="form-control" placeholder="反审原因" ng-model="contract.contract.remark"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.commitReflashAudit()">确定</button>
                            <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div id="addFeeApprovalAudit" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                            <h4 class="modal-title">合同费用审批</h4>
                        </div>
                        <div class="modal-body clearfix" style="padding-top: 25px;">
                            <div class="form-horizontal">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="control-label col-md-3"><span class="required">*</span>调整原因：</label>
                                            <div class="col-md-8">
                                                <textarea  class="form-control" placeholder="调整原因" ng-model="contract.contractDetailLet.auditRemark"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.commitFeeApprovalAudit()">确定</button>
                            <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div id="staticCancel" class="modal fade" tabindex="-1"
                 data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                            <h4 class="modal-title">撤回合同审批</h4>
                        </div>
                        <div class="modal-body clearfix" style="padding-top: 25px;">
                            <div class="col-sm-1 text-center">
                                <i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i>
                            </div>
                            <div class="col-sm-11">{{contract.message}}</div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.doCancelAudit()">确定</button>
                            <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div id="staticRemove" class="modal fade" tabindex="-1"
                 data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                            <h4 class="modal-title">删除合同</h4>
                        </div>
                        <div class="modal-body clearfix" style="padding-top: 25px;">
                            <div class="col-sm-1 text-center">
                                <i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i>
                            </div>
                            <div class="col-sm-11">{{contract.message}}</div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.doDelContract()">确定</button>
                            <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div id="staticRemoveDetail" class="modal fade" tabindex="-1"
                 data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                            <h4 class="modal-title">提示</h4>
                        </div>
                        <div class="modal-body">
                            <p>确认删除该明细吗？</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.delCheckRepet()">删除</button>
                            <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-modal-lg" id="invalidContractModel" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title">
                        <span>作废合同</span>
                    </h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <strong>注意：作废后不可进行恢复，作废后不还原统计信息!</strong>
                    </div>
                    <table class="table table-bordered table-hover">
                        <tr>
                            <td>生产编号</td>
                            <td>客户型号</td>
                            <td>数量</td>
                            <td>金额</td>
                            <td>作废原因</td>
                            <td>#</td>
                        </tr>
                        <tr ng-repeat ="item in contract.invalidInfo">
                            <td>{{item.craft.no}}</td>
                            <td><span>{{item.craft.customerModel}}</span></td>
                            <td><span>{{item.quantity}}</span></td>
                            <td><span>{{item.subTotal}}</span></td>
                            <td><input type="text" class="form-control" ng-model="item.voidReason"></td>
                            <td><button class="btn" ng-click="contract.doInvalidContractDetailDialog(item)">作废</button></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-modal-lg" id="editPrice" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title">
                        <span>修改合同明细的价格</span>
                    </h4>
                </div>
                <div class="modal-body">
                    <table class="table table-bordered table-hover">
                        <tr>
                            <td>销售价格（元/pcs）</td>
                            <td>采购价格（元/pcs）</td>
                            <td>江西公司价格（元/pcs）</td>
                        </tr>
                        <tr>
                            <td>
                                <input class="form-control" ng-model="contract.contractDeailPrice.pricees.price">
                            </td>
                            <td>
                                <input class="form-control" ng-model="contract.contractDeailPrice.lnPrice" ng-disabled="!contract.contractDeailPrice.lnConDeailId">
                            </td>
                            <td>
                                <input class="form-control" ng-model="contract.contractDeailPrice.jxPrice" ng-disabled="!contract.contractDeailPrice.jxConDeailId">
                            </td>
                        </tr>
                        <tr>
                            <td colspan="3">
                                <div class="row">
                                    <div class="col-md-12">
                                        <textarea class="form-control" placeholder="原因" ng-model="contract.contractDeailPrice.cause"></textarea>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.auditContractPrice(contract.contractDeailPrice)">提交审批</button>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div id="doInvalidConfirm" class="modal fade" tabindex="-1"
                 data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"
                                    aria-hidden="true"></button>
                            <h4 class="modal-title">操作提示</h4>
                        </div>
                        <div class="modal-body clearfix" style="padding-top: 25px;">
                            <div class="col-sm-1 text-center">
                                <i class="fa fa-warning font-yellow"
                                   style="font-size: 30px !important;"></i>
                            </div>
                            <div class="col-sm-11" style="color: red;">客户型号：{{contract.invalidInfoItem.craft.customerModel}}，生产编号：{{contract.invalidInfoItem.craft.no}}，数量：{{contract.invalidInfoItem.quantity}}，确认作废？</div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.doInvalidContractDetail()"
                            >确认</button>
                            <button type="button" data-dismiss="modal" class="btn btn-default" >取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade bs-modal-lg" id="adjustContract" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title">
                        <span>调整合同</span>
                    </h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <strong>注意：调整后不可恢复，将修改统计信息!</strong>
                    </div>
                    <div class="form-horizontal">
                        <div ng-repeat ="item in contract.adjustInfo" style="border-top:1px solid #EEE;">
                            <div class="row" style="margin-top:10px;">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label col-md-2">合同明细：</label>
                                        <div class="col-md-8">
                                            <table class="table table-bordered" style="margin-bottom:0px;">
                                                <tr>
                                                    <td>生产编号:</td>
                                                    <td><span>{{item.craft.no}}</span></td>
                                                    <td>客户型号:</td>
                                                    <td><span>{{item.craft.customerModel}}</span></td>
                                                    <td>订单数量:</td>
                                                    <td><span>{{item.quantity}}</span></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 有投料也有补料 -->
                            <div class="row" ng-if="item.hasFeeding && item.hasReplenish">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label col-md-2"><span class="required">*</span>调整合同数量：</label>
                                        <div class="col-md-8">
                                            <input class="form-control" placeholder="请输入数量"
                                                   value="因已进行投料，仅可以向上调整（若生产完成可向下调整）..."
                                                   ng-click="contract.adjustContractQtyBlur(item)" />
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label col-md-2">当前投料：</label>
                                        <div class="col-md-8">
                                            <div ng-repeat ="notiItem in item.notificationList">
                                                <div >
                                                    <table class="table table-bordered table-hover" style="margin-bottom:0px;">
                                                        <tr ng-repeat ="feedItem in notiItem.feedings">
                                                            <td>{{notiItem.no}}</td>
                                                            <td><span>{{feedItem.no}}</span></td>
                                                            <td><span>{{feedItem.pcsQuantity}}</span></td>
                                                            <td ng-if="feedItem.isCount"><span class="label label-warning">{{feedItem.isCountDesc}}</span></td>
                                                            <td ng-if="!feedItem.isCount"><span class="label label-success">可调整</span></td>
                                                            <td><input type="text" ng-disabled="feedItem.isCount" value="0" ng-click="contract.adjustFeedingQtyBlur(item, notiItem, feedItem)"/></td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label col-md-2">当前补料：</label>
                                        <div class="col-md-8">
                                            <div ng-repeat ="notiItem in item.notificationList">
                                                <div >
                                                    <table class="table table-bordered table-hover" style="margin-bottom:0px;">
                                                        <tr ng-repeat ="feedItem in notiItem.replenishs">
                                                            <td>{{notiItem.no}}</td>
                                                            <td><span>{{feedItem.no}}</span></td>
                                                            <td><span>{{feedItem.discardQty}}</span></td>
                                                            <td ng-if="feedItem.isCount"><span class="label label-warning" >{{feedItem.isCountDesc}}</span></td>
                                                            <td ng-if="!feedItem.isCount"><span class="label label-success">可调整</span></td>
                                                            <td><input type="text" ng-disabled="feedItem.isCount" value="0" ng-click="contract.adjustReplenishQtyBlur(item, notiItem, feedItem)"/></td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 有投料没有补料 -->
                            <div class="row" ng-if="item.hasFeeding && !item.hasReplenish">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label col-md-2"><span class="required">*</span>调整合同数量：</label>
                                        <div class="col-md-8">
                                            <input class="form-control" placeholder="请输入数量"
                                                   value="因已进行投料，仅可以向上调整（若生产完成可向下调整）..."
                                                   ng-click="contract.adjustContractQtyBlur(item)"/>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label col-md-2">当前投料：</label>
                                        <div class="col-md-8">
                                            <div ng-repeat ="notiItem in item.notificationList">
                                                <div >
                                                    <table class="table table-bordered table-hover" style="margin-bottom:0px;">
                                                        <tr ng-repeat ="feedItem in notiItem.feedings">
                                                            <td>{{notiItem.no}}</td>
                                                            <td><span>{{feedItem.no}}</span></td>
                                                            <td><span>{{feedItem.pcsQuantity}}</span></td>
                                                            <td ng-if="feedItem.isCount"><span class="label label-warning">{{feedItem.isCountDesc}}</span></td>
                                                            <td ng-if="!feedItem.isCount"><span class="label label-success">可调整</span></td>
                                                            <td><input type="text" ng-disabled="feedItem.isCount" value="0" ng-click="contract.adjustFeedingQtyBlur(item, notiItem, feedItem)"/></td>
                                                        </tr>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 有通知单没有投补料  /无通知单-->
                            <div class="row" ng-if="!item.hasNotification || (item.hasNotification && !item.hasFeeding && !item.hasReplenish)">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label col-md-2"><span class="required">*</span>调整合同数量：</label>
                                        <div class="col-md-8">
                                            <input class="form-control" placeholder="请输入数量"
                                                   value="0"
                                                   ng-click="contract.adjustContractQtyBlur(item)"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="modal fade bs-modal-lg" id="adjustContractDetail" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"
                                    aria-hidden="true"></button>
                            <h4 class="modal-title">调整合同数量</h4>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-warning" ng-show="contract.adjustMsg != ''">
                                调整提醒：<span ng-bind-html="contract.adjustMsg"></span>
                            </div>

                            <div class="form-horizontal">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="control-label col-md-3">生产编号：</label>
                                            <div class="col-md-8">
                                                <span class="form-control" disabled ng-bind="contract.adjustContractDetail.craft.no"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="control-label col-md-3">客户型号：</label>
                                            <div class="col-md-8">
                                                <span class="form-control" disabled ng-bind="contract.adjustContractDetail.craft.customerModel"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="control-label col-md-3">订单数量：</label>
                                            <div class="col-md-8">
                                                <span class="form-control" disabled ng-bind="contract.adjustContractDetail.quantity"></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="control-label col-md-3"><span class="required">*</span>调整后订单数量：</label>
                                            <div class="col-md-8">
                                                <input class="form-control" placeholder="请输入调整数量"
                                                       ng-model="contract.adjustQty"
                                                       ng-blur="contract.clickAdjustContractQtyBlur()" ng-onlynumberandzero/>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="control-label col-md-3">分批计划：</label>
                                            <div class="col-md-8">
                                                <table class="table table-bordered">
                                                    <thead>
                                                    <tr class="heading">
                                                        <th>批次编号</th>
                                                        <th>交货数量</th>
                                                        <th>交货日期</th>
                                                        <th>已送数量</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr ng-repeat="row in contract.adjustContractDetail.batchList">
                                                        <td style='vertical-align: middle;'>{{$index + 1}}</td>
                                                        <td style='vertical-align: middle;' width="30%" class="form-group">
                                                            <input required class="form-control" type="number"
                                                                   placeholder="请输入本批次交货数量"
                                                                   ng-model="row.quantity" min="0" max="{{contract.adjustQty}}"
                                                                   ng-onlynumber ng-disabled="row.status == '200204'||row.status == '200210'">
                                                        </td>
                                                        <td style='vertical-align: middle;' ng-bind="row.deliveryDate | dateYMD"></td>
                                                        <td style='vertical-align: middle;'>{{row.actualQty}}</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn blue" ng-click="contract.doAdjustContractDetail()">确定</button>
                            <button type="button" data-dismiss="modal" class="btn default">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- START 比较选择绑定生产编号后的变动 -->
    <tab active="contract.tabs.contractDetail.active" ng-show = "contract.tabs.contractDetail.show">
        <tab-heading>合同明细变动明细<i class="fa fa-times set-cursor-pointer" ng-click="contract.hideContractForm()"></i></tab-heading>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">合同明细变动</div>
            </div>
            <div class="portlet-body">
                <div id="InvenDeailStep1" class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                        <thead>
                        <tr class="heading" >
                            <th>变动名字</th>
                            <th>变动前</th>
                            <th>变动后</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in contract.changeList">
                            <td ng-bind="row.itemName"></td>
                            <td ng-bind="row.valueNameOne"></td>
                            <td ng-bind="row.valueNameTwo"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </tab>


    <div class="row">
        <div class="col-md-12">
            <div id="staticModSimple" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                            <h4 class="modal-title">新单样品订量修改(样品面积不能超过1平米)</h4>
                        </div>
                        <div class="modal-body clearfix" style="padding-top: 25px;">
                            <div class="form-horizontal">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label col-md-3">原订单数量：</label>
                                            <div class="col-md-8">
                                                <input class="form-control" type="text" placeholder="原订单数量" ng-model="contract.modSimple.oldQuantity" readonly="readonly">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label col-md-3">原订单面积：</label>
                                            <div class="col-md-8">
                                                <input class="form-control" type="text" placeholder="原订单面积" ng-model="contract.modSimple.oldOrderDeailArea" readonly="readonly">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label col-md-3">新订单数量：</label>
                                            <div class="col-md-8">
                                                <input class="form-control" type="text" placeholder="新订单数量" ng-model="contract.modSimple.quantity"  ng-blur="contract.doModSimpleArea()">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label col-md-3">新订单面积：</label>
                                            <div class="col-md-8">
                                                <input class="form-control" type="text" placeholder="新订单面积" ng-model="contract.modSimple.orderDeailArea" readonly="readonly">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.modSimpleQuantityCommit()">提交</button>
                            <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div id="staticCustomerPo" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                            <h4 class="modal-title">客户订单号修改</h4>
                        </div>
                        <div class="modal-body clearfix" style="padding-top: 25px;">
                            <div class="form-horizontal">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="control-label col-md-3">原客户订单号：</label>
                                            <div class="col-md-8">
                                                <input class="form-control" type="text" placeholder="原客户订单号" ng-model="contract.contract.customerPo" readonly="readonly">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="control-label col-md-3">客户订单号：</label>
                                            <div class="col-md-8">
                                                <input class="form-control" type="text" placeholder="客户订单号" ng-model="contract.contract.updateCustomerPo">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.editCustomerPo()">提交</button>
                            <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-modal-lg" id="splitDeail" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title">
                        <span>拆分合同明细的数量</span>
                    </h4>
                </div>
                <div class="modal-body">
                    <table class="table table-bordered table-hover">
                        <tr>
                            <td>客户型号</td>
                            <td>规格</td>
                            <td>板材厚度</td>
                            <td>数量</td>
                            <td>交期</td>
                            <td>操作</td>
                        </tr>
                        <tr ng-repeat="detail in contract.contract.contractDetails">
                            <td>
                                {{detail.contractCraftList.customerModel}}
                            </td>
                            <td>
                                {{detail.contractCraftList.pnlsize}}
                            </td>
                            <td>
                                {{detail.contractCraftList.boardThickness}}
                            </td>
                            <td>
                                {{detail.quantity}}
                            </td>
                            <td>
                                {{detail.deliveryDate}}
                            </td>
                            <td>
                                <a class="btn btn-xs btn-default" style="margin-left: 15px;"
                                   ng-click="contract.splitNum(detail)">拆分
                                </a>
                                <a class="btn btn-xs btn-default"
                                   ng-if="detail.recordId
                           		&&((detail.factoryName && detail.factoryName.indexOf('江西领德辉') >= 0) ||(!detail.factoryName && detail.company.recordId == contract.ValiteCompanyUtil.factId))"
                                   style="margin-left: 15px;" ng-click="contract.routerGroupCenter(detail.recordId)">
                                    一键集控
                                </a>
                                <a class="btn btn-xs btn-default"
                                   ng-if="detail.groupCenterId != null
                           		&& detail.groupCenterId != ''"
                                   style="margin-left: 15px;" ng-click="contract.clearGroupCenter(detail)">
                                    清除集控
                                </a>
                            </td>
                        </tr>
                    </table>
                    <div class="row">
                        <div class="col-md-12">
                            <textarea class="form-control" placeholder="原因"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.genSplitDetailData()">提交</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-modal-lg" id="splitDeailNum" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title">
                        <span>拆分合同明细的数量</span>
                        <span style="font-size: 0.5rem;">
                   		(客户型号：{{contract.contractDetail.contractCraftList.customerModel}}&emsp;
						规格：{{contract.contractDetail.contractCraftList.pnlsize}}&emsp;
						板材厚度：{{contract.contractDetail.contractCraftList.boardThickness}}&emsp;
						数量：{{contract.contractDetail.quantity}}&emsp;
						交期：{{contract.contractDetail.deliveryDate}})
					</span>
                    </h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-2" ng-repeat ="item in contract.conSplitList">
                            <div class="form-group">
                                <input class="form-control" type="text" placeholder="数量" ng-model="item.quantity" ng-blur="contract.updateConNum(item)">
                                <button type="button" class="btn btn-sm red" ng-click="contract.delConSplitList($index)">删除明细</button>
                            </div>
                        </div>
                        <div class="col-md-3" style="padding-top: 0.2rem;">
                            <div class="form-group">
                                <button type="button" class="btn btn-sm blue" ng-click="contract.addConSplitList()">增加明细</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.saveSplitList()">保存数据</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-modal-lg" id="closingCaseModel" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                    <h4 class="modal-title">
                        <span>作废合同</span>
                    </h4>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <strong>注意：结案后明细状态将变更为“已送货”!</strong>
                    </div>
                    <table class="table table-bordered table-hover">
                        <tr>
                            <td>生产编号</td>
                            <td>客户型号</td>
                            <td>数量</td>
                            <td>金额</td>
                            <td>#</td>
                        </tr>
                        <tr ng-repeat ="item in contract.closingCaseInfo">
                            <td>{{item.craft.no}}</td>
                            <td><span>{{item.craft.customerModel}}</span></td>
                            <td><span>{{item.quantity}}</span></td>
                            <td><span>{{item.subTotal}}</span></td>
                            <td><button class="btn" ng-click="contract.closingCaseModel(item)">结案</button></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- 是否确认结案 -->
    <div class="row">
        <div class="col-md-12">
            <div id="staticClosingCase" class="modal fade" tabindex="-1"
                 data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal"
                                    aria-hidden="true"></button>
                            <h4 class="modal-title">操作提示</h4>
                        </div>
                        <div class="modal-body clearfix" style="padding-top: 25px;">
                            <div class="col-sm-1 text-center">
                                <i class="fa fa-warning font-yellow"
                                   style="font-size: 30px !important;"></i>
                            </div>
                            <div class="col-sm-11" ng-bind="contract.closingCaseMessage"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn blue"
                                    ng-click="contract.closingCase()">确定</button>
                            <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div id="staticBatchClosingCaseContract" class="modal fade" tabindex="-1"
                 data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                            <h4 class="modal-title">结案合同</h4>
                        </div>
                        <div class="modal-body clearfix" style="padding-top: 25px;">
                            <div class="col-sm-1 text-center">
                                <i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i>
                            </div>
                            <div class="col-sm-11">{{contract.message}}</div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.batchClosingCase()">确定</button>
                            <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div id="staticDetailReflashAudit" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                            <h4 class="modal-title">合同明细反审</h4>
                        </div>
                        <div class="modal-body clearfix" style="padding-top: 25px;">
                            <div class="form-horizontal">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="control-label col-md-3"><span class="required">*</span>明细反审原因：</label>
                                            <div class="col-md-8">
                                                <textarea  class="form-control" placeholder="明细反审原因" ng-model="contract.contractDetail.remark"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.detailReflashAudit()">确定</button>
                            <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</tabset>

<div class="row">
    <div class="col-md-12">
        <div class="modal fade" id="showAddressData" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-full">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span class="text-primary">
                        	多收货地址设置(客户:{{contract.contract.customer.name}},客户型号:{{contract.setAddressDetail.contractCraftList.customerModel}},数量:{{contract.setAddressDetail.quantity | number:0}})
                        </span></h4>
                    </div>
                    <!-- <div class="modal-footer" >
                                <button type="button" class="btn btn-success btn-default-width" ng-click="contract.addDetailAddress()" >
                                                          <i class="fa fa-plus"></i>  添加
                                                     </button>
                                <button type="button"  class="btn blue" ng-click="contract.saveAddress()" >
                                <i class="fa fa-save"></i>保存</button>
                    </div> -->
                    <div class="modal-footer" ng-if="contract.contract.status!=200204&&contract.contract.status!=200210">
                        <button type="button"  class="btn btn-success btn-default-width" ng-click="contract.addDetailAddress()" >
                            <i class="fa fa-plus"></i>  添加
                        </button>
                        <button type="button"  class="btn blue" ng-click="contract.saveAddress()" >
                            <i class="fa fa-save"></i>保存</button>
                    </div>
                    <!-- 多月收款页面  start-->
                    <div class="row form-group" style="margin-bottom: 15px;" >
                        <div class="col-md-12">
                            <table class="table table-bordered table-condensed">
                                <thead>
                                <tr class="heading">
                                    <th width="10%"><span class="required text-danger">*</span>出货顺序</th>
                                    <th width="10%"><span class="required text-danger">*</span>联系人</th>
                                    <th width="10%">电话</th>
                                    <th width="10%"><span class="required text-danger">*</span>地址</th>
                                    <th width="10%"><span class="required text-danger">*</span>数量</th>
                                    <th width="10%" ng-if="detail.status != '200201'">出货状态</th>
                                    <th width="10%" ng-if="detail.status != '200201'">已出货数量</th>
                                    <th width="15%" >操作</th>
                                    <!-- <th width="15%" >操作</th> -->
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="row in contract.collectAdressList">
                                    <!-- 出货顺序  -->
                                    <td style='vertical-align: middle;' ng-if="row.isFinish == 1||contract.contract.status==200204||contract.contract.status==200210">
                                        {{row.sortNum}}
                                    </td>
                                    <td style='vertical-align: middle;' ng-if="row.isFinish != 1&&contract.contract.status!=200204&&contract.contract.status!=200210">
                                        <input required class="form-control" type="text"
                                               placeholder="请输入出货顺序" ng-blur="contract.doSortNum()"
                                               ng-model="row.sortNum"
                                               style="text-align: right;" ng-onlynumberandzero
                                        />
                                    </td>
                                    <!-- <td style='vertical-align: middle;' >
                                        <input required class="form-control" type="text"
                                           placeholder="请输入出货顺序" ng-blur="contract.doSortNum()"
                                           ng-model="row.sortNum"
                                           style="text-align: right;" ng-onlynumberandzero
                                           />
                                    </td> -->
                                    <!-- 联系人 -->
                                    <td style='vertical-align: middle;' ng-if="row.isFinish == 1||contract.contract.status==200204||contract.contract.status==200210">
                                        {{row.contacter.name}}
                                    </td>
                                    <td style='vertical-align: middle;' ng-if="row.isFinish != 1&&contract.contract.status!=200204&&contract.contract.status!=200210">
                                        <select  class="form-control"

                                                 ng-change="contract.loadContacterInfo($index)"
                                                 ng-model="row.contacterId"
                                                 ng-options="option.recordId as option.name for option in contract.customerContracterList" required>
                                            <option value="" selected="selected">--请选择--</option>
                                        </select>
                                    </td>
                                    <!-- <td style='vertical-align: middle;' >
                                        <select  class="form-control"

                                             ng-change="contract.loadContacterInfo($index)"
                                             ng-model="row.contacterId"
                                             ng-options="option.recordId as option.name for option in contract.customerContracterList" required>
                                            <option value="" selected="selected">--请选择--</option>
                                        </select>
                                    </td> -->
                                    <!-- 电话 -->
                                    <td style='vertical-align: middle;' >
                                        {{row.contacter.phone}}
                                    </td>
                                    <!-- 地址 -->
                                    <td style='vertical-align: middle;'>
                                        {{row.contacter.address}}
                                    </td>
                                    <!-- 数量 -->
                                    <td style='vertical-align: middle;' ng-if="row.isFinish == 1||contract.contract.status==200204||contract.contract.status==200210">
                                        {{row.quantity}}
                                    </td>
                                    <td style='vertical-align: middle;' ng-if="row.isFinish != 1&&contract.contract.status!=200204&&contract.contract.status!=200210">
                                        <input required class="form-control" type="text"
                                               placeholder="请输入数量" ng-blur="contract.doAddressQuantity()"
                                               ng-model="row.quantity"
                                               style="text-align: right;" ng-onlynumberandzero
                                        />
                                    </td>
                                    <!-- <td style='vertical-align: middle;' >
                                        <input required class="form-control" type="text"
                                   placeholder="请输入数量" ng-blur="contract.doAddressQuantity()"
                                   ng-model="row.quantity"
                                   style="text-align: right;" ng-onlynumberandzero
                                   />
                                    </td> -->
                                    <td style='vertical-align: middle;' ng-if="detail.status != '200201'">
                                        <span ng-if="row.isFinish == 1">已完成</span>
                                        <span ng-if="row.isFinish != 1"></span>
                                    </td>
                                    <td style='vertical-align: middle;' ng-if="detail.status != '200201'">
                                        {{row.outQuantity}}
                                    </td>
                                    <td style='vertical-align: middle;' >
                                        <button ng-if="row.isFinish != 1&&contract.contract.status!=200204&&contract.contract.status!=200210" type="button" class="btn btn-danger btn-xs" ng-click=" contract.deleteDetailAddress($index)">
                                            <i class="fa fa-trash-o"></i> 删除
                                        </button>
                                    </td>
                                    <!-- <td style='vertical-align: middle;' >
                                        <button type="button" class="btn btn-danger btn-xs" ng-click=" contract.deleteDetailAddress($index)">
                                             <i class="fa fa-trash-o"></i> 删除
                                         </button>
                                    </td>  -->
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- 多月收款页面 end -->
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="sentPrdorderStatic" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">生成采购合同</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="form-horizontal">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>供应商：</label>
                                        <div class="col-sm-7 col-md-7">
                                            <ui-select required ng-model="contract.prdorderObj.supplierId"theme="bootstrap">
                                                <ui-select-match placeholder="请选择...">{{$select.selected.name}} </ui-select-match>
                                                <ui-select-choices repeat="item.recordId as item in contract.refer.supplierList | filter: $select.search">
                                                    <div ng-bind-html="item.name | highlight: $select.search"></div>
                                                </ui-select-choices>
                                            </ui-select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.sentPrdorder()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="exportData" class="modal fade" tabindex="-1" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">合同引入</h4>
            </div>
            <div class="modal-body clearfix">
                <div class="row">
                    <div class="col-md-12">
                        <button class="btn btn-secondary">选择客户</button>
                    </div>
                </div>
                <div class="row portlet light bordered">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="row">

                                    <div class="form-group">
                                        <label class="col-sm-4 col-md-4 control-label"><span
                                                class="required">*</span>客户：</label>
                                        <div class="col-sm-7 col-md-7">
                                            <ui-select register-custom-form-control
                                                       on-select="contract.selectImpCustomer()"
                                                       ng-model="contract.impCustomer" theme="bootstrap"  ng-disabled="true"
                                            >
                                                <ui-select-match placeholder="">{{$select.selected.name}}</ui-select-match>
                                                <ui-select-choices
                                                        repeat="item.recordId as item in contract.customers | filter: $select.search| limitTo: contract.infiniteScrollcus.currentItemscus track by $index"
                                                        infinite-scroll="contract.addMoreItemscus()"
                                                        infinite-scroll-distance="2">
                                                    <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                    <small><span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.shortName}}<br></span></small>
                                                </ui-select-choices> </ui-select>
                                            <!-- <span>{{contract.impCustomerName}}</span>
                                            <input type="text" ng-show="false" name="contract.impCustomer" value="{{contract.impCustomer}}"/> -->
                                        </div>
                                    </div>
                                    <!-- <div class="col-md-4">
                                        <input type="radio" disabled/>追加
                                    </div>
                                    <div class="col-md-4">
                                        <input type="checkbox" disabled>仅暂存
                                    </div>
                                    <div class="col-md-4">
                                        <input type="checkbox" disabled>暂存引入失败数据
                                    </div> -->
                                </div>
                            </div>
                        </div>
                        <div class="row" style="padding-top: 1rem;">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-4">
                                        <span>适配摸版：</span>
                                    </div>
                                    <div class="col-md-4">
                                        <span ng-if="contract.uploadSettingName">{{contract.uploadSettingName}}</span>
                                        <span ng-if="!contract.uploadSettingName">系统默认摸版</span>
                                    </div>
                                    <div class="col-md-4">
                                        <a  target="_blank" style="margin-top: 5px;" ui-sref="crm.uploadsetting({ids: contract.impCustomer + ',4'})" >
                                            设置摸版
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="row" style="padding-top: 1rem;">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-4">
                                        <input type="radio" checked disabled/>覆盖
                                    </div>
                                    <div class="col-md-4">
                                        <input type="checkbox" disabled>运行追加
                                    </div>
                                    <div class="col-md-4">
                                        <input type="checkbox" checked disabled>匹配字段
                                    </div>
                                </div>
                            </div>
                        </div> -->
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        请选择要引入的数据文件
                        <form name="form1" enctype="multipart/form-data" id="form1" ng-submit="contract.uploadExcel();" style="padding-top: 0.5rem;">
                            <div class="row" style="padding-bottom: 1rem;">
                                <div class="col-md-12">
                                    <span class="btn btn-default" style="position: relative;display: inline-block;overflow: hidden;">
                                        <input id="upfile" name="upfile" type="file"/>
                                    </span>
                                </div>
                            </div>
                        </form>
                        <div class="row portlet light bordered">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-9">
                                        说明：数据文件需符合模板要求，若无模板请下载
                                    </div>
                                    <!--  <div class="col-md-3">
                                         <button ng-click="contract.runDownloadFile()" style="border: none;background-color: #fff;color: dodgerblue;"><i class="fa fa-download"></i> 引入模板</button>
                                     </div> -->
                                    <div class="col-md-3">
                                        <div class="portlet-input input-inline input-small">
                                            <form action="a/crm/uploadSetting/export"
                                                  method="post" enctype="multipart/form-data" target="hidden_frame">
                                                <input type="text" ng-show="false" name="customerId"
                                                       value="{{contract.impCustomer}}" />
                                                <input type="text" ng-show="false" name="orderType"
                                                       value="4" />
                                                <button type="submit"  style="border: none;background-color: #fff;color: dodgerblue;">
                                                    <i class="fa fa-download"></i> 下载模板
                                                </button>
                                                <iframe name="hidden_frame" id="hidden_frame"
                                                        style="display: none"></iframe>
                                            </form>
                                        </div>

                                        <!-- <button ng-click="contract.runDownloadFile()" style="border: none;background-color: #fff;color: dodgerblue;"><i class="fa fa-download"></i> 引入模板</button> -->
                                    </div>
                                </div>
                            </div>
                            <!--  <div class="col-md-12" style="padding-top: 1rem;">
                                 <div class="row">
                                     <div class="col-md-9">
                                         &emsp;&emsp;&emsp;引入失败的数据，可下载修复后重新引入
                                     </div>
                                     <div class="col-md-3">
                                         <form id="exportForm" action="a/crm/receivable/failReconciliationDataExport" method="POST" enctype="multipart/form-data" target="hidden_frame">
                                             <input type="text" ng-show="false" name="customerId" value="{{contract.impCustomer}}"/>
                                             <input type="text" ng-show="false" name="orderType"
                                                     value="4" />
                                             <input type="text" ng-show="false" name="type" value="1"/>
                                             <div ng-if="contract.right.view" >
                                                 <button type="submit" style="border: none;background-color: #fff;color: dodgerblue;"><i class="fa fa-download"></i> 失败数据</button>
                                                 <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                                             </div>
                                         </form>
                                     </div>
                                 </div>
                             </div>
                             <div class="col-md-12" style="padding-top: 1rem;">
                                 <div class="row">
                                     <div class="col-md-9">
                                         &emsp;&emsp;&emsp;引入成功的数据，可下载查看
                                     </div>
                                     <div class="col-md-3">
                                         <form action="a/crm/receivable/failReconciliationDataExport" method="POST" enctype="multipart/form-data" target="hidden_frame">
                                             <input type="text" ng-show="false"  name="customerId" value="{{contract.impCustomer}}"/>
                                             <input type="text" ng-show="false" name="orderType"
                                                     value="4" />
                                             <input type="text" ng-show="false" name="type" value="2"/>
                                             <div ng-if="contract.right.view" >
                                                 <button type="submit" style="border: none;background-color: #fff;color: dodgerblue;"><i class="fa fa-download"></i> 成功数据</button>
                                                 <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                                             </div>
                                         </form>
                                     </div>
                                 </div>
                             </div> -->
                        </div>
                        <div class="row">
                            <div class="col-md-12 text-right">
                                <button type="button" class="btn btn-default" ng-click="contract.uploadExcel();">引入数据</button>
                                <button type="button" data-dismiss="modal" class="btn btn-default">关闭</button>
                            </div>
                        </div>
                        <div class="row" style="padding-top: 1rem;" ng-if="contract.processFlag && contract.processFlag == '1'">
                            <div class="col-md-12">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" ng-style="{ 'width': contract.processNum + '%' }">
                                        <span class="sr-only">{{contract.processNum}}%</span>
                                    </div>
                                </div>
                                <div>
                                    <span>{{contract.dataNowMsg==""?"数据处理中":contract.dataNowMsg}}...{{contract.dataNowNum}}/{{contract.dataSumNum}}</span>
                                </div>
                            </div>
                        </div>
                        <div style="padding-top: 1rem;">
                            <span class="text-danger">{{contract.exportMessage}}</span>
                            <div class="text-danger" ng-if="contract.errorMsgList&&contract.errorMsgList.length>0" ng-repeat="msg in  contract.errorMsgList">{{msg}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="modal fade bs-modal-lg" id="groupCenterListTask" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-full">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">
                            <span>待集控的任务清单</span>
                        </h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="portlet light bordered">
                                <div class="portlet-body">
                                    <div class="row form-group">
                                        <div class="table-scrollable">
                                            <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                                                <thead>
                                                <tr class="heading">
                                                    <th>合同编号</th>
                                                    <th>订单日期</th>
                                                    <th>客户编号</th>
                                                    <th>客户名称</th>
                                                    <th>客户订单号</th>
                                                    <th>客户型号</th>
                                                    <th>规格(mm)</th>
                                                    <th>单 价</th>
                                                    <th>数 量</th>
                                                    <th>金 额</th>
                                                    <th>平米</th>
                                                    <th>客户预期</th>
                                                    <th>状态</th>
                                                    <th>操作</th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input type="text" class="form-control" ng-model="contract.noSearch">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" ng-model="contract.orderDateSearch">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" ng-model="contract.customerNoSearch">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" ng-model="contract.customerNameSearch">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" ng-model="contract.customerPoSearch">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" ng-model="contract.customerModelSearch">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" ng-model="contract.specSearch">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" ng-model="contract.costFeeSearch">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" ng-model="contract.quantitySearch">
                                                    </td>
                                                    <td></td>
                                                    <td></td>
                                                    <td>
                                                        <input type="text" class="form-control" ng-model="contract.deliveryDateSearch">
                                                    </td>
                                                    <td>
                                                        <input type="text" class="form-control" ng-model="contract.status1Search">
                                                    </td>
                                                    <td>
                                                        <a class="btn btn-xs btn-default"
                                                           style="margin-left: 15px;" ng-click="contract.refreshCenterListTask()">
                                                            <i class="fa fa-refresh"></i> 刷新
                                                        </a>
                                                    </td>
                                                </tr>

                                                </thead>
                                                <tbody ng-repeat="row in contract.waitingGroupCenterList | filter:{
													'no':contract.noSearch,
													'orderDate':contract.orderDateSearch,
													'customerNo':contract.customerNoSearch,
													'customerName':contract.customerNameSearch,
													'customerPo':contract.customerPoSearch,
													'customerModel':contract.customerModelSearch,
													'spec':contract.specSearch,
													'costFee':contract.costFeeSearch,
													'quantity':contract.quantitySearch,
													'deliveryDate':contract.deliveryDateSearch,
													'status1':contract.status1Search
													}
													">
                                                <tr ng-class="{'info': !row.groupCenterId}">
                                                    <td ng-bind="row.no"></td>
                                                    <td ng-bind="row.orderDate"></td>
                                                    <td ng-bind="row.customerNo"></td>
                                                    <td ng-bind="row.customerName"></td>
                                                    <td ng-bind="row.customerPo"></td>
                                                    <td ng-bind="row.customerModel"></td>
                                                    <td ng-bind="row.spec"></td>
                                                    <td ng-bind="row.costFee"></td>
                                                    <td ng-bind="row.quantity"></td>
                                                    <td ng-bind="row.subTotal"></td>
                                                    <td ng-bind="row.orderDeailArea"></td>
                                                    <td ng-bind="row.deliveryDate"></td>
                                                    <td ng-bind="row.status1"></td>
                                                    <td>
                                                        <a class="btn btn-xs btn-default" style="margin-left: 15px;" ng-click="contract.routerGroupCenter(row.recordId)">
                                                            <i class="fa fa-save"></i> 一键集控
                                                        </a>

                                                        <a class="btn btn-xs btn-default" ng-if="row.groupCenterId"
                                                           style="margin-left: 15px;" ng-click="contract.clearGroupCenter(row)">
                                                            <i class="fa fa-save"></i> 清除集控
                                                        </a>

                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="showPrdTaxDescript" class="modal fade" tabindex="-1" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span>提示</span></h4>
                    </div>
                    <div class="modal-body">
                        <p><span ng-bind="contract.message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-default" ng-click="contract.saveprdTaxDescript(1)">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default" ng-click="contract.saveprdTaxDescript(2)">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="modal fade" id="showCraftRecordStatic" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-full">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span class="text-primary">
                        	工程修改记录
                        </span></h4>
                    </div>
                    <div class="row form-group" style="margin-bottom: 15px;" >
                        <div class="col-md-12">
                            <table class="table table-bordered table-condensed">
                                <thead>
                                <tr class="heading">
                                    <th>名称</th>
                                    <th>修改前</th>
                                    <th>修改后</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td ng-bind="contract.showCraftRecord.itemName"></td>
                                    <td ng-bind="contract.showCraftRecord.oldValue"></td>
                                    <td ng-bind="contract.showCraftRecord.newValue"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="uploadInvoiceDiv" class="modal fade bs-example-modal-lg in" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" ng-disabled="contract.edit.upload"  ng-click="contract.confirmUplodFile()"></button> <h4 class="modal-title">添加附件</h4>
            </div>
            <div class="modal-body">
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption font-green-sharp">
                            <i class="icon-cloud-upload font-green-sharp"></i>
                            <span class="caption-subject bold uppercase">上传列表</span>
                        </div>
                        <div class="actions">
                            <div class="portlet-input input-inline input-small">
                                <button class="btn green" ng-if="contract.right.edit"
                                        ngf-select="contract.uploadFiles($files, $invalidFiles)" multiple
                                         ng-disabled="contract.edit.upload" >
                                    <span class="glyphicon glyphicon-cloud-upload"></span>上传原始凭据
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <div class="table-scrollable table-scrollable-borderless">
                            <table class="table table-hover table-light">
                                <thead>
                                <tr class="uppercase">
                                    <th width="50%">文件名</th>
                                    <th>大小</th>
                                    <th>进度</th>
                                    <th class="text-center">状态</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="item in contract.uFiles">
                                    <td><strong>{{ item.name }}</strong></td>
                                    <td>{{ item.size/1024|number:3 }} KB</td>
                                    <td>
                                        <div class="progress progress-sm" style="margin-bottom: 0;">
                                            <div class="progress-bar progress-bar-info" role="progressbar" ng-style="{ 'width': item.progress + '%' }"></div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span ng-show="item.status < 1 || item.status == 200" class="text-success"><i class="glyphicon glyphicon-ok"></i></span>
                                        <span ng-show="item.status == 0" class="text-info"><i class="glyphicon glyphicon-ban-circle"></i></span>
                                        <span ng-show="item.status > 200" class="text-danger"><i class="glyphicon glyphicon-remove"></i></span>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button"  class="btn btn-primary btn-default-width" data-dismiss="modal" ng-disabled="contract.edit.upload" ng-click="contract.confirmUplodFile()"  style="margin-left:45px;">确&nbsp;定</button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="staticInvoiceRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">删除原始凭据</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="contract.delMsg"></p></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.doDelFile()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="maxBatchAreaStatic" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">最大批次面积确认</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="form-horizontal">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label col-md-3"><span class="required">*</span>提交原因：</label>
                                        <div class="col-md-8">
                                            <textarea  class="form-control" placeholder="提交原因" ng-model="contract.maxBatchAreaConfirm.submitReason"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.saveMaxBatchArea()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="viewInvoiceImg" class="modal fade" tabindex="-1"
     data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"
                        aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <img height="100%" width="100%" ng-src="{{contract.imageSrc}}" >
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="modal fade bs-modal-lg" id="outStocksStatic" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-full">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">
                            <span>待送货的订单(厂编：{{contract.craftNoQuery}}--数量:{{contract.contractDetail.quantity}})</span>
                        </h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="portlet light bordered">
                                <div class="actions">
                                    <div class="portlet-input input-inline input-small" ng-if="contract.right.edit">
                                        <button type="button" class="btn primary btn-default-width" ng-click="contract.outStocks()">保存</button>
                                    </div>
                                </div>
                                <div class="portlet-body">
                                    <div class="row form-group">
                                        <div class="table-scrollable">
                                            <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                                                <thead>
                                                <tr class="heading">
                                                    <th>合同编号</th>
                                                    <th>订单日期</th>
                                                    <th>客户订单号</th>
                                                    <th>单价</th>
                                                    <th>库存</th>
                                                </tr>
                                                </thead>
                                                <tbody ng-repeat="row in contract.outStocksList">
                                                <tr ng-class="{'info': true}" ng-if="row.quantity >= 0">
                                                    <td ng-bind="row.contract.no"></td>
                                                    <td ng-bind="row.contract.orderDate"></td>
                                                    <td ng-bind="row.contract.customerPo"></td>
                                                    <td ng-bind="row.madePcsPrice"></td>
                                                    <td ng-bind="row.quantity"></td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="modal fade" id="lossOrdersList" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span class="text-primary">
                        	亏损下单申请列表
                        </span></h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 5px;">
                        <form class="form-horizontal">
                            <div class="portlet light bordered">
                                <div class="actions">
                                    <div class="col-md-12 text-right">
                                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="contract.addLossOrderCustomer()">
                                            <i class="fa fa-adjust font-blue"></i>添加亏损下单客户
                                        </a>
                                    </div>
                                </div>
                                <div class="portlet-body">
                                    <div class="row form-group">
                                        <div class="table">
                                            <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                                                <thead>
                                                <tr class="heading">
                                                    <th>客户名称</th>
                                                    <th>客户编号</th>
                                                    <th>亏损下单结束日期</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr ng-repeat="row in contract.lossOrderRequestList">
                                                    <td>{{row.customerName}}</td>
                                                    <td>{{row.customerNo}}</td>
                                                    <td>{{row.finishLossTime}}</td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="lossOrderCustomer" class="modal fade" tabindex="-1"
             data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"
                                aria-hidden="true"></button>
                        <h4 class="modal-title">添加亏损客户下单</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="form-horizontal">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>客户：</label>
                                        <div class="col-sm-7 col-md-8">
                                            <div class="dropdown">
                                                <ui-select multiple ng-model="contract.customerIdList" theme="bootstrap">
                                                    <ui-select-match placeholder="请选择...">{{$item.name}}</ui-select-match>
                                                    <ui-select-choices repeat="item in contract.customerList | filter: $select.search">
                                                        <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                        <small>
                                                            <span style="color: blue;">客户简称:{{item.shortName}}</span><br>
                                                            <span style="color: blue;">亏损额度:{{item.lossLimit}}</span>
                                                        </small>
                                                    </ui-select-choices>
                                                </ui-select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>亏损下单结束日期：</label>
                                        <div class="col-sm-7 col-md-8">
                                            <div class="dropdown">
                                                <input type="text" class="form-control"
                                                       ng-model="contract.endLossOrderDate"
                                                       data-date-format="yyyy-MM-dd"
                                                       data-date-type="number"
                                                       data-autoclose="1"
                                                       daysOfWeekDisabled="false"
                                                       ng-click="ctrl.checkDate(row)"
                                                       bs-datepicker
                                                       min-date="today" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>原因：</label>
                                        <div class="col-sm-7 col-md-8">
										<textarea  class="form-control" placeholder="原因" ng-model="contract.remark" ></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="contract.runOutBound()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="modal fade bs-modal-lg" id="showonlineOrderPage" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-full">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">
                            <span>线上下单清单</span>
                        </h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="portlet light bordered">
                                <div class="portlet-body">
                                    <div class="row form-group">
                                        <div class="table-scrollable">
                                            <table class="table table-bordered table-hover">
                                                <tr style="text-align: center;">
                                                    <td width="8%">客户名称</td>
                                                    <td width="8%">规格</td>
                                                    <td width="8%">面积</td>
                                                    <td width="8%">金额</td>
                                                    <td width="8%">下单时间</td>
                                                    <td width="8%">订单交期</td>
                                                    <td width="8%">资料文件</td>
                                                    <%--<td width="8%">状态</td>--%>
                                                    <td width="8%">操作</td>
                                                </tr>
                                                <tr ng-repeat ="item in contract.onlineOrderList track by $index">
                                                    <td class="text-center">{{item.customerName}}</td>
                                                    <td class="text-center">
                                                        <a ng-repeat ="row in item.deailList track by $index">
                                                            <span ng-if="row.inputType == 1">{{row.value}}{{row.unitType}}&nbsp;</span>
                                                            <span ng-if="row.inputType == 2">{{row.value1}}*{{row.value2}}&nbsp;</span>
                                                            <span ng-if="row.inputType == 3">{{row.value1}}*{{row.value2}}/{{row.value3}}&nbsp;</span>
                                                            <span ng-if="row.inputType == 4 && row.dictValue">{{row.dictValue}}&nbsp;</span>
                                                            <span ng-if="row.inputType == 5 && row.dictValue">{{row.dictValue}}&nbsp;</span>
                                                            <span ng-if="row.inputType == 6">图片&nbsp;</span>
                                                            <span ng-if="row.inputType == 7 && row.value">大文本{{row.value}}&nbsp;</span>
                                                        </a>
                                                    </td>
                                                    <td style="text-align: center;" ng-bind="item.orderDeailArea"></td>
                                                    <td style="text-align: center;">
                                                        <span ng-if="item.subTotal">
                                                        ￥{{ item.subTotal }}<span ng-if="item.exchangeRate">&emsp;/&emsp;
                                                        <span ng-if="item.exchangeType == 'USD'">$</span>
                                                        <span ng-if="item.exchangeType == 'HKD'">HK$</span>
                                                         {{(item.subTotal / item.exchangeRate).toFixed(4)}}</span>
                                                        </span>
                                                    </td>
                                                    <td style="text-align: center;" ng-bind="item.createdDate"></td>
                                                    <td style="text-align: center;" ng-bind="item.deliveryDateStr "></td>
<%--                                                    <td style="text-align: center;">
                                                        <span ng-if="item.status == 1">待确认</span>
                                                        <span ng-if="item.status == 2">待审批</span>
                                                        <span ng-if="item.status == 3">待付款</span>
                                                        <span ng-if="item.status == 4">生产中</span>
                                                    </td>--%>
                                                    <td style="text-align: center;">
                                                        <div ng-repeat ="row in item.attaList track by $index">
                                                            文件{{$index + 1}}:&nbsp;<a href="javascript:void(0)" ng-click="contract.showImg(row,row.orgFileName)">{{row.orgFileName}}</a>
                                                        </div>
                                                    </td>
                                                    <td style="text-align: center;">
                                                        <a href="javascript:void(0)" class="btn btn-xs btn-default ng-scope" ng-click="contract.senContract(item)">
                                                            确定
                                                        </a>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="viewFileImg" class="modal fade" tabindex="-1"
     data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"
                        aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <img height="100%" width="100%" ng-src="{{contract.imageSrc}}" >
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="jxDeferencePriceStation" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="form-horizontal">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        PCS单价 &nbsp;&nbsp; <span>{{contract.jxDeferencePrice > 0?'增加':'减少'}}&nbsp;:&nbsp;{{contract.jxDeferencePrice}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="jxDeferenceMoneyStation" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="form-horizontal">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        总金额 &nbsp;&nbsp; <span>{{contract.jxDeferenceMoney > 0?'增加':'减少'}}&nbsp;:&nbsp;{{contract.jxDeferenceMoney}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="modal fade" id="showAddDeliveryData" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static">
            <div class="modal-dialog modal-full">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span class="text-primary">
                        	多分批交货设置(客户:{{contract.contract.customer.name}},客户型号:{{contract.setAddressDetail.contractCraftList.customerModel}},数量:{{contract.setAddressDetail.quantity | number:0}})
                        </span></h4>
                    </div>
                    <div class="modal-footer">
                        <button type="button"  class="btn btn-success btn-default-width" ng-click="contract.addDetailDelivery()" >
                            <i class="fa fa-plus"></i>  添加
                        </button>
                        <button type="button"  class="btn blue" ng-click="contract.saveDelivery()" >
                            <i class="fa fa-save"></i>保存</button>
                    </div>
                    <!-- 多月收款页面  start-->
                    <div class="row form-group" style="margin-bottom: 15px;" >
                        <div class="col-md-12">
                            <table class="table table-bordered table-condensed">
                                <thead>
                                <tr class="heading">
                                    <th width="10%"><span class="required text-danger">*</span>交货日期</th>
                                    <th width="10%"><span class="required text-danger">*</span>数量</th>
                                    <th width="10%">交货面积</th>
                                    <th width="15%" >操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="row in contract.deliveryList">
                                    <td style='vertical-align: middle;'>
                                        <input ng-model="row.deliveryDate"
                                               placeholder="请选择交货日期" type="text" class="form-control"
                                               data-date-format="yyyy-MM-dd" required
                                               data-date-type="number"
                                               data-max-date="30.12.2099" data-autoclose="1"
                                               daysOfWeekDisabled="false" bs-datepicker
                                               min-date="today">
                                    </td>
                                    <td style='vertical-align: middle;'>
                                        <input required class="form-control" type="text"
                                               placeholder="请输入数量" ng-blur="contract.doDeliveryArea()"
                                               ng-model="row.quantity"
                                               style="text-align: right;"
                                        />
                                    </td>
                                    <!-- 面积-->
                                    <td style='vertical-align: middle;' ng-bind="row.orderArea"></td>
                                    <td style='vertical-align: middle;' >
                                        <button type="button" class="btn btn-danger btn-xs" ng-click=" contract.deleteDetailDelivery($index)">
                                            <i class="fa fa-trash-o"></i> 删除
                                        </button>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- 多月收款页面 end -->
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="priceVerification" class="modal fade" tabindex="-1"
             data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span>提示</span></h4>
                    </div>
                    <div class="modal-body">
                        <p><span id="contMessage" ng-bind="contract.message"></span></p>
                    </div>
                    <form class="form-horizontal" id="addcraft_form" name="addcraft_form">
                        <div class="modal-footer">
                            <button type="button" class="btn blue"
                                    ng-click="contract.submitContractDetail(addcraft_form)"
                                    ng-disabled="addcraft_form.$invalid">保&nbsp;存
                            </button>
                            <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span>提示</span></h4>
                    </div>
                    <div class="modal-body">
                        <p><span id="contMessage" ng-bind="contract.message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
