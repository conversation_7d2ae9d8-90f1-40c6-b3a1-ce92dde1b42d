package com.kyb.pcberp.modules.contract.vo;

import java.math.BigDecimal;

import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.contract.entity.GroupCenterBill;

public class GroupCenterBillVo extends GroupCenterBill
{

    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    
    @ExcelField(title = "送货单号", align = 2, sort = 60)
    public String getXsOutBoundNo()
    {
        return super.getXsOutBoundNo();
    }
    
    @ExcelField(title = "厂编", align = 2, sort = 70)
    public String getXsCraftNo()
    {
        return super.getXsCraftNo();
    }
    
    @ExcelField(title = "送货日期", align = 2, sort = 30)
    public String getOperateDateStr()
    {
        return super.getOperateDateStr();
    }
    
    @ExcelField(title = "客户", align = 2, sort = 10)
    public String getXsCustName()
    {
        return super.getXsCustName();
    }
    
    @ExcelField(title = "金额", align = 2, sort = 120)
    public BigDecimal getXsSubTotalStr()
    {
        return super.getXsSubTotalStr();
    }
    
    @ExcelField(title = "客户型号", align = 2, sort = 50)
    public String getXsCustomerModel()
    {
        return super.getXsCustomerModel();
    }
    
    @Override
    @ExcelField(title = "对账数量", align = 2, sort = 130)
    public BigDecimal getCheckQuantity()
    {
        return super.getCheckQuantity();
    }
    
//    @ExcelField(title = "客户编号", align = 2, sort = 20)
    public String getXsCustNo(){
        return super.getXsCustNo();
    }
    
    @ExcelField(title = "客户订单号", align = 2, sort = 40)
    public String getXsConCustomerPo(){
        return super.getXsConCustomerPo();
    }
    
    @ExcelField(title = "PCS单价", align = 2, sort = 80)
    public BigDecimal getXsPricePrice(){
        return super.getXsPricePrice();
    }
    
    @ExcelField(title = "送货数量", align = 2, sort = 90)
    public BigDecimal getXsStockOutBoundStocks(){
        return super.getXsStockOutBoundStocks();
    }
    
    @ExcelField(title = "平米单价", align = 2, sort = 100)
    public BigDecimal getAreaPrice(){
        return super.getAreaPrice();
    }
    
    @ExcelField(title = "送货平米", align = 2, sort = 110)
    public BigDecimal getArea(){
        return super.getArea();
    }
    
    @Override
    @ExcelField(title = "转下期数量", align = 2, sort = 140)
    public BigDecimal getNextNum()
    {
        return super.getNextNum();
    }
    
    @Override
    @ExcelField(title = "转下期金额", align = 2, sort = 150)
    public BigDecimal getNextMoney()
    {
        return super.getNextMoney();
    }

}
