<%@ page contentType="text/html;charset=UTF-8" %>

<div ng-intro-options="listOptions" ng-intro-method="helpList" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="infoOptions" ng-intro-method="helpInfo" ng-intro-autostart="shouldAutoStart"></div>


<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">系统管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="sys.usersofemployees">员工管理</a>
        </li>
    </ul>
    <div class="page-toolbar">
    	<button class="btn default btn-fit-height pull-right" ng-click="help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>
</div>
<!-- END 导航-->
<tabset class="tabset-margin-top">
    <!-- BEGIN 用户列表 -->
    <tab heading="员工列表" active="ctrl.tabs.viewForm.active">
    	<div id="listStep6" class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">姓名：</label>

                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="ctrl.query.employeeName.value"
                                           disable-valid-styling="true"
                                           disable-invalid-styling="true"/>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">员工编码 ：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="ctrl.query.employeeNo.value"
                                           disable-valid-styling="true"
                                           disable-invalid-styling="true"/>
                                </div>
                            </div>
                        </div>
				
                        <div class="col-md-6 col-lg-4">
			                 <div class="form-group">
			                     <label class="col-sm-3 col-md-4 control-label">部门：</label>
			                     <div class="col-sm-7 col-md-8">
			                         <ui-select ng-model="ctrl.query.deptName.value" theme="bootstrap" >
			                             <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
			                             <ui-select-choices repeat="item in ctrl.deptsList | filter: $select.search">
			                                 <div ng-bind-html="item.name | highlight: $select.search"></div>
			                             </ui-select-choices>
			                         </ui-select>
			                     </div>
			                 </div>
		                </div>
		                
		                </div>
		                
		                <div class="row">
		                 <div class="col-md-6 col-lg-4">
			                 <div class="form-group">
			                     <label class="col-sm-3 col-md-4 control-label">组织：</label>
			                     <div class="col-sm-7 col-md-8">
			                         <ui-select ng-model="ctrl.query.organizationName.value" theme="bootstrap" >
			                             <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
			                             <ui-select-choices repeat="item in ctrl.organizationsList | filter: $select.search">
			                                 <div ng-bind-html="item.name | highlight: $select.search"></div>
			                             </ui-select-choices>
			                         </ui-select>
			                     </div>
			                 </div>
		                </div>
                        
                         <div class="col-md-6 col-lg-4">
								<div class="form-group">
									<label class="col-sm-3 col-md-4 control-label">状态：</label>
									<div class="col-sm-7 col-md-8">
										 <select  class="form-control" 
                                    		 ng-model="ctrl.query.status.value"
                                             ng-options="gen.id as gen.name for gen in ctrl.status"
                                             disable-auto-validate="true">
	                                    </select>
									</div>
								</div>
							</div>	
	                        
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="ctrl.doQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
  
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">员工列表</div>
                <div id="listStep7" class="actions">
                <div style="float: left;">
                	<form name="form1" enctype="multipart/form-data" id="form1" 
                		ng-submit="ctrl.uploadExcel();" >  
            			<table>  
             				<tr style="float: left;">  
                				<td>上传文件:　</td>  
                				<td> <input id="upfile" type="file" name="upfile"></td>  
             				</tr>  
            				<tr style="float: left;margin-left: -10%">  
                				<td><input type="submit" value="提交" onclick="true"></td>  
             				</tr>  
            			</table>    
        			</form>
                </div>
<!--                 	<div class="portlet-input input-inline input-small" ng-if="ctrl.company.recordId==14"> -->
<!-- 	                    <form class="inline" ng-form-commit name="printForm" -->
<!-- 								action="a/sys/usersofemployees/print" method="get" -->
<!-- 								target="hidden_frame"> -->
<!-- 								<input type="submit" id="recordId" ng-show="false" name="recordId" ng-model="row.recordId" />  -->
<!-- 								<button type="submit" class="btn green btn-default-width" ng-click="ctrl.printRawmaterial(printForm)"><i class="fa fa-print font-blue-hoki"></i> 入职申请表</button> -->
<!-- 								<iframe name="hidden_frame" id="hidden_frame" -->
<!-- 									style="display: none"></iframe> -->
<!-- 						</form> -->
<!-- 					</div> -->
           
                    <div class="portlet-input input-inline input-small" style="float: right;">
                        <button ng-if="ctrl.right.edit" type="button" class="btn green btn-default-width" ng-click="ctrl.addUser()"><i class="fa fa-plus"></i> 添加员工</button>
                    </div>		
                     <div class="portlet-input input-inline input-small">
                        <form action="a/sys/usersofemployees/export" method="get" enctype="multipart/form-data" target="hidden_frame">
                            <input type="text" ng-show="false" name="no" value="{{ctrl.query.employeeNo.value}}" />
							<input type="text" ng-show="false" name="name" value="{{ctrl.query.employeeName.value}}" />
                          	<input type="text" ng-show="false" name="dept.recordId" value="{{ctrl.query.deptName.value.recordId}}" />
                         	<input type="text" ng-show="false" name="status" value="{{ctrl.query.status.value}}" />
                            <input type="text" ng-show="false"  name="queryAll" value="{{ctrl.queryAll}}"/>
                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出员工记录</button>
                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
                         </form>
                    </div>
                </div>
            </div>
            <div class="portlet-body">
            	<div id="listStep1" class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
                        <tr class="heading">
                       		<th>照&nbsp;片</th>
                        	<th>员工编码</th>
	                        <th>姓名</th>
	                        <th>部门</th>
	                        <th>组织</th>
	                        <th>职务</th>
	                        <th>在职状态</th>
	                        <th>联系电话</th>
	                        <th>身份证号</th>
	                        <th>紧急联系人</th>
	                        <th>入职日期</th>
	                        <th>操作</th>
	                    </tr>
	                    </thead>
	                    <tbody>
	                    <tr ng-repeat="row in ctrl.page.data.list" ng-dblclick="ctrl.ctrlShowDetail($index)">
	                    	<td>
	                    		<a ng-click="ctrl.viewStamp($index)" ng-if="row.showLogoPath">
	                    			<img  alt="" ng-src="{{row.showLogoPath}}" height="30px" width="30px">
		                    	</a>
	                    	</td>
	                     	<td><a href="javascript:void(0)" ng-click="ctrl.ctrlShowDetail($index)" ng-bind="row.no"></a></td>
	                        <td ng-bind="row.name"></td>
	                        <td ng-bind="row.dept.name"></td>
	                        <td ng-bind="row.organization.name"></td>
	                        <td ng-bind="row.position"></td>
	                        <td >
	                        	<span ng-if="row.status == 1" class="label label-sm label-success">在职 </span>
	                        	<span ng-if="row.status == 2" class="label label-sm label-danger">离职</span>
	                        </td>
	                        <td ng-bind="row.phone" ></td>
	                        <td ng-bind="row.idCard" ></td>
	                        <td ng-bind="row.contactNameOne" ></td>
	                        <td ng-bind="row.hiredDate" ></td>
	                        <td>
	                            <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="ctrl.loginUser.recordId !== row.recordId && row.status !== 3 && ctrl.right.edit" ng-click="ctrl.delEmployee($index)"><i class="fa fa-times font-red"></i> 删除</a>
<!-- 								<span ng-if="ctrl.company.recordId==14"> -->
<!-- 									<form class="inline" ng-form-commit name="printForm" action="a/sys/user/print" method="get" target="hidden_frame"> -->
<!-- 	                                    <input type="text" id="recordId" ng-show="false" name="recordId" ng-model="row.recordId"/> -->
<!-- 	                                    	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.printUser(printForm,row.recordId)"><i class="fa fa-print font-blue-hoki"></i> 打&nbsp;印	</a> -->
<!-- 	                                    <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe> -->
<!--                                 	</form> -->
<!-- 								</span> -->
								<!-- <a title="打印员工" href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.download(row.recordId)"><i class="fa fa-print font-blue-hoki"></i> 打印</a> -->
	                        </td>
	                    </tr>
	                    </tbody>
	                </table>
	            </div>
                <div class="row page-margin-top">
                    <div id="listStep5" class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select class="form-control inline" style="margin-top:8px; width:100px;"
                                disable-valid-styling="true"
                                disable-invalid-styling="true"
                                ng-model="ctrl.page.pageSize"
                                ng-change="ctrl.pageSizeChange()"
                                ng-options="pageSizeOption for pageSizeOption in ctrl.page.pageSizeOptions">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{ctrl.page.data.startCount}} / {{ctrl.page.data.endCount}} 条，共 {{ctrl.page.data.count}} 条</span>
                    </div>
                    <div id="listStep4" class="col-md-12 col-lg-6">
                        <paging
                                class="pull-right"
                                page="ctrl.page.data.pageNo"
                                page-size="ctrl.page.data.pageSize"
                                total="ctrl.page.data.count"
                                adjacent="1"
                                dots="..."
                                scroll-top="false"
                                hide-if-empty="false"
                                ul-class="pagination"
                                active-class="active"
                                disabled-class="disabled"
                                show-prev-next="true"
                                paging-action="ctrl.doPage(page, pageSize, total)">
                        </paging>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- END 员工列表 -->
    <!-- BEGIN 员工编辑 -->
    
    <tab active="ctrl.tabs.editForm.active" ng-show="ctrl.tabs.editForm.show">
        <tab-heading>
            员工详情 <i class="fa fa-times set-cursor-pointer" ng-click="ctrl.hideEditForm()"></i>
        </tab-heading>
        <div class="portlet light bordered protlet-edit-form">
            <div class="portlet-title">
                	<div class="caption font-blue-hoki">基本资料{{ctrl.editTitle}}</div>
           	</div>
            <div class="portlet-body">
                <form id="infoStep1" class="form-horizontal" name="userForm" ng-init="ctrl.setFormScope(this)"
                      novalidate="novalidate" ng-submit="ctrl.submitMod(ctrl.picFile,userForm);" ng-submit-force="true">
                <div class="col-md-6 col-lg-4">
	                <div class="form-group">
			                <label class="col-sm-3 control-label">员工照片：</label>
			                <fieldset>
			                    <div class="col-sm-7 fileinput" data-provides="fileinput">
			                        <div class="fileinput-preview thumbnail" data-trigger="fileinput" style="width: 200px; height: 150px; line-height: 150px;">
			                            <img id="logoPathImg" height="{{imageHeight}}" width="{{imageWidth}}" ng-src="{{imageSrc}}" >
			                        </div>
			                        <div>
										<span class="btn default btn-file">
											<span class="fileinput-new">选择文件</span>
				                            <input type="file" ngf-select ng-model="ctrl.picFile" name="file" accept="image/*" 
				                            	onchange="angular.element(this).scope().changeLogoImg(this)" disable-auto-validate="true">       
										</span>
			                        </div>
			                    </div>
			                </fieldset>
	            	 </div>
                 </div>
                 <div class="col-md-6 col-lg-4">   
	                    <div class="form-group">
	                        <label class="col-sm-3 control-label"><span class="required">*</span>编号：</label>
	                        <div class="col-sm-7">
	                            <input class="form-control" type="text"  
	                                   ng-model="ctrl.employee.no"  readonly="readonly"/>
	                        </div>
	                    </div>
                    </div>
            		<div class="col-md-6 col-lg-4">   
	                    <div class="form-group">
	                        <label class="col-sm-3 control-label"><span class="required">*</span>姓名：</label>
	                        <div class="col-sm-7">
	                            <input class="form-control" type="text" placeholder="请输入员工姓名" ng-blur="ctrl.snameBlur()"
	                                   ng-model="ctrl.employee.name" ng-maxlength="32" required/>
	                        </div>
	                    </div>
                    </div>
                    <div class="col-md-6 col-lg-4">       
	                    <div class="form-group">
	                        <label class="col-sm-3 control-label">合同开始时间：</label>
	                        <div class="col-sm-7">
	                            <input type="text" class="form-control" 
	                                   ng-model="ctrl.employee.contractStartDate"
	                                   data-date-format="yyyy年MM月dd日"
	                                   data-date-type="number"
	                                   data-min-date="02/10/1901"
	                                   data-max-date="today"
	                                   data-autoclose="1"
	                                   daysOfWeekDisabled="false"
	                                   ng-blur="ctrl.validateContractStartDate()"
	                                   bs-datepicker  />
	                        </div>
	                    </div>
                    </div>
					<div class="col-md-6 col-lg-4">       
	                    <div class="form-group">
	                        <label class="col-sm-3 control-label">合同结束时间：</label>
	                        <div class="col-sm-7">
	                            <input type="text" class="form-control"
	                                   ng-model="ctrl.employee.contractEndDate"
	                                   data-date-format="yyyy年MM月dd日"
	                                   data-date-type="number"
	                                   data-min-date="02/10/1901"
	                                   data-autoclose="1"
	                                   daysOfWeekDisabled="false"
	                                   ng-blur="ctrl.validateContractEndDate()"
	                                   bs-datepicker  />
	                        </div>
	                    </div>
                    </div>
<%--		            <div class="col-md-6 col-lg-4">
		                 <div class="form-group">
		                     <label class="col-sm-3 control-label"><span class="required">*</span>职务：</label>
		                     <div class="col-sm-7">
		                         <input class="form-control" type="text" placeholder="请输入职务" required
		                                ng-model="ctrl.employee.position" ng-maxlength="32"/>
		                     </div>
		                 </div>
               		 </div>--%>
          		     <div class="col-md-6 col-lg-4"> 
	                    <div class="form-group">
	                        <label class="col-sm-3 control-label">本人号码：</label>
	                        <div class="col-sm-7">
	                            <input class="form-control" type="text" placeholder="请输入用户手机号码"
	                                   ng-model="ctrl.employee.phone" ng-maxlength="20" ng-blur="ctrl.validatePhone()"/>
	                        </div>
	                     </div>
                   	  </div>
                   	  <div class="col-md-6 col-lg-4">
			                 <div class="form-group">
			                     <label class="col-sm-3 control-label"><span class="required">*</span>部门：</label>
			                     <div class="col-sm-7">
			                         <ui-select ng-model="ctrl.employee.dept" theme="bootstrap" ng-change="ctrl.selectDept()" required>
			                             <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
			                             <ui-select-choices repeat="item in ctrl.depts | filter: $select.search">
			                                 <div ng-bind-html="item.name | highlight: $select.search"></div>
			                             </ui-select-choices>
			                         </ui-select>
			                     </div>
			                 </div>
		                </div>
		                <div class="col-md-6 col-lg-4" ng-if="ctrl.organizationList && ctrl.organizationList.length > 0">
			                 <div class="form-group">
			                     <label class="col-sm-3 control-label"><span class="required">*</span>组织：</label>
			                     <div class="col-sm-7">
			                         <ui-select ng-model="ctrl.employee.organization" theme="bootstrap" required>
			                             <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
			                             <ui-select-choices repeat="item in ctrl.organizationList | filter: $select.search">
			                                 <div ng-bind-html="item.name | highlight: $select.search"></div>
			                             </ui-select-choices>
			                         </ui-select>
			                     </div>
			                 </div>
		                </div>
		                
		                 <div class="col-md-6 col-lg-4">   
		                    <div class="form-group">
		                        <label class="col-sm-3 control-label">户口所在地：</label>
		                        <div class="col-sm-7">
		                            <input class="form-control" type="text" ng-model="ctrl.employee.householdAddress"/>
		                        </div>
		                    </div>
                    	</div>
                    	 <div class="col-md-6 col-lg-4">   
		                    <div class="form-group">
		                        <label class="col-sm-3 control-label">现住地址：</label>
		                        <div class="col-sm-7">
		                            <input class="form-control" type="text" ng-model="ctrl.employee.nowAddress"/>
		                        </div>
		                    </div>
                    	</div>
                    	
		                <div class="row">
			                <div class="col-md-6 col-lg-4">       
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">入职日期：</label>
			                        <div class="col-sm-7">
			                            <input type="text" class="form-control"
			                                   ng-model="ctrl.employee.hiredDate"
			                                   data-date-format="yyyy年MM月dd日"
			                                   data-date-type="number"
			                                   data-min-date="02/10/1901"
			                                   data-max-date="today"
			                                   data-autoclose="1"
			                                   daysOfWeekDisabled="false"
			                                   ng-blur="ctrl.selectProbationPeriod()"
			                                   bs-datepicker  />
			                        </div>
			                    </div>
	                    	</div>
	       	                <div class="col-md-6 col-lg-4">
				                 <div class="form-group">
				                     <label class="col-sm-3 control-label">试用期限：</label>
				                     <div class="col-sm-7">
				                         <select class="form-control" ng-model="ctrl.employee.probationPeriod" placeholder="请选择" ng-change="ctrl.selectProbationPeriod()">
			                         		<option ng-selected="ctrl.employee.probationPeriod == 1" value="1">3个月</option>
	                                        <option ng-selected="ctrl.employee.probationPeriod == 2" value="2">6个月</option>
				                         </select>
				                     </div>
				                 </div>
	                		</div>
		                    <div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">转正日期：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" ng-model="ctrl.employee.regularWorkerDate" disabled="disabled"/>
			                        </div>
			                    </div>
	                    	</div>
	                    	 <div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">员工属性：</label>
			                        <div class="col-sm-7">
				                         <select class="form-control" ng-model="ctrl.employee.workType" placeholder="请选择" ng-change="ctrl.selectProbationPeriod()">
			                         		<option ng-selected="ctrl.employee.workType != 1" value="0">&nbsp;&nbsp;</option>
	                                        <option ng-selected="ctrl.employee.workType == 1" value="1">内部兼职人员</option>
				                         </select>
				                     </div>
			                    </div>
	                    	</div>
                    	</div>
                    	<div class="row">
	   	                    <div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">身份证号：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请输入员工身份证" 
			                                  ng-model="ctrl.employee.idCard" ng-blur="ctrl.validateIdCard()" ng-maxlength="18" />
			                        </div>
			                    </div>
	                    	</div>
	                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">出生日期：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" ng-model="ctrl.employee.birthday" disabled="disabled" />
			                        </div>
			                    </div>
	                    	</div>
	                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">年龄：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" ng-model="ctrl.employee.age" disabled="disabled"/>
			                        </div>
			                    </div>
	                    	</div>
                    	</div>
                    	<div class="row">
	                    	<div class="col-md-6 col-lg-4">
				                 <div class="form-group">
				                     <label class="col-sm-3 control-label">性别：</label>
				                     <div class="col-sm-7">
				                         <select class="form-control" ng-model="ctrl.employee.sex" placeholder="请选择">
			                         		<option ng-selected="ctrl.employee.sex == 1" value="1">男</option>
	                                        <option ng-selected="ctrl.employee.sex == 2" value="2">女</option>
				                         </select>
				                     </div>
				                 </div>
	                		</div>
	                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">民族：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" ng-model="ctrl.employee.nation"/>
			                        </div>
			                    </div>
	                    	</div>
	                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">籍贯：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" ng-model="ctrl.employee.hometown"/>
			                        </div>
			                    </div>
	                    	</div>
                    	</div>
                    	<div class="row">
                    	
                    	   <div class="col-md-6 col-lg-4" ng-if="!ctrl.isAdd">
			                 <div class="form-group">
			                     <label class="col-sm-3 control-label">用户：</label>
			                     <div class="col-sm-7">
			                         <ui-select ng-model="ctrl.employee.user" theme="bootstrap" ng-change="ctrl.selectUser()" >
			                             <ui-select-match placeholder="请选择...">{{$select.selected.userName}}</ui-select-match>
			                             <ui-select-choices repeat="item in ctrl.userList | filter: $select.search">
			                                 <div ng-bind-html="item.userName | highlight: $select.search"></div>
			                             </ui-select-choices>
			                         </ui-select>
			                     </div>
			                 </div>
		               		</div>

							<div class="col-md-6 col-lg-4" ng-if="ctrl.isAdd" >
								<div class="form-group">
									<label class="col-sm-3 control-label">用户：</label>
									<div class="col-sm-7">
										<ui-select ng-model="ctrl.employee.user" theme="bootstrap" ng-change="ctrl.selectUser()">
											<ui-select-match placeholder="请选择...">{{$select.selected.userName}}</ui-select-match>
											<ui-select-choices repeat="item in ctrl.userList | filter: $select.search">
												<div ng-bind-html="item.userName | highlight: $select.search"></div>
											</ui-select-choices>
										</ui-select>
									</div>
								</div>
							</div>
		                
                    		<div class="col-md-6 col-lg-4">
								<div class="form-group">
									<label class="col-sm-3 control-label">状态：</label>
									<div class="col-sm-7">
										 <select  class="form-control" ng-model="ctrl.employee.status" placeholder="请选择">
                                            <option ng-selected="ctrl.employee.status == 1" value="1">在职</option>
	                                        <option ng-selected="ctrl.employee.status == 2" value="2">离职</option>
	                                    </select>
									</div>
								</div>
							</div>
							
                    		<div class="col-md-6 col-lg-4">
				                 <div class="form-group">
				                     <label class="col-sm-3 control-label">最高学历：</label>
				                     <div class="col-sm-7">
				                         <select class="form-control" ng-model="ctrl.employee.levelOfEducation" placeholder="请选择">
			                         		<option ng-selected="ctrl.employee.levelOfEducation == 1" value="1">博士</option>
	                                        <option ng-selected="ctrl.employee.levelOfEducation == 2" value="2">硕士</option>
	                                        <option ng-selected="ctrl.employee.levelOfEducation == 3" value="3">本科</option>
	                                        <option ng-selected="ctrl.employee.levelOfEducation == 4" value="4">大专</option>
	                                        <option ng-selected="ctrl.employee.levelOfEducation == 5" value="5">高中/中专</option>
				                         </select>
				                     </div>
				                 </div>
                			</div>
                    	</div>
                    	<div class="row">
                    		<div class="col-md-6 col-lg-4">
				                 <div class="form-group">
				                     <label class="col-sm-4 control-label">最高学历教育模式：</label>
				                     <div class="col-sm-7">
				                         <select class="form-control" ng-model="ctrl.employee.educationModelHeight" placeholder="请选择" >
			                         		<option ng-selected="ctrl.employee.educationModelHeight == 1" value="1">全日制教育</option>
	                                        <option ng-selected="ctrl.employee.educationModelHeight == 2" value="2">自学考试</option>
	                                        <option ng-selected="ctrl.employee.educationModelHeight == 3" value="3">成人教育</option>
	                                        <option ng-selected="ctrl.employee.educationModelHeight == 4" value="4">远程教育</option>
	                                        <option ng-selected="ctrl.employee.educationModelHeight == 5" value="5">夜大</option>
				                         </select>
				                     </div>
				                 </div>
                			</div>
                    	
                    		<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-4 control-label">第一学历教育模式：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写第一学历教育模式" ng-model="ctrl.employee.firstEducationModel" />
			                        </div>
			                    </div>
	                    	</div>
	                    	
                    		<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">第一学历：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写第一学历" ng-model="ctrl.employee.firstEducation"/>
			                        </div>
			                    </div>
                    		</div>
                    	</div>
                    	<div class="row">
                    		<div class="col-md-6 col-lg-4">       
			                    <div class="form-group">
			                        <label class="col-sm-4 control-label">最高学历毕业时间：</label>
			                        <div class="col-sm-7">
			                            <input type="text" class="form-control"
			                                   ng-model="ctrl.employee.graduationTimeHeight"
			                                   data-date-format="yyyy年MM月dd日"
			                                   data-date-type="number"
			                                   data-min-date="02/10/1901"
			                                   data-max-date="today"
			                                   data-autoclose="1"
			                                   daysOfWeekDisabled="false"
			                                   bs-datepicker  />
			                        </div>
			                    </div>
                    		</div>
                    		<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-4 control-label">最高学历毕业院校：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="填写最高学历毕业院校" 
			                            	ng-model="ctrl.employee.graduationSchoolHeight" />
			                        </div>
			                    </div>
                    		</div>
                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">最高学历专业：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写最高学历专业" 
			                            	ng-model="ctrl.employee.professionalHeight" />
			                        </div>
			                    </div>
	                    	</div>
                    	</div>
                    	
                    	<div class="row">
                    		<div class="col-md-6 col-lg-4">       
			                    <div class="form-group">
			                        <label class="col-sm-4 control-label">第一学历毕业时间：</label>
			                        <div class="col-sm-7">
			                            <input type="text" class="form-control"
			                                   ng-model="ctrl.employee.firstGraduationTime"
			                                   data-date-format="yyyy年MM月dd日"
			                                   data-date-type="number"
			                                   data-min-date="02/10/1901"
			                                   data-max-date="today"
			                                   data-autoclose="1"
			                                   daysOfWeekDisabled="false"
			                                   bs-datepicker  />
			                        </div>
			                    </div>
                    		</div>
                    		<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-4 control-label">第一学历毕业院校：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="填写第一学历毕业院校" ng-model="ctrl.employee.firstGraduationSchool"/>
			                        </div>
			                    </div>
                    		</div>
                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">第一学历专业：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写第一学历专业" ng-model="ctrl.employee.firstProfessional"/>
			                        </div>
			                    </div>
	                    	</div>
                    	</div>
                    	
                    	<div class="row">
                    		<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">紧急联系人1：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写紧急联系人1" ng-model="ctrl.employee.contactNameOne"/>
			                        </div>
			                    </div>
                    		</div>
                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-4 control-label">联系人号码1：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写联系人号码1" ng-model="ctrl.employee.contactPhoneOne"/>
			                        </div>
			                    </div>
	                    	</div>
                    	</div>
                    	
                    	<div class="row">
                    		<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">紧急联系人2：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写紧急联系人2" ng-model="ctrl.employee.contactNameTwo"/>
			                        </div>
			                    </div>
                    		</div>
                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-4 control-label">联系人号码2：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写联系人号码2" ng-model="ctrl.employee.contactPhoneTwo"/>
			                        </div>
			                    </div>
	                    	</div>
                    	</div>
                    	
                    	<div class="row">
	                    	<div class="col-md-6 col-lg-4">
				                 <div class="form-group">
				                     <label class="col-sm-3 control-label">婚姻状况：</label>
				                     <div class="col-sm-7">
				                         <select class="form-control" ng-model="ctrl.employee.marriage" placeholder="请选择">
			                         		<option ng-selected="ctrl.employee.marriage == 1" value="1">未婚</option>
	                                        <option ng-selected="ctrl.employee.marriage == 2" value="2">已婚未育</option>
	                                        <option ng-selected="ctrl.employee.marriage == 3" value="3">已婚已育</option>
	                                        <option ng-selected="ctrl.employee.marriage == 4" value="3">离异</option>
				                         </select>
				                     </div>
				                 </div>
	                		</div>
	                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-4 control-label">政治面貌：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写政治面貌" ng-model="ctrl.employee.politicalStatus"/>
			                        </div>
			                    </div>
	                    	</div>
	                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">体重：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写体重（kg）" ng-model="ctrl.employee.weight"/>
			                        </div>
			                    </div>
	                    	</div>
                    	</div>
                    	
                    	<div class="row">
                    		<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">外语水平：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写外语水平" ng-model="ctrl.employee.languageLevel"/>
			                        </div>
			                    </div>
	                    	</div>
	                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-4 control-label">计算机能力：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写计算机能力" ng-model="ctrl.employee.computingPower"/>
			                        </div>
			                    </div>
	                    	</div>
	                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">身高：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写身高" ng-model="ctrl.employee.height"/>
			                        </div>
			                    </div>
	                    	</div>
                    	</div>
                    	
                    	
                    	<div class="row">
	                    	<div class="col-md-6 col-lg-4">
				                 <div class="form-group">
				                     <label class="col-sm-3 control-label">应聘状况：</label>
				                     <div class="col-sm-7">
				                         <select class="form-control" ng-model="ctrl.employee.applicationStatus" placeholder="请选择">
			                         		<option ng-selected="ctrl.employee.applicationStatus == 1" value="1">在职</option>
	                                        <option ng-selected="ctrl.employee.applicationStatus == 2" value="2">失业</option>
	                                        <option ng-selected="ctrl.employee.applicationStatus == 3" value="3">退伍</option>
	                                        <option ng-selected="ctrl.employee.applicationStatus == 4" value="4">退休</option>
	                                        <option ng-selected="ctrl.employee.applicationStatus == 5" value="5">其它</option>
				                         </select>
				                     </div>
				                 </div>
	                		</div>
	                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-4 control-label">配偶姓名及身份证：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写配偶姓名及身份证" ng-model="ctrl.employee.spousalInformation"/>
			                        </div>
			                    </div>
	                    	</div>
	                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">拥有证书：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写拥有证书" ng-model="ctrl.employee.certificate"/>
			                        </div>
			                    </div>
	                    	</div>
                    	</div>
                    	
                    	<div class="row">
                    		<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">Email：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写Email" ng-model="ctrl.employee.email"/>
			                        </div>
			                    </div>
	                    	</div>
	                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-4 control-label">居住地电话：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写居住地电话" ng-model="ctrl.employee.mobile"/>
			                        </div>
			                    </div>
	                    	</div>
	                    	
	                    	<div class="col-md-6 col-lg-4">   
			                    <div class="form-group">
			                        <label class="col-sm-3 control-label">招聘渠道：</label>
			                        <div class="col-sm-7">
			                            <input class="form-control" type="text" placeholder="请填写招聘渠道" ng-model="ctrl.employee.recruitmentChannel"/>
			                        </div>
			                    </div>
	                    	</div>
                    	</div>
                    	
                    	<div class="row" ng-if="!ctrl.isAdd">
							<div class="col-md-6 col-lg-4">
								<div class="form-group">
									<label class="col-sm-3 control-label">选择职位：</label>
									<div class="col-sm-7">
										<ui-select ng-model="ctrl.position.employeePosition" theme="bootstrap"  ng-change="ctrl.selectGroupChange(ctrl.position.employeePosition)">
											<ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
											<ui-select-choices repeat="item in ctrl.empPositionList | filter: $select.search">
												<div ng-bind-html="item.name | highlight: $select.search"></div>
											</ui-select-choices>

										</ui-select>
									</div>
								</div>
							</div>

                    		<div class="col-md-6 col-lg-4">
			                 <div class="form-group">
			                     <label class="col-sm-3 control-label">选择结构：</label>
			                     <div class="col-sm-7">
			                         <ui-select sortable="true" multiple=true ng-model="ctrl.group" theme="bootstrap"
												close-on-select="false" sortable="true" >
			                             <ui-select-match placeholder="请选择...">{{$item.name}}</ui-select-match>
			                             <ui-select-choices repeat="item in ctrl.groupList | filter: $select.search">
			                                 <div ng-bind-html="item.name | highlight: $select.search"></div>
			                             </ui-select-choices>
			                         </ui-select>
			                     </div>
			                 </div>
		               		</div>
		               		


							<div class="col-md-6 col-lg-4">
								<div class="form-group">

									<div class="col-sm-7">
										<button ng-if="ctrl.right.edit" type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.savePosition()">绑定权限</button>
									</div>
								</div>
							</div>

							<div class="portlet light bordered">
								<div class="portlet-body">
									<table style="margin-top: 5px;" class="table table-striped table-bordered table-condensed table-advance table-hover">
										<thead>
										<tr class="heading">
											<th>职位</th>
											<th>组织</th>
											<th>任职开始时间</th>
											<th>任职结束时间</th>
											<th>操作</th>
										</tr>
										</thead>
										<tbody>
										<tr ng-repeat="position in ctrl.positionList">
											<td ng-bind="position.positionName"></td>
											<td ng-bind="position.name"></td>
											<td>
												<input type="text" class="form-control" ng-model="position.startTime" data-date-format="yyyy-MM-dd" data-date-type="number"
												data-min-date="02/10/1901" data-max-date="today" data-autoclose="1" daysOfWeekDisabled="false"
												ng-blur="ctrl.validateStartDate(position)"
												 bs-datepicker  />
											</td>
											<td>
												<input type="text" class="form-control" ng-model="position.endTime" data-date-format="yyyy-MM-dd" data-date-type="number"
											    data-min-date="02/10/1901" data-max-date="today" data-autoclose="1" daysOfWeekDisabled="false"
											    ng-blur="ctrl.validateStartDate(position)"
											    bs-datepicker  />
											</td>
											<td>
												<a class="btn btn-xs btn-default" href="javascript:void(0)" ng-click="ctrl.deletePosition(position.recordId)"><i class="fa fa-times font-red"></i> 删&nbsp;除</a>
												<a class="btn btn-xs btn-default" style="margin-left: 15px;" ng-click="ctrl.updatePosition(position)"><i class="fa fa-save"></i> 保存</a>
											</td>
										</tr>
										</tbody>
									</table>
								</div>

							</div>
<%--							<div class="col-md-6 col-lg-4" style="width: 100%">
								<div class="form-group">
									<div class="col-sm-7" >
										<label class="col-sm-3 control-label" ng-repeat="item in ctrl.positionList"> {{item.positionName}} --- {{item.name}}
											<a href="#">
											<span class="glyphicon glyphicon-minus-sign" ng-click="ctrl.deletePosition(item.recordId)"></span>
											</a>
										</label>
									</div>
								</div>
							</div>--%>

                    	</div>
                    		
                
	                <div class="form-group">
	                    <div class="col-sm-offset-4 col-sm-10" ng-if="ctrl.right.edit">
	                        <button type="submit"  class="btn btn-primary btn-default-width" style="margin-left:15px;"><i class="fa fa-save"></i> {{ctrl.editTitle}}</button>
	                    </div>
	                </div>
              </form>
           </div>
       </div>
   </tab>
    <!-- END 员工编辑 -->


</tabset>

<div class="row">
    <div class="col-md-12">
        <div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">提示</h4>
                    </div>
                    <div class="modal-body">
                        <p><span ng-bind="ctrl.message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="staticRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">删除员工</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="col-sm-1 text-center">
							<i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i>
						</div>
						<div class="col-sm-11"><p ng-bind="ctrl.delMsg"></p></div>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.doDelUser()">删除</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


<div class="row">
	<div class="col-md-12">
		<div id="empNo" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">提示</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="col-sm-1 text-center">
							<i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i>
						</div>
						<div class="col-sm-11"><p ng-bind="ctrl.empNos"></p></div>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue" ng-click="ctrl.empClick()">确认</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


<div id="viewStampImg" class="modal fade" tabindex="-1"
     data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"
                        aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <img id="imgCord" height="100%" width="100%" ng-src="{{ctrl.stampSrc}}" > 
            </div>
        </div>
    </div>
</div>
