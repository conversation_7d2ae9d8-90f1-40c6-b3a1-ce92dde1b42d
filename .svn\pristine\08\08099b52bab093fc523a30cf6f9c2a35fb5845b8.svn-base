/*! kybyasuo XX 2015-11-13 */
if(!AmCharts)var AmCharts={themes:{},maps:{},inheriting:{},charts:[],onReadyArray:[],useUTC:!1,updateRate:40,uid:0,lang:{},translations:{},mapTranslations:{},windows:{},initHandlers:[]};AmCharts.Class=function(a){var b=function(){arguments[0]!==AmCharts.inheriting&&(this.events={},this.construct.apply(this,arguments))};a.inherits?(b.prototype=new a.inherits(AmCharts.inheriting),b.base=a.inherits.prototype,delete a.inherits):(b.prototype.createEvents=function(){for(var a=0,b=arguments.length;b>a;a++)this.events[arguments[a]]=[]},b.prototype.listenTo=function(a,b,c){this.removeListener(a,b,c),a.events[b].push({handler:c,scope:this})},b.prototype.addListener=function(a,b,c){this.removeListener(this,a,b),this.events[a].push({handler:b,scope:c})},b.prototype.removeListener=function(a,b,c){if(a&&a.events)for(a=a.events[b],b=a.length-1;b>=0;b--)a[b].handler===c&&a.splice(b,1)},b.prototype.fire=function(a,b){for(var c=this.events[a],d=0,e=c.length;e>d;d++){var f=c[d];f.handler.call(f.scope,b)}});for(var c in a)b.prototype[c]=a[c];return b},AmCharts.addChart=function(a){AmCharts.charts.push(a)},AmCharts.removeChart=function(a){for(var b=AmCharts.charts,c=b.length-1;c>=0;c--)b[c]==a&&b.splice(c,1)},AmCharts.isModern=!0,AmCharts.getIEVersion=function(){var a=0;if("Microsoft Internet Explorer"==navigator.appName){var b=navigator.userAgent,c=/MSIE ([0-9]{1,}[.0-9]{0,})/;null!=c.exec(b)&&(a=parseFloat(RegExp.$1))}else"Netscape"==navigator.appName&&(b=navigator.userAgent,c=/Trident\/.*rv:([0-9]{1,}[.0-9]{0,})/,null!=c.exec(b)&&(a=parseFloat(RegExp.$1)));return a},AmCharts.applyLang=function(a,b){var c=AmCharts.translations;b.dayNames=AmCharts.dayNames,b.shortDayNames=AmCharts.shortDayNames,b.monthNames=AmCharts.monthNames,b.shortMonthNames=AmCharts.shortMonthNames,c&&(c=c[a])&&(AmCharts.lang=c,c.monthNames&&(b.dayNames=c.dayNames,b.shortDayNames=c.shortDayNames,b.monthNames=c.monthNames,b.shortMonthNames=c.shortMonthNames))},AmCharts.IEversion=AmCharts.getIEVersion(),9>AmCharts.IEversion&&0<AmCharts.IEversion&&(AmCharts.isModern=!1,AmCharts.isIE=!0),AmCharts.dx=0,AmCharts.dy=0,(document.addEventListener||window.opera)&&(AmCharts.isNN=!0,AmCharts.isIE=!1,AmCharts.dx=.5,AmCharts.dy=.5),document.attachEvent&&(AmCharts.isNN=!1,AmCharts.isIE=!0,AmCharts.isModern||(AmCharts.dx=0,AmCharts.dy=0)),window.chrome&&(AmCharts.chrome=!0),AmCharts.handleResize=function(){for(var a=AmCharts.charts,b=0;b<a.length;b++){var c=a[b];c&&c.div&&c.handleResize()}},AmCharts.handleMouseUp=function(a){for(var b=AmCharts.charts,c=0;c<b.length;c++){var d=b[c];d&&d.handleReleaseOutside(a)}},AmCharts.handleMouseMove=function(a){for(var b=AmCharts.charts,c=0;c<b.length;c++){var d=b[c];d&&d.handleMouseMove(a)}},AmCharts.resetMouseOver=function(){for(var a=AmCharts.charts,b=0;b<a.length;b++){var c=a[b];c&&(c.mouseIsOver=!1)}},AmCharts.ready=function(a){AmCharts.onReadyArray.push(a)},AmCharts.handleLoad=function(){AmCharts.isReady=!0;for(var a=AmCharts.onReadyArray,b=0;b<a.length;b++){var c=a[b];isNaN(AmCharts.processDelay)?c():setTimeout(c,AmCharts.processDelay*b)}},AmCharts.addInitHandler=function(a,b){AmCharts.initHandlers.push({method:a,types:b})},AmCharts.callInitHandler=function(a){var b=AmCharts.initHandlers;if(AmCharts.initHandlers)for(var c=0;c<b.length;c++){var d=b[c];d.types&&-1!=d.types.indexOf(a.type)&&d.method(a)}},AmCharts.getUniqueId=function(){return AmCharts.uid++,"AmChartsEl-"+AmCharts.uid},AmCharts.isNN&&(document.addEventListener("mousemove",AmCharts.handleMouseMove,!0),window.addEventListener("resize",AmCharts.handleResize,!0),document.addEventListener("mouseup",AmCharts.handleMouseUp,!0),window.addEventListener("load",AmCharts.handleLoad,!0)),AmCharts.isIE&&(document.attachEvent("onmousemove",AmCharts.handleMouseMove),window.attachEvent("onresize",AmCharts.handleResize),document.attachEvent("onmouseup",AmCharts.handleMouseUp),window.attachEvent("onload",AmCharts.handleLoad)),AmCharts.clear=function(){var a=AmCharts.charts;if(a)for(var b=0;b<a.length;b++)a[b].clear();AmCharts.charts=null,AmCharts.isNN&&(document.removeEventListener("mousemove",AmCharts.handleMouseMove,!0),window.removeEventListener("resize",AmCharts.handleResize,!0),document.removeEventListener("mouseup",AmCharts.handleMouseUp,!0),window.removeEventListener("load",AmCharts.handleLoad,!0)),AmCharts.isIE&&(document.detachEvent("onmousemove",AmCharts.handleMouseMove),window.detachEvent("onresize",AmCharts.handleResize),document.detachEvent("onmouseup",AmCharts.handleMouseUp),window.detachEvent("onload",AmCharts.handleLoad))},AmCharts.makeChart=function(a,b,c){var d=b.type,e=b.theme;AmCharts.isString(e)&&(e=AmCharts.themes[e],b.theme=e);var f;switch(d){case"serial":f=new AmCharts.AmSerialChart(e);break;case"xy":f=new AmCharts.AmXYChart(e);break;case"pie":f=new AmCharts.AmPieChart(e);break;case"radar":f=new AmCharts.AmRadarChart(e);break;case"gauge":f=new AmCharts.AmAngularGauge(e);break;case"funnel":f=new AmCharts.AmFunnelChart(e);break;case"map":f=new AmCharts.AmMap(e);break;case"stock":f=new AmCharts.AmStockChart(e)}return AmCharts.extend(f,b),AmCharts.isReady?isNaN(c)?f.write(a):setTimeout(function(){AmCharts.realWrite(f,a)},c):AmCharts.ready(function(){isNaN(c)?f.write(a):setTimeout(function(){AmCharts.realWrite(f,a)},c)}),f},AmCharts.realWrite=function(a,b){a.write(b)},AmCharts.toBoolean=function(a,b){if(void 0===a)return b;switch(String(a).toLowerCase()){case"true":case"yes":case"1":return!0;case"false":case"no":case"0":case null:return!1;default:return Boolean(a)}},AmCharts.removeFromArray=function(a,b){var c;for(c=a.length-1;c>=0;c--)a[c]==b&&a.splice(c,1)},AmCharts.getDecimals=function(a){var b=0;return isNaN(a)||(a=String(a),-1!=a.indexOf("e-")?b=Number(a.split("-")[1]):-1!=a.indexOf(".")&&(b=a.split(".")[1].length)),b},AmCharts.wrappedText=function(a,b,c,d,e,f,g,h,i){var j=AmCharts.text(a,b,c,d,e,f,g),k="\n";if(AmCharts.isModern||(k="<br>"),i>10)return j;if(j){var l=j.getBBox();if(l.width>h){j.remove();for(var j=[],m=0;-1<(index=b.indexOf(" ",m));)j.push(index),m=index+1;for(var n,o=Math.round(b.length/2),p=1e3,m=0;m<j.length;m++){var q=Math.abs(j[m]-o);p>q&&(n=j[m],p=q)}if(isNaN(n)){if(h=Math.ceil(l.width/h),0==i)for(m=1;h>m;m++)n=Math.round(b.length/h*m),b=b.substr(0,n)+k+b.substr(n);return AmCharts.text(a,b,c,d,e,f,g)}return b=b.substr(0,n)+k+b.substr(n+1),AmCharts.wrappedText(a,b,c,d,e,f,g,h,i+1)}return j}},AmCharts.getStyle=function(a,b){var c="";return document.defaultView&&document.defaultView.getComputedStyle?c=document.defaultView.getComputedStyle(a,"").getPropertyValue(b):a.currentStyle&&(b=b.replace(/\-(\w)/g,function(a,b){return b.toUpperCase()}),c=a.currentStyle[b]),c},AmCharts.removePx=function(a){return void 0!=a?Number(a.substring(0,a.length-2)):void 0},AmCharts.getURL=function(a,b){if(a)if("_self"!=b&&b)if("_top"==b&&window.top)window.top.location.href=a;else if("_parent"==b&&window.parent)window.parent.location.href=a;else if("_blank"==b)window.open(a);else{var c=document.getElementsByName(b)[0];c?c.src=a:(c=AmCharts.windows[b])&&c.opener&&!c.opener.closed?c.location.href=a:AmCharts.windows[b]=window.open(a)}else window.location.href=a},AmCharts.ifArray=function(a){return a&&0<a.length?!0:!1},AmCharts.callMethod=function(a,b){var c;for(c=0;c<b.length;c++){var d=b[c];if(d){d[a]&&d[a]();var e=d.length;if(e>0){var f;for(f=0;e>f;f++){var g=d[f];g&&g[a]&&g[a]()}}}}},AmCharts.toNumber=function(a){return"number"==typeof a?a:Number(String(a).replace(/[^0-9\-.]+/g,""))},AmCharts.toColor=function(a){if(""!==a&&void 0!==a)if(-1!=a.indexOf(",")){a=a.split(",");var b;for(b=0;b<a.length;b++){var c=a[b].substring(a[b].length-6,a[b].length);a[b]="#"+c}}else a=a.substring(a.length-6,a.length),a="#"+a;return a},AmCharts.toCoordinate=function(a,b,c){var d;return void 0!==a&&(a=String(a),c&&b>c&&(b=c),d=Number(a),-1!=a.indexOf("!")&&(d=b-Number(a.substr(1))),-1!=a.indexOf("%")&&(d=b*Number(a.substr(0,a.length-1))/100)),d},AmCharts.fitToBounds=function(a,b,c){return b>a&&(a=b),a>c&&(a=c),a},AmCharts.isDefined=function(a){return void 0===a?!1:!0},AmCharts.stripNumbers=function(a){return a.replace(/[0-9]+/g,"")},AmCharts.roundTo=function(a,b){if(0>b)return a;var c=Math.pow(10,b);return Math.round(a*c)/c},AmCharts.toFixed=function(a,b){var c=String(Math.round(a*Math.pow(10,b)));if(b>0){var d=c.length;if(b>d){var e;for(e=0;b-d>e;e++)c="0"+c}return d=c.substring(0,c.length-b),""===d&&(d=0),d+"."+c.substring(c.length-b,c.length)}return String(c)},AmCharts.formatDuration=function(a,b,c,d,e,f){var g=AmCharts.intervals,h=f.decimalSeparator;if(a>=g[b].contains){var i=a-Math.floor(a/g[b].contains)*g[b].contains;return"ss"==b&&(i=AmCharts.formatNumber(i,f),1==i.split(h)[0].length&&(i="0"+i)),("mm"==b||"hh"==b)&&10>i&&(i="0"+i),c=i+""+d[b]+c,a=Math.floor(a/g[b].contains),b=g[b].nextInterval,AmCharts.formatDuration(a,b,c,d,e,f)}if("ss"==b&&(a=AmCharts.formatNumber(a,f),1==a.split(h)[0].length&&(a="0"+a)),("mm"==b||"hh"==b)&&10>a&&(a="0"+a),c=a+""+d[b]+c,g[e].count>g[b].count)for(a=g[b].count;a<g[e].count;a++)b=g[b].nextInterval,"ss"==b||"mm"==b||"hh"==b?c="00"+d[b]+c:"DD"==b&&(c="0"+d[b]+c);return":"==c.charAt(c.length-1)&&(c=c.substring(0,c.length-1)),c},AmCharts.formatNumber=function(a,b,c,d,e){a=AmCharts.roundTo(a,b.precision),isNaN(c)&&(c=b.precision);var f=b.decimalSeparator;b=b.thousandsSeparator;var g;g=0>a?"-":"",a=Math.abs(a);var h=String(a),i=!1;-1!=h.indexOf("e")&&(i=!0),c>=0&&!i&&(h=AmCharts.toFixed(a,c));var j="";if(i)j=h;else{var k,h=h.split("."),i=String(h[0]);for(k=i.length;k>=0;k-=3)j=k!=i.length?0!==k?i.substring(k-3,k)+b+j:i.substring(k-3,k)+j:i.substring(k-3,k);void 0!==h[1]&&(j=j+f+h[1]),void 0!==c&&c>0&&"0"!=j&&(j=AmCharts.addZeroes(j,f,c))}return j=g+j,""===g&&!0===d&&0!==a&&(j="+"+j),!0===e&&(j+="%"),j},AmCharts.addZeroes=function(a,b,c){return a=a.split(b),void 0===a[1]&&c>0&&(a[1]="0"),a[1].length<c?(a[1]+="0",AmCharts.addZeroes(a[0]+b+a[1],b,c)):void 0!==a[1]?a[0]+b+a[1]:a[0]},AmCharts.scientificToNormal=function(a){var b;a=String(a).split("e");var c;if("-"==a[1].substr(0,1)){for(b="0.",c=0;c<Math.abs(Number(a[1]))-1;c++)b+="0";b+=a[0].split(".").join("")}else{var d=0;for(b=a[0].split("."),b[1]&&(d=b[1].length),b=a[0].split(".").join(""),c=0;c<Math.abs(Number(a[1]))-d;c++)b+="0"}return b},AmCharts.toScientific=function(a,b){if(0===a)return"0";var c=Math.floor(Math.log(Math.abs(a))*Math.LOG10E);return Math.pow(10,c),mantissa=String(mantissa).split(".").join(b),String(mantissa)+"e"+c},AmCharts.randomColor=function(){return"#"+("00000"+(16777216*Math.random()<<0).toString(16)).substr(-6)},AmCharts.hitTest=function(a,b,c){var d=!1,e=a.x,f=a.x+a.width,g=a.y,h=a.y+a.height,i=AmCharts.isInRectangle;return d||(d=i(e,g,b)),d||(d=i(e,h,b)),d||(d=i(f,g,b)),d||(d=i(f,h,b)),d||!0===c||(d=AmCharts.hitTest(b,a,!0)),d},AmCharts.isInRectangle=function(a,b,c){return a>=c.x-5&&a<=c.x+c.width+5&&b>=c.y-5&&b<=c.y+c.height+5?!0:!1},AmCharts.isPercents=function(a){return-1!=String(a).indexOf("%")?!0:void 0},AmCharts.findPosX=function(a){var b=a,c=a.offsetLeft;if(a.offsetParent){for(;a=a.offsetParent;)c+=a.offsetLeft;for(;(b=b.parentNode)&&b!=document.body;)c-=b.scrollLeft||0}return c},AmCharts.findPosY=function(a){var b=a,c=a.offsetTop;if(a.offsetParent){for(;a=a.offsetParent;)c+=a.offsetTop;for(;(b=b.parentNode)&&b!=document.body;)c-=b.scrollTop||0}return c},AmCharts.findIfFixed=function(a){if(a.offsetParent)for(;a=a.offsetParent;)if("fixed"==AmCharts.getStyle(a,"position"))return!0;return!1},AmCharts.findIfAuto=function(a){return a.style&&"auto"==AmCharts.getStyle(a,"overflow")?!0:a.parentNode?AmCharts.findIfAuto(a.parentNode):!1},AmCharts.findScrollLeft=function(a,b){return a.scrollLeft&&(b+=a.scrollLeft),a.parentNode?AmCharts.findScrollLeft(a.parentNode,b):b},AmCharts.findScrollTop=function(a,b){return a.scrollTop&&(b+=a.scrollTop),a.parentNode?AmCharts.findScrollTop(a.parentNode,b):b},AmCharts.formatValue=function(a,b,c,d,e,f,g,h){if(b){void 0===e&&(e="");var i;for(i=0;i<c.length;i++){var j=c[i],k=b[j];void 0!==k&&(k=f?AmCharts.addPrefix(k,h,g,d):AmCharts.formatNumber(k,d),a=a.replace(new RegExp("\\[\\["+e+j+"\\]\\]","g"),k))}}return a},AmCharts.formatDataContextValue=function(a,b){if(a){var c,d=a.match(/\[\[.*?\]\]/g);for(c=0;c<d.length;c++){var e=d[c],e=e.substr(2,e.length-4);void 0!==b[e]&&(a=a.replace(new RegExp("\\[\\["+e+"\\]\\]","g"),b[e]))}}return a},AmCharts.massReplace=function(a,b){for(var c in b)if(b.hasOwnProperty(c)){var d=b[c];void 0===d&&(d=""),a=a.replace(c,d)}return a},AmCharts.cleanFromEmpty=function(a){return a.replace(/\[\[[^\]]*\]\]/g,"")},AmCharts.addPrefix=function(a,b,c,d,e){var f,g,h,i=AmCharts.formatNumber(a,d),j="";if(0===a)return"0";if(0>a&&(j="-"),a=Math.abs(a),a>1){for(f=b.length-1;f>-1;f--)if(a>=b[f].number&&(g=a/b[f].number,h=Number(d.precision),1>h&&(h=1),c=AmCharts.roundTo(g,h),h=AmCharts.formatNumber(c,{precision:-1,decimalSeparator:d.decimalSeparator,thousandsSeparator:d.thousandsSeparator}),!e||g==c)){i=j+""+h+b[f].prefix;break}}else for(f=0;f<c.length;f++)if(a<=c[f].number){g=a/c[f].number,h=Math.abs(Math.round(Math.log(g)*Math.LOG10E)),g=AmCharts.roundTo(g,h),i=j+""+g+c[f].prefix;break}return i},AmCharts.remove=function(a){a&&a.remove()},AmCharts.recommended=function(){var a="js";return document.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#BasicStructure","1.1")||swfobject&&swfobject.hasFlashPlayerVersion("8")&&(a="flash"),a},AmCharts.getEffect=function(a){return">"==a&&(a="easeOutSine"),"<"==a&&(a="easeInSine"),"elastic"==a&&(a="easeOutElastic"),a},AmCharts.getObjById=function(a,b){var c,d;for(d=0;d<a.length;d++){var e=a[d];e.id==b&&(c=e)}return c},AmCharts.applyTheme=function(a,b,c){b||(b=AmCharts.theme),b&&b[c]&&AmCharts.extend(a,b[c])},AmCharts.isString=function(a){return"string"==typeof a?!0:!1},AmCharts.extend=function(a,b,c){for(var d in b)c?a.hasOwnProperty(d)||(a[d]=b[d]):a[d]=b[d];return a},AmCharts.copyProperties=function(a,b){for(var c in a)a.hasOwnProperty(c)&&"events"!=c&&void 0!==a[c]&&"function"!=typeof a[c]&&"cname"!=c&&(b[c]=a[c])},AmCharts.processObject=function(a,b,c){return!1==a instanceof b&&(a=AmCharts.extend(new b(c),a)),a},AmCharts.fixNewLines=function(a){var b=RegExp("\\n","g");return a&&(a=a.replace(b,"<br />")),a},AmCharts.fixBrakes=function(a){if(AmCharts.isModern){var b=RegExp("<br>","g");a&&(a=a.replace(b,"\n"))}else a=AmCharts.fixNewLines(a);return a},AmCharts.deleteObject=function(a,b){if(a&&((void 0===b||null===b)&&(b=20),0!==b))if("[object Array]"===Object.prototype.toString.call(a))for(var c=0;c<a.length;c++)AmCharts.deleteObject(a[c],b-1),a[c]=null;else if(a&&!a.tagName)try{for(c in a)a[c]&&("object"==typeof a[c]&&AmCharts.deleteObject(a[c],b-1),"function"!=typeof a[c]&&(a[c]=null))}catch(d){}},AmCharts.bounce=function(a,b,c,d,e){return(b/=e)<1/2.75?7.5625*d*b*b+c:2/2.75>b?d*(7.5625*(b-=1.5/2.75)*b+.75)+c:2.5/2.75>b?d*(7.5625*(b-=2.25/2.75)*b+.9375)+c:d*(7.5625*(b-=2.625/2.75)*b+.984375)+c},AmCharts.easeInSine=function(a,b,c,d,e){return-d*Math.cos(b/e*(Math.PI/2))+d+c},AmCharts.easeOutSine=function(a,b,c,d,e){return d*Math.sin(b/e*(Math.PI/2))+c},AmCharts.easeOutElastic=function(a,b,c,d,e){a=1.70158;var f=0,g=d;return 0===b?c:1==(b/=e)?c+d:(f||(f=.3*e),g<Math.abs(d)?(g=d,a=f/4):a=f/(2*Math.PI)*Math.asin(d/g),g*Math.pow(2,-10*b)*Math.sin(2*(b*e-a)*Math.PI/f)+d+c)},AmCharts.AxisBase=AmCharts.Class({construct:function(a){this.createEvents("clickItem","rollOverItem","rollOutItem"),this.viY=this.viX=this.y=this.x=this.dy=this.dx=0,this.axisThickness=1,this.axisColor="#000000",this.axisAlpha=1,this.gridCount=this.tickLength=5,this.gridAlpha=.15,this.gridThickness=1,this.gridColor="#000000",this.dashLength=0,this.labelFrequency=1,this.showLastLabel=this.showFirstLabel=!0,this.fillColor="#FFFFFF",this.fillAlpha=0,this.labelsEnabled=!0,this.labelRotation=0,this.autoGridCount=!0,this.valueRollOverColor="#CC0000",this.offset=0,this.guides=[],this.visible=!0,this.counter=0,this.guides=[],this.ignoreAxisWidth=this.inside=!1,this.minHorizontalGap=75,this.minVerticalGap=35,this.titleBold=!0,this.minorGridEnabled=!1,this.minorGridAlpha=.07,this.autoWrap=!1,this.titleAlign="middle",this.labelOffset=0,AmCharts.applyTheme(this,a,"AxisBase")},zoom:function(a,b){this.start=a,this.end=b,this.dataChanged=!0,this.draw()},fixAxisPosition:function(){var a=this.position;"H"==this.orientation?("left"==a&&(a="bottom"),"right"==a&&(a="top")):("bottom"==a&&(a="left"),"top"==a&&(a="right")),this.position=a},draw:function(){var a=this.chart;this.allLabels=[],this.counter=0,this.destroy(),this.fixAxisPosition(),this.labels=[];var b=a.container,c=b.set();a.gridSet.push(c),this.set=c,b=b.set(),a.axesLabelsSet.push(b),this.labelsSet=b,this.axisLine=new this.axisRenderer(this),this.autoGridCount?("V"==this.orientation?(a=this.height/this.minVerticalGap,3>a&&(a=3)):a=this.width/this.minHorizontalGap,this.gridCountR=Math.max(a,1)):this.gridCountR=this.gridCount,this.axisWidth=this.axisLine.axisWidth,this.addTitle()},setOrientation:function(a){this.orientation=a?"H":"V"},addTitle:function(){var a=this.title;if(a){var b=this.chart,c=this.titleColor;void 0===c&&(c=b.color);var d=this.titleFontSize;isNaN(d)&&(d=b.fontSize+1),this.titleLabel=AmCharts.text(b.container,a,c,b.fontFamily,d,this.titleAlign,this.titleBold)}},positionTitle:function(){var a=this.titleLabel;if(a){var b,c,d=this.labelsSet,e={};0<d.length()?e=d.getBBox():(e.x=0,e.y=0,e.width=this.viW,e.height=this.viH),d.push(a);var d=e.x,f=e.y;AmCharts.VML&&(this.rotate?d-=this.x:f-=this.y);var g=e.width,e=e.height,h=this.viW,i=this.viH,j=0,k=a.getBBox().height/2,l=this.inside,m=this.titleAlign;switch(this.position){case"top":b="left"==m?-1:"right"==m?h:h/2,c=f-10-k;break;case"bottom":b="left"==m?-1:"right"==m?h:h/2,c=f+e+10+k;break;case"left":b=d-10-k,l&&(b-=5),c="left"==m?i+1:"right"==m?-1:i/2,j=-90;break;case"right":b=d+g+10+k-3,l&&(b+=7),c="left"==m?i+2:"right"==m?-2:i/2,j=-90}this.marginsChanged?(a.translate(b,c),this.tx=b,this.ty=c):a.translate(this.tx,this.ty),this.marginsChanged=!1,0!==j&&a.rotate(j)}},pushAxisItem:function(a,b){var c=this,d=a.graphics();0<d.length()&&(b?c.labelsSet.push(d):c.set.push(d)),(d=a.getLabel())&&(this.labelsSet.push(d),d.click(function(b){c.handleMouse(b,a,"clickItem")}).mouseover(function(b){c.handleMouse(b,a,"rollOverItem")}).mouseout(function(b){c.handleMouse(b,a,"rollOutItem")}))},handleMouse:function(a,b,c){this.fire(c,{type:c,value:b.value,serialDataItem:b.serialDataItem,axis:this,target:b.label,chart:this.chart,event:a})},addGuide:function(a){for(var b=this.guides,c=!1,d=0;d<b.length;d++)b[d]==a&&(c=!0);c||b.push(a)},removeGuide:function(a){var b,c=this.guides;for(b=0;b<c.length;b++)c[b]==a&&c.splice(b,1)},handleGuideOver:function(a){clearTimeout(this.chart.hoverInt);var b=a.graphics.getBBox(),c=b.x+b.width/2,b=b.y+b.height/2,d=a.fillColor;void 0===d&&(d=a.lineColor),this.chart.showBalloon(a.balloonText,d,!0,c,b)},handleGuideOut:function(a){this.chart.hideBalloon()},addEventListeners:function(a,b){var c=this;a.mouseover(function(){c.handleGuideOver(b)}),a.mouseout(function(){c.handleGuideOut(b)})},getBBox:function(){var a=this.labelsSet.getBBox();return AmCharts.VML||(a={x:a.x+this.x,y:a.y+this.y,width:a.width,height:a.height}),a},destroy:function(){AmCharts.remove(this.set),AmCharts.remove(this.labelsSet);var a=this.axisLine;a&&AmCharts.remove(a.set),AmCharts.remove(this.grid0)}}),AmCharts.ValueAxis=AmCharts.Class({inherits:AmCharts.AxisBase,construct:function(a){this.cname="ValueAxis",this.createEvents("axisChanged","logarithmicAxisFailed","axisSelfZoomed","axisZoomed"),AmCharts.ValueAxis.base.construct.call(this,a),this.dataChanged=!0,this.stackType="none",this.position="left",this.unitPosition="right",this.recalculateToPercents=this.includeHidden=this.includeGuidesInMinMax=this.integersOnly=!1,this.durationUnits={DD:"d. ",hh:":",mm:":",ss:""},this.scrollbar=!1,this.baseValue=0,this.radarCategoriesEnabled=!0,this.gridType="polygons",this.useScientificNotation=!1,this.axisTitleOffset=10,this.minMaxMultiplier=1,this.logGridLimit=2,AmCharts.applyTheme(this,a,this.cname)},updateData:function(){0>=this.gridCountR&&(this.gridCountR=1),this.totals=[],this.data=this.chart.chartData;var a=this.chart;"xy"!=a.type&&(this.stackGraphs("smoothedLine"),this.stackGraphs("line"),this.stackGraphs("column"),this.stackGraphs("step")),this.recalculateToPercents&&this.recalculate(),this.synchronizationMultiplier&&this.synchronizeWith?(AmCharts.isString(this.synchronizeWith)&&(this.synchronizeWith=a.getValueAxisById(this.synchronizeWith)),this.synchronizeWith&&(this.synchronizeWithAxis(this.synchronizeWith),this.foundGraphs=!0)):(this.foundGraphs=!1,this.getMinMax())},draw:function(){AmCharts.ValueAxis.base.draw.call(this);var a=this.chart,b=this.set;if("duration"==this.type&&(this.duration="ss"),!0===this.dataChanged&&(this.updateData(),this.dataChanged=!1),this.logarithmic&&(0>=this.getMin(0,this.data.length-1)||0>=this.minimum))this.fire("logarithmicAxisFailed",{type:"logarithmicAxisFailed",chart:a});else{this.grid0=null;var c,d,e=a.dx,f=a.dy,g=!1,h=this.logarithmic;if(isNaN(this.min)||isNaN(this.max)||!this.foundGraphs||1/0==this.min||-(1/0)==this.max)g=!0;else{var i,j=this.labelFrequency,k=this.showFirstLabel,l=this.showLastLabel,m=1,n=0,o=Math.round((this.max-this.min)/this.step)+1;!0===h?(i=Math.log(this.max)*Math.LOG10E-Math.log(this.minReal)*Math.LOG10E,this.stepWidth=this.axisWidth/i,i>this.logGridLimit&&(o=Math.ceil(Math.log(this.max)*Math.LOG10E)+1,n=Math.round(Math.log(this.minReal)*Math.LOG10E),o>this.gridCountR&&(m=Math.ceil(o/this.gridCountR)))):this.stepWidth=this.axisWidth/(this.max-this.min);var p=0;1>this.step&&-1<this.step&&(p=AmCharts.getDecimals(this.step)),this.integersOnly&&(p=0),p>this.maxDecCount&&(p=this.maxDecCount);var q=this.precision;isNaN(q)||(p=q),this.max=AmCharts.roundTo(this.max,this.maxDecCount),this.min=AmCharts.roundTo(this.min,this.maxDecCount),d={},d.precision=p,d.decimalSeparator=a.nf.decimalSeparator,d.thousandsSeparator=a.nf.thousandsSeparator,this.numberFormatter=d;var r,s=this.guides;if(c=s.length,c>0){var t=this.fillAlpha;for(d=this.fillAlpha=0;c>d;d++){var u=s[d],v=0/0,w=u.above;isNaN(u.toValue)||(v=this.getCoordinate(u.toValue),r=new this.axisItemRenderer(this,v,"",!0,0/0,0/0,u),this.pushAxisItem(r,w));var x=0/0;isNaN(u.value)||(x=this.getCoordinate(u.value),r=new this.axisItemRenderer(this,x,u.label,!0,0/0,(v-x)/2,u),this.pushAxisItem(r,w)),isNaN(v-x)||(r=new this.guideFillRenderer(this,x,v,u),this.pushAxisItem(r,w),r=r.graphics(),u.graphics=r,u.balloonText&&this.addEventListeners(r,u))}this.fillAlpha=t}for(this.exponential=!1,d=n;o>d;d+=m)s=AmCharts.roundTo(this.step*d+this.min,p),-1!=String(s).indexOf("e")&&(this.exponential=!0,String(s).split("e"));this.duration&&(this.maxInterval=AmCharts.getMaxInterval(this.max,this.duration));var y,p=this.step,s=this.minorGridAlpha;for(this.minorGridEnabled&&(y=this.getMinorGridStep(p,this.stepWidth*p)),d=n;o>d;d+=m)if(n=p*d+this.min,h&&this.max-this.min>5*this.min&&(n-=this.min),n=AmCharts.roundTo(n,this.maxDecCount+1),!(this.integersOnly&&Math.round(n)!=n||!isNaN(q)&&Number(AmCharts.toFixed(n,q))!=n)){if(!0===h&&(0===n&&(n=this.minReal),i>this.logGridLimit&&(n=Math.pow(10,d))),r=this.formatValue(n,!1,d),Math.round(d/j)!=d/j&&(r=void 0),(0===d&&!k||d==o-1&&!l)&&(r=" "),c=this.getCoordinate(n),r=new this.axisItemRenderer(this,c,r,void 0,void 0,void 0,void 0,this.boldLabels),this.pushAxisItem(r),n==this.baseValue&&"radar"!=a.type){var z,A,u=this.viW,w=this.viH;r=this.viX,t=this.viY,"H"==this.orientation?c>=0&&u+1>=c&&(z=[c,c,c+e],A=[w,0,f]):c>=0&&w+1>=c&&(z=[0,u,u+e],A=[c,c,c+f]),z&&(c=AmCharts.fitToBounds(2*this.gridAlpha,0,1),c=AmCharts.line(a.container,z,A,this.gridColor,c,1,this.dashLength),c.translate(r,t),this.grid0=c,a.axesSet.push(c),c.toBack())}if(!isNaN(y)&&s>0&&o-1>d){for(r=this.gridAlpha,this.gridAlpha=this.minorGridAlpha,c=1;p/y>c;c++)t=this.getCoordinate(n+y*c),t=new this.axisItemRenderer(this,t,"",!1,0,0,!1,!1,0,!0),this.pushAxisItem(t);this.gridAlpha=r}}e=this.baseValue,this.min>this.baseValue&&this.max>this.baseValue&&(e=this.min),this.min<this.baseValue&&this.max<this.baseValue&&(e=this.max),h&&e<this.minReal&&(e=this.minReal),this.baseCoord=this.getCoordinate(e),e={type:"axisChanged",target:this,chart:a},e.min=h?this.minReal:this.min,e.max=this.max,this.fire("axisChanged",e),this.axisCreated=!0}h=this.axisLine.set,e=this.labelsSet,this.positionTitle(),"radar"!=a.type?(a=this.viX,f=this.viY,b.translate(a,f),e.translate(a,f)):h.toFront(),!this.visible||g?(b.hide(),h.hide(),e.hide()):(b.show(),h.show(),e.show()),this.axisY=this.y-this.viY,this.axisX=this.x-this.viX}},formatValue:function(a,b,c){var d=this.exponential,e=this.logarithmic,f=this.numberFormatter,g=this.chart;return!0===this.logarithmic&&(d=-1!=String(a).indexOf("e")?!0:!1),this.useScientificNotation&&(d=!0),this.usePrefixes&&(d=!1),d?(valueText=-1==String(a).indexOf("e")?a.toExponential(15):String(a),c=valueText.split("e"),b=Number(c[0]),c=Number(c[1]),b=AmCharts.roundTo(b,14),10==b&&(b=1,c+=1),valueText=b+"e"+c,0===a&&(valueText="0"),1==a&&(valueText="1")):(e&&(d=String(a).split("."),d[1]?(f.precision=d[1].length,0>c&&(f.precision=Math.abs(c))):f.precision=-1),valueText=this.usePrefixes?AmCharts.addPrefix(a,g.prefixesOfBigNumbers,g.prefixesOfSmallNumbers,f,!b):AmCharts.formatNumber(a,f,f.precision)),this.duration&&(valueText=AmCharts.formatDuration(a,this.duration,"",this.durationUnits,this.maxInterval,f)),this.recalculateToPercents?valueText+="%":(f=this.unit)&&(valueText="left"==this.unitPosition?f+valueText:valueText+f),this.labelFunction&&(valueText=this.labelFunction(a,valueText,this).toString()),valueText},getMinorGridStep:function(a,b){var c=[5,4,2];60>b&&c.shift();for(var d=Math.floor(Math.log(Math.abs(a))*Math.LOG10E),e=0;e<c.length;e++){var f=a/c[e],g=Math.floor(Math.log(Math.abs(f))*Math.LOG10E);if(!(0<Math.abs(d-g)))if(1>a){if(g=Math.pow(10,-g)*f,g==Math.round(g))return f}else if(f==Math.round(f))return f}},stackGraphs:function(a){var b=this.stackType;"stacked"==b&&(b="regular"),"line"==b&&(b="none"),"100% stacked"==b&&(b="100%"),this.stackType=b;var c,d,e,f,g,h=[],i=[],j=[],k=[],l=this.chart.graphs,m=this.baseValue,n=!1;if(("line"==a||"step"==a||"smoothedLine"==a)&&(n=!0),n&&("regular"==b||"100%"==b))for(g=0;g<l.length;g++)f=l[g],f.hidden||(e=f.type,f.chart==this.chart&&f.valueAxis==this&&a==e&&f.stackable&&(d&&(f.stackGraph=d),d=f));for(d=this.start;d<=this.end;d++){var o=0;for(g=0;g<l.length;g++)if(f=l[g],f.hidden)f.newStack&&(j[d]=0/0,i[d]=0/0);else if(e=f.type,f.chart==this.chart&&f.valueAxis==this&&a==e&&f.stackable)if(e=this.data[d].axes[this.id].graphs[f.id],c=e.values.value,isNaN(c))f.newStack&&(j[d]=0/0,i[d]=0/0);else{var p=AmCharts.getDecimals(c);p>o&&(o=p),isNaN(k[d])?k[d]=Math.abs(c):k[d]+=Math.abs(c),k[d]=AmCharts.roundTo(k[d],o),p=f.fillToGraph,n&&p&&(p=this.data[d].axes[this.id].graphs[p.id])&&(e.values.open=p.values.value),"regular"==b&&(n&&(isNaN(h[d])?(h[d]=c,e.values.close=c,e.values.open=this.baseValue):(isNaN(c)?e.values.close=h[d]:e.values.close=c+h[d],e.values.open=h[d],h[d]=e.values.close)),"column"==a&&(f.newStack&&(j[d]=0/0,i[d]=0/0),e.values.close=c,0>c?(e.values.close=c,isNaN(i[d])?e.values.open=m:(e.values.close+=i[d],e.values.open=i[d]),i[d]=e.values.close):(e.values.close=c,isNaN(j[d])?e.values.open=m:(e.values.close+=j[d],e.values.open=j[d]),j[d]=e.values.close)))}}for(d=this.start;d<=this.end;d++)for(g=0;g<l.length;g++)f=l[g],f.hidden?f.newStack&&(j[d]=0/0,i[d]=0/0):(e=f.type,f.chart==this.chart&&f.valueAxis==this&&a==e&&f.stackable&&(e=this.data[d].axes[this.id].graphs[f.id],c=e.values.value,isNaN(c)||(h=c/k[d]*100,e.values.percents=h,e.values.total=k[d],f.newStack&&(j[d]=0/0,i[d]=0/0),"100%"==b&&(isNaN(i[d])&&(i[d]=0),isNaN(j[d])&&(j[d]=0),0>h?(e.values.close=AmCharts.fitToBounds(h+i[d],-100,100),e.values.open=i[d],i[d]=e.values.close):(e.values.close=AmCharts.fitToBounds(h+j[d],-100,100),e.values.open=j[d],j[d]=e.values.close)))))},recalculate:function(){var a,b=this.chart,c=b.graphs;for(a=0;a<c.length;a++){var d=c[a];if(d.valueAxis==this){var e="value";("candlestick"==d.type||"ohlc"==d.type)&&(e="open");var f,g,h=this.end+2,h=AmCharts.fitToBounds(this.end+1,0,this.data.length-1),i=this.start;i>0&&i--;var j;if(g=this.start,d.compareFromStart&&(g=0),!isNaN(b.startTime)&&(j=b.categoryAxis)){minDuration=j.minDuration();var k=new Date(b.startTime+minDuration/2),l=AmCharts.resetDateToMin(new Date(b.startTime),j.minPeriod).getTime();AmCharts.resetDateToMin(new Date(k),j.minPeriod).getTime()>l&&g++}for((j=b.recalculateFromDate)&&(b.dataDateFormat&&(j=AmCharts.stringToDate(j,b.dataDateFormat)),g=b.getClosestIndex(b.chartData,"time",j.getTime(),!0,0,b.chartData.length),h=b.chartData.length-1),j=g;h>=j&&(g=this.data[j].axes[this.id].graphs[d.id],f=g.values[e],isNaN(f));j++);for(this.recBaseValue=f,e=i;h>=e;e++){g=this.data[e].axes[this.id].graphs[d.id],g.percents={};var m,i=g.values;for(m in i)g.percents[m]="percents"!=m?i[m]/f*100-100:i[m]}}}},getMinMax:function(){var a,b=!1,c=this.chart,d=c.graphs;for(a=0;a<d.length;a++){var e=d[a].type;("line"==e||"step"==e||"smoothedLine"==e)&&this.expandMinMax&&(b=!0)}if(b&&(0<this.start&&this.start--,this.end<this.data.length-1&&this.end++),"serial"==c.type&&(!0!==c.categoryAxis.parseDates||b||this.end<this.data.length-1&&this.end++),b=this.minMaxMultiplier,this.min=this.getMin(this.start,this.end),this.max=this.getMax(),b=(this.max-this.min)*(b-1),this.min-=b,this.max+=b,b=this.guides.length,this.includeGuidesInMinMax&&b>0)for(c=0;b>c;c++)d=this.guides[c],d.toValue<this.min&&(this.min=d.toValue),d.value<this.min&&(this.min=d.value),d.toValue>this.max&&(this.max=d.toValue),d.value>this.max&&(this.max=d.value);isNaN(this.minimum)||(this.min=this.minimum),isNaN(this.maximum)||(this.max=this.maximum),this.min>this.max&&(b=this.max,this.max=this.min,this.min=b),isNaN(this.minTemp)||(this.min=this.minTemp),isNaN(this.maxTemp)||(this.max=this.maxTemp),this.minReal=this.min,this.maxReal=this.max,0===this.min&&0===this.max&&(this.max=9),this.min>this.max&&(this.min=this.max-1),b=this.min,c=this.max,d=this.max-this.min,a=0===d?Math.pow(10,Math.floor(Math.log(Math.abs(this.max))*Math.LOG10E))/10:Math.pow(10,Math.floor(Math.log(Math.abs(d))*Math.LOG10E))/10,isNaN(this.maximum)&&isNaN(this.maxTemp)&&(this.max=Math.ceil(this.max/a)*a+a),isNaN(this.minimum)&&isNaN(this.minTemp)&&(this.min=Math.floor(this.min/a)*a-a),0>this.min&&b>=0&&(this.min=0),0<this.max&&0>=c&&(this.max=0),"100%"==this.stackType&&(this.min=0>this.min?-100:0,this.max=0>this.max?0:100),d=this.max-this.min,a=Math.pow(10,Math.floor(Math.log(Math.abs(d))*Math.LOG10E))/10,this.step=Math.ceil(d/this.gridCountR/a)*a,d=Math.pow(10,Math.floor(Math.log(Math.abs(this.step))*Math.LOG10E)),d=this.fixStepE(d),a=Math.ceil(this.step/d),a>5&&(a=10),5>=a&&a>2&&(a=5),this.step=Math.ceil(this.step/(d*a))*d*a,1>d?(this.maxDecCount=Math.abs(Math.log(Math.abs(d))*Math.LOG10E),this.maxDecCount=Math.round(this.maxDecCount),this.step=AmCharts.roundTo(this.step,this.maxDecCount+1)):this.maxDecCount=0,this.min=this.step*Math.floor(this.min/this.step),this.max=this.step*Math.ceil(this.max/this.step),0>this.min&&b>=0&&(this.min=0),0<this.max&&0>=c&&(this.max=0),1<this.minReal&&1<this.max-this.minReal&&(this.minReal=Math.floor(this.minReal)),d=Math.pow(10,Math.floor(Math.log(Math.abs(this.minReal))*Math.LOG10E)),0===this.min&&(this.minReal=d),0===this.min&&1<this.minReal&&(this.minReal=1),0<this.min&&0<this.minReal-this.step&&(this.minReal=this.min+this.step<this.minReal?this.min+this.step:this.min),d=Math.log(c)*Math.LOG10E-Math.log(b)*Math.LOG10E,this.logarithmic&&(d>2?(this.minReal=this.min=Math.pow(10,Math.floor(Math.log(Math.abs(b))*Math.LOG10E)),this.max=Math.pow(10,Math.ceil(Math.log(Math.abs(c))*Math.LOG10E))):(c=Math.pow(10,Math.floor(Math.log(Math.abs(this.min))*Math.LOG10E))/10,b=Math.pow(10,Math.floor(Math.log(Math.abs(b))*Math.LOG10E))/10,b>c&&(this.minReal=this.min=10*b)))},fixStepE:function(a){a=a.toExponential(0).split("e");var b=Number(a[1]);

return 9==Number(a[0])&&b++,this.generateNumber(1,b)},generateNumber:function(a,b){var c,d="";c=0>b?Math.abs(b)-1:Math.abs(b);var e;for(e=0;c>e;e++)d+="0";return Number(0>b?"0."+d+String(a):String(a)+d)},getMin:function(a,b){var c,d;for(d=a;b>=d;d++){var e,f=this.data[d].axes[this.id].graphs;for(e in f)if(f.hasOwnProperty(e)){var g=this.chart.getGraphById(e);if(g.includeInMinMax&&(!g.hidden||this.includeHidden)){isNaN(c)&&(c=1/0),this.foundGraphs=!0,g=f[e].values,this.recalculateToPercents&&(g=f[e].percents);var h;if(this.minMaxField)h=g[this.minMaxField],c>h&&(c=h);else for(var i in g)g.hasOwnProperty(i)&&"percents"!=i&&"total"!=i&&(h=g[i],c>h&&(c=h))}}}return c},getMax:function(){var a,b;for(b=this.start;b<=this.end;b++){var c,d=this.data[b].axes[this.id].graphs;for(c in d)if(d.hasOwnProperty(c)){var e=this.chart.getGraphById(c);if(e.includeInMinMax&&(!e.hidden||this.includeHidden)){isNaN(a)&&(a=-(1/0)),this.foundGraphs=!0,e=d[c].values,this.recalculateToPercents&&(e=d[c].percents);var f;if(this.minMaxField)f=e[this.minMaxField],f>a&&(a=f);else for(var g in e)e.hasOwnProperty(g)&&"percents"!=g&&"total"!=g&&(f=e[g],f>a&&(a=f))}}}return a},dispatchZoomEvent:function(a,b){var c={type:"axisZoomed",startValue:a,endValue:b,target:this,chart:this.chart};this.fire(c.type,c)},zoomToValues:function(a,b){if(a>b){var c=b;b=a,a=c}a<this.min&&(a=this.min),b>this.max&&(b=this.max),c={type:"axisSelfZoomed"},c.chart=this.chart,c.valueAxis=this,c.multiplier=this.axisWidth/Math.abs(this.getCoordinate(b)-this.getCoordinate(a)),c.position=this.getCoordinate("V"==this.orientation?this.reversed?a:b:this.reversed?b:a),this.fire(c.type,c)},coordinateToValue:function(a){if(isNaN(a))return 0/0;var b=this.axisWidth,c=this.stepWidth,d=this.reversed,e=this.rotate,f=this.min,g=this.minReal;return!0===this.logarithmic?Math.pow(10,(e?!0===d?(b-a)/c:a/c:!0===d?a/c:(b-a)/c)+Math.log(g)*Math.LOG10E):!0===d?e?f-(a-b)/c:a/c+f:e?a/c+f:f-(a-b)/c},getCoordinate:function(a){if(isNaN(a))return 0/0;var b=this.rotate,c=this.reversed,d=this.axisWidth,e=this.stepWidth,f=this.min,g=this.minReal;return!0===this.logarithmic?(a=Math.log(a)*Math.LOG10E-Math.log(g)*Math.LOG10E,b=b?!0===c?d-e*a:e*a:!0===c?e*a:d-e*a):b=!0===c?b?d-e*(a-f):e*(a-f):b?e*(a-f):d-e*(a-f),b=this.rotate?b+(this.x-this.viX):b+(this.y-this.viY),1e7<Math.abs(b)&&(b=1e7*(b/Math.abs(b))),Math.round(b)},synchronizeWithAxis:function(a){this.synchronizeWith=a,this.listenTo(this.synchronizeWith,"axisChanged",this.handleSynchronization)},handleSynchronization:function(a){var b=this.synchronizeWith;a=b.min;var c=b.max,b=b.step,d=this.synchronizationMultiplier;d&&(this.min=a*d,this.max=c*d,this.step=b*d,a=Math.pow(10,Math.floor(Math.log(Math.abs(this.step))*Math.LOG10E)),a=Math.abs(Math.log(Math.abs(a))*Math.LOG10E),this.maxDecCount=a=Math.round(a),this.draw())}}),AmCharts.RecAxis=AmCharts.Class({construct:function(a){var b=a.chart,c=a.axisThickness,d=a.axisColor,e=a.axisAlpha,f=a.offset,g=a.dx,h=a.dy,i=a.viX,j=a.viY,k=a.viH,l=a.viW,m=b.container;"H"==a.orientation?(d=AmCharts.line(m,[0,l],[0,0],d,e,c),this.axisWidth=a.width,"bottom"==a.position?(a=c/2+f+k+j-1,c=i):(a=-c/2-f+j+h,c=g+i)):(this.axisWidth=a.height,"right"==a.position?(d=AmCharts.line(m,[0,0,-g],[0,k,k-h],d,e,c),a=j+h,c=c/2+f+g+l+i-1):(d=AmCharts.line(m,[0,0],[0,k],d,e,c),a=j,c=-c/2-f+i)),d.translate(c,a),b.axesSet.push(d),this.set=d}}),AmCharts.RecItem=AmCharts.Class({construct:function(a,b,c,d,e,f,g,h,i,j,k){b=Math.round(b),this.value=c,void 0==c&&(c=""),i||(i=0),void 0==d&&(d=!0);var l=a.chart.fontFamily,m=a.fontSize;void 0==m&&(m=a.chart.fontSize);var n=a.color;void 0==n&&(n=a.chart.color),void 0!==k&&(n=k);var o=a.chart.container,p=o.set();this.set=p;var q=a.axisThickness,r=a.axisColor,s=a.axisAlpha,t=a.tickLength,u=a.gridAlpha,v=a.gridThickness,w=a.gridColor,x=a.dashLength,y=a.fillColor,z=a.fillAlpha,A=a.labelsEnabled;k=a.labelRotation;var B,C,D=a.counter,E=a.inside,F=a.labelOffset,G=a.dx,H=a.dy,I=a.orientation,J=a.position,K=a.previousCoord,L=a.viH,M=a.viW,N=a.offset;g?(A=!0,isNaN(g.tickLength)||(t=g.tickLength),void 0!=g.lineColor&&(w=g.lineColor),void 0!=g.color&&(n=g.color),isNaN(g.lineAlpha)||(u=g.lineAlpha),isNaN(g.dashLength)||(x=g.dashLength),isNaN(g.lineThickness)||(v=g.lineThickness),!0===g.inside&&(E=!0),isNaN(g.labelRotation)||(k=g.labelRotation),isNaN(g.fontSize)||(m=g.fontSize),g.position&&(J=g.position),void 0!==g.boldLabel&&(h=g.boldLabel),isNaN(g.labelOffset)||(F=g.labelOffset)):""===c&&(t=0),C="start",e&&(C="middle");var O,P=k*Math.PI/180,Q=0,R=0,S=0,T=O=0,U=0;"V"==I&&(k=0);var V;A&&(V=a.autoWrap&&0===k?AmCharts.wrappedText(o,c,n,l,m,C,h,e,0):AmCharts.text(o,c,n,l,m,C,h),C=V.getBBox(),T=C.width,U=C.height),"H"==I?(b>=0&&M+1>=b&&(t>0&&s>0&&M+1>=b+i&&(B=AmCharts.line(o,[b+i,b+i],[0,t],r,s,v),p.push(B)),u>0&&(C=AmCharts.line(o,[b,b+G,b+G],[L,L+H,H],w,u,v,x),p.push(C))),R=0,Q=b,g&&90==k&&E&&(Q-=m),!1===d?(C="start",R="bottom"==J?E?R+t:R-t:E?R-t:R+t,Q+=3,e&&(Q+=e/2-3,C="middle"),k>0&&(C="middle")):C="middle",1==D&&z>0&&!g&&!j&&M>K&&(d=AmCharts.fitToBounds(b,0,M),K=AmCharts.fitToBounds(K,0,M),O=d-K,O>0&&(fill=AmCharts.rect(o,O,a.height,y,z),fill.translate(d-O+G,H),p.push(fill))),"bottom"==J?(R+=L+m/2+N,E?(k>0?(R=L-T/2*Math.sin(P)-t-3,Q+=T/2*Math.cos(P)-4+2):0>k?(R=L+T*Math.sin(P)-t-3+2,Q+=-T*Math.cos(P)-U*Math.sin(P)-4):R-=t+m+3+3,R-=F):(k>0?(R=L+T/2*Math.sin(P)+t+3,Q-=T/2*Math.cos(P)):0>k?(R=L+t+3-T/2*Math.sin(P)+2,Q+=T/2*Math.cos(P)):R+=t+q+3+3,R+=F)):(R+=H+m/2-N,Q+=G,E?(k>0?(R=T/2*Math.sin(P)+t+3,Q-=T/2*Math.cos(P)):R+=t+3,R+=F):(k>0?(R=-(T/2)*Math.sin(P)-t-6,Q+=T/2*Math.cos(P)):R-=t+m+3+q+3,R-=F)),"bottom"==J?O=(E?L-t-1:L+q-1)+N:(S=G,O=(E?H:H-t-q+1)-N),f&&(Q+=f),f=Q,k>0&&(f+=T/2*Math.cos(P)),V&&(m=0,E&&(m=T/2*Math.cos(P)),f+m>M+2||0>f)&&(V.remove(),V=null)):(b>=0&&L+1>=b&&(t>0&&s>0&&L+1>=b+i&&(B=AmCharts.line(o,[0,t],[b+i,b+i],r,s,v),p.push(B)),u>0&&(C=AmCharts.line(o,[0,G,M+G],[b,b+H,b+H],w,u,v,x),p.push(C))),C="end",(!0===E&&"left"==J||!1===E&&"right"==J)&&(C="start"),R=b-m/2,1==D&&z>0&&!g&&!j&&(d=AmCharts.fitToBounds(b,0,L),K=AmCharts.fitToBounds(K,0,L),P=d-K,fill=AmCharts.polygon(o,[0,a.width,a.width,0],[0,0,P,P],y,z),fill.translate(G,d-P+H),p.push(fill)),R+=m/2,"right"==J?(Q+=G+M+N,R+=H,E?(f||(R-=m/2+3),Q=Q-(t+4)-F):(Q+=t+4+q,R-=2,Q+=F)):E?(Q+=t+4-N,f||(R-=m/2+3),g&&(Q+=G,R+=H),Q+=F):(Q+=-t-q-4-2-N,R-=2,Q-=F),B&&("right"==J?(S+=G+N+M,O+=H,S=E?S-q:S+q):(S-=N,E||(S-=t+q))),f&&(R+=f),E=-3,"right"==J&&(E+=H),V&&(R>L+1||E>R)&&(V.remove(),V=null)),B&&B.translate(S,O),!1===a.visible&&(B&&B.remove(),V&&(V.remove(),V=null)),V&&(V.attr({"text-anchor":C}),V.translate(Q,R),0!==k&&V.rotate(-k,a.chart.backgroundColor),a.allLabels.push(V)," "!=c&&(this.label=V)),j||(a.counter=0===D?1:0,a.previousCoord=b),0===this.set.node.childNodes.length&&this.set.remove()},graphics:function(){return this.set},getLabel:function(){return this.label}}),AmCharts.RecFill=AmCharts.Class({construct:function(a,b,c,d){var e=a.dx,f=a.dy,g=a.orientation,h=0;if(b>c){var i=b;b=c,c=i}var j=d.fillAlpha;isNaN(j)&&(j=0),i=a.chart.container,d=d.fillColor,"V"==g?(b=AmCharts.fitToBounds(b,0,a.viH),c=AmCharts.fitToBounds(c,0,a.viH)):(b=AmCharts.fitToBounds(b,0,a.viW),c=AmCharts.fitToBounds(c,0,a.viW)),c-=b,isNaN(c)&&(c=4,h=2,j=0),0>c&&"object"==typeof d&&(d=d.join(",").split(",").reverse()),"V"==g?(a=AmCharts.rect(i,a.width,c,d,j),a.translate(e,b-h+f)):(a=AmCharts.rect(i,c,a.height,d,j),a.translate(b-h+e,f)),this.set=i.set([a])},graphics:function(){return this.set},getLabel:function(){}}),AmCharts.AmChart=AmCharts.Class({construct:function(a){this.theme=a,this.version="3.11.1",AmCharts.addChart(this),this.createEvents("dataUpdated","init","rendered","drawn","failed"),this.height=this.width="100%",this.dataChanged=!0,this.chartCreated=!1,this.previousWidth=this.previousHeight=0,this.backgroundColor="#FFFFFF",this.borderAlpha=this.backgroundAlpha=0,this.color=this.borderColor="#000000",this.fontFamily="Verdana",this.fontSize=11,this.usePrefixes=!1,this.precision=-1,this.percentPrecision=2,this.decimalSeparator=".",this.thousandsSeparator=",",this.labels=[],this.allLabels=[],this.titles=[],this.marginRight=this.marginLeft=this.autoMarginOffset=0,this.timeOuts=[],this.creditsPosition="top-left";var b=document.createElement("div"),c=b.style;c.overflow="hidden",c.position="relative",c.textAlign="left",this.chartDiv=b,b=document.createElement("div"),c=b.style,c.overflow="hidden",c.position="relative",c.textAlign="left",this.legendDiv=b,this.titleHeight=0,this.hideBalloonTime=150,this.handDrawScatter=2,this.handDrawThickness=1,this.prefixesOfBigNumbers=[{number:1e3,prefix:"k"},{number:1e6,prefix:"M"},{number:1e9,prefix:"G"},{number:1e12,prefix:"T"},{number:1e15,prefix:"P"},{number:1e18,prefix:"E"},{number:1e21,prefix:"Z"},{number:1e24,prefix:"Y"}],this.prefixesOfSmallNumbers=[{number:1e-24,prefix:"y"},{number:1e-21,prefix:"z"},{number:1e-18,prefix:"a"},{number:1e-15,prefix:"f"},{number:1e-12,prefix:"p"},{number:1e-9,prefix:"n"},{number:1e-6,prefix:"μ"},{number:.001,prefix:"m"}],this.panEventsEnabled=!0,AmCharts.bezierX=3,AmCharts.bezierY=6,this.product="amcharts",this.animations=[],this.balloon=new AmCharts.AmBalloon(this.theme),this.balloon.chart=this,AmCharts.applyTheme(this,a,"AmChart")},drawChart:function(){this.drawBackground(),this.redrawLabels(),this.drawTitles(),this.brr()},drawBackground:function(){AmCharts.remove(this.background);var a=this.container,b=this.backgroundColor,c=this.backgroundAlpha,d=this.set;AmCharts.isModern||0!==c||(c=.001);var e=this.updateWidth();this.realWidth=e;var f=this.updateHeight();this.realHeight=f,this.background=b=AmCharts.polygon(a,[0,e-1,e-1,0],[0,0,f-1,f-1],b,c,1,this.borderColor,this.borderAlpha),d.push(b),(b=this.backgroundImage)&&(this.path&&(b=this.path+b),this.bgImg=a=a.image(b,0,0,e,f),d.push(a))},drawTitles:function(){var a=this.titles;if(AmCharts.ifArray(a)){var b,c=20;for(b=0;b<a.length;b++){var d=a[b],e=d.color;void 0===e&&(e=this.color);var f=d.size;isNaN(f)&&(f=this.fontSize+2),isNaN(d.alpha);var g=this.marginLeft,e=AmCharts.text(this.container,d.text,e,this.fontFamily,f);e.translate(g+(this.realWidth-this.marginRight-g)/2,c),e.node.style.pointerEvents="none",g=!0,void 0!==d.bold&&(g=d.bold),g&&e.attr({"font-weight":"bold"}),e.attr({opacity:d.alpha}),c+=f+6,this.freeLabelsSet.push(e)}}},write:function(a){if(a="object"!=typeof a?document.getElementById(a):a){a.innerHTML="",this.div=a,a.style.overflow="hidden",a.style.textAlign="left";var b=this.chartDiv,c=this.legendDiv,d=this.legend,e=c.style,f=b.style;this.measure();var g,h=document.createElement("div");g=h.style,g.position="relative",this.containerDiv=h,a.appendChild(h);var i=this.exportConfig;if(i&&AmCharts.AmExport&&!this.AmExport&&(this.AmExport=new AmCharts.AmExport(this,i)),this.amExport&&AmCharts.AmExport&&(this.AmExport=AmCharts.extend(this.amExport,new AmCharts.AmExport(this),!0)),this.AmExport&&this.AmExport.init&&this.AmExport.init(),d)switch(d=this.addLegend(d,d.divId),d.position){case"bottom":h.appendChild(b),h.appendChild(c);break;case"top":h.appendChild(c),h.appendChild(b);break;case"absolute":g.width=a.style.width,g.height=a.style.height,e.position="absolute",f.position="absolute",void 0!==d.left&&(e.left=d.left+"px"),void 0!==d.right&&(e.right=d.right+"px"),void 0!==d.top&&(e.top=d.top+"px"),void 0!==d.bottom&&(e.bottom=d.bottom+"px"),d.marginLeft=0,d.marginRight=0,h.appendChild(b),h.appendChild(c);break;case"right":g.width=a.style.width,g.height=a.style.height,e.position="relative",f.position="absolute",h.appendChild(b),h.appendChild(c);break;case"left":g.width=a.style.width,g.height=a.style.height,e.position="absolute",f.position="relative",h.appendChild(b),h.appendChild(c);break;case"outside":h.appendChild(b)}else h.appendChild(b);this.listenersAdded||(this.addListeners(),this.listenersAdded=!0),this.initChart()}},createLabelsSet:function(){AmCharts.remove(this.labelsSet),this.labelsSet=this.container.set(),this.freeLabelsSet.push(this.labelsSet)},initChart:function(){AmCharts.callInitHandler(this),AmCharts.applyLang(this.language,this);var a=this.numberFormatter;a&&(isNaN(a.precision)||(this.precision=a.precision),void 0!==a.thousandsSeparator&&(this.thousandsSeparator=a.thousandsSeparator),void 0!==a.decimalSeparator&&(this.decimalSeparator=a.decimalSeparator)),(a=this.percentFormatter)&&!isNaN(a.precision)&&(this.percentPrecision=a.precision),this.nf={precision:this.precision,thousandsSeparator:this.thousandsSeparator,decimalSeparator:this.decimalSeparator},this.pf={precision:this.percentPrecision,thousandsSeparator:this.thousandsSeparator,decimalSeparator:this.decimalSeparator},this.divIsFixed=AmCharts.findIfFixed(this.chartDiv),this.previousHeight=this.divRealHeight,this.previousWidth=this.divRealWidth,this.destroy(),this.startInterval(),a=0,document.attachEvent&&!window.opera&&(a=1),this.dmouseX=this.dmouseY=0;var b=document.getElementsByTagName("html")[0];b&&window.getComputedStyle&&(b=window.getComputedStyle(b,null))&&(this.dmouseY=AmCharts.removePx(b.getPropertyValue("margin-top")),this.dmouseX=AmCharts.removePx(b.getPropertyValue("margin-left"))),this.mouseMode=a,(a=this.container)?(a.container.innerHTML="",this.chartDiv.appendChild(a.container),a.setSize(this.realWidth,this.realHeight)):a=new AmCharts.AmDraw(this.chartDiv,this.realWidth,this.realHeight,this),AmCharts.VML||AmCharts.SVG?(a.handDrawn=this.handDrawn,a.handDrawScatter=this.handDrawScatter,a.handDrawThickness=this.handDrawThickness,this.container=a,this.set&&this.set.remove(),this.set=a.set(),this.gridSet&&this.gridSet.remove(),this.gridSet=a.set(),this.cursorLineSet&&this.cursorLineSet.remove(),this.cursorLineSet=a.set(),this.graphsBehindSet&&this.graphsBehindSet.remove(),this.graphsBehindSet=a.set(),this.bulletBehindSet&&this.bulletBehindSet.remove(),this.bulletBehindSet=a.set(),this.columnSet&&this.columnSet.remove(),this.columnSet=a.set(),this.graphsSet&&this.graphsSet.remove(),this.graphsSet=a.set(),this.trendLinesSet&&this.trendLinesSet.remove(),this.trendLinesSet=a.set(),this.axesLabelsSet&&this.axesLabelsSet.remove(),this.axesLabelsSet=a.set(),this.axesSet&&this.axesSet.remove(),this.axesSet=a.set(),this.cursorSet&&this.cursorSet.remove(),this.cursorSet=a.set(),this.scrollbarsSet&&this.scrollbarsSet.remove(),this.scrollbarsSet=a.set(),this.bulletSet&&this.bulletSet.remove(),this.bulletSet=a.set(),this.freeLabelsSet&&this.freeLabelsSet.remove(),this.freeLabelsSet=a.set(),this.balloonsSet&&this.balloonsSet.remove(),this.balloonsSet=a.set(),this.zoomButtonSet&&this.zoomButtonSet.remove(),this.zoomButtonSet=a.set(),this.linkSet&&this.linkSet.remove(),this.linkSet=a.set(),this.renderFix()):this.fire("failed",{type:"failed",chart:this})},measure:function(){var a=this.div;if(a){var b=this.chartDiv,c=a.offsetWidth,d=a.offsetHeight,e=this.container;a.clientHeight&&(c=a.clientWidth,d=a.clientHeight);var f=AmCharts.removePx(AmCharts.getStyle(a,"padding-left")),g=AmCharts.removePx(AmCharts.getStyle(a,"padding-right")),h=AmCharts.removePx(AmCharts.getStyle(a,"padding-top")),i=AmCharts.removePx(AmCharts.getStyle(a,"padding-bottom"));isNaN(f)||(c-=f),isNaN(g)||(c-=g),isNaN(h)||(d-=h),isNaN(i)||(d-=i),f=a.style,a=f.width,f=f.height,-1!=a.indexOf("px")&&(c=AmCharts.removePx(a)),-1!=f.indexOf("px")&&(d=AmCharts.removePx(f)),a=AmCharts.toCoordinate(this.width,c),f=AmCharts.toCoordinate(this.height,d),this.balloon=AmCharts.processObject(this.balloon,AmCharts.AmBalloon,this.theme),this.balloon.chart=this,(a!=this.previousWidth||f!=this.previousHeight)&&a>0&&f>0&&(b.style.width=a+"px",b.style.height=f+"px",e&&e.setSize(a,f)),this.balloon.setBounds(2,2,a-2,f),this.realWidth=a,this.realHeight=f,this.divRealWidth=c,this.divRealHeight=d}},destroy:function(){this.chartDiv.innerHTML="",this.clearTimeOuts(),this.interval&&clearInterval(this.interval),this.interval=0/0},clearTimeOuts:function(){var a=this.timeOuts;if(a){var b;for(b=0;b<a.length;b++)clearTimeout(a[b])}this.timeOuts=[]},clear:function(a){AmCharts.callMethod("clear",[this.chartScrollbar,this.scrollbarV,this.scrollbarH,this.chartCursor]),this.chartCursor=this.scrollbarH=this.scrollbarV=this.chartScrollbar=null,this.clearTimeOuts(),this.interval&&clearInterval(this.interval),this.container&&(this.container.remove(this.chartDiv),this.container.remove(this.legendDiv)),a||AmCharts.removeChart(this)},setMouseCursor:function(a){"auto"==a&&AmCharts.isNN&&(a="default"),this.chartDiv.style.cursor=a,this.legendDiv.style.cursor=a},redrawLabels:function(){this.labels=[];var a=this.allLabels;this.createLabelsSet();var b;for(b=0;b<a.length;b++)this.drawLabel(a[b])},drawLabel:function(a){if(this.container){var b=a.y,c=a.text,d=a.align,e=a.size,f=a.color,g=a.rotation,h=a.alpha,i=a.bold,j=AmCharts.toCoordinate(a.x,this.realWidth),b=AmCharts.toCoordinate(b,this.realHeight);j||(j=0),b||(b=0),void 0===f&&(f=this.color),isNaN(e)&&(e=this.fontSize),d||(d="start"),"left"==d&&(d="start"),"right"==d&&(d="end"),"center"==d&&(d="middle",g?b=this.realHeight-b+b/2:j=this.realWidth/2-j),void 0===h&&(h=1),void 0===g&&(g=0),b+=e/2,c=AmCharts.text(this.container,c,f,this.fontFamily,e,d,i,h),c.translate(j,b),0!==g&&c.rotate(g),a.url?(c.setAttr("cursor","pointer"),c.click(function(){AmCharts.getURL(a.url)})):c.node.style.pointerEvents="none",this.labelsSet.push(c),this.labels.push(c)}},addLabel:function(a,b,c,d,e,f,g,h,i,j){a={x:a,y:b,text:c,align:d,size:e,color:f,alpha:h,rotation:g,bold:i,url:j},this.container&&this.drawLabel(a),this.allLabels.push(a)},clearLabels:function(){var a,b=this.labels;for(a=b.length-1;a>=0;a--)b[a].remove();this.labels=[],this.allLabels=[]},updateHeight:function(){var a=this.divRealHeight,b=this.legend;if(b){var c=this.legendDiv.offsetHeight,b=b.position;("top"==b||"bottom"==b)&&(a-=c,(0>a||isNaN(a))&&(a=0),this.chartDiv.style.height=a+"px")}return a},updateWidth:function(){var a=this.divRealWidth,b=this.divRealHeight,c=this.legend;if(c){var d=this.legendDiv,e=d.offsetWidth;isNaN(c.width)||(e=c.width);var f=d.offsetHeight,d=d.style,g=this.chartDiv.style,c=c.position;("right"==c||"left"==c)&&(a-=e,(0>a||isNaN(a))&&(a=0),g.width=a+"px","left"==c?g.left=e+"px":d.left=a+"px",b>f&&(d.top=(b-f)/2+"px"))}return a},getTitleHeight:function(){var a=0,b=this.titles;if(0<b.length){var c,a=15;for(c=0;c<b.length;c++){var d=b[c].size;isNaN(d)&&(d=this.fontSize+2),a+=d+6}}return a},addTitle:function(a,b,c,d,e){return isNaN(b)&&(b=this.fontSize+2),a={text:a,size:b,color:c,alpha:d,bold:e},this.titles.push(a),a},addMouseWheel:function(){var a=this;window.addEventListener&&!a.wheelAdded&&(window.addEventListener("DOMMouseScroll",function(b){a.handleWheel.call(a,b)},!1),document.addEventListener("mousewheel",function(b){a.handleWheel.call(a,b)},!1),a.wheelAdded=!0)},handleWheel:function(a){if(this.mouseIsOver){var b=0;a||(a=window.event),a.wheelDelta?b=a.wheelDelta/120:a.detail&&(b=-a.detail/3),b&&this.handleWheelReal(b,a.shiftKey),a.preventDefault&&a.preventDefault()}},handleWheelReal:function(a){},addListeners:function(){var a=this,b=a.chartDiv;document.addEventListener?(a.panEventsEnabled&&(b.style.msTouchAction="none","ontouchstart"in document.documentElement&&(b.addEventListener("touchstart",function(b){a.handleTouchMove.call(a,b),a.handleTouchStart.call(a,b)},!0),b.addEventListener("touchmove",function(b){a.handleTouchMove.call(a,b)},!0),b.addEventListener("touchend",function(b){a.handleTouchEnd.call(a,b)},!0))),b.addEventListener("mousedown",function(b){a.mouseIsOver=!0,a.handleMouseMove.call(a,b),a.handleMouseDown.call(a,b)},!0),b.addEventListener("mouseover",function(b){a.handleMouseOver.call(a,b)},!0),b.addEventListener("mouseout",function(b){a.handleMouseOut.call(a,b)},!0)):(b.attachEvent("onmousedown",function(b){a.handleMouseDown.call(a,b)}),b.attachEvent("onmouseover",function(b){a.handleMouseOver.call(a,b)}),b.attachEvent("onmouseout",function(b){a.handleMouseOut.call(a,b)}))},dispDUpd:function(){var a;this.dispatchDataUpdated&&(this.dispatchDataUpdated=!1,a="dataUpdated",this.fire(a,{type:a,chart:this})),this.chartCreated||(a="init",this.fire(a,{type:a,chart:this})),this.chartRendered||(a="rendered",this.fire(a,{type:a,chart:this}),this.chartRendered=!0),a="drawn",this.fire(a,{type:a,chart:this})},validateSize:function(){var a=this;a.measure();var b=a.legend;if((a.realWidth!=a.previousWidth||a.realHeight!=a.previousHeight)&&0<a.realWidth&&0<a.realHeight){if(a.sizeChanged=!0,b){clearTimeout(a.legendInitTO);var c=setTimeout(function(){b.invalidateSize()},100);a.timeOuts.push(c),a.legendInitTO=c}a.marginsUpdated="xy"!=a.type?!1:!0,clearTimeout(a.initTO),c=setTimeout(function(){a.initChart()},150),a.timeOuts.push(c),a.initTO=c}a.renderFix(),b&&b.renderFix()},invalidateSize:function(){this.previousHeight=this.previousWidth=0/0,this.invalidateSizeReal()},invalidateSizeReal:function(){var a=this;a.marginsUpdated=!1,clearTimeout(a.validateTO);var b=setTimeout(function(){a.validateSize()},5);a.timeOuts.push(b),a.validateTO=b},validateData:function(a){this.chartCreated&&(this.dataChanged=!0,this.marginsUpdated="xy"!=this.type?!1:!0,this.initChart(a))},validateNow:function(){this.chartRendered=!1,this.write(this.div)},showItem:function(a){a.hidden=!1,this.initChart()},hideItem:function(a){a.hidden=!0,this.initChart()},hideBalloon:function(){var a=this;clearInterval(a.hoverInt),clearTimeout(a.balloonTO),a.hoverInt=setTimeout(function(){a.hideBalloonReal.call(a)},a.hideBalloonTime)},cleanChart:function(){},hideBalloonReal:function(){var a=this.balloon;a&&a.hide()},showBalloon:function(a,b,c,d,e){var f=this;clearTimeout(f.balloonTO),clearInterval(f.hoverInt),f.balloonTO=setTimeout(function(){f.showBalloonReal.call(f,a,b,c,d,e)},1)},showBalloonReal:function(a,b,c,d,e){this.handleMouseMove();var f=this.balloon;f.enabled&&(f.followCursor(!1),f.changeColor(b),!c||f.fixedPosition?(f.setPosition(d,e),f.followCursor(!1)):f.followCursor(!0),a&&f.showBalloon(a))},handleTouchMove:function(a){this.hideBalloon();var b=this.chartDiv;a.touches&&(a=a.touches.item(0),this.mouseX=a.pageX-AmCharts.findPosX(b),this.mouseY=a.pageY-AmCharts.findPosY(b))},handleMouseOver:function(a){AmCharts.resetMouseOver(),this.mouseIsOver=!0},handleMouseOut:function(a){AmCharts.resetMouseOver(),this.mouseIsOver=!1},handleMouseMove:function(a){if(this.mouseIsOver){var b=this.chartDiv;a||(a=window.event);var c,d;if(a){switch(this.posX=AmCharts.findPosX(b),this.posY=AmCharts.findPosY(b),this.mouseMode){case 1:if(c=a.clientX-this.posX,d=a.clientY-this.posY,!this.divIsFixed){var e,f,b=document.body;b&&(e=b.scrollLeft,y1=b.scrollTop),(b=document.documentElement)&&(f=b.scrollLeft,y2=b.scrollTop),e=Math.max(e,f),f=Math.max(y1,y2),c+=e,d+=f}break;case 0:this.divIsFixed?(c=a.clientX-this.posX,d=a.clientY-this.posY):(c=a.pageX-this.posX,d=a.pageY-this.posY)}a.touches&&(a=a.touches.item(0),c=a.pageX-this.posX,d=a.pageY-this.posY),this.mouseX=c-this.dmouseX,this.mouseY=d-this.dmouseY}}},handleTouchStart:function(a){this.handleMouseDown(a)},handleTouchEnd:function(a){AmCharts.resetMouseOver(),this.handleReleaseOutside(a)},handleReleaseOutside:function(a){},handleMouseDown:function(a){AmCharts.resetMouseOver(),this.mouseIsOver=!0,a&&a.preventDefault&&a.preventDefault()},addLegend:function(a,b){a=AmCharts.processObject(a,AmCharts.AmLegend,this.theme),a.divId=b;var c;return c="object"!=typeof b&&b?document.getElementById(b):b,this.legend=a,a.chart=this,c?(a.div=c,a.position="outside",a.autoMargins=!1):a.div=this.legendDiv,c=this.handleLegendEvent,this.listenTo(a,"showItem",c),this.listenTo(a,"hideItem",c),this.listenTo(a,"clickMarker",c),this.listenTo(a,"rollOverItem",c),this.listenTo(a,"rollOutItem",c),this.listenTo(a,"rollOverMarker",c),this.listenTo(a,"rollOutMarker",c),this.listenTo(a,"clickLabel",c),a},removeLegend:function(){this.legend=void 0,this.legendDiv.innerHTML=""},handleResize:function(){(AmCharts.isPercents(this.width)||AmCharts.isPercents(this.height))&&this.invalidateSizeReal(),this.renderFix()},renderFix:function(){if(!AmCharts.VML){var a=this.container;a&&a.renderFix()}},getSVG:function(){return AmCharts.hasSVG?this.container:void 0},animate:function(a,b,c,d,e,f,g){return a["an_"+b]&&AmCharts.removeFromArray(this.animations,a["an_"+b]),c={obj:a,frame:0,attribute:b,from:c,to:d,time:e,effect:f,suffix:g},a["an_"+b]=c,this.animations.push(c),c},setLegendData:function(a){var b=this.legend;b&&b.setData(a)},startInterval:function(){var a=this;clearInterval(a.interval),a.interval=setInterval(function(){a.updateAnimations.call(a)},AmCharts.updateRate)},stopAnim:function(a){AmCharts.removeFromArray(this.animations,a)},updateAnimations:function(){var a;for(this.container&&this.container.update(),a=this.animations.length-1;a>=0;a--){var b=this.animations[a],c=1e3*b.time/AmCharts.updateRate,d=b.frame+1,e=b.obj,f=b.attribute;if(c>=d){b.frame++;var g=Number(b.from),h=Number(b.to)-g,c=AmCharts[b.effect](0,d,g,h,c);0===h?(this.animations.splice(a,1),e.node.style[f]=Number(b.to)+b.suffix):e.node.style[f]=c+b.suffix}else e.node.style[f]=Number(b.to)+b.suffix,this.animations.splice(a,1)}},inIframe:function(){try{return window.self!==window.top}catch(a){return!0}},brr:function(){var a,b=window.location.hostname.split(".");if(2<=b.length&&(a=b[b.length-2]+"."+b[b.length-1]),this.amLink&&(b=this.amLink.parentNode)&&b.removeChild(this.amLink),b=this.creditsPosition,"amcharts.com"!=a||!0===this.inIframe()){var c=a=0,d=this.realWidth,e=this.realHeight;("serial"==this.type||"xy"==this.type)&&(a=this.marginLeftReal,c=this.marginTopReal,d=a+this.plotAreaWidth,e=c+this.plotAreaHeight);var f="http://www.amcharts.com/javascript-charts/",g="JavaScript charts",h="";"ammap"==this.product&&(f="http://www.ammap.com/javascript-maps/",g="Interactive JavaScript maps",h="  map by amCharts");var i=document.createElement("a"),h=document.createTextNode(h);i.setAttribute("href",f),i.setAttribute("title",g),i.appendChild(h),this.chartDiv.appendChild(i),this.amLink=i,f=i.style,f.position="absolute",f.textDecoration="none",f.color=this.color,f.fontFamily=this.fontFamily,f.fontSize=this.fontSize+"px",f.opacity=.7,f.display="block";var g=i.offsetWidth,i=i.offsetHeight,h=5+a,j=c+5;"bottom-left"==b&&(h=5+a,j=e-i-3),"bottom-right"==b&&(h=d-g-5,j=e-i-3),"top-right"==b&&(h=d-g-5,j=c+5),f.left=h+"px",f.top=j+"px"}}}),AmCharts.Slice=AmCharts.Class({construct:function(){}}),AmCharts.SerialDataItem=AmCharts.Class({construct:function(){}}),AmCharts.GraphDataItem=AmCharts.Class({construct:function(){}}),AmCharts.Guide=AmCharts.Class({construct:function(a){this.cname="Guide",AmCharts.applyTheme(this,a,this.cname)}}),AmCharts.AmGraph=AmCharts.Class({construct:function(a){this.cname="AmGraph",this.createEvents("rollOverGraphItem","rollOutGraphItem","clickGraphItem","doubleClickGraphItem","rightClickGraphItem","clickGraph","rollOverGraph","rollOutGraph"),this.type="line",this.stackable=!0,this.columnCount=1,this.columnIndex=0,this.centerCustomBullets=this.showBalloon=!0,this.maxBulletSize=50,this.minBulletSize=4,this.balloonText="[[value]]",this.hidden=this.scrollbar=this.animationPlayed=!1,this.pointPosition="middle",this.depthCount=1,this.includeInMinMax=!0,this.negativeBase=0,this.visibleInLegend=!0,this.showAllValueLabels=!1,this.showBulletsAt=this.showBalloonAt="close",this.lineThickness=1,this.dashLength=0,this.connect=!0,this.lineAlpha=1,this.bullet="none",this.bulletBorderThickness=2,this.bulletBorderAlpha=0,this.bulletAlpha=1,this.bulletSize=8,this.hideBulletsCount=this.bulletOffset=0,this.labelPosition="top",this.cornerRadiusTop=0,this.cursorBulletAlpha=1,this.gradientOrientation="vertical",this.dy=this.dx=0,this.periodValue="",this.clustered=!0,this.periodSpan=1,this.y=this.x=0,this.switchable=!0,this.tcc=this.minDistance=1,AmCharts.applyTheme(this,a,this.cname)},draw:function(){var a=this.chart;isNaN(this.precision)||(this.numberFormatter?this.numberFormatter.precision=this.precision:this.numberFormatter={precision:this.precision,decimalSeparator:a.decimalSeparator,thousandsSeparator:a.thousandsSeparator});var b=a.container;this.container=b,this.destroy();var c=b.set(),d=b.set();this.behindColumns?(a.graphsBehindSet.push(c),a.bulletBehindSet.push(d)):(a.graphsSet.push(c),a.bulletSet.push(d));var e=this.bulletAxis;AmCharts.isString(e)&&(this.bulletAxis=a.getValueAxisById(e)),this.bulletSet=d,this.scrollbar||(e=a.marginLeftReal,a=a.marginTopReal,c.translate(e,a),d.translate(e,a)),b=b.set(),AmCharts.remove(this.columnsSet),c.push(b),this.set=c,this.columnsSet=b,this.columnsArray=[],this.ownColumns=[],this.allBullets=[],this.animationArray=[],AmCharts.ifArray(this.data)&&(c=!1,"xy"==this.chart.type?this.xAxis.axisCreated&&this.yAxis.axisCreated&&(c=!0):this.valueAxis.axisCreated&&(c=!0),!this.hidden&&c&&this.createGraph())},createGraph:function(){var a=this,b=a.chart;if("inside"==a.labelPosition&&"column"!=a.type&&(a.labelPosition="bottom"),a.startAlpha=b.startAlpha,a.seqAn=b.sequencedAnimation,a.baseCoord=a.valueAxis.baseCoord,void 0===a.fillAlphas&&(a.fillAlphas=0),a.bulletColorR=a.bulletColor,void 0===a.bulletColorR&&(a.bulletColorR=a.lineColorR,a.bulletColorNegative=a.negativeLineColor),void 0===a.bulletAlpha&&(a.bulletAlpha=a.lineAlpha),clearTimeout(a.playedTO),!isNaN(a.valueAxis.min)&&!isNaN(a.valueAxis.max)){switch(b.type){case"serial":a.categoryAxis&&(a.createSerialGraph(),"candlestick"==a.type&&1>a.valueAxis.minMaxMultiplier&&a.positiveClip(a.set));break;case"radar":a.createRadarGraph();break;case"xy":a.createXYGraph(),a.positiveClip(a.set)}a.playedTO=setTimeout(function(){a.setAnimationPlayed.call(a)},500*a.chart.startDuration)}},setAnimationPlayed:function(){this.animationPlayed=!0},createXYGraph:function(){var a=[],b=[],c=this.xAxis,d=this.yAxis;this.pmh=d.viH+1,this.pmw=c.viW+1,this.pmy=this.pmx=0;var e;for(e=this.start;e<=this.end;e++){var f=this.data[e].axes[c.id].graphs[this.id],g=f.values,h=g.x,i=g.y,g=c.getCoordinate(h),j=d.getCoordinate(i);!isNaN(h)&&!isNaN(i)&&(a.push(g),b.push(j),(h=this.createBullet(f,g,j,e))||(h=0),i=this.labelText)&&(f=this.createLabel(f,g,j,i),this.allBullets.push(f),this.positionLabel(g,j,f,this.labelPosition,h))}this.drawLineGraph(a,b),this.launchAnimation()},createRadarGraph:function(){var a,b,c,d=this.valueAxis.stackType,e=[],f=[];for(c=this.start;c<=this.end;c++){var g,h=this.data[c].axes[this.valueAxis.id].graphs[this.id];if(g="none"==d||"3d"==d?h.values.value:h.values.close,isNaN(g))this.drawLineGraph(e,f),e=[],f=[];else{var i=this.y-(this.valueAxis.getCoordinate(g)-this.height),j=180-360/(this.end-this.start+1)*c;g=i*Math.sin(j/180*Math.PI),i*=Math.cos(j/180*Math.PI),e.push(g),f.push(i),(j=this.createBullet(h,g,i,c))||(j=0);var k=this.labelText;k&&(h=this.createLabel(h,g,i,k),this.allBullets.push(h),this.positionLabel(g,i,h,this.labelPosition,j)),isNaN(a)&&(a=g),isNaN(b)&&(b=i)}}e.push(a),f.push(b),this.drawLineGraph(e,f),this.launchAnimation()},positionLabel:function(a,b,c,d,e){var f=c.getBBox();switch(d){case"left":a-=(f.width+e)/2+2;break;case"top":b-=(e+f.height)/2+1;break;case"right":a+=(f.width+e)/2+2;break;case"bottom":b+=(e+f.height)/2+1}c.translate(a,b)},getGradRotation:function(){var a=270;return"horizontal"==this.gradientOrientation&&(a=0),this.gradientRotation=a},createSerialGraph:function(){this.dashLengthSwitched=this.fillColorsSwitched=this.lineColorSwitched=void 0;var a=this.chart,b=this.id,c=this.index,d=this.data,e=this.chart.container,f=this.valueAxis,g=this.type,h=this.columnWidthReal,i=this.showBulletsAt;isNaN(this.columnWidth)||(h=this.columnWidth),isNaN(h)&&(h=.8);var j,k,l,m,n,o,p=this.useNegativeColorIfDown,q=this.width,r=this.height,s=this.y,t=this.rotate,u=this.columnCount,v=AmCharts.toCoordinate(this.cornerRadiusTop,h/2),w=this.connect,x=[],y=[],z=this.chart.graphs.length,A=this.dx/this.tcc,B=this.dy/this.tcc,C=f.stackType,D=this.labelPosition,E=this.start,F=this.end,G=this.scrollbar,H=this.categoryAxis,I=this.baseCoord,J=this.negativeBase,K=this.columnIndex,L=this.lineThickness,M=this.lineAlpha,N=this.lineColorR,O=this.dashLength,P=this.set,Q=D,R=this.getGradRotation(),S=this.chart.columnSpacing,T=H.cellWidth,U=(T*h-u)/u;S>U&&(S=U);var V,W,X,Y,Z,$,_,aa=r+1,ba=q+1,ca=0,da=0,ea=this.fillColorsR,fa=this.negativeFillColors,ga=this.negativeLineColor,ha=this.fillAlphas,ia=this.negativeFillAlphas;

"object"==typeof ha&&(ha=ha[0]),"object"==typeof ia&&(ia=ia[0]);var ja=f.getCoordinate(f.min);if(f.logarithmic&&(ja=f.getCoordinate(f.minReal)),this.minCoord=ja,this.resetBullet&&(this.bullet="none"),!(G||"line"!=g&&"smoothedLine"!=g&&"step"!=g||(1==d.length&&"step"!=g&&"none"==this.bullet&&(this.bullet="round",this.resetBullet=!0),!fa&&void 0==ga||p))){var ka=J;ka>f.max&&(ka=f.max),ka<f.min&&(ka=f.min),f.logarithmic&&(ka=f.minReal);var la=f.getCoordinate(ka),ma=f.getCoordinate(f.max);t?(aa=r,ba=Math.abs(ma-la),Y=r,Z=Math.abs(ja-la),_=da=0,f.reversed?(ca=0,$=la):(ca=la,$=0)):(ba=q,aa=Math.abs(ma-la),Z=q,Y=Math.abs(ja-la),$=ca=0,f.reversed?(_=s,da=la):_=la+1)}var na=Math.round;this.pmx=na(ca),this.pmy=na(da),this.pmh=na(aa),this.pmw=na(ba),this.nmx=na($),this.nmy=na(_),this.nmh=na(Y),this.nmw=na(Z),AmCharts.isModern||(this.nmy=this.nmx=0,this.nmh=this.height),this.clustered||(u=1),h="column"==g?(T*h-S*(u-1))/u:T*h,1>h&&(h=1);var oa;if("line"==g||"step"==g||"smoothedLine"==g){if(E>0){for(oa=E-1;oa>-1;oa--)if(V=d[oa],W=V.axes[f.id].graphs[b],X=W.values.value,!isNaN(X)){E=oa;break}if(this.lineColorField)for(oa=E;oa>-1;oa--)if(V=d[oa],W=V.axes[f.id].graphs[b],W.lineColor){this.bulletColorSwitched=this.lineColorSwitched=W.lineColor;break}if(this.fillColorsField)for(oa=E;oa>-1;oa--)if(V=d[oa],W=V.axes[f.id].graphs[b],W.fillColors){this.fillColorsSwitched=W.fillColors;break}if(this.dashLengthField)for(oa=E;oa>-1;oa--)if(V=d[oa],W=V.axes[f.id].graphs[b],!isNaN(W.dashLength)){this.dashLengthSwitched=W.dashLength;break}}if(F<d.length-1)for(oa=F+1;oa<d.length;oa++)if(V=d[oa],W=V.axes[f.id].graphs[b],X=W.values.value,!isNaN(X)){F=oa;break}}F<d.length-1&&F++;var pa=[],qa=[],ra=!1;("line"==g||"step"==g||"smoothedLine"==g)&&(this.stackable&&"regular"==C||"100%"==C||this.fillToGraph)&&(ra=!0);var sa=this.noStepRisers,ta=-1e3,ua=-1e3,va=this.minDistance,wa=!0,xa=!1;for(oa=E;F>=oa;oa++){V=d[oa],W=V.axes[f.id].graphs[b],W.index=oa;var ya,za=0/0;if(p&&void 0==this.openField)for(var Aa=oa+1;Aa<d.length&&(!d[Aa]||!(ya=d[oa+1].axes[f.id].graphs[b])||!ya.values||(za=ya.values.value,isNaN(za)));Aa++);var Ba,Ca,Da,Ea,Fa,Ga,Ha=0/0,Ia=0/0,Ja=0/0,Ka=0/0,La=0/0,Ma=0/0,Na=0/0,Oa=0/0,Pa=0/0,Qa=0/0,Ra=0/0,Sa=0/0,Ta=0/0,Ua=0/0,Va=0/0,Wa=0/0,Xa=0/0,Ya=void 0,Za=ea,$a=ha,_a=N,ab=this.proCandlesticks,bb=this.pattern;void 0!=W.pattern&&(bb=W.pattern),void 0!=W.color&&(Za=W.color),W.fillColors&&(Za=W.fillColors),isNaN(W.alpha)||($a=W.alpha),isNaN(W.dashLength)||(O=W.dashLength);var cb=W.values;if(f.recalculateToPercents&&(cb=W.percents),cb){if(Ua=this.stackable&&"none"!=C&&"3d"!=C?cb.close:cb.value,("candlestick"==g||"ohlc"==g)&&(Ua=cb.close,Wa=cb.low,Na=f.getCoordinate(Wa),Va=cb.high,Pa=f.getCoordinate(Va)),Xa=cb.open,Ja=f.getCoordinate(Ua),isNaN(Xa)||(La=f.getCoordinate(Xa),p&&(za=Xa,Xa=La=0/0)),p&&(void 0==this.openField?ya&&(ya.isNegative=Ua>za?!0:!1,isNaN(za)&&(W.isNegative=!wa)):W.isNegative=za>Ua?!0:!1),!G)switch(this.showBalloonAt){case"close":W.y=Ja;break;case"open":W.y=La;break;case"high":W.y=Pa;break;case"low":W.y=Na}var Ha=V.x[H.id],db=this.periodSpan-1,eb=Math.floor(T/2)+Math.floor(db*T/2),fb=eb,gb=0;if("left"==this.stepDirection&&(gb=(2*T+db*T)/2,Ha-=gb),"start"==this.pointPosition&&(Ha-=T/2+Math.floor(db*T/2),eb=0,fb=Math.floor(T)+Math.floor(db*T)),"end"==this.pointPosition&&(Ha+=T/2+Math.floor(db*T/2),eb=Math.floor(T)+Math.floor(db*T),fb=0),sa){var hb=this.columnWidth;isNaN(hb)||(eb*=hb,fb*=hb)}switch(G||(W.x=Ha),-1e5>Ha&&(Ha=-1e5),Ha>q+1e5&&(Ha=q+1e5),t?(Ia=Ja,Ka=La,La=Ja=Ha,isNaN(Xa)&&!this.fillToGraph&&(Ka=I),Ma=Na,Oa=Pa):(Ka=Ia=Ha,isNaN(Xa)&&!this.fillToGraph&&(La=I)),(!ab&&Xa>Ua||ab&&o>Ua)&&(W.isNegative=!0,fa&&(Za=fa),ia&&($a=ia),void 0!=ga&&(_a=ga)),xa=!1,isNaN(Ua)||(p?Ua>za?(wa&&(xa=!0),wa=!1):(wa||(xa=!0),wa=!0):W.isNegative=J>Ua?!0:!1,o=Ua),g){case"line":isNaN(Ua)?w||(this.drawLineGraph(x,y,pa,qa),x=[],y=[],pa=[],qa=[]):((Math.abs(Ia-ta)>=va||Math.abs(Ja-ua)>=va)&&(x.push(Ia),y.push(Ja),ta=Ia,ua=Ja),Qa=Ia,Ra=Ja,Sa=Ia,Ta=Ja,!ra||isNaN(La)||isNaN(Ka)||(pa.push(Ka),qa.push(La)),(xa||void 0!=W.lineColor||void 0!=W.fillColors||!isNaN(W.dashLength))&&(this.drawLineGraph(x,y,pa,qa),x=[Ia],y=[Ja],pa=[],qa=[],!ra||isNaN(La)||isNaN(Ka)||(pa.push(Ka),qa.push(La)),p?wa?(this.lineColorSwitched=N,this.fillColorsSwitched=ea):(this.lineColorSwitched=ga,this.fillColorsSwitched=fa):(this.lineColorSwitched=W.lineColor,this.fillColorsSwitched=W.fillColors),this.dashLengthSwitched=W.dashLength),W.gap&&(this.drawLineGraph(x,y,pa,qa),x=[],y=[],pa=[],qa=[]));break;case"smoothedLine":isNaN(Ua)?w||(this.drawSmoothedGraph(x,y,pa,qa),x=[],y=[],pa=[],qa=[]):((Math.abs(Ia-ta)>=va||Math.abs(Ja-ua)>=va)&&(x.push(Ia),y.push(Ja),ta=Ia,ua=Ja),Qa=Ia,Ra=Ja,Sa=Ia,Ta=Ja,!ra||isNaN(La)||isNaN(Ka)||(pa.push(Ka),qa.push(La)),void 0==W.lineColor&&void 0==W.fillColors&&isNaN(W.dashLength)||(this.drawSmoothedGraph(x,y,pa,qa),x=[Ia],y=[Ja],pa=[],qa=[],!ra||isNaN(La)||isNaN(Ka)||(pa.push(Ka),qa.push(La)),this.lineColorSwitched=W.lineColor,this.fillColorsSwitched=W.fillColors,this.dashLengthSwitched=W.dashLength),W.gap&&(this.drawSmoothedGraph(x,y,pa,qa),x=[],y=[],pa=[],qa=[]));break;case"step":isNaN(Ua)?w||((1>=this.periodSpan||1<this.periodSpan&&Ia-j>eb+fb)&&(j=k=0/0),this.drawLineGraph(x,y,pa,qa),x=[],y=[],pa=[],qa=[]):(void 0==W.lineColor&&void 0==W.fillColors&&isNaN(W.dashLength)||(this.drawLineGraph(x,y,pa,qa),x=[],y=[],pa=[],qa=[],this.lineColorSwitched=W.lineColor,this.fillColorsSwitched=W.fillColors,this.dashLengthSwitched=W.dashLength),t?(isNaN(j)||(x.push(j),y.push(Ja-eb)),y.push(Ja-eb),x.push(Ia),y.push(Ja+fb),x.push(Ia),!ra||isNaN(La)||isNaN(Ka)||(pa.push(l),qa.push(La-eb),pa.push(Ka),qa.push(La-eb),pa.push(Ka),qa.push(La+fb))):(isNaN(k)||(y.push(k),x.push(j),y.push(k),x.push(Ia-eb)),x.push(Ia-eb),y.push(Ja),x.push(Ia+fb),y.push(Ja),!ra||isNaN(La)||isNaN(Ka)||(pa.push(Ka-eb),qa.push(m),pa.push(Ka-eb),qa.push(La),pa.push(Ka+fb),qa.push(La))),j=Ia,k=Ja,l=Ka,m=La,Qa=Ia,Ra=Ja,Sa=Ia,Ta=Ja,xa&&(this.drawLineGraph(x,y,pa,qa),x=[],y=[],pa=[],qa=[],p&&(wa?(this.lineColorSwitched=N,this.fillColorsSwitched=ea):(this.lineColorSwitched=ga,this.fillColorsSwitched=fa))),(sa||W.gap)&&(j=k=0/0,this.drawLineGraph(x,y,pa,qa),x=[],y=[],pa=[],qa=[]));break;case"column":Fa=_a,void 0!=W.lineColor&&(Fa=W.lineColor);var ib=this.topRadius;if(!isNaN(Ua)){p||(W.isNegative=J>Ua?!0:!1),W.isNegative&&(fa&&(Za=fa),void 0!=ga&&(Fa=ga));var jb=f.min,kb=f.max;if(!(jb>Ua&&jb>Xa||Ua>kb&&Xa>kb))if(t){"3d"==C?(Ca=Ja-(u/2-this.depthCount+1)*(h+S)+S/2+B*K,Ba=Ka+A*K):(Ca=Math.floor(Ja-(u/2-K)*(h+S)+S/2),Ba=Ka),Da=h,Qa=Ia,Ra=Ca+h/2,Sa=Ia,Ta=Ca+h/2,Ca+Da>r+K*B&&(Da=r-Ca+K*B),K*B>Ca&&(Da+=Ca,Ca=K*B),Ea=Ia-Ka;var lb=Ba;Ba=AmCharts.fitToBounds(Ba,0,q),Ea+=lb-Ba,Ea=AmCharts.fitToBounds(Ea,-Ba,q-Ba+A*K),r>Ca&&Da>0&&(Ya=new AmCharts.Cuboid(e,Ea,Da,A-a.d3x,B-a.d3y,Za,$a,L,Fa,M,R,v,t,O,bb,ib),"bottom"!=D&&"inside"!=D&&"middle"!=D&&(D=f.reversed?"left":"right",0>Ua&&(D=f.reversed?"right":"left")),"regular"==C||"100%"==C)&&(Qa+=this.dx)}else{"3d"==C?(Ba=Ia-(u/2-this.depthCount+1)*(h+S)+S/2+A*K,Ca=La+B*K):(Ba=Ia-(u/2-K)*(h+S)+S/2,Ca=La),Da=h,Qa=Ba+h/2,Ra=Ja,Sa=Ba+h/2,Ta=Ja,Ba+Da>q+K*A&&(Da=q-Ba+K*A),K*A>Ba&&(Da+=Ba-K*A,Ba=K*A),Ea=Ja-La;var mb=Ca;Ca=AmCharts.fitToBounds(Ca,this.dy,r),Ea+=mb-Ca,Ea=AmCharts.fitToBounds(Ea,-Ca+B*K,r-Ca),q+K*A>Ba&&Da>0&&(this.showOnAxis&&(Ca-=B/2),Ya=new AmCharts.Cuboid(e,Da,Ea,A-a.d3x,B-a.d3y,Za,$a,L,Fa,this.lineAlpha,R,v,t,O,bb,ib),0>Ua&&"middle"!=D&&"inside"!=D?D="bottom":(D=Q,("regular"==C||"100%"==C)&&(Ra+=this.dy)))}Ya&&(Ga=Ya.set,W.columnGraphics=Ga,Ga.translate(Ba,Ca),this.columnsSet.push(Ga),(W.url||this.showHandOnHover)&&Ga.setAttr("cursor","pointer"),!G)&&("none"==C&&(n=t?(this.end+1-oa)*z-c:z*oa+c),"3d"==C&&(t?(n=(this.end+1-oa)*z-c-1e3*this.depthCount,Qa+=A*this.columnIndex,Sa+=A*this.columnIndex,W.y+=A*this.columnIndex):(n=(z-c)*(oa+1)+1e3*this.depthCount,Qa+=3,Ra+=B*this.columnIndex+7,Ta+=B*this.columnIndex,W.y+=B*this.columnIndex)),("regular"==C||"100%"==C)&&("inside"!=D&&(D="middle"),n=t?0<cb.value?(this.end+1-oa)*z+c:(this.end+1-oa)*z-c:0<cb.value?z*oa+c:z*oa-c),this.columnsArray.push({column:Ya,depth:n}),W.x=t?Ca+Da/2:Ba+Da/2,this.ownColumns.push(Ya),this.animateColumns(Ya,oa,Ia,Ka,Ja,La),this.addListeners(Ga,W))}break;case"candlestick":if(!isNaN(Xa)&&!isNaN(Ua)){var nb,ob;if(Fa=_a,void 0!=W.lineColor&&(Fa=W.lineColor),t){if(Ca=Ja-h/2,Ba=Ka,Da=h,Ca+Da>r&&(Da=r-Ca),0>Ca&&(Da+=Ca,Ca=0),r>Ca&&Da>0){var pb,qb;Ua>Xa?(pb=[Ia,Oa],qb=[Ka,Ma]):(pb=[Ka,Oa],qb=[Ia,Ma]),!isNaN(Oa)&&!isNaN(Ma)&&r>Ja&&Ja>0&&(nb=AmCharts.line(e,pb,[Ja,Ja],Fa,M,L),ob=AmCharts.line(e,qb,[Ja,Ja],Fa,M,L)),Ea=Ia-Ka,Ya=new AmCharts.Cuboid(e,Ea,Da,A,B,Za,ha,L,Fa,M,R,v,t,O,bb)}}else if(Ba=Ia-h/2,Ca=La+L/2,Da=h,Ba+Da>q&&(Da=q-Ba),0>Ba&&(Da+=Ba,Ba=0),Ea=Ja-La,q>Ba&&Da>0){ab&&Ua>=Xa&&($a=0);var rb,sb,Ya=new AmCharts.Cuboid(e,Da,Ea,A,B,Za,$a,L,Fa,M,R,v,t,O,bb);Ua>Xa?(rb=[Ja,Pa],sb=[La,Na]):(rb=[La,Pa],sb=[Ja,Na]),!isNaN(Pa)&&!isNaN(Na)&&q>Ia&&Ia>0&&(nb=AmCharts.line(e,[Ia,Ia],rb,Fa,M,L),ob=AmCharts.line(e,[Ia,Ia],sb,Fa,M,L))}Ya&&(Ga=Ya.set,W.columnGraphics=Ga,P.push(Ga),Ga.translate(Ba,Ca-L/2),(W.url||this.showHandOnHover)&&Ga.setAttr("cursor","pointer"),nb&&(P.push(nb),P.push(ob)),Qa=Ia,Ra=Ja,t?(Ta=Ja,Sa=Ia,"open"==i&&(Sa=Ka),"high"==i&&(Sa=Oa),"low"==i&&(Sa=Ma)):(Ta=Ja,"open"==i&&(Ta=La),"high"==i&&(Ta=Pa),"low"==i&&(Ta=Na),Sa=Ia),G||(W.x=t?Ca+Da/2:Ba+Da/2,this.animateColumns(Ya,oa,Ia,Ka,Ja,La),this.addListeners(Ga,W)))}break;case"ohlc":if(!(isNaN(Xa)||isNaN(Va)||isNaN(Wa)||isNaN(Ua))){Xa>Ua&&(W.isNegative=!0,void 0!=ga&&(_a=ga));var tb,ub,vb;if(t){var wb=Ja-h/2,wb=AmCharts.fitToBounds(wb,0,r),xb=AmCharts.fitToBounds(Ja,0,r),yb=Ja+h/2,yb=AmCharts.fitToBounds(yb,0,r);ub=AmCharts.line(e,[Ka,Ka],[wb,xb],_a,M,L,O),Ja>0&&r>Ja&&(tb=AmCharts.line(e,[Ma,Oa],[Ja,Ja],_a,M,L,O)),vb=AmCharts.line(e,[Ia,Ia],[xb,yb],_a,M,L,O),Ta=Ja,Sa=Ia,"open"==i&&(Sa=Ka),"high"==i&&(Sa=Oa),"low"==i&&(Sa=Ma)}else{var zb=Ia-h/2,zb=AmCharts.fitToBounds(zb,0,q),Ab=AmCharts.fitToBounds(Ia,0,q),Bb=Ia+h/2,Bb=AmCharts.fitToBounds(Bb,0,q);ub=AmCharts.line(e,[zb,Ab],[La,La],_a,M,L,O),Ia>0&&q>Ia&&(tb=AmCharts.line(e,[Ia,Ia],[Na,Pa],_a,M,L,O)),vb=AmCharts.line(e,[Ab,Bb],[Ja,Ja],_a,M,L,O),Ta=Ja,"open"==i&&(Ta=La),"high"==i&&(Ta=Pa),"low"==i&&(Ta=Na),Sa=Ia}P.push(ub),P.push(tb),P.push(vb),Qa=Ia,Ra=Ja}}if(!G&&!isNaN(Ua)){var Cb=this.hideBulletsCount;if(this.end-this.start<=Cb||0===Cb){var Db=this.createBullet(W,Sa,Ta,oa);Db||(Db=0);var Eb=this.labelText;if(Eb){var Fb=this.createLabel(W,0,0,Eb),Gb=0,Hb=0,Ib=Fb.getBBox(),Jb=Ib.width,Kb=Ib.height;switch(D){case"left":Gb=-(Jb/2+Db/2+3);break;case"top":Hb=-(Kb/2+Db/2+3);break;case"right":Gb=Db/2+2+Jb/2;break;case"bottom":t&&"column"==g?(Qa=I,0>Ua||Ua>0&&f.reversed?(Gb=-6,Fb.attr({"text-anchor":"end"})):(Gb=6,Fb.attr({"text-anchor":"start"}))):(Hb=Db/2+Kb/2,Fb.x=-(Jb/2+2));break;case"middle":"column"==g&&(t?(Hb=-(Kb/2)+this.fontSize/2,Gb=-(Ia-Ka)/2-A,Math.abs(Ia-Ka)<Jb&&!this.showAllValueLabels&&(Fb.remove(),Fb=null)):(Hb=-(Ja-La)/2-B,Math.abs(Ja-La)<Kb&&!this.showAllValueLabels&&(Fb.remove(),Fb=null)));break;case"inside":t?(Hb=-(Kb/2)+this.fontSize/2,Gb=0>Ea?Jb/2+6:-Jb/2-6):Hb=0>Ea?Kb:-Kb}if(Fb){if(isNaN(Ra)||isNaN(Qa))Fb.remove(),Fb=null;else if(Qa+=Gb,Ra+=Hb,Fb.translate(Qa,Ra),t)(0>Ra||Ra>r)&&(Fb.remove(),Fb=null);else{var Lb=0;"3d"==C&&(Lb=A*K),(0>Qa||Qa>q+Lb)&&(Fb.remove(),Fb=null)}Fb&&this.allBullets.push(Fb)}}if("regular"==C||"100%"==C){var Mb=f.totalText;if(Mb){var Nb=this.createLabel(W,0,0,Mb,f.totalTextColor);this.allBullets.push(Nb);var Ob,Pb,Qb=Nb.getBBox(),Rb=Qb.width,Sb=Qb.height,Tb=f.totals[oa];Tb&&Tb.remove();var Ub=0;"column"!=g&&(Ub=Db),t?(Pb=Ja,Ob=0>Ua?Ia-Rb/2-2-Ub:Ia+Rb/2+3+Ub):(Ob=Ia,Pb=0>Ua?Ja+Sb/2+Ub:Ja-Sb/2-3-Ub),Nb.translate(Ob,Pb),f.totals[oa]=Nb,t?(0>Pb||Pb>r)&&Nb.remove():(0>Ob||Ob>q)&&Nb.remove()}}}}}}("line"==g||"step"==g||"smoothedLine"==g)&&("smoothedLine"==g?this.drawSmoothedGraph(x,y,pa,qa):this.drawLineGraph(x,y,pa,qa),G||this.launchAnimation()),this.bulletsHidden&&this.hideBullets()},animateColumns:function(a,b,c,d,e,f){var g=this;c=g.chart.startDuration,c>0&&!g.animationPlayed&&(g.seqAn?(a.set.hide(),g.animationArray.push(a),a=setTimeout(function(){g.animate.call(g)},c/(g.end-g.start+1)*(b-g.start)*1e3),g.timeOuts.push(a)):g.animate(a))},createLabel:function(a,b,c,d,e){var f=this.chart,g=a.labelColor;g||(g=this.color),g||(g=f.color),e&&(g=e),e=this.fontSize,void 0===e&&(this.fontSize=e=f.fontSize);var h=this.labelFunction;return d=f.formatString(d,a),d=AmCharts.cleanFromEmpty(d),h&&(d=h(a,d)),a=AmCharts.text(this.container,d,g,f.fontFamily,e),a.node.style.pointerEvents="none",a.translate(b,c),this.bulletSet.push(a),a},positiveClip:function(a){a.clipRect(this.pmx,this.pmy,this.pmw,this.pmh)},negativeClip:function(a){a.clipRect(this.nmx,this.nmy,this.nmw,this.nmh)},drawLineGraph:function(a,b,c,d){var e=this;if(1<a.length){var f=e.set,g=e.chart,h=e.container,i=h.set(),j=h.set();f.push(j),f.push(i);var k=e.lineAlpha,l=e.lineThickness,f=e.fillAlphas,m=e.lineColorR,n=e.negativeLineAlpha;isNaN(n)&&(n=k);var o=e.lineColorSwitched;o&&(m=o);var o=e.fillColorsR,p=e.fillColorsSwitched;p&&(o=p);var q=e.dashLength;(p=e.dashLengthSwitched)&&(q=p);var p=e.negativeLineColor,r=e.negativeFillColors,s=e.negativeFillAlphas,t=e.baseCoord;0!==e.negativeBase&&(t=e.valueAxis.getCoordinate(e.negativeBase)),k=AmCharts.line(h,a,b,m,k,l,q,!1,!0),i.push(k),i.click(function(a){e.handleGraphEvent(a,"clickGraph")}).mouseover(function(a){e.handleGraphEvent(a,"rollOverGraph")}).mouseout(function(a){e.handleGraphEvent(a,"rollOutGraph")}),void 0===p||e.useNegativeColorIfDown||(l=AmCharts.line(h,a,b,p,n,l,q,!1,!0),j.push(l)),(f>0||s>0)&&(l=a.join(";").split(";"),n=b.join(";").split(";"),k=g.type,"serial"==k?0<c.length?(c.reverse(),d.reverse(),l=a.concat(c),n=b.concat(d)):e.rotate?(n.push(n[n.length-1]),l.push(t),n.push(n[0]),l.push(t),n.push(n[0]),l.push(l[0])):(l.push(l[l.length-1]),n.push(t),l.push(l[0]),n.push(t),l.push(a[0]),n.push(n[0])):"xy"==k&&(b=e.fillToAxis)&&(AmCharts.isString(b)&&(b=g.getValueAxisById(b)),"H"==b.orientation?(t="top"==b.position?0:b.viH,l.push(l[l.length-1]),n.push(t),l.push(l[0]),n.push(t),l.push(a[0]),n.push(n[0])):(t="left"==b.position?0:b.viW,n.push(n[n.length-1]),l.push(t),n.push(n[0]),l.push(t),n.push(n[0]),l.push(l[0]))),a=e.gradientRotation,f>0&&(g=AmCharts.polygon(h,l,n,o,f,1,"#000",0,a),g.pattern(e.pattern),i.push(g)),(r||void 0!==p)&&(isNaN(s)&&(s=f),r||(r=p),h=AmCharts.polygon(h,l,n,r,s,1,"#000",0,a),h.pattern(e.pattern),j.push(h),j.click(function(a){e.handleGraphEvent(a,"clickGraph")}).mouseover(function(a){e.handleGraphEvent(a,"rollOverGraph")}).mouseout(function(a){e.handleGraphEvent(a,"rollOutGraph")}))),e.applyMask(j,i)}},applyMask:function(a,b){var c=a.length();"serial"!=this.chart.type||this.scrollbar||(this.positiveClip(b),c>0&&this.negativeClip(a))},drawSmoothedGraph:function(a,b,c,d){if(1<a.length){var e=this.set,f=this.container,g=f.set(),h=f.set();e.push(h),e.push(g);var i=this.lineAlpha,j=this.lineThickness,e=this.dashLength,k=this.fillAlphas,l=this.lineColorR,m=this.fillColorsR,n=this.negativeLineColor,o=this.negativeFillColors,p=this.negativeFillAlphas,q=this.baseCoord,r=this.lineColorSwitched;r&&(l=r),(r=this.fillColorsSwitched)&&(m=r),r=this.negativeLineAlpha,isNaN(r)&&(r=i),i=new AmCharts.Bezier(f,a,b,l,i,j,m,0,e),g.push(i.path),void 0!==n&&(j=new AmCharts.Bezier(f,a,b,n,r,j,m,0,e),h.push(j.path)),k>0&&(i=a.join(";").split(";"),l=b.join(";").split(";"),j="",0<c.length?(c.push("M"),d.push("M"),c.reverse(),d.reverse(),i=a.concat(c),l=b.concat(d)):(this.rotate?(j+=" L"+q+","+b[b.length-1],j+=" L"+q+","+b[0]):(j+=" L"+a[a.length-1]+","+q,j+=" L"+a[0]+","+q),j+=" L"+a[0]+","+b[0]),c=new AmCharts.Bezier(f,i,l,0/0,0,0,m,k,e,j),c.path.pattern(this.pattern),g.push(c.path),o||void 0!==n)&&(p||(p=k),o||(o=n),a=new AmCharts.Bezier(f,a,b,0/0,0,0,o,p,e,j),a.path.pattern(this.pattern),h.push(a.path)),this.applyMask(h,g)}},launchAnimation:function(){var a=this,b=a.chart.startDuration;if(b>0&&!a.animationPlayed){var c=a.set,d=a.bulletSet;AmCharts.VML||(c.attr({opacity:a.startAlpha}),d.attr({opacity:a.startAlpha})),c.hide(),d.hide(),a.seqAn?(b=setTimeout(function(){a.animateGraphs.call(a)},a.index*b*1e3),a.timeOuts.push(b)):a.animateGraphs()}},animateGraphs:function(){var a=this.chart,b=this.set,c=this.bulletSet,d=this.x,e=this.y;b.show(),c.show();var f=a.startDuration,a=a.startEffect;b&&(this.rotate?(b.translate(-1e3,e),c.translate(-1e3,e)):(b.translate(d,-1e3),c.translate(d,-1e3)),b.animate({opacity:1,translate:d+","+e},f,a),c.animate({opacity:1,translate:d+","+e},f,a))},animate:function(a){var b=this.chart,c=this.animationArray;!a&&0<c.length&&(a=c[0],c.shift()),c=AmCharts[AmCharts.getEffect(b.startEffect)],b=b.startDuration,a&&(this.rotate?a.animateWidth(b,c):a.animateHeight(b,c),a.set.show())},legendKeyColor:function(){var a=this.legendColor,b=this.lineAlpha;return void 0===a&&(a=this.lineColorR,0===b&&(b=this.fillColorsR)&&(a="object"==typeof b?b[0]:b)),a},legendKeyAlpha:function(){var a=this.legendAlpha;return void 0===a&&(a=this.lineAlpha,this.fillAlphas>a&&(a=this.fillAlphas),0===a&&(a=this.bulletAlpha),0===a&&(a=1)),a},createBullet:function(a,b,c,d){if(!isNaN(b)&&!isNaN(c)){d=this.container;var e=this.bulletOffset,f=this.bulletSize;isNaN(a.bulletSize)||(f=a.bulletSize);var g=a.values.value,h=this.maxValue,i=this.minValue,j=this.maxBulletSize,k=this.minBulletSize;isNaN(h)||(isNaN(g)||(f=(g-i)/(h-i)*(j-k)+k),i==h&&(f=j)),h=f,this.bulletAxis&&(f=a.values.error,isNaN(f)||(g=f),f=this.bulletAxis.stepWidth*g),f<this.minBulletSize&&(f=this.minBulletSize),this.rotate?b=a.isNegative?b-e:b+e:c=a.isNegative?c+e:c-e;var l,k=this.bulletColorR;a.lineColor&&(this.bulletColorSwitched=a.lineColor),this.bulletColorSwitched&&(k=this.bulletColorSwitched),a.isNegative&&void 0!==this.bulletColorNegative&&(k=this.bulletColorNegative),void 0!==a.color&&(k=a.color);var m;"xy"==this.chart.type&&this.valueField&&(m=this.pattern,a.pattern&&(m=a.pattern)),e=this.bullet,a.bullet&&(e=a.bullet);var g=this.bulletBorderThickness,i=this.bulletBorderColorR,j=this.bulletBorderAlpha,n=this.bulletAlpha;i||(i=k),this.useLineColorForBulletBorder&&(i=this.lineColorR);var o=a.alpha;return isNaN(o)||(n=o),("none"!=this.bullet||a.bullet)&&(l=AmCharts.bullet(d,e,f,k,n,g,i,j,h,0,m)),(this.customBullet||a.customBullet)&&(m=this.customBullet,a.customBullet&&(m=a.customBullet),m&&(l&&l.remove(),"function"==typeof m?(l=new m,l.chart=this.chart,a.bulletConfig&&(l.availableSpace=c,l.graph=this,l.graphDataItem=a,l.bulletY=c,a.bulletConfig.minCoord=this.minCoord-c,l.bulletConfig=a.bulletConfig),l.write(d),l=l.set):(this.chart.path&&(m=this.chart.path+m),l=d.set(),d=d.image(m,0,0,f,f),l.push(d),this.centerCustomBullets&&d.translate(-f/2,-f/2)))),l&&((a.url||this.showHandOnHover)&&l.setAttr("cursor","pointer"),"serial"==this.chart.type&&(0>b-0||b-0>this.width||-f/2>c||c-0>this.height)&&(l.remove(),l=null),l&&(this.bulletSet.push(l),l.translate(b,c),this.addListeners(l,a),this.allBullets.push(l)),a.bx=b,a.by=c),a.bulletGraphics=l,f}},showBullets:function(){var a,b=this.allBullets;for(this.bulletsHidden=!1,a=0;a<b.length;a++)b[a].show()},hideBullets:function(){var a,b=this.allBullets;for(this.bulletsHidden=!0,a=0;a<b.length;a++)b[a].hide()},addListeners:function(a,b){var c=this;a.mouseover(function(a){c.handleRollOver(b,a)}).mouseout(function(a){c.handleRollOut(b,a)}).touchend(function(a){c.handleRollOver(b,a),c.chart.panEventsEnabled&&c.handleClick(b,a)}).touchstart(function(a){c.handleRollOver(b,a)}).click(function(a){c.handleClick(b,a)}).dblclick(function(a){c.handleDoubleClick(b,a)}).contextmenu(function(a){c.handleRightClick(b,a)})},handleRollOver:function(a,b){if(a){var c=this.chart,d={type:"rollOverGraphItem",item:a,index:a.index,graph:this,target:this,chart:this.chart,event:b};if(this.fire("rollOverGraphItem",d),c.fire("rollOverGraphItem",d),clearTimeout(c.hoverInt),d=this.showBalloon,c.chartCursor&&"serial"==c.type&&(d=!1,!c.chartCursor.valueBalloonsEnabled&&this.showBalloon&&(d=!0)),d){var d=c.formatString(this.balloonText,a,!0),e=this.balloonFunction;e&&(d=e(a,a.graph)),d=AmCharts.cleanFromEmpty(d),e=c.getBalloonColor(this,a),c.balloon.showBullet=!1,c.balloon.pointerOrientation="V";var f=a.x,g=a.y;c.rotate&&(f=a.y,g=a.x),c.showBalloon(d,e,!0,f+c.marginLeftReal,g+c.marginTopReal)}}this.handleGraphEvent(b,"rollOverGraph")},handleRollOut:function(a,b){if(this.chart.hideBalloon(),a){var c={type:"rollOutGraphItem",item:a,index:a.index,graph:this,target:this,chart:this.chart,event:b};this.fire("rollOutGraphItem",c),this.chart.fire("rollOutGraphItem",c)}this.handleGraphEvent(b,"rollOutGraph")},handleClick:function(a,b){if(a){var c={type:"clickGraphItem",item:a,index:a.index,graph:this,target:this,chart:this.chart,event:b};this.fire("clickGraphItem",c),this.chart.fire("clickGraphItem",c),AmCharts.getURL(a.url,this.urlTarget)}this.handleGraphEvent(b,"clickGraph")},handleGraphEvent:function(a,b){var c={type:b,graph:this,target:this,chart:this.chart,event:a};this.fire(b,c),this.chart.fire(b,c)},handleRightClick:function(a,b){if(a){var c={type:"rightClickGraphItem",item:a,index:a.index,graph:this,target:this,chart:this.chart,event:b};this.fire("rightClickGraphItem",c),this.chart.fire("rightClickGraphItem",c)}},handleDoubleClick:function(a,b){if(a){var c={type:"doubleClickGraphItem",item:a,index:a.index,graph:this,target:this,chart:this.chart,event:b};this.fire("doubleClickGraphItem",c),this.chart.fire("doubleClickGraphItem",c)}},zoom:function(a,b){this.start=a,this.end=b,this.draw()},changeOpacity:function(a){var b=this.set;if(b&&b.setAttr("opacity",a),b=this.ownColumns){var c;for(c=0;c<b.length;c++){var d=b[c].set;d&&d.setAttr("opacity",a)}}(b=this.bulletSet)&&b.setAttr("opacity",a)},destroy:function(){AmCharts.remove(this.set),AmCharts.remove(this.bulletSet);var a=this.timeOuts;if(a){var b;for(b=0;b<a.length;b++)clearTimeout(a[b])}this.timeOuts=[]}}),AmCharts.ChartCursor=AmCharts.Class({construct:function(a){this.cname="ChartCursor",this.createEvents("changed","zoomed","onHideCursor","draw","selected","moved"),this.enabled=!0,this.cursorAlpha=1,this.selectionAlpha=.2,this.cursorColor="#CC0000",this.categoryBalloonAlpha=1,this.color="#FFFFFF",this.type="cursor",this.zoomed=!1,this.zoomable=!0,this.pan=!1,this.categoryBalloonDateFormat="MMM DD, YYYY",this.categoryBalloonEnabled=this.valueBalloonsEnabled=!0,this.rolledOver=!1,this.cursorPosition="middle",this.bulletsEnabled=this.skipZoomDispatch=!1,this.bulletSize=8,this.selectWithoutZooming=this.oneBalloonOnly=!1,this.graphBulletSize=1.7,this.animationDuration=.3,this.zooming=!1,this.adjustment=0,this.avoidBalloonOverlapping=!0,AmCharts.applyTheme(this,a,this.cname)},draw:function(){var a=this;a.destroy();var b=a.chart,c=b.container;a.rotate=b.rotate,a.container=c,c=c.set(),c.translate(a.x,a.y),a.set=c,b.cursorSet.push(c),c=new AmCharts.AmBalloon,c.chart=b,a.categoryBalloon=c,AmCharts.copyProperties(b.balloon,c),c.cornerRadius=0,c.shadowAlpha=0,c.borderThickness=1,c.borderAlpha=1,c.showBullet=!1;var d=a.categoryBalloonColor;if(void 0===d&&(d=a.cursorColor),c.fillColor=d,c.fillAlpha=a.categoryBalloonAlpha,c.borderColor=d,c.color=a.color,d=a.valueLineAxis,AmCharts.isString(d)&&(d=b.getValueAxisById(d)),d||(d=b.valueAxes[0]),a.valueLineAxis=d,a.valueLineBalloonEnabled&&(d=new AmCharts.AmBalloon,a.vaBalloon=d,AmCharts.copyProperties(c,d),d.animationDuration=0,a.rotate||(d.pointerOrientation="H")),a.rotate&&(c.pointerOrientation="H"),a.extraWidth=0,a.prevX=[],a.prevY=[],a.prevTX=[],a.prevTY=[],a.valueBalloonsEnabled)for(c=0;c<b.graphs.length;c++)d=new AmCharts.AmBalloon,d.chart=b,AmCharts.copyProperties(b.balloon,d),b.graphs[c].valueBalloon=d;"cursor"==a.type?a.createCursor():a.createCrosshair(),a.interval=setInterval(function(){a.detectMovement.call(a)},40)},updateData:function(){var a=this.chart;this.data=a.chartData,this.firstTime=a.firstTime,this.lastTime=a.lastTime},createCursor:function(){var a,b,c,d,e=this.chart,f=this.cursorAlpha,g=e.categoryAxis,h=this.categoryBalloon;c=e.dx,d=e.dy;var i=this.width,j=this.height,e=e.rotate;h.pointerWidth=g.tickLength,e?(a=[0,i,i+c],b=[0,0,d],c=[c,0,0],d=[d,0,j]):(a=[c,0,0],b=[d,0,j],c=[0,i,i+c],d=[0,0,d]),this.line=a=AmCharts.line(this.container,a,b,this.cursorColor,f,1),(b=this.fullRectSet)?(b.push(a),b.translate(this.x,this.y)):this.set.push(a),this.valueLineEnabled&&(a=this.valueLineAlpha,isNaN(a)||(f=a),this.vLine=f=AmCharts.line(this.container,c,d,this.cursorColor,f,1),this.set.push(f)),this.setBalloonBounds(h,g,e),(g=this.vaBalloon)&&this.setBalloonBounds(g,this.valueLineAxis,!e),this.hideCursor()},createCrosshair:function(){var a=this.cursorAlpha,b=this.container,c=AmCharts.line(b,[0,0],[0,this.height],this.cursorColor,a,1),a=AmCharts.line(b,[0,this.width],[0,0],this.cursorColor,a,1);this.set.push(c),this.set.push(a),this.vLine=c,this.hLine=a,this.hideCursor()},detectMovement:function(){var a=this.chart;if(a.mouseIsOver){var b=a.mouseX-this.x,c=a.mouseY-this.y;b>-.5&&b<this.width+1&&c>0&&c<this.height?(this.drawing?this.rolledOver||a.setMouseCursor("crosshair"):this.pan&&(this.rolledOver||a.setMouseCursor("move")),this.rolledOver=!0,(this.valueLineEnabled||this.valueLineBalloonEnabled)&&this.updateVLine(b,c),this.setPosition()):this.rolledOver&&(this.handleMouseOut(),this.rolledOver=!1)}else this.rolledOver&&(this.handleMouseOut(),this.rolledOver=!1)},updateVLine:function(a,b){var c=this.vLine,d=this.vaBalloon;if((c||d)&&!this.panning&&!this.drawing){c&&c.show();var e,f=this.valueLineAxis,g=this.rotate;if(g?(c&&c.translate(a,0),f&&(e=f.coordinateToValue(a)),c=a):(c&&c.translate(0,b),f&&(e=f.coordinateToValue(b)),c=b-1),d&&!isNaN(e)&&this.prevLineValue!=e){var h=f.formatValue(e,!0);d&&(this.setBalloonPosition(d,f,c,!g),d.showBalloon(h))}this.prevLineValue=e}},getMousePosition:function(){var a,b=this.width,c=this.height;return a=this.chart,this.rotate?(a=a.mouseY-this.y,0>a&&(a=0),a>c&&(a=c)):(a=a.mouseX-this.x-1,0>a&&(a=0),a>b&&(a=b)),a},updateCrosshair:function(){var a=this.chart,b=a.mouseX-this.x,c=a.mouseY-this.y,d=this.vLine,e=this.hLine,b=AmCharts.fitToBounds(b,0,this.width),c=AmCharts.fitToBounds(c,0,this.height);0<this.cursorAlpha&&(d.show(),e.show(),d.translate(b,0),e.translate(0,c)),this.zooming&&(a.hideXScrollbar&&(b=0/0),a.hideYScrollbar&&(c=0/0),this.updateSelectionSize(b,c)),this.fireMoved(),a.mouseIsOver||this.zooming||this.hideCursor()},fireMoved:function(){var a=this.chart,b={type:"moved",target:this};b.chart=a,b.zooming=this.zooming,b.x=a.mouseX-this.x,b.y=a.mouseY-this.y,this.fire("moved",b)},updateSelectionSize:function(a,b){AmCharts.remove(this.selection);var c=this.selectionPosX,d=this.selectionPosY,e=0,f=0,g=this.width,h=this.height;isNaN(a)||(c>a&&(e=a,g=c-a),a>c&&(e=c,g=a-c),c==a&&(e=a,g=0),g+=this.extraWidth,e-=this.extraWidth/2),isNaN(b)||(d>b&&(f=b,h=d-b),b>d&&(f=d,h=b-d),d==b&&(f=b,h=0),h+=this.extraWidth,f-=this.extraWidth/2),g>0&&h>0&&(c=AmCharts.rect(this.container,g,h,this.cursorColor,this.selectionAlpha),c.translate(e+this.x,f+this.y),this.selection=c)},arrangeBalloons:function(){var a=this.valueBalloons,b=this.x,c=this.y,d=this.height+c;a.sort(this.compareY);var e;for(e=0;e<a.length;e++){var f=a[e].balloon;f.setBounds(b,c,b+this.width,d),f.prevX=this.prevX[e],f.prevY=this.prevY[e],f.prevTX=this.prevTX[e],f.prevTY=this.prevTY[e],f.draw(),d=f.yPos-3}this.arrangeBalloons2()},compareY:function(a,b){return a.yy<b.yy?1:-1},arrangeBalloons2:function(){var a=this.valueBalloons;a.reverse();var b,c,d,e=this.x,f=a.length;for(d=0;f>d;d++){var g=a[d].balloon;b=g.bottom;var h=g.bottom-g.yPos,i=f-d-1;d>0&&c+3>b-h&&(g.setBounds(e,c+3,e+this.width,c+h+3),g.prevX=this.prevX[i],g.prevY=this.prevY[i],g.prevTX=this.prevTX[i],g.prevTY=this.prevTY[i],g.draw()),g.set&&g.set.show(),this.prevX[i]=g.prevX,this.prevY[i]=g.prevY,this.prevTX[i]=g.prevTX,this.prevTY[i]=g.prevTY,c=g.bottom}},showBullets:function(){AmCharts.remove(this.allBullets);var a=this.container,b=a.set();this.set.push(b),this.set.show(),this.allBullets=b;var c,b=this.chart.graphs;for(c=0;c<b.length;c++){var d=b[c];if(!d.hidden&&d.balloonText){var e=this.data[this.index].axes[d.valueAxis.id].graphs[d.id],f=e.y;if(!isNaN(f)){var g,h;g=e.x,this.rotate?(h=f,f=g):h=g,d=AmCharts.circle(a,this.bulletSize/2,this.chart.getBalloonColor(d,e,!0),d.cursorBulletAlpha),d.translate(h,f),this.allBullets.push(d)}}}},destroy:function(){this.clear(),AmCharts.remove(this.selection),this.selection=null;var a=this.categoryBalloon;a&&a.destroy(),(a=this.vaBalloon)&&a.destroy(),this.destroyValueBalloons(),AmCharts.remove(this.set)},clear:function(){clearInterval(this.interval)},destroyValueBalloons:function(){var a=this.valueBalloons;if(a){var b;for(b=0;b<a.length;b++)a[b].balloon.hide()}},zoom:function(a,b,c,d){var e=this.chart;this.destroyValueBalloons(),this.zooming=!1;var f;this.rotate?this.selectionPosY=f=e.mouseY:this.selectionPosX=f=e.mouseX,this.start=a,this.end=b,this.startTime=c,this.endTime=d,this.zoomed=!0,d=e.categoryAxis,e=this.rotate,b=this.width,c=this.height,a=d.stepWidth,this.fullWidth&&(f=1,d.parseDates&&!d.equalSpacing&&(f=d.minDuration()),e?this.extraWidth=c=a*f:(this.extraWidth=b=a*f,this.categoryBalloon.minWidth=b),this.line&&this.line.remove(),this.line=AmCharts.rect(this.container,b,c,this.cursorColor,this.cursorAlpha,0),this.fullRectSet&&this.fullRectSet.push(this.line)),this.stepWidth=a,this.tempVal=this.valueBalloonsEnabled,this.valueBalloonsEnabled=!1,this.setPosition(),this.valueBalloonsEnabled=this.tempVal,this.hideCursor()},hideObj:function(a){a&&a.hide()},hideCursor:function(a){void 0===a&&(a=!0),this.hideObj(this.set),this.hideObj(this.categoryBalloon),this.hideObj(this.line),this.hideObj(this.vLine),this.hideObj(this.hLine),this.hideObj(this.vaBalloon),this.hideObj(this.allBullets),this.destroyValueBalloons(),this.selectWithoutZooming||AmCharts.remove(this.selection),this.previousIndex=0/0,a&&this.fire("onHideCursor",{type:"onHideCursor",chart:this.chart,target:this}),this.drawing||this.chart.setMouseCursor("auto"),this.normalizeBulletSize()},setPosition:function(a,b,c){void 0===b&&(b=!0),"cursor"==this.type?(this.tempPosition=0/0,AmCharts.ifArray(this.data)&&(isNaN(a)&&(a=this.getMousePosition()),(a!=this.previousMousePosition||!0===this.zoomed||this.oneBalloonOnly)&&!isNaN(a)&&("mouse"==this.cursorPosition&&(this.tempPosition=a),isNaN(c)&&(c=this.chart.categoryAxis.xToIndex(a)),c!=this.previousIndex||this.zoomed||"mouse"==this.cursorPosition||this.oneBalloonOnly)&&(this.updateCursor(c,b),this.zoomed=!1),this.previousMousePosition=a)):this.updateCrosshair()},normalizeBulletSize:function(){var a=this.resizedBullets;if(a)for(var b=0;b<a.length;b++){var c=a[b],d=c.bulletGraphics;d&&(d.translate(c.bx,c.by,1),c=c.graph,isNaN(this.graphBulletAlpha)||(d.setAttr("fill-opacity",c.bulletAlpha),d.setAttr("stroke-opacity",c.bulletBorderAlpha)))}},updateCursor:function(a,b){var c=this.chart,d=this.fullWidth,e=c.mouseX-this.x,f=c.mouseY-this.y;if(this.drawingNow&&(AmCharts.remove(this.drawingLine),this.drawingLine=AmCharts.line(this.container,[this.x+this.drawStartX,this.x+e],[this.y+this.drawStartY,this.y+f],this.cursorColor,1,1)),this.enabled){void 0===b&&(b=!0),this.index=a+=this.adjustment;var g=c.categoryAxis,h=c.dx,i=c.dy,j=this.x+1,k=this.y+1,l=this.width,m=this.height,n=this.data[a];if(this.fireMoved(),n){var o=n.x[g.id],p=c.rotate,q=this.stepWidth,r=this.categoryBalloon,s=this.firstTime,t=this.lastTime,u=this.cursorPosition,v=this.zooming,w=this.panning,x=c.graphs;if(c.mouseIsOver||v||w||this.forceShow)if(this.forceShow=!1,w){var h=this.panClickPos,c=this.panClickEndTime,v=this.panClickStartTime,y=this.panClickEnd,j=this.panClickStart,e=(p?h-f:h-e)/q;(!g.parseDates||g.equalSpacing)&&(e=Math.round(e)),0!==e&&(h={type:"zoomed",target:this},h.chart=this.chart,g.parseDates&&!g.equalSpacing?(c+e>t&&(e=t-c),s>v+e&&(e=s-v),h.start=Math.round(v+e),h.end=Math.round(c+e),this.fire(h.type,h)):y+e>=this.data.length||0>j+e||(h.start=j+e,h.end=y+e,this.fire(h.type,h)))}else{if("start"==u?o-=g.cellWidth/2:"mouse"==u&&(c.mouseIsOver?o=p?f-2:e-2:isNaN(this.tempPosition)||(o=this.tempPosition-2)),
p){if(0>o){if(!v)return void this.hideCursor();o=0}if(o>m+1){if(!v)return void this.hideCursor();o=m+1}}else{if(0>o){if(!v)return void this.hideCursor();o=0}if(o>l){if(!v)return void this.hideCursor();o=l}}if(0<this.cursorAlpha){var z=this.line;p?(s=0,t=o+i,d&&(t-=g.cellWidth/2)):(s=o,t=0,d&&(s-=g.cellWidth/2)),q=this.animationDuration,q>0&&!this.zooming?isNaN(this.previousX)?z.translate(s,t):(z.translate(this.previousX,this.previousY),z.animate({translate:s+","+t},q,"easeOutSine")):z.translate(s,t),this.previousX=s,this.previousY=t,z.show()}if(this.linePos=p?o+i:o,v&&(d&&z.hide(),p?this.updateSelectionSize(0/0,o):this.updateSelectionSize(o,0/0)),q=!0,v&&(q=!1),this.categoryBalloonEnabled&&q?(this.setBalloonPosition(r,g,o,p),(s=this.categoryBalloonFunction)?r.showBalloon(s(n.category)):g.parseDates?(g=AmCharts.formatDate(n.category,this.categoryBalloonDateFormat,c),-1!=g.indexOf("fff")&&(g=AmCharts.formatMilliseconds(g,n.category)),r.showBalloon(g)):r.showBalloon(AmCharts.fixNewLines(n.category))):r.hide(),x&&this.bulletsEnabled&&this.showBullets(),this.oneBalloonOnly){for(o=1/0,g=0;g<x.length;g++)s=x[g],s.showBalloon&&!s.hidden&&s.balloonText&&(t=n.axes[s.valueAxis.id].graphs[s.id],r=t.y,isNaN(r)||(p?Math.abs(e-r)<o&&(o=Math.abs(e-r),y=s):Math.abs(f-r)<o&&(o=Math.abs(f-r),y=s)));this.mostCloseGraph&&(y=this.mostCloseGraph)}if((a!=this.previousIndex||y!=this.previousMostCloseGraph)&&(this.normalizeBulletSize(),this.destroyValueBalloons(),this.resizedBullets=[],x&&this.valueBalloonsEnabled&&q&&c.balloon.enabled)){for(this.valueBalloons=q=[],g=0;g<x.length;g++)if(s=x[g],r=0/0,(!this.oneBalloonOnly||s==y)&&s.showBalloon&&!s.hidden&&s.balloonText&&("step"==s.type&&"left"==s.stepDirection&&(n=this.data[a+1]),n)){if((t=n.axes[s.valueAxis.id].graphs[s.id])&&(r=t.y),this.showNextAvailable&&isNaN(r)&&a+1<this.data.length)for(o=a+1;o<this.data.length&&(!(d=this.data[o])||(t=d.axes[s.valueAxis.id].graphs[s.id],r=t.y,isNaN(r)));o++);isNaN(r)||(d=t.x,i=!0,p?(o=r,(0>d||d>m)&&(i=!1)):(o=d,d=r,(0>o||o>l+h+1)&&(i=!1)),i&&(i=this.graphBulletSize,z=this.graphBulletAlpha,1==i&&isNaN(z)||!AmCharts.isModern||!(u=t.bulletGraphics)||(u.getBBox(),u.translate(t.bx,t.by,i),this.resizedBullets.push(t),isNaN(z)||(u.setAttr("fill-opacity",z),u.setAttr("stroke-opacity",z))),i=s.valueBalloon,z=c.getBalloonColor(s,t),i.setBounds(j,k,j+l,k+m),i.pointerOrientation="H",u=this.balloonPointerOrientation,"vertical"==u&&(i.pointerOrientation="V"),"horizontal"==u&&(i.pointerOrientation="H"),i.changeColor(z),void 0!==s.balloonAlpha&&(i.fillAlpha=s.balloonAlpha),void 0!==s.balloonTextColor&&(i.color=s.balloonTextColor),i.setPosition(o+j,d+k),o=c.formatString(s.balloonText,t,!0),(d=s.balloonFunction)&&(o=d(t,s).toString()),""!==o&&(p?i.showBalloon(o):(i.text=o,i.show=!0),q.push({yy:r,balloon:i})),!p&&i.set&&(i.set.hide(),s=i.textDiv)&&(s.style.visibility="hidden")))}this.avoidBalloonOverlapping&&this.arrangeBalloons()}b?(h={type:"changed"},h.index=a,h.chart=this.chart,h.zooming=v,h.mostCloseGraph=y,h.position=p?f:e,h.target=this,c.fire("changed",h),this.fire("changed",h),this.skipZoomDispatch=!1):(this.skipZoomDispatch=!0,c.updateLegendValues(a)),this.previousIndex=a,this.previousMostCloseGraph=y}}}else this.hideCursor()},setBalloonPosition:function(a,b,c,d){var e=b.position,f=b.inside;b=b.axisThickness;var g=this.chart,h=g.dx,g=g.dy,i=this.x,j=this.y,k=this.width,l=this.height;d?(f&&("right"==e?a.setBounds(i,j+g,i+k+h,j+c+g):a.setBounds(i,j+g,i+k+h,j+c)),"right"==e?f?a.setPosition(i+k+h,j+c+g):a.setPosition(i+k+h+b,j+c+g):f?a.setPosition(i,j+c):a.setPosition(i-b,j+c)):"top"==e?f?a.setPosition(i+c+h,j+g):a.setPosition(i+c+h,j+g-b+1):f?a.setPosition(i+c,j+l):a.setPosition(i+c,j+l+b-1)},setBalloonBounds:function(a,b,c){var d=b.position,e=b.inside,f=b.axisThickness,g=b.tickLength,h=this.chart,i=h.dx,h=h.dy,j=this.x,k=this.y,l=this.width,m=this.height;c?(e&&(a.pointerWidth=0),"right"==d?e?a.setBounds(j,k+h,j+l+i,k+m+h):a.setBounds(j+l+i+f,k+h,j+l+1e3,k+m+h):e?a.setBounds(j,k,l+j,m+k):a.setBounds(-1e3,-1e3,j-g-f,k+m+15)):(a.maxWidth=l,b.parseDates&&(g=0,a.pointerWidth=0),"top"==d?e?a.setBounds(j+i,k+h,l+i+j,m+k):a.setBounds(j+i,-1e3,l+i+j,k+h-g-f):e?a.setBounds(j,k,l+j,m+k-g):a.setBounds(j,k+m+g+f-1,j+l,k+m+g+f))},enableDrawing:function(a){this.enabled=!a,this.hideCursor(),this.rolledOver=!1,this.drawing=a},isZooming:function(a){a&&a!=this.zooming&&this.handleMouseDown("fake"),a||a==this.zooming||this.handleMouseUp()},handleMouseOut:function(){if(this.enabled)if(this.zooming)this.setPosition();else{this.index=void 0;var a={type:"changed",index:void 0,target:this};a.chart=this.chart,this.fire("changed",a),this.hideCursor()}},handleReleaseOutside:function(){this.handleMouseUp()},handleMouseUp:function(){var a,b=this.chart,c=this.data;if(b){var d=b.mouseX-this.x,e=b.mouseY-this.y;if(this.drawingNow){this.drawingNow=!1,AmCharts.remove(this.drawingLine),a=this.drawStartX;var f=this.drawStartY;(2<Math.abs(a-d)||2<Math.abs(f-e))&&(a={type:"draw",target:this,chart:b,initialX:a,initialY:f,finalX:d,finalY:e},this.fire(a.type,a))}if(this.enabled&&0<c.length){if(this.pan)this.rolledOver=!1;else if(this.zoomable&&this.zooming){if(a=this.selectWithoutZooming?{type:"selected"}:{type:"zoomed"},a.target=this,a.chart=b,"cursor"==this.type)this.rotate?this.selectionPosY=e:this.selectionPosX=e=d,2>Math.abs(e-this.initialMouse)&&this.fromIndex==this.index||(this.index<this.fromIndex?(a.end=this.fromIndex,a.start=this.index):(a.end=this.index,a.start=this.fromIndex),e=b.categoryAxis,e.parseDates&&!e.equalSpacing&&(c[a.start]&&(a.start=c[a.start].time),c[a.end]&&(a.end=b.getEndTime(c[a.end].time))),this.skipZoomDispatch||this.fire(a.type,a));else{var g=this.initialMouseX,h=this.initialMouseY;3>Math.abs(d-g)&&3>Math.abs(e-h)||(c=Math.min(g,d),f=Math.min(h,e),d=Math.abs(g-d),e=Math.abs(h-e),b.hideXScrollbar&&(c=0,d=this.width),b.hideYScrollbar&&(f=0,e=this.height),a.selectionHeight=e,a.selectionWidth=d,a.selectionY=f,a.selectionX=c,this.skipZoomDispatch||this.fire(a.type,a))}this.selectWithoutZooming||AmCharts.remove(this.selection)}this.skipZoomDispatch=!1}}this.panning=this.zooming=!1},showCursorAt:function(a){var b=this.chart.categoryAxis;a=b.parseDates?b.dateToCoordinate(a):b.categoryToCoordinate(a),this.previousMousePosition=0/0,this.forceShow=!0,this.setPosition(a,!1)},clearSelection:function(){AmCharts.remove(this.selection)},handleMouseDown:function(a){if(this.zoomable||this.pan||this.drawing){var b=this.rotate,c=this.chart,d=c.mouseX-this.x,e=c.mouseY-this.y;(d>0&&d<this.width&&e>0&&e<this.height||"fake"==a)&&(this.setPosition(),this.selectWithoutZooming&&AmCharts.remove(this.selection),this.drawing?(this.drawStartY=e,this.drawStartX=d,this.drawingNow=!0):this.pan?(this.zoomable=!1,c.setMouseCursor("move"),this.panning=!0,this.panClickPos=b?e:d,this.panClickStart=this.start,this.panClickEnd=this.end,this.panClickStartTime=this.startTime,this.panClickEndTime=this.endTime):this.zoomable&&("cursor"==this.type?(this.fromIndex=this.index,b?(this.initialMouse=e,this.selectionPosY=this.linePos):(this.initialMouse=d,this.selectionPosX=this.linePos)):(this.initialMouseX=d,this.initialMouseY=e,this.selectionPosX=d,this.selectionPosY=e),this.zooming=!0))}}}),AmCharts.SimpleChartScrollbar=AmCharts.Class({construct:function(a){this.createEvents("zoomed"),this.backgroundColor="#D4D4D4",this.backgroundAlpha=1,this.selectedBackgroundColor="#EFEFEF",this.scrollDuration=this.selectedBackgroundAlpha=1,this.resizeEnabled=!0,this.hideResizeGrips=!1,this.scrollbarHeight=20,this.updateOnReleaseOnly=!1,9>document.documentMode&&(this.updateOnReleaseOnly=!0),this.dragIconWidth=18,this.dragIconHeight=25,AmCharts.applyTheme(this,a,"SimpleChartScrollbar")},draw:function(){var a=this;a.destroy(),a.interval=setInterval(function(){a.updateScrollbar.call(a)},40);var b=a.chart.container,c=a.rotate,d=a.chart,e=b.set();a.set=e,d.scrollbarsSet.push(e);var f,g;if(c?(f=a.scrollbarHeight,g=d.plotAreaHeight):(g=a.scrollbarHeight,f=d.plotAreaWidth),a.width=f,(a.height=g)&&f){var h=AmCharts.rect(b,f,g,a.backgroundColor,a.backgroundAlpha,1,a.backgroundColor,a.backgroundAlpha);a.bg=h,e.push(h),h=AmCharts.rect(b,f,g,"#000",.005),e.push(h),a.invisibleBg=h,h.click(function(){a.handleBgClick()}).mouseover(function(){a.handleMouseOver()}).mouseout(function(){a.handleMouseOut()}).touchend(function(){a.handleBgClick()}),h=AmCharts.rect(b,f,g,a.selectedBackgroundColor,a.selectedBackgroundAlpha),a.selectedBG=h,e.push(h),f=AmCharts.rect(b,f,g,"#000",.005),a.dragger=f,e.push(f),f.mousedown(function(b){a.handleDragStart(b)}).mouseup(function(){a.handleDragStop()}).mouseover(function(){a.handleDraggerOver()}).mouseout(function(){a.handleMouseOut()}).touchstart(function(b){a.handleDragStart(b)}).touchend(function(){a.handleDragStop()}),f=d.pathToImages,c?(h=f+"dragIconH.gif",f=a.dragIconWidth,c=a.dragIconHeight):(h=f+"dragIcon.gif",c=a.dragIconWidth,f=a.dragIconHeight),g=b.image(h,0,0,c,f);var h=b.image(h,0,0,c,f),i=10,j=20;d.panEventsEnabled&&(i=25,j=a.scrollbarHeight);var k=AmCharts.rect(b,i,j,"#000",.005),l=AmCharts.rect(b,i,j,"#000",.005);l.translate(-(i-c)/2,-(j-f)/2),k.translate(-(i-c)/2,-(j-f)/2),c=b.set([g,l]),b=b.set([h,k]),a.iconLeft=c,a.iconRight=b,c.mousedown(function(){a.leftDragStart()}).mouseup(function(){a.leftDragStop()}).mouseover(function(){a.iconRollOver()}).mouseout(function(){a.iconRollOut()}).touchstart(function(b){a.leftDragStart()}).touchend(function(){a.leftDragStop()}),b.mousedown(function(){a.rightDragStart()}).mouseup(function(){a.rightDragStop()}).mouseover(function(){a.iconRollOver()}).mouseout(function(){a.iconRollOut()}).touchstart(function(b){a.rightDragStart()}).touchend(function(){a.rightDragStop()}),AmCharts.ifArray(d.chartData)?e.show():e.hide(),a.hideDragIcons(),a.clipDragger(!1)}e.translate(a.x,a.y)},updateScrollbarSize:function(a,b){var c,d,e,f,g=this.dragger;this.rotate?(c=0,d=a,e=this.width+1,f=b-a,g.setAttr("height",b-a),g.setAttr("y",d)):(c=a,d=0,e=b-a,f=this.height+1,g.setAttr("width",b-a),g.setAttr("x",c)),this.clipAndUpdate(c,d,e,f)},updateScrollbar:function(){var a,b,c,d=!1,e=this.x,f=this.y,g=this.dragger,h=this.getDBox();b=h.x+e,c=h.y+f;var i=h.width,h=h.height,j=this.rotate,k=this.chart,l=this.width,m=this.height,n=k.mouseX,o=k.mouseY;a=this.initialMouse,this.forceClip&&this.clipDragger(!0),k.mouseIsOver&&(this.dragging&&(k=this.initialCoord,j?(a=k+(o-a),0>a&&(a=0),k=m-h,a>k&&(a=k),g.setAttr("y",a)):(a=k+(n-a),0>a&&(a=0),k=l-i,a>k&&(a=k),g.setAttr("x",a)),this.clipDragger(!0)),this.resizingRight&&(j?(a=o-c,a+c>m+f&&(a=m-c+f),0>a?(this.resizingRight=!1,d=this.resizingLeft=!0):(0===a&&(a=.1),g.setAttr("height",a))):(a=n-b,a+b>l+e&&(a=l-b+e),0>a?(this.resizingRight=!1,d=this.resizingLeft=!0):(0===a&&(a=.1),g.setAttr("width",a))),this.clipDragger(!0)),this.resizingLeft&&(j?(b=c,c=o,f>c&&(c=f),c>m+f&&(c=m+f),a=!0===d?b-c:h+b-c,0>a?(this.resizingRight=!0,this.resizingLeft=!1,g.setAttr("y",b+h-f)):(0===a&&(a=.1),g.setAttr("y",c-f),g.setAttr("height",a))):(c=n,e>c&&(c=e),c>l+e&&(c=l+e),a=!0===d?b-c:i+b-c,0>a?(this.resizingRight=!0,this.resizingLeft=!1,g.setAttr("x",b+i-e)):(0===a&&(a=.1),g.setAttr("x",c-e),g.setAttr("width",a))),this.clipDragger(!0)))},stopForceClip:function(){this.forceClip=!1},clipDragger:function(a){var b=this.getDBox();if(b){var c=b.x,d=b.y,e=b.width,b=b.height,f=!1;this.rotate?(c=0,e=this.width+1,(this.clipY!=d||this.clipH!=b)&&(f=!0)):(d=0,b=this.height+1,(this.clipX!=c||this.clipW!=e)&&(f=!0)),f&&(this.clipAndUpdate(c,d,e,b),a&&(this.updateOnReleaseOnly||this.dispatchScrollbarEvent()))}},maskGraphs:function(){},clipAndUpdate:function(a,b,c,d){this.clipX=a,this.clipY=b,this.clipW=c,this.clipH=d,this.selectedBG.clipRect(a,b,c,d),this.updateDragIconPositions(),this.maskGraphs(a,b,c,d)},dispatchScrollbarEvent:function(){if(this.skipEvent)this.skipEvent=!1;else{var a=this.chart;a.hideBalloon();var b=this.getDBox(),c=b.x,d=b.y,e=b.width,b=b.height;this.rotate?(c=d,e=this.height/b):e=this.width/e,a={type:"zoomed",position:c,chart:a,target:this,multiplier:e},this.fire(a.type,a)}},updateDragIconPositions:function(){var a,b,c=this.getDBox(),d=c.x,e=c.y,f=this.iconLeft,g=this.iconRight,h=this.scrollbarHeight;this.rotate?(a=this.dragIconWidth,b=this.dragIconHeight,f.translate(this.x+(h-b)/2,this.y+e-a/2),g.translate(this.x+(h-b)/2,this.y+e+c.height-a/2)):(a=this.dragIconHeight,b=this.dragIconWidth,f.translate(this.x+d-b/2,this.y+(h-a)/2),g.translate(this.x+d-b/2+c.width,this.y+(h-a)/2))},showDragIcons:function(){this.resizeEnabled&&(this.iconLeft.show(),this.iconRight.show())},hideDragIcons:function(){this.resizingLeft||this.resizingRight||this.dragging||((this.hideResizeGrips||!this.resizeEnabled)&&(this.iconLeft.hide(),this.iconRight.hide()),this.removeCursors())},removeCursors:function(){this.chart.setMouseCursor("auto")},relativeZoom:function(a,b){this.dragger.stop(),this.multiplier=a,this.position=b,this.updateScrollbarSize(b,this.rotate?b+this.height/a:b+this.width/a)},destroy:function(){this.clear(),AmCharts.remove(this.set),AmCharts.remove(this.iconRight),AmCharts.remove(this.iconLeft)},clear:function(){clearInterval(this.interval)},handleDragStart:function(){var a=this.chart;this.dragger.stop(),this.removeCursors(),this.dragging=!0;var b=this.getDBox();this.rotate?(this.initialCoord=b.y,this.initialMouse=a.mouseY):(this.initialCoord=b.x,this.initialMouse=a.mouseX)},handleDragStop:function(){this.updateOnReleaseOnly&&(this.updateScrollbar(),this.skipEvent=!1,this.dispatchScrollbarEvent()),this.dragging=!1,this.mouseIsOver&&this.removeCursors(),this.updateScrollbar()},handleDraggerOver:function(){this.handleMouseOver()},leftDragStart:function(){this.dragger.stop(),this.resizingLeft=!0},leftDragStop:function(){this.resizingLeft=!1,this.mouseIsOver||this.removeCursors(),this.updateOnRelease()},rightDragStart:function(){this.dragger.stop(),this.resizingRight=!0},rightDragStop:function(){this.resizingRight=!1,this.mouseIsOver||this.removeCursors(),this.updateOnRelease()},iconRollOut:function(){this.removeCursors()},iconRollOver:function(){this.chart.setMouseCursor(this.rotate?"n-resize":"e-resize"),this.handleMouseOver()},getDBox:function(){return this.dragger?this.dragger.getBBox():void 0},handleBgClick:function(){var a=this;if(!a.resizingRight&&!a.resizingLeft){a.zooming=!0;var b,c,d=a.scrollDuration,e=a.dragger;b=a.getDBox();var f=b.height,g=b.width;c=a.chart;var h=a.y,i=a.x,j=a.rotate;j?(b="y",c=c.mouseY-f/2-h,c=AmCharts.fitToBounds(c,0,a.height-f)):(b="x",c=c.mouseX-g/2-i,c=AmCharts.fitToBounds(c,0,a.width-g)),a.updateOnReleaseOnly?(a.skipEvent=!1,e.setAttr(b,c),a.dispatchScrollbarEvent(),a.clipDragger()):(c=Math.round(c),j?e.animate({y:c},d,">"):e.animate({x:c},d,">"),a.forceClip=!0,clearTimeout(a.forceTO),a.forceTO=setTimeout(function(){a.stopForceClip.call(a)},3e3*d))}},updateOnRelease:function(){this.updateOnReleaseOnly&&(this.updateScrollbar(),this.skipEvent=!1,this.dispatchScrollbarEvent())},handleReleaseOutside:function(){this.set&&((this.resizingLeft||this.resizingRight||this.dragging)&&(this.updateOnRelease(),this.removeCursors()),this.mouseIsOver=this.dragging=this.resizingRight=this.resizingLeft=!1,this.hideDragIcons(),this.updateScrollbar())},handleMouseOver:function(){this.mouseIsOver=!0,this.showDragIcons()},handleMouseOut:function(){this.mouseIsOver=!1,this.hideDragIcons()}}),AmCharts.ChartScrollbar=AmCharts.Class({inherits:AmCharts.SimpleChartScrollbar,construct:function(a){this.cname="ChartScrollbar",AmCharts.ChartScrollbar.base.construct.call(this,a),this.graphLineColor="#BBBBBB",this.graphLineAlpha=0,this.graphFillColor="#BBBBBB",this.graphFillAlpha=1,this.selectedGraphLineColor="#888888",this.selectedGraphLineAlpha=0,this.selectedGraphFillColor="#888888",this.selectedGraphFillAlpha=1,this.gridCount=0,this.gridColor="#FFFFFF",this.gridAlpha=.7,this.skipEvent=this.autoGridCount=!1,this.color="#FFFFFF",this.scrollbarCreated=!1,this.offset=0,AmCharts.applyTheme(this,a,this.cname)},init:function(){var a=this.categoryAxis,b=this.chart;if(a||(this.categoryAxis=a=new AmCharts.CategoryAxis),a.chart=b,a.id="scrollbar",a.dateFormats=b.categoryAxis.dateFormats,a.markPeriodChange=b.categoryAxis.markPeriodChange,a.boldPeriodBeginning=b.categoryAxis.boldPeriodBeginning,a.axisItemRenderer=AmCharts.RecItem,a.axisRenderer=AmCharts.RecAxis,a.guideFillRenderer=AmCharts.RecFill,a.inside=!0,a.fontSize=this.fontSize,a.tickLength=0,a.axisAlpha=0,AmCharts.isString(this.graph)&&(this.graph=AmCharts.getObjById(b.graphs,this.graph)),a=this.graph){var c=this.valueAxis;c||(this.valueAxis=c=new AmCharts.ValueAxis,c.visible=!1,c.scrollbar=!0,c.axisItemRenderer=AmCharts.RecItem,c.axisRenderer=AmCharts.RecAxis,c.guideFillRenderer=AmCharts.RecFill,c.labelsEnabled=!1,c.chart=b),b=this.unselectedGraph,b||(b=new AmCharts.AmGraph,b.scrollbar=!0,this.unselectedGraph=b,b.negativeBase=a.negativeBase,b.noStepRisers=a.noStepRisers),b=this.selectedGraph,b||(b=new AmCharts.AmGraph,b.scrollbar=!0,this.selectedGraph=b,b.negativeBase=a.negativeBase,b.noStepRisers=a.noStepRisers)}this.scrollbarCreated=!0},draw:function(){var a=this;AmCharts.ChartScrollbar.base.draw.call(a),a.scrollbarCreated||a.init();var b=a.chart,c=b.chartData,d=a.categoryAxis,e=a.rotate,f=a.x,g=a.y,h=a.width,i=a.height,j=b.categoryAxis,k=a.set;if(d.setOrientation(!e),d.parseDates=j.parseDates,d.rotate=e,d.equalSpacing=j.equalSpacing,d.minPeriod=j.minPeriod,d.startOnAxis=j.startOnAxis,d.viW=h,d.viH=i,d.width=h,d.height=i,d.gridCount=a.gridCount,d.gridColor=a.gridColor,d.gridAlpha=a.gridAlpha,d.color=a.color,d.tickLength=0,d.axisAlpha=0,d.autoGridCount=a.autoGridCount,d.parseDates&&!d.equalSpacing&&d.timeZoom(b.firstTime,b.lastTime),d.zoom(0,c.length-1),j=a.graph){var l=a.valueAxis,m=j.valueAxis;l.id=m.id,l.rotate=e,l.setOrientation(e),l.width=h,l.height=i,l.viW=h,l.viH=i,l.dataProvider=c,l.reversed=m.reversed,l.logarithmic=m.logarithmic,l.gridAlpha=0,l.axisAlpha=0,k.push(l.set),e?(l.y=g,l.x=0):(l.x=f,l.y=0);var n,f=1/0,g=-(1/0);for(n=0;n<c.length;n++){var o,p=c[n].axes[m.id].graphs[j.id].values;for(o in p)if(p.hasOwnProperty(o)&&"percents"!=o&&"total"!=o){var q=p[o];f>q&&(f=q),q>g&&(g=q)}}1/0!=f&&(l.minimum=f),-(1/0)!=g&&(l.maximum=g+.1*(g-f)),f==g&&(l.minimum-=1,l.maximum+=1),void 0!==a.minimum&&(l.minimum=a.minimum),void 0!==a.maximum&&(l.maximum=a.maximum),l.zoom(0,c.length-1),o=a.unselectedGraph,o.id=j.id,o.rotate=e,o.chart=b,o.data=c,o.valueAxis=l,o.chart=j.chart,o.categoryAxis=a.categoryAxis,o.periodSpan=j.periodSpan,o.valueField=j.valueField,o.openField=j.openField,o.closeField=j.closeField,o.highField=j.highField,o.lowField=j.lowField,o.lineAlpha=a.graphLineAlpha,o.lineColorR=a.graphLineColor,o.fillAlphas=a.graphFillAlpha,o.fillColorsR=a.graphFillColor,o.connect=j.connect,o.hidden=j.hidden,o.width=h,o.height=i,o.pointPosition=j.pointPosition,o.stepDirection=j.stepDirection,o.periodSpan=j.periodSpan,m=a.selectedGraph,m.id=j.id,m.rotate=e,m.chart=b,m.data=c,m.valueAxis=l,m.chart=j.chart,m.categoryAxis=d,m.periodSpan=j.periodSpan,m.valueField=j.valueField,m.openField=j.openField,m.closeField=j.closeField,m.highField=j.highField,m.lowField=j.lowField,m.lineAlpha=a.selectedGraphLineAlpha,m.lineColorR=a.selectedGraphLineColor,m.fillAlphas=a.selectedGraphFillAlpha,m.fillColorsR=a.selectedGraphFillColor,m.connect=j.connect,m.hidden=j.hidden,m.width=h,m.height=i,m.pointPosition=j.pointPosition,m.stepDirection=j.stepDirection,m.periodSpan=j.periodSpan,b=a.graphType,b||(b=j.type),o.type=b,m.type=b,c=c.length-1,o.zoom(0,c),m.zoom(0,c),m.set.click(function(){a.handleBackgroundClick()}).mouseover(function(){a.handleMouseOver()}).mouseout(function(){a.handleMouseOut()}),o.set.click(function(){a.handleBackgroundClick()}).mouseover(function(){a.handleMouseOver()}).mouseout(function(){a.handleMouseOut()}),k.push(o.set),k.push(m.set)}k.push(d.set),k.push(d.labelsSet),a.bg.toBack(),a.invisibleBg.toFront(),a.dragger.toFront(),a.iconLeft.toFront(),a.iconRight.toFront()},timeZoom:function(a,b,c){this.startTime=a,this.endTime=b,this.timeDifference=b-a,this.skipEvent=!AmCharts.toBoolean(c),this.zoomScrollbar(),this.skipEvent||this.dispatchScrollbarEvent()},zoom:function(a,b){this.start=a,this.end=b,this.skipEvent=!0,this.zoomScrollbar()},dispatchScrollbarEvent:function(){if(this.skipEvent)this.skipEvent=!1;else{var a,b,c=this.chart.chartData,d=this.dragger.getBBox();a=d.x;var e=d.y,f=d.width,d=d.height,g=this.chart;this.rotate?(a=e,b=d):b=f,f={type:"zoomed",target:this},f.chart=g;var h=this.categoryAxis,i=this.stepWidth,e=g.minSelectedTime,d=!1;h.parseDates&&!h.equalSpacing?(c=g.lastTime,g=g.firstTime,h.minDuration(),h=Math.round(a/i)+g,a=this.dragging?h+this.timeDifference:Math.round((a+b)/i)+g,h>a&&(h=a),e>0&&e>a-h&&(a=Math.round(h+(a-h)/2),d=Math.round(e/2),h=a-d,a+=d,d=!0),a>c&&(a=c),h>a-e&&(h=a-e),g>h&&(h=g),h+e>a&&(a=h+e),(h!=this.startTime||a!=this.endTime)&&(this.startTime=h,this.endTime=a,f.start=h,f.end=a,f.startDate=new Date(h),f.endDate=new Date(a),this.fire(f.type,f))):(h.startOnAxis||(a+=i/2),b-=this.stepWidth/2,e=h.xToIndex(a),a=h.xToIndex(a+b),(e!=this.start||this.end!=a)&&(h.startOnAxis&&(this.resizingRight&&e==a&&a++,this.resizingLeft&&e==a&&(e>0?e--:a=1)),this.start=e,this.end=this.dragging?this.start+this.difference:a,f.start=this.start,f.end=this.end,h.parseDates&&(c[this.start]&&(f.startDate=new Date(c[this.start].time)),c[this.end]&&(f.endDate=new Date(c[this.end].time))),this.fire(f.type,f))),d&&this.zoomScrollbar()}},zoomScrollbar:function(){var a,b;a=this.chart;var c=a.chartData,d=this.categoryAxis;d.parseDates&&!d.equalSpacing?(c=d.stepWidth,d=a.firstTime,a=c*(this.startTime-d),b=c*(this.endTime-d)):(a=c[this.start].x[d.id],b=c[this.end].x[d.id],c=d.stepWidth,d.startOnAxis||(d=c/2,a-=d,b+=d)),this.stepWidth=c,this.updateScrollbarSize(a,b)},maskGraphs:function(a,b,c,d){var e=this.selectedGraph;e&&e.set.clipRect(a,b,c,d)},handleDragStart:function(){AmCharts.ChartScrollbar.base.handleDragStart.call(this),this.difference=this.end-this.start,this.timeDifference=this.endTime-this.startTime,0>this.timeDifference&&(this.timeDifference=0)},handleBackgroundClick:function(){AmCharts.ChartScrollbar.base.handleBackgroundClick.call(this),this.dragging||(this.difference=this.end-this.start,this.timeDifference=this.endTime-this.startTime,0>this.timeDifference&&(this.timeDifference=0))}}),AmCharts.AmBalloon=AmCharts.Class({construct:function(a){this.cname="AmBalloon",this.enabled=!0,this.fillColor="#FFFFFF",this.fillAlpha=.8,this.borderThickness=2,this.borderColor="#FFFFFF",this.borderAlpha=1,this.cornerRadius=0,this.maximumWidth=220,this.horizontalPadding=8,this.verticalPadding=4,this.pointerWidth=6,this.pointerOrientation="V",this.color="#000000",this.adjustBorderColor=!0,this.show=this.follow=this.showBullet=!1,this.bulletSize=3,this.shadowAlpha=.4,this.shadowColor="#000000",this.fadeOutDuration=this.animationDuration=.3,this.fixedPosition=!1,this.offsetY=6,this.offsetX=1,this.textAlign="center",AmCharts.isModern||(this.offsetY*=1.5),AmCharts.applyTheme(this,a,this.cname)},draw:function(){var a=this.pointToX,b=this.pointToY;this.deltaSignX=this.deltaSignY=1;var c=this.chart;if(AmCharts.VML&&(this.fadeOutDuration=0),this.xAnim&&c.stopAnim(this.xAnim),this.yAnim&&c.stopAnim(this.yAnim),!isNaN(a)){var d=this.follow,e=c.container,f=this.set;if(AmCharts.remove(f),this.removeDiv(),f=e.set(),f.node.style.pointerEvents="none",this.set=f,c.balloonsSet.push(f),this.show){var g=this.l,h=this.t,i=this.r,j=this.b,k=this.balloonColor,l=this.fillColor,m=this.borderColor,n=l;void 0!=k&&(this.adjustBorderColor?n=m=k:l=k);var o=this.horizontalPadding,p=this.verticalPadding,q=this.pointerWidth,r=this.pointerOrientation,s=this.cornerRadius,t=c.fontFamily,u=this.fontSize;void 0==u&&(u=c.fontSize);var k=document.createElement("div"),v=k.style;v.pointerEvents="none",v.position="absolute";var w=this.minWidth,x="";isNaN(w)||(x="min-width:"+(w-2*o)+"px; "),k.innerHTML='<div style="text-align:'+this.textAlign+"; "+x+"max-width:"+this.maxWidth+"px; font-size:"+u+"px; color:"+this.color+"; font-family:"+t+'">'+this.text+"</div>",c.chartDiv.appendChild(k),this.textDiv=k,u=k.offsetWidth,t=k.offsetHeight,k.clientHeight&&(u=k.clientWidth,t=k.clientHeight),t+=2*p,x=u+2*o,!isNaN(w)&&w>x&&(x=w),window.opera&&(t+=2);var y=!1,u=this.offsetY;c.handDrawn&&(u+=c.handDrawScatter+2),"H"!=r?(w=a-x/2,h+t+10>b&&"down"!=r?(y=!0,d&&(b+=u),u=b+q,this.deltaSignY=-1):(d&&(b-=u),u=b-t-q,this.deltaSignY=1)):(2*q>t&&(q=t/2),u=b-t/2,g+(i-g)/2>a?(w=a+q,this.deltaSignX=-1):(w=a-x-q,this.deltaSignX=1)),u+t>=j&&(u=j-t),h>u&&(u=h),g>w&&(w=g),w+x>i&&(w=i-x);var z,h=u+p,j=w+o,p=this.shadowAlpha,A=this.shadowColor,o=this.borderThickness,B=this.bulletSize;s>0||0===q?(p>0&&(a=AmCharts.rect(e,x,t,l,0,o+1,A,p,this.cornerRadius),AmCharts.isModern?a.translate(1,1):a.translate(4,4),f.push(a)),l=AmCharts.rect(e,x,t,l,this.fillAlpha,o,m,this.borderAlpha,this.cornerRadius),this.showBullet&&(z=AmCharts.circle(e,B,n,this.fillAlpha),f.push(z))):(n=[],s=[],"H"!=r?(g=a-w,g>x-q&&(g=x-q),q>g&&(g=q),n=[0,g-q,a-w,g+q,x,x,0,0],s=y?[0,0,b-u,0,0,t,t,0]:[t,t,b-u,t,t,0,0,t]):(n=b-u,n>t-q&&(n=t-q),q>n&&(n=q),s=[0,n-q,b-u,n+q,t,t,0,0],n=g+(i-g)/2>a?[0,0,a>w?0:a-w,0,0,x,x,0]:[x,x,w+x>a?x:a-w,x,x,0,0,x]),p>0&&(a=AmCharts.polygon(e,n,s,l,0,o,A,p),a.translate(1,1),f.push(a)),l=AmCharts.polygon(e,n,s,l,this.fillAlpha,o,m,this.borderAlpha)),this.bg=l,f.push(l),l.toFront(),e=1*this.deltaSignX,v.left=j+"px",v.top=h+"px",f.translate(w-e,u),l=l.getBBox(),this.bottom=u+t+1,this.yPos=l.y+u,z&&z.translate(this.pointToX-w+e,b-u),b=this.animationDuration,0<this.animationDuration&&!d&&!isNaN(this.prevX)&&(f.translate(this.prevX,this.prevY),f.animate({translate:w-e+","+u},b,"easeOutSine"),k&&(v.left=this.prevTX+"px",v.top=this.prevTY+"px",this.xAnim=c.animate({node:k},"left",this.prevTX,j,b,"easeOutSine","px"),this.yAnim=c.animate({node:k},"top",this.prevTY,h,b,"easeOutSine","px"))),this.prevX=w-e,this.prevY=u,this.prevTX=j,this.prevTY=h}}},followMouse:function(){if(this.follow&&this.show){var a=this.chart.mouseX-this.offsetX*this.deltaSignX,b=this.chart.mouseY;if(this.pointToX=a,this.pointToY=b,a!=this.previousX||b!=this.previousY)if(this.previousX=a,this.previousY=b,0===this.cornerRadius)this.draw();else{var c=this.set;if(c){var d=c.getBBox(),a=a-d.width/2,e=b-d.height-10;a<this.l&&(a=this.l),a>this.r-d.width&&(a=this.r-d.width),e<this.t&&(e=b+10),c.translate(a,e),b=this.textDiv.style,b.left=a+this.horizontalPadding+"px",b.top=e+this.verticalPadding+"px"}}}},changeColor:function(a){this.balloonColor=a},setBounds:function(a,b,c,d){this.l=a,this.t=b,this.r=c,this.b=d,this.destroyTO&&clearTimeout(this.destroyTO)},showBalloon:function(a){this.text=a,this.show=!0,this.destroyTO&&clearTimeout(this.destroyTO),a=this.chart,this.fadeAnim1&&a.stopAnim(this.fadeAnim1),this.fadeAnim2&&a.stopAnim(this.fadeAnim2),this.draw()},hide:function(){var a=this,b=a.fadeOutDuration,c=a.chart;if(b>0){a.destroyTO=setTimeout(function(){a.destroy.call(a)},1e3*b),a.follow=!1,a.show=!1;var d=a.set;d&&(d.setAttr("opacity",a.fillAlpha),a.fadeAnim1=d.animate({opacity:0},b,"easeInSine")),a.textDiv&&(a.fadeAnim2=c.animate({node:a.textDiv},"opacity",1,0,b,"easeInSine",""))}else a.show=!1,a.follow=!1,a.destroy()},setPosition:function(a,b,c){this.pointToX=a,this.pointToY=b,c&&(a==this.previousX&&b==this.previousY||this.draw()),this.previousX=a,this.previousY=b},followCursor:function(a){var b=this;(b.follow=a)?(b.pShowBullet=b.showBullet,b.showBullet=!1):void 0!==b.pShowBullet&&(b.showBullet=b.pShowBullet),clearInterval(b.interval);var c=b.chart.mouseX,d=b.chart.mouseY;!isNaN(c)&&a&&(b.pointToX=c-b.offsetX*b.deltaSignX,b.pointToY=d,b.followMouse(),b.interval=setInterval(function(){b.followMouse.call(b)},40))},removeDiv:function(){if(this.textDiv){var a=this.textDiv.parentNode;a&&a.removeChild(this.textDiv)}},destroy:function(){clearInterval(this.interval),AmCharts.remove(this.set),this.removeDiv(),this.set=null}}),AmCharts.AmCoordinateChart=AmCharts.Class({inherits:AmCharts.AmChart,construct:function(a){AmCharts.AmCoordinateChart.base.construct.call(this,a),this.theme=a,this.createEvents("rollOverGraphItem","rollOutGraphItem","clickGraphItem","doubleClickGraphItem","rightClickGraphItem","clickGraph","rollOverGraph","rollOutGraph"),this.startAlpha=1,this.startDuration=0,this.startEffect="elastic",this.sequencedAnimation=!0,this.colors="#FF6600 #FCD202 #B0DE09 #0D8ECF #2A0CD0 #CD0D74 #CC0000 #00CC00 #0000CC #DDDDDD #999999 #333333 #990000".split(" "),this.balloonDateFormat="MMM DD, YYYY",this.valueAxes=[],this.graphs=[],this.guides=[],this.gridAboveGraphs=!1,AmCharts.applyTheme(this,a,"AmCoordinateChart")},initChart:function(){AmCharts.AmCoordinateChart.base.initChart.call(this);var a=this.categoryAxis;a&&(this.categoryAxis=AmCharts.processObject(a,AmCharts.CategoryAxis,this.theme)),this.processValueAxes(),this.createValueAxes(),this.processGraphs(),this.processGuides(),AmCharts.VML&&(this.startAlpha=1),this.setLegendData(this.graphs),this.gridAboveGraphs&&this.gridSet.toFront()},createValueAxes:function(){if(0===this.valueAxes.length){var a=new AmCharts.ValueAxis;this.addValueAxis(a)}},parseData:function(){this.processValueAxes(),this.processGraphs()},parseSerialData:function(){var a,b=this.graphs,c={},d=this.seriesIdField;d||(d=this.categoryField),this.chartData=[];var e=this.dataProvider;if(e){var f,g,h,i=!1,j=this.categoryAxis;j&&(i=j.parseDates,g=j.forceShowField,h=j.labelColorField,f=j.categoryFunction);var k,l,m,n={};i&&(a=AmCharts.extractPeriod(j.minPeriod),k=a.period,l=a.count,m=AmCharts.getPeriodDuration(k,l));var o={};this.lookupTable=o;var p,q=this.dataDateFormat,r={};for(p=0;p<e.length;p++){var s={},t=e[p];if(a=t[this.categoryField],s.dataContext=t,s.category=f?f(a,t,j):String(a),g&&(s.forceShow=t[g]),h&&(s.labelColor=t[h]),o[t[d]]=s,!i||(a=j.categoryFunction?j.categoryFunction(a,t,j):a instanceof Date?AmCharts.newDate(a,j.minPeriod):q?AmCharts.stringToDate(a,q):new Date(a),a=AmCharts.resetDateToMin(a,k,l,j.firstDayOfWeek),s.category=a,s.time=a.getTime(),!isNaN(s.time))){var u=this.valueAxes;s.axes={},s.x={};var v;for(v=0;v<u.length;v++){var w=u[v].id;s.axes[w]={},s.axes[w].graphs={};var x;for(x=0;x<b.length;x++){a=b[x];var y=a.id,z=a.periodValue;if(a.valueAxis.id==w){s.axes[w].graphs[y]={};var A={};A.index=p;var B=t;a.dataProvider&&(B=c),A.values=this.processValues(B,a,z),!a.connect&&r&&r[y]&&s.time-n[y]>1.1*m&&(r[y].gap=!0),this.processFields(a,A,B),A.category=s.category,A.serialDataItem=s,A.graph=a,s.axes[w].graphs[y]=A,n[y]=s.time,r[y]=A}}}this.chartData[p]=s}}}for(c=0;c<b.length;c++)a=b[c],a.dataProvider&&this.parseGraphData(a)},processValues:function(a,b,c){var d,e={},f=!1;return"candlestick"!=b.type&&"ohlc"!=b.type||""===c||(f=!0),d=Number(a[b.valueField+c]),isNaN(d)||(e.value=d),d=Number(a[b.errorField+c]),isNaN(d)||(e.error=d),f&&(c="Open"),d=Number(a[b.openField+c]),isNaN(d)||(e.open=d),f&&(c="Close"),d=Number(a[b.closeField+c]),isNaN(d)||(e.close=d),f&&(c="Low"),d=Number(a[b.lowField+c]),isNaN(d)||(e.low=d),f&&(c="High"),d=Number(a[b.highField+c]),isNaN(d)||(e.high=d),e},parseGraphData:function(a){var b=a.dataProvider,c=a.seriesIdField;c||(c=this.seriesIdField),c||(c=this.categoryField);var d;for(d=0;d<b.length;d++){var e=b[d],f=this.lookupTable[String(e[c])],g=a.valueAxis.id;f&&(g=f.axes[g].graphs[a.id],g.serialDataItem=f,g.values=this.processValues(e,a,a.periodValue),this.processFields(a,g,e))}},addValueAxis:function(a){a.chart=this,this.valueAxes.push(a),this.validateData()},removeValueAxesAndGraphs:function(){var a,b=this.valueAxes;for(a=b.length-1;a>-1;a--)this.removeValueAxis(b[a])},removeValueAxis:function(a){var b,c=this.graphs;for(b=c.length-1;b>=0;b--){var d=c[b];d&&d.valueAxis==a&&this.removeGraph(d)}for(c=this.valueAxes,b=c.length-1;b>=0;b--)c[b]==a&&c.splice(b,1);this.validateData()},addGraph:function(a){this.graphs.push(a),this.chooseGraphColor(a,this.graphs.length-1),this.validateData()},removeGraph:function(a){var b,c=this.graphs;for(b=c.length-1;b>=0;b--)c[b]==a&&(c.splice(b,1),a.destroy());this.validateData();

},processValueAxes:function(){var a,b=this.valueAxes;for(a=0;a<b.length;a++){var c=b[a],c=AmCharts.processObject(c,AmCharts.ValueAxis,this.theme);b[a]=c,c.chart=this,c.id||(c.id="valueAxisAuto"+a+"_"+(new Date).getTime()),void 0===c.usePrefixes&&(c.usePrefixes=this.usePrefixes)}},processGuides:function(){var a=this.guides,b=this.categoryAxis;if(a)for(var c=0;c<a.length;c++){var d=a[c];(void 0!==d.category||void 0!==d.date)&&b&&b.addGuide(d);var e=d.valueAxis;e?(AmCharts.isString(e)&&(e=this.getValueAxisById(e)),e?e.addGuide(d):this.valueAxes[0].addGuide(d)):isNaN(d.value)||this.valueAxes[0].addGuide(d)}},processGraphs:function(){var a,b=this.graphs;for(a=0;a<b.length;a++){var c=b[a],c=AmCharts.processObject(c,AmCharts.AmGraph,this.theme);b[a]=c,this.chooseGraphColor(c,a),c.chart=this,AmCharts.isString(c.valueAxis)&&(c.valueAxis=this.getValueAxisById(c.valueAxis)),c.valueAxis||(c.valueAxis=this.valueAxes[0]),c.id||(c.id="graphAuto"+a+"_"+(new Date).getTime())}},formatString:function(a,b,c){var d=b.graph,e=d.valueAxis;return e.duration&&b.values.value&&(e=AmCharts.formatDuration(b.values.value,e.duration,"",e.durationUnits,e.maxInterval,e.numberFormatter),a=a.split("[[value]]").join(e)),a=AmCharts.massReplace(a,{"[[title]]":d.title,"[[description]]":b.description}),a=c?AmCharts.fixNewLines(a):AmCharts.fixBrakes(a),a=AmCharts.cleanFromEmpty(a)},getBalloonColor:function(a,b,c){var d=a.lineColor,e=a.balloonColor;return c&&(e=d),c=a.fillColorsR,"object"==typeof c?d=c[0]:void 0!==c&&(d=c),b.isNegative&&(c=a.negativeLineColor,a=a.negativeFillColors,"object"==typeof a?c=a[0]:void 0!==a&&(c=a),void 0!==c&&(d=c)),void 0!==b.color&&(d=b.color),void 0===e&&(e=d),e},getGraphById:function(a){return AmCharts.getObjById(this.graphs,a)},getValueAxisById:function(a){return AmCharts.getObjById(this.valueAxes,a)},processFields:function(a,b,c){if(a.itemColors){var d=a.itemColors,e=b.index;b.color=e<d.length?d[e]:AmCharts.randomColor()}for(d="lineColor color alpha fillColors description bullet customBullet bulletSize bulletConfig url labelColor dashLength pattern".split(" "),e=0;e<d.length;e++){var f=d[e],g=a[f+"Field"];g&&(g=c[g],AmCharts.isDefined(g)&&(b[f]=g))}b.dataContext=c},chooseGraphColor:function(a,b){if(a.lineColor)a.lineColorR=a.lineColor;else{var c;c=this.colors.length>b?this.colors[b]:AmCharts.randomColor(),a.lineColorR=c}a.fillColorsR=a.fillColors?a.fillColors:a.lineColorR,a.bulletBorderColorR=a.bulletBorderColor?a.bulletBorderColor:a.useLineColorForBulletBorder?a.lineColorR:a.bulletColor,a.bulletColorR=a.bulletColor?a.bulletColor:a.lineColorR,(c=this.patterns)&&(a.pattern=c[b])},handleLegendEvent:function(a){var b=a.type;if(a=a.dataItem,!this.legend.data&&a){var c=a.hidden,d=a.showBalloon;switch(b){case"clickMarker":this.textClickEnabled&&(d?this.hideGraphsBalloon(a):this.showGraphsBalloon(a));break;case"clickLabel":d?this.hideGraphsBalloon(a):this.showGraphsBalloon(a);break;case"rollOverItem":c||this.highlightGraph(a);break;case"rollOutItem":c||this.unhighlightGraph();break;case"hideItem":this.hideGraph(a);break;case"showItem":this.showGraph(a)}}},highlightGraph:function(a){var b,c=this.graphs,d=.2;if(this.legend&&(d=this.legend.rollOverGraphAlpha),1!=d)for(b=0;b<c.length;b++){var e=c[b];e!=a&&e.changeOpacity(d)}},unhighlightGraph:function(){var a;if(this.legend&&(a=this.legend.rollOverGraphAlpha),1!=a){a=this.graphs;var b;for(b=0;b<a.length;b++)a[b].changeOpacity(1)}},showGraph:function(a){a.switchable&&(a.hidden=!1,this.dataChanged=!0,"xy"!=this.type&&(this.marginsUpdated=!1),this.chartCreated&&this.initChart())},hideGraph:function(a){a.switchable&&(this.dataChanged=!0,"xy"!=this.type&&(this.marginsUpdated=!1),a.hidden=!0,this.chartCreated&&this.initChart())},hideGraphsBalloon:function(a){a.showBalloon=!1,this.updateLegend()},showGraphsBalloon:function(a){a.showBalloon=!0,this.updateLegend()},updateLegend:function(){this.legend&&this.legend.invalidateSize()},resetAnimation:function(){var a=this.graphs;if(a){var b;for(b=0;b<a.length;b++)a[b].animationPlayed=!1}},animateAgain:function(){this.resetAnimation(),this.validateNow()}}),AmCharts.AmSlicedChart=AmCharts.Class({inherits:AmCharts.AmChart,construct:function(a){this.createEvents("rollOverSlice","rollOutSlice","clickSlice","pullOutSlice","pullInSlice","rightClickSlice"),AmCharts.AmSlicedChart.base.construct.call(this,a),this.colors="#FF0F00 #FF6600 #FF9E01 #FCD202 #F8FF01 #B0DE09 #04D215 #0D8ECF #0D52D1 #2A0CD0 #8A0CCF #CD0D74 #754DEB #DDDDDD #999999 #333333 #000000 #57032A #CA9726 #990000 #4B0C25".split(" "),this.alpha=1,this.groupPercent=0,this.groupedTitle="Other",this.groupedPulled=!1,this.groupedAlpha=1,this.marginLeft=0,this.marginBottom=this.marginTop=10,this.marginRight=0,this.hoverAlpha=1,this.outlineColor="#FFFFFF",this.outlineAlpha=0,this.outlineThickness=1,this.startAlpha=0,this.startDuration=1,this.startEffect="bounce",this.sequencedAnimation=!0,this.pullOutDuration=1,this.pullOutEffect="bounce",this.pullOnHover=this.pullOutOnlyOne=!1,this.labelsEnabled=!0,this.labelTickColor="#000000",this.labelTickAlpha=.2,this.hideLabelsPercent=0,this.urlTarget="_self",this.autoMarginOffset=10,this.gradientRatio=[],this.maxLabelWidth=200,AmCharts.applyTheme(this,a,"AmSlicedChart")},initChart:function(){AmCharts.AmSlicedChart.base.initChart.call(this),this.dataChanged&&(this.parseData(),this.dispatchDataUpdated=!0,this.dataChanged=!1,this.setLegendData(this.chartData)),this.drawChart()},handleLegendEvent:function(a){var b=a.type,c=a.dataItem,d=this.legend;if(!d.data&&c){var e=c.hidden;switch(a=a.event,b){case"clickMarker":e||d.switchable||this.clickSlice(c,a);break;case"clickLabel":e||this.clickSlice(c,a,!1);break;case"rollOverItem":e||this.rollOverSlice(c,!1,a);break;case"rollOutItem":e||this.rollOutSlice(c,a);break;case"hideItem":this.hideSlice(c,a);break;case"showItem":this.showSlice(c,a)}}},invalidateVisibility:function(){this.recalculatePercents(),this.initChart();var a=this.legend;a&&a.invalidateSize()},addEventListeners:function(a,b){var c=this;a.mouseover(function(a){c.rollOverSlice(b,!0,a)}).mouseout(function(a){c.rollOutSlice(b,a)}).touchend(function(a){c.rollOverSlice(b,a),c.panEventsEnabled&&c.clickSlice(b,a)}).touchstart(function(a){c.rollOverSlice(b,a)}).click(function(a){c.clickSlice(b,a)}).contextmenu(function(a){c.handleRightClick(b,a)})},formatString:function(a,b,c){return a=AmCharts.formatValue(a,b,["value"],this.nf,"",this.usePrefixes,this.prefixesOfSmallNumbers,this.prefixesOfBigNumbers),a=AmCharts.formatValue(a,b,["percents"],this.pf),a=AmCharts.massReplace(a,{"[[title]]":b.title,"[[description]]":b.description}),-1!=a.indexOf("[[")&&(a=AmCharts.formatDataContextValue(a,b.dataContext)),a=c?AmCharts.fixNewLines(a):AmCharts.fixBrakes(a),a=AmCharts.cleanFromEmpty(a)},startSlices:function(){var a;for(a=0;a<this.chartData.length;a++)0<this.startDuration&&this.sequencedAnimation?this.setStartTO(a):this.startSlice(this.chartData[a])},setStartTO:function(a){var b=this;a=setTimeout(function(){b.startSequenced.call(b)},b.startDuration/b.chartData.length*500*a),b.timeOuts.push(a)},pullSlices:function(a){var b,c=this.chartData;for(b=0;b<c.length;b++){var d=c[b];d.pulled&&this.pullSlice(d,1,a)}},startSequenced:function(){var a,b=this.chartData;for(a=0;a<b.length;a++)if(!b[a].started){this.startSlice(this.chartData[a]);break}},startSlice:function(a){a.started=!0;var b=a.wedge,c=this.startDuration;b&&c>0&&(0<a.alpha&&b.show(),b.translate(a.startX,a.startY),b.animate({opacity:1,translate:"0,0"},c,this.startEffect))},showLabels:function(){var a,b=this.chartData;for(a=0;a<b.length;a++){var c=b[a];if(0<c.alpha){var d=c.label;d&&d.show(),(c=c.tick)&&c.show()}}},showSlice:function(a){isNaN(a)?a.hidden=!1:this.chartData[a].hidden=!1,this.invalidateVisibility()},hideSlice:function(a){isNaN(a)?a.hidden=!0:this.chartData[a].hidden=!0,this.hideBalloon(),this.invalidateVisibility()},rollOverSlice:function(a,b,c){if(isNaN(a)||(a=this.chartData[a]),clearTimeout(this.hoverInt),!a.hidden){this.pullOnHover&&this.pullSlice(a,1),1>this.hoverAlpha&&a.wedge&&a.wedge.attr({opacity:this.hoverAlpha});var d=a.balloonX,e=a.balloonY;a.pulled&&(d+=a.pullX,e+=a.pullY);var f=this.formatString(this.balloonText,a,!0),g=this.balloonFunction;g&&(f=g(a,f)),g=AmCharts.adjustLuminosity(a.color,-.15),this.showBalloon(f,g,b,d,e),a={type:"rollOverSlice",dataItem:a,chart:this,event:c},this.fire(a.type,a)}},rollOutSlice:function(a,b){isNaN(a)||(a=this.chartData[a]),a.wedge&&a.wedge.attr({opacity:1}),this.hideBalloon();var c={type:"rollOutSlice",dataItem:a,chart:this,event:b};this.fire(c.type,c)},clickSlice:function(a,b,c){isNaN(a)||(a=this.chartData[a]),a.pulled?this.pullSlice(a,0):this.pullSlice(a,1),AmCharts.getURL(a.url,this.urlTarget),c||(a={type:"clickSlice",dataItem:a,chart:this,event:b},this.fire(a.type,a))},handleRightClick:function(a,b){isNaN(a)||(a=this.chartData[a]);var c={type:"rightClickSlice",dataItem:a,chart:this,event:b};this.fire(c.type,c)},drawTicks:function(){var a,b=this.chartData;for(a=0;a<b.length;a++){var c=b[a];if(c.label){var d=c.ty,d=AmCharts.line(this.container,[c.tx0,c.tx,c.tx2],[c.ty0,d,d],this.labelTickColor,this.labelTickAlpha);c.tick=d,c.wedge.push(d)}}},initialStart:function(){var a=this,b=a.startDuration,c=setTimeout(function(){a.showLabels.call(a)},1e3*b);a.timeOuts.push(c),a.chartCreated?a.pullSlices(!0):(a.startSlices(),b>0?(b=setTimeout(function(){a.pullSlices.call(a)},1200*b),a.timeOuts.push(b)):a.pullSlices(!0))},pullSlice:function(a,b,c){var d=this.pullOutDuration;!0===c&&(d=0),(c=a.wedge)&&(d>0?c.animate({translate:b*a.pullX+","+b*a.pullY},d,this.pullOutEffect):c.translate(b*a.pullX,b*a.pullY)),1==b?(a.pulled=!0,this.pullOutOnlyOne&&this.pullInAll(a.index),a={type:"pullOutSlice",dataItem:a,chart:this}):(a.pulled=!1,a={type:"pullInSlice",dataItem:a,chart:this}),this.fire(a.type,a)},pullInAll:function(a){var b,c=this.chartData;for(b=0;b<this.chartData.length;b++)b!=a&&c[b].pulled&&this.pullSlice(c[b],0)},pullOutAll:function(a){a=this.chartData;var b;for(b=0;b<a.length;b++)a[b].pulled||this.pullSlice(a[b],1)},parseData:function(){var a=[];this.chartData=a;var b=this.dataProvider;if(isNaN(this.pieAlpha)||(this.alpha=this.pieAlpha),void 0!==b){var c,d,e,f=b.length,g=0;for(c=0;f>c;c++){d={};var h=b[c];d.dataContext=h,d.value=Number(h[this.valueField]),(e=h[this.titleField])||(e=""),d.title=e,d.pulled=AmCharts.toBoolean(h[this.pulledField],!1),(e=h[this.descriptionField])||(e=""),d.description=e,d.labelRadius=Number(h[this.labelRadiusField]),d.switchable=!0,d.url=h[this.urlField],e=h[this.patternField],!e&&this.patterns&&(e=this.patterns[c]),d.pattern=e,d.visibleInLegend=AmCharts.toBoolean(h[this.visibleInLegendField],!0),e=h[this.alphaField],d.alpha=void 0!==e?Number(e):this.alpha,e=h[this.colorField],void 0!==e&&(d.color=AmCharts.toColor(e)),d.labelColor=AmCharts.toColor(h[this.labelColorField]),g+=d.value,d.hidden=!1,a[c]=d}for(c=b=0;f>c;c++)d=a[c],d.percents=d.value/g*100,d.percents<this.groupPercent&&b++;for(b>1&&(this.groupValue=0,this.removeSmallSlices(),a.push({title:this.groupedTitle,value:this.groupValue,percents:this.groupValue/g*100,pulled:this.groupedPulled,color:this.groupedColor,url:this.groupedUrl,description:this.groupedDescription,alpha:this.groupedAlpha,pattern:this.groupedPattern,dataContext:{}})),f=this.baseColor,f||(f=this.pieBaseColor),g=this.brightnessStep,g||(g=this.pieBrightnessStep),c=0;c<a.length;c++)f?e=AmCharts.adjustLuminosity(f,c*g/100):(e=this.colors[c],void 0===e&&(e=AmCharts.randomColor())),void 0===a[c].color&&(a[c].color=e);this.recalculatePercents()}},recalculatePercents:function(){var a,b,c=this.chartData,d=0;for(a=0;a<c.length;a++)b=c[a],!b.hidden&&0<b.value&&(d+=b.value);for(a=0;a<c.length;a++)b=this.chartData[a],b.percents=!b.hidden&&0<b.value?100*b.value/d:0},removeSmallSlices:function(){var a,b=this.chartData;for(a=b.length-1;a>=0;a--)b[a].percents<this.groupPercent&&(this.groupValue+=b[a].value,b.splice(a,1))},animateAgain:function(){var a=this;a.startSlices();for(var b=0;b<a.chartData.length;b++){var c=a.chartData[b];c.started=!1;var d=c.wedge;d&&d.translate(c.startX,c.startY)}b=a.startDuration,b>0?(b=setTimeout(function(){a.pullSlices.call(a)},1200*b),a.timeOuts.push(b)):a.pullSlices()},measureMaxLabel:function(){var a,b=this.chartData,c=0;for(a=0;a<b.length;a++){var d=b[a],e=this.formatString(this.labelText,d),f=this.labelFunction;f&&(e=f(d,e)),d=AmCharts.text(this.container,e,this.color,this.fontFamily,this.fontSize),e=d.getBBox().width,e>c&&(c=e),d.remove()}return c}}),AmCharts.AmRectangularChart=AmCharts.Class({inherits:AmCharts.AmCoordinateChart,construct:function(a){AmCharts.AmRectangularChart.base.construct.call(this,a),this.theme=a,this.createEvents("zoomed"),this.marginRight=this.marginBottom=this.marginTop=this.marginLeft=20,this.verticalPosition=this.horizontalPosition=this.depth3D=this.angle=0,this.heightMultiplier=this.widthMultiplier=1,this.plotAreaFillColors="#FFFFFF",this.plotAreaFillAlphas=0,this.plotAreaBorderColor="#000000",this.plotAreaBorderAlpha=0,this.zoomOutButtonImageSize=17,this.zoomOutButtonImage="lens.png",this.zoomOutText="Show all",this.zoomOutButtonColor="#e5e5e5",this.zoomOutButtonAlpha=0,this.zoomOutButtonRollOverAlpha=1,this.zoomOutButtonPadding=8,this.trendLines=[],this.autoMargins=!0,this.marginsUpdated=!1,this.autoMarginOffset=10,AmCharts.applyTheme(this,a,"AmRectangularChart")},initChart:function(){AmCharts.AmRectangularChart.base.initChart.call(this),this.updateDxy();var a=!0;!this.marginsUpdated&&this.autoMargins&&(this.resetMargins(),a=!1),this.processScrollbars(),this.updateMargins(),this.updatePlotArea(),this.updateScrollbars(),this.updateTrendLines(),this.updateChartCursor(),this.updateValueAxes(),a&&(this.scrollbarOnly||this.updateGraphs())},drawChart:function(){if(AmCharts.AmRectangularChart.base.drawChart.call(this),this.drawPlotArea(),AmCharts.ifArray(this.chartData)){var a=this.chartCursor;a&&a.draw(),a=this.zoomOutText,""!==a&&a&&this.drawZoomOutButton()}},resetMargins:function(){var a,b={};if("serial"==this.type){var c=this.valueAxes;for(a=0;a<c.length;a++){var d=c[a];d.ignoreAxisWidth||(d.setOrientation(this.rotate),d.fixAxisPosition(),b[d.position]=!0)}(a=this.categoryAxis)&&!a.ignoreAxisWidth&&(a.setOrientation(!this.rotate),a.fixAxisPosition(),a.fixAxisPosition(),b[a.position]=!0)}else{for(d=this.xAxes,c=this.yAxes,a=0;a<d.length;a++){var e=d[a];e.ignoreAxisWidth||(e.setOrientation(!0),e.fixAxisPosition(),b[e.position]=!0)}for(a=0;a<c.length;a++)d=c[a],d.ignoreAxisWidth||(d.setOrientation(!1),d.fixAxisPosition(),b[d.position]=!0)}b.left&&(this.marginLeft=0),b.right&&(this.marginRight=0),b.top&&(this.marginTop=0),b.bottom&&(this.marginBottom=0),this.fixMargins=b},measureMargins:function(){var a,b=this.valueAxes,c=this.autoMarginOffset,d=this.fixMargins,e=this.realWidth,f=this.realHeight,g=c,h=c,i=e;a=f;var j;for(j=0;j<b.length;j++)a=this.getAxisBounds(b[j],g,i,h,a),g=Math.round(a.l),i=Math.round(a.r),h=Math.round(a.t),a=Math.round(a.b);(b=this.categoryAxis)&&(a=this.getAxisBounds(b,g,i,h,a),g=Math.round(a.l),i=Math.round(a.r),h=Math.round(a.t),a=Math.round(a.b)),d.left&&c>g&&(this.marginLeft=Math.round(-g+c)),d.right&&i>=e-c&&(this.marginRight=Math.round(i-e+c)),d.top&&h<c+this.titleHeight&&(this.marginTop=Math.round(this.marginTop-h+c+this.titleHeight)),d.bottom&&a>f-c&&(this.marginBottom=Math.round(this.marginBottom+a-f+c)),this.initChart()},getAxisBounds:function(a,b,c,d,e){if(!a.ignoreAxisWidth){var f=a.labelsSet,g=a.tickLength;if(a.inside&&(g=0),f)switch(f=a.getBBox(),a.position){case"top":a=f.y,d>a&&(d=a);break;case"bottom":a=f.y+f.height,a>e&&(e=a);break;case"right":a=f.x+f.width+g+3,a>c&&(c=a);break;case"left":a=f.x-g,b>a&&(b=a)}}return{l:b,t:d,r:c,b:e}},drawZoomOutButton:function(){var a=this,b=a.container.set();a.zoomButtonSet.push(b);var c=a.color,d=a.fontSize,e=a.zoomOutButtonImageSize,f=a.zoomOutButtonImage,g=AmCharts.lang.zoomOutText||a.zoomOutText,h=a.zoomOutButtonColor,i=a.zoomOutButtonAlpha,j=a.zoomOutButtonFontSize,k=a.zoomOutButtonPadding;isNaN(j)||(d=j),(j=a.zoomOutButtonFontColor)&&(c=j);var l,j=a.zoomOutButton;j&&(j.fontSize&&(d=j.fontSize),j.color&&(c=j.color),j.backgroundColor&&(h=j.backgroundColor),isNaN(j.backgroundAlpha)||(a.zoomOutButtonRollOverAlpha=j.backgroundAlpha));var m=j=0;for(void 0!==a.pathToImages&&f&&(l=a.container.image(a.pathToImages+f,0,0,e,e),b.push(l),l=l.getBBox(),j=l.width+5),void 0!==g&&(c=AmCharts.text(a.container,g,c,a.fontFamily,d,"start"),d=c.getBBox(),m=l?l.height/2-3:d.height/2,c.translate(j,m),b.push(c)),l=b.getBBox(),c=1,AmCharts.isModern||(c=0),h=AmCharts.rect(a.container,l.width+2*k+5,l.height+2*k-2,h,1,1,h,c),h.setAttr("opacity",i),h.translate(-k,-k),b.push(h),h.toBack(),a.zbBG=h,l=h.getBBox(),b.translate(a.marginLeftReal+a.plotAreaWidth-l.width+k,a.marginTopReal+k),b.hide(),b.mouseover(function(){a.rollOverZB()}).mouseout(function(){a.rollOutZB()}).click(function(){a.clickZB()}).touchstart(function(){a.rollOverZB()}).touchend(function(){a.rollOutZB(),a.clickZB()}),i=0;i<b.length;i++)b[i].attr({cursor:"pointer"});a.zbSet=b},rollOverZB:function(){this.zbBG.setAttr("opacity",this.zoomOutButtonRollOverAlpha)},rollOutZB:function(){this.zbBG.setAttr("opacity",this.zoomOutButtonAlpha)},clickZB:function(){this.zoomOut()},zoomOut:function(){this.updateScrollbar=!0,this.zoom()},drawPlotArea:function(){var a=this.dx,b=this.dy,c=this.marginLeftReal,d=this.marginTopReal,e=this.plotAreaWidth-1,f=this.plotAreaHeight-1,g=this.plotAreaFillColors,h=this.plotAreaFillAlphas,i=this.plotAreaBorderColor,j=this.plotAreaBorderAlpha;this.trendLinesSet.clipRect(c,d,e,f),"object"==typeof h&&(h=h[0]),g=AmCharts.polygon(this.container,[0,e,e,0,0],[0,0,f,f,0],g,h,1,i,j,this.plotAreaGradientAngle),g.translate(c+a,d+b),this.set.push(g),0!==a&&0!==b&&(g=this.plotAreaFillColors,"object"==typeof g&&(g=g[0]),g=AmCharts.adjustLuminosity(g,-.15),e=AmCharts.polygon(this.container,[0,a,e+a,e,0],[0,b,b,0,0],g,h,1,i,j),e.translate(c,d+f),this.set.push(e),a=AmCharts.polygon(this.container,[0,0,a,a,0],[0,f,f+b,b,0],g,h,1,i,j),a.translate(c,d),this.set.push(a)),(c=this.bbset)&&this.scrollbarOnly&&c.remove()},updatePlotArea:function(){var a=this.updateWidth(),b=this.updateHeight(),c=this.container;this.realWidth=a,this.realWidth=b,c&&this.container.setSize(a,b),a=a-this.marginLeftReal-this.marginRightReal-this.dx,b=b-this.marginTopReal-this.marginBottomReal,1>a&&(a=1),1>b&&(b=1),this.plotAreaWidth=Math.round(a),this.plotAreaHeight=Math.round(b)},updateDxy:function(){this.dx=Math.round(this.depth3D*Math.cos(this.angle*Math.PI/180)),this.dy=Math.round(-this.depth3D*Math.sin(this.angle*Math.PI/180)),this.d3x=Math.round(this.columnSpacing3D*Math.cos(this.angle*Math.PI/180)),this.d3y=Math.round(-this.columnSpacing3D*Math.sin(this.angle*Math.PI/180))},updateMargins:function(){var a=this.getTitleHeight();this.titleHeight=a,this.marginTopReal=this.marginTop-this.dy+a,this.marginBottomReal=this.marginBottom,this.marginLeftReal=this.marginLeft,this.marginRightReal=this.marginRight},updateValueAxes:function(){var a,b=this.valueAxes,c=this.marginLeftReal,d=this.marginTopReal,e=this.plotAreaHeight,f=this.plotAreaWidth;for(a=0;a<b.length;a++){var g=b[a];g.axisRenderer=AmCharts.RecAxis,g.guideFillRenderer=AmCharts.RecFill,g.axisItemRenderer=AmCharts.RecItem,g.dx=this.dx,g.dy=this.dy,g.viW=f-1,g.viH=e-1,g.marginsChanged=!0,g.viX=c,g.viY=d,this.updateObjectSize(g)}},updateObjectSize:function(a){a.width=(this.plotAreaWidth-1)*this.widthMultiplier,a.height=(this.plotAreaHeight-1)*this.heightMultiplier,a.x=this.marginLeftReal+this.horizontalPosition,a.y=this.marginTopReal+this.verticalPosition},updateGraphs:function(){var a,b=this.graphs;for(a=0;a<b.length;a++){var c=b[a];c.x=this.marginLeftReal+this.horizontalPosition,c.y=this.marginTopReal+this.verticalPosition,c.width=this.plotAreaWidth*this.widthMultiplier,c.height=this.plotAreaHeight*this.heightMultiplier,c.index=a,c.dx=this.dx,c.dy=this.dy,c.rotate=this.rotate}},updateChartCursor:function(){var a=this.chartCursor;a&&(a=AmCharts.processObject(a,AmCharts.ChartCursor,this.theme),this.addChartCursor(a),a.x=this.marginLeftReal,a.y=this.marginTopReal,a.width=this.plotAreaWidth-1,a.height=this.plotAreaHeight-1,a.chart=this)},processScrollbars:function(){var a=this.chartScrollbar;a&&(a=AmCharts.processObject(a,AmCharts.ChartScrollbar,this.theme),this.addChartScrollbar(a))},updateScrollbars:function(){},addChartCursor:function(a){AmCharts.callMethod("destroy",[this.chartCursor]),a&&(this.listenTo(a,"changed",this.handleCursorChange),this.listenTo(a,"zoomed",this.handleCursorZoom)),this.chartCursor=a},removeChartCursor:function(){AmCharts.callMethod("destroy",[this.chartCursor]),this.chartCursor=null},zoomTrendLines:function(){var a,b=this.trendLines;for(a=0;a<b.length;a++){var c=b[a];c.valueAxis.recalculateToPercents?c.set&&c.set.hide():(c.x=this.marginLeftReal+this.horizontalPosition,c.y=this.marginTopReal+this.verticalPosition,c.draw())}},addTrendLine:function(a){this.trendLines.push(a)},removeTrendLine:function(a){var b,c=this.trendLines;for(b=c.length-1;b>=0;b--)c[b]==a&&c.splice(b,1)},adjustMargins:function(a,b){var c=a.scrollbarHeight+a.offset;"top"==a.position?b?this.marginLeftReal+=c:this.marginTopReal+=c:b?this.marginRightReal+=c:this.marginBottomReal+=c},getScrollbarPosition:function(a,b,c){a.position=b?"bottom"==c||"left"==c?"bottom":"top":"top"==c||"right"==c?"bottom":"top"},updateChartScrollbar:function(a,b){if(a){a.rotate=b;var c=this.marginTopReal,d=this.marginLeftReal,e=a.scrollbarHeight,f=this.dx,g=this.dy,h=a.offset;"top"==a.position?b?(a.y=c,a.x=d-e-h):(a.y=c-e+g-1-h,a.x=d+f):b?(a.y=c+g,a.x=d+this.plotAreaWidth+f+h):(a.y=c+this.plotAreaHeight+h,a.x=this.marginLeftReal)}},showZB:function(a){var b=this.zbSet;b&&(a?b.show():b.hide(),this.rollOutZB())},handleReleaseOutside:function(a){AmCharts.AmRectangularChart.base.handleReleaseOutside.call(this,a),(a=this.chartCursor)&&a.handleReleaseOutside()},handleMouseDown:function(a){AmCharts.AmRectangularChart.base.handleMouseDown.call(this,a);var b=this.chartCursor;b&&b.handleMouseDown(a)},handleCursorChange:function(a){}}),AmCharts.TrendLine=AmCharts.Class({construct:function(a){this.cname="TrendLine",this.createEvents("click"),this.isProtected=!1,this.dashLength=0,this.lineColor="#00CC00",this.lineThickness=this.lineAlpha=1,AmCharts.applyTheme(this,a,this.cname)},draw:function(){var a=this;a.destroy();var b,c,d,e,f=a.chart,g=f.container,h=a.categoryAxis,i=a.initialDate,j=a.initialCategory,k=a.finalDate,l=a.finalCategory,m=a.valueAxis,n=a.valueAxisX,o=a.initialXValue,p=a.finalXValue,q=a.initialValue,r=a.finalValue,s=m.recalculateToPercents,t=f.dataDateFormat;h&&(i&&(i instanceof Date||(i=t?AmCharts.stringToDate(i,t):new Date(i)),a.initialDate=i,b=h.dateToCoordinate(i)),j&&(b=h.categoryToCoordinate(j)),k&&(k instanceof Date||(k=t?AmCharts.stringToDate(k,t):new Date(k)),a.finalDate=k,c=h.dateToCoordinate(k)),l&&(c=h.categoryToCoordinate(l))),n&&!s&&(isNaN(o)||(b=n.getCoordinate(o)),isNaN(p)||(c=n.getCoordinate(p))),m&&!s&&(isNaN(q)||(d=m.getCoordinate(q)),isNaN(r)||(e=m.getCoordinate(r))),isNaN(b)||isNaN(c)||isNaN(d)||isNaN(d)||(f.rotate?(h=[d,e],c=[b,c]):(h=[b,c],c=[d,e]),d=a.lineColor,b=AmCharts.line(g,h,c,d,a.lineAlpha,a.lineThickness,a.dashLength),e=h,i=c,l=h[1]-h[0],m=c[1]-c[0],0===l&&(l=.01),0===m&&(m=.01),j=l/Math.abs(l),k=m/Math.abs(m),m=l*m/Math.abs(l*m)*Math.sqrt(Math.pow(l,2)+Math.pow(m,2)),l=Math.asin(l/m),m=90*Math.PI/180-l,l=Math.abs(5*Math.cos(m)),m=Math.abs(5*Math.sin(m)),e.push(h[1]-j*m,h[0]-j*m),i.push(c[1]+k*l,c[0]+k*l),h=AmCharts.polygon(g,e,i,d,.005,0),g=g.set([h,b]),g.translate(f.marginLeftReal,f.marginTopReal),f.trendLinesSet.push(g),a.line=b,a.set=g,h.mouseup(function(){a.handleLineClick()}).mouseover(function(){a.handleLineOver()}).mouseout(function(){a.handleLineOut()}),h.touchend&&h.touchend(function(){a.handleLineClick()}))},handleLineClick:function(){var a={type:"click",trendLine:this,chart:this.chart};this.fire(a.type,a)},handleLineOver:function(){var a=this.rollOverColor;void 0!==a&&this.line.attr({stroke:a})},handleLineOut:function(){this.line.attr({stroke:this.lineColor})},destroy:function(){AmCharts.remove(this.set)}}),AmCharts.circle=function(a,b,c,d,e,f,g,h,i){return(void 0==e||0===e)&&(e=.01),void 0===f&&(f="#000000"),void 0===g&&(g=0),d={fill:c,stroke:f,"fill-opacity":d,"stroke-width":e,"stroke-opacity":g},a=isNaN(i)?a.circle(0,0,b).attr(d):a.ellipse(0,0,b,i).attr(d),h&&a.gradient("radialGradient",[c,AmCharts.adjustLuminosity(c,-.6)]),a},AmCharts.text=function(a,b,c,d,e,f,g,h){return f||(f="middle"),"right"==f&&(f="end"),isNaN(h)&&(h=1),void 0!==b&&(b=String(b),AmCharts.isIE&&!AmCharts.isModern&&(b=b.replace("&amp;","&"),b=b.replace("&","&amp;"))),c={fill:c,"font-family":d,"font-size":e,opacity:h},!0===g&&(c["font-weight"]="bold"),c["text-anchor"]=f,a.text(b,c)},AmCharts.polygon=function(a,b,c,d,e,f,g,h,i,j,k){isNaN(f)&&(f=.01),isNaN(h)&&(h=e);var l=d,m=!1;for("object"==typeof l&&1<l.length&&(m=!0,l=l[0]),void 0===g&&(g=l),e={fill:l,stroke:g,"fill-opacity":e,"stroke-width":f,"stroke-opacity":h},void 0!==k&&k>0&&(e["stroke-dasharray"]=k),k=AmCharts.dx,f=AmCharts.dy,a.handDrawn&&(c=AmCharts.makeHD(b,c,a.handDrawScatter),b=c[0],c=c[1]),g=Math.round,j&&(g=AmCharts.doNothing),j="M"+(g(b[0])+k)+","+(g(c[0])+f),h=1;h<b.length;h++)j+=" L"+(g(b[h])+k)+","+(g(c[h])+f);return a=a.path(j+" Z").attr(e),m&&a.gradient("linearGradient",d,i),a},AmCharts.rect=function(a,b,c,d,e,f,g,h,i,j,k){isNaN(f)&&(f=0),void 0===i&&(i=0),void 0===j&&(j=270),isNaN(e)&&(e=0);var l=d,m=!1;"object"==typeof l&&(l=l[0],m=!0),void 0===g&&(g=l),void 0===h&&(h=e),b=Math.round(b),c=Math.round(c);var n=0,o=0;return 0>b&&(b=Math.abs(b),n=-b),0>c&&(c=Math.abs(c),o=-c),n+=AmCharts.dx,o+=AmCharts.dy,e={fill:l,stroke:g,"fill-opacity":e,"stroke-opacity":h},void 0!==k&&k>0&&(e["stroke-dasharray"]=k),a=a.rect(n,o,b,c,i,f).attr(e),m&&a.gradient("linearGradient",d,j),a},AmCharts.bullet=function(a,b,c,d,e,f,g,h,i,j,k){var l;switch("circle"==b&&(b="round"),b){case"round":l=AmCharts.circle(a,c/2,d,e,f,g,h);break;case"square":l=AmCharts.polygon(a,[-c/2,c/2,c/2,-c/2],[c/2,c/2,-c/2,-c/2],d,e,f,g,h,j-180);break;case"rectangle":l=AmCharts.polygon(a,[-c,c,c,-c],[c/2,c/2,-c/2,-c/2],d,e,f,g,h,j-180);break;case"diamond":l=AmCharts.polygon(a,[-c/2,0,c/2,0],[0,-c/2,0,c/2],d,e,f,g,h);break;case"triangleUp":l=AmCharts.triangle(a,c,0,d,e,f,g,h);break;case"triangleDown":l=AmCharts.triangle(a,c,180,d,e,f,g,h);break;case"triangleLeft":l=AmCharts.triangle(a,c,270,d,e,f,g,h);break;case"triangleRight":l=AmCharts.triangle(a,c,90,d,e,f,g,h);break;case"bubble":l=AmCharts.circle(a,c/2,d,e,f,g,h,!0);break;case"line":l=AmCharts.line(a,[-c/2,c/2],[0,0],d,e,f,g,h);break;case"yError":l=a.set(),l.push(AmCharts.line(a,[0,0],[-c/2,c/2],d,e,f)),l.push(AmCharts.line(a,[-i,i],[-c/2,-c/2],d,e,f)),l.push(AmCharts.line(a,[-i,i],[c/2,c/2],d,e,f));break;case"xError":l=a.set(),l.push(AmCharts.line(a,[-c/2,c/2],[0,0],d,e,f)),l.push(AmCharts.line(a,[-c/2,-c/2],[-i,i],d,e,f)),l.push(AmCharts.line(a,[c/2,c/2],[-i,i],d,e,f))}return l&&l.pattern(k),l},AmCharts.triangle=function(a,b,c,d,e,f,g,h){(void 0===f||0===f)&&(f=1),void 0===g&&(g="#000"),void 0===h&&(h=0),d={fill:d,stroke:g,"fill-opacity":e,"stroke-width":f,"stroke-opacity":h},b/=2;var i;return 0===c&&(i=" M"+-b+","+b+" L0,"+-b+" L"+b+","+b+" Z"),180==c&&(i=" M"+-b+","+-b+" L0,"+b+" L"+b+","+-b+" Z"),90==c&&(i=" M"+-b+","+-b+" L"+b+",0 L"+-b+","+b+" Z"),270==c&&(i=" M"+-b+",0 L"+b+","+b+" L"+b+","+-b+" Z"),a.path(i).attr(d)},AmCharts.line=function(a,b,c,d,e,f,g,h,i,j,k){if(a.handDrawn&&!k)return AmCharts.handDrawnLine(a,b,c,d,e,f,g,h,i,j,k);for(f={fill:"none","stroke-width":f},void 0!==g&&g>0&&(f["stroke-dasharray"]=g),isNaN(e)||(f["stroke-opacity"]=e),d&&(f.stroke=d),d=Math.round,j&&(d=AmCharts.doNothing),j=AmCharts.dx,e=AmCharts.dy,g="M"+(d(b[0])+j)+","+(d(c[0])+e),h=1;h<b.length;h++)g+=" L"+(d(b[h])+j)+","+(d(c[h])+e);return AmCharts.VML?a.path(g,void 0,!0).attr(f):(i&&(g+=" M0,0 L0,0"),a.path(g).attr(f))},AmCharts.makeHD=function(a,b,c){for(var d=[],e=[],f=1;f<a.length;f++)for(var g=Number(a[f-1]),h=Number(b[f-1]),i=Number(a[f]),j=Number(b[f]),k=Math.sqrt(Math.pow(i-g,2)+Math.pow(j-h,2)),k=Math.round(k/50)+1,i=(i-g)/k,j=(j-h)/k,l=0;k>=l;l++){var m=g+l*i+Math.random()*c,n=h+l*j+Math.random()*c;d.push(m),e.push(n)}return[d,e]},AmCharts.handDrawnLine=function(a,b,c,d,e,f,g,h,i,j,k){var l=a.set();for(k=1;k<b.length;k++)for(var m=[b[k-1],b[k]],n=[c[k-1],c[k]],n=AmCharts.makeHD(m,n,a.handDrawScatter),m=n[0],n=n[1],o=1;o<m.length;o++)l.push(AmCharts.line(a,[m[o-1],m[o]],[n[o-1],n[o]],d,e,f+Math.random()*a.handDrawThickness-a.handDrawThickness/2,g,h,i,j,!0));return l},AmCharts.doNothing=function(a){return a},AmCharts.wedge=function(a,b,c,d,e,f,g,h,i,j,k,l){var m=Math.round;f=m(f),g=m(g),h=m(h);var n=m(g/f*h),o=AmCharts.VML,p=359.5+f/100;p>359.94&&(p=359.94),e>=p&&(e=p);var q=1/180*Math.PI,p=b+Math.sin(d*q)*h,r=c-Math.cos(d*q)*n,s=b+Math.sin(d*q)*f,t=c-Math.cos(d*q)*g,u=b+Math.sin((d+e)*q)*f,v=c-Math.cos((d+e)*q)*g,w=b+Math.sin((d+e)*q)*h,q=c-Math.cos((d+e)*q)*n,x={fill:AmCharts.adjustLuminosity(j.fill,-.2),"stroke-opacity":0,"fill-opacity":j["fill-opacity"]},y=0;180<Math.abs(e)&&(y=1),d=a.set();var z;o&&(p=m(10*p),s=m(10*s),u=m(10*u),w=m(10*w),r=m(10*r),t=m(10*t),v=m(10*v),q=m(10*q),b=m(10*b),i=m(10*i),c=m(10*c),f*=10,g*=10,h*=10,n*=10,1>Math.abs(e)&&1>=Math.abs(u-s)&&1>=Math.abs(v-t)&&(z=!0)),e="";var A;if(l&&(x["fill-opacity"]=0,x["stroke-opacity"]=j["stroke-opacity"]/2,x.stroke=j.stroke),i>0&&(A=" M"+p+","+(r+i)+" L"+s+","+(t+i),o?(z||(A+=" A"+(b-f)+","+(i+c-g)+","+(b+f)+","+(i+c+g)+","+s+","+(t+i)+","+u+","+(v+i)),A+=" L"+w+","+(q+i),h>0&&(z||(A+=" B"+(b-h)+","+(i+c-n)+","+(b+h)+","+(i+c+n)+","+w+","+(i+q)+","+p+","+(i+r)))):(A+=" A"+f+","+g+",0,"+y+",1,"+u+","+(v+i)+" L"+w+","+(q+i),h>0&&(A+=" A"+h+","+n+",0,"+y+",0,"+p+","+(r+i))),A=a.path(A+" Z",void 0,void 0,"1000,1000").attr(x),d.push(A),A=a.path(" M"+p+","+r+" L"+p+","+(r+i)+" L"+s+","+(t+i)+" L"+s+","+t+" L"+p+","+r+" Z",void 0,void 0,"1000,1000").attr(x),i=a.path(" M"+u+","+v+" L"+u+","+(v+i)+" L"+w+","+(q+i)+" L"+w+","+q+" L"+u+","+v+" Z",void 0,void 0,"1000,1000").attr(x),d.push(A),d.push(i)),o?(z||(e=" A"+m(b-f)+","+m(c-g)+","+m(b+f)+","+m(c+g)+","+m(s)+","+m(t)+","+m(u)+","+m(v)),f=" M"+m(p)+","+m(r)+" L"+m(s)+","+m(t)+e+" L"+m(w)+","+m(q)):f=" M"+p+","+r+" L"+s+","+t+(" A"+f+","+g+",0,"+y+",1,"+u+","+v)+" L"+w+","+q,h>0&&(o?z||(f+=" B"+(b-h)+","+(c-n)+","+(b+h)+","+(c+n)+","+w+","+q+","+p+","+r):f+=" A"+h+","+n+",0,"+y+",0,"+p+","+r),a.handDrawn&&(b=AmCharts.line(a,[p,s],[r,t],j.stroke,j.thickness*Math.random()*a.handDrawThickness,j["stroke-opacity"]),d.push(b)),a=a.path(f+" Z",void 0,void 0,"1000,1000").attr(j),k){for(b=[],c=0;c<k.length;c++)b.push(AmCharts.adjustLuminosity(j.fill,k[c]));0<b.length&&a.gradient("linearGradient",b)}return a.pattern(l),d.push(a),d},AmCharts.adjustLuminosity=function(a,b){a=String(a).replace(/[^0-9a-f]/gi,""),6>a.length&&(a=String(a[0])+String(a[0])+String(a[1])+String(a[1])+String(a[2])+String(a[2])),b=b||0;var c,d,e="#";for(d=0;3>d;d++)c=parseInt(a.substr(2*d,2),16),c=Math.round(Math.min(Math.max(0,c+c*b),255)).toString(16),e+=("00"+c).substr(c.length);return e},AmCharts.Bezier=AmCharts.Class({construct:function(a,b,c,d,e,f,g,h,i,j){for("object"==typeof g&&(g=g[0]),"object"==typeof h&&(h=h[0]),f={fill:g,"fill-opacity":h,"stroke-width":f},void 0!==i&&i>0&&(f["stroke-dasharray"]=i),isNaN(e)||(f["stroke-opacity"]=e),d&&(f.stroke=d),d="M"+Math.round(b[0])+","+Math.round(c[0]),e=[],i=0;i<b.length;i++)e.push({x:Number(b[i]),y:Number(c[i])});1<e.length&&(b=this.interpolate(e),d+=this.drawBeziers(b)),j?d+=j:AmCharts.VML||(d+="M0,0 L0,0"),this.path=a.path(d).attr(f)},interpolate:function(a){var b=[];b.push({x:a[0].x,y:a[0].y});var c=a[1].x-a[0].x,d=a[1].y-a[0].y,e=AmCharts.bezierX,f=AmCharts.bezierY;b.push({x:a[0].x+c/e,y:a[0].y+d/f});var g;for(g=1;g<a.length-1;g++){var h=a[g-1],i=a[g],d=a[g+1];isNaN(d.x)&&(d=i),isNaN(i.x)&&(i=h),isNaN(h.x)&&(h=i),c=d.x-i.x,d=d.y-h.y,
h=i.x-h.x,h>c&&(h=c),b.push({x:i.x-h/e,y:i.y-d/f}),b.push({x:i.x,y:i.y}),b.push({x:i.x+h/e,y:i.y+d/f})}return d=a[a.length-1].y-a[a.length-2].y,c=a[a.length-1].x-a[a.length-2].x,b.push({x:a[a.length-1].x-c/e,y:a[a.length-1].y-d/f}),b.push({x:a[a.length-1].x,y:a[a.length-1].y}),b},drawBeziers:function(a){var b,c="";for(b=0;b<(a.length-1)/3;b++)c+=this.drawBezierMidpoint(a[3*b],a[3*b+1],a[3*b+2],a[3*b+3]);return c},drawBezierMidpoint:function(a,b,c,d){var e=Math.round,f=this.getPointOnSegment(a,b,.75),g=this.getPointOnSegment(d,c,.75),h=(d.x-a.x)/16,i=(d.y-a.y)/16,j=this.getPointOnSegment(a,b,.375);return a=this.getPointOnSegment(f,g,.375),a.x-=h,a.y-=i,b=this.getPointOnSegment(g,f,.375),b.x+=h,b.y+=i,c=this.getPointOnSegment(d,c,.375),h=this.getMiddle(j,a),f=this.getMiddle(f,g),g=this.getMiddle(b,c),j=" Q"+e(j.x)+","+e(j.y)+","+e(h.x)+","+e(h.y),j+=" Q"+e(a.x)+","+e(a.y)+","+e(f.x)+","+e(f.y),j+=" Q"+e(b.x)+","+e(b.y)+","+e(g.x)+","+e(g.y),j+=" Q"+e(c.x)+","+e(c.y)+","+e(d.x)+","+e(d.y)},getMiddle:function(a,b){return{x:(a.x+b.x)/2,y:(a.y+b.y)/2}},getPointOnSegment:function(a,b,c){return{x:a.x+(b.x-a.x)*c,y:a.y+(b.y-a.y)*c}}}),AmCharts.AmDraw=AmCharts.Class({construct:function(a,b,c,d){if(AmCharts.SVG_NS="http://www.w3.org/2000/svg",AmCharts.SVG_XLINK="http://www.w3.org/1999/xlink",AmCharts.hasSVG=!!document.createElementNS&&!!document.createElementNS(AmCharts.SVG_NS,"svg").createSVGRect,1>b&&(b=10),1>c&&(c=10),this.div=a,this.width=b,this.height=c,this.rBin=document.createElement("div"),AmCharts.hasSVG){AmCharts.SVG=!0;var e=this.createSvgElement("svg");e.style.position="absolute",e.style.width=b+"px",e.style.height=c+"px",b=this.createSvgElement("desc"),b.appendChild(document.createTextNode("JavaScript chart by amCharts "+d.version)),e.appendChild(b),AmCharts.rtl&&(e.setAttribute("direction","rtl"),e.style.left="auto",e.style.right="0px"),e.setAttribute("version","1.1"),a.appendChild(e),this.container=e,this.R=new AmCharts.SVGRenderer(this)}else AmCharts.isIE&&AmCharts.VMLRenderer&&(AmCharts.VML=!0,AmCharts.vmlStyleSheet||(document.namespaces.add("amvml","urn:schemas-microsoft-com:vml"),31>document.styleSheets.length?(e=document.createStyleSheet(),e.addRule(".amvml","behavior:url(#default#VML); display:inline-block; antialias:true"),AmCharts.vmlStyleSheet=e):document.styleSheets[0].addRule(".amvml","behavior:url(#default#VML); display:inline-block; antialias:true")),this.container=a,this.R=new AmCharts.VMLRenderer(this,d),this.R.disableSelection(a))},createSvgElement:function(a){return document.createElementNS(AmCharts.SVG_NS,a)},circle:function(a,b,c,d){var e=new AmCharts.AmDObject("circle",this);return e.attr({r:c,cx:a,cy:b}),this.addToContainer(e.node,d),e},ellipse:function(a,b,c,d,e){var f=new AmCharts.AmDObject("ellipse",this);return f.attr({rx:c,ry:d,cx:a,cy:b}),this.addToContainer(f.node,e),f},setSize:function(a,b){a>0&&b>0&&(this.container.style.width=a+"px",this.container.style.height=b+"px")},rect:function(a,b,c,d,e,f,g){var h=new AmCharts.AmDObject("rect",this);return AmCharts.VML&&(e=Math.round(100*e/Math.min(c,d)),c+=2*f,d+=2*f,h.bw=f,h.node.style.marginLeft=-f,h.node.style.marginTop=-f),1>c&&(c=1),1>d&&(d=1),h.attr({x:a,y:b,width:c,height:d,rx:e,ry:e,"stroke-width":f}),this.addToContainer(h.node,g),h},image:function(a,b,c,d,e,f){var g=new AmCharts.AmDObject("image",this);return g.attr({x:b,y:c,width:d,height:e}),this.R.path(g,a),this.addToContainer(g.node,f),g},addToContainer:function(a,b){b||(b=this.container),b.appendChild(a)},text:function(a,b,c){return this.R.text(a,b,c)},path:function(a,b,c,d){var e=new AmCharts.AmDObject("path",this);return d||(d="100,100"),e.attr({cs:d}),e.attr(c?{dd:a}:{d:a}),this.addToContainer(e.node,b),e},set:function(a){return this.R.set(a)},remove:function(a){if(a){var b=this.rBin;b.appendChild(a),b.innerHTML=""}},renderFix:function(){var a,b=this.container,c=b.style;try{a=b.getScreenCTM()||b.createSVGMatrix()}catch(d){a=b.createSVGMatrix()}b=1-a.e%1,a=1-a.f%1,b>.5&&(b-=1),a>.5&&(a-=1),b&&(c.left=b+"px"),a&&(c.top=a+"px")},update:function(){this.R.update()}}),AmCharts.AmDObject=AmCharts.Class({construct:function(a,b){this.D=b,this.R=b.R,this.node=this.R.create(this,a),this.y=this.x=0,this.scale=1},attr:function(a){return this.R.attr(this,a),this},getAttr:function(a){return this.node.getAttribute(a)},setAttr:function(a,b){return this.R.setAttr(this,a,b),this},clipRect:function(a,b,c,d){this.R.clipRect(this,a,b,c,d)},translate:function(a,b,c,d){d||(a=Math.round(a),b=Math.round(b)),this.R.move(this,a,b,c),this.x=a,this.y=b,this.scale=c,this.angle&&this.rotate(this.angle)},rotate:function(a,b){this.R.rotate(this,a,b),this.angle=a},animate:function(a,b,c){for(var d in a)if(a.hasOwnProperty(d)){var e=d,f=a[d];c=AmCharts.getEffect(c),this.R.animate(this,e,f,b,c)}},push:function(a){if(a){var b=this.node;b.appendChild(a.node);var c=a.clipPath;c&&b.appendChild(c),(a=a.grad)&&b.appendChild(a)}},text:function(a){this.R.setText(this,a)},remove:function(){this.R.remove(this)},clear:function(){var a=this.node;if(a.hasChildNodes())for(;1<=a.childNodes.length;)a.removeChild(a.firstChild)},hide:function(){this.setAttr("visibility","hidden")},show:function(){this.setAttr("visibility","visible")},getBBox:function(){return this.R.getBBox(this)},toFront:function(){var a=this.node;if(a){this.prevNextNode=a.nextSibling;var b=a.parentNode;b&&b.appendChild(a)}},toPrevious:function(){var a=this.node;a&&this.prevNextNode&&(a=a.parentNode)&&a.insertBefore(this.prevNextNode,null)},toBack:function(){var a=this.node;if(a){this.prevNextNode=a.nextSibling;var b=a.parentNode;if(b){var c=b.firstChild;c&&b.insertBefore(a,c)}}},mouseover:function(a){return this.R.addListener(this,"mouseover",a),this},mouseout:function(a){return this.R.addListener(this,"mouseout",a),this},click:function(a){return this.R.addListener(this,"click",a),this},dblclick:function(a){return this.R.addListener(this,"dblclick",a),this},mousedown:function(a){return this.R.addListener(this,"mousedown",a),this},mouseup:function(a){return this.R.addListener(this,"mouseup",a),this},touchstart:function(a){return this.R.addListener(this,"touchstart",a),this},touchend:function(a){return this.R.addListener(this,"touchend",a),this},contextmenu:function(a){return this.node.addEventListener?this.node.addEventListener("contextmenu",a,!0):this.R.addListener(this,"contextmenu",a),this},stop:function(a){(a=this.animationX)&&AmCharts.removeFromArray(this.R.animations,a),(a=this.animationY)&&AmCharts.removeFromArray(this.R.animations,a)},length:function(){return this.node.childNodes.length},gradient:function(a,b,c){this.R.gradient(this,a,b,c)},pattern:function(a,b){a&&this.R.pattern(this,a,b)}}),AmCharts.VMLRenderer=AmCharts.Class({construct:function(a,b){this.chart=b,this.D=a,this.cNames={circle:"oval",ellipse:"oval",rect:"roundrect",path:"shape"},this.styleMap={x:"left",y:"top",width:"width",height:"height","font-family":"fontFamily","font-size":"fontSize",visibility:"visibility"}},create:function(a,b){var c;if("group"==b)c=document.createElement("div"),a.type="div";else if("text"==b)c=document.createElement("div"),a.type="text";else if("image"==b)c=document.createElement("img"),a.type="image";else{a.type="shape",a.shapeType=this.cNames[b],c=document.createElement("amvml:"+this.cNames[b]);var d=document.createElement("amvml:stroke");c.appendChild(d),a.stroke=d;var e=document.createElement("amvml:fill");c.appendChild(e),a.fill=e,e.className="amvml",d.className="amvml",c.className="amvml"}return c.style.position="absolute",c.style.top=0,c.style.left=0,c},path:function(a,b){a.node.setAttribute("src",b)},setAttr:function(a,b,c){if(void 0!==c){var d;8===document.documentMode&&(d=!0);var e=a.node,f=a.type,g=e.style;"r"==b&&(g.width=2*c,g.height=2*c),"oval"==a.shapeType&&("rx"==b&&(g.width=2*c),"ry"==b&&(g.height=2*c)),"roundrect"!=a.shapeType||"width"!=b&&"height"!=b||(c-=1),"cursor"==b&&(g.cursor=c),"cx"==b&&(g.left=c-AmCharts.removePx(g.width)/2),"cy"==b&&(g.top=c-AmCharts.removePx(g.height)/2);var h=this.styleMap[b];if(void 0!==h&&(g[h]=c),"text"==f&&("text-anchor"==b&&(a.anchor=c,h=e.clientWidth,"end"==c&&(g.marginLeft=-h+"px"),"middle"==c&&(g.marginLeft=-(h/2)+"px",g.textAlign="center"),"start"==c&&(g.marginLeft="0px")),"fill"==b&&(g.color=c),"font-weight"==b&&(g.fontWeight=c)),g=a.children)for(h=0;h<g.length;h++)g[h].setAttr(b,c);"shape"==f&&("cs"==b&&(e.style.width="100px",e.style.height="100px",e.setAttribute("coordsize",c)),"d"==b&&e.setAttribute("path",this.svgPathToVml(c)),"dd"==b&&e.setAttribute("path",c),f=a.stroke,a=a.fill,"stroke"==b&&(d?f.color=c:f.setAttribute("color",c)),"stroke-width"==b&&(d?f.weight=c:f.setAttribute("weight",c)),"stroke-opacity"==b&&(d?f.opacity=c:f.setAttribute("opacity",c)),"stroke-dasharray"==b&&(g="solid",c>0&&3>c&&(g="dot"),c>=3&&6>=c&&(g="dash"),c>6&&(g="longdash"),d?f.dashstyle=g:f.setAttribute("dashstyle",g)),("fill-opacity"==b||"opacity"==b)&&(0===c?d?a.on=!1:a.setAttribute("on",!1):d?a.opacity=c:a.setAttribute("opacity",c)),"fill"==b&&(d?a.color=c:a.setAttribute("color",c)),"rx"==b&&(d?e.arcSize=c+"%":e.setAttribute("arcsize",c+"%")))}},attr:function(a,b){for(var c in b)b.hasOwnProperty(c)&&this.setAttr(a,c,b[c])},text:function(a,b,c){var d=new AmCharts.AmDObject("text",this.D),e=d.node;return e.style.whiteSpace="pre",e.innerHTML=a,this.D.addToContainer(e,c),this.attr(d,b),d},getBBox:function(a){return this.getBox(a.node)},getBox:function(a){var b,c=a.offsetLeft,d=a.offsetTop,e=a.offsetWidth,f=a.offsetHeight;if(a.hasChildNodes()){var g,h,i;for(i=0;i<a.childNodes.length;i++){b=this.getBox(a.childNodes[i]);var j=b.x;isNaN(j)||(isNaN(g)?g=j:g>j&&(g=j));var k=b.y;isNaN(k)||(isNaN(h)?h=k:h>k&&(h=k)),j=b.width+j,isNaN(j)||(e=Math.max(e,j)),b=b.height+k,isNaN(b)||(f=Math.max(f,b))}0>g&&(c+=g),0>h&&(d+=h)}return{x:c,y:d,width:e,height:f}},setText:function(a,b){var c=a.node;c&&(c.innerHTML=b),this.setAttr(a,"text-anchor",a.anchor)},addListener:function(a,b,c){a.node["on"+b]=c},move:function(a,b,c){var d=a.node,e=d.style;"text"==a.type&&(c-=AmCharts.removePx(e.fontSize)/2-1),"oval"==a.shapeType&&(b-=AmCharts.removePx(e.width)/2,c-=AmCharts.removePx(e.height)/2),a=a.bw,isNaN(a)||(b-=a,c-=a),isNaN(b)||isNaN(c)||(d.style.left=b+"px",d.style.top=c+"px")},svgPathToVml:function(a){var b=a.split(" ");a="";var c,d,e=Math.round;for(d=0;d<b.length;d++){var f=b[d],g=f.substring(0,1),f=f.substring(1),h=f.split(","),i=e(h[0])+","+e(h[1]);if("M"==g&&(a+=" m "+i),"L"==g&&(a+=" l "+i),"Z"==g&&(a+=" x e"),"Q"==g){var j=c.length,k=c[j-1],l=h[0],m=h[1],i=h[2],n=h[3];c=e(c[j-2]/3+2/3*l),k=e(k/3+2/3*m),l=e(2/3*l+i/3),m=e(2/3*m+n/3),a+=" c "+c+","+k+","+l+","+m+","+i+","+n}"A"==g&&(a+=" wa "+f),"B"==g&&(a+=" at "+f),c=h}return a},animate:function(a,b,c,d,e){var f=a.node,g=this.chart;if("translate"==b){b=c.split(","),c=b[1];var h=f.offsetTop;g.animate(a,"left",f.offsetLeft,b[0],d,e,"px"),g.animate(a,"top",h,c,d,e,"px")}},clipRect:function(a,b,c,d,e){a=a.node,0===b&&0===c?(a.style.width=d+"px",a.style.height=e+"px",a.style.overflow="hidden"):a.style.clip="rect("+c+"px "+(b+d)+"px "+(c+e)+"px "+b+"px)"},rotate:function(a,b,c){if(0!==Number(b)){var d=a.node;a=d.style,c||(c=this.getBGColor(d.parentNode)),a.backgroundColor=c,a.paddingLeft=1,c=b*Math.PI/180;var e=Math.cos(c),f=Math.sin(c),g=AmCharts.removePx(a.left),h=AmCharts.removePx(a.top),i=d.offsetWidth,d=d.offsetHeight;b/=Math.abs(b),a.left=g+i/2-i/2*Math.cos(c)-b*d/2*Math.sin(c)+3,a.top=h-b*i/2*Math.sin(c)+b*d/2*Math.sin(c),a.cssText=a.cssText+"; filter:progid:DXImageTransform.Microsoft.Matrix(M11='"+e+"', M12='"+-f+"', M21='"+f+"', M22='"+e+"', sizingmethod='auto expand');"}},getBGColor:function(a){var b="#FFFFFF";if(a.style){var c=a.style.backgroundColor;""!==c?b=c:a.parentNode&&(b=this.getBGColor(a.parentNode))}return b},set:function(a){var b=new AmCharts.AmDObject("group",this.D);if(this.D.container.appendChild(b.node),a){var c;for(c=0;c<a.length;c++)b.push(a[c])}return b},gradient:function(a,b,c,d){var e="";"radialGradient"==b&&(b="gradientradial",c.reverse()),"linearGradient"==b&&(b="gradient");var f;for(f=0;f<c.length;f++){var g=Math.round(100*f/(c.length-1)),e=e+(g+"% "+c[f]);f<c.length-1&&(e+=",")}a=a.fill,90==d?d=0:270==d?d=180:180==d?d=90:0===d&&(d=270),8===document.documentMode?(a.type=b,a.angle=d):(a.setAttribute("type",b),a.setAttribute("angle",d)),e&&(a.colors.value=e)},remove:function(a){a.clipPath&&this.D.remove(a.clipPath),this.D.remove(a.node)},disableSelection:function(a){void 0!==typeof a.onselectstart&&(a.onselectstart=function(){return!1}),a.style.cursor="default"},pattern:function(a,b){var c=a.node,d=a.fill,e="none";b.color&&(e=b.color),c.fillColor=e,8===document.documentMode?(d.type="tile",d.src=b.url):(d.setAttribute("type","tile"),d.setAttribute("src",b.url))},update:function(){}}),AmCharts.SVGRenderer=AmCharts.Class({construct:function(a){this.D=a,this.animations=[]},create:function(a,b){return document.createElementNS(AmCharts.SVG_NS,b)},attr:function(a,b){for(var c in b)b.hasOwnProperty(c)&&this.setAttr(a,c,b[c])},setAttr:function(a,b,c){void 0!==c&&a.node.setAttribute(b,c)},animate:function(a,b,c,d,e){var f=a.node;a["an_"+b]&&AmCharts.removeFromArray(this.animations,a["an_"+b]),"translate"==b?(f=(f=f.getAttribute("transform"))?String(f).substring(10,f.length-1):"0,0",f=f.split(", ").join(" "),f=f.split(" ").join(","),0===f&&(f="0,0")):f=Number(f.getAttribute(b)),c={obj:a,frame:0,attribute:b,from:f,to:c,time:d,effect:e},this.animations.push(c),a["an_"+b]=c},update:function(){var a,b=this.animations;for(a=b.length-1;a>=0;a--){var c,d,e,f=b[a],g=1e3*f.time/AmCharts.updateRate,h=f.frame+1,i=f.obj,j=f.attribute;g>=h?(f.frame++,"translate"==j?(c=f.from.split(","),j=Number(c[0]),c=Number(c[1]),isNaN(c)&&(c=0),d=f.to.split(","),e=Number(d[0]),d=Number(d[1]),e=0===e-j?e:Math.round(AmCharts[f.effect](0,h,j,e-j,g)),f=0===d-c?d:Math.round(AmCharts[f.effect](0,h,c,d-c,g)),j="transform",f="translate("+e+","+f+")"):(d=Number(f.from),c=Number(f.to),e=c-d,f=AmCharts[f.effect](0,h,d,e,g),isNaN(f)&&(f=c),0===e&&this.animations.splice(a,1)),this.setAttr(i,j,f)):("translate"==j?(d=f.to.split(","),e=Number(d[0]),d=Number(d[1]),i.translate(e,d)):(c=Number(f.to),this.setAttr(i,j,c)),this.animations.splice(a,1))}},getBBox:function(a){if(a=a.node)try{return a.getBBox()}catch(b){}return{width:0,height:0,x:0,y:0}},path:function(a,b){a.node.setAttributeNS(AmCharts.SVG_XLINK,"xlink:href",b)},clipRect:function(a,b,c,d,e){var f=a.node,g=a.clipPath;g&&this.D.remove(g);var h=f.parentNode;h&&(f=document.createElementNS(AmCharts.SVG_NS,"clipPath"),g=AmCharts.getUniqueId(),f.setAttribute("id",g),this.D.rect(b,c,d,e,0,0,f),h.appendChild(f),b="#",AmCharts.baseHref&&!AmCharts.isIE&&(b=window.location.href+b),this.setAttr(a,"clip-path","url("+b+g+")"),this.clipPathC++,a.clipPath=f)},text:function(a,b,c){var d=new AmCharts.AmDObject("text",this.D);a=String(a).split("\n");var e,f=b["font-size"];for(e=0;e<a.length;e++){var g=this.create(null,"tspan");g.appendChild(document.createTextNode(a[e])),g.setAttribute("y",(f+2)*e+Math.round(f/2)),g.setAttribute("x",0),d.node.appendChild(g)}return d.node.setAttribute("y",Math.round(f/2)),this.attr(d,b),this.D.addToContainer(d.node,c),d},setText:function(a,b){var c=a.node;c&&(c.removeChild(c.firstChild),c.appendChild(document.createTextNode(b)))},move:function(a,b,c,d){b="translate("+b+","+c+")",d&&(b=b+" scale("+d+")"),this.setAttr(a,"transform",b)},rotate:function(a,b){var c=a.node.getAttribute("transform"),d="rotate("+b+")";c&&(d=c+" "+d),this.setAttr(a,"transform",d)},set:function(a){var b=new AmCharts.AmDObject("g",this.D);if(this.D.container.appendChild(b.node),a){var c;for(c=0;c<a.length;c++)b.push(a[c])}return b},addListener:function(a,b,c){a.node["on"+b]=c},gradient:function(a,b,c,d){var e=a.node,f=a.grad;if(f&&this.D.remove(f),b=document.createElementNS(AmCharts.SVG_NS,b),f=AmCharts.getUniqueId(),b.setAttribute("id",f),!isNaN(d)){var g=0,h=0,i=0,j=0;90==d?i=100:270==d?j=100:180==d?g=100:0===d&&(h=100),b.setAttribute("x1",g+"%"),b.setAttribute("x2",h+"%"),b.setAttribute("y1",i+"%"),b.setAttribute("y2",j+"%")}for(d=0;d<c.length;d++)g=document.createElementNS(AmCharts.SVG_NS,"stop"),h=100*d/(c.length-1),0===d&&(h=0),g.setAttribute("offset",h+"%"),g.setAttribute("stop-color",c[d]),b.appendChild(g);e.parentNode.appendChild(b),c="#",AmCharts.baseHref&&!AmCharts.isIE&&(c=window.location.href+c),e.setAttribute("fill","url("+c+f+")"),a.grad=b},pattern:function(a,b,c){var d=a.node;isNaN(c)&&(c=1);var e=a.patternNode;e&&this.D.remove(e);var e=document.createElementNS(AmCharts.SVG_NS,"pattern"),f=AmCharts.getUniqueId(),g=b;b.url&&(g=b.url);var h=Number(b.width);isNaN(h)&&(h=4);var i=Number(b.height);isNaN(i)&&(i=4),h/=c,i/=c,c=b.x,isNaN(c)&&(c=0);var j=-Math.random()*Number(b.randomX);isNaN(j)||(c=j),j=b.y,isNaN(j)&&(j=0);var k=-Math.random()*Number(b.randomY);isNaN(k)||(j=k),e.setAttribute("id",f),e.setAttribute("width",h),e.setAttribute("height",i),e.setAttribute("patternUnits","userSpaceOnUse"),e.setAttribute("xlink:href",g),b.color&&(k=document.createElementNS(AmCharts.SVG_NS,"rect"),k.setAttributeNS(null,"height",h),k.setAttributeNS(null,"width",i),k.setAttributeNS(null,"fill",b.color),e.appendChild(k)),this.D.image(g,0,0,h,i,e).translate(c,j),g="#",AmCharts.baseHref&&!AmCharts.isIE&&(g=window.location.href+g),d.setAttribute("fill","url("+g+f+")"),a.patternNode=e,d.parentNode.appendChild(e)},remove:function(a){a.clipPath&&this.D.remove(a.clipPath),a.grad&&this.D.remove(a.grad),a.patternNode&&this.D.remove(a.patternNode),this.D.remove(a.node)}}),AmCharts.AmDSet=AmCharts.Class({construct:function(a){this.create("g")},attr:function(a){this.R.attr(this.node,a)},move:function(a,b){this.R.move(this.node,a,b)}}),AmCharts.AmLegend=AmCharts.Class({construct:function(a){this.cname="AmLegend",this.createEvents("rollOverMarker","rollOverItem","rollOutMarker","rollOutItem","showItem","hideItem","clickMarker","rollOverItem","rollOutItem","clickLabel"),this.position="bottom",this.borderColor=this.color="#000000",this.borderAlpha=0,this.markerLabelGap=5,this.verticalGap=10,this.align="left",this.horizontalGap=0,this.spacing=10,this.markerDisabledColor="#AAB3B3",this.markerType="square",this.markerSize=16,this.markerBorderThickness=this.markerBorderAlpha=1,this.marginBottom=this.marginTop=0,this.marginLeft=this.marginRight=20,this.autoMargins=!0,this.valueWidth=50,this.switchable=!0,this.switchType="x",this.switchColor="#FFFFFF",this.rollOverColor="#CC0000",this.reversedOrder=!1,this.labelText="[[title]]",this.valueText="[[value]]",this.useMarkerColorForLabels=!1,this.rollOverGraphAlpha=1,this.textClickEnabled=!1,this.equalWidths=!0,this.dateFormat="DD-MM-YYYY",this.backgroundColor="#FFFFFF",this.backgroundAlpha=0,this.useGraphSettings=!1,this.showEntries=!0,AmCharts.applyTheme(this,a,this.cname)},setData:function(a){this.legendData=a,this.invalidateSize()},invalidateSize:function(){this.destroy(),this.entries=[],this.valueLabels=[],(AmCharts.ifArray(this.legendData)||AmCharts.ifArray(this.data))&&this.drawLegend()},drawLegend:function(){var a=this.chart,b=this.position,c=this.width,d=a.divRealWidth,e=a.divRealHeight,f=this.div,g=this.legendData;if(this.data&&(g=this.data),isNaN(this.fontSize)&&(this.fontSize=a.fontSize),"right"==b||"left"==b)this.maxColumns=1,this.autoMargins&&(this.marginLeft=this.marginRight=10);else if(this.autoMargins){this.marginRight=a.marginRight,this.marginLeft=a.marginLeft;var h=a.autoMarginOffset;"bottom"==b?(this.marginBottom=h,this.marginTop=0):(this.marginTop=h,this.marginBottom=0)}var i;if(void 0!==c?i=AmCharts.toCoordinate(c,d):"right"!=b&&"left"!=b&&(i=a.realWidth),"outside"==b?(i=f.offsetWidth,e=f.offsetHeight,f.clientHeight&&(i=f.clientWidth,e=f.clientHeight)):(isNaN(i)||(f.style.width=i+"px"),f.className="amChartsLegend"),this.divWidth=i,(b=this.container)?(b.container.innerHTML="",f.appendChild(b.container),b.setSize(i,e)):b=new AmCharts.AmDraw(f,i,e,a),this.container=b,this.lx=0,this.ly=8,e=this.markerSize,e>this.fontSize&&(this.ly=e/2-1),e>0&&(this.lx+=e+this.markerLabelGap),this.titleWidth=0,(e=this.title)&&(a=AmCharts.text(this.container,e,this.color,a.fontFamily,this.fontSize,"start",!0),a.translate(this.marginLeft,this.marginTop+this.verticalGap+this.ly+1),a=a.getBBox(),this.titleWidth=a.width+15,this.titleHeight=a.height+6),this.index=this.maxLabelWidth=0,this.showEntries){for(a=0;a<g.length;a++)this.createEntry(g[a]);for(a=this.index=0;a<g.length;a++)this.createValue(g[a])}this.arrangeEntries(),this.updateValues()},arrangeEntries:function(){var a=this.position,b=this.marginLeft+this.titleWidth,c=this.marginRight,d=this.marginTop,e=this.marginBottom,f=this.horizontalGap,g=this.div,h=this.divWidth,i=this.maxColumns,j=this.verticalGap,k=this.spacing,l=h-c-b,m=0,n=0,o=this.container;this.set&&this.set.remove();var p=o.set();this.set=p,o=o.set(),p.push(o);var q,r,s=this.entries;for(r=0;r<s.length;r++){q=s[r].getBBox();var t=q.width;t>m&&(m=t),q=q.height,q>n&&(n=q)}var t=n=0,u=f,v=0,w=0;for(r=0;r<s.length;r++){var x=s[r];this.reversedOrder&&(x=s[s.length-r-1]),q=x.getBBox();var y;this.equalWidths?y=f+t*(m+k+this.markerLabelGap):(y=u,u=u+q.width+f+k),q.height>w&&(w=q.height),y+q.width>l&&r>0&&0!==t&&(n++,t=0,y=f,u=y+q.width+f+k,v=v+w+j,w=0),x.translate(y,v),t++,!isNaN(i)&&t>=i&&(t=0,n++,v=v+w+j,w=0),o.push(x)}q=o.getBBox(),i=q.height+2*j-1,"left"==a||"right"==a?(h=q.width+2*f,g.style.width=h+b+c+"px"):h=h-b-c-1,c=AmCharts.polygon(this.container,[0,h,h,0],[0,0,i,i],this.backgroundColor,this.backgroundAlpha,1,this.borderColor,this.borderAlpha),p.push(c),p.translate(b,d),c.toBack(),b=f,("top"==a||"bottom"==a||"absolute"==a||"outside"==a)&&("center"==this.align?b=f+(h-q.width)/2:"right"==this.align&&(b=f+h-q.width)),o.translate(b,j+1),this.titleHeight>i&&(i=this.titleHeight),a=i+d+e+1,0>a&&(a=0),g.style.height=Math.round(a)+"px"},createEntry:function(a){if(!1!==a.visibleInLegend){var b=this.chart,c=a.markerType;c||(c=this.markerType);var d=a.color,e=a.alpha;a.legendKeyColor&&(d=a.legendKeyColor()),a.legendKeyAlpha&&(e=a.legendKeyAlpha());var f;!0===a.hidden&&(f=d=this.markerDisabledColor);var g=a.pattern,h=a.customMarker;h||(h=this.customMarker);var i=this.container,j=this.markerSize,k=0,l=0,m=j/2;if(this.useGraphSettings)if(k=a.type,this.switchType=void 0,"line"==k||"step"==k||"smoothedLine"==k||"ohlc"==k)g=i.set(),a.hidden||(d=a.lineColorR,f=a.bulletBorderColorR),l=AmCharts.line(i,[0,2*j],[j/2,j/2],d,a.lineAlpha,a.lineThickness,a.dashLength),g.push(l),a.bullet&&(a.hidden||(d=a.bulletColorR),l=AmCharts.bullet(i,a.bullet,a.bulletSize,d,a.bulletAlpha,a.bulletBorderThickness,f,a.bulletBorderAlpha))&&(l.translate(j+1,j/2),g.push(l)),m=0,k=j,l=j/3;else{var n;a.getGradRotation&&(n=a.getGradRotation()),k=a.fillColorsR,!0===a.hidden&&(k=d),(g=this.createMarker("rectangle",k,a.fillAlphas,a.lineThickness,d,a.lineAlpha,n,g))&&(m=j,g.translate(m,j/2)),k=j}else h?(b.path&&(h=b.path+h),g=i.image(h,0,0,j,j)):(g=this.createMarker(c,d,e,void 0,void 0,void 0,void 0,g))&&g.translate(j/2,j/2);this.addListeners(g,a),i=i.set([g]),this.switchable&&a.switchable&&i.setAttr("cursor","pointer"),(f=this.switchType)&&"none"!=f&&("x"==f?(n=this.createX(),n.translate(j/2,j/2)):n=this.createV(),n.dItem=a,!0!==a.hidden?"x"==f?n.hide():n.show():"x"!=f&&n.hide(),this.switchable||n.hide(),this.addListeners(n,a),a.legendSwitch=n,i.push(n)),f=this.color,a.showBalloon&&this.textClickEnabled&&void 0!==this.selectedColor&&(f=this.selectedColor),this.useMarkerColorForLabels&&(f=d),!0===a.hidden&&(f=this.markerDisabledColor),d=AmCharts.massReplace(this.labelText,{"[[title]]":a.title}),n=this.fontSize,g&&n>=j&&g.translate(m,j/2+this.ly-n/2+(n+2-j)/2-l);var o;d&&(d=AmCharts.fixBrakes(d),a.legendTextReal=d,o=this.labelWidth,o=isNaN(o)?AmCharts.text(this.container,d,f,b.fontFamily,n,"start"):AmCharts.wrappedText(this.container,d,f,b.fontFamily,n,"start",!1,o,0),o.translate(this.lx+k,this.ly),i.push(o),b=o.getBBox().width,this.maxLabelWidth<b&&(this.maxLabelWidth=b)),this.entries[this.index]=i,a.legendEntry=this.entries[this.index],a.legendLabel=o,this.index++}},addListeners:function(a,b){var c=this;a&&a.mouseover(function(a){c.rollOverMarker(b,a)}).mouseout(function(a){c.rollOutMarker(b,a)}).click(function(a){c.clickMarker(b,a)})},rollOverMarker:function(a,b){this.switchable&&this.dispatch("rollOverMarker",a,b),this.dispatch("rollOverItem",a,b)},rollOutMarker:function(a,b){this.switchable&&this.dispatch("rollOutMarker",a,b),this.dispatch("rollOutItem",a,b)},clickMarker:function(a,b){this.switchable&&(!0===a.hidden?this.dispatch("showItem",a,b):this.dispatch("hideItem",a,b)),this.dispatch("clickMarker",a,b)},rollOverLabel:function(a,b){a.hidden||(this.textClickEnabled&&a.legendLabel&&a.legendLabel.attr({fill:this.rollOverColor}),this.dispatch("rollOverItem",a,b))},rollOutLabel:function(a,b){if(!a.hidden){if(this.textClickEnabled&&a.legendLabel){var c=this.color;void 0!==this.selectedColor&&a.showBalloon&&(c=this.selectedColor),this.useMarkerColorForLabels&&(c=a.lineColor,void 0===c&&(c=a.color)),a.legendLabel.attr({fill:c})}this.dispatch("rollOutItem",a,b)}},clickLabel:function(a,b){this.textClickEnabled?a.hidden||this.dispatch("clickLabel",a,b):this.switchable&&(!0===a.hidden?this.dispatch("showItem",a,b):this.dispatch("hideItem",a,b))},dispatch:function(a,b,c){this.fire(a,{type:a,dataItem:b,target:this,event:c,chart:this.chart})},createValue:function(a){var b=this,c=b.fontSize;if(!1!==a.visibleInLegend){var d=b.maxLabelWidth;b.equalWidths||(b.valueAlign="left"),"left"==b.valueAlign&&(d=a.legendEntry.getBBox().width);var e=d;if(b.valueText&&0<b.valueWidth){var f=b.color;b.useMarkerColorForValues&&(f=a.color,a.legendKeyColor&&(f=a.legendKeyColor())),!0===a.hidden&&(f=b.markerDisabledColor);var g=b.valueText,d=d+b.lx+b.markerLabelGap+b.valueWidth,h="end";"left"==b.valueAlign&&(d-=b.valueWidth,h="start"),f=AmCharts.text(b.container,g,f,b.chart.fontFamily,c,h),f.translate(d,b.ly),b.entries[b.index].push(f),e+=b.valueWidth+2*b.markerLabelGap,f.dItem=a,b.valueLabels.push(f)}b.index++,f=b.markerSize,c+7>f&&(f=c+7,AmCharts.VML&&(f+=3)),c=b.container.rect(b.markerSize,0,e,f,0,0).attr({stroke:"none",fill:"#ffffff","fill-opacity":.005}),c.dItem=a,b.entries[b.index-1].push(c),c.mouseover(function(c){b.rollOverLabel(a,c)}).mouseout(function(c){b.rollOutLabel(a,c)}).click(function(c){b.clickLabel(a,c)})}},createV:function(){var a=this.markerSize;return AmCharts.polygon(this.container,[a/5,a/2,a-a/5,a/2],[a/3,a-a/5,a/5,a/1.7],this.switchColor)},createX:function(){var a=(this.markerSize-4)/2,b={stroke:this.switchColor,"stroke-width":3},c=this.container,d=AmCharts.line(c,[-a,a],[-a,a]).attr(b),a=AmCharts.line(c,[-a,a],[a,-a]).attr(b);return this.container.set([d,a])},createMarker:function(a,b,c,d,e,f,g,h){var i=this.markerSize,j=this.container;return e||(e=this.markerBorderColor),e||(e=b),isNaN(d)&&(d=this.markerBorderThickness),isNaN(f)&&(f=this.markerBorderAlpha),AmCharts.bullet(j,a,i,b,c,d,e,f,i,g,h)},validateNow:function(){this.invalidateSize()},updateValues:function(){var a,b=this.valueLabels,c=this.chart,d=this.data;for(a=0;a<b.length;a++){var e=b[a],f=e.dItem,g=" ";if(d)e.text(f.value?f.value:"");else{if(void 0!==f.type){var h=f.currentDataItem,i=this.periodValueText;f.legendPeriodValueText&&(i=f.legendPeriodValueText),h?(g=this.valueText,f.legendValueText&&(g=f.legendValueText),g=c.formatString(g,h)):i&&(g=c.formatPeriodString(i,f))}else g=c.formatString(this.valueText,f);(i=this.valueFunction)&&(h&&(f=h),g=i(f,g)),e.text(g)}}},renderFix:function(){if(!AmCharts.VML){var a=this.container;a&&a.renderFix()}},destroy:function(){this.div.innerHTML="",AmCharts.remove(this.set)}}),AmCharts.formatMilliseconds=function(a,b){if(-1!=a.indexOf("fff")){var c=b.getMilliseconds(),d=String(c);10>c&&(d="00"+c),c>=10&&100>c&&(d="0"+c),a=a.replace(/fff/g,d)}return a},AmCharts.extractPeriod=function(a){var b=AmCharts.stripNumbers(a),c=1;return b!=a&&(c=Number(a.slice(0,a.indexOf(b)))),{period:b,count:c}},AmCharts.newDate=function(a,b){return date="fff"==b?AmCharts.useUTC?new Date(a.getUTCFullYear(),a.getUTCMonth(),a.getUTCDate(),a.getUTCHours(),a.getUTCMinutes(),a.getUTCSeconds(),a.getUTCMilliseconds()):new Date(a.getFullYear(),a.getMonth(),a.getDate(),a.getHours(),a.getMinutes(),a.getSeconds(),a.getMilliseconds()):new Date(a)},AmCharts.resetDateToMin=function(a,b,c,d){void 0===d&&(d=1);var e,f,g,h,i,j,k;switch(AmCharts.useUTC?(e=a.getUTCFullYear(),f=a.getUTCMonth(),g=a.getUTCDate(),h=a.getUTCHours(),i=a.getUTCMinutes(),j=a.getUTCSeconds(),k=a.getUTCMilliseconds(),a=a.getUTCDay()):(e=a.getFullYear(),f=a.getMonth(),g=a.getDate(),h=a.getHours(),i=a.getMinutes(),j=a.getSeconds(),k=a.getMilliseconds(),a=a.getDay()),b){case"YYYY":e=Math.floor(e/c)*c,f=0,g=1,k=j=i=h=0;break;case"MM":f=Math.floor(f/c)*c,g=1,k=j=i=h=0;break;case"WW":0===a&&d>0&&(a=7),g=g-a+d,k=j=i=h=0;break;case"DD":k=j=i=h=0;break;case"hh":h=Math.floor(h/c)*c,k=j=i=0;break;case"mm":i=Math.floor(i/c)*c,k=j=0;break;case"ss":j=Math.floor(j/c)*c,k=0;break;case"fff":k=Math.floor(k/c)*c}return AmCharts.useUTC?(a=new Date,a.setUTCFullYear(e,f,g),a.setUTCHours(h,i,j,k)):a=new Date(e,f,g,h,i,j,k),a},AmCharts.getPeriodDuration=function(a,b){void 0===b&&(b=1);var c;switch(a){case"YYYY":c=316224e5;break;case"MM":c=26784e5;break;case"WW":c=6048e5;break;case"DD":c=864e5;break;case"hh":c=36e5;break;case"mm":c=6e4;break;case"ss":c=1e3;break;case"fff":c=1}return c*b},AmCharts.intervals={s:{nextInterval:"ss",contains:1e3},ss:{nextInterval:"mm",contains:60,count:0},mm:{nextInterval:"hh",contains:60,count:1},hh:{nextInterval:"DD",contains:24,count:2},DD:{nextInterval:"",contains:1/0,count:3}},AmCharts.getMaxInterval=function(a,b){var c=AmCharts.intervals;return a>=c[b].contains?(a=Math.round(a/c[b].contains),b=c[b].nextInterval,AmCharts.getMaxInterval(a,b)):"ss"==b?c[b].nextInterval:b},AmCharts.dayNames="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),AmCharts.shortDayNames="Sun Mon Tue Wed Thu Fri Sat".split(" "),AmCharts.monthNames="January February March April May June July August September October November December".split(" "),AmCharts.shortMonthNames="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),AmCharts.getWeekNumber=function(a){a=new Date(a),a.setHours(0,0,0),a.setDate(a.getDate()+4-(a.getDay()||7));var b=new Date(a.getFullYear(),0,1);return Math.ceil(((a-b)/864e5+1)/7)},AmCharts.stringToDate=function(a,b){var c={},d=[{pattern:"YYYY",period:"year"},{pattern:"YY",period:"year"},{pattern:"MM",period:"month"},{pattern:"M",period:"month"},{pattern:"DD",period:"date"},{pattern:"D",period:"date"},{pattern:"JJ",period:"hours"},{pattern:"J",period:"hours"},{pattern:"HH",period:"hours"},{pattern:"H",period:"hours"},{pattern:"KK",period:"hours"},{pattern:"K",period:"hours"},{pattern:"LL",period:"hours"},{pattern:"L",period:"hours"},{pattern:"NN",period:"minutes"},{pattern:"N",period:"minutes"},{pattern:"SS",period:"seconds"},{pattern:"S",period:"seconds"},{pattern:"QQQ",period:"milliseconds"},{pattern:"QQ",period:"milliseconds"},{pattern:"Q",period:"milliseconds"}],e=!0,f=b.indexOf("AA");-1!=f&&(a.substr(f,2),"pm"==a.toLowerCase&&(e=!1));var g,h,i,f=b;for(i=0;i<d.length;i++)h=d[i].period,c[h]=0,"date"==h&&(c[h]=1);for(i=0;i<d.length;i++)if(g=d[i].pattern,h=d[i].period,-1!=b.indexOf(g)){var j=AmCharts.getFromDateString(g,a,f);b=b.replace(g,""),("KK"==g||"K"==g||"LL"==g||"L"==g)&&(e||(j+=12)),c[h]=j}return AmCharts.useUTC?(d=new Date,d.setUTCFullYear(c.year,c.month,c.date),d.setUTCHours(c.hours,c.minutes,c.seconds,c.milliseconds)):d=new Date(c.year,c.month,c.date,c.hours,c.minutes,c.seconds,c.milliseconds),d},AmCharts.getFromDateString=function(a,b,c){return void 0!==b?(c=c.indexOf(a),b=b.substr(c,a.length),"0"==b.charAt(0)&&(b=b.substr(1,b.length-1)),b=Number(b),isNaN(b)&&(b=0),-1!=a.indexOf("M")&&b--,b):void 0},AmCharts.formatDate=function(a,b,c){c||(c=AmCharts);var d,e,f,g,h,i,j,k=AmCharts.getWeekNumber(a);AmCharts.useUTC?(d=a.getUTCFullYear(),e=a.getUTCMonth(),f=a.getUTCDate(),g=a.getUTCDay(),h=a.getUTCHours(),
i=a.getUTCMinutes(),j=a.getUTCSeconds(),a=a.getUTCMilliseconds()):(d=a.getFullYear(),e=a.getMonth(),f=a.getDate(),g=a.getDay(),h=a.getHours(),i=a.getMinutes(),j=a.getSeconds(),a=a.getMilliseconds());var l=String(d).substr(2,2),m=e+1;9>e&&(m="0"+m);var n="0"+g;b=b.replace(/W/g,k),k=h,24==k&&(k=0);var o=k;10>o&&(o="0"+o),b=b.replace(/JJ/g,o),b=b.replace(/J/g,k),o=h,0===o&&(o=24,-1!=b.indexOf("H")&&f--),k=f,10>f&&(k="0"+f);var p=o;return 10>p&&(p="0"+p),b=b.replace(/HH/g,p),b=b.replace(/H/g,o),o=h,o>11&&(o-=12),p=o,10>p&&(p="0"+p),b=b.replace(/KK/g,p),b=b.replace(/K/g,o),o=h,0===o&&(o=12),o>12&&(o-=12),p=o,10>p&&(p="0"+p),b=b.replace(/LL/g,p),b=b.replace(/L/g,o),o=i,10>o&&(o="0"+o),b=b.replace(/NN/g,o),b=b.replace(/N/g,i),i=j,10>i&&(i="0"+i),b=b.replace(/SS/g,i),b=b.replace(/S/g,j),j=a,10>j&&(j="00"+j),100>j&&(j="0"+j),i=a,10>i&&(i="00"+i),b=b.replace(/QQQ/g,j),b=b.replace(/QQ/g,i),b=b.replace(/Q/g,a),b=12>h?b.replace(/A/g,"am"):b.replace(/A/g,"pm"),b=b.replace(/YYYY/g,"@IIII@"),b=b.replace(/YY/g,"@II@"),b=b.replace(/MMMM/g,"@XXXX@"),b=b.replace(/MMM/g,"@XXX@"),b=b.replace(/MM/g,"@XX@"),b=b.replace(/M/g,"@X@"),b=b.replace(/DD/g,"@RR@"),b=b.replace(/D/g,"@R@"),b=b.replace(/EEEE/g,"@PPPP@"),b=b.replace(/EEE/g,"@PPP@"),b=b.replace(/EE/g,"@PP@"),b=b.replace(/E/g,"@P@"),b=b.replace(/@IIII@/g,d),b=b.replace(/@II@/g,l),b=b.replace(/@XXXX@/g,c.monthNames[e]),b=b.replace(/@XXX@/g,c.shortMonthNames[e]),b=b.replace(/@XX@/g,m),b=b.replace(/@X@/g,e+1),b=b.replace(/@RR@/g,k),b=b.replace(/@R@/g,f),b=b.replace(/@PPPP@/g,c.dayNames[g]),b=b.replace(/@PPP@/g,c.shortDayNames[g]),b=b.replace(/@PP@/g,n),b=b.replace(/@P@/g,g)},AmCharts.changeDate=function(a,b,c,d,e){var f=-1;switch(void 0===d&&(d=!0),void 0===e&&(e=!1),!0===d&&(f=1),b){case"YYYY":a.setFullYear(a.getFullYear()+c*f),d||e||a.setDate(a.getDate()+1);break;case"MM":b=a.getMonth(),a.setMonth(a.getMonth()+c*f),a.getMonth()>b+c*f&&a.setDate(a.getDate()-1),d||e||a.setDate(a.getDate()+1);break;case"DD":a.setDate(a.getDate()+c*f);break;case"WW":a.setDate(a.getDate()+c*f*7);break;case"hh":a.setHours(a.getHours()+c*f);break;case"mm":a.setMinutes(a.getMinutes()+c*f);break;case"ss":a.setSeconds(a.getSeconds()+c*f);break;case"fff":a.setMilliseconds(a.getMilliseconds()+c*f)}return a},AmCharts.AmFunnelChart=AmCharts.Class({inherits:AmCharts.AmSlicedChart,construct:function(a){this.type="funnel",AmCharts.AmFunnelChart.base.construct.call(this,a),this.cname="AmFunnelChart",this.startX=this.startY=0,this.baseWidth="100%",this.neckHeight=this.neckWidth=0,this.rotate=!1,this.valueRepresents="height",this.pullDistance=30,this.labelPosition="center",this.labelText="[[title]]: [[value]]",this.balloonText="[[title]]: [[value]]\n[[description]]",AmCharts.applyTheme(this,a,this.cname)},drawChart:function(){AmCharts.AmFunnelChart.base.drawChart.call(this);var a=this.chartData;if(AmCharts.ifArray(a))if(0<this.realWidth&&0<this.realHeight){var b=Math.round(this.depth3D*Math.cos(this.angle*Math.PI/180)),c=Math.round(-this.depth3D*Math.sin(this.angle*Math.PI/180)),d=this.container,e=this.startDuration,f=this.rotate,g=this.updateWidth();this.realWidth=g;var h=this.updateHeight();this.realHeight=h;var i=AmCharts.toCoordinate,j=i(this.marginLeft,g),k=i(this.marginRight,g),l=i(this.marginTop,h)+this.getTitleHeight(),i=i(this.marginBottom,h);b>0&&0>c&&(this.neckHeight=this.neckWidth=0,f?i-=c/2:l-=c/2);var k=g-j-k,m=AmCharts.toCoordinate(this.baseWidth,k),n=AmCharts.toCoordinate(this.neckWidth,k),o=h-i-l,p=AmCharts.toCoordinate(this.neckHeight,o),q=l+o-p;f&&(l=h-i,q=l-o+p),this.firstSliceY=l,AmCharts.VML&&(this.startAlpha=1);for(var r=k/2+j,s=(o-p)/((m-n)/2),t=1,u=m/2,m=(o-p)*(m+n)/2+n*p,v=l,w=0,p=0;p<a.length;p++){var x,y=a[p];if(!0!==y.hidden){var z,A=[],B=[];if("height"==this.valueRepresents)z=o*y.percents/100;else{var C=-m*y.percents/100/2,D=u;x=-1/(2*s),z=Math.pow(D,2)-4*x*C,0>z&&(z=0),z=(Math.sqrt(z)-D)/(2*x),!f&&l>=q||f&&q>=l?z=2*-C/n:(!f&&l+z>q||f&&q>l-z)&&(x=Math.round(f?z+(l-z-q):z-(l+z-q)),z=x/s,z=x+2*(-C-(D-z/2)*x)/n)}if(C=u-z/s,D=!1,!f&&l+z>q||f&&q>l-z?(C=n/2,A.push(r-u,r+u,r+C,r+C,r-C,r-C),f?(x=z+(l-z-q),q>l&&(x=0),B.push(l,l,l-x,l-z,l-z,l-x,l)):(x=z-(l+z-q),l>q&&(x=0),B.push(l,l,l+x,l+z,l+z,l+x,l)),D=!0):(A.push(r-u,r+u,r+C,r-C),f?B.push(l,l,l-z,l-z):B.push(l,l,l+z,l+z)),d.set(),x=d.set(),b>0&&0>c?(A=C/u,B=-1,f||(B=1),wedgeGraphics=new AmCharts.Cuboid(d,2*u,B*z,b,c*t,y.color,y.alpha,this.outlineThickness,this.outlineColor,this.outlineAlpha,90,0,!1,0,y.pattern,A).set,wedgeGraphics.translate(r-u,l-c/2*t),t*=A):wedgeGraphics=AmCharts.polygon(d,A,B,y.color,y.alpha,this.outlineThickness,this.outlineColor,this.outlineAlpha),x.push(wedgeGraphics),this.graphsSet.push(x),f||x.toBack(),y.wedge=x,y.index=p,A=this.gradientRatio){var E,B=[];for(E=0;E<A.length;E++)B.push(AmCharts.adjustLuminosity(y.color,A[E]));0<B.length&&wedgeGraphics.gradient("linearGradient",B),y.pattern&&wedgeGraphics.pattern(y.pattern)}if(e>0&&(this.chartCreated||x.setAttr("opacity",this.startAlpha)),this.addEventListeners(x,y),y.ty0=l-z/2,this.labelsEnabled&&this.labelText&&y.percents>=this.hideLabelsPercent){B=this.formatString(this.labelText,y),(A=this.labelFunction)&&(B=A(y,B)),E=y.labelColor,E||(E=this.color);var A=this.labelPosition,F="left";"center"==A&&(F="middle"),"left"==A&&(F="right"),B=AmCharts.wrappedText(d,B,E,this.fontFamily,this.fontSize,F,!1,this.maxLabelWidth),B.node.style.pointerEvents="none",x.push(B),E=r,f?(F=l-z/2,y.ty0=F):(F=l+z/2,y.ty0=F,v+w+5>F&&(F=v+w+5),F>h-i&&(F=h-i)),"right"==A&&(E=k+10+j,y.tx0=r+(u-z/2/s),D&&(y.tx0=r+C)),"left"==A&&(y.tx0=r-(u-z/2/s),D&&(y.tx0=r-C),E=j),y.label=B,y.labelX=E,y.labelY=F,y.labelHeight=B.getBBox().height,B.translate(E,F),u=B.getBBox(),v=AmCharts.rect(d,u.width+5,u.height+5,"#ffffff",.005),v.translate(E+u.x,F+u.y),x.push(v),y.hitRect=v,w=B.getBBox().height,v=F}(0===y.alpha||e>0&&!this.chartCreated)&&x.hide(),l=f?l-z:l+z,u=C,y.startX=AmCharts.toCoordinate(this.startX,g),y.startY=AmCharts.toCoordinate(this.startY,h),y.pullX=AmCharts.toCoordinate(this.pullDistance,g),y.pullY=0,y.balloonX=r,y.balloonY=y.ty0}}this.arrangeLabels(),this.initialStart(),(a=this.legend)&&a.invalidateSize()}else this.cleanChart();this.dispDUpd(),this.chartCreated=!0},arrangeLabels:function(){var a,b=this.rotate;a=b?0:this.realHeight;for(var c,d=0,e=this.chartData,f=e.length,g=0;f>g;g++){c=e[f-g-1];var h=c.label,i=c.labelY,j=c.labelX,k=c.labelHeight,l=i;if(b?a+d+5>i&&(l=a+d+5):i+k+5>a&&(l=a-5-k),a=l,d=k,h){h.translate(j,l);var m=h.getBBox()}c.hitRect.translate(j+m.x,l+m.y),c.labelY=l,c.tx=j,c.ty=l,c.tx2=j}"center"!=this.labelPosition&&this.drawTicks()}}),AmCharts.Cuboid=AmCharts.Class({construct:function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p){this.set=a.set(),this.container=a,this.h=Math.round(c),this.w=Math.round(b),this.dx=d,this.dy=e,this.colors=f,this.alpha=g,this.bwidth=h,this.bcolor=i,this.balpha=j,this.dashLength=n,this.topRadius=p,this.pattern=o,(this.rotate=m)?0>b&&0===k&&(k=180):0>c&&270==k&&(k=90),this.gradientRotation=k,0===d&&0===e&&(this.cornerRadius=l),this.draw()},draw:function(){var a=this.set;a.clear();var b=this.container,c=this.w,d=this.h,e=this.dx,f=this.dy,g=this.colors,h=this.alpha,i=this.bwidth,j=this.bcolor,k=this.balpha,l=this.gradientRotation,m=this.cornerRadius,n=this.dashLength,o=this.pattern,p=this.topRadius,q=g,r=g;"object"==typeof g&&(q=g[0],r=g[g.length-1]);var s,t,u,v,w,x,y,z,A,B=h;o&&(h=0);var C,D,E,F,G=this.rotate;if(0<Math.abs(e)||0<Math.abs(f))if(isNaN(p))y=r,r=AmCharts.adjustLuminosity(q,-.2),r=AmCharts.adjustLuminosity(q,-.2),s=AmCharts.polygon(b,[0,e,c+e,c,0],[0,f,f,0,0],r,h,1,j,0,l),k>0&&(A=AmCharts.line(b,[0,e,c+e],[0,f,f],j,k,i,n)),t=AmCharts.polygon(b,[0,0,c,c,0],[0,d,d,0,0],r,h,1,j,0,l),t.translate(e,f),k>0&&(u=AmCharts.line(b,[e,e],[f,f+d],j,k,i,n)),v=AmCharts.polygon(b,[0,0,e,e,0],[0,d,d+f,f,0],r,h,1,j,0,l),w=AmCharts.polygon(b,[c,c,c+e,c+e,c],[0,d,d+f,f,0],r,h,1,j,0,l),k>0&&(x=AmCharts.line(b,[c,c+e,c+e,c],[0,f,d+f,d],j,k,i,n)),r=AmCharts.adjustLuminosity(y,.2),y=AmCharts.polygon(b,[0,e,c+e,c,0],[d,d+f,d+f,d,d],r,h,1,j,0,l),k>0&&(z=AmCharts.line(b,[0,e,c+e],[d,d+f,d+f],j,k,i,n));else{var H,I,J;G?(H=d/2,r=e/2,J=d/2,I=c+e/2,D=Math.abs(d/2),C=Math.abs(e/2)):(r=c/2,H=f/2,I=c/2,J=d+f/2+1,C=Math.abs(c/2),D=Math.abs(f/2)),E=C*p,F=D*p,C>.1&&C>.1&&(s=AmCharts.circle(b,C,q,h,i,j,k,!1,D),s.translate(r,H)),E>.1&&E>.1&&(y=AmCharts.circle(b,E,AmCharts.adjustLuminosity(q,.5),h,i,j,k,!1,F),y.translate(I,J))}for(h=B,1>Math.abs(d)&&(d=0),1>Math.abs(c)&&(c=0),!isNaN(p)&&(0<Math.abs(e)||0<Math.abs(f))?(g=[q],g={fill:g,stroke:j,"stroke-width":i,"stroke-opacity":k,"fill-opacity":h},G?(h="M0,0 L"+c+","+(d/2-d/2*p),i=" B",c>0&&(i=" A"),AmCharts.VML?(h+=i+Math.round(c-E)+","+Math.round(d/2-F)+","+Math.round(c+E)+","+Math.round(d/2+F)+","+c+",0,"+c+","+d,h=h+(" L0,"+d)+(i+Math.round(-C)+","+Math.round(d/2-D)+","+Math.round(C)+","+Math.round(d/2+D)+",0,"+d+",0,0")):(h+="A"+E+","+F+",0,0,0,"+c+","+(d-d/2*(1-p))+"L0,"+d,h+="A"+C+","+D+",0,0,1,0,0"),C=90):(i=c/2-c/2*p,h="M0,0 L"+i+","+d,AmCharts.VML?(h="M0,0 L"+i+","+d,i=" B",0>d&&(i=" A"),h+=i+Math.round(c/2-E)+","+Math.round(d-F)+","+Math.round(c/2+E)+","+Math.round(d+F)+",0,"+d+","+c+","+d,h+=" L"+c+",0",h+=i+Math.round(c/2+C)+","+Math.round(D)+","+Math.round(c/2-C)+","+Math.round(-D)+","+c+",0,0,0"):(h+="A"+E+","+F+",0,0,0,"+(c-c/2*(1-p))+","+d+"L"+c+",0",h+="A"+C+","+D+",0,0,1,0,0"),C=180),b=b.path(h).attr(g),b.gradient("linearGradient",[q,AmCharts.adjustLuminosity(q,-.3),AmCharts.adjustLuminosity(q,-.3),q],C),G?b.translate(e/2,0):b.translate(0,f/2)):b=0===d?AmCharts.line(b,[0,c],[0,0],j,k,i,n):0===c?AmCharts.line(b,[0,0],[0,d],j,k,i,n):m>0?AmCharts.rect(b,c,d,g,h,i,j,k,m,l,n):AmCharts.polygon(b,[0,0,c,c,0],[0,d,d,0,0],g,h,i,j,k,l,!1,n),c=isNaN(p)?0>d?[s,A,t,u,v,w,x,y,z,b]:[y,z,t,u,v,w,s,A,x,b]:G?c>0?[s,b,y]:[y,b,s]:0>d?[s,b,y]:[y,b,s],d=0;d<c.length;d++)(e=c[d])&&a.push(e);o&&b.pattern(o)},width:function(a){this.w=Math.round(a),this.draw()},height:function(a){this.h=Math.round(a),this.draw()},animateHeight:function(a,b){var c=this;c.easing=b,c.totalFrames=Math.round(1e3*a/AmCharts.updateRate),c.rh=c.h,c.frame=0,c.height(1),setTimeout(function(){c.updateHeight.call(c)},AmCharts.updateRate)},updateHeight:function(){var a=this;a.frame++;var b=a.totalFrames;a.frame<=b&&(b=a.easing(0,a.frame,1,a.rh-1,b),a.height(b),setTimeout(function(){a.updateHeight.call(a)},AmCharts.updateRate))},animateWidth:function(a,b){var c=this;c.easing=b,c.totalFrames=Math.round(1e3*a/AmCharts.updateRate),c.rw=c.w,c.frame=0,c.width(1),setTimeout(function(){c.updateWidth.call(c)},AmCharts.updateRate)},updateWidth:function(){var a=this;a.frame++;var b=a.totalFrames;a.frame<=b&&(b=a.easing(0,a.frame,1,a.rw-1,b),a.width(b),setTimeout(function(){a.updateWidth.call(a)},AmCharts.updateRate))}}),AmCharts.GaugeAxis=AmCharts.Class({construct:function(a){this.cname="GaugeAxis",this.radius="95%",this.startAngle=-120,this.endAngle=120,this.startValue=0,this.endValue=200,this.gridCount=5,this.tickLength=10,this.minorTickLength=5,this.tickColor="#555555",this.labelFrequency=this.tickThickness=this.tickAlpha=1,this.inside=!0,this.labelOffset=10,this.showLastLabel=this.showFirstLabel=!0,this.axisThickness=1,this.axisColor="#000000",this.axisAlpha=1,this.gridInside=!0,this.topTextYOffset=0,this.topTextBold=!0,this.bottomTextYOffset=0,this.bottomTextBold=!0,this.centerY=this.centerX="0%",this.bandOutlineAlpha=this.bandOutlineThickness=0,this.bandOutlineColor="#000000",this.bandAlpha=1,AmCharts.applyTheme(this,a,"GaugeAxis")},value2angle:function(a){return(a-this.startValue)/(this.endValue-this.startValue)*(this.endAngle-this.startAngle)+this.startAngle},setTopText:function(a){if(void 0!==a){this.topText=a;var b=this.chart;if(this.axisCreated){this.topTF&&this.topTF.remove();var c=this.topTextFontSize;c||(c=b.fontSize);var d=this.topTextColor;d||(d=b.color),a=AmCharts.text(b.container,a,d,b.fontFamily,c,void 0,this.topTextBold),a.translate(this.centerXReal,this.centerYReal-this.radiusReal/2+this.topTextYOffset),this.chart.graphsSet.push(a),this.topTF=a}}},setBottomText:function(a){if(void 0!==a){this.bottomText=a;var b=this.chart;if(this.axisCreated){this.bottomTF&&this.bottomTF.remove();var c=this.bottomTextFontSize;c||(c=b.fontSize);var d=this.bottomTextColor;d||(d=b.color),a=AmCharts.text(b.container,a,d,b.fontFamily,c,void 0,this.bottomTextBold),a.translate(this.centerXReal,this.centerYReal+this.radiusReal/2+this.bottomTextYOffset),this.bottomTF=a,this.chart.graphsSet.push(a)}}},draw:function(){var a=this.chart,b=a.graphsSet,c=this.startValue,d=this.endValue,e=this.valueInterval;isNaN(e)&&(e=(d-c)/this.gridCount);var f=this.minorTickInterval;isNaN(f)&&(f=e/5);var g=this.startAngle,h=this.endAngle,i=this.tickLength,j=(d-c)/e+1,k=(h-g)/(j-1);this.singleValueAngle=d=k/e;var l=a.container,m=this.tickColor,n=this.tickAlpha,o=this.tickThickness,p=e/f,q=k/p,f=this.minorTickLength,r=this.labelFrequency,s=this.radiusReal;this.inside||(s-=15);var t=a.centerX+AmCharts.toCoordinate(this.centerX,a.realWidth),u=a.centerY+AmCharts.toCoordinate(this.centerY,a.realHeight);this.centerXReal=t,this.centerYReal=u;var v,w,x={fill:this.axisColor,"fill-opacity":this.axisAlpha,"stroke-width":0,"stroke-opacity":0};this.gridInside?w=v=s:(v=s-i,w=v+f);var y=this.axisThickness/2,h=AmCharts.wedge(l,t,u,g,h-g,v+y,v+y,v-y,0,x);for(b.push(h),h=AmCharts.doNothing,AmCharts.isModern||(h=Math.round),x=AmCharts.getDecimals(e),v=0;j>v;v++){var z=c+v*e,y=g+v*k,A=h(t+s*Math.sin(y/180*Math.PI)),B=h(u-s*Math.cos(y/180*Math.PI)),C=h(t+(s-i)*Math.sin(y/180*Math.PI)),D=h(u-(s-i)*Math.cos(y/180*Math.PI)),A=AmCharts.line(l,[A,C],[B,D],m,n,o,0,!1,!1,!0);b.push(A),A=-1,C=this.labelOffset,this.inside||(C=-C-i,A=1);var B=Math.sin(y/180*Math.PI),D=Math.cos(y/180*Math.PI),B=t+(s-i-C)*B,C=u-(s-i-C)*D,E=this.fontSize;isNaN(E)&&(E=a.fontSize);var D=Math.sin((y-90)/180*Math.PI),F=Math.cos((y-90)/180*Math.PI);if(r>0&&v/r==Math.round(v/r)&&(this.showLastLabel||v!=j-1)&&(this.showFirstLabel||0!==v)){var G;G=this.usePrefixes?AmCharts.addPrefix(z,a.prefixesOfBigNumbers,a.prefixesOfSmallNumbers,a.nf,!0):AmCharts.formatNumber(z,a.nf,x);var H=this.unit;H&&(G="left"==this.unitPosition?H+G:G+H),(H=this.labelFunction)&&(G=H(z)),z=AmCharts.text(l,G,a.color,a.fontFamily,E),E=z.getBBox(),z.translate(B+A*E.width/2*F,C+A*E.height/2*D),b.push(z)}if(j-1>v)for(z=1;p>z;z++)D=y+q*z,A=h(t+w*Math.sin(D/180*Math.PI)),B=h(u-w*Math.cos(D/180*Math.PI)),C=h(t+(w-f)*Math.sin(D/180*Math.PI)),D=h(u-(w-f)*Math.cos(D/180*Math.PI)),A=AmCharts.line(l,[A,C],[B,D],m,n,o,0,!1,!1,!0),b.push(A)}if(b=this.bands)for(c=0;c<b.length;c++)(e=b[c])&&(m=e.startValue,n=e.endValue,i=AmCharts.toCoordinate(e.radius,s),isNaN(i)&&(i=w),j=AmCharts.toCoordinate(e.innerRadius,s),isNaN(j)&&(j=i-f),k=g+d*(m-this.startValue),n=d*(n-m),o=e.outlineColor,void 0==o&&(o=this.bandOutlineColor),p=e.outlineThickness,isNaN(p)&&(p=this.bandOutlineThickness),q=e.outlineAlpha,isNaN(q)&&(q=this.bandOutlineAlpha),m=e.alpha,isNaN(m)&&(m=this.bandAlpha),i=AmCharts.wedge(l,t,u,k,n,i,i,j,0,{fill:e.color,stroke:o,"stroke-width":p,"stroke-opacity":q}),i.setAttr("opacity",m),a.gridSet.push(i),this.addEventListeners(i,e));this.axisCreated=!0,this.setTopText(this.topText),this.setBottomText(this.bottomText),a=a.graphsSet.getBBox(),this.width=a.width,this.height=a.height},addEventListeners:function(a,b){var c=this.chart;a.mouseover(function(a){c.showBalloon(b.balloonText,b.color,!0)}).mouseout(function(a){c.hideBalloon()})}}),AmCharts.GaugeArrow=AmCharts.Class({construct:function(a){this.cname="GaugeArrow",this.color="#000000",this.nailAlpha=this.alpha=1,this.startWidth=this.nailRadius=8,this.endWidth=0,this.borderAlpha=1,this.radius="90%",this.nailBorderAlpha=this.innerRadius=0,this.nailBorderThickness=1,this.frame=0,AmCharts.applyTheme(this,a,"GaugeArrow")},setValue:function(a){var b=this.chart;b&&b.setValue?b.setValue(this,a):this.previousValue=this.value=a}}),AmCharts.GaugeBand=AmCharts.Class({construct:function(){this.cname="GaugeBand"}}),AmCharts.AmAngularGauge=AmCharts.Class({inherits:AmCharts.AmChart,construct:function(a){this.cname="AmAngularGauge",AmCharts.AmAngularGauge.base.construct.call(this,a),this.theme=a,this.type="gauge",this.minRadius=this.marginRight=this.marginBottom=this.marginTop=this.marginLeft=10,this.faceColor="#FAFAFA",this.faceAlpha=0,this.faceBorderWidth=1,this.faceBorderColor="#555555",this.faceBorderAlpha=0,this.arrows=[],this.axes=[],this.startDuration=1,this.startEffect="easeOutSine",this.adjustSize=!0,this.extraHeight=this.extraWidth=0,AmCharts.applyTheme(this,a,this.cname)},addAxis:function(a){this.axes.push(a)},formatString:function(a,b){return a=AmCharts.formatValue(a,b,["value"],this.nf,"",this.usePrefixes,this.prefixesOfSmallNumbers,this.prefixesOfBigNumbers)},initChart:function(){AmCharts.AmAngularGauge.base.initChart.call(this);var a;0===this.axes.length&&(a=new AmCharts.GaugeAxis(this.theme),this.addAxis(a));var b;for(b=0;b<this.axes.length;b++)a=this.axes[b],a=AmCharts.processObject(a,AmCharts.GaugeAxis,this.theme),a.chart=this,this.axes[b]=a;var c=this.arrows;for(b=0;b<c.length;b++){a=c[b],a=AmCharts.processObject(a,AmCharts.GaugeArrow,this.theme),a.chart=this,c[b]=a;var d=a.axis;AmCharts.isString(d)&&(a.axis=AmCharts.getObjById(this.axes,d)),a.axis||(a.axis=this.axes[0]),isNaN(a.value)&&a.setValue(a.axis.startValue),isNaN(a.previousValue)&&(a.previousValue=a.axis.startValue)}this.setLegendData(c),this.drawChart(),this.totalFrames=1e3*this.startDuration/AmCharts.updateRate},drawChart:function(){AmCharts.AmAngularGauge.base.drawChart.call(this);var a=this.container,b=this.updateWidth();this.realWidth=b;var c=this.updateHeight();this.realHeight=c;var d=AmCharts.toCoordinate,e=d(this.marginLeft,b),f=d(this.marginRight,b),g=d(this.marginTop,c)+this.getTitleHeight(),h=d(this.marginBottom,c),i=d(this.radius,b,c),d=b-e-f,j=c-g-h+this.extraHeight;i||(i=Math.min(d,j)/2),i<this.minRadius&&(i=this.minRadius),this.radiusReal=i,this.centerX=(b-e-f)/2+e,this.centerY=(c-g-h)/2+g+this.extraHeight/2,isNaN(this.gaugeX)||(this.centerX=this.gaugeX),isNaN(this.gaugeY)||(this.centerY=this.gaugeY);var k,b=this.faceAlpha,c=this.faceBorderAlpha;for((b>0||c>0)&&(k=AmCharts.circle(a,i,this.faceColor,b,this.faceBorderWidth,this.faceBorderColor,c,!1),k.translate(this.centerX,this.centerY),k.toBack(),(a=this.facePattern)&&k.pattern(a)),b=i=a=0;b<this.axes.length;b++)c=this.axes[b],e=c.radius,c.radiusReal=AmCharts.toCoordinate(e,this.radiusReal),c.draw(),f=1,-1!==String(e).indexOf("%")&&(f=1+(100-Number(e.substr(0,e.length-1)))/100),c.width*f>a&&(a=c.width*f),c.height*f>i&&(i=c.height*f);(b=this.legend)&&b.invalidateSize(),this.adjustSize&&!this.chartCreated&&(k&&(k=k.getBBox(),k.width>a&&(a=k.width),k.height>i&&(i=k.height)),k=0,(j>i||d>a)&&(k=Math.min(j-i,d-a)),k>0&&(this.extraHeight=j-i,this.chartCreated=!0,this.validateNow())),this.dispDUpd(),this.chartCreated=!0},validateSize:function(){this.extraHeight=this.extraWidth=0,this.chartCreated=!1,AmCharts.AmAngularGauge.base.validateSize.call(this)},addArrow:function(a){this.arrows.push(a)},removeArrow:function(a){AmCharts.removeFromArray(this.arrows,a),this.validateNow()},removeAxis:function(a){AmCharts.removeFromArray(this.axes,a),this.validateNow()},drawArrow:function(a,b){a.set&&a.set.remove();var c=this.container;if(a.set=c.set(),!a.hidden){var d=a.axis,e=d.radiusReal,f=d.centerXReal,g=d.centerYReal,h=a.startWidth,i=a.endWidth,j=AmCharts.toCoordinate(a.innerRadius,d.radiusReal),k=AmCharts.toCoordinate(a.radius,d.radiusReal);d.inside||(k-=15);var l=a.nailColor;l||(l=a.color);var m=a.nailColor;m||(m=a.color),l=AmCharts.circle(c,a.nailRadius,l,a.nailAlpha,a.nailBorderThickness,l,a.nailBorderAlpha),a.set.push(l),l.translate(f,g),isNaN(k)&&(k=e-d.tickLength);var d=Math.sin(b/180*Math.PI),e=Math.cos(b/180*Math.PI),l=Math.sin((b+90)/180*Math.PI),n=Math.cos((b+90)/180*Math.PI),c=AmCharts.polygon(c,[f-h/2*l+j*d,f+k*d-i/2*l,f+k*d+i/2*l,f+h/2*l+j*d],[g+h/2*n-j*e,g-k*e+i/2*n,g-k*e-i/2*n,g-h/2*n-j*e],a.color,a.alpha,1,m,a.borderAlpha,void 0,!0);a.set.push(c),this.graphsSet.push(a.set)}},setValue:function(a,b){a.axis&&a.axis.value2angle&&(a.axis.value2angle(b),a.frame=0,a.previousValue=a.value),a.value=b;var c=this.legend;c&&c.updateValues()},handleLegendEvent:function(a){var b=a.type;if(a=a.dataItem,!this.legend.data&&a)switch(b){case"hideItem":this.hideArrow(a);break;case"showItem":this.showArrow(a)}},hideArrow:function(a){a.set.hide(),a.hidden=!0},showArrow:function(a){a.set.show(),a.hidden=!1},updateAnimations:function(){AmCharts.AmAngularGauge.base.updateAnimations.call(this);for(var a,b=this.arrows.length,c=0;b>c;c++){a=this.arrows[c];var d;a.frame>=this.totalFrames?d=a.value:(a.frame++,a.clockWiseOnly&&a.value<a.previousValue&&(d=a.axis,a.previousValue-=d.endValue-d.startValue),d=AmCharts.getEffect(this.startEffect),d=AmCharts[d](0,a.frame,a.previousValue,a.value-a.previousValue,this.totalFrames),isNaN(d)&&(d=a.value)),d=a.axis.value2angle(d),this.drawArrow(a,d)}}}),AmCharts.AmPieChart=AmCharts.Class({inherits:AmCharts.AmSlicedChart,construct:function(a){this.type="pie",AmCharts.AmPieChart.base.construct.call(this,a),this.cname="AmPieChart",this.pieBrightnessStep=30,this.minRadius=10,this.depth3D=0,this.startAngle=90,this.angle=this.innerRadius=0,this.startRadius="500%",this.pullOutRadius="20%",this.labelRadius=20,this.labelText="[[title]]: [[percents]]%",this.balloonText="[[title]]: [[percents]]% ([[value]])\n[[description]]",this.previousScale=1,AmCharts.applyTheme(this,a,this.cname)},drawChart:function(){AmCharts.AmPieChart.base.drawChart.call(this);var a=this.chartData;if(AmCharts.ifArray(a)){if(0<this.realWidth&&0<this.realHeight){AmCharts.VML&&(this.startAlpha=1);var b=this.startDuration,c=this.container,d=this.updateWidth();this.realWidth=d;var e=this.updateHeight();this.realHeight=e;var f,g,h,i=AmCharts.toCoordinate,j=i(this.marginLeft,d),k=i(this.marginRight,d),l=i(this.marginTop,e)+this.getTitleHeight(),m=i(this.marginBottom,e),n=AmCharts.toNumber(this.labelRadius),o=this.measureMaxLabel();for(o>this.maxLabelWidth&&(o=this.maxLabelWidth),this.labelText&&this.labelsEnabled||(n=o=0),f=void 0===this.pieX?(d-j-k)/2+j:i(this.pieX,this.realWidth),g=void 0===this.pieY?(e-l-m)/2+l:i(this.pieY,e),h=i(this.radius,d,e),h||(d=n>=0?d-j-k-2*o:d-j-k,e=e-l-m,h=Math.min(d,e),d>e&&(h/=1-this.angle/90,h>d&&(h=d)),e=AmCharts.toCoordinate(this.pullOutRadius,h),h=(n>=0?h-1.8*(n+e):h-1.8*e)/2),h<this.minRadius&&(h=this.minRadius),e=i(this.pullOutRadius,h),l=AmCharts.toCoordinate(this.startRadius,h),i=i(this.innerRadius,h),i>=h&&(i=h-1),m=AmCharts.fitToBounds(this.startAngle,0,360),0<this.depth3D&&(m=m>=270?270:90),m-=90,d=h-h*this.angle/90,j=0;j<a.length;j++)if(k=a[j],!0!==k.hidden&&0<k.percents){var p=360*k.percents/100,o=Math.sin((m+p/2)/180*Math.PI),q=-Math.cos((m+p/2)/180*Math.PI)*(d/h),r=this.outlineColor;r||(r=k.color);var s=this.alpha;if(isNaN(k.alpha)||(s=k.alpha),r={fill:k.color,stroke:r,"stroke-width":this.outlineThickness,"stroke-opacity":this.outlineAlpha,"fill-opacity":s},k.url&&(r.cursor="pointer"),r=AmCharts.wedge(c,f,g,m,p,h,d,i,this.depth3D,r,this.gradientRatio,k.pattern),this.addEventListeners(r,k),k.startAngle=m,a[j].wedge=r,b>0&&(this.chartCreated||r.setAttr("opacity",this.startAlpha)),k.ix=o,k.iy=q,k.wedge=r,k.index=j,this.labelsEnabled&&this.labelText&&k.percents>=this.hideLabelsPercent){var t=m+p/2;t>360&&(t-=360);var u=n;isNaN(k.labelRadius)||(u=k.labelRadius);var v,p=f+o*(h+u),s=g+q*(h+u),w=0;if(u>=0){var x;90>=t&&t>=0?(x=0,v="start",w=8):t>=90&&180>t?(x=1,v="start",w=8):t>=180&&270>t?(x=2,v="end",w=-8):t>=270&&360>t&&(x=3,v="end",w=-8),k.labelQuarter=x}else v="middle";var t=this.formatString(this.labelText,k),y=this.labelFunction;y&&(t=y(k,t)),y=k.labelColor,y||(y=this.color),t=AmCharts.wrappedText(c,t,y,this.fontFamily,this.fontSize,v,!1,this.maxLabelWidth),t.translate(p+1.5*w,s),t.node.style.pointerEvents="none",k.tx=p+1.5*w,k.ty=s,u>=0?(u=t.getBBox(),y=AmCharts.rect(c,u.width+5,u.height+5,"#FFFFFF",.005),y.translate(p+1.5*w+u.x,s+u.y),k.hitRect=y,r.push(t),r.push(y)):this.freeLabelsSet.push(t),k.label=t,k.tx=p,k.tx2=p+w,k.tx0=f+o*h,k.ty0=g+q*h}p=i+(h-i)/2,k.pulled&&(p+=this.pullOutRadiusReal),k.balloonX=o*p+f,k.balloonY=q*p+g,k.startX=Math.round(o*l),k.startY=Math.round(q*l),k.pullX=Math.round(o*e),k.pullY=Math.round(q*e),this.graphsSet.push(r),(0===k.alpha||b>0&&!this.chartCreated)&&r.hide(),m+=360*k.percents/100}n>0&&!this.labelRadiusField&&this.arrangeLabels(),this.pieXReal=f,this.pieYReal=g,this.radiusReal=h,this.innerRadiusReal=i,n>0&&this.drawTicks(),this.initialStart(),this.setDepths()}(a=this.legend)&&a.invalidateSize()}else this.cleanChart();this.dispDUpd(),this.chartCreated=!0},setDepths:function(){var a,b=this.chartData;for(a=0;a<b.length;a++){var c=b[a],d=c.wedge,c=c.startAngle;c>=0&&180>c?d.toFront():c>=180&&d.toBack()}},arrangeLabels:function(){var a,b,c=this.chartData,d=c.length;for(b=d-1;b>=0;b--)a=c[b],0!==a.labelQuarter||a.hidden||this.checkOverlapping(b,a,0,!0,0);for(b=0;d>b;b++)a=c[b],1!=a.labelQuarter||a.hidden||this.checkOverlapping(b,a,1,!1,0);for(b=d-1;b>=0;b--)a=c[b],2!=a.labelQuarter||a.hidden||this.checkOverlapping(b,a,2,!0,0);for(b=0;d>b;b++)a=c[b],3!=a.labelQuarter||a.hidden||this.checkOverlapping(b,a,3,!1,0)},checkOverlapping:function(a,b,c,d,e){var f,g,h=this.chartData,i=h.length,j=b.label;if(j){if(!0===d)for(g=a+1;i>g;g++)h[g].labelQuarter==c&&(f=this.checkOverlappingReal(b,h[g],c))&&(g=i);else for(g=a-1;g>=0;g--)h[g].labelQuarter==c&&(f=this.checkOverlappingReal(b,h[g],c))&&(g=0);!0===f&&100>e&&(f=b.ty+3*b.iy,b.ty=f,j.translate(b.tx2,f),b.hitRect&&(j=j.getBBox(),b.hitRect.translate(b.tx2+j.x,f+j.y)),this.checkOverlapping(a,b,c,d,e+1))}},checkOverlappingReal:function(a,b,c){var d=!1,e=a.label,f=b.label;return a.labelQuarter!=c||a.hidden||b.hidden||!f||(e=e.getBBox(),c={},c.width=e.width,c.height=e.height,c.y=a.ty,c.x=a.tx,a=f.getBBox(),f={},f.width=a.width,f.height=a.height,f.y=b.ty,f.x=b.tx,AmCharts.hitTest(c,f)&&(d=!0)),d}}),AmCharts.AmRadarChart=AmCharts.Class({inherits:AmCharts.AmCoordinateChart,construct:function(a){this.type="radar",AmCharts.AmRadarChart.base.construct.call(this,a),this.cname="AmRadarChart",this.marginRight=this.marginBottom=this.marginTop=this.marginLeft=0,this.radius="35%",AmCharts.applyTheme(this,a,this.cname)},initChart:function(){AmCharts.AmRadarChart.base.initChart.call(this),this.dataChanged&&(this.updateData(),this.dataChanged=!1,this.dispatchDataUpdated=!0),this.drawChart()},updateData:function(){this.parseData();var a,b=this.graphs;for(a=0;a<b.length;a++)b[a].data=this.chartData},updateGraphs:function(){var a,b=this.graphs;for(a=0;a<b.length;a++){var c=b[a];c.index=a,c.width=this.realRadius,c.height=this.realRadius,c.x=this.marginLeftReal,c.y=this.marginTopReal}},parseData:function(){AmCharts.AmRadarChart.base.parseData.call(this),this.parseSerialData()},updateValueAxes:function(){var a,b=this.valueAxes;for(a=0;a<b.length;a++){var c=b[a];c.axisRenderer=AmCharts.RadAxis,c.guideFillRenderer=AmCharts.RadarFill,c.axisItemRenderer=AmCharts.RadItem,c.autoGridCount=!1,c.x=this.marginLeftReal,c.y=this.marginTopReal,c.width=this.realRadius,c.height=this.realRadius}},drawChart:function(){AmCharts.AmRadarChart.base.drawChart.call(this);var a=this.updateWidth(),b=this.updateHeight(),c=this.marginTop+this.getTitleHeight(),d=this.marginLeft,b=b-c-this.marginBottom;if(this.marginLeftReal=d+(a-d-this.marginRight)/2,this.marginTopReal=c+b/2,this.realRadius=AmCharts.toCoordinate(this.radius,a,b),this.updateValueAxes(),this.updateGraphs(),a=this.chartData,AmCharts.ifArray(a)){if(0<this.realWidth&&0<this.realHeight){for(a=a.length-1,d=this.valueAxes,c=0;c<d.length;c++)d[c].zoom(0,a);for(d=this.graphs,c=0;c<d.length;c++)d[c].zoom(0,a);(a=this.legend)&&a.invalidateSize()}}else this.cleanChart();this.dispDUpd(),this.chartCreated=!0},formatString:function(a,b,c){var d=b.graph;return-1!=a.indexOf("[[category]]")&&(a=a.replace(/\[\[category\]\]/g,String(b.serialDataItem.category))),d=d.numberFormatter,d||(d=this.nf),a=AmCharts.formatValue(a,b.values,["value"],d,"",this.usePrefixes,this.prefixesOfSmallNumbers,this.prefixesOfBigNumbers),-1!=a.indexOf("[[")&&(a=AmCharts.formatDataContextValue(a,b.dataContext)),a=AmCharts.AmRadarChart.base.formatString.call(this,a,b,c)},cleanChart:function(){AmCharts.callMethod("destroy",[this.valueAxes,this.graphs])}}),AmCharts.RadAxis=AmCharts.Class({construct:function(a){var b=a.chart,c=a.axisThickness,d=a.axisColor,e=a.axisAlpha,f=a.x,g=a.y;this.set=b.container.set(),b.axesSet.push(this.set);var h=a.axisTitleOffset,i=a.radarCategoriesEnabled,j=a.chart.fontFamily,k=a.fontSize;void 0===k&&(k=a.chart.fontSize);var l=a.color;if(void 0===l&&(l=a.chart.color),b){this.axisWidth=a.height,a=b.chartData;var m,n=a.length;for(m=0;n>m;m++){var o=180-360/n*m,p=f+this.axisWidth*Math.sin(o/180*Math.PI),q=g+this.axisWidth*Math.cos(o/180*Math.PI);if(e>0&&(p=AmCharts.line(b.container,[f,p],[g,q],d,e,c),this.set.push(p)),i){var r="start",p=f+(this.axisWidth+h)*Math.sin(o/180*Math.PI),q=g+(this.axisWidth+h)*Math.cos(o/180*Math.PI);(180==o||0===o)&&(r="middle",p-=5),0>o&&(r="end",p-=10),180==o&&(q-=5),0===o&&(q+=5),o=AmCharts.text(b.container,a[m].category,l,j,k,r),o.translate(p+5,q),this.set.push(o),o.getBBox()}}}}}),AmCharts.RadItem=AmCharts.Class({construct:function(a,b,c,d,e,f,g,h){void 0===c&&(c="");var i=a.chart.fontFamily,j=a.fontSize;void 0===j&&(j=a.chart.fontSize);var k=a.color;void 0===k&&(k=a.chart.color);var l=a.chart.container;this.set=d=l.set();var m=a.axisColor,n=a.axisAlpha,o=a.tickLength,p=a.gridAlpha,q=a.gridThickness,r=a.gridColor,s=a.dashLength,t=a.fillColor,u=a.fillAlpha,v=a.labelsEnabled;e=a.counter;var w,x=a.inside,y=a.gridType,z=a.labelOffset;b-=a.height;var A;f=a.x;var B=a.y;g?(v=!0,isNaN(g.tickLength)||(o=g.tickLength),void 0!=g.lineColor&&(r=g.lineColor),isNaN(g.lineAlpha)||(p=g.lineAlpha),isNaN(g.dashLength)||(s=g.dashLength),isNaN(g.lineThickness)||(q=g.lineThickness),!0===g.inside&&(x=!0),void 0!==g.boldLabel&&(h=g.boldLabel)):c||(p/=3,o/=2);var C="end",D=-1;x&&(C="start",D=1);var E;if(v&&(E=AmCharts.text(l,c,k,i,j,C,h),E.translate(f+(o+3+z)*D,b),d.push(E),this.label=E,A=AmCharts.line(l,[f,f+o*D],[b,b],m,n,q),d.push(A)),b=Math.round(a.y-b),h=[],i=[],p>0){if("polygons"==y){for(w=a.data.length,j=0;w>j;j++)k=180-360/w*j,h.push(b*Math.sin(k/180*Math.PI)),i.push(b*Math.cos(k/180*Math.PI));h.push(h[0]),i.push(i[0]),p=AmCharts.line(l,h,i,r,p,q,s)}else p=AmCharts.circle(l,b,"#FFFFFF",0,q,r,p);p.translate(f,B),d.push(p)}if(1==e&&u>0&&!g&&""!==c){if(g=a.previousCoord,"polygons"==y){for(j=w;j>=0;j--)k=180-360/w*j,h.push(g*Math.sin(k/180*Math.PI)),i.push(g*Math.cos(k/180*Math.PI));w=AmCharts.polygon(l,h,i,t,u)}else w=AmCharts.wedge(l,0,0,0,360,b,b,g,0,{fill:t,"fill-opacity":u,stroke:"#000","stroke-opacity":0,"stroke-width":1});d.push(w),w.translate(f,B)}!1===a.visible&&(A&&A.hide(),E&&E.hide()),""!==c&&(a.counter=0===e?1:0,a.previousCoord=b)},graphics:function(){return this.set},getLabel:function(){return this.label}}),AmCharts.RadarFill=AmCharts.Class({construct:function(a,b,c,d){b-=a.axisWidth,c-=a.axisWidth;var e=Math.max(b,c);b=c=Math.min(b,c),c=a.chart.container;var f=d.fillAlpha,g=d.fillColor,e=Math.abs(e-a.y);b=Math.abs(b-a.y);var h=Math.max(e,b);if(b=Math.min(e,b),e=h,h=d.angle+90,d=d.toAngle+90,isNaN(h)&&(h=0),isNaN(d)&&(d=360),this.set=c.set(),void 0===g&&(g="#000000"),isNaN(f)&&(f=0),"polygons"==a.gridType){d=[];var i,j=[],k=a.data.length;for(i=0;k>i;i++)h=180-360/k*i,d.push(e*Math.sin(h/180*Math.PI)),j.push(e*Math.cos(h/180*Math.PI));for(d.push(d[0]),j.push(j[0]),i=k;i>=0;i--)h=180-360/k*i,d.push(b*Math.sin(h/180*Math.PI)),j.push(b*Math.cos(h/180*Math.PI));this.fill=AmCharts.polygon(c,d,j,g,f)}else this.fill=AmCharts.wedge(c,0,0,h,d-h,e,e,b,0,{fill:g,"fill-opacity":f,stroke:"#000","stroke-opacity":0,"stroke-width":1});this.set.push(this.fill),this.fill.translate(a.x,a.y)},graphics:function(){return this.set},getLabel:function(){}}),AmCharts.AmSerialChart=AmCharts.Class({inherits:AmCharts.AmRectangularChart,construct:function(a){this.type="serial",
AmCharts.AmSerialChart.base.construct.call(this,a),this.cname="AmSerialChart",this.theme=a,this.createEvents("changed"),this.columnSpacing=5,this.columnSpacing3D=0,this.columnWidth=.8,this.updateScrollbar=!0;var b=new AmCharts.CategoryAxis(a);b.chart=this,this.categoryAxis=b,this.zoomOutOnDataUpdate=!0,this.mouseWheelZoomEnabled=this.mouseWheelScrollEnabled=this.rotate=this.skipZoom=!1,this.minSelectedTime=0,AmCharts.applyTheme(this,a,this.cname)},initChart:function(){AmCharts.AmSerialChart.base.initChart.call(this),this.updateCategoryAxis(this.categoryAxis,this.rotate,"categoryAxis"),this.dataChanged&&(this.updateData(),this.dataChanged=!1,this.dispatchDataUpdated=!0);var a=this.chartCursor;a&&(a.updateData(),a.fullWidth&&(a.fullRectSet=this.cursorLineSet));var b,a=this.countColumns(),c=this.graphs;for(b=0;b<c.length;b++)c[b].columnCount=a;this.updateScrollbar=!0,this.drawChart(),this.autoMargins&&!this.marginsUpdated&&(this.marginsUpdated=!0,this.measureMargins()),(this.mouseWheelScrollEnabled||this.mouseWheelZoomEnabled)&&this.addMouseWheel()},handleWheelReal:function(a,b){if(!this.wheelBusy){var c=this.categoryAxis,d=c.parseDates,e=c.minDuration(),f=c=1;this.mouseWheelZoomEnabled?b||(c=-1):b&&(c=-1);var g=this.chartData.length,h=this.lastTime,i=this.firstTime;0>a?d?(g=this.endTime-this.startTime,d=this.startTime+c*e,e=this.endTime+f*e,f>0&&c>0&&e>=h&&(e=h,d=h-g),this.zoomToDates(new Date(d),new Date(e))):(f>0&&c>0&&this.end>=g-1&&(c=f=0),d=this.start+c,e=this.end+f,this.zoomToIndexes(d,e)):d?(g=this.endTime-this.startTime,d=this.startTime-c*e,e=this.endTime-f*e,f>0&&c>0&&i>=d&&(d=i,e=i+g),this.zoomToDates(new Date(d),new Date(e))):(f>0&&c>0&&1>this.start&&(c=f=0),d=this.start-c,e=this.end-f,this.zoomToIndexes(d,e))}},validateData:function(a){this.marginsUpdated=!1,this.zoomOutOnDataUpdate&&!a&&(this.endTime=this.end=this.startTime=this.start=0/0),AmCharts.AmSerialChart.base.validateData.call(this)},drawChart:function(){AmCharts.AmSerialChart.base.drawChart.call(this);var a=this.chartData;if(AmCharts.ifArray(a)){var b=this.chartScrollbar;if(b&&b.draw(),0<this.realWidth&&0<this.realHeight){var c,a=a.length-1,b=this.categoryAxis;b.parseDates&&!b.equalSpacing?(b=this.startTime,c=this.endTime,(isNaN(b)||isNaN(c))&&(b=this.firstTime,c=this.lastTime)):(b=this.start,c=this.end,(isNaN(b)||isNaN(c))&&(b=0,c=a)),this.endTime=this.startTime=this.end=this.start=void 0,this.zoom(b,c)}}else this.cleanChart();this.dispDUpd(),this.chartCreated=!0},cleanChart:function(){AmCharts.callMethod("destroy",[this.valueAxes,this.graphs,this.categoryAxis,this.chartScrollbar,this.chartCursor])},updateCategoryAxis:function(a,b,c){a.chart=this,a.id=c,a.rotate=b,a.axisRenderer=AmCharts.RecAxis,a.guideFillRenderer=AmCharts.RecFill,a.axisItemRenderer=AmCharts.RecItem,a.setOrientation(!this.rotate),a.x=this.marginLeftReal,a.y=this.marginTopReal,a.dx=this.dx,a.dy=this.dy,a.width=this.plotAreaWidth-1,a.height=this.plotAreaHeight-1,a.viW=this.plotAreaWidth-1,a.viH=this.plotAreaHeight-1,a.viX=this.marginLeftReal,a.viY=this.marginTopReal,a.marginsChanged=!0},updateValueAxes:function(){AmCharts.AmSerialChart.base.updateValueAxes.call(this);var a,b=this.valueAxes;for(a=0;a<b.length;a++){var c=b[a],d=this.rotate;c.rotate=d,c.setOrientation(d),d=this.categoryAxis,(!d.startOnAxis||d.parseDates)&&(c.expandMinMax=!0)}},updateData:function(){this.parseData();var a,b=this.graphs,c=this.chartData;for(a=0;a<b.length;a++)b[a].data=c;0<c.length&&(this.firstTime=this.getStartTime(c[0].time),this.lastTime=this.getEndTime(c[c.length-1].time))},getStartTime:function(a){var b=this.categoryAxis;return AmCharts.resetDateToMin(new Date(a),b.minPeriod,1,b.firstDayOfWeek).getTime()},getEndTime:function(a){var b=AmCharts.extractPeriod(this.categoryAxis.minPeriod);return AmCharts.changeDate(new Date(a),b.period,b.count,!0).getTime()-1},updateMargins:function(){AmCharts.AmSerialChart.base.updateMargins.call(this);var a=this.chartScrollbar;a&&(this.getScrollbarPosition(a,this.rotate,this.categoryAxis.position),this.adjustMargins(a,this.rotate))},updateScrollbars:function(){AmCharts.AmSerialChart.base.updateScrollbars.call(this),this.updateChartScrollbar(this.chartScrollbar,this.rotate)},zoom:function(a,b){var c=this.categoryAxis;c.parseDates&&!c.equalSpacing?this.timeZoom(a,b):this.indexZoom(a,b),this.updateLegendValues()},timeZoom:function(a,b){var c=this.maxSelectedTime;isNaN(c)||(b!=this.endTime&&b-a>c&&(a=b-c,this.updateScrollbar=!0),a!=this.startTime&&b-a>c&&(b=a+c,this.updateScrollbar=!0));var d=this.minSelectedTime;if(d>0&&d>b-a){var e=Math.round(a+(b-a)/2),d=Math.round(d/2);a=e-d,b=e+d}var f=this.chartData,e=this.categoryAxis;if(AmCharts.ifArray(f)&&(a!=this.startTime||b!=this.endTime)){var g=e.minDuration(),d=this.firstTime,h=this.lastTime;a||(a=d,isNaN(c)||(a=h-c)),b||(b=h),a>h&&(a=h),d>b&&(b=d),d>a&&(a=d),b>h&&(b=h),a>b&&(b=a+g),g/5>b-a&&(h>b?b=a+g/5:a=b-g/5),this.startTime=a,this.endTime=b,c=f.length-1,g=this.getClosestIndex(f,"time",a,!0,0,c),f=this.getClosestIndex(f,"time",b,!1,g,c),e.timeZoom(a,b),e.zoom(g,f),this.start=AmCharts.fitToBounds(g,0,c),this.end=AmCharts.fitToBounds(f,0,c),this.zoomAxesAndGraphs(),this.zoomScrollbar(),this.showZB(a!=d||b!=h?!0:!1),this.updateColumnsDepth(),this.dispatchTimeZoomEvent()}},indexZoom:function(a,b){var c=this.maxSelectedSeries;if(isNaN(c)||(b!=this.end&&b-a>c&&(a=b-c,this.updateScrollbar=!0),a!=this.start&&b-a>c&&(b=a+c,this.updateScrollbar=!0)),a!=this.start||b!=this.end){var d=this.chartData.length-1;isNaN(a)&&(a=0,isNaN(c)||(a=d-c)),isNaN(b)&&(b=d),a>b&&(b=a),b>d&&(b=d),a>d&&(a=d-1),0>a&&(a=0),this.start=a,this.end=b,this.categoryAxis.zoom(a,b),this.zoomAxesAndGraphs(),this.zoomScrollbar(),this.showZB(0!==a||b!=this.chartData.length-1?!0:!1),this.updateColumnsDepth(),this.dispatchIndexZoomEvent()}},updateGraphs:function(){AmCharts.AmSerialChart.base.updateGraphs.call(this);var a,b=this.graphs;for(a=0;a<b.length;a++){var c=b[a];c.columnWidthReal=this.columnWidth,c.categoryAxis=this.categoryAxis,AmCharts.isString(c.fillToGraph)&&(c.fillToGraph=this.getGraphById(c.fillToGraph))}},updateColumnsDepth:function(){var a,b,c=this.graphs;for(AmCharts.remove(this.columnsSet),this.columnsArray=[],a=0;a<c.length;a++){b=c[a];var d=b.columnsArray;if(d){var e;for(e=0;e<d.length;e++)this.columnsArray.push(d[e])}}if(this.columnsArray.sort(this.compareDepth),0<this.columnsArray.length){for(c=this.container.set(),this.columnSet.push(c),a=0;a<this.columnsArray.length;a++)c.push(this.columnsArray[a].column.set);b&&c.translate(b.x,b.y),this.columnsSet=c}},compareDepth:function(a,b){return a.depth>b.depth?1:-1},zoomScrollbar:function(){var a=this.chartScrollbar,b=this.categoryAxis;a&&this.updateScrollbar&&(b.parseDates&&!b.equalSpacing?a.timeZoom(this.startTime,this.endTime):a.zoom(this.start,this.end),this.updateScrollbar=!0)},updateTrendLines:function(){var a,b=this.trendLines;for(a=0;a<b.length;a++){var c=b[a],c=AmCharts.processObject(c,AmCharts.TrendLine,this.theme);b[a]=c,c.chart=this,AmCharts.isString(c.valueAxis)&&(c.valueAxis=this.getValueAxisById(c.valueAxis)),c.valueAxis||(c.valueAxis=this.valueAxes[0]),c.categoryAxis=this.categoryAxis}},zoomAxesAndGraphs:function(){if(!this.scrollbarOnly){var a,b=this.valueAxes;for(a=0;a<b.length;a++)b[a].zoom(this.start,this.end);for(b=this.graphs,a=0;a<b.length;a++)b[a].zoom(this.start,this.end);this.zoomTrendLines(),(a=this.chartCursor)&&a.zoom(this.start,this.end,this.startTime,this.endTime)}},countColumns:function(){var a,b,c,d,e=0,f=this.valueAxes.length,g=this.graphs.length,h=!1;for(d=0;f>d;d++){b=this.valueAxes[d];var i=b.stackType;if("100%"==i||"regular"==i)for(h=!1,c=0;g>c;c++)a=this.graphs[c],a.tcc=1,a.valueAxis==b&&"column"==a.type&&(!h&&a.stackable&&(e++,h=!0),(!a.stackable&&a.clustered||a.newStack)&&e++,a.columnIndex=e-1,a.clustered||(a.columnIndex=0));if("none"==i||"3d"==i){for(h=!1,c=0;g>c;c++)a=this.graphs[c],a.valueAxis==b&&"column"==a.type&&(a.clustered?(a.tcc=1,a.newStack&&(e=0),a.hidden||(a.columnIndex=e,e++)):a.hidden||(h=!0,a.tcc=1,a.columnIndex=0));h&&0==e&&(e=1)}if("3d"==i){for(b=1,d=0;g>d;d++)a=this.graphs[d],a.newStack&&b++,a.depthCount=b,a.tcc=e;e=b}}return e},parseData:function(){AmCharts.AmSerialChart.base.parseData.call(this),this.parseSerialData()},getCategoryIndexByValue:function(a){var b,c,d=this.chartData;for(c=0;c<d.length;c++)d[c].category==a&&(b=c);return b},handleCursorChange:function(a){this.updateLegendValues(a.index)},handleCursorZoom:function(a){this.updateScrollbar=!0,this.zoom(a.start,a.end)},handleScrollbarZoom:function(a){this.updateScrollbar=!1,this.zoom(a.start,a.end)},dispatchTimeZoomEvent:function(){if(this.prevStartTime!=this.startTime||this.prevEndTime!=this.endTime){var a={type:"zoomed"};a.startDate=new Date(this.startTime),a.endDate=new Date(this.endTime),a.startIndex=this.start,a.endIndex=this.end,this.startIndex=this.start,this.endIndex=this.end,this.startDate=a.startDate,this.endDate=a.endDate,this.prevStartTime=this.startTime,this.prevEndTime=this.endTime;var b=this.categoryAxis,c=AmCharts.extractPeriod(b.minPeriod).period,b=b.dateFormatsObject[c];a.startValue=AmCharts.formatDate(a.startDate,b,this),a.endValue=AmCharts.formatDate(a.endDate,b,this),a.chart=this,a.target=this,this.fire(a.type,a)}},dispatchIndexZoomEvent:function(){if(this.prevStartIndex!=this.start||this.prevEndIndex!=this.end){this.startIndex=this.start,this.endIndex=this.end;var a=this.chartData;if(AmCharts.ifArray(a)&&!isNaN(this.start)&&!isNaN(this.end)){var b={chart:this,target:this,type:"zoomed"};b.startIndex=this.start,b.endIndex=this.end,b.startValue=a[this.start].category,b.endValue=a[this.end].category,this.categoryAxis.parseDates&&(this.startTime=a[this.start].time,this.endTime=a[this.end].time,b.startDate=new Date(this.startTime),b.endDate=new Date(this.endTime)),this.prevStartIndex=this.start,this.prevEndIndex=this.end,this.fire(b.type,b)}}},updateLegendValues:function(a){var b,c=this.graphs;for(b=0;b<c.length;b++){var d=c[b];isNaN(a)?d.currentDataItem=void 0:d.currentDataItem=this.chartData[a].axes[d.valueAxis.id].graphs[d.id]}this.legend&&this.legend.updateValues()},getClosestIndex:function(a,b,c,d,e,f){0>e&&(e=0),f>a.length-1&&(f=a.length-1);var g=e+Math.round((f-e)/2),h=a[g][b];return 1>=f-e?d?e:(d=a[f][b],Math.abs(a[e][b]-c)<Math.abs(d-c)?e:f):c==h?g:h>c?this.getClosestIndex(a,b,c,d,e,g):this.getClosestIndex(a,b,c,d,g,f)},zoomToIndexes:function(a,b){this.updateScrollbar=!0;var c=this.chartData;if(c){var d=c.length;d>0&&(0>a&&(a=0),b>d-1&&(b=d-1),d=this.categoryAxis,d.parseDates&&!d.equalSpacing?this.zoom(c[a].time,this.getEndTime(c[b].time)):this.zoom(a,b))}},zoomToDates:function(a,b){this.updateScrollbar=!0;var c=this.chartData;if(this.categoryAxis.equalSpacing){var d=this.getClosestIndex(c,"time",a.getTime(),!0,0,c.length);b=AmCharts.resetDateToMin(b,this.categoryAxis.minPeriod,1),c=this.getClosestIndex(c,"time",b.getTime(),!1,0,c.length),this.zoom(d,c)}else this.zoom(a.getTime(),b.getTime())},zoomToCategoryValues:function(a,b){this.updateScrollbar=!0,this.zoom(this.getCategoryIndexByValue(a),this.getCategoryIndexByValue(b))},formatPeriodString:function(a,b){if(b){var c=["value","open","low","high","close"],d="value open low high close average sum count".split(" "),e=b.valueAxis,f=this.chartData,g=b.numberFormatter;g||(g=this.nf);for(var h=0;h<c.length;h++){for(var i,j,k,l,m,n,o,p,q,r,s=c[h],t=0,u=0,v=0,w=0,x=this.start;x<=this.end;x++){var y=f[x];if(y&&(y=y.axes[e.id].graphs[b.id])){if(y.values){var z=y.values[s];if(!isNaN(z)){isNaN(i)&&(i=z),j=z,(isNaN(k)||k>z)&&(k=z),(isNaN(l)||z>l)&&(l=z),m=AmCharts.getDecimals(t);var A=AmCharts.getDecimals(z),t=t+z,t=AmCharts.roundTo(t,Math.max(m,A));u++,m=t/u}}y.percents&&(y=y.percents[s],!isNaN(y))&&(isNaN(n)&&(n=y),o=y,(isNaN(p)||p>y)&&(p=y),(isNaN(q)||y>q)&&(q=y),r=AmCharts.getDecimals(v),z=AmCharts.getDecimals(y),v+=y,v=AmCharts.roundTo(v,Math.max(r,z)),w++,r=v/w)}}v={open:n,close:o,high:q,low:p,average:r,sum:v,count:w},a=AmCharts.formatValue(a,{open:i,close:j,high:l,low:k,average:m,sum:t,count:u},d,g,s+"\\.",this.usePrefixes,this.prefixesOfSmallNumbers,this.prefixesOfBigNumbers),a=AmCharts.formatValue(a,v,d,this.pf,"percents\\."+s+"\\.")}}return a=AmCharts.cleanFromEmpty(a)},formatString:function(a,b,c){var d=b.graph;if(-1!=a.indexOf("[[category]]")){var e=b.serialDataItem.category;if(this.categoryAxis.parseDates){var f=this.balloonDateFormat,g=this.chartCursor;g&&(f=g.categoryBalloonDateFormat),-1!=a.indexOf("[[category]]")&&(f=AmCharts.formatDate(e,f,this),-1!=f.indexOf("fff")&&(f=AmCharts.formatMilliseconds(f,e)),e=f)}a=a.replace(/\[\[category\]\]/g,String(e))}return d=d.numberFormatter,d||(d=this.nf),e=b.graph.valueAxis,(f=e.duration)&&!isNaN(b.values.value)&&(e=AmCharts.formatDuration(b.values.value,f,"",e.durationUnits,e.maxInterval,d),a=a.replace(RegExp("\\[\\[value\\]\\]","g"),e)),e="value open low high close total".split(" "),f=this.pf,a=AmCharts.formatValue(a,b.percents,e,f,"percents\\."),a=AmCharts.formatValue(a,b.values,e,d,"",this.usePrefixes,this.prefixesOfSmallNumbers,this.prefixesOfBigNumbers),a=AmCharts.formatValue(a,b.values,["percents"],f),-1!=a.indexOf("[[")&&(a=AmCharts.formatDataContextValue(a,b.dataContext)),a=AmCharts.AmSerialChart.base.formatString.call(this,a,b,c)},addChartScrollbar:function(a){AmCharts.callMethod("destroy",[this.chartScrollbar]),a&&(a.chart=this,this.listenTo(a,"zoomed",this.handleScrollbarZoom)),this.rotate?void 0===a.width&&(a.width=a.scrollbarHeight):void 0===a.height&&(a.height=a.scrollbarHeight),this.chartScrollbar=a},removeChartScrollbar:function(){AmCharts.callMethod("destroy",[this.chartScrollbar]),this.chartScrollbar=null},handleReleaseOutside:function(a){AmCharts.AmSerialChart.base.handleReleaseOutside.call(this,a),AmCharts.callMethod("handleReleaseOutside",[this.chartScrollbar])}}),AmCharts.Cuboid=AmCharts.Class({construct:function(a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p){this.set=a.set(),this.container=a,this.h=Math.round(c),this.w=Math.round(b),this.dx=d,this.dy=e,this.colors=f,this.alpha=g,this.bwidth=h,this.bcolor=i,this.balpha=j,this.dashLength=n,this.topRadius=p,this.pattern=o,(this.rotate=m)?0>b&&0===k&&(k=180):0>c&&270==k&&(k=90),this.gradientRotation=k,0===d&&0===e&&(this.cornerRadius=l),this.draw()},draw:function(){var a=this.set;a.clear();var b=this.container,c=this.w,d=this.h,e=this.dx,f=this.dy,g=this.colors,h=this.alpha,i=this.bwidth,j=this.bcolor,k=this.balpha,l=this.gradientRotation,m=this.cornerRadius,n=this.dashLength,o=this.pattern,p=this.topRadius,q=g,r=g;"object"==typeof g&&(q=g[0],r=g[g.length-1]);var s,t,u,v,w,x,y,z,A,B=h;o&&(h=0);var C,D,E,F,G=this.rotate;if(0<Math.abs(e)||0<Math.abs(f))if(isNaN(p))y=r,r=AmCharts.adjustLuminosity(q,-.2),r=AmCharts.adjustLuminosity(q,-.2),s=AmCharts.polygon(b,[0,e,c+e,c,0],[0,f,f,0,0],r,h,1,j,0,l),k>0&&(A=AmCharts.line(b,[0,e,c+e],[0,f,f],j,k,i,n)),t=AmCharts.polygon(b,[0,0,c,c,0],[0,d,d,0,0],r,h,1,j,0,l),t.translate(e,f),k>0&&(u=AmCharts.line(b,[e,e],[f,f+d],j,k,i,n)),v=AmCharts.polygon(b,[0,0,e,e,0],[0,d,d+f,f,0],r,h,1,j,0,l),w=AmCharts.polygon(b,[c,c,c+e,c+e,c],[0,d,d+f,f,0],r,h,1,j,0,l),k>0&&(x=AmCharts.line(b,[c,c+e,c+e,c],[0,f,d+f,d],j,k,i,n)),r=AmCharts.adjustLuminosity(y,.2),y=AmCharts.polygon(b,[0,e,c+e,c,0],[d,d+f,d+f,d,d],r,h,1,j,0,l),k>0&&(z=AmCharts.line(b,[0,e,c+e],[d,d+f,d+f],j,k,i,n));else{var H,I,J;G?(H=d/2,r=e/2,J=d/2,I=c+e/2,D=Math.abs(d/2),C=Math.abs(e/2)):(r=c/2,H=f/2,I=c/2,J=d+f/2+1,C=Math.abs(c/2),D=Math.abs(f/2)),E=C*p,F=D*p,C>.1&&C>.1&&(s=AmCharts.circle(b,C,q,h,i,j,k,!1,D),s.translate(r,H)),E>.1&&E>.1&&(y=AmCharts.circle(b,E,AmCharts.adjustLuminosity(q,.5),h,i,j,k,!1,F),y.translate(I,J))}for(h=B,1>Math.abs(d)&&(d=0),1>Math.abs(c)&&(c=0),!isNaN(p)&&(0<Math.abs(e)||0<Math.abs(f))?(g=[q],g={fill:g,stroke:j,"stroke-width":i,"stroke-opacity":k,"fill-opacity":h},G?(h="M0,0 L"+c+","+(d/2-d/2*p),i=" B",c>0&&(i=" A"),AmCharts.VML?(h+=i+Math.round(c-E)+","+Math.round(d/2-F)+","+Math.round(c+E)+","+Math.round(d/2+F)+","+c+",0,"+c+","+d,h=h+(" L0,"+d)+(i+Math.round(-C)+","+Math.round(d/2-D)+","+Math.round(C)+","+Math.round(d/2+D)+",0,"+d+",0,0")):(h+="A"+E+","+F+",0,0,0,"+c+","+(d-d/2*(1-p))+"L0,"+d,h+="A"+C+","+D+",0,0,1,0,0"),C=90):(i=c/2-c/2*p,h="M0,0 L"+i+","+d,AmCharts.VML?(h="M0,0 L"+i+","+d,i=" B",0>d&&(i=" A"),h+=i+Math.round(c/2-E)+","+Math.round(d-F)+","+Math.round(c/2+E)+","+Math.round(d+F)+",0,"+d+","+c+","+d,h+=" L"+c+",0",h+=i+Math.round(c/2+C)+","+Math.round(D)+","+Math.round(c/2-C)+","+Math.round(-D)+","+c+",0,0,0"):(h+="A"+E+","+F+",0,0,0,"+(c-c/2*(1-p))+","+d+"L"+c+",0",h+="A"+C+","+D+",0,0,1,0,0"),C=180),b=b.path(h).attr(g),b.gradient("linearGradient",[q,AmCharts.adjustLuminosity(q,-.3),AmCharts.adjustLuminosity(q,-.3),q],C),G?b.translate(e/2,0):b.translate(0,f/2)):b=0===d?AmCharts.line(b,[0,c],[0,0],j,k,i,n):0===c?AmCharts.line(b,[0,0],[0,d],j,k,i,n):m>0?AmCharts.rect(b,c,d,g,h,i,j,k,m,l,n):AmCharts.polygon(b,[0,0,c,c,0],[0,d,d,0,0],g,h,i,j,k,l,!1,n),c=isNaN(p)?0>d?[s,A,t,u,v,w,x,y,z,b]:[y,z,t,u,v,w,s,A,x,b]:G?c>0?[s,b,y]:[y,b,s]:0>d?[s,b,y]:[y,b,s],d=0;d<c.length;d++)(e=c[d])&&a.push(e);o&&b.pattern(o)},width:function(a){this.w=Math.round(a),this.draw()},height:function(a){this.h=Math.round(a),this.draw()},animateHeight:function(a,b){var c=this;c.easing=b,c.totalFrames=Math.round(1e3*a/AmCharts.updateRate),c.rh=c.h,c.frame=0,c.height(1),setTimeout(function(){c.updateHeight.call(c)},AmCharts.updateRate)},updateHeight:function(){var a=this;a.frame++;var b=a.totalFrames;a.frame<=b&&(b=a.easing(0,a.frame,1,a.rh-1,b),a.height(b),setTimeout(function(){a.updateHeight.call(a)},AmCharts.updateRate))},animateWidth:function(a,b){var c=this;c.easing=b,c.totalFrames=Math.round(1e3*a/AmCharts.updateRate),c.rw=c.w,c.frame=0,c.width(1),setTimeout(function(){c.updateWidth.call(c)},AmCharts.updateRate)},updateWidth:function(){var a=this;a.frame++;var b=a.totalFrames;a.frame<=b&&(b=a.easing(0,a.frame,1,a.rw-1,b),a.width(b),setTimeout(function(){a.updateWidth.call(a)},AmCharts.updateRate))}}),AmCharts.CategoryAxis=AmCharts.Class({inherits:AmCharts.AxisBase,construct:function(a){this.cname="CategoryAxis",AmCharts.CategoryAxis.base.construct.call(this,a),this.minPeriod="DD",this.equalSpacing=this.parseDates=!1,this.position="bottom",this.startOnAxis=!1,this.firstDayOfWeek=1,this.gridPosition="middle",this.markPeriodChange=this.boldPeriodBeginning=!0,this.safeDistance=30,this.centerLabelOnFullPeriod=!0,this.periods=[{period:"ss",count:1},{period:"ss",count:5},{period:"ss",count:10},{period:"ss",count:30},{period:"mm",count:1},{period:"mm",count:5},{period:"mm",count:10},{period:"mm",count:30},{period:"hh",count:1},{period:"hh",count:3},{period:"hh",count:6},{period:"hh",count:12},{period:"DD",count:1},{period:"DD",count:2},{period:"DD",count:3},{period:"DD",count:4},{period:"DD",count:5},{period:"WW",count:1},{period:"MM",count:1},{period:"MM",count:2},{period:"MM",count:3},{period:"MM",count:6},{period:"YYYY",count:1},{period:"YYYY",count:2},{period:"YYYY",count:5},{period:"YYYY",count:10},{period:"YYYY",count:50},{period:"YYYY",count:100}],this.dateFormats=[{period:"fff",format:"JJ:NN:SS"},{period:"ss",format:"JJ:NN:SS"},{period:"mm",format:"JJ:NN"},{period:"hh",format:"JJ:NN"},{period:"DD",format:"MMM DD"},{period:"WW",format:"MMM DD"},{period:"MM",format:"MMM"},{period:"YYYY",format:"YYYY"}],this.nextPeriod={},this.nextPeriod.fff="ss",this.nextPeriod.ss="mm",this.nextPeriod.mm="hh",this.nextPeriod.hh="DD",this.nextPeriod.DD="MM",this.nextPeriod.MM="YYYY",AmCharts.applyTheme(this,a,this.cname)},draw:function(){AmCharts.CategoryAxis.base.draw.call(this),this.generateDFObject();var a=this.chart.chartData;if(this.data=a,AmCharts.ifArray(a)){var b,c=this.chart,d=this.start,e=this.labelFrequency,f=0;b=this.end-d+1;var g,h=this.gridCountR,i=this.showFirstLabel,j=this.showLastLabel,k="",l=AmCharts.extractPeriod(this.minPeriod);g=AmCharts.getPeriodDuration(l.period,l.count);var m,n,o,p,q,r;m=this.rotate;var s,t=this.firstDayOfWeek,u=this.boldPeriodBeginning,a=AmCharts.resetDateToMin(new Date(a[a.length-1].time+1.05*g),this.minPeriod,1,t).getTime();this.endTime>a&&(this.endTime=a),r=this.minorGridEnabled;var v,w,a=this.gridAlpha;if(this.parseDates&&!this.equalSpacing){this.timeDifference=this.endTime-this.startTime,d=this.choosePeriod(0),e=d.period,m=d.count,n=AmCharts.getPeriodDuration(e,m),g>n&&(e=l.period,m=l.count,n=g),o=e,"WW"==o&&(o="DD"),this.stepWidth=this.getStepWidth(this.timeDifference);var h=Math.ceil(this.timeDifference/n)+5,x=k=AmCharts.resetDateToMin(new Date(this.startTime-n),e,m,t).getTime();o==e&&1==m&&this.centerLabelOnFullPeriod&&(q=n*this.stepWidth),this.cellWidth=g*this.stepWidth,b=Math.round(k/n),d=-1,b/2==Math.round(b/2)&&(d=-2,k-=n);var y=c.firstTime,z=0;if(r&&m>1&&(v=this.chooseMinorFrequency(m),w=AmCharts.getPeriodDuration(e,v)),0<this.gridCountR)for(b=d;h>=b;b++){if(l=y+n*(b+Math.floor((x-y)/n))-z,"DD"==e&&(l+=36e5),l=AmCharts.resetDateToMin(new Date(l),e,m,t).getTime(),"MM"==e&&(r=(l-k)/n,(l-k)/n>=1.5&&(l=l-(r-1)*n+AmCharts.getPeriodDuration("DD",3),l=AmCharts.resetDateToMin(new Date(l),e,1).getTime(),z+=n)),g=(l-this.startTime)*this.stepWidth,r=!1,this.nextPeriod[o]&&(r=this.checkPeriodChange(this.nextPeriod[o],1,l,k,o)),s=!1,r&&this.markPeriodChange?(r=this.dateFormatsObject[this.nextPeriod[o]],this.twoLineMode&&(r=this.dateFormatsObject[o]+"\n"+r,r=AmCharts.fixBrakes(r)),s=!0):r=this.dateFormatsObject[o],u||(s=!1),k=AmCharts.formatDate(new Date(l),r,c),(b==d&&!i||b==h&&!j)&&(k=" "),this.labelFunction&&(k=this.labelFunction(k,new Date(l),this,e,m,p).toString()),this.boldLabels&&(s=!0),p=new this.axisItemRenderer(this,g,k,!1,q,0,!1,s),this.pushAxisItem(p),p=k=l,!isNaN(v))for(g=1;m>g;g+=v)this.gridAlpha=this.minorGridAlpha,r=l+w*g,r=AmCharts.resetDateToMin(new Date(r),e,v,t).getTime(),r=new this.axisItemRenderer(this,(r-this.startTime)*this.stepWidth),this.pushAxisItem(r);this.gridAlpha=a}}else if(this.parseDates){if(this.parseDates&&this.equalSpacing){if(f=this.start,this.startTime=this.data[this.start].time,this.endTime=this.data[this.end].time,this.timeDifference=this.endTime-this.startTime,d=this.choosePeriod(0),e=d.period,m=d.count,n=AmCharts.getPeriodDuration(e,m),g>n&&(e=l.period,m=l.count,n=g),o=e,"WW"==o&&(o="DD"),this.stepWidth=this.getStepWidth(b),h=Math.ceil(this.timeDifference/n)+1,k=AmCharts.resetDateToMin(new Date(this.startTime-n),e,m,t).getTime(),this.cellWidth=this.getStepWidth(b),b=Math.round(k/n),d=-1,b/2==Math.round(b/2)&&(d=-2,k-=n),g=this.start,g/2==Math.round(g/2)&&g--,0>g&&(g=0),q=this.end+2,q>=this.data.length&&(q=this.data.length),w=!1,w=!i,this.previousPos=-1e3,20<this.labelRotation&&(this.safeDistance=5),n=g,this.data[g].time!=AmCharts.resetDateToMin(new Date(this.data[g].time),e,m,t).getTime())for(t=0,s=k,b=g;q>b;b++)l=this.data[b].time,this.checkPeriodChange(e,m,l,s)&&(t++,t>=2&&(n=b,b=q),s=l);if(r&&m>1&&(v=this.chooseMinorFrequency(m),AmCharts.getPeriodDuration(e,v)),0<this.gridCountR)for(b=g;q>b;b++)l=this.data[b].time,this.checkPeriodChange(e,m,l,k)&&b>=n?(g=this.getCoordinate(b-this.start),r=!1,this.nextPeriod[o]&&(r=this.checkPeriodChange(this.nextPeriod[o],1,l,k,o)),s=!1,r&&this.markPeriodChange?(r=this.dateFormatsObject[this.nextPeriod[o]],s=!0):r=this.dateFormatsObject[o],k=AmCharts.formatDate(new Date(l),r,c),(b==d&&!i||b==h&&!j)&&(k=" "),w?w=!1:(u||(s=!1),g-this.previousPos>this.safeDistance*Math.cos(this.labelRotation*Math.PI/180)&&(this.labelFunction&&(k=this.labelFunction(k,new Date(l),this,e,m,p)),this.boldLabels&&(s=!0),p=new this.axisItemRenderer(this,g,k,void 0,void 0,void 0,void 0,s),t=p.graphics(),this.pushAxisItem(p),p=t.getBBox().width,AmCharts.isModern||(p-=g),this.previousPos=g+p)),p=k=l):isNaN(v)||(this.checkPeriodChange(e,v,l,x)&&(this.gridAlpha=this.minorGridAlpha,g=this.getCoordinate(b-this.start),r=new this.axisItemRenderer(this,g),this.pushAxisItem(r),x=l),this.gridAlpha=a)}}else if(this.cellWidth=this.getStepWidth(b),h>b&&(h=b),f+=this.start,this.stepWidth=this.getStepWidth(b),h>0)for(u=Math.floor(b/h),v=this.chooseMinorFrequency(u),g=f,g/2==Math.round(g/2)&&g--,0>g&&(g=0),h=0,this.end-g+1>=this.autoRotateCount&&(this.labelRotation=this.autoRotateAngle),b=g;b<=this.end+2;b++){if(p=!1,b>=0&&b<this.data.length?(o=this.data[b],k=o.category,p=o.forceShow):k="",r&&!isNaN(v)){if(b/v!=Math.round(b/v)&&!p)continue;b/u==Math.round(b/u)||p||(this.gridAlpha=this.minorGridAlpha,k=void 0)}else if(b/u!=Math.round(b/u)&&!p)continue;g=this.getCoordinate(b-f),p=0,"start"==this.gridPosition&&(g-=this.cellWidth/2,p=this.cellWidth/2),t=!0,tickShift=p,"start"==this.tickPosition&&(tickShift=0,t=!1,p=0),(b==d&&!i||b==this.end&&!j)&&(k=void 0),Math.round(h/e)!=h/e&&(k=void 0),h++,x=this.cellWidth,m&&(x=0/0),this.labelFunction&&o&&(k=this.labelFunction(k,o,this)),k=AmCharts.fixBrakes(k),s=!1,this.boldLabels&&(s=!0),b>this.end&&"start"==this.tickPosition&&(k=" "),p=new this.axisItemRenderer(this,g,k,t,x,p,void 0,s,tickShift,!1,o.labelColor),p.serialDataItem=o,this.pushAxisItem(p),this.gridAlpha=a}for(b=0;b<this.data.length;b++)(i=this.data[b])&&(j=this.parseDates&&!this.equalSpacing?Math.round((i.time-this.startTime)*this.stepWidth+this.cellWidth/2):this.getCoordinate(b-f),i.x[this.id]=j);for(i=this.guides.length,b=0;i>b;b++)j=this.guides[b],u=u=u=a=d=0/0,v=j.above,j.toCategory&&(u=c.getCategoryIndexByValue(j.toCategory),isNaN(u)||(d=this.getCoordinate(u-f),j.expand&&(d+=this.cellWidth/2),p=new this.axisItemRenderer(this,d,"",!0,0/0,0/0,j),this.pushAxisItem(p,v))),j.category&&(u=c.getCategoryIndexByValue(j.category),isNaN(u)||(a=this.getCoordinate(u-f),j.expand&&(a-=this.cellWidth/2),u=(d-a)/2,p=new this.axisItemRenderer(this,a,j.label,!0,0/0,u,j),this.pushAxisItem(p,v))),j.toDate&&(j.toDate instanceof Date||(j.toDate=AmCharts.stringToDate(j.toDate,c.dataDateFormat)),this.equalSpacing?(u=c.getClosestIndex(this.data,"time",j.toDate.getTime(),!1,0,this.data.length-1),isNaN(u)||(d=this.getCoordinate(u-f))):d=(j.toDate.getTime()-this.startTime)*this.stepWidth,p=new this.axisItemRenderer(this,d,"",!0,0/0,0/0,j),this.pushAxisItem(p,v)),j.date&&(j.date instanceof Date||(j.date=AmCharts.stringToDate(j.date,c.dataDateFormat)),this.equalSpacing?(u=c.getClosestIndex(this.data,"time",j.date.getTime(),!1,0,this.data.length-1),isNaN(u)||(a=this.getCoordinate(u-f))):a=(j.date.getTime()-this.startTime)*this.stepWidth,u=(d-a)/2,p="H"==this.orientation?new this.axisItemRenderer(this,a,j.label,!1,2*u,0/0,j):new this.axisItemRenderer(this,a,j.label,!1,0/0,u,j),this.pushAxisItem(p,v)),(d>0||a>0)&&(d<this.width||a<this.width)&&(d=new this.guideFillRenderer(this,a,d,j),a=d.graphics(),this.pushAxisItem(d,v),j.graphics=a,a.index=b,j.balloonText&&this.addEventListeners(a,j))}this.axisCreated=!0,c=this.x,f=this.y,this.set.translate(c,f),this.labelsSet.translate(c,f),this.positionTitle(),(c=this.axisLine.set)&&c.toFront(),c=this.getBBox().height,2<c-this.previousHeight&&this.autoWrap&&!this.parseDates&&(this.axisCreated=this.chart.marginsUpdated=!1),this.previousHeight=c},chooseMinorFrequency:function(a){for(var b=10;b>0;b--)if(a/b==Math.round(a/b))return a/b},choosePeriod:function(a){var b=AmCharts.getPeriodDuration(this.periods[a].period,this.periods[a].count),c=Math.ceil(this.timeDifference/b),d=this.periods;return this.timeDifference<b&&a>0?d[a-1]:c<=this.gridCountR?d[a]:a+1<d.length?this.choosePeriod(a+1):d[a]},getStepWidth:function(a){var b;return this.startOnAxis?(b=this.axisWidth/(a-1),1==a&&(b=this.axisWidth)):b=this.axisWidth/a,b},getCoordinate:function(a){return a*=this.stepWidth,this.startOnAxis||(a+=this.stepWidth/2),Math.round(a)},timeZoom:function(a,b){this.startTime=a,this.endTime=b},minDuration:function(){var a=AmCharts.extractPeriod(this.minPeriod);return AmCharts.getPeriodDuration(a.period,a.count)},checkPeriodChange:function(a,b,c,d,e){c=new Date(c);var f=new Date(d),g=this.firstDayOfWeek;return d=b,"DD"==a&&(b=1),c=AmCharts.resetDateToMin(c,a,b,g).getTime(),b=AmCharts.resetDateToMin(f,a,b,g).getTime(),"DD"==a&&"hh"!=e&&c-b<=AmCharts.getPeriodDuration(a,d)?!1:c!=b?!0:!1},generateDFObject:function(){this.dateFormatsObject={};var a;for(a=0;a<this.dateFormats.length;a++){var b=this.dateFormats[a];this.dateFormatsObject[b.period]=b.format}},xToIndex:function(a){var b=this.data,c=this.chart,d=c.rotate,e=this.stepWidth;this.parseDates&&!this.equalSpacing?(a=this.startTime+Math.round(a/e)-this.minDuration()/2,c=c.getClosestIndex(b,"time",a,!1,this.start,this.end+1)):(this.startOnAxis||(a-=e/2),c=this.start+Math.round(a/e));var f,c=AmCharts.fitToBounds(c,0,b.length-1);return b[c]&&(f=b[c].x[this.id]),d?f>this.height+1&&c--:f>this.width+1&&c--,0>f&&c++,c=AmCharts.fitToBounds(c,0,b.length-1)},dateToCoordinate:function(a){return this.parseDates&&!this.equalSpacing?(a.getTime()-this.startTime)*this.stepWidth:this.parseDates&&this.equalSpacing?(a=this.chart.getClosestIndex(this.data,"time",a.getTime(),!1,0,this.data.length-1),this.getCoordinate(a-this.start)):0/0},categoryToCoordinate:function(a){return this.chart?(a=this.chart.getCategoryIndexByValue(a),this.getCoordinate(a-this.start)):0/0},coordinateToDate:function(a){return this.equalSpacing?(a=this.xToIndex(a),new Date(this.data[a].time)):new Date(this.startTime+a/this.stepWidth)}}),AmCharts.AmXYChart=AmCharts.Class({inherits:AmCharts.AmRectangularChart,construct:function(a){this.type="xy",AmCharts.AmXYChart.base.construct.call(this,a),this.cname="AmXYChart",this.theme=a,this.createEvents("zoomed"),this.maxZoomFactor=20,AmCharts.applyTheme(this,a,this.cname)},initChart:function(){AmCharts.AmXYChart.base.initChart.call(this),this.dataChanged&&(this.updateData(),this.dataChanged=!1,this.dispatchDataUpdated=!0),this.updateScrollbar=!0,this.drawChart(),this.autoMargins&&!this.marginsUpdated&&(this.marginsUpdated=!0,this.measureMargins());var a=this.marginLeftReal,b=this.marginTopReal,c=this.plotAreaWidth,d=this.plotAreaHeight;this.graphsSet.clipRect(a,b,c,d),this.bulletSet.clipRect(a,b,c,d),this.trendLinesSet.clipRect(a,b,c,d)},prepareForExport:function(){var a=this.bulletSet;a.clipPath&&this.container.remove(a.clipPath)},createValueAxes:function(){var a=[],b=[];this.xAxes=a,this.yAxes=b;var c,d,e=this.valueAxes;for(d=0;d<e.length;d++){c=e[d];var f=c.position;("top"==f||"bottom"==f)&&(c.rotate=!0),c.setOrientation(c.rotate),f=c.orientation,"V"==f&&b.push(c),"H"==f&&a.push(c)}for(0===b.length&&(c=new AmCharts.ValueAxis(this.theme),c.rotate=!1,c.setOrientation(!1),e.push(c),b.push(c)),0===a.length&&(c=new AmCharts.ValueAxis(this.theme),c.rotate=!0,c.setOrientation(!0),e.push(c),a.push(c)),d=0;d<e.length;d++)this.processValueAxis(e[d],d);for(a=this.graphs,d=0;d<a.length;d++)this.processGraph(a[d],d)},drawChart:function(){if(AmCharts.AmXYChart.base.drawChart.call(this),AmCharts.ifArray(this.chartData)?(this.chartScrollbar&&this.updateScrollbars(),this.zoomChart()):this.cleanChart(),this.hideXScrollbar){var a=this.scrollbarH;a&&(this.removeListener(a,"zoomed",this.handleHSBZoom),a.destroy()),this.scrollbarH=null}this.hideYScrollbar&&((a=this.scrollbarV)&&(this.removeListener(a,"zoomed",this.handleVSBZoom),a.destroy()),this.scrollbarV=null),(!this.autoMargins||this.marginsUpdated)&&(this.dispDUpd(),this.chartCreated=!0,this.zoomScrollbars())},cleanChart:function(){AmCharts.callMethod("destroy",[this.valueAxes,this.graphs,this.scrollbarV,this.scrollbarH,this.chartCursor])},zoomChart:function(){this.toggleZoomOutButton(),this.zoomObjects(this.valueAxes),this.zoomObjects(this.graphs),this.zoomTrendLines(),this.dispatchAxisZoom()},toggleZoomOutButton:function(){this.showZB(1==this.heightMultiplier&&1==this.widthMultiplier?!1:!0)},dispatchAxisZoom:function(){var a,b=this.valueAxes;for(a=0;a<b.length;a++){var c=b[a];if(!isNaN(c.min)&&!isNaN(c.max)){var d,e;if("V"==c.orientation?(d=c.coordinateToValue(-this.verticalPosition),e=c.coordinateToValue(-this.verticalPosition+this.plotAreaHeight)):(d=c.coordinateToValue(-this.horizontalPosition),e=c.coordinateToValue(-this.horizontalPosition+this.plotAreaWidth)),!isNaN(d)&&!isNaN(e)){if(d>e){var f=e;e=d,d=f}c.dispatchZoomEvent(d,e)}}}},zoomObjects:function(a){
var b,c=a.length;for(b=0;c>b;b++){var d=a[b];this.updateObjectSize(d),d.zoom(0,this.chartData.length-1)}},updateData:function(){this.parseData();var a,b,c=this.chartData,d=c.length-1,e=this.graphs,f=this.dataProvider,g=-(1/0),h=1/0;for(a=0;a<e.length;a++)if(b=e[a],b.data=c,b.zoom(0,d),b=b.valueField){var i;for(i=0;i<f.length;i++){var j=Number(f[i][b]);j>g&&(g=j),h>j&&(h=j)}}for(a=0;a<e.length;a++)b=e[a],b.maxValue=g,b.minValue=h;(c=this.chartCursor)&&(c.updateData(),c.type="crosshair",c.valueBalloonsEnabled=!1)},zoomOut:function(){this.verticalPosition=this.horizontalPosition=0,this.heightMultiplier=this.widthMultiplier=1,this.zoomChart(),this.zoomScrollbars()},processValueAxis:function(a){a.chart=this,a.minMaxField="H"==a.orientation?"x":"y",a.minTemp=0/0,a.maxTemp=0/0,this.listenTo(a,"axisSelfZoomed",this.handleAxisSelfZoom)},processGraph:function(a){AmCharts.isString(a.xAxis)&&(a.xAxis=this.getValueAxisById(a.xAxis)),AmCharts.isString(a.yAxis)&&(a.yAxis=this.getValueAxisById(a.yAxis)),a.xAxis||(a.xAxis=this.xAxes[0]),a.yAxis||(a.yAxis=this.yAxes[0]),a.valueAxis=a.yAxis},parseData:function(){AmCharts.AmXYChart.base.parseData.call(this),this.chartData=[];var a,b=this.dataProvider,c=this.valueAxes,d=this.graphs;if(b)for(a=0;a<b.length;a++){var e,f={axes:{},x:{},y:{}},g=b[a];for(e=0;e<c.length;e++){var h=c[e].id;f.axes[h]={},f.axes[h].graphs={};var i;for(i=0;i<d.length;i++){var j=d[i],k=j.id;if(j.xAxis.id==h||j.yAxis.id==h){var l={};l.serialDataItem=f,l.index=a;var m={},n=Number(g[j.valueField]);isNaN(n)||(m.value=n),n=Number(g[j.xField]),isNaN(n)||(m.x=n),n=Number(g[j.yField]),isNaN(n)||(m.y=n),n=Number(g[j.errorField]),isNaN(n)||(m.error=n),l.values=m,this.processFields(j,l,g),l.serialDataItem=f,l.graph=j,f.axes[h].graphs[k]=l}}}this.chartData[a]=f}},formatString:function(a,b,c){var d=b.graph.numberFormatter;return d||(d=this.nf),a=AmCharts.formatValue(a,b.values,["value","x","y"],d),-1!=a.indexOf("[[")&&(a=AmCharts.formatDataContextValue(a,b.dataContext)),a=AmCharts.AmXYChart.base.formatString.call(this,a,b,c)},addChartScrollbar:function(a){if(AmCharts.callMethod("destroy",[this.chartScrollbar,this.scrollbarH,this.scrollbarV]),a){this.chartScrollbar=a,this.scrollbarHeight=a.scrollbarHeight;var b="backgroundColor backgroundAlpha selectedBackgroundColor selectedBackgroundAlpha scrollDuration resizeEnabled hideResizeGrips scrollbarHeight updateOnReleaseOnly".split(" ");if(!this.hideYScrollbar){var c=new AmCharts.SimpleChartScrollbar(this.theme);c.skipEvent=!0,c.chart=this,this.listenTo(c,"zoomed",this.handleVSBZoom),AmCharts.copyProperties(a,c,b),c.rotate=!0,this.scrollbarV=c}this.hideXScrollbar||(c=new AmCharts.SimpleChartScrollbar(this.theme),c.skipEvent=!0,c.chart=this,this.listenTo(c,"zoomed",this.handleHSBZoom),AmCharts.copyProperties(a,c,b),c.rotate=!1,this.scrollbarH=c)}},updateTrendLines:function(){var a,b=this.trendLines;for(a=0;a<b.length;a++){var c=b[a],c=AmCharts.processObject(c,AmCharts.TrendLine,this.theme);b[a]=c,c.chart=this;var d=c.valueAxis;AmCharts.isString(d)&&(c.valueAxis=this.getValueAxisById(d)),d=c.valueAxisX,AmCharts.isString(d)&&(c.valueAxisX=this.getValueAxisById(d)),c.valueAxis||(c.valueAxis=this.yAxes[0]),c.valueAxisX||(c.valueAxisX=this.xAxes[0])}},updateMargins:function(){AmCharts.AmXYChart.base.updateMargins.call(this);var a=this.scrollbarV;a&&(this.getScrollbarPosition(a,!0,this.yAxes[0].position),this.adjustMargins(a,!0)),(a=this.scrollbarH)&&(this.getScrollbarPosition(a,!1,this.xAxes[0].position),this.adjustMargins(a,!1))},updateScrollbars:function(){AmCharts.AmXYChart.base.updateScrollbars.call(this);var a=this.scrollbarV;a&&(this.updateChartScrollbar(a,!0),a.draw()),(a=this.scrollbarH)&&(this.updateChartScrollbar(a,!1),a.draw())},zoomScrollbars:function(){var a=this.scrollbarH;a&&a.relativeZoom(this.widthMultiplier,-this.horizontalPosition/this.widthMultiplier),(a=this.scrollbarV)&&a.relativeZoom(this.heightMultiplier,-this.verticalPosition/this.heightMultiplier)},fitMultiplier:function(a){return a>this.maxZoomFactor&&(a=this.maxZoomFactor),a},handleHSBZoom:function(a){var b=this.fitMultiplier(a.multiplier);a=-a.position*b;var c=-(this.plotAreaWidth*b-this.plotAreaWidth);c>a&&(a=c),this.widthMultiplier=b,this.horizontalPosition=a,this.zoomChart()},handleVSBZoom:function(a){var b=this.fitMultiplier(a.multiplier);a=-a.position*b;var c=-(this.plotAreaHeight*b-this.plotAreaHeight);c>a&&(a=c),this.heightMultiplier=b,this.verticalPosition=a,this.zoomChart()},handleAxisSelfZoom:function(a){if("H"==a.valueAxis.orientation){var b=this.fitMultiplier(a.multiplier);a=-a.position*b;var c=-(this.plotAreaWidth*b-this.plotAreaWidth);c>a&&(a=c),this.horizontalPosition=a,this.widthMultiplier=b}else b=this.fitMultiplier(a.multiplier),a=-a.position*b,c=-(this.plotAreaHeight*b-this.plotAreaHeight),c>a&&(a=c),this.verticalPosition=a,this.heightMultiplier=b;for(this.zoomChart(),b=this.graphs,a=0;a<b.length;a++)b[a].setAnimationPlayed();this.zoomScrollbars()},handleCursorZoom:function(a){var b=this.widthMultiplier*this.plotAreaWidth/a.selectionWidth,c=this.heightMultiplier*this.plotAreaHeight/a.selectionHeight,b=this.fitMultiplier(b),c=this.fitMultiplier(c);this.horizontalPosition=(this.horizontalPosition-a.selectionX)*b/this.widthMultiplier,this.verticalPosition=(this.verticalPosition-a.selectionY)*c/this.heightMultiplier,this.widthMultiplier=b,this.heightMultiplier=c,this.zoomChart(),this.zoomScrollbars()},removeChartScrollbar:function(){AmCharts.callMethod("destroy",[this.scrollbarH,this.scrollbarV]),this.scrollbarV=this.scrollbarH=null},handleReleaseOutside:function(a){AmCharts.AmXYChart.base.handleReleaseOutside.call(this,a),AmCharts.callMethod("handleReleaseOutside",[this.scrollbarH,this.scrollbarV])}});