package com.kyb.pcberp.modules.report.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

import java.math.BigDecimal;

public class FactCostDeail extends DataEntity<FactCostDeail>
{
    private static final long serialVersionUID = 1L;

    private String month; // 年月

    private BigDecimal num;

    private BigDecimal money;

    private String departName; // 部门

    private String type;

    public String getMonth()
    {
        return month;
    }

    public void setMonth(String month)
    {
        this.month = month;
    }

    public BigDecimal getNum()
    {
        return num;
    }

    public void setNum(BigDecimal num)
    {
        this.num = num;
    }

    public BigDecimal getMoney()
    {
        return money;
    }

    public void setMoney(BigDecimal money)
    {
        this.money = money;
    }

    public String getDepartName()
    {
        return departName;
    }

    public void setDepartName(String departName)
    {
        this.departName = departName;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }
}
