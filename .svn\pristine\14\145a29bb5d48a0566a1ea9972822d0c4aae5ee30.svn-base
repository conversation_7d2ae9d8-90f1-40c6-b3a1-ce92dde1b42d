const certificateAudit = {
    template:'#certificateAudit',
    computed: {
        audit: {
            get () {
                return this.$store.state.auditStore.audit
            }
        }
    },
    methods:{
        openClose(row){
            if (row.showFlag){
                this.$set(row,'showFlag','');
            }else {
                this.$set(row,'showFlag',1);
            }
        },
    }
}