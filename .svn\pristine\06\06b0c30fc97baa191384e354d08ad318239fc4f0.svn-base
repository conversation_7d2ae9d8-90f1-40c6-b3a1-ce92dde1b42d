package com.kyb.pcberp.modules.production.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;

import java.util.Date;

public class BottleneckProcessShift extends DataEntity<BottleneckProcessShift>
{
    private static final long serialVersionUID = 1L;

    private String name; // 班次名称

    private Date startDate; // 开始时间

    private Date endDate; // 结束时间

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getStartDateStr()
    {
        if(null != startDate)
        {
            return DateUtils.formatDate(startDate,"HH:mm:ss");
        }
        return null;
    }

    public String getEndDateStr()
    {
        if(null != endDate)
        {
            return DateUtils.formatDate(endDate,"HH:mm:ss");
        }
        return null;
    }
}
