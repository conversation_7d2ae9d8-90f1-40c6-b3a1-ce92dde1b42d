package com.kyb.pcberp.modules.report.utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.SpringContextHolder;
import com.kyb.pcberp.modules.contract.entity.Delivery;
import com.kyb.pcberp.modules.contract.entity.MaterialContractDetail;
import com.kyb.pcberp.modules.contract.utils.ChangeDataUtils;
import com.kyb.pcberp.modules.crm.dao.AccountsReceivableDao;
import com.kyb.pcberp.modules.crm.dao.GoodsCheckDao;
import com.kyb.pcberp.modules.crm.entity.AccountsReceivable;
import com.kyb.pcberp.modules.crm.entity.GoodsCheck;
import com.kyb.pcberp.modules.purch.dao.AccountsPayableDao;
import com.kyb.pcberp.modules.purch.dao.MaterialCheckDao;
import com.kyb.pcberp.modules.purch.entity.AccountsPayable;
import com.kyb.pcberp.modules.purch.entity.MaterialCheck;
import com.kyb.pcberp.modules.purch.entity.PrdorderDetail;
import com.kyb.pcberp.modules.purch.entity.ProductCraft;
import com.kyb.pcberp.modules.stock.entity.ProductStore;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

/**
 * zjn 2018-10-16 生成财务报表工具类
 */
public class FinanceUtils
{
    private static GoodsCheckDao goodsCheckDao = SpringContextHolder.getBean(GoodsCheckDao.class);
    
    private static MaterialCheckDao materialCheckDao = SpringContextHolder.getBean(MaterialCheckDao.class);
    
    private static AccountsPayableDao accountsPayableDao = SpringContextHolder.getBean(AccountsPayableDao.class);
    
    private static AccountsReceivableDao accountsReceivableDao =
        SpringContextHolder.getBean(AccountsReceivableDao.class);
    
    /** zjn 2018-10-17 检查原料合同明细是否已发完货 */
    public static void executeCheckRawContractDtlStatus(Delivery delivery)
    {
        // 已完成合同明细列表
        List<MaterialContractDetail> mcdList = filterFinshedRawConDtl(delivery);
        
        // 若不存在已完成的合同明细不再处理
        if (Collections3.isEmpty(mcdList))
        {
            return;
        }
        
        // 循环处理已完成合同明细
        for (MaterialContractDetail mcd : mcdList)
        {
            // 判断合同是否需要更新状态
            updateRawContractStatus(mcd);
        }
    }
    
    /** zjn 2017-02-10 筛选出已完成原料合同明细列表，及更新原料合同明细状态 */
    public static List<MaterialContractDetail> filterFinshedRawConDtl(Delivery delivery)
    {
        // 已完成合原料同明细列表
        List<MaterialContractDetail> mcdList = new ArrayList<>();
        return mcdList;
    }
    
    /** zjn 2018-10-17 判断原料合同是否需要更新状态 */
    public static void updateRawContractStatus(MaterialContractDetail materialContractDetail)
    {
    }
    
    /** zjn 2018-12-06 冲红出入库记录，更新付款对账单金额、删除付款对账单明细(参数：原料出入库记录、成品出入库记录、区分原料、成品) */
    public static void redCutAccountsPayableCheck(RawmaterialStock raw, ProductStore ps, Integer num)
    {
        Map<String, Object> map = new HashMap<>();
        Integer type = null;
        
        // 原料
        if (num == 1)
        {
            map.put("company", raw.getCompany());
            type = raw.getInoutType();
        }
        // 成品
        else if (num == 2)
        {
            map.put("company", ps.getCompany());
            type = ps.getInoutType();
        }
        
        switch (type)
        {
            // 原料采购入库
            case 1:
                map.put("rawPoDetailId", raw.getRecordId());
                if (raw.getRushredQuantity() != null && raw.getRushredQuantity().compareTo(raw.getQuantity()) != 0)
                {
                    redCutAccountsPayableCheckOnePart(map, raw);
                }
                else
                {
                    redCutAccountsPayableCheckOne(map, true);
                }
                break;
            
            // 原料入库
            case 2:
                map.put("rawStockId", raw.getRecordId());
                if (raw.getRushredQuantity() != null && raw.getRushredQuantity().compareTo(raw.getQuantity()) != 0)
                {
                    redCutAccountsPayableCheckOnePart(map, raw);
                }
                else
                {
                    redCutAccountsPayableCheckOne(map, true);
                }
                break;
            
            // 原料采购退货出库
            case 4:
                map.put("prPoDetailOutId", raw.getReturnsNo());
                redCutAccountsPayableCheckOne(map, false);
                break;
            
            // 原料采购补货入库
            case 7:
                map.put("prPoDetailInId", raw.getReturnsNo());
                if (raw.getRushredQuantity() != null && raw.getRushredQuantity().compareTo(raw.getQuantity()) != 0)
                {
                    redCutAccountsPayableCheckOnePart(map, raw);
                }
                else
                {
                    redCutAccountsPayableCheckOne(map, true);
                }
                break;
            
            // 成品采购入库
            case 14:
                map.put("prdorderDetailId", ps.getRecordId());
                redCutAccountsPayableCheckOne(map, true);
                break;
            
            // 成品采购退货出库
            case 16:
                map.put("prPdDeteilOutId", ps.getDeliveNo());
                redCutAccountsPayableCheckOne(map, false);
                break;
            
            // 成品采购补货入库
            case 15:
                map.put("prPdDeteilInId", ps.getDeliveNo());
                redCutAccountsPayableCheckOne(map, true);
                break;
        }
    }
    
    /** rx 2021-10-15 入库部分冲红 更新付款对账单金额，更新付款对账单明细 */
    public static void redCutAccountsPayableCheckOnePart(Map<String, Object> map, RawmaterialStock raw)
    {
        MaterialCheck mc = new MaterialCheck();
        AccountsPayable ap = new AccountsPayable();
        mc = materialCheckDao.getMaterialCheckBySourceId(map);
        if (null != mc)
        {
            BigDecimal quantity = raw.getQuantity() == null ? BigDecimal.ZERO : raw.getQuantity();
            BigDecimal rushredQuantity = raw.getRushredQuantity() == null ? BigDecimal.ZERO : raw.getRushredQuantity();
            BigDecimal price = raw.getPrice() == null ? BigDecimal.ZERO : raw.getPrice();
            BigDecimal rushredAmount = rushredQuantity.multiply(price);
            BigDecimal nowQuantity = quantity.subtract(rushredQuantity);
            // 更新付款对账单金额
            ap = accountsPayableDao.getAccountsPayableAmount(mc);
            if (null != ap)
            {
                ap.setRecvMaterialValue(ap.getRecvMaterialValue().subtract(rushredAmount));
                accountsPayableDao.updateAccountsPayableAmount(ap);
            }
            
            // 更新付款对账明细，减去冲红数量
            mc.setQuantity(nowQuantity);
            if (mc.getAmount() != null)
            {
                if (mc.getAmount().compareTo(rushredAmount) >= 0)
                {
                    mc.setAmount(mc.getAmount().subtract(rushredAmount));
                }
            }
            mc.setWriteOffCause((mc.getWriteOffCause() == null ? "" : mc.getWriteOffCause() + ";") + "部分冲红：原数量："
                + quantity + ";冲红数量：" + rushredQuantity + "剩余数量：" + nowQuantity);
            mc.preUpdate();
            materialCheckDao.redPartUpdateMaterialCheck(mc);
            
            ChangeDataUtils changeDataUtils = new ChangeDataUtils();
            mc.setAmount(rushredAmount.multiply(new BigDecimal(-1)));
            changeDataUtils.advanceChargeWriteOff(mc.getCompany().getRecordId(), "1", mc.getSourceId(), mc, "2");
        }
    }
    
    /** zjn 2018-12-06 更新付款对账单金额，删除付款对账单明细(flag:true 收货,false 退货) */
    public static void redCutAccountsPayableCheckOne(Map<String, Object> map, Boolean flag)
    {
        MaterialCheck mc = new MaterialCheck();
        AccountsPayable ap = new AccountsPayable();
        
        mc = materialCheckDao.getMaterialCheckBySourceId(map);
        if (null != mc)
        {
            // 更新付款对账单金额
            ap = accountsPayableDao.getAccountsPayableAmount(mc);
            if (null != ap)
            {
                if (flag)
                {
                    // 收货金额
                    ap.setRecvMaterialValue(ap.getRecvMaterialValue().subtract(mc.getAmount()));
                }
                else
                {
                    // 退货金额
                    ap.setRetnMaterialValue(ap.getRetnMaterialValue().subtract(mc.getAmount()));
                }
                accountsPayableDao.updateAccountsPayableAmount(ap);
            }
            
            // 逻辑删除付款对账明细
            mc.setActiveFlag(TypeKey.INACTIVE.toString());
            materialCheckDao.deleteMaterialCheck(mc);
            
            ChangeDataUtils changeDataUtils = new ChangeDataUtils();
            mc.setAmount(mc.getAmount().multiply(new BigDecimal(-1)));
            changeDataUtils.advanceChargeWriteOff(mc.getCompany().getRecordId(), "1", mc.getSourceId(), mc, "2");
        }
    }
    
    /** zjn 2018-12-06 更新收款对账单金额，删除收款对账单明细(flag:true 收货,false 退货) */
    public static void redCutAccountsReceivableCheckOne(Map<String, Object> map, Boolean flag)
    {
        GoodsCheck gc = new GoodsCheck();
        AccountsReceivable ar = new AccountsReceivable();
        
        gc = goodsCheckDao.getGoodsCheckBySourceId(map);
        
        if (null != gc)
        {
            // 更新付款对账单金额
            ar = accountsReceivableDao.getAccountsReceivableAmount(gc);
            if (null != ar)
            {
                if (flag)
                {
                    // 收货金额
                    ar.setSentGoodsValue(ar.getSentGoodsValue().subtract(gc.getAmount()));
                }
                else
                {
                    // 退货金额
                    ar.setReturnGoodsValue(ar.getReturnGoodsValue().subtract(gc.getAmount()));
                }
                accountsReceivableDao.updateAccountsReceivableAmount(ar);
            }
            
            // 逻辑删除收款对账明细
            gc.setActiveFlag(TypeKey.INACTIVE.toString());
            goodsCheckDao.deleteGoodsCheck(gc);
        }
    }
    
    /** zjn 2018-12-18 原料出入库记录冲红验证对账单明细是否转月份、是否分页对账 */
    public static String rawStockValidator(RawmaterialStock raw)
    {
        String result = "success";
        MaterialCheck mc = new MaterialCheck();
        
        Map<String, Object> map = new HashMap<>();
        map.put("company", UserUtils.getUser().getCompany());
        
        switch (raw.getInoutType())
        {
            // 原料采购入库
            case 1:
                map.put("rawPoDetailId", raw.getRecordId());
                mc = materialCheckDao.getMaterialCheckBySourceId(map);
                if (null != mc && (null != mc.getAdjustFlag() || null != mc.getAdjustCheckFlag()))
                {
                    result = "原料采购入库记录对应的付款对账明细已经转月份，不能冲红这条出入库记录!";
                }
                break;
            // 原料入库
            case 2:
                map.put("rawStockId", raw.getRecordId());
                mc = materialCheckDao.getMaterialCheckBySourceId(map);
                if (null != mc && (null != mc.getAdjustFlag() || null != mc.getAdjustCheckFlag()))
                {
                    result = "原料入库记录对应的付款对账明细已经转月份，不能冲红这条出入库记录!";
                }
                break;
            // 原料采购退货出库
            case 4:
                map.put("prPoDetailOutId", raw.getReturnsNo());
                mc = materialCheckDao.getMaterialCheckBySourceId(map);
                if (null != mc && (null != mc.getAdjustFlag() || null != mc.getAdjustCheckFlag()))
                {
                    result = "原料采购退货出库记录对应的付款对账明细已经转月份，不能冲红这条出入库记录!";
                }
                break;
            // 原料出库
            case 6:
                map.put("rawStockOutId", raw.getRecordId());
                GoodsCheck gc = goodsCheckDao.getGoodsCheckBySourceId(map);
                if (null == gc)
                {
                    result = "原料出库记录对应的收款对账明细已经分月调整，不能冲红这条出入库记录!";
                }
                else
                {
                    if (null != gc.getAdjustFlag() || null != gc.getAdjustCheckFlag())
                    {
                        result = "原料出库记录对应的收款对账明细已经转月份，不能冲红这条出入库记录!";
                    }
                }
                break;
            // 原料采购补货入库
            case 7:
                map.put("prPoDetailInId", raw.getReturnsNo());
                mc = materialCheckDao.getMaterialCheckBySourceId(map);
                if (null != mc && (null != mc.getAdjustFlag() || null != mc.getAdjustCheckFlag()))
                {
                    result = "原料采购补货入库记录对应的付款对账明细已经转月份，不能冲红这条出入库记录!";
                }
                break;
        }
        return result;
    }
    
    /** zjn 2018-12-18 成品出入库记录冲红验证对账单明细是否转月份、是否分页对账 */
    public static String prdStockValidator(ProductStore productStore)
    {
        String result = "success";
        MaterialCheck mc = new MaterialCheck();
        
        Map<String, Object> map = new HashMap<>();
        map.put("company", UserUtils.getUser().getCompany());
        
        switch (productStore.getInoutType())
        {
            // 客诉退货入库
            case 13:
                map.put("returncontractDetailId", productStore.getRecordId());
                GoodsCheck gc = goodsCheckDao.getGoodsCheckBySourceId(map);
                if (null == gc)
                {
                    result = "退货入库记录对应的收款对账明细已经分月调整，不能冲红这条出入库记录!";
                }
                if (null != gc.getAdjustFlag() || null != gc.getAdjustCheckFlag())
                {
                    result = "退货入库记录对应的收款对账明细已经转月份，不能冲红这条出入库记录!";
                }
                break;
            // 成品采购入库
            case 14:
                map.put("prdorderDetailId", productStore.getRecordId());
                mc = materialCheckDao.getMaterialCheckBySourceId(map);
                if (null != mc.getAdjustFlag() || null != mc.getAdjustCheckFlag())
                {
                    result = "成品采购入库记录对应的付款对账明细已经转月份，不能冲红这条出入库记录!";
                }
                break;
            // 成品采购补货入库
            case 15:
                map.put("prPdDeteilInId", productStore.getDeliveNo());
                mc = materialCheckDao.getMaterialCheckBySourceId(map);
                if (null != mc.getAdjustFlag() || null != mc.getAdjustCheckFlag())
                {
                    result = "成品采购补货入库记录对应的付款对账明细已经转月份，不能冲红这条出入库记录!";
                }
                break;
            // 成品采购退货出库
            case 16:
                map.put("prPdDeteilOutId", productStore.getDeliveNo());
                mc = materialCheckDao.getMaterialCheckBySourceId(map);
                if (null != mc.getAdjustFlag() || null != mc.getAdjustCheckFlag())
                {
                    result = "成品采购退货出库记录对应的付款对账明细已经转月份，不能冲红这条出入库记录!";
                }
                break;
        }
        return result;
    }
    
    /**
     * 2016-08-24 wc 成品采购入库面积计算
     * 
     * @param prdorderDetail成品采购订单详情
     * @param inCaigoQutity 采购入库数量
     * @return
     */
    public static BigDecimal getCaigoInArea(PrdorderDetail prdorderDetail, BigDecimal inCaigoQutity)
    {
        BigDecimal area = BigDecimal.ZERO;
        ProductCraft craft = prdorderDetail.getContractCraft();
        if (craft != null)
        {
            if (craft.getPnlLength() != null && craft.getPnlLength().compareTo(BigDecimal.ZERO) == 1
                && craft.getPnlWidth() != null && craft.getPnlWidth().compareTo(BigDecimal.ZERO) == 1
                && craft.getPnlDivisor() != null)
            {
                BigDecimal pnlLength = craft.getPnlLength();
                BigDecimal pnlWidth = craft.getPnlWidth();
                BigDecimal pnlDivisor = craft.getPnlDivisor();
                area = pnlLength.multiply(pnlWidth)
                    .multiply(inCaigoQutity)
                    .divide(pnlDivisor, 4, BigDecimal.ROUND_HALF_UP)
                    .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
            }
            else if (craft.getUnitLength() != null && craft.getUnitWidth() != null)
            {
                BigDecimal unitLength = craft.getUnitLength();
                BigDecimal unitWidth = craft.getUnitWidth();
                area = unitLength.multiply(unitWidth)
                    .multiply(inCaigoQutity)
                    .divide(new BigDecimal(1000000), 4, BigDecimal.ROUND_HALF_UP);
            }
        }
        return area;
    }
}
