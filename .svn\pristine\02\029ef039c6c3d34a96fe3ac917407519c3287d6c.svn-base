---
---
{% include license-code.less %}

.icon-large {
  font-size: (4em/3);
  margin-top: -4px;
  padding-top: 3px;
  margin-bottom: -4px;
  padding-bottom: 3px;
  vertical-align: middle;
}

.nav {
  [class^="icon-"],
  [class*=" icon-"] {
    vertical-align: inherit;
    margin-top: -4px;
    padding-top: 3px;
    margin-bottom: -4px;
    padding-bottom: 3px;
    &.icon-large {
      vertical-align: -25%;
    }
  }
}

.nav-pills, .nav-tabs {
  [class^="icon-"],
  [class*=" icon-"] {
    &.icon-large {
      line-height: .75em;
      margin-top: -7px;
      padding-top: 5px;
      margin-bottom: -5px;
      padding-bottom: 4px;
    }
  }
}

.btn {
  [class^="icon-"],
  [class*=" icon-"] {
    &.pull-left, &.pull-right { vertical-align: inherit; }
    &.icon-large {
      margin-top: -.5em;
    }
  }
}

a [class^="icon-"],
a [class*=" icon-"] {
  cursor: pointer;
}

@mixin ie7icon($inner) { *zoom: expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '#{$inner}'); }

{% for icon in icons %}
.icon-{{ icon.id }} {
  @include ie7icon('&#x{{ icon.unicode }};');
}
{% for alias in icon.aliases %}
.icon-{{ alias }} {
  @include ie7icon('&#x{{ icon.unicode }};');
}
{% endfor %}
{% endfor %}
