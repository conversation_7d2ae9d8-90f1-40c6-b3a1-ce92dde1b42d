/**
 *
 */
package com.kyb.pcberp.modules.purch.dao;

import java.math.BigDecimal;
import java.util.List;

import com.kyb.pcberp.modules.crm.entity.Customer;
import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.crm.entity.ComplaintFile;
import com.kyb.pcberp.modules.finance.entity.PayMoney;
import com.kyb.pcberp.modules.finance.entity.SinglePayable;
import com.kyb.pcberp.modules.purch.entity.AccountsPayable;
import com.kyb.pcberp.modules.purch.entity.MaterialCheck;
import com.kyb.pcberp.modules.sys.entity.User;

/**
 * 付款对账单DAO接口
 * 
 * <AUTHOR>
 * @version 2015-09-30
 */
@MyBatisDao
public interface AccountsPayableDao extends CrudDao<AccountsPayable>
{
    
    List<AccountsPayable> findListOrderByPeriod(AccountsPayable ac);
    
    /**
     * 原料采购入库时，根据供应商、period查找
     * 
     * @param ap
     * @return
     */
    List<AccountsPayable> findAccountsPayableListByBean(AccountsPayable ap);
    
    void updateAccountsPayable(AccountsPayable ar);
    
    void updateApPaiedAmount(AccountsPayable ap);
    
    /** 查询其对账单日期List */
    List<Integer> getSeeWriteOffPeriodList(PayMoney payMoney);
    
    void updateStatus(AccountsPayable ap);
    
    /** zjn 2018-04-17 根据供应商名称查询付款对账单 */
    AccountsPayable getAccountsPayableBySupplierName(AccountsPayable ap);
    
    /** zjn 2018-12-06 根据供应商和对账月份获取付款对账单(收到、退货金额) */
    AccountsPayable getAccountsPayableAmount(MaterialCheck materialCheck);
    
    /** zjn 2018-12-06 更新收款对账单,收到、退货金额 */
    void updateAccountsPayableAmount(AccountsPayable ap);
    
    /** zjn 2019-01-16 获取付款对账单导出数据 */
    List<AccountsPayable> exportAccountsPayableData(AccountsPayable ap);
    
    /** zjn 2019-01-22 获取付款对账单某月调整金额 */
    BigDecimal getPayableAdjustAmount(User user);
    
    /** tj 2019-03-04 查询付款对账单金额 */
    AccountsPayable findCost(AccountsPayable ap);
    
    /** tj 2019-02-28 修改送货/退货金额 */
    void updateCost(AccountsPayable ap);
    
    /** lq 2019-04-22 上传发票附件 **/
    void uploadFile(ComplaintFile complaintFile);
    
    /** lq 2019-04-22 删除发票附件 **/
    void clearUpload(ComplaintFile complaintFile);
    
    /** lq 2019-04-22 获取附件 **/
    List<ComplaintFile> getFile(AccountsPayable accountsPayable);
    
    /** lq 2019-04-22 查询是否有附件的id **/
    List<String> selectFileFlag(@Param("accountsPayableList") List<AccountsPayable> accountsPayableList);
    
    AccountsPayable getPayableAmount(MaterialCheck materialCheck);
    
    void updateAmountData(AccountsPayable accountsPayable);
    
    void updateStatusTwo(AccountsPayable accountsPayable);
    
    void updatePayableAmount(AccountsPayable accountsPayable);
    
    Integer getPayableCountTwo(MaterialCheck materialCheck);
    
    void physicalDeletion(MaterialCheck materialCheck);
    
    void batchInsertPayable(MaterialCheck materialCheck);
    
    Integer checkPayableStatus(AccountsPayable accountsPayable);
    
    AccountsPayable getAccountsPayableById(AccountsPayable accountsPayable);
    
    void updateOpenStatus(@Param("supplierId") String customerId, @Param("period") String period);
    
    AccountsPayable getAccountsPayableBySupplier(AccountsPayable accountsPayable);
    
    void updateConfirmData(AccountsPayable accountsPayable);
    
    AccountsPayable getAccountsPayableData(SinglePayable singlePayable);
    
    void updatePaiedAmount(AccountsPayable accountsPayable);
    
    void updateByMaterialCheck(MaterialCheck materialCheck);
    
    void updateConcatData(MaterialCheck materialCheck);

    void updateCheckData(AccountsPayable accountsPayable);

    void updateCheckOutAmount(AccountsPayable accountsPayable);

    Integer getMinPeriod(Customer customer);

    void updateStatusData(AccountsPayable accountsPayable);

    void updateDetailStatusData(AccountsPayable accountsPayable);

    AccountsPayable getAccountsPayable(AccountsPayable accountsPayable);

    void updateAdjustValue(AccountsPayable accountsPayable);

    void updateAccountPayStatus(AccountsPayable accountsPayable);

    String getMoneyCompanyId(@Param("supplierId") String supplierId);

    List<AccountsPayable> getPaymentReconciledTotalList(AccountsPayable accountsPayable);

    List<AccountsPayable> getUnpaidPayableTotalList(AccountsPayable accountsPayable);

    List<AccountsPayable> getPaymentReconciliationList(AccountsPayable accountsPayable);

    List<AccountsPayable> getAmountPayableList(AccountsPayable accountsPayable);

    //明细
    List<AccountsPayable> getPaymentReconciledDetailList(AccountsPayable accountsPayable);

    List<AccountsPayable> getUnpaidPayableDetailList(AccountsPayable accountsPayable);

    List<AccountsPayable> getPaymentReconciliationDetailList(AccountsPayable accountsPayable);

    List<AccountsPayable> getAmountPayableDetailList(AccountsPayable accountsPayable);

    void updateStatusTwoData(AccountsPayable accountsPayable);

    void updateOtherAmountData(SinglePayable singlePayable);
}