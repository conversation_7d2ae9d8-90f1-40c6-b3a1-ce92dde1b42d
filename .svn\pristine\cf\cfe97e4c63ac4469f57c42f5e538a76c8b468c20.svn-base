package com.kyb.pcberp.modules.production.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.production.entity.ProduceRecord;

public class ScanWorkReportVo extends ProduceRecord
{

    /**
     * 
     */
    private static final long serialVersionUID = 4671203654240008914L;

    @ExcelField(title = "厂编", align = 2, sort = 10)
    public String getCraftNoEXP()
    {
        return super.getCraftNoEXP();
    }
    
    @ExcelField(title = "PCS", align = 2, sort = 20)
    public Integer getTakeOverQtyPcsT()
    {
        return super.getTakeOverQtyPcsT();
    }
    
    @ExcelField(title = "报废", align = 2, sort = 30)
    public Integer getHandOverQtyPcsTEXP()
    {
        return super.getHandOverQtyPcsTEXP();
    }
    
    @ExcelField(title = "接板时间", align = 2, sort = 40)
    @JsonFormat(pattern = "MM-dd HH:mm")
    public Date getTakeOverTimeStr()
    {
        return super.getTakeOverTime();
    }
    
    @ExcelField(title = "交板时间", align = 2, sort = 50)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getHandOverTimeStr()
    {
        return super.getHandOverTime();
    }

}
