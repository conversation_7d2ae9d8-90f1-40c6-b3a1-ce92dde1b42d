<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.crm.dao.AccountsReceivableDao">
    
    <resultMap id="baseResultMap" type="AccountsReceivable">
		<id property="recordId" column="recordId" />
		<result property="period" column="period" />
		<result property="adjustValue" column="adjustValue" />
		<result property="receivedAmount" column="receivedAmount" />
		<result property="sentGoodsValue" column="sentGoodsValue" />
		<result property="returnGoodsValue" column="returnGoodsValue" />
		<result property="completeFlag" column="completeFlag" />
		<result property="createdBy.recordId" column="createdBy" />
		<result property="createdDate" column="createdDate" />
		<result property="lastUpdBy.recordId" column="lastUpdBy" />
		<result property="lastUpdDate" column="lastUpdDate" />
		<result property="remark" column="remark" />
		<result property="status" column="status" />
		<result property="adjustFlag" column="adjustFlag" />
		<result property="checkCause" column="checkCause"/>
		<result property="jxAmount" column="jxAmount"/>
		<result property="otherAmount" column="otherAmount"/>
		<result property="concatStatus" column="concatStatus"/>
		<result property="excelPath" column="excelPath"/>
		<result property="proofPath" column="proofPath"/>
		<result property="receivableStatus" column="receivableStatus"/>
		<result property="counterTrialFlag" column="counterTrialFlag"/>
		<result property="confirmUserId" column="confirmUserId"/>
		<result property="confirmDate" column="confirmDate"/>
		<result property="checkUserId" column="checkUserId"/>
		<result property="checkDate" column="checkDate"/>
		<result property="confirmUserName" column="confirmUserName"/>
		<result property="checkUserName" column="checkUserName"/>
		<result property="collectionApplyFlag" column="collectionApplyFlag"/>
		<result property="supplyChainFlag" column="supplyChainFlag"/>
		<result property="expPeriod" column="expPeriod"/>
		<result property="checkOutAmount" column="checkOutAmount"/>
		<result property="groupOrgId" column="groupOrgId"/>
		<result property="groupOrgName" column="groupOrgName"/>
		<association property="customer" column="customerId" javaType="Customer">
			<result property="recordId" column="customer.recordId"/>
			<result property="no" column="customer.no"/>
			<result property="shortName" column="customer.shortName"/>
			<result property="checkDate" column="customer.checkDate"/>
		</association>
		<association property="createdBy" column="createdBy" javaType="User">
			<result property="recordId" column="createdBy.recordId"/>
			<result property="userName" column="createdBy.userName"/>
		</association>
		<association property="company" column="company" javaType="Company">
			<result property="recordId" column="company.recordId"/>
		</association>
	</resultMap>
	
	<sql id="accountsReceivableColumns">
		a.recordId,
		a.companyId AS "company.recordId",
		a.customerId AS "customer.recordId",
		a.period,
		a.adjustValue,
		a.receivedAmount,
		a.sentGoodsValue,
		a.returnGoodsValue,
		a.completeFlag,
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.activeFlag,
		a.remark,
		a.status,
		a.adjustFlag,
		a.checkCause,
		a.excelPath,
		a.proofPath,
		a.receivableStatus,
		a.checkOutAmount
	</sql>
	
	<sql id="accountsReceivableJoins">
	LEFT Join md_customer mc on mc.recordId = a.customerId
	</sql>
    
    <select id="getOverdueCollection" resultType="AccountsReceivable">
    	SELECT 
			a.receivedAmount as 'receivedAmount',
			a.sentGoodsValue as 'sentGoodsValue',
			a.adjustValue as 'adjustValue',
			a.returnGoodsValue as 'returnGoodsValue'
		from sl_accounts_receivable a 
	    <where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL} AND a.period <![CDATA[<]]> #{filterType} and completeFlag <![CDATA[<>]]> #{DEL_FLAG_NORMAL};
		</where>
    </select>
    
    <!-- 查询客户的所有没有结清的对账单 -->
    <select id = "getMoney" resultType="AccountsReceivable">
    	select <include refid="accountsReceivableColumns"/>
    	 from sl_accounts_receivable a where a.companyId =#{company.recordId} and a.completeFlag = 0  and a.customerId = #{recordId} and a.period !=#{selectdate};
    </select>
    
    
    
    	<!-- 根据客户编号去查询对账单 -->
	<select id="getReceivableByCus" resultType="AccountsReceivable">
		select 	<include refid="accountsReceivableColumns"/>
		 from sl_accounts_receivable a where a.customerId = #{recordId} and a.period =#{selectdate};
	</select>
    
	<select id="get" resultType="AccountsReceivable">
		SELECT 
			<include refid="accountsReceivableColumns"/>,
			mc.shortName AS "customer.shortName",
			mc.no AS "customer.no"
		FROM sl_accounts_receivable a
		<include refid="accountsReceivableJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="findAccountsReceivableListByBeanOrderByPeriod" resultType="AccountsReceivable">
		SELECT 
			<include refid="accountsReceivableColumns"/>
		FROM sl_accounts_receivable a
		WHERE a.customerId = #{customer.recordId}
		and a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
		<if test="completeFlag != null">
				AND a.completeFlag =#{completeFlag}
		</if>
		<if test="period != null">
				AND a.period =#{period}
		</if>
		<if test="adjustFlag != null and adjustFlag == true" >
				AND a.adjustFlag =#{adjustFlag}
		</if>
	</select>
	
	<select id="findList" resultMap="baseResultMap">
		SELECT 
			<include refid="accountsReceivableColumns"/>,
			mc.no AS "customer.no",
			mc.shortName AS "customer.shortName",
			mc.checkDate AS "customer.checkDate",
			su.userName AS "createdBy.userName",
			aa.jxAmount AS "jxAmount",
			aa.otherAmount AS "otherAmount",
			a.concatStatus,
			CASE WHEN
			(
				SELECT 
					COUNT(1) 
				FROM sl_accounts_receivable 
				WHERE customerId = a.customerId AND period > a.period 
				AND activeFlag = 1 AND `status` = 2003
			) > 0 THEN 0 ELSE 1 END AS "counterTrialFlag",
			a.confirmUserId,
			a.confirmDate,
			a.checkUserId,
			a.checkDate,
			confirmUser.userName AS "confirmUserName",
			checkUser.userName AS "checkUserName",
			CASE WHEN bb.status IS NULL OR bb.status = 2001 THEN 1 ELSE 0 END AS "collectionApplyFlag",
			CASE WHEN cc.count > 0 THEN 1 ELSE 0 END AS "supplyChainFlag",
			a.period AS "expPeriod",
			k.groupOrgId,
			l.`name` AS "groupOrgName"
		FROM sl_accounts_receivable a
		<include refid="accountsReceivableJoins"/>
		LEFT JOIN sl_goods_check b ON b.customerId = a.customerId AND b.companyId = a.companyId AND b.activeFlag = 1 AND b.period = a.period
		LEFT JOIN sl_delivery_detail c ON c.recordId = b.sourceId AND c.companyId = a.companyId AND c.activeFlag = 1 AND b.inOutFlag = 1
		LEFT JOIN sl_contract_detail d ON d.recordId = c.contractDetailId AND d.companyId = a.companyId AND d.activeFlag = 1
		LEFT JOIN sl_contract e ON e.recordId = d.contractId AND e.companyId = a.companyId AND e.activeFlag = 1
		LEFT JOIN md_dict_value f ON f.recordId = e.taxDescript AND f.companyId = a.companyId AND f.activeFlag = 1
		LEFT JOIN sl_delivery delivery ON delivery.recordId = c.deliveryId AND delivery.companyId = a.companyId AND delivery.activeFlag = 1
		LEFT JOIN st_product_store g ON g.recordId = b.sourceId AND g.companyId = a.companyId AND g.activeFlag = 1 AND b.inOutFlag = 0
		LEFT JOIN sl_contract_detail h ON h.recordId = g.contractDetailId AND h.companyId = a.companyId AND h.activeFlag = 1
		LEFT JOIN sl_contract i ON i.recordId = h.contractId AND i.companyId = a.companyId AND i.activeFlag = 1
		LEFT JOIN md_dict_value j ON j.recordId = i.taxDescript AND j.companyId = a.companyId AND j.activeFlag = 1
		
		LEFT JOIN sm_user su on su.recordId = a.createdBy AND su.companyId = a.companyId AND su.activeFlag = 1
		LEFT JOIN sm_user salesMan ON salesMan.recordId = mc.salesman AND salesMan.companyId = a.companyId AND salesMan.activeFlag = 1
		LEFT JOIN sm_user merchandiser1 ON merchandiser1.recordId = e.createdBy AND merchandiser1.companyId = a.companyId AND merchandiser1.activeFlag = 1
		LEFT JOIN sm_user merchandiser2 ON merchandiser2.recordId = i.createdBy AND merchandiser2.companyId = a.companyId AND merchandiser2.activeFlag = 1
		
		LEFT JOIN sm_user confirmUser ON confirmUser.recordId = a.confirmUserId
		LEFT JOIN sm_user checkUser ON checkUser.recordId = a.checkUserId
		
		LEFT JOIN
		(
			SELECT
				gc.customerId AS "customerId",
				gc.period AS "period",
				SUM(CASE WHEN jxscd.recordId IS NOT NULL THEN (CASE WHEN ISNULL(gc.inOutFlag) OR gc.inOutFlag = 0 THEN -gc.amount ELSE gc.amount END) ELSE 0 END ) AS "jxAmount",
				SUM(CASE WHEN jxscd.recordId IS NULL THEN (CASE WHEN ISNULL(gc.inOutFlag) OR gc.inOutFlag = 0 THEN -gc.amount ELSE gc.amount END) ELSE 0 END ) AS "otherAmount",
				SUM(IFNULL(gc.originalAmount,0)) AS "originalAmount"
			FROM sl_goods_check gc
			LEFT JOIN sl_delivery_detail b ON b.recordId = gc.sourceId AND gc.inOutFlag = 1
			LEFT JOIN sl_contract_detail scd1 ON scd1.recordId = b.contractDetailId 
			LEFT JOIN st_product_store c ON c.recordId = gc.sourceId AND c.inOutType = 13 AND gc.inOutFlag = 0 AND gc.inOutType = 1
			LEFT JOIN sl_contract_detail scd2 ON scd2.recordId = c.contractDetailId
			LEFT JOIN st_product_store d ON d.recordId = gc.sourceId AND gc.inOutFlag = 1 AND d.complaintId IS NOT NULL AND d.inOutType = 12
			LEFT JOIN sl_contract_detail scd3 ON scd3.recordId = d.contractDetailId
			LEFT JOIN icloud_group_center igc ON igc.recordId = IFNULL(IFNULL(scd1.groupCenterId,scd2.groupCenterId),scd3.groupCenterId)
			LEFT JOIN sl_contract_detail jxscd ON jxscd.groupCenterId = igc.recordId AND jxscd.companyId = #{factoryComId} AND jxscd.activeFlag = 1
			AND jxscd.`status` IN (200201, 200204, 200207)
			LEFT JOIN md_customer e ON e.recordId = gc.customerId
			WHERE gc.companyId = #{company.recordId} AND gc.activeFlag = 1 
			AND (gc.adjustCheckFlag <![CDATA[<>]]> 12 OR ISNULL(gc.adjustCheckFlag))
			AND (ISNULL(gc.adjustFlag) OR gc.adjustFlag = 1)
			<!-- 客户 -->
			<if test="customer != null">
				<if test="customer.no != null and customer.no != ''">
					AND REPLACE(e.no," ","") LIKE CONCAT('%', REPLACE(#{customer.no}," ",""), '%')
				</if>
				<if test="customer.shortName != null and customer.shortName != ''">
					AND REPLACE(e.shortName," ","") LIKE CONCAT('%', REPLACE(#{customer.shortName}," ",""), '%')
				</if>
			</if>
			<!-- 年-->
			<if test="dateType == 1">
				<if test="period != null and filterType == null">
					AND LEFT(gc.period,4) LIKE CONCAT('%',#{period},'%')
				</if>
				<if test="filterType != null and filterType == 1 and period != null">
					AND LEFT(gc.period,4) LIKE CONCAT('%',#{period}, '%') AND gc.completeFlag = 0
				</if>
				<if test="filterType != null and filterType == 2 and period != null">
					AND LEFT(gc.period,4) LIKE CONCAT('%',#{period}, '%') AND gc.completeFlag = 0
				</if>
			</if>			
			<!-- 月 -->
			<if test="dateType == 2">
				<if test="period != null and filterType == null">
					AND RIGHT(gc.period,2) LIKE CONCAT('%',#{period}, '%')
				</if>
				<if test="filterType != null and filterType == 1 and period != null">
					AND RIGHT(gc.period,2) LIKE CONCAT('%',#{period}, '%') AND gc.completeFlag = 0
				</if>
				<if test="filterType != null and filterType == 2 and period != null">
					AND RIGHT(gc.period,2) LIKE CONCAT('%',#{period}, '%') AND gc.completeFlag = 0
				</if>
			</if>
			<!-- 年月 -->
			<if test="dateType == 3">
				<if test="period != null and filterType == null">
					AND gc.period =#{period}
				</if>
				<if test="filterType != null and filterType == 1 and period != null">
					AND gc.period =#{period} AND gc.completeFlag = 0
				</if>
				<if test="filterType != null and filterType == 2 and period != null">
					AND gc.period != #{period} AND gc.completeFlag = 0
				</if>
			</if>
			GROUP BY gc.customerId,gc.period
		) aa ON aa.customerId = a.customerId AND aa.period = a.period
		LEFT JOIN 
		(
			SELECT
				recordId AS "recordId",
				customerId AS "customerId",
				period AS "period",
				status AS "status"
			FROM sl_single_receivable
			WHERE companyId = #{company.recordId} AND activeFlag = 1
		) bb ON bb.customerId = a.customerId AND bb.period = a.period
		LEFT JOIN 
		(
			SELECT 
				customerId AS "customerId",
				period AS "period",
				COUNT(1) AS "count"
			FROM sl_goods_check 
			WHERE companyId = #{company.recordId} AND activeFlag = 1 
			AND type = 2
			GROUP BY customerId,period
		) cc ON cc.customerId = a.customerId AND cc.period = a.period
		LEFT JOIN icloud_group_org_relation k ON k.deptId = e.deptId AND k.activeFlag = 1
		LEFT JOIN icloud_group_org l ON l.recordId = k.groupOrgId
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = 1 AND a.adjustFlag = #{adjustFlag}
			AND (a.sentGoodsValue > 0 OR a.returnGoodsValue > 0 OR a.adjustValue > 0 OR aa.originalAmount > 0)
			<!-- 客户 -->
			<if test="customer != null">
				<if test="customer.no != null and customer.no != ''">
					AND REPLACE(mc.no," ","") LIKE CONCAT('%', REPLACE(#{customer.no}," ",""), '%')
				</if>
				<if test="customer.shortName != null and customer.shortName != ''">
					AND REPLACE(mc.shortName," ","") LIKE CONCAT('%', REPLACE(#{customer.shortName}," ",""), '%')
				</if>
			</if>
			<!-- 年-->
			<if test="dateType == 1">
				<if test="period != null and filterType == null">
					AND LEFT(a.period,4) LIKE CONCAT('%',#{period},'%')
				</if>
				<if test="filterType != null and filterType == 1 and period != null">
					AND LEFT(a.period,4) LIKE CONCAT('%',#{period}, '%') AND a.completeFlag = 0
				</if>
				<if test="filterType != null and filterType == 2 and period != null">
					AND LEFT(a.period,4) LIKE CONCAT('%',#{period}, '%') AND a.completeFlag = 0
				</if>
			</if>			
			<!-- 月 -->
			<if test="dateType == 2">
				<if test="period != null and filterType == null">
					AND RIGHT(a.period,2) LIKE CONCAT('%',#{period}, '%')
				</if>
				<if test="filterType != null and filterType == 1 and period != null">
					AND RIGHT(a.period,2) LIKE CONCAT('%',#{period}, '%') AND a.completeFlag = 0
				</if>
				<if test="filterType != null and filterType == 2 and period != null">
					AND RIGHT(a.period,2) LIKE CONCAT('%',#{period}, '%') AND a.completeFlag = 0
				</if>
			</if>
			<!-- 年月 -->
			<if test="dateType == 3">
				<if test="period != null and filterType == null">
					AND a.period =#{period}
				</if>
				<if test="filterType != null and filterType == 1 and period != null">
					AND a.period =#{period} AND a.completeFlag = 0
				</if>
				<if test="filterType != null and filterType == 2 and period != null">
					AND a.period != #{period} AND a.completeFlag = 0
				</if>
			</if>
			<if test="filterType != null and filterType == 2003">
				AND a.status = 2003 AND a.completeFlag = 0
			</if>
			<if test="filterType != null and filterType == 4">
				AND a.completeFlag = 1
			</if>
			<!-- zjn 2018-07-03 含税状态 -->
			<if test="taxName != null and taxName != '' and taxDescriptId != null and taxDescriptId != ''">
				<if test="taxName == 1">
					AND IFNULL(f.recordId,j.recordId) <![CDATA[<>]]> #{taxDescriptId}
				</if>
				<if test="taxName == 2">
					AND IFNULL(f.recordId,j.recordId) <![CDATA[=]]> #{taxDescriptId}
				</if>
			</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (mc.salesman = #{createdBy.recordId}
				 or a.customerId in
				(SELECT	cs.customerId FROM sm_customer_salesAssistant cs WHERE cs.activeFlag = 1 AND cs.userId = #{createdBy.recordId}))
			</if>
			<!-- 状态 -->
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<!-- 业务员名称 -->
			<if test="salesManName != null and salesManName != ''">
				AND REPLACE(salesMan.userName," ","") LIKE CONCAT('%',REPLACE(#{salesManName}," ",""), '%')
			</if>
			<!-- 跟单员名称 -->
			<if test="merchandiserName != null and merchandiserName != ''">
				AND REPLACE(IFNULL(merchandiser1.userName,merchandiser2.userName)," ","") LIKE CONCAT('%', REPLACE(#{merchandiserName}," ",""), '%')
			</if>
			<if test="deliveryQuery != null and deliveryQuery != ''">
				AND REPLACE(CONCAT(IFNULL(delivery.no,""),IFNULL(IFNULL(e.no,i.no),"") )," ","") LIKE CONCAT('%',REPLACE(#{deliveryQuery}," ",""), '%')
			</if>
			<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
				AND a.createdDate >= #{sentTimeStartQr}
			</if>
			<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
				AND a.createdDate <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
			<if test="unStatus != null and unStatus != ''">
				AND a.status <![CDATA[<>]]> #{unStatus} AND a.status <![CDATA[<>]]> 60001
			</if>
			<if test="unCustomerId != null and unCustomerId != ''">
				AND a.customerId <![CDATA[<>]]> #{unCustomerId} 
			</if>
			GROUP BY a.recordId
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findListOrderByPeriod" resultMap="baseResultMap">
		SELECT 
			<include refid="accountsReceivableColumns"/>,
			mc.no as "customer.no",
			mc.shortName as "customer.shortName"
		FROM sl_accounts_receivable a
		<include refid="accountsReceivableJoins"/>
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
			<if test="customer != null and customer.recordId != null and customer.recordId != ''">
				AND a.customerId = #{customer.recordId}
			</if>
			<if test="period != null and period != ''">
				AND a.period = #{period}
			</if>
		</where>
	</select>
	
	<select id="findAllList" resultType="AccountsReceivable">
		SELECT 
			<include refid="accountsReceivableColumns"/>
		FROM sl_accounts_receivable a
		<include refid="accountsReceivableJoins"/>
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO sl_accounts_receivable(
			companyId,
			customerId,
			period,
			adjustValue,
			receivedAmount,
			sentGoodsValue,
			returnGoodsValue,
			completeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			activeFlag,
			remark,
			status,
			adjustFlag,
			checkCause
		) VALUES (
			#{company.recordId},
			#{customer.recordId},
			#{period},
			#{adjustValue},
			#{receivedAmount},
			#{sentGoodsValue},
			#{returnGoodsValue},
			#{completeFlag},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{DEL_FLAG_NORMAL},
			#{remark},
			IFNULL(#{status},2001),
			#{adjustFlag},
			#{checkCause}
		)
	</insert>
	
	<!-- 修改应收账款记录 -->
	<update id="updateAccountsReceivable">
		update sl_accounts_receivable set
		<if test="customer != null and customer.recordId != null and customer.recordId != ''">
				customerId = #{customer.recordId},
		</if>
		<if test="period != null and period != ''">
				period = #{period},
		</if>
		<if test="adjustValue != null">
				adjustValue = #{adjustValue},
		</if>
		<if test="receivedAmount != null">
				receivedAmount = #{receivedAmount},
		</if>
		<if test="sentGoodsValue != null">
				sentGoodsValue = #{sentGoodsValue},
		</if>
		<if test="returnGoodsValue != null">
				returnGoodsValue = #{returnGoodsValue},
		</if>
		<!-- 由于这个判断不能更新状态，其为整型所以添加了completeFlag >= 0 2016.10.26 ojh-->
		<if test="completeFlag != null and completeFlag >= 0">
				completeFlag = #{completeFlag},
		</if>
		<if test="lastUpdBy != null and lastUpdBy.recordId != null and lastUpdBy.recordId != ''">
				lastUpdBy = #{lastUpdBy.recordId},
		</if>
		<if test="lastUpdDate != null and lastUpdDate != ''">
				lastUpdDate = #{lastUpdDate},
		</if>
		<if test="adjustFlag != null and adjustFlag != ''">
				adjustFlag = #{adjustFlag},
		</if>
		activeFlag=#{DEL_FLAG_NORMAL} where recordId = #{recordId}
	</update>
	
	<update id="update">
		UPDATE sl_accounts_receivable SET 	
			customerId = #{customer.recordId},
			period = #{period},
			adjustValue = #{adjustValue},
			receivedAmount = #{receivedAmount},
			sentGoodsValue = #{sentGoodsValue},
			returnGoodsValue = #{returnGoodsValue},
			completeFlag = #{completeFlag},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			remark = #{remark},
			status = #{status}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE sl_accounts_receivable SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>
	
	<!-- 通过公司、客户、周期来查找收款对账单 -->
	<select id="findAccountsReceivableListByBean" resultMap="baseResultMap" parameterType="AccountsReceivable">
		SELECT 
			<include refid="accountsReceivableColumns"/>,
			mc.no as "customer.no",
			mc.shortName as "customer.shortName"
		FROM sl_accounts_receivable a
		<include refid="accountsReceivableJoins"/>
		<where>
		    a.companyId=#{company.recordId}  AND a.activeFlag = #{DEL_FLAG_NORMAL}
		    <if test="customer != null and customer.recordId != '' and customer.recordId != null">
					and a.customerId = #{customer.recordId}
			</if>
			<if test="period != null and period != ''">
					and a.period = #{period}
			</if>
			<if test="completeFlag != null and completeFlag != ''">
					and a.completeFlag = #{completeFlag}
			</if>
		</where>
	</select>
	
	<!-- 获取对账日期List -->
	<select id="getSeeWriteOffPeriodList" resultType="java.lang.Integer">
	    select a.period from sl_accounts_receivable a where a.companyId = #{company.recordId}
	     and a.customerId = #{customer.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
	</select>
	
	<!-- zjn 2018-04-08-->
	<update id="updateStatus">
		UPDATE sl_accounts_receivable SET 
			status = #{status},
			adjustFlag = #{adjustFlag},
			checkCause = #{checkCause}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateStatusTwo">
		UPDATE sl_accounts_receivable 
			<set>
				status = #{status},
				<if test="counterTrialCause != null and counterTrialCause != ''">
					counterTrialCause = #{counterTrialCause},
				</if>
			</set>
		WHERE recordId = #{recordId}
	</update>
	
	<!-- zjn 2018-04-17 根据客户名称查询收款对账单 -->
	<select id="getAccountsReceivableByCustomerName" resultType="AccountsReceivable">
		SELECT 
			<include refid="accountsReceivableColumns"/>,
			b.no as "customer.no",
			b.name as "customer.name"
		FROM sl_accounts_receivable a
		LEFT JOIN md_customer b ON a.customerId = b.recordId
		WHERE a.companyId = #{customer.company.recordId} AND b.name = #{customer.name} 
		AND a.activeFlag = #{DEL_FLAG_NORMAL} AND a.period = #{period}
		limit 0,1
	</select>
	
	<!-- zjn 2018-12-06 根据供应商和对账月份获取收款对账单(送货、退货金额) -->
	<select id="getAccountsReceivableAmount" resultType="AccountsReceivable">
		select
			recordId,
			sentGoodsValue,
			returnGoodsValue
		from sl_accounts_receivable
		where companyId = #{company.recordId} AND activeFlag = #{DEL_FLAG_NORMAL}
		AND customerId= #{customer.recordId} AND period = #{period}
	</select>
	
	<!-- zjn 2018-12-06 更新收款对账单,送货、退货金额-->
	<update id="updateAccountsReceivableAmount">
		update sl_accounts_receivable set
			sentGoodsValue = #{sentGoodsValue},
			returnGoodsValue = #{returnGoodsValue}
		where recordId = #{recordId}
	</update>
	
	<!-- zjn 2019-01-17 获取收款对账单导出数据 -->
	<select id="exportAccountsReceivableData" resultType="AccountsReceivable">
		SELECT 
			a.recordId,
			a.period,
			a.`status`,
			a.completeFlag,
			mc.shortName AS "customer.shortName",
			mc.recordId AS "customer.recordId",
			mc.`no` AS 'customer.no',
			DATE_FORMAT(DATE(CAST(CONCAT(CAST(a.period AS CHAR),'01') AS DATE)),'%Y.%m') AS "expPeriod",
			a.adjustValue,
			a.receivedAmount,
			a.sentGoodsValue,
			a.returnGoodsValue,
			<if test="taxName != null and taxName != ''">
				<if test="taxName == 1">
					"含税"  AS "taxDescript",
				</if>
				<if test="taxName == 2">
					"不含税"  AS "taxDescript",
				</if>
			</if>
			<if test="taxName == null">
				"含税"  AS "taxDescript",
			</if>
			mdv.value AS "payWay",
			salesMan2.userName AS "salesManName",
			IFNULL(merchandiser2.userName,merchandiser4.userName) AS "merchandiserName"
		FROM sl_accounts_receivable a
		<include refid="accountsReceivableJoins"/>
		LEFT JOIN sl_goods_check b ON b.customerId = a.customerId AND b.period = a.period
		LEFT JOIN sl_delivery_detail c ON c.recordId = b.sourceId
		LEFT JOIN sl_contract_detail d ON d.recordId = c.contractDetailId
		LEFT JOIN sl_contract e ON e.recordId = d.contractId
		LEFT JOIN md_dict_value f ON f.recordId = e.taxDescript AND f.companyId = a.companyId
		LEFT JOIN sl_delivery delivery ON delivery.recordId = c.deliveryId AND delivery.companyId = a.companyId AND delivery.activeFlag = 1
		LEFT JOIN st_product_store g ON g.recordId = b.sourceId
		LEFT JOIN sl_contract_detail h ON h.recordId = g.contractDetailId
		LEFT JOIN sl_contract i ON i.recordId = h.contractId
		LEFT JOIN md_dict_value j ON j.recordId = i.taxDescript AND j.companyId = a.companyId
		LEFT JOIN sm_user su on su.recordId = a.createdBy
		LEFT JOIN md_dict_value mdv on mdv.recordId = mc.payWay AND mdv.itemId = 13
		LEFT JOIN
		(
			SELECT
			salesMan1.recordId AS "recordId",
			REPLACE(salesMan1.userName,' ','') AS "userName"
			FROM sm_user salesMan1
		) salesMan2 on salesMan2.recordId = mc.salesman
		LEFT JOIN
		(
			SELECT
			merchandiser1.recordId AS "recordId",
			REPLACE(merchandiser1.userName,' ','') AS "userName"
			FROM sm_user merchandiser1
		) merchandiser2 on merchandiser2.recordId = e.createdBy
		LEFT JOIN
		(
			SELECT
			merchandiser3.recordId AS "recordId",
			REPLACE(merchandiser3.userName,' ','') AS "userName"
			FROM sm_user merchandiser3
		) merchandiser4 on merchandiser4.recordId = i.createdBy
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
			and a.adjustFlag = #{adjustFlag}
			AND (a.sentGoodsValue > 0 OR a.returnGoodsValue > 0 OR a.adjustValue > 0)
			<if test="customer != null and customer.no != null and customer.no != ''">
				AND mc.no like CONCAT('%', #{customer.no}, '%')
			</if>
			<if test="customer != null and customer.shortName != null and customer.shortName != ''">
				AND mc.shortName like CONCAT('%', #{customer.shortName}, '%')
			</if>
			<!-- 年-->
			<if test="dateType == 1">
				<if test="period != null and filterType == null">
					AND LEFT(a.period,4) like CONCAT('%',#{period},'%')
				</if>
				<if test="filterType != null and filterType == 1 and period != null">
					AND LEFT(a.period,4) like CONCAT('%',#{period}, '%') AND a.completeFlag = 0
				</if>
				<if test="filterType != null and filterType == 2 and period != null">
					AND LEFT(a.period,4) like CONCAT('%',#{period}, '%') AND a.completeFlag = 0
				</if>
			</if>			
			<!-- 月 -->
			<if test="dateType == 2">
				<if test="period != null and filterType == null">
					AND RIGHT(a.period,2) like CONCAT('%',#{period}, '%')
				</if>
				<if test="filterType != null and filterType == 1 and period != null">
					AND RIGHT(a.period,2) like CONCAT('%',#{period}, '%') AND a.completeFlag = 0
				</if>
				<if test="filterType != null and filterType == 2 and period != null">
					AND RIGHT(a.period,2) like CONCAT('%',#{period}, '%') AND a.completeFlag = 0
				</if>
			</if>
			<!-- 年月 -->
			<if test="dateType == 3">
				<if test="period != null and filterType == null">
					AND a.period =#{period}
				</if>
				<if test="filterType != null and filterType == 1 and period != null">
					AND a.period =#{period} AND a.completeFlag = 0
				</if>
				<if test="filterType != null and filterType == 2 and period != null">
					AND a.period != #{period} AND a.completeFlag = 0
				</if>
			</if>
			<if test="filterType != null and filterType == 2003">
				AND a.status = 2003 AND a.completeFlag = 0
			</if>
			<if test="filterType != null and filterType == 4">
				AND a.completeFlag = 1
			</if>
			<!-- zjn 2018-07-03 含税状态 -->
			<if test="taxName != null and taxName != '' and taxDescriptId != null and taxDescriptId != ''">
				<if test="taxName == 1">
					AND IFNULL(f.recordId,j.recordId) <![CDATA[<>]]> #{taxDescriptId}
				</if>
				<if test="taxName == 2">
					AND IFNULL(f.recordId,j.recordId) <![CDATA[=]]> #{taxDescriptId}
				</if>
			</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (mc.salesman = #{createdBy.recordId}
				 or a.customerId in
				(SELECT	cs.customerId FROM sm_customer_salesAssistant cs WHERE cs.activeFlag = 1 AND cs.userId = #{createdBy.recordId}))
			</if>
			<!-- 状态 -->
			<if test="status != null and status != ''">
				AND a.status = #{status}
			</if>
			<!-- 业务员名称 -->
			<if test="salesManName != null and salesManName != ''">
				AND salesMan2.userName like CONCAT('%', #{salesManName}, '%')
			</if>
			<!-- 跟单员名称 -->
			<if test="merchandiserName != null and merchandiserName != ''">
				AND IFNULL(merchandiser2.userName,merchandiser4.userName)  like CONCAT('%', #{merchandiserName}, '%')
			</if>
			<if test="deliveryQuery != null and deliveryQuery != ''">
				AND REPLACE(CONCAT(IFNULL(delivery.no,""),IFNULL(IFNULL(e.no,i.no),"") )," ","") LIKE CONCAT('%',REPLACE(#{deliveryQuery}," ",""), '%')
			</if>
			<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
				AND a.createdDate >= #{sentTimeStartQr}
			</if>
			<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
				AND a.createdDate <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
			GROUP BY a.recordId
			ORDER BY a.period desc
			</where>
	</select>
	
	<!-- zjn 2019-01-22 获取收款对账单某月调整金额 -->
	<select id="getReceivableAdjustAmount" resultType="java.math.BigDecimal">
		select
			sum(a.adjustValue)
		from sl_accounts_receivable a
		<where>
			a.activeFlag = #{DEL_FLAG_NORMAL} AND a.companyId in (${company.recordId})
			AND a.adjustFlag = 1
			<if test="queryDate != null and queryDate != ''">
				AND a.period = DATE_FORMAT(#{queryDate},'%Y%m')
			</if>
			<if test="queryDate == null or queryDate ==''">
				AND a.period = DATE_FORMAT(CURDATE(),'%Y%m')
			</if>
		</where>
	</select>
	
	<!-- tj 2019-02-28 修改送货/退货金额  -->
	<update id="updateCost">
		UPDATE sl_accounts_receivable SET 
			sentGoodsValue = #{sentGoodsValue},
			returnGoodsValue = #{returnGoodsValue}
		WHERE recordId = #{recordId}
	</update>
	
	<!-- tj 2019-02-28 查询收款对账单数据  -->
	<select id="findCost" resultType="AccountsReceivable">
		SELECT 
			a.recordId,
			a.sentGoodsValue,
			a.returnGoodsValue
		FROM sl_accounts_receivable a
		WHERE a.customerId = #{customer.recordId}
		AND a.companyId = #{company.recordId} 
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
		AND a.period =#{period}
	</select>
	
	<!-- zjn 2019-10-09 根据客户和对账月份获取对账单 -->
	<select id="getAccountsReceivable" resultType="AccountsReceivable">
		SELECT
			<include refid="accountsReceivableColumns"/>
		FROM sl_accounts_receivable a
		WHERE a.customerId = #{customer.recordId}
		AND a.period =#{period} AND a.activeFlag = 1 LIMIT 1
	</select>
	
	<!-- zjn 2019-10-09  批量删除对账单 -->
	<delete id="batchDeleteAr">
		DELETE FROM sl_accounts_receivable WHERE recordId IN (${recordId})
	</delete>
	
	<!-- zjn 2019-10-09 批量添加对账单 -->
	<insert id="batchInsertAr">
		INSERT INTO sl_accounts_receivable(
			companyId,
			customerId,
			period,
			adjustValue,
			receivedAmount,
			sentGoodsValue,
			returnGoodsValue,
			completeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			activeFlag,
			remark,
			status,
			adjustFlag,
			checkCause
		) VALUES 
		<foreach collection="arList" item="item" index= "index" separator =",">
    	(
			#{item.company.recordId},
			#{item.customer.recordId},
			#{item.period},
			#{item.adjustValue},
			#{item.receivedAmount},
			#{item.sentGoodsValue},
			#{item.returnGoodsValue},
			#{item.completeFlag},
			#{item.createdBy.recordId},
			#{item.createdDate},
			#{item.lastUpdBy.recordId},
			#{item.lastUpdDate},
			1,
			#{item.remark},
			IFNULL(#{item.status},2001),
			#{item.adjustFlag},
			#{item.checkCause}
   		)
    	</foreach>
	</insert>
	
	<update id="updateReceivableAmount">
		UPDATE sl_accounts_receivable aa
		LEFT JOIN
		(
			SELECT
				customerId AS "customerId",
				period AS "period",
				SUM(IF(ISNULL(inOutFlag) OR inOutFlag = 0,amount,0)) AS "returnGoodsValue",
				SUM(IF(!ISNULL(inOutFlag) AND inOutFlag = 1,amount,0)) AS "sentGoodsValue"
			FROM sl_goods_check
			WHERE companyId = #{company.recordId} AND activeFlag = 1 
			AND (adjustCheckFlag <![CDATA[<>]]> 12 OR ISNULL(adjustCheckFlag))
			AND (ISNULL(adjustFlag) OR adjustFlag = 1) AND customerId = #{customer.recordId}
			AND period = #{period}
		) bb ON bb.customerId = aa.customerId AND bb.period = aa.period
		SET aa.returnGoodsValue = IFNULL(bb.returnGoodsValue,0),aa.sentGoodsValue = IFNULL(bb.sentGoodsValue,0)
		WHERE aa.companyId = #{company.recordId} AND aa.activeFlag = 1 AND aa.customerId = #{customer.recordId}
		AND aa.period = #{period}
	</update>
	
	<select id="checkReceivableStatus" resultType="Integer">
		SELECT
			COUNT(1)
		FROM sl_goods_check a
		LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId AND b.activeFlag = 1
		LEFT JOIN sl_accounts_receivable c ON c.customerId = a.customerId AND c.period = a.period
		WHERE a.activeFlag = 1  AND c.`status` <![CDATA[<>]]> 2001
		<if test="groupCenterId != null and groupCenterId != ''">
			AND FIND_IN_SET(b.groupCenterId,#{groupCenterId})
		</if>
		<if test="groupCenterId == null || groupCenterId == ''">
			AND b.deliveryId = #{deliveryId}
		</if>
		<if test="receivedDate != null and receivedDate != ''">
			AND c.period = DATE_FORMAT(#{receivedDate},'%Y%m')
		</if>
	</select>
	
	<update id="updateReceivableAmountTwo">
		UPDATE sl_accounts_receivable aa
		LEFT JOIN
		(
			SELECT
				customerId AS "customerId",
				period AS "period",
				SUM(IF(ISNULL(inOutFlag) OR inOutFlag = 0,amount,0)) AS "returnGoodsValue",
				SUM(IF(!ISNULL(inOutFlag) AND inOutFlag = 1,amount,0)) AS "sentGoodsValue"
			FROM sl_goods_check 
			WHERE activeFlag = 1 AND (adjustCheckFlag <![CDATA[<>]]> 12 OR ISNULL(adjustCheckFlag))
			AND (ISNULL(adjustFlag) OR adjustFlag = 1) 
			AND customerId = #{customer.recordId}
			AND period = DATE_FORMAT(#{receivedDate},'%Y%m')
			GROUP BY customerId,period
		) bb ON bb.customerId = aa.customerId AND bb.period = aa.period
		SET aa.returnGoodsValue = IFNULL(bb.returnGoodsValue,0),aa.sentGoodsValue = IFNULL(bb.sentGoodsValue,0)
		WHERE aa.activeFlag = 1 AND aa.customerId = #{customer.recordId} 
		AND aa.period = DATE_FORMAT(#{receivedDate},'%Y%m')
	</update>
	
	<update id="updateReceivablePeriod">
		UPDATE sl_accounts_receivable SET
			period = DATE_FORMAT(#{newDate},'%Y%m')
		WHERE activeFlag = 1 AND customerId = #{customer.recordId} AND period = DATE_FORMAT(#{oldDate},'%Y%m')
	</update>
	
	<update id="deleteReceivable">
		UPDATE sl_accounts_receivable SET
			activeFlag = 2
		WHERE activeFlag = 1 AND customerId = #{customer.recordId} AND period = DATE_FORMAT(#{oldDate},'%Y%m')
	</update>
	
	<select id="getReceivableCount" resultType="Integer">
		SELECT 
			COUNT(1) 
		FROM sl_accounts_receivable 
		WHERE activeFlag = 1 AND customerId = #{customer.recordId}
		AND period = DATE_FORMAT(#{receivedDate},'%Y%m')
	</select>
	
	<select id="getReceivableCountTwo" resultType="Integer">
		SELECT 
			COUNT(1) 
		FROM sl_accounts_receivable 
		WHERE activeFlag = 1 AND customerId = #{customer.recordId}
		AND period = #{period}
	</select>
	
	<delete id="physicalDeletion">
		DELETE FROM sl_accounts_receivable WHERE activeFlag = 2 AND customerId = #{customer.recordId} AND period = #{period}
	</delete>
	
	<select id="getReceivableData" resultType="AccountsReceivable">
		SELECT 
			recordId,
			status,
			DATE_FORMAT(NOW(),'%Y%m') AS "period"
		FROM sl_accounts_receivable 
		WHERE companyId = #{company.recordId} AND activeFlag = 1 
		AND customerId = #{customer.recordId} AND period = #{period}
	</select>
	
	<select id="getInspectRepairData" resultType="com.kyb.pcberp.modules.contract.vo.InspectRepairVo">
		SELECT
			aa.*
		FROM
		(
			SELECT
				g.`no` AS "deliveryNo",
				d.`no` AS "contractNo",
				d.customerPo AS "customerPo",
				e.`no` AS "craftNo",
				e.customerModel AS "customerModual",
				a.quantity AS "quantity",
				a.period AS "period",
				a.amount AS "amount",
				1 AS "type",
				CONCAT(
					'源',
					h.shortName,
					'自动生成'
				) AS "explain",
				a.lastUpdDate AS "lastUpdDate",
				i.userName AS "lastUpdBy.userName"
			FROM sl_goods_check a
			LEFT JOIN sl_goods_check f ON f.recordId = a.sourceGcId
			LEFT JOIN sl_delivery_detail b ON b.recordId = f.sourceId
			LEFT JOIN sl_contract_detail c ON c.recordId = b.contractDetailId
			LEFT JOIN sl_contract d ON d.recordId = c.contractId
			LEFT JOIN sl_contract_craft e ON e.recordId = c.craftId
			LEFT JOIN sl_delivery g ON g.recordId = b.deliveryId
			LEFT JOIN md_company h ON h.recordId = f.companyId
			LEFT JOIN sm_user i ON i.recordId = a.lastUpdBy
			WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1
			AND a.sourceGcId IS NOT NULL
			<if test="period != null and period != ''">
				AND a.period = #{period}
			</if>	
			UNION ALL
		
				SELECT
					f.`no` AS "deliveryNo",
					d.`no` AS "contractNo",
					d.customerPo AS "customerPo",
					e.`no` AS "craftNo",
					e.customerModel AS "customerModual",
					a.quantity AS "quantity",
					a.period AS "period",
					a.amount AS "amount",
					2 AS "type",
					"重复数据清除" AS "explain",
					a.lastUpdDate AS "lastUpdDate",
					g.userName AS "lastUpdBy.userName"
				FROM
					sl_goods_check a
				LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId
				LEFT JOIN sl_contract_detail c ON c.recordId = b.contractDetailId
				LEFT JOIN sl_contract d ON d.recordId = c.contractId
				LEFT JOIN sl_contract_craft e ON e.recordId = c.craftId
				LEFT JOIN sl_delivery f ON f.recordId = b.deliveryId
				LEFT JOIN sm_user g ON g.recordId = a.lastUpdBy
				WHERE a.companyId = #{company.recordId}
				AND a.activeFlag = 2
				AND a.`status` = ********
				<if test="period != null and period != ''">
					AND a.period = #{period}
				</if>
		) aa
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<update id="batchUpdateAmount">
		UPDATE sl_accounts_receivable aa
		LEFT JOIN
		(
			SELECT
				customerId AS "customerId",
				period AS "period",
				companyId AS "companyId",
				SUM(IF(ISNULL(inOutFlag) OR inOutFlag = 0,amount,0)) AS "returnGoodsValue",
				SUM(IF(!ISNULL(inOutFlag) AND inOutFlag = 1,amount,0)) AS "sentGoodsValue"
			FROM sl_goods_check
			WHERE companyId = #{companyId} AND activeFlag = 1 
			AND (adjustCheckFlag <![CDATA[<>]]> 12 OR ISNULL(adjustCheckFlag))
			AND (ISNULL(adjustFlag) OR adjustFlag = 1) AND customerId = #{customer.recordId}
			AND FIND_IN_SET(period,#{periods})
			GROUP BY customerId,period
		) bb ON bb.companyId = aa.companyId AND bb.customerId = aa.customerId AND bb.period = aa.period
		SET aa.returnGoodsValue = IFNULL(bb.returnGoodsValue,0),aa.sentGoodsValue = IFNULL(bb.sentGoodsValue,0)
		WHERE aa.companyId = #{companyId} AND aa.activeFlag = 1 AND aa.customerId = #{customer.recordId}
		AND FIND_IN_SET(aa.period,#{periods})
	</update>
	
	<select id="getExternalInspectData" resultType="AccountsReceivable">
		SELECT
			a.recordId,
			b.`no` AS "customer.no",
			b.shortName AS "customer.shortName",
			a.`status`
		FROM sl_accounts_receivable a
		LEFT JOIN md_customer b ON b.recordId = a.customerId
		WHERE a.companyId = #{companyId} AND a.activeFlag = 1 AND a.period = #{period} AND a.`status` = 2001
		AND (a.sentGoodsValue > 0 OR a.returnGoodsValue > 0 OR a.adjustValue > 0)
	</select>
	
	<select id="checkReceivableStatusTwo" resultType="Integer">
		SELECT
			COUNT(1)
		FROM sl_accounts_receivable
		WHERE companyId = #{company.recordId} AND activeFlag = 1
		AND customerId = #{customer.recordId} AND period = #{period}
		AND status <![CDATA[<>]]> 2001
	</select>
	
	<update id="updateConfirmData">
		UPDATE sl_accounts_receivable SET
			confirmUserId = #{confirmUserId},
			confirmDate = #{confirmDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateCheckData">
		UPDATE sl_accounts_receivable SET
			checkUserId = #{checkUserId},
			checkDate = #{checkDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="opeanStatus">
		UPDATE sl_accounts_receivable SET
			status = 2001
		WHERE customerId = #{customerId} AND period = #{period} AND status = 2002 AND activeFlag = 1
	</update>
	
	<select id="getCheckFinishReport" resultType="AccountsReceivableVo">
		SELECT 
			ar.recordId,
			ar.`status`,
			mc.`no` AS 'customerNo',
			mc.shortName AS 'customerName',
			ar.period,
			(IFNULL(ar.sentGoodsValue,0)+IFNULL(ar.adjustValue,0)-IFNULL(ar.returnGoodsValue,0)-IFNULL(ar.receivedAmount,0)) AS 'amount',
			ar.confirmDate,
			ar.checkDate,
			mc.confirmBilDate AS 'customerConfirmDay',
			mc.checkBilDate AS 'customerCheckDay',
			mc.checkDate AS 'customer.checkDate',
			confirmUser.userName as "confirmUserName",
			checkUser.userName as "checkUserName"
		FROM sl_accounts_receivable ar 
		LEFT JOIN md_customer mc ON ar.customerId = mc.recordId
		LEFT JOIN sm_user confirmUser ON confirmUser.recordId = ar.confirmUserId
		LEFT JOIN sm_user checkUser ON checkUser.recordId = ar.checkUserId
		WHERE ar.activeFlag = 1 AND ar.companyId = #{company.recordId}
		AND (IFNULL(ar.sentGoodsValue,0)+IFNULL(ar.adjustValue,0)-IFNULL(ar.returnGoodsValue,0)-IFNULL(ar.receivedAmount,0)) > 0
		<if test="period != null and period != ''">
			AND ar.period  LIKE CONCAT('%',#{period},'%')
		</if>
		<if test="customerNo != null and customerNo != ''">
			AND mc.`no` LIKE CONCAT('%',#{customerNo},'%')
		</if>
		
		<if test="customerName != null and customerName != ''">
			AND mc.shortName LIKE CONCAT('%',#{customerName},'%')
		</if>
		ORDER BY ar.period DESC ,mc.`no` 
	</select>
	
	<select id="getdefaultAPR" resultType="BigDecimal">
		SELECT parameterValue FROM md_parameterset WHERE companyId = #{companyId} AND jianPin = 'defaultAPR' LIMIT 1
	</select>
	
	<select id="getSysReceivableDays" resultType="Integer">
		SELECT parameterValue FROM md_parameterset WHERE companyId = #{companyId} AND jianPin = 'receivableDays' LIMIT 1
	</select>
	
	<select id="checkFinishDetailReport" resultType="AccountsReceivableVo">
		SELECT
			mc.`no` AS 'customerNo',
			mc.shortName AS 'customerName',
			sc.customerPo,
			scc.customerModel,
			sc.`no` AS 'contractNo',
			scc.`no` As 'craftNo',
			IFNULL(sra.deliveryDate,xsscd.deliveryDate) AS 'deliveryDate' ,
			st.operateDate,
			SUM(st.quantity) AS 'quantity',
			SUM(st.quantity*sp.price) AS 'amount',
			SUM(DATEDIFF(st.operateDate,IFNULL(sra.deliveryDate,xsscd.deliveryDate))) AS 'diffDay'
			
		FROM
			st_product_store st
			LEFT JOIN sl_contract_detail scd ON st.contractDetailId = scd.recordId 
			LEFT JOIN sl_contract_detail xsscd ON xsscd.companyId = #{company.recordId} AND xsscd.groupCenterId = scd.groupCenterId AND xsscd.activeFlag = 1
			LEFT JOIN sl_contract sc ON xsscd.contractId = sc.recordId
			LEFT JOIN sl_contract_craft scc on scc.recordId = xsscd.craftId 
			LEFT JOIN sl_price sp ON sp.recordId = xsscd.priceId
			LEFT JOIN md_customer mc ON mc.recordId = sc.customerId
			LEFT JOIN qc_inspect qi ON qi.recordId = scd.inspectId AND qi.type = 2 
			LEFT JOIN st_reject_application sra ON sra.recordId = qi.produceBatchId 
		WHERE
			<if test="type != 4">
				st.inOutType = 11 
			</if>
			<if test="type == 4">
				st.inOutType = 12 
			</if>
			AND st.companyId = #{factoryComId}
			AND st.activeFlag = 1 
			AND st.`status` = 99999901 
			AND scd.activeFlag = 1 
			AND DATEDIFF(st.operateDate,IFNULL(sra.deliveryDate,xsscd.deliveryDate))>0
			AND scd.createdDate >= '2021-01-01'
			<if test="customerNo != null and customerNo != ''">
				AND mc.`no` LIKE CONCAT('%',#{customerNo},'%')
			</if>
			
			<if test="customerName != null and customerName != ''">
				AND mc.shortName LIKE CONCAT('%',#{customerName},'%')
			</if>
			
			<if test="customerPo != null and customerPo != ''">
				AND sc.customerPo LIKE CONCAT('%',#{customerPo},'%')
			</if>
			
			<if test="customerModel != null and customerModel != ''">
				AND scc.customerModel LIKE CONCAT('%',#{customerModel},'%')
			</if>
			
			<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
				AND IFNULL(sra.deliveryDate,xsscd.deliveryDate) <![CDATA[>=]]> #{sentTimeStartQr}
			</if>
			
			<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
				AND IFNULL(sra.deliveryDate,xsscd.deliveryDate) <![CDATA[<=]]> #{sentTimeEndQr}
			</if>
			
			GROUP BY sc.customerId,scd.craftId,DATE_FORMAT(st.operateDate,'%Y-%m-%d'),DATE_FORMAT(IFNULL(sra.deliveryDate,xsscd.deliveryDate),'%Y-%m-%d')
			ORDER BY DATE_FORMAT(IFNULL(sra.deliveryDate,xsscd.deliveryDate),'%Y-%m') DESC,mc.shortName,scc.`no`,st.operateDate DESC,scd.createdDate DESC
	</select>
	
	<select id="saleCostReport" resultType="SaleCostVo">
		SELECT
			IF
				( st.complaintId IS NULL, '正常送货', '客诉送货' ) AS 'sendType',
				IFNULL(de.`name`,de2.`name`) AS 'deptName',
				IFNULL(xssc.`no`,sc.`no`) AS 'contractNo',
				scc.`no` AS 'craftNo',
				mc.shortName AS 'customerName',
				scc.customerModel,
				IFNULL( xsc.shortName, c.shortName ) AS 'companyName',
				mc.`no` AS 'xsCustomerNo',
				st.inOutType,
				st.quantity AS 'inOutQuantity',
				scd.orderDeailArea,
				st.`status`,
				DATE_FORMAT(st.operateDate,'%Y-%m-%d') AS 'operateDate',
				sc.customerPo,
				scd.quantity,
				scd.subTotal AS 'orderSubTotal',
				IFNULL(ppd.subTotal,IF(xsscd.companyId = 17,IFNULL(igc.processFee,0)+IFNULL(igc.materialFee,0),null)) AS 'prdSubTotal',
				scd.createdDate ,
				IFNULL(ms.shortName,'江西领德辉') AS 'supplierName',
				IF(igc.recordId IS NULL,'外协','自制') AS 'madeType'
			FROM
				st_product_store st
				LEFT JOIN sl_contract_detail scd ON scd.recordId = st.contractDetailId
				LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId
				LEFT JOIN sl_contract sc ON sc.recordId = scd.contractId
				LEFT JOIN md_customer mc ON mc.recordId = sc.customerId
				LEFT JOIN icloud_group_center igc ON igc.recordId = scd.groupCenterId
				LEFT JOIN sl_contract_detail xsscd ON xsscd.recordId = igc.contactDeailId 
				AND igc.activeFlag = 1
				LEFT JOIN sl_contract_detail wxscd ON wxscd.recordId = scd.groupCenterId AND igc.recordId IS NULL
				LEFT JOIN sl_contract xssc ON xssc.recordId = IFNULL(xsscd.contractId,wxscd.contractId)
				LEFT JOIN md_customer xsmc ON xsmc.recordId = xssc.customerId
				LEFT JOIN md_company xsc ON xsc.recordId = xssc.companyId
				LEFT JOIN md_company c ON c.recordId = sc.companyId
				LEFT JOIN pu_prdorder_detail ppd ON ppd.contractDetailId = scd.recordId AND ppd.activeFlag = 1
				LEFT JOIN md_supplier ms ON ms.recordId = IFNULL(xsscd.madeSupplierId ,IFNULL(wxscd.madeSupplierId,scd.madeSupplierId))
				LEFT JOIN md_department de ON de.recordId = xssc.deptId
				LEFT JOIN md_department de2 ON de2.recordId = sc.deptId
			WHERE
				st.inOutType = 12 
				AND st.activeFlag = 1 
				AND st.`status` = 99999901 
				AND st.companyId = #{company.recordId} 
				AND ( st.remark IS NULL OR st.remark NOT LIKE '%盘%' )
				<if test="customerNo != null and customerNo != ''">
					AND IFNULL( xsmc.`no`, mc.`no` ) LIKE CONCAT('%',#{customerNo},'%')
				</if>
				
				<if test="customerName != null and customerName != ''">
					AND IFNULL( xsmc.shortName, mc.shortName ) LIKE CONCAT('%',#{customerName},'%')
				</if>
				
				<if test="customerPo != null and customerPo != ''">
					AND sc.customerPo LIKE CONCAT('%',#{customerPo},'%')
				</if>
				
				<if test="customerModel != null and customerModel != ''">
					AND scc.customerModel LIKE CONCAT('%',#{customerModel},'%')
				</if>
				
				<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
					AND st.operateDate <![CDATA[>=]]> #{sentTimeStartQr}
				</if>
				
				<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
					AND st.operateDate <![CDATA[<=]]> #{sentTimeEndQr}
				</if>
				<if test="craftNo != null and craftNo != ''">
					AND scc.`no` LIKE CONCAT('%',#{craftNo},'%')
				</if>
				<if test="companyName != null and companyName != ''">
					AND IFNULL( xsc.shortName, c.shortName ) LIKE CONCAT('%',#{companyName},'%')
				</if>
				
				ORDER BY st.operateDate DESC
				<if test="exportFlag ==null">
					LIMIT 200
				</if>
				
	</select>
	
	<select id="checkSingleDetails" resultType="SingleCostVo">
	SELECT 
		mc.shortName AS 'customerName',
		gc.period,
		mc.checkDate,
		ssrd.status,
		ssrd.lastUpdDate AS 'receivedDate',
		IFNULL(ssrd.confirmAmount,0) AS 'confirmAmount',
		IF(ssrd.recordId IS NULL,gc.amount,(ssrd.amount-IFNULL(ssrd.confirmAmount,0))) AS 'amount',
			salesMan.userName AS 'salesMan',
			IFNULL(
				merchandiser1.userName,
			IFNULL( merchandiser2.userName, merchandiser3.userName )) AS 'createOrderMan' ,
			mange.userName AS 'managerName'
		FROM sl_goods_check gc  
		LEFT JOIN sl_single_receivable_detail ssrd ON ssrd.goodsCheckId = gc.recordId AND ssrd.activeFlag = 1
		LEFT JOIN sl_single_receivable ssr ON ssr.recordId = ssrd.singleReceivableId AND ssr.activeFlag = 1  
		LEFT JOIN sl_delivery_detail b ON b.recordId = gc.sourceId 
		AND gc.inOutFlag = 1
		LEFT JOIN sl_contract_detail scd1 ON scd1.recordId = b.contractDetailId
		LEFT JOIN sl_contract sc1 ON sc1.recordId = scd1.contractId 
		AND sc1.companyId = scd1.companyId 
		AND sc1.activeFlag = 1
		LEFT JOIN st_product_store c ON c.recordId = gc.sourceId 
		AND c.inOutType = 13 
		AND gc.inOutFlag = 0 
		AND gc.inOutType = 1
		LEFT JOIN sl_contract_detail scd2 ON scd2.recordId = c.contractDetailId
		LEFT JOIN sl_contract sc2 ON sc2.recordId = scd2.contractId 
		AND sc2.companyId = scd2.companyId 
		AND sc2.activeFlag = 1
		LEFT JOIN st_product_store d ON d.recordId = gc.sourceId 
		AND gc.inOutFlag = 1 
		AND d.complaintId IS NOT NULL 
		AND d.inOutType = 12
		LEFT JOIN sl_contract_detail scd3 ON scd3.recordId = d.contractDetailId
		LEFT JOIN sl_contract sc3 ON sc3.recordId = scd3.contractId 
		AND sc3.companyId = scd3.companyId 
		AND sc3.activeFlag = 1
		LEFT JOIN md_customer mc ON mc.recordId = gc.customerId
		LEFT JOIN sm_user salesMan ON salesMan.recordId = mc.salesman 
		AND salesMan.companyId = mc.companyId 
		AND salesMan.activeFlag = 1
		LEFT JOIN sm_user merchandiser1 ON merchandiser1.recordId = sc1.createdBy 
		AND merchandiser1.companyId = sc1.companyId 
		AND merchandiser1.activeFlag = 1
		LEFT JOIN sm_user merchandiser2 ON merchandiser2.recordId = sc2.createdBy 
		AND merchandiser2.companyId = sc2.companyId 
		AND merchandiser2.activeFlag = 1
		LEFT JOIN sm_user merchandiser3 ON merchandiser2.recordId = sc3.createdBy 
		AND merchandiser2.companyId = sc3.companyId 
		AND merchandiser2.activeFlag = 1 
		LEFT JOIN md_employee me ON me.userId = IFNULL(merchandiser1.recordId,IFNULL( merchandiser2.recordId, merchandiser3.recordId )) AND me.activeFlag = 1
		LEFT JOIN md_department_relation_employee mdre ON mdre.employeeId = me.recordId AND mdre.activeFlag = 1 AND mdre.`status` = 1
		LEFT JOIN md_department_relation_employee mdre2 ON mdre2.employeeId = me.recordId AND mdre2.activeFlag = 1 AND mdre2.`status` = 2
		LEFT JOIN md_organization mo ON mo.recordId = mdre2.allId AND mo.activeFlag = 1
		LEFT JOIN md_department dep ON dep.recordId = IFNULL(mdre.allId,mo.deptId)
		LEFT JOIN sm_user mange ON  mange.recordId = dep.managerId
		WHERE  gc.activeFlag = 1 AND gc.companyId = #{company.recordId}
		<if test="customerNo != null and customerNo != ''">
			AND IFNULL( mc.`no`, mc.`no` ) LIKE CONCAT('%',#{customerNo},'%')
		</if>
		
		<if test="customerName != null and customerName != ''">
			AND mc.shortName  LIKE CONCAT('%',#{customerName},'%')
		</if>
		
		<if test="period != null and period != ''">
			AND gc.period LIKE CONCAT('%',#{period},'%')
		</if>
		ORDER BY gc.period DESC,gc.customerId,IFNULL(
				merchandiser1.userName,
			IFNULL( merchandiser2.userName, merchandiser3.userName )) ,mange.userName 
			
	
	</select>
	
	<update id="updateByGoodsCheck">
		UPDATE sl_accounts_receivable aa
		INNER JOIN
		(
			SELECT
				a.recordId AS "recordId",
				(
					SELECT
						COUNT(1)
					FROM sl_goods_check
					WHERE companyId = a.companyId AND customerId = a.customerId 
					AND period = a.period AND activeFlag = 1 AND operateStatus <![CDATA[<>]]> 1004
					AND (adjustCheckFlag <![CDATA[<>]]> 12 OR ISNULL(adjustCheckFlag))
					AND (ISNULL(adjustFlag) OR adjustFlag = 1)
				) AS "count"
			FROM sl_accounts_receivable a
			WHERE a.companyId = #{companyId} AND a.customerId = #{customer.recordId}
			AND a.period = #{period} AND a.activeFlag = 1
		) bb ON bb.recordId = aa.recordId
		SET aa.`status` = 2003
		WHERE bb.count = 0
	</update>
	
	<select id="getUsedMaterialReport" resultType="UsedMaterialVo">
		SELECT
			sc.`no` AS 'contractNo',
			sc.customerPo,
			scc.customerModel,
			ROUND(scd.orderDeailArea,4) AS "orderDeailArea",
			scc.`no` AS 'craftNo',
			pf.`no` AS 'feedNos',
			pf.recordId AS "feedId",
            GROUP_CONCAT(CONCAT(mm.`no`,"-",CASE WHEN !ISNULL(st.remainQty) AND st.inouttype IN (3,5) THEN st.quantity END,"-",st.remainQty,"-",IFNULL(st .price,0),"-",IFNULL(ee.`value`, 0) * IFNULL(ff.`value`, 0) / 1000000), "-",ee.`value`,"-",ff.`value`) AS "materialStockLists",
			mm2.`no` AS 'mainMaterialNo',
			mm2.specification AS 'mainMaterialSpe',
			mm3.`no` AS 'backMaterialNo',
			mm3.specification AS 'backMaterialSpe',
			scd.recordId AS 'contractDetailId',
			igc.needBord,
			igc.occupiedStock,
			igc.excessStock,
			igc.replyStock,
			igc.usePurchStocks,
			(IFNULL(igc.occupiedStock,0) + IFNULL(igc.excessStock,0) + IFNULL(igc.replyStock,0) + IFNULL(igc.usePurchStocks,0)) AS "mainStock",
			snm.useStocks,
			ROUND(igc.materialFee,2) AS 'materialFee',
			ROUND(IFNULL(scc.`pnlLength`,0) * IFNULL(scc.`pnlWidth`,0) * IFNULL(scd.quantity,0) / IFNULL(scc.pnlDivisor,1) /1000000/(a.parameterValue/100),4) AS "area",
            CONCAT_WS(' ', b.value, b1.value, b2.value, b3.value, b4.value, b5.value, b6.value, b7.value, b8.value, b9.value, b10.value, b11.value,
            CASE scd2.processValueId WHEN 1 THEN '曝光' WHEN 2 THEN '丝印' ELSE NULL END, CONCAT('品牌:',igc.manufacturer)) AS 'specification',
			ROUND(SUM(IFNULL(ee.`value`,0) * IFNULL(ff.`value`,0) / 1000000 *  st.quantity),4) AS "issueArea",
			sn.quantity AS "mergeOrderQty",
			sn2.quantity AS "orderQty",
			CASE IFNULL(scd.inspectType,0) WHEN 0 THEN '正常'
			WHEN 1 THEN '补料'
			WHEN 2 THEN '客诉'
			WHEN 3 THEN '客诉'
			WHEN 4 THEN '补料'
			ELSE NULL END AS "documentsStatus",
			orderType.`value` AS "orderType"
		FROM
			st_material_store st
		LEFT JOIN md_material mm ON mm.recordId = st.materialId
		LEFT JOIN md_material_specification_relation ee ON ee.materialId = mm.recordId AND ee.companyId = mm.companyId
		AND ee.specificationId = (SELECT recordId FROM md_material_specification WHERE companyId = mm.companyId AND `name` = '长' AND activeFlag = 1 LIMIT 1)
		AND ee.activeFlag = 1
		LEFT JOIN md_material_specification_relation ff ON ff.materialId = mm.recordId AND ff.companyId = mm.companyId
		AND ff.specificationId = (SELECT recordId FROM md_material_specification WHERE companyId = mm.companyId AND `name` = '宽' AND activeFlag = 1 LIMIT 1)
		AND ff.activeFlag = 1
		LEFT JOIN md_parameterset a ON a.companyId = st.companyId AND jianPin = 'defaultUtilizationOrders'
		LEFT JOIN pd_feeding pf ON pf.recordId = st.feedingId
		LEFT JOIN sl_notification sn ON sn.recordId = pf.notificationId
		LEFT JOIN sl_notification sn2 ON sn.mergeType = 200602 
		AND sn2.mergeId = sn.recordId 
		AND sn2.companyId = sn.companyId 
		AND sn2.activeFlag = 1
		LEFT JOIN sl_contract_detail scd ON scd.recordId =IF(sn.mergeType = 200602, sn2.contractDetailId, sn.contractDetailId )
		AND scd.activeFlag = 1
		LEFT JOIN icloud_group_center igc ON igc.recordId = scd.groupCenterId
		LEFT JOIN sl_contract_detail scd2 ON scd2.recordId = igc.contactDeailId 
		AND scd2.activeFlag = 1
		LEFT JOIN sl_contract_craft scc ON scc.recordId = scd.craftId
		LEFT JOIN md_dict_value b ON b.recordId = scc.boardLevel
		LEFT JOIN md_dict_value b1 ON b1.recordId = scc.materialType
		LEFT JOIN md_dict_value b2 ON b2.recordId = scc.boardThickness
		LEFT JOIN md_dict_value b3 ON b3.recordId = scc.copperCladThickness
		LEFT JOIN md_dict_value b4 ON b4.recordId = scc.surfaceProcess
		LEFT JOIN md_dict_value b5 ON b5.recordId = scc.solderMaskType
		LEFT JOIN md_dict_value b6 ON b6.recordId = scc.characterType
		LEFT JOIN md_dict_value b7 ON b7.recordId = scc.shapingWay
		LEFT JOIN md_dict_value b8 ON b8.recordId = scc.testMethod
		LEFT JOIN md_dict_value b9 ON b9.recordId = scc.daore
		LEFT JOIN md_dict_value b10 ON b10.recordId = scc.naiya
		LEFT JOIN md_dict_value b11 ON b11.recordId = scc.naiya
		LEFT JOIN sl_contract sc ON sc.recordId = scd2.contractId 
		AND sc.activeFlag = 1
		LEFT JOIN md_material mm2 ON mm2.recordId = igc.materialId
		LEFT JOIN sl_notification_material snm ON snm.notifiId =IF(sn.mergeType = 200602, sn2.recordId, sn.recordId)
		AND snm.activeFlag = 1
		LEFT JOIN md_material mm3 ON mm3.recordId = snm.materialId 
		LEFT JOIN md_customer mc ON mc.recordId = sc.customerId
		LEFT JOIN sm_user su ON su.recordId = IFNULL(sc.userId, mc.salesman)
		LEFT JOIN md_dict_value orderType ON orderType.recordId = scd2.referenceType
		WHERE
			st.inOutType = 3 AND st.activeFlag = 1 AND st.`status` = 99999901
		AND #{companyId} IN (sc.companyId, st.companyId)
		<if test="orderDeailArea != null and orderDeailArea != ''">
				AND scd.orderDeailArea <![CDATA[>=]]> 1 
		</if>
		<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
				AND st.operateDate <![CDATA[>=]]> #{sentTimeStartQr}
		</if>
		<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
			AND st.operateDate <![CDATA[<=]]> #{sentTimeEndQr}
		</if>
		<if test="contractNo != null and contractNo != ''">
				AND sc.`no` like CONCAT('%', #{contractNo}, '%')
		</if>
		<if test="craftNo != null and craftNo != ''">
				AND scc.`no` like CONCAT('%', #{craftNo}, '%')
		</if>
		<if test="customerModel != null and customerModel != ''">
				AND scc.customerModel like CONCAT('%', #{customerModel}, '%')
		</if>
		<if test="usedType != null and usedType == '100'">
			AND (mm.recordId = IFNULL(mm2.recordId,"") OR mm.recordId = IFNULL(mm3.recordId,""))
		</if>
		<if test="usedType != null and usedType == '200'">
				AND mm.recordId <![CDATA[<>]]> IFNULL(mm2.recordId,"") AND mm.recordId <![CDATA[<>]]> IFNULL(mm3.recordId,"")
		</if>
		<if test="createdBy != null and createdBy != ''">
			AND (
				su.recordId = #{createdBy.recordId}
				OR (
					su.recordId IN (
						SELECT
							rc.userId
						FROM
							md_employee rc
						LEFT JOIN md_department_relation_employee rd ON rd.employeeId = rc.recordId
						WHERE
							rd.allId = (
								SELECT
									ma.allId
								FROM
									md_department_relation_employee ma
								LEFT JOIN md_employee mb ON mb.recordId = ma.employeeId
								WHERE mb.position = "部门负责人"
								AND mb.userId = #{createdBy.recordId}
								AND mb.`status` = 1
								AND mb.activeFlag = 1
								LIMIT 1
							)
						AND rc.companyId = #{companyId}
						AND rc.`status` = 1
						AND rc.activeFlag = 1
					)
				)
			)
		</if>
		GROUP BY
			scd.recordId
		ORDER BY
			st.operateDate DESC
	</select>
	
	<insert id="batchInsertReceivable">
		INSERT INTO sl_accounts_receivable
		(
			companyId,
			customerId,
			period,
			sentGoodsValue,
			returnGoodsValue,
			activeFlag,
			createdDate,
			lastUpdDate,
			`status`,
			adjustFlag
		)
		SELECT
			companyId,
			customerId,
			period,
			0,
			0,
			1,
			NOW(),
			NOW(),
			2001,
			1
		FROM sl_goods_check
		WHERE companyId = #{company.recordId} AND activeFlag = 1 AND period = #{period}
		AND (adjustCheckFlag <![CDATA[<>]]> 12 OR ISNULL(adjustCheckFlag))
		AND (ISNULL(adjustFlag) OR adjustFlag = 1) 
		AND customerId NOT IN
		(
			SELECT customerId FROM sl_accounts_receivable WHERE companyId = #{company.recordId} AND period = #{period} AND activeFlag = 1
		)
		GROUP BY customerId,period
	</insert>
	
	<select id="financeCostReport" resultType="FinanceCostVo">
	SELECT
			CASE WHEN a.inOutFlag = 1 THEN 
			(
				CASE WHEN b.complaintId IS NOT NULL THEN '客诉送货出库'
				ELSE '送货出库' END
			) ELSE '客诉退货入库' END 'sendType',
			a.recordId AS "收款对账单明细id",
			k.recordId AS "付款对账单明细id",
			mc.`no` AS 'customer.no',
			mc.`name` AS 'customer.name',
			a.customerId AS 'customer.recordId',
			a.period,
			sar.`status` AS 'operateStatus',
			SUM(a.quantity) AS 'quantity',
			SUM(a.amount) AS 'amount',
			k.operateStatus AS 'payStatus',
			SUM(k.quantity) AS 'payQuantity',
			SUM(k.amount) AS 'payAmount',
			0 AS 'estimateCost',
			SUM(k.amount) AS 'checkCost',
			SUM(k.amount) AS 'billCost',
			'IN' AS 'type'
		FROM sl_goods_check a
		LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId AND a.inOutFlag = 1
		LEFT JOIN st_reject_application c ON c.recordId = b.complaintId
		LEFT JOIN sl_outbound d ON d.recordId = b.terminalDetailId
		LEFT JOIN sl_contract_detail e ON e.recordId = b.contractDetailId
		LEFT JOIN st_product_store f ON f.recordId = a.sourceId AND a.inOutFlag = 0 AND f.complaintId IS NOT NULL
		LEFT JOIN st_reject_application g ON g.recordId = f.complaintId
		LEFT JOIN sl_contract_detail h ON h.recordId = f.contractDetailId
		LEFT JOIN icloud_group_center i ON i.recordId = IFNULL(e.groupCenterId,h.groupCenterId)
		LEFT JOIN pu_prdorder_detail j ON j.contractDetailId = IFNULL(e.recordId,h.recordId) AND j.companyId = a.companyId AND j.activeFlag = 1
		LEFT JOIN pu_material_check k ON k.prdorderDetailId = j.recordId AND k.activeFlag = 1 AND k.companyId = a.companyId
		AND CASE WHEN a.inOutFlag = 1 THEN k.oldOutBoundId = d.oldOutBoundId ELSE 1=1 END
		AND CASE WHEN IFNULL(c.recordId,g.recordId) IS NOT NULL
		THEN k.complaintId = IFNULL(c.recordId,g.recordId) ELSE 1=1 END AND k.period = a.period AND k.quantity = a.quantity
		LEFT JOIN md_customer mc ON mc.recordId = a.customerId
		LEFT JOIN sl_accounts_receivable sar ON a.customerId = sar.customerId AND sar.period = a.period AND sar.activeFlag = 1
		WHERE a.companyId = #{company.recordId} 
		AND a.activeFlag = 1 
		AND a.period LIKE CONCAT('%',#{period},'%')
		AND i.recordId IS NOT NULL
		<if test="customerNo != null and customerNo != ''">
				AND mc.no like CONCAT('%', #{customerNo}, '%')
		</if>
		<if test="customerName != null and customerName != ''">
				AND mc.name like CONCAT('%', #{customerName}, '%')
		</if>
		GROUP BY a.customerId
	</select>
	
	<select id="financeCostReportOut" resultType="FinanceCostVo">
		SELECT
			CASE WHEN a.inOutFlag = 1 THEN 
			(
				CASE WHEN b.complaintId IS NOT NULL THEN '客诉送货出库'
				ELSE '送货出库' END
			) ELSE '客诉退货入库' END 'sendType',
			a.recordId AS "收款对账单明细id",
			k.recordId AS "付款对账单明细id",
			mc.`no` AS 'customer.no',
			mc.`name` AS 'customer.name',
			a.customerId AS 'customer.recordId',
			a.period,
			sar.`status` AS 'operateStatus',
			a.quantity,
			a.amount,
			k.operateStatus AS 'payStatus',
			k.quantity AS 'payQuantity',
			k.amount AS 'payAmount',
			0 AS 'estimateCost',
			k.amount AS 'checkCost',
			k.amount AS 'billCost',
			'OUT' AS 'type',
			pp.price AS 'purPrice',
			IFNULL(pp.mouldFee,0)+IFNULL(pp.testShelfFee,0)+IFNULL(pp.engineeringFee,0) AS 'otherFee',
			IFNULL(pp.mouldFee,0)+IFNULL(pp.testShelfFee,0)+IFNULL(pp.engineeringFee,0) AS 'otherFee',
			(
						SELECT
							COUNT(aa.recordId)
						FROM
							sl_delivery_detail aa
						WHERE
							aa.contractDetailId = b.contractDetailId
						AND aa.activeFlag = 1
						AND aa.`status` = 500518
						AND aa.createdDate <![CDATA[<]]> b.createdDate
					) AS "deliveryNum"
		FROM sl_goods_check a
		LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId AND a.inOutFlag = 1
		LEFT JOIN st_reject_application c ON c.recordId = b.complaintId
		LEFT JOIN sl_outbound d ON d.recordId = b.terminalDetailId
		LEFT JOIN sl_contract_detail e ON e.recordId = b.contractDetailId
		LEFT JOIN st_product_store f ON f.recordId = a.sourceId AND a.inOutFlag = 0 AND f.complaintId IS NOT NULL
		LEFT JOIN st_reject_application g ON g.recordId = f.complaintId
		LEFT JOIN sl_contract_detail h ON h.recordId = f.contractDetailId
		LEFT JOIN icloud_group_center i ON i.recordId = IFNULL(e.groupCenterId,h.groupCenterId)
		LEFT JOIN pu_prdorder_detail j ON j.contractDetailId = IFNULL(e.recordId,h.recordId) AND j.companyId = a.companyId AND j.activeFlag = 1
		LEFT JOIN pu_material_check k ON k.prdorderDetailId = j.recordId AND k.activeFlag = 1 AND k.companyId = a.companyId
		AND CASE WHEN a.inOutFlag = 1 THEN k.oldOutBoundId = d.oldOutBoundId ELSE 1=1 END
		AND CASE WHEN IFNULL(c.recordId,g.recordId) IS NOT NULL
		THEN k.complaintId = IFNULL(c.recordId,g.recordId) ELSE 1=1 END AND k.period = a.period
		LEFT JOIN md_customer mc ON mc.recordId = a.customerId
		LEFT JOIN md_supplier ms ON ms.recordId = k.supplierId
		LEFT JOIN pu_price pp ON pp.recordId = j.priceId
		LEFT JOIN sl_accounts_receivable sar ON a.customerId = sar.customerId AND sar.period = a.period AND sar.activeFlag = 1
		WHERE 
		a.companyId = #{company.recordId} 
		AND a.activeFlag = 1 
		AND a.period LIKE CONCAT('%',#{period},'%')
		AND i.recordId IS NULL 
		<if test="customerNo != null and customerNo != ''">
				AND mc.no like CONCAT('%', #{customerNo}, '%')
		</if>
		<if test="customerName != null and customerName != ''">
				AND mc.name like CONCAT('%', #{customerName}, '%')
		</if>
				
	
	</select>
	
	<select id="FinanceCostList" resultType="FinanceCostVo">
		SELECT
			a.*,mc.recordId AS 'customer.recordId', mc.no AS 'customer.no',mc.name AS 'customer.name',u.userName AS salesman,d.`name` AS departName
		FROM
			sl_accounts_receivable a 
			LEFT JOIN md_customer mc ON mc.recordId = a.customerId
			LEFT JOIN sm_user u ON mc.salesman = u.recordId
			LEFT JOIN md_department d ON d.recordId = mc.deptId
		WHERE
			a.companyId = #{company.recordId} 
		AND a.activeFlag = 1
		AND a.period LIKE CONCAT('%',#{period},'%')
		<if test="customerNo != null and customerNo != ''">
				AND mc.no like CONCAT('%', #{customerNo}, '%')
		</if>
		<if test="customerName != null and customerName != ''">
				AND mc.name like CONCAT('%', #{customerName}, '%')
		</if>
		<!-- AND (IFNULL(a.receivedAmount,0)<![CDATA[<>]]>0 OR IFNULL(a.sentGoodsValue,0)<![CDATA[<>]]>0 OR IFNULL(a.returnGoodsValue,0)<![CDATA[<>]]>0 OR IFNULL(a.adjustValue,0)<![CDATA[<>]]>0) -->
		ORDER BY a.period DESC,a.sentGoodsValue DESC 
	</select>
	
	<select id="getFinanceCostVo" resultType="FinanceCostVo">
	SELECT r.madeFrom,ROUND(SUM(r.amount * IFNULL(r.exchangeRate,1)),2) AS 'amount',ROUND(SUM(r.madeAmount * IFNULL(r.exchangeRate,1)),2) AS 'estimateCost',SUM(r.quantity) AS 'quantity' FROM (
		SELECT
			( CASE WHEN i.recordId IS NOT NULL THEN "内部" ELSE "外协" END ) 'madeFrom',
		IF
			( a.inOutFlag = 0,- 1 * a.amount, a.amount ) AS 'amount',
		IF ( a.inOutFlag = 0,- 1, 1 )*
		IF
		(
			(
				SELECT
					COUNT( aa.recordId )
				FROM sl_delivery_detail aa
				WHERE aa.contractDetailId = b.contractDetailId
				AND aa.activeFlag = 1 AND aa.`status` = 500518
				AND aa.recordId <![CDATA[<]]> b.recordId
			)= 0,
			IF(a.inOutFlag = 1,IFNULL(IFNULL(pp.mouldFee,a.mouldFee), 0 )+ IFNULL(IFNULL(pp.testShelfFee,a.testShelfFee), 0 )+ IFNULL(IFNULL(pp.engineeringFee,a.engineeringFee), 0 )
			+ IFNULL(IFNULL(pp.othersFee,a.othersFee),0) + IFNULL(IFNULL(pp.filmFee,a.filmFee),0),0) +
			ROUND(IFNULL(IFNULL(pp.price,i.jxPrice) * a.quantity,0)*IF(i.recordId IS NOT NULL,1,(j.quantity/IFNULL( e.quantity, h.quantity ))),2),
			ROUND(IFNULL(IFNULL(pp.price,i.jxPrice) * a.quantity ,0)*IF(i.recordId IS NOT NULL,1,(j.quantity/IFNULL( e.quantity, h.quantity ))),2)
		)+ROUND(IF(a.inOutFlag = 0,- IFNULL(a.originalAmount,a.amount)+a.price*a.quantity, 0),2) AS 'madeAmount',
		IF  ( a.inOutFlag = 0,- 1 * a.quantity , a.quantity  ) AS 'quantity',
		l.exchangeRate AS "exchangeRate"
		FROM
			sl_goods_check a
			LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId 
			AND a.inOutFlag = 1
			LEFT JOIN sl_contract_detail e ON e.recordId = b.contractDetailId
			LEFT JOIN st_product_store f ON f.recordId = a.sourceId 
			AND a.inOutFlag = 0 
			AND f.complaintId IS NOT NULL 
			LEFT JOIN sl_contract_detail h ON h.recordId = f.contractDetailId
			LEFT JOIN pu_prdorder_detail j ON j.contractDetailId = IFNULL( e.recordId, h.recordId ) 
			AND j.companyId = a.companyId 
			AND j.activeFlag = 1
			LEFT JOIN pu_price pp ON pp.recordId = j.priceId
			LEFT JOIN icloud_group_center i ON i.recordId = IFNULL( e.groupCenterId, h.groupCenterId )
			LEFT JOIN sl_contract l ON l.recordId = IFNULL( e.contractId, h.contractId )
		WHERE
			a.period = #{period}
			AND a.customerId = #{customerId}
			AND a.companyId = #{companyId} 
			AND a.activeFlag = 1 
			AND a.type = 1 
			AND (
				a.adjustCheckFlag <![CDATA[<>]]> 12 
			OR ISNULL( a.adjustCheckFlag ))
			) r GROUP BY r.madeFrom
	
	</select>
	
	<select id="getInEstimateCost" resultType="AccountsReceivable">
		SELECT
			ROUND(SUM(IF(aa.complaintId IS NOT NULL OR aa.inOutType = 2,-1*aa.amount,aa.amount) * IFNULL(aa.exchangeRate,1)),2) AS "amount",
			ROUND(SUM(IF(aa.complaintId IS NOT NULL OR aa.inOutType = 2,-1*aa.fee,aa.fee) * IFNULL(aa.exchangeRate,1)),2) AS "tubePinFee"
		FROM
		(
			SELECT
				IF
				(
					(
						SELECT
						COUNT( aa.recordId )
						FROM sl_delivery_detail aa
						WHERE aa.contractDetailId = b.contractDetailId
						AND aa.activeFlag = 1 AND aa.`status` = 500518 AND aa.recordId <![CDATA[<]]> b.recordId
					)= 0,
					IF(a.inOutFlag = 1,IFNULL(IFNULL(pp.mouldFee,a.mouldFee), 0 )+ IFNULL(IFNULL(pp.testShelfFee,a.testShelfFee), 0 )+ IFNULL(IFNULL(pp.engineeringFee,a.engineeringFee), 0 )
					+ IFNULL(IFNULL(pp.othersFee,a.othersFee),0) + IFNULL(IFNULL(pp.filmFee,a.filmFee),0),0) +
					ROUND(IFNULL(IFNULL(pp.price,i.jxPrice) * a.quantity,0)*IF(i.recordId IS NOT NULL,1,(j.quantity/IFNULL( e.quantity, h.quantity ))),2),
					ROUND(IFNULL(IFNULL(pp.price,i.jxPrice) * a.quantity ,0)*IF(i.recordId IS NOT NULL,1,(j.quantity/IFNULL( e.quantity, h.quantity ))),2)
				) +ROUND(IF(a.inOutFlag = 0,- IFNULL(a.originalAmount,a.amount)+a.price*a.quantity, 0),2) AS 'amount',
				f.complaintId AS "complaintId",
				a.inOutType AS "inOutType",
				l.exchangeRate AS "exchangeRate",
				ROUND(IFNULL(a.originalAmount,a.amount) * 0.008,2) AS "fee"
				FROM
					sl_goods_check a
					LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId
					AND a.inOutFlag = 1
					LEFT JOIN sl_contract_detail e ON e.recordId = b.contractDetailId
					LEFT JOIN st_product_store f ON f.recordId = a.sourceId
					AND a.inOutFlag = 0
					AND f.complaintId
					IS NOT NULL LEFT JOIN sl_contract_detail h ON h.recordId = f.contractDetailId
					LEFT JOIN pu_prdorder_detail j ON j.contractDetailId = IFNULL( e.recordId, h.recordId )
					AND j.companyId = a.companyId
					AND j.activeFlag = 1
					LEFT JOIN pu_price pp ON pp.recordId = j.priceId
					LEFT JOIN icloud_group_center i ON i.recordId = IFNULL( e.groupCenterId, h.groupCenterId )
					LEFT JOIN sl_contract l ON l.recordId = IFNULL( e.contractId, h.contractId )
				WHERE
					a.period = #{period}
					AND a.companyId = #{companyId}
					AND a.activeFlag = 1
					AND a.type = 1
					AND (
						a.adjustCheckFlag <![CDATA[<>]]> 12
					OR ISNULL( a.adjustCheckFlag ))
				AND i.recordId IN (
					SELECT
					IFNULL( e.groupCenterId, h.groupCenterId ) AS 'groupCenterId'
				FROM
					sl_goods_check a
					LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId
					AND a.inOutFlag = 1
					LEFT JOIN sl_contract_detail e ON e.recordId = b.contractDetailId
					LEFT JOIN st_product_store f ON f.recordId = a.sourceId
					AND a.inOutFlag = 0
					AND f.complaintId
					IS NOT NULL LEFT JOIN sl_contract_detail h ON h.recordId = f.contractDetailId
					LEFT JOIN icloud_group_center i ON i.recordId = IFNULL( e.groupCenterId, h.groupCenterId )
					LEFT JOIN sl_contract l ON l.recordId = IFNULL(e.contractId,h.contractId)
				WHERE
					a.period = #{period}
					AND a.customerId = #{customerId}
					AND a.companyId = #{companyId}
					AND a.activeFlag = 1
					AND a.type = 1
					AND (
						a.adjustCheckFlag <![CDATA[<>]]> 12
					OR ISNULL( a.adjustCheckFlag ))
					AND i.recordId IS NOT NULL
				)
			) aa
	</select>
	
	<select id="getOutEstimateCost" resultType="BigDecimal">
	SELECT
			SUM(IF
				( a.inOutFlag = 0,- 1 * a.amount, a.amount )) AS 'amount'
			FROM
				sl_goods_check a
				LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId 
				AND a.inOutFlag = 1
				LEFT JOIN sl_contract_detail e ON e.recordId = b.contractDetailId
				LEFT JOIN st_product_store f ON f.recordId = a.sourceId 
				AND a.inOutFlag = 0 
				AND f.complaintId
				IS NOT NULL LEFT JOIN sl_contract_detail h ON h.recordId = f.contractDetailId
				LEFT JOIN pu_prdorder_detail j ON j.contractDetailId = IFNULL( e.recordId, h.recordId ) 
				AND j.companyId = a.companyId 
				AND j.activeFlag = 1
				LEFT JOIN pu_price pp ON pp.recordId = j.priceId
				LEFT JOIN icloud_group_center i ON i.recordId = IFNULL( e.groupCenterId, h.groupCenterId ) 
			WHERE
				a.period = #{period}  
				AND a.companyId = #{proCompanyId}
				AND a.activeFlag = 1 
				AND a.type = 1 
				AND (
					a.adjustCheckFlag <![CDATA[<>]]> 12 
				OR ISNULL( a.adjustCheckFlag )) 
				AND i.recordId IS NULL
			AND IFNULL( e.groupCenterId, h.groupCenterId )  IN (
				SELECT
				IFNULL( e.groupCenterId, h.groupCenterId ) AS 'groupCenterId'
			FROM
				sl_goods_check a
				LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId 
				AND a.inOutFlag = 1
				LEFT JOIN sl_contract_detail e ON e.recordId = b.contractDetailId
				LEFT JOIN st_product_store f ON f.recordId = a.sourceId 
				AND a.inOutFlag = 0 
				AND f.complaintId
				IS NOT NULL LEFT JOIN sl_contract_detail h ON h.recordId = f.contractDetailId
				LEFT JOIN icloud_group_center i ON i.recordId = IFNULL( e.groupCenterId, h.groupCenterId ) 
			WHERE
				a.period = #{period}   
		  		AND a.customerId = #{customerId} 
				AND a.companyId = #{companyId} 
				AND a.activeFlag = 1 
				AND a.type = 1 
				AND (
					a.adjustCheckFlag <![CDATA[<>]]> 12 
				OR ISNULL( a.adjustCheckFlag )) 
				AND i.recordId IS NULL
			)
	
	</select>
	
	<select id="getOutPayCost" resultType="BigDecimal">
	
		SELECT
			SUM(IF(a.inOutFlag=0,-1*a.amount,a.amount))
		FROM
			pu_material_check a
			LEFT JOIN md_material b ON b.recordId = a.materialId
			LEFT JOIN st_product_store bb ON bb.recordId = a.sourceId 
			AND b.materialKind = 100702
			LEFT JOIN pu_prdorder_detail c ON c.recordId = bb.prdorderDetailId
			LEFT JOIN pu_prdorder_detail ppd ON ppd.recordId = a.prdorderDetailId
			LEFT JOIN pu_returns_detail prd ON prd.recordId = a.sourceId 
			AND prd.purchasingType = 2
			LEFT JOIN pu_prdorder_detail ppd2 ON ppd2.recordId = prd.purchasingDetailId
			LEFT JOIN sl_contract_detail d ON d.recordId = IFNULL(
				IFNULL( a.contractDetailId, c.contractDetailId ),
			IFNULL( ppd.contractDetailId, ppd2.contractDetailId ))
			LEFT JOIN icloud_group_center igc ON igc.recordId = d.groupCenterId
			LEFT JOIN sl_contract sc ON sc.recordId = d.contractId
			LEFT JOIN md_customer mc ON mc.recordId = sc.customerId
		WHERE
			a.companyId = #{companyId}  
			AND a.activeFlag = 1 
			AND ( a.adjustCheckFlag <![CDATA[<>]]> 12 OR ISNULL( a.adjustCheckFlag ) ) 
			AND a.period = #{period} 
			AND b.materialKind = 100702
			AND igc.recordId IS NULL
			AND mc.recordId = #{customerId}
		GROUP BY mc.recordId
	
	</select>
	
	
	<select id="getOutOtherList" resultType="FinanceCostVo">
		SELECT
			DISTINCT mc.no AS 'customer.no',mc.name AS 'customer.name',a.period,mc.recordId AS 'customer.recordId',mc.recordId AS 'customerId'
			,SUM(IF(a.inOutFlag=0,-1*a.amount,a.amount)) AS 'outEstimateCost'
		FROM
			pu_material_check a
			LEFT JOIN md_material b ON b.recordId = a.materialId
			LEFT JOIN st_product_store bb ON bb.recordId = a.sourceId 
			AND b.materialKind = 100702
			LEFT JOIN pu_prdorder_detail c ON c.recordId = bb.prdorderDetailId
			LEFT JOIN pu_prdorder_detail ppd ON ppd.recordId = a.prdorderDetailId
			LEFT JOIN pu_returns_detail prd ON prd.recordId = a.sourceId 
			AND prd.purchasingType = 2
			LEFT JOIN pu_prdorder_detail ppd2 ON ppd2.recordId = prd.purchasingDetailId
			LEFT JOIN sl_contract_detail d ON d.recordId = IFNULL(
				IFNULL( a.contractDetailId, c.contractDetailId ),
			IFNULL( ppd.contractDetailId, ppd2.contractDetailId ))
			LEFT JOIN icloud_group_center igc ON igc.recordId = d.groupCenterId
			LEFT JOIN sl_contract sc ON sc.recordId = d.contractId
			LEFT JOIN md_customer mc ON mc.recordId = sc.customerId
		WHERE
			a.companyId = #{company.recordId} 
			AND a.activeFlag = 1 
			AND ( a.adjustCheckFlag <![CDATA[<>]]> 12 OR ISNULL( a.adjustCheckFlag ) ) 
			AND a.period LIKE CONCAT('%',#{period},'%')
			AND b.materialKind = 100702
			AND igc.recordId IS NULL
			<if test="customerNo != null and customerNo != ''">
					AND mc.no like CONCAT('%', #{customerNo}, '%')
			</if>
			<if test="customerName != null and customerName != ''">
					AND mc.name like CONCAT('%', #{customerName}, '%')
			</if>
			AND mc.recordId NOT IN (
				SELECT
				DISTINCT mc.recordId
			FROM
				sl_accounts_receivable a 
				LEFT JOIN md_customer mc ON mc.recordId = a.customerId
			WHERE
				a.companyId = #{company.recordId} 
			AND a.activeFlag = 1
			AND a.period LIKE CONCAT('%',#{period},'%')
			<if test="customerNo != null and customerNo != ''">
					AND mc.no like CONCAT('%', #{customerNo}, '%')
			</if>
			<if test="customerName != null and customerName != ''">
					AND mc.name like CONCAT('%', #{customerName}, '%')
			</if>
			<!-- AND (IFNULL(a.receivedAmount,0)<![CDATA[<>]]>0 OR IFNULL(a.sentGoodsValue,0)<![CDATA[<>]]>0 OR IFNULL(a.returnGoodsValue,0)<![CDATA[<>]]>0 OR IFNULL(a.adjustValue,0)<![CDATA[<>]]>0) -->
			ORDER BY a.period DESC,a.sentGoodsValue DESC 
			)
			GROUP BY mc.recordId,a.period
		
		
	</select>
	
	<select id="receiveCostTotalReport" resultType="FinanceCostVo">
		SELECT
			r.shortName AS 'company.shortName',r.period,r.companyId ,
			r.madeFrom,
			r.billStatus,
			ROUND(SUM(r.amount),2) AS 'amount',
			SUM(quantity) AS 'quantity'
		FROM 
		(
		SELECT
				( CASE WHEN i.recordId IS NOT NULL THEN "内部" ELSE "外协" END ) 'madeFrom',
				IF
				( sar.`status` = 2003 OR sar.`status` = 60001, '实际', '估算' ) AS 'billStatus',
			IF
				( a.inOutFlag = 0,- 1 * a.amount, a.amount ) AS 'amount',
			IF
				( a.inOutFlag = 0,- 1 * a.quantity, a.quantity ) AS 'quantity' ,
				a.companyId,
				a.period,
				mc.shortName
				
			FROM
				sl_goods_check a
				LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId 
				AND a.inOutFlag = 1
				LEFT JOIN sl_contract_detail e ON e.recordId = b.contractDetailId
				LEFT JOIN st_product_store f ON f.recordId = a.sourceId 
				AND a.inOutFlag = 0 
				AND f.complaintId
				IS NOT NULL LEFT JOIN sl_contract_detail h ON h.recordId = f.contractDetailId
				LEFT JOIN pu_prdorder_detail j ON j.contractDetailId = IFNULL( e.recordId, h.recordId ) 
				AND j.companyId = a.companyId 
				AND j.activeFlag = 1
				LEFT JOIN pu_price pp ON pp.recordId = j.priceId
				LEFT JOIN icloud_group_center i ON i.recordId = IFNULL( e.groupCenterId, h.groupCenterId ) 
				LEFT JOIN sl_accounts_receivable sar ON sar.period = a.period AND sar.customerId = a.customerId AND sar.activeFlag = 1
				LEFT JOIN md_company mc ON mc.recordId = a.companyId
			WHERE
				 a.period LIKE CONCAT('%',#{period},'%')
				AND a.companyId IN (3,15,205,17) 
				AND a.activeFlag = 1 
				AND a.type = 1 
				AND (
					a.adjustCheckFlag <![CDATA[<>]]> 12 
				OR ISNULL( a.adjustCheckFlag )) 
				
				) r 
				GROUP BY r.period,r.companyId,r.billStatus,r.madeFrom
				ORDER BY r.period ,r.companyId,r.billStatus DESC
	
	</select>
	
	<select id="payCostTotalReport" resultType="FinanceCostVo">
			SELECT
				r.shortName AS 'company.shortName',r.period,r.companyId ,
				r.madeFrom,
				r.billStatus,
				ROUND(SUM(r.amount),2) AS 'amount',
				SUM(quantity) AS 'quantity'
			FROM 
			(	
					SELECT 
				( CASE WHEN igc.recordId IS NOT NULL THEN "内部" ELSE "外协" END ) 'madeFrom',
					IFNULL(rec.billStatus,'估算') AS 'billStatus',
				IF
					( a.inOutFlag = 0,- 1 * a.amount, a.amount ) AS 'amount',
				IF
					( a.inOutFlag = 0,- 1 * a.quantity, a.quantity ) AS 'quantity' ,
					a.companyId,
					a.period,
					cc.shortName
				
			FROM
				pu_material_check a
				LEFT JOIN md_material b ON b.recordId = a.materialId
				LEFT JOIN st_product_store bb ON bb.recordId = a.sourceId 
				AND b.materialKind = 100702
				LEFT JOIN pu_prdorder_detail c ON c.recordId = bb.prdorderDetailId
				LEFT JOIN pu_prdorder_detail ppd ON ppd.recordId = a.prdorderDetailId
				LEFT JOIN pu_returns_detail prd ON prd.recordId = a.sourceId 
				AND prd.purchasingType = 2
				LEFT JOIN pu_prdorder_detail ppd2 ON ppd2.recordId = prd.purchasingDetailId
				LEFT JOIN sl_contract_detail d ON d.recordId = IFNULL(
					IFNULL( a.contractDetailId, c.contractDetailId ),
				IFNULL( ppd.contractDetailId, ppd2.contractDetailId ))
				LEFT JOIN icloud_group_center igc ON igc.recordId = d.groupCenterId
				LEFT JOIN sl_contract sc ON sc.recordId = d.contractId
				LEFT JOIN md_customer mc ON mc.recordId = sc.customerId 
				LEFT JOIN pu_accounts_payable pap ON pap.period = a.period AND pap.supplierId = a.supplierId AND pap.activeFlag = 1
				LEFT JOIN md_company cc ON cc.recordId = a.companyId
				LEFT JOIN (
				SELECT
			   IFNULL( e.groupCenterId, h.groupCenterId ) AS 'groupCenterId',
				 IF(sar.`status` = 2003 OR sar.`status` = 60001,'实际','估算') AS 'billStatus',a.companyId
			FROM
				sl_goods_check a
				LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId 
				AND a.inOutFlag = 1
				LEFT JOIN sl_contract_detail e ON e.recordId = b.contractDetailId
				LEFT JOIN st_product_store f ON f.recordId = a.sourceId 
				AND a.inOutFlag = 0 
				AND f.complaintId
				IS NOT NULL LEFT JOIN sl_contract_detail h ON h.recordId = f.contractDetailId
				LEFT JOIN sl_accounts_receivable sar ON sar.period = a.period AND sar.customerId = a.customerId AND sar.activeFlag = 1
			WHERE
				 a.period LIKE CONCAT('%',#{period},'%')
				AND a.companyId IN (3,15,205,17) 
				AND a.activeFlag = 1 
				AND a.type = 1 
				AND (
					a.adjustCheckFlag <![CDATA[<>]]> 12 
				OR ISNULL( a.adjustCheckFlag )) 
				AND IFNULL( e.groupCenterId, h.groupCenterId ) IS NOT NULL
				GROUP BY IFNULL( e.groupCenterId, h.groupCenterId ),a.companyId
				) rec ON rec.groupCenterId = d.groupCenterId AND rec.companyId = a.companyId
			WHERE
				a.companyId IN (3,15,205,17) 
				AND a.activeFlag = 1 
				AND ( a.adjustCheckFlag <![CDATA[<>]]> 12 OR ISNULL( a.adjustCheckFlag ) ) 
				AND a.period LIKE CONCAT('%',#{period},'%')
				AND b.materialKind = 100702 
			
			) r 
					GROUP BY r.period,r.companyId,r.billStatus,r.madeFrom
					ORDER BY r.period ,r.companyId,r.billStatus DESC
	
	</select>
	
	<select id="receiveLnCostTotalReport" resultType="FinanceCostVo">
		SELECT
			r.shortName AS 'company.shortName',
			r.period,
			r.companyId,
			r.madeFrom,
			r.billStatus,
			ROUND( SUM( r.amount ), 2 ) AS 'amount',
			SUM( quantity ) AS 'quantity' 
		FROM
			(
			SELECT
				( CASE WHEN i.recordId IS NOT NULL THEN "内部" ELSE "外协" END ) 'madeFrom',
			IF
				( sar.`status` = 2003 OR sar.`status` = 60001, '实际', '估算' ) AS 'billStatus',
			IF
				( a.inOutFlag = 0,- 1 * a.amount, a.amount ) AS 'amount',
			IF
				( a.inOutFlag = 0,- 1 * a.quantity, a.quantity ) AS 'quantity',
				a.companyId,
				a.period,
				mc.shortName 
			FROM
				sl_goods_check a
				LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId 
				AND a.inOutFlag = 1
				LEFT JOIN sl_contract_detail e ON e.recordId = b.contractDetailId
				LEFT JOIN st_product_store f ON f.recordId = a.sourceId 
				AND a.inOutFlag = 0 
				AND f.complaintId
				IS NOT NULL LEFT JOIN sl_contract_detail h ON h.recordId = f.contractDetailId
				LEFT JOIN pu_prdorder_detail j ON j.contractDetailId = IFNULL( e.recordId, h.recordId ) 
				AND j.companyId = a.companyId 
				AND j.activeFlag = 1
				LEFT JOIN pu_price pp ON pp.recordId = j.priceId
				LEFT JOIN icloud_group_center i ON i.recordId = IFNULL( e.groupCenterId, h.groupCenterId )
				LEFT JOIN sl_accounts_receivable sar ON sar.period = a.period 
				AND sar.customerId = a.customerId 
				AND sar.activeFlag = 1
				LEFT JOIN md_company mc ON mc.recordId = a.companyId 
			WHERE
				a.period LIKE CONCAT( '%',#{period}, '%' ) 
				AND a.companyId = 17
				AND a.activeFlag = 1 
				AND a.customerId = 31147
				AND a.type = 1 
				AND (
					a.adjustCheckFlag <![CDATA[<>]]> 12 
				OR ISNULL( a.adjustCheckFlag )) 
			) r 
		GROUP BY
			r.period,
			r.companyId,
			r.billStatus,
			r.madeFrom 
		ORDER BY
			r.period,
			r.companyId,
			r.billStatus DESC
	</select>
	
	<select id="payLnCostTotalReport" resultType="FinanceCostVo">
	
		SELECT
			r.shortName AS 'company.shortName',
			r.period,
			r.companyId,
			r.madeFrom,
			r.billStatus,
			ROUND( SUM( r.amount ), 2 ) AS 'amount',
			SUM( quantity ) AS 'quantity' 
		FROM
			(
			SELECT
				( CASE WHEN igc.recordId IS NOT NULL THEN "内部" ELSE "外协" END ) 'madeFrom',
			IF
				( pap.`status` = 2003 OR pap.`status` = 60001, '实际', '估算' ) AS 'billStatus',
			IF
				( a.inOutFlag = 0,- 1 * a.amount, a.amount ) AS 'amount',
			IF
				( a.inOutFlag = 0,- 1 * a.quantity, a.quantity ) AS 'quantity',
				a.companyId,
				a.period,
				cc.shortName 
			FROM
				pu_material_check a
				LEFT JOIN md_material b ON b.recordId = a.materialId
				LEFT JOIN st_product_store bb ON bb.recordId = a.sourceId 
				AND b.materialKind = 100702
				LEFT JOIN pu_prdorder_detail c ON c.recordId = bb.prdorderDetailId
				LEFT JOIN pu_prdorder_detail ppd ON ppd.recordId = a.prdorderDetailId
				LEFT JOIN pu_returns_detail prd ON prd.recordId = a.sourceId 
				AND prd.purchasingType = 2
				LEFT JOIN pu_prdorder_detail ppd2 ON ppd2.recordId = prd.purchasingDetailId
				LEFT JOIN sl_contract_detail d ON d.recordId = IFNULL(
					IFNULL( a.contractDetailId, c.contractDetailId ),
				IFNULL( ppd.contractDetailId, ppd2.contractDetailId ))
				LEFT JOIN icloud_group_center igc ON igc.recordId = d.groupCenterId
				LEFT JOIN sl_contract sc ON sc.recordId = d.contractId
				LEFT JOIN md_customer mc ON mc.recordId = sc.customerId
				LEFT JOIN pu_accounts_payable pap ON pap.period = a.period 
				AND pap.supplierId = a.supplierId 
				AND pap.activeFlag = 1
				LEFT JOIN md_company cc ON cc.recordId = a.companyId 
			WHERE
				a.companyId = 17
				AND mc.recordId = 31147
				AND a.activeFlag = 1 
				AND ( a.adjustCheckFlag <![CDATA[<>]]> 12 OR ISNULL( a.adjustCheckFlag ) ) 
				AND a.period LIKE CONCAT( '%',#{period}, '%' ) 
				AND b.materialKind = 100702 
			) r 
		GROUP BY
			r.period,
			r.companyId,
			r.billStatus,
			r.madeFrom 
		ORDER BY
			r.period,
			r.companyId,
			r.billStatus DESC
	
	</select>
	
	<select id="getAdjustList" resultType="FinanceCostVo">
		SELECT
			a.companyId,
			SUM( a.adjustValue ) amount,
		IF
			( a.`status` = 2003 OR a.`status` = 60001, '实际', '估算' ) AS 'billStatus' 
		FROM
			sl_accounts_receivable a 
		WHERE
			a.period LIKE CONCAT( '%', #{period}, '%' ) 
			AND a.companyId IN ( 3, 15, 205, 17 ) 
			AND a.activeFlag = 1 
		GROUP BY
			a.companyId,
		IF
			(
				a.`status` = 2003 
				OR a.`status` = 60001,
				'实际',
			'估算' 
			)
	
	</select>

	<update id="updateCheckOutAmount">
		UPDATE sl_accounts_receivable aa
		LEFT JOIN
		(
			SELECT
				a.customerId AS "customerId",
				a.period AS "period",
				SUM(IFNULL(a.amount,0)) AS "checkOutAmount"
			FROM sl_goods_check a
			WHERE a.companyId = #{company.recordId} AND a.customerId = #{customer.recordId} AND a.period = #{period}
			AND a.activeFlag = 1 AND a.inOutFlag = 4 AND a.checkOutType IS NOT NULL
		) bb ON bb.customerId = aa.customerId AND bb.period = aa.period
		SET aa.checkOutAmount = bb.checkOutAmount
		WHERE aa.companyId = #{company.recordId} AND aa.customerId = #{customer.recordId} AND aa.period = #{period}
		AND aa.activeFlag = 1
	</update>

	<select id="getPrincipalTotalList" resultType="AccountsReceivable">
		SELECT
			period,
			`status`,
			COUNT(1) AS "quantity"
		FROM sl_accounts_receivable
		WHERE companyId = #{company.recordId} AND activeFlag = 1 AND status <![CDATA[<>]]> 2003
		<if test="period != null and period != ''">
			AND period <![CDATA[>=]]> #{period}
		</if>
		GROUP BY period,`status`
	</select>

	<update id="updateOtherAmountData">
		UPDATE sl_accounts_receivable a
		INNER JOIN
		(
			SELECT
				customerId AS "customerId",
				period AS "period",
				receivedAmount AS "receivedAmount"
			FROM sl_single_receivable
			WHERE companyId = #{company.recordId} AND customerId = #{customer.recordId} AND period = #{period}
		) b ON b.customerId = a.customerId AND b.period = a.period
		SET a.receivedAmount = b.receivedAmount,a.completeFlag = 0
	</update>

	<update id="updateStatusData">
		UPDATE sl_accounts_receivable SET
			status = #{status},closeBook = #{closeBook},closeTime = NOW()
		WHERE FIND_IN_SET(customerId,#{customerId})
		AND period = #{period} AND activeFlag = 1
	</update>

	<update id="updateStatusTwoData">
		UPDATE sl_accounts_receivable aa
		INNER JOIN
		(
			SELECT
				customerId,
				period,
				SUM(CASE operateStatus WHEN 1001 THEN 1 ELSE 0 END) AS "count"
			FROM sl_goods_check
			WHERE FIND_IN_SET(customerId,#{customerId}) AND period = #{period} AND activeFlag = 1
			GROUP BY customerId,period
		) bb ON bb.customerId = aa.customerId AND bb.period = aa.period
		SET aa.`status` = #{status}
		WHERE IFNULL(bb.count,0) = 0
	</update>

	<select id="getMaxAccountsReceivable" resultType="AccountsReceivable">
		SELECT
		 period,
		 status
		FROM sl_accounts_receivable WHERE customerId = #{customer.recordId}AND activeFlag = 1
		AND period > #{period} LIMIT 1;
	</select>

	<insert id="saveReceivable">
		INSERT INTO sl_accounts_receivable
		(
			companyId,
			customerId,
			period,
			sentGoodsValue,
			returnGoodsValue,
			activeFlag,
			createdDate,
			lastUpdDate,
			`status`,
			adjustFlag
		) VALUES(
			#{company.recordId},
			#{customer.recordId},
			#{period},
			0,
			0,
			1,
			NOW(),
			NOW(),
			2001,
			1
		)
	</insert>

	<update id="updateAdjustValue">
		UPDATE sl_accounts_receivable SET
			adjustValue = #{adjustValue}
		WHERE recordId = #{recordId}
	</update>

	<update id="updateAccountRecodStatus">
	UPDATE sl_accounts_receivable SET `status` = #{status}
	WHERE
	activeFlag = 1
	AND period = #{period}
	AND FIND_IN_SET(customerId,#{customerId})
	</update>

	<select id="getMoneyCompanyId" resultType="String">
		select companyId from md_customer WHERE activeFlag = 1 AND recordId = #{customerId}
	</select>

	<select id="getCollectionCompany" resultType="AccountsReceivable">
		SELECT
			i.`name` AS "departName",
			h.groupOrgId AS "groupOrgId",
			a.recordId,
			CASE a.inOutFlag WHEN 1 THEN a.amount WHEN 0 THEN -a.amount ELSE 0 END AS "amount",
			a.companyId AS "companyId",
			j.`name` AS "companyName"
		FROM sl_goods_check a
		LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId AND a.inOutFlag = 1
		LEFT JOIN st_product_store c ON c.recordId = a.sourceId AND a.inOutFlag = 0
		LEFT JOIN sl_contract_detail d ON d.recordId = IFNULL(b.contractDetailId,c.contractDetailId)
		LEFT JOIN icloud_group_center e ON e.recordId = d.groupCenterId
		LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(e.contactDeailId,d.groupCenterId)
		LEFT JOIN sl_contract g ON g.recordId = f.contractId
		LEFT JOIN icloud_group_org_relation h ON h.deptId = g.deptId AND h.activeFlag = 1
		LEFT JOIN icloud_group_org i ON i.recordId = h.groupOrgId
		LEFT JOIN md_company j ON j.recordId = a.companyId
		WHERE a.activeFlag = 1
		<if test="companyIdList != null and companyIdList.size > 0">
			AND (
				a.companyId
			) IN
			<foreach collection="companyIdList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="innerFlag != null and innerFlag != '' and innerFlag == 1">
			AND d.recordId = f.recordId
		</if>
		AND a.period >= DATE_FORMAT(#{sentTimeStartQr},'%Y%m')
		AND a.period <![CDATA[<=]]> DATE_FORMAT(#{sentTimeEndQr},'%Y%m')
	</select>

	<select id="getAmountReceivableList" resultType="AccountsReceivable">
		SELECT
		i.`name` AS "departName",
		h.groupOrgId AS "groupOrgId",
		IFNULL((CASE
		a.type
		WHEN 1 THEN
		a.amount
		WHEN 2 THEN
		a.amount
		WHEN 3 THEN
		- a.amount ELSE a.amount
		END),0)
		AS "amountReceivable",
		IFNUll(
		(CASE
		a.type
		WHEN 1 THEN
		a.confirmAmount
		WHEN 2 THEN
		a.confirmAmount
		WHEN 3 THEN
		- a.confirmAmount ELSE a.confirmAmount
		END),0)
		AS "amountReceived",
		a.companyId AS "companyId",
		j.`name` AS "companyName"
		FROM
		sl_single_receivable_detail a
		LEFT JOIN sl_goods_check sgc ON sgc.recordId = a.goodsCheckId
		LEFT JOIN sl_delivery_detail b ON b.recordId = sgc.sourceId AND sgc.inOutFlag = 1
		LEFT JOIN st_product_store c ON c.recordId = sgc.sourceId AND sgc.inOutFlag = 0
		LEFT JOIN sl_contract_detail d ON d.recordId = IFNULL( b.contractDetailId, c.contractDetailId )
		LEFT JOIN icloud_group_center e ON e.recordId = d.groupCenterId
		LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL( e.contactDeailId, d.groupCenterId )
		LEFT JOIN sl_contract g ON g.recordId = f.contractId
		LEFT JOIN icloud_group_org_relation h ON h.deptId = g.deptId AND h.activeFlag = 1
		LEFT JOIN icloud_group_org i ON i.recordId = h.groupOrgId
		LEFT JOIN md_company j ON j.recordId = a.companyId
		WHERE a.activeFlag = 1
		<if test="companyIdList != null and companyIdList.size > 0">
			AND (
				a.companyId
			) IN
			<foreach collection="companyIdList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		<if test="innerFlag != null and innerFlag != '' and innerFlag == 1">
			AND d.recordId = f.recordId
		</if>
		AND sgc.period >= DATE_FORMAT(#{sentTimeStartQr}, '%Y%m' )
		AND sgc.period <![CDATA[<=]]> DATE_FORMAT(#{sentTimeEndQr}, '%Y%m' )
	</select>

	<select id="getNotReconciliationList" resultType="AccountsReceivable">
		SELECT
			IFNULL( sentGoodsValue, 0 ) + IFNULL( adjustValue, 0 ) - IFNULL( returnGoodsValue, 0 ) - IFNULL( receivedAmount, 0 ) AS " unreconciledAmountTotal",
			b.recordId AS "companyId",
			b.name AS "companyName",
			IFNULL(f.recordId,-1) AS "groupOrgId"
		FROM sl_accounts_receivable a
		LEFT JOIN md_company b ON b.recordId = a.companyId
		LEFT JOIN md_customer c ON c.recordId = a.customerId
		LEFT JOIN md_department d ON d.recordId = c.deptId
		LEFT JOIN icloud_group_org_relation e ON e.deptId = d.recordId AND e.activeFlag = 1
		LEFT JOIN icloud_group_org f ON f.recordId = e.groupOrgId
		WHERE a.activeFlag = 1
		<if test="companyIdList != null and companyIdList.size > 0">
			AND (
				a.companyId
			) IN
			<foreach collection="companyIdList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		AND IFNULL( a.`status`, 2001 ) <![CDATA[<>]]> 2003
	</select>

	<select id="getReceivableNotList" resultType="AccountsReceivable">
		SELECT
			IFNULL( a.sentGoodsValue, 0 ) + IFNULL( a.adjustValue, 0 ) - IFNULL( a.returnGoodsValue, 0 ) - IFNULL( a.receivedAmount, 0 ) AS "amountNotReceivableTotal" ,
			b.recordId AS "companyId",
			b.name AS "companyName",
			IFNULL(f.recordId,-1) AS "groupOrgId"
		FROM
		sl_single_receivable a
		LEFT JOIN md_company b ON b.recordId = a.companyId
		LEFT JOIN md_customer c ON c.recordId = a.customerId
		LEFT JOIN md_department d ON d.recordId = c.deptId
		LEFT JOIN icloud_group_org_relation e ON e.deptId = d.recordId AND e.activeFlag = 1
		LEFT JOIN icloud_group_org f ON f.recordId = e.groupOrgId
		WHERE a.activeFlag = 1
		<if test="companyIdList != null and companyIdList.size > 0">
			AND (
				a.companyId
			) IN
			<foreach collection="companyIdList" item="item" index="index" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		AND IFNULL( a.`status`, 2001 ) <![CDATA[<>]]> 2003
	</select>

	<select id="getNotReconciliationDetailList" resultType="AccountsReceivable">
		SELECT
			a.receivedAmount AS "receivedAmount",
			a.returnGoodsValue AS "returnGoodsValue",
			a.sentGoodsValue AS "sentGoodsValue",
			a.adjustValue AS "adjustValue",
			IFNULL( sentGoodsValue, 0 ) + IFNULL( adjustValue, 0 ) - IFNULL( returnGoodsValue, 0 ) - IFNULL( receivedAmount, 0 ) AS " unreconciledAmountTotal",
			b.recordId AS "companyId",
			b.name AS "companyName",
			c.name AS "customer.name"
		FROM
		sl_accounts_receivable a
		LEFT JOIN md_company b ON b.recordId = a.companyId
		LEFT JOIN md_customer c ON c.recordId = a.customerId
		LEFT JOIN md_department d ON d.recordId = c.deptId
		LEFT JOIN icloud_group_org_relation e ON e.deptId = d.recordId AND e.activeFlag = 1
		LEFT JOIN icloud_group_org f ON f.recordId = e.groupOrgId
		WHERE
		a.activeFlag = 1
		<if test="selectStatus != null and selectStatus != '' and (selectStatus == 2 or selectStatus == 3)">
			AND a.companyId = #{erpCompanyId}
		</if>
		<if test="selectStatus != null and selectStatus != '' and selectStatus == 3">
			AND CASE #{groupDepartId} WHEN -1 THEN f.recordId IS NULL ELSE f.recordId = #{groupDepartId} END
		</if>
		<if test="customerName != null and customerName != ''">
			AND c.name like CONCAT('%', #{customerName}, '%')
		</if>
		AND IFNULL( a.`status`, 2001 ) <![CDATA[<>]]> 2003
	</select>

	<select id="getReceivableNotDetailList" resultType="AccountsReceivable">
		SELECT
			a.receivedAmount AS "receivedAmount",
			a.returnGoodsValue AS "returnGoodsValue",
			a.adjustValue AS "adjustValue",
			a.sentGoodsValue AS "sentGoodsValue",
			IFNULL( a.sentGoodsValue, 0 ) + IFNULL( a.adjustValue, 0 ) - IFNULL( a.returnGoodsValue, 0 ) - IFNULL( a.receivedAmount, 0 ) AS "amountNotReceivableTotal" ,
			b.recordId AS "companyId",
			b.name AS "companyName",
			c.name AS "customer.name"
		FROM
		sl_single_receivable a
		LEFT JOIN md_company b ON b.recordId = a.companyId
		LEFT JOIN md_customer c ON c.recordId = a.customerId
		LEFT JOIN md_department d ON d.recordId = c.deptId
		LEFT JOIN icloud_group_org_relation e ON e.deptId = d.recordId AND e.activeFlag = 1
		LEFT JOIN icloud_group_org f ON f.recordId = e.groupOrgId
		WHERE
		a.activeFlag = 1
		<if test="selectStatus != null and selectStatus != '' and (selectStatus == 2 or selectStatus == 3)">
			AND a.companyId = #{erpCompanyId}
		</if>
		<if test="selectStatus != null and selectStatus != '' and selectStatus == 3">
		    AND CASE #{groupDepartId} WHEN -1 THEN f.recordId IS NULL ELSE f.recordId = #{groupDepartId} END
		</if>
		<if test="customerName != null and customerName != ''">
			AND c.name like CONCAT('%', #{customerName}, '%')
		</if>
		AND IFNULL( a.`status`, 2001 ) <![CDATA[<>]]> 2003
	</select>

	<select id="getCollectionCompanyDetailList" resultType="AccountsReceivable">
		SELECT
		i.`name` AS "departName",
		h.groupOrgId AS "groupOrgId",
		a.recordId,
		CASE a.inOutFlag WHEN 1 THEN a.amount WHEN 0 THEN -a.amount ELSE 0 END AS "amount",
		a.companyId AS "companyId",
		j.`name` AS "companyName",
		k.name AS "customer.name",
		l.`no` AS "craftNo",
		g.`no` AS "contractNo",
		g.customerPo AS "customerPo",
		l.customerModel AS "customerModel",
		b.quantity,
		m.price
		FROM sl_goods_check a
		LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId AND a.inOutFlag = 1
		LEFT JOIN st_product_store c ON c.recordId = a.sourceId AND a.inOutFlag = 0
		LEFT JOIN sl_contract_detail d ON d.recordId = IFNULL(b.contractDetailId,c.contractDetailId)
		LEFT JOIN icloud_group_center e ON e.recordId = d.groupCenterId
		LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(e.contactDeailId,d.groupCenterId)
		LEFT JOIN sl_contract g ON g.recordId = f.contractId
		LEFT JOIN icloud_group_org_relation h ON h.deptId = g.deptId AND h.activeFlag = 1
		LEFT JOIN icloud_group_org i ON i.recordId = h.groupOrgId
		LEFT JOIN md_company j ON j.recordId = a.companyId
		LEFT JOIN md_customer k ON k.recordId = g.customerId
		LEFT JOIN sl_contract_craft l ON l.recordId = f.craftId
		LEFT JOIN sl_price m ON m.recordId =  f.priceId
		WHERE
		a.activeFlag = 1
		AND h.groupOrgId IS NOT NULL
		<if test="erpCompanyId != null and erpCompanyId != '' and (selectStatus == 2 or selectStatus == 3)">
			AND a.companyId = #{erpCompanyId}
		</if>
		AND d.recordId = f.recordId
		AND a.period >= DATE_FORMAT(#{sentTimeStartQr},'%Y%m')
		AND a.period <![CDATA[<=]]> DATE_FORMAT(#{sentTimeEndQr},'%Y%m')
		<if test="groupDepartId != null and groupDepartId != '' and selectStatus == 3">
			AND CASE #{groupDepartId} WHEN -1 THEN h.groupOrgId IS NULL ELSE h.groupOrgId = #{groupDepartId} END
		</if>
	</select>

	<select id="getAmountReceivableDetailList" resultType="AccountsReceivable">
		SELECT
		i.`name` AS "departName",
		h.groupOrgId AS "groupOrgId",
		IFNULL((CASE
		a.type
		WHEN 1 THEN
		a.amount
		WHEN 2 THEN
		a.amount
		WHEN 3 THEN
		- a.amount ELSE 0
		END),0)
		AS "amountReceivable",
		IFNUll(
		(CASE
		a.type
		WHEN 1 THEN
		a.confirmAmount
		WHEN 2 THEN
		a.confirmAmount
		WHEN 3 THEN
		- a.confirmAmount ELSE 0
		END),0)
		AS "amountReceived",
		a.companyId AS "companyId",
		j.`name` AS "companyName",
		l.`no` AS "craftNo",
		g.`no` AS "contractNo",
		g.customerPo AS "customerPo",
		l.customerModel AS "customerModel",
		b.quantity AS "quantity",
		m.price AS "price",
		k.name AS "customer.name"
		FROM
		sl_single_receivable_detail a
		LEFT JOIN sl_goods_check sgc ON sgc.recordId = a.goodsCheckId
		LEFT JOIN sl_delivery_detail b ON b.recordId = sgc.sourceId AND sgc.inOutFlag = 1
		LEFT JOIN st_product_store c ON c.recordId = sgc.sourceId AND sgc.inOutFlag = 0
		LEFT JOIN sl_contract_detail d ON d.recordId = IFNULL( b.contractDetailId, c.contractDetailId )
		LEFT JOIN icloud_group_center e ON e.recordId = d.groupCenterId
		LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL( e.contactDeailId, d.groupCenterId )
		LEFT JOIN sl_contract g ON g.recordId = f.contractId
		LEFT JOIN icloud_group_org_relation h ON h.deptId = g.deptId AND h.activeFlag = 1
		LEFT JOIN icloud_group_org i ON i.recordId = h.groupOrgId
		LEFT JOIN md_company j ON j.recordId = a.companyId

		LEFT JOIN md_customer k ON k.recordId = g.customerId
		LEFT JOIN sl_contract_craft l ON l.recordId = f.craftId
		LEFT JOIN sl_price m ON m.recordId =  f.priceId
		WHERE
		a.activeFlag = 1
		<if test="erpCompanyId != null and erpCompanyId != '' and (selectStatus == 2 or selectStatus == 3)">
			AND a.companyId = #{erpCompanyId}
		</if>
		AND a.type IN ( 1, 2, 3 )
		AND d.recordId = f.recordId
		AND sgc.period >= DATE_FORMAT(#{sentTimeStartQr}, '%Y%m' )
		AND sgc.period <![CDATA[<=]]> DATE_FORMAT(#{sentTimeEndQr}, '%Y%m' )
		<if test="groupDepartId != null and groupDepartId != '' and selectStatus == 3">
			AND CASE #{groupDepartId} WHEN -1 THEN h.groupOrgId IS NULL ELSE h.groupOrgId = #{groupDepartId} END
		</if>
	</select>

	<select id="approveAccountsReceivable" resultType="AccountsReceivable">
		SELECT
		aaa.recordId,
		aaa.companyId AS "company.recordId",
		aaa.customrId AS "customer.recordId",
		aaa.customerName AS "customer.shortName",
		aaa.period AS "period",
		aaa.reconcilingAmount AS "reconcilingAmount",
		aaa.estimateCost AS "estimateCost",
		ROUND(aaa.reconcilingAmount * IFNULL(aaa.salePrecent, 0) / 100, 2) AS "saleFee",
		ROUND(aaa.reconcilingAmount * 8 / 1000, 2) AS "tubePinFee",
		ROUND(aaa.reconcilingAmount * 15 / 1000, 2) AS "manageFee",
		aaa.sentGoodsValue AS "sentGoodsValue",
		aaa.returnGoodsValue AS "returnGoodsValue",
		aaa.adjustValue AS "adjustValue",
		aaa.exchangeRate AS "exchangeRate",
		ROUND(aaa.sentGoodsValue * aaa.exchangeRate) AS "precentAmount",
		ROUND(
					IFNULL(aaa.reconcilingAmount, 0) * aaa.exchangeRate - aaa.estimateCost - ROUND(IFNULL(aaa.reconcilingAmount, 0) * 15 / 1000, 2) - ROUND(IFNULL(aaa.reconcilingAmount, 0) * 8 / 1000, 2) - ROUND(IFNULL(aaa.reconcilingAmount, 0) * IFNULL(aaa.salePrecent, 0) / 100, 2),
					2) AS "profitLossAmount"
		FROM
		(
		SELECT
		a.recordId,
		a.companyId,
		b.recordId AS "customrId",
		b.shortName AS "customerName",
		a.period AS "period",
		IFNULL(a.sentGoodsValue, 0) - IFNULL(a.returnGoodsValue, 0) + IFNULL(a.adjustValue, 0) AS "reconcilingAmount",
		b.salePrecent,
		e.estimateCost,
		IFNULL(a.sentGoodsValue, 0) AS "sentGoodsValue",
		IFNULL(a.returnGoodsValue, 0) AS "returnGoodsValue",
		IFNULL(a.adjustValue, 0) AS "adjustValue",
		IFNULL(e.exchangeRate,1) AS "exchangeRate"
		FROM
		sl_accounts_receivable a
		LEFT JOIN md_customer b ON b.recordId = a.customerId
		LEFT JOIN (
		SELECT
		ROUND(
		SUM(
		(
		CASE

		WHEN aa.inOutFlag IS NULL
		OR aa.inOutFlag = 0 THEN
		IF
		(aa.estimateCost > 0,- 1 * aa.estimateCost, aa.estimateCost) ELSE aa.estimateCost
		END) * IFNULL(aa.exchangeRate, 1)),
		2) AS "estimateCost",
		aa.customerId,
		aa.period,
		aa.exchangeRate
		FROM
		(
		SELECT
		IF
		(
		(
		SELECT
		COUNT(aa.recordId)
		FROM
		sl_delivery_detail aa
		WHERE
		aa.contractDetailId = b.contractDetailId
		AND aa.activeFlag = 1
		AND aa.`status` = 500518
		AND aa.recordId <![CDATA[<]]> b.recordId) = 0,
		IF
		(
		a.inOutFlag = 1,
		IFNULL(IFNULL(pp.mouldFee, a.mouldFee), 0) + IFNULL(IFNULL(pp.testShelfFee, a.testShelfFee), 0) + IFNULL(IFNULL(pp.engineeringFee, a.engineeringFee), 0) + IFNULL(IFNULL(pp.othersFee, a.othersFee), 0) + IFNULL(IFNULL(pp.filmFee, a.filmFee), 0),
		0) + ROUND(
		IFNULL(IFNULL(pp.price, i.jxPrice) * a.quantity, 0) *
		IF
		(i.recordId IS NOT NULL, 1, (j.quantity / IFNULL(e.quantity, h.quantity))),
		2),
		ROUND(
		IFNULL(IFNULL(pp.price, i.jxPrice) * a.quantity, 0) *
		IF
		(i.recordId IS NOT NULL, 1, (j.quantity / IFNULL(e.quantity, h.quantity))),
		2)) + ROUND(
		IF
		(a.inOutFlag = 0,- IFNULL(a.originalAmount, a.amount) + a.price * a.quantity, 0),
		2) AS "estimateCost",
		a.inOutFlag AS "inOutFlag",
		l.exchangeRate AS "exchangeRate",
		a.customerId,
		a.period
		FROM
		sl_goods_check a
		LEFT JOIN sl_delivery_detail b ON b.recordId = a.sourceId
		AND a.inOutFlag = 1
		LEFT JOIN st_reject_application c ON c.recordId = b.complaintId
		LEFT JOIN sl_outbound d ON d.recordId = b.terminalDetailId
		LEFT JOIN sl_contract_detail e ON e.recordId = b.contractDetailId
		LEFT JOIN st_product_store f ON f.recordId = a.sourceId
		AND a.inOutFlag = 0
		AND f.complaintId
		IS NOT NULL LEFT JOIN st_reject_application g ON g.recordId = f.complaintId
		LEFT JOIN sl_contract_detail h ON h.recordId = f.contractDetailId
		LEFT JOIN icloud_group_center i ON i.recordId = IFNULL(e.groupCenterId, h.groupCenterId)
		LEFT JOIN pu_prdorder_detail j ON j.contractDetailId = IFNULL(e.recordId, h.recordId)
		AND j.companyId = a.companyId
		AND j.activeFlag = 1
		LEFT JOIN pu_material_check k ON k.prdorderDetailId = j.recordId
		AND k.activeFlag = 1
		AND k.companyId = a.companyId
		AND
		CASE

		WHEN a.inOutFlag = 1 THEN
		k.oldOutBoundId = d.oldOutBoundId ELSE 1 = 1
		END
		AND
		CASE

		WHEN IFNULL(c.recordId, g.recordId) IS NOT NULL THEN
		k.complaintId = IFNULL(c.recordId, g.recordId) ELSE 1 = 1
		END
		AND k.period = a.period
		AND k.quantity = a.quantity
		AND k.inOutFlag = a.inOutFlag
		LEFT JOIN sl_contract l ON l.recordId = IFNULL(e.contractId, h.contractId)
		LEFT JOIN pu_price pp ON pp.recordId = j.priceId
		LEFT JOIN sl_accounts_receivable m ON m.customerId = a.customerId
		AND m.period = a.period
		WHERE
		a.activeFlag = 1
		AND m.recordId = #{recordId}
		GROUP BY
		a.recordId) aa
		GROUP BY
		aa.customerId,
		aa.period) e ON e.customerId = a.customerId
		AND e.period = a.period) aaa
		WHERE
		aaa.recordId = #{recordId}
		ORDER BY
		aaa.period
	</select>

</mapper>