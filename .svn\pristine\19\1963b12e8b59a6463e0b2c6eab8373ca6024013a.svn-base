<%@ page contentType="text/html;charset=UTF-8"%>
<div ng-intro-options="help1Options" ng-intro-method="help1" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="help2Options" ng-intro-method="help2" ng-intro-autostart="shouldAutoStart"></div>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">库存管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="stock.rawmaterialstock">原料库存</a>
        </li>
    </ul>
    <div class="page-toolbar">
    	<button class="btn default btn-fit-height pull-right" ng-click="help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>
</div>
<!-- END 导航-->

<tabset class="tabset-margin-top">
	<!-- BEGIN 物料台账 -->
    <tab heading="物料台账" active="stockCtrl.tabs.placeInfo.active" ng-click="stockCtrl.getPlaceDataList()">
        <div class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                       <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">出入库时间：</label>
                                <div class="col-sm-7 col-md-8">
                                    <div class="input-prepend input-group">
										<span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
										<input type="text" class="form-control" disable-auto-validate="true"
											ng-blur="stockCtrl.initDate(stockCtrl.time)"
											kyb-daterange 
											kyb-daterange-options="stockCtrl.rangeOptions"
											ng-model="stockCtrl.time" 
											placeholder="请选择时间段">
									</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select  ng-model="stockCtrl.inoutPlaceQuery.material" theme="bootstrap" disable-auto-validate="true">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
                                        <ui-select-choices refresh="stockCtrl.addMaterialItems($select.search)" refresh-delay="500" repeat="item in stockCtrl.materialQueryList | filter: $select.search">
                                                <small>
                                               		<span><span style="color: blue;">编号:</span>{{item.no}}<br></span>
                                               		<span><span style="color: blue;">名称:</span>{{item.name}}<br></span>
                                               		<span><span style="color: blue;">规格:</span>{{item.specification}}<br></span>
                                               </small>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">所属仓库：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select  ng-model="stockCtrl.inoutPlaceQuery.storehouse.value" theme="bootstrap" disable-auto-validate="true" on-select="stockCtrl.fiterMaterialPlace()">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                        <ui-select-choices repeat="item in stockCtrl.storehouseList | filter: $select.search">
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" >
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">库位：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select  ng-model="stockCtrl.inoutPlaceQuery.materialPlace" theme="bootstrap" disable-auto-validate="true">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                        <ui-select-choices repeat="item in stockCtrl.placeList | filter: $select.search">
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">结余状态：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="stockCtrl.haveStockFlag" class="form-control"
                                            disable-auto-validate="true">
                                        <option value="1">有结余</option>
                                        <option value="2">所有</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">供应商编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.inoutPlaceQuery.supplierNo.value"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">冲红状态：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="stockCtrl.inoutPlaceQuery.status.value" class="form-control"
                                            disable-auto-validate="true">
                                        <option value="">所有</option>
                                        <option value="99999901">未冲红</option>
                                        <option value="99999903">已冲红</option>
                                        <option value="99999902">冲红单</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">出入库类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="stockCtrl.inoutPlaceQuery.inoutType.value" disable-auto-validate="true">
                                        <option value="">所有</option>
                                        <option value="1">采购入库</option>
                                        <option value="2">原料入库</option>
                                        <option value="3">投料出库</option>
                                        <option value="5">补料出库</option>
                                        <option value="4">退货出库</option>
                                        <option value="6">领料出库</option>
                                        <option value="7">补货入库</option>
                                        <option value="8">送货出库</option>
                                        <option value="9">调拨入库</option>
                                        <option value="10">调拨出库</option>
                                        <option value="20">客诉退货入库</option>
                                       <!--  <option value="21">盘点调整</option> -->
                                        <option value="29">盘盈入库</option>
                                        <option value="30">盘亏出库</option>

                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">供应商名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.inoutPlaceQuery.supplierName.value"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">生产编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.inoutPlaceQuery.craftNo.value"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
			<!-- TJ 2017-08-29 合同编号 -->
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">合同编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.contractNo"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>

                       <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                           <div class="form-group">
                               <label class="col-sm-3 col-md-4 control-label">供应商合同编号：</label>
                               <div class="col-sm-7 col-md-8">
                                   <input type="text" class="form-control"
                                          ng-model="stockCtrl.supplierContractNo"
                                          disable-auto-validate="true"/>
                               </div>
                           </div>
                       </div>
					   <div class="col-md-6 col-lg-4" ng-if="stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.materialNo" disable-auto-validate="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.materialName" disable-auto-validate="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="stockCtrl.materialTypeId"
                                            ng-options="a.recordId as a.value for a in stockCtrl.materialTypeList" class="form-control"
                                            disable-auto-validate="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">规格型号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.specification" disable-auto-validate="true" ng-blur="stockCtrl.getPlaceDataList()" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">所属仓库：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select multiple theme="bootstrap" ng-model="stockCtrl.storeHouseIdList" on-select="stockCtrl.selectStoreHouseId()" on-remove="stockCtrl.removeStoreHouseId($item)" disable-auto-validate="true" append-to-body = "true">
                                        <ui-select-match placeholder="请选择...">{{$item.name}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in stockCtrl.storehouseList | filter: $select.search">
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">公司：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select theme="bootstrap" ng-model="stockCtrl.branchId" disable-auto-validate="true">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.shortName}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in stockCtrl.branchList | filter: $select.search">
                                            <div ng-bind-html="item.shortName | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="stockCtrl.getPlaceDataList()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">台账列表
	                <span ng-if="stockCtrl.page.infoPlace.data.list && stockCtrl.page.infoPlace.data.list.length > 0 && !stockCtrl.materialPlaceFlag">
	                	（期初数量：{{stockCtrl.page.infoPlace.data.list[0].currStocks}}&emsp;本期入库总数：{{stockCtrl.page.infoPlace.data.list[0].inStocks}}
	                	&emsp;本期出库总数：{{stockCtrl.page.infoPlace.data.list[0].outStocks}}&emsp;
	                	当前结余：{{stockCtrl.page.infoPlace.data.list[0].allStocks}}&emsp;
	                	期初单价{{stockCtrl.page.infoPlace.data.list[0].startPrice}}&emsp;移动加权平均价{{stockCtrl.page.infoPlace.data.list[0].currPrice}}
	                	&emsp;最新采购单价{{stockCtrl.page.infoPlace.data.list[0].newPurchPrice}}
                        &emsp;<span ng-if="stockCtrl.page.infoPlace.data.list[0].updatePriceDateStr">更新时间{{stockCtrl.page.infoPlace.data.list[0].updatePriceDateStr}}</span>
                        ）
	                </span>
                    <span ng-if="stockCtrl.materialPlaceFlag">
                        （<span class="text-warning">汇总最新(不按时间查询)</span>总数量：{{stockCtrl.fomatFloat(stockCtrl.totalInfo.quantity)}}&emsp;总金额：{{stockCtrl.fomatFloat(stockCtrl.totalInfo.amount)}}）
	                </span>
                </div>
                <div class="actions">
                    <div class="collapse navbar-collapse navbar-ex1-collapse">
						<div class="portlet-input input-inline input-small">
							<button type="button" class="btn green btn-default-width" ng-click="stockCtrl.transferLibraryOpen()">
								<i class="fa fa-plus"></i> 移库
							</button>
						</div>
                    	<div class="portlet-input input-inline input-small">
	                        <form action="a/stock/rawmaterial/exportInoutPlace" method="POST" enctype="multipart/form-data" target="hidden_frame1">
	                            <input type="text" ng-show="false" name="material.recordId" value="{{stockCtrl.inoutPlaceQuery.material.recordId}}"/>
	                            <input type="text" ng-show="false" name="stockPlaceId" value="{{stockCtrl.inoutPlaceQuery.materialPlace.recordId}}"/>
	                            <input type="text" ng-show="false" name="inoutType" value="{{stockCtrl.inoutPlaceQuery.inoutType.value}}"/>
                                <input type="text" ng-show="false" name="supplier.shortName" value="{{stockCtrl.inoutPlaceQuery.supplierName.value}}"/>
                                <input type="text" ng-show="false" name="supplier.no" value="{{stockCtrl.inoutPlaceQuery.supplierNo.value}}"/>
                                <input type="text" ng-show="false" name="status" value="{{stockCtrl.inoutPlaceQuery.status.value}}"/>
                                <input type="text" ng-show="false" name="storehouse.recordId" value="{{stockCtrl.inoutPlaceQuery.storehouse.value.recordId}}"/>
                                <input type="text" ng-show="false" name="inoutTimeStartQr" value="{{stockCtrl.inoutPlaceQuery.inoutTimeStartQr.value}}"/>
                            	<input type="text" ng-show="false" name="inoutTimeEndQr" value="{{stockCtrl.inoutPlaceQuery.inoutTimeEndQr.value}}"/>
                            	<input type="text" ng-show="false" name="craftNo" value="{{stockCtrl.inoutPlaceQuery.craftNo.value}}"/>
                            	<input type="text" ng-show="false" name="contractNo" value="{{stockCtrl.contractNo}}"/>
                            	<input type="text" ng-show="false" name="remark" value="{{stockCtrl.remark}}"/>
                            	
                            	<input type="text" ng-show="false" name="materialNo" value="{{stockCtrl.materialNo}}"/>
                            	<input type="text" ng-show="false" name="materialName" value="{{stockCtrl.materialName}}"/>
                            	<input type="text" ng-show="false" name="materialTypeId" value="{{stockCtrl.materialTypeId}}"/>
                            	<input type="text" ng-show="false" name="specification" value="{{stockCtrl.specification}}"/>
                            	<input type="text" ng-show="false" name="storeHouseId" value="{{stockCtrl.storeHouseId}}"/>
                            	<input type="text" ng-show="false" name="branchId" value="{{stockCtrl.branchId}}"/>
                            	<input type="text" ng-show="false"  name="queryAll" value="{{stockCtrl.queryAll}}"/>
	                            <div>
	                                <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出出入库</button>
	                                <iframe name="hidden_frame1" id="hidden_frame1" style="display:none"></iframe>
	                            </div>
	                        </form>
	                    </div>
                    </div>
                </div>
            </div>

            <div class="portlet-body">
                <div id="twoStep1" class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
                        <tr class="heading" ng-if="!stockCtrl.materialPlaceFlag">
                            <th>物料编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>物料名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>合同编号</th>
                            <th>生产编号</th>
                            <th>出入库类型</th>
                           <!--  <th>出入库数量&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th> -->
                            <th>入库数量&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>出库数量&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>结余</th>
                            <th>余料</th>
                            <th>出入库时间&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>采购编号</th>
                            <th>供应商编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>供应商名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>单价&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>赠品数量</th>
                            <th>金额</th>
                            <%--<th>加权平均&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>--%>
                            <th>仓库&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>库位</th>
                            <th>销售公司</th>
                            <th>原因</th>
                            <th>状态&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>处理人</th>
                        </tr>
                        <tr class="heading" ng-if="stockCtrl.materialPlaceFlag">
                            <th>物料编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>物料名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>规格型号</th>
                            <th>实际板厚</th>
                            
                            <th>期初</th>
                            <th>累积入库</th>
                            <th>累积出库</th>
                            <th>结余</th>

                            <th>加权平均单价</th>
                            <th>期初单价</th>
                            <th>最新采购价</th>
                            <th>最新时间</th>
                            <th>税率</th>
                            <th>加权平均含税金额</th>
                            <th>不含税金额</th>
                            <%--<th>金额(先进先出含税)</th>
                            <th>金额(先进先出不含税)</th>--%>
                            <th>物料类型</th>
                            <th>单位</th>
                            <th>仓库</th>

                            <th ng-repeat="rowTwo in stockCtrl.page.infoPlace.companyNameListTwo">
                                在库-{{rowTwo.companyName}}
                            </th>
                            <th ng-repeat="row in stockCtrl.page.infoPlace.companyNameList">
                                在途-{{row.companyName}}
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in stockCtrl.page.infoPlace.data.list" ng-if="row.inoutType != 11 && row.inoutType != 12 && row.inoutType != 13 && !stockCtrl.materialPlaceFlag">
                            <td><a ng-bind="row.material.no"></a></td>
                            <td ng-bind="row.material.name"></td>
                            <td ng-if="row.inoutType!=1" ng-bind="row.contractNo"></td>
                            <td ng-if="row.inoutType==1" ng-bind="row.supplierContractNo"></td>
                            <td ng-if="row.inoutType == 11 || row.inoutType == 12"></td>
                            <td ng-bind="row.craftNo" ng-if="row.inoutType == 3"></td>
                            <td ng-bind="row.returnsNo" ng-if = "row.inoutType == 5"></td>
                            <td ng-if = "row.inoutType != 5 && row.inoutType != 3"></td>
                            <td ng-bind="row.inoutType | inoutTypeFilter" ng-if="!(row.inoutType == 11 || row.inoutType == 12 || !row.recordId||row.inoutType == 2||row.inoutType == 6)"></td>
                            <td ng-if="row.inoutType == 11 || row.inoutType == 12|| row.inoutType == 2|| row.inoutType == 6">
                            	<span ng-if="row.inoutType == 2&&!(row.remark&&row.remark.indexOf('盘盈')!=-1)">原料入库</span>
                            	<span ng-if="row.inoutType == 6&&!(row.remark&&row.remark.indexOf('盘亏')!=-1)">原材料出</span>
                            	<span ng-if="row.inoutType == 2&&row.remark&&row.remark.indexOf('盘盈')!=-1">盘盈入库</span>
                            	<span ng-if="row.inoutType == 6&&row.remark&&row.remark.indexOf('盘亏')!=-1">盘亏出库</span>
                            	<span ng-if="row.inoutType == 11">期初</span>
                            	<span ng-if="row.inoutType == 12">结余</span>
                            </td>
                            <td ng-if="!row.recordId && row.currStocks">
                            	期初
                            </td>
                            <td ng-if="!row.recordId && !row.currStocks">
                            	汇总
                            </td>
                            <td class="text-right"><span ng-if="row.inStocks && row.recordId">{{stockCtrl.fomatFloat(row.inStocks)}}</span>
                            <span ng-if="!row.inStocks">-</span></td>
                            <td class="text-right"><span ng-if="row.outStocks && row.recordId">{{stockCtrl.fomatFloat(row.outStocks)}}</span>
                            <span ng-if="!row.outStocks">-</span></td>
                            <td class="text-right"><span ng-if="row.currStocks">{{stockCtrl.fomatFloat(row.currStocks)}}</span>
                            <span ng-if="!row.currStocks">-</span></td>
                            
                            <td>{{stockCtrl.fomatFloat(row.remainQty)}}</td>
                            <td>{{row.operateDate.split(" ")[0]}}</td>
                             <td ng-bind="row.purchasingNo" ng-if="!(row.inoutType == 11 || row.inoutType == 12)"></td>
                            <td ng-bind="row.supplier.no"></td>
                            <td ng-bind="row.supplier.shortName"></td>
                            <td>
                            	<span ng-if="row.status === 99999901">{{stockCtrl.fomatFloat(row.price)}}</span>
                            	<span ng-if="row.status != 99999901&&!row.recordId && row.currStocks">{{stockCtrl.fomatFloat(row.startPrice)}}</span>
                            </td>
                            <td>{{stockCtrl.fomatFloat(row.giveNum)}}</td>
                            <%--<td>{{row.firstInFirstAmount ? row.firstInFirstAmount : 0}}</td>--%>
                            <td>
                            	<span ng-if="row.status === 99999901">{{stockCtrl.fomatFloat(row.cost)}}</span>
                            	<span ng-if="row.status != 99999901&&!row.recordId && row.currStocks">{{stockCtrl.fomatFloat(row.currStocks*row.startPrice)}}</span>
                            </td>
                            <td ng-bind="row.storehouse.name"></td>
                            <td ng-bind="row.placeName"></td>
                            <td ng-bind="row.stockPlaceComName"></td>
                            <td ng-bind="row.writeOffCause"></td>
                            <td ng-if="row.status">
                                <span class="label label-sm label-default" ng-if="row.status === 99999901">未冲红</span>
                                <span class="label label-sm label-success" ng-if="row.status === 99999902">冲红单 </span>
                                <span class="label label-sm label-danger" ng-if="row.status === 99999903">已冲红</span>
                            </td>
                            <td ng-if="!row.status"></td>
                            <td ng-bind="row.createdBy.userName"></td>
                        </tr>
                        <tr ng-repeat="row in stockCtrl.page.infoPlace.data.list" ng-if="stockCtrl.materialPlaceFlag">
                            <td>
                            	<a ng-click="stockCtrl.loadMaterialById(row)">{{row.materialNo}}</a>
                            </td>
                            <td>{{row.materialName}}</td>
                            <td>{{row.specification}}</td>
                            <td>{{row.actualThickness}}</td>
                            
                            <td>{{row.startStocks}}</td>
                            <td>{{row.inStocks}}</td>
                            <td>{{row.outStocks}}</td>
                            <td>{{row.quantity}}</td>
                            
                            <td>{{row.price}}</td>
                            <td>{{row.startPrice}}</td>
                            <td>{{row.newPurchPrice}}</td>
                            <td>{{row.updatePriceDateStr}}</td>
                            
                            <td>{{row.taxDescript}}</td>
                            <td>{{row.cost}}</td>
                            <td>{{row.unTaxDescriptCost}}</td>
                            <%--<td>{{row.firstInFirstAmountTotalTax ? row.firstInFirstAmountTotalTax : 0}}</td>
                            <td>{{row.firstInFirstAmountTotal ? row.firstInFirstAmountTotal : 0}}</td>--%>

                            <td>{{row.materialTypeVal}}</td>
                            <td>{{row.unitValue}}</td>
                            <td>{{row.storeName}}</td>

                            <td ng-repeat="itemTwo in stockCtrl.page.infoPlace.companyNameListTwo">
                                <div ng-repeat="itemsTwo in row.purchSpecListTwo" ng-if="itemsTwo.companyName === itemTwo.companyName">
                                    {{itemsTwo.val}}
                                </div>
                            </td>
                            <td ng-repeat="item in stockCtrl.page.infoPlace.companyNameList">
                                <div ng-repeat="items in row.purchSpecList" ng-if="items.companyName === item.companyName">
                                    {{items.val}}
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row page-margin-top">
                    <div id="twoStep5" class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select class="form-control inline table-pageSize-width"
                                ng-model="stockCtrl.page.infoPlace.pageSize"
                                ng-change="stockCtrl.pageSizeChangePlace()"
                                disable-auto-validate="true"
                                ng-options="pageSizeOption for pageSizeOption in stockCtrl.page.pageSizeOptions">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示{{stockCtrl.page.infoPlace.data.startCount}} / {{stockCtrl.page.infoPlace.data.endCount}}条，共 {{stockCtrl.page.infoPlace.data.count}} 条</span>
                    </div>
                    <div id="twoStep4" class="col-md-12 col-lg-6">
                        <paging class="pull-right" page="stockCtrl.page.infoPlace.data.pageNo"
                                page-size="stockCtrl.page.infoPlace.data.pageSize"
                                total="stockCtrl.page.infoPlace.data.count" adjacent="1" dots="..."
                                scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                active-class="active" disabled-class="disabled" show-prev-next="true"
                                paging-action="stockCtrl.doPagePlace( page, pageSize, total)"> </paging>
                    </div>
                </div>
            </div>
        </div>

    </tab>
    <!-- END 物料台账 -->
    <!-- BEGIN 原料信息 -->
    <tab heading="原料信息" active="stockCtrl.tabs.info.active" ng-click="stockCtrl.getInfoDataList()">
        <div id="oneStep5" class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.query.no.value" disable-auto-validate="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.query.name.value" disable-auto-validate="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="stockCtrl.query.materialType.value"
                                            ng-options="a.recordId as a.value for a in stockCtrl.materialTypeList" class="form-control"
                                            disable-auto-validate="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">规格型号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.query.specification.value" disable-auto-validate="true"
                                    ng-blur="stockCtrl.doQuery()"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">所属仓库：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select multiple theme="bootstrap" ng-model="stockCtrl.storeHouseIdList" on-select="stockCtrl.selectStoreHouseId()" on-remove="stockCtrl.removeStoreHouseId($item)" disable-auto-validate="true" append-to-body = "true">
                                        <ui-select-match placeholder="请选择...">{{$item.name}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in stockCtrl.storehouseList | filter: $select.search">
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料状态：</label>
                                <div class="col-sm-7 col-md-8">
                                   	<select class="form-control" ng-model="stockCtrl.query.ststus.value"  disable-auto-validate="true">
                                    	<option value="">所有</option>
                                    	<option value="1">正常</option>
                                    	<option value="0">作废</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" >
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">仅包含有库存：</label>
								<div class="col-sm-7 col-md-7">
									<div class="task-checkbox">
										<checkbox  ng-model="stockCtrl.query.isIncludeStock.value" ></checkbox>
									</div>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4" >
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">工艺不完善：</label>
								<div class="col-sm-7 col-md-7">
									<div class="task-checkbox">
										<checkbox  ng-model="stockCtrl.query.craftNoStatus"></checkbox>
									</div>
								</div>
							</div>
						</div>
                        <div class="col-md-6 col-lg-4" >
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">待提交：</label>
                                <div class="col-sm-7 col-md-7">
                                    <div class="task-checkbox">
                                        <checkbox  ng-model="stockCtrl.query.submittedStatus" ng-change="stockCtrl.doQuery()"></checkbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="stockCtrl.doQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">原料列表</div>
                <div id="oneStep6" class="actions">
                    <div class="portlet-input input-inline input-small">
                        <button type="button" class="btn green btn-default-width" ng-if="stockCtrl.query.submittedStatus" ng-click="stockCtrl.approval()">
                            <i class="fa fa-plus"></i> 提交审批
                        </button>
                    </div>
                    <div class="portlet-input input-inline input-small">
                        <form action="a/stock/rawmaterial/export" method="POST" enctype="multipart/form-data" target="hidden_frame">
                            <input type="text" ng-show="false"  name="no" value="{{stockCtrl.query.no.value}}"/>
                            <input type="text" ng-show="false"  name="storehouse.recordId" value="{{stockCtrl.query.storehouse.value}}"/>
                            <input type="text" ng-show="false"  name="name" value="{{stockCtrl.query.name.value}}"/>
                            <input type="text" ng-show="false"  name="craftNoStatus" value="{{stockCtrl.query.craftNoStatus}}"/>
                            <input type="text" ng-show="false"  name="specification" value="{{stockCtrl.query.specification.value}}"/>
                            <input type="text" ng-show="false"  name="status" value="{{stockCtrl.query.ststus.value}}"/>
                            <input type="text" ng-show="false"  name="materialType" value="{{stockCtrl.query.materialType.value}}"/>
                            <input type="text" ng-show="false"  name="includeStock" value="{{stockCtrl.query.isIncludeStock.value}}"/>
                            <input type="text" ng-show="false"  name="orderBy" value="{{stockCtrl.query.sort.value}}"/>
                            <input type="text" ng-show="false"  name="queryAll" value="{{stockCtrl.queryAll}}"/>
                            <div>
                                <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出原料</button>
                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="portlet-body">
                <div id="oneStep1" class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" data-filter-control="true">
                        <thead>
                        <tr class="heading">
                            <th ng-if="stockCtrl.query.submittedStatus">
                                <checkbox ng-model="stockCtrl.allPageChecked" value="1" ng-click="stockCtrl.selectAllPageTwo()"></checkbox>
                            </th>
                        	<!-- <th ng-if="(stockCtrl.purchase.start==1 && stockCtrl.right.edit) || stockCtrl.right.manage">操作</th> -->
                            <th id="oneStep2" ng-class="{'sorting': stockCtrl.sort.no1.both, 'sorting_desc': stockCtrl.sort.no1.desc, 'sorting_asc': stockCtrl.sort.no1.asc}" ng-click="stockCtrl.sortClick('no1')">物料编号</th>
                            <th ng-class="{'sorting': stockCtrl.sort.name1.both, 'sorting_desc': stockCtrl.sort.name1.desc, 'sorting_asc': stockCtrl.sort.name1.asc}" ng-click="stockCtrl.sortClick('name1')">物料名称</th>
                            <th ng-class="{'sorting': stockCtrl.sort.specification1.both, 'sorting_desc': stockCtrl.sort.specification1.desc, 'sorting_asc': stockCtrl.sort.specification1.asc}" ng-click="stockCtrl.sortClick('specification1')">规格型号</th>
                           	<!-- <th>单价</th>
                            <th>数量</th> -->
                            
                            <th>制造商&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>审批状态</th>
                            <th ng-class="{'sorting': stockCtrl.sort.leadTime1.both, 'sorting_desc': stockCtrl.sort.leadTime1.desc, 'sorting_asc': stockCtrl.sort.leadTime1.asc}" ng-click="stockCtrl.sortClick('leadTime1')">采购提前期(当前)&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th ng-click="stockCtrl.sortClick('leadTime1')">采购提前期(修改)&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th >价格(当前)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th >价格(修改)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th ng-repeat="row in stockCtrl.materialSpecList" ng-if="row.visible == 1">{{row.name}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <!-- <th>工艺数据</th> -->
                            
                            <th>物料类型</th>
                            <th>单位</th>
                            
                            <th>仓库</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in stockCtrl.page.info.data.list">
                            <td ng-if="stockCtrl.query.submittedStatus">
                                <checkbox ng-model="row.checked" value="1" ng-change="stockCtrl.getRecordList()"></checkbox>
                            </td>
                        	<!-- <td ng-if="(stockCtrl.purchase.start==1 && stockCtrl.right.edit) || stockCtrl.right.manage">
                            	<a ng-if="stockCtrl.right.manage" href="javascript:void(0);"  
                            	class="btn btn-xs btn-danger" ng-click="stockCtrl.adjustPrice(row)">
                            	<i class="fa  fa-stumbleupon"></i>单价</a>
                            	
                            	<a ng-if="stockCtrl.showRawmaterial && !row.remain && row.lowerLimit > row.stocks && stockCtrl.purchase.start==1"
                            		href="javascript:void(0);" class="btn btn-xs btn-default" 
                            		ui-sref="purch.rawmaterial({contractId: row.recordId})">
                            	<i class="fa fa-fighter-jet"></i>自动生成采购订单</a>
                            	
                            	<a ng-if="stockCtrl.right.manage" href="javascript:void(0);"  
                            		class="btn btn-xs btn-warning" ng-click="stockCtrl.adjustNum(row)">
                            	<i class="fa  fa-stumbleupon"></i>数量调整</a>
                            </td> -->
                            <td><a ng-bind="row.no" ng-click="stockCtrl.infoClick($index)"></a></td>
                            <td ng-bind="row.name"></td>
                            <td ng-bind="row.specification"></td>
                            
                           <!--  <td ng-bind="row.price"></td>
                            <td class="text-right" ng-bind="row.stocks | number:4"></td> -->
                            <td ng-if="row.manufacturerId">
                            	<ui-select ng-model="row.manufacturerId" theme="bootstrap" ng-change="stockCtrl.editSupplier(row)">
									<ui-select-match placeholder="请选择...">{{$select.selected.itemName}}</ui-select-match>
                                       	<ui-select-choices repeat="item.recordId as item in stockCtrl.supplierList | filter: $select.search">
										<div ng-bind-html="item.itemName | highlight: $select.search"></div>
									</ui-select-choices> 
								</ui-select>
                            </td>
                            <td ng-if="!row.manufacturerId"></td>
                            <%--<td ng-if="stockCtrl.materialDeliveryTimeSwitchValue == 2"><input type="text" class="form-control" ng-model="row.leadTime"  readonly="readonly"/></td>
                            <td ng-if="stockCtrl.materialDeliveryTimeSwitchValue != 2"><input type="text" class="form-control" ng-model="row.leadTime" ng-Onlynumber ng-blur="stockCtrl.editLeadTime(row)" /></td>--%>
                            <td>
                                <span ng-if="!row.interProductStockRecord || !row.interProductStockRecord.status">无</span>
                                <span ng-if="row.interProductStockRecord && row.interProductStockRecord.status == '1001'" class="badge badge-warning">待提交</span>
                                <span ng-if="row.interProductStockRecord && row.interProductStockRecord.status == '60001'" class="badge badge-danger">审批中</span>
                            </td>
                            <td ng-if="stockCtrl.materialPriceSwitchValue == 2">
                                <span ng-style="{'color': row.LeadTimeRed == 1 ? 'red' : 'black'}" ng-bind="row.leadTime"></span>
                            </td>
                            <td ng-if="stockCtrl.materialPriceSwitchValue == 2" ng-bind="row.erpRawmaterialLeadTime"></td>
                            <td ng-if="stockCtrl.materialPriceSwitchValue != 2">
                                <span ng-style="{'color': row.LeadTimeRed == 1 ? 'red' : 'black'}" ng-bind="row.leadTime"></span>
                            </td>
                            <td ng-if="stockCtrl.materialPriceSwitchValue != 2">
                                <input type="text" class="form-control" ng-model="row.erpRawmaterialLeadTime"
                                       ng-disabled="row.interProductStockRecord && row.interProductStockRecord.status == '60001'"
                                       ng-blur="stockCtrl.editLeadTime(row)" style="display: inline-block; width: 50%;"/>
                            </td>
                            <td ng-if="stockCtrl.materialPriceSwitchValue == 2">
                                <span ng-style="{'color': row.markedRed == 1 ? 'red' : 'black'}" ng-bind="row.rawmaterialPrice"></span>
                            </td>
                            <td ng-if="stockCtrl.materialPriceSwitchValue == 2" ng-bind="row.erpRawmaterialPrice"></td>
                            <td ng-if="stockCtrl.materialPriceSwitchValue != 2">
                                <span ng-style="{'color': row.LeadTimeRed == 1 ? 'red' : 'black'}" ng-bind="row.rawmaterialPrice"></span>
                            </td>
                            <td ng-if="stockCtrl.materialPriceSwitchValue != 2">
                                <input type="text" class="form-control" ng-model="row.erpRawmaterialPrice"
                                       ng-disabled="row.interProductStockRecord && row.interProductStockRecord.status === '60001'"
                                       ng-blur="stockCtrl.editPrice(row)" style="display: inline-block; width: 50%;"/>
                            </td>
                            <td ng-repeat="fujian in row.materialSpecList" ng-if="fujian.visible == 1">
                            	<div ng-if="fujian.dictItem == null || fujian.dictItem.recordId == null">
                           			<input type="text" class="form-control" ng-model="fujian.value" ng-if="fujian.enName != 'Length' && fujian.enName != 'width'"  ng-blur="stockCtrl.editCraft(fujian)"/>
		                            <input type="text" class="form-control" ng-model="fujian.value" ng-if="fujian.enName == 'Length' || fujian.enName == 'width'" ng-Onlynumber  ng-blur="stockCtrl.editCraft(fujian)"/>
                           		</div>
                           		<div ng-if="fujian.dictItem != null && fujian.dictItem.recordId != null">
                           			<ui-select ng-model="fujian.value" theme="bootstrap" ng-change="stockCtrl.editCraft(fujian)"> 
										<ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match> 
                                        	<ui-select-choices repeat="item.recordId as item in fujian.dictItem.values | filter: $select.search">
											<div ng-bind-html="item.value | highlight: $select.search"></div>
										</ui-select-choices> 
									</ui-select>
                           		</div>
                            </td>
                            
                            <td ng-bind="row.materialType.value"></td>
                            <td ng-bind="row.unit.value"></td>
                            
                            <td ng-bind="row.storehouse.name"></td>
                        </tr>
                        <tr>
                        	<td>&nbsp;</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row page-margin-top">
                    <div id="oneStep4" class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select class="form-control inline table-pageSize-width"
                                theme="bootstrap"
                                ng-model="stockCtrl.page.info.pageSize"
                                ng-change="stockCtrl.pageSizeChange(0)"
                                ng-options="pageSizeOption for pageSizeOption in stockCtrl.page.pageSizeOptions">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示{{stockCtrl.page.info.data.startCount}} / {{stockCtrl.page.info.data.endCount}}条，共 {{stockCtrl.page.info.data.count}} 条</span>
                    </div>
                    <div id="oneStep3" class="col-md-12 col-lg-6">
                        <paging class="pull-right" page="stockCtrl.page.info.data.pageNo"
                                page-size="stockCtrl.page.info.data.pageSize"
                                total="stockCtrl.page.info.data.count" adjacent="1" dots="..."
                                scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                active-class="active" disabled-class="disabled" show-prev-next="true"
                                paging-action="stockCtrl.doPage(0, page, pageSize, total)"> </paging>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- END 原料信息 -->

    <!-- BEGIN 原料出入库列表 -->
    <tab heading="原料出入库记录" active="stockCtrl.tabs.inout.active" ng-click="stockCtrl.getInOutDataList()">
        <div id="twoStep6" class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">出入库类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="stockCtrl.inoutQuery.inoutType.value" disable-auto-validate="true" ng-change="stockCtrl.doInoutQuery()">
                                        <option value="">所有</option>
                                        <option value="1">采购入库</option>
                                        <option value="2">原料入库</option>
                                        <option value="3">投料出库</option>
                                        <option value="5">补料出库</option>
                                        <option value="4">退货出库</option>
                                        <option value="6">领料出库</option>
                                        <option value="7">补货入库</option>
                                        <option value="8">送货出库</option>
                                        <option value="9">调拨入库</option>
                                        <option value="10">调拨出库</option>
                                        <option value="20">客诉退货入库</option>
                                        <!-- <option value="21">盘点调整</option> -->
                                        <option value="29">盘盈入库</option>
                                        <option value="30">盘亏出库</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.inoutQuery.no.value"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.inoutQuery.name.value"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">供应商编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.inoutQuery.supplierNo.value"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">冲红状态：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="stockCtrl.inoutQuery.status.value" class="form-control"
                                            disable-auto-validate="true">
                                        <option value="">所有</option>
                                        <option value="99999901">未冲红</option>
                                        <option value="99999903">已冲红</option>
                                        <option value="99999902">冲红单</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">所属仓库：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select multiple theme="bootstrap" ng-model="stockCtrl.storeHouseIdList" on-select="stockCtrl.selectStoreHouseId()" on-remove="stockCtrl.removeStoreHouseId($item)" disable-auto-validate="true" append-to-body = "true">
                                        <ui-select-match placeholder="请选择...">{{$item.name}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in stockCtrl.storehouseList | filter: $select.search">
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                   </div>
                   <div class="row">
                       <div class="col-md-6 col-lg-4" ng-if="stockCtrl.inoutQuery.inoutType.value == '' ||
                               stockCtrl.inoutQuery.inoutType.value == 1 ||
                               stockCtrl.inoutQuery.inoutType.value == 6">
                           <div class="form-group">
                               <label class="col-sm-3 col-md-4 control-label">产品类型：</label>
                               <div class="col-sm-7 col-md-8">
                                   <select ng-model="stockCtrl.productStatus" class="form-control"
                                           disable-auto-validate="true">
                                       <option value="">所有</option>
                                       <option value="1">ERP物料</option>
                                       <option value="2">大生态圈物料</option>
                                   </select>
                               </div>
                           </div>
                       </div>
                       <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">供应商名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.inoutQuery.supplierName.value"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">出入库时间：</label>
                                <div class="col-sm-7 col-md-8">
                                    <div class="input-prepend input-group">
										<span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
										<input type="text" class="form-control" disable-auto-validate="true"
											ng-blur="stockCtrl.initDate(stockCtrl.time)"
											kyb-daterange
											kyb-daterange-options="stockCtrl.rangeOptions"
											ng-model="stockCtrl.time"
											placeholder="请选择时间段">
									</div>
                                </div>
                            </div>
                        </div>
                        </div>
                        <div class = "row">
                            <div class="col-md-6 col-lg-4" ng-if="stockCtrl.inoutQuery.inoutType.value != 1">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">生产编号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="stockCtrl.inoutQuery.craftNo.value"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
			<!-- TJ 2017-08-29 合同编号 -->
                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.inoutQuery.inoutType.value != 1">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">合同编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.contractNo"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                       <div class="col-md-6 col-lg-4">
                           <div class="form-group">
                               <label class="col-sm-3 col-md-4 control-label">供应商合同编号：</label>
                               <div class="col-sm-7 col-md-8">
                                   <input type="text" class="form-control"
                                          ng-model="stockCtrl.supplierContractNo"
                                          disable-auto-validate="true"/>
                               </div>
                           </div>
                       </div>
                       <div class="col-md-6 col-lg-4">
                           <div class="form-group">
                               <label class="col-sm-3 col-md-4 control-label">编号：</label>
                               <div class="col-sm-7 col-md-8">
                                   <input type="text" class="form-control"
                                          ng-model="stockCtrl.queryNo"
                                          disable-auto-validate="true"/>
                               </div>
                           </div>
                       </div>
                       <div class="col-md-6 col-lg-4">
                           <div class="form-group">
                               <label class="col-sm-3 col-md-4 control-label">备注：</label>
                               <div class="col-sm-7 col-md-8">
                                   <input type="text" class="form-control"
                                          ng-model="stockCtrl.QueryRemark"
                                          disable-auto-validate="true"/>
                               </div>
                           </div>
                       </div>
                       <div class="col-md-6 col-lg-4" 
                       	ng-if="stockCtrl.inoutQuery.inoutType.value == 1 || 
                       		stockCtrl.inoutQuery.inoutType.value == 3 || 
                       		stockCtrl.inoutQuery.inoutType.value == 5">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">打印状态：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="stockCtrl.inoutQuery.statusTwo.value" class="form-control"
                                            disable-auto-validate="true">
                                        <option value="">所有</option>
                                        <option value="1">未打印</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4"
                        ng-if="stockCtrl.inoutQuery.inoutType.value == 1 || 
                       		stockCtrl.inoutQuery.inoutType.value == 3 || 
                       		stockCtrl.inoutQuery.inoutType.value == 5">
                           <div class="form-group">
                               <label class="col-sm-3 col-md-4 control-label">打印编号：</label>
                               <div class="col-sm-7 col-md-8">
                                   <input type="text" class="form-control"
                                          ng-model="stockCtrl.queryPrintNo"
                                          disable-auto-validate="true"/>
                               </div>
                           </div>
                       </div>
                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.inoutQuery.inoutType.value == 1">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">送货单号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.deliveryNo"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.inoutQuery.inoutType.value == 3">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">生产状态：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="stockCtrl.productionStatus" class="form-control">
                                        <option value="">所有</option>
                                        <option value="1">生产完成</option>
                                        <option value="2">生产未完成</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="stockCtrl.doInoutQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">原料出入库列表&nbsp;&nbsp;&nbsp;&nbsp;</div>
                <div class="caption font-blue-hoki">
	            	<button style="margin-top: -3px;" class="btn btn-default" ng-if="stockCtrl.inoutQuery.inoutType.value == 1"
	            		 ng-click="stockCtrl.managePrintTem(6)">采购入库打印模板管理
	            	</button>
	            	<button style="margin-top: -3px;" class="btn btn-default" ng-if="stockCtrl.inoutQuery.inoutType.value == 3"
	            		 ng-click="stockCtrl.managePrintTem(7)">投料出库打印模板管理
	            	</button>
	            	<button style="margin-top: -3px;" class="btn btn-default" ng-if="stockCtrl.inoutQuery.inoutType.value == 5"
	            		 ng-click="stockCtrl.managePrintTem(8)">补料出库打印模板管理
	            	</button>
            	</div>
                <div id="twoStep7" class="actions">
                	<div class="portlet-input input-inline input-small">
						<button type="button" ng-if="stockCtrl.inoutQuery.inoutType.value == 1 || stockCtrl.inoutQuery.inoutType.value == 3 || stockCtrl.inoutQuery.inoutType.value == 5" class="btn green btn-default-width" ng-click="stockCtrl.batchPrintPdf()">
							<i class="fa"></i> 进行打印
						</button>
					</div>
                   	<div class="portlet-input input-inline input-small">
                        <form action="a/stock/rawmaterial/inout/export" method="POST" enctype="multipart/form-data" target="hidden_frame1">
                            <input type="text" ng-show="false" name="material.no" value="{{stockCtrl.inoutQuery.no.value}}"/>
                               <input type="text" ng-show="false" name="material.name" value="{{stockCtrl.inoutQuery.name.value}}"/>
                               <input type="text" ng-show="false" name="supplier.shortName" value="{{stockCtrl.inoutQuery.supplierName.value}}"/>
                               <input type="text" ng-show="false" name="supplier.no" value="{{stockCtrl.inoutQuery.supplierNo.value}}"/>
                               <input type="text" ng-show="false" name="inoutType" value="{{stockCtrl.inoutQuery.inoutType.value}}"/>
                               <input type="text" ng-show="false" name="status" value="{{stockCtrl.inoutQuery.status.value}}"/>
                               <input type="text" ng-show="false" name="storehouse.recordId" value="{{stockCtrl.inoutQuery.storehouse.value}}"/>
                               <input type="text" ng-show="false" name="orderBy" value="{{stockCtrl.inoutQuery.sort.value}}"/>
                               <input type="text" ng-show="false" name="inoutTimeStartQr" value="{{stockCtrl.inoutQuery.inoutTimeStartQr.value}}"/>
                           	<input type="text" ng-show="false" name="inoutTimeEndQr" value="{{stockCtrl.inoutQuery.inoutTimeEndQr.value}}"/>
                           	<input type="text" ng-show="false" name="craftNo" value="{{stockCtrl.inoutQuery.craftNo.value}}"/>
                           	<input type="text" ng-show="false" name="contractNo" value="{{stockCtrl.contractNo}}"/>
                           	<input type="text" ng-show="false" name="queryNo" value="{{stockCtrl.queryNo}}"/>
                           	<input type="text" ng-show="false" name="remark" value="{{stockCtrl.QueryRemark}}"/>
                           	<input type="text" ng-show="false"  name="queryAll" value="{{stockCtrl.queryAll}}"/>
                            <input type="text" ng-show="false"  name="productionStatus" value="{{stockCtrl.productionStatus}}"/>
                            <input type="text" ng-show="false"  name="product" value="{{stockCtrl.productStatus}}"/>
                            <div>
                                <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出出入库</button>
                                <iframe name="hidden_frame1" id="hidden_frame1" style="display:none"></iframe>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="portlet-body">
                <div id="twoStep1" class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
                        <tr class="heading" ng-if="stockCtrl.inoutQuery.inoutType.value == 1">
                            <th>
                                <checkbox ng-model="stockCtrl.allPageChecked" value="1" ng-click="stockCtrl.selectAllPage()"></checkbox>
                            </th>
                            <th>操作</th>
                            <th>采购单号</th>
                            <th>物料编号</th>
                            <th>物料名称</th>
                            <th>出入库数量</th>
                            <th>出入库时间</th>
                            <th>送货单号</th>
                            <th>送货时间</th>
                            <th>供应商编号</th>
                            <th>供应商名称</th>
                            <th>单价</th>
                            <th>赠品数量</th>
                            <th>金额</th>
                            <th>处理人</th>
                        </tr>
                        <tr class="heading" ng-if="stockCtrl.inoutQuery.inoutType.value == 3">
                        	<th>
                            	<checkbox ng-model="stockCtrl.allPageChecked" value="1" ng-click="stockCtrl.selectAllPage()"></checkbox>
                            </th>
                            <th>操作时间</th>
                            <th>厂编</th>
                            <th>通知单号</th>
                            <th>投料单号</th>
                            <th>使用材料</th>
                            <th>使用材料规格</th>
                            <th>出库大板</th>
                            <th>出库余料</th>
                            <th>板材面积</th>
                            <th>单价</th>
                            <th>(金额)先进先出</th>
                            <th>加权平均</th>
                            <th>订单数量</th>
                            <th>投料数量</th>
                            <th>销售公司</th>
                            <th>工程尺寸</th>
                            <th>已打印编号</th>
                            <th>领料部门</th>
                            <th>类型</th>
                        </tr>
                        <tr class="heading" ng-if="stockCtrl.inoutQuery.inoutType.value != 1 && stockCtrl.inoutQuery.inoutType.value != 3">
                            <th ng-if="stockCtrl.inoutQuery.inoutType.value == 1">
                            	<checkbox ng-model="stockCtrl.allPageChecked" value="1" ng-click="stockCtrl.selectAllPage()"></checkbox>
                            </th>
                            <th id="twoStep2" ng-class="{'sorting': stockCtrl.sort.materialNo3.both, 'sorting_desc': stockCtrl.sort.materialNo3.desc, 'sorting_asc': stockCtrl.sort.materialNo3.asc}" ng-click="stockCtrl.sortClick('materialNo3')">物料编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>物料名称</th>
                            <th>库位&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>合同编号</th>
                            <th>编号</th>
                            <th>生产编号</th>
                            <th>批次明细编号</th>
                            <th>出入库类型</th>
                            <th ng-class="{'sorting': stockCtrl.sort.quantity3.both, 'sorting_desc': stockCtrl.sort.quantity3.desc, 'sorting_asc': stockCtrl.sort.quantity3.asc}" ng-click="stockCtrl.sortClick('quantity3')">出入库数量&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>结余</th>
                            <th>原材料</th>
                            <th>余料</th>
                            <th ng-class="{'sorting': stockCtrl.sort.operateDate3.both, 'sorting_desc': stockCtrl.sort.operateDate3.desc, 'sorting_asc': stockCtrl.sort.operateDate3.asc}" ng-click="stockCtrl.sortClick('operateDate3')">出入库时间&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th ng-class="{'sorting': stockCtrl.sort.supplierNo3.both, 'sorting_desc': stockCtrl.sort.supplierNo3.desc, 'sorting_asc': stockCtrl.sort.supplierNo3.asc}" ng-click="stockCtrl.sortClick('supplierNo3')">供应商编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th ng-class="{'sorting': stockCtrl.sort.supplierName3.both, 'sorting_desc': stockCtrl.sort.supplierName3.desc, 'sorting_asc': stockCtrl.sort.supplierName3.asc}" ng-click="stockCtrl.sortClick('supplierName3')">供应商名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>单价&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>赠品数量</th>
                            <th>(金额)先进先出</th>
                            <th>加权平均&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>仓库&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th ng-class="{'sorting': stockCtrl.sort.status3.both, 'sorting_desc': stockCtrl.sort.status3.desc, 'sorting_asc': stockCtrl.sort.status3.asc}" ng-click="stockCtrl.sortClick('status3')">状态&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>处理人</th>
                            <th>SET尺寸</th>

                            <th>备注</th>
                            <th>已打印编号</th>
                            <th>领料部门</th>
                            <th>类型</th>
                            <th>物料单位</th>
                            <th>补收差价金额</th>
                            <th id="twoStep3">操作&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in stockCtrl.page.inout.data.list" ng-dblclick="stockCtrl.inoutDetailClick($index)" ng-if="stockCtrl.inoutQuery.inoutType.value == 1">
                            <td>
                                <checkbox ng-model="row.checked" value="1"></checkbox>
                            </td>
                            <td>
                                <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-click="stockCtrl.bindDelivery(row)">绑定送货</a>
                            </td>
                            <td ng-bind="row.purchasingNo" ng-click="stockCtrl.inoutDetailClick($index)"></td>
                            <td ng-bind="row.material.no"></td>
                            <td ng-bind="row.material.name"></td>
                            <td ng-bind="row.quantity | number:4"></td>
                            <td ng-bind="row.operateDate"></td>
                            <td ng-bind="row.deliveryNo"></td>
                            <td ng-bind="row.deliveryDateStr"></td>
                            <td ng-bind="row.supplier.no"></td>
                            <td ng-bind="row.supplier.shortName"></td>
                            <td ng-bind="row.price |number:3"></td>
                            <td ng-bind="row.giveNum |number:3"></td>
                            <td ng-bind="row.cost  |number:3"></td>
                            <td ng-bind="row.createdBy.userName"></td>
                        </tr>
                        <tr ng-repeat="row in stockCtrl.page.inout.data.list" ng-dblclick="stockCtrl.inoutDetailClick($index)" ng-if="stockCtrl.inoutQuery.inoutType.value == 3">
                        	<td>
		                    	<checkbox ng-model="row.checked" value="1"></checkbox>
		                    </td>
		                    <td ng-bind="row.operateDate"></td>
                            <td ng-bind="row.craftNo"></td>
                            <td ng-bind="row.notifiNo"></td>
                            <td ng-bind="row.feedNo"></td>
                            <td>{{row.material.name}}/{{row.material.no}}</td>
                            <td ng-bind="row.material.specification"></td>
                            <td ng-bind="row.daBanQty"></td>
                            <td ng-bind="row.remainQty"></td>
                            <td ng-bind="row.bathArea"></td>
                            <td ng-bind="row.price |number:3"></td>
                            <td>
                                <a href="#" ng-click="stockCtrl.openFistInFirstRecord(row.fisrtInFirstOutList)">{{row.firstInFirstAmount ? row.firstInFirstAmount : 0}}</a>
                            </td>
                            <td ng-bind="row.amountTwo"></td>
                            <td ng-bind="row.orderQuantity"></td>
                            <td ng-bind="row.pcsQuantity"></td>
                            <td ng-bind="row.saleName"></td>
                            <td ng-bind="row.setSize"></td>
                            <td ng-bind="row.printTimeNo"></td>
                            <td ng-bind="row.deptName"></td>
                            <td ng-bind="row.materialTypeVal"></td>
                        </tr>
                        <tr ng-repeat="row in stockCtrl.page.inout.data.list" ng-dblclick="stockCtrl.inoutDetailClick($index)" ng-if="row.inoutType != 13 && stockCtrl.inoutQuery.inoutType.value != 1
                         && stockCtrl.inoutQuery.inoutType.value != 3">
                            <td ng-if="stockCtrl.inoutQuery.inoutType.value == 5">
		                    	<checkbox ng-model="row.checked" value="1"></checkbox>
		                    </td>
                            <td><a ng-bind="row.material.no" ng-click="stockCtrl.inoutDetailClick($index)"></a></td>
                            <td ng-bind="row.material.name"></td>
                            <td>{{row.placeName}}-{{row.stockPlaceComName}}</td>
                            <td ng-if="row.inoutType!=1" ng-bind="row.contractNo"></td>
                            <td ng-if="row.inoutType==1" ng-bind="row.supplierContractNo"></td>
                            <td ng-bind="row.purchasingNo" ng-if="!(row.inoutType == 11 || row.inoutType == 12)"></td>
                            <td ng-bind="row.craftNo" ng-if="row.inoutType == 3"></td>
                            <td>
                            	<span ng-if="row.productBatchId && row.productBatchDeailId">
                            		A{{row.productBatchId}}B{{row.productBatchDeailId}}
                            	</span>
                            </td>
                            <td ng-bind="row.returnsNo" ng-if = "row.inoutType == 5"></td>
                            <td ng-if = "row.inoutType != 5 && row.inoutType != 3"></td>
                            <td ng-bind="row.inoutType | inoutTypeFilter" ng-if="!(row.inoutType == 11 || row.inoutType == 12|| row.inoutType == 2|| row.inoutType == 6)"></td>
                            <td ng-if="row.inoutType == 11 || row.inoutType == 12|| row.inoutType == 2|| row.inoutType == 6">
                            	<span ng-if="row.inoutType == 2&&!(row.remark&&row.remark.indexOf('盘盈')!=-1)">原料入库</span>
                            	<span ng-if="row.inoutType == 6&&!(row.remark&&row.remark.indexOf('盘亏')!=-1)">原材料出</span>
                            	<span ng-if="row.inoutType == 2&&row.remark&&row.remark.indexOf('盘盈')!=-1">盘盈入库</span>
                            	<span ng-if="row.inoutType == 6&&row.remark&&row.remark.indexOf('盘亏')!=-1">盘亏出库</span>
                            	<span ng-if="row.inoutType == 11">期初</span>
                            	<span ng-if="row.inoutType == 12">结余</span>
                            </td>
                            <td class="text-right" ng-bind="row.quantity | number:4" ng-if="!(row.inoutType == 11 || row.inoutType == 12)"></td>
                            <td ng-if="row.inoutType == 11 || row.inoutType == 12"></td>
                            <td class="text-right" ng-bind="row.quantity | number:4" ng-if="row.inoutType == 11 || row.inoutType == 12"></td>
                            <td ng-if="!(row.inoutType == 11 || row.inoutType == 12)"></td>
                            <td><span ng-if="row.remainQty && row.remainQty > 0 && (row.inoutType == 3 || row.inoutType == 5)">{{row.daBanQty}}</span></td>
                            <td><span ng-if="row.remainQty && row.remainQty > 0 && (row.inoutType == 3 || row.inoutType == 5)">{{row.remainQty}}</span></td>
                            <td ng-bind="row.operateDate"></td>
                            <td ng-bind="row.supplier.no"></td>
                            <td ng-bind="row.supplier.shortName"></td>
                            <td ng-bind="row.price |number:3"></td>
                            <td ng-bind="row.giveNum |number:3"></td>
                            <td>
                                <a href="#" ng-click="stockCtrl.openFistInFirstRecord(row.fisrtInFirstOutList)">{{row.firstInFirstAmount ? row.firstInFirstAmount : 0}}</a>
                            </td>
                            <td ng-bind="row.cost  |number:3"></td>
                            <td ng-bind="row.storehouse.name"></td>
                            <td ng-if="row.status === 99999901">
                                <span class="label label-sm label-default">未冲红</span>
                            </td>
                            <td ng-if="row.status === 99999902">
                                <span class="label label-sm label-success">冲红单 </span>
                            </td>
                            <td ng-if="row.status === 99999903">
                                <span class="label label-sm label-danger">已冲红</span>
                            </td>
                            <td ng-if="!row.status || row.status == ''">无</td>
                            <td ng-bind="row.createdBy.userName"></td>
                            <td ng-bind="row.setSize"></td>

                            <td >{{row.remark}}{{row.writeOffCause}}</td>
                            <td ng-bind="row.printTimeNo"></td>
                            <td ng-bind="row.deptName"></td>
                            <td ng-bind="row.materialTypeVal"></td>
                            <td ng-bind="row.unit"></td>
                            <td ng-bind="row.deferencetotal"></td>
                            <td ng-if="row.inoutType!=5&&row.inoutType!=3&&row.inoutType!=8&&row.inoutType!=21">
                                <a ng-if="((!row.goodsCheckFlag && !row.materialCheckFlag) || row.inoutType==6) &&stockCtrl.right.edit &&row.status === 99999901" href="javascript:void(0);" class="btn btn-xs btn-default" ng-click="stockCtrl.isInventory($index)" ><i class="glyphicon glyphicon-trash font-red"></i> 冲红</a>
                            </td>
                            <td ng-if="(row.inoutType==5||row.inoutType==3)&&row.inoutType!=21">
                                <a ng-if="!row.goodsCheckFlag && !row.materialCheckFlag&&stockCtrl.right.edit &&row.status === 99999901" href="javascript:void(0);" class="btn btn-xs btn-default" ng-click="stockCtrl.isInventory($index)" ><i class="glyphicon glyphicon-trash font-red"></i> 冲红</a>
                            </td>
                            <td ng-if="row.inoutType==8">
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row page-margin-top">
                    <div id="twoStep5" class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select class="form-control inline table-pageSize-width"
                                ng-model="stockCtrl.page.inout.pageSize"
                                ng-change="stockCtrl.pageSizeChange(1)"
                                disable-auto-validate="true"
                                ng-options="pageSizeOption for pageSizeOption in stockCtrl.page.pageSizeOptions">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示{{stockCtrl.page.inout.data.startCount}} / {{stockCtrl.page.inout.data.endCount}}条，共 {{stockCtrl.page.inout.data.count}} 条</span>
                    </div>
                    <div id="twoStep4" class="col-md-12 col-lg-6">
                        <paging class="pull-right" page="stockCtrl.page.inout.data.pageNo"
                                page-size="stockCtrl.page.inout.data.pageSize"
                                total="stockCtrl.page.inout.data.count" adjacent="1" dots="..."
                                scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                active-class="active" disabled-class="disabled" show-prev-next="true"
                                paging-action="stockCtrl.doPage(1, page, pageSize, total)"> </paging>
                    </div>
                </div>
            </div>
        </div>

    </tab>
    <!-- END 原料出入库列表 -->

    <!-- BEGIN 原料出入库详情 -->
    <tab active="stockCtrl.tabs.inoutDetail.active" ng-show="stockCtrl.tabs.inoutDetail.show">
        <tab-heading>原料出入库记录详情 <i class="fa fa-times set-cursor-pointer" ng-click="stockCtrl.hideInoutDetail()"></i> </tab-heading>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">{{stockCtrl.inoutRecord.material.no}}</div>
            </div>
            <div class="portlet-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料编号:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.material.no"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料名称:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.material.name"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">规格型号:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.material.specification"></span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">数量:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.quantity | number:4"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">赠品数量:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.giveNum | number:4"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">入库时间:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.operateDate"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">放置仓库:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.storehouse.name"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">单价:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.price"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 col-lg-8">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label">备注：</label>
                                <div class="col-sm-7 col-md-10">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.remark"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 col-lg-8">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label">状态：</label>
                                <div class="col-sm-7 col-md-10">
                                    <span class="form-control" ng-if="stockCtrl.inoutRecord.status==99999901">未冲红</span>
                                    <span class="form-control" ng-if="stockCtrl.inoutRecord.status==99999903">已冲红</span>
                                    <span class="form-control" ng-if="stockCtrl.inoutRecord.status==99999902">冲红单</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

    </tab>
    <!-- END 原料出入库详情 -->

    <!-- BEGIN 原料出入库冲红 -->
    <tab active="stockCtrl.tabs.inoutRushRed.active" ng-show="stockCtrl.tabs.inoutRushRed.show">
        <tab-heading>原料出入库记录冲红 <i class="fa fa-times set-cursor-pointer" ng-click="stockCtrl.hideRushRed()"></i> </tab-heading>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">对物料编号为[{{stockCtrl.inoutRecord.material.no}}]的记录进行冲红</div>
            </div>
            <div class="portlet-body">
                <form class="form-horizontal" name="red_form" novalidate="novalidate" ng-init="stockCtrl.setRushRedFormScope(this)" ng-submit="stockCtrl.submitOk(red_form);" ng-submit-force="true">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料编号:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.material.no"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料名称:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.material.name"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">规格型号:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.material.specification"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">出入库数量:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.quantity | number:4"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">出入库时间:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.operateDate"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">放置仓库:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="stockCtrl.inoutRecord.storehouse.name"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>冲红数量:</label>
                                <div class="col-sm-7 col-md-8">
                                 <input class="form-control" type="number" step="any"
                                        placeholder="请输入冲红数量" ng-blur="stockCtrl.changeRushredQuantity()"
                                        ng-model="stockCtrl.inoutRecord.rushredQuantity"
                                        style="text-align: left;"
                                        ng-disabled="(stockCtrl.inoutRecord.inoutType!=1&&stockCtrl.inoutRecord.inoutType!=2&&stockCtrl.inoutRecord.inoutType!=7)
                                                    || (stockCtrl.inoutRecord.supplyChainId || stockCtrl.inoutRecord.internalSupplyChainId)"
                                        />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 col-lg-8">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label">备注：</label>
                                <div class="col-sm-7 col-md-10">
                                    <div class="form-control" ng-bind-html="stockCtrl.inoutRecord.remark" ></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 col-lg-8">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>冲红原因：</label>
                                <div class="col-sm-7 col-md-10">
                                    <textarea class="form-control" firstfocus="{{stockCtrl.focus}}" ng-model="stockCtrl.inoutRecord.writeOffCause" 
                                    ng-maxlength="255" required></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group">
                            <div class="col-sm-offset-4 col-sm-10">
                                <button type="submit" class="btn btn-primary btn-default-width btn-margin-left-15"><i class="fa fa-save"></i> 确&nbsp;定</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </tab>
    <!-- END 原料出入库冲红 -->
    
    <!-- BEGIN 原料期间结存表 -->
    <tab heading="原料期间结存表" ng-click="stockCtrl.groupCheckQuery()" ng-show = "false">
        <div class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            
                 <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                       <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">出入库时间：</label>
                                <div class="col-sm-7 col-md-8">
                                    <div class="input-prepend input-group">
										<span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
										<input type="text" class="form-control" disable-auto-validate="true"
											ng-blur="stockCtrl.initDateTwo(stockCtrl.timeTwo)"
											kyb-daterange 
											kyb-daterange-options="stockCtrl.rangeOptionsTwo"
											ng-model="stockCtrl.timeTwo" 
											placeholder="请选择时间段">
									</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select  ng-model="stockCtrl.inoutPlaceQuery.material" theme="bootstrap" disable-auto-validate="true">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
                                        <ui-select-choices refresh="stockCtrl.addMaterialItems($select.search)" refresh-delay="500" repeat="item in stockCtrl.materialQueryList | filter: $select.search">
                                                <small>
                                               		<span><span style="color: blue;">编号:</span>{{item.no}}<br></span>
                                               		<span><span style="color: blue;">名称:</span>{{item.name}}<br></span>
                                               		<span><span style="color: blue;">规格:</span>{{item.specification}}<br></span>
                                               </small>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div> 
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">所属仓库：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select  ng-model="stockCtrl.inoutPlaceQuery.storehouse.value" theme="bootstrap" disable-auto-validate="true" on-select="stockCtrl.fiterMaterialPlace()">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                        <ui-select-choices repeat="item in stockCtrl.storehouseList | filter: $select.search">
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">库位：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select  ng-model="stockCtrl.inoutPlaceQuery.materialPlace" theme="bootstrap" disable-auto-validate="true">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                        <ui-select-choices repeat="item in stockCtrl.placeList | filter: $select.search">
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">供应商编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.inoutPlaceQuery.supplierNo.value"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">冲红状态：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="stockCtrl.inoutPlaceQuery.status.value" class="form-control"
                                            disable-auto-validate="true">
                                        <option value="">所有</option>
                                        <option value="99999901">未冲红</option>
                                        <option value="99999903">已冲红</option>
                                        <option value="99999902">冲红单</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">出入库类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="stockCtrl.inoutPlaceQuery.inoutType.value" disable-auto-validate="true">
                                        <option value="">所有</option>
                                        <option value="1">采购入库</option>
                                        <option value="2">原料入库</option>
                                        <option value="3">投料出库</option>
                                        <option value="5">补料出库</option>
                                        <option value="4">退货出库</option>
                                        <option value="6">领料出库</option>
                                        <option value="7">补货入库</option>
                                        <option value="8">原料合同出库</option>
                                        <option value="21">盘点调整</option>
                                        <option value="9">通知单占用出库</option>
                                        <option value="10">通知单释放入库</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">供应商名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.inoutPlaceQuery.supplierName.value"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">生产编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.inoutPlaceQuery.craftNo.value"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>-->
			<!-- TJ 2017-08-29 合同编号 -->
                        <!-- <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">合同编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="stockCtrl.contractNo"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>

                       <div class="col-md-6 col-lg-4" ng-if="!stockCtrl.materialPlaceFlag">
                           <div class="form-group">
                               <label class="col-sm-3 col-md-4 control-label">供应商合同编号：</label>
                               <div class="col-sm-7 col-md-8">
                                   <input type="text" class="form-control"
                                          ng-model="stockCtrl.supplierContractNo"
                                          disable-auto-validate="true"/>
                               </div>
                           </div>
                       </div> -->
					   <div class="col-md-6 col-lg-4" >
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.materialNo" disable-auto-validate="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" >
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.materialName" disable-auto-validate="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" >
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="stockCtrl.materialTypeId"
                                            ng-options="a.recordId as a.value for a in stockCtrl.materialTypeList" class="form-control"
                                            disable-auto-validate="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" >
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">规格型号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.specification" disable-auto-validate="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" >
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">所属仓库：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select multiple theme="bootstrap" ng-model="stockCtrl.storeHouseIdList" on-select="stockCtrl.selectStoreHouseId()" on-remove="stockCtrl.removeStoreHouseId($item)" disable-auto-validate="true" append-to-body = "true">
                                        <ui-select-match placeholder="请选择...">{{$item.name}}</ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in stockCtrl.storehouseList | filter: $select.search">
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-md-6 col-lg-4" >
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">库位：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.materialName" disable-auto-validate="true" />
                                </div>
                            </div>
                        </div> -->
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="stockCtrl.groupCheckQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                        </div>
                    </div>
                </form>
            </div>
        
            
        </div>
        <div class="portlet light bordered">
            <!-- <div class="portlet-title">
                <div class="caption font-blue-hoki">
           			<div class="row" style="padding-left: 3rem;">
           				<div class="portlet-input input-inline input-small">
           					<input type="radio" name = "radio" ng-click="stockCtrl.getGroupCenterBillList()" ng-checked="stockCtrl.reportOne"/>
                               <label type="radio" ng-click="stockCtrl.getGroupCenterBillList()">月份数据</label>
           				</div>
           				<div class="portlet-input input-inline input-small">
           					<input type="radio" name = "radio" ng-click="stockCtrl.getGroupCenterDataList()" ng-checked="stockCtrl.reportThree"/>
                               <label type="radio" ng-click="stockCtrl.getGroupCenterDataList()">月统计数据</label>
           				</div>
           			</div>
           		</div>
            </div> -->
            <div class="portlet-body">
                
                <div id="twoStep7" class="actions">
                    <div class="collapse navbar-collapse navbar-ex1-collapse">
                    	<div class="portlet-input input-inline input-small" style="float:right;">
	                        <form action="a/stock/rawmaterial/exportMaterialMonth" method="POST" enctype="multipart/form-data" target="hidden_frame1">
	                            <input type="text" ng-show="false" name="material.recordId" value="{{stockCtrl.inoutPlaceQuery.material.recordId}}"/>
	                            <input type="text" ng-show="false" name="stockPlaceId" value="{{stockCtrl.inoutPlaceQuery.materialPlace.recordId}}"/>
	                            <input type="text" ng-show="false" name="inoutType" value="{{stockCtrl.inoutPlaceQuery.inoutType.value}}"/>
                                <input type="text" ng-show="false" name="supplier.shortName" value="{{stockCtrl.inoutPlaceQuery.supplierName.value}}"/>
                                <input type="text" ng-show="false" name="supplier.no" value="{{stockCtrl.inoutPlaceQuery.supplierNo.value}}"/>
                                <input type="text" ng-show="false" name="status" value="{{stockCtrl.inoutPlaceQuery.status.value}}"/>
                                <input type="text" ng-show="false" name="storehouse.recordId" value="{{stockCtrl.inoutPlaceQuery.storehouse.value.recordId}}"/>
                                <input type="text" ng-show="false" name="inoutTimeStartQr" value="{{stockCtrl.inoutPlaceQuery.inoutTimeTwoStartQr.value}}"/>
                            	<input type="text" ng-show="false" name="inoutTimeEndQr" value="{{stockCtrl.inoutPlaceQuery.inoutTimeTwoEndQr.value}}"/>
                            	<input type="text" ng-show="false" name="craftNo" value="{{stockCtrl.inoutPlaceQuery.craftNo.value}}"/>
                            	<input type="text" ng-show="false" name="contractNo" value="{{stockCtrl.contractNo}}"/>
                            	<input type="text" ng-show="false" name="remark" value="{{stockCtrl.remark}}"/>
                            	
                            	<input type="text" ng-show="false" name="materialNo" value="{{stockCtrl.materialNo}}"/>
                            	<input type="text" ng-show="false" name="materialName" value="{{stockCtrl.materialName}}"/>
                            	<input type="text" ng-show="false" name="materialTypeId" value="{{stockCtrl.materialTypeId}}"/>
                            	<input type="text" ng-show="false" name="specification" value="{{stockCtrl.specification}}"/>
                            	<input type="text" ng-show="false" name="storeHouseId" value="{{stockCtrl.storeHouseId}}"/>
                            	
                            	<input type="text" ng-show="false"  name="queryAll" value="{{stockCtrl.queryAll}}"/>
	                            <div>
	                                <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出原料期间结存表</button>
	                                <iframe name="hidden_frame1" id="hidden_frame1" style="display:none"></iframe>
	                            </div>
	                        </form>
	                    </div>
	                    
	                   <!--  <div class="portlet-input input-inline input-small" style="float:right;">
	                        <form action="a/stock/rawmaterial/exportMonthCheck" method="POST" enctype="multipart/form-data" target="hidden_frame1">
	                            <input type="text" ng-show="false" name="material.recordId" value="{{stockCtrl.inoutPlaceQuery.material.recordId}}"/>
	                            <input type="text" ng-show="false" name="stockPlaceId" value="{{stockCtrl.inoutPlaceQuery.materialPlace.recordId}}"/>
	                            <input type="text" ng-show="false" name="inoutType" value="{{stockCtrl.inoutPlaceQuery.inoutType.value}}"/>
                                <input type="text" ng-show="false" name="supplier.shortName" value="{{stockCtrl.inoutPlaceQuery.supplierName.value}}"/>
                                <input type="text" ng-show="false" name="supplier.no" value="{{stockCtrl.inoutPlaceQuery.supplierNo.value}}"/>
                                <input type="text" ng-show="false" name="status" value="{{stockCtrl.inoutPlaceQuery.status.value}}"/>
                                <input type="text" ng-show="false" name="storehouse.recordId" value="{{stockCtrl.inoutPlaceQuery.storehouse.value.recordId}}"/>
                                <input type="text" ng-show="false" name="inoutTimeStartQr" value="{{stockCtrl.inoutPlaceQuery.inoutTimeTwoStartQr.value}}"/>
                            	<input type="text" ng-show="false" name="inoutTimeEndQr" value="{{stockCtrl.inoutPlaceQuery.inoutTimeTwoEndQr.value}}"/>
                            	<input type="text" ng-show="false" name="craftNo" value="{{stockCtrl.inoutPlaceQuery.craftNo.value}}"/>
                            	<input type="text" ng-show="false" name="contractNo" value="{{stockCtrl.contractNo}}"/>
                            	<input type="text" ng-show="false" name="remark" value="{{stockCtrl.remark}}"/>
                            	
                            	<input type="text" ng-show="false" name="materialNo" value="{{stockCtrl.materialNo}}"/>
                            	<input type="text" ng-show="false" name="materialName" value="{{stockCtrl.materialName}}"/>
                            	<input type="text" ng-show="false" name="materialTypeId" value="{{stockCtrl.materialTypeId}}"/>
                            	<input type="text" ng-show="false" name="specification" value="{{stockCtrl.specification}}"/>
                            	<input type="text" ng-show="false" name="storeHouseId" value="{{stockCtrl.storeHouseId}}"/>
                            	
                            	<input type="text" ng-show="false"  name="queryAll" value="{{stockCtrl.queryAll}}"/>
	                            <div>
	                                <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 标记错误111</button>
	                                <iframe name="hidden_frame1" id="hidden_frame1" style="display:none"></iframe>
	                            </div>
	                        </form>
	                    </div> -->
                    </div>
                </div>
                <div class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-primary table-bordered table-condensed table-primary table-hover" >
                        <thead>
                        <tr class="heading" >
                            <th>物料编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>物料名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                            <th>规格型号</th>
                            
                            <th>期初</th>
                            <th>累积入库</th>
                            <th>累积出库</th>
                            <th>结余</th>
                            
                            <th>在库归属公司</th>
                            <th>在途归属数量</th>
                            <th>加权平均单价</th>
                            <th>期初单价</th>
                            <th>最新采购价</th>
                            <th>月份</th>
                            <th>物料类型</th>
                            <th>单位</th>
                            <th>仓库</th>
                        </tr>
                        </thead>
                        <tbody>
                       
                       
                        <tr ng-repeat="row in stockCtrl.page.infoMonth.data.list" >
                            <td>
                            {{row.materialNo}}
                            </td>
                            <td>{{row.materialName}}</td>
                            <td>{{row.specification}}</td>
                            
                            <td>{{row.startStocks}}</td>
                            <td>{{row.inStocks}}</td>
                            <td>{{row.outStocks}}</td>
                            <td>{{row.quantity}}</td>
                            
                           	<td>{{row.stockSpec}}</td>
                            <td>{{row.purchSpec}}</td>
                            
                            <td>{{row.price}}</td>
                            <td>{{row.startPrice}}</td>
                            <td>{{row.newPurchPrice}}</td>
                            <th>{{row.monthNum}}M</th>
                            <td>{{row.materialTypeVal}}</td>
                            <td>{{row.unitValue}}</td>
                            <td>{{row.storeName}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row page-margin-top">
                    <div id="twoStep5" class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select class="form-control inline table-pageSize-width"
                                ng-model="stockCtrl.page.inout.pageSize"
                                ng-change="stockCtrl.pageSizeChange(3)"
                                disable-auto-validate="true"
                                ng-options="pageSizeOption for pageSizeOption in stockCtrl.page.pageSizeOptions">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示{{stockCtrl.page.infoMonth.data.startCount}} / {{stockCtrl.page.infoMonth.data.endCount}}条，共 {{stockCtrl.page.infoMonth.data.count}} 条</span>
                    </div>
                    <div id="twoStep4" class="col-md-12 col-lg-6">
                        <paging class="pull-right" page="stockCtrl.page.infoMonth.data.pageNo"
                                page-size="stockCtrl.page.infoMonth.data.pageSize"
                                total="stockCtrl.page.infoMonth.data.count" adjacent="1" dots="..."
                                scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                active-class="active" disabled-class="disabled" show-prev-next="true"
                                paging-action="stockCtrl.doPage(3, page, pageSize, total)"> </paging>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- end 原料期间结存表 -->

    <!-- start 常用料规格管理 -->
    <tab heading="常用料规格管理" active="stockCtrl.tabs.comm.active" ng-click="stockCtrl.doQueryCommon()">
        <div id="listStep6" class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">品牌：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.queryCommon.producerName" disable-auto-validate="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">覆铜板材：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.queryCommon.copperBoardName" disable-auto-validate="true" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="stockCtrl.doQueryCommon()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">常用料规格列表</div>
                <div id="listStep7" class="actions">
                    <div class="portlet-input input-inline input-small">
                        <button type="button" class="btn green btn-default-width" ng-click="stockCtrl.openEdit(1)"><i class="fa fa-plus"></i> 添加常用料</button>
                    </div>
                </div>
            </div>
            <div class="portlet-body">
                <div id="listStep1" class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
                        <tr class="heading">
                            <th width="20%">品牌</th>
                            <th width="20%">覆铜板材</th>
                            <th width="20%">长*宽</th>
                            <th id="listStep3">操作&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="item in stockCtrl.page.common.data.list" ng-dblclick="stockCtrl.ctrlShowDetail($index)">
                            <td ng-bind="item.producerName"></td>
                            <td ng-bind="item.copperBoardName"></td>
                            <td>{{item.length }} * {{item.width}}</td>
                            <td>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.openEdit(2,item)"><i class="fa fa-edit font-blue"></i>编辑</a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.deleteOpen(item.recordId)"><i class="fa fa-times font-red"></i>删除</a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row page-margin-top">
                    <div id="listStep5" class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select  class="form-control inline table-pageSize-width"
                                 disable-valid-styling="true"
                                 disable-invalid-styling="true"
                                 ng-model="stockCtrl.page.common.pageSize"
                                 ng-change="stockCtrl.pageSizeCommonChange(0)"
                                 ng-options="option for option in stockCtrl.page.options">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{stockCtrl.page.common.data.startCount}} / {{stockCtrl.page.common.data.endCount}} 条，共 {{stockCtrl.page.common.data.count}} 条</span>
                    </div>
                    <div id="listStep4" class="col-md-12 col-lg-6">
                        <paging
                                class="pull-right"
                                ul-class="pagination" active-class="active" disabled-class="disabled"
                                page="stockCtrl.page.common.data.pageNo" page-size="stockCtrl.page.common.data.pageSize"
                                total="stockCtrl.page.common.data.count" adjacent="1" dots="..."
                                croll-top="false" hide-if-empty="false" show-prev-next="true"
                                paging-action="stockCtrl.doCommonPage(page, pageSize, total)">
                        </paging>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- end 常用料规格管理 -->

    <!-- start 常用料规格编辑 -->
    <tab active="stockCtrl.tabs.commEditForm.active" ng-show="stockCtrl.tabs.commEditForm.show">
        <tab-heading>常用料规格编辑 <i class="fa fa-times set-cursor-pointer" ng-click="stockCtrl.hideCommonMaterial()"></i></tab-heading>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">常用料规格编辑
                </div>
            </div>
            <div class="portlet-body">
                <form id="infoStep1" class="form-horizontal" name="editForm" ng-init="stockCtrl.setFormScope(this)" novalidate="novalidate" ng-submit="stockCtrl.saveCommonMaterial();" ng-submit-force="true">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">品牌：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control"
                                            ng-model="stockCtrl.edit.producerId"
                                            ng-options="gen.recordId as gen.value for gen in stockCtrl.producerList">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">覆铜板材：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control"
                                            ng-model="stockCtrl.edit.copperBoardId"
                                            ng-options="gen.recordId as gen.value for gen in stockCtrl.copperBoardList">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">长：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input class="form-control" type="number" onkeyup="value=value.replace(/[^\d]/g,'')" ng-model="stockCtrl.edit.width"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">宽：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input class="form-control" type="number" onkeyup="value=value.replace(/[^\d]/g,'')" ng-model="stockCtrl.edit.length"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="infoStep7" class="form-group">
                        <div class="col-sm-offset-4 col-sm-10">
                            <button type="submit" class="btn btn-primary btn-default-width btn-margin-left-15" ><i class="fa fa-save"></i>&nbsp;&nbsp;保&nbsp;&nbsp;存</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </tab>
    <!-- end 常用料规格编辑 -->

    <!-- start 厂内报价记录清单  -->
    <tab heading="厂内报价记录清单" active="stockCtrl.tabs.quotationForm.active" ng-click="stockCtrl.quotationRecord()">
        <div class="portlet light bordered">
            <div id="oneStep5" class="panel panel-default">
                <div class="panel-heading font-blue-hoki">查询</div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">时间：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <div class="input-prepend input-group">
                                            <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                            <input type="text" class="form-control" disable-auto-validate="true"
                                                   ng-blur="stockCtrl.initDateThree(stockCtrl.timeThree)"
                                                   kyb-daterange
                                                   kyb-daterange-options="stockCtrl.rangeOptionsThree"
                                                   ng-model="stockCtrl.timeThree"
                                                   placeholder="请选择时间段">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">物料编号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-model="stockCtrl.materialNoThree" disable-auto-validate="true" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default btn-default-width pull-right" ng-click="stockCtrl.quotationRecord()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">厂内报价记录清单</div>
                </div>
                <div class="portlet-body">
                    <div id="oneStep1" class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                            <thead>
                            <tr class="heading">
                                <th>审批单号</th>
                                <th>附件列表</th>
                                <th>物料编号</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody ng-repeat="row in stockCtrl.page.infoQuotation.data.list">
                            <tr>
                                <td>{{ row.no }}</td> <!-- 显示行号 -->
                                <td>
                                    <div ng-repeat="item in row.materialAttachementsList">
                                        文件{{ $index + 1 }}：
                                        <a href="#" ng-click="stockCtrl.downloadFile(item,2)">{{ item.orgFileName }}</a><br/>
                                    </div>
                                </td>
                                <td>
                                    {{row.materialNo}}
                                </td>
                                <td>
                                    <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.openFlag(row)">
                                        <i class="fa fa-adjust font-blue"></i>
                                        <span ng-if="!row.packUpFlag">展开</span>
                                        <span ng-if="row.packUpFlag">收缩</span>
                                    </a>
                                </td>
                            </tr>
                            <tr ng-if="row.packUpFlag">
                                <td colspan="4">
                                    <div class="form-group">&nbsp;&nbsp;&nbsp材料列表
                                        <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                                            <thead>
                                            <tr class="heading">
                                                <th style="text-align: center;">编号</th>
                                                <th style="text-align: center;">名称</th>
                                                <th style="text-align: center;">规格</th>
                                                <th style="text-align: center;">原价</th>
                                                <th style="text-align: center;">新价</th>

                                                <th style="text-align: center;">原提前期</th>
                                                <th style="text-align: center;">新提前期</th>
                                            </tr>
                                            </thead>
                                            <tbody ng-repeat="material in row.materialList track by $index">
                                            <tr>
                                                <td style="text-align: center;" ng-bind="material.no"></td>
                                                <td style="text-align: center;" ng-bind="material.name"></td>
                                                <td style="text-align: center;" ng-bind="material.specification"></td>
                                                <td style="text-align: center;" ng-bind="material.initPrice ?  material.initPrice : 0"></td>
                                                <td style="text-align: center;" ng-bind="material.erpRawmaterialPrice ? material.erpRawmaterialPrice : 0"></td>

                                                <td style="text-align: center;" ng-bind="material.initLeadTime ?  material.initLeadTime : 0"></td>
                                                <td style="text-align: center;" ng-bind="material.erpRawmaterialLeadTime ? material.erpRawmaterialLeadTime : 0"></td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row page-margin-top">
                        <div id="oneStep4" class="col-md-12 col-lg-6">
                            <span class="inline">每页</span>
                            <select class="form-control inline table-pageSize-width"
                                    theme="bootstrap"
                                    ng-model="stockCtrl.page.infoQuotation.pageSize"
                                    ng-change="stockCtrl.pageSizeChange(4)"
                                    ng-options="pageSizeOption for pageSizeOption in stockCtrl.page.pageSizeOptions">
                            </select>
                            <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示{{stockCtrl.page.infoQuotation.data.startCount}} / {{stockCtrl.page.infoQuotation.data.endCount}}条，共 {{stockCtrl.page.infoQuotation.data.count}} 条</span>
                        </div>
                        <div id="oneStep3" class="col-md-12 col-lg-6">
                            <paging class="pull-right" page="stockCtrl.page.infoQuotation.data.pageNo"
                                    page-size="stockCtrl.page.infoQuotation.data.pageSize"
                                    total="stockCtrl.page.infoQuotation.data.count" adjacent="1" dots="..."
                                    scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                    active-class="active" disabled-class="disabled" show-prev-next="true"
                                    paging-action="stockCtrl.doPage(4, page, pageSize, total)"> </paging>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </tab>


    <tab heading="出入库管理-先进先出法" ng-click="stockCtrl.firstFirstOut()">
        <div class="portlet light bordered">
            <div id="oneStep5" class="panel panel-default">
                <div class="panel-heading font-blue-hoki">查询</div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">时间：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <div class="input-prepend input-group">
                                            <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                            <input type="text" class="form-control" disable-auto-validate="true"
                                                   ng-blur="stockCtrl.initDateFour(stockCtrl.timeFour)"
                                                   kyb-daterange
                                                   kyb-daterange-options="stockCtrl.rangeOptionsFour"
                                                   ng-model="stockCtrl.timeFour"
                                                   placeholder="请选择时间段">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">物料编号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-model="stockCtrl.materialNoFour" disable-auto-validate="true" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">物料名称：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control" ng-model="stockCtrl.materialNameTwo" disable-auto-validate="true" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">物料类型：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <select ng-model="stockCtrl.materialTypeIdTwo"
                                                ng-options="a.recordId as a.value for a in stockCtrl.materialTypeList" class="form-control"
                                                disable-auto-validate="true">
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">所属仓库：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select multiple theme="bootstrap" ng-model="stockCtrl.storeHouseIdListTwo" on-select="stockCtrl.selectStoreHouseIdTwo()" on-remove="stockCtrl.storeHouseIdListTwo($item)" disable-auto-validate="true" append-to-body = "true">
                                            <ui-select-match placeholder="请选择...">{{$item.name}}</ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in stockCtrl.storehouseList | filter: $select.search">
                                                <div ng-bind-html="item.name | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" >
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">库位：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select  ng-model="stockCtrl.materialPlaceTwo" theme="bootstrap" disable-auto-validate="true">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                            <ui-select-choices repeat="item in stockCtrl.placeList | filter: $select.search">
                                                <div ng-bind-html="item.name | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default btn-default-width pull-right" ng-click="stockCtrl.firstFirstOut()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">先进先出法</div>
                    <div id="twoStep7" class="actions">
                        <div class="portlet-input input-inline input-small">
                            <form action="a/stock/rawmaterial/inout/exportFirstFirstOut" method="POST" enctype="multipart/form-data" target="hidden_frame1">
                                <input type="text" ng-show="false" name="materialNo" value="{{stockCtrl.materialNoFour}}"/>
                                <input type="text" ng-show="false" name="sentTimeStartQr" value="{{stockCtrl.firstInFirstOut.sentTimeStartQr.value}}"/>
                                <input type="text" ng-show="false" name="sentTimeEndQr" value="{{stockCtrl.firstInFirstOut.sentTimeEndQr.value}}"/>
                                <input type="text" ng-show="false" name="materialName" value="{{stockCtrl.materialNameTwo}}"/>
                                <input type="text" ng-show="false" name="materialTypeId" value="{{stockCtrl.materialTypeIdTwo}}"/>
                                <input type="text" ng-show="false" name="storeHouseId" value="{{stockCtrl.storeHouseIdTwo}}"/>
                                <input type="text" ng-show="false" name="stockPlaceId" value="{{stockCtrl.materialPlaceTwo.recordId}}"/>
                                <div>
                                    <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出</button>
                                    <iframe name="hidden_frame1" id="hidden_frame1" style="display:none"></iframe>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div id="oneStep1" class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                            <thead>
                            <tr class="heading">
                                <th style="text-align: center;" rowspan="2">日期</th>
                                <th style="text-align: center;" rowspan="2">物料编号</th>
                                <th style="text-align: center;" rowspan="2">物料名称</th>
                                <th style="text-align: center;" rowspan="2">物料类型</th>
                                <th style="text-align: center;" rowspan="2">物料规格</th>
                                <th style="text-align: center;" colspan="3">入库</th>
                                <th style="text-align: center;" colspan="3">出库</th>
                                <th style="text-align: center;" colspan="3">结余</th>
                            </tr>
                            <tr>
                                <th style="text-align: center;">数量</th>
                                <th style="text-align: center;">单价</th>
                                <th style="text-align: center;">金额</th>
                                <th style="text-align: center;">数量</th>
                                <th style="text-align: center;">单价</th>
                                <th style="text-align: center;">金额</th>
                                <th style="text-align: center;">数量</th>
                                <th style="text-align: center;">单价</th>
                                <th style="text-align: center;">金额</th>
                            </tr>
                            </thead>
                            <tbody ng-repeat="(indexA,row) in stockCtrl.page.firstInFirstOut.data.list">
                            <tr ng-if="row.materialList" ng-repeat="(indexB,mat) in row.materialList">
                                <td ng-if="indexB === 0" rowspan={{row.materialList.length}}>{{row.date}}</td>
                                <td>{{mat.no}}</td>
                                <td>{{mat.name}}</td>
                                <td>{{mat.materialTypeValue}}</td>
                                <td>{{mat.specification}}</td>
                                <td><a ng-click="stockCtrl.modDelivery(mat, 1)" ng-bind="mat.inStocks"></a></td>
                                <td><a ng-click="stockCtrl.modDelivery(mat, 1)" ng-bind="mat.inStockPrice"></a></td>
                                <td><a ng-click="stockCtrl.modDelivery(mat, 1)" ng-bind="mat.inStockCost"></a></td>
                                <td><a ng-click="stockCtrl.modDelivery(mat, 2)" ng-bind="mat.outStocks"></a></td>
                                <td><a ng-click="stockCtrl.modDelivery(mat, 2)" ng-bind="mat.outStockPrice"></a></td>
                                <td><a ng-click="stockCtrl.modDelivery(mat, 2)" ng-bind="mat.outStockCost"></a></td>

                                <td>{{mat.stocks ? mat.stocks : 0}}</td>
                                <td>{{mat.price ? mat.price : 0}}</td>
                                <td>{{mat.surplusAmount ? mat.surplusAmount : 0}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </tab>

    <tab heading="材料自动出库" ng-click="stockCtrl.selectMaterialStore()">
        <div class="panel panel-default" id="step6">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.materialNoFive"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.materialNameThree"
                                           disable-auto-validate="true"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">物料类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="stockCtrl.materialTypeIdThree"
                                            ng-options="a.recordId as a.value for a in stockCtrl.materialTypeList" class="form-control"
                                            disable-auto-validate="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">出库人：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="stockCtrl.userId"
                                            ng-options="a.recordId as a.userName for a in stockCtrl.userList" class="form-control"
                                            disable-auto-validate="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="stockCtrl.selectMaterialStore()"><i
                                    class="fa fa-search"></i> 查&nbsp;询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">材料自动出库列表</div>
                <div class="actions">
                    <div class="portlet-input input-inline">
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.saveMaterialStore(1)" ng-if="stockCtrl.right.edit"><i class="fa fa-plus-square-o font-blue"></i>&nbsp;添加</a>&nbsp;
                    </div>
                    <div class="portlet-input input-inline">
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.saveMaterialStore(2)" ng-if="stockCtrl.right.edit"><i class="fa fa-adjust font-blue"></i>编辑</a>&nbsp;
                    </div>
                    <div class="portlet-input input-inline">
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.deleteMaterialStore()" ng-if="stockCtrl.right.edit"><i class="fa fa-times font-red"></i>删除</a>&nbsp;
                    </div>
                </div>
            </div>
            <div class="portlet-body">
                <div class="table-scrollable" style="margin-top:0px !important">
                    <table id="contentTable" class="table table-striped table-bordered table-condensed table-advance table-hover">
                        <thead>
                        <tr class="heading">
                            <th>物料编号</th>
                            <th>物料名称</th>
                            <th>物料类型</th>
                            <th>自动出库操作人</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in stockCtrl.page.materialStore.data.list" ng-class="row.checked?'success':''" ng-click="stockCtrl.changeCheked(row)">
                            <td ng-bind="row.materialNo"></td>
                            <td ng-bind="row.materialName"></td>
                            <td ng-bind="row.materialTypeValue"></td>
                            <td ng-bind="row.userName"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row page-margin-top">
                    <div class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select class="form-control inline table-pageSize-width"
                                ng-model="stockCtrl.page.materialStore.pageSize"
                                ng-change="stockCtrl.materialStoreNotePaging()"
                                ng-options="pageSizeOption for pageSizeOption in stockCtrl.page.materialStore.pageSizeOptions"
                                disable-auto-validate="true">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{stockCtrl.page.materialStore.data.startCount}} / {{stockCtrl.page.materialStore.data.endCount}} 条，共 {{stockCtrl.page.materialStore.data.count}} 条</span>
                    </div>
                    <div class="col-md-12 col-lg-6">
                        <paging class="pull-right" page="stockCtrl.page.materialStore.data.pageNo"
                                page-size="stockCtrl.page.materialStore.data.pageSize"
                                total="stockCtrl.page.materialStore.data.count" adjacent="1" dots="..." scroll-top="false"
                                hide-if-empty="false"
                                ul-class="pagination" active-class="active" disabled-class="disabled"
                                show-prev-next="true" paging-action="stockCtrl.materialStorePaging(page, pageSize, total)">
                        </paging>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- end 原料报价列表 -->
</tabset>

<div class="modal fade" id="deleteModal" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="deleteModal" aria-hidden="true" style="overflow:auto!important;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger" id="exampleModalLabel">删除后不可恢复！</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i aria-hidden="true" class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body text-danger">
                请慎重考虑，是否删除该常用料规格？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger font-weight-bold" ng-click="stockCtrl.deleteCommon()">删除</button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="staticRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">确认原料出入库记录冲红</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p>确认冲红“<span ng-bind="stockCtrl.msg"></span>”记录吗？</p></div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="stockCtrl.submitRushRed();">确认</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-modal-lg" id="bindDelivery" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">绑定供应商送货信息-采购单号：{{stockCtrl.purchStock.purchasingNo}}-物料编号：{{stockCtrl.purchStock.material.no}}-物料名称：{{stockCtrl.purchStock.material.name}}
                    -入库数：{{stockCtrl.purchStock.quantity}}-操作时间：{{stockCtrl.purchStock.operateDate}}</h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">送货单号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="stockCtrl.purchStock.deliveryNo">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>送货时间：</label>
                                <div class="col-sm-7 col-md-7">
                                    <div class="dropdown">
                                        <a class="dropdown-toggle" id="dropdown2" role="button" data-toggle="dropdown" data-target="#" href="#">
                                            <div class="input-group">
                                                <span type="text" class="form-control" data-ng-bind="stockCtrl.purchStock.deliveryDate | date:'yyyy-MM-dd HH:mm:ss'"></span>
                                                <span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
                                            </div>
                                        </a>
                                        <ul class="dropdown-menu" role="menu" aria-labelledby="dLabel">
                                            <datetimepicker data-ng-model="stockCtrl.purchStock.deliveryDate" data-datetimepicker-config="{ dropdownSelector: '#dropdown2' }"/>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal" ng-click="stockCtrl.bindDeliveryMsg()">绑定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-modal-lg" id="staticAdjust" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">单价调整</h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">原料编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control-static" ng-bind="stockCtrl.materialPriceAdjust.material.no" ></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">原料名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control-static" ng-bind="stockCtrl.materialPriceAdjust.material.name" ></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">调整前单价：</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control-static" ng-bind="stockCtrl.materialPriceAdjust.beforePrice" ></span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>调整后单价：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input class="form-control" type="text" min="0" ng-onlydecimals
                                       ng-model="stockCtrl.materialPriceAdjust.afterPrice">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>调整原因：</label>
                                <div class="col-sm-7 col-md-10">
                                    <textarea class="form-control" placeholder="原因" ng-model="stockCtrl.materialPriceAdjust.remark"
                                          required ng-maxlength="255"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal" ng-disabled="stockCtrl.isAdjustFlag()" ng-click="stockCtrl.doAdjust()">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-modal-lg" id="staticAdjustNum" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">单价调整</h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">原料编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control-static" ng-bind="stockCtrl.adjustStocks.material.no" ></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label">原料名称：</label>
                                <div class="col-sm-7 col-md-10">
                                    <span class="form-control-static" ng-bind="stockCtrl.adjustStocks.material.name" ></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">调整前数量：</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control-static" ng-bind="stockCtrl.adjustStocks.beforeNum" ></span>
                                </div>
                            </div>
                        </div>
                    </div>
					<div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>调整数量：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input class="form-control" type="text" min="0" ng-onlydecimals
                                       ng-model="stockCtrl.adjustStocks.quantity">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label"><span class="required">*</span>调整原因：</label>
                                <div class="col-sm-7 col-md-10">
                                    <textarea class="form-control" placeholder="原因" required ng-maxlength="255"
                                    	ng-model="stockCtrl.adjustStocks.adjustCause"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal" 
                	ng-disabled="stockCtrl.isAdjustNumFlag()" ng-click="stockCtrl.doadjustNum()">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<div class="row">
	<div class="col-md-12">
		<div class="modal fade bs-modal-lg" id="managePrintTem" tabindex="-1" role="dialog" aria-hidden="true">
		    <div class="modal-dialog modal-full">
		        <div class="modal-content">
		            <div class="modal-header">
		            	<div class="row">
		            		<div class="col-md-10">
			            		<h4 class="modal-title">
				                	打印模板管理
				                </h4>
		            		</div>
		            		<div class="col-md-2">
		            			<button type="button" class="btn btn-default btn-default-width"
                                        ng-click="stockCtrl.manageTemp()" style="margin-left:15px;">
                                   	添加模板
                                </button>
		            		</div>
		            	</div>
		            </div>
		            <div class="modal-body">
		            	<div class="table-scrollable" style="margin-top:0px !important">
                    		<table class="table table-striped table-bordered table-condensed table-advance table-hover">
                    			<thead>
                    				<th>模板名称</th>
	                                <!-- <th>分配客户</th> -->
	                                <th>操作</th>
                    			</thead>
                    			<tbody>
	                    			<tr>
	                    				<td>
		                    				<input type="text" class="form-control" ng-model="stockCtrl.templateNameStr" ng-blur="stockCtrl.loadPrintTemList()">
		                    			</td>
<!-- 		                            	<td>
		                            		<input type="text" class="form-control" ng-model="stockCtrl.customerNamesStr" ng-blur="stockCtrl.loadPrintTemList()">
		                            	</td> -->
		                            	<td></td>
	                    			</tr>
	                    			<tr ng-repeat="item in stockCtrl.showPrintTemList">
		                            	<td>{{item.templateName}}</td>
		                            	<!-- <td>{{item.customerNames}}</td> -->
		                            	<td>
		                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.managePrintTemp(item)">编辑</a>
		                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTemp(item)">删除</a>
		                            	</td>
		                            </tr>
                    			</tbody>
                    		</table>
                    	</div>
		            </div>
		    	</div>
			</div>
		</div>
	</div>
</div>
<div class="row">
	<div class="col-md-12">
		<div class="modal fade bs-modal-lg" id="custTemplate" tabindex="-1" role="dialog" aria-hidden="true">
		    <div class="modal-dialog modal-full">
		        <div class="modal-content">
		            <div class="modal-header">
		            	<div class="row">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true" style="padding-right: 2rem;"></button>
		            		<div class="col-md-9">
			            		<h4 class="modal-title">
			                     	<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.templateName">{{stockCtrl.custTemplate.templateName}}</span>模板管理（自定义名字，换行例子Page NOrow订单号）
				                </h4>
		            		</div>
<!-- 		            		<div class="col-md-2 text-right" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.recordId">
		            			<button type="button" class="btn btn-primary" ng-click="stockCtrl.selectPrintPdf()">
				                	打印预览
				                </button>
		            			<button type="button" class="btn btn-primary" ng-click="stockCtrl.setPrintTemplate()">
				                	另存为模板
				                </button>
		            		</div> -->
		            	</div>
		            </div>
		            <div class="modal-body">
		            	<div class="row">
		            		<div class="col-md-12">
	            				<div class="panel panel-default">
					           		<div class="panel-body">
				                		<form class="form-horizontal">
				                			<div class="row">
				                				<div class="col-md-6 col-lg-4">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">模板名字：</label>
						                                <div class="col-sm-7 col-md-8">
						                                    <input type="text" class="form-control" ng-model="stockCtrl.custTemplate.templateName">
						                                </div>
						                            </div>
						                        </div>
				                			</div>
				                			<div class="row">
				                				<div class="col-md-6 col-lg-4">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">头部打印配置：</label>
						                                <div class="col-sm-7 col-md-8">
						                                    <ui-select theme="bootstrap" ng-model="stockCtrl.headPrintTag" on-select="stockCtrl.addPrintTag()">
						                                        <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
						                                        <ui-select-choices repeat="item as item in stockCtrl.headPrintShowList | filter: $select.search">
						                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
						                                        </ui-select-choices>
						                                    </ui-select>
						                                </div>
						                            </div>
						                        </div>
				                			</div>
				                			<div class="row">
				                				<div class="col-md-6 col-lg-2" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.headLogoFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-8 control-label">公司LOGO：</label>
						                                <div class="col-sm-7 col-md-4" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('headLogoFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-2" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.headNameFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-8 control-label">公司全称：</label>
						                                <div class="col-sm-7 col-md-4" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('headNameFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-2" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.headShortNameFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-8 control-label">公司简称：</label>
						                                <div class="col-sm-7 col-md-4" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('headShortNameFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-2" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.headNameEnFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-8 control-label">公司英文名称：</label>
						                                <div class="col-sm-7 col-md-4" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('headNameEnFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-2" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.headAddressFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-8 control-label">公司地址：</label>
						                                <div class="col-sm-7 col-md-4" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('headAddressFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-2" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.headPhoneFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-8 control-label">公司电话：</label>
						                                <div class="col-sm-7 col-md-4" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('headPhoneFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-2" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.headFaxFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-8 control-label">公司传真：</label>
						                                <div class="col-sm-7 col-md-4" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('headFaxFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-2" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.headWebEmailFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-8 control-label">公司网址、邮箱：</label>
						                                <div class="col-sm-7 col-md-4" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('headWebEmailFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
				                			</div>
				                		</form>
					                </div>
		            			</div>
		            		</div>
		            	</div>
		            	<div class="row">
		            		<div class="col-md-12">
	            				<div class="panel panel-default">
					           		<div class="panel-body">
				                		<form class="form-horizontal">
				                			<div class="row">
				                				<div class="col-md-6 col-lg-4">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">用户打印配置：</label>
						                                <div class="col-sm-7 col-md-8">
						                                    <ui-select theme="bootstrap" ng-model="stockCtrl.custPrintTag" on-select="stockCtrl.addPrintTag()">
						                                        <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
						                                        <ui-select-choices repeat="item as item in stockCtrl.custPrintShowList | filter: $select.search">
						                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
						                                        </ui-select-choices>
						                                    </ui-select>
						                                </div>
						                            </div>
						                        </div>
				                			</div>
				                			<div class="row">
				                				<!-- 采购入库 -->
				                				<div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.custNameFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">货单位：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" ng-model="stockCtrl.custTemplate.custName" placeholder="自定义名称">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('custNameFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.dateFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">日期：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.dateName">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('dateFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <!-- 投料出库 -->
						                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.custNameFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">领料单部门：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" ng-model="stockCtrl.custTemplate.custName" placeholder="自定义名称">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('custNameFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.noFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">领料单号：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.no">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('noFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.dateFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">日期：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.dateName">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('dateFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <!-- 补料出库 -->
						                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.custNameFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">领料单部门：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" ng-model="stockCtrl.custTemplate.custName" placeholder="自定义名称">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('custNameFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.noFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">领料单号：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.no">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('noFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.dateFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">日期：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.dateName">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('dateFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>						                        
				                			</div>
				                		</form>
					                </div>
		            			</div>
		            		</div>
		            	</div>
		            	<div class="row">
		            		<div class="col-md-12">
	            				<div class="panel panel-default">
					           		<div class="panel-body">
				                		<form class="form-horizontal">
				                			<div class="row">
				                				<div class="col-md-6 col-lg-4">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">表体打印配置：</label>
						                                <div class="col-sm-7 col-md-8">
						                                    <ui-select theme="bootstrap" ng-model="stockCtrl.bodyPrintTag" on-select="stockCtrl.addPrintTag()">
						                                        <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
						                                        <ui-select-choices repeat="item as item in stockCtrl.bodyPrintShowList | filter: $select.search">
						                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
						                                        </ui-select-choices>
						                                    </ui-select>
						                                </div>
						                            </div>
						                        </div>
				                			</div>
				                			<div class="row">
				                				<div class="table-scrollable" style="margin-top:0px !important">
						                    		<table class="table table-striped table-bordered table-condensed table-advance table-hover">
						                    			<thead>
						                    				<th ng-repeat="row in stockCtrl.bodyPrintList">
						                    					<!-- 采购入库 -->
						                    					<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.deailNoFlag && row.name=='deailNoFlag'">物料编号</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.deailCustNoFlag && row.name=='deailCustNoFlag'">名称</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.craftNoFlag && row.name=='craftNoFlag'">规格/型号</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.deailMaterNoFlag && row.name=='deailMaterNoFlag'">单位</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.custModalFlag && row.name=='custModalFlag'">数量</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.qualitityFlag && row.name=='qualitityFlag'">单价</span>
                                                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.rawQualitityNameFlag && row.name=='rawQualitityNameFlag'">原单价</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.doonateFlag && row.name=='doonateFlag'">金额</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.remarkFlag && row.name=='remarkFlag'">备注</span>
                                                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.deailProductNameFlag && row.name=='deailProductNameFlag'">送货单号</span>
						                    					<!-- 投料出库 -->
						                    					<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.deailNoFlag && row.name=='deailNoFlag'">投料单编号</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.deailCustNoFlag && row.name=='deailCustNoFlag'">生产编号</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.craftNoFlag && row.name=='craftNoFlag'">板厚</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.deailMaterNoFlag && row.name=='deailMaterNoFlag'">板料规格/型号</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.custModalFlag && row.name=='custModalFlag'">数量/单位</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.qualitityFlag && row.name=='qualitityFlag'">导热</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.doonateFlag && row.name=='doonateFlag'">供应商</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.remarkFlag && row.name=='remarkFlag'">物料编号</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.deliverySizeFlag && row.name=='deliverySizeFlag'">投料pcs</span>
                                                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.deailProductNameFlag && row.name=='deailProductNameFlag'">送货单号</span>
								                                <!-- 补料出库 -->
						                    					<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.deailNoFlag && row.name=='deailNoFlag'">补料单编号</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.deailCustNoFlag && row.name=='deailCustNoFlag'">生产编号</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.craftNoFlag && row.name=='craftNoFlag'">板厚</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.deailMaterNoFlag && row.name=='deailMaterNoFlag'">板料规格/型号</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.custModalFlag && row.name=='custModalFlag'">数量/单位</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.qualitityFlag && row.name=='qualitityFlag'">导热</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.doonateFlag && row.name=='doonateFlag'">供应商</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.remarkFlag && row.name=='remarkFlag'">物料编号</span>
								                                <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.deliverySizeFlag && row.name=='deliverySizeFlag'">补料pcs</span>
						                    				</th>
						                    			</thead>
						                    			<tbody>
							                    			<tr>
							                    				<td ng-repeat="row in stockCtrl.bodyPrintList">
						                    						<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailNoFlag && row.name=='deailNoFlag'">
									                            		<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.deailNo">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailCustNoFlag && row.name=='deailCustNoFlag'">
									                            		<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.deailCustNo">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.craftNoFlag && row.name=='craftNoFlag'">
									                            		<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.craftNo">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailMaterNoFlag && row.name=='deailMaterNoFlag'">
									                            		<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.deailMaterNo">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.custModalFlag && row.name=='custModalFlag'">
									                            		<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.custModal">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.qualitityFlag && row.name=='qualitityFlag'">
									                            		<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.qualitity">
									                            	</span>
                                                                    <span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.rawQualitityNameFlag && row.name=='rawQualitityNameFlag'">
									                            		<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.rawQualitityName">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.doonateFlag && row.name=='doonateFlag'">
									                            		<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.doonate">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.remarkFlag && row.name=='remarkFlag'">
									                            		<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.remarkName">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deliverySizeFlag && row.name=='deliverySizeFlag'">
									                            		<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.deliverySize">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailDateFlag && row.name=='deailDateFlag'">
									                            		<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.deailDate">
									                            	</span>
                                                                    <span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailProductNameFlag && row.name=='deailProductNameFlag'">
									                            		<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.deailProductName">
									                            	</span>
							                    				</td>
								                            </tr>
								                            <tr>
								                            	<td ng-repeat="row in stockCtrl.bodyPrintList">
								                            		<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailNoFlag && row.name=='deailNoFlag'">
									                            		<input type="text" class="form-control" placeholder="排序" ng-model="stockCtrl.custTemplate.deailNoSortNum" ng-blur="stockCtrl.resetBodySortNumList()">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailCustNoFlag && row.name=='deailCustNoFlag'">
									                            		<input type="text" class="form-control" placeholder="排序" ng-model="stockCtrl.custTemplate.deailCustSortNum" ng-blur="stockCtrl.resetBodySortNumList()">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.craftNoFlag && row.name=='craftNoFlag'">
									                            		<input type="text" class="form-control" placeholder="排序" ng-model="stockCtrl.custTemplate.craftNoSortNum" ng-blur="stockCtrl.resetBodySortNumList()">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailMaterNoFlag && row.name=='deailMaterNoFlag'">
									                            		<input type="text" class="form-control" placeholder="排序" ng-model="stockCtrl.custTemplate.deailMaterNoSortNum" ng-blur="stockCtrl.resetBodySortNumList()">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.custModalFlag && row.name=='custModalFlag'">
									                            		<input type="text" class="form-control" placeholder="排序" ng-model="stockCtrl.custTemplate.custModalSortNum" ng-blur="stockCtrl.resetBodySortNumList()">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.qualitityFlag && row.name=='qualitityFlag'">
									                            		<input type="text" class="form-control" placeholder="排序" ng-model="stockCtrl.custTemplate.qualititySortNum" ng-blur="stockCtrl.resetBodySortNumList()">
									                            	</span>
                                                                    <span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.rawQualitityNameFlag && row.name=='rawQualitityNameFlag'">
									                            		<input type="text" class="form-control" placeholder="排序" ng-model="stockCtrl.custTemplate.rawQualitityNameSortNum" ng-blur="stockCtrl.resetBodySortNumList()">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.doonateFlag && row.name=='doonateFlag'">
									                            		<input type="text" class="form-control" placeholder="排序" ng-model="stockCtrl.custTemplate.doonateSortNum" ng-blur="stockCtrl.resetBodySortNumList()">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.remarkFlag && row.name=='remarkFlag'">
									                            		<input type="text" class="form-control" placeholder="排序" ng-model="stockCtrl.custTemplate.remarkSortNum" ng-blur="stockCtrl.resetBodySortNumList()">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deliverySizeFlag && row.name=='deliverySizeFlag'">
									                            		<input type="text" class="form-control" placeholder="排序" ng-model="stockCtrl.custTemplate.deliverySizeSortNum" ng-blur="stockCtrl.resetBodySortNumList()">
									                            	</span>
									                            	<span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailDateFlag && row.name=='deailDateFlag'">
									                            		<input type="text" class="form-control" placeholder="排序" ng-model="stockCtrl.custTemplate.deailDateSortNum" ng-blur="stockCtrl.resetBodySortNumList()">
									                            	</span>
                                                                    <span class="form-group" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailProductNameFlag && row.name=='deailProductNameFlag'">
									                            		<input type="text" class="form-control" placeholder="排序" ng-model="stockCtrl.custTemplate.deailProductNameSortNum" ng-blur="stockCtrl.resetBodySortNumList()">
									                            	</span>
								                            	</td>
								                            </tr>
								                            <tr>
								                            	<td ng-repeat="row in stockCtrl.bodyPrintList">
								                            		<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailNoFlag && row.name=='deailNoFlag'">
									                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('deailNoFlag')">删除</a>
									                            	</span>
									                            	<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailCustNoFlag && row.name=='deailCustNoFlag'">
									                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('deailCustNoFlag')">删除</a>
									                            	</span>
									                            	<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.craftNoFlag && row.name=='craftNoFlag'">
									                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('craftNoFlag')">删除</a>
									                            	</span>
									                            	<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailMaterNoFlag && row.name=='deailMaterNoFlag'">
									                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('deailMaterNoFlag')">删除</a>
									                            	</span>
									                            	<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.custModalFlag && row.name=='custModalFlag'">
									                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('custModalFlag')">删除</a>
									                            	</span>
									                            	<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.qualitityFlag && row.name=='qualitityFlag'">
									                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('qualitityFlag')">删除</a>
									                            	</span>
                                                                    <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.rawQualitityNameFlag && row.name=='rawQualitityNameFlag'">
									                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('rawQualitityNameFlag')">删除</a>
									                            	</span>
									                            	<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.doonateFlag && row.name=='doonateFlag'">
									                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('doonateFlag')">删除</a>
									                            	</span>
									                            	<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.remarkFlag && row.name=='remarkFlag'">
									                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('remarkFlag')">删除</a>
									                            	</span>
									                            	<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deliverySizeFlag && row.name=='deliverySizeFlag'">
									                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('deliverySizeFlag')">删除</a>
									                            	</span>
									                            	<span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailDateFlag && row.name=='deailDateFlag'">
									                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('deailDateFlag')">删除</a>
									                            	</span>
                                                                    <span ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.deailProductNameFlag && row.name=='deailProductNameFlag'">
									                            		<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('deailProductNameFlag')">删除</a>
									                            	</span>
								                            	</td>
								                            </tr>
						                    			</tbody>
						                    		</table>
						                    	</div>
				                			</div>
				                		</form>
					                </div>
		            			</div>
		            		</div>
		            	</div>
		            	<div class="row">
		            		<div class="col-md-12">
	            				<div class="panel panel-default" id="step6">
					           		<div class="panel-body">
				                		<form class="form-horizontal">
				                			<div class="row">
				                				<div class="col-md-6 col-lg-4">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">底部打印配置：</label>
						                                <div class="col-sm-7 col-md-8">
						                                    <ui-select theme="bootstrap" ng-model="stockCtrl.bottomPrintTag" on-select="stockCtrl.addPrintTag()">
						                                        <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
						                                        <ui-select-choices repeat="item as item in stockCtrl.bottomPrintShowList | filter: $select.search">
						                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
						                                        </ui-select-choices>
						                                    </ui-select>
						                                </div>
						                            </div>
						                        </div>
				                			</div>
				                			<div class="row">
				                				<!-- 采购入库 -->
				                				<div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.bottomOneFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">保管：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.bottomOneName">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('bottomOneFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
				                				<div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.bottomOneFlag">
				                					<div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">经手人：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.bottomTwoName">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('bottomOneFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-12" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 6 && stockCtrl.custTemplate.bottomTwoFlag">
				                					<div class="form-group">
						                                <label class="col-sm-3 col-md-2 control-label">财务：</label>
						                                <div class="col-sm-7 col-md-8">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.bottomThreeName">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('bottomTwoFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <!-- 投料出库 -->
						                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.bottomOneFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">领料人：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.bottomOneName">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('bottomOneFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
				                				<div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.bottomOneFlag">
				                					<div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">主管审核：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.bottomTwoName">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('bottomOneFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-12" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 7 && stockCtrl.custTemplate.bottomTwoFlag">
				                					<div class="form-group">
						                                <label class="col-sm-3 col-md-2 control-label">仓库：</label>
						                                <div class="col-sm-7 col-md-8">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.bottomThreeName">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('bottomTwoFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <!-- 补料出库 -->
						                        <div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.bottomOneFlag">
						                            <div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">领料人：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.bottomOneName">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('bottomOneFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
				                				<div class="col-md-6 col-lg-4" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.bottomOneFlag">
				                					<div class="form-group">
						                                <label class="col-sm-3 col-md-4 control-label">主管审核：</label>
						                                <div class="col-sm-7 col-md-6">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.bottomTwoName">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('bottomOneFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
						                        <div class="col-md-6 col-lg-12" ng-if="stockCtrl.custTemplate && stockCtrl.custTemplate.tempalteFlag == 8 && stockCtrl.custTemplate.bottomTwoFlag">
				                					<div class="form-group">
						                                <label class="col-sm-3 col-md-2 control-label">仓库：</label>
						                                <div class="col-sm-7 col-md-8">
						                                	<input type="text" class="form-control" placeholder="自定义名称" ng-model="stockCtrl.custTemplate.bottomThreeName">
						                                </div>
						                                <div class="col-sm-7 col-md-2" style="padding-top: 0.7rem;">
						                                	<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.delPrintTag('bottomTwoFlag')">删除</a>
						                                </div>
						                            </div>
						                        </div>
				                			</div>
				                		</form>
					                </div>
		            			</div>
		            		</div>
		            	</div>
		            </div>
		            <div class="modal-footer">
						<button type="button" class="btn blue" ng-click="stockCtrl.savePrintTemplate()">保存修改</button>
					</div>
		        </div>
		    </div>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-12">
		<div id="transferLibraryStatic" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog modal-full">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">移库操作</h4>
					</div>
					
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="form-horizontal">
							<div class="row">
								<div class="col-md-6 col-lg-6">
		                            <div class="form-group">
		                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>物料：</label>
		                                <div class="col-sm-7 col-md-8">
		                                    <ui-select on-select="stockCtrl.getMatPlaceComData(null)" ng-model="stockCtrl.transferLibrary.material" theme="bootstrap" disable-auto-validate="true" required>
		                                        <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
		                                        <ui-select-choices refresh="stockCtrl.addMaterialItems($select.search)" refresh-delay="500" repeat="item in stockCtrl.materialQueryList | filter: $select.search">
		                                                <small>
		                                               		<span><span style="color: blue;">编号:</span>{{item.no}}<br></span>
		                                               		<span><span style="color: blue;">名称:</span>{{item.name}}<br></span>
		                                               		<span><span style="color: blue;">规格:</span>{{item.specification}}<br></span>
		                                               </small>
		                                        </ui-select-choices>
		                                    </ui-select>
		                                </div>
		                            </div>
		                        </div>
	                            <div class="col-md-6 col-lg-6">
		                            <div class="form-group">
		                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>移出库位：</label>
		                                <div class="col-sm-7 col-md-8">
		                                    <ui-select ng-model="stockCtrl.transferLibrary.matPlaceComOut" theme="bootstrap" disable-auto-validate="true" required>
		                                        <ui-select-match placeholder="请选择...">{{$select.selected.placeName}}-{{$select.selected.comName}}</ui-select-match>
		                                        <ui-select-choices repeat="item in stockCtrl.matPlaceComList | filter: $select.search">
		                                                <small>
		                                               		<span><span style="color: blue;">名称:</span>{{item.placeName}}<br></span>
		                                               		<span><span style="color: blue;">库存:</span>{{item.periodStocks}}<br></span>
		                                               		<span><span style="color: blue;">销售公司:</span>{{item.comName}}<br></span>
		                                               </small>
		                                        </ui-select-choices>
		                                    </ui-select>
		                                </div>
		                            </div>
		                        </div>
		                        <div class="col-md-6 col-lg-6">
		                            <div class="form-group">
		                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>移入库位：</label>
		                                <div class="col-sm-7 col-md-8">
		                                    <ui-select ng-model="stockCtrl.transferLibrary.matPlaceComIn" theme="bootstrap" disable-auto-validate="true" required>
		                                        <ui-select-match placeholder="请选择...">{{$select.selected.placeName}}-{{$select.selected.comName}}</ui-select-match>
		                                        <ui-select-choices repeat="item in stockCtrl.matPlaceComList | filter: $select.search">
		                                                <small>
		                                               		<span><span style="color: blue;">名称:</span>{{item.placeName}}<br></span>
		                                               		<span><span style="color: blue;">库存:</span>{{item.periodStocks}}<br></span>
		                                               		<span><span style="color: blue;">销售公司:</span>{{item.comName}}<br></span>
		                                               </small>
		                                        </ui-select-choices>
		                                    </ui-select>
		                                </div>
		                            </div>
		                        </div>
		                        <div class="col-md-6 col-lg-6">
	                                <div class="form-group">
	                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>移库数量：</label>
	                                    <div class="col-sm-7 col-md-8">
	                                        <input class="form-control" type="text" ng-model="stockCtrl.transferLibrary.stocks" ng-onlydecimals required/>
	                                    </div>
	                                </div>
	                            </div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn blue" ng-click="stockCtrl.transferLibrarySave()">提交</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="staticCommitAuditOpen" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">原料报价审批</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;">
                        <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                        <div class="col-sm-11" ><p ng-bind="stockCtrl.message"></p></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="stockCtrl.commitAudit()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="approvalStatic" class="modal fade bs-example-modal-lg in" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title" >
                    <span>待提交审批清单</span>
                </h4>
            </div>
            <div class="modal-body">
                <table class="table table-bordered table-hover">
                    <tr >
                        <th width="3%">物料编号</th>
                        <th width="3%">物料名称</th>
                        <th width="3%">规格尺寸</th>
                        <th width="3%">原周期</th>
                        <th width="3%">新周期</th>
                        <th width="3%">原价</th>
                        <th width="3%">新价</th>
                    </tr>
                    <tr  ng-repeat ="item in stockCtrl.recordList">
                        <td>{{item.no}}</td>
                        <td>{{item.name}}</td>
                        <td>{{item.specification}}</td>
                        <td>{{item.leadTime}}</td>
                        <td>{{item.erpRawmaterialLeadTime}}</td>

                        <td>{{item.rawmaterialPrice}}</td>
                        <td>{{item.erpRawmaterialPrice}}</td>
                    </tr>
                </table>
            </div>
            <div class="modal-body">
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption font-green-sharp">
                            <i class="icon-cloud-upload font-green-sharp"></i>
                            <span class="caption-subject bold uppercase">上传列表</span>
                        </div>
                        <div class="actions">
                            <div class="portlet-input input-inline input-small">
                                <button class="btn green" ng-if="stockCtrl.right.edit"
                                        ngf-select="stockCtrl.uploadFiles($files, $invalidFiles)" multiple
                                        ngf-max-size="500MB" ng-disabled="sourceCtrl.edit.upload" >
                                    <span class="glyphicon glyphicon-cloud-upload"></span>上传附件
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <div class="table-scrollable table-scrollable-borderless">
                            <table class="table table-hover table-light">
                                <thead>
                                <tr class="uppercase">
                                    <th width="50%">文件名</th>
                                    <th>大小</th>
                                    <th>操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="item in stockCtrl.fileList">
                                    <td><strong>{{ item.name }}</strong></td>
                                    <td>{{ item.size/1024|number:3 }} KB</td>
                                    <td>
                                        <button class="btn btn-sm btn-danger px-1 py-1" ng-click="stockCtrl.delFile($index)">删除</button>
                                        <button class="btn btn-sm btn-success px-1 py-1" ng-click="stockCtrl.downloadFile(item,1)">预览</button>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button"  class="btn btn-primary btn-default-width" data-dismiss="modal" ng-disabled="sourceCtrl.edit.upload" ng-click="stockCtrl.submitApprovalOne()"  style="margin-left:45px;">确&nbsp;定</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bd-example-modal-xl" data-backdrop="static" data-keyboard="false" tabindex="-1" id="previewId" style="overflow:scroll;">
    <div class="modal-dialog modal-xl">
        <div class="modal-body">
            <div class="row">
                <div class="col-sm text-center">
                    <div class="row">
                        <div class="col-sm">
                            <img id="preview" src="" class="w-100">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-default">关闭</button>
            </div>
        </div>
    </div>
</div>

<%--<div id="previewId" class="modal fade bs-example-modal-xl in" tabindex="-1" data-backdrop="static" data-keyboard="false" aria-hidden="true" >
    <div class="modal-dialog modal-xl">
        <div class="modal-body">
            <div class="row">
                <div class="col-sm text-center">
                    <div class="row">
                        <div class="col-sm">
                            <img id="preview" src="" class="w-100">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>--%>
<div class="row">
    <div class="col-md-12">
        <div class="modal fade bs-modal-lg" id="fistInFirstRecord" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">
                            <span>先进先出明细数据</span>
                        </h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="portlet light bordered">
                                <div class="form-group">
                                    <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                                        <thead>
                                        <tr class="heading">
                                            <th style="text-align: center;">采购编号</th>
                                            <th style="text-align: center;">物料名称</th>
                                            <th style="text-align: center;">供应商简称</th>
                                            <th style="text-align: center;">规格型号</th>
                                            <th style="text-align: center;">出库数量</th>
                                            <th style="text-align: center;">价格</th>
                                            <th style="text-align: center;">金额</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr></tr>
                                        </tbody>
                                        <tbody ng-repeat="(indexA, item) in stockCtrl.fisrtInFirstOutList">
                                        <tr>
                                            <td style="text-align: center;">{{item.purchasingNo}}</td>
                                            <td style="text-align: center;">{{item.materialName}}</td>
                                            <td style="text-align: center;">{{item.shortName}}</td>
                                            <td style="text-align: center;">{{item.specification}}</td>
                                            <td style="text-align: center;">{{item.outboundQuantity}}</td>
                                            <td style="text-align: center;">{{item.price}}</td>
                                            <td style="text-align: center;">{{item.firstInFirstAmount}}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="modal fade bs-modal-lg" id="fistInFirstRecordTwo" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">
                            <span>入库明细数据</span>
                        </h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="portlet light bordered">
                                <div class="form-group">
                                    <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                                        <thead>
                                        <tr class="heading">
                                            <th style="text-align: center;">出入库时间</th>
                                            <th style="text-align: center;">出入库方式</th>
                                            <th style="text-align: center;">物料编号</th>
                                            <th style="text-align: center;">物料名称</th>
                                            <th style="text-align: center;">规格型号</th>
                                            <th style="text-align: center;">出入库数量</th>
                                            <th style="text-align: center;">出入库单价</th>
                                            <th style="text-align: center;">出入库金额</th>
                                        </tr>
                                        <tr>
                                            <th style="text-align: center;" colspan="4">先进先出单价</th>
                                            <th style="text-align: center;" colspan="4">先进先出金额</th>
                                        </tr>
                                        </thead>
                                        <tbody ng-repeat="item in stockCtrl.firstOutList track by $index">
                                        <tr>
                                            <td style="text-align: center;">{{item.operateDateStrTwo}}</td>
                                            <td style="text-align: center;">{{item.inoutTypeStr}}</td>
                                            <td style="text-align: center;">{{item.material.no}}</td>
                                            <td style="text-align: center;">{{item.material.name}}</td>
                                            <td style="text-align: center;">{{item.material.specification}}</td>
                                            <td style="text-align: center;">{{item.quantity}}</td>
                                            <td style="text-align: center;">{{item.price}}</td>
                                            <td style="text-align: center;">{{item.cost}}</td>
                                        </tr>
                                        <tr ng-if="item.firstOutList" ng-repeat="(indexD,detail) in item.firstOutList">
                                            <td style="text-align: center;" colspan="4">{{detail.price}}</td>
                                            <td style="text-align: center;" colspan="4">{{detail.firstInFirstAmount}}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="modal fade" id="saveMaterialStoreList" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-full">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"></button>
                        <h4 class="modal-title"><span class="text-primary">
						<span>自动出库保存</span>
					</span></h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 5px;">
                        <form class="form-horizontal">
                            <div class="portlet light bordered">
                                <div class="actions">
                                    <div class="col-md-12 text-right">
                                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="stockCtrl.openFlag == 1" ng-click="stockCtrl.addShiftDetailRecord()"><i class="fa fa-adjust font-blue"></i>增加行</a>&nbsp;
                                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="stockCtrl.openFlag == 1" ng-click="stockCtrl.delShiftDetailRecord()"><i class="fa fa-times font-red"></i>删除行</a>&nbsp;
                                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="stockCtrl.saveShiftDetailRecord()"><i class="fa fa-save font-green"></i>保存</a>
                                    </div>
                                </div>
                                <div class="portlet-body">
                                    <div class="row form-group">
                                        <div class="table">
                                            <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                                                <thead>
                                                <tr class="heading">
                                                    <th style="width: 10%">物料编号</th>
                                                    <th style="width: 10%">物料类型</th>
                                                    <th style="width: 10%">自动出库操作人</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr ng-repeat="row in stockCtrl.materialStoreList" ng-class="row.checked?'success':''" ng-click="ctrl.changeCheked(row)">
                                                    <td style="width: 10%">
                                                        <ui-select on-select="stockCtrl.onMaterialSelected($item, row)" ng-model="row.materialId" theme="bootstrap" disable-auto-validate="true">
                                                            <ui-select-match placeholder="请选择...">{{$select.selected.no}}-{{$select.selected.name}}</ui-select-match>
                                                            <ui-select-choices refresh="stockCtrl.addMaterialItemsTwo($select.search)" refresh-delay="500" repeat="item.recordId as item in stockCtrl.materialQueryListTwo | filter: $select.search">
                                                                <small>
                                                                    <span><span style="color: blue;">编号:</span>{{item.no}}<br></span>
                                                                    <span><span style="color: blue;">名称:</span>{{item.name}}<br></span>
                                                                    <span><span style="color: blue;">规格:</span>{{item.specification}}<br></span>
                                                                </small>
                                                            </ui-select-choices>
                                                        </ui-select>
                                                    </td>
                                                    <td style="width: 10%">
                                                        <ui-select on-select="stockCtrl.onMaterialTypSelected($item, row)" ng-model="row.materialTypeId" theme="bootstrap" disable-auto-validate="true">
                                                            <ui-select-match placeholder="请选择...">{{$select.selected.value}}</ui-select-match>
                                                            <ui-select-choices repeat="item.recordId as item in stockCtrl.materialTypeListTwo | filter: $select.search">
                                                                <div ng-bind-html="item.value | highlight: $select.search"></div>
                                                            </ui-select-choices>
                                                        </ui-select>
                                                    </td>
                                                    <td style="width: 10%">
                                                        <ui-select  ng-model="row.userId" theme="bootstrap" disable-auto-validate="true">
                                                            <ui-select-match placeholder="请选择...">{{$select.selected.userName}}</ui-select-match>
                                                            <ui-select-choices repeat="item.recordId as item in stockCtrl.userList | filter: $select.search">
                                                                <div ng-bind-html="item.userName | highlight: $select.search"></div>
                                                            </ui-select-choices>
                                                        </ui-select>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="delMaterialStore" class="modal fade" tabindex="-1"
             data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">
                            <span>删除自动出库设置</span>
                        </h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="col-sm-1 text-center">
                            <i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i>
                        </div>
                        <div class="col-sm-11">{{stockCtrl.message}}</div>
                    </div>
                    <div class="modal-footer">
                        <button  type="button" data-dismiss="modal" class="btn blue" ng-click="stockCtrl.deleteCustomerSale()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"
                        aria-hidden="true"></button>
                <h4 class="modal-title"><span>提示</span></h4>
            </div>
            <div class="modal-body">
                <p>
                    <span ng-bind="stockCtrl.message"></span>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
            </div>
        </div>
    </div>
</div>