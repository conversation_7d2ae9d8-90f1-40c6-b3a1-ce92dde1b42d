package com.kyb.pcberp.modules.production.entity;

import java.math.BigDecimal;

import com.kyb.pcberp.common.persistence.DataEntity;

public class PopoCost extends DataEntity<PopoCost>
{
    
    private static final long serialVersionUID = 1L;
    
    private Popo popo;
    
    private Integer type;
    
    private String name;
    
    private BigDecimal value;
    
    private Integer addSub;
    
    private BigDecimal quantity;
    
    private BigDecimal sum;
    
    public BigDecimal getSum()
    {
        return sum;
    }
    
    public void setSum(BigDecimal sum)
    {
        this.sum = sum;
    }
    
    public Popo getPopo()
    {
        return popo;
    }
    
    public void setPopo(Popo popo)
    {
        this.popo = popo;
    }
    
    public Integer getType()
    {
        return type;
    }
    
    public void setType(Integer type)
    {
        this.type = type;
    }
    
    public String getName()
    {
        return name;
    }
    
    public void setName(String name)
    {
        this.name = name;
    }
    
    public BigDecimal getValue()
    {
        return value;
    }
    
    public void setValue(BigDecimal value)
    {
        this.value = value;
    }
    
    public Integer getAddSub()
    {
        return addSub;
    }
    
    public void setAddSub(Integer addSub)
    {
        this.addSub = addSub;
    }
    
    public BigDecimal getQuantity()
    {
        return quantity;
    }
    
    public void setQuantity(BigDecimal quantity)
    {
        this.quantity = quantity;
    }
    
}
