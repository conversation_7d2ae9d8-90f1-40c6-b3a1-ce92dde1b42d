/**
 * 
 */
package com.kyb.pcberp.modules.finance.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.finance.entity.CollectionTotal;

/**
 * 收款统计DAO接口
 * <AUTHOR>
 * @version 2015-11-02
 */
@MyBatisDao
public interface CollectionTotalDao extends CrudDao<CollectionTotal> {
	
	public CollectionTotal selectOfcustomerId(Customer customer);
	
	/**
	 * 根据客户编号去查询 客户总金额
	 * 
	 */
	public Integer selectCustomerSum(Customer customer);
	
	/**
	 * 查询公司收款数据
	 * @param total
	 * @return
	 */
	public CollectionTotal selectCollectionSum(CollectionTotal total);
}