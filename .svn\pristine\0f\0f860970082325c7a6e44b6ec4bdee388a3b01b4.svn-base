package com.kyb.pcberp.modules.wechat.pojo.wechatUser;

import com.kyb.pcberp.common.persistence.DataEntity;

public class InterConfigOaDetail  extends DataEntity<InterConfigOaDetail>
{
    private static final long serialVersionUID = 1L;

    private String configId;

    private String auditId;

    private String detailId;

    private String value;

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public String getAuditId() {
        return auditId;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
