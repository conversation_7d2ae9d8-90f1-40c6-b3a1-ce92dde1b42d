kybApp.controller('profileCtrl', ['$rootScope', '$scope', 'upida', '$timeout', '$filter', function ($rootScope, $scope, upida, $timeout, $filter) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();

        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });

    $scope.smuser = {};
    $scope.birthday = "";
    $scope.edit = true;
    
    // 用户
    function loadRight() {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
        upida.post("sys/user/uesr").then(function (data) {
            //格式化时间

            $scope.birthday = $filter('date')(data.birthday, "yyyy-MM-dd");
            $scope.smuser = data;
            if (data.roleList != null &&data.roleList.length>0) 
            {
            	for (var i = 0; i < data.roleList.length; i++) 
            	{
            		$scope.smuser.roleNames = data.roleList[0].name;
            	}
           }

            MainCtrl.unblockUI();
        });
    }

    //修改
    $scope.mod = function (form) {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	form.$setDirty();
    	upida.post("sys/user/isExist", $scope.smuser).then(function (d) {
    		if(d == "1")
    		{
    			// 提示信息
                $scope.message = "此登录帐号已存在，请使用其他登录帐号！";
                $('#static').modal();
                MainCtrl.unblockUI();
                return ;
    		}
            
            upida.post("sys/user/saveProfile", $scope.smuser).then(function (data) {
                // 提示信息
                $scope.message = data;
                $('#static').modal();
                form.$setPristine();
                //操作成功 再调用查询
                loadRight();
            });
        });
    };

    //初始化 
    $scope.$on("$stateChangeSuccess", function () {
        loadRight();
    });
}]);