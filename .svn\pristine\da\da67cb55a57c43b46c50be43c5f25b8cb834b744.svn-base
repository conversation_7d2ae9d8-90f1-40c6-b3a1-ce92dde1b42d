package com.kyb.pcberp.modules.crm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.crm.entity.AccountsReceivable;
import com.kyb.pcberp.modules.crm.entity.AccountsReceivableAdjust;

@MyBatisDao
public interface AccountsReceivableAdjustDao extends CrudDao<AccountsReceivableAdjust>
{
    /** zjn 2019-10-09 批量添加收款对账单录入记录*/
    void batchInsert(@Param("arList") List<AccountsReceivable> arList);
}
