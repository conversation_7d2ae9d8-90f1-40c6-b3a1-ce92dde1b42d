package com.kyb.pcberp.modules.crm.vo;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class UsedMaterialVo extends DataEntity<UsedMaterialVo>
{
    private static final long serialVersionUID = -8449004501646439164L;

    private Date sentTimeStartQr; // 时间段查询条件 载体

    private Date sentTimeEndQr;

    private String contractNo;

    private String customerPo;

    private String customerModel;

    private BigDecimal orderDeailArea;

    private String craftNo;

    private String usedType;

    private String feedNos;

    private String feedMaterialNo;

    private String feedMaterialSpe;

    private String feedQuantity;

    private String feedRemainQty;

    private String mainMaterialNo;

    private String backMaterialNo;

    private String mainMaterialSpe;

    private String backMaterialSpe;

    private String contractDetailId;

    private String companyId;

    private String companyName;

    private BigDecimal occupiedStock;

    private BigDecimal replyStock;

    private BigDecimal excessStock;

    private BigDecimal needBord;

    private BigDecimal useStocks;

    private BigDecimal mainStock;

    private String materialStockLists;

    private List<String> list;

    private Integer boardNum;

    private Integer remaQty;

    private String manufacturer;

    private BigDecimal materialFee;

    private String specification;

    private BigDecimal area;

    private BigDecimal price;

    private BigDecimal money;

    private BigDecimal materialP;

    private BigDecimal areaPrice;

    private Date acDistributeDate;

    private String feedId;

    private Integer boardQuantity; // 投料大板数

    private BigDecimal feedArea; // 投料面积

    private BigDecimal issueArea; // 发料面积

    private Integer mergeOrderQty; // 合单订单数量

    private Integer orderQty; // 订单数量

    private String documentsStatus;

    private String orderType;

    private BigDecimal weightedAverageMoney;

    private BigDecimal materialPp;

    private String materialStockListsTwo;

    private BigDecimal purchasePrice; // 平米单价

    private BigDecimal purchaseMoney; //最新采购平米金额

    private BigDecimal weightingPrice; //加权单价

    private BigDecimal weightingMoney; //加权平米金额

    private BigDecimal priceDifference; //采购平米差价

    private BigDecimal weightingDifference; //加权平米差价

    @ExcelField(title = "销售合同", align = 2, sort = 10)
    public String getContractNo()
    {
        return contractNo;
    }

    @ExcelField(title = "客户订单号", align = 2, sort = 20)
    public String getCustomerPo()
    {
        return customerPo;
    }

    @ExcelField(title = "客户型号", align = 2, sort = 30)
    public String getCustomerModel()
    {
        return customerModel;
    }

    @ExcelField(title = "生产编号", align = 2, sort = 40)
    public String getCraftNo()
    {
        return craftNo;
    }

    @ExcelField(title = "合同面积", align = 2, sort = 50)
    public BigDecimal getOrderDeailArea()
    {
        return orderDeailArea;
    }

    @ExcelField(title = "投料单", align = 2, sort = 60)
    public String getFeedNos()
    {
        return feedNos;
    }

    @ExcelField(title = "市场指定主料", align = 2, sort = 70)
    public String getMainMaterialNo()
    {
        return mainMaterialNo;
    }

    @ExcelField(title = "市场指定主料数量", align = 2, sort = 80)
    public BigDecimal getMainStock()
    {
        return mainStock;
    }

    @ExcelField(title = "市场指定替换料", align = 2, sort = 90)
    public String getBackMaterialNo()
    {
        return backMaterialNo;
    }

    @ExcelField(title = "市场指定替换料数量", align = 2, sort = 100)
    public BigDecimal getUseStocks()
    {
        return useStocks;
    }

    @ExcelField(title = "市场指定小计", align = 2, sort = 110)
    public BigDecimal getNeedBord()
    {
        return needBord;
    }

    @ExcelField(title = "实际出库大板数", align = 2, sort = 120)
    public Integer getBoardNum()
    {
        return boardNum;
    }

    @ExcelField(title = "实际出库余料数", align = 2, sort = 130)
    public Integer getRemaQty()
    {
        return remaQty;
    }

    @ExcelField(title = "实际出库小计", align = 2, sort = 140)
    public Integer getBoardRemaQty()
    {
        if(null == remaQty)
        {
            remaQty = 0;
        }
        if(null == boardNum)
        {
            boardNum = 0;
        }
        return remaQty + boardNum;
    }

    @ExcelField(title = "实际出库详情", align = 2, sort = 150)
    public String getDeailStr()
    {
        String str = "";
        if (list != null && list.size() > 0)
        {
            for (int i = 0; i < list.size(); i++)
            {
                str = StringUtils.isNotBlank(str) ? (str + "," + list.get(i)) : list.get(i);
            }
        }
        return str;
    }

    public Date getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }

    public void setSentTimeStartQr(Date sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }

    public Date getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }

    public void setSentTimeEndQr(Date sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }

    public void setContractNo(String contractNo)
    {
        this.contractNo = contractNo;
    }

    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }

    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }

    public void setOrderDeailArea(BigDecimal orderDeailArea)
    {
        this.orderDeailArea = orderDeailArea;
    }

    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }

    public String getUsedType()
    {
        return usedType;
    }

    public void setUsedType(String usedType)
    {
        this.usedType = usedType;
    }

    public void setFeedNos(String feedNos)
    {
        this.feedNos = feedNos;
    }

    public String getFeedMaterialNo()
    {
        return feedMaterialNo;
    }

    public void setFeedMaterialNo(String feedMaterialNo)
    {
        this.feedMaterialNo = feedMaterialNo;
    }

    public String getFeedMaterialSpe()
    {
        return feedMaterialSpe;
    }

    public void setFeedMaterialSpe(String feedMaterialSpe)
    {
        this.feedMaterialSpe = feedMaterialSpe;
    }

    public String getFeedQuantity()
    {
        return feedQuantity;
    }

    public void setFeedQuantity(String feedQuantity)
    {
        this.feedQuantity = feedQuantity;
    }

    public String getFeedRemainQty()
    {
        return feedRemainQty;
    }

    public void setFeedRemainQty(String feedRemainQty)
    {
        this.feedRemainQty = feedRemainQty;
    }

    public void setMainMaterialNo(String mainMaterialNo)
    {
        this.mainMaterialNo = mainMaterialNo;
    }

    public void setBackMaterialNo(String backMaterialNo)
    {
        this.backMaterialNo = backMaterialNo;
    }

    public String getMainMaterialSpe()
    {
        return mainMaterialSpe;
    }

    public void setMainMaterialSpe(String mainMaterialSpe)
    {
        this.mainMaterialSpe = mainMaterialSpe;
    }

    public String getBackMaterialSpe()
    {
        return backMaterialSpe;
    }

    public void setBackMaterialSpe(String backMaterialSpe)
    {
        this.backMaterialSpe = backMaterialSpe;
    }

    public String getContractDetailId()
    {
        return contractDetailId;
    }

    public void setContractDetailId(String contractDetailId)
    {
        this.contractDetailId = contractDetailId;
    }

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    public String getCompanyName()
    {
        return companyName;
    }

    public void setCompanyName(String companyName)
    {
        this.companyName = companyName;
    }

    public BigDecimal getOccupiedStock()
    {
        return occupiedStock;
    }

    public void setOccupiedStock(BigDecimal occupiedStock)
    {
        this.occupiedStock = occupiedStock;
    }

    public BigDecimal getReplyStock()
    {
        return replyStock;
    }

    public void setReplyStock(BigDecimal replyStock)
    {
        this.replyStock = replyStock;
    }

    public BigDecimal getExcessStock()
    {
        return excessStock;
    }

    public void setExcessStock(BigDecimal excessStock)
    {
        this.excessStock = excessStock;
    }

    public void setNeedBord(BigDecimal needBord)
    {
        this.needBord = needBord;
    }

    public void setUseStocks(BigDecimal useStocks)
    {
        this.useStocks = useStocks;
    }

    public void setMainStock(BigDecimal mainStock)
    {
        this.mainStock = mainStock;
    }

    public String getMaterialStockLists()
    {
        return materialStockLists;
    }

    public void setMaterialStockLists(String materialStockLists)
    {
        this.materialStockLists = materialStockLists;
    }

    public List<String> getList()
    {
        return list;
    }

    public void setList(List<String> list)
    {
        this.list = list;
    }

    public void setBoardNum(Integer boardNum)
    {
        this.boardNum = boardNum;
    }

    public void setRemaQty(Integer remaQty)
    {
        this.remaQty = remaQty;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public BigDecimal getArea() {
        return area;
    }

    public void setArea(BigDecimal area) {
        this.area = area;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public BigDecimal getMaterialP() {
        return materialP;
    }

    public void setMaterialP(BigDecimal materialP) {
        this.materialP = materialP;
    }

    public BigDecimal getMaterialFee() {
        return materialFee;
    }

    public void setMaterialFee(BigDecimal materialFee) {
        this.materialFee = materialFee;
    }

    public BigDecimal getAreaPrice()
    {
        return areaPrice;
    }

    public void setAreaPrice(BigDecimal areaPrice)
    {
        this.areaPrice = areaPrice;
    }

    public Date getAcDistributeDate()
    {
        return acDistributeDate;
    }

    public void setAcDistributeDate(Date acDistributeDate)
    {
        this.acDistributeDate = acDistributeDate;
    }

    public String getAcDistributeDateStr()
    {
        if(null != acDistributeDate)
        {
            return DateUtils.formatDate(acDistributeDate,"yyyy-MM-dd HH:mm:ss");
        }
        return null;
    }

    public String getFeedId()
    {
        return feedId;
    }

    public void setFeedId(String feedId)
    {
        this.feedId = feedId;
    }

    public Integer getBoardQuantity()
    {
        return boardQuantity;
    }

    public void setBoardQuantity(Integer boardQuantity)
    {
        this.boardQuantity = boardQuantity;
    }

    public BigDecimal getFeedArea()
    {
        return feedArea;
    }

    public void setFeedArea(BigDecimal feedArea)
    {
        this.feedArea = feedArea;
    }

    public BigDecimal getIssueArea()
    {
        return issueArea;
    }

    public void setIssueArea(BigDecimal issueArea)
    {
        this.issueArea = issueArea;
    }

    public Integer getMergeOrderQty()
    {
        return mergeOrderQty;
    }

    public void setMergeOrderQty(Integer mergeOrderQty)
    {
        this.mergeOrderQty = mergeOrderQty;
    }

    public Integer getOrderQty()
    {
        return orderQty;
    }

    public void setOrderQty(Integer orderQty)
    {
        this.orderQty = orderQty;
    }

    public String getDocumentsStatus()
    {
        return documentsStatus;
    }

    public void setDocumentsStatus(String documentsStatus)
    {
        this.documentsStatus = documentsStatus;
    }

    public String getOrderType()
    {
        return orderType;
    }

    public void setOrderType(String orderType)
    {
        this.orderType = orderType;
    }

    public BigDecimal getWeightedAverageMoney()
    {
        return weightedAverageMoney;
    }

    public void setWeightedAverageMoney(BigDecimal weightedAverageMoney)
    {
        this.weightedAverageMoney = weightedAverageMoney;
    }

    public BigDecimal getMaterialPp()
    {
        return materialPp;
    }

    public void setMaterialPp(BigDecimal materialPp)
    {
        this.materialPp = materialPp;
    }

    public String getMaterialStockListsTwo()
    {
        return materialStockListsTwo;
    }

    public void setMaterialStockListsTwo(String materialStockListsTwo)
    {
        this.materialStockListsTwo = materialStockListsTwo;
    }

    public BigDecimal getPurchasePrice()
    {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice)
    {
        this.purchasePrice = purchasePrice;
    }

    public BigDecimal getPurchaseMoney()
    {
        return purchaseMoney;
    }

    public void setPurchaseMoney(BigDecimal purchaseMoney)
    {
        this.purchaseMoney = purchaseMoney;
    }

    public BigDecimal getWeightingPrice()
    {
        return weightingPrice;
    }

    public void setWeightingPrice(BigDecimal weightingPrice)
    {
        this.weightingPrice = weightingPrice;
    }

    public BigDecimal getWeightingMoney()
    {
        return weightingMoney;
    }

    public void setWeightingMoney(BigDecimal weightingMoney)
    {
        this.weightingMoney = weightingMoney;
    }

    public BigDecimal getPriceDifference()
    {
        return priceDifference;
    }

    public void setPriceDifference(BigDecimal priceDifference)
    {
        this.priceDifference = priceDifference;
    }

    public BigDecimal getWeightingDifference()
    {
        return weightingDifference;
    }

    public void setWeightingDifference(BigDecimal weightingDifference)
    {
        this.weightingDifference = weightingDifference;
    }
}
