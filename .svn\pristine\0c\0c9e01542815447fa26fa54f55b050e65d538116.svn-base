package com.kyb.pcberp.modules.crm.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

import java.math.BigDecimal;
import java.util.List;

public class QuotationResult extends DataEntity<QuotationResult> {

    private static final long serialVersionUID = 1L;

    private BigDecimal salesProcessPrice;

    private BigDecimal processPrice;

    private String description;

    private String quotationDetailId;

    private BigDecimal price;

    private BigDecimal salePrcessPrice;

    private String quotationResultId;

    private BigDecimal salePcsPrice;

    private BigDecimal windowPcsPrice;

    private Integer checked;

    private BigDecimal prcessPrice; //系统加工平米单价

    private BigDecimal avgPrice; //材料平米单价

    private BigDecimal sysSalePrice; //系统业务费平米单价

    private BigDecimal managePrice; //系统管理费平米单价

    private BigDecimal pcsPrecent; //费率

    private BigDecimal salePcesssPrice; //销售加工平米单价

    private BigDecimal saleManagePrice; //管理费平米单价

    private BigDecimal saleSalePrice; //业务费平米单价

    private BigDecimal saleFee; //业务费率

    private BigDecimal manageFee; //管理费率

    private BigDecimal avgWindowPrice;

    private BigDecimal avgSalePrice;

    private BigDecimal materialWindowArea;

    private BigDecimal materialSaleArea;

    private BigDecimal adjustProcessPrice;

    private String keepDecimal;

    private List<QuoteSetupDetailColumn> cloumnList;

    public BigDecimal getSalesProcessPrice() {
        return salesProcessPrice;
    }

    public void setSalesProcessPrice(BigDecimal salesProcessPrice) {
        this.salesProcessPrice = salesProcessPrice;
    }

    public BigDecimal getProcessPrice() {
        return processPrice;
    }

    public void setProcessPrice(BigDecimal processPrice) {
        this.processPrice = processPrice;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getQuotationDetailId() {
        return quotationDetailId;
    }

    public void setQuotationDetailId(String quotationDetailId) {
        this.quotationDetailId = quotationDetailId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getSalePrcessPrice() {
        return salePrcessPrice;
    }

    public void setSalePrcessPrice(BigDecimal salePrcessPrice) {
        this.salePrcessPrice = salePrcessPrice;
    }

    public String getQuotationResultId() {
        return quotationResultId;
    }

    public void setQuotationResultId(String quotationResultId) {
        this.quotationResultId = quotationResultId;
    }

    public BigDecimal getSalePcsPrice() {
        return salePcsPrice;
    }

    public void setSalePcsPrice(BigDecimal salePcsPrice) {
        this.salePcsPrice = salePcsPrice;
    }

    public BigDecimal getWindowPcsPrice() {
        return windowPcsPrice;
    }

    public void setWindowPcsPrice(BigDecimal windowPcsPrice) {
        this.windowPcsPrice = windowPcsPrice;
    }

    public Integer getChecked() {
        return checked;
    }

    public void setChecked(Integer checked) {
        this.checked = checked;
    }

    public BigDecimal getPrcessPrice() {
        return prcessPrice;
    }

    public void setPrcessPrice(BigDecimal prcessPrice) {
        this.prcessPrice = prcessPrice;
    }

    public BigDecimal getAvgPrice() {
        return avgPrice;
    }

    public void setAvgPrice(BigDecimal avgPrice) {
        this.avgPrice = avgPrice;
    }

    public BigDecimal getSysSalePrice() {
        return sysSalePrice;
    }

    public void setSysSalePrice(BigDecimal sysSalePrice) {
        this.sysSalePrice = sysSalePrice;
    }

    public BigDecimal getManagePrice() {
        return managePrice;
    }

    public void setManagePrice(BigDecimal managePrice) {
        this.managePrice = managePrice;
    }

    public BigDecimal getPcsPrecent() {
        return pcsPrecent;
    }

    public void setPcsPrecent(BigDecimal pcsPrecent) {
        this.pcsPrecent = pcsPrecent;
    }

    public BigDecimal getSalePcesssPrice() {
        return salePcesssPrice;
    }

    public void setSalePcesssPrice(BigDecimal salePcesssPrice) {
        this.salePcesssPrice = salePcesssPrice;
    }

    public BigDecimal getSaleManagePrice() {
        return saleManagePrice;
    }

    public void setSaleManagePrice(BigDecimal saleManagePrice) {
        this.saleManagePrice = saleManagePrice;
    }

    public BigDecimal getSaleSalePrice() {
        return saleSalePrice;
    }

    public void setSaleSalePrice(BigDecimal saleSalePrice) {
        this.saleSalePrice = saleSalePrice;
    }

    public BigDecimal getSaleFee() {
        return saleFee;
    }

    public void setSaleFee(BigDecimal saleFee) {
        this.saleFee = saleFee;
    }

    public BigDecimal getManageFee() {
        return manageFee;
    }

    public void setManageFee(BigDecimal manageFee) {
        this.manageFee = manageFee;
    }

    public BigDecimal getAvgWindowPrice() {
        return avgWindowPrice;
    }

    public void setAvgWindowPrice(BigDecimal avgWindowPrice) {
        this.avgWindowPrice = avgWindowPrice;
    }

    public BigDecimal getAvgSalePrice() {
        return avgSalePrice;
    }

    public void setAvgSalePrice(BigDecimal avgSalePrice) {
        this.avgSalePrice = avgSalePrice;
    }

    public List<QuoteSetupDetailColumn> getCloumnList()
    {
        return cloumnList;
    }

    public void setCloumnList(List<QuoteSetupDetailColumn> cloumnList)
    {
        this.cloumnList = cloumnList;
    }

    public BigDecimal getMaterialWindowArea() {
        return materialWindowArea;
    }

    public void setMaterialWindowArea(BigDecimal materialWindowArea) {
        this.materialWindowArea = materialWindowArea;
    }

    public BigDecimal getMaterialSaleArea() {
        return materialSaleArea;
    }

    public void setMaterialSaleArea(BigDecimal materialSaleArea) {
        this.materialSaleArea = materialSaleArea;
    }

    public BigDecimal getAdjustProcessPrice() {
        return adjustProcessPrice;
    }

    public void setAdjustProcessPrice(BigDecimal adjustProcessPrice) {
        this.adjustProcessPrice = adjustProcessPrice;
    }

    public String getKeepDecimal() {
        return keepDecimal;
    }

    public void setKeepDecimal(String keepDecimal) {
        this.keepDecimal = keepDecimal;
    }
}
