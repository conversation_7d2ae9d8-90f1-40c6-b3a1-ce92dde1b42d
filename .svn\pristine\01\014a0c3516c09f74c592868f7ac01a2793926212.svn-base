/**
 * 
 */
package com.kyb.pcberp.modules.contract.dao;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.contract.entity.Delivery;
import com.kyb.pcberp.modules.contract.entity.DeliveryDetail;
import com.kyb.pcberp.modules.contract.vo.LdsyDeliveryExport;
import com.kyb.pcberp.modules.crm.entity.RejectApplication;
import com.kyb.pcberp.modules.sys.entity.Company;

/**
 * deliver送货单DAO接口
 * 
 * <AUTHOR>
 * @version 2015-09-28
 */
@MyBatisDao
public interface DeliveryDao extends CrudDao<Delivery>
{
    
    public List<Delivery> findListByCondition(Delivery entity);
    
    void updateUpdateDate(Delivery delivery);
    
    // 确认状态
    void updateStutas(Delivery delivery);
    
    List<Delivery> findAllUnfilledDelivery(Delivery delivery);
    
    public Delivery getById(String recordId);
    
    /**
     * ycy 2016-9-27 初始化销售交货，金额查询
     * 
     * @param delivery
     * @return
     */
    public List<Delivery> getInitializationDelivery(Delivery delivery);
    
    /**
     * 打印
     * 
     * @param Delivery
     * @return
     */
    public Delivery toPrint(Delivery delivery);
    
    /**
     * 查询送货单中此单据编号是否被引用了
     * 
     * @param notification
     * @return
     */
    public Integer findDeliveryNoisEnable(Delivery delivery);
    
    /**
     * 导出
     * 
     * @param delivery
     * @return
     */
    public List<Delivery> findExpList(Delivery delivery);
    
    /**
     * WC 2017-01-16 查询送货单的状态
     */
    public Delivery findDeliveryStatus(Delivery delivery);
    
    /**
     * WC 2017-02-06 根据送货单明细ID获取送货单信息
     */
    public Delivery getDeliveryByDetailId(@Param("recordId") String recordId);
    
    /**
     * WC 2017-02-15 根据客户ID查询所有的寄存送货单
     */
    public List<Delivery> getAllDeliveryListByCustomID(Delivery delivery);
    
    /**
     * WC 2017-03-15 查询送货单状态及是否被删除了
     * 
     * @param delivery
     * @return
     */
    public Delivery getDeliveryStatusAndActiveFlag(Delivery delivery);
    
    /**
     * WC 2017-07-20 检查送货单的明细对应合同是否关闭
     */
    public Delivery findContractIsClosed(Delivery delivery);
    
    public String getCustomerDelivery(String recordId);
    
    /**
     * <AUTHOR>
     * @Description 删除时查询送货单明细中的送货数量和合同明细的已送货数量
     * @Date 13:25 2018-09-22
     * @Param [delivery]
     * @return
     **/
    List<DeliveryDetail> selectDetailQAndContractDetailQ(Delivery delivery);
    
    /**
     * <AUTHOR>
     * @Description 报表统计中。除当天已确认送货单
     * @Date 18:27 2018-10-09
     * @Param [rejectApplication]
     * @return
     **/
    public Integer selectDeliverySum(RejectApplication rejectApplication);
    
    /** 2019-03-18 根据合同明细查送货单 **/
    Delivery getDeliveryByContractDetailId(ContractDetail contractDetail);
    
    List<LdsyDeliveryExport> getLdsyDeliveryExports(Delivery delivery);
    
    void runInsert(Delivery delivery);
    
    List<Delivery> findDeliveryList(Delivery delivery);
    
    void runAdd(DeliveryDetail lnDeliveryDetail);
    
    void delDeliveryByGroupCenterId(@Param("groupCenterId") String groupCenterId, @Param("company") Company company);
    
    Integer checkSentOutStockCount(Delivery delivery);
    
    void updateSentTimeByGroupCenterId(@Param("groupCenterId") String groupCenterId, @Param("sentTime") Date sentTime,
        @Param("oldoutBoundId") String oldoutBoundId);
    
    void updateSentTimeByDelivery(Delivery delivery);
    
    String getDeliveryIds(@Param("oldOutBoundId") String oldOutBoundId);
    
    void updateSignBackDate(Delivery delivery);
    
    void cleanDataByIds(Delivery delivery);
    
    void updateActiveFlag(@Param("recordId") String recordId);

    List<Delivery> getDeliveryList(@Param("recordId") String recordId);
}