package com.kyb.pcberp.modules.crm.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.approval.entity.Approval;
import com.kyb.pcberp.modules.crm.entity.BatchTable;
import com.kyb.pcberp.modules.crm.entity.BusinessRates;

@MyBatisDao
public interface BusinessRatesDao
{
    void insert(BusinessRates businessRates);
    
    void update(BusinessRates businessRates);
    
    void updateStatus(BusinessRates businessRates);
    
    void delete(BusinessRates businessRates);
    
    BusinessRates get(BusinessRates businessRates);
    
    List<BusinessRates> findList(BusinessRates businessRates);
    
    Integer checkCount(BusinessRates businessRates);
    
    void insertBatchTable(BatchTable batchTable);
    
    Integer checkStatusCount(BatchTable batchTable);
    
    void batchUpdateStatus(BatchTable batchTable);
    
    BatchTable getBatchTable(@Param("recordId") String recordId);
    
    void batchUpdateData(BatchTable batchTable);
    
    void updateCustomerSalePrecent(BusinessRates businessRates);
    
    List<BusinessRates> getShowBusinessRates(Approval approval);
}
