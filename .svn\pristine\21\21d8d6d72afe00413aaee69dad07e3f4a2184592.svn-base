package com.kyb.pcberp.common.yunxingkong;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.kingdee.bos.webapi.entity.RepoRet;
import com.kingdee.bos.webapi.sdk.K3CloudApi;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.api.entity.CollectionReceipt;

import java.text.SimpleDateFormat;
import java.util.*;

import com.kyb.pcberp.modules.hr.finance_center.pojo.Hr_CollectPayBill;

public class CloudySkyUtil {

    // 静态内部类懒汉式（推荐）
    private CloudySkyUtil() {}

    // 静态内部类在第一次使用时才会加载
    private static class SingletonHolder
    {
        private static final CloudySkyUtil INSTANCE = new CloudySkyUtil();
    }

    public static CloudySkyUtil getInstance() {
        return SingletonHolder.INSTANCE;
    }

    public String returnFieldKeys(Integer type)
    {
        String fieldKeys = null;
        if(null == type)
        {
            return fieldKeys;
        }

        switch (type)
        {
            // 付款单
            case 1:
                fieldKeys = "FBillTypeID,FPAYORGID,FDATE,FCONTACTUNITTYPE,FCURRENCYID,FCONTACTUNIT," +
                            "FSETTLEORGID,FRECTUNITTYPE,FPURCHASEORGID,FRECTUNIT,FEXCHANGERATE,FSETTLECUR," +
                            "FSETTLERATE,FPURCHASEDEPTID,FREMARK,FBookingDate,FBUSINESSTYPE,FSETTLEMAINBOOKID,FPAYBILLENTRY";
                break;
        }
        return fieldKeys;
    }


    /**
     * zjn 2025-04-02 获取金蝶数据
     * @param type 分类类型
     * @return JSONArray
     */
    public JSONArray getKingdeeData(Integer type)
    {
        JSONArray array = new JSONArray();
        if(null == type)
        {
            return array;
        }
        String FormId = null; // 必填 - 要查询的表单ID
        String FieldKeys = null; // 可选 - 要返回的字段列表
        String FilterString = null; // 可选 - 过滤条件数组
        String OrderString = null; // // 可选 - 排序字段
        Integer TopRowCount = null; // 可选 - 返回前N条记录
        Integer StartRow = null; // 可选 - 分页起始行
        Integer Limit = null; // 可选 - 每页记录数
        String SubSystemId = null; // 可选 - 子系统ID
        switch (type)
        {
            // 付款单
            case 1:
                FormId = "FormId";
                FieldKeys = returnFieldKeys(type);
                Calendar cal = Calendar.getInstance();
                cal.setTime(new Date());
                cal.add(Calendar.DATE,-1);
                String nowDate = DateUtils.formatDate(cal.getTime());
                FilterString = "FDate >= "+nowDate+" and FDate <= "+nowDate;
                OrderString = "FDate DESC";
                break;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("FormId",FormId);
        params.put("FieldKeys", FieldKeys);
        params.put("FilterString", FilterString);
        params.put("OrderString", OrderString);
        params.put("TopRowCount", TopRowCount);
        params.put("StartRow", StartRow);
        params.put("Limit", Limit);
        params.put("SubSystemId", SubSystemId);
        String result = JSON.toJSONString(params);

        //读取配置，初始化SDK
        K3CloudApi client = new K3CloudApi();
        try
        {
            array = JSONArray.parseArray(JSON.toJSONString(client.executeBillQuery(result)));
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return array;
    }

    /**
     * zjn 2025-04-02 适配数据
     * @param  array  JSON集合
     * @return List<CollectionReceipt>
     */
    public List<CollectionReceipt> daptationData(JSONArray array)
    {
        List<CollectionReceipt> list = Lists.newArrayList();
        if(null == array || array.size() == 0)
        {
            return list;
        }
        List<CollectionReceipt> collectionReceiptList = Lists.newArrayList();
        // fieldKeys = "FBillTypeID,FPAYORGID,FDATE,FCONTACTUNITTYPE,FCURRENCYID,FCONTACTUNIT,FSETTLEORGID,FRECTUNITTYPE,FPURCHASEORGID,FRECTUNIT,FEXCHANGERATE,FSETTLECUR,FSETTLERATE,"
        //+ "FPURCHASEDEPTID,FREMARK,FBookingDate,FBUSINESSTYPE,FSETTLEMAINBOOKID,FPAYBILLENTRY";
        for (int i = 0; i < array.size(); i++) {
            JSONArray row = array.getJSONArray(i);
            CollectionReceipt collection = new CollectionReceipt();
            collection.setFBillTypeID(row.get(0).toString());
            collection.setFPAYORGID(row.get(1).toString());
            collection.setFDATE(row.get(2).toString());
            collection.setFCONTACTUNITTYPE(row.get(3).toString());


            collectionReceiptList.add(collection);
        }
        return list;
    }



    public Map<String,Object> collectPayBill()
    {
        Map<String,Object> data = new HashMap<>();
        //注意 1：此处不再使用参数形式传入用户名及密码等敏感信息，改为在登录配置文件中设置。
        //注意 2：必须先配置第三方系统登录授权信息后，再进行业务操作，详情参考各语言版本SDK介绍中的登录配置文件说明。
        //读取配置，初始化SDK
        K3CloudApi client = new K3CloudApi();
        // 请求参数，要求为json字符串
        String jsonData = "{\"FormId\":\"AR_RECEIVEBILL\",\"FieldKeys\":\"FSETTLEORGID,FBillNo,FDATE\",\"FilterString\":\"\",\"OrderString\":\"\",\"TopRowCount\":0,\"StartRow\":0,\"Limit\":5,\"SubSystemId\":\"\"}";
        try {
            // 调用接口
            com.alibaba.fastjson.JSONArray resultJson = JSONArray.parseArray( JSON.toJSONString(client.executeBillQuery(jsonData)));
            System.out.println("接口返回结果: " + resultJson);
            // 解析返回结果
            List<CollectionReceipt> collectionReceiptList = new ArrayList<>();
            for (int i = 0; i < resultJson.size(); i++) {
                JSONArray row = resultJson.getJSONArray(i);
                CollectionReceipt collection = new CollectionReceipt();
                collection.setFBillNo(row.get(0).toString());
                collection.setFDATE(row.get(1).toString());
                collectionReceiptList.add(collection);
            }
            // 打印解析后的结果
            for (CollectionReceipt receipt : collectionReceiptList) {
                System.out.println("FBillNo: " + receipt.getFBillNo() + ", FDATE: " + receipt.getFDATE());
            }
            data.put("collectionReceiptList",collectionReceiptList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    //生成收款单
    public String saveReceiptPayBill(Hr_CollectPayBill hr_collectPayBill)
    {
        hr_collectPayBill.setFBUSINESSTYPE(hr_collectPayBill.getBusinessType()); //业务类型
        hr_collectPayBill.setFBillTypeIDNumber(hr_collectPayBill.getBillTypeNo()); //单据类型
        hr_collectPayBill.setFPAYORGIDNumber(hr_collectPayBill.getCollectOrganizationNo()); //结算组织
        hr_collectPayBill.setFSETTLEORGIDNumber(hr_collectPayBill.getClearOrganizationNo()); //结算组织
        hr_collectPayBill.setFSALEORGIDNumber(hr_collectPayBill.getSaleOrganizationNo()); //销售组织
        Date businessDate = hr_collectPayBill.getBusinessDate();
        // 定义目标日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 将业务日期转换为指定格式的字符串
        String formattedDate = dateFormat.format(businessDate);
        // 将格式化后的日期设置给 hr_collectPayBill
        hr_collectPayBill.setFDATE(formattedDate);//业务日期
        hr_collectPayBill.setFCONTACTUNITTYPE(hr_collectPayBill.getAccountTypeIdStr()); //往来单位类型
        hr_collectPayBill.setFCONTACTUNITNumber(hr_collectPayBill.getCorrespondentIdNo()); //往来单位
        hr_collectPayBill.setFPAYUNITTYPE(hr_collectPayBill.getPayUnitTypeIdStr()); //付款单位类型
        hr_collectPayBill.setFPAYUNITNumber(hr_collectPayBill.getPayUnitIdNo()); //付款单位
        hr_collectPayBill.setFCURRENCYIDNumber(hr_collectPayBill.getCurrencyNo()); //币别编码
        hr_collectPayBill.setFEXCHANGERATE(hr_collectPayBill.getCurrencyRate()); //汇率
        hr_collectPayBill.setFSETTLECURNumber(hr_collectPayBill.getCurrencyNo());//结算币别编码
        hr_collectPayBill.setFSETTLERATE(hr_collectPayBill.getCurrencyRate()); //结算币别汇率
        hr_collectPayBill.setFSALEDEPTIDNumber(hr_collectPayBill.getSaleDeptIdNo()); //销售部门编码
        hr_collectPayBill.setFSALEERIDNumber(hr_collectPayBill.getSalesManIdNo()); //销售人员编码
        String remarkHead = "A" + hr_collectPayBill.getRecordId();
        if (StringUtils.isNotBlank(hr_collectPayBill.getRemarkHead()))
        {
            remarkHead = remarkHead +"-" + hr_collectPayBill.getRemarkHead();
        }
//        if (hr_collectPayBill.getDimensionId().equals("1"))
//        {
//            formattedId = "SKD" + String.format("%08d", Integer.valueOf(hr_collectPayBill.getRecordId()));
//        }else{
//            formattedId = "FKD" + String.format("%08d", Integer.valueOf(hr_collectPayBill.getRecordId()));
//        }
//        String remarkHead = formattedId + "-" + hr_collectPayBill.getRemarkHead();
        hr_collectPayBill.setFREMARK(remarkHead); //表头备注
        Date createdDate = hr_collectPayBill.getCreatedDate();
        // 定义目标日期格式
        SimpleDateFormat dateFormatTwo = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 将业务日期转换为指定格式的字符串
        String formattedDateTwo = dateFormatTwo.format(createdDate);
        // 将格式化后的日期设置给 hr_collectPayBill
        hr_collectPayBill.setFPOSTDATE(formattedDateTwo); //创建日期
        hr_collectPayBill.setFSETTLEMAINBOOKIDNumber(hr_collectPayBill.getCurrencyNo());

        K3CloudApi client = new K3CloudApi();
        String jsonData = "";
        if (StringUtils.isNotBlank(hr_collectPayBill.getDimensionId()) && hr_collectPayBill.getDimensionId().equals("1"))
        {
            //请求参数，要求为json字符串
            jsonData =
                    "{\"NeedUpDateFields\":[]," +
                    "\"NeedReturnFields\":[]," +
                    "\"IsDeleteEntry\":\"true\"," +
                    "\"SubSystemId\":\"\"," +
                    "\"IsVerifyBaseDataField\":\"false\"," +
                    "\"IsEntryBatchFill\":\"true\"," +
                    "\"ValidateFlag\":\"true\"," +
                    "\"NumberSearch\":\"true\"," +
                    "\"IsAutoAdjustField\":\"true\"," +
                    "\"InterationFlags\":\"\"," +
                    "\"IgnoreInterationFlag\":\"\"," +
                    "\"IsControlPrecision\":\"false\"," +
                    "\"ValidateRepeatJson\":\"false\"," +
                    "\"Model\":{" +
                    "\"FID\":0," +
                    "\"FBillTypeID\":{\"FNUMBER\":\""+ hr_collectPayBill.getFBillTypeIDNumber() + "\"}," +
                    "\"FPAYORGID\":{\"FNumber\":\"" + hr_collectPayBill.getFPAYORGIDNumber() + "\"}," +
                    "\"FSETTLEORGID\":{\"FNumber\":\""+ hr_collectPayBill.getFSETTLEORGIDNumber() +"\"}," +
                    "\"FSALEORGID\":{\"FNumber\":\"" + hr_collectPayBill.getFSALEORGIDNumber() + "\"}," +
                    "\"FDATE\":\""+ hr_collectPayBill.getFDATE() +"\"," +
                    "\"FCONTACTUNITTYPE\":\""+ hr_collectPayBill.getFCONTACTUNITTYPE()+"\"," +
                    "\"FCONTACTUNIT\":{\"FNumber\":\""+ hr_collectPayBill.getFCONTACTUNITNumber()+"\"}," +
                    "\"FPAYUNITTYPE\":\""+ hr_collectPayBill.getFPAYUNITTYPE() +"\"," +
                    "\"FPAYUNIT\":{\"FNumber\":\""+ hr_collectPayBill.getFPAYUNITNumber() +"\"}," +
                    "\"FCURRENCYID\":{\"FNumber\":\""+ hr_collectPayBill.getFCURRENCYIDNumber() +"\"}," +
                    "\"FEXCHANGERATE\":"+ hr_collectPayBill.getFEXCHANGERATE() +"," +
                    "\"FSETTLECUR\":{\"FNUMBER\":\""+hr_collectPayBill.getFSETTLECURNumber() +"\"}," +
                    "\"FSETTLERATE\":"+ hr_collectPayBill.getFSETTLERATE() +"," +
                    "\"FSALEDEPTID\":{\"FNumber\":\""+hr_collectPayBill.getFSALEDEPTIDNumber() +"\"}," +
                    "\"FSALEERID\":{\"FNumber\":\"\"}," +
                    "\"FISINIT\":\"false\"," +
                    "\"FREMARK\":\""+hr_collectPayBill.getFREMARK()+"\"," +
                    "\"FISCARRYRATE\":\"false\"," +
                    "\"FDOCUMENTSTATUS\":\"Z\"," +
                    "\"FBUSINESSTYPE\":\"" + hr_collectPayBill.getFBUSINESSTYPE() + "\"," +
                    "\"FCancelStatus\":\"A\"," +
                    "\"FSETTLEMAINBOOKID\":{\"FNUMBER\":\""+hr_collectPayBill.getFSETTLEMAINBOOKIDNumber()+"\"}," +
                    "\"FRECEIVEBILLENTRY\":[";

                for(int i = 0; i< hr_collectPayBill.getCollectPayDetailList().size(); i++) {
                    Hr_CollectPayBill collectPay = hr_collectPayBill.getCollectPayDetailList().get(i);
                    hr_collectPayBill.setFSETTLETYPEIDNumber(collectPay.getSettlementNo()); //结算方式
                    hr_collectPayBill.setFPURPOSEIDNumber(collectPay.getCollectPurposeNo()); //收付款用途
                    hr_collectPayBill.setFRECTOTALAMOUNTFOR(collectPay.getAmountReceivable()); //表体-应收金额
                    hr_collectPayBill.setFRECAMOUNTFOR_E(collectPay.getPaidAmount()); //表体-应付金额
                    hr_collectPayBill.setFRECNOTAXAMOUNTFOR(collectPay.getAmountReceivable()); //应收金额不含税
                    hr_collectPayBill.setFRECNOTAXAMOUNT(collectPay.getAmountReceivable()); //应收金额不含税本位币
                    hr_collectPayBill.setFACCOUNTIDNumber(collectPay.getBankAccountName()); //银行账号
                    String remark = "B" + collectPay.getRecordId();
                    if (StringUtils.isNotBlank(hr_collectPayBill.getRemarkHead()))
                    {
                        remark = remark +"-" + collectPay.getRemark();
                    }
                    hr_collectPayBill.setFCOMMENT(remark); //明细备注
                    hr_collectPayBill.setFNOTVERIFICATEAMOUNT(collectPay.getAmountReceivable()); //未核销金额
                    hr_collectPayBill.setFPAYAMOUNT_E(collectPay.getAmountReceivable()); //付款金额本位币
                    hr_collectPayBill.setFSETTLEPAYAMOUNTFOR(collectPay.getAmountReceivable()); //折后金额
                    hr_collectPayBill.setFPAYAMOUNTFOR_E(collectPay.getAmountReceivable()); //付款金额
                    hr_collectPayBill.setFPAYTOTALAMOUNTFOR(collectPay.getAmountReceivable()); //表体应付金额

                    String jsonDataDetail = "{" +
                            "\"FSETTLETYPEID\":{\"FNumber\":\""+hr_collectPayBill.getFSETTLETYPEIDNumber()+"\"}," +
                            "\"FPURPOSEID\":{\"FNumber\":\""+ hr_collectPayBill.getFPURPOSEIDNumber() +"\"}," +
                            "\"FSaleOrderID\":0," +
                            "\"FRECTOTALAMOUNTFOR\":"+hr_collectPayBill.getFRECTOTALAMOUNTFOR() +"," +
                            "\"FRECAMOUNTFOR_E\":"+hr_collectPayBill.getFRECAMOUNTFOR_E()+"," +
                            "\"FSETTLEDISTAMOUNTFOR\":0.0," +
                            "\"FHANDLINGCHARGEFOR\":0.0," +
                            "\"FOVERUNDERAMOUNTFOR\":0.0," +
                            "\"FCOMMENT\":\""+hr_collectPayBill.getFCOMMENT()+"\"," +
                            "\"FACCOUNTID\":{\"FNumber\":\""+ hr_collectPayBill.getFACCOUNTIDNumber() +"\"}," +
                            "\"FPrice\":0," +
                            "\"FMATERIALSEQ\":0," +
                            "\"FORDERENTRYID\":0," +
                            "\"FQty\":0," +
                            "\"FPOSTDATE\":\""+ hr_collectPayBill.getFPOSTDATE() +"\"," +
                            "\"FENTRYTAXRATE\":0.0," +
                            "\"FTAXAMOUNTFOR\":0.0," +
                            "\"FTAXAMOUNT\":0.0," +
                            "\"FRECNOTAXAMOUNTFOR\":"+ hr_collectPayBill.getFRECNOTAXAMOUNTFOR() +"," +
                            "\"FRECNOTAXAMOUNT\":"+ hr_collectPayBill.getFRECNOTAXAMOUNT() +"," +
                            "\"FWRITTENOFFTAXAMOUNTFOR\":0.0," +
                            "\"FWRITTENOFFTAXAMOUNT\":0.0," +
                            "}";
                            if (hr_collectPayBill.getCollectPayDetailList().size() > 0 && hr_collectPayBill.getCollectPayDetailList().size() - 1 != i)
                            {
                                jsonData = jsonData + jsonDataDetail + ",";
                            }else{
                                jsonData = jsonData + jsonDataDetail;
                            }
                }
                jsonData = jsonData + "]}}";
        }
        //生成付款单至金蝶
        if (StringUtils.isNotBlank(hr_collectPayBill.getDimensionId()) && hr_collectPayBill.getDimensionId().equals("2"))
        {
            hr_collectPayBill.setFRECTUNITTYPE(hr_collectPayBill.getPayUnitTypeIdStr()); //收款单位类型
            hr_collectPayBill.setFPURCHASEORGIDNumber(hr_collectPayBill.getSaleOrganizationNo());//采购组织
            hr_collectPayBill.setFRECTUNITNumber(hr_collectPayBill.getPayUnitIdNo()); //收款单位
            hr_collectPayBill.setFPURCHASEDEPTIDNumber(hr_collectPayBill.getSaleDeptIdNo()); //采购部门
            if (null != hr_collectPayBill.getExpectedPayDate())
            {
                Date expectedPayDate = hr_collectPayBill.getExpectedPayDate();
                // 定义目标日期格式
                SimpleDateFormat dateFormatThree = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                // 将业务日期转换为指定格式的字符串
                String formattedDateThree = dateFormatThree.format(expectedPayDate);
                // 将格式化后的日期设置给 hr_collectPayBill
                hr_collectPayBill.setFBookingDate(formattedDateThree); //期望付款日期
            }

            jsonData = "{\"NeedUpDateFields\":[]," +
                    "\"NeedReturnFields\":[]," +
                    "\"IsDeleteEntry\":\"true\"," +
                    "\"SubSystemId\":\"\"," +
                    "\"IsVerifyBaseDataField\":\"false\"," +
                    "\"IsEntryBatchFill\":\"true\"," +
                    "\"ValidateFlag\":\"true\"," +
                    "\"NumberSearch\":\"true\"," +
                    "\"IsAutoAdjustField\":\"true\"," +
                    "\"InterationFlags\":\"\"," +
                    "\"IgnoreInterationFlag\":\"\"," +
                    "\"IsControlPrecision\":\"false\"," +
                    "\"ValidateRepeatJson\":\"false\"," +
                    "\"Model\":{\"FID\":0," +
                    "\"FBillTypeID\":{\"FNUMBER\":\""+ hr_collectPayBill.getFBillTypeIDNumber() +"\"}," +
                    "\"FPAYORGID\":{\"FNumber\":\""+hr_collectPayBill.getFPAYORGIDNumber()+"\"}," +
                    "\"FDATE\":\""+ hr_collectPayBill.getFDATE() +"\"," +
                    "\"FCONTACTUNITTYPE\":\""+ hr_collectPayBill.getFCONTACTUNITTYPE()+"\"," +
                    "\"FCURRENCYID\":{\"FNumber\":\""+ hr_collectPayBill.getFCURRENCYIDNumber() +"\"}," +
                    "\"FCONTACTUNIT\":{\"FNumber\":\""+ hr_collectPayBill.getFCONTACTUNITNumber() +"\"}," +
                    "\"FSETTLEORGID\":{\"FNumber\":\""+ hr_collectPayBill.getFSETTLEORGIDNumber() +"\"}," +
                    "\"FRECTUNITTYPE\":\""+ hr_collectPayBill.getFRECTUNITTYPE() +"\"," +
                    "\"FPURCHASEORGID\":{\"FNumber\":\""+ hr_collectPayBill.getFPURCHASEORGIDNumber() +"\"}," +
                    "\"FRECTUNIT\":{\"FNumber\":\""+ hr_collectPayBill.getFRECTUNITNumber() +"\"}," +
                    "\"FEXCHANGERATE\":"+ hr_collectPayBill.getFEXCHANGERATE() +"," +
                    "\"FSETTLECUR\":{\"FNUMBER\":\""+ hr_collectPayBill.getFSETTLECURNumber() +"\"}," +
                    "\"FDOCUMENTSTATUS\":\"Z\"," +
                    "\"FSETTLERATE\":"+ hr_collectPayBill.getFSETTLERATE() +"," +
                    "\"FDepartment\":{\"FNumber\":\"\"}," +
                    "\"FISINIT\":\"false\"," +
                    "\"FPURCHASEDEPTID\":{\"FNumber\":\""+ hr_collectPayBill.getFPURCHASEDEPTIDNumber() +"\"}," +
                    "\"FPURCHASERGROUPID\":{\"FNumber\":\"\"}," +
                    "\"FPURCHASERID\":{\"FNumber\":\"\"}," +
                    "\"FScanPoint\":{\"FNUMBER\":\"\"}," +
                    "\"FPRESETBASE1\":{\"FNUMBER\":\"\"}," +
                    "\"FPRESETBASE2\":{\"FNUMBER\":\"\"}," +
                    "\"FISSAMEORG\":\"false\"," +
                    "\"FIsCredit\":\"false\"," +
                    "\"FIsWriteOff\":\"false\"," +
                    "\"FMatchMethodID\":0," +
                    "\"FREALPAY\":\"false\"," +
                    "\"FREMARK\":\""+ hr_collectPayBill.getFREMARK() +"\"," +
                    "\"FBookingDate\":\""+ hr_collectPayBill.getFBookingDate() +"\"," +
                    "\"FTHIRDBILLNO\":\"\"," +
                    "\"FWBSETTLENO\":\"\"," +
                    "\"FISCARRYRATE\":\"false\"," +
                    "\"FGYSHOPNAME\":\"\"," +
                    "\"FGYCUSTOMERID\":{\"FNUMBER\":\"\"}," +
                    "\"FMoreReceive\":\"false\"," +
                    "\"FPRESETASSISTANT1\":{\"FNumber\":\"\"}," +
                    "\"FPRESETASSISTANT2\":{\"FNumber\":\"\"}," +
                    "\"FPRESETTEXT1\":\"\"," +
                    "\"FPRESETTEXT2\":\"\"," +
                    "\"FVirIsSameAcctOrg\":\"false\"," +
                    "\"FBUSINESSTYPE\":\""+ hr_collectPayBill.getFBUSINESSTYPE() +"\"," +
                    "\"FCancelStatus\":\"A\"," +
                    "\"FSETTLEMAINBOOKID\":{\"FNUMBER\":\""+ hr_collectPayBill.getFSETTLEMAINBOOKIDNumber() +"\"}," +
                    "\"FSHRID\":\"\"," +
                    "\"FSourceBillNumber\":\"\"," +
                    "\"FPAYBILLENTRY\":[" ;

                    for(int i = 0; i< hr_collectPayBill.getCollectPayDetailList().size(); i++)
                    {
                       Hr_CollectPayBill collectPay = hr_collectPayBill.getCollectPayDetailList().get(i);
                        hr_collectPayBill.setFSETTLETYPEIDNumber(collectPay.getSettlementNo()); //结算方式
                        hr_collectPayBill.setFPURPOSEIDNumber(collectPay.getCollectPurposeNo()); //收付款用途
                        hr_collectPayBill.setFRECTOTALAMOUNTFOR(collectPay.getAmountReceivable()); //表体-应收金额
                        hr_collectPayBill.setFRECAMOUNTFOR_E(collectPay.getPaidAmount()); //表体-应付金额
                        hr_collectPayBill.setFRECNOTAXAMOUNTFOR(collectPay.getAmountReceivable()); //应收金额不含税
                        hr_collectPayBill.setFRECNOTAXAMOUNT(collectPay.getAmountReceivable()); //应收金额不含税本位币
                        hr_collectPayBill.setFACCOUNTIDNumber(collectPay.getBankAccountName()); //银行账号
                        String remark = "B" + collectPay.getRecordId();
                        if (StringUtils.isNotBlank(hr_collectPayBill.getRemarkHead()))
                        {
                            remark = remark +"-" + collectPay.getRemark();
                        }
                        hr_collectPayBill.setFCOMMENT(remark); //明细备注
                        hr_collectPayBill.setFNOTVERIFICATEAMOUNT(collectPay.getAmountReceivable()); //未核销金额
                        hr_collectPayBill.setFPAYAMOUNT_E(collectPay.getAmountReceivable()); //付款金额本位币
                        hr_collectPayBill.setFSETTLEPAYAMOUNTFOR(collectPay.getAmountReceivable()); //折后金额

                        hr_collectPayBill.setFPAYAMOUNTFOR_E(collectPay.getAmountReceivable()); //付款金额
                        hr_collectPayBill.setFPAYTOTALAMOUNTFOR(collectPay.getAmountReceivable()); //表体应付金额

                       String jsonDataT =  "{" +
                                "\"FSETTLETYPEID\":{\"FNumber\":\""+ hr_collectPayBill.getFSETTLETYPEIDNumber() +"\"}," +
                                "\"FPURPOSEID\":{\"FNumber\":\""+ hr_collectPayBill.getFPURPOSEIDNumber() +"\"}," +
                                "\"FPAYITEMTYPE\":\"\"," +
                                "\"FPAYITEM\":\"\"," +
                                "\"FPAYAMOUNTFOR_E\":"+ hr_collectPayBill.getFPAYAMOUNTFOR_E() +"," +
                                "\"FPAYTOTALAMOUNTFOR\":"+ hr_collectPayBill.getFPAYTOTALAMOUNTFOR() +"," +
                                "\"FSETTLEDISTAMOUNTFOR\":0," +
                                "\"FOVERSHORTAGEFOR\":0," +
                                "\"FSETTLEPAYAMOUNTFOR\":"+ hr_collectPayBill.getFSETTLEPAYAMOUNTFOR() +"," +
                                "\"FHANDLINGCHARGEFOR\":0," +
                                "\"FACCOUNTID\":{\"FNumber\":\""+hr_collectPayBill.getFACCOUNTIDNumber()+"\"}," +
                                "\"FINNERACCOUNTID\":{\"FNUMBER\":\"\"}," +
                                "\"FOPPOSITECCOUNTNAME\":\"\"," +
                                "\"FOPPOSITEBANKNAME\":\"\"," +
                                "\"FCashAccount\":{\"FNUMBER\":\"\"}," +
                                "\"FOPPOSITEBANKACCOUNT\":\"\"," +
                                "\"FSETTLENO\":\"\"," +
                                "\"FCOMMENT\":\""+ hr_collectPayBill.getFCOMMENT() +"\"," +
                                "\"FPURCHASEORDERID\":0," +
                                "\"FOpenAddressRec\":\"\"," +
                                "\"FProvince\":{\"FNAME\":\"\"}," +
                                "\"FRecType\":\"\"," +
                                "\"FCity\":{\"FNAME\":\"\"}," +
                                "\"FCNAPS\":\"\"," +
                                "\"FDistrict\":{\"FNAME\":\"\"}," +
                                "\"FBankTypeRec\":{\"FNAME\":\"\"}," +
                                "\"FCOSTID\":{\"FNUMBER\":\"\"}," +
                                "\"FEXPENSEDEPTID_E\":{\"FNUMBER\":\"\"}," +
                                "\"FPAYAMOUNT_E\":"+ hr_collectPayBill.getFPAYAMOUNT_E() +"," +
                                "\"FPOSTDATE\":\""+ hr_collectPayBill.getFPOSTDATE() +"\"," +
                                "\"FMATERIALID\":{\"FNUMBER\":\"\"}," +
                                "\"FPRICEUNITID\":{\"FNumber\":\"\"}," +
                                "\"FPRICE\":0," +
                                "\"FPURCHASEORDERNO\":\"\"," +
                                "\"FMATERIALSEQ\":0," +
                                "\"FORDERENTRYID\":0," +
                                "\"FRuZhangType\":\"1\"," +
                                "\"FPayType\":\"\"," +
                                "\"FSwiftCode\":\"\"," +
                                "\"FTaxAmt\":0," +
                                "\"FNProvince\":\"\"," +
                                "\"FNCity\":\"\"," +
                                "\"FNDistrict\":\"\"," +
                                "\"FDistrictCode\":\"\"," +
                                "\"FBankDetail\":{\"FNUMBER\":\"\"}," +
                                "\"FCountry\":\"\"," +
                                "\"FSignature\":\"\"," +
                                "\"FGYSALEORDERNO\":\"\"," +
                                "\"FCertType\":\"\"," +
                                "\"FNOTVERIFICATEAMOUNT\":"+ hr_collectPayBill.getFNOTVERIFICATEAMOUNT() +"," +
                                "\"FCertNum\":\"\"," +
                                "\"FENTRYPREPAYORDERNO\":\"\"," +
                                "\"FBankInvoice\":\"false\"," +
                                "\"FNoteStatus\":\"\"," +
                                "\"FUnique\":0," +
                                "\"FTransacType\":\"\"," +
                                "\"FMessageInstruct\":\"\"," +
                                "\"FServiceLevel\":\"\"," +
                                "\"FPayMethod\":\"\"," +
                                "\"FCNYPayMethod\":\"\"," +
                                "\"FExContractNo\":\"\"," +
                                "\"FCollectionCurr\":{\"FNUMBER\":\"\"}," +
                                "\"FCollectionAmount\":0," +
                                "\"FReceiveBankBIC\":\"\"," +
                                "\"FReceiveBankCode\":\"\"," +
                                "\"FReceiveBranchCode\":\"\"," +
                                "\"FServiceChargeBear\":\"\"," +
                                "\"FServiceChargeNo\":{\"FNUMBER\":\"\"}," +
                                "\"FServiceChargeBank\":{\"FNUMBER\":\"\"}," +
                                "\"FServiceChargeCurr\":{\"FNUMBER\":\"\"}," +
                                "\"FCNAPSNum\":\"\"," +
                                "\"FCNYTransactType\":{\"FNUMBER\":\"\"}," +
                                "\"FRMBPayMethod\":\"\"," +
                                "\"FByAgentBank\":\"false\"," +
                                "\"FAgentBankAcnt\":{\"FNUMBER\":\"\"}," +
                                "\"FAgentBankName\":\"\"," +
                                "\"FAgentBankArea\":\"\"," +
                                "\"FAgentSWIFTCode\":\"\"," +
                                "\"FAgentBankAddress\":\"\"," +
                                "\"FReceiveBankClear\":\"\"," +
                                "\"FReceiveBranchClear\":\"\"," +
                                "\"FClearParameter\":\"\"," +
                                "\"FOtherPartyAdress\":\"\"," +
                                "\"FOtherPartyEmail\":\"\"," +
                                "\"FLinkRowId\":\"\"," +
                                "\"FOverseaPay\":\"false\"," +
                                "\"FUse\":\"\"," +
                                "\"FXDexchangeRate\":0," +
                                "\"FRELATEREFUNDAMOUNT\":0" +
                                "}";
                       if (hr_collectPayBill.getCollectPayDetailList().size() > 0 && hr_collectPayBill.getCollectPayDetailList().size() - 1 != i)
                       {
                           jsonData = jsonData + jsonDataT + ",";
                       }else{
                           jsonData = jsonData + jsonDataT;
                       }
                    }

                 jsonData = jsonData + "]}}";
        }
        try{
            //业务对象标识
            String formId = "";
            if (StringUtils.isNotBlank(hr_collectPayBill.getDimensionId()) && hr_collectPayBill.getDimensionId().equals("1"))
            {
                formId = "AR_RECEIVEBILL";
            }else if (StringUtils.isNotBlank(hr_collectPayBill.getDimensionId()) && hr_collectPayBill.getDimensionId().equals("2")){
                formId = "AP_PAYBILL";
            }
            //调用接口
            String resultJson = client.save(formId,jsonData);

            //用于记录结果
            Gson gson = new Gson();
            //对返回结果进行解析和校验
            RepoRet repoRet = gson.fromJson(resultJson, RepoRet.class);
            if (repoRet.getResult().getResponseStatus().isIsSuccess()) {
                System.out.printf("接口返回结果: %s%n", gson.toJson(repoRet.getResult()));
            } else {
                System.out.printf( "接口返回结果: " + gson.toJson(repoRet.getResult().getResponseStatus()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "success";
    }


    public static void main(String[] args) {
        //注意 1：此处不再使用参数形式传入用户名及密码等敏感信息，改为在登录配置文件中设置。
        //注意 2：必须先配置第三方系统登录授权信息后，再进行业务操作，详情参考各语言版本SDK介绍中的登录配置文件说明。
        //读取配置，初始化SDK
//        K3CloudApi client = new K3CloudApi();
//        //请求参数，要求为json字符串
//        String jsonData = "{\"FormId\":\"AR_RECEIVEBILL\",\"FieldKeys\":\"FSETTLEORGID,FBillNo,FDATE\",\"FilterString\":\"\",\"OrderString\":\"\",\"TopRowCount\":0,\"StartRow\":0,\"Limit\":5,\"SubSystemId\":\"\"}";
//
//        //组织机构
//        String jsonDataTwo = "{\"FormId\":\"ORG_Organizations\",\"FieldKeys\":\"FOrgID,FName\",\"FilterString\":\"\",\"OrderString\":\"\",\"TopRowCount\":0,\"StartRow\":0,\"Limit\":2000,\"SubSystemId\":\"\"}";
//
//        try {
//            //调用接口
//            String resultJson = String.valueOf(client.executeBillQuery(jsonData));
//            System.out.println("接口返回结果: " + resultJson);
//
//
//            String resultJsonTwo = String.valueOf(client.executeBillQuery(jsonDataTwo));
//            System.out.println("接口返回结果two: " + resultJsonTwo);
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        K3CloudApi client = new K3CloudApi();
//        //请求参数，要求为json字符串
//        String jsonData =
//                        "{\"NeedUpDateFields\":[]," +
//                        "\"NeedReturnFields\":[]," +
//                        "\"IsDeleteEntry\":\"true\"," +
//                        "\"SubSystemId\":\"\"," +
//                        "\"IsVerifyBaseDataField\":\"false\"," +
//                        "\"IsEntryBatchFill\":\"true\"," +
//                        "\"ValidateFlag\":\"true\"," +
//                        "\"NumberSearch\":\"true\"," +
//                        "\"IsAutoAdjustField\":\"true\"," +
//                        "\"InterationFlags\":\"\"," +
//                        "\"IgnoreInterationFlag\":\"\"," +
//                        "\"IsControlPrecision\":\"false\"," +
//                        "\"ValidateRepeatJson\":\"false\"," +
//                        "\"Model\":{\"FID\":0," +
//                        "\"FBillTypeID\":{\"FNUMBER\":\"SKDLX01_SYS\"}," +
//                        "\"FPAYORGID\":{\"FNumber\":\"105\"}," +
//                        "\"FSETTLEORGID\":{\"FNumber\":\"105\"}," +
//                        "\"FSALEORGID\":{\"FNumber\":\"105\"}," +
//                        "\"FDATE\":\"2025-02-24 00:00:00\"," +
//                        "\"FCONTACTUNITTYPE\":\"BD_Customer\"," +
//                        "\"FCONTACTUNIT\":{\"FNumber\":\"1.005\"}," +
//                        "\"FPAYUNITTYPE\":\"BD_Customer\"," +
//                        "\"FPAYUNIT\":{\"FNumber\":\"1.005\"}," +
//                        "\"FCURRENCYID\":{\"FNumber\":\"PRE001\"}," +
//                        "\"FEXCHANGERATE\":1.0," +
//                        "\"FSETTLECUR\":{\"FNUMBER\":\"PRE001\"}," +
//                        "\"FSETTLERATE\":1.0," +
//                        "\"FSALEDEPTID\":{\"FNumber\":\"04\"}," +
//                        "\"FSALEERID\":{\"FNumber\":\"\"}," +
////                        "\"FSALEDEPTID\":{\"FNumber\":\"\"}," +
////                        "\"FSALEGROUPID\":{\"FNumber\":\"\"}," +
////                        "\"FSALEERID\":{\"FNumber\":\"\"}," +
////                        "\"FDepartment\":{\"FNumber\":\"\"}," +
//                        "\"FISINIT\":\"false\"," +
//                        "\"FREMARK\":\"测试数据123\"," +
//
////                        "\"FREMARK\":\"\"," +
//                        "\"FISCARRYRATE\":\"false\"," +
//                        "\"FDOCUMENTSTATUS\":\"Z\"," +
//                        "\"FBUSINESSTYPE\":\"1\"," +
//                        "\"FCancelStatus\":\"A\"," +
//                        "\"FSETTLEMAINBOOKID\":{\"FNUMBER\":\"PRE001\"}," +
//
////                        "\"FSourceBillNumber\":\"\"," +
//                        "\"FRECEIVEBILLENTRY\":[" +
//                                "{\"FEntryID\":0," +
//                        "\"FSETTLETYPEID\":{\"FNumber\":\"JSFS01_SYS\"}," +
//                        "\"FPURPOSEID\":{\"FNumber\":\"SFKYT01_SYS\"}," +
////                        "\"FRECEIVEITEMTYPE\":\"\"," +
////                        "\"FRECEIVEITEM\":\"\"," +
//                        "\"FSaleOrderID\":0," +
//
//                        "\"FRECTOTALAMOUNTFOR\":9500.0," +
//                        "\"FRECAMOUNTFOR_E\":9500.0," +
//                        "\"FSETTLEDISTAMOUNTFOR\":0.0," +
//                        "\"FHANDLINGCHARGEFOR\":0.0," +
//                        "\"FOVERUNDERAMOUNTFOR\":0.0," +
//                        "\"FCOMMENT\":\"测试数据\"," +
//                        "\"FACCOUNTID\":{\"FNumber\":\"************\"}," +
////                        "\"FACCOUNTID\":{\"FNumber\":\"\"}," +
////                        "\"FOPPOSITEBANKACCOUNT\":\"\"," +
////                        "\"FOPPOSITECCOUNTNAME\":\"\"," +
////                        "\"FINNERACCOUNTID\":{\"FNUMBER\":\"\"}," +
////                        "\"FCashAccount\":{\"FNUMBER\":\"\"}," +
////                        "\"FSETTLENO\":\"\"," +
////                        "\"FOPPOSITEBANKNAME\":\"\"," +
////                        "\"FCOMMENT\":\"\"," +
////                        "\"FMATERIALID\":{\"FNUMBER\":\"\"}," +
////                        "\"FPRICEUNITID\":{\"FNumber\":\"\"}," +
////
////                        "\"FSALEORDERNO\":\"\"," +
//
//                        "\"FPrice\":0," +
//                        "\"FMATERIALSEQ\":0," +
//                        "\"FORDERENTRYID\":0," +
//                        "\"FQty\":0," +
////                        "\"FCOSTID\":{\"FNUMBER\":\"\"}," +
////                        "\"FCOSTDEPARTMENTID\":{\"FNUMBER\":\"\"}," +
////                        "\"FLinkRowId\":\"\"," +
//                        "\"FPOSTDATE\":\"2025-02-24 00:00:00\"," +
//                        "\"FENTRYTAXRATE\":0.0," +
//                        "\"FTAXAMOUNTFOR\":0.0," +
//                        "\"FTAXAMOUNT\":0.0," +
//                        "\"FRECNOTAXAMOUNTFOR\":9500.0," +
//                        "\"FRECNOTAXAMOUNT\":9500.0," +
//                        "\"FWRITTENOFFTAXAMOUNTFOR\":0.0," +
//                        "\"FWRITTENOFFTAXAMOUNT\":0.0," +
////                        "\"FASSSALESORDER\":[{\"FDetailID\":0}]}]," +
////                        "\"FRECEIVEBILLSRCENTRY\":[{\"FEntryID\":0," +
////                        "\"FSRCBILLTYPEID\":\"\"," +
////                        "\"FSRCBILLNO\":\"\"," +
////                        "\"FORDERBILLNO\":\"\"," +
////                        "\"FSRCMATERIALID\":{\"FNumber\":\"\"}," +
////                        "\"FSRCPRICEUNITID\":{\"FNumber\":\"\"}," +
////                        "\"FSRCMATERIALSEQ\":0," +
////                        "\"FSRCPRICE\":0," +
////                        "\"FSRCORDERENTRYID\":0," +
////                        "\"FSRCQTY\":0," +
////                        "\"FREALRECAMOUNTFOR_S\":0," +
////                        "\"FSRCCOSTID\":{\"FNUMBER\":\"\"}," +
////                        "\"FSRCCOSTDEPARTMENTID\":{\"FNUMBER\":\"\"}," +
////                        "\"FSrcLinkRowId\":\"\"," +
////                        "\"FSRCREMARK\":\"\"," +
////                        "\"FASSORDERAMOUNT\":0}]," +
////                        "\"FBILLRECEIVABLEENTRY\":[{\"FEntryID\":0," +
////                        "\"FInnerAccountID_B\":{\"FNUMBER\":\"\"}," +
////                        "\"FBILLID\":{\"FNumber\":\"\"}," +
////                        "\"FUSEDAMOUNTFOR\":0," +
////                        "\"FTempOrgId\":{\"FNumber\":\"\"}}]," +
////                        "\"FBILLSKDRECENTRY\":[{\"FEntryID\":0," +
////                        "\"FInnerActId\":{\"FNUMBER\":\"\"}," +
////                        "\"FReceivebleBillId\":{\"FNUMBER\":\"\"}," +
////                        "\"FKDBPARBILLNO\":\"\"," +
////                        "\"FPayPurse\":{\"FNUMBER\":\"\"}," +
////                        "\"FReturnAmount\":0," +
////                        "\"FReturnAmountStd\":0," +
////                        "\"FParAmount\":0," +
////                        "\"FPARAMOUNTSTD\":0," +
////                        "\"FBCONTACTUNITTYPE\":\"\"," +
////                        "\"FBCONTACTUNIT\":{\"FNumber\":\"\"}" +
//                                "}]}}";
//        try{
//
//            //业务对象标识
//            String formId = "AR_RECEIVEBILL";
//            //调用接口
//            String resultJson = client.save(formId,jsonData);
//
//            //用于记录结果
//            Gson gson = new Gson();
//            //对返回结果进行解析和校验
//            RepoRet repoRet = gson.fromJson(resultJson, RepoRet.class);
//            if (repoRet.getResult().getResponseStatus().isIsSuccess()) {
//                System.out.printf("接口返回结果: %s%n", gson.toJson(repoRet.getResult()));
//            } else {
////                "接口返回结果: " + gson.toJson(repoRet.getResult().getResponseStatus());
//            }
//        } catch (Exception e) {
////            fail(e.getMessage());
//        }



        //注意 1：此处不再使用参数形式传入用户名及密码等敏感信息，改为在登录配置文件中设置。
        //注意 2：必须先配置第三方系统登录授权信息后，再进行业务操作，详情参考各语言版本SDK介绍中的登录配置文件说明。
        //读取配置，初始化SDK
        K3CloudApi client = new K3CloudApi();
        //请求参数，要求为json字符串
        String jsonData = "{\"NeedUpDateFields\":[]," +
                "\"NeedReturnFields\":[]," +
                "\"IsDeleteEntry\":\"true\"," +
                "\"SubSystemId\":\"\"," +
                "\"IsVerifyBaseDataField\":\"false\"," +
                "\"IsEntryBatchFill\":\"true\"," +
                "\"ValidateFlag\":\"true\"," +
                "\"NumberSearch\":\"true\"," +
                "\"IsAutoAdjustField\":\"true\"," +
                "\"InterationFlags\":\"\"," +
                "\"IgnoreInterationFlag\":\"\"," +
                "\"IsControlPrecision\":\"false\"," +
                "\"ValidateRepeatJson\":\"false\"," +
                "\"Model\":{\"FID\":0," +

                "\"FBillTypeID\":{\"FNUMBER\":\"FKDLX10_SYS\"}," +
                "\"FPAYORGID\":{\"FNumber\":\"103\"}," +
                "\"FDATE\":\"2025-03-27 00:00:00\"," +
                "\"FCONTACTUNITTYPE\":\"FIN_OTHERS\"," +
                "\"FCURRENCYID\":{\"FNumber\":\"PRE001\"}," +
                "\"FCONTACTUNIT\":{\"FNumber\":\"01.02\"}," +
                "\"FSETTLEORGID\":{\"FNumber\":\"103\"}," +
                "\"FRECTUNITTYPE\":\"FIN_OTHERS\"," +



                "\"FPURCHASEORGID\":{\"FNumber\":\"103\"}," +
                "\"FRECTUNIT\":{\"FNumber\":\"01.02\"}," +
                "\"FEXCHANGERATE\":1.0," +
                "\"FSETTLECUR\":{\"FNUMBER\":\"PRE001\"}," +
                "\"FDOCUMENTSTATUS\":\"Z\"," +
                "\"FSETTLERATE\":1.0," +
                "\"FDepartment\":{\"FNumber\":\"\"}," +
                "\"FISINIT\":\"false\"," +
                "\"FPURCHASEDEPTID\":{\"FNumber\":\"01\"}," +


                "\"FPURCHASERGROUPID\":{\"FNumber\":\"\"}," +
                "\"FPURCHASERID\":{\"FNumber\":\"\"}," +
                "\"FScanPoint\":{\"FNUMBER\":\"\"}," +
                "\"FPRESETBASE1\":{\"FNUMBER\":\"\"}," +
                "\"FPRESETBASE2\":{\"FNUMBER\":\"\"}," +
                "\"FISSAMEORG\":\"false\"," +
                "\"FIsCredit\":\"false\"," +
                "\"FIsWriteOff\":\"false\"," +
                "\"FMatchMethodID\":0," +
                "\"FREALPAY\":\"false\"," +
                "\"FREMARK\":\"\"," +

                "\"FBookingDate\":\"1900-01-01\"," +


                "\"FTHIRDBILLNO\":\"\"," +
                "\"FWBSETTLENO\":\"\"," +
                "\"FISCARRYRATE\":\"false\"," +
                "\"FGYSHOPNAME\":\"\"," +
                "\"FGYCUSTOMERID\":{\"FNUMBER\":\"\"}," +
                "\"FMoreReceive\":\"false\"," +
                "\"FPRESETASSISTANT1\":{\"FNumber\":\"\"}," +
                "\"FPRESETASSISTANT2\":{\"FNumber\":\"\"}," +
                "\"FPRESETTEXT1\":\"\"," +
                "\"FPRESETTEXT2\":\"\"," +
                "\"FVirIsSameAcctOrg\":\"false\"," +
                "\"FBUSINESSTYPE\":\"3\"," +
                "\"FCancelStatus\":\"A\"," +

                "\"FSETTLEMAINBOOKID\":{\"FNUMBER\":\"PRE001\"}," +
                "\"FSHRID\":\"\"," +
                "\"FSourceBillNumber\":\"\"," +
                "\"FPAYBILLENTRY\":[{\"FEntryID\":0," +

                "\"FSETTLETYPEID\":{\"FNumber\":\"JSFS04_SYS\"}," +
                "\"FPURPOSEID\":{\"FNumber\":\"SF001\"}," +

                "\"FPAYITEMTYPE\":\"\"," +
                "\"FPAYITEM\":\"\"," +
                "\"FPAYAMOUNTFOR_E\":9500.0," +
                "\"FPAYTOTALAMOUNTFOR\":9500.0," +
                "\"FSETTLEDISTAMOUNTFOR\":0," +
                "\"FOVERSHORTAGEFOR\":0," +
                "\"FSETTLEPAYAMOUNTFOR\":9500.0," +
                "\"FHANDLINGCHARGEFOR\":0," +

                "\"FACCOUNTID\":{\"FNumber\":\"139369277000044074\"}," +


                "\"FINNERACCOUNTID\":{\"FNUMBER\":\"\"}," +
                "\"FOPPOSITECCOUNTNAME\":\"\"," +
                "\"FOPPOSITEBANKNAME\":\"\"," +
                "\"FCashAccount\":{\"FNUMBER\":\"\"}," +
                "\"FOPPOSITEBANKACCOUNT\":\"\"," +
                "\"FSETTLENO\":\"\"," +
                "\"FCOMMENT\":\"\"," +
                "\"FPURCHASEORDERID\":0," +
                "\"FOpenAddressRec\":\"\"," +
                "\"FProvince\":{\"FNAME\":\"\"}," +
                "\"FRecType\":\"\"," +
                "\"FCity\":{\"FNAME\":\"\"}," +
                "\"FCNAPS\":\"\"," +
                "\"FDistrict\":{\"FNAME\":\"\"}," +
                "\"FBankTypeRec\":{\"FNAME\":\"\"}," +
                "\"FCOSTID\":{\"FNUMBER\":\"\"}," +
                "\"FEXPENSEDEPTID_E\":{\"FNUMBER\":\"\"}," +


                "\"FPAYAMOUNT_E\":9500.0," +
                "\"FPOSTDATE\":\"2025-03-27 00:00:00\"," +

                "\"FMATERIALID\":{\"FNUMBER\":\"\"}," +
                "\"FPRICEUNITID\":{\"FNumber\":\"\"}," +
                "\"FPRICE\":0," +
                "\"FPURCHASEORDERNO\":\"\"," +
                "\"FMATERIALSEQ\":0," +
                "\"FORDERENTRYID\":0," +
                "\"FRuZhangType\":\"1\"," +
                "\"FPayType\":\"\"," +
                "\"FSwiftCode\":\"\"," +
                "\"FTaxAmt\":0," +
                "\"FNProvince\":\"\"," +
                "\"FNCity\":\"\"," +
                "\"FNDistrict\":\"\"," +
                "\"FDistrictCode\":\"\"," +
                "\"FBankDetail\":{\"FNUMBER\":\"\"}," +
                "\"FCountry\":\"\"," +
                "\"FSignature\":\"\"," +
                "\"FGYSALEORDERNO\":\"\"," +
                "\"FCertType\":\"\"," +
                "\"FNOTVERIFICATEAMOUNT\":9500.0," +
                "\"FCertNum\":\"\"," +
                "\"FENTRYPREPAYORDERNO\":\"\"," +
                "\"FBankInvoice\":\"false\"," +
                "\"FNoteStatus\":\"\"," +
                "\"FUnique\":0," +
                "\"FTransacType\":\"\"," +
                "\"FMessageInstruct\":\"\"," +
                "\"FServiceLevel\":\"\"," +
                "\"FPayMethod\":\"\"," +
                "\"FCNYPayMethod\":\"\"," +
                "\"FExContractNo\":\"\"," +
                "\"FCollectionCurr\":{\"FNUMBER\":\"\"}," +
                "\"FCollectionAmount\":0," +
                "\"FReceiveBankBIC\":\"\"," +
                "\"FReceiveBankCode\":\"\"," +
                "\"FReceiveBranchCode\":\"\"," +
                "\"FServiceChargeBear\":\"\"," +
                "\"FServiceChargeNo\":{\"FNUMBER\":\"\"}," +
                "\"FServiceChargeBank\":{\"FNUMBER\":\"\"}," +
                "\"FServiceChargeCurr\":{\"FNUMBER\":\"\"}," +
                "\"FCNAPSNum\":\"\"," +
                "\"FCNYTransactType\":{\"FNUMBER\":\"\"}," +
                "\"FRMBPayMethod\":\"\"," +
                "\"FByAgentBank\":\"false\"," +
                "\"FAgentBankAcnt\":{\"FNUMBER\":\"\"}," +
                "\"FAgentBankName\":\"\"," +
                "\"FAgentBankArea\":\"\"," +
                "\"FAgentSWIFTCode\":\"\"," +
                "\"FAgentBankAddress\":\"\"," +
                "\"FReceiveBankClear\":\"\"," +
                "\"FReceiveBranchClear\":\"\"," +
                "\"FClearParameter\":\"\"," +
                "\"FOtherPartyAdress\":\"\"," +
                "\"FOtherPartyEmail\":\"\"," +
                "\"FLinkRowId\":\"\"," +
                "\"FOverseaPay\":\"false\"," +
                "\"FUse\":\"\"," +
                "\"FXDexchangeRate\":0," +
                "\"FRELATEREFUNDAMOUNT\":0," +
                "\"FASSPURCHASEORDER\":[{\"FDetailID\":0," +
                "\"FASSPURORDERNO\":\"\"," +
                "\"FASSMATERIALID\":{\"FNUMBER\":\"\"}," +
                "\"FASSORDERENTRYID\":0}]}]," +
                "\"FPAYBILLSRCENTRY\":[{\"FEntryID\":0," +
                "\"FSOURCETYPE\":\"\"," +
                "\"FSRCBILLNO\":\"\"," +
                "\"FTAXAMOUNT\":0," +
                "\"FPAYPURPOSEID\":{\"FNUMBER\":\"\"}," +
                "\"FSRCCOSTID\":{\"FNUMBER\":\"\"}," +
                "\"FEXPENSEDEPTID\":{\"FNUMBER\":\"\"}," +
                "\"FSRCPRICEUNITID\":{\"FNumber\":\"\"}," +
                "\"FSRCPRICE\":0," +
                "\"FSRCQTY\":0," +
                "\"FTAXAMOUNTLOC\":0," +
                "\"FPLANPAYAMOUNTLOC\":0," +
                "\"FSRCREMARK\":\"\"," +
                "\"FSrcLinkRowId\":\"\"," +
                "\"FSETTLEAMOUNT\":0," +
                "\"FREALPAYAMOUNTLOC\":0}]," +
                "\"FBILLPAYABLEENTRY\":[{\"FEntryID\":0," +
                "\"FINNERACCOUNTID_B\":{\"FNUMBER\":\"\"}," +
                "\"FBILLID\":{\"FNumber\":\"\"}," +
                "\"FUSEDAMOUNTFOR\":0," +
                "\"FBPPARAMOUNT\":0," +
                "\"FPARLEFTAMOUNT\":0," +
                "\"FUSEDAMOUNT\":0," +
                "\"FTempOrgId\":{\"FNumber\":\"\"}}]," +
                "\"FBILLRECEIVABLEENTRY\":[{\"FEntryID\":0," +
                "\"FInnerActId\":{\"FNUMBER\":\"\"}," +
                "\"FReceivebleBillId\":{\"FNUMBER\":\"\"}," +
                "\"FPayPurse\":{\"FNUMBER\":\"\"}," +
                "\"FReturnAmount\":0," +
                "\"FParAmount\":0," +
                "\"FReturnAmountStd\":0," +
                "\"FPARAMOUNTSTD\":0," +
                "\"FBCONTACTUNITTYPE\":\"\"," +
                "\"FBCONTACTUNIT\":{\"FNumber\":\"\"}," +
                "\"FIsEndorse\":0," +
                "\"FBankEndorse\":\"false\"," +
                "\"FEndorseeNo\":\"\"," +
                "\"FOppBankName\":\"\"," +
                "\"FEndorseeCnapsCode\":\"\"}]," +
                "\"FPAYBILLMORERECENTRY\":[{\"FEntryID\":0," +
                "\"FSETTLETYPEID_M\":{\"FNUMBER\":\"\"}," +
                "\"FRECTUNIT_M\":{\"FNumber\":\"\"}," +
                "\"FPAYAMOUNTFOR_M\":0," +
                "\"FPayAmount_M\":0," +
                "\"FACCOUNTID_M\":{\"FNUMBER\":\"\"}," +
                "\"FCOUNTRY_M\":\"\"," +
                "\"FNPROVINCE_M\":\"\"," +
                "\"FNCITY_M\":\"\"," +
                "\"FNDISTRICT_M\":\"\"," +
                "\"FBANKDETAIL_M\":{\"FNUMBER\":\"\"}," +
                "\"FOPPOSITEBANKNAME_M\":\"\"," +
                "\"FOPENADDRESSREC_M\":\"\"," +
                "\"FCNAPS_M\":\"\"," +
                "\"FOPPOSITEBANKACCOUNT_M\":\"\"," +
                "\"FOPPOSITECCOUNTNAME_M\":\"\"," +
                "\"FCOMMENT_M\":\"\"," +
                "\"FSWIFTCODE_M\":\"\"," +
                "\"FRECTYPE_M\":\"\"," +
                "\"FRUZHANGTYPE_M\":\"\"," +
                "\"FPAYTYPE_M\":\"A\"," +
                "\"FUrgentSign_M\":\"\"," +
                "\"FURGENT_M\":\"false\"," +
                "\"FDistrictCode_M\":\"\"," +
                "\"FCertType_M\":\"\"," +
                "\"FCERTNUM_M\":\"\"," +
                "\"FSHRENTRYID\":\"\"}]}}";
        try{
            //业务对象标识
            String formId = "AP_PAYBILL";
            //调用接口
            String resultJson = client.save(formId,jsonData);

            //用于记录结果
            Gson gson = new Gson();
            //对返回结果进行解析和校验
            RepoRet repoRet = gson.fromJson(resultJson, RepoRet.class);
            if (repoRet.getResult().getResponseStatus().isIsSuccess()) {
                System.out.printf("接口返回结果: %s%n", gson.toJson(repoRet.getResult()));
            } else {
                System.out.printf("接口返回结果: " + gson.toJson(repoRet.getResult().getResponseStatus()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}
