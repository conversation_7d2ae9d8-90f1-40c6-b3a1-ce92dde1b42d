<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.crm.dao.BusinessRatesDao">
	
	<insert id="insert" keyProperty="recordId" useGeneratedKeys="true">
		INSERT INTO md_business_rates
		(
			companyId,
			customerId,
			rates,
			status,
			auditDate,
			activeFlag,
			createdBy,
			createdDate,
			remark
		)VALUES(
			#{companyId},
			#{customerId},
			#{rates},
			#{status},
			#{auditDate},
			1,
			#{createdBy.recordId},
			#{createdDate},
			#{remark}
		)
	</insert>
	
	<update id="update">
		UPDATE md_business_rates SET
			customerId = #{customerId},
			rates = #{rates},
			remark = #{remark},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateStatus">
		UPDATE md_business_rates SET
			status = #{status},
			<if test="auditDate != null and auditDate != ''">
				auditDate = #{auditDate},
			</if>
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE md_business_rates SET
			activeFlag = 2
		WHERE recordId = #{recordId}
	</update>
	
	<select id="get" resultType="BusinessRates">
		SELECT
			*
		FROM md_business_rates
		WHERE recordId = #{recordId}
	</select>
	
	<select id="findList" resultType="BusinessRates">
		SELECT
			a.recordId,
			a.customerId,
			b.no AS "customer.no",
			b.shortName AS "customer.shortName",
			a.rates,
			a.status,
			a.auditDate,
			c.userName AS "createdBy.userName",
			a.createdDate,
			a.remark
		FROM md_business_rates a
		LEFT JOIN md_customer b ON b.recordId = a.customerId
		LEFT JOIN sm_user c ON c.recordId = a.createdBy
		WHERE a.companyId = #{companyId} AND a.activeFlag = 1
		<if test="customer != null">
			<if test="customer.no != null and customer.no != ''">
				AND REPLACE(b.no," ","") LIKE CONCAT('%',REPLACE(#{customer.no}," ",""),'%')
			</if>
			<if test="customer.shortName != null and customer.shortName != ''">
				AND REPLACE(b.shortName," ","") LIKE CONCAT('%',REPLACE(#{customer.shortName}," ",""),'%')
			</if>
		</if>
		<if test="status != null and status != ''">
			AND a.status = #{status}
		</if>
		<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
			AND a.createdBy = #{createdBy.recordId}
		</if>
		<if test="startTime != null and startTime != ''">
			AND a.createdDate >= #{startTime}
		</if>
		<if test="endTime != null and endTime != ''">
			AND #{endTime} > a.createdDate
		</if>
		ORDER BY a.createdDate DESC
	</select>
	
	<select id="checkCount" resultType="Integer">
		SELECT
			COUNT(1)
		FROM md_business_rates 
		WHERE companyId = #{companyId} AND activeFlag = 1
		AND customerId = #{customerId}
		<if test="recordId != null and recordId != ''">
			AND recordId <![CDATA[<>]]> #{recordId}
		</if>
	</select>
	
	<insert id="insertBatchTable" keyProperty="recordId" useGeneratedKeys="true">
		INSERT INTO md_approval_batchTable
		(
			companyId,
			type,
			ids,
			activeFlag,
			createdBy,
			createdDate
		)VALUES(
			#{companyId},
			#{type},
			#{ids},
			1,
			#{createdBy.recordId},
			#{createdDate}
		)
	</insert>
	
	<select id="checkStatusCount" resultType="Integer">
		SELECT
			COUNT(1)
		FROM md_business_rates
		WHERE FIND_IN_SET(recordId,#{ids}) AND activeFlag = 1
		AND status = 60001
	</select>
	
	<update id="batchUpdateStatus">
		UPDATE md_business_rates SET
			status = #{status}
		WHERE FIND_IN_SET(recordId,#{ids}) AND activeFlag = 1
	</update>
	
	<select id="getBatchTable" resultType="BatchTable">
		SELECT
			*
		FROM md_approval_batchTable
		WHERE recordId = #{recordId}
	</select>
	
	<update id="batchUpdateData">
		<foreach collection="backupTwoList" item="item" index="index" separator=";">
			UPDATE md_business_rates SET
				status = #{status},
				auditDate = #{auditDate},
				rates = #{item.salePrecent},
				lastUpdBy = #{lastUpdBy.recordId},
				lastUpdDate = #{lastUpdDate},
				remark = #{item.remark}
			WHERE recordId = #{item.batchId}
		</foreach>
	</update>
	
	<update id="updateCustomerSalePrecent">
		UPDATE md_customer aa
		INNER JOIN
		(
			SELECT
				b.recordId AS "recordId",
				a.rates AS "salePrecent"
			FROM md_business_rates a
			LEFT JOIN md_customer b ON b.recordId = a.customerId
			WHERE FIND_IN_SET(a.recordId,#{recordId}) AND a.activeFlag = 1
		) bb ON bb.recordId = aa.recordId
		SET aa.salePrecent = bb.salePrecent
	</update>
	
	<select id="getShowBusinessRates" resultType="BusinessRates">
		SELECT
			c.`no` AS "customer.no",
			c.shortName AS "customer.shortName",
			a.salePrecent AS "rates",
			a.remark AS "remark"
		FROM md_approval_backup_two a
		LEFT JOIN md_business_rates b ON b.recordId = a.batchId
		LEFT JOIN md_customer c ON c.recordId = b.customerId
		WHERE FIND_IN_SET(a.recordId,#{backups.recordId}) AND a.activeFlag = 1
	</select>
</mapper>