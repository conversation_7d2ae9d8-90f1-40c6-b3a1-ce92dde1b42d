package com.kyb.pcberp.modules.stock.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.approval.entity.Approval;
import com.kyb.pcberp.modules.stock.entity.MaterialAttachements;
import com.kyb.pcberp.modules.sys.entity.Company;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface MaterialAttachementsDao extends CrudDao<MaterialAttachements>
{
    Integer getAuditFileCount(@Param("auditId") String auditId, @Param("orgFileName") String orgFileName);

    void uploadFile(MaterialAttachements materialAttachements);

    List<MaterialAttachements> getMaterialAttachementsList(@Param("company")Company company,@Param("approvalList") List<Approval> approvalList);
}