<%@ page contentType="text/html;charset=UTF-8" %>
<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">统计报表</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="report.complaintReport">客诉考核财务报表</a>
        </li>
    </ul>
</div>
<!-- END 导航-->
<tabset class="tabset-margin-top">
	<tab heading="客诉考核财务报表" active="ctrl.tabs.viewForm.active">
		<div class="portlet light bordered">
				<div class="actions">
					<div class="row">
						<div class="col-md-12 text-right" ng-if="!ctrl.newVersionId || ctrl.newVersionId == ctrl.versionId">
							<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.payOpen()">
								<i class="fa fa-adjust font-blue"></i>结账
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="panel panel-default">
			<div class="panel-heading font-blue-hoki">查询</div>
			<div class="panel-body">
				<form class="form-horizontal">
					<div class="row">
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">月份：</label>
								<div class="col-sm-7 col-md-8">
									<ui-select register-custom-form-control ng-model="ctrl.query.applyDate" theme="bootstrap">
										<ui-select-match placeholder="月份">{{$select.selected}}</ui-select-match>
										<ui-select-choices repeat="option in ctrl.periodList | filter: $select.search">
											<div ng-show="false" ng-bind-html="option | highlight: $select.search"></div>
											<small><span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{option}}<br></span></small>
										</ui-select-choices>
									</ui-select>
								</div>
							</div>
						</div>
						<div class="col-md-6 col-lg-4">
							<div class="form-group">
								<label class="col-sm-3 col-md-4 control-label">部门：</label>
								<div class="col-sm-7 col-md-8">
									<input type="text" class="form-control" ng-model="ctrl.query.departName" disable-auto-validate="true" />
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-12">
							<button class="btn btn-default btn-default-width pull-right"
									ng-click="ctrl.loadReport()"><i class="fa fa-search"></i> 查询
							</button>
						</div>
					</div>
				</form>
			</div>
		</div>
		<div class="panel panel-default">
			<div class="panel-body">
				<span>
					当前客诉率：{{ctrl.complaintRate}}&nbsp;&nbsp;奖惩金额 =<%--		{{ctrl.penaltyAmount}}--%>（客诉笔数/出货笔数-目标值/增长值*平米惩罚单价*客诉送货面积）&nbsp;&nbsp;客诉数：{{ctrl.complaintRateNumber}}&nbsp;&nbsp;送货批次数：{{ctrl.deliveryNumber}}
				</span>
			</div>
		</div>
		<div class="panel panel-default">
			<div class="panel-body">
				<span>目标:{{ctrl.complaintSet.targetValue}}%(含)以内 考核:每超{{ctrl.complaintSet.exceedTarget}}，减{{ctrl.complaintSet.punish}}元/平米<br></span>
					<span>2023年第一季度先收集收据，暂不考核，微信、口头、邮件等所有客诉全部录入系统，均为客诉</span>
			</div>
		</div>
		<div class="portlet light bordered">
			<div class="portlet-title">
				<div class="caption font-blue-hoki">汇总列表</div>
				<div id="InvenMasterStep2" class="actions">
					<div class="portlet-input input-inline input-small">
						<form action="a/report/reportComplaint/exportSummaryList" method="post" enctype="multipart/form-data" target="hidden_frame">
							<input type="text" ng-show="false" name="departName" value="{{ctrl.query.departName}}" />
							<input type="text" ng-show="false" name="applyDate" value="{{ctrl.query.applyDate}}" />
							<button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出</button>
						</form>
					</div>
				</div>
			</div>
			<div class="portlet-body">
				<div class="row">
					<div class="table-scrollable">
						<table class="table table-striped table-bordered table-condensed table-advance table-hover">
							<thead>
							<tr class="heading">
								<th style="text-align: center;">部门</th>
								<th style="text-align: center;">客诉笔数</th>
								<th style="text-align: center;">出货笔数</th>
								<th style="text-align: center;">客诉率</th>
								<th style="text-align: center;">惩罚单价</th>
								<th style="text-align: center;">面积</th>
								<th style="text-align: center;">奖惩金额</th>
								<th style="text-align: center;">账期月份</th>
							</tr>
							</thead>
							<tbody>
							<tr ng-repeat="row in ctrl.summaryList">
								<td style="text-align: center;" ng-bind="row.departName"></td>
								<td style="text-align: center;" ng-bind="row.complaintRateSize"></td>
								<td style="text-align: center;" ng-bind="row.deliverySize"></td>
								<td style="text-align: center;" ng-bind="row.complaintRate"></td>
								<td style="text-align: center;" ng-bind="row.penaltyPrice"></td>
								<td style="text-align: center;" ng-bind="row.area"></td>
								<td style="text-align: center;" ng-bind="row.penaltyAmount"></td>
								<td style="text-align: center;" ng-bind="row.periodTime"></td>
							</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
		<div class="portlet light bordered">
			<div class="portlet-title">
				<div class="caption font-blue-hoki">客诉率列表</div>
				<div id="InvenMasterStep2" class="actions">
					<div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit && ctrl.confirmFlag && ctrl.defaultWay == 1 && ctrl.ValiteCompanyUtil.valiteSaleCompany">
						<button type="button" class="btn green btn-default-width" ng-click="ctrl.checkStatusDataOpen()"><i class="fa fa-check font-green"></i>确认</button>
					</div>
					<div class="portlet-input input-inline input-small">
						<form action="a/report/reportComplaint/exportCustomerComplaintRate" method="post" enctype="multipart/form-data" target="hidden_frame">
							<input type="text" ng-show="false" name="departName" value="{{ctrl.query.departName}}" />
							<input type="text" ng-show="false" name="applyDate" value="{{ctrl.query.applyDate}}" />
							<button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出</button>
						</form>
					</div>
				</div>
			</div>

			<div class="portlet-body">
				<div class="row">
					<div class="table-scrollable">
						<table class="table table-striped table-bordered table-condensed table-advance table-hover">
							<thead>
							<tr class="heading">
								<th style="text-align: center;">客诉单号</th>
								<th style="text-align: center;">合同号</th>
								<th style="text-align: center;">生产编号</th>
								<th style="text-align: center;">客户型号</th>
								<th style="text-align: center;">部门</th>
								<th style="text-align: center;">销售公司</th>
								<th style="text-align: center;">业务员</th>
								<th style="text-align: center;">客诉数量</th>
								<th style="text-align: center;">补发数量</th>
								<th style="text-align: center;">账期月份</th>
							</tr>
							<tr>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.noQuery1">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.contractNoQuery1">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.materialNoQuery1">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.customerModelQuery1">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.departNameQuery1">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.saleComNameQuery1">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.salesmanNameQuery1">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.quantityQuery1">
								</td>

							</tr>
							</thead>
							<tbody>
							<tr ng-repeat="row in ctrl.complaintRateList |
									filter:{
										'no':ctrl.noQuery1,
										'contractNo':ctrl.contractNoQuery1,
										'customerModel':ctrl.customerModelQuery1,
										'departName':ctrl.departNameQuery1,
										'saleComName':ctrl.saleComNameQuery1,
										'salesmanName':ctrl.salesmanNameQuery1,
										'quantity':ctrl.quantityQuery1,
										'customerModel':ctrl.customerModelQuery1,
										'applyDate':ctrl.applyDateQuery1}">
								<td style="text-align: center;" ng-bind="row.no"></td>
                                <td style="text-align: center;" ng-bind="row.contractNo"></td>
								<td style="text-align: center;" ng-bind="row.materialNo"></td>
								<td style="text-align: center;" ng-bind="row.customerModel"></td>
								<td style="text-align: center;" ng-bind="row.departName"></td>
								<td style="text-align: center;" ng-bind="row.saleComName"></td>
								<td style="text-align: center;" ng-bind="row.salesmanName"></td>
								<td style="text-align: center;" ng-bind="row.quantity"></td>
								<td style="text-align: center;" ng-bind="row.fedNum"></td>
								<td style="text-align: center;" ng-bind="row.applyDate"></td>
							</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
		<div class="portlet light bordered">
			<div class="portlet-title">
				<div class="caption font-blue-hoki">送货批次列表</div>
				<div id="InvenMasterStep2" class="actions">
					<div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit && ctrl.confirmFlag && ctrl.defaultWay == 1 && ctrl.ValiteCompanyUtil.valiteSaleCompany">
						<button type="button" class="btn green btn-default-width" ng-click="ctrl.checkStatusDataOpen()"><i class="fa fa-check font-green"></i>确认</button>
					</div>
					<div class="portlet-input input-inline input-small">
						<form action="a/report/reportComplaint/exportDelivery" method="post" enctype="multipart/form-data" target="hidden_frame">
							<input type="text" ng-show="false" name="departName" value="{{ctrl.query.departName}}" />
							<input type="text" ng-show="false" name="applyDate" value="{{ctrl.query.applyDate}}" />
							<button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出</button>
						</form>
					</div>
				</div>
			</div>

			<div class="portlet-body">
				<div class="row">
					<div class="table-scrollable">
						<table class="table table-striped table-bordered table-condensed table-advance table-hover">
							<thead>
							<tr class="heading">
								<th style="text-align: center;" >合同号</th>
								<th style="text-align: center;" >生产编号</th>
								<th style="text-align: center;" >客户型号</th>
								<th style="text-align: center;" >部门</th>
								<th style="text-align: center;" >销售公司</th>
								<th style="text-align: center;" >业务员</th>
								<th style="text-align: center;" >订单数量</th>
								<th style="text-align: center;" >送货数量</th>
								<th style="text-align: center;" >送货时间</th>
								<th style="text-align: center;" >账期月份</th>
							</tr>
							<tr>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.contractNoQuery2">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.craftNoQuery2">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.customerModelQuery2">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.departNameQuery2">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.saleComNameQuery2">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.salesmanNameQuery2">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.orderQuantityQuery2">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.sendQuantityQuery2">
								</td>
								<td>
									<input type="text" class="form-control" ng-model="ctrl.sentTimeQuery2">
								</td>
							</tr>
							</thead>
							<tbody>
							<tr ng-repeat="row in ctrl.deliveryList |
									filter:{
										'contractNo':ctrl.contractNoQuery2,
										'craftNo':ctrl.craftNoQuery2,
										'customerModel':ctrl.customerModelQuery2,
										'departName':ctrl.departNameQuery2,
										'saleComName':ctrl.saleComNameQuery2,
										'salesmanName':ctrl.salesmanNameQuery2,
										'orderQuantity':ctrl.orderQuantityQuery2,
										'sendQuantity':ctrl.sendQuantityQuery2,
										'sentTime':ctrl.sentTimeQuery2}">
								<td style="text-align: center;" ng-bind="row.contractNo"></td>
								<td style="text-align: center;" ng-bind="row.craftNo"></td>
								<td style="text-align: center;" ng-bind="row.customerModel"></td>
								<td style="text-align: center;" ng-bind="row.departName"></td>
								<td style="text-align: center;" ng-bind="row.saleComName"></td>
								<td style="text-align: center;" ng-bind="row.salesmanName"></td>
								<td style="text-align: center;" ng-bind="row.orderQuantity"></td>
								<td style="text-align: center;" ng-bind="row.sendQuantity"></td>
								<td style="text-align: center;" ng-bind="row.sentTime"></td>
								<td style="text-align: center;" ng-bind="row.periodTime"></td>
							</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</tab>
	<tab heading="客诉考核规则设置" active="ctrl.tabs.setForm.active" ng-click="ctrl.loadVersion()">
		<div class="portlet light bordered">
			<div class="portlet-title">
				<div class="caption" ng-if="ctrl.versionList && ctrl.versionList.length > 0">
					<div class="row">
						<div class="col-md-12">
							<select class="form-control"
									ng-model="ctrl.versionId"
									ng-change="ctrl.loadComplaintRecord()"
									ng-options="gen.recordId as gen.versionDate for gen in ctrl.versionList">
							</select>
						</div>
					</div>
				</div>
				<div class="actions">
					<div class="row">
						<div class="col-md-6 text-right" ng-if="!ctrl.newVersionId || ctrl.newVersionId == ctrl.versionId">
							<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.createVersion()">
								<i class="fa fa-adjust font-blue"></i>创建新版本
							</a>
						</div>
						<div class="col-md-6 text-right" ng-if="ctrl.newVersionId == ctrl.versionId && ctrl.useFlag!=1">
							<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.complaintRecordSave()"><i class="fa fa-save font-green"></i>保存</a>
						</div>
					</div>
				</div>
			</div>
			<div class="panel panel-default">
				<div class="panel-heading font-blue-hoki">客诉考核率目标值</div>
				<div class="panel-body" id="stepDetail1">
					<form class="form-horizontal" name="editForm">
						<div class="row">
							<div class="col-md-8 col-lg-8">
								<div class="form-group">
									<label class="col-sm-4 col-md-4 control-label text-right">客诉考核目标值(%)：</label>
									<div class="col-sm-6 col-md-8">
										<input class="form-control" ng-disabled="ctrl.newVersionId != ctrl.versionId || ctrl.useFlag==1" type="number" ng-model="ctrl.complaintSet.targetValue">
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-md-8 col-lg-8">
								<div class="form-group">
									<label class="col-sm-4 col-md-4 control-label text-right">客诉率每增加(%)：</label>
									<div class="col-sm-6 col-md-8">
										<input class="form-control" ng-disabled="ctrl.newVersionId != ctrl.versionId || ctrl.useFlag==1" type="number" ng-model="ctrl.complaintSet.exceedTarget">
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-md-8 col-lg-8">
								<div class="form-group">
									<label class="col-sm-4 col-md-4 control-label text-right">超出目标值每平米惩罚(元/平米)：</label>
									<div class="col-sm-6 col-md-8">
										<input class="form-control" ng-disabled="ctrl.newVersionId != ctrl.versionId || ctrl.useFlag==1" type="number" ng-model="ctrl.complaintSet.punish">
									</div>
								</div>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</tab>
</tabset>

<div class="row">
	<div class="col-md-12">
		<div id="addCheckoutStatic" class="modal fade" tabindex="-1" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">提示</h4>
					</div>
					<div class="modal-body">
						<p><span ng-bind="ctrl.message"></span></p>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn btn-default" ng-click="ctrl.pay()">确定</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true"></button>
				<h4 class="modal-title"><span>提示</span></h4>
			</div>
			<div class="modal-body">
				<p>
					<span ng-bind="ctrl.message"></span>
				</p>
			</div>
			<div class="modal-footer">
				<button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
			</div>
		</div>
	</div>
</div>
