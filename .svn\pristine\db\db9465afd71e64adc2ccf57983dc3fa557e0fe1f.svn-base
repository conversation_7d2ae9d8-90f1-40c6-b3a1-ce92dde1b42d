<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.contract.dao.IcloudGroupBargainingRecordDao">
	<insert id="insert">
		INSERT INTO icloud_group_bargaining_record
		(
			groupCenterId,
			rawMaterialCost,
			originalProcessingFee,
			activeFlag,
			createdBy,
			createdDate
		) VALUES
		(
			#{groupCenterId},
			#{rawMaterialCost},
			#{originalProcessingFee},
			1,
			#{createdById},
			NOW()
		)
	</insert>

	<update id="update">
		UPDATE icloud_group_bargaining_record
		SET
		    activeFlag = 0,
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE groupCenterId = #{groupCenterId}
	</update>
	
</mapper>