/* Setup general page controller */
kybApp.controller('capacityReportCtrl', [ '$rootScope', '$scope', 'upida', '$filter','$timeout', 'CommonUtil','BaseUtil','$http', function ($rootScope, $scope, upida, $filter,$timeout, CommonUtil,BaseUtil,$http)
{
	$scope.$on('$viewContentLoaded', function ()
	{
		// initialize core components
		MainCtrl.initAjax();
		// set default layout mode
		$rootScope.settings.layout.pageBodySolid = false;
		$rootScope.settings.layout.pageSidebarClosed = false;
	});
	var vm = this;
	// tabs控制
	vm.tabs = {
		viewForm: {active: true},
		setForm: {active: false}
	};

	// 时间范围的选项
	vm.rangeOptions = {
		// format: "YYYY-MM-DD",
		startDate: new Date((new Date).setMonth(((new Date).getMonth() - 1))),
		minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
	};

	// 时间范围的Model
	vm.time = {
		start: {},
		end: {}
	};

	vm.initDate = function(date) {
		if(date == "") {
			vm.rangeOptions = {
				// format: "YYYY-MM-DD",
				startDate: new Date(vm.query.sentTimeStartQr),
				minDate: new Date(new Date(vm.query.sentTimeEndQr).setFullYear(new Date(vm.query.sentTimeEndQr).getFullYear() - 5))
			};
			vm.time = {
				start: vm.rangeOptions.startDate,
				end: vm.rangeOptions.minDate
			}
		}
	};


	vm.query = {};
	vm.query.section = "1";
	vm.query.sentTimeStartQr = "";
	vm.query.sentTimeEndQr = "";

	vm.loadData = function ()
	{
		MainCtrl.blockUI({
			animate: true,
		});
		// 获取所有年月
		upida.get("report/reportCapacity/load/data").then(function (data) {
			vm.deptList = data.deptList;
			let obj = {groupOrgId:null,groupOrgName:'所有'};
			vm.deptList.splice(0,0,obj);
			vm.periodList = data.periodList;
			vm.query.processValueId = "0";
			vm.query.yearMonth = data.period;
			vm.loadReport();
			MainCtrl.unblockUI();
		});
	}

	vm.selectSection = function()
	{
		$timeout(function(){
			if(vm.query.section == '2')
			{
				if(vm.time.start) {
					vm.query.sentTimeStartQr = vm.time.start.valueOf();
				}
				if(vm.time.end) {
					vm.query.sentTimeEndQr = vm.time.end.valueOf();
				}
			}
		});
	}

	vm.reportList = [];
	vm.discountAmountSum = 0;
	vm.punishAmountSum = 0;
	vm.reconciliationAmountSum = 0;
	vm.loadReport = function(){
		if(vm.query.section == '2')
		{
			if(vm.time.start) {
				vm.query.sentTimeStartQr = vm.time.start.valueOf();
			}
			if(vm.time.end) {
				vm.query.sentTimeEndQr = vm.time.end.valueOf();
			}
		}
		vm.discountAmountSum = 0;
		vm.punishAmountSum = 0;
		vm.reconciliationAmountSum = 0;
		upida.post("report/reportCapacity/reportList",vm.query).then(function (result) {
			vm.reportList = result;
			if(vm.reportList.length>0){
				for (let item of vm.reportList) {
					if(item.discountAmount){
						vm.discountAmountSum+=item.discountAmount;
					}
					if(item.punishAmount){
						vm.punishAmountSum+=item.punishAmount;
					}
					if(item.reconciliationAmount){
						vm.reconciliationAmountSum+=item.reconciliationAmount;
					}
				}
				vm.discountAmountSum = Math.round(vm.discountAmountSum*100)/100.0;
				vm.punishAmountSum = Math.round(vm.punishAmountSum*100)/100.0;
				vm.reconciliationAmountSum = Math.round(vm.reconciliationAmountSum*100)/100.0;
			}
		});
	}

	vm.loadSet = function()
	{
		vm.loadVersion();
	}

	vm.versionList = [];
	vm.versionId = '';
	vm.useFlag = 0;
	vm.newVersionId = '';
	vm.loadVersion = function(){
		upida.get("report/reportCapacity/versionList").then(function (result) {
			vm.versionList = result;
			if(vm.versionList.length>0)
			{
				vm.useFlag = vm.versionList[0].useFlag;
				vm.versionId = vm.versionList[0].recordId;
				vm.newVersionId = vm.versionList[0].recordId;
				vm.loadCapacityRecord();
			}
		});
	}

	vm.createVersion = function(){
		upida.get("report/reportCapacity/createVersion").then(function (result) {
			if(result=="success"){
				vm.loadVersion();
				vm.message = "创建成功!";
				$('#static').modal();
			}else{
				vm.message = "创建失败!";
				$('#static').modal();
			}
		});
	}

	vm.capacitySet = {};
	vm.loadCapacityRecord = function(){
		for (let i = 0; i < vm.versionList.length; i++) {
			if(vm.versionList[i].recordId == vm.versionId){
				vm.useFlag = vm.versionList[i].useFlag;
			}
		}
		upida.post("report/reportCapacity/capacityRecord",vm.versionId).then(function (result) {
			vm.capacitySet = result;
		});
	}

	vm.capacityRecordSave = function(){
		let validate = false;
		for (const key in vm.capacitySet) {
			if(typeof vm.capacitySet[key] === 'number'){
				if(vm.capacitySet[key]<0){
					validate = true;
					break;
				}
			}
		}
		if(validate){
			vm.message = "数据必须大于0!";
			$('#static').modal();
			return;
		}
		vm.capacitySet.versionId = vm.versionId;
		upida.post("report/reportCapacity/capacityRecordSave",vm.capacitySet).then(function (result) {
			vm.message = result.message;
			$('#static').modal();
			if(result.result=="success"){
				vm.loadCapacityRecord();
			}
		});
	}

	vm.payOpen = function () {
		vm.message = "您确定要结账?";
		$('#addCheckoutStatic').modal();
	}

	vm.pay = function () {
		upida.post("report/reportCapacity/pay", vm.reportList).then(function (result) {
			vm.message = result.message;
			$('#static').modal();
			if (result.result == "success") {
				vm.message = result.message;
			}
		});
	};
	vm.showMoney = function (row){
		if (row.showFlag){
			row.showFlag = '';
		}else {
			row.showFlag = 1;
		}
	};

	vm.right = {};
	function loadRight() {
		MainCtrl.blockUI({
			animate: true,
		});
		vm.queryAll = CommonUtil.dataRangeIsAll("10941", BaseUtil.getMenuList());
		upida.get("common/rightall?prefix=count:capacityReport").then(function(data){
			vm.right.edit = data.edit;
			vm.right.view = data.view;
			vm.right.manage = data.manage;
			vm.loadData();
			vm.loadSet();
			MainCtrl.unblockUI();
		});
	}
	// vm.capacityObj = {};
	// vm.showcapacityC = function(row){
	// 	vm.capacityObj = row;
	// 	$('#showcapacityList').modal();
	// }
	// vm.showcapacityDetail = function(){
	// 	MainCtrl.blockUI({
	// 		animate: true,
	// 	});
	// 	// 获取所有年月
	// 	upida.get("report/reportCapacity/showcapacityDetail",vm.capacityObj).then(function (data) {
	// 		vm.periodList = data.periodList;
	// 		vm.loadReport();
	// 		MainCtrl.unblockUI();
	// 	});
    //
	// };

	$scope.$on("$stateChangeSuccess", function () {
		upida.setScope($scope);
		loadRight();
	});

} ]);
