<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isErrorPage="true"%>
<div class="p-2">
	<div class="row pt-2 pb-2 alert-light border-bottom border-bottom-secondary">
		<div class="col text-center text-primary">
			<h5>提交日报</h5>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			日报时间
			<input id="date1" class="custom-select form-control h-32px" autocomplete="off"/>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			工作成效
			<textarea class="form-control" v-model="report.content"></textarea>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			总结心得
			<textarea class="form-control" v-model="report.contentTwo"></textarea>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			计划内容
			<textarea class="form-control" v-model="report.contentThree"></textarea>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			共享人员
			<select v-model="shareId" class="custom-select form-control selectpicker" data-max-options="6"
				multiple data-none-selected-text="共享人员" data-live-search="true" id="shareEmp">
			</select>
		</div>
	</div>
	<div class="row pt-2 pb-2 alert-light" style="word-break: break-word;">
		<div class="col">
			直接上级:
			{{superName}}&emsp;
			{{leadershipsName}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">图片列表</div>
	</div>
	<div v-for="(item,index) in localIds">
		<div class="row alert-light pt-2 pb-2 align-items-center">
			<div class="col">
				<img :src="item" class="w-25" v-on:click="preview(item)">
			</div>
			<div class="col-2">
				<button class="btn btn-sm btn-danger" v-on:click="deleteImg(index)">删除</button>
			</div>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col"><button class="btn btn-primary" v-on:click="chooseImg">上传图片</button></div>
	</div>
	<div style="padding-top: 4rem;"></div>
	<div class="row fixed-bottom bg-light pt-2 pb-2">
		<div class="col">
			<button class="btn btn-secondary w-100" v-on:click="cancle">取消</button>
		</div>
		<div class="col">
			<button class="btn btn-primary w-100" v-on:click="submit">发布</button>
		</div>
	</div>
</div>