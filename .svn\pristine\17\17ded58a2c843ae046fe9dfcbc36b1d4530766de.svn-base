<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="d-flex flex-column flex-root">
    <div class="d-flex flex-row flex-column-fluid page">
        <div class="d-flex flex-column flex-row-fluid wrapper">
            <div class="content d-flex flex-column flex-column-fluid"
                 style="background-image: url(${pageContext.request.contextPath}/assets/global/metronic/assets/media/bg/bgMain.jpg);height: 100px;">
                <div class="subheader subheader-transparent">
                    <div class="container d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <h5 class="text-white my-2 mr-5">销售系统</h5>
                            </div>
                        </div>
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <router-link to="/" class="btn btn-transparent-warning font-weight-bold mr-2">
                                    返回主页
                                </router-link>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-column-fluid">
                    <div class="container">
                        <div class="card card-custom gutter-b">
                            <div class="card-body d-flex flex-column p-3">
                                <div class="row pt-3 border-bottom">
                                    <div class="col-xl-12">
                                        <div class="row">
                                            <div class="col-xl-12">
                                                <span class="font-weight-bolder">销售模块</span>
                                            </div>
                                        </div>
                                        <div class="row pt-3">
                                            <div class="col-3 text-center pb-3" v-on:click="toPath(1)">
                                                <a href="#" class="btn btn-icon btn-light-primary">
                                                    <i class="flaticon-layer"></i>
                                                    <span class="pulse-ring"></span>
                                                </a>
                                                <div class="align-items-center">
                                                    <button class="btn btn-white mr-2 align-items-center">我的产品</button>
                                                </div>
                                            </div>
                                            <div class="col-3 text-center pb-3" v-on:click="toPath(2)">
                                                <a href="#" class="btn btn-icon btn-light-info">
                                                    <i class="flaticon-paper-plane"></i>
                                                    <span class="pulse-ring"></span>
                                                </a>
                                                <div class="align-items-center">
                                                    <button class="btn btn-white mr-2 align-items-center">推送产品</button>
                                                </div>
                                            </div>
                                            <div class="col-3 text-center pb-3" v-on:click="toPath(3)">
                                                <a href="#" class="btn btn-icon btn-light-danger">
                                                    <i class="flaticon-home-2"></i>
                                                    <span class="pulse-ring"></span>
                                                </a>
                                                <div class="align-items-center">
                                                    <button class="btn btn-white mr-2 align-items-center">产品维护</button>
                                                </div>
                                            </div>
                                            <div class="col-3 text-center pb-3" v-on:click="toPath(4)">
                                                <a href="#" class="btn btn-icon btn-light-success">
                                                    <i class="flaticon-presentation"></i>
                                                    <span class="pulse-ring"></span>
                                                </a>
                                                <div class="align-items-center">
                                                    <button class="btn btn-white mr-2 align-items-center">开始询价</button>
                                                </div>
                                            </div>
                                            <div class="col-3 text-center pb-3" v-on:click="toPath(5)">
                                                <a href="#" class="btn btn-icon btn-light-warning">
                                                    <i class="flaticon-notes"></i>
                                                    <span class="pulse-ring"></span>
                                                </a>
                                                <div class="align-items-center">
                                                    <button class="btn btn-white mr-2 align-items-center">历史询价</button>
                                                </div>
                                            </div>
                                            <div class="col-3 text-center pb-3" v-on:click="toPath(6)">
                                                <a href="#" class="btn btn-icon btn-light-primary">
                                                    <i class="flaticon2-open-text-book"></i>
                                                    <span class="pulse-ring"></span>
                                                </a>
                                                <div class="align-items-center">
                                                    <button class="btn btn-white mr-2 align-items-center">订单管理</button>
                                                </div>
                                            </div>
                                            <div class="col-3 text-center pb-3" v-on:click="toPath(7)">
                                                <a href="#" class="btn btn-icon btn-light-info">
                                                    <i class="flaticon-network"></i>
                                                    <span class="pulse-ring"></span>
                                                </a>
                                                <div class="align-items-center">
                                                    <button class="btn btn-white mr-2 align-items-center">申请合作</button>
                                                </div>
                                            </div>
                                            <%--<div class="col-3 text-center pb-3" v-on:click="toPath(8)">
                                                <a href="#" class="btn btn-icon btn-light-danger">
                                                    <i class="flaticon-home-2"></i>
                                                    <span class="pulse-ring"></span>
                                                </a>
                                                <div class="align-items-center">
                                                    <button class="btn btn-white mr-2 align-items-center">板材超市</button>
                                                </div>
                                            </div>--%>
                                        </div>
                                    </div>
                                </div>
                                <div class="row pt-3 pb-3">
                                    <div class="col-xl-12">
                                        <div class="row">
                                            <div class="col-4">
                                                <span class="font-weight-bolder">我的公司</span>
                                            </div>
                                            <div class="col text-right">
                                                <button class="btn btn-sm btn-outline-primary" v-on:click="openWindow">添加企业</button>&nbsp;
                                                <button class="btn btn-sm btn-outline-primary" v-on:click="inCompanyModal">加入企业</button>
                                            </div>
                                        </div>
                                        <div class="row" v-if="!(companyList && companyList.length > 0)">
                                            <div class="col-xl-12">
                                                <h3 class="text-danger">请创建公司或加入企业</h3>
                                            </div>
                                        </div>
                                        <div class="row pt-3 pb-3 border-bottom" v-for="item in companyList" :key="item.recordId">
                                            <div class="col">
                                                <div class="row align-items-center">
                                                    <div class="col-12">
                                                        <span class="text-primary" v-on:click="editDeail(item.recordId)" v-if="item.manageStatus">{{item.name}}</span>
                                                        <span v-else>{{item.name}}</span>
                                                    </div>
                                                </div>
                                                <div class="row pt-1 pb-2">
                                                    <div class="col-8">
                                                        <div class="d-flex align-items-center">
                                                            <div class="d-flex flex-column">
                                                                <div v-if="item.useFlag == 1">
                                                                    <div v-if="item.flag">
                                                                        <span class="text-warning font-weight-bolder">待管理员审核</span>
                                                                    </div>
                                                                    <div v-else>
                                                                        员工数：{{item.empNum}}
                                                                        <span v-if="item.manageStatus && item.waitNum > 0">&emsp;待通过：{{item.waitNum}}</span>
                                                                    </div>
                                                                    <span v-if="item.companyIn==1">已</span><span v-else>未</span>加入公司
                                                                </div>
                                                                <div v-else>
                                                                    <span class="text-danger font-weight-bolder">已停用</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col text-right">
                                                        <span v-if="item.useFlag == 1 && item.manageStatus"><button class="btn btn-sm btn-outline-primary" v-on:click="manage(item.recordId)">管理</button>&nbsp;</span>
                                                        <span v-if="!item.useFlag == 1 && item.manageStatus"><button class="btn btn-sm btn-outline-primary" v-on:click="useCom(item.recordId)">启用</button>&nbsp;</span>
                                                        <span><button class="btn btn-sm btn-outline-danger" v-on:click="exitModal(item.recordId)">退出</button></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" id="inCompanyModal" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="inCompanyModal" aria-hidden="true" style="overflow:auto!important;">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">加入企业</h5>
                                                <button class="btn btn-light-primary" v-on:click="closeWindow()">
                                                    关闭
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <form class="form">
                                                    <div class="form-group">
                                                        <button class="btn btn-sm btn-light-primary" v-on:click="wxRcanQRcode">扫描企业识别码</button>
                                                    </div>
                                                    <div class="form-group">
                                                        <label>输入企业识别码或企业名称:</label>
                                                        <input class="form-control form-control-solid" type="text" v-model="companyCode" v-on:input="loadCompany">
                                                    </div>
                                                </form>
                                                <div v-if="appCompanyList && appCompanyList.length > 0">
                                                    <div v-for="row in appCompanyList" :key="row.recordId">
                                                        <div class="row pt-7 pb-3">
                                                            <div class="col">
                                                                <span class="font-size-h3">企业信息</span>
                                                            </div>
                                                        </div>
                                                        <div class="row pb-2">
                                                            <div class="col">
                                                                <div class="d-flex align-items-center">
                                                                    <div class="symbol symbol-success pr-3" v-if="row.logo">
                                                                        <img alt="Pic" :src="row.logoStr" class="h-100"/>
                                                                    </div>
                                                                    <div class="symbol symbol-45 symbol-light-danger mr-4 flex-shrink-0" v-else>
                                                                        <div class="symbol-label">
                                                                            logo
                                                                        </div>
                                                                    </div>
                                                                    <div class="d-flex flex-column text-left">
                                                                        <div>
                                                                            <span>{{row.name}}</span>
                                                                        </div>
                                                                        <div>
                                                <span class="text-primary" v-if="row.erpCompanyId">
                                                    <i class="flaticon-car text-primary" style="font-size: 1rem;"></i>ERP&nbsp;
                                                </span>
                                                                            法人代表:{{row.legalPerson}}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row pb-2">
                                                            <div class="col">
                                                                统一社会信用代码:&nbsp;{{row.licence}}
                                                                <div class="pt-2">
                                                                    {{row.introduction}}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row pb-2 border-bottom">
                                                            <div class="col text-right">
                                                                <button class="btn btn-light-primary" v-on:click="inCompany(row)">加入</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer border-0">
                                                <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">取消</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" id="openCreateCompany" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="addBindWaitRepair" aria-hidden="true" style="overflow:auto!important;">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">
                                                    <span>创建公司</span>
                                                </h5>
                                                <button class="btn btn-light-primary text-center" v-on:click="closeWindow()">
                                                    关闭
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row pt-3 pb-3">
                                                    <div class="col-12">
                                                        <label><span style="color: red;">*</span>公司名称:</label>
                                                        <input type="text" class="form-control" v-model="company.name"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">取消</button>
                                                <button type="button" class="btn btn-light-primary font-weight-bold" v-on:click="saveCompany">保存</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" id="exitModal" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="exitModal" aria-hidden="true" style="overflow:auto!important;">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title text-danger">退出后不可恢复!</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <i aria-hidden="true" class="ki ki-close"></i>
                                                </button>
                                            </div>
                                            <div class="modal-body text-danger">
                                                <span v-if="companyMessage.status">请设置管理员后退出！</span><span v-else>请慎重考虑，是否退出该公司？</span>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-light-primary font-weight-bold" data-dismiss="modal">取消</button>
                                                <button type="button" class="btn btn-danger font-weight-bold" v-if="companyMessage.status" v-on:click="exitCompany()">强制退出</button>
                                                <button type="button" class="btn btn-danger font-weight-bold" v-else v-on:click="exitCompany()">退出</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>