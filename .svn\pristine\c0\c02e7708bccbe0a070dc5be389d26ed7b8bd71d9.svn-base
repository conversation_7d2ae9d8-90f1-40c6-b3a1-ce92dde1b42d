package com.kyb.pcberp.modules.crm.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.crm.entity.AccountsReceivable;

public class AccountsReceivableVo extends AccountsReceivable
{
    
    /**
     * 
     */
    private static final long serialVersionUID = -8172054707077795992L;
    
    private String type; // 类型1、用户确认;2、财务复核
    
    private String customerName;
    
    private BigDecimal amount;
    
    private Integer customerConfirmDay; // 客户预期确认日
    
    private Integer customerCheckDay;// 客户预期财务核算日
    
    private Date customerConfirmDate; // 客户预期确认时间 period+customerConfirmDay
    
    private Date customerCheckDate;// 客户预期确认时间 period+customerCheckDay
    
    private Integer confirmDiffDay;// 确认延期天数
    
    private Integer checkDiffDay;// 复核延期天数
    
    private String confirmLaterRate;// 确认延期比率
    
    private String checkLaterRate;// 复核延期比率
    
    private String confirmStatus;// 确认延期状态
    
    private String checkStatus;// 复核延期状态
    
    private Integer totalNum;// 总数
    
    private Integer confirmLaterNum;// 确认延期数
    
    private Integer checkLaterNum;// 复核延期数
    
    private BigDecimal dayRate; // 日利率
    
    private BigDecimal defaultAPR;// 年利率
    
    private BigDecimal confirmDelayCost;// 确认延期成本
    
    private BigDecimal checkDelayCost;// 复核延期成本
    
    private BigDecimal productDelayCost;// 生产延期成本
    
    private BigDecimal sendDelayCost;// 送货延期成本
    
    private Integer diffDay;// 延期天数
    
    private String customerPo;
    
    private String customerModel;
    
    private String contractNo;
    
    private String craftNo;
    
    private Date deliveryDate;
    
    private Date operateDate;
    
    private Integer quantity;

    private String confirmUserName;

    private String checkUserName;
    
    public BigDecimal getDayRate()
    {
        return dayRate;
    }
    
    public void setDayRate(BigDecimal dayRate)
    {
        this.dayRate = dayRate;
    }
    
    public BigDecimal getDefaultAPR()
    {
        return defaultAPR;
    }
    
    public void setDefaultAPR(BigDecimal defaultAPR)
    {
        this.defaultAPR = defaultAPR;
    }
    
    public String getType()
    {
        return type;
    }
    
    public void setType(String type)
    {
        this.type = type;
    }
    
    @ExcelField(title = "客户编号", align = 2, sort = 10)
    public String getCustomerNo()
    {
        return super.getCustomerNo();
    }
    
    @ExcelField(title = "客户名称", align = 2, sort = 20)
    public String getCustomerName()
    {
        return customerName;
    }
    
    @ExcelField(title = "对账月份", align = 2, sort = 30)
    public Integer getPeriod()
    {
        return super.getPeriod();
    }
    
    public void setCustomerName(String customerName)
    {
        this.customerName = customerName;
    }
    
    @ExcelField(title = "对账金额", align = 2, sort = 40)
    public BigDecimal getAmount()
    {
        return amount;
    }
    
    public void setAmount(BigDecimal amount)
    {
        this.amount = amount;
    }
    
    public Integer getCustomerConfirmDay()
    {
        return customerConfirmDay;
    }
    
    public void setCustomerConfirmDay(Integer customerConfirmDay)
    {
        this.customerConfirmDay = customerConfirmDay;
    }
    
    public Integer getCustomerCheckDay()
    {
        return customerCheckDay;
    }
    
    public void setCustomerCheckDay(Integer customerCheckDay)
    {
        this.customerCheckDay = customerCheckDay;
    }
    
    public Integer getConfirmDiffDay()
    {
        return confirmDiffDay;
    }
    
    public void setConfirmDiffDay(Integer confirmDiffDay)
    {
        this.confirmDiffDay = confirmDiffDay;
    }
    
    public Integer getCheckDiffDay()
    {
        return checkDiffDay;
    }
    
    public void setCheckDiffDay(Integer checkDiffDay)
    {
        this.checkDiffDay = checkDiffDay;
    }
    
    public String getConfirmLaterRate()
    {
        return confirmLaterRate;
    }
    
    public void setConfirmLaterRate(String confirmLaterRate)
    {
        this.confirmLaterRate = confirmLaterRate;
    }
    
    public String getCheckLaterRate()
    {
        return checkLaterRate;
    }
    
    public void setCheckLaterRate(String checkLaterRate)
    {
        this.checkLaterRate = checkLaterRate;
    }
    
    public String getConfirmStatus()
    {
        return confirmStatus;
    }
    
    public void setConfirmStatus(String confirmStatus)
    {
        this.confirmStatus = confirmStatus;
    }
    
    public String getCheckStatus()
    {
        return checkStatus;
    }
    
    public void setCheckStatus(String checkStatus)
    {
        this.checkStatus = checkStatus;
    }
    
    public Integer getTotalNum()
    {
        return totalNum;
    }
    
    public void setTotalNum(Integer totalNum)
    {
        this.totalNum = totalNum;
    }
    
    public Integer getConfirmLaterNum()
    {
        return confirmLaterNum;
    }
    
    public void setConfirmLaterNum(Integer confirmLaterNum)
    {
        this.confirmLaterNum = confirmLaterNum;
    }
    
    public Integer getCheckLaterNum()
    {
        return checkLaterNum;
    }
    
    public void setCheckLaterNum(Integer checkLaterNum)
    {
        this.checkLaterNum = checkLaterNum;
    }
    
    @ExcelField(title = "预期确认日期", align = 2, sort = 55)
    public Date getConfirmDate()
    {
        return super.getConfirmDate();
    }
    
    @ExcelField(title = " 预期复核日期", align = 2, sort = 65)
    public String getCheckDateStr()
    {
        return super.getCheckDateStr();
    }
    
    public BigDecimal getConfirmDelayCost()
    {
        return confirmDelayCost;
    }
    
    public void setConfirmDelayCost(BigDecimal confirmDelayCost)
    {
        this.confirmDelayCost = confirmDelayCost;
    }
    
    public BigDecimal getCheckDelayCost()
    {
        return checkDelayCost;
    }
    
    public void setCheckDelayCost(BigDecimal checkDelayCost)
    {
        this.checkDelayCost = checkDelayCost;
    }
    
    public BigDecimal getProductDelayCost()
    {
        return productDelayCost;
    }
    
    public void setProductDelayCost(BigDecimal productDelayCost)
    {
        this.productDelayCost = productDelayCost;
    }
    
    public BigDecimal getSendDelayCost()
    {
        return sendDelayCost;
    }
    
    public void setSendDelayCost(BigDecimal sendDelayCost)
    {
        this.sendDelayCost = sendDelayCost;
    }
    
    public Date getCustomerConfirmDate()
    {
        return customerConfirmDate;
    }
    
    public void setCustomerConfirmDate(Date customerConfirmDate)
    {
        this.customerConfirmDate = customerConfirmDate;
    }
    
    public Date getCustomerCheckDate()
    {
        return customerCheckDate;
    }
    
    public void setCustomerCheckDate(Date customerCheckDate)
    {
        this.customerCheckDate = customerCheckDate;
    }
    
    public Integer getDiffDay()
    {
        return diffDay;
    }
    
    public void setDiffDay(Integer diffDay)
    {
        this.diffDay = diffDay;
    }
    
    public String getCustomerPo()
    {
        return customerPo;
    }
    
    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }
    
    public String getCustomerModel()
    {
        return customerModel;
    }
    
    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }
    
    public String getContractNo()
    {
        return contractNo;
    }
    
    public void setContractNo(String contractNo)
    {
        this.contractNo = contractNo;
    }
    
    public String getCraftNo()
    {
        return craftNo;
    }
    
    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }
    
    public Date getDeliveryDate()
    {
        return deliveryDate;
    }
    
    public void setDeliveryDate(Date deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }
    
    public Date getOperateDate()
    {
        return operateDate;
    }
    
    public void setOperateDate(Date operateDate)
    {
        this.operateDate = operateDate;
    }
    
    public Integer getQuantity()
    {
        return quantity;
    }
    
    public void setQuantity(Integer quantity)
    {
        this.quantity = quantity;
    }

    @Override
    public String getConfirmUserName()
    {
        return confirmUserName;
    }

    @Override
    public void setConfirmUserName(String confirmUserName)
    {
        this.confirmUserName = confirmUserName;
    }

    @Override
    public String getCheckUserName()
    {
        return checkUserName;
    }

    @Override
    public void setCheckUserName(String checkUserName)
    {
        this.checkUserName = checkUserName;
    }
}
