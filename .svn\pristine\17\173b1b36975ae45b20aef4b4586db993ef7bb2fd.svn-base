package com.kyb.pcberp.modules.report.dao;

import java.util.List;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.contract.entity.DeliveryDetail;
import com.kyb.pcberp.modules.crm.entity.RejectApplication;
import com.kyb.pcberp.modules.purch.entity.PrdorderDetail;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;
import com.kyb.pcberp.modules.report.entity.Report;
import com.kyb.pcberp.modules.report.entity.SaleNewReportData;
import com.kyb.pcberp.modules.report.entity.overdueData;
import com.kyb.pcberp.modules.sys.entity.Cockpit;
import org.apache.ibatis.annotations.Param;

@MyBatisDao
public interface ErpReportDao
{
    /** zjn 2019-09-18 获取销售报表导出数据*/
    List<SaleNewReportData> getSaleNewReportDatas(Report report);
    
    /** zjn 2019-09-19 获取合同明细报表详情显示数据*/
    List<ContractDetail> getReportContractDetailList(Report report);
    
    /** zjn 2019-09-20 获取逾期合同明细报表详情显示数据 */
    List<ContractDetail> getReportOverDueList(Report report);
    
    /** zjn 2019-09-27 获取送货单明细报表详情显示数据*/
    List<DeliveryDetail> getReportDeliveryDetailList(Report report);
    
    /** zjn 2019-09-27 获取客诉单报表详情显示数据*/
    List<RejectApplication> getReportRejectList(Report report);
    
    /** zjn 2019-09-29 获取原料采购单明细详情显示数据*/
    List<PurchasingDetail> getReportPurchasingDetailList(Report report);
    
    /** zjn 2019-09-29 获取成品采购单明细详情显示数据*/
    List<PrdorderDetail> getReportPrdorderDetailList(Report report);

    /** 获取逾期订单款数详情数据*/
    List<overdueData>getExportReportData(Report report);

    List<Cockpit>getOrderStatusList(@Param("contractDetailId") String contractDetailId);
}
