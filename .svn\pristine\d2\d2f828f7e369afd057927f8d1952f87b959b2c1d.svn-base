<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div>
	<div class="position-absolute top-0 left-0 text-left mb-lg-0 flex-column-auto justify-content-center py-2 px-6">
		<span style="font-weight: bold; font-size: 1.3rem;">登录</span>
	</div>
	<div class="position-absolute top-0 right-0 text-right mb-lg-0 flex-column-auto justify-content-center py-13 px-6">
		<span class="font-weight-bold font-size-3 text-dark-60">还没有账号?</span>
		<router-link class="font-weight-bold font-size-3 ml-2" :to="{ path: '/register' }">注册!</router-link>
	</div>
	<div class="login-form login-signin" style="padding-top: 5rem;">
		<div>
			<div class="form-group">
				<label style="font-size: 1.3rem;">姓名/手机号:</label>
				<input class="form-control form-control-solid" type="text" v-model="name" name="name" autocomplete="off" required>
			</div>
			<div class="form-group">
				<label style="font-size: 1.3rem;">密码:</label>
				<input class="form-control form-control-solid" type="password" v-model="password" name="password" autocomplete="new-password" required>
				<router-link class="font-weight-bold font-size-3 ml-2" :to="{ path: '/editPwd' }">忘记密码？</router-link>
			</div>
			<div class="form-group">
				<label class="checkbox">
					<input type="checkbox" v-model="agreeFlag" required/>
					<span></span>
					&nbsp;我同意<a href="">《科易博服务条款》</a>及<a href="">《隐私说明》</a>
				</label>
			</div>
			<div class="row pt-8">
				<div class="col text-center">
					<button class="btn btn-primary" style="width: 25rem" v-on:click="login">登&emsp;录</button>
				</div>
			</div>
		</div>
	</div>
</div>