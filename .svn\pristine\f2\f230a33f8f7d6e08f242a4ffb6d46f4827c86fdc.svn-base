/**
 *
 */
package com.kyb.pcberp.common.utils;

import com.google.common.collect.Maps;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.*;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.enums.CommonEnums.DictItemEnum;
import com.kyb.pcberp.modules.crm.dao.AccountsAttachementsDao;
import com.kyb.pcberp.modules.sys.entity.Codegen;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.service.CodegenService;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import org.apache.commons.lang.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Cache工具类
 *
 * @version 2013-5-29
 */
public class CommonUtils
{

    private static CodegenService codegenService = SpringContextHolder.getBean(CodegenService.class);

    private static AccountsAttachementsDao accountsAttachementsDao= SpringContextHolder.getBean(AccountsAttachementsDao.class);

    public static final String CACHE_REDUCE_PNL_MAP = "reducePnlMap";


    public static String getReduceSeq(User user)
    {
        @SuppressWarnings("unchecked") Map<String, Map<String, Map<String, String>>> reducePnlMap =
            (Map<String, Map<String, Map<String, String>>>)CacheUtils.get(CACHE_REDUCE_PNL_MAP);
        if (reducePnlMap == null)
        {
            reducePnlMap = Maps.newHashMap();

            Map<String, Map<String, String>> dates = Maps.newHashMap();
            dates.put(DateUtils.getDate(), Maps.newHashMap());
            reducePnlMap.put(user.getCompany().getRecordId(), dates);
            CacheUtils.put(CACHE_REDUCE_PNL_MAP, reducePnlMap);
            return null;
        }

        // 获取日期列表
        Map<String, Map<String, String>> dates = reducePnlMap.get(user.getCompany().getRecordId());
        if (dates == null)
        {
            return null;
        }
        else
        {
            Map<String, String> users = dates.get(DateUtils.getDate());
            if (users == null)
            {
                return null;
            }
            else
            {
                return users.get(user.getRecordId());
            }
        }
    }

    public static void putReduceSeq(User user, String seq)
    {
        @SuppressWarnings("unchecked") Map<String, Map<String, Map<String, String>>> reducePnlMap =
            (Map<String, Map<String, Map<String, String>>>)CacheUtils.get(CACHE_REDUCE_PNL_MAP);
        if (reducePnlMap == null)
        {
            reducePnlMap = Maps.newHashMap();
            CacheUtils.put(CACHE_REDUCE_PNL_MAP, reducePnlMap);
        }

        // 获取日期列表
        Map<String, Map<String, String>> dates = reducePnlMap.get(user.getCompany().getRecordId());
        if (dates == null)
        {
            dates = Maps.newHashMap();

            Map<String, String> users = Maps.newHashMap();

            // 放入用户
            users.put(user.getRecordId(), seq);

            // 放入日期
            dates.put(DateUtils.getDate(), users);

            // 放入公司
            reducePnlMap.put(user.getCompany().getRecordId(), dates);
        }
        else
        {
            // 移除非当天的
            Iterator<Map.Entry<String, Map<String, String>>> it = dates.entrySet().iterator();
            while (it.hasNext())
            {
                Map.Entry<String, Map<String, String>> entry = it.next();
                String key = entry.getKey();
                if (key.equals(DateUtils.getDate()))
                {
                    continue;
                }
                it.remove();
            }

            Map<String, String> users = dates.get(DateUtils.getDate());
            if (users == null)
            {
                users = Maps.newHashMap();
                users.put(user.getRecordId(), seq);
                dates.put(DateUtils.getDate(), users);
            }
            else
            {
                users.put(user.getRecordId(), seq);
            }
        }
    }

    public static void updateNextNo(Integer type, Company company)
    {
        Codegen qc = new Codegen();
        qc.setCompany(company);
        qc.setCodeType(type);

        Codegen codegen = codegenService.getByCodeType(qc);
        if (codegen != null)
        {
            codegen.setNextno("1");
            codegenService.addNextNo(codegen);
        }
    }

    public static void updateNextNo(Integer type)
    {
        updateNextNo(type, UserUtils.getUser().getCompany());
    }

    public static void updateNextNo(Integer type, Integer size)
    {
        Codegen qc = new Codegen();
        qc.setCompany(UserUtils.getUser().getCompany());
        qc.setCodeType(type);

        Codegen codegen = codegenService.getByCodeType(qc);
        if (codegen != null)
        {
            Integer nun = size + 1;
            codegen.setNextno(nun.toString());
            codegenService.addNextNo(codegen);
        }
    }

    public static String geDocumentNo(String type)
    {
        return geDocumentNo(type, null, UserUtils.getUser().getCompany());
    }

    // 云平台下单专用
    public static String geDocumentNoIcloud(String type, Company company)
    {
        return geDocumentNo(type, null, company);
    }

    public static String geDocumentNo(String type, String customerNo)
    {
        return geDocumentNo(type, customerNo, UserUtils.getUser().getCompany());
    }

    public static String geDocumentNo(String type, Company company)
    {
        return geDocumentNo(type, null, company);
    }

    /**
     * 公共生成编号的方法 传CommonEnums.CodeType 中的类型
     *
     * @param type CommonEnums.CodeType
     * @return
     */
    public static String geDocumentNo(String type, String customerNo, Company company)
    {
        Codegen qc = new Codegen();
        qc.setCompany(company);
        qc.setCodeType(Integer.parseInt(type));
        Codegen code = codegenService.getByCodeType(qc);

        if (code == null)
        {
            code = new Codegen();
            code.setCompany(qc.getCompany());
            code.setCodeType(Integer.parseInt(type));
            code.setNextno("1");
            code.preInsert();
            StringBuffer sb = new StringBuffer("01");
            sb.append("%");
            sb.append(code.getCompany().getCode());
            if (CommonEnums.CodeType.PRODUCTION.getIndex().toString().equals(type))
            {
                sb.append("+101");
            }
            sb.append("+825+04");
            code.setRegex(sb.toString());
            codegenService.save(code);
        }
        else
        {
            code.setCompany(qc.getCompany());
        }

        String no = IdGen.generateNoByCode(code, customerNo);

        // 是否需要将code的编号+1
        // if (StringUtils.isNotBlank(no))
        // {
        // updateNextNo(code.getCodeType());
        // }

        return no;
    }

    public static ByteArrayOutputStream readerPdf(String file)
    {
        ByteArrayOutputStream output = null;
        Document document = null;
        try
        {
            PdfReader reader = new PdfReader(file);
            output = new ByteArrayOutputStream();
            document = new Document();

            PdfWriter writer = PdfWriter.getInstance(document, output);

            document.open();
            PdfContentByte cb = writer.getDirectContent();

            int pageNo = 0;
            while (pageNo < reader.getNumberOfPages())
            {
                document.newPage();
                pageNo++;
                PdfImportedPage page = writer.getImportedPage(reader, pageNo);
                cb.addTemplate(page, 0, 0);
            }

            return output;
        }
        catch (Exception e)
        {
        }
        finally
        {
            try
            {
                output.flush();
                document.close();
                output.close();
            }
            catch (IOException e)
            {
            }
        }
        return output;
    }

    public static ByteArrayOutputStream mergePdfFiles(ByteArrayOutputStream page1, ByteArrayOutputStream page2)
        throws IOException
    {
        PdfReader reader1 = new PdfReader(page1.toByteArray());
        PdfReader reader2 = new PdfReader(page2.toByteArray());

        ByteArrayOutputStream o = new ByteArrayOutputStream();

        Document doc = new Document(reader1.getPageSize(1));

        try
        {
            PdfCopy copy = new PdfCopy(doc, o);

            doc.open();

            int n = reader1.getNumberOfPages();

            for (int j = 1; j <= n; j++)
            {
                doc.newPage();
                PdfImportedPage page = copy.getImportedPage(reader1, j);
                copy.addPage(page);
            }

            int m = reader2.getNumberOfPages();

            for (int k = 1; k <= m; k++)
            {
                doc.newPage();
                PdfImportedPage page = copy.getImportedPage(reader2, k);
                copy.addPage(page);
            }

        }
        catch (IOException e)
        {
            // logger.warn(e.getMessage(), e);
        }
        catch (DocumentException e)
        {
            // logger.warn(e.getMessage(), e);
        }
        finally
        {
            if (page1 != null)
            {
                page1.close();
            }
            if (page2 != null)
            {
                page2.close();
            }
            doc.close();
        }
        return o;
    }

    public static int getDays(Long recordId)
        throws Exception
    {
        String s = DictUtils.getDictValue(recordId.toString(), DictItemEnum.DELIVERY_DAYS, "");
        try
        {
            // String str = s.replace("天交货", "");
            // return Integer.parseInt(str);
            Pattern p = Pattern.compile("(^\\d+)\\S+");
            Matcher m = p.matcher(s);
            return Integer.parseInt(m.replaceAll("$1"));
        }
        catch (Exception e)
        {
            throw new Exception();
        }
    }

    public static String getConstValue(Integer recordId, Map<String, String> maps)
    {
        String s = "";
        try
        {
            if (maps.containsKey(String.valueOf(recordId)))
            {
                s = maps.get(String.valueOf(recordId));
            }
        }
        catch (Exception e)
        {

        }
        return s;
    }

    public static String getTemplateUrl(String realPath, String template)
    {
        return realPath + "WEB-INF/template/" + template;
    }

    // 获得当前月--开始日期
    public static Date getMinMonthDate(Date date)
    {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        calendar.set(Calendar.MINUTE, 00);
        calendar.set(Calendar.SECOND, 00);
        calendar.set(Calendar.MILLISECOND, 00);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        return calendar.getTime();
    }

    // 获得当前月--结束日期
    public static Date getMaxMonthDate(Date date)
    {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 59);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return calendar.getTime();
    }

    public static String shortString(String content, int count)
    {
        if (content == null)
        {
            return null;
        }
        if (content.length() > count)
        {
            return StringUtils.substring(content, 0, count) + "...";
        }
        else
        {
            return content;
        }
    }

    /**
     * @Title getExceptionTrace
     * @Description 获取堆栈信息
     * @param e
     * @return String
     */
    public static String getExceptionTrace(Throwable e)
    {
        if (e != null)
        {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            return sw.toString();
        }
        return "No Exception";

    }

    public static String getDisplayPDFName(String fileName)
    {
        StringBuffer filename = new StringBuffer();
        filename.append(fileName + "_");
        filename.append(DateUtils.getDate("yyyyMMddHHmmss"));
        filename.append(".pdf");
        return filename.toString();
    }

    public static String getDisplayPNGName(String fileName)
    {
        StringBuffer filename = new StringBuffer();
        filename.append(fileName + "_");
        filename.append(DateUtils.getDate("yyyyMMddHHmmss"));
        filename.append(".png");
        return filename.toString();
    }

    public static String getDisplayPNGName(String fileName,String name)
    {
        StringBuffer filename = new StringBuffer();
        filename.append(fileName + "_" + name+"_");
        filename.append(DateUtils.getDate("yyyyMMddHHmmss"));
        filename.append(".png");
        return filename.toString();
    }

    public static String getDisplayWordName(String fileName)
    {
        StringBuffer filename = new StringBuffer();
        filename.append(fileName + "_");
        filename.append(DateUtils.getDate("yyyyMMddHHmmss"));
        filename.append(".docx");
        return filename.toString();
    }

    /**
     * 日期比较公用方法 2016-01-26 chengqiang add
     *
     * @param DATE1
     * @param DATE2
     * @return
     */
    public static int compare_date(String DATE1, String DATE2)
    {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd hh:mm");
        try
        {
            Date dt1 = df.parse(DATE1);
            Date dt2 = df.parse(DATE2);
            if (dt1.getTime() > dt2.getTime())
            {
                return 1;
            }
            else if (dt1.getTime() < dt2.getTime())
            {
                return -1;
            }
            else
            {
                return 0;
            }
        }
        catch (Exception exception)
        {
            exception.printStackTrace();
        }
        return 0;
    }

    /**
     * 获取报废、返工或不良率
     *
     * @param area 报废、返工或不量面积
     * @param putArea 入库面积
     */
    public static BigDecimal getRatio(BigDecimal area, BigDecimal putArea)
    {
        // 返工/报废/不良率 = 当日返工/报废/不良面积／（当日返工/报废/不良面积 + 当日入库面积） * 100%

        if (area == null || area.compareTo(BigDecimal.ZERO) == 0)
        {
            return BigDecimal.ZERO;
        }

        if (putArea == null || area.compareTo(BigDecimal.ZERO) == 0)
        {
            putArea = BigDecimal.ZERO;
        }

        BigDecimal ratio = new BigDecimal(MathUtils.roundAsString(MathUtils.mul(MathUtils.div(area.doubleValue(),
            MathUtils.add(area.doubleValue(), putArea.doubleValue()),
            4), 100), 4));
        return ratio;
    }

    /*
     * saleId 销售公司id
     * econnmyId  总部经济公司id
     * factoryId  工厂公司id
     * 根据三个id值进行判断，得到利率值
     * */
    public static BigDecimal calculate(String saleId, String econnmyId, String factoryId)
    {
        //默认利率值
        BigDecimal bigDecimal= BigDecimal.ZERO;
        //如果没有匹配得到利率值，则默认利率值为100
        if (StringUtils.isBlank(saleId))
        {
            return bigDecimal;
        }
        BigDecimal grossProfitRate =accountsAttachementsDao.selectcalculate(saleId,econnmyId,factoryId);
        if (null == grossProfitRate  )
        {
            grossProfitRate = bigDecimal;
        }
        //如果查询出来的值为空也设置为默认利率
        return grossProfitRate;
    }

    public static String geHrDocumentNo(String type, Company company)
    {
        return geDocumentNo(type, null, company);
    }
}
