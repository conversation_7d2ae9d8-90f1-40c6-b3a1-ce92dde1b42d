package com.kyb.pcberp.modules.production.dao;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.contract.entity.DeliveryLate;
import com.kyb.pcberp.modules.production.entity.BatchDetailCapacity;
import com.kyb.pcberp.modules.sys.entity.CockpitSheet;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@MyBatisDao
public interface BatchDetailCapacityDao
{
    void batchInsert(@Param("list") List<BatchDetailCapacity> list);

    List<BatchDetailCapacity> findList(BatchDetailCapacity batchDetailCapacity);

    void delete(BatchDetailCapacity batchDetailCapacity);

    void deleteByBatchId(@Param("batchId") String batchId);

    void updateCapacityDetailArea(@Param("batchId") String batchId);

    BatchDetailCapacity getMinDeliveryDateBybatchDetailId(@Param("recordId") String recordId);

    List<BatchDetailCapacity> getMinDeliveryDateList(@Param("list") List<CockpitSheet> list);

    List<BatchDetailCapacity> getMinDeliveryDateListTwo(@Param("detailId") String detailId);

    BatchDetailCapacity getMinDeliveryDateBybatchDetailIdTwo(@Param("recordId") String recordId);

    void updateDeliveryDate(DeliveryLate deliveryLates);

    List<BatchDetailCapacity> getMinDeliveryDateListThree(BatchDetailCapacity batchDetailCapacity);

    void updateDeliveryDateByTimeId(@Param("orderDeliveryTimeId") String orderDeliveryTimeId);
}
