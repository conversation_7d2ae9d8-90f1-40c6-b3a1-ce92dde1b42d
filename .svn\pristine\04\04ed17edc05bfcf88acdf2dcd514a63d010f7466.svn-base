package com.kyb.pcberp.common.utils.excel;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import com.kyb.pcberp.common.utils.MathUtils;

public class ExcelUtils {

    /**
     * 下载excel文档时给数字单元格单独设置单元格格式 hxy
     *
     * @param style 单元格style
     * @param val   单元格参数
     * @param cell  单元格
     * @param wb    工作薄对象
     */
    public static void settingNumeric(CellStyle style, String val, Cell cell, SXSSFWorkbook wb) {

        if (MathUtils.isNumeric(val)) {
            if (val.equals("0")) {
                val = "0.0";
            }


            //设置所有纯数字，小数字符单元格为小数类型 hxy
            String formatStr = "0";
            try {
                if (val.indexOf(".") != -1) {
                    //获取小数点后位数
                    String z = val.substring(val.indexOf(".") + 1);
                    formatStr += "." + z.replaceAll("[^[0-9]*$]", "0");

                    cell.setCellValue(Double.parseDouble(val));// 设值
                } else {
                    //整数
                    cell.setCellValue(Long.parseLong(val));// 设值
                }

            }catch (Exception e){
                 e.printStackTrace();
                 cell.setCellValue(val);// 设值
            }

            DataFormat format = wb.createDataFormat();
            style.setDataFormat(format.getFormat(formatStr));
        } else {
            //cell.setCellValue(val);// 设值

        }
        cell.setCellStyle(style);
    }

}
