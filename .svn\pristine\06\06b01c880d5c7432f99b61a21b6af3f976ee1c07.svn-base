<%@ page contentType="text/html;charset=UTF-8" %>
<tab heading="客户列表" active="ctrl.tabs.viewForm.active">
    <div class="panel panel-default" id="step6">
        <div class="panel-heading font-blue-hoki">查询</div>
        <div  class="panel-body">
            <form class="form-horizontal">
                <div  class="row">
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">客户编号：</label>
                            <div class="col-sm-7 col-md-8">
                                <input type="text" class="form-control" ng-model="ctrl.query.no.value" disable-auto-validate="true"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">客户名称：</label>
                            <div class="col-sm-7 col-md-8">
                                <input type="text" class="form-control" ng-model="ctrl.query.name.value" disable-auto-validate="true"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">业务员：</label>
                            <div class="col-sm-7 col-md-8">
                                <input type="text" class="form-control" ng-model="ctrl.query.salesman.userName.value" disable-auto-validate="true"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">客户状态：</label>
                            <div class="col-sm-7 col-md-8">
                                <select  class="form-control"
                                         ng-model="ctrl.query.statusSelected"
                                         ng-options="status.value as status.name for status in ctrl.query.statusList"
                                         disable-auto-validate="true">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">客户跟进：</label>
                            <div class="col-sm-7 col-md-8">
                                <select  class="form-control"
                                         ng-model="ctrl.query.genSelected"
                                         ng-options="gen.value as gen.name for gen in ctrl.query.genList"
                                         disable-auto-validate="true">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">客户来源类型：</label>
                            <div class="col-sm-7 col-md-8">
                                <select  class="form-control"
                                         ng-model="ctrl.query.showType.value"
                                         ng-options="gen.value as gen.name for gen in ctrl.query.showTypeList"
                                         disable-auto-validate="true">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">客户来源：</label>
                            <div class="col-sm-7 col-md-8">
                                <select class="form-control" ng-model="ctrl.query.customerSource.value" ng-options="gen.shortName as gen.shortName for gen in ctrl.branchList" disable-auto-validate="true">
                                    <option value="">所有</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">客户等级：</label>
                            <div class="col-sm-7 col-md-8">
                                <select class="form-control" ng-model="ctrl.query.customerGrade.value" ng-options="gen.recordId as gen.value for gen in ctrl.customerGradeList" disable-auto-validate="true">
                                    <option value="">所有</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">部门：</label>
                            <div class="col-sm-7 col-md-8">
                                <ui-select  ng-model="ctrl.deptIdQuery" theme="bootstrap">
                                    <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                    <ui-select-choices repeat="item.recordId as item in ctrl.deptListCopy | filter: $select.search">
                                        <div ng-bind-html="item.no | highlight: $select.search"></div>
                                        <div ng-bind-html="item.name | highlight: $select.search"></div>
                                    </ui-select-choices>
                                </ui-select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">组织：</label>
                            <div class="col-sm-7 col-md-8">
                                <select class="form-control" ng-model="ctrl.finalQuery" disable-auto-validate="true">
                                    <option value="">所有</option>
                                    <option value="1">创建组织</option>
                                    <option value="2">使用组织</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">创建日期：</label>
                            <div class="col-sm-7 col-md-8">
                                <div class="input-prepend input-group">
                                    <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                    <input type="text" class="form-control" disable-auto-validate="true"
                                           ng-blur="ctrl.initDate(ctrl.time)"
                                           kyb-daterange
                                           kyb-daterange-options="ctrl.rangeOptions"
                                           ng-model="ctrl.time"
                                           placeholder="请选择时间段">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <button class="btn btn-default btn-default-width pull-right" ng-click="ctrl.doQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="portlet light bordered">
        <div class="portlet-title">
            <div class="caption font-blue-hoki">客户列表 </div>
            <div id="step7" class="actions">
                <%--<div class="portlet-input input-inline input-small">
                    <form action="a/crm/customer/export" method="POST" enctype="multipart/form-data" target="hidden_frame">
                        <input type="text" ng-show="false"  name="no" value="{{ctrl.query.no.value}}"/>
                        <input type="text" ng-show="false"  name="name" value="{{ctrl.query.name.value}}"/>
                        <input type="text" ng-show="false"  name="status" value="{{ctrl.query.statusSelected}}"/>
                        <input type="text" ng-show="false"  name="salesman.recordId" value="{{ctrl.query.genSelected}}"/>
                        <input type="text" ng-show="false"  name="sentTimeStartQr" value="{{ctrl.query.sentTimeStartQr.value}}"/>
                        <input type="text" ng-show="false"  name="sentTimeEndQr" value="{{ctrl.query.sentTimeEndQr.value}}"/>
                        <input type="text" ng-show="false"  name="orderBy" value="{{ctrl.query.sort.value}}"/>
                        <input type="text" ng-show="false"  name="showType" value="{{ctrl.query.showType.value}}"/>
                        <input type="text" ng-show="false"  name="customerSource" value="{{ctrl.query.customerSource.value}}"/>
                        <input type="text" ng-show="false"  name="customerGrade" value="{{ctrl.query.customerGrade.value}}"/>
                        <input type="text" ng-show="false"  name="queryAll" value="{{ctrl.queryAll}}"/>
                        <input type="text" ng-show="false"  name="salesman.userName" value="{{ctrl.query.salesman.userName.value}}"/>
                        <input type="text" ng-show="false"  name="deptId" value="{{ctrl.deptIdQuery}}"/>
                        <input type="text" ng-show="false"  name="finalQuery" value="{{ctrl.finalQuery}}"/>
                        <div ng-if="ctrl.right.view" >
                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出客户</button>
                            <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                        </div>
                    </form>
                </div>--%>
                <div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
                    <button type="button" class="btn green btn-default-width" ng-click="ctrl.addCustomer()"><i class="fa fa-plus"></i> 添加客户</button>
                </div>
                <div class="portlet-input input-inline input-small" ng-if="ctrl.right.edit">
                    <button type="button" class="btn green btn-default-width" ng-click="ctrl.batchDistributionCusOpen()"><i class="fa fa-plus"></i> 批量分配</button>
                </div>
            </div>
        </div>
        <span class="label text-default label-primary" ng-if="ctrl.giveCapacity && ctrl.giveCapacity > 0">
            	工厂分配日产能{{ctrl.giveCapacity}}平米
            </span>
        <span class="label text-default label-success" ng-if="ctrl.waitCapa && ctrl.waitCapa > 0">
            	可分配{{ctrl.waitCapa}}%产能至其它客户
            </span>
        <span class="label text-default label-danger" ng-if="ctrl.waitCapa && ctrl.waitCapa < 0">
            	当前产能比差{{ctrl.waitCapa}}%，请调整客户产能减少进行平衡
            </span>
        <div class="portlet-body">
            <div id="step1" class="table-scrollable" style="margin-top:0px !important">
                <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                    <thead>
                    <tr class="heading">
                  	    <th>
                      		<checkbox ng-model="ctrl.allPageChecked" value="1" name="test" ng-click="ctrl.selectAllPage()"></checkbox>
                      	</th>
                        <th id="step2" ng-class="{'sorting': ctrl.sort.no.both, 'sorting_desc': ctrl.sort.no.desc, 'sorting_asc': ctrl.sort.no.asc}" ng-click="ctrl.sortClick('no')">客户编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <th ng-class="{'sorting': ctrl.sort.name.both, 'sorting_desc': ctrl.sort.name.desc, 'sorting_asc': ctrl.sort.name.asc}" ng-click="ctrl.sortClick('name')">客户简称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <th>客户全称</th>
                        <th>业务员</th>
                        <th>客户百分比</th>
                        <th>客户等级产能</th>
                        <th>同等级客户数</th>
                        <th>客户来源</th>
                        <th>部门</th>
                        <th>打印文件</th>
                        <th>确认账单日期</th>
                        <th>复核账单日期</th>
                        <th>组织&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <th ng-class="{'sorting': ctrl.sort.status.both, 'sorting_desc': ctrl.sort.status.desc, 'sorting_asc': ctrl.sort.status.asc}" ng-click="ctrl.sortClick('status')">状态&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <th ng-class="{'sorting': ctrl.sort.createdDate.both, 'sorting_desc': ctrl.sort.createdDate.desc, 'sorting_asc': ctrl.sort.createdDate.asc}" ng-click="ctrl.sortClick('createdDate')">创建日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                        <th id="step3">操作&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="row in ctrl.page.data.list" ng-dblclick="ctrl.ctrlShowCustomerDetail($index)">
                        <td>
                   			 <checkbox ng-model="row.checked" ng-if="row.safekeeping != '1'" ng-change="ctrl.getRecordList()"></checkbox>
                   	  	</td>
                        <td height="60px" ng-if="row.safekeeping != 1">
                            <a ng-click="ctrl.ctrlShowCustomerDetail($index)">{{row.no}}</a>
                        </td>
                        <td height="60px" ng-if="row.safekeeping == 1">
                            {{row.no}}
                        </td>
                        <td ng-bind="row.shortName"></td>
                        <td ng-bind="row.name"></td>
                        <td ng-bind="row.salesman.userName"></td>
                        <td>{{row.capacityPrecent ? (row.capacityPrecent).toFixed(4) : 0}}%</td>
                        <td>
                            {{row.customerGradeValue}}
                            <span ng-if="row.customerGradeRemark > 0">
                            		({{(row.customerGradeRemark).toFixed(2)}}%)
                            		<span ng-if="ctrl.giveCapacity > 0">
                            			{{(ctrl.giveCapacity * row.customerGradeRemark / 100).toFixed(2)}}㎡
                            		</span>
                            	</span>
                        </td>
                        <td><span ng-if="row.customerGradeNum > 0 && row.customerGradeValue">{{row.customerGradeNum}}</span></td>
                        <td ng-bind="row.customerSource"></td>
                        <td ng-bind="row.dept ? row.dept.name : ''"></td>
                        <td ng-bind="row.realFileName"></td>
                        <td ng-bind="row.confirmBilDate"></td>
                        <td ng-bind="row.checkBilDate"></td>
                        <td ng-bind="row.finalQueryName"></td>
                        <td ng-if="row.status === 100301">
                            <span class="label label-sm label-default">待审核</span>
                        </td>
                        <td ng-if="row.status === 100302">
                            <span class="label label-sm label-success">有效 </span>
                        </td>
                        <td ng-if="row.status === 100303">
                            <span class="label label-sm label-danger">无效</span>
                        </td>
                        <td ng-if="!row.status"></td>
                        <td ng-if="row.status === 60001">
                            <span class="label label-sm label-danger" ng-if="row.approvalCount && row.approvalCount > 0">审批中</span>
                            <span class="label label-sm label-danger" ng-if="!row.approvalCount || row.approvalCount == 0">待手动提交审批</span>
                        </td>
                        <td>
                            <span ng-show="row.self || row.assistantSelf" ng-bind="row.createdDate" ></span>
                        </td>
                        <td>
                            <div ng-if="row.safekeeping != '1'">
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="ctrl.right.edit && row.status === 100301 && (!row.showType || row.showType == 1)" ng-click="ctrl.delCustomer($index)"><i class="fa fa-times font-red"></i> 删除</a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="ctrl.right.edit && row.lockbillStatus !== 1 && row.status === 100302" ng-click="ctrl.lockOrder(row)"><i class="fa fa-lock font-red"> 锁&nbsp;单</i></a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="ctrl.right.edit && row.lockbillStatus == 1" ng-click="ctrl.unlockLockOrder(row)"><i class="fa fa-lock font-green"> 取消锁单</i></a>
                                <%--<a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="ctrl.right.manage && row.status === 100301 && (!row.showType || row.showType == 1)" ng-click="ctrl.audit($index)"><i class="fa fa-check-square-o font-green"></i> 审核</a>--%>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.showManForm($index)"><i class="fa fa-search font-blue"></i> 联系人</a>
<%--                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.showManForm_zhijie($index)"><i class="fa fa-search fa-maxcdn"></i> 客户子公司</a>--%>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.showSalesAssForm($index)"><i class="fa fa-search font-red"></i> 业务员助理</a>
<%--                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.showCustomerSalesMan($index)"><i class="fa fa-search font-red"></i> 业务员</a>--%>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ngf-select="ctrl.selectPrintFile($file, $invalidFile,$index)"
                                   ngf-max-size="2MB" >
                                    <span class="glyphicon glyphicon-cloud-upload"></span> 上传送货模板
                                </a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="ctrl.approvalFlag && !row.backupTwoFlag && row.status == 60001 && (!row.showType || row.showType == 1) && (!row.approvalCount || row.approvalCount == 0)" ng-click="ctrl.produceApproval($index)"><i class="fa fa-check font-green"></i> 手动审批</a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="ctrl.right.edit && row.status === 100302 && (!row.showType || row.showType == 1)" ng-click="ctrl.distributionCusOpen($index)"><i class="fa fa-check-square-o font-green"></i>分配</a>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="row page-margin-top">
                <div id="step5" class="col-md-12 col-lg-6">
                    <span class="inline">每页</span>
                    <select  class="form-control inline table-pageSize-width"
                             ng-model="ctrl.page.pageSize"
                             ng-change="ctrl.pageSizeChange()"
                             ng-options="pageSizeOption for pageSizeOption in ctrl.page.pageSizeOptions"
                             disable-auto-validate="true">
                    </select>
                    <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{ctrl.page.data.startCount}} / {{ctrl.page.data.endCount}} 条，共 {{ctrl.page.data.count}} 条</span>
                </div>
                <div id="step4" class="col-md-12 col-lg-6">
                    <paging class="pull-right" page="ctrl.page.data.pageNo" page-size="ctrl.page.data.pageSize"
                            total="ctrl.page.data.count" adjacent="1" dots="..." scroll-top="false" hide-if-empty="false"
                            ul-class="pagination" active-class="active" disabled-class="disabled"
                            show-prev-next="true" paging-action="ctrl.doPage(page, pageSize, total)">
                    </paging>
                </div>
            </div>
        </div>
    </div>
    </div>
</tab>