package com.kyb.pcberp.modules.quality.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.sys.entity.User;

public class StockDiscard extends DataEntity<StockDiscard>
{

    
    private static final long serialVersionUID = -2911171486209528908L;
    
    private String type;
    
    private String materialId;
    
    private BigDecimal quantity;
    
    private BigDecimal price;
    
    private BigDecimal subTotal;
    
    private String discardCause;
    
    private String priceRemark;
    
    private String status;
    
    private String madeUserId;
    
    private String auditUserId;
    
    private String auditDate;
    
    private Date endDate;
    
    private Date startDate;
    
    private String no;
    
    private String craftNo;
    
    private String madeUserName;
    
    private Date checkDate;
    
    private User madeUser;
    
    private Material material;
    
    public User getMadeUser()
    {
        return madeUser;
    }

    public void setMadeUser(User madeUser)
    {
        this.madeUser = madeUser;
    }

    public Material getMaterial()
    {
        return material;
    }

    public void setMaterial(Material material)
    {
        this.material = material;
    }

    public Date getCheckDate()
    {
        return checkDate;
    }

    public void setCheckDate(Date checkDate)
    {
        this.checkDate = checkDate;
    }

    public String getMadeUserName()
    {
        return madeUserName;
    }

    public void setMadeUserName(String madeUserName)
    {
        this.madeUserName = madeUserName;
    }

    public String getNo()
    {
        
        return no;
    }

    public void setNo(String no)
    {
        this.no = no;
    }

    public String getCraftNo()
    {
        return craftNo;
    }

    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }

    public Date getEndDate()
    {
        return endDate;
    }

    public void setEndDate(Date endDate)
    {
        this.endDate = endDate;
    }

    public Date getStartDate()
    {
        return startDate;
    }

    public void setStartDate(Date startDate)
    {
        this.startDate = startDate;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public String getMaterialId()
    {
        return materialId;
    }

    public void setMaterialId(String materialId)
    {
        this.materialId = materialId;
    }

    public BigDecimal getQuantity()
    {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity)
    {
        this.quantity = quantity;
    }

    public BigDecimal getPrice()
    {
        return price;
    }

    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    public BigDecimal getSubTotal()
    {
        return subTotal;
    }

    public void setSubTotal(BigDecimal subTotal)
    {
        this.subTotal = subTotal;
    }

    public String getDiscardCause()
    {
        return discardCause;
    }

    public void setDiscardCause(String discardCause)
    {
        this.discardCause = discardCause;
    }

    public String getPriceRemark()
    {
        return priceRemark;
    }

    public void setPriceRemark(String priceRemark)
    {
        this.priceRemark = priceRemark;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getMadeUserId()
    {
        return madeUserId;
    }

    public void setMadeUserId(String madeUserId)
    {
        this.madeUserId = madeUserId;
    }

    public String getAuditUserId()
    {
        return auditUserId;
    }

    public void setAuditUserId(String auditUserId)
    {
        this.auditUserId = auditUserId;
    }

    public String getAuditDate()
    {
        return auditDate;
    }

    public void setAuditDate(String auditDate)
    {
        this.auditDate = auditDate;
    }
    
    

}
