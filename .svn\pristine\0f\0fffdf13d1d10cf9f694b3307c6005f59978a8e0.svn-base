/* reportEgCtrl */
kybApp.controller('reportsaleCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil', 'BaseUtil', '$filter', function ($rootScope, $scope, upida, $timeout, CommonUtil, BaseUtil, $filter) {

    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();

        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });

    var vm = this;
    
    
    $scope.option = {pur:{}, pro:{},pvo:{},salesmanWeek:{},salesmanMonth:{},salesmanYear:{},customerWeek:{},customerMonth:{},customerYear:{}};
    //销售
    $scope.option.pro.years = [];
    $scope.option.pro.year = "";
    
    $scope.option.pur.years = [];
    $scope.option.pur.year = "";
    $scope.option.pur.months = [];
    $scope.option.pur.month = "";
    
    $scope.option.pvo.years = [];
    $scope.option.pvo.year = "";
    //业务员
    $scope.option.salesmanWeek.weeks = [];
    $scope.option.salesmanWeek.week = "";
    $scope.option.salesmanWeek.months = [];
    $scope.option.salesmanWeek.month = "";
    $scope.option.salesmanWeek.years = [];
    $scope.option.salesmanWeek.year = "";
    
    $scope.option.salesmanMonth.weeks = [];
    $scope.option.salesmanMonth.week = "";
    $scope.option.salesmanMonth.months = [];
    $scope.option.salesmanMonth.month = "";
    $scope.option.salesmanMonth.years = [];
    $scope.option.salesmanMonth.year = "";
    
    $scope.option.salesmanYear.weeks = [];
    $scope.option.salesmanYear.week = "";
    $scope.option.salesmanYear.months = [];
    $scope.option.salesmanYear.month = "";
    $scope.option.salesmanYear.years = [];
    $scope.option.salesmanYear.year = "";
    //客户customer
    $scope.option.customerWeek.weeks = [];
    $scope.option.customerWeek.week = "";
    $scope.option.customerWeek.months = [];
    $scope.option.customerWeek.month = "";
    $scope.option.customerWeek.years = [];
    $scope.option.customerWeek.year = "";
    
    $scope.option.customerMonth.weeks = [];
    $scope.option.customerMonth.week = "";
    $scope.option.customerMonth.months = [];
    $scope.option.customerMonth.month = "";
    $scope.option.customerMonth.years = [];
    $scope.option.customerMonth.year = "";
    
    $scope.option.customerYear.weeks = [];
    $scope.option.customerYear.week = "";
    $scope.option.customerYear.months = [];
    $scope.option.customerYear.month = "";
    $scope.option.customerYear.years = [];
    $scope.option.customerYear.year = "";
    
    $scope.customerName="";
    
    $scope.query = {}; // 查询对象
    
    $scope.query.genSelected = -1;
    $scope.query.genList = [];
    
    $scope.query.salesmanGenSelected = -1;
    $scope.query.salesmanGenList = [];
    
    $scope.query.customerGenSelected = -1;
    $scope.query.customerGenList = [];
    
    $scope.option.day=new Date().getDate();
    //总览
    $scope.showWeekOfMonth = false;
    $scope.showDayOfWeek = true;
    $scope.showMonthOfYear = false;
    //业务员-排行榜
    $scope.showDayRankingsOfMan = false;
    $scope.showWeekRankingsOfMan = true;
    $scope.showMonthRankingsOfMan = false;
    $scope.showYearRankingsOfMan = false;
    //客户-排行榜
    $scope.showDayRankingsOfCustomer = false;
    $scope.showWeekRankingsOfCustomer = true;
    $scope.showMonthRankingsOfCustomer = false;
    $scope.showYearRankingsOfCustomer = false;
    //总览每周的日数据
    $scope.daysOfWeek = []; // 一周的每天
    $scope.currDay = $filter('date')(new Date(), 'yyyy-MM-dd');
    $scope.currWeekData = {};
    $scope.charOptionDayOfWeek = {
        id: "dayofweek",
        type: "serial",
        theme: "light",
        valueField: "count",
        titleField: "name"
    };
    //总览每月的周数据
    $scope.daysOfMonth = []; // 一年的每月一月的每周
    $scope.currWeek = 0;
    $scope.currMonthData = {};
    $scope.charOptionDayOfMonth = {
    		id: "dayofmonth",
    		type: "serial",
    		theme: "light",
    		valueField: "count",
    		titleField: "name"
    };
    //总览每年的月数据
    $scope.monthOfYear = []; // 
    $scope.currYearData = {};
    $scope.charOptionMonthOfYear = {
    		id: "monthofyear",
    		type: "serial",
    		theme: "light",
    		valueField: "count",
    		titleField: "name"
    };
    
    //业务员排行榜列表展示数据
    $scope.rankingsOfManDatas = [];
    //业务员年排行
    $scope.yearRankingsOfMan = [];
    $scope.charOptionYearRankingsOfMan = {
        id: "yearrankingsofman",
        type: "serial",
        theme: "light",
        valueField: "count",
        titleField: "name"
    };
    //业务员每年的月排行
    $scope.monthRankingsOfMan = [];
    $scope.charOptionMonthRankingsOfMan = {
		id: "monthrankingsofman",
		type: "serial",
		theme: "light",
		valueField: "count",
		titleField: "name"
    };
    //业务员每月的周排行
    $scope.weekRankingsOfMan = [];
    $scope.charOptionWeekRankingsOfMan = {
        id: "weekrankingsofman",
        type: "serial",
        theme: "light",
        valueField: "count",
        titleField: "name"
    };
    //业务员每周的天排行
    $scope.dayRankingsOfMan = [];
    $scope.charOptionDayRankingsOfMan = {
        id: "dayrankingsofman",
        type: "serial",
        theme: "light",
        valueField: "count",
        titleField: "name"
    };
    //点击Tab初始业务员周排行报表
    $scope.initManRankings=function(){
    	//初始客户销售排行榜
        $scope.getWeekRankingsOfManDatas();
    }
    //客户年排行
    $scope.yeaRankingsOfCustomer = [];
    $scope.charOptionYearRankingsOfCustomer = {
		id: "yearrankingsofcustomer",
		type: "serial",
		theme: "light",
		valueField: "count",
		titleField: "name"
    };
    //客户每年的月排行
    $scope.monthRankingsOfCustomer = [];
    $scope.charOptionMonthRankingsOfCustomer = {
		id: "monthrankingsofcustomer",
		type: "serial",
		theme: "light",
		valueField: "count",
		titleField: "name"
    };
    //客户每月的周排行
    $scope.weekRankingsOfCustomer = [];
    $scope.charOptionWeekRankingsOfCustomer = {
        id: "weekrankingsofcustomer",
        type: "serial",
        theme: "light",
        valueField: "count",
        titleField: "name"
    };
    //客户每周的日排行
    $scope.dayRankingsOfCustomer = [];
    $scope.charOptionDayRankingsOfCustomer = {
        id: "dayrankingsofcustomer",
        type: "serial",
        theme: "light",
        valueField: "count",
        titleField: "name"
    };
    //点击Tab初始客户周排行报表
    $scope.initCustomerRankings=function(){
    	//初始客户销售排行榜
        $scope.getWeekRankingsOfCustomerDatas();
    }
    $scope.getWeekDatas = function(){
    	$scope.showWeekOfMonth = false;
    	$scope.showMonthOfYear = false;
    	$scope.showDayOfWeek = true;
        MainCtrl.blockUI({
    	    animate: true,
    	});
        var reqObj = {};
    	reqObj.year = $scope.option.pro.year;
    	reqObj.month = $scope.option.pro.month;
    	reqObj.week = $scope.query.genSelected;
    	upida.post("report/sale/daysofweeks", reqObj).then(function(result){
    		$scope.daysOfWeek = [];
    		$scope.query.genList=result.option.weeks;
    		$scope.query.genSelected=result.option.week;
	    	angular.forEach(result.days, function(p){
		        	$scope.daysOfWeek.push({
		        		"date": $filter('date')(p.day, 'yyyy-MM-dd'),
		        		"orderArea": p.orderArea ? p.orderArea: 0,
		                "saleArea": p.saleArea ? p.saleArea: 0,
		                "orderNum": p.orderNum ? p.orderNum: 0,
		                "sampleNum": p.sampleNum ? p.sampleNum : 0,
		        		"sampleRatio": p.sampleRatio ? p.sampleRatio : 0,
		        		"orderMoney": p.orderMoney ? p.orderMoney: 0,
		        		"saleMoney": p.saleMoney ? p.saleMoney: 0,
						"punctualDelRatio": p.punctualDelRatio ? p.punctualDelRatio : 0,
						"rejectRatio": p.rejectRatio ? p.rejectRatio : 0
		        	});
    		 });
        	MainCtrl.unblockUI();
        });
    };
    // 加载总览本月的周数据
    $scope.getMonthDatas = function(){
    	$scope.showDayOfWeek = false;
    	$scope.showMonthOfYear = false;
    	$scope.showWeekOfMonth = true;
		MainCtrl.blockUI({
    	    animate: true,
    	});
		var reqObj = {};
    	
    	reqObj.year = $scope.option.pur.year;
    	reqObj.month = $scope.option.pur.month;
		upida.post("report/sale/daysofmonths", reqObj).then(function(result) {
        	$scope.daysOfMonth = [];
            angular.forEach(result.currMonth, function(p){
            	$scope.daysOfMonth.push({
            		"date": $filter('date')(p.day, 'yyyy-MM-dd'),
	        		"orderArea": p.orderArea ? p.orderArea: 0,
	                "saleArea": p.saleArea ? p.saleArea: 0,
	                "orderNum": p.orderNum ? p.orderNum: 0,
	                "sampleNum": p.sampleNum ? p.sampleNum : 0,
	        		"sampleRatio": p.sampleRatio ? p.sampleRatio : 0,
	        		"orderMoney": p.orderMoney ? p.orderMoney: 0,
	        		"saleMoney": p.saleMoney ? p.saleMoney: 0,
					"punctualDelRatio": p.punctualDelRatio ? p.punctualDelRatio : 0,
					"rejectRatio": p.rejectRatio ? p.rejectRatio : 0
            	});
            });
            MainCtrl.unblockUI();
		})
       
    };
    
    // 加载总览本年的月数据
    $scope.getYearDatas = function(){
    	$scope.showDayOfWeek = false;
    	$scope.showWeekOfMonth = false;
    	$scope.showMonthOfYear = true;
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	var reqObj = {};
    	reqObj.year = $scope.option.pvo.year;
    	upida.post("report/sale/monthofyear", reqObj).then(function(result) {
    		$scope.monthOfYear = [];
            angular.forEach(result.months, function(p){
            	$scope.monthOfYear.push({
            		"month": p.month + " 月",
        			"orderArea": p.orderArea ? p.orderArea: 0,
                    "saleArea": p.saleArea ? p.saleArea: 0,
                    "orderNum": p.orderNum ? p.orderNum: 0,
                    "sampleNum": p.sampleNum ? p.sampleNum : 0,
            		"sampleRatio": p.sampleRatio ? p.sampleRatio : 0,
            		"orderMoney": p.orderMoney ? p.orderMoney: 0,
            		"saleMoney": p.saleMoney ? p.saleMoney: 0,
    				"punctualDelRatio": p.punctualDelRatio ? p.punctualDelRatio : 0,
    				"rejectRatio": p.rejectRatio ? p.rejectRatio : 0
            	});
            });
            MainCtrl.unblockUI();
    	});
    };
    
    /**
     * 业务员查询年所有
     */
    $scope.doQuerySalesmanYear=function()
    {
    	$scope.getYearRankingsOfManDatas(0);
    }
    
    /**初始业务员销售额年排行榜**/
    $scope.getYearRankingsOfManDatas=function(pageSize)
    {
    	$scope.showDayRankingsOfMan = false;
        $scope.showWeekRankingsOfMan = false;
        $scope.showMonthRankingsOfMan = false;
        $scope.showYearRankingsOfMan = true;
        MainCtrl.blockUI({
    	    animate: true,
    	});
    	var reqObj = {};
    	reqObj.year = $scope.option.salesmanYear.year;
    	reqObj.pageSize = pageSize;
		upida.post("report/sale/salesmanMonthofyear", reqObj).then(function(result) {
			$scope.yearRankingsOfMan = [];
			$scope.yearRankingsOfMans = [];
			 angular.forEach(result.year, function(p){
		        	$scope.yearRankingsOfMan.push({
		        		"salesman": p.salesmanName,
		        		"orderArea": p.orderArea ? p.orderArea: 0,
		        		"orderMoney": p.orderMoney ? p.orderMoney: 0,
						"orderNum": p.orderNum ? p.orderNum: 0,
		                "productionNum": p.productionNum ? p.productionNum: 0,
		                "sampleNum": p.sampleNum ? p.sampleNum : 0,
		        		"freeSampleNum": p.freeSampleNum ? p.freeSampleNum : 0,
						"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0
		        	});
		        });
			 angular.forEach(result.days, function(p){
		        	$scope.yearRankingsOfMans.push({
		        		"salesman": p.salesmanName,
		        		"orderArea": p.orderArea ? p.orderArea: 0,
		        		"orderMoney": p.orderMoney ? p.orderMoney: 0,
						"orderNum": p.orderNum ? p.orderNum: 0,
		                "productionNum": p.productionNum ? p.productionNum: 0,
		                "sampleNum": p.sampleNum ? p.sampleNum : 0,
		        		"freeSampleNum": p.freeSampleNum ? p.freeSampleNum : 0,
						"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0
		        	});
		        });
		        $scope.rankingsOfManDatas = $scope.yearRankingsOfMans ;
		        MainCtrl.unblockUI();
		});
    };
    
    /**
     * 业务员查询年所有
     */
    $scope.doQuerySalesmanMonth=function()
    {
    	$scope.getMonthRankingsOfManDatas(0);
    } 
    
    /**初始业务员销售额月排行榜**/
    $scope.getMonthRankingsOfManDatas=function(pageSize){
    	$scope.showDayRankingsOfMan = false;
    	$scope.showWeekRankingsOfMan = false;
    	$scope.showMonthRankingsOfMan = true;
    	$scope.showYearRankingsOfMan = false;
    	MainCtrl.blockUI({
    	    animate: true,
    	}); 
    	var reqObj = {};
    	reqObj.year = $scope.option.salesmanMonth.year;
    	reqObj.month = $scope.option.salesmanMonth.month;
    	reqObj.pageSize = pageSize;
    	upida.post("report/sale/salesmanDaysofmonth", reqObj).then(function(result) {
    		$scope.monthRankingsOfMan = [];
    		$scope.monthRankingsOfMans = [];
    		angular.forEach(result.days, function(p){
        		$scope.monthRankingsOfMan.push({
        			"salesman": p.salesmanName,
        			"orderArea": p.orderArea ? p.orderArea: 0,
    				"orderMoney": p.orderMoney ? p.orderMoney: 0,
    				"orderNum": p.orderNum ? p.orderNum: 0,
    				"productionNum": p.productionNum ? p.productionNum: 0,
    				"sampleNum": p.sampleNum ? p.sampleNum : 0,
    				"freeSampleNum": p.freeSampleNum ? p.freeSampleNum : 0,
    				"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0
        		});
        	});
    		angular.forEach(result.month, function(p){
        		$scope.monthRankingsOfMans.push({
        			"salesman": p.salesmanName,
        			"orderArea": p.orderArea ? p.orderArea: 0,
    				"orderMoney": p.orderMoney ? p.orderMoney: 0,
    				"orderNum": p.orderNum ? p.orderNum: 0,
    				"productionNum": p.productionNum ? p.productionNum: 0,
    				"sampleNum": p.sampleNum ? p.sampleNum : 0,
    				"freeSampleNum": p.freeSampleNum ? p.freeSampleNum : 0,
    				"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0
        		});
        	});
        	$scope.rankingsOfManDatas = $scope.monthRankingsOfMans ;
        	 MainCtrl.unblockUI();
    	});
    };
    /**
     * 业务员查询周所有
     */
    $scope.doQuerySalesmanWeek=function()
    {
    	$scope.getWeekRankingsOfManDatas(0);
    } 
    /**初始业务员销售额周排行榜**/
    $scope.getWeekRankingsOfManDatas=function(pageSize){
    	$scope.showDayRankingsOfMan = false;
    	$scope.showWeekRankingsOfMan = true;
    	$scope.showMonthRankingsOfMan = false;
    	$scope.showYearRankingsOfMan = false;
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	var reqObj = {};
    	reqObj.week = $scope.query.salesmanGenSelected;
    	reqObj.year = $scope.option.salesmanWeek.year;
    	reqObj.pageSize = pageSize;
    	upida.post("report/sale/salesmanDaysofweek", reqObj).then(function(result) {
    		$scope.weekRankingsOfMan = [];
    		$scope.weekRankingsOfMans = [];
      	    $scope.query.salesmanGenList=result.option.weeks;
      	  $scope.query.salesmanGenSelected=result.option.week;
    		angular.forEach(result.days, function(p){
        		$scope.weekRankingsOfMan.push({
        			"date": $filter('date')(p.day, 'yyyy-MM-dd'),
        			"salesman": p.salesmanName,
        			"orderArea": p.orderArea ? p.orderArea: 0,
    				"orderMoney": p.orderMoney ? p.orderMoney: 0,
    				"orderNum": p.orderNum ? p.orderNum: 0,
    				"productionNum": p.productionNum ? p.productionNum: 0,
    				"sampleNum": p.sampleNum ? p.sampleNum : 0,
    				"freeSampleNum": p.freeSampleNum ? p.freeSampleNum : 0,
    				"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0
        		});
        	});
    		angular.forEach(result.week, function(p){
        		$scope.weekRankingsOfMans.push({
        			"date": $filter('date')(p.day, 'yyyy-MM-dd'),
        			"salesman": p.salesmanName,
        			"orderArea": p.orderArea ? p.orderArea: 0,
    				"orderMoney": p.orderMoney ? p.orderMoney: 0,
    				"orderNum": p.orderNum ? p.orderNum: 0,
    				"productionNum": p.productionNum ? p.productionNum: 0,
    				"sampleNum": p.sampleNum ? p.sampleNum : 0,
    				"freeSampleNum": p.freeSampleNum ? p.freeSampleNum : 0,
    				"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0
        		});
        	});
        	$scope.rankingsOfManDatas = $scope.weekRankingsOfMans;
        	 MainCtrl.unblockUI();
    	});
    };
    /**初始业务员销售额日排行榜**/
    $scope.getDayRankingsOfManDatas=function(){
    	$scope.showDayRankingsOfMan = true;
        $scope.showWeekRankingsOfMan = false;
        $scope.showMonthRankingsOfMan = false;
        $scope.showYearRankingsOfMan = false;
		MainCtrl.blockUI({
    	    animate: true,
    	});
    	var reqObj = {};
    	reqObj.week = $scope.option.salesmanWeek.week;
    	reqObj.year = $scope.option.salesmanWeek.year;
    	reqObj.month = $scope.option.salesmanWeek.month;
    	upida.post("report/sale/dayRankingsOfMan", reqObj).then(function(result) {
    		$scope.dayRankingsOfMan = [];
    		angular.forEach(result.days, function(p){
            	$scope.dayRankingsOfMan.push({
            		"salesman": p.salesmanName,
            		"orderArea": p.orderArea ? p.orderArea: 0,
            		"orderMoney": p.orderMoney ? p.orderMoney: 0,
    				"orderNum": p.orderNum ? p.orderNum: 0,
                    "productionNum": p.productionNum ? p.productionNum: 0,
                    "sampleNum": p.sampleNum ? p.sampleNum : 0,
            		"freeSampleNum": p.freeSampleNum ? p.freeSampleNum : 0,
    				"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0
            	});
            });
            $scope.rankingsOfManDatas = $scope.dayRankingsOfMan;
            MainCtrl.unblockUI();
    	});
    };
    
    $scope.doQuery=function()
    {
    	 $scope.getYearRankingsOfCustomerDatas(0);
    }
    
   
    
    /**初始客户销售额年排行榜**/
    $scope.getYearRankingsOfCustomerDatas=function(pageSize){
    	$scope.showDayRankingsOfCustomer = false;
        $scope.showWeekRankingsOfCustomer = false;
        $scope.showMonthRankingsOfCustomer = false;
        $scope.showYearRankingsOfCustomer = true;
		MainCtrl.blockUI({
    	    animate: true,
    	});
    	var reqObj = {};
    	reqObj.year = $scope.option.customerYear.year;
    	reqObj.pageSize = pageSize;
    	upida.post("report/sale/customerMonthofyear",reqObj).then(function(result) {
    		$scope.yearRankingsOfCustomer = [];
    		 angular.forEach(result.days, function(p){
    			 if(p.customerName.substring(2,3)=="市"){
    				 if(p.customerName.indexOf("有")>0){
        				 customerName= p.customerName.substring(3, p.customerName.indexOf("有"));
        			 }else{
        				 customerName=p.customerName;
        			 } 
    			 }else{
    				 if(p.customerName.indexOf("有")>0){
        				 customerName= p.customerName.substring(0, p.customerName.indexOf("有"));
        			 }else{
        				 customerName=p.customerName;
        			 } 
    			 }
    	        	$scope.yearRankingsOfCustomer.push({
    	        		"customer": customerName,
    	                "saleMoney": p.saleMoney ? p.saleMoney: 0,
    	        		"orderMoney": p.orderMoney ? p.orderMoney: 0,
    					"orderArea": p.orderArea ? p.orderArea: 0,
    	                "orderNum": p.orderNum ? p.orderNum: 0,
    	                "sampleNum": p.sampleNum ? p.sampleNum : 0,
    					"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0,
    					"toProdRatio": p.toProdRatio ? p.toProdRatio : 0,
    					"rejectNum": p.rejectNum ? p.rejectNum : 0,
    	                "rejectRatio": p.rejectRatio ? p.rejectRatio : 0,
    	                "punctualDelRatio": p.punctualDelRatio ? p.punctualDelRatio : 0
    	        	});
    	        });
    		 $scope.rankingsOfCustomerDatas=[];
    		 angular.forEach(result.year, function(p){
    			 if(p.customerName.substring(2,3)=="市"){
    				 if(p.customerName.indexOf("有")>0){
        				 customerName= p.customerName.substring(3, p.customerName.indexOf("有"));
        			 }else{
        				 customerName=p.customerName;
        			 } 
    			 }else{
    				 if(p.customerName.indexOf("有")>0){
        				 customerName= p.customerName.substring(0, p.customerName.indexOf("有"));
        			 }else{
        				 customerName=p.customerName;
        			 } 
    			 }
    	        	$scope.rankingsOfCustomerDatas.push({
    	        		"customer": customerName,
    	                "saleMoney": p.saleMoney ? p.saleMoney: 0,
    	        		"orderMoney": p.orderMoney ? p.orderMoney: 0,
    					"orderArea": p.orderArea ? p.orderArea: 0,
    	                "orderNum": p.orderNum ? p.orderNum: 0,
    	                "sampleNum": p.sampleNum ? p.sampleNum : 0,
    					"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0,
    					"toProdRatio": p.toProdRatio ? p.toProdRatio : 0,
    					"rejectNum": p.rejectNum ? p.rejectNum : 0,
    	                "rejectRatio": p.rejectRatio ? p.rejectRatio : 0,
    	                "punctualDelRatio": p.punctualDelRatio ? p.punctualDelRatio : 0
    	        	});
    	        });
    	        MainCtrl.unblockUI();
    	});
    };
    
    $scope.monthDoQuerys=function()
    {
    	 $scope.getMonthRankingsOfCustomerDatas(0);
    }
    
    /**初始客户销售额月排行榜**/
    $scope.getMonthRankingsOfCustomerDatas=function(pageSize){
    	$scope.showDayRankingsOfCustomer = false;
    	$scope.showWeekRankingsOfCustomer = false;
    	$scope.showMonthRankingsOfCustomer = true;
    	$scope.showYearRankingsOfCustomer = false;
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	var reqObj = {};
    	reqObj.year = $scope.option.customerMonth.year;
    	reqObj.month = $scope.option.customerMonth.month;
    	reqObj.pageSize = pageSize;
    	upida.post("report/sale/customerDaysofmonth", reqObj).then(function(result) {
    		$scope.monthRankingsOfCustomer = [];
    		angular.forEach(result.month, function(p){
    			if(p.customerName.substring(2,3)=="市"){
	   				 if(p.customerName.indexOf("有")>0){
	       				 customerName= p.customerName.substring(3, p.customerName.indexOf("有"));
	       			 }else{
	       				 customerName=p.customerName;
	       			 } 
	   			 }else{
	   				 if(p.customerName.indexOf("有")>0){
	       				 customerName= p.customerName.substring(0, p.customerName.indexOf("有"));
	       			 }else{
	       				 customerName=p.customerName;
	       			 } 
	   			 }
        	$scope.monthRankingsOfCustomer.push({
        			"customer": customerName,
        			"saleMoney": p.saleMoney ? p.saleMoney: 0,
    				"orderMoney": p.orderMoney ? p.orderMoney: 0,
    				"orderArea": p.orderArea ? p.orderArea: 0,		
    				"orderNum": p.orderNum ? p.orderNum: 0,
    				"sampleNum": p.sampleNum ? p.sampleNum : 0,
    				"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0,
    				"toProdRatio": p.toProdRatio ? p.toProdRatio : 0,
    				"rejectNum": p.rejectNum ? p.rejectNum : 0,
    				"rejectRatio": p.rejectRatio ? p.rejectRatio : 0,
    				"punctualDelRatio": p.punctualDelRatio ? p.punctualDelRatio : 0
        		});
        	});
    		
    		 $scope.rankingsOfCustomerDatas=[];
    		 angular.forEach(result.days, function(p){
    			 if(p.customerName.substring(2,3)=="市"){
    				 if(p.customerName.indexOf("有")>0){
        				 customerName= p.customerName.substring(3, p.customerName.indexOf("有"));
        			 }else{
        				 customerName=p.customerName;
        			 } 
    			 }else{
    				 if(p.customerName.indexOf("有")>0){
        				 customerName= p.customerName.substring(0, p.customerName.indexOf("有"));
        			 }else{
        				 customerName=p.customerName;
        			 } 
    			 }
    	        	$scope.rankingsOfCustomerDatas.push({
    	        		"customer": customerName,
    	                "saleMoney": p.saleMoney ? p.saleMoney: 0,
    	        		"orderMoney": p.orderMoney ? p.orderMoney: 0,
    					"orderArea": p.orderArea ? p.orderArea: 0,
    	                "orderNum": p.orderNum ? p.orderNum: 0,
    	                "sampleNum": p.sampleNum ? p.sampleNum : 0,
    					"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0,
    					"toProdRatio": p.toProdRatio ? p.toProdRatio : 0,
    					"rejectNum": p.rejectNum ? p.rejectNum : 0,
    	                "rejectRatio": p.rejectRatio ? p.rejectRatio : 0,
    	                "punctualDelRatio": p.punctualDelRatio ? p.punctualDelRatio : 0
    	        	});
    	        });
        	MainCtrl.unblockUI();
    	});
    };
    
    $scope.weekDoQuerys=function()
    {
    	 $scope.getWeekRankingsOfCustomerDatas(0);
    }
    
    /**初始客户销售额周排行榜**/
    $scope.getWeekRankingsOfCustomerDatas=function(pageSize){
    	$scope.showDayRankingsOfCustomer = false;
    	$scope.showWeekRankingsOfCustomer = true;
    	$scope.showMonthRankingsOfCustomer = false;
    	$scope.showYearRankingsOfCustomer = false;
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	var reqObj = {};
    	reqObj.year = $scope.option.customerWeek.year;
    	reqObj.week = $scope.query.customerGenSelected;
    	reqObj.pageSize = pageSize;
    	upida.post("report/sale/customerDaysofweek", reqObj).then(function(result) {
    		$scope.weekRankingsOfCustomer = [];
    		$scope.query.customerGenList=result.option.weeks;
    	    $scope.query.customerGenSelected=result.option.week;
    		angular.forEach(result.week, function(p){
    			if(p.customerName.substring(2,3)=="市"){
	   				 if(p.customerName.indexOf("有")>0){
	       				 customerName= p.customerName.substring(3, p.customerName.indexOf("有"));
	       			 }else{
	       				 customerName=p.customerName;
	       			 } 
	   			 }else{
	   				 if(p.customerName.indexOf("有")>0){
	       				 customerName= p.customerName.substring(0, p.customerName.indexOf("有"));
	       			 }else{
	       				 customerName=p.customerName;
	       			 } 
	   			 }
        		$scope.weekRankingsOfCustomer.push({
        			"customer": customerName,
        			"saleMoney": p.saleMoney ? p.saleMoney: 0,
    				"orderMoney": p.orderMoney ? p.orderMoney: 0,
    				"orderArea": p.orderArea ? p.orderArea: 0,
    				"orderNum": p.orderNum ? p.orderNum: 0,
    				"sampleNum": p.sampleNum ? p.sampleNum : 0,
    				"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0,
    				"toProdRatio": p.toProdRatio ? p.toProdRatio : 0,
    				"rejectNum": p.rejectNum ? p.rejectNum : 0,
    				"rejectRatio": p.rejectRatio ? p.rejectRatio : 0,
    				"punctualDelRatio": p.punctualDelRatio ? p.punctualDelRatio : 0
        		});
        	});
    		 $scope.rankingsOfCustomerDatas=[];
    		 angular.forEach(result.days, function(p){
    			 if(p.customerName.substring(2,3)=="市"){
    				 if(p.customerName.indexOf("有")>0){
        				 customerName= p.customerName.substring(3, p.customerName.indexOf("有"));
        			 }else{
        				 customerName=p.customerName;
        			 } 
    			 }else{
    				 if(p.customerName.indexOf("有")>0){
        				 customerName= p.customerName.substring(0, p.customerName.indexOf("有"));
        			 }else{
        				 customerName=p.customerName;
        			 } 
    			 }
    	        	$scope.rankingsOfCustomerDatas.push({
    	        		"customer": customerName,
    	                "saleMoney": p.saleMoney ? p.saleMoney: 0,
    	        		"orderMoney": p.orderMoney ? p.orderMoney: 0,
    					"orderArea": p.orderArea ? p.orderArea: 0,
    	                "orderNum": p.orderNum ? p.orderNum: 0,
    	                "sampleNum": p.sampleNum ? p.sampleNum : 0,
    					"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0,
    					"toProdRatio": p.toProdRatio ? p.toProdRatio : 0,
    					"rejectNum": p.rejectNum ? p.rejectNum : 0,
    	                "rejectRatio": p.rejectRatio ? p.rejectRatio : 0,
    	                "punctualDelRatio": p.punctualDelRatio ? p.punctualDelRatio : 0
    	        	});
    	        });
        	MainCtrl.unblockUI();
    	});
    };
    /**初始客户销售额日排行榜**/
    $scope.getDayRankingsOfCustomerDatas=function(){
    	$scope.showDayRankingsOfCustomer = true;
        $scope.showWeekRankingsOfCustomer = false;
        $scope.showMonthRankingsOfCustomer = false;
        $scope.showYearRankingsOfCustomer = false;
		MainCtrl.blockUI({
    	    animate: true,
    	});
    	var reqObj = {};
    	reqObj.year = $scope.option.customerWeek.year;
    	reqObj.month = $scope.option.customerWeek.month;
    	reqObj.week = $scope.option.customerWeek.week;
		upida.post("report/sale/rankingsOfCustomer", reqObj).then(function(result) {
			 $scope.dayRankingsOfCustomer = [];
			 angular.forEach(result.days, function(p){
					if(p.customerName.substring(2,3)=="市"){
		   				 if(p.customerName.indexOf("有")>0){
		       				 customerName= p.customerName.substring(3, p.customerName.indexOf("有"));
		       			 }else{
		       				 customerName=p.customerName;
		       			 } 
		   			 }else{
		   				 if(p.customerName.indexOf("有")>0){
		       				 customerName= p.customerName.substring(0, p.customerName.indexOf("有"));
		       			 }else{
		       				 customerName=p.customerName;
		       			 } 
		   			 }
		        	$scope.dayRankingsOfCustomer.push({
		        		"customer": customerName,
		        		"saleMoney": p.saleMoney ? p.saleMoney: 0,
		        		"orderMoney": p.orderMoney ? p.orderMoney: 0,
						"orderArea": p.orderArea ? p.orderArea: 0,
		                "orderNum": p.orderNum ? p.orderNum: 0,
		                "sampleNum": p.sampleNum ? p.sampleNum : 0,
						"sampleToProdNum": p.sampleToProdNum ? p.sampleToProdNum : 0,
						"toProdRatio": p.toProdRatio ? p.toProdRatio : 0,
						"rejectNum": p.rejectNum ? p.rejectNum : 0,
		                "rejectRatio": p.punctualDelNum ? p.punctualDelNum : 0,
		                "punctualDelRatio": p.punctualDelRatio ? p.punctualDelRatio : 0
		        	});
		        });
		        $scope.rankingsOfCustomerDatas = $scope.dayRankingsOfCustomer;
		        MainCtrl.unblockUI();
		});
    };
    function loadData() {
    	//初始销售总览信息
    	
    	$scope.option.pro.weeks = [];
    	$scope.option.pro.months = [];
    	$scope.option.pro.years = [];
    	
    	upida.get("report/sale/yearmonthweek").then(function(result){
    	    
    	    $scope.option.pro.weeks = result.weeks;
    	    $scope.option.pro.week = result.week;
    	    $scope.option.pro.months = result.months;
    	    $scope.option.pro.month = result.month;
    	    $scope.option.pro.years = result.years;
    	    $scope.option.pro.year = result.year;
    	    $scope.query.month=result.month;
    	    
    	    
    	    $scope.query.genSelected=result.week;
    	    $scope.query.salesmanGenSelected=result.week;
    	    $scope.query.customerGenSelected=result.week;
    	    
    	    $scope.query.genList=result.weeks;
    	    $scope.query.salesmanGenList=result.weeks;
    	    $scope.query.customerGenList=result.weeks;
    	    
    	    
    	    $scope.option.pur.months = result.months;
    	    $scope.option.pur.month = result.month;
    	    $scope.option.pur.years = result.years;
    	    $scope.option.pur.year = result.year;
    	  
    	    $scope.option.pvo.years = result.years;
    	    $scope.option.pvo.year = result.year;
    	    
    	    
    	    $scope.option.salesmanWeek.weeks = result.weeks;
    	    $scope.option.salesmanWeek.week = result.week;
    	    $scope.option.salesmanWeek.months = result.months;
    	    $scope.option.salesmanWeek.month = result.month;
    	    $scope.option.salesmanWeek.years = result.years;
    	    $scope.option.salesmanWeek.year = result.year;
    	    
    	    
    	    $scope.option.salesmanMonth.months = result.months;
    	    $scope.option.salesmanMonth.month = result.month;
    	    $scope.option.salesmanMonth.years = result.years;
    	    $scope.option.salesmanMonth.year = result.year;
    	    
    	
    	    $scope.option.salesmanYear.years = result.years;
    	    $scope.option.salesmanYear.year = result.year;
    	    
    	    
    	    $scope.option.customerWeek.weeks = result.weeks;
    	    $scope.option.customerWeek.week = result.week;
    	    $scope.option.customerWeek.months = result.months;
    	    $scope.option.customerWeek.month = result.month;
    	    $scope.option.customerWeek.years = result.years;
    	    $scope.option.customerWeek.year = result.year;
    	    
    	    
    	    $scope.option.customerMonth.months = result.months;
    	    $scope.option.customerMonth.month = result.month;
    	    $scope.option.customerMonth.years = result.years;
    	    $scope.option.customerMonth.year = result.year;
    	    
    	
    	    $scope.option.customerYear.years = result.years;
    	    $scope.option.customerYear.year = result.year;
    	    
    		$scope.getWeekDatas();
    	});
    }
    
    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadData();
    });
}]);