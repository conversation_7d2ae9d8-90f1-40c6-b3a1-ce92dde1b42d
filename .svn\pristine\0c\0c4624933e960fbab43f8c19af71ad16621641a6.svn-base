<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.finance.dao.SinglePayableDetailDao">
	
	<select id="getDetailList" resultType="SinglePayableDetail">
		SELECT
			a.*,
			c.no AS "matNo",
			IFNULL(a.amount,0) - IFNULL(a.confirmAmount,0) AS "singAmount"
		FROM pu_single_payable_detail a
		LEFT JOIN pu_single_payable b ON b.recordId = a.singlePayableId
		LEFT JOIN md_material c ON c.recordId = a.materialId
		LEFT JOIN pu_material_check d ON d.recordId = a.materialCheckId
		LEFT JOIN st_product_store e ON e.recordId = d.sourceId AND a.type = 1
		LEFT JOIN pu_prdorder_detail ppd1 ON ppd1.recordId = e.prdorderDetailId
		LEFT JOIN pu_prdorder_detail ppd2 ON ppd2.recordId = d.prdorderDetailId
		LEFT JOIN pu_returns_detail f ON f.recordId = d.sourceId AND f.purchasingType = 2 AND a.type IN (2,3)
		LEFT JOIN pu_prdorder_detail ppd3 ON ppd3.recordId = f.purchasingDetailId
		LEFT JOIN sl_contract_detail g ON g.recordId = IFNULL(IFNULL(d.contractDetailId,ppd1.contractDetailId),IFNULL(ppd2.contractDetailId,ppd3.contractDetailId)) 
		LEFT JOIN icloud_group_center igc ON igc.recordId=d.groupCenterId
		LEFT JOIN sl_contract_detail h ON h.recordId = IFNULL(igc.recordId,g.groupCenterId) 
		WHERE a.companyId = #{companyId} AND a.activeFlag = 1
		<if test="recordId != null and recordId != ''">
			AND a.singlePayableId = #{recordId}
		</if>
		<if test="saleComId != null and saleComId != ''">
			AND igc.recordId IS NULL AND h.companyId = #{saleComId}
		</if>
	</select>
	
	<delete id="cleanDetail">
		DELETE FROM pu_single_payable_detail
		WHERE singlePayableId = #{recordId}
		<if test="orgList != null and orgList.size > 0">
			AND materialCheckId IN
			(
				SELECT
					recordId
				FROM pu_material_check
				WHERE
				(
				orgId
				) IN
				<foreach collection="orgList" item="item" index="index" open="(" close=")" separator=",">
					#{item}
				</foreach>
			)
		</if>
	</delete>
	
	<insert id="batchInsert">
		INSERT INTO pu_single_payable_detail(
			companyId,
			singlePayableId,
			type,
			materialCheckId,
			materialId,
			billNo,
			specification,
			quantity,
			price,
			engineeringFee,
			testShelfFee,
			mouldFee,
			othersFee,
			amount,
			activeFlag,
			createdBy,
			createdDate,
			status,
			product
		) VALUES
		<foreach collection="mcList" item="item" separator=",">
		(
			#{item.company.recordId},
			#{recordId},
			#{item.type},
			#{item.recordId},
			#{item.material.recordId},
			#{item.billNo},
			#{item.specification},
			#{item.quantity},
			#{item.price},
			#{item.engineeringFee},
			#{item.testShelfFee},
			#{item.mouldFee},
			#{item.othersFee},
			#{item.amount},
			1,
			#{createdBy.recordId},
			NOW(),
			1001,
		 	#{item.product}
		)
		</foreach>
	</insert>
	
	<select id="getSingleDetailByPurOrderId" resultType="SinglePayableDetail">
		SELECT
			a.*,
			IFNULL(a.amount,0) - IFNULL(a.confirmAmount,0) AS "singAmount" 
		FROM pu_single_payable_detail a
		LEFT JOIN pu_material_check b ON b.recordId = a.materialCheckId
		LEFT JOIN md_material mm ON mm.recordId = b.materialId
		LEFT JOIN st_product_store c ON c.recordId = b.sourceId AND c.inOutType = 14 AND mm.materialKind = 100702
		LEFT JOIN st_material_store d ON d.recordId = b.sourceId AND d.inOutType = 1 AND mm.materialKind = 100701
		WHERE a.companyId = #{companyId} AND a.activeFlag = 1 
		AND IFNULL(a.amount,0) - IFNULL(a.confirmAmount,0) > 0
		<if test="type != null and type != ''">
			<!-- 原料 -->
			<if test="type == 1">
				d.purchasingDtlId = #{orderDetailId}
			</if>
			<!-- 成品 -->
			<if test="type == 2">
				c.prdorderDetailId = #{orderDetailId}
			</if>
		</if>
		LIMIT 1
	</select>
	
	<update id="updateConfirmAmount">
		UPDATE pu_single_payable_detail SET
			confirmAmount = #{confirmAmount},
			status = #{status},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="batchUpdateConfirmAmount">
		UPDATE pu_single_payable_detail aa
		INNER JOIN
		(
			SELECT
				a.recordId AS "recordId",
				b.amount AS "amount"
			FROM pu_single_payable_detail a
			LEFT JOIN pu_material_check b ON b.recordId = a.materialCheckId
			LEFT JOIN md_material mm ON mm.recordId = b.materialId
			LEFT JOIN st_product_store c ON c.recordId = b.sourceId AND c.inOutType = 14 AND mm.materialKind = 100702 AND c.`status` = 99999901
			LEFT JOIN st_material_store d ON d.recordId = b.sourceId AND d.inOutType = 1 AND mm.materialKind = 100701 AND d.`status` = 99999901
			LEFT JOIN pu_prdorder_detail ppd1 ON ppd1.recordId = c.prdorderDetailId
			LEFT JOIN pu_purchasing_detail ppd2 ON ppd2.recordId = d.purchasingDtlId
			LEFT JOIN pu_pay_application e ON e.purchasingId = ppd1.prdorderId AND e.purchasingType = 2 AND e.`status` IN(500508,500509) AND e.fundType = 99999911
			LEFT JOIN pu_pay_application f ON f.purchasingId = ppd2.purchasingId AND f.purchasingType = 1 AND f.`status` IN(500508,500509) AND f.fundType = 99999911
			WHERE a.singlePayableId = #{recordId} AND a.activeFlag = 1 AND IFNULL(e.recordId,f.recordId) IS NOT NULL
		) bb ON bb.recordId = aa.recordId
		SET aa.confirmAmount = bb.amount
	</update>
	
	<select id="checkDeailCount" resultType="Integer">
		SELECT
			COUNT(1)
		FROM pu_single_payable_detail a
		LEFT JOIN pu_material_check b ON b.recordId = a.materialCheckId
		LEFT JOIN md_material c ON c.recordId = b.materialId
		LEFT JOIN st_material_store d ON d.recordId = b.sourceId AND c.materialKind = 100701
		WHERE d.recordId = #{recordId}
	</select>

	<select id="getSinglePayableDetail" resultType="SinglePayableDetail">
		SELECT * FROM pu_single_payable_detail WHERE recordId = #{recordId} AND activeFlag = 1
	</select>

	<select id="getSinglePayableDetailData" resultType="SinglePayableDetail">
		SELECT
			CONCAT(c.`no`,'-',c.shortName) AS "supplierName",
			b.billNo AS "billNo",
			d.`no` AS "craftNo",
			d.specification,
			b.quantity,
			b.price,
			CASE b.inOutFlag WHEN 2 THEN -b.amount ELSE b.amount END AS "amount",
			CASE a.type WHEN 1 THEN '采购入库' WHEN 2 THEN '退货出库' WHEN 3 THEN '补货入库' ELSE NULL END AS "name",
			h.shortName AS "companyName",
			d.materialKind,
			b.period
		FROM pu_single_payable_detail a
		LEFT JOIN pu_material_check b ON b.recordId = a.materialCheckId
		LEFT JOIN md_supplier c ON c.recordId = b.supplierId
		LEFT JOIN md_material d ON d.recordId = b.materialId

		LEFT JOIN st_product_store e ON e.recordId = b.sourceId AND d.materialKind = 100702 AND b.inOutFlag = 1
		LEFT JOIN pu_returns_detail f ON f.recordId = b.sourceId AND d.materialKind = 100702 AND f.purchasingType = 2
		LEFT JOIN pu_prdorder_detail g ON g.recordId = IFNULL(e.prdorderDetailId,f.purchasingDetailId)

		LEFT JOIN st_material_store ee ON ee.recordId = b.sourceId AND d.materialKind = 100701 AND b.inOutFlag = 1
		LEFT JOIN pu_returns_detail ff ON ff.recordId = b.sourceId AND d.materialKind = 100701 AND ff.purchasingType = 1
		LEFT JOIN pu_purchasing_detail gg ON gg.recordId = IFNULL(ee.purchasingDtlId,ff.purchasingDetailId)
		LEFT JOIN md_company h ON h.recordId = b.companyId
		WHERE a.recordId = #{recordId}
	</select>

	<update id="updateStatus">
		UPDATE pu_single_payable_detail SET
			confirmAmount = IFNULL(confirmAmount,0) + #{confirmAmount},
			status = #{status},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate}
		WHERE recordId = #{recordId}
	</update>

	<insert id="batchInsertSingleDetailCollect">
		INSERT INTO pu_singledetail_paymoney
		(
		companyId,
		detailId,
		type,
		paymoneyId,
		amount,
		activeFlag,
		createdBy,
		createdDate
		)VALUES
		<foreach collection="detailList" item="item" separator=",">
			(
			#{company.recordId},
			#{item.detailId},
			#{item.type},
			#{recordId},
			#{item.confirmAmount},
			1,
			#{createdBy.recordId},
			#{createdDate}
			)
		</foreach>
	</insert>

</mapper>