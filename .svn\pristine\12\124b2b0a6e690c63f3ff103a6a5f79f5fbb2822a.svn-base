/**
 * 
 */
package com.kyb.pcberp.modules.sys.dao;

import java.util.List;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.sys.entity.Menu;
import com.kyb.pcberp.modules.sys.entity.Role;
import com.kyb.pcberp.modules.sys.entity.RoleItem;
import com.kyb.pcberp.modules.sys.entity.User;

/**
 * 角色菜单DAO接口
 * <AUTHOR>
 * @version 2015-08-27
 */
@MyBatisDao
public interface RoleItemDao extends CrudDao<RoleItem> {
	
	
	public List<Menu> selectRoleItem(Role role);
	
	
	/**
	 * 根据角色编号删除
	 * @param roleItem
	 * @return
	 */
	public int deleteByRoleId(RoleItem roleItem);
	
	
	
	/**
	 * 查询客户下单的菜单
	 * @param role
	 * @return
	 */
	 public List<Menu> selectRoleItemcus(Role role);
	
	 /** zjn 2018-03-12 根据用户查询权限 */
	 public List<Menu> selectRoleItemByUser(User user);
	 
	 /** zjn 2019-09-16 根据用户id获取品质检测管理权限*/
	 Integer getDetectionManage(User user);
	 
	 Integer getRoleItemAccountant(User user);
}