package com.kyb.pcberp.common.config;

/**
 * 类型常量
 */
public interface TypeKey
{

    Integer IS_YU = 2; // 余料

    // 1.原材料 2.辅助材料 3.消耗品 4.生产工具 5.成品
    /**
     * 采购报表变量 原材料
     */
    Integer RAW_MATERIAL = 1;

    /**
     * 采购报表变量 辅助材料
     */
    Integer AUXILIARY_MATERIALS = 2;

    /**
     * 采购报表变量 消耗品
     */
    Integer CONSUMABLES = 3;

    /**
     * 采购报表变量 生产工具
     */
    Integer MEANS_OF_PRODUCTION = 4;

    /**
     * 采购报表变量 成品
     */
    Integer FINISHED_PRODUCT = 5;

    /**
     * 活动的
     */
    Integer ACTIVE = 1;

    /**
     * 不活动的
     */
    Integer INACTIVE = 0;

    /**
     * 通知单加急标志 是
     */
    String URGENTFLAGYES = "1";

    /**
     * 通知单加急标志 否
     */
    String URGENTFLAGNO = "0";

    public static final String ERROR_INDEX = "-1";

    /**
     * 是
     */
    Integer YES = 1;

    /**
     * 不是
     */
    Integer NO = 0;

    /**
     * 真
     */
    Integer TRUE = 1;

    /**
     * 假
     */
    Integer FALSE = 0;

    Integer TAKE = 0;

    Integer HAND = 1;

    Integer TAKE_HAND = 2;

    String STRING_TRUE = "true";

    String STRING_FALSE = "false";

    String SUCCESS = "1";

    String FAILURE = "0";

    Integer CONTAINTAXFLAG_YES = 1;

    Integer CONTAINTAXFLAG_NO = 0;

    /**
     * 本系统服务到期延时使用的天数
     */
    Integer SYSTEM_SERVICE_DELAY_DAYS = 7;

    /**
     * 货物进出标志：送货(用于对账单)
     */
    Integer GOODS_INOUTFLAG_SEND = 1;

    /**
     * 货物进出标志：退货(用于对账单)
     */
    Integer GOODS_INOUTFLAG_RETURN = 0;

    /**
     * 原材料对账单货物进出志：进货
     */
    Integer MATERIAL_INOUTFLAG_IN = 1;

    /**
     * 原材料对账单货物进出志：退货
     */
    Integer MATERIAL_INOUTFLAG_RETURN = 0;

    /**
     * 原材料对账单货物进出志：外发
     */
    Integer MATERIAL_INOUTFLAG_OUTWARD = 2;

    String EDITABLE = "e";

    String UNEDITABLE = "u";

    /**
     * 操作等级：全局的
     */
    Integer OPERLEVEL_GLOBAL = 1;

    /**
     * 操作等级：局部的
     */
    Integer OPERLEVEL_LOCAL = 0;

    Integer PDF_OUT_TEMPLATE = 1;

    Integer PDF_OUT_DIRECT = 2;

    String TYPE_FOUR = "4";

    String TYPE_THREE = "3";

    /**
     * 账单状态：正常记录 -未冲红
     */
    Integer BILL_STATUS_NORMAL = 99999901;

    /**
     * 账单状态：用于冲红的记录 -冲红单(生成的新记录)
     */
    Integer BILL_STATUS_FOR_WRITEOFF = 99999902;

    /**
     * 账单状态：被冲红的记录 -已冲红
     */
    Integer BILL_STATUS_BE_WRITEOFFED = 99999903;

    /**
     * 款项类别：预付款
     */
    Integer FUND_TYPE_PREPAYMENT = 99999911;

    /**
     * 款项类别：应付款
     */
    Integer FUND_TYPE_DUEPAYMENT = 99999912;

    /**
     * 款项类别：预收款
     */
    Integer FUND_TYPE_PRECOLLECT = 99999913;

    /**
     * 款项类别：应收款
     */
    Integer FUND_TYPE_RECEVIABLES = 99999914;

    /*
     * ======================================================================
     *
     * 基础数据模块部份常量
     *
     * =======================================================================
     */

    /**
     * 本系统用户(公司)状态：试用用户
     */
    Integer MD_COMPANY_STATUS_TASTE = 100101;

    /**
     * 本系统用户(公司)状态：正式用户
     */
    Integer MD_COMPNAY_STATUS_NORMAL = 100102;

    /**
     * 本系统用户(公司)状态：服务已过期
     */
    Integer MD_COMPANY_STATUS_EXPIRED = 100103;

    /**
     * 货运公司合作状态：初步合作
     */
    Integer MD_TRANSPORT_STATUS_INCHOATE = 100201;

    /**
     * 货运公司合作状态：合作正常
     */
    Integer MD_TRANSPORT_STATUS_NORMAL = 100202;

    /**
     * 货运公司合作状态：已中断合作
     */
    Integer MD_TRANSPORT_STATUS_BREAK = 100203;

    /**
     * 与客户的合作状态：初步合作
     */
    Integer MD_CUSTOMER_STATUS_INCHOATE = 100301;

    /**
     * 与客户的合作状态：合作正常
     */
    Integer MD_CUSTOMER_STATUS_NORMAL = 100302;

    /**
     * 与客户的合作状态：已中断合作
     */
    Integer MD_CUSTOMER_STATUS_BREAK = 100303;

    /**
     * 客户协议价：待审核
     */
    Integer MD_CUSTOMER_QUOTE_AUDIT = 100304;

    /**
     * 客户协议价：已审核
     */
    Integer MD_CUSTOMER_QUOTE_APPROVED = 100305;

    /**
     * 客户协议价：审核不批准
     */
    Integer MD_CUSTOMER_QUOTE_DENIED = 100306;

    /**
     * 与供货商的合作状态：初步合作
     */
    Integer MD_SUPPLIER_STATUS_INCHOATE = 100401;

    /**
     * 与供货商的合作状态：合作正常
     */
    Integer MD_SUPPLIER_STATUS_NORMAL = 100402;

    /**
     * 与供货商的合作状态：已中断合作
     */
    Integer MD_SUPPLIER_STATUS_BREAK = 100403;

    /**
     * 客户类型：只是询价的非正式客户
     */
    Integer MD_CUSTOMER_TYPE_INQUIRY = 100501;

    /**
     * 客户类型：有实际交易的正式客户
     */
    Integer MD_CUSTOMER_TYPE_ACTUAL = 100502;

    /**
     * 员工在职状态：试用期
     */
    Integer MD_EMPLOYEE_STATUS_TASTE = 100601;

    /**
     * 员工在职状态：正式
     */
    Integer MD_EMPLOYEE_STATUS_NORMAL = 100602;

    /**
     * 员工在职状态：离职
     */
    Integer MD_EMPLOYEE_STATUS_OUTJOB = 100603;

    /**
     * 原料
     */
    Integer MD_MATERIAL_TYPE_RAW = 100701;

    /**
     * 成品
     */
    Integer MD_MATERIAL_TYPE_PRODUCT = 100702;

    /*
     * ======================================================================
     *
     * 销售模块部份常量
     *
     * =======================================================================
     */
    /**
     * 报价单状态：尚未交给客户
     */
    Integer SL_QUOTATION_STATUS_UNSUBMIT = 200101;

    /**
     * 报价单状态：已递交给客户
     */
    Integer SL_QUOTATION_STATUS_SUBMITED = 200102;

    /**
     * 报价单状态：客户已回复
     */
    Integer SL_QUOTATION_STATUS_WRITEDBACK = 200103;

    /**
     * 报价单状态：已成交(签定合同)
     */
    Integer SL_QUOTATION_STATUS_DEALED = 200104;

    /**
     * 报价单状态：已取消
     */
    Integer SL_QUOTATION_STATUS_CANCELED = 200105;

    /**
     * 合同状态：待商家确认
     */
    String SL_ICLOUD_CONTRACT_STATUS_1 = "1";

    /**
     * 合同状态：商家不可操作
     */
    String SL_ICLOUD_CONTRACT_STATUS_2 = "2";

    /**
     * 云平台合同状态：已完成
     */
    String SL_ICLOUD_CONTRACT_STATUS_3 = "3";

    /**
     * 合同状态：客户不可操作
     */
    String SL_ICLOUD_CONTRACT_STATUS_4 = "4";

    /**
     * 合同状态：未确认
     */
    Integer SL_CONTRACT_STATUS_DRAFT = 200201;

    /**
     * 合同状态：客户已确认
     */
    Integer SL_CONTRACT_STATUS_CONFIRMED = 200202;

    /**
     * 合同状态：生产完成
     */
    Integer SL_CONTRACT_STATUS_PRODUCECOMPLETED = 200203;

    /**
     * 合同状态：送货完成
     */
    Integer SL_CONTRACT_STATUS_DELIVERYCOMPLETED = 200204;

    /**
     * 合同状态：已作废
     */
    Integer SL_CONTRACT_STATUS_CANCELED = 200205;

    /**
     * 取消确认
     */
    Integer SL_CONTRACT_STATUS_CANCEL_CONFIRM = 200206;

    /**
     * 合同已审核
     */
    Integer SL_CONTRACT_STATUS_MANEGE = 200207;

    /**
     * 合同状态：等待客户确认
     */
    Integer SL_CONTRACT_STATUS_CUSTOMER = 200208;

    /**
     * 合同状态：审批中
     */
    Integer SL_CONTRACT_STATUS_WAIT = 200209;

    /**
     * 合同状态：对账关闭
     */
    Integer SL_CONTRACT_STATUS_CHECKED = 200210;

    /**
     * 合同状态：批次面积确认
     */
    Integer SL_CONTRACT_STATUS_BATCHAREACONFIRM = 200211;

    /**
     * 送货单状态：尚未送货
     */
    Integer SL_DELIVERY_STATUS_UNDELIVER = 200301;

    /**
     * 送货单状态：已送货
     */
    Integer SL_DELIVERY_STATUS_DELIVERED = 200302;

    /**
     * 送货单状态：客户已收到
     */
    Integer SL_DELIVERY_STATUS_RECEIVED = 200303;

    /** 送货单状态：客户已退货 */
    // Integer SL_DELIVERY_STATUS_RETURN=200304;

    /**
     * 送货单状态：已作废
     */
    Integer SL_DELIVERY_STATUS_CANCELED = 200304;

    /**
     * 生产通知单状态:未确认
     */
    Integer SL_NOTIFICATION_STATUS_DRAFT = 200401;

    /**
     * 生产通知单状态:已确认
     */
    Integer SL_NOTIFICATION_STATUS_CONFIRMED = 200402;

    /**
     * 生产通知单状态:已入库
     */
    Integer SL_NOTIFICATION_STATUS_COMPLETED = 200403;

    /**
     * 生产通知单状态:已投料
     */
    Integer SL_NOTIFICATION_STATUS_FEED = 200404;

    /**
     * 生产通知单状态:已作废
     */
    Integer SL_NOTIFICATION_STATUS_CANCELED = 200406;

    /**
     * 生产通知单状态:补料中
     */
    Integer SL_NOTIFICATION_STATUS_SUPPLEMENTARYFOOD = 200405;

    /**
     * 生产编号状态:未确认
     */
    Integer SL_CRAFT_STATUS_DRAFT = 200501;

    /**
     * 生产编号状态:已确认
     */
    Integer SL_CRAFT_STATUS_CONFIRMED = 200502;

    /**
     * 合单标识状态 正常
     */
    Integer SL_NOTIFICATION_MERGE_NORMAL = 200601;

    /**
     * 合单标识状态 合单后
     */
    Integer SL_NOTIFICATION_MERGE_NEW = 200602;

    /**
     * 合单标识状态 被合单
     */
    Integer SL_NOTIFICATION_BE_MERGED = 200603;

    Integer SL_NOTIFICATION_REPLENISH = 200604;

    /*
     * ======================================================================
     *
     * 工程模块部份常量
     *
     * =======================================================================
     */

    /**
     * 主模板
     */
    Integer EG_PROCESSTEMPLATE_ISPRIMARY_YES = 1;

    /**
     * 副模板
     */
    Integer EG_PROCESSTEMPLATE_ISPRIMARY_NO = 0;

    /**
     * 流程在WIP表中是否可见：是
     */
    Integer EG_PROCESS_VISIBLE_FLAG_YES = 1;

    /**
     * 流程在WIP表中是否可见：否
     */
    Integer EG_PROCESS_VISIBLE_FLAG_NO = 0;

    /**
     * 第一轮钻孔
     */
    Integer EG_DRILLTURNS_FIRST = 1;

    /**
     * 第二轮钻孔
     */
    Integer EG_DRILLTURNS_SECOND = 2;

    /**
     * 流程卡A工序参数选项标志：直接填写
     */
    Integer EG_PROCESSITEMS_OPTIONALFLAG_FILL = 1;

    /**
     * 流程卡A工序参数选项标志：下接选择框
     */
    Integer EG_PROCESSITEMS_OPTIONALFLAG_SELECT = 2;

    /**
     * 流程卡A工序参数选项标志：复选框
     */
    Integer EG_PROCESSITEMS_OPTIONALFLAG_CHECKBOX = 3;

    /**
     * 流程卡状态:未确认
     */
    Integer EG_PROCESSCARD_STATUS_DRAFT = 300101;

    /**
     * 流程卡状态:已确认
     */
    Integer EG_PROCESSCARD_STATUS_CONFIRMED = 300102;

    /**
     * 流程卡状态:已取消
     */
    Integer EG_PROCESSCARD_STATUS_CANCEL = 300103;

    /** 流程卡状态:已完成生产 */
    // Integer EG_PROCESSCARD_STATUS_COMPLETED=300103;

    /**
     * 流程卡状态:已作废
     */
    Integer EG_PROCESSCARD_STATUS_CANCELED = 300104;

    /*
     * ======================================================================
     *
     * 仓库管理部份常量
     *
     * =======================================================================
     */

    /**
     * 退货申请单处理状态：退货申请
     */
    Integer ST_REJECTAPPLICATION_STATUS_RETURNAPPLY = 400101;

    /**
     * 退货申请单处理状态：退货申请通过审核
     */
    Integer ST_REJECTAPPLICATION_STATUS_RETURNAUDITED = 400102;

    /**
     * 退货申请单处理状态：退回产品已入库
     */
    Integer ST_REJECTAPPLICATION_STATUS_STORED = 400103;

    /**
     * 退货申请单处理状态：补货已送出
     */
    Integer ST_REJECTAPPLICATION_STATUS_REDELIVERED = 400104;

    /**
     * 退货申请单处理状态：退货申请未通过审核
     */
    Integer ST_REJECTAPPLICATION_STATUS_DENIED = 400105;

    /**
     * 退货申请单处理状态：已作废
     */
    Integer ST_REJECTAPPLICATION_STATUS_CANCELED = 400106;

    /**
     * 退货申请单处理状态：品质已确认
     */
    Integer ST_REJECTAPPLICATION_STATUS_QUATITY = 400107;

    /**
     * 退货申请单处理状态：已完成
     */
    Integer ST_REJECTAPPLICATION_STATUS_FINISH = 400108;

    /**
     * 库存调整状态：未确认
     */
    Integer ST_ADJUSTSTOCKS_STATUS_DRAFT = 400201;

    /**
     * 库存调整状态：已确认
     */
    Integer ST_ADJUSTSTOCKS_STATUS_CONFIRMED = 400202;

    /**
     * 库存调整状态：审核不批准
     */
    Integer ST_ADJUSTSTOCKS_STATUS_DENIED = 400203;

    /**
     * 库存调整状态：审核批准
     */
    Integer ST_ADJUSTSTOCKS_STATUS_APPROVED = 400204;

    /**
     * 物料领用单未确认
     */
    Integer MATERIAL_USE_STATUS_DRAFT = 400301;

    /**
     * 物料领用单已确认
     */
    Integer MATERIAL_USE_STATUS_CONFIRMED = 400302;

    /**
     * 物料领用单已出库-领用完
     */
    Integer MATERIAL_USE_STATUS_OUTHOUSE_YES = 400303;

    /**
     * 物料领用单已出库-未领完
     */
    Integer MATERIAL_USE_STATUS_OUTHOUSE_NO = 400304;

    /**
     * 物料领用单已出库-已冲红
     */
    Integer MATERIAL_USE_STATUS_BE_WRITEOFFED = 400305;

    /**
     * 原材料出/入库标志：采购入库（修改对账单）
     */
    Integer ST_MATERIAL_INOUTTYPE_IN = 1;

    /**
     * 原材料出/入库标志：原料入库（不修改对账单）
     */
    Integer ST_MATERIAL_INOUTTYPE_OTHER_IN = 2;

    /**
     * 原材料出/入库标志：投料出库
     */
    Integer ST_MATERIAL_INOUTTYPE_FEEDING_OUT = 3;

    /**
     * 原材料出/入库标志：采购退货出库
     */
    Integer ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT = 4;

    /**
     * 原材料出/入库标志：补料出库
     */
    Integer ST_MATERIAL_INOUTTYPE_FEDING_OUT = 5;

    /**
     * 原材料出/入库标志：领料出库
     */
    Integer ST_MATERIAL_INOUTTYPE_OUT = 6;

    /**
     * 原材料出/入库标志：采购补货入库
     */
    Integer ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_REDELIVER = 7;

    /**
     * 原材料出/入库标志：原料送货出库
     */
    Integer ST_MATERIAL_INOUTTYPE_SENTOUT = 8;

    // 移库入库
    Integer ST_MATERIAL_INOUTTYPE_MOVE_IN = 9;

    // 移库出库
    Integer ST_MATERIAL_INOUTTYPE_MOVE_OUT = 10;

    // 原料客诉退货入库
    Integer ST_MATERIAL_INOUTTYPE_REJECT_IN = 20;

    /*-- 成品 --*/
    /**
     * 本厂产品入库/提货标志：生产入库
     */
    Integer ST_PRODUCT_INOUTTYPE_IN = 11;

    /**
     * 本厂产品入库/提货标志：送货出库
     */
    Integer ST_PRODUCT_INOUTTYPE_OUT = 12;

    /**
     * 本厂产品入库/提货标志：客诉退货入库
     */
    Integer ST_PRODUCT_INOUTTYPE_RETURN = 13;

    /**
     * 采购入库
     */
    Integer ST_PRODUCT_INOUTTYPE_PURCHASING_IN = 14;

    /**
     * 采购补货入库
     */
    Integer ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_ENTER = 15;

    /**
     * 采购退货出库
     */
    Integer ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_OUT = 16;

    /**
     * 设置期初
     */
    Integer ST_INIT_PRODUCT_INOUTTYPE = 17;

    /**
     * 每月结余
     */
    Integer ST_COUNT_PRODUCT_INOUTTYPE = 18;

    /**
     * 期初初始数据
     */
    Integer ST_INITDATA_PRODUCT_INOUTTYPE = 19;

    /**
     * 客户希望退回产品的处理方试：退货
     */
    Integer ST_REJECTAPPLICATION_TREATMENT_RETURN = 21;

    /**
     * 客户希望退回产品的处理方试：补货
     */
    Integer ST_REJECTAPPLICATION_TREATMENT_REDELIVER = 22;

    /**
     * 品质管理 部分常量
     **/

    /**
     * 来料检测单处理状态：检测申请
     */
    Integer ST_SOURCEDETECTION_STATUS_APPLY = 400501;

    /**
     * 来料检测单处理状态：检测申请通过审核
     */
    Integer ST_SOURCEDETECTION_STATUS_AUDITED = 400502;

    /**
     * 来料检测单处理状态：检测申请未通过审核
     */
    Integer ST_SOURCEDETECTION_STATUS_DENIED = 400503;

    /**
     * 来料检测单处理状态：检测申请取消
     */
    Integer ST_SOURCEDETECTION_STATUS_CANCEL = 400504;

    /**
     * 来料检测单处理状态：已入库
     */
    Integer ST_SOURCEDETECTION_STATUS_INHOUSE = 400505;

    /*
     * ======================================================================
     *
     * 仓库管理部份常量
     *
     * =======================================================================
     */
    /**
     * 原料销售订单状态
     */
    Integer PU_MATERIAL_STATUS_UNCONFIRM = 2000201;

    /**
     * 采购订单状态：未确认
     */
    Integer PU_PURCHASING_STATUS_UNCONFIRM = 500101;

    /**
     * 采购订单状态：已确认
     */
    Integer PU_PURCHASING_STATUS_CONFIRMED = 500102;

    /**
     * 采购订单状态：已审核
     */
    Integer PU_PURCHASING_STATUS_APPROVE = 500103;

    /**
     * 采购订单状态：待供应链采购
     */
    Integer PU_PURCHASING_STATUS_SUPPLIER = 500113;

    /**
     * 采购订单状态：已入库
     */
    Integer PU_PURCHASING_STATUS_ALREADY_IN = 500104;

    /**
     * 采购订单明细状态：检测通过
     */
    Integer PU_PURCHASING_STATUS_AUDITED = 500105;

    /**
     * 采购订单明细状态：检测未通过
     */
    Integer PU_PURCHASING_STATUS_DENIED = 500106;

    /**
     * 采购订单明细状态：检测待审核
     */
    Integer PU_PURCHASING_STATUS_PEEDING = 500107;

    /**
     * 采购订单状态：已取消
     */
    Integer PU_PURCHASING_STATUS_CANCLE = 500108;

    /**
     * 采购订单状态:检测中
     */
    Integer PU_PURCHASING_STATUS_TEN = 500109;

    /**
     * 采购订单状态：审批中
     */
    Integer PU_PURCHASING_STATUS_APPROVAL = 500110;

    /**
     * 原材料退货单状态：尚未送货
     */
    Integer PU_RETURNS_STATUS_UNDELIVER = 500201;

    /**
     * 原材料退货单状态：已送货
     */
    Integer PU_RETURNS_STATUS_DELIVERED = 500202;

    /**
     * 原材料退货单状态：客户已收到
     */
    Integer PU_RETURNS_STATUS_RECEIVED = 500203;

    /**
     * 原材料退货单状态：已作废
     */
    Integer PU_RETURNS_STATUS_CANCELED = 500204;

    /**
     * 采购付款申请单状态：未批准
     */
    Integer PU_PAYAPPLICATION_STATUS_UNAPPROVED = 500301;

    /**
     * 采购付款申请单状态：已批准
     */
    Integer PU_PAYAPPLICATION_STATUS_APPROVED = 500302;

    /**
     * 采购付款申请单状态：已付款
     */
    Integer PU_PAYAPPLICATION_STATUS_PAIED = 500303;

    /**
     * 采购付款申请单状态：已作废(取消)
     */
    Integer PU_PAYAPPLICATION_STATUS_CANCELED = 500304;

    /**
     * 希望退回产品的处理方试：退货
     */
    Integer PU_RETURNS_DETAIL_TREATMENT_RETURN = 500401;

    /**
     * 希望退回产品的处理方试：补货
     */
    Integer PU_RETURNS_DETAIL_TREATMENT_REDELIVER = 500402;

    /**
     * 采购订单状态：未确认
     */
    Integer PU_PRODUCT_ORDER_STATUS_UNCONFIRM = 500501;

    /**
     * 采购订单状态：已确认
     */
    Integer PU_PRODUCT_ORDER_STATUS_CONFIRMED = 500502;

    /**
     * 采购订单状态：已作废(取消)
     */
    Integer PU_PRODUCT_ORDER_STATUS_CANCELED = 500503;

    /**
     * 采购退货状态：未确认
     */
    Integer PU_PURETURNS_STATUS_UNCONFIRM = 500504;

    /**
     * 采购退货状态：已确认
     */
    Integer PU_PURETURNS_STATUS_CONFIRMED = 500505;

    /**
     * 采购退货状态：已完成
     */
    Integer PU_PURETURNS_STATUS_ALPAY = 500515;

    /**
     * 采购退货状态：已出库
     */
    Integer PU_PURETURNS_STATUS_THELIBRARY = 500516;

    /**
     * 采购付款申请状态：未确认
     */
    Integer PU_PAY_APPLICATION_STATUS_UNCONFIRM = 500506;

    /**
     * 采购付款申请状态：审批中
     */
    Integer PU_PAY_APPLICATION_STATUS_CONFIRMED = 500507;

    /**
     * 采购付款申请状态：审批通过
     */
    Integer PU_PAY_APPLICATION_STATUS_ALPAY = 500508;

    /**
     * 采购付款申请状态：已完成
     */
    Integer PU_PAY_APPLICATION_STATUS_APPROVE = 500509;

    /**
     * 送货单状态：未确认
     */
    Integer PU_ORDER_DELIVERY_STATUS_UNCONFIRM = 500516;

    /**
     * 送货单状态：已确认
     */
    Integer PU_ORDER_DELIVERY_STATUS_CONFIRMED = 500517;

    /**
     * 送货单状态：已出库
     */
    Integer PU_ORDER_DELIVERY_STATUS_COMPLETE = 500518;

    /*
     * ======================================================================
     *
     * 质量管理部份常量
     *
     * =======================================================================
     */

    /**
     * 品质检测初始化
     */
    Integer QC_STATUS_INIT = 900601;

    /**
     * 品质检测完成
     */
    Integer QC_STATUS_FANISH = 900605;

    /**
     * 生产加工类别：自制
     */
    Integer QC_MANUFACTURE_TYPE_SELFWORK = 600101;

    /**
     * 生产加工类别：外发
     */
    Integer QC_MANUFACTURE_TYPE_OUTWORK = 600102;

    /**
     * 检测类型：通过
     */
    Integer QC_MANUFACTURE_TYPE_PASS = 1;

    /**
     * 检测类型：返工
     */
    Integer QC_MANUFACTURE_TYPE_REWORK = 2;

    /**
     * 检测类型：返修
     */
    Integer QC_MANUFACTURE_TYPE_REPAIR = 4;

    /**
     * 检测类型：反洗
     */
    Integer QC_MANUFACTURE_TYPE_BACKWASHING = 5;

    /**
     * 检测类型：报废
     */
    Integer QC_MANUFACTURE_TYPE_SCRAP = 3;

    /*
     * ======================================================================
     *
     * 生产管理部份常量
     *
     * =======================================================================
     */

    /**
     * 通知单状态：全部
     */
    Integer PD_NOTIFICATION_STATUS_ALL = 700201;

    /**
     * 通知单状态：资料未完成
     */
    Integer PD_NOTIFICATION_STATUS_NO_MI = 700202;

    /**
     * 通知单状态：交期已过
     */
    Integer PD_NOTIFICATION_STATUS_EXCEED = 700203;

    /**
     * 通知单状态：加急
     */
    Integer PD_NOTIFICATION_STATUS_URGENT = 700204;

    /**
     * 通知单状态：补料
     */
    Integer PD_NOTIFICATION_STATUS_REPLENISH = 700205;

    /**
     * 通知单状态： 已投料
     */
    Integer PD_NOTIFICATION_STATUS_FEED = 700206;

    /**
     * 投料：未确认
     */
    Integer PD_FEEDING_STATUS_UNCONFIRM = 700101;

    /**
     * 投料：已确认
     */
    Integer PD_FEEDING_STATUS_CONFIRMED = 700102;

    /**
     * 投料：已取消 (工程变更带来的)
     */
    Integer PD_FEEDING_STATUS_CANCELED = 700103;

    /**
     * 投料：已完成
     */
    Integer PD_FEEDING_STATUS_FINISHED = 700104;

    /**
     * 投料单 缺料状态
     */
    Integer PD_FEEDING_STATUS_LOSS = 700105;

    /**
     * 投料单 暂停状态
     */
    Integer PD_FEEDING_STATUS_PAUSE = 700106;

    /**
     * 补料：未确认
     */
    Integer PD_REPLENISH_STATUS_UNCONFIRMED = 700401;

    /**
     * 补料：已确认
     */
    Integer PD_REPLENISH_STATUS_ACKNOWLEDGED = 700402;

    /**
     * 补料：已取消 (工程变更带来的)作废
     */
    Integer PD_REPLENISH_STATUS_CANCELED = 700403;

    /**
     * 补料：已完成
     */
    Integer PD_REPLENISH_STATUS_FINISHED = 700404;

    /**
     * 补料单 缺料状态
     */
    Integer PD_REPLENISH_STATUS_LOSS = 700405;

    /**
     * 补料单 暂停状态
     */
    Integer PD_REPLENISH_STATUS_PAUSE = 700406;

    /**
     * 生产外发加工单 申请外发
     */
    Integer PD_POPO_INIT = 700601;

    /**
     * 生产外发加工单 拒绝外发
     */
    Integer PD_POPO_FAIL = 700602;

    /**
     * 生产外发加工单 正在外发（审核通过）
     */
    Integer PD_POPO_PASS = 700603;

    /**
     * 生产外发加工单 外发完成（过数完成表示完成）
     */
    Integer PD_POPO_END = 700604;

    /**
     * 生产外发加工单 作废
     */
    Integer PD_POPO_INVALID = 700605;

    /**
     * 外发设置 待生效
     */
    Integer PD_POPOSET_UNCONFIRMED = 700606;

    /**
     * 外发设置 生效
     */
    Integer PD_POPOSET_NOMAL = 700607;

    /**
     * 外发设置 失效
     */
    Integer PD_POPOSET_CANCEL = 700608;

    /*
     * ======================================================================
     *
     * 报表部份常量
     *
     * =======================================================================
     */

    /**
     * 待处理订单
     */
    Integer REPORT_STATUS_PENDING_VALUE = 800101;

    /**
     * 未送货订单
     */
    Integer REPORT_STATUS_UNFILLED_VALUE = 800102;

    /**
     * 预警订单
     */
    Integer REPORT_STATUS_WARNING_VALUE = 800103;

    /**
     * 逾期订单
     */
    Integer REPORT_STATUS_EXPIRY_VALUE = 800104;

    /**
     * 投料统计
     */
    Integer REPORT_CAPACITY_STATUS_FEED_VALUE = 800105;

    /**
     * 入库统计
     */
    Integer REPORT_CAPACITY_STATUS_PROIN_VALUE = 800106;

    /**
     * 订单数量
     */
    Integer REPORT_CAPACITY_STATUS_ORDER_VALUE = 800107;

    /** 批次与批次明细共用下面的值 */
    /**
     * 批次操作类型 投料
     */
    Integer PRODUCE_BATCH_OPERATION_TYPE_FEED = 900101;

    /**
     * 批次操作类型 补料
     */
    Integer PRODUCE_BATCH_OPERATION_TYPE_REP = 900102;

    /**
     * 批次状态 未完成
     */
    Integer PRODUCE_BATCH_STATUS_INIT = 900301;

    /**
     * 批次状态 暂停
     */
    Integer PRODUCE_BATCH_STATUS_PAUSE = 900302;

    /**
     * 批次状态 缺料
     */
    Integer PRODUCE_BATCH_STATUS_LOSS = 900303;

    /**
     * 批次状态作废
     */
    Integer PRODUCE_BATCH_STATUS_CANCELED = 900304;

    /**
     * 生产过数完成
     */
    Integer PRODUCE_BATCH_STATUS_OPERATION = 900305;

    /**
     * 批次状态 完成
     */
    Integer PRODUCE_BATCH_STATUS_FANISH = 900306;

    /**
     * 客户下单状态 未确认
     */
    Integer CUSTOMER_ORDER_UNCONFIRMED = 10010701;

    /**
     * 客户下单状态 已确认
     */
    Integer CUSTOMER_ORDER_CONFIRMED = 10010702;

    /**
     * 客户下单状态 已受理
     */
    Integer CUSTOMER_ORDER_ACCEPT = 10010703;

    /**
     * 客户下单状态 工艺价格有改
     */
    Integer CUSTOMER_ORDER_CONFIRMEDAGAIN = 10010704;

    /**
     * 客户下单状态 已拒绝
     */
    Integer CUSTOMER_ORDER_REFUSE = 10010705;

    /**
     * 客户下单状态 已被业务员删除
     */
    Integer CUSTOMER_ORDER_SALEREFUSE = 10010706;

    /**
     * 参数设置-物料领用 开启
     */
    Integer MATERIALUSE_START = 1;

    /**
     * 参数设置-物料领用 开启
     */
    Integer MATERIALUSE_CLOSE = 0;

    /**
     * WC20170215 客诉类型：合同客诉
     */
    String COMPLAINT_STYLE_CONTRACT = "0";

    /**
     * WC20170215 客诉类型：寄存客诉
     */
    String COMPLAINT_STYLE_DEPOSIT = "1";

    /**
     * WC20170323 寄存时机：确认送货单
     */
    Integer DEPOSIT_TIME_CONFIRM_DELIVERY = 1;

    /**
     * WC20170323 寄存时机：生产入库
     */
    Integer DEPOSIT_TIME_PRODUCE_INHOUSE = 2;

    /**
     * WC20170323 寄存时机：采购入库
     */
    Integer DEPOSIT_TIME_PURCHASING_INHOUSE = 3;

    /**
     * WC20170323 寄存时机：退货入库
     */
    Integer DEPOSIT_TIME_BACKGOODS_INHOUSE = 4;

    /**
     * WC20170323 寄存时机：补货入库
     */
    Integer DEPOSIT_TIME_REPLENISHMENT_INHOUSE = 5;

    /*
     * ======================================================================
     *
     * 供应商
     *
     * =======================================================================
     */
    /**
     * lh 默认材料供应商
     */
    Integer SUPPLIER_ISDEFAULT = 1;

    /*
     * ======================================================================
     *
     * 报价模块
     *
     * =======================================================================
     */
    /** lh 报价模块 - 工艺组合价设置 */

    /**
     * lh 是参与报价
     */
    Integer ISQUOTATION_YES = 0;

    /**
     * lh 否参与报价
     */
    Integer ISQUOTATION_NO = 1;

    /** lh 报价模块 - 客户型号daoru */

    /**
     * lh 是参与报价
     */
    Integer QUOTEFLAG_NO = 1;

    /**
     * lh 否参与报价
     */
    Integer QUOTEFLAG_YES = 2;

    /*
     * ======================================================================
     *
     * 云平台下单常量
     *
     * =======================================================================
     */
    /**
     * zjn 2017-11-09 单有改
     */
    String NEWORDER = "1";

    /**
     * zjn 2017-11-09 单未改
     */
    String ORDER = "2";

    /**
     * zjn 2018-01-25 0代表调整
     */
    Integer ADJUSTFLAG_THIS = 0;

    /**
     * zjn 2017-01-25 1代表被调整
     */
    Integer ADJUSTFLAG_THAT = 1;

    /**
     * zjn 2018-02-09 1代表第一次调整
     */
    Integer ADJUSTCHECKFLAG_FIRST = 1;

    /**
     * zjn 2018-02-09 12代表中间的调整
     */
    Integer ADJUSTCHECKFLAG_AMONG = 12;

    /**
     * zjn 2017-02-09 2代表最新的一次调整
     */
    Integer ADJUSTCHECKFLAG_END = 2;

    /**
     * WC 2018-03-19 厂编调整记录状态：已调整
     */
    String CRAFTNO_ADJUST_YES = "101001";

    /**
     * WC 2018-03-19 厂编调整记录状态：已取消
     */
    String CRAFTNO_ADJUST_NO = "101002";

    /**
     * TJ 2018-03-27 云平台传下来的所有权限
     */
    String ALL_JURISDICTION = "0";

    /**
     * TJ 2018-03-27 云平台传下来的个人权限
     */
    String PERSONAL_JURISDICTION = "1";

    /**
     * WC 2018-08-25 外发品检状态：待审核
     */
    String QC_POPO_INSPECT_STATUS_NO = "900602";

    /**
     * WC 2018-08-25 外发品检状态：已审核
     */
    String QC_POPO_INSPECT_STATUS_YES = "900603";

    /**
     * zjn 2018-08-09 1：erp操作生成领用记录
     */
    String ERP_MATERIALUSE_TYPE = "1";

    /**
     * zjn 2018-08-09 2：物料网操作生成领用记录
     */
    String MI_MATERIALUSE_TYPE = "2";

    /**
     * zjn 2018-08-09 3：物料网操作生成被领用记录
     */
    String MI_QUILT_MATERIALUSE_TYPE = "3";

    /********** tj 2018-10-24 自动化 **********/
    /**
     * 合同
     */
    String PROCEDURE_CONTRACT = "NO000001";

    /**
     * 通知单
     */
    String PROCEDURE_NOTIFICATION = "NO000002";

    /**
     * 成品采购
     */
    String PROCEDURE_PRDORDER = "NO000003";

    /**
     * 来料检测
     */
    String PROCEDURE_SOURCEDETETION = "NO000004";

    /**
     * 采购入库
     */
    String PROCEDURE_PREORDER_IN = "NO000005";

    /**
     * 送货单管理
     */
    String PROCEDURE_DELIVERY_ONE = "NO000006";

    /**
     * 送货出库
     */
    String PROCEDURE_DELIVERYOUT_ONE = "NO000007";

    /**
     * 流程卡
     */
    String PROCEDURE_RUN_CARD = "NO000003";

    /**
     * 生产投料
     */
    String PROCEDURE_FEEDING = "NO000004";

    /**
     * 投料出库
     */
    String PROCEDURE_FEEDING_OUT = "NO000005";

    /**
     * 生产过数
     */
    String PROCEDURE_PRODUCTION_NUMBER = "NO000006";

    /**
     * 生产入库
     */
    String PROCEDURE_PRODUCTION_IN = "NO000007";

    /**
     * 送货单
     */
    String PROCEDURE_DELIVERY_TWO = "NO000008";

    /**
     * 送货出库
     */
    String PROCEDURE_DELIVERYOUT_TWO = "NO000009";

    /***************** 自动化 END **************/

    /****************** 收 款 **************************/

    /**
     * zjn 2018-12-10 收款对账单明细(原料合同送货)
     */
    Integer GOODS_INOUTFLAG_RAWSENDSTORE = 2;

    /**
     * zjn 2018-12-10 收款对账单明细 (云平台领用:原料出库)
     */
    Integer GOODS_INOUTFLAG_RAWOUTSTORE = 3;

    /**
     * zjn 2018-12-10 收款对账单明细新增操作状态 1001：未确认
     */
    Integer GOODSCHECK_STATUS_UNCONFIRM = 1001;

    /**
     * zjn 2018-12-10 收款对账单明细新增操作状态 1002：已确认
     */
    Integer GOODSCHECK_STATUS_CONFIRM = 1002;

    /**
     * zjn 2018-12-10 收款对账单明细新增操作状态 1003：未复核
     */
    Integer GOODSCHECK_STATUS_NOCHECK = 1003;

    /**
     * zjn 2018-12-10 收款对账单明细新增操作状态 1004：已复核
     */
    Integer GOODSCHECK_STATUS_CHECK = 1004;

    /**
     * zjn 2018-12-11 收款对账单状态 2001：未确认
     */
    Integer RECEIVABLE_STATUS_UNCONFIRMEND = 2001;

    /**
     * zjn 2018-12-11 收款对账单状态 2002：已确认
     */
    Integer RECEIVABLE_STATUS_CONFIRMEND = 2002;

    /**
     * zjn 2018-12-11 收款对账单状态 2004：未复核
     */
    Integer RECEIVABLE_STATUS_NOHANDOVER = 2004;

    /**
     * zjn 2018-12-11 收款对账单状态 2005：已复核
     */
    Integer RECEIVABLE_STATUS_HANDOVER = 2005;

    /**
     * zjn 2018-12-11 收款对账单状态 2003：已完成
     */
    Integer RECEIVABLE_STATUS_CHECK = 2003;

    /****************** 付 款 ****************************/

    /**
     * zjn 2018-12-10 付款对账单明细(云平台领用:原料入库)
     */
    Integer MATERIAL_INOUTFLAG_RAWINSTORE = 3;

    /**
     * zjn 2018-12-10 付款对账单明细新增操作状态 1001：未确认
     */
    Integer MATERIAL_STATUS_UNCONFIRM = 1001;

    /**
     * zjn 2018-12-10 付款对账单明细新增操作状态 1002：已确认
     */
    Integer MATERIAL_STATUS_CONFIRM = 1002;

    /**
     * zjn 2018-12-10 付款对账单明细新增操作状态 1003：未复核
     */
    Integer MATERIAL_STATUS_NOCHECK = 1003;

    /**
     * zjn 2018-12-10 付款对账单明细新增操作状态 1004：已复核
     */
    Integer MATERIAL_STATUS_CHECK = 1004;

    /**
     * zjn 2018-12-11 付款对账单状态 2001：未确认
     */
    Integer PAYABLE_STATUS_UNCONFIRMEND = 2001;

    /**
     * zjn 2018-12-11 付款对账单状态 2002：已确认
     */
    Integer PAYABLE_STATUS_CONFIRMEND = 2002;

    /**
     * zjn 2018-12-11 付款对账单状态 2004：未复核
     */
    Integer PAYABLE_STATUS_NOHANDOVER = 2004;

    /**
     * zjn 2018-12-11 付款对账单状态 2005：已复核
     */
    Integer PAYABLE_STATUS_HANDOVER = 2005;

    /**
     * zjn 2018-12-11 付款对账单状态 2003：已完成
     */
    Integer PAYABLE_STATUS_CHECK = 2003;

    /********************* 审批 ****************************/
    /**
     * tj 2018-12-24 审批：审批中
     */
    Integer APPROVAL_STATUS_WAIT = 60001;

    /**
     * tj 2018-12-24 审批：审批通过
     */
    Integer APPROVAL_STATUS_COMPLETE = 60002;

    /**
     * tj 2018-12-24 审批：审批驳回
     */
    Integer APPROVAL_STATUS_REJECT = 60003;

    /** zjn 2019-03-13 物联网共享仓库状态 */

    /**
     * zjn 2019-03-13 不自动领用：1001
     */
    String SHARE_HOUSE_STATUS_CONFIRM = "1001";

    /**
     * zjn 2019-03-13 自动领用：1002
     */
    String SHARE_HOUSE_STATUS_CANCLE = "1002";

    String APPROVE_ASSENT = "assent";

    String APPROVE_REJECT = "reject";

    String APPROVE_DEFAULT = "0";

    String APPROVE_END = "1";

    // 审批类型
    /**
     * 请假申请单
     */
    String OAAUDDITTYPE_LEAVE = "22001";

    /**
     * 采购申请单
     */
    String OAAUDDITTYPE_PURCHASE = "22002";

    /**
     * 报销申请单
     */
    String OAAUDDITTYPE_EXPENSE = "22003";

    /**
     * 离职申请单
     */
    String OAAUDDITTYPE_DEPARTURE = "22004";

    /**
     * 出差申请单
     */
    String OAAUDDITTYPE_TRAVEL = "22005";

    /**
     * 加班申请单
     */
    String OAAUDDITTYPE_OVERTIME = "22028";

    /**
     * 人员增补申请单
     */
    String OAAUDDITTYPE_RECRUIT = "22007";

    /**
     * 工作调动申请单
     */
    String OAAUDDITTYPE_TRANSFER = "22008";

    /**
     * 工作请示单
     */
    String OAAUDDITTYPE_ASK = "22009";

    /**
     * 转正申请单
     */
    String OAAUDDITTYPE_FORMAL = "22010";

    /**
     * 调休申请单
     */
    String OAAUDDITTYPE_REST = "22011";

    /**
     * 补卡申请单
     */
    String OAAUDDITTYPE_OFFSETTIME = "22012";

    /**
     * 付款申请单
     */
    String OAAUDDITTYPE_PAY = "22013";

    /**
     * 借款申请单
     */
    String OAAUDDITTYPE_LOAN = "22014";

    /**
     * 用印申请单
     */
    String OAAUDDITTYPE_STAMP = "22015";

    /**
     * 外出申请单
     */
    String OAAUDDITTYPE_GOOUT = "22016";

    /**
     * 外出申请单
     */
    String OAAUDDITTYPE_REPORT = "22017";

    /**
     * 奖金分配申请单
     */
    String OAAUDDITTYPE_BOUND = "22024";

    /**
     * 订单交期申请单
     */
    String OAAUDDITTYPE_ORDERDELIVERYTIME = "22025";

    /**
     * 记账凭证申请单
     */
    String OAAUDDITTYPE_ACCOUNTVOUCHER = "22029";

    /**
     * 记账凭证反审
     */
    String OAAUDDITTYPE_ACCOUNTVOUCHERTURN = "22030";
    /**
     * 申请单草稿状态
     */
    /**
     * 收付款单审批
     */
    String OAAUDDITTYPE_COLLECTPAYBILL = "22033";

    /**
     *应收单任务审批
     */
    String OAAUDDITTYPE_RECEIVETASK = "22034";

    /**
     * 应付单任务审批
     */
    String OAAUDDITTYPE_PAYMENTTASK = "22035";

    /**
     * 审批流申请单
     */
    String APPROVALFLOW = "22038";

    String APPLICATION_DRAFT = "1";

    /**
     * 申请单审核中状态
     */
    String APPLICATION_AUDITING = "2";

    /**
     * zjn 2019-07-31 成品
     */
    Integer REPORT_PRODUCT = 1;

    /**
     * zjn 2019-07-31 成品作废
     */
    Integer REPORT_PRODUCT_CANCEL = 2;

    /**
     * zjn 2019-07-31 原料
     */
    Integer REPORT_RawMaterial = 3;

    /**
     * zjn 2019-07-31 原料作废
     */
    Integer REPORT_RawMaterial_CANCEL = 4;

    /************************** zjn 2019-12-25 客户、供应商审批 begin ***********************************************/
    /**
     * zjn 2019-12-25 客户资料
     */
    Integer APPROVAL_CUSTOMER_INFO = 1;

    /**
     * zjn 2019-12-25 客户联系人
     */
    Integer APPROVAL_CUSTOMER_CONCAT = 2;

    /**
     * zjn 2019-12-25 客户子公司
     */
    Integer APPROVAL_CUSTOMER_BRACH = 3;

    /**
     * zjn 2019-12-25 业务员助理
     */
    Integer APPROVAL_SALES_ASSISTANT = 4;

    /**
     * zjn 2019-12-25 客户报价协议
     */
    Integer APPROVAL_CUSTOMER_DIFFICOFF = 5;

    /**
     * zjn 2019-12-25 删除客户资料
     */
    Integer APPROVAL_CUSTOMER_INFO_DELETE = 6;

    /**
     * zjn 2019-12-25 删除客户联系人
     */
    Integer APPROVAL_CUSTOMER_CONCAT_DELETE = 7;

    /**
     * zjn 2019-12-25 删除客户子公司
     */
    Integer APPROVAL_CUSTOMER_BRACH_DELETE = 8;

    /**
     * zjn 2019-12-25 删除业务员助理
     */
    Integer APPROVAL_SALES_ASSISTANT_DELETE = 9;

    /**
     * zjn 2019-12-25 默认客户联系人
     */
    Integer APPROVAL_CUSTOMER_CONCAT_DEFAULT = 10;

    /**
     * zjn 2019-12-25 供应商资料
     */
    Integer APPROVAL_SUPPLIER_INFO = 11;

    /**
     * zjn 2019-12-25 删除供应商资料
     */
    Integer APPROVAL_SUPPLIER_INFO_DELETE = 12;

    /**
     * zjn 2019-12-25 默认/取消默认材料供应商
     */
    Integer APPROVAL_SUPPLIER_DEFAULT = 13;

    /**
     * zjn 2020-01-11 移除客户附件
     */
    Integer APPROVAL_CUSTOMER_DELETE_ATT = 14;

    /**
     * zjn 2020-01-11 移除供应商附件
     */
    Integer APPROVAL_SUPPLIER_DELETE_ATT = 15;

    /**
     * zjn 2021-05-27 合同反审
     */
    Integer APPROVAL_CONTRACT_REFLASH = 16;

    /**
     * zjn 2021-05-27 通知单作废
     */
    Integer APPROVAL_NOTICATION_DOINVALID = 17;

    /**
     * zjn 2021-05-27 合同明细反审
     */
    Integer APPROVAL_CONTRACTDETAIL_REFLASH = 18;

    /**
     * zjn 2021-05-29 云平台订单审批
     */
    Integer APPROVAL_ICLOUDCONTRACT_ORDER = 19;

    /**
     * zjn 2021-05-29 erp订单审批
     */
    Integer APPROVAL_CONTRACT_ORDER = 20;

    /**
     * zjn 2021-08-29 erp加急审批
     */
    Integer APPROVAL_NOTIFICATION_ERPURGENT = 21;

    /**
     * zjn 2021-08-29 一键集控加急审批
     */
    Integer APPROVAL_NOTIFICATION_ICLOUDURGENT = 22;

    /**
     * zjn 2021-05-29 新erp合同明细费用
     */
    Integer APPROVAL_CONTRACTDETAIL_FEE = 23;

    /**
     * zjn 2021-11-18 客诉单审核进行审批
     */
    Integer APPROVAL_COMPLAINT_ONE = 24;

    /**
     * zjn 2021-11-18 客诉单品质确认进行审批
     */
    Integer APPROVAL_COMPLAINT_TWO = 25;

    /**
     * zjn 2021-12-02 生产检测品质确认进行审批
     */
    Integer APPROVAL_COMPLAINT_THREE = 26;

    /**
     * zjn 2021-12-02 客诉修复检测品质确认进行审批
     */
    Integer APPROVAL_COMPLAINT_FOUR = 27;

    /**
     * zjn 2021-12-31 业务费率记录
     */
    Integer APPROVAL_BUSINESS_RATES = 28;

    /**
     * fzl 2023-09-06 微信物料领用单
     */
    Integer APPROVAL_MATERIALUSE_WX = 29;

    /************************** zjn 2019-12-25 客户、供应商审批 end *************************************************/

    /**
     * 未制作
     */
    String NOTIFICATION_SCHEDULING_START = "1001";

    /**
     * 制作中
     */
    String NOTIFICATION_SCHEDULING_MIDDLE = "1002";

    /**
     * 已完成
     */
    String NOTIFICATION_SCHEDULING_END = "1003";

    /**
     * 订单移库出库
     */
    Integer ST_PRODUCT_INOUTTYPE_MOVE_OUT = 25;

    /**
     * 订单移库入库
     */
    Integer ST_PRODUCT_INOUTTYPE_MOVE_IN = 26;

    /**
     * 尾数移库出库
     */
    Integer ST_PRODUCT_INOUTTYPE_STOCKMOVE_OUT = 27;

    /**
     * 尾数移库入库
     */
    Integer ST_PRODUCT_INOUTTYPE_STOCKMOVE_IN = 28;

    /**
     * 尾数入库
     */
    Integer ST_PRODUCT_INOUTTYPE_MANTISSA_IN = 29;

    /**
     * 收款对账单sourceId是退货出库记录id
     */
    Integer SL_GOODS_CHECK_IOUTTYPE_ONE = 1;

    /**
     * 收款对账单sourceId是客诉单id
     */
    Integer SL_GOODS_CHECK_IOUTTYPE_TWO = 2;

    /**
     * 应收单状态未收款
     */
    Integer SL_SINGLE_RECEIVABLE_UNCONFIRM = 2001;

    /**
     * 应收单状态已申请
     */
    Integer SL_SINGLE_RECEIVABLE_CONFIRM = 2002;

    /**
     * 应收单状态已收款
     */
    Integer SL_SINGLE_RECEIVABLE_FINISH = 2003;

    /**
     * 应收单明细状态未收款
     */
    Integer SL_SINGLE_RECEIVABLE_DETAIL_UNCONFIRM = 1001;

    /**
     * 应收单明细已收款
     */
    Integer SL_SINGLE_RECEIVABLE_DETAIL_CONFIRM = 1002;

    /**
     * 应付单状态未申请
     */
    Integer SL_SINGLE_PAYABLE_UNCONFIRM = 2001;

    /**
     * 应付单状态已申请
     */
    Integer SL_SINGLE_PAYABLE_CONFIRM = 2002;

    /**
     * 应付单状态已付款
     */
    Integer SL_SINGLE_PAYABLE_UNPAIEDAMOUNT = 2004;

    /**
     * 应付单状态已付款
     */
    Integer SL_SINGLE_PAYABLE_PAIEDAMOUNT = 2003;

    /**
     * 应付单明细状态未申请
     */
    Integer SL_SINGLE_PAYABLE_DETAIL_UNCONFIRM = 1001;

    /**
     * 应付单明细状态已申请
     */
    Integer SL_SINGLE_PAYABLE_DETAIL_CONFIRM = 1002;
    
    /** 尾数区管理状态：未确认*/
    Integer ST_MANTISSA_STATUS_UNCONFIRM = 1001;
    
    /** 尾数区管理状态：已确认*/
    Integer ST_MANTISSA_STATUS_CONFIRM = 1002;

    Integer DEPART_ORDER = 1;

    /** 原料申请单：未合单*/
    Integer PU_PURCHASING_APPLY_MERGETY_ONE = 1001;

    /** 原料申请单：被合单*/
    Integer PU_PURCHASING_APPLY_MERGETY_TWO = 1002;

    /** 原料申请单：已被合单*/
    Integer PU_PURCHASING_APPLY_MERGETY_THREE = 1003;
    
    /** 报价单：审批不通过*/
    Integer QU_QUOTATION_APPROVAL_NOTPASS = 1001;

    /** 报价单：审批通过*/
    Integer QU_QUOTATION_APPROVAL_PASS = 1002;

    /** 报价单明细：审批不通过*/
    Integer QU_QUOTATION_DETAIL_APPROVAL_NOTPASS = 1001;

    /** 报价单明细：审批通过*/
    Integer QU_QUOTATION_DETAIL_APPROVAL_PASS = 1002;

    /** 供应商报价: 未确认（审批驳回）*/
    Integer Supplier_Quotation_APPROVAL_NOTPASS = 1001;

    /** 供应商报价: 已确认（审批通过）*/
    Integer Supplier_Quotation_APPROVAL_PASS = 1002;

    /** 原料报价单：未确认（审批驳回）*/
    Integer Material_Quotation_Version_NOTPASS = 1001;

    /** 原料报价单：已确认（审批通过）*/
    Integer Material_Quotation_Version_PASS = 1002;

    /** 客户业务员设置：未确认（审批驳回）*/
    Integer MD_CUSTOMER_SALESMAN_NOTPASS = 1001;

    /** 客户业务员设置：已确认（审批通过）*/
    Integer MD_CUSTOMER_SALESMAN_PASS = 1002;
}
