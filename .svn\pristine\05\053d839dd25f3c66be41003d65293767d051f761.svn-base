package com.kyb.pcberp.modules.finance.web;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.stock.entity.CostMaterialPriceMonth;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.service.MaterialService;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

@Controller
@RequestMapping(value = "${adminPath}/finance/finishedmaterialsinitialize")
public class FinishedMaterialsController extends BaseController
{
    @Autowired
    private MaterialService materialService;
    
    // @Autowired
    // private ParentMessageService parentMessageService;
    
    /**
     * 定位页面
     */
    @RequestMapping(value = "list")
    public String list()
    {
        return "modules/finance/finishedmaterials";
    }
    
    @RequiresPermissions("finance:finishedmaterialsinitialize:edit")
    @RequestMapping(value = "save")
    @ResponseBody
    public String save(@RequestBody CostMaterialPriceMonth materialPriceMonth)
    {
        materialPriceMonth.setCompany(UserUtils.getUser().getCompany());
        
        // 设置创建日期
        Calendar calendar = Calendar.getInstance();
        materialPriceMonth.setCreatedDate(calendar.getTime());
        
        // 设置月份
        calendar.add(Calendar.MONTH, -1);// 获取上个月月份
        calendar.set(Calendar.DAY_OF_MONTH, 5);
        materialPriceMonth.setMonth(DateUtils.formatDate(calendar.getTime()));
        
        Date upOneMonth_Start = DateUtils.getFirstDayOfMonth(calendar);
        Date upOneMonth_End = DateUtils.getLastDayOfMonth(calendar);
        
        // 查看看是否存在期初值
        materialPriceMonth.setStartDate(DateUtils.formatDate(upOneMonth_Start));
        materialPriceMonth.setEndDate(DateUtils.formatDate(upOneMonth_End));
        List<CostMaterialPriceMonth> materialPriceMonthList = new ArrayList<CostMaterialPriceMonth>();
        // costMaterialPriceMonthService.findList(materialPriceMonth);
        if (Collections3.isEmpty(materialPriceMonthList))
        {
            // ParentMessage parentMessage = new ParentMessage();
            // parentMessage.setMessageModuleCopy(TypeKey.MESSAGE_COLLECTIONCREATE.toString());
            // parentMessage.setMessaeDeail("编号为" + materialPriceMonth.getMaterialNo() + "已经设置了期初单价！");
            // parentMessage.setCreateByName(materialPriceMonth.getCreatedBy());
            // parentMessageService.sendMessage(parentMessage);
            
            // costMaterialPriceMonthService.save(materialPriceMonth);
            return "保存“" + materialPriceMonth.getMaterialNo() + "”成功！";
        }
        else
        {
            return "保存“" + materialPriceMonth.getMaterialNo() + "”失败，已经存在期初值！";
        }
    }
    
    /**
     * 加载初始数据
     * 
     * @param model
     * @return
     */
    @RequestMapping(value = "load/data")
    @ResponseBody
    public List<Material> loadData()
    {
        // 加载原物料
        Material material = new Material();
        material.setCompany(UserUtils.getUser().getCompany());
        material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_RAW);
        List<Material> materialList = materialService.findNeedSetMonthPriceList(material);
        
        // 返回数据
        return materialList;
    }
}
