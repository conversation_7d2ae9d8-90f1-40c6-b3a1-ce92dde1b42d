package com.kyb.pcberp.modules.finance.util;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.crm.dao.AccountsReceivableDao;
import com.kyb.pcberp.modules.crm.entity.AccountsReceivable;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.finance.dao.*;
import com.kyb.pcberp.modules.finance.entity.*;
import com.kyb.pcberp.modules.hr.finance_center.dao.Hr_CertificateDao;
import com.kyb.pcberp.modules.hr.finance_center.pojo.Hr_RecordDetail;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import com.kyb.pcberp.modules.sys.utils.CockpitUtilsOne;
import org.springframework.transaction.annotation.Transactional;

import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.utils.CommonUtils;
import com.kyb.pcberp.common.utils.SpringContextHolder;
import com.kyb.pcberp.modules.purch.dao.AccountsPayableDao;
import com.kyb.pcberp.modules.purch.dao.PayApplicationDao;
import com.kyb.pcberp.modules.purch.entity.AccountsPayable;
import com.kyb.pcberp.modules.purch.entity.PayApplication;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.User;

public class PayMoneyUtil
{
    private PayApplicationDao payApplicationDao = SpringContextHolder.getBean(PayApplicationDao.class);
    
    private PayMoneyDao payMoneyDao = SpringContextHolder.getBean(PayMoneyDao.class);
    
    private AccountsPayableDao accountsPayableDao = SpringContextHolder.getBean(AccountsPayableDao.class);
    
    private SinglePayableDao singlePayableDao = SpringContextHolder.getBean(SinglePayableDao.class);

    private SinglePayableDetailDao singlePayableDetailDao = SpringContextHolder.getBean(SinglePayableDetailDao.class);

    private SingleReceivableDao singleReceivableDao = SpringContextHolder.getBean(SingleReceivableDao.class);

    private SingleReceivableDetailDao singleReceivableDetailDao = SpringContextHolder.getBean(SingleReceivableDetailDao.class);

    private AccountsReceivableDao accountsReceivableDao = SpringContextHolder.getBean(AccountsReceivableDao.class);

    private Hr_CertificateDao hr_CertificateDao = SpringContextHolder.getBean(Hr_CertificateDao.class);

    public static PayMoneyUtil getInstance()
    {
        return new PayMoneyUtil();
    }
    
    public PayMoney payApplicationToPayMoney(PayApplication payApplication, Company company)
    {
        PayMoney payMoney = new PayMoney();
        payMoney.setCompany(company);
        payMoney
            .setBillNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PAYMENTRECORDS.getIndex().toString(), company));
        payMoney.setSupplier(payApplication.getSupplier());
        payMoney.setPayApplication(payApplication);
        payMoney.setPayDate(payApplication.getPayDate());
        payMoney.setPayMethod(payApplication.getPayMethod());
        payMoney.setAmount(payApplication.getAmount());
        payMoney.setPayBank(payApplication.getPayBank());
        payMoney.setPayAccount(payApplication.getPayAccount());
        payMoney.setFundType(payApplication.getFundType());
        payMoney.setCreatedDate(new Date());
        payMoney.setPayPeriod(payApplication.getPayPeriod());
        payMoney.setStatus(TypeKey.BILL_STATUS_NORMAL);
        payMoney.setCurrencyType(payApplication.getCurrencyType());
        return payMoney;
    }
    
    @Transactional(readOnly = false)
    public String savePayMoneyBean(PayMoney payMoney)
    {
        PayApplication payApplication = payApplicationDao.getById(payMoney.getApplicationId());
        if(null == payApplication || StringUtils.isBlank(payApplication.getRecordId()))
        {
            return "数据异常";
        }
        if(null == payMoney.getCompany())
        {
            payMoney.setCompany(payApplication.getCompany());
        }
        if(StringUtils.isBlank(payMoney.getUserId()))
        {
            payMoney.setUserId(payApplication.getCreatedBy().getRecordId());
        }
        // 申请单到付款单的适配器转换
        PayMoney insertPayMoney = payApplicationToPayMoney(payApplication, payMoney.getCompany());
        CommonUtils.updateNextNo(CommonEnums.CodeType.PAYMENTRECORDS.getIndex(), payMoney.getCompany());
        insertPayMoney.setRemark(payMoney.getRemark());
        insertPayMoney.setResponsiblePerson(new User(payMoney.getUserId()));
        payMoneyDao.insertPayMoney(insertPayMoney);

        // 更新已经对完账
        payApplication.setStatus(TypeKey.PU_PAY_APPLICATION_STATUS_APPROVE);
        payApplicationDao.updateStutas(payApplication);

        if (payApplication.getFundType().equals(TypeKey.FUND_TYPE_DUEPAYMENT.toString()))
        {
            BigDecimal addAmount = payApplication.getAmount();
            
            SinglePayable singlePayable = new SinglePayable();
            singlePayable.setRecordId(payApplication.getPayPeriod());
            SinglePayable payable = singlePayableDao.get(singlePayable);
            if (null != payable)
            {
                BigDecimal rcvAmount =
                    null == payable.getRcvAmount() ? BigDecimal.ZERO : payable.getRcvAmount(); // 待付金额
                BigDecimal paiedAmountOne =
                    null == payable.getPaiedAmount() ? BigDecimal.ZERO : payable.getPaiedAmount();// 已付款总金额
                payable.setPaiedAmount(paiedAmountOne.add(addAmount));// 已付款总金额

                rcvAmount = rcvAmount.subtract(addAmount);
                if(rcvAmount.compareTo(BigDecimal.ZERO) <= 0)
                {
                    payable.setStatus(TypeKey.SL_SINGLE_PAYABLE_PAIEDAMOUNT.toString());
                    singlePayableDao.updateStatus(payable);
                }
                // 更新付款金额
                singlePayableDao.updatePaiedAmount(payable);

                AccountsPayable accountsPayableCondition = new AccountsPayable();
                accountsPayableCondition.setSupplierId(payable.getSupplierId());
                accountsPayableCondition.setPeriod(payable.getPeriod());
                AccountsPayable accountsPayable =
                    accountsPayableDao.getAccountsPayableBySupplier(accountsPayableCondition);
                if (accountsPayable != null)
                {
                    BigDecimal amt =
                        null == accountsPayable.getPayAmount() ? BigDecimal.ZERO : accountsPayable.getPayAmount(); // 待付金额
                    BigDecimal paiedAmount =
                        null == accountsPayable.getPaiedAmount() ? BigDecimal.ZERO : accountsPayable.getPaiedAmount();// 已付款总金额
                    accountsPayable.setPaiedAmount(paiedAmount.add(addAmount));// 已付款总金额
                    amt = amt.subtract(addAmount);

                    if (amt.compareTo(BigDecimal.ZERO) > 0)
                    {
                        accountsPayable.setCompleteFlag(TypeKey.FALSE);
                    }
                    else
                    {
                        accountsPayable.setCompleteFlag(TypeKey.YES);
                        accountsPayable.setStatus(TypeKey.PAYABLE_STATUS_CHECK);
                        accountsPayableDao.updateStatus(accountsPayable);
                    }
                    accountsPayable.preUpdate();
                    accountsPayableDao.updateAccountsPayable(accountsPayable);
                }

                // 获取应付单明细
                List<SinglePayableDetail> payableDetailList = singlePayableDetailDao.getDetailList(payable);
                List<SinglePayableDetail> deList = Lists.newArrayList();
                for(SinglePayableDetail detail : payableDetailList)
                {
                    if(null == detail.getSingAmount())
                    {
                        continue;
                    }
                    BigDecimal singAmount = detail.getSingAmount();
                    if("3".equals(detail.getType()) || "6".equals(detail.getType()))
                    {
                        addAmount = addAmount.add(singAmount);
                    }
                    else
                    {
                        if(detail.getSingAmount().compareTo(addAmount) > 0)
                        {
                            singAmount = addAmount;
                            addAmount = BigDecimal.ZERO;
                        }
                        else
                        {
                            if(singAmount.compareTo(BigDecimal.ZERO) < 0)
                            {
                                singAmount = singAmount.multiply(new BigDecimal(-1));
                            }
                            addAmount = addAmount.subtract(singAmount);
                        }
                    }
                    detail.setDetailId(detail.getRecordId());
                    detail.setPaymoneyId(insertPayMoney.getRecordId());
                    detail.setConfirmAmount(singAmount);

                    detail.setRecordId(detail.getDetailId());
                    detail.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_DETAIL_CONFIRM.toString());
                    detail.preUpdate();
                    singlePayableDetailDao.updateStatus(detail);
                    deList.add(detail);
                    if(addAmount.compareTo(BigDecimal.ZERO) <= 0)
                    {
                        break;
                    }
                }
                if(Collections3.isNotEmpty(deList))
                {
                    insertPayMoney.setDetailList(deList);
                    singlePayableDetailDao.batchInsertSingleDetailCollect(insertPayMoney);
                }
            }
        }
        return "保存成功";
    }

    /**
     * zjn 2025-02-27 核销应收单
     * @param company 公司
     * @param customerId 客户
     * @param period 账期
     * @param amount 核销金额
     * @param operateType 1：确认，2：取消确认
     * @param paymentRecordId 收付款记录id
     */
    @Transactional(readOnly = false)
    public void writeOffReceivables(Company company,String customerId,Integer period,BigDecimal amount,String operateType,String paymentRecordId)
    {
        if(null == company || StringUtils.isBlank(customerId) || null == period || null == amount || StringUtils.isBlank(operateType) || StringUtils.isBlank(paymentRecordId))
        {
            return;
        }
        // 确认
        if("1".equals(operateType))
        {
            BigDecimal muchComputAmount = amount;

            // 获取应收单
            SingleReceivable srQuery = new SingleReceivable();
            srQuery.setCustomer(new Customer(customerId));
            srQuery.setPeriod(period);
            SingleReceivable sr = singleReceivableDao.get(srQuery);
            if(null == sr)
            {
                return;
            }
            // 应收单明细
            List<SingleReceivableDetail> detailList = singleReceivableDetailDao.getDetailList(sr);
            if(Collections3.isEmpty(detailList))
            {
                return;
            }
            List<Hr_RecordDetail> recordDetailList = Lists.newArrayList();
            for(SingleReceivableDetail detail : detailList)
            {
                if(null == detail.getSingAmount())
                {
                    continue;
                }
                BigDecimal singAmount = detail.getSingAmount();

                if(!"5".equals(detail.getType()) && null != detail.getInOutFlag() && detail.getInOutFlag().compareTo(0) == 0)
                {
                    amount = amount.add(singAmount);
                }
                else
                {
                    if(detail.getSingAmount().compareTo(amount) > 0)
                    {
                        singAmount = amount;
                        amount = BigDecimal.ZERO;
                    }
                    else
                    {
                        amount = amount.subtract(singAmount);
                    }
                }
                detail.setConfirmAmount(singAmount);
                detail.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_DETAIL_CONFIRM.toString());
                detail.preUpdate();
                singleReceivableDetailDao.updateStatus(detail);

                Hr_RecordDetail recordDetail = new Hr_RecordDetail();
                recordDetail.setPaymentRecordId(paymentRecordId);
                recordDetail.setDetailId(detail.getRecordId());
                recordDetail.setType(1);
                recordDetail.setAmount(singAmount);
                recordDetailList.add(recordDetail);

                if(amount.compareTo(BigDecimal.ZERO) <= 0)
                {
                    break;
                }
            }

            if(Collections3.isNotEmpty(recordDetailList))
            {
                hr_CertificateDao.batchInsertRecordDetail(recordDetailList);
            }

            // 如果款项类型为应收款时，不关联合同信息
            AccountsReceivable ac = new AccountsReceivable();
            ac.setCompany(company);
            ac.setCustomer(sr.getCustomer());
            ac.setPeriod(sr.getPeriod());
            // 如果收款金额大于0 就正常添加
            // 查询该客户的传过来的核销日期对应的的收款对账单
            List<AccountsReceivable> al =
                    accountsReceivableDao.findAccountsReceivableListByBeanOrderByPeriod(ac);
            AccountsReceivable accountsReceivable = al.get(0);
            // 期末余款 就是还需要收取的金额
            BigDecimal amt = sr.getRcvAmount() == null ? BigDecimal.ZERO : sr.getRcvAmount();
            BigDecimal amtTwo = accountsReceivable.getRcvAmount() == null ? BigDecimal.ZERO : accountsReceivable.getRcvAmount();
            // 本期已收收款金额
            BigDecimal receivedAmount = sr.getReceivedAmount() == null ? BigDecimal.ZERO : sr.getReceivedAmount();
            // 如果期未余额大于现在的收款
            if (amt.compareTo(muchComputAmount) == 1)
            {
                // 对账单收到的款项+现在付的款项=现在收到的总的款项
                accountsReceivable.setReceivedAmount(receivedAmount.add(muchComputAmount));
                // 此对账单没有完成
                accountsReceivable.setCompleteFlag(TypeKey.FALSE);
                // 更新对账单
                accountsReceivableDao.updateAccountsReceivable(accountsReceivable);

            }
            // 如果余款等于于现在的收款
            else if (amt.compareTo(muchComputAmount) <= 0)
            {
                accountsReceivable.setReceivedAmount(receivedAmount.add(amt));
                if(amtTwo.compareTo(amt) <= 0)
                {
                    accountsReceivable.setCompleteFlag(TypeKey.YES);
                }
                else
                {
                    accountsReceivable.setCompleteFlag(TypeKey.FALSE);
                }
                accountsReceivableDao.updateAccountsReceivable(accountsReceivable);

                sr.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_FINISH.toString());
                singleReceivableDao.updateStatus(sr);
            }
            singleReceivableDao.updateOtherAmountData(sr);
        }
        // 取消确认
        else if("2".equals(operateType))
        {
            Hr_RecordDetail recordDetail = new Hr_RecordDetail();
            recordDetail.setPaymentRecordId(paymentRecordId);
            List<Hr_RecordDetail> recordDetailList = hr_CertificateDao.getRecordDetailList(recordDetail);
            if(Collections3.isEmpty(recordDetailList))
            {
                return;
            }
            // 获取应收单
            SingleReceivable singleReceivable = new SingleReceivable();
            singleReceivable.setCompany(company);
            singleReceivable.setCustomer(new Customer(customerId));
            singleReceivable.setPeriod(period);
            singleReceivable = singleReceivableDao.get(singleReceivable);
            if (null != singleReceivable)// 正常冲红
            {
                singleReceivable.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_UNCONFIRM.toString());
                singleReceivableDao.updateStatus(singleReceivable);

                for (Hr_RecordDetail detail : recordDetailList)
                {
                    SingleReceivableDetail sd = new SingleReceivableDetail();
                    sd.setRecordId(detail.getDetailId());
                    sd.setConfirmAmount(detail.getAmount().multiply(new BigDecimal(-1)));
                    sd.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_DETAIL_UNCONFIRM.toString());
                    singleReceivableDetailDao.updateStatus(sd);

                    hr_CertificateDao.deleteSingleDetailRecords(detail);
                }

                singleReceivableDao.updateOtherAmountData(singleReceivable);
                accountsReceivableDao.updateOtherAmountData(singleReceivable);
            }
        }
    }

    /**
     * zjn 2025-02-27 核销应付单
     * @param company 公司
     * @param supplierId 供应商
     * @param period 账期
     * @param amount 核销金额
     * @param operateType 1：确认，2：取消确认
     * @param paymentRecordId 收付款记录id
     */
    @Transactional(readOnly = false)
    public void writeOffPayable(Company company,String supplierId,Integer period,BigDecimal amount,String operateType,String paymentRecordId)
    {
        if(null == company || StringUtils.isBlank(supplierId) || null == period || null == amount || StringUtils.isBlank(operateType) || StringUtils.isBlank(paymentRecordId))
        {
            return;
        }
        // 确认
        if("1".equals(operateType))
        {
            BigDecimal addAmount = amount;

            SinglePayable singlePayable = new SinglePayable();
            singlePayable.setSupplier(new Supplier(supplierId));
            singlePayable.setPeriod(period);
            SinglePayable payable = singlePayableDao.get(singlePayable);
            if (null != payable)
            {
                BigDecimal rcvAmount =
                        null == payable.getRcvAmount() ? BigDecimal.ZERO : payable.getRcvAmount(); // 待付金额
                BigDecimal paiedAmountOne =
                        null == payable.getPaiedAmount() ? BigDecimal.ZERO : payable.getPaiedAmount();// 已付款总金额
                payable.setPaiedAmount(paiedAmountOne.add(addAmount));// 已付款总金额

                rcvAmount = rcvAmount.subtract(addAmount);
                if(rcvAmount.compareTo(BigDecimal.ZERO) <= 0)
                {
                    payable.setStatus(TypeKey.SL_SINGLE_PAYABLE_PAIEDAMOUNT.toString());
                    singlePayableDao.updateStatus(payable);
                }
                // 更新付款金额
                singlePayableDao.updatePaiedAmount(payable);

                AccountsPayable accountsPayableCondition = new AccountsPayable();
                accountsPayableCondition.setSupplierId(payable.getSupplierId());
                accountsPayableCondition.setPeriod(payable.getPeriod());
                AccountsPayable accountsPayable =
                        accountsPayableDao.getAccountsPayableBySupplier(accountsPayableCondition);
                if (accountsPayable != null)
                {
                    BigDecimal amt =
                            null == accountsPayable.getPayAmount() ? BigDecimal.ZERO : accountsPayable.getPayAmount(); // 待付金额
                    BigDecimal paiedAmount =
                            null == accountsPayable.getPaiedAmount() ? BigDecimal.ZERO : accountsPayable.getPaiedAmount();// 已付款总金额
                    accountsPayable.setPaiedAmount(paiedAmount.add(addAmount));// 已付款总金额
                    amt = amt.subtract(addAmount);

                    if (amt.compareTo(BigDecimal.ZERO) > 0)
                    {
                        accountsPayable.setCompleteFlag(TypeKey.FALSE);
                    }
                    else
                    {
                        accountsPayable.setCompleteFlag(TypeKey.YES);
                        accountsPayable.setStatus(TypeKey.PAYABLE_STATUS_CHECK);
                        accountsPayableDao.updateStatus(accountsPayable);
                    }
                    accountsPayable.preUpdate();
                    accountsPayableDao.updateAccountsPayable(accountsPayable);
                }

                // 获取应付单明细
                List<SinglePayableDetail> payableDetailList = singlePayableDetailDao.getDetailList(payable);

                List<Hr_RecordDetail> recordDetailList = Lists.newArrayList();
                for(SinglePayableDetail detail : payableDetailList)
                {
                    if(null == detail.getSingAmount())
                    {
                        continue;
                    }
                    BigDecimal singAmount = detail.getSingAmount();
                    if("3".equals(detail.getType()) || "6".equals(detail.getType()))
                    {
                        addAmount = addAmount.add(singAmount);
                    }
                    else
                    {
                        if(detail.getSingAmount().compareTo(addAmount) > 0)
                        {
                            singAmount = addAmount;
                            addAmount = BigDecimal.ZERO;
                        }
                        else
                        {
                            if(singAmount.compareTo(BigDecimal.ZERO) < 0)
                            {
                                singAmount = singAmount.multiply(new BigDecimal(-1));
                            }
                            addAmount = addAmount.subtract(singAmount);
                        }
                    }
                    detail.setDetailId(detail.getRecordId());
                    detail.setConfirmAmount(singAmount);

                    detail.setRecordId(detail.getDetailId());
                    detail.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_DETAIL_CONFIRM.toString());
                    detail.preUpdate();
                    singlePayableDetailDao.updateStatus(detail);


                    Hr_RecordDetail recordDetail = new Hr_RecordDetail();
                    recordDetail.setPaymentRecordId(paymentRecordId);
                    recordDetail.setDetailId(detail.getRecordId());
                    recordDetail.setType(2);
                    recordDetail.setAmount(singAmount);
                    recordDetailList.add(recordDetail);

                    if(addAmount.compareTo(BigDecimal.ZERO) <= 0)
                    {
                        break;
                    }
                }
                if(Collections3.isNotEmpty(recordDetailList))
                {
                    hr_CertificateDao.batchInsertRecordDetail(recordDetailList);
                }
            }
        }
        // 取消确认
        else if("2".equals(operateType))
        {
            Hr_RecordDetail recordDetail = new Hr_RecordDetail();
            recordDetail.setPaymentRecordId(paymentRecordId);
            List<Hr_RecordDetail> recordDetailList = hr_CertificateDao.getRecordDetailList(recordDetail);
            if(Collections3.isEmpty(recordDetailList))
            {
                return;
            }
            // 获取应付单
            SinglePayable singlePayable = new SinglePayable();
            singlePayable.setCompany(company);
            singlePayable.setSupplier(new Supplier(supplierId));
            singlePayable.setPeriod(period);
            singlePayable = singlePayableDao.get(singlePayable);
            if (null != singlePayable)// 正常冲红
            {
                singlePayable.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_UNCONFIRM.toString());
                singlePayableDao.updateStatus(singlePayable);

                for (Hr_RecordDetail detail : recordDetailList)
                {
                    SinglePayableDetail sd = new SinglePayableDetail();
                    sd.setRecordId(detail.getDetailId());
                    sd.setConfirmAmount(detail.getAmount().multiply(new BigDecimal(-1)));
                    sd.setStatus(TypeKey.SL_SINGLE_RECEIVABLE_DETAIL_UNCONFIRM.toString());
                    singlePayableDetailDao.updateStatus(sd);

                    hr_CertificateDao.deleteSingleDetailRecords(detail);
                }
                singlePayableDao.updateOtherAmountTwoData(singlePayable);
                accountsPayableDao.updateOtherAmountData(singlePayable);
            }
        }
    }
}
