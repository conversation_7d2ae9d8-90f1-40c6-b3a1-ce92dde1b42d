<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.crm.dao.SlCustomerOrderDetailDao">
    
	<sql id="slCustomerOrderDetailColumns">
		a.recordId ,
		a.companyId AS "company.recordId",
		a.customerorderId AS "customerorderId.recordId",
		a.customerId AS "customerId.recordId",
		a.sampleAmt AS "sampleAmt",
		a.unitWidth AS "unitWidth",
		a.unitLength AS "unitLength",
		a.pnlWidth AS "pnlWidth",
		a.pnlLength AS "pnlLength",
		a.pnlDivisor AS "pnlDivisor",
		a.customermodel AS "customermodel",
		a.boardLevel AS "boardLevel",
		a.materialType AS "materialType",
		a.boardThickness AS "boardThickness",
		a.copperCladThickness AS "copperCladThickness",
		a.surfaceProcess AS "surfaceProcess",
		a.solderMaskType AS "solderMaskType",
		a.characterType AS "characterType",
		a.shapingWay AS "shapingWay",
		a.testMethod AS "testMethod",
		a.lingeSpacing AS "lingeSpacing",
		a.smallAperture AS "smallAperture",
		a.halAhole AS "halAhole",
		a.buryBlindHole AS "buryBlindHole",
		a.deliveryUrgent AS "deliveryUrgent",
		a.resistance AS "resistance",
		a.productionType AS "productionType",
		a.remark AS "remark",
		a.price AS "price",
		a.engineeringFee AS "engineeringFee",
		a.totalAmt AS "totalAmt",
		a.activeFlag AS "activeFlag",
		a.createdBy AS "createdBy",
		a.createdDate AS "createdDate",
		a.deliveryDays AS "deliveryDays",
		a.totalAmtBefore AS "totalAmtBefore",
		a.deailArea AS "deailArea",
		a.daore,
		a.naiya,
		a.pliesnumber
	</sql>
	
	<sql id="slCustomerOrderDetailJoins">
	</sql>
    
	<select id="get" resultType="SlCustomerOrderDetail">
		SELECT 
			<include refid="slCustomerOrderDetailColumns"/>
		FROM sl_customer_order_detail a
		<include refid="slCustomerOrderDetailJoins"/>
		WHERE a.recordId = #{recordId}
	</select>
	
	
	
	<!-- 根据下单编号去查询所有明细 -->
	<select id="getAllByCustomerOrder"  resultType="SlCustomerOrderDetail">
		select 
		    a.recordId ,
		    a.companyId AS "company.recordId",
			a.customerorderId AS "customerorderId.recordId",
			a.customerId AS "customerId.recordId",
			a.sampleAmt AS "sampleAmt",
			a.unitWidth AS "unitWidth",
			a.unitLength AS "unitLength",
			a.pnlWidth AS "pnlWidth",
			a.pnlLength AS "pnlLength",
			a.pnlDivisor AS "pnlDivisor",
			a.customermodel AS "customermodel",
			a.boardLevel AS "boardLevel",
			a.materialType AS "materialType",
			a.boardThickness AS "boardThickness",
			a.resistance AS "resistance",
		    a.productionType AS "productionType",
			a.copperCladThickness AS "copperCladThickness",
			a.surfaceProcess AS "surfaceProcess",
			a.solderMaskType AS "solderMaskType",
			a.characterType AS "characterType",
			a.shapingWay AS "shapingWay",
			a.testMethod AS "testMethod",
			a.lingeSpacing AS "lingeSpacing",
			a.smallAperture AS "smallAperture",
			a.halAhole AS "halAhole",
			a.buryBlindHole AS "buryBlindHole",
			a.deliveryUrgent AS "deliveryUrgent",
			a.remark AS "remark",
			a.price AS "price",
			a.engineeringFee AS "engineeringFee",
			a.totalAmt AS "totalAmt",
			a.activeFlag AS "activeFlag",
			a.createdBy AS "createdBy",
			a.createdDate AS "createdDate",
		    a.deliveryDays AS "deliveryDays",
		    a.totalAmtBefore AS "totalAmtBefore",
		    a.deailArea AS "deailArea",
		    a.daore,
		    a.naiya,
		    a.pliesnumber
		 from sl_customer_order_detail a where a.customerOrderId = #{recordId} and a.activeFlag = 1;
	</select>
	
	
	<select id="findList" resultType="SlCustomerOrderDetail">
		SELECT 
			<include refid="slCustomerOrderDetailColumns"/>
		FROM sl_customer_order_detail a
		<include refid="slCustomerOrderDetailJoins"/>
		<where>
			
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="SlCustomerOrderDetail">
		SELECT 
			<include refid="slCustomerOrderDetailColumns"/>
		FROM sl_customer_order_detail a
		<include refid="slCustomerOrderDetailJoins"/>
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<insert id="insert">
		INSERT INTO sl_customer_order_detail(
			companyId,
			customerorderId,
			customerId,
			sampleAmt,
			unitWidth,
			unitLength,
			pnlLength,
			pnlWidth,
			pnlDivisor,
			customerModel, 
			boardLevel,
			materialType,
			boardThickness,
			copperCladThickness,
			surfaceProcess,
			solderMaskType,
			characterType,
			shapingWay,
			testMethod,
			lingeSpacing,
			smallAperture,
			halAhole,
			buryBlindHole,
			resistance,
			deliveryUrgent,
			remark,
			productionType,
			price,
			engineeringFee,
			totalAmt,
			activeFlag, 
			createdBy, 
			createdDate,
			deliveryDays,
			totalAmtBefore,
			deailArea,
			daore,
			naiya,
			pliesnumber
		) VALUES (
			#{company.recordId},
			#{customerorderId.recordId},
			#{customerId.recordId},
			#{sampleAmt},
			#{unitWidth},
			#{unitLength},
			#{pnlLength},
			#{pnlWidth},
			#{pnlDivisor},
			#{customerModel},
			#{boardLevel},
			#{materialType},
			#{boardThickness},
			#{copperCladThickness},
			#{surfaceProcess},
			#{solderMaskType},
			#{characterType},
			#{shapingWay},
			#{testMethod},
			#{lingeSpacing},
			#{smallAperture},
			#{halAhole},
			#{buryBlindHole},
			#{resistance},
			#{deliveryUrgent},
			#{remark},
			#{productionType},
			#{price},
			#{engineeringFee},
			#{totalAmt},
			1,
			#{createdBy.recordId},
			now(),
			#{deliveryDays},
			#{totalAmtBefore},
			#{deailArea},
			#{daore},
			#{naiya},
			#{pliesnumber}
		)
	</insert>
	
	<update id="update">
		UPDATE sl_customer_order_detail SET
			sampleAmt=#{sampleAmt},
			unitWidth=#{unitWidth},
			unitLength=#{unitLength },
			pnlWidth=#{pnlWidth },
			pnlLength=#{pnlLength },
			pnlDivisor=#{pnlDivisor},
			customerModel=#{customerModel},
			boardLevel=#{boardLevel},
			materialType=#{materialType},
			boardThickness=#{boardThickness},
			copperCladThickness=#{copperCladThickness},
			surfaceProcess=#{surfaceProcess},
			solderMaskType=#{solderMaskType},
			characterType=#{characterType},
			shapingWay=#{shapingWay},
			testMethod=#{testMethod},
			lingeSpacing=#{lingeSpacing},
			smallAperture=#{smallAperture},
			halAhole=#{halAhole},
			buryBlindHole=#{buryBlindHole},
			resistance=#{resistance},
			deliveryUrgent=#{deliveryUrgent},
			remark=#{remark},
			productionType=#{productionType},
			price=#{price},
			engineeringFee=#{engineeringFee},
			totalAmt=#{totalAmt},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			deliveryDays = #{deliveryDays},
			totalAmtBefore = #{totalAmtBefore},
			deailArea = #{deailArea},
			daore = #{daore},
			naiya = #{naiya},
			pliesnumber = #{pliesnumber}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateStatus">
		update sl_customer_order a set 
		status = 10010703 
		where a.recordId = (select customerOrderId from sl_customer_order_detail where recordId = #{recordId})
	</update>
	
	
	
	<update id="delete">
		UPDATE  sl_customer_order_detail SET
		activeFlag = 2
		WHERE recordId = #{recordId}
	</update>
	
		<update id="deleteByslCustomerOrderId">
		UPDATE  sl_customer_order_detail SET
		activeFlag = 2
		WHERE customerorderId = #{recordId}
	</update>
	
	<update id="updateCustFlag">
	    update sl_customer_order_detail set changeFlag = 201601 where recordId = #{recordId}
	</update>
	
	<update id="updateDelCh">
		update sl_customer_order_detail set changeFlag=null where recordId = #{recordId}
	</update>
	
	<update id="updateChF">
	    UPDATE sl_customer_order_detail SET changeFlag = NULL WHERE recordId IN 
	    (SELECT customerOrderDetailId FROM sl_contract_detail WHERE contractId = #{recordId} and companyId =#{companyId})
	</update>
	
	<!-- 添加合同时根据下单编号去查询所有明细 -->
	<select id="getAllByCust"  resultType="SlCustomerOrderDetail">
			select 
				a.recordId ,
		    	a.companyId AS "company.recordId",
				a.customerorderId AS "customerorderId.recordId",
				a.customerId AS "customerId.recordId",
				a.sampleAmt AS "sampleAmt",
				a.unitWidth AS "unitWidth",
				a.unitLength AS "unitLength",
				a.pnlWidth AS "pnlWidth",
				a.pnlLength AS "pnlLength",
				a.pnlDivisor AS "pnlDivisor",
				a.customermodel AS "customermodel",
				a.boardLevel AS "boardLevel",
				a.materialType AS "materialType",
				a.boardThickness AS "boardThickness",
				a.copperCladThickness AS "copperCladThickness",
				a.surfaceProcess AS "surfaceProcess",
				a.solderMaskType AS "solderMaskType",
				a.characterType AS "characterType",
				a.shapingWay AS "shapingWay",
				a.testMethod AS "testMethod",
				a.lingeSpacing AS "lingeSpacing",
				a.smallAperture AS "smallAperture",
				a.halAhole AS "halAhole",
				a.buryBlindHole AS "buryBlindHole",
				a.deliveryUrgent AS "deliveryUrgent",
				a.resistance AS "resistance",
		        a.productionType AS "productionType",
				a.remark AS "remark",
				a.price AS "price",
				a.engineeringFee AS "engineeringFee",
				a.totalAmt AS "totalAmt",
				a.activeFlag AS "activeFlag",
				a.createdBy AS "createdBy",
				a.createdDate AS "createdDate",
				a.deliveryDays AS "deliveryDays",
				a.totalAmtBefore AS "totalAmtBefore",
				a.deailArea AS "deailArea",
				a.daore,
				a.naiya,
				a.pliesnumber
		 from sl_customer_order_detail a where a.customerOrderId = #{recordId} and a.activeFlag = 1 and changeFlag IS NULL;
	</select>
	
	<select id="getDelCust" resultType="SlCustomerOrderDetail">
	    select * from sl_customer_order_detail sc  INNER join 
	    (SELECT customerOrderDetailId FROM sl_contract_detail WHERE contractId = #{recordId} and companyId =#{companyId}) cu
	    on sc.recordId = cu.customerOrderDetailId
	</select>
	
	<select id="getCustNum" resultType="Integer">
	     SELECT count(customerOrderId) FROM sl_customer_order_detail WHERE customerOrderId in
	    ( SELECT customerOrderId FROM sl_customer_order_detail WHERE recordId = #{recordId}) AND changeFlag = 201601
	</select>
	
	<update id="updateTotalAmt">
		UPDATE sl_customer_order_detail SET
			totalAmt=#{totalAmt},
			totalAmtBefore = #{totalAmtBefore},
			deailArea = #{deailArea}
		WHERE recordId = #{recordId}
	</update>
</mapper>