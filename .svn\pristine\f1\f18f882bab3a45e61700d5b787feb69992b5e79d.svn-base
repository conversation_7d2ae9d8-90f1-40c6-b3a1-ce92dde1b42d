package com.kyb.pcberp.modules.production.util.factory;

import java.util.HashMap;
import java.util.Map;

import com.kyb.pcberp.modules.production.entity.ProduceRecord;
import com.kyb.pcberp.modules.production.util.machine.PdMachine;
import com.kyb.pcberp.modules.production.util.pdEnum.PdEnum;

public class PdFactory
{
    /**
     * 2019-02-19 为了线程安全，采用饿汉式设计
     */
    private PdFactory()
    {
    
    }
    
    private static PdFactory pdFactory = new PdFactory();
    
    public static PdFactory getInstance()
    {
        return pdFactory;
    }
    
    public Map<String, Object> PdFactoryStart(PdEnum pdEnum,ProduceRecord produceRecord)
    {
        Map<String, Object> result = new HashMap<>();
        PdMachine pdMachine = new PdMachine();
        switch (pdEnum)
        {
            // 原料出库
            case STOCK:
                pdMachine.stock(produceRecord);
                break;
            // 接板
            case TAKE:
                pdMachine.take(produceRecord);
                break;
            // 交板
            case HAND:
                pdMachine.hand(produceRecord);
                break;
            // 交接板
            case TANKHAND:
                pdMachine.tankHand(produceRecord);
                break;                
            default:
        }
        return result;
    }
}
