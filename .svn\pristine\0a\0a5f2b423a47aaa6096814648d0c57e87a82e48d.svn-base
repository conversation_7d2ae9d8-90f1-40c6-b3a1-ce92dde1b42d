﻿<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.approval.dao.AllocationDao">
	
	<sql id="allocationColumns">
		a.recordId,
		a.companyId AS "company.recordId",
		a.typeId AS "type.recordId",
		a.quota,
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.activeFlag,
		a.area,
		a.urgentFlag,
		a.profitRate,
		a.quotaMode,
		a.areaMode,
		a.profitRateMode,
		a.typeApproval,
		a.carbonCopyId,
		a.supplierChainFlag,
		a.materialType
	</sql>
	
	<sql id="positionColumns">
		a.recordId,
		a.companyId AS "company.recordId",
		a.name,
		a.status,
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.activeFlag
	</sql>
	
	<select id="get" resultType="Allocation">
		SELECT 
			<include refid="allocationColumns"/>,
			ri.mouldId AS "menuList.recordId"
		FROM sm_procedure a
		JOIN sm_procedure_item ri ON ri.processId = a.recordId
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="findList" resultType="Allocation">
		SELECT <!-- DISTINCT -->
			<include refid="allocationColumns"/>,
			b.name AS "type.name",
			b.status AS "type.status"
		FROM md_approval_allocation a
		LEFT JOIN md_approval_type b ON b.recordId = a.typeId
		WHERE a.activeFlag = #{DEL_FLAG_NORMAL} AND a.companyId = #{company.recordId}
		<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
			AND a.createdBy = #{createdBy.recordId}
		</if>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
				ORDER BY a.typeId,a.recordId DESC 
			</otherwise>
		</choose>
	</select>
	
	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO md_approval_allocation(
			companyId,
			typeId,
			quota,
		  	activeFlag,
		  	createdBy,
		  	createdDate,
		  	lastUpdBy,
		  	lastUpdDate,
		  	area,
		  	urgentFlag,
		  	profitRate,
		  	quotaMode,
		  	areaMode,
		  	profitRateMode,
			typeApproval,
			carbonCopyId,
			supplierChainFlag,
			materialType
		) VALUES (
			#{company.recordId},
			#{type.recordId},
			#{quota},
		  	#{activeFlag},
		  	#{createdBy.recordId},
		 	#{createdDate},
		  	#{lastUpdBy.recordId},
		 	#{lastUpdDate},
		 	#{area},
		 	#{urgentFlag},
		 	#{profitRate},
		 	#{quotaMode},
		 	#{areaMode},
		 	#{profitRateMode},
		 	#{typeApproval},
		  	#{carbonCopyId},
		  	#{supplierChainFlag},
		    #{materialType}
		)
	</insert>
	
	<update id="update">
		UPDATE md_approval_allocation SET 	
			quota = #{quota},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			remark = #{remark},
			area = #{area},
			urgentFlag = #{urgentFlag},
			profitRate = #{profitRate},
			quotaMode = #{quotaMode},
		  	areaMode = #{areaMode},
		  	profitRateMode = #{profitRateMode},
			typeApproval = #{typeApproval},
			carbonCopyId = #{carbonCopyId},
			supplierChainFlag = #{supplierChainFlag},
			materialType = #{materialType}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE md_approval_allocation SET 
			activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="allPosition" resultType="EmployeePosition">
		SELECT 
			<include refid="positionColumns"/> 
		FROM md_employee_position a
		WHERE a.companyId = #{company.recordId}
		AND a.status = 5
		AND a.activeFlag = 1
	</select>
	
	<insert id="savePositions" parameterType="java.util.List">
		INSERT INTO md_employee_position(
			companyId,
			name,
			status,
		  	activeFlag,
		  	createdBy,
		  	createdDate,
		  	lastUpdBy,
		  	lastUpdDate
		)VALUES
		<foreach collection="list" item="emp" separator=",">
			(
				#{emp.company.recordId},
				#{emp.name},
				#{emp.status},
		  		#{emp.activeFlag},
		  		#{emp.createdBy.recordId},
		 		#{emp.createdDate},
		  		#{emp.lastUpdBy.recordId},
		 		#{emp.lastUpdDate}
			)
		</foreach>
	</insert>
	
	<select id="allApprovalType" resultType="ApprovalType">
		SELECT 
			recordId,
			name,
			status,
			activeFlag
		FROM md_approval_type a
		WHERE a.activeFlag = 1
	</select>
	
	<select id="verificationtype" resultType="String">
		SELECT COUNT(a.recordId) FROM md_approval_allocation a 
		WHERE a.typeId = #{type.recordId} 
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
		AND a.companyId = #{company.recordId}
		<if test="quota != null and quota != ''">
			AND a.quota = #{quota}
		</if>
		<if test="typeApproval != null and typeApproval !=''">
			AND a.typeApproval = #{typeApproval}
		</if>
		<if test="supplierChainFlag != null and supplierChainFlag != ''">
			AND a.supplierChainFlag = #{supplierChainFlag}
		</if>
		<if test="materialType != null and materialType != ''">
			AND a.materialType = #{materialType}
		</if>
	</select>
	
	<select id="finAllocationByType" resultType="Allocation">
		SELECT  
			a.recordId,
			a.typeId AS "type.recordId",
			at.name AS "type.name",
			a.companyId AS "company.recordId"
		FROM md_approval_allocation a 
		LEFT JOIN md_approval_type at ON at.recordId = a.typeId
		WHERE a.typeId = #{typeId}
		AND a.companyId = #{company.recordId} 
		AND a.activeFlag = 1
		<if test="quota != null and quota != ''">
			AND a.quota = #{quota}
		</if>
		<if test="area != null and area != ''">
			AND a.area = #{area}
		</if>
		<if test="profitRate != null and profitRate != ''">
			AND a.profitRate = #{profitRate}
		</if>
		<if test="typeApproval != null and typeApproval !=''">
			AND a.typeApproval = #{typeApproval}
		</if>
		<if test="supplierChainFlag != null and supplierChainFlag !=''">
			AND a.supplierChainFlag = #{supplierChainFlag}
		</if>
		<if test="typeId == 16">
			<if test="supplierChainFlag == null or supplierChainFlag ==''">
				AND a.supplierChainFlag IS NULL
			</if>
		</if>
		<if test="materialType != null and materialType != ''">
			AND (a.materialType = #{materialType} OR a.materialType IS NULL)
		</if>
		LIMIT 0,1
	</select>
	
	<select id="findQuota" resultType="Allocation">
		SELECT
			a.quota,
			a.quotaMode
		FROM md_approval_allocation a
		WHERE a.typeId = #{typeId} 
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
		AND a.companyId = #{company.recordId}
		AND a.quota IS NOT NULL
		GROUP BY a.quota
		ORDER BY a.quota DESC
	</select>
	
	<select id="verificationtype2" resultType="String">
		SELECT COUNT(a.recordId) FROM md_approval_allocation a 
		WHERE a.typeId = #{type.recordId} 
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
		AND a.companyId = #{company.recordId}
		<if test="quota != null and quota != ''">
			AND a.quota = #{quota}
		</if>
		<if test="typeApproval != null and typeApproval !=''">
			AND a.typeApproval = #{typeApproval}
		</if>
		AND a.recordId <![CDATA[<>]]> #{recordId}
	</select>
	
	<select id="findArea" resultType="Allocation">
		SELECT
			a.area,
			a.areaMode
		FROM md_approval_allocation a
		WHERE a.typeId = #{typeId} 
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
		AND a.companyId = #{company.recordId}
		AND a.area IS NOT NULL 
		GROUP BY a.area
		ORDER BY a.area DESC
	</select>
	
	<select id="findQuotaTwo" resultType="Allocation">
		SELECT
			a.quota,
			a.urgentFlag
		FROM md_approval_allocation a
		WHERE a.typeId = #{typeId} 
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
		AND a.companyId = #{company.recordId} 
		GROUP BY a.urgentFlag,a.quota
		ORDER BY a.urgentFlag DESC,a.quota DESC
	</select>
	
	<select id="findAreaTwo" resultType="Allocation">
		SELECT
			a.area,
			a.urgentFlag
		FROM md_approval_allocation a
		WHERE a.typeId = #{typeId} 
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
		AND a.companyId = #{company.recordId}
		AND a.area IS NOT NULL 
		GROUP BY a.urgentFlag,a.area
		ORDER BY a.urgentFlag DESC,a.area DESC
	</select>
	
	<select id="getExaminationApprovalCount" resultType="Integer">
		SELECT
			COUNT(1)
		FROM md_approval
		WHERE companyId = #{company.recordId} AND activeFlag = 1
		AND dataId = #{dataId} AND typeId = #{typeId} AND status = 60001
	</select>

	<select id="findProfitRate" resultType="Allocation">
		SELECT
			IFNULL(a.profitRate,0) AS "profitRate",
			IFNULL(a.quota,0) AS "quota",
			IFNULL(a.area,0) AS "area",
			a.profitRateMode,
			a.quotaMode,
			a.areaMode,
			a.typeApproval
		FROM md_approval_allocation a
		WHERE a.typeId = #{typeId}
		AND a.activeFlag = 1
		AND a.companyId = #{company.recordId}
		AND a.profitRate IS NOT NULL
		ORDER BY a.profitRate DESC
	</select>

	<select id="findProfitRateTwo" resultType="Allocation">
		SELECT
		IFNULL(a.profitRate,0) AS "profitRate",
		IFNULL(a.quota,0) AS "quota",
		IFNULL(a.area,0) AS "area",
		a.profitRateMode,
		a.quotaMode,
		a.areaMode,
		a.typeApproval
		FROM md_approval_allocation a
		WHERE a.typeId = #{type.recordId}
		AND a.activeFlag = 1
		AND a.companyId = #{company.recordId}
		<if test="type != null and type != '' and type.recordId != 45 and type.recordId != 46 and type.recordId != 47">
			AND a.profitRate IS NOT NULL
		</if>
		ORDER BY a.profitRate DESC
	</select>

</mapper>