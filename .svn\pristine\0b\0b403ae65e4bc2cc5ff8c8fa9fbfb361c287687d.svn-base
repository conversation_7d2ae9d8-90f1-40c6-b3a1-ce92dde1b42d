package com.kyb.pcberp.modules.sys.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

import java.math.BigDecimal;
import java.util.Date;

public class OperatingRecord extends DataEntity<OperatingRecord>
{
    private static final long serialVersionUID = 1L;

    private String operatorName; // 操作人

    private Date operationTime; // 操作时间

    private String module; //模块

    private String moduleId; // 模块id

    private BigDecimal subTotal; // 金额

    private BigDecimal area; // 面积

    private BigDecimal price; // 单价

    private Integer operatingMode; // 操作方式

    private BigDecimal quantity; // 数量

    private BigDecimal costExpense; // 成本费用

    private BigDecimal costPrice; // 成本价

    private String moduleType; // 模块类型

    private Integer terminal; // 是否为终端 1：是 2：否

    private Integer saveFlag;

    public static long getSerialVersionUID()
    {
        return serialVersionUID;
    }

    public String getOperatorName()
    {
        return operatorName;
    }

    public void setOperatorName(String operatorName)
    {
        this.operatorName = operatorName;
    }

    public Date getOperationTime()
    {
        return operationTime;
    }

    public void setOperationTime(Date operationTime)
    {
        this.operationTime = operationTime;
    }

    public String getModule()
    {
        return module;
    }

    public void setModule(String module)
    {
        this.module = module;
    }

    public String getModuleId()
    {
        return moduleId;
    }

    public void setModuleId(String moduleId)
    {
        this.moduleId = moduleId;
    }

    public BigDecimal getSubTotal()
    {
        return subTotal;
    }

    public void setSubTotal(BigDecimal subTotal)
    {
        this.subTotal = subTotal;
    }

    public BigDecimal getArea()
    {
        return area;
    }

    public void setArea(BigDecimal area)
    {
        this.area = area;
    }

    public BigDecimal getPrice()
    {
        return price;
    }

    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    public Integer getOperatingMode()
    {
        return operatingMode;
    }

    public void setOperatingMode(Integer operatingMode)
    {
        this.operatingMode = operatingMode;
    }

    public BigDecimal getQuantity()
    {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity)
    {
        this.quantity = quantity;
    }

    public BigDecimal getCostExpense()
    {
        return costExpense;
    }

    public void setCostExpense(BigDecimal costExpense)
    {
        this.costExpense = costExpense;
    }

    public BigDecimal getCostPrice()
    {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice)
    {
        this.costPrice = costPrice;
    }

    public String getModuleType()
    {
        return moduleType;
    }

    public void setModuleType(String moduleType)
    {
        this.moduleType = moduleType;
    }

    public Integer getTerminal()
    {
        return terminal;
    }

    public void setTerminal(Integer terminal)
    {
        this.terminal = terminal;
    }

    public Integer getSaveFlag()
    {
        return saveFlag;
    }

    public void setSaveFlag(Integer saveFlag)
    {
        this.saveFlag = saveFlag;
    }
}
