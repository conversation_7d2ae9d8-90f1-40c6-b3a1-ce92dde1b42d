/**
 * 
 */
package com.kyb.pcberp.modules.production.entity;

import java.util.Date;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.modules.eg.entity.EgProcess;
import com.kyb.pcberp.modules.sys.entity.User;

/**
 * 过数统计Entity
 * 
 * <AUTHOR>
 * @version 2015-10-26
 */
public class ProduceStatistics extends DataEntity<ProduceStatistics>
{
    
    private static final long serialVersionUID = 1L;
    
    private EgProcess process;       // 生产工序
    private Date month;     // 过数月份
    private Integer monthCountA;     // 过数月数量
    private Integer monthCountB;     // 过数月数量
    private User operator;      // 过数人(操作员)
    
    public ProduceStatistics()
    {
        super();
    }
    
    public ProduceStatistics(String id)
    {
        super(id);
    }
    
    public EgProcess getProcess()
    {
        return process;
    }
    
    public void setProcess(EgProcess process)
    {
        this.process = process;
    }
    
    public Date getMonth()
    {
        return month;
    }
    
    public void setMonth(Date month)
    {
        this.month = month;
    }
    
    public Integer getMonthCountA()
    {
        return monthCountA;
    }
    
    public void setMonthCountA(Integer monthCountA)
    {
        this.monthCountA = monthCountA;
    }
    
    public Integer getMonthCountB()
    {
        return monthCountB;
    }
    
    public void setMonthCountB(Integer monthCountB)
    {
        this.monthCountB = monthCountB;
    }
    
    public User getOperator()
    {
        return operator;
    }
    
    public void setOperator(User operator)
    {
        this.operator = operator;
    }
    
}