<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="activiti-repos2" />
      <option name="name" value="Activiti Repository 2" />
      <option name="url" value="https://app.camunda.com/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="oschina-repos" />
      <option name="name" value="Oschina Releases" />
      <option name="url" value="http://maven.oschina.net/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="java-repos" />
      <option name="name" value="Java Repository" />
      <option name="url" value="http://download.java.net/maven/2/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="thinkgem-repos2" />
      <option name="name" value="ThinkGem Repository 2" />
      <option name="url" value="https://raw.github.com/thinkgem/repository/master" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="Eclipse Paho Repo" />
      <option name="name" value="Eclipse Paho Repo" />
      <option name="url" value="https://repo.eclipse.org/content/repositories/paho-releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central-repos2" />
      <option name="name" value="Central Repository 2" />
      <option name="url" value="http://repo1.maven.org/maven2/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="activiti-repos" />
      <option name="name" value="Activiti Repository" />
      <option name="url" value="https://maven.alfresco.com/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="snapshots-repo" />
      <option name="name" value="snapshots-repo" />
      <option name="url" value="https://oss.sonatype.org/content/repositories/snapshots" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="springsource-repos" />
      <option name="name" value="SpringSource Repository" />
      <option name="url" value="http://repo.spring.io/release/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="thinkgem-repos" />
      <option name="name" value="ThinkGem Repository" />
      <option name="url" value="http://git.oschina.net/thinkgem/repos/raw/master" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central-repos" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://repo.maven.apache.org/maven2" />
    </remote-repository>
  </component>
</project>