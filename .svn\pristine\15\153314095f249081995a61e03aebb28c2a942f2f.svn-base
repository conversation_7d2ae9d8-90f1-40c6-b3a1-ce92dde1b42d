package com.kyb.pcberp.modules.stock.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.kyb.pcberp.common.persistence.DataEntity;

public class MaterialPriceInit extends DataEntity<MaterialPriceInit>
{
    private static final long serialVersionUID = 1L;
    
    private String companyId;
    
    private BigDecimal purchPrice;
    
    private BigDecimal startPrice;
    
    private BigDecimal price;
    
    private BigDecimal startNum;
    
    private BigDecimal operNum;
    
    private Date initDate;
    
    private String materialId;
    
    private BigDecimal moneyRadio;
    
    private BigDecimal waitUseStocks;
    
    public String getCompanyId()
    {
        return companyId;
    }
    
    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }
    
    public BigDecimal getPurchPrice()
    {
        return purchPrice;
    }
    
    public void setPurchPrice(BigDecimal purchPrice)
    {
        this.purchPrice = purchPrice;
    }
    
    public BigDecimal getStartPrice()
    {
        return startPrice;
    }
    
    public void setStartPrice(BigDecimal startPrice)
    {
        this.startPrice = startPrice;
    }
    
    public BigDecimal getPrice()
    {
        return price;
    }
    
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }
    
    public BigDecimal getStartNum()
    {
        return startNum;
    }
    
    public void setStartNum(BigDecimal startNum)
    {
        this.startNum = startNum;
    }
    
    public BigDecimal getOperNum()
    {
        return operNum;
    }
    
    public void setOperNum(BigDecimal operNum)
    {
        this.operNum = operNum;
    }
    
    public Date getInitDate()
    {
        return initDate;
    }
    
    public void setInitDate(Date initDate)
    {
        this.initDate = initDate;
    }
    
    public String getMaterialId()
    {
        return materialId;
    }
    
    public void setMaterialId(String materialId)
    {
        this.materialId = materialId;
    }
    
    public BigDecimal getMoneyRadio()
    {
        return moneyRadio;
    }
    
    public void setMoneyRadio(BigDecimal moneyRadio)
    {
        this.moneyRadio = moneyRadio;
    }

    public BigDecimal getWaitUseStocks()
    {
        return waitUseStocks;
    }

    public void setWaitUseStocks(BigDecimal waitUseStocks)
    {
        this.waitUseStocks = waitUseStocks;
    }
    
    
}
