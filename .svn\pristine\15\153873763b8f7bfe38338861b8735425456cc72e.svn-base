package com.kyb.pcberp.modules.hr.depart_center.pojo;

import com.kyb.pcberp.common.persistence.DataEntity;

import java.math.BigDecimal;
import java.util.List;

public class ScoreTable extends DataEntity<ScoreTable> {

    private static final long serialVersionUID = 1L;

    private String monthly;

    private String employeeId;

    private String chooseId;

    private String score;

    private String scorerId;

    private Integer type;

    private List<ScoreTable> scoreTableList;

    private BigDecimal personScore;

    private BigDecimal superiorsScore;

    private String name;

    private String positionName;

    private String weight;

    private BigDecimal taskScore;

    private BigDecimal totalScore;

    private BigDecimal performanceScore;

    private String empId;

    private String numNo;

    List<ScoreTable> markScoreList;

    private String structureId;

    private Integer num;

    private String allId;

    private BigDecimal performanceBonus; //绩效奖金

    private String finshText;

    private String adivceText;

    private String finshTextT;

    private String adivceTextT;

    private String empName;

    private BigDecimal manualScore;

    private BigDecimal superScore;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getMonthly() {
        return monthly;
    }

    public void setMonthly(String monthly) {
        this.monthly = monthly;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getChooseId() {
        return chooseId;
    }

    public void setChooseId(String chooseId) {
        this.chooseId = chooseId;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getScorerId() {
        return scorerId;
    }

    public void setScorerId(String scorerId) {
        this.scorerId = scorerId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<ScoreTable> getScoreTableList() {
        return scoreTableList;
    }

    public void setScoreTableList(List<ScoreTable> scoreTableList) {
        this.scoreTableList = scoreTableList;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getPersonScore() {
        return personScore;
    }

    public void setPersonScore(BigDecimal personScore) {
        this.personScore = personScore;
    }

    public BigDecimal getSuperiorsScore() {
        return superiorsScore;
    }

    public void setSuperiorsScore(BigDecimal superiorsScore) {
        this.superiorsScore = superiorsScore;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public BigDecimal getTaskScore() {
        return taskScore;
    }

    public void setTaskScore(BigDecimal taskScore) {
        this.taskScore = taskScore;
    }

    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public BigDecimal getPerformanceScore() {
        return performanceScore;
    }

    public void setPerformanceScore(BigDecimal performanceScore) {
        this.performanceScore = performanceScore;
    }

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public String getNumNo() {
        return numNo;
    }

    public void setNumNo(String numNo) {
        this.numNo = numNo;
    }

    public List<ScoreTable> getMarkScoreList() {
        return markScoreList;
    }

    public void setMarkScoreList(List<ScoreTable> markScoreList) {
        this.markScoreList = markScoreList;
    }

    public String getStructureId() {
        return structureId;
    }

    public void setStructureId(String structureId) {
        this.structureId = structureId;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getAllId() {
        return allId;
    }

    public void setAllId(String allId) {
        this.allId = allId;
    }

    public BigDecimal getPerformanceBonus() {
        return performanceBonus;
    }

    public void setPerformanceBonus(BigDecimal performanceBonus) {
        this.performanceBonus = performanceBonus;
    }

    public String getFinshText() {
        return finshText;
    }

    public void setFinshText(String finshText) {
        this.finshText = finshText;
    }

    public String getAdivceText() {
        return adivceText;
    }

    public void setAdivceText(String adivceText) {
        this.adivceText = adivceText;
    }

    public String getFinshTextT() {
        return finshTextT;
    }

    public void setFinshTextT(String finshTextT) {
        this.finshTextT = finshTextT;
    }

    public String getAdivceTextT() {
        return adivceTextT;
    }

    public void setAdivceTextT(String adivceTextT) {
        this.adivceTextT = adivceTextT;
    }

    public String getEmpName()
    {
        return empName;
    }

    public void setEmpName(String empName)
    {
        this.empName = empName;
    }

    public BigDecimal getManualScore() {
        return manualScore;
    }

    public void setManualScore(BigDecimal manualScore) {
        this.manualScore = manualScore;
    }

    public BigDecimal getSuperScore() {
        return superScore;
    }

    public void setSuperScore(BigDecimal superScore) {
        this.superScore = superScore;
    }
}
