package com.kyb.pcberp.modules.icloud.material.pojo;

import com.kyb.pcberp.common.base.pojo.Icloud_BasePojo;

import java.math.BigDecimal;
import java.util.List;

public class Icloud_MaterialArea extends Icloud_BasePojo
{
    private static final long serialVersionUID = 1L;

    private String versionId;

    private String deptId;

    private String groupCenterId;

    private String specification;

    private BigDecimal useArea;

    private String matPreparationId;

    private List<Icloud_MaterialAreaDetail> detailList;

    public String getVersionId()
    {
        return versionId;
    }

    public void setVersionId(String versionId)
    {
        this.versionId = versionId;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public String getGroupCenterId()
    {
        return groupCenterId;
    }

    public void setGroupCenterId(String groupCenterId)
    {
        this.groupCenterId = groupCenterId;
    }

    public String getSpecification()
    {
        return specification;
    }

    public void setSpecification(String specification)
    {
        this.specification = specification;
    }

    public BigDecimal getUseArea()
    {
        return useArea;
    }

    public void setUseArea(BigDecimal useArea)
    {
        this.useArea = useArea;
    }

    public String getMatPreparationId()
    {
        return matPreparationId;
    }

    public void setMatPreparationId(String matPreparationId)
    {
        this.matPreparationId = matPreparationId;
    }

    public List<Icloud_MaterialAreaDetail> getDetailList()
    {
        return detailList;
    }

    public void setDetailList(List<Icloud_MaterialAreaDetail> detailList)
    {
        this.detailList = detailList;
    }
}
