package com.kyb.pcberp.modules.stock.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(readOnly = true)
public class CostMaterialPriceMonthService
{
    
    // public List<CostMaterialPriceMonth> findList(CostMaterialPriceMonth entity)
    // {
    // return super.dao.findList(entity);
    // }
    //
    // /**
    // * 求出生产编号的单价，根据公司id,物料id，期限，获取上个月期末单价
    // *
    // * @param recordId
    // * @param companyId
    // * @param start
    // * @param end
    // * @return
    // */
    // public BigDecimal getPriceByMaterialId(String recordId, String companyId, String start, String end)
    // {
    //
    // return dao.getPriceByMaterialId(recordId, companyId, start, end);
    // }
    //
    // /**
    // * 最新的物料价格
    // * @param 物料
    // * */
    // public BigDecimal getPriceByMaterial(Material material)
    // {
    // return dao.getPriceByMaterial(material);
    // }
    
}
