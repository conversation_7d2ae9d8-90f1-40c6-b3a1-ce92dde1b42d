package com.kyb.pcberp.modules.oa.dao;

import cn.hutool.core.lang.Dict;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.approval.entity.Approval;
import com.kyb.pcberp.modules.crm.entity.CustomerBillData;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;
import com.kyb.pcberp.modules.hr.depart_center.pojo.Hr_KybAttachments;
import com.kyb.pcberp.modules.hr.finance_center.pojo.Hr_CollectPayBill;
import com.kyb.pcberp.modules.hr.finance_center.pojo.Hr_ManualBill;
import com.kyb.pcberp.modules.inter.entity.InterOrderConfig;
import com.kyb.pcberp.modules.oa.pojo.audit.*;
import com.kyb.pcberp.modules.oa.pojo.common.Oa_comment;
import com.kyb.pcberp.modules.oa.pojo.common.Oa_number;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@MyBatisDao
public interface Oa_auditDao
{
    List<Oa_audit> getAuditList(Oa_audit audit);

    List<Oa_audit> getExcelAuditList(Oa_audit audit);

    List<Oa_audit> getExcelAuditAllList(Oa_audit audit);

    Oa_audit getAudit(Oa_audit audit);

    Oa_audit getShowConcatData(Oa_audit oaaudit);

    void deleteAudit(Oa_audit audit);

    void updateDraftAudit(Oa_audit oaaudit);

    void deleteDraftAuditRecord(Oa_audit oaaudit);

    Oa_audit getCopyforEmpName(Oa_audit oaaudit);

    Oa_audit getPayMsg(Oa_audit oaaudit);

    void deleteAuditGroupDetail(Oa_audit oaaudit);

    void resubmit(Oa_audit audit);

    void commitAudit(Oa_audit audit);

    void delRest(Oa_audit audit);

    void delExpense(Oa_audit audit);

    List<Oa_auditDeail> getExpenses(Oa_audit audit);

    List<Oa_auditDeail> getRests(Oa_audit audit);

    void commitExpense(List<Oa_auditDeail> list);

    void commitRest(List<Oa_auditDeail> list);

    List<Oa_auditRecord> getRejectUser(Oa_audit audit);

    List<Oa_auditRecord> getAuditRecord(Oa_audit audit);

    void updateRecordIdUser(Oa_auditRecord recordId);

    List<Hr_KybAttachments> getAttachmments(Oa_audit audit);

    void uploadFile(Hr_KybAttachments AuFile);

    void delFile(Hr_KybAttachments attach);

    void addAuditComment(Oa_comment comment);

    List<Oa_comment> getAuditComment(Oa_audit audit);

    Integer findNumber(Oa_number number);

    void saveNumber(Oa_number number);

    void commitToApprove(Oa_auditApprove approve);

    Integer canAuditCount(Oa_auditApprove approve);

    String isEndAudit(Oa_auditApprove approve);

    List<String> getNextApprove(Map<String, String> map);

    String getExpenseGroupId();

    String getRestGroupId();

    String getKybAuditEmpId(String recordId);

    List<String> getAuditUserIds(@Param("recordId") String recordId,@Param("turn") Integer turn);

    void updateApplicationStutas(@Param("auditId") String auditId);

    void updateSinglePayableStatus(@Param("auditId") String auditId);

    Integer getAuditFileCount(@Param("auditId") String auditId, @Param("orgFileName") String orgFileName);

    List<Oa_audit> getExcelAuditListTwo(Oa_audit kybAudit);

    List<Oa_auditGroupDeail> getAuditGroupDetailList(Oa_audit kybAudit);

    void delApply(Oa_audit audit);

    String getApplyGroupId();

    void batchInsertSupplyChain(Oa_audit kybAudit);

    void batchInsertAuditGroupDetail(Oa_audit kybAudit);

    List<Hr_Employee> getDepartmentList(Oa_audit kybAudit);

    List<Oa_supplychainApply> getSupplychainApplyList(Oa_supplychainApply supplychainApply);

    void insertCustomBill(Oa_audit oa_audit);

    void updateCustomBill(Oa_audit oa_audit);

    void updateCustomBillStatus(Oa_auditApprove approve);

    BigDecimal insertCustomBalance(Oa_audit oa_audit);

    CustomerBillData updateCustomBalance(Oa_audit oa_audit);

    void updateCustomDate(Oa_audit oa_audit);

    void updateCustomDateT(Oa_auditApprove approve);

    BigDecimal insertCustomBalanceT(CustomerBillData customerBillData);

    CustomerBillData getCustomBillDate(Oa_auditApprove oa_audit);

    void insertCustomBillT(CustomerBillData customerBillData);

    Oa_audit getAuditMessage(@Param("auditId") String auditId);

    Integer getMaxTurn(@Param("auditId") String auditId);

    //工时套餐
    CustomerBillData getMaintanceBalanceDate(Oa_audit oa_audit);

    void divideBalance(Oa_auditApprove oa_auditApprove);

    void updateStatus(Oa_auditApprove approve);

    BigDecimal getUsePackageHour(CustomerBillData customerBillData);

    void updateBuyPackageStatus(CustomerBillData customerBillData);

    void uploadFileTwo(Hr_KybAttachments AuFile);

    List<Hr_KybAttachments> getAttachmmentsPass(Oa_audit audit);

    void delFileTwo(Hr_KybAttachments attach);

    List<Hr_ManualBill> getCertificateList(@Param("certificateIds") String certificateIds);

    List<Hr_ManualBill> getCustomCollectionList(@Param("certificateIds") String certificateIds);

    List<Hr_ManualBill> getSupplierPaymentList(@Param("certificateIds") String certificateIds);

    List<Hr_ManualBill> getCertificateAuditList(Oa_audit oa_audit);

    List<Hr_ManualBill> getCertificateDetailList(@Param("list") List<Hr_ManualBill> list);

    String getCertificateAauditDataList(@Param("auditId") String auditId);

    void updateCertificateStatus(@Param("accountVoucherIds") String accountVoucherIds,@Param("auditStatus") String auditStatus);

    Hr_CollectPayBill getCollectionPaidList(Oa_audit oa_audit);

    List<Hr_CollectPayBill> getCollectionRecordList(Hr_CollectPayBill hr_collectPayBill);

    List<Hr_CollectPayBill> getPaymentRecordList(Hr_CollectPayBill hr_collectPayBill);

    void updateCollectPayStatus(@Param("hrCollectPayId") String hrCollectPayId);

    List<Hr_CollectPayBill> getPaymentRecordMoneyList(Oa_audit oa_audit);

    void updateAuditStatus(Oa_audit oa_audit);

    List<Hr_CollectPayBill> getPaymentRecordMoneyListT(@Param("list") List<Oa_audit> list);

    void updateSingleStatus(Oa_audit oa_audit);

    void updatePayableStatus(Oa_audit oa_audit);

    BigDecimal getSettlementAmountTotal(Oa_audit oa_audit);

    BigDecimal getSettlementPayAmountTotal(Oa_audit oa_audit);

    Hr_CollectPayBill getAuditAllMessageList(Oa_audit oa_audit);

    String getDictValueMessage(DictValue dictValue);

    String getDepartCompanyName(Oa_audit oa_audit);

    Approval getErpAudit(String recordId);

    Oa_audit getAuditData(String recordId);

    String getAuditTypeId(Oa_audit oa_audit);
}
