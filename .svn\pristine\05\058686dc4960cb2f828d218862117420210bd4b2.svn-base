<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isErrorPage="true"%>
<div>
	<div class="row pt-2 pb-2 alert-light border-bottom">
		<div class="col text-center font-weight-bolder">
			<span>用印申请单({{audit.no}})</span>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col text-left font-weight-bold text-primary">
			提交人：{{audit.name}}
		</div>
		<div class="col text-right">
			{{audit.createDateStr}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			来源：{{audit.departmentName}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			用印公司：{{audit.stampCompanyName}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			用印部门：{{audit.stampDeptName}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			申请日期：{{audit.startTime}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			需要印章：{{audit.shortName}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col-6">
			印章：{{audit.childType}}
		</div>
		<div class="col-6">
			印章次数：{{audit.stampCount}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			印章专管员：{{audit.passName}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">印章用途：{{audit.auditResult}}</div>
	</div>
</div>