package com.kyb.pcberp.modules.stock.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.stock.entity.*;
import com.kyb.pcberp.modules.sys.entity.Company;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface InitMaterialDao extends CrudDao<InitMaterialDao>
{
    List<MaterialPlaceCom> getMaterialPlaceList(Company company);

    List<RawmaterialStock> getRawmaterialStockList(Company company);

    List<RawmaterialStock> getCanUseList(Company company);

    List<Material> getMaterialList(Company company);

    List<RawmaterialStock> getRawmaterialPriceList(Company company);

    void batchResetMaterial(@Param("list") List<MaterialStockInit> list);

    void batchUpdatePlaceCom(@Param("list") List<MaterialStockInit> list);

    void batchResetMaterialPrice(@Param("list") List<MaterialPriceInit> list);

    void bathUpdateMaterialPrice(@Param("list") List<MaterialPriceInit> list);

    void bathWaitUseUpdate(@Param("list") List<RawmaterialStock> list);

    void updateMaterialInitStocks(Company company);
}
