kybApp.controller('reconciliationCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'BaseUtil', 'CommonUtil', '$filter', '$http',
    function ($rootScope, $scope, upida, $timeout, BaseUtil, CommonUtil, $filter,$http) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();
        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
    var vm = this;
    vm.right = {};
    //查询范围
    vm.queryAll = false;
    vm.clicks = true;
    vm.reconciliation = {};
    vm.periodQuery = null;
    vm.currCompanyId = "";
    vm.scheduleList = [{
    	'id':1,
    	'name':'对账检测',
    	'img':'glyphicon glyphicon-list-alt'
    },{
    	'id':2,
    	'name':'数量一致性检测',
    	'img':'glyphicon glyphicon-hourglass',
    	'name2':'修复'
    },{
    	'id':3,
    	'name':'金额一致性检测',
    	'img':'glyphicon glyphicon-sort',
		'name2':'下一步'
    },{
    	'id':4,
    	'name':'数据修复',
    	'img':'glyphicon glyphicon-cloud-upload'
    },{
    	'id':5,
    	'name':'对账结束',
    	'img':'glyphicon glyphicon-ok'
    }];
    
    // 分页数据
    vm.page = {};
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    
    // 字典项分页数据
    vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.data.list = {};
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "finance/reconciliation/page";
    vm.page.condition = []; // 条件
    
    // tabs控制
    vm.tabs = {
        viewForm: {active: true},
        editForm: {active: false, show: false},
    };
    
	// 显示编辑
	vm.showEditForm = function() {
		vm.tabs.editForm.show = true;
		vm.tabs.editForm.active = true;
	};
	// 隐藏编辑
	vm.hideEditForm = function() {
		vm.tabs.editForm.show = false;
		vm.tabs.editForm.active = false;
        $timeout(function () {
            vm.tabs.viewForm.active = true;
        });
	};
    
    // 查询数据
    vm.doQuery = function () {
        // 设置查询条件
        var condition = [];
        if (vm.periodQuery && vm.periodQuery != '所有') {
            condition.push({
                name: "period",
                value: vm.periodQuery
            });
        }
        vm.page.pageNo = 1;
        vm.page.condition = condition;
        // 查询数据
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    };
    
    // 分页按钮单击处理
    vm.doPage = function (page, pageSize, total) {
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        vm.init(page, pageSize, vm.page.condition, vm.page.url);
    };
    
    // 页面显示数量改变
    vm.pageSizeChange = function () {
        vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url);
    };
    
    vm.init = function (no, size, condition, url) {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;
        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condition);
        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};
            // 如果结果为空
            if (typeof result === 'undefined' || typeof result.list === 'undefined') {
                data.pageNo = 1;
                data.pageSize = 10;
                data.list = [];
                data.startCount = 0;
                data.endCount = 0;
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }
            vm.page.data = data;
			vm.getReconciliationData();
/*            if(vm.currCompanyId == 205){
            	vm.getReconciliationData();
            }*/
            MainCtrl.unblockUI();
        });
    };

    vm.schedule = null;
    vm.showSchedule = null;
    vm.lnPrecent = null;
    
    vm.saveOpen = function()
    {
    	$('#saveStatic').modal();
    }
    
    vm.saveReconciliation = function()
    {
    	if(!vm.reconciliation.period || vm.reconciliation.period == '')
    	{
         	 vm.message = "月份不能为空";
             $('#static').modal();
             return;
    	}
    	if(!vm.clicks)
    	{
    		return;
    	}
    	vm.clicks = false;
    	MainCtrl.blockUI({
    	    animate: true,
    	});
        upida.post("finance/reconciliation/save", vm.reconciliation).then(function (data) {
        	if(data == "success")
        	{
                vm.message = "添加"+vm.reconciliation.period+"月份对账进度成功";
                $('#static').modal();
                vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
        	}
        	else if(data == "checkFail")
        	{
                vm.message = vm.reconciliation.period+"月份对账进度已经存在，不能重复添加";
                $('#static').modal();
        	}
        	else
        	{
                vm.message = "添加"+vm.reconciliation.period+"月份对账进度失败";
                $('#static').modal();
        	}
        	MainCtrl.unblockUI();
            vm.clicks = true;
        });
    }
    
	vm.deleteOpen = function(row) 
	{
		vm.reconciliation = row;
		vm.message = "您确定要删除对账进度?";
		$('#staticRemove').modal();
	};

    vm.deleteReconciliation = function()
    {
    	if(!vm.clicks)
    	{
    		return;
    	}
    	vm.clicks = false;
    	MainCtrl.blockUI({
    	    animate: true,
    	});
        upida.post("finance/reconciliation/delete", vm.reconciliation).then(function (data) {
        	if(data == "success")
        	{
                vm.message = "删除对账进度成功!";
                $('#static').modal();
                vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
        	}
        	else
        	{
                vm.message = "删除对账进度失败!";
                $('#static').modal();
        	}
        	MainCtrl.unblockUI();
            vm.clicks = true;
        });
    };
    
    vm.scheduleOperation = function(schedule)
    {
    	vm.reconciliation.scheduleModule = schedule;
    	if(schedule != 5){
    		handleData(schedule);
    	}else{
    		MainCtrl.blockUI({
        	    animate: true,
        	});
        	upida.post("finance/reconciliation/getScheduleModuleData",vm.reconciliation).then(function (data) {
        		handleData(schedule,data);
        		MainCtrl.unblockUI();
            });
    	}
    };
    
    vm.repairData = function()
    {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	upida.post("finance/reconciliation/repairData",vm.reconciliation).then(function (data) {
    		 if(data.result == "success")
    		 {
                 vm.message = "修复数据成功!";
                 $('#static').modal();
    		 }
    		 else
    		 {
                 vm.message = "修复数据失败!";
                 $('#static').modal();
    		 }
         });
    };
    
    vm.resetGroupMoney = function() {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	upida.post("common/resetGroupCenterMoney").then(function (data) {
    		 if(data == "success")
    		 {
                 vm.message = "修复数据成功!";
                 $('#static').modal();
    		 }
    		 else
    		 {
                 vm.message = "修复数据失败!";
                 $('#static').modal();
    		 }
    		 MainCtrl.unblockUI();
         });
	};
	
	vm.resetGroupPrice = function() {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	upida.post("common/resetGroupPrice").then(function (data) {
    		 if(data == "success")
    		 {
                 vm.message = "修复数据成功!";
                 $('#static').modal();
    		 }
    		 else
    		 {
                 vm.message = "修复数据失败!";
                 $('#static').modal();
    		 }
    		 MainCtrl.unblockUI();
         });
	};
	
	vm.resetGroupOrder = function() {
		MainCtrl.blockUI({
    	    animate: true,
    	});
    	upida.post("common/resetGroupOrder").then(function (data) {
    		 if(data == "success")
    		 {
                 vm.message = "修复数据成功!";
                 $('#static').modal();
    		 }
    		 else
    		 {
                 vm.message = "修复数据失败!";
                 $('#static').modal();
    		 }
    		 MainCtrl.unblockUI();
         });
	};
	
	vm.resetData = function() {
		MainCtrl.blockUI({
    	    animate: true,
    	});
    	upida.post("common/resetData").then(function (data) {
    		alert(data.data);
/*    		 if(data == "success")
    		 {
                 vm.message = "修复数据成功!";
                 $('#static').modal();
    		 }
    		 else
    		 {
                 vm.message = "修复数据失败!";
                 $('#static').modal();
    		 }*/
    		 MainCtrl.unblockUI();
         });
	};
	vm.kybTestData = function() {
		vm.kybTestExpectionList = [];
		MainCtrl.blockUI({
			animate: true,
		});
		upida.post("common/kybTestData").then(function (data) {
			if(data && data.length > 0)
			{
				vm.kybTestExpectionList = data;
				$('#openTestExpection').modal();
			}
			else
			{
				vm.message = "修复数据成功!";
				$('#static').modal();
			}
			MainCtrl.unblockUI();
		});
	};
    
    vm.nextHandle = function()
    {
    	if(vm.schedule == 4){
    		vm.repairData();
    	}
    	if(!vm.reconciliation.scheduleModule)
    	{
            vm.message = "进度不能为空!";
            $('#static').modal();
            return;
    	}
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	 upida.post("finance/reconciliation/nextHandle",vm.reconciliation).then(function (data) {
    		if(data.result == "success")
    		{
                vm.message = "操作为下一步成功!";
                $('#static').modal();
                vm.reconciliation = data.reconciliation;
    		}
    		else if(data.result == "fail")
    		{
                vm.message = data.message;
                $('#static').modal();
    		}
    		else
    		{
                vm.message = "操作为下一步失败!";
                $('#static').modal();
    		}
    		MainCtrl.unblockUI();
         });
    };
    
    vm.scheduleNumList = [];
    vm.scheduleMoneyList = [];
    vm.dealScheduleData = function() {
    	vm.scheduleNumList = [];
        vm.scheduleMoneyList = [];
    	angular.forEach(vm.showBalanceList, function(p){
    		if(p.xsReceivableQuality != p.xsPayableQuality 
    				|| p.xsPayableQuality != p.lnReceivableQuality 
    				|| p.lnReceivableQuality != p.lnPayableQuality
    				|| p.lnPayableQuality != p.gcReceivableQuality){
    			vm.scheduleNumList.push(p);
    		}
    		if(p.xsPayableAmount != p.lnReceivableAmount || p.lnPayableAmount != p.gcReceivableAmount || p.radioFlag){
    			vm.scheduleMoneyList.push(p);
    		}
		});
	};
    
    function handleData (schedule,data)
    {
    	for(let obj of vm.scheduleList)
    	{
    		if(obj.id == schedule)
    		{
    			vm.showSchedule = obj.name;
    			break;
    		}
    	}
    	vm.schedule = schedule;
    	switch (schedule) {
			case 5:
				vm.reconciliation = data.reconciliation;
				vm.message = "对账结束成功!";
				$('#static').modal();
				break;
			default:
				$('#nextStatic').modal();
				break;
		}
    };
    
    vm.fomatFloat = function(num)
    {
    	var n = 2
    	var f = parseFloat(num);
	    if(!num || isNaN(f)){
	        return null;
	    }   
	    f = Math.round(num*Math.pow(10, n))/Math.pow(10, n); // n 幂   
	    var s = f.toString();
	    return vm.toQfw(s);
    };
    
    vm.fomatFloatQfw = function(num)
    {
    	if(null==num||( undefined==num)||(""==num))
    	{
    		return vm.toQfw("-");
    	}
    	else
    	{
    		if(num<0)
    		{
    			var s = (-1*num).toString()+"";
    			var sArr = s.split(".");
    			var pointStr = ".00";//补2个零
    			if(sArr.length>1)
    			{
    				if(sArr[1].length>2)
    				{
    					pointStr = "."+sArr[1].substring(0,2);//截断两位
    				}
    				else if(sArr[1].length==1)
    				{
    					pointStr = "."+sArr[1]+"0";//补一个零
    				}
    				else
    				{
    					pointStr = "."+sArr[1];//不补
    				}
    			}
    			return "-"+vm.toQfw(sArr[0])+pointStr;
    		}
    		else
    		{
    			var s = num.toString()+"";
    			var sArr = s.split(".");
    			var pointStr = ".00";//补2个零
    			if(sArr.length>1)
    			{
    				if(sArr[1].length>2)
    				{
    					pointStr = "."+sArr[1].substring(0,2);//截断两位
    				}
    				else if(sArr[1].length==1)
    				{
    					pointStr = "."+sArr[1]+"0";//补一个零
    				}
    				else
    				{
    					pointStr = "."+sArr[1];//不补
    				}
    			}
    			return vm.toQfw(sArr[0])+pointStr;
    		}
    	}
    };
	
	vm.toQfw = function(str){
		var re=/(?!^)(?=(\d{3})+(?:$|\.))/g;
		str=str.toString().replace(re,",");
		return str;
	};
	
	vm.currCompanyId = "";
    vm.companyFlag = null; // 公司标志：1工厂，2总部经济，3销售
    vm.ecoemyCom = {}; // 总部经济公司
    vm.factCom = {}; // 工厂公司
    vm.saleCom = {}; // 销售公司
    vm.saleComList = []; // 销售公司集合
    vm.groupCenterTwoDataList = []; // 显示数据
    vm.defaultWay = "1";
    vm.totalWay = null;
    
    vm.title = "";
    vm.showReconciliation = function(row)
    {
    	vm.reconciliation = angular.copy(row);
    	if(vm.reconciliation.status == "1001")
    	{
    		vm.title = "处理";
    	}
    	else
    	{
    		vm.title = "查看";
    	}
    	vm.periodQuery = row.period;
    	vm.getReceivableByPeriod();
		vm.getReconciliationData();
    	vm.showEditForm();
    };
    
    vm.receivableList = [];
    vm.getReceivableByPeriod = function() {
    	vm.receivableList = [];
		var groupData = {};
		groupData.period = vm.periodQuery;
		MainCtrl.blockUI({
	       animate: true,
	    });
		upida.post("finance/reconciliation/getReceivableByPeriod", groupData).then(function(data) 
		{
			vm.receivableList = data;
			vm.dealReceivableData();
			MainCtrl.unblockUI();
		});
	};
	
	vm.receivableData = {};
	vm.receivableData.jxAduitList = [];
	vm.receivableData.ldhAduitList = [];
	vm.receivableData.syAduitList = [];
	
	vm.receivableData.jxUnCheckList = [];
	vm.receivableData.ldhUnCheckList = [];
	vm.receivableData.syUnCheckList = [];
	
	vm.receivableData.jxCheckList = [];
	vm.receivableData.ldhCheckList = [];
	vm.receivableData.syCheckList = [];
	
	vm.receivableData.jxUnConfimList = [];
	vm.receivableData.ldhUnConfimList = [];
	vm.receivableData.syUnConfimList = [];
	
	vm.receivableData.jxConfimList = [];
	vm.receivableData.ldhConfimList = [];
	vm.receivableData.syConfimList = [];
	
	vm.receivableData.jxFinishList = [];
	vm.receivableData.ldhFinishList = [];
	vm.receivableData.syFinishList = [];
	
	vm.nextFlag = 0;
	
	vm.dealReceivableData = function() {
		vm.receivableData = {};
		vm.receivableData.jxUnConfimList = [];
		vm.receivableData.ldhUnConfimList = [];
		vm.receivableData.syUnConfimList = [];
		vm.receivableData.jxConfimList = [];
		vm.receivableData.ldhConfimList = [];
		vm.receivableData.syConfimList = [];
		vm.receivableData.jxUnCheckList = [];
		vm.receivableData.ldhUnCheckList = [];
		vm.receivableData.syUnCheckList = [];
		vm.receivableData.jxCheckList = [];
		vm.receivableData.ldhCheckList = [];
		vm.receivableData.syCheckList = [];
		vm.receivableData.jxFinishList = [];
		vm.receivableData.ldhFinishList = [];
		vm.receivableData.syFinishList = [];
		vm.receivableData.jxAduitList = [];
		vm.receivableData.ldhAduitList = [];
		vm.receivableData.syAduitList = [];
		for(let i=0;i<vm.receivableList.length;i++){
			if(vm.receivableList[i].status == 2001){
				vm.nextFlag = 1;
				// 未确认
				if(vm.receivableList[i].companyId == 15){
					vm.receivableData.syUnConfimList.push(vm.receivableList[i]);
				}else if(vm.receivableList[i].companyId == 3){
					vm.receivableData.ldhUnConfimList.push(vm.receivableList[i]);
				}else if(vm.receivableList[i].companyId == 17){
					vm.receivableData.jxUnConfimList.push(vm.receivableList[i]);
				}
			}else if(vm.receivableList[i].status == 2002){
				vm.nextFlag = 1;
				// 已确认
				if(vm.receivableList[i].companyId == 15){
					vm.receivableData.syConfimList.push(vm.receivableList[i]);
				}else if(vm.receivableList[i].companyId == 3){
					vm.receivableData.ldhConfimList.push(vm.receivableList[i]);
				}else if(vm.receivableList[i].companyId == 17){
					vm.receivableData.jxConfimList.push(vm.receivableList[i]);
				}
			}else if(vm.receivableList[i].status == 2004){
				vm.nextFlag = 1;
				// 未复核
				if(vm.receivableList[i].companyId == 15){
					vm.receivableData.syUnCheckList.push(vm.receivableList[i]);
				}else if(vm.receivableList[i].companyId == 3){
					vm.receivableData.ldhUnCheckList.push(vm.receivableList[i]);
				}else if(vm.receivableList[i].companyId == 17){
					vm.receivableData.jxUnCheckList.push(vm.receivableList[i]);
				}
			}else if(vm.receivableList[i].status == 2005){
				vm.nextFlag = 1;
				// 已复核
				if(vm.receivableList[i].companyId == 15){
					vm.receivableData.syCheckList.push(vm.receivableList[i]);
				}else if(vm.receivableList[i].companyId == 3){
					vm.receivableData.ldhCheckList.push(vm.receivableList[i]);
				}else if(vm.receivableList[i].companyId == 17){
					vm.receivableData.jxCheckList.push(vm.receivableList[i]);
				}
			}else if(vm.receivableList[i].status == 2003){
				// 完成的账单
				if(vm.receivableList[i].companyId == 15){
					vm.receivableData.syFinishList.push(vm.receivableList[i]);
				}else if(vm.receivableList[i].companyId == 3){
					vm.receivableData.ldhFinishList.push(vm.receivableList[i]);
				}else if(vm.receivableList[i].companyId == 17){
					vm.receivableData.jxFinishList.push(vm.receivableList[i]);
				}
			}else if(vm.receivableList[i].status == 60001){
				vm.nextFlag = 1;
				// 审批中的账单
				if(vm.receivableList[i].companyId == 15){
					vm.receivableData.syAduitList.push(vm.receivableList[i]);
				}else if(vm.receivableList[i].companyId == 3){
					vm.receivableData.ldhAduitList.push(vm.receivableList[i]);
				}else if(vm.receivableList[i].companyId == 17){
					vm.receivableData.jxAduitList.push(vm.receivableList[i]);
				}
			}
		}
	};
    
    vm.getReconciliationData = function() {
    	clienParamter(); // 清理参数
		var groupData = {};
		groupData.period = vm.periodQuery;
		groupData.defaultWay = vm.defaultWay;
		MainCtrl.blockUI({
	       animate: true,
	    });
		upida.post("finance/reconciliation/getReconciliationData", groupData).then(function(data) 
		{
			vm.currCompanyId = data.companyId; // 当前公司id
			vm.ecoemyCom = data.ecoemyCom; // 总部经济公司
			vm.factCom = data.factCom; // 工厂公司
			vm.saleComList = data.saleComList; // 销售公司集合
			if(vm.saleComList && vm.saleComList.length > 0)
			{
				vm.saleCom = vm.saleComList[0];
				vm.saleComId = vm.saleComList[0].recordId;
			}
			else
			{
				vm.saleCom = vm.nowCompany;
				vm.saleComId = vm.nowCompany.recordId;
			}
			vm.groupCenterTwoDataList = data.groupCenterTwoDataList;
			computTotal(vm.groupCenterTwoDataList);
			if(vm.reconciliation.status == "1001")
	    	{
				// 处理对账数据
	        	vm.dealScheduleData();
	    	}
			MainCtrl.unblockUI();
		});
	};
    
	function clienParamter()
    {
        //vm.currCompanyId = "";
        vm.companyFlag = null; // 公司标志：1工厂，2总部经济，3销售
        vm.ecoemyCom = {}; // 总部经济公司
        vm.factCom = {}; // 工厂公司
        vm.saleCom = {};
        vm.saleComList = []; // 销售公司集合
        vm.groupCenterTwoDataList = []; // 显示数据
        vm.showBalanceList = [];
    };
    
    vm.sumTotal = {};
	vm.showBalanceList = [];
	vm.confirmFlag = true;
	function computTotal(itemList)
	{
		vm.showBalanceList = [];
		var list = [];
		vm.confirmFlag = true;
		if(!itemList || itemList.length == 0)
		{
			return;
		}
		if(vm.issue == 1){
			for(let i=0;i<itemList.length;i++){
				if(itemList[i].differenceFalg == "金额差异"){
					list.push(itemList[i]);
					if(vm.confirmFlag)
					{
						vm.confirmFlag = false;
					}
				}
			}
		}else if(vm.issue == 2){
			for(let i=0;i<itemList.length;i++){
				if(itemList[i].differenceFalg == "无差异"){
					list.push(itemList[i]);
				}
			}
		}else{
			list = itemList;
		}
		vm.showBalanceList = list;
		// 筛选数据
		vm.sumTotal = {};
		if(!list || list.length == 0)
		{
			return;
		}
		vm.sumTotal.sumXsReceivableQuality = 0;
		vm.sumTotal.sumXsPayableQuality = 0;
		vm.sumTotal.sumLnReceivableQuality = 0;
		vm.sumTotal.sumLnPayableQuality = 0;
		vm.sumTotal.sumGcReceivableQuality = 0;
		vm.sumTotal.sumGcPayableQuality = 0;
		
		vm.sumTotal.sumXsReceivableArea = 0;
		vm.sumTotal.sumXsPayableArea = 0;
		vm.sumTotal.sumLnReceivableArea = 0;
		vm.sumTotal.sumLnPayableArea = 0;
		vm.sumTotal.sumGcReceivableArea = 0;
		vm.sumTotal.sumGcPayableArea = 0;
		
		vm.sumTotal.sumOrderNum = 0;
		vm.sumTotal.sumOrderMoney = 0;
		vm.sumTotal.sumOrderArea = 0;
		// 销售收款金额汇总
		vm.sumTotal.sumXsReceivableAmount = 0;
		
		// 销售付款金额汇总
		vm.sumTotal.sumXsPayableAmount = 0;
		
		// 总部经济收款金额汇总
		vm.sumTotal.sumLnReceivableAmount = 0;
		
		// 总部经济付款金额汇总
		vm.sumTotal.sumLnPayableAmount = 0;
		
		// 工厂收款金额汇总
		vm.sumTotal.sumGcReceivableAmount = 0;
		
		// 送货数量、面积
		vm.sumTotal.sumJxSentQty = 0;
		vm.sumTotal.sumJxSentArea = 0;
		
		// 生产入库数量、面积
		vm.sumTotal.sumWarehousingQty = 0;
		vm.sumTotal.sumWarehousingArea = 0;
		
		angular.forEach(list,function(p){
			if(p.xsReceivableAmount)
			{
				vm.sumTotal.sumXsReceivableAmount += Number(p.xsReceivableAmount);
			}
			if(p.xsPayableAmount)
			{
				vm.sumTotal.sumXsPayableAmount += Number(p.xsPayableAmount);
			}
			if(p.lnReceivableAmount)
			{
				vm.sumTotal.sumLnReceivableAmount += Number(p.lnReceivableAmount);
			}
			if(p.lnPayableAmount)
			{
				vm.sumTotal.sumLnPayableAmount += Number(p.lnPayableAmount);
			}
			if(p.gcReceivableAmount)
			{
				vm.sumTotal.sumGcReceivableAmount += Number(p.gcReceivableAmount);
			}
			if(p.gcPayableAmount)
			{
				vm.sumTotal.sumGcPayableAmount += Number(p.gcPayableAmount);
			}
			
			if(p.xsReceivableQuality)
			{
				vm.sumTotal.sumXsReceivableQuality += Number(p.xsReceivableQuality);
			}
			if(p.xsPayableQuality)
			{
				vm.sumTotal.sumXsPayableQuality += Number(p.xsPayableQuality);
			}
			if(p.lnReceivableQuality)
			{
				vm.sumTotal.sumLnReceivableQuality += Number(p.lnReceivableQuality);
			}
			if(p.lnPayableQuality)
			{
				vm.sumTotal.sumLnPayableQuality += Number(p.lnPayableQuality);
			}
			if(p.gcReceivableQuality)
			{
				vm.sumTotal.sumGcReceivableQuality += Number(p.gcReceivableQuality);
			}
			if(p.gcPayableQuality)
			{
				vm.sumTotal.sumGcPayableQuality += Number(p.gcPayableQuality);
			}
			
			if(p.xsReceivableArea)
			{
				vm.sumTotal.sumXsReceivableArea += Number(p.xsReceivableArea);
			}
			if(p.xsPayableArea)
			{
				vm.sumTotal.sumXsPayableArea += Number(p.xsPayableArea);
			}
			if(p.lnReceivableArea)
			{
				vm.sumTotal.sumLnReceivableArea += Number(p.lnReceivableArea);
			}
			if(p.lnPayableArea)
			{
				vm.sumTotal.sumLnPayableArea += Number(p.lnPayableArea);
			}
			if(p.gcReceivableArea)
			{
				vm.sumTotal.sumGcReceivableArea += Number(p.gcReceivableArea);
			}
			if(p.gcPayableArea)
			{
				vm.sumTotal.sumGcPayableArea += Number(p.gcPayableArea);
			}
			
			if(p.orderMoney)
			{
				vm.sumTotal.sumOrderMoney += Number(p.orderMoney);
			}
			if(p.orderArea)
			{
				vm.sumTotal.sumOrderArea += Number(p.orderArea);
			}
			if(p.orderNum)
			{
				vm.sumTotal.sumOrderNum += Number(p.orderNum);
			}
			
			// 送货数量/面积累计
			if(p.jxSentQty)
			{
				vm.sumTotal.sumJxSentQty += Number(p.jxSentQty);
			}
			if(p.jxSentArea)
			{
				vm.sumTotal.sumJxSentArea += Number(p.jxSentArea);
			}
			
			// 入库数量/面积累计
			if(p.warehousingQty)
			{
				vm.sumTotal.sumWarehousingQty += Number(p.warehousingQty);
			}
			if(p.warehousingArea)
			{
				vm.sumTotal.sumWarehousingArea += Number(p.warehousingArea);
			}
		});
		// 销售收款金额汇总
		if(vm.sumTotal.sumXsReceivableAmount > 0)
		{
			vm.sumTotal.sumXsReceivableAmount = bigNumberTransform(vm.sumTotal.sumXsReceivableAmount);
		}
		else if(vm.sumTotal.sumXsReceivableAmount < 0)
		{
			vm.sumTotal.sumXsReceivableAmount = "-" + bigNumberTransform(vm.sumTotal.sumXsReceivableAmount * -1);
		}
		
		// 销售付款金额汇总
		if(vm.sumTotal.sumXsPayableAmount > 0)
		{
			vm.sumTotal.sumXsPayableAmount = bigNumberTransform(vm.sumTotal.sumXsPayableAmount);
		}
		else if(vm.sumTotal.sumXsPayableAmount < 0)
		{
			vm.sumTotal.sumXsPayableAmount = "-" + bigNumberTransform(vm.sumTotal.sumXsPayableAmount * -1);
		}
		
		// 总部经济收款金额汇总
		if(vm.sumTotal.sumLnReceivableAmount > 0)
		{
			vm.sumTotal.sumLnReceivableAmount = bigNumberTransform(vm.sumTotal.sumLnReceivableAmount);
		}
		else if(vm.sumTotal.sumLnReceivableAmount < 0)
		{
			vm.sumTotal.sumLnReceivableAmount = "-" + bigNumberTransform(vm.sumTotal.sumLnReceivableAmount * -1);
		}
		
		// 总部经济付款金额汇总
		if(vm.sumTotal.sumLnPayableAmount > 0)
		{
			vm.sumTotal.sumLnPayableAmount = bigNumberTransform(vm.sumTotal.sumLnPayableAmount);
		}
		else if(vm.sumTotal.sumLnPayableAmount < 0)
		{
			vm.sumTotal.sumLnPayableAmount = "-" + bigNumberTransform(vm.sumTotal.sumLnPayableAmount * -1);
		}
		
		// 工厂收款金额汇总
		if(vm.sumTotal.sumGcReceivableAmount > 0)
		{
			vm.sumTotal.sumGcReceivableAmount = bigNumberTransform(vm.sumTotal.sumGcReceivableAmount);
		}
		else if(vm.sumTotal.sumGcReceivableAmount < 0)
		{
			vm.sumTotal.sumGcReceivableAmount = "-" + bigNumberTransform(vm.sumTotal.sumGcReceivableAmount * -1);
		}
		
		if(vm.sumTotal.sumOrderMoney > 0)
		{
			vm.sumTotal.sumOrderMoney = bigNumberTransform(vm.sumTotal.sumOrderMoney);
		}
		else if(vm.sumTotal.sumOrderMoney < 0)
		{
			vm.sumTotal.sumOrderMoney = "-" + bigNumberTransform(vm.sumTotal.sumOrderMoney * -1);
		}
	};
	
    function bigNumberTransform (value) {
  	  value = Number(value);
  	  const newValue = ['', '', '']
  	  let fr = 1000
  	  let num = 3
  	  let text1 = ''
  	  let fm = 1
  	  while (value / fr >= 1) {
  	    fr *= 10
  	    num += 1
  	  }
  	  if (num <= 4) { 
  	    newValue[0] = value.toFixed(2);
  	  } else if (num <= 8) { // 万
  	    text1 = parseInt(num - 4) / 3 > 1 ? '千万' : '万'
  	    fm = text1 === '万' ? 10000 : 10000000
  	    if (value % fm === 0) {
  	      newValue[0] = parseInt(value / fm) + ''
  	    } else {
  	      newValue[0] = parseFloat(value / fm).toFixed(2) + ''
  	    }
  	    newValue[1] = text1
  	  } else if (num <= 16) { // 亿
  	    text1 = (num - 8) / 3 > 1 ? '千亿' : '亿'
  	    text1 = (num - 8) / 4 > 1 ? '万亿' : text1
  	    text1 = (num - 8) / 7 > 1 ? '千万亿' : text1
  	    fm = 1
  	    if (text1 === '亿') {
  	      fm = 100000000
  	    } else if (text1 === '千亿') {
  	      fm = 100000000000
  	    } else if (text1 === '万亿') {
  	      fm = 1000000000000
  	    } else if (text1 === '千万亿') {
  	      fm = 1000000000000000
  	    }
  	    if (value % fm === 0) {
  	      newValue[0] = parseInt(value / fm) + ''
  	    } else {
  	      newValue[0] = parseFloat(value / fm).toFixed(2) + ''
  	    }
  	    newValue[1] = text1
  	  }
  	  if (value < 1000) {
  	    newValue[0] = value + ''
  	    newValue[1] = ''
  	  }
  	  return newValue.join('')
  	};
  	
  	vm.saleComId = "";
  	vm.selectSaleComId = function() {
  		var groupData = {};
		groupData.period = vm.reconciliation.period;
		groupData.companyId = vm.saleComId;
		if(vm.saleComList && vm.saleComList.length > 0)
		{
			for(let i=0;i<vm.saleComList.length;i++){
				if(vm.saleComList[i].recordId == vm.saleComId){
					vm.saleCom = vm.saleComList[i];
					break;
				}
			}
		}
		vm.getScreenList(groupData);
	};
  	
    vm.selectSaleCom = function()
    {
		var groupData = {};
/*		if(vm.currCompanyId == 205){
  			groupData.period = vm.periodQuery;
  			vm.reconciliation.period = vm.periodQuery;
  		}else{
  			groupData.period = vm.reconciliation.period;
  		}*/
		groupData.period = vm.periodQuery;
		vm.reconciliation.period = vm.periodQuery;
		groupData.companyId = vm.saleCom.recordId;
		vm.saleComId = vm.saleCom.recordId;
		groupData.defaultWay = vm.defaultWay;
		groupData.queryType = vm.queryType;
/*		initMaterialCheckData(vm.reconciliation.period);*/
		vm.getScreenList(groupData);
    };
    
    vm.getScreenList = function(groupData) {
    	MainCtrl.blockUI({
 	       animate: true,
 	    });
 		upida.post("finance/reconciliation/screenReconciliationData", groupData).then(function(data) 
 		{
 			vm.groupCenterTwoDataList = data;
 			computTotal(vm.groupCenterTwoDataList);
 			vm.dealVeidooDataList();
 			if(vm.reconciliation.status == "1001")
	    	{
				// 处理对账数据
	        	vm.dealScheduleData();
	    	}
 			MainCtrl.unblockUI();
 		});
	};
	
	vm.reconPro = "1";
	vm.custSumTotal = {};
	vm.supSumTotal = {};
	vm.issue = "1";
    vm.veidoo = "1";
    vm.veidooList = [];
    vm.dealVeidooDataList = function() {
    	vm.custSumTotal = {};
    	vm.custSumTotal.sumXsReceivableAmount = 0;
		vm.custSumTotal.sumXsPayableAmount = 0;
		vm.custSumTotal.sumLnReceivableAmount = 0;
		vm.custSumTotal.sumLnPayableAmount = 0;
		vm.custSumTotal.sumGcReceivableAmount = 0;
		vm.custSumTotal.sumGcPayableAmount = 0;
		vm.custSumTotal.sumXsReceivableQuality = 0;
		vm.custSumTotal.sumXsPayableQuality = 0;
		vm.custSumTotal.sumLnReceivableQuality = 0;
		vm.custSumTotal.sumLnPayableQuality = 0;
		vm.custSumTotal.sumGcReceivableQuality = 0;
		vm.custSumTotal.sumGcPayableQuality = 0;
		
		vm.custSumTotal.sumXsReceivableArea = 0;
		vm.custSumTotal.sumXsPayableArea = 0;
		vm.custSumTotal.sumLnReceivableArea = 0;
		vm.custSumTotal.sumLnPayableArea = 0;
		vm.custSumTotal.sumGcReceivableArea = 0;
		vm.custSumTotal.sumGcPayableArea = 0;
		vm.supSumTotal = {};
		vm.supSumTotal.sumXsReceivableAmount = 0;
		vm.supSumTotal.sumXsPayableAmount = 0;
		vm.supSumTotal.sumLnReceivableAmount = 0;
		vm.supSumTotal.sumLnPayableAmount = 0;
		vm.supSumTotal.sumGcReceivableAmount = 0;
		vm.supSumTotal.sumGcPayableAmount = 0;
		vm.supSumTotal.sumXsReceivableQuality = 0;
		vm.supSumTotal.sumXsPayableQuality = 0;
		vm.supSumTotal.sumLnReceivableQuality = 0;
		vm.supSumTotal.sumLnPayableQuality = 0;
		vm.supSumTotal.sumGcReceivableQuality = 0;
		vm.supSumTotal.sumGcPayableQuality = 0;
		
		vm.supSumTotal.sumXsReceivableArea = 0;
		vm.supSumTotal.sumXsPayableArea = 0;
		vm.supSumTotal.sumLnReceivableArea = 0;
		vm.supSumTotal.sumLnPayableArea = 0;
		vm.supSumTotal.sumGcReceivableArea = 0;
		vm.supSumTotal.sumGcPayableArea = 0;
    	vm.veidooList = [];
    	if(vm.veidoo == "2"){
    		if(vm.showBalanceList && vm.showBalanceList.length > 0){
            	for(let i=0;i<vm.showBalanceList.length;i++){
            		if(!vm.showBalanceList[i].finalCustId){
            			continue;
            		}
            		var createFlag = true;
            		for(let j=0;j<vm.veidooList.length;j++){
            			if(vm.veidooList[j].finalCustId == vm.showBalanceList[i].finalCustId){
            				vm.veidooList[j].xsReceivableAmount = vm.veidooList[j].xsReceivableAmount == null?0:Number(vm.veidooList[j].xsReceivableAmount);
            				vm.veidooList[j].xsInnerPayableAmount = vm.veidooList[j].xsInnerPayableAmount == null?0:Number(vm.veidooList[j].xsInnerPayableAmount);
            				vm.veidooList[j].xsExterReceivableAmount = vm.veidooList[j].xsExterReceivableAmount == null?0:Number(vm.veidooList[j].xsExterReceivableAmount);
            				
            				vm.veidooList[j].xsPayableAmount = vm.veidooList[j].xsPayableAmount == null?0:Number(vm.veidooList[j].xsPayableAmount);
            				vm.veidooList[j].xsInnerPayableAmount = vm.veidooList[j].xsInnerPayableAmount == null?0:Number(vm.veidooList[j].xsInnerPayableAmount);
            				vm.veidooList[j].xsExterPayableAmount = vm.veidooList[j].xsExterPayableAmount == null?0:Number(vm.veidooList[j].xsExterPayableAmount);
            				
            				vm.veidooList[j].lnReceivableAmount = vm.veidooList[j].lnReceivableAmount == null?0:Number(vm.veidooList[j].lnReceivableAmount);
            				vm.veidooList[j].lnInnerReceivableAmount = vm.veidooList[j].lnInnerReceivableAmount == null?0:Number(vm.veidooList[j].lnInnerReceivableAmount);
            				vm.veidooList[j].lnExterReceivableAmount = vm.veidooList[j].lnExterReceivableAmount == null?0:Number(vm.veidooList[j].lnExterReceivableAmount);
            				
            				vm.veidooList[j].lnPayableAmount = vm.veidooList[j].lnPayableAmount == null?0:Number(vm.veidooList[j].lnPayableAmount);
            				vm.veidooList[j].lnInnerPayableAmount = vm.veidooList[j].lnInnerPayableAmount == null?0:Number(vm.veidooList[j].lnInnerPayableAmount);
            				vm.veidooList[j].lnExterPayableAmount = vm.veidooList[j].lnExterPayableAmount == null?0:Number(vm.veidooList[j].lnExterPayableAmount);
            				
            				vm.veidooList[j].gcReceivableAmount = vm.veidooList[j].gcReceivableAmount == null?0:Number(vm.veidooList[j].gcReceivableAmount);
            				vm.veidooList[j].gcPayableAmount = vm.veidooList[j].gcPayableAmount == null?0:Number(vm.veidooList[j].gcPayableAmount);
            				
            				vm.veidooList[j].xsReceivableQuality = vm.veidooList[j].xsReceivableQuality == null?0:Number(vm.veidooList[j].xsReceivableQuality);
            				vm.veidooList[j].xsPayableQuality = vm.veidooList[j].xsPayableQuality == null?0:Number(vm.veidooList[j].xsPayableQuality);
            				vm.veidooList[j].lnReceivableQuality = vm.veidooList[j].lnReceivableQuality == null?0:Number(vm.veidooList[j].lnReceivableQuality);
            				vm.veidooList[j].lnPayableQuality = vm.veidooList[j].lnPayableQuality == null?0:Number(vm.veidooList[j].lnPayableQuality);
            				vm.veidooList[j].gcReceivableQuality = vm.veidooList[j].gcReceivableQuality == null?0:Number(vm.veidooList[j].gcReceivableQuality);
            				vm.veidooList[j].gcPayableQuality = vm.veidooList[j].gcPayableQuality == null?0:Number(vm.veidooList[j].gcPayableQuality);
            				vm.veidooList[j].xsReceivableQuality += 
            					(vm.showBalanceList[i].xsReceivableQuality==null?0:Number(vm.showBalanceList[i].xsReceivableQuality));
            				vm.veidooList[j].xsPayableQuality += 
            					(vm.showBalanceList[i].xsPayableQuality==null?0:Number(vm.showBalanceList[i].xsPayableQuality));
            				vm.veidooList[j].lnReceivableQuality += 
            					(vm.showBalanceList[i].lnReceivableQuality==null?0:Number(vm.showBalanceList[i].lnReceivableQuality));
            				vm.veidooList[j].lnPayableQuality += 
            					(vm.showBalanceList[i].lnPayableQuality==null?0:Number(vm.showBalanceList[i].lnPayableQuality));
            				vm.veidooList[j].gcReceivableQuality += 
            					(vm.showBalanceList[i].gcReceivableQuality==null?0:Number(vm.showBalanceList[i].gcReceivableQuality));
            				vm.veidooList[j].gcPayableQuality += 
            					(vm.showBalanceList[i].gcPayableQuality==null?0:Number(vm.showBalanceList[i].gcPayableQuality));
            				
            				vm.veidooList[j].xsReceivableArea = vm.veidooList[j].xsReceivableArea == null?0:Number(vm.veidooList[j].xsReceivableArea);
            				vm.veidooList[j].xsPayableArea = vm.veidooList[j].xsPayableArea == null?0:Number(vm.veidooList[j].xsPayableArea);
            				vm.veidooList[j].lnReceivableArea = vm.veidooList[j].lnReceivableArea == null?0:Number(vm.veidooList[j].lnReceivableArea);
            				vm.veidooList[j].lnPayableArea = vm.veidooList[j].lnPayableArea == null?0:Number(vm.veidooList[j].lnPayableArea);
            				vm.veidooList[j].gcReceivableArea = vm.veidooList[j].gcReceivableArea == null?0:Number(vm.veidooList[j].gcReceivableArea);
            				vm.veidooList[j].gcPayableArea = vm.veidooList[j].gcPayableArea == null?0:Number(vm.veidooList[j].gcPayableArea);
            				vm.veidooList[j].xsReceivableArea += 
            					(vm.showBalanceList[i].xsReceivableArea==null?0:Number(vm.showBalanceList[i].xsReceivableArea));
            				vm.veidooList[j].xsPayableArea += 
            					(vm.showBalanceList[i].xsPayableArea==null?0:Number(vm.showBalanceList[i].xsPayableArea));
            				vm.veidooList[j].lnReceivableArea += 
            					(vm.showBalanceList[i].lnReceivableArea==null?0:Number(vm.showBalanceList[i].lnReceivableArea));
            				vm.veidooList[j].lnPayableArea += 
            					(vm.showBalanceList[i].lnPayableArea==null?0:Number(vm.showBalanceList[i].lnPayableArea));
            				vm.veidooList[j].gcReceivableArea += 
            					(vm.showBalanceList[i].gcReceivableArea==null?0:Number(vm.showBalanceList[i].gcReceivableArea));
            				vm.veidooList[j].gcPayableArea += 
            					(vm.showBalanceList[i].gcPayableArea==null?0:Number(vm.showBalanceList[i].gcPayableArea));
            				
            				vm.veidooList[j].xsReceivableAmount += 
            					(vm.showBalanceList[i].xsReceivableAmount==null?0:Number(vm.showBalanceList[i].xsReceivableAmount));
            				if(vm.showBalanceList[i].status == "1"){
            					vm.veidooList[j].xsInnerReceivableAmount += 
                					(vm.showBalanceList[i].xsReceivableAmount==null?0:Number(vm.showBalanceList[i].xsReceivableAmount));
            				}
            				if(vm.showBalanceList[i].status == "2"){
            					vm.veidooList[j].xsExterReceivableAmount += 
                					(vm.showBalanceList[i].xsReceivableAmount==null?0:Number(vm.showBalanceList[i].xsReceivableAmount));
            				}
            				
            				vm.veidooList[j].xsPayableAmount += 
            					(vm.showBalanceList[i].xsPayableAmount==null?0:Number(vm.showBalanceList[i].xsPayableAmount));
            				if(vm.showBalanceList[i].status == "1"){
            					vm.veidooList[j].xsInnerPayableAmount += 
                					(vm.showBalanceList[i].xsPayableAmount==null?0:Number(vm.showBalanceList[i].xsPayableAmount));
            				}
            				if(vm.showBalanceList[i].status == "2"){
            					vm.veidooList[j].xsExterPayableAmount += 
                					(vm.showBalanceList[i].xsPayableAmount==null?0:Number(vm.showBalanceList[i].xsPayableAmount));
            				}
            				
            				vm.veidooList[j].lnReceivableAmount += 
            					(vm.showBalanceList[i].lnReceivableAmount==null?0:Number(vm.showBalanceList[i].lnReceivableAmount));
            				if(vm.showBalanceList[i].status == "1"){
            					vm.veidooList[j].lnInnerReceivableAmount += 
                					(vm.showBalanceList[i].lnReceivableAmount==null?0:Number(vm.showBalanceList[i].lnReceivableAmount));
            				}
            				if(vm.showBalanceList[i].status == "2"){
            					vm.veidooList[j].lnExterReceivableAmount += 
                					(vm.showBalanceList[i].lnReceivableAmount==null?0:Number(vm.showBalanceList[i].lnReceivableAmount));
            				}
            				
            				vm.veidooList[j].lnPayableAmount += 
            					(vm.showBalanceList[i].lnPayableAmount==null?0:Number(vm.showBalanceList[i].lnPayableAmount));
            				if(vm.showBalanceList[i].status == "1"){
            					vm.veidooList[j].lnInnerPayableAmount += 
                					(vm.showBalanceList[i].lnPayableAmount==null?0:Number(vm.showBalanceList[i].lnPayableAmount));
            				}
            				if(vm.showBalanceList[i].status == "2"){
            					vm.veidooList[j].lnExterPayableAmount += 
                					(vm.showBalanceList[i].lnPayableAmount==null?0:Number(vm.showBalanceList[i].lnPayableAmount));
            				}
            				
            				vm.veidooList[j].gcReceivableAmount += 
            					(vm.showBalanceList[i].gcReceivableAmount==null?0:Number(vm.showBalanceList[i].gcReceivableAmount));
            				vm.veidooList[j].gcPayableAmount += 
            					(vm.showBalanceList[i].gcPayableAmount==null?0:Number(vm.showBalanceList[i].gcPayableAmount));
            				createFlag = false;
            			}
            		}
            		if(createFlag){
            			var veidooShow = {};
            			veidooShow.finalCustId = vm.showBalanceList[i].finalCustId;
            			veidooShow.finalCustNo = vm.showBalanceList[i].finalCustNo;
            			veidooShow.finalCustName = vm.showBalanceList[i].cusName;
            			veidooShow.reconNum = vm.reconciliation.period;
            			
            			veidooShow.xsReceivableQuality = vm.showBalanceList[i].xsReceivableQuality;
            			veidooShow.xsPayableQuality = vm.showBalanceList[i].xsPayableQuality;
            			veidooShow.lnReceivableQuality = vm.showBalanceList[i].lnReceivableQuality;
            			veidooShow.lnPayableQuality = vm.showBalanceList[i].lnPayableQuality;
            			veidooShow.gcReceivableQuality = vm.showBalanceList[i].gcReceivableQuality;
            			veidooShow.gcPayableQuality = vm.showBalanceList[i].gcPayableQuality;
            			
            			veidooShow.xsReceivableArea = vm.showBalanceList[i].xsReceivableArea;
            			veidooShow.xsPayableArea = vm.showBalanceList[i].xsPayableArea;
            			veidooShow.lnReceivableArea = vm.showBalanceList[i].lnReceivableArea;
            			veidooShow.lnPayableArea = vm.showBalanceList[i].lnPayableArea;
            			veidooShow.gcReceivableArea = vm.showBalanceList[i].gcReceivableArea;
            			veidooShow.gcPayableArea = vm.showBalanceList[i].gcPayableArea;
            			
            			veidooShow.xsReceivableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			if(vm.showBalanceList[i].status == "1"){
            				veidooShow.xsInnerReceivableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			if(vm.showBalanceList[i].status == "2"){
            				veidooShow.xsExterReceivableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			
            			veidooShow.xsPayableAmount = vm.showBalanceList[i].xsPayableAmount;
            			if(vm.showBalanceList[i].status == "1"){
            				veidooShow.xsInnerPayableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			if(vm.showBalanceList[i].status == "2"){
            				veidooShow.xsExterPayableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			
            			veidooShow.lnReceivableAmount = vm.showBalanceList[i].lnReceivableAmount;
            			if(vm.showBalanceList[i].status == "1"){
            				veidooShow.lnInnerReceivableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			if(vm.showBalanceList[i].status == "2"){
            				veidooShow.lnExterReceivableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			
            			veidooShow.lnPayableAmount = vm.showBalanceList[i].lnPayableAmount;
            			if(vm.showBalanceList[i].status == "1"){
            				veidooShow.lnInnerPayableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			if(vm.showBalanceList[i].status == "2"){
            				veidooShow.lnExterPayableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			
            			veidooShow.gcReceivableAmount = vm.showBalanceList[i].gcReceivableAmount;
            			veidooShow.gcPayableAmount = vm.showBalanceList[i].gcPayableAmount;
            			vm.veidooList.push(veidooShow);
            		}
            	}
        	}
    	}else if(vm.veidoo == "3"){
    		if(vm.showBalanceList && vm.showBalanceList.length > 0){
            	for(let i=0;i<vm.showBalanceList.length;i++){
            		if(!vm.showBalanceList[i].finalSupId){
            			continue;
            		}
            		var createFlag = true;
            		for(let j=0;j<vm.veidooList.length;j++){
            			if(vm.veidooList[j].finalSupId == vm.showBalanceList[i].finalSupId){
            				
            				vm.veidooList[j].xsReceivableQuality = vm.veidooList[j].xsReceivableQuality == null?0:Number(vm.veidooList[j].xsReceivableQuality);
            				vm.veidooList[j].xsPayableQuality = vm.veidooList[j].xsPayableQuality == null?0:Number(vm.veidooList[j].xsPayableQuality);
            				vm.veidooList[j].lnReceivableQuality = vm.veidooList[j].lnReceivableQuality == null?0:Number(vm.veidooList[j].lnReceivableQuality);
            				vm.veidooList[j].lnPayableQuality = vm.veidooList[j].lnPayableQuality == null?0:Number(vm.veidooList[j].lnPayableQuality);
            				vm.veidooList[j].gcReceivableQuality = vm.veidooList[j].gcReceivableQuality == null?0:Number(vm.veidooList[j].gcReceivableQuality);
            				vm.veidooList[j].gcPayableQuality = vm.veidooList[j].gcPayableQuality == null?0:Number(vm.veidooList[j].gcPayableQuality);
            				vm.veidooList[j].xsReceivableQuality += 
            					(vm.showBalanceList[i].xsReceivableQuality==null?0:Number(vm.showBalanceList[i].xsReceivableQuality));
            				vm.veidooList[j].xsPayableQuality += 
            					(vm.showBalanceList[i].xsPayableQuality==null?0:Number(vm.showBalanceList[i].xsPayableQuality));
            				vm.veidooList[j].lnReceivableQuality += 
            					(vm.showBalanceList[i].lnReceivableQuality==null?0:Number(vm.showBalanceList[i].lnReceivableQuality));
            				vm.veidooList[j].lnPayableQuality += 
            					(vm.showBalanceList[i].lnPayableQuality==null?0:Number(vm.showBalanceList[i].lnPayableQuality));
            				vm.veidooList[j].gcReceivableQuality += 
            					(vm.showBalanceList[i].gcReceivableQuality==null?0:Number(vm.showBalanceList[i].gcReceivableQuality));
            				vm.veidooList[j].gcPayableQuality += 
            					(vm.showBalanceList[i].gcPayableQuality==null?0:Number(vm.showBalanceList[i].gcPayableQuality));
            				
            				vm.veidooList[j].xsReceivableArea = vm.veidooList[j].xsReceivableArea == null?0:Number(vm.veidooList[j].xsReceivableArea);
            				vm.veidooList[j].xsPayableArea = vm.veidooList[j].xsPayableArea == null?0:Number(vm.veidooList[j].xsPayableArea);
            				vm.veidooList[j].lnReceivableArea = vm.veidooList[j].lnReceivableArea == null?0:Number(vm.veidooList[j].lnReceivableArea);
            				vm.veidooList[j].lnPayableArea = vm.veidooList[j].lnPayableArea == null?0:Number(vm.veidooList[j].lnPayableArea);
            				vm.veidooList[j].gcReceivableArea = vm.veidooList[j].gcReceivableArea == null?0:Number(vm.veidooList[j].gcReceivableArea);
            				vm.veidooList[j].gcPayableArea = vm.veidooList[j].gcPayableArea == null?0:Number(vm.veidooList[j].gcPayableArea);
            				vm.veidooList[j].xsReceivableArea += 
            					(vm.showBalanceList[i].xsReceivableArea==null?0:Number(vm.showBalanceList[i].xsReceivableArea));
            				vm.veidooList[j].xsPayableArea += 
            					(vm.showBalanceList[i].xsPayableArea==null?0:Number(vm.showBalanceList[i].xsPayableArea));
            				vm.veidooList[j].lnReceivableArea += 
            					(vm.showBalanceList[i].lnReceivableArea==null?0:Number(vm.showBalanceList[i].lnReceivableArea));
            				vm.veidooList[j].lnPayableArea += 
            					(vm.showBalanceList[i].lnPayableArea==null?0:Number(vm.showBalanceList[i].lnPayableArea));
            				vm.veidooList[j].gcReceivableArea += 
            					(vm.showBalanceList[i].gcReceivableArea==null?0:Number(vm.showBalanceList[i].gcReceivableArea));
            				vm.veidooList[j].gcPayableArea += 
            					(vm.showBalanceList[i].gcPayableArea==null?0:Number(vm.showBalanceList[i].gcPayableArea));
            				
            				vm.veidooList[j].xsReceivableAmount = vm.veidooList[j].xsReceivableAmount == null?0:Number(vm.veidooList[j].xsReceivableAmount);
            				vm.veidooList[j].xsInnerPayableAmount = vm.veidooList[j].xsInnerPayableAmount == null?0:Number(vm.veidooList[j].xsInnerPayableAmount);
            				vm.veidooList[j].xsExterReceivableAmount = vm.veidooList[j].xsExterReceivableAmount == null?0:Number(vm.veidooList[j].xsExterReceivableAmount);
            				
            				vm.veidooList[j].xsPayableAmount = vm.veidooList[j].xsPayableAmount == null?0:Number(vm.veidooList[j].xsPayableAmount);
            				vm.veidooList[j].xsInnerPayableAmount = vm.veidooList[j].xsInnerPayableAmount == null?0:Number(vm.veidooList[j].xsInnerPayableAmount);
            				vm.veidooList[j].xsExterPayableAmount = vm.veidooList[j].xsExterPayableAmount == null?0:Number(vm.veidooList[j].xsExterPayableAmount);
            				
            				vm.veidooList[j].lnReceivableAmount = vm.veidooList[j].lnReceivableAmount == null?0:Number(vm.veidooList[j].lnReceivableAmount);
            				vm.veidooList[j].lnInnerReceivableAmount = vm.veidooList[j].lnInnerReceivableAmount == null?0:Number(vm.veidooList[j].lnInnerReceivableAmount);
            				vm.veidooList[j].lnExterReceivableAmount = vm.veidooList[j].lnExterReceivableAmount == null?0:Number(vm.veidooList[j].lnExterReceivableAmount);
            				
            				vm.veidooList[j].lnPayableAmount = vm.veidooList[j].lnPayableAmount == null?0:Number(vm.veidooList[j].lnPayableAmount);
            				vm.veidooList[j].lnInnerPayableAmount = vm.veidooList[j].lnInnerPayableAmount == null?0:Number(vm.veidooList[j].lnInnerPayableAmount);
            				vm.veidooList[j].lnExterPayableAmount = vm.veidooList[j].lnExterPayableAmount == null?0:Number(vm.veidooList[j].lnExterPayableAmount);
            				
            				vm.veidooList[j].gcReceivableAmount = vm.veidooList[j].gcReceivableAmount == null?0:Number(vm.veidooList[j].gcReceivableAmount);
            				vm.veidooList[j].gcPayableAmount = vm.veidooList[j].gcPayableAmount == null?0:Number(vm.veidooList[j].gcPayableAmount);
            				
            				vm.veidooList[j].xsReceivableAmount += 
            					(vm.showBalanceList[i].xsReceivableAmount==null?0:Number(vm.showBalanceList[i].xsReceivableAmount));
            				if(vm.showBalanceList[i].status == "1"){
            					vm.veidooList[j].xsInnerReceivableAmount += 
                					(vm.showBalanceList[i].xsReceivableAmount==null?0:Number(vm.showBalanceList[i].xsReceivableAmount));
            				}
            				if(vm.showBalanceList[i].status == "2"){
            					vm.veidooList[j].xsExterReceivableAmount += 
                					(vm.showBalanceList[i].xsReceivableAmount==null?0:Number(vm.showBalanceList[i].xsReceivableAmount));
            				}
            				
            				vm.veidooList[j].xsPayableAmount += 
            					(vm.showBalanceList[i].xsPayableAmount==null?0:Number(vm.showBalanceList[i].xsPayableAmount));
            				if(vm.showBalanceList[i].status == "1"){
            					vm.veidooList[j].xsInnerPayableAmount += 
                					(vm.showBalanceList[i].xsPayableAmount==null?0:Number(vm.showBalanceList[i].xsPayableAmount));
            				}
            				if(vm.showBalanceList[i].status == "2"){
            					vm.veidooList[j].xsExterPayableAmount += 
                					(vm.showBalanceList[i].xsPayableAmount==null?0:Number(vm.showBalanceList[i].xsPayableAmount));
            				}
            				
            				vm.veidooList[j].lnReceivableAmount += 
            					(vm.showBalanceList[i].lnReceivableAmount==null?0:Number(vm.showBalanceList[i].lnReceivableAmount));
            				if(vm.showBalanceList[i].status == "1"){
            					vm.veidooList[j].lnInnerReceivableAmount += 
                					(vm.showBalanceList[i].lnReceivableAmount==null?0:Number(vm.showBalanceList[i].lnReceivableAmount));
            				}
            				if(vm.showBalanceList[i].status == "2"){
            					vm.veidooList[j].lnExterReceivableAmount += 
                					(vm.showBalanceList[i].lnReceivableAmount==null?0:Number(vm.showBalanceList[i].lnReceivableAmount));
            				}
            				
            				vm.veidooList[j].lnPayableAmount += 
            					(vm.showBalanceList[i].lnPayableAmount==null?0:Number(vm.showBalanceList[i].lnPayableAmount));
            				if(vm.showBalanceList[i].status == "1"){
            					vm.veidooList[j].lnInnerPayableAmount += 
                					(vm.showBalanceList[i].lnPayableAmount==null?0:Number(vm.showBalanceList[i].lnPayableAmount));
            				}
            				if(vm.showBalanceList[i].status == "2"){
            					vm.veidooList[j].lnExterPayableAmount += 
                					(vm.showBalanceList[i].lnPayableAmount==null?0:Number(vm.showBalanceList[i].lnPayableAmount));
            				}
            				
            				vm.veidooList[j].gcReceivableAmount += 
            					(vm.showBalanceList[i].gcReceivableAmount==null?0:Number(vm.showBalanceList[i].gcReceivableAmount));
            				vm.veidooList[j].gcPayableAmount += 
            					(vm.showBalanceList[i].gcPayableAmount==null?0:Number(vm.showBalanceList[i].gcPayableAmount));
            				createFlag = false;
            			}
            		}
            		if(createFlag){
            			var veidooShow = {};
            			veidooShow.finalCustId = vm.showBalanceList[i].finalCustId;
            			veidooShow.finalCustNo = vm.showBalanceList[i].finalCustNo;
            			veidooShow.finalCustName = vm.showBalanceList[i].cusName;
            			
            			veidooShow.finalSupId = vm.showBalanceList[i].finalSupId;
            			veidooShow.finalSupNo = vm.showBalanceList[i].finalSupNo;
            			veidooShow.supName = vm.showBalanceList[i].supName;
            			
            			veidooShow.reconNum = vm.reconciliation.period;
            			
            			veidooShow.xsReceivableQuality = vm.showBalanceList[i].xsReceivableQuality;
            			veidooShow.xsPayableQuality = vm.showBalanceList[i].xsPayableQuality;
            			veidooShow.lnReceivableQuality = vm.showBalanceList[i].lnReceivableQuality;
            			veidooShow.lnPayableQuality = vm.showBalanceList[i].lnPayableQuality;
            			veidooShow.gcReceivableQuality = vm.showBalanceList[i].gcReceivableQuality;
            			veidooShow.gcPayableQuality = vm.showBalanceList[i].gcPayableQuality;
            			
            			veidooShow.xsReceivableArea = vm.showBalanceList[i].xsReceivableArea;
            			veidooShow.xsPayableArea = vm.showBalanceList[i].xsPayableArea;
            			veidooShow.lnReceivableArea = vm.showBalanceList[i].lnReceivableArea;
            			veidooShow.lnPayableArea = vm.showBalanceList[i].lnPayableArea;
            			veidooShow.gcReceivableArea = vm.showBalanceList[i].gcReceivableArea;
            			veidooShow.gcPayableArea = vm.showBalanceList[i].gcPayableArea;
            			
            			veidooShow.xsReceivableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			if(vm.showBalanceList[i].status == "1"){
            				veidooShow.xsInnerReceivableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			if(vm.showBalanceList[i].status == "2"){
            				veidooShow.xsExterReceivableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			
            			veidooShow.xsPayableAmount = vm.showBalanceList[i].xsPayableAmount;
            			if(vm.showBalanceList[i].status == "1"){
            				veidooShow.xsInnerPayableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			if(vm.showBalanceList[i].status == "2"){
            				veidooShow.xsExterPayableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			
            			veidooShow.lnReceivableAmount = vm.showBalanceList[i].lnReceivableAmount;
            			if(vm.showBalanceList[i].status == "1"){
            				veidooShow.lnInnerReceivableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			if(vm.showBalanceList[i].status == "2"){
            				veidooShow.lnExterReceivableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			
            			veidooShow.lnPayableAmount = vm.showBalanceList[i].lnPayableAmount;
            			if(vm.showBalanceList[i].status == "1"){
            				veidooShow.lnInnerPayableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			if(vm.showBalanceList[i].status == "2"){
            				veidooShow.lnExterPayableAmount = vm.showBalanceList[i].xsReceivableAmount;
            			}
            			
            			veidooShow.gcReceivableAmount = vm.showBalanceList[i].gcReceivableAmount;
            			veidooShow.gcPayableAmount = vm.showBalanceList[i].gcPayableAmount;
            			vm.veidooList.push(veidooShow);
            		}
            	}
        	}
    	}
    	angular.forEach(vm.veidooList,function(p){
			if(p.xsReceivableAmount)
			{
				p.xsReceivableAmount = Number(p.xsReceivableAmount).toFixed(2);
			}
			if(p.xsPayableAmount)
			{
				p.xsPayableAmount = Number(p.xsPayableAmount).toFixed(2);
			}
			if(p.lnReceivableAmount)
			{
				p.lnReceivableAmount = Number(p.lnReceivableAmount).toFixed(2);
			}
			if(p.lnPayableAmount)
			{
				p.lnPayableAmount = Number(p.lnPayableAmount).toFixed(2);
			}
			if(p.gcReceivableAmount)
			{
				p.gcReceivableAmount = Number(p.gcReceivableAmount).toFixed(2);
			}
			if(p.gcPayableAmount)
			{
				p.gcPayableAmount = Number(p.gcPayableAmount).toFixed(2);
			}
			if(vm.veidoo == "2"){
				if(p.xsReceivableAmount)
				{
					vm.custSumTotal.sumXsReceivableAmount += Number(p.xsReceivableAmount);
				}
				if(p.xsPayableAmount)
				{
					vm.custSumTotal.sumXsPayableAmount += Number(p.xsPayableAmount);
				}
				if(p.lnReceivableAmount)
				{
					vm.custSumTotal.sumLnReceivableAmount += Number(p.lnReceivableAmount);
				}
				if(p.lnPayableAmount)
				{
					vm.custSumTotal.sumLnPayableAmount += Number(p.lnPayableAmount);
				}
				if(p.gcReceivableAmount)
				{
					vm.custSumTotal.sumGcReceivableAmount += Number(p.gcReceivableAmount);
				}
				if(p.gcPayableAmount)
				{
					vm.custSumTotal.sumGcPayableAmount += Number(p.gcPayableAmount);
				}
				
				if(p.xsReceivableQuality)
				{
					vm.custSumTotal.sumXsReceivableQuality += Number(p.xsReceivableQuality);
				}
				if(p.xsPayableQuality)
				{
					vm.custSumTotal.sumXsPayableQuality += Number(p.xsPayableQuality);
				}
				if(p.lnReceivableQuality)
				{
					vm.custSumTotal.sumLnReceivableQuality += Number(p.lnReceivableQuality);
				}
				if(p.lnPayableQuality)
				{
					vm.custSumTotal.sumLnPayableQuality += Number(p.lnPayableQuality);
				}
				if(p.gcReceivableQuality)
				{
					vm.custSumTotal.sumGcReceivableQuality += Number(p.gcReceivableQuality);
				}
				if(p.gcPayableQuality)
				{
					vm.custSumTotal.sumGcPayableQuality += Number(p.gcPayableQuality);
				}
				
				if(p.xsReceivableArea)
				{
					vm.custSumTotal.sumXsReceivableArea += Number(p.xsReceivableArea);
				}
				if(p.xsPayableArea)
				{
					vm.custSumTotal.sumXsPayableArea += Number(p.xsPayableArea);
				}
				if(p.lnReceivableArea)
				{
					vm.custSumTotal.sumLnReceivableArea += Number(p.lnReceivableArea);
				}
				if(p.lnPayableArea)
				{
					vm.custSumTotal.sumLnPayableArea += Number(p.lnPayableArea);
				}
				if(p.gcReceivableArea)
				{
					vm.custSumTotal.sumGcReceivableArea += Number(p.gcReceivableArea);
				}
				if(p.gcPayableArea)
				{
					vm.custSumTotal.sumGcPayableArea += Number(p.gcPayableArea);
				}
			}else if(vm.veidoo == "3"){
				if(p.xsReceivableAmount)
				{
					vm.supSumTotal.sumXsReceivableAmount += Number(p.xsReceivableAmount);
				}
				if(p.xsPayableAmount)
				{
					vm.supSumTotal.sumXsPayableAmount += Number(p.xsPayableAmount);
				}
				if(p.lnReceivableAmount)
				{
					vm.supSumTotal.sumLnReceivableAmount += Number(p.lnReceivableAmount);
				}
				if(p.lnPayableAmount)
				{
					vm.supSumTotal.sumLnPayableAmount += Number(p.lnPayableAmount);
				}
				if(p.gcReceivableAmount)
				{
					vm.supSumTotal.sumGcReceivableAmount += Number(p.gcReceivableAmount);
				}
				if(p.gcPayableAmount)
				{
					vm.supSumTotal.sumGcPayableAmount += Number(p.gcPayableAmount);
				}
				
				if(p.xsReceivableQuality)
				{
					vm.supSumTotal.sumXsReceivableQuality += Number(p.xsReceivableQuality);
				}
				if(p.xsPayableQuality)
				{
					vm.supSumTotal.sumXsPayableQuality += Number(p.xsPayableQuality);
				}
				if(p.lnReceivableQuality)
				{
					vm.supSumTotal.sumLnReceivableQuality += Number(p.lnReceivableQuality);
				}
				if(p.lnPayableQuality)
				{
					vm.supSumTotal.sumLnPayableQuality += Number(p.lnPayableQuality);
				}
				if(p.gcReceivableQuality)
				{
					vm.supSumTotal.sumGcReceivableQuality += Number(p.gcReceivableQuality);
				}
				if(p.gcPayableQuality)
				{
					vm.supSumTotal.sumGcPayableQuality += Number(p.gcPayableQuality);
				}
				
				if(p.xsReceivableArea)
				{
					vm.supSumTotal.sumXsReceivableArea += Number(p.xsReceivableArea);
				}
				if(p.xsPayableArea)
				{
					vm.supSumTotal.sumXsPayableArea += Number(p.xsPayableArea);
				}
				if(p.lnReceivableArea)
				{
					vm.supSumTotal.sumLnReceivableArea += Number(p.lnReceivableArea);
				}
				if(p.lnPayableArea)
				{
					vm.supSumTotal.sumLnPayableArea += Number(p.lnPayableArea);
				}
				if(p.gcReceivableArea)
				{
					vm.supSumTotal.sumGcReceivableArea += Number(p.gcReceivableArea);
				}
				if(p.gcPayableArea)
				{
					vm.supSumTotal.sumGcPayableArea += Number(p.gcPayableArea);
				}
			}
		});
    	// 销售收款金额汇总
		if(vm.custSumTotal.sumXsReceivableAmount > 0)
		{
			vm.custSumTotal.sumXsReceivableAmount = bigNumberTransform(vm.custSumTotal.sumXsReceivableAmount);
		}
		else if(vm.custSumTotal.sumXsReceivableAmount < 0)
		{
			vm.custSumTotal.sumXsReceivableAmount = "-" + bigNumberTransform(vm.custSumTotal.sumXsReceivableAmount * -1);
		}
		// 销售付款金额汇总
		if(vm.custSumTotal.sumXsPayableAmount > 0)
		{
			vm.custSumTotal.sumXsPayableAmount = bigNumberTransform(vm.custSumTotal.sumXsPayableAmount);
		}
		else if(vm.custSumTotal.sumXsPayableAmount < 0)
		{
			vm.custSumTotal.sumXsPayableAmount = "-" + bigNumberTransform(vm.custSumTotal.sumXsPayableAmount * -1);
		}
		// 总部经济收款金额汇总
		if(vm.custSumTotal.sumLnReceivableAmount > 0)
		{
			vm.custSumTotal.sumLnReceivableAmount = bigNumberTransform(vm.custSumTotal.sumLnReceivableAmount);
		}
		else if(vm.custSumTotal.sumLnReceivableAmount < 0)
		{
			vm.custSumTotal.sumLnReceivableAmount = "-" + bigNumberTransform(vm.custSumTotal.sumLnReceivableAmount * -1);
		}
		// 总部经济付款金额汇总
		if(vm.custSumTotal.sumLnPayableAmount > 0)
		{
			vm.custSumTotal.sumLnPayableAmount = bigNumberTransform(vm.custSumTotal.sumLnPayableAmount);
		}
		else if(vm.custSumTotal.sumLnPayableAmount < 0)
		{
			vm.custSumTotal.sumLnPayableAmount = "-" + bigNumberTransform(vm.custSumTotal.sumLnPayableAmount * -1);
		}
		// 工厂收款金额汇总
		if(vm.custSumTotal.sumGcReceivableAmount > 0)
		{
			vm.custSumTotal.sumGcReceivableAmount = bigNumberTransform(vm.custSumTotal.sumGcReceivableAmount);
		}
		else if(vm.custSumTotal.sumGcReceivableAmount < 0)
		{
			vm.custSumTotal.sumGcReceivableAmount = "-" + bigNumberTransform(vm.custSumTotal.sumGcReceivableAmount * -1);
		}
		
		// 销售收款金额汇总
		if(vm.supSumTotal.sumXsReceivableAmount > 0)
		{
			vm.supSumTotal.sumXsReceivableAmount = bigNumberTransform(vm.supSumTotal.sumXsReceivableAmount);
		}
		else if(vm.supSumTotal.sumXsReceivableAmount < 0)
		{
			vm.supSumTotal.sumXsReceivableAmount = "-" + bigNumberTransform(vm.supSumTotal.sumXsReceivableAmount * -1);
		}
		// 销售付款金额汇总
		if(vm.supSumTotal.sumXsPayableAmount > 0)
		{
			vm.supSumTotal.sumXsPayableAmount = bigNumberTransform(vm.supSumTotal.sumXsPayableAmount);
		}
		else if(vm.supSumTotal.sumXsPayableAmount < 0)
		{
			vm.supSumTotal.sumXsPayableAmount = "-" + bigNumberTransform(vm.supSumTotal.sumXsPayableAmount * -1);
		}
		// 总部经济收款金额汇总
		if(vm.supSumTotal.sumLnReceivableAmount > 0)
		{
			vm.supSumTotal.sumLnReceivableAmount = bigNumberTransform(vm.supSumTotal.sumLnReceivableAmount);
		}
		else if(vm.supSumTotal.sumLnReceivableAmount < 0)
		{
			vm.supSumTotal.sumLnReceivableAmount = "-" + bigNumberTransform(vm.supSumTotal.sumLnReceivableAmount * -1);
		}
		// 总部经济付款金额汇总
		if(vm.supSumTotal.sumLnPayableAmount > 0)
		{
			vm.supSumTotal.sumLnPayableAmount = bigNumberTransform(vm.supSumTotal.sumLnPayableAmount);
		}
		else if(vm.supSumTotal.sumLnPayableAmount < 0)
		{
			vm.supSumTotal.sumLnPayableAmount = "-" + bigNumberTransform(vm.supSumTotal.sumLnPayableAmount * -1);
		}
		// 工厂收款金额汇总
		if(vm.supSumTotal.sumGcReceivableAmount > 0)
		{
			vm.supSumTotal.sumGcReceivableAmount = bigNumberTransform(vm.supSumTotal.sumGcReceivableAmount);
		}
		else if(vm.supSumTotal.sumGcReceivableAmount < 0)
		{
			vm.supSumTotal.sumGcReceivableAmount = "-" + bigNumberTransform(vm.supSumTotal.sumGcReceivableAmount * -1);
		}
	};
	
	vm.dealIssueDataList = function() {
		computTotal(vm.groupCenterTwoDataList);
		vm.dealVeidooDataList();
	};
	 
    function loadData() {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
        // 获取所有客户
        upida.get("finance/reconciliation/load/data").then(function (data) {
            vm.periodList = data.periodList;
            if(vm.periodList && vm.periodList.length > 0)
            {
            	vm.periodList.splice(0,0,'所有');
            }
            vm.periodQuery = data.period;
            vm.page.pageNo = 1;
/*            if(vm.currCompanyId == 205){
            	vm.reconciliation.period = data.period
            }*/
			vm.reconciliation.period = data.period;
			// 重置付款

            // 初始化第一页，条件为空
            // vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
/*			initMaterialCheckData(vm.reconciliation.period);*/
			vm.getReconciliationData();
            MainCtrl.unblockUI();
            vm.queryType = "1";
        });
    };

    vm.periodCopy = null;
    function initMaterialCheckData(period)
	{
		return;
		if(vm.periodCopy && vm.periodCopy == period)
		{
			return;
		}
		let query = {};
		query.startMonth = period;
		upida.post("finance/reconciliation/initMaterialCheckData", query).then(function(data)
		{
			vm.periodCopy = period;
		});
	}
    
    vm.checkStatusDataOpen = function()
    {
		vm.message = "您确定内部公司账单？";
		$('#showcheckStatusData').modal();
    }
    
    vm.checkStatusData = function()
    {
		var groupData = {};
  		groupData.period = vm.reconciliation.period;
		groupData.company = vm.saleCom;
    	MainCtrl.blockUI({
  	       animate: true,
  	    });
  		upida.post("finance/reconciliation/checkStatusData", groupData).then(function(data) 
  		{
  			if(data == "success")
  			{
				vm.message = "确认成功!";
  			}
  			else
  			{
				vm.message = "确认失败!";
  			}
  			$('#static').modal();
  			MainCtrl.unblockUI();
  		});
    }
    
    function loadRight() {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	vm.queryAll = CommonUtil.dataRangeIsAll("10804", BaseUtil.getMenuList());
        upida.get("common/rightall?prefix=financemain:reconciliation").then(function(data){
            vm.right.manage = data.manage;
            vm.right.edit = data.edit;
            vm.right.view = data.view;
            loadData();
        });
    };
    
    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        vm.currCompanyId = BaseUtil.getUser().company.recordId;
        vm.nowCompany = BaseUtil.getUser().company;
        vm.ValiteCompanyUtil = BaseUtil.getDefaultData();
        loadRight();
    });
}]);
