/* Setup general page controller */
kybApp.controller('rawmaterialStockCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil','BaseUtil','$http','Upload', function ($rootScope, $scope, upida, $timeout, CommonUtil,BaseUtil,$http,Upload) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();
        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });

    var vm = this;
    $scope.shouldAutoStart = false;
    $scope.help1Options = {
        steps:[
        {
            element: '#oneStep1',
            intro: "当您打开页面后会自动加载原料信息至列表！",
            position: 'top'
        },
        {
            element: '#oneStep2',
            intro: "单击右边的排序按钮进行排序，箭头向上为升序，箭头向下为降序，若为上下则未排序, <strong>单击首列物料编号或双击该行查看原料的出入库信息！</strong>'",
            position: 'right'
        },
        {
            element: '#oneStep3',
            intro: '此处为分页区域，您可以点击您想查看的页面！',
            position: 'left'
        },
        {
            element: '#oneStep4',
            intro: '此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！',
            position: 'right'
        },
        {
            element: '#oneStep5',
            intro: "此处为查询条件，可设置不同的条件，点击右下角的查询来过滤信息！",
            position: 'bottom'
        },
        {
            element: '#oneStep6',
            intro: '操作按钮区域，可导出原料信息！',
            position: 'left'
        },
        {
            element: '#oneStep7',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
    
    $scope.help2Options = {
        steps:[
        {
            element: '#twoStep1',
            intro: "当您打开页面后会自动加载原料出入库信息至列表！",
            position: 'top'
        },
        {
            element: '#twoStep2',
            intro: "单击右边的排序按钮进行排序，箭头向上为升序，箭头向下为降序，若为上下则未排序, <strong>单击首列物料编号或双击该行查看详情！</strong>'",
            position: 'right'
        },
        {
            element: '#twoStep3',
            intro: "快捷操作，可对原料出入库记录进行冲红，冲红指因某些原因要使此条记录作废，生成一条记录抵消此记录，冲红后此记录作废，记录仍然保留！",
            position: 'left'
        }
        ,
        {
            element: '#twoStep4',
            intro: '此处为分页区域，您可以点击您想查看的页面！',
            position: 'left'
        },
        {
            element: '#twoStep5',
            intro: '此处可设置每页显示数据的条数，您可以选择每页显示5/10/30/50条数据！',
            position: 'right'
        },
        {
            element: '#twoStep6',
            intro: "此处为查询条件，可设置不同的条件，点击右下角的查询来过滤信息！",
            position: 'bottom'
        },
        {
            element: '#twoStep7',
            intro: '操作按钮区域，可导出原料出入库信息！',
            position: 'left'
        },
        {
            element: '#twoStep8',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
    
    $scope.help = function(){
    	if (vm.tabs.info.active){
    		$scope.help1();
    	} else if(vm.tabs.inout.active){
    		$scope.help2();
    	}
    };
    
    vm.right = {}; // 权限

    /***** tj *****/
    // tj 判断是否多次点击
    vm.clicks = true;
    vm.adjustStocks = {};
    /*****end*****/
    //是否有原料采购菜单的权限
    vm.showRawmaterial = false;
    
    // 原料物料操作对象
    vm.rawmaterial = {};

    // 原料物料的出入库记录操作对象
    vm.inoutRecord = {};

    var handle = {info: 0, inout: 1,adjust: 2,month:3,quotation:4,first:5,store:6}; // 操作对象
    vm.page = {}; // 分页数据
    vm.page.pageSizeOptions = [5, 10, 30, 50, 100, 150]; // 显示数据大小

    // 原料
    vm.page.info = {};
    vm.page.info.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.info.pageSize = 10;
    vm.page.info.pageNo = 1;
    vm.page.info.url = "stock/rawmaterial/page";
    vm.page.info.condition = []; // 条件
    
    vm.page.infoPlace = {};
    vm.page.infoPlace.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.infoPlace.pageSize = 10;
    vm.page.infoPlace.pageNo = 1;
    vm.page.infoPlace.url = "stock/rawmaterial/inoutPlacePage";
    vm.page.infoPlace.condition = []; // 条件
    
    vm.page.infoMonth = {};
    vm.page.infoMonth.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.infoMonth.pageSize = 10;
    vm.page.infoMonth.pageNo = 1;
    vm.page.infoMonth.url = "stock/rawmaterial/materialMonthPage";
    vm.page.infoMonth.condition = []; // 条件

    vm.page.infoQuotation = {};
    vm.page.infoQuotation.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.infoQuotation.pageSize = 10;
    vm.page.infoQuotation.pageNo = 1;
    vm.page.infoQuotation.url = "stock/rawmaterial/approvalPage";
    vm.page.infoQuotation.condition = []; // 条件

    vm.page.firstInFirstOut = {};
    vm.page.firstInFirstOut.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    /*vm.page.firstInFirstOut.pageSize = 10;
    vm.page.firstInFirstOut.pageNo = 1;*/
    vm.page.firstInFirstOut.url = "stock/rawmaterial/getFirstFirstOutPage";
    vm.page.firstInFirstOut.condition = []; // 条件
    vm.firstInFirstOut = {};
    vm.firstInFirstOut.sort = {};
    vm.firstInFirstOut.sort.name = "orderBy";
    vm.firstInFirstOut.sort.value = "createdDate DESC";

    vm.firstInFirstOut.sentTimeStartQr = {};
    vm.firstInFirstOut.sentTimeStartQr.name = "sentTimeStartQr";
    vm.firstInFirstOut.sentTimeStartQr.value = "";

    vm.firstInFirstOut.sentTimeEndQr = {};
    vm.firstInFirstOut.sentTimeEndQr.name = "sentTimeEndQr";
    vm.firstInFirstOut.sentTimeEndQr.value = "";

    // 查询条件参数
    vm.query = {}; // 查询对象
    vm.query.no = {};
    vm.query.no.name = "no";
    vm.query.no.value = "";
    vm.query.name = {};
    vm.query.name.name = "name";
    vm.query.name.value = "";
    vm.query.materialType = {};
    vm.query.materialType.name = "materialType";
    vm.query.materialType.value = "";
    vm.query.specification = {};
    vm.query.specification.name = "specification";
    vm.query.specification.value = "";
    vm.query.storehouse = {};
    vm.query.storehouse.name = "storehouse";
    vm.query.storehouse.value = "";
    vm.query.isIncludeStock = {};
    vm.query.isIncludeStock.value = true;
    vm.query.craftNoStatus = false;
    vm.query.submittedStatus = false;
    vm.query.isIncludeStock.name = "includeStock";
    vm.query.sort = {};
    vm.query.sort.name = "orderBy";
    vm.query.sort.value = "createdDate DESC";
    vm.query.ststus = {};
    vm.query.ststus.name = "status";
    vm.query.ststus.value = "1";
    // 原料出入库记录
    vm.page.inout = {};
    vm.page.inout.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.inout.pageSize = 10;
    vm.page.inout.pageNo = 1;
    vm.page.inout.url = "stock/rawmaterial/inout/page";
    vm.page.inout.condition = []; // 条件

    // 查询条件参数
    vm.inoutQuery = {}; // 查询对象
    vm.inoutQuery.inoutType = {};
    vm.inoutQuery.inoutType.name = "inoutType";
    vm.inoutQuery.inoutType.value = "";
    vm.inoutQuery.no = {};
    vm.inoutQuery.no.name = "no";
    vm.inoutQuery.no.value = "";
    vm.inoutQuery.name = {};
    vm.inoutQuery.name.name = "name";
    vm.inoutQuery.name.value = "";
    vm.inoutQuery.supplierNo = {};
    vm.inoutQuery.supplierNo.name = "supplierNo";
    vm.inoutQuery.supplierNo.value = "";
    vm.inoutQuery.status = {};
    vm.inoutQuery.status.name = "status";
    vm.inoutQuery.status.value = "";
    vm.inoutQuery.statusTwo = {};
    vm.inoutQuery.statusTwo.name = "statusTwo";
    vm.inoutQuery.statusTwo.value = "1";
    vm.inoutQuery.storehouse = {};
    vm.inoutQuery.storehouse.name = "storehouse";
    vm.inoutQuery.storehouse.value = "";
    vm.inoutQuery.supplierName = {};
    vm.inoutQuery.supplierName.name = "supplierName";
    vm.inoutQuery.supplierName.value = "";
    vm.inoutQuery.sort = {};
    vm.inoutQuery.sort.name = "orderBy";
    vm.inoutQuery.sort.value = "operateDate DESC";
    // 查询条件参数
    vm.inoutQuery.inoutTimeStartQr = {};
    vm.inoutQuery.inoutTimeStartQr.name = "inoutTimeStartQr";
    vm.inoutQuery.inoutTimeStartQr.value = "";
    vm.inoutQuery.inoutTimeEndQr = {};
    vm.inoutQuery.inoutTimeEndQr.name = "inoutTimeEndQr";
    vm.inoutQuery.inoutTimeEndQr.value = "";
    //生产编号查询
    vm.inoutQuery.craftNo = {};
    vm.inoutQuery.craftNo.name = "craftNo";
    vm.inoutQuery.craftNo.value = "";
    
    vm.inoutPlaceQuery = {}; // 查询对象
    vm.inoutPlaceQuery.inoutType = {};
    vm.inoutPlaceQuery.inoutType.name = "inoutType";
    vm.inoutPlaceQuery.inoutType.value = "";
    vm.inoutPlaceQuery.material = {};
    vm.inoutPlaceQuery.materialPlace = {};
    vm.inoutPlaceQuery.supplierNo = {};
    vm.inoutPlaceQuery.supplierNo.name = "supplierNo";
    vm.inoutPlaceQuery.supplierNo.value = "";
    vm.inoutPlaceQuery.status = {};
    vm.inoutPlaceQuery.status.name = "status";
    vm.inoutPlaceQuery.status.value = "";
    vm.inoutPlaceQuery.storehouse = {};
    vm.inoutPlaceQuery.storehouse.name = "storehouse";
    vm.inoutPlaceQuery.storehouse.value = "";
    vm.inoutPlaceQuery.supplierName = {};
    vm.inoutPlaceQuery.supplierName.name = "supplierName";
    vm.inoutPlaceQuery.supplierName.value = "";
    vm.inoutPlaceQuery.sort = {};
    vm.inoutPlaceQuery.sort.name = "orderBy";
    vm.inoutPlaceQuery.sort.value = "operateDate DESC";
    vm.inoutPlaceQuery.sentTimeStartQr = {};
    vm.inoutPlaceQuery.sentTimeStartQr.name = "sentTimeStartQr";
    vm.inoutPlaceQuery.sentTimeStartQr.value = "";

    vm.inoutPlaceQuery.sentTimeEndQr = {};
    vm.inoutPlaceQuery.sentTimeEndQr.name = "sentTimeEndQr";
    vm.inoutPlaceQuery.sentTimeEndQr.value = "";
    // 查询条件参数
    vm.inoutPlaceQuery.inoutTimeStartQr = {};
    vm.inoutPlaceQuery.inoutTimeStartQr.name = "inoutTimeStartQr";
    vm.inoutPlaceQuery.inoutTimeStartQr.value = "";
    vm.inoutPlaceQuery.inoutTimeEndQr = {};
    vm.inoutPlaceQuery.inoutTimeEndQr.name = "inoutTimeEndQr";
    vm.inoutPlaceQuery.inoutTimeEndQr.value = "";
    vm.inoutPlaceQuery.inoutTimeTwoStartQr = {};
    vm.inoutPlaceQuery.inoutTimeTwoEndQr = {};
    //生产编号查询
    vm.inoutPlaceQuery.craftNo = {};
    vm.inoutPlaceQuery.craftNo.name = "craftNo";
    vm.inoutPlaceQuery.craftNo.value = "";
    
    // 单价调整记录
    vm.adjustPage = {};
    vm.adjustPage.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.adjustPage.pageSize = 10;
    vm.adjustPage.pageNo = 1;
    vm.adjustPage.url = "stock/rawmaterial/adjust/page";
    vm.adjustPage.condition = []; // 条件
    
    vm.adjustQuery = {};
    vm.adjustQuery.no = {};
    vm.adjustQuery.no.name = "material.no";
    vm.adjustQuery.no.value = "";
    
    vm.adjustQuery.name = {};
    vm.adjustQuery.name.name = "material.name";
    vm.adjustQuery.name.value = "";
    
    vm.adjustQuery.sort = {};
    vm.adjustQuery.sort.name = "orderBy";
    vm.adjustQuery.sort.value = "createdDate DESC";

    // 原料报价查询参数
    // 查询条件参数
    vm.quotationQuery = {}; // 查询对象
    vm.quotationQuery.no = {};
    vm.quotationQuery.no.name = "no";
    vm.quotationQuery.no.value = "";
    vm.quotationQuery.name = {};
    vm.quotationQuery.name.name = "name";
    vm.quotationQuery.name.value = "";
    vm.quotationQuery.materialType = {};
    vm.quotationQuery.materialType.name = "materialType";
    vm.quotationQuery.materialType.value = "";
    vm.quotationQuery.specification = {};
    vm.quotationQuery.specification.name = "specification";
    vm.quotationQuery.specification.value = "";
    vm.quotationQuery.storehouse = {};
    vm.quotationQuery.storehouse.name = "storehouse";
    vm.quotationQuery.storehouse.value = "";
    vm.quotationQuery.isIncludeStock = {};
    vm.quotationQuery.isIncludeStock.value = true;
    vm.quotationQuery.craftNoStatus = false;
    vm.quotationQuery.isIncludeStock.name = "includeStock";
    vm.quotationQuery.sort = {};
    vm.quotationQuery.sort.name = "orderBy";
    vm.quotationQuery.sort.value = "createdDate DESC";
    vm.quotationQuery.ststus = {};
    vm.quotationQuery.ststus.name = "status";
    vm.quotationQuery.ststus.value = "1";

    // 自动出库
    vm.page.materialStore = {};
    vm.page.materialStore.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.materialStore.pageSize = 10;
    vm.page.materialStore.pageNo = 1;
    vm.page.materialStore.url = "stock/materialStore/getMaterialStorePage";
    vm.page.materialStore.condition = []; // 条件
    vm.materialStore = {};
    vm.materialStore.sort = {};
    vm.materialStore.sort.name = "orderBy";
    vm.materialStore.sort.value = "createdDate DESC";
    vm.page.materialStore.pageSizeOptions = [5, 10, 30, 50];

    vm.materialStore.sentTimeStartQr = {};
    vm.materialStore.sentTimeStartQr.name = "sentTimeStartQr";
    vm.materialStore.sentTimeStartQr.value = "";

    vm.materialStore.sentTimeEndQr = {};
    vm.materialStore.sentTimeEndQr.name = "sentTimeEndQr";
    vm.materialStore.sentTimeEndQr.value = "";

    vm.queryNo = "";
    vm.queryPrintNo = "";
    
    vm.haveStockFlag = "1";
    
    // 时间范围的Model
    vm.time = {
        start: {},
        end: {}
    };
    // 时间范围的选项
    vm.rangeOptions = {
        //format: "YYYY-MM-DD",
//    	startDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5))),
    	startDate:new Date((new Date).getFullYear(), (new Date).getMonth(), 1),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 6)))
    };
    
    vm.initDate=function(date)
	{
		 if(date==""){
			 vm.rangeOptions = {
		        //format: "YYYY-MM-DD",
		    	startDate: new Date(vm.inoutQuery.inoutTimeStartQr.value),
		    	minDate:new Date(new Date(vm.inoutQuery.inoutTimeEndQr.value).setFullYear(new Date(vm.inoutQuery.inoutTimeEndQr.value).getFullYear()-5))
			 };
			 vm.time= {
		       start: vm.rangeOptions.startDate,
				 end: vm.rangeOptions.minDate
			 } 
		 }
	 };
	 
	 vm.timeTwo = {
		        start: {},
		        end: {}
		    };
	 vm.initDateTwo=function(date)
		{
			 if(date==""){
				 vm.rangeOptionsTwo = {
			        //format: "YYYY-MM-DD",
			    	startDate: new Date(vm.inoutQuery.inoutTimeStartQr.value),
			    	minDate:new Date(new Date(vm.inoutQuery.inoutTimeEndQr.value).setFullYear(new Date(vm.inoutQuery.inoutTimeEndQr.value).getFullYear()-5))
				 };
				 vm.timeTwo= {
			         start: vm.rangeOptionsTwo.startDate,
					 end: vm.rangeOptionsTwo.minDate
				 } 
			 }
		 };
		 
   vm.rangeOptionsTwo = {
	        //format: "YYYY-MM-DD",
//			    	startDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5))),
	    	startDate:new Date((new Date).getFullYear(), (new Date).getMonth(), 1),
	        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 6)))
	    };

    vm.timeThree = {
        start: {},
        end: {}
    };
    vm.initDateThree=function(date)
    {
        if(date==""){
            vm.rangeOptionsThree = {
                //format: "YYYY-MM-DD",
                startDate: new Date(vm.inoutQuery.inoutTimeStartQr.value),
                minDate:new Date(new Date(vm.inoutQuery.inoutTimeEndQr.value).setFullYear(new Date(vm.inoutQuery.inoutTimeEndQr.value).getFullYear()-5))
            };
            vm.timeThree= {
                start: vm.rangeOptionsThree.startDate,
                end: vm.rangeOptionsThree.minDate
            }
        }
    };

    vm.rangeOptionsThree = {
        //format: "YYYY-MM-DD",
//			    	startDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5))),
        startDate:new Date((new Date).getFullYear(), (new Date).getMonth(), 1),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 6)))
    };
	//采购
    vm.purchase={};
    // 仓库列表
    vm.storehouseList = [];
    vm.materialTypeList = [];
    vm.materialTypeListTwo = [];

    // tabs控制
    vm.tabs = {
    	placeInfo: {active: true},
    	placeReport: {active: false},
        info: {active: false},
        inout: {active: false},
        inoutDetail: {active: false, show: false},
        inoutRushRed: {active: false, show: false},
        comm: {active: false, show: false},
        commEditForm: {active: false, show: false}
    };

	 // 显示出入库
    vm.showInout = function () {
        vm.tabs.inout.active = true;
    };
        
    // 查看详情
    vm.showInoutDetail = function () {
        vm.tabs.inoutDetail.show = true;
        vm.tabs.inoutDetail.active = true;
    };

    // 隐藏查看详情
    vm.hideInoutDetail = function () {
        vm.tabs.inoutDetail.show = false;
        $timeout(function () {
            vm.tabs.inout.active = true;
        });
    };

    // 设置冲红的域
    vm.rushRedFormScope = null;
    vm.setRushRedFormScope = function (scope) {
        vm.rushRedFormScope = scope;
    };

    // 显示冲红
    vm.showRushRed = function (index) {
        vm.tabs.inoutRushRed.show = true;
        vm.tabs.inoutRushRed.active = true;
    };

    //隐藏冲红
    vm.hideRushRed = function () {
        vm.tabs.inoutRushRed.show = false;

        $timeout(function () {
            vm.tabs.inout.active = true;
        });
    };

    // 显示常用料编辑
    vm.showCommonMaterial = function () {
        vm.tabs.commEditForm.show = true;
        vm.tabs.commEditForm.active = true;
    };

    //隐藏常用料编辑
    vm.hideCommonMaterial = function () {
        vm.tabs.commEditForm.show = false;
        $timeout(function () {
            vm.tabs.comm.active = true;
        });
    };


    // 默认排序按时间排序排序处理
    vm.sort = {};
    vm.sort.no1 = {both: true, desc: false, asc: false};
    vm.sort.name1 = {both: true, desc: false, asc: false};
    vm.sort.specification1 = {both: true, desc: false, asc: false};
    vm.sort.stocks1 = {both: true, desc: false, asc: false};
    vm.sort.leadTime1 = {both: true, desc: false, asc: false};

    vm.sort.no2 = {both: true, desc: false, asc: false};
    vm.sort.name2 = {both: true, desc: false, asc: false};
    vm.sort.specification2 = {both: true, desc: false, asc: false};
    vm.sort.stocks2 = {both: true, desc: false, asc: false};
    vm.sort.leadTime2 = {both: true, desc: false, asc: false};
    
    vm.sort.supplierNo3 = {both: true, desc: false, asc: false};
    vm.sort.supplierName3 = {both: true, desc: false, asc: false};
    vm.sort.materialNo3 = {both: true, desc: false, asc: false};
    vm.sort.operateDate3 = {both: true, desc: false, asc: false};
    vm.sort.status3 = {both: true, desc: false, asc: false};
    vm.sort.quantity3 = {both: true, desc: false, asc: false};

    vm.sortClick = function (col) {
        vm.sort[col].both = false;
        if (!vm.sort[col].desc && !vm.sort[col].asc) {
            vm.sort[col].asc = true;
        } else {
            if (vm.sort[col].asc) {
                vm.sort[col].desc = true;
                vm.sort[col].asc = false;
            } else {
                vm.sort[col].desc = false;
                vm.sort[col].asc = true;
            }
        }
        
        // 将后缀相同的放入arr
        var arr = [];
        for (var p in vm.sort) {
            if (p.charAt(p.length - 1) == col.charAt(col.length - 1)) {
                arr.push(p);
            }
        }
        
        // 将非本排序列的其它列的图标变为上下
        for (var a = 0; a < arr.length; a++) {
            var k = arr[a];
            if (k !== col) {
                vm.sort[k].desc = false;
                vm.sort[k].asc = false;
                vm.sort[k].both = true;
            }
        }
        
        if (col == "supplierNo3") {
            vm.inoutQuery.sort.value = vm.sort[col].asc ? "supl.no ASC" : "supl.no DESC";
        } else if (col == "supplierName3") {
            vm.inoutQuery.sort.value = vm.sort[col].asc ? "supl.shortName ASC" : "supl.shortName DESC";
        } else if (col == "materialNo3") {
            vm.inoutQuery.sort.value = vm.sort[col].asc ? "mat.no ASC" : "mat.no DESC";
        } else if (col == "quantity3" || col == "operateDate3" || col == "status3") {
            vm.inoutQuery.sort.value = vm.sort[col].asc ? col.substring(0, col.length - 1) + " ASC" : col.substring(0, col.length - 1) + " DESC";
        }else if (col.charAt(col.length - 1) == "2") {
            vm.quotationQuery.sort.value = vm.sort[col].asc ? col.substring(0, col.length - 1) + " ASC" : col.substring(0, col.length - 1) + " DESC";
        }else {
        	vm.query.sort.value = vm.sort[col].asc ? col.substring(0, col.length - 1) + " ASC" : col.substring(0, col.length - 1) + " DESC";
        }
        
        if (col.charAt(col.length - 1) == "1") {
            vm.init(vm.page.info.pageNo, vm.page.info.pageSize, vm.page.info.condition, vm.page.info.url, handle.info);
        } else if (col.charAt(col.length - 1) == "2") {
            vm.init(vm.page.infoQuotation.pageNo, vm.page.infoQuotation.pageSize, vm.page.infoQuotation.condition, vm.page.infoQuotation.url, handle.quotation);
        } else {
            vm.init(vm.page.inout.pageNo, vm.page.inout.pageSize, vm.page.inout.condition, vm.page.inout.url, handle.inout);
        }
    };

    // 分页按钮单击处理
    vm.doPage = function (h, page, pageSize) {
        var url = "";
        var condition = [];

        if (h == handle.info) {
            vm.page.info.pageNo = page;
            vm.page.info.pageSize = pageSize;
            url = vm.page.info.url;
            condition = vm.page.info.condition;
        } else if(h == handle.adjust){
        	vm.adjustPage.pageNo = page;
            vm.adjustPage.pageSize = pageSize;
            url = vm.adjustPage.url;
            condition = vm.adjustPage.condition;
        } else if(h == handle.quotation){
        	vm.page.infoQuotation.pageNo = page;
            vm.page.infoQuotation.pageSize = pageSize;
            url = vm.page.infoQuotation.url;
            condition = vm.page.infoQuotation.condition;
        }else if(h == handle.month){
            vm.page.infoMonth.pageNo = page;
            vm.page.infoMonth.pageSize = pageSize;
            url = vm.page.infoMonth.url;
            condition = vm.page.infoMonth.condition;
        }else {
            vm.page.inout.pageNo = page;
            vm.page.inout.pageSize = pageSize;
            url = vm.page.inout.url;
            condition = vm.page.inout.condition;
        }

        vm.init(page, pageSize, condition, url, h);
    };

    // 页面显示数量改变
    vm.pageSizeChange = function (h) {
        var size = 10;
        var condition = [];
        var url = "";

        if (h == handle.info) {
            size = vm.page.info.pageSize;
            url = vm.page.info.url;
            condition = vm.page.info.condition;
        } else if(h == handle.adjust){
        	size = vm.adjustPage.pageSize;
            url = vm.adjustPage.url;
            condition = vm.adjustPage.condition;
        } else if (h == handle.month) {
        	size = vm.page.infoMonth.pageSize;
            url = vm.page.infoMonth.url;
            condition = vm.page.infoMonth.condition;
        }else if (h == handle.quotation) {
            size = vm.page.infoQuotation.pageSize;
            url = vm.page.infoQuotation.url;
            condition = vm.page.infoQuotation.condition;
        }else {
            size = vm.page.inout.pageSize;
            url = vm.page.inout.url;
            condition = vm.page.inout.condition;
        }

        vm.init(1, size, condition, url, h);
    };

    /*
     * 兼容条件查询与普通查询
     * no 当前页
     * size 当前页显示数量
     * condition 是数组，可放入多个条件对象，如{name:"type", value:"1"}  名称对应实体字段名称，值对应页面输入的值
     * url 请求数据链接
     */
    vm.init = function (no, size, condition, url, h) {
        MainCtrl.blockUI({
            animate: true,
        });

        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;

        if (h == handle.info) {
            condition.push({
                name: vm.query.sort.name,
                value: vm.query.sort.value
            });
        }else if(h == handle.quotation) {
            condition.push({
                name: vm.quotationQuery.sort.name,
                value: vm.quotationQuery.sort.value
            });
        }else if(h == handle.adjust){
        	condition.push({
                name: vm.adjustQuery.no.name,
                value: vm.adjustQuery.no.value
            },{
            	name: vm.adjustQuery.name.name,
                value: vm.adjustQuery.name.value
            },{
            	name: vm.adjustQuery.sort.name,
                value: vm.adjustQuery.sort.value
            });
        }else if(h == handle.first) {
            condition.push({
                name: vm.firstInFirstOut.sort.name,
                value: vm.firstInFirstOut.sort.value
            });
        }else if (h== handle.store)
        {
            condition.push({
                name: vm.materialStore.sort.name,
                value: vm.materialStore.sort.value
            })
        } else {
        	condition.push({
                name: vm.inoutQuery.sort.name,
                value: vm.inoutQuery.sort.value
            });
        }

        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condition);

        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};

            // 如果结果为空
            if (angular.isUndefined(result) || angular.isUndefined(result.list)) {
                data.pageNo = 1;
                data.pageSize = 10;
                data.startCount = 0;
                data.endCount = 0;
                data.list = [];
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }
            if(data && data.list && data.list.length > 0)
            {
                for(let obj of data.list)
                {
                    obj.amountTwo = ((obj.daBanQty ? Number(obj.daBanQty) : 0) * (obj.price ? Number(obj.price) : 0)).toFixed(2);
                }
            }
            // 设置返回的请求数据
            switch (h) {
                case handle.info:
                    vm.page.info.data = data;
                    break;
                case handle.month:
                    vm.page.infoMonth.data = data;
                    break;
                case handle.quotation:
                    vm.page.infoQuotation.data = data;
                    break;
                case handle.inout:
                    vm.page.inout.data = data;
                    break;
                case handle.adjust:
                	vm.adjustPage.data = data;
                	break;
                case handle.first:
                    vm.page.firstInFirstOut.data = data;
                    break;
                case handle.store:
                    vm.page.materialStore.data = data;
                    break;
            }

            MainCtrl.unblockUI();
        });
    };

    // 查询数据
    vm.doQuery = function () {
        // 设置查询条件
        var condition = [];
        if (vm.query.storehouse.value !== "") {
            condition.push({
                name: vm.query.storehouse.name,
                value: vm.query.storehouse.value
            });
        }

        if (vm.query.no.value !== "") {
            condition.push({
                name: vm.query.no.name,
                value: vm.query.no.value
            });
        }

        if (vm.query.name.value !== "") {
            condition.push({
                name: vm.query.name.name,
                value: vm.query.name.value
            });
        }
        if (vm.query.specification.value !== "") {
            condition.push({
                name: vm.query.specification.name,
                value: vm.query.specification.value
            });
        }

        if (vm.query.materialType.value !== "") {
            condition.push({
                name: vm.query.materialType.name,
                value: vm.query.materialType.value
            });
        }
        if (vm.query.ststus.value !== "") {
            condition.push({
                name: vm.query.ststus.name,
                value: vm.query.ststus.value
            });
        }
        condition.push({
    		name: vm.query.isIncludeStock.name,
    		value: vm.query.isIncludeStock.value
    	});
        condition.push({
    		name: "craftNoStatus",
    		value: vm.query.craftNoStatus
    	});
        condition.push({
            name: "submittedStatus",
            value: vm.query.submittedStatus
        });

        vm.page.info.pageNo = 1;
        vm.page.info.condition = condition;

        // 查询数据
        vm.init(vm.page.info.pageNo, vm.page.info.pageSize, vm.page.info.condition, vm.page.info.url, handle.info);
    };
    
    vm.editCraft = function (msg) 
    {
    	if(msg.recordId && msg.materialId){
    		MainCtrl.blockUI({
            	animate: true
            });
        	upida.post("stock/rawmaterial/editCraft",msg).then(function (data) {
                // 初始化数据
                if(data && data=="success"){
                	// 提示信息
                }else{
                	vm.message = "修改工艺失败";
                	$('#static').modal();
                }
              //进度条关闭
    			MainCtrl.unblockUI();
            });
    	}else{
    		vm.message = "请刷新重试";
        	$('#static').modal();
    	}
    }
    
    vm.editLeadTime = function (msg) 
    {
    	if(msg.recordId){
    		MainCtrl.blockUI({
            	animate: true
            });
        	upida.post("stock/rawmaterial/editLeadTime",msg).then(function (data) {
                // 初始化数据
                if(data && data=="success"){
                	// 提示信息
                }else{
                	vm.message = "修改备料提前期失败";
                	$('#static').modal();
                }
                if(!msg.interProductStockRecord)
                {
                    msg.interProductStockRecord = {};
                }
                msg.interProductStockRecord.status = 1001;
              //进度条关闭
    			MainCtrl.unblockUI();
            });
    	}else{
    		vm.message = "请刷新重试";
        	$('#static').modal();
    	}
    }

    vm.editPrice = function (msg)
    {
        if(msg.recordId){
            MainCtrl.blockUI({
                animate: true
            });
            upida.post("stock/rawmaterial/editPrice",msg).then(function (data) {
                // 初始化数据
                if(data && data=="success"){
                    // 提示信息
                }else{
                    vm.message = "修改初始价失败";
                    $('#static').modal();
                }
                if(!msg.interProductStockRecord)
                {
                    msg.interProductStockRecord = {};
                }
                msg.interProductStockRecord.status = 1001;
                //进度条关闭
                MainCtrl.unblockUI();
            });
        }else{
            vm.message = "请刷新重试";
            $('#static').modal();
        }
    }
    
    vm.editSupplier = function (msg) 
    {
    	if(msg.recordId){
    	    let madeSupplierName = null;
    	    for(let supplier of vm.supplierList)
    	    {
    	        if(supplier.recordId == msg.manufacturerId)
    	        {
                    madeSupplierName = supplier.itemName;
                    break;
                }
            }
            // 组合物料名字,制造商+覆铜板材+追加项
            if(madeSupplierName){
                msg.name = madeSupplierName;
                if(msg.materialTypeValue){
                    msg.name = msg.name + " " + msg.materialTypeValue;
                }
                if(msg.additionalItems)
                {
                    msg.name = msg.name + " " + msg.additionalItems;
                }
            }else{
                if(msg.materialTypeValue){
                    msg.name = msg.materialTypeValue;
                }
                if(msg.additionalItems)
                {
                    msg.name = msg.name + " " + msg.additionalItems;
                }
            }
    		MainCtrl.blockUI({
            	animate: true
            });
        	upida.post("stock/rawmaterial/editSupplier",msg).then(function (data) {
                // 初始化数据
                if(data && data=="success"){
                	// 提示信息
                }else{
                	vm.message = "修改品牌失败";
                	$('#static').modal();
                }
              //进度条关闭
    			MainCtrl.unblockUI();
            });
    	}else{
    		vm.message = "请刷新重试";
        	$('#static').modal();
    	}
    }
    
    vm.materialPlaceFlag = true;
    
    vm.loadMaterialById = function(row) {
		if(row && row.recordId){
			var material = {};
	    	material.no = row.materialNo;
	    	material.materialKind = 100701;
	    	upida.post("stock/material/addMaterialItems",material).then(function(data){
	    		if(data && data.length > 0)
	    		{
	    			vm.materialQueryList = data;
	        		var obj = {name: '所有', recordId: ''};
	                vm.materialQueryList.splice(0, 0, obj);
	                
	                for(let i=0;i<vm.materialQueryList.length;i++){
	                	if(vm.materialQueryList[i] && vm.materialQueryList[i].recordId == row.recordId){
	                		vm.inoutPlaceQuery.material = vm.materialQueryList[i];
	                		vm.getPlaceDataList();
	                	}
	                }
	    		}
	    	});
		}
	}
    
    vm.materialNo = "";
    vm.materialName = "";
    vm.materialTypeId = "";
    vm.specification = "";
    vm.storeHouseId = "";
    vm.getPlaceDataList = function() {
    	// 设置查询条件
        var condit = [];
        var material = {};//用物料属性查
        var supplier = {};//用客户属性查

        if(vm.inoutPlaceQuery.material && vm.inoutPlaceQuery.material.recordId)
        {
        	vm.materialPlaceFlag = false;
        	material.recordId = vm.inoutPlaceQuery.material.recordId;
            condit.push({
                name: 'material',
                value: material
            });
        }else{
        	vm.materialPlaceFlag = true;
        }
        if(vm.inoutPlaceQuery.materialPlace && vm.inoutPlaceQuery.materialPlace.recordId)
        {
            condit.push({
                name: 'stockPlaceId',
                value: vm.inoutPlaceQuery.materialPlace.recordId
            });
        }
        if(vm.materialNo)
        {
        	condit.push({
                name: 'materialNo',
                value: vm.materialNo
            });
        }
        if(vm.materialName)
        {
        	condit.push({
                name: 'materialName',
                value: vm.materialName
            });
        }
        if(vm.materialTypeId)
        {
        	condit.push({
                name: 'materialTypeId',
                value: vm.materialTypeId
            });
        }
        if(vm.specification)
        {
        	condit.push({
                name: 'specification',
                value: vm.specification
            });
        }
        if(vm.storeHouseId)
        {
        	condit.push({
                name: 'storeHouseId',
                value: vm.storeHouseId
            });
        }
        
        if(vm.branchId)
        {
        	condit.push({
                name: 'branchId',
                value: vm.branchId
            });
        }
        condit.push({
            name: 'haveStockFlag',
            value: vm.haveStockFlag
        });
        if (vm.inoutPlaceQuery.inoutType.value !== "") {
            condit.push({
                name: vm.inoutPlaceQuery.inoutType.name,
                value: vm.inoutPlaceQuery.inoutType.value
            });
        }

        if (vm.inoutPlaceQuery.supplierNo.value !== "") {
            supplier.no = vm.inoutPlaceQuery.supplierNo.value;
        }

        if (vm.inoutPlaceQuery.supplierName.value !== "") {
            supplier.shortName = vm.inoutPlaceQuery.supplierName.value;
        }

        if (supplier != null && (supplier.no || supplier.shortName)) {
            condit.push({
                name: 'supplier',
                value: supplier
            });
        }

        if (vm.inoutPlaceQuery.status.value !== "") {
            condit.push({
                name: vm.inoutPlaceQuery.status.name,
                value: vm.inoutPlaceQuery.status.value
            });
        }

        if (vm.inoutPlaceQuery.storehouse.value !== "") {
            condit.push({
                name: vm.inoutPlaceQuery.storehouse.name,
                value: vm.inoutPlaceQuery.storehouse.value
            });
        }
        
        if(vm.inoutPlaceQuery.craftNo.value !== ""){
        	condit.push({
                name: vm.inoutPlaceQuery.craftNo.name,
                value: vm.inoutPlaceQuery.craftNo.value
            });
        } 
        
        if (vm.time.start) {
        	condit.push({
                name: vm.inoutPlaceQuery.inoutTimeStartQr.name,
                value: vm.time.start.valueOf()
            });
        	vm.inoutPlaceQuery.inoutTimeStartQr.value = vm.time.start.valueOf();
        }
        if (vm.time.end) {
        	condit.push({
                name: vm.inoutPlaceQuery.inoutTimeEndQr.name,
                value: vm.time.end.valueOf()
            });
        	vm.inoutPlaceQuery.inoutTimeEndQr.value = vm.time.end.valueOf();
        }
        // TJ 2017-08-29 合同编号
        if (vm.contractNo !== "") {
            condit.push({
                name: 'contractNo',
                value: vm.contractNo
            });
        }
        
        if (vm.supplierContractNo !== "") {
            condit.push({
                name: 'supplierContractNo',
                value: vm.supplierContractNo
            });
        }

        vm.page.infoPlace.pageNo = 1;
        vm.page.infoPlace.condition = condit;
        
        vm.initPlaceDate(vm.page.infoPlace.pageNo, vm.page.infoPlace.pageSize, vm.page.infoPlace.condition, vm.page.infoPlace.url);
        vm.loadTotalInfo(vm.page.infoPlace.condition);
	}

    vm.totalInfo = {};
    vm.loadTotalInfo = function(condition)
    {
        // 请求数据
        var param = {};
        // 设置过滤条件
        CommonUtil.setRequestBody(param, condition);
        upida.post("stock/rawmaterial/loadTotalInfo",param).then(function(data){
            vm.totalInfo = data;
        });
    };
    
    vm.sortClickPlace = function (col) {
        vm.sort[col].both = false;
        if (!vm.sort[col].desc && !vm.sort[col].asc) {
            vm.sort[col].asc = true;
        } else {
            if (vm.sort[col].asc) {
                vm.sort[col].desc = true;
                vm.sort[col].asc = false;
            } else {
                vm.sort[col].desc = false;
                vm.sort[col].asc = true;
            }
        }
        
        // 将后缀相同的放入arr
        var arr = [];
        for (var p in vm.sort) {
            if (p.charAt(p.length - 1) == col.charAt(col.length - 1)) {
                arr.push(p);
            }
        }
        
        // 将非本排序列的其它列的图标变为上下
        for (var a = 0; a < arr.length; a++) {
            var k = arr[a];
            if (k !== col) {
                vm.sort[k].desc = false;
                vm.sort[k].asc = false;
                vm.sort[k].both = true;
            }
        }
        
        if (col == "supplierNo3") {
        	vm.inoutPlaceQuery.sort.value = vm.sort[col].asc ? "supl.no ASC" : "supl.no DESC";
        } else if (col == "supplierName3") {
        	vm.inoutPlaceQuery.sort.value = vm.sort[col].asc ? "supl.shortName ASC" : "supl.shortName DESC";
        } else if (col == "materialNo3") {
        	vm.inoutPlaceQuery.sort.value = vm.sort[col].asc ? "mat.no ASC" : "mat.no DESC";
        } else if (col == "quantity3" || col == "operateDate3" || col == "status3") {
        	vm.inoutPlaceQuery.sort.value = vm.sort[col].asc ? col.substring(0, col.length - 1) + " ASC" : col.substring(0, col.length - 1) + " DESC";
        }else {
        	vm.inoutPlaceQuery.sort.value = vm.sort[col].asc ? col.substring(0, col.length - 1) + " ASC" : col.substring(0, col.length - 1) + " DESC";
        }
        
        vm.doPagePlace();
    };
    
    vm.pageSizeChangePlace = function () {
        var size = 10;
        var condition = [];
        var url = "";

        size = vm.page.infoPlace.pageSize;
        url = vm.page.infoPlace.url;
        condition = vm.page.infoPlace.condition;

        vm.initPlaceDate(1, size, condition, url);
    };
    
    vm.doPagePlace = function (page, pageSize) {
        var url = "";
        var condition = [];

        vm.page.infoPlace.pageNo = page;
        vm.page.infoPlace.pageSize = pageSize;
        url = vm.page.infoPlace.url;
        condition = vm.page.infoPlace.condition;
        
        vm.initPlaceDate(page, pageSize, condition, url);
    };
    
    vm.initPlaceDate = function (no, size, condition, url) {
        MainCtrl.blockUI({
            animate: true,
        });

        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;
        
        condition.push({
            name: vm.inoutPlaceQuery.sort.name,
            value: "operateDate ASC"
        });

        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condition);

        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};

            // 如果结果为空
            if (angular.isUndefined(result) || angular.isUndefined(result.list)) {
                data.pageNo = 1;
                data.pageSize = 10;
                data.startCount = 0;
                data.endCount = 0;
                data.list = [];
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
                //
                vm.page.infoPlace.companyNameList = [];
                for(var i = 0 ;i<data.list.length;i++){
                	var purchSpec = data.list[i].purchSpec;
                	data.list[i].purchSpecList = [];
                	if(purchSpec&&purchSpec.length>0){
                		var specArray = purchSpec.split(",");
                		for(var j = 0;j<specArray.length;j++){
                			var specInfos = specArray[j].split(":");
                			if(specInfos.length<2){
                				break;
                			}
                			var companyName = specInfos[0];
                			var value = specInfos[1];
                			data.list[i].purchSpecList.push({"companyName":companyName,"val":value});
                			if(vm.page.infoPlace.companyNameList.length>0){
                				for(var n = 0;n<vm.page.infoPlace.companyNameList.length;n++){
                					if(vm.page.infoPlace.companyNameList[n].companyName == companyName){
                						break;
                					}else{
                						if(n ==(vm.page.infoPlace.companyNameList.length-1)){
                							vm.page.infoPlace.companyNameList.push({"companyName":companyName});
                						}
                					}
                				}
                			}else{
                				vm.page.infoPlace.companyNameList.push({"companyName":companyName});
                			}
                			
                		}
                		
                	}
                }
                
                vm.page.infoPlace.companyNameListTwo = [];
                for(var i = 0 ;i<data.list.length;i++){
                	var purchSpec = data.list[i].stockSpec;
                	data.list[i].purchSpecListTwo = [];
                	if(purchSpec&&purchSpec.length>0){
                		var specArray = purchSpec.split(",");
                		for(var j = 0;j<specArray.length;j++){
                			var specInfos = specArray[j].split(":");
                			if(specInfos.length<2){
                				break;
                			}
                			var companyName = specInfos[0];
                			var value = specInfos[1];
                			data.list[i].purchSpecListTwo.push({"companyName":companyName,"val":value});
                			if(vm.page.infoPlace.companyNameListTwo.length>0){
                				for(var n = 0;n<vm.page.infoPlace.companyNameListTwo.length;n++){
                					if(vm.page.infoPlace.companyNameListTwo[n].companyName == companyName){
                						break;
                					}else{
                						if(n ==(vm.page.infoPlace.companyNameListTwo.length-1)){
                							vm.page.infoPlace.companyNameListTwo.push({"companyName":companyName});
                						}
                					}
                				}
                			}else{
                				vm.page.infoPlace.companyNameListTwo.push({"companyName":companyName});
                			}
                			
                		}
                		
                	}
                }
                //如果都为空，默认显示本厂销售公司
                if(vm.page.infoPlace.companyNameList == 0){
                	vm.page.infoPlace.companyNameList.push({"companyName":vm.defaultComp});
                }
                if(vm.page.infoPlace.companyNameListTwo == 0){
                	vm.page.infoPlace.companyNameListTwo.push({"companyName":vm.defaultComp});
                }
            }

            // 设置返回的请求数据
            vm.page.infoPlace.data = data;

            MainCtrl.unblockUI();
        });
    };
    vm.page.infoPlace.companyNameList = [];
    vm.page.infoPlace.companyNameListTwo = [];
    vm.defaultComp = "";
    vm.productStatus = "";
    // 查询数据
    vm.doInoutQuery = function () {
        // 设置查询条件
        var condit = [];
        var material = {};//用物料属性查
        var supplier = {};//用客户属性查

        if (vm.inoutQuery.no.value !== "") {
            material.no = vm.inoutQuery.no.value;
        }

        if (vm.inoutQuery.name.value !== "") {
            material.name = vm.inoutQuery.name.value;
        }

        if (material != null && (material.no || material.name)) {
            condit.push({
                name: 'material',
                value: material
            });
        }

        if (vm.inoutQuery.inoutType.value !== "") {
            condit.push({
                name: vm.inoutQuery.inoutType.name,
                value: vm.inoutQuery.inoutType.value
            });
        }

        if (vm.inoutQuery.supplierNo.value !== "") {
            supplier.no = vm.inoutQuery.supplierNo.value;
        }

        if (vm.inoutQuery.supplierName.value !== "") {
            supplier.shortName = vm.inoutQuery.supplierName.value;
        }

        if (supplier != null && (supplier.no || supplier.shortName)) {
            condit.push({
                name: 'supplier',
                value: supplier
            });
        }

        if (vm.inoutQuery.status.value !== "") {
            condit.push({
                name: vm.inoutQuery.status.name,
                value: vm.inoutQuery.status.value
            });
        }
        
        if (vm.inoutQuery.statusTwo.value !== "" && (vm.inoutQuery.inoutType.value == 1 || 
        		vm.inoutQuery.inoutType.value == 3 || 
        		vm.inoutQuery.inoutType.value == 5)) {
            condit.push({
                name: vm.inoutQuery.statusTwo.name,
                value: vm.inoutQuery.statusTwo.value
            });
        }

        if (vm.inoutQuery.storehouse.value !== "") {
            condit.push({
                name: vm.inoutQuery.storehouse.name,
                value: vm.inoutQuery.storehouse.value
            });
        }
        
        if(vm.inoutQuery.craftNo.value !== ""){
        	condit.push({
                name: vm.inoutQuery.craftNo.name,
                value: vm.inoutQuery.craftNo.value
            });
        } 
        
        if (vm.time.start) {
        	condit.push({
                name: vm.inoutQuery.inoutTimeStartQr.name,
                value: vm.time.start.valueOf()
            });
        	vm.inoutQuery.inoutTimeStartQr.value = vm.time.start.valueOf();
        }
        if (vm.time.end) {
        	condit.push({
                name: vm.inoutQuery.inoutTimeEndQr.name,
                value: vm.time.end.valueOf()
            });
        	vm.inoutQuery.inoutTimeEndQr.value = vm.time.end.valueOf();
        }
        // TJ 2017-08-29 合同编号
        if (vm.contractNo !== "") {
            condit.push({
                name: 'contractNo',
                value: vm.contractNo
            });
        }
        
        if (vm.supplierContractNo !== "") {
            condit.push({
                name: 'supplierContractNo',
                value: vm.supplierContractNo
            });
        }

        if (vm.queryNo !== "") {
            condit.push({
                name: 'queryNo',
                value: vm.queryNo
            });
        }
        if (vm.remark !== "") {
            condit.push({
                name: 'remark',
                value: vm.QueryRemark
            });
        }
        if (vm.queryPrintNo !== "" && (vm.inoutQuery.inoutType.value == 1 || 
        		vm.inoutQuery.inoutType.value == 3 || 
        		vm.inoutQuery.inoutType.value == 5)) {
            condit.push({
                name: 'queryPrintNo',
                value: vm.queryPrintNo
            });
        }

        if (vm.deliveryNo !== "" && vm.inoutQuery.inoutType.value == 1){
            condit.push({
                name: 'deliveryNo',
                value: vm.deliveryNo
            });
        }

        if (vm.productionStatus !== "" && vm.inoutQuery.inoutType.value == 3){
            condit.push({
                name: 'productionStatus',
                value: vm.productionStatus
            });
        }

        if (vm.productStatus !== "" && (vm.inoutQuery.inoutType.value == 1 || vm.inoutQuery.inoutType.value == 6 || vm.inoutQuery.inoutType.value == "")){
            condit.push({
                name: 'product',
                value: vm.productStatus
            });
        }
        
        vm.page.inout.pageNo = 1;
        vm.page.inout.condition = condit;

        // 查询数据
        vm.init(vm.page.inout.pageNo, vm.page.inout.pageSize, vm.page.inout.condition, vm.page.inout.url, handle.inout);
    };
    
    // 查询数据
    vm.doAdjustQuery = function () {
    	// 设置查询条件
    	var condit = [];
    	vm.adjustPage.pageNo = 1;
    	vm.adjustPage.condition = condit;
    	
    	// 查询数据
    	vm.init(vm.adjustPage.pageNo, vm.adjustPage.pageSize, vm.adjustPage.condition, vm.adjustPage.url, handle.adjust);
    };

    // 原料信息单击查看出入库
    vm.infoClick = function(index){
    	vm.rawmaterial = angular.copy(vm.page.info.data.list[index]);
    	var material = {};
    	if (vm.rawmaterial.no && vm.rawmaterial.no !== "") {
            material.no = vm.rawmaterial.no;
            vm.inoutQuery.no.value = material.no;
            vm.page.inout.condition.push({
                name: 'material',
                value: material
            });
        }
        
    	// 查询数据
        vm.page.inout.pageNo = 1;
        vm.init(vm.page.inout.pageNo, vm.page.inout.pageSize, vm.page.inout.condition, vm.page.inout.url, handle.inout);
        
    	vm.showInout();
    };
    
    // 单击查看出入库明细
    vm.inoutDetailClick = function (index) {
        vm.inoutRecord = angular.copy(vm.page.inout.data.list[index]);
        vm.showInoutDetail();
    };
    
    
    //zjn 2017-07-28 使用余料时点击冲红
    vm.rushRedClickRemain = function (index) {
    	vm.inoutRecord = angular.copy(vm.page.inout.data.list[index]);
    	vm.msg = "";
    	vm.remainLst = [];
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	if(vm.inoutRecord && vm.inoutRecord.feeding)
    	{
    		upida.post("stock/rawmaterial/getRawmaterialByfeedingId",vm.inoutRecord.feeding).then(function (data) 
    		{
    			vm.remainLst=data;
    			for(let remain of vm.remainLst)
    			{
                    if(vm.msg && vm.msg.concat(remain.material.no))
                    {
                        continue;
                    }
                    if(vm.msg == "")
                    {
                        vm.msg = remain.material.no;
                    }
                    else
                    {
                        vm.msg = remain.material.no +","+ vm.msg;
                    }
                }
    			vm.message = "冲红"+vm.msg+"记录";
    			$('#static').modal();
    			vm.rushRedClick(index);
    			MainCtrl.unblockUI();
    			vm.clicks = true;// tj 2017-09-07  用户是否多次点击
    		});
    	}
    	else if(vm.inoutRecord && vm.inoutRecord.feding)
    	{
    		upida.post("stock/rawmaterial/getRawmaterialByfedingId",vm.inoutRecord.feding).then(function (data) 
    	    {
    			vm.remainLst=data;	
    			angular.forEach(vm.remainLst,function(p){
    				if(vm.msg == "")
    				{
    					vm.msg = p.material.no;
    				}
    				else
    				{
    					vm.msg = p.material.no +","+ vm.msg;
    				}
    			});
    			vm.message = "冲红"+vm.msg+"记录";
    			$('#static').modal();
    			vm.rushRedClick(index);
    			MainCtrl.unblockUI();
    	    });
    	}
    	else
    	{
    		vm.rushRedClick(index);
    		MainCtrl.unblockUI();
    	}
    };
    
    //判断是否正在盘点
    vm.isInventory = function (index) 
    {
    	vm.rushRedClickRemain(index);
    }
    // 单击冲红
    vm.rushRedClick = function (index) {
        vm.rushRedFormScope.red_form.$setPristine();
        if((vm.inoutRecord && vm.inoutRecord.feeding) || (vm.inoutRecord && vm.inoutRecord.feding) )
    	{
    		vm.inoutRecord = angular.copy(vm.page.inout.data.list[index]);
    		vm.inoutRecord.material.no = vm.msg;
    		vm.inoutRecord.rushredQuantity = vm.inoutRecord.quantity;
    	}
    	else
    	{
    		vm.inoutRecord = angular.copy(vm.page.inout.data.list[index]);
    		vm.inoutRecord.rushredQuantity = vm.inoutRecord.quantity;
    	}
        vm.showRushRed();
        vm.focus += 1;
    };
    
    
    // 提交冲红
    vm.submitOk = function (form) {
        form.$setDirty();
        if (!form.$valid) {
            return;
        }
       
        vm.msg = vm.inoutRecord.material.no;
        $('#staticRemove').modal();
      
    };

    // 提交冲红
    vm.submitRushRed = function () {
    	
    	if(!vm.inoutRecord.rushredQuantity||vm.inoutRecord.rushredQuantity>vm.inoutRecord.quantity||vm.inoutRecord.rushredQuantity<=0){
    		vm.message = "冲红数量不能大于出入库数量，且必须大于0";
            $('#static').modal();
            return;
    	}
        
    	MainCtrl.blockUI({
            animate: true,
        });

        upida.post("stock/rawmaterial/inout/rushred", vm.inoutRecord).then(function (data) {
            // 初始化第一页，条件为空
            vm.init(vm.page.inout.pageNo, vm.page.inout.pageSize, vm.page.inout.condition, vm.page.inout.url, handle.inout);

            // 初始化数据
            vm.inoutRecord = {};
            vm.hideRushRed();
            vm.message = data;
            $('#static').modal();
        });
    };
    
    vm.changeRushredQuantity = function()
    {
        if (isNaN(parseFloat(vm.inoutRecord.rushredQuantity))) {
            vm.message = "请输入有效数字";
            $('#static').modal();
            return;
        }
    	if(!vm.inoutRecord.rushredQuantity||vm.inoutRecord.rushredQuantity>vm.inoutRecord.quantity||vm.inoutRecord.rushredQuantity<=0){
    		vm.message = "冲红数量不能大于出入库数量，且必须大于0";
            $('#static').modal();
            return;
    	}
    }
    
    //------------------------------------------WC 22018-07-10 物料单价7调整-----------------------------------
    vm.materialPriceAdjust = {};
    vm.adjustPrice = function(row){
    	vm.materialPriceAdjust = {};
    	vm.materialPriceAdjust.material = row;
    	vm.materialPriceAdjust.beforePrice = !row.price ? 0 : row.price;
    	vm.materialPriceAdjust.afterPrice = '';
    	$("#staticAdjust").modal();
    }
    
    vm.isAdjustFlag = function(){
    	if((!vm.materialPriceAdjust.afterPrice) || isNaN(vm.materialPriceAdjust.afterPrice) || 
    			Number(vm.materialPriceAdjust.beforePrice) == Number(vm.materialPriceAdjust.afterPrice) ||
    			(!vm.materialPriceAdjust.remark) || vm.materialPriceAdjust.remark.length > 255){
    		return true;
    	}
    	return false;
    	
    }
    
    vm.doAdjust = function(){
    	MainCtrl.blockUI({
            animate: true,
        });
     	upida.post("stock/rawmaterial/priceadjust",vm.materialPriceAdjust).then(function(data){
     		MainCtrl.unblockUI();
     		vm.adjustFlag = true;
     		vm.materialPriceAdjust = {};
     		vm.init(vm.page.info.pageNo, vm.page.info.pageSize, vm.page.info.condition, vm.page.info.url, handle.info);
     		vm.init(vm.adjustPage.pageNo, vm.adjustPage.pageSize, vm.adjustPage.condition, vm.adjustPage.url, handle.adjust);
     		vm.message = data;
     		$("#static").modal();
     	 });
    }
    
    /*** tj ***/
    // tj 调整数量
    vm.adjustNum = function(row)
    {
    	vm.adjustStocks = {};
    	vm.adjustStocks.material = row;
    	vm.adjustStocks.beforeNum = !row.stocks ? 0 : row.stocks;
    	vm.adjustStocks.quantity = '';
    	$("#staticAdjustNum").modal();
    }
    
    vm.doadjustNum = function()
    {
    	MainCtrl.blockUI({
            animate: true,
        });
    	upida.post("stock/rawmaterial/adjustNum", vm.adjustStocks).then(function(data){
     		MainCtrl.unblockUI();
     		vm.adjustFlag = true;
     		vm.materialPriceAdjust = {};
     		vm.init(vm.page.info.pageNo, vm.page.info.pageSize, vm.page.info.condition, vm.page.info.url, handle.info);
     		vm.init(vm.adjustPage.pageNo, vm.adjustPage.pageSize, vm.adjustPage.condition, vm.adjustPage.url, handle.adjust);
     		vm.message = data;
     		$("#static").modal();
     	 });
    }
    
    vm.isAdjustNumFlag = function(){
    	if((!vm.adjustStocks.quantity) || isNaN(vm.adjustStocks.quantity) ||
    		(!vm.adjustStocks.adjustCause) || vm.adjustStocks.adjustCause.length > 255)
    	{
    		return true;
    	}
    	return false;
    	
    }
    
    vm.queryAll = false;
    vm.materialSpecList = [];
    vm.supplierList = [];
    
    vm.materialQueryList = [];
    vm.placeList = [];
    vm.query.material = {};
    vm.query.materialPlace = {};
    vm.materialQueryListTwo = [];
    
    vm.getInOutDataList = function() {
    	vm.init(vm.page.inout.pageNo, vm.page.inout.pageSize, vm.page.inout.condition, vm.page.inout.url, handle.inout);
	}
    
    vm.getInfoDataList = function() {
        vm.init(vm.page.info.pageNo, vm.page.info.pageSize, vm.page.info.condition, vm.page.info.url, handle.info);
	}
    
    // 加载数据
    function loadData() {
    	 MainCtrl.blockUI({
             animate: true,
         });

    	 vm.queryAll = CommonUtil.dataRangeIsAll("10603", BaseUtil.getMenuList());
    	 
    	//是否有采购管理的菜单权限
     	upida.get("common/rightall?prefix=purch:rawmaterial").then(function(data){
     		 if (data.edit || data.manage) {
  				vm.showRawmaterial = true;
  			}
     	 });
    	 
    	upida.get("stock/rawmaterial/load/data?queryAll="+vm.queryAll).then(function (data) {

            // 加载物料类型
            vm.materialQueryListTwo = data.materialList && data.materialList.length > 0 ? data.materialList.slice() : [];
            vm.materialTypeListTwo = data.materialTypeList && data.materialTypeList.length > 0 ? data.materialTypeList.slice() : [];
            vm.materialTypeList = data.materialTypeList && data.materialTypeList.length > 0 ? [...data.materialTypeList] : [];
            vm.materialTypeList.splice(0, 0, { value: '所有', recordId: '' });
            // 处理页面物料类型筛选方式为板材
            if(!vm.quotationQuery.materialType.value || vm.quotationQuery.materialType.value == '')
            {
                for(let materialType of vm.materialTypeList)
                {
                    if(materialType.value == '板材')
                    {
                        vm.quotationQuery.materialType.value = materialType.recordId;
                        break;
                    }
                }
            }
            
            vm.materialSpecList = data.materialSpecList;
            vm.supplierList = data.supplierList;
            
            //加载默认仓库
            vm.storehouseList = data.storehouseList && data.storehouseList.length > 0 ? data.storehouseList : [];
            vm.storehouseList.splice(0, 0, vm.object);
            vm.query.storehouse.value = vm.storehouseList[0];
            vm.inoutQuery.storehouse.value = vm.storehouseList[0];
            
            //加载默认公司
            vm.branchList = data.branchList && data.branchList.length > 0 ? data.branchList : [];
            var branchObj = {'shortName': '所有', 'recordId': ''};
            vm.branchList.splice(0, 0, branchObj);
            for(var i = 0;i<vm.branchList.length;i++){
            	if(vm.branchList[i].companyid&&vm.branchList[i].companyid == vm.branchList[i].bindErpComId){
            		vm.defaultComp = vm.branchList[i].shortName;
            	}
            }
            
            var obj = {name: '所有', recordId: ''};
            vm.materialQueryList = data.materialList;
            if(vm.materialQueryList && vm.materialQueryList.length > 0)
            {
            	vm.materialQueryList.splice(0, 0, obj);
            }
            
            vm.placeList = data.placeList;
            if(vm.placeList && vm.placeList.length > 0)
            {
            	vm.placeList.splice(0, 0, obj);
            }

            // 加载权限
            //权限 stock:rawMaterial
            vm.right.view = data.view;
            vm.right.edit = data.edit;
            vm.right.manage = data.manage;

            //原料查看的初始查询页
            vm.page.info.pageNo = 1;
            vm.page.info.condition.push({
        		name: vm.query.isIncludeStock.name,
        		value: vm.query.isIncludeStock.value
        	});
            vm.page.info.condition.push({
        		name: vm.query.ststus.name,
        		value: vm.query.ststus.value
        	});
            
            //原料出入库的初始查询页
            vm.page.inout.pageNo = 1;
            if (vm.time.start) {
            	vm.page.inout.condition.push({
                    name: vm.inoutQuery.inoutTimeStartQr.name,
                    value: vm.time.start.valueOf()
                });
            	vm.inoutQuery.inoutTimeStartQr.value = vm.time.start.valueOf();
            }
            if (vm.time.end) {
            	vm.page.inout.condition.push({
                    name: vm.inoutQuery.inoutTimeEndQr.name,
                    value: vm.time.end.valueOf()
                });
            	 vm.inoutQuery.inoutTimeEndQr.value = vm.time.end.valueOf();
            }

            // 加载系统设置中物料价格维护授权开关值
            vm.materialPriceSwitchValue = data.materialPriceSwitchValue;
            // 加载系统设置中物料交期维护授权开关值
            vm.materialDeliveryTimeSwitchValue = data.materialDeliveryTimeSwitchValue;
            vm.userList = data.userList;
            vm.getPlaceDataList();
        });
    }
    
    vm.fiterMaterialPlace = function()
    {
    	var storeHouse = vm.inoutPlaceQuery.storehouse.value;
    	vm.inoutPlaceQuery.materialPlace = {};
    	vm.placeList = [];
    	MainCtrl.blockUI({
    		animate: true,
    	});
    	upida.post("stock/material/fiterMaterialPlace",storeHouse).then(function(data){
    		if(data && data.length > 0)
    		{
    			vm.placeList = data;
                var obj = {name: '所有', recordId: ''};
                vm.placeList.splice(0, 0, obj);
    			vm.inoutPlaceQuery.materialPlace = vm.placeList[0];
    		}
    		MainCtrl.unblockUI();
    	});
    }
    
    vm.addMaterialItems = function(no)
    {
    	var material = {};
    	material.no = no;
    	material.materialKind = 100701;
    	upida.post("stock/material/addMaterialItems",material).then(function(data){
    		if(data && data.length > 0)
    		{
    			vm.materialQueryList = data;
        		var obj = {name: '所有', recordId: ''};
                vm.materialQueryList.splice(0, 0, obj);
    		}
    	});
    }
    
    vm.fomatFloat = function(num)
    {
    	var n = 2
    	var f = parseFloat(num);
	    if(!num || isNaN(f)){
	        return null;
	    }   
	    f = Math.round(num*Math.pow(10, n))/Math.pow(10, n); // n 幂   
	    var s = f.toString();
	    return s;
    }
    
    vm.templateNameStr = "";
	vm.userNamesStr = "";
	vm.printTemList = [];
	vm.custTemplate = {};
	vm.tempalteFlag = null;
	
	// type:采购入库(6)投料出库(7)补料出库(8)
	vm.managePrintTem = function(type) {
		vm.printTemList = [];
		vm.custTemplate = {};
		vm.tempalteFlag = type;
		vm.custTemplate.tempalteFlag = type;
		MainCtrl.blockUI({
			animate: true,
		});
		upida.post("crm/getPrintTemplateTwo",vm.custTemplate).then(function(data){
			vm.printTemList = data;
			vm.showPrintTemList = data;
			$('#managePrintTem').modal();
			MainCtrl.unblockUI();
		});
	};
    
	vm.headPrintShowList = [];
	vm.custPrintShowList = [];
	vm.bodyPrintShowList = [];
	vm.bottomPrintShowList = [];
	vm.manageTemp = function(index) {
		vm.headPrintShowList = [];
		vm.custPrintShowList = [];
		vm.bodyPrintShowList = [];
		vm.bottomPrintShowList = [];
		vm.custTemplate = {};
		vm.custTemplate.tempalteFlag = vm.tempalteFlag;
		vm.resetShowPrint();
		// 打开弹窗管理
		$('#custTemplate').modal();
	};
	
	vm.bodyPrintList = [];
	vm.resetShowPrint = function() {
		vm.headPrintShowList = [];
		vm.custPrintShowList = [];
		vm.bottomPrintShowList = [];
		// 头部标签
		if(!vm.custTemplate.headLogoFlag){
			vm.headPrintShowList.push({"name":"公司LOGO","status":"headLogoFlag","value":""});
		}
		if(!vm.custTemplate.headNameFlag){
			vm.headPrintShowList.push({"name":"公司全称","status":"headNameFlag","value":""});
		}
		if(!vm.custTemplate.headShortNameFlag){
			vm.headPrintShowList.push({"name":"公司简称","status":"headShortNameFlag","value":""});
		}
		if(!vm.custTemplate.headNameEnFlag){
			vm.headPrintShowList.push({"name":"公司英文名称","status":"headNameEnFlag","value":""});
		}
		if(!vm.custTemplate.headAddressFlag){
			vm.headPrintShowList.push({"name":"公司地址","status":"headAddressFlag","value":""});
		}
		if(!vm.custTemplate.headPhoneFlag){
			vm.headPrintShowList.push({"name":"公司电话号码","status":"headPhoneFlag","value":""});
		}
		if(!vm.custTemplate.headFaxFlag){
			vm.headPrintShowList.push({"name":"公司传真","status":"headFaxFlag","value":""});
		}
		if(!vm.custTemplate.headWebEmailFlag){
			vm.headPrintShowList.push({"name":"网址、邮箱","status":"headWebEmailFlag","value":""});
		}
		switch (vm.custTemplate.tempalteFlag) {
			// 采购入库
			case 6:
				// 客户标签
				// 货单位
				if(!vm.custTemplate.custNameFlag){
					vm.custPrintShowList.push({"name":"货单位","status":"custNameFlag","value":"","otherName":""});
				}
				// 日期
				if(!vm.custTemplate.dateFlag){
					vm.custPrintShowList.push({"name":"日期","status":"dateFlag","value":"","otherName":""});
				}
				// 底部标签
				// 保管
				if(!vm.custTemplate.bottomOneFlag){
					vm.bottomPrintShowList.push({"name":"保管","status":"bottomOneFlag","value":""});
				}
				// 经手人
				if(!vm.custTemplate.bottomTwoFlag){
					vm.bottomPrintShowList.push({"name":"经手人","status":"bottomTwoFlag","value":""});
				}
				// 财务
				if(!vm.custTemplate.bottomThreeFlag){
					vm.bottomPrintShowList.push({"name":"财务","status":"bottomThreeFlag","value":""});
				}
				break;
			// 投料出库
			case 7:
				// 客户标签
				// 领料单部门
				if(!vm.custTemplate.custNameFlag){
					vm.custPrintShowList.push({"name":"领料单部门","status":"custNameFlag","value":"","otherName":""});
				}
				// 领料单号
				if(!vm.custTemplate.noFlag){
					vm.custPrintShowList.push({"name":"领料单号","status":"noFlag","value":"","otherName":""});
				}
				// 日期
				if(!vm.custTemplate.dateFlag){
					vm.custPrintShowList.push({"name":"日期","status":"dateFlag","value":"","otherName":""});
				}
				// 底部标签
				// 领料人
				if(!vm.custTemplate.bottomOneFlag){
					vm.bottomPrintShowList.push({"name":"领料人","status":"bottomOneFlag","value":""});
				}
				// 主管审核
				if(!vm.custTemplate.bottomTwoFlag){
					vm.bottomPrintShowList.push({"name":"主管审核","status":"bottomTwoFlag","value":""});
				}
				// 仓库
				if(!vm.custTemplate.bottomThreeFlag){
					vm.bottomPrintShowList.push({"name":"仓库","status":"bottomThreeFlag","value":""});
				}
				break;
			// 补料出库
			case 8:
				// 客户标签
				// 领料单部门
				if(!vm.custTemplate.custNameFlag){
					vm.custPrintShowList.push({"name":"领料单部门","status":"custNameFlag","value":"","otherName":""});
				}
				// 领料单号
				if(!vm.custTemplate.noFlag){
					vm.custPrintShowList.push({"name":"领料单号","status":"noFlag","value":"","otherName":""});
				}
				// 日期
				if(!vm.custTemplate.dateFlag){
					vm.custPrintShowList.push({"name":"日期","status":"dateFlag","value":"","otherName":""});
				}
				// 底部标签
				// 领料人
				if(!vm.custTemplate.bottomOneFlag){
					vm.bottomPrintShowList.push({"name":"领料人","status":"bottomOneFlag","value":""});
				}
				// 主管审核
				if(!vm.custTemplate.bottomTwoFlag){
					vm.bottomPrintShowList.push({"name":"主管审核","status":"bottomTwoFlag","value":""});
				}
				// 仓库
				if(!vm.custTemplate.bottomThreeFlag){
					vm.bottomPrintShowList.push({"name":"仓库","status":"bottomThreeFlag","value":""});
				}
				break;
		}
		// 表体标签
		vm.resetBodyList();
	};
	
	vm.resetBodyList = function() {
		vm.bodyPrintList = [];
		vm.bodyPrintShowList = [];
		let sortNum = 0;
		switch (vm.custTemplate.tempalteFlag) {
		// 采购入库
		case 6:
			// 物料编号
			if(!vm.custTemplate.deailNoFlag){
				vm.bodyPrintShowList.push({"name":"物料编号","status":"deailNoFlag","value":"","otherName":""});
			}
			// 名称
			if(!vm.custTemplate.deailCustNoFlag){
				vm.bodyPrintShowList.push({"name":"名称","status":"deailCustNoFlag","value":"","otherName":""});
			}
			// 规格/型号
			if(!vm.custTemplate.craftNoFlag){
				vm.bodyPrintShowList.push({"name":"规格/型号","status":"craftNoFlag","value":"","otherName":""});
			}
			// 单位
			if(!vm.custTemplate.deailMaterNoFlag){
				vm.bodyPrintShowList.push({"name":"单位","status":"deailMaterNoFlag","value":"","otherName":""});
			}
			// 数量
			if(!vm.custTemplate.custModalFlag){
				vm.bodyPrintShowList.push({"name":"数量","status":"custModalFlag","value":"","otherName":""});
			}
			// 单价
			if(!vm.custTemplate.qualitityFlag){
				vm.bodyPrintShowList.push({"name":"单价","status":"qualitityFlag","value":"","otherName":""});
			}
            // 原单价
            if(!vm.custTemplate.rawQualitityNameFlag){
                vm.bodyPrintShowList.push({"name":"原单价","status":"rawQualitityNameFlag","value":"","otherName":""});
            }
			// 金额
			if(!vm.custTemplate.doonateFlag){
				vm.bodyPrintShowList.push({"name":"金额","status":"doonateFlag","value":"","otherName":""});
			}
			// 备注
			if(!vm.custTemplate.remarkFlag){
				vm.bodyPrintShowList.push({"name":"备注","status":"remarkFlag","value":"","otherName":""});
			}
            // 送货单号
            if(!vm.custTemplate.deailProductNameFlag){
                vm.bodyPrintShowList.push({"name":"送货单号","status":"deailProductNameFlag","value":"","otherName":""});
            }
			break;
		// 投料出库
		case 7:
			// 投料单编号
			if(!vm.custTemplate.deailNoFlag){
				vm.bodyPrintShowList.push({"name":"投料单编号","status":"deailNoFlag","value":"","otherName":""});
			}
			// 生产编号
			if(!vm.custTemplate.deailCustNoFlag){
				vm.bodyPrintShowList.push({"name":"生产编号","status":"deailCustNoFlag","value":"","otherName":""});
			}
			// 板厚
			if(!vm.custTemplate.craftNoFlag){
				vm.bodyPrintShowList.push({"name":"板厚","status":"craftNoFlag","value":"","otherName":""});
			}
			// 板料规格/型号
			if(!vm.custTemplate.deailMaterNoFlag){
				vm.bodyPrintShowList.push({"name":"板料规格/型号","status":"deailMaterNoFlag","value":"","otherName":""});
			}
			// 数量/单位
			if(!vm.custTemplate.custModalFlag){
				vm.bodyPrintShowList.push({"name":"数量/单位","status":"custModalFlag","value":"","otherName":""});
			}
			// 导热
			if(!vm.custTemplate.qualitityFlag){
				vm.bodyPrintShowList.push({"name":"导热","status":"qualitityFlag","value":"","otherName":""});
			}
			// 供应商
			if(!vm.custTemplate.doonateFlag){
				vm.bodyPrintShowList.push({"name":"供应商","status":"doonateFlag","value":"","otherName":""});
			}
			// 物料编号
			if(!vm.custTemplate.remarkFlag){
				vm.bodyPrintShowList.push({"name":"物料编号","status":"remarkFlag","value":"","otherName":""});
			}
			// 投料pcs
			if(!vm.custTemplate.deliverySizeFlag){
				vm.bodyPrintShowList.push({"name":"投料pcs","status":"deliverySizeFlag","value":"","otherName":""});
			}
            // 送货单号
            if(!vm.custTemplate.deailProductNameFlag){
                vm.bodyPrintShowList.push({"name":"送货单号","status":"deailProductNameFlag","value":"","otherName":""});
            }
			break;
		// 补料出库
		case 8:
			// 补料单编号
			if(!vm.custTemplate.deailNoFlag){
				vm.bodyPrintShowList.push({"name":"补料单编号","status":"deailNoFlag","value":"","otherName":""});
			}
			// 生产编号
			if(!vm.custTemplate.deailCustNoFlag){
				vm.bodyPrintShowList.push({"name":"生产编号","status":"deailCustNoFlag","value":"","otherName":""});
			}
			// 板厚
			if(!vm.custTemplate.craftNoFlag){
				vm.bodyPrintShowList.push({"name":"板厚","status":"craftNoFlag","value":"","otherName":""});
			}
			// 板料规格/型号
			if(!vm.custTemplate.deailMaterNoFlag){
				vm.bodyPrintShowList.push({"name":"板料规格/型号","status":"deailMaterNoFlag","value":"","otherName":""});
			}
			// 数量/单位
			if(!vm.custTemplate.custModalFlag){
				vm.bodyPrintShowList.push({"name":"数量/单位","status":"custModalFlag","value":"","otherName":""});
			}
			// 导热
			if(!vm.custTemplate.qualitityFlag){
				vm.bodyPrintShowList.push({"name":"导热","status":"qualitityFlag","value":"","otherName":""});
			}
			// 供应商
			if(!vm.custTemplate.doonateFlag){
				vm.bodyPrintShowList.push({"name":"供应商","status":"doonateFlag","value":"","otherName":""});
			}
			// 物料编号
			if(!vm.custTemplate.remarkFlag){
				vm.bodyPrintShowList.push({"name":"物料编号","status":"remarkFlag","value":"","otherName":""});
			}
			// 补料pcs
			if(!vm.custTemplate.deliverySizeFlag){
				vm.bodyPrintShowList.push({"name":"投料pcs","status":"deliverySizeFlag","value":"","otherName":""});
			}
			break;
		}
		vm.resetBodySortNumList();
	};

	vm.resetBodySortNumList = function() {
		vm.bodyPrintList = [];
		if(vm.custTemplate.deailNoFlag && vm.custTemplate.deailNoFlag == 1){
			var print = {};
			print.name = "deailNoFlag";
			print.sortNum = vm.custTemplate.deailNoSortNum;
			vm.bodyPrintList.push(print);
		}
		if(vm.custTemplate.deailCustNoFlag && vm.custTemplate.deailCustNoFlag == 1){
			var print = {};
			print.name = "deailCustNoFlag";
			print.sortNum = vm.custTemplate.deailCustSortNum;
			vm.bodyPrintList.push(print);
		}
		if(vm.custTemplate.craftNoFlag && vm.custTemplate.craftNoFlag == 1){
			var print = {};
			print.name = "craftNoFlag";
			print.sortNum = vm.custTemplate.craftNoSortNum;
			vm.bodyPrintList.push(print);
		}
		if(vm.custTemplate.deailMaterNoFlag && vm.custTemplate.deailMaterNoFlag == 1){
			var print = {};
			print.name = "deailMaterNoFlag";
			print.sortNum = vm.custTemplate.deailMaterNoSortNum;
			vm.bodyPrintList.push(print);
		}
		if(vm.custTemplate.custModalFlag && vm.custTemplate.custModalFlag == 1){
			var print = {};
			print.name = "custModalFlag";
			print.sortNum = vm.custTemplate.custModalSortNum;
			vm.bodyPrintList.push(print);
		}
		if(vm.custTemplate.qualitityFlag && vm.custTemplate.qualitityFlag == 1){
			var print = {};
			print.name = "qualitityFlag";
			print.sortNum = vm.custTemplate.qualititySortNum;
			vm.bodyPrintList.push(print);
		}
        if(vm.custTemplate.rawQualitityNameFlag && vm.custTemplate.rawQualitityNameFlag == 1){
            var print = {};
            print.name = "rawQualitityNameFlag";
            print.sortNum = vm.custTemplate.rawQualitityNameSortNum;
            vm.bodyPrintList.push(print);
        }
		if(vm.custTemplate.doonateFlag && vm.custTemplate.doonateFlag == 1){
			var print = {};
			print.name = "doonateFlag";
			print.sortNum = vm.custTemplate.doonateSortNum;
			vm.bodyPrintList.push(print);
		}
		if(vm.custTemplate.remarkFlag && vm.custTemplate.remarkFlag == 1){
			var print = {};
			print.name = "remarkFlag";
			print.sortNum = vm.custTemplate.remarkSortNum;
			vm.bodyPrintList.push(print);
		}
		if(vm.custTemplate.deliverySizeFlag && vm.custTemplate.deliverySizeFlag == 1){
			var print = {};
			print.name = "deliverySizeFlag";
			print.sortNum = vm.custTemplate.deliverySizeSortNum;
			vm.bodyPrintList.push(print);
		}
		if(vm.custTemplate.deailDateFlag && vm.custTemplate.deailDateFlag == 1){
			var print = {};
			print.name = "deailDateFlag";
			print.sortNum = vm.custTemplate.deailDateSortNum;
			vm.bodyPrintList.push(print);
		}
        if(vm.custTemplate.deailProductNameFlag && vm.custTemplate.deailProductNameFlag == 1){
            var print = {};
            print.name = "deailProductNameFlag";
            print.sortNum = vm.custTemplate.deailProductNameSortNum;
            vm.bodyPrintList.push(print);
        }
		if(vm.bodyPrintList && vm.bodyPrintList.length  > 0){
			vm.bodyPrintList.sort(function(a,b){
				if(Number(a.sortNum) < Number(b.sortNum)){
					return -1;
			    }else if(Number(a.sortNum) > Number(b.sortNum)){
			        return 1;
			    }
			    return 0;
			});
			vm.custTemplate.deailNoSortNum = "";
			vm.custTemplate.deailCustSortNum = "";
			vm.custTemplate.deailMaterNoSortNum = "";
			vm.custTemplate.custModalSortNum = "";
			vm.custTemplate.qualititySortNum = "";
			vm.custTemplate.doonateSortNum = "";
			vm.custTemplate.remarkSortNum = "";
			vm.custTemplate.deliverySizeSortNum = "";
			vm.custTemplate.deliverySizeSortNum = "";
			vm.custTemplate.deailDateSortNum = "";
            vm.custTemplate.deailProductNameSortNum = "";
            vm.custTemplate.rawQualitityNameSortNum = "";
			for(let i=0;i<vm.bodyPrintList.length;i++){
				var row = vm.bodyPrintList[i].name;
				if(row　==　"deailNoFlag"){
					vm.custTemplate.deailNoSortNum = i + 1;
					continue;
				}
				if(row　==　"deailCustNoFlag"){
					vm.custTemplate.deailCustSortNum = i + 1;
					continue;
				}
				if(row　==　"craftNoFlag"){
					vm.custTemplate.craftNoSortNum = i + 1;
					continue;
				}
				if(row　==　"deailMaterNoFlag"){
					vm.custTemplate.deailMaterNoSortNum = i + 1;
					continue;
				}
				if(row　==　"custModalFlag"){
					vm.custTemplate.custModalSortNum = i + 1;
					continue;
				}
				if(row　==　"qualitityFlag"){
					vm.custTemplate.qualititySortNum = i + 1;
					continue;
				}
				if(row　==　"doonateFlag"){
					vm.custTemplate.doonateSortNum = i + 1;
					continue;
				}
				if(row　==　"remarkFlag"){
					vm.custTemplate.remarkSortNum = i + 1;
					continue;
				}
				if(row　==　"deliverySizeFlag"){
					vm.custTemplate.deliverySizeSortNum = i + 1;
					continue;
				}
				if(row　==　"deailDateFlag"){
					vm.custTemplate.deailDateSortNum = i + 1;
					continue;
				}
                if(row　==　"deailProductNameFlag"){
                    vm.custTemplate.deailProductNameSortNum = i + 1;
                    continue;
                }
                if(row　==　"rawQualitityNameFlag"){
                    vm.custTemplate.rawQualitityNameSortNum = i + 1;
                    continue;
                }
			}
		}
	};
	
	vm.delPrintTag = function(row) {
		if(row　==　"headLogoFlag"){
			vm.custTemplate.headLogoFlag = "";
		}
		if(row　==　"headNameFlag"){
			vm.custTemplate.headNameFlag = "";
		}
		if(row　==　"headShortNameFlag"){
			vm.custTemplate.headShortNameFlag = "";
		}
		if(row　==　"headNameEnFlag"){
			vm.custTemplate.headNameEnFlag = "";
		}
		if(row　==　"headAddressFlag"){
			vm.custTemplate.headAddressFlag = "";
		}
		if(row　==　"headPhoneFlag"){
			vm.custTemplate.headPhoneFlag = "";
		}
		if(row　==　"headFaxFlag"){
			vm.custTemplate.headFaxFlag = "";
		}
		if(row　==　"headWebEmailFlag"){
			vm.custTemplate.headWebEmailFlag = "";
		}
		if(row　==　"custNameFlag"){
			vm.custTemplate.custNameFlag = "";
		}
		if(row　==　"noFlag"){
			vm.custTemplate.noFlag = "";
		}
		if(row　==　"linkManFlag"){
			vm.custTemplate.linkManFlag = "";
		}
		if(row　==　"linkPhoneFlag"){
			vm.custTemplate.linkPhoneFlag = "";
		}
		if(row　==　"dateFlag"){
			vm.custTemplate.dateFlag = "";
		}
		if(row　==　"custAddFlag"){
			vm.custTemplate.custAddFlag = "";
		}
		if(row　==　"deailNoFlag"){
			vm.custTemplate.deailNoFlag = "";
			vm.resetBodySortNumList();
		}
		if(row　==　"deailCustNoFlag"){
			vm.custTemplate.deailCustNoFlag = "";
			vm.resetBodySortNumList();
		}
		if(row　==　"craftNoFlag"){
			vm.custTemplate.craftNoFlag = "";
			vm.resetBodySortNumList();
		}
		if(row　==　"deailMaterNoFlag"){
			vm.custTemplate.deailMaterNoFlag = "";
			vm.resetBodySortNumList();
		}
		if(row　==　"custModalFlag"){
			vm.custTemplate.custModalFlag = "";
			vm.resetBodySortNumList();
		}
		if(row　==　"qualitityFlag"){
			vm.custTemplate.qualitityFlag = "";
			vm.resetBodySortNumList();
		}
		if(row　==　"doonateFlag"){
			vm.custTemplate.doonateFlag = "";
			vm.resetBodySortNumList();
		}
		if(row　==　"remarkFlag"){
			vm.custTemplate.remarkFlag = "";
			vm.resetBodySortNumList();
		}
		if(row　==　"deliverySizeFlag"){
			vm.custTemplate.deliverySizeFlag = "";
			vm.resetBodySortNumList();
		}
		if(row　==　"deailDateFlag"){
			vm.custTemplate.deailDateFlag = "";
			vm.resetBodySortNumList();
		}
        if(row　==　"deailProductNameFlag"){
            vm.custTemplate.deailProductNameFlag = "";
            vm.resetBodySortNumList();
        }
		if(row　==　"bottomOneFlag"){
			vm.custTemplate.bottomOneFlag = "";
		}
		if(row　==　"bottomTwoFlag"){
			vm.custTemplate.bottomTwoFlag = "";
		}
        if(row　==　"rawQualitityNameFlag"){
            vm.custTemplate.rawQualitityNameFlag = "";
            vm.resetBodySortNumList();
        }
		vm.resetShowPrint();
	};
	
	vm.savePrintTemplate = function() {
		if(!(vm.custTemplate && vm.custTemplate.templateName)){
			vm.message = "请填写模板名字！";
			$('#static').modal();
			return;
		}
		MainCtrl.blockUI({
			animate: true,
		});
		upida.post("crm/savePrintSet",vm.custTemplate).then(function(data){
			alert("保存成功！");
			$('#custTemplate').modal('hide');
			upida.post("crm/getPrintTemplateTwo",vm.custTemplate).then(function(data){
				$('#managePrintTem').modal('hide');
				vm.printTemList = data;
				vm.showPrintTemList = data;
				MainCtrl.unblockUI();
			});
		});
	};
	
	vm.addPrintTag = function() {
		if(!vm.custTemplate) {
			vm.custTemplate = {};
		}
		if(vm.headPrintTag && vm.headPrintTag.name) {
			vm.printTag = vm.headPrintTag;
		}
		if(vm.custPrintTag && vm.custPrintTag.name) {
			vm.printTag = vm.custPrintTag;	
		}
		if(vm.bodyPrintTag && vm.bodyPrintTag.name) {
			vm.printTag = vm.bodyPrintTag;
		}
		if(vm.bottomPrintTag && vm.bottomPrintTag.name) {
			vm.printTag = vm.bottomPrintTag;
		}
		if(vm.printTag &&　vm.printTag.status){
			if(vm.printTag.status　==　"headLogoFlag"){
				vm.custTemplate.headLogoFlag = 1;
			}
			if(vm.printTag.status　==　"headNameFlag"){
				vm.custTemplate.headNameFlag = 1;
			}
			if(vm.printTag.status　==　"headShortNameFlag"){
				vm.custTemplate.headShortNameFlag = 1;
			}
			if(vm.printTag.status　==　"headNameEnFlag"){
				vm.custTemplate.headNameEnFlag = 1;
			}
			if(vm.printTag.status　==　"headAddressFlag"){
				vm.custTemplate.headAddressFlag = 1;
			}
			if(vm.printTag.status　==　"headPhoneFlag"){
				vm.custTemplate.headPhoneFlag = 1;
			}
			if(vm.printTag.status　==　"headFaxFlag"){
				vm.custTemplate.headFaxFlag = 1;
			}
			if(vm.printTag.status　==　"headWebEmailFlag"){
				vm.custTemplate.headWebEmailFlag = 1;
			}
			if(vm.printTag.status　==　"custNameFlag"){
				vm.custTemplate.custNameFlag = 1;
			}
			if(vm.printTag.status　==　"noFlag"){
				vm.custTemplate.noFlag = 1;
			}
			if(vm.printTag.status　==　"linkManFlag"){
				vm.custTemplate.linkManFlag = 1;
			}
			if(vm.printTag.status　==　"linkPhoneFlag"){
				vm.custTemplate.linkPhoneFlag = 1;
			}
			if(vm.printTag.status　==　"dateFlag"){
				vm.custTemplate.dateFlag = 1;
			}
			if(vm.printTag.status　==　"custAddFlag"){
				vm.custTemplate.custAddFlag = 1;
			}
			if(vm.printTag.status　==　"deailNoFlag"){
				vm.custTemplate.deailNoFlag = 1;
				vm.custTemplate.deailNoSortNum = vm.bodyPrintList.length + 1;
			}
			if(vm.printTag.status　==　"deailCustNoFlag"){
				vm.custTemplate.deailCustNoFlag = 1;
				vm.custTemplate.deailCustSortNum = vm.bodyPrintList.length + 1;
			}
			if(vm.printTag.status　==　"craftNoFlag"){
				vm.custTemplate.craftNoFlag = 1;
				vm.custTemplate.craftNoSortNum = vm.bodyPrintList.length + 1;
			}
			if(vm.printTag.status　==　"deailMaterNoFlag"){
				vm.custTemplate.deailMaterNoFlag = 1;
				vm.custTemplate.deailMaterNoSortNum = vm.bodyPrintList.length + 1;
			}
			if(vm.printTag.status　==　"custModalFlag"){
				vm.custTemplate.custModalFlag = 1;
				vm.custTemplate.custModalSortNum = vm.bodyPrintList.length + 1;
			}
			if(vm.printTag.status　==　"qualitityFlag"){
				vm.custTemplate.qualitityFlag = 1;
				vm.custTemplate.qualititySortNum = vm.bodyPrintList.length + 1;
			}
			if(vm.printTag.status　==　"doonateFlag"){
				vm.custTemplate.doonateFlag = 1;
				vm.custTemplate.doonateSortNum = vm.bodyPrintList.length + 1;
			}
			if(vm.printTag.status　==　"remarkFlag"){
				vm.custTemplate.remarkFlag = 1;
				vm.custTemplate.remarkSortNum = vm.bodyPrintList.length + 1;
			}
			if(vm.printTag.status　==　"deliverySizeFlag"){
				vm.custTemplate.deliverySizeFlag = 1;
				vm.custTemplate.deliverySizeSortNum = vm.bodyPrintList.length + 1;
			}
			if(vm.printTag.status　==　"deailDateFlag"){
				vm.custTemplate.deailDateFlag = 1;
				vm.custTemplate.deailDateSortNum = vm.bodyPrintList.length + 1;
			}
            if(vm.printTag.status　==　"deailProductNameFlag"){
                vm.custTemplate.deailProductNameFlag = 1;
                vm.custTemplate.deailProductNameSortNum = vm.bodyPrintList.length + 1;
            }
			if(vm.printTag.status　==　"bottomOneFlag"){
				vm.custTemplate.bottomOneFlag = 1;
			}
			if(vm.printTag.status　==　"bottomTwoFlag"){
				vm.custTemplate.bottomTwoFlag = 1;
			}
            if(vm.printTag.status　==　"rawQualitityNameFlag"){
                vm.custTemplate.rawQualitityNameFlag = 1;
                vm.custTemplate.rawQualitityNameSortNum = vm.bodyPrintList.length + 1;
            }
			vm.resetShowPrint();
		}
		vm.headPrintTag = {};
		vm.custPrintTag = {};
		vm.bodyPrintTag = {};
		vm.bottomPrintTag = {};
	};
	
    vm.showPrintTemList = [];
    vm.loadPrintTemList = function() {
    	vm.showPrintTemList = [];
    	angular.forEach(vm.printTemList,function(row){
    		// 判断是否有条件
    		var flag = 0;
    	    if(vm.templateNameStr){
    	    	if(!(row.templateName && row.templateName.indexOf(vm.templateNameStr) >= 0)){
    	    		flag = 1;
    	    	}
    	    }
    	    if(flag == 0){
    	    	vm.showPrintTemList.push(row);
    	    }
    	});
	};
	
	vm.managePrintTemp = function(row) {
		vm.headPrintShowList = [];
		vm.custPrintShowList = [];
		vm.bodyPrintShowList = [];
		vm.bottomPrintShowList = [];
		vm.custTemplate = row;
		vm.resetShowPrint();
		// 打开弹窗管理
		$('#custTemplate').modal();
	};
	
	vm.delPrintTemp = function(row) {
		MainCtrl.blockUI({
			animate: true,
		});
		upida.post("crm/delPrintTemp",row).then(function(data){
			alert("删除成功！")
			upida.post("crm/getPrintTemplateTwo",vm.custTemplate).then(function(data){
				vm.printTemList = data;
				vm.showPrintTemList = data;
				MainCtrl.unblockUI();
			});
		});
	};
	
	vm.allPageChecked = false;
	vm.recordIds = null;
    vm.selectAllPage = function(){
    	if(!vm.page.inout.data.list){
    		return;
    	}
    	angular.forEach(vm.page.inout.data.list,function(row){
	    	row.checked = !vm.allPageChecked;
    	});
    	setRecordIds();
    }
    
    function setRecordIds()
    {
    	vm.recordIds = null;
    	if(vm.inoutQuery.inoutType.value == 3){
    		angular.forEach(vm.page.inout.data.list,function(row){
    	    	if(row.checked)
    	    	{
    	    		if(vm.recordIds)
    		    	{
    		    		vm.recordIds = vm.recordIds +","+ row.recordId;
    		    	}
    		    	else
    		    	{
    		    		vm.recordIds = row.recordId;
    		    	}
    	    	}
        	});
    	}else{
    		angular.forEach(vm.page.inout.data.list,function(row){
    	    	if(row.checked)
    	    	{
    		    	if(vm.recordIds)
    		    	{
    		    		vm.recordIds = vm.recordIds +","+ row.recordId;
    		    	}
    		    	else
    		    	{
    		    		vm.recordIds = row.recordId;
    		    	}
    	    	}
        	});
    	}
    }
    
	vm.batchPrintPdf = function() {
		setRecordIds();
		if(!vm.recordIds)
		{
			vm.message = "请选择需要打印的内容!";
			$('#static').modal();
			return;
		}
		$http.post('a/stock/rawmaterial/batchPrintPdf',{'recordId': vm.recordIds,'inoutType':vm.inoutQuery.inoutType.value},{responseType: 'arraybuffer'})
            .success(function (response, status, headers, config) {
                MainCtrl.unblockUI();
                var a = document.createElement('a');
                var file = new Blob([response], {type: 'application/pdf'});
                var fileURL = (window.URL || window.webkitURL).createObjectURL(file);
                //window.open(fileURL);
                a.href = fileURL;
                a.download = headers('X-File-Name');
                document.body.appendChild(a);
                a.click();
                $timeout(function () {
                    document.body.removeChild(a);
                    URL.revokeObjectURL(fileURL);
                }, 100);
                vm.doInoutQuery();
            }).error(function (data) {
            MainCtrl.unblockUI();
            vm.message = "Sorry，打印失败！";
            $('#static').modal();
        });
	};
	
	vm.reportOne = true;
	vm.reportThree = false;
    vm.groupCheckQuery = function() {
    	if(vm.reportOne){
    		vm.getGroupCenterBillList();
    	}else{
    		vm.getGroupCenterDataList();
    	}
	};
	
	vm.monthDataList = [];
	vm.monthGroupDataList = [];
    vm.getGroupCenterBillList = function(){
    	
    	// 设置查询条件
        var condit = [];
        var material = {};//用物料属性查
        var supplier = {};//用客户属性查

        /*if(vm.inoutPlaceQuery.material && vm.inoutPlaceQuery.material.recordId)
        {
        	vm.materialPlaceFlag = false;
        	material.recordId = vm.inoutPlaceQuery.material.recordId;
            condit.push({
                name: 'material',
                value: material
            });
        }else{
        	vm.materialPlaceFlag = true;
        }
        if(vm.inoutPlaceQuery.materialPlace && vm.inoutPlaceQuery.materialPlace.recordId)
        {
            condit.push({
                name: 'stockPlaceId',
                value: vm.inoutPlaceQuery.materialPlace.recordId
            });
        }*/
        if(vm.materialNo)
        {
        	condit.push({
                name: 'materialNo',
                value: vm.materialNo
            });
        }
        if(vm.materialName)
        {
        	condit.push({
                name: 'materialName',
                value: vm.materialName
            });
        }
        if(vm.materialTypeId)
        {
        	condit.push({
                name: 'materialTypeId',
                value: vm.materialTypeId
            });
        }
        if(vm.specification)
        {
        	condit.push({
                name: 'specification',
                value: vm.specification
            });
        }
        if(vm.storeHouseId)
        {
        	condit.push({
                name: 'storeHouseId',
                value: vm.storeHouseId
            });
        }
       /* if (vm.inoutPlaceQuery.inoutType.value !== "") {
            condit.push({
                name: vm.inoutPlaceQuery.inoutType.name,
                value: vm.inoutPlaceQuery.inoutType.value
            });
        }*/

        
        
        if (vm.time.start) {
        	condit.push({
                name: vm.inoutPlaceQuery.inoutTimeStartQr.name,
                value: vm.timeTwo.start.valueOf()
            });
        	vm.inoutPlaceQuery.inoutTimeTwoStartQr.value = vm.timeTwo.start.valueOf();
        }
        if (vm.time.end) {
        	condit.push({
                name: vm.inoutPlaceQuery.inoutTimeEndQr.name,
                value: vm.timeTwo.end.valueOf()
            });
        	vm.inoutPlaceQuery.inoutTimeTwoEndQr.value = vm.timeTwo.end.valueOf();
        }
        vm.page.infoMonth.pageNo = 1;
        vm.page.infoMonth.condition = condit;
        
    	
    	/*var queryCond = [];
    	queryCond.push({
            name: "inoutTimeStartQr",
            value: vm.time.start.valueOf()
        });
    	queryCond.push({
            name: "inoutTimeEndQr",
            value: vm.time.end.valueOf()
        });
    	vm.page.infoMonth.condition = queryCond;
    	MainCtrl.blockUI({
			animate: true,
		});*/
    	vm.init(vm.page.infoMonth.pageNo, vm.page.infoMonth.pageSize, vm.page.infoMonth.condition, vm.page.infoMonth.url, handle.month);
    	//vm.initReportData(1, 10, queryCond, "stock/rawmaterial/materialMonthPage");
    	
    } ;
    
    vm.getGroupCenterDataList = function(){
    	
    };
    vm.initReportDataList = [];
    vm.initReportData = function (no, size, condition, url) {
        MainCtrl.blockUI({
            animate: true,
        });

        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;
        
        condition.push({
            name: vm.inoutPlaceQuery.sort.name,
            value: "operateDate ASC"
        });

        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condition);

        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};

            // 如果结果为空
            if (angular.isUndefined(result) || angular.isUndefined(result.list)) {
                data.pageNo = 1;
                data.pageSize = 10;
                data.startCount = 0;
                data.endCount = 0;
                data.list = [];
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }

            // 设置返回的请求数据
            vm.initReportDataList = data.list;
            MainCtrl.unblockUI();
        });
    };

    vm.transferLibrary = {};
    vm.transferLibraryOpen = function()
    {
    	vm.transferLibrary = {};
    	if(vm.inoutPlaceQuery.material && vm.inoutPlaceQuery.material.recordId)
    	{
    		vm.transferLibrary.material = vm.inoutPlaceQuery.material;
    		vm.getMatPlaceComData(vm.transferLibrary.material,1);
    	}
    	else
    	{
    		$('#transferLibraryStatic').modal();
    	}
    };
    
    vm.matPlaceComList = [];
    vm.getMatPlaceComData = function(num)
    {
		upida.post("stock/rawmaterial/getMatPlaceComData",vm.transferLibrary.material).then(function(data){
			vm.matPlaceComList = data;
			if(num)
			{
				$('#transferLibraryStatic').modal();
			}
		});
    };
    
    vm.transferLibrarySave = function()
    {
    	if(!vm.transferLibrary.material || !vm.transferLibrary.material.recordId)
    	{
    		alert("请选择物料!");
    		return;
    	}
    	if(!vm.transferLibrary.matPlaceComOut || !vm.transferLibrary.matPlaceComOut.recordId)
    	{
    		alert("请选择移出库位!");
    		return;
    	}
    	if(!vm.transferLibrary.matPlaceComIn || !vm.transferLibrary.matPlaceComIn.recordId)
    	{
    		alert("请选择移入库位!");
    		return;
    	}
    	if(vm.transferLibrary.matPlaceComOut.recordId == vm.transferLibrary.matPlaceComIn.recordId)
    	{
    		alert("移出移入库位不能相同!");
    		return;
    	}
    	if(!vm.transferLibrary.stocks || vm.transferLibrary.stocks == 0)
    	{
    		alert("移库数量要大于0!");
    		return;
    	}
    	if(vm.transferLibrary.stocks > (vm.transferLibrary.matPlaceComOut.periodStocks ? Number(vm.transferLibrary.matPlaceComOut.periodStocks) : 0))
    	{
    		alert("移库数量不能大于移出库位的库存!");
    		return;
    	}
        MainCtrl.blockUI({
            animate: true,
        });
		upida.post("stock/rawmaterial/transferLibrary",vm.transferLibrary).then(function(data){
			vm.message = data.message;
			$('#static').modal();
			if(data.result == "success") 
			{
				vm.getPlaceDataList();
				$('#transferLibraryStatic').modal('hide');
			}
			MainCtrl.unblockUI();
		});
    };

    vm.deliveryNo = "";
    vm.productionStatus = "";

    vm.purchStock = {};
    vm.bindDelivery = function(row)
    {
        vm.purchStock = row;
        $("#bindDelivery").modal();
    };

    vm.bindDeliveryMsg = function(){
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("stock/rawmaterial/bindDeliveryMsg",vm.purchStock).then(function(data){
            vm.message = "保存成功";
            $('#static').modal();
            vm.init(vm.page.inout.pageNo, vm.page.inout.pageSize, vm.page.inout.condition, vm.page.inout.url, handle.inout);
            MainCtrl.unblockUI();
        });
    };
	
    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadData();
        getMatPlaceComData();
    });

    vm.producerList = []; //品牌集合
    vm.copperBoardList = []; // 覆铜板材集合
    vm.right = {};
    vm.page.options = [5, 10, 30, 50];
    vm.page.common = {};
    vm.page.common.data = {};
    vm.page.common.pageSize = 10;
    vm.page.common.pageNo = 1;
    vm.page.common.condition = [];
    vm.queryCommon = {};
    vm.edit = {};
    function getMatPlaceComData()
    {
        upida.get("stock/common/material/getParameter").then(function(data){
            vm.producerList = data.producerList;
            vm.copperBoardList = data.copperBoardList;
        });
    };

    vm.openEdit = function(type,item){
        if(type==1){
            vm.edit = {};
        }else{
            vm.edit = JSON.parse(JSON.stringify(item));
            vm.edit.width = Number(vm.edit.width)
            vm.edit.length = Number(vm.edit.width)
        }
        vm.showCommonMaterial();
    }

    vm.CommonMaterialData = function(obj)
    {
        // 请求数据
        var reqData = {};
        reqData.pageNo = obj.pageNo;
        reqData.pageSize = obj.pageSize;
        reqData.queryAll = obj.queryAll;
        // 设置过滤条件
        if (obj.condition.length > 0)
        {
            angular.forEach(obj.condition, function (p)
            {
                reqData[p.name] = p.value;
            });
        }
        upida.post("stock/common/material/page",reqData).then(function(result){
            var data = {};
            if ((typeof result === "undefined") || (typeof result.list === "undefined")) {
                data.pageNo = 1;
                data.pageSize = 10;
                data.startCount = 0;
                data.endCount = 0;
                data.list = [];
            } else {
                // 计算开始数、计算结束数
                data = result;
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }
            // 设置返回的请求数据
            obj.data = data;
        });
    }
    vm.doQueryCommon = function () {
        // 设置查询条件
        var condition = [];
        if (vm.queryCommon.producerName !== "") {
            condition.push({
                name: 'producerName',
                value: vm.queryCommon.producerName
            });
        }
        if (vm.queryCommon.copperBoardName !== "") {
            condition.push({
                name: 'copperBoardName',
                value: vm.queryCommon.copperBoardName
            });
        }
        vm.page.common.pageNo = 1;
        vm.page.common.condition = condition;

        // 查询数据
        vm.CommonMaterialData(vm.page.common);
    }
    // 页面显示数量改变
    vm.pageSizeCommonChange = function () {
        vm.page.common.pageNo = 1;
        vm.doQueryCommon();
    };
    // 分页按钮单击处理
    vm.doCommonPage = function (page, pageSize, total) {
        vm.page.common.pageSize = pageSize;
        vm.page.common.pageNo = page;
        vm.CommonMaterialData(vm.page.common);
        vm.allPageChecked = false; //跳转页面重置选中状态
    };

    vm.deleteId = '';
    vm.deleteOpen = function(deleteId){
      vm.deleteId = deleteId;
      $('#deleteModal').modal();
    }

    vm.deleteCommon = function(){
        upida.post("stock/common/material/delete",vm.deleteId).then(function (result) {
            if(result=="success"){
                vm.message = "删除成功!";
                $('#static').modal();
                $('#deleteModal').modal('hide');
                vm.doQueryCommon();
            }else{
                vm.message = result;
                $('#static').modal();
            }
            MainCtrl.unblockUI();
        });
    };

    vm.saveCommonMaterial = function(){
        if (!vm.edit.producerId && !vm.edit.copperBoardId) {
            vm.message = "请选择品牌或者覆铜板材!";
            $('#static').modal();
            return;
        }else if (!vm.edit.width) {
            vm.message = "请输入宽度!";
            $('#static').modal();
            return;
        }else if (!vm.edit.length) {
            vm.message = "请输入长度!";
            $('#static').modal();
            return;
        }else{
            upida.post("stock/common/material/save",vm.edit).then(function (result) {
                if(result=="success"){
                    vm.message = "保存成功!";
                    $('#static').modal();
                    vm.hideCommonMaterial();
                    vm.doQueryCommon();
                }else{
                    vm.message = result;
                    $('#static').modal();
                }
                MainCtrl.unblockUI();
            });
        }
    }

    vm.versionList = [];
    vm.versionId = '';
    vm.newVersionId = '';
    vm.quotationMaterial = function(){
        upida.get("stock/materialQuotation/versionList").then(function (result) {
            vm.versionList = result;
            if(vm.versionList && vm.versionList.length>0)
            {
                vm.versionId = vm.versionList[0].recordId;
                vm.newVersionId = vm.versionList[0].recordId;
                vm.loadQuotationRecord();
            }
        });
    }
    /*vm.createVersion = function(versionId){
        if (!versionId)
        {
            vm.message = "数据错误清刷新重试";
            $('#static').modal();
            return;
        }
        let item = {};
        item.recordId = versionId
        upida.post("stock/materialQuotation/createVersion",item).then(function (result) {
           if(result=="success"){
               vm.quotationMaterial();
               vm.message = "创建成功!";
               $('#static').modal();
               }
               else if (result == "approvalStatus")
               {
                   vm.message = "当前版本没有审批通过不能再次更新报价！";
                   $('#static').modal();
               }
               else{
               vm.message = "创建失败!";
               $('#static').modal();
           }
        });
    }*/

    vm.materialNoThree = null;
    vm.quotationRecord = function(){
        var condition = [];
        if (vm.timeThree.start) {
            condition.push({
                name: vm.inoutPlaceQuery.sentTimeStartQr.name,
                value: vm.timeThree.start.valueOf()
            });
            vm.inoutPlaceQuery.sentTimeStartQr.value = vm.timeThree.start.valueOf();
        }
        if (vm.time.end) {
            condition.push({
                name: vm.inoutPlaceQuery.sentTimeEndQr.name,
                value: vm.timeThree.end.valueOf()
            });
            vm.inoutPlaceQuery.sentTimeEndQr.value = vm.timeThree.end.valueOf();
        }
        if(vm.materialNoThree)
        {
            condition.push({
                name: 'materialNo',
                value: vm.materialNoThree
            });
        }
        vm.page.infoQuotation.pageNo = 1;
        vm.page.infoQuotation.condition = condition;
        // 请求数据
        vm.init(vm.page.infoQuotation.pageNo, vm.page.infoQuotation.pageSize, vm.page.infoQuotation.condition, vm.page.infoQuotation.url, handle.quotation);
    }

    vm.editQuotation = function () {
        vm.saveList = [];
        vm.list = vm.page.infoQuotation.data.list;
        for (let i = 0; i < vm.list.length ; i++) {
            vm.saveList.push({"materialId":vm.list[i].recordId,"leadTime":vm.list[i].leadTime,"safetyStock":vm.list[i].safetyStockNumber,"price":vm.list[i].price});
        }
        upida.post("stock/materialQuotation/updateRecord?versionId="+vm.versionId, vm.saveList).then(function (data) {
            if(data!='success'){
                vm.message = data;
                $('#static').modal();
            }
            if (data=='success')
            {
                vm.message = "保存成功";
                $('#static').modal();
                vm.loadQuotationRecord();
            }
        });
    };

    vm.approvalMaterialQuotation = function ()
    {
        vm.message = "您确定要提交原料报价单审批吗？";
        $('#staticCommitAuditOpen').modal();
    }

    vm.commitAudit = function ()
    {
        if (!vm.clicks)
        {
            return;
        }
        let item = {};
        item.versionId = vm.versionId
        vm.clicks = false;
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("stock/materialQuotation/approvalMaterialQuotation", item).then(function (data){
            vm.message = data.message;
            $('#static').modal();
            if (data.result == "success")
            {
                vm.loadQuotationRecord();
            }
        })
    }

    vm.recordList = [];
    vm.selectAllPageTwo = function(){
        if(!vm.page.info.data.list){
            return;
        }
        angular.forEach(vm.page.info.data.list,function(row){
            row.checked = !vm.allPageChecked;
        });
        vm.getRecordList();
    }
    vm.getRecordList = function()
    {
        vm.recordList = [];
        angular.forEach(vm.page.info.data.list,function(row){
            if(row.checked)
            {
                vm.recordList.push(angular.copy(row));
            }
        });
    };

    vm.approval = function (){
        if (!vm.recordList || vm.recordList.length == 0)
        {
            vm.message = "请选择要审批的原材料";
            $('#static').modal();
            return;
        }
        vm.fileList = [];
        $('#approvalStatic').modal();
    };

    vm.fileList = []
    vm.uploadFiles = function(file) {
        if (file && file.length > 0) {
            for (let x = 0; x < file.length; x++) {
                const f = file[x];
                const type = [
                    "image/jpg",
                    "image/jpeg",
                    "image/png",
                    "application/pdf",
                    "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "application/vnd.ms-excel",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "application/vnd.ms-powerpoint",
                    "application/vnd.openxmlformats-officedocument.presentationml.presentation"
                ];
                if (f && type.indexOf(f.type) >= 0) {
                    for (const i in vm.fileList) {
                        if (vm.fileList[i].name === f.name) {
                            alert("文件“" + f.name + "”已经存在！");
                            return;
                        }
                    }
                    vm.fileList.push(f);
                } else {
                    alert(
                        "只能上传jpg/jpeg/png/pdf/doc/docx/xls/xlsx/ppt/pptx后缀名的文件"
                    );
                }
            }
        }
    };

    vm.delFile = function(index) {
        vm.fileList.splice(index, 1);
    };

    // 上传文件
    vm.submitApprovalOne = function () {
        // 初始化
        vm.uploadResp = {};
        var len = 0;
        var fileNum = 0;
        if (!vm.fileList || vm.fileList.length == 0)
        {
            vm.message = "请先上传附件";
            $('#static').modal();
            return;
        }
        for(let uFile of vm.fileList)
        {
            if(!uFile.recordId)
            {
                fileNum++;
            }
        }
        angular.forEach(vm.fileList, function (file) {
            // 自定义大板、A板、B板和附件的名称，用于上传识别、或返回显示等
            var respName = "erpRawmaterialPrice"
            if(!file.recordId)
            {
                MainCtrl.blockUI({
                    animate: true,
                });
                // 定义上传方法、需要带通知单号
                file.upload = Upload.upload({
                    url: 'a/stock/rawmaterial/upload',
                    fields: {
                        'uploadType': respName,
                        'oper': 'add'
                    },
                    file: file
                });

                // 设置默认的上传状态
                file.status = 2;
                file.upload.then(function (response) {
                    $timeout(function () {
                        file.status = response.status;

                        // 上传数量+1
                        len++;

                        // 设置返回结果
                        if (!vm.uploadResp[respName]) {
                            vm.uploadResp[respName] = [];
                        }
                        vm.uploadResp[respName].push(response.data[respName]);

                        if(len == fileNum)
                        {
                            MainCtrl.unblockUI();
                            submitApproval(vm.uploadResp);
                        }
                    });
                }, function (response) {
                    file.status = response.status;
                    if (response.status > 0) {
                        vm.errorMsg = response.status + ': ' + response.data;
                    }
                }, function (evt) {
                    file.progress = Math.min(100, parseInt(100.0 * evt.loaded / evt.total));
                });
            }
        });
        vm.clicks = true;// tj 2017-09-07 用户是否多次点击
    };

    function submitApproval (file)
    {
        let item = {};
        vm.recordIdTwo = null;
        for (var i = 0; i < vm.recordList.length; i++) {
            if (vm.recordList[i].erpRawmaterialPrice || vm.recordList[i].erpRawmaterialLeadTime)
            {
                if (null != vm.recordIdTwo)
                {
                    vm.recordIdTwo = vm.recordIdTwo + "," + vm.recordList[i].recordId;
                }
                else
                {
                    vm.recordIdTwo = vm.recordList[i].recordId;
                }
            }
        }
        item.materialIds = vm.recordIdTwo;
        item.materialAttachementsList = file.erpRawmaterialPrice;
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("stock/rawmaterial/submitApproval", item).then(function (data){
            vm.message = data;
            $('#static').modal();
            MainCtrl.unblockUI();
            vm.doQuery();
        })
    }

    vm.downloadFile = function(item,num)
    {
        const file = item;
        let type = [];
        let typePdf = [];
        if(num == 1)
        {
            type = ["image/jpg", "image/jpeg", "image/png"];
            typePdf = ["application/pdf"];
        }
        else if(num == 2)
        {
            type = ['jpg', 'jpeg', 'png'];
            typePdf = ['pdf'];
        }
        if (file && type.indexOf(file.type) >= 0) {
            if (item.downloadUrl) {
                $('#previewId').modal();
                document.getElementById('preview').src = item.downloadUrl
            } else {
                const reader = new FileReader()
                reader.readAsDataURL(file)
                reader.onload = function () {
                    $('#previewId').modal();
                    document.getElementById('preview').src = reader.result
                }
            }
        } else if (file && typePdf.indexOf(file.type) >= 0) {
            if (item.downloadUrl) {
                const win = window.open()
                win.document.write('<body style="margin:0px;"><iframe src="' + item.downloadUrl + '" scrolling="no" width="100%" height="100%" frameborder="0" ></iframe></body>')
                win.document.close()
            } else {
                const reader = new FileReader()
                reader.readAsDataURL(file)
                reader.onload = function () {
                    const win = window.open()
                    win.document.write('<body style="margin:0px;"><iframe src="' + reader.result + '" scrolling="no" width="100%" height="100%" frameborder="0" ></iframe></body>')
                    win.document.close()
                }
            }
        } else {
            if (item.downloadUrl) {
                if (file.type) {
                    const win = window.open()
                    win.document.write('<body style="margin:0px;"><iframe src="https://view.officeapps.live.com/op/view.aspx?src=' + encodeURIComponent(item.downloadUrl) + '" scrolling="no" width="100%" height="100%" frameborder="0" ></iframe></body>')
                    win.document.close()
                } else {
                    const strs = item.name.split('.')
                    if (strs[1] === 'jpg' || strs[1] === 'JPG' || strs[1] === 'png' || strs[1] === 'PNG' || strs[1] === 'jpeg' ||
                        strs[1] === 'JPEG' || strs[1] === 'bmp' || strs[1] === 'BMP' || strs[1] === 'gif' || strs[1] === 'GIF') {
                        $('#previewId').modal('show')
                        document.getElementById('preview').src = item.downloadUrl
                    } else if (strs[1] === 'docx' || strs[1] === 'DOCX' || strs[1] === 'doc' || strs[1] === 'DOC' || strs[1] === 'xls' ||
                        strs[1] === 'XLS' || strs[1] === 'xlsx' || strs[1] === 'XLSX') {
                        const win = window.open()
                        win.document.write('<body style="margin:0px;"><iframe src="https://view.officeapps.live.com/op/view.aspx?src=' + encodeURIComponent(item.downloadUrl) + '" scrolling="no" width="100%" height="100%" frameborder="0" ></iframe></body>')
                        win.document.close()
                    } else if (strs[1] === 'pdf' || strs[1] === 'PDF') {
                        const win = window.open()
                        win.document.write('<body style="margin:0px;"><iframe src="' + item.downloadUrl + '" scrolling="no" width="100%" height="100%" frameborder="0" ></iframe></body>')
                        win.document.close()
                    }
                }
            } else {
                const reader = new FileReader()
                reader.readAsDataURL(file)
                reader.onload = function () {
                    const win = window.open()
                    win.document.write('<body style="margin:0px;"><iframe src="' + reader.result + '" scrolling="no" width="100%" height="100%" frameborder="0" ></iframe></body>')
                    win.document.close()
                }
            }
        }
    };

    vm.storeHouseIdList = [];

    vm.selectStoreHouseId = function()
    {
        vm.storeHouseId = null;
        if(vm.storeHouseIdList && vm.storeHouseIdList.length > 0)
        {
            angular.forEach(vm.storeHouseIdList,function(p){
                if(vm.storeHouseId)
                {
                    vm.storeHouseId = vm.storeHouseId +","+ p;
                    vm.inoutQuery.storehouse.value = vm.inoutQuery.storehouse.value +","+ p;
                    vm.query.storehouse.value = vm.query.storehouse.value + "," + p;
                }
                else
                {
                    vm.storeHouseId = p;
                    vm.inoutQuery.storehouse.value = p;
                    vm.query.storehouse.value = p;
                }
            });
        }
    }
    vm.removeStoreHouseId = function(name)
    {
        for(let i=0;i<vm.storeHouseIdList.length;i++)
        {
            if(vm.storeHouseIdList[i] == name)
            {
                vm.storeHouseIdList.splice(i,1);
            }
        }
        vm.selectStoreHouseId();
    }

    vm.openFlag = function (row)
    {
        if (!row)
        {
            return;
        }
        row.packUpFlag = !row.packUpFlag;
        if(row.packUpFlag)
        {
            MainCtrl.blockUI({
                animate: true,
            });
            upida.post("stock/rawmaterial/getShowApprovalData", row).then(function (data) {
                row.materialList = data;
                MainCtrl.unblockUI();
            });
        }
    }

    vm.fisrtInFirstOutList = [];
    vm.openFistInFirstRecord = function (list) {
        vm.fisrtInFirstOutList = list;
        $('#fistInFirstRecord').modal();

    }

    vm.timeFour = {
        start: {},
        end: {}
    };

    vm.initDateFour=function(date)
    {
        if(date==""){
            vm.rangeOptionsFour = {
                //format: "YYYY-MM-DD",
                startDate: new Date(vm.firstInFirstOut.sentTimeStartQr.value),
                minDate:new Date(new Date(vm.firstInFirstOut.sentTimeEndQr.value).setFullYear(new Date(vm.firstInFirstOut.sentTimeEndQr.value).getFullYear()-5))
            };
            vm.timeFour= {
                start: vm.rangeOptionsFour.startDate,
                end: vm.rangeOptionsFour.minDate
            }
        }
    };

    vm.rangeOptionsFour = {
        //format: "YYYY-MM-DD",
//			    	startDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5))),
        startDate:new Date((new Date).getFullYear(), (new Date).getMonth(), 1),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 6)))
    };

    vm.storeHouseIdListTwo = [];

    vm.selectStoreHouseIdTwo = function()
    {
        vm.storeHouseIdTwo = null;
        if(vm.storeHouseIdListTwo && vm.storeHouseIdListTwo.length > 0)
        {
            angular.forEach(vm.storeHouseIdListTwo,function(p){
                if(vm.storeHouseIdTwo)
                {
                    vm.storeHouseIdTwo = vm.storeHouseIdTwo +","+ p;
                }
                else
                {
                    vm.storeHouseIdTwo = p;
                }
            });
        }
    }
    vm.removeStoreHouseIdTwo = function(name)
    {
        for(let i=0;i<vm.storeHouseIdListTwo.length;i++)
        {
            if(vm.storeHouseIdListTwo[i] == name)
            {
                vm.storeHouseIdListTwo.splice(i,1);
            }
        }
        vm.selectStoreHouseIdTwo();
    }

    vm.materialNoFour = null;
    vm.firstDate = null;
    vm.materialPlaceTwo = null;
    vm.materialNameTwo = null;
    vm.materialTypeIdTwo = null;
    vm.firstFirstOut = function(){
        var condition = [];
        if (vm.timeFour.start) {
            condition.push({
                name: vm.firstInFirstOut.sentTimeStartQr.name,
                value: vm.timeFour.start.valueOf()
            });
            vm.firstInFirstOut.sentTimeStartQr.value = vm.timeFour.start.valueOf();
        }
        if (vm.timeFour.end) {
            condition.push({
                name: vm.firstInFirstOut.sentTimeEndQr.name,
                value: vm.timeFour.end.valueOf()
            });
            vm.firstInFirstOut.sentTimeEndQr.value = vm.timeFour.end.valueOf();
        }
        if(vm.materialNoFour)
        {
            condition.push({
                name: 'materialNo',
                value: vm.materialNoFour
            });
        }
        if(vm.materialPlaceTwo)
        {
            condition.push({
                name: 'stockPlaceId',
                value: vm.materialPlaceTwo.recordId
            });
        }
        if(vm.materialNameTwo)
        {
            condition.push({
                name: 'materialName',
                value: vm.materialNameTwo
            });
        }
        if (vm.materialTypeIdTwo)
        {
            condition.push({
                name: 'materialTypeId',
                value: vm.materialTypeIdTwo
            });
        }
        if (vm.storeHouseIdTwo)
        {
            condition.push({
                name: 'storeHouseId',
                value: vm.storeHouseIdTwo
            });
        }
        /*vm.page.firstInFirstOut.pageNo = 1;*/
        vm.page.firstInFirstOut.condition = condition;
        // 请求数据
        vm.init(vm.page.firstInFirstOut.pageNo, vm.page.firstInFirstOut.pageSize, vm.page.firstInFirstOut.condition, vm.page.firstInFirstOut.url, handle.first);
    }

    vm.firstOutList = [];
    vm.modDelivery = function (data, num)
    {
        vm.firstOutList = [];
        if (num == 1)
        {
            vm.firstOutList = data.inStockList;
        }
        else if (num = 2)
        {
            vm.firstOutList = data.outStockList;
        }
        $('#fistInFirstRecordTwo').modal();
    };

    vm.materialNoFive = null;
    vm.materialNameThree = null;
    vm.materialTypeIdThree = null;
    vm.userId = null;
    vm.selectMaterialStore = function ()
    {
        var condition = [];
        if(vm.materialNoFive)
        {
            condition.push({
                name: 'materialNo',
                value: vm.materialNoFive
            });
        }
        if(vm.materialNameThree)
        {
            condition.push({
                name: 'materialName',
                value: vm.materialNameThree
            });
        }
        if (vm.materialTypeIdThree)
        {
            condition.push({
                name: 'materialTypeId',
                value: vm.materialTypeIdTwo
            });
        }
        if (vm.userId)
        {
            condition.push({
                name: 'userId',
                value: vm.userId
            });
        }
        vm.page.firstInFirstOut.pageNo = 1;
        vm.page.firstInFirstOut.condition = condition;
        vm.init(vm.page.materialStore.pageNo, vm.page.materialStore.pageSize, vm.page.materialStore.condition, vm.page.materialStore.url, handle.store);
    }

    vm.materialStorePaging = function(page, pageSize, total) {
        vm.materialStore.pageNo = page;
        vm.materialStore.pageSize = pageSize;
        vm.init(vm.page.materialStore.pageNo, vm.page.materialStore.pageSize, vm.page.materialStore.condition, vm.page.materialStore.url, handle.store);
    };

    vm.materialStoreNotePaging = function() {
        vm.init(1, vm.page.materialStore.pageSize, vm.page.materialStore.condition, vm.page.materialStore.url, handle.store);
    };

    vm.addMaterialItemsTwo = function(no)
    {
        var material = {};
        material.no = no;
        material.materialKind = 100701;
        upida.post("stock/material/addMaterialItems",material).then(function(data){
            if(data && data.length > 0)
            {
                vm.materialQueryListTwo = data;
            }
        });
    }

    vm.changeCheked = function (row){
        if(!row)
        {
            return;
        }
        if (row.checked == 1)
        {
            row.checked = "";
        }
        else
        {
            row.checked = 1;
        }
        vm.materialStoreList = [];
        angular.forEach(vm.page.materialStore.data.list,function (p){
            if (p.checked){
                vm.materialStoreList.push(p)
            }
        })
    };
    vm.openFlag = null;
    vm.saveMaterialStore = function (num){
        vm.openFlag = num;
        if (!num){
            return;
        }
        vm.openFlag = num;
        if (num == 1)
        {
            vm.materialStoreList = [];
        }
        else if (num == 2)
        {
            if (!vm.materialStoreList || vm.materialStoreList ==0)
            {
                vm.message = "请选择要修改的自动出库信息";
                $('#static').modal();
                return;
            }
        }
        $('#saveMaterialStoreList').modal();
    };

    vm.deleteMaterialStoreList = [];
    vm.deleteMaterialStore = function (){
        if (!vm.materialStoreList || vm.materialStoreList.length == 0){
            vm.message = "请选择要删除的自动出库信息";
            $('#static').modal();
            return;
        }
        vm.deleteMaterialStoreList =  [];
        for (let record of vm.materialStoreList){
            if (record.checked){
                vm.deleteMaterialStoreList.push(record);
            }
        }
        vm.message = "您确定要删除自动出库信息?";
        $('#delMaterialStore').modal();
    };

    vm.addShiftDetailRecord = function (){
        if (!vm.materialStoreList || vm.materialStoreList == 0){
            vm.materialStoreList = [];
        }
        let record = {};
        vm.materialStoreList.splice(0,0,record);
    }

    vm.delShiftDetailRecord = function (){
        if (!vm.materialStoreList || vm.materialStoreList.length == 0){
            vm.materialStoreList = [];
            return;
        }
        for (let i = 0;i<vm.materialStoreList.length;i++){
            if (vm.materialStoreList[i].checked){
                vm.materialStoreList.splice(i,1);
                i--;
            }
        }
    };

    vm.delShiftDetailRecord = function (){
        if (!vm.materialStoreList || vm.materialStoreList.length == 0){
            vm.materialStoreList = [];
            return;
        }
        for (let i = 0;i<vm.materialStoreList.length;i++){
            if (vm.materialStoreList[i].checked){
                vm.materialStoreList.splice(i,1);
                i--;
            }
        }
    };

    vm.onMaterialSelected = function (item, row)
    {
        if (item && item.materialTypeId)
        {
            row.materialTypeId = item.materialTypeId;
        }
    }

    vm.onMaterialTypSelected = function (item, row)
    {
        if (row.materialId)
        {
            // 从物料列表中查找当前选中的物料对象
            const selectedMaterial = this.materialQueryListTwo.find(
                item => item.recordId === row.materialId
            );

            // 检查物料类型是否一致
            if (selectedMaterial && selectedMaterial.materialTypeId.toString() !== item.recordId)
            {
                row.materialId = null; // 清空物料选择
            }
        }
    };

    vm.saveShiftDetailRecord = function()
    {
        if(!vm.clicks)
        {
            vm.message = "请勿重复点击";
            $('#static').modal();
            return;
        }
        if(!vm.materialStoreList || vm.materialStoreList.length == 0)
        {
            vm.message = "没有保存的内容，请重试!";
            $('#static').modal();
            return;
        }
        let checkFlag = false;
        for(let record of vm.materialStoreList)
        {
            if(!record.materialId && !record.materialTypeId)
            {
                vm.message = "物料和物料类型至少选择一个";
                checkFlag = true;
                break;
            }
            if (!record.userId){
                vm.message = "请选择出库人";
                checkFlag = true;
                break;
            }
        }
        if(checkFlag)
        {
            $('#static').modal();
            return;
        }
        vm.clicks = false;
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("stock/materialStore/addMaterialStoreList", vm.materialStoreList).then(function(data) {
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#saveMaterialStoreList').modal('hide');
                vm.init(vm.page.materialStore.pageNo, vm.page.materialStore.pageSize, vm.page.materialStore.condition, vm.page.materialStore.url, handle.store);
            }
            MainCtrl.unblockUI();
            vm.clicks = true;
        });
    }

    vm.deleteCustomerSale = function (){
        if (!vm.deleteMaterialStoreList || vm.deleteMaterialStoreList == 0){
            vm.message = "没有要删除的内容!";
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        //进行保存
        upida.post("stock/materialStore/delMaterialStoreList",vm.deleteMaterialStoreList).then(function(data){
            vm.message = data.message;
            $('#static').modal();
            if(data.result == "success")
            {
                $('#delCustomerRemarks').modal('hide');
                vm.init(vm.page.materialStore.pageNo, vm.page.materialStore.pageSize, vm.page.materialStore.condition, vm.page.materialStore.url, handle.store);
            }
            MainCtrl.unblockUI();
        });
    }
}]);