/**
 * 
 */
package com.kyb.pcberp.modules.crm.service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.enums.CommonEnums.DictItemEnum;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.CommonUtils;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.MathUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.contract.dao.BatchDeliveryDao;
import com.kyb.pcberp.modules.contract.dao.ContractCraftDao;
import com.kyb.pcberp.modules.contract.dao.ContractDao;
import com.kyb.pcberp.modules.contract.dao.ContractDetailDao;
import com.kyb.pcberp.modules.contract.dao.PriceDao;
import com.kyb.pcberp.modules.contract.entity.BatchDelivery;
import com.kyb.pcberp.modules.contract.entity.Contract;
import com.kyb.pcberp.modules.contract.entity.ContractCraft;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.contract.entity.Price;
import com.kyb.pcberp.modules.crm.dao.CustomerDao;
import com.kyb.pcberp.modules.crm.dao.SlCustomerOrderDao;
import com.kyb.pcberp.modules.crm.dao.SlCustomerOrderDetailDao;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.crm.entity.SlCustomerOrder;
import com.kyb.pcberp.modules.crm.entity.SlCustomerOrderDetail;
import com.kyb.pcberp.modules.eg.entity.BoardPartCraft;
import com.kyb.pcberp.modules.eg.entity.CardA;
import com.kyb.pcberp.modules.finance.dao.CollectionTotalDao;
import com.kyb.pcberp.modules.finance.entity.CollectionTotal;
import com.kyb.pcberp.modules.production.dao.ProduceRecordDao;
import com.kyb.pcberp.modules.production.entity.Feeding;
import com.kyb.pcberp.modules.production.entity.ProduceBatchDetail;
import com.kyb.pcberp.modules.production.entity.ProduceBatchDetailCraft;
import com.kyb.pcberp.modules.production.entity.ProduceRecord;
import com.kyb.pcberp.modules.production.entity.Wip;
import com.kyb.pcberp.modules.production.entity.WipProcessStop;
import com.kyb.pcberp.modules.sys.dao.BranchDao;
import com.kyb.pcberp.modules.sys.entity.Branch;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import com.kyb.pcberp.modules.sys.entity.ParameterSet;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.ConfigUtils;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

/**
 * cqService
 * 
 * <AUTHOR>
 * @version 2016-08-12
 */
@Service
@Transactional(readOnly = true)
public class SlCustomerOrderService extends CrudService<SlCustomerOrderDao, SlCustomerOrder>
{
    
    @Autowired
    private SlCustomerOrderDao slCustomerOrderDao;
    
    @Autowired
    private SlCustomerOrderDetailDao slCustomerOrderDetailDao;
    
    @Autowired
    private ContractDao contractDao;
    
    @Autowired
    private ContractCraftDao contractCraftDao;
    
    @Autowired
    private PriceDao priceDao;
    
    @Autowired
    private ContractDetailDao contractDetailDao;
    
    @Autowired
    private CollectionTotalDao collectionTotalDao;
    
    @Autowired
    private BranchDao branchDao;
    
    @Autowired
    private CustomerDao customerDao;
    
    @Autowired
    private BatchDeliveryDao batchDao;
    
    @Autowired
    private ProduceRecordDao produceRecordDao;
    
    public SlCustomerOrder get(String id)
    {
        return super.get(id);
    }
    
    public List<SlCustomerOrder> findList(SlCustomerOrder slCustomerOrder)
    {
        return super.findList(slCustomerOrder);
    }
    
    public Page<SlCustomerOrder> findPage(Page<SlCustomerOrder> page, SlCustomerOrder slCustomerOrder)
    {
        return super.findPage(page, slCustomerOrder);
    }
    
    @Transactional(readOnly = false)
    public String saveSlCustomerOrder(SlCustomerOrder slCustomerOrder)
    {
        
        if (slCustomerOrder.getIsNewRecord())
        {
            // 设置客户下单状态
            slCustomerOrder.setStatus(TypeKey.CUSTOMER_ORDER_UNCONFIRMED.toString());
            
            // 判断编号是否被引用了 是就累加
            if (slCustomerOrder.getFindNoisEnable() == 1)
            {
                slCustomerOrder
                    .setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CUSTOMERORDER.getIndex().toString()));
            }
            Integer noNum = slCustomerOrderDao.findContractNoisEnable(slCustomerOrder); // 查询编号是否依旧存在
            while (noNum != null && noNum > 0)
            {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.CUSTOMERORDER.getIndex());
                slCustomerOrder
                    .setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CUSTOMERORDER.getIndex().toString()));
                noNum = slCustomerOrderDao.findContractNoisEnable(slCustomerOrder); // 查询编号是否依旧存在
            }
            slCustomerOrder.preInsert();
            slCustomerOrderDao.insert(slCustomerOrder);
            
            for (int i = 0; i < slCustomerOrder.getQuotationItemList().size(); i++)
            {
                slCustomerOrder.getQuotationItemList().get(i).setCustomerorderId(slCustomerOrder);
                slCustomerOrder.getQuotationItemList().get(i).setCompany(UserUtils.getUser().getCompany());
                slCustomerOrder.getQuotationItemList().get(i).setCustomerId(slCustomerOrder.getCustomer());
                slCustomerOrder.getQuotationItemList().get(i).preInsert();
                slCustomerOrderDetailDao.insert(slCustomerOrder.getQuotationItemList().get(i));
            }
            // 添加成功把对应编码的nextNo 修改为+1 TODO 要添加客户下单对应的编号
            CommonUtils.updateNextNo(CommonEnums.CodeType.CUSTOMERORDER.getIndex());
        }
        else
        {
            slCustomerOrder.preUpdate();
            slCustomerOrderDao.update(slCustomerOrder);
            // 修改订单明细的价格
            if (slCustomerOrder.getQuotationItemList() != null && slCustomerOrder.getQuotationItemList().size() > 0)
            {
                for (int i = 0; i < slCustomerOrder.getQuotationItemList().size(); i++)
                {
                    slCustomerOrderDetailDao.updateTotalAmt(slCustomerOrder.getQuotationItemList().get(i));
                }
            }
        }
        return slCustomerOrder.getNo();
    }
    
    @Transactional(readOnly = false)
    public void delete(SlCustomerOrder slCustomerOrder)
    {
        
        slCustomerOrderDao.delete(slCustomerOrder);
        // 再删除明细
        slCustomerOrderDetailDao.deleteByslCustomerOrderId(slCustomerOrder);
        
    }
    
    /**
     * 确认订单
     * 
     * @param slCustomerOrder
     */
    @Transactional(readOnly = false)
    public Contract updateStatus(SlCustomerOrder slCustomerOrder)
    {
        // 确认的时候添加新的合同
        SlCustomerOrder slCustomerOrderCopy = slCustomerOrderDao.get(slCustomerOrder);
        Contract contract = new Contract();
        contract.setCompany(slCustomerOrderCopy.getCompany());
        Customer customerCopy = customerDao.get(slCustomerOrderCopy.getCustomer());
        contract.setCustomer(customerCopy);
        // 判断编号是否被引用了 是就累加
        contract.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CONTRACT.getIndex().toString()));
        Integer noNum = contractDao.findContractNoisEnable(contract); // 查询编号是否依旧存在
        while (noNum != null && noNum > 0)
        {
            // 将对应编码的nextNo 修改为+1
            CommonUtils.updateNextNo(CommonEnums.CodeType.CONTRACT.getIndex());
            contract.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CONTRACT.getIndex().toString()));
            noNum = contractDao.findContractNoisEnable(contract); // 查询编号是否依旧存在
        }
        contract.setCustomerPo(slCustomerOrderCopy.getCustomerPo());
        contract.setTaxDescript(slCustomerOrderCopy.getTaxDescript());
        contract.setCurrencyType(slCustomerOrderCopy.getCurrencyType());
        contract.setDeliveryWay(slCustomerOrderCopy.getDeliveryWay());
        contract.setDeliveryPlace(slCustomerOrderCopy.getDeliveryPlace());
        contract.setDeliverycity(slCustomerOrderCopy.getDeliverycity());
        contract.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
        contract.setInventorySwitch("0");
        contract.setActiveFlag("1");
        contract.setRemark(slCustomerOrderCopy.getRemark());
        contract.setUser(customerCopy.getSalesman());
        Date date = new Date();
        contract.setOrderDate(date);
        contract.setSlCustomerOrder(slCustomerOrderCopy);
        if (slCustomerOrderCopy.getTotalAmtBefore() != null)
        {
            contract.setTotalAmt(new BigDecimal(slCustomerOrderCopy.getTotalAmtBefore()));
        }
        contract.setOrderArea(slCustomerOrderCopy.getArea());
        Branch branch = new Branch();
        branch.setCompany(slCustomerOrderCopy.getCompany());
        List<Branch> branchList = branchDao.findList(branch);
        contract.setBranch(branchList.get(0));// 默认为第一个
        contract.preInsert();
        contractDao.insert(contract);
        List<SlCustomerOrderDetail> slCustomerOrderDetailList =
            slCustomerOrderDetailDao.getAllByCustomerOrder(slCustomerOrder);
        if (Collections3.isNotEmpty(slCustomerOrderDetailList))
        {
            for (int i = 0; i < slCustomerOrderDetailList.size(); i++)
            {
                SlCustomerOrderDetail scod = slCustomerOrderDetailList.get(i);
                // 首先添加合同工艺
                ContractCraft contractCraft = new ContractCraft();
                contractCraft.setCompany(slCustomerOrder.getCompany());
                contractCraft.setCustomer(slCustomerOrder.getCustomer());
                contractCraft.setNo("");
                contractCraft.setCustomerModel(scod.getCustomerModel());
                contractCraft.setUnitWidth(scod.getUnitWidth());
                contractCraft.setUnitLength(scod.getUnitLength());
                contractCraft.setPnlWidth(scod.getPnlWidth());
                contractCraft.setPnlLength(scod.getPnlLength());
                if (scod.getPnlDivisor() != null)
                {
                    contractCraft.setPnlDivisor(new BigDecimal(scod.getPnlDivisor()));
                }
                contractCraft.setMaterialType(scod.getMaterialType());
                contractCraft.setBoardLevel(scod.getBoardLevel());
                contractCraft.setBoardThickness(scod.getBoardThickness());
                contractCraft.setSurfaceProcess(scod.getSurfaceProcess());
                contractCraft.setCopperCladThickness(scod.getCopperCladThickness());
                contractCraft.setSolderMaskType(scod.getSolderMaskType());
                contractCraft.setCharacterType(scod.getCharacterType());
                contractCraft.setShapingWay(scod.getShapingWay());
                contractCraft.setTestMethod(scod.getTestMethod());
                contractCraft.setLingeSpacing(scod.getLingeSpacing());
                contractCraft.setSmallAperture(scod.getSmallAperture());
                contractCraft.setHalAhole(scod.getHalAhole());
                contractCraft.setBuryBlindHole(scod.getBuryBlindHole());
                contractCraft.setResistance(scod.getResistance());
                contractCraft.setDeliveryUrgent(scod.getDeliveryUrgent());
                contractCraft.setStatus(TypeKey.SL_CRAFT_STATUS_DRAFT.toString());
                contractCraft.setActiveFlag("1");
                contractCraft.setDaore(scod.getDaore());
                contractCraft.setNaiya(scod.getNaiya());
                contractCraft.setPliesnumber(scod.getPliesnumber());
                contractCraft.preInsert();
                contractCraftDao.insert(contractCraft);
                Price price = new Price();
                price.setCompany(slCustomerOrder.getCompany());
                price.setCraft(contractCraft);
                
                BigDecimal deailArea = new BigDecimal(scod.getDeailArea());
                price.setEngineeringFee(scod.getEngineeringFee().multiply(deailArea));
                
                BigDecimal orderArea = new BigDecimal(0);
                if (scod.getPnlWidth() != null && scod.getPnlLength() != null)
                {
                    orderArea = scod.getPnlWidth().multiply(scod.getPnlLength());
                }
                else
                {
                    orderArea = scod.getUnitWidth().multiply(scod.getUnitLength());
                }
                BigDecimal math = new BigDecimal(1000000);
                price.setPrice(scod.getPrice().divide(math).multiply(orderArea));
                price.preInsert();
                priceDao.insert(price);
                ContractDetail contractDetail = new ContractDetail();
                contractDetail.setCompany(slCustomerOrder.getCompany());
                contractDetail.setContract(contract);
                contractDetail.setPricees(price);
                contractDetail.setCraft(contractCraft);
                contractDetail.setQuantity(scod.getSampleAmt().toString());
                contractDetail.setDeliveryDays(scod.getDeliveryDays());
                contractDetail
                    .setDeliveryDate(DateUtils.getDeliveryDate(contract.getOrderDate(), scod.getDeliveryDays()));
                String referType = DictUtils.getValuesByItem(DictItemEnum.PRODUCTION_TYPE).get(0).getRecordId();
                contractDetail.setReferenceType(Long.parseLong(referType));
                BigDecimal totalAmts = new BigDecimal(0);
                if (scod.getTotalAmtBefore() != null)
                {
                    totalAmts = new BigDecimal(scod.getTotalAmtBefore());
                    contractDetail.setSubTotal(totalAmts);
                }
                contractDetail.setOrderDeailArea(scod.getDeailArea());
                contractDetail.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
                contractDetail.setActiveFlag("1");
                contractDetail.setCustomerOrderDetailId(scod);
                contractDetail.preInsert();
                contractDetailDao.insert(contractDetail);
                
                // 客户下单自动生成一个批次的分批交货WC 2018-01-08
                BatchDelivery batch = new BatchDelivery();
                batch.setCompany(contractDetail.getCompany());
                batch.setQuantity(Integer.parseInt(contractDetail.getQuantity()));
                batch.setDeliveryDate(contractDetail.getDeliveryDate());
                batch.setConDetId(contractDetail.getRecordId());
                batch.setOrderDate(contract.getOrderDate());
                batch.preInsert();
                batchDao.insert(batch);
                
                // 操作中间表 先查询中间表 是否有数据 有累加金额 没有初始化
                CollectionTotal total = collectionTotalDao.selectOfcustomerId(contract.getCustomer());
                if (total != null)
                {
                    // 累加金额
                    total.setReceivable(new BigDecimal(0));
                    // add + subtract - multiply * divide / 方法
                    total.setActualReceipt(total.getActualReceipt().add(totalAmts));
                    total.setLastUpdBy(UserUtils.getUser());
                    total.setLastUpdDate(new Date());
                    total.preUpdate();
                    collectionTotalDao.update(total);
                    
                }
                else
                {
                    // 初始化数据
                    CollectionTotal num = new CollectionTotal();
                    num.setCompany(UserUtils.getUser().getCompany());
                    num.setActualReceipt(totalAmts);// 设置客户应收款
                    num.setReceivable(new BigDecimal(0));// 实收
                    num.setCustomer(contract.getCustomer());
                    num.preInsert();
                    collectionTotalDao.insert(num);
                }
            }
        }
        // 添加成功把对应编码的nextNo 修改为+1
        CommonUtils.updateNextNo(CommonEnums.CodeType.CONTRACT.getIndex());
        
        slCustomerOrderDao.updateStatus(slCustomerOrder);
        return contract;
    }
    
    @Transactional(readOnly = false)
    public void deleteDetail(SlCustomerOrderDetail slCustomerOrderDetail)
    {
        slCustomerOrderDetailDao.delete(slCustomerOrderDetail);
    }
    
    /**
     * 查询合同中需要用的客户下单
     * 
     * @param quotation
     * @return chenqiang
     */
    public List<SlCustomerOrder> findListOfVerify(SlCustomerOrder slCustomerOrder)
    {
        return slCustomerOrderDao.findListOfVerifyByCu(slCustomerOrder);
    }
    
    /**
     * 删除合同将客户下单还原
     */
    @Transactional(readOnly = false)
    public void updateChF(String recordId, String companyId)
    {
        slCustomerOrderDetailDao.updateChF(recordId, companyId);
    }
    
    /**
     * 根据客户查询出当月的交货款数
     * 
     * @param customer
     * @return
     */
    public Integer getDeliveryCount(Customer customer)
    {
        
        customer.setCompany(UserUtils.getUser().getCompany());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        String date = format.format(new Date()) + "%";
        customer.setSelectdate(date);
        return slCustomerOrderDao.getDeliveryCount(customer);
    }
    
    /**
     * 根据客户查询出当月的交货款数（上月）
     * 
     * @param customer
     * @return
     */
    public Integer getDeliveryCountlastMonth(Customer customer)
    {
        
        customer.setCompany(UserUtils.getUser().getCompany());
        String mo = "";
        Calendar c = Calendar.getInstance();// 可以对每个时间域单独修改
        int year = c.get(Calendar.YEAR);
        int month = c.get(Calendar.MONTH);
        if ((month + 1) <= 9)
        {
            mo = "0" + (month);
        }
        customer.setSelectdate(year + mo + "%");
        
        return slCustomerOrderDao.getDeliveryCount(customer);
    }
    
    public Integer getinoutTypeCount(Customer customer)
    {
        customer.setCompany(UserUtils.getUser().getCompany());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        String date = format.format(new Date()) + "%";
        customer.setSelectdate(date);
        return slCustomerOrderDao.getinoutTypeCount(customer);
    }
    
    /**
     * 删除合同将客户下单还原
     */
    @Transactional(readOnly = false)
    public void updateDelCh(String recordId)
    {
        slCustomerOrderDetailDao.updateDelCh(recordId);
    }
    
    /**
     * 根据客户下单明细编号获取客户下单所有报给客户的客户下单明细数量
     */
    public Integer getCustNum(String recordId)
    {
        return slCustomerOrderDetailDao.getCustNum(recordId);
    }
    
    /**
     * 修改客户下单如果没有被引用的客户下单明细的状态
     */
    @Transactional(readOnly = false)
    public void updateCustSta(String recordId)
    {
        slCustomerOrderDao.updateCustSta(recordId);
    }
    
    /**
     * 根据合同id获取合同明细再获取客户下单明细
     */
    public List<SlCustomerOrderDetail> getDelCust(String recordId, String companyId)
    {
        return slCustomerOrderDetailDao.getDelCust(recordId, companyId);
    }
    
    /**
     * 接受订单
     */
    @Transactional(readOnly = false)
    public void acceptStatus(SlCustomerOrder slCustomerOrder)
    {
        slCustomerOrderDao.updateStatus(slCustomerOrder);
        Contract contract = contractDao.getContractBycust(slCustomerOrder);
        contract.setStatus(TypeKey.SL_CONTRACT_STATUS_DRAFT.toString());
        contractDao.updateContractStatus(contract);
        // 接受订单要把订单数据替换成最新合同数据
        List<SlCustomerOrderDetail> list = slCustomerOrderDetailDao.getAllByCustomerOrder(slCustomerOrder);
        slCustomerOrder.setDeliveryPlace(contract.getDeliveryPlace());
        slCustomerOrder.setCustomerPo(contract.getCustomerPo());
        slCustomerOrder.setDeliveryWay(contract.getDeliveryWay());
        slCustomerOrder.setCurrencyType(contract.getCurrencyType());
        slCustomerOrder.setTaxDescript(contract.getTaxDescript());
        slCustomerOrder.setTotalAmtBefore(contract.getTotalAmt().toString());
        slCustomerOrder.setArea(contract.getOrderArea());
        slCustomerOrder.setDeliverycity(contract.getDeliverycity());
        List<DictValue> taxDescriptList = DictUtils.getValuesByItem(DictItemEnum.TAX_DESCRIPTS);
        BigDecimal tax = new BigDecimal(1);
        if (contract.getTaxDescript() != null)
        {
            for (int i = 0; i < taxDescriptList.size(); i++)
            {
                if (contract.getTaxDescript().toString().equals(taxDescriptList.get(i).getRecordId()))
                {
                    if (taxDescriptList.get(i).getValue() != null && taxDescriptList.get(i).getValue().contains("税"))
                    {
                        String taxArray[] = taxDescriptList.get(i).getValue().split("税");
                        if (taxArray.length > 1)
                        {
                            String taxTwoCopy = taxDescriptList.get(i).getValue().split("税")[1];
                            double taxTwo = Double.parseDouble(taxTwoCopy.split("%")[0]) / 100;
                            tax = new BigDecimal(1 + taxTwo);
                        }
                    }
                }
            }
        }
        if (contract.getTotalAmt() != null)
        {
            slCustomerOrder.setTotalAmt(contract.getTotalAmt().multiply(tax).toString());
        }
        slCustomerOrderDao.update(slCustomerOrder);
        contract.setCompany(slCustomerOrder.getCompany());
        List<ContractDetail> listContractDeail = contractDetailDao.findDetailListByContractId(contract);
        for (int i = 0; i < listContractDeail.size(); i++)
        {
            Price price = new Price();
            if (listContractDeail.get(i).getPricees() != null)
            {
                price = priceDao.get(listContractDeail.get(i).getPricees().getRecordId());
            }
            ContractCraft contractCraft = new ContractCraft();
            if (listContractDeail.get(i).getCraft() != null)
            {
                contractCraft = contractCraftDao.get(listContractDeail.get(i).getCraft());
            }
            for (int j = 0; j < list.size(); j++)
            {
                if (listContractDeail.get(i).getCustomerOrderDetailId() != null && listContractDeail.get(i)
                    .getCustomerOrderDetailId()
                    .getRecordId()
                    .equals(list.get(j).getRecordId()))
                {
                    list.get(j).setUnitWidth(contractCraft.getUnitWidth());
                    list.get(j).setUnitLength(contractCraft.getUnitLength());
                    list.get(j).setPnlWidth(contractCraft.getPnlWidth());
                    list.get(j).setPnlLength(contractCraft.getPnlLength());
                    if (contractCraft.getPnlDivisor() != null)
                    {
                        list.get(j).setPnlDivisor(contractCraft.getPnlDivisor().intValue());
                    }
                    else
                    {
                        list.get(j).setPnlDivisor(1);
                    }
                    list.get(j).setCustomerModel(contractCraft.getCustomerModel());
                    if (listContractDeail.get(i).getQuantity() != null)
                    {
                        list.get(j).setSampleAmt(Integer.valueOf(listContractDeail.get(i).getQuantity()));
                    }
                    list.get(j).setBoardLevel(contractCraft.getBoardLevel());
                    list.get(j).setMaterialType(contractCraft.getMaterialType());
                    list.get(j).setBoardThickness(contractCraft.getBoardThickness());
                    list.get(j).setCopperCladThickness(contractCraft.getCopperCladThickness());
                    list.get(j).setSurfaceProcess(contractCraft.getSurfaceProcess());
                    list.get(j).setSolderMaskType(contractCraft.getSolderMaskType());
                    list.get(j).setCharacterType(contractCraft.getCharacterType());
                    list.get(j).setShapingWay(contractCraft.getShapingWay());
                    list.get(j).setTestMethod(contractCraft.getTestMethod());
                    list.get(j).setLingeSpacing(contractCraft.getLingeSpacing());
                    list.get(j).setSmallAperture(contractCraft.getSmallAperture());
                    list.get(j).setHalAhole(contractCraft.getHalAhole());
                    list.get(j).setBuryBlindHole(contractCraft.getBuryBlindHole());
                    list.get(j).setResistance(contractCraft.getResistance());
                    list.get(j).setDeliveryUrgent(contractCraft.getDeliveryUrgent());
                    list.get(j).setDaore(contractCraft.getDaore());
                    list.get(j).setNaiya(contractCraft.getNaiya());
                    list.get(j).setPliesnumber(contractCraft.getPliesnumber());
                    
                    if (listContractDeail.get(i).getSubTotal() != null)
                    {
                        list.get(i).setTotalAmtBefore(listContractDeail.get(i).getSubTotal().toString());
                    }
                    list.get(i).setDeailArea(listContractDeail.get(i).getOrderDeailArea());
                    if (listContractDeail.get(i).getSubTotal() != null)
                    {
                        list.get(i).setTotalAmt(listContractDeail.get(i).getSubTotal().multiply(tax).toString());
                    }
                    list.get(j).setDeliveryDays(listContractDeail.get(i).getDeliveryDays());
                    BigDecimal orderArea = new BigDecimal(0);
                    BigDecimal math = new BigDecimal(1000000);
                    if (contractCraft.getPnlWidth() != null && contractCraft.getPnlLength() != null)
                    {
                        orderArea = contractCraft.getPnlWidth().multiply(contractCraft.getPnlLength());
                        list.get(j).setPrice(price.getPrice().multiply(math).divide(orderArea, 3));
                    }
                    else
                    {
                        list.get(j).setPrice(orderArea);
                    }
                    BigDecimal engArea =
                        orderArea.divide(math).multiply(new BigDecimal(listContractDeail.get(i).getQuantity()));
                    list.get(j).setEngineeringFee(price.getEngineeringFee().divide(engArea));
                    list.get(j).setDeliveryDays(listContractDeail.get(i).getDeliveryDays());
                }
                slCustomerOrderDetailDao.update(list.get(j));
            }
        }
    }
    
    /**
     * 拒绝订单
     */
    @Transactional(readOnly = false)
    public void refuseStatus(SlCustomerOrder slCustomerOrder)
    {
        slCustomerOrderDao.updateStatus(slCustomerOrder);
        Contract contract = contractDao.getContractBycust(slCustomerOrder);
        contract.setStatus(TypeKey.SL_CONTRACT_STATUS_CANCELED.toString());
        contractDao.updateContractStatus(contract);
        
        List<ContractDetail> detailList = contractDao.findDetails(contract);
        if (Collections3.isNotEmpty(detailList))
        {
            for (ContractDetail detail : detailList)
            {
                // 作废当前合同明细
                detail.setStatus(TypeKey.SL_CONTRACT_STATUS_CANCELED.toString());
                contractDetailDao.updateContractDetailStatus(detail);
                // WC 2017-10-10 作废分批交货
                batchDao.deleteByConDetId(detail);
            }
        }
    }
    
    /**
     * 比较获取合同和订单的数据
     */
    public SlCustomerOrder getChangeDeail(SlCustomerOrder slCustomerOrder)
    {
        // 获取该订单的明细
        List<SlCustomerOrderDetail> list = slCustomerOrderDetailDao.getAllByCustomerOrder(slCustomerOrder);
        Contract contract = contractDao.getContractBycust(slCustomerOrder);
        slCustomerOrder.setDeliverPlaceCopy(contract.getDeliveryPlace());
        slCustomerOrder.setCustomerPoCopy(contract.getCustomerPo());
        slCustomerOrder.setDeliveryWayCopy(contract.getDeliveryWay());
        slCustomerOrder.setCurrencyTypeCopy(contract.getCurrencyType());
        slCustomerOrder.setTaxDescriptCopy(contract.getTaxDescript());
        slCustomerOrder.setDeliverycityCopy(contract.getDeliverycity());
        if (contract.getTotalAmt() != null)
        {
            slCustomerOrder.setTotalAmtCopy(contract.getTotalAmt().toString());
        }
        contract.setCompany(slCustomerOrder.getCompany());
        List<ContractDetail> listContractDeail = contractDetailDao.findDetailListByContractId(contract);
        for (int i = 0; i < listContractDeail.size(); i++)
        {
            Price price = new Price();
            if (listContractDeail.get(i).getPricees() != null)
            {
                price = priceDao.get(listContractDeail.get(i).getPricees().getRecordId());
            }
            ContractCraft contractCraft = new ContractCraft();
            if (listContractDeail.get(i).getCraft() != null)
            {
                contractCraft = contractCraftDao.get(listContractDeail.get(i).getCraft());
            }
            for (int j = 0; j < list.size(); j++)
            {
                if (listContractDeail.get(i).getCustomerOrderDetailId() != null && listContractDeail.get(i)
                    .getCustomerOrderDetailId()
                    .getRecordId()
                    .equals(list.get(j).getRecordId()))
                {
                    if (contractCraft.getUnitLength() != null && contractCraft.getUnitWidth() != null)
                    {
                        String pcsSizeCopy =
                            contractCraft.getUnitLength().toString() + "*" + contractCraft.getUnitWidth().toString();
                        list.get(j).setPcsSizeCopy(pcsSizeCopy);
                    }
                    if (contractCraft.getPnlLength() != null && contractCraft.getPnlWidth() != null
                        && contractCraft.getPnlDivisor() != null)
                    {
                        String deliverSizeCopy = contractCraft.getPnlLength().toString() + "*"
                            + contractCraft.getPnlWidth().toString() + "/" + contractCraft.getPnlDivisor().toString();
                        list.get(j).setDeliverSizeCopy(deliverSizeCopy);
                    }
                    if (list.get(j).getUnitLength() != null && list.get(j).getUnitWidth() != null)
                    {
                        String pcsSize =
                            list.get(j).getUnitLength().toString() + "*" + list.get(j).getUnitWidth().toString();
                        list.get(j).setPcsSize(pcsSize);
                    }
                    if (list.get(j).getPnlLength() != null && list.get(j).getPnlWidth() != null
                        && list.get(j).getPnlDivisor() != null)
                    {
                        String deliverSize = list.get(j).getPnlLength().toString() + "*"
                            + list.get(j).getPnlWidth().toString() + "/" + list.get(j).getPnlDivisor().toString();
                        list.get(j).setDeliverSize(deliverSize);
                    }
                    list.get(j).setUnitWidthCopy(contractCraft.getUnitWidth());
                    list.get(j).setUnitLengthCopy(contractCraft.getUnitLength());
                    list.get(j).setPnlWidthCopy(contractCraft.getPnlWidth());
                    list.get(j).setPnlLengthCopy(contractCraft.getPnlLength());
                    list.get(j).setPnlDivisorCopy(contractCraft.getPnlDivisor());
                    list.get(j).setCustomerModelCopy(contractCraft.getCustomerModel());
                    if (listContractDeail.get(i).getQuantity() != null)
                    {
                        list.get(j).setSampleAmtCopy(Integer.valueOf(listContractDeail.get(i).getQuantity()));
                    }
                    list.get(j).setBoardLevelCopy(contractCraft.getBoardLevel());
                    list.get(j).setMaterialTypeCopy(contractCraft.getMaterialType());
                    list.get(j).setBoardThicknessCopy(contractCraft.getBoardThickness());
                    list.get(j).setCopperCladThicknessCopy(contractCraft.getCopperCladThickness());
                    list.get(j).setSurfaceProcessCopy(contractCraft.getSurfaceProcess());
                    list.get(j).setSolderMaskTypeCopy(contractCraft.getSolderMaskType());
                    list.get(j).setCharacterTypeCopy(contractCraft.getCharacterType());
                    list.get(j).setShapingWayCopy(contractCraft.getShapingWay());
                    list.get(j).setTestMethodCopy(contractCraft.getTestMethod());
                    list.get(j).setLingeSpacingCopy(contractCraft.getLingeSpacing());
                    list.get(j).setSmallApertureCopy(contractCraft.getSmallAperture());
                    list.get(j).setHalAholeCopy(contractCraft.getHalAhole());
                    list.get(j).setBuryBlindHoleCopy(contractCraft.getBuryBlindHole());
                    list.get(j).setResistanceCopy(contractCraft.getResistance());
                    list.get(j).setDeliveryUrgentCopy(contractCraft.getDeliveryUrgent());
                    list.get(j).setDaoreCopy(contractCraft.getDaore());
                    list.get(j).setNaiyaCopy(contractCraft.getNaiya());
                    list.get(j).setPliesnumberCopy(contractCraft.getPliesnumber());
                    if (listContractDeail.get(i).getSubTotal() != null)
                    {
                        list.get(i).setSubTotalCopy(listContractDeail.get(i).getSubTotal().toString());
                    }
                    list.get(j).setDeailAreaCopy(listContractDeail.get(i).getOrderDeailArea());
                    list.get(j).setDeliveryDaysCopy(listContractDeail.get(i).getDeliveryDays());
                    BigDecimal orderArea = new BigDecimal(0);
                    BigDecimal math = new BigDecimal(1000000);
                    if (contractCraft.getPnlWidth() != null && contractCraft.getPnlLength() != null)
                    {
                        orderArea = contractCraft.getPnlWidth().multiply(contractCraft.getPnlLength());
                        list.get(j).setPriceCopy(price.getPrice().multiply(math).divide(orderArea, 3));
                    }
                    else
                    {
                        orderArea = contractCraft.getUnitWidth().multiply(contractCraft.getUnitLength());
                        list.get(j).setPriceCopy(price.getPrice().multiply(math).divide(orderArea, 3));
                    }
                    BigDecimal engArea =
                        orderArea.divide(math).multiply(new BigDecimal(listContractDeail.get(i).getQuantity()));
                    list.get(j).setEngineeringFeeCopy(price.getEngineeringFee().divide(engArea));
                }
            }
        }
        slCustomerOrder.setQuotationItemList(list);
        return slCustomerOrder;
    }
    
    /**
     * 获取订单的状态
     */
    public SlCustomerOrder getStatus(SlCustomerOrder slCustomerOrder)
    {
        return slCustomerOrderDao.getStatus(slCustomerOrder);
    }
    
    public Map<String, Object> loadWipCusOfPC(User user)
    {
        
        Notification notification = new Notification();
        notification.setCompany(user.getCompany());
        notification.setStatus(TypeKey.SL_NOTIFICATION_STATUS_FEED.toString());
        notification.setStatus1(TypeKey.SL_NOTIFICATION_STATUS_SUPPLEMENTARYFOOD.toString());
        notification.setCustomerId(user.getCustomer().getRecordId());
        notification.setCustomerNo(user.getCustomer().getNo());
        return selectWipAndDealForCustomerPC(notification);
    }
    
    public Map<String, Object> selectWipAndDealForCustomerPC(Notification query)
    {
        // 查询Wip数据
        List<Wip> list = produceRecordDao.selectWipOfWeChatByCustomer(query);
        
        // 若查询不到数据，就返回空
        Map<String, Object> result = Maps.newHashMap();
        if (Collections3.isEmpty(list))
        {
            result.put("list", Lists.newArrayList());
            result.put("stops", Lists.newArrayList());
            result.put("totalArea", 0);
            result.put("warn", false);
            return result;
        }
        
        // 是否告警
        ParameterSet set = ConfigUtils.getParameterSetByJP(ConstKey.PARAMETERSET_BAOFEILV, query.getCompany());
        boolean warn = false;
        if (set != null)
        {
            // 若参数值为空，设置默认值
            if (StringUtils.isEmpty(set.getParameterValue()))
            {
                set.setParameterValue(ConstKey.PARAMETERSET_BAOFEILV_VALUE);
            }
            
            // 判断是否启动告警
            warn = StringUtils.isNotEmpty(set.getStart()) && set.getStart().equals(TypeKey.YES.toString());
        }
        
        // 总结存数
        Map<String, WipProcessStop> processStops = Maps.newHashMap();
        
        // 总面积
        String totalArea = "";
        Integer count = 1;
        for (Wip wip : list)
        {
            // 设置当前Wip的编号
            wip.setNum(count.toString());
            
            count++;
            
            // 投补料信息
            Feeding feeding = wip.getFeeding();
            if (feeding == null)
            {
                continue;
            }
            
            // 通知单信息
            Notification notification = wip.getNotification();
            if (notification == null)
            {
                continue;
            }
            
            // 设置状态
            wip.setBitSuspend(feeding.getBitSuspend().toString());
            
            // 设置生产编号
            wip.setCraftNo(notification.getCraftNo());
            
            // 设置订单/投料数量
            wip.setOrderFeedPcsQuantity(feeding.getOrderQuantity() + "/" + feeding.getPcsQuantity());
            
            // 设置A、B板数量
            StringBuilder buffer = new StringBuilder();
            buffer.append("A:");
            buffer.append(StringUtils.isNotBlank(feeding.getBoardAPnlQty()) ? feeding.getBoardAPnlQty() : "0");
            buffer.append("/");
            buffer.append("B:");
            buffer.append(StringUtils.isNotBlank(feeding.getBoardBPnlQty()) ? feeding.getBoardBPnlQty() : "0");
            wip.setBoardABPnlQty(buffer.toString());
            
            // 设置交货期限
            if (null != notification.getDeliveryDate())
            {
                wip.setDeliveryDate(DateUtils.formatDate(notification.getDeliveryDate()));
            }
            
            // 一条Wip的工序结存信息
            Map<String, WipProcessStop> wipProcessStopList = Maps.newHashMap();
            wip.setWipProcessStopList(wipProcessStopList);
            
            // WIP是否和单
            boolean ishe =
                StringUtils.isNotEmpty(notification.getCraftNo()) && notification.getCraftNo().indexOf(",") > 0;
            
            // 循环处理过数记录
            for (ProduceRecord produceRecord : wip.getProduceRecordList())
            {
                // 交板标志为空，不处理
                if (StringUtils.isEmpty(produceRecord.getHandOverFlag()))
                {
                    continue;
                }
                
                // 所有Wip的工序结存
                WipProcessStop processStop = processStops.get(produceRecord.getProcessId());
                if (null == processStop)
                {
                    processStop = new WipProcessStop();
                    processStop.setaQty("0");
                    processStop.setaArea(0.0);
                    processStop.setbQty("0");
                    processStop.setbArea(0.0);
                    processStop.setArea(0.0);
                    processStop.setAbQty("");
                    processStop.setRecordId(produceRecord.getProcessId());
                    processStop.setName(produceRecord.getProcess().getName());
                    processStop.setCategory(produceRecord.getProcess().getCategory());
                    processStops.put(produceRecord.getProcessId(), processStop);
                }
                
                // 当前Wip的结存信息
                WipProcessStop wipProcessStop = wipProcessStopList.get(produceRecord.getProcessId());
                if (null == wipProcessStop)
                {
                    wipProcessStop = new WipProcessStop();
                    wipProcessStop.setaQty("0");
                    wipProcessStop.setaArea(0.0);
                    wipProcessStop.setbQty("0");
                    wipProcessStop.setbArea(0.0);
                    wipProcessStop.setAbQty("");
                    wipProcessStop.setArea(0.0);
                    wipProcessStop.setRecordId(produceRecord.getProcessId());
                    wipProcessStop.setName(produceRecord.getProcess().getName());
                    wipProcessStop.setCategory(produceRecord.getProcess().getCategory());
                    wipProcessStopList.put(produceRecord.getProcessId(), wipProcessStop);
                }
                
                // 累加A板
                if (StringUtils.isNotEmpty(produceRecord.getTakeOverQtyA()))
                {
                    // 累加当前Wip的A板
                    wipProcessStop
                        .setaQty(wipProcessStop.getaQty() + Integer.parseInt(produceRecord.getTakeOverQtyA()));
                    
                    // 累加总Wip的A板
                    processStop.setaQty(processStop.getaQty() + Integer.parseInt(produceRecord.getTakeOverQtyA()));
                }
                
                // 累加B板
                if (StringUtils.isNotEmpty(produceRecord.getTakeOverQtyB()))
                {
                    // 累加当前Wip的B板
                    wipProcessStop
                        .setbQty(wipProcessStop.getbQty() + Integer.parseInt(produceRecord.getTakeOverQtyB()));
                    
                    // 累加总Wip的B板
                    processStop.setbQty(processStop.getbQty() + Integer.parseInt(produceRecord.getTakeOverQtyB()));
                }
                
                Double areaA = 0.0;
                Double areaB = 0.0;
                
                // 流程卡的Set面积
                CardA cardA = notification.getCardA();
                
                // 流程卡的Set面积
                Double tempNum = MathUtils
                    .round(MathUtils.mul(cardA.getSetLength().doubleValue(), cardA.getSetWidth().doubleValue()), 4);
                
                if (ishe)
                {
                    // 获取合单批次明细工艺
                    List<ProduceBatchDetailCraft> detailCraftList = notification.getProduceBatchDetailCraftList();
                    // 获取合单流程卡分编号工艺
                    List<BoardPartCraft> boardPartCraftList = notification.getBoardPartCraftList();
                    
                    // 临时的
                    List<ProduceBatchDetailCraft> tempArr = Lists.newArrayList();
                    
                    if (!Collections3.isEmpty(boardPartCraftList) && !Collections3.isEmpty(boardPartCraftList))
                    {
                        for (ProduceBatchDetailCraft detailCraft : detailCraftList)
                        {
                            if (detailCraft.getProduceBatchDetail()
                                .getRecordId()
                                .equals(produceRecord.getProduceBatchDetailId()))
                            {
                                for (BoardPartCraft boardPartCraft : boardPartCraftList)
                                {
                                    if (detailCraft.getCraftNo().equals(boardPartCraft.getCraftNo()))
                                    {
                                        detailCraft.setBoardPartCraft(boardPartCraft);
                                        tempArr.add(detailCraft);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    
                    if (tempArr.size() > 0)
                    {
                        for (ProduceBatchDetailCraft detailCraft : tempArr)
                        {
                            if (StringUtils.isNotEmpty(detailCraft.getBoardPartCraft().getSetLength())
                                && StringUtils.isNotEmpty(detailCraft.getBoardPartCraft().getSetWidth())
                                && StringUtils.isNotEmpty(detailCraft.getBoardPartCraft().getPnlDivisor()))
                            {
                                
                                Double tempNumCraft = MathUtils
                                    .round(MathUtils.mul(Double.valueOf(detailCraft.getBoardPartCraft().getSetLength()),
                                        Double.valueOf(detailCraft.getBoardPartCraft().getSetWidth())), 4);
                                
                                if (null != detailCraft.getQtyPcsA())
                                {
                                    Double a = MathUtils.div(MathUtils.div(
                                        MathUtils.round(MathUtils.mul(detailCraft.getQtyPcsA(), tempNumCraft), 4),
                                        Double.valueOf(detailCraft.getBoardPartCraft().getPnlDivisor()),
                                        4), new Double(1000000), 4);
                                    
                                    areaA = MathUtils.round(MathUtils.add(areaA, a), 4);
                                }
                                
                                if (null != detailCraft.getQtyPcsB())
                                {
                                    Double b = MathUtils.div(MathUtils.div(
                                        MathUtils.round(MathUtils.mul(detailCraft.getQtyPcsB(), tempNumCraft), 4),
                                        Double.valueOf(detailCraft.getBoardPartCraft().getPnlDivisor()),
                                        4), new Double(1000000), 4);
                                    
                                    areaB = MathUtils.round(MathUtils.add(areaB, b), 4);
                                }
                            }
                        }
                    }
                    else
                    {
                        // 合单，但是生产编号相同
                        Double aPcs = new Double(
                            null == produceRecord.getTakeOverQtyPcsA() ? 0 : produceRecord.getTakeOverQtyPcsA());
                        Double bPcs = new Double(
                            null == produceRecord.getTakeOverQtyPcsB() ? 0 : produceRecord.getTakeOverQtyPcsB());
                        
                        if (aPcs > 0)
                        {
                            areaA = MathUtils.div(MathUtils.div(MathUtils.round(MathUtils.mul(aPcs, tempNum), 4),
                                cardA.getPnlDivisor().doubleValue(),
                                4), new Double(1000000), 4);
                        }
                        
                        if (bPcs > 0)
                        {
                            areaB = MathUtils.div(MathUtils.div(MathUtils.round(MathUtils.mul(bPcs, tempNum), 4),
                                cardA.getPnlDivisor().doubleValue(),
                                4), new Double(1000000), 4);
                        }
                    }
                }
                else
                {
                    // 不是和单的
                    Double aPcs =
                        new Double(null == produceRecord.getTakeOverQtyPcsA() ? 0 : produceRecord.getTakeOverQtyPcsA());
                    Double bPcs =
                        new Double(null == produceRecord.getTakeOverQtyPcsB() ? 0 : produceRecord.getTakeOverQtyPcsB());
                    
                    if (aPcs > 0)
                    {
                        areaA = MathUtils.div(MathUtils.div(MathUtils.round(MathUtils.mul(aPcs, tempNum), 4),
                            cardA.getPnlDivisor().doubleValue(),
                            4), new Double(1000000), 4);
                    }
                    
                    if (bPcs > 0)
                    {
                        areaB = MathUtils.div(MathUtils.div(MathUtils.round(MathUtils.mul(bPcs, tempNum), 4),
                            cardA.getPnlDivisor().doubleValue(),
                            4), new Double(1000000), 4);
                    }
                }
                
                // 总面积
                Double area = MathUtils.round(MathUtils.add(areaA, areaB), 4);
                
                // 累加当前Wip的A板面积、B板面积、总面积
                wipProcessStop.setaArea(MathUtils.round(MathUtils.add(wipProcessStop.getaArea(), areaA), 1));
                wipProcessStop.setbArea(MathUtils.round(MathUtils.add(wipProcessStop.getbArea(), areaB), 1));
                wipProcessStop.setArea(MathUtils.round(MathUtils.add(wipProcessStop.getArea(), area), 1));
                
                // 累加总Wip的A板面积、B板面积、总面积
                processStop.setaArea(MathUtils.round(MathUtils.add(processStop.getaArea(), areaA), 4));
                processStop.setbArea(MathUtils.round(MathUtils.add(processStop.getbArea(), areaB), 4));
                processStop.setArea(MathUtils.round(MathUtils.add(processStop.getArea(), area), 4));
            }
            
            // 设置连接字符串
            wipProcessStopList.forEach((k, v) -> {
                if (StringUtils.isNotBlank(v.getaQty()) && StringUtils.isNotBlank(v.getbQty()))
                {
                    v.setAbQty(
                        "A:" + v.getaQty() + "(" + v.getaArea() + ")" + "/B:" + v.getbQty() + "(" + v.getbArea() + ")");
                }
                else if (StringUtils.isNotBlank(v.getaQty()))
                {
                    v.setAbQty("A:" + v.getaQty() + "(" + v.getaArea() + ")" + "/B:-");
                }
                else if (StringUtils.isNotBlank(v.getbQty()))
                {
                    v.setAbQty("A:-" + "/B:" + v.getbQty() + "(" + v.getbArea() + ")");
                }
                else
                {
                    v.setAbQty("");
                }
            });
            
            // 设置欠数
            Integer feedQty = null != notification.getFeedPcsQty() ? notification.getFeedPcsQty() : 0;
            Integer compQty = null != notification.getCompletedQty() ? notification.getCompletedQty() : 0;
            Integer num = feedQty - compQty;
            wip.setQianshu(num.toString());
            
            // 设置已入库数
            wip.setStocks("0");
            if (feeding.getProduceBatch() != null
                && !Collections3.isEmpty(feeding.getProduceBatch().getProduceBatchDetail()))
            {
                Integer qty = 0;
                for (ProduceBatchDetail detail : feeding.getProduceBatch().getProduceBatchDetail())
                {
                    if (null != detail.getQtyPcsA())
                    {
                        qty += detail.getQtyPcsA();
                    }
                }
                if (qty > 0)
                {
                    wip.setStocks(qty.toString());
                }
            }
            
            // 设置下单/补料日期
            if (null != notification.getOrderDate())
            {
                wip.setOrderDate(DateUtils.formatDateTime(notification.getOrderDate()));
            }
            
            // 设置发料日期
            if (null != feeding.getAcDistributeDate())
            {
                wip.setAcDistributeDate(DateUtils.formatDateTime(feeding.getAcDistributeDate()));
            }
            
            // 仅仅取一个
            if (wipProcessStopList.size() > 0)
            {
                for (String key : wipProcessStopList.keySet())
                {
                    WipProcessStop pro = wipProcessStopList.get(key);
                    wip.setWipProcessStop(pro);
                    break;
                }
            }
            
            // 设置通知单号
            wip.setNno(notification.getNo());
            
            // 设置加急标志
            wip.setUrgentFlag(notification.getUrgentFlag().equals(TypeKey.YES.toString()) ? "加急" : "不加急");
            
            // 设置投料编号
            wip.setFno(feeding.getNo());
            
            // 设置客户型号
            wip.setCustomerModel(notification.getCustomerModel());
            
            // 算预警率
            if (warn)
            {
                double discardQty = 0.0;
                
                if (feeding.getProduceBatch() != null
                    && StringUtils.isNotEmpty(feeding.getProduceBatch().getDiscardQty()))
                {
                    discardQty = Double.parseDouble(feeding.getProduceBatch().getDiscardQty());
                }
                
                // 算出比率
                double ratio = MathUtils.div(discardQty, Double.parseDouble(feeding.getPcsQuantity().toString()), 2);
                
                // 算出报废率
                double warnRatio = MathUtils.div(Double.parseDouble(set.getParameterValue()), new Double(100.00), 2);
                
                // 如果超过预警率
                wip.setWarn(ratio >= warnRatio);
            }
            
            // 设置状态
            if (StringUtils.isBlank(wip.getDeliveryDate()))
            {
                wip.setState("进度延期");
            }
            else
            {
                // 当前时间
                Calendar currTime = Calendar.getInstance();
                
                // 交货日期默认为该天的23点59分59秒
                Calendar deliTime = Calendar.getInstance();
                deliTime.setTime(DateUtils.parseDate(wip.getDeliveryDate()));
                deliTime.set(Calendar.HOUR_OF_DAY, 23);
                deliTime.set(Calendar.MINUTE, 59);
                deliTime.set(Calendar.SECOND, 59);
                deliTime.set(Calendar.MILLISECOND, 59);
                
                if (currTime.getTimeInMillis() >= deliTime.getTimeInMillis())
                {
                    wip.setState("进度延期");
                }
                else
                {
                    if (StringUtils.isEmpty(wip.getAcDistributeDate()))
                    {
                        wip.setState("还未发料");
                    }
                    else
                    {
                        // 发料日期
                        Date aDate = DateUtils.parseDate(wip.getAcDistributeDate());
                        
                        // 已过去天数
                        int pastdays = (int)((currTime.getTime().getTime() - aDate.getTime()) / 86400000);
                        
                        // 订单总天数
                        int countdays = (int)((deliTime.getTime().getTime() - aDate.getTime()) / 86400000);
                        
                        // 若订单总天数小于等于0，说明进度延期
                        if (countdays <= 0)
                        {
                            wip.setState("进度延期");
                        }
                        else
                        {
                            if (StringUtils.isEmpty(wip.getFeeding().getOrderQuantity())
                                || Integer.parseInt(wip.getFeeding().getOrderQuantity()) == 0)
                            {
                                wip.setState("进度正常");
                            }
                            else
                            {
                                int re = 0;
                                try
                                {
                                    int tmpStock = wip.getStocks() == null ? 0 : Integer.parseInt(wip.getStocks());
                                    re = pastdays / countdays
                                        - tmpStock / Integer.parseInt(wip.getFeeding().getOrderQuantity());
                                }
                                catch (Exception e)
                                {
                                }
                                
                                // 存在风险 黄色标示, 进度正常 绿色标示
                                wip.setState(re > 0 ? "存在风险" : "进度正常");
                            }
                        }
                    }
                }
            }
        }
        
        // 总结存面积
        double total = 0;
        for (String key : processStops.keySet())
        {
            WipProcessStop stop = processStops.get(key);
            total = MathUtils.round(MathUtils.add(total, stop.getArea()), 4);
        }
        String num = DecimalFormat.getNumberInstance().format(total);
        totalArea = "汇总(㎡):" + num;
        
        // 设置连接字符串
        processStops.forEach((k, v) -> {
            v.setAbQty("A:" + v.getaQty() + "/B:" + v.getbQty());
        });
        
        result.put("list", list);
        result.put("stops", processStops);
        result.put("totalArea", totalArea);
        result.put("warn", warn);
        return result;
    }
}