<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.stock.dao.RawmaterialStockDao">

	<resultMap id="RawmaterialStockMap" type="RawmaterialStock">
		<id property="recordId" column="recordId" />
		<result property="company.recordId" column="companyId" />
		<result property="material.recordId" column="materialId" />
		<result property="storehouse.recordId" column="storeHouseId" />
		<result property="feeding.recordId" column="feedingId" />
		<result property="feding.recordId" column="fedingId" />
		<result property="inoutType" column="inoutType" />
		<result property="purchasingDtlId" column="purchasingDtlId" />
		<result property="returnsNo" column="returnsNo" />
		<result property="quantity" column="quantity" />
		<result property="amount" column="amount" />
		<result property="operateDate" column="operateDate" />
		<result property="administrator" column="administrator" />
		<result property="writeOffId" column="writeOffId" />
		<result property="writeOffCause" column="writeOffCause" />
		<result property="status" column="status" />
		<result property="operator" column="operator" />
		<result property="createdDate" column="createdDate" />
		<result property="lastUpdDate" column="lastUpdDate" />
		<result property="remark" column="remark" />
		<result property="craftNo" column="craftNo" />
		<result property="price" column="price"/>
		<result property="cost" column="cost" />
		<result property="contractNo" column="contractNo"/>
		<result property="giveNum" column="giveNum"/>
		<result property="purchasingNo" column="purchasingNo"/>
		<result property="stockPlaceId" column="stockPlaceId"/>
		<result property="stockPlaceComId" column="stockPlaceComId"/>
		

		<association property="company" column="companyId"
			javaType="Company">
			<id property="recordId" column="company.recordId" />
			<result property="name" column="company.name" />
		</association>

		<association property="createdBy" column="createdBy"
			javaType="User">
			<id property="recordId" column="createdBy.recordId" />
			<result property="userName" column="createdBy.userName" />
		</association>

		<association property="lastUpdBy" column="lastUpdBy"
			javaType="User">
			<id property="recordId" column="lastUpdBy.recordId" />
		</association>
		
		<association property="feeding" column="feedingId"
			javaType="Feeding">
			<id property="recordId" column="feeding.recordId" />
		</association>
		
		<association property="feding" column="fedingId"
			javaType="Replenish">
			<id property="recordId" column="feding.recordId" />
		</association>

		<association property="material" column="materialId"
			javaType="Material">
			<id property="recordId" column="material.recordId" />
			<result property="name" column="material.name" />
			<result property="no" column="material.no" />
			<result property="lowerLimit" column="material.lowerLimit" />
			<result property="stocks" column="material.stocks" />
			<result property="specification" column="material.specification" />
		</association>
		
		<association property="supplier" column="supplierId"
			javaType="Supplier">
			<id property="recordId" column="supplier.recordId" />
			<result property="name" column="supplier.name" />
			<result property="shortName" column="supplier.shortName" />
			<result property="no" column="supplier.no" />
		</association>
		
		<association property="storehouse" column="storeHouseId" javaType="StoreHouse">
			<id property="recordId" column="storehouse.recordId" />
			<result property="name" column="storehouse.name" />
		</association>
			
		<association property="materialUse" column="materialUseId" javaType="MaterialUse">
			<id property="recordId" column="materialUse.recordId" />
		</association>
		<association property="notification" column="notificationId" javaType="Notification">
			<id property="recordId" column="notification.recordId" />
		</association>
	</resultMap>

	<select id="selectStoreHouseById" parameterType="Long"
		resultType="StoreHouse">
		select recordId,name from md_store_house where recordId=#{recordId}
	</select>
	<!-- <select id="selectContractDetailById" parameterType="Long" resultType="ContractDetail"> 
		select * from sl_contract_detail where recordId=#{recordId} </select> <select 
		id="selectUserById" parameterType="Long" resultType="User"> select recordId,userName 
		from sm_user where recordId=#{recordId} </select> -->

	<sql id="MaterialStoreColumns">
		a.recordid AS "recordId",
		a.companyid AS "company.recordId",
		a.materialid AS "material.recordId",
		a.feedingId AS "feeding.recordId",
		a.fedingId AS "feding.recordId",
		a.materialUseId AS "materialUse.recordId",
		a.supplierid AS "supplier.recordId",
		a.storehouseid AS "storehouse.recordId",
		a.inouttype AS "inoutType",
		a.returnsno AS "returnsNo",
		a.purchasingdtlid AS "purchasingDtlId",
		a.quantity AS "quantity",
		a.amount AS "amount",
		a.operatedate AS "operateDate",
		a.administrator AS "administrator",
		a.operator AS "operator",
		a.writeoffid AS "writeOffId",
		a.writeoffcause AS "writeOffCause",
		a.status AS "status",
		a.activeflag AS "activeFlag",
		a.createdby AS "createdby.recordId",
		a.createddate AS "createdDate",
		a.lastupdby AS "lastupdby.recordId",
		a.lastupddate AS "lastUpdDate",
		a.remark AS "remark",
		mc.name as "company.name",
		a.cost AS "cost",
		a.price AS "price",
		a.giveNum AS "giveNum",
		a.purchasingNo AS "purchasingNo",
		a.remainQty AS "remainQty",
		a.stockPlaceId,
		a.stockPlaceComId,
		a.notificationId AS "notification.recordId",
		a.product
	</sql>

	<sql id="MaterialStoreJoins">
		JOIN md_company mc on mc.recordId = a.companyId
	</sql>

	<select id="get" resultMap="RawmaterialStockMap">
		SELECT
		<include refid="MaterialStoreColumns" />
		,a.contractNo
		FROM st_material_store a
		<include refid="MaterialStoreJoins" />
		WHERE a.recordId = #{recordId}
	</select>
	
	<select id="getFeeding" resultType="RawmaterialStock">
		SELECT
		<include refid="MaterialStoreColumns" />
		FROM st_material_store a
		<include refid="MaterialStoreJoins" />
		WHERE a.recordId = #{recordId}
	</select>
	
	<!-- 根据原料采购明细id查询入库记录条数 -->
	<select id="findListByPurchasingDetails" resultType="int">
		SELECT
		count(*)
		FROM st_material_store a
		WHERE a.companyId = #{companyId} and a.activeFlag = '1'
		and a.purchasingDtlId in 
		<foreach collection="purchasingDetailIds" item ="item" open="(" separator="," close=")">
			  #{item}  
		</foreach> 
	</select>
	
	<!-- ycy 2016-12-13 查询投料出库是否已添加 -->
	<select id="getFeedingRecord" resultType="RawmaterialStock">
		select * from st_material_store a
		where  a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
		and a.inOutType=#{inoutType} and a.feedingId=#{feeding.recordId}
	</select>
	
	<select id="getFeedingRecordByPro" resultType="Integer">
		SELECT 
			COUNT(a.recordId) 
		FROM st_material_store a
		WHERE  a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
		AND a.inOutType= #{inoutType} AND a.feedingId=#{feeding.recordId} AND a.status = ********
		AND (a.productBatchDeailId = #{productBatchDeailId} OR a.productBatchDeailId IS NULL)
	</select>
	
	<!-- ycy 2016-12-13 查询补料出库是否已添加 -->
	<select id="getReplenishRecord" resultType="RawmaterialStock">
		select * from st_material_store a
		where  a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
		and a.inOutType=#{inoutType} and a.fedingId=#{feding.recordId}
	</select>
	
	<!-- ycy 2016-12-13 查询原料入库是否已添加 -->
	<select id="getMaterialRecord" resultType="int">
		select count(*) from st_material_store a
		where  a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
		and a.inOutType=#{inoutType} and a.materialId=#{material.recordId}
	</select>
	
	<!--  ycy 2016-12-16 查询退货记录 -->
	<select id="getRawmaterialStock" resultType="RawmaterialStock">
		select * from st_material_store a
		where  a.companyId = #{company.recordId} and a.activeFlag = #{DEL_FLAG_NORMAL}
		and a.inOutType=#{inoutType} and a.returnsNo=#{returnsNo}
	</select>
	
	<!-- ycy 2016-12-16 查询采购入库记录 -->
	<select id="getRawmatetDate" resultType="RawmaterialStock">
		SELECT * from st_material_store a 
		WHERE a.companyId=#{company.recordId} 
		and a.activeFlag=#{DEL_FLAG_NORMAL}
		and a.purchasingDtlId=#{purchasingDtlId}
		and a.inoutType=#{inoutType}
	</select>
	
	<select id="findMaterialByPlace" resultType="Material">
		SELECT
			mat.recordId,
			mat.name,
			mat.no,
			mat.specification,
			mat.lowerLimit,
			mat.stocks,
			mat.leadTime
		FROM md_material mat
		WHERE mat.recordId = #{recordId}
	</select>
	
	<select id="findList" resultType="RawmaterialStock">
		SELECT
			a.printTimeNo,
			a.recordid AS "recordId",
			a.companyid AS "company.recordId",
			a.materialid AS "material.recordId",
			a.feedingId AS "feeding.recordId",
			a.fedingId AS "feding.recordId",
			a.materialUseId AS "materialUse.recordId",
			a.storehouseid AS "storehouse.recordId",
			a.inouttype AS "inoutType",
			a.returnsno AS "returnsNo",
			IFNULL(a.purchasingdtlid,d.purchasingDetailId) AS "purchasingDtlId",
			a.amount AS "amount",
			a.operatedate AS "operateDate",
			a.administrator AS "administrator",
			a.operator AS "operator",
			a.writeoffid AS "writeOffId",
			a.writeoffcause AS "writeOffCause",
			a.status AS "status",
			a.activeflag AS "activeFlag",
			a.createdby AS "createdby.recordId",
			a.createddate AS "createdDate",
			a.lastupdby AS "lastupdby.recordId",
			a.lastupddate AS "lastUpdDate",
			a.remark AS "remark",
			a.stockPlaceId,
			mc.name as "company.name",
			<!-- CASE a.inOutType WHEN 6 THEN IFNULL(( SELECT price FROM md_material_price_init WHERE materialId = a.materialid AND initDate  <![CDATA[<=]]>a.operateDate ORDER BY initDate DESC LIMIT 1 ) , 0) * IFNULL(a.quantity, 0)
			ELSE a.cost END AS "cost", -->
			a.price AS "price",
			a.giveNum AS "giveNum",
			IFNULL(IFNULL(a.purchasingNo,f.no),pf.no) AS "purchasingNo",
			SUM(IFNULL(a.remainQty,0)) AS "remainQty",
			a.productBatchId,
			a.productBatchDeailId,
			a.stockPlaceComId,
			IFNULL(mat.NAME, inter.name) as "material.name",
			IFNULL(mat.NO,CONCAT('YL', inter.recordId)) as "material.no",
			IFNULL(mat.specification, inter.specifications) as "material.specification",
			mat.lowerLimit as "material.lowerLimit",
			mat.stocks as "material.stocks",
			mat.leadTime AS "material.leadTime",
			IFNULL(a.supplierid,supl2.recordId)  AS "supplier.recordId",
			IFNULL(supl.name,supl2.name) as "supplier.name",
			IFNULL(supl.shortName,supl2.shortName) as "supplier.shortName",
			IFNULL(supl.no,supl2.no) as "supplier.no",
			sn.craftNo AS "craftNo",
			sn.recordId AS "notification.recordId",
			sn.mergeType AS "notification.mergeType",
			su.userName AS "createdBy.userName",
			a.contractNo AS "contractNo",
			pp.supplierContractNo as "supplierContractNo",
			msh.name AS "storehouse.name",
			md.name AS "deptName",
			REPLACE (IFNULL(taxDescriptOne.VALUE,taxDescriptTwo.VALUE),"含税","")  AS "taxDescript",
			/*CASE WHEN !ISNULL(a.remainQty) AND a.inouttype IN(3,5) THEN IFNULL(a.quantity,0)ELSE a.quantity END AS "quantity",*/
			SUM(CASE WHEN !ISNULL(a.remainQty) AND a.inouttype IN(3,5) THEN IFNULL(a.quantity,0) + IFNULL(a.remainQty,0) ELSE a.quantity END) AS "quantity",
			SUM(CASE WHEN !ISNULL(a.remainQty) AND a.inouttype IN(3,5) THEN a.quantity ELSE NULL END) AS "daBanQty",
			CASE WHEN a.inouttype IN(3,5) THEN CONCAT(IFNULL(CAST(eca.setLength AS CHAR)+0,0),'*',IFNULL(CAST(eca.setWidth AS CHAR)+0,0),'/',IFNULL(CAST(eca.pnlDivisor AS CHAR)+0,1))
			ELSE NULL
			END AS "setSize",
			mmp.name AS "placeName",
			IFNULL(mat.specification, inter.specifications) AS "specification",
			mdm.`name` AS "materialUName",
			mdm.`no` AS "materialUno",
			mdm.specification AS "materialSpecial",
			sn.occupiedStock AS "materialNum",
			mdb.`name` AS "saleName",
			mmdv.value AS "orderTypeName",
			ROUND(IFNULL(ee.value,0) * IFNULL(ff.value,0) * (IFNULL(a.quantity,0) + IFNULL(a.remainQty,0)) / 1000000,4) AS "exportArea",
			sxb.shortName AS "stockPlaceComName",
			SUM(IFNULL(a.cost, 0)) AS "cost",
			b.`value` AS "materialTypeVal",
			a.deliveryNo,
			a.deliveryDate,
			ROUND(IFNULL(a.differenceAmount,0),2) AS "deferencetotal",
			fri.`value` AS "unit",
			pp.recordId AS "purchasing.recordId",
			sn.no AS "notifiNo",
			pf.no AS "feedNo",
			ROUND(IFNULL(ee.value,0) * IFNULL(ff.value,0) * a.quantity / 1000000,4) AS "bathArea",
			IFNULL(pf.orderQuantity,scd.quantity) AS "orderQuantity",
			pf.pcsQuantity AS "pcsQuantity",
			pp.recordId AS "purchasingId",
			scd.processValueId AS "processValueId",
			sm.userName AS "acceptanceName",
			CONCAT("A",a.productBatchId,"B",a.productBatchDeailId) AS "productBatchStr",
			pp.supplyChainId,
			pp.internalSupplyChainId
		FROM st_material_store a
		LEFT JOIN md_company mc on mc.recordId = a.companyId
		LEFT JOIN md_material mat on mat.recordId = a.materialId AND (a.product = 1 OR a.product IS NULL)
		LEFT JOIN md_supplier supl on supl.recordId = a.supplierid
		LEFT JOIN pd_feeding pf on pf.recordId = a.feedingId
		LEFT JOIN pd_replenish pr on pr.recordId = a.fedingId
		LEFT JOIN sl_notification sn ON IFNULL(pf.notificationId,pr.notificationId) = sn.recordId
		LEFT JOIN eg_carda eca ON eca.recordId = sn.processCardAId AND eca.companyId = a.companyId AND eca.activeFlag = 1
		LEFT JOIN sm_user su ON a.createdBy = su.recordId
		left join pu_purchasing_detail ppd on ppd.recordId = a.purchasingDtlId
		left join pu_purchasing pp on pp.recordId=ppd.purchasingId
		LEFT JOIN sl_contract_detail scd ON scd.recordId = sn.contractDetailId
        LEFT JOIN sl_contract sc ON sc.recordId = scd.contractId
        LEFT JOIN md_customer cus ON cus.recordId = sc.customerId
        LEFT JOIN md_store_house msh ON msh.recordId = a.storeHouseId AND msh.companyId = a.companyId AND msh.activeFlag = 1
        LEFT JOIN re_material_use rmu ON rmu.recordId = a.materialUseId AND rmu.companyId = a.companyId AND rmu.activeFlag = 1
        LEFT JOIN md_department md ON md.recordId = rmu.departId AND md.companyId = a.companyId AND md.activeFlag = 1
        LEFT JOIN md_dict_value taxDescriptOne ON taxDescriptOne.recordId = pp.taxDescript AND taxDescriptOne.companyId = a.companyId AND taxDescriptOne.activeFlag = 1
        LEFT JOIN md_dict_value taxDescriptTwo ON taxDescriptTwo.recordId = sc.taxDescript AND taxDescriptTwo.companyId = a.companyId AND taxDescriptTwo.activeFlag = 1
        LEFT JOIN md_dict_value mmdv ON mmdv.recordId = sn.orderType
        LEFT JOIN md_material_childcompany mmc ON mmc.recordId = a.stockPlaceComId
        LEFT JOIN md_material_place mmp ON mmp.recordId = mmc.materPlaceId
        LEFT JOIN md_branch sxb ON sxb.recordId = mmc.saleCompanyId
        LEFT JOIN md_material mdm ON mdm.recordId=sn.materialId
		LEFT JOIN md_branch mdb ON mdb.recordId=sn.saleId
		LEFT JOIN md_supplier supl2 ON supl2.recordId = mat.supplierId

		LEFT JOIN md_material_specification_relation ee ON ee.materialId = a.materialId
		AND ee.companyId = a.companyId AND ee.activeFlag = 1 AND ee.specificationId = #{specificationId8}

		LEFT JOIN md_material_specification_relation ff ON ff.materialId = a.materialId
		AND ff.companyId = a.companyId AND ff.activeFlag = 1 AND ff.specificationId = #{specificationId9}
		
		LEFT JOIN pu_returns_detail d ON d.recordId = a.returnsNo AND a.inOutType IN (4,7)
		LEFT JOIN pu_purchasing_detail e ON e.recordId = d.purchasingDetailId
		LEFT JOIN pu_purchasing f ON f.recordId = e.purchasingId
		LEFT JOIN md_dict_value b ON b.recordId = mat.materialType
		LEFT JOIN md_dict_value fri ON fri.recordId = mat.unit
		LEFT JOIN sm_user sm ON sm.recordId = rmu.createdBy
		LEFT JOIN inter_config_supplier inter ON inter.recordId = a.materialId AND a.product = 2
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = 1
			AND a.inOutType NOT IN (11,12,13)
			<if test="reportSumFlag != null and reportSumFlag != ''">
				AND a.inOutType IN (3,5,6)
			</if>
			<if test="material!=null and material.recordId!=null and material.recordId!=''">
				AND a.materialId=#{material.recordId}
			</if>
			<if test="material!=null and material.no!=null and material.no!=''">
				AND (REPLACE(mat.no," ","") like CONCAT('%', REPLACE(#{material.no}," ",""), '%') OR REPLACE(inter.recordId," ","") like CONCAT('%', REPLACE(#{material.no}," ",""), '%'))
			</if>
			<if test="material!=null and material.name!=null and material.name!=''">
				AND (REPLACE(mat.name," ","") like CONCAT('%', REPLACE(#{material.name}," ",""), '%') OR REPLACE(inter.name," ","") like CONCAT('%', REPLACE(#{material.name}," ",""), '%'))
			</if>
			<if test="supplier!=null and supplier.no!=null and supplier.no!=''">
				AND REPLACE(supl.no," ","") like CONCAT('%', REPLACE(#{supplier.no}," ",""), '%')
			</if>
			<if test="supplier!=null and supplier.shortName!=null and supplier.shortName!=''">
				AND REPLACE(supl.shortName," ","") like CONCAT('%', REPLACE(#{supplier.shortName}," ",""), '%')
			</if>
			<if test="inoutType!=null and inoutType!=''">
				<choose>
					<when test="inoutType == '29'">
						AND a.inoutType=2 AND a.remark LIKE '%盘盈%' 
					</when>
					<when test="inoutType == '30'">
						AND a.inoutType=6 AND a.remark LIKE '%盘亏%'  
					</when>
					<when test="inoutType == '2'.toString()">
						AND a.inoutType=2 AND INSTR(IFNULL(a.remark,''),'盘盈') = 0
					</when>
					<when test="inoutType == '6'.toString()">
						AND a.inoutType=6 AND INSTR(IFNULL(a.remark,''),'盘亏') = 0
					</when>
					<otherwise>
						AND a.inoutType=#{inoutType}
					</otherwise>
				</choose>
			</if>
			<if test="status!=null and status!=''">
				AND a.status=#{status}
			</if>
			<if test="inoutTimeStartQr != null and inoutTimeStartQr != ''">
				AND a.operateDate >= #{inoutTimeStartQr}
			</if>
			<if test="inoutTimeEndQr != null and inoutTimeEndQr != ''">
				AND a.operateDate <![CDATA[<=]]> #{inoutTimeEndQr}
			</if>
			<if test="storehouse!=null and storehouse.recordId!=null and storehouse.recordId!=''">
				AND FIND_IN_SET(a.storehouseId,#{storehouse.recordId})
			</if>
			<if test="craftNo!=null and craftNo!=''">
				AND REPLACE(CONCAT(IFNULL(sn.craftNo,""), IFNULL(a.returnsno,""))," ","") LIKE CONCAT('%', REPLACE(#{craftNo}," ",""), '%') 
			    <if test="inoutType!=3 and inoutType!=5">
			    	AND (a.inoutType = 3 OR a.inoutType = 5)
			    </if>
			</if>
			<if test="contractNo !=null and contractNo != ''">
				AND REPLACE(a.contractNo," ","") like CONCAT('%', REPLACE(#{contractNo}," ",""), '%')
			</if>
			<if test="supplierContractNo !=null and supplierContractNo != ''">
				AND REPLACE(pp.supplierContractNo," ","") like CONCAT('%', REPLACE(#{supplierContractNo}," ",""), '%')
			</if>
			<if test="queryNo !=null and queryNo != ''">
				AND REPLACE(IFNULL(a.purchasingNo,pf.no)," ","") like CONCAT('%', REPLACE(#{queryNo}," ",""), '%')
			</if>
			<if test="queryPrintNo !=null and queryPrintNo != ''">
				AND REPLACE(a.printTimeNo," ","") like CONCAT('%', REPLACE(#{queryPrintNo}," ",""), '%')
			</if>
			<if test="deliveryNo !=null and deliveryNo != ''">
				AND REPLACE(a.deliveryNo," ","") like CONCAT('%', REPLACE(#{deliveryNo}," ",""), '%')
			</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND (
					a.createdBy = #{createdBy.recordId}
					OR a.administrator = #{createdBy.recordId}
					OR a.operator = #{createdBy.recordId}
					OR pf.createdBy = #{createdBy.recordId}
					OR pf.distributer = #{createdBy.recordId}
					OR pr.createdBy = #{createdBy.recordId}
					OR pr.applicant = #{createdBy.recordId}
					OR pr.doApplicant = #{createdBy.recordId}
					OR supl.createdBy = #{createdBy.recordId}
					OR supl.recordId IN (SELECT supplierId FROM md_supplier_user WHERE userId = #{createdBy.recordId} AND activeFlag = 1)
					OR cus.createdBy = #{createdBy.recordId}
					OR (!ISNULL(cus.salesman) AND cus.salesman = #{createdBy.recordId})
					OR cus.recordId IN (SELECT customerId FROM sm_customer_salesAssistant WHERE userId = #{createdBy.recordId} AND activeFlag = 1)
				)
			</if>
			<if test="stockPlaceId != null and stockPlaceId != ''">
				AND mmp.recordId = #{stockPlaceId}
			</if>
			<if test="statusTwo != null and statusTwo != '' and statusTwo == 1">
				AND a.printTime IS NULL
			</if>
			<if test="remark !=null and remark != ''">
				AND REPLACE(IFNULL(a.remark,"")," ","") LIKE CONCAT('%', REPLACE(#{remark}," ",""), '%') 
			</if>
			<if test="product != null and product != ''">
				<if test="product == 1">
					AND (a.product IS NULL OR  a.product = #{product})
				</if>
				<if test="product == 2">
					AND a.product = #{product}
				</if>
			</if>
			<if test="productionStatus != null and productionStatus != ''">
				<if test="productionStatus == 1">
					<if test="productBatchDeailId != null and productBatchDeailId != ''">
						AND FIND_IN_SET(a.productBatchDeailId,#{productBatchDeailId})
					</if>
					<if test="productBatchDeailId == null or productBatchDeailId == ''">
						AND 1=2
					</if>
				</if>
				<if test="productionStatus == 2">
					<if test="productBatchDeailId != null and productBatchDeailId != ''">
						AND !FIND_IN_SET(a.productBatchDeailId,#{productBatchDeailId})
					</if>
					<if test="productBatchDeailId == null or productBatchDeailId == ''">
						AND 1=1
					</if>
				</if>
			</if>
		</where>
		GROUP BY CASE WHEN a.inOutType = 3 THEN a.productBatchDeailId ELSE a.recordId  END,a.materialId
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findStockList" resultType="RawmaterialStock">
		SELECT
			a.printTimeNo,
			a.recordid AS "recordId",
			a.companyid AS "company.recordId",
			a.materialid AS "material.recordId",
			a.feedingId AS "feeding.recordId",
			a.fedingId AS "feding.recordId",
			a.materialUseId AS "materialUse.recordId",
			a.storehouseid AS "storehouse.recordId",
			a.inouttype AS "inoutType",
			a.returnsno AS "returnsNo",
			a.purchasingdtlid AS "purchasingDtlId",
			a.amount AS "amount",
			a.operatedate AS "operateDate",
			a.administrator AS "administrator",
			a.operator AS "operator",
			a.writeoffid AS "writeOffId",
			a.writeoffcause AS "writeOffCause",
			a.status AS "status",
			a.activeflag AS "activeFlag",
			a.createdby AS "createdby.recordId",
			a.createddate AS "createdDate",
			a.lastupdby AS "lastupdby.recordId",
			a.lastupddate AS "lastUpdDate",
			a.remark AS "remark",
			a.stockPlaceId,
			mc.name as "company.name",
			a.cost AS "cost",
			a.price AS "price",
			a.giveNum AS "giveNum",
			IFNULL(a.purchasingNo,pf.no) AS "purchasingNo",
			a.remainQty AS "remainQty",
			a.productBatchId,
			a.productBatchDeailId,
			a.stockPlaceComId,
			mat.name as "material.name",
			mat.no as "material.no",
			mat.specification as "material.specification",
			mat.lowerLimit as "material.lowerLimit",
			mat.stocks as "material.stocks",
			mat.leadTime AS "material.leadTime",
			IFNULL(a.supplierid,supl2.recordId)  AS "supplier.recordId",
			IFNULL(supl.name,supl2.name) as "supplier.name",
			IFNULL(supl.shortName,supl2.shortName) as "supplier.shortName",
			IFNULL(supl.no,supl2.no) as "supplier.no",
			sn.craftNo AS "craftNo",
			sn.recordId AS "notification.recordId",
			sn.mergeType AS "notification.mergeType",
			su.userName AS "createdBy.userName",
			a.contractNo AS "contractNo",
			pp.supplierContractNo as "supplierContractNo",
			msh.name AS "storehouse.name",
			md.name AS "deptName",
			CASE WHEN !ISNULL(a.remainQty) AND a.inouttype IN(3,5) THEN IFNULL(a.quantity,0) + IFNULL(a.remainQty,0) ELSE a.quantity END AS "quantity",
			CASE WHEN !ISNULL(a.remainQty) AND a.inouttype IN(3,5) THEN a.quantity ELSE NULL END AS "daBanQty",
			mmp.name AS "placeName",
			mat.specification AS "specification",
			mdm.`name` AS "materialUName",
			mdm.`no` AS "materialUno",
			mdm.specification AS "materialSpecial",
			sn.occupiedStock AS "materialNum",
			mdb.`name` AS "saleName",
			mmdv.value AS "orderTypeName",
			mdb2.shortName AS "stockPlaceComName",
            b.`value` AS "materialTypeVal",
			a.deliveryNo,
			a.deliveryDate,
			CAST(mat.actualThickness AS char)+0 AS "actualThickness"
		FROM st_material_store a
		LEFT JOIN md_company mc on mc.recordId = a.companyId
		LEFT JOIN md_material mat on mat.recordId = a.materialId
		LEFT JOIN md_supplier supl on supl.recordId = a.supplierid
		LEFT JOIN pd_feeding pf on pf.recordId = a.feedingId
		LEFT JOIN pd_replenish pr on pr.recordId = a.fedingId
		LEFT JOIN sl_notification sn ON IFNULL(pf.notificationId,pr.notificationId) = sn.recordId
		LEFT JOIN eg_carda eca ON eca.recordId = sn.processCardAId AND eca.companyId = a.companyId AND eca.activeFlag = 1
		LEFT JOIN sm_user su ON a.createdBy = su.recordId
		left join pu_purchasing_detail ppd on ppd.recordId = a.purchasingDtlId
		left join pu_purchasing pp on pp.recordId=ppd.purchasingId
		LEFT JOIN sl_contract_detail scd ON scd.recordId = sn.contractDetailId
        LEFT JOIN sl_contract sc ON sc.recordId = scd.contractId
        LEFT JOIN md_customer cus ON cus.recordId = sc.customerId
        LEFT JOIN md_store_house msh ON msh.recordId = a.storeHouseId AND msh.companyId = a.companyId AND msh.activeFlag = 1
        LEFT JOIN re_material_use rmu ON rmu.recordId = a.materialUseId AND rmu.companyId = a.companyId AND rmu.activeFlag = 1
        LEFT JOIN md_department md ON md.recordId = rmu.departId AND md.companyId = a.companyId AND md.activeFlag = 1
        LEFT JOIN md_dict_value mmdv ON mmdv.recordId = sn.orderType
        LEFT JOIN md_material_childcompany mmc ON mmc.recordId = a.stockPlaceComId
        LEFT JOIN md_material_place mmp ON mmp.recordId = mmc.materPlaceId
        LEFT JOIN md_material mdm ON mdm.recordId=sn.materialId
		LEFT JOIN md_branch mdb ON mdb.recordId=sn.saleId
		LEFT JOIN md_supplier supl2 ON supl2.recordId = mat.supplierId
		LEFT JOIN md_branch mdb2 ON mdb2.recordId = mmc.saleCompanyId
        LEFT JOIN md_dict_value b ON b.recordId = mat.materialType
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1
		<if test="material!=null and material.recordId!=null and material.recordId!=''">
			AND a.materialId=#{material.recordId}
		</if>
		<if test="status!=null and status!=''">
			AND a.status=#{status}
		</if>
		<if test="inoutTimeStartQr != null and inoutTimeStartQr != ''">
			AND a.operateDate >= #{inoutTimeStartQr}
		</if>
		<if test="inoutTimeEndQr != null and inoutTimeEndQr != ''">
			AND a.operateDate <![CDATA[<=]]> #{inoutTimeEndQr}
		</if>
		<if test="inoutType!=null and inoutType!=''">
				<choose>
					<when test="inoutType == '29'">
						AND a.inoutType=2 AND a.remark LIKE '%盘盈%' 
					</when>
					<when test="inoutType == '30'">
						AND a.inoutType=6 AND a.remark LIKE '%盘亏%'  
					</when>
					<when test="inoutType == '2'.toString()">
						AND a.inoutType=2 AND INSTR(IFNULL(a.remark,''),'盘盈') = 0
					</when>
					<when test="inoutType == '6'.toString()">
						AND a.inoutType=6 AND INSTR(IFNULL(a.remark,''),'盘亏') = 0
					</when>
					<otherwise>
						AND a.inoutType=#{inoutType}
					</otherwise>
				</choose>
		</if>
		ORDER BY a.operateDate ASC
	</select>
	
	<select id="findFeedPrintList" resultType="RawmaterialStock">
		SELECT
			a.recordId,
			SUM(a.remainQty) AS "remainQty",
			SUM(a.quantity) AS "quantity",
			sc.`no` AS "contractNo",
			sn.craftNo AS "craftNo",
			sn.`no` AS "notifiNo",
			mdm.`name` AS "materialUName",
			mdm.`no` AS "materialUno",
			mdm.specification AS "materialSpecial",
			sn.occupiedStock AS "materialNum",
			mdb.`name` AS "saleName",
			IFNULL(pf.`no`,pr.`no`) AS "feedNo",
			CONCAT(IFNULL(CAST(eca.setLength AS CHAR)+0,0),'*',
			IFNULL(CAST(eca.setWidth AS CHAR)+0,0),'/',
			IFNULL(CAST(eca.pnlDivisor AS CHAR)+0,1)) AS "setSize",
			mat.name as "material.name",
			mat.`no` as "material.no",
			mat.specification as "material.specification",
			ROUND(SUM(IFNULL((IFNULL(a.quantity,0) + IFNULL(a.remainQty,0)) * ee.value * ff.value / 1000000,0)),4) AS bathArea,
			IFNULL(pf.orderQuantity,scd.quantity) AS "orderQuantity",
			IFNULL(pf.pcsQuantity,pr.discardQty) AS "pcsQuantity",
			(SELECT
				MAX(printNo)
			FROM
				st_material_store
			WHERE
				inOutType = a.inOutType
			AND companyId = #{company.recordId}
			AND activeFlag = 1
			AND `status` = ********
			AND DATE_FORMAT(printTime, "%y%m%d") = DATE_FORMAT(NOW(), "%y%m%d")) AS "printNo",
			GROUP_CONCAT(a.recordId) AS "listId",
		    a.purchasingDtlId
		FROM st_material_store a
		LEFT JOIN md_company mc on mc.recordId = a.companyId
		LEFT JOIN md_material mat on mat.recordId = a.materialId
		LEFT JOIN pd_feeding pf on pf.recordId = a.feedingId
		LEFT JOIN pd_replenish pr ON pr.recordId = a.fedingId AND pr.companyId = a.companyId
		LEFT JOIN sl_notification sn ON sn.recordId = IFNULL(pf.notificationId,pr.notificationId)
		LEFT JOIN eg_carda eca ON eca.recordId = sn.processCardAId AND eca.companyId = a.companyId AND eca.activeFlag = 1
		LEFT JOIN sm_user su ON a.createdBy = su.recordId
		LEFT JOIN sl_contract_detail scd ON scd.recordId = sn.contractDetailId
        LEFT JOIN sl_contract sc ON sc.recordId = scd.contractId
        LEFT JOIN md_customer cus ON cus.recordId = sc.customerId
        LEFT JOIN md_store_house msh ON msh.recordId = a.storeHouseId AND msh.companyId = a.companyId AND msh.activeFlag = 1
        LEFT JOIN md_dict_value taxDescriptTwo ON taxDescriptTwo.recordId = sc.taxDescript AND taxDescriptTwo.companyId = a.companyId AND taxDescriptTwo.activeFlag = 1
        LEFT JOIN md_dict_value mmdv ON mmdv.recordId = sn.orderType
        LEFT JOIN md_material_childcompany mmc ON mmc.recordId = a.stockPlaceComId
        LEFT JOIN md_material_place mmp ON mmp.recordId = mmc.materPlaceId
        LEFT JOIN md_material mdm ON mdm.recordId=sn.materialId
		LEFT JOIN md_branch mdb ON mdb.recordId=sn.saleId
		LEFT JOIN md_material_specification_relation ee ON ee.materialId = mat.recordId
		AND ee.companyId = a.companyId
		AND ee.specificationId = (
			SELECT
				recordId
			FROM
				md_material_specification
			WHERE
				`name` = "长"
			AND companyId = a.companyId AND activeFlag = 1
		)
		LEFT JOIN md_material_specification_relation ff ON ff.materialId = mat.recordId
		AND ff.companyId = a.companyId
		AND ff.specificationId = (
			SELECT
				recordId
			FROM
				md_material_specification
			WHERE
				`name` = "宽"
			AND companyId = a.companyId AND activeFlag = 1
		)
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = 1 
		AND a.recordId IN(${recordId})
		</where>
		GROUP BY
			a.feedingId,
			a.operateDate,
			a.materialId
		ORDER BY a.operateDate ASC
	</select>
	
	<select id="findFeedMainList" resultType="RawmaterialStock">
		SELECT
			IFNULL(a.feedingId,a.fedingId) AS "recordId",
			SUM(a.remainQty) AS "remainQty",
			SUM(a.quantity) AS "quantity",
			sc.`no` AS "contractNo",
			sn.craftNo AS "craftNo",
			sn.`no` AS "notifiNo",
			mdm.`name` AS "materialUName",
			mdm.`no` AS "materialUno",
			mdm.specification AS "materialSpecial",
			sn.occupiedStock AS "materialNum",
			mdb.`name` AS "saleName",
			IFNULL(pf.`no`,pr.`no`) AS "feedNo",
			CONCAT(IFNULL(CAST(eca.setLength AS CHAR)+0,0),'*',
			IFNULL(CAST(eca.setWidth AS CHAR)+0,0),'/',
			IFNULL(CAST(eca.pnlDivisor AS CHAR)+0,1)) AS "setSize",
			mat.name as "material.name",
			mat.`no` as "material.no",
			mat.specification as "material.specification",
			a.operateDate,
			ROUND(SUM(IFNULL((IFNULL(a.quantity,0) + IFNULL(a.remainQty,0)) * ee.value * ff.value / 1000000,0)),4) AS bathArea,
			IFNULL(pf.orderQuantity,scd.quantity) AS "orderQuantity",
			IFNULL(pf.pcsQuantity,pr.discardQty) AS "pcsQuantity",
			GROUP_CONCAT(a.recordId) AS "listId",
			a.printTimeNo,
			a.deliveryNo,
			a.deliveryDate,
			a.status,
			a.inoutType,
			a.price,
			a.cost,
			REPLACE (taxDescriptTwo.value,"含税","") AS 'taxDescript',
			ROUND(IFNULL(a.differenceAmount,0),2) AS "deferencetotal"
			<!-- (
				SELECT
					price
				FROM
					md_material_price_init
				WHERE
					materialId = a.materialId
				AND DATE_FORMAT(initDate,"%y%m%d") <![CDATA[<=]]> DATE_FORMAT(a.operateDate,"%y%m%d")
				ORDER BY
					initDate DESC
				LIMIT 1
			) AS "price" -->
		FROM st_material_store a
		LEFT JOIN md_company mc on mc.recordId = a.companyId
		LEFT JOIN md_material mat on mat.recordId = a.materialId
		LEFT JOIN pd_feeding pf on pf.recordId = a.feedingId
		LEFT JOIN pd_replenish pr ON pr.recordId = a.fedingId AND pr.companyId = a.companyId
		LEFT JOIN sl_notification sn ON sn.recordId = IFNULL(pf.notificationId,pr.notificationId)
		LEFT JOIN eg_carda eca ON eca.recordId = sn.processCardAId AND eca.companyId = a.companyId AND eca.activeFlag = 1
		LEFT JOIN sm_user su ON a.createdBy = su.recordId
		LEFT JOIN sl_contract_detail scd ON scd.recordId = sn.contractDetailId
        LEFT JOIN sl_contract sc ON sc.recordId = scd.contractId
        LEFT JOIN md_customer cus ON cus.recordId = sc.customerId
        LEFT JOIN md_store_house msh ON msh.recordId = a.storeHouseId AND msh.companyId = a.companyId AND msh.activeFlag = 1
        LEFT JOIN md_dict_value taxDescriptTwo ON taxDescriptTwo.recordId = sc.taxDescript AND taxDescriptTwo.companyId = a.companyId AND taxDescriptTwo.activeFlag = 1
        LEFT JOIN md_dict_value mmdv ON mmdv.recordId = sn.orderType
        LEFT JOIN md_material_childcompany mmc ON mmc.recordId = a.stockPlaceComId
        LEFT JOIN md_material_place mmp ON mmp.recordId = mmc.materPlaceId
        LEFT JOIN md_material mdm ON mdm.recordId=sn.materialId
		LEFT JOIN md_branch mdb ON mdb.recordId=sn.saleId
		LEFT JOIN md_material_specification_relation ee ON ee.materialId = mat.recordId
		AND ee.companyId = a.companyId
		AND ee.specificationId = (
			SELECT
				recordId
			FROM
				md_material_specification
			WHERE
				`name` = "长"
			AND companyId = a.companyId AND activeFlag = 1
		)
		LEFT JOIN md_material_specification_relation ff ON ff.materialId = mat.recordId
		AND ff.companyId = a.companyId
		AND ff.specificationId = (
			SELECT
				recordId
			FROM
				md_material_specification
			WHERE
				`name` = "宽"
			AND companyId = a.companyId AND activeFlag = 1
		)
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = 1
			AND a.inoutType IN (3,5)
			AND a.status = ********
			<if test="material!=null and material.recordId!=null and material.recordId!=''">
				AND a.materialId=#{material.recordId}
			</if>
			<if test="material!=null and material.no!=null and material.no!=''">
				AND REPLACE(mat.no," ","") like CONCAT('%', REPLACE(#{material.no}," ",""), '%')
			</if>
			<if test="material!=null and material.name!=null and material.name!=''">
				AND REPLACE(mat.name," ","") like CONCAT('%', REPLACE(#{material.name}," ",""), '%')
			</if>
			<if test="status!=null and status!=''">
				AND a.status=#{status}
			</if>
			<if test="inoutTimeStartQr != null and inoutTimeStartQr != ''">
				AND a.operateDate >= #{inoutTimeStartQr}
			</if>
			<if test="inoutTimeEndQr != null and inoutTimeEndQr != ''">
				AND a.operateDate <![CDATA[<=]]> #{inoutTimeEndQr}
			</if>
			<if test="storehouse!=null and storehouse.recordId!=null and storehouse.recordId!=''">
				AND a.storehouseId=#{storehouse.recordId}
			</if>
			<if test="craftNo!=null and craftNo!=''">
				AND REPLACE(IFNULL(sn.craftNo,"")," ","") LIKE CONCAT('%', REPLACE(#{craftNo}," ",""), '%') 
			</if>
			<if test="contractNo !=null and contractNo != ''">
				AND REPLACE(sc.`no`," ","") like CONCAT('%', REPLACE(#{contractNo}," ",""), '%')
			</if>
			<if test="statusTwo != null and statusTwo != '' and statusTwo == 1">
				AND a.printTime IS NULL
			</if>
			<if test="queryPrintNo !=null and queryPrintNo != ''">
				AND REPLACE(a.printTimeNo," ","") like CONCAT('%', REPLACE(#{queryPrintNo}," ",""), '%')
			</if>
			<if test="remark !=null and remark != ''">
				AND REPLACE(IFNULL(a.remark,"")," ","") LIKE CONCAT('%', REPLACE(#{remark}," ",""), '%') 
			</if>
		</where>
		GROUP BY
			a.feedingId,
			a.operateDate,
			a.materialId
		ORDER BY a.operateDate DESC
	</select>
	
	<select id="findFeedList" resultType="RawmaterialStock">
		SELECT
			a.recordid AS "recordId",
			a.companyid AS "company.recordId",
			a.materialid AS "material.recordId",
			a.feedingId AS "feeding.recordId",
			a.fedingId AS "feding.recordId",
			a.materialUseId AS "materialUse.recordId",
			a.storehouseid AS "storehouse.recordId",
			a.inouttype AS "inoutType",
			a.returnsno AS "returnsNo",
			a.purchasingdtlid AS "purchasingDtlId",
			a.amount AS "amount",
			a.operatedate AS "operateDate",
			a.administrator AS "administrator",
			a.operator AS "operator",
			a.writeoffid AS "writeOffId",
			a.writeoffcause AS "writeOffCause",
			a.status AS "status",
			a.activeflag AS "activeFlag",
			a.createdby AS "createdby.recordId",
			a.createddate AS "createdDate",
			a.lastupdby AS "lastupdby.recordId",
			a.lastupddate AS "lastUpdDate",
			a.remark AS "remark",
			a.stockPlaceId,
			a.cost AS "cost",
			a.price AS "price",
			a.giveNum AS "giveNum",
			a.remainQty AS "remainQty",
			a.productBatchId,
			a.productBatchDeailId,
			a.stockPlaceComId,
			a.contractNo AS "contractNo",
			mc.name as "company.name",
			mat.name as "material.name",
			mat.no as "material.no",
			mat.specification as "material.specification",
			mat.lowerLimit as "material.lowerLimit",
			mat.stocks as "material.stocks",
			mat.leadTime AS "material.leadTime",
			sn.craftNo AS "craftNo",
			sn.recordId AS "notification.recordId",
			sn.mergeType AS "notification.mergeType",
			su.userName AS "createdBy.userName",
			msh.name AS "storehouse.name",
			a.quantity AS "quantity",
			CONCAT(IFNULL(CAST(eca.setLength AS CHAR)+0,0),'*',
			IFNULL(CAST(eca.setWidth AS CHAR)+0,0),'/',
			IFNULL(CAST(eca.pnlDivisor AS CHAR)+0,1)) AS "setSize",
			mmp.name AS "placeName",
			mdm.`name` AS "materialUName",
			mdm.`no` AS "materialUno",
			mdm.specification AS "materialSpecial",
			sn.occupiedStock AS "materialNum",
			mdb.`name` AS "saleName"
		FROM st_material_store a
		LEFT JOIN md_company mc on mc.recordId = a.companyId
		LEFT JOIN md_material mat on mat.recordId = a.materialId
		LEFT JOIN pd_feeding pf on pf.recordId = a.feedingId
		LEFT JOIN sl_notification sn ON pf.notificationId = sn.recordId
		LEFT JOIN eg_carda eca ON eca.recordId = sn.processCardAId AND eca.companyId = a.companyId AND eca.activeFlag = 1
		LEFT JOIN sm_user su ON a.createdBy = su.recordId
		LEFT JOIN sl_contract_detail scd ON scd.recordId = sn.contractDetailId
        LEFT JOIN sl_contract sc ON sc.recordId = scd.contractId
        LEFT JOIN md_customer cus ON cus.recordId = sc.customerId
        LEFT JOIN md_store_house msh ON msh.recordId = a.storeHouseId AND msh.companyId = a.companyId AND msh.activeFlag = 1
        LEFT JOIN md_dict_value taxDescriptTwo ON taxDescriptTwo.recordId = sc.taxDescript AND taxDescriptTwo.companyId = a.companyId AND taxDescriptTwo.activeFlag = 1
        LEFT JOIN md_dict_value mmdv ON mmdv.recordId = sn.orderType
        LEFT JOIN md_material_childcompany mmc ON mmc.recordId = a.stockPlaceComId
        LEFT JOIN md_material_place mmp ON mmp.recordId = mmc.materPlaceId
        LEFT JOIN md_material mdm ON mdm.recordId=sn.materialId
		LEFT JOIN md_branch mdb ON mdb.recordId=sn.saleId
		WHERE a.activeFlag = 1
		AND a.inoutType=3 AND a.feedingId = #{recordId} AND a.status = ********
	</select>
	
	<select id="findMaterialList" resultType="RawmaterialStock">
		SELECT
			a.recordId,
			a.`no` AS "materialNo",
			a.`name` AS "materialName",
			a.specification AS "specification",
			mdd.`value` AS "materialTypeVal",
			mdv.`value` AS "unitValue",
			msh.`name` AS "storeName",
			(
			SELECT
			price
			FROM
			md_material_price_init
			WHERE
			materialId = a.recordId
			AND activeFlag = 1
			AND initDate <![CDATA[ <= ]]> #{inoutTimeStartQr}
			ORDER BY
			initDate DESC
			LIMIT 1
			) AS initPrice,
			(
			SELECT
			moneyRadio
			FROM
			md_material_price_init
			WHERE
			materialId = a.recordId
			AND activeFlag = 1
			AND initDate <![CDATA[ <= ]]> #{inoutTimeStartQr}
			ORDER BY
			initDate DESC
			LIMIT 1
			) AS moneyRadio,
			(
			SELECT
			initDate
			FROM
			md_material_price_init
			WHERE
			materialId = a.recordId
			AND activeFlag = 1
			AND initDate <![CDATA[ <= ]]> #{inoutTimeStartQr}
			ORDER BY
			initDate DESC
			LIMIT 1
			) AS initDate,
			(
			SELECT
			price
			FROM
			md_material_price_init
			WHERE
			materialId = a.recordId
			AND activeFlag = 1
			AND initDate = '2021-01-01 00:00:00'
			ORDER BY
			initDate DESC
			LIMIT 1
			) AS startPrice,
			CAST(a.actualThickness AS char)+0 AS "actualThickness",
			a.initStocks,
			a.companyId,
			a.materialType AS "materialTypeId"
		FROM
			md_material a
		LEFT JOIN md_dict_value mdd ON mdd.recordId = a.materialType
		LEFT JOIN md_dict_value mdv ON mdv.recordId = a.unit
		LEFT JOIN md_store_house msh ON msh.recordId = a.storehouseid
		WHERE
			a.companyid = #{company.recordId}
		AND a.activeFlag = 1
		AND a.materialKind = 100701
		AND a.`status` = 1
		<if test="materialNo != null and materialNo != ''">
			AND a.no like CONCAT('%', #{materialNo}, '%')
		</if>
		<if test="materialName != null and materialName != ''">
			AND a.name like CONCAT('%', #{materialName}, '%')
		</if>
		<if test="condition1 != null and condition1 != ''">
			AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition1}," ",""), '%')
		</if>
		<if test="condition2 != null and condition2 != ''">
			AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition2}," ",""), '%')
		</if>
		<if test="condition3 != null and condition3 != ''">
			AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition3}," ",""), '%')
		</if>
		<if test="condition4 != null and condition4 != ''">
			AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition4}," ",""), '%')
		</if>
		<if test="condition5 != null and condition5 != ''">
			AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition5}," ",""), '%')
		</if>
		<if test="condition6 != null and condition6 != ''">
			AND REPLACE(a.specification," ","") LIKE CONCAT('%', REPLACE(#{condition6}," ",""), '%')
		</if>
		<if test="materialTypeId != null and materialTypeId !=''">
			AND a.materialType =#{materialTypeId}
		</if>
		<if test="storeHouseId != null and storeHouseId !=''">
		    AND FIND_IN_SET(a.storeHouseId,#{storeHouseId})
		</if>
		<if test="stockPlaceId != null and stockPlaceId !=''">
			AND a.recordId in (select DISTINCT materialId from md_material_childcompany WHERE  materPlaceId =#{stockPlaceId})
		</if>
		<if test="haveStockFlag != null and haveStockFlag !='' and haveStockFlag == 1">
			AND (
			IFNULL((
			SELECT
			SUM(CASE inOutType WHEN 1 THEN IFNULL(quantity,0)
			WHEN 2 THEN IFNULL(quantity,0)
			WHEN 3 THEN IFNULL(-quantity,0)
			WHEN 4 THEN IFNULL(-quantity,0)
			WHEN 5 THEN IFNULL(-quantity,0)
			WHEN 6 THEN IFNULL(-quantity,0)
			WHEN 7 THEN IFNULL(quantity,0)
			ELSE 0 END)
			FROM st_material_store
			WHERE companyId = #{company.recordId}
			AND activeFlag = 1
			AND materialId = a.recordId
			AND status = ********
			AND DATE_FORMAT(operateDate,"%y%m%d") >= DATE_FORMAT(IFNULL(a.initDate,'2021-01-01'),"%y%m%d")
			),0) +
			IFNULL(a.initStocks,0)
			) > 0
		</if>
		ORDER BY
			a.createdDate DESC
	</select>
	
	<select id="findMaterialExcelList" resultType="RawmaterialExcel">
		SELECT
			a.recordId,
			a.`no` AS "materialNo",
			a.`name` AS "materialName",
			a.specification AS "specification",
			mdd.`value` AS "materialTypeVal",
			mdv.`value` AS "unitValue",
			msh.`name` AS "storeName"
		FROM
			md_material a
		LEFT JOIN md_dict_value mdd ON mdd.recordId = a.materialType
		LEFT JOIN md_dict_value mdv ON mdv.recordId = a.unit
		LEFT JOIN md_store_house msh ON msh.recordId = a.storehouseid
		WHERE
			a.companyid = #{company.recordId}
		AND a.activeFlag = 1
		AND a.materialKind = 100701
		AND a.`status` = 1
		<if test="materialNo != null and materialNo != ''">
			AND a.no like CONCAT('%', #{materialNo}, '%') 
		</if>
		<if test="materialName != null and materialName != ''">
			AND a.name like CONCAT('%', #{materialName}, '%')
		</if>
		<if test="specification != null and specification != ''">
			AND a.specification like CONCAT('%', #{specification}, '%')
		</if>
		<if test="materialTypeId != null and materialTypeId !=''">
			AND a.materialType =#{materialTypeId}
		</if>
		<if test="storeHouseId != null and storeHouseId !=''">
			AND a.storeHouseId =#{storeHouseId}
		</if>
		ORDER BY
			a.createdDate DESC
	</select>

	<select id="findAllList" resultType="RawmaterialStock">
		SELECT
		<include refid="MaterialStoreColumns" />
		FROM st_material_store a
		<include refid="MaterialStoreJoins" />
		<where>

		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>

    <insert id="bathInsert" parameterType="java.util.List">
		INSERT INTO st_material_store(
			companyid,
			materialid,
			supplierid,
			storehouseid,
			materialUseId,
			inouttype,
			returnsno,
			purchasingdtlid,
			quantity,
			amount,
			operatedate,
			administrator,
			operator,
			writeoffid,
			writeoffcause,
			status,
			activeflag,
			createdby,
			createddate,
			lastUpdBy,
			lastUpdDate,
			remark,
			feedingId,
			fedingId,
			price,
			cost,
			contractNo,
			giveNum,
			purchasingNo,
			remainQty,
			productBatchId,
			productBatchDeailId,
			stockPlaceId,
			stockPlaceComId,
			notificationId,
			contractDetailId
		) VALUES
		<foreach collection="rawList" item="item" index= "index" separator =",">
		(
			#{item.company.recordId},
			#{item.material.recordId},
			#{item.supplier.recordId},
			#{item.storehouse.recordId},
			#{item.materialUse.recordId},
			#{item.inoutType},
			#{item.returnsNo},
			#{item.purchasingDtlId},
			#{item.quantity},
			#{item.amount},
			#{item.operateDate},
			#{item.administrator},
			#{item.operator},
			#{item.writeOffId},
			#{item.writeOffCause},
			#{item.status},
			1,
			#{item.createdBy.recordId},
			#{item.createdDate},
			#{item.lastUpdBy.recordId},
			#{item.lastUpdDate},
			#{item.remark},
			#{item.feeding.recordId},
			#{item.feding.recordId},
			#{item.price},
			#{item.cost},
			#{item.contractNo},
			#{item.giveNum},
			#{item.purchasingNo},
			#{item.remainQty},
			#{item.productBatchId},
			#{item.productBatchDeailId},
			#{item.stockPlaceId},
			#{item.stockPlaceComId},
			#{item.notification.recordId},
			#{item.contractDetailId}
		)
		</foreach>
	</insert>

	<insert id="bathInsertTwo" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO st_material_store(
		companyid,
		materialid,
		supplierid,
		storehouseid,
		materialUseId,
		inouttype,
		returnsno,
		purchasingdtlid,
		quantity,
		amount,
		operatedate,
		administrator,
		operator,
		writeoffid,
		writeoffcause,
		status,
		activeflag,
		createdby,
		createddate,
		lastUpdBy,
		lastUpdDate,
		remark,
		feedingId,
		fedingId,
		price,
		cost,
		contractNo,
		giveNum,
		purchasingNo,
		remainQty,
		productBatchId,
		productBatchDeailId,
		stockPlaceId,
		stockPlaceComId,
		notificationId,
		contractDetailId,
		product
		) VALUES(
			#{company.recordId},
			#{material.recordId},
			#{supplier.recordId},
			#{storehouse.recordId},
			#{materialUse.recordId},
			#{inoutType},
			#{returnsNo},
			#{purchasingDtlId},
			#{quantity},
			#{amount},
			#{operateDate},
			#{administrator},
			#{operator},
			#{writeOffId},
			#{writeOffCause},
			#{status},
			1,
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark},
			#{feeding.recordId},
			#{feding.recordId},
			#{price},
			#{cost},
			#{contractNo},
			#{giveNum},
			#{purchasingNo},
			#{remainQty},
			#{productBatchId},
			#{productBatchDeailId},
			#{stockPlaceId},
			#{stockPlaceComId},
			#{notification.recordId},
			#{contractDetailId},
		    #{product}
			)
	</insert>

	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT
		INTO st_material_store(
		companyid,
		materialid,
		supplierid,
		storehouseid,
		materialUseId,
		inouttype,
		returnsno,
		purchasingdtlid,
		quantity,
		amount,
		operatedate,
		administrator,
		operator,
		writeoffid,
		writeoffcause,
		status,
		activeflag,
		createdby,
		createddate,
		lastUpdBy,
		lastUpdDate,
		remark,
		feedingId,
		fedingId,
		price,
		cost,
		contractNo,
		giveNum,
		purchasingNo,
		remainQty,
		productBatchId,
		productBatchDeailId,
		stockPlaceId,
		stockPlaceComId,
		notificationId,
		sourceId,
		contractDetailId,
		deliveryDetailId,
		outboundId,
		rejectId,
		product
		) VALUES (
		#{company.recordId},
		#{material.recordId},
		#{supplier.recordId},
		#{storehouse.recordId},
		#{materialUse.recordId},
		#{inoutType},
		#{returnsNo},
		#{purchasingDtlId},
		#{quantity},
		#{amount},
		#{operateDate},
		#{administrator},
		#{operator},
		#{writeOffId},
		#{writeOffCause},
		#{status},
		#{DEL_FLAG_NORMAL},
		#{createdBy.recordId},
		#{createdDate},
		#{lastUpdBy.recordId},
		#{lastUpdDate},
		#{remark},
		#{feeding.recordId},
		#{feding.recordId},
		#{price},
		#{cost},
		#{contractNo},
		#{giveNum},
		#{purchasingNo},
		#{remainQty},
		#{productBatchId},
		#{productBatchDeailId},
		#{stockPlaceId},
		#{stockPlaceComId},
		#{notification.recordId},
		#{sourceId},
		#{contractDetailId},
		#{deliveryDetailId},
		#{outboundId},
		#{rejectId},
		#{product}
		)
	</insert>
	
	<insert id="insertInit" useGeneratedKeys="true" keyProperty="recordId">
		INSERT
		INTO st_material_store(
		companyid,
		materialid,
		supplierid,
		storehouseid,
		materialUseId,
		inouttype,
		returnsno,
		purchasingdtlid,
		quantity,
		amount,
		operatedate,
		administrator,
		operator,
		writeoffid,
		writeoffcause,
		status,
		activeflag,
		createdby,
		createddate,
		lastUpdBy,
		lastUpdDate,
		remark,
		feedingId,
		fedingId,
		price,
		cost,
		contractNo,
		giveNum,
		purchasingNo,
		remainQty,
		productBatchId,
		productBatchDeailId,
		stockPlaceId,
		stockPlaceComId,
		notificationId
		) VALUES (
		#{company.recordId},
		#{material.recordId},
		#{supplier.recordId},
		#{storehouse.recordId},
		#{materialUse.recordId},
		#{inoutType},
		#{returnsNo},
		#{purchasingDtlId},
		#{quantity},
		#{amount},
		#{operateDateStr},
		#{administrator},
		#{operator},
		#{writeOffId},
		#{writeOffCause},
		#{status},
		#{DEL_FLAG_NORMAL},
		#{createdBy.recordId},
		#{createdDate},
		#{lastUpdBy.recordId},
		#{lastUpdDate},
		#{remark},
		#{feeding.recordId},
		#{feding.recordId},
		#{price},
		#{cost},
		#{contractNo},
		#{giveNum},
		#{purchasingNo},
		#{remainQty},
		#{productBatchId},
		#{productBatchDeailId},
		#{stockPlaceId},
		#{stockPlaceComId},
		#{notification.recordId}
		)
	</insert>

	<update id="update">
		UPDATE st_material_store SET
		companyid = #{company.recordId},
		materialid = #{material.recordId},
		supplierid = #{supplier.recordId},
		storehouseid = #{storehouse.recordId},
		materialUseId = #{materialUse.recordId},
		inouttype = #{inoutType},
		returnsno = #{returnsNo},
		purchasingdtlid = #{purchasingDtlId},
		quantity = #{quantity},
		amount = #{amount},
		operatedate = #{operateDate},
		administrator = #{administrator},
		operator = #{operator},
		writeoffid = #{writeOffId},
		writeoffcause = #{writeOffCause},
		status = #{status},
		lastupdby = #{lastUpdBy.recordId},
		lastupddate = #{lastUpdDate},
		remark = #{remark},
		contractNo = #{contractNo},
		stockPlaceId = #{stockPlaceId},
		stockPlaceComId = #{stockPlaceComId}
		WHERE recordId = #{recordId}
	</update>

	<update id="delete">
		UPDATE st_material_store SET
		activeFlag = #{DEL_FLAG_DELETE}
		WHERE recordId IN (${recordId})
	</update>

	<update id="updateCost">
		UPDATE st_material_store SET
		price = #{price},
		cost = #{cost}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="quaryStocked" resultType="BigDecimal">
		select quantity from st_material_store where materialId=#{materialId}
		and companyId=#{company.recordId}
		and activeFlag =#{company.activeFlag}
		and status <![CDATA[<>]]> #{status} and status <![CDATA[<>]]> 99999903


		<if test="purchasingDetailId!=null and purchasingDetailId!=''">
			and purchasingDtlId=#{purchasingDetailId}
		</if>
		<if test="returnsNo!=null and returnsNo!=''">
			and returnsNo=#{returnsNo}
		</if>
	</select>

	<!-- 查找所有已经补料的原料记录 -->
	<select id="findQtyTSumWithFedings" parameterType="RawmaterialStock" resultType="BigDecimal">
		SELECT
			sum(IFNULL(a.quantity,0) + IFNULL(a.remainQty,0))
		from st_material_store a
		<where>
			a.companyid = #{company.recordId} 
			and a.activeFlag = #{DEL_FLAG_NORMAL} 
			and a.fedingId = #{feding.recordId}
		</where>
	</select>
	
	<!-- zjn 2017-07-17 根据投料单id查询投料出入库记录 -->
	<select id="getRawmaterialByfeedingId" resultType="RawmaterialStock">
		SELECT a.recordid AS "recordId",
		a.companyid AS "company.recordId",
		a.materialid AS "material.recordId",
		a.feedingId AS "feeding.recordId",
		a.fedingId AS "feding.recordId",
		a.supplierid AS "supplier.recordId",
		a.storehouseid AS "storehouse.recordId",
		a.inouttype AS "inoutType",
		a.returnsno AS "returnsNo",
		a.purchasingdtlid AS "purchasingDtlId",
		a.quantity AS "quantity",
		a.amount AS "amount",
		a.operatedate AS "operateDate",
		a.administrator AS "administrator",
		a.operator AS "operator",
		a.writeoffid AS "writeOffId",
		a.writeoffcause AS "writeOffCause",
		a.status AS "status",
		a.activeflag AS "activeFlag",
		a.createdby AS "createdby",
		a.createddate AS "createdDate",
		a.lastupdby AS "lastupdby",
		a.lastupddate AS "lastUpdDate",
		a.remark AS "remark",
		a.cost AS "cost",
		b.no AS "material.no",
		a.stockPlaceId,
		a.stockPlaceComId,
		a.productBatchDeailId
		FROM st_material_store a 
		LEFT JOIN md_material b ON a.materialId = b.recordId
        LEFT JOIN pd_feeding c ON a.feedingId = c.recordId
		LEFT JOIN pd_produce_batch_detail d ON d.recordId = a.productBatchDeailId
        WHERE c.recordId =#{recordId} AND c.companyId = #{company.recordId} and a.status= '********'
	</select>
	
	<!-- zjn 2017-07-17 根据补料单id查询投料出入库记录 -->
	<select id="getRawmaterialByfedingId" resultType="RawmaterialStock">
		SELECT 
		a.recordid AS "recordId",
		a.companyid AS "company.recordId",
		a.materialid AS "material.recordId",
		a.feedingId AS "feeding.recordId",
		a.fedingId AS "feding.recordId",
		a.supplierid AS "supplier.recordId",
		a.storehouseid AS "storehouse.recordId",
		a.inouttype AS "inoutType",
		a.returnsno AS "returnsNo",
		a.purchasingdtlid AS "purchasingDtlId",
		a.quantity AS "quantity",
		a.amount AS "amount",
		a.operatedate AS "operateDate",
		a.administrator AS "administrator",
		a.operator AS "operator",
		a.writeoffid AS "writeOffId",
		a.writeoffcause AS "writeOffCause",
		a.status AS "status",
		a.activeflag AS "activeFlag",
		a.createdby AS "createdby",
		a.createddate AS "createdDate",
		a.lastupdby AS "lastupdby",
		a.lastupddate AS "lastUpdDate",
		a.remark AS "remark",
		a.cost AS "cost",
		b.no AS "material.no",
		a.stockPlaceId,
		a.stockPlaceComId
		FROM st_material_store a LEFT JOIN md_material b ON a.materialId = b.recordId
        LEFT JOIN pd_replenish c ON a.fedingId = c.recordId
        WHERE c.recordId =#{recordId} AND c.companyId = #{company.recordId} and a.status= '********'
	</select>
	
	<select id="getConcatNoByNotifiId" resultType="String">
	    SELECT
			GROUP_CONCAT(d.`no`)
		FROM
			sl_notification a
		LEFT JOIN sl_notification b ON b.mergeId = a.recordId
		LEFT JOIN sl_contract_detail c ON c.recordId = IFNULL(
			a.contractDetailId,
			b.contractDetailId
		)
		LEFT JOIN sl_contract d ON d.recordId = c.contractId
		WHERE
			a.recordId = #{notificationId}
	</select>

	<!-- TJ 2017-09-06 修改出入库记录的合同编号  -->
	<update id="updateContractNo">
	    UPDATE st_material_store SET contractNo = #{contractNo}  WHERE recordId = #{recordId}
	</update>
	
	<select id="findListGroupDate" resultType="RawmaterialStock">
		SELECT 
			<include refid="MaterialStoreColumns" />
		FROM st_material_store a
		<include refid="MaterialStoreJoins" />
		WHERE a.companyId = #{company.recordId}
		AND a.activeFlag = #{DEL_FLAG_NORMAL}
		AND a.status= '********'
		AND a.inoutType = #{inoutType}
		AND a.materialId = #{material.recordId}
		GROUP BY date_format(a.operatedate,'%Y-%m')
		ORDER BY a.operatedate DESC
	</select>
	
	<select id="findSumCostGroupDate" resultType="RawmaterialStock">
		SELECT 
			SUM(IFNULL(a.cost,0)) AS "cost",SUM(IFNULL(a.quantity,0)) AS "quantity"
		FROM st_material_store a
		<where>
			a.companyId = #{company.recordId}
			AND a.activeFlag = #{DEL_FLAG_NORMAL}
			AND a.status= '********'
			AND a.inoutType = #{inoutType}
			AND a.materialId = #{material.recordId}
		</where>
		<choose>
			<when test="dates != null and dates.length > 0">
				AND date_format(a.operatedate,'%Y-%m') IN
			    <foreach collection="dates" item ="item" open="(" separator="," close=")">
			    	date_format(#{item},'%Y-%m')  
			    </foreach>  
			</when>
			<otherwise>
			</otherwise>
		</choose>
		
	</select>
	
	<!-- zjn 2018-08-22 获取收付款对账单明细id -->
	<select id="getCheckId" resultType="String">
		<if test="inoutType == 2">
			select
			recordId
			from pu_material_check
			where companyId = #{company.recordId} AND sourceId = #{recordId} limit 0,1
		</if>
		<if test="inoutType == 6">
			select
			recordId
			from sl_goods_check
			where companyId = #{company.recordId} AND sourceId = #{recordId} limit 0,1
		</if>
	</select>

	<select id="findRawmaterialStockByDetail" resultType="RawmaterialStock">
		SELECT 
			a.recordId,
			a.companyId AS "company.recordId",
			a.materialId AS "material.recordId",
			a.storeHouseId AS "storeHouse.recordId",
			a.purchasingDtlId AS "purchasingDtlId",
			a.inOutType,
			a.quantity,
			a.createdBy AS "createdBy.recordId",
			a.amount
		FROM st_material_store a
		LEFT JOIN pu_purchasing_detail ppd ON ppd.recordId = a.purchasingDtlId
		WHERE ppd.recordId = #{recordId}
		AND a.`status` = ********
	</select>
	
	<update id="updataProduceBatchDeail">
		UPDATE pd_produce_batch_detail SET 
			stockFlag = 1,
		 	haveBoard = #{haveBoard},
		 	cloutBoard = #{cloutBoard},
		 	useBoard = #{useBoard},
		 	acDistributeDate =
		 	(
		 		SELECT operateDate FROM st_material_store WHERE inOutType = 3 AND activeFlag = 1 AND productBatchDeailId = #{recordId} AND `status` = ******** LIMIT 1
		 	)
		WHERE
			recordId = #{recordId}
	</update>
	
	<update id="starving">
		UPDATE pd_produce_batch_detail SET 
		 	starvingBoard = #{haveBoard}
		WHERE
			recordId = #{recordId}
	</update>
	
	<select id="getUnFinProDeail" resultType="Integer">
		SELECT
			COUNT(aa.recordId)
		FROM
			pd_produce_batch_detail aa
		LEFT JOIN pd_produce_batch bb ON bb.recordId = aa.produceBatchId
		WHERE
			bb.feedingNo IN (
				SELECT
					c.recordId
				FROM
					pd_produce_batch_detail a
				LEFT JOIN pd_produce_batch b ON b.recordId = a.produceBatchId
				LEFT JOIN pd_feeding c ON c.recordId = b.feedingNo
				WHERE
					a.recordId = #{recordId}
			)
		AND aa.stockFlag IS NULL
	</select>
	<!-- rx.add 部分冲红，冲红单和已冲红单的数量不一致，也需要增加这两种类型,99999903,99999902 -->
	<select id="getWarehousingSum" resultType="BigDecimal">
		SELECT
			SUM(
				CASE
					a.inOutType
					WHEN 1 THEN
						IFNULL(a.quantity, 0)
					WHEN 4 THEN
						IFNULL(- a.quantity, 0)
					WHEN 7 THEN
						IFNULL(a.quantity, 0) ELSE 0
					END
			    ) AS "quantity"
		FROM st_material_store a
		LEFT JOIN pu_purchasing_detail ppd ON ppd.recordId = a.purchasingDtlId
		WHERE ppd.recordId = #{recordId} AND a.activeFlag = 1
		AND a.`status` in (********,99999903,99999902)
	</select>
	
	<!-- zjn 2019-10-23 根据投补料id获取出库记录  -->
	<select id="getRawStockCount" resultType="Integer">
		SELECT
			COUNT(recordId)
		FROM st_material_store 
		<where>
			companyId = #{company.recordId} AND activeFlag = 1
			<!-- 投料出库 -->
			<if test="feeding != null and feeding.recordId != null and feeding.recordId != ''">
				AND feedingId = #{feeding.recordId} AND inOutType = 3
			</if>
			<!-- 补料出库 -->
			<if test="feding != null and feding.recordId != null and feding.recordId != ''">
				AND fedingId = #{feding.recordId} AND inOutType = 5
			</if>
		</where>
	</select>
	
	<select id="getTaxDescriptValue" resultType="String">
		SELECT
			REPLACE(GROUP_CONCAT(DISTINCT d.`value`),"含税","") 
		FROM sl_notification a
		LEFT JOIN sl_contract_detail b ON b.recordId = a.contractDetailId AND b.activeFlag = 1
		LEFT JOIN sl_contract c ON c.recordId = b.contractId AND c.activeFlag = 1
		LEFT JOIN md_dict_value d ON d.recordId = c.taxDescript AND d.activeFlag = 1
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 AND a.mergeId = #{recordId}
	</select>
	
	<select id="getStocks" resultType="RawmaterialStock">
		SELECT
			SUM(CASE inOutType WHEN 1 THEN IFNULL(quantity,0)
			WHEN 2 THEN IFNULL(quantity,0)
			WHEN 3 THEN IFNULL(-quantity,0)
			WHEN 4 THEN IFNULL(-quantity,0)
			WHEN 5 THEN IFNULL(-quantity,0)
			WHEN 6 THEN IFNULL(-quantity,0)
			WHEN 7 THEN IFNULL(quantity,0)
			WHEN 8 THEN IFNULL(-quantity,0)
			WHEN 9 THEN IFNULL(quantity,0)
			WHEN 10 THEN IFNULL(-quantity,0)
			WHEN 20 THEN IFNULL(quantity,0)
			ELSE 0 END) AS "quantity",
			SUM(CASE inOutType WHEN 1 THEN IFNULL(quantity,0)
			WHEN 2 THEN IFNULL(quantity,0)
			WHEN 7 THEN IFNULL(quantity,0)
			WHEN 9 THEN IFNULL(quantity,0)
			WHEN 20 THEN IFNULL(quantity,0)
			ELSE 0 END) AS "inStocks",
			SUM(CASE inOutType WHEN WHEN 3 THEN IFNULL(quantity,0)
			WHEN 4 THEN IFNULL(quantity,0)
			WHEN 5 THEN IFNULL(quantity,0)
			WHEN 6 THEN IFNULL(quantity,0)
			WHEN 8 THEN IFNULL(quantity,0)
			WHEN 10 THEN IFNULL(quantity,0)
			ELSE 0 END) AS "outStocks"
		FROM
			st_material_store
		WHERE companyId = #{company.recordId} AND activeFlag = 1
		AND materialId = #{material.recordId} AND status = ********
		AND ISNULL(stockPlaceComId)
		<if test="createdDate != null and createdDate != ''">
			AND createdDate <![CDATA[<]]> #{createdDate}
		</if>
		<if test="queryDate != null and queryDate != ''">
			AND operateDate >= #{queryDate}
		</if>
	</select>
	
	<select id="findRawmaterialStockWithFeedingsTwo" parameterType="RawmaterialStock" resultType="RawmaterialStock">
		SELECT
			a.recordId AS "recordId",
			IFNULL(a.quantity,0) + IFNULL(a.remainQty,0)  AS "quantity",
			a.status as "status",
			a.feedingId AS "feeding.recordId",
			pf.pcsQuantity AS "feeding.pcsQuantity",
			pf.boardAPnlQty AS "feeding.boardAPnlQty",
			pf.boardBPnlQty AS "feeding.boardBPnlQty",
			pf.boardQuantity AS "feeding.boardQuantity"
		FROM st_material_store a
		JOIN pd_feeding pf on a.feedingId = pf.recordId
		<where>
			a.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
			AND (
				a.feedingId
			) IN
			<foreach collection="feedingList" item="item" index="index" open="(" close=")" separator=",">
				#{item.recordId}
			</foreach>
		</where>
	</select>
	
	<update id="updateStockPlaceComId">
		UPDATE st_material_store SET
			stockPlaceComId = #{stockPlaceComId}
		WHERE recordId IN(${recordId})
	</update>
	
	<select id="getPurInStockQty" resultType="BigDecimal">
		SELECT 
			SUM(IFNULL(quantity,0))
		FROM st_material_store 
		WHERE companyId = #{company.recordId} AND activeFlag = 1 AND inOutType = 1 
		AND `status` = ******** AND purchasingDtlId = #{recordId}
	</select>
	
	<update id="updateRawRecordCostPrice">
		UPDATE st_material_store SET
			price = #{price},
			cost = IFNULL(quantity,0) * IFNULL(#{price},0)
		WHERE companyId = #{company.recordId} AND activeFlag = 1 AND inOutType = 1 
		AND `status` = ******** AND purchasingDtlId = #{recordId} 
	</update>
	
	<select id="getPrintRawStockData" resultType="RawmaterialStock">
		SELECT
			a.recordId,
			CASE WHEN !ISNULL(a.remainQty) AND a.inouttype IN(3,5) 
			THEN IFNULL(a.quantity,0) + IFNULL(a.remainQty,0) ELSE a.quantity END AS "quantity",
			ROUND(a.price,4) AS "price",
			ROUND(IFNULL(a.cost,IFNULL(a.quantity,0) * IFNULL(a.price,0)),2) AS "cost",
			IFNULL(mat.recordId,inter.recordId)  AS "material.recordId",
			IFNULL(mat.`no`,CONCAT('YL', inter.recordId)) AS "material.no",
			IFNULL(mat.`name`,inter.`name`) AS "material.name",
			unit.`value` AS "material.unit.value",
			IFNULL(IFNULL(cc.`value`, mat.specification),inter.specifications) AS "material.specification",
    		IFNULL(mat.specification,inter.specifications) AS "spec",
			dd.`value` AS "material.boardThicknessValue",
			ee.`value` AS "material.daoreValue",
			IFNULL(sup.recordId,sup2.recordId) AS "supplier.recordId",
			IFNULL(sup.shortName,sup2.shortName) AS "supplier.shortName",
			pf.recordId AS "feeding.recordId",
			pf.`no` AS "feeding.no",
			pf.pcsQuantity AS "feeding.pcsQuantity",
			sn.craftNo,
			pr.`no` AS "feding.no",
			pr.discardQty AS "feding.discardQty",
			(SELECT
				MAX(printNo)
			FROM
				st_material_store
			WHERE
				inOutType = a.inOutType
			AND companyId = #{company.recordId}
			AND activeFlag = 1
			AND `status` = ********
			AND DATE_FORMAT(printTime, "%y%m%d") = DATE_FORMAT(NOW(), "%y%m%d")) AS "printNo",
			c.`no` AS "purchNo",
			a.purchasingdtlid,
			b.price AS "rawPrice"
		FROM st_material_store a
		LEFT JOIN md_material mat ON mat.recordId = a.materialId AND mat.companyId = a.companyId AND (a.product = 1 OR a.product IS NULL)
		LEFT JOIN md_supplier sup ON sup.recordId = a.supplierid AND sup.companyId = a.companyId
		LEFT JOIN pd_feeding pf ON pf.recordId = a.feedingId AND pf.companyId = a.companyId
		LEFT JOIN pd_replenish pr ON pr.recordId = a.fedingId AND pr.companyId = a.companyId
		LEFT JOIN sl_notification sn ON sn.recordId = IFNULL(pf.notificationId,pr.notificationId) AND sn.companyId = a.companyId
		LEFT JOIN md_dict_value unit ON unit.recordId = mat.unit AND unit.companyId = a.companyId
		LEFT JOIN md_supplier sup2 ON sup2.recordId = mat.supplierId AND sup2.companyId = a.companyId
		LEFT JOIN 
		(
			SELECT
				aa.materialId AS "materialId",
				GROUP_CONCAT(aa.`value` SEPARATOR '*') AS "value"
			FROM md_material_specification_relation aa
			LEFT JOIN md_material_specification bb ON bb.recordId = aa.specificationId
			WHERE aa.companyId = #{company.recordId} AND aa.activeFlag = 1
			AND (bb.`name` = '长' || bb.`name` = '宽')
			GROUP BY aa.materialId
		) cc ON cc.materialId = a.materialId
		LEFT JOIN
		(
			SELECT
				aa.materialId AS "materialId",
				mdv.`value` AS "value"
			FROM md_material_specification_relation aa
			LEFT JOIN md_material_specification bb ON bb.recordId = aa.specificationId
			LEFT JOIN md_dict_value mdv ON mdv.recordId = aa.`value`
			WHERE aa.companyId = #{company.recordId} AND aa.activeFlag = 1 AND mdv.itemId = 3
			GROUP BY aa.materialId
		) dd ON dd.materialId = a.materialId
		LEFT JOIN
		(
			SELECT
				aa.materialId AS "materialId",
				mdv.`value` AS "value"
			FROM md_material_specification_relation aa
			LEFT JOIN md_material_specification bb ON bb.recordId = aa.specificationId
			LEFT JOIN md_dict_value mdv ON mdv.recordId = aa.`value`
			WHERE aa.companyId = #{company.recordId} AND aa.activeFlag = 1 AND mdv.itemId = 58
			GROUP BY aa.materialId
		) ee ON ee.materialId = a.materialId
		LEFT JOIN pu_purchasing_detail b ON b.recordId = a.purchasingDtlId
		LEFT JOIN pu_purchasing c ON c.recordId = b.purchasingId
		LEFT JOIN inter_config_supplier inter ON inter.recordId = a.materialId AND a.product = 2
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1 
		AND a.recordId IN(${recordId})
		ORDER BY a.operateDate ASC
	</select>
	
	<update id="updatePrintNo">
		UPDATE st_material_store
		SET printNo = #{printNo},
			printTime = NOW(),
			printTimeNo = #{printTimeNo}
		WHERE
			FIND_IN_SET(recordId, #{ids});
	</update>
	
	<select id="findMonthMaterialList" resultType="RawmaterialStock">
		SELECT
			a.recordId,
			a.`no` AS "materialNo",
			a.`name` AS "materialName",
			a.specification AS "specification",
			mdd.`value` AS "materialTypeVal",
			mdv.`value` AS "unitValue",
			msh.`name` AS "storeName",
			(
				SELECT
					price
				FROM
					md_material_price_init
				WHERE
					materialId = a.recordId
					AND activeFlag = 1
				AND initDate <![CDATA[ <= ]]> dateRange.beginTime
				ORDER BY
					initDate DESC
				LIMIT 1
			) AS initPrice,
			dateRange.beginTime AS "inoutTimeStartQr" ,
			dateRange.monthNum,
			DATE_FORMAT( last_day( dateRange.beginTime ), '%Y-%m-%d 23:59:59.059') AS "inoutTimeEndQr" 
		FROM
			md_material a
		LEFT JOIN md_dict_value mdd ON mdd.recordId = a.materialType
		LEFT JOIN md_dict_value mdv ON mdv.recordId = a.unit
		LEFT JOIN md_store_house msh ON msh.recordId = a.storehouseid
		LEFT JOIN (
			SELECT MONTH
				( @startTime ) 'monthNum',
				@startTime AS 'beginTime',
				@startTime := DATE_ADD( @startTime, INTERVAL 1 MONTH ) 
			FROM
				mysql.help_topic,
				(
				SELECT
				@startTime := DATE_FORMAT( #{inoutTimeStartQr}, '%Y-%m-%01' )) a 
			WHERE
			@startTime <![CDATA[ <= ]]>  DATE_FORMAT( #{inoutTimeEndQr}, '%Y-%m-%01' )) dateRange ON 1 = 1 
		WHERE
			a.companyid = #{company.recordId}
		AND a.activeFlag = 1
		AND a.materialKind = 100701
		AND a.`status` = 1
		<if test="materialNo != null and materialNo != ''">
			AND a.no like CONCAT('%', #{materialNo}, '%') 
		</if>
		<if test="materialName != null and materialName != ''">
			AND a.name like CONCAT('%', #{materialName}, '%')
		</if>
		<if test="search1 != null and search1 != ''">
			AND a.specification like CONCAT('%', #{search1}, '%')
		</if>
		<if test="search2 != null and search2 != ''">
			AND a.specification like CONCAT('%', #{search2}, '%')
		</if>
		<if test="search3 != null and search3 != ''">
			AND a.specification like CONCAT('%', #{search3}, '%')
		</if>
		<if test="search4 != null and search4 != ''">
			AND a.specification like CONCAT('%', #{search4}, '%')
		</if>
		<if test="search5 != null and search5 != ''">
			AND a.specification like CONCAT('%', #{search5}, '%')
		</if>
		<if test="search6 != null and search6 != ''">
			AND a.specification like CONCAT('%', #{search6}, '%')
		</if>
		<if test="materialTypeId != null and materialTypeId !=''">
			AND a.materialType =#{materialTypeId}
		</if>
		<if test="storeHouseId != null and storeHouseId !=''">
			AND a.storeHouseId =#{storeHouseId}
		</if>
		ORDER BY
			a.createdDate DESC
	</select>

	<insert id="bathInsertMaterialMonth" parameterType="java.util.List">
	INSERT INTO md_material_month(
			companyId,
			month,
			materialId,
			quantity,
			price,
			amount,
			inQuantity,
			outQuantity,
			inAmount,
			outAmount,
			status,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			remark
		) VALUES
		<foreach collection="rawList" item="item" index= "index" separator =",">
		(
			#{item.company.recordId},
			#{item.month},
			#{item.materialId},
			#{item.quantity},
			#{item.price},
			#{item.amount},
			#{item.inQuantity},
			#{item.outQuantity},
			#{item.inAmount},
			#{item.outAmount},
			1,
			#{item.createdBy},
			#{item.createdDate},
			#{item.lastUpdBy},
			#{item.lastUpdDate},
			#{item.remark}
		)
		</foreach>
	</insert>
	
	<update id="updatePriceInfo">
		UPDATE st_material_store set price = #{price},cost =#{cost} 
		WHERE recordId = #{recordId} AND IFNULL(price,0) = 0
	
	</update>
	
	<select id="getInPurchList" resultType="RawmaterialStock">
		SELECT 
			<include refid="MaterialStoreColumns" />
		FROM st_material_store a
		<include refid="MaterialStoreJoins" />
		LEFT JOIN pu_purchasing_detail b ON b.recordId = a.purchasingDtlId
		LEFT JOIN pu_purchasing c ON c.recordId = b.purchasingId
		LEFT JOIN md_dict_value d ON d.recordId = c.payWay
		WHERE a.inOutType = 1 AND a.activeFlag = 1
		AND a.`status` = ******** AND d.`value` <![CDATA[<>]]> '现金'
		AND a.recordId NOT IN
		(
			SELECT 
				aa.sourceId 
			FROM pu_material_check aa
			LEFT JOIN st_material_store bb ON bb.recordId = aa.sourceId AND bb.inOutType = 1
			LEFT JOIN md_material cc ON cc.recordId = aa.materialId
			WHERE aa.activeFlag = 1 AND aa.inOutFlag = 1 
			AND aa.`status` = ******** AND cc.materialKind = 100701
			AND bb.recordId IS NOT NULL
		) AND a.operateDate >= '2021-11-01' 
<!-- 		AND IFNULL( -->
<!-- 			( -->
<!-- 				SELECT  -->
<!-- 					`status`  -->
<!-- 				FROM pu_accounts_payable  -->
<!-- 				WHERE supplierId = a.supplierId AND period = DATE_FORMAT(a.operateDate,'%Y%m') AND activeFlag = 1 -->
<!-- 			),2001) IN (2001,2002) -->
	</select>
	
	<select id="getPurchReturnList" resultType="RawmaterialStock">
		SELECT 
			<include refid="MaterialStoreColumns" />,
			b.desireTreatment AS "desireTreatments"
		FROM st_material_store a
		<include refid="MaterialStoreJoins" />
		LEFT JOIN pu_returns_detail b ON b.recordId = a.returnsNo
		WHERE a.inOutType IN(4,7) AND a.activeFlag = 1
		AND a.`status` = ******** AND b.purchasingType = 1
		AND a.recordId NOT IN
		(
			SELECT 
				sourceId 
			FROM pu_material_check aa
			LEFT JOIN pu_returns_detail bb ON bb.recordId = aa.sourceId
			LEFT JOIN md_material cc ON cc.recordId = aa.materialId
			WHERE aa.activeFlag = 1 AND aa.inOutFlag = 1 
			AND aa.`status` = ******** AND cc.materialKind = 100701
			AND bb.recordId IS NOT NULL AND bb.purchasingType = 1
		) AND a.recordId IN (74739,74740)
	</select>

	<update id="bindDeliveryMsg">
		UPDATE st_material_store SET
			deliveryNo = #{deliveryNo},
			deliveryDate = #{deliveryDate}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="updateSourceId">
		UPDATE st_material_store SET
			sourceId = #{recordId}
		WHERE recordId = #{recordId}
	</update>
	
	<select id="getCountBySourceId" resultType="Integer">
		SELECT 
			COUNT(1)
		FROM st_material_store
		WHERE companyId = #{company.recordId} AND sourceId = #{recordId}
		AND inOutType = 1 AND activeFlag = 1 AND `status` = ********
	</select>
	
	<select id="getSumPurQty" resultType="java.math.BigDecimal">
		SELECT
			SUM(IFNULL(quantity,0))
		FROM st_material_store
		WHERE activeFlag = 1 AND purchasingDtlId = #{recordId} AND inOutType = 1 AND status = ********
	</select>
	
	<select id="getUsedMaterialStrict" resultType="Integer">
		SELECT start FROM md_parameterset WHERE companyId = (
		SELECT scd2.companyId
			FROM sl_notification sn
			LEFT JOIN sl_notification sn2 ON sn.mergeType = 200602 AND sn2.mergeId = sn.recordId AND sn2.companyId = sn.companyId AND sn2.activeFlag = 1
			LEFT JOIN sl_contract_detail scd ON scd.recordId = IF(sn.mergeType = 200602,sn2.contractDetailId,sn.contractDetailId) AND scd.activeFlag = 1
			LEFT JOIN icloud_group_center igc ON igc.recordId = scd.groupCenterId
			LEFT JOIN sl_contract_detail scd2 ON scd2.recordId = igc.contactDeailId
			WHERE sn.recordId = #{recordId} LIMIT 1
		) AND jianPin = 'usedMaterialStrict' LIMIT 1
	</select>
	
	<select id="getSupplyChainContractDetail" resultType="MaterialContractDetail">
		SELECT 
			smcd.recordId,
			smcd.orgId,
			smc.`no` AS 'materialContract.no',
			mc.recordId AS 'materialContract.customer.recordId',
			mc.checkDate AS 'materialContract.customer.checkDate'
		FROM pu_purchasing_detail ppd
		LEFT JOIN sl_material_contract_detail smcd ON smcd.recordId = ppd.contractDeailId
		LEFT JOIN sl_material_contract smc ON smc.recordId = smcd.purchasingId
		LEFT JOIN md_customer mc ON mc.recordId = smc.customerId
		WHERE ppd.recordId = #{recordId}
	</select>
	
	<select id="getRawSupplyChainData" resultType="RawmaterialStock">
		SELECT
			a.recordId,
			a.companyId AS "company.recordId",
			b.recordId AS "purchasingDtlId",
			c.recordId AS "sourceDetectionId",
			a.quantity,
			a.operateDate,
			a.status,
			a.writeOffCause
		FROM st_material_store a
		LEFT JOIN pu_purchasing_detail b ON b.recordId = a.purchasingDtlId
		LEFT JOIN qc_source_detection c ON c.orderId = b.recordId AND c.companyId = a.companyId AND c.resourceType = 1 
		AND c.detectionNum = a.quantity AND c.activeFlag = 1
		WHERE b.purchasingId = #{recordId} AND a.activeFlag = 1 AND a.inOutType = 1 AND a.`status` = ********
		GROUP BY a.recordId
	</select>
	
	<select id="getRawListByIds" resultType="RawmaterialStock">
		SELECT
			*,
			rejectId AS "complaintId" 
		FROM st_material_store
		WHERE companyId = #{companyId} AND activeFlag = 1
		AND `status` = ******** AND	FIND_IN_SET(contractDetailId,#{ids})
		ORDER BY operateDate ASC
	</select>

	<select id="getCountTotalInfo" resultType="RawmaterialStock">
		SELECT
		sum(
		(IFNULL((
		SELECT
		SUM(CASE inOutType WHEN 1 THEN IFNULL(quantity,0)
		WHEN 2 THEN IFNULL(quantity,0)
		WHEN 3 THEN IFNULL(-quantity,0)
		WHEN 4 THEN IFNULL(-quantity,0)
		WHEN 5 THEN IFNULL(-quantity,0)
		WHEN 6 THEN IFNULL(-quantity,0)
		WHEN 7 THEN IFNULL(quantity,0)
		WHEN 8 THEN IFNULL(-quantity,0)
		WHEN 9 THEN IFNULL(quantity,0)
		WHEN 10 THEN IFNULL(-quantity,0)
		WHEN 20 THEN IFNULL(quantity,0)
		ELSE 0 END)
		FROM st_material_store
		WHERE companyId = #{factoryComId}
		AND activeFlag = 1
		AND materialId = a.recordId
		AND DATE_FORMAT(operateDate,"%y%m%d") <![CDATA[ >= ]]> DATE_FORMAT(IFNULL(a.initDate,'2021-01-01'),"%y%m%d")
		),0) +
		IFNULL(a.initStocks,0))
		) AS 'quantity',
		sum(IFNULL(a.initPrice,0)*
		(IFNULL((
		SELECT
		SUM(CASE inOutType WHEN 1 THEN IFNULL(quantity,0)
		WHEN 2 THEN IFNULL(quantity,0)
		WHEN 3 THEN IFNULL(-quantity,0)
		WHEN 4 THEN IFNULL(-quantity,0)
		WHEN 5 THEN IFNULL(-quantity,0)
		WHEN 6 THEN IFNULL(-quantity,0)
		WHEN 7 THEN IFNULL(quantity,0)
		WHEN 8 THEN IFNULL(-quantity,0)
		WHEN 9 THEN IFNULL(quantity,0)
		WHEN 10 THEN IFNULL(-quantity,0)
		WHEN 20 THEN IFNULL(quantity,0)
		ELSE 0 END)
		FROM st_material_store
		WHERE companyId = #{factoryComId}
		AND activeFlag = 1
		AND materialId = a.recordId
		AND DATE_FORMAT(operateDate,"%y%m%d") <![CDATA[ >= ]]> DATE_FORMAT(IFNULL(a.initDate,'2021-01-01'),"%y%m%d")
		),0) +
		IFNULL(a.initStocks,0))) AS 'amount'
		FROM
		md_material a
		WHERE
		a.companyid = #{company.recordId}
		AND a.activeFlag = 1
		AND a.materialKind = 100701
		AND a.`status` = 1
		<if test="materialNo != null and materialNo != ''">
			AND a.no like CONCAT('%', #{materialNo}, '%')
		</if>
		<if test="materialName != null and materialName != ''">
			AND a.name like CONCAT('%', #{materialName}, '%')
		</if>
		<if test="search1 != null and search1 != ''">
			AND a.specification like CONCAT('%', #{search1}, '%')
		</if>
		<if test="search2 != null and search2 != ''">
			AND a.specification like CONCAT('%', #{search2}, '%')
		</if>
		<if test="search3 != null and search3 != ''">
			AND a.specification like CONCAT('%', #{search3}, '%')
		</if>
		<if test="search4 != null and search4 != ''">
			AND a.specification like CONCAT('%', #{search4}, '%')
		</if>
		<if test="search5 != null and search5 != ''">
			AND a.specification like CONCAT('%', #{search5}, '%')
		</if>
		<if test="search6 != null and search6 != ''">
			AND a.specification like CONCAT('%', #{search6}, '%')
		</if>
		<if test="materialTypeId != null and materialTypeId !=''">
			AND a.materialType =#{materialTypeId}
		</if>
		<if test="storeHouseId != null and storeHouseId !=''">
			AND FIND_IN_SET(a.storeHouseId,#{storeHouseId})
		</if>
		<if test="stockPlaceId != null and stockPlaceId !=''">
			AND a.recordId in (select DISTINCT materialId from md_material_childcompany WHERE  materPlaceId =#{stockPlaceId})
		</if>
		<if test="haveStockFlag != null and haveStockFlag !='' and haveStockFlag == 1">
			AND (
			IFNULL((
			SELECT
			SUM(CASE inOutType WHEN 1 THEN IFNULL(quantity,0)
			WHEN 2 THEN IFNULL(quantity,0)
			WHEN 3 THEN IFNULL(-quantity,0)
			WHEN 4 THEN IFNULL(-quantity,0)
			WHEN 5 THEN IFNULL(-quantity,0)
			WHEN 6 THEN IFNULL(-quantity,0)
			WHEN 7 THEN IFNULL(quantity,0)
			WHEN 8 THEN IFNULL(-quantity,0)
			WHEN 9 THEN IFNULL(quantity,0)
			WHEN 10 THEN IFNULL(-quantity,0)
			WHEN 20 THEN IFNULL(quantity,0)
			ELSE 0 END)
			FROM st_material_store
			WHERE companyId = #{company.recordId}
			AND activeFlag = 1
			AND materialId = a.recordId
			AND status = ********
			AND DATE_FORMAT(operateDate,"%y%m%d") <![CDATA[ >= ]]> DATE_FORMAT(IFNULL(a.initDate,'2021-01-01'),"%y%m%d")
			),0) +
			IFNULL(a.initStocks,0)
			) > 0
		</if>
		ORDER BY
		a.createdDate DESC
	</select>

	<select id="getRawmaterialStockList" resultType="RawmaterialStock">
		SELECT
			b.sourceNo,
			IFNULL( LOCATE( 'MJ', c.`name` ), 0 ) AS "mjCount",
			IFNULL( LOCATE( 'CSJ', c.`name` ), 0 ) AS "csjCount"
		FROM pu_purchasing_detail a
		LEFT JOIN pu_purchasing b ON b.recordId = a.purchasingId
		LEFT JOIN md_material c ON c.recordId = a.materialId
		WHERE
			a.recordId = #{recordId}
		  	AND b.sourceNo IS NOT NULL;
	</select>

	<select id="getListByContractDetailIds" resultType="RawmaterialStock">
		SELECT
			f.recordId AS "contractDetailId",
			(
				SELECT
					GROUP_CONCAT(DISTINCT `no`)
				FROM md_material
				WHERE companyId = sms.companyId AND activeFlag = 1 AND FIND_IN_SET(recordId,IFNULL(c.materialIds,c.materialId))
			) AS "matConcatValue",
			sms.operateDate,
			h.no AS "matConcatValueTwo"
		FROM st_material_store sms
		LEFT JOIN pd_produce_batch_detail a ON a.recordId = sms.productBatchDeailId
		LEFT JOIN pd_produce_batch b ON b.recordId = a.produceBatchId
		LEFT JOIN eg_carda c ON c.recordId = b.processCardAId
		LEFT JOIN sl_notification d ON d.recordId = b.notificationId
		LEFT JOIN sl_notification e ON e.mergeId = d.recordId
		LEFT JOIN sl_contract_detail f ON f.recordId = IFNULL(d.contractDetailId,e.contractDetailId)
		LEFT JOIN md_material h ON h.recordId = sms.materialId
		WHERE sms.activeFlag = 1 AND sms.`status` = ******** AND FIND_IN_SET(f.recordId,#{contractDetailId})
		ORDER BY sms.operateDate DESC
	</select>

	<update id="batchUpdatePrice">
		<foreach collection="list" item="item" separator=";">
			UPDATE st_material_store SET
				price = #{item.price}
			WHERE recordId = #{item.recordId}
		</foreach>
	</update>
	<update id="updateMaterFeeDeference">
	UPDATE st_material_store aa INNER JOIN
	(
		SELECT
			a.recordId AS "recordId",
			SUM(CASE WHEN g.materialFee IS NOT NULL THEN IFNULL(f.materialFee,0) - IFNULL(g.materialFee,0) ELSE 0 END) AS "differenceAmount"
		FROM st_material_store a
		LEFT JOIN pd_feeding b ON b.recordId = a.feedingId
		LEFT JOIN sl_notification c ON c.recordId = b.notificationId
		LEFT JOIN sl_notification d ON d.mergeId = c.recordId AND d.companyId = c.companyId AND d.activeFlag = 1
		LEFT JOIN sl_contract_detail e ON e.recordId = IFNULL(d.contractDetailId,c.contractDetailId)
		LEFT JOIN icloud_group_center f ON f.recordId = e.groupCenterId
		LEFT JOIN
		(
			SELECT
				aa.groupCenterId AS "groupCenterId",
				aa.materialFee AS "materialFee"
			FROM
			(
				SELECT
					groupCenterId AS "groupCenterId",
					materialFee AS "materialFee"
				FROM icloud_group_center_clone_two
				WHERE activeFlag = 1
				ORDER BY createdDate DESC
			) aa GROUP BY aa.groupCenterId
		) g ON g.groupCenterId = f.recordId
		WHERE a.companyId = #{companyId} AND a.activeFlag = 1 AND b.recordId = #{recordId}
        AND a.status = ********
		GROUP BY a.recordId ORDER BY a.recordId ASC LIMIT 1
	) bb ON bb.recordId = aa.recordId
	SET aa.differenceAmount = bb.differenceAmount
	</update>

    <select id="getCardaMaterialOccupyMatId" resultType="String">
        SELECT
            aa.materialId
        FROM
        (
            SELECT
                a.materialId AS "materialId",
                SUM(IFNULL(a.quantity,0)) AS "quantity"
            FROM eg_carda_material_occupy a
            LEFT JOIN pd_produce_batch_detail b ON b.recordId = a.produceBatchDetailId
            LEFT JOIN pd_produce_batch c ON c.recordId = b.produceBatchId
            LEFT JOIN md_material d ON d.recordId = a.materialId
            WHERE a.activeFlag = 1 AND c.feedingNo = #{recordId}
            GROUP BY a.materialId
        ) aa
        ORDER BY aa.quantity DESC
    </select>

	<select id="getQuantity" resultType="BigDecimal">
		SELECT
			SUM(b.quantity)
		FROM
			md_material a
				LEFT JOIN st_material_store b ON b.materialId = a.recordId
				AND b.activeFlag = 1
		WHERE
			a.recordId = #{materialId} AND a.companyId = #{companyId} AND a.activeFlag = 1
		  AND DATE(b.operateDate) = CURDATE();
	</select>

	<select id="getlistByFeedId" resultType="RawmaterialStock">
		SELECT
			<include refid="MaterialStoreColumns"/>
		FROM st_material_store a
		<include refid="MaterialStoreJoins" />
		WHERE FIND_IN_SET(a.feedingId,#{feedId}) AND a.activeFlag = 1 AND a.`status` = ******** AND a.inOutType = 3
	</select>
	
	<select id="getPcsAmount" resultType="RawmaterialStock">
		SELECT
			feedingId,
			ROUND(SUM(cost) / SUM(b.qtyPcsT),2) AS "pcsAmount"
		FROM st_material_store a
		LEFT JOIN pd_produce_batch_detail b ON b.recordId = a.productBatchDeailId
		WHERE a.companyId = #{companyId} AND a.activeFlag = 1 AND a.inOutType = 3 AND a.`status` = ********
		AND a.cost > 0 AND b.qtyPcsT > 0
		GROUP BY a.feedingId
	</select>

	<select id="getRawmaterialStockListT" resultType="RawmaterialStock">
		SELECT
			IFNULL(a.purchasingDtlId,b.purchasingDetailId)AS "purchasingDetaillId",
			a.inOutType AS "inOutType",
			a.quantity AS "quantity",
			a.materialId AS "materialId",
			a.createdDate AS "operateDate",
			a.giveNum
		FROM st_material_store a
		LEFT JOIN pu_returns_detail b ON b.recordId = a.returnsNo
		WHERE a.activeFlag = 1 AND a.status = ******** AND a.createdDate >= #{date}
		AND a.createdDate IS NOT NULL
		AND (
		a.materialId
		) IN
		<foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>

	<insert id="insetFirstInFirstOut" keyProperty="recordId" useGeneratedKeys="true">
		INSERT INTO sl_rawMaterial_inventory_first_in_first_out (
			companyId,
			purchasingInId,
			outboundQuantity,
			rawMaterialId,
			activeFlag,
			createdDate
		)VALUES(
			#{companyId},
			#{purchasingInId},
			#{outboundQuantity},
			#{rawMaterialId},
			1,
			NOW()
		)
	</insert>

	<select id="getFisrtInFirstOutList" resultType="RawmaterialStock">
		SELECT
			b.price,
			a.outboundQuantity,
			ROUND(IFNULL(a.outboundQuantity,0)*IFNULL(b.price,0),2) AS "firstInFirstAmount",
			f.shortName AS "shortName",
			c.`name` AS "materialName",
			c.specification AS "specification",
			e.`no` AS "purchasingNo"
		FROM
			sl_rawMaterial_inventory_first_in_first_out a
			LEFT JOIN st_material_store b ON b.recordId = a.purchasingInId
			LEFT JOIN md_material c ON c.recordId = b.materialId
			LEFT JOIN pu_purchasing_detail d ON d.recordId = b.purchasingDtlId
			LEFT JOIN pu_purchasing e ON e.recordId = d.purchasingId
			LEFT JOIN md_supplier f ON f.recordId = e.supplierId
		WHERE
			a.activeFlag = 1
			AND a.rawMaterialId = #{recordId};
	</select>

	<select id="getkybFistInOutList" resultType="RawmaterialStock">
		SELECT
			SUM(IFNULL(prd.outboundQuantity,0)) AS "outboundQuantityTotal",
			prd.purchasingDtlId AS "purchasingDtlId",
			prd.materialId AS "materialId",
			IFNULL(prd.surplusStock,0) AS "surplusStock",
			ROUND(SUM(prd.inQuantity)+SUM(IFNULL(prd.qty,0)),0) AS "inQuantityTotal",
			prd.materialNo AS "materialNo",
			prd.materialName AS "materialName",
			prd.purchasingNo AS "purchasingNo",
			prd.createdDate AS "createdDate",
			prd.quantity AS "quantity"
		FROM(

				SELECT
					SUM(IFNULL( a.outboundQuantity, 0 )) AS "outboundQuantity",
					IFNULL( b.quantity, 0 )+ IFNULL( b.giveNum, 0 ) AS "inQuantity",
					b.purchasingDtlId AS "purchasingDtlId",
					b.materialId AS "materialId",
					c.surplusStock AS "surplusStock",
					(
						SELECT
							SUM(CASE aa.inOutType WHEN 4 THEN -aa.quantity WHEN 7 THEN aa.quantity ELSE 0 END)
						FROM st_material_store aa
						LEFT JOIN pu_returns_detail bb ON bb.recordId = aa.returnsNo
						WHERE aa.activeFlag = 1 AND aa.`status` = ******** AND aa.inOutType IN(4,7)
						AND bb.purchasingDetailId = c.recordId AND bb.purchasingType = 1 AND bb.activeFlag = 1
					) AS "qty",
					d.`no` AS "materialNo",
					d.`name` AS "materialName",
					e.`no` AS "purchasingNo",
					c.createdDate AS "createdDate",
					c.quantity AS "quantity"
				FROM sl_rawMaterial_inventory_first_in_first_out a
				LEFT JOIN st_material_store b ON b.recordId = a.purchasingInId
				LEFT JOIN pu_purchasing_detail c ON c.recordId = b.purchasingDtlId
				LEFT JOIN md_material d ON d.recordId = c.materialId
				LEFT JOIN pu_purchasing e ON e.recordId = c.purchasingId
				WHERE a.companyId = #{companyId} AND a.activeFlag = 1  AND b.inOutType = 1 AND b.`status` = ********
				AND c.activeFlag = 1
				GROUP BY
					b.recordId
		) prd GROUP BY prd.purchasingDtlId
	</select>

	<select id="getkybFistInOutListTwo" resultType="RawmaterialStock">
		SELECT
			a.outboundQuantity,
			c.inoutType AS "inoutType",
			a.createdDate AS "createdDate",
			b.*
		FROM
			`sl_rawMaterial_inventory_first_in_first_out` a
			LEFT JOIN st_material_store b ON b.recordId = a.purchasingInId
			LEFT JOIN st_material_store c ON c.recordId = a.rawMaterialId
			WHERE a.activeFlag = 1 AND a.companyId = #{companyId}
			AND b.purchasingDtlId = #{recordId}
	</select>

	<update id="updateFirstInFistOut">
		UPDATE sl_rawMaterial_inventory_first_in_first_out SET activeFlag = 0 WHERE rawMaterialId = #{writeOffId};
	</update>

	<select id="getDetailList" resultType="RawmaterialStock">
		SELECT
			g.recordId AS "contractDetailId",
			a.*
		FROM st_material_store a
		LEFT JOIN pd_feeding b ON b.recordId = a.feedingId
		LEFT JOIN sl_notification c ON c.recordId = b.notificationId
		LEFT JOIN sl_notification d ON d.mergeId = c.recordId AND d.companyId = c.companyId AND d.activeFlag = 1
		LEFT JOIN sl_contract_detail e ON e.recordId = IFNULL(d.contractDetailId,c.contractDetailId)
		LEFT JOIN icloud_group_center f ON f.recordId = e.groupCenterId
		LEFT JOIN sl_contract_detail g ON g.recordId = f.contactDeailId
		WHERE  a.activeFlag = 1 AND a.inoutType = 3 AND a.quantity > 0
		<if test="companyId != null and companyId != ''">
			AND a.companyId = #{companyId}
		</if>
		AND a.operateDate >= #{sentTimeStartQr}
		AND a.operateDate <![CDATA[<=]]> #{sentTimeEndQr}
		GROUP BY a.recordId
	</select>

	<select id="getDeliveryStorageList" resultType="RawmaterialStock">
		SELECT
			a.*
		FROM st_material_store a
		WHERE a.companyId = #{companyId} AND a.activeFlag = 1 AND a.operateDate >= #{startDate}
		AND a.inOutType NOT IN (1,4,7) AND a.status = ********
		ORDER BY a.operateDate ASC,a.recordId ASC
	</select>

	<select id="getWarehousingList" resultType="RawmaterialStock">
		SELECT
			a.*
		FROM st_material_store a
		WHERE a.activeFlag = 1 AND a.inOutType = 1 AND a.status = ********
		AND a.companyId = #{companyId}
		<if test="type != null and type != ''">
			AND a.surplusStock > 0
			ORDER BY a.operateDate ASC
		</if>
		<if test="type == null or type == ''">
			ORDER BY a.operateDate DESC
		</if>
	</select>

	<update id="batchUpdateSurplusStock">
		<foreach collection="list" item="item" separator=";">
			UPDATE st_material_store SET
				surplusStock = #{item.surplusStock}
			WHERE recordId = #{item.recordId}
		</foreach>
	</update>

	<update id="cleanSurplusStock">
		UPDATE st_material_store SET
			surplusStock = 0
		WHERE activeFlag = 1 AND inOutType = 1
	</update>


	<insert id="batchInsetFirstInFirstOut">
		INSERT INTO sl_rawMaterial_inventory_first_in_first_out (
			companyId,
			purchasingInId,
			outboundQuantity,
			rawMaterialId,
			activeFlag,
			createdDate
		)VALUES
		<foreach collection="list" item="item" separator=",">
			(
				#{item.companyId},
				#{item.purchasingInId},
				#{item.outboundQuantity},
				#{item.rawMaterialId},
				1,
				NOW()
			)
		</foreach>
	</insert>

	<update id="cleanFirstInFirstOut">
		UPDATE sl_rawMaterial_inventory_first_in_first_out SET
			activeFlag = 2
		WHERE activeFlag = 1
	</update>


	<select id="getWarehousingListTwo" resultType="RawmaterialStock">
		SELECT
			a.recordId,
			IFNULL(a.quantity,0) +
			(
			SELECT
			SUM(IFNULL(CASE aa.inOutType WHEN 4 THEN -aa.quantity ELSE aa.quantity END ,0))
			FROM st_material_store aa
			LEFT JOIN pu_returns_detail bb ON bb.recordId = aa.returnsNo
			WHERE aa.companyId = a.companyId AND aa.activeFlag = 1 AND aa.inOutType IN(4,7)
			AND bb.purchasingType = 1 AND bb.purchasingDetailId = a.purchasingDtlId
			) AS "quantity",
			a.operateDate,
			a.surplusStock
		FROM st_material_store a
		WHERE a.companyId = #{companyId} AND a.activeFlag = 1 AND a.inOutType = 1 AND a.status = ********
		<if test="materialId != null and materialId != ''">
			AND a.materialId = #{materialId} AND a.surplusStock > 0
		</if>
		<if test="purchasingDtlId != null and purchasingDtlId != ''">
			AND a.purchasingDtlId = #{purchasingDtlId}
		</if>
		ORDER BY a.operateDate ASC
	</select>

	<update id="batchUpdateSurplusStockTwo">
		UPDATE st_material_store aa
		INNER JOIN
		(
			SELECT
				purchasingInId AS "recordId",
				outboundQuantity AS "outboundQuantity"
			FROM sl_rawMaterial_inventory_first_in_first_out
			WHERE activeFlag = 1 AND rawMaterialId = #{writeOffId}
		) bb ON bb.recordId = aa.recordId
		SET aa.surplusStock = IFNULL(aa.surplusStock,0) + bb.outboundQuantity
	</update>

	<select id="getWeightedAverageDetailList" resultType="RawmaterialStock">
		SELECT
			a.companyId,
			CASE WHEN a.inOutType = 3 THEN ROUND(IFNULL(a.quantity, 0) * IFNULL(a.price,0),2) ELSE 0 END AS " feedingFirstInOutAmountTotal",
			CASE WHEN a.inOutType = 6 AND (a.remark NOT LIKE '%盘亏%' OR a.remark IS NULL) THEN ROUND(IFNULL(a.quantity, 0) * IFNULL(a.price,0),2) ELSE 0 END AS "materialFirstInOutAmountTotal",
			CASE WHEN a.inOutType = 2 AND a.remark LIKE '%盘盈%' THEN
			(
			SELECT
			IFNULL(price,0)
			FROM md_material_price_init aa
			WHERE aa.materialId = 1 AND aa.initDate <![CDATA[<=]]> a.operateDate
			ORDER BY aa.initDate DESC LIMIT 1
			) * a.quantity
			ELSE 0
			END AS "inventoryFirstInOutAmountTotal",
			CASE WHEN a.inOutType = 6 AND a.remark LIKE '%盘亏%' THEN
			(
			SELECT
			IFNULL(price,0)
			FROM md_material_price_init aa
			WHERE aa.materialId = 1 AND aa.initDate <![CDATA[<=]]> a.operateDate
			ORDER BY aa.initDate DESC LIMIT 1
			) * a.quantity
			ELSE 0
			END AS "inventoryLossFirstInOutAmountTotal",
			a.operateDate AS "createdDate",
			b.materialType AS "materialTypeId",
			c.`value` AS "materialTypeName",
			a.quantity,
			a.price,
			d.no AS "material.no",
			d.name AS "material.name",
			d.specification AS "material.specification"
		FROM st_material_store a
		LEFT JOIN md_material b ON b.recordId = a.materialId
		LEFT JOIN md_dict_value c ON c.recordId = b.materialType
		LEFT JOIN md_material d ON d.recordId = a.materialId
		WHERE a.activeFlag = 1
		AND a.`status` = ********
		AND a.inOutType IN(2,3,6)
		AND CASE a.inOutType WHEN 2 THEN a.remark LIKE '%盘盈%' ELSE 1=1 END
		<if test="companyId != null and companyId != ''">
			AND a.companyId = #{companyId}
		</if>
		<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
			AND DATE_FORMAT(a.createdDate, '%Y-%m-%d' ) >= DATE_FORMAT(#{sentTimeStartQr},'%Y-%m-%d')
		</if>
		<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
			AND DATE_FORMAT(a.createdDate, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT(#{sentTimeEndQr},'%Y-%m-%d')
		</if>
	</select>

	<select id="getUseCostFeeFirstInFirstOutList" resultType="RawmaterialStock">
		SELECT
			a.companyId,
			e.outboundQuantity AS "quantity",
			e.price AS "price",
			CASE WHEN a.inOutType = 3 THEN e.amount ELSE 0 END AS "feedingFirstInOutAmountTotal",
			CASE WHEN a.inOutType = 6 AND (a.remark NOT LIKE '%盘亏%' OR a.remark IS NULL) THEN e.amount ELSE 0 END AS "materialFirstInOutAmountTotal",
			CASE WHEN a.inOutType = 2 AND a.remark LIKE '%盘盈%' THEN e.amount ELSE 0 END AS "inventoryFirstInOutAmountTotal",
			CASE WHEN a.inOutType = 6 AND a.remark LIKE '%盘亏%' THEN e.amount ELSE 0 END AS "inventoryLossFirstInOutAmountTotal",
			a.operateDate AS "createdDate",
			b.materialType AS "materialTypeId",
			c.`value` AS "materialTypeName",
			d.no AS "material.no",
			d.name AS "material.name",
			d.specification AS "material.specification"
		FROM st_material_store a
	    LEFT JOIN md_material b ON b.recordId = a.materialId
	    LEFT JOIN md_dict_value c ON c.recordId = b.materialType
	    LEFT JOIN md_material d ON d.recordId = a.materialId
		LEFT JOIN
		(
			SELECT
				ROUND(SUM(aa.outboundQuantity * bb.price),2) AS "amount",
				SUM(aa.outboundQuantity) AS "outboundQuantity",
				SUM(bb.price) AS "price",
			aa.rawMaterialId AS "rawMaterialId"
			FROM sl_rawMaterial_inventory_first_in_first_out aa
			LEFT JOIN st_material_store bb ON bb.recordId = aa.purchasingInId
			WHERE aa.activeFlag = 1
			GROUP BY aa.rawMaterialId
		) e ON e.rawMaterialId = a.recordId
		WHERE a.activeFlag = 1
	    AND a.`status` = ********
	    AND a.inOutType IN(2,3,6)
	    AND CASE a.inOutType WHEN 2 THEN a.remark LIKE '%盘盈%' ELSE 1=1 END
		<if test="companyId != null and companyId != ''">
			AND a.companyId = #{companyId}
		</if>
		<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
			AND DATE_FORMAT(a.createdDate, '%Y-%m-%d' ) >= DATE_FORMAT(#{sentTimeStartQr},'%Y-%m-%d')
		</if>
		<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
			AND DATE_FORMAT(a.createdDate, '%Y-%m-%d' ) <![CDATA[<=]]> DATE_FORMAT(#{sentTimeEndQr},'%Y-%m-%d')
		</if>
		<if test="concreteType != null and concreteType != ''">
			AND b.materialType = #{concreteType}
		</if>
	</select>

	<select id="getFisrtInFirstOutListTwo" resultType="RawmaterialStock">
		SELECT
			a.recordId,
			a.rawMaterialId,
			b.price,
			a.outboundQuantity,
			ROUND(IFNULL(a.outboundQuantity,0)*IFNULL(b.price,0),2) AS "firstInFirstAmount",
			f.shortName AS "shortName",
			c.`name` AS "materialName",
			c.specification AS "specification",
			e.`no` AS "purchasingNo"
		FROM sl_rawMaterial_inventory_first_in_first_out a
		LEFT JOIN st_material_store b ON b.recordId = a.purchasingInId
		LEFT JOIN md_material c ON c.recordId = b.materialId
		LEFT JOIN pu_purchasing_detail d ON d.recordId = b.purchasingDtlId
		LEFT JOIN pu_purchasing e ON e.recordId = d.purchasingId
		LEFT JOIN md_supplier f ON f.recordId = e.supplierId
		LEFT JOIN st_material_store g ON g.recordId = a.rawMaterialId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1
		<if test="inoutTimeStartQr != null and inoutTimeStartQr != ''">
			AND g.operateDate >= #{inoutTimeStartQr}
		</if>
		<if test="inoutTimeEndQr != null and inoutTimeEndQr != ''">
			AND g.operateDate <![CDATA[<=]]> #{inoutTimeEndQr}
		</if>
        ORDER BY g.operateDate DESC
	</select>

	<select id="getWarehousingSumList" resultType="RawmaterialStock">
		SELECT
			SUM(
					CASE
						a.inOutType
						WHEN 1 THEN
							IFNULL(a.quantity, 0)
						WHEN 4 THEN
							IFNULL(- a.quantity, 0)
						WHEN 7 THEN
							IFNULL(a.quantity, 0) ELSE 0
						END
				) AS "quantity",
		       ppd.recordId AS "purchasingDetailId"
		FROM st_material_store a
		LEFT JOIN pu_purchasing_detail ppd ON ppd.recordId = a.purchasingDtlId
		WHERE FIND_IN_SET(ppd.recordId, #{recordId}) AND a.activeFlag = 1 AND a.companyId = #{companyId}
		AND a.`status` in (********,99999903,99999902)
		GROUP BY a.recordId
	</select>

	<select id="getDetailListTwo" resultType="RawmaterialStock">
		SELECT
			b.no AS "feedNo",
			c.no AS "notifiNo",
			h.no AS "materialNo",
			h.name AS "materialName",
			h.specification AS "specification",
			i.userName AS "shortName",
			a.*
		FROM st_material_store a
		LEFT JOIN pd_feeding b ON b.recordId = a.feedingId
		LEFT JOIN sl_notification c ON c.recordId = b.notificationId
		LEFT JOIN sl_notification d ON d.mergeId = c.recordId AND d.companyId = c.companyId AND d.activeFlag = 1
		LEFT JOIN sl_contract_detail e ON e.recordId = IFNULL(d.contractDetailId,c.contractDetailId)
		LEFT JOIN icloud_group_center f ON f.recordId = e.groupCenterId
		LEFT JOIN sl_contract_detail g ON g.recordId = f.contactDeailId
		LEFT JOIN md_material h ON h.recordId = a.materialId
		LEFT JOIN sm_user i ON i.recordId = a.createdBy
		WHERE  a.activeFlag = 1 AND a.inoutType = 3 AND a.quantity > 0
		<if test="companyId != null and companyId != ''">
			AND a.companyId = #{companyId}
		</if>
		AND a.operateDate >= #{sentTimeStartQr}
		AND a.operateDate <![CDATA[<=]]> #{sentTimeEndQr}
		GROUP BY a.recordId
	</select>

	<select id="getPurchasingDetailData" resultType="RawmaterialStock">
		SELECT
			c.`no` AS "purchasingNo",
			d.`no` AS "materialNo",
			d.`name` AS "materialName",
			d.specification AS "specification",
			e.userName AS "shortName",
			ROUND(IFNULL(ee.value,0) * IFNULL(ff.value,0) * a.quantity / 1000000,4) AS "exportArea",
			a.*
		FROM st_material_store a
		LEFT JOIN pu_purchasing_detail b ON b.recordId = a.purchasingDtlId
		LEFT JOIN pu_purchasing c ON c.recordId = b.purchasingId
		LEFT JOIN md_material d ON d.recordId = a.materialId
		LEFT JOIN sm_user e ON e.recordId = a.createdBy
		LEFT JOIN md_material_specification_relation ee ON ee.materialId = b.recordId AND ee.companyId = a.companyId
		AND ee.specificationId = (SELECT recordId FROM md_material_specification WHERE companyId = a.companyId AND `name` = '长' AND activeFlag = 1 LIMIT 1)
		AND ee.activeFlag = 1
		LEFT JOIN md_material_specification_relation ff ON ff.materialId = b.recordId AND ff.companyId = a.companyId
		AND ff.specificationId = (SELECT recordId FROM md_material_specification WHERE companyId = a.companyId AND `name` = '宽' AND activeFlag = 1 LIMIT 1)
		AND ff.activeFlag = 1
		WHERE a.recordId = #{recordId} AND a.activeFlag = 1
	</select>

	<select id="getFeedOutList" resultType="RawmaterialStock">
		SELECT
			a.*,
			b.`no` AS "materialNo",
			b.`name` AS "materialName",
			b.specification AS "specification",
			c.userName AS "operator",
			ROUND(IFNULL(ee.value,0) * IFNULL(ff.value,0) * a.quantity / 1000000,4) AS "exportArea"
		FROM st_material_store a
		LEFT JOIN md_material b ON b.recordId = a.materialId
		LEFT JOIN sm_user c ON c.recordId = a.createdBy
		LEFT JOIN md_material_specification_relation ee ON ee.materialId = b.recordId AND ee.companyId = a.companyId
		AND ee.specificationId = (SELECT recordId FROM md_material_specification WHERE companyId = a.companyId AND `name` = '长' AND activeFlag = 1 LIMIT 1)
		AND ee.activeFlag = 1
		LEFT JOIN md_material_specification_relation ff ON ff.materialId = b.recordId AND ff.companyId = a.companyId
		AND ff.specificationId = (SELECT recordId FROM md_material_specification WHERE companyId = a.companyId AND `name` = '宽' AND activeFlag = 1 LIMIT 1)
		AND ff.activeFlag = 1
		WHERE a.feedingId = #{recordId} AND a.activeFlag = 1 AND a.`status` = ********
	</select>

	<select id="getSourceStoreId" resultType="String">
		SELECT recordId FROM st_material_store WHERE purchasingDtlId = #{purchasingDtlId} AND activeFlag = 1 AND `status` = ******** AND inOutType = 1
	</select>

	<select id="getFirstFirstOutList" resultType="RawmaterialStock">
		SELECT
			a.recordId,
			a.price,
			a.cost,
			a.quantity,
			a.operatedate,
			a.materialId,
			IFNULL(b.recordId,d.recordId) AS "material.recordId",
			IFNULL(b.NO,CONCAT('YL', d.recordId)) AS "material.no",
			IFNULL(b.NAME,d.NAME) AS "material.name",
			IFNULL(b.specification,d.specifications) AS "material.specification",
			c.value AS "material.materialTypeValue",
			a.inOutType
		FROM st_material_store a
		LEFT JOIN md_material b ON b.recordId = a.materialId AND (a.product IS NULL OR a.product = 1)
		LEFT JOIN md_dict_value c ON c.recordId = b.materialType
		LEFT JOIN inter_config_supplier d ON d.recordId = a.materialId AND a.product = 2
		LEFT JOIN md_material_childcompany mmc ON mmc.recordId = a.stockPlaceComId
		LEFT JOIN md_material_place mmp ON mmp.recordId = mmc.materPlaceId
		LEFT JOIN md_store_house msh ON msh.recordId = a.storeHouseId AND msh.companyId = a.companyId AND msh.activeFlag = 1
		WHERE
			a.companyId = #{company.recordId}
			AND a.activeFlag = 1
			AND a. STATUS = ********
		AND (a.remark NOT LIKE '%盘亏%' OR a.remark NOT LIKE '%盘盈%' OR a.remark IS NULL)
		AND a.price > 0
		<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
			AND a.operateDate >= #{sentTimeStartQr}
		</if>
		<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
			AND a.operateDate <![CDATA[<=]]> #{sentTimeEndQr}
		</if>
		<if test="materialNo != null and materialNo != ''">
			AND REPLACE(b.no," ","") like CONCAT('%', REPLACE(#{materialNo}," ",""), '%')
		</if>
		<if test="materialName != null and materialName != ''">
			AND REPLACE(b.name," ","") like CONCAT('%', REPLACE(#{materialName}," ",""), '%')
		</if>
		<if test="materialTypeId != null and materialTypeId != ''">
			AND b.materialType = #{materialTypeId}
		</if>
		<if test="stockPlaceId != null and stockPlaceId != ''">
			AND mmp.recordId = #{stockPlaceId}
		</if>
		<if test="storeHouseId != null and storeHouseId !=''">
			AND FIND_IN_SET(a.storeHouseId,#{storeHouseId})
		</if>
		ORDER BY b.recordId
	</select>

	<select id="getDateList" resultType="RawmaterialStock">
		SELECT
			aa.*
		FROM
		(
			SELECT
				CASE WHEN YEAR(DATE(#{sentTimeStartQr})) = YEAR(DATE(#{sentTimeEndQr})) THEN DATE_FORMAT(DATE(@startTime),'%m-%d')
				ELSE DATE(@startTime)
				END AS "date"
				,@startTime AS "startTime"
				,@startTime := DATE_FORMAT(DATE_ADD(@startTime, INTERVAL 1 DAY), '%Y-%m-%d') AS "endTime"
			FROM mysql.help_topic,
			(SELECT @startTime := #{sentTimeStartQr}) a
			WHERE @startTime <![CDATA[<]]> #{sentTimeEndQr}
		) aa
		WHERE 1=1 GROUP BY aa.date
	</select>

	<select id="getFisrtInFirstOutListByDate" resultType="RawmaterialStock">
		SELECT
			b.price AS "price",
			b.recordId,
			b.operateDate,
			a.outboundQuantity,
			ROUND(IFNULL(a.outboundQuantity, 0) * IFNULL(b.price, 0), 2) AS "firstInFirstAmount",
			e.`no` AS "purchasingNo",
			f.shortName AS "supplierName"
		FROM sl_rawMaterial_inventory_first_in_first_out a
		LEFT JOIN st_material_store b ON b.recordId = a.rawMaterialId
		LEFT JOIN st_material_store c ON c.recordId = a.purchasingInId
		LEFT JOIN pu_purchasing_detail d ON d.recordId = c.purchasingDtlId
		LEFT JOIN pu_purchasing e ON e.recordId = d.purchasingId
		LEFT JOIN md_supplier f ON f.recordId = e.supplierId
		WHERE a.companyId = #{company.recordId} AND a.activeFlag = 1
		<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
			AND b.operateDate >= #{sentTimeStartQr}
		</if>
		<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
			AND b.operateDate <![CDATA[<=]]> #{sentTimeEndQr}
		</if>
		ORDER BY a.createdDate DESC
	</select>

	<select id="getFirstFirstOutListTwo" resultType="RawmaterialStock">
		SELECT
		a.recordId,
		a.price,
		a.cost,
		a.quantity,
		a.operatedate,
		a.materialId,
		IFNULL(b.recordId,d.recordId) AS "material.recordId",
		IFNULL(b.NO,CONCAT('YL', d.recordId)) AS "material.no",
		IFNULL(b.NAME,d.NAME) AS "material.name",
		IFNULL(b.specification,d.specifications) AS "material.specification",
		c.value AS "material.materialTypeValue",
		a.inOutType,
		(
		SELECT
		SUM(price)
		FROM
		md_material_price_init
		WHERE
		companyId = a.companyId
		AND materialId = a.materialId
		AND DATE(initDate) = DATE(a.operatedate) + INTERVAL 1 DAY) AS "initPrice",
		(
		SELECT
		SUM(periodStocks)
		FROM
		md_material_stock_init
		WHERE
		materialId = a.materialId
		AND DATE(initDate) = DATE(a.operatedate) + INTERVAL 1 DAY
		ORDER BY
		initDate DESC
		LIMIT 1) AS "initStocks"
		FROM st_material_store a
		LEFT JOIN md_material b ON b.recordId = a.materialId AND (a.product IS NULL OR a.product = 1)
		LEFT JOIN md_dict_value c ON c.recordId = b.materialType
		LEFT JOIN inter_config_supplier d ON d.recordId = a.materialId AND a.product = 2
		WHERE
		a.companyId = #{company.recordId}
		AND a.activeFlag = 1
		AND a. STATUS = ********
		AND (a.remark NOT LIKE '%盘亏%' OR a.remark NOT LIKE '%盘盈%' OR a.remark IS NULL)
		AND a.price > 0
		<if test="sentTimeStartQr != null and sentTimeStartQr != ''">
			AND a.operateDate >= #{sentTimeStartQr}
		</if>
		<if test="sentTimeEndQr != null and sentTimeEndQr != ''">
			AND a.operateDate <![CDATA[<=]]> #{sentTimeEndQr}
		</if>
		<if test="materialNo != null and materialNo != ''">
			AND REPLACE(b.no," ","") like CONCAT('%', REPLACE(#{materialNo}," ",""), '%')
		</if>
		ORDER BY b.recordId
	</select>
	
	<update id="updateStatus">
		UPDATE st_material_store SET
			activeFlag = 0
		WHERE sourceId = #{rawmaterialStock.recordId} AND purchasingDtlId = #{purchasingDtlId}
	</update>

	<update id="updateStatusTwo">
		UPDATE st_material_store SET
			activeFlag = 0
		WHERE deliveryDetailId = #{rawStockOutId} AND companyId = #{company.recordId}
	</update>

</mapper>