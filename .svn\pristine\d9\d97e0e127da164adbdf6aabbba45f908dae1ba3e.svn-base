package com.kyb.pcberp.common.utils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.kyb.pcberp.common.material.MaterialTimeUtils;
import com.kyb.pcberp.modules.approval.entity.Approval;
import com.kyb.pcberp.modules.approval.utils.ModifyUtils;
import com.kyb.pcberp.modules.contract.dao.*;
import com.kyb.pcberp.modules.contract.entity.*;
import com.kyb.pcberp.modules.crm.dao.*;
import com.kyb.pcberp.modules.crm.entity.*;
import com.kyb.pcberp.modules.finance.dao.CollectMoneyDao;
import com.kyb.pcberp.modules.finance.entity.CollectMoney;
import com.kyb.pcberp.modules.finance.entity.CollectMuchMoney;
import com.kyb.pcberp.modules.production.dao.BottleneckProcessUseDao;
import com.kyb.pcberp.modules.production.dao.ProduceBatchDetailDao;
import com.kyb.pcberp.modules.production.entity.ProduceBatchDetail;
import com.kyb.pcberp.modules.production.service.BatchDetailSetupService;
import com.kyb.pcberp.modules.purch.dao.*;
import com.kyb.pcberp.modules.purch.entity.Purchasing;
import com.kyb.pcberp.modules.report.dao.GroupOrgCostDao;
import com.kyb.pcberp.modules.report.dao.OverdueDeductionsDao;
import com.kyb.pcberp.modules.report.dao.RoyaltyRateDao;
import com.kyb.pcberp.modules.report.entity.*;
import com.kyb.pcberp.modules.report.service.ReportService;
import com.kyb.pcberp.modules.stock.dao.*;
import com.kyb.pcberp.modules.stock.entity.*;
import com.kyb.pcberp.modules.sys.dao.DepartmentDao;
import com.kyb.pcberp.modules.sys.dao.ParameterSetDao;
import com.kyb.pcberp.modules.sys.dao.UserDao;
import com.kyb.pcberp.modules.sys.entity.*;
import com.kyb.pcberp.modules.sys.utils.CockpitUtilsTwo;
import org.apache.poi.hssf.record.crypto.Biff8DecryptingStream;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.modules.contract.utils.ChangeDataUtils;
import com.kyb.pcberp.modules.eg.entity.CardA;
import com.kyb.pcberp.modules.finance.service.ReconciliationService;
import com.kyb.pcberp.modules.purch.entity.MaterialCheck;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import com.kyb.pcberp.modules.purch.vo.ProDetailInVo;
import com.kyb.pcberp.modules.stock.service.OutBoundService;
import com.kyb.pcberp.modules.stock.service.RawInoutService;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class InitDataUtils
{
    public static InitDataUtils getInstance()
    {
        return new InitDataUtils();
    }

    private InitDataDao initDataDao = SpringContextHolder.getBean(InitDataDao.class);

    private ContractDetailDao contractDetailDao = SpringContextHolder.getBean(ContractDetailDao.class);

    private CompanyDao companyDao = SpringContextHolder.getBean(CompanyDao.class);

    private ErpCraftDao erpCraftDao = SpringContextHolder.getBean(ErpCraftDao.class);

    private MaterialCheckDao materialCheckDao = SpringContextHolder.getBean(MaterialCheckDao.class);

    private RejectApplicationDao rejectApplicationDao = SpringContextHolder.getBean(RejectApplicationDao.class);

    private AccountsPayableDao accountsPayableDao = SpringContextHolder.getBean(AccountsPayableDao.class);

    private UpdateDataDao updateDataDao = SpringContextHolder.getBean(UpdateDataDao.class);

    private RawmaterialStockDao rawmaterialStockDao = SpringContextHolder.getBean(RawmaterialStockDao.class);

    private PurchasingDetailDao purchasingDetailDao = SpringContextHolder.getBean(PurchasingDetailDao.class);

    private RawInoutService rawInoutService = SpringContextHolder.getBean(RawInoutService.class);

    private ReconciliationService reconciliationService = SpringContextHolder.getBean(ReconciliationService.class);

    // private PuReturnsDetailDao returnDetailDao = SpringContextHolder.getBean(PuReturnsDetailDao.class);

    private GoodsCheckDao goodsCheckDao = SpringContextHolder.getBean(GoodsCheckDao.class);

    private AccountsReceivableDao accountsReceivableDao = SpringContextHolder.getBean(AccountsReceivableDao.class);

    private CustomerDao customerDao = SpringContextHolder.getBean(CustomerDao.class);

    private PrdorderDetailDao prdorderDetailDao = SpringContextHolder.getBean(PrdorderDetailDao.class);

    private DeliveryDetailDao deliveryDetailDao = SpringContextHolder.getBean(DeliveryDetailDao.class);

    private DeliveryDao deliveryDao = SpringContextHolder.getBean(DeliveryDao.class);

    private ProductStoreDao productStoreDao = SpringContextHolder.getBean(ProductStoreDao.class);

    private OutBoundDao outBoundDao = SpringContextHolder.getBean(OutBoundDao.class);

    private MaterialDao materialDao = SpringContextHolder.getBean(MaterialDao.class);

    private OutBoundService outBoundService = SpringContextHolder.getBean(OutBoundService.class);

    private ContractCraftDao contractCraftDao = SpringContextHolder.getBean(ContractCraftDao.class);

    private StoreHouseDao storeHouseDao = SpringContextHolder.getBean(StoreHouseDao.class);

    private PurchasingDao purchasingDao = SpringContextHolder.getBean(PurchasingDao.class);

    private MaterialAreaDetailDao materialAreaDetailDao = SpringContextHolder.getBean(MaterialAreaDetailDao.class);

    private MatPreparationDao matPreparationDao = SpringContextHolder.getBean(MatPreparationDao.class);

    private ProduceBatchDetailDao produceBatchDetailDao = SpringContextHolder.getBean(ProduceBatchDetailDao.class);

    private ParameterSetDao parameterSetDao = SpringContextHolder.getBean(ParameterSetDao.class);

    private BatchDetailSetupService batchDetailSetupService = SpringContextHolder.getBean(BatchDetailSetupService.class);

    private ContractDao contractDao = SpringContextHolder.getBean(ContractDao.class);

    private UserDao userDao = SpringContextHolder.getBean(UserDao.class);

    private BottleneckProcessUseDao bottleneckProcessUseDao = SpringContextHolder.getBean(BottleneckProcessUseDao.class);

    private MantissaStoreDao mantissaStoreDao = SpringContextHolder.getBean(MantissaStoreDao.class);

    private RoyaltyRateDao royaltyRateDao = SpringContextHolder.getBean(RoyaltyRateDao.class);

    private CollectMoneyDao collectMoneyDao = SpringContextHolder.getBean(CollectMoneyDao.class);

    private DepartmentDao departmentDao = SpringContextHolder.getBean(DepartmentDao.class);

    private CustomerSalesmanDao customerSalesmanDao = SpringContextHolder.getBean(CustomerSalesmanDao.class);

    private ReportService reportService = SpringContextHolder.getBean(ReportService.class);

    private OverdueDeductionsDao overdueDeductionsDao = SpringContextHolder.getBean(OverdueDeductionsDao.class);

    private GroupOrgCostDao groupOrgCostDao = SpringContextHolder.getBean(GroupOrgCostDao.class);

    // 重置龙南公司缺失数据
    @Transactional(readOnly = false)
    public void initLnOrderData()
    {
        CompanyUtil companyUtil = CompanyUtil.getInstance();
        String factId = companyUtil.getFactId();
        String ecoemyId = companyUtil.getEcoemyId();

        // 获取集控id集合
        ContractDetail detail = new ContractDetail();
        detail.setFactId(factId);
        detail.setEcoemyId(ecoemyId);
        List<ContractDetail> list = initDataDao.getGroupCenterDatas(detail);
        if (Collections3.isEmpty(list))
        {
            return;
        }
        String detailIds = null;
        for (ContractDetail cd : list)
        {
            if (StringUtils.isNotBlank(detailIds))
            {
                detailIds = detailIds + "," + cd.getRecordId();
            }
            else
            {
                detailIds = cd.getRecordId();
            }
        }
        ContractDetail query = new ContractDetail(detailIds);
        List<ContractDetail> detailList = initDataDao.getDetailDataList(query);

        for (ContractDetail cd : list)
        {
            for (ContractDetail data : detailList)
            {
                if (cd.getRecordId().equals(data.getRecordId()))
                {
                    User user = data.getCreatedBy();
                    Company company = companyDao.get(new Company(cd.getCompanyId()));

                    ContractDetail cdQuery = new ContractDetail();
                    cdQuery.setRecordId(cd.getRecordId());
                    List<GroupCenter> groupCenterList = contractDetailDao.getGroupCenterList(cdQuery);

                    List<ContractDetail> lnDeailList = new ArrayList<ContractDetail>();
                    String comIds = "";
                    List<DictValue> valList = new ArrayList<DictValue>();
                    Map<String, String> map = new HashMap<String, String>();
                    ChangeDataUtils dataUtils = new ChangeDataUtils();
                    String ids = dataUtils.getNeedDealData(data);
                    if (StringUtils.isNotBlank(ids))
                    {
                        comIds = CompanyUtil.getInstance().getEcoemyId() + "," + company.getRecordId();
                        valList = contractDetailDao.getAllValue(comIds, ids);
                        map = dataUtils.getMapDatas(valList);
                    }

                    // 龙南合同
                    ContractDetail lnContractDetail = data.clone();
                    ContractCraft craft = data.getContractCraftList().clone();
                    lnContractDetail.setContractCraftList(craft);
                    Contract contract = data.getContract().clone();
                    lnContractDetail.setContract(contract);
                    if (StringUtils.isBlank(lnContractDetail.getGroupCenterId()))
                    {
                        lnContractDetail.setGroupCenterId(data.getRecordId());
                    }
                    // 进行工艺转换
                    dataUtils.trainCondeail(valList,
                        lnContractDetail,
                        CompanyUtil.getInstance().getEcoemyId(),
                        map,
                        company.getRecordId());
                    lnDeailList.add(lnContractDetail);

                    dataUtils.genCompanyData(lnDeailList,
                        CompanyUtil.getInstance().getEcoemyId(),
                        company,
                        groupCenterList,
                        user);
                    break;
                }
            }
        }
    }

    // 重置成品采购单缺失数据
    @Transactional(readOnly = false)
    public void sentPrdorderData()
    {
        CompanyUtil companyUtil = CompanyUtil.getInstance();
        String factId = companyUtil.getFactId();
        Company company = companyDao.get(factId);
        User user = new User("10873");

        ContractDetail query = new ContractDetail("548207");
        List<ContractDetail> detailList = initDataDao.getDetailDataList(query);

        // 生成本公司的采购单
        ChangeDataUtils dataUtils = new ChangeDataUtils();
        dataUtils.setPrdorderData(detailList, company, null, user);
    }

    public BigDecimal computQty(BigDecimal needBord, BigDecimal excessStock, BigDecimal useStocks)
    {
        BigDecimal returnQty = needBord.subtract(excessStock).subtract(useStocks);
        if (returnQty.compareTo(BigDecimal.ZERO) <= 0)
        {
            returnQty = BigDecimal.ZERO;
        }
        return returnQty;
    }

    // 重置材料费和集控成本单价
    @Transactional(readOnly = false)
    public void initMatierlFeePrice()
    {
        Notification query = new Notification();
        query.setFactId(CompanyUtil.getInstance().getFactId());
        BigDecimal zero = BigDecimal.ZERO;

        List<GroupCenter> updateList = Lists.newArrayList();

        // 获取要重置的集控集合
        List<Notification> groupCenterList = initDataDao.getGroupCenterInitData(query);
        if (Collections3.isEmpty(groupCenterList))
        {
            return;
        }
        for (Notification noti : groupCenterList)
        {
            // 材料费
            BigDecimal materialFee = zero;

            // 获取工程资料开料尺寸
            CardA card = new CardA();
            card.setCompany(new Company(noti.getCompanyId()));
            card.setCustomerModels(noti.getCustomerModel());
            MaterialCard result = erpCraftDao.findMaterialByCraft(card);
            if (null == result)
            {
                continue;
            }
            BigDecimal useRatio = null == result.getUseRatio() ? zero : result.getUseRatio(); // 利用率
            BigDecimal pnlLength = null == result.getSetLength() ? zero : result.getSetLength();// set长
            BigDecimal pnlWidth = null == result.getSetWidth() ? zero : result.getSetWidth(); // set宽
            BigDecimal pnlDivisor =
                null == result.getPnlDivisor() ? zero : new BigDecimal(result.getPnlDivisor());// 拼版数
            BigDecimal quantity =
                StringUtils.isBlank(noti.getQuantity()) ? zero : new BigDecimal(noti.getQuantity());// 数量
            BigDecimal needBord = null == noti.getNeedBord() ? zero : new BigDecimal(noti.getNeedBord()); // 需要大板数
            BigDecimal excessStock = null == noti.getExcessStock() ? zero : noti.getExcessStock(); // 余料数量

            BigDecimal orderArea = pnlLength.multiply(pnlWidth)
                .divide(pnlDivisor, 6, BigDecimal.ROUND_HALF_UP)
                .divide(new BigDecimal(1000000), 6, BigDecimal.ROUND_HALF_UP)
                .multiply(quantity)
                .setScale(4, BigDecimal.ROUND_HALF_UP);

            // 获取替换料信息
            List<NotificationMaterial> nmList = initDataDao.getNotificationMaterialList(noti);
            Integer useStocks = 0;
            if (Collections3.isNotEmpty(nmList))
            {
                for (NotificationMaterial nm : nmList)
                {
                    if (null != nm.getUseStocks())
                    {
                        useStocks += nm.getUseStocks();
                    }
                }
            }

            // 主材料成本费
            if (null != noti.getPrice() && noti.getPrice().compareTo(BigDecimal.ZERO) > 0)
            {
                BigDecimal bordLength = null == noti.getBordLength() ? zero : noti.getBordLength();// 长
                BigDecimal bordWidth = null == noti.getBordWidth() ? zero : noti.getBordWidth(); // 宽
                BigDecimal price = noti.getPrice()
                    .multiply(new BigDecimal(1000000))
                    .divide(bordLength, 6, BigDecimal.ROUND_HALF_UP)
                    .divide(bordWidth, 6, BigDecimal.ROUND_HALF_UP);

                materialFee = price.divide(useRatio.subtract(new BigDecimal(1)), 6, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal(100))
                    .multiply(orderArea)
                    .multiply(computQty(needBord, excessStock, new BigDecimal(useStocks)))
                    .divide(needBord, 2, BigDecimal.ROUND_HALF_UP);
            }

            // 替换料成本费
            for (NotificationMaterial nm : nmList)
            {
                BigDecimal bordLength = null == nm.getBordLength() ? zero : nm.getBordLength();// 长
                BigDecimal bordWidth = null == nm.getBordWidth() ? zero : nm.getBordWidth(); // 宽
                BigDecimal price = nm.getPrice()
                    .multiply(new BigDecimal(1000000))
                    .divide(bordLength, 6, BigDecimal.ROUND_HALF_UP)
                    .divide(bordWidth, 6, BigDecimal.ROUND_HALF_UP);

                BigDecimal nmMaterialFee =
                    price.divide(useRatio.subtract(new BigDecimal(1)), 6, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal(100))
                        .multiply(orderArea)
                        .multiply(new BigDecimal(useStocks))
                        .divide(needBord, 2, BigDecimal.ROUND_HALF_UP);
                materialFee = materialFee.add(nmMaterialFee);
            }

            BigDecimal sumFee = materialFee.add(noti.getProcessFee());
            BigDecimal costPrice = sumFee.divide(quantity, 6, BigDecimal.ROUND_HALF_UP);
            BigDecimal lnPrice = costPrice.divide(new BigDecimal(100), 6, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal(102))
                .setScale(6, BigDecimal.ROUND_HALF_UP);

            GroupCenter groupCenter = new GroupCenter();
            groupCenter.setRecordId(noti.getGroupCenterId());
            groupCenter.setMaterialFee(materialFee);
            groupCenter.setCostPrice(costPrice);
            groupCenter.setLnPrice(lnPrice);
            groupCenter.setJxPrice(costPrice);
            updateList.add(groupCenter);
        }
        if (Collections3.isNotEmpty(updateList))
        {
            initDataDao.updateGroupCenterFeePrice(updateList);
        }

        InitIcloudGroupCenterUtils utils = new InitIcloudGroupCenterUtils();
        utils.initAmount();
    }

    // 重置付款对账单(外协)
    @Transactional(readOnly = false)
    public void iniPayableData(String companyId, Integer period)
    {
        String saleIds = CompanyUtil.getInstance().getSaleIds();
        String ecoemyId = CompanyUtil.getInstance().getEcoemyId();
        Company company = new Company(companyId);
        MaterialCheck check = new MaterialCheck();
        check.setPeriod(period);
        check.setSaleComIds(saleIds);

        // // 清除销售公司付款对账数据(外协)
        // check.setCompanyId(CompanyUtil.getInstance().getEcoemyId());
        // check.setSaleCompanyId(company.getRecordId());
        // materialCheckDao.delMcOutSourceData(check);
        //
        // // 清除龙南付款对账数据(外协)
        // check.setCompanyId(company.getRecordId());
        // check.setSaleCompanyId(null);
        // materialCheckDao.delMcOutSourceData(check);

        // 插入销售公司付款对账数据(采购入库外协)
        check.setCompanyId(company.getRecordId());
        materialCheckDao.saleCheckOutSourceTwoInsert(check);

        // 插入龙南付款对账数据(采购入库外协)
        check.setCompanyId(ecoemyId);
        check.setSaleCompanyId(company.getRecordId());
        materialCheckDao.saleCheckOutSourceTwoInsert(check);

        // 插入销售公司付款对账数据(补货入库外协)
        check.setCompanyId(company.getRecordId());
        check.setSaleCompanyId(null);
        materialCheckDao.saleCheckOutSourceInsert(check);

        // 插入龙南付款对账数据(补货入库外协)
        check.setCompanyId(ecoemyId);
        check.setSaleCompanyId(company.getRecordId());
        materialCheckDao.saleCheckOutSourceInsert(check);

        // 生成销售客诉付款对账单(退货出库外协)
        check.setCompanyId(company.getRecordId());
        check.setSaleCompanyId(null);
        materialCheckDao.saleCheckComplaintOutSourceInsert(check);

        // 生成龙南付款对账单(退货出库外协)
        check.setCompanyId(ecoemyId);
        check.setSaleCompanyId(company.getRecordId());
        materialCheckDao.saleCheckComplaintOutSourceInsert(check);

        // 更新销售供应商和总金额
        check.setCompany(company);
        accountsPayableDao.batchInsertPayable(check);
        updateDataDao.updatePayableAmount(check);

        // 更新龙南供应商和总金额
        check.setCompany((new Company(ecoemyId)));
        accountsPayableDao.batchInsertPayable(check);
        updateDataDao.updatePayableAmount(check);
    }

    // 重置退货入库记录及账单
    @Transactional(readOnly = false)
    public void initReturnInStockData()
    {
        ContractDetail query = new ContractDetail();
        query.setCompanyId("3");
        query.setFactId(CompanyUtil.getInstance().getFactId());
        List<String> list = initDataDao.getFactRejectList(query);
        for (String id : list)
        {
            RejectApplication rejectApplication = rejectApplicationDao.get(id);
            rejectApplication.setStatus(TypeKey.ST_REJECTAPPLICATION_STATUS_FINISH);
            ModifyUtils.sentRejectApplicationData(rejectApplication, new User(), 2);
        }
    }

    // 重置原料付款对账单
    @Transactional(readOnly = false)
    public void initPurchPayable()
    {
        // 获取原料采购入库未生成付款账单的记录
        List<RawmaterialStock> inPurchList = rawmaterialStockDao.getInPurchList();
        if (Collections3.isNotEmpty(inPurchList))
        {
            for (RawmaterialStock stock : inPurchList)
            {
                if (null == stock.getPurchasingDtlId())
                {
                    continue;
                }
                PurchasingDetail detail = purchasingDetailDao.get(stock.getPurchasingDtlId().toString());
                rawInoutService.executeGenerateMaterialCheck(stock, detail);
            }
        }

        // 获取采购退货补货未生成付款账单的记录
        // List<RawmaterialStock> purchReturnList = rawmaterialStockDao.getPurchReturnList();
        // if (Collections3.isNotEmpty(purchReturnList))
        // {
        // for (RawmaterialStock stock : purchReturnList)
        // {
        // if (StringUtils.isBlank(stock.getReturnsNo()))
        // {
        // continue;
        // }
        // PuReturnsDetail puReturnsDetail = returnDetailDao.get(stock.getReturnsNo());
        // PurchasingDetail poDtl = purchasingDetailDao.getPurchasingDetailinformation(puReturnsDetail);
        // rawInoutService.executeGenerateMaterialReturnsCheck(stock.getReturnsNo(),
        // stock.getQuantity(),
        // stock.getDesireTreatments(),
        // poDtl);
        // }
        // }
    }

    // 根据客诉单生成销售公司与总部经济的采购退货单、退货记录
    @Transactional(readOnly = false)
    public void sentPurReturnPayable(String rejectId)
    {
        RejectApplication rejectApplication = rejectApplicationDao.get(rejectId);
        String ecoemyId = CompanyUtil.getInstance().getEcoemyId();
        rejectApplication.setEcoemyId(ecoemyId);
        RejectApplication applications = rejectApplicationDao.findGroupData(rejectApplication);
        if (null == applications)
        {
            return;
        }
        if (StringUtils.isBlank(applications.getJxComId()))
        {
            return;
        }
        applications.setXsComId(applications.getJxComId());
        applications.setXsCustId(applications.getJxCustId());
        applications.setXsPrice(applications.getJxPrice());
        applications.setContractDeailId(applications.getJxDeailId());
        applications.setXsSuppierId(applications.getJxSuppierId());
        applications.setXsPurDeailId(applications.getJxPurDeailId());

        Date nowDate = new Date();
        if (null != rejectApplication.getBusinessHappenTime())
        {
            nowDate = rejectApplication.getBusinessHappenTime();
        }

        // 退货
        ModifyUtils.sentRejectProductData(applications, rejectApplication, new User(), 2, 1, nowDate);

/*        if (StringUtils.isNotBlank(applications.getLnAppId()))
        {
            // 退货
            ModifyUtils.sentRejectProductData(applications, rejectApplication, new User(), 2, 2, nowDate);
        }*/
    }

/*    public boolean checkOrderPayWay(String id, Integer num)
    {
        boolean flag = false;
        String payWayValue = null;
        switch (num)
        {
            // 送货单明细
            case 1:
                payWayValue = initDataDao.getOrderPayWayOne(id);
                break;
            // 成品采购明细
            case 2:
                payWayValue = initDataDao.getOrderPayWayTwo(id);
                break;
            // 原料采购明细
            case 3:
                payWayValue = initDataDao.getOrderPayWayThree(id);
                break;
            // 成品出入库记录
            case 4:
                payWayValue = initDataDao.getOrderPayWayFour(id);
                break;
        }
        if ("现金".equals(payWayValue))
        {
            flag = true;
        }
        return flag;
    }*/

    // 重置成品付款对账单
    public void initGroupCenterPayable(String period)
    {
        GroupCenterBill groupCenterBill = new GroupCenterBill();
        groupCenterBill.setStartMonth(period);
        reconciliationService.updateCheckData(groupCenterBill);
    }

    // 原料付款对账单生成应付单
    // 根据供应链账单重置供应链收款与工厂付款

    public void initPurSinglePayble()
    {
        Company company = new Company("17");
        List<MaterialCheck> mcList = initDataDao.getSingMaterialCheckList(company);
        if (Collections3.isEmpty(mcList))
        {
            return;
        }
        for (MaterialCheck materialCheck : mcList)
        {
            materialCheck.setCompany(company);
            ModifyUtils.sentSinglePayableData(materialCheck, new User());
        }
    }

    @Transactional(readOnly = false)
    public void initSupplyChainAccounts(String companyId, Integer period)
    {
        MaterialCheck materialCheck = new MaterialCheck();
        materialCheck.setCompanyId(companyId);
        materialCheck.setPeriod(period);
        List<MaterialCheck> list = materialCheckDao.getSupplyChainData(materialCheck);
        if (Collections3.isEmpty(list))
        {
            return;
        }
        // 转期需要新增的账单
        List<MaterialCheck> mcInsertList = Lists.newArrayList();

        // 要更新账期、数量、金额、状态的账单
        List<MaterialCheck> mcUpdateList = Lists.newArrayList();

        for (MaterialCheck mc : list)
        {
            // 如果进行了转期会存在多笔记录
            List<MaterialCheck> sourceList = materialCheckDao.getSourceList(mc);

            for (MaterialCheck sourceMc : sourceList)
            {
                sourceMc.setSupplyChainMcId(mc.getSupplyChainMcId());
                sourceMc.setSupplyChainGcId(mc.getSupplyChainGcId());
                sourceMc.setMcId(mc.getMcId());
                if (sourceMc.getRecordId().equals(mc.getRecordId()))
                {
                    mcUpdateList.add(sourceMc);
                    continue;
                }
                mcInsertList.add(sourceMc);
            }
        }
        List<MaterialCheck> mcSaveList = Lists.newArrayList();
        List<GoodsCheck> gcSaveList = Lists.newArrayList();
        if (Collections3.isNotEmpty(mcInsertList))
        {
            for (MaterialCheck insert : mcInsertList)
            {
                BigDecimal quantity = BigDecimal.ZERO;
                if (null != insert.getQuantity())
                {
                    quantity = insert.getQuantity();
                }
                MaterialCheck mc = materialCheckDao.get(insert.getMcId());
                if (null != mc)
                {
                    mc.setRecordId(null);
                    mc.setQuantity(quantity);
                    mc.setPeriod(insert.getPeriod());
                    mc.setOperateStatus(insert.getOperateStatus());
                    mc.setEngineeringFee(insert.getEngineeringFee());
                    mc.setMouldFee(insert.getMouldFee());
                    mc.setOthersFee(insert.getOthersFee());
                    mc.setTestShelfFee(insert.getTestShelfFee());
                    if (null == mc.getPrice())
                    {
                        mc.setPrice(BigDecimal.ZERO);
                    }
                    BigDecimal amount = mc.getPrice().multiply(quantity);
                    if (null != mc.getMouldFee())
                    {
                        amount = amount.add(mc.getMouldFee());
                    }
                    if (null != mc.getOthersFee())
                    {
                        amount = amount.add(mc.getOthersFee());
                    }
                    if (null != mc.getTestShelfFee())
                    {
                        amount = amount.add(mc.getTestShelfFee());
                    }
                    if (null != mc.getEngineeringFee())
                    {
                        amount = amount.add(mc.getEngineeringFee());
                    }
                    mc.setAmount(amount);
                    mcSaveList.add(mc);
                }
                GoodsCheck supplyChainGc = goodsCheckDao.get(insert.getSupplyChainGcId());
                if (null != supplyChainGc)
                {
                    supplyChainGc.setRecordId(null);
                    supplyChainGc.setQuantity(quantity.intValue());
                    supplyChainGc.setPeriod(insert.getPeriod());
                    supplyChainGc.setOperateStatus(insert.getOperateStatus());

                    supplyChainGc.setEngineeringFee(insert.getEngineeringFee());
                    supplyChainGc.setMouldFee(insert.getMouldFee());
                    supplyChainGc.setOthersFee(insert.getOthersFee());
                    supplyChainGc.setTestShelfFee(insert.getTestShelfFee());

                    if (null == supplyChainGc.getPrice())
                    {
                        supplyChainGc.setPrice(BigDecimal.ZERO);
                    }
                    BigDecimal amount = supplyChainGc.getPrice().multiply(quantity);
                    if (null != supplyChainGc.getMouldFee())
                    {
                        amount = amount.add(supplyChainGc.getMouldFee());
                    }
                    if (null != supplyChainGc.getOthersFee())
                    {
                        amount = amount.add(supplyChainGc.getOthersFee());
                    }
                    if (null != supplyChainGc.getTestShelfFee())
                    {
                        amount = amount.add(supplyChainGc.getTestShelfFee());
                    }
                    if (null != supplyChainGc.getEngineeringFee())
                    {
                        amount = amount.add(supplyChainGc.getEngineeringFee());
                    }
                    supplyChainGc.setAmount(amount);
                    gcSaveList.add(supplyChainGc);
                }
            }
        }

        List<MaterialCheck> mcUpList = Lists.newArrayList();
        List<GoodsCheck> gcUpList = Lists.newArrayList();
        if (Collections3.isNotEmpty(mcUpdateList))
        {
            for (MaterialCheck update : mcUpdateList)
            {
                BigDecimal quantity = BigDecimal.ZERO;
                if (null != update.getQuantity())
                {
                    quantity = update.getQuantity();
                }
                MaterialCheck mc = materialCheckDao.get(update.getMcId());
                if (null != mc)
                {
                    mc.setQuantity(quantity);
                    mc.setPeriod(update.getPeriod());
                    mc.setOperateStatus(update.getOperateStatus());
                    mc.setEngineeringFee(update.getEngineeringFee());
                    mc.setMouldFee(update.getMouldFee());
                    mc.setOthersFee(update.getOthersFee());
                    mc.setTestShelfFee(update.getTestShelfFee());
                    if (null == mc.getPrice())
                    {
                        mc.setPrice(BigDecimal.ZERO);
                    }
                    BigDecimal amount = mc.getPrice().multiply(quantity);
                    if (null != mc.getMouldFee())
                    {
                        amount = amount.add(mc.getMouldFee());
                    }
                    if (null != mc.getOthersFee())
                    {
                        amount = amount.add(mc.getOthersFee());
                    }
                    if (null != mc.getTestShelfFee())
                    {
                        amount = amount.add(mc.getTestShelfFee());
                    }
                    if (null != mc.getEngineeringFee())
                    {
                        amount = amount.add(mc.getEngineeringFee());
                    }
                    mc.setAmount(amount);
                    mcUpList.add(mc);
                }
                GoodsCheck supplyChainGc = goodsCheckDao.get(update.getSupplyChainGcId());
                if (null != supplyChainGc)
                {
                    supplyChainGc.setQuantity(quantity.intValue());
                    supplyChainGc.setPeriod(update.getPeriod());
                    supplyChainGc.setOperateStatus(update.getOperateStatus());
                    supplyChainGc.setEngineeringFee(update.getEngineeringFee());
                    supplyChainGc.setMouldFee(update.getMouldFee());
                    supplyChainGc.setOthersFee(update.getOthersFee());
                    supplyChainGc.setTestShelfFee(update.getTestShelfFee());
                    if (null == supplyChainGc.getPrice())
                    {
                        supplyChainGc.setPrice(BigDecimal.ZERO);
                    }
                    BigDecimal amount = supplyChainGc.getPrice().multiply(quantity);
                    if (null != supplyChainGc.getMouldFee())
                    {
                        amount = amount.add(supplyChainGc.getMouldFee());
                    }
                    if (null != supplyChainGc.getOthersFee())
                    {
                        amount = amount.add(supplyChainGc.getOthersFee());
                    }
                    if (null != supplyChainGc.getTestShelfFee())
                    {
                        amount = amount.add(supplyChainGc.getTestShelfFee());
                    }
                    if (null != supplyChainGc.getEngineeringFee())
                    {
                        amount = amount.add(supplyChainGc.getEngineeringFee());
                    }
                    if (null != supplyChainGc.getSampleFee())
                    {
                        amount = amount.add(supplyChainGc.getSampleFee());
                    }
                    if (null != supplyChainGc.getFilmFee())
                    {
                        amount = amount.add(supplyChainGc.getFilmFee());
                    }
                    supplyChainGc.setAmount(amount);
                    gcUpList.add(supplyChainGc);
                }
            }
        }

        // 处理账单明细数据
        List<MaterialCheck> concatMcList = Lists.newArrayList();
        List<GoodsCheck> concatGcList = Lists.newArrayList();
        if (Collections3.isNotEmpty(mcSaveList))
        {
            materialCheckDao.batchInsertAdjustData(mcSaveList);
            concatMcList.addAll(mcSaveList);
        }
        if (Collections3.isNotEmpty(mcUpList))
        {
            materialCheckDao.batchUpdateAdjustData(mcUpList);
            concatMcList.addAll(mcUpList);
        }
        if (Collections3.isNotEmpty(gcSaveList))
        {
            goodsCheckDao.batchInsertAdjustData(gcSaveList);
            concatGcList.addAll(gcSaveList);
        }
        if (Collections3.isNotEmpty(gcUpList))
        {
            goodsCheckDao.batchUpdateAdjustData(gcUpList);
            concatGcList.addAll(gcUpList);
        }

        // 处理账单数据并生成应收付单
        if (Collections3.isNotEmpty(concatMcList))
        {
            // 对账单明细根据公司和客户进行分组
            Map<String, Long> countMap = concatMcList.stream()
                .collect(Collectors.groupingBy(o -> o.getCompany().getRecordId() + "_" + o.getSupplier().getRecordId()
                    + "_" + o.getPeriod(), Collectors.counting()));
            List<MaterialCheck> distinctMcList = countMap.keySet().stream().map(key -> {
                String[] temp = key.split("_");
                String mcCompanyId = temp[0];
                String supplierId = temp[1];
                String mcPeriod = temp[2];
                MaterialCheck check = new MaterialCheck();
                check.setCompanyId(mcCompanyId);
                check.setCompany(new Company(mcCompanyId));
                check.setSupplier(new Supplier(supplierId));
                check.setPeriod(Integer.valueOf(mcPeriod));
                return check;
            }).collect(Collectors.toList());

            if (Collections3.isNotEmpty(distinctMcList))
            {
                for (MaterialCheck check : distinctMcList)
                {
                    // 没有账单先创建
                    accountsPayableDao.batchInsertPayable(check);

                    // 更新对账单金额
                    updateDataDao.updatePayableAmount(check);

                    if (period.compareTo(check.getPeriod()) == 0)
                    {
                        // 更新对账单状态
                        accountsPayableDao.updateByMaterialCheck(check);

                        // 生成应付单
                        check.setCompanyId(check.getCompany().getRecordId());
                        ModifyUtils.sentSinglePayableData(check, new User());
                    }
                }
            }
        }
        if (Collections3.isNotEmpty(concatGcList))
        {
            // 对账单明细根据公司和客户进行分组
            Map<String, Long> countMap = concatGcList.stream()
                .collect(Collectors.groupingBy(o -> o.getCompany().getRecordId() + "_" + o.getCustomer().getRecordId()
                    + "_" + o.getPeriod(), Collectors.counting()));
            List<GoodsCheck> distinctGcList = countMap.keySet().stream().map(key -> {
                String[] temp = key.split("_");
                String gcCompanyId = temp[0];
                String customerId = temp[1];
                String gcPeriod = temp[2];
                GoodsCheck check = new GoodsCheck();
                check.setCompanyId(gcCompanyId);
                check.setCompany(new Company(gcCompanyId));
                check.setCustomer(new Customer(customerId));
                check.setPeriod(Integer.valueOf(gcPeriod));
                return check;
            }).collect(Collectors.toList());

            if (Collections3.isNotEmpty(distinctGcList))
            {
                for (GoodsCheck check : distinctGcList)
                {
                    // 没有账单先创建
                    accountsReceivableDao.batchInsertReceivable(check);

                    // 更新对账单金额
                    updateDataDao.updateReceivableAmount(check);

                    if (period.compareTo(check.getPeriod()) == 0)
                    {
                        // 更新对账单状态
                        accountsReceivableDao.updateByGoodsCheck(check);

                        // 生成应收单
                        check.setCompanyId(check.getCompany().getRecordId());
                        ModifyUtils.sentSingleReceivableData(check, new User());
                    }
                }
            }
        }
    }

    // 复制客户
    @Transactional(readOnly = false)
    public void cloneCustmer()
    {
        Company company = new Company("15");
        List<Customer> list = customerDao.getCustomerDataTwo(company);
        if (Collections3.isEmpty(list))
        {
            return;
        }
        for (Customer cus : list)
        {
            ShareDataUtil.getInstance().saveData(cus, 2, 1);
        }
    }

    // 清除总部经济送货收款数据
    @Transactional(readOnly = false)
    public void cleanOutEcoemyData()
    {
        Company company = new Company();
        company.setRecordId(CompanyUtil.getInstance().getEcoemyId());
        String startDate = "2022-01-01";
        String endDate = "2023-01-01";

        // 清除总部经济外协送货、出库与账单数据
        ContractDetail contractDetail = new ContractDetail();
        contractDetail.setCompany(company);
        contractDetail.setCreatedDate(DateUtils.parseDate(startDate));
        List<GoodsCheck> goodsCheckList = goodsCheckDao.getCleanGcData(contractDetail);
        if (Collections3.isNotEmpty(goodsCheckList))
        {
            String gcIds = null;
            String deliveryIds = null;
            String delDetailIds = null;
            String outBoundIds = null;
            String productStoreIds = null;
            Customer customer = goodsCheckList.get(0).getCustomer();
            Set<Integer> periodList = goodsCheckList.stream().map(GoodsCheck::getPeriod).collect(Collectors.toSet());
            for (GoodsCheck goodsCheck : goodsCheckList)
            {
                if (StringUtils.isNotBlank(gcIds))
                {
                    gcIds = gcIds + "," + goodsCheck.getRecordId();
                }
                else
                {
                    gcIds = goodsCheck.getRecordId();
                }
                if (StringUtils.isNotBlank(goodsCheck.getDeliveryId()))
                {
                    if (StringUtils.isNotBlank(deliveryIds))
                    {
                        deliveryIds = deliveryIds + "," + goodsCheck.getDeliveryId();
                    }
                    else
                    {
                        deliveryIds = goodsCheck.getDeliveryId();
                    }
                }
                if (StringUtils.isNotBlank(goodsCheck.getDelDetailId()))
                {
                    if (StringUtils.isNotBlank(delDetailIds))
                    {
                        delDetailIds = delDetailIds + "," + goodsCheck.getDelDetailId();
                    }
                    else
                    {
                        delDetailIds = goodsCheck.getDelDetailId();
                    }
                }
                if (StringUtils.isNotBlank(goodsCheck.getOutBoundId()))
                {
                    if (StringUtils.isNotBlank(outBoundIds))
                    {
                        outBoundIds = outBoundIds + "," + goodsCheck.getOutBoundId();
                    }
                    else
                    {
                        outBoundIds = goodsCheck.getOutBoundId();
                    }
                }
                if (StringUtils.isNotBlank(goodsCheck.getProductStoreId()))
                {
                    if (StringUtils.isNotBlank(productStoreIds))
                    {
                        productStoreIds = productStoreIds + "," + goodsCheck.getProductStoreId();
                    }
                    else
                    {
                        productStoreIds = goodsCheck.getProductStoreId();
                    }
                }
            }

            // 清除送货单明细
            if (StringUtils.isNotBlank(delDetailIds))
            {
                DeliveryDetail deliveryDetail = new DeliveryDetail(delDetailIds);
                deliveryDetailDao.cleanDataByIds(deliveryDetail);
            }
            // 清除送货单
            if (StringUtils.isNotBlank(deliveryIds))
            {
                Delivery delivery = new Delivery(deliveryIds);
                deliveryDao.cleanDataByIds(delivery);
            }
            // 清除送货出库记录
            if (StringUtils.isNotBlank(productStoreIds))
            {
                ProductStore productStore = new ProductStore(productStoreIds);
                productStoreDao.cleanDataByIds(productStore);
            }
            // 清除出库单
            if (StringUtils.isNotBlank(outBoundIds))
            {
                OutBound outBound = new OutBound();
                outBound.setRecordId(outBoundIds);
                outBoundDao.cleanDataByIds(outBound);
            }
            // 清除收款对账单明细
            if (StringUtils.isNotBlank(gcIds))
            {
                goodsCheckDao.batchDeleteAdjustData(gcIds);
            }
            // 更新收款对账单金额
            String periods = null;
            for (Integer period : periodList)
            {
                if (StringUtils.isNotBlank(periods))
                {
                    periods = periods + "," + period.toString();
                }
                else
                {
                    periods = period.toString();
                }
            }
            GoodsCheck goodsCheck = new GoodsCheck();
            goodsCheck.setCompanyId(company.getRecordId());
            goodsCheck.setCustomer(customer);
            goodsCheck.setPeriods(periods);
            accountsReceivableDao.batchUpdateAmount(goodsCheck);
        }

        // 获取总部经济采购入库记录
        ProductStore query = new ProductStore();
        query.setCompany(company);
        query.setInoutTimeStartQr(DateUtils.parseDate(startDate));
        query.setInoutTimeEndQr(DateUtils.parseDate(endDate));
        List<ProductStore> storeList = productStoreDao.getListByDate(query);
        if (Collections3.isEmpty(storeList))
        {
            return;
        }
        String prdDetailIds = null;
        List<ProductStore> updateOldList = Lists.newArrayList();
        for (ProductStore store : storeList)
        {
            if (null != store.getPrdOrderDetailId())
            {
                if (StringUtils.isNotBlank(prdDetailIds))
                {
                    prdDetailIds = prdDetailIds + "," + store.getPrdOrderDetailId().toString();
                }
                else
                {
                    prdDetailIds = store.getPrdOrderDetailId().toString();
                }
            }
            ProductStore updateOld = new ProductStore();
            String recordIds = null;
            if (StringUtils.isNotBlank(store.getXsStoreId()))
            {
                recordIds = store.getXsStoreId() + "," + store.getRecordId();
                updateOld.setRecordId(recordIds);
                updateOld.setOldPrdStockId(store.getRecordId());
                updateOldList.add(updateOld);
            }
        }
        // 批量更新终端采购入库id
        if (Collections3.isNotEmpty(updateOldList))
        {
            productStoreDao.batchUpdateOldId(updateOldList);
        }

        // 按采购入库生成总部经济外协送货、出库与账单数据
        ChangeDataUtils changeDataUtils = new ChangeDataUtils();
        ProDetailInVo prdquery = new ProDetailInVo();
        prdquery.setCompany(company);
        prdquery.setRecordId(prdDetailIds);
        List<ProDetailInVo> list = prdorderDetailDao.getDetailWaitingInListByIds(prdquery);

        if (Collections3.isNotEmpty(storeList))
        {
            for (ProductStore store : storeList)
            {
                for (ProDetailInVo detail : list)
                {
                    if (null != store.getPrdOrderDetailId() && store.getPrdOrderDetailId()
                        .toString()
                        .equals(detail.getRecordId()))
                    {
                        if (null == store.getOperateDate())
                        {
                            store.setOperateDate(store.getCreatedDate());
                        }
                        changeDataUtils.sentDeliveryOutData(detail, store);
                    }
                }
            }
        }
    }

    // 重置客诉单缺失
    @Transactional(readOnly = false)
    public void initRejectData()
    {
        RejectApplication rejectApplication = rejectApplicationDao.get("11978");
        ModifyUtils.sentRejectApplicationData(rejectApplication, new User(), 3);
    }

    // 重置外协客诉的单据、出入库记录及账单
    @Transactional(readOnly = false)
    public void initOutComplaintData(Company company, Integer period)
    {
        GoodsCheck query = new GoodsCheck();
        query.setCompany(company);
        query.setPeriod(period);
        List<GoodsCheck> list = goodsCheckDao.getOutComplaintList(query);
        if (Collections3.isNotEmpty(list))
        {
            for (GoodsCheck gc : list)
            {
                if (null == gc.getInOutFlag())
                {
                    continue;
                }
                RejectApplication rejectApplication = rejectApplicationDao.get(gc.getComplaintId());
                String ecoemyId = CompanyUtil.getInstance().getEcoemyId();
                rejectApplication.setEcoemyId(ecoemyId);
                RejectApplication applications = rejectApplicationDao.findGroupData(rejectApplication);

                applications.setXsComId(applications.getJxComId());
                applications.setXsCustId(applications.getJxCustId());
                applications.setXsPrice(applications.getJxPrice());
                applications.setContractDeailId(applications.getJxDeailId());
                applications.setXsSuppierId(applications.getJxSuppierId());
                applications.setXsPurDeailId(applications.getJxPurDeailId());

                Date date = gc.getReceivedDate();
                User user = new User();
                switch (gc.getInOutFlag())
                {
                    // 客诉退货生成采购退货出库记录
                    case 0:
                        // 退货
                        ModifyUtils.sentRejectProductData(applications, rejectApplication, user, 2, 1, date);
                        if (StringUtils.isNotBlank(applications.getLnAppId()))
                        {
                            ModifyUtils.sentRejectProductData(applications, rejectApplication, user, 2, 2, date);
                        }
                        break;
                    // 客诉送货出库生成采购补货出库记录
                    case 1:
                        // 补货
                        ModifyUtils.sentRejectProductData(applications, rejectApplication, user, 1, 1, date);
                        if (StringUtils.isNotBlank(applications.getLnAppId()))
                        {
                            ModifyUtils.sentRejectProductData(applications, rejectApplication, user, 1, 2, date);
                        }
                        break;
                }
            }
        }
    }

    @Transactional(readOnly = false)
    public void initSentSinglePayble()
    {
        Company company = UserUtils.getUser().getCompany();
        List<GoodsCheck> gcList = initDataDao.getSingGoodsCheckList(company);
        if (Collections3.isEmpty(gcList))
        {
            return;
        }
        for (GoodsCheck goodsCheck : gcList)
        {
            goodsCheck.setCompany(company);
            ModifyUtils.sentSingleReceivableData(goodsCheck, new User());
        }
    }

    // 重置客诉送货数据缺失
    @Transactional(readOnly = false)
    public void initRejectSentOutData()
    {
        Company company = new Company("17");
        String ecoemyId = CompanyUtil.getInstance().getEcoemyId();
        List<OutBound> outBoundList = outBoundDao.getRejectOutBoundList(company);
        if (Collections3.isEmpty(outBoundList))
        {
            return;
        }
        for (OutBound outBound : outBoundList)
        {
            // 根据编号获取删除的出库单
            List<OutBound> delOutBoundList = outBoundDao.getListByNoError(outBound);
            if (Collections3.isEmpty(delOutBoundList))
            {
                continue;
            }
            List<OutBoundDeail> deailList = outBoundDao.getListByOutBoundId(outBound);
            if (Collections3.isEmpty(deailList))
            {
                continue;
            }
            List<OutBoundDeail> groupDeailList = Lists.newArrayList();
            for (OutBoundDeail deail : deailList)
            {
                if (StringUtils.isNotBlank(deail.getLnCompanyId()))
                {
                    OutBoundDeail lnAddDeail = deail.clone();
                    lnAddDeail.setSumQualitity(lnAddDeail.getOutBoundStocks() + lnAddDeail.getDonateStocks());
                    lnAddDeail.setCompanyId(deail.getLnCompanyId());
                    if (StringUtils.isNotBlank(lnAddDeail.getLnMaterialId()))
                    {
                        lnAddDeail.setMaterialId(lnAddDeail.getLnMaterialId());
                        lnAddDeail.setStoreHouseId(lnAddDeail.getLnStoreHouseId());
                    }
                    else
                    {
                        OutBoundDeail lnDeail = new OutBoundDeail();
                        lnDeail.setMaterialId(deail.getMaterialId());
                        lnDeail.setCompanyId(deailList.get(0).getLnCompanyId());
                        Material mat = materialDao.getMaterialIdByNo(lnDeail);
                        if (null != mat && StringUtils.isNotBlank(mat.getRecordId()))
                        {
                            lnAddDeail.setMaterialId(mat.getRecordId());
                            if (null != mat.getStoreHouseId())
                            {
                                lnAddDeail.setStoreHouseId(mat.getStoreHouseId().toString());
                            }
                        }
                        else
                        {
                            materialDao.insertMaterial(lnDeail);
                            lnAddDeail.setMaterialId(lnDeail.getRecordId());
                        }
                    }
                    lnAddDeail.setCustomerId(lnAddDeail.getLnCustomerId());
                    lnAddDeail.setContractDetailId(lnAddDeail.getLnContractDeailId());
                    for (OutBound delBound : delOutBoundList)
                    {
                        if (delBound.getCompanyId().equals(lnAddDeail.getCompanyId()))
                        {
                            lnAddDeail.setOutBoundId(delBound.getRecordId());
                            lnAddDeail.setDeliveryId(delBound.getDeliveryId());
                            break;
                        }
                    }
                    lnAddDeail.setComplaintId(lnAddDeail.getLnComplaintId());
                    groupDeailList.add(lnAddDeail);

                    if (StringUtils.isNotBlank(deail.getSaleContractDeailId()))
                    {
                        OutBoundDeail saleAddDeail = deail.clone();
                        saleAddDeail.setSumQualitity(saleAddDeail.getOutBoundStocks() + saleAddDeail.getDonateStocks());
                        saleAddDeail.setCompanyId(saleAddDeail.getSaleCompanyId());
                        if (StringUtils.isNotBlank(saleAddDeail.getSaleMaterialId()))
                        {
                            saleAddDeail.setMaterialId(saleAddDeail.getSaleMaterialId());
                            saleAddDeail.setStoreHouseId(saleAddDeail.getXsStoreHouseId());
                        }
                        else
                        {
                            OutBoundDeail xsDeail = new OutBoundDeail();
                            xsDeail.setMaterialId(deail.getMaterialId());
                            xsDeail.setCompanyId(saleAddDeail.getSaleCompanyId());
                            Material mat = materialDao.getMaterialIdByNo(xsDeail);
                            if (null != mat && StringUtils.isNotBlank(mat.getRecordId()))
                            {
                                saleAddDeail.setMaterialId(mat.getRecordId());
                                if (null != mat.getStoreHouseId())
                                {
                                    saleAddDeail.setStoreHouseId(mat.getStoreHouseId().toString());
                                }
                            }
                            else
                            {
                                materialDao.insertMaterial(xsDeail);
                                saleAddDeail.setMaterialId(xsDeail.getRecordId());
                            }
                        }
                        saleAddDeail.setCustomerId(saleAddDeail.getSaleCustomerId());
                        saleAddDeail.setContractDetailId(saleAddDeail.getSaleContractDeailId());
                        for (OutBound delBound : delOutBoundList)
                        {
                            if (delBound.getCompanyId().equals(saleAddDeail.getCompanyId()))
                            {
                                saleAddDeail.setOutBoundId(delBound.getRecordId());
                                saleAddDeail.setDeliveryId(delBound.getDeliveryId());
                                break;
                            }
                        }
                        saleAddDeail.setComplaintId(saleAddDeail.getXsComplaintId());
                        groupDeailList.add(saleAddDeail);
                    }
                }
            }

            outBound.setDeailList(groupDeailList);
            outBoundDao.runInsertDeail(outBound);
            outBoundDao.runBatchAdd(outBound);

            for (OutBoundDeail deail : deailList)
            {
                RejectApplication rej = new RejectApplication();
                rej.setRecordId(deail.getComplaintId());
                deail.setComplaint(rej);
                deail.setEcoemyId(ecoemyId);
                List<RejectApplication> rejectList = rejectApplicationDao.getRejectData(deail);
                if (Collections3.isNotEmpty(rejectList))
                {
                    for (RejectApplication reject : rejectList)
                    {
                        if (null != reject.getSentOutQty() && reject.getSentOutQty() > 0)
                        {
                            String rejectIds = null;
                            if (StringUtils.isNotBlank(reject.getJxAppId()))
                            {
                                if (StringUtils.isNotBlank(rejectIds))
                                {
                                    rejectIds = rejectIds + "," + reject.getJxAppId();
                                }
                                else
                                {
                                    rejectIds = reject.getJxAppId();
                                }
                            }
                            if (StringUtils.isNotBlank(reject.getLnAppId()))
                            {
                                if (StringUtils.isNotBlank(rejectIds))
                                {
                                    rejectIds = rejectIds + "," + reject.getLnAppId();
                                }
                                else
                                {
                                    rejectIds = reject.getLnAppId();
                                }
                            }
                            if (StringUtils.isNotBlank(reject.getXsAppId()))
                            {
                                if (StringUtils.isNotBlank(rejectIds))
                                {
                                    rejectIds = rejectIds + "," + reject.getXsAppId();
                                }
                                else
                                {
                                    rejectIds = reject.getXsAppId();
                                }
                            }
                            RejectApplication ra = new RejectApplication();
                            if (null == reject.getWaitSentOutQty() || reject.getWaitSentOutQty() == 0)
                            {
                                ra.setStatus(TypeKey.ST_REJECTAPPLICATION_STATUS_FINISH);

                                // 生成销售公司与龙南公司补货入库记录
                                RejectApplication rejectApplication = rejectApplicationDao.get(reject.getJxAppId());
                                rejectApplication.setEcoemyId(ecoemyId);
                                RejectApplication applications = rejectApplicationDao.findGroupData(rejectApplication);
                                // 内部
                                if (StringUtils.isNotBlank(applications.getGroupCenterId()))
                                {
                                    // 销售公司和工厂公司相同，则不需要走龙南和销售
                                    if (!applications.getJxAppId().equals(applications.getXsAppId()))
                                    {
                                        ModifyUtils.sentRejectProductData(applications,
                                            rejectApplication,
                                            UserUtils.getUser(),
                                            1,
                                            1,
                                            deail.getCreatedDate());

                                        ModifyUtils.sentRejectProductData(applications,
                                            rejectApplication,
                                            UserUtils.getUser(),
                                            1,
                                            2,
                                            deail.getCreatedDate());
                                    }

                                }
                                // 外协
                                else
                                {
                                    applications.setXsComId(applications.getJxComId());
                                    applications.setXsCustId(applications.getJxCustId());
                                    applications.setXsPrice(applications.getJxPrice());
                                    applications.setContractDeailId(applications.getJxDeailId());
                                    applications.setXsSuppierId(applications.getJxSuppierId());
                                    applications.setXsPurDeailId(applications.getJxPurDeailId());

                                    // 补货
                                    ModifyUtils.sentRejectProductData(applications,
                                        rejectApplication,
                                        UserUtils.getUser(),
                                        1,
                                        1,
                                        deail.getCreatedDate());

                                    if (StringUtils.isNotBlank(applications.getLnAppId()))
                                    {
                                        ModifyUtils.sentRejectProductData(applications,
                                            rejectApplication,
                                            UserUtils.getUser(),
                                            1,
                                            2,
                                            deail.getCreatedDate());
                                    }
                                }
                            }
                            else
                            {
                                ra.setStatus(TypeKey.ST_REJECTAPPLICATION_STATUS_REDELIVERED);
                            }
                            ra.setRecordId(rejectIds);
                            rejectApplicationDao.updateStatusTwo(ra);
                        }
                    }
                }
            }

            String outBoundId = null;
            String deliveryId = null;
            for (OutBound delBound : delOutBoundList)
            {
                if (StringUtils.isNotBlank(delBound.getRecordId()))
                {
                    if (StringUtils.isNotBlank(outBoundId))
                    {
                        outBoundId = outBoundId + "," + delBound.getRecordId();
                    }
                    else
                    {
                        outBoundId = delBound.getRecordId();
                    }
                }
                if (StringUtils.isNotBlank(delBound.getDeliveryId()))
                {
                    if (StringUtils.isNotBlank(deliveryId))
                    {
                        deliveryId = deliveryId + "," + delBound.getDeliveryId();
                    }
                    else
                    {
                        deliveryId = delBound.getDeliveryId();
                    }
                }
            }
            deliveryDao.updateActiveFlag(deliveryId);
            outBoundDao.updateActiveFlag(outBoundId);

            // 自动生成生成对账单
            for (OutBoundDeail deail : groupDeailList)
            {
                Delivery del = deliveryDao.get(deail.getDeliveryId());
                outBoundService.executeGenerateGoodsCheck(del, false, true, deail);
            }

        }
    }

    // 结算方式为现金的重新生成账单
    @Transactional(readOnly = false)
    public void sentGoodsCheckData()
    {
        String deliveryIds = "64527,66149,68231,68235,68375,68378,68379,68379,69124,69125,69126,69130,69131";
        String[] deliveryIdList = deliveryIds.split(",");
        for (String deliveryId : deliveryIdList)
        {
            Delivery del = deliveryDao.get(deliveryId);
            outBoundService.executeGenerateGoodsCheck(del, false);
        }
    }

    // 重置外协订单的工艺和物料
    @Transactional(readOnly = false)
    public void initOutMatCheckCraftMatData()
    {
        String ecoemyId = CompanyUtil.getInstance().getEcoemyId();
        List<MaterialCheck> list = materialCheckDao.getInitCheckData(ecoemyId);
        if (Collections3.isEmpty(list))
        {
            return;
        }
        for (MaterialCheck materialCheck : list)
        {
            if (null == materialCheck || StringUtils.isBlank(materialCheck.getContactDeailId()) || StringUtils.isBlank(
                materialCheck.getCompanyId()) || StringUtils.isBlank(materialCheck.getXsCraftId())
                || StringUtils.isBlank(materialCheck.getCraftNo())
                || StringUtils.isBlank(materialCheck.getCustomerModel()))
            {
                continue;
            }
            Company company = new Company(materialCheck.getCompanyId());

            // 创建厂编
            ContractCraft craft = new ContractCraft();
            craft.setRecordId(materialCheck.getXsCraftId());
            craft.setCompany(company);
            contractCraftDao.saveSqlData(craft);

            ContractDetail detail = new ContractDetail(materialCheck.getContactDeailId());
            detail.setCraft(craft);
            contractDetailDao.updateCraftId(detail);

            // 创建物料
            Material material = new Material();
            List<StoreHouse> storeHouseList = storeHouseDao.selectStoreHouselist(company);
            List<StoreHouse> storeHouseListCopy = new ArrayList<>();
            for (StoreHouse storeHouse : storeHouseList)
            {
                if (storeHouse.getType() == 2)
                {
                    storeHouseListCopy.add(storeHouse);
                }
            }
            if (storeHouseListCopy != null && storeHouseListCopy.size() > 0)
            {
                material.setStorehouse(storeHouseListCopy.get(0));
            }
            Customer da = customerDao.getdata(materialCheck.getContractId());
            material.setCustomerNo(da.getNo());// 客户编号
            material.setCompany(company);
            material.setNo(materialCheck.getCraftNo().trim());
            material.setSpecification(materialCheck.getCustomerModel());
            material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_PRODUCT);
            material.setMaterialType(ChangeDataUtils.returnMaterialType(company));
            if (StringUtils.isNotBlank(detail.getGroupCenterId()))
            {
                material.setGroupCenterId(detail.getGroupCenterId());
            }
            material.setCustomer(da);
            material.setCreatedDate(new Date());
            material.setLastUpdDate(new Date());
            materialDao.insert(material);

            materialCheck.setMaterial(material);
            materialCheckDao.updateMaterial(materialCheck);
        }
        list = materialCheckDao.getInitCheckDataTwo(ecoemyId);
        if (Collections3.isEmpty(list))
        {
            return;
        }
        for (MaterialCheck materialCheck : list)
        {
            if (null == materialCheck || StringUtils.isBlank(materialCheck.getContactDeailId()) || StringUtils.isBlank(
                materialCheck.getCompanyId()) || StringUtils.isBlank(materialCheck.getXsCraftId())
                || StringUtils.isBlank(materialCheck.getCraftNo())
                || StringUtils.isBlank(materialCheck.getCustomerModel()))
            {
                continue;
            }
            Company company = new Company(materialCheck.getCompanyId());

            // 创建厂编
            ContractCraft craft = new ContractCraft();
            craft.setRecordId(materialCheck.getXsCraftId());
            craft.setCompany(company);
            contractCraftDao.saveSqlData(craft);

            ContractDetail detail = new ContractDetail(materialCheck.getContactDeailId());
            detail.setCraft(craft);
            contractDetailDao.updateCraftId(detail);

            // 创建物料
            Material material = new Material();
            List<StoreHouse> storeHouseList = storeHouseDao.selectStoreHouselist(company);
            List<StoreHouse> storeHouseListCopy = new ArrayList<>();
            for (StoreHouse storeHouse : storeHouseList)
            {
                if (storeHouse.getType() == 2)
                {
                    storeHouseListCopy.add(storeHouse);
                }
            }
            if (storeHouseListCopy != null && storeHouseListCopy.size() > 0)
            {
                material.setStorehouse(storeHouseListCopy.get(0));
            }
            Customer da = customerDao.getdata(materialCheck.getContractId());
            material.setCustomerNo(da.getNo());// 客户编号
            material.setCompany(company);
            material.setNo(materialCheck.getCraftNo().trim());
            material.setSpecification(materialCheck.getCustomerModel());
            material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_PRODUCT);
            material.setMaterialType(ChangeDataUtils.returnMaterialType(company));
            if (StringUtils.isNotBlank(detail.getGroupCenterId()))
            {
                material.setGroupCenterId(detail.getGroupCenterId());
            }
            material.setCustomer(da);
            material.setCreatedDate(new Date());
            material.setLastUpdDate(new Date());
            materialDao.insert(material);

        }
    }

    @Transactional(readOnly = false)
    public void initCardFee()
    {
        String factId = CompanyUtil.getInstance().getFactId();
        Company company = new Company(factId);
        List<Purchasing> list = purchasingDao.getCardPurData(company);
        if (Collections3.isEmpty(list))
        {
            return;
        }
        for (Purchasing pur : list)
        {
            if (null == pur || StringUtils.isBlank(pur.getRecordId()))
            {
                continue;
            }
            Approval approval = new Approval();
            approval.setDataId(pur.getRecordId());
            ModifyUtils.updateOrderFeeByCardPur(approval, company, 1);
        }
    }

    @Transactional(readOnly = false)
    public void initUseMaterialArea()
    {
        // 清除原来的使用面积明细
//        materialAreaDetailDao.cleanAreaDetail();

        // 使用面积详情列表
        List<MaterialArea> areaList = materialAreaDetailDao.getMaterialAreaList();
        if (Collections3.isEmpty(areaList))
        {
            return;
        }
        Map<String, String> mapData = new HashMap<>();
        for (MaterialArea area : areaList)
        {
            if (null == area || null == area.getUseArea())
            {
                continue;
            }
            if (mapData.containsKey(area.getSpecification()))
            {
                continue;
            }
            mapData.put(area.getSpecification(), area.getSpecification());
        }
        Map<String, String> matMap = new HashMap<>();
        Material material = new Material();
        Company com = new Company();
        String factId = CompanyUtil.getInstance().getFactId();
        com.setRecordId(factId);
        material.setCompany(com);
        for (Map.Entry<String, String> entry : mapData.entrySet())
        {
            if (entry != null && entry.getValue() != null)
            {
                String specification = entry.getValue();
                String[] specifications = specification.split(" ");
                material.setCondition1(null);
                material.setCondition2(null);
                material.setCondition3(null);
                material.setCondition4(null);
                material.setCondition5(null);
                material.setCondition6(null);
                material.setCondition7(null);
                material.setCondition8(null);

                if (null != specifications && specifications.length > 0)
                {
                    for (int i = 0; i < specifications.length; i++)
                    {
                        String condition = specifications[i];
                        switch (i)
                        {
                            case 0:
                                material.setCondition1(condition);
                                break;
                            case 1:
                                material.setCondition2(condition);
                                break;
                            case 2:
                                material.setCondition3(condition);
                                break;
                            case 3:
                                material.setCondition4(condition);
                                break;
                            case 4:
                                material.setCondition5(condition);
                                break;
                            case 5:
                                material.setCondition6(condition);
                                break;
                            case 6:
                                material.setCondition7(condition);
                                break;
                            case 7:
                                material.setCondition8(condition);
                                break;
                        }
                    }
                }
                String materialIds = materialDao.getConcatIds(material);
                if (StringUtils.isBlank(materialIds))
                {
                    continue;
                }
                if (matMap.containsKey(specification))
                {
                    continue;
                }
                matMap.put(specification, materialIds);
            }
        }
        List<MaterialArea> list = Lists.newArrayList();
        for (MaterialArea area : areaList)
        {
            MatPreparation matp = new MatPreparation();
            matp.setDeptId(area.getDeptId());
            if (null == matMap || null == matMap.get(area.getSpecification()))
            {
                continue;
            }
            String materialids = matMap.get(area.getSpecification()).toString();
            matp.setMaterialId(materialids);
            String preparationIds = matPreparationDao.getconcatIds(matp);
            area.setMatPreparationId(preparationIds);
            list.add(area);
        }
        if (Collections3.isEmpty(list))
        {
            return;
        }
        for (MaterialArea materialArea : list)
        {
            if (null == materialArea || null == materialArea.getUseArea()
                || StringUtils.isBlank(materialArea.getMatPreparationId()))
            {
                continue;
            }
            // 获取报表列表(剩余面积)
            List<MatPreparation> dataList = matPreparationDao.getListByIds(materialArea.getMatPreparationId());
            if (Collections3.isEmpty(dataList))
            {
                continue;
            }
            BigDecimal area = materialArea.getUseArea();
            List<MaterialAreaDetail> insertList = Lists.newArrayList();
            for (MatPreparation matp : dataList)
            {
                if (null == matp || null == matp.getArea())
                {
                    continue;
                }
                BigDecimal useArea = matp.getArea();
                BigDecimal computArea = BigDecimal.ZERO;
                if (area.compareTo(useArea) > 0)
                {
                    area = area.subtract(useArea);
                    computArea = useArea;
                }
                else
                {
                    computArea = area;
                    area = BigDecimal.ZERO;
                }
                MaterialAreaDetail detail = new MaterialAreaDetail();
                detail.setMaterialAreaId(materialArea.getRecordId());
                detail.setMatPreparationId(matp.getRecordId());
                detail.setUseArea(computArea);
                detail.setCreatedDate(materialArea.getEstimateStartDate());
                insertList.add(detail);
                if (area.compareTo(BigDecimal.ZERO) <= 0)
                {
                    break;
                }
            }
            if (Collections3.isNotEmpty(insertList))
            {
                materialAreaDetailDao.batchInsertAreaDetail(insertList);

                materialAreaDetailDao.updateAreaMatpId(materialArea);
            }
        }
    }

    public void innerPurCardFee()
    {
        String period = "202302";
        List<GoodsCheck> list = goodsCheckDao.getInnerPurCardFeeList(period);
        if (Collections3.isEmpty(list))
        {
            return;
        }
        List<GoodsCheck> updateList = Lists.newArrayList();
        for (GoodsCheck gc : list)
        {
            if (null == gc)
            {
                continue;
            }
            BigDecimal amount = null == gc.getAmount() ? BigDecimal.ZERO : gc.getAmount();
            BigDecimal fee = null == gc.getFee() ? BigDecimal.ZERO : gc.getFee();
            gc.setOldAmount(amount);
            gc.setAmount(amount.add(fee));
            if (null != gc.getOriginalAmount())
            {
                gc.setOriginalAmount(gc.getOriginalAmount().add(fee));
            }
            updateList.add(gc);
        }
        if (Collections3.isNotEmpty(updateList))
        {
            goodsCheckDao.batchUpdateAmountData(updateList);
            // 更新对账单金额
            if (Collections3.isNotEmpty(updateList))
            {
                // 对账单明细根据公司和客户进行分组
                Map<String, Long> countMap = updateList.stream()
                    .collect(Collectors.groupingBy(o -> o.getCompanyId() + "_" + o.getCustomerId() + "_"
                        + o.getPeriod(), Collectors.counting()));
                List<GoodsCheck> gcList = countMap.keySet().stream().map(key -> {
                    String[] temp = key.split("_");
                    String companyId = temp[0];
                    String customerId = temp[1];
                    String periodStr = temp[2];
                    GoodsCheck check = new GoodsCheck();
                    check.setCompanyId(companyId);
                    check.setCompany(new Company(companyId));
                    check.setCustomer(new Customer(customerId));
                    check.setPeriod(Integer.valueOf(periodStr));
                    return check;
                }).collect(Collectors.toList());

                if (Collections3.isNotEmpty(gcList))
                {
                    for (GoodsCheck check : gcList)
                    {
                        AccountsReceivable ar = new AccountsReceivable();
                        ar.setCompany(check.getCompany());
                        ar.setCustomer(check.getCustomer());
                        ar.setPeriod(check.getPeriod());
                        accountsReceivableDao.updateReceivableAmount(ar);
                    }
                }
            }
        }
    }
    
    @Transactional(readOnly = false)
    public void initRawMaterialPrice(String startDateStr,String endDateStr)
    {
        if(StringUtils.isBlank(startDateStr) || StringUtils.isBlank(endDateStr))
        {
            return;
        }
        Date startDate = DateUtils.parseDate(startDateStr);
        Date endDate = DateUtils.parseDate(endDateStr);
        RawmaterialStock query = new RawmaterialStock();
        query.setCompany(new Company(CompanyUtil.getInstance().getFactId()));
        query.setInoutTimeStartQr(startDate);
        List<RawmaterialStock> materialList = rawmaterialStockDao.findMaterialList(query);
        if(Collections3.isNotEmpty(materialList))
        {
            for(RawmaterialStock rawMaterial : materialList)
            {
                rawMaterial.setCompany(query.getCompany());
                rawMaterial.setMaterial(new Material());
                rawMaterial.getMaterial().setRecordId(rawMaterial.getRecordId());
                rawMaterial.setInoutTimeStartQr(startDate);
                rawMaterial.setInoutTimeEndQr(endDate);
                List<RawmaterialStock> list = rawmaterialStockDao.findStockList(rawMaterial);
                Material stockRawmaterialStock;
                if (list.size() > 0)
                {
                    stockRawmaterialStock = list.get(0).getMaterial();
                }
                else
                {
                    stockRawmaterialStock = rawmaterialStockDao.findMaterialByPlace(rawMaterial.getMaterial());
                }
                rawMaterial.setMaterial(stockRawmaterialStock);
                RawStockUtil rawStockUtil = new RawStockUtil();
                List<RawmaterialStock> stockMaStockList = rawStockUtil.getRawoMaterialStockPlace(rawMaterial, "1");
                rawmaterialStockDao.batchUpdatePrice(stockMaStockList);
            }
        }
    }

    @Transactional(readOnly = false)
    public void sentPrdOrder(String contractId)
    {
        Contract query = new Contract();
        query.setRecordId(contractId);
        Contract contract = contractDao.get(query);
        Company company = companyDao.get(contract.getCompany());
        ChangeDataUtils changeDataUtils = new ChangeDataUtils();
        User userCopy = new User();
        if (null != contract.getCreatedBy() && StringUtils.isNotBlank(contract.getCreatedBy().getRecordId()))
        {
            userCopy = contract.getCreatedBy();
        }
        else if (null != contract.getLastUpdBy() && StringUtils.isNotBlank(contract.getLastUpdBy().getRecordId()))
        {
            userCopy = contract.getLastUpdBy();
        }
        User us = userDao.get(userCopy);
        if (null == us)
        {
            us = UserUtils.getUser();
        }
        changeDataUtils.genConrtactData(contract.getRecordId(), company, us);
    }

    @Transactional(readOnly = false)
    public void initMaterialSpecificationRelation()
    {
        List<Material> materialList = materialDao.getInitMaterialSpecificationRelationList();
        if(Collections3.isNotEmpty(materialList))
        {
            for(Material material : materialList)
            {
                Material mat = new Material();
                mat.setCompanyId(material.getCompanyId());
                mat.setRecordId(material.getRecordId());

                // 获取仓库是否存在
                Integer count = storeHouseDao.checkNameCount(mat);
                if(null == count || count == 0)
                {
                    storeHouseDao.saveByMaterial(mat);
                }
                mat.setFromComId(material.getFromComId());
                mat.preInsert();
                materialDao.cleanMaterialSpeRe(material.getMaterialId());
                materialDao.copyMaterialSpeRe(mat);
            }
        }
    }

    @Transactional(readOnly = false)
    public void resetPurchasingDetailData()
    {
        PurchasingDetail purchasingDetail = new PurchasingDetail();
        purchasingDetail.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        //依据采购单明细数据
        List<PurchasingDetail> purchasingDetailList = purchasingDetailDao.getPurchasingDetailList(purchasingDetail);
        //去重物料Id
        Map<String,String> map = new HashMap<>();
        List<String> matIdList = Lists.newArrayList();
        for(PurchasingDetail detail : purchasingDetailList)
        {
            if(null == detail || null == detail.getMaterial() || StringUtils.isBlank(detail.getMaterial().getRecordId()))
            {
                continue;
            }
            if(map.containsKey(detail.getMaterial().getRecordId()))
            {
                continue;
            }
            matIdList.add(detail.getMaterial().getRecordId());
            map.put(detail.getMaterial().getRecordId(),detail.getMaterial().getRecordId());
        }
        //获取出入库记录集合
        List<RawmaterialStock> rawmaterialStockList = rawmaterialStockDao.getRawmaterialStockListT(matIdList,purchasingDetailList.get(0).getCreatedDate());
        // 拆分出入库记录
        if(Collections3.isNotEmpty(rawmaterialStockList))
        {
            for(PurchasingDetail detail : purchasingDetailList)
            {
                detail.setRawmaterialStockList(Lists.newArrayList());
                for(int i = 0; i < rawmaterialStockList.size();i++)
                {
                    RawmaterialStock rs = rawmaterialStockList.get(i);
                    if(detail.getMaterial().getRecordId().equals(rs.getMaterialId()) && rs.getOperateDate().compareTo(detail.getCreatedDate()) > 0)
                    {
                        detail.getRawmaterialStockList().add(rs);
                    }
                }
                System.out.println(detail.getRawmaterialStockList().size());
            }
        }

        for(PurchasingDetail detail : purchasingDetailList)
        {
            if(null == detail || null == detail.getMaterial() || StringUtils.isBlank(detail.getMaterial().getRecordId()))
            {
                continue;
            }
            // 获取采购单库存
            BigDecimal totalQuantity = BigDecimal.ZERO;
            for(RawmaterialStock rs : detail.getRawmaterialStockList())
            {
                // 采购入库
                if (rs.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_IN && detail.getRecordId().equals(rs.getPurchasingDetaillId()))
                {
                    // 出入库数量+赠送数量
                    totalQuantity = totalQuantity.add(rs.getQuantity());
                    if(null != rs.getGiveNum())
                    {
                        totalQuantity = totalQuantity.add(rs.getGiveNum());
                    }
                }
                // 退货出库
                else if (rs.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_OUT && detail.getRecordId().equals(rs.getPurchasingDetaillId()))
                {
                    totalQuantity = totalQuantity.subtract(rs.getQuantity());
                }
                // 补货入库
                else if (rs.getInoutType()
                        == TypeKey.ST_MATERIAL_INOUTTYPE_PURCHASING_RETURN_REDELIVER && detail.getRecordId().equals(rs.getPurchasingDetaillId()))
                {
                    totalQuantity = totalQuantity.add(rs.getQuantity());
                }
            }
            if(totalQuantity.compareTo(BigDecimal.ZERO) > 0)
            {
                for(RawmaterialStock rs : detail.getRawmaterialStockList())
                {
                    // 投料出库、领料出库、送货出库
                    if(rs.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_OUT || rs.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_FEEDING_OUT
                    || rs.getInoutType() == TypeKey.ST_MATERIAL_INOUTTYPE_SENTOUT )
                    {
                        if(null == rs.getStatusFlag() || !rs.getStatusFlag())
                        {
                            if(totalQuantity.compareTo(rs.getQuantity()) >= 0)
                            {
                                totalQuantity = totalQuantity.subtract(rs.getQuantity());
                                rs.setStatusFlag(true);
                            }
                            else
                            {
                                totalQuantity = BigDecimal.ZERO;
                                rs.setQuantity(rs.getQuantity().subtract(totalQuantity));
                                rs.setStatusFlag(false);
                            }
                        }
                    }
                    if(totalQuantity.compareTo(BigDecimal.ZERO) <= 0)
                    {
                        totalQuantity = BigDecimal.ZERO;
                        break;
                    }
                }

            }
            //如果剩余库存数量大于采购数量，以采购数量为准
            if (totalQuantity.compareTo(BigDecimal.ZERO) > 0 && totalQuantity.compareTo(detail.getQuantity()) > 0)
            {
                totalQuantity = detail.getQuantity();
            }
            //赋值剩余库存
            detail.setSurplusStock(totalQuantity);
        }
        //批量更新采购明细剩余库存
        purchasingDetailDao.updateSurplusStockDate(purchasingDetailList);
    }

    // 成品尾数盘点处理
    @Transactional(readOnly = false)
    public void handleFinishedMantissa()
    {
        Company company = UserUtils.getUser().getCompany();

        // 获取导入的test表中的成品尾数盘点数据
        List<Material> matList = initDataDao.getTestList();
        if(Collections3.isEmpty(matList))
        {
            return;
        }
        // 按厂编分类库存
        Map<String,BigDecimal> mapData = new HashMap<>();
        for(Material mat : matList)
        {
            BigDecimal stocks = null == mat.getStocks() ? BigDecimal.ZERO : mat.getStocks();
            if(mapData.containsKey(mat.getRecordId()))
            {
                stocks = stocks.add(mapData.get(mat.getRecordId()));
            }
            mapData.put(mat.getRecordId(),stocks);
        }

        // 获取当前尾数列表
        List<InventoryMantissa> mantissaList = initDataDao.getInventoryMantissaList(company);

        // 入库list
        List<InventoryMantissa> insertList = Lists.newArrayList();
        // 入库更新list
        List<InventoryMantissa> updateList = Lists.newArrayList();

        // 出库list
        List<InventoryMantissa> mantissaStoreList = Lists.newArrayList();

        if(Collections3.isNotEmpty(mantissaList))
        {
            // 累计系统尾数库存
            Map<String,BigDecimal> map = new HashMap<>();
            for(InventoryMantissa mantissa : mantissaList)
            {
                BigDecimal storageQuantity = null == mantissa.getStorageQuantity() ? BigDecimal.ZERO : mantissa.getStorageQuantity(); // 入库数量
                BigDecimal shippingQty = null == mantissa.getShippingQty() ? BigDecimal.ZERO : mantissa.getShippingQty(); // 出库数量
                BigDecimal stocks = storageQuantity.subtract(shippingQty);
                if(stocks.compareTo(BigDecimal.ZERO) <= 0)
                {
                    stocks = BigDecimal.ZERO;
                }
                if(map.containsKey(mantissa.getNumberId()))
                {
                    stocks = stocks.add(map.get(mantissa.getNumberId()));
                }
                map.put(mantissa.getNumberId(),stocks);
                mantissa.setStorageQuantity(storageQuantity);
                mantissa.setShippingQty(shippingQty);
            }

            for(Map.Entry<String, BigDecimal> entry : mapData.entrySet())
            {
                // 导入的
                String id = entry.getKey();
                BigDecimal stocks = entry.getValue();

                //系统的
                BigDecimal systemStocks = map.get(id);
                if(null == systemStocks)
                {
                    systemStocks = BigDecimal.ZERO;
                }

                if(stocks.compareTo(systemStocks) > 0)
                {
                    // 加差值入库记录
                    BigDecimal difference = stocks.subtract(systemStocks);
                    InventoryMantissa inventoryMantissa = new InventoryMantissa();
                    inventoryMantissa.setNumberId(id);
                    inventoryMantissa.setStorageQuantity(difference);
                    inventoryMantissa.setCompany(company);
                    inventoryMantissa.setInputOutputType(1);
                    inventoryMantissa.preInsert();
                    insertList.add(inventoryMantissa);
                }
                else if(stocks.compareTo(systemStocks) < 0)
                {
                    // 减出库记录
                    BigDecimal difference = systemStocks.subtract(stocks);
                    for (InventoryMantissa inventoryMantissa : mantissaList)
                    {
                        BigDecimal stocksTwo = inventoryMantissa.getStorageQuantity().subtract(inventoryMantissa.getShippingQty());
                        if (stocksTwo.compareTo(BigDecimal.ZERO) <= 0)
                        {
                            continue;
                        }
                        BigDecimal cumQuantity = BigDecimal.ZERO;
                        if (inventoryMantissa.getNumberId().equals(id))
                        {
                            if (difference.compareTo(stocksTwo) >= 0)
                            {
                                cumQuantity = stocksTwo;
                                stocksTwo = BigDecimal.ZERO;
                                difference = difference.subtract(stocksTwo);
                            }
                            else
                            {
                                cumQuantity = difference;
                                difference = BigDecimal.ZERO;
                                stocksTwo = stocksTwo.subtract(cumQuantity);
                            }
                            InventoryMantissa mantissa = new InventoryMantissa();
                            mantissa.setNumberId(id);
                            mantissa.setShippingQty(cumQuantity);
                            mantissa.setInputOutputType(2);
                            mantissa.setCompany(company);
                            mantissa.setRemark("库存盘点");
                            mantissa.setRecordId(inventoryMantissa.getRecordId());
                            mantissa.preInsert();
                            mantissaStoreList.add(mantissa);

                            InventoryMantissa update = new InventoryMantissa();
                            update.setRecordId(inventoryMantissa.getRecordId());
                            // 入库数量 - 结余数量 = 出库数量
                            update.setShippingQty(inventoryMantissa.getStorageQuantity().subtract(stocksTwo));
                            updateList.add(update);

                            if (difference.compareTo(BigDecimal.ZERO) <= 0)
                            {
                                break;
                            }
                        }
                    }
                }
                else
                {
                    continue;
                }
            }
            if (Collections3.isNotEmpty(insertList))
            {
                productStoreDao.batchInsertMantissa(insertList);
            }
            if (Collections3.isNotEmpty(mantissaStoreList))
            {
                productStoreDao.batchInsertMantissaStore(mantissaStoreList);
            }
            if(Collections3.isNotEmpty(updateList))
            {
                productStoreDao.batchUpdateShippingQty(mantissaStoreList);
            }
        }
    }

    @Transactional(readOnly = false)
    public void resetPurchasingDetailTwoData(String startDate,String companyId)
    {
        if(StringUtils.isBlank(companyId))
        {
            return;
        }
        // 清除所有采购入库记录剩余库存
        rawmaterialStockDao.cleanSurplusStock();

        Company company = new Company(companyId);
        List<RawmaterialStock> updateList = Lists.newArrayList();

        // 获取非采购入库记录
        Map<String,BigDecimal> matMap = new HashMap<>();
        List<RawmaterialStock> deliveryStorageList = rawmaterialStockDao.getDeliveryStorageList(companyId,startDate);
        if(Collections3.isNotEmpty(deliveryStorageList))
        {
            for(RawmaterialStock stock : deliveryStorageList)
            {
                String key = stock.getMaterialId();
                BigDecimal outboundQuantity = BigDecimal.ZERO; // 出库总数
                if(matMap.containsKey(key))
                {
                    outboundQuantity = matMap.get(key);
                }
                // 出库
                if(TypeKey.ST_MATERIAL_INOUTTYPE_FEEDING_OUT.compareTo(stock.getInoutType()) == 0 || TypeKey.ST_MATERIAL_INOUTTYPE_FEDING_OUT.compareTo(stock.getInoutType()) == 0
                        || TypeKey.ST_MATERIAL_INOUTTYPE_SENTOUT.compareTo(stock.getInoutType()) == 0 || TypeKey.ST_MATERIAL_INOUTTYPE_OUT.compareTo(stock.getInoutType()) == 0)
                {
                    outboundQuantity = outboundQuantity.add(null == stock.getQuantity() ? BigDecimal.ZERO : stock.getQuantity());
                }
                matMap.put(key,outboundQuantity);
            }
        }

        // 获取结余
        RawmaterialStock rawmaterialStock = new RawmaterialStock();
        rawmaterialStock.setInoutTimeStartQr(new Date());
        rawmaterialStock.setInoutTimeEndQr(new Date());
        rawmaterialStock.setCompany(company);
        MaterialTimeUtils materialTimeUtils = new MaterialTimeUtils();
        List<RawmaterialStock> materialLlist =  materialTimeUtils.dealAllPlace(rawmaterialStock,company);

        if(Collections3.isNotEmpty(materialLlist))
        {
            for(RawmaterialStock stock : materialLlist)
            {
                BigDecimal quantity = null == stock.getQuantity() ? BigDecimal.ZERO : stock.getQuantity();
                if(matMap.containsKey(stock.getRecordId()))
                {
                    quantity = quantity.add(matMap.get(stock.getRecordId()));
                }
                matMap.put(stock.getRecordId(),quantity);
            }
        }

        // 转换为物料集合
        List<Material> materialList = Lists.newArrayList();
        if(null != matMap)
        {
            for (Map.Entry<String, BigDecimal> entry : matMap.entrySet())
            {
                if (entry != null && entry.getValue() != null)
                {
                    Material mat = new Material();
                    mat.setRecordId(entry.getKey());
                    mat.setStocks(entry.getValue());
                    materialList.add(mat);
                }
            }
        }

        // 获取采购入库记录
        List<RawmaterialStock> warehousingList = rawmaterialStockDao.getWarehousingList(companyId,null);
        if(Collections3.isNotEmpty(materialList))
        {
            for(Material mat : materialList)
            {
                if(null == mat.getStocks() || mat.getStocks().compareTo(BigDecimal.ZERO) < 0)
                {
                    mat.setStocks(BigDecimal.ZERO);
                }
                BigDecimal sumQty = mat.getStocks();
                if(Collections3.isNotEmpty(warehousingList))
                {
                    for(RawmaterialStock warehousing : warehousingList)
                    {
                        if(warehousing.getMaterialId().equals(mat.getRecordId()))
                        {
                            BigDecimal computQty = BigDecimal.ZERO;
                            if(sumQty.compareTo(warehousing.getQuantity()) > 0)
                            {
                                computQty = warehousing.getQuantity();
                                sumQty = sumQty.subtract(warehousing.getQuantity());
                            }
                            else
                            {
                                computQty = sumQty;
                                sumQty = BigDecimal.ZERO;
                            }
                            if(computQty.compareTo(BigDecimal.ZERO) > 0)
                            {
                                warehousing.setSurplusStock(computQty);
                                updateList.add(warehousing);
                            }
                            if(sumQty.compareTo(BigDecimal.ZERO) <= 0)
                            {
                                break;
                            }
                        }
                    }
                }
            }
        }
        if(Collections3.isNotEmpty(updateList))
        {
            rawmaterialStockDao.batchUpdateSurplusStock(updateList);
            updateList = Lists.newArrayList();
        }
        // 清除所有先进先出记录
        rawmaterialStockDao.cleanFirstInFirstOut();

        // 获取剩余库存大于0的采购入口记录
        warehousingList = rawmaterialStockDao.getWarehousingList(companyId,1);

        // 增加先进先出记录
        List<RawmaterialStock> insertList = Lists.newArrayList();
        if(Collections3.isNotEmpty(deliveryStorageList))
        {
            for(int i = 0; i< deliveryStorageList.size(); i++)
            {
                RawmaterialStock deliveryStorage = deliveryStorageList.get(i);
                if(!(TypeKey.ST_MATERIAL_INOUTTYPE_FEEDING_OUT.compareTo(deliveryStorage.getInoutType()) == 0 || TypeKey.ST_MATERIAL_INOUTTYPE_FEDING_OUT.compareTo(deliveryStorage.getInoutType()) == 0
                        || TypeKey.ST_MATERIAL_INOUTTYPE_SENTOUT.compareTo(deliveryStorage.getInoutType()) == 0 || TypeKey.ST_MATERIAL_INOUTTYPE_OUT.compareTo(deliveryStorage.getInoutType()) == 0))
                {
                    continue;
                }
                BigDecimal quantity = deliveryStorage.getQuantity();
                for(RawmaterialStock warehousing : warehousingList)
                {
                    if(deliveryStorage.getMaterialId().equals(warehousing.getMaterialId()))
                    {
                        BigDecimal computQty = BigDecimal.ZERO;
                        BigDecimal surplusStock = BigDecimal.ZERO;
                        if(quantity.compareTo(warehousing.getSurplusStock()) > 0)
                        {
                            computQty = warehousing.getSurplusStock();
                            quantity = quantity.subtract(warehousing.getSurplusStock());
                        }
                        else
                        {
                            computQty = quantity;
                            quantity = BigDecimal.ZERO;
                            surplusStock = warehousing.getSurplusStock().subtract(computQty);
                        }
                         if(computQty.compareTo(BigDecimal.ZERO) > 0)
                        {
                            RawmaterialStock raw = new RawmaterialStock();
                            raw.setCompanyId(deliveryStorage.getCompanyId());
                            raw.setRawMaterialId(deliveryStorage.getRecordId());
                            raw.setPurchasingInId(warehousing.getRecordId());
                            raw.setOutboundQuantity(computQty);
                            insertList.add(raw);
                        }
                        Boolean flag = true;
                        if(Collections3.isNotEmpty(updateList))
                        {
                            for(RawmaterialStock update : updateList)
                            {
                                if(update.getRecordId().equals(warehousing.getRecordId()))
                                {
                                    update.setSurplusStock(surplusStock);
                                    flag = false;
                                    break;
                                }
                            }
                        }
                        if(flag)
                        {
                            warehousing.setSurplusStock(surplusStock);
                            updateList.add(warehousing);
                        }
                        if(quantity.compareTo(BigDecimal.ZERO) <= 0)
                        {
                            break;
                        }
                    }
                }
            }
        }
        if(Collections3.isNotEmpty(insertList))
        {
            rawmaterialStockDao.batchInsetFirstInFirstOut(insertList);
        }
        if(Collections3.isNotEmpty(updateList))
        {
            rawmaterialStockDao.batchUpdateSurplusStock(updateList);
        }
    }

    // 重置供应链订单账单
    @Transactional(readOnly = false)
    public void inisSupplierChainAccounts()
    {
        List<RawmaterialStock> dataList = initDataDao.getTestTwoList();
        if(Collections3.isEmpty(dataList))
        {
            return;
        }
        for(RawmaterialStock raw : dataList)
        {
            rawInoutService.sentPurSupplyChainData(
                null == raw.getPurchasingDtlId() ? "" : raw.getPurchasingDtlId().toString(),
                null == raw.getQuantity() ? 0 : raw.getQuantity().intValue(),
                raw.getCheckOrgId(),
                raw.getOperateDate());
        }
    }

    // 生成未回款逾期扣款
    @Transactional(readOnly = false)
    public void sentOverdueDeductions()
    {
        try {
            // 获取所有公司未回款逾期扣款设置
            RoyaltyRate query = new RoyaltyRate();
            query.setValue("未回款逾期扣款");
            List<RoyaltyRate> royaltyRateList = royaltyRateDao.getOverdueRoyaltyList(query);
            if(Collections3.isEmpty(royaltyRateList))
            {
                return;
            }
            // 获取存在的未回款扣款记录
            List<OverdueDeductions> overdueDeductionsList = overdueDeductionsDao.getList(new OverdueDeductions());

            // 获取每个集团部门的成本记录
            List<GroupOrgCost> groupOrgCostList = groupOrgCostDao.getCostList(new GroupOrgCost());

            // 获取每个公司最近的未回款逾期扣款设置
            List<RoyaltyRate> rrList = Lists.newArrayList();
            Map<String,String> companyIdMap = new HashMap<>();
            for(RoyaltyRate royaltyRate : royaltyRateList)
            {
                if(companyIdMap.containsKey(royaltyRate.getCompanyId()))
                {
                    continue;
                }
                List<RoyaltyRateDetail> detailList = royaltyRateDao.getDetailList(royaltyRate);
                royaltyRate.setDetailList(detailList);
                List<RoyaltyRateAscription> ascriptionList = royaltyRateDao.getAscriptionList(royaltyRate);
                royaltyRate.setAscriptionList(ascriptionList);
                rrList.add(royaltyRate);
                companyIdMap.put(royaltyRate.getCompanyId(),royaltyRate.getCompanyId());
            }

            // 获取集团部门每个月的销售额
            List<GroupOrgRelation> groupOrgList = departmentDao.getGroupOrgSaleAmount();
/*            String dateStr = "2025-03-31";
            Date overdueDate = DateUtils.parseDate(dateStr);*/
            Date overdueDate = new Date();
            List<OverdueDeductions> insertList = Lists.newArrayList();
            for(RoyaltyRate royaltyRate : rrList)
            {
                // 获取应收单明细
                CollectMoney cm = new CollectMoney();
                cm.setCompanyId(royaltyRate.getCompanyId());
                cm.setSentTimeStartQr(royaltyRate.getStartTime());
                cm.setSentTimeEndQr(royaltyRate.getEndTime());
                List<CollectMuchMoney> collectMuchMoneyList = collectMoneyDao.getSingleDetailList(cm);
                // 设置下级部门
                cm.setPosition("业务员");
                cm.setFlag(1);
                collectMuchMoneyList = reportService.setChildDeptList(collectMuchMoneyList,cm);

                // 获取公司分配的客户前3个月的金额数据
                CustomerSalesman cusSalseMan = new CustomerSalesman();
                cusSalseMan.setCompany(royaltyRate.getCompany());
                List<CustomerSalesman> cusSalesManList = customerSalesmanDao.getCusRoyaltyData(cusSalseMan);

                if(Collections3.isNotEmpty(collectMuchMoneyList))
                {
                    Map<String, CollectMuchMoney> map = new HashMap<>();
                    for(CollectMuchMoney money : collectMuchMoneyList)
                    {
                        String custType = StringUtils.isBlank(money.getCustType()) ? "2" : money.getCustType();
                        String name = money.getShortName() + custType + money.getPeriod();
                        // 退货
                        if ("3".equals(money.getType()) && null != money.getAmount())
                        {
                            money.setAmount(money.getAmount().multiply(new BigDecimal(-1)));
                        }
                        if (map.containsKey(name))
                        {
                            CollectMuchMoney collectMuchMoney = map.get(name);
                            if (collectMuchMoney == null)
                            {
                                map.put(name, money);
                                continue;
                            }
                            if (StringUtils.isNotBlank(money.getContractDetailId()))
                            {
                                if(!collectMuchMoney.getContractDetailId().contains(money.getContractDetailId()))
                                {
                                    if (StringUtils.isNotBlank(collectMuchMoney.getContractDetailId()))
                                    {
                                        collectMuchMoney.setContractDetailId(
                                                collectMuchMoney.getContractDetailId() + "," + money.getContractDetailId());
                                    }
                                    else
                                    {
                                        collectMuchMoney.setContractDetailId(money.getContractDetailId());
                                    }
                                }
                            }
                            // 回款金额
                            BigDecimal amount = money.getAmount() == null ? BigDecimal.ZERO : money.getAmount();
                            BigDecimal amountAdd =
                                    collectMuchMoney.getAmount() == null ? BigDecimal.ZERO : collectMuchMoney.getAmount();
                            collectMuchMoney.setAmount(amount.add(amountAdd));
                            map.put(name, collectMuchMoney);
                        }
                        else
                        {
                            map.put(name, money);
                        }
                    }

                    Map<String,BigDecimal> adjustMap = new HashMap<>();
                    List<CollectMuchMoney> list = new ArrayList<>();
                    for (Map.Entry<String, CollectMuchMoney> entry : map.entrySet())
                    {
                        if (entry != null && entry.getValue() != null)
                        {
                            CollectMuchMoney money = entry.getValue();
                            if(null == money.getAmount() || money.getAmount().compareTo(BigDecimal.ZERO) <= 0)
                            {
                                continue;
                            }
                            String goodsCheckId = goodsCheckDao.getIdsByCustomerPeriod(money);
                            if(StringUtils.isBlank(goodsCheckId))
                            {
                                continue;
                            }
                            money.setGoodCheckId(goodsCheckId);

                            String key = money.getNo() + "-"+ money.getPeriod();
                            if(null == money.getAmount())
                            {
                                money.setAmount(BigDecimal.ZERO);
                            }
                            // 前3个月的30%销售金额
                            if(Collections3.isNotEmpty(cusSalesManList))
                            {
                                for(CustomerSalesman cusSaleMan : cusSalesManList)
                                {
                                    if(StringUtils.isNotBlank(money.getFinalId()))
                                    {
                                        if(StringUtils.isNotBlank(cusSaleMan.getFinalId()) && cusSaleMan.getFinalId().equals(money.getFinalId())
                                                && cusSaleMan.getUserId().equals(money.getUserId()))
                                        {
                                            money.setFirstAmount(cusSaleMan.getTotalAmt());
                                            break;
                                        }
                                    }
                                    else
                                    {
                                        if(null != money.getCustomer() && cusSaleMan.getCustomerId().equals(money.getCustomer().getRecordId())
                                                && cusSaleMan.getUserId().equals(money.getUserId()))
                                        {
                                            money.setFirstAmount(cusSaleMan.getTotalAmt());
                                            break;
                                        }
                                    }
                                }
                            }
                            // 部门的销售金额
                            if(Collections3.isNotEmpty(groupOrgList))
                            {
                                for(GroupOrgRelation groupOrgRelation : groupOrgList)
                                {
                                    Boolean flag = false;
                                    if(null != money.getPeriod() && groupOrgRelation.getPeriod().compareTo(money.getPeriod()) == 0)
                                    {
                                        if(StringUtils.isNotBlank(money.getDeptId()))
                                        {
                                            String[] deptIds = money.getDeptId().split(",");
                                            for(String detpId : deptIds)
                                            {
                                                if(groupOrgRelation.getDeptId().contains(detpId))
                                                {
                                                    money.setDeptAmount(groupOrgRelation.getTotalAmt());
                                                    money.setDeptType(groupOrgRelation.getType());
                                                    flag = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                    if(flag)
                                    {
                                        break;
                                    }
                                }
                            }
                            if(Collections3.isNotEmpty(groupOrgCostList))
                            {
                                                              for(GroupOrgCost cost : groupOrgCostList)
                                {
                                    if(money.getGroupOrgId().equals(cost.getGroupOrgId()) && money.getPeriod().compareTo(cost.getPeriod()) == 0)
                                    {
                                        money.setDeptAmount(cost.getReconcilingAmount());
                                        money.setDeptCondeailMoney(cost.getReconcilingAmount());
                                        money.setDeptPrdorderMoney(cost.getPrdorderMoney());
                                        money.setDeptSaleFee(cost.getSaleFee());
                                        money.setDeptBarrelBoltFee(cost.getBarrelBoltFee());
                                        money.setDeptManageFee(cost.getManageFee());
                                        break;
                                    }
                                }
                            }

                            BigDecimal prdorderMoney = collectMoneyDao.getSumPrdMoney(money);

                            // 合同明细金额(对账金额)
                            BigDecimal condeailMoney =
                                    money.getReconcilingAmount() == null ? BigDecimal.ZERO : money.getReconcilingAmount();
                            // 采购明细金额
                            if(null == prdorderMoney)
                            {
                                prdorderMoney = BigDecimal.ZERO;
                            }
                            // 业务费率
                            BigDecimal salePrecent = money.getSalePrecent() == null ? BigDecimal.ZERO : money.getSalePrecent();

                            // 业务费
                            BigDecimal saleFee =
                                    condeailMoney.multiply(salePrecent).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
                            money.setCondeailMoney(condeailMoney);
                            money.setPrdorderMoney(prdorderMoney);
                            money.setComputAmount(condeailMoney);
                            money.setSaleFee(saleFee);

                            // 管销费 = 对账金额 * 千分之八
                            BigDecimal barrelBoltFee = condeailMoney.multiply(new BigDecimal(8)).divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_UP);
                            money.setBarrelBoltFee(barrelBoltFee);

                            // 管理费 = 对账金额 * 千分之15
                            BigDecimal manageFee = condeailMoney.multiply(new BigDecimal(15)).divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_UP);
                            money.setManageFee(manageFee);

                            list.add(money);
                        }
                    }

                    if(Collections3.isNotEmpty(list))
                    {
                        // 计算出提成
                        CollectMoney collectMoney = new CollectMoney();
                        collectMoney.setCompany(royaltyRate.getCompany());
                        collectMoney.setPosition(royaltyRate.getPositionName());
                        reportService.profitSetUp(list,1,collectMoney);

                        for(CollectMuchMoney money : list)
                        {
                            if(null == money || null == money.getRoyalty() || money.getRoyalty().compareTo(BigDecimal.ZERO) <= 0)
                            {
                                continue;
                            }
                            if(money.getDifferenceMonth() <= 0)
                            {
                                continue;
                            }
                            OverdueDeductions overdueDeductions = new OverdueDeductions();
                            overdueDeductions.setCompanyId(royaltyRate.getCompanyId());
                            overdueDeductions.setUserId(money.getUserId());
                            overdueDeductions.setCustomerId(money.getCustomer().getRecordId());
                            overdueDeductions.setGroupOrgId(money.getGroupOrgId());
                            overdueDeductions.setRoyaltyRateId(royaltyRate.getRecordId());
                            overdueDeductions.setPeriod(money.getPeriod());

                            overdueDeductions.setGrossProfit(money.getGrossProfit()); // 净利率
                            overdueDeductions.setGrossProfitMonth(money.getGrossProfitMonth());
                            overdueDeductions.setPolicy(money.getPolicy()); // 政策
                            overdueDeductions.setRoyaltyRate(money.getRoyaltyRate()); // 提成率
                            overdueDeductions.setReconcilingAmount(money.getReconcilingAmount()); // 对账金额
                            overdueDeductions.setPrdorderMoney(money.getPrdorderMoney()); // 成本费用
                            overdueDeductions.setSaleFee(money.getSaleFee()); // 业务费用
                            overdueDeductions.setBarrelBoltFee(money.getBarrelBoltFee()); // 管销费用
                            overdueDeductions.setManageFee(money.getManageFee()); // 管理费
                            overdueDeductions.setProfits(money.getProfits()); // 利润

                            overdueDeductions.setOverdueDate(overdueDate);
                            overdueDeductions.setAmount(money.getAmount());
                            overdueDeductions.setRateAmount(money.getRoyalty());

                            BigDecimal rate = BigDecimal.ZERO;
                            for(RoyaltyRateDetail rateDetail : royaltyRate.getDetailList())
                            {
                                if(StringUtils.isBlank(rateDetail.getConditionFlag()) || null == money.getDifferenceMonth())
                                {
                                    continue;
                                }
                                BigDecimal conditionValue = BigDecimal.ZERO;
                                if(StringUtils.isNotBlank(rateDetail.getConditionValue()))
                                {
                                    conditionValue = new BigDecimal(rateDetail.getConditionValue());
                                }
                                BigDecimal differenceMonth = new BigDecimal(money.getDifferenceMonth());
                                switch (rateDetail.getConditionFlag())
                                {
                                    // 1大于，2小于，3等于，4大于等于，5小于等于，6无
                                    case "1":
                                        if(differenceMonth.compareTo(conditionValue) > 0)
                                        {
                                            rate = rateDetail.getRate();
                                        }
                                        break;
                                    case "2":
                                        if(differenceMonth.compareTo(conditionValue) < 0)
                                        {
                                            rate = rateDetail.getRate();
                                        }
                                        break;
                                    case "3":
                                        if(differenceMonth.compareTo(conditionValue) == 0)
                                        {
                                            rate = rateDetail.getRate();
                                        }
                                        break;
                                    case "4":
                                        if(differenceMonth.compareTo(conditionValue) >= 0)
                                        {
                                            rate = rateDetail.getRate();
                                        }
                                        break;
                                    case "5":
                                        if(differenceMonth.compareTo(conditionValue) <= 0)
                                        {
                                            rate = rateDetail.getRate();
                                        }
                                        break;
                                    case "6":
                                        rate = rateDetail.getRate();
                                        break;
                                }
                            }
                            overdueDeductions.setDeductionRate(rate);
                            Boolean flag = true;
                            if(Collections3.isNotEmpty(overdueDeductionsList))
                            {
                                for(OverdueDeductions obj : overdueDeductionsList)
                                {
                                    if(obj.getCompanyId().equals(overdueDeductions.getCompanyId()) && obj.getCustomerId().equals(overdueDeductions.getCustomerId())
                                            && obj.getPeriod().compareTo(overdueDeductions.getPeriod()) == 0 && obj.getDeductionRate().compareTo(overdueDeductions.getDeductionRate()) == 0)
                                    {
                                        flag = false;
                                        break;
                                    }
                                }
                            }
                            if(flag)
                            {
                                BigDecimal deductionAmount = money.getRoyalty().multiply(rate).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
                                if(deductionAmount.compareTo(BigDecimal.ZERO) >= 0)
                                {
                                    overdueDeductions.setDeductionAmount(deductionAmount);
                                    overdueDeductions.setStatus(1001);
                                    insertList.add(overdueDeductions);
                                }
                            }
                        }
                    }
                }
            }
            if(Collections3.isNotEmpty(insertList))
            {
                overdueDeductionsDao.batchInsert(insertList);
            }
        }
        catch (Exception ex)
        {
            System.out.println("An error occurred: " + ex.getMessage());
        }
    }

    /**
     * zjn 2025-03-14 重置每月集团部门的成本费
     * @param period 账期
     */
    @Transactional(readOnly = false)
    public void initGrouporgCost(Integer period)
    {
        if(null == period)
        {
            Date date = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.MONTH,-1);
            String periodStr = DateUtils.formatDate(calendar.getTime(),"yyyyMM");
            period = Integer.parseInt(periodStr);
        }
        // 清除统计核统计明细
        groupOrgCostDao.cleanCost(period);
        groupOrgCostDao.cleanCostDetail(period);

        List<GroupOrgCost> list = groupOrgCostDao.getDataList(period);
        if(Collections3.isEmpty(list))
        {
            return;
        }
        // 获取大于等于当前账期的客户设置
        List<CustomerSalesman> customerSalesmenList = customerSalesmanDao.getListByPeriod(period);

        Map<String,String> deptMap = new HashMap<>();
        Map<Integer,List<GroupOrgCost>> periodMap = new HashMap<>();
        for(GroupOrgCost data : list)
        {
            if(Collections3.isNotEmpty(customerSalesmenList))
            {
                for(CustomerSalesman cs : customerSalesmenList)
                {
                    if(cs.getCustomerId().equals(data.getCustomerId()))
                    {
                        if(null != cs.getEndPeriod())
                        {
                            if(data.getPeriod() >= cs.getStartPeriod() && data.getPeriod() <= cs.getEndPeriod())
                            {
                                data.setGroupOrgId(cs.getGroupOrgId());
                                break;
                            }
                        }
                        else
                        {
                            if(data.getPeriod() >= cs.getStartPeriod())
                            {
                                data.setGroupOrgId(cs.getGroupOrgId());
                                break;
                            }
                        }
                    }
                }
            }
            String deptKey = data.getGroupOrgId();
            Integer periodKey = data.getPeriod();
            if(!deptMap.containsKey(deptKey))
            {
                deptMap.put(deptKey,deptKey);
            }
            List<GroupOrgCost> periodCostList = Lists.newArrayList();
            if(periodMap.containsKey(periodKey))
            {
               periodCostList = periodMap.get(periodKey);
            }
            periodCostList.add(data);
            periodMap.put(periodKey,periodCostList);
        }
        if(null != deptMap)
        {
            List<GroupOrgCost> deptDataList = Lists.newArrayList();
            for (Map.Entry<String, String> entry : deptMap.entrySet())
            {
                if (entry != null && entry.getValue() != null)
                {
                    String deptValue = entry.getValue();
                    if(null != periodMap)
                    {
                        for (Map.Entry<Integer, List<GroupOrgCost>> entry2 : periodMap.entrySet())
                        {
                            if (entry2 != null && entry2.getValue() != null)
                            {
                                List<GroupOrgCost> costList = entry2.getValue();
                                BigDecimal reconcilingAmount = BigDecimal.ZERO;// 对账金额
                                BigDecimal prdorderMoney = BigDecimal.ZERO;// 成本费用
                                BigDecimal saleFee = BigDecimal.ZERO;// 业务费用
                                BigDecimal barrelBoltFee = BigDecimal.ZERO;// 管销费用
                                BigDecimal manageFee = BigDecimal.ZERO;// 管理费

                                for(GroupOrgCost data : costList)
                                {
                                    if(!deptValue.equals(data.getGroupOrgId()))
                                    {
                                        continue;
                                    }
                                    if(null != data.getReconcilingAmount())
                                    {
                                        reconcilingAmount = reconcilingAmount.add(data.getReconcilingAmount());
                                    }
                                    if(null != data.getPrdorderMoney())
                                    {
                                        prdorderMoney = prdorderMoney.add(data.getPrdorderMoney());
                                    }
                                    if(null != data.getSaleFee())
                                    {
                                        saleFee = saleFee.add(data.getSaleFee());
                                    }
                                    if(null != data.getBarrelBoltFee())
                                    {
                                        barrelBoltFee = barrelBoltFee.add(data.getBarrelBoltFee());
                                    }
                                    if(null != data.getManageFee())
                                    {
                                        manageFee = manageFee.add(data.getManageFee());
                                    }
                                }

                                GroupOrgCost cost = new GroupOrgCost();
                                cost.setGroupOrgId(deptValue);
                                cost.setPeriod(entry2.getKey());
                                cost.setReconcilingAmount(reconcilingAmount);
                                cost.setPrdorderMoney(prdorderMoney);
                                cost.setSaleFee(saleFee);
                                cost.setBarrelBoltFee(barrelBoltFee);
                                cost.setManageFee(manageFee);
                                deptDataList.add(cost);
                            }
                        }
                    }
                }
            }
            if(Collections3.isNotEmpty(deptDataList))
            {
                groupOrgCostDao.batchInsertCost(deptDataList);
            }
        }
        groupOrgCostDao.batchInsertCostDetail(list);
    }
}
