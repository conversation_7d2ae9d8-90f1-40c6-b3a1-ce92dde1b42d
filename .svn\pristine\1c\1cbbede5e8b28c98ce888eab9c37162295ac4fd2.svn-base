package com.kyb.pcberp.modules.contract.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.persistence.DataEntity;

/**
 * WC 2017-10-13 合同明细分批交货变更前后比较
 */
public class BatchDeliveryChangeCompare extends DataEntity<BatchDeliveryChangeCompare>
{
    
    private static final long serialVersionUID = 1L;
    
    private BatchDeliveryChange change;
    
    private String type; // 变更类型：1表示变更前；2表示变更后
    
    private Integer quantity; // 交货数量
    
    private Date deliveryDate;// 交货日期
    
    public BatchDeliveryChange getChange()
    {
        return change;
    }
    
    public void setChange(BatchDeliveryChange change)
    {
        this.change = change;
    }
    
    public String getType()
    {
        return type;
    }
    
    public void setType(String type)
    {
        this.type = type;
    }
    
    public Integer getQuantity()
    {
        return quantity;
    }
    
    public void setQuantity(Integer quantity)
    {
        this.quantity = quantity;
    }
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getDeliveryDate()
    {
        return deliveryDate;
    }
    
    public void setDeliveryDate(Date deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }
    
}
