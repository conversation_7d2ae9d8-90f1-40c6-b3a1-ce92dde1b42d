package com.kyb.pcberp.modules.stock.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.persistence.DataEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class MaterialPlaceCom extends DataEntity<MaterialPlaceCom>
{
    private static final long serialVersionUID = 1L;

    private String companyId;

    private String materPlaceId; // 库位id

    private String materialId; // 物料id

    private String saleCompanyId; // 销售公司id

    private String placeName;

    private String materialName;

    private String materialNo;

    private String materialSpe;

    private String brachName;

    private BigDecimal stocks;

    private Date inveDate;

    private BigDecimal changeStocks; // 滚动库存

    private String storeId;

    private String addStatus;

    private List<Material> materials;

    private String stockPlaceName; // 仓库库位名字

    private List<RawmaterialStock> rawInoutList;

    private BigDecimal occStocks; // 通知单占用库存

    private Integer defaultStatus; // 默认状态

    private List<ProductStore> productInoutList;

    private String materialKind;

    private BigDecimal inStocks;

    private BigDecimal outStocks;

    private String comName;

    private BigDecimal periodStocks;

    private Date initDate;

    private String materialIds;

    private Integer stockAge;

    private Date finaInOutDate;

    private String finalNum;

    private Material material;

    private MaterialPlaceCom matPlaceComIn;

    private MaterialPlaceCom matPlaceComOut;

    private String matPlaceComInId;

    private String matPlaceComOutId;

    private String groupDeptId;

    private Boolean useFlag;

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    public String getMaterPlaceId()
    {
        return materPlaceId;
    }

    public void setMaterPlaceId(String materPlaceId)
    {
        this.materPlaceId = materPlaceId;
    }

    public String getMaterialId()
    {
        return materialId;
    }

    public void setMaterialId(String materialId)
    {
        this.materialId = materialId;
    }

    public String getSaleCompanyId()
    {
        return saleCompanyId;
    }

    public void setSaleCompanyId(String saleCompanyId)
    {
        this.saleCompanyId = saleCompanyId;
    }

    public String getPlaceName()
    {
        return placeName;
    }

    public void setPlaceName(String placeName)
    {
        this.placeName = placeName;
    }

    public String getMaterialName()
    {
        return materialName;
    }

    public void setMaterialName(String materialName)
    {
        this.materialName = materialName;
    }

    public String getMaterialNo()
    {
        return materialNo;
    }

    public void setMaterialNo(String materialNo)
    {
        this.materialNo = materialNo;
    }

    public String getMaterialSpe()
    {
        return materialSpe;
    }

    public void setMaterialSpe(String materialSpe)
    {
        this.materialSpe = materialSpe;
    }

    public String getBrachName()
    {
        return brachName;
    }

    public void setBrachName(String brachName)
    {
        this.brachName = brachName;
    }

    public BigDecimal getStocks()
    {
        return stocks;
    }

    public void setStocks(BigDecimal stocks)
    {
        this.stocks = stocks;
    }

    public Date getInveDate()
    {
        return inveDate;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getInveDateStr()
    {
        return inveDate;
    }

    public void setInveDate(Date inveDate)
    {
        this.inveDate = inveDate;
    }

    public BigDecimal getChangeStocks()
    {
        return changeStocks;
    }

    public void setChangeStocks(BigDecimal changeStocks)
    {
        this.changeStocks = changeStocks;
    }

    public String getStoreId()
    {
        return storeId;
    }

    public void setStoreId(String storeId)
    {
        this.storeId = storeId;
    }

    public String getAddStatus()
    {
        return addStatus;
    }

    public void setAddStatus(String addStatus)
    {
        this.addStatus = addStatus;
    }

    public List<Material> getMaterials()
    {
        return materials;
    }

    public void setMaterials(List<Material> materials)
    {
        this.materials = materials;
    }

    public String getStockPlaceName()
    {
        return stockPlaceName;
    }

    public void setStockPlaceName(String stockPlaceName)
    {
        this.stockPlaceName = stockPlaceName;
    }

    public List<RawmaterialStock> getRawInoutList()
    {
        return rawInoutList;
    }

    public void setRawInoutList(List<RawmaterialStock> rawInoutList)
    {
        this.rawInoutList = rawInoutList;
    }

    public BigDecimal getOccStocks()
    {
        return occStocks;
    }

    public void setOccStocks(BigDecimal occStocks)
    {
        this.occStocks = occStocks;
    }

    public Integer getDefaultStatus()
    {
        return defaultStatus;
    }

    public void setDefaultStatus(Integer defaultStatus)
    {
        this.defaultStatus = defaultStatus;
    }

    public MaterialPlaceCom copyEntity()
    {
        try
        {
            return (MaterialPlaceCom)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return null;
    }

    public List<ProductStore> getProductInoutList()
    {
        return productInoutList;
    }

    public void setProductInoutList(List<ProductStore> productInoutList)
    {
        this.productInoutList = productInoutList;
    }

    public String getMaterialKind()
    {
        return materialKind;
    }

    public void setMaterialKind(String materialKind)
    {
        this.materialKind = materialKind;
    }

    public BigDecimal getInStocks()
    {
        return inStocks;
    }

    public void setInStocks(BigDecimal inStocks)
    {
        this.inStocks = inStocks;
    }

    public BigDecimal getOutStocks()
    {
        return outStocks;
    }

    public void setOutStocks(BigDecimal outStocks)
    {
        this.outStocks = outStocks;
    }

    public String getComName()
    {
        return comName;
    }

    public void setComName(String comName)
    {
        this.comName = comName;
    }

    public BigDecimal getPeriodStocks()
    {
        return periodStocks;
    }

    public void setPeriodStocks(BigDecimal periodStocks)
    {
        this.periodStocks = periodStocks;
    }

    public Date getInitDate()
    {
        return initDate;
    }

    public void setInitDate(Date initDate)
    {
        this.initDate = initDate;
    }

    public String getMaterialIds()
    {
        return materialIds;
    }

    public void setMaterialIds(String materialIds)
    {
        this.materialIds = materialIds;
    }

    public Integer getStockAge()
    {
        return stockAge;
    }

    public void setStockAge(Integer stockAge)
    {
        this.stockAge = stockAge;
    }

    public Date getFinaInOutDate()
    {
        return finaInOutDate;
    }

    public void setFinaInOutDate(Date finaInOutDate)
    {
        this.finaInOutDate = finaInOutDate;
    }

    public String getFinalNum()
    {
        return finalNum;
    }

    public void setFinalNum(String finalNum)
    {
        this.finalNum = finalNum;
    }

    public Material getMaterial()
    {
        return material;
    }

    public void setMaterial(Material material)
    {
        this.material = material;
    }

    public MaterialPlaceCom getMatPlaceComIn()
    {
        return matPlaceComIn;
    }

    public void setMatPlaceComIn(MaterialPlaceCom matPlaceComIn)
    {
        this.matPlaceComIn = matPlaceComIn;
    }

    public MaterialPlaceCom getMatPlaceComOut()
    {
        return matPlaceComOut;
    }

    public void setMatPlaceComOut(MaterialPlaceCom matPlaceComOut)
    {
        this.matPlaceComOut = matPlaceComOut;
    }

    public String getMatPlaceComInId()
    {
        return matPlaceComInId;
    }

    public void setMatPlaceComInId(String matPlaceComInId)
    {
        this.matPlaceComInId = matPlaceComInId;
    }

    public String getMatPlaceComOutId()
    {
        return matPlaceComOutId;
    }

    public void setMatPlaceComOutId(String matPlaceComOutId)
    {
        this.matPlaceComOutId = matPlaceComOutId;
    }

    public String getGroupDeptId()
    {
        return groupDeptId;
    }

    public void setGroupDeptId(String groupDeptId)
    {
        this.groupDeptId = groupDeptId;
    }

    public Boolean getUseFlag()
    {
        return useFlag;
    }

    public void setUseFlag(Boolean useFlag)
    {
        this.useFlag = useFlag;
    }
}