package com.kyb.pcberp.modules.sys.web;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.collect.Maps;
import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.sys.entity.MessageMoudle;
import com.kyb.pcberp.modules.sys.entity.ParentMessage;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.service.ParentMessageService;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

@Controller
@RequestMapping(value = "${adminPath}/sys/message")
public class ParentMessageController extends BaseController
{
    @Autowired
    private ParentMessageService parentMessageService;
    
    @RequestMapping(value = "list")
    public String list(ParentMessage parentMessage)
    {
        return "modules/sys/message";
    }
    
    /**
     * 保存消息
     */
    @RequestMapping(value = "saveMsg", method = {RequestMethod.POST})
    @ResponseBody
    public String saveMsg(@RequestBody String msg)
    {
        String message = UserUtils.getUser().getUserName() + "删除了" + msg + "的工序";
        ParentMessage parentMessage = new ParentMessage();
        parentMessage.setMessaeDeail(message);
        parentMessage.setCompany(UserUtils.getUser().getCompany());
        parentMessageService.save(parentMessage);
        return "success";
    }
    
    /**
     * 查询所有的消息
     */
    @RequestMapping(value = "page", method = {RequestMethod.POST})
    @ResponseBody
    public Page<ParentMessage> getList(@RequestBody ParentMessage parentMessage, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        parentMessage.setCompany(user.getCompany());
        parentMessage.setCreatedBy(user);
        Page<ParentMessage> qpage = new Page<ParentMessage>(request, response);
        if (StringUtils.isNotBlank(parentMessage.getPageNo()) && StringUtils.isNotBlank(parentMessage.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(parentMessage.getPageNo()));
            qpage.setPageSize(Integer.parseInt(parentMessage.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        Page<ParentMessage> page = parentMessageService.getMessageList(qpage, parentMessage);
        return page;
    }
    
    /**
     * 查看需要设置的消息权限
     */
    @RequestMapping(value = "getSetMsgList", method = {RequestMethod.POST})
    @ResponseBody
    public List<MessageMoudle> getSetMsgList()
    {
        return null;
    }
    
    /**
     * 保存需要设置的消息权限
     */
    @RequestMapping(value = "saveSetMsgList", method = {RequestMethod.POST})
    @ResponseBody
    public void saveSetMsgList(@RequestBody List<MessageMoudle> msgList)
    {
    }
    
    /**
     * 获取未读消息的数量
     */
    @RequestMapping(value = "getMsgNum", method = {RequestMethod.POST})
    @ResponseBody
    public Integer getMsgNum()
    {
        ParentMessage parentMessage = new ParentMessage();
        parentMessage.setCompany(UserUtils.getUser().getCompany());
        parentMessage.setCreatedBy(UserUtils.getUser());
        Integer num = parentMessageService.getMsgNum(parentMessage);
        return num;
    }
    
    /**
     * 获取用户是不是拥有获取该消息的权限
     */
    @RequestMapping(value = "getPermissions", method = {RequestMethod.POST})
    @ResponseBody
    public Map<String, Object> getPermissions(@RequestBody String payload)
    {
        Map<String, Object> result = Maps.newHashMap();
        String payloadCopy = null;
        String payloadDream = null;
        if (payload.contains("="))
        {
            String[] str = payload.split("=");
            if (str.length > 1)
            {
                String[] strCopy = str[1].split("\\.");
                if (strCopy.length > 1)
                {
                    payloadDream = strCopy[0];
                    payloadCopy = strCopy[1];
                }
                else
                {
                    payloadDream = str[1];
                }
            }
        }
        if (!payloadDream.equals("666"))
        {
            ParentMessage parentMessage = new ParentMessage(payloadCopy);
            parentMessage = parentMessageService.get(parentMessage);
            if (parentMessage != null)
            {
                // APP消息先查询该消息的内容，再根据消息的内容、用户查询该用户是否拥有该消息
                parentMessage.setCreatedBy(UserUtils.getUser());
                parentMessage.setCompany(UserUtils.getUser().getCompany());
                parentMessage.setCreateDateStr(DateUtils.formatDateTime(parentMessage.getCreatedDate()));
                ParentMessage parentMessageCopy = parentMessageService.getMsgPremission(parentMessage);
                if (parentMessageCopy != null && parentMessageCopy.getMessageModule() !=null)
                {
                    result.put("message", parentMessageCopy.getMessaeDeail());
                    result.put("title", parentMessageCopy.getMessageModule().getMsgModuleName());
                }
            }
        }
        return result;
    }
    
    /**
     * 清空我的消息列表
     */
    @RequestMapping(value = "deleteMyMsg", method = {RequestMethod.POST})
    @ResponseBody
    public void deleteMyMsg(@RequestBody ParentMessage parentMessage)
    {
        parentMessage.setCompany(UserUtils.getUser().getCompany());
        parentMessage.setCreatedBy(UserUtils.getUser());
        parentMessageService.deleteMyMsg(parentMessage);
    }
    
    /**
     * 删除我的消息
     */
    @RequestMapping(value = "clearMyMessage", method = {RequestMethod.POST})
    @ResponseBody
    public void clearMyMessage()
    {
        ParentMessage parentMessage = new ParentMessage();
        parentMessage.setCompany(UserUtils.getUser().getCompany());
        parentMessage.setCreatedBy(UserUtils.getUser());
        parentMessageService.clearMyMessage(parentMessage);
    }
    
    /**
     * 阅读消息
     */
    @RequestMapping(value = "updateMyMsg", method = {RequestMethod.POST})
    @ResponseBody
    public void updateMyMsg(@RequestBody ParentMessage parentMessage)
    {
    }
    
    /**
     * 查看需要设置的消息权限
     */
    @RequestMapping(value = "getSetRoleMsgList", method = {RequestMethod.POST})
    @ResponseBody
    public List<MessageMoudle> getSetRoleMsgList(@RequestBody String roleId)
    {
        return null;
    }
    
    /**
     * 获取用户信息
     * 
     * @return
     */
    @RequestMapping(value = "infoData", method = {RequestMethod.POST})
    @ResponseBody
    public String infoData()
    {
        return UserUtils.getUser().getRecordId() + UserUtils.getUser().getCompany().getRecordId();
    }
    
    /**
     * TJ 2017-09-13 昨天未读消息
     * @return
     */
    @RequestMapping(value = "getMessage", method = {RequestMethod.POST})
    @ResponseBody
    public List<ParentMessage> getMessage()
    {
        ParentMessage parentMessage = new ParentMessage();
        parentMessage.setCompany(UserUtils.getUser().getCompany());
        parentMessage.setCreatedBy(UserUtils.getUser());
        List<ParentMessage> messageList = parentMessageService.fendYesterdayUnreadMsg(parentMessage);
        return messageList;
    }
}
