package com.kyb.pcberp.modules.icloud.erp.dict.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kyb.pcberp.common.base.pojo.Icloud_BasePojo;
import com.kyb.pcberp.modules.icloud.erp.pojo.Icloud_Company;

import java.util.List;

public class Icloud_DictItem extends Icloud_BasePojo
{
    private static final long serialVersionUID = 1L;

    private String itemName; // itemName

    private String operLevel; // operlevel

    private Integer itemType;

    private String setDesc; // 设置情况

    private List<Icloud_DictValue> values;

    private Icloud_Company company;

    private String craftList;

    private String valId;

    private String value;

    public Icloud_DictItem()
    {
        super();
    }

    public Icloud_DictItem(String id)
    {
        this();
        this.recordId = id;
    }

    public String getItemName()
    {
        return itemName;
    }

    public void setItemName(String itemName)
    {
        this.itemName = itemName;
    }

    public String getOperLevel()
    {
        return operLevel;
    }

    public void setOperLevel(String operLevel)
    {
        this.operLevel = operLevel;
    }

    @JsonIgnore
    public List<Icloud_DictValue> getValues()
    {
        return values;
    }

    public void setValues(List<Icloud_DictValue> values)
    {
        this.values = values;
    }

    public Integer getItemType()
    {
        return itemType;
    }

    public void setItemType(Integer itemType)
    {
        this.itemType = itemType;
    }

    public String getSetDesc()
    {
        return setDesc;
    }

    public void setSetDesc(String setDesc)
    {
        this.setDesc = setDesc;
    }

    public Icloud_Company getCompany()
    {
        return company;
    }

    public void setCompany(Icloud_Company company)
    {
        this.company = company;
    }

    public String getCraftList()
    {
        return craftList;
    }

    public void setCraftList(String craftList)
    {
        this.craftList = craftList;
    }

    public String getValId()
    {
        return valId;
    }

    public void setValId(String valId)
    {
        this.valId = valId;
    }

    public String getValue()
    {
        return value;
    }

    public void setValue(String value)
    {
        this.value = value;
    }
}
