<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div>
	<div class="row align-items-center alert-light border-bottom pt-2 pb-2">
		<div class="col-10 text-left">待办任务</div>
		<div class="col text-right"><img src="${pageContext.request.contextPath}/assets/wechat/img/logo.png" width="50%"></div>
	</div>
	<div class="row">
		<div class="col">
			<div class="row pt-3">
				<div class="col-12">
					<div class="input-icon">
						<input type="text" class="form-control" placeholder="请输入要搜索的厂编或者批次号" v-model="craftNo" v-on:blur="loadData()"/>
						<span><i class="flaticon2-search-1 icon-md"></i></span>
					</div>
				</div>
			</div>
			<div class="row align-items-center pt-3 border-bottom" v-for="row in bottleneckProcessesList" :key="row.recordId">
				<div class="col-12">
					<div class="row pt-1 font-weight-bolder text-primary">
						<div class="col">
							{{row.occurrenceDateStr}}：{{row.craftNo}}&nbsp;&nbsp;&nbsp;{{row.batchNo}}
						</div>
					</div>
					<div class="row pt-1">
						<div class="col-6">
							任务工序：{{row.processManName}}
						</div>
						<div class="col-6">
							当前进度：{{row.category}}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
    <div style="padding-top: 10rem;"></div>
    <div class="row fixed-bottom alert-light font-size-sm text-secondary">
        <div class="col text-center">
            <div>
                粤ICP备16000534号
            </div>
            <div>
                Copyright©2021 深圳市科易博软件有限公司 版权所有
            </div>
        </div>
    </div>
</div>