package com.kyb.pcberp.modules.sys.utils;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.utils.SpringContextHolder;
import com.kyb.pcberp.modules.sys.dao.DepartmentDao;
import com.kyb.pcberp.modules.sys.dao.EmployeeDao;
import com.kyb.pcberp.modules.sys.dao.OrganizationDao;
import com.kyb.pcberp.modules.sys.dao.RelationEmployeeDao;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.Department;
import com.kyb.pcberp.modules.sys.entity.Employee;
import com.kyb.pcberp.modules.sys.entity.Organization;
import com.kyb.pcberp.modules.sys.entity.RelationEmployee;

/**
 * zjn 2018-09-26 
 * 部门、组织、员工工具类
 */
public class RelationUtils {

	 private static DepartmentDao departmentDao = SpringContextHolder.getBean(DepartmentDao.class);
	 
	 private static OrganizationDao organizationDao = SpringContextHolder.getBean(OrganizationDao.class);
	 
	 private static EmployeeDao employeeDao = SpringContextHolder.getBean(EmployeeDao.class);
	 
	 private static RelationEmployeeDao relationEmployeeDao = SpringContextHolder.getBean(RelationEmployeeDao.class);
	
	 /**zjn 2018-09-26 根据公司获取部门*/
	 public static List<Department> getDeptListByCompany(Company company)
	 {
		List<Department> deptList = departmentDao.getDeptListByCompany(company);
		return deptList;
	 }
	
	 /**zjn 2018-09-26 根据部门获取组织*/
	 public static List<Organization> getOrganizationListByDept(Department dept)
	 {
		 List<Organization> organizationList = organizationDao.getOrganizationListByDept(dept);
		 return organizationList;
	 }
	
	 /**zjn 2018-09-26 根据部门或者组织获取员工*/
	 public static List<Employee> getEmployeeByAllId(Employee employee)
	 {
		 List<Employee> employeeList = employeeDao.getEmployeeByAllId(employee);
		 return employeeList;
	 }
	 
	 /**zjn 2018-09-26 根据员工id组删除员工和关联表*/
	 @Transactional(readOnly = false)
	 public static void deleteEmployeeByIds(String recordId) 
	 {
		 //删除所有员工
		 Employee employee = new Employee();
		 employee.setRecordId(recordId);
		 employee.setActiveFlag(TypeKey.NO.toString());
		 employeeDao.deleteEmployeeByIds(employee);
		 
		 //删除所有关系表
		 RelationEmployee relationEmployee = new RelationEmployee();
		 relationEmployee.setEmployee(employee);
		 relationEmployee.setActiveFlag(TypeKey.NO.toString());
		 relationEmployeeDao.deleteRelation(relationEmployee);
	 }
	 
	 /** zjn 2018-09-26 根据组织id组删除组织*/
	 @Transactional(readOnly = false)
	 public static void deleteOrganizationByIds(String recordId) 
	 {
		 Organization organization = new Organization();
		 organization.setRecordId(recordId);
		 organization.setActiveFlag(TypeKey.NO.toString());
		 organizationDao.deleteOrganizationByIds(organization);
	 }
}
