/**
 * 
 */
package com.kyb.pcberp.modules.contract.entity;

import com.kyb.pcberp.modules.production.entity.Feeding;
import com.kyb.pcberp.modules.production.entity.ProduceBatch;
import com.kyb.pcberp.modules.production.entity.Replenish;

/**
 * 合同调整类
 * 
 * <AUTHOR>
 */
public class AdjustContract
{
    private String adjustType; // 1：合同，2：投料，3：补料
    
    private String adjustQty;
    
    private ContractDetail contractDetail;
    
    private Notification notification;
    
    private Feeding feeding;
    
    private Replenish replenish;
    
    private ProduceBatch oldBatch;
    private ProduceBatch newBatch;
    
    public AdjustContract()
    {
    }
    
    public String getAdjustType()
    {
        return adjustType;
    }
    
    public void setAdjustType(String adjustType)
    {
        this.adjustType = adjustType;
    }
    
    public String getAdjustQty()
    {
        return adjustQty;
    }
    
    public void setAdjustQty(String adjustQty)
    {
        this.adjustQty = adjustQty;
    }
    
    public ContractDetail getContractDetail()
    {
        return contractDetail;
    }
    
    public void setContractDetail(ContractDetail contractDetail)
    {
        this.contractDetail = contractDetail;
    }
    
    public Notification getNotification()
    {
        return notification;
    }
    
    public void setNotification(Notification notification)
    {
        this.notification = notification;
    }
    
    public Feeding getFeeding()
    {
        return feeding;
    }
    
    public void setFeeding(Feeding feeding)
    {
        this.feeding = feeding;
    }
    
    public Replenish getReplenish()
    {
        return replenish;
    }
    
    public void setReplenish(Replenish replenish)
    {
        this.replenish = replenish;
    }

    public ProduceBatch getOldBatch()
    {
        return oldBatch;
    }

    public void setOldBatch(ProduceBatch oldBatch)
    {
        this.oldBatch = oldBatch;
    }

    public ProduceBatch getNewBatch()
    {
        return newBatch;
    }

    public void setNewBatch(ProduceBatch newBatch)
    {
        this.newBatch = newBatch;
    }
}