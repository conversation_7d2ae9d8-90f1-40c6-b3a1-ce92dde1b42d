<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.crm.dao.AccountsAttachementsDao">
    
	<sql id="AccountsAttachementsColumns">
		a.recordid AS "recordId",
		a.companyid AS "company.recordId",
		a.type AS "type",
		a.accountsId AS "accountsId",
		a.orgFileName AS "orgFileName",
		a.realFileName AS "realFileName",
		a.source AS "source",
		a.createdBy AS "createdBy",
		a.createdDate AS "createdDate"
	</sql>
    
	<select id="get" resultType="AccountsAttachements">
		SELECT 
			<include refid="AccountsAttachementsColumns"/>
		FROM sl_accounts_attachements a
		WHERE a.recordId = #{recordId}
	</select>
	
	<!-- 根据申请单ID查询发票信息 -->
	<select id="findAccountsAttachements" resultType="AccountsAttachements">
		SELECT 
			<include refid="AccountsAttachementsColumns"/>,su.userName AS createdByName
		FROM sl_accounts_attachements a
		LEFT JOIN sm_user su ON su.recordId = a.createdBy AND su.activeFlag = 1
		WHERE a.accountsId = #{accountsId}
		<if test="type != null and type != ''">
			AND type = #{type}
		</if>
	</select>
	
	<select id="findList" resultType="AccountsAttachements">
		SELECT 
			<include refid="AccountsAttachementsColumns"/>
		FROM sl_accounts_attachements a
		<where>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<select id="findAllList" resultType="AccountsAttachements">
		SELECT 
			<include refid="AccountsAttachementsColumns"/>
		FROM sl_accounts_attachements a
		<where>
			
		</where>		
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<insert id="saveAccountsAttachement">
		INSERT INTO sl_accounts_attachements(
			companyId,
			type,
			accountsId,
			orgFileName,
			realFileName,
			source,
			createdBy,
			createdDate
		) VALUES (
			#{company.recordId},
			#{type},
			#{accountsId},
			#{orgFileName},
			#{realFileName},
			#{source},
			#{createdBy.recordId},
			#{createdDate}
		)
	</insert>
	
	<update id="update">
		UPDATE sl_accounts_attachements SET 	
			type = #{type},
			accountsId = #{accountsId},
			orgFileName = #{orgFileName},
			realFileName = #{realFileName}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		delete from sl_accounts_attachements
		WHERE recordId = #{recordId}
	</update>


	<select id="selectcalculate" resultType="BigDecimal">
		SELECT
		a.radio
		FROM
		group_process a
		LEFT JOIN group_department_relation b ON a.saleId = b.recordId
		LEFT JOIN group_department_relation c ON a.econnmyId = c.recordId
		LEFT JOIN group_department_relation d ON a.factoryId = d.recordId
		WHERE
		b.bindErpId = #{saleId} AND a.activeFlag = 1
		<if test="econnmyId != null and econnmyId != ''">
			AND FIND_IN_SET(c.bindErpId,#{econnmyId})
		</if>
		<if test="factoryId == null || factoryId == ''">
			AND d.bindErpId IS NULL
		</if>
		<if test="factoryId != null and factoryId != ''">
			AND d.bindErpId =#{factoryId}
		</if>
		limit 1
	</select>
</mapper>