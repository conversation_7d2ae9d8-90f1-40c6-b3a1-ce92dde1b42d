<%@ page contentType="text/html;charset=UTF-8"%>
<div ng-intro-options="countCtrl.introListOptions" ng-intro-method="countCtrl.helpList" ng-intro-autostart="countCtrl.shouldAutoStart"></div>
<div ng-intro-options="countCtrl.introDetailOptions" ng-intro-method="countCtrl.helpDetail" ng-intro-autostart="countCtrl.shouldAutoStart"></div>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">生产管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="production.countion">过数设置</a>
        </li>
    </ul>
    
    <div class="page-toolbar">
    	<button class="btn btn-fit-height default pull-right" ng-click="countCtrl.help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>
</div>
<!-- END 导航-->

<tabset class="tabset-margin-top">
    <!-- BEGIN 员工过数列表 -->
    <tab heading="员工过数设置" active="countCtrl.tabs.viewForm.active">
        <div class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row" id="step6">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">员工编号：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="countCtrl.query.userCode.value"
                                           disable-auto-validate="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">员工姓名：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="countCtrl.query.userName.value"
                                           disable-auto-validate="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">所属部门：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select  ng-model="countCtrl.query.dept.value" theme="bootstrap" disable-auto-validate="true">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                        <ui-select-choices repeat="item in countCtrl.depts | filter: $select.search">
                                            <div ng-bind-html="item.name | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">职务：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="countCtrl.query.position.value"
                                           disable-auto-validate="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" >
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">在职状态：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select ng-model="countCtrl.query.status.value" placeholder="请选择..." class="form-control"
                                            disable-auto-validate="true">
                                        <option value="">所有</option>
                                        <option value="1">在职</option>
                                        <option value="2">离职</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">工序(多选)：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select theme="bootstrap" multiple sortable="true" close-on-select="false"
                                               ng-model="countCtrl.query.processList.value"
                                               disable-auto-validate="true">
                                        <ui-select-match placeholder="请选择...">{{$item.category}}</ui-select-match>
                                        <ui-select-choices repeat="item in countCtrl.processList | filter: $select.search">
                                            <div ng-bind-html="item.category | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row" >
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="countCtrl.doQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">员工过数设置列表</div>
                <div class="actions" id="step7">
                    <div class="portlet-input input-inline input-small" ng-if="countCtrl.right.edit">
                        <button type="button" class="btn green btn-default-width" ng-click="countCtrl.addEmpCount()"><i class="fa fa-plus"></i> 添&nbsp;加</button>
                    </div>
                </div>
            </div>

            <div class="portlet-body">
                <div class="table-scrollable" style="margin-top:0px !important" id="step1">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
                        <tr class="heading" id="step2">
                            <th ng-class="{'sorting': countCtrl.sort.userCode.both, 'sorting_desc': countCtrl.sort.userCode.desc, 'sorting_asc': countCtrl.sort.userCode.asc}" ng-click="countCtrl.sortClick('userCode')">员工编号</th>
                            <th>员工姓名</th>
                            <th>归属部门</th>
                            <th>职务</th>
                            <th ng-class="{'sorting': countCtrl.sort.status.both, 'sorting_desc': countCtrl.sort.status.desc, 'sorting_asc': countCtrl.sort.status.asc}" ng-click="countCtrl.sortClick('status')">状态</th>
                            <th width="30%">拥有过数工序</th>
                            <th>跳过过数</th>
                            <th>检测所有</th>
                            <th ng-if="countCtrl.right.view" id="step3">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in countCtrl.page.data.list"  ng-dblclick="countCtrl.modEmpCount($index)">
                            <td><a ng-bind="row.user.userCode" ng-click="countCtrl.modEmpCount($index)"></a></td>
                            <td ng-bind="row.user.userName"></td>
                            <td ng-bind="row.user.deptName | deptFilter:row.deptList"></td>
                            <td ng-bind="row.user.position"></td>
                            <td ng-if="row.user.status === '1'">
                                <span class="label label-sm label-success">在职</span>
                            </td>
                            <td ng-if="row.user.status === '2'">
                                <span class="label label-sm label-danger">离职</span>
                            </td>
                            <td ng-if="row.user.status!=='1' && (row.user.status!='2')">
                                <span class="label label-sm label-default">其它</span>
                            </td>
                            <td ng-bind="row.processNames"></td>
                            <td>
                            	<span ng-if="row.user.skipCount == 1" class="label label-sm label-success">有</span>
                            	<span ng-if="row.user.skipCount == 0" class="label label-sm label-default">无</span>
                            </td>
                            <td>
                            	<span ng-if="row.user.checkAllQc == 1" class="label label-sm label-success">有</span>
                            	<span ng-if="row.user.checkAllQc == 0" class="label label-sm label-default">无</span>
                            </td>
                            <td ng-if="countCtrl.right.view">
                                <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-if="countCtrl.right.edit" ng-click="countCtrl.delEmpCount($index)"><i class="fa fa-times font-red"></i> 删除</a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="row page-margin-top">
                    <div class="col-md-12 col-lg-6" id="step5">
                        <span class="inline">每页</span>
                        <select class="form-control inline table-pageSize-width"
                                ng-model="countCtrl.page.pageSize"
                                ng-change="countCtrl.pageSizeChange()"
                                ng-options="pageSizeOption for pageSizeOption in countCtrl.page.pageSizeOptions"
                                disable-auto-validate="true">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示
                             {{countCtrl.page.data.startCount}} / 
                             {{countCtrl.page.data.endCount}}条，共
                             {{countCtrl.page.data.count}} 条</span>
                    </div>
                    <div class="col-md-12 col-lg-6" id="step4">
                        <paging class="pull-right" page="countCtrl.page.data.pageNo"
                                page-size="countCtrl.page.data.pageSize"
                                total="countCtrl.page.data.count" adjacent="1" dots="..."
                                scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                active-class="active" disabled-class="disabled"
                                show-prev-next="true"
                                paging-action="countCtrl.doPage(page, pageSize, total)"> </paging>
                    </div>
                </div>
            </div>
        </div>
    </tab>
    <!-- END 员工过数列表 -->

    <!-- BEGIN 员工过数编辑 -->
    <tab active="countCtrl.tabs.editForm.active" ng-show="countCtrl.tabs.editForm.show">
        <tab-heading>员工过数设置详情 <i class="fa fa-times set-cursor-pointer" ng-click="countCtrl.hideEditForm()"></i></tab-heading>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">{{countCtrl.editTitle}}员工过数设置</div>
            </div>
            <div class="portlet-body" id="stepDetail1">
                <form class="form-horizontal" name="count_form" ng-init="countCtrl.setFormScope(this)" novalidate="novalidate" ng-submit="countCtrl.submitMod(count_form);" ng-submit-force="true">
                    <div class="form-group">
                        <label class="col-sm-3 control-label"><span class="required">*</span>用户：</label>
                        <div class="col-sm-6">
                            <span class="form-control" disabled ng-bind="countCtrl.count.user.userName" ng-if="countCtrl.editAdd == 'edit'" ></span>
                            <ui-select theme="bootstrap" ng-model="countCtrl.count.user" register-custom-form-control ng-if="countCtrl.editAdd != 'edit'" required firstfocus="{{countCtrl.focus.main}}">
                                <ui-select-match placeholder="请选择...">{{$select.selected.userName}}</ui-select-match>
                                <ui-select-choices repeat="item in countCtrl.userList | filter: $select.search">
                                    <div ng-bind-html="item.userName | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                        <div class="col-sm-1" id="stepDetail2">
                            <button type="button" class="btn green" ng-click="countCtrl.seeUserDetail()"> 用户信息</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label"><span class="required">*</span>工序：</label>
                        <div class="col-sm-7">
                            <ui-select theme="bootstrap" multiple ng-model="countCtrl.processSelected" sortable="true"
                            		   ng-disabled="!countCtrl.right.edit"
                                       close-on-select="false" required>
                                <ui-select-match placeholder="选择工序...">{{$item.name}}</ui-select-match>
                                <ui-select-choices repeat="item in countCtrl.processList | filter: $select.search">
                                    <div ng-bind-html="item.name | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                    <div class="form-group">
                       <label class="col-sm-3 control-label">权限：</label>
                        <div class="col-sm-6" style="margin-top: 8px">
                           	过数
							<checkbox ng-model="countCtrl.skipCount" class="liChild"></checkbox>&nbsp;&nbsp;&nbsp;&nbsp;
							检测
							<checkbox ng-model="countCtrl.checkQc" class="liChild"></checkbox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">多人过数：</label>
                        <div class="col-sm-6">
                            <ui-select theme="bootstrap" ng-model="countCtrl.count.user.multipleCount" register-custom-form-control>
                                <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                <ui-select-choices repeat="item.value as item in countCtrl.multipleCountList | filter: $select.search">
                                    <div ng-bind-html="item.name | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">接/交板权限：</label>
                        <div class="col-sm-6">
                            <ui-select theme="bootstrap" ng-model="countCtrl.count.user.takeHandCount" register-custom-form-control>
                                <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                <ui-select-choices repeat="item.value as item in countCtrl.takeHandCountList | filter: $select.search">
                                    <div ng-bind-html="item.name | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">跳过过数：</label>
                        <div class="col-sm-6">
                            <ui-select theme="bootstrap" ng-model="countCtrl.count.user.skipCount" register-custom-form-control>
                                <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                <ui-select-choices repeat="item.value as item in countCtrl.skipCountList | filter: $select.search">
                                    <div ng-bind-html="item.name | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">检测所有：</label>
                        <div class="col-sm-6">
                            <ui-select theme="bootstrap" ng-model="countCtrl.count.user.checkAllQc" register-custom-form-control>
                                <ui-select-match placeholder="请选择...">{{$select.selected.name}}</ui-select-match>
                                <ui-select-choices repeat="item.value as item in countCtrl.checkAllQcList | filter: $select.search">
                                    <div ng-bind-html="item.name | highlight: $select.search"></div>
                                </ui-select-choices>
                            </ui-select>
                        </div>
                    </div>
                    
                    <div class="row" >
                        <div class="form-group" ng-if="countCtrl.right.edit">
                            <div class="col-sm-offset-4 col-sm-10" >
                                <button id="stepDetail3" type="submit"  class="btn btn-primary btn-default-width" ng-disabled="count_form.$invalid || !countCtrl.processSelected || countCtrl.processSelected.length ==0"><i class="fa fa-save"></i> 保&nbsp;存</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </tab>
    <!-- END 员工过数编辑 -->
</tabset>

<div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">
                    <span>提示</span>
                </h4>
            </div>
            <div class="modal-body">
                <p>
                    <span ng-bind="countCtrl.message"></span>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
            </div>
        </div>
    </div>
</div>

<div id="staticRemove" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">删除用户过数设置</h4>
            </div>
            <div class="modal-body clearfix" style="padding-top:25px;">
                <div class="col-sm-1 text-center" ><i class="fa fa-warning font-yellow" style="font-size: 30px !important;"></i></div>
                <div class="col-sm-11" ><p>确认删除用户{{countCtrl.delName}}的过数设置吗？</p></div>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn blue" ng-click="countCtrl.doDelProcessCtrl()">确定</button>
                <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade bs-modal-lg" id="large" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">员工信息</h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">员工编号:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="countCtrl.count.user.userCode"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">员工姓名:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="countCtrl.count.user.userName"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">所属部门:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="countCtrl.count.user.deptName"></span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">职务:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="countCtrl.count.user.position"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">入职时间:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="countCtrl.count.user.hiredDate | date:'yyyy-MM-dd'"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">在职状态:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-if="countCtrl.count.user.status==1">在职</span>
                                    <span class="form-control" ng-if="countCtrl.count.user.status==2">离职</span>
                                    <span class="form-control" ng-if="countCtrl.count.user.status==null"></span>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">出生日期:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="countCtrl.count.user.birthday | date:'yyyy-MM-dd'"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">家庭号码:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="countCtrl.count.user.phone"></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">手机号码:</label>
                                <div class="col-sm-7 col-md-8">
                                    <span class="form-control" ng-bind="countCtrl.count.user.mobile"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-2 control-label">描述：</label>
                                <div class="col-sm-7 col-md-10">
                                    <span class="form-control" ng-bind="countCtrl.count.user.remark"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>