
/* customerCtrl */
kybApp.controller('customerorderCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil', 'BaseUtil',"Upload", function ($rootScope, $scope, upida, $timeout, CommonUtil, BaseUtil,Upload) {

    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();
        
        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
    
    var vm = this; 
    vm.isshow = false; //是否显示明细的添加按钮
    // 工艺配置
    vm.config = {};
    
    vm.focus = {customer: 1, contact: 1};
    
    vm.queryAll = false;
    // 分页数据
    vm.page = {};
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    // 分页数据
    vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "crm/slCustomerOrder/page";
    vm.page.condition = []; // 条件

    // 报价单明细
    vm.page.quotationDetail = {};
    vm.page.quotationDetail.data = {};
    vm.page.quotationDetail.pageSize = 10;
    vm.page.quotationDetail.pageNo = 1;
    vm.page.quotationDetail.url = "crm/quotation/detail/page";
    vm.page.quotationDetail.condition = [];
    
    
    
    vm.refer = {};
    vm.refer.customerList = []; // 客户信息
    vm.refer.branchList = []; // 子公司，承接公司
    vm.refer.currencyTypeList = [];// 货币类型
    vm.refer.taxDescriptList = []; // 税类说明
    vm.refer.payWayList = []; // 结算方式
    vm.refer.userList = [];
    vm.refer.daoreList = [];//導熱
	vm.refer.naiyaList = [];// 耐壓
	vm.refer.pliesnumberList = [];//耐压
	vm.refer.deliverycityList= [];//耐立德收货地点
    
    // 默认排序按时间排序排序处理
    vm.sort = {};
    vm.sort.no = {both: true, desc: false, asc: false};
    vm.sort.name = {both: true, desc: false, asc: false};
    vm.sort.createdDate = {both: false, desc: true, asc: false};
    vm.sort.status = {both: true, desc: false, asc: false};

    vm.sortClick = function (col) {
        vm.sort[col].both = false;
        if (!vm.sort[col].desc && !vm.sort[col].asc) {
            vm.sort[col].asc = true;
        } else {
            if (vm.sort[col].asc) {
                vm.sort[col].desc = true;
                vm.sort[col].asc = false;
            } else {
                vm.sort[col].desc = false;
                vm.sort[col].asc = true;
            }
        }

        for (var p in vm.sort) {
            if (p !== col) {
                vm.sort[p].desc = false;
                vm.sort[p].asc = false;
                vm.sort[p].both = true;
            }
        }

        vm.query.sort.value = vm.sort[col].asc ? col + " ASC" : col + " DESC";
      
        	vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    };

    // 查询条件参数
    vm.query = {}; // 查询对象

    // 排序字段
    vm.query.sort = {};
    vm.query.sort.name = "orderBy";
    vm.query.sort.value = "createdDate DESC";

    vm.query.no = {};
    vm.query.no.name = "no";
    vm.query.no.value = "";

    vm.query.name = {};
    vm.query.name.name = "shortName";
    vm.query.name.value = "";

    // 状态
    vm.query.statusName = "status";
    vm.query.statusSelected = 3;
    vm.query.statusList = [{
        "value": 100301,
        "name": "待审核"
    }, {
        "value": 100302,
        "name": "有效"
    }, {
        "value": 100303,
        "name": "无效"
    }, {
        "value": 3,
        "name": "所有"
    }];

    vm.query.genName = "salesman.recordId";
    vm.query.genSelected = 2;
    vm.query.genList = [{
        "value": 0,
        "name": "有业务员"
    }, {
        "value": 1,
        "name": "无业务员"
    }, {
        "value": 2,
        "name": "所有"
    }];

    // tabs控制
    vm.tabs = {
        viewForm: {
            active: true
        },
        editForm: {
            active: false,
            show: false
        },
        manForm: {
            active: false,
            show: false
        }
    };
    
    
    // 时间范围的选项
    vm.rangeOptions = {
        //format: "YYYY-MM-DD",
    	startDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 10))),
        minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
    };
    
    //查询时间条件
    vm.query.sentTimeStartQr = {};
    vm.query.sentTimeStartQr.name = "sentTimeStartQr";
    vm.query.sentTimeStartQr.value = "";
    vm.query.sentTimeEndQr = {};
    vm.query.sentTimeEndQr.name = "sentTimeEndQr";
    vm.query.sentTimeEndQr.value = "";
    
    // 时间范围的Model
    vm.time = {	
        start: {},
        end: {}
    };

    vm.isAdd = false;

    // 权限
    vm.right = {};

    // 编辑操作对象
    vm.editItem = {};
    vm.edit = {};
    vm.edit.item = {};
    vm.edit.title = "";
    vm.edit.delIndex = -1;
    vm.edit.delMsg = "";
    vm.edit.formScope = null;
    vm.edit.isAdd = false;

    vm.edit.detail = {};
    vm.edit.detail.item = {};
    vm.edit.detail.btnDesc = "添 加";
    vm.edit.detail.btnTitle = "添加";
    vm.edit.detail.modIndex = -1;
    vm.edit.detail.delIndex = -1;
    vm.edit.detail.delMsg = "";
    vm.edit.detail.formScope = null;
    vm.edit.detail.isAdd = false;

    // 删除客户记录所在列表的索引
    vm.delIndex = -1;
    vm.delMsg = "";

    // 修改标题
    vm.editTitle = "";
    vm.formScope = null;
    vm.formScope_zgs = null;

    // 编辑联系人操作对象
    vm.editManItem = {customer: {}};
    // 删除联系人数组索引
    vm.delManItemIndex = -1;
    vm.delManItemMsg = "";
    vm.modManItemIndex = -1;
    // 联系人列表
    vm.contacts = {};
    vm.currentContacts = [];
    vm.tempContacts = [];
    // 修改联系人按钮描述
    vm.editContactBtnDesc = "添 加";
    vm.manFormScope = null;

    // 用户列表
    vm.users = [];
    vm.empCount = []; // 员工数量
    vm.yearSalerooms = []; // 年销售额
    vm.currencyType = [];// 货币类型
    vm.taxDescripts = []; // 税类说明
    vm.freightWays = []; // 交货方式
    vm.deliveryWays = []; // 送货方式
    vm.payWays = []; // 结算方式
    vm.checkDates = []; // 对账日期
    vm.provinceList = [];
    vm.provinceAreaCode = "";
    vm.cityAreaCode = "";
    vm.areaCode = {};
    vm.cityList = [];
    vm.countyList = [];
    vm.customerList = [];
    vm.needQuery = false; // 需要查询联系人
    // 显示状态
    vm.showStatus = 0;
    //比较的时候判断是否有改
    vm.DeliverPlaceFlag = false;
    vm.customerPoFlag = false;
    vm.deliveryWayFlag = false;
    vm.currencyTypeFlag = false;
    vm.taxDescriptFlag = false;
    vm.totalAmtFlag = false;
    vm.pcsSizeFlag = false;
    vm.deliverSizeFlag = false;
    vm.customerModelFlag = false;
    vm.sampleAmtFlag = false;
    vm.boardLevelFlag = false;
    vm.materialTypeFlag = false;
    vm.boardThicknessFlag = false;
    vm.copperCladThicknessFlag = false;
    vm.surfaceProcessFlag = false;
    vm.solderMaskTypeFlag = false;
    vm.characterTypeFlag = false;
    vm.shapingWayFlag = false;
    vm.testMethodFlag = false;
    vm.lingeSpacingFlag = false;
    vm.smallApertureFlag = false;
    vm.halAholeFlag = false;
    vm.buryBlindHoleFlag = false;
    vm.resistanceFlag = false;
    vm.deliveryUrgentFlag = false;
    vm.priceFlag = false;
    vm.engineeringFeeFlag = false;
    vm.deliveryDaysFlag = false;
    vm.subTotalFlag = false;
    vm.daoreFlag = false;
    vm.naiyaFlag = false;
    vm.pliesnumberFlag = false;
    vm.deliverycityFlag = false;
    
    vm.setFormScopeDetail = function (scope) {
        vm.edit.detail.formScope = scope;
     };
    
    
    vm.setFormScope = function (scope) {
    	vm.edit.formScope = scope;
    };

    // 显示编辑
    vm.showEditForm = function () {
        vm.tabs.editForm.show = true;
        vm.tabs.editForm.active = true;
    };

    // 隐藏编辑
    vm.hideEditForm = function () {
    	vm.seeAttachFlag = false; // 查看附件标志
    	vm.seedeadlineFlag = false;//设置出单期限标志
        vm.tabs.editForm.show = false;
        vm.tempContacts = [];
        vm.edit.formScope.editForm.$setPristine();

        vm.isAdd = false;
        $timeout(function () {
            vm.tabs.viewForm.active = true;
        });
    };

    // 隐藏所有的窗口
    vm.hideManAndEditForm = function () {
        vm.formScope.editForm.$setPristine();
        vm.tabs.manForm.show = false;
        vm.tabs.editForm.show = false;
        vm.tempContacts = [];
        vm.editManItem = {customer: {}};
        clearContactPage();
        vm.isAdd = false;

        $timeout(function () {
            vm.tabs.viewForm.active = true;
        });
    };

    // 分页按钮单击处理
    vm.doPage = function (page, pageSize, total) {
    	  var url = "";
          var condition = [];
          var canQuery = false;
          vm.page.pageNo = page;
          vm.page.pageSize = pageSize;
          url = vm.page.url;
          // 重新查询数据
          vm.use = BaseUtil.getUser();
          if(vm.use.userType === "1"){
          	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
               	if(data !=null){
               		vm.edit.item.customer = data;
               		 // 状态
                      vm.page.condition.push({
                          name: "customer",
                          value: vm.edit.item.customer
                      });
                      condition = vm.page.condition;
                      canQuery = true;
                      vm.init(page, pageSize, condition, url);
               	}
               });
          }
    };

    /*
     * 兼容条件查询与普通查询
     * no 当前页
     * size 当前页显示数量
     * condition 是数组，可放入多个条件对象，如{name:"type", value:"1"}  名称对应实体字段名称，值对应页面输入的值
     * url 请求数据链接
     */
    vm.init = function (no, size, condition, url, name) {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
		
        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        reqData.queryAll = vm.queryAll;

        condition.push({
            name: vm.query.sort.name,
            value: vm.query.sort.value
        });
        // 设置过滤条件
        CommonUtil.setRequestBody(reqData, condition);
        
        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};

            // 如果结果为空
            if (typeof result === 'undefined' || typeof result.list === 'undefined') {
                data.pageNo = 1;
                data.pageSize = 10;
                data.list = [];
                data.startCount = 0;
                data.endCount = 0;
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }
            vm.page.data = data;
            
            vm.page.condition = [];
            
            MainCtrl.unblockUI();
        });
    };

    // 页面显示数量改变
    vm.pageSizeChange = function () {
    	if(vm.urlFlag){
        	vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.allurl);
        }else{
        	vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url);
        }
    };

    // 查询数据
    vm.doQuery = function () {
        // 设置查询条件
        var condition = [];
        vm.use = BaseUtil.getUser();
        if(vm.use.userType === "1"){
        	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
             	if(data !=null){
             		vm.edit.item.customer = data;
             		 // 状态
                    vm.page.condition.push({
                        name: "customer",
                        value: vm.edit.item.customer
                    });
                    
                    
                    if (vm.query.no.value && vm.query.no.value !== "") {
                    	vm.page.condition.push({
                            name: vm.query.no.name,
                            value: vm.query.no.value
                        });
                    }
                    vm.page.pageNo = 1;
                    // 初始化第一页，条件为空
                    vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
             	}
             });
        }else{
        	   vm.page.pageNo = 1;
               // 初始化第一页，条件为空
               vm.init(vm.page.pageNo, vm.page.pageSize, [], vm.page.url);
        	
        }
    };


    // 单击修改
    vm.modQuotation = function (index) {
        vm.edit.title = "修改";
        vm.edit.item = angular.copy(vm.page.data.list[index]);
        vm.edit.isAdd = true;
        vm.focus.main += 1;
        
    	//赋值报价人
    	for (var i = 0; i < vm.refer.userList.length; i++)
		{
			if(vm.refer.userList[i].recordId ===  vm.edit.item.customeruserId.recordId){
				vm.edit.item.quoter = vm.refer.userList[i];
			}
		}
        
        if (typeof vm.edit.item.quoter !== "undefined" && typeof vm.edit.item.quoter.recordId !== "undefined"){
        	vm.edit.item.quoter = CommonUtil.getObjectById(vm.edit.item.quoter.recordId, vm.refer.userList);
        }
        vm.showEditForm();
        findCustomerOrderDetailByCustomerOrder(vm.edit.item);
    };


    // 单击添加
    vm.addCustomer = function () {
    	vm.isshow = true;
        // 判断是否已打开添加窗口，且已经是添加状态，若已是添加状态不再初始化
        if (vm.tabs.editForm.show && vm.isAdd) {
            vm.showEditForm();
            return;
        }

        if (vm.tabs.editForm.show && !vm.isAdd) {
            vm.formScope.editForm.$setPristine();
        }

        vm.isAdd = true;
        vm.edit.item = {};
        vm.edit.item.quoter = null;
        vm.edit.item.branch = {};
        
        vm.focus.main += 1;
        
        // 设置状态为10
        vm.edit.item.status = "10010701";
        
        // 明细为空
        vm.tmpQuotationItemList = [];
        vm.page.quotationDetail.data.list = [];
        	

        upida.get("crm/slCustomerOrder/getNo").then(function(data){
            vm.edit.item.no = data;
        });
        vm.showEditForm();
        
        //根据用户编号 去查询是属于哪个客户的  把客户信息返回
        vm.use = BaseUtil.getUser();
        if(vm.use.userType === "1"){
        	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
             	if(data !=null){
             		for (var i = 0; i < vm.refer.customerList.length; i++)
					{
             			if(vm.refer.customerList[i].recordId === data.recordId){
             					vm.edit.item.customer = vm.refer.customerList[i];
             			}
					}
             	}
             });
        }
    };

    // 单击取消
    vm.cacelSave = function (form) {
        vm.hideEditForm();
    };


    // 创建和修改
    vm.submitMod = function () {
    	MainCtrl.blockUI({
	     animate: true,
	    });
    	upida.post("crm/slCustomerOrder/getStatus", vm.edit.item).then(function (data) {
    		if(data && data.status == "10010701"){
    			vm.edit.formScope.editForm.$setDirty();
    			   if (!vm.edit.formScope.editForm.$valid) {
    				   MainCtrl.unblockUI();
    			       return;
    			   }
    		       var msg = "";
    		       // 添加的时候，设置明细
    		       if (!vm.isAdd) {
    		       		msg = "修改下单“" + vm.edit.item.no + "”";
    		       		vm.edit.item.customeruserId  = BaseUtil.getUser();
    		       		vm.edit.item.quotationItemList = vm.page.quotationDetail.data.list;
    		       	    vm.page.condition.push({
    		    	       name: "customer.recordId",
    		    	       value: vm.edit.item.customer.recordId
    		            });
    		           // 提交数据
    		           upida.post("crm/slCustomerOrder/save", vm.edit.item).then(function (data) {
    		               // 修改成功以后重新查询数据并重置编辑对象
    		               if (data) {
    		                  //重新查询
    		            	   vm.init(vm.page.pageNo,
    		                       vm.page.pageSize,
    		                       vm.page.condition,
    		                       vm.page.url);
    		            	   
    		            	   
    		                   vm.edit.item = {};
    		                   vm.hideEditForm();
    		                   vm.message = msg + "成功！";
    		               }else{
    		            	   MainCtrl.unblockUI();
    		               	   vm.message = msg + "失败！";
    		               }
    		               // 提示信息
    		               $('#static').modal();
    		           });
    		       }else{
    		    	   MainCtrl.unblockUI();
    		       }
    		}else{
    			// 重新查询数据
	            vm.use = BaseUtil.getUser();
	            if(vm.use.userType === "1"){
	            	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
	                 	if(data !=null){
	                 		vm.edit.item.customer = data;
	                 		 // 状态
	                        vm.page.condition.push({
	                            name: "customer",
	                            value: vm.edit.item.customer
	                        });
	                        // 初始化第一页，条件为空
	                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
	                 	}else{
	                 		MainCtrl.unblockUI();
	                 	}
	                 });
	            }else{
	            	MainCtrl.unblockUI();
	            }
    			vm.message = "不能重复操作！";
	            $('#static').modal();
    		}
    	 });
    };

    // 删除
    vm.delCustomer = function (index) {
    	vm.customerOrder = vm.page.data.list[index];
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	upida.post("crm/slCustomerOrder/getStatus", vm.customerOrder).then(function (data) {
    		if(data && data.status == "10010701"){
                vm.delIndex = index;
                vm.delMsg = "您确定要删除客户下单“" + vm.page.data.list[vm.delIndex].no + "”吗？";
                $('#staticRemove').modal();
    		}else{
    			// 重新查询数据
	            vm.use = BaseUtil.getUser();
	            if(vm.use.userType === "1"){
	            	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
	                 	if(data !=null){
	                 		vm.edit.item.customer = data;
	                 		 // 状态
	                        vm.page.condition.push({
	                            name: "customer",
	                            value: vm.edit.item.customer
	                        });
	                        // 初始化第一页，条件为空
	                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
	                 	}
	                 });
	            }else
	            {
	            	MainCtrl.unblockUI();
	            }
    			vm.message = "不能重复操作！";
	            $('#static').modal();
    		}
    	 });
    };

    // 做删除操作
    vm.doDelCustomer = function () {
        var reqData = {};
        reqData.recordId = vm.page.data.list[vm.delIndex].recordId;
        reqData.shortName = vm.page.data.list[vm.delIndex].shortName;
        MainCtrl.blockUI({
    	    animate: true,
    	});
        upida.post("crm/slCustomerOrder/delete", reqData).then(function (data) {
               // 重新查询数据
               vm.use = BaseUtil.getUser();
               if(vm.use.userType === "1"){
               	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
                    	if(data !=null){
                    		vm.edit.item.customer = data;
                    		 // 状态
                           vm.page.condition.push({
                               name: "customer",
                               value: vm.edit.item.customer
                           });
                           vm.page.pageNo = 1;
                           // 初始化第一页，条件为空
                           vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
                    	}
                    });
               }else
               {
            	   MainCtrl.unblockUI();
               }
            vm.delIndex = -1;
            vm.message = data.message;
            $('#static').modal();
        });
    };

    
    
    /**
     * 客户客户下单
     */
    vm.commitCustomer = function(index){
    	vm.customerOrder = vm.page.data.list[index];
    	//做确认操作
    	if(!vm.customerOrder.deliveryWay || !vm.customerOrder.currencyType || !vm.customerOrder.taxDescript)
    	{
    		 vm.message = "请填写好所有必填的内容！";
	         $('#static').modal();
	         return;
    	}
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	upida.post("crm/slCustomerOrder/getStatus", vm.customerOrder).then(function (data) {
    		if(data && data.status == "10010701"){
    			 upida.post("crm/slCustomerOrder/commitCustomer", vm.customerOrder).then(function (data) {
    		            // 重新查询数据
    		            vm.use = BaseUtil.getUser();
    		            if(vm.use.userType === "1"){
    		            	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
    		                 	if(data !=null){
    		                 		vm.edit.item.customer = data;
    		                 		 // 状态
    		                        vm.page.condition.push({
    		                            name: "customer",
    		                            value: vm.edit.item.customer
    		                        });
    		                        // 初始化第一页，条件为空
    		                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    		                 	}
    		                 });
    		            }
    		            else
    		            {
    		            	MainCtrl.unblockUI();
    		            }
    		            vm.message = data;
    		            $('#static').modal();
    		        });
    		}else{
    			// 重新查询数据
	            vm.use = BaseUtil.getUser();
	            if(vm.use.userType === "1"){
	            	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
	                 	if(data !=null){
	                 		vm.edit.item.customer = data;
	                 		 // 状态
	                        vm.page.condition.push({
	                            name: "customer",
	                            value: vm.edit.item.customer
	                        });
	                        // 初始化第一页，条件为空
	                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
	                 	}
	                 });
	            }
	            else
	            {
	            	MainCtrl.unblockUI();
	            }
    			vm.message = "不能重复操作！";
	            $('#static').modal();
    		}
    	 });
    };
    
    vm.resetFlag = function(){
    	vm.DeliverPlaceFlag = false;
        vm.customerPoFlag = false;
        vm.deliveryWayFlag = false;
        vm.currencyTypeFlag = false;
        vm.taxDescriptFlag = false;
        vm.totalAmtFlag = false;
        vm.pcsSizeFlag = false;
        vm.deliverSizeFlag = false;
        vm.customerModelFlag = false;
        vm.sampleAmtFlag = false;
        vm.boardLevelFlag = false;
        vm.materialTypeFlag = false;
        vm.boardThicknessFlag = false;
        vm.copperCladThicknessFlag = false;
        vm.surfaceProcessFlag = false;
        vm.solderMaskTypeFlag = false;
        vm.characterTypeFlag = false;
        vm.shapingWayFlag = false;
        vm.testMethodFlag = false;
        vm.lingeSpacingFlag = false;
        vm.smallApertureFlag = false;
        vm.halAholeFlag = false;
        vm.buryBlindHoleFlag = false;
        vm.resistanceFlag = false;
        vm.deliveryUrgentFlag = false;
        vm.priceFlag = false;
        vm.engineeringFeeFlag = false;
        vm.deliveryDaysFlag = false;
        vm.subTotalFlag = false;
        vm.deailAreaFlag = false;
        vm.daoreFlag = false;
        vm.naiyaFlag = false;
        vm.pliesnumberFlag = false;
        vm.deliverycityFlag = false;
    }
    
    // 总控制显示
    vm.ctrlShowDetail = function(index){
    	vm.resetFlag();
        if (vm.page.data.list[index].status === '10010701'){
        	 vm.isshow = false; //是否显示明细的添加按钮
            vm.modQuotation(index);
            
        } else {
        	 vm.isshow = false; //是否显示明细的添加按钮
        	  vm.seeQuotation(index);
        }
    };
    
    vm.seeQuotation = function(index){
    	vm.edit.detail.isAdd = false;
    	vm.edit.isAdd = false;
    	vm.customerOrder = vm.page.data.list[index];
    	//主表数据显示 赋值
    	//赋值报价人
    	for (var i = 0; i < vm.refer.userList.length; i++)
		{
			if(vm.refer.userList[i].recordId === vm.customerOrder.customeruserId.recordId){
				vm.edit.item.quoter = vm.refer.userList[i];
			}
		}
    	vm.edit.item.no = vm.customerOrder.no;
    	vm.edit.item.remark = vm.customerOrder.remark;
    	vm.edit.item = angular.copy(vm.page.data.list[index]);
    	vm.showEditForm();
    	// 如果是再次确认，需要查出修改的内容
    	if(vm.customerOrder.status == "10010704"){
    		vm.getChangeDeail();
    	}else{
        	findCustomerOrderDetailByCustomerOrder(vm.customerOrder);
    	}
    };
    
    //获取合同与客户订单的差异
    vm.getChangeDeail = function(){
    	 upida.post("crm/slCustomerOrder/getChangeDeail", vm.customerOrder).then(function (data) {
    		 if (data) {
    			    vm.page.quotationDetail.data.list = data.quotationItemList;
    			    vm.customerOrderCopy = data;
                 	angular.forEach(vm.page.quotationDetail.data.list, function(p){
             		if(p.hasOwnProperty('sampleAmt')){
             			p.unitPrice = p.price * p.sampleAmt;
             		}else{
             			p.sampleAmt = 0;
             			p.unitPrice = p.price * p.sampleAmt;
             		}
                 	p.pnlSize = "";
                 	p.pnlSize = p.unitLength + "*" + p.unitWidth;
                 	 if(p.pcsSize == p.pcsSizeCopy){
                    	    vm.pcsSizeFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.pcsSize;
                         p.pcsSize = p.pcsSizeCopy;
                         p.pcsSizeCopy = vm.replaceData;
                         p.unitWidth = p.unitWidthCopy;
                         p.unitLength = p.unitLengthCopy;
                     }
                     if(p.deliverSize == p.deliverSizeCopy){
                    	    vm.deliverSizeFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.deliverSize;
                         p.deliverSize = p.deliverSizeCopy;
                         p.deliverSizeCopy = vm.replaceData;
                         p.pnlWidth = p.pnlWidthCopy;
                         p.pnlLength = p.pnlLengthCopy;
                         p.pnlDivisor = p.pnlDivisorCopy;
                     }
                     if(p.customerModel == p.customerModelCopy){
                    	    vm.customerModelFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.customerModel;
                         p.customerModel = p.customerModelCopy;
                         p.customerModelCopy = vm.replaceData;
                     }
                     if(p.sampleAmt == p.sampleAmtCopy){
                    	    vm.sampleAmtFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.sampleAmt;
                         p.sampleAmt = p.sampleAmtCopy;
                         p.sampleAmtCopy = vm.replaceData;
                     }
                     if(p.boardLevel == p.boardLevelCopy){
                    	    vm.boardLevelFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.boardLevel;
                         p.boardLevel = p.boardLevelCopy;
                         p.boardLevelCopy = vm.replaceData;
                     }
                     if(p.materialType == p.materialTypeCopy){
                    	    vm.materialTypeFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.materialType;
                         p.materialType = p.materialTypeCopy;
                         p.materialTypeCopy = vm.replaceData;
                     }
                     if(p.boardThickness == p.boardThicknessCopy){
                    	    vm.boardThicknessFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.boardThickness;
                         p.boardThickness = p.boardThicknessCopy;
                         p.boardThicknessCopy = vm.replaceData;
                     }
                     if(p.copperCladThickness == p.copperCladThicknessCopy){
                    	    vm.copperCladThicknessFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.copperCladThickness;
                         p.copperCladThickness = p.copperCladThicknessCopy;
                         p.copperCladThicknessCopy = vm.replaceData;
                     }
                     if(p.surfaceProcess == p.surfaceProcessCopy){
                    	    vm.surfaceProcessFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.surfaceProcess;
                         p.surfaceProcess = p.surfaceProcessCopy;
                         p.surfaceProcessCopy = vm.replaceData;
                     }
                     if(p.solderMaskType == p.solderMaskTypeCopy){
                    	    vm.solderMaskTypeFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.solderMaskType;
                         p.solderMaskType = p.solderMaskTypeCopy;
                         p.solderMaskTypeCopy = vm.replaceData;
                     }
                     if(p.characterType == p.characterTypeCopy){
                    	    vm.characterTypeFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.characterType;
                         p.characterType = p.characterTypeCopy;
                         p.characterTypeCopy = vm.replaceData;
                     }
                     if(p.shapingWay == p.shapingWayCopy){
                    	    vm.shapingWayFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.shapingWay;
                         p.shapingWay = p.shapingWayCopy;
                         p.shapingWayCopy = vm.replaceData;
                     }
                     if(p.testMethod == p.testMethodCopy){
                    	    vm.testMethodFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.testMethod;
                         p.testMethod = p.testMethodCopy;
                         p.testMethodCopy = vm.replaceData;
                     }
                     if(p.lingeSpacing == p.lingeSpacingCopy){
                    	    vm.lingeSpacingFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.lingeSpacing;
                         p.lingeSpacing = p.lingeSpacingCopy;
                         p.lingeSpacingCopy = vm.replaceData;
                     }
                     if(p.smallAperture == p.smallApertureCopy){
                    	    vm.smallApertureFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.smallAperture;
                         p.smallAperture = p.smallApertureCopy;
                         p.smallApertureCopy = vm.replaceData;
                     }
                     if(p.halAhole == p.halAholeCopy){
                    	    vm.halAholeFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.halAhole;
                         p.halAhole = p.halAholeCopy;
                         p.halAholeCopy = vm.replaceData;
                     }
                     if(p.buryBlindHole == p.buryBlindHoleCopy){
                    	    vm.buryBlindHoleFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.buryBlindHole;
                         p.buryBlindHole = p.buryBlindHoleCopy;
                         p.buryBlindHoleCopy = vm.replaceData;
                     }
                     if(p.resistance == p.resistanceCopy){
                    	    vm.resistanceFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.resistance;
                         p.resistance = p.resistanceCopy;
                         p.resistanceCopy = vm.replaceData;
                     }
                     if(p.deliveryUrgent == p.deliveryUrgentCopy){
                    	    vm.deliveryUrgentFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.deliveryUrgent;
                         p.deliveryUrgent = p.deliveryUrgentCopy;
                         p.deliveryUrgentCopy = vm.replaceData;
                     }
                     if(p.daore == p.daoreCopy){
                    	 vm.daoreFlag = false;
                     }else{
                    	 vm.replaceData = "";
                         vm.replaceData = p.daore;
                         p.daore = p.daoreCopy;
                         p.daoreCopy = vm.replaceData;
                     }
                     if(p.naiya == p.naiyaCopy){
                    	 vm.naiyaFlag = false;
                     }else{
                    	 vm.replaceData = "";
                         vm.replaceData = p.naiya;
                         p.naiya = p.naiyaCopy;
                         p.naiyaCopy = vm.replaceData;
                     }
                     if(p.pliesnumber == p.pliesnumberCopy){
                    	 vm.pliesnumberFlag = false;
                     }else{
                    	 vm.replaceData = "";
                         vm.replaceData = p.pliesnumber;
                         p.pliesnumber = p.pliesnumberCopy;
                         p.pliesnumberCopy = vm.replaceData;
                     }
                     
                     if(p.price == p.priceCopy){
                    	    vm.priceFlag = false;
                     }else{
                     	p.price = vm.toDecimal3(p.price);
                 		p.priceCopy = vm.toDecimal3(p.priceCopy);
                     	vm.replaceData = "";
                         vm.replaceData = p.price;
                         p.price = p.priceCopy;
                         p.priceCopy = vm.replaceData;
                     }
                     if(p.engineeringFee == p.engineeringFeeCopy){
                    	    vm.engineeringFeeFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.engineeringFee;
                         p.engineeringFee = p.engineeringFeeCopy;
                         p.engineeringFeeCopy = vm.replaceData;
                     }
                     if(p.deliveryDays == p.deliveryDaysCopy){
                         vm.deliveryDaysFlag = false;	
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.deliveryDays;
                         p.deliveryDays = p.deliveryDaysCopy;
                         p.deliveryDaysCopy = vm.replaceData;
                     }
                     if(p.totalAmtBefore == p.subTotalCopy){
                     	vm.subTotalFlag = false;
                     }else{
                     	p.totalAmtBefore = vm.toDecimal3(p.totalAmtBefore);
                 		p.subTotalCopy = vm.toDecimal3(p.subTotalCopy);
                     	vm.replaceData = "";
                         vm.replaceData = p.totalAmtBefore;
                         p.totalAmtBefore = p.subTotalCopy;
                         p.subTotalCopy = vm.replaceData;
                     }
                     if(p.deailArea == p.deailAreaCopy){
                    	    vm.deailAreaFlag = false;
                     }else{
                     	vm.replaceData = "";
                         vm.replaceData = p.deailArea;
                         p.deailArea = p.deailAreaCopy;
                         p.deailAreaCopy = vm.replaceData;
                     }
                    });
                 	if(data.deliveryPlace == data.deliverPlaceCopy){
                   	     vm.DeliverPlaceFlag = false;
                    }else{
                    	vm.replaceData = "";
                        vm.replaceData = data.deliveryPlace;
                        data.deliveryPlace = data.deliverPlaceCopy;
                        data.deliverPlaceCopy = vm.replaceData;
                        vm.edit.item.deliveryPlace = data.deliveryPlace;
                   	    vm.DeliverPlaceFlag = true;
                    }
                 	if(data.deliverycity == data.deliverycityCopy){
                  	     vm.deliverycityFlag = false;
                   }else{
                   	vm.replaceData = "";
                       vm.replaceData = data.deliverycity;
                       data.deliverycity = data.deliverycityCopy;
                       data.deliverycityCopy = vm.replaceData;
                       vm.edit.item.deliverycity = data.deliverycity;
                  	    vm.deliverycityFlag = true;
                   }
                 	
                    if(data.customerPo == data.customerPoCopy){
                   	    vm.customerPoFlag = false;
                    }else{
                    	vm.replaceData = "";
                        vm.replaceData = data.customerPo;
                        data.customerPo = data.customerPoCopy;
                        data.customerPoCopy = vm.replaceData;
                        vm.edit.item.customerPo = data.customerPo;
                   	    vm.customerPoFlag = true;
                    }
                    if(data.deliveryWay == data.deliveryWayCopy){
                   	    vm.deliveryWayFlag = false;
                    }else{
                    	vm.replaceData = "";
                        vm.replaceData = data.deliveryWay;
                        data.deliveryWay = data.deliveryWayCopy;
                        data.deliveryWayCopy = vm.replaceData;
                        vm.edit.item.deliveryWay = data.deliveryWay;
                   	    vm.deliveryWayFlag = true;
                    }
                    if(data.currencyType == data.currencyTypeCopy){
                   	    vm.currencyTypeFlag = false;
                    }else{
                    	vm.replaceData = "";
                        vm.replaceData = data.currencyType;
                        data.currencyType = data.currencyTypeCopy;
                        data.currencyTypeCopy = vm.replaceData;
                        vm.edit.item.currencyType = data.currencyType;
                   	    vm.currencyTypeFlag = true;
                    }
                    if(data.taxDescript == data.taxDescriptCopy){
                   	    vm.taxDescriptFlag = false;
                    }else{
                    	vm.replaceData = "";
                        vm.replaceData = data.taxDescript;
                        data.taxDescript = data.taxDescriptCopy;
                        data.taxDescriptCopy = vm.replaceData;
                        vm.edit.item.taxDescript = data.taxDescript;
                   	    vm.taxDescriptFlag = true;
                    }
                    if(data.totalAmtBefore == data.totalAmtCopy){
                   	    vm.totalAmtFlag = false;
                    }else{
                   	vm.edit.item.totalAmt = 0;
                 	vm.edit.item.totalAmtBefore = 0;
                 	vm.taxTwo = 0;
                 	vm.taxTwoCopy = 0;
                 	vm.replaceData = "";
                 	if(vm.edit.item.taxDescript){
                 		angular.forEach(vm.refer.taxDescriptList, function(p){
                             if (p.recordId == vm.edit.item.taxDescript)
                             {
                             	if (p.value){
                             		if (p.value.indexOf("税") >= 0)
                                     {
                             			vm.taxTwoCopy = p.value.split("税")[1];
                                 		vm.taxTwo = Number(vm.taxTwoCopy.split("%")[0]) / 100;
                                 		data.totalAmtBefore = Number(data.totalAmt) / (1 + Number(vm.taxTwo));
                                 		data.totalAmtBefore = vm.toDecimal3(data.totalAmtBefore);
                                     }else{
                                    	 vm.edit.item.totalAmt = data.totalAmtBefore;
                                     }
                             	}
                             }
                         });
                 	}
                    vm.replaceData = data.totalAmtBefore;
                    data.totalAmtBefore = data.totalAmtCopy;
                    data.totalAmtCopy = vm.replaceData;
               	    vm.totalAmtFlag = true;
               	    vm.edit.item.totalAmtBefore = data.totalAmtBefore;
               	    vm.edit.item.totalAmt = Number(vm.edit.item.totalAmtBefore) * (1 + Number(vm.taxTwo));
                 	vm.edit.item.totalAmt = vm.toDecimal3(vm.edit.item.totalAmt);
                 	vm.edit.item.totalAmtBefore = vm.toDecimal3(vm.edit.item.totalAmtBefore);
             }
    		 }
    		 for (var j = 0; j < vm.refer.customerList.length; j++)
 			{
             	 if(vm.refer.customerList[j].recordId ===data.quotationItemList[0].customerId.recordId){
             		 vm.edit.item.customer = vm.refer.customerList[j];
             	 }
 			}
    	 });
    }
    
    /**
     * 查询明细
     */
    vm.edit.item.totalAmtBefore = 0;
    function findCustomerOrderDetailByCustomerOrder(customerOrder){
    	 upida.post("crm/slCustomerOrder/getAllByCustomerOrder", customerOrder).then(function (data) {
             if (data) {
                 vm.page.quotationDetail.data.list = data;
                 vm.edit.item.totalAmtBefore = 0;
                 vm.edit.item.totalAmt = 0;
                 vm.edit.item.area = 0;
                 	angular.forEach(vm.page.quotationDetail.data.list, function(p){
                 	vm.edit.item.area = Number(vm.edit.item.area) + Number(p.deailArea);
                 	vm.edit.item.totalAmtBefore = Number(vm.edit.item.totalAmtBefore) + Number(p.totalAmtBefore);
                 	vm.edit.item.totalAmt = Number(vm.edit.item.totalAmt) + Number(p.totalAmt);
             		if(p.hasOwnProperty('sampleAmt')){
             			p.unitPrice = p.price * p.sampleAmt;
             		}else{
             			p.sampleAmt = 0;
             			p.unitPrice = p.price * p.sampleAmt;
             		}
             		
                 	p.pnlSize = "";
                 	p.pnlSize = p.unitLength + "*" + p.unitWidth;
                 });
                 	vm.edit.item.totalAmt = vm.toDecimal3(vm.edit.item.totalAmt);
                 	vm.edit.item.totalAmtBefore = vm.toDecimal3(vm.edit.item.totalAmtBefore);
             }
             
             for (var j = 0; j < vm.refer.customerList.length; j++)
			{
            	 if(vm.refer.customerList[j].recordId ===data[0].customerId.recordId){
            		 vm.edit.item.customer = vm.refer.customerList[j];
            	 }
			}
         });
    };
    
    /**  明细 */
    vm.ctrlShowQuotationDetail = function(index){
    	loadData();
    	vm.edit.detail.formScope.detailForm.$setPristine();// 设置控制到原始状态 wc
    	if (vm.edit.isAdd){
    		vm.modQuotationDetail(index);
    	}else {
    		vm.disQuotationDetail(index);
    	}
    };
    
    // 单击显示修改
    vm.modQuotationDetail = function (index) {
        vm.edit.detail.isAdd = false;
        vm.edit.detail.btnDesc = "修 改";
        vm.edit.detail.btnTitle = "修改";
        
        vm.edit.detail.modIndex = index;
        vm.edit.detail.item = angular.copy(vm.page.quotationDetail.data.list[index]);
        // 显示detail
        vm.showDetailForm(false);
    };
    
    vm.disQuotationDetail = function(index){
        vm.edit.detail.isAdd = false;
        vm.edit.detail.btnDesc = "查 看";
        vm.edit.detail.btnTitle = "查看";
        vm.edit.detail.modIndex = index;
        vm.edit.detail.item = angular.copy(vm.page.quotationDetail.data.list[index]);
        vm.resetFlag();
         	 if(vm.edit.detail.item.pcsSize == vm.edit.detail.item.pcsSizeCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.pcsSizeFlag = false;
             }else{
            	 vm.pcsSizeFlag = true;
             }
             if(vm.edit.detail.item.deliverSize == vm.edit.detail.item.deliverSizeCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.deliverSizeFlag = false;
             }else{
            	    vm.deliverSizeFlag = true;
             }
             if(vm.edit.detail.item.customerModel == vm.edit.detail.item.customerModelCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.customerModelFlag = false;
             }else{
            	    vm.customerModelFlag = true;
             }
             if(vm.edit.detail.item.sampleAmt == vm.edit.detail.item.sampleAmtCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.sampleAmtFlag = false;
             }else{
            	    vm.sampleAmtFlag = true;
             }
             if(vm.edit.detail.item.boardLevel == vm.edit.detail.item.boardLevelCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.boardLevelFlag = false;
             }else{
            	    vm.boardLevelFlag = true;
             }
             if(vm.edit.detail.item.materialType == vm.edit.detail.item.materialTypeCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.materialTypeFlag = false;
             }else{
            	    vm.materialTypeFlag = true;
             }
             if(vm.edit.detail.item.boardThickness == vm.edit.detail.item.boardThicknessCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.boardThicknessFlag = false;
             }else{
            	    vm.boardThicknessFlag = true;
             }
             if(vm.edit.detail.item.copperCladThickness == vm.edit.detail.item.copperCladThicknessCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.copperCladThicknessFlag = false;
             }else{
            	    vm.copperCladThicknessFlag = true;
             }
             if(vm.edit.detail.item.surfaceProcess == vm.edit.detail.item.surfaceProcessCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.surfaceProcessFlag = false;
             }else{
            	    vm.surfaceProcessFlag = true;
             }
             if(vm.edit.detail.item.solderMaskType == vm.edit.detail.item.solderMaskTypeCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.solderMaskTypeFlag = false;
             }else{
            	    vm.solderMaskTypeFlag = true;
             }
             if(vm.edit.detail.item.characterType == vm.edit.detail.item.characterTypeCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.characterTypeFlag = false;
             }else{
            	    vm.characterTypeFlag = true;
             }
             if(vm.edit.detail.item.shapingWay == vm.edit.detail.item.shapingWayCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.shapingWayFlag = false;
             }else{
            	    vm.shapingWayFlag = true;
             }
             if(vm.edit.detail.item.testMethod == vm.edit.detail.item.testMethodCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.testMethodFlag = false;
             }else{
            	    vm.testMethodFlag = true;
             }
             if(vm.edit.detail.item.lingeSpacing == vm.edit.detail.item.lingeSpacingCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.lingeSpacingFlag = false;
             }else{
            	    vm.lingeSpacingFlag = true;
             }
             if(vm.edit.detail.item.smallAperture == vm.edit.detail.item.smallApertureCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.smallApertureFlag = false;
             }else{
            	    vm.smallApertureFlag = true;
             }
             if(vm.edit.detail.item.halAhole == vm.edit.detail.item.halAholeCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.halAholeFlag = false;
             }else{
            	    vm.halAholeFlag = true;
             }
             if(vm.edit.detail.item.buryBlindHole == vm.edit.detail.item.buryBlindHoleCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.buryBlindHoleFlag = false;
             }else{
            	    vm.buryBlindHoleFlag = true;
             }
             if(vm.edit.detail.item.resistance == vm.edit.detail.item.resistanceCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.resistanceFlag = false;
             }else{
            	    vm.resistanceFlag = true;
             }
             if(vm.edit.detail.item.deliveryUrgent == vm.edit.detail.item.deliveryUrgentCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.deliveryUrgentFlag = false;
             }else{
            	    vm.deliveryUrgentFlag = true;
             }
             if(vm.edit.detail.item.price == vm.edit.detail.item.priceCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.priceFlag = false;
             }else{
            	    vm.priceFlag = true;
             }
             if(vm.edit.detail.item.engineeringFee == vm.edit.detail.item.engineeringFeeCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.engineeringFeeFlag = false;
             }else{
            	    vm.engineeringFeeFlag = true;
             }
             if(vm.edit.detail.item.deliveryDays == vm.edit.detail.item.deliveryDaysCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
                 vm.deliveryDaysFlag = false;	
             }else{
            	    vm.deliveryDaysFlag = true;
             }
             if(vm.edit.detail.item.totalAmtBefore == vm.edit.detail.item.subTotalCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
             	vm.subTotalFlag = false;
             }else{
            	    vm.subTotalFlag = true;
             }
             if(vm.edit.detail.item.deailArea == vm.edit.detail.item.deailAreaCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	    vm.deailAreaFlag = false;
             }else{
            	    vm.deailAreaFlag = true;
             }
             if(vm.edit.detail.item.daore == vm.edit.detail.item.daoreCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	 vm.daoreFlag = false;
             }else{
            	 vm.daoreFlag = true;
             }
             if(vm.edit.detail.item.naiya == vm.edit.detail.item.naiyaCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	 vm.naiyaFlag = false;
             }else{
            	 vm.naiyaFlag = true;
             }
             if(vm.edit.detail.item.pliesnumber == vm.edit.detail.item.pliesnumberCopy || vm.edit.item.status == '10010702' || vm.edit.item.status == '10010705' || vm.edit.item.status == '10010706'){
            	 vm.pliesnumberFlag = false;
             }else{
            	 vm.pliesnumberFlag = true;
             }
             
        // 显示detail
        vm.showDetailForm(false);
    };
    
    // 选择客户
    vm.selectCustomer = function () {
        if (angular.isDefined(vm.editManItem.customer.recordId)) {
            // 初始化第一页，条件为空
            vm.init(1, vm.manPage.pageSize, [{
                name: "customer.recordId",
                value: vm.editManItem.customer.recordId
            }], vm.manPage.url, vm.editManItem.customer.bizPerson.name);
        } else {
            // 添加的临时数据
            vm.manPage.data.list = vm.tempContacts;
        }
    };

    // 单击显示修改
    vm.modContact = function (index) {
        vm.isManAdd = false;
        vm.editContactBtnDesc = "修 改";

        vm.modManItemIndex = index;
        var data = vm.editManItem.customer;
        vm.editManItem = angular.copy(vm.manPage.data.list[index]);
        vm.editManItem.phone = vm.editManItem.phone.replace(/\s+/g,"");
        vm.editManItem.customer = data;
    };

    // 重置联系人处理
    vm.resetContact = function () {
        vm.isManAdd = true;
        vm.modManItemIndex = -1;
        var data = vm.editManItem.customer;
        vm.editManItem = {customer: {}};
        vm.editManItem.customer = data;
        vm.editContactBtnDesc = "添 加";
    };

    // 创建和修改联系人
    vm.submitModContact = function (form) {
        form.$setDirty();
        if (!form.$valid) {
            return;
        }
        
        // 添加到临时列表
        var name = "";
        for(x in vm.manPage.data.list){
        	if (vm.manPage.data.list[x].isDefault){
        		name = vm.manPage.data.list[x].name; 
        		break;
        	}
        }
        
        // 当前是添加客户的时候，联系人添加至临时列表即可，在保存客户的时候保存联系人
        if (vm.isAdd) {
            // 判断名称是否存在相同的
            var find = false;
            for (var i = 0; i < vm.tempContacts.length; i++) {
                if (vm.editManItem.name === vm.tempContacts[i].name) {
                    find = true;
                    break;
                }
            }

            if (find) {
                if (vm.isManAdd) {
                    vm.message = "添加客户联系人失败，存在相同的姓名！";
                } else {
                    vm.message = "修改客户联系人失败，存在相同的姓名！";
                }

                return;
            }

            vm.manPage.data.list = vm.tempContacts;
            var tempCustomer = vm.editManItem.customer;
            var data = vm.editManItem;
            data.customer = null;
            if (vm.isManAdd) {
                vm.message = "添加客户联系人成功！";
                vm.tempContacts.push(data);
            } else {
                vm.message = "修改客户联系人成功！";
                vm.tempContacts[vm.modManItemIndex] = data;
            }
            
            // 显示默认联系人
            vm.setDefaultContactByName(name);
            
            vm.editManItem = {customer: {}};
            vm.editManItem.customer = tempCustomer;

            // 重置数据
            vm.resetContact();
            form.$setPristine();
            $('#static').modal();
            return;
        }

        // 判断是否存在相同名称的客户联系人
        MainCtrl.blockUI({
    	    animate: true,
    	});
        
        upida.get("crm/contact/exist?customerId=" + vm.editManItem.customer.recordId + "&name=" + vm.editManItem.name).then(function (data) {
            if (data) {
                if (vm.isManAdd) {
                    vm.message = "添加客户联系人失败，存在相同的姓名！";
                } else {
                    vm.message = "修改客户联系人失败，存在相同的姓名！";
                }
                
                $('#static').modal();
                MainCtrl.unblockUI();
                return;
            }

            // 设置客户的信息
            var reqData = angular.copy(vm.editManItem);
            reqData.customer = {recordId: vm.editManItem.customer.recordId};

            // 提交数据
            upida.post("crm/contact/save", reqData).then(function (data) {
                if (!data.result) {
                	 MainCtrl.unblockUI();
                    // 提示信息
                    vm.message = data.message;
                    $('#static').modal();
                } else {
                    // 重新查询数据
                    vm.init(vm.manPage.pageNo,
                        vm.manPage.pageSize,
                        [{
                            name: "customer.recordId",
                            value: reqData.customer.recordId
                        }],
                        vm.manPage.url, name);

                    // 重置数据
                    vm.resetContact();
                    form.$setPristine();

                    // 提示信息
                    vm.message = data.message;
                    $('#static').modal();
                }
            });
        });
    };

   vm.setDefaultContactByName = function (name) {
   	 if (name){
    	for(x in vm.manPage.data.list){
    		vm.manPage.data.list[x].isDefault = vm.manPage.data.list[x].name == name;
    	}
     }else {
    	 for(x in vm.manPage.data.list){
     		vm.manPage.data.list[x].isDefault = false;
     	} 
     }
   };


    
    /**
     * 客户编码失去焦点事件
     */
    vm.clickNgblur = function(no){
    	if (no){
        	//去后台查询此编号有没有被用掉
        	upida.post("crm/customer/selectRepeat/" + no, null).then(function (data) {
               if(data > 0){
            	   vm.message = "已经存在此客户编码！";
                   $('#static').modal();
               }
            });
    	}
    };
    
    // 选择了业务员
    vm.selectsalesman = function (value) {
    	//vm.seedeadlineFlag = false;//是否设置出单时间限制
        if (value != "" && "undefined" != value) {
        	vm.seedeadlineFlag = true;
        } 
    };
    
    // 清除
    vm.clearUpload = function (index) {
        vm.uFiles.splice(index, 1);
    };
    
    //查询对应客户的对账单
    vm.showreceivable = function(){
	    vm.use = BaseUtil.getUser();
        if(vm.use.userType === "1"){
        	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
             	if(data !=null){
             			//根据客户编号去查询对账单
             		 upida.post("crm/slCustomerOrder/getReceivableByCus",data).then(function(dataes){
             			 vm.edit.r = {};
             			 vm.edit.r.item = {};
             			 vm.edit.r.item =  dataes;
             			vm.edit.r.item.customer = data;
             		 });	
             	}
             });
        }
    };
    
    // 加载权限
    function loadRight() {
        vm.queryAll = CommonUtil.dataRangeIsAll("10107", BaseUtil.getMenuList());
        MainCtrl.blockUI({
    	    animate: true,
    	});
        upida.get("common/rightall?prefix=crm:customerorder").then(function (data) {
            vm.right.view = data.view;
            vm.right.edit = data.edit;
            vm.right.manage = data.manage;

            // 有编辑权限才进行加载素有角色和所有部门的数据
            loadData();
            //根据用户编号 去查询是属于哪个客户的  把客户信息返回
            vm.use = BaseUtil.getUser();
            if(vm.use.userType === "1"){
            	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
                 	if(data !=null){
                 		vm.edit.item.customer = data;
                 		 // 状态
                        vm.page.condition.push({
                            name: "customer",
                            value: vm.edit.item.customer
                        });
                        vm.page.pageNo = 1;
                        // 初始化第一页，条件为空
                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
                 	}
                 });
            }else{
            	   vm.page.pageNo = 1;
                   // 初始化第一页，条件为空
                   vm.init(vm.page.pageNo, vm.page.pageSize, [], vm.page.url);
            }
        });
    }
    
    
    
    // 显示明细窗口
    vm.showDetailForm = function (isAdd) {
        if (isAdd) {
            vm.edit.detail.item = {};
            vm.edit.detail.item.customerorderId = {};
            vm.edit.detail.item.customerId = {};
            vm.edit.detail.btnDesc = "添 加";
            vm.edit.detail.btnTitle = "添加";
            vm.edit.detail.isAdd = true;
            vm.edit.detail.item.price = 0.000;
        }
        
        vm.focus.detail += 1;
        
        vm.tabs.detailForm.show = true;
        $timeout(function () {
            vm.tabs.detailForm.active = true;
        });
    };
    
    
    // 隐藏明细窗口
    vm.hideDetailForm = function () {
        vm.tabs.detailForm.show = false;
        vm.tabs.detailForm.active = true;

        $timeout(function () {
            if (vm.tabs.editForm.show) {
                vm.tabs.editForm.active = true;
            } else {
                vm.tabs.viewForm.active = true;
            }
        });
    };
    
    
    // 重置处理
    vm.resetQuotationDetail = function () {
        vm.edit.detail.item.unitLength = "";
        vm.edit.detail.item.unitWidth = "";
        vm.edit.detail.item.pnlLength = "";
        vm.edit.detail.item.pnlWidth = "";
        vm.edit.detail.item.pnlDivisor = "";
        vm.edit.detail.item.customerModel = "";
        vm.edit.detail.item.sampleAmt = "";
        
        vm.edit.detail.item.boardLevel = "";
        vm.edit.detail.item.materialType = "";
        vm.edit.detail.item.boardThickness = "";
        vm.edit.detail.item.copperCladThickness = "";
        vm.edit.detail.item.surfaceProcess = "";
        vm.edit.detail.item.solderMaskType = "";
        vm.edit.detail.item.characterType = "";
        vm.edit.detail.item.shapingWay = "";
        vm.edit.detail.item.testMethod = "";
        vm.edit.detail.item.lingeSpacing = "";
        vm.edit.detail.item.smallAperture = "";
        vm.edit.detail.item.halAhole = "";
        vm.edit.detail.item.buryBlindHole = "";
        vm.edit.detail.item.resistance = "";
        vm.edit.detail.item.deliveryUrgent = "";
        
        vm.edit.detail.item.daore = "";
        vm.edit.detail.item.naiya = "";
        vm.edit.detail.item.pliesnumber = "";
        
        vm.edit.detail.item.deliveryDays = "";
        vm.edit.detail.item.remark = "";
        
        vm.edit.detail.item.price = "";
        vm.edit.detail.item.engineeringFee = "";
        vm.edit.detail.item.totalAmtBefore = "";
        vm.edit.detail.item.deailArea = "";
    };
    // 获取对应的价格
    vm.getPrice = function(id, objs){
         vm.itemPrice = 0.000;
         angular.forEach(objs, function(p){
             if (p.recordId == id)
             {
             	if (p.craftPrice){
             		vm.itemPrice = p.craftPrice.price;
             	}
             }
         });
         return vm.itemPrice;
     }
    // 设置每平米工艺单价
    vm.edit.detail.item.price = 0.000;
    // 设置当前明细的单价
    vm.doSetPrice = function () {
    	vm.edit.detail.item.price = 0;
    		if(vm.edit.detail.item.boardLevel){
        		vm.boardLevelPrice = 0;
        		vm.boardLevelPrice = vm.getPrice(vm.edit.detail.item.boardLevel, vm.refer.boardLevelList);
        		vm.edit.detail.item.price = vm.edit.detail.item.price + vm.boardLevelPrice;
        	}
        	if(vm.edit.detail.item.materialType){
        		vm.materialTypePrice = 0;
        		vm.materialTypePrice = vm.getPrice(vm.edit.detail.item.materialType, vm.refer.materialTypeList);
        		vm.edit.detail.item.price = vm.edit.detail.item.price + vm.materialTypePrice;
        	}
            if(vm.edit.detail.item.boardThickness){
            	vm.boardThicknessPrice = 0;
            	vm.boardThicknessPrice = vm.getPrice(vm.edit.detail.item.boardThickness, vm.refer.boardThicknessList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.boardThicknessPrice;
        	}
            if(vm.edit.detail.item.copperCladThickness){
            	vm.copperCladThicknessPrice = 0;
            	vm.copperCladThicknessPrice = vm.getPrice(vm.edit.detail.item.copperCladThickness, vm.refer.copperCladThicknessList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.copperCladThicknessPrice;
        	}
            if(vm.edit.detail.item.surfaceProcess){
            	vm.surfaceProcessPrice = 0;
            	vm.surfaceProcessPrice = vm.getPrice(vm.edit.detail.item.surfaceProcess, vm.refer.surfaceProcessList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.surfaceProcessPrice;
        	}
            if(vm.edit.detail.item.solderMaskType){
            	vm.solderMaskTypePrice = 0;
            	vm.solderMaskTypePrice = vm.getPrice(vm.edit.detail.item.solderMaskType, vm.refer.solderMaskTypeList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.solderMaskTypePrice;
        	}
            if(vm.edit.detail.item.characterType){
            	vm.characterTypePrice = 0;
            	vm.characterTypePrice = vm.getPrice(vm.edit.detail.item.characterType, vm.refer.characterTypeList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.characterTypePrice;
        	}
            if(vm.edit.detail.item.shapingWay){
            	vm.shapingWayPrice = 0;
            	vm.shapingWayPrice = vm.getPrice(vm.edit.detail.item.shapingWay, vm.refer.shapingWayList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.shapingWayPrice;
        	}
            if(vm.edit.detail.item.testMethod){
            	vm.testMethodPrice = 0;
            	vm.testMethodPrice = vm.getPrice(vm.edit.detail.item.testMethod, vm.refer.testMethodList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.testMethodPrice;
        	}
            if(vm.edit.detail.item.lingeSpacing){
            	vm.lingeSpacingPrice = 0;
            	vm.lingeSpacingPrice = vm.getPrice(vm.edit.detail.item.lingeSpacing, vm.refer.lingeSpacingList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.lingeSpacingPrice;
        	}
            if(vm.edit.detail.item.smallAperture){
            	vm.smallAperturePrice = 0;
            	vm.smallAperturePrice = vm.getPrice(vm.edit.detail.item.smallAperture, vm.refer.smallApertureList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.smallAperturePrice;
        	}
            if(vm.edit.detail.item.halAhole){
            	vm.halAholePrice = 0;
            	vm.halAholePrice = vm.getPrice(vm.edit.detail.item.halAhole, vm.refer.halAholeList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.halAholePrice;
        	}
            if(vm.edit.detail.item.buryBlindHole){
            	vm.buryBlindHolePrice = 0;
            	vm.buryBlindHolePrice = vm.getPrice(vm.edit.detail.item.buryBlindHole, vm.refer.buryBlindHoleList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.buryBlindHolePrice;
        	}
            if(vm.edit.detail.item.resistance){
            	vm.resistancePrice = 0;
            	vm.resistancePrice = vm.getPrice(vm.edit.detail.item.resistance, vm.refer.resistanceList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.resistancePrice;
        	}
            if(vm.edit.detail.item.deliveryUrgent){
            	vm.deliveryUrgentPrice = 0;
            	vm.deliveryUrgentPrice = vm.getPrice(vm.edit.detail.item.deliveryUrgent, vm.refer.deliveryUrgentList);
            	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.deliveryUrgentPrice;
        	}
    	
        if(vm.edit.detail.item.daore){
        	vm.daorePrice = 0;
        	vm.daorePrice = vm.getPrice(vm.edit.detail.item.daore, vm.refer.daoreList);
        	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.daorePrice;
    	}
        if(vm.edit.detail.item.naiya){
        	vm.naiyaPrice = 0;
        	vm.naiyaPrice = vm.getPrice(vm.edit.detail.item.naiya, vm.refer.naiyaList);
        	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.naiyaPrice;
    	}
        if(vm.edit.detail.item.pliesnumber){
        	vm.pliesnumberPrice = 0;
        	vm.pliesnumberPrice = vm.getPrice(vm.edit.detail.item.pliesnumber, vm.refer.pliesnumberList);
        	vm.edit.detail.item.price = vm.edit.detail.item.price + vm.pliesnumberPrice;
    	}
        
        vm.lenthCopy = 0;
    	vm.widthCopy = 0;
    	vm.divisor = 1;
    	if(vm.edit.detail.item.pnlLength){
    		vm.lenthCopy = vm.edit.detail.item.pnlLength;
    	}else if(vm.edit.detail.item.unitLength){
    		vm.lenthCopy = vm.edit.detail.item.unitLength;
    	}else{
    		vm.lenthCopy = 0;
    	}
    	if(vm.edit.detail.item.pnlWidth){
    		vm.widthCopy = vm.edit.detail.item.pnlWidth;
    	}else if(vm.edit.detail.item.unitWidth){
    		vm.widthCopy = vm.edit.detail.item.unitWidth;
    	}else{
    		vm.widthCopy = 0;
    	}
    	if(vm.edit.detail.item.pnlDivisor && vm.edit.detail.item.pnlDivisor != 0){
    		vm.divisor = vm.edit.detail.item.pnlDivisor;
    	}else{
    		vm.divisor = 1;
    	}
        vm.edit.detail.item.totalAmtBefore = 0;
        if(vm.lenthCopy && vm.widthCopy && vm.lenthCopy != 0 && vm.widthCopy != 0 && vm.edit.detail.item.sampleAmt){
        	vm.orderArea = 0;
        	vm.orderArea = Number(vm.lenthCopy) * Number(vm.widthCopy) * Number(vm.edit.detail.item.sampleAmt) / Number(vm.divisor);
        	vm.edit.detail.item.engineeringFee = vm.calculateAmtByPnlAreaScope(vm.refer.engineeringfeeList,vm.orderArea);
        	vm.edit.detail.item.deailArea = vm.orderArea / 1000000;
        	if(vm.edit.detail.item.engineeringFee){
            	vm.edit.detail.item.totalAmtBefore = (vm.edit.detail.item.price + vm.edit.detail.item.engineeringFee) * vm.orderArea / 1000000;
            }else{
            	vm.edit.detail.item.totalAmtBefore = vm.edit.detail.item.price * vm.orderArea / 1000000;
            }
        }
        vm.edit.detail.item.price = vm.toDecimal3(vm.edit.detail.item.price);
        vm.edit.detail.item.engineeringFee = vm.toDecimal3(vm.edit.detail.item.engineeringFee);
        vm.edit.detail.item.totalAmtBefore = vm.toDecimal3(vm.edit.detail.item.totalAmtBefore);
        vm.edit.detail.item.deailArea = vm.toDecimal3(vm.edit.detail.item.deailArea);
        vm.doInvoicePrice();
    }
    
    vm.edit.item.totalAmt = 0;
	vm.edit.item.totalAmtBefore = 0;
    //计算所有税后明细价格
    vm.doAllInvoicePrice = function () {
    	vm.edit.item.totalAmt = 0;
    	vm.edit.item.totalAmtBefore = 0;
    	vm.taxTwo = 0;
    	vm.taxTwoCopy = 0;
    	if(vm.edit.item.taxDescript){
    		angular.forEach(vm.refer.taxDescriptList, function(p){
                if (p.recordId == vm.edit.item.taxDescript)
                {
                	if (p.value){
                		if (p.value.indexOf("税") >= 0)
                        {
                			vm.taxTwoCopy = p.value.split("税")[1];
                    		vm.taxTwo = Number(vm.taxTwoCopy.split("%")[0]) / 100;
                    		angular.forEach(vm.page.quotationDetail.data.list, function(data){
                    			data.totalAmt = Number(data.totalAmtBefore) * (1 + Number(vm.taxTwo));
                    			data.totalAmt = vm.toDecimal3(data.totalAmt);
                    			vm.edit.item.totalAmtBefore = Number(vm.edit.item.totalAmtBefore) + Number(data.totalAmtBefore);
                    			vm.edit.item.totalAmt = Number(vm.edit.item.totalAmt) + Number(data.totalAmt);
                            });
                        }else{
                        	angular.forEach(vm.page.quotationDetail.data.list, function(data){
                        		data.totalAmt = data.totalAmtBefore;
                        		data.totalAmt = vm.toDecimal3(data.totalAmt);
                        		vm.edit.item.totalAmtBefore = Number(vm.edit.item.totalAmtBefore) + Number(data.totalAmtBefore);
                    			vm.edit.item.totalAmt = Number(vm.edit.item.totalAmt) + Number(data.totalAmt);
                            });
                        }
                	}
                }
            });
    	}
    	vm.edit.item.totalAmt = vm.toDecimal3(vm.edit.item.totalAmt);
    	vm.edit.item.totalAmtBefore = vm.toDecimal3(vm.edit.item.totalAmtBefore);
    }
    
    //计算税后明细价格
    vm.doInvoicePrice = function () {
    	vm.taxTwo = 0;
    	vm.taxTwoCopy = 0;
    	if(vm.edit.item.taxDescript){
    		angular.forEach(vm.refer.taxDescriptList, function(p){
                if (p.recordId == vm.edit.item.taxDescript)
                {
                	if (p.value){
                		if (p.value.indexOf("税") >= 0)
                        {
                			vm.taxTwoCopy = p.value.split("税")[1];
                    		vm.taxTwo = vm.taxTwoCopy.split("%")[0] / 100;
                    		vm.edit.detail.item.totalAmt = vm.edit.detail.item.totalAmtBefore * (1 + vm.taxTwo);
                        }else{
                        	vm.edit.detail.item.totalAmt = vm.edit.detail.item.totalAmtBefore;
                        }
                		vm.edit.detail.item.totalAmt = vm.toDecimal3(vm.edit.detail.item.totalAmt);
                	}
                }
            });
    	}
    }
    
    vm.toDecimal3 = function(x) {    
        var f = parseFloat(x);    
        if (isNaN(f)) {    
            return 0.000;    
        }    
        var f = Math.round(x*1000)/1000;    
        var s = f.toString();    
        var rs = s.indexOf('.');    
        if (rs < 0) {    
            rs = s.length;    
            s += '.';    
        }    
        while (s.length <= rs + 3) {    
            s += '0';    
        }    
        return s;    
     }    
    
    // 获取对应的价格
    vm.getItemValue = function(id, objs){
    	if(id){
    		angular.forEach(objs, function(p){
                if (p.recordId == id)
                {
                	vm.itemValue = p.value;
                }
            });
    	}else{
    		vm.itemValue = "";
    	}
         return vm.itemValue;
     }
    
    // 创建和修改明细
    vm.submitModDetail = function () {
    	 MainCtrl.blockUI({
     	    animate: true,
     	});
    	upida.post("crm/slCustomerOrder/getStatus", vm.edit.item).then(function (data) {
    		if(data && data.status == "10010701"){
    			vm.edit.detail.formScope.detailForm.$setDirty();
    	    	 if (!vm.edit.detail.formScope.detailForm.$valid) {
    	    		 MainCtrl.unblockUI();
    	             return;
    	         }
    	    	 if((!vm.edit.detail.item.pnlLength || vm.edit.detail.item.pnlLength == "") && (!vm.edit.detail.item.pnlWidth || vm.edit.detail.item.pnlWidth == ""))
    	  		{
    	  			vm.edit.detail.item.pnlLength = vm.edit.detail.item.unitLength;
    	  			vm.edit.detail.item.pnlWidth = vm.edit.detail.item.unitWidth;
    	  		}else{
    	  			if((!vm.edit.detail.item.pnlLength || vm.edit.detail.item.pnlLength == "") || (!vm.edit.detail.item.pnlWidth || vm.edit.detail.item.pnlWidth == ""))
    	  			{
    	  				vm.message = "请填写好交货尺寸的长与宽，如果使用默认请都不填！";
    	  				$('#static').modal();
    	  				MainCtrl.unblockUI();
    	  				return;
    	  			}
    	  		}
    	  		if(!vm.edit.detail.item.pnlDivisor || vm.edit.detail.item.pnlDivisor == "")
    	  		{
    	  			vm.edit.detail.item.pnlDivisor = 1;
    	  		}
    	    	 if(Number(vm.edit.detail.item.pnlLength) < Number(vm.edit.detail.item.unitLength)) {
    	  			vm.message = "交货尺寸的长不能小于PCS尺寸的长！";
    	  			$('#static').modal();
    	  			MainCtrl.unblockUI();
    	  			return;
    	  		}
    	  		if(Number(vm.edit.detail.item.pnlWidth) < Number(vm.edit.detail.item.unitWidth)) {
    	  			vm.message = "交货尺寸的宽不能小于PCS尺寸的宽！";
    	  			$('#static').modal();
    	  			MainCtrl.unblockUI();
    	  			return;
    	  		}
    	        // 当前是添加报价的时候，明细添加至临时列表即可，在保存报价的时候保存明细
    	        if (!vm.isAdd) {
    	            // 设置报价ID
    	        	vm.edit.detail.item.customerorderId = {};
    	            vm.edit.detail.item.customerorderId.recordId = vm.edit.item.recordId;
    	            vm.edit.detail.item.customerId = {};
    	            vm.edit.detail.item.customerId.recordId = vm.edit.item.customer.recordId;
    	            // 提交数据
    	            upida.post("crm/slCustomerOrder/detail/update", vm.edit.detail.item).then(function (data) {
    	                if (!data.result) {
    	                    // 提示信息
    	                    vm.message = data.message;
    	                    $('#static').modal();
    	                    MainCtrl.unblockUI();
    	                } else {
    	                    vm.edit.detail.formScope.detailForm.$setPristine();
    	                    // 提示信息
    	                    vm.message = "修改下单明细成功！";
    	                    //重新查询明细 放到列表中
    	                    findCustomerOrderDetailByCustomerOrder(vm.edit.item);
    	                    $('#static').modal();
    	                    MainCtrl.unblockUI();
    	                }
    	            });
    	        }else
    	        {
    	        	MainCtrl.unblockUI();
    	        }
    	        vm.hideDetailForm();
    		}else{
    			// 重新查询数据
	            vm.use = BaseUtil.getUser();
	            if(vm.use.userType === "1"){
	            	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
	                 	if(data !=null){
	                 		vm.edit.item.customer = data;
	                 		 // 状态
	                        vm.page.condition.push({
	                            name: "customer",
	                            value: vm.edit.item.customer
	                        });
	                        // 初始化第一页，条件为空
	                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
	                 	}
	                 	else
	                 	{
	                 		MainCtrl.unblockUI();
	                 	}
	                 });
	            }
	            else
	            {
	            	MainCtrl.unblockUI();	
	            }
    			vm.message = "不能重复操作！";
	            $('#static').modal();
    		}
    	 });
    };
    
    // 显示删除明细
    vm.delQuotationDetail = function (index) {
        vm.edit.detail.delIndex = index;
        vm.edit.detail.delMsg = "您确定要删除客户型号为“"+ vm.page.quotationDetail.data.list[index].customerModel +"”的明细项吗？";
        $('#staticDetailRemove').modal();
    };
    
    // 做删除操作
    vm.doDelQuotationDetail = function () {
        var delId = vm.page.quotationDetail.data.list[vm.edit.detail.delIndex].recordId;
        MainCtrl.blockUI({
    	    animate: true,
    	});
        // 根据Id删除联系人
        upida.post("crm/slCustomerOrder/detail/delete/" + delId).then(function (data) {
            // 重新查询数据
        	findCustomerOrderDetailByCustomerOrder( vm.edit.item);
            vm.edit.detail.delIndex = -1;
            vm.message = data.message;
            $('#static').modal();
            
            MainCtrl.unblockUI();
        });
    };
    
    //根据价格区间面积获取价格
    vm.calculateAmtByPnlAreaScope = function(calList,pnlArea){
    	vm.beforeScope = 0;//前置范围
		vm.afterScope = 0;//后置范围
		vm.scopePrice = 0;
    	if(calList && pnlArea){
    		pnlArea = pnlArea / 1000000;
    		angular.forEach(calList, function(data, key) {
    		    vm.beforeScope = data.value.split("-")[0];//前置范围
  			    vm.afterScope = data.value.split("-")[1];//后置范围
  			    if(parseInt(vm.beforeScope) <= parseInt(pnlArea) && parseInt(pnlArea) <= parseInt(vm.afterScope)){//判断面积是否超出前置后置
  			    	if(data.craftPrice){
  			    		vm.scopePrice = data.craftPrice.price;//价格
  			    	}
				    return vm.scopePrice;
			    }
			});
    	}
		return vm.scopePrice;
    }
    
    //接受
    vm.acceptChange = function(index){
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	vm.customerOrder = vm.page.data.list[index];
    	upida.post("crm/slCustomerOrder/getStatus", vm.customerOrder).then(function (data) {
    		if(data && data.status == "10010704"){
    	        upida.post("crm/slCustomerOrder/acceptChange", vm.customerOrder).then(function (data) {
    	            vm.use = BaseUtil.getUser();
    	            if(vm.use.userType === "1"){
    	            	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
    	                 	if(data !=null){
    	                 		vm.edit.item.customer = data;
    	                 		 // 状态
    	                        vm.page.condition.push({
    	                            name: "customer",
    	                            value: vm.edit.item.customer
    	                        });
    	                        // 初始化第一页，条件为空
    	                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    	                 	}
    	                 	else
    	                 	{
    	                 		MainCtrl.unblockUI();
    	                 	}
    	                 });
    	            }
    	            else
    	            {
    	            	MainCtrl.unblockUI();
    	            }
    	            vm.message = data;
    	            $('#static').modal();
    	        });
    		}else{
    			// 重新查询数据
	            vm.use = BaseUtil.getUser();
	            if(vm.use.userType === "1"){
	            	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
	                 	if(data !=null){
	                 		vm.edit.item.customer = data;
	                 		 // 状态
	                        vm.page.condition.push({
	                            name: "customer",
	                            value: vm.edit.item.customer
	                        });
	                        // 初始化第一页，条件为空
	                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
	                 	}
	                 	else
	                 	{
	                 		MainCtrl.unblockUI();
	                 	}
	                 });
	            }
	            else
	            {
	            	MainCtrl.unblockUI();
	            }
    			vm.message = "不能重复操作！";
	            $('#static').modal();
    		}
    	 });
    }
    
    //拒绝
    vm.refuseChange = function(index){
    	MainCtrl.blockUI({
    	   animate: true,
    	});
    	vm.customerOrder = vm.page.data.list[index];
    	upida.post("crm/slCustomerOrder/getStatus", vm.customerOrder).then(function (data) {
    		if(data && data.status == "10010704"){
    	        upida.post("crm/slCustomerOrder/refuseChange", vm.customerOrder).then(function (data) {
    	            vm.use = BaseUtil.getUser();
    	            if(vm.use.userType === "1"){
    	            	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
    	                 	if(data !=null){
    	                 		vm.edit.item.customer = data;
    	                 		 // 状态
    	                        vm.page.condition.push({
    	                            name: "customer",
    	                            value: vm.edit.item.customer
    	                        });
    	                        // 初始化第一页，条件为空
    	                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    	                 	}
    	                 	else
    	                 	{
    	                 		MainCtrl.unblockUI();
    	                 	}
    	                 });
    	            }
    	            else
    	            {
    	            	MainCtrl.unblockUI();
    	            }
    	            vm.message = data;
    	            $('#static').modal();
    	        });
    		}else{
    			// 重新查询数据
	            vm.use = BaseUtil.getUser();
	            if(vm.use.userType === "1"){
	            	 upida.post("crm/slCustomerOrder/getCusByUser",BaseUtil.getUser()).then(function(data){
	                 	if(data !=null){
	                 		vm.edit.item.customer = data;
	                 		 // 状态
	                        vm.page.condition.push({
	                            name: "customer",
	                            value: vm.edit.item.customer
	                        });
	                        // 初始化第一页，条件为空
	                        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
	                 	}
	                 	else
	                 	{
	                 		MainCtrl.unblockUI();
	                 	}
	                 });
	            }
	            else
	            {
	            	MainCtrl.unblockUI();
	            }
    			vm.message = "不能重复操作！";
	            $('#static').modal();
    		}
    	 });
    }
    
    vm.itemName = "";
    vm.valueNameOne = "";
    vm.valueNameTwo = "";
    vm.compareChange = function(index){
    	if(index == 1){
    		vm.itemName = "收货地点";
    		vm.valueNameTwo = vm.customerOrderCopy.deliveryPlace;
    		vm.valueNameOne = vm.customerOrderCopy.deliverPlaceCopy;
    	}
    	if(index == 2){
    		vm.itemName = "订单号";
    		vm.valueNameTwo = vm.customerOrderCopy.customerPo;
    		vm.valueNameOne = vm.customerOrderCopy.customerPoCopy;
    	}
    	if(index == 3){
    		vm.itemName = "送货方式";
    		vm.valueNameTwo = vm.getItemValue(vm.customerOrderCopy.deliveryWay,vm.refer.deliverywaysList);
    		vm.valueNameOne = vm.getItemValue(vm.customerOrderCopy.deliveryWayCopy,vm.refer.deliverywaysList);
    	}
    	if(index == 4){
    		vm.itemName = "货币类型";
    		vm.valueNameTwo = vm.getItemValue(vm.customerOrderCopy.currencyType,vm.refer.currencyTypeList);
    		vm.valueNameOne = vm.getItemValue(vm.customerOrderCopy.currencyTypeCopy,vm.refer.currencyTypeList);
    	}
    	if(index == 5){
    		vm.itemName = "税种说明";
    		vm.valueNameTwo = vm.getItemValue(vm.customerOrderCopy.taxDescript,vm.refer.taxDescriptList);
    		vm.valueNameOne = vm.getItemValue(vm.customerOrderCopy.taxDescriptCopy,vm.refer.taxDescriptList);
    	}
    	if(index == 6){
    		vm.itemName = "税前价格";
    		vm.valueNameTwo = vm.customerOrderCopy.totalAmtBefore;
    		vm.valueNameOne = vm.customerOrderCopy.totalAmtCopy;
    	}
    	if(index == 8){
    		vm.itemName = "客户型号";
    		vm.valueNameTwo = vm.edit.detail.item.customerModel;
    		vm.valueNameOne = vm.edit.detail.item.customerModelCopy;
    	}
    	if(index == 9){
    		vm.itemName = "PCS数量";
    		vm.valueNameTwo = vm.edit.detail.item.sampleAmt;
    		vm.valueNameOne = vm.edit.detail.item.sampleAmtCopy;
    	}
    	if(index == 10){
    		vm.itemName = "PCB类型";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.boardLevel,vm.refer.boardLevelList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.boardLevelCopy,vm.refer.boardLevelList);
    	}
    	if(index == 11){
    		vm.itemName = "覆铜板材";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.materialType,vm.refer.materialTypeList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.materialTypeCopy,vm.refer.materialTypeList);
    	}
    	if(index == 12){
    		vm.itemName = "板材厚度";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.boardThickness,vm.refer.boardThicknessList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.boardThicknessCopy,vm.refer.boardThicknessList);
    	}
    	if(index == 13){
    		vm.itemName = "覆铜要求";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.copperCladThickness,vm.refer.copperCladThicknessList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.copperCladThicknessCopy,vm.refer.copperCladThicknessList);
    	}
    	if(index == 14){
    		vm.itemName = "镀层处理";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.surfaceProcess,vm.refer.surfaceProcessList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.surfaceProcessCopy,vm.refer.surfaceProcessList);
    	}
    	if(index == 15){
    		vm.itemName = "阻焊类型";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.solderMaskType,vm.refer.solderMaskTypeList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.solderMaskTypeCopy,vm.refer.solderMaskTypeList);
    	}
    	if(index == 16){
    		vm.itemName = "板面字符";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.characterType,vm.refer.characterTypeList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.characterTypeCopy,vm.refer.characterTypeList);
    	}
    	if(index == 17){
    		vm.itemName = "成型方式";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.shapingWay,vm.refer.shapingWayList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.shapingWayCopy,vm.refer.shapingWayList);
    	}
    	if(index == 18){
    		vm.itemName = "测试要求";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.testMethod,vm.refer.testMethodList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.testMethodCopy,vm.refer.testMethodList);
    	}
    	if(index == 19){
    		vm.itemName = "最小线宽/线距";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.lingeSpacing,vm.refer.lingeSpacingList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.lingeSpacingCopy,vm.refer.lingeSpacingList);
    	}
    	if(index == 20){
    		vm.itemName = "最小孔径";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.smallAperture,vm.refer.smallApertureList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.smallApertureCopy,vm.refer.smallApertureList);
    	}
    	if(index == 21){
    		vm.itemName = "半孔";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.halAhole,vm.refer.halAholeList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.halAholeCopy,vm.refer.halAholeList);
    	}
    	if(index == 22){
    		vm.itemName = "埋盲孔";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.buryBlindHole,vm.refer.buryBlindHoleList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.buryBlindHoleCopy,vm.refer.buryBlindHoleList);
    	}
    	if(index == 23){
    		vm.itemName = "抗阻";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.resistance,vm.refer.resistanceList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.resistanceCopy,vm.refer.resistanceList);
    	}
    	if(index == 24){
    		vm.itemName = "是否加急";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.deliveryUrgent,vm.refer.deliveryUrgentList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.deliveryUrgentCopy,vm.refer.deliveryUrgentList);
    	}
    	if(index == 25){
    		vm.itemName = "工艺/㎡价格";
    		vm.valueNameTwo = vm.edit.detail.item.price;
    		vm.valueNameOne = vm.edit.detail.item.priceCopy;
    	}
    	if(index == 27){
    		vm.itemName = "总金额";
    		vm.valueNameTwo = vm.edit.detail.item.totalAmtBefore;
    		vm.valueNameOne = vm.edit.detail.item.subTotalCopy;
    	}
    	if(index == 7){
    		vm.itemName = "pcs尺寸";
    		vm.valueNameTwo = vm.edit.detail.item.pcsSize;
    		vm.valueNameOne = vm.edit.detail.item.pcsSizeCopy;
    	}
    	if(index == 28){
    		vm.itemName = "交货尺寸";
    		vm.valueNameTwo = vm.edit.detail.item.deliverSize;
    		vm.valueNameOne = vm.edit.detail.item.deliverSizeCopy;
    	}
    	if(index == 29){
    		vm.itemName = "收货期限";
    		if(vm.edit.detail.item.deliveryDays){
    			vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.deliveryDays,vm.refer.deliverydaysList);
    		}else{
    			vm.valueNameTwo = "";
    		}
    		if(vm.edit.detail.item.deliveryDaysCopy){
    			vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.deliveryDaysCopy,vm.refer.deliverydaysList);
    		}else{
    			vm.valueNameOne = "";
    		}
    		
    	}
    	if(index == 30){
    		vm.itemName = "平米";
    		vm.valueNameTwo = vm.edit.detail.item.deailArea;
    		vm.valueNameOne = vm.edit.detail.item.deailAreaCopy;
    	}
    	if(index == 31){
    		vm.itemName = "导热";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.daore,vm.refer.daoreList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.daoreCopy,vm.refer.daoreList);
    	}
    	if(index == 32){
    		vm.itemName = "耐压";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.naiya,vm.refer.naiyaList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.naiyaCopy,vm.refer.naiyaList);
    	}
    	if(index == 33){
    		vm.itemName = "层数";
    		vm.valueNameTwo = vm.getItemValue(vm.edit.detail.item.pliesnumber,vm.refer.pliesnumberList);
    		vm.valueNameOne = vm.getItemValue(vm.edit.detail.item.pliesnumberCopy,vm.refer.pliesnumberList);
    	}
    	if(index == 34){
    		vm.itemName = "收货地点";
    		vm.valueNameTwo = vm.getItemValue(vm.customerOrderCopy.deliverycity,vm.refer.deliverycityList);
    		vm.valueNameOne = vm.getItemValue(vm.customerOrderCopy.deliverycityCopy,vm.refer.deliverycityList);
    	}
    	vm.showCompareForm();
    }
    
    // 显示编辑
    vm.showCompareForm = function () {
        vm.tabs.compareDetail.show = true;
        vm.tabs.compareDetail.active = true;
    };

    // 隐藏编辑
    vm.hideCompareForm = function () {
        vm.tabs.compareDetail.show = false;
        $timeout(function () {
        	if(vm.tabs.detailForm.show){
        		vm.tabs.detailForm.active = true;
        	}else if (vm.tabs.editForm.show) {
                vm.tabs.editForm.active = true;
            } else {
                vm.tabs.viewForm.active = true;
            }
        });
    };
    
    function loadData() {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
        upida.get("crm/slCustomerOrder/load/data").then(function(result) {
            vm.refer.customerList = result.data.customerList; // 客户信息
            vm.refer.branchList = result.data.branchList; // 子公司，承接公司
            vm.refer.currencyTypeList = result.data.currencyTypeList;// 货币类型
            vm.refer.taxDescriptList = result.data.taxDescriptList; // 税类说明
            vm.refer.payWayList = result.data.payWayList; // 结算方式
            vm.refer.userList = result.data.userList;
            vm.refer.boardLevelList = result.data.boardLevelList; // PCB类型
            vm.refer.materialTypeList = result.data.materialTypeList; // 覆铜板材
            vm.refer.boardThicknessList = result.data.boardThicknessList; // 板材厚度
            vm.refer.copperCladThicknessList = result.data.copperCladThicknessList; // 覆铜要求
            vm.refer.surfaceProcessList = result.data.surfaceProcessList; // 镀层处理
            vm.refer.solderMaskTypeList = result.data.solderMaskTypeList; // 阻焊类型
            vm.refer.characterTypeList = result.data.characterTypeList; // 板面字符
            vm.refer.shapingWayList = result.data.shapingWayList; // 成型方式
            vm.refer.testMethodList = result.data.testMethodList; // 测试要求
            vm.refer.deliverySmpDaysList = result.data.deliverySmpDaysList; // 样板交期
            vm.refer.deliveryPrdDaysList = result.data.deliveryPrdDaysList; // 量产交期
            // 2016-02-07 fzd START
			vm.refer.deliverywaysList = result.data.deliverywaysList; //送货方式deliverywaysList
            vm.refer.lingeSpacingList=result.data.lingeSpacingList;//最小线宽/线距
            vm.refer.smallApertureList=result.data.smallApertureList;//最小孔径
            vm.refer.halAholeList=result.data.halAholeList;//半孔
            vm.refer.buryBlindHoleList=result.data.buryBlindHoleList;//埋盲孔
            vm.refer.resistanceList=result.data.resistanceList;//抗阻
            vm.refer.deliveryUrgentList=result.data.deliveryUrgentList;//发货加急
            vm.refer.engineeringfeeList=result.data.engineeringfeeList;//工程费
            vm.refer.productionTypeList = result.data.productionTypeList; //生产模式
            vm.refer.referenceTypeList = result.data.referenceTypeList; //生产方式 是新资料 或者其它 有传图片的生产类型
            vm.refer.deliverydaysList = result.data.deliverydaysList; //交货期限(订单)
            
            // 2017-06-07 WC 
            vm.refer.daoreList = result.data.daoreList;//導熱
        	vm.refer.naiyaList = result.data. naiyaList;// 耐壓
    		vm.refer.pliesnumberList =  result.data.pliesnumberList;// 層數
    		vm.refer.deliverycityList= result.data.deliverycityList;//耐立德收货地点
            //END
            MainCtrl.unblockUI();
        });
        
        
    }

    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
    });
}]);