const report_deail = {
    template: '#report_deail',
    created() {
        this.getProgress();
    },
    computed: {
        sortedReportList() {
            return this.reportList.sort((a, b) => {
                // 根据时间顺序进行排序逻辑，可根据实际需求进行调整
                return a.timestamp - b.timestamp;
            });
        }
    },
    mounted: function ()
    {

    },
    watch: {

    },
    data() {
        return{
            reportList: [],
            reportName: ""
        }
    },
    methods: {
        getProgress: function (){
            let report = eval('(' + window.localStorage.getItem('reportList') + ')');
            const _this = this;
            _this.reportName = report.name;
            if (_this.reportName == "订单线")
            {
                _this.reportList = report;
            }
            else
            {
                _this.reportList = report.wipList;
            }
        }
    }
}