package com.kyb.pcberp.modules.contract.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.contract.entity.MaterialContract;
import com.kyb.pcberp.modules.contract.entity.MaterialContractDetail;
import com.kyb.pcberp.modules.purch.entity.Purchasing;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface MaterialContractDao extends CrudDao<MaterialContract>
{
    List<MaterialContractDetail> getContractDeailList(MaterialContract materialContract);

    void batchInsertDeail(@Param("details") List<MaterialContractDetail> details);

    List<MaterialContractDetail> getWaitOrderList(MaterialContractDetail detail);

    void updateServiceFee(MaterialContractDetail detail);

    List<MaterialContractDetail> getPurchContractDeailList(@Param("id") String id);

    MaterialContractDetail getDetailsByOrgId(@Param("orgId") String orgId);

    MaterialContract getContractUseData(MaterialContractDetail detail);

    MaterialContractDetail getContractDatailUseData(MaterialContractDetail detail);

    void updateContract(MaterialContract materialContract);

    void updateDeail(MaterialContractDetail materialContractDetail);

    void deleteDetail(MaterialContractDetail contractDetail);

    void deleteOrder(MaterialContract contract);

    void deleteOrderDeail(MaterialContract contract);

    void updateOrderStatus(MaterialContract contract);

    void updateDeailStatus(MaterialContract contract);
    
    void updateDeailStatusTwo(MaterialContractDetail contractDetail);
    
    Integer checkStatusCount(MaterialContractDetail contractDetail);
    
    MaterialContract getContract(MaterialContractDetail contractDetail);

    void updateOrderHistory(MaterialContract contractInDb);

    void updateDeailHistory(MaterialContract contractInDb);

    void updatePurchaseDeailNew(MaterialContract contractInDb);

    void updatePurchaseOrderNew(MaterialContract contractInDb);
    
    Integer checkCountByOrgId(Purchasing purchasing);

    Integer checkCountByOrgIdTwo(PurchasingDetail purchasingDetail);

    List<MaterialContractDetail> getContractDetailListByOrgIds(@Param("orgId") String orgId);

    List<MaterialContract> getGiveData(MaterialContract contract);

    List<MaterialContract> getSupplyChainList(MaterialContract materialContract);

    void modifyStatus(MaterialContract materialContract);

    List<MaterialContractDetail> getContractDeails(String materialContractId);

    void modifyDetailStatus(MaterialContractDetail materialContractDetail);

    MaterialContract getMaterialContract(MaterialContract materialContract);

    void renewalStatus(MaterialContract materialContract);

    void renewalDetailStatus(MaterialContractDetail materialContractDetail);

    List<MaterialContract> getDeliverGoodsList(MaterialContract materialContract);

    String getMatContractDetailId(@Param("companyId") String companyId,@Param("orgId") String orgId);

    List<MaterialContractDetail> toPrint(MaterialContract mc);

    List<MaterialContractDetail> toPrintTwo(MaterialContract mc);

    List<MaterialContractDetail> toPrintTDTWT(MaterialContract mc);

    Integer findMaterialNotisEnable(MaterialContract mc);

    Integer getMaterialContractDayNum(@Param("no") String no,@Param("companyId") String companyId);

    String getMaterialId(@Param("recordId") String recordId);

    void updateContractsPrintNo(MaterialContract mc);

    Integer getContractNum(MaterialContract mc);

    MaterialContractDetail getDetail(PurchasingDetail purchasingDetail);

    MaterialContractDetail getDetailTwo(@Param("orgId") String purDetailId);
}
