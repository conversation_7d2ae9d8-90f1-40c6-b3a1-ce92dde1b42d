<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.sys.dao.OrganizationDao">

	<sql id="organizationCloumns">
		a.recordId,
		a.deptId AS "dept.recordId",
		a.no,
		a.name,
		a.phone,
		a.activeFlag,
		a.createdBy AS "createdBy.recordId",
		a.createdDate,
		a.lastUpdBy AS "lastUpdBy.recordId",
		a.lastUpdDate,
		a.remark
	</sql>

	<insert id="insert" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO md_organization
		(
			deptId,
			no,
			name,
			phone,
			activeFlag,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			remark
		)VALUES(
			#{dept.recordId},
			#{no},
			#{name},
			#{phone},
			#{activeFlag},
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			#{remark}
		)
	</insert>
	
	<update id="update">
		UPDATE md_organization SET
			deptId = #{dept.recordId},
			no = #{no},
			name = #{name},
			phone = #{phone},
			createdBy = #{createdBy.recordId},
			createdDate = #{createdDate},
			lastUpdBy = #{lastUpdBy.recordId},
			lastUpdDate = #{lastUpdDate},
			remark = #{remark}
		WHERE recordId = #{recordId}
	</update>
	
	<update id="delete">
		UPDATE md_organization SET
			activeFlag = #{activeFlag}
		where recordId = #{recordId}
	</update>
	
	<select id="get" resultType="Organization">
		select
		<include refid="organizationCloumns"/>
		from md_organization a
		where a.recordId = #{recordId}		
	</select>
	
	<select id="findList" resultType="Organization">
		select
		<include refid="organizationCloumns"/>,
		b.name AS "dept.name",
		c.recordId AS "company.recordId",
		c.name AS "company.name"
		from md_organization a
		LEFT JOIN md_department b on b.recordId = a.deptId
		LEFT JOIN md_company c on c.recordId = b.companyId
		<where>
			c.recordId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL}
			<!-- 部门名称 -->
			<if test="dept != null and dept.name != null and dept.name != ''">
				AND b.name LIKE concat('%',#{dept.name},'%')
			</if>
			<!-- 组织名称 -->
			<if test="name != null and name != ''">
				AND a.name LIKE concat('%',#{name},'%')
			</if>
			<!-- 组织编号 -->
			<if test="no != null and no != ''">
				AND a.no LIKE concat('%',#{no},'%')
			</if>
			<if test="createdBy != null and createdBy.recordId != null and createdBy.recordId != ''">
				AND a.createdBy = #{createdBy.recordId} 
			</if>
		</where>
		<choose>
			<when test="page !=null and page.orderBy != null and page.orderBy != ''">
				ORDER BY ${page.orderBy}
			</when>
			<otherwise>
			</otherwise>
		</choose>
	</select>
	
	<!-- zjn 2018-09-26 根据部门获取组织 -->
	<select id="getOrganizationListByDept" resultType="Organization">
		select
		<include refid="organizationCloumns"/>,
		b.name AS "dept.name",
		c.recordId AS "company.recordId",
		c.shortName AS "company.shortName"
		from md_organization a
		LEFT JOIN md_department b on b.recordId = a.deptId
		LEFT JOIN md_company c on c.recordId = b.companyId
		where a.deptId = #{recordId} AND a.activeFlag = 1
	</select>
	
	<!-- zjn 2018-09-26 根据组织id组删除组织 -->
	<update id="deleteOrganizationByIds">
		update md_organization set
			activeFlag = #{activeFlag}
		where recordId in (${recordId})
	</update>
	
	<!-- zjn 2018-10-14 验证组织编号 -->
	<select id="getOrganizationNoCount" resultType="Integer">
		select
		count(1)
		from md_organization a
		LEFT JOIN md_department b on b.recordId = a.deptId
		where b.companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL} AND a.no = #{no}
		<if test="recordId != null and recordId != ''">
			AND a.recordId <![CDATA[<>]]> #{recordId}
		</if>
	</select>
	
	<!-- zjn 2018-10-14 验证组织名称 -->
	<select id="getOrganizationNameCount" resultType="Integer">
		select
		count(1)
		from md_organization a
		LEFT JOIN md_department b on b.recordId = a.deptId
		where companyId = #{company.recordId} AND a.activeFlag = #{DEL_FLAG_NORMAL} AND	a.name = #{name}
		<if test="recordId != null and recordId != ''">
			AND a.recordId <![CDATA[<>]]> #{recordId}
		</if>
	</select>
</mapper>