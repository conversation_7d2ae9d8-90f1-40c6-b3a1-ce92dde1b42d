'use strict';
angular.module("ngLocale", [], ["$provide", function($provide) {
var PLURAL_CATEGORY = {ZERO: "zero", ONE: "one", TWO: "two", FEW: "few", MANY: "many", OTHER: "other"};
$provide.value("$locale", {
  "DATETIME_FORMATS": {
    "AMPMS": [
      "PG",
      "PTG"
    ],
    "DAY": [
      "Ahad",
      "Isnin",
      "<PERSON><PERSON><PERSON>",
      "<PERSON><PERSON>",
      "<PERSON>ham<PERSON>",
      "Juma<PERSON>",
      "Sabtu"
    ],
    "ERANAMES": [
      "S.M.",
      "TM"
    ],
    "ERAS": [
      "S.M.",
      "TM"
    ],
    "FIRSTDAYOFWEEK": 0,
    "MONTH": [
      "<PERSON><PERSON><PERSON>",
      "<PERSON><PERSON><PERSON>",
      "<PERSON>",
      "April",
      "Mei",
      "Jun",
      "<PERSON>ai",
      "Ogos",
      "September",
      "Oktober",
      "November",
      "Disember"
    ],
    "SHORTDAY": [
      "Ahd",
      "Isn",
      "<PERSON><PERSON>",
      "<PERSON>b",
      "<PERSON><PERSON>",
      "<PERSON><PERSON>",
      "Sab"
    ],
    "SHORTMONTH": [
      "Jan",
      "Feb",
      "<PERSON>",
      "Apr",
      "<PERSON>",
      "<PERSON>",
      "Jul",
      "Ogo",
      "Sep",
      "Okt",
      "Nov",
      "Dis"
    ],
    "WEEKENDRANGE": [
      5,
      6
    ],
    "fullDate": "dd MMMM y",
    "longDate": "d MMMM y",
    "medium": "d MMM y h:mm:ss a",
    "mediumDate": "d MMM y",
    "mediumTime": "h:mm:ss a",
    "short": "d/MM/yy h:mm a",
    "shortDate": "d/MM/yy",
    "shortTime": "h:mm a"
  },
  "NUMBER_FORMATS": {
    "CURRENCY_SYM": "$",
    "DECIMAL_SEP": ",",
    "GROUP_SEP": ".",
    "PATTERNS": [
      {
        "gSize": 3,
        "lgSize": 3,
        "maxFrac": 3,
        "minFrac": 0,
        "minInt": 1,
        "negPre": "-",
        "negSuf": "",
        "posPre": "",
        "posSuf": ""
      },
      {
        "gSize": 3,
        "lgSize": 3,
        "maxFrac": 2,
        "minFrac": 2,
        "minInt": 1,
        "negPre": "\u00a4\u00a0-",
        "negSuf": "",
        "posPre": "\u00a4\u00a0",
        "posSuf": ""
      }
    ]
  },
  "id": "ms-latn-bn",
  "pluralCat": function(n, opt_precision) {  return PLURAL_CATEGORY.OTHER;}
});
}]);
