package com.kyb.pcberp.modules.crm.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import com.kyb.pcberp.modules.sys.entity.DictValue;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class QuotationPrePrice extends DataEntity<QuotationPrePrice>
{
    private static final long serialVersionUID = 1L;

    private BigDecimal price; // 产品生产单价

    private Long materialType; // 材料类别、覆铜板材

    private Long boardLevel; // 板层数-pcb类型

    private Long boardThickness; // 板厚

    private Long surfaceProcess; // 表面加工工艺，镀层处理

    private Long copperCladThickness; // 覆铜厚度，覆铜要求

    private Long solderMaskType; // 阻焊类型

    private Long characterType; // 字符印刷类型，板面字符

    private Long shapingWay; // 成型方式

    private Long testMethod; // 产品出厂检测方法，测试要求

    private BigDecimal unitPrice;

    private String pnlSize;

    private Date sentTimeStartQr; // 时间段查询条件 载体

    private Date sentTimeEndQr;

    private Long lingeSpacing;// 最小线宽/线距

    private Long smallAperture;// 最小孔径

    private Long halAhole;// 半孔

    private Long buryBlindHole;// 埋盲孔

    private Long resistance;// 抗阻

    private Long deliveryUrgent;// 发货加急

    private BigDecimal totalAmt;// 总金额

    private Long deliveryDays; // 交货期限

    private String totalAmtBefore;// 税前金额

    private String custOrderPrice;// 税前金额

    private String deailArea;// 合同明细总面积

    private Supplier banLiaoSupplier; // WC 2017-05-04 板料供应商

    private Supplier youMoSupplier; // WC 2017-05-04 油墨供应商

    private String customerMaterialNo; //客户物料号

    // WC 2017-05-27 ADD
    private Long daore; // 导热

    private Long naiya; // 耐压

    private Long pliesnumber; // 层数

    private String boardLevelValue;

    private String materialTypeValue;

    private String boardThicknessValue;

    private String copperCladThicknessValue;

    private String surfaceProcessValue;

    private String solderMaskTypeValue;

    private String characterTypeValue;

    private String shapingWayValue;

    private String testMethodValue;

    private String productTypeId;

    private Date effectiveTime;

    private String manufacturer;

    private String brandId;

    private BigDecimal salePcsPrice;

    private DictValue productType;

    private String processValueId;

    private String status; // 报价明细状态

    private BigDecimal calculationResult;

    private BigDecimal windowPcsPrice; // 系统计算pcs单价

    private List<QuotationPrePriceResult> resultList;

    private String lingeSpacingValue;

    private String smallApertureValue;

    private String halAholeValue;

    private String buryBlindHoleValue;

    private String resistanceValue;

    private String daoreValue;

    private String naiyaValue;

    private String deliveryPrdDayValue;

    private String deliverySmpDayValue;

    private List<QuotationResult> ladderQuotationList;

    private String priceStatus;

    private List<QuoteSetup> quoteSetupList;

    private BigDecimal avgPrice;

    private BigDecimal areaPrice;

    private BigDecimal avgWindowPrice;

    private BigDecimal avgSalePrice;

    private String priceQuotiaon;

    private Boolean systemQuotation;

    private Boolean manualAdjustmentProcessingRate;

    private Boolean manualAdjustmentMaterialRate;

    private Boolean manualAdjustmentQuotationRate;

    private String totalColumnValue;

    private List<QuotationDetail> nameList;

    private Boolean showFlag;

    private BigDecimal materialArea;

    private BigDecimal materialWindowArea;

    private BigDecimal materialSaleArea;

    private String condition;

    private String condition1;

    private String condition2;

    private String condition3;

    private String condition4;

    private String condition5;

    private String condition6;

    private String condition7;

    private String condition8;

    private String condition9;

    private String condition10;

    private String condition11;

    private BigDecimal fixedOffer;

    private String keepDecimal;

    public QuotationPrePrice()
    {
        super();
    }

    public QuotationPrePrice(String id)
    {
        super(id);
    }

    public BigDecimal getPrice()
    {
        return price;
    }

    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    public Long getMaterialType()
    {
        return materialType;
    }

    public void setMaterialType(Long materialType)
    {
        this.materialType = materialType;
    }

    public Long getBoardLevel()
    {
        return boardLevel;
    }

    public void setBoardLevel(Long boardLevel)
    {
        this.boardLevel = boardLevel;
    }

    public Long getBoardThickness()
    {
        return boardThickness;
    }

    public void setBoardThickness(Long boardThickness)
    {
        this.boardThickness = boardThickness;
    }

    public Long getSurfaceProcess()
    {
        return surfaceProcess;
    }

    public void setSurfaceProcess(Long surfaceProcess)
    {
        this.surfaceProcess = surfaceProcess;
    }

    public Long getCopperCladThickness()
    {
        return copperCladThickness;
    }

    public void setCopperCladThickness(Long copperCladThickness)
    {
        this.copperCladThickness = copperCladThickness;
    }

    public Long getSolderMaskType()
    {
        return solderMaskType;
    }

    public void setSolderMaskType(Long solderMaskType)
    {
        this.solderMaskType = solderMaskType;
    }

    public Long getCharacterType()
    {
        return characterType;
    }

    public void setCharacterType(Long characterType)
    {
        this.characterType = characterType;
    }

    public Long getShapingWay()
    {
        return shapingWay;
    }

    public void setShapingWay(Long shapingWay)
    {
        this.shapingWay = shapingWay;
    }

    public Long getTestMethod()
    {
        return testMethod;
    }

    public void setTestMethod(Long testMethod)
    {
        this.testMethod = testMethod;
    }

    public BigDecimal getUnitPrice()
    {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice)
    {
        this.unitPrice = unitPrice;
    }

    public String getPnlSize()
    {
        return pnlSize;
    }

    public void setPnlSize(String pnlSize)
    {
        this.pnlSize = pnlSize;
    }

    public Date getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }

    public void setSentTimeStartQr(Date sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }

    public Date getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }

    public void setSentTimeEndQr(Date sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }

    public Long getLingeSpacing()
    {
        return lingeSpacing;
    }

    public void setLingeSpacing(Long lingeSpacing)
    {
        this.lingeSpacing = lingeSpacing;
    }

    public Long getSmallAperture()
    {
        return smallAperture;
    }

    public void setSmallAperture(Long smallAperture)
    {
        this.smallAperture = smallAperture;
    }

    public Long getHalAhole()
    {
        return halAhole;
    }

    public void setHalAhole(Long halAhole)
    {
        this.halAhole = halAhole;
    }

    public Long getBuryBlindHole()
    {
        return buryBlindHole;
    }

    public void setBuryBlindHole(Long buryBlindHole)
    {
        this.buryBlindHole = buryBlindHole;
    }

    public Long getResistance()
    {
        return resistance;
    }

    public void setResistance(Long resistance)
    {
        this.resistance = resistance;
    }

    public Long getDeliveryUrgent()
    {
        return deliveryUrgent;
    }

    public void setDeliveryUrgent(Long deliveryUrgent)
    {
        this.deliveryUrgent = deliveryUrgent;
    }

    public BigDecimal getTotalAmt()
    {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt)
    {
        this.totalAmt = totalAmt;
    }

    public Long getDeliveryDays()
    {
        return deliveryDays;
    }

    public void setDeliveryDays(Long deliveryDays)
    {
        this.deliveryDays = deliveryDays;
    }

    public String getTotalAmtBefore()
    {
        return totalAmtBefore;
    }

    public void setTotalAmtBefore(String totalAmtBefore)
    {
        this.totalAmtBefore = totalAmtBefore;
    }

    public String getCustOrderPrice()
    {
        return custOrderPrice;
    }

    public void setCustOrderPrice(String custOrderPrice)
    {
        this.custOrderPrice = custOrderPrice;
    }

    public String getDeailArea()
    {
        return deailArea;
    }

    public void setDeailArea(String deailArea)
    {
        this.deailArea = deailArea;
    }

    public Supplier getBanLiaoSupplier()
    {
        return banLiaoSupplier;
    }

    public void setBanLiaoSupplier(Supplier banLiaoSupplier)
    {
        this.banLiaoSupplier = banLiaoSupplier;
    }

    public Supplier getYouMoSupplier()
    {
        return youMoSupplier;
    }

    public void setYouMoSupplier(Supplier youMoSupplier)
    {
        this.youMoSupplier = youMoSupplier;
    }

    public String getCustomerMaterialNo()
    {
        return customerMaterialNo;
    }

    public void setCustomerMaterialNo(String customerMaterialNo)
    {
        this.customerMaterialNo = customerMaterialNo;
    }

    public Long getDaore()
    {
        return daore;
    }

    public void setDaore(Long daore)
    {
        this.daore = daore;
    }

    public Long getNaiya()
    {
        return naiya;
    }

    public void setNaiya(Long naiya)
    {
        this.naiya = naiya;
    }

    public Long getPliesnumber()
    {
        return pliesnumber;
    }

    public void setPliesnumber(Long pliesnumber)
    {
        this.pliesnumber = pliesnumber;
    }

    public String getBoardLevelValue()
    {
        return boardLevelValue;
    }

    public void setBoardLevelValue(String boardLevelValue)
    {
        this.boardLevelValue = boardLevelValue;
    }

    public String getMaterialTypeValue()
    {
        return materialTypeValue;
    }

    public void setMaterialTypeValue(String materialTypeValue)
    {
        this.materialTypeValue = materialTypeValue;
    }

    public String getBoardThicknessValue()
    {
        return boardThicknessValue;
    }

    public void setBoardThicknessValue(String boardThicknessValue)
    {
        this.boardThicknessValue = boardThicknessValue;
    }

    public String getCopperCladThicknessValue()
    {
        return copperCladThicknessValue;
    }

    public void setCopperCladThicknessValue(String copperCladThicknessValue)
    {
        this.copperCladThicknessValue = copperCladThicknessValue;
    }

    public String getSurfaceProcessValue()
    {
        return surfaceProcessValue;
    }

    public void setSurfaceProcessValue(String surfaceProcessValue)
    {
        this.surfaceProcessValue = surfaceProcessValue;
    }

    public String getSolderMaskTypeValue()
    {
        return solderMaskTypeValue;
    }

    public void setSolderMaskTypeValue(String solderMaskTypeValue)
    {
        this.solderMaskTypeValue = solderMaskTypeValue;
    }

    public String getCharacterTypeValue()
    {
        return characterTypeValue;
    }

    public void setCharacterTypeValue(String characterTypeValue)
    {
        this.characterTypeValue = characterTypeValue;
    }

    public String getShapingWayValue()
    {
        return shapingWayValue;
    }

    public void setShapingWayValue(String shapingWayValue)
    {
        this.shapingWayValue = shapingWayValue;
    }

    public String getTestMethodValue()
    {
        return testMethodValue;
    }

    public void setTestMethodValue(String testMethodValue)
    {
        this.testMethodValue = testMethodValue;
    }

    public String getProductTypeId()
    {
        return productTypeId;
    }

    public void setProductTypeId(String productTypeId)
    {
        this.productTypeId = productTypeId;
    }

    public Date getEffectiveTime()
    {
        return effectiveTime;
    }

    public void setEffectiveTime(Date effectiveTime)
    {
        this.effectiveTime = effectiveTime;
    }

    public String getManufacturer()
    {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer)
    {
        this.manufacturer = manufacturer;
    }

    public String getBrandId()
    {
        return brandId;
    }

    public void setBrandId(String brandId)
    {
        this.brandId = brandId;
    }

    public BigDecimal getSalePcsPrice()
    {
        return salePcsPrice;
    }

    public void setSalePcsPrice(BigDecimal salePcsPrice)
    {
        this.salePcsPrice = salePcsPrice;
    }

    public DictValue getProductType()
    {
        return productType;
    }

    public void setProductType(DictValue productType)
    {
        this.productType = productType;
    }

    public String getProcessValueId()
    {
        return processValueId;
    }

    public void setProcessValueId(String processValueId)
    {
        this.processValueId = processValueId;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public BigDecimal getCalculationResult()
    {
        return calculationResult;
    }

    public void setCalculationResult(BigDecimal calculationResult)
    {
        this.calculationResult = calculationResult;
    }

    public BigDecimal getWindowPcsPrice()
    {
        return windowPcsPrice;
    }

    public void setWindowPcsPrice(BigDecimal windowPcsPrice)
    {
        this.windowPcsPrice = windowPcsPrice;
    }

    public List<QuotationPrePriceResult> getResultList()
    {
        return resultList;
    }

    public void setResultList(List<QuotationPrePriceResult> resultList)
    {
        this.resultList = resultList;
    }

    public String getLingeSpacingValue()
    {
        return lingeSpacingValue;
    }

    public void setLingeSpacingValue(String lingeSpacingValue)
    {
        this.lingeSpacingValue = lingeSpacingValue;
    }

    public String getSmallApertureValue()
    {
        return smallApertureValue;
    }

    public void setSmallApertureValue(String smallApertureValue)
    {
        this.smallApertureValue = smallApertureValue;
    }

    public String getHalAholeValue()
    {
        return halAholeValue;
    }

    public void setHalAholeValue(String halAholeValue)
    {
        this.halAholeValue = halAholeValue;
    }

    public String getBuryBlindHoleValue()
    {
        return buryBlindHoleValue;
    }

    public void setBuryBlindHoleValue(String buryBlindHoleValue)
    {
        this.buryBlindHoleValue = buryBlindHoleValue;
    }

    public String getResistanceValue()
    {
        return resistanceValue;
    }

    public void setResistanceValue(String resistanceValue)
    {
        this.resistanceValue = resistanceValue;
    }

    public String getDaoreValue()
    {
        return daoreValue;
    }

    public void setDaoreValue(String daoreValue)
    {
        this.daoreValue = daoreValue;
    }

    public String getNaiyaValue()
    {
        return naiyaValue;
    }

    public void setNaiyaValue(String naiyaValue)
    {
        this.naiyaValue = naiyaValue;
    }

    public String getDeliveryPrdDayValue()
    {
        return deliveryPrdDayValue;
    }

    public void setDeliveryPrdDayValue(String deliveryPrdDayValue)
    {
        this.deliveryPrdDayValue = deliveryPrdDayValue;
    }

    public String getDeliverySmpDayValue()
    {
        return deliverySmpDayValue;
    }

    public void setDeliverySmpDayValue(String deliverySmpDayValue)
    {
        this.deliverySmpDayValue = deliverySmpDayValue;
    }

    public List<QuotationResult> getLadderQuotationList()
    {
        return ladderQuotationList;
    }

    public void setLadderQuotationList(List<QuotationResult> ladderQuotationList)
    {
        this.ladderQuotationList = ladderQuotationList;
    }

    public String getPriceStatus()
    {
        return priceStatus;
    }

    public void setPriceStatus(String priceStatus)
    {
        this.priceStatus = priceStatus;
    }

    public List<QuoteSetup> getQuoteSetupList()
    {
        return quoteSetupList;
    }

    public void setQuoteSetupList(List<QuoteSetup> quoteSetupList)
    {
        this.quoteSetupList = quoteSetupList;
    }

    public BigDecimal getAvgPrice()
    {
        return avgPrice;
    }

    public void setAvgPrice(BigDecimal avgPrice)
    {
        this.avgPrice = avgPrice;
    }

    public BigDecimal getAreaPrice()
    {
        return areaPrice;
    }

    public void setAreaPrice(BigDecimal areaPrice)
    {
        this.areaPrice = areaPrice;
    }

    public BigDecimal getAvgWindowPrice()
    {
        return avgWindowPrice;
    }

    public void setAvgWindowPrice(BigDecimal avgWindowPrice)
    {
        this.avgWindowPrice = avgWindowPrice;
    }

    public BigDecimal getAvgSalePrice()
    {
        return avgSalePrice;
    }

    public void setAvgSalePrice(BigDecimal avgSalePrice)
    {
        this.avgSalePrice = avgSalePrice;
    }

    public String getPriceQuotiaon()
    {
        return priceQuotiaon;
    }

    public void setPriceQuotiaon(String priceQuotiaon)
    {
        this.priceQuotiaon = priceQuotiaon;
    }

    public Boolean getSystemQuotation()
    {
        return systemQuotation;
    }

    public void setSystemQuotation(Boolean systemQuotation)
    {
        this.systemQuotation = systemQuotation;
    }

    public Boolean getManualAdjustmentProcessingRate()
    {
        return manualAdjustmentProcessingRate;
    }

    public void setManualAdjustmentProcessingRate(Boolean manualAdjustmentProcessingRate)
    {
        this.manualAdjustmentProcessingRate = manualAdjustmentProcessingRate;
    }

    public Boolean getManualAdjustmentMaterialRate()
    {
        return manualAdjustmentMaterialRate;
    }

    public void setManualAdjustmentMaterialRate(Boolean manualAdjustmentMaterialRate)
    {
        this.manualAdjustmentMaterialRate = manualAdjustmentMaterialRate;
    }

    public Boolean getManualAdjustmentQuotationRate()
    {
        return manualAdjustmentQuotationRate;
    }

    public void setManualAdjustmentQuotationRate(Boolean manualAdjustmentQuotationRate)
    {
        this.manualAdjustmentQuotationRate = manualAdjustmentQuotationRate;
    }

    public String getTotalColumnValue()
    {
        return totalColumnValue;
    }

    public void setTotalColumnValue(String totalColumnValue)
    {
        this.totalColumnValue = totalColumnValue;
    }

    public List<QuotationDetail> getNameList()
    {
        return nameList;
    }

    public void setNameList(List<QuotationDetail> nameList)
    {
        this.nameList = nameList;
    }

    public Boolean getShowFlag()
    {
        return showFlag;
    }

    public void setShowFlag(Boolean showFlag)
    {
        this.showFlag = showFlag;
    }

    public BigDecimal getMaterialArea()
    {
        return materialArea;
    }

    public void setMaterialArea(BigDecimal materialArea)
    {
        this.materialArea = materialArea;
    }

    public BigDecimal getMaterialWindowArea()
    {
        return materialWindowArea;
    }

    public void setMaterialWindowArea(BigDecimal materialWindowArea)
    {
        this.materialWindowArea = materialWindowArea;
    }

    public BigDecimal getMaterialSaleArea()
    {
        return materialSaleArea;
    }

    public void setMaterialSaleArea(BigDecimal materialSaleArea)
    {
        this.materialSaleArea = materialSaleArea;
    }

    public String getCondition()
    {
        return condition;
    }

    public void setCondition(String condition)
    {
        this.condition = condition;
    }

    public String getCondition1()
    {
        return condition1;
    }

    public void setCondition1(String condition1)
    {
        this.condition1 = condition1;
    }

    public String getCondition2()
    {
        return condition2;
    }

    public void setCondition2(String condition2)
    {
        this.condition2 = condition2;
    }

    public String getCondition3()
    {
        return condition3;
    }

    public void setCondition3(String condition3)
    {
        this.condition3 = condition3;
    }

    public String getCondition4()
    {
        return condition4;
    }

    public void setCondition4(String condition4)
    {
        this.condition4 = condition4;
    }

    public String getCondition5()
    {
        return condition5;
    }

    public void setCondition5(String condition5)
    {
        this.condition5 = condition5;
    }

    public String getCondition6()
    {
        return condition6;
    }

    public void setCondition6(String condition6)
    {
        this.condition6 = condition6;
    }

    public String getCondition7()
    {
        return condition7;
    }

    public void setCondition7(String condition7)
    {
        this.condition7 = condition7;
    }

    public String getCondition8()
    {
        return condition8;
    }

    public void setCondition8(String condition8)
    {
        this.condition8 = condition8;
    }

    public String getCondition9()
    {
        return condition9;
    }

    public void setCondition9(String condition9)
    {
        this.condition9 = condition9;
    }

    public String getCondition10()
    {
        return condition10;
    }

    public void setCondition10(String condition10)
    {
        this.condition10 = condition10;
    }

    public String getCondition11()
    {
        return condition11;
    }

    public void setCondition11(String condition11)
    {
        this.condition11 = condition11;
    }

    public String getConcatValue()
    {
        String result = null;
        if(StringUtils.isNotBlank(boardLevelValue))
        {
            if(StringUtils.isNotBlank(result))
            {
                result = result +" "+ boardLevelValue;
            }
            else
            {
                result = boardLevelValue;
            }
        }
        if(StringUtils.isNotBlank(boardThicknessValue))
        {
            if(StringUtils.isNotBlank(result))
            {
                result = result +" "+ boardThicknessValue;
            }
            else
            {
                result = boardThicknessValue;
            }
        }
        if(StringUtils.isNotBlank(copperCladThicknessValue))
        {
            if(StringUtils.isNotBlank(result))
            {
                result = result +" "+ copperCladThicknessValue;
            }
            else
            {
                result = copperCladThicknessValue;
            }
        }
        if(StringUtils.isNotBlank(solderMaskTypeValue))
        {
            if(StringUtils.isNotBlank(result))
            {
                result = result +" "+ solderMaskTypeValue;
            }
            else
            {
                result = solderMaskTypeValue;
            }
        }
        if(StringUtils.isNotBlank(surfaceProcessValue))
        {
            if(StringUtils.isNotBlank(result))
            {
                result = result +" "+ surfaceProcessValue;
            }
            else
            {
                result = surfaceProcessValue;
            }
        }
        if(StringUtils.isNotBlank(shapingWayValue))
        {
            if(StringUtils.isNotBlank(result))
            {
                result = result +" "+ shapingWayValue;
            }
            else
            {
                result = shapingWayValue;
            }
        }
        return result;
    }

    public BigDecimal getFixedOffer() {
        return fixedOffer;
    }

    public void setFixedOffer(BigDecimal fixedOffer) {
        this.fixedOffer = fixedOffer;
    }

    public String getKeepDecimal() {
        return keepDecimal;
    }

    public void setKeepDecimal(String keepDecimal) {
        this.keepDecimal = keepDecimal;
    }
}
