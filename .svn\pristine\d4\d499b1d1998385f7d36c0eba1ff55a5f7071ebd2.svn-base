<%@ page contentType="text/html;charset=UTF-8"%>
<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">统计报表</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="report.qualitydailyreport">品质部日报表</a>
        </li>
    </ul>
</div>

<tabset class="tabset-margin-top">
  
    
    <tab heading="日报表" active="ctrl.tabs.customerReport.active">
        
        <div class="panel panel-default">
			<div class="panel-heading font-blue-hoki">查询</div>
			<div class="panel-body">
                <form class="form-horizontal" id="step6">
                    <div class="row" >
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">日期：</label>
                                <div class="col-sm-7 col-md-8">
                                    <div class="input-prepend input-group">
                                        <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                        <input type="text" class="form-control" disable-auto-validate="true"
                                               ng-blur="ctrl.initDateTwo(ctrl.timeTwo)"
                                               kyb-daterange
                                               kyb-daterange-options="ctrl.rangeOptionsTwo"
                                               ng-model="ctrl.timeTwo"
                                               placeholder="请选择时间段">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="ctrl.sourceTypeValue">
                                        <option value="">所有</option>
                                        <option value="1">生产检测</option>
                                        <option value="2">客诉检测</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="ctrl.doCustomerDetailQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                        </div>
                    </div>
                </form>
			</div>
		</div>
        
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="actions">
                    <div class="collapse navbar-collapse navbar-ex1-collapse">
                    	<div class="portlet-input input-inline input-small">
	                        <form action="a/quality/detectionRecord/exportQualityDailyReport" method="POST" enctype="multipart/form-data" target="hidden_frame1">
	                            <div>
	                                <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出</button>
                                    <input type="text" ng-show="false" name="sentTimeStartQr" value="{{ctrl.timeTwo.start.valueOf()}}"/>
                                    <input type="text" ng-show="false" name="sentTimeEndQr" value="{{ctrl.timeTwo.end.valueOf()}}"/>
                            		<%--<input type="text" ng-show="false" name="queryDate" value="{{ctrl.queryDate}}"/>--%>
                                    <input type="text" ng-show="false" name="sourceType" value="{{ctrl.sourceTypeValue}}"/>
	                                <iframe name="hidden_frame1" id="hidden_frame1" style="display:none"></iframe>
	                            </div>
	                        </form>
	                    </div>
                    </div>
                </div>
            </div>

            <div class="portlet-body" ng-if="ctrl.onePartOneList && ctrl.onePartOneList.length > 0">
                <div class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                        <thead>
                        <tr class="heading">
							<th colspan="{{ctrl.onePartOneList.length + 1}}" style="text-align: center;">{{ctrl.queryDate | date:'yyyy-MM-dd'}}品质日报表</th>
                        </tr>
                        <tr class="heading">
                            <th colspan="{{ctrl.onePartOneList.length + 1}}" style="text-align: left;">一、制程报废统计(日入库面积：{{ctrl.dayInstoreArea.toFixed(2)}},月入库面积：{{ctrl.monInstoreArea.toFixed(2)}})</th>
                        </tr>
                        </thead>
                        <tbody>


<%--                        <tr>
                       		<th>工序</th>
							<th>报废面积（㎡）</th>
                       		<th>累计报废面积（㎡）</th>
							<th>当日报废率(%)</th>
							<th>累计报废率(%)</th>
							<th colspan="2">目标值(%)</th>
                        </tr>
                        <tr ng-repeat="row in ctrl.onePartOneList">
                            <td>{{row.discardCauseOne}}</td>
                            <td>{{row.discardArea.toFixed(4)}}</td>
                            <td>{{row.monthDiscardArea.toFixed(4)}}</td>
							<td>{{row.discardRate.toFixed(4)}}</td>
							<td>{{row.monthDiscardRate.toFixed(4)}}</td>
                            <td colspan="2">{{row.planRate.toFixed(4)}}</td>
                        </tr>--%>
                        <tr>
                            <th>报废信息</th>
                            <th ng-repeat="value in ctrl.onePartOneList">{{value.discardCauseOne}}</th>
                        </tr>
                        <tr ng-repeat="oneRwo in ctrl.onePartOneListTwo">
                            <td ng-bind="oneRwo.processName"></td>
                            <td ng-repeat="showObj in oneRwo.cellList">{{showObj.showData}}</td>
                        </tr>
                        </tbody>
                        <tbody>
                            <tr><td colspan="7"></td></tr>
                             <tr>
                                <td colspan="3">缺陷前五项不良</td>
                                <td colspan="4">型号前五项不良</td>
                                <td colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></td>
                            </tr>
                            <tr ng-repeat="twoRwo in ctrl.onePartTwoList">
                                <td colspan="2">{{twoRwo.discardCause}}</td>
                                <td>{{twoRwo.discardArea.toFixed(4)}}㎡</td>
                                <td colspan="2">{{twoRwo.craftNo}}</td>
                                <td colspan="2">{{twoRwo.craftNoArea.toFixed(4)}}㎡</td>
                                <td colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></td>
                            </tr>
                        
                        <tr><td colspan="7"></td></tr>

                        <tr class="heading">
							<th colspan="7" style="text-align: left;">二、检验合格率</th>
                        </tr>
                        <tr>
                       		<th>检测方式</th>
							<th>检验数量</th>
                       		<th>合格数量</th>
							<th>当天合格率(%)</th>
							<th>累计合格率%</th>
							<th colspan="2">目标值(%)</th>
                            <th colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></th>
                        </tr>
                         <tr ng-repeat="row in ctrl.twoPartList">
                            <td>{{row.testTypeName}}</td>
                            <td>{{row.checkQuality}}</td>
							<td>{{row.goodQuality}}</td>
							<td>{{row.percentOfPass.toFixed(4)}}</td>
							<td>{{row.monthPercentOfPass.toFixed(4)}}</td>
							<td colspan="2">{{row.planRate.toFixed(4)}}</td>
                            <td colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></td>
                        </tr>
                        
                        <tr><td colspan="7"></td></tr>

                        <tr class="heading">
							<th colspan="7" style="text-align: left;">三、客户投诉</th>
                        </tr>
                        <tr>
	                        <td colspan="2">日累计出货批数</td>
	                        <td colspan="2">日累计客诉</td>
	                        <td colspan="3">日累计客诉率(%)</td>
                            <td colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></td>
                        </tr>
                        <tr>
	                        <td colspan="2">{{ctrl.threePartOneObj.outBatchNum}}</td>
	                        <td colspan="2">{{ctrl.threePartOneObj.complaintNum}}</td>
	                        <td colspan="3">{{ctrl.threePartOneObj.complaintRate.toFixed(4)}}</td>
                            <td colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></td>
                        </tr>
                        <tr>
	                        <td colspan="2">本月累计出货批数</td>
	                        <td colspan="2">本月累计客诉</td>
	                        <td colspan="3">本月累计客诉率(%)</td>
                            <td colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></td>
                        </tr>
                        <tr>
	                        <td colspan="2">{{ctrl.threePartOneObj.monthOutBatchNum}}</td>
	                        <td colspan="2">{{ctrl.threePartOneObj.monthComplaintNum}}</td>
	                        <td colspan="3">{{ctrl.threePartOneObj.monthComplaintRate.toFixed(4)}}</td>
                            <td colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></td>
                        </tr>
                        <tr ng-repeat="rowThree in ctrl.threePartRowList">
                            <td ng-repeat="cc in rowThree.cellList">
                        		{{cc.discardCauseOne}}
                        	</td>
                            <td colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></td>
                        </tr>
                        <tr>
                        	<td colspan="7" >当日客诉内容：
                        	<div ng-repeat="rr in ctrl.compalintContentList">{{rr.complaintContents}}</div>
                        	</td>
                        </tr>
                        <tr>
                        	<td colspan="7" >当日客诉结果：{{ctrl.threePartTwoObj.complaintContents}}</td>
                        </tr>

                        <tr><td colspan="7"></td></tr>

                       <tr class="heading">
                          <th colspan="7" style="text-align: left;">四、补料</th>
                      </tr>
                      <tr>
                          <td colspan="2">本厂编号</td>
                          <td>补数数量PCS</td>
                          <td>补料面积</td>
                          <td colspan="2"> 缺陷描述</td>
                          <td>责任工序</td>
                          <td colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></td>
                      </tr>
                      <tr ng-repeat="ff in ctrl.fourPartOneList">
                          <td colspan="2">{{ff.craftNo}}</td>
                          <td>{{ff.replenishQuality}}</td>
                          <td>{{ff.replenishiArea.toFixed(4)}}</td>
                          <td colspan="2">{{ff.discardCause}}</td>
                          <td>{{ff.discardCauseOne}}</td>
                          <td colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></td>
                      </tr>

                      <tr><td colspan="7"></td></tr>

                      <tr>
                          <td colspan="2">日累计补料批数</td>
                          <td>{{ctrl.fourObj.replenishBatch}}</td>
                          <td colspan="2">月累计补料批数</td>
                          <td colspan="2">{{ctrl.fourObj.monthReplenishBatch}}</td>
                          <td colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></td>
                      </tr>
                      <tr ng-repeat="fr in ctrl.fourPartRowList">
                          <td ng-repeat="fc in fr.cellList">
                              {{fc.discardCauseOne}}
                          </td>
                          <td colspan="{{ctrl.onePartOneList.length + 1 - 7}}" ng-show = "false"></td>
                      </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </tab>

<div class="row">
    <div class="col-md-12">
        <div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">提示</h4>
                    </div>
                    <div class="modal-body">
                        <p><span ng-bind="ctrl.message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>