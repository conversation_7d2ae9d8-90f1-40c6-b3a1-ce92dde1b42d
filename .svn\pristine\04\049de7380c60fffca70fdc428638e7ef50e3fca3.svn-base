package com.kyb.pcberp.modules.report.entity;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.stock.entity.ProductStore;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class DeliveryAssessmentDetail extends DataEntity<DeliveryAssessmentDetail>
{
    private static final long serialVersionUID = 1L;

    private String contractNo; // 合同编号

    private String craftNo; // 生产编号

    private BigDecimal orderArea; // 订单面积(㎡)

    private String customerModule; // 客户型号

    private String processValueId; // 1曝光，2丝印

    private String orderTypeValue; // 订单类型

    private String deptName; // 部门

    private String saleComId;

    private String saleComName; // 销售公司

    private String saleUserName; // 业务员

    private Date sentTime; // 送货时间

    private Date estimateDate; // 系统评估时间

    private String period; // 账期月份

    private String contractDetailId;

    private String deptId;

    private Integer quantity;

    private Date orderDate;

    private List<ProductStore> productStoreList;

    private BigDecimal pcsArea;

    private String dictOrderType;

    private String organizationType; // 组织类型

    private Integer type;

    private String saleCus; // 销售客户

    private String customerPo; // 客户订单号

    private String status;

    private Date firstBatchDate; // 首批交期

    private String firstDays; // 首批天数

    private String finalDays; // 最终天数

    private String actualDays; // 实际天数

    private Date estimateStartDate; // 评估开始日期

    private Date firstSentTime; // 第一次送货时间

    private Integer firstSentDay; // 第一次送货天数

    private BigDecimal firstAveDays; // 首批平均交期

    private String materialType;

    public String getContractNo()
    {
        return contractNo;
    }

    public void setContractNo(String contractNo)
    {
        this.contractNo = contractNo;
    }

    public String getCraftNo()
    {
        return craftNo;
    }

    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }

    public BigDecimal getOrderArea()
    {
        return orderArea;
    }

    public void setOrderArea(BigDecimal orderArea)
    {
        this.orderArea = orderArea;
    }

    public String getCustomerModule()
    {
        return customerModule;
    }

    public void setCustomerModule(String customerModule)
    {
        this.customerModule = customerModule;
    }

    public String getProcessValueId()
    {
        return processValueId;
    }

    public void setProcessValueId(String processValueId)
    {
        this.processValueId = processValueId;
    }

    public String getProcessValue()
    {
        String result = null;
        if(StringUtils.isBlank(processValueId))
        {
            return result;
        }
        switch (processValueId)
        {
            case "1":
                result = "曝光";
                break;
            case "2":
                result = "丝印";
                break;
        }
        return result;
    }

    public String getOrderTypeValue()
    {
        return orderTypeValue;
    }

    public void setOrderTypeValue(String orderTypeValue)
    {
        this.orderTypeValue = orderTypeValue;
    }

    public String getDeptName()
    {
        return deptName;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }

    public String getSaleComId()
    {
        return saleComId;
    }

    public void setSaleComId(String saleComId)
    {
        this.saleComId = saleComId;
    }

    public String getSaleComName()
    {
        return saleComName;
    }

    public void setSaleComName(String saleComName)
    {
        this.saleComName = saleComName;
    }

    public String getSaleUserName()
    {
        return saleUserName;
    }

    public void setSaleUserName(String saleUserName)
    {
        this.saleUserName = saleUserName;
    }

    public Date getSentTime()
    {
        return sentTime;
    }

    public void setSentTime(Date sentTime)
    {
        this.sentTime = sentTime;
    }

    public String getSentTimeStr()
    {
        if(null != sentTime)
        {
            return DateUtils.formatDate(sentTime,"yyyy-MM-dd");
        }
        return null;
    }

    public Date getEstimateDate()
    {
        return estimateDate;
    }

    public void setEstimateDate(Date estimateDate)
    {
        this.estimateDate = estimateDate;
    }

    public String getEstimateDateStr()
    {
        if(null != estimateDate)
        {
            return DateUtils.formatDate(estimateDate,"yyyy-MM-dd");
        }
        return null;
    }

    public Integer getFirstOverdueDays()
    {
        Integer firstOverdueDays = 0;
        if(null != estimateDate)
        {
            if(null != firstSentTime)
            {
                firstOverdueDays = DateUtils.getDays(estimateDate,firstSentTime);
            }
            else
            {
                firstOverdueDays = DateUtils.getDays(estimateDate,new Date());
            }
            if(firstOverdueDays <= 0)
            {
                firstOverdueDays = 0;
            }
        }
        return firstOverdueDays;
    }

    public Integer getOverdueDays()
    {
        Integer overdueDays = 0;
        if(null != estimateDate)
        {
            if(null != sentTime)
            {
                overdueDays = DateUtils.getDays(estimateDate,sentTime);
            }
            else
            {
                overdueDays = DateUtils.getDays(estimateDate,new Date());
            }
            if(overdueDays <= 0)
            {
                overdueDays = 0;
            }
        }
        return overdueDays;
    }

    public String getPeriod()
    {
        return period;
    }

    public void setPeriod(String period)
    {
        this.period = period;
    }

    public String getContractDetailId()
    {
        return contractDetailId;
    }

    public void setContractDetailId(String contractDetailId)
    {
        this.contractDetailId = contractDetailId;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public Integer getQuantity()
    {
        return quantity;
    }

    public void setQuantity(Integer quantity)
    {
        this.quantity = quantity;
    }

    public Date getOrderDate()
    {
        return orderDate;
    }

    public void setOrderDate(Date orderDate)
    {
        this.orderDate = orderDate;
    }

    public String getOrderDateStr()
    {
        if(null != orderDate)
        {
            return DateUtils.formatDate(orderDate,"yyyy-MM-dd");
        }
        return null;
    }

    public List<ProductStore> getProductStoreList()
    {
        return productStoreList;
    }

    public void setProductStoreList(List<ProductStore> productStoreList)
    {
        this.productStoreList = productStoreList;
    }

    public BigDecimal getPcsArea()
    {
        return pcsArea;
    }

    public void setPcsArea(BigDecimal pcsArea)
    {
        this.pcsArea = pcsArea;
    }

    public String getDictOrderType()
    {
        return dictOrderType;
    }

    public void setDictOrderType(String dictOrderType)
    {
        this.dictOrderType = dictOrderType;
    }

    public String getOrganizationType()
    {
        return organizationType;
    }

    public void setOrganizationType(String organizationType)
    {
        this.organizationType = organizationType;
    }

    public Integer getType()
    {
        return type;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    public String getSaleCus()
    {
        return saleCus;
    }

    public void setSaleCus(String saleCus)
    {
        this.saleCus = saleCus;
    }

    public String getCustomerPo()
    {
        return customerPo;
    }

    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public Date getFirstBatchDate()
    {
        return firstBatchDate;
    }

    public String getFirstBatchDateStr()
    {
        if(null != firstBatchDate)
        {
            return DateUtils.formatDate(firstBatchDate,"yyyy-MM-dd");
        }
        return null;
    }

    public void setFirstBatchDate(Date firstBatchDate)
    {
        this.firstBatchDate = firstBatchDate;
    }

    public String getFirstDays()
    {
        return firstDays;
    }

    public void setFirstDays(String firstDays)
    {
        this.firstDays = firstDays;
    }

    public String getFinalDays()
    {
        return finalDays;
    }

    public void setFinalDays(String finalDays)
    {
        this.finalDays = finalDays;
    }

    public String getActualDays()
    {
        return actualDays;
    }

    public void setActualDays(String actualDays)
    {
        this.actualDays = actualDays;
    }

    public Date getEstimateStartDate()
    {
        return estimateStartDate;
    }

    public void setEstimateStartDate(Date estimateStartDate)
    {
        this.estimateStartDate = estimateStartDate;
    }

    public String getEstimateStartDateStr()
    {
        if(null != estimateStartDate)
        {
            return DateUtils.formatDate(estimateStartDate,"yyyy-MM-dd");
        }
        return null;
    }

    public Date getFirstSentTime()
    {
        return firstSentTime;
    }

    public void setFirstSentTime(Date firstSentTime)
    {
        this.firstSentTime = firstSentTime;
    }

    public String getFirstSentTimeStr()
    {
        if(null != firstSentTime)
        {
            return DateUtils.formatDate(firstSentTime,"yyyy-MM-dd");
        }
        return null;
    }

    public Integer getFirstSentDay()
    {
        return firstSentDay;
    }

    public void setFirstSentDay(Integer firstSentDay)
    {
        this.firstSentDay = firstSentDay;
    }

    public BigDecimal getFirstAveDays()
    {
        return firstAveDays;
    }

    public void setFirstAveDays(BigDecimal firstAveDays)
    {
        this.firstAveDays = firstAveDays;
    }

    public String getMaterialType()
    {
        return materialType;
    }

    public void setMaterialType(String materialType)
    {
        this.materialType = materialType;
    }
}
