package com.kyb.pcberp.modules.stock.web;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Row;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.kyb.pcberp.common.config.Global;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.stock.entity.MaterialMonth;
import com.kyb.pcberp.modules.stock.entity.ProductStore;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStockPlace;
import com.kyb.pcberp.modules.stock.service.MaterialMonthService;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

@Controller
@RequestMapping(value = "${adminPath}/stock/materialMonth")
public class MaterialMonthController extends BaseController
{
    
    @RequestMapping(value = "monthView")
    public String stockMonthList()
    {
        return "modules/stock/stockMonth";
    }
    
    @Autowired
    private MaterialMonthService materialMonthService;
    
    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"materialMonthPage"})
    @ResponseBody
    public Page<MaterialMonth> materialMonthPage(@RequestBody MaterialMonth rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        User user = UserUtils.getUser();
        if (null != rawMaterial.getQueryAll() && !rawMaterial.getQueryAll())
        {
            rawMaterial.setCreatedBy(user);
        }
        
        // 设置查询企业编号
        if (rawMaterial != null)
        {
            rawMaterial.setCompany(user.getCompany());
        }
        // 分页查询数据
        Page<MaterialMonth> qpage = new Page<MaterialMonth>(request, response);
        if (StringUtils.isNotBlank(rawMaterial.getPageNo()) && StringUtils.isNotBlank(rawMaterial.getPageSize()))
        {
            // 分页查询数据
            qpage.setPageNo(Integer.parseInt(rawMaterial.getPageNo()));
            qpage.setPageSize(Integer.parseInt(rawMaterial.getPageSize()));
        }
        else
        {
            qpage.setPageNo(Global.PAGE_NO);
            qpage.setPageNo(Global.PAGE_SIZE);
        }
        
        // 设置排序
        if (StringUtils.isNotBlank(rawMaterial.getOrderBy()))
        {
            qpage.setOrderBy(rawMaterial.getOrderBy());
        }
        return materialMonthService.findMaterialMonthPage(qpage, rawMaterial);
        
    }
    
    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"getStockList"})
    @ResponseBody
    public List<RawmaterialStock> getStockList(@RequestBody MaterialMonth rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        return materialMonthService.getStockList(rawMaterial);
        
    }
    
    @RequiresPermissions(value = {"stock:rawMaterial:view"})
    @RequestMapping(value = {"getProductStockList"})
    @ResponseBody
    public List<ProductStore> getProductStockList(@RequestBody MaterialMonth rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        return materialMonthService.getProductStockList(rawMaterial);
        
    }
    
    /**
     * 导出
     * 
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "exportMaterialMonth")
    public String exportMaterialMonth(MaterialMonth rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            if (rawMaterial == null)
            {
                rawMaterial = new MaterialMonth();
            }
            rawMaterial.setMonth(request.getParameter("month"));
            rawMaterial.setNo(request.getParameter("no"));
            rawMaterial.setMaterialTypeId(request.getParameter("materialTypeId"));
            rawMaterial.setStoreHouseId(request.getParameter("storeHouseId"));
            rawMaterial.setCompany(UserUtils.getUser().getCompany());
            List<MaterialMonth> list = materialMonthService.findMaterialMonthList(rawMaterial);
            String fileName = "月结数据" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            new ExportExcel("月结数据", MaterialMonth.class, 1).setDataList(list).write(response, fileName).dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
        
    }
    
    /**
     * 导出原材料明细
     * 
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "exportStockDeail")
    public String exportStockDeail(MaterialMonth rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            if (rawMaterial == null)
            {
                rawMaterial = new MaterialMonth();
            }
            rawMaterial.setMonth(request.getParameter("month"));
            rawMaterial.setMaterialId(request.getParameter("materialId"));
            rawMaterial.setNo(request.getParameter("no"));
            List<RawmaterialStock> list = materialMonthService.getStockList(rawMaterial);
            String fileName = rawMaterial.getNo()+"月结明细" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            new ExportExcel("月结数据", RawmaterialStockPlace.class, new Integer(0)).setDataList(list,2).write(response, fileName).dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
        
    }
    
    /**
     * 导出成品明细
     * 
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "exportProductStockDeail")
    public String exportProductStockDeail(MaterialMonth rawMaterial, HttpServletRequest request,
        HttpServletResponse response)
    {
        try
        {
            if (rawMaterial == null)
            {
                rawMaterial = new MaterialMonth();
            }
            rawMaterial.setMonth(request.getParameter("month"));
            rawMaterial.setMaterialId(request.getParameter("materialId"));
            rawMaterial.setNo(request.getParameter("no"));
            String fileName = rawMaterial.getNo()+"月结明细" + DateUtils.getDate("yyyyMMddHHmmss") + ".xlsx";
            List<ProductStore> list = materialMonthService.getProductStockList(rawMaterial);
            List<String> headerList = new ArrayList<String>();
            String[] headersPre = {"物料编号", "客户型号", "合同编号", "采购编号", "送货单号", "出入库类型", "入库数量", "出库数量", "结余", "出入库时间",
                "销售公司", "仓库", "库位", "处理人"};
            for (String s : headersPre)
            {
                headerList.add(s);
            }
            String[] headers = new String[headerList.size()];
            headerList.toArray(headers);
            ExportExcel excel = new ExportExcel("成品台账明细数据", headers);
            setDataList(excel, list, headers);
            excel.write(response, fileName).dispose();
            return null;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "redirect:" + adminPath + "/crm/quotation/view";
        
    }
    
    public void setDataList(ExportExcel excel, List<ProductStore> list, String[] hearList)
    {
        for (ProductStore one : list)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList)
            {
                String val = "";
                switch (name)
                {
                    case "物料编号":
                        val = one.getMaterial().getNo();
                        break;
                    case "客户型号":
                        val = one.getCustomerModel();
                        break;
                    case "合同编号":
                        val = one.getContractNo();
                        break;
                    case "采购编号":
                        val = one.getPrdorderNo();
                        break;
                    case "送货单号":
                        val = one.getDeliveryShowNo();
                        break;
                    case "出入库类型":
                        val = one.getInoutTypeStr();
                        break;
                    case "入库数量":
                        val = one.getInStocks() == null ? "-" : one.getInStocks().toString();
                        break;
                    case "出库数量":
                        val = one.getOutStocks() == null ? "-" : one.getOutStocks().toString();
                        break;
                    case "结余":
                        val = one.getCurrStocks() == null ? "-" : one.getCurrStocks().toString();
                        break;
                    case "出入库时间":
                        val = one.getOperateDateStr();
                        break;
                    case "销售公司":
                        val = one.getFinalComName();
                        break;
                    case "仓库":
                        val = one.getStoreHouseName();
                        break;
                    case "库位":
                        val = one.getPlaceName();
                        break;
                    case "处理人":
                        val = one.getCreatedBy() == null ? "" : one.getCreatedBy().getUserName();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }
    
    @RequiresPermissions("stock:rawMaterial:view")
    @RequestMapping(value = "resetMonthData")
    @ResponseBody
    public String resetMonthData(@RequestBody MaterialMonth mm)
        throws ParseException
    {
        materialMonthService.resetMonthData(mm);
        return "success";
    }
    
}
