/* dictCtrl */
kybApp.controller('quotationsetCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil', function ($rootScope, $scope, upida, $timeout, CommonUtil) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();

        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });

    var vm = this;
    
    $scope.shouldAutoStart = false;
    $scope.itemOptions = {
        steps:[
        {
            element: '#itemStep1',
            intro: "当您打开页面会自动加载可设置的工艺项至列表！",
            position: 'bottom'
        },
        {
            element: '#itemStep2',
            intro: "单价设置情况，单击或双击行皆可打开工艺值界面！",
            position: 'bottom'
        },
        {
            element: '#itemStep3',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };

    $scope.valueOptions = {
        steps:[
        {
            element: '#valueStep1',
            intro: "工艺值列表，单价/㎡列显示工艺值每平米的单价，如果没有工艺值请到系统设置中的数据字典中添加！",
            position: 'bottom'
        },
        {
            element: '#valueStep2',
            intro: "单击工艺值列，或双击行可打开设置界面（亲，您必须有权限哦），设置单价保存即可，<strong style='color:red;'>添加报价单明细</strong>并选择工艺是当前工艺项和工艺值时，会读取当前设置的单价！",
            position: 'bottom'
        },
        {
            element: '#valueStep5',
            intro: '谢谢使用，再见。'
        }
        ],
        showStepNumbers: false,
        exitOnOverlayClick: true,
        exitOnEsc:true,
        nextLabel: '<strong>下一步!</strong>',
        prevLabel: '<span style="color:green">上一步</span>',
        skipLabel: '退出',
        doneLabel: '谢谢'
    };
    
    $scope.houOptions = {
            steps:[
            {
                element: '#hou1',
                intro: "单价信息录入区域，带 * 的为必填项，单价/㎡代表工艺值每平米的单价，工程费中0-10工艺值代表大于0平米小于等于10平米，半孔、埋盲孔中0-10工艺值代表着大于等于0个孔小于等于10个孔！",
                position: 'top'
            },
            {
                element: '#hou2',
                intro: "单价信息录入完成，请点击保存！",
                position: 'top'
            },
            {
                element: '#hou3',
                intro: '谢谢使用，再见。'
            }
            ],
            showStepNumbers: false,
            exitOnOverlayClick: true,
            exitOnEsc:true,
            nextLabel: '<strong>下一步!</strong>',
            prevLabel: '<span style="color:green">上一步</span>',
            skipLabel: '退出',
            doneLabel: '谢谢'
        };
    
    $scope.help = function(){
    	if (vm.tabs.item.active){
    		$scope.helpItem();
    	} else if(vm.tabs.valueForm.active){
    		$scope.houValue();
    	}else{
    		$scope.helpValue();
    	}
    };
    
    vm.focus = 1;
    
    // tj 判断是否多次点击
    vm.clicks = true;
    vm.right = {};// 权限
    vm.title = ""; // 编辑标题
    vm.type = {item: 0, value: 1}; // 当前类型：项或值

    vm.item = {};     // 工艺项
    vm.editItem = {}; // 编辑对象
    vm.currOper = 0;  // 当前操作
    vm.setItem = {}; // 设置界面的ITEM

    vm.page = {}; // 分页数据
    vm.page.pageSizeOptions = [5, 10, 30, 50];// 显示数据大小

    // 工艺项分页数据
    vm.page.dictItem = {};
    vm.page.dictItem.data = {}; // 对象
    vm.page.dictItem.condition = []; // 条件
    vm.page.dictItem.pageSize = 10;  // 显示数量
    vm.page.dictItem.pageNo = 1;
    vm.page.dictItem.url = "crm/quotation/set/item/page";

    // 工艺值分页数据
    vm.page.dictValue = {};
    vm.page.dictValue.data = {}; // page对象
    vm.page.dictValue.condition = [];
    vm.page.dictValue.pageSize = 10; // 每页显示数量
    vm.page.dictValue.pageNo = 1;
    vm.page.dictValue.url = "crm/quotation/set/value/page";

    // tabs控制
    vm.tabs = {
        item: {active: true},
        value: {active: false, show: false, hasClose: false},
        valueForm: {active: false, show: false}
    };

    // 检查操作是否有单价
    vm.isDefinedOfPrice = function (price) {
        return typeof price !== "undefined";
    };

    // 显示值
    vm.showValue = function () {
        vm.tabs.value.active = true;
        vm.tabs.value.show = true;
        vm.tabs.value.hasClose = false;
    };

    // 隐藏值
    vm.hideValue = function () {
        vm.tabs.value.show = false;

        // 当前工艺编辑处于激活状态时，关闭工艺值列表时不进行跳转
        vm.tabs.value.hasClose = true;
        if (vm.tabs.valueForm.active) {
            $timeout(function () {
                vm.tabs.valueForm.active = true;
            });
        } else {
            $timeout(function () {
                vm.tabs.item.active = true;
            });
        }
    };

    // 显示单价编辑
    vm.showValueForm = function () {
        vm.tabs.valueForm.show = true;
        vm.tabs.valueForm.active = true;
        vm.formScope.editForm.$setPristine();
    };

    // 隐藏单价编辑
    vm.hideValueForm = function () {
        vm.tabs.valueForm.show = false;

        // 值已经关闭后，当关闭工艺单价编辑的时候直接跳到工艺项列表
        if (vm.tabs.value.hasClose) {
            $timeout(function () {
                vm.tabs.item.active = true;
            });
        } else {
            $timeout(function () {
                vm.tabs.value.active = true;
            });
        }
    };

    // 分页按钮单击处理
    vm.doPage = function (type, page, pageSize, total) {
        if (type === vm.type.item) {
            vm.page.dictItem.pageNo = page;
            vm.init(page,
                pageSize,
                vm.page.dictItem.condition,
                vm.page.dictItem.url,
                vm.type.item);
        } else {
            vm.page.dictValue.pageNo = page;
            vm.init(page,
                pageSize,
                vm.page.dictValue.condition,
                vm.page.dictValue.url,
                vm.type.value);
        }
    };

    /*
     * 兼容条件查询与普通查询
     * no 当前页
     * size 当前页显示数量
     * condition 是数组，可放入多个条件对象，如{name:"type", value:"1"}  名称对应实体字段名称，值对应页面输入的值
     * url 链接
     * type 类型
     */
    vm.init = function (no, size, condition, url, type) {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
    	
        // 请求数据
        var reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;

        // 是值的时候需要设置请求数据的项
        if (type === vm.type.value) {
            reqData.item = vm.item;
        } else {
            // 设置过滤条件
            if (condition.length > 0) {
                angular.forEach(condition, function (p) {
                    reqData[p.name] = p.value;
                });
            }
        }

        // 请求分页数据
        upida.post(url, reqData).then(function (result) {
            var data = {};
            data.url = url;
            data.data = result;
            data.condition = condition;
            data.pageSize = size;
            data.pageNo = no;

            // 如果结果为空
            if (typeof data.data === 'undefined' || typeof data.data.list === 'undefined') {
                data.data.startCount = 0;
                data.data.endCount = 0;
            } else {
                // 计算开始数
                data.data.startCount = (result.pageNo - 1) * result.pageSize + 1;
                // 计算结束数
                data.data.endCount = (result.pageNo - 1) * result.pageSize + result.list.length;
                if (type === vm.type.value) {
                    // 排序
                    data.data.list.sort(CommonUtil.compare("seqNum", true))
                }
            }

            if (type === vm.type.item) {
                vm.page.dictItem = data;
            } else {
                // 排序显示
                vm.page.dictValue = data;
            }
            MainCtrl.unblockUI();
        });
    };

    // 页面显示数量改变
    vm.pageSizeChange = function (type) {
        if (type === vm.type.item) {
            vm.init(1,
                vm.page.dictItem.pageSize,
                vm.page.dictItem.condition,
                vm.page.dictItem.url,
                type);
        } else {
            vm.init(1,
                vm.page.dictValue.pageSize,
                vm.page.dictValue.condition,
                vm.page.dictValue.url,
                type);
        }
    };

    vm.showDetial = function (index) {
        // 得到工艺项
        vm.item = angular.copy(vm.page.dictItem.data.list[index]);

        // 初始化第一页，条件为空
        vm.init(1,
            vm.page.dictValue.pageSize,
            [],
            vm.page.dictValue.url,
            vm.type.value);

        // 显示值视图
        vm.showValue();
    };

    vm.setFormScope = function (scope) {
        vm.formScope = scope;
    };

    // 单击修改
    vm.modPrice = function (index, type) {
        if (!vm.right.edit) {
            return;
        }
        
        vm.focus += 1;
        
        // 设置单价的Item项为当前选择项
        vm.setItem = angular.copy(vm.item);

        // 待修改的工艺值对象
        vm.editItem = angular.copy(vm.page.dictValue.data.list[index]);

        // 当前操作默认为修改，若工艺价格不存在则为添加
        vm.currOper = 0;
        if (typeof vm.editItem.craftPrice === "undefined") {
            vm.currOper = 1;
            vm.editItem.craftPrice = {};
        }

        vm.showValueForm();
    };

    // 单击取消
    vm.cacelMod = function () {
        vm.hideValueForm();
    };

    // 创建和修改字典
    vm.submitMod = function () {
    	if(vm.clicks){
    	    vm.clicks = false; // tj 2017-08-22  用户是否多次点击
    	    vm.formScope.editForm.$setDirty();

            if (!vm.formScope.editForm.$valid) {
            	vm.clicks = true;// tj 2017-09-07  用户是否多次点击
                return;
            }

            var data = {};

            // 修改
            if (vm.currOper === 0) {
                data.price = vm.editItem.craftPrice.price;
                data.recordId = vm.editItem.craftPrice.recordId;
            } else {
                // 添加
                data.itemId = vm.item.recordId;
                data.valueId = vm.editItem.recordId;
                data.price = vm.editItem.craftPrice.price;
            }

            MainCtrl.blockUI({
        	    animate: true,
        	});
            
            upida.post("crm/quotation/set/save", data).then(function (data) {
                // 重新查询工艺值列表
                vm.init(vm.page.dictValue.pageNo,
                    vm.page.dictValue.pageSize,
                    vm.page.dictValue.condition,
                    vm.page.dictValue.url,
                    vm.type.value);
                vm.init(vm.page.dictItem.pageNo,
                	vm.page.dictItem.pageSize,
                    vm.page.dictItem.condition,
                    vm.page.dictItem.url,
                    vm.type.item);
                // 提示信息
                vm.message = data.message;
                vm.clicks = true;// tj 2017-09-07  用户是否多次点击
                $('#static').modal();
                vm.hideValueForm();
            });			
    	}
    };

    // 加载权限
    function loadRight() {
    	MainCtrl.blockUI({
    	    animate: true,
    	});
        upida.get("common/rightall?prefix=crm:quotation_s").then(function (data) {
            vm.right.view = data.view;
            vm.right.edit = data.edit;
            vm.right.manage = data.manage;

            // 初始化第一页，条件为空
            vm.init(1,
                vm.page.dictItem.pageSize,
                [],
                vm.page.dictItem.url,
                vm.type.item);
        });
    }

    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
    });
}]);