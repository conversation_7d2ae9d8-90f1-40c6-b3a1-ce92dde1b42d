//
// Responsive: Landscape phone to desktop/tablet
// --------------------------------------------------


@media (max-width: 767px) {
  .jumbotron, footer, #social-buttons {
    padding-left: 20px;
    padding-right: 20px;
    margin-right: -20px;
    margin-left: -20px;
  }

  #iconCarousel {
    @size: 180px;
    font-size: @size;
    line-height: @size;
    width: 280px;
    margin: 30px auto 0;
    .carousel-control {
      top: @size / 2;
      .square(44px);
      font-size: 44px;
      line-height: 44px;
      left: -7px;
      &.right { right: -7px; }
    }
  }

  .jumbotron-index {
    h1 { font-size: 58px; }
    p { font-size: 24px; }
    .btn-large {
      font-size: 20px;
      padding: 14px 26px;
    }
    .shameless-self-promotion { font-size: 12px; }
  }

  .jumbotron-ad {
    h1 { font-size: 39px; }
    p {
      font-size: 20px;
      margin-bottom: 20px;
    }
  }

  .jumbotron-icon {
    .icon-1, .icon-2, .icon-3, .icon-4, .icon-5, .icon-6 { margin-right: 0; }
    .icon-6 { font-size: 16em; }
    h1 small {
      display: block;
      margin-top: 15px;
      margin-left: 0;
      line-height: 20px;
    }
  }

  .stripe-ad .lead {
    margin: 0 0 30px;
  }

  #carbonads-container {
    margin-right: -20px;
    margin-left: -20px;
    .carbonad {
      width: 100%;
      height: 120px;
      border-right: none;
      border-left: none;
    }
  }
  #azcarbon {
    width: 300px;
    margin: 0 auto;
  }

  .sticky-footer(170px, 40px, 40px, 60px); // sets default values for sticky footer
  .footer {
    padding-left: 20px;
    padding-right: 20px;
  }
}
