package com.kyb.pcberp.modules.finance.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.finance.entity.UploadSetting;
import com.kyb.pcberp.modules.finance.entity.UploadSettingDetail;

@MyBatisDao
public interface UploadSettingDao extends CrudDao<UploadSetting>
{

    void batchInsertDetail(@Param("list") List<UploadSettingDetail> details);

    List<UploadSettingDetail> getDetail(UploadSetting entity);

    void updateFileColumn(UploadSettingDetail dt);

    String getMaxNo(UploadSetting entity);

    void updateStatus(UploadSetting uploadSetting);

    UploadSetting findExsit(UploadSetting entity);

    UploadSetting getSettingName(UploadSetting uploadSetting);

    Integer findUploadSettingNoEnable(UploadSetting uploadSetting);
}
