kybApp.controller('reportCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil', 'BaseUtil', '$filter', function ($rootScope, $scope, upida, $timeout, CommonUtil, BaseUtil, $filter) {
    $scope.$on('$viewContentLoaded', function () {
        MainCtrl.initAjax();
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
    var vm = this;

    //弹窗空值
    vm.tabs = 
    {
        index: {active: true},
        opendetail: {active: false,show:false},
        section: {active: false,show:false}
    };
    
    // 显示form
    function showForm()
    {
    	if(vm.tabs.opendetail.show)
    	{
            $timeout(function () {
            	vm.tabs.opendetail.active = true;
            });
    	}
    	else if(vm.tabs.section.show)
    	{
            $timeout(function () {
            	vm.tabs.section.active = true;
            	vm.tabs.section.show = false;
            });
    	}
    	else
    	{
            $timeout(function () {
            	vm.tabs.index.active = true;
            });
    	}
    }
    
    //日
    vm.dayStyle = {};
    //日选中
    vm.daySelectStyle ={"float": "left","border-right": "1px solid #1caf9a","width": "33%","text-align": "center","padding-top": "0.6vh","padding-bottom": "0.6vh","background-color": "#1caf9a","color": "#fff"};
    //日未选中
    vm.dayNoSelectStyle ={"float": "left","border-right": "1px solid #1caf9a","width": "33%","text-align": "center","padding-top": "0.6vh","padding-bottom": "0.6vh"};

    //周
    vm.weekStyle = {};
    //周选中
    vm.weekSelectStyle ={"float": "left","border-right": "1px solid #1caf9a","width": "33%","text-align": "center","padding-top": "0.6vh","padding-bottom": "0.6vh","background-color": "#1caf9a","color": "#fff"};
    //周未选中
    vm.weekNoSelectStyle ={"float": "left","border-right": "1px solid #1caf9a","width": "33%","text-align": "center","padding-top": "0.6vh","padding-bottom": "0.6vh"};

    //月
    vm.monthStyle = {};
    //月选中
    vm.monthSelectStyle ={"float": "left","width": "34%","text-align": "center","padding-top": "0.6vh","padding-bottom": "0.6vh","background-color": "#1caf9a","color": "#fff"};
    //月未选中
    vm.monthNoSelectStyle ={"float": "left","width": "32.8%","text-align": "center","padding-top": "0.6vh","padding-bottom": "0.6vh"};

    vm.showFirstStr = "";
    vm.showAfterStr = "";
    
    vm.showReport = {};
	vm.deptIdQuery = null;
    vm.loadData = function () {
        MainCtrl.blockUI({
            animate: true,
        });
		switch (vm.report.dateType) 
		{
			// 日
			case 1:
				vm.showFirstStr = "昨日";
				vm.showAfterStr = "今日";
				break;
			// 周
			case 2:
				vm.showFirstStr = "上周";
				vm.showAfterStr = "本周";
				break;
			// 月
			case 3:
				vm.showFirstStr = "上月";
				vm.showAfterStr = "本月";
				break;
		}
		vm.showReportList = [];
		vm.report.deptId = vm.deptIdQuery;
        upida.post("report/reportData/getAllReportData", vm.report).then(function (data) {
        	if(data.result == "success")
        	{
        		var reportList = data.reportList;// 报表数据
        		var overDueReportList = data.overDueReportList;// 未交货逾期
        		var list = handleOverDueReport(reportList,overDueReportList,1);
        		vm.showReportList = handleReportData(list);
        	}
        	else
        	{
             	vm.message = data.message;
                $('#static').modal();
        	}
        	MainCtrl.unblockUI();
        });
    };

    // 处理逾期和报表数据
    function handleOverDueReport(reportList,overDueReportList,num)
    {
    	if(num == 1)
    	{
			// 逾期区间数据需要累加
			if(overDueReportList && overDueReportList.length > 0)
			{
				var obj1 = {};// 当前
				var obj2 = {};// 上一个
				var obj3 = {};// 上上一个
				var obj4 = {};
				for(let obj of overDueReportList)
				{
					if(obj.sequence == '1')
					{
						obj1 = obj;
					}
					else if(obj.sequence == '2')
					{
						obj2 = obj;
					}
					else if(obj.sequence == '3')
					{
						obj3 = obj;
					}
					else
					{
						obj4 = obj;
					}        				
				}
				var overDueSum = obj1 && obj1.overDueSum ? Number(obj1.overDueSum) : 0;
				var overDueAmount = obj1 && obj1.overDueAmount ? Number(obj1.overDueAmount) : 0;
				var overDueSumYester = obj2 && obj2.overDueSum ? Number(obj2.overDueSum) : 0;
				var overDueAmountYester = obj2 && obj2.overDueAmount ? Number(obj2.overDueAmount) : 0;
				var overDueSumBeforeYester = obj3 && obj3.overDueSum ? Number(obj3.overDueSum) : 0;
				var overDueAmountBeforeYester = obj3 && obj3.overDueAmount ? Number(obj3.overDueAmount) : 0;
				var overDueSumFour = obj4 && obj4.overDueSum ? Number(obj4.overDueSum) : 0;
				var overDueAmountFour = obj4 && obj4.overDueAmount ? Number(obj4.overDueAmount) : 0;
				obj3.overDueSum = overDueSum + overDueSumYester + overDueSumBeforeYester + overDueSumFour;
				obj3.overDueAmount = overDueAmount + overDueAmountYester + overDueAmountBeforeYester + overDueAmountFour;
				obj2.overDueSum = overDueSumYester + overDueSum + overDueSumFour;
				obj2.overDueAmount = overDueAmountYester + overDueAmount + overDueAmountFour;
			}
    	}
    	
		// 逾期数据和报表数据合并
		var list = [];
		if(overDueReportList && overDueReportList.length > 0
		&& reportList && reportList.length > 0)
		{
			for(let obj1 of overDueReportList)
			{
				for(let obj2 of reportList)
				{
					if(num == 1)
					{
						if((!obj1.reportDate || obj1.reportDate == obj2.reportDate) && obj1.reportType == obj2.reportType)
						{
							obj1.flag = true;
							obj2.overDueSum = obj1.overDueSum ? obj1.overDueSum : 0;
							obj2.overDueAmount = obj1.overDueAmount ? obj1.overDueAmount.toFixed(4) : 0;
							break;
						}
					}
					else if(num == 2)
					{
						if(obj1.reportType == obj2.reportType)
						{
							obj1.flag = true;
							obj2.overDueSum = obj1.overDueSum ? obj1.overDueSum : 0;
							obj2.overDueAmount = obj1.overDueAmount ? obj1.overDueAmount.toFixed(4) : 0;
							break;
						}
					}
				}
			}
			// 如果报表没数据，逾期有数据，把逾期赋值进来
/*			for(let obj of overDueReportList)
			{
				if(!obj.flag)
				{
					reportList.push(obj);
				}
			}*/
			// reportList.push(obj2);
			reportList.push(obj3);
			list = reportList;
		}
		else if(overDueReportList && overDueReportList.length > 0)
		{
			list = overDueReportList;
		}
		else if(reportList && reportList.length > 0)
		{
			list = reportList;
		}
		return list;
    }
    
    // 处理报表数据
    vm.showReportList = [];
    function handleReportData(reportList)
    {
    	var list = [];
    	if(reportList && reportList.length > 0)
    	{
    		var showReportList = [];
    		angular.forEach(reportList,function(obj){
    			var report = {};
    			switch (obj.reportType) {
    				// 销售
					case "1":
						report.status = 1;
						report.moduleValue = "接单";
						
						// 创建报表集合
						createdList(6,report);
						
						// 接单面积 (㎡)
						handleConcreteReportData("orderArea","接单面积 (㎡)",obj.area,obj.sequence,1,report,2);
						
						// 接单金额 (元)
						handleConcreteReportData("orderAmount","接单金额 (元)",obj.amount,obj.sequence,1,report,2);
						
						// 接单款数 (款)
						handleConcreteReportData("orderSums","接单款数 (款)",obj.sums,obj.sequence,1,report,2);
						
						// 平均单价 (元)
						handleConcreteReportData("orderAvgPrice","平均单价 (元)",obj.avgPrice,obj.sequence,1,report,2);
						
						// 未交货逾期款数 (款)
						handleConcreteReportData("overDueSum","未交货逾期款数 (款)",obj.overDueSum,obj.sequence,1,report,2);
						
						// 未交货逾期金额 (元)
						handleConcreteReportData("overDueAmount","未交货逾期金额 (元)",obj.overDueAmount,obj.sequence,1,report,2);
						
						break;
					// 交货
					case "2":
						report.status = 2;
						report.moduleValue = "交货";
						
						// 创建报表集合
						createdList(3,report);
						
						// 销售面积 (㎡)
						handleConcreteReportData("salesArea","销售面积 (㎡)",obj.area,obj.sequence,2,report,2);
						
						// 销售金额 (元)
						handleConcreteReportData("salesAmount","销售金额 (元)",obj.amount,obj.sequence,2,report,2);
						
						// 已交货款数 (款)
						handleConcreteReportData("deliveredSums","已交货款数 (款)",obj.sums,obj.sequence,2,report,2);
						
						break;
					// 客诉
					case "3":
						report.status = 3;
						report.moduleValue = "客诉";
						
						// 创建报表集合
						createdList(3,report);
						
						// 客诉面积 (㎡)
						handleConcreteReportData("rejectArea","客诉面积 (㎡)",obj.area,obj.sequence,3,report,2);
						
						// 客诉金额 (元)
						handleConcreteReportData("rejectAmount","客诉金额 (元)",obj.amount,obj.sequence,3,report,2);
						
						// 客诉款数 (款)
						handleConcreteReportData("rejectSums","客诉款数 (款)",obj.sums,obj.sequence,3,report,2);
						
						break;
					// 原料采购
					case "4":
						report.status = 4;
						report.moduleValue = "采购";
						
						// 创建报表集合
						createdList(3,report);
						
						// 原料采购金额 (元)
						handleConcreteReportData("purchasingAmount","原料采购金额 (元)",obj.amount,obj.sequence,4,report,2);
						
						// 成品采购金额 (元)
						handleConcreteReportData("prdorderAmount","成品采购金额  (元)",0,obj.sequence,4,report,2);
						
						// 成品采购面积 (㎡)
						handleConcreteReportData("prdorderArea","成品采购面积 (㎡)",0,obj.sequence,4,report,2);
						
						break;
					// 成品采购
					case "5":
						report.status = 4;
						report.moduleValue = "采购";
						
						// 创建报表集合
						createdList(3,report);
						
						// 原料采购金额 (元)
						handleConcreteReportData("purchasingAmount","原料采购金额 (元)",0,obj.sequence,4,report,2);
						
						// 成品采购金额 (元)
						handleConcreteReportData("prdorderAmount","成品采购金额  (元)",obj.amount,obj.sequence,4,report,2);
						
						// 成品采购面积 (㎡)
						handleConcreteReportData("prdorderArea","成品采购面积 (㎡)",obj.area,obj.sequence,4,report,2);
						
						break;
					// 收款
					case "6":
						report.status = 5;
						report.moduleValue = "财务";
						
						// 创建报表集合
						createdList(6,report);
						
						// 收款款数 (款)
						handleConcreteReportData("receivableSums","收款款数 (款)",obj.sums,obj.sequence,5,report,2);
						
						// 收款金额 (元)
						handleConcreteReportData("receivableAmount","收款金额 (元)",obj.amount,obj.sequence,5,report,2);
						
						// 付款款数 (款)
						handleConcreteReportData("payableSums","付款款数 (款)",0,obj.sequence,5,report,2);
						
						// 付款金额 (元)
						handleConcreteReportData("payableAmount","付款金额 (元)",0,obj.sequence,5,report,2);
						
						// 已收金额 (元)
						handleConcreteReportData("receivedAmount","已收金额 (元)",0,obj.sequence,5,report,2);
						
						// 已付金额 (元)
						handleConcreteReportData("paidAmount","已付金额 (元)",0,obj.sequence,5,report,2);
						
						break;
					// 付款
					case "7":
						report.status = 5;
						report.moduleValue = "财务";
						
						// 创建报表集合
						createdList(6,report);
						
						// 收款款数 (款)
						handleConcreteReportData("receivableSums","收款款数 (款)",0,obj.sequence,5,report,2);
						
						// 收款金额 (元)
						handleConcreteReportData("receivableAmount","收款金额 (元)",0,obj.sequence,5,report,2);
						
						// 付款款数 (款)
						handleConcreteReportData("payableSums","付款款数 (款)",obj.sums,obj.sequence,5,report,2);
						
						// 付款金额 (元)
						handleConcreteReportData("payableAmount","付款金额 (元)",obj.amount,obj.sequence,5,report,2);
						
						// 已收金额 (元)
						handleConcreteReportData("receivedAmount","已收金额 (元)",0,obj.sequence,5,report,2);
						
						// 已付金额 (元)
						handleConcreteReportData("paidAmount","已付金额 (元)",0,obj.sequence,5,report,2);
						
						break;
					// 已收
					case "8":
						report.status = 5;
						report.moduleValue = "财务";
						
						// 创建报表集合
						createdList(6,report);
						
						// 收款款数 (款)
						handleConcreteReportData("receivableSums","收款款数 (款)",0,obj.sequence,5,report,2);
						
						// 收款金额 (元)
						handleConcreteReportData("receivableAmount","收款金额 (元)",0,obj.sequence,5,report,2);
						
						// 付款款数 (款)
						handleConcreteReportData("payableSums","付款款数 (款)",0,obj.sequence,5,report,2);
						
						// 付款金额 (元)
						handleConcreteReportData("payableAmount","付款金额 (元)",0,obj.sequence,5,report,2);
						
						// 已收金额 (元)
						handleConcreteReportData("receivedAmount","已收金额 (元)",obj.amount,obj.sequence,5,report,2);
						
						// 已付金额 (元)
						handleConcreteReportData("paidAmount","已付金额 (元)",0,obj.sequence,5,report,2);
						
						break;
					//已付
					case "9":
						report.status = 5;
						report.moduleValue = "财务";
						
						// 创建报表集合
						createdList(6,report);
						
						// 收款款数 (款)
						handleConcreteReportData("receivableSums","收款款数 (款)",0,obj.sequence,5,report,2);
						
						// 收款金额 (元)
						handleConcreteReportData("receivableAmount","收款金额 (元)",0,obj.sequence,5,report,2);
						
						// 付款款数 (款)
						handleConcreteReportData("payableSums","付款款数 (款)",0,obj.sequence,5,report,2);
						
						// 付款金额 (元)
						handleConcreteReportData("payableAmount","付款金额 (元)",0,obj.sequence,5,report,2);
						
						// 已收金额 (元)
						handleConcreteReportData("receivedAmount","已收金额 (元)",0,obj.sequence,5,report,2);
						
						// 已付金额 (元)
						handleConcreteReportData("paidAmount","已付金额 (元)",obj.amount,obj.sequence,5,report,2);
						break;
				}
    			showReportList.push(report);
    		});
    		
    		// 把相同模块的数据值累计
    		if(showReportList && showReportList.length > 0)
    		{
    			angular.forEach(showReportList,function(p){
    				if(list && list.length > 0)
    				{
    					var flag = false;
    					for(let obj of list)
    					{
    						if(obj.status == p.status)
    						{
    							flag = true;
    							break;
    						}
    					}
    					if(flag)
    					{
        					for(let obj of list)
        					{
        						if(obj.status == p.status)
        						{
        							angular.forEach(obj.reportValueList,function(p1){
            							angular.forEach(p.reportValueList,function(p2){
            								if(p1.name == p2.name)
            								{
            									// typeValue:1 字符串型,2:整型或浮点型
            									if(p1.typeValue == 2)
            									{
            										p1.value1 = (p1.value1 ? Number(p1.value1) : 0) + (p2.value1 ? Number(p2.value1) : 0);
                									p1.value2 = (p1.value2 ? Number(p1.value2) : 0) + (p2.value2 ? Number(p2.value2) : 0);
            									}
            									else if(p1.typeValue == 1)
            									{
            										if(p1.value1 && p1.value1 != '')
            										{
            											if(p2.value1 && p2.value1 != '')
            											{
            												p1.value1 = p1.value1 +","+ p2.value1;
            											}
            										}
            										else
            										{
            											p1.value2 = p2.value2;
            										}
            									}
            								}
            							});
        							});
        						}
        					}
    					}
    					else
    					{
    						list.push(p);
    					}
    				}
    				else
    				{
    					list.push(p);
    				}
    			});
    		}
    	}
    	return list;
    }
    
    // 创建报表集合
    function createdList(size,report)
    {
    	report.reportValueList = [];
    	for(var i=0;i<size;i++)
    	{
    		var obj = {};
    		obj.status = report.status;//模块
    		report.reportValueList.push(obj);
    	}
    }
    
    // 处理具体报表数据(name:属性值名称,nameValue:显示属性值名称,value:属性值,sequence:排序方式,status:模块值,typeValue:值类型(1:字符串型,2:整数或浮点型))
    function handleConcreteReportData(name,nameValue,value,sequence,status,report,typeValue)
    {
    	for(let obj of report.reportValueList)
    	{
    		if(!obj.showFlag && (!obj.name || obj.name == name) && obj.status == status)
    		{
    			obj.typeValue = typeValue
    			obj.name = name
        		if(!obj.nameValue || obj.nameValue == '')
        		{
        			obj.nameValue = nameValue;
        		}
        		if(sequence == '1')
        		{
        			obj.value1 = value;
        		}
        		else if(sequence == '2')
        		{
        			obj.value2 = value;
        		}
        		if(obj.value1 && obj.value1 != '' && obj.value2 && obj.value2 != '')
        		{
        			obj.showFlag = true;
        		}
        		break;
    		}
    	}
    }
    
    //日、周、月样式初始化
    function clearCss() {
        vm.dayStyle = vm.dayNoSelectStyle;
        vm.weekStyle = vm.weekNoSelectStyle;
        vm.monthStyle = vm.monthNoSelectStyle;
        vm.year = null;
        vm.month = null;
        vm.week = null;
    }

    //格式化日期
    function formatDate(date) {
        var myyear = date.getFullYear();
        var mymonth = date.getMonth() + 1;
        var myweekday = date.getDate();
        if (mymonth < 10) {
            mymonth = "0" + mymonth;
        }
        if (myweekday < 10) {
            myweekday = "0" + myweekday;
        }
        return (myyear + "-" + mymonth + "-" + myweekday);
    };

    // 某个月有多少天
    function mGetDate(year, month){
        var d = new Date(year, month, 0);
        return "-"+d.getDate();
    }
    
    vm.yearList = [];
    vm.years = null;
    vm.months = null;
    vm.weeks = null;
    function getYearMonthWeek()
    {
    	vm.yearList = [];
		var date = new Date();//当天
		vm.years = date.getFullYear();//当前年
		for(var i=0;i<=10;i++)
		{
			var year = {};
			year.id = vm.years - i;
			year.name = year.id + "年";
			var monthList = [];
			for(var k=0;k<12;k++)
			{
				var month = {};
				month.id = k + 1;
				month.name = month.id + "月";
				if(month.id < 10)
				{
					month.queryDate = year.id + "-0" + month.id + '-01';
					month.startTime = year.id + "-0" + month.id + '-01';
					month.endTime = year.id + "-0" + month.id + mGetDate(year.id,month.id);	
				}
				else
				{
					month.queryDate = year.id + "-" + month.id + '-01';
					month.startTime = year.id + "-" + month.id + '-01';
					month.endTime = year.id + "-" + month.id + mGetDate(year.id,month.id);
				}
				if(year.id == vm.years && k == date.getMonth())
				{
					vm.months = k + 1;
				}
				monthList.push(month);
			}
			year.monthList = monthList;
			
			// 一年第一个周一
		    var firstDay = new Date(year.id, 0, 1) 
			while (firstDay.getDay() != 1) {
				firstDay.setDate(firstDay.getDate() + 1);
		    }
		    var to = new Date(year.id, 0, 1) 
		    while (to.getDay() != 1) {
		    	to.setDate(to.getDate() + 1);
		    }
		    // 下一年的周一
		    var lastDay = new Date(year.id + 1, 0, 1) 
		    while (lastDay.getDay() != 1) {
		    	lastDay.setDate(lastDay.getDate() + 1);
		    }
		    // 这一年共多少天
		    var days = Math.floor((lastDay - firstDay) / (24 * 3600 * 1000));
		    // 这一年共多少周
		    var w = days / 7;
		    var weekList = [];
		    var j = 0;
		    for(var from = firstDay; from < lastDay;)
		    {
		    	var week = {};
		    	week.id = j + 1;
		    	week.name = week.id + "周";
		    	to.setDate(to.getDate() + 7)
		    	if(date > from && date < to)
		    	{
		    		vm.weeks = week.id;
		    	}
		    	// 计算每周的开始结束日期
		    	var startTime = year.id+"-"+((from.getMonth() + 1) > 9 ? (from.getMonth() + 1) : '0'+(from.getMonth() + 1))+"-"+(from.getDate() > 9 ? from.getDate() : '0'+from.getDate());
		    	var endTime = null;
		    	from.setDate(from.getDate() + 6);
		        if (from < lastDay) {
		        	endTime = year.id+"-"+((from.getMonth() + 1) > 9 ? (from.getMonth() + 1) : '0'+(from.getMonth() + 1)) + "-" + (from.getDate() > 9 ? from.getDate() : '0'+from.getDate());
		        	if(startTime > endTime){
		        		endTime = year.id+1+"-"+((from.getMonth() + 1) > 9 ? (from.getMonth() + 1) : '0'+(from.getMonth() + 1)) + "-" + (from.getDate() > 9 ? from.getDate() : '0'+from.getDate());
		        	}
		            from.setDate(from.getDate() + 1);
		        } else {
		        	endTime = year.id+"-"+((lastDay.getMonth() + 1) > 9 ? (lastDay.getMonth() + 1) : '0'+(lastDay.getMonth() + 1)) + "-" + (lastDay.getDate() > 9 ? lastDay.getDate() : '0'+lastDay.getDate());
		        	if(startTime > endTime){
		        		endTime = year.id+1+"-"+((lastDay.getMonth() + 1) > 9 ? (lastDay.getMonth() + 1) : '0'+(lastDay.getMonth() + 1)) + "-" + (lastDay.getDate() > 9 ? lastDay.getDate() : '0'+lastDay.getDate());
		        	}
		        	lastDay.setDate(lastDay.getDate() - 1);
		        }
		        week.startTime = startTime;
		        week.endTime = endTime;
		    	weekList.push(week);
		    	j++
		    }
		    year.weekList = weekList;
		    vm.yearList.push(year);
		}
    }
    
    vm.report = {};
    vm.selectReport = function(item)
    {
    	clearCss();//日、周、月样式初始化
		vm.report = {};
    	vm.report.queryDate = '';
        vm.report.dateType = item;
    	switch (item) {
    		// 日
			case 1:
				 vm.dayStyle = vm.daySelectStyle;
				 vm.report.queryDate = formatDate(new Date());
				 vm.report.dateType = 1
	    		 vm.report.sentTimeStartQr = formatDate(new Date());
	    		 vm.report.sentTimeEndQr = formatDate(new Date());
				 vm.loadData();
				break;
			// 周
			case 2:
				vm.weekStyle = vm.weekSelectStyle;
				vm.year = vm.years;
				vm.week = vm.weeks;
				vm.getMonthWeek();
				vm.setWeek();
				break;
			// 月
			case 3:
				vm.monthStyle = vm.monthSelectStyle;
				vm.year = vm.years;
				vm.month = vm.months;
				vm.getMonthWeek();
				vm.setMonth();
				break;
		}
    };
    
    // 选择年获取月和周
    vm.getMonthWeek = function()
    {
    	angular.forEach(vm.yearList,function(year){
    		if(vm.year == year.id)
    		{
    			vm.monthList = year.monthList;
    			vm.weekList = year.weekList
    		}
    	});
    };
    // 选择月、周、日
    vm.selectMonthWeekDay = function(num)
    {
    	// 月
    	if(num == 1)
    	{
    		vm.setMonth();
    	}
    	// 周
    	else if(num == 2)
    	{
    		vm.setWeek();
    	}
    	// 日
    	else if(num == 3)
    	{
    		vm.report.dateType = 1
    		vm.report.queryDate = formatDate(new Date(vm.report.queryDate));
    		vm.report.sentTimeStartQr = formatDate(new Date(vm.report.queryDate));
    		vm.report.sentTimeEndQr = formatDate(new Date(vm.report.queryDate));
    		vm.loadData();
    	}
    }
    vm.setMonth = function()
    {
    	angular.forEach(vm.monthList,function(month){
    		if(vm.month == month.id)
    		{
    			vm.report.dateType = 3;
    			vm.report.queryDate = month.queryDate;
        		vm.report.sentTimeStartQr = month.startTime;
        		vm.report.sentTimeEndQr = month.endTime;
    			return;
    		}
    	});
    	vm.loadData();
    }
    vm.setWeek = function()
    {
    	angular.forEach(vm.weekList,function(week){
    		if(vm.week == week.id)
    		{
    			vm.report.dateType = 2;
    			vm.report.queryDate = week.startTime;
        		vm.report.sentTimeStartQr = week.startTime;
        		vm.report.sentTimeEndQr = week.endTime;
    			return;
    		}
    	});
    	vm.loadData();
    }
    
    // 查看详情
    vm.reportDetail={};
    vm.roleList = []; // 人员组list
    vm.propertyValue = null;
    vm.showReportDetailData = function(report,reportValue1,reportValue2)
    {
    	if(report.status == 5)
    	{
	       	 vm.message = "暂未开放！";
	         $('#static').modal();
	         return;
    	}
    	
    	vm.roleList = [];
    	vm.reportDetail.dateType = vm.report.dateType;
    	vm.reportDetail.queryDate = vm.report.queryDate;
		vm.reportDetail.deptId = vm.report.deptId;
    	vm.orderBy = "2";// 从大到小
    	
    	// 赋值人员类型组
    	setRoleList(report);
    	
    	// 查看当时的某个属性的详情
    	if(reportValue1)
    	{
    		var value1 = angular.copy(reportValue1);
    		vm.reportDetail.sequence = 1;
    		vm.reportDetail.reportValueList = [];
    		vm.reportDetail.typeStr = getTypeStr(value1.name);
    		value1.value2 = null;
    		vm.reportDetail.reportValueList.push(value1);
    		vm.propertyValue = value1.name;
    	}
    	// 查看上一个的某个属性的详情
    	else if(reportValue2)
    	{
    		var value2 = angular.copy(reportValue2);
    		vm.reportDetail.sequence = 2;
    		vm.reportDetail.reportValueList = [];
    		vm.reportDetail.typeStr = getTypeStr(value2.name);
    		value2.value1 = null;
    		vm.reportDetail.reportValueList.push(value2);
    		vm.propertyValue = value2.name;
    	}
    	// 查看详情
    	else
    	{
    		vm.reportDetail.typeStr = getTypeStr(report.reportValueList[0].name);
    		vm.reportDetail.sequence = 1;
    		vm.reportDetail.reportValueList = report.reportValueList;
    		vm.propertyValue = report.reportValueList[0].name;
    	}
    	vm.reportDetail.status = report.status;
    	vm.loadReportDetailData();
    }
    
    // 加载详情数据
    vm.personList = [];
    vm.showValueList = [];
    vm.loadReportDetailData = function()
    {
    	  MainCtrl.blockUI({
              animate: true,
          });
    	  vm.personList = [];
    	  vm.reportList = [];
  		  vm.reportDetail.selectValue = null;
  		  vm.reportDetail.personId = '';
    	  
          upida.post("report/reportData/getReportDetailData", vm.reportDetail).then(function (data) {
          	if(data.result == "success")
          	{
          		vm.personList = data.personList;
          		if(vm.personList && vm.personList.length > 0)
          		{
					var object = {name: '所有', recodId: ''};
					vm.personList.splice(0, 0, object);
				}

                vm.reportList = data.reportList;
                
          		switch (vm.reportDetail.status) {
          			// 销售
					case 1:
						vm.contractDetailListCopy = data.contractDetailList;
						vm.overDueCdListCopy = data.overDueCdList;
						vm.overDueReportList = handleOverDueReportData(data.overDueReportList);
						break;
					// 交货
					case 2:
						vm.deliveryDetailListCopy = data.deliveryDetailList;
						break;
					// 客诉
					case 3:
						vm.rejectListCopy = data.rejectList;
						break;
					// 采购
					case 4:
						vm.purchasingDetailListCopy = data.purchasingDetailList;
						vm.prdorderDetailListCopy = data.prdorderDetailList;
						break;
					// 财务
					case 5:
						vm.goodsCheckListCopy = data.goodsCheckList;
						vm.materialCheckListCopy = data.materialCheckList;
						vm.collectMuchMoneyListCopy = data.collectMuchMoneyList;
						vm.payMoneyListCopy = data.payMoneyList;
						break;
				}
          		
          		// 赋值显示值列表
                setShowValueList();
          	}
          	else
          	{
               	vm.message = data.message;
                $('#static').modal();
          	}
          	vm.showDetail();
          	MainCtrl.unblockUI();
          });
    }
    
    // 赋值显示值列表
	vm.exportMap = [];
    function setShowValueList()
    {
    	vm.showValueList = [];
    	var list = [];
    	switch (vm.reportDetail.status) 
    	{
    		// 销售
			case 1:
			 	// 逾期的单独处理
		    	if(vm.reportDetail.typeStr == "overDueSum" || vm.reportDetail.typeStr == "overDueAmount")
		    	{
					vm.contractDetailList = angular.copy(vm.overDueCdListCopy);
					list = angular.copy(vm.overDueReportList);
		    	}
		    	else
		    	{
		    		vm.contractDetailList = angular.copy(vm.contractDetailListCopy);
		    		list = angular.copy(vm.reportList);
		    	}
				vm.exportMap.exportData = JSON.stringify(vm.contractDetailList);
				break;
    		// 交货
			case 2:
				vm.deliveryDetailList = angular.copy(vm.deliveryDetailListCopy);
				list = angular.copy(vm.reportList);
				break;
    		// 客诉
			case 3:
				vm.rejectList = angular.copy(vm.rejectListCopy);
				list = angular.copy(vm.reportList);
				break;
			// 采购
			case 4:
				vm.purchasingDetailList = [];
				vm.prdorderDetailList = [];
				if(vm.propertyValue == "purchasingAmount")
				{
					vm.purchasingDetailList = angular.copy(vm.purchasingDetailListCopy);
					angular.forEach(vm.reportList,function(report){
						if(report.reportType == 4)
						{
							list.push(report);
						}
					});
				}
				else if(vm.propertyValue == "prdorderAmount" || vm.propertyValue == "prdorderArea")
				{
					vm.prdorderDetailList = angular.copy(vm.prdorderDetailListCopy);
					angular.forEach(vm.reportList,function(report){
						if(report.reportType == 5)
						{
							list.push(report);
						}
					});
				}
				break;	
		}
       	if(list && list.length > 0)
  		{
  			angular.forEach(list,function(report){
  				var showValue = eval("report."+vm.reportDetail.typeStr);
  				if(vm.reportDetail.personId && vm.reportDetail.personId != '' && report.personId == vm.reportDetail.personId)
  				{
  					vm.reportDetail.selectValue = showValue;
  				}
  				var obj = {name:report.name};
  				obj.showValue = showValue;
  				vm.showValueList.push(obj);
  			});
  			// 从大到小排序
  			if(vm.orderBy == "2")
  			{
  				vm.showValueList = vm.showValueList.sort(function(a,b){return b.showValue-a.showValue}); 
  			}
  			// 从小到大排序
  			else
  			{
  				vm.showValueList = vm.showValueList.sort(function(a,b){return a.showValue-b.showValue}); 
  			}
  			angular.forEach(vm.showValueList,function(obj){
  				if(obj.showValue > 10000)
  				{
  					obj.showValue = (obj.showValue/10000).toFixed(4)+"万"+vm.showUnit; 
  				}
  				else
  				{
  					obj.showValue = obj.showValue+vm.showUnit; 
  				}
  			});
  		}
    }
    
    // 赋值人员类型组
    function setRoleList(report)
    {
    	switch (report.status)
    	{
			// 销售
			case 1:
				vm.roleList = [{id:1,name:'客户'},{id:2,name:'业务员'},{id:3,name:'创建者'}];
				if(!vm.reportDetail.role || vm.reportDetail.role == 4)
				{
					vm.reportDetail.role = 1;
				}
				break;
			// 交货
			case 2:
				vm.roleList = [{id:1,name:'客户'},{id:2,name:'业务员'},{id:3,name:'创建者'}];
				if(!vm.reportDetail.role || vm.reportDetail.role == 4)
				{
					vm.reportDetail.role = 1;
				}
				break;
			// 客诉
			case 3:
				vm.roleList = [{id:1,name:'客户'},{id:2,name:'业务员'},{id:3,name:'创建者'}];
				if(!vm.reportDetail.role || vm.reportDetail.role == 4)
				{
					vm.reportDetail.role = 1;
				}
				break;
			// 采购
			case 4:
				vm.roleList = [{id:3,name:'创建者'},{id:4,name:'供应商'}];
				if(!vm.reportDetail.role || vm.reportDetail.role == 1 || vm.reportDetail.role == 2)
				{
					vm.reportDetail.role = 4;
				}
				break;
			// 财务
			case 5:
				vm.roleList = [{id:1,name:'客户'},{id:2,name:'业务员'},{id:4,name:'供应商'}];
				if(!vm.reportDetail.role || vm.reportDetail.role == 3)
				{
					vm.reportDetail.role = 1;
				}
				break;
    	}
    }
    
    vm.hideDetail = function()
    {
    	vm.tabs.opendetail.show = false;
    	vm.tabs.opendetail.active = false;
    	showForm();
    }
    vm.showDetail = function()
    {
        vm.tabs.opendetail.active = true;
        vm.tabs.opendetail.show = true;
    }
      
    // 获取报表查询的金额/面积/款数/平均单价/
    vm.showUnit = null;
    function getTypeStr(value)
    {
	   var str = "";
	   switch (value) 
	   {
	   		// 接单金额
			case "orderAmount":
				str = "amount";
			    vm.showUnit = "元";
		     	break;
	   		// 接单面积 
			case "orderArea":
				str = "area";
			    vm.showUnit = "㎡";
		     	break;	
	   		// 接单款数 
			case "orderSums":
				str = "sums";
			    vm.showUnit = "款";
		     	break;	
	   		// 平均单价 
			case "orderAvgPrice":
				str = "avgPrice";
			    vm.showUnit = "元";
		     	break;
	   		// 未交货逾期款数
			case "overDueSum":
				str = "overDueSum";
			    vm.showUnit = "款";
		     	break;	
	   		// 未交货逾期款数
			case "overDueAmount":
				str = "overDueAmount";
			    vm.showUnit = "元";
		     	break;	
	   		// 销售面积
			case "salesArea":
				str = "area";
			    vm.showUnit = "㎡";
		     	break;	
	   		// 销售金额
			case "salesAmount":
				str = "amount";
			    vm.showUnit = "元";
		     	break;	
	   		// 已交货款数
			case "deliveredSums":
				str = "sums";
			    vm.showUnit = "款";
		     	break;
	   		// 客诉金额
			case "rejectAmount":
				str = "amount";
			    vm.showUnit = "元";
		     	break;
	   		// 客诉面积
			case "rejectArea":
				str = "area";
			    vm.showUnit = "㎡";
		     	break;
	   		// 客诉款数
			case "rejectSums":
				str = "sums";
			    vm.showUnit = "款";
		     	break;
	   		// 原料采购金额
			case "purchasingAmount":
				str = "amount";
			    vm.showUnit = "元";
		     	break;
	   		// 成品采购金额
			case "prdorderAmount":
				str = "amount";
			    vm.showUnit = "元";
		     	break;
	   		// 成品采购面积 
			case "prdorderArea":
				str = "area";
			    vm.showUnit = "㎡";
		     	break;
	   		// 收款款数
			case "receivableSums":
				str = "sums";
			    vm.showUnit = "款";
		     	break;
	   		// 收款金额
			case "receivableAmount":
				str = "amount";
			    vm.showUnit = "元";
		     	break;
	   		// 付款款数 
			case "payableSums":
				str = "sums";
			    vm.showUnit = "款";
		     	break;
	   		// 付款金额 
			case "payableAmount":
				str = "amount";
			    vm.showUnit = "元";
		     	break;
	   		// 已收金额 
			case "receivedAmount":
				str = "amount";
			    vm.showUnit = "元";
		     	break;
	   		// 已收金额 
			case "paidAmount":
				str = "amount";
			    vm.showUnit = "元";
		     	break;
		}
	    return str;
    }
    
    // 选择人员
    vm.selectPerson = function()
    {
    	vm.reportDetail.selectValue = null; 
    	if(vm.reportDetail.personId && vm.reportDetail.personId != '')
    	{
    		var list = [];
    		if(vm.propertyValue == "overDueSum" || vm.propertyValue == "overDueAmount")
    		{
    			list = vm.overDueReportList;
    		}
    		else
    		{
    			list = vm.reportList;
    		}
    		for(let obj of list)
    		{
    			if(obj.personId == vm.reportDetail.personId)
    			{
    				vm.reportDetail.selectValue = eval("obj."+vm.reportDetail.typeStr);
    				break;
    			}
    		}
    	}
  		switch (vm.reportDetail.status) 
  		{
			// 销售
			case 1:
				var contractDetailList = [];
	    		if(vm.propertyValue == "overDueSum" || vm.propertyValue == "overDueAmount")
	    		{
	    			contractDetailList = angular.copy(vm.overDueCdListCopy);
	    		}
	    		else
	    		{
	    			contractDetailList = angular.copy(vm.contractDetailListCopy);
	    		}
				if(!vm.reportDetail.personId || vm.reportDetail.personId == '')
				{
					vm.contractDetailList = angular.copy(contractDetailList);
				}
				else
				{
					vm.contractDetailList=[];
					angular.forEach(contractDetailList,function(contractDetail){
						if(vm.reportDetail.personId == contractDetail.personId)
						{
							vm.contractDetailList.push(contractDetail);
						}
					});
				}
				break;
			// 交货
			case 2:
				if(!vm.reportDetail.personId || vm.reportDetail.personId == '')
				{
					vm.deliveryDetailList = angular.copy(vm.deliveryDetailListCopy);
				}
				else
				{
					vm.deliveryDetailList=[];
					angular.forEach(vm.deliveryDetailListCopy,function(deliveryDetail){
						if(vm.reportDetail.personId == deliveryDetail.personId)
						{
							vm.deliveryDetailList.push(deliveryDetail);
						}
					});
				}
				break;
			// 客诉
			case 3:
				if(!vm.reportDetail.personId || vm.reportDetail.personId == '')
				{
					vm.rejectList = angular.copy(vm.rejectListCopy);
				}
				else
				{
					vm.rejectList=[];
					angular.forEach(vm.rejectListCopy,function(reject){
						if(vm.reportDetail.personId == reject.personId)
						{
							vm.rejectList.push(reject);
						}
					});
				}
				break;
			// 采购
			case 4:
				vm.purchasingDetailList = [];
				vm.prdorderDetailList = [];
				if(!vm.reportDetail.personId || vm.reportDetail.personId == '')
				{
					if(vm.propertyValue == "purchasingAmount")
					{
						vm.purchasingDetailList = angular.copy(vm.purchasingDetailListCopy);
					}
					else if(vm.propertyValue == "prdorderAmount" || vm.propertyValue == "prdorderArea")
					{
						vm.prdorderDetailList = angular.copy(vm.prdorderDetailListCopy);
					}
				}
				else
				{
					if(vm.propertyValue == "purchasingAmount")
					{
						angular.forEach(vm.purchasingDetailListCopy,function(purchasingDetail){
							if(vm.reportDetail.personId == purchasingDetail.personId)
							{
								vm.purchasingDetailList.push(purchasingDetail);
							}
						});
					}
					else if(vm.propertyValue == "prdorderAmount" || vm.propertyValue == "prdorderArea")
					{
						angular.forEach(vm.prdorderDetailListCopy,function(prdorderDetail){
							if(vm.reportDetail.personId == prdorderDetail.personId)
							{
								vm.prdorderDetailList.push(prdorderDetail);
							}
						});
					}
				}
				break;
			// 财务
			case 5:
				
				break;
  		}
    }
    
    // 选择属性
    vm.selectProperty = function()
    {
    	// 获取报表查询的金额/面积/款数/平均单价/
    	vm.reportDetail.typeStr = getTypeStr(vm.propertyValue);
    	
    	// 赋值显示值列表
    	setShowValueList();
    }
    
    // 选择排序
    vm.selectOrderBy = function()
    {
    	vm.showValueList = vm.showValueList.reverse();
    }
    
    // 处理详情逾期报表数据
    function handleOverDueReportData(list)
    {
    	var overDueList = [];
    	if(list && list.length > 0)
    	{
    		list.forEach(obj1=>{
    		    const index = overDueList.findIndex(obj2=>{return obj1.personId === obj2.personId})
    		    if(index !== -1)
    		    {
    		    	overDueList[index].overDueSum = overDueList[index].overDueSum + obj1.overDueSum;
    		    	overDueList[index].overDueAmount = overDueList[index].overDueAmount + obj1.overDueAmount;
    		    }
    		    else
    		    {
    		    	overDueList.push(obj1)
    		    } 
    		})
    	}
    	return overDueList;
    }
    
    // 区间报表 start
    
    vm.query = {};
    
	//查询时间条件
	vm.query.sentTimeStartQr = {};
	vm.query.sentTimeEndQr = {};
    
    // 加载区间报表数据
    vm.loadSectionData = function()
    {
    	vm.sectionReportList = [];
    	vm.tabs.section.show = true;
        MainCtrl.blockUI({
            animate: true,
        });
		if(vm.time.start) 
		{
			vm.query.sentTimeStartQr = formatDate(new Date(vm.time.start));
		}
		if(vm.time.end) 
		{
			vm.query.sentTimeEndQr = formatDate(new Date(vm.time.end));
		}
        upida.post("report/reportData/getSectionReportData",vm.query).then(function (data) {
        	if(data.result == "success")
        	{
        		var reportList = data.reportList;// 报表数据
        		var overDueReportList = data.overDueReportList;// 未交货逾期
        		var list = handleOverDueReport(reportList,overDueReportList,2);
        		vm.sectionReportList = handleReportData(list);
        	}
        	else
        	{
             	vm.message = data.message;
                $('#static').modal();
        	}
        	MainCtrl.unblockUI();
        });
    }
    
    // zjn 2019-12-27 查看区间报表详情
    vm.showSectionReportDetailData = function(report,reportValue1)
    {
    	if(report.status == 5)
    	{
	       	 vm.message = "暂未开放！";
	         $('#static').modal();
	         return;
    	}
    	
    	vm.roleList = [];
		if(vm.time.start) 
		{
			vm.reportDetail.sentTimeStartQr = formatDate(new Date(vm.time.start));
		}
		if(vm.time.end) 
		{
			vm.reportDetail.sentTimeEndQr = formatDate(new Date(vm.time.end));
		}
    	vm.orderBy = "2";// 从大到小
    	
    	// 赋值人员类型组
    	setRoleList(report);
    	
    	// 查看当时的某个属性的详情
    	if(reportValue1)
    	{
    		var value1 = angular.copy(reportValue1);
    		vm.reportDetail.sequence = 1;
    		vm.reportDetail.reportValueList = [];
    		vm.reportDetail.typeStr = getTypeStr(value1.name);
    		value1.value2 = null;
    		vm.reportDetail.reportValueList.push(value1);
    		vm.propertyValue = value1.name;
    	}    	
    	// 查看详情
    	else
    	{
    		vm.reportDetail.typeStr = getTypeStr(report.reportValueList[0].name);
    		vm.reportDetail.sequence = 1;
    		vm.reportDetail.reportValueList = report.reportValueList;
    		vm.propertyValue = report.reportValueList[0].name;
    	}
    	vm.reportDetail.status = report.status;
    	vm.reportDetail.dateType = null;
    	vm.loadReportDetailData();
    }
    
	// 时间范围的选项
	vm.rangeOptions = {
		// format: "YYYY-MM-DD",
		startDate: new Date((new Date).setMonth(((new Date).getMonth() - 1))),
		minDate: new Date((new Date).setFullYear(((new Date).getFullYear() - 5)))
	};

	// 时间范围的Model
	vm.time = {
		start: {},
		end: {}
	};

	vm.initDate = function(date) {
		if(date == "") {
			vm.rangeOptions = {
				// format: "YYYY-MM-DD",
				startDate: new Date(vm.query.sentTimeStartQr.value),
				minDate: new Date(new Date(vm.query.sentTimeEndQr.value).setFullYear(new Date(vm.query.sentTimeEndQr.value).getFullYear() - 5))
			};
			vm.time = {
				start: vm.rangeOptions.startDate,
				end: vm.rangeOptions.minDate
			}
		}
	};

	function loadParmData() {
		MainCtrl.blockUI({
			animate: true,
		});
		upida.get("report/reportData/loadParmData").then(function(result) {
			vm.deptList = result.deptList;
			vm.deptList.splice(0,0,{name:'所有',recordId:null});
			MainCtrl.unblockUI();
		});
	}
    
    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        getYearMonthWeek();
        vm.selectReport(3);
		loadParmData();
    });
}]);
