/* Setup general page controller */
kybApp.controller('singlePayableCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil','BaseUtil','$filter',function ($rootScope, $scope, upida, $timeout, CommonUtil,BaseUtil,$filter) {
    $scope.$on('$viewContentLoaded', function () {
        // initialize core components
        MainCtrl.initAjax();
        // set default layout mode
        $rootScope.settings.layout.pageBodySolid = false;
        $rootScope.settings.layout.pageSidebarClosed = false;
    });
    
    var vm = this;
    
    vm.clicks = true;
    
    vm.page = {};
    vm.page.pageSizeOptions = [5, 10, 30, 50];
   
    // tabs控制
    vm.tabs = {
        viewForm: {active: true},
        editForm: {active: false, show: false},
        editDetailForm:{active: false, show: false},
        outForm: {active: false}
    };
    
    // 设置Form的域
    vm.setFormScope = function (scope) {
        vm.formScope = scope;
    };
    //vm.formScope.adjustDetailBatchEditForm.$setDirty();
    
    // 权限
    vm.right = {};
    
    vm.doSinglePayablePage = function(page, pageSize, total) 
	{
		vm.page.singlePayablePageNo = page;
		vm.page.singlePayablePageSize = pageSize;
		vm.setSinglePayableList();
	};
	
	vm.pageSinglePayableSizeChange = function() {
		vm.setSinglePayableList();
	};
	
	vm.querySinglePayableList = function() {
		vm.page.singlePayableData = {};
		vm.page.singlePayablePageSize = 10;
		vm.page.singlePayablePageNo = 1;
		vm.setSinglePayableList();
	};

	function setQuery(num)
	{
		var singlePayable = {supplier:{}};
		if(!num || num == 1)
		{
			if(vm.noQuery != "")
			{	
				singlePayable.no = vm.noQuery;
			}
			if(vm.supNoQuery != "")
			{
				singlePayable.supplier.no = vm.supNoQuery;
			}
			if(vm.supNameQuery != "")
			{
				singlePayable.supplier.name = vm.supNameQuery;
			}
			if(vm.statusQuery != "")
			{
				singlePayable.status = vm.statusQuery;
			}
			if(vm.periodQuery != "")
			{
				singlePayable.dateType = vm.dateType;
				singlePayable.period = vm.periodQuery;
			}
		} 
		else if(num == 2)
		{
			if(vm.noTwoQuery != "")
			{	
				singlePayable.no = vm.noTwoQuery;
			}
			if(vm.supNoTwoQuery != "")
			{
				singlePayable.supplier.no = vm.supTwoNoQuery;
			}
			if(vm.supNameTwoQuery != "")
			{
				singlePayable.supplier.name = vm.supNameTwoQuery;
			}
			if(vm.statusTwoQuery != "")
			{
				singlePayable.status = vm.statusTwoQuery;
			}
			if(vm.periodTwoQuery != "")
			{
				singlePayable.dateType = vm.dateType;
				singlePayable.period = vm.periodTwoQuery;
			}
			singlePayable.saleComId = vm.saleComId;
			singlePayable.lnCompanyId = vm.lnCompanyId;
		}
		return singlePayable;
	}
	
	vm.singlePayableList = [];
	vm.page.singlePayableData = {};
	vm.page.singlePayablePageSize = 10;
	vm.page.singlePayablePageNo = 1;
	vm.noQuery = "";
	vm.supNoQuery = "";
	vm.supNameQuery = "";
	vm.statusQuery = "";
	vm.periodQuery = "";
	
	vm.setSinglePayableList = function(num)
	{
		vm.singlePayableList = [];
		var singlePayable = setQuery(num);
		singlePayable.pageNo = vm.page.singlePayablePageNo;
		singlePayable.pageSize = vm.page.singlePayablePageSize;
	   	MainCtrl.blockUI({
	        animate: true,
	    });
		// 查询出合同详情
		upida.post("finance/singlePayable/getSinglePayablePage", singlePayable).then(function(data) 
		{
			// 计算开始数
			data.startCount = (data.pageNo - 1) * data.pageSize + 1;
			// 计算结束数
			data.endCount = (data.pageNo - 1) * data.pageSize;
			if(data && data.list && data.list.length > 0) 
			{
				vm.singlePayableList = data.list;
				// 计算结束数
				data.endCount = data.endCount + data.list.length;
			}
			vm.page.singlePayableData = data;
			MainCtrl.unblockUI();
		});
	}
	
    // zjn 2019-01-17 输入对账月份，重新加载月份列表
    vm.dateType = 3;
    vm.loadPeriodList = function(num)
    {
    	var periodValue = null;
    	if(num == 1)
    	{
    		periodValue = vm.periodQuery;
    	}
    	else if(num == 2)
    	{
    		periodValue = vm.periodTwoQuery;
    	}
    	if(!periodValue || periodValue == '')
    	{
    		vm.dateType = 3;
    		return;
    	}
		var period = periodValue.trim(); 
		if(isNaN(period))
		{
    		vm.message = "请输入数字!";
       	 	$('#static').modal();
       	 	vm.periodQuery = null;
       	 	return; 
		}
		if(!(period.length == 1 || period.length == 2
		  || period.length == 4 || period.length == 6))
		{
    		vm.message = "请输入正确的格式，年：4位，月：1至2位，年月：6位!";
       	 	$('#static').modal();
       	 	vm.periodQuery = null;
       	 	return; 
		}
		if(period.length == 1 || period.length == 2)
		{
			vm.dateType = 2
		}
		else if(period.length == 4)
		{
			vm.dateType = 1
		}
		else if(period.length == 6)
		{
			vm.dateType = 3
		}
    }
    
    // 应付单详情
    vm.showEditForm = function () {
        vm.tabs.editForm.show = true;
        vm.tabs.editForm.active = true;
    };
    
    // 关闭应付单详情
    vm.closeEditForm = function () {
        // 关闭对账明细
        vm.tabs.editForm.show = false;
        vm.tabs.editForm.active = false;
        showForm();
    };
    
    // 显示form
    function showForm()
    {
    	// 应收单详情
    	if(vm.tabs.editForm.show)
    	{
            $timeout(function () {
            	vm.tabs.editForm.active = true;
            });
    	}
        //应付单
        else 
        {
        	if(vm.lnCompanyId)
        	{
                $timeout(function () {
                	vm.tabs.outForm.active = true;
                });
        	}
        	else
        	{
                $timeout(function () {
                	vm.tabs.viewForm.active = true;
                });
        	}
        }
    }
    
    vm.singlePayable = {};
    vm.singlePayableDetails = [];
    
    // 显示对账明细
    vm.seeSinglePayable = function (index) {
    	vm.singlePayable = angular.copy(vm.page.singlePayableData.list[index]);
    	vm.singlePayable.lnCompanyId = vm.lnCompanyId;
    	if(vm.singlePayable.lnCompanyId)
    	{
    		vm.singlePayable.saleComId = vm.saleComId;
    	}
	   	MainCtrl.blockUI({
	        animate: true,
	    });
    	upida.post("finance/singlePayable/getDetailList",vm.singlePayable).then(function(data){
    		vm.singlePayableDetails = data;
			setDefaultValue(vm.singlePayableDetails);
    		vm.showEditForm();
    		MainCtrl.unblockUI();
    	});
    };

    function setDefaultValue(list)
	{
		angular.forEach(list,function(p){
			if(!p.matNo)
			{
				p.matNo = '无';
			}
			if(!p.billNo)
			{
				p.billNo = '无';
			}
			if(!p.specification)
			{
				p.specification = '无';
			}
			if(!p.typeStr)
			{
				p.typeStr = '无';
			}
			if(!p.price)
			{
				p.price = 0;
			}
			if(!p.quantity)
			{
				p.quantity = 0;
			}
			if(!p.amount)
			{
				p.amount = 0;
			}
			if(!p.statusStr)
			{
				p.statusStr = '无';
			}
		});
	}
    
    vm.singlePayableDetail = {};
    vm.seeDetail = function(row)
    {
    	vm.singlePayableDetail = angular.copy(row);
    	vm.showDetailDForm();
    }
    
    // 对账单明细详情
    vm.showDetailDForm = function () {
        vm.tabs.editDetailForm.show = true;
        vm.tabs.editDetailForm.active = true;
    };

    // 关闭对账单明细详情
    vm.closeDetailDForm = function () {
        // 关闭对账单明细详情
        vm.tabs.editDetailForm.show = false;
        vm.tabs.editDetailForm.active = false;
        showForm();
    };
    
    // 加载权限
    function loadRight() {
    	MainCtrl.blockUI({
    	    //进度条展示
    	    animate: true,
    	});	
    	//查询范围
    	vm.queryAll = CommonUtil.dataRangeIsAll("10806", BaseUtil.getMenuList());
    	vm.companyId = BaseUtil.getUser().company.recordId;
        upida.get("common/rightall?prefix=finance:singlePayable").then(function(data){
            vm.right.manage = data.manage;
            if(data.edit){
            	vm.right.edit = data.edit;
                loadItemsData();
            }
            vm.setSinglePayableList();
        });
    }
    
    vm.loadOutSourceData = function(num)
    {
    	var condition = [];
       	var newDate = new Date();
    	var year = newDate.getFullYear(); //获取完整的年份(4位,1970-????)
        vm.periodTwoQuery = year.toString();
        vm.dateType = 1
        condition.push({
            name: "dateType",
            value: vm.dateType
        });
        
    	if(num == 1)
    	{
    		vm.lnCompanyId = null;
    		vm.saleComId = null;
    		vm.periodQuery = vm.periodTwoQuery;
    	}
    	else if(num == 2)
    	{
    		vm.lnCompanyId = vm.ValiteCompanyUtil.ecoemyId;
    		vm.saleComId = BaseUtil.getUser().company.recordId;
    	}
    	vm.setSinglePayableList(num);
    }
    
    vm.supplierList = [];
    vm.periodList = [];
    vm.periodListCopy = [];
    
    //加载下拉框数据
    function loadItemsData() {
    	MainCtrl.blockUI({
    	    //进度条展示
    	    animate: true,
    	});	
        upida.get("finance/singlePayable/load/data?queryAll="+vm.queryAll+"").then(function (result) {
            if(result.data)
            {
            	vm.supplierList = result.data.supplierList;
                vm.periodList = result.data.periodList;
                vm.periodListCopy = result.data.periodList;
            }
        });
    }
    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
        vm.ValiteCompanyUtil = BaseUtil.getDefaultData();
    });
}]);
