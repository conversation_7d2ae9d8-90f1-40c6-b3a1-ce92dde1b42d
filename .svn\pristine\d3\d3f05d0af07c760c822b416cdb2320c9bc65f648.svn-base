package com.kyb.pcberp.modules.report.entity;

import com.kyb.pcberp.common.persistence.DataEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ReportDeliveryCapacity
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/2/22 11:40
 * @Version 1.0
 **/
public class ReportCapacityAssessment extends DataEntity<ReportCapacityAssessment> {

    private String recordId;

    private String departId;

    private String customerId;

    private String versionId;

    private String organizationName;

    private String yearMonth;

    private String processValueId;

    private Integer capacityBuy;

    private Double capacityUsed;

    private Double discountPrice;

    private Double discountAmount;

    private Double punishAmount;

    private Double reconciliationAmount;

    private List<ReportCapacityAssessmentDetail> usedDetailList;

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getDepartId() {
        return departId;
    }

    public void setDepartId(String departId) {
        this.departId = departId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getVersionId() {
        return versionId;
    }

    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public String getProcessValueId() {
        return processValueId;
    }

    public void setProcessValueId(String processValueId) {
        this.processValueId = processValueId;
    }

    public Integer getCapacityBuy() {
        return capacityBuy;
    }

    public void setCapacityBuy(Integer capacityBuy) {
        this.capacityBuy = capacityBuy;
    }

    public Double getCapacityUsed() {
        return capacityUsed;
    }

    public void setCapacityUsed(Double capacityUsed) {
        this.capacityUsed = capacityUsed;
    }

    public Double getDiscountPrice() {
        return discountPrice;
    }

    public void setDiscountPrice(Double discountPrice) {
        this.discountPrice = discountPrice;
    }

    public Double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public Double getPunishAmount() {
        return punishAmount;
    }

    public void setPunishAmount(Double punishAmount) {
        this.punishAmount = punishAmount;
    }

    public Double getReconciliationAmount() {
        return reconciliationAmount;
    }

    public void setReconciliationAmount(Double reconciliationAmount) {
        this.reconciliationAmount = reconciliationAmount;
    }

    public List<ReportCapacityAssessmentDetail> getUsedDetailList() {
        if(usedDetailList==null)
        {
            return new ArrayList<>();
        }
        else
        {
            return usedDetailList;
        }
    }

    public void setUsedDetailList(List<ReportCapacityAssessmentDetail> usedDetailList) {
        this.usedDetailList = usedDetailList;
    }


}
