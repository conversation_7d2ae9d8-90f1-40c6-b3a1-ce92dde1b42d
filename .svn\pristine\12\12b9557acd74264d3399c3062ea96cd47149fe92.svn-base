package com.kyb.pcberp.modules.stock.service;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.pdf.ItextPdfHeaderFooter;
import com.kyb.pcberp.common.pdf.ext.ParagraphCn12;
import com.kyb.pcberp.common.pdf.ext.ParagraphCn16;
import com.kyb.pcberp.common.pdf.ext.ParagraphCn9;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.FmtUtils;
import com.kyb.pcberp.common.utils.ProductStockUtil;
import com.kyb.pcberp.common.utils.RawStockUtil;
import com.kyb.pcberp.modules.contract.entity.Notification;
import com.kyb.pcberp.modules.eg.entity.BoardPartCraft;
import com.kyb.pcberp.modules.production.dao.ProduceRecordDao;
import com.kyb.pcberp.modules.production.entity.ProduceBatchDetailCraft;
import com.kyb.pcberp.modules.production.entity.ProduceRecord;
import com.kyb.pcberp.modules.production.entity.Wip;
import com.kyb.pcberp.modules.stock.dao.InventoryDao;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.dao.StoreHouseDao;
import com.kyb.pcberp.modules.stock.entity.*;
import com.kyb.pcberp.modules.stock.vo.ImportReturnVo;
import com.kyb.pcberp.modules.sys.dao.BranchDao;
import com.kyb.pcberp.modules.sys.dao.ParameterSetDao;
import com.kyb.pcberp.modules.sys.entity.*;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;

@Service
@Transactional(readOnly = true)
public class InventoryService extends CrudService<InventoryDao, Inventory>
{
    
    @Autowired
    private InventoryDao inventoryDao;
    
    @Autowired
    private MaterialDao materialDao;
    
    @Autowired
    private StoreHouseDao storeHouseDao;
    
    @Autowired
    private BranchDao branchDao;
    
    /*
     * @Autowired private ProduceRecordService produceRecordService;
     */
    
    @Autowired
    private ProduceRecordDao produceRecordDao;
    
    @Autowired
    private ParameterSetDao parameterSetDao;
    
    /**
     * 查询该公司的所有的库存盘点
     */
    public Page<Inventory> getAllInventory(Page<Inventory> page, Inventory inventory)
    {
        inventory.setPage(page);
        List<Inventory> inventoryList = inventoryDao.getAllInventory(inventory);
        page.setList(inventoryList);
        
        return page;
    }
    
    /**
     * 获取公司盘点的状态
     */
    public String isFlag(Inventory inventory, StoreHouse storeHouse)
    {
        String result = "";
        // 设置该公司正在盘点中
        // 查询参数设置中是否已经对该公司的库存盘点进行设置
        Integer num = inventoryDao.getInventoryFlag(storeHouse);
        if (num != null)
        {
            if (num > 0)
            {
                result = "on";
            }
            else
            {
                result = "off";
            }
        }
        else
        {
            result = "off";
        }
        return result;
    }
    
    /**
     * 完成盘点
     */
    @Transactional(readOnly = false)
    public String copyM(Inventory inventory)
    {
        String result = "";
        inventory.setCompany(UserUtils.getUser().getCompany());
        Inventory dbObj = inventoryDao.get(inventory);
        if (-1 == dbObj.getStoreId() && "1".equals(dbObj.getDealFlag()) && "2".equals(inventory.getDealFlag()))
        {
            // WIP盘点，数据录入完成
            // 1、没有盘差的，清空盘差详情和差异说明
            inventoryDao.updateNoDiffInfoQty(inventory);
            
            WipCopy copy = new WipCopy();
            copy.setCompany(UserUtils.getUser().getCompany());
            copy.setCopyRecId(Integer.parseInt(dbObj.getRecordId()));
            List<WipCopy> diffCopyList = inventoryDao.getWipSnapDiffList(copy);
            if (!CollectionUtils.isEmpty(diffCopyList))
            {
                
                List<WipCopyInfo> updateCopyInfoList = new ArrayList<WipCopyInfo>();
                for (WipCopy diffC : diffCopyList)
                {
                    if (CollectionUtils.isEmpty(diffC.getCopyInfoList()))
                    {
                        continue;
                    }
                    handelDiffInfoQtyByCompleAddSnap(diffC, "A");
                    handelDiffInfoQtyByCompleAddSnap(diffC, "B");
                    updateCopyInfoList.addAll(diffC.getCopyInfoList());
                }
                // 6批量更新有盘差的，详情qty数据
                if (!CollectionUtils.isEmpty(updateCopyInfoList))
                {
                    inventoryDao.batchUpdateDiffInfoQty(updateCopyInfoList);
                }
            }
            
        }
        // 设置该公司盘点已经完成
        inventoryDao.auditInventoryFlag(inventory);
        // 盈亏确认，更改盘点状态;李杰华 完成数据录入确认，解锁仓库
        if ("3".equals(inventory.getDealFlag())
            || ("2".equals(inventory.getDealFlag()) && "李杰华".equals(UserUtils.getUser().getUserName())))
        {
            inventoryDao.updateInventoryFlag(inventory);
        }
        
        return result;
    }
    
    /**
     * @param diffC
     * @param type "A"：setSnapQtyPnlA;"B"：setSnapQtyPnlB;
     */
    private void handelDiffInfoQtyByCompleAddSnap(WipCopy diffC, String type)
    {
        List<WipCopyInfo> wipCopyInfoList = diffC.getCopyInfoList();
        if (CollectionUtils.isEmpty(wipCopyInfoList))
        {
            return;
        }
        boolean isNullFlag = false;
        if ("A".equals(type))
        {
            isNullFlag = isNullQty(diffC.getSnapQtyPnlA());
        }
        else if ("B".equals(type))
        {
            isNullFlag = isNullQty(diffC.getSnapQtyPnlB());
        }
        if (isNullFlag)
        {
            // 2、有盘差的，但是实盘数为空或者0，清空对应的盘差详情实盘数据
            clearCopyInfoQty(wipCopyInfoList, type);
        }
        else
        {
            if (wipCopyInfoList.size() == 1)
            {
                // 3、有盘差，实盘不为空或者0，只有一条明细的，将实盘数据赋值给盘差明细
                copyQtyToInfo(wipCopyInfoList, diffC, type);
            }
            else
            {
                if (!checkIsExistSnapQty(wipCopyInfoList, type))
                {
                    // 5、有盘差，实盘不为空或者0，有多条明细的，且盘差明细全部为空或者为0，则按照投料单创建时间，依次分配实盘数
                    distributeQtyDefault(wipCopyInfoList, diffC, type);
                }
                // 4、有盘差，实盘不为空或者0，有多条明细的，且盘差明细已经存在不为空或者0的明细，不处理
            }
        }
    }
    
    private void distributeQtyDefault(List<WipCopyInfo> wipCopyInfoList, WipCopy diffC, String type)
    {
        if (!CollectionUtils.isEmpty(wipCopyInfoList))
        {
            int snapQtyA = diffC.getSnapQtyPnlA() == null ? 0 : diffC.getSnapQtyPnlA();
            int snapQtyB = diffC.getSnapQtyPnlB() == null ? 0 : diffC.getSnapQtyPnlB();
            // info集合已经默认按照投料单的创建时间查询，直接顺序分配
            WipCopyInfo info = null;
            int infoNum = wipCopyInfoList.size();
            for (int i = 0; i < infoNum; i++)
            {
                info = wipCopyInfoList.get(i);
                if ("A".equals(type))
                {
                    if (snapQtyA > 0)
                    {
                        if (i == (infoNum - 1))
                        {
                            // 最后一条记录，直接分配剩余所有的
                            info.setSnapQtyPnlA(snapQtyA);
                            snapQtyA = 0;
                        }
                        else
                        {
                            int infoQtyA = 0;
                            if (null != diffC.getUnitType() && 1 == diffC.getUnitType())
                            {
                                infoQtyA = info.getQtyPcsA() == null ? 0 : info.getQtyPcsA();// 账面数
                            }
                            else
                            {
                                infoQtyA = info.getQtyPnlA() == null ? 0 : info.getQtyPnlA();// 账面数
                            }
                            
                            if (infoQtyA < snapQtyA)
                            {
                                info.setSnapQtyPnlA(infoQtyA);
                                snapQtyA = snapQtyA - infoQtyA;
                            }
                            else
                            {
                                info.setSnapQtyPnlA(snapQtyA);
                                break;
                            }
                        }
                        
                    }
                    else
                    {
                        break;
                    }
                    
                }
                else if ("B".equals(type))
                {
                    if (snapQtyB > 0)
                    {
                        
                        if (i == (infoNum - 1))
                        {
                            // 最后一条记录，直接分配剩余所有的
                            info.setSnapQtyPnlB(snapQtyB);
                            snapQtyB = 0;
                        }
                        else
                        {
                            int infoQtyB = 0;
                            if (null != diffC.getUnitType() && 1 == diffC.getUnitType())
                            {
                                infoQtyB = info.getQtyPcsB() == null ? 0 : info.getQtyPcsB();// 账面数
                            }
                            else
                            {
                                infoQtyB = info.getQtyPnlB() == null ? 0 : info.getQtyPnlB();// 账面数
                            }
                            if (infoQtyB < snapQtyB)
                            {
                                info.setSnapQtyPnlB(infoQtyB);
                                snapQtyB = snapQtyB - infoQtyB;
                            }
                            else
                            {
                                info.setSnapQtyPnlB(snapQtyB);
                                break;
                            }
                        }
                    }
                    else
                    {
                        break;
                    }
                }
            }
        }
        
    }
    
    private boolean checkIsExistSnapQty(List<WipCopyInfo> wipCopyInfoList, String type)
    {
        if (!CollectionUtils.isEmpty(wipCopyInfoList))
        {
            for (WipCopyInfo info : wipCopyInfoList)
            {
                if ("A".equals(type))
                {
                    if (!isNullQty(info.getSnapQtyPnlA()))
                    {
                        return true;
                    }
                }
                else if ("B".equals(type))
                {
                    if (!isNullQty(info.getSnapQtyPnlB()))
                    {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
    private void copyQtyToInfo(List<WipCopyInfo> wipCopyInfoList, WipCopy copy, String type)
    {
        if (!CollectionUtils.isEmpty(wipCopyInfoList))
        {
            for (WipCopyInfo info : wipCopyInfoList)
            {
                if ("A".equals(type))
                {
                    info.setSnapQtyPnlA(copy.getSnapQtyPnlA());
                }
                else if ("B".equals(type))
                {
                    info.setSnapQtyPnlB(copy.getSnapQtyPnlB());
                }
                
            }
        }
    }
    
    private void clearCopyInfoQty(List<WipCopyInfo> wipCopyInfoList, String type)
    {
        if (!CollectionUtils.isEmpty(wipCopyInfoList))
        {
            for (WipCopyInfo info : wipCopyInfoList)
            {
                if ("A".equals(type))
                {
                    info.setSnapQtyPnlA(null);
                }
                else if ("B".equals(type))
                {
                    info.setSnapQtyPnlB(null);
                }
                
            }
        }
        
    }
    
    private boolean isNullQty(Integer snapQtyPnlA)
    {
        return null == snapQtyPnlA || 0 == snapQtyPnlA;
    }
    
    /**
     * 保存新的库存盘点
     *
     */
    @Transactional(readOnly = false)
    public String insertCopyMaterial(String companyId, String userId, String storeId, Date snapshotDate)
    {
        // 保存库存盘点
        StoreHouse storeHouse = new StoreHouse();
        storeHouse.setRecordId(storeId);
        Integer num = inventoryDao.getInventoryIsOpen(storeHouse);
        if (num > 0)
        {
            return "该仓库已经在盘点了，每个仓库同时间只能开启一次";
        }
        else if ("-1".equals(storeId))
        {
            // WIP虚拟仓库id:-1
            // 插入一条盘点主表记录
            Company company = UserUtils.getUser().getCompany();
            Inventory in = new Inventory();
            in.setCompany(company);
            in.setStoreId(Integer.parseInt(storeId));
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            in.setSnapshotDate(dateFormat.format(snapshotDate));
            in.setNo("PDWIP0" + new SimpleDateFormat("yyMMddHHmmss").format(new Date()));
            in.setType(2);
            in.preInsert();
            inventoryDao.insert(in);
            // 查询Wip数据
            // 获取接/交Wip参数设置
            Notification notification = new Notification();
            notification.setCompany(company);
            notification.setStatus(TypeKey.SL_NOTIFICATION_STATUS_FEED.toString());
            notification.setStatus1(TypeKey.SL_NOTIFICATION_STATUS_SUPPLEMENTARYFOOD.toString());
            ParameterSet ps = new ParameterSet();
            ps.setCompany(notification.getCompany());
            ps.setJianPin("takeOrHandWip");
            ParameterSet parameterSet = parameterSetDao.getParameterSetByCompanyAndjianPin(ps);
            if (null != parameterSet)
            {
                notification.setTakeHandWip(parameterSet.getStart());
            }
            else
            {
                notification.setTakeHandWip("0");
            }
            // 查询Wip数据
            List<Wip> list = produceRecordDao.selectWip(notification);
            // 插入WIP明细和WIP明细详情表
            if (!CollectionUtils.isEmpty(list))
            {
                List<WipCopy> insertCopyList = new ArrayList<WipCopy>();
                List<WipCopyInfo> insertCopyInfoList = new ArrayList<WipCopyInfo>();
                getCopyInfoFromWip(Integer.parseInt(in.getRecordId()), list, insertCopyInfoList, insertCopyList);
                // 保存
                for (WipCopy c : insertCopyList)
                {
                    inventoryDao.insertWipCopy(c);
                }
                inventoryDao.batchInsertWipCopyInfo(insertCopyInfoList);
                
            }
            else
            {
                return "没有查询到WIP可供盘点";
            }
            
            return null;
        }
        else
        {
            
            // 获取仓库信息，type:1,原材料;type2;成品
            String copyStoreId = new String(storeId);
            if ("-44".equals(copyStoreId))
            {
                storeId = "44";// 成品尾数仓
            }
            StoreHouse store = storeHouseDao.get(storeId);
            if (null == store)
            {
                return "仓库不存在数据库中";
            }
            else
            {
                inventoryDao.insertCopyMaterial(companyId, userId, storeId, snapshotDate);
                // 生产盘点单号
                genNo(companyId, storeId, snapshotDate);
                Inventory in = inventoryDao.getCurrentCopyRec(snapshotDate, companyId, storeId);
                if (store.getType() == 2)
                {
                    if (!"-44".equals(copyStoreId))
                    {
                        // 更新临时仓成品账面库存
                        inventoryDao.updateProductStoreStock(in);
                    }
                    else
                    {
                        // 更新尾数仓的仓库StoreId
                        inventoryDao.updateLastStoreId(in);
                        // 更新尾数仓成品账面库存
                        inventoryDao.updateProductLastStoreStock(in);
                    }
                    
                    return null;
                }
                else
                {
                    // 更新原料账面库存
                    inventoryDao.updateMaterialStoreStock(in);
                    return null;
                    // 原材料
                    // 获取跟进明细id和账期获取账面库存
                }
            }
            
        }
    }
    
    /**
     * @param list wip 分级批次-明细工艺表； CopyInf:feedId,notificationId,craftNo,cateName,batchDetailId,detailCraftId
     * @param insertCopyInfoList
     * @param insertCopyList
     */
    private void getCopyInfoFromWip(Integer recId, List<Wip> list, List<WipCopyInfo> insertCopyInfoList,
        List<WipCopy> insertCopyList)
    {
        // 获取不同生产编号合单的作废记录
        List<WipCopyInfo> craftDiscardList = getCraftDiscardInfo(list);
        // 获取
        WipCopy query = new WipCopy();
        query.setCompany(UserUtils.getUser().getCompany());
        List<WipCopyInfo> sets = inventoryDao.getWipSet(query);
        Map<String, Integer> unitSet = new HashMap<>();
        if (!CollectionUtils.isEmpty(sets))
        {
            for (WipCopyInfo set : sets)
            {
                if (!unitSet.containsKey(set.getCateName()))
                {
                    unitSet.put(set.getCateName(), set.getUnitType());
                }
                
            }
        }
        WipCopyInfo copyInfo = null;
        for (Wip w : list)
        {
            if (!CollectionUtils.isEmpty(w.getProduceRecordList()))
            {
                for (ProduceRecord pr : w.getProduceRecordList())
                {
                    if(null == pr || null == pr.getProcess())
                    {
                        continue;
                    }
                    // 批次
                    if (!CollectionUtils.isEmpty(w.getNotification().getProduceBatchDetailCraftList()))
                    {
                        List<WipCopyInfo> moreCraftNoList = new ArrayList<WipCopyInfo>();
                        // 明细工艺
                        for (ProduceBatchDetailCraft craft : w.getNotification().getProduceBatchDetailCraftList())
                        {
                            if (pr.getProduceBatchDetailId().equals(craft.getProduceBatchDetail().getRecordId()))
                            {
                                // key:通知单+生产编号
                                copyInfo = new WipCopyInfo();
                                copyInfo.setCateName(pr.getProcess().getName());
                                copyInfo.setCateId(pr.getProcess().getRecordId());
                                if ("仓库".equals(pr.getProcess().getName()))
                                {
                                    copyInfo.setCateName("开料");
                                }
                                copyInfo.setFeedId(w.getFeeding().getRecordId());
                                copyInfo.setFeedNo(w.getFeeding().getNo());
                                copyInfo.setOrderQty(Integer.parseInt(w.getFeeding().getOrderQuantity()));
                                copyInfo.setFeedQty(w.getFeeding().getPcsQuantity().intValue());
                                copyInfo.setProduceType(w.getNotification().getProduceType());
                                copyInfo.setNotificationId(Integer.parseInt(craft.getNotificationId()));
                                copyInfo
                                    .setBatchDetailId(Integer.parseInt(craft.getProduceBatchDetail().getRecordId()));
                                copyInfo.setDetailCraftId(Integer.parseInt(craft.getRecordId()));
                                copyInfo.setCraftNo(craft.getCraftNo());
                                // copyInfo.setQtyPnlA(craft.getQtyPcsA());
                                // copyInfo.setQtyPnlB(craft.getQtyPcsB());
                                copyInfo.setQtyPcsA(craft.getQtyPcsA());
                                copyInfo.setQtyPcsB(craft.getQtyPcsB());
                                // copyInfo.setDiscardPcsQtyA(craft.getDiscardQty());报废
                                handleDiscartInfo(copyInfo, craftDiscardList);
                                if (!CollectionUtils.isEmpty(w.getNotification().getBoardPartCraftList()))
                                {
                                    for (BoardPartCraft board : w.getNotification().getBoardPartCraftList())
                                    {
                                        if (craft.getNotificationId().equals(board.getNotificationId())
                                            && craft.getCraftNo().equals(board.getCraftNo()))
                                        {
                                            copyInfo.setSetWidth(new BigDecimal(board.getSetWidth()));
                                            copyInfo.setSetLength(new BigDecimal(board.getSetLength()));
                                            copyInfo.setQtySetT(Integer.parseInt(board.getPnlDivisor()));
                                            copyInfo.setaPnlHasPcsQty(board.getPcsCountA() == null ? 0
                                                : Integer.parseInt(board.getPcsCountA()));
                                            copyInfo.setbPnlHasPcsQty(board.getPcsCountB() == null ? 0
                                                : Integer.parseInt(board.getPcsCountB()));
                                            copyInfo.setQtySetA(board.getTotalSetCountA() == null ? 0
                                                : Integer.parseInt(board.getTotalSetCountA()));
                                            copyInfo.setQtySetB(board.getTotalSetCountA() == null ? 0
                                                : Integer.parseInt(board.getTotalSetCountA()));
                                            break;
                                        }
                                    }
                                }
                                else
                                {
                                    copyInfo.setQtySetT(w.getNotification().getCardA().getPnlDivisor());
                                    copyInfo.setSetLength(w.getNotification().getCardA().getSetLength());
                                    copyInfo.setSetWidth(w.getNotification().getCardA().getSetWidth());
                                    copyInfo.setaPnlHasPcsQty(pr.getPcsCountA());
                                    copyInfo.setbPnlHasPcsQty(pr.getPcsCountB());
                                    copyInfo.setQtySetA(pr.getPcsCountB());
                                    copyInfo.setQtySetB(pr.getPcsCountB());
                                }
                                handleCopyInfoArea(copyInfo);
                                copyInfo.setCompany(UserUtils.getUser().getCompany());
                                copyInfo.preInsert();
                                copyInfo.setStatus("1");
                                moreCraftNoList.add(copyInfo);
                                // 明细工艺分
                                // insertCopyInfoList.add(copyInfo);
                            }
                            
                        }
                        // 明细工艺合并
                        WipCopyInfo copyInfoArea = getMoreCraftNoCopyInfo(moreCraftNoList);
                        // 重新赋值
                        copyInfo = new WipCopyInfo();
                        copyInfo.setCateName(pr.getProcess().getName());
                        copyInfo.setCateId(pr.getProcess().getRecordId());
                        if ("仓库".equals(pr.getProcess().getName()))
                        {
                            copyInfo.setCateName("开料");
                        }
                        // 工序默认为PNL
                        Integer unitType =
                            unitSet.get(copyInfo.getCateName()) == null ? 2 : unitSet.get(copyInfo.getCateName());
                        copyInfo.setUnitType(unitType);
                        copyInfo.setFeedId(w.getFeeding().getRecordId());
                        copyInfo.setFeedNo(w.getFeeding().getNo());
                        copyInfo.setOrderQty(Integer.parseInt(w.getFeeding().getOrderQuantity()));
                        copyInfo.setFeedQty(w.getFeeding().getPcsQuantity().intValue());
                        copyInfo.setNotificationId(Integer.parseInt(w.getNotification().getRecordId()));
                        copyInfo.setCraftNo(w.getNotification().getCraftNo());
                        copyInfo.setProduceType(w.getNotification().getProduceType());
                        if (copyInfo.getCraftNo().contains(","))
                        {
                            String[] craftNos = copyInfo.getCraftNo().split(",");
                            String craftNo = craftNos[0];
                            for (int i = 0; i < craftNos.length; i++)
                            {
                                if (!craftNo.contains(craftNos[i]))
                                {
                                    craftNo = craftNo + "," + craftNos[i];
                                }
                            }
                            copyInfo.setCraftNo(craftNo);
                        }
                        copyInfo.setBatchDetailId(Integer.parseInt(pr.getProduceBatchDetailId()));
                        copyInfo.setQtySetA(pr.getTotalSetCountA());
                        copyInfo.setQtySetB(pr.getTotalSetCountB());
                        copyInfo.setQtyPcsA(pr.getTakeOverQtyPcsA());
                        copyInfo.setQtyPcsB(pr.getTakeOverQtyPcsB());
                        copyInfo.setQtySetT(w.getNotification().getCardA().getPnlDivisor());
                        copyInfo.setSetLength(w.getNotification().getCardA().getSetLength());
                        copyInfo.setSetWidth(w.getNotification().getCardA().getSetWidth());
                        copyInfo.setaPnlHasPcsQty(pr.getPcsCountA());
                        copyInfo.setbPnlHasPcsQty(pr.getPcsCountB());
                        // handleCopyInfoArea(copyInfo);
                        copyInfo.setPcsArea(copyInfoArea.getPcsArea());
                        copyInfo.setPnlArea(copyInfoArea.getPnlArea());
                        countPnl(copyInfo);
                        copyInfo.setCompany(UserUtils.getUser().getCompany());
                        copyInfo.preInsert();
                        copyInfo.setStatus("1");
                        insertCopyInfoList.add(copyInfo);
                    }
                    else
                    {
                        copyInfo = new WipCopyInfo();
                        copyInfo.setCateName(pr.getProcess().getName());
                        copyInfo.setCateId(pr.getProcess().getRecordId());
                        if ("仓库".equals(pr.getProcess().getName()))
                        {
                            copyInfo.setCateName("开料");
                        }
                        Integer unitType =
                            unitSet.get(copyInfo.getCateName()) == null ? 2 : unitSet.get(copyInfo.getCateName());
                        copyInfo.setUnitType(unitType);
                        copyInfo.setFeedId(w.getFeeding().getRecordId());
                        copyInfo.setFeedNo(w.getFeeding().getNo());
                        copyInfo.setOrderQty(Integer.parseInt(w.getFeeding().getOrderQuantity()));
                        copyInfo.setFeedQty(w.getFeeding().getPcsQuantity().intValue());
                        copyInfo.setNotificationId(Integer.parseInt(w.getNotification().getRecordId()));
                        copyInfo.setCraftNo(w.getNotification().getCraftNo());
                        copyInfo.setProduceType(w.getNotification().getProduceType());
                        if (copyInfo.getCraftNo().contains(","))
                        {
                            String[] craftNos = copyInfo.getCraftNo().split(",");
                            String craftNo = craftNos[0];
                            for (int i = 0; i < craftNos.length; i++)
                            {
                                if (!craftNo.contains(craftNos[i]))
                                {
                                    craftNo = craftNo + "," + craftNos[i];
                                }
                            }
                            copyInfo.setCraftNo(craftNo);
                        }
                        copyInfo.setBatchDetailId(Integer.parseInt(pr.getProduceBatchDetailId()));
                        copyInfo.setQtySetA(pr.getTotalSetCountA());
                        copyInfo.setQtySetB(pr.getTotalSetCountB());
                        copyInfo.setQtyPcsA(pr.getTakeOverQtyPcsA());
                        copyInfo.setQtyPcsB(pr.getTakeOverQtyPcsB());
                        copyInfo.setQtySetT(w.getNotification().getCardA().getPnlDivisor());
                        copyInfo.setSetLength(w.getNotification().getCardA().getSetLength());
                        copyInfo.setSetWidth(w.getNotification().getCardA().getSetWidth());
                        copyInfo.setaPnlHasPcsQty(pr.getPcsCountA());
                        copyInfo.setbPnlHasPcsQty(pr.getPcsCountB());
                        handleCopyInfoArea(copyInfo);
                        countPnl(copyInfo);
                        copyInfo.setCompany(UserUtils.getUser().getCompany());
                        copyInfo.preInsert();
                        copyInfo.setStatus("1");
                        insertCopyInfoList.add(copyInfo);
                    }
                    
                }
            }
        }
        
        // 同一工序，同一生产编号、同一批次的合并
        Map<String, WipCopy> copySet = new HashMap<String, WipCopy>();
        WipCopy copyTemp = null;
        for (WipCopyInfo info : insertCopyInfoList)
        {
            String key = info.getCateName() + ";" + info.getCraftNo() + ";" + info.getFeedId();
            if (copySet.containsKey(key))
            {
                wipCopyAddWipInfo(info, copySet.get(key));
                info.setWipCopy(copySet.get(key));
            }
            else
            {
                copyTemp = new WipCopy();
                copyTemp.setCompany(info.getCompany());
                copyTemp.setCopyRecId(recId);
                copyTemp.setCraftNo(info.getCraftNo());
                copyTemp.setCateName(info.getCateName());
                copyTemp.setCateId(info.getCateId());
                copyTemp.setOrderQty(info.getOrderQty());
                copyTemp.setFeedQty(info.getFeedQty());
                copyTemp.setQtySetA(info.getQtySetA());
                copyTemp.setQtySetB(info.getQtySetB());
                copyTemp.setQtySetT(info.getQtySetT());
                copyTemp.setSetWidth(info.getSetWidth());
                copyTemp.setSetLength(info.getSetLength());
                copyTemp.setQtySetT(info.getQtySetT());
                copyTemp.setQtyPnlA(info.getQtyPnlA());
                copyTemp.setQtyPnlB(info.getQtyPnlB());
                copyTemp.setQtyPcsA(info.getQtyPcsA());
                copyTemp.setQtyPcsB(info.getQtyPcsB());
                copyTemp.setFeedArea(info.getFeedArea());
                copyTemp.setPcsArea(info.getPcsArea());
                copyTemp.setFeedIds(info.getFeedId());
                copyTemp.setFeedId(info.getFeedId());
                copyTemp.setUnitType(info.getUnitType());
                copyTemp.setPnlArea(info.getPnlArea());
                copyTemp.setaPnlHasPcsQty(info.getaPnlHasPcsQty());
                copyTemp.setbPnlHasPcsQty(info.getbPnlHasPcsQty());
                copyTemp.setStatus("1");
                copyTemp.preInsert();
                info.setWipCopy(copyTemp);
                insertCopyList.add(copyTemp);
                copySet.put(key, copyTemp);
            }
        }
        
    }
    
    private void countPnl(WipCopyInfo copyInfo)
    {
        if (null == copyInfo.getaPnlHasPcsQty() || 0 == copyInfo.getaPnlHasPcsQty())
        {
            copyInfo.setQtyPnlA(null);
        }
        else if (null == copyInfo.getQtyPcsA() || 0 == copyInfo.getQtyPcsA())
        {
            copyInfo.setQtyPnlA(null);
        }
        else
        {
            // 去掉小数位
            copyInfo.setQtyPnlA(copyInfo.getQtyPcsA() / copyInfo.getaPnlHasPcsQty());
        }
        
        if (null == copyInfo.getbPnlHasPcsQty() || 0 == copyInfo.getbPnlHasPcsQty())
        {
            copyInfo.setQtyPnlB(null);
        }
        else if (null == copyInfo.getQtyPcsB() || 0 == copyInfo.getQtyPcsB())
        {
            copyInfo.setQtyPnlB(null);
        }
        else
        {
            // 去掉小数位
            copyInfo.setQtyPnlB(copyInfo.getQtyPcsB() / copyInfo.getbPnlHasPcsQty());
        }
        
    }
    
    private WipCopyInfo getMoreCraftNoCopyInfo(List<WipCopyInfo> moreCraftNoList)
    {
        WipCopyInfo copyInfo = new WipCopyInfo();
        // 合计 PNL面积
        BigDecimal onePnlArea = BigDecimal.ZERO;// 单个pnl的面积
        BigDecimal pcsArea = BigDecimal.ZERO;
        for (int i = 0; i < moreCraftNoList.size(); i++)
        {
            onePnlArea = onePnlArea.add(moreCraftNoList.get(i).getPnlArea());
            pcsArea = pcsArea.add(moreCraftNoList.get(i).getPcsArea());
        }
        copyInfo.setPnlArea(onePnlArea);
        copyInfo.setPcsArea(pcsArea);
        return copyInfo;
    }
    
    private void wipCopyAddWipInfo(WipCopyInfo info, WipCopy wipCopy)
    {
        String[] feedIds = wipCopy.getFeedIds().split(",");
        boolean isExist = false;
        for (int i = 0; i < feedIds.length; i++)
        {
            if (feedIds[0].equals(info.getFeedId()))
            {
                isExist = true;
                break;
            }
        }
        if (!isExist)
        {
            wipCopy.setFeedIds(wipCopy.getFeedIds() + "," + info.getFeedId());
            wipCopy.setOrderQty(integerAdd(wipCopy.getOrderQty(), info.getOrderQty()));
            wipCopy.setFeedQty(integerAdd(wipCopy.getFeedQty(), info.getFeedQty()));
        }
        wipCopy.setPcsArea(wipCopy.getPcsArea().add(info.getPcsArea()));
        wipCopy.setQtyPnlA(integerAdd(wipCopy.getQtyPnlA(), info.getQtyPnlA()));
        wipCopy.setQtyPnlB(integerAdd(wipCopy.getQtyPnlB(), info.getQtyPnlB()));
        wipCopy.setQtyPcsA(integerAdd(wipCopy.getQtyPcsA(), info.getQtyPcsA()));
        wipCopy.setQtyPcsB(integerAdd(wipCopy.getQtyPcsB(), info.getQtyPcsB()));
        
    }
    
    private Integer integerAdd(Integer one, Integer two)
    {
        if (one == null)
        {
            return two;
        }
        if (two == null)
        {
            return one;
        }
        return one + two;
    }
    
    private void handleCopyInfoArea(WipCopyInfo copyInfo)
    {
        int qtyPcs = 0;
        int qtyPcsA = copyInfo.getQtyPcsA() == null ? 0 : copyInfo.getQtyPcsA();
        int qtyPcsB = copyInfo.getQtyPcsB() == null ? 0 : copyInfo.getQtyPcsB();
        int setA = copyInfo.getQtySetA() == null ? 0 : copyInfo.getQtySetA();
        int setB = copyInfo.getQtySetB() == null ? 0 : copyInfo.getQtySetB();
        int discardA = copyInfo.getDiscardPcsQtyA() == null ? 0 : copyInfo.getDiscardPcsQtyA();
        int discardB = copyInfo.getDiscardPcsQtyB() == null ? 0 : copyInfo.getDiscardPcsQtyB();
        BigDecimal nomal = new BigDecimal(1000000);
        if (copyInfo.getDetailCraftId() != null)
        {
            // 不同生产编号合同的 SUM(TRUNCATE ((IFNULL(aaa.qtyPcsT,0) - IFNULL(aaa.discardQty,0)) * eee.setLength * eee.setWidth
            // / eee.pnlDivisor / 1000000,2)
            if (copyInfo.getSetLength() == null || copyInfo.getSetWidth() == null || copyInfo.getQtySetT() == null
                || copyInfo.getQtySetT() == 0)
            {
                copyInfo.setPcsArea(BigDecimal.ZERO);
            }
            else
            {
                qtyPcs = qtyPcsA + qtyPcsB - discardA - discardB;
                copyInfo.setPcsArea(copyInfo.getSetLength()
                    .multiply(copyInfo.getSetWidth())
                    .multiply(new BigDecimal(qtyPcs))
                    .divide(new BigDecimal(copyInfo.getQtySetT()), 4, BigDecimal.ROUND_HALF_UP)
                    .divide(nomal, 4, BigDecimal.ROUND_HALF_UP));
            }
        }
        else
        {
            if (copyInfo.getSetLength() == null || copyInfo.getSetWidth() == null || copyInfo.getQtySetT() == null
                || copyInfo.getQtySetT() == 0)
            {
                copyInfo.setPcsArea(BigDecimal.ZERO);
            }
            else
            {
                qtyPcs = qtyPcsA + qtyPcsB;
                copyInfo.setPcsArea(copyInfo.getSetLength()
                    .multiply(copyInfo.getSetWidth())
                    .multiply(new BigDecimal(qtyPcs))
                    .divide(new BigDecimal(copyInfo.getQtySetT()), 4, BigDecimal.ROUND_HALF_UP)
                    .divide(nomal, 4, BigDecimal.ROUND_HALF_UP));
            }
        }
        
        copyInfo.setPnlArea(copyInfo.getSetLength()
            .multiply(copyInfo.getSetWidth())
            .multiply(new BigDecimal(setA + setB))
            .divide(nomal, 4, BigDecimal.ROUND_HALF_UP));
    }
    
    private void handleDiscartInfo(WipCopyInfo copyInfo, List<WipCopyInfo> craftDiscardList)
    {
        if (!CollectionUtils.isEmpty(craftDiscardList))
        {
            for (WipCopyInfo info : craftDiscardList)
            {
                if (copyInfo.getFeedId().equals(info.getFeedId())
                    && (copyInfo.getBatchDetailId() == (info.getBatchDetailId())
                        && copyInfo.getCraftNo().equals(info.getCraftNo())))
                {
                    if (info.getDiscardPcsQtyA() != null || info.getDiscardPcsQtyB() != null)
                    {
                        copyInfo.setDiscardPcsQtyA(info.getDiscardPcsQtyA());
                        copyInfo.setDiscardPcsQtyB(info.getDiscardPcsQtyB());
                    }
                    else if (info.getDiscardPcsQty() != null)
                    {
                        if (copyInfo.getQtyPcsA() != null && copyInfo.getQtyPcsA() > info.getDiscardPcsQty())
                        {
                            copyInfo.setDiscardPcsQtyA(info.getDiscardPcsQty());
                        }
                        else if (copyInfo.getQtyPcsB() != null && copyInfo.getQtyPcsB() > info.getDiscardPcsQty())
                        {
                            copyInfo.setDiscardPcsQtyB(info.getDiscardPcsQty());
                        }
                        else if (copyInfo.getQtyPcsA() != null)
                        {
                            copyInfo.setDiscardPcsQtyA(copyInfo.getQtyPcsA());
                            copyInfo.setDiscardPcsQtyB(info.getDiscardPcsQty() - copyInfo.getQtyPcsA());
                        }
                    }
                    
                }
            }
        }
        
    }
    
    private List<WipCopyInfo> getCraftDiscardInfo(List<Wip> list)
    {
        StringBuilder feedIdsb = new StringBuilder();
        for (Wip info : list)
        {
            if (!CollectionUtils.isEmpty(info.getNotification().getProduceBatchDetailCraftList()))
            {
                if (feedIdsb.length() == 0)
                {
                    feedIdsb.append(info.getFeeding().getRecordId());
                }
                else
                {
                    feedIdsb.append(",");
                    feedIdsb.append(info.getFeeding().getRecordId());
                }
            }
        }
        String feedIds = feedIdsb.toString();
        return inventoryDao.getFeedBatchAndDiscardInfo(feedIds);
        
    }
    
    private void genNo(String companyId, String storeId, Date snapshotDate)
    {
        Inventory in = inventoryDao.getCurrentCopyRec(snapshotDate, companyId, storeId);
        if (StringUtils.isEmpty(in.getNo()))
        {
            // PD+仓库编码（3位）+盘点类型（1位）+年月日时分秒
            String type = "0";// 盘点类型位，现在默认为0
            String store = "000" + storeId;// 仓库位
            store = store.substring(store.length() - 3, store.length());
            in.setNo("PD" + store + type + new SimpleDateFormat("yyMMddHHmmss").format(new Date()));
            inventoryDao.updateInventoryNo(in);
        }
    }
    
    @Transactional(readOnly = false)
    public String editCopyStock(Material material)
    {
        if (null != material.getSencondStrocks())
        {
            material.setSnapStrocks(material.getSencondStrocks().toString());
        }
        else if (null != material.getFirstStrocks())
        {
            material.setSnapStrocks(material.getFirstStrocks().toString());
        }
        else
        {
            material.setSnapStrocks(null);
        }
        inventoryDao.editCopyStock(material);
        return "success";
    }
    
    @Transactional(readOnly = false)
    public Page<Material> getSnapDifferenceList(Page<Material> page, Material material)
    {
        material.setPage(page);
        List<Material> materialList = Collections.emptyList();
        if (!StringUtils.isEmpty(material.getSnapshotDate()))
        {
            materialList = inventoryDao.getSnapDifferenceList(material);
        }
        // 查下所有关联的出入库
        if (null != material && !StringUtils.isEmpty(material.getRecordId()) && !CollectionUtils.isEmpty(materialList))
        {
            // addInOutInfo(material.getRecordId(), materialList);
            // 查下库位
            StoreHouse store = storeHouseDao.get(material.getStoreHouseId() + "");
            int type = 1;// 默认原材料
            if (null != store && 2 == store.getType())
            {
                type = 2;// 成品
            }
            if (!"4".equals(material.getInvenFlag()))
            {
                addPlaceInfo(materialList, type);
            }
            else
            {
                addPlaceInfoExist(materialList);
            }
            
        }
        // 设置对应的出入库
        page.setList(materialList);
        return page;
    }
    
    private void addPlaceInfoExist(List<Material> materialList)
    {
        // 查询默认公司简称
        String defComName = "江西领德辉电路有限公司";
        Branch br = new Branch();
        Company company = UserUtils.getUser().getCompany();
        br.setCompany(company);
        br.setBindErpComId(company.getRecordId());
        List<Branch> branchs = branchDao.findList(br);
        if (!CollectionUtils.isEmpty(branchs))
        {
            defComName = branchs.get(0).getShortName();
        }
        String materailIds = "";
        String recoredIs = "";
        for (Material ma : materialList)
        {
            materailIds = materailIds + "," + ma.getMaterialId();
            recoredIs = recoredIs + "," + ma.getRecordId();
        }
        if (materailIds.endsWith(","))
        {
            materailIds = materailIds.substring(0, materailIds.length() - 1);
        }
        // 查下已经设置了多库位库存
        List<MaterialPlaceCom> copyPlaceInfo = inventoryDao.getCopyPlaceInfo(recoredIs);
        List<MaterialPlaceCom> templist = null;
        for (Material m : materialList)
        {
            templist = new ArrayList<MaterialPlaceCom>();
            // 已经存在
            for (MaterialPlaceCom copyPlace : copyPlaceInfo)
            {
                // 物料id和盘点明细id相等
                if (m.getMaterialId().equals(copyPlace.getMaterialId())
                    && m.getRecordId().equals(copyPlace.getMatPlaceComInId()))
                {
                    templist.add(copyPlace);
                }
            }
            handelPlaceName(templist, defComName);
            m.setMaterialPlaceComList(templist);
        }
    }
    
    private void addPlaceInfo(List<Material> materialList, int type)
    {
        
        // 查询默认公司简称
        String defComName = "江西领德辉电路有限公司";
        Branch br = new Branch();
        Company company = UserUtils.getUser().getCompany();
        br.setCompany(company);
        br.setBindErpComId(company.getRecordId());
        List<Branch> branchs = branchDao.findList(br);
        if (!CollectionUtils.isEmpty(branchs))
        {
            defComName = branchs.get(0).getShortName();
        }
        String materailIds = "";
        String recoredIs = "";
        for (Material ma : materialList)
        {
            materailIds = materailIds + "," + ma.getMaterialId();
            recoredIs = recoredIs + "," + ma.getRecordId();
        }
        if (materailIds.endsWith(","))
        {
            materailIds = materailIds.substring(0, materailIds.length() - 1);
        }
        try
        {
            List<MaterialPlaceCom> list = null;
            if (1 == type)
            {
                RawStockUtil rsu = new RawStockUtil();
                list = rsu.getMaterialApplyStock(materailIds,
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(materialList.get(0).getSnapshotDate()),
                    null);
            }
            else
            {
                ProductStockUtil proUtil = new ProductStockUtil();
                list = proUtil.getMaterialApplyStock(materailIds,
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(materialList.get(0).getSnapshotDate()),
                    null);
            }
            
            // 查下已经设置了多库位库存
            List<MaterialPlaceCom> copyPlaceInfo = inventoryDao.getCopyPlaceInfo(recoredIs);
            List<MaterialPlaceCom> templist = null;
            for (Material m : materialList)
            {
                templist = new ArrayList<MaterialPlaceCom>();
                // 已经存在
                for (MaterialPlaceCom copyPlace : copyPlaceInfo)
                {
                    // 物料id和盘点明细id相等
                    if (m.getMaterialId().equals(copyPlace.getMaterialId())
                        && m.getRecordId().equals(copyPlace.getMatPlaceComInId()))
                    {
                        templist.add(copyPlace);
                    }
                }
                // 重新查询
                if (templist.size() == 0)
                {
                    for (MaterialPlaceCom mp : list)
                    {
                        if (m.getMaterialId().equals(mp.getMaterialId()))
                        {
                            mp.setChangeStocks(BigDecimal.ZERO);
                            mp.setRemark("");
                            mp.setStocks(mp.getPeriodStocks());
                            templist.add(mp);
                        }
                    }
                    // 多库位，只存在一条库存不等于零，设置这条为默认的实盘库位
                    if (templist.size() > 1)
                    {
                        int defNum = 0;
                        int defIndex = -1;
                        for (int i = 0; i < templist.size(); i++)
                        {
                            if (null != templist.get(i).getStocks()
                                && templist.get(i).getStocks().compareTo(BigDecimal.ZERO) > 0)
                            {
                                defNum++;
                                defIndex = i;
                            }
                        }
                        if (defNum == 1)
                        {
                            templist.get(defIndex)
                                .setChangeStocks(
                                    null == m.getSnapStrocks() ? BigDecimal.ZERO : new BigDecimal(m.getSnapStrocks()));
                        }
                    }
                }
                // 需要对库位名称销售公司简称，为空显示当前公司和编号处理50：H-物料id,52：s-物料id，其它 ：j-物料id；
                handelPlaceName(templist, defComName);
                m.setMaterialPlaceComList(templist);
            }
        }
        catch (ParseException e)
        {
            e.printStackTrace();
        }
        
    }
    
    // 需要对库位名称销售公司简称，为空显示当前公司和编号处理50：s-物料id,52：H-物料id，其它 ：j-物料id；
    private void handelPlaceName(List<MaterialPlaceCom> templist, String defComName)
    {
        if (!CollectionUtils.isEmpty(templist))
        {
            for (MaterialPlaceCom pc : templist)
            {
                // 销售公司
                if (StringUtils.isEmpty(pc.getComName()))
                {
                    pc.setComName(defComName);
                }
                // 库存位置
                if ("52".equals(pc.getSaleCompanyId()))
                {
                    pc.setPlaceName("s-" + pc.getRecordId() + "-" + pc.getMaterialId());
                }
                else if ("50".equals(pc.getSaleCompanyId()))
                {
                    pc.setPlaceName("h-" + pc.getRecordId() + "-" + pc.getMaterialId());
                }
                else
                {
                    pc.setPlaceName("j-" + pc.getRecordId() + "-" + pc.getMaterialId());
                }
            }
        }
        
    }
    
    /**
     * countKeyword 中保存库存id:数量;
     *
     * @return
     */
    /*
     * private List<MaterialPlaceCom> getPlanceStockFromCountKeyword(String countKeyword) { List<MaterialPlaceCom>
     * resultList = new ArrayList<MaterialPlaceCom>(); if (!StringUtils.isEmpty(countKeyword)) { String[] infos =
     * countKeyword.split(";"); for (int i = 0; i < infos.length; i++) { String[] contents = infos[i].split(":"); if
     * (contents.length == 2) { MaterialPlaceCom m = new MaterialPlaceCom(); m.setMaterPlaceId(contents[0]);
     * m.setStocks(StringUtils.isEmpty(contents[1]) ? BigDecimal.ZERO : new BigDecimal(contents[1])); resultList.add(m);
     * } } } return resultList; }
     */
    
    /*
     * private void addInOutInfo(String recordId, List<Material> materialList) { List<RawmaterialStock> stockList =
     * inventoryDao.findStockInfos(recordId); if (!CollectionUtils.isEmpty(stockList)) { List<RawmaterialStock> tempList
     * = null; for (Material m : materialList) { tempList = new ArrayList<RawmaterialStock>(); for (RawmaterialStock rs
     * : stockList) { if (!StringUtils.isEmpty(m.getMaterialId()) && m.getMaterialId().equals(rs.getMaterialId())) {
     * tempList.add(rs); } } m.setStockList(tempList); } } }
     */
    @Transactional(readOnly = false)
    public List<Material> getSnapDifference(Material material)
    {
        return inventoryDao.getSnapDifferenceList(material);
    }
    
    @Transactional(readOnly = false)
    public String auditSnap(Material material)
    {
        if (null == material.getStoreHouseId() && !StringUtils.isEmpty(material.getStoreId()))
        {
            material.setStoreHouseId(Integer.parseInt(material.getStoreId()));
        }
        // 更新状态为4，审核
        Inventory inventory = new Inventory(material.getRecordId());
        Inventory dbObj = inventoryDao.get(inventory);
        inventory.setCompany(material.getCompany());
        inventory.setDealFlag("4");
        inventoryDao.auditInventoryFlag(inventory);
        // 改为盈亏确认，直接取消禁止入出库等控制
        // inventoryDao.updateInventoryFlag(inventory);
        // 查下此次盘点，存在盘差的记录
        List<Material> materialList = inventoryDao.getSnapDifferenceList(material);
        if (materialList == null || materialList.size() == 0)
        {
            // 没有盘差
            return "success";
        }
        StoreHouse store = storeHouseDao.get(Math.abs(material.getStoreHouseId()) + "");
        if (null == store)
        {
            return "仓库不存在数据库中";
        }
        else if (store.getType() == 2)
        {
            BigDecimal diff = BigDecimal.ZERO;
            Integer type = null;
            // 成品 return "success";
            List<ProductStore> insetPsList = new ArrayList<>();
            ProductStore ps = null;
            for (Material m : materialList)
            {
                diff = new BigDecimal(m.getSnapStrocks() == null ? "0" : m.getSnapStrocks())
                    .subtract(m.getStocks() == null ? BigDecimal.ZERO : m.getStocks());// 实盘-账面>0 需要入库；实盘-账面<0 需要出库；实盘
                // = 账面+-出入库 = 新账面
                ps = new ProductStore();
                if (diff.compareTo(BigDecimal.ZERO) > 0)
                {
                    type = TypeKey.ST_PRODUCT_INOUTTYPE_IN;// 盘盈
                    if ("-44".equals(dbObj.getStoreId().toString()))
                    {
                        type = TypeKey.ST_PRODUCT_INOUTTYPE_STOCKMOVE_IN;// 尾数移库入库
                    }
                    ps.setRemark("盘盈");
                }
                else if (diff.compareTo(BigDecimal.ZERO) < 0)
                {
                    type = TypeKey.ST_PRODUCT_INOUTTYPE_OUT;// 盘亏
                    if ("-44".equals(dbObj.getStoreId().toString()))
                    {
                        type = TypeKey.ST_PRODUCT_INOUTTYPE_STOCKMOVE_OUT;// 尾数移库出库
                    }
                    diff = diff.abs();// 取绝对值
                    ps.setRemark("盘亏");
                    // 最多出到零
                }
                
                ps.setMaterialId(m.getMaterialId());
                ps.setCompany(UserUtils.getUser().getCompany());
                ps.setStoreHouseId(material.getStoreHouseId());
                ps.setInoutType(type);
                ps.setQuantity(diff.intValue());
                try
                {
                    ps.setOperateDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(material.getSnapshotDate()));
                }
                catch (ParseException e)
                {
                    e.printStackTrace();
                }
                // ps.setStockPlaceId(3588+"");//临时库位id
                // ps.setStockPlaceComId(stockPlaceComId);
                ps.preInsert();
                insetPsList.add(ps);
            }
            if (!CollectionUtils.isEmpty(insetPsList))
            {
                inventoryDao.copyUpdateStock(insetPsList);
            }
            return "success";
            // return "成品仓盈亏不进行自动生成出入库单，只展示库存差异";
        }
        
        String materialIds = "";
        for (Material ma : materialList)
        {
            if (!StringUtils.isEmpty(materialIds))
            {
                materialIds = materialIds + "," + ma.getMaterialId();
            }
            else
            {
                materialIds = ma.getMaterialId();
            }
        }
        // 查询所有物料存在的仓库位置信息
        RawStockUtil rsu = new RawStockUtil();
        List<MaterialPlaceCom> comList = null;
        try
        {
            comList = rsu.getMaterialApplyStock(materialIds,
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(material.getSnapshotDate()),
                null);
            // 库存大的优先
            if (!CollectionUtils.isEmpty(comList))
            {
                comList.sort((x, y) -> compareBigDiciamlDes(x.getPeriodStocks(), y.getPeriodStocks()));
            }
        }
        catch (ParseException e)
        {
            e.printStackTrace();
            return "failure";
        }
        // 生成出库和入库单 ；盘差记录循环-物料位置和存储量循环，id相等，盘差，则减去数量，如果数量不够，继续循环；如果是入库，第一个为摸版，新增入库单；
        BigDecimal diff = BigDecimal.ZERO;
        Integer type = null;
        BigDecimal periodStocks = BigDecimal.ZERO;
        List<Material> insertList = new ArrayList<Material>();
        Material ins = null;
        User user = UserUtils.getUser();
        Date currD = new Date();
        // String remarkStr = "盘点:";
        for (Material m : materialList)
        {
            diff = new BigDecimal(m.getSnapStrocks() == null ? "0" : m.getSnapStrocks())
                .subtract(m.getStocks() == null ? BigDecimal.ZERO : m.getStocks());// 实盘-账面>0 需要入库；实盘-账面<0 需要出库；实盘
            // = 账面+-出入库 = 新账面
            if (diff.compareTo(BigDecimal.ZERO) > 0)
            {
                type = TypeKey.ST_MATERIAL_INOUTTYPE_OTHER_IN;// 盘盈
            }
            else if (diff.compareTo(BigDecimal.ZERO) < 0)
            {
                type = TypeKey.ST_MATERIAL_INOUTTYPE_OUT;// 盘亏
                diff = diff.abs();// 取绝对值
            }
            List<MaterialPlaceCom> setInfo = inventoryDao.getCopyPlaceInfo(m.getRecordId());
            // 多库位处理
            if (!CollectionUtils.isEmpty(setInfo))
            {
                // 实盘库位库存
                // List<MaterialPlaceCom> setInfo = getPlanceStockFromCountKeyword(m.getCountKeyword());
                for (MaterialPlaceCom newStock : setInfo)
                {
                    for (MaterialPlaceCom oldStock : comList)
                    {
                        if (newStock.getMaterialId().equals(oldStock.getMaterialId()) && null != newStock.getRecordId()
                            && newStock.getRecordId().equals(oldStock.getRecordId()))
                        {
                            // 出库单
                            periodStocks =
                                null != oldStock.getPeriodStocks() ? oldStock.getPeriodStocks() : BigDecimal.ZERO;
                            String des = "";
                            if (newStock.getChangeStocks().compareTo(periodStocks) > 0)
                            {
                                type = TypeKey.ST_MATERIAL_INOUTTYPE_OTHER_IN;// 盘盈
                                des = "盘盈";
                            }
                            else if (newStock.getChangeStocks().compareTo(periodStocks) < 0)
                            {
                                type = TypeKey.ST_MATERIAL_INOUTTYPE_OUT;// 盘亏
                                des = "盘亏";
                            }
                            else
                            {
                                continue;
                            }
                            diff = newStock.getChangeStocks().subtract(periodStocks).abs();// 取绝对值
                            ins = new Material();
                            ins.setCompanyId(m.getCompany().getRecordId());
                            ins.setMaterialId(m.getMaterialId());
                            ins.setStoreHouseId(material.getStoreHouseId());
                            ins.setInOutType(type);
                            ins.setQuantity(diff + "");
                            // ins.setOperateDate(m.getSnapshotDate());
                            ins.setStatus(99999901);
                            ins.setActiveFlag("1");
                            ins.setCreatedBy(user);
                            ins.setCreatedDate(currD);
                            ins.setLastUpdBy(user);
                            ins.setLastUpdDate(currD);
                            ins.setRemark(
                                des + ":" + (StringUtils.isEmpty(newStock.getRemark()) ? "" : newStock.getRemark()));
                            ins.setStockPlaceId(oldStock.getMaterPlaceId());
                            ins.setStockPlaceComId(oldStock.getRecordId());
                            ins.setMaterialCopyId(m.getRecordId());
                            insertList.add(ins);
                        }
                    }
                }
                
            }
            else
            {
                for (MaterialPlaceCom mpc : comList)
                {
                    if (m.getMaterialId().equals(mpc.getMaterialId()))
                    {
                        if (type == TypeKey.ST_MATERIAL_INOUTTYPE_OUT)
                        {
                            // 出库单
                            periodStocks = null != mpc.getPeriodStocks() ? mpc.getPeriodStocks() : BigDecimal.ZERO;
                            // 需要出库的量小于库位上的量，直接在当前库位出盘差量
                            if (diff.compareTo(periodStocks) <= 0)
                            {
                                ins = new Material();
                                ins.setCompanyId(m.getCompany().getRecordId());
                                ins.setMaterialId(m.getMaterialId());
                                ins.setStoreHouseId(material.getStoreHouseId());
                                ins.setInOutType(TypeKey.ST_MATERIAL_INOUTTYPE_OUT);
                                ins.setQuantity(diff + "");
                                // ins.setOperateDate(m.getSnapshotDate());
                                ins.setStatus(99999901);
                                ins.setActiveFlag("1");
                                ins.setCreatedBy(user);
                                ins.setCreatedDate(currD);
                                ins.setLastUpdBy(user);
                                ins.setLastUpdDate(currD);
                                ins.setRemark(
                                    "盘亏:" + (StringUtils.isEmpty(m.getDiffExplain()) ? "" : m.getDiffExplain()));
                                ins.setStockPlaceId(mpc.getMaterPlaceId());
                                ins.setStockPlaceComId(mpc.getRecordId());
                                ins.setMaterialCopyId(m.getRecordId());
                                insertList.add(ins);
                                diff = BigDecimal.ZERO;
                                break;
                            }
                            // 需要出库的量大于库位上的量，直接出库位上所有量，余量继续循环
                            else if (diff.compareTo(periodStocks) > 0 && periodStocks.compareTo(BigDecimal.ZERO) > 0)
                            {
                                // 出库单
                                ins = new Material();
                                ins.setCompanyId(m.getCompany().getRecordId());
                                ins.setMaterialId(m.getMaterialId());
                                ins.setStoreHouseId(material.getStoreHouseId());
                                ins.setInOutType(TypeKey.ST_MATERIAL_INOUTTYPE_OUT);
                                ins.setQuantity(periodStocks + "");
                                // ins.setOperateDate(m.getSnapshotDate());
                                ins.setStatus(99999901);
                                ins.setActiveFlag("1");
                                ins.setCreatedBy(user);
                                ins.setCreatedDate(currD);
                                ins.setLastUpdBy(user);
                                ins.setLastUpdDate(currD);
                                ins.setRemark(
                                    "盘亏:" + (StringUtils.isEmpty(m.getDiffExplain()) ? "" : m.getDiffExplain()));
                                ins.setStockPlaceId(mpc.getMaterPlaceId());
                                ins.setStockPlaceComId(mpc.getRecordId());
                                ins.setMaterialCopyId(m.getRecordId());
                                insertList.add(ins);
                                diff = diff.subtract(periodStocks);
                                continue;
                            }
                            
                        }
                        else if (type == TypeKey.ST_MATERIAL_INOUTTYPE_OTHER_IN)
                        {
                            // 入库单
                            ins = new Material();
                            ins.setCompanyId(m.getCompany().getRecordId());
                            ins.setMaterialId(m.getMaterialId());
                            ins.setStoreHouseId(material.getStoreHouseId());
                            ins.setInOutType(TypeKey.ST_MATERIAL_INOUTTYPE_OTHER_IN);
                            ins.setQuantity(diff + "");
                            // ins.setOperateDate(m.getSnapshotDate());
                            ins.setStatus(99999901);
                            ins.setActiveFlag("1");
                            ins.setCreatedBy(user);
                            ins.setCreatedDate(currD);
                            ins.setLastUpdBy(user);
                            ins.setLastUpdDate(currD);
                            ins.setRemark("盘盈:" + (StringUtils.isEmpty(m.getDiffExplain()) ? "" : m.getDiffExplain()));
                            ins.setStockPlaceId(mpc.getMaterPlaceId());
                            ins.setStockPlaceComId(mpc.getRecordId());
                            ins.setMaterialCopyId(m.getRecordId());
                            insertList.add(ins);
                            diff = BigDecimal.ZERO;
                            break;
                        }
                    }
                }
            }
            
            // 理论上出入库应该完成，diff ==0;
        }
        if (Collections3.isNotEmpty(insertList))
        {
//            inventoryDao.batchInsertStMaterialStore(insertList);
            for(Material mat : insertList)
            {
                inventoryDao.insertStMaterialStore(mat);
                //原料仓、辅料仓
                if (mat.getStoreHouseId() == 42 || mat.getStoreHouseId() == 43)
                {
                    if(TypeKey.ST_MATERIAL_INOUTTYPE_FEEDING_OUT.compareTo(mat.getInOutType()) == 0 || TypeKey.ST_MATERIAL_INOUTTYPE_OUT.compareTo(mat.getInOutType()) == 0)
                    {
                        RawmaterialStock stock = new RawmaterialStock();
                        stock.setRecordId(mat.getRecordId());
                        stock.setMaterialId(mat.getMaterialId());
                        stock.setQuantity(new BigDecimal(mat.getQuantity()));
                        stock.setCompany(UserUtils.getUser().getCompany());
                        stock.setInoutType(mat.getInOutType());
                        RawStockUtil rawStockUtil = new RawStockUtil();
                        rawStockUtil.handlePurDetailFirstInFirstOut(stock,2);
                    }
                }
            }
        }
        return "success";
    }
    
    public int compareBigDiciamlDes(BigDecimal x, BigDecimal y)
    {
        if (x == null)
        {
            return 1;
        }
        else if (y == null)
        {
            return -1;
        }
        else
        {
            return -x.compareTo(y);
        }
    }
    
    @Transactional(readOnly = false)
    public List<Material> dataHandle(List<List<Object>> listob, String recRecordId, ImportReturnVo vo)
    {
        List<Material> redList = Collections.emptyList();
        // 查出所有的盘点明细，物料no， recordId,companyId
        List<Material> copyDetailList =
            inventoryDao.findAllCopyDetailList(recRecordId, UserUtils.getUser().getCompany().getRecordId());
        List<Material> updateSnapStockList = new ArrayList<Material>();
        List<Material> updateSnapStockListTwo = new ArrayList<Material>();
        if (CollectionUtils.isEmpty(copyDetailList))
        {
            return redList;
        }
        StoreHouse house = copyDetailList.get(0).getStorehouse();
        // 循环明细，对比更新录入盘点的数量，放入到更新的list
        Map<String, BigDecimal> inData = new HashMap<String, BigDecimal>();
        Map<String, BigDecimal> inDataTwo = new HashMap<String, BigDecimal>();
        BigDecimal snapStock = null;
        BigDecimal snapStockTwo = null;
        if (Collections3.isNotEmpty(listob))
        {
            String materialNoCellTitle = "物料编号";
            String productNoCellTitle = "生产编号";
            String snapStockCellTitle = "初盘库存";
            String snapStockCellTitleTwo = "复盘库存";
            Integer materialNoCellNum = null;
            Integer snapStockCellNum = null;
            Integer snapStockCellNumTwo = null;
            Integer maxCellNum = 0;
            for (int i = 0; i < listob.size(); i++)
            {
                List<Object> lo = listob.get(i);
                // 先读取物料编号和实盘库存所在的列
                if (null == materialNoCellNum || (null == snapStockCellNum || snapStockCellNumTwo == snapStockCellNum))
                {
                    for (int n = 0; n < lo.size(); n++)
                    {
                        String content = lo.get(n) == null ? "" : lo.get(n).toString();
                        if (null == materialNoCellNum && (materialNoCellTitle.equals(content.trim())
                            || productNoCellTitle.equals(content.trim())))
                        {
                            materialNoCellNum = n;
                            maxCellNum = maxCellNum > n ? maxCellNum : n;
                        }
                        if (null == snapStockCellNum && snapStockCellTitle.equals(content.trim()))
                        {
                            snapStockCellNum = n;
                            maxCellNum = maxCellNum > n ? maxCellNum : n;
                        }
                        if (null == snapStockCellNumTwo && snapStockCellTitleTwo.equals(content.trim()))
                        {
                            snapStockCellNumTwo = n;
                            maxCellNum = maxCellNum > n ? maxCellNum : n;
                        }
                    }
                }
                else
                {
                    if (lo.size() > maxCellNum && !StringUtils.isEmpty(lo.get(materialNoCellNum))
                        && (!StringUtils.isEmpty(lo.get(snapStockCellNum))
                            || !StringUtils.isEmpty(lo.get(snapStockCellNumTwo))))
                    {
                        String materialNo = lo.get(materialNoCellNum).toString();
                        // String placeNo = lo.get(1).toString();
                        if (null != snapStockCellNum && null != lo.get(snapStockCellNum)
                            && !StringUtils.isEmpty(lo.get(snapStockCellNum).toString().trim())
                            && com.kyb.pcberp.common.utils.StringUtils.isNumber(lo.get(snapStockCellNum).toString()))
                        {
                            snapStock = new BigDecimal(lo.get(snapStockCellNum).toString().trim());
                            if (inData.containsKey(materialNo))
                            {
                                inData.put(materialNo, inData.get(materialNo).add(snapStock));
                            }
                            else
                            {
                                inData.put(materialNo, snapStock);
                            }
                        }
                        
                        if (null != snapStockCellNumTwo && null != lo.get(snapStockCellNumTwo)
                            && !StringUtils.isEmpty(lo.get(snapStockCellNumTwo).toString().trim())
                            && com.kyb.pcberp.common.utils.StringUtils.isNumber(lo.get(snapStockCellNumTwo).toString()))
                        {
                            snapStockTwo = new BigDecimal(lo.get(snapStockCellNumTwo).toString().trim());
                            if (inDataTwo.containsKey(materialNo))
                            {
                                inDataTwo.put(materialNo, inData.get(materialNo).add(snapStockTwo));
                            }
                            else
                            {
                                inDataTwo.put(materialNo, snapStockTwo);
                            }
                        }
                        
                    }
                }
            }
            if (inData.size() == 0 && inDataTwo.size() == 0)
            {
                return redList;
            }
            String noDetailMaterialNos = "";
            int count = 0;
            int tatal = 0;
            Set<String> unNos = new HashSet<>();
            if (inData.size() > 0)
            {
                for (String mNo : inData.keySet())
                {
                    boolean isExist = false;
                    for (Material mr : copyDetailList)
                    {
                        // YLBM00000004 12位 前缀YLBM
                        if (mr.getNo().equals(getMaterialNo(12, "YLBM", mNo, "0"))
                            || mr.getNo().trim().equals(mNo.trim()))
                        {
                            // YLBM原材料开头
                            mr.setFirstStrocks(new BigDecimal(inData.get(mNo).toString().trim()));
                            // 复盘数据为空，或者等于零，则赋值给盘点结果
                            if (mr.getSencondStrocks() == null
                                || mr.getSencondStrocks().compareTo(BigDecimal.ZERO) == 0)
                            {
                                mr.setSnapStrocks(inData.get(mNo).toString());
                            }
                            updateSnapStockList.add(mr);
                            isExist = true;
                            break;
                        }
                    }
                    // 导入的excle中存在的物流编号 ，盘点明细中没找到，盘点量不为零；
                    if (!isExist && BigDecimal.ZERO.compareTo(inData.get(mNo)) != 0)
                    {
                        tatal += inData.get(mNo).intValue();
                        if (!unNos.contains(mNo))
                        {
                            unNos.add(mNo);
                            if (com.kyb.pcberp.common.utils.StringUtils.isNotBlank(noDetailMaterialNos))
                            {
                                noDetailMaterialNos = noDetailMaterialNos + "," + mNo;
                            }
                            else
                            {
                                noDetailMaterialNos = mNo;
                            }
                            // noDetailMaterialNos = noDetailMaterialNos + mNo + ",";
                            System.out.println("第" + (count++) + ":" + noDetailMaterialNos + ",数量:" + tatal);
                        }
                        
                    }
                }
            }
            
            if (inDataTwo.size() > 0)
            {
                for (String mNo : inDataTwo.keySet())
                {
                    boolean isExist = false;
                    for (Material mr : copyDetailList)
                    {
                        // YLBM00000004 12位 前缀YLBM
                        if (mr.getNo().equals(getMaterialNo(12, "YLBM", mNo, "0"))
                            || mr.getNo().trim().equals(mNo.trim()))
                        {
                            // 没有初盘数，不能填复盘数
                            if (mr.getFirstStrocks() == null && !inData.containsKey(mNo))
                            {
                                break;
                            }
                            // YLBM原材料开头
                            mr.setSencondStrocks(new BigDecimal(inDataTwo.get(mNo).toString().trim()));
                            mr.setSnapStrocks(inDataTwo.get(mNo).toString());
                            updateSnapStockListTwo.add(mr);
                            isExist = true;
                            break;
                        }
                    }
                    // 导入的excle中存在的物流编号 ，盘点明细中没找到，盘点量不为零；
                    if (!isExist && BigDecimal.ZERO.compareTo(inDataTwo.get(mNo)) != 0)
                    {
                        if (com.kyb.pcberp.common.utils.StringUtils.isNotBlank(noDetailMaterialNos))
                        {
                            noDetailMaterialNos = noDetailMaterialNos + "," + mNo;
                        }
                        else
                        {
                            noDetailMaterialNos = mNo;
                        }
                        // noDetailMaterialNos =
                        // (noDetailMaterialNos.length() > 0 ? (noDetailMaterialNos + ",") : "") + mNo;
                    }
                }
            }
            
            // 验证编号在数据库存在后，返回页面
            if (noDetailMaterialNos.length() > 0)
            {
                System.out.println(noDetailMaterialNos);
                redList = materialDao.findListByNos(noDetailMaterialNos,
                    UserUtils.getUser().getCompany().getRecordId(),
                    house.getRecordId());
                
            }
        }
        
        // 批量更新盘点数量
        if (updateSnapStockList.size() == 0 && updateSnapStockListTwo.size() == 0)
        {
            return redList;
        }
        if (null != house && com.kyb.pcberp.common.utils.StringUtils.isNotBlank(house.getRecordId()) && house.getRecordId().equals("44"))
        {
            if (updateSnapStockList.size() > 0)
            {
                inventoryDao.batchUpdateSnopStockTwo(updateSnapStockList,house.getRecordId());
                vo.setInsertNum(updateSnapStockList.size());
            }

            if (updateSnapStockListTwo.size() > 0)
            {
                inventoryDao.batchUpdateSnopStockTwo(updateSnapStockListTwo,house.getRecordId());
                vo.setUpdateNum(updateSnapStockListTwo.size());
            }
            for (Material material : copyDetailList)
            {
                if (updateSnapStockList.size() > 0)
                {
                    Boolean isExist = false;
                    for (Material material1 : updateSnapStockList)
                    {
                        if (material.getRecordId().equals(material1.getRecordId()))
                        {
                            isExist = true;
                            break;
                        }
                    }
                    if (!isExist)
                    {
                        material.setFirstStrocks(BigDecimal.ZERO);
                        material.setSencondStrocks(BigDecimal.ZERO);
                        material.setSnapStrocks("0");
                        material.setStocks(BigDecimal.ZERO);
                        inventoryDao.updateBatchUpdateSnopStock(material);
                    }
                }
                if (updateSnapStockListTwo.size() > 0)
                {
                    Boolean isExist = false;
                    for (Material materialTwo : updateSnapStockListTwo)
                    {
                        if (material.getRecordId().equals(materialTwo.getRecordId()))
                        {
                            isExist = true;
                            break;
                        }
                    }
                    if (!isExist)
                    {
                        material.setFirstStrocks(BigDecimal.ZERO);
                        material.setSencondStrocks(BigDecimal.ZERO);
                        material.setSnapStrocks("0");
                        material.setStocks(BigDecimal.ZERO);
                        inventoryDao.updateBatchUpdateSnopStock(material);
                    }
                }
            }
        }
        else
        {
            if (updateSnapStockList.size() > 0)
            {
                inventoryDao.batchUpdateSnopStock(updateSnapStockList);
                vo.setInsertNum(updateSnapStockList.size());
            }

            if (updateSnapStockListTwo.size() > 0)
            {
                inventoryDao.batchUpdateSnopStock(updateSnapStockListTwo);
                vo.setUpdateNum(updateSnapStockListTwo.size());
            }
        }
        return redList;
        
    }
    
    /**
     * @param length 长度
     * @param prex 前缀
     * @param endx 后缀
     * @param addStr 补位
     * @return
     */
    public String getMaterialNo(int length, String prex, String endx, String addStr)
    {
        String result = endx;
        if (!endx.startsWith("YLBM"))
        {
            int num = length - prex.length() - endx.length();
            if (num > 0)
            {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < num; i++)
                {
                    sb.append(addStr);
                }
                result = prex + sb.toString() + result;
            }
            else
            {
                result = prex + result;
            }
        }
        return result;
    }
    
    @Transactional(readOnly = false)
    public String editCopyRemark(Material material)
    {
        if (-1 == material.getStoreHouseId())
        {
            inventoryDao.editWipCopyRemark(material);
        }
        else
        {
            inventoryDao.editCopyRemark(material);
        }
        return "success";
    }
    
    public Integer getStoreType(String storeId)
    {
        StoreHouse store = storeHouseDao.get(storeId);
        if (null != store && 2 == store.getType())
        {
            return 2;
        }
        else
        {
            return 1;
        }
    }
    
    public List<Material> findCopyDetailById(Inventory inventory, Integer type)
    {
        if (2 == type)
        {
            return inventoryDao.findCopyProductDetailById(inventory);
        }
        else
        {
            return inventoryDao.findCopyDetailById(inventory);
        }
        
    }
    
    public Pdf getCopyPrintPDF(List<Material> list, Inventory inventory, String realPath, int type)
    {
        // 查询要打印的成品采购信息
        Pdf pdf = new Pdf();
        ByteArrayOutputStream base = new ByteArrayOutputStream();
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        String inventoryNo = inventoryDao.getInventoryNo(inventory);
        try
        {
            // 查询出分编号工艺的信息
            Rectangle rectPageSize = new Rectangle(PageSize.A4);
            Document document = new Document(rectPageSize, 10, 10, 20, 40);
            
            ByteArrayOutputStream page1 = new ByteArrayOutputStream();
            PdfWriter writer = PdfWriter.getInstance(document, page1);
            BaseFont baseFontChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            setFooter("", writer, baseFontChinese, 9, rectPageSize);
            document.open();
            
            // 主表-无边框
            PdfPTable table = new PdfPTable(1);
            table.setHeaderRows(1);
            table.setSplitLate(false); // 这页表格能放多少就放多少
            table.setTotalWidth(556f);
            table.setLockedWidth(true);
            table.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
            
            // 公司名称
            // 仓库+时间+盘点单
            String titleName = "盘点单";
            if (!CollectionUtils.isEmpty(list))
            {
                titleName = list.get(0).getStorehouseName() + "盘点单";
            }
            PdfPCell title = new PdfPCell(new ParagraphCn16(titleName));
            title.setHorizontalAlignment(Element.ALIGN_CENTER);
            title.setBorder(PdfPCell.NO_BORDER);
            title.setMinimumHeight(36);
            table.addCell(title);
            
            Integer num = 19;
            PdfPTable infoTable = new PdfPTable(num);
            
            PdfPCell cell = new PdfPCell(new ParagraphCn9("单号：" + FmtUtils.empty(inventoryNo)));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(9);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9("开始时间：" + FmtUtils.empty(list.get(0).getSnapshotDate())));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(9);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);
            table.addCell(infoTable);
            
            cell = new PdfPCell(new ParagraphCn12(""));
            cell.setPaddingTop(10);
            cell.setPadding(10f);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);
            
            // 列表标题
            PdfPTable titleTable = null;
            titleTable = new PdfPTable(num);
            titleTable.setHeaderRows(1);
            if (2 == type)
            {
                cell = new PdfPCell(new ParagraphCn9("生产编号"));
            }
            else
            {
                cell = new PdfPCell(new ParagraphCn9("物料编号"));
            }
            
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            if (2 == type)
            {
                cell = new PdfPCell(new ParagraphCn9("客户型号"));
            }
            else
            {
                cell = new PdfPCell(new ParagraphCn9("物料名称"));
            }
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            if (2 == type)
            {
                cell = new PdfPCell(new ParagraphCn9("交货尺寸"));
            }
            else
            {
                cell = new PdfPCell(new ParagraphCn9("品名规格"));
            }
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9("物料类型"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9("物料种类"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9("单位"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9("物料备注"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("结余数"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9("初盘数量"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9("复盘数量"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            // 列表内容
            String storeName = "";
            if (Collections3.isNotEmpty(list))
            {
                storeName = list.get(0).getStorehouseName();
                for (int i = 0; i < list.size(); i++)
                {
                    Material detail = list.get(i);
                    cell = new PdfPCell(new ParagraphCn9(detail.getNo()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);
                    if (2 == type)
                    {
                        cell = new PdfPCell(new ParagraphCn9(detail.getCustomerModels()));
                    }
                    else
                    {
                        cell = new PdfPCell(new ParagraphCn9(detail.getName()));
                    }
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);
                    
                    if (2 == type)
                    {
                        cell = new PdfPCell(new ParagraphCn9(detail.getDeliverySize()));
                    }
                    else
                    {
                        cell = new PdfPCell(new ParagraphCn9(detail.getSpecification()));
                    }
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);
                    
                    cell = new PdfPCell(
                        new ParagraphCn9(null == detail.getMaterialType() ? "" : detail.getMaterialType().getValue()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);
                    
                    cell = new PdfPCell(new ParagraphCn9(detail.getMaterialKindStr()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);
                    
                    cell = new PdfPCell(new ParagraphCn9(detail.getUnitStr()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);
                    
                    cell = new PdfPCell(new ParagraphCn9(detail.getRemark()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);

                    cell = new PdfPCell(new ParagraphCn9(
                        null == detail.getStocks() ? null : detail.getStocks().toString()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);
                    
                    cell = new PdfPCell(new ParagraphCn9(detail.getSnapStrocks()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);
                    
                    cell = new PdfPCell(new ParagraphCn9(
                        null == detail.getSencondStrocks() ? null : detail.getSencondStrocks().toString()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);
                    
                }
                
            }
            table.addCell(titleTable);
            
            // 要求
            
            // 底部
            
            PdfPTable bottomTable = new PdfPTable(num);
            
            cell = new PdfPCell(new ParagraphCn9("初盘人："));
            cell.setPaddingTop(10);
            cell.setPaddingBottom(10);
            cell.setColspan(9);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            bottomTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9("复盘人："));
            cell.setPaddingTop(10);
            cell.setPaddingBottom(10);
            cell.setColspan(9);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            bottomTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9("时间："));
            cell.setPaddingTop(10);
            cell.setPaddingBottom(10);
            cell.setColspan(9);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            bottomTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9("时间："));
            cell.setPaddingTop(5);
            cell.setPaddingBottom(10);
            cell.setColspan(9);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            bottomTable.addCell(cell);
            table.addCell(bottomTable);
            
            document.add(table);
            document.close();
            
            output = page1;
            base = output;
            pdf = new Pdf();
            pdf.setOutPut(base);
            pdf.setShowName(storeName + "盘点单");
            
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return pdf;
    }
    
    public void setFooter(String yeMei, PdfWriter writer, BaseFont bf, int presentFontSize, Rectangle pageSize)
    {
        ItextPdfHeaderFooter headerFooter = new ItextPdfHeaderFooter(yeMei, bf, presentFontSize, pageSize);
        writer.setPageEvent(headerFooter);
    }
    
    @Transactional(readOnly = false)
    public void updateDealFlagOne(Inventory inventory)
    {
        inventoryDao.updateDealFlagOne(inventory);
        
    }
    
    @Transactional(readOnly = false)
    public Map<String, Object> savePlanceInfo(Material material)
    {
        Map<String, Object> result = new HashMap<String, Object>();
        User user = UserUtils.getUser();
        Company company = user.getCompany();
        if (null != material && !CollectionUtils.isEmpty(material.getMaterialPlaceComList())
            && !StringUtils.isEmpty(material.getSnapStrocks()))
        {
            BigDecimal snapStocks = new BigDecimal(material.getSnapStrocks());
            BigDecimal count = BigDecimal.ZERO;
            // String keyWork = "";
            for (MaterialPlaceCom mp : material.getMaterialPlaceComList())
            {
                if (null != mp.getChangeStocks())
                {
                    count = count.add(mp.getChangeStocks());
                }
                else
                {
                    mp.setChangeStocks(BigDecimal.ZERO);
                }
                if (mp.getChangeStocks().compareTo(BigDecimal.ZERO) < 0)
                {
                    result.put("result", "fail");
                    result.put("message", "盘点数不能为负数");
                    
                    return result;
                }
                mp.setMatPlaceComInId(material.getRecordId());
                mp.setCompanyId(company.getRecordId());
                mp.setCreatedBy(user);
                mp.setMaterialId(material.getMaterialId());
                // keyWork=keyWork+mp.getMaterPlaceId()+":"+mp.getStocks()+";";
            }
            if (count.compareTo(snapStocks) != 0)
            {
                result.put("result", "fail");
                result.put("message", "物料实盘库存要等于库位盘点数之和");
                return result;
            }
            // material.setCountKeyword(keyWork);
            // 删掉已经存在的
            inventoryDao.deletePlaceInfo(material.getRecordId());
            // 插入新的
            inventoryDao.addPlaceInfo(material.getMaterialPlaceComList());
            material.setCompany(UserUtils.getUser().getCompany());
            // inventoryDao.updatePlanceInfo(material);
        }
        result.put("result", "successs");
        return result;
    }
    
    @Transactional(readOnly = false)
    public Map<String, Object> checkDiffPlance(Inventory inventory)
    {
        Map<String, Object> result = new HashMap<String, Object>();
        inventory.setCompany(UserUtils.getUser().getCompany());
        if (-1 == inventory.getStoreId())
        {
            // WIP检查
            // 检查明细实盘相加和盘点数是否相等
            List<WipCopy> copyList = inventoryDao.checkWipAllInfoQty(inventory);
            if (!CollectionUtils.isEmpty(copyList))
            {
                result.put("message", "生产编号：" + copyList.get(0).getCraftNo() + "多批次实盘数相加不等于实盘数，请检查");
                result.put("result", "fail");
                return result;
            }
            // 检查差异说明
            copyList = inventoryDao.checkWipDiffExplain(inventory);
            if (!CollectionUtils.isEmpty(copyList))
            {
                result.put("message", "差异说明必填且长度不能少于3个字符，请检查");
                result.put("result", "fail");
                return result;
                
            }
            
        }
        else
        {
            // 检查多库位实盘数与物料实盘数是否相等
            inventory.setCompany(UserUtils.getUser().getCompany());
            List<Material> list = inventoryDao.getDiffMaterial(inventory);
            if (!CollectionUtils.isEmpty(list))
            {
                String materialNos = "";
                for (Material m : list)
                {
                    if (materialNos.length() == 0)
                    {
                        materialNos = m.getNo();
                    }
                    else
                    {
                        materialNos = materialNos + "," + m.getNo();
                    }
                }
                result.put("message", "物料：" + materialNos + "库位实盘数相加不等于物料实盘数，请检查");
                result.put("result", "fail");
                return result;
                
            }
            // 检查差异说明是否都填完整了,最少三个字符
            list = inventoryDao.checkDiffExplain(inventory);
            if (!CollectionUtils.isEmpty(list))
            {
                result.put("message", "差异说明必填且长度不能少于3个字符，请检查");
                result.put("result", "fail");
                return result;
                
            }
            // 检查多库位,
            list = inventoryDao.checkMorePlanceDiffExplain(inventory);
            if (!CollectionUtils.isEmpty(list))
            {
                result.put("message", list.get(0).getNo() + "多库位差异说明必填且长度不能少于3个字符，请检查");
                result.put("result", "fail");
                return result;
                
            }
            // 过滤已经设置了分库位的id
            String materailIds = inventoryDao.getMorePlanceMaterailIds(inventory);
            // 检查剩余的物流id，是否存有两个库位库存大于零的情况，有提示
            List<MaterialPlaceCom> placeStockList = null;
            try
            {
                if (!StringUtils.isEmpty(materailIds))
                {
                    if (1 == inventory.getStoreType())
                    {
                        RawStockUtil rsu = new RawStockUtil();
                        
                        placeStockList = rsu.getMaterialApplyStock(materailIds,
                            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(inventory.getSnapshotDate()),
                            null);
                        
                    }
                    else
                    {
                        ProductStockUtil proUtil = new ProductStockUtil();
                        placeStockList = proUtil.getMaterialApplyStock(materailIds,
                            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(inventory.getSnapshotDate()),
                            null);
                    }
                    if (!CollectionUtils.isEmpty(placeStockList))
                    {
                        String[] idArray = materailIds.split(",");
                        for (String mId : idArray)
                        {
                            int defNum = 0;
                            for (MaterialPlaceCom mp : placeStockList)
                            {
                                if (mId.equals(mp.getMaterialId()) && null != mp.getPeriodStocks()
                                    && mp.getPeriodStocks().compareTo(BigDecimal.ZERO) > 0)
                                {
                                    defNum++;
                                }
                            }
                            if (defNum > 1)
                            {
                                // 存在需要分配的多库位
                                Material mat = materialDao.get(mId);
                                result.put("message", "物料：" + mat == null ? "" : mat.getNo() + "需要分配多库位，请检查");
                                result.put("result", "fail");
                                return result;
                            }
                        }
                    }
                    
                }
                
            }
            catch (ParseException e)
            {
                result.put("message", "更新完成状态失败，请稍后重试");
                result.put("result", "fail");
                return result;
            }
        }
        
        // 更新完成状态
        inventory.setDealFlag("3");
        copyM(inventory);
        result.put("result", "successs");
        return result;
    }
    
    public Page<Material> findProductCopyPage(Page<Material> page, Material material)
    {
        material.setPage(page);
        List<Material> materialList = null;
        materialList = inventoryDao.findProductCopyPage(material);
        page.setList(materialList);
        return page;
    }
    
    public Pdf getCopyPrintPDFTwo(List<Material> list, Inventory inventory, String realPath, int type, Integer pageSize)
    {
        // 查询要打印的成品采购信息
        Pdf pdf = new Pdf();
        ByteArrayOutputStream base = new ByteArrayOutputStream();
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        String inventoryNo = inventoryDao.getInventoryNo(inventory);
        try
        {
            // 查询出分编号工艺的信息
            Rectangle rectPageSize = new Rectangle(PageSize.A4);
            Document document = new Document(rectPageSize, 10, 10, 20, 40);
            
            ByteArrayOutputStream page1 = new ByteArrayOutputStream();
            PdfWriter writer = PdfWriter.getInstance(document, page1);
            writer.setPageEmpty(true);
            BaseFont baseFontChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            setFooter("", writer, baseFontChinese, 9, rectPageSize);
            document.open();
            
            // 列表内容
            String storeName = "";
            if (Collections3.isNotEmpty(list))
            {
                storeName = list.get(0).getStorehouseName();
                if (null == pageSize)
                {
                    pageSize = 100;
                }
                int pageNum = list.size() / pageSize;
                int lastInfo = list.size() % pageSize;
                for (int i = 1; i <= pageNum; i++)
                {
                    // 主表-无边框
                    PdfPTable table = new PdfPTable(1);
                    table.setHeaderRows(1);
                    table.setSplitLate(false); // 这页表格能放多少就放多少
                    table.setTotalWidth(556f);
                    table.setLockedWidth(true);
                    table.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
                    String snapshotDate = list.get(0).getSnapshotDate();
                    addTitle(table, inventoryNo, snapshotDate, storeName);
                    // 列表标题
                    PdfPTable titleTable = null;
                    int num = 19;
                    titleTable = new PdfPTable(num);
                    titleTable.setHeaderRows(1);
                    addTableHearder(table, titleTable, type, num);
                    addTableContent(titleTable, type, list, (i - 1) * pageSize, i * pageSize - 1);
                    table.addCell(titleTable);
                    PdfPTable bottomTable = new PdfPTable(num);
                    addTableFooter(bottomTable);
                    table.addCell(bottomTable);
                    document.add(table);
                    document.newPage();
                }
                if (lastInfo > 0)
                {
                    // 主表-无边框
                    document.newPage();
                    PdfPTable table = new PdfPTable(1);
                    table.setHeaderRows(1);
                    table.setSplitLate(false); // 这页表格能放多少就放多少
                    table.setTotalWidth(556f);
                    table.setLockedWidth(true);
                    table.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
                    String snapshotDate = list.get(0).getSnapshotDate();
                    addTitle(table, inventoryNo, snapshotDate, storeName);
                    // 列表标题
                    PdfPTable titleTable = null;
                    int num = 19;
                    titleTable = new PdfPTable(num);
                    titleTable.setHeaderRows(1);
                    addTableHearder(table, titleTable, type, num);
                    addTableContent(titleTable, type, list, list.size() - lastInfo, list.size() - 1);
                    table.addCell(titleTable);
                    PdfPTable bottomTable = new PdfPTable(num);
                    addTableFooter(bottomTable);
                    table.addCell(bottomTable);
                    document.add(table);
                    document.newPage();
                    
                }
            }
            document.close();
            
            output = page1;
            base = output;
            pdf = new Pdf();
            pdf.setOutPut(base);
            pdf.setShowName(storeName + "盘点单");
            
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return pdf;
    }
    
    private void addTitle(PdfPTable table, String inventoryNo, String snapshotDate, String storeName)
    {
        Integer num = 18;
        PdfPTable infoTable = new PdfPTable(num);
        
        PdfPCell cell = new PdfPCell(new ParagraphCn9("盘点单号：" + FmtUtils.empty(inventoryNo)));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(6);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        infoTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("盘点仓库：" + FmtUtils.empty(storeName)));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(6);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        infoTable.addCell(cell);
        table.addCell(infoTable);
        
        cell = new PdfPCell(new ParagraphCn9("开始时间：" + FmtUtils.empty(snapshotDate)));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(6);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        infoTable.addCell(cell);
        table.addCell(infoTable);
        
        cell = new PdfPCell(new ParagraphCn12(""));
        cell.setPaddingTop(10);
        cell.setPadding(10f);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
    }
    
    private void addThreeTitle(PdfPTable table, WipCopy copy, String snapshotDate, String storeName)
    {
        Integer num = 20;
        PdfPTable infoTable = new PdfPTable(num);
        
        PdfPCell cell = new PdfPCell(new ParagraphCn9("工序：" + FmtUtils.empty(copy.getCateName())));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        infoTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("单位：" + FmtUtils.empty(copy.getUnitName())));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        infoTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("盘点日期：" + FmtUtils.empty(snapshotDate)));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(8);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        infoTable.addCell(cell);
        table.addCell(infoTable);
        
        cell = new PdfPCell(new ParagraphCn9("盘点单号：" + FmtUtils.empty(storeName)));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        infoTable.addCell(cell);
        table.addCell(infoTable);
        
        cell = new PdfPCell(new ParagraphCn12(""));
        cell.setPaddingTop(10);
        cell.setPadding(10f);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
    }
    
    private void addTableHearder(PdfPTable table, PdfPTable titleTable, int type, int num)
    {
        // 列表标题
        PdfPCell cell = null;
        if (2 == type)
        {
            cell = new PdfPCell(new ParagraphCn9("生产编号"));
        }
        else
        {
            cell = new PdfPCell(new ParagraphCn9("物料编号"));
        }
        
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        // cell.setBorder(PdfPCell.NO_BORDER);
        titleTable.addCell(cell);
        if (2 == type)
        {
            cell = new PdfPCell(new ParagraphCn9("客户型号"));
        }
        else
        {
            cell = new PdfPCell(new ParagraphCn9("物料名称"));
        }
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        // cell.setBorder(PdfPCell.NO_BORDER);
        titleTable.addCell(cell);
        if (2 == type)
        {
            cell = new PdfPCell(new ParagraphCn9("交货尺寸"));
        }
        else
        {
            cell = new PdfPCell(new ParagraphCn9("品名规格"));
        }
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        // cell.setBorder(PdfPCell.NO_BORDER);
        titleTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("物料类型"));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        // cell.setBorder(PdfPCell.NO_BORDER);
        titleTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("物料种类"));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        // cell.setBorder(PdfPCell.NO_BORDER);
        titleTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("单位"));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        // cell.setBorder(PdfPCell.NO_BORDER);
        titleTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("物料备注"));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        // cell.setBorder(PdfPCell.NO_BORDER);
        titleTable.addCell(cell);

        cell = new PdfPCell(new ParagraphCn9("结余数量"));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        titleTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("初盘数量"));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        // cell.setBorder(PdfPCell.NO_BORDER);
        titleTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("复盘数量"));
        cell.setPaddingTop(1);
        cell.setPaddingBottom(2);
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        // cell.setBorder(PdfPCell.NO_BORDER);
        titleTable.addCell(cell);
    }
    
    private void addTableContent(PdfPTable titleTable, int type, List<Material> list, int beginNum, int endNum)
    {
        PdfPCell cell = null;
        for (int i = beginNum; i <= endNum; i++)
        {
            Material detail = list.get(i);
            cell = new PdfPCell(new ParagraphCn9(detail.getNo()));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            if (2 == type)
            {
                cell = new PdfPCell(new ParagraphCn9(detail.getCustomerModels()));
            }
            else
            {
                cell = new PdfPCell(new ParagraphCn9(detail.getName()));
            }
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            if (2 == type)
            {
                cell = new PdfPCell(new ParagraphCn9(detail.getDeliverySize()));
            }
            else
            {
                cell = new PdfPCell(new ParagraphCn9(detail.getSpecification()));
            }
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            cell = new PdfPCell(
                new ParagraphCn9(null == detail.getMaterialType() ? "" : detail.getMaterialType().getValue()));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9(detail.getMaterialKindStr()));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9(detail.getUnitStr()));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9(detail.getRemark()));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9(
                null == detail.getStocks() ? null : detail.getStocks().toString()));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            cell = new PdfPCell(new ParagraphCn9(detail.getSnapStrocks()));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
            
            cell = new PdfPCell(
                new ParagraphCn9(null == detail.getSencondStrocks() ? null : detail.getSencondStrocks().toString()));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);
        }
    }
    
    private void addTableFooter(PdfPTable bottomTable)
    {
        PdfPCell cell = null;
        cell = new PdfPCell(new ParagraphCn9("初盘人："));
        cell.setPaddingTop(10);
        cell.setPaddingBottom(10);
        cell.setColspan(9);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        bottomTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("复盘人："));
        cell.setPaddingTop(10);
        cell.setPaddingBottom(10);
        cell.setColspan(9);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        bottomTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("时间："));
        cell.setPaddingTop(10);
        cell.setPaddingBottom(10);
        cell.setColspan(9);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        bottomTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("时间："));
        cell.setPaddingTop(5);
        cell.setPaddingBottom(10);
        cell.setColspan(9);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        bottomTable.addCell(cell);
    }
    
    private void addTableFooterThree(PdfPTable bottomTable)
    {
        PdfPCell cell = null;
        cell = new PdfPCell(new ParagraphCn9("盘点人："));
        cell.setPaddingTop(10);
        cell.setPaddingBottom(10);
        cell.setColspan(6);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        bottomTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("计划复盘："));
        cell.setPaddingTop(10);
        cell.setPaddingBottom(10);
        cell.setColspan(6);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        bottomTable.addCell(cell);
        
        cell = new PdfPCell(new ParagraphCn9("财务抽盘："));
        cell.setPaddingTop(10);
        cell.setPaddingBottom(10);
        cell.setColspan(6);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        bottomTable.addCell(cell);
        
        /*
         * cell = new PdfPCell(new ParagraphCn9("时间：")); cell.setPaddingTop(5); cell.setPaddingBottom(10);
         * cell.setColspan(9); cell.setHorizontalAlignment(Element.ALIGN_CENTER); cell.setBorder(PdfPCell.NO_BORDER);
         * bottomTable.addCell(cell);
         */
    }
    
    /**
     * 打印WIP盘点单
     *
     * @param list
     * @param inventory
     * @param realPath
     * @param type
     * @param pageSize
     * @return
     */
    public Pdf getCopyPrintPDFThree(List<WipCopy> list, Inventory inventory, String realPath, int type,
        Integer pageSize)
    {
        // 查询要打印的成品采购信息
        Pdf pdf = new Pdf();
        ByteArrayOutputStream base = new ByteArrayOutputStream();
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        Inventory copyRec = inventoryDao.get(inventory);
        List<String> cateList = new ArrayList<>();
        Map<String, List<WipCopy>> egMap = getEgMap(list, cateList);
        Integer showFlag = findWipSetShowFag();
        List<WipCopy> tempCopyList = null;
        try
        {
            // 查询出分编号工艺的信息
            Rectangle rectPageSize = new Rectangle(PageSize.A4);
            Document document = new Document(rectPageSize, 10, 10, 20, 40);
            
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            Font title = new Font(bfChinese, 20, Font.BOLD);
            // Font cellTitle = new Font(bfChinese, 11, Font.BOLD);
            Font cellContent = new Font(bfChinese, 11, Font.NORMAL);
            
            ByteArrayOutputStream page1 = new ByteArrayOutputStream();
            PdfWriter writer = PdfWriter.getInstance(document, page1);
            writer.setPageEmpty(true);
            BaseFont baseFontChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            setFooter("", writer, baseFontChinese, 9, rectPageSize);
            document.open();
            
            // 分工序
            for (String cateName : cateList)
            {
                
                tempCopyList = egMap.get(cateName);
                if (CollectionUtils.isEmpty(tempCopyList))
                {
                    continue;
                }
                Paragraph docTitle = new Paragraph("月盘点表\n", title);
                docTitle.setAlignment(Element.ALIGN_CENTER);
                docTitle.setSpacingBefore(20);
                document.add(docTitle);
                
                PdfPTable table = new PdfPTable(1);
                table.setSplitLate(false); // 这页表格能放多少就放多少
                table.setTotalWidth(556f);
                table.setLockedWidth(true);
                table.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
                String snapshotDate = copyRec.getSnapshotDate();
                addThreeTitle(table, tempCopyList.get(0), snapshotDate, copyRec.getNo());
                document.add(table);
                
                // int size = 35;
                // int contentSize = 30;
                
                // int widths[] = { 12,12, 25, 21, 25, 25, 25, 15, 15 };
                PdfPTable baseTable = new PdfPTable(28);
                if (showFlag == 1)
                {
                    baseTable = new PdfPTable(32);
                }
                baseTable.setHeaderRows(2);
                baseTable.setWidthPercentage(100);
                baseTable.setSpacingBefore(10);
                // baseTable.setWidths(widths);
                
                PdfPCell baseTableCell = new PdfPCell(new Paragraph("序号", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setRowspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("生产编号", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(3);
                baseTableCell.setRowspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("订单数量", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(2);
                baseTableCell.setRowspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("投料数量", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(2);
                baseTableCell.setRowspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("工程SET长", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(2);
                baseTableCell.setRowspan(2);
                // baseTableCell.setFixedHeight(size*3);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("工程SET宽", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(2);
                baseTableCell.setRowspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("SET拼版", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(2);
                baseTableCell.setRowspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("A板SET数", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(2);
                baseTableCell.setRowspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("B板SET数", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(2);
                baseTableCell.setRowspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("结存总面积", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(2);
                baseTableCell.setRowspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                if (showFlag == 1)
                {
                    baseTableCell = new PdfPCell(new Paragraph("ERP结存", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(4);
                    // baseTableCell.setFixedHeight(size);
                    baseTable.addCell(baseTableCell);
                    
                }
                
                baseTableCell = new PdfPCell(new Paragraph("实盘数（PNL/PCS)", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(4);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("复盘/抽盘", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(4);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                if (showFlag == 1)
                {
                    baseTableCell = new PdfPCell(new Paragraph("A板", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    // baseTableCell.setFixedHeight(size);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph("B板", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    // baseTableCell.setFixedHeight(size);
                    baseTable.addCell(baseTableCell);
                    
                }
                
                baseTableCell = new PdfPCell(new Paragraph("A板", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("B板", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("A板", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                baseTableCell = new PdfPCell(new Paragraph("B板", cellContent));
                baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                baseTableCell.setColspan(2);
                // baseTableCell.setFixedHeight(size);
                baseTable.addCell(baseTableCell);
                
                WipCopy tempCopy = null;
                for (int i = 0; i < tempCopyList.size(); i++)
                {
                    tempCopy = tempCopyList.get(i);
                    baseTableCell = new PdfPCell(new Paragraph((i + 1) + "", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(1);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph(tempCopy.getCraftNo(), cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(3);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph(tempCopy.getOrderQty() + "", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph(getCellVal(tempCopy.getFeedQty()) + "", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph(getCellVal(tempCopy.getSetLength()) + "", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph(getCellVal(tempCopy.getSetWidth()) + "", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph(getCellVal(tempCopy.getQtySetT()) + "", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph(getCellVal(tempCopy.getQtySetA()) + "", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph(getCellVal(tempCopy.getQtySetB()) + "", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph(getCellVal(tempCopy.getFeedArea()) + "", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    baseTable.addCell(baseTableCell);
                    
                    if (showFlag == 1)
                    {
                        baseTableCell =
                            new PdfPCell(new Paragraph(tempCopy.getExcErpA() == null ? "" : tempCopy.getExcErpA() + "",
                                cellContent));
                        baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                        baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        baseTableCell.setColspan(2);
                        baseTable.addCell(baseTableCell);
                        
                        baseTableCell =
                            new PdfPCell(new Paragraph(tempCopy.getExcErpB() == null ? "" : tempCopy.getExcErpB() + "",
                                cellContent));
                        baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                        baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                        baseTableCell.setColspan(2);
                        baseTable.addCell(baseTableCell);
                    }
                    
                    baseTableCell = new PdfPCell(new Paragraph("", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph("", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph("", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    baseTable.addCell(baseTableCell);
                    
                    baseTableCell = new PdfPCell(new Paragraph("", cellContent));
                    baseTableCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    baseTableCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    baseTableCell.setColspan(2);
                    baseTable.addCell(baseTableCell);
                    // baseTableCell.setFixedHeight(size);
                }
                
                document.add(baseTable);
                
                PdfPTable footerTable = new PdfPTable(18);
                footerTable.setSplitLate(false); // 这页表格能放多少就放多少
                footerTable.setTotalWidth(556f);
                footerTable.setLockedWidth(true);
                footerTable.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
                addTableFooterThree(footerTable);
                document.add(footerTable);
                
                document.newPage();
            }
            
            document.close();
            
            output = page1;
            base = output;
            pdf = new Pdf();
            pdf.setOutPut(base);
            pdf.setShowName("盘点单");
            
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return pdf;
    }
    
    private Object getCellVal(Object obj)
    {
        if (null == obj)
        {
            return "";
        }
        else
        {
            return obj;
        }
    }
    
    public Map<String, List<WipCopy>> getEgMap(List<WipCopy> list, List<String> cateNameList)
    {
        Map<String, List<WipCopy>> result = new HashMap<String, List<WipCopy>>();
        List<WipCopy> tempList = null;
        for (WipCopy copy : list)
        {
            if (result.containsKey(copy.getCateName()))
            {
                result.get(copy.getCateName()).add(copy);
            }
            else
            {
                tempList = new ArrayList<WipCopy>();
                tempList.add(copy);
                result.put(copy.getCateName(), tempList);
                cateNameList.add(copy.getCateName());
            }
        }
        return result;
    }
    
    public List<WipCopy> findwipCopyDetailById(Inventory inventory)
    {
        return inventoryDao.findCopyWIPDetailById(inventory);
    }
    
    public Page<WipCopy> findWipCopyPage(Page<WipCopy> page, WipCopy copy)
    {
        copy.setPage(page);
        List<WipCopy> wipCopyList = inventoryDao.findWipCopyPage(copy);
        page.setList(wipCopyList);
        return page;
    }
    
    public List<WipCopy> findWipCopyDetail(WipCopy copy)
    {
        return inventoryDao.findWipCopyPage(copy);
    }
    
    @Transactional(readOnly = false)
    public String editWipCopyStock(WipCopy copy)
    {
        if (null != copy.getQtyPnlASecond())
        {
            copy.setSnapQtyPnlA(copy.getQtyPnlASecond());
        }
        else if (null != copy.getQtyPnlAFirst())
        {
            copy.setSnapQtyPnlA(copy.getQtyPnlAFirst());
        }
        else
        {
            copy.setSnapQtyPnlA(null);
        }
        
        if (null != copy.getQtyPnlBSecond())
        {
            copy.setSnapQtyPnlB(copy.getQtyPnlBSecond());
        }
        else if (null != copy.getQtyPnlBFirst())
        {
            copy.setSnapQtyPnlB(copy.getQtyPnlBFirst());
        }
        else
        {
            copy.setSnapQtyPnlB(null);
        }
        copy.preUpdate();
        inventoryDao.editWipCopyStock(copy);
        return "success";
    }
    
    @Transactional(readOnly = false)
    public List<WipCopy> wipDataHandle(List<List<Object>> listob, String recRecordId, ImportReturnVo vo)
    {
        List<WipCopy> redList = Collections.emptyList();
        // 查出所有的盘点明细，物料no， recordId,companyId
        Inventory inventory = new Inventory();
        inventory.setRecordId(recRecordId);
        inventory.setCompany(UserUtils.getUser().getCompany());
        List<WipCopy> copyDetailList = inventoryDao.findCopyWIPDetailById(inventory);
        List<WipCopy> updateSnapStockList = new ArrayList<WipCopy>();
        if (CollectionUtils.isEmpty(copyDetailList))
        {
            return redList;
        }
        // 循环明细，对比更新录入盘点的数量，放入到更新的list
        Map<String, WipCopy> inData = new HashMap<String, WipCopy>();
        if (Collections3.isNotEmpty(listob))
        {
            String cateNameTitle = "工序";
            String craftNoTitile = "生产编号";
            String orderQtyTitile = "订单数量";
            String firstA = "初盘A板";
            String firstB = "初盘B板";
            String firstA2 = "盘点A板";
            String firstB2 = "盘点B板";
            String secondA = "复盘A板";
            String secondB = "复盘B板";
            Integer cateNameNum = null;
            Integer craftNoNum = null;
            Integer orderQtyNum = null;
            Integer firstANum = null;
            Integer firstBNum = null;
            Integer secondANum = null;
            Integer secondBNum = null;
            Integer maxCellNum = 0;
            WipCopy copyTemp = null;
            for (int i = 0; i < listob.size(); i++)
            {
                List<Object> lo = listob.get(i);
                // 先读取物料编号和实盘库存所在的列
                if (null == cateNameNum || null == craftNoNum)
                {
                    for (int n = 0; n < lo.size(); n++)
                    {
                        String content = lo.get(n) == null ? "" : lo.get(n).toString();
                        if (null == cateNameNum && (content.contains(cateNameTitle)))
                        {
                            cateNameNum = n;
                            maxCellNum = maxCellNum > n ? maxCellNum : n;
                        }
                        if (null == craftNoNum && craftNoTitile.equals(content.trim()))
                        {
                            craftNoNum = n;
                            maxCellNum = maxCellNum > n ? maxCellNum : n;
                        }
                        if (null == orderQtyNum && orderQtyTitile.equals(content.trim()))
                        {
                            orderQtyNum = n;
                            maxCellNum = maxCellNum > n ? maxCellNum : n;
                        }
                        if (null == firstANum && (firstA.equals(content.trim()) || firstA2.equals(content.trim())))
                        {
                            firstANum = n;
                            maxCellNum = maxCellNum > n ? maxCellNum : n;
                        }
                        
                        if (null == firstBNum && (firstB.equals(content.trim()) || firstB2.equals(content.trim())))
                        {
                            firstBNum = n;
                            maxCellNum = maxCellNum > n ? maxCellNum : n;
                        }
                        
                        if (null == secondANum && secondA.equals(content.trim()))
                        {
                            secondANum = n;
                            maxCellNum = maxCellNum > n ? maxCellNum : n;
                        }
                        
                        if (null == secondBNum && secondB.equals(content.trim()))
                        {
                            secondBNum = n;
                            maxCellNum = maxCellNum > n ? maxCellNum : n;
                        }
                    }
                }
                else
                {
                    if (lo.size() > maxCellNum && !StringUtils.isEmpty(lo.get(cateNameNum))
                        && (!StringUtils.isEmpty(lo.get(craftNoNum))))
                    {
                        copyTemp = new WipCopy();
                        String cateName = lo.get(cateNameNum).toString().trim();
                        String craftNo = lo.get(craftNoNum).toString().trim();
                        String ordetQty = lo.get(orderQtyNum).toString().trim();
                        copyTemp.setCateName(cateName);
                        copyTemp.setCraftNo(craftNo);
                        String wipKey = null;
                        if(com.kyb.pcberp.common.utils.StringUtils.isNotBlank(ordetQty) && !orderQtyTitile.equals(ordetQty))
                        {
                            copyTemp.setOrderQty(Integer.valueOf(ordetQty));
                            wipKey = cateName + ";" + craftNo+ ";" + ordetQty;
                        }
                        else
                        {
                            wipKey = cateName + ";" + craftNo;
                        }
                        if (null != firstANum && null != lo.get(firstANum)
                            && !StringUtils.isEmpty(lo.get(firstANum).toString().trim())
                            && com.kyb.pcberp.common.utils.StringUtils.isNumber(lo.get(firstANum).toString()))
                        {
                            copyTemp.setQtyPnlAFirst(new BigDecimal(lo.get(firstANum).toString()).intValue());
                            
                        }
                        if (null != firstBNum && null != lo.get(firstBNum)
                            && !StringUtils.isEmpty(lo.get(firstBNum).toString().trim())
                            && com.kyb.pcberp.common.utils.StringUtils.isNumber(lo.get(firstBNum).toString()))
                        {
                            copyTemp.setQtyPnlBFirst(new BigDecimal(lo.get(firstBNum).toString()).intValue());
                            
                        }
                        if (null != secondANum && null != lo.get(secondANum)
                            && !StringUtils.isEmpty(lo.get(secondANum).toString().trim())
                            && com.kyb.pcberp.common.utils.StringUtils.isNumber(lo.get(secondANum).toString()))
                        {
                            copyTemp.setQtyPnlASecond(new BigDecimal(lo.get(secondANum).toString()).intValue());
                            
                        }
                        if (null != secondBNum && null != lo.get(secondBNum)
                            && !StringUtils.isEmpty(lo.get(secondBNum).toString().trim())
                            && com.kyb.pcberp.common.utils.StringUtils.isNumber(lo.get(secondBNum).toString()))
                        {
                            copyTemp.setQtyPnlBSecond(new BigDecimal(lo.get(secondBNum).toString()).intValue());
                            
                        }
                        // 相同工序、编号 累加
                        if (inData.containsKey(wipKey))
                        {
                            WipCopy mapCopy = inData.get(wipKey);
                            addQty(copyTemp, mapCopy);
                        }
                        else
                        {
                            inData.put(wipKey, copyTemp);
                        }
                        
                    }
                }
            }
            if (inData.size() == 0)
            {
                return redList;
            }
            if (inData.size() > 0)
            {
                for (String mNo : inData.keySet())
                {
                    copyTemp = inData.get(mNo);
                    // 检查 初盘A板 初盘B板 复盘A板 复盘B板 同时为空，不处理
                    if (checkDataIsNull(copyTemp))
                    {
                        continue;
                    }
                    for (WipCopy mr : copyDetailList)
                    {
                        //
                        if (mNo.equals(mr.getCateName().trim() + ";" + mr.getCraftNo().trim() + ";" + mr.getOrderQty()))
                        {
                            // 处理复盘数据
                            if (null != copyTemp.getQtyPnlASecond())
                            {
                                // 存在初盘数据
                                if (null != copyTemp.getQtyPnlAFirst() || null != mr.getQtyPnlAFirst())
                                {
                                    mr.setQtyPnlASecond(copyTemp.getQtyPnlASecond());
                                    mr.setSnapQtyPnlA(copyTemp.getQtyPnlASecond());
                                    if (null != copyTemp.getQtyPnlAFirst())
                                    {
                                        mr.setQtyPnlAFirst(copyTemp.getQtyPnlAFirst());
                                    }
                                }
                            }
                            else if (null != copyTemp.getQtyPnlAFirst())
                            {
                                mr.setQtyPnlAFirst(copyTemp.getQtyPnlAFirst());
                                // 如果复盘历史数据为空
                                if (null == mr.getQtyPnlASecond())
                                {
                                    mr.setSnapQtyPnlA(copyTemp.getQtyPnlAFirst());
                                }
                            }
                            
                            if (null != copyTemp.getQtyPnlBSecond())
                            {
                                // 存在初盘数据
                                if (null != copyTemp.getQtyPnlBFirst() || null != mr.getQtyPnlBFirst())
                                {
                                    mr.setQtyPnlBSecond(copyTemp.getQtyPnlBSecond());
                                    mr.setSnapQtyPnlB(copyTemp.getQtyPnlBSecond());
                                    if (null != copyTemp.getQtyPnlBFirst())
                                    {
                                        mr.setQtyPnlBFirst(copyTemp.getQtyPnlBFirst());
                                    }
                                }
                            }
                            else if (null != copyTemp.getQtyPnlBFirst())
                            {
                                mr.setQtyPnlBFirst(copyTemp.getQtyPnlBFirst());
                                // 如果复盘历史数据为空
                                if (null == mr.getQtyPnlBSecond())
                                {
                                    mr.setSnapQtyPnlB(copyTemp.getQtyPnlBFirst());
                                }
                            }
                            mr.preUpdate();
                            updateSnapStockList.add(mr);
                            break;
                        }
                    }
                    // 导入的excle中存在的物流编号 ，盘点明细中没找到，盘点量不为零；
                    /*
                     * if (!isExist && BigDecimal.ZERO.compareTo(inData.get(mNo)) != 0) { noDetailMaterialNos =
                     * (noDetailMaterialNos.length() > 0 ? (noDetailMaterialNos.length() + ",") : "") +
                     * getMaterialNo(12, "YLBM", mNo, "0"); }
                     */
                }
            }
            
            // 验证编号在数据库存在后，返回页面
            /*
             * if (noDetailMaterialNos.length() > 0) { redList = materialDao.findListByNos(noDetailMaterialNos,
             * UserUtils.getUser().getCompany().getRecordId());
             *
             * }
             */
        }
        
        // 批量更新盘点数量
        if (updateSnapStockList.size() > 0)
        {
            inventoryDao.batchUpdateWipSnopStock(updateSnapStockList);
            vo.setInsertNum(updateSnapStockList.size());
        }
        return redList;
    }
    
    private boolean checkDataIsNull(WipCopy copyTemp)
    {
        if (null == copyTemp)
        {
            return true;
        }
        else if (copyTemp.getQtyPnlAFirst() == null && copyTemp.getQtyPnlASecond() == null
            && copyTemp.getQtyPnlBFirst() == null && copyTemp.getQtyPnlBSecond() == null)
        {
            return true;
        }
        return false;
    }
    
    /**
     * @param newObj 加数
     * @param destObj 累计目标
     */
    private void addQty(WipCopy newObj, WipCopy destObj)
    {
        destObj.setQtyPnlAFirst(add(destObj.getQtyPnlAFirst(), newObj.getQtyPnlAFirst()));
        destObj.setQtyPnlBFirst(add(destObj.getQtyPnlBFirst(), newObj.getQtyPnlBFirst()));
        destObj.setQtyPnlASecond(add(destObj.getQtyPnlASecond(), newObj.getQtyPnlASecond()));
        destObj.setQtyPnlBSecond(add(destObj.getQtyPnlBSecond(), newObj.getQtyPnlBSecond()));
        
    }
    
    private Integer add(Integer one, Integer two)
    {
        if (one == null && two == null)
        {
            return null;
        }
        return (one == null ? 0 : one) + (two == null ? 0 : two);
    }
    
    public Page<WipCopy> getWipSnapDiffList(Page<WipCopy> qpage, WipCopy copy)
    {
        copy.setPage(qpage);
        List<WipCopy> copyIds = inventoryDao.getWipSnapDiffIds(copy);
        List<WipCopy> copyList = Collections.emptyList();
        List<WipCopy> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(copyIds))
        {
            String ids = "";
            for (WipCopy wc : copyIds)
            {
                if ("".equals(ids))
                {
                    ids = wc.getRecordId();
                }
                else
                {
                    ids = ids + "," + wc.getRecordId();
                }
            }
            if (!StringUtils.isEmpty(copy.getCopyRecId()))
            {
                copy.setPage(null);
                copy.setRecordId(ids);
                copyList = inventoryDao.getWipSnapDiffList(copy);
            }
            if (!CollectionUtils.isEmpty(copyList))
            {
                // 排序
                for (WipCopy ss : copyIds)
                {
                    for (WipCopy info : copyList)
                    {
                        if (ss.getRecordId().equals(info.getRecordId()))
                        {
                            resultList.add(info);
                            break;
                        }
                    }
                }
                int sortNum = 1;
                for (WipCopy wc : resultList)
                {
                    wc.setSortNum(sortNum++);
                }
            }
        }
        // 设置对应的出入库
        qpage.setList(resultList);
        return qpage;
    }
    
    @Transactional(readOnly = false)
    public Map<String, Object> saveBatchInfo(WipCopy copy)
    {
        Map<String, Object> result = new HashMap<String, Object>();
        if (null != copy && !CollectionUtils.isEmpty(copy.getCopyInfoList()))
        {
            int snapStocksA = copy.getSnapQtyPnlA() == null ? 0 : copy.getSnapQtyPnlA();
            int snapStocksB = copy.getSnapQtyPnlB() == null ? 0 : copy.getSnapQtyPnlB();
            int countA = 0;
            int countB = 0;
            // String keyWork = "";
            for (WipCopyInfo info : copy.getCopyInfoList())
            {
                if (null != info.getSnapQtyPnlA())
                {
                    countA = countA + info.getSnapQtyPnlA();
                }
                else
                {
                    info.setSnapQtyPnlA(0);
                }
                if (null != info.getSnapQtyPnlB())
                {
                    countB = countB + info.getSnapQtyPnlB();
                }
                else
                {
                    info.setSnapQtyPnlB(0);
                }
                
                if (info.getSnapQtyPnlA() < 0 || info.getSnapQtyPnlB() < 0)
                {
                    result.put("result", "fail");
                    result.put("message", "盘点数不能为负数");
                    
                    return result;
                }
                info.preUpdate();
            }
            if (countA != snapStocksA || countB != snapStocksB)
            {
                result.put("result", "fail");
                result.put("message", "物料实盘库存要等于库位盘点数之和");
                return result;
            }
            inventoryDao.updateCopyInfo(copy.getCopyInfoList());
        }
        result.put("result", "successs");
        return result;
    }
    
    public List<WipCopy> getWipSnapDiffList(WipCopy copy)
    {
        return inventoryDao.getWipSnapDiffList(copy);
    }
    
    public List<String> getCateNameList(Inventory inventory)
    {
        return inventoryDao.getCateNameList(inventory);
    }
    
    public List<WipCopyInfo> getHandleInfo(Inventory inventory)
    {
        return inventoryDao.getHandleInfo(inventory);
    }
    
    @Transactional(readOnly = false)
    public String editDiffHandleInfo(WipCopyInfo copyInfo)
    {
        copyInfo.preUpdate();
        inventoryDao.editDiffHandleInfo(copyInfo);
        return "success";
    }
    
    public String getAuditRole(Inventory inventory)
    {
        return inventoryDao.getAuditRole(inventory);
    }
    
    @Transactional(readOnly = false)
    public String saveWipSet(WipCopy wipCopy)
    {
        WipCopyInfo showFlagInfo = new WipCopyInfo();
        showFlagInfo.setCateName("showFlag");
        if (null == wipCopy.getShowFlag())
        {
            showFlagInfo.setUnitType(1);
            ;
        }
        else
        {
            showFlagInfo.setUnitType(wipCopy.getShowFlag());
            ;
        }
        List<WipCopyInfo> infos = wipCopy.getCopyInfoList();
        if (CollectionUtils.isEmpty(infos))
        {
            infos = new ArrayList<WipCopyInfo>();
        }
        infos.add(showFlagInfo);
        // 查询已经有的; 比较工序名称，没有，新增；有，比较单位类型是否相等，不相等更新
        List<WipCopyInfo> sets = inventoryDao.getWipSet(wipCopy);
        List<WipCopyInfo> insertList = new ArrayList<>();
        List<WipCopyInfo> updateList = new ArrayList<>();
        for (WipCopyInfo info : infos)
        {
            boolean isNew = true;
            for (WipCopyInfo s : sets)
            {
                if (info.getCateName().equals(s.getCateName()))
                {
                    s.setUnitType(info.getUnitType() == null ? 1 : info.getUnitType());
                    s.preUpdate();
                    updateList.add(s);
                    isNew = false;
                    break;
                }
            }
            if (isNew)
            {
                if (info.getUnitType() == null)
                {
                    info.setUnitType(1);
                }
                info.preInsert();
                info.setCompany(UserUtils.getUser().getCompany());
                insertList.add(info);
            }
        }
        if (!CollectionUtils.isEmpty(insertList))
        {
            inventoryDao.batchInsertWipSet(insertList);
        }
        if (!CollectionUtils.isEmpty(updateList))
        {
            inventoryDao.batchUpdateWipSet(updateList);
        }
        return "sucess";
    }
    
    public List<WipCopyInfo> getWipSet(Inventory inventory)
    {
        Notification notification = new Notification();
        notification.setCompany(UserUtils.getUser().getCompany());
        notification.setStatus(TypeKey.SL_NOTIFICATION_STATUS_FEED.toString());
        notification.setStatus1(TypeKey.SL_NOTIFICATION_STATUS_SUPPLEMENTARYFOOD.toString());
        ParameterSet ps = new ParameterSet();
        ps.setCompany(notification.getCompany());
        ps.setJianPin("takeOrHandWip");
        ParameterSet parameterSet = parameterSetDao.getParameterSetByCompanyAndjianPin(ps);
        if (null != parameterSet)
        {
            notification.setTakeHandWip(parameterSet.getStart());
        }
        else
        {
            notification.setTakeHandWip("0");
        }
        List<String> cateNames = produceRecordDao.getProcessNameList(notification);
        WipCopy wipCopy = new WipCopy();
        wipCopy.setCompany(UserUtils.getUser().getCompany());
        List<WipCopyInfo> sets = inventoryDao.getWipSet(wipCopy);
        List<WipCopyInfo> resultList = new ArrayList<>();
        WipCopyInfo temp = null;
        for (String cateName : cateNames)
        {
            if(com.kyb.pcberp.common.utils.StringUtils.isBlank(cateName))
            {
                continue;
            }
            boolean isExist = false;
            for (WipCopyInfo s : sets)
            {
                if (com.kyb.pcberp.common.utils.StringUtils.isBlank(cateName) || null == s)
                {
                    continue;
                }
                if (cateName.equals(s.getCateName()))
                {
                    resultList.add(s);
                    isExist = true;
                }
            }
            if (!isExist)
            {
                temp = new WipCopyInfo();
                temp.setCateName(cateName);
                if ("手V".equals(cateName) || "啤".equals(cateName) || "抗氧化".equals(cateName) || "FQC".equals(cateName)
                    || "终检入库".equals(cateName))
                {
                    temp.setUnitType(1);
                }
                else
                {
                    temp.setUnitType(2);
                }
                resultList.add(temp);
            }
        }
        return resultList;
    }
    
    public Integer findWipSetShowFag()
    {
        WipCopy wipCopy = new WipCopy();
        wipCopy.setCompany(UserUtils.getUser().getCompany());
        wipCopy.setCateName("showFlag");
        List<WipCopyInfo> reuslt = inventoryDao.getWipSet(wipCopy);
        if (!CollectionUtils.isEmpty(reuslt) && null != reuslt.get(0).getUnitType())
        {
            return reuslt.get(0).getUnitType();
        }
        else
        {
            return 2;// 默认返回2,不显示账面数
        }
        
    }
    
    public List<Material> getNewProductList(Inventory inventory)
    {
        return inventoryDao.getNewProductList(inventory);
    }
    
    @Transactional(readOnly = false)
    public Map<String, Object> addCopyDetail(Material material)
    {
        Map<String, Object> result = new HashMap<>();
        // 检查数据库中是否已经存在该物料
        if (StringUtils.isEmpty(material.getMaterialCopyId()))
        {
            result.put("result", "fail");
            result.put("message", "请刷新后重试");
        }
        Material m = materialDao.get(material);
        // 检查数据库中是否已经存在该物料
        if (m == null || StringUtils.isEmpty(m.getRecordId()))
        {
            result.put("result", "fail");
            result.put("message", "请刷新后重试");
        }
        Material oldM = inventoryDao.findCopyMaterial(material);
        if (oldM != null)
        {
            result.put("result", "fail");
            result.put("message", "该物料已经存在,请刷新后确认");
        }
        if (m.getMaterialKind() != null && m.getMaterialKind() == 100701)
        {
            RawStockUtil rsu = new RawStockUtil();
            try
            {
                List<Material> querList = new ArrayList<>();
                querList.add(m);
                rsu.getMaterialList(querList,
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(material.getSnapshotDate()));
            }
            catch (ParseException e)
            {
                e.printStackTrace();
            }
        }
        else
        {
            m.setStocks(material.getStocks());
        }
        
        m.setSnapshotDate(material.getSnapshotDate());
        m.setMaterialCopyId(material.getMaterialCopyId());
        if (m.getMaterialType() != null && !StringUtils.isEmpty(m.getMaterialType().getRecordId())
            && m.getMaterialTypeId() == null)
        {
            m.setMaterialTypeId(Long.parseLong(m.getMaterialType().getRecordId()));
        }
        if (m.getStorehouse() != null && !StringUtils.isEmpty(m.getStorehouse().getRecordId())
            && m.getStoreHouseId() == null)
        {
            m.setStoreHouseId(Integer.parseInt(m.getStorehouse().getRecordId()));
        }
        if (m.getUnit() == null)
        {
            m.setUnit(new DictValue());
        }
        
        m.preInsert();
        m.setCompany(UserUtils.getUser().getCompany());
        inventoryDao.addCopyMaterial(m);
        result.put("result", "successs");
        return result;
    }
    
    public List<WipCopy> getAllCateAreaInfo(Inventory inventory)
    {
        List<WipCopy> result = new ArrayList<>();
        List<WipCopy> wipCopyList = inventoryDao.findCopyWIPDetailById(inventory);
        if (!CollectionUtils.isEmpty(wipCopyList))
        {
            Map<String, WipCopy> cateAreatotal = new HashMap<>();
            WipCopy areaCopy = null;
            BigDecimal snapArea = null;
            BigDecimal pcsArea = null;
            for (WipCopy copy : wipCopyList)
            {
                snapArea = copy.getSnapArea() == null ? BigDecimal.ZERO : copy.getSnapArea();
                pcsArea = copy.getPcsArea() == null ? BigDecimal.ZERO : copy.getPcsArea();
                if (cateAreatotal.containsKey(copy.getCateName()))
                {
                    cateAreatotal.get(copy.getCateName())
                        .setSnapArea(cateAreatotal.get(copy.getCateName()).getSnapArea().add(snapArea));
                    cateAreatotal.get(copy.getCateName())
                        .setPcsArea(cateAreatotal.get(copy.getCateName()).getPcsArea().add(pcsArea));
                }
                else
                {
                    areaCopy = new WipCopy();
                    areaCopy.setCateName(copy.getCateName());
                    areaCopy.setCateId(copy.getCateId());
                    areaCopy.setSnapArea(snapArea);
                    areaCopy.setPcsArea(pcsArea);
                    cateAreatotal.put(copy.getCateName(), areaCopy);
                }
            }
            for (String key : cateAreatotal.keySet())
            {
                result.add(cateAreatotal.get(key));
            }
        }
        return result;
    }
}
