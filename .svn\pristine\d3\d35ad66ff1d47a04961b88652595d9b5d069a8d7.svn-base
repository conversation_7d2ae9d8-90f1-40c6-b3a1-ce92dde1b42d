package com.kyb.pcberp.modules.report.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.persistence.DataEntity;

public class ReportCapacityVersion extends DataEntity<ReportCapacityVersion>
{
    
    private static final long serialVersionUID = 1L;
    
    private String companyId;
    
    // 版本时间戳
    private Date versionDate;
    
    private Integer sortNum;

    private Integer useFlag;

    public String getCompanyId()
    {
        return companyId;
    }
    
    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getVersionDate()
    {
        return versionDate;
    }
    
    public void setVersionDate(Date versionDate)
    {
        this.versionDate = versionDate;
    }
    
    public Integer getSortNum()
    {
        return sortNum;
    }
    
    public void setSortNum(Integer sortNum)
    {
        this.sortNum = sortNum;
    }

    public Integer getUseFlag() {
        return useFlag;
    }

    public void setUseFlag(Integer useFlag) {
        this.useFlag = useFlag;
    }
}
