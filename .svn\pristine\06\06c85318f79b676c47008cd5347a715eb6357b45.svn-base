<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isErrorPage="true"%>
<div>
	<div class="row pt-2 pb-2 alert-light border-bottom">
		<div class="col text-center font-weight-bolder">
			<span>集团内部资金调拨审批({{audit.no}})</span>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col text-left font-weight-bold text-primary">
			提交人：{{audit.name}}
		</div>
		<div class="col text-right">
			{{audit.createDateStr}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">
			来源：{{audit.departmentName}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col-6">
			付款金额：{{audit.expensePrice}}
		</div>
		币种：
		<div class="col">
			<select class="custom-select form-control h-32px" v-model="audit.childType" disabled="disabled">
				<option value="1">人民币</option>
				<option value="2">港币</option>
				<option value="3">美金</option>
			</select>
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col-5">
			付款方式：{{audit.payMethod}}
		</div>
		<div class="col" v-if="audit.startTime">
			&emsp;&emsp;支付日期：{{audit.startTime.split(" ")[0]}}
		</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">支付对象：{{audit.payObject}}</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">开户行：{{audit.bank}}</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">银行账号：{{audit.bankAccount}}</div>
	</div>
	<div class="row alert-light pt-2 pb-2 align-items-center">
		<div class="col">付款原因：{{audit.auditResult}}</div>
	</div>
</div>