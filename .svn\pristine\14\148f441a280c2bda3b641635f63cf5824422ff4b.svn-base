kybApp.controller('materialDefineCtrl', ['$rootScope', '$scope', 'upida', '$timeout', 'CommonUtil','BaseUtil','$http',function($rootScope, $scope, upida, $timeout,CommonUtil,BaseUtil,$http) {
    $scope.$on('$viewContentLoaded', function() {
        MainCtrl.initAjax();
    });
    const vm = this;
    vm.clicks = true;
    // tabs控制
    vm.tabs = {
        viewForm: {active:true},
        editForm: {active:false, show: false},
        viewSpecForm: {active:false},
        editSpecForm: {active:false, show: false}
    };
    // 权限
    vm.right = {};

    // 分页数据
    vm.page = {};
    vm.materialKinds=[{"value":100701,"name":"原料"}];//物料种类的下拉值
    // 显示数据大小
    vm.page.pageSizeOptions = [5, 10, 30, 50];
    // 字典项分页数据
    vm.page.data = {}; // 指令输入数据（包含 data=>page 和 condition =>条件）
    vm.page.pageSize = 10;
    vm.page.pageNo = 1;
    vm.page.url = "stock/material/page";
    vm.page.condition = []; // 条件
    
    // 查询条件参数
    vm.query = {};
    vm.query.no = {};
    vm.query.no.name = "no";
    vm.query.no.value = "";
    vm.query.name = {};
    vm.query.name.name = "name";
    vm.query.name.value = "";
    vm.query.specification = "";
    vm.query.materialKind = {};
    vm.query.materialKind.name = "materialKind";
    vm.query.materialKind.value = "100701";
    vm.query.materialType = {};
    vm.query.materialType.name = "materialType";
    vm.query.materialType.value = "";
    vm.query.status = {};
    vm.query.status.name = "status";
    vm.query.status.value = "1";
    vm.query.source = "2";
    vm.query.storehouse = {};
    vm.query.storehouse.name = "storehouse";
    vm.query.storehouse.value = "";
    vm.query.sort = {};
    vm.query.sort.name = "orderBy";
    vm.query.sort.value = "createdDate DESC";
    // 物料编辑操作对象
    vm.material = {};
    // 删除主键
    vm.delRecordId= -1;
    // 修改标题
    vm.editTitle = "";
    //编辑还是新增
    vm.edit = false;
    // 角色
    vm.roles = [];

    // 显示编辑物料
    vm.showEditForm = function () {
        vm.tabs.editForm.show = true;
        vm.tabs.editForm.active = true;
    };
    
    vm.setFormScope= function(scope){
        vm.formScope = scope;
    };
    // 物料详情隐藏编辑
    vm.hideEditForm = function () {
        vm.tabs.editForm.show = false;
        vm.formScope.material_form.$setPristine();
        vm.material.lowerLimit="";
        $timeout(function(){
            vm.tabs.viewForm.active = true;
        });
    };
    
    // 物料分页按钮单击处理
    vm.doPage = function(page, pageSize, total){
        vm.page.pageNo = page;
        vm.page.pageSize = pageSize;
        vm.init(page, pageSize, vm.page.condition, vm.page.url);
    };
    // 默认排序按时间排序排序处理
    vm.sort = {};
    vm.sort.no = {both: true, desc: false, asc: false};
    vm.sort.name = {both: true, desc: false, asc: false};
    vm.sort.customerNo = {both: true, desc: false, asc: false};
    vm.sort.lowerLimit = {both: true, desc: false, asc: false};
    vm.sort.stocks = {both: true, desc: false, asc: false};
    vm.sort.lowPrice = {both: true, desc: false, asc: false};
    vm.sort.createdDate = {both: false, desc: true, asc: false};
    vm.sortClick = function(col){
        vm.sort[col].both = false;
        if (!vm.sort[col].desc && !vm.sort[col].asc){
            vm.sort[col].asc = true;
        }else {
            if (vm.sort[col].asc){
                vm.sort[col].desc = true;
                vm.sort[col].asc = false;
            }else {
                vm.sort[col].desc = false;
                vm.sort[col].asc = true;
            }
        }

        for(var p in vm.sort){
            if (p !== col){
                vm.sort[p].desc = false;
                vm.sort[p].asc = false;
                vm.sort[p].both = true;
            }
        }

        vm.query.sort.value = vm.sort[col].asc ? col + " ASC" :  col + " DESC";
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    };

    vm.init = function(no, size, condition, url){
    	MainCtrl.blockUI({
        	animate: true
        });
        // 请求数据
        const reqData = {};
        reqData.pageNo = no;
        reqData.pageSize = size;
        condition.push({
            name: vm.query.sort.name,
            value: vm.query.sort.value
        });
        // 设置过滤条件
        if (condition.length > 0)
        {
            angular.forEach(condition, function(p){
                reqData[p.name] = p.value;
            });
        }
        upida.post(url, reqData).then(function (result) {
            let data = {};
            // 如果结果为空
            if (typeof result === 'undefined' || typeof result.list === 'undefined')
            {
                data.pageNo = 1;
                data.pageSize = 10;
                data.list = [];
                data.startCount = 0;
                data.endCount = 0;
            } else {
                data = result;
                // 计算开始数
                data.startCount = (data.pageNo - 1) * data.pageSize + 1;
                // 计算结束数
                data.endCount = (data.pageNo - 1) * data.pageSize + data.list.length;
            }
            vm.page.data = data;
            //进度条关闭
			MainCtrl.unblockUI();
        });
    };

    // 页面显示数量改变
    vm.pageSizeChange = function(){
        vm.init(1, vm.page.pageSize, vm.page.condition, vm.page.url);
    };

    // 查询数据
    vm.doQuery = function(){
        // 设置查询条件
        const condition = [];
        if (vm.query.no.value !== "")
        {
            condition.push({
                name: vm.query.no.name,
                value: vm.query.no.value
            });
        }
        if (vm.query.specification !== "")
        {
            condition.push({
                name: "specification",
                value: vm.query.specification
            });
        }
        if (vm.query.name.value !== "")
        {
            condition.push({
                name: vm.query.name.name,
                value: vm.query.name.value
            });
        }
        if (vm.query.materialKind.value !== "")
        {
            condition.push({
                name: vm.query.materialKind.name,
                value: vm.query.materialKind.value
            });
        }
        if (vm.query.materialType.value !== "")
        {
            condition.push({
                name: vm.query.materialType.name,
                value: vm.query.materialType.value
            });
        }
        if (vm.query.status.value !== "")
        {
            condition.push({
                name: vm.query.status.name,
                value: vm.query.status.value
            });
        }
	    if(vm.query.storehouse.value != "")
	    {
	        const storehouse = {};
	    	storehouse.name = vm.query.storehouse.value;
	    	condition.push({
				name: 'storehouse',
				value: storehouse
			});
	    }
        if (vm.query.source !== "")
        {
            condition.push({
                name: 'source',
                value: vm.query.source
            });
        }
        if (vm.query.boardSupermarketStatus !== "")
        {
            condition.push({
                name: 'boardSupermarketStatus',
                value: vm.query.boardSupermarketStatus
            });
        }
        vm.page.pageNo = 1;
        vm.page.condition = condition;
        // 查询数据
        vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
    };

    // 单击修改
    vm.modMaterial = function(index){
    	vm.edit = true;
        vm.editTitle = vm.right.edit ? "修改" : "查看";
        vm.focus += 1;
        vm.material = angular.copy(vm.page.data.list[index]);
        vm.productTypeId = vm.material.productTypeId;
        vm.icloudCompanyId = vm.material.icloudCompanyId;
        if (vm.material.materialSpecList){
            for (let i=0;i<vm.material.materialSpecList.length;i++){
                vm.material.materialSpecList[i].valueList = [];
                if (vm.material.materialSpecList[i].valDefinition == 2){
                    if (vm.material.materialSpecList[i].dictItem && vm.material.materialSpecList[i].dictItem.values && vm.material.materialSpecList[i].value) {
                        vm.material.materialSpecList[i].valueList = vm.material.materialSpecList[i].value.split(",");
                    }
                }
            }
        }
        vm.stockPlaceIds = [];
        if(vm.material.stockPlaceId)
        {
            var stockPlaceIds = vm.material.stockPlaceId.split(',');
            angular.forEach(stockPlaceIds,function(p){
                let flag = true;
                for(let id of vm.stockPlaceIds)
                {
                    if(id == p)
                    {
                        flag = false;
                        break;
                    }
                }
                if(flag)
                {
                    vm.stockPlaceIds.push(p);
                }
            });
        }
        if(vm.material.storehouse)
        {
            vm.loadMaterialPlace(vm.material.storehouse);
        }
        vm.dealMaterialSpe();
        vm.showEditForm();
    };
    
    // 单击添加
    vm.addMaterial = function(){
    	vm.edit = false;
        vm.material = {};
        vm.stockPlaceIds = [];
        vm.material.createdDate = Date.parse(new Date());
        vm.material.applicanter = BaseUtil.getUser();
        vm.material.lowerLimit = 1;
        vm.material.stocks = 0.000;
        vm.focus += 1;
        vm.show = false; //隐藏统计关键字
        vm.editTitle = "添加";
        vm.showEditForm();
        vm.parent = {};
        vm.productTypeId = "";
        vm.icloudCompanyId = null;
    };
    
    // 复制添加
    vm.addMaterialCopy = function(index){
    	vm.edit = false;
        vm.material = angular.copy(vm.page.data.list[index]);
        vm.material.recordId = "";
        vm.material.createdDate = Date.parse(new Date());
        vm.material.applicanter = BaseUtil.getUser();
        vm.material.lowerLimit = 1;
        vm.material.remark = "";
        vm.material.stocks = 0.000;
        vm.material.systemFlag = "";
        vm.material.availableQty = "";
        vm.material.stocks = "";
        vm.focus += 1;
        vm.show = false; //隐藏统计关键字
        vm.editTitle = "添加";
        vm.showEditForm();
        // vm.loadMaterialNoByType();
        if (vm.material.materialSpecList){
            for (let i=0;i<vm.material.materialSpecList.length;i++){
                vm.material.materialSpecList[i].valueList = [];
                if (vm.material.materialSpecList[i].valDefinition == 2){
                    if (vm.material.materialSpecList[i].dictItem && vm.material.materialSpecList[i].dictItem.values && vm.material.materialSpecList[i].value) {
                        vm.material.materialSpecList[i].valueList = vm.material.materialSpecList[i].value.split(",");
                    }
                }
            }
        }
        vm.dealMaterialSpe();
        vm.parent = {};
        upida.post("stock/material/getRawNo").then(function (data) {
            vm.material.no = data;
        });
        vm.loadMaterialPlace(vm.material.storehouse);
        vm.stockPlaceIds = [];
        if(vm.material.stockPlaceId)
        {
            var stockPlaceIds = vm.material.stockPlaceId.split(',');
            angular.forEach(stockPlaceIds,function(p){
                let flag = true;
                for(let id of vm.stockPlaceIds)
                {
                    if(id == p)
                    {
                        flag = false;
                        break;
                    }
                }
                if(flag)
                {
                    vm.stockPlaceIds.push(p);
                }
            });
        }
    };
    vm.craftNo = "";
    //选择了原料或者成品时
    vm.loadMaterialNo =function(){
    	vm.material.materialSpecList = [];
    	var a=vm.material.materialKind;
    	if(a=="100701"){
    		//获取物料编号
            upida.post("stock/material/getRawNo").then(function (data) {
                vm.material.no = data;
            });
    	}else{
    		vm.material.materialSpecList = null;
    		//获取物料编号
            upida.post("stock/material/getProNo").then(function (data) {
                vm.material.no = data;
                vm.craftNo = vm.material.no;
            });
    	}
    };

    vm.loadMaterialNoByType = function() {
        let copperSheetIds = null;
        if(vm.material.materialSpecList && vm.material.materialSpecList.length > 0)
        {
            for(let materialSpec of vm.material.materialSpecList)
            {
                if(materialSpec.name == '覆铜板材')
                {
                    copperSheetIds = materialSpec.value;
                    break;
                }
            }
        }
        vm.material.materialType.copperSheetIds = copperSheetIds;
        upida.post("stock/material/materialSpecListByType",vm.material.materialType).then(function (data) {
            vm.material.materialSpecList = [];
            vm.material.materialSpecList = angular.copy(data.materialSpecList);
            for (let i=0;i<vm.material.materialSpecList.length;i++){
                if(vm.material.materialSpecList[i].recordId == vm.materialCraft.recordId)
                {
                    for(let value of vm.material.materialSpecList[i].dictItem.values)
                    {
                        if(value.recordId == vm.materialCraft.value)
                        {
                            vm.material.materialSpecList[i].value = value.recordId;
                            break;
                        }
                    }
                }
                vm.material.materialSpecList[i].valueList = [];
                if (vm.material.materialSpecList[i].valDefinition == 2){
                    if (vm.material.materialSpecList[i].dictItem && vm.material.materialSpecList[i].dictItem.values && vm.material.materialSpecList[i].value) {
                        vm.material.materialSpecList[i].valueList = vm.material.materialSpecList[i].value.split(",");
                    }
                }
            }
            vm.dealMaterialSpe();
        });
    };

    vm.dealMaterialSpe = function() {
        if(!vm.material.materialSpecList)
        {
            return;
        }
        // 处理默认显示
        for (let i=0;i<vm.material.materialSpecList.length;i++){
            vm.material.materialSpecList[i].showFlag = false;
            if (vm.material.materialSpecList[i].hideflag == 0){
                vm.material.materialSpecList[i].showFlag = true;
            }
        }
        // 获取多值显示
        for (let i=0;i<vm.material.materialSpecList.length;i++){
            if (vm.material.materialSpecList[i].valDefinition == 2 && vm.material.materialSpecList[i].valueList && vm.material.materialSpecList[i].valueList.length > 0){
                vm.material.materialSpecList[i].value = "";
                for(let j=0; j<vm.material.materialSpecList[i].valueList.length; j++)
                {
                    vm.material.materialSpecList[i].value = vm.material.materialSpecList[i].value ? vm.material.materialSpecList[i].value + "," +
                        vm.material.materialSpecList[i].valueList[j]:vm.material.materialSpecList[i].valueList[j];
                }
            }
        }

        // 处理条件显示和值清空
        for (let i = 0; i < vm.material.materialSpecList.length; i++) {
            // 选中了值并且设置了打开项
            let val = vm.material.materialSpecList[i].value;
            if (vm.material.materialSpecList[i].valDefinition == 1){
                if (vm.material.materialSpecList[i].minValue && vm.material.materialSpecList[i].maxValue){
                    val = "(" + vm.material.materialSpecList[i].minValue + "≤值" + vm.material.materialSpecList[i].maxValue + ")";
                }
            }else if (val && vm.material.materialSpecList[i].dictItem && vm.material.materialSpecList[i].dictItem.values){
                if (vm.material.materialSpecList[i].valueList && vm.material.materialSpecList[i].valueList.length > 0){
                    val = "(";
                    for (let z=0;z<vm.material.materialSpecList[i].valueList.length;z++){
                        for (let j=0;j<vm.material.materialSpecList[i].dictItem.values.length;j++){
                            if (vm.material.materialSpecList[i].valueList[z] == vm.material.materialSpecList[i].dictItem.values[j].recordId){
                                val = val?val+" "+vm.material.materialSpecList[i].dictItem.values[j].value:vm.material.materialSpecList[i].dictItem.values[j].value;
                            }
                        }
                    }
                    val = val + ")";
                }else {
                    for (let j=0;j<vm.material.materialSpecList[i].dictItem.values.length;j++){
                        if (vm.material.materialSpecList[i].dictItem.values[j].recordId == val){
                            val = vm.material.materialSpecList[i].dictItem.values[j].value;
                        }
                    }
                }
            }
            vm.material.materialSpecList[i].valStr = val;
            if (vm.material.materialSpecList[i].specCraft && val){
                // 条件判断
                if (vm.material.materialSpecList[i].openScope) {
                    const openScope = vm.material.materialSpecList[i].openScope;
                    if (!val.includes(openScope)){
                        continue;
                    }
                }
                // 有值肯定是选中的
                const strIds = vm.material.materialSpecList[i].specCraft.split(",");
                // 开启规格展示
                for (let z=0;z<strIds.length;z++){
                    if (strIds[z]){
                        for (let j=0;j<vm.material.materialSpecList.length;j++){
                            if (vm.material.materialSpecList[j].recordId == strIds[z]){
                                vm.material.materialSpecList[j].showFlag = true;
                                break;
                            }
                        }
                    }
                }
            }
        }
    };

    vm.materialCraft = {};
    vm.reConcactName = function (craft){
        if(craft && craft.name == '覆铜板材')
        {
            vm.materialCraft = craft;
            vm.loadMaterialNoByType();
        }
        vm.dealMaterialSpe();
        vm.reConcactNameStr();
    };

    vm.reConcactNameStr = function() {
    	vm.material.name = "";
        vm.material.specification = "";
    	if(vm.material.materialSpecList && vm.material.materialSpecList.length > 0){
    		// 制造商
            let madeSupplierName = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
    			let fujian = vm.material.materialSpecList[i];
    			if(fujian.dictItem != null && fujian.dictItem.recordId === "70" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    madeSupplierName = fujian.valStr;
    				break;
    			}
    		}
    		
    		// 覆铜板材
            let board = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
    			let fujian = vm.material.materialSpecList[i];
    			if(fujian.dictItem != null && fujian.dictItem.recordId === "2" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    board = fujian.valStr;
    				break;
    			}
    		}
    		
    		// 组合物料名字,制造商+覆铜板材+追加项
    		if(madeSupplierName){
    			vm.material.name = madeSupplierName;
    			if(board){
        			vm.material.name = vm.material.name + " " + board;
        		}
    			if(vm.material.additionalItems)
    			{
    				vm.material.name = vm.material.name + " " + vm.material.additionalItems;
    			}
    		}else{
    			if(board){
        			vm.material.name = board;
        		}
    			if(vm.material.additionalItems)
    			{
    				vm.material.name = vm.material.name + " " + vm.material.additionalItems;
    			}
    		}
    		
    		// 板材厚度
    		let thickness = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.dictItem != null && fujian.dictItem.recordId === "3" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    thickness = fujian.valStr;
    				break;
    			}
    		}
    		
    		// 导热
            let daore = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.dictItem != null && fujian.dictItem.recordId === "58" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    daore = fujian.valStr;
    				break;
    			}
    		}
    		
    		// 耐压
            let naiya = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.dictItem != null && fujian.dictItem.recordId === "59" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    naiya = fujian.valStr;
    				break;
    			}
    		}
    		
    		// 覆铜要求
            let copperClad = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.dictItem != null && fujian.dictItem.recordId === "31" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    copperClad = fujian.valStr;
    				break;
    			}
    		}

            let length = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.name == "长"){
                    vm.material.materialSpecList[i].useFlag = 1;
    				length = fujian.valStr;
    				break;
    			}
    		}
            let width = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.name == "宽"){
                    vm.material.materialSpecList[i].useFlag = 1;
    				width = fujian.valStr;
    				break;
    			}
    		}
    		
    		// 贴膜
            let film = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.dictItem != null && fujian.dictItem.recordId === "71" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    film = fujian.valStr;
    				break;
    			}
    		}
    		// PCB类型
            let pcbType = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.dictItem != null && fujian.dictItem.recordId === "1" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    pcbType = fujian.valStr;
    				break;
    			}
    		}
            let simple = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.name == "样品"){
                    vm.material.materialSpecList[i].useFlag = 1;
    				simple = fujian.valStr;
    				break;
    			}
    		}
            let gum = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.name == "胶"){
                    vm.material.materialSpecList[i].useFlag = 1;
    				gum = fujian.valStr;
    				break;
    			}
    		}
            let insulat = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.name == "绝缘层"){
                    vm.material.materialSpecList[i].useFlag = 1;
    				insulat = fujian.valStr;
    				break;
    			}
    		}
            let cuprum = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.name == "无铜"){
                    vm.material.materialSpecList[i].useFlag = 1;
    				cuprum = fujian.valStr;
    				break;
    			}
    		}
            let special = "";
    		for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
    			if(fujian.name == "特殊要求"){
                    vm.material.materialSpecList[i].useFlag = 1;
    				special = fujian.valStr;
    				break;
    			}
    		}
    		let hou = vm.material.actualThickness;
    		// 组合规格型号，板材厚度+覆铜要求+长*宽+胶+膜+样品+无铜
    		if(thickness){
                let thicknessStr = thickness.replace('mm', '厚');
    			vm.material.specification = thicknessStr;
    		}
    		if(copperClad){
    			copperClad = copperClad.toLowerCase();
                let copperCladStr = "";
    			if(copperClad == "2/0"){
    				copperCladStr = "70um";
    			}else if(copperClad == "1/0"){
    				copperCladStr = "35um";
    			}else if(copperClad == "h/0"){
    				copperCladStr = "18um";
    			}else if(copperClad == "18um"){
    				copperCladStr = "18um";
    			}else if(copperClad == "3/0"){
    				copperCladStr = "105um";
    			}else if(copperClad == "28um"){
    				copperCladStr = "28um";
    			}else if(copperClad == "25um"){
    				copperCladStr = "25um";
    			}else if(copperClad == "15um"){
    				copperCladStr = "15um";
    			}else if(copperClad == "12um"){
    				copperCladStr = "12um";
    			}else if(copperClad == "30um"){
                    copperCladStr = "30um";
                }else if(copperClad == "35um"){
    				copperCladStr = "35um";
    			}else if(copperClad == "h/h"){
                    copperCladStr = "H/H";
                }else if(copperClad == "105um"){
                    copperCladStr = "105um";
                }else if(copperClad == "70um"){
                    copperCladStr = "70um";
                }else if(copperClad == "12u/12u"){
                    copperCladStr = "12U/12U";
                }
    			vm.material.specification = vm.material.specification + " " + copperCladStr;
    		}
    		if(vm.material.specification){
    			vm.material.specification = vm.material.specification + " ";
    		}
    		if(length){
    			vm.material.specification = vm.material.specification + length + "*";
    		}
    		if(width){
    			vm.material.specification = vm.material.specification + width + " ";
    		}
    		if(daore){
    			vm.material.specification = vm.material.specification + daore + " ";
    		}
    		if(naiya){
    			vm.material.specification = vm.material.specification + naiya + " ";
    		}
    		if(film){
    			vm.material.specification = vm.material.specification + film;
    		}
    		if(pcbType){
    			vm.material.specification = vm.material.specification + pcbType;
    		}
    		if(simple){
    			vm.material.specification = vm.material.specification + simple;
    		}
    		if(gum){
    			vm.material.specification = vm.material.specification + gum;
    		}
    		if(insulat){
    			vm.material.specification = vm.material.specification + insulat;
    		}
    		if(cuprum){
    			vm.material.specification = vm.material.specification + cuprum;
    		}
    		if(special){
    			vm.material.specification = vm.material.specification + special;
    		}
    		// 其它组合
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let value = "";
                let fujian = vm.material.materialSpecList[i];
                if (!fujian.useFlag){
                    value = fujian.valStr;
                    if (value){
                        vm.material.specification = vm.material.specification + " " + value;
                    }
                }
            }
            if(hou){
                vm.material.specification = vm.material.specification +" "+"实厚"+ hou;
            }
    	}
	}
    // 创建和修改物料
    vm.submitMod = function(form){
    	if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    form.$setDirty();
            if(!form.$valid){
            	vm.clicks = true;// tj 2017-09-07  用户是否多次点击
            	return;
            }
            vm.submitMaterial();
    	}
    };
    
    //提交添加物料
    vm.submitMaterial = function(){
        vm.saveMaterial();
    };
    
    vm.saveMaterial = function(){
        if(vm.stockPlaceList && vm.stockPlaceList.length > 0)
        {
            if(!vm.stockPlaceIds || vm.stockPlaceIds.length == 0)
            {
                vm.message = "请选择库位";
                $('#static').modal();
                vm.clicks = true;
                return;
            }
        }
        var materialPlaceComList = [];
        if(vm.stockPlaceIds && vm.stockPlaceIds.length > 0)
        {
            angular.forEach(vm.stockPlaceIds,function(p){
                var materialPlace = {};
                materialPlace.materPlaceId = p;
                materialPlaceComList.push(materialPlace);
            });
        }
        vm.material.materialPlaceComList = materialPlaceComList;
        vm.material.typeA = vm.typeA;
        vm.material.productTypeId = vm.productTypeId;
        vm.material.icloudCompanyId = vm.icloudCompanyId;
    	upida.post("stock/material/validateMaterial",vm.material).then(function(data){
    		if(data == "fail") {
    			vm.message = "参数错误，请刷新重试";
                $('#static').modal();
    		}
    		else if(data == "failValidate") {
    			vm.message = "存在名称与规格相同的物料，不能重复添加！";
                $('#static').modal();
    		}
    		else if(data == "success")
    		{
    			upida.post("stock/material/save", vm.material).then(function (data) {
	                if(data.flag) {
	                	vm.message = data.rs;
	                }else {
	                	// 提示信息
	                	vm.message = vm.editTitle + "物料" + data;
	                	vm.material = {};
	                	vm.hideEditForm();
	                }
                    vm.doQuery();
	                $('#static').modal();
    			});
    		}
    		else {
    			vm.message = "系统错误，请联系管理员！";
                $('#static').modal();
    		}
            MainCtrl.unblockUI();
            vm.clicks = true;
    	});
    	
    };
    // 确认是否可以作废物料
    vm.cancelMaterial = function(index){
    	vm.delRecordId = vm.page.data.list[index].recordId;
        vm.delMaterialName=vm.page.data.list[index].no;
        // 提示信息
        $('#staticCancel').modal();
    };

    vm.cancelVoidMaterial = function (index){
        vm.delRecordId = vm.page.data.list[index].recordId;
        vm.delMaterialName=vm.page.data.list[index].no;
        $('#staticBack').modal();
    }
    
    // 做物料的废除操作
    vm.doCancelmaterialCtrl = function(){
    	if(vm.clicks){
    	    vm.clicks = false; // tj 2017-09-07  用户是否多次点击
    	    
    	    MainCtrl.blockUI({
            	animate: true
            });
            upida.post("stock/material/cancel/" + vm.delRecordId, null).then(function (data) {
            	if(data){
            		vm.message = "作废物料成功!";
            		// 重新查询数据
            		vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
            		vm.delRecordId= -1;
            	}else{
            		vm.message = "亲,正在使用该物料,不能作废!";
            		//进度条关闭
    				MainCtrl.unblockUI();
            	}
                $('#static').modal();
                vm.clicks = true;// tj 2017-09-07  用户是否多次点击
            });
    	}
    };

    // 做物料的去取消作废操作
    vm.doBackmaterialCtrl = function(){
        if(vm.clicks){
            vm.clicks = false; // tj 2017-09-07  用户是否多次点击

            MainCtrl.blockUI({
                animate: true
            });
            upida.post("stock/material/cancelVoidMaterial/" + vm.delRecordId, null).then(function (data) {
                if(data){
                    vm.message = "取消成功!";
                    // 重新查询数据
                    vm.init(vm.page.pageNo, vm.page.pageSize, vm.page.condition, vm.page.url);
                    vm.delRecordId= -1;
                }else{
                    vm.message = "数据错误请刷新重试!";
                    //进度条关闭
                    MainCtrl.unblockUI();
                }
                $('#static').modal();
                vm.clicks = true;// tj 2017-09-07  用户是否多次点击
            });
        }
    };

    vm.materialSpecification = {};
    // 选择添加方式
    vm.checkPro1 = true;
    vm.checkPro = false;
    vm.checkDN = false;
    vm.checkMN = true;
    vm.disabledFlag = false;
    // 物料规格列表查询对象
    vm.specQuery = {};
    vm.specQuery.name = "";
    vm.specQuery.materialType = "";
    vm.specQuery.copperSheetId = "";
    // 单击添加物料规格
    vm.addMaterialSpec = function(){
    	vm.specIsAdd = true;
        vm.materialSpec = {};
        vm.materialSpec.name = "";
        vm.materialSpec.remark = "";
        vm.editMaterialSpec = false;
        vm.materialSpec.visible = "0";
        vm.materialSpec.export = "0";
        vm.materialSpec.hideflag = "0";
        vm.editSpecTitle = "添加物料规格";
        vm.showEditSpecForm();
        vm.parent = {};
    };
    
	// 选择方式 1 (添加)
	vm.manualRadioClick = function() {
		vm.checkPro1 = true;
		vm.checkPro = false;
		vm.checkDN = false;
		vm.checkMN = true;
		vm.materialSpec = {};
		vm.materialSpec.name = "";
        vm.materialSpec.remark = "";
		vm.materialSpec.visible = "0";
        vm.materialSpec.export = "0";
        vm.materialSpec.hideflag = "0";
	};

	// 选择方式 2 (添加)
	vm.dictionariesClick = function(){
		vm.checkPro1 = false;
		vm.checkPro = true;
		vm.checkDN = true;
		vm.checkMN = false;
		vm.materialSpec = {};
		vm.materialSpec.name = "";
        vm.materialSpec.remark = "";
		vm.materialSpec.visible = "0";
        vm.materialSpec.export = "0";
        vm.materialSpec.hideflag = "0";
	};
	
	vm.doDictionaries = function(){
		vm.materialSpec.name = vm.materialSpec.dictItem.itemName;
	};
    
    // 创建和修改物料规格
    vm.submitSpecMod = function(form){
        form.$setDirty();
        if(!form.$valid){
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        if (vm.materialSpec.copperSheetIds){
            vm.materialSpec.copperSheetIds = vm.materialSpec.copperSheetIds.join(',');
        }
        if (vm.specIsAdd)
        {
            upida.post("stock/material/spec/check", vm.materialSpec).then(function (data) {
                if(data){
                    vm.saveMaterialSpec();
                } else{
                    vm.message = "物料规格名称重复，请重新输入。";
                    $('#static').modal();
                    MainCtrl.unblockUI();
                }
            });
        }else {
            upida.post("stock/material/spec/check",vm.materialSpec).then(function (data) {
                if(data){
                    vm.saveMaterialSpec();
                } else{
                    vm.message = "物料规格名称重复，请重新输入。";
                    $('#static1').modal();
                    MainCtrl.unblockUI();
                }
            });
        }
        if(vm.tabs.editForm.show)
        {
            vm.tabs.editForm.show = false;
        }
    };
        
    //保存规格
    vm.saveMaterialSpec = function(){
        if(vm.materialSpec && vm.materialSpec.specList)
        {
            vm.materialSpec.specCraft = "";
            for(let i=0; i<vm.materialSpec.specList.length; i++)
            {
                if (vm.materialSpec.specList[i].recordId == vm.materialSpec.recordId){
                    vm.message = "不能打开自身规格，请重新配置";
                    $('#static1').modal();
                    return;
                }
                vm.materialSpec.specCraft = vm.materialSpec.specCraft ? vm.materialSpec.specCraft + "," +
                    vm.materialSpec.specList[i]:vm.materialSpec.specList[i];
            }
        }
    	upida.post("stock/material/spec/save", vm.materialSpec).then(function (data) {
            // 初始化数据
            if(data){
            	vm.materialSpec = {};
            	// 提示信息
            	vm.message = data;
            	// 重新查询数据
                vm.doSpecQuery();
                vm.hideEditSpecForm();
            }else{
            	// 提示信息
            	vm.message = data;
            }
            $('#static').modal();
            MainCtrl.unblockUI();
        });
	};

    vm.delMaterialSpec = function (index){
        vm.materialSpec = angular.copy(vm.materialSpecList[index]);
        MainCtrl.blockUI({
            animate: true,
        });
        vm.delSpecRecordId = vm.materialSpec.recordId;
        upida.post("stock/material/spec/del/" + vm.delSpecRecordId).then(function (data) {
            if (data){
                vm.message = data;
                $('#static').modal();
            }
            vm.doSpecQuery();
            MainCtrl.unblockUI();
        });
    };
    
    // 隐藏规格编辑
    vm.hideEditSpecForm = function () {
        vm.tabs.editSpecForm.show = false;
        vm.formScope.material_spec_form.$setPristine();
        $timeout(function(){
            vm.tabs.viewSpecForm.active = true;
        });
    };
    
    // 显示规格编辑
    vm.showEditSpecForm = function () {
        vm.tabs.editSpecForm.show = true;
        vm.tabs.editSpecForm.active = true;
        vm.disabledFlag = false;
    };
    
    // 查询数据
    vm.materialSpecList = [];
    vm.doSpecQuery = function(){
        vm.materialSpecList = [];
        upida.post("stock/material/specPage", vm.specQuery).then(function (data) {
            // 初始化数据
            if(data){
                vm.materialSpecList = data;
            }
        });
    };
    
    // 单击修改
    vm.mod2MaterialSpec = function(index){
    	vm.specIsAdd = false;
    	vm.editMaterialSpec = true;
        vm.editSpecTitle = vm.right.edit ? "修改" : "查看";
        vm.focus += 1;
        vm.materialSpec = angular.copy(vm.materialSpecList[index]);
        if (vm.materialSpec.copperSheetIds){
            vm.materialSpec.copperSheetIds = vm.materialSpec.copperSheetIds.split(',');
        }
        if(vm.materialSpec.dictItem != null && vm.materialSpec.dictItem.recordId != null)
        {
        	vm.checkPro1 = false;
    		vm.checkPro = true;
    		vm.checkDN = true;
    		vm.checkMN = false;
        }
        else
        {
        	vm.checkPro1 = true;
    		vm.checkPro = false;
    		vm.checkDN = false;
    		vm.checkMN = true;
        }
        let specList = [];
        if(vm.materialSpec && vm.materialSpec.specCraft)
        {
            specList = vm.materialSpec.specCraft.split(",");
        }
        vm.materialSpec.specList = specList;
        vm.modMaterialSpec();
    };
    
    // 显示编辑
    vm.modMaterialSpec = function () {
        vm.tabs.editSpecForm.show = true;
        vm.tabs.editSpecForm.active = true;
    };

    function loadItemsData() {
        MainCtrl.blockUI({
            animate: true
        });
        // 加载数据
        upida.get("stock/material/load/data").then(function(data) {
            vm.materialTypes = data.materialTypes;
            //物料类型
            vm.materialTypesMaterial = data.materialTypesMaterial;
            //加载计量单位
            vm.units = data.units;
            //加载仓库集合
            vm.storeHouseList = data.storeHouseList;
            //数据字典集合
            vm.dictionariesList = data.dictionariesList;
            //税票名称
            vm.productNameList = data.productNameList;
            //加载产品类型
            vm.productTypeList = data.productTypeList;
            //加载覆铜板材字典值
            vm.copperSheetList = data.copperSheetList;
            if(vm.copperSheetList && vm.copperSheetList.length > 0)
            {
                vm.copperSheetList.splice(0,0,{recordId:'',value:'所有'});
            }
            //加载大生态圈公司
            vm.icloudCompanyList = data.icloudCompanyList;
            //进度条关闭
            MainCtrl.unblockUI();
        });
    };

    // 加载库位
    vm.stockPlaceList = [];
    vm.loadMaterialPlace = function(storehouse)
    {
        vm.stockPlaceList = [];
        var materialPlace = {};
        materialPlace.storeId = storehouse.recordId;
        upida.post("purch/rawApply/getSaleStockPlaceList", materialPlace).then(function (data) {
            if(data)
            {
                vm.stockPlaceList = data.placeList;
            }
        });
    }

    vm.selectMaterialType = function()
    {
        vm.materialSpec.copperSheetIds = [];
    }

    vm.madeCode = function(obj){
        MainCtrl.blockUI({
            animate: true,
        });
        $http.post('a/stock/material/madeCode', {
            'recordId': obj.recordId
        }, {
            responseType: 'arraybuffer'
        }).success(function (response, status, headers, config) {
            if(response.byteLength == 0){
                MainCtrl.unblockUI();
                vm.message = "制作失败，请刷新重试";
                $('#static').modal();
            }else{
                MainCtrl.unblockUI();
                var a = document.createElement('a');
                var file = new Blob([response], {
                    type: 'image/png'
                });
                var fileURL = (window.URL || window.webkitURL).createObjectURL(file);
                // window.open(fileURL);
                a.href = fileURL;
                a.download = headers('X-File-Name');
                document.body.appendChild(a);
                a.click();
                $timeout(function () {
                    document.body.removeChild(a);
                    URL.revokeObjectURL(fileURL);
                }, 100);
            }
        }).error(function (data) {
            MainCtrl.unblockUI();
            vm.message = "制作失败，请刷新重试！";
            $('#static').modal();
        });
    };

    vm.handleMatList = [];
    vm.resetData = function()
    {
        upida.post("stock/material/handleMatSpecification").then(function(data){
            vm.handleMatList = data;
            if(vm.handleMatList && vm.handleMatList.length > 0)
            {
                $('#handleMatStatic').modal();
            }
        });
    }
    vm.updateMatSpecification = function()
    {
        let list = [];
        for(let mat of vm.handleMatList)
        {
            let matCopy = vm.reConcactNameStrTwo(mat);
            list.push(angular.copy(matCopy));
        }
        upida.post("stock/material/updateMatSpecification",list).then(function(data){
            if(data == "success")
            {
                vm.message = "修改成功";
            }
            else
            {
                vm.message = "修改失败";
            }
            $('#static').modal();
        });
    }

    vm.reConcactNameStrTwo = function(mat) {
        vm.material = mat;
        vm.material.name = "";
        vm.material.specification = "";
        vm.dealMaterialSpe();
        if(vm.material.materialSpecList && vm.material.materialSpecList.length > 0){
            // 制造商
            let madeSupplierName = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId === "70" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    madeSupplierName = fujian.valStr;
                    break;
                }
            }

            // 覆铜板材
            let board = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId === "2" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    board = fujian.valStr;
                    break;
                }
            }

            // 组合物料名字,制造商+覆铜板材+追加项
            if(madeSupplierName){
                vm.material.name = madeSupplierName;
                if(board){
                    vm.material.name = vm.material.name + " " + board;
                }
                if(vm.material.additionalItems)
                {
                    vm.material.name = vm.material.name + " " + vm.material.additionalItems;
                }
            }else{
                if(board){
                    vm.material.name = board;
                }
                if(vm.material.additionalItems)
                {
                    vm.material.name = vm.material.name + " " + vm.material.additionalItems;
                }
            }

            // 板材厚度
            let thickness = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId === "3" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    thickness = fujian.valStr;
                    break;
                }
            }

            // 导热
            let daore = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId === "58" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    daore = fujian.valStr;
                    break;
                }
            }

            // 耐压
            let naiya = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId === "59" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    naiya = fujian.valStr;
                    break;
                }
            }

            // 覆铜要求
            let copperClad = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId === "31" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    copperClad = fujian.valStr;
                    break;
                }
            }

            let length = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.name == "长"){
                    vm.material.materialSpecList[i].useFlag = 1;
                    length = fujian.valStr;
                    break;
                }
            }
            let width = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.name == "宽"){
                    vm.material.materialSpecList[i].useFlag = 1;
                    width = fujian.valStr;
                    break;
                }
            }

            // 贴膜
            let film = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId === "71" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    film = fujian.valStr;
                    break;
                }
            }
            // PCB类型
            let pcbType = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.dictItem != null && fujian.dictItem.recordId === "1" && fujian.dictItem.values != null){
                    vm.material.materialSpecList[i].useFlag = 1;
                    pcbType = fujian.valStr;
                    break;
                }
            }
            let simple = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.name == "样品"){
                    vm.material.materialSpecList[i].useFlag = 1;
                    simple = fujian.valStr;
                    break;
                }
            }
            let gum = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.name == "胶"){
                    vm.material.materialSpecList[i].useFlag = 1;
                    gum = fujian.valStr;
                    break;
                }
            }
            let insulat = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.name == "绝缘层"){
                    vm.material.materialSpecList[i].useFlag = 1;
                    insulat = fujian.valStr;
                    break;
                }
            }
            let cuprum = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.name == "无铜"){
                    vm.material.materialSpecList[i].useFlag = 1;
                    cuprum = fujian.valStr;
                    break;
                }
            }
            let special = "";
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let fujian = vm.material.materialSpecList[i];
                if(fujian.name == "特殊要求"){
                    vm.material.materialSpecList[i].useFlag = 1;
                    special = fujian.valStr;
                    break;
                }
            }
            let hou = vm.material.actualThickness;
            // 组合规格型号，板材厚度+覆铜要求+长*宽+胶+膜+样品+无铜
            if(thickness){
                let thicknessStr = thickness.replace('mm', '厚');
                vm.material.specification = thicknessStr;
            }
            if(copperClad){
                copperClad = copperClad.toLowerCase();
                let copperCladStr = "";
                if(copperClad == "2/0"){
                    copperCladStr = "70um";
                }else if(copperClad == "1/0"){
                    copperCladStr = "35um";
                }else if(copperClad == "h/0"){
                    copperCladStr = "18um";
                }else if(copperClad == "18um"){
                    copperCladStr = "18um";
                }else if(copperClad == "3/0"){
                    copperCladStr = "105um";
                }else if(copperClad == "28um"){
                    copperCladStr = "28um";
                }else if(copperClad == "25um"){
                    copperCladStr = "25um";
                }else if(copperClad == "15um"){
                    copperCladStr = "15um";
                }else if(copperClad == "12um"){
                    copperCladStr = "12um";
                }else if(copperClad == "30um"){
                    copperCladStr = "30um";
                }else if(copperClad == "35um"){
                    copperCladStr = "35um";
                }else if(copperClad == "h/h"){
                    copperCladStr = "H/H";
                }else if(copperClad == "105um"){
                    copperCladStr = "105um";
                }else if(copperClad == "70um"){
                    copperCladStr = "70um";
                }else if(copperClad == "12u/12u"){
                    copperCladStr = "12U/12U";
                }
                vm.material.specification = vm.material.specification + " " + copperCladStr;
            }
            if(vm.material.specification){
                vm.material.specification = vm.material.specification + " ";
            }
            if(length){
                vm.material.specification = vm.material.specification + length + "*";
            }
            if(width){
                vm.material.specification = vm.material.specification + width + " ";
            }
            if(daore){
                vm.material.specification = vm.material.specification + daore + " ";
            }
            if(naiya){
                vm.material.specification = vm.material.specification + naiya + " ";
            }
            if(film){
                vm.material.specification = vm.material.specification + film;
            }
            if(pcbType){
                vm.material.specification = vm.material.specification + pcbType;
            }
            if(simple){
                vm.material.specification = vm.material.specification + simple;
            }
            if(gum){
                vm.material.specification = vm.material.specification + gum;
            }
            if(insulat){
                vm.material.specification = vm.material.specification + insulat;
            }
            if(cuprum){
                vm.material.specification = vm.material.specification + cuprum;
            }
            if(special){
                vm.material.specification = vm.material.specification + special;
            }
            // 其它组合
            for(let i=0;i<vm.material.materialSpecList.length;i++){
                let value = "";
                let fujian = vm.material.materialSpecList[i];
                if (!fujian.useFlag){
                    value = fujian.valStr;
                    if (value){
                        vm.material.specification = vm.material.specification + " " + value;
                    }
                }
            }
            if(hou){
                vm.material.specification = vm.material.specification +" "+"实厚"+ hou;
            }
            return vm.material;
        }
    }

    vm.boardSupermarketMaterial = {};
    vm.boardSupermarket = function (index)
    {
        vm.boardSupermarketMaterial = vm.page.data.list[index];
        $('#boardSupermarketShow').modal();
    }

    vm.addBoardSupermarket = function ()
    {
        if (!vm.boardSupermarketMaterial.supermarketQuantity)
        {
            vm.message = "请填写板材超市数量!";
            $('#static').modal();
            return;
        }
        if (!vm.boardSupermarketMaterial.supermarketQuantity > vm.boardSupermarketMaterial.stocks)
        {
            vm.message = "板材超市数量不能大于甚于库存!";
            $('#static').modal();
            return;
        }
        if (!vm.boardSupermarketMaterial.supermarketPrice)
        {
            vm.message = "请填写板材超市单价!";
            $('#static').modal();
            return;
        }
        MainCtrl.blockUI({
            animate: true,
        });
        upida.post("stock/material/addBoardSupermarket", vm.boardSupermarketMaterial).then(function(data) {
            vm.message = data;
            $('#static').modal();
            vm.doQuery();
            MainCtrl.unblockUI();
        });
    };

    vm.updBoardSupermarket = function ()
    {
        $('#delistMaterial').modal();
    };

    vm.delistSupermarket = function ()
    {
        if (!vm.boardSupermarketMaterial.boardSupermarketStatus || vm.boardSupermarketMaterial.boardSupermarketStatus != 1)
        {
            vm.message = "该材料不需要下架!";
            $('#static').modal();
            return;
        }
        upida.post("stock/material/delistSupermarket", vm.boardSupermarketMaterial).then(function(data) {
            vm.message = data;
            $('#static').modal();
            vm.doQuery();
            MainCtrl.unblockUI();
        });
    }

    // 加载权限
    function loadRight(){
        upida.get("common/rightall?prefix=stock:material").then(function(data){
            vm.right.view = data.view;
            vm.right.edit = data.edit;
            vm.right.manage = data.manage;
            loadItemsData();
            // 初始化第一页，条件为空
            vm.doQuery();
        });
    };

    $scope.$on("$stateChangeSuccess", function () {
        upida.setScope($scope);
        loadRight();
    });
}]);