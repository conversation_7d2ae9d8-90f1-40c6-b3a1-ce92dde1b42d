const kybMenu = {
	template:'#kybMenu',
	data(){
		return{
			workClass: 'nav-link',
			msgClass: 'nav-link',
			submitClass: 'nav-link',
			contractClass: 'nav-link',
			myClass: 'nav-link'
		}
	},
	computed: {
		showSel: {
			get () {
				return this.$store.state.myStore.showSel
		    }
		}
	},
	methods:{
		changeClass:function(item){
			this.initClass();
			if(item == 1){
				this.workClass = 'nav-link active';
			}else if(item == 2){
				this.msgClass = 'nav-link active';
			}else if(item == 3){
				this.submitClass = 'nav-link active';
			}else if(item == 4){
				this.contractClass = 'nav-link active';
			}else if(item == 5){
				this.myClass = 'nav-link active';
			}
		},
		initClass:function(){
			this.workClass = 'nav-link';
			this.msgClass = 'nav-link';
			this.submitClass = 'nav-link';
			this.contractClass = 'nav-link';
			this.myClass = 'nav-link';
		}
	}
}