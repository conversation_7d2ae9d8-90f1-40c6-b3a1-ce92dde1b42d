package com.kyb.pcberp.modules.icloud.erp.erp.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kyb.pcberp.common.base.pojo.Icloud_BasePojo;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.modules.icloud.contract.pojo.Icloud_ContractDeail;
import com.kyb.pcberp.modules.icloud.contract.pojo.Icloud_NotificationMaterial;
import com.kyb.pcberp.modules.icloud.erp.craft.pojo.Icloud_ContractCraft;
import com.kyb.pcberp.modules.icloud.erp.dict.pojo.Icloud_DictValue;
import com.kyb.pcberp.modules.icloud.erp.pojo.Icloud_Company;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class Icloud_Notification extends Icloud_BasePojo
{
    private static final long serialVersionUID = 1L;

    private Icloud_Company company;

    private Icloud_ContractDetailErp contractDetail; // 合同明细编号

    private String no; // 生产通知单编号

    private Icloud_DictValue orderType; // 订单类型

    private String status; // 单据状态

    private String craftNo; // 生产编号

    private Icloud_ContractErp contract;

    private Date confirmDate; // 确认时间

    private String customerModel; // 客户型号

    private String urgentFlag; // 加急单标志 1 加急 0 不加急

    private String pdtReportFlag; // 要求有成品出货报告

    private String smpCfmFlag; // 要求有样品确认书

    private String metalAnalyzeFlag; // 要求有金相切片分析书

    private String otherReport; // 其它分析报告

    private String quantity; // quantity

    private Date orderDate; // orderdate

    private Date deliveryDate; // deliverydate

    private Integer notificationType; // 通知单类型 （1.正常通知单 2 客诉通知单）

    private Integer mergeType; // mergetype

    private String progressDesc; // 周期

    private Icloud_CardA cardA; // 流程卡

    private Icloud_Branch branch;// 子公司

    private String packageReq; // 包装要求

    private String remark; // 备注

    private Icloud_ContractCraft craft; // 工艺

    private String customerNo; // 客户编号

    private String startDate; // 开始时间

    private String endDate; // 结束时间

    private String queryFlag; // 个人1，所有0

    private String craftNoList; // 生产编号组

    private String custModelList; // 客户型号组

    private Integer completedQty; // 入库总数

    private Integer canSendDeliveryQty; // 可送货数量

    private Integer feedPcsQty; // 投料数量

    private String mergeId; // 合单后的Id

    private Integer occupiedAvailableQty; // 占用可用数量

    private Icloud_Material material; // 物料

    private Integer spares; // 备品数量

    private Integer isFeeding;// 是否继续投料

    private Integer type; // 条件(0:所有;1:已送货;2:有库存;3:生产中;4:已作废)

    private BigDecimal percent; // 真实预投率

    private BigDecimal spec; // 下单平米数

    private String hascardA; // 工程资料状态

    private Integer deliveryQty; // 送货数量

    private String saleName;

    private String saleCompanyId;

    private String erpCustomerId;

    private String saleId;

    private String oneValue;

    private String oneRemark;

    private String oneShortName;

    private Integer oneCustomerGradeNum;

    private Integer oneLogisticsTime;

    private BigDecimal oneCapacityPrecent;

    private String twoValue;

    private String twoRemark;

    private String twoShortName;

    private Integer twoCustomerGradeNum;

    private Integer twoLogisticsTime;

    private BigDecimal twoCapacityPrecent;

    private String threeValue;

    private String threeRemark;

    private String threeShortName;

    private Integer threeCustomerGradeNum;

    private Integer threeLogisticsTime;

    private BigDecimal threeCapacityPrecent;

    private String condtionStr;

    private Date newDeliveryDays;

    private Integer oueDays; // 顺延天数

    private String value;

    private String shortName;

    private BigDecimal orderDeailArea;

    private Integer customerGradeNum;

    private Date estimateDateCopy;

    private BigDecimal capacityPrecent;

    private String capaUseStatus;

    private BigDecimal needArea;

    private String totalPcses;

    private String dayAreas;

    private Boolean checkFlag;

    private String productionCycleId;

    private Date estimateStartDate;

    private Boolean issueStatusFlag;

    private String saleCsutomerId;

    private String saleCsutomerName;

    private String branchQuery;

    private String salseManName;

    private String needBord;

    private String replyStock;

    private String replyremark;

    private String materialId;

    private String replyMaterialName;

    private String occupiedStock; // 占用库存

    private String oldOccupiedStock; // 备份占用库存

    private String leadTime; // 采购周期

    private String occupiedStockCopy; // 占用库存复制（不用了，可以考虑去掉）

    private Date estimateDate;

    private Date estimateDateTwo;

    private String productArea;

    private String productAreaCopy;

    private String estimateRemark;

    private String estimateMethod;

    private String purchRowDays;

    private String notiNeedDays;

    private String waitDays;

    private String logisticsTime;

    private Integer starvingBoard;

    private Integer availableQty;

    private Integer feedStocks;

    private Integer purchRawNum;

    private Integer purchNum;

    private Integer purchStockNum;

    private String notifiMaterialId;

    private String icloud;

    private String cancelCause;

    private BigDecimal excessStock;

    private String userId;

    private String companyId;

    private String contractDetailId;

    private String processCardAId;

    private String feedingId;

    private String deliveryRequier;

    private String completeDate;

    private String failedPcsQty;

    private String cost;

    private String invalidQty;

    private String isReplenish;

    private Icloud_Price pricees;

    private String customerId;

    private Icloud_ContractDeail conDeail;

    private String customerModels;

    private Icloud_Feeding feeding; // feedingid

    private String status1; // status

    private String status2; // status

    private String status3; // status 通知单已经入库的状态

    private List<Icloud_ProduceBatch> batchList;// 通知单中的批次

    private List<Icloud_Feeding> feedings;// 通知单对应的投料信息

    private Integer feedPcsQtyes; // 前台传过来的本次投料数

    private String customerName;

    private String contractNo;

    private String customerPo;

    private BigDecimal price;

    private Long boardLevel;

    private Long materialType;

    private Long boardThickness;

    private Long copperCladThickness;

    private Long surfaceProcess;

    private Long solderMaskType;

    private Long characterType;

    private Long shapingWay;

    private Long testMethod;

    private BigDecimal engineeringFee;

    private BigDecimal mouldFee;

    private BigDecimal testShelfFee;

    private BigDecimal othersFee;

    private BigDecimal totalAmt;

    private Long orderTypees; // 订单类型(查询)

    private BigDecimal pnlWidth;

    private BigDecimal pnlLength;

    private int pnlDivisor;

    private Long orderTypes;

    private Icloud_BoardPart partA; // A板布局信息ID

    private Icloud_BoardPart partB; // B板布局信息ID

    private Integer findNoisEnable; // 在保存单据的时候 单据编号被占用了 就在前台赋值为 1 了 没有占用就是为0

    private Integer readyQty; // 待入库数量

    private Integer savetypees; // 新保存类型

    private Icloud_BoardCutting boardCutting;

    private List<Icloud_BoardPartCraft> boardPartCraftList;

    private String clerk;// 业务员ID

    private boolean self = true;// 自己的通知单

    public static final Integer NOTIFICATION_NOMARL_TYPE = 1;

    public static final Integer NOTIFICATION_REJECT_TYPE = 2;

    private Date sentTimeStartQr; // 时间段查询条件 载体

    private Date sentTimeEndQr;

    private Date sentTimeStartQr2; // 交货时间查询条件 载体

    private Date sentTimeEndQr2;

    private String time;

    private Integer lateDays;// 逾期天数

    private Boolean isModCraftNo;

    private String showInfo;

    private String inlet; // 登录入口

    private String erpUserId; // erp 用户Id

    private String takeHandWip; // 参数设置(0:按接板显示结存,1:按交板显示结存)

    private String search; // 查询条件

    private Icloud_ProduceBatchDetail produceBatchDetail;// 批次明细

    private String acDistributeDateStr; // 实际发料日期

    private String orderDateStr;

    private String deliveryDateStr;

    private Integer bitSuspend; // 是否暂停（缺料） 1 不是 2 是

    private String develierState;

    private Integer useHouseQty;

    private Integer useHouseRejQty;

    private Integer deliveryFlag;

    private String noticeIds;

    private String orderTypeName;

    private String produceType;

    private Integer printNum;

    private String inkTypeVal;

    private BigDecimal setWidth; // SET宽度，单位mm

    private BigDecimal setLength; // SET长度，单位mm

    private String unitLength; // pcs尺寸

    private String unitWidth; // pcs尺寸

    private String notifiRemark; // 通知单特殊说明

    private List<Icloud_PurchasingDetail> purDetailList;

    private String showStatus;

    private String materialTypeValue; // 覆铜板材

    private String boardLevelValue; // PCB类型

    private String boardThicknessValue; // 板材厚度

    private String copperCladThicknessValue; // 覆铜要求

    private String surfaceProcessValue; // 镀层处理

    private String solderMaskTypeValue; // 阻焊类型

    private String groupCenterId;

    private String oldCraftNo;

    private List<Icloud_NotificationMaterial> notifiMaterList;

    private BigDecimal engineeringFeeCopy;

    private BigDecimal mouldFeeCopy;

    private BigDecimal testShelfFeeCopy;

    private String priceId;

    private BigDecimal prdEngineeringFee;

    private BigDecimal prdMouldFee;

    private BigDecimal prdTestShelfFee;

    private BigDecimal prdFilmFee;

    private BigDecimal prdOthersFee;

    private BigDecimal prdEngineeringFeeCopy;

    private BigDecimal prdMouldFeeCopy;

    private BigDecimal prdTestShelfFeeCopy;

    private BigDecimal prdFilmFeeCopy;

    private BigDecimal prdOthersFeeCopy;

    private String factoryComId;

    private String qcFlag;

    private Double salePrecent;

    private String groupDbId;

    private String egCardNo;

    private String dictOrderType;

    private String dictCardType;

    private String deptId;

    private String versionDate;

    private BigDecimal maxBatchArea;

    private String deptName;

    private BigDecimal exchangeRate;

    private Date exchangeDate;

    private String exchangeType;

    private String daoreValue;

    private BigDecimal length; // 长

    private BigDecimal width; // 宽

    private String boardThicknessId; // 板材厚度

    private String copperCladThicknessId; // 覆铜要求

    private String materialNo;

    private Integer hours; // 下单时范围

    private String econnmyId;

    private String factoryId;

    private String econnmyName;// 总部经济公司

    private String factoryName; // 工厂公司

    private String cusProfitRate; // 客户要求利润率

    public Icloud_Notification()
    {
        super();
    }

    public Icloud_Notification(String id)
    {
        super(id);
    }

    public Icloud_Company getCompany()
    {
        return company;
    }

    public void setCompany(Icloud_Company company)
    {
        this.company = company;
    }

    public Icloud_ContractDetailErp getContractDetail()
    {
        return contractDetail;
    }

    public void setContractDetail(Icloud_ContractDetailErp contractDetail)
    {
        this.contractDetail = contractDetail;
    }

    public String getNo()
    {
        return no;
    }

    public void setNo(String no)
    {
        this.no = no;
    }

    public Icloud_DictValue getOrderType()
    {
        return orderType;
    }

    public void setOrderType(Icloud_DictValue orderType)
    {
        this.orderType = orderType;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getCraftNo()
    {
        return craftNo;
    }

    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }

    public Icloud_ContractErp getContract()
    {
        return contract;
    }

    public void setContract(Icloud_ContractErp contract)
    {
        this.contract = contract;
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getConfirmDate()
    {
        return confirmDate;
    }

    public void setConfirmDate(Date confirmDate)
    {
        this.confirmDate = confirmDate;
    }

    public String getCustomerModel()
    {
        return customerModel;
    }

    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }

    public String getUrgentFlag()
    {
        return urgentFlag;
    }

    public void setUrgentFlag(String urgentFlag)
    {
        this.urgentFlag = urgentFlag;
    }

    public String getPdtReportFlag()
    {
        return pdtReportFlag;
    }

    public void setPdtReportFlag(String pdtReportFlag)
    {
        this.pdtReportFlag = pdtReportFlag;
    }

    public String getSmpCfmFlag()
    {
        return smpCfmFlag;
    }

    public void setSmpCfmFlag(String smpCfmFlag)
    {
        this.smpCfmFlag = smpCfmFlag;
    }

    public String getMetalAnalyzeFlag()
    {
        return metalAnalyzeFlag;
    }

    public void setMetalAnalyzeFlag(String metalAnalyzeFlag)
    {
        this.metalAnalyzeFlag = metalAnalyzeFlag;
    }

    public String getOtherReport()
    {
        return otherReport;
    }

    public void setOtherReport(String otherReport)
    {
        this.otherReport = otherReport;
    }

    public String getQuantity()
    {
        return quantity;
    }

    public void setQuantity(String quantity)
    {
        this.quantity = quantity;
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getOrderDate()
    {
        return orderDate;
    }

    public void setOrderDate(Date orderDate)
    {
        this.orderDate = orderDate;
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getDeliveryDate()
    {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }

    public Integer getNotificationType()
    {
        return notificationType;
    }

    public void setNotificationType(Integer notificationType)
    {
        this.notificationType = notificationType;
    }

    public Integer getMergeType()
    {
        return mergeType;
    }

    public void setMergeType(Integer mergeType)
    {
        this.mergeType = mergeType;
    }

    public String getProgressDesc()
    {
        return progressDesc;
    }

    public void setProgressDesc(String progressDesc)
    {
        this.progressDesc = progressDesc;
    }

    public Icloud_CardA getCardA()
    {
        return cardA;
    }

    public void setCardA(Icloud_CardA cardA)
    {
        this.cardA = cardA;
    }

    public Icloud_Branch getBranch()
    {
        return branch;
    }

    public void setBranch(Icloud_Branch branch)
    {
        this.branch = branch;
    }

    public String getPackageReq()
    {
        return packageReq;
    }

    public void setPackageReq(String packageReq)
    {
        this.packageReq = packageReq;
    }

    public String getRemark()
    {
        return remark;
    }

    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public Icloud_ContractCraft getCraft()
    {
        return craft;
    }

    public void setCraft(Icloud_ContractCraft craft)
    {
        this.craft = craft;
    }

    public String getCustomerNo()
    {
        return customerNo;
    }

    public void setCustomerNo(String customerNo)
    {
        this.customerNo = customerNo;
    }

    public String getStartDate()
    {
        return startDate;
    }

    public void setStartDate(String startDate)
    {
        this.startDate = startDate;
    }

    public String getEndDate()
    {
        return endDate;
    }

    public void setEndDate(String endDate)
    {
        this.endDate = endDate;
    }

    public String getQueryFlag()
    {
        return queryFlag;
    }

    public void setQueryFlag(String queryFlag)
    {
        this.queryFlag = queryFlag;
    }

    public String getCraftNoList()
    {
        return craftNoList;
    }

    public void setCraftNoList(String craftNoList)
    {
        this.craftNoList = craftNoList;
    }

    public String getCustModelList()
    {
        return custModelList;
    }

    public void setCustModelList(String custModelList)
    {
        this.custModelList = custModelList;
    }

    public Integer getCompletedQty()
    {
        return completedQty;
    }

    public void setCompletedQty(Integer completedQty)
    {
        this.completedQty = completedQty;
    }

    public Integer getCanSendDeliveryQty()
    {
        return canSendDeliveryQty;
    }

    public void setCanSendDeliveryQty(Integer canSendDeliveryQty)
    {
        this.canSendDeliveryQty = canSendDeliveryQty;
    }

    public Integer getFeedPcsQty()
    {
        return feedPcsQty;
    }

    public void setFeedPcsQty(Integer feedPcsQty)
    {
        this.feedPcsQty = feedPcsQty;
    }

    public String getMergeId()
    {
        return mergeId;
    }

    public void setMergeId(String mergeId)
    {
        this.mergeId = mergeId;
    }

    public Integer getOccupiedAvailableQty()
    {
        return occupiedAvailableQty;
    }

    public void setOccupiedAvailableQty(Integer occupiedAvailableQty)
    {
        this.occupiedAvailableQty = occupiedAvailableQty;
    }

    public Icloud_Material getMaterial()
    {
        return material;
    }

    public void setMaterial(Icloud_Material material)
    {
        this.material = material;
    }

    public Integer getSpares()
    {
        return spares;
    }

    public void setSpares(Integer spares)
    {
        this.spares = spares;
    }

    public Integer getIsFeeding()
    {
        return isFeeding;
    }

    public void setIsFeeding(Integer isFeeding)
    {
        this.isFeeding = isFeeding;
    }

    public Integer getType()
    {
        return type;
    }

    public void setType(Integer type)
    {
        this.type = type;
    }

    public BigDecimal getPercent()
    {
        return percent;
    }

    public void setPercent(BigDecimal percent)
    {
        this.percent = percent;
    }

    public BigDecimal getSpec()
    {
        return spec;
    }

    public void setSpec(BigDecimal spec)
    {
        this.spec = spec;
    }

    public String getHascardA()
    {
        return hascardA;
    }

    public void setHascardA(String hascardA)
    {
        this.hascardA = hascardA;
    }

    public Integer getDeliveryQty()
    {
        return deliveryQty;
    }

    public void setDeliveryQty(Integer deliveryQty)
    {
        this.deliveryQty = deliveryQty;
    }

    public String getSaleName()
    {
        return saleName;
    }

    public void setSaleName(String saleName)
    {
        this.saleName = saleName;
    }

    public String getSaleCompanyId()
    {
        return saleCompanyId;
    }

    public void setSaleCompanyId(String saleCompanyId)
    {
        this.saleCompanyId = saleCompanyId;
    }

    public String getErpCustomerId()
    {
        return erpCustomerId;
    }

    public void setErpCustomerId(String erpCustomerId)
    {
        this.erpCustomerId = erpCustomerId;
    }

    public String getSaleId()
    {
        return saleId;
    }

    public void setSaleId(String saleId)
    {
        this.saleId = saleId;
    }

    public String getOneValue()
    {
        return oneValue;
    }

    public void setOneValue(String oneValue)
    {
        this.oneValue = oneValue;
    }

    public String getOneRemark()
    {
        return oneRemark;
    }

    public void setOneRemark(String oneRemark)
    {
        this.oneRemark = oneRemark;
    }

    public String getOneShortName()
    {
        return oneShortName;
    }

    public void setOneShortName(String oneShortName)
    {
        this.oneShortName = oneShortName;
    }

    public Integer getOneCustomerGradeNum()
    {
        return oneCustomerGradeNum;
    }

    public void setOneCustomerGradeNum(Integer oneCustomerGradeNum)
    {
        this.oneCustomerGradeNum = oneCustomerGradeNum;
    }

    public Integer getOneLogisticsTime()
    {
        return oneLogisticsTime;
    }

    public void setOneLogisticsTime(Integer oneLogisticsTime)
    {
        this.oneLogisticsTime = oneLogisticsTime;
    }

    public BigDecimal getOneCapacityPrecent()
    {
        return oneCapacityPrecent;
    }

    public void setOneCapacityPrecent(BigDecimal oneCapacityPrecent)
    {
        this.oneCapacityPrecent = oneCapacityPrecent;
    }

    public String getTwoValue()
    {
        return twoValue;
    }

    public void setTwoValue(String twoValue)
    {
        this.twoValue = twoValue;
    }

    public String getTwoRemark()
    {
        return twoRemark;
    }

    public void setTwoRemark(String twoRemark)
    {
        this.twoRemark = twoRemark;
    }

    public String getTwoShortName()
    {
        return twoShortName;
    }

    public void setTwoShortName(String twoShortName)
    {
        this.twoShortName = twoShortName;
    }

    public Integer getTwoCustomerGradeNum()
    {
        return twoCustomerGradeNum;
    }

    public void setTwoCustomerGradeNum(Integer twoCustomerGradeNum)
    {
        this.twoCustomerGradeNum = twoCustomerGradeNum;
    }

    public Integer getTwoLogisticsTime()
    {
        return twoLogisticsTime;
    }

    public void setTwoLogisticsTime(Integer twoLogisticsTime)
    {
        this.twoLogisticsTime = twoLogisticsTime;
    }

    public BigDecimal getTwoCapacityPrecent()
    {
        return twoCapacityPrecent;
    }

    public void setTwoCapacityPrecent(BigDecimal twoCapacityPrecent)
    {
        this.twoCapacityPrecent = twoCapacityPrecent;
    }

    public String getThreeValue()
    {
        return threeValue;
    }

    public void setThreeValue(String threeValue)
    {
        this.threeValue = threeValue;
    }

    public String getThreeRemark()
    {
        return threeRemark;
    }

    public void setThreeRemark(String threeRemark)
    {
        this.threeRemark = threeRemark;
    }

    public String getThreeShortName()
    {
        return threeShortName;
    }

    public void setThreeShortName(String threeShortName)
    {
        this.threeShortName = threeShortName;
    }

    public Integer getThreeCustomerGradeNum()
    {
        return threeCustomerGradeNum;
    }

    public void setThreeCustomerGradeNum(Integer threeCustomerGradeNum)
    {
        this.threeCustomerGradeNum = threeCustomerGradeNum;
    }

    public Integer getThreeLogisticsTime()
    {
        return threeLogisticsTime;
    }

    public void setThreeLogisticsTime(Integer threeLogisticsTime)
    {
        this.threeLogisticsTime = threeLogisticsTime;
    }

    public BigDecimal getThreeCapacityPrecent()
    {
        return threeCapacityPrecent;
    }

    public void setThreeCapacityPrecent(BigDecimal threeCapacityPrecent)
    {
        this.threeCapacityPrecent = threeCapacityPrecent;
    }

    public String getCondtionStr()
    {
        return condtionStr;
    }

    public void setCondtionStr(String condtionStr)
    {
        this.condtionStr = condtionStr;
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getNewDeliveryDays()
    {
        return newDeliveryDays;
    }

    public void setNewDeliveryDays(Date newDeliveryDays)
    {
        this.newDeliveryDays = newDeliveryDays;
    }

    public Integer getOueDays()
    {
        return oueDays;
    }

    public void setOueDays(Integer oueDays)
    {
        this.oueDays = oueDays;
    }

    public String getValue()
    {
        return value;
    }

    public void setValue(String value)
    {
        this.value = value;
    }

    public String getShortName()
    {
        return shortName;
    }

    public void setShortName(String shortName)
    {
        this.shortName = shortName;
    }

    public BigDecimal getOrderDeailArea()
    {
        return orderDeailArea;
    }

    public void setOrderDeailArea(BigDecimal orderDeailArea)
    {
        this.orderDeailArea = orderDeailArea;
    }

    public Integer getCustomerGradeNum()
    {
        return customerGradeNum;
    }

    public void setCustomerGradeNum(Integer customerGradeNum)
    {
        this.customerGradeNum = customerGradeNum;
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getEstimateDateCopy()
    {
        return estimateDateCopy;
    }

    public void setEstimateDateCopy(Date estimateDateCopy)
    {
        this.estimateDateCopy = estimateDateCopy;
    }

    public BigDecimal getCapacityPrecent()
    {
        return capacityPrecent;
    }

    public void setCapacityPrecent(BigDecimal capacityPrecent)
    {
        this.capacityPrecent = capacityPrecent;
    }

    public String getCapaUseStatus()
    {
        return capaUseStatus;
    }

    public void setCapaUseStatus(String capaUseStatus)
    {
        this.capaUseStatus = capaUseStatus;
    }

    public BigDecimal getNeedArea()
    {
        return needArea;
    }

    public void setNeedArea(BigDecimal needArea)
    {
        this.needArea = needArea;
    }

    public String getTotalPcses()
    {
        return totalPcses;
    }

    public void setTotalPcses(String totalPcses)
    {
        this.totalPcses = totalPcses;
    }

    public String getDayAreas()
    {
        return dayAreas;
    }

    public void setDayAreas(String dayAreas)
    {
        this.dayAreas = dayAreas;
    }

    public Boolean getCheckFlag()
    {
        return checkFlag;
    }

    public void setCheckFlag(Boolean checkFlag)
    {
        this.checkFlag = checkFlag;
    }

    public String getProductionCycleId()
    {
        return productionCycleId;
    }

    public void setProductionCycleId(String productionCycleId)
    {
        this.productionCycleId = productionCycleId;
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getEstimateStartDate()
    {
        return estimateStartDate;
    }

    public void setEstimateStartDate(Date estimateStartDate)
    {
        this.estimateStartDate = estimateStartDate;
    }

    public Boolean getIssueStatusFlag()
    {
        return issueStatusFlag;
    }

    public void setIssueStatusFlag(Boolean issueStatusFlag)
    {
        this.issueStatusFlag = issueStatusFlag;
    }

    public String getSaleCsutomerId()
    {
        return saleCsutomerId;
    }

    public void setSaleCsutomerId(String saleCsutomerId)
    {
        this.saleCsutomerId = saleCsutomerId;
    }

    public String getSaleCsutomerName()
    {
        return saleCsutomerName;
    }

    public void setSaleCsutomerName(String saleCsutomerName)
    {
        this.saleCsutomerName = saleCsutomerName;
    }

    public String getBranchQuery()
    {
        return branchQuery;
    }

    public void setBranchQuery(String branchQuery)
    {
        this.branchQuery = branchQuery;
    }

    public String getSalseManName()
    {
        return salseManName;
    }

    public void setSalseManName(String salseManName)
    {
        this.salseManName = salseManName;
    }

    public String getNeedBord()
    {
        return needBord;
    }

    public void setNeedBord(String needBord)
    {
        this.needBord = needBord;
    }

    public String getReplyStock()
    {
        return replyStock;
    }

    public void setReplyStock(String replyStock)
    {
        this.replyStock = replyStock;
    }

    public String getReplyremark()
    {
        return replyremark;
    }

    public void setReplyremark(String replyremark)
    {
        this.replyremark = replyremark;
    }

    public String getMaterialId()
    {
        return materialId;
    }

    public void setMaterialId(String materialId)
    {
        this.materialId = materialId;
    }

    public String getReplyMaterialName()
    {
        return replyMaterialName;
    }

    public void setReplyMaterialName(String replyMaterialName)
    {
        this.replyMaterialName = replyMaterialName;
    }

    public String getOccupiedStock()
    {
        return occupiedStock;
    }

    public void setOccupiedStock(String occupiedStock)
    {
        this.occupiedStock = occupiedStock;
    }

    public String getOldOccupiedStock()
    {
        return oldOccupiedStock;
    }

    public void setOldOccupiedStock(String oldOccupiedStock)
    {
        this.oldOccupiedStock = oldOccupiedStock;
    }

    public String getLeadTime()
    {
        return leadTime;
    }

    public void setLeadTime(String leadTime)
    {
        this.leadTime = leadTime;
    }

    public String getOccupiedStockCopy()
    {
        return occupiedStockCopy;
    }

    public void setOccupiedStockCopy(String occupiedStockCopy)
    {
        this.occupiedStockCopy = occupiedStockCopy;
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getEstimateDate()
    {
        return estimateDate;
    }

    public void setEstimateDate(Date estimateDate)
    {
        this.estimateDate = estimateDate;
    }

    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getEstimateDateTwo()
    {
        return estimateDateTwo;
    }

    public void setEstimateDateTwo(Date estimateDateTwo)
    {
        this.estimateDateTwo = estimateDateTwo;
    }

    public String getProductArea()
    {
        return productArea;
    }

    public void setProductArea(String productArea)
    {
        this.productArea = productArea;
    }

    public String getProductAreaCopy()
    {
        return productAreaCopy;
    }

    public void setProductAreaCopy(String productAreaCopy)
    {
        this.productAreaCopy = productAreaCopy;
    }

    public String getEstimateRemark()
    {
        return estimateRemark;
    }

    public void setEstimateRemark(String estimateRemark)
    {
        this.estimateRemark = estimateRemark;
    }

    public String getEstimateMethod()
    {
        return estimateMethod;
    }

    public void setEstimateMethod(String estimateMethod)
    {
        this.estimateMethod = estimateMethod;
    }

    public String getPurchRowDays()
    {
        return purchRowDays;
    }

    public void setPurchRowDays(String purchRowDays)
    {
        this.purchRowDays = purchRowDays;
    }

    public String getNotiNeedDays()
    {
        return notiNeedDays;
    }

    public void setNotiNeedDays(String notiNeedDays)
    {
        this.notiNeedDays = notiNeedDays;
    }

    public String getWaitDays()
    {
        return waitDays;
    }

    public void setWaitDays(String waitDays)
    {
        this.waitDays = waitDays;
    }

    public String getLogisticsTime()
    {
        return logisticsTime;
    }

    public void setLogisticsTime(String logisticsTime)
    {
        this.logisticsTime = logisticsTime;
    }

    public Integer getStarvingBoard()
    {
        return starvingBoard;
    }

    public void setStarvingBoard(Integer starvingBoard)
    {
        this.starvingBoard = starvingBoard;
    }

    public Integer getAvailableQty()
    {
        return availableQty;
    }

    public void setAvailableQty(Integer availableQty)
    {
        this.availableQty = availableQty;
    }

    public Integer getFeedStocks()
    {
        return feedStocks;
    }

    public void setFeedStocks(Integer feedStocks)
    {
        this.feedStocks = feedStocks;
    }

    public Integer getPurchRawNum()
    {
        return purchRawNum;
    }

    public void setPurchRawNum(Integer purchRawNum)
    {
        this.purchRawNum = purchRawNum;
    }

    public Integer getPurchNum()
    {
        return purchNum;
    }

    public void setPurchNum(Integer purchNum)
    {
        this.purchNum = purchNum;
    }

    public Integer getPurchStockNum()
    {
        return purchStockNum;
    }

    public void setPurchStockNum(Integer purchStockNum)
    {
        this.purchStockNum = purchStockNum;
    }

    public String getNotifiMaterialId()
    {
        return notifiMaterialId;
    }

    public void setNotifiMaterialId(String notifiMaterialId)
    {
        this.notifiMaterialId = notifiMaterialId;
    }

    public String getIcloud()
    {
        return icloud;
    }

    public void setIcloud(String icloud)
    {
        this.icloud = icloud;
    }

    public String getCancelCause()
    {
        return cancelCause;
    }

    public void setCancelCause(String cancelCause)
    {
        this.cancelCause = cancelCause;
    }

    public BigDecimal getExcessStock()
    {
        return excessStock;
    }

    public void setExcessStock(BigDecimal excessStock)
    {
        this.excessStock = excessStock;
    }

    public String getUserId()
    {
        return userId;
    }

    public void setUserId(String userId)
    {
        this.userId = userId;
    }

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    public String getContractDetailId()
    {
        return contractDetailId;
    }

    public void setContractDetailId(String contractDetailId)
    {
        this.contractDetailId = contractDetailId;
    }

    public String getProcessCardAId()
    {
        return processCardAId;
    }

    public void setProcessCardAId(String processCardAId)
    {
        this.processCardAId = processCardAId;
    }

    public String getFeedingId()
    {
        return feedingId;
    }

    public void setFeedingId(String feedingId)
    {
        this.feedingId = feedingId;
    }

    public String getDeliveryRequier()
    {
        return deliveryRequier;
    }

    public void setDeliveryRequier(String deliveryRequier)
    {
        this.deliveryRequier = deliveryRequier;
    }

    public String getCompleteDate()
    {
        return completeDate;
    }

    public void setCompleteDate(String completeDate)
    {
        this.completeDate = completeDate;
    }

    public String getFailedPcsQty()
    {
        return failedPcsQty;
    }

    public void setFailedPcsQty(String failedPcsQty)
    {
        this.failedPcsQty = failedPcsQty;
    }

    public String getCost()
    {
        return cost;
    }

    public void setCost(String cost)
    {
        this.cost = cost;
    }

    public String getInvalidQty()
    {
        return invalidQty;
    }

    public void setInvalidQty(String invalidQty)
    {
        this.invalidQty = invalidQty;
    }

    public String getIsReplenish()
    {
        return isReplenish;
    }

    public void setIsReplenish(String isReplenish)
    {
        this.isReplenish = isReplenish;
    }

    public Icloud_Price getPricees()
    {
        return pricees;
    }

    public void setPricees(Icloud_Price pricees)
    {
        this.pricees = pricees;
    }

    public String getCustomerId()
    {
        return customerId;
    }

    public void setCustomerId(String customerId)
    {
        this.customerId = customerId;
    }

    public Icloud_ContractDeail getConDeail()
    {
        return conDeail;
    }

    public void setConDeail(Icloud_ContractDeail conDeail)
    {
        this.conDeail = conDeail;
    }

    public String getCustomerModels()
    {
        return customerModels;
    }

    public void setCustomerModels(String customerModels)
    {
        this.customerModels = customerModels;
    }

    public Icloud_Feeding getFeeding()
    {
        return feeding;
    }

    public void setFeeding(Icloud_Feeding feeding)
    {
        this.feeding = feeding;
    }

    public String getStatus1()
    {
        return status1;
    }

    public void setStatus1(String status1)
    {
        this.status1 = status1;
    }

    public String getStatus2()
    {
        return status2;
    }

    public void setStatus2(String status2)
    {
        this.status2 = status2;
    }

    public String getStatus3()
    {
        return status3;
    }

    public void setStatus3(String status3)
    {
        this.status3 = status3;
    }

    public List<Icloud_ProduceBatch> getBatchList()
    {
        return batchList;
    }

    public void setBatchList(List<Icloud_ProduceBatch> batchList)
    {
        this.batchList = batchList;
    }

    public List<Icloud_Feeding> getFeedings()
    {
        return feedings;
    }

    public void setFeedings(List<Icloud_Feeding> feedings)
    {
        this.feedings = feedings;
    }

    public Integer getFeedPcsQtyes()
    {
        return feedPcsQtyes;
    }

    public void setFeedPcsQtyes(Integer feedPcsQtyes)
    {
        this.feedPcsQtyes = feedPcsQtyes;
    }

    public String getCustomerName()
    {
        return customerName;
    }

    public void setCustomerName(String customerName)
    {
        this.customerName = customerName;
    }

    public String getContractNo()
    {
        return contractNo;
    }

    public void setContractNo(String contractNo)
    {
        this.contractNo = contractNo;
    }

    public String getCustomerPo()
    {
        return customerPo;
    }

    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }

    public BigDecimal getPrice()
    {
        return price;
    }

    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }

    public Long getBoardLevel()
    {
        return boardLevel;
    }

    public void setBoardLevel(Long boardLevel)
    {
        this.boardLevel = boardLevel;
    }

    public Long getMaterialType()
    {
        return materialType;
    }

    public void setMaterialType(Long materialType)
    {
        this.materialType = materialType;
    }

    public Long getBoardThickness()
    {
        return boardThickness;
    }

    public void setBoardThickness(Long boardThickness)
    {
        this.boardThickness = boardThickness;
    }

    public Long getCopperCladThickness()
    {
        return copperCladThickness;
    }

    public void setCopperCladThickness(Long copperCladThickness)
    {
        this.copperCladThickness = copperCladThickness;
    }

    public Long getSurfaceProcess()
    {
        return surfaceProcess;
    }

    public void setSurfaceProcess(Long surfaceProcess)
    {
        this.surfaceProcess = surfaceProcess;
    }

    public Long getSolderMaskType()
    {
        return solderMaskType;
    }

    public void setSolderMaskType(Long solderMaskType)
    {
        this.solderMaskType = solderMaskType;
    }

    public Long getCharacterType()
    {
        return characterType;
    }

    public void setCharacterType(Long characterType)
    {
        this.characterType = characterType;
    }

    public Long getShapingWay()
    {
        return shapingWay;
    }

    public void setShapingWay(Long shapingWay)
    {
        this.shapingWay = shapingWay;
    }

    public Long getTestMethod()
    {
        return testMethod;
    }

    public void setTestMethod(Long testMethod)
    {
        this.testMethod = testMethod;
    }

    public BigDecimal getEngineeringFee()
    {
        return engineeringFee;
    }

    public void setEngineeringFee(BigDecimal engineeringFee)
    {
        this.engineeringFee = engineeringFee;
    }

    public BigDecimal getMouldFee()
    {
        return mouldFee;
    }

    public void setMouldFee(BigDecimal mouldFee)
    {
        this.mouldFee = mouldFee;
    }

    public BigDecimal getTestShelfFee()
    {
        return testShelfFee;
    }

    public void setTestShelfFee(BigDecimal testShelfFee)
    {
        this.testShelfFee = testShelfFee;
    }

    public BigDecimal getOthersFee()
    {
        return othersFee;
    }

    public void setOthersFee(BigDecimal othersFee)
    {
        this.othersFee = othersFee;
    }

    public BigDecimal getTotalAmt()
    {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt)
    {
        this.totalAmt = totalAmt;
    }

    public Long getOrderTypees()
    {
        return orderTypees;
    }

    public void setOrderTypees(Long orderTypees)
    {
        this.orderTypees = orderTypees;
    }

    public BigDecimal getPnlWidth()
    {
        return pnlWidth;
    }

    public void setPnlWidth(BigDecimal pnlWidth)
    {
        this.pnlWidth = pnlWidth;
    }

    public BigDecimal getPnlLength()
    {
        return pnlLength;
    }

    public void setPnlLength(BigDecimal pnlLength)
    {
        this.pnlLength = pnlLength;
    }

    public int getPnlDivisor()
    {
        return pnlDivisor;
    }

    public void setPnlDivisor(int pnlDivisor)
    {
        this.pnlDivisor = pnlDivisor;
    }

    public Long getOrderTypes()
    {
        return orderTypes;
    }

    public void setOrderTypes(Long orderTypes)
    {
        this.orderTypes = orderTypes;
    }

    public Icloud_BoardPart getPartA()
    {
        return partA;
    }

    public void setPartA(Icloud_BoardPart partA)
    {
        this.partA = partA;
    }

    public Icloud_BoardPart getPartB()
    {
        return partB;
    }

    public void setPartB(Icloud_BoardPart partB)
    {
        this.partB = partB;
    }

    public Integer getFindNoisEnable()
    {
        return findNoisEnable;
    }

    public void setFindNoisEnable(Integer findNoisEnable)
    {
        this.findNoisEnable = findNoisEnable;
    }

    public Integer getReadyQty()
    {
        return readyQty;
    }

    public void setReadyQty(Integer readyQty)
    {
        this.readyQty = readyQty;
    }

    public Integer getSavetypees()
    {
        return savetypees;
    }

    public void setSavetypees(Integer savetypees)
    {
        this.savetypees = savetypees;
    }

    public Icloud_BoardCutting getBoardCutting()
    {
        return boardCutting;
    }

    public void setBoardCutting(Icloud_BoardCutting boardCutting)
    {
        this.boardCutting = boardCutting;
    }

    public List<Icloud_BoardPartCraft> getBoardPartCraftList()
    {
        return boardPartCraftList;
    }

    public void setBoardPartCraftList(List<Icloud_BoardPartCraft> boardPartCraftList)
    {
        this.boardPartCraftList = boardPartCraftList;
    }

    public String getClerk()
    {
        return clerk;
    }

    public void setClerk(String clerk)
    {
        this.clerk = clerk;
    }

    public boolean isSelf()
    {
        return self;
    }

    public void setSelf(boolean self)
    {
        this.self = self;
    }

    public Date getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }

    public void setSentTimeStartQr(Date sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }

    public Date getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }

    public void setSentTimeEndQr(Date sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }

    public Date getSentTimeStartQr2()
    {
        return sentTimeStartQr2;
    }

    public void setSentTimeStartQr2(Date sentTimeStartQr2)
    {
        this.sentTimeStartQr2 = sentTimeStartQr2;
    }

    public Date getSentTimeEndQr2()
    {
        return sentTimeEndQr2;
    }

    public void setSentTimeEndQr2(Date sentTimeEndQr2)
    {
        this.sentTimeEndQr2 = sentTimeEndQr2;
    }

    public String getTime()
    {
        return time;
    }

    public void setTime(String time)
    {
        this.time = time;
    }

    public Integer getLateDays()
    {
        return lateDays;
    }

    public void setLateDays(Integer lateDays)
    {
        this.lateDays = lateDays;
    }

    public Boolean getIsModCraftNo()
    {
        return isModCraftNo;
    }

    public void setIsModCraftNo(Boolean isModCraftNo)
    {
        this.isModCraftNo = isModCraftNo;
    }

    public String getShowInfo()
    {
        return showInfo;
    }

    public void setShowInfo(String showInfo)
    {
        this.showInfo = showInfo;
    }

    public String getInlet()
    {
        return inlet;
    }

    public void setInlet(String inlet)
    {
        this.inlet = inlet;
    }

    public String getErpUserId()
    {
        return erpUserId;
    }

    public void setErpUserId(String erpUserId)
    {
        this.erpUserId = erpUserId;
    }

    public String getTakeHandWip()
    {
        return takeHandWip;
    }

    public void setTakeHandWip(String takeHandWip)
    {
        this.takeHandWip = takeHandWip;
    }

    public String getSearch()
    {
        return search;
    }

    public void setSearch(String search)
    {
        this.search = search;
    }

    public Icloud_ProduceBatchDetail getProduceBatchDetail()
    {
        return produceBatchDetail;
    }

    public void setProduceBatchDetail(Icloud_ProduceBatchDetail produceBatchDetail)
    {
        this.produceBatchDetail = produceBatchDetail;
    }

    public String getAcDistributeDateStr()
    {
        return acDistributeDateStr;
    }

    public void setAcDistributeDateStr(String acDistributeDateStr)
    {
        this.acDistributeDateStr = acDistributeDateStr;
    }

    public String getOrderDateStr()
    {
        return orderDateStr;
    }

    public void setOrderDateStr(String orderDateStr)
    {
        this.orderDateStr = orderDateStr;
    }

    public String getDeliveryDateStr()
    {
        return deliveryDateStr;
    }

    public void setDeliveryDateStr(String deliveryDateStr)
    {
        this.deliveryDateStr = deliveryDateStr;
    }

    public Integer getBitSuspend()
    {
        return bitSuspend;
    }

    public void setBitSuspend(Integer bitSuspend)
    {
        this.bitSuspend = bitSuspend;
    }

    public String getDevelierState()
    {
        return develierState;
    }

    public void setDevelierState(String develierState)
    {
        this.develierState = develierState;
    }

    public Integer getUseHouseQty()
    {
        return useHouseQty;
    }

    public void setUseHouseQty(Integer useHouseQty)
    {
        this.useHouseQty = useHouseQty;
    }

    public Integer getUseHouseRejQty()
    {
        return useHouseRejQty;
    }

    public void setUseHouseRejQty(Integer useHouseRejQty)
    {
        this.useHouseRejQty = useHouseRejQty;
    }

    public Integer getDeliveryFlag()
    {
        return deliveryFlag;
    }

    public void setDeliveryFlag(Integer deliveryFlag)
    {
        this.deliveryFlag = deliveryFlag;
    }

    public String getNoticeIds()
    {
        return noticeIds;
    }

    public void setNoticeIds(String noticeIds)
    {
        this.noticeIds = noticeIds;
    }

    public String getOrderTypeName()
    {
        return orderTypeName;
    }

    public void setOrderTypeName(String orderTypeName)
    {
        this.orderTypeName = orderTypeName;
    }

    public String getProduceType()
    {
        return produceType;
    }

    public void setProduceType(String produceType)
    {
        this.produceType = produceType;
    }

    public Integer getPrintNum()
    {
        return printNum;
    }

    public void setPrintNum(Integer printNum)
    {
        this.printNum = printNum;
    }

    public String getInkTypeVal()
    {
        return inkTypeVal;
    }

    public void setInkTypeVal(String inkTypeVal)
    {
        this.inkTypeVal = inkTypeVal;
    }

    public BigDecimal getSetWidth()
    {
        return setWidth;
    }

    public void setSetWidth(BigDecimal setWidth)
    {
        this.setWidth = setWidth;
    }

    public BigDecimal getSetLength()
    {
        return setLength;
    }

    public void setSetLength(BigDecimal setLength)
    {
        this.setLength = setLength;
    }

    public String getUnitLength()
    {
        return unitLength;
    }

    public void setUnitLength(String unitLength)
    {
        this.unitLength = unitLength;
    }

    public String getUnitWidth()
    {
        return unitWidth;
    }

    public void setUnitWidth(String unitWidth)
    {
        this.unitWidth = unitWidth;
    }

    public String getNotifiRemark()
    {
        return notifiRemark;
    }

    public void setNotifiRemark(String notifiRemark)
    {
        this.notifiRemark = notifiRemark;
    }

    public List<Icloud_PurchasingDetail> getPurDetailList()
    {
        return purDetailList;
    }

    public void setPurDetailList(List<Icloud_PurchasingDetail> purDetailList)
    {
        this.purDetailList = purDetailList;
    }

    public String getShowStatus()
    {
        return showStatus;
    }

    public void setShowStatus(String showStatus)
    {
        this.showStatus = showStatus;
    }

    public String getMaterialTypeValue()
    {
        return materialTypeValue;
    }

    public void setMaterialTypeValue(String materialTypeValue)
    {
        this.materialTypeValue = materialTypeValue;
    }

    public String getBoardLevelValue()
    {
        return boardLevelValue;
    }

    public void setBoardLevelValue(String boardLevelValue)
    {
        this.boardLevelValue = boardLevelValue;
    }

    public String getBoardThicknessValue()
    {
        return boardThicknessValue;
    }

    public void setBoardThicknessValue(String boardThicknessValue)
    {
        this.boardThicknessValue = boardThicknessValue;
    }

    public String getCopperCladThicknessValue()
    {
        return copperCladThicknessValue;
    }

    public void setCopperCladThicknessValue(String copperCladThicknessValue)
    {
        this.copperCladThicknessValue = copperCladThicknessValue;
    }

    public String getSurfaceProcessValue()
    {
        return surfaceProcessValue;
    }

    public void setSurfaceProcessValue(String surfaceProcessValue)
    {
        this.surfaceProcessValue = surfaceProcessValue;
    }

    public String getSolderMaskTypeValue()
    {
        return solderMaskTypeValue;
    }

    public void setSolderMaskTypeValue(String solderMaskTypeValue)
    {
        this.solderMaskTypeValue = solderMaskTypeValue;
    }

    public String getGroupCenterId()
    {
        return groupCenterId;
    }

    public void setGroupCenterId(String groupCenterId)
    {
        this.groupCenterId = groupCenterId;
    }

    public List<Icloud_NotificationMaterial> getNotifiMaterList()
    {
        return notifiMaterList;
    }

    public void setNotifiMaterList(List<Icloud_NotificationMaterial> notifiMaterList)
    {
        this.notifiMaterList = notifiMaterList;
    }

    public String getOldCraftNo()
    {
        return oldCraftNo;
    }

    public void setOldCraftNo(String oldCraftNo)
    {
        this.oldCraftNo = oldCraftNo;
    }

    public BigDecimal getEngineeringFeeCopy()
    {
        return engineeringFeeCopy;
    }

    public void setEngineeringFeeCopy(BigDecimal engineeringFeeCopy)
    {
        this.engineeringFeeCopy = engineeringFeeCopy;
    }

    public BigDecimal getMouldFeeCopy()
    {
        return mouldFeeCopy;
    }

    public void setMouldFeeCopy(BigDecimal mouldFeeCopy)
    {
        this.mouldFeeCopy = mouldFeeCopy;
    }

    public BigDecimal getTestShelfFeeCopy()
    {
        return testShelfFeeCopy;
    }

    public void setTestShelfFeeCopy(BigDecimal testShelfFeeCopy)
    {
        this.testShelfFeeCopy = testShelfFeeCopy;
    }

    public String getPriceId()
    {
        return priceId;
    }

    public void setPriceId(String priceId)
    {
        this.priceId = priceId;
    }

    public BigDecimal getPrdEngineeringFee()
    {
        return prdEngineeringFee;
    }

    public void setPrdEngineeringFee(BigDecimal prdEngineeringFee)
    {
        this.prdEngineeringFee = prdEngineeringFee;
    }

    public BigDecimal getPrdMouldFee()
    {
        return prdMouldFee;
    }

    public void setPrdMouldFee(BigDecimal prdMouldFee)
    {
        this.prdMouldFee = prdMouldFee;
    }

    public BigDecimal getPrdTestShelfFee()
    {
        return prdTestShelfFee;
    }

    public void setPrdTestShelfFee(BigDecimal prdTestShelfFee)
    {
        this.prdTestShelfFee = prdTestShelfFee;
    }

    public BigDecimal getPrdEngineeringFeeCopy()
    {
        return prdEngineeringFeeCopy;
    }

    public void setPrdEngineeringFeeCopy(BigDecimal prdEngineeringFeeCopy)
    {
        this.prdEngineeringFeeCopy = prdEngineeringFeeCopy;
    }

    public BigDecimal getPrdMouldFeeCopy()
    {
        return prdMouldFeeCopy;
    }

    public void setPrdMouldFeeCopy(BigDecimal prdMouldFeeCopy)
    {
        this.prdMouldFeeCopy = prdMouldFeeCopy;
    }

    public BigDecimal getPrdTestShelfFeeCopy()
    {
        return prdTestShelfFeeCopy;
    }

    public void setPrdTestShelfFeeCopy(BigDecimal prdTestShelfFeeCopy)
    {
        this.prdTestShelfFeeCopy = prdTestShelfFeeCopy;
    }

    public String getFactoryComId()
    {
        return factoryComId;
    }

    public void setFactoryComId(String factoryComId)
    {
        this.factoryComId = factoryComId;
    }

    public String getQcFlag()
    {
        return qcFlag;
    }

    public void setQcFlag(String qcFlag)
    {
        this.qcFlag = qcFlag;
    }

    public Double getSalePrecent()
    {
        return salePrecent;
    }

    public void setSalePrecent(Double salePrecent)
    {
        this.salePrecent = salePrecent;
    }

    public String getGroupDbId()
    {
        return groupDbId;
    }

    public void setGroupDbId(String groupDbId)
    {
        this.groupDbId = groupDbId;
    }

    public String getEgCardNo()
    {
        return egCardNo;
    }

    public void setEgCardNo(String egCardNo)
    {
        this.egCardNo = egCardNo;
    }

    public BigDecimal getPrdFilmFee()
    {
        return prdFilmFee;
    }

    public BigDecimal getPrdOthersFee()
    {
        return prdOthersFee;
    }

    public BigDecimal getPrdFilmFeeCopy()
    {
        return prdFilmFeeCopy;
    }

    public BigDecimal getPrdOthersFeeCopy()
    {
        return prdOthersFeeCopy;
    }

    public void setPrdFilmFee(BigDecimal prdFilmFee)
    {
        this.prdFilmFee = prdFilmFee;
    }

    public void setPrdOthersFee(BigDecimal prdOthersFee)
    {
        this.prdOthersFee = prdOthersFee;
    }

    public void setPrdFilmFeeCopy(BigDecimal prdFilmFeeCopy)
    {
        this.prdFilmFeeCopy = prdFilmFeeCopy;
    }

    public void setPrdOthersFeeCopy(BigDecimal prdOthersFeeCopy)
    {
        this.prdOthersFeeCopy = prdOthersFeeCopy;
    }

    public String getDictOrderType()
    {
        return dictOrderType;
    }

    public void setDictOrderType(String dictOrderType)
    {
        this.dictOrderType = dictOrderType;
    }

    public String getDictCardType()
    {
        return dictCardType;
    }

    public void setDictCardType(String dictCardType)
    {
        this.dictCardType = dictCardType;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public String getVersionDate()
    {
        return versionDate;
    }

    public void setVersionDate(String versionDate)
    {
        this.versionDate = versionDate;
    }

    public BigDecimal getMaxBatchArea()
    {
        return maxBatchArea;
    }

    public void setMaxBatchArea(BigDecimal maxBatchArea)
    {
        this.maxBatchArea = maxBatchArea;
    }

    public String getDeptName()
    {
        return deptName;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }

    public BigDecimal getExchangeRate()
    {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate)
    {
        this.exchangeRate = exchangeRate;
    }

    public String getExchangeDateStr()
    {
        if (exchangeDate != null)
        {
            return DateUtils.formatDate(exchangeDate);
        }
        return null;
    }

    public Date getExchangeDate()
    {
        return exchangeDate;
    }

    public void setExchangeDate(Date exchangeDate)
    {
        this.exchangeDate = exchangeDate;
    }

    public String getExchangeType()
    {
        return exchangeType;
    }

    public void setExchangeType(String exchangeType)
    {
        this.exchangeType = exchangeType;
    }

    public String getDaoreValue()
    {
        return daoreValue;
    }

    public void setDaoreValue(String daoreValue)
    {
        this.daoreValue = daoreValue;
    }

    public BigDecimal getLength()
    {
        return length;
    }

    public void setLength(BigDecimal length)
    {
        this.length = length;
    }

    public BigDecimal getWidth()
    {
        return width;
    }

    public void setWidth(BigDecimal width)
    {
        this.width = width;
    }

    public String getBoardThicknessId()
    {
        return boardThicknessId;
    }

    public void setBoardThicknessId(String boardThicknessId)
    {
        this.boardThicknessId = boardThicknessId;
    }

    public String getCopperCladThicknessId()
    {
        return copperCladThicknessId;
    }

    public void setCopperCladThicknessId(String copperCladThicknessId)
    {
        this.copperCladThicknessId = copperCladThicknessId;
    }

    public String getMaterialNo()
    {
        return materialNo;
    }

    public void setMaterialNo(String materialNo)
    {
        this.materialNo = materialNo;
    }

    public Integer getHours()
    {
        return hours;
    }

    public void setHours(Integer hours)
    {
        this.hours = hours;
    }

    public String getEconnmyId()
    {
        return econnmyId;
    }

    public void setEconnmyId(String econnmyId)
    {
        this.econnmyId = econnmyId;
    }

    public String getFactoryId()
    {
        return factoryId;
    }

    public void setFactoryId(String factoryId)
    {
        this.factoryId = factoryId;
    }

    public String getEconnmyName()
    {
        return econnmyName;
    }

    public void setEconnmyName(String econnmyName)
    {
        this.econnmyName = econnmyName;
    }

    public String getFactoryName()
    {
        return factoryName;
    }

    public void setFactoryName(String factoryName)
    {
        this.factoryName = factoryName;
    }

    public String getCusProfitRate()
    {
        return cusProfitRate;
    }

    public void setCusProfitRate(String cusProfitRate)
    {
        this.cusProfitRate = cusProfitRate;
    }
}
