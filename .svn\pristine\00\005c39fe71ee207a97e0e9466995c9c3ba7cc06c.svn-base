<%@ page contentType="text/html;charset=UTF-8" %>
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">财务管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="finance.collectbank">期末管理</a>
        </li>
    </ul>
</div>

<tabset class="tabset-margin-top">
    <tab heading="期末管理" active="ctrl.tabs.viewForm.active">
        <div class="rows">
            <div class="panel panel-default">
                <div class="panel-heading font-blue-hoki">查询</div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">结账时间：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <div class="input-prepend input-group">
                                            <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                            <input type="text" class="form-control" disable-auto-validate="true"
                                                   ng-blur="ctrl.initDate(ctrl.time)"
                                                   kyb-daterange
                                                   kyb-daterange-options="ctrl.rangeOptions"
                                                   ng-model="ctrl.time"
                                                   placeholder="请选择时间段">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default btn-default-width pull-right" ng-click="ctrl.queryData()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="portlet light bordered">
                <div class="row">
                    <div class="col-md-12 text-left">
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.closeCollectBank()"><i class="fa fa-adjust font-blue"></i> 结账</a>
                        <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="ctrl.cancelCollectBank()"><i class="fa fa-times font-red"></i> 反结账</a>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="row">
                        <div class="table-scrollable">
                            <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                                <thead>
                                <tr class="heading">
                                    <th colspan="1">编码</th>
                                    <th colspan="1">启用会计期间</th>
                                    <th colspan="1">当前会计期间</th>
                                    <th colspan="1">期间风险金</th>
                                    <th colspan="1">累计风险金</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="row in ctrl.page.data.list" ng-class="{ 'info': row.checkedFlag}" ng-click="ctrl.dbClikckDetailRow($index)">
                                    <td>{{row.no}}</td>
                                    <td>{{row.startDate}}</td>
                                    <td>{{row.endDate}}</td>
                                    <td>{{row.periodFee}}</td>
                                    <td>{{row.riskFee}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="row page-margin-top">
                            <div id="step5" class="col-md-12 col-lg-6">
                                <span class="inline">每页</span>
                                <select class="form-control inline"
                                        style="margin-top: 8px; width: 100px;"
                                        disable-valid-styling="true"
                                        disable-invalid-styling="true"
                                        ng-model="ctrl.page.pageSize"
                                        ng-change="ctrl.pageSizeChange()"
                                        ng-options="pageSizeOption for pageSizeOption in ctrl.page.pageSizeOptions">
                                </select>
                                <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示{{ctrl.page.data.startCount}} / {{ctrl.page.data.endCount}}条，共 {{ctrl.page.data.count}} 条</span>
                            </div>
                            <div id="step4" class="col-md-12 col-lg-6">
                                <paging class="pull-right" page="ctrl.page.data.pageNo"
                                        page-size="ctrl.page.data.pageSize"
                                        total="ctrl.page.data.count" adjacent="1" dots="..."
                                        scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                        active-class="active" disabled-class="disabled"
                                        show-prev-next="true"
                                        paging-action="ctrl.doPage(page, pageSize, total)">
                                </paging>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </tab>
</tabset>
<div class="row">
    <div id="closeCollectBank" class="modal fade" tabindex="-1" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-hidden="true"></button>
                    <h4 class="modal-title">
                        按月关账
                    </h4>
                </div>
                <div class="modal-body">
                    <input ng-model="ctrl.closeMonth" placeholder="关账月份" type="text" class="form-control" disabled/>
                </div>
                <div class="row" style="padding-top: 1rem;padding-left: 1rem;padding-right: 1rem;" ng-if="ctrl.processFlag && ctrl.processFlag == '1'">
                    <div class="col-md-12">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" ng-style="{ 'width': ctrl.processNum + '%' }">
                                <span class="sr-only">{{ctrl.processNum}}%</span>
                            </div>
                        </div>
                        <div>
                            <span>{{ctrl.dataNowMsg==""?"数据处理中":ctrl.dataNowMsg}}...{{ctrl.dataNowNum}}/{{ctrl.dataSumNum}}</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" ng-click="ctrl.closeBank()">关账</button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div id="downStatic" class="modal fade" tabindex="-1" data-keyboard="false" data-backdrop="static">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">导入数据提示</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top:25px;" ng-bind="ctrl.message"></div>
                    <div class="modal-footer">
                        <form action="a/finance/collect/closeBankExport" method="POST" enctype="multipart/form-data" target="hidden_frame">
                            <input type="text" ng-show="false" name="closeMonth" value="{{ctrl.closeMonth}}"/>
                            <div ng-if="ctrl.right.view" >
                                <button type="submit" class="btn btn-default" ng-click="ctrl.confimExcel()">确认</button>
                                <iframe name="hidden_frame" id="hidden_frame" style="display:none"></iframe>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div id="static" class="modal fade" tabindex="-1"
         data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                            aria-hidden="true"></button>
                    <h4 class="modal-title">
                        提示
                    </h4>
                </div>
                <div class="modal-body">
                    <p>
                        <span ng-bind="ctrl.message"></span>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                </div>
            </div>
        </div>
    </div>
</div>