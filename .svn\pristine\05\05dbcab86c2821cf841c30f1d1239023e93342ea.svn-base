<%@ page contentType="text/html;charset=UTF-8"%>
<div ng-intro-options="purRawCtrl.purchRawListOptions" ng-intro-method="purRawCtrl.helpList" ng-intro-autostart="inveCtrl.shouldAutoStart"></div>
<div ng-intro-options="purRawCtrl.purchRawDeailOptions" ng-intro-method="purRawCtrl.helpDetail" ng-intro-autostart="inveCtrl.shouldAutoStart"></div>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li><i class="fa fa-home"></i> <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i></li>
        <li><a href="javascript:void(0);">采购管理</a> <i class="fa fa-angle-right"></i>
        </li>
        <li><a ui-sref="purch.rawapply">原料申请单</a></li>
    </ul>
    <div class="page-toolbar">
    	<button class="btn default btn-fit-height pull-right" ng-click="purRawCtrl.help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>
</div>
<!-- END 导航-->

<tabset class="tabset-margin-top">
    <!-- START 原料申请单列表 -->
       <tab heading="原料申请单" active="purRawCtrl.tabs.purchRawList.active">
        <div id="rawApplyStep1" class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                	<div class="row">
                        <div class="col-md-6 col-lg-4">
                          	<div class="form-group">
	                              <label class="col-sm-3 col-md-4 control-label">物料：</label>
	                              <div class="col-sm-7 col-md-8">
	                                  <input type="text" class="form-control" ng-model="purRawCtrl.query.materialName" disable-valid-styling="true" disable-invalid-styling="true"/>
	                              </div>
                              </div>
                          </div>
                          <div class="col-md-6 col-lg-4">
                          	<div class="form-group">
                               <label class="col-sm-3 col-md-4 control-label">供应商：</label>
                               <div class="col-sm-7 col-md-8">
                                   <input type="text" class="form-control" ng-model="purRawCtrl.query.supplierName" disable-valid-styling="true" disable-invalid-styling="true"/>
                               </div>
                              </div>
                          </div>
                           <div class="col-md-6 col-lg-4">
                            	<div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">申请人：</label>
	                                <div class="col-sm-7 col-md-8">
	                                    <input type="text" class="form-control" ng-model="purRawCtrl.query.rawUserName.value" disable-valid-styling="true" disable-invalid-styling="true"/>
	                                </div>
                                </div>
                           </div>
                         <div class="col-md-6 col-lg-4">
                          	<div class="form-group">
	                              <label class="col-sm-3 col-md-4 control-label">申请单编号：</label>
	                              <div class="col-sm-7 col-md-8">
	                                  <input type="text" class="form-control" ng-model="purRawCtrl.query.purchApplyId" disable-valid-styling="true" disable-invalid-styling="true"/>
	                              </div>
                              </div>
                          </div>
<%--                          <div class="col-md-6 col-lg-4">
                          	<div class="form-group">
                               <label class="col-sm-3 col-md-4 control-label">通知单编号：</label>
                               <div class="col-sm-7 col-md-8">
                                   <input type="text" class="form-control" ng-model="purRawCtrl.query.notifiNo" disable-valid-styling="true" disable-invalid-styling="true"/>
                               </div>
                              </div>
                          </div> 
                          <div class="col-md-6 col-lg-4">
                          	<div class="form-group">
                               <label class="col-sm-3 col-md-4 control-label">归属公司：</label>
                               <div class="col-sm-7 col-md-8">
                                   <input type="text" class="form-control" ng-model="purRawCtrl.query.saleComName" disable-valid-styling="true" disable-invalid-styling="true"/>
                               </div>
                              </div>
                          </div> --%>
                          <div class="col-md-6 col-lg-4">
						    <div class="form-group">
							    <label class="col-sm-3 col-md-4 control-label">状态：</label>
							    <div class="col-sm-7 col-md-8">
								     <select class="form-control" ng-model="purRawCtrl.query.statusSelected"
                                     ng-options="gen.value as gen.name for gen in purRawCtrl.query.statusList"
                                     disable-auto-validate="true">
                                    </select>
							    </div>
						    </div>
						   </div>                         
                           <div class="col-md-6 col-lg-4" >
								<div class="form-group">
									<label class="col-sm-3 col-md-4 control-label">还未采购：</label>
									<div class="col-sm-7 col-md-7">
										<div class="task-checkbox">
											<checkbox ng-model="purRawCtrl.query.noPurch"></checkbox>
										</div>
									</div>
								</div>
							</div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">申请状态：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="purRawCtrl.query.typeSelected">
                                        <option value="">所有</option>
                                        <option value="1">特殊申请</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">拢单类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="purRawCtrl.query.mergeType">
                                        <option value="">所有</option>
                                        <option value="1">正常</option>
                                        <option value="2">被拢单</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">采购方式：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="purRawCtrl.query.purchaseWay">
                                        <option value="">所有</option>
                                        <option value="1">询价下单</option>
                                        <option value="2">比价下单</option>
                                        <option value="3">定向采购</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right" ng-click="purRawCtrl.doQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">原料申请单列表</div>
                <div id="rawApplyStep2" class="actions">
<!--                 	<div class="portlet-input input-inline input-small">
	                 	<button type="button" class="btn primary btn-default-width"
	                 	 href="javascript:void(0)" 
	                 	 ui-sref="purch.rawmaterial({contractId: purRawCtrl.allApplyIds})"
	                 	 ui-sref-opts="{reload: true}">生成采购单</button>
	                </div> -->
	                <div class="portlet-input input-inline input-small">
	                 	<button type="button" class="btn primary btn-default-width" ng-click="purRawCtrl.batchOperate(1)" ng-disabled="!purRawCtrl.right.edit"><i class="fa fa-plus"></i>批量确认</button>
	                </div>
                	<div class="portlet-input input-inline input-small">
	                 	<button type="button" class="btn primary btn-default-width" ng-click="purRawCtrl.batchOperate(2)" ng-disabled="!purRawCtrl.right.edit"><i class="fa fa-plus"></i>合并采购</button>
	                </div>
                 	<div class="portlet-input input-inline input-small">
	                    <button type="button" class="btn green btn-default-width" ng-click="purRawCtrl.editRaw()" ng-disabled="!purRawCtrl.right.edit"><i class="fa fa-plus"></i>特殊申请</button>
	                </div>
                    <div class="portlet-input input-inline input-small">
                        <button type="button" class="btn green btn-default-width" ng-click="purRawCtrl.batchPurchaseWay()" ng-disabled="!purRawCtrl.right.edit"><i class="fa fa-plus"></i>批量采购</button>
                    </div>
	                <div class="portlet-input input-inline input-small">
                        <form action="a/purch/rawApply/export" method="post" enctype="multipart/form-data" target="hidden_frame">
                            <input type="text" ng-show="false" name="materialName" value="{{purRawCtrl.query.materialName}}" /> 
                            <input type="text" ng-show="false" name="supplierName" value="{{purRawCtrl.query.supplierName}}" /> 
                            <input type="text" ng-show="false" name="createdBy.userName" value="{{purRawCtrl.query.rawUserName.value}}" />
                            <input type="text" ng-show="false" name="purchApplyId" value="{{purRawCtrl.query.purchApplyId}}" /> 
                            <input type="text" ng-show="false" name="notifiNo" value="{{purRawCtrl.query.notifiNo}}" />
                           	<input type="text" ng-show="false" name="saleComName" value="{{purRawCtrl.query.saleComName}}" /> 
                            <input type="text" ng-show="false" name="noPurch" value="{{purRawCtrl.query.noPurch}}"/>
                            <input type="text" ng-show="false" name="type" value="{{purRawCtrl.query.typeSelected}}"/>
                            <input type="text" ng-show="false" name="status" value="{{purRawCtrl.query.statusSelected}}"/>
                            <input type="text" ng-show="false" name="queryAll" value="{{purRawCtrl.queryAll}}"/>
                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出申请单</button>
                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="portlet-body">
                <div id="rawApplyStep3" class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                        <thead>
                        <tr class="heading">
                        	<th>
                        		<checkbox ng-model="purRawCtrl.allPageChecked" value="1" name="test" ng-click="purRawCtrl.selectAllPage()" ></checkbox>
                        	</th>
                            <th width="10%">操作</th>
                            <th>采购方式</th>
                            <th ng-class="{'sorting': purRawCtrl.sort.purchApplyId1.both, 'sorting_desc': purRawCtrl.sort.purchApplyId1.desc, 'sorting_asc': purRawCtrl.sort.purchApplyId1.asc}" ng-click="purRawCtrl.sortClick('purchApplyId1')">原料申请单编号</th>
                            <th>申请状态</th>
                          <%--  <th ng-class="{'sorting': purRawCtrl.sort.saleComName1.both, 'sorting_desc': purRawCtrl.sort.saleComName1.desc, 'sorting_asc': purRawCtrl.sort.saleComName1.asc}" ng-click="purRawCtrl.sortClick('saleComName1')">归属公司</th>
                            <th ng-class="{'sorting': purRawCtrl.sort.notifiNo1.both, 'sorting_desc': purRawCtrl.sort.notifiNo1.desc, 'sorting_asc': purRawCtrl.sort.notifiNo1.asc}" ng-click="purRawCtrl.sortClick('notifiNo1')">通知单编号</th>
                            <th ng-class="{'sorting': purRawCtrl.sort.deliveryDate1.both, 'sorting_desc': purRawCtrl.sort.deliveryDate1.desc, 'sorting_asc': purRawCtrl.sort.deliveryDate1.asc}" ng-click="purRawCtrl.sortClick('deliveryDate1')">到货日期</th>
                            <th>生产编号</th>
                            <th>采购结论</th>
                            <th ng-class="{'sorting': purRawCtrl.sort.purchRowDays1.both, 'sorting_desc': purRawCtrl.sort.purchRowDays1.desc, 'sorting_asc': purRawCtrl.sort.purchRowDays1.asc}" ng-click="purRawCtrl.sortClick('purchRowDays1')">采购天数</th>
                            <th ng-class="{'sorting': purRawCtrl.sort.notiNeedDays1.both, 'sorting_desc': purRawCtrl.sort.notiNeedDays1.desc, 'sorting_asc': purRawCtrl.sort.notiNeedDays1.asc}" ng-click="purRawCtrl.sortClick('notiNeedDays1')">生产天数</th>
                            <th ng-class="{'sorting': purRawCtrl.sort.estimateDate1.both, 'sorting_desc': purRawCtrl.sort.estimateDate1.desc, 'sorting_asc': purRawCtrl.sort.estimateDate1.asc}" ng-click="purRawCtrl.sortClick('estimateDate1')">预估交期</th>
                            <th>最迟采购日期</th>--%>
                            <th ng-class="{'sorting': purRawCtrl.sort.materialNo1.both, 'sorting_desc': purRawCtrl.sort.materialNo1.desc, 'sorting_asc': purRawCtrl.sort.materialNo1.asc}" ng-click="purRawCtrl.sortClick('materialNo1')">物料编号</th>
                            <th ng-class="{'sorting': purRawCtrl.sort.materialName1.both, 'sorting_desc': purRawCtrl.sort.materialName1.desc, 'sorting_asc': purRawCtrl.sort.materialName1.asc}" ng-click="purRawCtrl.sortClick('materialName1')">物料名称</th>
                            <th>单位</th>
                            <th ng-class="{'sorting': purRawCtrl.sort.quantity1.both, 'sorting_desc': purRawCtrl.sort.quantity1.desc, 'sorting_asc': purRawCtrl.sort.quantity1.asc}" ng-click="purRawCtrl.sortClick('quantity1')">数量</th>
                            <th ng-class="{'sorting': purRawCtrl.sort.craftDescript1.both, 'sorting_desc': purRawCtrl.sort.craftDescript1.desc, 'sorting_asc': purRawCtrl.sort.craftDescript1.asc}" ng-click="purRawCtrl.sortClick('craftDescript1')">规格要求</th>
                            <th>申请人</th>
                            <th>报备申请人</th>
                            <th>报备部门</th>
                            <th ng-class="{'sorting': purRawCtrl.sort.createdDate1.both, 'sorting_desc': purRawCtrl.sort.createdDate1.desc, 'sorting_asc': purRawCtrl.sort.createdDate1.asc}" ng-click="purRawCtrl.sortClick('createdDate1')">申请日期</th>
<%--                            <th>需求数量</th>--%>
                            <th>状态</th>
<%--                            <th>客户</th>--%>
<%--                            <th>拢单类型</th>--%>
                        </tr>
                        </thead>
                        <tbody ng-repeat="row in purRawCtrl.page.data.list">
                        <tr class="info">
                        	<td>
                        		<checkbox ng-model="row.checked" value="1" name="test" ng-if="row.mergeType != 1002"></checkbox>
                        	</td>
                            <td>
                                <a class="btn btn-xs btn-default" ng-if="purRawCtrl.right.edit && row.status == 1 && row.mergeType != 1002" href="javascript:void(0)" ng-click="purRawCtrl.delRaw($index)">
                                    <i class="fa fa-times font-red"></i> 删&nbsp;除</a>
                                <a ng-if="purRawCtrl.right.edit && row.status == 1 && row.mergeType != 1002" href="javascript:void(0)" class="btn btn-xs btn-default" ng-click="purRawCtrl.affirmPurchRaw($index)">
                                    <i class="fa fa-check font-green"></i> 确&nbsp;认</a>
                                <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-if="row.status == 2 && purRawCtrl.right.manage && row.mergeType != 1002" ng-click="purRawCtrl.approvePurchRaw($index)">
                                    <i class="fa fa-check-square-o font-green"></i> 审核</a>
                                <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-if="purRawCtrl.right.manage && row.status != 4 && row.status != 5 && row.status != 1 && row.status != 2 && row.status != 9 && row.mergeType != 1002" ng-click="purRawCtrl.canclePurchRaw($index)">
                                    <i class="icon-trash font-red"></i> 作废</a>
                                <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-if="purRawCtrl.right.edit && row.status == 2 && row.mergeType != 1002" ng-click="purRawCtrl.cancleAffirm($index)">
                                    <i class="icon-trash font-red"></i> 取消</a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="purRawCtrl.right.edit && row.status == 1 && row.mergeType == 1001 && !row.mergeId " ng-click="purRawCtrl.purchMerge(row)"><i class="fa fa-eraser font-red"></i>拢单</a>
                                <a href="javascript:void(0)" class="btn btn-xs btn-default" ng-if="purRawCtrl.right.edit && row.status == 1 && row.mergeType == 1003" ng-click="purRawCtrl.canclePurchMerge(row)"><i class="fa fa-reply font-red"></i>取消拢单</a>
<%--                                <a class="btn btn-xs btn-default" ui-sref="purch.supplierbidding({purchRawId: row.recordId})"  titie="供应商招标链接"  ng-if="purRawCtrl.right.edit && row.status == 7 && row.mergeId != 1002 && row.bidding==0"> <i class="fa fa-fighter-jet"></i>采购</a>--%>

                                <a class="btn btn-xs btn-default" ng-click="purRawCtrl.loadPurchaseWay(row)"  ng-if="purRawCtrl.right.edit && row.status == 7 && row.mergeId != 1002 && row.bidding==0"> <i class="fa fa-fighter-jet"></i>采购</a>
                            </td>
                            <td>
                                {{row.purchaseWayStr}}<span ng-if="row.purchaseWay == 1 || row.purchaseWay == 2">({{row.areaPrice}}/㎡)</span>
                            </td>
                            <td>
                                <a href="javascript:void(0)" ng-click="purRawCtrl.showPurchRaw($index)">{{row.purchApplyId}}
                                    <span ng-if="row.mergeType == 1003">(拢单)</span>
                                </a>
                            </td>
                            <td ng-bind="row.typeStr"></td>
                          <%--  <td ng-bind="row.saleComName"></td>
                            <td ng-bind="row.notifiNo"></td>
                            <td>
                            	{{row.endPurchDateTwoStr}}
                            </td>
                            <td>
                            	{{row.craftNo}}
                            </td>
                            <td>
                            	<span ng-if="row.productDays > 0" class="text-success">{{row.productResult}}</span>
	                        	<span ng-if="row.productDays == 0" class="text-warning">{{row.productResult}}</span>
	                        	<span ng-if="row.productDays < 0" class="text-danger">{{row.productResult}}</span>
                            </td>
                            <td>
                            	<input type="text" ng-disabled="true" class="form-control" ng-model="row.purchRowDays" ng-Onlynumber ng-blur="purRawCtrl.updatePurchDays(row)"/>
                            	<!-- {{row.purchRowDays}} -->
                            </td>
                            <td ng-bind="row.notiNeedDays"></td>
                            <td ng-bind="row.estimateDateStr"></td>
                            <td ng-bind="row.endPurchDateStr"></td>--%>
                            <td ng-bind="row.material.no"></td>
                            <td ng-bind="row.material.name"></td>
                            <td ng-bind="row.material.unit.value"></td>
                            <td ng-bind="row.quantity"></td>
                            <td ng-bind="row.craftDescript"></td>
                            <td ng-bind="row.createdBy.userName"></td>
                            <td ng-bind="row.filingApplicant"></td>
                            <td ng-bind="row.reportingDept"></td>
                            <td ng-bind="row.createdDate"></td>
<%--                            <td ng-bind="row.needBord"></td>--%>
                            <td ng-if="row.status == 1">
                                <span class="label label-sm label-default">未确认</span>
                            </td>
                            <td ng-if="row.status == 2">
                                <span class="label label-sm label-success">已确认</span>
                            </td>
                            <td ng-if="row.status == 4">
                                <span class="label label-sm label-danger">已作废</span>
                            </td>
                            <td ng-if="row.status == 5">
                                <span class="label label-sm label-danger">已完成</span>
                            </td>
                            <td ng-if="row.status == 7">
                                <span class="label label-sm label-warning">已审核</span>
                            </td>
                            <td ng-if="row.status == 9">
                                <span class="label label-sm label-danger">审批中</span>
                            </td>
<%--                            <td ng-bind="row.cusName"></td>--%>
<%--                            <td ng-bind="row.mergeTypeStr"></td>--%>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div class="row page-margin-top">
                    <div id="rawApplyStep4" class="col-md-12 col-lg-6">
                        <span class="inline">每页</span>
                        <select class="form-control inline"
                                style="margin-top: 8px; width: 100px;"
                                disable-valid-styling="true" disable-invalid-styling="true"
                                ng-model="purRawCtrl.page.pageSize"
                                ng-change="purRawCtrl.pageSizeChange()"
                                ng-options="pageSizeOption for pageSizeOption in purRawCtrl.page.pageSizeOptions">
                        </select>
                        <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示{{purRawCtrl.page.data.startCount}} / {{purRawCtrl.page.data.endCount}}条，共 {{purRawCtrl.page.data.count}} 条</span>
                    </div>
                    <div id="" class="col-md-12 col-lg-6">
                        <paging class="pull-right" page="purRawCtrl.page.data.pageNo"
                                page-size="purRawCtrl.page.data.pageSize"
                                total="purRawCtrl.page.data.count" adjacent="1" dots="..."
                                scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                active-class="active" disabled-class="disabled"
                                show-prev-next="true"
                                paging-action="purRawCtrl.doPage(page, pageSize, total)"> </paging>
                    </div>
                </div>
            </div>

        </div>
    </tab>
    <!-- END 原料申请单列表 -->
    <!-- END 设置默认数据-->
    <!--START 原料申请单明细操作  -->
     <tab active="purRawCtrl.tabs.purchRawDeail.active"
         ng-show="purRawCtrl.tabs.purchRawDeail.show">
        <tab-heading>
            	原料申请操作<i style="cursor: pointer" class="fa fa-times"
                       ng-click="purRawCtrl.hiddenRawDeail()"></i></tab-heading>
        <div class="rows">
            <div class="portlet light bordered ">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">
                        	原料申请{{purRawCtrl.rawTitle}}
                    </div>
                </div>
                <div id="rawDeail1" class="portlet-body form">
                    <form class="form-horizontal" role="form" name="purRawCtrl.detailForm"
                          novalidate="novalidate"
                          ng-submit="purRawCtrl.submitMod();"
                          ng-submit-force="true" ng-init="purRawCtrl.setFormScope(this)">
                        <div class="row">
                        
                          <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">原料申请单编号:</label>

                                    <div class="col-sm-7 col-md-8">
									<span class="form-control" ng-disabled="true"
                                          ng-bind="purRawCtrl.purchRaw.purchApplyId"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">主体类型:</label>
                                    <div class="col-sm-7 col-md-8">
                                        <select ng-model="purRawCtrl.purchRaw.subjectType" class="form-control" ng-click="purRawCtrl.getMaterialList(purRawCtrl.purchRaw.subjectType)">
                                            <option value="1">供应商</option>
                                            <option value="2">客户</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span
                                            class="required">*</span>物料编号:</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select 
                                        		on-select="purRawCtrl.getRawmaterialInfo()"
                                        		ng-model="purRawCtrl.purchRaw.material"
                                                ng-disabled="purRawCtrl.rawEditFlag"
                                                theme="bootstrap" register-custom-form-control required>
                                            <ui-select-match placeholder="请选择...">{{$select.selected.no}} </ui-select-match>
                                            <ui-select-choices refresh="purRawCtrl.seachMaterialList($select.search)" refresh-delay="1000"   repeat="item in purRawCtrl.materialList | filter: $select.search">
                                            <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                <small>
                               						<span style="color: blue;">名称：&nbsp;&nbsp;&nbsp;&nbsp;{{item.name}}<br></span>
                                                    <span style="color: red;">规格：&nbsp;&nbsp;&nbsp;&nbsp;{{item.specification}}</span>
                                                </small>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">物料名称:</label>

                                    <div class="col-sm-7 col-md-8">
									<span class="form-control" ng-disabled="true"
                                          ng-bind="purRawCtrl.purchRaw.material.name"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">单位:</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" ng-disabled="true" class="form-control"
                                               ng-model="purRawCtrl.purchRaw.material.unit.value"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span
                                            class="required">*</span>数量:</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-disabled="purRawCtrl.rawFlag"
                                               ng-model="purRawCtrl.purchRaw.quantity" ng-onlydecimal4s
                                               required/>
                                    </div>
                                </div>
                            </div>
                           <div class="col-md-6 col-lg-4" ng-if="purRawCtrl.purchRaw.subjectType == 1">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span
                                            class="required">*</span>供应商名称</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select required register-custom-form-control ng-model="purRawCtrl.purchRaw.material.supplierId" theme="bootstrap" ng-disabled="purRawCtrl.rawFlag">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.shortName}}</ui-select-match>
                                            <ui-select-choices
                                                    repeat="item.recordId as item in purRawCtrl.supplierList | filter: $select.search">
                                                <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                <small>
                                                	<span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.name}}<br></span>
                                                	<span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.shortName}}</span>
                                                </small>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
<!--                             <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">销售公司:</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="purRawCtrl.purchRaw.branchId" on-select = "purRawCtrl.changeSaleStockPlace($select.selected)" ng-disabled="purRawCtrl.rawFlag" theme="bootstrap">
                                            <ui-select-match  placeholder="请选择...">{{$select.selected.name}}
                                            </ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in purRawCtrl.branchList | filter: $select.search">
                                                <div ng-bind-html="item.name | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div> 
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>放至库位:</label>

                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="purRawCtrl.purchRaw.stockPlaceId" 
                                        		   ng-disabled="purRawCtrl.rawFlag"
                                                   theme="bootstrap" required>
                                            <ui-select-match
                                                    placeholder="请选择...">{{$select.selected.stockPlaceName}}
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="item.recordId as item in purRawCtrl.stockPlaceList | filter: $select.search">
                                                <div ng-bind-html="item.stockPlaceName | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div> -->
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">交货期限:</label>

                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="purRawCtrl.purchRaw.deliveryDays"
                                                   ng-disabled="purRawCtrl.rawFlag"
                                                   theme="bootstrap">
                                            <ui-select-match
                                                    placeholder="请选择...">{{$select.selected.value}}
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="item.recordId as item in purRawCtrl.deliveryDaysList | filter: $select.search">
                                                <div ng-bind-html="item.value | highlight: $select.search"></div>

                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label" ng-if="purRawCtrl.purchRaw.subjectType == 2">客户型号:</label>
                                    <label class="col-sm-3 col-md-4 control-label" ng-if="purRawCtrl.purchRaw.subjectType == 1">供应商型号:</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-disabled="purRawCtrl.rawFlag"
                                               ng-model="purRawCtrl.purchRaw.supplierModel"
                                               ng-maxlength="100"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">申请类型:</label>
                                    <div class="col-sm-7 col-md-8">
                                        <select class="form-control" ng-model="purRawCtrl.purchRaw.type" ng-disabled= "true">
                                            <option value=""></option>
                                            <option value="1">特殊申请</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
<%--                        <div class="row">--%>
<%--                        	<div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span
                                            class="required">*</span>销售公司:</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="purRawCtrl.purchRaw.branchId" on-select = "purRawCtrl.changeSaleStockPlace($select.selected)" ng-disabled="purRawCtrl.rawFlag" theme="bootstrap">
                                            <ui-select-match  placeholder="请选择...">{{$select.selected.name}}
                                            </ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in purRawCtrl.branchList | filter: $select.search">
                                                <div ng-bind-html="item.name | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>--%>
                           <%-- <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span
                                            class="required">*</span>部门:</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="purRawCtrl.purchRaw.groupDeptId" ng-disabled="purRawCtrl.rawFlag" theme="bootstrap" ng-disabled="purRawCtrl.rawFlag" required>
                                            <ui-select-match  placeholder="请选择...">{{$select.selected.groupOrgName}}
                                            </ui-select-match>
                                            <ui-select-choices repeat="item.groupOrgId as item in purRawCtrl.relationList | filter: $select.search">
                                                <div ng-bind-html="item.groupOrgName | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>--%>
<%--                        </div>--%>
						<div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">规格要求:</label>
                                    <div class="col-sm-7 col-md-10">
										<input type="text" class="form-control"
                                               disabled="disabled"
                                               ng-model="purRawCtrl.purchRaw.craftDescript"
                                               ng-maxlength="200"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label"><span class="required" ng-if="purRawCtrl.purchRaw.type == 1">*</span>备注：</label>
                                    <div class="col-sm-7 col-md-10">
										<textarea class="form-control" 
                                              ng-disabled="purRawCtrl.rawFlag"
                                              ng-model="purRawCtrl.purchRaw.remark"
                                              ng-maxlength="255"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                          <div align="center" class="row" 
                                               ng-show="!purRawCtrl.rawFlag">
                       <button class="btn green" ngf-select="purRawCtrl.selectUploadFile($file, $invalidFile)"
                          ngf-max-size="2MB" >
                        <span class="glyphicon glyphicon-cloud-upload"></span> 上传文件
                       </button>
                       <h6>仅支持.jpg,.png,.jpeg,.bmp,.gif,.docx,.doc,.xls,.xlsx文件</h6>
                    </div>
                    <br/>
                    <div class="row" class="form-group" style="margin-bottom: 5px;">
                            <!-- 显示附件 -->
                            <div class="col-md-12" ng-show="purRawCtrl.uFiles.length>0">
                                <table class="table table-hover">
                                    <thead>
                                    <tr class="uppercase">
                                        <th width="50%">名称</th>
                                        <th>大小</th>
                                        <th>进度</th>
                                        <th>操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat="item in purRawCtrl.uFiles">
                                        <td><strong>{{ item.name }}</strong></td>
                                        <td>{{ item.size/1024|number:3 }} KB</td>
                                        <td>
                                            <div class="progress progress-sm" style="margin-bottom: 0;">
                                                <div class="progress-bar progress-bar-info" role="progressbar" ng-style="{ 'width': item.progress + '%' }"></div>
                                            </div>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-danger btn-xs" ng-click="purRawCtrl.clearUpload($index)">
                                                <span class="glyphicon glyphicon-trash"></span> 移除
                                            </button>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                           <div class="col-md-6 col-lg-4" ng-repeat="wenjian in purRawCtrl.supplierFile">
                                <div class="form-group">
                                <div class="col-sm-8 col-md-8 control-label">
                                    <label>附件{{$index + 1}}：</label>
                                    <a href="{{wenjian.tempUrl}}">{{wenjian.realFileName}} 下载</a>
                                    <a ng-click="purRawCtrl.clearUploadCopy($index)" ng-show="!purRawCtrl.rawFlag" ng-if="purRawCtrl.right.edit">移除</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="stepDetail2" class="col-sm-offset-5">
                            <button type="submit" class="btn btn-primary btn-default-width"
                                    ng-class="{green: !purRawCtrl.rawFlag, purple: purRawCtrl.rawFlag}"
                                    ng-if="!purRawCtrl.rawFlag"
                                    style="margin-left: 15px;" ng-disabled="!purRawCtrl.right.edit">
                                <i class="fa fa-save"></i> {{purRawCtrl.rawTitle}}
                            </button>
                            <button type="reset" class="btn btn-default btn-default-width"
                                    ng-click="purRawCtrl.resetRaw()"
                                    style="margin-left: 15px;"
                                    ng-if="!purRawCtrl.rawFlag && !purRawCtrl.rawEditFlag"  ng-disabled="!purRawCtrl.right.edit"
                                    >
                                <i class="fa fa-mail-reply"></i> 重&nbsp;置
                            </button>
                            
                            
                        </div>
                    </form>
                </div>
            </div>
        </div>
     </tab>
    <!--END 原料申请单明细操作  -->
    <tab heading="报备待购清单" active="purRawCtrl.tabs.purchSummaryForm.active" ng-click="purRawCtrl.doSummaryQuery()">
        <div class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">创建时间：</label>
                                <div class="col-sm-7 col-md-8">
                                    <div class="input-prepend input-group">
                                        <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                        <input type="text" class="form-control" disable-auto-validate="true" ng-blur="purRawCtrl.initDate(purRawCtrl.time)"  kyb-daterange kyb-daterange-options="purRawCtrl.rangeOptions" ng-model="purRawCtrl.time" placeholder="请选择时间段">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">确认月份：</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select ng-model="purRawCtrl.periodQuery" theme="bootstrap">
                                        <ui-select-match placeholder="请选择...">{{$select.selected}}</ui-select-match>
                                        <ui-select-choices repeat="item in purRawCtrl.periodList | filter: $select.search">
                                            <div ng-bind-html="item | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">名称：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="purRawCtrl.nameQuery" disable-valid-styling="true" disable-invalid-styling="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">规格：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="purRawCtrl.matSpecificationQuery" disable-valid-styling="true" disable-invalid-styling="true" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">备注：</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="purRawCtrl.remarksQuery" disable-valid-styling="true" disable-invalid-styling="true" />
                                </div>
                            </div>
                        </div>
<%--                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">安全库存：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="purRawCtrl.purchasedQtyFlag">
                                        <option value="">所有</option>
                                        <option value="1">已标红</option>
                                        <option value="2">未标红</option>
                                    </select>
                                </div>
                            </div>
                        </div>--%>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">申购类型：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="purRawCtrl.subscriptionType">
                                        <option value="1">备料</option>
                                        <option value="2">按单订料</option>
                                        <option value="">全部</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">部门展示：</label>
                                <div class="col-sm-7 col-md-8">
                                    <select class="form-control" ng-model="purRawCtrl.departmentHideType">
                                        <option value="1">显示</option>
                                        <option value="2">隐藏</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4" >
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">待申购：</label>
                                <div class="col-sm-7 col-md-7">
                                    <div class="task-checkbox">
                                        <checkbox ng-model="purRawCtrl.pendingApplicationFlag"></checkbox>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row pull-right">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width "
                                    ng-click="purRawCtrl.doSummaryQuery()">
                                <i class="fa fa-search"></i> 查&nbsp;询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <form class="form-horizontal">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">备料清单列表</div>
                </div>
                <div class="portlet-body">
                    <div class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading">
                                <th style="text-align: center;width: 10%" rowspan="2">操作</th>
                                <th style="text-align: center;width: 20%" colspan="3">规格</th>
                                <th style="text-align: center;width: 15%" colspan="2">工厂库存</th>
                                <th style="text-align: center;width: 15%" colspan="3">生成采购</th>
                                <th style="text-align: center;width: 5%" rowspan="2">安全库存</th>
                                <th style="text-align: center;" colspan="{{purRawCtrl.relationList.length}}" ng-show="purRawCtrl.relationList && purRawCtrl.relationList.length > 0 && purRawCtrl.departmentHideType == 1">需求备料数量</th>
                            </tr>
                            <tr>
                                <th style="text-align: center;width: 5%" rowspan="1">名称</th>
                                <th style="text-align: center;width: 10%" rowspan="1" colspan="2">规格</th>
                                <th style="text-align: center;width: 5%" rowspan="1">剩余库存</th>
                                <th style="text-align: center;width: 5%" rowspan="1">在途库存</th>
                                <th style="text-align: center;width: 5%" rowspan="1">已申请数量</th>
                                <th style="text-align: center;width: 5%" rowspan="1">待采购数量</th>
                                <th style="text-align: center;width: 5%" rowspan="1">汇总</th>
                                <th style="text-align: center;width: 8%" rowspan="1"  ng-repeat="dept in purRawCtrl.relationList" ng-show="purRawCtrl.relationList && purRawCtrl.relationList.length > 0 && purRawCtrl.departmentHideType == 1">{{dept.groupOrgName}}</th>
                                <%--<th style="text-align: center;width: 5%" rowspan="1">备注</th>--%>
                            </tr>
                            </thead>
                            <tbody ng-repeat="row in purRawCtrl.summaryPage.data.list" ng-if="purRawCtrl.relationList && purRawCtrl.relationList.length > 0">
                            <tr ng-class="{'danger': row.colorFlag}">
                                <td style="text-align: center;" colspan="1">
                                    <a href="javascript:void(0);" ng-if="purRawCtrl.right.edit" class="btn btn-xs btn-default" ng-click="purRawCtrl.setOpenFlag(row,1)">
                                        <i class="fa fa-check font-green"></i>{{row.openFlag ? '收缩' : '展开'}}
                                    </a>
                                    <a href="javascript:void(0);" ng-if="purRawCtrl.right.edit" class="btn btn-xs btn-default" ng-click="purRawCtrl.setOpenFlag(row,2)">
                                        <i class="fa fa-check font-green"></i>申购
                                    </a>
                                </td>
                                <td style="text-align: center;" rowspan="1" ng-bind="row.matName"></td>
                                <td style="text-align: center;" rowspan="1" colspan="2" ng-bind="row.matSpecification"></td>
                                <td style="text-align: center;" rowspan="1">
                                    <span ng-if="row.surplusStocks">
                                        {{row.surplusStocks}}
                                    </span>
                                    <span ng-if="!row.surplusStocks">
                                        -
                                    </span>
                                </td>
                                <td style="text-align: center;" rowspan="1">
                                    <span ng-if="row.transitStocks">
                                        {{row.transitStocks}}
                                    </span>
                                    <span ng-if="!row.transitStocks">
                                        -
                                    </span>
                                </td>
                                <td style="text-align: center;" rowspan="1">
                                    <span ng-if="row.purchSotcks">
                                        {{row.purchSotcks}}
                                    </span>
                                    <span ng-if="!row.purchSotcks">
                                        -
                                    </span>
                                </td>
                                <td style="text-align: center;" rowspan="1">
                                    <span ng-if="row.purchasedQty">
                                        {{row.purchasedQty}}
                                    </span>
                                    <span ng-if="!row.purchasedQty">
                                        -
                                    </span>
                                </td>
                                <td style="text-align: center;" rowspan="1" ng-bind="row.sumQuantity"></td>
                                <td style="text-align: center;" rowspan="1" ng-bind="row.safeStock"></td>
                                <td style="text-align: center;" rowspan="1" ng-repeat="org in row.list" ng-show="purRawCtrl.departmentHideType == 1">
                                    <a href="javascript:void(0)" ng-click="purRawCtrl.showUseDataOpen(org)">
                                    <span ng-if="org.showStr">
                                       {{org.showStr}}
                                    </span></a>
                                    <span ng-if="!org.showStr">
                                        -
                                    </span>
                                </td>
                                <%--<td style="text-align: center;" rowspan="1" ng-bind="row.remark"></td>--%>
                            </tr>
                            <tr ng-if="row.openFlag && row.applyMatList && row.applyMatList.length > 0">
                                <td style="text-align: center;" rowspan="1" ng-repeat="org in mat.list"></td>
                                <td style="text-align: center;" rowspan="1"></td>
                                <td style="text-align: center;" rowspan="1">
                                    <input type="text" class="form-control" ng-model="row.query1" />
                                </td>
                                <td style="text-align: center;" rowspan="1">
                                    <input type="text" class="form-control" ng-model="row.query2" />
                                </td>
                                <td style="text-align: center;" rowspan="1" >
                                    <input type="text" class="form-control" ng-model="row.query3" />
                                </td>
                                <td style="text-align: center;" rowspan="1"></td>
                                <td style="text-align: center;" rowspan="1"></td>
                                <td style="text-align: center;" rowspan="1"></td>
                                <td style="text-align: center;" rowspan="1"></td>
                                <td style="text-align: center;" rowspan="1"></td>
                                <td style="text-align: center;" rowspan="1"></td>
                                <td style="text-align: center;" rowspan="1" ng-show="purRawCtrl.departmentHideType == 1"></td>
                                <td style="text-align: center;" rowspan="1" ng-show="purRawCtrl.departmentHideType == 1"></td>
                                <td style="text-align: center;" rowspan="1" ng-show="purRawCtrl.departmentHideType == 1"></td>
                                <td style="text-align: center;" rowspan="1" ng-show="purRawCtrl.departmentHideType == 1"></td>
                                <td style="text-align: center;" rowspan="1" ng-show="purRawCtrl.departmentHideType == 1"></td>
                                <td style="text-align: center;" rowspan="1" ng-show="purRawCtrl.departmentHideType == 1"></td>
                                <td style="text-align: center;" rowspan="1" ng-show="purRawCtrl.departmentHideType == 1"></td>
                                <td style="text-align: center;" rowspan="1" ng-show="purRawCtrl.departmentHideType == 1"></td>
                            </tr>
                            <tr ng-if="row.openFlag && row.applyMatList && row.applyMatList.length > 0" ng-repeat="mat in row.applyMatList| filter:{
                                    'no':row.query1,
                                    'name':row.query2,
                                    'specification':row.query3}"  ng-class="{'success': true}">
                                <td style="text-align: center;" colspan="1">
                                    <a href="javascript:void(0);" ng-if="purRawCtrl.right.edit" class="btn btn-xs btn-default" ng-click="purRawCtrl.setApplyPruchRawOpen(mat,1)">
                                        <i class="fa fa-check font-green"></i>申购
                                    </a>
                                    <%--                              <a href="javascript:void(0);" ng-if="purRawCtrl.right.edit" class="btn btn-xs btn-default" ng-click="purRawCtrl.applyOpen(row)">
                                                                      <i class="fa fa-check font-green"></i>申请
                                                                  </a>--%>
                                </td>
                                <td style="text-align: center;" rowspan="1" ng-bind="mat.no"></td>
                                <td style="text-align: center;" rowspan="1" ng-bind="mat.name"></td>
                                <td style="text-align: center;" rowspan="1" ng-bind="mat.specification"></td>
                                <td style="text-align: center;" rowspan="1" ng-bind="mat.stocks ? mat.stocks : 0"></td>
                                <td style="text-align: center;" rowspan="1" ng-bind="mat.transitStocks ? mat.transitStocks : 0"></td>
                                <td style="text-align: center;" rowspan="1" colspan="4"></td>
                                <td style="text-align: center;" rowspan="1" ng-repeat="org in mat.list" ng-show="purRawCtrl.departmentHideType == 1">
<%--                                    <input type="text" class="form-control" ng-model="org.quantityCopy" ng-onlynumber ng-blur="purRawCtrl.computPurchQty(org)" ng-disabled="!org.showStr"/>--%>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row page-margin-top">
                        <div class="col-md-12 col-lg-6">
                            <span class="inline">每&nbsp;页</span>
                            <select
                                    class="form-control inline" style="margin-top: 8px; width: 100px;"
                                    disable-valid-styling="true" disable-invalid-styling="true"
                                    ng-model="purRawCtrl.summaryPage.pageSize"
                                    ng-change="purRawCtrl.summaryPageSizeChange()"
                                    ng-options="pageSizeOption for pageSizeOption in purRawCtrl.summaryPage.pageSizeOptions">
                            </select>
                            <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;
                            当前显示{{purRawCtrl.summaryPage.data.startCount}} / {{purRawCtrl.summaryPage.data.endCount}}条，共 {{purRawCtrl.summaryPage.data.count}} 条</span>
                        </div>
                        <div class="col-md-12 col-lg-6">
                            <paging class="pull-right" page="purRawCtrl.summaryPage.data.pageNo"
                                    page-size="purRawCtrl.summaryPage.data.pageSize"
                                    total="purRawCtrl.summaryPage.data.count" adjacent="1" dots="..."
                                    scroll-top="false" hide-if-empty="false" ul-class="pagination"
                                    active-class="active" disabled-class="disabled"
                                    show-prev-next="true"
                                    paging-action="purRawCtrl.doSummaryPage(page, pageSize, total)"></paging>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </tab>
</tabset>

<div class="modal fade bs-modal-lg" id="staticPurchhMerge" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title" >
                    <span>拢单原料申请单(物料:{{purRawCtrl.showPurch.material.no}}-{{purRawCtrl.showPurch.material.name}}-{{purRawCtrl.showPurch.material.specification}})</span>
                </h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="portlet light bordered">
                        <div class="actions">
                            <div class="portlet-input input-inline input-small" >
                                <button type="button" class="btn green btn-default-width" ng-click="purRawCtrl.genPurchMerge()">拢单</button>
                            </div>
                        </div>
                        <div class="portlet-body">
                            <div class="row form-group">
                                <div class="table-scrollable">
                                    <table class="table table-bordered table-hover">
                                        <tr >
                                            <td width="5%">
                                                <checkbox ng-model="purRawCtrl.allPageCheckedTwo" value="1" name="test" ng-click="purRawCtrl.selectAllPageTwo()"></checkbox>
                                            </td>
                                            <td width="20%">申请单编号</td>
                                            <td width="10%">申请类型</td>
                                            <td width="10%">数量</td>
                                            <td width="10%">申请人</td>
                                            <td width="10%">申请方式</td>
                                            <td width="10%">申请时间</td>
                                            <td width="10%">到货日期</td>
                                        </tr>
                                        <tr  ng-repeat ="item in purRawCtrl.waitingPurchMergeList">
                                            <td>
                                                <checkbox ng-model="item.checked"></checkbox>
                                            </td>
                                            <td>{{item.purchApplyId}}</td>
                                            <td>{{item.typeStr}}</td>
                                            <td>{{item.quantity}}</td>
                                            <td>{{item.createdBy.userName}}</td>
                                            <td>{{item.applyMethod}}</td>
                                            <td>{{item.createdDate}}</td>
                                            <td>{{item.endPurchDateTwoStr}}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>



<div class="row">
    <div class="col-md-12">
        <div id="staticRemove" class="modal fade" tabindex="-1"
             data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"
                                aria-hidden="true"></button>
                        <h4 class="modal-title">删除采购申请</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="col-sm-1 text-center">
                            <i class="fa fa-warning font-yellow"
                               style="font-size: 30px !important;"></i>
                        </div>
                        <div class="col-sm-11">确认删除该申请吗？</div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue"
                                ng-click="purRawCtrl.delPurchRaw()">确定
                        </button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="row">
	<div class="col-md-12">
		<div id="staticGenaPurchRow" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">供应商修改</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="form-horizontal">
							<div class="row">
								<div class="col-md-12">
									 <div class="form-group">
	                                    <label class="control-label col-md-3"><span class="required">*</span>供应商</label>
	                                    <div class="col-md-8">
	                                        <ui-select required register-custom-form-control ng-model="purRawCtrl.purchRowList[0].supplierId" theme="bootstrap">
	                                            <ui-select-match placeholder="请选择...">{{$select.selected.shortName}}</ui-select-match>
	                                            <ui-select-choices
	                                                    repeat="item.recordId as item in purRawCtrl.supplierList | filter: $select.search">
	                                                <div ng-bind-html="item.no | highlight: $select.search"></div>
	                                                <small>
	                                                	<span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.name}}<br></span>
	                                                	<span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.shortName}}</span>
	                                                </small>
	                                            </ui-select-choices>
	                                        </ui-select>
	                                    </div>
	                                </div>
								</div>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue" ng-click="purRawCtrl.genaPurchRow()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div>
	<div class="col-md-12">
		<div id="batchConfirmStatic" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
						<h4 class="modal-title">批量确认申请单</h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 25px;">
						<div class="form-horizontal">
							您确定要批量确认申请单?
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" data-dismiss="modal" class="btn blue" ng-click="purRawCtrl.batchConfirmPurchRow()">确定</button>
						<button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


<div class="row">
	<div class="col-md-12">
		<div class="modal fade" id="mergePurchRawStatic" tabindex="-1" role="dialog" aria-hidden="true">
		    <div class="modal-dialog modal-full">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span class="text-primary">
                        	合并采购
                        </span></h4>
					</div>
					<div class="modal-body clearfix" style="padding-top: 5px;">
						<form class="form-horizontal">
							<div class="portlet light bordered">
								<div class="actions">
									<div class="portlet-input input-inline input-small" ng-if="purRawCtrl.right.edit">
										<button type="button" class="btn primary btn-default-width" ng-click="purRawCtrl.toPruch()">批量采购</button>
									</div>
		               		    </div>
                 				<div class="portlet-body">
									<div class="row form-group">
										 <div class="table-scrollable">
					                      <table class="table table-striped table-bordered table-condensed table-advance table-hover">
					                          <thead>
					                          <tr class="heading">
					                          	<th width="5%">
					                        		<checkbox ng-model="purRawCtrl.allChecked" value="1" name="test" ng-click="purRawCtrl.selectAll()"></checkbox>
					                        	</th>
					                          	<th width="8%">物料编号</th>
					                          	<th width="8%">物料名称</th>
					                          	<th width="15%">规格要求</th>
					                          	<th width="6%">数量</th>
					                          	<th width="8%">申请单号</th>
					                          	<th width="8%">供应商名称</th>
					                          	<th width="8">通知单号</th>
					                          	<th width="8%">预估交期</th>
					                          	<th width="8%">最迟采购日期</th>
					                          	<th width="5%">申请人</th>
					                          	<th width="10%">操作</th>
					                          </tr>
									          <tr>
									          	<td></td>
									          	<td>
													<input type="text" class="form-control" ng-model="purRawCtrl.materialNoSearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="purRawCtrl.materialNameSearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="purRawCtrl.craftDescriptSearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="purRawCtrl.quantitySearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="purRawCtrl.purchRawNoSearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="purRawCtrl.supplierNameSearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="purRawCtrl.notifiNoSearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="purRawCtrl.estimateDateStrSearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="purRawCtrl.endPurchDateStrSearch">
												</td>
												<td>
													<input type="text" class="form-control" ng-model="purRawCtrl.createdByNameSearch">
												</td>
												<td></td>
											 </tr>
					                          </thead>
					                          <tbody ng-repeat="(index1,item) in purRawCtrl.mergePurchRawList">
					                          	<tr ng-repeat="(index2,row) in item.purchRawList |
													filter:{
														'materialNo':purRawCtrl.materialNoSearch,
														'materialName':purRawCtrl.materialNameSearch,
														'craftDescript':purRawCtrl.craftDescriptSearch,
														'quantity':purRawCtrl.quantitySearch,
														'purchApplyId':purRawCtrl.purchRawNoSearch,
														'supplierName':purRawCtrl.supplierNameSearch,
														'notifiNo':purRawCtrl.notifiNoSearch,
														'estimateDateStr':purRawCtrl.estimateDateStrSearch,
														'endPurchDateStr':purRawCtrl.endPurchDateStrSearch,
														'createdByName':purRawCtrl.createdByNameSearch
													}" class="success">
													<td ng-if="index2 == 0" rowspan={{item.purchRawList.length}}>
														<checkbox ng-model="item.checked" ng-clikc="purRawCtrl.setRecordIds()"></checkbox>
													</td>
													<td ng-if="index2 == 0" rowspan={{item.purchRawList.length}}>{{item.materialNo}}</td>
													<td ng-if="index2 == 0" rowspan={{item.purchRawList.length}}>{{item.materialName}}</td>
					                          		<td ng-if="index2 == 0" rowspan={{item.purchRawList.length}}>{{item.craftDescript}}</td>
					                          		<td ng-if="index2 == 0" rowspan={{item.purchRawList.length}}>{{item.quantity}}</td>
					                          		<td>{{row.purchApplyId}}</td>
					                          		<td>{{row.supplierName}}</td>
					                          		<td>{{row.notifiNo}}</td>
					                          		<td>{{row.estimateDateStr}}</td>
					                          		<td>{{row.endPurchDateStr}}</td>
					                          		<td>{{row.createdByName}}</td>
					                          		<td ng-if="index2 == 0" rowspan={{item.purchRawList.length}}>
														<button type="button" class="btn primary btn-default-width" ng-click="purRawCtrl.toPruch(item.recordId)">采购</button>
					                          		</td>
					                          	</tr>
					                          </tbody>
					                      </table>
					                  </div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<%--<div class="row">
    <div class="col-md-12">
        <div class="modal fade" id="applyMatStatic" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-full">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span class="text-primary">
                        	申请备料：物料信息({{purRawCtrl.applyMatCopy.matName}}-{{purRawCtrl.applyMatCopy.matSpecification}}) / 总数量({{purRawCtrl.applyMatCopy.sumQuantity}})
                        </span></h4>
                    </div>
                        <form class="form-horizontal">
                            <div class="modal-body clearfix" style="padding-top: 5px;">
                            <div class="portlet light bordered">
                                <div class="row">
                                    <div class="col-md-6 col-lg-6">
                                        <div class="form-group">
                                            <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>申购材料：</label>
                                            <div class="col-sm-7 col-md-7">
                                                <ui-select  ng-model="purRawCtrl.applyMat.materialId" required theme="bootstrap">
                                                    <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
                                                    <ui-select-choices repeat="item.recordId as item in purRawCtrl.applyMatList | filter: $select.search">
                                                        <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                        <small>
                                                            <span><span style="color: blue;">名称:</span>{{item.name}}<br></span>
                                                            <span><span style="color: blue;">规格:</span>{{item.specification}}<br></span>
                                                        </small>
                                                    </ui-select-choices>
                                                </ui-select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-6">
                                        <div class="form-group">
                                            <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>总申购数量：</label>
                                            <div class="col-sm-7 col-md-8">
                                                <input type="text" class="form-control" ng-model="purRawCtrl.applyMat.sumQuantity" ng-onlynumber ng-blur="purRawCtrl.checkSumQuantity()"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="portlet-body">
                                    <div class="row form-group">
                                        <div class="table-scrollable">
                                            <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                                                <thead>
                                                <tr class="heading">
                                                    <th width="20%">部门</th>
                                                    <th width="20%">待申购数量</th>
                                                    <th width="20%">申购数量</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr ng-repeat="row in purRawCtrl.applyMat.list" class="success" ng-if="row.quantity > 0">
                                                    <td ng-bind="row.groupOrgName"></td>
                                                    <td ng-bind="row.quantity"></td>
                                                    <td>
                                                        <input type="text" class="form-control" ng-model="row.quantityCopy" ng-onlynumber ng-blur="purRawCtrl.computPurchQty(2,row)"/>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn blue" ng-click="purRawCtrl.saveApplyMat()">申请</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>--%>

<div class="row">
    <div class="col-md-12">
        <div id="applyMatStatic" class="modal fade" tabindex="-1"
             data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">申购</h4>
                    </div>
                    <div class="modal-body">
                        <p><span ng-bind="purRawCtrl.message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="purRawCtrl.saveApplyMatData()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="applyMatMessageStatic" class="modal fade" tabindex="-1" data-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">申购</h4>
            </div>
            <div class="modal-body">
                <div class="row border-bottom font-size-h3">
                    <div class="col-md-12">
                        <div>原材料名称：{{purRawCtrl.applyMatCopy.matName}}</div>
                        <div style="padding-top: 1rem;">原材料规格：{{purRawCtrl.applyMatCopy.matSpecification}}</div>
                    </div>
                </div>
                <div class="form-group row" style="border-bottom: 1px solid #7a7a7a; padding-bottom: 1rem;padding-top: 3rem;" ng-if="purRawCtrl.showSelMat">
                    <label class="col-sm-3 col-md-2 control-label font-default"><span class="required">*</span>物料</label>
                    <div class="col-sm-7 col-md-8">
                        <ui-select on-select="purRawCtrl.setApplyPruchRawOpen(purRawCtrl.saveApplyMat,2)" ng-model="purRawCtrl.saveApplyMat"
                                theme="bootstrap" register-custom-form-control required>
                            <ui-select-match placeholder="请选择...">{{$select.selected.no}} </ui-select-match>
                            <ui-select-choices repeat="item in purRawCtrl.applyMat.applyMatList | filter: $select.search">
                                <div ng-bind-html="item.no | highlight: $select.search"></div>
                                <small>
                                    <span style="color: blue;">名称：&nbsp;&nbsp;&nbsp;&nbsp;{{item.name}}<br></span>
                                    <span style="color: red;">规格：&nbsp;&nbsp;&nbsp;&nbsp;{{item.specification}}<br></span>
                                    <span style="color:red;">库存量:&nbsp;&nbsp;&nbsp;&nbsp;{{item.stocks}}</span>
                                </small>
                            </ui-select-choices>
                        </ui-select>
                    </div>
                </div>
                <div ng-if="purRawCtrl.saveApplyMat && purRawCtrl.saveApplyMat.recordId">
                    <div class="form-group row" style="border-bottom: 1px solid #7a7a7a; padding-bottom: 1rem;">
                        <div class="col-md-6">
                            物料编号
                        </div>
                        <div class="col-md-6 text-right">
                            {{purRawCtrl.saveApplyMat.no}}
                        </div>
                    </div>
                    <div class="form-group row" style="border-bottom: 1px solid #7a7a7a; padding-bottom: 1rem;">
                        <div class="col-md-6">
                            物料名称
                        </div>
                        <div class="col-md-6 text-right">
                            {{purRawCtrl.saveApplyMat.name}}
                        </div>
                    </div>
                    <div class="form-group row" style="border-bottom: 1px solid #7a7a7a; padding-bottom: 1rem;">
                        <div class="col-md-4">
                            物料规格
                        </div>
                        <div class="col-md-8 text-right">
                            {{purRawCtrl.saveApplyMat.specification}}
                        </div>
                    </div>
                    <div class="form-group row" style="border-bottom: 1px solid #7a7a7a; padding-bottom: 1rem;">
                        <div class="col-md-6">
                            工厂库存:&nbsp;{{purRawCtrl.applyMat.surplusStocks+purRawCtrl.applyMat.transitStocks}}
                        </div>
                        <div class="col-md-6 text-right">
                            在途采购:&nbsp;{{purRawCtrl.applyMat.transitStocks}}
                        </div>
                    </div>
                    <div class="form-group row" style="border-bottom: 1px solid #7a7a7a; padding-bottom: 1rem;">
                        <div class="col-md-6">
                            待采购数量
                        </div>
                        <div class="col-md-6 text-right">
                            {{purRawCtrl.applyMat.purchasedQty}}
                        </div>
                    </div>
                    <div class="form-group row"  ng-class="{'bg-danger': purRawCtrl.applyMat.colorFlag}">
                        <div class="col-md-6">
                            安全库存
                        </div>
                        <div class="col-md-6 text-right">
                            {{purRawCtrl.applyMat.safeStock}}
                        </div>
                    </div>
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                        <tr class="heading">
                            <th style="text-align: center;">部门</th>
                            <th style="text-align: center;">报备</th>
                            <th style="text-align: center;">待采购</th>
                            <th style="text-align: center;">本次采购</th>
                        </tr>
                        <tr ng-repeat ="row in purRawCtrl.saveApplyMat.list" ng-show="row.showStr">
                            <td style="text-align: center;">{{row.groupOrgName}}</td>
                            <td style="text-align: center;">{{row.showStr}}</td>
                            <td style="text-align: center;">{{row.quantity}}</td>
                            <td style="text-align: center;">
                                <input type="text" class="form-control" ng-model="row.quantityCopy" ng-blur="purRawCtrl.checkQuantity(row)">
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn blue" ng-click="purRawCtrl.saveApplyMatData()">生成采购</button>
                <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="modal fade bs-modal-lg" id="staticUseData" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-full">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">
                            <span>报备明细</span>
                        </h4>
                    </div>
                    <div class="modal-body">
                        <table class="table table-bordered table-hover">
                            <tr>
                                <th>类型</th>
                                <th>数量</th>
                                <th>面积㎡</th>
                                <th>备注</th>
                                <th>确认时间</th>
                                <th>到货日期</th>
                                <th>申请人</th>
                                <th>申请人号码</th>
                                <th>确认人</th>
                                <th>确认人号码</th>
                                <th>已申请数量</th>
                                <th>操作</th>
                            </tr>
                            <tr ng-repeat ="row in purRawCtrl.detailsOfPreparation.preparationList" ng-class="{'success': true}">
                                <td>
                                    <span ng-if="row.groupCenterId">按单订料</span>
                                    <span ng-if="!row.groupCenterId">备料</span>
                                </td>
                                <td>{{row.quantity}}</td>
                                <td>{{row.area}}</td>
                                <td>{{row.remark}}</td>
                                <td>{{row.confirmTimeStr}}</td>
                                <td>{{row.arrivalDate}}</td>
                                <td>{{row.createdByName}}</td>
                                <td>{{row.confirmatorNumber}}</td>
                                <td>{{row.confirmUserName}}</td>
                                <td>{{row.applicantNumber}}</td>
                                <td>{{row.subscribedQty}}</td>
                                <td>
                                    <a href="javascript:void(0);" class="btn btn-xs btn-default" ng-if="!row.subscribedQty || row.subscribedQty == 0 || row.quantity > row.subscribedQty " ng-click="purRawCtrl.confirmButtonOpen(row)">
                                        <i class="fa fa-check font-green"></i>无需采购
                                    </a>&nbsp;
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="modal-footer">
                        <div class="portlet-input input-inline input-small">
                            <button type="button" data-dismiss="modal" class="btn btn-default">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="row">
    <div class="col-md-12">
        <div class="modal fade" id="confirmButtonOpen" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title"><span class="text-primary">
                        	按单订单无需采购
                        </span></h4>
                    </div>
                    <div class="portlet-body" style="padding-top: 5px;">
                        <form id="infoStep1" class="form-horizontal" name="editForm" ng-init="purRawCtrl.setFormScope(this)" novalidate="novalidate" ng-submit="purRawCtrl.confirmReasonTwo()" ng-submit-force="true">
                            <div class="row">
                                <div class="col-md-8 col-lg-8">
                                    <div class="form-group">
                                        <label class="col-sm-3 col-md-4 control-label"><span class="text-danger">*</span>无需采购原因：</label>
                                        <div class="col-sm-8 col-md-8">
                                            <textarea class="form-control" required ng-model="purRawCtrl.contentReason"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="infoStep7" class="form-group">
                                <div class="col-sm-offset-4 col-sm-10">
                                    <button type="submit" class="btn btn-primary btn-default-width btn-margin-left-15" ><i class="fa fa-save"></i>&nbsp;&nbsp;保&nbsp;&nbsp;存</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div id="purchaseWayWindow" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">采购</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="form-horizontal">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label col-md-3"><span class="required">*</span>采购方式：</label>
                                        <div class="col-md-8">
                                            <select class="form-control" ng-model="purRawCtrl.query.purchaseWayT">
                                                <option value="1">询价下单</option>
                                                <option value="2">比价下单</option>
                                                <option value="3">定向采购</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="portlet light bordered" ng-if="purRawCtrl.query.purchaseWayT === '2' || purRawCtrl.query.purchaseWayT == '3'">
                                <div class="portlet-body">
                                    <div class="row form-group">
                                        <div class="table-scrollable">
                                            <table class="table table-bordered table-hover">
                                                <thead>
                                                <tr class="heading">
                                                    <th>品牌</th>
                                                    <th>价格</th>
                                                    <th>平米单价</th>
                                                    <th>操作</th>
                                                </tr>
                                                </thead>
                                                <tbody ng-repeat="item in purRawCtrl.materialManuList">
                                                    <tr>
                                                        <td ng-bind="item.manufacturer"></td>
                                                        <td ng-bind="item.price"></td>
                                                        <td ng-bind="item.areaPrice"></td>
                                                        <td>
                                                            <span ng-if="item.useFlag === '1'"><button type="button" class="btn blue" readonly="readonly">使用中</button></span>
                                                            <span ng-if="item.useFlag !== '1'"><button type="button" ng-click="purRawCtrl.getConcreteMatList(item)" class="btn red">使用</button></span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                    </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" ng-click="purRawCtrl.updatePurhchaseWay()" class="btn blue">确认</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="purchaseWayWindowTwo" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">采购</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="form-horizontal">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label col-md-3"><span class="required">*</span>采购方式：</label>
                                        <div class="col-md-8">
                                            <select class="form-control" ng-model="purRawCtrl.query.purchaseWayT">
                                                <option value="1">询价下单</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" ng-click="purRawCtrl.updatePurhchaseWay()" class="btn blue">确认</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div id="concreteMatModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                        <h4 class="modal-title">选择具体料</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="form-horizontal">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="control-label col-md-3"><span class="required">*</span>具体料:</label>
                                        <div class="col-sm-8">
                                            <ui-select ng-model="purRawCtrl.concreteMatId" theme="bootstrap" register-custom-form-control required>
                                                <ui-select-match placeholder="请选择...">{{$select.selected.no}} </ui-select-match>
                                                <ui-select-choices repeat="item.recordId as item in purRawCtrl.concreteMatList | filter: $select.search">
                                                    <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                    <small>
                                                        <span style="color: blue;">名称：&nbsp;&nbsp;&nbsp;&nbsp;{{item.name}}<br></span>
                                                        <span style="color: red;">规格：&nbsp;&nbsp;&nbsp;&nbsp;{{item.specification}}</span>
                                                    </small>
                                                </ui-select-choices>
                                            </ui-select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="purRawCtrl.updateUseFlag()">确定</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<%--<div class="row">
    <div class="col-md-12">
        <div id="confirmButtonOpen" class="modal fade" tabindex="-1"
             data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"
                                aria-hidden="true"></button>
                        <h4 class="modal-title">操作提示</h4>
                    </div>
                    <div class="modal-body clearfix" style="padding-top: 25px;">
                        <div class="col-sm-1 text-center">
                            <i class="fa fa-warning font-yellow"
                               style="font-size: 30px !important;"></i>
                        </div>
                        <div class="form-group">
                            <label>无需采购原因:</label>
                            <textarea class="form-control" ng-model="purRawCtrl.contentReason" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn blue" ng-click="purRawCtrl.confirmReasonTwo()"
                        >确认</button>
                        <button type="button" data-dismiss="modal" class="btn btn-default" >取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>--%>

<div class="row">
    <div class="col-md-12">
        <div id="static" class="modal fade" tabindex="-1"
             data-keyboard="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"
                                aria-hidden="true"></button>
                        <h4 class="modal-title">提示</h4>
                    </div>
                    <div class="modal-body">
                        <p><span ng-bind="purRawCtrl.message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
