package com.kyb.pcberp.modules.production.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.production.entity.CapacityRecord;
import com.kyb.pcberp.modules.production.entity.CycleHoliday;
import com.kyb.pcberp.modules.production.entity.Deliveryschedule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface DeliveryscheduleDao extends CrudDao<Deliveryschedule> {
    List<Deliveryschedule> getCapacityDeailList(Deliveryschedule deliveryschedule);
    List<CapacityRecord> findByMaxVersionRecord(@Param("companyId") String companyId,
                                                @Param("processValueId") String processValueId, @Param("versionDate") String versionDate);
    List<CycleHoliday> getHolidayList(@Param("companyId") String companyId);

}
