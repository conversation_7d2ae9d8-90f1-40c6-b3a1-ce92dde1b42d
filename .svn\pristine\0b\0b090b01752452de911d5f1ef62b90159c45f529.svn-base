<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="d-flex flex-column flex-root">
    <div class="d-flex flex-row flex-column-fluid page">
        <div class="d-flex flex-column flex-row-fluid wrapper">
            <div class="content d-flex flex-column flex-column-fluid"
                 style="background-image: url(${pageContext.request.contextPath}/assets/global/metronic/assets/media/bg/bgMain.jpg);height: 180px;">
                <div class="subheader subheader-transparent">
                    <div class="container d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <h5 class="text-white my-2 mr-5">消息通知</h5>
                            </div>
                        </div>
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <a href="#" class="btn btn-transparent-warning font-weight-bold mr-2" v-on:click="quickOperation">
                                    <i class="flaticon-questions-circular-button"></i>快捷操作
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-column-fluid">
                    <div class="container">
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="card card-custom gutter-b">
                                    <div class="card-body pt-0 pl-3 pr-3">
                                        <div class="row">
                                            <div class="col-xl-12">
                                                <ul class="nav nav-tabs nav-tabs-line mb-2" style="width: 100%;">
                                                    <li class="nav-item" style="width: 50%;">
                                                        <a class="nav-link active" data-toggle="tab" v-on:click="changeDataList(1)">
                                                            <span class="nav-icon"><i class="flaticon2-chat-1"></i></span>
                                                            <span class="nav-text font-size-h5">消息</span>
                                                        </a>
                                                    </li>
                                                    <li class="nav-item text-center" style="width: 50%;">
                                                        <a class="nav-link" data-toggle="tab" v-on:click="changeDataList(2)">
                                                            <span class="nav-icon"><i class="flaticon-support"></i></span>
                                                            <span class="nav-text font-size-h5">通讯录</span>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-xl-12">
                                                <div class="input-icon">
                                                    <input type="text" class="form-control" v-model="serchMsg" placeholder="请输入要搜索的内容" v-on:click="getDataList"/>
                                                    <span><i class="flaticon2-search-1 icon-md"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="pt-3 pb-7">
                                            <div class="row pt-2 pb-2 border-bottom align-items-center" v-for="item in messageList" :key="item.recordId" v-if="pageFlag == 1" v-on:click="updateFlag(item)">
                                                <div class="col-xl-12">
                                                    <div class="row align-items-center">
                                                        <div class="col-6">
                                                            <i class="flaticon-alert text-primary"></i>
                                                            <span class="font-size-h5" v-if="item.status == 1">通知公告</span>
                                                            <span class="font-size-h5" v-if="item.status == 2">用户消息</span>
                                                        </div>
                                                        <div class="col-6 text-right">
                                                            {{item.createdDate}}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-xl-12">
                                                    <div class="pt-2">
                                                        <span class="text-primary" v-if="item.flag == 1">{{item.message}}</span>
                                                        <span class="text-muted" v-if="item.flag == 2">{{item.message}}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row pt-2 pb-2 border-bottom" v-for="item in phoneBookList" :key="item.recordId" v-if="pageFlag == 2">
                                                <div class="col-xl-12 font-weight-bolder">
                                                    {{item.companyName}}-{{item.position}}
                                                </div>
                                                <div class="col-xl-12">
                                                    <div class="row align-items-center">
                                                        <div class="col-6">
                                                            <div class="d-flex align-items-center">
                                                                <div class="symbol symbol-success pr-3" v-if="item.downloadUrl">
                                                                    <img alt="Pic" :src="item.downloadUrl"/>
                                                                </div>
                                                                <div class="d-flex flex-column text-left">
                                                                    <span v-if="item.realName">{{item.realName}}</span>
                                                                    <span v-else-if="item.userName">{{item.userName}}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-6 text-right">
                                                            <a :href="item.phone">{{item.phone}}</a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>