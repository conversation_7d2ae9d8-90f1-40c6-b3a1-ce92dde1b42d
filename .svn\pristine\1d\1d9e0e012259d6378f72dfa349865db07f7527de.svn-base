package com.kyb.pcberp.modules.finance.dao;

import java.math.BigDecimal;
import java.util.List;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.finance.entity.PayMoney;
import com.kyb.pcberp.modules.purch.entity.PayApplication;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.User;
import org.apache.ibatis.annotations.Param;

@MyBatisDao
public interface PayMoneyDao extends CrudDao<PayMoney>
{

    /**
     * 原料出入库时，采购入库生成的付款对账单的付款单查询
     *
     * @param pm
     * @return
     */
    List<PayMoney> findPayMoneyListByBean(PayMoney pm);

    // 查询该申请单已支付金额
    BigDecimal findAlPayAmountSumByPayApplication(PayApplication payApplication);

    List<PayMoney> getReceiveAccountsBySupplierId(PayMoney pa);

    List<PayMoney> getPayAccounts(PayMoney pa);

    /**
     * 导出
     *
     * @param detial
     * @return
     */
    List<PayMoney> findExpList(PayMoney pa);

    /**
     * ycy 2016-09-30 财务付款初始化数据
     *
     * @param pa
     * @return
     */
    List<PayMoney> initializationPayMoney(PayMoney pa);

    /**
     * ojh 2016.10.25 查询这条预付款记录被系统关联生成的应付款记录
     */
    int getDoBySystemCount(PayMoney pmBeWrtOff);

    /**
     * ycy 2016-10-24 删除付款报表数据
     *
     * @param pa
     */
    void deletePayMoney(Company company);

    /**
     * ycy 2016-10-24 删除付款报表数据
     *
     * @param pa
     */
    void deletePayMoneyTow(Company company);

    /**
     * zjn 2018-01-30 根据冲红记录id查询预付款记录
     */
    PayMoney findPayMoney(PayMoney payMoney);

    /**
     * zjn 2019-01-22 根据报表跟单员查询付款记录
     */
    List<PayMoney> getPayMoneyByCreatedId(User user);

    void insertPayMoney(PayMoney payMoney);

    Integer getApplicationCount(@Param("recordId") String recordId);

    Integer findDocumentNo(@Param("no") String no, @Param("companyId") String companyId);
}
