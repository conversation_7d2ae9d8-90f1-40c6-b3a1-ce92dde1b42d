<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.icloud.erp.dict.dao.Icloud_ErpDictDao">
    <select id="loadDictList" resultType="com.kyb.pcberp.modules.icloud.erp.dict.pojo.Icloud_DictItem">
		SELECT
			a.recordId AS "valId",
			a.`value`,
			b.recordId,
			b.itemName,
			a.companyId AS "company.recordId"
			FROM md_dict_value a
		LEFT JOIN md_dict_item b ON b.recordId=a.itemId
		WHERE a.companyId =#{erpCompanyId} AND a.activeFlag=1 AND b.activeFlag=1
	</select>

    <select id="getValueById" resultType="String">
		SELECT
			GROUP_CONCAT(`value`)
		FROM
			md_dict_value
		WHERE
			FIND_IN_SET(recordId,#{recordId})
	</select>

	<select id="getValueByIdTwo" resultType="String">
		SELECT
			CASE WHEN (value+0) > 0 THEN (value+0) ELSE remark END
		FROM
			md_dict_value
		WHERE recordId = #{recordId}
	</select>

</mapper>
