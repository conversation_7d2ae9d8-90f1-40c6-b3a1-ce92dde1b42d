<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="d-flex flex-column flex-root">
    <div class="d-flex flex-row flex-column-fluid page">
        <div class="d-flex flex-column flex-row-fluid wrapper">
            <div class="content d-flex flex-column flex-column-fluid"
                 style="background-image: url(${pageContext.request.contextPath}/assets/global/metronic/assets/media/bg/bgMain.jpg);height: 180px;">
                <div class="subheader subheader-transparent" id="kt_subheader">
                    <div class="container d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <h5 class="text-white font-weight-bold my-2 mr-5">我的待办</h5>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-column-fluid">
                    <div class="container">
                        <div class="row">
                            <div class="col-12">
                                <div class="card card-custom gutter-b">
                                    <div class="card-body" style="padding-top: 0px;">
                                        <div class="row mt-0">
                                            <div class="col-12">
                                                <ul class="nav nav-tabs nav-tabs-line mb-1" style="width: 100%;">
                                                    <li class="nav-item text-center" style="width: 33%;">
                                                        <a class="nav-link active" data-toggle="tab" v-on:click="dealShowList(1)">
                                                            <span class="nav-text font-size-h5">待办</span>
                                                        </a>
                                                    </li>
                                                    <li class="nav-item text-center" v-on:click="dealShowList(2)" style="width: 33%;">
                                                        <a class="nav-link" data-toggle="tab">
                                                            <span class="nav-text font-size-h5">已通过</span>
                                                        </a>
                                                    </li>
                                                    <li class="nav-item text-center" v-on:click="dealShowList(3)" style="width: 34%;">
                                                        <a class="nav-link" data-toggle="tab">
                                                            <span class="nav-text font-size-h5">已拒绝</span>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="row pt-2 pb-2 border-bottom align-items-center" v-for="item in showList" :key="item.recordId" v-if="showList && showList.length > 0">
                                            <div class="col-12">
                                                <div class="row align-items-center">
                                                    <div class="col-9">
                                                        <span v-if="item.dealStatus == 'edit' && showFlag == 1" class="font-weight-bolder">产品推送审核</span>
                                                        <span v-else class="font-weight-bolder">商家合作审核</span>
                                                    </div>
                                                    <div class="col-3 text-right">
                                                        <button class="btn btn-sm btn-light-primary" v-on:click="auditModal(item)">
                                                            <span v-if="item.status == '1'">资质审核</span>
                                                            <span v-else>查看详情</span>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div>
                                                    <span>申请时间：{{item.createdDate}}</span>
                                                </div>
                                                <div>
                                                    <span>通过时间：{{item.lastUpDate}}</span>
                                                </div>
                                                <div>
                                                    <div class="d-flex align-items-center">
                                                        <div class="d-flex flex-column text-left">
                                                            <div>
                                                                <span v-if="item.applyFlag == 2">申请方&nbsp;</span>
                                                                <span>客户:&nbsp;{{item.customer.name}}</span>
                                                                <span class="text-primary" v-if="item.customer.erpCompanyId">
                                                                        <i class="flaticon-car text-primary" style="font-size: 1rem;"></i>ERP
                                                                    </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="d-flex align-items-center">
                                                        <div class="d-flex flex-column text-left">
                                                            <div>
                                                                <span v-if="item.applyFlag == 1 ||item.applyFlag == 4">申请方&nbsp;</span>
                                                                    <span>
                                                                        <span v-if="item.applyFlag == 4">设备</span>
                                                                        供应商:&nbsp;{{item.supplier.name}}
                                                                    </span>
                                                                <span v-if="item.supplier.erpCompanyId" class="text-primary">
                                                                        <i class="flaticon-car text-primary" style="font-size: 1rem;"></i>ERP
                                                                    </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="auditModal" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="auditModal" aria-hidden="true" style="overflow:auto!important;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <span v-if="coop.status == 1">资质审核</span>
                        <span v-else>查看详情</span>
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i aria-hidden="true" class="ki ki-close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="pb-3 border-bottom" v-if="coop && coop.customer && coop.customer.name">
                        <div class="row pb-3">
                            <div class="col">
                                <span class="font-size-h3">客户企业信息<span v-if="coop.applyFlag == 2">(申请方)</span></span>
                            </div>
                        </div>
                        <div class="row pb-2">
                            <div class="col">
                                <div class="d-flex align-items-center">
                                    <div class="symbol symbol-success pr-3" v-if="coop.customer.logo">
                                        <img alt="Pic" :src="coop.customer.logoStr" class="h-100"/>
                                    </div>
                                    <div class="symbol symbol-45 symbol-light-danger mr-4 flex-shrink-0" v-else>
                                        <div class="symbol-label">
                                            logo
                                        </div>
                                    </div>
                                    <div class="d-flex flex-column text-left">
                                        <div>
                                            <span>{{coop.customer.name}}</span>
                                        </div>
                                        <div class="text-primary" v-if="coop.customer.erpCompanyId">
                                            <i class="flaticon-car text-primary" style="font-size: 1rem;"></i>ERP
                                        </div>
                                        <div>
                                            法人代表:{{coop.customer.legalPerson}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row pb-2">
                            <div class="col">
                                统一社会信用代码:{{coop.customer.licence}}
                            </div>
                        </div>
                        <div class="row pb-2">
                            <div class="col">
                                {{coop.customer.introduction}}
                            </div>
                        </div>
                    </div>
                    <div class="pt-3 pb-3 border-bottom" v-if="coop && coop.supplier && coop.supplier.name">
                        <div class="row pb-3">
                            <div class="col">
                                <span class="font-size-h3">
                                    <span v-if="coop.applyFlag == 4">设备</span>供应商企业信息
                                    <span v-if="coop.applyFlag == 1 || coop.applyFlag == 4">(申请方)</span>
                                </span>
                            </div>
                        </div>
                        <div class="row pb-2">
                            <div class="col">
                                <div class="d-flex align-items-center">
                                    <div class="symbol symbol-success pr-3" v-if="coop.supplier.logo">
                                        <img alt="Pic" :src="coop.supplier.logoStr" class="h-100"/>
                                    </div>
                                    <div class="symbol symbol-45 symbol-light-danger mr-4 flex-shrink-0" v-else>
                                        <div class="symbol-label">
                                            logo
                                        </div>
                                    </div>
                                    <div class="d-flex flex-column text-left">
                                        <div>
                                            <span>{{coop.supplier.name}}</span>
                                        </div>
                                        <div class="text-primary" v-if="coop.supplier.erpCompanyId">
                                            <i class="flaticon-car text-primary" style="font-size: 1rem;"></i>ERP
                                        </div>
                                        <div>
                                            法人代表:{{coop.supplier.legalPerson}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row pb-2">
                            <div class="col">
                                统一社会信用代码:{{coop.supplier.licence}}
                            </div>
                        </div>
                        <div class="row pb-2">
                            <div class="col">
                                {{coop.supplier.introduction}}
                            </div>
                        </div>
                    </div>
                    <div class="pt-3 pb-3 border-bottom" v-if="coop.message">
                        <div class="row pb-3 border-bottom">
                            <div class="col">
                                <span class="font-size-h3">申请信息</span>
                            </div>
                        </div>
                        <div class="row pt-3">
                            <div class="col">
                                {{coop.message}}
                            </div>
                        </div>
                    </div>
                    <div class="pt-3 pb-3" v-if="productList && productList.length > 0">
                        <div class="row pb-3 border-bottom">
                            <div class="col">
                                <span class="font-size-h3">提供产品</span>
                            </div>
                        </div>
                        <div class="row pt-3 pb-3 border" v-for="item in productList" :key="item.recordId">
                            <div class="col">
                                <div class="checkbox-inline">
                                    <label class="checkbox checkbox-success" v-if="showFlag == 1 && item.status == 1">
                                        <input type="checkbox" v-model="item.checked"/>
                                        <span></span>
                                    </label>
                                    <span class="font-weight-bolder text-primary" v-on:click="showDeail(item)">规格：{{item.specifications}}</span>
                                </div>
                                <div class="row pt-1">
                                    <div class="col-8">
                                        名称：{{item.name}}
                                        <span v-if="item.status == 1">(待通过)</span>
                                        <span v-if="item.status == 2">(合作中)</span>
                                    </div>
                                    <div class="col text-right">库存：{{item.stocks}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light-primary font-weight-bold" v-on:click="audit(2)" v-if="showFlag == 1">通过</button>
                    <button type="button" class="btn btn-light-danger font-weight-bold" v-on:click="audit(3)" v-if="showFlag == 1">拒绝</button>
                    <button type="button" class="btn btn-light-dark font-weight-bold" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="showDeail" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="showDeail" aria-hidden="true" style="overflow:auto!important;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        产品详情
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i aria-hidden="true" class="ki ki-close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="pt-1">
                        <div class="row">
                            <div class="col">
                                <div class="row pt-3 pb-3">
                                    <div class="col font-weight-bolder">
                                        基本信息
                                    </div>
                                </div>
                                <div class="row pt-2 pb-2">
                                    <div class="col font-weight-bolder">
                                        <div class="input-group align-items-center">
                                            <div class="input-group-append">
                                                产品：
                                            </div>
                                            <input class="form-control" v-model="product.configName" disabled>
                                        </div>
                                    </div>
                                </div>
                                <div class="row pt-2 pb-2">
                                    <div class="col font-weight-bolder">
                                        <div class="input-group align-items-center">
                                            <div class="input-group-append">
                                                名字：
                                            </div>
                                            <input class="form-control" v-model="product.name" disabled>
                                        </div>
                                    </div>
                                </div>
                                <div class="row pt-2 pb-2">
                                    <div class="col font-weight-bolder">
                                        <div class="input-group align-items-center">
                                            <div class="input-group-append">
                                                规格：
                                            </div>
                                            <input class="form-control" v-model="product.specifications" disabled>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" v-for="item in configList" :key="item.recordId">
                            <div class="col">
                                <div class="row pt-3 pb-3">
                                    <div class="col font-weight-bolder">
                                        {{item.name}}
                                    </div>
                                </div>
                                <div class="row pt-2 pb-2" v-for="row in item.list" :key="row.recordId" v-if="item.list">
                                    <div class="col font-weight-bolder">
                                        {{row.name}}:
                                        <div class="row" v-if="row.inputType == 1">
                                            <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                                <div class="input-group">
                                                    <input class="form-control" v-model="row.value" disabled>
                                                    <div class="input-group-append" v-if="row.unitType">
                                                        <button class="btn btn-outline-secondary">{{row.unitType}}</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" v-if="row.inputType == 2">
                                            <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                                <div class="input-group">
                                                    <input class="form-control" placeholder="长" v-model="row.value1" disabled>
                                                    <div class="input-group-append">
                                                        <button class="btn btn-outline-secondary">*</button>
                                                    </div>
                                                    <input class="form-control" placeholder="宽" v-model="row.value2" disabled>
                                                    <div class="input-group-append" v-if="row.unitType">
                                                        <button class="btn btn-outline-secondary">{{row.unitType}}</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" v-if="row.inputType == 3">
                                            <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                                <div class="input-group">
                                                    <input class="form-control" placeholder="长" v-model="row.value1" disabled>
                                                    <div class="input-group-append">
                                                        <button class="btn btn-outline-secondary">*</button>
                                                    </div>
                                                    <input class="form-control" placeholder="宽" v-model="row.value2" disabled>
                                                    <div class="input-group-append">
                                                        <button class="btn btn-outline-secondary">/</button>
                                                    </div>
                                                    <input class="form-control" placeholder="拼接数" v-model="row.value3" disabled>
                                                    <div class="input-group-append" v-if="row.unitType">
                                                        <button class="btn btn-outline-secondary">{{row.unitType}}</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row" v-if="row.inputType == 4">
                                            <div class="col-12">
                                                <button :class="craft.checked == 1? 'btn btn-outline-warning mr-1 mb-1': 'btn btn-outline-secondary mr-1 mb-1'"
                                                        v-for="craft in row.list" :key="craft.recordId">
                                                    {{ craft.value }}
                                                </button>
                                            </div>
                                        </div>
                                        <div class="row" v-if="row.inputType == 5">
                                            <div class="col-12">
                                                <button :class="craft.checked == 1? 'btn btn-outline-warning mr-1 mb-1': 'btn btn-outline-secondary mr-1 mb-1'"
                                                        v-for="craft in row.list" :key="craft.recordId">
                                                    {{ craft.value }}
                                                </button>
                                            </div>
                                        </div>
                                        <div class="row" v-if="row.inputType == 6">
                                            <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                                <button class="btn btn-primary">上传文件</button>
                                            </div>
                                        </div>
                                        <div class="row" v-if="row.inputType == 7">
                                            <div :class="row.inputWidth ? row.inputWidth : 'col'">
                                                <textarea class="form-control" v-model="row.value" disabled></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>