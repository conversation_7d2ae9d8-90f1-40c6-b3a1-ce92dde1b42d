package com.kyb.pcberp.modules.purch.utils;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.modules.contract.dao.MaterialContractDao;
import com.kyb.pcberp.modules.contract.entity.MaterialContract;
import com.kyb.pcberp.modules.contract.entity.MaterialContractDetail;
import com.kyb.pcberp.modules.contract.utils.ChangeDataUtils;
import com.kyb.pcberp.modules.crm.dao.CustomerDao;
import com.kyb.pcberp.modules.crm.dao.GoodsCheckDao;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.crm.entity.GoodsCheck;
import com.kyb.pcberp.modules.crm.service.ReceivableService;
import com.kyb.pcberp.modules.purch.dao.*;
import com.kyb.pcberp.modules.purch.entity.*;
import com.kyb.pcberp.modules.purch.service.PayableService;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.dao.StoreHouseDao;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.sys.dao.BranchDao;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import com.kyb.pcberp.modules.sys.dao.DictValueDao;
import com.kyb.pcberp.modules.sys.entity.Branch;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.DictValue;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class IngredientsUtils
{
    private IngredientsDao ingredientsDao = SpringContextHolder.getBean(IngredientsDao.class);

    private PurchasingDao purchasingDao = SpringContextHolder.getBean(PurchasingDao.class);

    private MaterialContractDao materialContractDao = SpringContextHolder.getBean(MaterialContractDao.class);

    private PurchasingDetailDao purchasingDetailDao = SpringContextHolder.getBean(PurchasingDetailDao.class);

    private BranchDao branchDao = SpringContextHolder.getBean(BranchDao.class);

    private CustomerDao customerDao = SpringContextHolder.getBean(CustomerDao.class);

    private DictValueDao dictValueDao = SpringContextHolder.getBean(DictValueDao.class);

    private MaterialDao materialDao = SpringContextHolder.getBean(MaterialDao.class);

    private StoreHouseDao storeHouseDao = SpringContextHolder.getBean(StoreHouseDao.class);

    private CompanyDao companyDao = SpringContextHolder.getBean(CompanyDao.class);

    private SupplierDao supplierDao = SpringContextHolder.getBean(SupplierDao.class);

    private GroupSupplyChainProcessDao groupSupplyChainProcessDao = SpringContextHolder.getBean(GroupSupplyChainProcessDao.class);

    private GoodsCheckDao goodsCheckDao = SpringContextHolder.getBean(GoodsCheckDao.class);

    private MaterialCheckDao materialCheckDao = SpringContextHolder.getBean(MaterialCheckDao.class);

    private ReceivableService receivableService = SpringContextHolder.getBean(ReceivableService.class);

    private PayableService payableService = SpringContextHolder.getBean(PayableService.class);

    /**
     * 获取原料组织业务
     * @param purchasing 原料采购单
     * @return Ingredients 料组织业务
     */
    public Ingredients getIngredients(Purchasing purchasing)
    {
        if(null == purchasing)
        {
            return null;
        }
        return ingredientsDao.getIngredients(purchasing);
    }

    /**
     * 生成供应链流程数据
     * @param purchasing 原料采购单
     * @param ingredients 原料组织业务
     * @param middleList 原料组织中间层
     */
    @Transactional(readOnly = false)
    public void sentGroupSupplyChainData(Purchasing purchasing,Ingredients ingredients,List<IngredientsMiddle> middleList)
    {
        // 查询供应链流程控制表有无数据
        int tun = 1;
        GroupSupplyChainProcess process = new GroupSupplyChainProcess();
        if (StringUtils.isNotBlank(process.getSourcePurchaseOrdersId()))
        {
            process.setSourcePurchaseOrdersId(purchasing.getSourcePurchaseOrdersId());
        }
        else
        {
            process.setSourcePurchaseOrdersId(purchasing.getRecordId());
        }

        // 查询终端供应商的公司id
        purchasing.setSupplier(purchasingDao.getSupplierErpId(purchasing));

        List<GroupSupplyChainProcess> groupSupplyChainProcessList = groupSupplyChainProcessDao.maxTunList(process);
        if (Collections3.isNotEmpty(groupSupplyChainProcessList))
        {
            tun = Integer.parseInt(groupSupplyChainProcessList.get(0).getTun()) + 1;
        }
        // 终端公司操作生成流程数据
        if (ingredients.getHead().getBindErpId().equals(purchasing.getCompany().getRecordId()))
        {
            // 添加供应链流程控制表数据
            List<String> bindErpIds = new ArrayList<>();
            // 添加头部
            bindErpIds.add(ingredients.getHead().getBindErpId());
            // 添加中间层
            if(Collections3.isNotEmpty(middleList))
            {
                for (IngredientsMiddle ingredientsMiddle : middleList)
                {
                    if (StringUtils.isNotBlank(ingredientsMiddle.getRelation().getBindErpId()))
                    {
                        bindErpIds.add(ingredientsMiddle.getRelation().getBindErpId());
                    }
                }
            }
            // 添加尾部
            bindErpIds.add(ingredients.getTail().getBindErpId());

            // 添加终端供应商
            if (null != purchasing.getSupplier() && StringUtils.isNotBlank(purchasing.getSupplier().getErpId()))
            {
                if (StringUtils.isNotBlank(purchasing.getSupplier().getErpId()))
                {
                    bindErpIds.add(purchasing.getSupplier().getErpId());
                }
            }
            else
            {
                // 生成erp公司
                Company company = new Company();
                if (null != purchasing.getSupplier())
                {
                    if (StringUtils.isNotBlank(purchasing.getSupplier().getName()))
                    {
                        company.setName(purchasing.getSupplier().getName());
                    }
                    if (StringUtils.isNotBlank(purchasing.getSupplier().getShortName()))
                    {
                        company.setShortName(purchasing.getSupplier().getShortName());
                    }
                    if (StringUtils.isNotBlank(purchasing.getSupplier().getPhone()))
                    {
                        company.setPhone(purchasing.getSupplier().getPhone());
                    }
                    if (StringUtils.isNotBlank(purchasing.getSupplier().getAddress()))
                    {
                        company.setAddress(purchasing.getSupplier().getAddress());
                    }
                    company.preInsert();
                    companyDao.addErpCompany(company);
                    // 更新供应商的erpId
                    Supplier sup = purchasing.getSupplier();
                    sup.setErpId(company.getRecordId());
                    supplierDao.updateErpId(sup);
                    bindErpIds.add(company.getRecordId());
                }
            }
            int index = 1; // 顺序
            for (String bidErpId : bindErpIds)
            {
                // 将数据保存到供应链流程控制表
                GroupSupplyChainProcess groupSupplyChainProcess = new GroupSupplyChainProcess();
                if (index == 1)
                {
                    groupSupplyChainProcess.setStatus("2");
                }
                else
                {
                    groupSupplyChainProcess.setStatus("1");
                }
                groupSupplyChainProcess.setCreatedBy(purchasing.getCreatedBy());
                groupSupplyChainProcess.setSourcePurchaseOrdersId(process.getSourcePurchaseOrdersId());
                groupSupplyChainProcess.setCompanyId(bidErpId);
                groupSupplyChainProcess.setSequence(index);
                groupSupplyChainProcess.setTun(String.valueOf(tun));
                groupSupplyChainProcess.setIngredientsId(ingredients.getRecordId());
                groupSupplyChainProcessDao.insert(groupSupplyChainProcess);
                index++;
            }
        }
    }

    /**
     * 生成供应链公司的销售订单，并修改状态
     * @param purchasing 原料采购单
     * @param ingredients 原料组织业务
     */
    @Transactional(readOnly = false)
    public void genaricMaterialContract(Purchasing purchasing,Ingredients ingredients)
    {
        if(null == purchasing || null == ingredients)
        {
            return;
        }
        if(null == purchasing.getCompany())
        {
            purchasing.setCompany(UserUtils.getUser().getCompany());
        }
        // 获取原料组织业务中间层
        List<IngredientsMiddle> middleList = ingredientsDao.getMiddleList(ingredients);

        // 生成供应链流程数据
        sentGroupSupplyChainData(purchasing,ingredients,middleList);

        String cusComTwpId = purchasing.getCompany().getRecordId();
        Integer status = TypeKey.PU_PURCHASING_STATUS_CONFIRMED;// 状态为已确认
        if(Collections3.isNotEmpty(middleList))
        {
            for(int i = 0; i < middleList.size(); i++)
            {
                IngredientsMiddle middle = middleList.get(i);
                if(null == middle.getRelation() || StringUtils.isBlank(middle.getRelation().getBindErpId()))
                {
                    continue;
                }
                String cusComId = null;
                String supComId = null;
                if(i == 0)
                {
                    cusComId = purchasing.getCompany().getRecordId();
                }
                else
                {
                    cusComId = middleList.get(i - 1).getRelation().getBindErpId();
                }
                supComId = middle.getRelation().getBindErpId();

                // 生成供应商原料合同
                MaterialContract materialContract = sentSupMaterialContract(purchasing,supComId,cusComId,status,1,middle);

                String nextSupComId = null;
                if(i + 1 < middleList.size())
                {
                    nextSupComId =  middleList.get(i + 1).getRelation().getBindErpId();
                }
                else
                {
                    nextSupComId = ingredients.getTail().getBindErpId();
                }
                // 生成供应商原料采购单
                sentSupPurchasing(materialContract,supComId,nextSupComId,purchasing.getCompany().getRecordId());
                cusComTwpId = supComId;
            }
        }
        else
        {
            String supComId = ingredients.getTail().getBindErpId();
            // 生成供应商原料合同
            sentSupMaterialContract(purchasing,supComId,cusComTwpId,status,2,null);
        }

        String supComId = ingredients.getTail().getBindErpId();
        // 生成供应商原料合同
        if(null != ingredients.getProcessMode() && ingredients.getProcessMode().compareTo(1) != 0)
        {
            status = TypeKey.PU_PURCHASING_STATUS_UNCONFIRM;// 状态为未确认
        }
        MaterialContract materialContract = sentSupMaterialContract(purchasing,supComId,cusComTwpId,status,2,null);

        // 自动生产时，生成跟终端供应商的采购单和原料合同
        if(null != ingredients.getProcessMode() && ingredients.getProcessMode().compareTo(1) == 0)
        {
            // 生成原料采购单
            sentSupPurchasing(materialContract,supComId,purchasing);

            // 生成终端供应商原料合同
            supComId = purchasing.getSupplier().getErpId();
            cusComTwpId = ingredients.getTail().getBindErpId();
            sentSupMaterialContract(purchasing,supComId,cusComTwpId,status,3,null);
        }

        // 获取原料采购明细列表
        List<PurchasingDetail> list = purchasingDetailDao.getDeailById(purchasing);
        if(Collections3.isNotEmpty(list))
        {
            for(PurchasingDetail detail : list)
            {
                purchasingDetailDao.updateFlatUnitPriceTwo(detail);
            }
        }
    }

    /**
     * 生成供应商原料采购单(供应商要有ERP系统)
     * @param materialContract 原料合同
     * @param supComId 供应商公司id
     * @param nextSupComId 下个供应商公司id
     * @param companyId 下单公司id
     */
    @Transactional(readOnly = false)
    public void sentSupPurchasing(MaterialContract materialContract,String supComId,String nextSupComId,String companyId)
    {
        if(null == materialContract || StringUtils.isBlank(supComId) || StringUtils.isBlank(nextSupComId))
        {
            return;
        }
        List<MaterialContractDetail> contractDetailList = materialContractDao.getContractDeailList(materialContract);
        if(Collections3.isEmpty(contractDetailList))
        {
            return;
        }

        // 赋值原料采购单
        Purchasing purchasing = new Purchasing();
        purchasing.setCompany(new Company(supComId));
        purchasing.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString(),purchasing.getCompany()));
        Integer noNum = purchasingDao.findPurchasingNoisEnable(purchasing);
        while (noNum != null && noNum > 0)
        {
            // 将对应编码的nextNo 修改为+1
            CommonUtils.updateNextNo(CommonEnums.CodeType.PURCHASEORDER.getIndex(), purchasing.getCompany());
            purchasing.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString(), purchasing.getCompany()));
            noNum = purchasingDao.findPurchasingNoisEnable(purchasing);// 查询编号是否依旧存在
        }
        Supplier sup = new Supplier();
        sup.setCompanyId(supComId);
        sup.setErpId(nextSupComId);
        String supperId = supplierDao.getSupplierIdByErpId(sup);
        if (StringUtils.isNotBlank(supperId))
        {
            sup.setRecordId(supperId);
        }
        else
        {
            Company company = new Company(supComId);
            // 创建客户
            Supplier s = new Supplier();
            s.setCompany(company);
            Company nexrtSupCom =companyDao.get(nextSupComId);
            s.setName(nexrtSupCom.getName());
            s.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.SUPPLIER.getIndex().toString(),company));
            noNum = supplierDao.findSupplierNoisEnable(s);
            while (noNum != null && noNum > 0)
            {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.SUPPLIER.getIndex(), company);
                s.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.SUPPLIER.getIndex().toString(), company));
                noNum = supplierDao.findSupplierNoisEnable(s);// 查询编号是否依旧存在
            }
            s.setShortName(nexrtSupCom.getName());
            s.setCreatedDate(new Date());
            s.setLastUpdDate(new Date());
            s.setCheckDate(1);
            s.setStatus(TypeKey.MD_SUPPLIER_STATUS_NORMAL);
            s.setErpId(nextSupComId);

            supplierDao.insert(s);
            // 添加成功把对应编码的nextNo 修改为+1
            CommonUtils.updateNextNo(CommonEnums.CodeType.SUPPLIER.getIndex());
            sup.setRecordId(s.getRecordId());
        }
        purchasing.setSupplier(sup);
        if(null != materialContract.getOrderDate())
        {
            purchasing.setOrderDate(materialContract.getOrderDate());
        }
        purchasing.setDeliveryDate(materialContract.getDeliveryDate());
        if(StringUtils.isNotBlank(materialContract.getDeliveryWay()))
        {
            purchasing.setDeliveryWay(Long.valueOf(materialContract.getDeliveryWay()));
        }
        if(StringUtils.isNotBlank(materialContract.getCurrencyType()))
        {
            purchasing.setCurrencyType(Long.valueOf(materialContract.getCurrencyType()));
        }

        Integer payWay = supplierDao.getSourcePayWayId(companyId,nextSupComId);
        if(null == payWay && StringUtils.isNotBlank(materialContract.getPayWay()))
        {
            payWay = Integer.valueOf(materialContract.getPayWay());
        }
        else
        {
            payWay = Integer.valueOf(genaricDictVal(payWay, materialContract.getCompanyId()));
        }
        purchasing.setPayWay(payWay);

        if(StringUtils.isNotBlank(materialContract.getPayDays()))
        {
            purchasing.setPayDays(Integer.valueOf(materialContract.getPayDays()));
        }
        if(StringUtils.isNotBlank(materialContract.getTaxDescript()))
        {
            purchasing.setTaxDescript(Long.valueOf(materialContract.getTaxDescript()));
        }
        if(StringUtils.isNotBlank(materialContract.getAssureDays()))
        {
            purchasing.setAssureDays(Long.valueOf(materialContract.getAssureDays()));
        }
        if(StringUtils.isNotBlank(materialContract.getPaycause()))
        {
            purchasing.setPaycause(Long.valueOf(materialContract.getPaycause()));
        }
        if(StringUtils.isNotBlank(materialContract.getPurchaser()))
        {
            purchasing.setPurchaser(new User(materialContract.getPurchaser()));
        }
        purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_APPROVE);
        if(StringUtils.isNotBlank(materialContract.getBranchId()))
        {
            purchasing.setBranch(new Branch(materialContract.getBranchId()));
        }
        purchasing.setCreatedDate(new Date());
        purchasing.setSupplierContractNo(materialContract.getSupplierContractNo());
        if (StringUtils.isNotBlank(materialContract.getSourcePurchaseOrdersId()))
        {
            purchasing.setSourcePurchaseOrdersId(materialContract.getSourcePurchaseOrdersId());
        }
        purchasing.setType("1");
        purchasingDao.insert(purchasing);

        for(MaterialContractDetail detail : contractDetailList)
        {
            PurchasingDetail purDetail = new PurchasingDetail();
            purDetail.setCompany(purchasing.getCompany());
            purDetail.setPurchasing(purchasing);
            purDetail.setQuantity(detail.getQuantity());
            purDetail.setMaterial(new Material(detail.getMaterialId()));
            purDetail.setPrice(detail.getPrice());
            purDetail.setAmount(detail.getAmount());
            purDetail.setCraftDescript(detail.getCraftDescript());
            purDetail.setStatus(TypeKey.PU_PURCHASING_STATUS_APPROVE);
            purDetail.setCreatedDate(new Date());
            purDetail.setSupplierModel(detail.getSupplierModel());
            purDetail.setDeliveryDate(detail.getDeliveryDate());
            purDetail.setContractDeailId(detail.getRecordId());
            purchasingDetailDao.insert(purDetail);
        }
    }

    /**
     * 生成供应商原料合同
     * @param purchasing 原料采购单
     * @param supComId 供应商公司id
     * @param cusComId 客户公司id
     * @param status 状态
     * @param type 类型：1：中间层，2：供应链
     * @param middle 中间层
     */
    @Transactional(readOnly = false)
    public MaterialContract sentSupMaterialContract(Purchasing purchasing,String supComId,String cusComId,Integer status,Integer type,IngredientsMiddle middle)
    {
        String comapnyId = purchasing.getCompany().getRecordId();
        Purchasing order = purchasingDao.get(purchasing.getRecordId());
        MaterialContract materialContract = new MaterialContract();
        materialContract.setCompanyId(supComId);
        materialContract.setSourcePurchaseOrdersId(order.getSourcePurchaseOrdersId());
        String payWay = null;
        if(null != middle)
        {
            payWay = middle.getPayWay();
        }
        purchToContract(materialContract, order,cusComId,status,type,payWay);
        // 检查该采购单编号是否存在
//        String materialContractNo = CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString(), order.getCompany());
//        materialContract.setNo(materialContractNo);
//        Integer num = purchasingDao.findPurchsingNo(materialContract.getNo(), supComId);
//        while (num != null && num > 0)
//        {
//            CommonUtils.updateNextNo(CommonEnums.CodeType.PURCHASEORDER.getIndex(), order.getCompany());
//            materialContractNo = CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString(), order.getCompany());
//            num = purchasingDao.findPurchsingNo(materialContractNo, order.getCompany().getRecordId());
//            materialContract.setNo(materialContractNo);
//        }
        ChangeDataUtils change =  new ChangeDataUtils();
        String materialContractNo = change.getPurchasingMaterialNo(supComId,1,"");
        materialContract.setNo(materialContractNo);
        materialContractDao.insert(materialContract);
        List<PurchasingDetail> list = purchasingDetailDao.getDeailById(order);
        List<MaterialContractDetail> details = new ArrayList<>();
        for (int i = 0; i < list.size(); i++)
        {
            MaterialContractDetail detail = new MaterialContractDetail();
            detail.setCompanyId(supComId);
            detail.setPurchasingId(materialContract.getRecordId());
            BigDecimal quantity = null == list.get(i).getQuantity() ? BigDecimal.ZERO : list.get(i).getQuantity();
            BigDecimal amount = null == list.get(i).getAmount() ? BigDecimal.ZERO : list.get(i).getAmount();
            BigDecimal paymentDays = null == order.getPaymentDays() ? BigDecimal.ZERO : new BigDecimal(order.getPaymentDays());
            if(type.compareTo(2) == 0)
            {

                BigDecimal radio = null == order.getRadio() ? BigDecimal.ZERO : order.getRadio();

                Integer calculationType = 2;
                Integer days = 30;
                if(null != purchasing.getSupplyChain())
                {
                    if(null != purchasing.getSupplyChain().getCalculationType())
                    {
                        calculationType = purchasing.getSupplyChain().getCalculationType();
                    }
                    if(null != purchasing.getSupplyChain().getRateType() && 1 == purchasing.getSupplyChain().getRateType())
                    {
                        days = 360;
                    }
                }

                BigDecimal serviceFee = BigDecimal.ZERO;
                if(calculationType.compareTo(1) == 0)
                {
                    // 服务费 = 金额 * 费率 / 30天 * 付款天数
                    serviceFee = amount.multiply(radio).multiply(paymentDays).divide(new BigDecimal(days).multiply(new BigDecimal(100)), 6, BigDecimal.ROUND_HALF_UP);
                }
                else if(calculationType.compareTo(2) == 0)
                {
                    serviceFee = amount.multiply(radio.multiply(paymentDays).divide(new BigDecimal(days).multiply(new BigDecimal(100)),6,BigDecimal.ROUND_HALF_UP)).setScale(6,BigDecimal.ROUND_HALF_UP);
                }

                // 平摊单价 = (服务费 + 金额) / 数量
                BigDecimal flatUnitPrice = BigDecimal.ZERO;
                if(quantity.compareTo(BigDecimal.ZERO) > 0)
                {
                    flatUnitPrice = (serviceFee.add(amount)).divide(quantity,6, BigDecimal.ROUND_HALF_UP);
                }
                detail.setServiceFee(serviceFee);
                detail.setFlatUnitPrice(flatUnitPrice);
            }
            else
            {
                if(null != middle && null != middle.getRadio() && null != middle.getRadioType())
                {
                    Integer days = 1;
                    switch (middle.getRadioType())
                    {
                        case 1:
                            days = 30;
                            break;
                        case 2:
                            days = 90;
                            break;
                        case 3:
                            days = 360;
                            break;
                    }

                    BigDecimal serviceFee = amount.multiply(middle.getRadio()).multiply(paymentDays).divide(new BigDecimal(days).multiply(new BigDecimal(100)),2, BigDecimal.ROUND_HALF_UP);

                    // 平摊单价 = (服务费 + 金额) / 数量
                    BigDecimal flatUnitPrice = BigDecimal.ZERO;
                    if(quantity.compareTo(BigDecimal.ZERO) > 0)
                    {
                        flatUnitPrice = (serviceFee.add(amount)).divide(quantity,6, BigDecimal.ROUND_HALF_UP);
                    }
                    detail.setServiceFee(serviceFee);
                    detail.setFlatUnitPrice(flatUnitPrice);
                }
            }
/*            if(list.get(i).getCompany().getRecordId().equals(comapnyId) && null != detail.getFlatUnitPrice())
            {
                // 更新原料采购单明细平摊单价
                list.get(i).setFlatUnitPrice(detail.getFlatUnitPrice());
                purchasingDetailDao.updateFlatUnitPrice(list.get(i));
            }*/

            purchToContractDeail(detail, list.get(i),cusComId,status);
            details.add(detail);
        }
        materialContractDao.batchInsertDeail(details);
        return materialContract;
    }

    /**
     * 原料采购单生成原料合同
     * @param materialContract 原料合同
     * @param purchasing 原料采购单
     * @param status 状态
     * @param type 类型：1：中间层，2：供应链
     * @param payWay 月结方式
     */
    @Transactional(readOnly = false)
    public void purchToContract(MaterialContract materialContract, Purchasing purchasing,String cusComId,Integer status,Integer type,String payWay)
    {
        String companyId = cusComId;
        materialContract.setNo(purchasing.getNo());
        materialContract.setStatus(status.toString());
        materialContract.setOrderDate(purchasing.getOrderDate());
        materialContract.setDeliveryDate(purchasing.getDeliveryDate());
        materialContract.setCreatedBy(purchasing.getPurchaser());
        materialContract.setRemark(purchasing.getRemark());
        materialContract.setDeliveryPlace(purchasing.getDeliveryPlace());
        materialContract.setQualityStd(purchasing.getQualityStd());
        materialContract.setCancelCause(purchasing.getCancelCause());
        materialContract.setPeriod(purchasing.getPeriod());
        materialContract.setSupplierContractNo(purchasing.getSupplierContractNo());
        materialContract.setCreatedDate(new Date());
        if (purchasing.getPurchaser() != null)
        {
            materialContract.setPurchaser(purchasing.getPurchaser().getRecordId());
        }
        // 供应链合同保存付款天数和费率
        if(type.compareTo(2) == 0)
        {
            materialContract.setPaymentDays(purchasing.getPaymentDays());
            materialContract.setRadio(purchasing.getRadio());
        }
        String branchId = branchDao.getDefaultId(materialContract);
        materialContract.setBranchId(branchId);
        Customer cust = new Customer();
        cust.setCompanyId(materialContract.getCompanyId());
        cust.setErpId(companyId);
        String customerId = customerDao.getCustomerIdByErpId(cust);
        if (StringUtils.isNotBlank(customerId))
        {
            materialContract.setCustomerId(customerId);
        }
        else
        {
            Company company = new Company(materialContract.getCompanyId());
            // 创建客户
            Customer cus = new Customer();
            cus.setCompany(company);
            Company cusCom =companyDao.get(companyId);
            cus.setName(cusCom.getName());
            cus.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CUSTOMER.getIndex().toString(),company));
            Integer noNum = customerDao.findCustomerNoisEnable(cus);
            while (noNum != null && noNum > 0)
            {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.CUSTOMER.getIndex(), company);
                cus.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CUSTOMER.getIndex().toString(), company));
                noNum = customerDao.findCustomerNoisEnable(cus);// 查询编号是否依旧存在
            }
            cus.setShortName(cus.getName());
            cus.setCreatedDate(new Date());
            cus.setLastUpdDate(new Date());
            cus.setCheckDate("1");
            cus.setStatus(TypeKey.MD_CUSTOMER_STATUS_NORMAL);
            cus.setErpId(companyId);
            customerDao.insert(cus);
            // 添加成功把对应编码的nextNo 修改为+1
            CommonUtils.updateNextNo(CommonEnums.CodeType.CUSTOMER.getIndex());
            materialContract.setCustomerId(cus.getRecordId());
        }
        // 工艺转换
        materialContract.setDeliveryWay(genaricDictVal(purchasing.getDeliveryWay(), materialContract.getCompanyId()));
        materialContract.setCurrencyType(genaricDictVal(purchasing.getCurrencyType(), materialContract.getCompanyId()));
        materialContract.setPayDays(genaricDictVal(purchasing.getPayDays(), materialContract.getCompanyId()));
        materialContract.setTaxDescript(genaricDictVal(purchasing.getTaxDescript(), materialContract.getCompanyId()));
        materialContract.setAssureDays(genaricDictVal(purchasing.getAssureDays(), materialContract.getCompanyId()));
        materialContract.setPaycause(genaricDictVal(purchasing.getPaycause(), materialContract.getCompanyId()));

        Integer payWayTwo = null;
        if(type == 1 || type == 2)
        {
            if(StringUtils.isNotBlank(payWay))
            {
                payWayTwo = Integer.valueOf(payWay);
            }
            else
            {
                // 付款方式以下单源公司的供应商信息为准
                payWayTwo = supplierDao.getSourcePayWayId(purchasing.getCompany().getRecordId(),materialContract.getCompanyId());
                if(null == payWayTwo)
                {
                    payWayTwo = purchasing.getPayWay();
                }
            }
        }
        else if(type == 3)
        {
            payWayTwo = purchasing.getPayWay();
        }
        materialContract.setPayWay(genaricDictVal(payWayTwo, materialContract.getCompanyId()));
    }

    /**
     * 获取转换后的工艺id
     * @param craftId 原工艺id
     * @param toComId 转换后公司
     * @return 转换后的工艺id
     */
    public String genaricDictVal(Object craftId, String toComId)
    {
        if (craftId == null)
        {
            return null;
        }
        String dictId = craftId.toString();
        String value = dictValueDao.genaricDictVal(dictId, toComId);
        if (StringUtils.isBlank(value))
        {
            DictValue value1 = dictValueDao.get(dictId);
            value1.setCompany(new Company(toComId));
            value1.preInsert();
            dictValueDao.insert(value1);
            return value1.getRecordId();
        }
        return value;
    }

    public void purchToContractDeail(MaterialContractDetail detail, PurchasingDetail purchasingDetail,String cusComId,Integer status)
    {
        detail.setQuantity(purchasingDetail.getQuantity());
        detail.setPrice(purchasingDetail.getPrice());
        detail.setAmount(purchasingDetail.getAmount());
        detail.setCraftDescript(purchasingDetail.getCraftDescript());
        detail.setStatus(status);
        detail.setRemark(purchasingDetail.getRemark());
        detail.setSupplierModel(purchasingDetail.getSupplierModel());
        detail.setDeliveryDate(purchasingDetail.getDeliveryDate());
        detail.setPurchRawId(purchasingDetail.getOldPurchRawId());
        detail.setOrgId(purchasingDetail.getRecordId());

        // 增加数据字典值
        dictValueDao.insertValueByMatId(purchasingDetail.getMaterial().getRecordId(),detail.getCompanyId());

        // 增加规格项
        materialDao.insertMaterialSpe(purchasingDetail.getMaterial().getRecordId(),detail.getCompanyId());

        // 转换物料id，没有就生成
        Material material =
            materialDao.getMaterialByOtherId(purchasingDetail.getMaterial().getRecordId(), detail.getCompanyId());
        if (material == null)
        {
            Material mat = new Material();
            mat.setCompanyId(detail.getCompanyId());
            mat.setRecordId(purchasingDetail.getMaterial().getRecordId());

            // 获取仓库是否存在
            Integer count = storeHouseDao.checkNameCount(mat);
            if(null == count || count == 0)
            {
                storeHouseDao.saveByMaterial(mat);
            }
            mat.setFromComId(cusComId);
            mat.preInsert();
            materialDao.copyMaterial(mat);
            mat.setFromComId(purchasingDetail.getCompany().getRecordId());
            materialDao.copyMaterialSpeRe(mat);
            material =
                materialDao.getMaterialByOtherId(purchasingDetail.getMaterial().getRecordId(), detail.getCompanyId());
        }
        detail.setMaterialId(material.getRecordId());
    }

    /**
     * 生成供应商原料采购单(供应商不要有ERP系统)
     * @param materialContract 原料采购单
     * @param supComId 供应商公司id
     * @param pur 原料采购单
     */
    @Transactional(readOnly = false)
    public void sentSupPurchasing(MaterialContract materialContract,String supComId,Purchasing pur)
    {
        if(null == materialContract || StringUtils.isBlank(supComId) || null == pur)
        {
            return;
        }
        List<MaterialContractDetail> contractDetailList = materialContractDao.getContractDeailList(materialContract);
        if(Collections3.isEmpty(contractDetailList))
        {
            return;
        }

        // 赋值原料采购单
        Purchasing purchasing = new Purchasing();
        purchasing.setCompany(new Company(supComId));
//        purchasing.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString(),purchasing.getCompany()));
//        Integer noNum = purchasingDao.findPurchasingNoisEnable(purchasing);
//        while (noNum != null && noNum > 0)
//        {
//            // 将对应编码的nextNo 修改为+1
//            CommonUtils.updateNextNo(CommonEnums.CodeType.PURCHASEORDER.getIndex(), purchasing.getCompany());
//            purchasing.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString(), purchasing.getCompany()));
//            noNum = purchasingDao.findPurchasingNoisEnable(purchasing);// 查询编号是否依旧存在
//        }

        Supplier sup = new Supplier();
        sup.setCompanyId(supComId);
        sup.setName(pur.getSupplier().getName());
        Supplier supData = supplierDao.getSupByName(sup);
        if (null != supData && StringUtils.isNotBlank(supData.getRecordId()))
        {
            sup.setRecordId(supData.getRecordId());
        }
        else
        {
            Company company = new Company(supComId);
            // 创建客户
            Supplier s = new Supplier();
            s.setCompany(company);
            s.setName(pur.getSupplier().getName());
            s.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.SUPPLIER.getIndex().toString(),company));
            Integer noNum = supplierDao.findSupplierNoisEnable(s);
            while (noNum != null && noNum > 0)
            {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.SUPPLIER.getIndex(), company);
                s.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.SUPPLIER.getIndex().toString(), company));
                noNum = supplierDao.findSupplierNoisEnable(s);// 查询编号是否依旧存在
            }
            s.setShortName(pur.getSupplier().getShortName());
            s.setCreatedDate(new Date());
            s.setLastUpdDate(new Date());
            s.setCheckDate(1);
            s.setStatus(TypeKey.MD_SUPPLIER_STATUS_NORMAL);

            supplierDao.insert(s);
            // 添加成功把对应编码的nextNo 修改为+1
            CommonUtils.updateNextNo(CommonEnums.CodeType.SUPPLIER.getIndex());
            sup.setRecordId(s.getRecordId());
        }
        ChangeDataUtils change =  new ChangeDataUtils();
        String purchasingNo = change.getPurchasingMaterialNo(supComId,2,sup.getRecordId());
        purchasing.setNo(purchasingNo);
        purchasing.setSupplier(sup);
        if(null != materialContract.getOrderDate())
        {
            purchasing.setOrderDate(materialContract.getOrderDate());
        }
        purchasing.setDeliveryDate(materialContract.getDeliveryDate());
        if(StringUtils.isNotBlank(materialContract.getDeliveryWay()))
        {
            purchasing.setDeliveryWay(Long.valueOf(materialContract.getDeliveryWay()));
        }
        if(StringUtils.isNotBlank(materialContract.getCurrencyType()))
        {
            purchasing.setCurrencyType(Long.valueOf(materialContract.getCurrencyType()));
        }

        if(null != pur.getPayWay())
        {
            String payWay = genaricDictVal(pur.getPayWay(), materialContract.getCompanyId());
            if(StringUtils.isNotBlank(payWay))
            {
                purchasing.setPayWay(Integer.valueOf(payWay));
            }
        }
        if(StringUtils.isNotBlank(materialContract.getPayDays()))
        {
            purchasing.setPayDays(Integer.valueOf(materialContract.getPayDays()));
        }
        if(StringUtils.isNotBlank(materialContract.getTaxDescript()))
        {
            purchasing.setTaxDescript(Long.valueOf(materialContract.getTaxDescript()));
        }
        if(StringUtils.isNotBlank(materialContract.getAssureDays()))
        {
            purchasing.setAssureDays(Long.valueOf(materialContract.getAssureDays()));
        }
        if(StringUtils.isNotBlank(materialContract.getPaycause()))
        {
            purchasing.setPaycause(Long.valueOf(materialContract.getPaycause()));
        }
        if(StringUtils.isNotBlank(materialContract.getPurchaser()))
        {
            purchasing.setPurchaser(new User(materialContract.getPurchaser()));
        }
        purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_APPROVE);
        if(StringUtils.isNotBlank(materialContract.getBranchId()))
        {
            purchasing.setBranch(new Branch(materialContract.getBranchId()));
        }
        purchasing.setSupplierContractNo(materialContract.getSupplierContractNo());
        purchasing.setCreatedDate(new Date());
        purchasing.setType("1");
        purchasingDao.insert(purchasing);

        for(MaterialContractDetail detail : contractDetailList)
        {
            PurchasingDetail purDetail = new PurchasingDetail();
            purDetail.setCompany(purchasing.getCompany());
            purDetail.setPurchasing(purchasing);
            purDetail.setQuantity(detail.getQuantity());
            purDetail.setMaterial(new Material(detail.getMaterialId()));
            purDetail.setPrice(detail.getPrice());
            purDetail.setAmount(detail.getAmount());
            purDetail.setCraftDescript(detail.getCraftDescript());
            purDetail.setStatus(TypeKey.PU_PURCHASING_STATUS_APPROVE);
            purDetail.setCreatedDate(new Date());
            purDetail.setSupplierModel(detail.getSupplierModel());
            purDetail.setDeliveryDate(detail.getDeliveryDate());
            purDetail.setContractDeailId(detail.getRecordId());
            purchasingDetailDao.insert(purDetail);
        }
    }

    @Transactional(readOnly = false)
    public Purchasing setSupPurchasing(MaterialContract materialContract,String supComId,String nextSupComId)
        throws ParseException
    {
        if(null == materialContract || StringUtils.isBlank(supComId) || StringUtils.isBlank(nextSupComId))
        {
            return null;
        }
        List<MaterialContractDetail> contractDetailList = materialContractDao.getContractDeailList(materialContract);
        if(Collections3.isEmpty(contractDetailList))
        {
            return null;
        }

        // 赋值原料采购单
        Purchasing purchasing = new Purchasing();
        purchasing.setCompany(new Company(supComId));
//        purchasing.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString(),purchasing.getCompany()));
//        Integer noNum = purchasingDao.findPurchasingNoisEnable(purchasing);
//        while (noNum != null && noNum > 0)
//        {
//            // 将对应编码的nextNo 修改为+1
//            CommonUtils.updateNextNo(CommonEnums.CodeType.PURCHASEORDER.getIndex(), purchasing.getCompany());
//            purchasing.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PURCHASEORDER.getIndex().toString(), purchasing.getCompany()));
//            noNum = purchasingDao.findPurchasingNoisEnable(purchasing);// 查询编号是否依旧存在
//        }
        Supplier sup = new Supplier();
        sup.setCompanyId(supComId);
        sup.setErpId(nextSupComId);
        String supperId = supplierDao.getSupplierIdByErpId(sup);
        if (StringUtils.isNotBlank(supperId))
        {
            sup.setRecordId(supperId);
        }
        else
        {
            Company company = new Company(supComId);
            // 创建客户
            Supplier s = new Supplier();
            s.setCompany(company);
            Company nexrtSupCom =companyDao.get(nextSupComId);
            s.setName(nexrtSupCom.getName());
            s.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.SUPPLIER.getIndex().toString(),company));
            Integer noNum = supplierDao.findSupplierNoisEnable(s);
            while (noNum != null && noNum > 0)
            {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.SUPPLIER.getIndex(), company);
                s.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.SUPPLIER.getIndex().toString(), company));
                noNum = supplierDao.findSupplierNoisEnable(s);// 查询编号是否依旧存在
            }
            s.setShortName(nexrtSupCom.getName());
            s.setCreatedDate(new Date());
            s.setLastUpdDate(new Date());
            s.setCheckDate(1);
            s.setStatus(TypeKey.MD_CUSTOMER_STATUS_NORMAL);
            s.setErpId(nextSupComId);

            supplierDao.insert(s);
            // 添加成功把对应编码的nextNo 修改为+1
            CommonUtils.updateNextNo(CommonEnums.CodeType.SUPPLIER.getIndex());
            sup.setRecordId(s.getRecordId());
        }
        ChangeDataUtils change =  new ChangeDataUtils();
        String purchasingNo = change.getPurchasingMaterialNo(supComId,2,sup.getRecordId());
        purchasing.setNo(purchasingNo);
        purchasing.setSupplier(sup);
        if(null != materialContract.getOrderDate())
        {
            purchasing.setOrderDate(materialContract.getOrderDate());
        }
        purchasing.setDeliveryDate(materialContract.getDeliveryDate());
        if(StringUtils.isNotBlank(materialContract.getDeliveryWay()))
        {
            purchasing.setDeliveryWay(Long.valueOf(materialContract.getDeliveryWay()));
        }
        if(StringUtils.isNotBlank(materialContract.getCurrencyType()))
        {
            purchasing.setCurrencyType(Long.valueOf(materialContract.getCurrencyType()));
        }
        if(StringUtils.isNotBlank(materialContract.getPayWay()))
        {
            purchasing.setPayWay(Integer.valueOf(materialContract.getPayWay()));
        }
        if(StringUtils.isNotBlank(materialContract.getPayDays()))
        {
            purchasing.setPayDays(Integer.valueOf(materialContract.getPayDays()));
        }
        if(StringUtils.isNotBlank(materialContract.getTaxDescript()))
        {
            purchasing.setTaxDescript(Long.valueOf(materialContract.getTaxDescript()));
        }
        if(StringUtils.isNotBlank(materialContract.getAssureDays()))
        {
            purchasing.setAssureDays(Long.valueOf(materialContract.getAssureDays()));
        }
        if(StringUtils.isNotBlank(materialContract.getPaycause()))
        {
            purchasing.setPaycause(Long.valueOf(materialContract.getPaycause()));
        }
        if(StringUtils.isNotBlank(materialContract.getPurchaser()))
        {
            purchasing.setPurchaser(new User(materialContract.getPurchaser()));
        }
        purchasing.setStatus(TypeKey.PU_PURCHASING_STATUS_APPROVE);
        if(StringUtils.isNotBlank(materialContract.getBranchId()))
        {
            purchasing.setBranch(new Branch(materialContract.getBranchId()));
        }
        if (StringUtils.isNotBlank(materialContract.getSourcePurchaseOrdersId()))
        {
            purchasing.setSourcePurchaseOrdersId(materialContract.getSourcePurchaseOrdersId());
        }
        purchasing.setSupplierContractNo(materialContract.getSupplierContractNo());
        purchasing.setCreatedBy(UserUtils.getUser());
        purchasing.setCreatedDate(new Date());
        purchasing.setType("1");
        purchasingDao.insert(purchasing);

        for(MaterialContractDetail detail : contractDetailList)
        {
            PurchasingDetail purDetail = new PurchasingDetail();
            purDetail.setCompany(purchasing.getCompany());
            purDetail.setPurchasing(purchasing);
            purDetail.setQuantity(detail.getQuantity());
            purDetail.setMaterial(new Material(detail.getMaterialId()));
            purDetail.setPrice(detail.getPrice());
            purDetail.setAmount(detail.getAmount());
            purDetail.setCraftDescript(detail.getCraftDescript());
            purDetail.setStatus(TypeKey.PU_PURCHASING_STATUS_APPROVE);
            purDetail.setCreatedDate(new Date());
            purDetail.setSupplierModel(detail.getSupplierModel());
            purDetail.setDeliveryDate(detail.getDeliveryDate());
            String id = materialContractDao.getMatContractDetailId(purchasing.getCompany().getRecordId(),detail.getOrgId());
            if(StringUtils.isNotBlank(id))
            {
                purDetail.setContractDeailId(id);
            }
            else
            {
                purDetail.setContractDeailId(detail.getRecordId());
            }
            purDetail.setRemark(detail.getRemark());
            purchasingDetailDao.insert(purDetail);
        }
        return purchasing;
    }

/*    public MaterialContract setSupMaterialContract(Purchasing purchasing,String supComId,String cusComId,Integer status,Integer type)
    {
        Purchasing order = purchasingDao.get(purchasing.getRecordId());
        MaterialContract materialContract = new MaterialContract();
        materialContract.setCompanyId(supComId);
        materialContract.setSourcePurchaseOrdersId(purchasing.getSourcePurchaseOrdersId());
        purchToContract(materialContract, order,cusComId,status,type);
        materialContractDao.insert(materialContract);
        List<PurchasingDetail> list = purchasingDetailDao.getDeailByIds(order);
        List<MaterialContractDetail> details = new ArrayList<>();
        for (int i = 0; i < list.size(); i++)
        {
            MaterialContractDetail detail = new MaterialContractDetail();
            detail.setCompanyId(supComId);
            detail.setPurchasingId(materialContract.getRecordId());
            if(type.compareTo(2) == 0)
            {
                BigDecimal quantity = null == list.get(i).getQuantity() ? BigDecimal.ZERO : list.get(i).getQuantity();
                BigDecimal amount = null == list.get(i).getAmount() ? BigDecimal.ZERO : list.get(i).getAmount();
                BigDecimal paymentDays = null == purchasing.getPaymentDays() ? BigDecimal.ZERO : new BigDecimal(purchasing.getPaymentDays());
                BigDecimal radio = null == purchasing.getRadio() ? BigDecimal.ZERO : purchasing.getRadio();

                // 服务费 = 金额 * 80% * 费率 / 30天 * 付款天数
                BigDecimal serviceFee = amount.multiply(new BigDecimal(0.8)).multiply(radio).multiply(paymentDays).divide(new BigDecimal(30).multiply(new BigDecimal(100)),2, BigDecimal.ROUND_HALF_UP);

                // 平摊单价 = (服务费 + 金额) / 数量
                BigDecimal flatUnitPrice = BigDecimal.ZERO;
                if(quantity.compareTo(BigDecimal.ZERO) > 0)
                {
                    flatUnitPrice = (serviceFee.add(amount)).divide(quantity,6, BigDecimal.ROUND_HALF_UP);
                }
                detail.setServiceFee(serviceFee);
                detail.setFlatUnitPrice(flatUnitPrice);
            }
            purchToContractDeail(detail, list.get(i),cusComId,status);
            details.add(detail);
        }
        materialContractDao.batchInsertDeail(details);
        return materialContract;
    }*/

    public Ingredients getIngredientss(Purchasing purchasing)
    {
        if(null == purchasing)
        {
            return null;
        }
        return ingredientsDao.getIngredients(purchasing);
    }

    /**
     * zjn 供应链账单转期
     * @param check 付款对账单明细
     * @param insert 插入的付款对账单明细
     */
    @Transactional(readOnly = false)
    public void supplyChainRenewal(MaterialCheck check,MaterialCheck insert)
    {
        if(null == check)
        {
            return;
        }
        if(StringUtils.isBlank(check.getOrgId()) || check.getMaterialProperty().equals("成品"))
        {
            return;
        }
        // 获取供应链收款对账单明细集合
        List<GoodsCheck> goodCheckList = goodsCheckDao.getListByOrgId(check);
        if(Collections3.isEmpty(goodCheckList))
        {
            return;
        }
        // 获取供应链付款对账单明细集合
        List<MaterialCheck> materialCheckList = materialCheckDao.getListByOrgId(check);
        if(Collections3.isEmpty(materialCheckList))
        {
            return;
        }
        if(null != insert)
        {
            materialCheckDao.updateSourceId(insert.getRecordId(),insert.getRecordId());
        }
        // 调整属性赋值
        for(GoodsCheck gc : goodCheckList)
        {
            gc.setPeriod(check.getPeriod());
            gc.setPeriodFirst(check.getPeriodFirst());
            gc.setAdjustQty(check.getAdjustQty());
        }
        for(MaterialCheck mc : materialCheckList)
        {
            if(check.getRecordId().equals(mc.getRecordId()))
            {
                continue;
            }
            mc.setPeriod(check.getPeriod());
            mc.setPeriodFirst(check.getPeriodFirst());
            mc.setAdjustQty(check.getAdjustQty());
        }

        List<GoodsCheck> updateReceivableList = Lists.newArrayList();
        List<GoodsCheck> batchInsertReceivableList = Lists.newArrayList();
        List<GoodsCheck> batchUpdateReceivableList = Lists.newArrayList();
        List<GoodsCheck> batchDeleteReceivableList = Lists.newArrayList();
        if(Collections3.isNotEmpty(goodCheckList))
        {
            for(GoodsCheck gc : goodCheckList)
            {
                receivableService.confirmAdjusHandle(gc, batchInsertReceivableList, batchUpdateReceivableList, batchDeleteReceivableList);
                updateReceivableList.add(gc);
            }
        }

        // 批量添加
        if (Collections3.isNotEmpty(batchInsertReceivableList))
        {
            for(GoodsCheck gc : batchInsertReceivableList)
            {
                gc.setOrgId(insert.getRecordId());
                gc.setType("2");
            }
            goodsCheckDao.batchInsertAdjustData(batchInsertReceivableList);
        }
        // 批量更新
        if (Collections3.isNotEmpty(batchUpdateReceivableList))
        {
            goodsCheckDao.batchUpdateAdjustData(batchUpdateReceivableList);
        }
        // 批量删除
        if (Collections3.isNotEmpty(batchDeleteReceivableList))
        {
            String deleteIds = null;
            for(GoodsCheck gc : batchDeleteReceivableList)
            {
                if(StringUtils.isNotBlank(deleteIds))
                {
                    deleteIds = deleteIds +","+ gc.getRecordId();
                }
                else
                {
                    deleteIds = gc.getRecordId();
                }
            }
            goodsCheckDao.batchDeleteAdjustData(deleteIds);
        }

        // 更新对账单金额
        if (Collections3.isNotEmpty(updateReceivableList))
        {
            Set<String> companyIds =
                updateReceivableList.stream().map(GoodsCheck::getCompanyId).collect(Collectors.toSet());
            for (String companyId : companyIds)
            {
                for (GoodsCheck goodsCheck : updateReceivableList)
                {
                    if (companyId.equals(goodsCheck.getCompanyId()))
                    {
                        receivableService.updateReceivableData(goodsCheck, goodsCheck.getPeriodFirst(), goodsCheck.getPeriod());
                        break;
                    }
                }
            }
        }

        List<MaterialCheck> updatePayableList = Lists.newArrayList();
        List<MaterialCheck> batchInsertPayableList = Lists.newArrayList();
        List<MaterialCheck> batchUpdatePayableList = Lists.newArrayList();
        List<MaterialCheck> batchDeletePayableList = Lists.newArrayList();
        if(Collections3.isNotEmpty(materialCheckList))
        {
            for(MaterialCheck mc : materialCheckList)
            {
                payableService.confirmAdjusHandle(mc, batchInsertPayableList, batchUpdatePayableList, batchDeletePayableList);
                updatePayableList.add(mc);
            }
        }

        // 批量添加
        if (Collections3.isNotEmpty(batchInsertPayableList))
        {
            for(MaterialCheck mc : batchInsertPayableList)
            {
                mc.setOrgId(insert.getRecordId());
            }
            materialCheckDao.batchInsertAdjustData(batchInsertPayableList);
        }
        // 批量更新
        if (Collections3.isNotEmpty(batchUpdatePayableList))
        {
            materialCheckDao.batchUpdateAdjustData(batchUpdatePayableList);
        }
        // 批量删除
        if (Collections3.isNotEmpty(batchDeletePayableList))
        {
            String deleteIds = null;
            for(MaterialCheck mc : batchDeletePayableList)
            {
                if(StringUtils.isNotBlank(deleteIds))
                {
                    deleteIds = deleteIds +","+ mc.getRecordId();
                }
                else
                {
                    deleteIds = mc.getRecordId();
                }
            }
            materialCheckDao.batchDeleteAdjustData(deleteIds);
        }

        // 更新对账单金额
        if (Collections3.isNotEmpty(updatePayableList))
        {
            Set<String> companyIds =
                updatePayableList.stream().map(MaterialCheck::getCompanyId).collect(Collectors.toSet());
            for (String companyId : companyIds)
            {
                for (MaterialCheck mc : updatePayableList)
                {
                    if (companyId.equals(mc.getCompanyId()))
                    {
                        payableService.updatePayableData(mc, mc.getPeriodFirst(), mc.getPeriod());
                        break;
                    }
                }
            }
        }

        // 获取源生成的付款对账单明细id
        if(null != insert)
        {
            String sourceGcId = materialCheckDao.getSourceGcId(materialCheckList.get(0).getCompanyId(),insert.getRecordId());
            if(StringUtils.isNotBlank(sourceGcId))
            {
                materialCheckDao.updateSourceIdTwo(sourceGcId,insert.getRecordId());
                goodsCheckDao.updateSourceIdTwo(sourceGcId,insert.getRecordId());
            }
        }
    }
}
