package com.kyb.pcberp.common.pdf.ext;

import java.util.Date;

import com.itextpdf.text.Font;
import com.itextpdf.text.Paragraph;
import com.kyb.pcberp.common.utils.FmtUtils;

public class ParagraphEn extends Paragraph
{
    
    private static final long serialVersionUID = 2774975317930417840L;
    
    private static Font font_cn10;
    
    static
    {
        font_cn10 = new Font(Font.FontFamily.COURIER, 10, Font.NORMAL);
    }
    
    public ParagraphEn(String value)
    {
        super(value, font_cn10);
    }
    
    public ParagraphEn(Long value)
    {
        this(String.valueOf(value));
    }
    
    public ParagraphEn(Integer value)
    {
        this(String.valueOf(value));
    }
    
    public ParagraphEn(Date value)
    {
        this(FmtUtils.formatDate(value));
    }
}
