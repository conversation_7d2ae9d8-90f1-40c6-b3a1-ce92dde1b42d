package com.kyb.pcberp.modules.stock.dao;

import java.util.List;
import java.util.Map;

import com.kyb.pcberp.modules.production.entity.Feeding;
import com.kyb.pcberp.modules.stock.entity.MaterialPlace;
import org.apache.ibatis.annotations.Param;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.crm.entity.GoodsCheck;
import com.kyb.pcberp.modules.purch.entity.MaterialCheck;
import com.kyb.pcberp.modules.stock.entity.MaterialUse;
import com.kyb.pcberp.modules.sys.entity.Company;

/**
 * wc:2016-11-30 物料领用单dao
 */
@MyBatisDao
public interface MaterialUseDao extends CrudDao<MaterialUse>
{
    /**
     * 修改物料领用单
     */
    public void updateMaterialUse(MaterialUse materialUse);
    
    /**
     * 查询出所有未完成的领用单列表
     * 
     * @param company
     * @return
     */
    public List<MaterialUse> findMaterialUseList(Company company);
    
    /**
     * 原料出库时更新领用单的状态和已领用数量
     */
    public void updateMaterialUseStatusAndActualQuantity(MaterialUse materialUse);
    
    /**
     * 导出物料领用单
     */
    public List<MaterialUse> findExpList(MaterialUse materialUse);
    
    /**
     * 检查领用单编号是否被引用
     */
    public Integer findMaterialUseNo(@Param("no") String no, @Param("companyId") String companyId);
    
    /**
     * 更新状态
     */
    public void updateMaterialUseStatus(MaterialUse materialUse);
    
    /**
     * 查询领用单状态
     */
    public MaterialUse getMaterialUseStatus(MaterialUse materialUse);
    
    /**
     * ycy 2017-1-13 根据物料id和出库时间查询领用单
     * 
     * @param materialUse
     * @return
     */
    public MaterialUse getMaterialUse(MaterialUse materialUse);
    
    /**
     * ycy 2017-01-18 修改状态
     */
    public void updateStatus(MaterialUse materialUse);
    
    public MaterialUse print(MaterialUse materialUse);
    
    /** zjn 2018-08-13 物联网领用列表 */
    public List<MaterialUse> miFindList(MaterialUse materialUse);
    
    /** zjn 2018-08-14 根据付款对账明细id获取领用单备注 */
    public MaterialUse getRemarkByMaterialCheckId(MaterialCheck materialCheck);
    
    /** zjn 2018-08-14 根据收款对账明细id获取领用单备注 */
    public MaterialUse getRemarkByGoodsCheckId(GoodsCheck goodsCheck);
    
    /** zjn 2018-08-14 更新领用单备注 */
    public void updateRemark(MaterialUse materialUse);
    
    /** zjn 2019-01-08 下单助手：我的领用获取公司领用单 */
    public List<MaterialUse> getOrderMaterialUseList(Map<String, String> map);
    
    void updatePrintNo(MaterialUse materialUse);

    List<MaterialUse> getMantissaMaterialUseData(MaterialUse materialUse);

    List<MaterialUse> getListByIds(@Param("recordId") String recordId);

    List<MaterialUse> getOutStoreQtyList(@Param("recordId") String recordId);

    void deleteByFeed(Feeding feeding);

    void updateApprove(MaterialUse materialUse);

    List<MaterialUse> getMaterialUseList(MaterialUse materialUse);

    List<MaterialUse> getReceipt(MaterialUse materialUse);

    void updatePrice(MaterialUse materialUse);

    List<MaterialUse> materialUseList(MaterialPlace materialPlace);

    List<MaterialUse> materialUseListTwo(MaterialPlace materialPlace);
}
