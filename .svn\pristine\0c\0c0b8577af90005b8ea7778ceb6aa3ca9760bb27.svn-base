package com.kyb.pcberp.modules.report.utils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.modules.contract.dao.ContractDao;
import com.kyb.pcberp.modules.contract.dao.ContractDetailDao;
import com.kyb.pcberp.modules.contract.entity.Contract;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.crm.dao.AccountsReceivableDao;
import com.kyb.pcberp.modules.crm.dao.CustomerDao;
import com.kyb.pcberp.modules.crm.dao.GoodsCheckDao;
import com.kyb.pcberp.modules.crm.entity.AccountsReceivable;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.crm.entity.GoodsCheck;
import com.kyb.pcberp.modules.purch.dao.AccountsPayableDao;
import com.kyb.pcberp.modules.purch.dao.MaterialCheckDao;
import com.kyb.pcberp.modules.purch.dao.SupplierDao;
import com.kyb.pcberp.modules.purch.entity.AccountsPayable;
import com.kyb.pcberp.modules.purch.entity.MaterialCheck;
import com.kyb.pcberp.modules.purch.entity.Supplier;
import com.kyb.pcberp.modules.report.dao.DeliveryAssessmentDao;
import com.kyb.pcberp.modules.report.dao.ReportCapacityDao;
import com.kyb.pcberp.modules.report.dao.ReportComplaintDao;
import com.kyb.pcberp.modules.report.entity.*;
import com.kyb.pcberp.modules.stock.dao.ProductStoreDao;
import com.kyb.pcberp.modules.stock.entity.ProductStore;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import com.kyb.pcberp.modules.sys.dao.DepartmentDao;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.GroupOrgRelation;

/**
 * 2018-10-26 报表插入工具类
 */

public class ReportAllUtils
{
    private static ContractDao contractDao = SpringContextHolder.getBean(ContractDao.class);
    
    private static ContractDetailDao contractDetailDao = SpringContextHolder.getBean(ContractDetailDao.class);
    
    private static ProductStoreDao pstoreDao = SpringContextHolder.getBean(ProductStoreDao.class);

    private static DeliveryAssessmentDao deliveryAssessmentDao = SpringContextHolder.getBean(DeliveryAssessmentDao.class);

    private static CompanyDao companyDao = SpringContextHolder.getBean(CompanyDao.class);

    private static CustomerDao customerDao = SpringContextHolder.getBean(CustomerDao.class);

    private static GoodsCheckDao goodsCheckDao = SpringContextHolder.getBean(GoodsCheckDao.class);

    private static AccountsReceivableDao accountsReceivableDao = SpringContextHolder.getBean(AccountsReceivableDao.class);

    private static MaterialCheckDao materialCheckDao = SpringContextHolder.getBean(MaterialCheckDao.class);

    private static AccountsPayableDao accountsPayableDao = SpringContextHolder.getBean(AccountsPayableDao.class);

    private static SupplierDao supplierDao = SpringContextHolder.getBean(SupplierDao.class);

    private static ReportCapacityDao reportCapacityDao = SpringContextHolder.getBean(ReportCapacityDao.class);

    private static ReportComplaintDao reportComplaintDao = SpringContextHolder.getBean(ReportComplaintDao.class);

    private static DepartmentDao departmentDao = SpringContextHolder.getBean(DepartmentDao.class);
    
    /**
     * lq 2018-10-09 成品出入库统计
     *
     */
    @Transactional(readOnly = false)
    public static void countInOrOutStore(ProductStore productStore, Integer type)
    {
        // 11生产入库 12 送货出库 13退货入库 14 采购入库 15 补货入库 16 退货出库
        
        if (null == productStore.getTempArea())
        {
            // 判断统计的类型
            switch (productStore.getInoutType())
            {
                case 11:
                    // 生产入库
                    productStore.setTempArea(productStore.getProductionInStoreArea());
                    pstoreDao.updateTempArea(productStore);
                    break;
                case 12:
                    // 送货出库
                    productStore.setTempArea(productStore.getDeliveryOutStoreArea());
                    pstoreDao.updateTempArea(productStore);
                    break;
                case 13:
                    ContractDetail contractDetail = new ContractDetail();
                    if (productStore.getContractDetail().getRecordId() != null)
                    {
                        contractDetail = contractDetailDao.get(productStore.getContractDetail());
                        
                    }
                    else
                    {
                        contractDetail = contractDetailDao
                            .get(productStore.getProduceBatchDetail().getNotification().getContractDetail());
                    }
                    
                    Contract contract = contractDao.get(contractDetail.getContract());
                    contractDetail.setContract(contract);
                    productStore.setContractDetail(contractDetail);
                    // 退货入库
                    // 用面积除以数量得到 单个面积
                    BigDecimal singleArea = new BigDecimal(contractDetail.getOrderDeailArea())
                        .divide(new BigDecimal(contractDetail.getQuantity()), 6, BigDecimal.ROUND_HALF_UP);
                    productStore.setRefundInStoreArea(singleArea.multiply(new BigDecimal(productStore.getQuantity())));
                    // 每次出入库都记录面积
                    productStore.setTempArea(productStore.getRefundInStoreArea());
                    pstoreDao.updateTempArea(productStore);
                    
                    break;
                case 14:
                    // 采购入库
                    productStore.setTempArea(productStore.getPurchaseInStoreArea());
                    pstoreDao.updateTempArea(productStore);
                    break;
                case 15:
                    // 补货入库
                    productStore.setTempArea(productStore.getReplenishmentInStoreArea());
                    pstoreDao.updateTempArea(productStore);
                    
                    break;
                case 16:
                    // 退货出库
                    productStore.setTempArea(productStore.getRefundOutStoreArea());
                    pstoreDao.updateTempArea(productStore);
                    break;
            }
        }
    }

    /**
     * 结账
     * @param checkOutType-结账类型：1交货考核，2市场报备考核
     * @param checkoutData-结账数据
     * @param versionData-版本数据
     * @param company 公司
     */
    @Transactional(readOnly = false)
    public static Map<String,Object> saveCheckOutData(String checkOutType,String checkoutData,String versionData,Company company)
        throws CloneNotSupportedException
    {
        Map<String,Object> data = new HashMap<>();
        Boolean flag = false;
        DeliveryAssessment saveObj = new DeliveryAssessment();
        saveObj.setCompany(company);
        saveObj.preInsert();
        switch (checkOutType)
        {
            // 交货考核
            case "1":
                DeliveryAssessmentVersion version = JSON.parseObject(versionData,DeliveryAssessmentVersion.class);
                List<DeliveryAssessment> daList = JSON.parseArray(checkoutData,DeliveryAssessment.class);
                if(null == version || Collections3.isEmpty(daList))
                {
                    flag = true;
                    break;
                }
                saveObj.setDaList(daList);
                // 修改版本为已使用
                version.setUseFlag("1");
                deliveryAssessmentDao.updateVesionUseFlag(version);
                break;
            // 市场报备考核
            case "2":
                version = JSON.parseObject(versionData,DeliveryAssessmentVersion.class);
                List<MarketAssessment> maList = JSON.parseArray(checkoutData, MarketAssessment.class);
                if(null == version || Collections3.isEmpty(maList))
                {
                    flag = true;
                    break;
                }
                saveObj.setMaList(maList);
                // 修改版本为已使用
                version.setUseFlag("1");
                deliveryAssessmentDao.updateVesionUseFlag(version);
                break;
        }
        if(flag)
        {
            data.put("result","fail");
            data.put("message","数据错误，请刷新重试!");
            return data;
        }
        // 生成记录
        generateRecords(checkOutType,saveObj);
        data.put("result","success");
        data.put("message","结账成功!");
        return data;
    }

    /**
     * 生成记录
     * @param checkOutType-结账类型：1交货考核，2市场报备考核
     * @param saveObj 操作数据
     */
    public static void generateRecords(String checkOutType,DeliveryAssessment saveObj)
        throws CloneNotSupportedException
    {
        if(StringUtils.isBlank(checkOutType) || null == saveObj)
        {
            return;
        }
        switch (checkOutType)
        {
            // 交货考核
            case "1":
                if(Collections3.isNotEmpty(saveObj.getDaList()))
                {
                    for(DeliveryAssessment da : saveObj.getDaList())
                    {
                        // 保存交货考核记录
                        da.setCompany(saveObj.getCompany());
                        da.setGroupOrgId(da.getGroupOrgId());
                        da.preInsert();
                        deliveryAssessmentDao.saveDeliveryAssessment(da);

                        // 保存交货考核明细记录
                        deliveryAssessmentDao.saveDeliveryAssessmentDetail(da);

                        String period = null;
                        if(Collections3.isNotEmpty(da.getDetailList()) && null != da.getDetailList().get(0))
                        {
                            period = da.getDetailList().get(0).getPeriod();
                        }
                        if(StringUtils.isBlank(period))
                        {
                            period = DateUtils.formatDate(new Date(),"yyyyMM");
                        }
                        da.setPeriod(period);

                        // 适配收款对账单明细
                        GoodsCheck gc = adaptationGoodsCheck(checkOutType,da);

                        // 生成账单
                        generateBill(da.getSaleComId(),gc,saveObj.getCompany());
                    }
                }
                break;
            // 市场报备考核
            case "2":
                for(MarketAssessment ma : saveObj.getMaList())
                {
                    // 保存市场报备考核记录
                    ma.setCompany(saveObj.getCompany());
                    ma.setGroupOrgId(ma.getGroupOrgId());
                    ma.preInsert();
                    deliveryAssessmentDao.saveMarketAssessment(ma);

                    // 保存市场报备考核明细记录
                    deliveryAssessmentDao.saveMarketAssessmentDetail(ma);

                    String period = DateUtils.formatDate(new Date(),"yyyyMM");
                    ma.setPeriod(period);

                    // 适配收款对账单明细
                    GoodsCheck gc = adaptationGoodsCheck(checkOutType,ma);

                    // 生成账单
                    generateBill(ma.getSaleComId(),gc,saveObj.getCompany());
                }
                break;
        }
    }

    /**
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @Description //TODO 产能考核结账
     * @Date 14:19 2023/2/22
     * @Param [versionId, dataList]
     */
    @Transactional(readOnly = false)
    public static Map<String, Object> saveCheckOutCapacity(String versionId, List<ReportCapacityAssessment> dataList,
        Company company)
        throws CloneNotSupportedException
    {
        Map<String, Object> data = new HashMap<>();
        if (null == versionId || Collections3.isEmpty(dataList))
        {
            data.put("result", "fail");
            data.put("message", "数据错误，请刷新重试!");
            return data;
        }
        reportCapacityDao.updateVesionUseFlag(1, versionId);
        List<ReportCapacityAssessment> assessmentList =
            reportCapacityDao.capacityAssessmentList(dataList.get(0).getYearMonth());
        if (assessmentList.size() > 0)
        {
            data.put("result", "fail");
            data.put("message", "该月份已结账!");
            return data;
        }
        generateCapacity(dataList, company);
        data.put("result", "success");
        data.put("message", "结账成功!");
        return data;
    }
    
    /**
     * 产能生成记录
     *
     * @param saveObj 操作数据
     */
    public static void generateCapacity(List<ReportCapacityAssessment> saveObj, Company company)
        throws CloneNotSupportedException
    {
        if (null == saveObj)
        {
            return;
        }
        for (ReportCapacityAssessment da : saveObj)
        {
            
            // 保存交货考核记录
            da.setCompany(company);
            da.preInsert();
            Integer i = reportCapacityDao.insertCapacityAssessment(da);
            if (i == null)
            {
                continue;
            }
            Integer id = reportCapacityDao.getCapacityAssessmentMaxId();
            da.setRecordId(id.toString());
            for (ReportCapacityAssessmentDetail reportCapacityAssessmentDetail : da.getUsedDetailList())
            {
                reportCapacityAssessmentDetail.setAssessmentId(id.toString());
                reportCapacityAssessmentDetail.setCompany(company);
                reportCapacityAssessmentDetail.preInsert();
            }
            // 保存交货考核明细记录
            if(da.getUsedDetailList().size()>0){
                reportCapacityDao.insertCapacityAssessmentDetail(da.getUsedDetailList());
            }
            String period = null;
            if (StringUtils.isNotBlank(da.getYearMonth()) && null != da.getYearMonth())
            {
                period = da.getYearMonth();
            }
            if (StringUtils.isBlank(period))
            {
                period = DateUtils.formatDate(new Date(), "yyyyMM");
            }
            da.setYearMonth(period);
            
            // 适配收款对账单明细
            GoodsCheck gc = adaptationGoodsCheck("3", da);
            
            // 集团部门
            List<GroupOrgRelation> deptList = CompanyUtil.getInstance().getGroupList();
            
            // 获取集团部门的销售公司
            String saleComIds = CompanyUtil.getInstance().getSaleIds();
            List<GroupOrgRelation> groupSaleComList = departmentDao.getGroupSaleComData(saleComIds);
            if (Collections3.isNotEmpty(deptList) && Collections3.isNotEmpty(groupSaleComList))
            {
                for (GroupOrgRelation dept : deptList)
                {
                    for (GroupOrgRelation groupSaleCom : groupSaleComList)
                    {
                        if (dept.getGroupOrgId().equals(groupSaleCom.getGroupOrgId()))
                        {
                            dept.setSaleComId(groupSaleCom.getSaleComId());
                            dept.setSaleComName(groupSaleCom.getSaleComName());
                            break;
                        }
                    }
                }
            }
            List<GroupOrgRelation> depts =
                deptList.stream().filter(r -> r.getGroupOrgId().equals(da.getDepartId())).collect(Collectors.toList());
            if (depts.size() > 0 && !BigDecimal.ZERO.equals(gc.getAmount()))
            {
                // 生成账单
                generateBill(depts.get(0).getSaleComId(), gc, da.getCompany());
            }
        }
    }
    
    /**
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @Description //TODO 客诉考核结账
     * @Date 14:19 2023/2/22
     * @Param [versionId, dataList]
     */
    @Transactional(readOnly = false)
    public static Map<String, Object> saveCheckOutComplaint(String versionId, List<ReportComplaintAssessment> dataList,
        Company company)
        throws CloneNotSupportedException
    {
        Map<String, Object> data = new HashMap<>();
        if (null == versionId || Collections3.isEmpty(dataList))
        {
            data.put("result", "fail");
            data.put("message", "数据错误，请刷新重试!");
            return data;
        }
        reportComplaintDao.updateVesionUseFlag(1, versionId);
        generateComplaint(dataList, company);
        data.put("result", "success");
        data.put("message", "结账成功!");
        return data;
    }
    
    /**
     * 产能生成记录
     *
     * @param saveObj 操作数据
     */
    public static void generateComplaint(List<ReportComplaintAssessment> saveObj, Company company)
        throws CloneNotSupportedException
    {
        if (null == saveObj)
        {
            return;
        }
        for (ReportComplaintAssessment da : saveObj)
        {
            // 保存交货考核记录
            da.setCompany(company);
            da.preInsert();
            Integer i = reportComplaintDao.insertComplaintAssessment(da);
            if (i == null)
            {
                continue;
            }
            for (ReportComplaintCustomerAssessmentDetail customerAssessmentDetail : da
                .getCustomerAssessmentDetailList())
            {
                customerAssessmentDetail.setCompany(company);
                customerAssessmentDetail.preInsert();
            }
            for (ReportComplaintDeliveryAssessmentDetail customerAssessmentDetail : da
                .getDeliveryAssessmentDetailList())
            {
                customerAssessmentDetail.setCompany(company);
                customerAssessmentDetail.preInsert();
            }
            // 保存交货考核明细记录
            if (da.getCustomerAssessmentDetailList().size() > 0)
            {
                reportComplaintDao.insertComplaintCustomerAssessment(da.getCustomerAssessmentDetailList());
            }
            if (da.getDeliveryAssessmentDetailList().size() > 0)
            {
                reportComplaintDao.insertDeliveryAssessmentDetail(da.getDeliveryAssessmentDetailList());
            }
            String period = null;
            if (StringUtils.isNotEmpty(da.getPeriodTime()))
            {
                period = da.getPeriodTime();
            }
            if (StringUtils.isBlank(period))
            {
                period = DateUtils.formatDate(new Date(), "yyyyMM");
            }
            da.setPeriodTime(period);
            
            // 适配收款对账单明细
            GoodsCheck gc = adaptationGoodsCheck("4", da);
            
            // 集团部门
            List<GroupOrgRelation> deptList = CompanyUtil.getInstance().getGroupList();
            
            // 获取集团部门的销售公司
            String saleComIds = CompanyUtil.getInstance().getSaleIds();
            List<GroupOrgRelation> groupSaleComList = departmentDao.getGroupSaleComData(saleComIds);
            if (Collections3.isNotEmpty(deptList) && Collections3.isNotEmpty(groupSaleComList))
            {
                for (GroupOrgRelation dept : deptList)
                {
                    for (GroupOrgRelation groupSaleCom : groupSaleComList)
                    {
                        if (dept.getGroupOrgId().equals(groupSaleCom.getGroupOrgId()))
                        {
                            dept.setSaleComId(groupSaleCom.getSaleComId());
                            dept.setSaleComName(groupSaleCom.getSaleComName());
                            break;
                        }
                    }
                }
            }
            if (da.getPenaltyAmount() != null && da.getPenaltyAmount() > 0)
            {
                List<GroupOrgRelation> depts = deptList.stream()
                    .filter(r -> r.getGroupOrgId().equals(da.getDepartId()))
                    .collect(Collectors.toList());
                if (depts.size() > 0)
                {
                    // 生成账单
                    generateBill(depts.get(0).getSaleComId(), gc, da.getCompany());
                }
            }
        }
    }

    /**
     * 适配收款对账单明细
     * @param checkOutType-结账类型：1交货考核，2市场报备考核
     * @param object-操作对象
     */
    public static GoodsCheck adaptationGoodsCheck(String checkOutType,Object object)
    {
        GoodsCheck goodsCheck = new GoodsCheck();
        if(StringUtils.isBlank(checkOutType) || null == object)
        {
            return goodsCheck;
        }
        goodsCheck.setInOutFlag(4); // 结账标志
        goodsCheck.setCheckOutType(checkOutType);
        switch (checkOutType)
        {
            // 交货考核
            case "1":
                DeliveryAssessment da = (DeliveryAssessment)object;
                if(null == da || StringUtils.isBlank(da.getRecordId()) || StringUtils.isBlank(da.getPeriod()))
                {
                    return goodsCheck;
                }
                goodsCheck.setSourceId(Long.valueOf(da.getRecordId()));
                goodsCheck.setPeriod(Integer.valueOf(da.getPeriod()));
                goodsCheck.setAmount(da.getRewardsPunishmentAmount());
                break;
            // 市场报备考核
            case "2":
                MarketAssessment ma = (MarketAssessment)object;
                if(null == ma || StringUtils.isBlank(ma.getRecordId()) || StringUtils.isBlank(ma.getPeriod()))
                {
                    return goodsCheck;
                }
                goodsCheck.setSourceId(Long.valueOf(ma.getRecordId()));
                goodsCheck.setPeriod(Integer.valueOf(ma.getPeriod()));
                goodsCheck.setAmount(ma.getClosingAmount());
                break;
            // 产能考核
            case "3":
                ReportCapacityAssessment capacityAssessment = (ReportCapacityAssessment)object;
                if (null == capacityAssessment || StringUtils.isBlank(capacityAssessment.getRecordId())
                    || StringUtils.isBlank(capacityAssessment.getYearMonth()))
                {
                    return goodsCheck;
                }
                goodsCheck.setSourceId(Long.valueOf(capacityAssessment.getRecordId()));
                goodsCheck.setPeriod(Integer.valueOf(capacityAssessment.getYearMonth()));
                goodsCheck.setAmount(new BigDecimal(capacityAssessment.getReconciliationAmount()));
                break;
            // 客诉考核
            case "4":
                ReportComplaintAssessment complaintAssessment = (ReportComplaintAssessment)object;
                if (null == complaintAssessment || StringUtils.isBlank(complaintAssessment.getRecordId())
                    || StringUtils.isBlank(complaintAssessment.getPeriodTime()))
                {
                    return goodsCheck;
                }
                goodsCheck.setSourceId(Long.valueOf(complaintAssessment.getRecordId()));
                goodsCheck.setPeriod(Integer.valueOf(complaintAssessment.getPeriodTime()));
                goodsCheck.setAmount(new BigDecimal(complaintAssessment.getPenaltyAmount()));
                break;
        }
        return goodsCheck;
    }

    /**
     * 生成账单
     * @param saleComId-销售公司id
     * @param goodsCheck 操作对象
     * @param company 公司
     */
    public static void generateBill(String saleComId,GoodsCheck goodsCheck,Company company)
        throws CloneNotSupportedException
    {
        if(StringUtils.isBlank(saleComId) || null == goodsCheck || null == company || saleComId.equals(company.getRecordId()))
        {
            return;
        }
        String ecoemyId = CompanyUtil.getInstance().getEcoemyId(); // 总部经济公司id
        Company ecoemy = companyDao.get(ecoemyId);
        Company saleCom = companyDao.get(saleComId);

        // 获取工厂总部经济客户
        Customer cus = new Customer();
        cus.setCompany(company);
        cus.setName(ecoemy.getName());
        Customer exoemyCus = customerDao.getCustomerByName(cus);
        if(null == exoemyCus)
        {
            cus.setCompany(company);
            cus.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CUSTOMER.getIndex().toString(),company));
            Integer noNum = customerDao.findCustomerNoisEnable(cus);
            while (noNum != null && noNum > 0)
            {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.CUSTOMER.getIndex(), company);
                cus.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CUSTOMER.getIndex().toString(), company));
                noNum = customerDao.findCustomerNoisEnable(cus);// 查询编号是否依旧存在
            }
            cus.setShortName(cus.getName());
            cus.setCreatedDate(new Date());
            cus.setLastUpdDate(new Date());
            cus.setCheckDate("1");
            cus.setStatus(TypeKey.MD_CUSTOMER_STATUS_NORMAL);
            customerDao.insert(cus);
            // 添加成功把对应编码的nextNo 修改为+1
            CommonUtils.updateNextNo(CommonEnums.CodeType.CUSTOMER.getIndex(),company);
        }

        // 获取总部经济销售公司客户
        cus.setCompany(ecoemy);
        cus.setName(saleCom.getName());
        Customer saleCus = customerDao.getCustomerByName(cus);
        if(null == saleCus)
        {
            cus.setCompany(company);
            cus.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CUSTOMER.getIndex().toString(),ecoemy));
            Integer noNum = customerDao.findCustomerNoisEnable(cus);
            while (noNum != null && noNum > 0)
            {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.CUSTOMER.getIndex(), ecoemy);
                cus.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.CUSTOMER.getIndex().toString(), ecoemy));
                noNum = customerDao.findCustomerNoisEnable(cus);// 查询编号是否依旧存在
            }
            cus.setShortName(cus.getName());
            cus.setCreatedDate(new Date());
            cus.setLastUpdDate(new Date());
            cus.setCheckDate("1");
            cus.setStatus(TypeKey.MD_CUSTOMER_STATUS_NORMAL);
            customerDao.insert(cus);
            // 添加成功把对应编码的nextNo 修改为+1
            CommonUtils.updateNextNo(CommonEnums.CodeType.CUSTOMER.getIndex(),company);
            saleCus = cus;
        }

        // 工厂账单明细
        goodsCheck.setCompany(company);
        goodsCheck.setCustomer(exoemyCus);
        goodsCheck.setStatus(********);
        goodsCheck.setOperateStatus(TypeKey.GOODSCHECK_STATUS_UNCONFIRM);
        goodsCheck.preInsert();
        goodsCheckDao.insert(goodsCheck);

        // 关联id
        goodsCheck.setOrgId(goodsCheck.getRecordId());
        goodsCheckDao.updateOrgId(goodsCheck);

        // 更新对账单结账金额
        AccountsReceivable ar = new AccountsReceivable();
        ar.setCompany(goodsCheck.getCompany());
        ar.setCustomer(goodsCheck.getCustomer());
        ar.setPeriod(goodsCheck.getPeriod());
        accountsReceivableDao.updateCheckOutAmount(ar);

        // 总部经济账单明细
        GoodsCheck lnGc = (GoodsCheck)goodsCheck.clone();
        lnGc.setRecordId(null);
        lnGc.setCompany(ecoemy);
        lnGc.setCustomer(saleCus);
        goodsCheckDao.insert(lnGc);

        // 更新对账单结账金额
        ar = new AccountsReceivable();
        ar.setCompany(lnGc.getCompany());
        ar.setCustomer(lnGc.getCustomer());
        ar.setPeriod(lnGc.getPeriod());
        accountsReceivableDao.updateCheckOutAmount(ar);

        // 获取销售公司供应商
        Supplier sup = new Supplier();
        sup.setCompanyId(saleCom.getRecordId());
        sup.setName(ecoemy.getName());
        Supplier saleSup = supplierDao.getSupByName(sup);
        if(null == saleSup)
        {
            sup.setCompany(saleCom);
            sup.setName(ecoemy.getName());
            sup.setShortName(ecoemy.getName());
            sup.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.SUPPLIER.getIndex().toString(), saleCom));
            Integer noNum = supplierDao.findSupplierNoisEnable(sup); // 查询编号是否依旧存在
            while (noNum != null && noNum > 0)
            {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.SUPPLIER.getIndex(), saleCom);
                sup.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.SUPPLIER.getIndex().toString(),
                    saleCom));
                noNum = supplierDao.findSupplierNoisEnable(sup); // 查询编号是否依旧存在
            }
            sup.setCreatedDate(new Date());
            sup.setLastUpdDate(new Date());
            supplierDao.insert(sup);
            // 添加成功把对应编码的nextNo 修改为+1
            CommonUtils.updateNextNo(CommonEnums.CodeType.SUPPLIER.getIndex(),saleCom);
            saleSup = sup;
        }

        // 获取总部经济供应商
        sup.setCompanyId(ecoemy.getRecordId());
        sup.setName(company.getName());
        Supplier lnSup = supplierDao.getSupByName(sup);
        if(null == lnSup)
        {
            sup.setCompany(saleCom);
            sup.setName(company.getName());
            sup.setShortName(company.getName());
            sup.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.SUPPLIER.getIndex().toString(), ecoemy));
            Integer noNum = supplierDao.findSupplierNoisEnable(sup); // 查询编号是否依旧存在
            while (noNum != null && noNum > 0)
            {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.SUPPLIER.getIndex(), ecoemy);
                sup.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.SUPPLIER.getIndex().toString(),
                    ecoemy));
                noNum = supplierDao.findSupplierNoisEnable(sup); // 查询编号是否依旧存在
            }
            sup.setCreatedDate(new Date());
            sup.setLastUpdDate(new Date());
            supplierDao.insert(sup);
            // 添加成功把对应编码的nextNo 修改为+1
            CommonUtils.updateNextNo(CommonEnums.CodeType.SUPPLIER.getIndex(),ecoemy);
            lnSup = sup;
        }

        // 销售付款账单明细
        MaterialCheck saleMc = new MaterialCheck();
        saleMc.setCompany(saleCom);
        saleMc.setSupplier(saleSup);
        saleMc.setStatus(********);
        saleMc.setOperateStatus(TypeKey.MATERIAL_STATUS_UNCONFIRM);
        saleMc.setPeriod(goodsCheck.getPeriod());
        saleMc.setInOutFlag(4);
        saleMc.setCheckOutType(goodsCheck.getCheckOutType());
        saleMc.setAmount(goodsCheck.getAmount());
        saleMc.setOrgId(goodsCheck.getOrgId());
        materialCheckDao.insert(saleMc);

        // 销售付款账单
        AccountsPayable ap = new AccountsPayable();
        ap.setCompany(saleMc.getCompany());
        ap.setSupplier(saleMc.getSupplier());
        ap.setPeriod(saleMc.getPeriod());
        accountsPayableDao.updateCheckOutAmount(ap);

        // 龙南付款账单明细
        MaterialCheck lnMc = saleMc.clone();
        lnMc.setRecordId(null);
        lnMc.setCompany(ecoemy);
        lnMc.setSupplier(lnSup);
        materialCheckDao.insert(lnMc);

        // 销售付款账单
        ap = new AccountsPayable();
        ap.setCompany(lnMc.getCompany());
        ap.setSupplier(lnMc.getSupplier());
        ap.setPeriod(lnMc.getPeriod());
        accountsPayableDao.updateCheckOutAmount(ap);
    }
}
