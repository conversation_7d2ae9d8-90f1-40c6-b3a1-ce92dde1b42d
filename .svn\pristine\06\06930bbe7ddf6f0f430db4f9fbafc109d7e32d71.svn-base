/**
 *
 */
package com.kyb.pcberp.modules.contract.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.common.utils.excel.annotation.ExcelField;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.crm.entity.CustomerContact;
import com.kyb.pcberp.modules.crm.entity.Quotation;
import com.kyb.pcberp.modules.crm.entity.SlCustomerOrder;
import com.kyb.pcberp.modules.sys.entity.Branch;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.User;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * 2015/08/31Entity
 *
 * <AUTHOR>
 * @version 2015-08-31
 */
public class Contract extends DataEntity<Contract>
{

    private static final long serialVersionUID = 1L;

    // 2018-08-24 lq 新增备注以及交货日期
    private String remark; // 合同备注

    private String deliveryTerm;// 交货日期

    private Integer findNoisEnable; // 在保存单据的时候 单据编号被占用了 就在前台赋值为 1 了 没有占用就是为0

    private Customer customer; // 客户ID

    private String no; // 合同编号

    private String customerPo; // 客户订单号

    private Date orderDate; // 合同签定日期

    private BigDecimal totalAmt; // 合同总金额

    private Long taxDescript; // 价格税率说明

    private Long currencyType; // 货币类型

    private Long payWay; // 付款方式

    private String payDays; // 付款期限，以天为计数单位

    private BigDecimal prePayAmount; // 预付款总额

    private BigDecimal prePayBalance; // 预付款余额

    private Long deliveryWay; // 提货方式

    private String deliveryPlace; // 交货地点

    private Long freightWay; // 交货方式

    private Long chkAcptWay; // 验收方式

    private String chkDissentDays; // 验收异议期限，以天为计算单位

    private String qualityStd; // 质量技术标准

    private Long assureDays; // 保证天数

    private String packagingStd; // 包装标准

    private String status; // 合同状态，1 未确认、2 已确认、3 生产中、4 已生产、5 已送货、6 已完成、 7 已取消

    private String cancelReason; // 合同取消原因

    private String contractId; // 详情查询编号

    private ContractDetail contractDetailList; //

    private List<ContractAttachements> contractAttachementsList = Lists.newArrayList(); // 合同附件 列表

    private ContractCraft contractCraftList; // 合同明细的工艺

    private Price price; // 合同价格

    private List<ContractDetail> contractDetails; // 合同明细列表

    private Branch branch; // branchid

    private Quotation quotation; // 合同引用报价单编号

    private int notificationId; // 通知单编号

    private Notification notification; // 通知单

    private BigDecimal money;// 整个合同价格 所有子表中价格加起来

    private Date sentTimeStartQr; // 时间段查询条件 载体

    private Date sentTimeEndQr;

    private String customerNo; // 客户编号 (导出列)

    private String craftNo; // 工艺要求编号

    private String customerModel; // 客户命名的产品类型（客户型号）

    private Long boardLevel; // pcb类型

    private Long materialType; // 覆铜板材

    private Long boardThickness; // 板材厚度、

    private Long copperCladThickness; // 覆铜要求

    private Long surfaceProcess; // 镀层处理

    private Long solderMaskType; // 阻焊类型

    private Long characterType; // 板面字符

    private Long shapingWay; // 成形方式

    private Long testMethod; // 测试要求

    private BigDecimal pnlWidth; // PNL宽度，单位mm

    private BigDecimal pnlLength; // PNL长度，单位mm

    private BigDecimal pnlDivisor; // PNL拼接数,指一个PNL中含有多少个PCS

    private Long lingeSpacing;// 最小线宽/线距

    private Long smallAperture;// 最小孔径

    private Long halAhole;// 半孔

    private Long buryBlindHole;// 埋盲孔

    private Long resistance;// 抗阻

    private Long deliveryUrgent;// 发货加急

    private BigDecimal engineeringFee; // 工程费

    private BigDecimal sampleFee; // 样板费

    private BigDecimal othersFee; // 其它费用

    private BigDecimal testShelfFee; // 测试架费

    private BigDecimal mouldFee; // 模具费

    private BigDecimal orderLowLimit; // 一次订单的最低限额

    private BigDecimal engineeringLimit; // 返还样板费最低限额

    private BigDecimal mouldLimit; // 返还模具费最低限额

    private BigDecimal testShelfLimit; // 返还测试架费最低限额

    private BigDecimal filmFee; // 菲林费用

    private String priceOfContract; // 合同明细单价

    private String quantity; // 合同明细数量

    private String clerk;// 业务员ID

    private boolean self = true;// 自己的合同

    private User user; // 业务员

    private SlCustomerOrder slCustomerOrder;// 客户订单

    private String orderArea;// 合同平米数

    private Boolean dangerPrice; // WC 2017-05-02 标识合同是否含有业务员给定单价小于系统报价设置算出的价格，合同预警

    private Long signplace; // 签订地点

    private Long deliverycity; // 交货地点

    private Long frightbear; // 运费承担

    private Long billformula; // 票据形式

    private Long hasquestion; // 如有问题

    private Long paycause; // 付款方式

    private Long inspectionstandard; // 检验标准

    private Long packingrequirement; // 包装方式

    private String inventorySwitch; // 库存开关

    private String customerMaterialNo; // 客户物料号

    private String salesMan; // 业务员

    private String documenter; // 跟单员

    private Boolean noNotification; // WC 2017-08-22 标识合同是否生成了通知单

    private String icloudId; // 云平台下单

    private String icloudType;// 下单类型 0：客户下单，1：业务员下单，2：云平台下单

    // 客户反馈信息
    private String customerRemark;

    // 商家反馈信息
    private String supplierRemark;

    private String type; // 合同类型 (云平台)

    private String billingState; // 云平台客户下单合同状态

    private Boolean selfPrdorder;// 是否自动生成采购单

    private String detailRemark;// 是否自动生成采购单

    private Company quiltCombinationCompany;

    private String flag;// 供应商为9

    private BigDecimal subTotal; // 合同明细金额

    private String batchInfo; // 明细的分批数据拼接

    private String detailId; // 合同明细Id

    private BigDecimal orderAmount; // 接单总金额(成品) zjn

    private BigDecimal orderAreaSum; // 接单总面积(成品) zjn

    private Integer ordersum; // 接单款数(成品) zjn

    private BigDecimal prdPrice;// 成品单价 zjn

    private String overDueStr;// 逾期合同筛选条件

    private String fileId;

    private List<ContractAttachMent> attachList;

    private String orderFlag;

    private String companyId;

    private String taxDescriptValue;

    private String currencyTypeValue;

    private String payWayValue;

    private String boardLevelValue;

    private String materialTypeValue;

    private String boardThicknessValue;

    private String copperCladThicknessValue;

    private String surfaceProcessValue;

    private String solderMaskTypeValue;

    private String characterTypeValue;

    private String shapingWayValue;

    private String testMethodValue;

    private String lingeSpacingValue;

    private String smallApertureValue;

    private String halAholeValue;

    private String buryBlindHoleValue;

    private String resistanceValue;

    private String deliveryUrgentValue;

    private String daoreValue;

    private String countersinkHole;

    private String customerPorderNo;

    private String cardSet; // 工程卡尺寸

    private String cardSetArea;

    private BigDecimal cardPnlLength;

    private BigDecimal cardPnlWidth;

    private BigDecimal cardPnlDivisor;

    private Integer closingCaseQuery;

    private String saleId;

    private String saleName;

    private String saleCsutomerId;

    private String branchQuery;

    private String customerId;

    private String branchId;

    private String groupCenterId;

    private Integer groupCenterCount;

    private Date deliveryDate;

    private String customerName;

    private String referenceTypeValue;

    private String specialCraftValue;

    private BigDecimal orderDeailArea;

    private BigDecimal processFee;

    private BigDecimal materialFee;

    private BigDecimal costFee;

    private CustomerContact customerContact;

    private BigDecimal squareMeterPrice;

    private Date oldOrderDate; // 旧合同签定日期(2022-05-13 修改为审核日期(没改名称))

    private Integer refPreContractId;// 备料合同id

    private Integer sourceType;

    private String refPreContractNo;// 备料合同no

    private String pcsSize; // pcs尺寸

    private Boolean showCheckFlag;

    private String hideCopyDetailFlag;// 有值时，过滤oldDetailId的合同明细

    private Double salePrecent;

    private String factoryComId;

    private String deptId;

    private String deptName;

    private String jxComId;

    private String lnComId;

    private String finalContractNo;

    private String ecoemyId;

    private String saleComId;

    private String processValue;

    private String quoteInfo;

    private String materialFeeInfo;

    private BigDecimal paidAmount;

    private BigDecimal exchangeRate; // 外币汇率

    private String exchangeType; // 汇率类型

    private Date exchangeDate; // 外汇时间

    private BigDecimal notaxPrdPrice; // 不含税采购价格

    private BigDecimal spareRate;

    private boolean sparestatus;

    private BigDecimal saleFee;

    private BigDecimal spareQuantity;

    private String forecastMaterialFee;

    private Date forecastEstimateDate;

    private String forecastUseRatio;

    private BigDecimal cardMouldFee;

    private BigDecimal cardTestShelfFee;

    private String msg;

    private String appId;

    private String linkName; // 联系人

    private String linkPhone; // 联系电话

    private String contractDetailsId ;

    private Integer year;

    private Integer month;

    private String checkDate;

    private String unitType;

    private String oldStatusInit;

    private String detailStatus;

    private String showProductionStatus;

    private String overdueStatus;

    private String productionStatus;

    private BigDecimal netCostFee; // 净成本

    private BigDecimal manageFee; // 管理费

    private String customerGrade; // 客户等级

    private BigDecimal barrelBoltFee; // 管销费

    private String interCustomRecordId;

    @ExcelField(title = "订单汇率", align = 2, sort = 62)
    public BigDecimal getExchangeRate()
    {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate)
    {
        this.exchangeRate = exchangeRate;
    }

    @ExcelField(title = "合同明细id", align = 2, sort = 105)
    public String getRecordId()
    {
        return recordId;
    }

    public void setRecordId(String recordId)
    {
        this.recordId = recordId;
    }

    @Override
    public String getRemark()
    {
        return remark;
    }

    @Override
    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public String getDeliveryTerm()
    {
        return deliveryTerm;
    }

    public void setDeliveryTerm(String deliveryTerm)
    {
        this.deliveryTerm = deliveryTerm;
    }

    public Integer getFindNoisEnable()
    {
        return findNoisEnable;
    }

    public void setFindNoisEnable(Integer findNoisEnable)
    {
        this.findNoisEnable = findNoisEnable;
    }

    @ExcelField(title = "客户物料号", align = 2, sort = 105)
    public String getCustomerMaterialNo()
    {
        return customerMaterialNo;
    }

    public void setCustomerMaterialNo(String customerMaterialNo)
    {
        this.customerMaterialNo = customerMaterialNo;
    }

    @ExcelField(title = "数量", align = 2, sort = 340)
    public String getQuantity()
    {
        return quantity;
    }

    public void setQuantity(String quantity)
    {
        this.quantity = quantity;
    }

    @ExcelField(title = "单价", align = 2, sort = 330)
    public String getPriceOfContract()
    {
        return priceOfContract;
    }

    public void setPriceOfContract(String priceOfContract)
    {
        this.priceOfContract = priceOfContract;
    }

    @ExcelField(title = "样板费用", align = 2, sort = 240)
    public BigDecimal getSampleFee()
    {
        return sampleFee;
    }

    public void setSampleFee(BigDecimal sampleFee)
    {
        this.sampleFee = sampleFee;
    }

    @ExcelField(title = "其它费用", align = 2, sort = 250)
    public BigDecimal getOthersFee()
    {
        return othersFee;
    }

    public void setOthersFee(BigDecimal othersFee)
    {
        this.othersFee = othersFee;
    }

    @ExcelField(title = "测试架费", align = 2, sort = 260)
    public BigDecimal getTestShelfFee()
    {
        return testShelfFee;
    }

    public void setTestShelfFee(BigDecimal testShelfFee)
    {
        this.testShelfFee = testShelfFee;
    }

    @ExcelField(title = "模具费用", align = 2, sort = 270)
    public BigDecimal getMouldFee()
    {
        return mouldFee;
    }

    public void setMouldFee(BigDecimal mouldFee)
    {
        this.mouldFee = mouldFee;
    }

    @ExcelField(title = "单项产品限额", align = 2, sort = 280)
    public BigDecimal getOrderLowLimit()
    {
        return orderLowLimit;
    }

    public void setOrderLowLimit(BigDecimal orderLowLimit)
    {
        this.orderLowLimit = orderLowLimit;
    }

    @ExcelField(title = "样板返还费", align = 2, sort = 290)
    public BigDecimal getEngineeringLimit()
    {
        return engineeringLimit;
    }

    public void setEngineeringLimit(BigDecimal engineeringLimit)
    {
        this.engineeringLimit = engineeringLimit;
    }

    @ExcelField(title = "模具费返还费", align = 2, sort = 300)
    public BigDecimal getMouldLimit()
    {
        return mouldLimit;
    }

    public void setMouldLimit(BigDecimal mouldLimit)
    {
        this.mouldLimit = mouldLimit;
    }

    @ExcelField(title = "测试架费返还费", align = 2, sort = 310)
    public BigDecimal getTestShelfLimit()
    {
        return testShelfLimit;
    }

    public void setTestShelfLimit(BigDecimal testShelfLimit)
    {
        this.testShelfLimit = testShelfLimit;
    }

    @ExcelField(title = "菲林费用", align = 2, sort = 320)
    public BigDecimal getFilmFee()
    {
        return filmFee;
    }

    public void setFilmFee(BigDecimal filmFee)
    {
        this.filmFee = filmFee;
    }

    @ExcelField(title = "生产编号", align = 2, sort = 90)
    public String getCraftNo()
    {
        return craftNo;
    }

    public void setCraftNo(String craftNo)
    {
        this.craftNo = craftNo;
    }

    @ExcelField(title = "客户型号", align = 2, sort = 100)
    public String getCustomerModel()
    {
        return customerModel;
    }

    public void setCustomerModel(String customerModel)
    {
        this.customerModel = customerModel;
    }

    public Long getBoardLevel()
    {
        return boardLevel;
    }

    public void setBoardLevel(Long boardLevel)
    {
        this.boardLevel = boardLevel;
    }

    @ExcelField(title = "PCB类型", align = 2, sort = 110)
    public String getBoardLevelValue()
    {
        return boardLevelValue;
    }

    public void setBoardLevelValue(String boardLevelValue)
    {
        this.boardLevelValue = boardLevelValue;
    }

    public Long getMaterialType()
    {
        return materialType;
    }

    public void setMaterialType(Long materialType)
    {
        this.materialType = materialType;
    }

    @ExcelField(title = "覆铜板材", align = 2, sort = 120)
    public String getMaterialTypeValue()
    {
        return materialTypeValue;
    }

    public void setMaterialTypeValue(String materialTypeValue)
    {
        this.materialTypeValue = materialTypeValue;
    }

    public Long getBoardThickness()
    {
        return boardThickness;
    }

    public void setBoardThickness(Long boardThickness)
    {
        this.boardThickness = boardThickness;
    }

    @ExcelField(title = "板材厚度", align = 2, sort = 130)
    public String getBoardThicknessValue()
    {
        return boardThicknessValue;
    }

    public void setBoardThicknessValue(String boardThicknessValue)
    {
        this.boardThicknessValue = boardThicknessValue;
    }

    public Long getCopperCladThickness()
    {
        return copperCladThickness;
    }

    public void setCopperCladThickness(Long copperCladThickness)
    {
        this.copperCladThickness = copperCladThickness;
    }

    @ExcelField(title = "覆铜要求", align = 2, sort = 140)
    public String getCopperCladThicknessValue()
    {
        return copperCladThicknessValue;
    }

    public void setCopperCladThicknessValue(String copperCladThicknessValue)
    {
        this.copperCladThicknessValue = copperCladThicknessValue;
    }

    public Long getSurfaceProcess()
    {
        return surfaceProcess;
    }

    public void setSurfaceProcess(Long surfaceProcess)
    {
        this.surfaceProcess = surfaceProcess;
    }

    @ExcelField(title = "镀层处理", align = 2, sort = 150)
    public String getSurfaceProcessValue()
    {
        return surfaceProcessValue;
    }

    public void setSurfaceProcessValue(String surfaceProcessValue)
    {
        this.surfaceProcessValue = surfaceProcessValue;
    }

    public Long getSolderMaskType()
    {
        return solderMaskType;
    }

    public void setSolderMaskType(Long solderMaskType)
    {
        this.solderMaskType = solderMaskType;
    }

    @ExcelField(title = "阻焊类型", align = 2, sort = 160)
    public String getSolderMaskTypeValue()
    {
        return solderMaskTypeValue;
    }

    public void setSolderMaskTypeValue(String solderMaskTypeValue)
    {
        this.solderMaskTypeValue = solderMaskTypeValue;
    }

    public Long getCharacterType()
    {
        return characterType;
    }

    public void setCharacterType(Long characterType)
    {
        this.characterType = characterType;
    }

    @ExcelField(title = "板面字符", align = 2, sort = 170)
    public String getCharacterTypeValue()
    {
        return characterTypeValue;
    }

    public void setCharacterTypeValue(String characterTypeValue)
    {
        this.characterTypeValue = characterTypeValue;
    }

    public Long getShapingWay()
    {
        return shapingWay;
    }

    public void setShapingWay(Long shapingWay)
    {
        this.shapingWay = shapingWay;
    }

    @ExcelField(title = "成型方式", align = 2, sort = 180)
    public String getShapingWayValue()
    {
        return shapingWayValue;
    }

    public void setShapingWayValue(String shapingWayValue)
    {
        this.shapingWayValue = shapingWayValue;
    }

    public Long getTestMethod()
    {
        return testMethod;
    }

    public void setTestMethod(Long testMethod)
    {
        this.testMethod = testMethod;
    }

    @ExcelField(title = "测试要求", align = 2, sort = 190)
    public String getTestMethodValue()
    {
        return testMethodValue;
    }

    public void setTestMethodValue(String testMethodValue)
    {
        this.testMethodValue = testMethodValue;
    }

    public String getCustomerNo()
    {
        return customerNo;
    }

    @ExcelField(title = "终端客户编号", align = 2, sort = 20)
    public String getFinalCustomerNo()
    {
        if (StringUtils.isEmpty(this.getCustomerNo()) && this.getCustomer() != null)
        {
            return this.getCustomer().getName();
        }
        return customerNo;
    }

    public void setCustomerNo(String customerNo)
    {
        this.customerNo = customerNo;
    }

    public Date getSentTimeStartQr()
    {
        return sentTimeStartQr;
    }

    public void setSentTimeStartQr(Date sentTimeStartQr)
    {
        this.sentTimeStartQr = sentTimeStartQr;
    }

    public Date getSentTimeEndQr()
    {
        return sentTimeEndQr;
    }

    public void setSentTimeEndQr(Date sentTimeEndQr)
    {
        this.sentTimeEndQr = sentTimeEndQr;
    }

    public Notification getNotification()
    {
        return notification;
    }

    public void setNotification(Notification notification)
    {
        this.notification = notification;
    }

    public Contract()
    {
        super();
    }

    public Contract(String id)
    {
        super(id);
    }

    public List<ContractDetail> getContractDetails()
    {
        return contractDetails;
    }

    public void setContractDetails(List<ContractDetail> contractDetails)
    {
        this.contractDetails = contractDetails;
    }

    public BigDecimal getMoney()
    {
        return money;
    }

    public void setMoney(BigDecimal money)
    {
        this.money = money;
    }

    public int getNotificationId()
    {
        return notificationId;
    }

    public void setNotificationId(int notificationId)
    {
        this.notificationId = notificationId;
    }

    public Quotation getQuotation()
    {
        return quotation;
    }

    public void setQuotation(Quotation quotation)
    {
        this.quotation = quotation;
    }

    public Price getPrice()
    {
        return price;
    }

    public void setPrice(Price price)
    {
        this.price = price;
    }

    public ContractCraft getContractCraftList()
    {
        return contractCraftList;
    }

    public void setContractCraftList(ContractCraft contractCraftList)
    {
        this.contractCraftList = contractCraftList;
    }

    public String getContractId()
    {
        return contractId;
    }

    public void setContractId(String contractId)
    {
        this.contractId = contractId;
    }

    public ContractDetail getContractDetailList()
    {
        return contractDetailList;
    }

    public void setContractDetailList(ContractDetail contractDetailList)
    {
        this.contractDetailList = contractDetailList;
    }

    public List<ContractAttachements> getContractAttachementsList()
    {
        return contractAttachementsList;
    }

    public void setContractAttachementsList(List<ContractAttachements> contractAttachementsList)
    {
        this.contractAttachementsList = contractAttachementsList;
    }

    public Customer getCustomer()
    {
        return customer;
    }

    public void setCustomer(Customer customer)
    {
        this.customer = customer;
    }

    @ExcelField(title = "合同编号", align = 2, sort = 10)
    public String getNo()
    {
        return no;
    }

    public void setNo(String no)
    {
        this.no = no;
    }

    @ExcelField(title = "客户订单号", align = 2, sort = 40)
    public String getCustomerPo()
    {
        return customerPo;
    }

    public void setCustomerPo(String customerPo)
    {
        this.customerPo = customerPo;
    }

    @ExcelField(title = "下单时间", align = 2, sort = 80)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getOrderDate()
    {
        return orderDate;
    }

    public void setOrderDate(Date orderDate)
    {
        this.orderDate = orderDate;
    }

    @ExcelField(title = "明细备注", align = 2, sort = 410)
    public String getDetailRemark()
    {
        return detailRemark;
    }

    public void setDetailRemark(String detailRemark)
    {
        this.detailRemark = detailRemark;
    }

    public BigDecimal getTotalAmt()
    {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt)
    {
        this.totalAmt = totalAmt;
    }

    public Long getTaxDescript()
    {
        return taxDescript;
    }

    public void setTaxDescript(Long taxDescript)
    {
        this.taxDescript = taxDescript;
    }

    @ExcelField(title = "税类说明", align = 2, sort = 70)
    public String getTaxDescriptValue()
    {
        return taxDescriptValue;
    }

    public void setTaxDescriptValue(String taxDescriptValue)
    {
        this.taxDescriptValue = taxDescriptValue;
    }

    public Long getCurrencyType()
    {
        return currencyType;
    }

    public void setCurrencyType(Long currencyType)
    {
        this.currencyType = currencyType;
    }

    @ExcelField(title = "货币类型", align = 2, sort = 60)
    public String getCurrencyTypeValue()
    {
        return currencyTypeValue;
    }

    public void setCurrencyTypeValue(String currencyTypeValue)
    {
        this.currencyTypeValue = currencyTypeValue;
    }

    public Long getPayWay()
    {
        return payWay;
    }

    public void setPayWay(Long payWay)
    {
        this.payWay = payWay;
    }

    @ExcelField(title = "结算方式", align = 2, sort = 50)
    public String getPayWayValue()
    {
        return payWayValue;
    }

    public void setPayWayValue(String payWayValue)
    {
        this.payWayValue = payWayValue;
    }

    public String getPayDays()
    {
        return payDays;
    }

    public void setPayDays(String payDays)
    {
        this.payDays = payDays;
    }

    public BigDecimal getPrePayAmount()
    {
        return prePayAmount;
    }

    public void setPrePayAmount(BigDecimal prePayAmount)
    {
        this.prePayAmount = prePayAmount;
    }

    public BigDecimal getPrePayBalance()
    {
        return prePayBalance;
    }

    public void setPrePayBalance(BigDecimal prePayBalance)
    {
        this.prePayBalance = prePayBalance;
    }

    public Long getDeliveryWay()
    {
        return deliveryWay;
    }

    public void setDeliveryWay(Long deliveryWay)
    {
        this.deliveryWay = deliveryWay;
    }

    public String getDeliveryPlace()
    {
        return deliveryPlace;
    }

    public void setDeliveryPlace(String deliveryPlace)
    {
        this.deliveryPlace = deliveryPlace;
    }

    public Long getFreightWay()
    {
        return freightWay;
    }

    public void setFreightWay(Long freightWay)
    {
        this.freightWay = freightWay;
    }

    public Long getChkAcptWay()
    {
        return chkAcptWay;
    }

    public void setChkAcptWay(Long chkAcptWay)
    {
        this.chkAcptWay = chkAcptWay;
    }

    public String getChkDissentDays()
    {
        return chkDissentDays;
    }

    public void setChkDissentDays(String chkDissentDays)
    {
        this.chkDissentDays = chkDissentDays;
    }

    public String getQualityStd()
    {
        return qualityStd;
    }

    public void setQualityStd(String qualityStd)
    {
        this.qualityStd = qualityStd;
    }

    public Long getAssureDays()
    {
        return assureDays;
    }

    public void setAssureDays(Long assureDays)
    {
        this.assureDays = assureDays;
    }

    public String getPackagingStd()
    {
        return packagingStd;
    }

    public void setPackagingStd(String packagingStd)
    {
        this.packagingStd = packagingStd;
    }

    @ExcelField(title = "合同状态", align = 2, sort = 1)
    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getCancelReason()
    {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason)
    {
        this.cancelReason = cancelReason;
    }

    public Branch getBranch()
    {
        return branch;
    }

    public void setBranch(Branch branch)
    {
        this.branch = branch;
    }

    public String getClerk()
    {
        return clerk;
    }

    public void setClerk(String clerk)
    {
        this.clerk = clerk;
    }

    public boolean isSelf()
    {
        return self;
    }

    public void setSelf(boolean self)
    {
        this.self = self;
    }

    public User getUser()
    {
        return user;
    }

    public void setUser(User user)
    {
        this.user = user;
    }

    @ExcelField(title = "SET长", align = 2, sort = 200)
    public BigDecimal getPnlWidth()
    {
        return pnlWidth;
    }

    public void setPnlWidth(BigDecimal pnlWidth)
    {
        this.pnlWidth = pnlWidth;
    }

    public Long getLingeSpacing()
    {
        return lingeSpacing;
    }

    @ExcelField(title = "最小线宽/线距", align = 2, sort = 202)
    public String getLingeSpacingValue()
    {
        return lingeSpacingValue;
    }

    public void setLingeSpacingValue(String lingeSpacingValue)
    {
        this.lingeSpacingValue = lingeSpacingValue;
    }

    public Long getSmallAperture()
    {
        return smallAperture;
    }

    @ExcelField(title = "最小孔径", align = 2, sort = 206)
    public String getSmallApertureValue()
    {
        return smallApertureValue;
    }

    public void setSmallApertureValue(String smallApertureValue)
    {
        this.smallApertureValue = smallApertureValue;
    }

    public Long getHalAhole()
    {
        return halAhole;
    }

    @ExcelField(title = "半孔", align = 2, sort = 208)
    public String getHalAholeValue()
    {
        return halAholeValue;
    }

    public void setHalAholeValue(String halAholeValue)
    {
        this.halAholeValue = halAholeValue;
    }

    public Long getBuryBlindHole()
    {
        return buryBlindHole;
    }

    @ExcelField(title = "埋盲孔", align = 2, sort = 212)
    public String getBuryBlindHoleValue()
    {
        return buryBlindHoleValue;
    }

    public void setBuryBlindHoleValue(String buryBlindHoleValue)
    {
        this.buryBlindHoleValue = buryBlindHoleValue;
    }

    @ExcelField(title = "SET宽", align = 2, sort = 204)
    public BigDecimal getPnlLength()
    {
        return pnlLength;
    }

    public void setPnlLength(BigDecimal pnlLength)
    {
        this.pnlLength = pnlLength;
    }

    @ExcelField(title = "拼接数", align = 2, sort = 210)
    public BigDecimal getPnlDivisor()
    {
        return pnlDivisor;
    }

    public void setPnlDivisor(BigDecimal pnlDivisor)
    {
        this.pnlDivisor = pnlDivisor;
    }

    @ExcelField(title = "工程费用", align = 2, sort = 214)
    public BigDecimal getEngineeringFee()
    {
        return engineeringFee;
    }

    public void setEngineeringFee(BigDecimal engineeringFee)
    {
        this.engineeringFee = engineeringFee;
    }

    public Long getResistance()
    {
        return resistance;
    }

    @ExcelField(title = "抗阻", align = 2, sort = 240)
    public String getResistanceValue()
    {
        return resistanceValue;
    }

    public void setResistanceValue(String resistanceValue)
    {
        this.resistanceValue = resistanceValue;
    }

    public Long getDeliveryUrgent()
    {
        return deliveryUrgent;
    }

    @ExcelField(title = "发货加急", align = 2, sort = 250)
    public String getDeliveryUrgentValue()
    {
        return deliveryUrgentValue;
    }

    public void setDeliveryUrgentValue(String deliveryUrgentValue)
    {
        this.deliveryUrgentValue = deliveryUrgentValue;
    }

    public void setLingeSpacing(Long lingeSpacing)
    {
        this.lingeSpacing = lingeSpacing;
    }

    public void setSmallAperture(Long smallAperture)
    {
        this.smallAperture = smallAperture;
    }

    public void setHalAhole(Long halAhole)
    {
        this.halAhole = halAhole;
    }

    public void setBuryBlindHole(Long buryBlindHole)
    {
        this.buryBlindHole = buryBlindHole;
    }

    public void setResistance(Long resistance)
    {
        this.resistance = resistance;
    }

    public void setDeliveryUrgent(Long deliveryUrgent)
    {
        this.deliveryUrgent = deliveryUrgent;
    }

    public SlCustomerOrder getSlCustomerOrder()
    {
        return slCustomerOrder;
    }

    public void setSlCustomerOrder(SlCustomerOrder slCustomerOrder)
    {
        this.slCustomerOrder = slCustomerOrder;
    }

    public String getOrderArea()
    {
        return orderArea;
    }

    public void setOrderArea(String orderArea)
    {
        this.orderArea = orderArea;
    }

    public Boolean getDangerPrice()
    {
        return dangerPrice;
    }

    public void setDangerPrice(Boolean dangerPrice)
    {
        this.dangerPrice = dangerPrice;
    }

    public Long getSignplace()
    {
        return signplace;
    }

    public void setSignplace(Long signplace)
    {
        this.signplace = signplace;
    }

    public Long getDeliverycity()
    {
        return deliverycity;
    }

    public void setDeliverycity(Long deliverycity)
    {
        this.deliverycity = deliverycity;
    }

    public Long getFrightbear()
    {
        return frightbear;
    }

    public void setFrightbear(Long frightbear)
    {
        this.frightbear = frightbear;
    }

    public Long getBillformula()
    {
        return billformula;
    }

    public void setBillformula(Long billformula)
    {
        this.billformula = billformula;
    }

    public Long getHasquestion()
    {
        return hasquestion;
    }

    public void setHasquestion(Long hasquestion)
    {
        this.hasquestion = hasquestion;
    }

    public Long getPaycause()
    {
        return paycause;
    }

    public void setPaycause(Long paycause)
    {
        this.paycause = paycause;
    }

    public Long getInspectionstandard()
    {
        return inspectionstandard;
    }

    public void setInspectionstandard(Long inspectionstandard)
    {
        this.inspectionstandard = inspectionstandard;
    }

    public Long getPackingrequirement()
    {
        return packingrequirement;
    }

    public void setPackingrequirement(Long packingrequirement)
    {
        this.packingrequirement = packingrequirement;
    }

    public String getInventorySwitch()
    {
        return inventorySwitch;
    }

    public void setInventorySwitch(String inventorySwitch)
    {
        this.inventorySwitch = inventorySwitch;
    }

    @ExcelField(title = "业务员", align = 2, sort = 35)
    public String getSalesMan()
    {
        return salesMan;
    }

    public void setSalesMan(String salesMan)
    {
        this.salesMan = salesMan;
    }

    @ExcelField(title = "跟单员", align = 2, sort = 37)
    public String getDocumenter()
    {
        return documenter;
    }

    public void setDocumenter(String documenter)
    {
        this.documenter = documenter;
    }

    public Boolean getNoNotification()
    {
        return noNotification;
    }

    public void setNoNotification(Boolean noNotification)
    {
        this.noNotification = noNotification;
    }

    public String getIcloudId()
    {
        return icloudId;
    }

    public void setIcloudId(String icloudId)
    {
        this.icloudId = icloudId;
    }

    public String getCustomerRemark()
    {
        return customerRemark;
    }

    public void setCustomerRemark(String customerRemark)
    {
        this.customerRemark = customerRemark;
    }

    public String getSupplierRemark()
    {
        return supplierRemark;
    }

    public void setSupplierRemark(String supplierRemark)
    {
        this.supplierRemark = supplierRemark;
    }

    public String getIcloudType()
    {
        return icloudType;
    }

    public void setIcloudType(String icloudType)
    {
        this.icloudType = icloudType;
    }

    // 实体深拷贝
    public Contract clone()
    {
        Contract o = null;
        try
        {
            o = (Contract)super.clone();
        }
        catch (CloneNotSupportedException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } // Object 中的clone()识别出你要复制的是哪一个对象。
        return o;
    }

    public Boolean getSelfPrdorder()
    {
        return selfPrdorder;
    }

    public void setSelfPrdorder(Boolean selfPrdorder)
    {
        this.selfPrdorder = selfPrdorder;
    }

    public String getType()
    {
        return type;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public String getBillingState()
    {
        return billingState;
    }

    public void setBillingState(String billingState)
    {
        this.billingState = billingState;
    }

    public Company getQuiltCombinationCompany()
    {
        return quiltCombinationCompany;
    }

    public void setQuiltCombinationCompany(Company quiltCombinationCompany)
    {
        this.quiltCombinationCompany = quiltCombinationCompany;
    }

    public String getFlag()
    {
        return flag;
    }

    public void setFlag(String flag)
    {
        this.flag = flag;
    }

    @ExcelField(title = "合同明细金额", align = 2, sort = 400)
    public BigDecimal getSubTotal()
    {
        return subTotal;
    }

    public void setSubTotal(BigDecimal subTotal)
    {
        this.subTotal = subTotal;
    }

    public String getDetailId()
    {
        return detailId;
    }

    public void setDetailId(String detailId)
    {
        this.detailId = detailId;
    }

    public String getBatchInfo()
    {
        return batchInfo;
    }

    public void setBatchInfo(String batchInfo)
    {
        this.batchInfo = batchInfo;
    }

    public BigDecimal getOrderAmount()
    {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount)
    {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getOrderAreaSum()
    {
        return orderAreaSum;
    }

    public void setOrderAreaSum(BigDecimal orderAreaSum)
    {
        this.orderAreaSum = orderAreaSum;
    }

    public Integer getOrdersum()
    {
        return ordersum;
    }

    public void setOrdersum(Integer ordersum)
    {
        this.ordersum = ordersum;
    }

    public BigDecimal getPrdPrice()
    {
        return prdPrice;
    }

    public void setPrdPrice(BigDecimal prdPrice)
    {
        this.prdPrice = prdPrice;
    }

    public String getOverDueStr()
    {
        return overDueStr;
    }

    public void setOverDueStr(String overDueStr)
    {
        this.overDueStr = overDueStr;
    }

    public String getFileId()
    {
        return fileId;
    }

    public void setFileId(String fileId)
    {
        this.fileId = fileId;
    }

    public List<ContractAttachMent> getAttachList()
    {
        return attachList;
    }

    public void setAttachList(List<ContractAttachMent> attachList)
    {
        this.attachList = attachList;
    }

    public String getOrderFlag()
    {
        return orderFlag;
    }

    public void setOrderFlag(String orderFlag)
    {
        this.orderFlag = orderFlag;
    }

    public String getCompanyId()
    {
        return companyId;
    }

    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }

    @ExcelField(title = "导热", align = 2, sort = 193)
    public String getDaoreValue()
    {
        return daoreValue;
    }

    public void setDaoreValue(String daoreValue)
    {
        this.daoreValue = daoreValue;
    }

    @ExcelField(title = "沉头孔个数", align = 2, sort = 196)
    public String getCountersinkHole()
    {
        return countersinkHole;
    }

    public void setCountersinkHole(String countersinkHole)
    {
        this.countersinkHole = countersinkHole;
    }

    public String getCustomerPorderNo()
    {
        return customerPorderNo;
    }

    public void setCustomerPorderNo(String customerPorderNo)
    {
        this.customerPorderNo = customerPorderNo;
    }

    public String getCardSet()
    {
        return cardSet;
    }

    public void setCardSet(String cardSet)
    {
        this.cardSet = cardSet;
    }

    @ExcelField(title = "工程交货面积", align = 2, sort = 324)
    public String getCardSetArea()
    {
        return cardSetArea;
    }

    public void setCardSetArea(String cardSetArea)
    {
        this.cardSetArea = cardSetArea;
    }

    @ExcelField(title = "工程交货长", align = 2, sort = 321)
    public BigDecimal getCardPnlLength()
    {
        return cardPnlLength;
    }

    public void setCardPnlLength(BigDecimal cardPnlLength)
    {
        this.cardPnlLength = cardPnlLength;
    }

    @ExcelField(title = "工程交货宽", align = 2, sort = 322)
    public BigDecimal getCardPnlWidth()
    {
        return cardPnlWidth;
    }

    public void setCardPnlWidth(BigDecimal cardPnlWidth)
    {
        this.cardPnlWidth = cardPnlWidth;
    }

    @ExcelField(title = "工程拼接数", align = 2, sort = 323)
    public BigDecimal getCardPnlDivisor()
    {
        return cardPnlDivisor;
    }

    public void setCardPnlDivisor(BigDecimal cardPnlDivisor)
    {
        this.cardPnlDivisor = cardPnlDivisor;
    }

    public Integer getClosingCaseQuery()
    {
        return closingCaseQuery;
    }

    public void setClosingCaseQuery(Integer closingCaseQuery)
    {
        this.closingCaseQuery = closingCaseQuery;
    }

    public String getSaleId()
    {
        return saleId;
    }

    public void setSaleId(String saleId)
    {
        this.saleId = saleId;
    }

    @ExcelField(title = "销售公司", align = 2, sort = 10)
    public String getSaleName()
    {
        return saleName;
    }

    public void setSaleName(String saleName)
    {
        this.saleName = saleName;
    }

    public String getSaleCsutomerId()
    {
        return saleCsutomerId;
    }

    public void setSaleCsutomerId(String saleCsutomerId)
    {
        this.saleCsutomerId = saleCsutomerId;
    }

    public String getBranchQuery()
    {
        return branchQuery;
    }

    public void setBranchQuery(String branchQuery)
    {
        this.branchQuery = branchQuery;
    }

    public String getCustomerId()
    {
        return customerId;
    }

    public void setCustomerId(String customerId)
    {
        this.customerId = customerId;
    }

    public String getBranchId()
    {
        return branchId;
    }

    public void setBranchId(String branchId)
    {
        this.branchId = branchId;
    }

    public String getGroupCenterId()
    {
        return groupCenterId;
    }

    public void setGroupCenterId(String groupCenterId)
    {
        this.groupCenterId = groupCenterId;
    }

    public Integer getGroupCenterCount()
    {
        return groupCenterCount;
    }

    public void setGroupCenterCount(Integer groupCenterCount)
    {
        this.groupCenterCount = groupCenterCount;
    }

    public Date getDeliveryDate()
    {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }

    @ExcelField(title = "交货日期", align = 2, sort = 102)
    public String getDeliveryDateStr()
    {
        if (deliveryDate != null)
        {
            return DateUtils.formatDate(deliveryDate);
        }
        return null;
    }

    @ExcelField(title = "客户编号", align = 2, sort = 30)
    public String getCustomerNoExp()
    {
        if (this.getCustomer() != null)
        {
            return this.getCustomer().getNo();
        }
        return "";
    }

    @ExcelField(title = "客户名称", align = 2, sort = 32)
    public String getCustomerNameExp()
    {
        if (this.getCustomer() != null)
        {
            return this.getCustomer().getName();
        }
        return "";
    }

    public String getCustomerName()
    {
        return customerName;
    }

    public void setCustomerName(String customerName)
    {
        this.customerName = customerName;
    }

    @ExcelField(title = "加工费", align = 2, sort = 331)
    public BigDecimal getProcessFee()
    {
        return processFee;
    }

    public void setProcessFee(BigDecimal processFee)
    {
        this.processFee = processFee;
    }

    @ExcelField(title = "订单类型", align = 2, sort = 101)
    public String getReferenceTypeValue()
    {
        return referenceTypeValue;
    }

    public void setReferenceTypeValue(String referenceTypeValue)
    {
        this.referenceTypeValue = referenceTypeValue;
    }

    public String getSpecialCraftValue()
    {
        return specialCraftValue;
    }

    public void setSpecialCraftValue(String specialCraftValue)
    {
        this.specialCraftValue = specialCraftValue;
    }

    @ExcelField(title = "平米", align = 2, sort = 333)
    public BigDecimal getOrderDeailArea()
    {
        return orderDeailArea;
    }

    public void setOrderDeailArea(BigDecimal orderDeailArea)
    {
        this.orderDeailArea = orderDeailArea;
    }

    @ExcelField(title = "材料费", align = 2, sort = 332)
    public BigDecimal getMaterialFee()
    {
        return materialFee;
    }

    public void setMaterialFee(BigDecimal materialFee)
    {
        this.materialFee = materialFee;
    }

    @ExcelField(title = "成本费", align = 2, sort = 338)
    public BigDecimal getCostFee()
    {
        return costFee;
    }

    public void setCostFee(BigDecimal costFee)
    {
        this.costFee = costFee;
    }

    @ExcelField(title = "净成本", align = 2, sort = 339)
    public BigDecimal getNetCostFee()
    {
        return netCostFee;
    }

    public void setNetCostFee(BigDecimal netCostFee)
    {
        this.netCostFee = netCostFee;
    }

    @ExcelField(title = "管销费用", align = 2, sort = 334)
    public BigDecimal getBarrelBoltFee() {
        return barrelBoltFee;
    }

    public void setBarrelBoltFee(BigDecimal barrelBoltFee) {
        this.barrelBoltFee = barrelBoltFee;
    }

    public CustomerContact getCustomerContact()
    {
        return customerContact;
    }

    public void setCustomerContact(CustomerContact customerContact)
    {
        this.customerContact = customerContact;
    }

    @ExcelField(title = "价格(元/㎡)", align = 2, sort = 334)
    public BigDecimal getSquareMeterPrice()
    {
        return squareMeterPrice;
    }

    public void setSquareMeterPrice(BigDecimal squareMeterPrice)
    {
        this.squareMeterPrice = squareMeterPrice;
    }

    /**
     * 计算价格(元/㎡) SquareMeterPrice = priceOfContract*1000000/pnlLength/pnlWidth*divisor
     */
    public void countSquareMeterPrice()
    {
        if (null != this.pnlLength && null != this.pnlWidth && null != this.pnlDivisor)
        {
            BigDecimal price = (this.priceOfContract == null || this.priceOfContract.trim().length() == 0) ?
                BigDecimal.ZERO :
                new BigDecimal(this.priceOfContract);
            if (this.pnlLength.compareTo(BigDecimal.ZERO) != 0 && pnlWidth.compareTo(BigDecimal.ZERO) != 0
                && this.pnlDivisor.compareTo(BigDecimal.ZERO) != 0)
            {
                this.squareMeterPrice = price.multiply(new BigDecimal(1000000))
                    .divide(this.pnlLength, BigDecimal.ROUND_HALF_UP)
                    .divide(this.pnlWidth, BigDecimal.ROUND_HALF_UP)
                    .multiply(this.pnlDivisor)
                    .setScale(4, BigDecimal.ROUND_HALF_UP);
            }
            else
            {
                this.squareMeterPrice = new BigDecimal(0);
            }
        }
    }

    public Date getOldOrderDate()
    {
        return oldOrderDate;
    }

    public void setOldOrderDate(Date oldOrderDate)
    {
        this.oldOrderDate = oldOrderDate;
    }

    public Integer getRefPreContractId()
    {
        return refPreContractId;
    }

    public void setRefPreContractId(Integer refPreContractId)
    {
        this.refPreContractId = refPreContractId;
    }

    public Integer getSourceType()
    {
        return sourceType;
    }

    public void setSourceType(Integer sourceType)
    {
        this.sourceType = sourceType;
    }

    public String getRefPreContractNo()
    {
        return refPreContractNo;
    }

    public void setRefPreContractNo(String refPreContractNo)
    {
        this.refPreContractNo = refPreContractNo;
    }

    @ExcelField(title = "pcs尺寸", align = 2, sort = 211)
    public String getPcsSize()
    {
        return pcsSize;
    }

    public void setPcsSize(String pcsSize)
    {
        this.pcsSize = pcsSize;
    }

    public Boolean getShowCheckFlag()
    {
        return showCheckFlag;
    }

    public void setShowCheckFlag(Boolean showCheckFlag)
    {
        this.showCheckFlag = showCheckFlag;
    }

    public String getHideCopyDetailFlag()
    {
        return hideCopyDetailFlag;
    }

    public void setHideCopyDetailFlag(String hideCopyDetailFlag)
    {
        this.hideCopyDetailFlag = hideCopyDetailFlag;
    }

    @ExcelField(title = "业务费率%", align = 2, sort = 333)
    public Double getSalePrecent()
    {
        return salePrecent;
    }

    public void setSalePrecent(Double salePrecent)
    {
        this.salePrecent = salePrecent;
    }

    public String getFactoryComId()
    {
        return factoryComId;
    }

    public void setFactoryComId(String factoryComId)
    {
        this.factoryComId = factoryComId;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    @ExcelField(title = "销售部门", align = 2, sort = 38)
    public String getDeptName()
    {
        return deptName;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }

    public String getJxComId()
    {
        return jxComId;
    }

    public void setJxComId(String jxComId)
    {
        this.jxComId = jxComId;
    }

    public String getLnComId()
    {
        return lnComId;
    }

    public void setLnComId(String lnComId)
    {
        this.lnComId = lnComId;
    }

    @ExcelField(title = "终端合同号", align = 2, sort = 405)
    public String getFinalContractNo()
    {
        return finalContractNo;
    }

    public void setFinalContractNo(String finalContractNo)
    {
        this.finalContractNo = finalContractNo;
    }

    public String getEcoemyId()
    {
        return ecoemyId;
    }

    public void setEcoemyId(String ecoemyId)
    {
        this.ecoemyId = ecoemyId;
    }

    public String getSaleComId()
    {
        return saleComId;
    }

    public void setSaleComId(String saleComId)
    {
        this.saleComId = saleComId;
    }

    @ExcelField(title = "曝光/丝印", align = 2, sort = 1000)
    public String getProcessValue()
    {
        return processValue;
    }

    public void setProcessValue(String processValue)
    {
        this.processValue = processValue;
    }

    /*    @ExcelField(title = "加工费计价规则", align = 2, sort = 1010)*/
    public String getQuoteInfo()
    {
        return quoteInfo;
    }

    public void setQuoteInfo(String quoteInfo)
    {
        this.quoteInfo = quoteInfo;
    }

    /*    @ExcelField(title = "材料使用", align = 2, sort = 1020)*/
    public String getMaterialFeeInfo()
    {
        return materialFeeInfo;
    }

    public void setMaterialFeeInfo(String materialFeeInfo)
    {
        this.materialFeeInfo = materialFeeInfo;
    }

    public BigDecimal getPaidAmount()
    {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount)
    {
        this.paidAmount = paidAmount;
    }

    public BigDecimal getNotaxPrdPrice()
    {
        return notaxPrdPrice;
    }

    public void setNotaxPrdPrice(BigDecimal notaxPrdPrice)
    {
        this.notaxPrdPrice = notaxPrdPrice;
    }

    public BigDecimal getSpareRate()
    {
        return spareRate;
    }

    public void setSpareRate(BigDecimal spareRate)
    {
        this.spareRate = spareRate;
    }

    public boolean isSparestatus()
    {
        return sparestatus;
    }

    public void setSparestatus(boolean sparestatus)
    {
        this.sparestatus = sparestatus;
    }

    @ExcelField(title = "业务费", align = 2, sort = 334)
    public BigDecimal getSaleFee()
    {
        return saleFee;
    }

    public void setSaleFee(BigDecimal saleFee)
    {
        this.saleFee = saleFee;
    }

    @ExcelField(title = "管理费", align = 2, sort = 334)
    public BigDecimal getManageFee()
    {
        return manageFee;
    }

    public void setManageFee(BigDecimal manageFee)
    {
        this.manageFee = manageFee;
    }

    @ExcelField(title = "备品数量", align = 2, sort = 343)
    public BigDecimal getSpareQuantity()
    {
        return spareQuantity;
    }

    public void setSpareQuantity(BigDecimal spareQuantity)
    {
        this.spareQuantity = spareQuantity;
    }

    @ExcelField(title = "工程材料费", align = 2, sort = 1030)
    public String getForecastMaterialFee()
    {
        return forecastMaterialFee;
    }

    public void setForecastMaterialFee(String forecastMaterialFee)
    {
        this.forecastMaterialFee = forecastMaterialFee;
    }

    public Date getForecastEstimateDate()
    {
        return forecastEstimateDate;
    }

    public void setForecastEstimateDate(Date forecastEstimateDate)
    {
        this.forecastEstimateDate = forecastEstimateDate;
    }

    @ExcelField(title = "工程交期", align = 2, sort = 1040)
    public String getForecastEstimateDateStr()
    {
        if (null != forecastEstimateDate)
        {
            return DateUtils.formatDate(forecastEstimateDate, "yyyy-MM-dd");
        }
        return null;
    }

    @ExcelField(title = "工程利用率", align = 2, sort = 1050)
    public String getForecastUseRatio()
    {
        return forecastUseRatio;
    }

    public void setForecastUseRatio(String forecastUseRatio)
    {
        this.forecastUseRatio = forecastUseRatio;
    }

    @ExcelField(title = "工程模具费", align = 2, sort = 1060)
    public BigDecimal getCardMouldFee()
    {
        return cardMouldFee;
    }

    public void setCardMouldFee(BigDecimal cardMouldFee)
    {
        this.cardMouldFee = cardMouldFee;
    }

    @ExcelField(title = "工程测试架费", align = 2, sort = 1070)
    public BigDecimal getCardTestShelfFee()
    {
        return cardTestShelfFee;
    }

    public void setCardTestShelfFee(BigDecimal cardTestShelfFee)
    {
        this.cardTestShelfFee = cardTestShelfFee;
    }

    public String getMsg()
    {
        return msg;
    }

    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    public String getAppId()
    {
        return appId;
    }

    public void setAppId(String appId)
    {
        this.appId = appId;
    }

    public String getLinkName()
    {
        return linkName;
    }

    public void setLinkName(String linkName)
    {
        this.linkName = linkName;
    }

    public String getLinkPhone()
    {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone)
    {
        this.linkPhone = linkPhone;
    }

    public String getExchangeType()
    {
        return exchangeType;
    }

    public void setExchangeType(String exchangeType)
    {
        this.exchangeType = exchangeType;
    }

    public Date getExchangeDate()
    {
        return exchangeDate;
    }

    public void setExchangeDate(Date exchangeDate)
    {
        this.exchangeDate = exchangeDate;
    }

    public String getExchangeDateStr()
    {
        if(null != exchangeDate)
        {
            return DateUtils.formatDate(exchangeDate,"yyyy-MM-dd");
        }
        return null;
    }

    public String getContractDetailsId() {
        return contractDetailsId;
    }

    public void setContractDetailsId(String contractDetailsId) {
        this.contractDetailsId = contractDetailsId;
    }

    public Integer getYear()
    {
        return year;
    }

    public void setYear(Integer year)
    {
        this.year = year;
    }

    public Integer getMonth()
    {
        return month;
    }

    public void setMonth(Integer month)
    {
        this.month = month;
    }

    public String getCheckDate()
    {
        return checkDate;
    }

    public void setCheckDate(String checkDate)
    {
        this.checkDate = checkDate;
    }

    @ExcelField(title = "订单单位", align = 2, sort = 1080)
    public String getUnitType()
    {
        if ("1".equals(unitType))
        {
            return "SET";
        }
        else
        {
            return "PCS";
        }
    }

    public void setUnitType(String unitType)
    {
        this.unitType = unitType;
    }

    public String getOldStatusInit()
    {
        return oldStatusInit;
    }

    public void setOldStatusInit(String oldStatusInit)
    {
        this.oldStatusInit = oldStatusInit;
    }

    public String getDetailStatus()
    {
        return detailStatus;
    }

    public void setDetailStatus(String detailStatus)
    {
        this.detailStatus = detailStatus;
    }

    @ExcelField(title = "逾期状态", align = 2, sort = 1090)
    public String getShowOverdueStatus()
    {
        String result = null;
        if(StringUtils.isBlank(oldStatusInit))
        {
            result = "正常";
        }
        else
        {
            if(!TypeKey.SL_CONTRACT_STATUS_CHECKED.toString().equals(oldStatusInit))
            {
                result = "关闭";
            }
        }
        if(StringUtils.isBlank(result))
        {
            result = "正常";
        }
        return result;
    }

    @ExcelField(title = "生产状态", align = 2, sort = 1100)
    public String getShowProductionStatus()
    {
        return showProductionStatus;
    }

    public void setShowProductionStatus(String showProductionStatus)
    {
        this.showProductionStatus = showProductionStatus;
    }

    public String getOverdueStatus()
    {
        return overdueStatus;
    }

    public void setOverdueStatus(String overdueStatus)
    {
        this.overdueStatus = overdueStatus;
    }

    public String getProductionStatus()
    {
        return productionStatus;
    }

    public void setProductionStatus(String productionStatus)
    {
        this.productionStatus = productionStatus;
    }

    public String getCustomerGrade()
    {
        return customerGrade;
    }

    public void setCustomerGrade(String customerGrade)
    {
        this.customerGrade = customerGrade;
    }

    public String getShowStatus()
    {
        String result = null;
        if(StringUtils.isBlank(status))
        {
            return result;
        }
        switch (status)
        {
            case "200201":
                result = "未确认";
                break;
            case "200204":
                result = "送货完成";
                break;
            case "200205":
                result = "已作废";
                break;
            case "200207":
                result = "已审核";
                break;
            case "200209":
                result = "审批中";
                break;
            case "200210":
                result = "对账关闭";
                break;
        }
        return result;
    }

    public String getInterCustomRecordId() {
        return interCustomRecordId;
    }

    public void setInterCustomRecordId(String interCustomRecordId) {
        this.interCustomRecordId = interCustomRecordId;
    }
}