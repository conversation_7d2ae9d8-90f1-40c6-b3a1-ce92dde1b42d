package com.kyb.pcberp.modules.hr.payment_center.controller;

import com.kyb.pcberp.modules.hr.payment_center.pojo.Hr_ClockIn;
import com.kyb.pcberp.modules.hr.payment_center.service.Hr_ClockInService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

// 打卡
@Controller
@RequestMapping(value = "${hrPath}/clockIn")
public class Hr_ClockInController {
    @Autowired
    private Hr_ClockInService hitCardService;

    @RequestMapping(value = {"getHitCard"})
    @ResponseBody
    public List<Hr_ClockIn> getHitCard(@RequestBody Hr_ClockIn hrClockIn) {
        return hitCardService.getHitCard(hrClockIn);
    }

    @RequestMapping(value = {"delHitCard"})
    @ResponseBody
    public String delHitCard(@RequestBody Hr_ClockIn hrClockIn) {
        return hitCardService.delHitCard(hrClockIn);
    }

    @RequestMapping(value = {"insertUpdHitCard"})
    @ResponseBody
    public String insertUpdHitCard(@RequestBody Hr_ClockIn hrClockIn) {
        return hitCardService.insertUpdHitCard(hrClockIn);
    }

    @RequestMapping(value = {"getOrganizationList"})
    @ResponseBody
    public List<Hr_ClockIn> getOrganizationList(@RequestBody Hr_ClockIn hrClockIn) {
        return hitCardService.getOrganizationList(hrClockIn);
    }
}
