/**
 * 
 */
package com.kyb.pcberp.modules.finance.dao;


import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.finance.entity.TrendPayment;

/**
 * 付款走势DAO接口
 * <AUTHOR>
 * @version 2015-11-03
 */
@MyBatisDao
public interface TrendPaymentDao extends CrudDao<TrendPayment> {
	
	/**
	 * 统计是否是本田第一条数据
	 * @param date
	 * @return
	 */
	public TrendPayment  selectOfFirstdate(TrendPayment trendPayment);
	
	/**
	 * 总览查询付款总数
	 * @param trendPayment
	 * @return
	 */
	public  TrendPayment selectPayMoneySum(TrendPayment trendPayment);
	
}