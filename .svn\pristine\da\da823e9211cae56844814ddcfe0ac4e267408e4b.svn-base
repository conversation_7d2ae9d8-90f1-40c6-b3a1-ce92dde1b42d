const produce = {
	template:'#produce',
	created () {
		this.$store.dispatch('myStore/setUserInformation')
		var pro = JSON.parse(window.localStorage.getItem('produceDeail'))
    	this.userId = pro.userId
		if (pro.company && pro.company.multipleCount)
		{
			this.multipleCount = pro.company.multipleCount;
		}
	},
	computed: {
		produceDeail:{
			get () {
				return this.$store.state.produceStore.produceDeail
			}
		},
		followUpProcessList:{
			get () {
				return this.$store.state.produceStore.followUpProcessList
			}
		},
		produceUsers:{
			get () {
				return this.$store.state.produceStore.produceUsers
			}
		},
		userInformation:{
			get () {
				return this.$store.state.myStore.userInformation
		    }
		},
		message: {
			get () {
				return this.$store.state.produceStore.message
		    }
		}
	},
	data(){
		return{
			userId: "",
			processId: "",
			takePcsA: null,
			takePcsB: null,
			handPcsA: null,
			handPcsB: null,
			clickFlag:false,
			multipleCount:"",
		}
	},
	watch:{
		userInformation:function(){
			$('#loadingModal').modal()
			this.initData()
		},
		produceDeail:function(){
			if(this.produceDeail && this.produceDeail.recordId){
				if(this.produceDeail && this.produceDeail.handOverFlag == '1'){
					this.takePcsA = this.produceDeail.takeOverQtyPcsA?this.produceDeail.takeOverQtyPcsA:0;
					this.takePcsB = this.produceDeail.takeOverQtyPcsB?this.produceDeail.takeOverQtyPcsB:0;
				}
				if(this.produceDeail && this.produceDeail.handOverFlag == '2'){
					let handPcsAall = this.produceDeail.takeCheckNumA?this.produceDeail.takeCheckNumA:0;
					let handPcsBall = this.produceDeail.takeCheckNumB?this.produceDeail.takeCheckNumB:0;
					
					let handPcsAread = this.produceDeail.handOverQtyPcsA?this.produceDeail.handOverQtyPcsA:0;
					let handPcsBread = this.produceDeail.handOverQtyPcsB?this.produceDeail.handOverQtyPcsB:0;
					
					this.handPcsA = Number(handPcsAall) - Number(handPcsAread);
					this.handPcsB = Number(handPcsBall) - Number(handPcsBread);
				}
				this.$store.dispatch('produceStore/setProduceUsers', JSON.stringify(this.produceDeail))
				if(this.produceDeail && this.produceDeail.skipCount == 1){
					this.$store.dispatch('produceStore/setFollowUpProcess', JSON.stringify(this.produceDeail))
				}
			}
		},
		produceUsers:function(){
			$('#loadingModal').modal('hide')
		},
		message: function() {
			$('#static').modal()
			var _this = this
			setTimeout(function(){
				$('#static').modal('hide')
				_this.$router.push('/')
				_this.$store.dispatch('produceStore/clearMessage')
			},2000);
		}
	},
	methods:{
		initData:function(){
			if(window.localStorage.getItem('produceDeail')){
				this.$store.dispatch('produceStore/setProduceDeail')
	    	}else{
	    		if(this.temp > 50){
					alert("系统错误，请刷新重试");
					this.temp = 0;
	    		}
	    		this.temp ++ ;
	    		//递归 等待dom渲染完毕
	    		var _this = this;
	    		setTimeout(function(){_this.initData()},500);
	    	}
		},
		inInformation:function(item){
			window.localStorage.setItem('carda', JSON.stringify(item.cardA));
			this.$router.push('/workDeail/information')
		},
		hand:function(){
			if(this.clickFlag)
			{
				alert("请勿多次点击！");
				return;
			}
			this.produceDeail.takePcsA = this.takePcsA;
			this.produceDeail.takePcsB = this.takePcsB;
			this.produceDeail.handPcsA = this.handPcsA;
			this.produceDeail.handPcsB = this.handPcsB;
			let handPcsA = this.produceDeail.handPcsA?this.produceDeail.handPcsA:0;
            let handPcsB = this.produceDeail.handPcsB?this.produceDeail.handPcsB:0;
            let handPcsT = Number(handPcsA) + Number(handPcsB);
            if (handPcsA < 0){
            	alert("A交板数必须大于等于0");
                return;
            }
            if (handPcsB < 0){
            	alert("B交板数必须大于等于0");
                return;
            }
            if (handPcsT <= 0 && !this.produceDeail.complaintId){
            	alert("交板总数必须大于0才能交板");
                return;
            }
            if(this.produceDeail.complaintId){
            	this.produceDeail.handOverQtyPcsA = this.produceDeail.takeOverQtyPcsA;
    			this.produceDeail.handOverQtyPcsB = this.produceDeail.takeOverQtyPcsB;
    			this.produceDeail.handOverQtyPcsT = this.produceDeail.takeOverQtyPcsT;
            }
			
			this.produceDeail.lastUpdBy =　{}
			this.produceDeail.lastUpdBy.recordId = this.userId
			this.produceDeail.operType = 1;
			this.clickFlag = true;
			var this_js = this;
			$.ajax({
				type:"post",
			    url:ctx + "/f/wechat/produce/add",
			    data:JSON.stringify(this.produceDeail),
			    contentType:"application/json",
			    success:function(data)
			    {
			    	if(data.result)
			    	{
			    		this_js.$router.push('/')
			    		alert(data.message)
			    	}
			    	else
			    	{
			    		alert(data.message)
			    	}
					this_js.clickFlag = false;
			    }
			})
		},
		take:function(){
			if(this.clickFlag)
			{
				alert("请勿多次点击！");
				return;
			}
			this.produceDeail.takePcsA = this.takePcsA;
			this.produceDeail.takePcsB = this.takePcsB;
			this.produceDeail.handPcsA = this.handPcsA;
			this.produceDeail.handPcsB = this.handPcsB;
			this.produceDeail.createdBy =　{}
			this.produceDeail.createdBy.recordId = this.userId
			this.produceDeail.operType = 0
			this.produceDeail.operator = this.userId
			this.clickFlag = true;
			var this_js = this;
			$.ajax({
				type:"post",
			    url:ctx + "/f/wechat/produce/add",
			    data:JSON.stringify(this.produceDeail),
			    contentType:"application/json",
			    success:function(data)
			    {
			    	if(data.resultThree == "fail"){
						alert(data.messageThree);
					}
			    	if(data.result)
			    	{
			    		this_js.$store.dispatch('produceStore/setProduceDeail')
						if (data.messageTwo){
							alert(data.message + data.messageTwo);
						}else{
							alert(data.message)
						}
			    		this_js.$router.push('/')
			    	}
			    	else
			    	{
			    		alert(data.message)
			    	}
					this_js.clickFlag = false;
			    }
			})
		},
		takeHand:function(){
			this.produceDeail.handOverQtyPcsA = this.produceDeail.takeOverQtyPcsA;
			this.produceDeail.handOverQtyPcsB = this.produceDeail.takeOverQtyPcsB;
			this.produceDeail.handOverQtyPcsT = this.produceDeail.takeOverQtyPcsT;
			this.produceDeail.createdBy =　{}
			this.produceDeail.createdBy.recordId = this.userId
			this.produceDeail.lastUpdBy =　this.produceDeail.createdBy
			this.produceDeail.operType = 2
			this.produceDeail.operator = this.userId
			var this_js = this;
			$.ajax({
				type:"post",
			    url:ctx + "/f/wechat/produce/add",
			    data:JSON.stringify(this.produceDeail),
			    contentType:"application/json",
			    success:function(data)
			    {
			    	if(data.result)
			    	{
			    		this_js.$router.push('/')
			    		alert(data.message)
			    	}
			    	else
			    	{
			    		alert(data.message)
			    	}
			    }
			})
		},
		skipCount:function(){
			this.produceDeail.handOverQtyPcsA = this.produceDeail.takeOverQtyPcsA;
			this.produceDeail.handOverQtyPcsB = this.produceDeail.takeOverQtyPcsB;
			this.produceDeail.handOverQtyPcsT = this.produceDeail.takeOverQtyPcsT;
			this.produceDeail.createdBy =　{}
			this.produceDeail.createdBy.recordId = this.userId
			this.produceDeail.lastUpdBy =　this.produceDeail.createdBy
			this.produceDeail.endProcessId = this.processId
			this.produceDeail.operator = this.userId
			var this_js = this;
			$.ajax({
				type:"post",
			    url:ctx + "/f/wechat/produce/saveSkipCount",
			    data:JSON.stringify(this.produceDeail),
			    contentType:"application/json",
			    success:function(data)
			    {
			    	if(data.result)
			    	{
			    		this_js.$router.push('/')
			    		alert(data.message)
			    	}
			    	else
			    	{
			    		alert(data.message)
			    	}
			    }
			})
		},
		checkHandNum:function(num){
	        if(num == 1)
	        {
	            if(!this.handPcsA)
	            {
	                this.handPcsA = null;
	                alert("交板A板数不能为空");
	                return;
	            }
	            if(isNaN(this.handPcsA))
	            {
	                this.handPcsA = null;
	                alert("交板A板数必须为数字");
	                return;
	            }
	            if(this.handPcsA < 0)
	            {
	                this.handPcsA = null;
	                alert("交板A板数必须大于等于0");
	                return;
	            }
	            if(this.handPcsA%1 !== 0 || this.handPcsA.indexOf(".")!=-1)
	            {
	                this.handPcsA = null;
	                alert("交板A板数必须是整数");
	                return;
	            }
	        }
	        // 接板B板检数
	        else if(num == 2)
	        {
	            if(!this.handPcsB)
	            {
	                this.handPcsB = null;
	                alert("交板B板数不能为空");
	                return;
	            }
	            if(isNaN(this.handPcsB))
	            {
	                this.handPcsB = null;
	                alert("交板B板数必须为数字");
	                return;
	            }
	            if(this.handPcsB < 0)
	            {
	                this.handPcsB = null;
	                alert("交板B板数必须大于等于0");
	                return;
	            }
	            if(this.handPcsB%1 !== 0 || this.handPcsB.indexOf(".")!=-1)
	            {
	                this.handPcsB = null;
	                alert("交板B板数必须是整数");
	                return;
	            }
	        }
	    },
		checkTakeNum:function(num){
	        if(num == 1)
	        {
	            if(!this.takePcsA)
	            {
	            	this.takePcsA = null;
	                alert("接板A板检数不能为空");
	                return;
	            }
	            if(isNaN(this.takePcsA))
	            {
	            	this.takePcsA = null;
	                alert("接板A板检数必须为数字");
	                return;
	            }
	            if(this.takePcsA < 0)
	            {
	            	this.takePcsA = null;
	                alert("接板A板检数必须大于等于0");
	                return;
	            }
	            if(this.takePcsA%1 !== 0 || this.takePcsA.indexOf(".")!=-1)
	            {
	            	this.takePcsA = null;
	                alert("接板A板检数必须是整数");
	                return;
	            }
	        }
	        // 接板B板检数
	        else if(num == 2)
	        {
	            if(!this.takePcsB)
	            {
	            	this.takePcsB = null;
	                alert("接板B板检数不能为空");
	                return;
	            }
	            if(isNaN(this.takePcsB))
	            {
	            	this.takePcsB = null;
	                alert("接板B板检数必须为数字");
	                return;
	            }
	            if(this.takePcsB < 0)
	            {
	            	this.takePcsB = null;
	                alert("接板B板检数必须大于等于0");
	                return;
	            }
	            if(this.takePcsB%1 !== 0 || this.takePcsB.indexOf(".")!=-1)
	            {
	            	this.takePcsB = null;
	                alert("接板B板检数必须是整数");
	                return;
	            }
	        }
	    },
		getQualityReasons:function ()
		{
			$('#qualityReasons').modal('show');
		}
	}
}