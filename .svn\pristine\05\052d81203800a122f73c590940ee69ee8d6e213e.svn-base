/**
 *
 */
package com.kyb.pcberp.modules.contract.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.itextpdf.text.Document;
import com.itextpdf.text.Element;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.kyb.pcberp.common.config.ConstKey;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.common.enums.CommonEnums;
import com.kyb.pcberp.common.enums.CommonEnums.DictItemEnum;
import com.kyb.pcberp.common.oss.util.FileManageUtils;
import com.kyb.pcberp.common.pdf.ItextPdfHeaderFooter;
import com.kyb.pcberp.common.pdf.ext.ParagraphCn12;
import com.kyb.pcberp.common.pdf.ext.ParagraphCn16;
import com.kyb.pcberp.common.pdf.ext.ParagraphCn9;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.service.CrudService;
import com.kyb.pcberp.common.utils.*;
import com.kyb.pcberp.common.utils.excel.ExportExcel;
import com.kyb.pcberp.modules.approval.entity.Backups;
import com.kyb.pcberp.modules.approval.utils.ModifyUtils;
import com.kyb.pcberp.modules.approval.vo.ContractDeailVo;
import com.kyb.pcberp.modules.approval.vo.ContractVo;
import com.kyb.pcberp.modules.contract.dao.*;
import com.kyb.pcberp.modules.contract.entity.*;
import com.kyb.pcberp.modules.contract.utils.ChangeDataUtils;
import com.kyb.pcberp.modules.contract.utils.QuotationSetUpUtils;
import com.kyb.pcberp.modules.contract.vo.OrderProcedureVo;
import com.kyb.pcberp.modules.crm.dao.CustomerDao;
import com.kyb.pcberp.modules.crm.entity.Customer;
import com.kyb.pcberp.modules.crm.entity.GoodsCheck;
import com.kyb.pcberp.modules.eg.dao.*;
import com.kyb.pcberp.modules.eg.entity.*;
import com.kyb.pcberp.modules.finance.dao.ReconciliationDao;
import com.kyb.pcberp.modules.finance.entity.Reconciliation;
import com.kyb.pcberp.modules.production.dao.*;
import com.kyb.pcberp.modules.production.entity.*;
import com.kyb.pcberp.modules.purch.dao.PrdorderDetailDao;
import com.kyb.pcberp.modules.purch.dao.PurchRawDao;
import com.kyb.pcberp.modules.purch.dao.PurchasingDetailDao;
import com.kyb.pcberp.modules.purch.entity.PurchRaw;
import com.kyb.pcberp.modules.purch.entity.PurchasingDetail;
import com.kyb.pcberp.modules.report.entity.CapacityDeail;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.dao.ProductStoreDao;
import com.kyb.pcberp.modules.stock.dao.StoreHouseDao;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.MaterialPlaceCom;
import com.kyb.pcberp.modules.stock.entity.StoreHouse;
import com.kyb.pcberp.modules.sys.dao.CompanyDao;
import com.kyb.pcberp.modules.sys.dao.DictValueDao;
import com.kyb.pcberp.modules.sys.dao.ParameterSetDao;
import com.kyb.pcberp.modules.sys.entity.*;
import com.kyb.pcberp.modules.sys.utils.DictUtils;
import com.kyb.pcberp.modules.sys.utils.UserUtils;
import com.kyb.pcberp.modules.sys.vo.CapacityVo;
import com.kyb.pcberp.modules.wechat.pojo.wechatUser.WechatAudit;
import org.apache.poi.ss.usermodel.Row;
import org.apache.shiro.util.CollectionUtils;
import org.aspectj.weaver.ast.Not;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生产通知单Service
 *
 * <AUTHOR>
 * @version 2015-09-11
 */
@Service
@Transactional(readOnly = false)
public class NotificationService extends CrudService<NotificationDao, Notification> {

    @Autowired
    private NotificationDao notificationDao;

    @Autowired
    private ContractDetailDao contractDetailDao;

    @Autowired
    private ContractCraftDao contractCraftDao;

    @Autowired
    private FeedingDao feedingDao;

    @Autowired
    private MaterialDao materialDao;

    @Autowired
    private ContractDao contractDao;

    @Autowired
    private CardADao cardADao;

    @Autowired
    private CardBDao cardBDao;

    @Autowired
    private CardAProcessValueDao processValueDao;

    @Autowired
    private BoardPartDao boardPartDao;

    @Autowired
    private BoardCuttingDao boardCuttingDao;

    @Autowired
    private DrillInfoDao drillInfoDao;

    @Autowired
    private CustomerDao customerDao;

    @SuppressWarnings("unused")
    @Autowired
    private ProduceBatchDao produceBatchDao;

    @Autowired
    private ProduceBatchDetailDao produceBatchDetailDao;

    @Autowired
    private ParameterSetDao parameterSetDao;

    @Autowired
    private ProductStoreDao productStoreDao;

    @Autowired
    private StoreHouseDao storeHouseDao;

    @Autowired
    private OrderProcedureVoDao orderProcedureVoDao;

    @Autowired
    private NotificationSchedulingDao notificationSchedulingDao;

    @Autowired
    private NotiSchedulRecordDao notiSchedulRecordDao;

    @Autowired
    private PurchRawDao purchRawDao;

    @Autowired
    private PurchasingDetailDao purchasingDetailDao;

    @Autowired
    private ErpCraftDao erpCraftDao;

    @Autowired
    private PrdorderDetailDao prdorderDetailDao;

    @Autowired
    CompanyDao companyDao;

    @Autowired
    private ReconciliationDao reconciliationDao;

    @Autowired
    private CapacityDao capacityDao;

    @Autowired
    private CapacityRecordDao capacityRecordDao;

    @Autowired
    private MaxBatchAreaConfirmDao maxBatchAreaConfirmDao;

    @Autowired
    private BottleneckProcessUseDao bottleneckProcessUseDao;

    @Autowired
    private DictValueDao dictValueDao;

    public Notification get(String id) {
        return super.get(id);
    }

    public List<Notification> findList(Notification notification) {
        return super.findList(notification);
    }

    public Page<Notification> findPage(Page<Notification> page, Notification notification) {
        // 通知单编号
        if (StringUtils.isNotBlank(notification.getNo())) {
            notification.setNo(notification.getNo().replace(" ", ""));
        }
        // 生产编号
        if (StringUtils.isNotBlank(notification.getCraftNo())) {
            notification.setCraftNo(notification.getCraftNo().replace(" ", ""));
        }
        // 合同编号
        if (StringUtils.isNotBlank(notification.getContractNo())) {
            notification.setContractNo(notification.getContractNo().replace(" ", ""));
        }
        // 客户型号
        if (StringUtils.isNotBlank(notification.getCustomerModel())) {
            notification.setCustomerModel(notification.getCustomerModel().replace(" ", ""));
        }
        Company company = UserUtils.getUser().getCompany();
        notification.setMaterialType(TypeKey.SL_NOTIFICATION_MERGE_NEW.longValue());

        notification.setPage(page);
        notification.setFactoryComId(CompanyUtil.getInstance().getFactId());
        List<Notification> notificationList = new ArrayList<Notification>();
        if (UserUtils.getUser().getCompany().getRecordId().equals(CompanyUtil.getInstance().getFactId())) {
            notificationList = dao.findList(notification);
        } else {
            notificationList = dao.findSaleList(notification);
        }
        // 处理合同明细状态
        if (!CollectionUtils.isEmpty(notificationList)) {
            String notiIds = null;
            String contractDetailIds = null;
            for (Notification nf : notificationList) {
                if (StringUtils.isNotBlank(nf.getFactNotiId())) {
                    if (StringUtils.isNotBlank(notiIds)) {
                        notiIds = notiIds + "," + nf.getFactNotiId();
                    } else {
                        notiIds = nf.getFactNotiId();
                    }
                }
                if(StringUtils.isNotBlank(nf.getContractDetailId()))
                {
                    if (StringUtils.isNotBlank(contractDetailIds)) {
                        contractDetailIds = contractDetailIds + "," + nf.getContractDetailId();
                    } else {
                        contractDetailIds = nf.getContractDetailId();
                    }
                }

                BigDecimal netCostFee = BigDecimal.ZERO;
                if(null == nf.getCostFee())
                {
                    nf.setCostFee(BigDecimal.ZERO);
                }
                netCostFee = nf.getCostFee();
                nf.setNetCostFee(netCostFee.toString());

                BigDecimal notificationTotalAmt = StringUtils.isBlank(nf.getNotificationTotalAmt()) ? BigDecimal.ZERO : new BigDecimal(nf.getNotificationTotalAmt());
                BigDecimal computRate = notificationTotalAmt;
                if (computRate.compareTo(BigDecimal.ZERO) <= 0) {
                    computRate = BigDecimal.ONE;
                }
                BigDecimal netGrossProfitMargin = (notificationTotalAmt.subtract(netCostFee)).multiply(new BigDecimal(100)).divide(computRate,4, BigDecimal.ROUND_HALF_UP);
                if(null != netGrossProfitMargin)
                {
                    nf.setNetGrossProfitMargin(netGrossProfitMargin.toString());
                }
            }
            List<Notification> cardDataList = notificationDao.getCardData(notification.getFactoryComId(), notiIds);

            // 获取通知单最大瓶颈工序日期列表
            List<BottleneckProcessUse> maxDateList = bottleneckProcessUseDao.getNotiMaxDateList(notification.getFactoryComId());

            // 获取管理费比例
            ParameterSet ps = new ParameterSet();
            ps.setCompany(company);
            ps.setJianPin("managePrecent");
            Double managePrecent = 0.0;
            ParameterSet parameterSet = parameterSetDao.getParameterSetByCompanyAndjianPin(ps);
            if (null != parameterSet && StringUtils.isNotBlank(parameterSet.getParameterValue())) {
                managePrecent = Double.valueOf(parameterSet.getParameterValue());
            }

            for (Notification nf : notificationList) {
                if (Collections3.isNotEmpty(cardDataList) && StringUtils.isNotBlank(nf.getFactNotiId())) {
                    for (Notification cardData : cardDataList) {
                        if (cardData.getRecordId().equals(nf.getFactNotiId())) {
                            if (StringUtils.isBlank(nf.getSetSize())) {
                                nf.setSetSize(cardData.getSetSize());
                            }
                            if (StringUtils.isNotBlank(cardData.getProcessCraftId())) {
                                switch (cardData.getProcessCraftId()) {
                                    // 测试方式
                                    case "72":
                                        nf.setProcessCraftValueOne(cardData.getProcessCraftValue());
                                        break;
                                    // 油墨种类
                                    case "195":
                                        nf.setProcessCraftValueTwo(cardData.getProcessCraftValue());
                                        break;
                                    // 啤(锣)方式
                                    case "61":
                                        nf.setProcessCraftValueThree(cardData.getProcessCraftValue());
                                        break;
                                }
                            }
                            break;
                        }
                    }
                }
                if(Collections3.isNotEmpty(maxDateList))
                {
                    for(BottleneckProcessUse maxDate : maxDateList)
                    {
                        if(nf.getRecordId().equals(maxDate.getNoticationId()))
                        {
                            nf.setProductionDeliveryDate(DateUtils.formatDate(maxDate.getOccurrenceDate()));
                            break;
                        }
                    }
                }

                int outStore = nf.getOutStoreQty() == null ? 0 : nf.getOutStoreQty();
                int inStore = nf.getInStoreQty() == null ? 0 : nf.getInStoreQty();
                int orderQty =
                        (nf.getContractDetail() == null || StringUtils.isEmpty(nf.getContractDetail().getQuantity())) ? 0
                                : Integer.parseInt(nf.getContractDetail().getQuantity());
                if (nf.getContractDetail() != null
                        && TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()
                        .equals(nf.getContractDetail().getStatus())
                        || TypeKey.SL_CONTRACT_STATUS_CHECKED.toString().equals(nf.getContractDetail().getStatus())
                        || outStore >= orderQty) {
                    nf.setContractDetailStatus("已送货");
                } else if (outStore > 0) {
                    nf.setContractDetailStatus("送货中");
                } else if (inStore >= orderQty) {
                    nf.setContractDetailStatus("已入库");
                } else if (!StringUtils.isEmpty(nf.getAcDistributeDateStr())) {
                    nf.setContractDetailStatus("已投料");
                } else {
                    nf.setContractDetailStatus("待投料");
                }
                // nf.setWechatAudit(wa);
            }
        }
        page.setList(notificationList);
        return page;
    }

    @Transactional(readOnly = false)
    public synchronized Map<String, String> saveNotification(Notification notification) {
        Map<String, String> result = Maps.newHashMap();

        Company company = UserUtils.getUser().getCompany();

        String remark = "";
        notification.setCompany(company);
        notification.setStatus(TypeKey.SL_NOTIFICATION_STATUS_DRAFT.toString());
        if (notification.getUrgentFlag() != null && notification.getUrgentFlag().equals("true")) {
            // 加急
            notification.setUrgentFlag(TypeKey.ACTIVE.toString());
        } else {
            notification.setUrgentFlag(TypeKey.INACTIVE.toString());
        }

        if (notification.getPdtReportFlag() != null && notification.getPdtReportFlag().equals("true")) {
            // 要求有成品出货报告
            notification.setPdtReportFlag(TypeKey.ACTIVE.toString());
        } else {
            notification.setPdtReportFlag(TypeKey.INACTIVE.toString());
        }

        if (notification.getSmpCfmFlag() != null && notification.getSmpCfmFlag().equals("true")) {
            // 样品确认书
            notification.setSmpCfmFlag(TypeKey.ACTIVE.toString());
        } else {
            notification.setSmpCfmFlag(TypeKey.INACTIVE.toString());
        }

        if (notification.getMetalAnalyzeFlag() != null && notification.getMetalAnalyzeFlag().equals("true")) {
            // 要求有金相切片分析书
            notification.setMetalAnalyzeFlag(TypeKey.ACTIVE.toString());
        } else {
            notification.setMetalAnalyzeFlag(TypeKey.INACTIVE.toString());
        }

        if (notification.getOtherReport() != null && notification.getOtherReport().equals("true")) {
            // 其它报告
            notification.setOtherReport(TypeKey.ACTIVE.toString());
        } else {
            notification.setOtherReport(TypeKey.INACTIVE.toString());
        }

        // zjn 2020-11-30 占用库存复制保存
        // notification.setOccupiedStockCopy(notification.getOccupiedStock());

        if (StringUtils.isNotBlank(notification.getEstimateRemark())) {
            if (notification.getOueDays() != null && notification.getOueDays() > 0) {
                notification.setEstimateStartDate(DateUtils.dateAdd(new Date(), notification.getOueDays()));
            } else {
                notification.setEstimateStartDate(new Date());
            }
        }

        if (StringUtils.isBlank(notification.getSaleId()) || StringUtils.isBlank(notification.getSaleCsutomerId())) {
            // 获取合同的销售公司id
            ContractDetail contDetail = notification.getContractDetail();
            contDetail.setCompany(notification.getCompany());
            Contract contract = contractDao.getContractSaleData(contDetail);
            if (null != contract) {
                notification.setSaleId(contract.getSaleId());
                notification.setSaleCsutomerId(contract.getSaleCsutomerId());
            }
        }

        if (null == notification.getRecordId()) {
            if (null != notification.getCheckFlag() && notification.getCheckFlag()
                    && Collections3.isEmpty(notification.getMonyDeailList())) {
                result.put("message", "请确保每个订单批次有生产日期!");
                result.put("result", "false");
                return result;
            }

            /*
             * if (null != notification.getOccupiedStock() && notification.getOccupiedStock() > 0 &&
             * StringUtils.isNotBlank(notification.getMaterialId())) { // 验证该物料可用库存是否大于0 // 根据物料id进行可用库存减去 Material
             * material = new Material(notification.getMaterialId());
             * material.setOccStocks(notification.getOccupiedStock()); materialDao.updateMaterialOccStock(material); }
             */
            // 判断编号是否被引用了 是就累加
            if (notification.getFindNoisEnable() == 1) {
                notification.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.REQUISITION.getIndex().toString()));
            }
            Integer noNum = notificationDao.findNotificationNoisEnable(notification); // 查询编号是否依旧存在
            while (noNum != null && noNum > 0) {
                // 将对应编码的nextNo 修改为+1
                CommonUtils.updateNextNo(CommonEnums.CodeType.REQUISITION.getIndex());
                notification.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.REQUISITION.getIndex().toString()));
                noNum = notificationDao.findNotificationNoisEnable(notification); // 查询编号是否依旧存在
            }

            notification.setMergeType(TypeKey.SL_NOTIFICATION_MERGE_NORMAL);// 非合单
            notification.setCraftNo(notification.getCraftNo().trim());
            notification.setNotificationType(TypeKey.ACTIVE);// 正常通知单
            notification.preInsert();
            // 操作合同明细工艺
            String customerId = this.updateContractCraft(notification, false);
            notificationDao.insert(notification);
            CommonUtils.updateNextNo(CommonEnums.CodeType.REQUISITION.getIndex());// 添加成功把通知单编码+1

            // 添加成品物料
            this.insertMaterial(notification, customerId);
        } else {
            remark = notificationDao.getEstRemark(notification.getRecordId());
            /*
             * notification.setOldOccupiedStock( null == notification.getOldOccupiedStock() ? 0 :
             * notification.getOldOccupiedStock()); notification .setOccupiedStock(null ==
             * notification.getOccupiedStock() ? 0 : notification.getOccupiedStock()); //
             * 修改的时候验证占用库存是否修改，修改了需要修改物料的可用库存（释放或占用一部分库存） if (notification.getOccupiedStock() !=
             * notification.getOldOccupiedStock()) { Integer occStock = 0;
             *
             * if (notification.getOccupiedStock() > notification.getOldOccupiedStock()) { occStock =
             * notification.getOccupiedStock() - notification.getOldOccupiedStock(); } else if
             * (notification.getOccupiedStock() < notification.getOldOccupiedStock()) { occStock =
             * notification.getOccupiedStock() - notification.getOldOccupiedStock(); } Material material = new
             * Material(notification.getMaterialId()); material.setOccStocks(occStock);
             * materialDao.updateMaterialOccStock(material); }
             */
            // Add lpjLiu 2016-12-08 生产编号改变了,重新处理
            if (notification.getIsModCraftNo()) {
                // 重新创建生产工艺
                notification.setSavetypees(2);

                // 查询工艺
                ContractCraft craft = contractCraftDao.get(notification.getContractDetail().getContractCraftList());
                notification.getContractDetail().setContractCraftList(craft);

                // 操作合同明细工艺
                String resultStr = this.updateContractCraft(notification, true);
                if (StringUtils.isNotBlank(resultStr)) {
                    result.put("message", "厂编" + notification.getCraftNo() + "已经被使用!");
                    result.put("result", "fail");
                    return result;
                }

                // 添加成品物料
                this.insertMaterial(notification, null);

                // 若生产编号使用了当前的生产编号，就将生产编号+1
                String craftNo = CommonUtils.geDocumentNo(CommonEnums.CodeType.PRODUCTION.getIndex().toString(),
                        notification.getCustomerNo());
                if (craftNo.equals(notification.getCraftNo().trim())) {
                    CommonUtils.updateNextNo(CommonEnums.CodeType.PRODUCTION.getIndex());
                }
            } else {
                notification.setCraftNo(null);
            }

            notification.preUpdate();
            if (StringUtils.isBlank(remark)) {
                notificationDao.updateNotificationDataOnEditNotfThree(notification);
            } else {
                notificationDao.updateNotificationDataOnEditNotf(notification);
            }

            // Add lpjLiu 2016-12-08 生产编号改变了,更新本合同下面的其它通知单编号
            if (notification.getIsModCraftNo()) {
                notificationDao.updateCraftNoByContractDetailAndNotInNotifiId(notification);
            }
        }

/*        if (StringUtils.isNotBlank(notification.getEstimateRemark()) && !notification.getEstimateRemark().equals("样品")
                && StringUtils.isBlank(remark)) {
            // 占用替换料库存
            notificationDao.delNotifiMaterialTwo(notification);
            if (notification.getNotifiMaterList() != null && notification.getNotifiMaterList().size() > 0) {
                notification.setCompany(company);
                notificationDao.insertNotifiMaterialTwo(notification);
            }

            // 验证批次信息
            List<NotificationMonyDeail> notiList = getMonyDeailList(notification.getMonyDeailList());
            // for (int i = 0; i < notiList.size(); i++)
            // {
            // if (notiList.get(i).getProTime() == null)
            // {
            // // 日期为空代表未分配到生产日期，返回前端
            // result.put("message", "请确保每个订单批次有生产日期!");
            // result.put("result", "false");
            // return result;
            // }
            // }

            // 进行全部删除和批量插入
            if (Collections3.isNotEmpty(notification.getMonyDeailList())) {
                NotificationMonyDeail deail = new NotificationMonyDeail();
                deail.setNoficationId(notification.getRecordId());
                notificationDao.delMonyDeailByNotification(deail);
                notification.setMonyDeailList(notiList);
                notification.preInsert();
                notificationDao.monyDeailInsert(notification);
            }
        }*/

        // zjn 2021-05-11 防止通知单交货日期为空
        if (null == notification.getDeliveryDate()) {
            notificationDao.updateNotiDeliveryDate(notification);
        }

        // 更新和单的评估结果
        updateMergeNotiEstimateRemark(notification);

        // 更新销售公司和龙南的物料和厂编
        contractCraftDao.updateGroupCenterNo(notification);
        materialDao.updateGroupCenterNo(notification);

        result.put("message", "保存通知单" + notification.getNo() + "成功!");
        result.put("result", "success");
        return result;
    }

    @Transactional(readOnly = false)
    public String saveNotificationSee(Notification notification) {
        Company company = UserUtils.getUser().getCompany();
        notification.setCompany(company);
        if (StringUtils.isNotBlank(notification.getEstimateRemark())) {
            // 判断通知单是否要对产品进行备料
            if (notification.getReplyStock() != null && notification.getReplyStock() > 0) {
                // 生成原料申请单
                PurchRaw purchRow = new PurchRaw();
                Material material = new Material(notification.getMaterialId());
                String no = CommonUtils.geDocumentNo("22");
                purchRow.setCompany(company);
                purchRow.setPurchApplyId(no);
                purchRow.setMaterial(material);
                if(null != notification.getReplyStock())
                {
                    purchRow.setQuantity(new BigDecimal(notification.getReplyStock()));
                }
                purchRow.preInsert();
                purchRow.setRemark(notification.getReplyremark());
                purchRow.setStatus("1");
                purchRow.setNotifiId(notification.getRecordId());
                purchRow.setMergeType(TypeKey.PU_PURCHASING_APPLY_MERGETY_ONE.toString());
                purchRow.setType("2");
                CommonUtils.updateNextNo(22);

                // 获取库位
                MaterialPlaceCom materialPlaceCom = new MaterialPlaceCom();
                materialPlaceCom.setSaleCompanyId(notification.getSaleId());
                materialPlaceCom.setMaterialId(notification.getMaterialId());
                materialPlaceCom.setCompanyId(notification.getCompany().getRecordId());
                List<MaterialPlaceCom> placeComList = storeHouseDao.getMaterialPlaceComListTwo(materialPlaceCom);
                if (Collections3.isNotEmpty(placeComList)) {
                    MaterialPlaceCom maCom = placeComList.get(0);
                    purchRow.setStockPlaceComId(maCom.getRecordId());
                    purchRow.setStockPlaceId(maCom.getMaterPlaceId());
                    purchRow.setBranchId(maCom.getSaleCompanyId());
                } else {
                    materialPlaceCom.setSaleCompanyId(null);
                    placeComList = storeHouseDao.getMaterialPlaceComListTwo(materialPlaceCom);
                    if (Collections3.isNotEmpty(placeComList)) {
                        MaterialPlaceCom maCom = placeComList.get(0);
                        purchRow.setStockPlaceComId(maCom.getRecordId());
                        purchRow.setStockPlaceId(maCom.getMaterPlaceId());
                    }
                }
                purchRawDao.savePurRaw(purchRow);
            }
            notification.preUpdate();
            if (notification.getOueDays() != null && notification.getOueDays() > 0) {
                notification.setEstimateStartDate(DateUtils.dateAdd(new Date(), notification.getOueDays()));
            } else {
                notification.setEstimateStartDate(new Date());
            }
            notificationDao.updateNotificationDataOnEditNotfTwo(notification);

            // 占用替换料库存
            notificationDao.delNotifiMaterialTwo(notification);
            if (notification.getNotifiMaterList() != null && notification.getNotifiMaterList().size() > 0) {
                notification.setCompany(company);
                notificationDao.insertNotifiMaterialTwo(notification);
            }

            // 验证批次信息
            List<NotificationMonyDeail> notiList = getMonyDeailList(notification.getMonyDeailList());
            // for (int i = 0; i < notiList.size(); i++)
            // {
            // if (notiList.get(i).getProTime() == null)
            // {
            // // 日期为空代表未分配到生产日期，返回前端
            // result.put("message", "请确保每个订单批次有生产日期!");
            // result.put("result", "false");
            // return result;
            // }
            // }

            // 进行全部删除和批量插入
            if (Collections3.isNotEmpty(notification.getMonyDeailList())) {
                NotificationMonyDeail deail = new NotificationMonyDeail();
                deail.setNoficationId(notification.getRecordId());
                notificationDao.delMonyDeailByNotification(deail);
                notification.setMonyDeailList(notiList);
                notification.preInsert();
                notificationDao.monyDeailInsert(notification);
            }

            // 更新和单的评估结果
            updateMergeNotiEstimateRemark(notification);

            return "success";
        }

        // 更新和单的评估结果
        updateMergeNotiEstimateRemark(notification);

        return "false";
    }

    /**
     * wc 2016-11-09 添加成品物料
     */
    public void insertMaterial(Notification notification, String customerId) {
        // if (notification.getSavetypees() == 3)
        // {
        // // 工艺编号以前有，未修改
        // return;
        // }
        Material material = new Material(notification.getCraftNo(), notification.getCompany());
        Integer num = materialDao.selectmaterialByno(material);
        if (num == 0) {
            // zjn 2017-09-15 自动生成的物料，默认仓库为成品仓的第一个 begin
            List<StoreHouse> storeHouseList = storeHouseDao.selectStoreHouselist(notification.getCompany());
            List<StoreHouse> storeHouseListCopy = new ArrayList<>();
            for (StoreHouse storeHouse : storeHouseList) {
                if (storeHouse.getType() == 2) {
                    storeHouseListCopy.add(storeHouse);
                }
            }
            if (storeHouseListCopy != null && storeHouseListCopy.size() > 0) {
                material.setStorehouse(storeHouseListCopy.get(0));
            }
            // end
            Customer da = new Customer();
            if (customerId != null && StringUtils.isNotBlank(customerId)) {
                da = customerDao.get(customerId);
            } else {
                da = customerDao.getdata(notification.getContract().getRecordId());
            }
            material.setCustomerNo(da.getNo());// 客户编号
            material.setMaterialKind(TypeKey.MD_MATERIAL_TYPE_PRODUCT);
            material.setCompany(notification.getCompany());
            material.setNo(notification.getCraftNo().trim());
            material.setSpecification(notification.getCustomerModel());
            material.setSystemFlag(ConstKey.MATERIAL_SYSTEM_FLAG);
            material.setApplicanter(notification.getCreatedBy());
            material.setAvailableQty(BigDecimal.ZERO);
            material.setMaterialType(returnMaterialType());// zjn 2017-09-14
            material.preInsert();
            // WC 2017-01-22 新增字段赋值
            material.setDepositQty(0);
            material.setDepositOaQty(0);
            material.setCustomer(da);
            material.setDeliverySize(this.getDeliverySize(notification));
            materialDao.insert(material);
        }
    }

    /**
     * WC 2017-01-22 将交货尺寸拼接成字符串格式：xx*xx/x
     */
    public String getDeliverySize(Notification notification) {
        if (notification.getContractDetail() == null || notification.getContractDetail().getContractCraftList() == null) {
            return "";
        }
        ContractCraft craft = notification.getContractDetail().getContractCraftList();
        String deliveryLength = ""; // 交货长度
        String deliveryWidth = ""; // 交货宽度
        String deliveryDivisor = ""; // PCS个数
        if (craft.getPnlLength() != null) {
            deliveryLength = craft.getPnlLength().toString();
        } else {
            deliveryLength = craft.getUnitLength().toString();
        }
        if (craft.getPnlWidth() != null) {
            deliveryWidth = craft.getPnlWidth().toString();
        } else {
            deliveryWidth = craft.getUnitWidth().toString();
        }
        if (craft.getPnlDivisor() == null || craft.getPnlDivisor().compareTo(BigDecimal.ZERO) == 0) {
            deliveryDivisor = "1";
        } else {
            deliveryDivisor = craft.getPnlDivisor().toString();
        }
        if (StringUtils.isNotBlank(deliveryLength) && StringUtils.isNotBlank(deliveryWidth)
                && StringUtils.isNotBlank(deliveryDivisor)) {
            return deliveryLength + "*" + deliveryWidth + "/" + deliveryDivisor;
        }
        return "";
    }

    /**
     * wc 2016-11-09 操作合同明细工艺
     */
    public String updateContractCraft(Notification notification, Boolean flag) {
        // 防止高并发引起的编码重复
        ContractCraft craft = notification.getContractDetail().getContractCraftList();
        craft.setStatus(TypeKey.SL_CRAFT_STATUS_CONFIRMED.toString());
        craft.setNo(notification.getCraftNo());
        List<ContractCraft> contractCrafts = contractCraftDao.craftCraftList(craft);
        // 及时获取当前合同的厂编内容
        ContractCraft craftDeal = contractCraftDao.getDealCraft(craft);
        if (contractCrafts.size() > 0) {
            // 删除新加厂编
            if (StringUtils.isBlank(craftDeal.getNo())) {
                contractCraftDao.deleteCraftByNo(craftDeal);
            }
            // 首先查询该厂编是否存在并且工艺是否一样，是的话删除状态为1的当前绑定厂编
            notification.getContractDetail().setCraft(contractCrafts.get(0));
            contractDetailDao.updateCraftId(notification.getContractDetail());
        } else {
            Integer count = contractCraftDao.findNotificationCraft(craft);
            while (count != null && count > 0) {
                if (flag) {
                    return "noFail";
                } else {
                    // 将对应编码的nextNo 修改为+1
                    CommonUtils.updateNextNo(CommonEnums.CodeType.PRODUCTION.getIndex());
                    craft.setNo(CommonUtils.geDocumentNo(CommonEnums.CodeType.PRODUCTION.getIndex().toString()));
                    count = contractCraftDao.findNotificationCraft(craft); // 查询编号是否依旧存在
                }
            }
            // 其次如果该厂编编码不存在的话直接修改当前合同明细所绑定的厂编，并修改状态值
            notification.setCraftNo(craft.getNo());
            notification.getContractDetail().getContractCraftList().setAddCraftFlag(null);
            notification.getContractDetail().getContractCraftList().setNo(notification.getCraftNo());
            // 判断该厂编是更新还是新增
            if (StringUtils.isBlank(craftDeal.getNo()) && StringUtils.isNotBlank(craftDeal.getAddCraftFlag())) {
                // 更新厂编，更新状态
                craft.setAddCraftFlag(null);
                craft.preUpdate();
                craft.setStatus(TypeKey.SL_CRAFT_STATUS_CONFIRMED.toString());
                contractCraftDao.updateStatusAndCraftNo(craft);
                notification.getContractDetail().setCraft(notification.getContractDetail().getContractCraftList());
            } else {
                // 新增
                contractCraftDao.insertStatusAndCraftNo(craft);
                notification.getContractDetail().setCraft(craft);
                contractDetailDao.updateCraftId(notification.getContractDetail());
            }
            CommonUtils.updateNextNo(CommonEnums.CodeType.PRODUCTION.getIndex());
        }
        return null;
    }

    @Transactional(readOnly = false)
    public void delete(Notification notification) {
        /*
         * if (notification.getOccupiedStock() != null && notification.getOccupiedStock() > 0) { Material material = new
         * Material(notification.getMaterialId()); material.setOccStocks(-notification.getOccupiedStock());
         * materialDao.updateMaterialOccStock(material); }
         */
        super.delete(notification);

        // WeiXinReportUtil.deliveryUpdate();
    }

    @Transactional(readOnly = false)
    public String updateDeliveryFlag(Notification notification) {
        if (notification.getDeliveryFlag() != null && notification.getDeliveryFlag() == 1) {
            notification.setDeliveryFlag(null);
        } else {
            notification.setDeliveryFlag(1);
        }
        notificationDao.updateDeliveryFlag(notification);
        return "操作成功";
    }

    @Transactional(readOnly = false)
    public String updateALLDeliveryFlag(Notification notification) {
        notificationDao.updateALLDeliveryFlag(notification);
        return "操作成功";
    }

    /**
     * 作废通知单
     *
     * @param notification
     * @return
     */
    @Transactional(readOnly = false)
    public int updateNotificationStutasForCancel(Notification notification) {
        // 释放占用库存
        if (notification.getOccupiedStock() != null && notification.getOccupiedStock() > 0) {
            Material material = new Material(notification.getMaterialId());
            material.setOccStocks(-notification.getOccupiedStock());
            materialDao.updateMaterialOccStock(material);
        }
        int i = notificationDao.updateStatusById(notification);

        // 删除通知单任务安排记录
        notiSchedulRecordDao.delete(notification);

        // 通知单任务安排
        notificationSchedulingDao.delete(notification);

        // WeiXinReportUtil.deliveryUpdate();
        return i;
    }

    public int getCountByContractDetailAndNotInNotifiId(Notification noti) {
        return notificationDao.getCountByContractDetailAndNotInNotifiId(noti);
    }

    public int getContractCountNotification(ContractDetail contract) {
        return notificationDao.getContractCountNotification(contract);
    }

    @Transactional(readOnly = false)
    public String genPurchRaw(Notification notification) {
        Company company = UserUtils.getUser().getCompany();
        if (notification.getReplyStock() != null && notification.getReplyStock() > 0) {
            // 生成原料申请单
            PurchRaw purchRow = new PurchRaw();
            Material material = new Material(notification.getMaterialId());
            String no = CommonUtils.geDocumentNo("22");
            purchRow.setCompany(company);
            purchRow.setPurchApplyId(no);
            purchRow.setMaterial(material);
            if(null != notification.getReplyStock())
            {
                purchRow.setQuantity(new BigDecimal(notification.getReplyStock()));
            }
            purchRow.preInsert();
            purchRow.setRemark(notification.getReplyremark());
            purchRow.setStatus("1");
            purchRow.setNotifiId(notification.getRecordId());
            purchRow.setNotifiMaterialId(notification.getNotifiMaterialId());
            purchRow.setMergeType(TypeKey.PU_PURCHASING_APPLY_MERGETY_ONE.toString());
            purchRow.setType("2");
            CommonUtils.updateNextNo(22);
            purchRawDao.savePurRaw(purchRow);
        }
        return "success";
    }

    public void sentPurchRaw(Notification notification, Company company) {
        // 判断通知单是否要对产品进行备料
        if (notification.getReplyStock() != null && notification.getReplyStock() > 0) {
            // 获取销售公司跟单员
            String finalUserId = notificationDao.getFinalUserId(notification);

            purchRawDao.delPurchRaw(notification);

            // 生成原料申请单
            PurchRaw purchRow = new PurchRaw();
            Material material = new Material(notification.getMaterialId());
            String no = CommonUtils.geDocumentNo("22", company);
            purchRow.setCompany(company);
            purchRow.setPurchApplyId(no);
            purchRow.setMaterial(material);
            if(null != notification.getReplyStock())
            {
                purchRow.setQuantity(new BigDecimal(notification.getReplyStock()));
            }
            purchRow.preInsert();
            if (StringUtils.isNotBlank(finalUserId)) {
                User user = new User(finalUserId);
                purchRow.setCreatedBy(user);
                purchRow.setLastUpdBy(user);
            }
            purchRow.setRemark(notification.getReplyremark());
            purchRow.setStatus("1");
            purchRow.setNotifiId(notification.getRecordId());
            purchRow.setMergeType(TypeKey.PU_PURCHASING_APPLY_MERGETY_ONE.toString());
            purchRow.setType("2");
            CommonUtils.updateNextNo(22);

            // 获取库位
            MaterialPlaceCom materialPlaceCom = new MaterialPlaceCom();
            materialPlaceCom.setSaleCompanyId(notification.getSaleId());
            materialPlaceCom.setMaterialId(notification.getMaterialId());
            materialPlaceCom.setCompanyId(notification.getCompany().getRecordId());
            List<MaterialPlaceCom> placeComList = storeHouseDao.getMaterialPlaceComListTwo(materialPlaceCom);
            if (Collections3.isNotEmpty(placeComList)) {
                MaterialPlaceCom maCom = placeComList.get(0);
                purchRow.setStockPlaceComId(maCom.getRecordId());
                purchRow.setStockPlaceId(maCom.getMaterPlaceId());
                purchRow.setBranchId(maCom.getSaleCompanyId());
            } else {
                materialPlaceCom.setSaleCompanyId(null);
                placeComList = storeHouseDao.getMaterialPlaceComListTwo(materialPlaceCom);
                if (Collections3.isNotEmpty(placeComList)) {
                    MaterialPlaceCom maCom = placeComList.get(0);
                    purchRow.setStockPlaceComId(maCom.getRecordId());
                    purchRow.setStockPlaceId(maCom.getMaterPlaceId());
                }
            }
            purchRawDao.savePurRaw(purchRow);
        }
    }

    /**
     * 确认通知单
     *
     * @param notification
     * @return
     */
    @Transactional(readOnly = false)
    public synchronized String updateNotificationStutas(Notification notification) {

        if (null != notification){
            String companyId = UserUtils.getUser().getCompanyId();
            String type = "3";
            String dataId = notification.getRecordId();
            ChangeDataUtils changeDataUtils = new ChangeDataUtils();
            Map<String,String> checkMap = changeDataUtils.checkLockMap(companyId,type,dataId);
            if(null != checkMap && "fail".equals(checkMap.get("result").toString()))
            {
                return checkMap.get("message").toString();
            }

        }

        Integer num = notificationDao.getCheckCardCount(notification);
        if (null != num && num > 0) {
            return "1";
        }

        /*  sentPurchRaw(notification, company);*/

        // 处理返单的通知单，需要生成流程卡
        String result = dealReorder(notification);
        if(!"success".equals(result))
        {
            return result;
        }

        notification.setConfirmDate(null);
        if (StringUtils.isNotBlank(notification.getStatus()) && notification.getStatus()
                .equals(TypeKey.SL_NOTIFICATION_STATUS_CONFIRMED.toString())) {
            notification.setConfirmDate(Calendar.getInstance().getTime());
        }

        // 更新评估结果(自动生成样品通知单，无评估结果)
        if (StringUtils.isNotBlank(notification.getEstimateRemark())) {
            notificationDao.updateEstimateRemark(notification);
        }

        if (notification.getCardA() != null && StringUtils.isNotBlank(notification.getCardA().getRecordId())) {
            //获取销售公司的合同明细id
            String contractDetailId =  contractDetailDao.getSaleContractDetailId(notification.getContractDetail().getRecordId());

            Backups approveBack =  notificationDao.getApproveBack(contractDetailId);

            if (approveBack!=null){
                notificationDao.updateBorderWidth(approveBack.getBordLength(),approveBack.getBordWidth(),notification.getCardA().getRecordId());
            }
            return String.valueOf(notificationDao.updateNotificationStutasAndCardA(notification));
        } else {
            // 查询生产编号对应的最新的工程卡
            CardA queryCardA = new CardA();
            queryCardA.setCompany(notification.getCompany());
            queryCardA.setProduceModel(notification.getCraftNo());
            CardA dealCardA = cardADao.getByCraftNo(queryCardA);
            if (dealCardA == null) {
                return "failCardA";
            }else{
                return String.valueOf(notificationDao.updateStatusById(notification));
            }
        }

    }

    /**
     * 处理返单的通知单，需要生成流程卡
     *
     * @param notification
     */
    public String dealReorder(Notification notification) {
        String result = null;
        // 判断通知单的订单类型是否是返单、返单直接生成工程卡
        List<DictValue> orderTypeList = DictUtils.getValuesByItem(DictItemEnum.ORDER_TYPE);

        boolean isReorder = false;
        boolean isSample = false;
        for (DictValue value : orderTypeList) {
            // 选择(样品转量产，样品返单有改，生产返单有改),重新设置生产编号内容
            if (notification.getOrderType().getRecordId().equals(value.getRecordId())) {
                if (ConstKey.ORDER_TYPE_REORDER_PRODUCTION.equals(value.getValue())
                        || ConstKey.ORDER_TYPE_REORDER_SAMPLE.equals(value.getValue())
                        || "补料生产返单有改".equals(value.getValue()) || "只做资料不投产".equals(value.getValue())) {
                    isReorder = true;
                    if (ConstKey.ORDER_TYPE_REORDER_SAMPLE.equals(value.getValue())) {
                        isSample = true;
                    }
                    break;
                }
            }
        }

        if (!isReorder) {
            // 通知单确认生成通知单任务安排
            ChangeDataUtils changeDataUtils = new ChangeDataUtils();
            changeDataUtils.setNotificationScheduling(notification);
            return "fail";
        }
        // 补料的不参与合单
        if (StringUtils.isNotBlank(notification.getDocumentsStatus()) && !"正常".equals(notification.getDocumentsStatus())) {
            result = "fail";
            return result;
        }

        // 查询生产编号对应的最新的工程卡
        CardA queryCardA = new CardA();
        queryCardA.setCompany(notification.getCompany());
        queryCardA.setProduceModel(notification.getCraftNo());
        CardA dealCardA = cardADao.getByCraftNo(queryCardA);
        if (dealCardA == null) {
            return "failCardA";
        }

        List<CardA> dealCardTwoList = cardADao.getByRemarkTwoList(queryCardA);
        if (Collections3.isNotEmpty(dealCardTwoList))
        {
            dealCardA.setProgressDesc(dealCardTwoList.get(0).getProgressDesc());
            for (CardAProcessValue cardAProcessValue : dealCardA.getProcessValueList())
            {
                for (CardA cardA : dealCardTwoList)
                {
                    if (cardA.getProcessId().equals(cardAProcessValue.getProcess().getRecordId()))
                    {
                        cardAProcessValue.setRemark(cardA.getRemark());
                        break;
                    }
                }
            }
        }
        // 获取客户编号和客户文件名
        String customerFileName = null;
        String customerNo = null;
        ContractDetail detail = contractDetailDao.getCustomerFileName(notification.getContractDetail());
        if (detail != null) {
            customerFileName = detail.getCustomerFileName();
            if (detail.getContract() != null && detail.getContract().getCustomer() != null) {
                customerNo = detail.getContract().getCustomer().getNo();
            }
        }

        // 判断工程卡是合并了几个，最多不可超过18个
        String[] nos = dealCardA.getNoticeNos().split(ConstKey.COMMA);

        boolean isMerge = false;
        if (dealCardA.getStatus().intValue() == TypeKey.EG_PROCESSCARD_STATUS_DRAFT.intValue()
                && !getFeedingCountByCard(dealCardA) && StringUtils.isBlank(dealCardA.getCancelCause())) // 判断是否存在投补料单；无投料单取消确认工程卡也不能自动生成采购单
        {
            if (nos.length <= ConstKey.MAX_CARD_MERGE_COUNT) {
                ParameterSet queryParam = new ParameterSet();
                queryParam.setCompany(notification.getCompany());
                queryParam.setJianPin(ConstKey.PARAMETERSET_SOMECRAFTNOAUTOMERGE);
                ParameterSet parameterSet = parameterSetDao.getParameterSetByCompanyAndjianPin(queryParam);// 查询每个公司设置的业务员跟单期限
                if (parameterSet != null && StringUtils.isNotBlank(parameterSet.getParameterValue())) {
                    if (nos.length < Integer.parseInt(parameterSet.getParameterValue())) {
                        if (StringUtils.isNotBlank(parameterSet.getStart())) {
                            if (parameterSet.getStart().equals(TypeKey.YES.toString())) {
                                isMerge = true;
                            }
                        }
                    }
                }
            }
        }

        // 如果是返单样品不自动和单或者只做资料不投产
        if (isSample || "只做资料不投产".equals(dealCardA.getOrderTypeValue())) {
            isMerge = false;
        }

        // 设置周期
        // dealCardA.setProgressDesc(notification.getProgressDesc());

        // 销售公司两个客户不自动合单
        Boolean checkFlag = false;
        if (StringUtils.isNotBlank(notification.getSaleCsutomerId()) && ("27923".equals(notification.getSaleCsutomerId())
                || "34245".equals(notification.getSaleCsutomerId()))) {
            checkFlag = true;
        }

        // 工程卡若是未确认直接合并到该工程卡，否则创建新的工程卡
        if (isMerge && !checkFlag) {
            dealCardA.setNo(dealCardA.getNo() + ConstKey.COMMA + notification.getCraftNo());
            dealCardA.setProduceModel(dealCardA.getProduceModel() + ConstKey.COMMA + notification.getCraftNo());
            dealCardA.setNoticeNos(dealCardA.getNoticeNos() + ConstKey.COMMA + notification.getNo());
            // 客户型号
            dealCardA
                    .setCustomerModels(dealCardA.getCustomerModels() + ConstKey.COMMA + notification.getCustomerModel());

            // 客户编号
            if (StringUtils.isNotBlank(customerNo)) {
                dealCardA.setCustomerNos(dealCardA.getCustomerNos() + ConstKey.COMMA + customerNo);
            }

            // 客户文件名
            if (StringUtils.isNotBlank(customerFileName)) {
                dealCardA.setCustomerFileNames(dealCardA.getCustomerFileNames() + ConstKey.COMMA + customerFileName);
            }

            // 描述
            if (StringUtils.isNotBlank(notification.getRemark())) {
                dealCardA.setRemark(dealCardA.getRemark() + ConstKey.COMMA + notification.getRemark());
            }

            // 合单单更新最新物料
            if (StringUtils.isNotBlank(notification.getMaterialId())) {
                dealCardA.setMaterialId(notification.getMaterialId());
            }

            // String[] notificationNos = dealCardA.getNoticeNos().split(",");
            // for (String no : notificationNos)
            // {
            // // 更新通知单任务安排的状态
            // Notification notifi = new Notification();
            // notifi.setCompany(dealCardA.getCompany());
            // notifi.setNo(no);
            // Notification n = notificationDao.getFindNotification(notifi);
            //
            // // 更新通知单的物料id
            // n.setMaterialId(dealCardA.getMaterialId());
            // dao.updateMaterialId(n);
            // }

            // 更新流程卡
            dealCardA.preUpdate();
            cardADao.updateCardA(dealCardA);

            // 设置通知单的流程卡，用于 更新通知单
            notification.setCardA(dealCardA);
        } else {
            // 设置流程卡A的数据
            dealCardA.setRecordId(null);
            dealCardA.setCancelCause(null);
            dealCardA.setNo(notification.getCraftNo());
            dealCardA.setProduceModel(notification.getCraftNo());
            dealCardA.setNoticeNos(notification.getNo());
            dealCardA.setCustomerModels(notification.getCustomerModel());
            if (StringUtils.isNotBlank(customerNo)) {
                dealCardA.setCustomerNos(customerNo);
            }

            if (StringUtils.isNotBlank(customerFileName)) {
                dealCardA.setCustomerFileNames(customerFileName);
            }
            dealCardA.setOrderType(Long.parseLong(notification.getOrderType().getRecordId()));

            dealCardA.preInsertDate();
            if (dealCardA.getCreatedBy() == null) {
                dealCardA.setLastUpdBy(dealCardA.getCreatedBy());
            } else {
                dealCardA.setLastUpdBy(dealCardA.getLastUpdBy());
            }
            dealCardA.setOrderType(Long.parseLong(notification.getOrderType().getRecordId()));
            dealCardA.setCardMadeDate(dealCardA.getCreatedDate());
            dealCardA.setStatus(TypeKey.EG_PROCESSCARD_STATUS_DRAFT);
            if (StringUtils.isNotEmpty(dealCardA.getRemark()) && StringUtils.isNotEmpty(notification.getRemark())) {
                /* dealCardA.setRemark(dealCardA.getRemark() + ConstKey.COMMA + notification.getRemark()); */
                dealCardA.setRemark(notification.getRemark());
            } else if (StringUtils.isNotEmpty(dealCardA.getRemark())) {
                dealCardA.setRemark(dealCardA.getRemark());
            } else if (StringUtils.isNotEmpty(notification.getRemark())) {
                dealCardA.setRemark(notification.getRemark());
            }
            dealCardA.setRemark2(dealCardA.getRemark2());

            // 查询一钻孔和二钻孔
            DrillInfo drillInfo = new DrillInfo();
            drillInfo.setCompany(dealCardA.getCompany());
            drillInfo.setCardB(dealCardA.getCardB());
            List<DrillInfo> firstDrillInfoList = drillInfoDao.findFirstDrillListByCardB(drillInfo);
            List<DrillInfo> secondDrillInfoList = drillInfoDao.findSecondDrillListByCardB(drillInfo);

            // 设置流程卡B的数据
            dealCardA.getCardB().setRecordId(null);
            dealCardA.getCardB().setCompany(dealCardA.getCompany());
            dealCardA.getCardB().setStatus(TypeKey.EG_PROCESSCARD_STATUS_DRAFT);
            dealCardA.getCardB().preInsert();

            if (!Collections3.isEmpty(dealCardA.getCardB().getAttachList())) {
                dealCardA.getCardB().getAttachList().forEach(item -> {
                    item.setRecordId(null);
                });
            }

            // 若钻孔为空，插入一个刀序
            if (Collections3.isEmpty(firstDrillInfoList)) {
                DrillInfo drill = new DrillInfo();
                drill.preInsert();
                drill.setDrillTurns(1);
                drill.setDrillOrder("T1");
                firstDrillInfoList.add(drill);
            } else {
                firstDrillInfoList.forEach(item -> {
                    item.setRecordId(null);
                    item.setCardB(null);
                    item.preInsert();
                });
            }

            // 若钻孔为空，插入一个刀序
            if (Collections3.isEmpty(secondDrillInfoList)) {
                DrillInfo drill = new DrillInfo();
                drill.preInsert();
                drill.setDrillTurns(2);
                drill.setDrillOrder("T1");
                secondDrillInfoList.add(drill);
            } else {
                secondDrillInfoList.forEach(item -> {
                    item.setRecordId(null);
                    item.setCardB(null);
                    item.preInsert();
                });
            }

            dealCardA.getCardB().setFirstDrillInfoList(firstDrillInfoList);
            dealCardA.getCardB().setSecondDrillInfoList(secondDrillInfoList);

            // 设置流程卡开料数据
            dealCardA.getCardB().getBoardCutting().setRecordId(null);
            dealCardA.getCardB().getBoardCutting().setCompany(dealCardA.getCompany());
            dealCardA.getCardB().getBoardCutting().preInsert();

            // 设置开料A板数据
            dealCardA.getCardB().getBoardCutting().getPartA().setRecordId(null);
            dealCardA.getCardB().getBoardCutting().getPartA().setCompany(dealCardA.getCompany());
            dealCardA.getCardB().getBoardCutting().getPartA().preInsert();

            // 设置开料B板数据
            dealCardA.getCardB().getBoardCutting().getPartB().setRecordId(null);
            dealCardA.getCardB().getBoardCutting().getPartB().setCompany(dealCardA.getCompany());
            dealCardA.getCardB().getBoardCutting().getPartB().preInsert();

            // 合单单更新最新物料
            if (StringUtils.isNotBlank(notification.getMaterialId())) {
                dealCardA.setMaterial(new Material());
                dealCardA.getMaterial().setRecordId(notification.getMaterialId());
            }
            if(StringUtils.isBlank(dealCardA.getMaterialIds()) && null != dealCardA.getMaterial() && StringUtils.isNotBlank(dealCardA.getMaterial().getRecordId()))
            {
                dealCardA.setMaterialIds(dealCardA.getMaterial().getRecordId());
            }

            // 添加流程卡A
            dealCardA.setProductTypeId(notification.getProductTypeId());
            cardADao.insert(dealCardA);

            // 添加流程卡A的工序，工序对应工艺值
            int seqNum = 1;
            for (CardAProcessValue item : dealCardA.getProcessValueList()) {
                item.preInsert();
                item.setCardA(dealCardA);
                item.setCompany(dealCardA.getCompany());
                item.setSeqNum(seqNum);

                if (item.getProcess() != null && StringUtils.isNotBlank(item.getProcess().getOutDeal())) {
                    item.setOutDeal(item.getProcess().getOutDeal());
                }

                processValueDao.insert(item);

                // 添加工艺与值
                if (item.getCraftValueList() != null && item.getCraftValueList().size() > 0) {

                    // 赋值包装规格
                    if(StringUtils.isNotBlank(notification.getPackingRequirementValue()))
                    {
                        if(null != item.getProcess() && "终检入库".equals(item.getProcess().getCategory()))
                        {
                            for(CardAProcessCraftValue value : item.getCraftValueList())
                            {
                                if(null != value.getProcessCraft() && "包装规格".equals(value.getProcessCraft().getName()))
                                {
                                    value.setProcessCraftValue(notification.getPackingRequirementValue());
                                    break;
                                }
                            }
                        }
                    }
                    processValueDao.insertCardAProcessCraftValue(item);
                }
                seqNum++;
            }

            CardB cardB = dealCardA.getCardB();
            cardB.setCardA(dealCardA);

            // 添加开料信息A板
            if (cardB.getBoardCutting().getPartA() != null) {
                boardPartDao.insert(cardB.getBoardCutting().getPartA());
            }

            // 添加开料信息B板
            if (cardB.getBoardCutting().getPartB() != null) {
                boardPartDao.insert(cardB.getBoardCutting().getPartB());
            }

            // 添加开料信息
            boardCuttingDao.insert(cardB.getBoardCutting());

            // 添加CardB
            cardBDao.insert(cardB);

            // 添加一钻孔信息
            cardBDao.insertCardBFirstDrillInfo(cardB);

            // 添加二钻孔信息
            cardBDao.insertCardBSecondDrillInfo(cardB);

            // 添加附件
            if (cardB.getAttachList() != null && cardB.getAttachList().size() > 0) {
                cardBDao.insertCardBAttach(cardB);
            }

            // 更新CardA的CardB
            if(null == dealCardA.getVersion())
            {
                dealCardA.setVersion(0);
            }
            cardADao.update(dealCardA);

            // 更新通知单的流程卡
            notification.setCardA(dealCardA);
        }
        return "success";
    }

    /**
     * 生成pdf文件方法
     *
     * @param notifcation
     * @param basePath
     * @return
     */
    public Pdf getPdf(Notification notifcation, String basePath) {
        notificationDao.updateNotificationPrintNum(notifcation);
        // 查询通知单
        Notification notification = notificationDao.printNotification(notifcation);
        Map<String, Object> map = Maps.newHashMap();
        Pdf pdf = new Pdf();

        if (notification == null) {
            pdf.setDataMap(map);
            pdf.setFileName(ConstKey.DEFAULT_TEMPLATE_PATH + "notification.pdf");
            return pdf;
        }

        Branch branch = notification.getBranch();
        map.put("title1", FmtUtils.empty(branch.getName()));
        map.put("title2", FmtUtils.empty(branch.getNameEn()));

        if (branch != null && StringUtils.isNotEmpty(branch.getLogoPath())) {
            // map.put("image.logo", ServletActionContext.getRequest().getSession().getServletContext().getRealPath(
            // branch.getLogoPath()));
            String path = FileManageUtils.isLocal() ? basePath + ConstKey.GLOBAL_FILE_UPLOAD_PATH + branch.getLogoPath()
                    : FileManageUtils.getUrl(branch.getLogoPath()).toString();
            map.put("image.logo", path);
        }

        if (notification.getPackageReq() == null || notification.getPackageReq().equals("")) {

        } else {
            map.put("packageReq",
                    FmtUtils.blank(DictUtils
                            .getDictValue(notification.getPackageReq().toString(), DictItemEnum.PACKING_REQUIREMENT, "")));

        }

        // 下单日期
        map.put("orderDate", FmtUtils.formatDateWithoutTime(notification.getOrderDate()));

        map.put("no", FmtUtils.blank(notification.getNo()));
        // 客户PO号
        map.put("contractDetail.contract.customerPo", FmtUtils.blank(notification.getCustomerPo()));
        // 交货数量
        map.put("quantity", FmtUtils.blank(notification.getQuantity()));
        // 交货日期
        map.put("contractDetail.deliveryDate", FmtUtils.formatDateWithoutTime(notification.getDeliveryDate()));

        map.put("remark", FmtUtils.blank(notification.getRemark()));

        map.put("contractDetail.craft.no", FmtUtils.blank(notification.getCraftNo()));

        // 订单类型
        if (notification.getOrderType() != null) {
            map.put("orderType",
                    DictUtils
                            .getDictValue(notification.getOrderType().getRecordId().toString(), DictItemEnum.ORDER_TYPE, ""));
        } else {
            map.put("orderType", "");
        }
        ContractCraft craft = notification.getCraft();
        String remark = "";
        if (craft != null) {
            if (craft.getReferenceType() != null) {
                map.put("contractDetail.craft.referenceType",
                        DictUtils.getDictValue(craft.getReferenceType().toString(), DictItemEnum.SHAPING_WAY, ""));
            } else {
                map.put("contractDetail.craft.referenceType", "");
            }
            // map.put("contractDetail.craft.referenceDesc",FmtUtils.blank(notification.getContractDetail().getReferenceDesc()));
            if (craft.getBoardLevel() != null) {
                map.put("contractDetail.craft.materialType",
                        DictUtils.getDictValue(craft.getMaterialType().toString(), DictItemEnum.MATERIAL_TYPE, "") + "/  "
                                + DictUtils.getDictValue(craft.getBoardLevel().toString(), DictItemEnum.BOARD_LEVEL, ""));
            } else {
                map.put("contractDetail.craft.materialType", "");
            }

            if (craft.getSurfaceProcess() != null) {
                map.put("contractDetail.craft.surfaceProcess",
                        DictUtils.getDictValue(craft.getSurfaceProcess().toString(), DictItemEnum.SURFACE_PROCESS, ""));
            } else {
                map.put("contractDetail.craft.surfaceProcess", "");
            }

            if (craft.getBoardThickness() != null && craft.getCopperCladThickness() != null) {
                map.put("boardAndCopperThickness",
                        DictUtils.getDictValue(craft.getBoardThickness(), DictItemEnum.BOARD_THICKNESS, "") + DictUtils
                                .getDictValue(craft.getCopperCladThickness(), DictItemEnum.COPPER_CLAD_THICKNESS, ""));
            } else {
                map.put("boardAndCopperThickness", "");
            }

            if (craft.getSolderMaskType() != null) {
                map.put("contractDetail.craft.solderMaskType",
                        DictUtils.getDictValue(craft.getSolderMaskType().toString(), DictItemEnum.SOLDER_MASK_TYPE, ""));
            } else {
                map.put("contractDetail.craft.solderMaskType", "");
            }

            if (craft.getCharacterType() != null) {
                map.put("contractDetail.craft.characterType",
                        DictUtils.getDictValue(craft.getCharacterType().toString(), DictItemEnum.CHARACTER_TYPE, ""));
            } else {
                map.put("contractDetail.craft.characterType", "");
            }

            if (craft.getShapingWay() != null) {
                map.put("contractDetail.craft.shapingWay",
                        DictUtils.getDictValue(craft.getShapingWay().toString(), DictItemEnum.SHAPING_WAY, ""));
            } else {
                map.put("contractDetail.craft.shapingWay", "");
            }

            if (craft.getShapingWay() != null) {
                map.put("contractDetail.craft.shapingWay",
                        DictUtils.getDictValue(craft.getShapingWay().toString(), DictItemEnum.SHAPING_WAY, ""));
            } else {
                map.put("contractDetail.craft.shapingWay", "");
            }

            if (craft.getTestMethod() != null) {
                map.put("contractDetail.craft.testMethod",
                        DictUtils.getDictValue(craft.getTestMethod().toString(), DictItemEnum.TEST_METHOD, ""));
            } else {
                map.put("contractDetail.craft.testMethod", "");
            }
            if (craft.getDaore() != null) {
                map.put("contractDetail.craft.daore",
                        DictUtils.getDictValue(craft.getDaore().toString(), DictItemEnum.DAORE, ""));
            }
            if (craft.getNaiya() != null) {
                map.put("contractDetail.craft.naiya",
                        DictUtils.getDictValue(craft.getNaiya().toString(), DictItemEnum.NAIYA, ""));
            }
            if (craft.getInkType() != null) {
                map.put("contractDetail.craft.inkType",
                        DictUtils.getDictValue(craft.getInkType().toString(), DictItemEnum.INK_TYPE, ""));
            }
            if (StringUtils.isNotBlank(craft.getSpecialCraft())) {
                String value = notificationDao.getSpeialCraft(craft);
                if (StringUtils.isNotBlank(value)) {
                    remark = "(" + value + ")";
                }
            }
            // if (craft.getPliesnumber() != null)
            // {
            // map.put("contractDetail.craft.pliesnumber",
            // DictUtils.getDictValue(craft.getPliesnumber().toString(), DictItemEnum.PLIES_NUMBER, ""));
            // }
        }

        // Add: lpjLiu 2016-11-24
        if (notification.getCompany().getRecordId().equals(ConstKey.COMPANY_HaoYi)) {
            map.put("contractDetail.craft.customerModel",
                    FmtUtils.blank(notification.getCustomerModel() + " / " + notification.getCustomerNo()));
        } else {
            map.put("contractDetail.craft.customerModel", FmtUtils.blank(notification.getCustomerModel()));
        }

        map.put("contractDetail.quantity", FmtUtils.blank(notification.getQuantity()) + " PCS");
        map.put("contractDetail.contract.orderDate",
                FmtUtils.blank(FmtUtils.formatDateWithoutTime((notification.getOrderDate()))));
        // FmtUtils.blank(DictUtils.getDictValue(notification.getContractdetail().getDeliveryDays().toString(),DictItemEnum.DELIVERY_PRD_DAYS,""))
        // map.put("contractDetail.deliveryDate",DictUtils.getDictValue(notification.getContractdetail().getDeliveryDays().toString(),DictItemEnum.DELIVERY_PRD_DAYS,""));
        if (craft != null) {

            BigDecimal bd = new BigDecimal(craft.getUnitLength().toString());
            BigDecimal bd1 = new BigDecimal(craft.getUnitWidth().toString());
            BigDecimal a = new BigDecimal(0);
            BigDecimal b = new BigDecimal(0);
            if (craft.getPnlLength() != null) {
                a = new BigDecimal(craft.getPnlLength().toString());
            }
            if (craft.getPnlWidth() != null) {
                b = new BigDecimal(craft.getPnlWidth().toString());
            }
            map.put("pcsSize", FmtUtils.holdDecimal(bd, 2) + " * " + FmtUtils.holdDecimal(bd1, 2));

            if (craft.getPnlDivisor() != null && a != null && b != null) {
                map.put("setSize",
                        FmtUtils.holdDecimal(a, 2) + " * " + FmtUtils.holdDecimal(b, 2) + " /" + craft.getPnlDivisor());
            } else {
                if (a != null && b != null) {
                    map.put("setSize", FmtUtils.holdDecimal(a, 2) + " * " + FmtUtils.holdDecimal(b, 2));
                } else {
                    map.put("setSize", "");
                }

            }

            map.put("contractDetail.craft.no", FmtUtils.blank(craft.getNo()));
        }

        if (notification.getPdtReportFlag() != null && notification.getPdtReportFlag().equals("1")) {
            map.put("pdtReportFlag", FmtUtils.blank("成品分析报告"));
        }

        if (notification.getSmpCfmFlag() != null && notification.getSmpCfmFlag().equals("1")) {
            map.put("smpCfmFlag", FmtUtils.blank("样品确认书"));
        }

        if (notification.getMetalAnalyzeFlag() != null && notification.getMetalAnalyzeFlag().equals("1")) {
            map.put("metalAnalyzeFlag", FmtUtils.blank("金相切片及分析报告"));
        }
        if (notification.getOtherReport() != null && !notification.getOtherReport().equals("1")) {
            map.put("othersFlag", FmtUtils.blank("其他"));
        }
        if (StringUtils.isNotBlank(notification.getRemark())) {
            remark = remark + notification.getRemark();
        }
        if (remark != null && !remark.equals("")) {
            int length = remark.length();
            if (length <= 53) {
                map.put("remark0", remark);
            } else {
                map.put("remark0", remark.substring(0, 53));
            }
            if (length > 53) {
                if (length <= 111) {
                    map.put("remark1", remark.substring(53, length));
                } else {
                    map.put("remark1", remark.substring(53, 111));
                }
            }
            if (length > 111) {
                if (length <= 169) {
                    map.put("remark2", remark.substring(111, length));
                } else {
                    map.put("remark2", remark.substring(111, 169));
                }
            }
            if (remark.length() > 169) {
                map.put("remark3", remark.substring(169, remark.length()));
            }
        } else {
            map.put("remark0", FmtUtils.blank(remark));
        }
        String companyId = UserUtils.getUser().getCompany().getRecordId();
        if (notification.getUrgentFlag() != null && notification.getUrgentFlag().equals(TypeKey.URGENTFLAGYES)) {
            if (companyId.equals(ConstKey.COMPANY_JMH)) {
                pdf.setFileName(ConstKey.DEFAULT_TEMPLATE_PATH + "urgentNotification_jmh.pdf");
            } else {
                pdf.setFileName(ConstKey.DEFAULT_TEMPLATE_PATH + "urgentNotification.pdf");
            }
        } else {
            if (companyId.equals(ConstKey.COMPANY_JMH)) {
                pdf.setFileName(ConstKey.DEFAULT_TEMPLATE_PATH + "notification_jmh.pdf");
            } else {
                pdf.setFileName(ConstKey.DEFAULT_TEMPLATE_PATH + "notification.pdf");
            }
        }
        pdf.setDataMap(map);

        return pdf;

    }

    /**
     * 大数据查询投料单中通知单的支持方法
     *
     * @param notification
     * @return
     */
    public List<Notification> selectOfFeedShow(Notification notification) {
        return notificationDao.selectOfFeedShow(notification);
    }

    public List<Notification> selectOfFedShow(Notification notification) {
        return notificationDao.selectOfFedShow(notification);
    }

    public Integer findHeOaQtys(Notification n) {
        Integer allOaQty = notificationDao.findBeHeNotSumByNo(n);
        allOaQty = allOaQty == null ? 0 : allOaQty;
        return allOaQty;
    }

    public List<Notification> selectOfFeed() {
        Notification notification = new Notification();
        notification.setStatus(TypeKey.SL_NOTIFICATION_STATUS_CONFIRMED.toString());
        notification.setStatus1(TypeKey.SL_NOTIFICATION_STATUS_SUPPLEMENTARYFOOD.toString());
        notification.setStatus2(TypeKey.SL_NOTIFICATION_STATUS_FEED.toString());
        notification.setStatus3(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
        CardA carda = new CardA();
        carda.setStatus(TypeKey.EG_PROCESSCARD_STATUS_CONFIRMED);
        notification.setCardA(carda);// 这里在属性carda中保存了让通知单对应的流程卡状态
        notification.setCompany(UserUtils.getUser().getCompany());
        return notificationDao.selectOfFeed(notification);
    }

    /**
     * 外部接口（添加合同选择通知单的数据）
     *
     * @return
     */
    public List<ContractCraft> selectNotification(ContractCraft craft) {
        List<ContractCraft> list1 = Lists.newArrayList();
        if (null != craft.getCompany() && (ConstKey.COMPANY_SZLDH.equals(craft.getCompany().getRecordId())
                || ConstKey.COMPANY_JXLDH.equals(craft.getCompany().getRecordId()))) {
            list1 = contractCraftDao.getContractCraftList(craft);
        } else {
            list1 = notificationDao.selectNotification(craft);
        }
        List<ContractCraft> list2 = notificationDao.selectNotification2(craft);
        return Collections3.union(list1, list2);
    }

    /**
     * 校验本公司下的通知单的生产编号是否有重复的
     *
     * @param notification
     * @return
     */
    public Integer selectcraftNo(Notification notification) {
        return notificationDao.selectcraftNo(notification);
    }

    /**
     * WC 2017-09-06 整理代码 标识投料单状态为已完成
     */
    public void updateFeedingStatus(Notification notificationes) {
        Feeding feeding = new Feeding();
        feeding.setCompany(notificationes.getCompany());
        feeding.setStatus(TypeKey.PD_FEEDING_STATUS_FINISHED.toString());
        feeding.setNotification(notificationes);
        feedingDao.updateStatusByNotification(feeding);
    }

    /**
     * WC 2017-09-06 整理代码 标识通知单状态为已完成
     */
    public Notification updateNotificationStatus(Notification notification) {
        notification.preUpdate();
        notificationDao.updateNotificationStutasByNo(notification);
        return notificationDao.getNotification(notification);
    }

    /**
     * WIP列表点击【完成】： 修复数据漏洞（根据编号修改数据一定要加companyId）和封装简化代码 MOD: WC 2017-09-06
     */
    @Transactional(readOnly = false)
    public void updateNotificationStutasByNo(Notification notification) {
        Company company = UserUtils.getUser().getCompany();
        notification.setCompany(company);
        Notification notificationes = new Notification();
        if (notification.getNo().indexOf(",") >= 0) {
            // 修改和单的状态 和 修改投料单的状态
            updateFeedingStatus(updateNotificationStatus(notification));

            /*
             * ProduceBatch date = produceBatchDao.getByFeefingNo(feeding.getRecordId()); // 根据批次编号 去修改批次状态
             * date.setStatus(TypeKey.PRODUCE_BATCH_STATUS_FANISH.toString()); produceBatchDao.update(date);
             * produceBatchDetailDao.updateStatusByProduceBatchRecordId(date.getRecordId());
             */

            String nos[] = notification.getNo().split(",");
            for (int i = 0; i < nos.length; i++) {
                // 单独修改通知单状态
                notification.setNo(nos[i]);
                updateNotificationStatus(notification);
            }
        } else {
            Notification notif = notificationDao.getNotificationByNo(notification);
            Integer count = feedingDao.selectFeedCountByNotificationId(notif);

            // 修改通知单的状态： 先查询通知单 是不是只是一个投料单 是才可修改
            notificationes =
                    count > 1 ? notificationDao.getNotification(notification) : updateNotificationStatus(notification);

            // 修改通知单下所有的投料单状态为已完成
            updateFeedingStatus(notificationes);

            /*
             * 再去修改传过来的wip的 投料的状态 和批次 和批次明细的状态 ProduceBatch date =
             * produceBatchDao.getByFeefingNo(feeding.getRecordId()); // 根据批次编号 去修改批次状态
             * date.setStatus(TypeKey.PRODUCE_BATCH_STATUS_FANISH.toString()); produceBatchDao.update(date);
             * produceBatchDetailDao.updateStatusByProduceBatchRecordId(date.getRecordId());
             */
        }
    }

    /**
     * 通过合同明细编号去查询通知单中是否有本明细的count 数
     *
     * @return
     */
    public Integer findContractDetailOnNotification(ContractDetail contractDetail) {
        return notificationDao.findContractDetailOnNotification(contractDetail);
    }

    /**
     * 通过合同明细编号去查询通知单中是否有确认过的
     *
     * @param contractDetail
     * @return
     */
    public Integer findContractDetailOnNotificationByContractDetailRecordId(ContractDetail contractDetail) {
        return notificationDao.findContractDetailOnNotificationByContractDetailRecordId(contractDetail);
    }

    public List<Notification> findExpList(Notification notification) {
        List<Notification> result = notificationDao.findExpList(notification);
        if (Collections3.isEmpty(result)) {
            return result;
        }
        String notiIds = null;
        for (Notification nf : result) {
            if (StringUtils.isNotBlank(nf.getFactNotiId())) {
                if (StringUtils.isNotBlank(notiIds)) {
                    notiIds = notiIds + "," + nf.getFactNotiId();
                } else {
                    notiIds = nf.getFactNotiId();
                }
            }
        }
        String factId = CompanyUtil.getInstance().getFactId();
        List<Notification> cardDataList = notificationDao.getCardData(factId, notiIds);

        result.forEach(r -> {
            if (Collections3.isNotEmpty(cardDataList) && StringUtils.isNotBlank(r.getFactNotiId())) {
                for (Notification cardData : cardDataList) {
                    if (cardData.getRecordId().equals(r.getFactNotiId())) {
                        if (StringUtils.isBlank(r.getSetSize())) {
                            r.setSetSize(cardData.getSetSize());
                        }
                        if (StringUtils.isNotBlank(cardData.getProcessCraftId())) {
                            switch (cardData.getProcessCraftId()) {
                                // 测试方式
                                case "72":
                                    r.setProcessCraftValueOne(cardData.getProcessCraftValue());
                                    break;
                                // 油墨种类
                                case "195":
                                    r.setProcessCraftValueTwo(cardData.getProcessCraftValue());
                                    break;
                                // 啤(锣)方式
                                case "61":
                                    r.setProcessCraftValueThree(cardData.getProcessCraftValue());
                                    break;
                            }
                        }
                    }
                }
            }
        });
        return result;
    }

    /**
     * 查询成品
     *
     * @param material
     * @return
     */
    public List<Material> selectmaterial(Material material) {
        return materialDao.selectmaterial(material);
    }

    /**
     * 查询生产数据
     *
     * @return
     */
    public List<Notification> selectOfFeedDataShow(Notification notification) {

        return notificationDao.selectOfFeedDataShow(notification);
    }

    /**
     * 生产进度中 查找通知单
     *
     * @return
     */
    public List<Notification> selectOfFeedData(Notification notification) {

        return notificationDao.selectOfFeedData(notification);

    }

    /**
     * 去工艺表中查询生产编号是否有重复的
     *
     * @param contractCraft
     * @return
     */
    public Integer findNotificationCraft(ContractCraft contractCraft) {

        return contractCraftDao.findNotificationCraft(contractCraft);
    }

    /**
     * 查询通知单中此编号是否被用了
     *
     * @param notification
     * @return
     */
    public Integer findNoisEnable(Notification notification) {
        return notificationDao.findNoisEnable(notification);
    }

    public boolean changeOrderType(String orderType) {
        List<DictValue> payWayList = DictUtils.getValuesByItem(DictItemEnum.ORDER_TYPE);
        boolean changeFlag = true;
        for (DictValue pw : payWayList) {
            // 选择(样品转量产，样品返单有改，生产返单有改,新单样品，新单量产),重新设置生产编号内容
            if (orderType.equals(pw.getRecordId())) {
                if (ConstKey.ORDER_TYPE_PRODUCTION_CHANGE.equals(pw.getValue())
                        || ConstKey.ORDER_TYPE_SAMPLE_PRODUCTION.equals(pw.getValue())
                        || ConstKey.ORDER_TYPE_SAMPLE_CHANGE.equals(pw.getValue())
                        || ConstKey.ORDER_TYPE_NEW_SAMPLE.equals(pw.getValue())
                        || ConstKey.ORDER_TYPE_NEW_PRODUCTION.equals(pw.getValue())) {
                    changeFlag = false;
                    break;
                }
            }
        }
        return changeFlag;
    }

    /**
     * 根据通知单编号 去查询是否被流程卡引用了
     *
     * @param recordId
     * @return
     */
    public String selectNotificationCardArecordId(String recordId) {
        return notificationDao.selectNotificationCardArecordId(recordId);
    }

    /**
     * 通过通知单编号 去get通知单
     *
     * @param notification
     * @return
     */
    public Notification getNotificationByRecordId(Notification notification) {
        notification.setCompany(UserUtils.getUser().getCompany());
        return notificationDao.getNotification(notification);
    }

    public Integer selectNotificationCountByContractDeId(ContractDetail contractDetail) {
        return notificationDao.selectNotificationCountByContractDeId(contractDetail);
    }

    public List<Notification> selectBeforeNotificationAndCardA(ContractDetail contractDetail) {
        contractDetail.setCompany(UserUtils.getUser().getCompany());
        return notificationDao.selectBeforeNotificationAndCardA(contractDetail);
    }

    public void updateCost(String recordId, BigDecimal cost) {
        notificationDao.updateCost(recordId, cost);

    }

    public Notification getByNo(String companyId, String no, String craftNo) {
        return notificationDao.getByNo(companyId, no, craftNo);
    }

    public void execMergedId() {
        List<Notification> objs = notificationDao.findMergeInfo();
        for (Notification notification : objs) {
            // 修改本通知单的mergeId
            notificationDao.updateMergeId(notification);
        }

    }

    public String judgmentContractDetail(String no) {
        Notification notification = new Notification();
        notification.setCompany(UserUtils.getUser().getCompany());
        String[] nos = no.split(",");
        boolean result = false;
        if (nos.length > 1) {
            // 合单
            for (int i = 0; i < nos.length; i++) {
                notification.setNo(nos[i]);
                result = checkFeeding(notification);
                if (result) {
                    break;
                }
            }
        } else {
            // 不是合单
            notification.setNo(nos[0]);
            result = checkFeeding(notification);
        }
        if (result) {
            return "亲，您的订单数量已经完成，不能继续投补料！";
        }
        return null;
    }

    /**
     * WC 2017-02-24 判断是否可以继续投料
     */
    public boolean checkFeeding(Notification notification) {
        // 判断合同明细开始寄存
        ContractDetail conDtl = notificationDao.getStatusAndDeposit(notification);
        String status = conDtl.getStatus();

        if ((!conDtl.getDeposit()) && status.equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())) {
            // 无寄存
            return true;
        } else if (conDtl.getDeposit() && status.equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())) {
            // 開始寄存
            if (checkDepositConDtlQty(conDtl)) {
                return true;
            }
        }
        return false;
    }

    /**
     * WC 2017-02-13 判断已经寄存的合同明细生产入库数量 + 占可用库存数量+ 成品采购数量
     * 是否超过订单数量(本等同于非寄存的'已送货'条件,因为非寄存更改合同明细为已送货状态条件是送货数量订单数量,而订单数量的来源就是生产数量+占可用库存数量+成品采购数量)
     */
    public boolean checkDepositConDtlQty(ContractDetail conDtl) {
        // WC 2017-04-20 是否可以继续投补料条件 ：该明细下所有通知单生产入库数量 + 占可用库存数量 + 成品采购入库数量是否达到订单数量
        // 订单数量
        if (conDtl.getCompany() == null) {
            conDtl.setCompany(UserUtils.getUser().getCompany());
        }
        Integer orderQty = Integer.parseInt(conDtl.getQuantity());
        conDtl.setCompany(UserUtils.getUser().getCompany());
        Notification no = notificationDao.findNotificationByConDtlId(conDtl);
        if (no == null) {
            return false;
        }
        // 所有通知单生产入库数量
        Integer completedQty = no.getCompletedQty() == null ? 0 : no.getCompletedQty();

        // 所有通知单占可用库存数量总和
        Integer occupiedAvailableQty = no.getOccupiedAvailableQty() == null ? 0 : no.getOccupiedAvailableQty();

        // 查询采购入库数量
        Integer purchaingInQty = productStoreDao.getPurchasingInQtyByConDetId(conDtl.getCompany().getRecordId(),
                TypeKey.ST_PRODUCT_INOUTTYPE_PURCHASING_IN.toString(),
                conDtl.getRecordId());
        purchaingInQty = purchaingInQty == null ? 0 : purchaingInQty;

        // 查询采购补货入库数量
        Integer purchaingBuGoodsQty = productStoreDao.getReturnOrBuGoodsQtyByConDetId(conDtl.getCompany().getRecordId(),
                TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_ENTER.toString(),
                ConstKey.PA_PRDPRODUCT_FLAG,
                TypeKey.ST_REJECTAPPLICATION_TREATMENT_REDELIVER.toString(),
                conDtl.getRecordId());
        purchaingBuGoodsQty = purchaingBuGoodsQty == null ? 0 : purchaingBuGoodsQty;

        // 查询采购退货出库数量
        Integer purchaingReturnGoodsQty =
                productStoreDao.getReturnOrBuGoodsQtyByConDetId(conDtl.getCompany().getRecordId(),
                        TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_OUT.toString(),
                        ConstKey.PA_PRDPRODUCT_FLAG,
                        TypeKey.ST_REJECTAPPLICATION_TREATMENT_RETURN.toString(),
                        conDtl.getRecordId());
        purchaingReturnGoodsQty = purchaingReturnGoodsQty == null ? 0 : purchaingReturnGoodsQty;

        // 成品采购数量 = 采购入库数量 + 采购补货入库数量 － 采购退货出库数量
        Integer purchingQty = purchaingInQty + purchaingBuGoodsQty - purchaingReturnGoodsQty;
        // 判断明细是否达到订单数量
        if (completedQty + occupiedAvailableQty + purchingQty >= orderQty) {
            return true;
        }
        return false;
    }

    /**
     * fzd 2017-03-15 根据通知单编号获取客户
     */
    public Customer getCustomerByNotifi(Notification notification) {
        return notificationDao.getCustomerByNotifi(notification);
    }

    /**
     * fzd 2017-03-28 根据编号获取消息所需内容
     */
    public Notification getMsgByNo(Notification notification) {
        notification.setCompany(UserUtils.getUser().getCompany());
        return notificationDao.getMsgByNo(notification);
    }

    /**
     * WC 2017-04-26 查询出未达订单数量的寄存合同
     */
    public List<Contract> findContractOfNotificationHadDeposit(Contract contract) {
        contract.setStatus(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString()); // 已送货的合同下可能有未完成订单数量的寄存明细
        List<ContractDetail> conDetList = contractDao.findContractDetailsOfNotificationHadDeposit(contract);
        List<ContractDetail> cdList = Lists.newArrayList();
        List<Contract> result = Lists.newArrayList();
        if (Collections3.isNotEmpty(conDetList)) {
            for (ContractDetail cd : conDetList) {
                if (!checkDepositConDtlQty(cd)) {
                    cdList.add(cd);
                }
            }
        }
        if (Collections3.isNotEmpty(cdList)) {
            result =
                    contractDao.findContractOfNotificationHadDeposit(cdList, contract.getPageNo(), contract.getPageSize());
        }
        return result;
    }

    /**
     * WC 2014-04-26 根据合同编号去查询所有合同详情
     */
    public List<ContractDetail> findContractDetailAll(Contract contract) {
        List<ContractDetail> result1 = contractDao.findContractDetailAll(contract);
        if (StringUtils.isNotBlank(contract.getStatus())
                && contract.getStatus().equals(TypeKey.SL_CONTRACT_STATUS_DELIVERYCOMPLETED.toString())) {
            // 合同是已送货 筛选出是寄存且未达到订单数量的明细，可再次添加通知单
            List<ContractDetail> result2 = Lists.newArrayList();
            if (Collections3.isNotEmpty(result1)) {
                for (ContractDetail cd : result1) {
                    if (cd.getDeposit() != null && cd.getDeposit() && !checkDepositConDtlQty(cd)) {
                        result2.add(cd);
                    }
                }
            }
            return result2;
        }
        return result1;
    }

    /**
     * ycy 2017-05-02 修改加急标志
     */
    @Transactional(readOnly = false)
    public void updateUrgentFlag(Notification notification) {
        if (notification.getUrgentFlag() != null && notification.getUrgentFlag().equals("true")) {
            // 加急
            notification.setUrgentFlag(TypeKey.ACTIVE.toString());
        } else {
            notification.setUrgentFlag(TypeKey.INACTIVE.toString());
        }
        notificationDao.updateUrgentFlag(notification);

        // 查询和单的通知单
        List<Notification> notiList = notificationDao.getHeNotificationList(notification);
        if (Collections3.isNotEmpty(notiList)) {
            for (Notification noti : notiList) {
                String urgentFlag = TypeKey.INACTIVE.toString();
                List<String> notiNos = Arrays.asList(noti.getNo().split(","));// 将通知单关联mi
                if (Collections3.isNotEmpty(notiNos)) {
                    List<Notification> ntList =
                            notificationDao.findNotificationsByNos(notiNos, noti.getCompany().getRecordId());
                    if (Collections3.isNotEmpty(ntList)) {
                        for (Notification n : ntList) {
                            if (TypeKey.ACTIVE.toString().equals(n.getUrgentFlag())) {
                                urgentFlag = TypeKey.ACTIVE.toString();
                                break;
                            }
                        }
                    }
                }
                noti.setUrgentFlag(urgentFlag);
                notificationDao.updateUrgentFlag(noti);
            }
        }
    }

    /**
     * fzd 2017-06-23 根据通知单编号获取通知单
     */
    public Notification getNotifi(String no) {
        Notification notification = new Notification();
        notification.setNo(no);
        notification.setCompany(UserUtils.getUser().getCompany());
        return notificationDao.getNotifi(notification);
    }

    /**
     * fzd 2017-06-23 根据通知单编号获取合同
     */
    public Contract getContract(String no) {
        Notification notification = new Notification();
        notification.setNo(no);
        notification.setCompany(UserUtils.getUser().getCompany());
        return notificationDao.getContract(notification);
    }

    // zjn 2017-09-14 返回成品的物料类型
    public DictValue returnMaterialType() {
        DictValue materialType = new DictValue();
        List<DictValue> dictValueList = DictUtils.getValuesByItem(DictItemEnum.MATERIAL_TYPE_STORE);
        for (DictValue dv : dictValueList) {
            if (StringUtils.isNotBlank(dv.getValue()) && dv.getValue().equals("成品")) {
                materialType = dv;
            }
        }
        return materialType;
    }

    /**
     * fzd 2017-12-27 获取某个客户的所有生产编号报价
     */
    public List<ContractCraft> getCraftPriceList(ContractCraft craft) {
        return notificationDao.getCraftPriceList(craft);
    }

    /**
     * WC 2018-01-24 查询工程卡是否存在投料单，只要有投料单都不可自动合单
     */
    public boolean getFeedingCountByCard(CardA cardA) {
        return cardADao.getFeedingCountByCardOne(cardA) > 0;
    }

    /**
     * TJ 2018-06-09
     *
     * @param detail
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Transactional(readOnly = false)
    public Map craftNoList(ContractDetail detail) {
        Map result = new HashMap<>();
        Company company = UserUtils.getUser().getCompany();
        result.put("result", "success");
        List<ContractCraft> craftList = new ArrayList<>();
        if (detail.getContractCraftList() != null) {
            detail.getContractCraftList().setCompany(company);
            detail.setCompany(company);
            detail.getContractCraftList().setNo(null);
            if (StringUtils.isNotBlank(detail.getOldCraftId())) {
                detail.getContractCraftList().setRecordId(detail.getOldCraftId());
                craftList = contractCraftDao.craftCraftList(detail.getContractCraftList());
            }
            if (craftList == null || craftList.size() == 0) {
                detail.getContractCraftList().setRecordId(null);
                craftList = contractCraftDao.craftCraftList(detail.getContractCraftList());
                if (craftList.size() > 1) {
                    if (detail.getContractCraftList().getCustomer() != null
                            && StringUtils.isNotBlank(detail.getContractCraftList().getCustomer().getRecordId())) {
                        for (int i = 0; i < craftList.size(); i++) {
                            if (craftList.get(i)
                                    .getCustomerId()
                                    .equals(detail.getContractCraftList().getCustomer().getRecordId())) {
                                ContractCraft craft = craftList.get(i);
                                craftList.add(0, craft);
                                craftList.remove(i + 1);
                            }
                        }
                    }
                }
                if (StringUtils.isNotBlank(detail.getOldCraftId())) {
                    result.put("result", "fail");
                }
            }
        }
        result.put("craftList", craftList);
        return result;
    }

    public BigDecimal getMaterialStocks(MaterialCard materialResult) {
        Company company = UserUtils.getUser().getCompany();
        // 拿取最终的销售公司
        String branchId = null;
        if (null != materialResult && StringUtils.isNotBlank(materialResult.getSaleCompanyId())) {
            // 根据销售公司获取子公司
            Branch branch = new Branch();
            branch.setCompany(company);
            branch.setBindErpComId(materialResult.getSaleCompanyId());
            branchId = notificationDao.getBranchIdByErpId(branch);
        }
        // 根据子公司和物料id获取库位，拿取库位库存，加到物料的库存上面
        RawStockUtil rawStockUtil = new RawStockUtil();
        BigDecimal stockPlaceNum = rawStockUtil.getStockPlaceComStocks(branchId, materialResult.getMaterialId());
        return stockPlaceNum;
    }

    public Map<String, Object> getCustList(Company company) {
        Map<String, Object> map = new HashMap<String, Object>();
        if (StringUtils.isBlank(company.getSaleId())) {
            return null;
        }
        Company com = UserUtils.getUser().getCompany();
        // 查询当日下的订单，以及该订单关联的生产日期的最大产能
        if (StringUtils.isBlank(company.getDate())) {
            company.setDate(DateUtils.getDate());
        } else {
            if (company.getOueDays() != null && company.getOueDays() > 0) {
                company.setDate(DateUtils.formatDate(DateUtils.dateAdd(new Date(), company.getOueDays())));
            }
        }
        company.setRecordId(com.getRecordId());
        List<Notification> custList = notificationDao.getNotifiCustomerArea(company);
        // 获取每个订单上的产能占用表，并判断是否满足，不满足改掉状态
        for (int i = 0; i < custList.size(); i++) {
            // 设置每个订单的客户信息
            String value = "";
            String remark = "";
            String shortName = "";
            Integer customerGradeNum = null;
            Integer logisticsTime = null;
            BigDecimal capacityPrecent = null;
            if (StringUtils.isNotBlank(custList.get(i).getThreeShortName())) {
                value = custList.get(i).getThreeValue();
                remark = custList.get(i).getThreeRemark();
                shortName = custList.get(i).getThreeShortName();
                customerGradeNum = custList.get(i).getThreeCustomerGradeNum();
                logisticsTime = custList.get(i).getThreeLogisticsTime();
                capacityPrecent = custList.get(i).getThreeCapacityPrecent();
            } else if (StringUtils.isNotBlank(custList.get(i).getTwoShortName())) {
                value = custList.get(i).getTwoValue();
                remark = custList.get(i).getTwoRemark();
                shortName = custList.get(i).getTwoShortName();
                customerGradeNum = custList.get(i).getTwoCustomerGradeNum();
                logisticsTime = custList.get(i).getTwoLogisticsTime();
                capacityPrecent = custList.get(i).getTwoCapacityPrecent();
            } else if (StringUtils.isNotBlank(custList.get(i).getOneShortName())) {
                value = custList.get(i).getOneValue();
                remark = custList.get(i).getOneRemark();
                shortName = custList.get(i).getOneShortName();
                customerGradeNum = custList.get(i).getOneCustomerGradeNum();
                logisticsTime = custList.get(i).getOneLogisticsTime();
                capacityPrecent = custList.get(i).getOneCapacityPrecent();
            }
            custList.get(i).setValue(value);
            custList.get(i).setRemark(remark);
            custList.get(i).setShortName(shortName);
            custList.get(i).setCustomerGradeNum(customerGradeNum);
            custList.get(i).setLogisticsTime(logisticsTime);
            custList.get(i).setCapacityPrecent(capacityPrecent);

            custList.get(i).setCapaUseStatus(null);
            // 加载该订单的产能占用表
            custList.get(i).setCompany(company);
            List<NotificationMonyDeail> notiList = notificationDao.getMonyDeailListByNotifi(custList.get(i));
            // 如果占用产能和订单面积不匹配，标记状态表明生产安排有问题
            BigDecimal needArea = new BigDecimal(0);
            for (int j = 0; j < notiList.size(); j++) {
                if (notiList.get(j).getNeedArea() != null
                        && notiList.get(j).getNeedArea().compareTo(new BigDecimal(0)) == 1) {
                    needArea = needArea.add(notiList.get(j).getNeedArea());
                }
            }
            if (needArea.compareTo(new BigDecimal(0)) == 1) {
                custList.get(i).setNeedArea(needArea);
            }
        }
        map.put("custList", custList);
        // 重新加载该销售公司总产能
        BigDecimal allCapa = new BigDecimal(0);
        ParameterSet param = new ParameterSet();
        param.setCompany(new Company(com.getRecordId()));
        param.setJianPin("allCapacity");
        List<ParameterSet> params = erpCraftDao.findList(param);
        if (params.size() > 0 && StringUtils.isNotBlank(params.get(0).getParameterValue())) {
            allCapa = new BigDecimal(params.get(0).getParameterValue());
        }

        BigDecimal result = BigDecimal.ZERO;
        company.setCustomerId(company.getSaleId());
        if (StringUtils.isNotBlank(company.getDate())) {
            List<ProductionCapacityAllocation> list = new ArrayList<ProductionCapacityAllocation>();
            // 如果时间不等于当天，使用默认
            if (company.getDate().equals(DateUtils.getDate())) {
                list = erpCraftDao.getProCapacityList(company);
            } else {
                list = erpCraftDao.getProCapacityCopyList(company);
            }
            if (list.size() > 0 && list.get(0).getCapacityPercent() > 0) {
                BigDecimal precent = new BigDecimal(list.get(0).getCapacityPercent());
                if (allCapa.compareTo(new BigDecimal(0)) == 1) {
                    // 计算给当前公司的产能
                    result =
                            precent.multiply(allCapa).divide(new BigDecimal(100)).setScale(4, BigDecimal.ROUND_HALF_UP);
                }
            }
        }
        if (result != null) {
            map.put("result", result);
        } else {
            map.put("result", 0);
        }
        return map;
    }

    /**
     * 报价查询已确认通知单的厂编
     */
    public List<ContractCraft> selectNotifiList(ContractCraft craft) {
        // 正常的厂编
        List<ContractCraft> listA = notificationDao.selectNotifiListA(craft);
        // 调整的厂编
        List<ContractCraft> listB = notificationDao.selectNotifiListB(craft);
        // 合并
        return Collections3.union(listA, listB);
    }

    /** zjn 2019-10-18 获取已确认后的通知单列表 */
    public List<Notification> getNotificationList(Notification notification, String no, String start) {
        if (null != notification) {
            if (notification.getQueryAll() != null && !notification.getQueryAll()) {
                notification.setCreatedBy(UserUtils.getUser());
            }
        }
        notification.setNo(no);
        notification.setPageNo(start);
        notification.setPageSize(ConstKey.Max_SHOW_SIZE);
        return notificationDao.getNotifications(notification);
    }

    /** zjn 2019-10-31 获取订单流程数据 */
    public List<OrderProcedureVo> getOrderProcedureList(Notification notification) {
        return orderProcedureVoDao.getOrderProcedureData(notification);
    }

    public Page<NotificationScheduling> getDatalist(Page<NotificationScheduling> page,
                                                    NotificationScheduling notificationScheduling) {
        notificationScheduling.setPage(page);
        List<NotificationScheduling> list = notificationSchedulingDao.findList(notificationScheduling);
        for (NotificationScheduling scheduling : list) {
            if (StringUtils.isNotBlank(scheduling.getUnitLength()) && StringUtils.isNotBlank(scheduling.getUnitWidth())) {
                scheduling.setReferenceType(notificationSchedulingDao.findMaterialNum(scheduling));
            }
        }
        page.setList(list);
        return page;
    }

    // 调整安排时间
    @Transactional(readOnly = false)
    public Map<String, String> adjustArrangePlanDate(NotificationScheduling notificationScheduling) {
        Map<String, String> data = new HashMap<>();

        // 验证当前状态是非未制作
        NotificationScheduling ns = notificationSchedulingDao.get(notificationScheduling);
        if (null != ns) {
            if (ns.getStatus().equals(TypeKey.NOTIFICATION_SCHEDULING_MIDDLE)) {
                data.put("result", "fail");
                data.put("message", "订单在制作中,不能进行调整");
                return data;
            } else if (ns.getStatus().equals(TypeKey.NOTIFICATION_SCHEDULING_END)) {
                data.put("result", "fail");
                data.put("message", "订单在已完成,不能进行调整");
                return data;
            }
        }

        notificationScheduling.setCompany(UserUtils.getUser().getCompany());
        Date arrangePlanDate = notificationScheduling.getArrangePlanDate();
        Date afterArrangePlanDate = notificationScheduling.getAfterArrangePlanDate();
        ParameterSet ps = new ParameterSet();
        ps.setCompany(notificationScheduling.getCompany());
        ps.setJianPin("maximumNewOrderDay");
        ParameterSet parameterSet = parameterSetDao.getParameterSetByCompanyAndjianPin(ps);
        if (null != parameterSet && "1".equals(parameterSet.getStart())) {
            Integer qtyNum = StringUtils.isBlank(parameterSet.getParameterValue()) ? 0
                    : Integer.valueOf(parameterSet.getParameterValue());
            NotificationScheduling notiSchedul = new NotificationScheduling();
            notiSchedul.setRecordId(notificationScheduling.getRecordId());
            notiSchedul.setArrangePlanDate(afterArrangePlanDate);

            // 如果调整后的日期超过参数设置的最大数量
            Integer afterCount = notificationSchedulingDao.getNowMaximumNewOrderDay(notiSchedul);
            if (afterCount > qtyNum) {
                data.put("result", "fail");
                data.put("message", "调整后的订单数量已经达到您系统设置的最大数量，不能继续调整!");
                return data;
            }
        }

        NotificationScheduling notiSchedul = new NotificationScheduling();
        notiSchedul.setRecordId(notificationScheduling.getRecordId());
        notiSchedul.setArrangePlanDate(afterArrangePlanDate);
        notificationSchedulingDao.updateArrangePlanDate(notiSchedul);

        // 插单记录
        NotiSchedulRecord notiSchedulRecord = new NotiSchedulRecord();
        notiSchedulRecord.setCompany(notificationScheduling.getCompany());
        notiSchedulRecord.setNotiSchedul(notiSchedul);
        notiSchedulRecord.setAdjustFirstDate(arrangePlanDate);
        notiSchedulRecord.setAdjustAfterDate(afterArrangePlanDate);
        notiSchedulRecord.setRemark(notificationScheduling.getRemark());
        notiSchedulRecord.preInsert();
        notiSchedulRecordDao.insert(notiSchedulRecord);

        data.put("result", "success");
        data.put("message", "调整安排时间成功!");
        return data;
    }

    public Map<String, String> updateFinshStatus(Notification notification) {
        Map<String, String> data = new HashMap<>();
        notification.setCompany(UserUtils.getUser().getCompany());
        String status = produceBatchDetailDao.getpbdStatusByNotiId(notification);
        if (StringUtils.isBlank(status)) {
            data.put("result", "fail");
            data.put("message", "通知单" + notification.getNo() + "还未投料");
            return data;
        }
        if (status.contains(TypeKey.PRODUCE_BATCH_STATUS_INIT.toString().toString())) {
            data.put("result", "fail");
            data.put("message", "通知单" + notification.getNo() + "还未过数完");
            return data;
        } else if (status.contains(TypeKey.PRODUCE_BATCH_STATUS_OPERATION.toString())) {
            data.put("result", "fail");
            data.put("message", "通知单" + notification.getNo() + "还未过生产入库完");
            return data;
        }

        // 更新通知单状态
        notification.setStatus(TypeKey.SL_NOTIFICATION_STATUS_COMPLETED.toString());
        dao.updateStatusById(notification);

        if (StringUtils.isNotBlank(notification.getMaterialId())) {
            // 更新物料总库存(总库存 - 占用库存)
            Material ma = materialDao.get(notification.getMaterialId());
            BigDecimal occupiedStock = null == notification.getOccupiedStock() ? BigDecimal.ZERO
                    : new BigDecimal(notification.getOccupiedStock());
            ma.setStocks(ma.getStocks().subtract(occupiedStock));
            materialDao.updateStocks(ma);
        }

        // 更新通知单占用库存
        notification.setOccupiedStock(null);
        notificationDao.updateOccupiedStock(notification);

        // 更新批次明细完成入库时间
        List<ProduceBatchDetail> pbdList = produceBatchDetailDao.getPbdListByNotiId(notification);
        if (Collections3.isNotEmpty(pbdList)) {
            Date nowDate = new Date();
            for (ProduceBatchDetail pbd : pbdList) {
                pbd.setFinshDate(nowDate);
                produceBatchDetailDao.updateFinshDate(pbd);
            }
        }

        data.put("result", "success");
        data.put("message", "通知单完工成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public String saveMaterialShowMsg(NotificationMaterial notificationMaterial) {
        notificationMaterial.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        if (StringUtils.isNotBlank(notificationMaterial.getRecordId())) {
            // 查询库存
            Integer stocks = notificationDao.getNotifiMaterialStocks(notificationMaterial);
            if (notificationMaterial.getUseStocks() == null) {
                notificationMaterial.setUseStocks(0);
            }
            if (stocks == null) {
                stocks = 0;
            }
            Integer occStocks = notificationMaterial.getUseStocks() - stocks;
            // 占用或者释放可用库存
            if (StringUtils.isNotBlank(notificationMaterial.getMaterialId())) {
                Material material = new Material(notificationMaterial.getMaterialId());
                material.setOccStocks(occStocks);
                materialDao.updateMaterialOccStock(material);
            }
            notificationMaterial.preUpdate();
            notificationDao.updateNotifiMaterial(notificationMaterial);
        } else {
            // 占用可用库存
            if (StringUtils.isNotBlank(notificationMaterial.getMaterialId())) {
                Material material = new Material(notificationMaterial.getMaterialId());
                if (notificationMaterial.getUseStocks() != null) {
                    material.setOccStocks(notificationMaterial.getUseStocks());
                    materialDao.updateMaterialOccStock(material);
                }
            }
            notificationMaterial.preInsert();
            notificationDao.insertNotifiMaterial(notificationMaterial);
        }
        return "success";
    }

    @Transactional(readOnly = false)
    public String delNotifiMaterial(NotificationMaterial notificationMaterial) {
        // 释放可用库存
        if (StringUtils.isNotBlank(notificationMaterial.getMaterialId())) {
            Material material = new Material(notificationMaterial.getMaterialId());
            if (notificationMaterial.getUseStocks() != null) {
                material.setOccStocks(-notificationMaterial.getUseStocks());
                materialDao.updateMaterialOccStock(material);
            }
            notificationDao.delNotifiMaterial(notificationMaterial);
            return "success";
        } else {
            return "fail";
        }
    }

    public List<NotificationMonyDeail> getMonyDeailList(List<NotificationMonyDeail> notifiMonyList) {
        Collections.sort(notifiMonyList, new Comparator<NotificationMonyDeail>() {
            @Override
            public int compare(NotificationMonyDeail o1, NotificationMonyDeail o2) {
                return o1.getDeailArea() < o2.getDeailArea() ? -1 : 1;
            }
        });
        Company company = UserUtils.getUser().getCompany();
        // 计算日期分配列表
        if (notifiMonyList.size() > 0) {
            for (int j = 0; j < notifiMonyList.size(); j++) {
                // 全部重置生产日期
                notifiMonyList.get(j).setProTime(null);
                notifiMonyList.get(j).setStatus(null);
            }
            int length = notifiMonyList.size() - 1;
            NotificationMonyDeail notificationMonyDeail = notifiMonyList.get(0);
            notificationMonyDeail.setCompany(company);
            Date firstTime = null;
            Date endTime = null;
            if (notificationMonyDeail.getFirstWorkTime() != null) {
                firstTime = notificationMonyDeail.getFirstWorkTime();
            }
            if (notificationMonyDeail.getEndWorkTime() != null) {
                if (length > 0) {
                    endTime = DateUtils.dateAdd(notificationMonyDeail.getEndWorkTime(), length);
                } else {
                    endTime = notificationMonyDeail.getEndWorkTime();
                }
            }
            List<Date> dateList = new ArrayList<Date>();
            String dates = "";
            if (firstTime != null && endTime != null) {
                int days = DateUtils.getDays(firstTime, endTime);
                for (int i = days; i >= 0; i--) {
                    // 获取计算日期
                    Date useDate = DateUtils.dateAdd(firstTime, i);
                    dateList.add(useDate);
                    if (StringUtils.isNotBlank(dates)) {
                        dates = dates + "," + DateUtils.getDateDays(useDate);
                    } else {
                        dates = DateUtils.getDateDays(useDate);
                    }
                }
            }
            BigDecimal allArea = notificationMonyDeail.getAllArea();
            notificationMonyDeail.setProTimeList(dates);
            List<NotificationMonyDeail> monyList = notificationDao.getNotificationMonyDeailSum(notificationMonyDeail);
            List<NotificationMonyDeail> monyApplyList = new ArrayList<NotificationMonyDeail>();
            for (int i = 0; i < dateList.size(); i++) {
                NotificationMonyDeail deail = new NotificationMonyDeail();
                for (int j = 0; j < monyList.size(); j++) {
                    if (monyList.get(j).getProTime() == dateList.get(i)) {
                        deail = monyList.get(j);
                        break;
                    }
                }
                if (deail.getProTime() == null) {
                    deail.setProTime(dateList.get(i));
                    deail.setSumArea(new BigDecimal(0));
                }
                monyApplyList.add(deail);
            }
            List<NotificationMonyDeail> monyInsertList = new ArrayList<NotificationMonyDeail>();
            for (int i = 0; i < monyApplyList.size(); i++) {
                // 如果当日分配面积未超过该客户上限
                if (allArea.compareTo(monyApplyList.get(i).getSumArea()) == 1) {
                    // 每个日期初始化产能
                    BigDecimal otherArea = allArea.subtract(monyApplyList.get(i).getSumArea());
                    monyApplyList.get(i).setOtherArea(otherArea);
                    for (int j = 0; j < notifiMonyList.size(); j++) {
                        // 如果该批次还未占用
                        if (StringUtils.isBlank(notifiMonyList.get(j).getStatus())) {
                            BigDecimal deailArea = new BigDecimal(notifiMonyList.get(j).getDeailArea());
                            // 如果剩余产能足够进行分配
                            if (monyApplyList.get(i).getOtherArea().compareTo(deailArea) != -1) {

                                // 减去剩余产能
                                otherArea = monyApplyList.get(i).getOtherArea().subtract(deailArea);
                                // 相对日期递减产能
                                monyApplyList.get(i).setOtherArea(otherArea);
                            } else {
                                BigDecimal needArea = deailArea.subtract(monyApplyList.get(i).getOtherArea());
                                monyApplyList.get(i).setNeedArea(needArea);
                                monyApplyList.get(i).setOtherArea(new BigDecimal(0));
                            }
                            notifiMonyList.get(j).setStatus("1");
                            notifiMonyList.get(j).setProTime(monyApplyList.get(i).getProTime());
                            notifiMonyList.get(j).setSortNum(i);
                            monyInsertList.add(notifiMonyList.get(j));
                            // 占用成功后进入下一次日期占用
                            break;
                        }
                    }
                }
            }
            // 如果存在未占用成功的批次重新循环多次占用，直到没有产能分配或者全部占用为止
            // Boolean flag = false;
            // while (monyInsertList.size() < notifiMonyList.size() && flag)
            // {
            // flag = false;
            // for (int i = 0; i < monyApplyList.size(); i++)
            // {
            // // 如果当日分配面积未超过该客户上限
            // if (allArea.compareTo(monyApplyList.get(i).getSumArea()) == 1)
            // {
            // for (int j = 0; j < notifiMonyList.size(); j++)
            // {
            // // 如果该批次还未占用
            // if (StringUtils.isBlank(notifiMonyList.get(j).getStatus()))
            // {
            // // 如果剩余产能足够进行分配
            // if (monyApplyList.get(i)
            // .getOtherArea()
            // .compareTo(notifiMonyList.get(j).getDeailArea()) != -1)
            // {
            // // 相对日期递减产能
            // BigDecimal otherArea = monyApplyList.get(i)
            // .getOtherArea()
            // .subtract(notifiMonyList.get(j).getDeailArea());
            // monyApplyList.get(i).setOtherArea(otherArea);
            // notifiMonyList.get(j).setStatus("1");
            // notifiMonyList.get(j).setProTime(monyApplyList.get(i).getProTime());
            // monyInsertList.add(notifiMonyList.get(j));
            // // 占用成功后进入下一次日期占用
            // flag = true;
            // break;
            // }
            // }
            // }
            // }
            // }
            // }
        }
        Collections.sort(notifiMonyList, new Comparator<NotificationMonyDeail>() {
            @Override
            public int compare(NotificationMonyDeail o1, NotificationMonyDeail o2) {
                return o1.getSortNum() > o2.getSortNum() ? -1 : 1;
            }
        });
        for (int i = 0; i < notifiMonyList.size(); i++) {
            notifiMonyList.get(i).setDeailId((i + 1) + "");
        }
        return notifiMonyList;
    }

    public List<NotificationMonyDeail> getMonyDeailListByNotifi(Notification notification) {
        notification.setCompany(UserUtils.getUser().getCompany());
        return notificationDao.getMonyDeailListByNotifi(notification);
    }

    public String updateProArea(Notification notification) {
        double d = Math
                .ceil(DateUtils.getDistanceOfTwoDate(notification.getDeliveryDate(), notification.getEstimateDateTwo()));
        if (d > 0) {
            notification.setEstimateRemark("不能生产，有" + d + "天的延期");
        } else {
            notification.setEstimateRemark("可以生产");
        }
        // 进行修改该通知单上的日产能
        notification.preUpdate();
        notificationDao.updateProArea(notification);
        // 删除产能占用，进行重新分配
        // 验证批次信息
        List<NotificationMonyDeail> notiList = getMonyDeailList(notification.getMonyDeailList());
        // for (int i = 0; i < notiList.size(); i++)
        // {
        // if (notiList.get(i).getProTime() == null)
        // {
        // // 日期为空代表未分配到生产日期，返回前端
        // result.put("message", "请确保每个订单批次有生产日期!");
        // result.put("result", "false");
        // return result;
        // }
        // }
        // 进行全部删除和批量插入
        NotificationMonyDeail deail = new NotificationMonyDeail();
        deail.setNoficationId(notification.getRecordId());
        notificationDao.delMonyDeailByNotification(deail);
        notification.setMonyDeailList(notiList);
        notification.preInsert();
        notificationDao.monyDeailInsert(notification);
        return notification.getEstimateRemark();
    }

    @Transactional(readOnly = false)
    public String updateNotiMaterialId(Notification notification) {
        Company company = UserUtils.getUser().getCompany();
        notification.setCompany(company);
        // 修改通知单的评估数据
        notificationDao.updateNotiMaterialId(notification);
        // 占用替换料库存
        notificationDao.delNotifiMaterialTwo(notification);
        if (notification.getNotifiMaterList() != null && notification.getNotifiMaterList().size() > 0) {
            notification.setCompany(company);
            notificationDao.insertNotifiMaterialTwo(notification);
        }

        // 查询先前备料
        PurchRaw raw = purchRawDao.getPurRawByNotifiId(notification);
        if (raw != null && StringUtils.isNotBlank(raw.getMaterialId()) && raw.getQuantity() != null
                && raw.getMaterialId().equals(notification.getMaterialId()) && notification.getReplyStock() != null
                && raw.getQuantity().compareTo(new BigDecimal(notification.getReplyStock())) == 0 ) {
            return "success";
        }
        updateEstStock(notification);
        // 删除以前该通知单的备料
        purchRawDao.delPurchRawByNoticeId(notification);
        // 判断通知单是否要对产品进行备料
        if (notification.getReplyStock() != null && notification.getReplyStock() > 0) {
            // 生成原料申请单
            PurchRaw purchRow = new PurchRaw();
            Material material = new Material(notification.getMaterialId());
            String no = CommonUtils.geDocumentNo("22");
            purchRow.setCompany(company);
            purchRow.setPurchApplyId(no);
            purchRow.setMaterial(material);
            if(null != notification.getReplyStock())
            {
                purchRow.setQuantity(new BigDecimal(notification.getReplyStock()));
            }
            purchRow.preInsert();
            purchRow.setRemark(notification.getReplyremark());
            purchRow.setStatus("1");
            purchRow.setNotifiId(notification.getRecordId());
            purchRow.setMergeType(TypeKey.PU_PURCHASING_APPLY_MERGETY_ONE.toString());
            purchRow.setType("2");
            CommonUtils.updateNextNo(22);

            // 获取库位
            MaterialPlaceCom materialPlaceCom = new MaterialPlaceCom();
            materialPlaceCom.setSaleCompanyId(notification.getSaleId());
            materialPlaceCom.setMaterialId(notification.getMaterialId());
            materialPlaceCom.setCompanyId(notification.getCompany().getRecordId());
            List<MaterialPlaceCom> placeComList = storeHouseDao.getMaterialPlaceComListTwo(materialPlaceCom);
            if (Collections3.isNotEmpty(placeComList)) {
                MaterialPlaceCom maCom = placeComList.get(0);
                purchRow.setStockPlaceComId(maCom.getRecordId());
                purchRow.setStockPlaceId(maCom.getMaterPlaceId());
                purchRow.setBranchId(maCom.getSaleCompanyId());
            } else {
                materialPlaceCom.setSaleCompanyId(null);
                placeComList = storeHouseDao.getMaterialPlaceComListTwo(materialPlaceCom);
                if (Collections3.isNotEmpty(placeComList)) {
                    MaterialPlaceCom maCom = placeComList.get(0);
                    purchRow.setStockPlaceComId(maCom.getRecordId());
                    purchRow.setStockPlaceId(maCom.getMaterPlaceId());
                }
            }
            purchRawDao.savePurRaw(purchRow);
        }
        return "success";
    }

    public void updateEstStock(Notification notification) {
        if (notification.getEstimateDate() != null && notification.getEstimateDateTwo() != null
                && notification.getDeliveryDate() != null) {
            Integer purchRowDays = 0;
            // 判断一下，如果可以送就不用修改其它信息
            if (notification.getWaitDays() == null) {
                notification.setWaitDays(0);
            }
            if (notification.getPurchRowDays() == null) {
                notification.setPurchRowDays(0);
            }
            // 如果输入采购时间大于待产时间
            if (purchRowDays > notification.getWaitDays()) {
                // 如果采购时间大于待产时间
                if (notification.getPurchRowDays() > notification.getWaitDays()) {
                    // 将输入采购时间和采购时间进行对比
                    notification.setEstimateDateTwo(DateUtils.date_add(notification.getEstimateDateTwo(),
                            purchRowDays - notification.getPurchRowDays()));
                } else {
                    // 将输入采购时间和待产时间进行对比
                    notification.setEstimateDateTwo(DateUtils.date_add(notification.getEstimateDateTwo(),
                            purchRowDays - notification.getWaitDays()));
                }
            } else {
                // 如果采购时间大于待产时间
                if (notification.getPurchRowDays() > notification.getWaitDays()) {
                    // 将采购时间和待产时间进行对比，使用待产时间
                    notification.setEstimateDateTwo(DateUtils.date_add(notification.getEstimateDateTwo(),
                            notification.getWaitDays() - notification.getPurchRowDays()));
                }
            }
            double d = 0d;
            // 判断是否可以正常交货
            if (notification.getNewDeliveryDays() != null) {
                d = Math.ceil(DateUtils.getDistanceOfTwoDate(notification.getNewDeliveryDays(),
                        notification.getEstimateDateTwo()));
            } else {
                d = Math.ceil(
                        DateUtils.getDistanceOfTwoDate(notification.getDeliveryDate(), notification.getEstimateDateTwo()));
            }

            if (d > 0) {
                if (notification.getReplyStock() != null && notification.getReplyStock() > 0) {
                    notification.setEstimateRemark("待采购确认");
                } else {
                    notification.setEstimateRemark("不能生产，有" + d + "天的延期");
                }
                // notification.setEstimateRemark("不能生产，有" + d + "天的延期");
                notification.setEstimateDate(notification.getEstimateDateTwo());
            } else {
                if (notification.getReplyStock() != null && notification.getReplyStock() > 0) {
                    notification.setEstimateRemark("待采购确认");
                } else {
                    notification.setEstimateRemark("可以生产");
                }
                // notification.setEstimateRemark("可以生产");
                if (notification.getNewDeliveryDays() != null) {
                    notification.setEstimateDate(notification.getNewDeliveryDays());
                } else {
                    notification.setEstimateDate(notification.getDeliveryDate());
                }
            }

            String method = "";
            if (StringUtils.isNotBlank(notification.getEstimateMethod())) {
                method = notification.getEstimateMethod() + ";" + UserUtils.getUser().getUserName() + "修改了材料，采购进行重置";
            } else {
                method = UserUtils.getUser().getUserName() + "修改了材料，采购进行重置";
            }
            notification.setEstimateMethod(method);

            if (notification.getReplyStock() != null && notification.getReplyStock() > 0) {
                notification.setEstimateRemark("待采购确认");
            }
            notification.setPurchRowDays(0);
            purchRawDao.updatePurchDays(notification);
        }
    }

    public List<Notification> getPurchRawPurDetail(List<Notification> notiList) {
        String notiIds = null;
        for (Notification noti : notiList) {
            if (StringUtils.isNotBlank(notiIds)) {
                notiIds = notiIds + "," + noti.getRecordId();
            } else {
                notiIds = noti.getRecordId();
            }
        }
        if (StringUtils.isBlank(notiIds)) {
            return notiList;
        }

        PurchRaw purchRaw = new PurchRaw();
        purchRaw.setCompany(UserUtils.getUser().getCompany());

        // 获取主料的
        purchRaw.setNotifiId(notiIds);
        List<PurchasingDetail> hostMatList = purchasingDetailDao.getPurDetailList(purchRaw);

        // 获取取替换料的
        List<PurchasingDetail> replaceMatList = Lists.newArrayList();
        Notification notifi = new Notification();
        notifi.setRecordId(notiIds);
        List<NotificationMaterial> notiMaterialList = dao.getNotifiMaterial(notifi);
        if (Collections3.isNotEmpty(notiMaterialList)) {
            String notiMatIds = null;
            for (NotificationMaterial notiMat : notiMaterialList) {
                if (StringUtils.isNotBlank(notiMatIds)) {
                    notiMatIds = notiMatIds + "," + notiMat.getRecordId();
                } else {
                    notiMatIds = notiMat.getRecordId();
                }
            }

            purchRaw.setNotifiId(null);
            purchRaw.setNotifiMaterialId(notiMatIds);
            replaceMatList = purchasingDetailDao.getPurDetailList(purchRaw);
        }

        for (Notification noti : notiList) {
            noti.setPurDetailList(Lists.newArrayList());
            if (Collections3.isNotEmpty(hostMatList)) {
                for (PurchasingDetail purDetail : hostMatList) {
                    if (StringUtils.isNotBlank(purDetail.getNotifiId())
                            && purDetail.getNotifiId().equals(noti.getRecordId())) {
                        noti.getPurDetailList().add(purDetail);
                    }
                }
            }
            if (Collections3.isNotEmpty(replaceMatList)) {
                for (PurchasingDetail purDetail : replaceMatList) {
                    if (StringUtils.isNotBlank(purDetail.getNotifiId())
                            && purDetail.getNotifiId().equals(noti.getRecordId())) {
                        noti.getPurDetailList().add(purDetail);
                    }
                }
            }
        }
        return notiList;
    }

    @Transactional(readOnly = false)
    public Map<String, String> resetEstimate(Notification notification) {
        Map<String, String> data = new HashMap<>();
        // 删除所有产能占用记录
        NotificationMonyDeail monyDeail = new NotificationMonyDeail();
        monyDeail.setNoficationId(notification.getRecordId());
        notificationDao.delMonyDeailByNotification(monyDeail);
        // 更新该通知单上面所有关于评估的字段为空
        notification.preUpdate();
        notificationDao.updateNotificationDataOnEditNotfTwoReset(notification);
        data.put("result", "success");
        data.put("message", "通知单重置评估成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> updateCustomerDays(Notification notification) {
        Map<String, Object> data = new HashMap<>();
        // 更新评估结果、评估交期、客户协调交期
        notification.preUpdate();
        notificationDao.updateNewDeliverDays(notification);

        // 更新和单的评估结果
        updateMergeNotiEstimateRemark(notification);

        data.put("result", "success");
        data.put("message", "通知单协调交期成功!");
        return data;
        // return ModifyUtils.coordinateHandoverApprovalApproval(notification);
    }

    @Transactional(readOnly = false)
    public Map<String, Object> closeTheCase(Notification notification) {
        Map<String, Object> data = new HashMap<>();
        if (null == notification || StringUtils.isBlank(notification.getRecordId())) {
            data.put("result", "fail");
            data.put("message", "通知单不能为空!");
            return data;
        }
        notification.setCompany(UserUtils.getUser().getCompany());
        // 验证通知单是否已做送货单
        /*
         * Integer count = deliveryDetailDao.getDelDetailCountByNotiId(notification); if (null != count && count > 0) {
         * data.put("result", "fail"); data.put("message", "通知单已经做了送货通知单，不能进行结案!"); return data; }
         */
        String groupCenterId = contractDetailDao.getGroupCenterId(notification.getContractDetail().getRecordId());
        String factoryComId = CompanyUtil.getInstance().getFactId();
        Integer countTwo = feedingDao.checkFeedOutCountTwo(groupCenterId, factoryComId);
        if (null != countTwo && countTwo > 0) {
            data.put("result", "fail");
            data.put("message", "江西领德辉有卡还没有过完数，不能进行结案!");
            return data;
        }
        boolean con = ModifyUtils.allocationExist("24", notification.getCompany());
        if (con) {
            String result = ModifyUtils.notiCloseTheCaseApproval(notification);
            if (!"fail".equals(result)) {
                data.put("result", "success");
                data.put("message", result);
                return data;
            }
        } else {
            data.put("result", "fail");
            data.put("message", "请先配置通知单结案审批!");
        }

        return data;
    }

    public Map<String, Object> cancleNoti(Notification notification) {
        Map<String, Object> data = new HashMap<>();
        Company company = UserUtils.getUser().getCompany();
        if (null == notification || StringUtils.isBlank(notification.getRecordId())) {
            data.put("result", "fail");
            data.put("message", "通知单不能为空!");
            return data;
        }
        if (null == notification.getContractDetail()
                || StringUtils.isBlank(notification.getContractDetail().getRecordId())) {
            data.put("result", "fail");
            data.put("message", "数据错误，请联系管理员!");
            return data;
        }
        // 验证当前公司是否已采购入库
        Integer countOne = prdorderDetailDao.checkPrdDetailCount(notification.getContractDetail().getRecordId());
        if (null != countOne && countOne > 0) {
            data.put("result", "fail");
            data.put("message", company.getName() + "已采购入库不能作废!");
            return data;
        }
        // 验证合同是否已送货
        Integer deliveryId = contractDetailDao.getDeliveryDetailCount(notification.getContractDetail().getRecordId());
        if (null != deliveryId && deliveryId > 0)
        {
            data.put("result", "fail");
            data.put("message", "已送货合同明细不能作废!");
            return data;
        }

        String groupCenterId = contractDetailDao.getGroupCenterId(notification.getContractDetail().getRecordId());

        // 验证龙南公司是否已采购入库
        Integer countTwo = prdorderDetailDao.checkPrdDetailCountTwo(groupCenterId);
        if (null != countTwo && countTwo > 0) {
            data.put("result", "fail");
            data.put("message", "龙南领德公司已采购入库不能作废!");
            return data;
        }
        // 验证江西领德辉是否已投料出库
        String factoryComId = CompanyUtil.getInstance().getFactId();
        Integer countThree = feedingDao.checkFeedOutCount(groupCenterId, factoryComId);
        if (null != countThree && countThree > 0) {
            data.put("result", "fail");
            data.put("message", "江西领德辉已经投料出库,不能作废!");
            return data;
        }
        // 验证江西领德辉是否已做投料单
        Integer countFour = feedingDao.checkFeedOutCountThree(groupCenterId, factoryComId);
        if (null != countFour && countFour > 0)
        {
            data.put("result", "fail");
            data.put("message", "工厂已经做了投料单，请通知计划删除投料单后再操作!");
            return data;
        }
        // 验证江西领德辉是否已做工程卡
        Integer countFive = notificationDao.checkDetailCount(groupCenterId, factoryComId);
        if (null != countFive && countFive > 0)
        {
            data.put("result", "fail");
            data.put("message", "工厂已制作工程卡，请通知工程删除工程资料再操作!");
            return data;
        }
        // 验证江西工厂是否送货
        Integer factoryDeliveryDetail = contractDetailDao.getFactoryDeliveryDetail(groupCenterId, factoryComId);
        if (null != factoryDeliveryDetail && factoryDeliveryDetail > 0)
        {
            data.put("result", "fail");
            data.put("message", "工厂送货出库，不能作废!");
            return data;
        }
        String result = ModifyUtils.reflashApproval(notification);
        if (!"fail".equals(result)) {
            data.put("result", "success");
            data.put("message", result);
            return data;
        }
        data.put("result", "success");
        data.put("message", "作废通知单成功!");
        return data;
    }

    // 更新和单的评估结果
    @Transactional(readOnly = false)
    public void updateMergeNotiEstimateRemark(Notification notification) {
        Notification not = notificationDao.get(notification);
        if (null != not && StringUtils.isNotBlank(not.getMergeId())) {
            Integer count = notificationDao.getMergeNotiCount(not);
            Notification mergeNoti = new Notification();
            mergeNoti.setRecordId(not.getMergeId());
            if (null == count || count == 0) {
                mergeNoti.setEstimateRemark("可以生产");
            }
            notificationDao.updateEstimateRemark(mergeNoti);
        }
    }

    public List<GroupCenterData> getGroupCenterDataList(GroupCenterData groupCenterData) {
        groupCenterData.setLnCompanyId(CompanyUtil.getInstance().getEcoemyId());
        groupCenterData.setJxCompanyId(CompanyUtil.getInstance().getFactId());
        groupCenterData.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        return notificationDao.getGroupCenterDataList(groupCenterData);
    }

    // 时间段的出库数据
    public List<GroupCenterBill> getGroupCenterBillList(GroupCenterBill groupCenterBill) {
        groupCenterBill.setLnCompanyId(CompanyUtil.getInstance().getEcoemyId());
        groupCenterBill.setJxCompanyId(CompanyUtil.getInstance().getFactId());
        groupCenterBill.setXsComIds(CompanyUtil.getInstance().getSaleIds());
        groupCenterBill.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        List<GroupCenterBill> billList = notificationDao.getGroupCenterBillList(groupCenterBill);
        return billList;
    }

    public List<Reconciliation> getReconciliationCount(GroupCenterBill groupCenterBill) {
        groupCenterBill.setXsComIds(CompanyUtil.getInstance().getSaleIds());
        return reconciliationDao.getReconciliationAllCount(groupCenterBill);
    }

    public List<GoodsCheck> getGroupCenterCustPlaintList(GroupCenterBill groupCenterBill) {
        groupCenterBill.setJxCompanyId(CompanyUtil.getInstance().getFactId());
        groupCenterBill.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        List<GoodsCheck> custPlainList = notificationDao.getGroupCenterCustPlaintList(groupCenterBill);
        return custPlainList;
    }

    public Map<String, Object> getGroupCenterBoundList(GroupCenterBound groupCenterBound) {
        Map<String, Object> data = new HashMap<>();
        Company company = UserUtils.getUser().getCompany();
        String companyId = company.getRecordId();
        if (groupCenterBound.getEndTime() != null) {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(groupCenterBound.getEndTime());
            deliTime.set(Calendar.HOUR_OF_DAY, 23);
            deliTime.set(Calendar.MINUTE, 59);
            deliTime.set(Calendar.SECOND, 59);
            deliTime.set(Calendar.MILLISECOND, 59);
            groupCenterBound.setEndTime(deliTime.getTime());
        }
        if (groupCenterBound.getStartTime() != null) {
            Calendar deliTime = Calendar.getInstance();
            deliTime.setTime(groupCenterBound.getStartTime());
            deliTime.set(Calendar.HOUR_OF_DAY, 00);
            deliTime.set(Calendar.MINUTE, 00);
            deliTime.set(Calendar.SECOND, 00);
            deliTime.set(Calendar.MILLISECOND, 00);
            groupCenterBound.setStartTime(deliTime.getTime());
        }

        groupCenterBound.setCompanyId(CompanyUtil.getInstance().getFactId());
        if (CompanyUtil.getInstance().valiteSaleCompany()) {
            groupCenterBound.setXsCompanyId(companyId);
        } else {
            groupCenterBound.setXsCompanyId(CompanyUtil.getInstance().getSaleIds());
        }
        List<GroupCenterBound> groupCenterBoundList = notificationDao.getGroupCenterBoundList(groupCenterBound);
        data.put("groupCenterBoundList", groupCenterBoundList);

        GroupCenterBill groupCenterBill = new GroupCenterBill();
        groupCenterBill.setStartTime(groupCenterBound.getStartTime());
        groupCenterBill.setLnCompanyId(CompanyUtil.getInstance().getEcoemyId());
        groupCenterBill.setJxCompanyId(CompanyUtil.getInstance().getFactId());

        String comId = null;
        if (CompanyUtil.getInstance().valiteSaleCompany()) {
            comId = companyId;
        } else {
            comId = CompanyUtil.getInstance().getSaleIds();
        }
        groupCenterBill.setCompanyId(comId);
        List<GroupCenterBill> billList = notificationDao.getGroupCenterBillListTwo(groupCenterBill);
        data.put("billList", billList);

        return data;
    }

    public void setFooter(String yeMei, PdfWriter writer, BaseFont bf, int presentFontSize, Rectangle pageSize) {
        ItextPdfHeaderFooter headerFooter = new ItextPdfHeaderFooter(yeMei, bf, presentFontSize, pageSize);
        writer.setPageEvent(headerFooter);
    }

    public Pdf getGroupListPrintPDF(List<GroupCenterBill> resultList, String basePath, String timeStr, String showFlag) {
        // 查询要打印的成品采购信息
        Pdf pdf = new Pdf();
        Company company = UserUtils.getUser().getCompany();
        ByteArrayOutputStream base = new ByteArrayOutputStream();
        ByteArrayOutputStream output = new ByteArrayOutputStream();

        try {
            // 查询出分编号工艺的信息
            Rectangle rectPageSize = new Rectangle(PageSize.A4);
            Document document = new Document(rectPageSize, 10, 10, 20, 40);

            ByteArrayOutputStream page1 = new ByteArrayOutputStream();
            PdfWriter writer = PdfWriter.getInstance(document, page1);
            BaseFont baseFontChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            setFooter("", writer, baseFontChinese, 9, rectPageSize);
            document.open();

            // 主表-无边框
            PdfPTable table = new PdfPTable(1);
            table.setSplitLate(false); // 这页表格能放多少就放多少
            table.setTotalWidth(556f);
            table.setLockedWidth(true);
            table.getDefaultCell().setBorder(PdfPCell.NO_BORDER);

            // 公司名称
            PdfPCell title = new PdfPCell(new ParagraphCn16(company.getName()));
            title.setHorizontalAlignment(Element.ALIGN_CENTER);
            title.setBorder(PdfPCell.NO_BORDER);
            table.addCell(title);

            PdfPCell cell = null;

            cell = new PdfPCell(new ParagraphCn12(""));
            cell.setPaddingTop(10);
            cell.setPadding(10f);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);

            // 双方信息
            PdfPTable infoTable = new PdfPTable(10);
            String xsCompanyName =
                    CollectionUtils.isEmpty(resultList) || resultList.get(0).getXsCompanyName() == null ? ""
                            : resultList.get(0).getXsCompanyName();
            String lnCompanyName =
                    CollectionUtils.isEmpty(resultList) || resultList.get(0).getLnCompanyName() == null ? ""
                            : resultList.get(0).getLnCompanyName();
            String tempName = lnCompanyName;
            if (xsCompanyName.contains("江西领德辉")) {
                lnCompanyName = xsCompanyName;
                xsCompanyName = tempName;
            }
            cell = new PdfPCell(new ParagraphCn9("卖方：" + lnCompanyName));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(7);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("对账月份：" + timeStr));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(3);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("买方：" + xsCompanyName));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(10);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            table.addCell(infoTable);

            cell = new PdfPCell(new ParagraphCn12(""));
            cell.setPaddingTop(10);
            cell.setPadding(10f);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);
            // 列表标题
            PdfPTable titleTable = null;
            if (!"2".equals(showFlag)) {
                titleTable = new PdfPTable(18);
            } else {
                titleTable = new PdfPTable(22);
            }

            /*
             * cell = new PdfPCell(new ParagraphCn9("客户")); cell.setPaddingTop(1); cell.setPaddingBottom(2);
             * cell.setColspan(2); cell.setHorizontalAlignment(Element.ALIGN_LEFT); //
             * cell.setBorder(PdfPCell.NO_BORDER); titleTable.addCell(cell);
             */

            cell = new PdfPCell(new ParagraphCn9("送货日期"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("送货单号"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("客户订单号"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("客户型号"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("厂编"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("PCS单价"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("送货数量"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);

            /*
             * cell = new PdfPCell(new ParagraphCn9("平米单价")); cell.setPaddingTop(1); cell.setPaddingBottom(2);
             * cell.setColspan(2); cell.setHorizontalAlignment(Element.ALIGN_LEFT); //
             * cell.setBorder(PdfPCell.NO_BORDER); titleTable.addCell(cell);
             *
             * cell = new PdfPCell(new ParagraphCn9("送货平米")); cell.setPaddingTop(1); cell.setPaddingBottom(2);
             * cell.setColspan(2); cell.setHorizontalAlignment(Element.ALIGN_LEFT); //
             * cell.setBorder(PdfPCell.NO_BORDER); titleTable.addCell(cell);
             */

            cell = new PdfPCell(new ParagraphCn9("金额"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);

            if ("2".equals(showFlag)) {
                cell = new PdfPCell(new ParagraphCn9("转下期数量"));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(2);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                // cell.setBorder(PdfPCell.NO_BORDER);
                titleTable.addCell(cell);

                cell = new PdfPCell(new ParagraphCn9("转下期金额"));
                cell.setPaddingTop(1);
                cell.setPaddingBottom(2);
                cell.setColspan(2);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                // cell.setBorder(PdfPCell.NO_BORDER);
                titleTable.addCell(cell);
            }

            cell = new PdfPCell(new ParagraphCn9("终端客编"));
            cell.setPaddingTop(1);
            cell.setPaddingBottom(2);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            // cell.setBorder(PdfPCell.NO_BORDER);
            titleTable.addCell(cell);

            // 列表内容
            if (Collections3.isNotEmpty(resultList)) {
                for (int i = 0; i < resultList.size(); i++) {
                    GroupCenterBill detail = resultList.get(i);
                    /*
                     * cell = new PdfPCell(new ParagraphCn9(detail.getXsCustName())); cell.setPaddingTop(1);
                     * cell.setPaddingBottom(2); cell.setColspan(2); cell.setHorizontalAlignment(Element.ALIGN_LEFT); //
                     * cell.setBorder(PdfPCell.NO_BORDER); titleTable.addCell(cell);
                     */

                    cell = new PdfPCell(new ParagraphCn9(detail.getOperateDateStr()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);

                    cell = new PdfPCell(new ParagraphCn9(detail.getXsOutBoundNo()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);

                    cell = new PdfPCell(new ParagraphCn9(detail.getXsConCustomerPo()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);

                    cell = new PdfPCell(new ParagraphCn9(detail.getXsCustomerModel()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);

                    cell = new PdfPCell(new ParagraphCn9(detail.getXsCraftNo()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);

                    cell = new PdfPCell(new ParagraphCn9(
                            null == detail.getXsPricePrice() ? "" : strs(detail.getXsPricePrice().toString())));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);

                    cell = new PdfPCell(new ParagraphCn9(null == detail.getXsStockOutBoundStocks() ? ""
                            : strs(detail.getXsStockOutBoundStocks().toString())));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);

                    /*
                     * cell = new PdfPCell( new ParagraphCn9(null == detail.getAreaPrice() ? "" :
                     * strs(detail.getAreaPrice().toString()))); cell.setPaddingTop(1); cell.setPaddingBottom(2);
                     * cell.setColspan(2); cell.setHorizontalAlignment(Element.ALIGN_LEFT); //
                     * cell.setBorder(PdfPCell.NO_BORDER); titleTable.addCell(cell);
                     *
                     * cell = new PdfPCell( new ParagraphCn9(null == detail.getArea() ? "" :
                     * strs(detail.getArea().toString()))); cell.setPaddingTop(1); cell.setPaddingBottom(2);
                     * cell.setColspan(2); cell.setHorizontalAlignment(Element.ALIGN_LEFT); //
                     * cell.setBorder(PdfPCell.NO_BORDER); titleTable.addCell(cell);
                     */

                    cell = new PdfPCell(new ParagraphCn9(
                            null == detail.getXsSubTotalStr() ? "" : strs(detail.getXsSubTotalStr().toString())));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);

                    if ("2".equals(showFlag)) {

                        cell = new PdfPCell(
                                new ParagraphCn9(null == detail.getNextNum() ? "" : strs(detail.getNextNum().toString())));
                        cell.setPaddingTop(1);
                        cell.setPaddingBottom(2);
                        cell.setColspan(2);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        // cell.setBorder(PdfPCell.NO_BORDER);
                        titleTable.addCell(cell);

                        cell = new PdfPCell(new ParagraphCn9(
                                null == detail.getNextMoney() ? "" : strs(detail.getNextMoney().toString())));
                        cell.setPaddingTop(1);
                        cell.setPaddingBottom(2);
                        cell.setColspan(2);
                        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                        // cell.setBorder(PdfPCell.NO_BORDER);
                        titleTable.addCell(cell);
                    }

                    cell = new PdfPCell(new ParagraphCn9(detail.getFinalNo()));
                    cell.setPaddingTop(1);
                    cell.setPaddingBottom(2);
                    cell.setColspan(2);
                    cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    // cell.setBorder(PdfPCell.NO_BORDER);
                    titleTable.addCell(cell);

                }

            }
            table.addCell(titleTable);

            // 要求

            // 底部
            // 双方信息
            infoTable = new PdfPTable(10);
            cell = new PdfPCell(new ParagraphCn9("卖方："));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(6);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("买方："));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);
            String xsCompany = "";
            String xsAddr = "";
            String xsPhone = "";
            String xsBank = "";
            String xsAccount = "";
            if (!CollectionUtils.isEmpty(resultList) && null != resultList.get(0).getXsCompanyId()) {
                Company xc = companyDao.get(resultList.get(0).getXsCompanyId());
                if (null != xc) {
                    xsCompany = xc.getName() == null ? "" : xc.getName();
                    xsAddr = xc.getAddress() == null ? "" : xc.getAddress();
                    xsPhone = xc.getPhone() == null ? "" : xc.getPhone();
                    xsBank = xc.getBank() == null ? "" : xc.getBank();
                    xsAccount = xc.getAccount() == null ? "" : xc.getAccount();
                }
            }
            String lsCompany = "";
            String lsAddr = "";
            String lsPhone = "";
            String lsBank = "";
            String lsAccount = "";
            if (!CollectionUtils.isEmpty(resultList) && null != resultList.get(0).getLnCompanyId()) {
                Company lS = companyDao.get(resultList.get(0).getLnCompanyId());
                if (null != lS) {
                    lsCompany = lS.getName() == null ? "" : lS.getName();
                    lsAddr = lS.getAddress() == null ? "" : lS.getAddress();
                    lsPhone = lS.getPhone() == null ? "" : lS.getPhone();
                    lsBank = lS.getBank() == null ? "" : lS.getBank();
                    lsAccount = lS.getAccount() == null ? "" : lS.getAccount();
                }
            }
            String templsCompany = lsCompany;
            String templsAddr = lsAddr;
            String templsPhone = lsPhone;
            String templsBank = lsBank;
            String templsAccount = lsAccount;
            if (xsCompany.contains("江西领德辉")) {
                lsCompany = xsCompany;
                lsAddr = xsAddr;
                lsPhone = xsPhone;
                lsBank = xsBank;
                lsAccount = xsAccount;

                xsCompany = templsCompany;
                xsAddr = templsAddr;
                xsPhone = templsPhone;
                xsBank = templsBank;
                xsAccount = templsAccount;
            }
            cell = new PdfPCell(new ParagraphCn9("单位名称：" + lsCompany));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(6);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("单位名称：" + xsCompany));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            // 地址

            cell = new PdfPCell(new ParagraphCn9("地址：" + lsAddr));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(6);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("地址：" + xsAddr));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            // 电话

            cell = new PdfPCell(new ParagraphCn9("电话：" + lsPhone));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(6);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("电话：" + xsPhone));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            // 开户行

            cell = new PdfPCell(new ParagraphCn9("开户行：" + lsBank));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(6);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("开户行：" + xsBank));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            // 账号

            cell = new PdfPCell(new ParagraphCn9("账号：" + lsAccount));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(6);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            cell = new PdfPCell(new ParagraphCn9("账号：" + xsAccount));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);
            table.addCell(infoTable);

            cell = new PdfPCell(new ParagraphCn12(""));
            cell.setPaddingTop(5);
            cell.setPadding(10f);
            table.addCell(cell);

            infoTable = new PdfPTable(10);
            cell = new PdfPCell(new ParagraphCn9("制表人：" + UserUtils.getUser().getUserName()));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(7);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            cell = new PdfPCell(
                    new ParagraphCn9("打印时间：" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
            cell.setPaddingTop(2);
            cell.setPaddingBottom(4);
            cell.setColspan(3);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            infoTable.addCell(cell);

            table.addCell(infoTable);

            document.add(table);
            document.close();

            output = page1;
            base = output;
            pdf = new Pdf();
            pdf.setOutPut(base);
            pdf.setShowName(DateUtils.getDate("yyyyMMddHHmmss") + "QD");

        } catch (Exception e) {
            e.printStackTrace();
        }
        return pdf;
    }

    private String strs(String str) {
        if (str.indexOf(".") > 0) {
            str = str.replaceAll("0+?$", "");// 删掉尾数为0的字符
            str = str.replaceAll("[.]$", "");// 结尾如果是小数点，则去掉
        }
        return str;
    }

    public List<GroupCenterBill> getGroupCenterBillTwoList(GroupCenterBill groupCenterBill) {
        groupCenterBill.setLnCompanyId(CompanyUtil.getInstance().getEcoemyId());
        groupCenterBill.setJxCompanyId(CompanyUtil.getInstance().getFactId());
        groupCenterBill.setXsComIds(CompanyUtil.getInstance().getSaleIds());
        List<GroupCenterBill> billList = notificationDao.getGroupCenterBillList(groupCenterBill);
        return billList;
    }

    public void calculationCost(WechatAudit audit, Notification notification) {
        if (audit == null) {
            return;
        }
//        if (audit.getSalePrecent() == null) {
//            audit.setSalePrecent(0.0);
//        }
//        if (audit.getManagePrecent() == null) {
//            audit.setManagePrecent(0.0);
//        }
        double sumCostFee = 0;
        double sumAmount = 0;
        double sumProcessFee = 0;
        double sumMaterialFee = 0;
        double sumSaleFee = 0;
        double sumPrdMouldFee = 0;
        double sumPrdTestShelfFee = 0;
        double sumOutboundFee = 0;
        double sumManageFee = 0;
//        for (ContractDeailVo contractDeailVo : audit.getVoList()) {
//            if (contractDeailVo.getTotalAmt() == null) {
//                contractDeailVo.setTotalAmt("0");
//            }
//            if (contractDeailVo.getPrdTestShelfFee() == null) {
//                contractDeailVo.setPrdTestShelfFee(BigDecimal.ZERO);
//            }
//            if (contractDeailVo.getPrdMouldFee() == null) {
//                contractDeailVo.setPrdMouldFee(BigDecimal.ZERO);
//            }
//            sumAmount += Number(contractDeailVo.getTotalAmt());
//            sumPrdTestShelfFee += contractDeailVo.getPrdTestShelfFee().doubleValue();
//            sumPrdMouldFee += contractDeailVo.getPrdMouldFee().doubleValue();
//            sumOutboundFee += Number(contractDeailVo.getOutboundFee());
//            double costFee = contractDeailVo.getCostFee().doubleValue(), saleFee = 0, manageFee = 0;
//            // 量产
//            if (contractDeailVo.getValueOrderType() == null || contractDeailVo.getValueOrderType().equals("2")) {
//                // 业务费
//                saleFee = Number(contractDeailVo.getTotalAmt()) * audit.getSalePrecent() / 100;
//                sumSaleFee += saleFee;
//                // 管理费
//                manageFee = Number(contractDeailVo.getTotalAmt()) * audit.getManagePrecent() / 100;
//                sumManageFee += manageFee;
//
//                if (contractDeailVo.getMadePcsPrice() == null
//                        || contractDeailVo.getMadePcsPrice().compareTo(BigDecimal.ZERO) == 0) {
//                    if (contractDeailVo.getGroupCenter() != null) {
//                        if (contractDeailVo.getGroupCenter().getProcessFee() != null
//                                && contractDeailVo.getGroupCenter().getProcessFee().compareTo(BigDecimal.ZERO) > 0) {
//                            sumProcessFee += contractDeailVo.getGroupCenter().getProcessFee().doubleValue();
//                        }
//                        if (contractDeailVo.getGroupCenter().getMaterialFee() != null
//                                && contractDeailVo.getGroupCenter().getMaterialFee().compareTo(BigDecimal.ZERO) > 0) {
//                            sumMaterialFee += contractDeailVo.getGroupCenter().getMaterialFee().doubleValue();
//                        }
//                    }
//                } else {
//                    if (contractDeailVo.getPrdAmount() == null) {
//                        costFee = 0;
//                    } else {
//                        costFee = contractDeailVo.getPrdAmount().doubleValue();
//                    }
//                }
//            } else {
//                costFee = 0;
//            }
//
//            // 成本
//            sumCostFee += costFee;
//
//            if (contractDeailVo.getGroupCenter() != null) {
//                // 材料费
//                Double materialFee = contractDeailVo.getGroupCenter().getMaterialFee() != null
//                        ? contractDeailVo.getGroupCenter().getMaterialFee().doubleValue()
//                        : 0;
//                // 面积
//                Double deailArea =
//                        contractDeailVo.getDeailArea() != null ? Double.valueOf(contractDeailVo.getDeailArea()) : 0;
//                // 单价/㎡
//                Double squareMeter = 0.0;
//                if (contractDeailVo.getPrice() != null && contractDeailVo.getPnlLength() != null
//                        && contractDeailVo.getPnlWidth() != null && contractDeailVo.getPnlDivisor() != null) {
//                    squareMeter = contractDeailVo.getPrice().doubleValue()
//                            / (contractDeailVo.getPnlLength().doubleValue() * contractDeailVo.getPnlWidth().doubleValue()
//                            / contractDeailVo.getPnlDivisor().doubleValue() / 1000000);
//                }
//                Double processFeeSquareMeter = Math.round((squareMeter - materialFee / deailArea) * 100) / 100.0;
//                notification.setProcessFeeSquareMeter(processFeeSquareMeter.toString());
//            }
//        }

        // 总金额
        notification.setNotificationTotalAmt(Number(sumAmount));
        // 总加工费
        notification.setNotificationProcessFee(Number(sumProcessFee));
        // 总材料费
        notification.setMaterialFee(Number(sumMaterialFee));
        // 总业务费
        notification.setSaleFee(new BigDecimal(sumSaleFee).setScale(2,BigDecimal.ROUND_HALF_UP).toString());
        // 总管理费
        notification.setManageFee(Number(Math.round(sumManageFee * 100) / 100.0));
        // 总净成本
        notification.setNetCostFee(Number(Math.round((sumSaleFee + sumManageFee + sumCostFee) * 100) / 100.0));
        // 模具费
        notification.setPrdMouldFee(Number(sumPrdMouldFee));
        // 测试架费
        notification.setPrdTestShelfFee(Number(sumPrdTestShelfFee));
        // 外发费
        notification.setOutboundFee(Number(sumOutboundFee));

        Double computRate = sumAmount;
        if (computRate <= 0) {
            computRate = 1.0;
        }
        // 总净利率
        notification.setNetGrossProfitMargin(
                Number(Math.round((sumAmount - Number(notification.getNetCostFee())) / computRate * 100 * 100) / 100.0));

    }

    public double Number(String value) {
        if (StringUtils.isBlank(value)) {
            return 0;
        } else {
            return Double.valueOf(value);
        }
    }

    public String Number(Double value) {
        if (value == null) {
            return "0";
        } else {
            return String.valueOf(value);
        }
    }

    @Transactional(readOnly = false)
    public Map<String, List<?>> splitCapacity(CapacityDeail capacityDeail) {
        // 删除被拆分的数据
        capacityRecordDao.deleteCapacityDeail(capacityDeail.getContactDeailId());
        CapacityDeail deail1 = capacityDeail.clone();
        deail1.setDeailArea(capacityDeail.getPartCapacity());
        List<CapacityDeail> addList = new ArrayList<>();
        addList.add(deail1);
        addList.add(capacityDeail);
        // 插入两条拆分后的数据
        capacityRecordDao.saveCapacityDeailList(addList);
        // 重新查询
        return getCapacityDetailDate(capacityDeail);
    }

    @Transactional(readOnly = false)
    public Map<String, List<?>> mergeCapacity(CapacityDeail capacityDeail) {
        String companyId = UserUtils.getUser().getCompany().getRecordId();
        // 获取本合同明细对应的所有面积
        BigDecimal area = capacityRecordDao.getSumDeailArea(capacityDeail.getContactDeailId(), companyId);
        // 插入合并的合同明细面积
        CapacityDeail deail1 = capacityDeail.clone();
        deail1.setDeailArea(area);
        // 删除被拆分的数据
        capacityRecordDao.deleteCapacityDeail(capacityDeail.getContactDeailId());
        // 插入合并后的数据
        List<CapacityDeail> addList = new ArrayList<>();
        addList.add(deail1);
        capacityRecordDao.saveCapacityDeailList(addList);
        // 重新查询
        return getCapacityDetailDate(capacityDeail);
    }

    public void saveCapacityChangeList(List<MaxBatchAreaConfirm> list)
    {
        if(Collections3.isEmpty(list))
        {
            return;
        }
        User user = UserUtils.getUser();
        List<CapacityChange> changeList = Lists.newArrayList();
        for(MaxBatchAreaConfirm comfirm : list)
        {
            CapacityChange change = new CapacityChange();
            change.setCompanyId(user.getCompany().getRecordId());
            change.setProTime(comfirm.getUseDate());
            change.setDepartId(comfirm.getDeptId());
            change.setProductType(comfirm.getProductType());
            // 样品款数
            change.setSampleNum(comfirm.getSampleNum());
            // 量产款数
            change.setMassProductionNum(comfirm.getMassProductionNum());
            // 新单款数
            change.setNewOrderNum(comfirm.getNewOrderNum());
            // 产能
            if (StringUtils.isNotBlank(comfirm.getArea()))
            {
                change.setCapacity(new BigDecimal(comfirm.getArea()));
            }
            change.setType(2);
            change.setRemark("提前范围使用");
            change.preInsert();
            changeList.add(change);

            CapacityChange clone = change.clone();
            if(null != change.getSampleNum())
            {
                change.setSampleNum(change.getSampleNum() * -1);
            }
            if(null != change.getMassProductionNum())
            {
                change.setMassProductionNum(change.getMassProductionNum() * -1);
            }
            if(null != change.getNewOrderNum())
            {
                change.setNewOrderNum(change.getNewOrderNum() * -1);
            }
            if(null != change.getCapacity())
            {
                change.setCapacity(change.getCapacity().multiply(new BigDecimal(-1)));
            }
            clone.setType(1);
            clone.setDepartId(comfirm.getDeptAfterId());
            clone.preInsert();
            changeList.add(clone);
        }
        if(Collections3.isNotEmpty(changeList))
        {
            capacityRecordDao.saveCapacityChangeList(changeList,user.getCompany().getRecordId(),user.getRecordId());
        }
    }

    @Transactional(readOnly = false)
    public Map<String, List<?>> turnCapacity(CapacityDeail capacityDeail) {
        String proTime = StringUtils.isNotBlank(capacityDeail.getProTimeStr()) ? capacityDeail.getProTimeStr() : capacityDeail.getOrgProTimeStr();
        proTime = StringUtils.isNotBlank(proTime) ? proTime : null;
        String proTimeCopy = StringUtils.isNotBlank(capacityDeail.getProTimeCopyStr()) ? capacityDeail.getProTimeCopyStr() : null;
        // 更新数据的月份
        capacityRecordDao.updateProTime(proTime, proTimeCopy, capacityDeail.getRecordId());

        saveCapacityChangeList(capacityDeail.getConfirmList());

        // 重新查询
        return getCapacityDetailDate(capacityDeail);
    }

    @Transactional(readOnly = false)
    public Map<String, List<?>> turnAllCapacity(CapacityDeail capacityDeail) {
        String proTime = StringUtils.isNotBlank(capacityDeail.getProTimeStr()) ? capacityDeail.getProTimeStr() : null;
        // 更新数据的月份
        capacityRecordDao.batchUpdateProTime(proTime, capacityDeail.getList());

        // 重新查询
        return getCapacityDetailDate(capacityDeail);
    }

    @Transactional(readOnly = false)
    public Map<String, List<?>> auditCapacity(CapacityDeail capacityDeail) {
        if (capacityDeail != null && capacityDeail.getList() != null && capacityDeail.getList().size() > 0){
            capacityRecordDao.batchUpdateStatus(capacityDeail);

            for(CapacityDeail cd : capacityDeail.getList())
            {
                saveCapacityChangeList(cd.getConfirmList());
            }
        }
        return getCapacityDetailDate(capacityDeail);
    }

    public void setChangeData(List<CapacityChange> capacityChangeList,Department department,CapacityVo vo)
    {
        if(Collections3.isNotEmpty(capacityChangeList))
        {
            for(CapacityChange change : capacityChangeList)
            {
                if(!DateUtils.formatDate(change.getProTime()).equals(DateUtils.formatDate(vo.getDay())))
                {
                    continue;
                }
                if(change.getDepartId().equals(department.getRecordId()))
                {
                    CapacityUse use = null;
                    if("1".equals(change.getProductType()) && null != department.getExCapacity())
                    {
                        use = department.getExCapacity().clone();
                    }
                    else if("2".equals(change.getProductType()) && null != department.getSiCapacity())
                    {
                        use = department.getSiCapacity().clone();
                    }
                    if(null == use)
                    {
                        continue;
                    }
                    BigDecimal capacity = null == use.getCapacity() ? BigDecimal.ZERO : use.getCapacity();
                    Integer amount = null == use.getAmount() ? 0 : use.getAmount();
                    Integer sampleAmount = null == use.getSampleAmount() ? 0 : use.getSampleAmount();
                    Integer orderAmount = null == use.getOrderAmount() ? 0 : use.getOrderAmount();
                    if(Collections3.isEmpty(use.getChaneList()))
                    {
                        use.setChaneList(Lists.newArrayList());
                    }
                    use.getChaneList().add(change);
                    if(null != change.getCapacity())
                    {
                        use.setCapacity(capacity.add(change.getCapacity()));
                    }
                    if(null != change.getSampleNum())
                    {
                        use.setSampleAmount(sampleAmount + change.getSampleNum());
                    }
                    if(null != change.getMassProductionNum())
                    {
                        use.setAmount(amount + change.getMassProductionNum());
                    }
                    if(null != change.getNewOrderNum())
                    {
                        use.setOrderAmount(orderAmount + change.getNewOrderNum());
                    }

                    if("1".equals(change.getProductType()))
                    {
                        department.setExCapacity(use);
                    }
                    else if("2".equals(change.getProductType()))
                    {
                        department.setSiCapacity(use);
                    }
                }
            }
        }
    }

    public void setConfirmUseDeptData(List<MaxBatchAreaConfirm> confirmUseDeptList,Department department,CapacityVo vo)
    {
        if(Collections3.isNotEmpty(confirmUseDeptList))
        {
            for(MaxBatchAreaConfirm confirm : confirmUseDeptList)
            {
                if(!DateUtils.formatDate(confirm.getUseDate()).equals(DateUtils.formatDate(vo.getDay())))
                {
                    continue;
                }
                if(confirm.getDeptAfterId().equals(department.getRecordId()))
                {
                    CapacityUse use = null;
                    if("1".equals(confirm.getProductType()) && null != department.getExCapacity())
                    {
                        use = department.getExCapacity().clone();
                    }
                    else if("2".equals(confirm.getProductType()) && null != department.getSiCapacity())
                    {
                        use = department.getSiCapacity().clone();
                    }
                    if(null == use)
                    {
                        continue;
                    }
                    BigDecimal capacity = null == use.getComfirmCapacity() ? BigDecimal.ZERO : use.getComfirmCapacity();
                    Integer amount = null == use.getComfirmAmount() ? 0 : use.getComfirmAmount();
                    Integer sampleAmount = null == use.getComfirmSampleAmount() ? 0 : use.getComfirmSampleAmount();
                    Integer orderAmount = null == use.getComfirmOrderAmount() ? 0 : use.getComfirmOrderAmount();
                    if(Collections3.isEmpty(use.getConfirmList()))
                    {
                        use.setConfirmList(Lists.newArrayList());
                    }
                    use.getConfirmList().add(confirm);
                    if(StringUtils.isNotBlank(confirm.getArea()))
                    {
                        use.setComfirmCapacity(capacity.add(new BigDecimal(confirm.getArea())));
                    }
                    if(null != confirm.getSampleNum())
                    {
                        use.setComfirmSampleAmount(sampleAmount + confirm.getSampleNum());
                    }
                    if(null != confirm.getMassProductionNum())
                    {
                        use.setComfirmAmount(amount + confirm.getMassProductionNum());
                    }
                    if(null != confirm.getNewOrderNum())
                    {
                        use.setComfirmOrderAmount(orderAmount + confirm.getNewOrderNum());
                    }
                    if("1".equals(confirm.getProductType()))
                    {
                        department.setExCapacity(use);
                    }
                    else if("2".equals(confirm.getProductType()))
                    {
                        department.setSiCapacity(use);
                    }
                }
            }
        }
    }

    public Map<String, List<?>> getCapacityDetailDate(CapacityDeail capacityDeail) {
        Map<String, List<?>> result = new HashMap<>();
        String companyId = UserUtils.getUser().getCompany().getRecordId();
        capacityDeail.setFactoryId(companyId);
        capacityDeail.setCompanyId(companyId);
        // 获取当前工厂产能、样品、新单、量产数据
        CapacityVersion version = capacityRecordDao.getVersionDateNew(companyId);
        if (version == null || StringUtils.isBlank(version.getVersionDate())) {
            return result;
        }
        String versionDate = version.getVersionDate();
        List<CapacityDeail> capacityDeailList = capacityDao.getCapacityDeailList(capacityDeail);
        List<CapacityChange> capacityChangeList = capacityDao.findChangeDeail(capacityDeail);
        List<CapacityRecord> recordList =
                capacityDao.findByMaxVersionRecord(companyId, null,null, versionDate);
        List<CycleHoliday> holidayList = capacityDao.getHolidayList(companyId);

        // 获取确认中的使用部门列表
        List<MaxBatchAreaConfirm> confirmUseDeptList = maxBatchAreaConfirmDao.getConfirmUseDeptList(companyId);

        // 获取产能往前可用天数
        Integer beforeCapacityDay = null;
        ParameterSet ps = new ParameterSet();
        ps.setCompany(new Company(companyId));
        ps.setJianPin("beforeCapacity");
        ParameterSet parameterSet = parameterSetDao.getParameterSetByCompanyAndjianPin(ps);
        if(null != parameterSet && StringUtils.isNotBlank(parameterSet.getParameterValue()))
        {
            beforeCapacityDay = Integer.valueOf(parameterSet.getParameterValue());
        }
        Integer defaultDay = 7;
        // 部门
        List<GroupOrgRelation> groupOrgRelationList = CompanyUtil.getInstance().getGroupList();
        List<Department> departList = groupOrgRelationList.stream().map(g -> {
            Department d = new Department();
            d.setRecordId(g.getGroupOrgId());
            d.setName(g.getGroupOrgName());
            Integer flag = 0;
            for (int i = 0; i < recordList.size(); i++) {
                // 设置每个部门的限制产能、新单、样品、量产
                CapacityRecord cc = recordList.get(i);
                if (StringUtils.isNotBlank(d.getRecordId()) && StringUtils.isNotBlank(cc.getDepartId())
                        && d.getRecordId().equals(cc.getDepartId())) {
                    flag = 1;
                    CapacityUse use = new CapacityUse();
                    use.setCapacity(cc.getCapacity());
                    use.setAmount(cc.getAmount());
                    use.setSampleAmount(cc.getSampleAmount());
                    use.setOrderAmount(cc.getOrderAmount());
                    use.setFlag(flag);
                    if (StringUtils.isNotBlank(cc.getProductType())) {
                        if (cc.getProductType().equals("1")) {
                            d.setExCapacity(use);
                        } else if (cc.getProductType().equals("2")) {
                            d.setSiCapacity(use);
                        }
                    } else {
                        d.setAllCapacity(use);
                    }
                }
            }
            d.setType(flag.toString());
            return d;
        }).collect(Collectors.toList());

        departList.removeIf(department -> department.getType().equals("0"));

        List<CapacityVo> dayList = new ArrayList<>();
        // 三层：日期-部门-订单
        if (capacityDeail.getDay() != null && capacityDeail.getDay() > 0) {
            Integer needDay = 0;
            Integer restDay = 0;
            for (int i = 0; i < capacityDeail.getDay(); i++) {
                CapacityVo vo = new CapacityVo();
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DAY_OF_MONTH, i);
                vo.setDay(calendar.getTime());

                vo.setAllCapacity(BigDecimal.ZERO);
                vo.setAllSampleOrderCount(0);
                vo.setAllNewOrderCount(0);
                vo.setAllOrderCount(0);

                vo.setCapacity(BigDecimal.ZERO);
                vo.setNewOrderCount(0);
                vo.setOrderCount(0);
                vo.setSampleOrderCount(0);

                vo.setCanUseCapacity(BigDecimal.ZERO);
                vo.setCanUseOrderCount(0);
                vo.setCanUseSampleOrderCount(0);
                vo.setCanUseNewOrderCount(0);
                boolean flag = false;
                for (int j = 0; j < holidayList.size(); j++) {
                    if (holidayList.get(j).getHolidayDate() != null && DateUtils.formatDate(holidayList.get(j)
                            .getHolidayDate()).equals(DateUtils.formatDate(vo.getDay()))) {
                        flag = true;
                        break;
                    }
                }
                if (flag) {
                    ++restDay;
                    // 休息天数
                    vo.setNeedDays(++needDay);
                    vo.setStatus("1");
                    dayList.add(vo);
                    continue;
                } else {
                    // 工作天数
                    vo.setNeedDays(++needDay);
                    // 可以使用全工厂产能
                    if(null != beforeCapacityDay && beforeCapacityDay > 0)
                    {
                        // 去掉休息日
                        Integer useDay =  needDay - restDay;
                        if(useDay < defaultDay && useDay >= (defaultDay - beforeCapacityDay))
                        {
                            vo.setBeforeCapacityFlag(1);
                        }
                    }
                }
                vo.setRestDay(restDay);
                vo.setAllCapacity(version.getCapacity());
                vo.setAllSampleOrderCount(version.getSampleAmount());
                vo.setAllNewOrderCount(version.getOrderAmount());
                vo.setAllOrderCount(version.getAmount());

                // 设置部门
                Integer useAmount = 0;
                Integer useSampleAmount = 0;
                Integer useOrderAmount = 0;
                BigDecimal useCapacity = BigDecimal.ZERO;
                List<CapacityDeail> deailList = new ArrayList<>();
                vo.setDepartmentList(new ArrayList<>());
                for (Department depart : departList) {
                    Department department = depart.clone();

                    // zjn 2023-04-13 借用记录数据赋值
                    setChangeData(capacityChangeList,department,vo);

                    // zjn 2023-04-17 借用未确认记录使用部门赋值
                    setConfirmUseDeptData(confirmUseDeptList,department,vo);

                    // 曝光
                    Integer exUseAmount = 0;
                    Integer exUseSampleAmount = 0;
                    Integer exUseOrderAmount = 0;
                    BigDecimal exUseCapacity = BigDecimal.ZERO;
                    List<CapacityDeail> exDeailList = new ArrayList<>();

                    // 丝印
                    Integer siUseAmount = 0;
                    Integer siUseSampleAmount = 0;
                    Integer siUseOrderAmount = 0;
                    BigDecimal siUseCapacity = BigDecimal.ZERO;
                    List<CapacityDeail> siDeailList = new ArrayList<>();
                    for (CapacityDeail c : capacityDeailList) {
                        if (c.getUseFlag().equals("3")){
                            continue;
                        }
                        if (c.getProTime() == null || c.getDepartId() == null) {
                            continue;
                        }
                        if (DateUtils.formatDate(c.getProTime()).equals(DateUtils.formatDate(vo.getDay())) && c.getDepartId().equals(department.getRecordId())) {
                            if (StringUtils.isNotBlank(c.getProcessValueId())) {
                                if (c.getProcessValueId().equals("1")) {
                                    exDeailList.add(c);
                                    if (StringUtils.isNotBlank(c.getMaterialType()) && c.getMaterialType().equals("1")){
                                        exUseOrderAmount++;
                                    }
                                    if (StringUtils.isNotBlank(c.getOrderType())){
                                        if (c.getOrderType().equals("2")) {
                                            BigDecimal capacity =
                                                    c.getDeailArea() == null ? BigDecimal.ZERO : c.getDeailArea();
                                            exUseCapacity = exUseCapacity.add(capacity);
                                            exUseAmount++;
                                        } else if (c.getOrderType().equals("1")) {
                                            exUseSampleAmount++;
                                        }
                                    }
                                    if (department.getExCapacity() == null) {
                                        CapacityUse use = new CapacityUse();
                                        use.setFlag(1);
                                        department.setExCapacity(use);
                                    }
                                } else if (c.getProcessValueId().equals("2")) {
                                    siDeailList.add(c);
                                    if (StringUtils.isNotBlank(c.getMaterialType()) && c.getMaterialType().equals("1")){
                                        siUseOrderAmount++;
                                    }
                                    if (StringUtils.isNotBlank(c.getOrderType())){
                                        if (c.getOrderType().equals("2")) {
                                            BigDecimal capacity =
                                                    c.getDeailArea() == null ? BigDecimal.ZERO : c.getDeailArea();
                                            siUseCapacity = siUseCapacity.add(capacity);
                                            siUseAmount++;
                                        } else if (c.getOrderType().equals("1")) {
                                            siUseSampleAmount++;
                                        }
                                    }
                                    if (department.getSiCapacity() == null) {
                                        CapacityUse use = new CapacityUse();
                                        use.setFlag(1);
                                        department.setSiCapacity(use);
                                    }
                                }
                            }
                        }
                    }
                    if (department.getExCapacity() != null) {
                        department.getExCapacity().setUseCapacity(exUseCapacity);
                        department.getExCapacity().setUseAmount(exUseAmount);
                        department.getExCapacity().setUseOrderAmount(exUseOrderAmount);
                        department.getExCapacity().setUseSampleAmount(exUseSampleAmount);
                        department.getExCapacity().setDeailList(exDeailList);
                    }
                    if (department.getSiCapacity() != null) {
                        department.getSiCapacity().setUseCapacity(siUseCapacity);
                        department.getSiCapacity().setUseAmount(siUseAmount);
                        department.getSiCapacity().setUseOrderAmount(siUseOrderAmount);
                        department.getSiCapacity().setUseSampleAmount(siUseSampleAmount);
                        department.getSiCapacity().setDeailList(siDeailList);
                    }
                    useAmount = useAmount + exUseAmount + siUseAmount;
                    useSampleAmount = useSampleAmount + exUseSampleAmount + siUseSampleAmount;
                    useOrderAmount = useOrderAmount + exUseOrderAmount + siUseOrderAmount;
                    useCapacity = useCapacity.add(exUseCapacity).add(siUseCapacity);

                    deailList.addAll(exDeailList);
                    deailList.addAll(siDeailList);
                    vo.getDepartmentList().add(department);
                }
                vo.setCapacity(useCapacity);
                vo.setNewOrderCount(useOrderAmount);
                vo.setOrderCount(useAmount);
                vo.setSampleOrderCount(useSampleAmount);
                vo.setDeailList(deailList);
                boolean flagA = false;
                if(StringUtils.isBlank(capacityDeail.getCraftNoQuery())&& StringUtils.isBlank(capacityDeail.getCustomerPoQuery())){
                    flagA = true;
                }else{
                    if(StringUtils.isNotBlank(capacityDeail.getCustomerPoQuery())){
                        flagA = false;
                        String customerPo = capacityDeail.getCustomerPoQuery().replace(" ","");
                        for(CapacityDeail capacityDeailA :deailList ){
                            if (StringUtils.isBlank(capacityDeailA.getCustomerPo())){
                                continue;
                            }
                            String customerPoA = capacityDeailA.getCustomerPo().replace(" ","");
                            if(customerPoA.contains(customerPo)){
                                flagA = true;
                                break;
                            }
                        }
                    }
                    if(StringUtils.isNotBlank(capacityDeail.getCraftNoQuery())){
                        flagA = false;
                        String craftNo = capacityDeail.getCraftNoQuery().replace(" ","");
                        for(CapacityDeail capacityDeailA :deailList ){
                            if (StringUtils.isBlank(capacityDeailA.getCraftNo())){
                                continue;
                            }
                            String craftNoA = capacityDeailA.getCraftNo().replace(" ","");
                            if(craftNoA.contains(craftNo) ){
                                flagA = true;
                                break;
                            }
                        }
                    }
                }
                if(!flagA){
                    continue;
                }
                // 设置明细
                vo.setCanUseCapacity(vo.getAllCapacity().subtract(vo.getCapacity()));
                vo.setCanUseOrderCount(vo.getAllOrderCount() - vo.getOrderCount());
                vo.setCanUseSampleOrderCount(vo.getAllSampleOrderCount() - vo.getSampleOrderCount());
                vo.setCanUseNewOrderCount(vo.getAllNewOrderCount() - vo.getNewOrderCount());
                dayList.add(vo);
            }
            result.put("dayList", dayList);
        }
        result.put("capacityList", capacityDeailList);
        return result;
    }

    public void setDownLoad(ExportExcel excel, List<CapacityDeail> list, String[] hearList)
    {
        for (CapacityDeail capacityDeail : list)
        {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList)
            {
                String val = "";
                switch (name)
                {
                    case "部门":
                        val = capacityDeail.getDepartName();
                        break;
                    case "客户交期":
                        val = capacityDeail.getDeliveryDateStr();
                        break;
                    case "出货日期":
                        val = capacityDeail.getProTimeStr();
                        break;
                    case "入单日期":
                        val = capacityDeail.getEstimateStartDateStr();
                        break;
                    case "材料":
                        if (capacityDeail.getPurchRowDays()!=null){
                            val = capacityDeail.getPurchRowDays().toString();
                        }else{
                            val = "";
                        }
                        break;
                    case "生产":
                        val = String.valueOf(capacityDeail.getNotiNeedDays().subtract(capacityDeail.getPurchRowDays()));
                        break;
                    case "最早交期":
                        val = capacityDeail.getMinDateStr();
                        break;
                    case "客户订单号":
                        val = capacityDeail.getCustomerPo();
                        break;
                    case "厂编":
                        val = capacityDeail.getCraftNo();
                        break;
                    case "面积":
                        if(capacityDeail.getDeailArea()!=null){
                            val = capacityDeail.getDeailArea().toString();
                        }else{
                            val = "";
                        }
                        break;
                    case "类型":
                        if (StringUtils.isNotBlank(capacityDeail.getProcessValueId()))
                        {
                            switch (capacityDeail.getProcessValueId())
                            {
                                case "1":
                                    val = "曝光";
                                    break;
                                case "2":
                                    val = "丝印";
                                    break;
                            }
                        }
                        break;
                    case "订单":
                        if (StringUtils.isNotBlank(capacityDeail.getOrderType()))
                        {
                            switch (capacityDeail.getOrderType())
                            {
                                case "1":
                                    val = "样品";
                                    break;
                                case "2":
                                    val = "量产";
                                    break;
                            }
                        }
                        break;
                    case "资料":
                        if (StringUtils.isNotBlank(capacityDeail.getMaterialType()))
                        {
                            switch (capacityDeail.getMaterialType().toString())
                            {
                                case "1":
                                    val = "新单";
                                    break;
                                case "2":
                                    val = "返单";
                                    break;
                            }
                        }
                        break;
                    case "投料单号":
                        val = capacityDeail.getFeedNo();
                        break;
                    case "出库日期":
                        val = capacityDeail.getAcDistributeDateStr();
                        break;
                    case "订单状态":
                        val = capacityDeail.getStatusStr();
                        break;
                    case "类型状态":
                        String statusA = "";
                        if(capacityDeail.getFeedId() == null && capacityDeail.getStatus() == null){
                            statusA = "待确认排产表";
                        }else if( capacityDeail.getFeedId() == null && capacityDeail.getStatus().equals(1) ){
                            statusA = "已确认排产表";
                        }else if (capacityDeail.getFeedId()!= null){
                            statusA = "已投料清单";
                        }
                        val = statusA;
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    @Transactional(readOnly = false)
    public Map<String, Object> definitiveLock(Notification notification) {
        Map<String, Object> data = new HashMap<>();
        if (null == notification) {
            data.put("result", "fail");
            data.put("message", "参数失效，请刷新重试!");
            return data;
        }
        notification.preUpdate();
        notificationDao.updateLock(notification);
        data.put("result", "success");
        data.put("message", "锁单成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> definitiveLockUnlock(Notification notification) {
        Map<String, Object> data = new HashMap<>();
        if (null == notification) {
            data.put("result", "fail");
            data.put("message", "参数失效，请刷新重试!");
            return data;
        }
        notification.preUpdate();
        notificationDao.updateLockUnlock(notification);
        data.put("result", "success");
        data.put("message", "取消锁单成功!");
        return data;
    }

    public Map<String,List<?>> getApacityManagementData()
    {
        Map<String, List<?>> result = new HashMap<>();
        // 客户
/*        Customer customer = new Customer();
        customer.setStatus(TypeKey.MD_CUSTOMER_STATUS_NORMAL);
        List<Customer> customerList = customerDao.findCustomers(customer);
        result.put("customerList", customerList);*/

        // 部门
        List<GroupOrgRelation> deptList = CompanyUtil.getInstance().getGroupList();
        result.put("deptList", deptList);

        return result;
    }

    public List<CapacityDeail> getHolisticManagementData(CapacityDeail capacityDeail)
    {
        String companyId = UserUtils.getUser().getCompany().getRecordId();
        capacityDeail.setFactoryId(companyId);
        if(null != capacityDeail.getDay() && capacityDeail.getDay() > 0)
        {
            capacityDeail.setStartDate(new Date());
            capacityDeail.setEndDate(DateUtils.date_add(capacityDeail.getStartDate(),capacityDeail.getDay()));
        }
        List<CapacityDeail> capacityDeailList = capacityDao.getCapacityDeailListTwo(capacityDeail);
        return capacityDeailList;
    }
    public List<MaxBatchAreaConfirm> apacityBorrowOpen( MaxBatchAreaConfirm maxBatchAreaConfirm){
        maxBatchAreaConfirm.setCompany(UserUtils.getUser().getCompany());
        List<MaxBatchAreaConfirm> lists =  capacityDao.apacityBorrowOpen(maxBatchAreaConfirm);
        return lists;
    }

    public void setDataList(ExportExcel excel, List<Notification> list, String[] hearList)
    {
        for (Notification n : list) {
            int colunm = 0;
            Row row = excel.addRow();
            for (String name : hearList)
            {
                String val = "";
                switch (name)
                {
                    case "通知单编号":
                        val = n.getNo();
                        break;
                    case "单据状态":
                        val = StringUtils.isBlank(n.getDocumentsStatus()) ? "正常" : n.getDocumentsStatus();
                        break;
                    case "来源":
                        val = n.getShowGroupCenterValue();
                        break;
                    case "外发工厂":
                        String factoryName = null;
                        if(null != n.getContractDetail())
                        {
                            factoryName = n.getContractDetail().getFactoryName();
                        }
                        val = factoryName;
                        break;
                    case "评估信息":
                        val = n.getEstimate();
                        break;
                    case "客户型号":
                        val = n.getCustomerModel();
                        break;
                    case "生产编号":
                        val = n.getCraftNo();
                        break;
                    case "销售公司":
                        val = n.getSaleName();
                        break;
                    case "下单时间":
                        String orderDate = null;
                        if(null != n.getOrderDate())
                        {
                            orderDate = DateUtils.formatDate(n.getOrderDate());
                        }
                        val = orderDate;
                        break;
                    case "客户预期":
                        val = n.getCustDeliveryDateStr();
                        break;
                    case "预估交期":
                        String showEstimateDate = null;
                        if(null != n.getShowEstimateDate())
                        {
                            showEstimateDate = DateUtils.formatDate(n.getShowEstimateDate());
                        }
                        val = showEstimateDate;
                        break;
                    case "订单审批时间":
                        String orderApprovalFinshTime = null;
                        if(null != n.getOrderApprovalFinshTime())
                        {
                            orderApprovalFinshTime = DateUtils.formatDate(n.getOrderApprovalFinshTime());
                        }
                        val = orderApprovalFinshTime;
                        break;
                    case "工程set尺寸":
                        val = n.getSetSize();
                        break;
                    case "测试方式":
                        val = n.getProcessCraftValueOne();
                        break;
                    case "油墨种类":
                        val = n.getProcessCraftValueTwo();
                        break;
                    case "啤(锣)方式":
                        val = n.getProcessCraftValueThree();
                        break;
                    case "问客时间":
                        val = n.getFeedbackHour();
                        break;
                    case "工厂交期":
                        String factoryDate = null;
                        if(null != n.getFactoryDate())
                        {
                            factoryDate = DateUtils.formatDate(n.getFactoryDate());
                        }
                        val = factoryDate;
                        break;
                    case "生产交期":
                        val = n.getProductionDeliveryDate();
                        break;
                    case "状态":
                        String urgentFlag = null;
                        if(StringUtils.isBlank(n.getUrgentFlag()) || "0".equals(n.getUrgentFlag()))
                        {
                            urgentFlag = "普通";
                        }
                        else if(StringUtils.isNotBlank(n.getUrgentFlag()) && "1".equals(n.getUrgentFlag()))
                        {
                            urgentFlag = "加急";
                        }
                        val = urgentFlag;
                        break;
                    case "通知单状态":
                        String showStatus = null;
                        if(StringUtils.isNotBlank(n.getStatus()))
                        {
                            switch (n.getStatus())
                            {
                                case "200401":
                                    showStatus = "未确认";
                                    break;
                                case "200402":
                                    showStatus = "已确认";
                                    break;
                                case "200403":
                                    showStatus = "已完成";
                                    break;
                                case "200404":
                                    if(null != n.getFeedOutFlag() && n.getFeedOutFlag())
                                    {
                                        showStatus = "已投料";
                                    }
                                    else
                                    {
                                        showStatus = "待投料";
                                    }
                                    break;
                                case "200405":
                                    showStatus = "补料中";
                                    break;
                                case "200406":
                                    showStatus = "已作废";
                                    break;
                                case "60001":
                                    showStatus = "审批中";
                                    break;
                            }
                        }
                        val = showStatus;
                        break;
                    case "合同编号":
                        String contractNo = null;
                        if(null != n.getContract())
                        {
                            contractNo = n.getContract().getNo();
                        }
                        val = contractNo;
                        break;
                    case "客诉单号":
                        val = n.getRejectNo();
                        break;
                    case "订单类型":
                        String orderTypeValue = null;
                        if(null != n.getOrderType())
                        {
                            orderTypeValue = n.getOrderType().getValue();
                        }
                        val = orderTypeValue;
                        break;
                    case "使用材料":
                        val = n.getReplyMaterialName();
                        break;
                    case "材料品牌":
                        val = n.getManufacturer();
                        break;
                    case "材料数量":
                        val = null == n.getOldOccupiedStock() ? "0" : n.getOldOccupiedStock().toString();
                        break;
                    case "客户订单号":
                        String customerPo = null;
                        if(null != n.getContract())
                        {
                            customerPo = n.getContract().getCustomerPo();
                        }
                        val = customerPo;
                        break;
                    case "订单数量":
                        val = n.getQuantity();
                        break;
                    case "生产数量":
                        val = null == n.getFeedPcsQty() ? "0" : n.getFeedPcsQty().toString();
                        break;
                    case "已入库数量":
                        val = null == n.getCompletedQty() ? "0" : n.getCompletedQty().toString();
                        break;
                    case "下单平米数":
                        val = n.getSpec();
                        break;
                    case "客户名称":
                        String cusName = null;
                        if(null != n.getContract() && null != n.getContract().getCustomer())
                        {
                            cusName = n.getContract().getCustomer().getShortName();
                        }
                        val = cusName;
                        break;
                    case "业务员":
                        val = n.getSalseManName();
                        break;
                    case "合同明细状态":
                        String showDetailStatus = null;
                        if(null != n.getContractDetail() && StringUtils.isNotBlank(n.getContractDetail().getStatus()))
                        {
                            switch (n.getContractDetail().getStatus())
                            {
                                case "200205":
                                    showDetailStatus = "已作废";
                                    break;
                                case "200204":
                                    showDetailStatus = "已送货";
                                    break;
                                case "200210":
                                    showDetailStatus = "对账关闭";
                                    break;
                                default:
                                    showDetailStatus = n.getContractDetailStatus();
                                    break;
                            }
                        }
                        val = showDetailStatus;
                        break;
                    case "打印次数":
                        val = null == n.getPrintNum() ? "0" : n.getPrintNum().toString();
                        break;
                    case "结案原因":
                        val = n.getCloseTheCaseCause();
                        break;
                    case "金额(￥)":
                        val = n.getNotificationTotalAmt();
                        break;
                    case "加工费":
                        val = n.getNotificationProcessFee();
                        break;
                    case "材料费":
                        val = n.getMaterialFee();
                        break;
                    case "业务费":
                        val = n.getSaleFee();
                        break;
                    case "管理费":
                        val = n.getManageFee();
                        break;
                    case "成本费":
                        val = n.getNetCostFee();
                        break;
                    case "平方米加工费":
                        val = n.getProcessFeeSquareMeter();
                        break;
                    case "采购测试架费":
                        val = n.getPrdTestShelfFee();
                        break;
                    case "采购模具费":
                        val = n.getPrdMouldFee();
                        break;
                    case "外发费用":
                        val = n.getOutboundFee();
                        break;
                    case "利润率":
                        val = n.getNetGrossProfitMargin();
                        break;
                    case "销售部门":
                        val = n.getDepartName();
                        break;
                }
                excel.addCell(row, colunm++, val, 2, null);
            }
        }
    }

    public Map<String,Object>getBusinessChangeData(Notification notification)
    {
        Map<String,Object>data = new HashMap<>();
        List<Material> orgList = Lists.newArrayList();
        List<Material> materialCompare = Lists.newArrayList();
        if(null == notification || StringUtils.isBlank(notification.getRecordId()))
        {
            data.put("result", "fail");
            data.put("message", "参数失效，请刷新重试!");
            return data;
        }
        Integer count = produceBatchDetailDao.feedingOutputCount(notification);
        if (count !=null && count > 0){
            data.put("result", "fail");
            data.put("message", "已投料出库，不可再进行业务变更!");
            return data;
        }
        String companyId = CompanyUtil.getInstance().getFactId();
        String notiId = notification.getRecordId();
        ChangeDataUtils changeDataUtils = new ChangeDataUtils();
        List<Material> matList = changeDataUtils.getOrderMaterialList(companyId,notiId,false);
        orgList = changeDataUtils.handleMaterialData(matList);
        //计算具体料的平米单价
        materialCompare = changeDataUtils.compareMaterData(matList);
        data.put("result","success");
        data.put("orgList",orgList);
        data.put("resList",materialCompare);
        return data;
    }
    @Transactional(readOnly = false)
    public GroupCenter compareDate(Notification notification){
        if(null == notification)
        {
            return null;
        }
        //获取变更前的平米单价、江西单价、净利润
        GroupCenter groupCenter =  new GroupCenter();
        groupCenter.setContractDetailId(notification.getContractDetailId());
        groupCenter.setCompany(UserUtils.getUser().getCompany());
        GroupCenter groupCenterObj = notificationDao.getGroupCenterDate(groupCenter);
        //运算求现在的平米单价、江西单价、净利润
        BigDecimal materialFee = BigDecimal.ZERO;
        BigDecimal jxPrice = BigDecimal.ZERO;
        BigDecimal usePercent = BigDecimal.ZERO;
        BigDecimal percent = BigDecimal.ZERO;
        BigDecimal costPrice = BigDecimal.ZERO;
        BigDecimal areaPrice = notification.getAreaPrice();

        //计算采购费
        BigDecimal prdAmount = BigDecimal.ZERO;
        BigDecimal prdEngineeringFee = BigDecimal.ZERO;
        BigDecimal prdMouldFee = BigDecimal.ZERO;
        BigDecimal prdTestShelfFee = BigDecimal.ZERO;
        BigDecimal prdFilmFee = BigDecimal.ZERO;
        BigDecimal prdOthersFee = BigDecimal.ZERO;
        prdEngineeringFee = groupCenterObj.getPrdEngineeringFee() == null ? new BigDecimal(0):groupCenterObj.getPrdEngineeringFee();
        prdMouldFee = groupCenterObj.getPrdMouldFee() == null ? new BigDecimal(0):groupCenterObj.getPrdMouldFee();
        prdTestShelfFee = groupCenterObj.getPrdTestShelfFee() == null ? new BigDecimal(0):groupCenterObj.getPrdTestShelfFee();
        prdFilmFee = groupCenterObj.getPrdFilmFee() == null ? new BigDecimal(0):groupCenterObj.getPrdFilmFee();
        prdOthersFee = groupCenterObj.getPrdOthersFee() == null ? new BigDecimal(0):groupCenterObj.getPrdOthersFee();
        if(notification.getContractDetail().getProcessValueId().equals("1")&& !notification.getContractDetail().getRecordId().equals(notification.getContractDetail().getGroupCenterId())){
            prdEngineeringFee = BigDecimal.ZERO;
        }
        prdAmount = prdEngineeringFee.add(prdMouldFee).add(prdTestShelfFee).add(prdFilmFee).add(prdOthersFee);

        //获取利用率
        BigDecimal useRate = dictValueDao.getUseRateFee(notification.getCopperValueId());
        //计算材料费
        if (StringUtils.isNotBlank(notification.getMaterialNo())){
            if (groupCenterObj.getParameterValue() != null && groupCenterObj.getOrderDeailArea() != null) {
                if(null == useRate || useRate.compareTo(BigDecimal.ZERO) == 0)
                {
                    return null;
                }
                percent = useRate.divide(new BigDecimal(100));
                usePercent = groupCenterObj.getOrderDeailArea().divide(percent, 4, RoundingMode.HALF_UP);
                materialFee = areaPrice.multiply(usePercent).setScale(4, RoundingMode.HALF_UP);
                if (notification.getEstimate().equals("加急"))
                {
                    materialFee = materialFee.add(groupCenterObj.getOrderDeailArea());
                }
                if (StringUtils.isNotBlank(notification.getMaterialFeeT())){
                    materialFee  =  materialFee.add(new BigDecimal(notification.getMaterialFeeT()));
                }
                groupCenter.setMaterialFee(materialFee);
            }
        }else{
            materialFee = new BigDecimal(notification.getMaterialFee());
            if (StringUtils.isNotBlank(notification.getMaterialFeeT())){
                materialFee  =  materialFee.add(new BigDecimal(notification.getMaterialFeeT()));
            }
            groupCenter.setMaterialFee(materialFee);
        }
        BigDecimal processFee = notification.getProcessFee();
        if (notification.getEstimate().equals("加急"))
        {
            processFee = processFee.add(groupCenterObj.getOrderDeailArea().multiply(new BigDecimal(2)));
        }
        costPrice = processFee.add(materialFee).divide(new BigDecimal(notification.getQuantity()),6, RoundingMode.HALF_UP);
        BigDecimal testpercent = new BigDecimal(100);
        BigDecimal zero = new BigDecimal(0.0000);
        //获取利润率
        BigDecimal percentTwo = notificationDao.getSelectcalculate(notification);
        if (null == percentTwo || percentTwo.compareTo(zero) == 0){
            jxPrice = costPrice;
        }else{
            BigDecimal calculate = (testpercent.add(percentTwo)).divide(new BigDecimal(100));
            jxPrice = costPrice;
        }
        //销售总金额
        BigDecimal saleMoney = new BigDecimal(notification.getNotificationTotalAmt());

        BigDecimal saleMoneyTwo = new BigDecimal(notification.getNotificationTotalAmt());
        if (saleMoneyTwo.compareTo(BigDecimal.ZERO) == 0){
            saleMoneyTwo = new BigDecimal(1);
        }

        //计算净利率（销售金额-（材料+加工+业务费+管理费））/ 销售金额
        BigDecimal netProfitRate = BigDecimal.ZERO;
        BigDecimal saleFee = groupCenterObj.getSaleFee() == null? BigDecimal.ZERO:groupCenterObj.getSaleFee();
        BigDecimal manageFee = groupCenterObj.getManageFee() == null? BigDecimal.ZERO:groupCenterObj.getManageFee();
        netProfitRate = (saleMoney.subtract(materialFee.add(processFee).add(prdAmount).add(saleFee).add(manageFee))).divide(saleMoneyTwo,4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));

        groupCenter.setJxPriceBefore(groupCenterObj.getJxPrice());
        //计算成本：
        BigDecimal costFee = BigDecimal.ZERO;
        BigDecimal netProfitRateBefore = BigDecimal.ZERO;
        costFee = BigDecimal.valueOf(Double.parseDouble(notification.getMaterialFee())).add(processFee).add(prdAmount);
        //计算净利率
        netProfitRateBefore = (saleMoney.subtract(costFee.add(saleFee).add(manageFee))).divide(saleMoneyTwo,4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));

        groupCenter.setNetProfitRateBefore(netProfitRateBefore);
        if (StringUtils.isBlank(notification.getManufacturerAfterTwo())&& StringUtils.isBlank(notification.getMaterialNo())) {
            groupCenter.setAreaPrice(groupCenterObj.getAreaPrice());
        }else{
            groupCenter.setAreaPrice(notification.getAreaPrice());
        }
        groupCenter.setAreaPriceBefore(groupCenterObj.getAreaPrice());
        groupCenter.setJxPrice(jxPrice);
        groupCenter.setNetProfitRate(netProfitRate);
        return groupCenter;
    }

    @Transactional(readOnly = false)
    public Map<String,String> updateManufacturer(Notification notification)
    {
        Map<String,String> data = new HashMap<>();
        if(null == notification)
        {
            data.put("result","fail");
            data.put("message","参数失效，请刷新重试!");
            return data;
        }
        String result = ModifyUtils.businessChange(notification);
        if (!"fail".equals(result))
        {
            notification.setStatus(String.valueOf(TypeKey.APPROVAL_STATUS_WAIT));
            notificationDao.updateStatus(notification);
            data.put("result", "success");
            data.put("message", result);
            return data;
        }
        else
        {
            data.put("result", "fail");
            data.put("message", "请先配置业务变更审批!");
        }
        /*GroupCenter groupCenter =  new GroupCenter();
        groupCenter.setContractDetailId(notification.getContractDetailId());
        groupCenter.setCompany(UserUtils.getUser().getCompany());
        GroupCenter groupcenterObj = notificationDao.getGroupCenterDate(groupCenter);
        groupcenterObj.setCompany(UserUtils.getUser().getCompany());
        groupcenterObj.setRemark(notification.getSubmitReason());
        //获取之前的数据插入到备份表里
        groupcenterObj.preInsert();
        notificationDao.InsertIntoGroupCenterTwo(groupcenterObj);
        BigDecimal materialFee = BigDecimal.ZERO;
        BigDecimal percent = BigDecimal.ZERO;
        BigDecimal areaPrice = notification.getAreaPrice();
        BigDecimal usePercent = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(notification.getManufacturerAfter())) {
            if (groupcenterObj != null) {
                if (groupcenterObj.getParameterValue() != null && groupcenterObj.getOrderDeailArea() != null) {
                    percent = groupcenterObj.getParameterValue().divide(new BigDecimal(100));
                    usePercent = groupcenterObj.getOrderDeailArea().divide(percent, 4, RoundingMode.HALF_UP);
                    materialFee = areaPrice.multiply(usePercent);
                    groupCenter.setMaterialFee(materialFee);
                    groupCenter.setManufacturer(notification.getManufacturerAfter());
                }
            }
        }else{
            materialFee = new BigDecimal(notification.getMaterialFee());
            groupCenter.setMaterialFee(materialFee);
            groupCenter.setManufacturer(notification.getManufacturer());
        }
        BigDecimal costPrice = BigDecimal.ZERO;
        BigDecimal lnPrice = BigDecimal.ZERO;
        BigDecimal jxPrice = BigDecimal.ZERO;
        costPrice = notification.getProcessFee().add(materialFee).divide(new BigDecimal(notification.getQuantity()),2, RoundingMode.HALF_UP);
        BigDecimal testpercent = new BigDecimal(100);
        BigDecimal zero = new BigDecimal(0.0000);
        //获取利润率
        BigDecimal percentTwo = notificationDao.getSelectcalculate(notification);
        if (percentTwo.compareTo(zero) == 0){
            lnPrice = costPrice;
            jxPrice = costPrice;
        }else{
           BigDecimal calculate = (testpercent.add(percentTwo)).divide(new BigDecimal(100));
            lnPrice = costPrice.multiply(calculate.add(percentTwo));
            jxPrice = costPrice;
        }
        //计算毛利率（销售金额-（材料费+加工费））/ 销售金额
        BigDecimal grossProfitRate = BigDecimal.ZERO;
        BigDecimal saleMoney = new BigDecimal(notification.getNotificationTotalAmt());
        grossProfitRate = (saleMoney.subtract(materialFee.add(notification.getProcessFee()))).divide(saleMoney,4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));

        //计算净利率（销售金额-（材料+加工+业务费+管理费））/ 销售金额
        BigDecimal netProfitRate = BigDecimal.ZERO;
        BigDecimal saleFee = new BigDecimal(notification.getSaleFee());
        BigDecimal manageFee = new BigDecimal(notification.getManageFee());
        netProfitRate = (saleMoney.subtract(materialFee.add(notification.getProcessFee()).add(saleFee).add(manageFee))).divide(saleMoney,4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));;

        String materialMsg = groupcenterObj.getMaterialMsg().substring(0,2);
        StringBuilder sb = new StringBuilder();
        sb.append(materialMsg).append("(").append(String.valueOf(areaPrice)).append("*").append(String.valueOf(usePercent)).append(")");
        String result = sb.toString();
        groupCenter.setMaterialMsg(result);
        groupCenter.setGrossProfitRate(grossProfitRate);
        groupCenter.setNetProfitRate(netProfitRate);
        groupCenter.setCostPrice(costPrice);
        groupCenter.setProcessFee(notification.getProcessFee());
        groupCenter.setSolderMaskType(Integer.valueOf(notification.getSolderMaskTypeAfter()));
        groupCenter.setLnPrice(lnPrice);
        groupCenter.setJxPrice(jxPrice);
        groupCenter.setContactDeailId(notification.getContractDetailId());
        groupCenter.setRecordId(String.valueOf(groupcenterObj.getGroupCenterId()));
        notificationDao.updateGroupCenterDate(groupCenter);

        ChangeDataUtils util = new ChangeDataUtils();
        util.updateDataByGroupCenter(groupCenter,new BigDecimal(0));
        data.put("result","success");
        data.put("message","业务变更成功!");*/
        return data;
    }


    public List<DeliveryLate> getDeliveryLateList(DeliveryLate deliveryLate)
    {
        deliveryLate.setCompany(UserUtils.getUser().getCompany());
        List<DeliveryLate>list = notificationDao.getDeliveryLateList(deliveryLate);
        //定义一个空字符串缓冲区
        StringBuffer str =  new StringBuffer();
        //循环结果集把recordid组合成id组
        for (DeliveryLate delice : list)
        {
            if (StringUtils.isNotBlank(delice.getRecordId()))
            {
                //组合id
                str.append(delice.getRecordId()).append(",");
            }
        }
        //获取交货批次时间
        List<DeliveryLate> demandDelivery = notificationDao.getDemandDelivery(str.toString());
        //获取计划排单日期
        List<DeliveryLate> schedulingTime = notificationDao.getSchedulingTime(str.toString());

        //循环集合
        for (DeliveryLate late : list)
        {
            for (DeliveryLate scheduling : schedulingTime)
            {
                //判断id是否相等，相等则把两个时间添加进结果集进行显示
                if (late.getRecordId().equals(scheduling.getBatchDetailId()))
                {
                    late.setIntendedDate(scheduling.getIntendedDate());
                    break;
                }
            }
            for (DeliveryLate delivery : demandDelivery)
            {
                if (late.getRecordId().equals(delivery.getBatchDetailId()))
                {
                    late.setDeliveryDate(delivery.getDeliveryDate());
                    break;
                }
            }
        }
        return list;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> refuseOrder(DeliveryLate deliveryLates)
    {
        Map<String, Object> data = new HashMap<>();
        if (null == deliveryLates)
        {
            data.put("result","fail");
            data.put("message","参数失败请重试!");
            return data;
        }
        deliveryLates.preUpdate();
        notificationDao.refuseOrder(deliveryLates);
        data.put("result","success");
        data.put("message","操作成功!");
        return data;
    }

    @Transactional(readOnly = false)
    public Map<String, Object> consentOrder(DeliveryLate deliveryLates)
    {
        Map<String, Object> data = new HashMap<>();
        if (null == deliveryLates)
        {
            data.put("result","fail");
            data.put("message","参数失败请重试!");
            return data;
        }
        deliveryLates.preUpdate();
        notificationDao.consentOrder(deliveryLates);
        data.put("result","success");
        data.put("message","操作成功!");
        return data;
    }

    public Map<String,Object> getProcessFeeRule(Notification notification)
        throws NoSuchFieldException, IllegalAccessException
    {
        Map<String,Object> map = new HashMap<>();
        QuotationSetUpUtils quotationSetUpUtils = new QuotationSetUpUtils();
        quotationSetUpUtils.priceMatch(notification.getContractDetail(), map);
        return map;
    }

    @Transactional(readOnly = false)
    public String updateLockSequence(Notification notification)
    {
        notificationDao.updateLockSequence(notification);
        return "success";
    }

}