<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kyb.pcberp.modules.production.dao.UserProcessDao">

	<!-- zjn 2019-08-30 用户工序添加 -->
	<insert id="insertUserProcess" useGeneratedKeys="true" keyProperty="recordId">
		INSERT INTO pd_user_process_relation(
			companyId,
			userId,
			processId,
			createdBy,
			createdDate,
			activeFlag,
			remark,
			status
		) VALUES (
			#{company.recordId},
			#{user.recordId},
			#{process.recordId},
			#{createdBy.recordId},
			#{createdDate},
			#{DEL_FLAG_NORMAL},
			#{remark},
			#{status}
		)
	</insert>

	<select id="findList" resultType="UserProcess">
		SELECT
			*
		FROM
		(
			SELECT
				a.recordId,
				su.recordId AS "user.recordId",
				su.userCode AS "user.userCode",
				su.userName AS "user.userName",
				su.position AS "user.position",
				su.status AS "user.status",
				su.birthday AS "user.birthday",
				su.hiredDate AS "user.hiredDate",
				su.phone AS "user.phone",
				su.mobile AS "user.mobile",
				su.email AS "user.email",
				su.remark AS "user.remark",	
				IFNULL(su.skipCount,0) AS "user.skipCount",
				IFNULL(su.checkAllQc,0) AS "user.checkAllQc",
				GROUP_CONCAT(DISTINCT ep.recordId) AS "processIds",
				GROUP_CONCAT(DISTINCT ep.category) AS "processNames",
				md.recordId AS "user.departmentId",
				md.`name` AS "user.deptName",
				a.createdDate,
				a.status,
				IFNULL(su.multipleCount,0) AS "user.multipleCount",
				IFNULL(su.takeHandCount,0) AS "user.takeHandCount"
			FROM pd_user_process_relation a
			LEFT JOIN sm_user su ON su.recordId = a.userId AND su.companyId = a.companyId AND su.activeFlag = 1 AND su.userType = 0
			LEFT JOIN eg_process ep ON ep.recordId = a.processId AND ep.activeFlag = 1
			LEFT JOIN sm_user_department sud ON sud.userId = su.recordId AND sud.companyId = a.companyId AND sud.activeFlag = 1
			LEFT JOIN md_department md ON md.recordId = sud.departmentId AND md.companyId = a.companyId AND md.activeFlag = 1
			<where>
				a.companyId = #{company.recordId} AND a.activeFlag = 1
				<if test="user != null ">
					<!-- 用户编号 -->
					<if test="user.userCode != null and user.userCode != ''">
						AND REPLACE(su.userCode," ","") like CONCAT('%', #{user.userCode}, '%')
					</if>
					<!-- 用户姓名 -->
					<if test="user.userName != null and user.userName != ''">
						AND REPLACE(su.userName," ","") like CONCAT('%', #{user.userName}, '%')
					</if>
					<!-- 所属部门 -->
					<if test="user.departmentId != null and user.departmentId != ''">
						AND md.recordId = #{user.departmentId}
					</if>
					<!-- 职务 -->
					<if test="user.position != null and user.position != ''">
						AND REPLACE(su.position," ","") like CONCAT('%', #{user.position}, '%')
					</if>
					<!-- 在职状态 -->
					<if test="user.status != null and user.status != ''">
						AND su.status = #{user.status}
					</if>
				</if>
				GROUP BY su.recordId
			</where>
		) aa
		<where>
			1=1
			<!-- 工序组 -->
			<if test="processIds != null and processIds != ''">
				 AND aa.processIds LIKE CONCAT('%',#{processIds},'%')
			</if>
			ORDER BY aa.createdDate
		</where>
		
	</select>

	<select id="get" resultType="UserProcess">
		SELECT
			recordId,
			companyId AS "company.recordId",
			userId AS "user.recordId",
			processId AS "process.recordId",
			activeFlag,
			createdBy AS "createdBy.recordId",
			createdDate,
			lastUpdBy AS "lastUpdBy.recordId",
			lastUpdDate,
			remark,
			status
		FROM pd_user_process_relation
		WHERE recordId = #{recordId}
	</select>
	
	<update id="delete">
		UPDATE pd_user_process_relation SET
			activeFlag = 0
		WHERE recordId = #{recordId}
	</update>
	
	<!-- zjn 2019-08-30 删除用户所有的用户工序 -->
	<delete id="deleteUserProcessByUser">
		DELETE FROM pd_user_process_relation WHERE companyId = #{company.recordId} AND userId = #{user.recordId}
	</delete>
	
	<!-- zjn 2019-08-30 批量添加用户工序-->
	<insert id="batchInsertUp" keyProperty="recordId" useGeneratedKeys="true">
		INSERT INTO pd_user_process_relation
		(
			companyId,
			userId,
			processId,
			createdBy,
			createdDate,
			lastUpdBy,
			lastUpdDate,
			activeFlag,
			status
		) 
		SELECT
			#{company.recordId},
			#{user.recordId},
			recordId,
			#{createdBy.recordId},
			#{createdDate},
			#{lastUpdBy.recordId},
			#{lastUpdDate},
			1,
			#{status}
		FROM eg_process
		WHERE recordId IN (${processIds})
	</insert>
	
</mapper>