package com.kyb.pcberp.modules.approval.dao;

import java.util.List;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.approval.entity.Allocation;
import com.kyb.pcberp.modules.approval.entity.AllocationRole;
import com.kyb.pcberp.modules.approval.entity.Approval;
import com.kyb.pcberp.modules.crm.entity.QuoteSetup;
import com.kyb.pcberp.modules.crm.entity.RejectApplication;
import com.kyb.pcberp.modules.sys.entity.EmployeePosition;
import org.apache.ibatis.annotations.Param;

@MyBatisDao
public interface AllocationRoleDao extends CrudDao<AllocationRole>
{
    /** tj 2018-12-17 查询审批配置角色 */
    public List<AllocationRole> findRoleByAllocation(Allocation allocation);
    
    /** 保存审批角色 */
    public void saveApprovalRole(AllocationRole allocationRole);
    
    /** 根据审批配置删除 */
    public void deleteByApprovalId(Allocation allocation);

    /** 重制审批全部角色状态 */
    public void approvalRoleStatuses(Approval approval);

    /** 修改审批角色状态 */
    public void approvalRoleStatus(AllocationRole role);
    
    /** tj 2018-12-17 查询审批角色 */
    public List<AllocationRole> findRoleByApproval(Approval approval);

    /** tj 2018-01-9 保存审批人和审批意见 */
    public void saveApprovalBy(AllocationRole role);

    EmployeePosition getPrevious(EmployeePosition employeePosition);

    EmployeePosition getLast(EmployeePosition employeePosition);

    void updateSortNum(@Param("list") List<EmployeePosition> updateList, @Param("userId") String userId);

    List<AllocationRole> getRejectRoleData(RejectApplication rejectApplication);

    void updateApprovalRoleStatuses(@Param("recordId") String recordId);
}
