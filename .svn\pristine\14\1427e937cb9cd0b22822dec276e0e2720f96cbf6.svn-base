<%@ page contentType="text/html;charset=UTF-8" %>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">生产管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="production.prepare">准备跟进</a>
        </li>
    </ul>
</div>
<!-- END 导航-->

<tabset class="tabset-margin-top">
    <tab heading="准备跟进" active="prepareCtrl.tabs.viewForm.active">
        <div class="panel panel-default">
            <div class="panel-heading font-blue-hoki">查询</div>
            <div class="panel-body">
                <form class="form-horizontal">
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">时间：</label>
                            <div class="col-sm-7 col-md-8">
                                <div class="input-prepend input-group">
                                    <span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
                                    <input type="text" class="form-control" disable-auto-validate="true"
                                           ng-blur="prepareCtrl.initDate(prepareCtrl.time)"
                                           kyb-daterange
                                           kyb-daterange-options="prepareCtrl.rangeOptions"
                                           ng-model="prepareCtrl.time"
                                           placeholder="请选择时间段">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4">
                        <div class="form-group">
                            <label class="col-sm-3 col-md-4 control-label">状态：</label>
                            <div class="col-sm-7 col-md-8">
                                <select  class="form-control" ng-model="prepareCtrl.statusQuery">
                                    <option value="">所有</option>
                                    <option value="1">必须</option>
                                    <option value="2">非必须</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <button class="btn btn-default btn-default-width pull-right"
                                    ng-click="prepareCtrl.loadData()">
                                <i class="fa fa-search"></i> 查&nbsp;询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="rows">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">准备跟进</div>
                </div>
                <div class="portlet-body">
                    <div id="step1" class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                            <thead>
                            <tr class="heading">
                                <th style="text-align: center;width: 3%;">序号</th>
                                <th style="text-align: center;">通知单号</th>
                                <th style="text-align: center;" >投料单号</th>
                                <th style="text-align: center;" >批次号</th>
                                <th style="text-align: center;" >厂编</th>
                                <th style="text-align: center;" >客户订单号</th>
                                <th style="text-align: center;" ng-repeat="detailName in prepareCtrl.detailNameList">{{detailName.name}}</th>
                            </tr>
                            <tr>
                                <td></td>
                                <td>
                                    <input type="text" class="form-control" ng-model="prepareCtrl.notiNoA">
                                </td>
                                <td>
                                    <input type="text" class="form-control" ng-model="prepareCtrl.feedNoA">
                                </td>
                                <td>
                                    <input type="text" class="form-control" ng-model="prepareCtrl.batchNoA">
                                </td>
                                <td>
                                    <input type="text" class="form-control" ng-model="prepareCtrl.craftNoA">
                                </td>
                                <td>
                                    <input type="text" class="form-control" ng-model="prepareCtrl.customerPoA">
                                </td>
                                <td ng-repeat="detailName in prepareCtrl.detailNameList"></td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in prepareCtrl.prepareList |
                                 filter:{
                            'notiNo':prepareCtrl.notiNoA,
                            'feedNo':prepareCtrl.feedNoA,
                            'batchNo':prepareCtrl.batchNoA,
                            'craftNo':prepareCtrl.craftNoA,
                            'customerPo':prepareCtrl.customerPoA
                                } track by $index" ng-if="row.showFlag">
                                <td style="text-align: center;" nowrap="nowrap" ng-bind="$index + 1"></td>
                                <td style="text-align: center;" nowrap="nowrap" ng-bind="row.notiNo"></td>
                                <td style="text-align: center;" nowrap="nowrap" ng-bind="row.feedNo"></td>
                                <td style="text-align: center;" nowrap="nowrap" ng-bind="row.batchNo"></td>
                                <td style="text-align: center;" nowrap="nowrap" ng-bind="row.craftNo"></td>
                                <td style="text-align: center;" nowrap="nowrap" ng-bind="row.customerPo"></td>
                                <td style="text-align: center;" nowrap="nowrap" ng-repeat="detailValue in row.detailList" ng-class="{'success': prepareCtrl.isStatus(detailValue)}">
                                    <span ng-if="detailValue.userName" style="float: left">{{detailValue.userName}}</span><br>
                                    <span ng-if="detailValue.showStatus" style="float: left">{{detailValue.showStatus}}</span>
                                    <span ng-if="!detailValue.showStatus" style="float: left">无</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </tab>

</tabset>

<div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">
                    <span>提示</span>
                </h4>
            </div>
            <div class="modal-body">
                <p>
                    <span ng-bind="discardsCtrl.message"></span>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
            </div>
        </div>
    </div>
</div>

