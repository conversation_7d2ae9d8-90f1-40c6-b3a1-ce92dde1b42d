<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="row pt-2" style="padding-left: 1rem; padding-right: 1rem;" v-if="showData && showData.complaintArea">
    <div class="col-xl-12">
        <div class="text-muted" style="font-weight: bold;color: white">客诉统计</div>
        <div class="card card-custom bg-light-danger gutter-b text-dark">
            <div class="card-body d-flex flex-column p-2">
                <div class="row align-items-center text-dark">
                    <div class="col">
                        <div class="text-muted">客诉面积(㎡)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.complaintArea ? showData.complaintArea : 0)}}</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">补发面积(㎡)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.fedArea ? showData.fedArea : 0)}}</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">客诉款数</div>
                        <span class="text-primary font-weight-bolder">{{showData.count ? showData.count : 0}}</span>
                    </div>
                </div>
                <div class="row align-items-center text-dark">
                    <div class="col">
                        <div class="text-muted">退货金额(元)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.returnAmount ? showData.returnAmount : 0)}}</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">赔偿金额(元)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.compensationAmount ? showData.compensationAmount : 0)}}</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">补发金额(元)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.fedAmount ? showData.fedAmount : 0)}}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="text-muted" style="font-weight: bold;color: white">品质统计</div>
        <div class="card card-custom bg-light-danger gutter-b text-dark">
            <div class="card-body d-flex flex-column p-2">
                <div class="row align-items-center text-dark">
                    <div class="col">
                        <div class="text-muted">不良(㎡)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.sumBadnessArea ? showData.sumBadnessArea : 0)}}</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">报废(㎡)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.sumScrapArea ? showData.sumScrapArea : 0)}}</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">返工(㎡)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.sumReworkArea ? showData.sumReworkArea : 0)}}</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">补料(㎡)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.sumSupplementaryFeedingArea ? showData.sumSupplementaryFeedingArea : 0)}}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card card-custom bg-light-primary gutter-b" v-if="showData.causeList && showData.causeList.length > 0">
            <div class="card-body d-flex flex-column p-2">
                <div class="row">
                    <div class="col-8">
                        <span class="text-muted">点击不同原因会按对应的原因进行统计</span>
                    </div>
                    <div class="col text-right" v-if="showData.causeList && showData.causeList.length > 3">
                        <button class="btn btn-sm btn-outline-primary" v-on:click="openClose(5)">
                            <span v-if="!disOpenFlag">展开</span>
                            <span v-else>收起</span>
                        </button>
                    </div>
                </div>
                <div class="row text-dark font-weight-bold">
                    <div class="col-xl-12 text-primary font-weight-bolder">
                        {{reportData.showDate}}
                    </div>
                </div>
                <div class="row text-dark pt-2" v-for="item in showData.cockpitCopyList">
                    <div class="col-12">
                        <div class="row text-primary">
                            <div class="col-xl-12">
                                <span class="font-weight-bolder">{{item.manufacturer}}</span>
                            </div>
                        </div>
                        <div class="row text-muted">
                            <div class="col-4">
                                <div class="text-muted">不良(㎡)&nbsp;
                                    <i :class="item.badnessAreaFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.badnessAreaRate ? item.badnessAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.badnessArea ? item.badnessArea : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">返工(㎡)&nbsp;
                                    <i :class="item.reworkAreaFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.reworkAreaRate ? item.reworkAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.reworkArea ? item.reworkArea : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">报废(㎡)&nbsp;
                                    <i :class="item.scrapAreaFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.scrapAreaRate ? item.scrapAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.scrapArea ? item.scrapArea : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">补料(㎡)&nbsp;
                                    <i :class="item.supplementaryFeedingAreaFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.supplementaryFeedingAreaRate ? item.supplementaryFeedingAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.supplementaryFeedingArea ? item.supplementaryFeedingArea : 0)}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card card-custom bg-light-primary gutter-b" v-if="showData.groupDeptList && showData.groupDeptList.length > 0">
            <div class="card-body d-flex flex-column p-2">
                <div class="row">
                    <div class="col-8">
                        <span class="text-muted">点击不同部门会按对应的部门进行统计</span>
                    </div>
                    <div class="col text-right" v-if="showData.groupDeptList && showData.groupDeptList.length > 3">
                        <button class="btn btn-sm btn-outline-primary" v-on:click="openClose(1)">
                            <span v-if="!deptOpenFlag">展开</span>
                            <span v-else>收起</span>
                        </button>
                    </div>
                </div>
                <div class="row text-dark font-weight-bold">
                    <div class="col-xl-12 text-primary font-weight-bolder">
                        {{reportData.showDate}}
                    </div>
                </div>
                <div class="row text-dark pt-2" v-for="item in showData.groupDeptCopyList">
                    <div class="col-12">
                        <div class="row text-primary">
                            <div class="col-xl-12">
                                <span class="font-weight-bolder">{{item.groupOrgName}}</span>
                            </div>
                        </div>
                        <div class="row text-muted">
                            <div class="col-4">
                                <div class="text-muted">不良(㎡)&nbsp;
                                    <i :class="item.badnessAreaFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.badnessAreaRate ? item.badnessAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.badnessArea ? item.badnessArea : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">返工(㎡)&nbsp;
                                    <i :class="item.reworkAreaFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.reworkAreaRate ? item.reworkAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.reworkArea ? item.reworkArea : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">报废(㎡)&nbsp;
                                    <i :class="item.scrapAreaFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.scrapAreaRate ? item.scrapAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.scrapArea ? item.scrapArea : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">补料(㎡)&nbsp;
                                    <i :class="item.supplementaryFeedingAreaFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.supplementaryFeedingAreaRate ? item.supplementaryFeedingAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.supplementaryFeedingArea ? item.supplementaryFeedingArea : 0)}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card card-custom bg-light-primary gutter-b" v-if="showData.customerList && showData.customerList.length > 0">
            <div class="card-body d-flex flex-column p-2">
                <div class="row">
                    <div class="col-8">
                        <span class="text-muted">点击不同客户会按对应的客户进行统计</span>
                    </div>
                    <div class="col text-right" v-if="showData.customerList && showData.customerList.length > 3">
                        <button class="btn btn-sm btn-outline-primary" v-on:click="openClose(2)">
                            <span v-if="!customerOpenFlag">展开</span>
                            <span v-else>收起</span>
                        </button>
                    </div>
                </div>
                <div class="row text-dark font-weight-bold">
                    <div class="col-xl-12 text-primary font-weight-bolder">
                        {{reportData.showDate}}
                    </div>
                </div>
                <div class="row text-dark pt-2" v-for="item in showData.customerCopyList">
                    <div class="col-12">
                        <div class="row text-primary">
                            <div class="col-xl-12">
                                <span class="font-weight-bolder">{{item.shortName}}</span>
                            </div>
                        </div>
                        <div class="row text-muted">
                            <div class="col-4">
                                <div class="text-muted">不良(㎡)&nbsp;
                                    <i :class="item.badnessAreaFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.badnessAreaRate ? item.badnessAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.badnessArea ? item.badnessArea : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">返工(㎡)&nbsp;
                                    <i :class="item.reworkAreaFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.reworkAreaRate ? item.reworkAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.reworkArea ? item.reworkArea : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">报废(㎡)&nbsp;
                                    <i :class="item.scrapAreaFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.scrapAreaRate ? item.scrapAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.scrapArea ? item.scrapArea : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">补料(㎡)&nbsp;
                                    <i :class="item.supplementaryFeedingAreaFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.supplementaryFeedingAreaRate ? item.supplementaryFeedingAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.supplementaryFeedingArea ? item.supplementaryFeedingArea : 0)}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>