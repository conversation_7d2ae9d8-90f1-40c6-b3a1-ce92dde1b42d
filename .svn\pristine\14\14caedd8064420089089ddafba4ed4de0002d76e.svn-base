package com.kyb.pcberp.modules.contract.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.kyb.pcberp.common.persistence.DataEntity;

public class GroupMaterialMoney extends DataEntity<GroupMaterialMoney>
{
    
    /**
     * 
     */
    private static final long serialVersionUID = 7375855085438887634L;
    
    private String groupCenterId;
    
    private String inStoreId;
    
    private String materialId;
    
    private Integer quantity;
    
    private Integer remainQty;
    
    private BigDecimal price;
    
    private BigDecimal purPrice;
    
    private String status;
    
    private BigDecimal amount;
    
    private Date operateDate; // 投料出库时间
    
    private Integer orderDetailQty; // 被合单明细订量
    
    private Integer orderAllQty;// 总订量
    
    private String mergeType;// 合单标示
    
    private String companyId;
    
    private String newPurRecordId;// 最新采购入库id - 需要跟进这个查询加权平均价
    
    private Date initPriceDate;// 最近的价格滚存日期
    
    private Integer currStock;
    
    private BigDecimal cost;
    
    private Integer orderQuantity;
    
    private Integer scdQuantity;
    
    public String getGroupCenterId()
    {
        return groupCenterId;
    }
    
    public void setGroupCenterId(String groupCenterId)
    {
        this.groupCenterId = groupCenterId;
    }
    
    public String getInStoreId()
    {
        return inStoreId;
    }
    
    public void setInStoreId(String inStoreId)
    {
        this.inStoreId = inStoreId;
    }
    
    public String getMaterialId()
    {
        return materialId;
    }
    
    public void setMaterialId(String materialId)
    {
        this.materialId = materialId;
    }
    
    public Integer getQuantity()
    {
        return quantity == null ? 0 : quantity;
    }
    
    public Integer getRemainQty()
    {
        return remainQty == null ? 0 : remainQty;
    }
    
    public BigDecimal getPrice()
    {
        return price;
    }
    
    public void setPrice(BigDecimal price)
    {
        this.price = price;
    }
    
    public BigDecimal getPurPrice()
    {
        return purPrice;
    }
    
    public void setPurPrice(BigDecimal purPrice)
    {
        this.purPrice = purPrice;
    }
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public BigDecimal getAmount()
    {
        return amount;
    }
    
    public void setAmount(BigDecimal amount)
    {
        this.amount = amount;
    }
    
    public Date getOperateDate()
    {
        return operateDate;
    }
    
    public void setOperateDate(Date operateDate)
    {
        this.operateDate = operateDate;
    }
    
    public Integer getOrderDetailQty()
    {
        return orderDetailQty;
    }
    
    public void setOrderDetailQty(Integer orderDetailQty)
    {
        this.orderDetailQty = orderDetailQty;
    }
    
    public Integer getOrderAllQty()
    {
        return orderAllQty;
    }
    
    public void setOrderAllQty(Integer orderAllQty)
    {
        this.orderAllQty = orderAllQty;
    }
    
    public String getMergeType()
    {
        return mergeType;
    }
    
    public void setMergeType(String mergeType)
    {
        this.mergeType = mergeType;
    }
    
    public void setQuantity(Integer quantity)
    {
        this.quantity = quantity;
    }
    
    public void setRemainQty(Integer remainQty)
    {
        this.remainQty = remainQty;
    }
    
    public String getCompanyId()
    {
        return companyId;
    }
    
    public void setCompanyId(String companyId)
    {
        this.companyId = companyId;
    }
    
    public String getNewPurRecordId()
    {
        return newPurRecordId;
    }
    
    public void setNewPurRecordId(String newPurRecordId)
    {
        this.newPurRecordId = newPurRecordId;
    }
    
    public Date getInitPriceDate()
    {
        return initPriceDate;
    }
    
    public void setInitPriceDate(Date initPriceDate)
    {
        this.initPriceDate = initPriceDate;
    }
    
    public Integer getCurrStock()
    {
        return currStock;
    }
    
    public void setCurrStock(Integer currStock)
    {
        this.currStock = currStock;
    }
    
    public BigDecimal getCost()
    {
        return cost;
    }
    
    public void setCost(BigDecimal cost)
    {
        this.cost = cost;
    }

    public Integer getOrderQuantity()
    {
        return orderQuantity;
    }

    public void setOrderQuantity(Integer orderQuantity)
    {
        this.orderQuantity = orderQuantity;
    }

    public Integer getScdQuantity()
    {
        return scdQuantity;
    }

    public void setScdQuantity(Integer scdQuantity)
    {
        this.scdQuantity = scdQuantity;
    }
    
    
    
}
