<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="row pt-2" style="padding-left: 1rem; padding-right: 1rem;" v-if="showData && showData.sumArea">
    <div class="col-xl-12">
        <div class="text-muted" style="font-weight: bold;color: white">采购统计</div>
        <div class="card card-custom bg-light-danger gutter-b text-dark">
            <div class="card-body d-flex flex-column p-2">
                <div class="row align-items-center text-dark">
                    <div class="col">
                        <div class="text-muted">面积(㎡)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.sumArea ? showData.sumArea : 0)}}</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">金额(元)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.sumAmount ? showData.sumAmount : 0)}}</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">款数</div>
                        <span class="text-primary font-weight-bolder">{{showData.sumCount ? showData.sumCount : 0}}</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <div class="text-muted">定向采购(元)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.targetedPurchasingAmount ? showData.targetedPurchasingAmount : 0)}}</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">比价下单(元)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.comparisonOrderAmount ? showData.comparisonOrderAmount : 0)}}</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">询价下单(元)</div>
                        <span class="text-primary font-weight-bolder">{{bigNumberTransform(showData.inquiryOrderAmount ? showData.inquiryOrderAmount : 0)}}</span>
                    </div>
                </div>
                <%--<div class="row align-items-center text-dark">
                    <div class="col">
                        <div class="text-muted">未对账(元)</div>
                        <span class="text-primary font-weight-bolder">等待查询</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">未付(元)</div>
                        <span class="text-primary font-weight-bolder">等待查询</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">已付(元)</div>
                        <span class="text-primary font-weight-bolder">等待查询</span>
                    </div>
                </div>
                <div class="row align-items-center text-dark">
                    <div class="col">
                        <div class="text-muted">到料准时率</div>
                        <span class="text-primary font-weight-bolder">等待查询</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">累计未对账(元)</div>
                        <span class="text-primary font-weight-bolder">等待查询</span>
                    </div>
                    <div class="col">
                        <div class="text-muted">累计未付(元)</div>
                        <span class="text-primary font-weight-bolder">等待查询</span>
                    </div>
                </div>--%>
            </div>
        </div>
        <div class="card card-custom bg-light-danger gutter-b" v-if="showData.matList && showData.matList.length > 0">
            <div class="card-body d-flex flex-column p-2">
                <div class="row">
                    <div class="col-8">
                        <span class="text-muted">点击不同材料会按对应的材料进行统计</span>
                    </div>
                    <div class="col text-right" v-if="showData.matList && showData.matList.length > 3">
                        <button class="btn btn-sm btn-outline-primary" v-on:click="openClose(6)">
                            <span v-if="!matOpenFlag">展开</span>
                            <span v-else>收起</span>
                        </button>
                    </div>
                </div>
                <div class="row text-dark font-weight-bold">
                    <div class="col-xl-12 text-primary font-weight-bolder">
                        {{reportData.showDate}}
                    </div>
                </div>
                <div class="row text-dark pt-2" v-for="item in showData.matCopyList">
                    <div class="col-12">
                        <div class="row text-primary">
                            <div class="col-xl-12">
                                <span class="font-weight-bolder">{{item.no}}</span>
                            </div>
                        </div>
                        <div class="row text-muted">
                            <div class="col-4">
                                <div class="text-muted">剩余面积(㎡)</div>
                                <div>{{bigNumberTransform(item.area ? item.area : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">剩余数量</div>
                                <div>{{bigNumberTransform(item.stocks ? item.stocks : 0)}}</div>
                            </div>
                            <%--<div class="col-4">
                                <div class="text-muted">超期天数(天)</div>
                                <div>等待查询</div>
                            </div>--%>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card card-custom bg-light-primary gutter-b" v-if="showData.groupDeptList && showData.groupDeptList.length > 0">
            <div class="card-body d-flex flex-column p-2">
                <div class="row">
                    <div class="col-8">
                        <span class="text-muted">点击不同部门会按对应的部门进行统计</span>
                    </div>
                    <div class="col text-right" v-if="showData.groupDeptList && showData.groupDeptList.length > 3">
                        <button class="btn btn-sm btn-outline-primary" v-on:click="openClose(1)">
                            <span v-if="!deptOpenFlag">展开</span>
                            <span v-else>收起</span>
                        </button>
                    </div>
                </div>
                <div class="row text-dark font-weight-bold">
                    <div class="col-xl-12 text-primary font-weight-bolder">
                        {{reportData.showDate}}
                    </div>
                </div>
                <div class="row text-dark pt-2" v-for="item in showData.groupDeptCopyList">
                    <div class="col-12">
                        <div class="row text-primary">
                            <div class="col-xl-12">
                                <span class="font-weight-bolder">{{item.groupOrgName}}</span>
                            </div>
                        </div>
                        <div class="row text-muted">
                            <div class="col-4">
                                <div class="text-muted">报备面积(㎡)&nbsp;
                                    <i :class="item.areaRateFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.areaRate ? item.areaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.area ? item.area : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">已用面积(㎡)&nbsp;
                                    <i :class="item.useAreaRateFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.useAreaRate ? item.useAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.useArea ? item.useArea : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">待用面积(㎡)&nbsp;
                                    <i :class="item.waitUseAreaRateFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.waitUseAreaRate ? item.waitUseAreaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.waitUseArea ? item.waitUseArea : 0)}}</div>
                            </div>
                        </div>
                        <div class="row text-muted">
                            <div class="col-4">
                                <div class="text-muted">累计报备面积(㎡)</div>
                                <div>{{bigNumberTransform(item.totalArea ? item.totalArea : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">累计已用面积(㎡)</div>
                                <div>{{bigNumberTransform(item.totalUseArea ? item.totalUseArea : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">累计待用面积(㎡)</div>
                                <div>{{bigNumberTransform(item.totalWaitUseArea ? item.totalWaitUseArea : 0)}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card card-custom bg-light-primary gutter-b" v-if="showData.manufacturerList && showData.manufacturerList.length > 0">
            <div class="card-body d-flex flex-column p-2">
                <div class="row">
                    <div class="col-8">
                        <span class="text-muted">点击不同供应商会按对应的供应商进行统计</span>
                    </div>
                    <div class="col text-right" v-if="showData.manufacturerList && showData.manufacturerList.length > 3">
                        <button class="btn btn-sm btn-outline-primary" v-on:click="openClose(7)">
                            <span v-if="!suppOpenFlag">展开</span>
                            <span v-else>收起</span>
                        </button>
                    </div>
                </div>
                <div class="row text-dark font-weight-bold">
                    <div class="col-xl-12 text-primary font-weight-bolder">
                        {{reportData.showDate}}
                    </div>
                </div>
                <div class="row text-dark pt-2" v-for="item in showData.manufacturerCopyList">
                    <div class="col-12">
                        <div class="row text-primary">
                            <div class="col-xl-12">
                                <span class="font-weight-bolder">{{item.manufacturer}}</span>
                            </div>
                        </div>
                        <div class="row text-muted">
                            <div class="col-4">
                                <div class="text-muted">面积(㎡)&nbsp;
                                    <i :class="item.areaRateFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.areaRate ? item.areaRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.area ? item.area : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">款数&nbsp;
                                    <i :class="item.countRateFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.countRate ? item.countRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.count ? item.count : 0)}}</div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted">金额(元)&nbsp;
                                    <i :class="item.amountRateFlag ? riseClass:descendClass" style="font-size: 0.8rem;">{{item.amountRate ? item.amountRate : 0}}%</i>
                                </div>
                                <div class="text-primary font-weight-bolder">{{bigNumberTransform(item.amount ? item.amount : 0)}}</div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <div class="text-muted">定向采购(元)</div>
                                <span>{{bigNumberTransform(item.targetedPurchasingAmount ? item.targetedPurchasingAmount : 0)}}</span>
                            </div>
                            <div class="col">
                                <div class="text-muted">比价下单(元)</div>
                                <span>{{bigNumberTransform(item.comparisonOrderAmount ? item.comparisonOrderAmount : 0)}}</span>
                            </div>
                            <div class="col">
                                <div class="text-muted">询价下单(元)</div>
                                <span>{{bigNumberTransform(item.inquiryOrderAmount ? item.inquiryOrderAmount : 0)}}</span>
                            </div>
                        </div>
                        <%--<div class="row">
                            <div class="col">
                                <div class="text-muted">未对账(元)</div>
                                <span>等待查询</span>
                            </div>
                            <div class="col">
                                <div class="text-muted">未付(元)</div>
                                <span>等待查询</span>
                            </div>
                            <div class="col">
                                <div class="text-muted">已付(元)</div>
                                <span>等待查询</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <div class="text-muted">到料及时率</div>
                                <span>等待查询</span>
                            </div>
                            <div class="col">
                                <div class="text-muted">累计未对账(元)</div>
                                <span>等待查询</span>
                            </div>
                            <div class="col">
                                <div class="text-muted">累计未付(元)</div>
                                <span>等待查询</span>
                            </div>
                        </div>--%>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>