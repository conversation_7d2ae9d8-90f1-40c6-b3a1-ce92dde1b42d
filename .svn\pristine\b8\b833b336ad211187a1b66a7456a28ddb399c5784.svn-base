/**
 * 
 */
package com.kyb.pcberp.modules.sys.utils;

import java.util.*;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kyb.pcberp.common.enums.CommonEnums.DictItemEnum;
import com.kyb.pcberp.common.enums.CommonEnums.DictItemType;
import com.kyb.pcberp.common.utils.CacheUtils;
import com.kyb.pcberp.common.utils.Collections3;
import com.kyb.pcberp.common.utils.SpringContextHolder;
import com.kyb.pcberp.common.utils.StringUtils;
import com.kyb.pcberp.modules.sys.dao.DictCompanyDao;
import com.kyb.pcberp.modules.sys.dao.DictItemDao;
import com.kyb.pcberp.modules.sys.dao.DictValueDao;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.DictCompany;
import com.kyb.pcberp.modules.sys.entity.DictItem;
import com.kyb.pcberp.modules.sys.entity.DictValue;

import javax.jws.soap.SOAPBinding;

/**
 * 字典工具类
 * 
 * @version 2013-5-29
 */
public class DictUtils
{
    
    private static DictValueDao valueDao = SpringContextHolder.getBean(DictValueDao.class);
    
    private static DictItemDao itemDao = SpringContextHolder.getBean(DictItemDao.class);
    
    private static DictCompanyDao dictCompanyDao = SpringContextHolder.getBean(DictCompanyDao.class);
    
    public static final String CACHE_DICT_MAP = "dictMap";
    
    public static final String CACHE_DICT_ITEMS_MAP = "dictItemMap";
    
    private static Map<String, DictItem> getItems_values()
    {
        return getItems_values(UserUtils.getUser().getCompany());
    }
    
    private static Map<String, DictItem> loadDictItemAndValues(Company company)
    {
        // 新建当前公司的字典缓存
        Map<String, DictItem> datas = Maps.newHashMap();
        
        // 获取所有的字典项
        List<DictItem> dictItems = getItems();
        
        // 查询出当前公司的所有字典值
        List<DictValue> queryValues = valueDao.findAllList(new DictValue(company));
        
        // 循环所有的字典项，设置该字典项对应的字典值
        for (DictItem dictItem : dictItems)
        {
            // 待设置的字典值列表
            List<DictValue> waitValues = Lists.newArrayList();
            
            // 循环所有的字典值，将属于该字典项的字典值放入待设置的列表中
            for (DictValue dictValue : queryValues)
            {
                if (dictValue.getItem().getRecordId().equals(dictItem.getRecordId()))
                {
                    // 排序如果为空，设置默认值9999
                    if (StringUtils.isBlank(dictValue.getSeqNum()))
                    {
                        dictValue.setSeqNum("9999");
                    }
                    
                    waitValues.add(dictValue);
                }
            }
            
            // 排序
            sortDictValues(waitValues);
            
            // 克隆
            DictItem newObj = new DictItem(dictItem.getRecordId());
            newObj.setItemName(dictItem.getItemName());
            newObj.setItemType(dictItem.getItemType());
            newObj.setOperLevel(dictItem.getOperLevel());
            newObj.setValues(waitValues);
            
            // 放入待缓存的Map
            datas.put(newObj.getRecordId(), newObj);
        }
        
        return datas;
    }
    
    /**
     * 基本方法<br>
     * 查询所有的字典项并获取字典项对应的值列表，并按类型分类<BR>
     * 首先从缓存获取，否则数据库查询
     * 
     */
    private static Map<String, DictItem> getItems_values(Company company)
    {
        // 返回结果
        Map<String, DictItem> datas = null;
        
        // 缓存中不存在时就查询所有的字典项并放入缓存中
        @SuppressWarnings("unchecked")
        Map<String, Map<String, DictItem>> dictMap = (Map<String, Map<String, DictItem>>)CacheUtils.get(CACHE_DICT_MAP);
        if (dictMap == null)
        {
            dictMap = Maps.newHashMap();
            datas = loadDictItemAndValues(company);
            dictMap.put(company.getRecordId(), datas);
            CacheUtils.put(CACHE_DICT_MAP, dictMap);
        }
        else
        {
            
            datas = dictMap.get(company.getRecordId());
            if (datas == null)
            {
                datas = loadDictItemAndValues(company);
                dictMap.put(company.getRecordId(), datas);
            }
        }
        
        return datas;
    }
    
    /**
     * 仅获取所有的字典项
     * 
     * @return
     */
    public static List<DictItem> getItems()
    {
        @SuppressWarnings("unchecked")
        List<DictItem> items = (List<DictItem>)CacheUtils.get(CACHE_DICT_ITEMS_MAP);
        if (items == null)
        {
            items = itemDao.findAllList(new DictItem());
            CacheUtils.put(CACHE_DICT_ITEMS_MAP, items);
        }
        return items;
    }
    
    /**
     * 根据类型获取所有在Item项
     */
    public static List<DictItem> getItems(DictItemType itemType)
    {
        return getItems(itemType.getIndex());
    }
    
    /**
     * 根据类型获取所有在Item项
     */
    public static List<DictItem> getItems(Integer itemType)
    {
        List<DictItem> items = Lists.newArrayList();
        
        // 从缓存中获取，否则查询数据库
        for (DictItem item : getItems_values().values())
        {
            if ((item.getItemType() != null) && item.getItemType().equals(itemType))
            {
                items.add(item);
            }
        }
        
        return items;
    }
    
    /**
     * 获取字典项列表并根据传入项名称进行过滤
     * 
     * @param qDict
     * @return
     */
    public static List<DictItem> getItemsDoLike(DictItem qItem, Integer itemType)
    {
        List<DictItem> items = Lists.newArrayList();
        // 从缓存中获取，否则查询数据库
        for (DictItem item : getItems_values().values())
        {
            if (itemType != null)
            {
                if ((item.getItemType() == null) || !item.getItemType().equals(itemType))
                {
                    continue;
                }
            }
            
            // 过滤掉名称不符合条件的项
            if (StringUtils.isNotBlank(qItem.getItemName()))
            {
                if (!item.getItemName().contains(qItem.getItemName()))
                {
                    continue;
                }
                
            }
            if (itemType != null)
            {
                StringBuilder desc = new StringBuilder("当前已设置 ");
                int count = 0;
                for (DictValue value : item.getValues())
                {
                    if (value.getCraftPrice() != null && value.getCraftPrice().getPrice() != null)
                    {
                        count++;
                    }
                }
                desc.append(count);
                desc.append(" 项");
                
                if (count < item.getValues().size())
                {
                    desc.append("，还有 ");
                    desc.append(item.getValues().size() - count);
                    desc.append(" 项待设置");
                }
                
                item.setSetDesc(desc.toString());
            }
            
            items.add(item);
        }

        if (StringUtils.isNotBlank(qItem.getOrderBy()))
        {
            Boolean isAsc = qItem.getOrderBy().indexOf("ASC") != -1;
            sortDictItemsByName(items, isAsc);
        }
        return items;
    }
    
    /**
     * 获取所有的值列表
     * 
     * @return
     */
    public static List<DictValue> getValues()
    {
        // 返回所有的
        List<DictValue> values = Lists.newArrayList();
        
        // 从缓存中获取，否则查询数据库
        for (DictItem item : getItems_values().values())
        {
            for (DictValue value : item.getValues())
            {
                values.add(value);
            }
        }
        
        return values;
    }
    
    /**
     * 根据字典项得到对应的值列表,推荐使用
     * 
     * @param e
     * @return
     */
    public static List<DictValue> getValuesByItem(DictItemEnum e)
    {
        List<DictValue> list = new ArrayList<>();
        if (StringUtils.isBlank(UserUtils.getUser().getRecordId())){
            Company company = new Company();
            company.setRecordId("3");
            list = getValuesByItem(e.getIndex(),company);
        }else{
            list = getValuesByItem(e.getIndex(), UserUtils.getUser().getCompany());
        }
        List<DictValue> listCopy = new ArrayList<>();
        listCopy.addAll(list);
        // 标明该字典值是隐藏还是显示
        DictCompany dictCompany = new DictCompany();
        if (StringUtils.isBlank(UserUtils.getUser().getRecordId())){
            Company company = new Company();
            company.setRecordId("3");
            dictCompany.setCompany(company);
        }else{
            dictCompany.setCompany(UserUtils.getUser().getCompany());
        }
        List<DictCompany> listCompany = dictCompanyDao.getDictCompany(dictCompany);
        for (int i = 0; i < list.size(); i++)
        {
            for (int j = 0; j < listCompany.size(); j++)
            {
                if (listCompany.get(j) != null && listCompany.get(j).getDictValue() != null
                    && list.get(i).getRecordId().equals(listCompany.get(j).getDictValue().getRecordId()))
                {
                    for (int l = 0; l < listCopy.size(); l++)
                    {
                        if (list.get(i).getRecordId().equals(listCopy.get(l).getRecordId()))
                        {
                            listCopy.remove(l);
                        }
                    }
                }
            }
        }
        return listCopy;
    }
    
    public static List<DictValue> getValuesByItem(DictItemEnum e, Company company)
    {
        List<DictValue> list = getValuesByItem(e.getIndex(), company);
        List<DictValue> listCopy = new ArrayList<>();
        listCopy.addAll(list);
        // 标明该字典值是隐藏还是显示
        DictCompany dictCompany = new DictCompany();
        dictCompany.setCompany(company);
        List<DictCompany> listCompany = dictCompanyDao.getDictCompany(dictCompany);
        for (int i = 0; i < list.size(); i++)
        {
            for (int j = 0; j < listCompany.size(); j++)
            {
                if (listCompany.get(j) != null && listCompany.get(j).getDictValue() != null
                    && list.get(i).getRecordId().equals(listCompany.get(j).getDictValue().getRecordId()))
                {
                    for (int l = 0; l < listCopy.size(); l++)
                    {
                        if (list.get(i).getRecordId().equals(listCopy.get(l).getRecordId()))
                        {
                            listCopy.remove(l);
                        }
                    }
                }
            }
        }
        return listCopy;
    }
    
    public static List<DictValue> getValuesByItem(DictItemEnum e, String companyId)
    {
        List<DictValue> list = valueDao.findCompanysDictValue(e.getIndex(), companyId);
        List<DictValue> listCopy = new ArrayList<>();
        listCopy.addAll(list);
        // 标明该字典值是隐藏还是显示
        List<DictCompany> listCompany = dictCompanyDao.getDictCompanys(companyId);
        for (int i = 0; i < list.size(); i++)
        {
            for (int j = 0; j < listCompany.size(); j++)
            {
                if (listCompany.get(j) != null && listCompany.get(j).getDictValue() != null
                    && list.get(i).getRecordId().equals(listCompany.get(j).getDictValue().getRecordId()))
                {
                    for (int l = 0; l < listCopy.size(); l++)
                    {
                        if (list.get(i).getRecordId().equals(listCopy.get(l).getRecordId()))
                        {
                            listCopy.remove(l);
                        }
                    }
                }
            }
        }
        return listCopy;
    }
    
    /**
     * 根据字典项得到对应的值列表,不推荐使用
     * 
     * @param itemId
     * @return
     */
    public static List<DictValue> getValuesByItem(Integer itemId, Company company)
    {
        if (itemId == null)
        {
            return null;
        }
        
        return getValuesByItem(new DictItem(itemId.toString()), company);
    }
    
    /**
     * 根据字典项得到对应的值列表
     * 
     * @param qDict 需要字典项(id)
     * @return
     */
    public static List<DictValue> getValuesByItem(DictValue dict)
    {
        if (dict == null)
        {
            return null;
        }
        
        return getValuesByItem(dict.getItem(), UserUtils.getUser().getCompany());
    }
    
    /**
     * 根据字典项得到对应的值列表
     * 
     * @param qDict 需要企业对象(id)与字典项(id)
     * @return
     */
    public static List<DictValue> getValuesByItem(DictItem qitem, Company company)
    {
        if (qitem == null)
        {
            return null;
        }
        
        List<DictValue> values = null;
        DictItem item = getItems_values(company).get(qitem.getRecordId());
        if (item == null || item.getValues() == null)
        {
            return values;
        }
        
        return item.getValues();
    }

    /**
     * 根据主键查询值对象
     *
     * @param valueId 值ID
     * @return 对象
     */
    public static DictValue getValuesByItem(String valueId)
    {
        if (valueId == null)
        {
            return null;
        }
        return valueDao.get(valueId);
    }
    
    /**
     * 获取字典项对应的字典值列表并根据传入的字典值进行过滤
     * 
     * @param qDict(需要item)
     * @return
     */
    public static List<DictValue> getValuesByItemDoLike(DictValue qDict)
    {
        if (qDict.getItem() == null)
        {
            return null;
        }
        
        List<DictValue> values = Lists.newArrayList();
        // 获取该公司隐藏的字典值项
        for (DictValue value : getValuesByItem(qDict))
        {
            // 过滤掉名称不符合条件的项
            if (StringUtils.isNotBlank(qDict.getValue()))
            {
                if (!value.getValue().contains(qDict.getValue()))
                {
                    continue;
                }
            }
            value.setFlag("0");
            values.add(value);
        }
        // 标明该字典值是隐藏还是显示
        DictCompany dictCompany = new DictCompany();
        dictCompany.setCompany(UserUtils.getUser().getCompany());
        List<DictCompany> list = dictCompanyDao.getDictCompany(dictCompany);
        for (int i = 0; i < values.size(); i++)
        {
            for (int j = 0; j < list.size(); j++)
            {
                if (list.get(j) != null && list.get(j).getDictValue() != null
                    && values.get(i).getRecordId().equals(list.get(j).getDictValue().getRecordId()))
                {
                    values.get(i).setFlag("1");
                }
            }
        }
        return values;
    }
    
    /**
     * 导出使用
     * 
     * @param value 字典值
     * @param type 字典项
     * @param defaultId 默认ID(未找到将自动填充)
     * @return
     */
    public static String getDictValueId(String value, DictItemEnum type, String defaultId)
    {
        String result = defaultId;
        List<DictValue> dictValues = getValuesByItem(type);
        for (DictValue dictValue : dictValues)
        {
            if (dictValue.getValue().equals(value))
            {
                result = dictValue.getRecordId();
                break;
            }
        }
        return result;
    }
    
    /**
     * 导入使用
     * 
     * @param id 字典值id
     * @param type 字典项
     * @param defaultValue 默认值(未找到将自动填充)
     * @return
     */
    public static String getDictValue(String id, DictItemEnum type, String defaultValue)
    {
        String result = defaultValue;
        List<DictValue> dictValues = getValuesByItem(type);
        for (DictValue dictValue : dictValues)
        {
            if (dictValue.getRecordId().equals(id))
            {
                result = dictValue.getValue();
                break;
            }
        }
        return result;
    }
    
    public static String getDictValue(String id, DictItemEnum type, String defaultValue, Company company)
    {
        String result = defaultValue;
        List<DictValue> dictValues = getValuesByItem(type, company);
        for (DictValue dictValue : dictValues)
        {
            if (dictValue.getRecordId().equals(id))
            {
                result = dictValue.getValue();
                break;
            }
        }
        return result;
    }
    
    public static String getDictValue(Long id, DictItemEnum type, String defaultValue)
    {
        String dId = "";
        if (id != null)
        {
            dId = id.toString();
        }
        return getDictValue(dId, type, defaultValue);
    }
    
    public static String getDictValue(Long id, DictItemEnum type, String defaultValue, Company company)
    {
        String dId = "";
        if (id != null)
        {
            dId = id.toString();
        }
        return getDictValue(dId, type, defaultValue, company);
    }
    
    public static String getDictValue(Integer id, DictItemEnum type, String defaultValue)
    {
        String dId = "";
        if (id != null)
        {
            dId = id.toString();
        }
        return getDictValue(dId, type, defaultValue);
    }
    
    public static DictValue getDictValueObj(String id, DictItemEnum type)
    {
        List<DictValue> dictValues = getValuesByItem(type);
        for (DictValue dictValue : dictValues)
        {
            if (dictValue.getRecordId().equals(id))
            {
                return dictValue;
            }
        }
        return null;
    }
    
    public static List<DictValue> sortDictValues(List<DictValue> list)
    {
        Collections.sort(list, new Comparator<DictValue>()
        {
            public int compare(DictValue arg0, DictValue arg1)
            {
                return Integer.valueOf(arg0.getSeqNum()).compareTo(Integer.valueOf(arg1.getSeqNum()));
            }
        });
        return list;
    }
    
    public static List<DictItem> sortDictItemsByName(List<DictItem> list, Boolean isAsc)
    {
        Collections.sort(list, new Comparator<DictItem>()
        {
            public int compare(DictItem arg0, DictItem arg1)
            {
                if (isAsc)
                {
                    return arg0.getItemName().compareTo(arg1.getItemName());
                }
                else
                {
                    return arg1.getItemName().compareTo(arg0.getItemName());
                }
            }
        });
        return list;
    }
    
    public static void removeDictCache(Company company)
    {
        @SuppressWarnings("unchecked")
        Map<String, Map<String, DictItem>> dictMap = (Map<String, Map<String, DictItem>>)CacheUtils.get(CACHE_DICT_MAP);
        if (dictMap != null)
        {
            dictMap.remove(company.getRecordId());
        }
    }
    
    /**
     * 云平台下单专用
     */
    public static List<DictValue> getValuesByItemIcloud(DictItemEnum e, Company company)
    {
        List<DictValue> list = getValuesByItem(e.getIndex(), company);
        List<DictValue> listCopy = new ArrayList<>();
        listCopy.addAll(list);
        // 标明该字典值是隐藏还是显示
        DictCompany dictCompany = new DictCompany();
        dictCompany.setCompany(company);
        List<DictCompany> listCompany = dictCompanyDao.getDictCompany(dictCompany);
        for (int i = 0; i < list.size(); i++)
        {
            for (int j = 0; j < listCompany.size(); j++)
            {
                if (listCompany.get(j) != null && listCompany.get(j).getDictValue() != null
                    && list.get(i).getRecordId().equals(listCompany.get(j).getDictValue().getRecordId()))
                {
                    for (int l = 0; l < listCopy.size(); l++)
                    {
                        if (list.get(i).getRecordId().equals(listCopy.get(l).getRecordId()))
                        {
                            listCopy.remove(l);
                        }
                    }
                }
            }
        }
        return listCopy;
    }
    
    /**
     * zjn 2018-07-03 获取不含税的id
     */
    public static String getNoTaxId()
    {
        String taxDescriptId = "";
        
        DictValue dv = new DictValue();
        dv.setItem(new DictItem("19"));
        List<DictValue> dvList = DictUtils.getValuesByItem(dv);
        if (Collections3.isNotEmpty(dvList))
        {
            for (DictValue dictValue : dvList)
            {
                if ("不含税".equals(dictValue.getValue()))
                {
                    taxDescriptId = dictValue.getRecordId();
                    break;
                }
            }
        }
        return taxDescriptId;
    }
}
