<%@ page contentType="text/html;charset=UTF-8" %>
<tab active="dataCtrl.tabs.editForm.active" ng-show="dataCtrl.tabs.editForm.show">
    <tab-heading>生产数据详情 <i style="cursor: pointer" class="fa fa-times" ng-click="dataCtrl.hideEditForm()"></i></tab-heading>
    <div class="rows" >
        <div class="portlet light bordered">
            <div class="portlet-title">
                <div class="caption font-blue-hoki">生产数据{{dataCtrl.editTitle}}</div>
            </div>
            <div class="portlet-body" id="stepDetail1">
                <div class="alert alert-warning" ng-if="dataCtrl.complaint.hadComplaint||dataCtrl.inspect.hadInspect">
                    <span ng-if="dataCtrl.inspect.hadInspect">生产报废原因：{{dataCtrl.inspect.info}}<br/></span>
                    <div ng-if="dataCtrl.complaint.hadComplaint"></span> <strong>您好，您当前选择的生产编号存在客诉!</strong><br/>客诉原因：<span ng-bind-html="dataCtrl.complaint.info"></span></div>
                </div>

                <form class="form-horizontal" ng-init="dataCtrl.setFormScope(this)" novalidate="novalidate" ng-submit-force="true">
                    <div class="row"  id="stepDetail2">
                        <div class="col-md-6 col-lg-4" id="stepDetail3">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>通知单号:</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select theme="bootstrap" firstfocus="{{dataCtrl.focus.main}}"
                                               ng-model="dataCtrl.as" on-select="dataCtrl.selectNotifications($select.selected, $select.selected.craftNo)">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.no}}</ui-select-match>
                                        <ui-select-choices refresh="dataCtrl.seachOfData($select.search)" refresh-delay="300"
                                                           repeat="item.recordId as item in dataCtrl.notification"
                                                           infinite-scroll='dataCtrl.addMoreItemsOfData()' infinite-scroll-distance='3'>
                                            <div ng-bind-html="item.no | highlight: $select.search"></div>
                                            <small> <span style="color: blue;">&nbsp;&nbsp;&nbsp;&nbsp;{{item.craftNo}}</span></small>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4"  id="stepDetail4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"> <span class="required">*</span>生产批次:</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select theme="bootstrap" ng-model="dataCtrl.as123" on-select="dataCtrl.selectpici($select.selected.recordId)">
                                        <ui-select-match
                                                placeholder="请选择...">{{$select.selected.showNo}}
                                        </ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in dataCtrl.produceBatch | filter: $select.search">
                                            <div ng-bind-html="item.showNo | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4"  id="stepDetail5">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label"> <span class="required">*</span>批次明细:</label>
                                <div class="col-sm-7 col-md-8">
                                    <ui-select theme="bootstrap" ng-model="dataCtrl.a34" on-select="dataCtrl.selectpicidetail($select.selected.recordId,true)">
                                        <ui-select-match placeholder="请选择...">{{$select.selected.showNo}}
                                        </ui-select-match>
                                        <ui-select-choices repeat="item.recordId as item in dataCtrl.produceBatchDetailList | filter: $select.search">
                                            <div ng-bind-html="item.showNo | highlight: $select.search"></div>
                                        </ui-select-choices>
                                    </ui-select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">交货日期:</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="dataCtrl.feeded.deliveryDate" readonly="readonly"/>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">投料PNL数(A板):</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control"
                                           ng-model="dataCtrl.feeded.boardAPnlQty" readonly="readonly"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">投料PNL数(B板):</label>
                                <div class="col-sm-7 col-md-8">
                                    <input class="form-control" ng-model="dataCtrl.feeded.boardBPnlQty" readonly="readonly"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">投料PNL数(总):</label>
                                <div class="col-sm-7 col-md-8">
                                    <input class="form-control" ng-model="dataCtrl.feeded.boardQuantity" readonly="readonly"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">投料PCS数(总):</label>
                                <div class="col-sm-7 col-md-8">
                                    <input class="form-control" ng-model="dataCtrl.feeded.pcsQuantity" readonly="readonly"/>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-4">
                            <div class="form-group">
                                <label class="col-sm-3 col-md-4 control-label">生产编号:</label>
                                <div class="col-sm-7 col-md-8">
                                    <input type="text" class="form-control" ng-model="dataCtrl.feeded.craftNo" readonly="readonly"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="table-scrollable" style="margin-top:0px !important">
                    <table class="table table-striped table-bordered table-condensed table-advance table-hover">
                        <thead>
                        <tr class="heading">
                            <th style="text-align: center;">序号</th>
                            <th style="text-align: center;">生产工序 </th>
                            <th style="text-align: center;">接板数量</th>
                            <th style="text-align: center;">接板检数</th>
                            <th style="text-align: center;">接板时间</th>
                            <th style="text-align: center;">接板操作员</th>
                            <th style="text-align: center;">已交数量</th>
                            <th style="text-align: center;">交板数量</th>
                            <th style="text-align: center;">交板时间</th>
                            <th style="text-align: center;">交板操作员</th>
                            <th style="text-align: center;">类型</th>
                            <th style="text-align: center;">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="row in dataCtrl.produceRecordList" ng-class="{'success': dataCtrl.isSkipProcess(row), 'danger': dataCtrl.isNotFinished(row)}">
                            <td style="text-align: center;" ng-bind="$index + 1"></td>
                            <td style="text-align: center;" ng-bind="row.process.category"></td>
                            <td>
                                <span ng-if="row.takeOverQtyPcsT && row.takeOverQtyPcsT > 0">
                                    PNL&nbsp;A:{{row.takeOverQtyA ? row.takeOverQtyA : 0}}&nbsp;B:{{row.takeOverQtyB ? row.takeOverQtyB : 0}}<br/>
                                    SET&nbsp;A:{{row.takeOverQtySetA ? row.takeOverQtySetA : 0}}&nbsp;B:{{row.takeOverQtySetB ? row.takeOverQtySetB : 0}}<br/>
                                    PCS&nbsp;A:{{(row.takeOverQtyPcsA ? row.takeOverQtyPcsA : 0) | number : 0}}&nbsp;B:{{(row.takeOverQtyPcsB ? row.takeOverQtyPcsB : 0) | number : 0}}<br/>
                                    检数:A:{{row.takeCheckNumA}}&nbsp;B:{{row.takeCheckNumB}}
                                </span>
                            </td>
                            <td>
                                <div ng-if="!(row.handOverFlag == 2 || row.handOverFlag == 3 || row.handOverFlag == 4 || row.skipCount == 1 || ((!row.takeOverQtyPcsA || row.takeOverQtyPcsA == 0)&&(!row.takeOverQtyPcsB || row.takeOverQtyPcsB == 0)))">
                                    A<input style="width: 50px;" ng-model="row.takePcsA" ng-blur="dataCtrl.checkTakeNum(row,1)"/><br/>
                                    B<input style="width: 50px;" ng-model="row.takePcsB" ng-blur="dataCtrl.checkTakeNum(row,2)"/>
                                </div>
                            </td>
                            <td style="text-align: center;">
                                <div class="form-group">
                                    <input style="width: 100px;" type="text" ng-model="row.takeOverTime" ng-disabled = "row.handOverFlag != 1 || row.skipCount == 1"
                                           data-date-format="yyyy-MM-dd" data-date-type="number" data-min-date="02/10/1901" data-max-date="today" data-autoclose="1"
                                           daysOfWeekDisabled="false" name="birthday" placeholder="具体日期" bs-datepicker/>&nbsp;&nbsp;
                                    <input style="width: 100px;" type="text" size="8" placeholder="具体时间" ng-model="row.takeOverTime"
                                           ng-disabled="row.handOverFlag != 1 || row.skipCount == 1" data-time-format="HH:mm:ss" data-time-type="number" data-autoclose="1" bs-timepicker>
                                </div>
                            </td>
                            <td>
                                <div ng-if="row.skipCount != 1" style="text-align: center">
                                    <div class="form-group" ng-if="!row.recordId || (row.recordId && row.handOverFlag == 1)">
                                        <ui-select theme="bootstrap" ng-model="row.createdBy" style="width: 120px;">
                                            <ui-select-match
                                                    placeholder="">{{$select.selected.userName}}
                                            </ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in row.dataOperatorList | filter: $select.search" >
                                                <div ng-bind-html="item.userName | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                    <div class="form-group" ng-if="row.recordId && row.handOverFlag != 1">{{row.createdBy.userName}}</div>
                                </div>
                            </td>
                            <td>
                                <span ng-if="row.handOverQtyPcsT && row.handOverQtyPcsT > 0">
                                    PNL&nbsp;A:{{row.handOverQtyA ? row.handOverQtyA : 0}}&nbsp;B:{{row.handOverQtyB ? row.handOverQtyB : 0}}<br/>
                                    SET&nbsp;A:{{row.handOverQtySetA ? row.handOverQtySetA : 0}}&nbsp;B:{{row.handOverQtySetB ? row.handOverQtySetB : 0}}<br/>
                                    PCS&nbsp;A:{{(row.handOverQtyPcsA ? row.handOverQtyPcsA : 0) | number : 0}}&nbsp;B:{{(row.handOverQtyPcsB ? row.handOverQtyPcsB : 0) | number : 0}}<br/>
                                    PCS&nbsp;T:{{(row.handOverQtyPcsT ? row.handOverQtyPcsT : 0) | number : 0}}<br/>
                                </span>
                                <span class="text-danger" ng-if="row.mrb && !row.type">报废数:{{row.mrb}}</span>
                                <span class="text-danger" ng-if="row.type && row.type == 2 && ($index == 0 || ($index > 0 && dataCtrl.produceRecordList[$index - 1].type != dataCtrl.produceRecordList[$index].type))">回来数量:{{row.handOverQtyPcsA}}</span>
                            </td>
                            <td>
                                <div ng-if="!(row.handOverFlag == 3 || row.handOverFlag == 4 || row.skipCount == 1 || ((!row.takeOverQtyPcsA || row.takeOverQtyPcsA == 0)&&(!row.takeOverQtyPcsB || row.takeOverQtyPcsB == 0)))">
                                    A<input style="width: 50px;" ng-model="row.handPcsA" ng-blur="dataCtrl.checkHandNum(row,1)"/><br/>
                                    B<input style="width: 50px;" ng-model="row.handPcsB" ng-blur="dataCtrl.checkHandNum(row,2)"/>
                                </div>
                            </td>
                            <td  style="text-align: center; ">
                                <div class="form-group">
                                    <input 	style="width: 100px;" type="text" ng-model="row.handOverTime" ng-disabled = "!row.recordId || row.handOverFlag == 3 || row.handOverFlag == 4 || row.skipCount == 1"
                                              data-date-format="yyyy-MM-dd" data-date-type="number" data-min-date="02/10/1901" data-max-date="today" data-autoclose="1" daysOfWeekDisabled="false"
                                              placeholder="具体日期" name="birthday" bs-datepicker/>&nbsp;&nbsp;
                                    <input style="width: 100px;" type="text" size="8" placeholder="具体时间" ng-model="row.handOverTime"
                                           ng-disabled = "!row.recordId || row.handOverFlag == 3 || row.handOverFlag == 4 || row.skipCount == 1" data-time-format="HH:mm:ss" data-time-type="number" data-autoclose="1" bs-timepicker>
                                </div>
                            </td>
                            <td>
                                <div ng-if="row.skipCount != 1">
                                    <div class="form-group" ng-if="row.handOverFlag == 1 || row.handOverFlag == 2 || !row.recordId" style="text-align: center">
                                        <ui-select theme="bootstrap" ng-model="row.lastUpdBy" style="width: 120px;">
                                            <ui-select-match placeholder="">{{$select.selected.userName}}
                                            </ui-select-match>
                                            <ui-select-choices repeat="item.recordId as item in row.dataOperatorList | filter: $select.search">
                                                <div ng-bind-html="item.userName | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                    <div ng-if = "row.handOverFlag == 3 || row.handOverFlag == 4" ng-repeat="item in row.handNameList" style="text-align: left">
                                        {{item.name}}
                                    </div>
                                </div>
                            </td>
                            <td style="text-align: center;">
                                <span ng-if="!row.type">正常</span>
                                <span ng-if="row.type && row.type == 2">外发</span>
                            </td>
                            <td style="text-align: center;">
                                <a href="javascript:void(0)" title="保存" ng-if="dataCtrl.isCanSave(row)" ng-click="dataCtrl.isInventory(row)"><img src="assets/pages/img/save_24.png" /></a>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</tab>