<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" isErrorPage="true"%>
<div class="d-flex flex-column flex-root">
    <div class="d-flex flex-row flex-column-fluid page">
        <div class="d-flex flex-column flex-row-fluid wrapper" id="kt_wrapper">
            <div class="content d-flex flex-column flex-column-fluid" id="kt_content"
                 style="background-image: url(${pageContext.request.contextPath}/assets/global/metronic/assets/media/bg/bgMain.jpg);height: 160px;">
                <div class="subheader subheader-transparent" id="kt_subheader">
                    <div class="container d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
                        <div class="d-flex align-items-center flex-wrap mr-1 mb-3">
                            <div class="d-flex flex-column">
                                <h5 class="text-white font-weight-bold my-2 mr-5">信息完善</h5>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-column-fluid">
                    <div class="container">
                        <div class="row">
                            <div class="col-12 text-center">
                                <div class="card card-custom gutter-b card-stretch">
                                    <div class="row card-body p-0 align-items-center">
                                        <div class="col-12">
                                            <div class="d-flex flex-column flex-root">
                                                <div class="row bg-white">
                                                    <div class="col">
                                                        <div class="d-flex flex-column flex-lg-row flex-row-fluid">
                                                            <div class="flex-row-fluid d-flex flex-column position-relative p-7 overflow-hidden">
                                                                <div class="d-flex flex-center">
                                                                    <div class="login-form login-signin">
                                                                        <form class="form">
                                                                            <div class="form-group">
                                                                                <div class="row pb-1">
                                                                                    <div class="col text-left">
                                                                                        <button class="btn btn-primary" v-on:click="chooseImg()">头像更换</button>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="row pt-3 pb-1">
                                                                                    <div class="col text-left">
                                                                                        <div v-for="(item,index) in localIds" v-if="localIds && localIds.length > 0">
                                                                                            <div class="symbol symbol-success pr-3">
                                                                                                <img alt="Pic" :src="item" v-on:click="preview(item)"/>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class="symbol symbol-success pr-3" v-if="!(localIds && localIds.length > 0) && user.downloadUrl">
                                                                                            <img alt="Pic" :src="user.downloadUrl"/>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="form-group">
                                                                                <div class="row pb-1">
                                                                                    <div class="col text-left">个人识别码</div>
                                                                                </div>
                                                                                <input class="form-control form-control-solid" v-model="user.code" type="text" autocomplete="off" required>
                                                                            </div>
                                                                            <div class="form-group">
                                                                                <div class="row pb-1">
                                                                                    <div class="col text-left">性别</div>
                                                                                </div>
                                                                                <select class="form-control form-control-solid" type="text" v-model="user.gender" autocomplete="off">
                                                                                    <option value=""></option>
                                                                                    <option value="1">男</option>
                                                                                    <option value="2">女</option>
                                                                                </select>
                                                                            </div>
                                                                            <div class="form-group">
                                                                                <div class="row pb-1">
                                                                                    <div class="col text-left">邮箱</div>
                                                                                </div>
                                                                                <input class="form-control form-control-solid" type="text" v-model="user.email" autocomplete="off">
                                                                            </div>
                                                                            <div class="form-group">
                                                                                <div class="row pb-1">
                                                                                    <div class="col text-left">行业</div>
                                                                                </div>
                                                                                <input class="form-control form-control-solid" type="text" v-model="user.industry" autocomplete="off">
                                                                            </div>
                                                                            <div class="form-group">
                                                                                <div class="row pb-1">
                                                                                    <div class="col text-left">业务</div>
                                                                                </div>
                                                                                <input class="form-control form-control-solid" type="text" v-model="user.business" autocomplete="off">
                                                                            </div>
                                                                            <div class="form-group">
                                                                                <div class="row pb-1">
                                                                                    <div class="col text-left">网址</div>
                                                                                </div>
                                                                                <input class="form-control form-control-solid" type="text" v-model="user.webUrl" autocomplete="off">
                                                                            </div>
                                                                            <div class="form-group">
                                                                                <div class="row pb-1">
                                                                                    <div class="col text-left">地址</div>
                                                                                </div>
                                                                                <input class="form-control form-control-solid" type="text" v-model="user.contractAdress" autocomplete="off">
                                                                            </div>
                                                                            <div class="row pt-8">
                                                                                <div class="col text-center">
                                                                                    <button class="btn btn-primary" type="button" style="width: 25rem" v-on:click="editMsg">修&emsp;改</button>
                                                                                </div>
                                                                            </div>
                                                                        </form>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>