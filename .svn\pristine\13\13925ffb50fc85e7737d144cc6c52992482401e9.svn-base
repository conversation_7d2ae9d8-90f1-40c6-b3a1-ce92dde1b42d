package com.kyb.pcberp.modules.approval.web;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.kyb.pcberp.modules.approval.service.AllocationService;
import com.kyb.pcberp.modules.sys.entity.EmployeePosition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kyb.pcberp.common.mapper.JsonMapper;
import com.kyb.pcberp.common.persistence.Page;
import com.kyb.pcberp.common.web.BaseController;
import com.kyb.pcberp.modules.approval.entity.Allocation;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

@Controller
@RequestMapping(value = "${adminPath}/approval/approvalallocation")
public class AllocationController extends BaseController
{
    private final ObjectMapper contractMapper = new JsonMapper().enableSimple();
    
    private final MappingJackson2JsonView view = new MappingJackson2JsonView();
    
    @Autowired
    private AllocationService allocationService;
    
    @RequestMapping(value = "list")
    public String list()
    {
        return "modules/approval/allocation";
    }
    
    @RequestMapping(value = "remakePosition")
    @ResponseBody
    public String remakePosition()
    {
        String str = allocationService.remakePosition();
        return str;
    }
    
    /**
     * 加载数据
     * 
     * @param model
     * @return
     */
    @RequestMapping(value = "load/data")
    public View loadData(Boolean queryAll, Model model)
    {
        view.setObjectMapper(contractMapper);
        Map<String, Object> map = new HashMap<>();
        
        map = allocationService.loadData();
        
        model.addAttribute("data", map);
        
        return view;
    }
    
    @RequestMapping(value = "save")
    @ResponseBody
    public String save(@RequestBody Allocation allocation)
    {
        String str = allocationService.saveAllocation(allocation);
        return str;
    }
    
    /** tj 2018-12-16 审批配置page */
    @RequestMapping(value = {"page"})
    @ResponseBody
    public Page<Allocation> page(@RequestBody Allocation allocation, HttpServletRequest request,
        HttpServletResponse response)
    {
        allocation.setCompany(UserUtils.getUser().getCompany());
        
        if (allocation.getQueryAll() != null && !allocation.getQueryAll())
        {
            allocation.setCreatedBy(UserUtils.getUser());
        }
        
        Page<Allocation> page = allocationService.page(new Page<Allocation>(request, response), allocation);
        
        return page;
    }
    
    /** tj 2018-12-16 验证审批类型是否存在 */
    @RequestMapping(value = {"verificationtype"})
    @ResponseBody
    public String verificationtype(@RequestBody Allocation allocation)
    {
        allocation.setCompany(UserUtils.getUser().getCompany());
        String str = allocationService.verificationtype(allocation);
        return str;
    }
    
    @RequestMapping(value = {"delete"})
    @ResponseBody
    public String delete(@RequestBody Allocation allocation)
    {
        return allocationService.deleteAllocation(allocation);
    }

    @RequestMapping(value = {"getPositionList"})
    @ResponseBody
    public List<EmployeePosition> getPositionList(@RequestBody Allocation allocation)
    {
        return allocationService.getPositionList(allocation);
    }

    @RequestMapping(value = "upSort")
    @ResponseBody
    public String upSort(@RequestBody EmployeePosition employeePosition) {
        return allocationService.inSortNum(employeePosition, 1);
    }

    @RequestMapping(value = "downSort")
    @ResponseBody
    public String downSort(@RequestBody EmployeePosition employeePosition) {
        return allocationService.inSortNum(employeePosition, 2);
    }
}
