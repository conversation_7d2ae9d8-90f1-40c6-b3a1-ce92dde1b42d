package com.kyb.pcberp.modules.eg.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.eg.entity.CardA;
import com.kyb.pcberp.modules.eg.entity.EgMeshBoard;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface EgMeshBoardDao extends CrudDao<EgMeshBoard>
{
    List<EgMeshBoard> getMeshBoardList(String companyId);

    int saveMeshBoard(EgMeshBoard egMeshBoard);

    void deleteMeshBoard(@Param("list") List<EgMeshBoard> list);

    void confirmBoard(EgMeshBoard egMeshBoard);

    EgMeshBoard getMeshBoard(String recordId);
}
