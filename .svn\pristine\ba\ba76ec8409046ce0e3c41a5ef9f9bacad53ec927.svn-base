/**
 *
 */
package com.kyb.pcberp.modules.purch.dao;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.approval.vo.ContractDeailVo;
import com.kyb.pcberp.modules.approval.vo.ContractVo;
import com.kyb.pcberp.modules.purch.entity.*;
import com.kyb.pcberp.modules.report.entity.Report;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.RawmaterialStock;
import com.kyb.pcberp.modules.wechat.entity.InterProduct;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 原料采购明细DAO接口
 *
 * <AUTHOR>
 * @version 2015-09-15
 */
@MyBatisDao
public interface PurchasingDetailDao extends CrudDao<PurchasingDetail>
{

    List<PurchasingDetail> getPurchasingDetailByPurchasingId(Purchasing purchasing);

    /**
     * 查询出所有的原料采购明细
     *
     * @param rawPurchDetail
     * @return
     */
    List<PurchasingDetail> findAllListInStock(PurchasingDetail rawPurchDetail);

    List<PurchasingDetail> findPurDtlListByPurchasingId(Purchasing purchasing);

    List<PurchasingDetail> findPurDtlStatusByParentId(PurchasingDetail rawPurchDetail);

    PurchasingDetail getById(String recordId);

    void updateStatus(PurchasingDetail purchasingDtl);

    List<PurchasingDetail> findPurchasingDetailByPurchasingId(Purchasing purchasing);

    List<PurchasingDetail> findPrdByPurchasingId(Purchasing purchasing);

    List<PurchasingDetail> findPurchasingDetailByPurchasingIdByReport(Purchasing purchasing);

    Integer isExistOperateStatus(Purchasing pc);

    /**
     * ycy 2016-10-09 查询原料检测数量
     *
     * @param pc
     * @return
     */
    String getQcsourceDetectionQuantity(PurchasingDetail purchasingDtl);

    /**
     * ycy 2016-10-09 查询原料检测数量
     *
     * @param pc
     * @return
     */
    String getQcsourceDetectionQuantityTo(PurchasingDetail purchasingDtl);

    /**
     * ycy 2016-10-10 查询审核的原料采购数量
     *
     * @param purchasingDtl
     * @return
     */
    String getToExaminePurchasingDetailQuantity(PurchasingDetail purchasingDtl);

    /**
     * ycy 2016-10-10 查询成品检测数量
     *
     * @return
     */
    String getQcsourcePrdorderDetailQuantity(PrdorderDetail prdorderDetail);

    /**
     * ycy 2016-10-10 查询成品检测数量
     *
     * @return
     */
    String getQcsourcePrdorderDetailQuantityTo(PrdorderDetail prdorderDetail);

    /**
     * ycy 2016-10-10 查询审核的成品采购数量
     *
     * @return
     */
    String getToExaminePrdorderDetailQuantity(PrdorderDetail prdorderDetail);

    /**
     * ycy 2016-10-24 修改成品查找订单数量
     *
     * @return
     */
    String getEditPrdorderDetailQuantity(PrdorderDetail prdorderDetail);

    /**
     * ojh 2016.11.24 查询原料采购明细集合ids
     */
    List<Integer> findPurDtlIdListByPurchasingId(Purchasing purchasingFilter);

    /**
     * ojh 2016.11.24 查询成品采购明细集合ids
     */
    List<Integer> findPrdDtlListByPrdorderId(Purchasing purchasingFilter);

    /**
     * fzd 2016-12-12 获得原料采购引用的原料申购id
     *
     * @return
     */
    PurchasingDetail getPurRaw(@Param("recordId") String recordId);

    /**
     * fzd 2016-12-12 获得原料采购引用的原料申购id集合
     *
     * @return
     */
    List<PurchasingDetail> getPurRawList(@Param("recordId") String recordId);

    /**
     * fzd 2016-12-12 原料采购调整数量
     */
    void adjustPurchNum(PurchasingDetail puPurchasingDetail);

    /**
     * ycy 2016-12-22
     *
     * @param puPurchasingDetail
     * @return
     */
    PurchasingDetail getPurchasingDetailinformation(PuReturnsDetail pureturns);

    /**
     * tj 2017-06-12 查询采购明细 根据Id
     */
    PurchasingDetail findPurchasingDetailbyId(PurchasingDetail purchasingDetail);

    /**
     * WC 2017-08-01 查询采购明细的单价及数量
     *
     * @param puchasingDet
     * @return
     */
    PurchasingDetail getPuchDetPriceAndQty(PurchasingDetail puchasingDet);

    /**
     * WC 2017-08-02 更新采购明细amount
     */
    void updateDetailAmout(PurchasingDetail puchasingDet);

    /**
     * WC 2017-08-02 获取采购明细Amout
     */
    BigDecimal getDetailAmount(String recordId);

    /**
     * lh 2018-01-21 通过规格查询原材料价格
     */
    List<PurchasingDetail> queryPurchPriceList(PurchasingDetail purchasingDetail);

    /**
     * lh 2018-01-21 lh 云平台规格查询原材料价格
     */
    List<PurchasingDetail> queryPurchPriceListIcloud(Supplier supplier);

    /** zjn 2018-07-11 根据付款对账明细id获取原料采购备注 */
    PurchasingDetail getPurchasingDetailByCheckId(MaterialCheck materialCheck);

    /** zjn 2018-07-11 更新原料采购明细备注 */
    void updatePurchasingDetailRemark(PurchasingDetail purchasingDetail);

    /** lq 2018-12-03 冲红修改采购单来料检测的状态 */
    void updateDetailStatus(RawmaterialStock raw);

    /** lq 2018-12-03 冲红修改退货单明细的状态 */
    void updateReturnDetailStatus(RawmaterialStock raw);

    /** lq 2018-12-03 冲红修改采购单明细的状态 */
    void updatePurDetailStatus(RawmaterialStock raw);

    /** lq 2018-12-03 冲红修改采购单的状态 */
    void updatePurStatus(RawmaterialStock raw);

    /** lq 2019-01-25 最低采购单价 */
    String getlowPrice(Material material);

    /** tj 2019-03-14 查询原料采购明细状态 */
    PurchasingDetail findStatusInfo(PurchasingDetail detail);

    /** tj 2019-03-14 修改状态金额 */
    void updateStatusAndQuantity(PurchasingDetail detail);

    /** tj 2019-08-09 审批详情获取原料采购 */
    List<ContractDeailVo> getPurchasingByDetailId(ContractVo contractVo);

    /** tj 2019-05-05 查询原料采购明细状态 */
    List<PurchasingDetail> findDetailPrice(Purchasing purchasing);

    /** tj 201-05-07 统计未入库明细数量 */
    Integer countNotRawDetail(PurchasingDetail detail);

    /** tj 2019-06-14 WX原料修改审批详情 */
    List<ContractDeailVo> findPurchasingById(ContractVo contractVo);

    /** tj 2019-07-25 根据采购单获取明细 */
    List<PurchasingDetail> findDetailByPurchasing(Purchasing purchasing);

    List<PurchasingDetail> getPurDetailList(PurchRaw purchRaw);

    void updateDetailAmountData(PurchasingDetail purchasingDetail);

    List<Purchasing> loadPurchList(Supplier supplier);

    String isSupplierCenter(Purchasing purchasing);

    List<PurchasingDetail> getDeailById(Purchasing purchasing);

    List<PurchasingDetail> getMaterialContractDeailList(Purchasing purchasing);

    Integer getWaitPurchCout(PurchasingDetail purchasingDetail);
    
    List<PurchasingDetail> findPrdByPurchasingIdTwo(Purchasing purchasing);
    
    List<PurchasingDetail> getDetailById(PurchasingDetail purchasingDetail);
    
    Integer getUnInStoreCount(PurchasingDetail purchasingDetail);
    
    List<PurchasingDetail> getShowPurDetailList(PurchasingDetail purchasingDetail);

    List<PurchasingDetail> getReplenishDetailById(PurchasingDetail pd);
    
    void updateStatusByPur(Purchasing purchasing);
    
    void updateCraftDescript(Material material);

    List<PurchRaw> getPurchMatPreparationData(@Param("companyId") String companyId,@Param("matPreparationId") String matPreparationId);

    List<PurchasingDetail> getDetailData(@Param("companyId") String companyId,@Param("recordId") String recordId);

    void approvalUpdateStatus(PurchasingDetail purchasingDetail);

    String getStateType (@Param("purchasingId") String purchasingId, @Param("companyId") String companyId);

    String getapprovalStatus(Purchasing purchasing);

    Integer getDetailCount(Purchasing purchasing);

    List<PurchasingDetail> getDetailRecordId(Purchasing purchasing);

    List<PurchasingDetail> getReportPurchasingDetail(Report report);

    List<PurchasingDetail> getListByorgId(@Param("orgId") String orgId);

    List<PurchasingDetail> getPurDetailLists(String purchasingDetailId);

    void modifyStatus(PurchasingDetail purchasingDetail);

    List<PurchasingDetail> getDeailByIds(Purchasing purchasing);

    String getCheckDataId(@Param("purchingDetail") String purchingDetail,@Param("adviseDeliveryId") String adviseDeliveryId);

    void updateFlatUnitPrice(PurchasingDetail purchasingDetail);

    List<PurchasingDetail> getDetailId(String id);

    List<PurchasingDetail>getDateList(PurchasingDetail purchasingDetail);

    List<PurchasingDetail>toPrint(Purchasing purchasing);

    List<PurchasingDetail>toPrintThree(Purchasing purchasing);

    Integer getDetailStatus(PurchasingDetail purchasingDetail);
    List<PurchasingDetail> getWxDetailList(Purchasing purchasing);

    List<PurchasingDetail> getList(@Param("recordId") String recordId);

    List<PurchasingDetail> getPriceAndDelivery(@Param("materialId") String materialId);

    List<PurchasingDetail> getPriceAndDeliveryT(@Param("materialIds") String materialIds);

    Integer getMaterialType(@Param("purchasingDetailIds") String purchasingDetailIds);

    void batchInsertStockRecord(Purchasing purchasing);

    List<PurchasingDetail> getListByCompanyId(@Param("companyId") String companyId);

    List<PurchasingDetail> getPurchasingDetailList(PurchasingDetail purchasingDetail);

    void updateSurplusStockDate(@Param("list") List<PurchasingDetail> list);

    List<PurchasingDetail> getRemainPurchasingList(RawmaterialStock rawmaterialStock);

    void updateSurplusStock(PurchasingDetail purchasingDetail);

    List<PurchasingDetail> getPuDetaillList(@Param("list") List<String> list,@Param("companyId") String companyId);

    PurchasingDetail getPurchasingDetailDate(@Param("recordId") String recordId);

    PurchasingDetail getPurchasingDetailDateTwo(@Param("returnsNo") String returnsNo);

    PurchasingDetail getPurchasingDetailDateObj(@Param("recordId") String recordId);

    List<PurchasingDetail> getPurchasingDeList(@Param("recordId") String recordId);

    Integer getCountClosed(PurchasingDetail purchasingDetail);

    List<PurchasingDetail> getClosedDetailList(Purchasing purchasing);

    void closedDetailStatus(Purchasing purchasing);

    void updateClosedStatus(PurchasingDetail purchasingDetail);

    List<ContractDeailVo> getPurchasingByDetailIdTwo(ContractVo contractVo);

    String getDetailIds(Purchasing purchasing);

    PurchasingDetail getPurchasingDetailData(@Param("recordId") String recordId);

    void updateDetectionStatus(PurchasingDetail purchasingDetail);

    void blushStatus(PurchasingDetail purchasingDetail);

    void blushDetailStatus(PurchasingDetail purchasingDetail);

    void updateFlatUnitPriceTwo(PurchasingDetail purchasingDetail);

    List<ContractDeailVo> getApprovalList(ContractVo contractVo);

    List<PurchasingDetail>getPriceAlterList(ContractDeailVo contractDeailVo);

    List<PurchasingDetail> getListByInterProduct(InterProduct interProduct);
}