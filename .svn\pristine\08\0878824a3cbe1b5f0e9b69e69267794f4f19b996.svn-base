package com.kyb.pcberp.modules.hr.depart_center.dao;

import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.hr.depart_center.pojo.*;
import com.kyb.pcberp.modules.hr.emp_center.pojo.Hr_Employee;
import com.kyb.pcberp.modules.hr.group_center.pojo.Hr_DepartRelation;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.entity.GroupOrgRelation;
import com.kyb.pcberp.modules.sys.entity.User;
import com.kyb.pcberp.modules.wechat.entity.IcloudUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@MyBatisDao
public interface Hr_OrganizationDao {
    List<Hr_DepartMent> getOaGroup(@Param("groupManageId") String groupManageId, @Param("phone") String phone, @Param("salarySystemId") String salarySystemId);

    List<Hr_Employee> getOaEmpList(Hr_DepartMent depart);

    void addDepart(Hr_DepartMent depart);

    Integer getDepartNum(Hr_DepartMent depart);

    void editDepart(Hr_DepartMent depart);

    List<Hr_Employee> getEmpAssignmentList(Hr_Employee emp);

    Integer checkAssignmentEmp(Hr_Employee emp);

    void saveAssignmentEmp(Hr_Employee emp);

    void deleteAssignmentEmp(Hr_Employee emp);

    List<Hr_Position> getPositionList(Hr_Employee emp);

    void delPosition(Hr_Position position);

    Integer getApprovePo(Hr_Position position);

    Integer getPositionNum(Hr_Position position);

    void addPosition(Hr_Position position);

    void editPosition(Hr_Position position);

    List<Hr_Position> getAllPosition(IcloudUser user);

    void delPoEmp(Hr_PositionEmployee poEmp);

    Integer getPoEmpNum(Hr_PositionEmployee poEmp);

    void addPoEmp(Hr_PositionEmployee poEmp);

    Integer getDepartEmpNum(Hr_DepartMent depart);

    void delDepart(Hr_DepartMent depart);

    void delPoEmpByDept(Hr_DepartMent depart);

    List<Hr_PositionEmployee> getPoEmpList(Hr_Employee emp);

    List<Hr_OrganizationRetrospect> getOrganizationRetrospectList(IcloudUser user);

    Hr_OrganizationRetrospect getMaxOrganizationRetrospect();

    void insertOrganizationRetrospect(Hr_OrganizationRetrospect hr_OrganizationRetrospect);

    void backupDepartmentRelation(@Param("horId") String horId, @Param("userId") String userId);

    void updateDepartmentRelationAllId(@Param("horId") String horId);

    void backupEmployee(@Param("horId") String horId, @Param("userId") String userId);

    void updateEmployeeData(@Param("horId") String horId);

    void backupPositionEmployee(@Param("horId") String horId, @Param("userId") String userId);

    void backupDepartmentRelationEmployee(@Param("horId") String horId);

    void getInsertFramework(Hr_OrganizationRetrospect organizationRetrospect);

    void getUpdateFramework(Hr_OrganizationRetrospect organizationRetrospect);

    void getDelFramework(Hr_OrganizationRetrospect organizationRetrospect);

    List<Hr_TaskManagement> getTaskManagementList(Hr_TaskManagement hr_taskManagement);

    Integer getNameNum(Hr_DepartRelation groupDepartRelation);

    void insert(Hr_DepartRelation groupDepartRelation);

    void deleteMission(Hr_TaskManagement hr_taskManagement);

    List<Hr_ManagementTarget> getManagementObjectivesList(Hr_ManagementTarget hr_managementTarget);

    void insertManagement(Hr_TaskManagement hr_taskManagement);

    //获取关联账套信息
    List<Company> getConnectionAList(User user);

    List<Hr_Employee> getEmployeeOtherPath(@Param("recordId") String recordId);

    List<Hr_Employee> getSalarySystemEmpList(@Param("horId") String horId);

    List<Hr_DepartMent> getSalarySystemDepartmentList(@Param("horId") String horId);

    void batchUpdateEmpGroupId(@Param("list") List<Hr_Employee> list);

    List<Hr_Employee> getSalarySystemEmpTwoList(@Param("horId") String horId, @Param("workStatus") String workStatus);

    void bindeErpCompany(GroupOrgRelation groupOrgRelation);

    String getGroupId(@Param("groupManageId") String groupManageId);

    List<OrganizationBudget> selectBudget(OrganizationBudget organizationBudget);

    void deleteBudget(OrganizationBudget organizationBudget);

    void insertBudget(OrganizationBudget organizationBudget);

    void updateBudget(OrganizationBudget organizationBudget);
}
