package com.kyb.pcberp.modules.wechat.pojo.wechatUser;

import com.kyb.pcberp.common.persistence.DataEntity;
import com.kyb.pcberp.common.utils.DateUtils;

import java.util.List;

public class WechatReport extends DataEntity<WechatRecord>
{
    private static final long serialVersionUID = 1L;
    
    private String employeeId;
    
    private String name;
    
    private String content;
    
    private String contentTwo;
    
    private String contentThree;
    
    private String status;
    
    private String firstDate;
    
    private String day;
    
    private String reportId;
    
    private String superName;
    
    private String flag;
    
    // 共享人
    private String share;
    
    // 上司ID
    private String superEmpId;
    
    private String queryDate;
    
    // 共享人员
    private List<WechatUser> sharesList;
    
    // 附件
    private List<WechatAttachments> attachList;
    
    // 评论回复
    private List<WechatComment> commentList;
    
    private List<String> serverList;
    
    private String phone;
    
    private String groupId;

    private String leadershipsName;

    private String groupName;

    private String position;

    private String workStatus;

    private String completeTime;

    private String generateTaskContent;

    private String shareIds;

    private String shares;

    private String departmentName;

    private String superiorsName;

    private String departId;

    private String comment;
    
    public List<String> getServerList()
    {
        return serverList;
    }
    
    public void setServerList(List<String> serverList)
    {
        this.serverList = serverList;
    }
    
    public String getCreatedDateStr()
    {
        if (createdDate != null)
        {
            return DateUtils.formatDate(createdDate);
        }
        return null;
    }
    
    public String getEmployeeId()
    {
        return employeeId;
    }
    
    public void setEmployeeId(String employeeId)
    {
        this.employeeId = employeeId;
    }
    
    public String getName()
    {
        return name;
    }
    
    public void setName(String name)
    {
        this.name = name;
    }
    
    public String getContent()
    {
        return content;
    }
    
    public void setContent(String content)
    {
        this.content = content;
    }
    
    public String getContentTwo()
    {
        return contentTwo;
    }
    
    public void setContentTwo(String contentTwo)
    {
        this.contentTwo = contentTwo;
    }
    
    public String getContentThree()
    {
        return contentThree;
    }
    
    public void setContentThree(String contentThree)
    {
        this.contentThree = contentThree;
    }
    
    public String getStatus()
    {
        return status;
    }
    
    public void setStatus(String status)
    {
        this.status = status;
    }
    
    public String getFirstDate()
    {
        return firstDate;
    }
    
    public void setFirstDate(String firstDate)
    {
        this.firstDate = firstDate;
    }
    
    public String getDay()
    {
        return day;
    }
    
    public void setDay(String day)
    {
        this.day = day;
    }
    
    public String getReportId()
    {
        return reportId;
    }
    
    public void setReportId(String reportId)
    {
        this.reportId = reportId;
    }
    
    public List<WechatUser> getSharesList()
    {
        return sharesList;
    }
    
    public void setSharesList(List<WechatUser> sharesList)
    {
        this.sharesList = sharesList;
    }
    
    public List<WechatAttachments> getAttachList()
    {
        return attachList;
    }
    
    public void setAttachList(List<WechatAttachments> attachList)
    {
        this.attachList = attachList;
    }
    
    public List<WechatComment> getCommentList()
    {
        return commentList;
    }
    
    public void setCommentList(List<WechatComment> commentList)
    {
        this.commentList = commentList;
    }
    
    public String getShare()
    {
        return share;
    }
    
    public void setShare(String share)
    {
        this.share = share;
    }
    
    public String getSuperEmpId()
    {
        return superEmpId;
    }
    
    public void setSuperEmpId(String superEmpId)
    {
        this.superEmpId = superEmpId;
    }
    
    public String getSuperName()
    {
        return superName;
    }
    
    public void setSuperName(String superName)
    {
        this.superName = superName;
    }
    
    public String getFlag()
    {
        return flag;
    }
    
    public void setFlag(String flag)
    {
        this.flag = flag;
    }
    
    public String getQueryDate()
    {
        return queryDate;
    }
    
    public void setQueryDate(String queryDate)
    {
        this.queryDate = queryDate;
    }
    
    public String getPhone()
    {
        return phone;
    }
    
    public void setPhone(String phone)
    {
        this.phone = phone;
    }
    
    public String getGroupId()
    {
        return groupId;
    }
    
    public void setGroupId(String groupId)
    {
        this.groupId = groupId;
    }

    public String getLeadershipsName() {
        return leadershipsName;
    }

    public void setLeadershipsName(String leadershipsName) {
        this.leadershipsName = leadershipsName;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getWorkStatus() {
        return workStatus;
    }

    public void setWorkStatus(String workStatus) {
        this.workStatus = workStatus;
    }

    public String getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(String completeTime) {
        this.completeTime = completeTime;
    }

    public String getGenerateTaskContent() {
        return generateTaskContent;
    }

    public void setGenerateTaskContent(String generateTaskContent) {
        this.generateTaskContent = generateTaskContent;
    }

    public String getShareIds() {
        return shareIds;
    }

    public void setShareIds(String shareIds) {
        this.shareIds = shareIds;
    }

    public String getShares() {
        return shares;
    }

    public void setShares(String shares) {
        this.shares = shares;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getSuperiorsName() {
        return superiorsName;
    }

    public void setSuperiorsName(String superiorsName) {
        this.superiorsName = superiorsName;
    }

    public String getDepartId() {
        return departId;
    }

    public void setDepartId(String departId) {
        this.departId = departId;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}
