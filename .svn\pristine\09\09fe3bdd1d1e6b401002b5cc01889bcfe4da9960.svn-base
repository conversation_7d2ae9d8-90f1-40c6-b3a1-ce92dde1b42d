package com.kyb.pcberp.modules.quality.dao;

import java.util.List;
import java.util.Map;

import com.kyb.pcberp.common.persistence.CrudDao;
import com.kyb.pcberp.common.persistence.annotation.MyBatisDao;
import com.kyb.pcberp.modules.production.entity.Popo;
import com.kyb.pcberp.modules.quality.entity.PopoInspect;
import com.kyb.pcberp.modules.quality.entity.ReworkBadDiscard;

/**
 * WC 2018-05-25 外发品检DAO
 *
 */
@MyBatisDao
public interface PopoInspectDao extends CrudDao<PopoInspect>
{
    /**
     * insert 外发品检结果
     */
    public void insertResult(ReworkBadDiscard reworkBadDiscard);
    
    /**
     * 分组查询品检扣费
     */
    public List<ReworkBadDiscard> getSubMoneyList(Popo popo);

    /** zjn 2018-11-01 获取付款对账明细根据公司、开始时间、结束时间(初始化报表)*/
    public List<PopoInspect> getReportPopoInspect(Map<String, Object> map);
}
