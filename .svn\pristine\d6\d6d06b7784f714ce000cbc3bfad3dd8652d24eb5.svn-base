<%@ page contentType="text/html;charset=UTF-8" %>

<div ng-intro-options="listOptions" ng-intro-method="helpList" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="readOnlyListOptions" ng-intro-method="helpReadOnlyList" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="infoOptions" ng-intro-method="helpInfo" ng-intro-autostart="shouldAutoStart"></div>
<div ng-intro-options="infoRedOptions" ng-intro-method="helpRedInfo" ng-intro-autostart="shouldAutoStart"></div>

<!-- BEGIN 导航-->
<div class="page-bar page-bar-margin-top">
    <ul class="page-breadcrumb">
        <li>
            <i class="fa fa-home"></i>
            <a href="#/dashboard.html">主页</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a href="javascript:;">财务管理</a>
            <i class="fa fa-angle-right"></i>
        </li>
        <li>
            <a ui-sref="finance.payment">付款管理</a>
        </li>
    </ul>
    <div class="page-toolbar">
    	<button class="btn default btn-fit-height pull-right" ng-click="help()"><i class="fa fa-mortar-board font-yellow-casablanca"></i> 帮&nbsp;助</button>
	</div>
</div>
<!-- END 导航-->
<tabset class="tabset-margin-top">
    <!-- BEGIN 用户列表 -->
    <tab heading="付款记录" active="paymoneyctrl.tabs.viewForm.active" ng-click="paymoneyctrl.loadOutSourceData(1)">
        <div class="rows">
            <div id="listStep6" class="panel panel-default">
                <div class="panel-heading font-blue-hoki">查询</div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="paymoneyctrl.query.supplierNo.value"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商名称：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="paymoneyctrl.query.supplierName.value"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                       	   <div class="col-md-6 col-lg-4">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">付款时间：</label>
	                                <div class="col-sm-7 col-md-8">
	                                     <div class="input-prepend input-group">
											<span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
											<input type="text" class="form-control" disable-auto-validate="true"
												ng-blur="paymoneyctrl.initDate(paymoneyctrl.time)"
												kyb-daterange 
												kyb-daterange-options="paymoneyctrl.rangeOptions"
												ng-model="paymoneyctrl.time" 
												placeholder="请选择时间段">
										</div>
	                                </div>
	                            </div>
                       		</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default btn-default-width pull-right" ng-click="paymoneyctrl.doQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">付款记录列表</div>
                    <div id="listStep7" class="actions">
                        <div class="portlet-input input-inline input-small" ng-if="paymoneyctrl.right.edit">
                            <button type="button" class="btn green btn-default-width" ng-click="paymoneyctrl.addPayMoney()"><i class="fa fa-plus"></i> 添加付款</button>
                        </div>
                        <div class="portlet-input input-inline input-small">
		                        <form action="a/finance/payment/export" method="get" enctype="multipart/form-data" target="hidden_frame">
		                            <input type="text" ng-show="false" name="supplier.no" value="{{paymoneyctrl.query.supplierNo.value}}" /> 
		                            <input type="text" ng-show="false" name="supplier.shortName" value="{{paymoneyctrl.query.supplierName.value}}" /> 
		                            <input type="text" type="text" ng-show="false" name="orderBy" value="{{paymoneyctrl.query.sort.value}}" />
		                           	<input type="text" ng-show="false" name="status" value="{{paymoneyctrl.query.status.value}}" /> 
		                            <input type="text" ng-show="false" name="sentTimeStartQr" value="{{paymoneyctrl.query.sentTimeStartQr.value}}"/>
		                            <input type="text" ng-show="false" name="sentTimeEndQr" value="{{paymoneyctrl.query.sentTimeEndQr.value}}"/>
		                            <input type="text" ng-show="false" name="statusCollectFlag" value="{{paymoneyctrl.query.statusCollectFlag}}"/>
		                            <input type="text" ng-show="false"  name="queryAll" value="{{paymoneyctrl.queryAll}}"/>
		                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出付款记录</button>
		                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
		                        </form>
                    	</div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div id="listStep1" class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                            <thead>
                            <tr class="heading">
                                <th id="listStep2" ng-class="{'sorting': paymoneyctrl.sort.supplierNo.both, 'sorting_desc': paymoneyctrl.sort.supplierNo.desc, 'sorting_asc': paymoneyctrl.sort.supplierNo.asc}" 
                                			ng-click="paymoneyctrl.sortClick('supplierNo')">供应商编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th ng-class="{'sorting': paymoneyctrl.sort.supplierShortName.both, 'sorting_desc': paymoneyctrl.sort.supplierShortName.desc, 'sorting_asc': paymoneyctrl.sort.supplierShortName.asc}" 
                                			ng-click="paymoneyctrl.sortClick('supplierShortName')">供应商名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>申请类型</th>
                                <th ng-class="{'sorting': paymoneyctrl.sort.fundType.both, 'sorting_desc': paymoneyctrl.sort.fundType.desc, 'sorting_asc': paymoneyctrl.sort.fundType.asc}"
                                			ng-click="paymoneyctrl.sortClick('fundType')">款项类型&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>金额</th>
                                <th>对账月份</th>
                                <th>采购单号</th>
                                <th ng-class="{'sorting': paymoneyctrl.sort.payDate.both, 'sorting_desc': paymoneyctrl.sort.payDate.desc, 'sorting_asc': paymoneyctrl.sort.payDate.asc}" 
                                			ng-click="paymoneyctrl.sortClick('payDate')">付款日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>创建日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                               	<th>经办人</th>
                                <th>状态</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in paymoneyctrl.page.data.list | orderBy:row.supplier.shortName" ng-dblclick="paymoneyctrl.seePayMoney($index)">
                                <td><a ng-click="paymoneyctrl.seePayMoney($index)">{{row.supplier.no}}</a></td>
                                <td ng-bind="row.supplier.shortName"></td>
                                <td ng-if="row.applyType == 0">采购</td>
                                <td ng-if="row.applyType == 1">外发加工</td>
                                <td ng-if="!row.applyType">无</td>
                                <td ng-if="(!row.statusCollectFlag || row.statusCollectFlag == '1') && row.fundType == '99999911'"><span class="label label-sm label-success">预付款</span></td>
                                <td ng-if="(!row.statusCollectFlag || row.statusCollectFlag == '1') && row.fundType == '99999912'"><span class="label label-sm label-danger">应付款</span></td>
                                <td ng-if="(!row.statusCollectFlag || row.statusCollectFlag == '1') && row.fundType != '99999911' && row.fundType != '99999912'"><span class="label label-sm label-default">状态异常 </span></td>
                                <td ng-if="row.statusCollectFlag && row.statusCollectFlag == '2'"><span class="label label-sm label-default">多月付款</span></td>
                                <td class="text-right" ng-bind="row.amount | currency:'':3"></td>
                                <td ng-bind="row.payApplication.showPeriod"></td>
                                <td ng-bind="row.payApplication.showPurchNo"></td>
                                <td ng-bind="row.payDate"></td>
                                <td ng-bind="row.createdDate"></td>
                                <td ng-bind="row.responsiblePerson.userName"></td>
                                <td ng-if="row.status === 99999901">
                                    <span class="label label-sm label-default">未关闭</span>
                                </td>
                                <td ng-if="row.status === 99999904">
                                    <span class="label label-sm label-info">已结账</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row page-margin-top">
                        <div id="listStep5" class="col-md-12 col-lg-6">
                            <span class="inline">每页</span>
                            <select class="form-control inline" style="margin-top:8px; width:100px;"
                                    disable-valid-styling="true"
                                    disable-invalid-styling="true"
                                    ng-model="paymoneyctrl.page.pageSize"
                                    ng-change="paymoneyctrl.pageSizeChange()"
                                    ng-options="option for option in paymoneyctrl.page.pageSizeOptions">
                            </select>
                            <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{paymoneyctrl.page.data.startCount}} / {{paymoneyctrl.page.data.endCount}} 条，共 {{paymoneyctrl.page.data.count}} 条</span>
                        </div>
                        <div id="listStep4" class="col-md-12 col-lg-6">
                            <paging
                                    class="pull-right"
                                    page="paymoneyctrl.page.data.pageNo"
                                    page-size="paymoneyctrl.page.data.pageSize"
                                    total="paymoneyctrl.page.data.count"
                                    adjacent="1"
                                    dots="..."
                                    scroll-top="false"
                                    hide-if-empty="false"
                                    ul-class="pagination"
                                    active-class="active"
                                    disabled-class="disabled"
                                    show-prev-next="true"
                                    paging-action="paymoneyctrl.doPage(page, pageSize, total)">
                            </paging>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </tab>
    <!-- END 用户列表 -->

    <tab heading="总部经济付款记录" ng-if="paymoneyctrl.ValiteCompanyUtil.valiteSaleCompany" ng-click="paymoneyctrl.loadOutSourceData(2)">
        <div class="rows">
            <div class="panel panel-default">
                <div class="panel-heading font-blue-hoki">查询</div>
                <div class="panel-body">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="paymoneyctrl.supNoQuery"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商名称：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" class="form-control"
                                               ng-model="paymoneyctrl.supNameQuery"
                                               disable-auto-validate="true"/>
                                    </div>
                                </div>
                            </div>
                       	   <div class="col-md-6 col-lg-4">
	                            <div class="form-group">
	                                <label class="col-sm-3 col-md-4 control-label">付款时间：</label>
	                                <div class="col-sm-7 col-md-8">
	                                     <div class="input-prepend input-group">
											<span class="add-on input-group-addon"><i class="glyphicon glyphicon-calendar fa fa-calendar"></i></span>
											<input type="text" class="form-control" disable-auto-validate="true"
												ng-blur="paymoneyctrl.initDate(paymoneyctrl.timeTwo)"
												kyb-daterange 
												kyb-daterange-options="paymoneyctrl.rangeOptions"
												ng-model="paymoneyctrl.timeTwo" 
												placeholder="请选择时间段">
										</div>
	                                </div>
	                            </div>
                       		</div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default btn-default-width pull-right" ng-click="paymoneyctrl.doOutQuery()"><i class="fa fa-search"></i> 查&nbsp;询</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">付款记录列表</div>
                    <div id="listStep7" class="actions">
                        <div class="portlet-input input-inline input-small" ng-if="paymoneyctrl.right.edit">
                            <button type="button" class="btn green btn-default-width" ng-click="paymoneyctrl.addPayMoney()"><i class="fa fa-plus"></i> 添加付款</button>
                        </div>
                        <div class="portlet-input input-inline input-small">
		                        <form action="a/finance/payment/export" method="get" enctype="multipart/form-data" target="hidden_frame">
		                            <input type="text" ng-show="false" name="supplier.no" value="{{paymoneyctrl.supNoQuery}}" /> 
		                            <input type="text" ng-show="false" name="supplier.shortName" value="{{paymoneyctrl.supNameQuery}}" /> 
		                            <input type="text" type="text" ng-show="false" name="orderBy" value="{{paymoneyctrl.query.sort.value}}" />
		                            <input type="text" ng-show="false" name="sentTimeStartQr" value="{{paymoneyctrl.query.sentTimeStartQr.value}}"/>
		                            <input type="text" ng-show="false" name="sentTimeEndQr" value="{{paymoneyctrl.query.sentTimeEndQr.value}}"/>
		                            <input type="text" ng-show="false" name="lnCompanyId" value="{{paymoneyctrl.lnCompanyId}}"/>
               		                <input type="text" ng-show="false" name="statusCollectFlag" value="{{paymoneyctrl.query.statusCollectFlag}}"/>
		                            <input type="text" ng-show="false"  name="queryAll" value="{{paymoneyctrl.queryAll}}"/>
		                            <button type="submit" class="btn blue-hoki btn-default-width"><i class="fa fa-download"></i> 导出付款记录</button>
		                            <iframe name="hidden_frame" id="hidden_frame" style="display: none"></iframe>
		                        </form>
                    	</div>
                    </div>
                </div>
                <div class="portlet-body">
                    <div id="listStep1" class="table-scrollable" style="margin-top:0px !important">
                        <table class="table table-striped table-bordered table-condensed table-advance table-hover" >
                            <thead>
                            <tr class="heading">
                                <th id="listStep2" ng-class="{'sorting': paymoneyctrl.sort.supplierNo.both, 'sorting_desc': paymoneyctrl.sort.supplierNo.desc, 'sorting_asc': paymoneyctrl.sort.supplierNo.asc}" 
                                			ng-click="paymoneyctrl.sortClick('supplierNo')">供应商编号&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th ng-class="{'sorting': paymoneyctrl.sort.supplierShortName.both, 'sorting_desc': paymoneyctrl.sort.supplierShortName.desc, 'sorting_asc': paymoneyctrl.sort.supplierShortName.asc}" 
                                			ng-click="paymoneyctrl.sortClick('supplierShortName')">供应商名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>申请类型</th>
                                <th ng-class="{'sorting': paymoneyctrl.sort.fundType.both, 'sorting_desc': paymoneyctrl.sort.fundType.desc, 'sorting_asc': paymoneyctrl.sort.fundType.asc}"
                                			ng-click="paymoneyctrl.sortClick('fundType')">款项类型&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>金额</th>
                                <th>对账月份</th>
                                <th>采购单号</th>
                                <th ng-class="{'sorting': paymoneyctrl.sort.payDate.both, 'sorting_desc': paymoneyctrl.sort.payDate.desc, 'sorting_asc': paymoneyctrl.sort.payDate.asc}" 
                                			ng-click="paymoneyctrl.sortClick('payDate')">付款日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                                <th>创建日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</th>
                               	<th>经办人</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in paymoneyctrl.page.data.list | orderBy:row.supplier.shortName" ng-dblclick="paymoneyctrl.seePayMoney($index)">
                                <td><a ng-click="paymoneyctrl.seePayMoney($index)">{{row.supplier.no}}</a></td>
                                <td ng-bind="row.supplier.shortName"></td>
                                <td ng-if="row.applyType == 0">采购</td>
                                <td ng-if="row.applyType == 1">外发加工</td>
                                <td ng-if="!row.applyType">无</td>
                                <td ng-if="(!row.statusCollectFlag || row.statusCollectFlag == '1') && row.fundType == '99999911'"><span class="label label-sm label-success">预付款</span></td>
                                <td ng-if="(!row.statusCollectFlag || row.statusCollectFlag == '1') && row.fundType == '99999912'"><span class="label label-sm label-danger">应付款</span></td>
                                <td ng-if="(!row.statusCollectFlag || row.statusCollectFlag == '1') && row.fundType != '99999911' && row.fundType != '99999912'"><span class="label label-sm label-default">状态异常 </span></td>
                                <td ng-if="row.statusCollectFlag && row.statusCollectFlag == '2'"><span class="label label-sm label-default">多月付款</span></td>
                                <td class="text-right" ng-bind="row.amount | currency:'':3"></td>
                                <td ng-bind="row.payApplication.showPeriod"></td>
                                <td ng-bind="row.payApplication.showPurchNo"></td>
                                <td ng-bind="row.payDate"></td>
                                <td ng-bind="row.createdDate"></td>
                                <td ng-bind="row.responsiblePerson.userName"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="row page-margin-top">
                        <div id="listStep5" class="col-md-12 col-lg-6">
                            <span class="inline">每页</span>
                            <select class="form-control inline" style="margin-top:8px; width:100px;"
                                    disable-valid-styling="true"
                                    disable-invalid-styling="true"
                                    ng-model="paymoneyctrl.page.pageSize"
                                    ng-change="paymoneyctrl.pageSizeChange()"
                                    ng-options="option for option in paymoneyctrl.page.pageSizeOptions">
                            </select>
                            <span class="inline">条记录&nbsp;&nbsp;&nbsp;&nbsp;当前显示 {{paymoneyctrl.page.data.startCount}} / {{paymoneyctrl.page.data.endCount}} 条，共 {{paymoneyctrl.page.data.count}} 条</span>
                        </div>
                        <div id="listStep4" class="col-md-12 col-lg-6">
                            <paging
                                    class="pull-right"
                                    page="paymoneyctrl.page.data.pageNo"
                                    page-size="paymoneyctrl.page.data.pageSize"
                                    total="paymoneyctrl.page.data.count"
                                    adjacent="1"
                                    dots="..."
                                    scroll-top="false"
                                    hide-if-empty="false"
                                    ul-class="pagination"
                                    active-class="active"
                                    disabled-class="disabled"
                                    show-prev-next="true"
                                    paging-action="paymoneyctrl.doPage(page, pageSize, total)">
                            </paging>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </tab>

    <!-- BEGIN 用户编辑 -->
    <tab active="paymoneyctrl.tabs.editForm.active" ng-show="paymoneyctrl.tabs.editForm.show">
        <tab-heading>付款记录详情 <i class="fa fa-times set-cursor-pointer" ng-click="paymoneyctrl.hideEditForm()"></i></tab-heading>
        <div class="rows" ng-if="paymoneyctrl.right.edit">
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption font-blue-hoki">付款记录{{paymoneyctrl.editTitle}}</div>
                </div>
                <div class="portlet-body">
                    <form class="form-horizontal">
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>单据编号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input required type="text" class="form-control"
                                               ng-model="paymoneyctrl.finished.billNo" disabled/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                     <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>供应商名称：</label>
                                     <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="paymoneyctrl.finished.payApplication.supplier" theme="bootstrap"
                                                   register-custom-form-control ng-disabled="paymoneyctrl.finished.recordId"
                                                   on-select="paymoneyctrl.selectSuppliers($select.selected)"
                                                   firstfocus="{{paymoneyctrl.focus.paymoney}}" required ng-if="!paymoneyctrl.finished.recordId">
                                            <ui-select-match placeholder="请选择...">{{$select.selected.name}}
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="item in paymoneyctrl.refer.supplierList | filter: $select.search">
                                                <div ng-bind-html="item.no | highlight: $select.search"></div>
                                                <small>
                                             	简称:<span style="color:red;">&nbsp;&nbsp;{{item.shortName}}{{item.company.recordId}}</span>
                                            </small>
                                            </ui-select-choices>
                                        </ui-select>
                                         <span class="form-control" disabled
                                               ng-bind="paymoneyctrl.finished.supplier.name" ng-if="paymoneyctrl.finished.recordId"></span>
                                    </div>
                                </div>
                            </div>
                             <div class="col-md-6 col-lg-4">
                                <div id="infoStep2" class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>付款申请单：</label>

                                    <div class="col-sm-7 col-md-8">
                                        <ui-select ng-model="paymoneyctrl.finished.payApplication" theme="bootstrap"
                                                   register-custom-form-control ng-disabled="paymoneyctrl.finished.recordId"
                                                   on-select="paymoneyctrl.selectPayApplications($select.selected.fundType,$select.selected.payMethod)"
                                                   firstfocus="{{paymoneyctrl.focus.paymoney}}" required>
                                            <ui-select-match placeholder="请选择...">{{$select.selected.no}}
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="item in paymoneyctrl.refer.payApplicationList | filter: $select.search">
                                                <div ng-bind-html="item.no | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="paymoneyctrl.finished.payApplication.fundType == '99999911'">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">采购订单：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" required class="form-control" readonly ng-model="paymoneyctrl.finished.payApplication.showPurchNo">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4" ng-if="paymoneyctrl.finished.payApplication.fundType == '99999912'">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">对账日期：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" required class="form-control" readonly ng-model="paymoneyctrl.finished.payApplication.showPeriod">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">付款金额：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" disabled
                                                  ng-bind="paymoneyctrl.finished.payApplication.amount | currency:'':3"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">供应商编号：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" disabled
                                                  ng-bind="paymoneyctrl.finished.payApplication.supplier.no" ng-if="!paymoneyctrl.finished.recordId"></span>
                                            <span class="form-control" disabled
                                                  ng-bind="paymoneyctrl.finished.supplier.no" ng-if="paymoneyctrl.finished.recordId"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">款项类别：</label>

                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" disabled
                                                  ng-bind="paymoneyctrl.finished.payApplication.fundTypeVal" ng-if="!paymoneyctrl.finished.recordId"></span>
                                            <span class="form-control" disabled ng-if="paymoneyctrl.finished.recordId">
                                                <span ng-if="paymoneyctrl.finished.fundType == 99999911">预付</span>
                                                <span ng-if="paymoneyctrl.finished.fundType == 99999912">应付</span>
                                            </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>付款方式：</label>
                                    <div class="col-sm-7 col-md-8">
                                            <span class="form-control" disabled
                                                  ng-bind="paymoneyctrl.finished.payApplication.payMethod"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>付款日期：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" required class="form-control" readonly ng-model="paymoneyctrl.finished.payDate">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">付款银行：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" required class="form-control" readonly ng-model="paymoneyctrl.finished.payBank" ng-if="!paymoneyctrl.finished.recordId">
                                        <input type="text" required class="form-control" readonly ng-model="paymoneyctrl.finished.payApplication.payBank"
                                               ng-if="paymoneyctrl.finished.recordId">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label">付款账号：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <input type="text" required class="form-control" readonly ng-model="paymoneyctrl.finished.payAccount" ng-if="!paymoneyctrl.finished.recordId">
                                        <input type="text" required class="form-control" readonly ng-model="paymoneyctrl.finished.payApplication.payAccount"
                                               ng-if="paymoneyctrl.finished.recordId">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-lg-4">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-4 control-label"><span class="required">*</span>经办人：</label>
                                    <div class="col-sm-7 col-md-8">
                                        <ui-select required ng-model="paymoneyctrl.finished.responsiblePerson"
                                                   register-custom-form-control theme="bootstrap" ng-disabled="paymoneyctrl.finished.recordId">
                                            <ui-select-match
                                                    placeholder="请选择...">{{$select.selected.userName}}
                                            </ui-select-match>
                                            <ui-select-choices
                                                    repeat="item in paymoneyctrl.refer.employeeList | filter: $select.search">
                                                <div ng-bind-html="item.userName | highlight: $select.search"></div>
                                            </ui-select-choices>
                                        </ui-select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-lg-8">
                                <div class="form-group">
                                    <label class="col-sm-3 col-md-2 control-label">备注：</label>
                                    <div class="col-sm-7 col-md-10">
                                            <textarea class="form-control"
                                                      ng-model="paymoneyctrl.finished.remark"
                                                      ng-maxlength="255" ng-disabled="paymoneyctrl.finished.recordId"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" ng-if="!paymoneyctrl.finished.recordId">
                            <div class="form-group">
                                <div class="col-sm-offset-4 col-sm-10">
                                    <button  class="btn btn-primary btn-default-width" ng-click="paymoneyctrl.savePayMoney()"><i class="fa fa-save"></i> 保&nbsp;存</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </tab>

</tabset>

<div id="static" class="modal fade" tabindex="-1" data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">提示</h4>
            </div>
            <div class="modal-body">
                <p>
                    <span ng-bind="paymoneyctrl.message"></span>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-default">OK</button>
            </div>
        </div>
    </div>
</div>