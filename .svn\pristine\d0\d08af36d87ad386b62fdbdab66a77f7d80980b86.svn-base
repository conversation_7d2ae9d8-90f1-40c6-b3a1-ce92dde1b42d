package com.kyb.pcberp.common.utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Iterator;
import java.util.List;

import com.kyb.pcberp.modules.stock.dao.InitMaterialDao;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.google.common.collect.Lists;
import com.kyb.pcberp.common.config.TypeKey;
import com.kyb.pcberp.modules.contract.entity.ContractDetail;
import com.kyb.pcberp.modules.stock.dao.MaterialDao;
import com.kyb.pcberp.modules.stock.dao.ProductStoreDao;
import com.kyb.pcberp.modules.stock.dao.StoreHouseDao;
import com.kyb.pcberp.modules.stock.entity.Material;
import com.kyb.pcberp.modules.stock.entity.MaterialPlace;
import com.kyb.pcberp.modules.stock.entity.MaterialPlaceCom;
import com.kyb.pcberp.modules.stock.entity.MaterialStockInit;
import com.kyb.pcberp.modules.stock.entity.ProductStore;
import com.kyb.pcberp.modules.sys.entity.Company;
import com.kyb.pcberp.modules.sys.utils.UserUtils;

public class ProductStockUtil
{
    private ProductStoreDao productStoreDao = SpringContextHolder.getBean(ProductStoreDao.class);
    
    private MaterialDao materialDao = SpringContextHolder.getBean(MaterialDao.class);
    
    private StoreHouseDao storeHouseDao = SpringContextHolder.getBean(StoreHouseDao.class);

    private InitMaterialDao initMaterialDao = SpringContextHolder.getBean(InitMaterialDao.class);
    
    // 获取某个时间点物料所有库位的库存
    public List<MaterialPlaceCom> getMaterialApplyStock(String materialIds, Date operDate, String stockPlaceComId)
    {
        // 根据materialIds找出所有库位以及各库位对应的期初库存、期初时间
        MaterialPlaceCom materialPlaceCom = new MaterialPlaceCom();
        materialPlaceCom.setMaterialIds(materialIds);
        materialPlaceCom.setInitDate(operDate);
        if (StringUtils.isNotBlank(stockPlaceComId))
        {
            materialPlaceCom.setRecordId(stockPlaceComId);
        }
        List<MaterialPlaceCom> mpList = materialDao.getMaterialPlaceApplyList(materialPlaceCom);
        // 成品可以使用一个空的库位
        List<MaterialPlaceCom> mp = materialDao.getMaterialPlaceApply(materialPlaceCom);
        mpList.addAll(mp);
        
        // 成品有临时库和尾数库
        materialPlaceCom.setCompanyId(UserUtils.getUser().getCompany().getRecordId());
        List<MaterialPlaceCom> mpTwo = materialDao.getMaterialPlaceApplyTwo(materialPlaceCom);
        mpList.addAll(mpTwo);
        
        // 获取从期初到查询开始时间之间的出入库记录
        Material material = new Material();
        material.setMaterialIds(materialIds);
        material.setInitDate(operDate);
        if (mpList.size() > 0)
        {
            material.setStartDate(mpList.get(0).getInitDate());
        }
        List<ProductStore> rsList = materialDao.getRawPrudcutStockApplyTwoList(material);
        for (int i = 0; i < rsList.size(); i++)
        {
            if (StringUtils.isBlank(rsList.get(i).getStockPlaceComId()))
            {
                rsList.get(i).setStockPlaceComId("sky" + rsList.get(i).getMaterialId());
            }
        }
        // 进行滚存数据得到最终库存
        List<MaterialPlaceCom> list = cumulativeStock(mpList, rsList);
        // 进行修改期初库存和期初时间
        for (int i = 0; i < mpList.size(); i++)
        {
            for (int j = 0; j < list.size(); j++)
            {
                if (mpList.get(i).getRecordId().equals(list.get(j).getRecordId()))
                {
                    mpList.get(i).setPeriodStocks(list.get(j).getChangeStocks());
                    mpList.get(i).setInitDate(operDate);
                }
            }
        }
        return mpList;
    }
    
    // 依据 开始时间库存信息comList，开始时间到结束时间的出入库rsList，更新materialList物料的 期初、出库量、入库量、结余
    public List<ProductStore> getMaterialApplyList(List<ProductStore> materialList, List<MaterialPlaceCom> comList,
        List<ProductStore> rsList)
    {
        List<MaterialPlaceCom> list = cumulativeStock(comList, rsList);
        // 将库位结存合并成物料库存
        for (int i = 0; i < materialList.size(); i++)
        {
            BigDecimal stocks = BigDecimal.ZERO;
            BigDecimal periodStocks = BigDecimal.ZERO;
            BigDecimal inStocks = BigDecimal.ZERO;
            BigDecimal outStocks = BigDecimal.ZERO;
            for (int j = 0; j < list.size(); j++)
            {
                // 物料id相等的话进行拼接数据
                if (list.get(j).getMaterialId().equals(materialList.get(i).getRecordId()))
                {
                    if (list.get(j).getChangeStocks() != null)
                    {
                        stocks = stocks.add(list.get(j).getChangeStocks());
                    }
                    if (list.get(j).getPeriodStocks() != null)
                    {
                        periodStocks = periodStocks.add(list.get(j).getPeriodStocks());
                    }
                    if (list.get(j).getInStocks() != null)
                    {
                        inStocks = inStocks.add(list.get(j).getInStocks());
                    }
                    if (list.get(j).getOutStocks() != null)
                    {
                        outStocks = outStocks.add(list.get(j).getOutStocks());
                    }
                }
            }
            Integer val = stocks == null ? 0 : stocks.intValue();
            materialList.get(i).setQuantity(val);
            materialList.get(i).setStartStocks(periodStocks);
            materialList.get(i).setInStocks(inStocks);
            materialList.get(i).setOutStocks(outStocks);
        }
        return materialList;
    }
    
    // 根据库位和出入库记录滚存数据得到库存相关信息
    public List<MaterialPlaceCom> cumulativeStock(List<MaterialPlaceCom> mpList, List<ProductStore> rsList)
    {
        List<MaterialPlaceCom> list = new ArrayList<MaterialPlaceCom>();
        Iterator<MaterialPlaceCom> it = mpList.iterator();
        while (it.hasNext())
        {
            MaterialPlaceCom materialPlaceCom = it.next();
            // 获取库存进行更新
            if (StringUtils.isNotBlank(materialPlaceCom.getRecordId()))
            {
                BigDecimal changeStocks = BigDecimal.ZERO;
                
                BigDecimal inStocks = BigDecimal.ZERO;
                BigDecimal outStocks = BigDecimal.ZERO;
                // 期初库存
                if (materialPlaceCom.getPeriodStocks() != null)
                {
                    changeStocks = materialPlaceCom.getPeriodStocks();
                }
                // 计算库存
                if (rsList != null && rsList.size() > 0)
                {
                    for (ProductStore productStore : rsList)
                    {
                        // 物料id和库位id都相同的进行计算
                        if (productStore.getMaterialId().equals(materialPlaceCom.getMaterialId())
                            && productStore.getStockPlaceComId().equals(materialPlaceCom.getRecordId()))
                        {
                            if (productStore.getInoutType() != null && productStore.getQuantity() != null)
                            {
                                BigDecimal qty = null == productStore.getQuantity() ? BigDecimal.ZERO
                                    : new BigDecimal(productStore.getQuantity());
                                
                                // 生产入库
                                if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_IN)
                                {
                                    changeStocks = changeStocks.add(qty);
                                    inStocks = inStocks.add(qty);
                                }
                                // 送货出库
                                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_OUT)
                                {
                                    changeStocks = changeStocks.subtract(qty);
                                    outStocks = outStocks.add(qty);
                                }
                                // 客诉退货入库
                                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_RETURN)
                                {
                                    changeStocks = changeStocks.add(qty);
                                    inStocks = inStocks.add(qty);
                                }
                                // 采购入库
                                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_PURCHASING_IN)
                                {
                                    changeStocks = changeStocks.add(qty);
                                    inStocks = inStocks.add(qty);
                                }
                                // 采购补货入库
                                else if (productStore
                                    .getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_ENTER)
                                {
                                    changeStocks = changeStocks.add(qty);
                                    inStocks = inStocks.add(qty);
                                }
                                // 采购退货出库
                                else if (productStore
                                    .getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_OUT)
                                {
                                    changeStocks = changeStocks.subtract(qty);
                                    outStocks = outStocks.add(qty);
                                }
                                
                                // 订单移库出库
                                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_MOVE_OUT)
                                {
                                    changeStocks = changeStocks.subtract(qty);
                                    outStocks = outStocks.add(qty);
                                }
                                // 订单移库入库
                                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_MOVE_IN)
                                {
                                    changeStocks = changeStocks.add(qty);
                                    inStocks = inStocks.add(qty);
                                }
                                // 尾数移库出库
                                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_STOCKMOVE_OUT)
                                {
                                    changeStocks = changeStocks.subtract(qty);
                                    outStocks = outStocks.add(qty);
                                }
                                // 尾数移库入库
                                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_STOCKMOVE_IN)
                                {
                                    changeStocks = changeStocks.add(qty);
                                    inStocks = inStocks.add(qty);
                                }
                            }
                            
                        }
                    }
                }
                materialPlaceCom.setChangeStocks(changeStocks);
                
                materialPlaceCom.setInStocks(inStocks);
                materialPlaceCom.setOutStocks(outStocks);
                list.add(materialPlaceCom);
            }
        }
        return list;
    }
    
    public List<ProductStore> dealPlace(ProductStore productStore)
    {
        productStore.setMaterialKind(100702);
        productStore.setCraftStatus(200502);
        // 1、出入库记录
        List<ProductStore> proList = productStoreDao.findList(productStore);
        List<ProductStore> list = new ArrayList<ProductStore>();
        for (int i = 0; i < proList.size(); i++)
        {
            if (proList.get(i).getInoutType() != null)
            {
                if (proList.get(i).getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_IN)
                {
                    proList.get(i).setInoutTypeStr("生产入库");
                    if (StringUtils.isEmpty(proList.get(i).getContractDetailId())
                        && !StringUtils.isEmpty(proList.get(i).getRemark())
                        && proList.get(i).getRemark().contains("盘盈"))
                    {
                        proList.get(i).setInoutTypeStr("盘盈入库");
                    }
                    list.add(proList.get(i));
                }
                else if (proList.get(i).getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_OUT)
                {
                    proList.get(i).setInoutTypeStr("送货出库");
                    if (StringUtils.isEmpty(proList.get(i).getContractDetailId())
                        && !StringUtils.isEmpty(proList.get(i).getRemark())
                        && proList.get(i).getRemark().contains("盘亏"))
                    {
                        proList.get(i).setInoutTypeStr("盘亏出库");
                    }
                    list.add(proList.get(i));
                }
                else if (proList.get(i).getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_RETURN)
                {
                    proList.get(i).setInoutTypeStr("客诉退货入库");
                    list.add(proList.get(i));
                }
                else if (proList.get(i).getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_PURCHASING_IN)
                {
                    proList.get(i).setInoutTypeStr("采购入库");
                    list.add(proList.get(i));
                }
                else if (proList.get(i).getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_ENTER)
                {
                    proList.get(i).setInoutTypeStr("采购补货入库");
                    list.add(proList.get(i));
                }
                else if (proList.get(i).getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_OUT)
                {
                    proList.get(i).setInoutTypeStr("采购退货出库");
                    list.add(proList.get(i));
                }
                
                else if (proList.get(i).getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_MOVE_OUT)
                {
                    proList.get(i).setInoutTypeStr("订单移库出库");
                    list.add(proList.get(i));
                }
                else if (proList.get(i).getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_MOVE_IN)
                {
                    proList.get(i).setInoutTypeStr("订单移库入库");
                    list.add(proList.get(i));
                }
                else if (proList.get(i).getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_STOCKMOVE_OUT)
                {
                    proList.get(i).setInoutTypeStr("尾数移库出库");
                    if (!StringUtils.isEmpty(proList.get(i).getRemark()) && proList.get(i).getRemark().contains("盘"))
                    {
                        proList.get(i).setInoutTypeStr("盘盈出库");
                    }
                    list.add(proList.get(i));
                }
                else if (proList.get(i).getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_STOCKMOVE_IN)
                {
                    proList.get(i).setInoutTypeStr("尾数移库入库");
                    if (!StringUtils.isEmpty(proList.get(i).getRemark()) && proList.get(i).getRemark().contains("盘"))
                    {
                        proList.get(i).setInoutTypeStr("盘盈入库");
                    }
                    list.add(proList.get(i));
                }
            }
        }
        
        // 2、出入库记录增加期初和结余
        ProductStore stock = new ProductStore();
        stock.setMaterial(productStore.getMaterial());
        stock.setInoutTimeStartQr(productStore.getInoutTimeStartQr());
        stock.setInoutTimeEndQr(productStore.getInoutTimeEndQr());
        stock.setCompany(UserUtils.getUser().getCompany());
        // 2.1、查询期初
        ProductStore psObj = productStoreDao.getCurrentStocks(stock);
        BigDecimal startStocks = BigDecimal.ZERO;
        if (null != psObj)
        {
            startStocks = psObj.getStartStocks();
        }
        List<ProductStore> rawInOutList = storeHouseDao.getProductStocksPlaceComTwo(stock);
        ProductStore rawStock = setProInoutList(rawInOutList, startStocks, productStore.getMaterial().getRecordId());
        List<ProductStore> allInOutList = rawStock.getProList();
        // 4、期初、汇总记录
        
        // 模仿插入汇总数据
        ProductStore sto = new ProductStore();
        if (rawInOutList != null && rawInOutList.size() > 0)
        {
            sto.setMaterial(productStore.getMaterial());
            sto.setInoutTypeStr("汇总");
            sto.setCurrStocks(rawStock.getChangeStocks());
            sto.setAllStocks(rawStock.getChangeStocks());
            sto.setInStocks(rawStock.getInStocks());
            sto.setOutStocks(rawStock.getOutStocks());
        }
        ProductStore sto1 = new ProductStore();
        sto1.setRecordId("期初");
        sto1.setMaterial(productStore.getMaterial());
        sto1.setInoutTypeStr("期初");
        sto1.setCurrStocks(startStocks);
        // 对分页 出入库记录赋值结存
        if (list.size() > 0)
        {
            if (allInOutList != null && allInOutList.size() > 0)
            {
                for (ProductStore raw : list)
                {
                    for (int i = 0; i < allInOutList.size(); i++)
                    {
                        ProductStore stockTemp = allInOutList.get(i);
                        if (raw.getRecordId().equals(stockTemp.getRecordId()))
                        {
                            raw.setSortNum(i);
                            raw.setCurrStocks(stockTemp.getCurrStocks());
                            raw.setInStocks(stockTemp.getInStocks());
                            raw.setOutStocks(stockTemp.getOutStocks());
                            break;
                        }
                    }
                }
            }
        }
        for (ProductStore raw : list)
        {
            if (raw != null && raw.getSortNum() == null)
            {
                raw.setSortNum(0);
            }
        }
        list.sort((x, y) -> Double.compare(x.getSortNum(), y.getSortNum()));
        list.add(0, sto);
        list.add(1, sto1);
        return list;
    }
    
    // 用作物料台账查单个物料数据
    public List<ProductStore> getProMaterialStockPlace(ProductStore productStore)
    {
        List<ProductStore> rowList = new ArrayList<ProductStore>();
        Company company = UserUtils.getUser().getCompany();
        if (productStore.getMaterial() != null && StringUtils.isNotBlank(productStore.getMaterial().getRecordId()))
        {
            // 如果是按单个库位查询
            if (StringUtils.isNotBlank(productStore.getStockPlaceComId()))
            {
                BigDecimal changeStocks = BigDecimal.ZERO;
                List<MaterialPlaceCom> comList = getMaterialApplyStock(productStore.getMaterial().getRecordId(),
                    productStore.getInoutTimeStartQr(),
                    productStore.getStockPlaceComId());
                if (comList.size() > 0)
                {
                    changeStocks = comList.get(0).getPeriodStocks();
                }
                // 查询出入库记录，给每个出入库记录赋值
                ProductStore stock = new ProductStore();
                stock.setMaterial(productStore.getMaterial());
                stock.setStockPlaceComId(productStore.getStockPlaceComId());
                stock.setInoutTimeStartQr(productStore.getInoutTimeStartQr());
                stock.setInoutTimeEndQr(productStore.getInoutTimeEndQr());
                stock.setCompany(company);
                List<ProductStore> rawInOutList = storeHouseDao.getProductStocksPlaceComTwo(stock);
                
                ProductStore rawStock =
                    setProInoutList(rawInOutList, changeStocks, productStore.getMaterial().getRecordId());
                
                rowList = rawStock.getProList();
                ProductStore stok = new ProductStore();
                stok.setCurrStocks(changeStocks);
                
                BigDecimal stocks = BigDecimal.ZERO;
                if (changeStocks != null)
                {
                    stocks = stocks.add(changeStocks);
                }
                if (rawStock.getInStocks() != null)
                {
                    stocks = stocks.add(rawStock.getInStocks());
                }
                if (rawStock.getOutStocks() != null)
                {
                    stocks = stocks.subtract(rawStock.getOutStocks());
                }
                stok.setAllStocks(stocks);
                stok.setInStocks(rawStock.getInStocks());
                stok.setOutStocks(rawStock.getOutStocks());
                rowList.add(stok);
                return rowList;
            }
            else
            {
                BigDecimal changeStocks = BigDecimal.ZERO;
                List<MaterialPlaceCom> comList = getMaterialApplyStock(productStore.getMaterial().getRecordId(),
                    productStore.getInoutTimeStartQr(),
                    null);
                // 查询每个库位的出入库记录
                for (int i = 0; i < comList.size(); i++)
                {
                    changeStocks = changeStocks.add(comList.get(i).getPeriodStocks());
                }
                // 查询出入库记录，给每个出入库记录赋值
                ProductStore stock = new ProductStore();
                stock.setMaterial(productStore.getMaterial());
                stock.setInoutTimeStartQr(productStore.getInoutTimeStartQr());
                stock.setInoutTimeEndQr(productStore.getInoutTimeEndQr());
                stock.setCompany(company);
                List<ProductStore> rawInOutList = storeHouseDao.getProductStocksPlaceComTwo(stock);
                ProductStore rawStock =
                    setProInoutList(rawInOutList, changeStocks, productStore.getMaterial().getRecordId());
                rowList = rawStock.getProList();
                ProductStore stok = new ProductStore();
                stok.setCurrStocks(changeStocks);
                BigDecimal stocks = BigDecimal.ZERO;
                if (changeStocks != null)
                {
                    stocks = stocks.add(changeStocks);
                }
                if (rawStock.getInStocks() != null)
                {
                    stocks = stocks.add(rawStock.getInStocks());
                }
                if (rawStock.getOutStocks() != null)
                {
                    stocks = stocks.subtract(rawStock.getOutStocks());
                }
                stok.setAllStocks(stocks);
                stok.setInStocks(rawStock.getInStocks());
                stok.setOutStocks(rawStock.getOutStocks());
                
                rowList.add(stok);
                return rowList;
            }
        }
        return null;
    }
    
    // 对出入库记录进行计算并且给每条出入库记录赋值
    public ProductStore setProInoutList(List<ProductStore> rawInOutList, BigDecimal changeStock, String materialId)
    {
        BigDecimal changeStocks = changeStock;
        ProductStore stock = new ProductStore();
        BigDecimal inStocks = BigDecimal.ZERO;
        BigDecimal outStocks = BigDecimal.ZERO;
        for (int j = 0; j < rawInOutList.size(); j++)
        {
            if (rawInOutList.get(j).getInoutType() != null)
            {
                ProductStore productStore = rawInOutList.get(j);
                BigDecimal qty =
                    null == productStore.getQuantity() ? BigDecimal.ZERO : new BigDecimal(productStore.getQuantity());
                rawInOutList.get(j).setCurrStocks(changeStocks);
                
                // 生产入库
                if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_IN)
                {
                    changeStocks = changeStocks.add(qty);
                    inStocks = inStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setInStocks(qty);
                }
                // 送货出库
                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_OUT)
                {
                    changeStocks = changeStocks.subtract(qty);
                    outStocks = outStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setOutStocks(qty);
                }
                // 客诉退货入库
                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_RETURN)
                {
                    changeStocks = changeStocks.add(qty);
                    inStocks = inStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setInStocks(qty);
                }
                // 采购入库
                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_PURCHASING_IN)
                {
                    changeStocks = changeStocks.add(qty);
                    inStocks = inStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setInStocks(qty);
                }
                // 采购补货入库
                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_ENTER)
                {
                    changeStocks = changeStocks.add(qty);
                    inStocks = inStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setInStocks(qty);
                }
                // 采购退货出库
                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_OUT)
                {
                    changeStocks = changeStocks.subtract(qty);
                    outStocks = outStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setOutStocks(qty);
                }
                
                // 订单移库出库
                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_MOVE_OUT)
                {
                    changeStocks = changeStocks.subtract(qty);
                    outStocks = outStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setOutStocks(qty);
                }
                // 订单移库入库
                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_MOVE_IN)
                {
                    changeStocks = changeStocks.add(qty);
                    inStocks = inStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setInStocks(qty);
                }
                // 尾数移库出库
                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_STOCKMOVE_OUT)
                {
                    changeStocks = changeStocks.subtract(qty);
                    outStocks = outStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setOutStocks(qty);
                }
                // 尾数移库入库
                else if (productStore.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_STOCKMOVE_IN)
                {
                    changeStocks = changeStocks.add(qty);
                    inStocks = inStocks.add(qty);
                    rawInOutList.get(j).setCurrStocks(changeStocks);
                    rawInOutList.get(j).setInStocks(qty);
                }
            }
        }
        stock.setChangeStocks(changeStocks);
        stock.setInStocks(inStocks);
        stock.setOutStocks(outStocks);
        stock.setProList(rawInOutList);
        return stock;
    }
    
    /**
     * 用作客诉、正常送货关闭/手工结案关闭
     * 
     * @param detail 合同明细recordId、合同厂编craftNo、移动库存useStocks、集团管控groupCenterId
     * @param typeOne 尾数出入库状态
     * @param typeTwo 订单出入库状态
     */
    public String orderToArrears(ContractDetail detail, Integer typeOne, Integer typeTwo)
    {
        // 获取当前系统时间的后1秒种的时间
        Date date = new Date();
        Calendar c = new GregorianCalendar();
        c.setTime(date);
        c.add(Calendar.SECOND, +1);
        
        // 获取成品库位
        List<MaterialPlace> mpList = detail.getMpList();
        
        // 临时库位
        MaterialPlace temporary = new MaterialPlace();
        
        // 尾数库位
        MaterialPlace mantissa = new MaterialPlace();
        
        if (Collections3.isNotEmpty(mpList))
        {
            for (MaterialPlace mp : mpList)
            {
                if (StringUtils.isNotBlank(mp.getName()))
                {
                    if ("临时库".equals(mp.getName()))
                    {
                        temporary = mp;
                    }
                    else if ("尾数库".equals(mp.getName()))
                    {
                        mantissa = mp;
                    }
                }
            }
        }
        
        if (detail != null && StringUtils.isNotBlank(detail.getRecordId())
            && StringUtils.isNotBlank(detail.getCraftNo()) && detail.getUseStocks() != null
            && detail.getUseStocks() > 0)
        {
            Company company = new Company(CompanyUtil.getInstance().getFactId());
            if (company != null && StringUtils.isNotBlank(company.getRecordId()))
            {
                Material material = materialDao.findMaterialByCraftNo(detail.getCraftNo(), company.getRecordId());
                if (material != null && StringUtils.isNotBlank(material.getRecordId()))
                {
                    material.setCompanyId(company.getRecordId());
                    material.setUseStocks(detail.getUseStocks());
                    material.setStatus(99999901);
                    material.preInsert();
                    
                    List<ProductStore> storeList = new ArrayList<ProductStore>();
                    // 尾数出库/入库
                    ProductStore store = new ProductStore();
                    store.setInoutType(typeOne);
                    store.setGroupCenterId(detail.getGroupCenterId());
                    store.setSaleComId(detail.getSaleId());
                    if (null != mantissa)
                    {
                        store.setStockPlaceId(mantissa.getRecordId());
                        store.setStockPlaceComId(
                            returnStockPlaceComId(store.getStockPlaceId(), material.getRecordId(), detail.getSaleId()));
                    }
                    store.setOperateDate(c.getTime());
                    storeList.add(store);
                    
                    // 订单入库/出库
                    store = new ProductStore();
                    store.setInoutType(typeTwo);
                    store.setContractDeailId(detail.getRecordId());
                    store.setGroupCenterId(detail.getGroupCenterId());
                    store.setSaleComId(detail.getSaleId());
                    if (null != temporary)
                    {
                        store.setStockPlaceId(temporary.getRecordId());
                        store.setStockPlaceComId(
                            returnStockPlaceComId(store.getStockPlaceId(), material.getRecordId(), detail.getSaleId()));
                    }
                    store.setOperateDate(date);
                    storeList.add(store);
                    
                    material.setStoreList(storeList);
                    // 进行数据插入
                    productStoreDao.moveStocks(material);
                    
                    return "success";
                }
            }
        }
        return "fail";
    }
    
    // 获取
    public String returnStockPlaceComId(String stockPlaceId, String materialId, String saleComId)
    {
        if (StringUtils.isBlank(stockPlaceId))
        {
            return null;
        }
        if (StringUtils.isBlank(materialId))
        {
            return null;
        }
        String stockPlaceComId = null;
        if (StringUtils.isNotBlank(saleComId))
        {
            stockPlaceComId = stockPlaceId + materialId + saleComId;
        }
        else
        {
            stockPlaceComId = stockPlaceId + materialId;
        }
        return stockPlaceComId;
    }
    
    public void setProductInoutList(ContractDetail contractDetail, Company company)
    {
        List<String> idList = Lists.newArrayList();
        idList.add(contractDetail.getRecordId());
        List<ProductStore> productInOutList =
            productStoreDao.getProListByIds(idList, company.getRecordId());
        if (contractDetail != null && StringUtils.isNotBlank(contractDetail.getRecordId()))
        {
            BigDecimal stocks = BigDecimal.ZERO;
            BigDecimal inStocks = BigDecimal.ZERO;
            BigDecimal outStocks = BigDecimal.ZERO;
            BigDecimal deliverStocks = BigDecimal.ZERO;
            BigDecimal donateStocks = BigDecimal.ZERO;
            List<ProductStore> proList = new ArrayList<ProductStore>();
            if (Collections3.isNotEmpty(productInOutList))
            {
                for (ProductStore ps : productInOutList)
                {
                    if (ps != null && null != ps.getInoutType() && StringUtils.isNotBlank(ps.getContractDetailId())
                        && ps.getContractDetailId().equals(contractDetail.getRecordId()))
                    {
                        contractDetail.setMaterialId(ps.getMaterialId());
                        BigDecimal qty = ps.getQuantity() == null ? BigDecimal.ZERO : new BigDecimal(ps.getQuantity());
                        
                        // 生产入库
                        if (ps.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_IN)
                        {
                            stocks = stocks.add(qty);
                            inStocks = inStocks.add(qty);
                            ps.setCurrStocks(stocks);
                            ps.setInStocks(qty);
                        }
                        // 送货出库
                        else if (ps.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_OUT)
                        {
                            stocks = stocks.subtract(qty);
                            deliverStocks = deliverStocks.add(qty);
                            outStocks = outStocks.add(qty);
                            ps.setCurrStocks(stocks);
                            ps.setOutStocks(qty);
                            donateStocks =
                                donateStocks.add(null == ps.getDonateStocks() ? BigDecimal.ZERO : ps.getDonateStocks());
                        }
                        // 客诉退货入库
                        else if (ps.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_RETURN)
                        {
                            stocks = stocks.add(qty);
                            inStocks = inStocks.add(qty);
                            deliverStocks = deliverStocks.subtract(qty);
                            ps.setCurrStocks(stocks);
                            ps.setInStocks(qty);
                        }
                        // 采购入库
                        else if (ps.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_PURCHASING_IN)
                        {
                            stocks = stocks.add(qty);
                            inStocks = inStocks.add(qty);
                            ps.setCurrStocks(stocks);
                            ps.setInStocks(qty);
                        }
                        // 采购补货入库
                        else if (ps.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_ENTER)
                        {
                            stocks = stocks.add(qty);
                            inStocks = inStocks.add(qty);
                            ps.setCurrStocks(stocks);
                            ps.setInStocks(qty);
                        }
                        // 采购退货出库
                        else if (ps.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_PRDORDER_RETURN_OUT)
                        {
                            stocks = stocks.subtract(qty);
                            outStocks = outStocks.add(qty);
                            ps.setCurrStocks(stocks);
                            ps.setOutStocks(qty);
                        }
                        
                        // 订单移库出库
                        else if (ps.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_MOVE_OUT)
                        {
                            stocks = stocks.subtract(qty);
                            outStocks = outStocks.add(qty);
                            ps.setCurrStocks(stocks);
                            ps.setOutStocks(qty);
                        }
                        // 订单移库入库
                        else if (ps.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_MOVE_IN)
                        {
                            stocks = stocks.add(qty);
                            inStocks = inStocks.add(qty);
                            ps.setCurrStocks(stocks);
                            ps.setInStocks(qty);
                        }
                        proList.add(ps);
                    }
                }
            }
            // 设置出入库记录
            contractDetail.setProList(proList);
            // 结余
            contractDetail.setStocks(stocks);
            // 入
            contractDetail.setInStocks(inStocks);
            // 出
            contractDetail.setOutStocks(outStocks);
            // 送货出库
            if (null != deliverStocks)
            {
                contractDetail.setDeliceryStocks(deliverStocks.intValue());
            }
            // 累计备品，订单备品大于已送备品结案时订单数量要减去备品
            if (null != contractDetail.getSpareQuantity())
            {
                BigDecimal spareQuantity = new BigDecimal(contractDetail.getSpareQuantity());
                if (spareQuantity.compareTo(donateStocks) >= 0)
                {
                    contractDetail.setSpareQuantity(spareQuantity.subtract(donateStocks).intValue());
                }
                else
                {
                    contractDetail.setSpareQuantity(null);
                }
            }
        }
    }
    
    public ProductStore getSingleProductStoreStocks(String materialId, Date startDate, Date endDate)
    {
        ProductStore productStore = new ProductStore();
        productStore.setRecordId(materialId);
        
        // 获取查询时间内所有出入库记录
        Material materials = new Material();
        materials.setMaterialIds(materialId);
        materials.setStartDate(startDate);
        materials.setEndDate(endDate);
        List<ProductStore> rsList = materialDao.getProductStockApplyList(materials);
        for (int i = 0; i < rsList.size(); i++)
        {
            if (StringUtils.isBlank(rsList.get(i).getStockPlaceComId()))
            {
                rsList.get(i).setStockPlaceComId("sky" + rsList.get(i).getMaterialId());
            }
        }
        // 获取期初数据
        List<MaterialPlaceCom> comList = getMaterialApplyStock(materialId, startDate, null);
        
        // 处理数据
        List<ProductStore> list = Lists.newArrayList();
        list.add(productStore);
        getMaterialApplyList(list, comList, rsList);
        
        return list.get(0);
    }
    
    public void resetProductStocks(Company company, String operDate)
    {
        try
        {
            company.setInitDate(operDate);
            // 需要转存库存的库位
            List<MaterialPlaceCom> list = materialDao.getProductPlaceInfoList(company);
            // 出入库记录
            List<ProductStore> inoutList = materialDao.getProductInOutList(company);
            // 大于期初的所有出入库记录"sky"+materialId
            setNullComIdSkyDes(inoutList);
            resetMaterialHandleStocks(list, inoutList, company, operDate);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
    }
    
    private void resetMaterialHandleStocks(List<MaterialPlaceCom> materialPlaceList, List<ProductStore> inoutList,
        Company company, String operDate)
    {
        Date currTime = null;
        if (inoutList != null && inoutList.size() > 0)
        {
            currTime = inoutList.get(0).getCurrTime();
        }
        // 要进行插入的数据
        List<MaterialStockInit> list = new ArrayList<MaterialStockInit>();
        
        List<MaterialPlaceCom> comlist = cumulativeStock(materialPlaceList, inoutList);
        // 按照库位进行初始化
        Iterator<MaterialPlaceCom> it = comlist.iterator();
        while (it.hasNext())
        {
            MaterialPlaceCom materialPlaceCom = it.next();
            // 获取库存进行更新
            if (StringUtils.isNotBlank(materialPlaceCom.getRecordId()))
            {
                // 验证是否存在该时间的期初
                if (currTime != null
                    && !(materialPlaceCom.getInitDate() != null && materialPlaceCom.getInitDate() == currTime))
                {
                    // 进行数据插入
                    MaterialStockInit materialStockInit = new MaterialStockInit();
                    materialStockInit.setCompanyId(company.getRecordId());
                    materialStockInit.setMaterialId(materialPlaceCom.getMaterialId());
                    materialStockInit.setInitDate(currTime);
                    materialStockInit.setPeriodStocks(materialPlaceCom.getChangeStocks());
                    materialStockInit.setStockPlaceComId(
                        materialPlaceCom.getRecordId().startsWith("sky") ? null : materialPlaceCom.getRecordId());
                    materialStockInit.setStockPlaceId(materialPlaceCom.getMaterPlaceId());
                    list.add(materialStockInit);
                }
            }
        }
        // 更新物料数据
        if (list.size() > 0)
        {
            initMaterialDao.batchResetMaterial(list);
        }
        
    }
    
    private void setNullComIdSkyDes(List<ProductStore> inoutList)
    {
        if (!CollectionUtils.isEmpty(inoutList))
        {
            for (ProductStore rs : inoutList)
            {
                if (StringUtils.isBlank(rs.getStockPlaceComId()))
                {
                    rs.setStockPlaceComId("sky" + rs.getMaterialId());
                }
            }
        }
    }
    
    public void setProductInoutListTwo(List<ProductStore> productInOutList, ContractDetail deail)
    {
        if (productInOutList != null && productInOutList.size() > 0 && deail != null
            && StringUtils.isNotBlank(deail.getRecordId()))
        {
            BigDecimal stocks = BigDecimal.ZERO;
            List<ProductStore> proList = new ArrayList<ProductStore>();
            for (ProductStore ps : productInOutList)
            {
                if (ps != null && null != ps.getInoutType() && StringUtils.isNotBlank(ps.getContractDetailId())
                    && ps.getContractDetailId().equals(deail.getRecordId()))
                {
                    deail.setMaterialId(ps.getMaterialId());
                    BigDecimal qty = ps.getQuantity() == null ? BigDecimal.ZERO : new BigDecimal(ps.getQuantity());
                    
                    // 生产入库
                    if (ps.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_IN)
                    {
                        stocks = stocks.add(qty);
                    }
                    // 订单移库入库
                    else if (ps.getInoutType() == TypeKey.ST_PRODUCT_INOUTTYPE_MOVE_IN)
                    {
                        stocks = stocks.add(qty);
                    }
                    proList.add(ps);
                }
            }
            // 结余
            deail.setStocks(stocks);
        }
    }
    
}
