<%@ taglib prefix="v-on" uri="http://www.springframework.org/tags/form" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isErrorPage="true"%>
<div class="p-2">
    <div class="row pt-2 pb-2 alert-light border-bottom border-bottom-secondary">
        <div class="col text-center text-primary">
            <h5>耗材领用申请</h5>
        </div>
    </div>
    <div class="row alert-light pt-2 pb-2 align-items-center">
        <div class="col">
            公&emsp;&emsp;司
            <select class="custom-select form-control h-32px" v-model="company" v-on:change="getMaterialList" id="companySelect">
                <option v-for="item in companyLists" :key="item.recordId" :value="item.recordId">
                    {{item.name}}
                </option>
            </select>
        </div>
    </div>
    <div class="row alert-light pt-2 pb-2 align-items-center">
        <div class="col">
            类型
            <select class="custom-select form-control h-32px" v-model="product" v-on:change="getMaterialList">
                <option value="1">ERP产品</option>
                <option value="2">大生态圈产品</option>
            </select>
        </div>
    </div>
    <div class="row alert-light pt-2 pb-2 align-items-center">
        <div class="col">
            <div class="row">
                <div class="col text-right">
                    <span class="text-primary" v-on:click="add">增加+</span>
                </div>
            </div>
            <div v-for="(item,index) in materialUseList">
                <div class="row border-bottom pb-1 pt-1">
                    <div class="col">
                        <div class="row alert-light pt-2 pb-2 align-items-center">
                            <div class="col">
                                <select class="custom-select form-control selectpicker" data-live-search="true" v-model="item.material.recordId" v-on:change="getStockPlaceId(item)" :id="item.itemId"></select>
                            </div>
                        </div>
                        <div class="row alert-light pt-2 pb-2 align-items-center">
                            <div class="col">
                                物料规格
                                <input class="custom-select form-control h-32px" v-model="item.material.specification" :disabled="true">
                            </div>
                        </div>
                        <div class="row alert-light pt-2 pb-2 align-items-center">
                            <div class="col">
                                物料类型
                                <input class="custom-select form-control h-32px" v-model="item.material.materialType.value" :disabled="true">
                            </div>
                            <div class="col">
                                单&emsp;&emsp;位
                                <input class="custom-select form-control h-32px" v-model="item.material.unit.value" :disabled="true">
                            </div>
                        </div>
                        <div class="row alert-light pt-2 pb-2 align-items-center">
                            <div class="col">
                                库存数量
                                <input class="custom-select form-control h-32px" v-model="item.material.stocks" :disabled="true">
                            </div>
                            <div class="col">
                                占用数量
                                <span class="custom-select form-control h-32px" v-on:click="getMaterialUseList(item)">{{item.material.occupationQuantity}}</span>
                            </div>
                        </div>
                        <div class="row alert-light pt-2 pb-2 align-items-center">
                            <div class="col">
                                领用数量
                                <input class="custom-select form-control h-32px" v-model="item.quantity" onkeyup="this.value=this.value.replace(/[^0-9.]/g,'')"
                                       onafterpaste="this.value=this.value.replace(/\D/g,'')">
                            </div>
                        </div>
                        <div class="row pt-2">
                            <div class="col-12 text-right">
                                <button class="btn btn-sm btn-danger" v-on:click="reduce(index)">&nbsp;-&nbsp;</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row alert-light pt-2 pb-2 align-items-center">
        <div class="col">
            领用部门
            <select class="custom-select form-control h-32px" v-model="depart">
                <option v-for="item in departList" :key="item.recordId" :value="item">
                    {{item.name}}
                </option>
            </select>
        </div>
        <div class="col">
            领用人
            <select class="custom-select form-control h-32px" v-model="user">
                <option v-for="item in userList" :key="item.recordId" :value="item">
                    {{item.userName}}
                </option>
            </select>
        </div>
    </div>
    <!-- audit.askDate -->
    <div class="row alert-light pt-2 pb-2 align-items-center">
        <div class="col">
            仓库
            <select class="custom-select form-control h-32px" v-model="defaultStoreHouse" :disabled="company == 17">
                <option v-for="item in storeHouseList" :key="item.recordId" :value="item">
                    {{item.name}}
                </option>
            </select>
        </div>
        <div class="col">
            领用时间
            <startDate></startDate>
        </div>
    </div>
    <div class="row fixed-bottom bg-light pt-2 pb-2">
        <div class="col">
            <button class="btn btn-secondary w-100" v-on:click="cancle">取消</button>
        </div>
        <div class="col">
            <button class="btn btn-primary w-100" v-on:click="submit" :disabled="isDisable">提交</button>
        </div>
    </div>
    <div class="modal fade" id="occupation" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">占用列表</h5>
                </div>
                <div class="modal-body">
                    <div v-for="occupation in occupationList" class="occupation-row align-items-center border-bottom border-dark">
                        <div class="row align-items-center">
                            <span class="col">领用单号:{{occupation.no ? occupation.no : '无'}}</span>
                            <span class="col">领用数量:{{occupation.quantity ? occupation.quantity : 0}}</span>
                        </div>
                        <div class="row align-items-center">
                            <span class="col-6">已领用数量:{{occupation.actualQuantity ? occupation.actualQuantity : 0}}</span>
                            <span class="col">领用时间:{{occupation.createdDate ? occupation.createdDate : '无'}}</span>
                        </div>
                        <div class="row align-items-center">
                            <span class="col">申请人:{{occupation.userName ? occupation.userName : '无'}}</span>
                        </div>
                        <div class="row pt-2 align-items-center"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
</div>