router.beforeEach((to, from, next) => {
	if (to.name){
		$.ajax({
			type: "post",
			url:ctx + "/f/wechat/kybsoft/getUser",
			contentType: "application/json",
			success: function(data)
			{
				if (to.name == 'login' || to.name == 'register' || to.name == 'editPwd'){
					if(data && data.recordId){
						next({path:'/'});
					}else {
						next();
					}
				}else {
					if(data && data.recordId){
						next();
					}else {
						next({path:'/login'});
					}
				}
			}
		});
	}
	setTimeout(() => {
		window.scrollTo(0, 0);
	}, 100);
});
const app = new Vue({
	router,
	store
}).$mount('#main');